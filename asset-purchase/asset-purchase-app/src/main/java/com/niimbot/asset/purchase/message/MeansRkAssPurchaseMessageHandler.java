package com.niimbot.asset.purchase.message;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.material.model.AsMaterialRkOrder;
import com.niimbot.asset.material.service.AsMaterialRkOrderService;
import com.niimbot.asset.means.model.AsStoreOrder;
import com.niimbot.asset.means.service.AsStoreOrderService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.InstantCompanyMessageHandler;
import com.niimbot.asset.purchase.model.AsPurchaseApply;
import com.niimbot.asset.purchase.model.AsPurchaseOrder;
import com.niimbot.asset.purchase.model.AsPurchaseOrderDetail;
import com.niimbot.asset.purchase.service.AsPurchaseApplyService;
import com.niimbot.asset.purchase.service.AsPurchaseOrderDetailService;
import com.niimbot.asset.purchase.service.AsPurchaseOrderService;
import com.niimbot.purchase.PurchaseOrderQueryDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产入库采购完成提醒
 * <p>
 * 资产关联采购入库-采购订单-采购申请-采购申请人
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MeansRkAssPurchaseMessageHandler extends InstantCompanyMessageHandler {

    private final AsStoreOrderService meansStoreOrderService;

    private final AsPurchaseOrderService purchaseOrderService;

    private final AsPurchaseOrderDetailService purchaseOrderDetailService;

    private final AsPurchaseApplyService purchaseApplyService;

    private final AsMaterialRkOrderService materialRkOrderService;

    @Override
    public String code() {
        return MessageConstant.Code.CGWCTX.getCode();
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        StringBuilder nameBuilder = new StringBuilder();
        String orderType = params.getType();
        Long rkOrderId = params.getId();
        JSONObject orderData = null;
        // 资产入库单
        if (String.valueOf(OrderFormTypeEnum.STORE.getCode()).equals(orderType)) {
            AsStoreOrder storeOrder = meansStoreOrderService.getById(rkOrderId);
            if (Objects.isNull(storeOrder) || Objects.isNull(storeOrder.getOrderData())) {
                return;
            }
            orderData = storeOrder.getOrderData();
            nameBuilder.append("物品");
        }
        // 耗材入库单
        if (String.valueOf(OrderFormTypeEnum.RK.getCode()).equals(orderType)) {
            AsMaterialRkOrder materialRkOrder = materialRkOrderService.getById(rkOrderId);
            if (Objects.isNull(materialRkOrder) || Objects.isNull(materialRkOrder.getOrderData())) {
                return;
            }
            orderData = materialRkOrder.getOrderData();
            nameBuilder.append("耗材");
        }
        if (Objects.isNull(orderData) || orderData.isEmpty()) {
            return;
        }
        // 是否关联了采购入库
        String rkType = "rkType";
        String isPurchase = "采购入库";
        if (!orderData.containsKey(rkType)) {
            return;
        }
        String type = orderData.getString(rkType);
        if (!isPurchase.equals(type)) {
            return;
        }
        // 采购订单ID
        String purchaseOrderNoKey = "purchaseOrderNo";
        if (!orderData.containsKey(purchaseOrderNoKey)) {
            return;
        }
        String purchaseOrderNo = orderData.getString(purchaseOrderNoKey);
        if (StrUtil.isBlank(purchaseOrderNo)) {
            return;
        }
        AsPurchaseOrder purchaseOrder = null;
        List<AsPurchaseOrder> records = purchaseOrderService.page(new PurchaseOrderQueryDto().setOrderType(13).setKw(purchaseOrderNo)).getRecords();
        if (CollUtil.isNotEmpty(records)) {
            purchaseOrder = records.get(0);
        }
        // AsPurchaseOrder purchaseOrder = purchaseOrderService.getOne(
        //         Wrappers.lambdaQuery(AsPurchaseOrder.class)
        //                 .eq(AsPurchaseOrder::getOrderNo, purchaseOrderNo)
        // );
        // 校验入库状态
        if (Objects.isNull(purchaseOrder) || Objects.isNull(purchaseOrder.getOrderData()) || purchaseOrder.getStoreStatus() != 4) {
            return;
        }
        String purchaseOrderModeKey = "mode";
        String purchaseOrderModeVal = "关联采购申请";
        JSONObject purchaseOrderData = purchaseOrder.getOrderData();
        if (!purchaseOrderData.containsKey(purchaseOrderModeKey) || !purchaseOrderData.getString(purchaseOrderModeKey).equals(purchaseOrderModeVal)) {
            return;
        }
        // 采购单详情
        List<AsPurchaseOrderDetail> details = purchaseOrderDetailService.list(
                Wrappers.lambdaQuery(AsPurchaseOrderDetail.class)
                        .eq(AsPurchaseOrderDetail::getPurchaseOrderId, purchaseOrder.getId())
        );
        if (CollUtil.isEmpty(details)) {
            return;
        }
        List<Long> applyIds = details.stream().map(AsPurchaseOrderDetail::getPurchaseApplyId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(applyIds)) {
            return;
        }
        // 采购申请单-申请人列表
        Map<Long, Long> group = purchaseApplyService.listByIds(applyIds).stream()
                .filter(v -> Objects.nonNull(v.getId()) && Objects.nonNull(v.getOrderData()) && v.getOrderData().containsKey("applyUser"))
                .collect(Collectors.toMap(AsPurchaseApply::getId, v -> Convert.toLong(v.getOrderData().getString("applyUser"))));
        group.forEach((k, v) -> {
            Map<String, Object> extMap = new HashMap<>(2);
            extMap.put("docId", k);
            extMap.put("orderType", 10);
            Map<String, String> contentMap = new HashMap<>(3);
            contentMap.put("name", nameBuilder.toString());
            contentMap.put("url", pcDomain + "/#/purchase-apply/detail?orderType=10&docId=" + k);
            bodies.add(
                    Body.builder()
                            .userIds(Collections.singleton(v))
                            .mapParam(contentMap)
                            .appExtMapParam(Convert.toMap(String.class, String.class, extMap))
                            .commonExtMap(extMap)
                            .build()
            );
        });
    }

}
