package com.niimbot.asset.purchase.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.activiti.WorkflowExecuteStepDto;
import com.niimbot.activiti.WorkflowStartDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.model.ActWorkflowBusiness;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActWorkflowBusinessService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.PurchaseConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.core.enums.PurchaseResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.WorkflowUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.material.model.AsMaterial;
import com.niimbot.asset.material.service.AsMaterialService;
import com.niimbot.asset.means.model.AsProduct;
import com.niimbot.asset.means.service.AsProductService;
import com.niimbot.asset.purchase.model.AsPurchaseApply;
import com.niimbot.asset.purchase.model.AsPurchaseApplyDetail;
import com.niimbot.asset.purchase.model.AsPurchaseOrderType;
import com.niimbot.asset.purchase.service.AsPurchaseApplyService;
import com.niimbot.asset.purchase.service.AsPurchaseOrderTypeService;
import com.niimbot.asset.purchase.service.PurchaseCommonService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.purchase.PurchaseApplyDetailDto;
import com.niimbot.purchase.PurchaseApplyDetailSelectDto;
import com.niimbot.purchase.PurchaseApplyDto;
import com.niimbot.purchase.PurchaseApplySubmitDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountQueryDto;
import com.niimbot.purchase.PurchaseOrderDetailQueryDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 采购申请 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Slf4j
@RestController
@RequestMapping("server/purchase/apply")
@RequiredArgsConstructor
public class AsPurchaseApplyServiceController {
    private final AsPurchaseApplyService purchaseApplyService;
    private final ActWorkflowService workflowService;
    private final AsCusEmployeeService cusEmployeeService;
    private final ActWorkflowBusinessService workflowBusinessService;
    private final AsProductService productService;
    private final AsPurchaseOrderTypeService purchaseOrderTypeService;
    private final PurchaseCommonService purchaseCommonService;
    private final AsMaterialService materialService;
    @Resource
    private RedisService redisService;
    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto,
                                                  @RequestBody PurchaseApplyDto dto) {
        loginUserDto.getCusUser().setOrgId(dto.getOrgId());
        WorkflowExecuteDto result = new WorkflowExecuteDto();
        String uuid = UUID.fastUUID().toString();
        result.setConditionId(uuid);
        this.check(dto);
        // 查询单据类型
        AsPurchaseOrderType orderType = purchaseOrderTypeService.getOne(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsPurchaseOrderType::getType, dto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), dto.getOrgId(), false);
        if (workflow != null) {
            JSONObject orderData = dto.getOrderData();
            BigDecimal totalMoney = BigDecimal.ZERO;

            for (PurchaseApplyDetailDto detailDto : dto.getProductDetail()) {
                BigDecimal price = detailDto.getPrice() == null ? BigDecimal.ZERO : detailDto.getPrice();
                totalMoney = totalMoney.add(price.multiply(detailDto.getQuantity()));
            }
            JSONObject condition = new JSONObject();
            condition.put(QueryFieldConstant.PURCHASE_TOTAL_MONEY, totalMoney);
            condition.put(QueryFieldConstant.SUBMITTER_ORG, CollUtil.union(ListUtil.of(LoginUserThreadLocal.getCurrentUserId(), dto.getOrgId()), approveRoleMemberService.getEmpRoleIds(LoginUserThreadLocal.getCurrentUserId())));
            orderData.putAll(condition);

            String redisKey = RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.hSetAll(redisKey, condition);
            redisService.expire(redisKey, 2, TimeUnit.HOURS);
            result.setWorkflowExecuteStepDto(workflowService.preStartWorkflow(loginUserDto, workflow, orderData, null));
            String stepKey = RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.set(stepKey, result.getWorkflowExecuteStepDto());
            redisService.expire(stepKey, 2, TimeUnit.HOURS);
        }
        return result;
    }

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(LoginUserDto loginUserDto, @RequestBody PurchaseApplySubmitDto dto) {
        loginUserDto.getCusUser().setOrgId(dto.getOrderDto().getOrgId());
        PurchaseApplyDto applyDto = dto.getOrderDto();
        // 校验单据
        this.check(applyDto);
        purchaseCommonService.verify(applyDto);
        purchaseCommonService.translation(applyDto);
        // 初始化物品信息
        this.initProductInfo(loginUserDto.getCusUser().getCompanyId(), applyDto);
        // 查询单据类型
        AsPurchaseOrderType orderType = purchaseOrderTypeService.getOne(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsPurchaseOrderType::getType, AssetConstant.ORDER_TYPE_PURCHASE));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), loginUserDto.getCusUser().getOrgId(), false);

        // 创建采购申请单
        applyDto.setId(IdUtils.getId());
        purchaseApplyService.create(applyDto, workflow != null);

        // 流程开启, 走流程审批
        if (workflow != null) {
            if (dto.getConditionId() == null) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_COMMON_ERROR, "流程执行条件Id不能为空");
            }
            if (dto.getExecuteStepDtoList().isEmpty()) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_EXECUTE_STEP_IS_EMPTY);
            }
            // 写入审批人
            List<WorkflowExecuteStepDto> executeStepList = Convert.toList(WorkflowExecuteStepDto.class, redisService.get(
                    RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId())));
            Map<Long, List<WorkflowExecuteStepDto.WorkflowApprover>> submitExecuteStepList = dto.getExecuteStepDtoList().stream()
                    .collect(Collectors.toMap(WorkflowExecuteStepDto::getStepId, WorkflowExecuteStepDto::getApproverList, (k1, k2) -> k1));
            for (WorkflowExecuteStepDto stepDto : executeStepList) {
                if (submitExecuteStepList.containsKey(stepDto.getStepId())) {
                    stepDto.setApproverList(submitExecuteStepList.get(stepDto.getStepId()));
                }
            }
            AsCusEmployee employee = cusEmployeeService.getById(loginUserDto.getCusUser().getId());
            String userName = employee == null ? loginUserDto.getCusUser().getAccount() : employee.getEmpName();

            JSONObject orderData = applyDto.getOrderData();
            Map<Object, Object> map = redisService.hGetAll(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            if (map != null) {
                Map<String, Object> conditionMap = Convert.toMap(String.class, Object.class, map);
                orderData.putAll(conditionMap);
            }
            workflowService.startWorkflow(
                    new WorkflowStartDto()
                            .setUserDto(loginUserDto.getCusUser())
                            .setTitle(getTitle(userName))
                            .setActivitiKey(orderType.getActivitiKey())
                            .setBusinessType(orderType.getType().shortValue())
                            .setWorkflowId(workflow.getId())
                            .setVersion(workflow.getVersion())
                            .setCallbackUrl(workflow.getCallbackUrl())
                            .setBusinessId(applyDto.getId())
                            .setFormData(orderData)
                            .setExecuteStepDtoList(executeStepList));
            redisService.del(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            redisService.del(RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
        }
        return true;
    }

    @GetMapping("/{id}")
    public PurchaseApplyDto getById(@PathVariable("id") Long id) {
        AsPurchaseApply purchaseApply = purchaseApplyService.getWithStatusById(id);
        if (purchaseApply == null) {
            return null;
        }
        PurchaseApplyDto purchaseApplyDto = BeanUtil.copyProperties(purchaseApply, PurchaseApplyDto.class);
        purchaseApplyDto.setProductDetail(
                purchaseApplyService.getApplyDetailById(purchaseApply.getId())
                        .stream().map(detail -> BeanUtil.copyProperties(detail, PurchaseApplyDetailDto.class))
                        .collect(Collectors.toList()));
        return purchaseApplyDto;
    }

    @PostMapping("/page")
    public IPage<AsPurchaseApply> page(@RequestBody PurchaseOrderQueryDto query) {
        return purchaseApplyService.page(query);
    }

    @PostMapping("/list")
    public List<AsPurchaseApply> list(@RequestBody PurchaseOrderQueryDto query) {
        return purchaseApplyService.list(query);
    }

    @PostMapping("/page/select")
    public IPage<PurchaseApplyDetailSelectDto> pageSelect(@RequestBody PurchaseOrderDetailQueryDto query) {
        return purchaseApplyService.pageSelect(query);
    }

    @PostMapping(value = "/listDetails")
    public List<AsPurchaseApplyDetail> listDetailsByOrderId(@RequestBody Collection<Long> ids) {
        return purchaseApplyService.getApplyDetailById(ids);
    }

    /**
     * 采购申请明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "/pageDetail")
    public IPage<AsPurchaseApplyDetail> pageDetail(PurchaseDetailPageQueryDto dto) {
        return purchaseApplyService.pageDetail(dto);
    }

    @PostMapping(value = "/link/order")
    public List<PurchaseLinkOrderAmountDto> listOrderAmount(@RequestBody PurchaseLinkOrderAmountQueryDto dto) {
        return purchaseApplyService.listOrderAmount(dto);
    }

    /**
     * 采购流程回调接口
     *
     * @param callbackDto
     * @return
     */
    @PostMapping("/processApplyCallback")
    @Transactional(rollbackFor = Exception.class)
    public Boolean processApplyCallback(@RequestBody WorkflowCallbackDto callbackDto) {
        AsPurchaseApply apply = purchaseApplyService.getById(Long.parseLong(callbackDto.getBusinessId()));
        if (apply == null) {
            return false;
        }
        if (WorkflowUtil.finishWorkflowCallbackAllowed().contains(apply.getApproveStatus())) {
            return true;
        }
        short status = Short.parseShort(callbackDto.getApproveStatus());
        if (!WorkflowUtil.finishWorkflowCallbackAllowed().contains(status)) {
            return false;
        }
        // threadLocal写入公司Id
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(new CusUserDto()
                .setId(apply.getCreateBy())
                .setIsAdmin(true)
                .setCompanyId(apply.getCompanyId()));
        LoginUserThreadLocal.set(userDto);
        Optional<ActWorkflowBusiness> workflowBusiness = Optional.ofNullable(workflowBusinessService.getById(callbackDto.getProcessInstanceId()));
        workflowBusiness.ifPresent(v -> v.setApproveStatus(status));
        apply.setApproveStatus(status);
        try {
            workflowBusiness.ifPresent(workflowBusinessService::updateById);
            purchaseApplyService.updateById(apply);
        } finally {
            LoginUserThreadLocal.remove();
        }
        return true;
    }

    private String getTitle(String userName) {
        return String.format("%s提交的采购申请", userName);
    }

    private void initProductInfo(Long companyId, PurchaseApplyDto applyDto) {
        // 1.处理产品
        List<Long> productIds = applyDto.getProductDetail().stream()
                .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_ASSET)
                .map(PurchaseApplyDetailDto::getProductId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(productIds)) {
            List<AsProduct> products = productService.listByProductIds(companyId, productIds);
            if (products.size() == 0) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_APPLY_PRODUCT_NO_EXISTS);
            }
            Map<Long, AsProduct> productMap = products.stream().collect(Collectors.toMap(AsProduct::getId, k -> k));
            applyDto.getProductDetail().stream()
                    .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_ASSET)
                    .forEach(detailDto -> {
                        AsProduct product = productMap.get(detailDto.getProductId());
                        detailDto.setName(product.getName());
                        detailDto.setCode(product.getCode());
                        detailDto.setBrand(product.getBrand());
                        detailDto.setModel(product.getModel());
                        detailDto.setImages(product.getImages());
                    });
        }
        // 2.处理耗材
        List<Long> materialIds = applyDto.getProductDetail().stream()
                .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_MATERIAL)
                .map(PurchaseApplyDetailDto::getProductId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(materialIds)) {
            List<AsMaterial> materials = materialService.listByIds(materialIds);
            if (materials.size() == 0) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_APPLY_MATERIAL_NO_EXISTS);
            }
            Map<Long, AsMaterial> materialMap = materials.stream().collect(Collectors.toMap(AsMaterial::getId, k -> k));
            applyDto.getProductDetail().stream()
                    .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_MATERIAL)
                    .forEach(detailDto -> {
                        AsMaterial material = materialMap.get(detailDto.getProductId());
                        detailDto.setName(material.getMaterialData().getString("materialName"));
                        detailDto.setCode(material.getMaterialData().getString("materialCode"));
                        detailDto.setBrand(material.getMaterialData().getString("brand"));
                        detailDto.setModel(material.getMaterialData().getString("model"));
                        JSONArray picture = material.getMaterialData().getJSONArray("picture");
                        if (picture != null) {
                            detailDto.setImages(picture.toJavaList(String.class));
                        }
                    });
        }
    }

    private void check(PurchaseApplyDto applyDto) {
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal totalQuantity = BigDecimal.ZERO;
        for (PurchaseApplyDetailDto detailDto : applyDto.getProductDetail()) {
            // 校验
            detailDto.checkQuantity();
            // 总数量
            totalQuantity = totalQuantity.add(detailDto.getQuantity());
            // 总价格
            if (detailDto.getPrice() != null) {
                totalMoney = totalMoney.add(detailDto.getPrice().multiply(detailDto.getQuantity()));
            }
        }
        applyDto.setTotalMoney(totalMoney).setTotalQuantity(totalQuantity);
    }
}
