package com.niimbot.asset.purchase.controller;

import com.niimbot.asset.purchase.service.PurchaseCommonService;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/10/18 16:42
 */
@Slf4j
@RestController
@RequestMapping("server/purchase/common")
@RequiredArgsConstructor
public class PurchaseCommonServiceController {
    private final PurchaseCommonService purchaseCommonService;
    /**
     * 采购单据表单查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "/form/{orderType}")
    public FormVO getForm(@PathVariable("orderType") Integer orderType) {
        return purchaseCommonService.getForm(orderType);
    }

    /**
     * 单据排序字段查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "/sortField/{orderType}")
    public QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType) {
        return purchaseCommonService.sortField(orderType);
    }
}
