package com.niimbot.asset.purchase.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.purchase.mapper.AsSupplierContactMapper;
import com.niimbot.asset.purchase.model.AsSupplierContact;
import com.niimbot.asset.purchase.service.AsSupplierContactService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 供应商联系人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
public class AsSupplierContactServiceImpl extends ServiceImpl<AsSupplierContactMapper, AsSupplierContact> implements AsSupplierContactService {

    @Override
    public boolean removeBySupplierId(Long supplierId) {
        return this.remove(Wrappers.<AsSupplierContact>lambdaQuery()
                .eq(AsSupplierContact::getSupplierId, supplierId));
    }

    @Override
    public List<AsSupplierContact> getListBySupplierId(Long supplierId) {
        return this.list(Wrappers.<AsSupplierContact>lambdaQuery()
                .eq(AsSupplierContact::getSupplierId, supplierId)
                .orderByAsc(AsSupplierContact::getId));
    }
}
