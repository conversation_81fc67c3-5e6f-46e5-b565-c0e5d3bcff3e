package com.niimbot.asset.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.PurchaseConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.PurchaseResultCode;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.OrderToStringConverter;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.material.model.AsMaterial;
import com.niimbot.asset.material.service.AsMaterialService;
import com.niimbot.asset.means.model.AsProduct;
import com.niimbot.asset.means.service.AsProductService;
import com.niimbot.asset.purchase.mapper.AsPurchaseOrderMapper;
import com.niimbot.asset.purchase.model.*;
import com.niimbot.asset.purchase.service.*;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.page.SortQuery;
import com.niimbot.purchase.*;
import com.niimbot.system.QueryConditionSortDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
@RequiredArgsConstructor
public class AsPurchaseOrderServiceImpl extends ServiceImpl<AsPurchaseOrderMapper, AsPurchaseOrder> implements AsPurchaseOrderService {
    private final AsPurchaseOrderDetailService purchaseOrderDetailService;
    private final AsPurchaseApplyService purchaseApplyService;
    private final AsPurchaseApplyDetailService purchaseApplyDetailService;
    private final AsPurchaseOrderLinkApplyService purchaseOrderLinkApplyService;
    private final AsProductService productService;
    private final AsMaterialService materialService;
    private final MySqlPurchaseOrderQueryConditionResolver conditionResolver;
    private final PurchaseCommonService purchaseCommonService;
    private final AsPurchaseOrderTypeService purchaseOrderTypeService;
    private final ActWorkflowService workflowService;
    private final DataScopeStrategyManager dataScopeManager;
    @Resource
    private RedisService redisService;
    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Override
    public WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, PurchaseOrderDto orderDto) {
        WorkflowExecuteDto result = new WorkflowExecuteDto();
        String uuid = UUID.fastUUID().toString();
        result.setConditionId(uuid);
        // 查询单据类型
        AsPurchaseOrderType orderType = purchaseOrderTypeService.getOne(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsPurchaseOrderType::getType, orderDto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), orderDto.getOrgId(), false);
        if (workflow != null) {
            JSONObject orderData = orderDto.getOrderData();
            BigDecimal totalMoney = BigDecimal.ZERO;

            for (PurchaseOrderDetailDto detailDto : orderDto.getProductDetail()) {
                BigDecimal price = detailDto.getPrice() == null ? BigDecimal.ZERO : detailDto.getPrice();
                totalMoney = totalMoney.add(price.multiply(detailDto.getQuantity()));
            }
            JSONObject condition = new JSONObject();
            condition.put(QueryFieldConstant.PURCHASE_TOTAL_MONEY, totalMoney);
            condition.put(QueryFieldConstant.SUBMITTER_ORG, CollUtil.union(ListUtil.of(LoginUserThreadLocal.getCurrentUserId(), orderDto.getOrgId()), approveRoleMemberService.getEmpRoleIds(LoginUserThreadLocal.getCurrentUserId())));
            orderData.putAll(condition);

            String redisKey = RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.hSetAll(redisKey, condition);
            redisService.expire(redisKey, 2, TimeUnit.HOURS);
            result.setWorkflowExecuteStepDto(workflowService.preStartWorkflow(loginUserDto, workflow, orderData, null));
            String stepKey = RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.set(stepKey, result.getWorkflowExecuteStepDto());
            redisService.expire(stepKey, 2, TimeUnit.HOURS);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(PurchaseOrderDto dto, Boolean enableWorkflow) {
        // 设置id与编号
        dto.setId(IdUtils.getId());
        dto.setOrderNo(StringUtils.getOrderNo("PO"));

        // 从采购申请采购
        if (PurchaseConstant.PURCHASE_MODE_LINK_APPLY.equals(dto.getOrderData().getString("mode"))) {
            this.initProductInfoFromApply(dto);
        } else {
            this.initProductInfo(LoginUserThreadLocal.getCompanyId(), dto);
        }

        AsPurchaseOrder purchaseOrder = BeanUtil.copyProperties(dto, AsPurchaseOrder.class)
                .setSummary(buildSummary(dto.getProductDetail()));
        if (enableWorkflow) {
            purchaseOrder.setApproveStatus(DictConstant.WAIT_APPROVE);
        }

        List<AsPurchaseOrderDetail> details = dto.getProductDetail().stream()
                .map(detailDto -> BeanUtil.copyProperties(detailDto, AsPurchaseOrderDetail.class)
                        .setPurchaseOrderId(dto.getId()).setPurchaseApplyId(detailDto.getPurchaseApplyId()).setPurchaseApplyDetailId(detailDto.getPurchaseApplyDetailId()))
                .collect(Collectors.toList());

        if (PurchaseConstant.PURCHASE_MODE_LINK_APPLY.equals(dto.getOrderData().getString("mode"))) {
            List<AsPurchaseOrderLinkApply> linkApplies = new ArrayList<>();
            dto.getProductDetail().forEach(detail -> {
                detail.getLinkApply().forEach(linkApply -> {
                    AsPurchaseOrderLinkApply link = BeanUtil.copyProperties(linkApply,
                            AsPurchaseOrderLinkApply.class);
                    linkApplies.add(link);
                });
            });
            purchaseOrderLinkApplyService.saveBatch(linkApplies);
        }

        return this.save(purchaseOrder)
                && purchaseOrderDetailService.saveBatch(details);
    }

    @Override
    public PurchaseOrderDto getDetailById(Long id) {
        AsPurchaseOrder purchaseOrder = getById(id);
        if (purchaseOrder == null) {
            return null;
        }
        PurchaseOrderDto purchaseOrderDto = BeanUtil.copyProperties(purchaseOrder, PurchaseOrderDto.class);
        purchaseOrderDto.setProductDetail(
                purchaseOrderDetailService.listByOrderId(id)
                        .stream().map(detail -> BeanUtil.copyProperties(detail, PurchaseOrderDetailDto.class))
                        .collect(Collectors.toList()));
        List<Long> productDetailIds = purchaseOrderDto.getProductDetail().stream().map(PurchaseOrderDetailDto::getId).collect(Collectors.toList());
        Map<Long, List<PurchaseOrderLinkApplyDto>> detailMap = purchaseOrderLinkApplyService.list(Wrappers.lambdaQuery(AsPurchaseOrderLinkApply.class)
                .eq(AsPurchaseOrderLinkApply::getCompanyId, purchaseOrder.getCompanyId())
                .eq(AsPurchaseOrderLinkApply::getPurchaseOrderId, id)
                .in(CollUtil.isNotEmpty(productDetailIds), AsPurchaseOrderLinkApply::getPurchaseOrderDetailId, productDetailIds))
                .stream().map(detail -> BeanUtil.copyProperties(detail, PurchaseOrderLinkApplyDto.class, "id"))
                .collect(Collectors.groupingBy(PurchaseOrderLinkApplyDto::getPurchaseOrderDetailId));
        for (PurchaseOrderDetailDto orderDetailDto : purchaseOrderDto.getProductDetail()) {
            orderDetailDto.setPurchaseApplyId(orderDetailDto.getPurchaseApplyId());
            orderDetailDto.setPurchaseApplyDetailId(orderDetailDto.getPurchaseApplyDetailId());
            orderDetailDto.setLinkApply(detailMap.get(orderDetailDto.getId()));
        }
        return purchaseOrderDto;
    }

    @Override
    public PurchaseOrderDto getDetailByOrderNo(String orderNo) {
        AsPurchaseOrder purchaseOrder = getOne(
                Wrappers.<AsPurchaseOrder>lambdaQuery()
                        .eq(AsPurchaseOrder::getOrderNo, orderNo));
        if (purchaseOrder == null) {
            return null;
        }
        PurchaseOrderDto purchaseOrderDto = BeanUtil.copyProperties(purchaseOrder, PurchaseOrderDto.class);
        purchaseOrderDto.setProductDetail(
                purchaseOrderDetailService.listByOrderId(purchaseOrder.getId())
                        .stream().map(detail -> BeanUtil.copyProperties(detail, PurchaseOrderDetailDto.class))
                        .collect(Collectors.toList()));

        return purchaseOrderDto;
    }

    @Override
    public IPage<AsPurchaseOrder> page(PurchaseOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<Object> page = buildOrderSort(tableAlias, query);
        String permsSql = dataScopeManager.generalWhereSql(AsPurchaseOrderMapper.class, tableAlias);
        return this.getBaseMapper().page(page, query, conditions, LoginUserThreadLocal.getCompanyId(), permsSql);
    }

    @Override
    public List<AsPurchaseOrder> list(PurchaseOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<Object> page = buildOrderSort(tableAlias, query);
        String orderByStr = OrderToStringConverter.orderByToString(page.getOrders());
        String permsSql = dataScopeManager.generalWhereSql(AsPurchaseOrderMapper.class, tableAlias);
        return this.getBaseMapper().list(query, conditions, orderByStr, LoginUserThreadLocal.getCompanyId(), permsSql);
    }

    @Override
    public List<AsPurchaseOrderDetail> getOrderDetailById(Collection<Long> ids) {
        return purchaseOrderDetailService.listByOrderId(ids);
    }

    @Override
    public IPage<PurchaseOrderDetailDto> pageDetail(PurchaseDetailPageQueryDto dto) {
        return purchaseOrderDetailService.page(dto);
    }

    @Override
    public List<JSONObject> dataConvert(PurchaseDetailDataConvertDto dto) {
        return purchaseOrderDetailService.dataConvert(dto);
    }

    @Override
    public List<PurchaseLinkApplyAmountDto> listApplyAmount(PurchaseLinkAmountQueryDto dto) {
        return purchaseOrderLinkApplyService.listApplyAmount(dto);
    }

    @Override
    public List<PurchaseLinkStoreAmountDto> listStoreAmount(PurchaseLinkAmountQueryDto dto) {
        if (PurchaseConstant.PURCHASE_PRODUCT_TYPE_ASSET == dto.getProductType()) {
            return this.getBaseMapper().listStoreAssetAmount(dto);
        }
        return this.getBaseMapper().listStoreMaterialAmount(dto);
    }

    private Page<Object> buildOrderSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_purchase_order";
        }
        Page<Object> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = purchaseCommonService.sortField(AssetConstant.ORDER_TYPE_PURCHASE_ORDER);
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias,  String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    private String buildSummary(List<PurchaseOrderDetailDto> productDetail) {
        return productDetail.stream().map(detail -> detail.getName() + "*" + detail.getQuantity())
                .collect(Collectors.joining("、"));
    }

    private void initProductInfoFromApply(PurchaseOrderDto orderDto) {
        // 获取产品和耗材信息
        List<Long> productIds = orderDto.getProductDetail().stream()
                .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_ASSET)
                .map(PurchaseOrderDetailDto::getProductId)
                .collect(Collectors.toList());
        List<Long> materialIds = orderDto.getProductDetail().stream()
                .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_MATERIAL)
                .map(PurchaseOrderDetailDto::getProductId)
                .collect(Collectors.toList());

        final Map<Long, AsProduct> productMap = new HashMap<>();
        if (CollUtil.isNotEmpty(productIds)) {
            List<AsProduct> products = productService.listByProductIds(LoginUserThreadLocal.getCompanyId(), productIds);
            if (products.size() == 0) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_APPLY_PRODUCT_NO_EXISTS);
            }
            Map<Long, AsProduct> map = products.stream().collect(Collectors.toMap(AsProduct::getId, k -> k));
            productMap.putAll(map);
        }

        final Map<Long, AsMaterial> materialMap = new HashMap<>();
        if (CollUtil.isNotEmpty(materialIds)) {
            List<AsMaterial> materials = materialService.listByIds(materialIds);
            if (materials.size() == 0) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_APPLY_MATERIAL_NO_EXISTS);
            }
            Map<Long, AsMaterial> map = materials.stream().collect(Collectors.toMap(AsMaterial::getId, k -> k));
            materialMap.putAll(map);
        }

        // 初始化
        orderDto.getProductDetail().forEach(detailDto -> {
            List<Long> applyIds =
                    detailDto.getLinkApply().stream()
                            .map(PurchaseOrderLinkApplyDto::getPurchaseApplyId).collect(Collectors.toList());
            List<AsPurchaseApplyDetail> applyDetails = purchaseApplyDetailService.listByApplyIds(
                    detailDto.getProductType(), detailDto.getProductId(), applyIds);
            if (CollUtil.isEmpty(applyDetails)) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_WAIT_LIST_PRODUCT_NO_EXISTS);
            }
            Map<Long, AsPurchaseApply> applyMap = purchaseApplyService.getApplyMap(applyIds);
            Map<String, AsPurchaseApplyDetail> applyDetailMap = applyDetails.stream().collect(
                    Collectors.toMap(o -> o.getPurchaseApplyId() + "_" + o.getProductId(), o -> o, (k1, k2) -> k1));

            detailDto.setId(IdUtils.getId());
            detailDto.setPurchaseOrderId(orderDto.getId());
            if (detailDto.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_ASSET) {
                AsProduct product = productMap.get(detailDto.getProductId());
                detailDto.setName(product.getName());
                detailDto.setCode(product.getCode());
                detailDto.setBrand(product.getBrand());
                detailDto.setModel(product.getModel());
                detailDto.setImages(product.getImages());
            } else {
                AsMaterial material = materialMap.get(detailDto.getProductId());
                detailDto.setName(material.getMaterialData().getString("materialName"));
                detailDto.setCode(material.getMaterialData().getString("materialCode"));
                detailDto.setBrand(material.getMaterialData().getString("brand"));
                detailDto.setModel(material.getMaterialData().getString("model"));
                JSONArray picture = material.getMaterialData().getJSONArray("picture");
                if (picture != null) {
                    detailDto.setImages(picture.toJavaList(String.class));
                }
            }

            detailDto.getLinkApply().forEach(link-> {
                AsPurchaseApply apply = applyMap.get(link.getPurchaseApplyId());
                link.setPurchaseOrderId(detailDto.getPurchaseOrderId());
                link.setPurchaseOrderDetailId(detailDto.getId());
                link.setProductType(detailDto.getProductType());
                link.setProductId(detailDto.getProductId());
                link.setStandardId(detailDto.getStandardId());
                link.setPurchaseApplyOrderNo(apply.getOrderNo());
                String key = link.getPurchaseApplyId() + "_" + link.getProductId();
                AsPurchaseApplyDetail purchaseApplyDetail = applyDetailMap.get(key);
                if (link.getQuantity().compareTo(purchaseApplyDetail.getWaitPurchaseQuantity()) > 0) {
                    String tpl = "采购数量不能大于采购申请单[%s]待采购数量";
                    BusinessExceptionUtil.throwException(String.format(tpl, apply.getOrderNo()));
                }
            });
        });
    }

    private void initProductInfo(Long companyId, PurchaseOrderDto orderDto) {
        // 1.处理产品
        List<Long> productIds = orderDto.getProductDetail().stream()
                .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_ASSET)
                .map(PurchaseOrderDetailDto::getProductId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(productIds)) {
            List<AsProduct> products = productService.listByProductIds(companyId, productIds);
            if (products.size() == 0) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_APPLY_PRODUCT_NO_EXISTS);
            }
            Map<Long, AsProduct> productMap = products.stream().collect(Collectors.toMap(AsProduct::getId, k -> k));
            orderDto.getProductDetail().stream()
                    .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_ASSET)
                    .forEach(detailDto -> {
                        AsProduct product = productMap.get(detailDto.getProductId());
                        detailDto.setId(IdUtils.getId());
                        detailDto.setPurchaseOrderId(orderDto.getId());
                        detailDto.setName(product.getName());
                        detailDto.setCode(product.getCode());
                        detailDto.setBrand(product.getBrand());
                        detailDto.setModel(product.getModel());
                        detailDto.setImages(product.getImages());
                    });
        }
        // 2.处理耗材
        List<Long> materialIds = orderDto.getProductDetail().stream()
                .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_MATERIAL)
                .map(PurchaseOrderDetailDto::getProductId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(materialIds)) {
            List<AsMaterial> materials = materialService.listByIds(materialIds);
            if (materials.size() == 0) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_APPLY_MATERIAL_NO_EXISTS);
            }
            Map<Long, AsMaterial> materialMap = materials.stream().collect(Collectors.toMap(AsMaterial::getId, k -> k));
            orderDto.getProductDetail().stream()
                    .filter(o -> o.getProductType() == PurchaseConstant.PURCHASE_PRODUCT_TYPE_MATERIAL)
                    .forEach(detailDto -> {
                        AsMaterial material = materialMap.get(detailDto.getProductId());
                        detailDto.setId(IdUtils.getId());
                        detailDto.setPurchaseOrderId(orderDto.getId());
                        detailDto.setName(material.getMaterialData().getString("materialName"));
                        detailDto.setCode(material.getMaterialData().getString("materialCode"));
                        detailDto.setBrand(material.getMaterialData().getString("brand"));
                        detailDto.setModel(material.getMaterialData().getString("model"));
                        JSONArray picture = material.getMaterialData().getJSONArray("picture");
                        if (picture != null) {
                            detailDto.setImages(picture.toJavaList(String.class));
                        }
                    });
        }
    }

    @Override
    public AsPurchaseOrder tempGetById(Long orderId) {
        return this.getBaseMapper().tempGetById(orderId);
    }

    @Override
    public List<PurchaseOrderDetailDto> listExtInfo(PurchaseOrderDetailExtInfoQueryDto queryDto) {
        AsPurchaseOrder purchaseOrder = getOne(Wrappers.lambdaQuery(AsPurchaseOrder.class).eq(AsPurchaseOrder::getOrderNo, queryDto.getPurchaseOrderNo()));
        if (purchaseOrder == null) {
            return ListUtil.empty();
        }
        PurchaseDetailPageQueryDto pageQueryDto = new PurchaseDetailPageQueryDto();
        pageQueryDto.setPageSize(999);
        pageQueryDto.setExcludeIds(queryDto.getProductId());
        pageQueryDto.setProductType(queryDto.getProductType());
        pageQueryDto.setOrderId(purchaseOrder.getId());
        IPage<PurchaseOrderDetailDto> asPurchaseOrderDetailIPage = pageDetail(pageQueryDto);
        return asPurchaseOrderDetailIPage.getRecords();
    }
}
