package com.niimbot.asset.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.purchase.model.AsPurchaseOrderLinkApply;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkApplyAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购单明细关联采购申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@EnableDataPerm
public interface AsPurchaseOrderLinkApplyMapper extends BaseMapper<AsPurchaseOrderLinkApply> {
    List<PurchaseLinkApplyAmountDto> listApplyAmount(@Param("query") PurchaseLinkAmountQueryDto query);

    List<PurchaseLinkOrderAmountDto> listOrderAmount(@Param("query") PurchaseLinkOrderAmountQueryDto query);
}
