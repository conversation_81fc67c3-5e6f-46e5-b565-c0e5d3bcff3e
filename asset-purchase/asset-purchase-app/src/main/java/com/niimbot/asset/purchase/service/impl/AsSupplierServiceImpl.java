package com.niimbot.asset.purchase.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.purchase.mapper.AsSupplierMapper;
import com.niimbot.asset.purchase.model.AsSupplier;
import com.niimbot.asset.purchase.service.AsSupplierService;
import com.niimbot.purchase.SupplierDto;
import com.niimbot.purchase.SupplierPageQueryDto;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 供应商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Slf4j
@Service
public class AsSupplierServiceImpl extends ServiceImpl<AsSupplierMapper, AsSupplier> implements AsSupplierService, ApplicationListener<ApplicationReadyEvent> {
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource
    private RedisService redisService;

    @Override
    public AsSupplier getByName(String name) {
        return this.getOne(Wrappers.<AsSupplier>lambdaUpdate()
                .eq(AsSupplier::getName, name));
    }

    @Override
    public IPage<SupplierDto> pageQueryDto(SupplierPageQueryDto query) {
        return this.getBaseMapper().pageQueryDto(query.buildIPage(), query);
    }

    @Override
    public List<SupplierDto> listQueryDto(String name) {
        return this.getBaseMapper().listQueryDto(name);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return ;
        }

        taskExecutor.execute(() -> {
            List<AsSupplier> suppliers = list();
            Map<String, String> collect = new ConcurrentHashMap<>();
            suppliers.forEach(it -> {
                collect.put(Convert.toStr(it.getId()), it.getName());
            });
            redisService.hSetAll(RedisConstant.supplierDictKey(), collect);
            log.info("init supplier cache finish");
        });
    }
}
