package com.niimbot.asset.purchase.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.purchase.mapper.AsPurchaseOrderLinkApplyMapper;
import com.niimbot.asset.purchase.model.AsPurchaseOrderLinkApply;
import com.niimbot.asset.purchase.service.AsPurchaseOrderLinkApplyService;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkApplyAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountQueryDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 采购单明细关联采购申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Service
public class AsPurchaseOrderLinkApplyServiceImpl extends ServiceImpl<AsPurchaseOrderLinkApplyMapper, AsPurchaseOrderLinkApply> implements AsPurchaseOrderLinkApplyService {

    @Override
    public List<PurchaseLinkApplyAmountDto> listApplyAmount(PurchaseLinkAmountQueryDto dto) {
        return this.getBaseMapper().listApplyAmount(dto);
    }

    @Override
    public List<PurchaseLinkOrderAmountDto> listOrderAmount(PurchaseLinkOrderAmountQueryDto dto) {
        return this.getBaseMapper().listOrderAmount(dto);
    }
}
