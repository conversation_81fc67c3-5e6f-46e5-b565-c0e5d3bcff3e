package com.niimbot.asset.purchase.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.purchase.model.AsPurchaseOrderDetail;
import com.niimbot.purchase.PurchaseDetailDataConvertDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface AsPurchaseOrderDetailService extends IService<AsPurchaseOrderDetail> {
    IPage<PurchaseOrderDetailDto> page(PurchaseDetailPageQueryDto dto);

    List<AsPurchaseOrderDetail> listByOrderId(Long orderId);

    List<AsPurchaseOrderDetail> listByOrderId(Collection<Long> orderIds);

    List<JSONObject> dataConvert(PurchaseDetailDataConvertDto dto);
}
