package com.niimbot.asset.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.purchase.model.AsPurchaseOrderLinkApply;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkApplyAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountQueryDto;

import java.util.List;

/**
 * <p>
 * 采购单明细关联采购申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface AsPurchaseOrderLinkApplyService extends IService<AsPurchaseOrderLinkApply> {

    List<PurchaseLinkApplyAmountDto> listApplyAmount(PurchaseLinkAmountQueryDto dto);

    List<PurchaseLinkOrderAmountDto> listOrderAmount(PurchaseLinkOrderAmountQueryDto dto);
}
