package com.niimbot.asset.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.purchase.model.AsPurchaseOrderType;
import com.niimbot.purchase.PurchaseOrderTypeDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface AsPurchaseOrderTypeMapper extends BaseMapper<AsPurchaseOrderType> {

    List<PurchaseOrderTypeDto> listOrderType(@Param("companyId") Long companyId);
}
