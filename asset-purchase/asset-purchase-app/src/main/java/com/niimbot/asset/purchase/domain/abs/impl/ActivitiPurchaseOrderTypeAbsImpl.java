package com.niimbot.asset.purchase.domain.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.activiti.abs.ActivitiPurchaseOrderTypeAbs;
import com.niimbot.asset.activiti.dto.OrderTypeGetQry;
import com.niimbot.asset.activiti.dto.clientobjct.PurchaseOrderTypeCO;
import com.niimbot.asset.purchase.mapstruct.PurchaseActivitiMapStruct;
import com.niimbot.asset.purchase.model.AsPurchaseOrderType;
import com.niimbot.asset.purchase.service.AsPurchaseOrderTypeService;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/client/abs/purchase/activitiPurchaseOrderTypeAbs/")
@RequiredArgsConstructor
public class ActivitiPurchaseOrderTypeAbsImpl implements ActivitiPurchaseOrderTypeAbs {

    private final AsPurchaseOrderTypeService orderTypeService;

    private final PurchaseActivitiMapStruct purchaseActivitiMapStruct;

    @Override
    public PurchaseOrderTypeCO getOneForApprovalMessage(OrderTypeGetQry qry) {
        AsPurchaseOrderType orderType = orderTypeService.getOne(
                Wrappers.lambdaQuery(AsPurchaseOrderType.class)
                        .select(AsPurchaseOrderType::getName, AsPurchaseOrderType::getType)
                        .eq(AsPurchaseOrderType::getCompanyId, qry.getCompanyId())
                        .eq(AsPurchaseOrderType::getType, qry.getOrderType())
        );
        return purchaseActivitiMapStruct.convertPurchaseOrderTypeDoToCo(orderType);
    }

}
