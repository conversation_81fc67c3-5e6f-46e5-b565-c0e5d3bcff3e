package com.niimbot.asset.purchase.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.purchase.model.AsPurchaseOrderType;
import com.niimbot.asset.purchase.service.AsPurchaseOrderTypeService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("server/purchase/order/setting")
@RequiredArgsConstructor
public class AsPurchaseOrderSettingFeignClient {

    private final AsPurchaseOrderTypeService purchaseOrderTypeService;

    @GetMapping(value = "/orderType/app/list")
    public List<AsPurchaseOrderType> appListOrderTypeShow() {
        return purchaseOrderTypeService.list(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsPurchaseOrderType::getEnableApp, true));
    }

}
