package com.niimbot.asset.purchase.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.PurchaseResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.purchase.model.AsPurchaseOrderType;
import com.niimbot.asset.purchase.service.AsPurchaseOrderTypeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.purchase.PurchaseOrderTypeDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/10/18 14:19
 */
@RestController
@RequestMapping("server/purchase/orderType")
@RequiredArgsConstructor
public class AsPurchaseOrderTypeServiceController {
    private final AsPurchaseOrderTypeService purchaseOrderTypeService;

    /**
     * 单据类型详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/getById/{id}")
    public AsPurchaseOrderType getOrderTypeById(@PathVariable("id") Long id) {
        return purchaseOrderTypeService.getOne(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, getCompanyId())
                        .eq(AsPurchaseOrderType::getId, id));
    }

    /**
     * 更新单据类型
     *
     * @param orderType
     * @return
     */
    @PutMapping
    public Boolean updateOrderType(@RequestBody AsPurchaseOrderType orderType) {
        AsPurchaseOrderType type = getOrderTypeById(orderType.getId());
        if (type == null) {
            throw new BusinessException(PurchaseResultCode.ORDER_TYPE_NOT_EXISTS, orderType.getName());
        }
        if (!purchaseOrderTypeService.updateById(orderType)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 单据类型列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public List<PurchaseOrderTypeDto> listOrderType() {
        return purchaseOrderTypeService.listOrderType();
    }

    @GetMapping("/enableWorkflow/{orderType}")
    public Boolean enableWorkflow(@PathVariable("orderType") Integer orderType) {
        return purchaseOrderTypeService.enableWorkflow(orderType);
    }

    private Long getCompanyId() {
        if (LoginUserThreadLocal.getCusUser() == null) {
            return 0L;
        }
        return LoginUserThreadLocal.getCompanyId();
    }
}
