package com.niimbot.asset.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.purchase.model.AsPurchaseApplyDetail;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购申请明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface AsPurchaseApplyDetailService extends IService<AsPurchaseApplyDetail> {
    List<AsPurchaseApplyDetail> listByApplyId(Long applyId);

    List<AsPurchaseApplyDetail> listByApplyId(Collection<Long> applyIds);

    List<AsPurchaseApplyDetail> listByApplyIds(Integer productType, Long productId, Collection<Long> appIds);

    IPage<AsPurchaseApplyDetail> page(PurchaseDetailPageQueryDto dto);
}
