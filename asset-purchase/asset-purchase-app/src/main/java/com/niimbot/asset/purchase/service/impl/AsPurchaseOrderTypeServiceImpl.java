package com.niimbot.asset.purchase.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.core.enums.PurchaseResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.purchase.mapper.AsPurchaseOrderTypeMapper;
import com.niimbot.asset.purchase.model.AsPurchaseOrderType;
import com.niimbot.asset.purchase.service.AsPurchaseOrderTypeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.purchase.PurchaseOrderTypeDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Service
public class AsPurchaseOrderTypeServiceImpl extends ServiceImpl<AsPurchaseOrderTypeMapper, AsPurchaseOrderType> implements AsPurchaseOrderTypeService {

    @Autowired
    private ActWorkflowService workflowService;

    @Override
    public void initCompanyOrderType(Long companyId) {
        List<AsPurchaseOrderType> orderTypes = this.list(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, 0L));
        if (orderTypes.isEmpty()) {
            return;
        }
        List<AsPurchaseOrderType> companyOrderTypes = orderTypes.stream()
                .peek(type ->
                        type.setId(null).setCompanyId(companyId))
                .collect(Collectors.toList());
        this.saveBatch(companyOrderTypes);
    }

    @Override
    public boolean syncCompanyOrderType(Long companyId) {
        List<AsPurchaseOrderType> orderTypes = this.list(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .in(AsPurchaseOrderType::getCompanyId, Arrays.asList(companyId, 0L)));
        List<Integer> exists = orderTypes.stream()
                .filter(type -> type.getCompanyId().equals(companyId))
                .map(AsPurchaseOrderType::getType)
                .collect(Collectors.toList());
        List<AsPurchaseOrderType> systemOrderTypes = orderTypes.stream()
                .filter(field -> field.getCompanyId().equals(0L))
                .filter(type -> {
                    if (CollUtil.isNotEmpty(exists)) {
                        return !exists.contains(type.getType());
                    }
                    return true;
                })
                .collect(Collectors.toList());
        List<AsPurchaseOrderType> companyOrderTypes = systemOrderTypes.stream()
                .peek(type ->
                        type.setId(null).setCompanyId(companyId))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(companyOrderTypes)) {
            return true;
        }
        return this.saveBatch(companyOrderTypes);
    }

    @Override
    public Boolean enableWorkflow(Integer orderType) {
        AsPurchaseOrderType one = getOne(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsPurchaseOrderType::getType, orderType));
        if (one == null) {
            throw new BusinessException(PurchaseResultCode.ORDER_TYPE_NOT_EXISTS, String.valueOf(orderType));
        }
        return workflowService.enableWorkflow(one.getActivitiKey());

    }

    @Override
    public List<PurchaseOrderTypeDto> listOrderType() {
        return getBaseMapper().listOrderType(LoginUserThreadLocal.getCompanyId());
    }
}
