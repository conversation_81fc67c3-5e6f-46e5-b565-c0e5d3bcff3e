package com.niimbot.asset.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.purchase.model.AsPurchaseApplyDetail;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购申请明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@EnableDataPerm
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsPurchaseApplyDetailMapper.class)
public interface AsPurchaseApplyDetailMapper extends BaseMapper<AsPurchaseApplyDetail> {

    List<AsPurchaseApplyDetail> listByApplyId(@Param("applyId") Long applyId);

    List<AsPurchaseApplyDetail> listByApplyIdList(@Param("applyIds") Collection<Long> applyIds);

    List<AsPurchaseApplyDetail> listByApplyIds(@Param("productType") Integer productType, @Param("productId") Long productId,
                                               @Param("applyIds") Collection<Long> applyIds);

    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @return
     */
    IPage<AsPurchaseApplyDetail> page(IPage<AsPurchaseApplyDetail> page, @Param("query") PurchaseDetailPageQueryDto query);
}
