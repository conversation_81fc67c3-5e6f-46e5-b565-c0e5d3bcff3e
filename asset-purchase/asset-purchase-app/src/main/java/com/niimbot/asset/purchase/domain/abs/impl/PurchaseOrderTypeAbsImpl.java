package com.niimbot.asset.purchase.domain.abs.impl;

import com.niimbot.asset.purchase.service.AsPurchaseOrderTypeService;
import com.niimbot.asset.system.abs.PurchaseOrderTypeAbs;
import com.niimbot.asset.system.dto.PurchaseOrderTypeInitCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/10/18 15:37
 */
@RestController
@RequestMapping("/client/abs/purchase/purchaseOrderFieldAbs")
@RequiredArgsConstructor
public class PurchaseOrderTypeAbsImpl implements PurchaseOrderTypeAbs {
    private final AsPurchaseOrderTypeService purchaseOrderTypeService;

    @Override
    public void initCompanyOrderType(PurchaseOrderTypeInitCmd cmd) {
        purchaseOrderTypeService.initCompanyOrderType(cmd.getCompanyId());
    }
}
