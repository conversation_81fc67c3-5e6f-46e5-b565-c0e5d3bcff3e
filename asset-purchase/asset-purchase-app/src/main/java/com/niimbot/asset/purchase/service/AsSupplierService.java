package com.niimbot.asset.purchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.purchase.model.AsSupplier;
import com.niimbot.purchase.SupplierDto;
import com.niimbot.purchase.SupplierPageQueryDto;

import java.util.List;

/**
 * <p>
 * 供应商 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface AsSupplierService extends IService<AsSupplier> {
    /**
     * 通过名称查找供应商
     *
     * @param name 名称
     * @return 供应商
     */
    AsSupplier getByName(String name);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    IPage<SupplierDto> pageQueryDto(SupplierPageQueryDto query);

    /**
     * 列表查询
     *
     * @param name
     * @return
     */
    List<SupplierDto> listQueryDto(String name);
}
