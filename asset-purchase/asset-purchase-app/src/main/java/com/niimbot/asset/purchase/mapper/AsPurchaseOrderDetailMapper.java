package com.niimbot.asset.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.purchase.model.AsPurchaseApplyDetail;
import com.niimbot.asset.purchase.model.AsPurchaseOrderDetail;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.purchase.PurchaseDetailDataConvertDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@EnableDataPerm(excludeMethodName = {"page", "list", "listByOrderId", "listByOrderIds"})
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsPurchaseOrderDetailMapper.class)
public interface AsPurchaseOrderDetailMapper extends BaseMapper<AsPurchaseOrderDetail> {
    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @return
     */
    IPage<PurchaseOrderDetailDto> page(IPage<AsPurchaseApplyDetail> page,
                                       @Param("query") PurchaseDetailPageQueryDto query,
                                       @Param("companyId") Long companyId);

    List<AsPurchaseOrderDetail> listByOrderId(@Param("orderId") Long orderId,
                                              @Param("companyId") Long companyId);

    List<AsPurchaseOrderDetail> listByOrderIds(@Param("orderIds") Collection<Long> orderIds,
                                               @Param("companyId") Long companyId);

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    List<AsPurchaseOrderDetail> list(@Param("query") PurchaseDetailDataConvertDto query,
                                     @Param("companyId") Long companyId);
}
