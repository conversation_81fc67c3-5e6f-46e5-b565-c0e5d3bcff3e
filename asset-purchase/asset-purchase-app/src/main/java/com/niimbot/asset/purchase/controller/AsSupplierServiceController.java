package com.niimbot.asset.purchase.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.core.enums.PurchaseResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheSupplierStrategy;
import com.niimbot.asset.purchase.model.AsSupplier;
import com.niimbot.asset.purchase.model.AsSupplierContact;
import com.niimbot.asset.purchase.service.AsSupplierContactService;
import com.niimbot.asset.purchase.service.AsSupplierService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.purchase.SupplierContactDto;
import com.niimbot.purchase.SupplierDto;
import com.niimbot.purchase.SupplierPageQueryDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 供应商 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@RestController
@RequestMapping("server/purchase/supplier")
@RequiredArgsConstructor
public class AsSupplierServiceController {
    private final AsSupplierService supplierService;
    private final AsSupplierContactService supplierContactService;

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Long save(@RequestBody SupplierDto dto) {
        if (supplierService.getByName(dto.getName()) != null) {
            throw new BusinessException(PurchaseResultCode.SUPPLIER_NAME_EXISTS);
        }
        Long id = IdUtils.getId();
        AsSupplier supplier = BeanUtil.copyProperties(dto, AsSupplier.class)
                .setId(id);
        if (CollUtil.isEmpty(dto.getContacts())) {
            supplierService.save(supplier);
        } else {
            List<AsSupplierContact> contactList = dto.getContacts().stream()
                    .map(contactDto -> BeanUtil.copyProperties(contactDto, AsSupplierContact.class)
                            .setSupplierId(id))
                    .collect(Collectors.toList());
            supplierService.save(supplier);
            supplierContactService.saveBatch(contactList);
        }
        return supplier.getId();
    }

    @PutMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(@RequestBody SupplierDto dto) {
        AsSupplier byName = supplierService.getByName(dto.getName());
        if (byName != null && !byName.getId().equals(dto.getId())) {
            throw new BusinessException(PurchaseResultCode.SUPPLIER_NAME_EXISTS);
        }
        AsSupplier supplier = BeanUtil.copyProperties(dto, AsSupplier.class);

        if (CollUtil.isEmpty(dto.getContacts())) {
            supplierContactService.removeBySupplierId(dto.getId());
            return supplierService.updateById(supplier);
        }

        List<AsSupplierContact> contactList = dto.getContacts().stream()
                .map(contactDto -> BeanUtil.copyProperties(contactDto, AsSupplierContact.class, AsSupplierContact.ID)
                        .setSupplierId(dto.getId()))
                .collect(Collectors.toList());
        supplierContactService.removeBySupplierId(dto.getId());
        supplierService.updateById(supplier);
        supplierContactService.saveBatch(contactList);

        SpringUtil.getBean(CacheSupplierStrategy.class).evictCache(supplier.getId());
        return true;
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Long id) {
        return supplierService.removeById(id);
    }

    @GetMapping("/{id}")
    @AutoConvert
    public SupplierDto getById(@PathVariable("id") Long id) {
        AsSupplier supplier = supplierService.getById(id);
        if (supplier == null) {
            return null;
        }
        SupplierDto supplierDto = BeanUtil.copyProperties(supplier, SupplierDto.class);
        supplierDto.setContacts(
                supplierContactService.getListBySupplierId(id)
                        .stream()
                        .map(contact -> BeanUtil.copyProperties(contact, SupplierContactDto.class))
                        .collect(Collectors.toList()));
        return supplierDto;
    }

    @GetMapping("/page")
    @AutoConvert
    public IPage<SupplierDto> page(SupplierPageQueryDto query) {
        return supplierService.pageQueryDto(query);
    }

    @GetMapping("/list")
    @AutoConvert
    public List<SupplierDto> list(@RequestParam(value = "name", required = false) String name) {
        return supplierService.listQueryDto(name);
    }

    @PostMapping(value = "/listByIds")
    public List<AsSupplier> listByIds(@RequestBody List<Long> ids) {
        return supplierService.listByIds(ids);
    }
}
