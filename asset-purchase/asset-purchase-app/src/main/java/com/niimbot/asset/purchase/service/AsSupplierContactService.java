package com.niimbot.asset.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.purchase.model.AsSupplierContact;

import java.util.List;

/**
 * <p>
 * 供应商联系人 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface AsSupplierContactService extends IService<AsSupplierContact> {
    /**
     * 通过供应商id删除联系人
     * @param supplierId 供应商id
     * @return
     */
    boolean removeBySupplierId(Long supplierId);

    /**
     * 通过供应商id查询联系人列表
     * @param supplierId 供应商id
     * @return 联系人列表
     */
    List<AsSupplierContact> getListBySupplierId(Long supplierId);
}
