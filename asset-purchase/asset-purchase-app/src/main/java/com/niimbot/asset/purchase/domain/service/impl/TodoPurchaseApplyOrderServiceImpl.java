package com.niimbot.asset.purchase.domain.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.purchase.model.AsPurchaseApply;
import com.niimbot.asset.purchase.service.AsPurchaseApplyService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.TodoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/6 上午10:51
 */
@Slf4j
@Service
public class TodoPurchaseApplyOrderServiceImpl implements TodoOrderService {

    private static final List<Integer> supportTypeList = ListUtil.of(AssetConstant.ORDER_TYPE_PURCHASE);

    @Autowired
    private AsPurchaseApplyService purchaseApplyService;

    @Override
    public Boolean supportOrderType(Integer orderType) {
        return supportTypeList.contains(orderType);
    }

    @Override
    public JSONObject getOrderJson(Long businessId) {
        AsPurchaseApply purchaseApply = purchaseApplyService.getById(businessId);
        if (Objects.isNull(purchaseApply)) {
            return null;
        }

        JSONObject result = JsonUtil.toJsonObject(purchaseApply, o -> {
            JSONObject orderData = o.getJSONObject("orderData");
            o.fluentPutAll(orderData);
            o.fluentRemove("orderData");
        });
        return result;
    }

    @Override
    public AsTodo getOrderTodo(AsTodo todo) {
        AsPurchaseApply purchaseApply = purchaseApplyService.getById(todo.getBusinessId());
        if (Objects.isNull(purchaseApply)) {
            return null;
        }

        //如果orderData为空，优先设置下
        if (Objects.isNull(purchaseApply.getOrderData())) {
            purchaseApply.setOrderData(new JSONObject());
        }

        purchaseApply.getOrderData().put("summary", purchaseApply.getSummary());
        AsTodo result = new AsTodo()
                .setSummary(purchaseApply.getSummary())
                .setCreateBy(purchaseApply.getCreateBy())
                .setOrderData(JSONObject.toJSONString(purchaseApply.getOrderData()));
        return result;
    }
}
