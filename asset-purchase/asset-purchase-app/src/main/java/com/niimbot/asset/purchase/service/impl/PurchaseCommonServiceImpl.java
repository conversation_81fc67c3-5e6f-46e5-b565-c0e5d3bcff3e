package com.niimbot.asset.purchase.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AbstractOrderDto;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.purchase.service.PurchaseCommonService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.sdk.FormValidatorCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/10/18 16:22
 */
@Service
@RequiredArgsConstructor
public class PurchaseCommonServiceImpl implements PurchaseCommonService {
    private final AsFormService formService;
    private final AssetUtil assetUtil;
    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;

    @Override
    public void verify(AbstractOrderDto orderDto) {
        JSONObject submitOrderData = orderDto.getOrderData();
        FormVO orderFormVO = getForm(orderDto.getOrderType());
        if (submitOrderData.entrySet().size() != orderFormVO.getFormFields().size()) {
            throw new BusinessException(SystemResultCode.ORDER_SETTING_AND_DATA_INCONSISTENT);
        }
        FormValidatorCmd orderFormValidatorCmd = new FormValidatorCmd(submitOrderData, orderFormVO.getFormId(),
                Convert.toStr(LoginUserThreadLocal.getCompanyId()));
        formService.validatorForm(orderFormValidatorCmd);
    }

    @Override
    public FormVO getForm(Integer orderType) {
        return formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType(),
                LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public void translation(AbstractOrderDto orderDto) {
        FormVO formVO = getForm(orderDto.getOrderType());
        List<DefaultTranslateUtil.FieldTranslation> translations =
                formVO.getFormFields().stream()
                        .map(
                                f ->
                                        new DefaultTranslateUtil.FieldTranslation()
                                                .setFieldCode(f.getFieldCode())
                                                .setFieldType(f.getFieldType())
                                                .setTranslationCode(f.getTranslationCode()))
                        .collect(Collectors.toList());
        assetUtil.translateAssetJson(orderDto.getOrderData(), translations);
    }

    @Override
    public QueryConditionSortDto sortField(Integer orderType) {
        QueryConditionSortDto querySort = new QueryConditionSortDto();
        String type = QueryFieldConstant.PURCHASE_ORDER_TYPE_HEAD.get(orderType);
        if (StrUtil.isBlank(type)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "此采购单据类型不存在");
        }
        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(type);
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

//        QueryFieldConstant.Field approveStatusField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(approveStatusField.getName(), approveStatusField.getCode(), approveStatusField.getType()));

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

//        QueryFieldConstant.Field createByField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_BY);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(createByField.getName(), createByField.getCode(), createByField.getType()));

        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }
}
