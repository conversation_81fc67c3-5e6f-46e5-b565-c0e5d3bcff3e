package com.niimbot.asset.purchase.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.purchase.model.AsPurchaseOrder;
import com.niimbot.asset.purchase.model.AsPurchaseOrderDetail;
import com.niimbot.purchase.PurchaseDetailDataConvertDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkApplyAmountDto;
import com.niimbot.purchase.PurchaseLinkStoreAmountDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;
import com.niimbot.purchase.PurchaseOrderDetailExtInfoQueryDto;
import com.niimbot.purchase.PurchaseOrderDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
public interface AsPurchaseOrderService extends IService<AsPurchaseOrder> {

    WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, PurchaseOrderDto orderDto);

    /**
     * 创建采购单
     *
     * @param dto
     * @param enableWorkflow
     * @return
     */
    Boolean create(PurchaseOrderDto dto, Boolean enableWorkflow);

    /**
     * 采购单详情
     *
     * @param id
     * @return
     */
    PurchaseOrderDto getDetailById(Long id);

    PurchaseOrderDto getDetailByOrderNo(String orderNo);

    IPage<AsPurchaseOrder> page(PurchaseOrderQueryDto query);

    List<AsPurchaseOrder> list(PurchaseOrderQueryDto query);

    List<AsPurchaseOrderDetail> getOrderDetailById(Collection<Long> ids);

    IPage<PurchaseOrderDetailDto> pageDetail(PurchaseDetailPageQueryDto dto);

    List<JSONObject> dataConvert(PurchaseDetailDataConvertDto dto);

    List<PurchaseLinkApplyAmountDto> listApplyAmount(PurchaseLinkAmountQueryDto dto);

    List<PurchaseLinkStoreAmountDto> listStoreAmount(PurchaseLinkAmountQueryDto dto);

    AsPurchaseOrder tempGetById(Long orderId);

    List<PurchaseOrderDetailDto> listExtInfo(PurchaseOrderDetailExtInfoQueryDto queryDto);
}
