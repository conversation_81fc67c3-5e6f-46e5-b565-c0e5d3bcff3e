package com.niimbot.asset.purchase.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.framework.dataperm.annonation.OrderFilterColumn;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.framework.dataperm.annonation.UserFilterColumn;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 采购单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsPurchaseOrder对象", description="采购单")
@TableName(value = "as_purchase_order", autoResultMap = true)
public class AsPurchaseOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @OrderFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_ASSET_ORDER, subBizCode = AssetConstant.AUTHORITY_APPROVAL_USER)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "单据编号：字母前缀PO+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "单据审批状态 0-无审批 1-待审批 2-已驳回 3-已同意 4-已撤销")
    private Short approveStatus;

    @ApiModelProperty(value = "入库状态 0-无 1-待入库 2-入库中 3-部分入库 4-入库完成")
    @TableField(exist = false)
    private Integer storeStatus;

    @ApiModelProperty(value = "自定义数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject orderData;

    @ApiModelProperty(value = "合计数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    @UserFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_ASSET_ORDER, subBizCode = AssetConstant.AUTHORITY_CREATE_USER)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
