package com.niimbot.asset.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.purchase.model.AsSupplier;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.purchase.SupplierDto;
import com.niimbot.purchase.SupplierPageQueryDto;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 供应商 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@EnableDataPerm
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsSupplierMapper.class)
public interface AsSupplierMapper extends BaseMapper<AsSupplier> {
    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @return
     */
    IPage<SupplierDto> pageQueryDto(IPage<SupplierDto> page, @Param("query") SupplierPageQueryDto query);

    /**
     * 列表查询
     *
     * @param name
     * @return
     */
    List<SupplierDto> listQueryDto(@Param("name") String name);
}
