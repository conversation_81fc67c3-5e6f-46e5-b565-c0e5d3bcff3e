package com.niimbot.asset.purchase.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.query.OrderToStringConverter;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.purchase.mapper.AsPurchaseApplyMapper;
import com.niimbot.asset.purchase.model.AsPurchaseApply;
import com.niimbot.asset.purchase.model.AsPurchaseApplyDetail;
import com.niimbot.asset.purchase.service.AsPurchaseApplyDetailService;
import com.niimbot.asset.purchase.service.AsPurchaseApplyService;
import com.niimbot.asset.purchase.service.AsPurchaseOrderLinkApplyService;
import com.niimbot.asset.purchase.service.PurchaseCommonService;
import com.niimbot.page.SortQuery;
import com.niimbot.purchase.PurchaseApplyDetailDto;
import com.niimbot.purchase.PurchaseApplyDetailSelectDto;
import com.niimbot.purchase.PurchaseApplyDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountDto;
import com.niimbot.purchase.PurchaseLinkOrderAmountQueryDto;
import com.niimbot.purchase.PurchaseOrderDetailQueryDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 采购申请 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
@RequiredArgsConstructor
public class AsPurchaseApplyServiceImpl extends ServiceImpl<AsPurchaseApplyMapper, AsPurchaseApply> implements AsPurchaseApplyService {
    private final AsPurchaseApplyDetailService purchaseApplyDetailService;
    private final AsPurchaseOrderLinkApplyService purchaseOrderLinkApplyService;
    private final PurchaseCommonService purchaseCommonService;
    private final MySqlPurchaseOrderQueryConditionResolver conditionResolver;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(PurchaseApplyDto dto, Boolean enableWorkflow) {
        // 设置id与编号
        dto.setOrderNo(StringUtils.getOrderNo("PR"));
        // dto.setId(IdUtils.getId());
        AsPurchaseApply purchaseApply = BeanUtil.copyProperties(dto, AsPurchaseApply.class)
                .setSummary(buildSummary(dto.getProductDetail()));

        if (enableWorkflow) {
            purchaseApply.setApproveStatus(DictConstant.WAIT_APPROVE);
        }

        List<AsPurchaseApplyDetail> details = dto.getProductDetail().stream()
                .map(detailDto -> BeanUtil.copyProperties(detailDto, AsPurchaseApplyDetail.class)
                        .setId(null).setPurchaseApplyId(dto.getId()))
                .collect(Collectors.toList());

        return this.save(purchaseApply) && purchaseApplyDetailService.saveBatch(details);
    }

    @Override
    public AsPurchaseApply getWithStatusById(Long id) {
        return this.getBaseMapper().getWithStatusById(id);
    }

    @Override
    public IPage<AsPurchaseApply> page(PurchaseOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<Object> page = buildOrderSort(tableAlias, query);
        return this.getBaseMapper().page(page, query, conditions);
    }

    @Override
    public List<AsPurchaseApply> list(PurchaseOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<Object> page = buildOrderSort(tableAlias, query);
        String orderByStr = OrderToStringConverter.orderByToString(page.getOrders());
        return this.getBaseMapper().list(query, conditions, orderByStr);
    }

    @Override
    public IPage<PurchaseApplyDetailSelectDto> pageSelect(PurchaseOrderDetailQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        return this.getBaseMapper().pageSelect(query.buildIPage(), query, conditions);
    }

    @Override
    public List<AsPurchaseApplyDetail> getApplyDetailById(Long id) {
        return purchaseApplyDetailService.listByApplyId(id);
    }

    @Override
    public List<AsPurchaseApplyDetail> getApplyDetailById(Collection<Long> ids) {
        return purchaseApplyDetailService.listByApplyId(ids);
    }

    @Override
    public Map<Long, AsPurchaseApply> getApplyMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return MapUtil.empty();
        }
        return listByIds(ids).stream().collect(
                Collectors.toMap(AsPurchaseApply::getId, o -> o, (k1, k2) -> k1));
    }

    @Override
    public IPage<AsPurchaseApplyDetail> pageDetail(PurchaseDetailPageQueryDto dto) {
        return purchaseApplyDetailService.page(dto);
    }

    @Override
    public List<PurchaseLinkOrderAmountDto> listOrderAmount(PurchaseLinkOrderAmountQueryDto dto) {
        return purchaseOrderLinkApplyService.listOrderAmount(dto);
    }

    private String buildSummary(List<PurchaseApplyDetailDto> productDetail) {
        return productDetail.stream().map(detail -> detail.getName() + "*" + detail.getQuantity())
                .collect(Collectors.joining("、"));
    }

    /**
     * 通用排序处理
     *
     * @param tableAlias
     * @param queryDto
     * @return
     */
    private Page<Object> buildOrderSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_purchase_apply";
        }
        Page<Object> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = purchaseCommonService.sortField(AssetConstant.ORDER_TYPE_PURCHASE);
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlPurchaseOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    @Override
    public AsPurchaseApply tempGetById(Long orderId) {
        return this.getBaseMapper().tempGetById(orderId);
    }
}
