package com.niimbot.asset.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.purchase.model.AsPurchaseApply;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.purchase.PurchaseApplyDetailSelectDto;
import com.niimbot.purchase.PurchaseOrderDetailQueryDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 采购申请 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@EnableDataPerm(excludeMethodName = {"tempGetById"})
public interface AsPurchaseApplyMapper extends BaseMapper<AsPurchaseApply> {

    AsPurchaseApply getWithStatusById(@Param("id")Long id);

    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @param permsSql
     * @return
     */
    IPage<AsPurchaseApply> page(IPage<Object> page,
                                @Param("query") PurchaseOrderQueryDto query,
                                @Param("conditions") String conditions);

    /**
     * 列表查询
     *
     * @param query
     * @param permsSql
     * @param conditions
     * @param orderBySql
     * @return
     */
    List<AsPurchaseApply> list(@Param("query") PurchaseOrderQueryDto query,
                               @Param("conditions") String conditions,
                               @Param("orderBySql") String orderBySql);

    /**
     * 采购申请单选择列表分页查询
     *
     * @param page
     * @param query
     * @param permsSql
     * @return
     */
    IPage<PurchaseApplyDetailSelectDto> pageSelect(IPage<Object> page,
                                             @Param("query") PurchaseOrderDetailQueryDto query,
                                             @Param("conditions") String conditions);

    @Select("select * from as_purchase_apply where id = #{id}")
    AsPurchaseApply tempGetById(Long id);

}
