package com.niimbot.asset.purchase.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.purchase.model.AsPurchaseOrder;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkStoreAmountDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 采购单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@EnableDataPerm(excludeMethodName = {"listStoreAssetAmount", "listStoreMaterialAmount", "tempGetById", "page", "list"})
public interface AsPurchaseOrderMapper extends BaseMapper<AsPurchaseOrder> {

    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @param permsSql
     * @return
     */
    IPage<AsPurchaseOrder> page(IPage<Object> page,
                                @Param("query") PurchaseOrderQueryDto query,
                                @Param("conditions") String conditions,
                                @Param("companyId") Long companyId,
                                @Param("permsSql") String permsSql);

    /**
     * 列表查询
     *
     * @param query
     * @param permsSql
     * @param conditions
     * @param orderBySql
     * @return
     */
    List<AsPurchaseOrder> list(@Param("query") PurchaseOrderQueryDto query,
                                @Param("conditions") String conditions,
                               @Param("orderBySql") String orderBySql,
                               @Param("companyId") Long companyId,
                               @Param("permsSql") String permsSql);

    List<PurchaseLinkStoreAmountDto> listStoreAssetAmount(@Param("query") PurchaseLinkAmountQueryDto query);

    List<PurchaseLinkStoreAmountDto> listStoreMaterialAmount(@Param("query") PurchaseLinkAmountQueryDto query);

    @Select("select * from as_purchase_order where id = #{id}")
    AsPurchaseOrder tempGetById(Long id);
}
