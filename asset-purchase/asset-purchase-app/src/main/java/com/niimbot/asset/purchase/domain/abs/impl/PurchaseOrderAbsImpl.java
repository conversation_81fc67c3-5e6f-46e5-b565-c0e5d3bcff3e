package com.niimbot.asset.purchase.domain.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.purchase.model.AsPurchaseOrder;
import com.niimbot.asset.purchase.service.AsPurchaseOrderService;
import com.niimbot.asset.system.abs.PurchaseOrderAbs;
import com.niimbot.purchase.PurchaseOrderDto;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/13 15:50
 */
@RestController
@RequestMapping("/client/abs/purchase/purchaseOrderAbs/")
@RequiredArgsConstructor
public class PurchaseOrderAbsImpl implements PurchaseOrderAbs {
    private final AsPurchaseOrderService purchaseOrderService;

    @Override
    public PurchaseOrderDto getDetailByOrderNo(String orderNo) {
        return purchaseOrderService.getDetailByOrderNo(orderNo);
    }

    @Override
    public Long getIdByOrderNo(String orderNo) {
        Optional<AsPurchaseOrder> oneOpt = purchaseOrderService.getOneOpt(Wrappers.lambdaQuery(AsPurchaseOrder.class)
                .select(AsPurchaseOrder::getId)
                .eq(AsPurchaseOrder::getOrderNo, orderNo));
        return oneOpt.map(AsPurchaseOrder::getId).orElse(null);
    }
}
