package com.niimbot.asset.purchase.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsProduct;
import com.niimbot.asset.means.service.AsProductService;
import com.niimbot.asset.means.service.StandardService;
import com.niimbot.asset.purchase.mapper.AsPurchaseOrderDetailMapper;
import com.niimbot.asset.purchase.model.AsPurchaseOrderDetail;
import com.niimbot.asset.purchase.service.AsPurchaseOrderDetailService;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.purchase.PurchaseDetailDataConvertDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 采购单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
@RequiredArgsConstructor
public class AsPurchaseOrderDetailServiceImpl extends ServiceImpl<AsPurchaseOrderDetailMapper, AsPurchaseOrderDetail> implements AsPurchaseOrderDetailService {
    private final AsProductService productService;
    private final StandardService standardService;
    private final AsFormService formService;

    @Override
    public IPage<PurchaseOrderDetailDto> page(PurchaseDetailPageQueryDto dto) {
        List<Long> productIds = new ArrayList<>();
        productIds.addAll(dto.getExcludeAssetIds());
        productIds.addAll(dto.getExcludeMaterialIds());
        dto.setExcludeAssetIds(productIds);
        return this.getBaseMapper().page(dto.buildIPage(), dto, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public List<AsPurchaseOrderDetail> listByOrderId(Long orderId) {
        return this.getBaseMapper().listByOrderId(orderId, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public List<AsPurchaseOrderDetail> listByOrderId(Collection<Long> orderIds) {
        return this.getBaseMapper().listByOrderIds(orderIds, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public List<JSONObject> dataConvert(PurchaseDetailDataConvertDto dto) {
        List<JSONObject> result = new ArrayList<>();
        List<AsPurchaseOrderDetail> details = this.getBaseMapper().list(dto, LoginUserThreadLocal.getCompanyId());
        List<AsProduct> products = productService.listByProductIds(LoginUserThreadLocal.getCompanyId(), dto.getProductIds());
        Map<Long, AsProduct> productMap = products.stream().collect(Collectors.toMap(AsProduct::getId, o -> o,
                (k1, k2) -> k1));
        FormVO assetTpl = formService.assetTpl();
        for (AsPurchaseOrderDetail detail : details) {
            JSONObject res = detail.extraTransformJSON();
            FormVO form = standardService.form(detail.getStandardId(), true);
            res.put("form", form);
            AsProduct product = productMap.get(detail.getProductId());
            res.put("product", product);
            if (form.getFormProps().containsKey("dataMapping")) {
                JSONObject productJson = JsonUtil.toJsonObject(product);
                JSONObject dataMapping = form.getFormProps().getJSONObject("dataMapping");
                JSONArray mappings = dataMapping.getJSONArray(Convert.toStr(assetTpl.getFormId()));
                if (CollUtil.isNotEmpty(mappings)) {
                    for (int i = 0; i < mappings.size(); i++) {
                        JSONObject mapping = mappings.getJSONObject(i);
                        res.put(mapping.getString("targetCode"), productJson.getOrDefault(mapping.getString("sourceCode"), ""));
                    }
                }
            } else {
                res.put("assetName", detail.getName());
            }
            result.add(res);
        }
        return result;
    }
}
