package com.niimbot.asset.purchase.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 采购单明细关联采购申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsPurchaseOrderLinkApply对象", description="采购单明细关联采购申请表")
@TableName("as_purchase_order_link_apply")
public class AsPurchaseOrderLinkApply implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "采购单id")
    private Long purchaseOrderId;

    @ApiModelProperty(value = "采购单明细id")
    private Long purchaseOrderDetailId;

    @ApiModelProperty(value = "采购申请id")
    private Long purchaseApplyId;

    @ApiModelProperty(value = "采购申请单号")
    private String purchaseApplyOrderNo;

    @ApiModelProperty(value = "物品类型: 1-资产, 2-耗材")
    private Integer productType;

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "标准品id")
    private Long standardId;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

}
