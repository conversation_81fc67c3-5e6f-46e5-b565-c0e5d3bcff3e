package com.niimbot.asset.purchase.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.activiti.WorkflowExecuteStepDto;
import com.niimbot.activiti.WorkflowStartDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.model.ActWorkflowBusiness;
import com.niimbot.asset.activiti.service.ActWorkflowBusinessService;
import com.niimbot.asset.activiti.service.ActWorkflowCallbackErrorService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.PurchaseConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.WorkflowUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.purchase.model.AsPurchaseOrder;
import com.niimbot.asset.purchase.model.AsPurchaseOrderDetail;
import com.niimbot.asset.purchase.model.AsPurchaseOrderType;
import com.niimbot.asset.purchase.service.AsPurchaseOrderService;
import com.niimbot.asset.purchase.service.AsPurchaseOrderTypeService;
import com.niimbot.asset.purchase.service.PurchaseCommonService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.purchase.PurchaseDetailDataConvertDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkApplyAmountDto;
import com.niimbot.purchase.PurchaseLinkStoreAmountDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;
import com.niimbot.purchase.PurchaseOrderDetailExtInfoQueryDto;
import com.niimbot.purchase.PurchaseOrderDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;
import com.niimbot.purchase.PurchaseOrderSubmitDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 采购单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Slf4j
@RestController
@RequestMapping("server/purchase/order")
@RequiredArgsConstructor
public class AsPurchaseOrderServiceController {
    private final AsPurchaseOrderService purchaseOrderService;
    private final PurchaseCommonService purchaseCommonService;
    private final AsPurchaseOrderTypeService purchaseOrderTypeService;
    private final ActWorkflowService workflowService;
    private final AsCusEmployeeService cusEmployeeService;
    private final ActWorkflowBusinessService workflowBusinessService;
    @Resource
    private ActWorkflowCallbackErrorService callbackErrorService;
    @Resource
    private RedisService redisService;

    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, @RequestBody PurchaseOrderDto orderDto) {
        loginUserDto.getCusUser().setOrgId(orderDto.getOrgId());
        this.check(orderDto);
        return this.purchaseOrderService.getWorkflowStepList(loginUserDto, orderDto);
    }

    @PostMapping
    public Boolean create(LoginUserDto loginUserDto, @RequestBody PurchaseOrderSubmitDto dto) {
        loginUserDto.getCusUser().setOrgId(dto.getOrderDto().getOrgId());
        PurchaseOrderDto orderDto = dto.getOrderDto();
        this.check(orderDto);
        purchaseCommonService.verify(orderDto);
        purchaseCommonService.translation(orderDto);
        // 查询单据类型
        AsPurchaseOrderType orderType = purchaseOrderTypeService.getOne(
                Wrappers.<AsPurchaseOrderType>lambdaQuery()
                        .eq(AsPurchaseOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsPurchaseOrderType::getType, AssetConstant.ORDER_TYPE_PURCHASE_ORDER));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), loginUserDto.getCusUser().getOrgId(), false);
        purchaseOrderService.create(orderDto, workflow != null);
        // 流程开启, 走流程审批
        if (workflow != null) {
            if (dto.getConditionId() == null) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_COMMON_ERROR, "流程执行条件Id不能为空");
            }
            if (dto.getExecuteStepDtoList().isEmpty()) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_EXECUTE_STEP_IS_EMPTY);
            }
            // 写入审批人
            List<WorkflowExecuteStepDto> executeStepList = Convert.toList(WorkflowExecuteStepDto.class, redisService.get(
                    RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId())));
            Map<Long, List<WorkflowExecuteStepDto.WorkflowApprover>> submitExecuteStepList = dto.getExecuteStepDtoList().stream()
                    .collect(Collectors.toMap(WorkflowExecuteStepDto::getStepId, WorkflowExecuteStepDto::getApproverList, (k1, k2) -> k1));
            for (WorkflowExecuteStepDto stepDto : executeStepList) {
                if (submitExecuteStepList.containsKey(stepDto.getStepId())) {
                    stepDto.setApproverList(submitExecuteStepList.get(stepDto.getStepId()));
                }
            }
            AsCusEmployee employee = cusEmployeeService.getById(loginUserDto.getCusUser().getId());
            String userName = employee == null ? loginUserDto.getCusUser().getAccount() : employee.getEmpName();

            JSONObject orderData = orderDto.getOrderData();
            Map<Object, Object> map = redisService.hGetAll(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            if (map != null) {
                Map<String, Object> conditionMap = Convert.toMap(String.class, Object.class, map);
                orderData.putAll(conditionMap);
            }
            workflowService.startWorkflow(
                    new WorkflowStartDto()
                            .setUserDto(loginUserDto.getCusUser())
                            .setTitle(getTitle(userName))
                            .setActivitiKey(orderType.getActivitiKey())
                            .setWorkflowId(workflow.getId())
                            .setVersion(workflow.getVersion())
                            .setCallbackUrl(workflow.getCallbackUrl())
                            .setBusinessType(orderType.getType().shortValue())
                            .setBusinessId(orderDto.getId())
                            .setFormData(orderData)
                            .setExecuteStepDtoList(executeStepList));
            redisService.del(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            redisService.del(RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
        }
        return true;
    }

    /**
     * 采购流程回调接口
     *
     * @param callbackDto
     * @return
     */
    @PostMapping("/processCallback")
    @Transactional(rollbackFor = Exception.class)
    public Boolean processCallback(@RequestBody WorkflowCallbackDto callbackDto) {
        AsPurchaseOrder order = purchaseOrderService.getById(Long.parseLong(callbackDto.getBusinessId()));
        if (order == null) {
            return false;
        }
        callbackErrorService.remove(callbackDto);
        if (WorkflowUtil.finishWorkflowCallbackAllowed().contains(order.getApproveStatus())) {
            return true;
        }
        short status = Short.parseShort(callbackDto.getApproveStatus());
        if (!WorkflowUtil.finishWorkflowCallbackAllowed().contains(status)) {
            return false;
        }
        // threadLocal写入公司Id
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(new CusUserDto()
                .setId(order.getCreateBy())
                .setIsAdmin(true)
                .setCompanyId(order.getCompanyId()));
        LoginUserThreadLocal.set(userDto);
        try {
            Optional<ActWorkflowBusiness> workflowBusiness = Optional.ofNullable(workflowBusinessService.getById(callbackDto.getProcessInstanceId()));
            workflowBusiness.ifPresent(v -> v.setApproveStatus(status));
            order.setApproveStatus(status);
            workflowBusiness.ifPresent(workflowBusinessService::updateById);
            purchaseOrderService.updateById(order);

        } finally {
            LoginUserThreadLocal.remove();
        }
        return true;
    }

    private String getTitle(String userName) {
        return String.format("%s提交的采购订单", userName);
    }

    @GetMapping("/{id}")
    public PurchaseOrderDto getById(@PathVariable("id") Long id) {
        return purchaseOrderService.getDetailById(id);
    }

    @GetMapping("/getByNo/{orderNo}")
    public PurchaseOrderDto getByOrderNo(@PathVariable("orderNo") String orderNo) {
        return purchaseOrderService.getDetailByOrderNo(orderNo);
    }

    @PostMapping("/page")
    public IPage<AsPurchaseOrder> page(@RequestBody PurchaseOrderQueryDto query) {
        return purchaseOrderService.page(query);
    }

    @PostMapping("/list")
    public List<AsPurchaseOrder> list(@RequestBody PurchaseOrderQueryDto query) {
        return purchaseOrderService.list(query);
    }

    @PostMapping(value = "/listDetails")
    public List<AsPurchaseOrderDetail> listDetailsByOrderId(@RequestBody Collection<Long> ids) {
        return purchaseOrderService.getOrderDetailById(ids);
    }

    /**
     * 采购单明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "/pageDetail")
    public IPage<PurchaseOrderDetailDto> pageDetail(@RequestBody PurchaseDetailPageQueryDto dto) {
        return purchaseOrderService.pageDetail(dto);
    }

    /**
     * 采购单明细分页数据转换
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "/convert")
    public List<JSONObject> dataConvert(@RequestBody PurchaseDetailDataConvertDto dto) {
        return purchaseOrderService.dataConvert(dto);
    }

    @PostMapping(value = "/link/apply")
    public List<PurchaseLinkApplyAmountDto> listApplyAmount(@RequestBody PurchaseLinkAmountQueryDto dto) {
        return purchaseOrderService.listApplyAmount(dto);
    }

    @PostMapping(value = "/link/store")
    public List<PurchaseLinkStoreAmountDto> listStoreAmount(@RequestBody PurchaseLinkAmountQueryDto dto) {
        return purchaseOrderService.listStoreAmount(dto);
    }

    private void check(PurchaseOrderDto orderDto) {
        List<PurchaseOrderDetailDto> productDetail = orderDto.getProductDetail();
        if (PurchaseConstant.PURCHASE_MODE_LINK_APPLY.equals(orderDto.getOrderData().getString("mode"))) {
            for (PurchaseOrderDetailDto p : productDetail) {
                if (CollUtil.isEmpty(p.getLinkApply())) {
                    BusinessExceptionUtil.throwException("关联采购申请信息不能为空");
                }
            }
        }

        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalMoney = BigDecimal.ZERO;
        for (PurchaseOrderDetailDto detailDto : orderDto.getProductDetail()) {
            // 校验
            detailDto.checkQuantity();
            // 总数量
            totalQuantity = totalQuantity.add(detailDto.getQuantity());
            // 总价格
            totalMoney = totalMoney.add(detailDto.getMoney());
            // 计算单价
            BigDecimal price = detailDto.getMoney().divide(detailDto.getQuantity(), 4, RoundingMode.HALF_UP);
            if (BigDecimal.ZERO.compareTo(totalMoney) != 0 && BigDecimal.ZERO.compareTo(price) == 0) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION,
                        detailDto.getName() + "单价" + price + "不支持，请重新设置");
            } else if (price.compareTo(new BigDecimal("999999999999.9999")) > 0) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION,
                        detailDto.getName() + "单价" + price + "不支持，请重新设置");
            }
            detailDto.setPrice(price);
        }
        orderDto.setTotalMoney(totalMoney).setTotalQuantity(totalQuantity);
    }

    @PostMapping(value = "/listExtInfo")
    public List<PurchaseOrderDetailDto> listExtInfo(@RequestBody PurchaseOrderDetailExtInfoQueryDto queryDto) {
        return purchaseOrderService.listExtInfo(queryDto);
    }

}
