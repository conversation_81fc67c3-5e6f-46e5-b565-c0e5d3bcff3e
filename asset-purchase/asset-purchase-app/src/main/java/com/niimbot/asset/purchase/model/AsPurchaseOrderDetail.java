package com.niimbot.asset.purchase.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 采购单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "AsPurchaseOrderDetail对象", description = "采购单明细")
@TableName(value = "as_purchase_order_detail", autoResultMap = true)
public class AsPurchaseOrderDetail extends AbstractPurchaseProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "采购单id")
    private Long purchaseOrderId;

    @ApiModelProperty(value = "关联的采购申请单的ID")
    private Long purchaseApplyId;

    @ApiModelProperty(value = "关联的采购申请单详情的ID")
    private Long purchaseApplyDetailId;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "总价格")
    private BigDecimal money;

    // __________________额外信息___________________
    @ApiModelProperty(value = "已入库数量")
    @TableField(exist = false)
    private BigDecimal storedQuantity;

    @ApiModelProperty(value = "入库中数量")
    @TableField(exist = false)
    private BigDecimal storingQuantity;

    @ApiModelProperty(value = "待入库数量")
    @TableField(exist = false)
    private BigDecimal waitStoreQuantity;

    public JSONObject extraTransformJSON() {
        JSONObject resolve = new JSONObject();
        resolve.put("productId", getProductId());
        resolve.put("quantity", getQuantity());
        resolve.put("storedQuantity", storedQuantity);
        resolve.put("storingQuantity", storingQuantity);
        resolve.put("waitStoreQuantity", waitStoreQuantity);
        return resolve;
    }
}
