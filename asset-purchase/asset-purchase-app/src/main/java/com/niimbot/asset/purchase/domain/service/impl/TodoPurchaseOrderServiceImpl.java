package com.niimbot.asset.purchase.domain.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.purchase.model.AsPurchaseOrder;
import com.niimbot.asset.purchase.service.AsPurchaseOrderService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.TodoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/6 上午11:12
 */
@Slf4j
@Service
public class TodoPurchaseOrderServiceImpl implements TodoOrderService {

    //当前service支持单据类型
    private static final List<Integer> supportTypeList = ListUtil.of(AssetConstant.ORDER_TYPE_PURCHASE_ORDER);

    @Autowired
    private AsPurchaseOrderService purchaseOrderService;

    @Override
    public Boolean supportOrderType(Integer orderType) {
        return supportTypeList.contains(orderType);
    }

    @Override
    public JSONObject getOrderJson(Long businessId) {
        AsPurchaseOrder purchaseOrder = purchaseOrderService.getById(businessId);
        if (Objects.isNull(purchaseOrder)) {
            return null;
        }

        JSONObject result = JsonUtil.toJsonObject(purchaseOrder, o -> {
            JSONObject orderData = o.getJSONObject("orderData");
            o.fluentPutAll(orderData);
            o.fluentRemove("orderData");
        });
        return result;
    }

    @Override
    public AsTodo getOrderTodo(AsTodo todo) {
        AsPurchaseOrder purchaseOrder = purchaseOrderService.getById(todo.getBusinessId());
        if (Objects.isNull(purchaseOrder)) {
            return null;
        }

        //如果orderData为空，优先设置下
        if (Objects.isNull(purchaseOrder.getOrderData())) {
            purchaseOrder.setOrderData(new JSONObject());
        }

        purchaseOrder.getOrderData().put("summary", purchaseOrder.getSummary());
        AsTodo result = new AsTodo()
                .setSummary(purchaseOrder.getSummary())
                .setCreateBy(purchaseOrder.getCreateBy())
                .setOrderData(JSONObject.toJSONString(purchaseOrder.getOrderData()));
        return result;
    }
}
