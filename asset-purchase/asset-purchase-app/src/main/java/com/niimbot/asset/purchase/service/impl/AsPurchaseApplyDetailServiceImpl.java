package com.niimbot.asset.purchase.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.purchase.mapper.AsPurchaseApplyDetailMapper;
import com.niimbot.asset.purchase.model.AsPurchaseApplyDetail;
import com.niimbot.asset.purchase.service.AsPurchaseApplyDetailService;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购申请明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@Service
public class AsPurchaseApplyDetailServiceImpl extends ServiceImpl<AsPurchaseApplyDetailMapper, AsPurchaseApplyDetail> implements AsPurchaseApplyDetailService {
    @Override
    public List<AsPurchaseApplyDetail> listByApplyId(Long applyId) {
        return this.getBaseMapper().listByApplyId(applyId);
    }

    @Override
    public List<AsPurchaseApplyDetail> listByApplyId(Collection<Long> applyIds) {
        return this.getBaseMapper().listByApplyIdList(applyIds);
    }

    @Override
    public List<AsPurchaseApplyDetail> listByApplyIds(Integer productType, Long productId, Collection<Long> appIds) {
        if (CollUtil.isEmpty(appIds)) {
            return ListUtil.empty();
        }
        return this.getBaseMapper().listByApplyIds(productType, productId, appIds);
    }

    @Override
    public IPage<AsPurchaseApplyDetail> page(PurchaseDetailPageQueryDto dto) {
        return this.getBaseMapper().page(dto.buildIPage(), dto);
    }
}
