<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.purchase.mapper.AsPurchaseOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.purchase.model.AsPurchaseOrder">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="summary" property="summary" />
        <result column="order_no" property="orderNo" />
        <result column="approve_status" property="approveStatus" />
        <result column="store_status" property="storeStatus" />
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="total_quantity" property="totalQuantity" />
        <result column="total_money" property="totalMoney" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="queryCondition">
        <if test="query.excludeIds!=null and query.excludeIds.size() > 0">
            and a.id not in
            <foreach collection="query.excludeIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 勾选的单据id -->
        <if test="query.ids!=null and query.ids.size() > 0">
            and a.id in
            <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
    </sql>

    <select id="page" resultMap="BaseResultMap">
        select
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        a.store_status,
        a.wait_store_quantity
        from (
        select
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        IFNULL(a.total_quantity, 0) - ifnull(storing.quantity, 0) - ifnull(stored_.quantity, 0) as wait_store_quantity,
        case
        when a.approve_status in (1, 2) then 0
        when a.approve_status in (0, 3) and ifnull(storing.quantity, 0) = 0 and ifnull(stored_.quantity, 0) = 0 then 1
        when a.approve_status in (0, 3) and ifnull(storing.quantity, 0) > 0 and ifnull(stored_.quantity, 0) = 0 then 2
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(stored_.quantity, 0) > 0 then 3
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(stored_.quantity, 0) = 0 then 4 end as
        store_status
        from as_purchase_order a
        left join (
        select a.purchase_order_no, sum(a.quantity) as quantity from (
        select b.purchase_order_no, sum(a.quantity) as quantity from as_store_order_summary_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_store_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status = 1 and company_id = #{companyId}) b on b.id =
        a.store_order_id
        group by b.purchase_order_no
        union all
        select b.purchase_order_no, sum(a.rk_num) as quantity from as_material_rk_order_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_material_rk_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status = 1 and company_id = #{companyId}) b on b.id =
        a.order_id
        group by b.purchase_order_no
        ) a group by a.purchase_order_no
        ) storing on storing.purchase_order_no = a.order_no
        left join (
        select a.purchase_order_no, sum(a.quantity) as quantity from (
        select b.purchase_order_no, sum(a.quantity) as quantity from as_store_order_summary_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_store_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status in (0, 3) and company_id = #{companyId}) b on b.id =
        a.store_order_id
        group by b.purchase_order_no
        union all
        select b.purchase_order_no, sum(a.rk_num) as quantity from as_material_rk_order_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_material_rk_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status in (0, 3) and company_id = #{companyId}) b on b.id =
        a.order_id
        group by b.purchase_order_no
        ) a group by a.purchase_order_no
        ) stored_ on stored_.purchase_order_no = a.order_no
        <where> <!-- 权限条件 -->
            <if test="permsSql != null and permsSql != ''">
                and ${permsSql}
            </if>
            and a.id in (select purchase_order_id from `as_purchase_order_detail` b where a.company_id = b.company_id
            <if test="query.productType != null">
                AND b.product_type = #{query.productType}
            </if>
            <if test="query.kw != null and query.kw != ''">
                and (
                a.order_no like concat('%', #{query.kw}, '%')
                or a.summary like concat('%', #{query.kw}, '%')
                or b.code like concat('%', #{query.kw}, '%')
                )
            </if>
            )
        </where>
        ) a
        <where>
            <if test="query.hasWaitStore != null and query.hasWaitStore">
                and wait_store_quantity &gt; 0
            </if>
            <include refid="queryCondition"/>
        </where>
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        a.store_status
        from (
        select
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        case
        when a.approve_status in (1, 2) then 0
        when a.approve_status in (0, 3) and ifnull(storing.quantity, 0) = 0 and ifnull(stored_.quantity, 0) = 0 then 1
        when a.approve_status in (0, 3) and ifnull(storing.quantity, 0) > 0 and ifnull(stored_.quantity, 0) = 0 then 2
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(stored_.quantity, 0) > 0 then 3
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(stored_.quantity, 0) = 0 then 4 end as
        store_status
        from as_purchase_order a
        left join (
        select a.purchase_order_no, sum(a.quantity) as quantity from (
        select b.purchase_order_no, sum(a.quantity) as quantity from as_store_order_summary_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_store_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status = 1 and company_id = #{companyId}) b on b.id =
        a.store_order_id
        group by b.purchase_order_no
        union all
        select b.purchase_order_no, sum(a.rk_num) as quantity from as_material_rk_order_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_material_rk_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status = 1 and company_id = #{companyId}) b on b.id =
        a.order_id
        group by b.purchase_order_no
        ) a group by a.purchase_order_no
        ) storing on storing.purchase_order_no = a.order_no
        left join (
        select a.purchase_order_no, sum(a.quantity) as quantity from (
        select b.purchase_order_no, sum(a.quantity) as quantity from as_store_order_summary_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_store_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status in (0, 3) and company_id = #{companyId}) b on b.id =
        a.store_order_id
        group by b.purchase_order_no
        union all
        select b.purchase_order_no, sum(a.rk_num) as quantity from as_material_rk_order_detail a
        inner join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no from as_material_rk_order where
        order_data ->> '$.rkType' = '采购入库' and approve_status in (0, 3) and company_id = #{companyId}) b on b.id =
        a.order_id
        group by b.purchase_order_no
        ) a group by a.purchase_order_no
        ) stored_ on stored_.purchase_order_no = a.order_no
        <where> <!-- 权限条件 -->
            <if test="permsSql != null and permsSql != ''">
                and ${permsSql}
            </if>
            and a.id in (select purchase_order_id from `as_purchase_order_detail` b where a.company_id = b.company_id
            <if test="query.productType != null">
                AND b.product_type = #{query.productType}
            </if>
            <if test="query.kw != null and query.kw != ''">
                and (
                a.order_no like concat('%', #{query.kw}, '%')
                or a.summary like concat('%', #{query.kw}, '%')
                or b.code like concat('%', #{query.kw}, '%')
                )
            </if>
        </where>
        ) a
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="orderBySql != null and orderBySql != ''">
            ${orderBySql}
        </if>
    </select>

    <select id="listStoreAssetAmount" resultType="com.niimbot.purchase.PurchaseLinkStoreAmountDto">
        select
        b.order_no as storeOrderNo,
        b.store_date as storeDate,
        a.quantity
        from as_store_order_summary_detail a
        inner join (
        select id, order_no, order_data ->> '$.purchaseOrderNo' as purchase_order_no, order_data ->> '$.storeDate' as
        store_date from as_store_order
        where order_data ->> '$.rkType' = '采购入库'
        and approve_status in
        <foreach collection="query.approveStatus" item="status" index="index" open="(" close=")"
                 separator=",">
            #{status}
        </foreach>
        ) b on b.id = a.store_order_id
        left join as_purchase_order po on po.order_no = purchase_order_no
        where po.id = #{query.purchaseOrderId} and a.product_id = #{query.productId}
    </select>

    <select id="listStoreMaterialAmount" resultType="com.niimbot.purchase.PurchaseLinkStoreAmountDto">
        select
        b.order_no as storeOrderNo,
        b.store_date as storeDate,
        a.rk_num as quantity
        from as_material_rk_order_detail a
        inner join (
        select id, order_no, order_data ->> '$.purchaseOrderNo' as purchase_order_no, order_data ->> '$.storageTime' as
        store_date from as_material_rk_order
        where order_data ->> '$.rkType' = '采购入库'
        and approve_status in
        <foreach collection="query.approveStatus" item="status" index="index" open="(" close=")"
                 separator=",">
            #{status}
        </foreach>
        and approve_status in
        <foreach collection="query.approveStatus" item="status" index="index" open="(" close=")"
                 separator=",">
            #{status}
        </foreach>
        ) b on b.id = a.order_id
        left join as_purchase_order po on po.order_no = purchase_order_no
        where po.id = #{query.purchaseOrderId} and a.material_id = #{query.productId}
    </select>

</mapper>
