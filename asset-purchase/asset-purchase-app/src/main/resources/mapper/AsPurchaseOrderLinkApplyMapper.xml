<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.purchase.mapper.AsPurchaseOrderLinkApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.purchase.model.AsPurchaseOrderLinkApply">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="purchase_order_id" property="purchaseOrderId" />
        <result column="purchase_order_detail_id" property="purchaseOrderDetailId" />
        <result column="purchase_apply_id" property="purchaseApplyId" />
        <result column="purchase_apply_order_no" property="purchaseApplyOrderNo" />
        <result column="product_type" property="productType" />
        <result column="product_id" property="productId" />
        <result column="standard_id" property="standardId" />
        <result column="quantity" property="quantity" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, purchase_order_id, purchase_order_detail_id, purchase_apply_id, purchase_apply_order_no, product_type, product_id, standard_id, quantity
    </sql>

    <resultMap id="PurchaseLinkApplyAmountDtoMap" type="com.niimbot.purchase.PurchaseLinkApplyAmountDto">
        <result column="quantity" property="quantity" />
        <result column="purchase_apply_order_no" property="purchaseApplyNo" />
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    </resultMap>

    <select id="listApplyAmount" resultMap="PurchaseLinkApplyAmountDtoMap">
        select a.quantity, a.purchase_apply_order_no , b.order_data from as_purchase_order_link_apply a
        left join as_purchase_apply b on b.id = a.purchase_apply_id
        where
            a.purchase_order_id = #{query.purchaseOrderId}
            and a.product_type = #{query.productType}
            and a.product_id = #{query.productId}
    </select>

    <select id="listOrderAmount" resultType="com.niimbot.purchase.PurchaseLinkOrderAmountDto">
        select
        po.order_no as purchase_order_no,
        IF(po.order_data ->> '$.purchaseDate' = 'null', null, po.order_data ->> '$.purchaseDate') as purchase_date,
        a.quantity
        from as_purchase_order_link_apply a
        left join as_purchase_order po on po.id = a.purchase_order_id
        where
        a.purchase_apply_id = #{query.purchaseApplyId}
        and a.product_type = #{query.productType}
        and a.product_id = #{query.productId}
        and po.approve_status in
        <foreach collection="query.approveStatus" item="status" index="index" open="(" close=")"
                 separator=",">
              #{status}
          </foreach>
    </select>
</mapper>
