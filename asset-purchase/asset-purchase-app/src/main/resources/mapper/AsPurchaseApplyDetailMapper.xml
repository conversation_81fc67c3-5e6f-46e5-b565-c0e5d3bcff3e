<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.purchase.mapper.AsPurchaseApplyDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.purchase.model.AsPurchaseApplyDetail">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="purchase_apply_id" property="purchaseApplyId" />
        <result column="product_type" property="productType" />
        <result column="product_id" property="productId" />
        <result column="standard_id" property="standardId" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="brand" property="brand" />
        <result column="model" property="model" />
        <result column="images" property="images"
                typeHandler="com.niimbot.asset.means.handle.StringListTypeHandler" />
        <result column="quantity" property="quantity" />
        <result column="unit" property="unit" />
        <result column="price" property="price" />
        <result column="expected_arrival_date" property="expectedArrivalDate" />
        <result column="remark" property="remark" />
        <result column="purchased_quantity" property="purchasedQuantity" />
        <result column="purchasing_quantity" property="purchasingQuantity" />
        <result column="wait_purchase_quantity" property="waitPurchaseQuantity" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, purchase_apply_id, product_id, standard_id, name, brand, model, quantity, unit, price, expected_arrival_date, remark,
        purchased_quantity, purchasing_quantity, wait_purchase_quantity
    </sql>

    <sql id="BaseSql">
        select a.* from (
                          select
                              b.id,
                              b.company_id,
                              b.purchase_apply_id,
                              b.product_type,
                              b.product_id,
                              b.standard_id,
                              b.name,
                              b.code,
                              b.brand,
                              b.model,
                              b.images,
                              b.quantity,
                              b.unit,
                              b.price,
                              b.expected_arrival_date,
                              b.remark,
                              ifnull(c.purchased_quantity, 0) as purchased_quantity,
                              ifnull(d.purchasing_quantity, 0) as purchasing_quantity,
                              b.quantity - ifnull(c.purchased_quantity, 0) -  ifnull(d.purchasing_quantity, 0) as wait_purchase_quantity
                          from as_purchase_apply_detail b
                                   left join as_purchase_apply a on b.purchase_apply_id = a.id
                                   left join (
                              select a.purchase_apply_id, a.product_type, a.product_id, sum(a.quantity) as purchased_quantity from as_purchase_order_link_apply a
                              where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status in (0, 3))
                              group by purchase_apply_id, product_type, product_id
                          ) c on c.purchase_apply_id = a.id and c.product_id = b.product_id and c.product_type = b.product_type
                                   left join (
                              select a.purchase_apply_id, a.product_type, a.product_id, sum(a.quantity) as purchasing_quantity from as_purchase_order_link_apply a
                              where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status = 1)
                              group by purchase_apply_id, product_type, product_id
                          ) d on d.purchase_apply_id = a.id and d.product_id = b.product_id and d.product_type = b.product_type
        ) a
    </sql>

    <select id="listByApplyId" resultMap="BaseResultMap">
        <include refid="BaseSql" />
        where a.purchase_apply_id = #{applyId}
    </select>

    <select id="listByApplyIdList" resultMap="BaseResultMap">
        <include refid="BaseSql" />
        where a.purchase_apply_id in
        <foreach collection="applyIds" item="applyId" index="index" open="(" close=")"
                 separator=",">
            #{applyId}
        </foreach>
    </select>

    <select id="listByApplyIds" resultMap="BaseResultMap">
        <include refid="BaseSql" />
        where 
            a.product_type = #{productType}
            and
            a.product_id = #{productId}
            and
            a.purchase_apply_id in
            <foreach collection="applyIds" item="applyId" index="index" open="(" close=")"
                separator=",">
                #{applyId}
            </foreach>
    </select>

    <select id="page" resultMap="BaseResultMap">
        <include refid="BaseSql" />
        where a.purchase_apply_id = #{query.orderId}
    </select>

</mapper>
