<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.purchase.mapper.AsSupplierContactMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.purchase.model.AsSupplierContact">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="supplier_id" property="supplierId" />
        <result column="name" property="name" />
        <result column="mobile" property="mobile" />
        <result column="email" property="email" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, supplier_id, name, mobile, email
    </sql>

    <select id="getSupplierContactBySupplierId" resultType="com.niimbot.purchase.SupplierContactDto">
        select <include refid="Base_Column_List" />
        from as_supplier_contact
        where supplier_id = #{supplierId}
    </select>

</mapper>
