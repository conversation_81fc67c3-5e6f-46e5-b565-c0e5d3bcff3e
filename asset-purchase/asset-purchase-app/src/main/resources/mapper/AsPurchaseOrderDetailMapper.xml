<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.purchase.mapper.AsPurchaseOrderDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.purchase.model.AsPurchaseOrderDetail">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="purchase_order_id" property="purchaseOrderId" />
        <result column="purchase_apply_id" property="purchaseApplyId" />
        <result column="purchase_apply_detail_id" property="purchaseApplyDetailId" />
        <result column="product_type" property="productType" />
        <result column="product_id" property="productId" />
        <result column="standard_id" property="standardId" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="brand" property="brand" />
        <result column="model" property="model" />
        <result column="images" property="images"
                typeHandler="com.niimbot.asset.means.handle.StringListTypeHandler" />
        <result column="quantity" property="quantity" />
        <result column="unit" property="unit" />
        <result column="price" property="price" />
        <result column="money" property="money" />
        <result column="stored_quantity" property="storedQuantity" />
        <result column="storing_quantity" property="storingQuantity" />
        <result column="wait_store_quantity" property="waitStoreQuantity" />
    </resultMap>

    <resultMap id="PurchaseOrderDetailDto" type="com.niimbot.purchase.PurchaseOrderDetailDto">
        <id column="id" property="id" />
        <result column="purchase_order_id" property="purchaseOrderId" />
        <result column="purchase_apply_id" property="purchaseApplyId" />
        <result column="purchase_apply_detail_id" property="purchaseApplyDetailId" />
        <result column="product_type" property="productType" />
        <result column="product_id" property="productId" />
        <result column="standard_id" property="standardId" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="brand" property="brand" />
        <result column="model" property="model" />
        <result column="images" property="images"
                typeHandler="com.niimbot.asset.means.handle.StringListTypeHandler" />
        <result column="quantity" property="quantity" />
        <result column="unit" property="unit" />
        <result column="price" property="price" />
        <result column="money" property="money" />
        <result column="stored_quantity" property="storedQuantity" />
        <result column="storing_quantity" property="storingQuantity" />
        <result column="wait_store_quantity" property="waitStoreQuantity" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <select id="page" resultMap="PurchaseOrderDetailDto">
        select a.* from (
        select
        b.id,
        b.company_id,
        b.purchase_order_id,
        b.product_type,
        b.product_id,
        b.standard_id,
        b.name,
        b.code,
        b.brand,
        b.model,
        b.images,
        b.quantity,
        b.unit,
        b.price,
        b.money,
        b.purchase_apply_id,
        b.purchase_apply_detail_id,
        d.is_delete,
        ifnull(c.stored_quantity, 0) as stored_quantity,
        ifnull(c.storing_quantity, 0) as storing_quantity,
        b.quantity - ifnull(c.stored_quantity, 0) -  ifnull(c.storing_quantity, 0) as wait_store_quantity
        from as_purchase_order_detail b
        join as_purchase_order a on b.purchase_order_id = a.id
        left join (select id, is_delete from as_product ast where (ast.company_id = #{companyId} or ast.company_id = 0)
        union all
        select id, is_delete from as_material mt where mt.company_id = #{companyId}) d on b.product_id = d.id
        left join (
            select b.purchase_order_no, '1' as product_type, a.product_id, sum(IF(b.approve_status in (0, 3), a.quantity, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.quantity, 0)) as storing_quantity  from as_store_order_summary_detail a
            join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_store_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3)) b on b.id = a.store_order_id and b.company_id = #{companyId}
            group by b.purchase_order_no, product_type, a.product_id
            union all
            select b.purchase_order_no, '2' as product_type, a.material_id as product_id, sum(IF(b.approve_status in (0, 3), a.rk_num, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.rk_num, 0)) as storing_quantity  from as_material_rk_order_detail a
            join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_material_rk_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3) ) b on b.id = a.order_id and b.company_id = #{companyId}
            group by b.purchase_order_no, product_type, product_id
        ) c on c.purchase_order_no = a.order_no and c.product_id = b.product_id and c.product_type = b.product_type
        ) a
        where a.purchase_order_id = #{query.orderId}
        and a.company_id = #{companyId}
        <if test="query.waitQuantityGT0">
            and a.wait_store_quantity <![CDATA[ > ]]> 0
        </if>
        <if test="query.productType!=null">
            and a.product_type = #{query.productType}
        </if>
        <!-- 关键字（编码/名称）-->
        <if test="query.kw!=null and query.kw!=''">
            and (
            (a.name like concat('%',#{query.kw},'%'))
            or
            (a.code like concat('%',#{query.kw},'%'))
            )
        </if>
        <if test="query.excludeIds!=null and query.excludeIds.size() > 0">
            and a.id not in
            <foreach collection="query.excludeIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test="query.excludeAssetIds!=null and query.excludeAssetIds.size() > 0">
            and a.product_id not in
            <foreach collection="query.excludeAssetIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test="query.includeIds!=null and query.includeIds.size() > 0">
            and a.id in
            <foreach collection="query.excludeIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="list" resultMap="BaseResultMap">
        select a.* from (
        select
        b.id,
        b.company_id,
        b.purchase_order_id,
        b.product_type,
        b.product_id,
        b.standard_id,
        b.name,
        b.code,
        b.brand,
        b.model,
        b.images,
        b.quantity,
        b.unit,
        b.price,
        b.money,
        b.purchase_apply_id,
        b.purchase_apply_detail_id,
        ifnull(c.stored_quantity, 0) as stored_quantity,
        ifnull(c.storing_quantity, 0) as storing_quantity,
        b.quantity - ifnull(c.stored_quantity, 0) -  ifnull(c.storing_quantity, 0) as wait_store_quantity
        from as_purchase_order_detail b
        join as_purchase_order a on b.purchase_order_id = a.id
        left join (
        select b.purchase_order_no, '1' as product_type, a.product_id, sum(IF(b.approve_status in (0, 3), a.quantity, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.quantity, 0)) as storing_quantity  from as_store_order_summary_detail a
        join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_store_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3)) b on b.id = a.store_order_id and b.company_id = #{companyId}
        group by b.purchase_order_no, product_type, a.product_id
        union all
        select b.purchase_order_no, '2' as product_type, a.material_id as product_id, sum(IF(b.approve_status in (0, 3), a.rk_num, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.rk_num, 0)) as storing_quantity  from as_material_rk_order_detail a
        join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_material_rk_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3) ) b on b.id = a.order_id and b.company_id = #{companyId}
        group by b.purchase_order_no, product_type, product_id
        ) c on c.purchase_order_no = a.order_no and c.product_id = b.product_id and c.product_type = b.product_type
        ) a
        where a.product_type = 1 and a.purchase_order_id = #{query.orderId}
        and a.company_id = #{companyId}
        <if test="query.productIds!=null and query.productIds.size() > 0">
            and a.product_id in
            <foreach collection="query.productIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listByOrderId" resultMap="BaseResultMap">
        select
            b.id,
            b.company_id,
            b.purchase_order_id,
            b.product_type,
            b.product_id,
            b.standard_id,
            b.name,
            b.code,
            b.brand,
            b.model,
            b.images,
            b.quantity,
            b.unit,
            b.price,
            b.money,
            b.purchase_apply_id,
            b.purchase_apply_detail_id,
            ifnull(c.stored_quantity, 0) as stored_quantity,
            ifnull(c.storing_quantity, 0) as storing_quantity,
            b.quantity - ifnull(c.stored_quantity, 0) -  ifnull(c.storing_quantity, 0) as wait_store_quantity
        from as_purchase_order_detail b
                 join as_purchase_order a on b.purchase_order_id = a.id
                 left join (
            select b.purchase_order_no, '1' as product_type, a.product_id, sum(IF(b.approve_status in (0, 3), a.quantity, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.quantity, 0)) as storing_quantity  from as_store_order_summary_detail a
                                                                                                                                                                                                                             join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_store_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3)) b on b.id = a.store_order_id and b.company_id = #{companyId}
            group by b.purchase_order_no, product_type, a.product_id
            union all
            select b.purchase_order_no, '2' as product_type, a.material_id as product_id, sum(IF(b.approve_status in (0, 3), a.rk_num, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.rk_num, 0)) as storing_quantity  from as_material_rk_order_detail a
                                                                                                                                                                                                                                        join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_material_rk_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3) ) b on b.id = a.order_id and b.company_id = #{companyId}
            group by b.purchase_order_no, product_type, product_id
        ) c on c.purchase_order_no = a.order_no and c.product_id = b.product_id and c.product_type = b.product_type
        where b.purchase_order_id = #{orderId}
    </select>

    <select id="listByOrderIds" resultMap="BaseResultMap">
        select
            b.id,
            b.company_id,
            b.purchase_order_id,
            b.product_type,
            b.product_id,
            b.standard_id,
            b.name,
            b.code,
            b.brand,
            b.model,
            b.images,
            b.quantity,
            b.unit,
            b.price,
            b.money,
            b.purchase_apply_id,
            b.purchase_apply_detail_id,
            ifnull(c.stored_quantity, 0) as stored_quantity,
            ifnull(c.storing_quantity, 0) as storing_quantity,
            b.quantity - ifnull(c.stored_quantity, 0) -  ifnull(c.storing_quantity, 0) as wait_store_quantity
        from as_purchase_order_detail b
                 left join as_purchase_order a on b.purchase_order_id = a.id
        left join (
        select b.purchase_order_no, '1' as product_type, a.product_id, sum(IF(b.approve_status in (0, 3), a.quantity, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.quantity, 0)) as storing_quantity  from as_store_order_summary_detail a
        join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_store_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3)) b on b.id = a.store_order_id and b.company_id = #{companyId}
        group by b.purchase_order_no, product_type, a.product_id
        union all
        select b.purchase_order_no, '2' as product_type, a.material_id as product_id, sum(IF(b.approve_status in (0, 3), a.rk_num, 0)) as stored_quantity, sum(IF(b.approve_status = 1, a.rk_num, 0)) as storing_quantity  from as_material_rk_order_detail a
        join (select id, order_data ->> '$.purchaseOrderNo' as purchase_order_no, approve_status, company_id from as_material_rk_order where order_data ->> '$.rkType' = '采购入库' and approve_status in (0,1,3) ) b on b.id = a.order_id and b.company_id = #{companyId}
        group by b.purchase_order_no, product_type, product_id
        ) c on c.purchase_order_no = a.order_no and c.product_id = b.product_id and c.product_type = b.product_type
        where b.purchase_order_id in
        <foreach collection="orderIds" item="orderId" index="index" open="(" close=")"
                 separator=",">
            #{orderId}
        </foreach>
    </select>

</mapper>
