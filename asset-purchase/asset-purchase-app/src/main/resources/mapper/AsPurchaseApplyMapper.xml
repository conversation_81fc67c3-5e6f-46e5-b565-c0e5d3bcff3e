<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.purchase.mapper.AsPurchaseApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.purchase.model.AsPurchaseApply">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="summary" property="summary" />
        <result column="order_no" property="orderNo" />
        <result column="approve_status" property="approveStatus" />
        <result column="purchase_status" property="purchaseStatus" />
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="total_quantity" property="totalQuantity" />
        <result column="total_money" property="totalMoney" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="page" resultMap="BaseResultMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        a.purchase_status
        from (
        select
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        case
        when a.approve_status in (1, 2, 4) then 0
        when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) = 0 and ifnull(purchased.quantity, 0) = 0 then 1
        when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) > 0 and ifnull(purchased.quantity, 0) = 0 then 2
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) > 0 then 3
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) = 0 then 4 end as purchase_status
        from as_purchase_apply a
        left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status = 1 ) group by a.purchase_apply_id) purchasing
        on purchasing.purchase_apply_id = a.id
        left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status in (0, 3) ) group by a.purchase_apply_id) purchased
        on purchased.purchase_apply_id = a.id
        ) a
        <if test="query.kw != null and query.kw != ''">
            join as_purchase_apply_detail b on b.purchase_apply_id = a.id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        a.purchase_status
        from (
        select
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        case
        when a.approve_status in (1, 2) then 0
        when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) = 0 and ifnull(purchased.quantity, 0) = 0 then 1
        when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) > 0 and ifnull(purchased.quantity, 0) = 0 then 2
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) > 0 then 3
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) = 0 then 4 end as purchase_status
        from as_purchase_apply a
        left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status = 1 ) group by a.purchase_apply_id) purchasing
        on purchasing.purchase_apply_id = a.id
        left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status in (0, 3) ) group by a.purchase_apply_id) purchased
        on purchased.purchase_apply_id = a.id
        ) a
        <if test="query.kw != null and query.kw != ''">
            join as_purchase_apply_detail b on b.purchase_apply_id = a.id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="orderBySql != null and orderBySql != ''">
            ${orderBySql}
        </if>
    </select>

    <resultMap id="PurchaseApplyDetailSelectDtoMap" type="com.niimbot.purchase.PurchaseApplyDetailSelectDto">
        <result column="purchase_apply_id" property="purchaseApplyId" />
        <result column="summary" property="summary" />
        <result column="order_no" property="orderNo" />
        <result column="approve_status" property="approveStatus" />
        <result column="purchase_status" property="purchaseStatus" />
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />

        <result column="purchase_apply_detail_id" property="purchaseApplyDetailId" />
        <result column="product_type" property="productType" />
        <result column="product_id" property="productId" />
        <result column="standard_id" property="standardId" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="brand" property="brand" />
        <result column="model" property="model" />
        <result column="images" property="images"
                typeHandler="com.niimbot.asset.means.handle.StringListTypeHandler" />
        <result column="quantity" property="quantity" />
        <result column="unit" property="unit" />
        <result column="price" property="price" />
        <result column="expected_arrival_date" property="expectedArrivalDate" />
        <result column="remark" property="remark" />

        <result column="purchased_quantity" property="purchasedQuantity" />
        <result column="purchasing_quantity" property="purchasingQuantity" />
        <result column="wait_purchase_quantity" property="waitPurchaseQuantity" />
    </resultMap>

    <select id="pageSelect" resultMap="PurchaseApplyDetailSelectDtoMap">
        select
        a.id as purchase_apply_id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        a.purchase_status,
        b.id as purchase_apply_detail_id,
        b.product_type,
        b.product_id,
        b.standard_id,
        b.name,
        b.code,
        b.brand,
        b.model,
        b.images,
        b.quantity,
        b.unit,
        b.price,
        b.expected_arrival_date,
        b.remark,
        ifnull(c.purchased_quantity, 0) as purchased_quantity,
        ifnull(d.purchasing_quantity, 0) as purchasing_quantity,
        b.quantity - ifnull(c.purchased_quantity, 0) -  ifnull(d.purchasing_quantity, 0) as wait_purchase_quantity
        from (
        select
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time,
        case
        when a.approve_status in (1, 2) then 0
        when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) = 0 and ifnull(purchased.quantity, 0) = 0 then 1
        when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) > 0 and ifnull(purchased.quantity, 0) = 0 then 2
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) > 0 then 3
        when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) = 0 then 4 end as purchase_status
        from as_purchase_apply a
        left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status = 1 ) group by a.purchase_apply_id) purchasing
        on purchasing.purchase_apply_id = a.id
        left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status in (0, 3) ) group by a.purchase_apply_id) purchased
        on purchased.purchase_apply_id = a.id
        ) a
        join as_purchase_apply_detail b on b.purchase_apply_id = a.id
        and b.product_id in (select id from as_product ast where (ast.company_id = a.company_id or ast.company_id = 0) and ast.is_delete = 0
        union all
        select id from as_material mt where mt.company_id = a.company_id and mt.is_delete = 0)
        left join (
        select a.purchase_apply_id, a.product_type, a.product_id, sum(a.quantity) as purchased_quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status in (0, 3))
        group by purchase_apply_id, product_type, product_id
        ) c on c.purchase_apply_id = a.id and c.product_id = b.product_id and c.product_type = b.product_type
        left join (
        select a.purchase_apply_id, a.product_type, a.product_id, sum(a.quantity) as purchasing_quantity from as_purchase_order_link_apply a
        where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status = 1)
        group by purchase_apply_id, product_type, product_id
        ) d on d.purchase_apply_id = a.id and d.product_id = b.product_id and d.product_type = b.product_type
        <where>
            a.approve_status in (0, 3) and
            (b.quantity - ifnull(c.purchased_quantity, 0) -  ifnull(d.purchasing_quantity, 0)) > 0
            <if test="query.excludeDetailIds!=null and query.excludeDetailIds.size() > 0">
                and b.id not in
                <foreach collection="query.excludeDetailIds" item="detailId" index="index" open="(" close=")"
                         separator=",">
                    #{detailId}
                </foreach>
            </if>
            <if test="query.includeDetailIds!=null and query.includeDetailIds.size() > 0">
                and b.id in
                <foreach collection="query.includeDetailIds" item="detailId" index="index" open="(" close=")"
                         separator=",">
                    #{detailId}
                </foreach>
            </if>
            <include refid="queryCondition"/>
        </where>
        order by a.create_time desc
    </select>

    <select id="getWithStatusById" resultMap="BaseResultMap">
        select
            a.id,
            a.company_id,
            a.approve_status,
            a.summary,
            a.order_no,
            a.order_data,
            a.total_quantity,
            a.total_money,
            a.create_by,
            a.create_time,
            case
                when a.approve_status in (1, 2) then 0
                when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) = 0 and ifnull(purchased.quantity, 0) = 0 then 1
                when a.approve_status in (0, 3) and ifnull(purchasing.quantity, 0) > 0 and ifnull(purchased.quantity, 0) = 0 then 2
                when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) > 0 then 3
                when a.approve_status in (0, 3) and a.total_quantity - ifnull(purchased.quantity, 0) = 0 then 4 end as purchase_status
        from as_purchase_apply a
         left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
                    where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status = 1 ) group by a.purchase_apply_id) purchasing
                   on purchasing.purchase_apply_id = a.id
         left join (select a.purchase_apply_id, sum(a.quantity) as quantity from as_purchase_order_link_apply a
                    where exists (select 1 from as_purchase_order b where b.id = a.purchase_order_id and b.approve_status in (0, 3) ) group by a.purchase_apply_id) purchased
                   on purchased.purchase_apply_id = a.id
        where a.id = #{id}
    </select>

    <sql id="queryCondition">
        <if test="query.kw != null and query.kw != ''">
            and (
            a.order_no like concat('%', #{query.kw}, '%')
            or a.summary like concat('%', #{query.kw}, '%')
            or b.code like concat('%', #{query.kw}, '%')
            )
        </if>
        <if test="query.excludeIds!=null and query.excludeIds.size() > 0">
            and a.id not in
            <foreach collection="query.excludeIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 勾选的单据id -->
        <if test="query.ids!=null and query.ids.size() > 0">
            and a.id in
            <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
    </sql>
</mapper>
