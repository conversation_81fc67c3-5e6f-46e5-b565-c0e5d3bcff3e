<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.purchase.mapper.AsSupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.purchase.model.AsSupplier">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="name" property="name" />
        <result column="company_name" property="companyName" />
        <result column="official_website" property="officialWebsite" />
        <result column="invoice_info" property="invoiceInfo" />
        <result column="is_delete" property="isDelete" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseDtoResultMap" type="com.niimbot.purchase.SupplierDto">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="company_name" property="companyName" />
        <result column="official_website" property="officialWebsite" />
        <result column="invoice_info" property="invoiceInfo" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <!--
        <collection property="contacts" ofType="com.niimbot.purchase.SupplierContactDto">
            <id column="cid" property="id" />
            <result column="supplier_id" property="supplierId" />
            <result column="cname" property="name" />
            <result column="mobile" property="mobile" />
            <result column="email" property="email" />
        </collection> -->
        <collection property="contacts" ofType="com.niimbot.purchase.SupplierContactDto"
                    select="com.niimbot.asset.purchase.mapper.AsSupplierContactMapper.getSupplierContactBySupplierId"
                    column="{supplierId=id}"
                    javaType="java.util.ArrayList">
        </collection>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BasePageDtoResultMap" type="com.niimbot.purchase.SupplierDto">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="company_name" property="companyName" />
        <result column="official_website" property="officialWebsite" />
        <result column="invoice_info" property="invoiceInfo" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <!--
        <collection property="contacts" ofType="com.niimbot.purchase.SupplierContactDto"
                    select="com.niimbot.asset.purchase.mapper.AsSupplierContactMapper.getSupplierContactBySupplierId"
                    column="{supplierId=id}"
                    javaType="java.util.ArrayList">
        </collection> -->
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, name, company_name, officia_website, invoice_info, is_delete, create_by, create_time, update_by, update_time, remark
    </sql>

    <select id="pageQueryDto" resultMap="BasePageDtoResultMap">
        select
            a.id,
            a.name,
            a.company_name,
            a.official_website,
            a.invoice_info,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.remark
        from as_supplier a
        <where>
            a.is_delete = 0
            <if test="query.keywords != null and query.keywords != ''">
                and (a.name LIKE CONCAT('%', #{query.keywords}, '%')
                or a.company_name LIKE CONCAT('%', #{query.keywords}, '%')
                or
                a.id in (
                select b.supplier_id from as_supplier_contact b
                where b.name LIKE CONCAT('%', #{query.keywords}, '%')
                or b.mobile LIKE CONCAT('%', #{query.keywords}, '%')
                or b.email LIKE CONCAT('%', #{query.keywords}, '%')
                ))
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="listQueryDto" resultMap="BaseDtoResultMap">
        select
            a.id,
            a.name,
            a.company_name,
            a.official_website,
            a.invoice_info,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.remark
            <!--
            b.id as cid,
            b.supplier_id,
            b.name as cname,
            b.mobile,
            b.email -->
        from as_supplier a
        <!-- left join as_supplier_contact b on b.supplier_id = a.id-->
        where a.is_delete = 0
        <if test="name != null and name != ''">
            and a.name LIKE CONCAT('%', #{name}, '%')
        </if>
        order by a.create_time desc
    </select>

</mapper>
