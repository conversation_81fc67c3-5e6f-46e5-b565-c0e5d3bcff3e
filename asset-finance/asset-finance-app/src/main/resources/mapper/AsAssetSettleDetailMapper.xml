<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetSettleDetailMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetSettleDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="settle_org_id" jdbcType="BIGINT" property="settleOrgId" />
    <result column="asset_id" jdbcType="BIGINT" property="assetId" />
    <result column="asset_code" jdbcType="VARCHAR" property="assetCode" />
    <result column="asset_category" jdbcType="BIGINT" property="assetCategory" />
    <result column="asset_finance_code" jdbcType="VARCHAR" property="assetFinanceCode" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="previous_remaining_amount" jdbcType="DECIMAL" property="previousRemainingAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="settle_month" jdbcType="INTEGER" property="settleMonth" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <resultMap id="SummaryResultMap" type="com.niimbot.asset.finance.model.AsAssetSettleSummary">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="settle_org_id" jdbcType="BIGINT" property="settleOrgId" />
    <result column="asset_category" jdbcType="BIGINT" property="assetCategory" />
    <result column="asset_num" jdbcType="BIGINT" property="assetNum" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="previous_remaining_amount" jdbcType="DECIMAL" property="previousRemainingAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="settle_month" jdbcType="INTEGER" property="settleMonth" />
  </resultMap>

    <resultMap id="DetailResultMap" type="com.niimbot.finance.SettleDetailDto">
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="asset_id" jdbcType="BIGINT" property="assetId" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="asset_code" jdbcType="VARCHAR" property="assetCode" />
        <result column="asset_name" jdbcType="VARCHAR" property="assetName" />
        <result column="asset_category" jdbcType="BIGINT" property="assetCategory" />
        <result column="asset_finance_code" jdbcType="VARCHAR" property="bizCode" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="settle_org_id" jdbcType="BIGINT" property="settleOrgId" />
        <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
        <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
        <result column="previous_remaining_amount" jdbcType="DECIMAL" property="previousRemainingAmount" />
        <result column="settle_month" jdbcType="INTEGER" property="settleMonth" />
        <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
        <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
        <result column="pre_residual_rate" jdbcType="DECIMAL" property="preResidualRate" />
        <result column="first_settle_month" jdbcType="INTEGER" property="firstSettleMonth" />
        <result column="pre_settle_count" jdbcType="INTEGER" property="preSettleCount" />
        <result column="remaining_settle_count" jdbcType="INTEGER" property="remainingSettleCount" />
    </resultMap>


  <sql id="Base_Column_List">
    id, org_id, settle_org_id, asset_id, asset_code, asset_category, asset_finance_code,
    origin_price, accumulation_amount, settle_amount, previous_remaining_amount, remaining_amount, settle_month,
    is_delete, create_by, create_time
  </sql>

  <select id="selectCompanySummary" resultMap="SummaryResultMap">
    select max(org_id) as org_id, settle_org_id, asset_category, count(distinct asset_id) as asset_num,
           sum(origin_price) as origin_price, sum(settle_amount) as settle_amount, sum(accumulation_amount) as accumulation_amount,
           sum(previous_remaining_amount) as previous_remaining_amount, sum(remaining_amount) as remaining_amount,
           max(settle_month) as settle_month
    from as_asset_settle_detail
    where org_id = #{orgId} and settle_month = #{settleMonth} and is_delete = 0
    group by settle_org_id, asset_category
  </select>

  <select id="pageQuery" parameterType="com.niimbot.finance.SettleDetailQueryDto" resultMap="DetailResultMap">
    select *
    from (
           select a.id as asset_id, a.status, a.asset_code, a.asset_name, a.asset_category, a.create_time, c.id, c.asset_finance_code, c.org_id, c.settle_org_id, c.origin_price, c.accumulation_amount,
                  c.previous_remaining_amount, c.settle_month, c.settle_amount, c.remaining_amount, b.pre_residual_rate, b.first_settle_month,
                  b.pre_settle_count, b.settle_count, (b.pre_settle_count - b.settle_count) as remaining_settle_count
           from as_asset_settle_detail as c left join as_asset_finance_info as b on c.asset_finance_code = b.biz_code
                                            left join as_asset as a on c.asset_id = a.id
           <where>
               c.is_delete = 0 and c.org_id = #{param.orgId} and c.settle_month = #{param.settleMonthInt}
               <!-- 导出选中id -->
               <if test="param.exportIds != null and param.exportIds.size > 0">
                   and c.id in
                   <foreach collection="param.exportIds" item="item" index="index" open="(" close=")"
                            separator=",">
                       #{item}
                   </foreach>
               </if>
               <if test="param.permissionCondition != null and param.permissionCondition != ''">
                   ${param.permissionCondition}
               </if>
           </where>
         ) as d
    <where>
        <if test="param.assetCode != null and param.assetCode != ''">
            and d.asset_code = #{param.assetCode}
        </if>
        <if test="param.assetName != null and param.assetName != ''">
            and d.asset_name = #{param.assetName}
        </if>
        <if test="param.assetCategory != null and param.assetCategory.size > 0">
            and d.asset_category in
            <foreach collection="param.assetCategory" item="assetCategory" index="index" open="(" separator="," close=")">
                #{assetCategory}
            </foreach>
        </if>
        <if test="param.settleOrgId != null and param.settleOrgId.size > 0">
            and d.settle_org_id in
            <foreach collection="param.settleOrgId" item="settleOrgId" index="index" open="(" separator="," close=")">
                #{settleOrgId}
            </foreach>
        </if>
        <if test="param.originPrice != null and param.originPrice.size == 2">
            <if test="param.originPrice[0] != null">
                and d.origin_price <![CDATA[ >= ]]> #{param.originPrice[0]}
            </if>
            <if test="param.originPrice[1] != null">
                and d.origin_price <![CDATA[ <= ]]> #{param.originPrice[1]}
            </if>
        </if>
        <if test="param.accumulationAmount != null and param.accumulationAmount.size == 2">
            <if test="param.accumulationAmount[0] != null">
                and d.accumulation_amount <![CDATA[ >= ]]> #{param.accumulationAmount[0]}
            </if>
            <if test="param.accumulationAmount[1] != null">
                and d.accumulation_amount <![CDATA[ <= ]]> #{param.accumulationAmount[1]}
            </if>
        </if>
        <if test="param.settleAmount != null and param.settleAmount.size == 2">
            <if test="param.settleAmount[0] != null">
                and d.settle_amount <![CDATA[ >= ]]> #{param.settleAmount[0]}
            </if>
            <if test="param.settleAmount[1] != null">
                and d.settle_amount <![CDATA[ <= ]]> #{param.settleAmount[1]}
            </if>
        </if>
        <if test="param.preResidualRate != null and param.preResidualRate.size == 2">
            <if test="param.preResidualRate[0] != null">
                and d.pre_residual_rate <![CDATA[ >= ]]> #{param.preResidualRate[0]}
            </if>
            <if test="param.preResidualRate[1] != null">
                and d.pre_residual_rate <![CDATA[ <= ]]> #{param.preResidualRate[1]}
            </if>
        </if>
        <if test="param.firstSettleMonth != null and param.firstSettleMonth.size == 2">
            <if test="param.firstSettleMonth[0] != null">
                and d.first_settle_month <![CDATA[ >= ]]> #{param.firstSettleMonth[0]}
            </if>
            <if test="param.firstSettleMonth[1] != null">
                and d.first_settle_month <![CDATA[ <= ]]> #{param.firstSettleMonth[1]}
            </if>
        </if>
        <if test="param.preSettleCount != null and param.preSettleCount.size == 2">
            <if test="param.preSettleCount[0] != null">
                and d.pre_settle_count <![CDATA[ >= ]]> #{param.preSettleCount[0]}
            </if>
            <if test="param.preSettleCount[1] != null">
                and d.pre_settle_count <![CDATA[ <= ]]> #{param.preSettleCount[1]}
            </if>
        </if>
        <if test="param.settleCount != null and param.settleCount.size == 2">
            <if test="param.settleCount[0] != null">
                and d.settle_count <![CDATA[ >= ]]> #{param.settleCount[0]}
            </if>
            <if test="param.settleCount[1] != null">
                and d.settle_count <![CDATA[ <= ]]> #{param.settleCount[1]}
            </if>
        </if>
        <if test="param.remainingSettleCount != null and param.remainingSettleCount.size == 2">
            <if test="param.remainingSettleCount[0] != null">
                and d.remaining_settle_count <![CDATA[ >= ]]> #{param.remainingSettleCount[0]}
            </if>
            <if test="param.remainingSettleCount[1] != null">
                and d.remaining_settle_count <![CDATA[ <= ]]> #{param.remainingSettleCount[1]}
            </if>
        </if>
        <if test="param.previousRemainingAmount != null and param.previousRemainingAmount.size == 2">
            <if test="param.previousRemainingAmount[0] != null">
                and d.previous_remaining_amount <![CDATA[ >= ]]> #{param.previousRemainingAmount[0]}
            </if>
            <if test="param.previousRemainingAmount[1] != null">
                and d.previous_remaining_amount <![CDATA[ <= ]]> #{param.previousRemainingAmount[1]}
            </if>
        </if>
        <if test="param.remainingAmount != null and param.remainingAmount.size == 2">
            <if test="param.remainingAmount[0] != null">
                and d.remaining_amount <![CDATA[ >= ]]> #{param.remainingAmount[0]}
            </if>
            <if test="param.remainingAmount[1] != null">
                and d.remaining_amount <![CDATA[ <= ]]> #{param.remainingAmount[1]}
            </if>
        </if>
    </where>
    order by d.${param.sidx} ${param.order}
  </select>
</mapper>