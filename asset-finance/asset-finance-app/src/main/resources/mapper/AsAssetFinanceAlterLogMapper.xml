<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetFinanceAlterLogMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetFinanceAlterLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="asset_bill_code" jdbcType="VARCHAR" property="assetBillCode" />
    <result column="detail" jdbcType="VARCHAR" property="detail" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <resultMap id="LogResultMap" type="com.niimbot.finance.AssetFinanceAlterLogDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="asset_bill_code" jdbcType="VARCHAR" property="assetFinanceCode" />
    <result column="detail" jdbcType="VARCHAR" property="detail" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, asset_bill_code, detail, create_by, create_time
  </sql>

  <select id="pageQuery" resultMap="LogResultMap">
    select
    <include refid="Base_Column_List" />
    from as_asset_finance_alter_log
    <where>
        <if test="page.assetFinanceCode != null and page.assetFinanceCode != ''">
            and asset_bill_code = #{page.assetFinanceCode}
        </if>
    </where>
    order by create_time desc
  </select>


</mapper>