<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetSettleTaskMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetSettleTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_parameters" jdbcType="VARCHAR" property="taskParameters" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="total_cost" jdbcType="BIGINT" property="totalCost" />
    <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
    <result column="handle_count" jdbcType="INTEGER" property="handleCount" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, org_name, task_type, task_name, task_parameters, `status`, remark,
    total_cost, execute_time, handle_count, is_delete, create_by, create_time, update_time
  </sql>

  <select id="selectWaitExecute" resultMap="BaseResultMap" parameterType="com.niimbot.finance.SettleTaskQueryDto">
    select
    <include refid="Base_Column_List" />
    from as_asset_settle_task
    where create_time <![CDATA[>=]]> #{param.startTime} and `status` = #{param.status} and is_delete = 0
    order by create_time asc
    limit 10
  </select>
</mapper>