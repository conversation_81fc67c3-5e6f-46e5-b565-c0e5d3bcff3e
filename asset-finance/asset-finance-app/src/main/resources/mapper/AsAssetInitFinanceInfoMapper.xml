<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetInitFinanceInfoMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetInitFinanceInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="company_id" jdbcType="BIGINT" property="orgId" />
    <result column="settle_org_id" jdbcType="BIGINT" property="settleOrgId" />
    <result column="asset_id" jdbcType="BIGINT" property="assetId" />
    <result column="strategy" jdbcType="INTEGER" property="strategy" />
    <result column="settle_type" jdbcType="INTEGER" property="settleType" />
    <result column="bill_date" jdbcType="INTEGER" property="billDate" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="tax" jdbcType="DECIMAL" property="tax" />
    <result column="pre_residual_rate" jdbcType="DECIMAL" property="preResidualRate" />
    <result column="pre_remaining_amount" jdbcType="DECIMAL" property="preRemainingAmount" />
    <result column="pre_settle_count" jdbcType="INTEGER" property="preSettleCount" />
    <result column="settle_count" jdbcType="INTEGER" property="settleCount" />
    <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_code, company_id, settle_org_id, asset_id, strategy, settle_type, bill_date, 
    origin_price, tax, pre_residual_rate, pre_remaining_amount, pre_settle_count, settle_count,
    accumulation_amount, remaining_amount, remark, bill_status, settle_status, handle_time, 
    is_delete, create_by, create_time, update_by, update_time
  </sql>
</mapper>