<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetBillInfoMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetBillInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="settle_month" jdbcType="INTEGER" property="settleMonth" />
    <result column="settle_date" jdbcType="INTEGER" property="settleDate" />
    <result column="bill_date" jdbcType="INTEGER" property="billDate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, settle_month, settle_date, bill_date, status, is_delete, create_by, create_time
  </sql>

  <select id="queryLastedByOrgId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from as_asset_bill_info
    where is_delete = 0 and org_id = #{orgId} and `status` = 3
    order by settle_month desc
    limit 1
  </select>

  <select id="selectMaxSettleMonth" resultType="java.lang.Integer">
    select max(settle_month)
    from as_asset_bill_info
    <where>
      is_delete = 0 and `status` in (2,3)
      <if test="orgIdList != null and orgIdList.size > 0">
        and org_id in
        <foreach collection="orgIdList" item="orgId" index="index" open="(" separator="," close=")">
            #{orgId}
        </foreach>
      </if>
    </where>
  </select>
</mapper>