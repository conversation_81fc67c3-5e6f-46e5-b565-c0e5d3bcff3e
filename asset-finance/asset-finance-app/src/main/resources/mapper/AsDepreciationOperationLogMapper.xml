<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsDepreciationOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsDepreciationOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="action_name" jdbcType="VARCHAR" property="actionName" />
    <result column="action_method" jdbcType="VARCHAR" property="actionMethod" />
    <result column="action_param" jdbcType="VARCHAR" property="actionParam" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_code, action_name, action_method, action_param, remark, create_by, create_time
  </sql>
</mapper>