<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetFinanceInfoMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetFinanceInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="settle_org_id" jdbcType="BIGINT" property="settleOrgId" />
    <result column="asset_id" jdbcType="BIGINT" property="assetId" />
    <result column="asset_code" jdbcType="VARCHAR" property="assetCode" />
    <result column="asset_category" jdbcType="BIGINT" property="assetCategory" />
    <result column="strategy" jdbcType="INTEGER" property="strategy" />
    <result column="settle_type" jdbcType="INTEGER" property="settleType" />
    <result column="bill_date" jdbcType="INTEGER" property="billDate" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="tax" jdbcType="DECIMAL" property="tax" />
    <result column="pre_residual_rate" jdbcType="DECIMAL" property="preResidualRate" />
    <result column="pre_remaining_amount" jdbcType="DECIMAL" property="preRemainingAmount" />
    <result column="pre_settle_count" jdbcType="INTEGER" property="preSettleCount" />
    <result column="settle_count" jdbcType="INTEGER" property="settleCount" />
    <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="current_settle_count" jdbcType="INTEGER" property="currentSettleCount" />
    <result column="current_settle_amount" jdbcType="DECIMAL" property="currentSettleAmount" />
    <result column="first_settle_month" jdbcType="INTEGER" property="firstSettleMonth" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="previous_settle_status" jdbcType="INTEGER" property="previousSettleStatus" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_code, org_id, settle_org_id, asset_id, asset_code, asset_category, strategy, settle_type, bill_date,
    origin_price, tax, pre_residual_rate, pre_remaining_amount, pre_settle_count, settle_count,previous_settle_status,
    accumulation_amount, remaining_amount, current_settle_count, current_settle_amount, first_settle_month, remark, bill_status, settle_status, handle_time,
    is_delete, create_by, create_time, update_by, update_time
  </sql>

  <resultMap id="AsAssetMap" type="com.niimbot.asset.means.model.AsAsset">
    <id column="id" property="id"/>
    <result column="status" property="status"/>
    <result column="asset_data" property="assetData"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="create_time" property="createTime"/>
    <result column="asset_code" property="assetCode"/>
  </resultMap>

  <resultMap id="AssetFinanceInfoMap" type="com.niimbot.finance.AssetFinanceInfoDto">
    <result column="id" property="assetId"/>
    <result column="status" property="status"/>
    <result column="asset_data" property="assetData"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="create_time" property="createTime"/>
    <result column="asset_code" property="assetCode"/>
    <result column="biz_code" property="bizCode" />
    <result column="bill_status" property="billStatus" />
    <result column="settle_status" property="settleStatus" />
  </resultMap>

  <resultMap id="AssetInitFinanceInfoMap" type="com.niimbot.finance.AssetInitFinanceInfoDto">
    <result column="id" property="id"/>
    <result column="asset_id" property="assetId"/>
    <result column="status" property="status"/>
    <result column="asset_data" property="assetData"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="asset_code" property="assetCode"/>
    <result column="org_id" property="orgId" />
    <result column="settle_org_id" property="settleOrgId" />
    <result column="strategy" property="strategy" />
    <result column="bill_date" property="billDate" />
    <result column="settle_type" property="settleType" />
    <result column="origin_price" property="originPrice" />
    <result column="tax" property="tax" />
    <result column="pre_residual_rate" property="preResidualRate" />
    <result column="pre_remaining_amount" property="preRemainingAmount" />
    <result column="pre_settle_count" property="preSettleCount" />
    <result column="settle_count" property="settleCount" />
    <result column="accumulation_amount" property="accumulationAmount" />
    <result column="remaining_amount" property="remainingAmount" />
    <result column="remark" property="remark" />
  </resultMap>

  <resultMap id="AssetMachineAccountMap" type="com.niimbot.finance.AssetMachineAccountDto">
    <result column="id" property="id"/>
    <result column="biz_code" property="bizCode"/>
    <result column="asset_id" property="assetId"/>
    <result column="status" property="status"/>
    <result column="asset_data" property="assetData"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="asset_code" property="assetCode"/>
    <result column="org_id" property="orgId" />
    <result column="settle_org_id" property="settleOrgId" />
    <result column="origin_price" property="originPrice" />
    <result column="pre_residual_rate" property="preResidualRate" />
    <result column="pre_settle_count" property="preSettleCount" />
    <result column="settle_count" property="settleCount" />
    <result column="remaining_settle_count" property="remainingSettleCount" />
    <result column="accumulation_amount" property="accumulationAmount" />
    <result column="remaining_amount" property="remainingAmount" />
    <result column="first_settle_month" property="firstSettleMonth" />
    <result column="settle_status" property="settleStatus" />
  </resultMap>

  <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.niimbot.finance.AssetFinanceInfoQueryDto">
    select
    <include refid="Base_Column_List" />
    from as_asset_finance_info
    <where>
        is_delete = 0 and bill_status = 1
        <if test="request.orgId != null">
            and org_id = #{request.orgId}
        </if>
        <if test="request.id != null">
            and id <![CDATA[>]]> #{request.id}
        </if>
    </where>
    order by id asc
    limit #{start}, #{size}
  </select>

  <update id="resetSettleData" parameterType="java.lang.Long">
    update as_asset_finance_info
    set
        <!--折旧次数回滚，已折旧次数减一，当前折旧次数重新为0 -->
        settle_count = settle_count - 1, current_settle_count = 0,
        <!--累计折旧金额回滚，剩余净值回滚 -->
        accumulation_amount = accumulation_amount - current_settle_amount, remaining_amount = remaining_amount + current_settle_amount,
        <!--当期折旧金额置为0，折旧状态回滚到之前的状态 -->
        current_settle_amount = 0, settle_status = previous_settle_status
    where org_id = #{orgId} and is_delete = 0 and (current_settle_amount <![CDATA[>]]> 0 or current_settle_count <![CDATA[>]]> 0)
  </update>

  <select id="selectValidCompany" resultType="java.lang.Long">
    select distinct org_id
    from as_asset_finance_info
    where is_delete = 0 and bill_status = 1 and settle_status != 3
  </select>

  <select id="countCurrentAssetNum" resultType="java.lang.Integer">
    select count(id)
    from as_asset_finance_info
    where org_id = #{orgId} and bill_date <![CDATA[ >= ]]> #{startDate} and bill_date <![CDATA[ <= ]]> #{endDate}
        and settle_type = 2 and bill_status = 1 and settle_status != 3 and is_delete = 0
  </select>

  <select id="countHistoryAssetNum" resultType="java.lang.Integer">
    select count(id)
    from as_asset_finance_info
    where org_id = #{orgId} and create_time <![CDATA[ >= ]]> #{startDate} and create_time <![CDATA[ <= ]]> #{endDate}
      and settle_type = 1 and bill_status = 1 and settle_status != 3 and is_delete = 0
  </select>

  <select id="countPreMonthAssetNum" resultType="java.lang.Integer">
    select count(id)
    from as_asset_finance_info
    where org_id = #{orgId} and bill_date <![CDATA[ < ]]> #{startDate} and bill_status = 1 and settle_status != 3 and is_delete = 0
  </select>

  <select id="countHandleAssetNum" resultType="java.lang.Integer">
    select count(id)
    from as_asset_finance_info
    where org_id = #{orgId} and handle_time <![CDATA[ >= ]]> #{startDate} and handle_time <![CDATA[ <= ]]> #{endDate}
      and bill_status = 1 and settle_status != 3 and is_delete = 0
  </select>

  <select id="selectAssetId" resultType="java.lang.Long">
    select distinct asset_id
    from as_asset_finance_info
    <where>
      org_id = #{orgId} and is_delete = 0
      <if test="billStatusList != null and billStatusList.size > 0">
        and bill_status in
        <foreach collection="billStatusList" index="index" item="billStatus" open="(" separator="," close=")">
            #{billStatus}
        </foreach>
      </if>
    </where>
  </select>

  <sql id="queryAssetCondition">
    <!-- 权限条件 -->
    <if test="param.permissionCondition != null and param.permissionCondition != ''">
      ${param.permissionCondition}
    </if>
    <!-- 资产状态 -->
    <if test="param.status != null and param.status.size() > 0">
      and a.status in
      <foreach collection="param.status" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 资产分类 -->
    <if test="param.assetCategory != null and param.assetCategory.size() > 0">
      and a.asset_category in
      <foreach collection="param.assetCategory" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 所属组织 -->
    <if test="param.orgOwner != null and param.orgOwner.size() > 0">
      and a.org_owner in
      <foreach collection="param.orgOwner" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 使用组织 -->
    <if test="param.useOrg != null and param.useOrg.size() > 0">
      and a.use_org in
      <foreach collection="param.useOrg" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 区域 -->
    <if test="param.storageArea != null and param.storageArea.size() > 0">
      and a.storage_area in
      <foreach collection="param.storageArea" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 品牌 -->
    <if test="param.brand!=null and param.brand!=''">
      and a.asset_data ->> '$.brand' like concat('%',#{param.brand},'%')
    </if>
    <!-- 规格型号 -->
    <if test="param.model!=null and param.model!=''">
      and a.asset_data ->> '$.model' like concat('%',#{param.model},'%')
    </if>
    <!-- 来源 -->
    <if test="param.assetOrigin != null and param.assetOrigin.size() > 0">
      and a.asset_origin in
      <foreach collection="param.assetOrigin" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <if test="param.buyTime!=null and param.buyTime.size==2">
      <!-- 购买开始时间 -->
      <if test="param.buyTime[0]!=null and param.buyTime[0]!='' and param.buyTime[0]!='null'">
        and a.asset_data ->> '$.buyTime' != ''
        and FROM_UNIXTIME( a.asset_data ->> '$.buyTime' / 1000, '%Y-%m-%d %H:%i:%s' ) &gt;=
        CONCAT(#{param.buyTime[0]}, ' 00:00:00')
      </if>
      <!-- 购买结束时间 -->
      <if test="param.buyTime[1]!=null and param.buyTime[1]!='' and param.buyTime[1]!='null'">
        and a.asset_data ->> '$.buyTime' != ''
        and FROM_UNIXTIME( a.asset_data ->> '$.buyTime' / 1000, '%Y-%m-%d %H:%i:%s' ) &lt;=
        CONCAT(#{param.buyTime[1]}, ' 23:59:59')
      </if>
    </if>
    <!-- 创建时间 -->
    <if test="param.createTime != null and param.createTime.size == 2">
      <if test="param.createTime[0] != null and param.createTime[0] != '' and param.createTime[0] != 'null'">
        and a.create_time &gt;= CONCAT(#{param.createTime[0]}, ' 00:00:00')
      </if>
      <if test="param.createTime[1]!=null and param.createTime[1] != '' and param.createTime[1] != 'null'">
        and a.create_time &lt;= CONCAT(#{param.createTime[1]}, ' 23:59:59')
      </if>
    </if>
    <!-- 关键字（编码/名称）-->
    <if test="param.kw!=null and param.kw!=''">
      and (
      (a.asset_code like concat('%',#{param.kw},'%'))
      or
      (a.asset_name like concat('%',#{param.kw},'%'))
      )
    </if>
    <if test="param.price!=null and param.price.size==2">
      <!-- 资产价值 -->
      <if test="param.price[0]!=null">
        and a.asset_data ->> '$.price' &gt;= #{param.price[0]}
      </if>
      <!-- 资产价值 -->
      <if test="param.price[1]!=null">
        and a.asset_data ->> '$.price' &lt;= #{param.price[1]}
      </if>
    </if>
    <!-- 资产使用期限-->
    <if test="param.useTimeLimit != null and param.useTimeLimit.size == 2">
      <if test="param.useTimeLimit[0] != null">
        and a.asset_data ->> '$.useTimeLimit' &gt;= #{param.useTimeLimit[0]}
      </if>
      <if test="param.useTimeLimit[1] != null">
        and a.asset_data ->> '$.useTimeLimit' &lt;= #{param.useTimeLimit[1]}
      </if>
    </if>
    <!-- 待入账资产查询条件：资产来源 -->
    <if test="param.waitAssetOrigin != null and param.waitAssetOrigin.size() > 0">
      and a.asset_origin in
      <foreach collection="param.waitAssetOrigin" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 待入账资产查询条件：资产价值 -->
    <if test="param.waitAssetPrice!=null">
      and a.asset_data ->> '$.price' &gt;= #{param.waitAssetPrice}
    </if>
    <!-- 排除资产 -->
    <if test="param.excludeAssetIds != null and param.excludeAssetIds.size() > 0">
      and a.id not in
      <foreach collection="param.excludeAssetIds" item="id" index="index" open="(" close=")"
               separator=",">
        #{id}
      </foreach>
    </if>
    <!-- 入账状态 -->
    <if test="param.billStatus != null">
        and b.bill_status = #{param.billStatus}
    </if>
    <!-- 折旧状态 -->
    <if test="param.settleStatus != null">
      and b.settle_status in
      <foreach collection="param.settleStatus" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 入账日期 -->
    <if test="param.billDateInt != null and param.billDateInt.size == 2">
      <if test="param.billDateInt[0] != null">
        and b.bill_date <![CDATA[ >= ]]> #{param.billDateInt[0]}
      </if>
      <if test="param.billDateInt[1] != null">
        and b.bill_date <![CDATA[ <= ]]> #{param.billDateInt[1]}
      </if>
    </if>
    <!-- 资产原值 -->
    <if test="param.originPrice != null and param.originPrice.size == 2">
      <if test="param.originPrice[0] != null">
        and b.origin_price <![CDATA[ >= ]]> #{param.originPrice[0]}
      </if>
      <if test="param.originPrice[1] != null">
        and b.origin_price <![CDATA[ <= ]]> #{param.originPrice[1]}
      </if>
    </if>
    <!-- 资产原值 -->
    <if test="param.accumulationPrice != null and param.accumulationPrice.size == 2">
      <if test="param.accumulationPrice[0] != null">
        and b.accumulation_amount <![CDATA[ >= ]]> #{param.accumulationPrice[0]}
      </if>
      <if test="param.accumulationPrice[1] != null">
        and b.accumulation_amount <![CDATA[ <= ]]> #{param.accumulationPrice[1]}
      </if>
    </if>
    <!-- 预计残值率 -->
    <if test="param.preResidualRate != null and param.preResidualRate.size == 2">
      <if test="param.preResidualRate[0] != null">
        and b.pre_residual_rate <![CDATA[ >= ]]> #{param.preResidualRate[0]}
      </if>
      <if test="param.preResidualRate[1] != null">
        and b.pre_residual_rate <![CDATA[ <= ]]> #{param.preResidualRate[1]}
      </if>
    </if>
    <!-- 剩余净值 -->
    <if test="param.remainingAmount != null and param.remainingAmount.size == 2">
      <if test="param.remainingAmount[0] != null">
        and b.remaining_amount <![CDATA[ >= ]]> #{param.remainingAmount[0]}
      </if>
      <if test="param.remainingAmount[1] != null">
        and b.remaining_amount <![CDATA[ <= ]]> #{param.remainingAmount[1]}
      </if>
    </if>
    <!-- 使用组织 -->
    <if test="param.settleOrgId != null and param.settleOrgId.size() > 0">
      and b.settle_org_id in
      <foreach collection="param.settleOrgId" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 入账类型 -->
    <if test="param.settleType != null and param.settleType.size() > 0">
      and b.settle_type in
      <foreach collection="param.settleType" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <!-- 预计使用期间数 -->
    <if test="param.preSettleCount != null and param.preSettleCount.size == 2">
      <if test="param.preSettleCount[0] != null">
        and b.pre_settle_count <![CDATA[ >= ]]> #{param.preSettleCount[0]}
      </if>
      <if test="param.preSettleCount[1] != null">
        and b.pre_settle_count <![CDATA[ <= ]]> #{param.preSettleCount[1]}
      </if>
    </if>
    <!-- 已使用期间数 -->
    <if test="param.settleCount != null and param.settleCount.size == 2">
      <if test="param.settleCount[0] != null">
        and b.settle_count <![CDATA[ >= ]]> #{param.settleCount[0]}
      </if>
      <if test="param.settleCount[1] != null">
        and b.settle_count <![CDATA[ <= ]]> #{param.settleCount[1]}
      </if>
    </if>
    <!-- 导出选中id -->
    <if test="param.exportIds != null and param.exportIds.size > 0">
      and b.id in
      <foreach collection="param.exportIds" item="item" index="index" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
  </sql>

  <select id="selectWaitFinanceAsset" resultMap="AsAssetMap" parameterType="com.niimbot.finance.AssetFinanceInfoQueryDto">
    select id, status, asset_data, asset_code, create_time
    from as_asset as a
    where a.is_delete = 0 and a.status != 4 and a.company_id = #{param.companyId}
    <include refid="queryAssetCondition"/>
    order by a.${param.sidx} ${param.order}
  </select>

  <select id="selectAssetFinance" resultMap="AssetFinanceInfoMap" parameterType="com.niimbot.finance.AssetFinanceInfoQueryDto">
    select a.id, a.status, a.asset_data, a.asset_code, a.create_time, b.biz_code, b.bill_status, b.settle_status
    from as_asset_finance_info as b left join as_asset as a on b.asset_id = a.id
    where b.is_delete = 0 and b.org_id = #{param.orgId} and a.is_delete = 0
    <include refid="queryAssetCondition"/>
    order by a.${param.sidx} ${param.order}
  </select>

  <select id="selectAssetInitFinance" resultMap="AssetInitFinanceInfoMap" parameterType="com.niimbot.finance.AssetFinanceInfoQueryDto">
    select a.id as asset_id, a.status, a.asset_data, a.asset_code, a.create_time, b.id, b.org_id, b.settle_org_id, b.strategy, b.bill_date, b.settle_type,
           b.origin_price, b.tax, b.pre_residual_rate, b.pre_remaining_amount, b.pre_settle_count, b.settle_count, b.accumulation_amount,
           b.remaining_amount, b.remark
    from as_asset_init_finance_info as b left join as_asset as a on b.asset_id = a.id
    where b.is_delete = 0 and b.org_id = #{param.orgId} and a.is_delete = 0
    <include refid="queryAssetCondition"/>
    order by a.${param.sidx} ${param.order}
  </select>

  <select id="selectMachineAccount" resultMap="AssetMachineAccountMap" parameterType="com.niimbot.finance.AssetFinanceInfoQueryDto">
    select d.* from (
    select a.id as asset_id, a.status, a.asset_data, a.asset_code, a.create_time, b.id, b.biz_code, b.org_id,
    b.settle_org_id,
    b.origin_price, b.pre_residual_rate, b.pre_settle_count, b.settle_count, (b.pre_settle_count - b.settle_count) as
    remaining_settle_count,
    b.accumulation_amount, b.remaining_amount, b.first_settle_month, b.settle_status
    from as_asset_finance_info as b left join as_asset as a on b.asset_id = a.id
    where b.is_delete = 0 and b.org_id = #{param.orgId} and a.is_delete = 0
    <include refid="queryAssetCondition"/>
    ) as d
    <where>
      <!-- 剩余使用期间数 -->
      <if test="param.remainingSettleCount != null and param.remainingSettleCount.size == 2">
        <if test="param.remainingSettleCount[0] != null">
          and d.remaining_settle_count <![CDATA[ >= ]]> #{param.remainingSettleCount[0]}
        </if>
        <if test="param.remainingSettleCount[1] != null">
          and d.remaining_settle_count <![CDATA[ <= ]]> #{param.remainingSettleCount[1]}
        </if>
      </if>
    </where>
    order by d.${param.sidx} ${param.order}
  </select>

</mapper>