<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetSettleSummaryMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetSettleSummary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="settle_org_id" jdbcType="BIGINT" property="settleOrgId" />
    <result column="asset_category" jdbcType="BIGINT" property="assetCategory" />
    <result column="asset_num" jdbcType="INTEGER" property="assetNum" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
    <result column="previous_remaining_amount" jdbcType="DECIMAL" property="previousRemainingAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="settle_month" jdbcType="INTEGER" property="settleMonth" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <resultMap id="SummaryResultMap" type="com.niimbot.finance.SettleSummaryDto">
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="asset_category" jdbcType="BIGINT" property="assetCategory" />
    <result column="asset_num" jdbcType="INTEGER" property="assetNum" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="settle_month" jdbcType="INTEGER" property="settleMonth" />
  </resultMap>

  <resultMap id="DeptSummaryResultMap" type="com.niimbot.finance.SettleSummaryDeptDto">
    <result column="settle_org_id" jdbcType="BIGINT" property="settleOrgId" />
    <result column="asset_category" jdbcType="BIGINT" property="assetCategory" />
    <result column="asset_num" jdbcType="INTEGER" property="assetNum" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="accumulation_amount" jdbcType="DECIMAL" property="accumulationAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="settle_month" jdbcType="INTEGER" property="settleMonth" />
  </resultMap>

  <sql id="Base_Column_List">
    id, org_id, org_name, settle_org_id, asset_category, asset_num, origin_price, settle_amount, accumulation_amount,
    previous_remaining_amount, remaining_amount, settle_month, is_delete, create_by, create_time
  </sql>

  <select id="pageQuery" resultMap="SummaryResultMap" parameterType="com.niimbot.finance.SettleSummaryQueryDto">
    select
    org_id, asset_category, sum(asset_num) as asset_num,
    sum(origin_price) as origin_price, sum(settle_amount) as settle_amount,
    sum(accumulation_amount) as accumulation_amount, sum(remaining_amount) as remaining_amount
    from as_asset_settle_summary
    <where>
      is_delete = 0
      <if test="param.orgId != null and param.orgId.size > 0">
        and org_id in
        <foreach collection="param.orgId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="param.settleMonthInt != null">
        and settle_month = #{param.settleMonthInt}
      </if>
      <if test="param.assetCategory != null and param.assetCategory.size > 0">
        and asset_category in
        <foreach collection="param.assetCategory" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="param.settleOrgId != null and param.settleOrgId.size > 0">
        and settle_org_id in
        <foreach collection="param.settleOrgId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="param.originPrice != null and param.originPrice.size == 2">
        <if test="param.originPrice[0] != null">
          and origin_price <![CDATA[ >= ]]> #{param.originPrice[0]}
        </if>
        <if test="param.originPrice[1] != null">
          and origin_price <![CDATA[ <= ]]> #{param.originPrice[1]}
        </if>
      </if>
      <if test="param.settleAmount != null and param.settleAmount.size == 2">
        <if test="param.settleAmount[0] != null">
          and settle_amount <![CDATA[ >= ]]> #{param.settleAmount[0]}
        </if>
        <if test="param.settleAmount[1] != null">
          and settle_amount <![CDATA[ <= ]]> #{param.settleAmount[1]}
        </if>
      </if>
      <if test="param.remainingAmount != null and param.remainingAmount.size == 2">
        <if test="param.remainingAmount[0] != null">
          and remaining_amount <![CDATA[ >= ]]> #{param.remainingAmount[0]}
        </if>
        <if test="param.remainingAmount[1] != null">
          and remaining_amount <![CDATA[ <= ]]> #{param.remainingAmount[1]}
        </if>
      </if>
      <if test="param.accumulationAmount != null and param.accumulationAmount.size == 2">
        <if test="param.accumulationAmount[0] != null">
          and accumulation_amount <![CDATA[ >= ]]> #{param.accumulationAmount[0]}
        </if>
        <if test="param.accumulationAmount[1] != null">
          and accumulation_amount <![CDATA[ <= ]]> #{param.accumulationAmount[1]}
        </if>
      </if>
    </where>
    group by org_id, asset_category
    order by org_name, asset_category
  </select>

  <select id="pageQueryDept" resultMap="DeptSummaryResultMap" parameterType="com.niimbot.finance.SettleSummaryQueryDto">
    select
    settle_org_id, asset_category, sum(asset_num) as asset_num,
    sum(origin_price) as origin_price, sum(settle_amount) as settle_amount,
    sum(accumulation_amount) as accumulation_amount, sum(remaining_amount) as remaining_amount
    from as_asset_settle_summary
    <where>
      is_delete = 0
      <if test="param.orgId != null and param.orgId.size > 0">
        and org_id in
        <foreach collection="param.orgId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="param.settleMonthInt != null">
        and settle_month = #{param.settleMonthInt}
      </if>
      <if test="param.assetCategory != null and param.assetCategory.size > 0">
        and asset_category in
        <foreach collection="param.assetCategory" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="param.settleOrgId != null and param.settleOrgId.size > 0">
        and settle_org_id in
        <foreach collection="param.settleOrgId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="param.originPrice != null and param.originPrice.size == 2">
        <if test="param.originPrice[0] != null">
          and origin_price <![CDATA[ >= ]]> #{param.originPrice[0]}
        </if>
        <if test="param.originPrice[1] != null">
          and origin_price <![CDATA[ <= ]]> #{param.originPrice[1]}
        </if>
      </if>
      <if test="param.settleAmount != null and param.settleAmount.size == 2">
        <if test="param.settleAmount[0] != null">
          and settle_amount <![CDATA[ >= ]]> #{param.settleAmount[0]}
        </if>
        <if test="param.settleAmount[1] != null">
          and settle_amount <![CDATA[ <= ]]> #{param.settleAmount[1]}
        </if>
      </if>
      <if test="param.remainingAmount != null and param.remainingAmount.size == 2">
        <if test="param.remainingAmount[0] != null">
          and remaining_amount <![CDATA[ >= ]]> #{param.remainingAmount[0]}
        </if>
        <if test="param.remainingAmount[1] != null">
          and remaining_amount <![CDATA[ <= ]]> #{param.remainingAmount[1]}
        </if>
      </if>
      <if test="param.accumulationAmount != null and param.accumulationAmount.size == 2">
        <if test="param.accumulationAmount[0] != null">
          and accumulation_amount <![CDATA[ >= ]]> #{param.accumulationAmount[0]}
        </if>
        <if test="param.accumulationAmount[1] != null">
          and accumulation_amount <![CDATA[ <= ]]> #{param.accumulationAmount[1]}
        </if>
      </if>
    </where>
    group by settle_org_id, asset_category
    order by settle_org_id, asset_category
  </select>
</mapper>