<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.mapper.AsAssetDepreciationConfigMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.model.AsAssetDepreciationConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="asset_origin" jdbcType="VARCHAR" property="assetOrigin" />
    <result column="asset_price" jdbcType="DECIMAL" property="assetPrice" />
    <result column="strategy" jdbcType="INTEGER" property="strategy" />
    <result column="manual_settle" jdbcType="INTEGER" property="manualSettle" />
    <result column="first_settle_time" jdbcType="INTEGER" property="firstSettleTime" />
    <result column="depreciation_param" jdbcType="VARCHAR" property="depreciationParam" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_code, org_id, asset_origin, asset_price, strategy, manual_settle, first_settle_time, 
    depreciation_param, status, create_by, create_time, update_by, update_time
  </sql>

  <select id="selectMinSettleTime" resultType="java.lang.Integer">
    select min(first_settle_time) as minSettleTime
    from as_asset_depreciation_config
    <where>
        status = 0
        <if test="orgIdList != null and orgIdList.size > 0">
            and org_id in
            <foreach collection="orgIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </where>
  </select>
</mapper>