package com.niimbot.asset.finance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.finance.mapper.AsAssetSettleDetailMapper;
import com.niimbot.asset.finance.model.AsAssetSettleDetail;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;
import com.niimbot.asset.finance.service.AssetSettleDetailService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.means.resolver.MySqlAssetQueryConditionResolver;
import com.niimbot.finance.SettleDetailDto;
import com.niimbot.finance.SettleDetailQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:09
 */
@Slf4j
@Service
public class AssetSettleDetailServiceImpl extends ServiceImpl<AsAssetSettleDetailMapper, AsAssetSettleDetail> implements AssetSettleDetailService {

    @Resource
    private MySqlAssetQueryConditionResolver conditionResolver;

    @Autowired
    private CacheResourceUtil cacheResourceUtil;

    @Override
    public List<AsAssetSettleSummary> queryCompanySummary(Long companyId, Integer settleMonth) {
        if (Objects.isNull(companyId) || Objects.isNull(settleMonth)) {
            return null;
        }
        return this.getBaseMapper().selectCompanySummary(companyId, settleMonth);
    }

    @Override
    public IPage<SettleDetailDto> pageQuery(SettleDetailQueryDto queryDto) {
        //折旧月份转换
        if (StrUtil.isNotBlank(queryDto.getSettleMonth())) {
            queryDto.setSettleMonthInt(Integer.parseInt(queryDto.getSettleMonth().replace("-", "")));
        }
        //资产权限过滤条件
        String permissionCondition = conditionResolver.getPermsSql("a");
        queryDto.setPermissionCondition(permissionCondition);
        IPage<SettleDetailDto>  detailPage = this.getBaseMapper().pageQuery(queryDto.buildIPage(), queryDto);
        if (Objects.nonNull(detailPage) && CollUtil.isNotEmpty(detailPage.getRecords())) {
            for (SettleDetailDto item : detailPage.getRecords()) {
                item.setStatusText(cacheResourceUtil.getAssetStatusName(item.getStatus().longValue()));
                item.setOrgName(cacheResourceUtil.getOrgName(item.getOrgId()));
                item.setSettleOrgName(cacheResourceUtil.getOrgName(item.getSettleOrgId()));
                if (Objects.nonNull(item.getAssetCategory())) {
                    item.setAssetCategoryDesc(cacheResourceUtil.getCategoryName(item.getAssetCategory()));
                }
            }
        }
        return detailPage;
    }
}
