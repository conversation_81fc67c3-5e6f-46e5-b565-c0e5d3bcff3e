package com.niimbot.asset.finance.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_asset_settle_detail
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_asset_settle_detail", autoResultMap = true)
public class AsAssetSettleDetail implements Serializable {

    private static final long serialVersionUID = -8669077275947634961L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属组织
     */
    private Long orgId;

    /**
     * 分摊部门
     */
    private Long settleOrgId;

    /**
     * 资产id
     */
    private Long assetId;

    /**
     * 资产编码
     */
    private String assetCode;

    /**
     * 资产分类
     */
    private Long assetCategory;

    /**
     * 财务入账信息编码
     */
    private String assetFinanceCode;

    /**
     * 本币原值
     */
    private BigDecimal originPrice;

    /**
     * 本期折旧金额
     */
    private BigDecimal settleAmount;

    /**
     * 累积折旧
     */
    private BigDecimal accumulationAmount;

    /**
     * 上期净值
     */
    private BigDecimal previousRemainingAmount;

    /**
     * 上期净值
     */
    private BigDecimal remainingAmount;

    /**
     * 会计期间
     */
    private Integer settleMonth;

    /**
     * 是否删除 0-否  1-是
     */
    private Integer isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}