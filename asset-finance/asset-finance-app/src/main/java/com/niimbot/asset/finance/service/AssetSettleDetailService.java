package com.niimbot.asset.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.finance.SettleDetailDto;
import com.niimbot.finance.SettleDetailQueryDto;
import com.niimbot.asset.finance.model.AsAssetSettleDetail;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:06
 */
public interface AssetSettleDetailService extends IService<AsAssetSettleDetail> {

    /**
     * 折旧明细按分摊部门和资产分类进行汇总
     * @param companyId
     * @param settleMonth
     * @return
     */
    List<AsAssetSettleSummary> queryCompanySummary(Long companyId, Integer settleMonth);

    /**
     * 分页查询
     * @param queryDto
     * @return
     */
    IPage<SettleDetailDto> pageQuery(SettleDetailQueryDto queryDto);
}
