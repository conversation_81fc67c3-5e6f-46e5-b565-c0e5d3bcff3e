package com.niimbot.asset.finance.enums;

/**
 * <AUTHOR>
 * @date 2023/2/13 下午2:27
 */
public enum SettleTaskStatusEnum {

    /**
     * 执行失败
     */
    FAIL(-1, "执行失败"),
    /**
     * 待执行
     */
    INIT(0, "待执行"),
    /**
     * 执行中
     */
    EXECUTE(1, "执行中"),
    /**
     * 执行成功
     */
    SUCCESS(2, "执行成功"),

    ;

    SettleTaskStatusEnum(Integer statusCode, String statusDesc) {
        this.statusCode = statusCode;
        this.statusDesc = statusDesc;
    }

    private Integer statusCode;

    private String statusDesc;

    public Integer getStatusCode() {
        return statusCode;
    }

    public String getStatusDesc() {
        return statusDesc;
    }
}
