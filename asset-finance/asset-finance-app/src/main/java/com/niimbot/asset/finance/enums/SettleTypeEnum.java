package com.niimbot.asset.finance.enums;

import java.util.Objects;

/**
 * 入账类型枚举
 *
 * <AUTHOR>
 * @date 2023/2/8 下午3:58
 */
public enum SettleTypeEnum {

    /**
     * 历史入账
     */
    HISTORY(1, "历史入账"),
    /**
     * 当期入账
     */
    PERIOD(2, "当期入账"),
    /**
     * 未知
     */
    UN_KNOW(-1, "未知"),
    ;

    SettleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SettleTypeEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (SettleTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static SettleTypeEnum getByDesc(String desc) {
        if (Objects.isNull(desc)) {
            return UN_KNOW;
        }

        for (SettleTypeEnum item : values()) {
            if (item.getDesc().equalsIgnoreCase(desc)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
