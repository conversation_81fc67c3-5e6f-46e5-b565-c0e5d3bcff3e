package com.niimbot.asset.finance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.finance.enums.AssetBillStatusEnum;
import com.niimbot.asset.finance.enums.AssetFinanceStatusEnum;
import com.niimbot.asset.finance.enums.AssetSettleStatusEnum;
import com.niimbot.asset.finance.enums.DepreciationConfigStatusEnum;
import com.niimbot.asset.finance.enums.DepreciationStrategyEnum;
import com.niimbot.asset.finance.enums.SettleTypeEnum;
import com.niimbot.asset.finance.mapper.AsAssetFinanceInfoMapper;
import com.niimbot.asset.finance.model.AsAssetDepreciationConfig;
import com.niimbot.asset.finance.model.AsAssetFinanceAlterLog;
import com.niimbot.asset.finance.model.AsAssetFinanceInfo;
import com.niimbot.asset.finance.model.AsAssetInitFinanceInfo;
import com.niimbot.asset.finance.model.AsDepreciationOperationLog;
import com.niimbot.asset.finance.service.AssetFinanceAlterLogService;
import com.niimbot.asset.finance.service.AssetFinanceInfoService;
import com.niimbot.asset.finance.service.AssetInitFinanceInfoService;
import com.niimbot.asset.finance.service.AssetSettleInfoService;
import com.niimbot.asset.finance.service.DepreciationConfigService;
import com.niimbot.asset.finance.service.DepreciationOperationLogService;
import com.niimbot.asset.finance.utils.CompareUtil;
import com.niimbot.asset.finance.utils.TimeUtil;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.resolver.MySqlAssetQueryConditionResolver;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.finance.AlterLogQueryDto;
import com.niimbot.finance.AssetAttributeAlterMessageDto;
import com.niimbot.finance.AssetFinanceAlterLogDto;
import com.niimbot.finance.AssetFinanceDefaultDto;
import com.niimbot.finance.AssetFinanceImportDto;
import com.niimbot.finance.AssetFinanceInfoConfigDto;
import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.finance.AssetFinanceInfoQueryDto;
import com.niimbot.finance.AssetInitFinanceInfoDto;
import com.niimbot.finance.AssetMachineAccountDto;
import com.niimbot.finance.ComparisonDto;
import com.niimbot.finance.DepreciationConfigParamDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午3:52
 */
@Slf4j
@Service
public class AssetFinanceInfoServiceImpl extends ServiceImpl<AsAssetFinanceInfoMapper, AsAssetFinanceInfo> implements AssetFinanceInfoService {

    //变更日志模板
    public static String ALTER_LOG_TEMPLATE = "【%s】由【%s】变更为【%s】";

    @Autowired
    private AssetService assetService;
    @Resource
    private MySqlAssetQueryConditionResolver conditionResolver;
    @Autowired
    private OrgService orgService;
    @Autowired
    private DepreciationConfigService depreciationConfigService;
    @Autowired
    private AssetInitFinanceInfoService initFinanceInfoService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private AssetFinanceAlterLogService alterLogService;
    @Autowired
    private DepreciationOperationLogService depreciationOperationLog;
    @Autowired
    private CacheResourceUtil cacheResourceUtil;
    @Autowired
    private AsAssetImportErrorService assetImportErrorService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private AssetSettleInfoService settleInfoService;

    @Override
    public AssetFinanceDefaultDto assetFinanceDefault(Long assetId) {
        log.info("assetFinanceInfoService assetFinanceDefault assetId=[{}]", assetId);
        AsAsset asset = assetService.getById(assetId);
        if (Objects.isNull(asset)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产信息不存在");
        }
        if (StrUtil.isBlank(asset.getOrgOwner())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产所属组织为空");
        }
        //资产所属组织
        Long orgOwnerId = Long.parseLong(asset.getOrgOwner());
        AsOrg orgOwner = orgService.getById(orgOwnerId);
        if (Objects.isNull(orgOwner)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产所属组织不存在");
        }

        AssetFinanceDefaultDto result = new AssetFinanceDefaultDto();
        //如果所属组织是企业，直接取值，所属组织是部门，取部门所在公司
        if (Objects.nonNull(orgOwner.getOrgType()) && orgOwner.getOrgType() == 1) {
            result.setOrgId(orgOwner.getId());
            result.setOrgName(orgOwner.getOrgName());
        } else {
            result.setOrgId(orgOwner.getCompanyOwner());
            result.setOrgName(cacheResourceUtil.getOrgName(orgOwner.getCompanyOwner()));
        }

        //资产使用组织不为空，且使用组织为部门类型
        if (StrUtil.isNotBlank(asset.getUseOrg())) {
            AsOrg useOrg = orgService.getById(Long.parseLong(asset.getUseOrg()));
            if (Objects.nonNull(useOrg) && useOrg.getOrgType() == 2) {
                result.setSettleOrgId(useOrg.getId());
                result.setSettleOrgName(useOrg.getOrgName());
            }
        }

        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, result.getOrgId())
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }

        //折旧方案配置参数
        List<DepreciationConfigParamDto> depreciationParamList = JSONObject.parseArray(depreciationConfig.getDepreciationParam(), DepreciationConfigParamDto.class);
        Optional<DepreciationConfigParamDto> matchConfig = matchDepreciationParam(depreciationParamList, asset.getAssetData().getString("assetCategory"));

        //设置预计残值率和预计使用期间数
        matchConfig.ifPresent(depreciationConfigParamDto -> {
            result.setPreResidualRate(depreciationConfigParamDto.getResidualRate());
            result.setPreSettleCount(depreciationConfigParamDto.getPreSettleCount());
        });
        //设置预计残值率和预计使用期间数
        if (Objects.nonNull(asset.getAssetData().getInteger("useTimeLimit"))) {
            result.setPreSettleCount(asset.getAssetData().getInteger("useTimeLimit"));
        }

        //资产价值
        if (Objects.nonNull(asset.getAssetData().getBigDecimal("price"))) {
            result.setOriginPrice(asset.getAssetData().getBigDecimal("price"));
        }
        result.setStrategy(depreciationConfig.getStrategy());
        result.setStrategyName(DepreciationStrategyEnum.getByCode(depreciationConfig.getStrategy()).getDesc());
        return result;
    }

    /**
     * 匹配这就参数
     *
     * @param depreciationParamList
     * @param assetCategory
     * @return
     */
    private Optional<DepreciationConfigParamDto> matchDepreciationParam(List<DepreciationConfigParamDto> depreciationParamList, String assetCategory) {
        Optional<DepreciationConfigParamDto> matchConfig = Optional.empty();
        //根据资产分类进行匹配，找到对应的折旧配置默认参数
        if (CollUtil.isNotEmpty(depreciationParamList)) {
            matchConfig = depreciationParamList.stream().filter(item -> CollUtil.isEmpty(item.getAssetCategory())
                    || item.getAssetCategory().contains(assetCategory)).findAny();
        }
        return matchConfig;
    }

    @Override
    public Boolean saveConfig(AssetFinanceInfoConfigDto configDto) {
        //校验入账信息参数
        verifyFinanceParam(configDto, Boolean.FALSE);

        //判断是否已存在当前企业当前资产的入账信息
        AsAssetFinanceInfo financeInfo = getOne(Wrappers.lambdaQuery(AsAssetFinanceInfo.class)
                .eq(AsAssetFinanceInfo::getOrgId, configDto.getOrgId())
                .eq(AsAssetFinanceInfo::getAssetId, configDto.getAssetId())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        if (Objects.nonNull(financeInfo) && AssetFinanceStatusEnum.CREDITED.getCode().equals(financeInfo.getBillStatus())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产入账信息已存在");
        }

        //入账信息
        AsAssetFinanceInfo assetFinanceInfo = new AsAssetFinanceInfo();
        //初始入账信息
        AsAssetInitFinanceInfo assetInitFinanceInfo = new AsAssetInitFinanceInfo();

        BeanUtils.copyProperties(configDto, assetFinanceInfo);
        //设置入账日期
        assetFinanceInfo.setBillDate(TimeUtil.formatDateInteger(configDto.getBillDate()));
        BeanUtils.copyProperties(assetFinanceInfo, assetInitFinanceInfo);

        //反入账重新登记
        if (Objects.nonNull(financeInfo)) {
            //反入账了之后，才允许重新入账，其他的只有走编辑或变更的方式进行修改入账信息
            if (!AssetFinanceStatusEnum.UN_CREDITED.getCode().equals(financeInfo.getBillStatus())) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "非反入账状态，不允许重新入账");
            }
            //重新设置入账状态
            assetFinanceInfo.setBillStatus(AssetFinanceStatusEnum.CREDITED.getCode());
            assetInitFinanceInfo.setBizCode(financeInfo.getBizCode());
            //保存初始入账信息，反入账的时候会删除掉初始入账信息
            initFinanceInfoService.save(assetInitFinanceInfo);
            //更新入账信息
            return update(assetFinanceInfo, Wrappers.lambdaUpdate(AsAssetFinanceInfo.class).eq(AsAssetFinanceInfo::getBizCode, financeInfo.getBizCode()));
        } else {
            //保存入账信息
            assetFinanceInfo.setBizCode(IdUtil.simpleUUID());
            assetInitFinanceInfo.setBizCode(assetFinanceInfo.getBizCode());
            //保存入账快照信息
            initFinanceInfoService.save(assetInitFinanceInfo);
            return save(assetFinanceInfo);
        }
    }

    @Override
    public Boolean saveImportData(AssetFinanceImportDto importDto) {
        try {
            //转换和校验资产入账导入数据
            AssetFinanceInfoConfigDto configDto = transferAndVerifyImportData(importDto);

            if (importDto.getErrorNum() > 0) {
                AsAssetImportError importError = copyToAsImportError(importDto);
                assetImportErrorService.saveOrUpdate(importError);
                redisService.hIncr(RedisConstant.companyImportKey("asset_finance", importDto.getCompanyId()), "error", 1L);
                return false;
            } else {
                //查询已存在的资产信息，对于非待折旧
                AsAssetFinanceInfo financeInfo = getOne(Wrappers.lambdaQuery(AsAssetFinanceInfo.class)
                        .eq(AsAssetFinanceInfo::getOrgId, configDto.getOrgId())
                        .eq(AsAssetFinanceInfo::getAssetId, configDto.getAssetId())
                        .eq(AsAssetFinanceInfo::getIsDelete, 0));
                if (Objects.nonNull(financeInfo) && !AssetSettleStatusEnum.WAIT.getCode().equals(financeInfo.getSettleStatus())) {
                    LuckySheetModel codee = importDto.getSheetModelList().get(0);
                    if (ObjectUtil.isNull(codee.getV().getPs())) {
                        LuckySheetModel.Comment comment = new LuckySheetModel.Comment("资产非待折旧，不允许覆盖");
                        codee.getV().setPs(comment);
                        importDto.setErrorNum(importDto.getErrorNum() + 1);
                    } else {
                        codee.getV().getPs().setValue(String.join(",", codee.getV().getPs().getValue(), "资产非待折旧，不允许覆盖"));
                        importDto.setErrorNum(importDto.getErrorNum() + 1);
                    }
                    AsAssetImportError importError = copyToAsImportError(importDto);
                    assetImportErrorService.saveOrUpdate(importError);
                    redisService.hIncr(RedisConstant.companyImportKey("asset_finance", importDto.getCompanyId()), "error", 1L);
                    return false;
                }
                //入账信息
                AsAssetFinanceInfo assetFinanceInfo = new AsAssetFinanceInfo();
                //初始入账信息
                AsAssetInitFinanceInfo assetInitFinanceInfo = new AsAssetInitFinanceInfo();

                BeanUtils.copyProperties(configDto, assetFinanceInfo);
                //设置入账日期
                assetFinanceInfo.setBillDate(TimeUtil.formatDateInteger(configDto.getBillDate()));
                BeanUtils.copyProperties(assetFinanceInfo, assetInitFinanceInfo);

                //反入账重新登记
                if (Objects.nonNull(financeInfo)) {
                    //保存初始入账信息，反入账的时候会删除掉初始入账信息
                    if (AssetFinanceStatusEnum.UN_CREDITED.getCode().equals(financeInfo.getBillStatus())) {
                        assetInitFinanceInfo.setBizCode(financeInfo.getBizCode());
                        initFinanceInfoService.save(assetInitFinanceInfo);
                    }
                    //更新入账信息
                    update(assetFinanceInfo, Wrappers.lambdaUpdate(AsAssetFinanceInfo.class).eq(AsAssetFinanceInfo::getBizCode, financeInfo.getBizCode()));
                } else {
                    //保存入账信息
                    assetFinanceInfo.setBizCode(IdUtil.simpleUUID());
                    assetInitFinanceInfo.setBizCode(assetFinanceInfo.getBizCode());
                    //保存入账快照信息
                    initFinanceInfoService.save(assetInitFinanceInfo);
                    save(assetFinanceInfo);
                }
                String importRedisKey = RedisConstant.companyImportKey("asset_finance", importDto.getCompanyId());
                redisService.hIncr(importRedisKey, "success", 1L);
                redisService.hIncr(importRedisKey, "totalSuccess", 1L);
                return true;
            }
        } catch (Exception e) {
            importDto.setErrorNum(importDto.getErrorNum() + 1);
            log.error("assetFinanceInfoService saveImportData error! param=[{}] exception ", JSONObject.toJSONString(importDto), e);
            AsAssetImportError importError = copyToAsImportError(importDto);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("asset_finance", importDto.getCompanyId()), "error", 1L);
            return false;
        }
    }

    private AsAssetImportError copyToAsImportError(AssetFinanceImportDto importDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_ASSET_FINANCE);
        List<LuckySheetModel> sheetModelList = importDto.getSheetModelList();
        importError.setExcelJson(new ArrayList<>(sheetModelList));
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }

    /**
     * 转换和校验资产入账导入数据
     *
     * @param importDto
     * @return
     */
    private AssetFinanceInfoConfigDto transferAndVerifyImportData(AssetFinanceImportDto importDto) {
        AssetFinanceInfoConfigDto result = new AssetFinanceInfoConfigDto();
        BeanUtils.copyProperties(importDto, result);

        //本币原值
        try {
            BigDecimal originPrice = new BigDecimal(importDto.getOriginPrice());
            BigDecimal startValue = BigDecimal.valueOf(0.01);
            BigDecimal endValue = BigDecimal.valueOf(9999999999.99);
            if (originPrice.compareTo(startValue) < 0 || originPrice.compareTo(endValue) > 0) {
                LuckySheetModel codee = importDto.getSheetModelList().get(8);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("本币原值最小值须大于0，本币原值最大值为9999999999.99");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            } else {
                result.setOriginPrice(originPrice);
            }
        } catch (Exception e) {
            LuckySheetModel codee = importDto.getSheetModelList().get(8);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("本币原值最小值须大于0，本币原值最大值为9999999999.99");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }

        //可抵扣税额
        if (StrUtil.isNotBlank(importDto.getTax())) {
            try {
                BigDecimal tax = new BigDecimal(importDto.getTax());
                result.setTax(tax);
            } catch (Exception e) {
                LuckySheetModel codee = importDto.getSheetModelList().get(9);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("可抵扣税额需数值型数据");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
        }

        //预计残值率
        try {
            BigDecimal preResidualRate = new BigDecimal(importDto.getPreResidualRate());
            BigDecimal startValue = BigDecimal.valueOf(0.00);
            BigDecimal endValue = BigDecimal.valueOf(99.99);
            if (preResidualRate.compareTo(startValue) < 0 || preResidualRate.compareTo(endValue) > 0) {
                LuckySheetModel codee = importDto.getSheetModelList().get(10);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("预计残值率最小值须大于0，本币原值最大值为99.99");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            } else {
                result.setPreResidualRate(preResidualRate);
            }
        } catch (Exception e) {
            LuckySheetModel codee = importDto.getSheetModelList().get(10);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("预计残值率最小值须大于0，本币原值最大值为99.99");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }

        //预计使用期间数
        try {
            int preSettleCount = Integer.parseInt(importDto.getPreSettleCount());
            if (preSettleCount < 1) {
                LuckySheetModel codee = importDto.getSheetModelList().get(11);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("预计使用期间数最小值为1");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            } else {
                result.setPreSettleCount(preSettleCount);
            }
        } catch (Exception e) {
            LuckySheetModel codee = importDto.getSheetModelList().get(11);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("预计使用期间数最小值为1");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }

        //已折旧期间数
        if (StrUtil.isNotBlank(importDto.getSettleCount())) {
            try {
                Integer settleCount = Integer.parseInt(importDto.getSettleCount());
                result.setSettleCount(settleCount);
            } catch (Exception e) {
                LuckySheetModel codee = importDto.getSheetModelList().get(12);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("已折旧期间数需数值型");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
        }

        //累计折旧
        if (StrUtil.isNotBlank(importDto.getAccumulationAmount())) {
            try {
                BigDecimal accumulationAmount = new BigDecimal(importDto.getAccumulationAmount());
                result.setAccumulationAmount(accumulationAmount);
            } catch (Exception e) {
                LuckySheetModel codee = importDto.getSheetModelList().get(13);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("累计折旧需数值型");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
        }

        //上面类型值转换错误，直接返回
        if (importDto.getErrorNum() > 1) {
            return result;
        }

        //入账类型
        if (SettleTypeEnum.UN_KNOW.equals(SettleTypeEnum.getByDesc(result.getSettleTypeDesc()))) {
            LuckySheetModel codee = importDto.getSheetModelList().get(5);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("入账类型错误");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        } else {
            result.setSettleType(SettleTypeEnum.getByDesc(result.getSettleTypeDesc()).getCode());
        }

        //资产编码
        AsAsset asset = assetService.getInfoByCode(importDto.getAssetCode(), importDto.getCompanyId());
        if (Objects.isNull(asset)) {
            LuckySheetModel codee = importDto.getSheetModelList().get(0);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("资产编码不存在或不存在该资产权限");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        } else {
            result.setAssetId(asset.getId());
            //查询资产所属组织信息，组织可能为公司，也有可能是部门
            AsOrg org = orgService.getById(asset.getAssetData().getLong("orgOwner"));
            Long companyId = null;
            if (Objects.isNull(org)) {
                LuckySheetModel codee = importDto.getSheetModelList().get(0);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("资产所属组织不存在");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                } else {
                    codee.getV().getPs().setValue(String.join(",", codee.getV().getPs().getValue(), "资产所属组织不存在"));
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            } else {
                companyId = org.getCompanyOwner();
            }

            //过滤已处置资产
            if (asset.getStatus() != null && asset.getStatus() == 4) {
                LuckySheetModel codee = importDto.getSheetModelList().get(0);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("资产已处置，不允许导入");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                } else {
                    codee.getV().getPs().setValue(String.join(",", codee.getV().getPs().getValue(), "资产已处置，不允许导入"));
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }

            //设置资产所属企业信息
            result.setOrgId(companyId);
            result.setAssetCode(asset.getAssetData().getString("assetCode"));
            result.setAssetCategory(StrUtil.isNotBlank(asset.getAssetData().getString("assetCategory")) ? Long.parseLong(asset.getAssetData().getString("assetCategory")) : null);

            //获取分摊部门
            AsOrg settleOrg = null;
            if (StrUtil.isNotBlank(result.getSettleOrgName())) {
                // 如果是微信用编码查，saas和钉钉是采用名称进行筛选
                if (Edition.isWeixin()) {
                    settleOrg = orgService.getOne(
                            Wrappers.lambdaQuery(AsOrg.class)
                                    .eq(AsOrg::getCompanyOwner, companyId)
                                    .eq(AsOrg::getOrgCode, result.getSettleOrgCode()),
                            false
                    );
                    result.setSettleOrgId(settleOrg.getId());
                } else {
                    //判断分摊部门是否包含部门编码，为了处理分摊部门名称重复问题
                    String settleOrgName = result.getSettleOrgName();
                    String orgName = null;
                    String orgCode = null;
                    boolean containOrgCode = ExcelUtils.matchCode(result.getSettleOrgName());
                    if (containOrgCode) {
                        settleOrgName = result.getSettleOrgName().replace("(", "（");
                        settleOrgName = settleOrgName.replace(")", "）");

                        int startIndex = settleOrgName.indexOf("（");
                        int endIndex = settleOrgName.lastIndexOf("）");
                        orgName = settleOrgName.substring(0, startIndex);
                        orgCode = settleOrgName.substring(startIndex + 1, endIndex);
                    } else {
                        orgName = result.getSettleOrgName();
                    }

                    //如果分摊部门名称里面填写了分摊部门编码，则查询条件里面带上分摊部门编码
                    LambdaQueryWrapper<AsOrg> orgWrapper = Wrappers.lambdaQuery(AsOrg.class)
                            .eq(AsOrg::getCompanyOwner, companyId)
                            .eq(AsOrg::getIsDelete, 0);
                    if (StrUtil.isNotBlank(orgCode)) {
                        String finalOrgName = orgName;
                        String finalOrgCode = orgCode;
                        String finalSettleOrgName = result.getSettleOrgName();
                        orgWrapper.and(wrapper -> wrapper.eq(AsOrg::getOrgName, finalOrgName).eq(AsOrg::getOrgCode, finalOrgCode)
                                .or()
                                .eq(AsOrg::getOrgName, finalSettleOrgName));
                    } else {
                        orgWrapper.eq(AsOrg::getOrgName, settleOrgName);
                    }
                    List<AsOrg> orgList = orgService.list(orgWrapper);
                    if (CollUtil.isNotEmpty(orgList)) {
                        settleOrg = orgList.get(0);
                        result.setSettleOrgId(settleOrg.getId());
                    }
                }
            } else {
                settleOrg = orgService.getById(result.getSettleOrgId());
            }

            //校验分摊部门
            if (Objects.isNull(settleOrg) || settleOrg.getOrgType() != 2) {
                LuckySheetModel codee = importDto.getSheetModelList().get(6);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("分摊部门不存在");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
            //校验分摊部门为当前企业下
            if (Objects.nonNull(settleOrg) && !settleOrg.getCompanyOwner().equals(result.getOrgId())) {
                LuckySheetModel codee = importDto.getSheetModelList().get(6);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("分摊部门不属于当前公司");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                } else {
                    codee.getV().getPs().setValue(String.join(",", codee.getV().getPs().getValue(), "分摊部门不属于当前公司"));
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }

            //校验折旧方案是否已停用
            AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                    .eq(AsAssetDepreciationConfig::getOrgId, result.getOrgId())
                    .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
            if (Objects.isNull(depreciationConfig)) {
                LuckySheetModel codee = importDto.getSheetModelList().get(0);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("企业折旧已停用");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                } else {
                    codee.getV().getPs().setValue(String.join(",", codee.getV().getPs().getValue(), "企业折旧已停用"));
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
                return result;
            } else {
                //校验资产来源是否匹配
                if (StrUtil.isNotBlank(depreciationConfig.getAssetOrigin())
                        && !depreciationConfig.getAssetOrigin().contains(asset.getAssetOrigin())) {
                    LuckySheetModel codee = importDto.getSheetModelList().get(0);
                    if (ObjectUtil.isNull(codee.getV().getPs())) {
                        LuckySheetModel.Comment comment = new LuckySheetModel.Comment("折旧方案使用资产来源和当前资产来源不匹配");
                        codee.getV().setPs(comment);
                        importDto.setErrorNum(importDto.getErrorNum() + 1);
                    } else {
                        codee.getV().getPs().setValue(String.join(",", codee.getV().getPs().getValue(), "折旧方案使用资产来源和当前资产来源不匹配"));
                        importDto.setErrorNum(importDto.getErrorNum() + 1);
                    }
                }

                //入账日期校验，依赖财务折旧配置，所以放在这里校验
                LocalDateTime billDate = null;
                try {
                    billDate = TimeUtil.parseTime(importDto.getBillDate(), "yyyy/M/d");
                } catch (Exception e) {
                    //
                }
                try {
                    billDate = TimeUtil.parseTime(importDto.getBillDate(), "yyyy-MM-dd");
                } catch (Exception e) {
                    //
                }
                try {
                    billDate = TimeUtil.parseTime(importDto.getBillDate(), "yyyy/M/d HH:mm:ss");
                } catch (Exception e) {
                    //
                }
                try {
                    billDate = TimeUtil.parseTime(importDto.getBillDate(), "yyyy-M-d HH:mm:ss");
                } catch (Exception e) {
                    //
                }
                if (Objects.isNull(billDate)) {
                    LuckySheetModel codee = importDto.getSheetModelList().get(7);
                    if (ObjectUtil.isNull(codee.getV().getPs())) {
                        LuckySheetModel.Comment comment = new LuckySheetModel.Comment("入账日期格式错误");
                        codee.getV().setPs(comment);
                        importDto.setErrorNum(importDto.getErrorNum() + 1);
                    }
                } else {
                    result.setBillDate(billDate);

                    //获取当前会计期间信息
                    int settleMonth = settleInfoService.queryCurrentSettleMonth(depreciationConfig.getOrgId(), depreciationConfig.getFirstSettleTime());
                    LocalDate settleDate = TimeUtil.transferTime(Integer.parseInt(settleMonth + "01"));
                    LocalDateTime settleDateFirstDay = settleDate.atStartOfDay();
                    LocalDateTime settleDateEndDay = settleDateFirstDay.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);

                    //校验入账日期
                    SettleTypeEnum settleTypeEnum = SettleTypeEnum.getByCode(result.getSettleType());
                    if (SettleTypeEnum.HISTORY.equals(settleTypeEnum)) {
                        //历史入账仅允许选当前期间之前的日期
                        if (result.getBillDate().isAfter(settleDateFirstDay)) {
                            LuckySheetModel codee = importDto.getSheetModelList().get(7);
                            if (ObjectUtil.isNull(codee.getV().getPs())) {
                                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("入账日期错误");
                                codee.getV().setPs(comment);
                                importDto.setErrorNum(importDto.getErrorNum() + 1);
                            }
                        }
                    } else {
                        //当期入账仅允许选当前期间的日期
                        if (result.getBillDate().isBefore(settleDateFirstDay) || result.getBillDate().isAfter(settleDateEndDay)) {
                            LuckySheetModel codee = importDto.getSheetModelList().get(7);
                            if (ObjectUtil.isNull(codee.getV().getPs())) {
                                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("入账日期错误");
                                codee.getV().setPs(comment);
                                importDto.setErrorNum(importDto.getErrorNum() + 1);
                            }
                        }
                    }
                }
            }

            //设置预计净残值
            result.setPreRemainingAmount(result.getOriginPrice().multiply(result.getPreResidualRate()).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));

            //校验预计使用期间数
            long preSettleCount;
            if (DepreciationStrategyEnum.AVERAGE.getCode().equals(depreciationConfig.getStrategy())) {
                preSettleCount = result.getOriginPrice().subtract(result.getPreRemainingAmount()).multiply(BigDecimal.valueOf(100)).longValue();
            } else {
                BigDecimal accumulationAmount = result.getAccumulationAmount();
                if (Objects.isNull(accumulationAmount)) {
                    accumulationAmount = BigDecimal.ZERO;
                }
                preSettleCount = result.getOriginPrice().subtract(result.getPreRemainingAmount()).subtract(accumulationAmount).multiply(BigDecimal.valueOf(100)).longValue();
            }
            if (result.getPreSettleCount() > preSettleCount) {
                LuckySheetModel codee = importDto.getSheetModelList().get(11);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("预计使用期间数过大");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }

            //校验累积折旧金额
            if (Objects.nonNull(result.getAccumulationAmount())
                    && result.getAccumulationAmount().compareTo(result.getOriginPrice().subtract(result.getPreRemainingAmount())) > 0) {
                LuckySheetModel codee = importDto.getSheetModelList().get(13);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("累计折旧金额错误");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }

            //折旧算法
            result.setStrategy(depreciationConfig.getStrategy());
        }

        //校验税额
        if (Objects.nonNull(result.getTax()) && result.getTax().compareTo(result.getOriginPrice()) > 0) {
            LuckySheetModel codee = importDto.getSheetModelList().get(9);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("可抵扣税额超过资产原始值");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }

        //校验已折旧期间数
        if (Objects.nonNull(result.getSettleCount())) {
            if (result.getSettleCount() > result.getPreSettleCount()) {
                LuckySheetModel codee = importDto.getSheetModelList().get(12);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("已折旧期间数大于预计使用期间数");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }

            //预计使用期间数和已使用期间数相同的时候，一开始入账就是折旧已提完的状态，并且设置上一期折旧状态为0，以防该被计提和结账
            if (Objects.equals(result.getSettleCount(), result.getPreSettleCount())) {
                result.setSettleStatus(AssetSettleStatusEnum.FINISH.getCode());
                result.setPreviousSettleStatus(0);
            }
        }

        //校验备注信息
        if (Objects.nonNull(result.getRemark()) && result.getRemark().length() > 200) {
            LuckySheetModel codee = importDto.getSheetModelList().get(14);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("备注最多支持200个字符");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }

        //设置一些前端不允许修改的固定值
        BigDecimal accumulationAmount = BigDecimal.ZERO;
        if (Objects.nonNull(result.getAccumulationAmount())) {
            accumulationAmount = result.getAccumulationAmount();
        }
        //净值
        result.setRemainingAmount(result.getOriginPrice().subtract(accumulationAmount));
        return result;
    }

    @Override
    public AssetFinanceInfoConfigDto queryByCode(String bizCode) {
        //查询入账信息
        AsAssetFinanceInfo financeInfo = getOne(Wrappers.lambdaQuery(AsAssetFinanceInfo.class)
                .eq(AsAssetFinanceInfo::getBizCode, bizCode)
                .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        if (Objects.isNull(financeInfo)) {
            return null;
        }

        AssetFinanceInfoConfigDto result = new AssetFinanceInfoConfigDto();
        BeanUtils.copyProperties(financeInfo, result);
        result.setSettleTypeDesc(SettleTypeEnum.getByCode(financeInfo.getSettleType()).getDesc());
        result.setStrategyName(DepreciationStrategyEnum.getByCode(financeInfo.getStrategy()).getDesc());
        result.setBillStatusDesc(AssetBillStatusEnum.getByCode(financeInfo.getBillStatus()).getDesc());
        result.setSettleStatusDesc(AssetSettleStatusEnum.getByCode(financeInfo.getSettleStatus()).getDesc());
        result.setBillDate(TimeUtil.transferTime(financeInfo.getBillDate()).atStartOfDay());
        result.setOrgName(cacheResourceUtil.getOrgName(financeInfo.getOrgId()));
        result.setSettleOrgName(cacheResourceUtil.getOrgName(financeInfo.getSettleOrgId()));
        return result;
    }

    @Override
    public Boolean edit(AssetFinanceInfoConfigDto configDto) {
        //校验参数
        verifyFinanceParam(configDto, Boolean.FALSE);

        //判断是否已存在当前企业当前资产的入账信息
        AsAssetFinanceInfo financeInfo = getOne(Wrappers.lambdaQuery(AsAssetFinanceInfo.class)
                .eq(AsAssetFinanceInfo::getOrgId, configDto.getOrgId())
                .eq(AsAssetFinanceInfo::getAssetId, configDto.getAssetId())
                .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        if (Objects.isNull(financeInfo)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产入账信息不存在");
        }
        //待折旧状态的资产才允许编辑
        if (!AssetSettleStatusEnum.WAIT.getCode().equals(financeInfo.getSettleStatus())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "非待折旧状态资产，不允许编辑入账信息");
        }

        //编辑保存入库
        AsAssetFinanceInfo modifyParam = new AsAssetFinanceInfo();
        BeanUtils.copyProperties(configDto, modifyParam);
        return update(modifyParam, Wrappers.lambdaUpdate(AsAssetFinanceInfo.class).eq(AsAssetFinanceInfo::getBizCode, financeInfo.getBizCode()));
    }

    @Override
    public Boolean alter(AssetFinanceInfoConfigDto configDto) {
        //校验参数
        verifyFinanceParam(configDto, Boolean.TRUE);

        //判断是否已存在当前企业当前资产的入账信息
        AsAssetFinanceInfo financeInfo = getOne(Wrappers.lambdaQuery(AsAssetFinanceInfo.class)
                .eq(AsAssetFinanceInfo::getOrgId, configDto.getOrgId())
                .eq(AsAssetFinanceInfo::getAssetId, configDto.getAssetId())
                .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        if (Objects.isNull(financeInfo)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产入账信息不存在");
        }
        //待折旧状态的资产才允许编辑
        if (!AssetSettleStatusEnum.SETTLEMENT.getCode().equals(financeInfo.getSettleStatus())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "非折旧中资产，不允许变更入账信息");
        }

        //变更保存入库
        AsAssetFinanceInfo modifyParam = new AsAssetFinanceInfo();
        BeanUtils.copyProperties(configDto, modifyParam);
        List<ComparisonDto> diffResult = CompareUtil.compareFields(financeInfo, modifyParam, AsAssetFinanceInfo.class);
        if (CollUtil.isNotEmpty(diffResult)) {
            String alterLogDetail = formatDiff(diffResult);
            AsAssetFinanceAlterLog alterLog = new AsAssetFinanceAlterLog();
            if (StrUtil.isNotBlank(alterLogDetail)) {
                alterLog.setAssetBillCode(financeInfo.getBizCode());
                alterLog.setDetail(alterLogDetail);
                //保存变更日志
                alterLogService.save(alterLog);
            }
        }
        return update(modifyParam, Wrappers.lambdaUpdate(AsAssetFinanceInfo.class).eq(AsAssetFinanceInfo::getBizCode, financeInfo.getBizCode()));
    }

    @Override
    public IPage<AssetFinanceAlterLogDto> pageAlterLog(AlterLogQueryDto queryDto) {
        return alterLogService.pageQuery(queryDto);
    }

    @Override
    public Boolean resetConfig(String bizCode) {
        //判断是否已存在当前企业当前资产的入账信息
        AsAssetFinanceInfo financeInfo = getOne(Wrappers.lambdaQuery(AsAssetFinanceInfo.class)
                .eq(AsAssetFinanceInfo::getBizCode, bizCode)
                .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        if (Objects.isNull(financeInfo)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产入账信息不存在");
        }

        //待折旧状态才允许反入账
        if (!AssetSettleStatusEnum.WAIT.getCode().equals(financeInfo.getSettleStatus())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产非待折旧状态，不允许反入账");
        }

        //添加操作日志
        AsDepreciationOperationLog operationLog = new AsDepreciationOperationLog();
        operationLog.setBizCode(bizCode)
                .setActionName("资产反入账").setActionMethod("resetConfig").setActionParam(JSONObject.toJSONString(bizCode))
                .setRemark("资产反入账").setCreateBy(LoginUserThreadLocal.getCurrentUserId()).setCreateTime(LocalDateTime.now());
        depreciationOperationLog.save(operationLog);

        //删除入账初始信息
        initFinanceInfoService.remove(Wrappers.lambdaQuery(AsAssetInitFinanceInfo.class).eq(AsAssetInitFinanceInfo::getBizCode, bizCode));

        //更改入账信息为反入账
        return update(Wrappers.lambdaUpdate(AsAssetFinanceInfo.class)
                .set(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.UN_CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getBizCode, bizCode)
                .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode()));
    }

    /**
     * 校验入账信息参数
     *
     * @param configDto
     * @param isAlter   变更不需要校验入账日期
     */
    private void verifyFinanceParam(AssetFinanceInfoConfigDto configDto, Boolean isAlter) {
        //校验入账类型
        if (SettleTypeEnum.UN_KNOW.equals(SettleTypeEnum.getByCode(configDto.getSettleType()))) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "入账类型错误");
        }

        //校验资产信息和资产所属组织信息是否存在，带有资产权限了
        AsAsset asset = assetService.getInfoPerm(configDto.getAssetId());
        if (Objects.isNull(asset) || Objects.isNull(asset.getAssetData().getLong("orgOwner"))) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产信息不存在");
        }

        //查询资产所属组织信息，组织可能为公司，也有可能是部门
        AsOrg org = orgService.getById(asset.getAssetData().getLong("orgOwner"));
        if (Objects.isNull(org)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产所属组织不存在");
        }
        long companyId = org.getCompanyOwner();

        //设置资产所属企业信息
        configDto.setOrgId(companyId);
        configDto.setAssetCode(asset.getAssetData().getString("assetCode"));
        configDto.setAssetCategory(StrUtil.isNotBlank(asset.getAssetData().getString("assetCategory")) ? Long.parseLong(asset.getAssetData().getString("assetCategory")) : null);

        //获取分摊部门
        AsOrg settleOrg = null;
        if (StrUtil.isNotBlank(configDto.getSettleOrgName())) {
            List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                    .eq(AsOrg::getCompanyOwner, companyId)
                    .eq(AsOrg::getOrgName, configDto.getSettleOrgName())
                    .eq(AsOrg::getIsDelete, 0));
            if (CollUtil.isNotEmpty(orgList)) {
                settleOrg = orgList.get(0);
                configDto.setSettleOrgId(settleOrg.getId());
            }
        } else {
            settleOrg = orgService.getById(configDto.getSettleOrgId());
        }

        //校验分摊部门
        if (Objects.isNull(settleOrg) || settleOrg.getOrgType() != 2) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "分摊部门不存在");
        }
        //校验分摊部门为当前企业下
        if (!settleOrg.getCompanyOwner().equals(configDto.getOrgId())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "分摊部门不属于当前公司");
        }

        //校验折旧方案是否已停用
        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, configDto.getOrgId())
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "企业折旧已停用");
        }

        //校验资产来源是否匹配
        if (StrUtil.isNotBlank(depreciationConfig.getAssetOrigin()) && !depreciationConfig.getAssetOrigin().contains(asset.getAssetOrigin())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案使用资产来源和当前资产来源不匹配");
        }

        //变更不需要校验入账日期，变更不能变更入账日期
        if (!isAlter) {
            //校验入账日期
            SettleTypeEnum settleTypeEnum = SettleTypeEnum.getByCode(configDto.getSettleType());
            //获取结算月份
            int settleMonth = settleInfoService.queryCurrentSettleMonth(depreciationConfig.getOrgId(), depreciationConfig.getFirstSettleTime());
            LocalDate settleDate = TimeUtil.transferTime(Integer.parseInt(settleMonth + "01"));
            LocalDateTime settleDateFirstDay = settleDate.atStartOfDay();
            LocalDateTime settleDateEndDay = settleDateFirstDay.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);

            if (SettleTypeEnum.HISTORY.equals(settleTypeEnum)) {
                //历史入账仅允许选当前期间之前的日期
                if (configDto.getBillDate().isAfter(settleDateFirstDay)) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "入账日期错误");
                }
            } else {
                //当期入账仅允许选当前期间的日期
                if (configDto.getBillDate().isBefore(settleDateFirstDay) || configDto.getBillDate().isAfter(settleDateEndDay)) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "入账日期错误");
                }
            }
        }

        //校验税额
        if (Objects.nonNull(configDto.getTax()) && configDto.getTax().compareTo(configDto.getOriginPrice()) > 0) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "可抵扣税额超过资产原始值");
        }

        //设置预计净残值
        configDto.setPreRemainingAmount(configDto.getOriginPrice().multiply(configDto.getPreResidualRate()).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));

        //校验预计使用期间数
        long preSettleCount;
        if (DepreciationStrategyEnum.AVERAGE.getCode().equals(depreciationConfig.getStrategy())) {
            preSettleCount = configDto.getOriginPrice().subtract(configDto.getPreRemainingAmount()).multiply(BigDecimal.valueOf(100)).longValue();
        } else {
            BigDecimal accumulationAmount = configDto.getAccumulationAmount();
            if (Objects.isNull(accumulationAmount)) {
                accumulationAmount = BigDecimal.ZERO;
            }
            preSettleCount = configDto.getOriginPrice().subtract(configDto.getPreRemainingAmount()).subtract(accumulationAmount).multiply(BigDecimal.valueOf(100)).longValue();
        }
        if (configDto.getPreSettleCount() > preSettleCount) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "预计使用期间数过大");
        }

        //校验已折旧期间数
        if (Objects.nonNull(configDto.getSettleCount())) {
            if (configDto.getSettleCount() > configDto.getPreSettleCount()) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "已折旧期间数大于预计使用期间数");
            }

            //预计使用期间数和已使用期间数相同的时候，一开始入账就是折旧已提完的状态，并且设置上一期折旧状态为0，以防该被计提和结账
            if (Objects.equals(configDto.getSettleCount(), configDto.getPreSettleCount())) {
                configDto.setSettleStatus(AssetSettleStatusEnum.FINISH.getCode());
                configDto.setPreviousSettleStatus(0);
            }
        }

        //校验累积折旧金额
        if (Objects.nonNull(configDto.getAccumulationAmount())
                && configDto.getAccumulationAmount().compareTo(configDto.getOriginPrice().subtract(configDto.getPreRemainingAmount())) > 0) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "累积折旧金额错误");
        }

        //校验备注信息
        if (Objects.nonNull(configDto.getRemark()) && configDto.getRemark().length() > 200) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "备注最多支持200个字符");
        }

        //设置一些前端不允许修改的固定值
        configDto.setStrategy(depreciationConfig.getStrategy());//折旧算法
        BigDecimal accumulationAmount = BigDecimal.ZERO;
        if (Objects.nonNull(configDto.getAccumulationAmount())) {
            accumulationAmount = configDto.getAccumulationAmount();
        }
        //净值
        configDto.setRemainingAmount(configDto.getOriginPrice().subtract(accumulationAmount));
    }

    /**
     * 格式化变更日志
     *
     * @param comparisonDtoList
     * @return
     */
    private String formatDiff(List<ComparisonDto> comparisonDtoList) {
        if (CollUtil.isEmpty(comparisonDtoList)) {
            return null;
        }

        StringBuilder result = new StringBuilder();
        for (ComparisonDto item : comparisonDtoList) {
            if (StrUtil.isBlank(item.getLater())) {
                continue;
            }
            String temp;
            switch (item.getLabel()) {
                case "所属公司":
                    AsCompany preCompany = companyService.getById(Long.valueOf(item.getPrevious()));
                    AsCompany oldCompany = companyService.getById(Long.valueOf(item.getLater()));
                    temp = String.format(ALTER_LOG_TEMPLATE, item.getLabel(), preCompany.getName(), oldCompany.getName());
                    result.append(temp).append(";");
                    break;
                case "分摊部门":
                    AsOrg preOrg = orgService.getById(Long.valueOf(item.getPrevious()));
                    AsOrg oldOrg = orgService.getById(Long.valueOf(item.getLater()));
                    temp = String.format(ALTER_LOG_TEMPLATE, item.getLabel(), preOrg.getOrgName(), oldOrg.getOrgName());
                    result.append(temp).append(";");
                    break;
                case "入账类型":
                    String preTypeDesc = SettleTypeEnum.getByCode(Integer.valueOf(item.getPrevious())).getDesc();
                    String oldTypeDesc = SettleTypeEnum.getByCode(Integer.valueOf(item.getLater())).getDesc();
                    temp = String.format(ALTER_LOG_TEMPLATE, item.getLabel(), preTypeDesc, oldTypeDesc);
                    result.append(temp).append(";");
                    break;
                case "折旧算法":
                    String preStrategyDesc = DepreciationStrategyEnum.getByCode(Integer.valueOf(item.getPrevious())).getDesc();
                    String oldStrategyDesc = DepreciationStrategyEnum.getByCode(Integer.valueOf(item.getLater())).getDesc();
                    temp = String.format(ALTER_LOG_TEMPLATE, item.getLabel(), preStrategyDesc, oldStrategyDesc);
                    result.append(temp).append(";");
                    break;
                default:
                    temp = String.format(ALTER_LOG_TEMPLATE, item.getLabel(), item.getPrevious(), item.getLater());
                    result.append(temp).append(";");
                    break;
            }
        }
        return result.toString();
    }

    @Override
    public List<AsAssetFinanceInfo> pageQuery(AssetFinanceInfoQueryDto request) {
        long start = (request.getCurrent() - 1) * request.getSize();
        return this.getBaseMapper().pageQuery(request, start, request.getSize());
    }

    @Override
    public Integer resetSettleData(Long companyId) {
        return this.getBaseMapper().resetSettleData(companyId);
    }

    @Override
    public List<Long> queryValidCompany() {
        return this.getBaseMapper().selectValidCompany();
    }

    @Override
    public Integer countCurrentAssetNum(Long companyId, Integer startDate, Integer endDate) {
        return this.getBaseMapper().countCurrentAssetNum(companyId, startDate, endDate);
    }

    @Override
    public Integer countHistoryAssetNum(Long companyId, LocalDateTime startDate, LocalDateTime endDate) {
        return this.getBaseMapper().countHistoryAssetNum(companyId, startDate, endDate);
    }

    @Override
    public Integer countPreMonthAssetNum(Long companyId, Integer startDate) {
        return this.getBaseMapper().countPreMonthAssetNum(companyId, startDate);
    }

    @Override
    public Integer countHandleAssetNum(Long companyId, LocalDateTime startTime, LocalDateTime endTime) {
        return this.getBaseMapper().countHandleAssetNum(companyId, startTime, endTime);
    }

    @Override
    public List<List<Object>> queryAssetWait(AssetFinanceInfoQueryDto queryDto) {
        //查询当前折旧方案配置
        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, queryDto.getOrgId())
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }

        //折旧参数配置
        List<DepreciationConfigParamDto> depreciationParamList = JSONObject.parseArray(depreciationConfig.getDepreciationParam(), DepreciationConfigParamDto.class);
        //组织名称
        String orgName = cacheResourceUtil.getOrgName(queryDto.getOrgId());
        //折旧策略
        String strategyDesc = DepreciationStrategyEnum.getByCode(depreciationConfig.getStrategy()).getDesc();
        //入账类型
        String settleTypeDesc = SettleTypeEnum.PERIOD.getDesc();
        //入账日期
        String billDateDesc = TimeUtil.formatTime(LocalDateTime.now(), "yyyy/MM/dd");

        List<List<Object>> rowDataList = new ArrayList<>();
        //查询待入账资产列表信息
        IPage<AsAsset> assetPage = innerQueryWaitAsset(depreciationConfig, queryDto);
        if (Objects.nonNull(assetPage) && CollUtil.isNotEmpty(assetPage.getRecords())) {
            for (AsAsset item : assetPage.getRecords()) {
                //转换成excel的数据行
                List<Object> rowData = transferExcelRow(item, orgName, strategyDesc, settleTypeDesc, billDateDesc, depreciationParamList);
                if (CollUtil.isNotEmpty(rowData)) {
                    rowDataList.add(rowData);
                }
            }
        }
        return rowDataList;
    }

    /**
     * 数据转换成excel行数据，参数有点多轻喷，为了for循环数据共用
     *
     * @param item
     * @param orgName
     * @param strategyDesc
     * @param settleTypeDesc
     * @param billDateDesc
     * @param depreciationParamList
     * @return
     */
    private List<Object> transferExcelRow(AsAsset item, String orgName, String strategyDesc, String settleTypeDesc, String billDateDesc, List<DepreciationConfigParamDto> depreciationParamList) {
        try {
            List<Object> rowData = new ArrayList<>();
            //资产编码
            rowData.add(item.getAssetCode());
            //资产名称
            rowData.add(item.getAssetData().getString("assetName"));
            //资产分类
            if (Objects.nonNull(item.getAssetData().getLong("assetCategory"))) {
                rowData.add(cacheResourceUtil.getCategoryName(item.getAssetData().getLong("assetCategory")));
            } else {
                rowData.add("");
            }
            //折旧算法
            rowData.add(strategyDesc);
            //组织名称
            rowData.add(orgName);
            //入账类型
            rowData.add(settleTypeDesc);

            //分摊部门
            String settleOrgName = null;
            String settleOrgCode = null;
            //资产使用组织不为空，且使用组织为部门类型
            if (Objects.nonNull(item.getAssetData().getLong("useOrg"))) {
                AsOrg useOrg = orgService.getById(item.getAssetData().getLong("useOrg"));
                if (Objects.nonNull(useOrg) && useOrg.getOrgType() == 2) {
                    settleOrgName = useOrg.getOrgName();
                    settleOrgCode = useOrg.getOrgCode();
                }
            }
            if (StrUtil.isBlank(settleOrgName)) {
                rowData.add("");
            } else {
                rowData.add(settleOrgName + "（" + settleOrgCode + "）");
            }
            //入账日期
            rowData.add(billDateDesc);
            //本币原值
            if (Objects.nonNull(item.getAssetData().getBigDecimal("price"))) {
                rowData.add(item.getAssetData().getBigDecimal("price"));
            } else {
                rowData.add(BigDecimal.ZERO);
            }
            //可抵扣税额
            rowData.add(BigDecimal.ZERO);
            //查询匹配的折旧参数
            Optional<DepreciationConfigParamDto> configParamDto = matchDepreciationParam(depreciationParamList, item.getAssetData().getString("assetCategory"));
            //预计残值率
            BigDecimal preResidualRate = BigDecimal.valueOf(5L);
            //预计使用期间数
            int preSettleCount = 36;
            if (configParamDto.isPresent()) {
                preResidualRate = configParamDto.get().getResidualRate();
                preSettleCount = configParamDto.get().getPreSettleCount();
            }
            if (Objects.nonNull(item.getAssetData().getInteger("useTimeLimit"))) {
                preSettleCount = item.getAssetData().getInteger("useTimeLimit");
            }
            rowData.add(preResidualRate);
            rowData.add(preSettleCount);
            //已折旧期间数
            rowData.add(0);
            //累积折旧
            rowData.add(BigDecimal.ZERO);
            //备注
            rowData.add("");
            return rowData;
        } catch (Exception e) {
            log.error("assetFinanceInfoService transferExcelRow error! assetId=[{}] exception ", item.getId(), e);
            return null;
        }
    }

    @Override
    public PageUtils<AssetFinanceInfoDto> queryWaitFinanceAsset(AssetFinanceInfoQueryDto queryDto) {
        //查询当前折旧方案配置
        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, queryDto.getOrgId())
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }

        //查询待入账资产数据
        IPage<AsAsset> assetPageResult = innerQueryWaitAsset(depreciationConfig, queryDto);
        if (Objects.isNull(assetPageResult) || CollUtil.isEmpty(assetPageResult.getRecords())) {
            return new PageUtils<>(null, 0, 1, 20);
        }
        List<AssetFinanceInfoDto> dataList = new ArrayList<>();
        for (AsAsset item : assetPageResult.getRecords()) {
            AssetFinanceInfoDto assetFinanceInfoDto = JSONObject.parseObject(item.getAssetData().toJSONString(), AssetFinanceInfoDto.class);
            assetFinanceInfoDto.setAssetId(item.getId());
            assetFinanceInfoDto.setStatus(item.getStatus());
            assetFinanceInfoDto.setStatusText(cacheResourceUtil.getAssetStatusName(assetFinanceInfoDto.getStatus().longValue()));
            if (StrUtil.isNotBlank(assetFinanceInfoDto.getAssetCategory())) {
                assetFinanceInfoDto.setAssetCategoryText(cacheResourceUtil.getCategoryName(Long.valueOf(assetFinanceInfoDto.getAssetCategory())));
            }

            if (StrUtil.isNotBlank(assetFinanceInfoDto.getStorageArea())) {
                assetFinanceInfoDto.setStorageAreaText(cacheResourceUtil.getAreaName(Long.valueOf(assetFinanceInfoDto.getStorageArea())));
            }

            if (StrUtil.isNotBlank(assetFinanceInfoDto.getOrgOwner())) {
                assetFinanceInfoDto.setOrgOwnerText(cacheResourceUtil.getOrgName(Long.valueOf(assetFinanceInfoDto.getOrgOwner())));
            }

            if (StrUtil.isNotBlank(assetFinanceInfoDto.getUseOrg())) {
                assetFinanceInfoDto.setUseOrgText(cacheResourceUtil.getOrgName(Long.valueOf(assetFinanceInfoDto.getUseOrg())));
            }
            assetFinanceInfoDto.setCreateTime(item.getCreateTime());
            //待入账列表这里都是待入账状态
            assetFinanceInfoDto.setBillStatusText(AssetFinanceStatusEnum.WAIT.getDesc());
            //待入账列表没有折旧状态
            assetFinanceInfoDto.setSettleStatusText("-");
            dataList.add(assetFinanceInfoDto);
        }
        return new PageUtils<>(dataList, (int) assetPageResult.getTotal(), (int) assetPageResult.getSize(), (int) assetPageResult.getCurrent());
    }

    /**
     * 查询待入账资产数据
     *
     * @param depreciationConfig
     * @param queryDto
     * @return
     */
    private IPage<AsAsset> innerQueryWaitAsset(AsAssetDepreciationConfig depreciationConfig, AssetFinanceInfoQueryDto queryDto) {
        //查询已入账或反入账的资产信息
        List<Long> existAssetIdList = this.getBaseMapper().selectAssetId(queryDto.getOrgId(), Arrays.asList(AssetFinanceStatusEnum.UN_CREDITED.getCode(), AssetFinanceStatusEnum.CREDITED.getCode()));
        if (CollUtil.isNotEmpty(existAssetIdList)) {
            //排除已入账的资产信息
            queryDto.setExcludeAssetIds(existAssetIdList);
        }

        //所属组织为空时，需要查询这个企业下所有的资产信息
        if (CollUtil.isEmpty(queryDto.getOrgOwner())) {
            List<String> orgOwner = new ArrayList<>();
            orgOwner.add(String.valueOf(queryDto.getOrgId()));

            //查询这个企业下所有的部门信息
            List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                    .select(AsOrg::getId)
                    .eq(AsOrg::getCompanyOwner, queryDto.getOrgId()).eq(AsOrg::getIsDelete, 0));
            if (CollUtil.isNotEmpty(orgList)) {
                orgOwner.addAll(orgList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.toList()));
            }
            //设置资产所属组织查询条件
            queryDto.setOrgOwner(orgOwner);
        }

        //待入账资产没有本币范围这个查询条件，所以需要设置为null，以防mapper查询报错
        queryDto.setOriginPrice(null);

        //资产权限过滤条件
        String permissionCondition = conditionResolver.getPermsSql("a");
        queryDto.setPermissionCondition(permissionCondition);
        //待入账资产需要根据折旧方案资产价值进行过滤
        queryDto.setWaitAssetPrice(depreciationConfig.getAssetPrice());
        //待入账资产需要根据折旧方案资产适用来源进行过滤
        if (StrUtil.isNotBlank(depreciationConfig.getAssetOrigin())) {
            queryDto.setWaitAssetOrigin(Arrays.asList(depreciationConfig.getAssetOrigin().split(",")));
        }

        //用租户过滤下
        queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        return this.getBaseMapper().selectWaitFinanceAsset(queryDto.buildIPage(), queryDto);
    }

    @Override
    public PageUtils<AssetFinanceInfoDto> queryFinanceInfo(AssetFinanceInfoQueryDto queryDto) {
        if (Objects.isNull(queryDto.getBillStatus())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产入账状态不能为空");
        }
        if (AssetFinanceStatusEnum.WAIT.getCode().equals(queryDto.getBillStatus())) {
            //待入账资产数据查询的是as_asset表，这些条件添加会导致查询语句报错，这里设置下为空
            queryDto.setBillStatus(null);
            queryDto.setSettleStatus(null);
            queryDto.setBillDate(null);
            return queryWaitFinanceAsset(queryDto);
        } else {
            //所属组织为空时，需要查询这个企业下所有的资产信息
            if (CollUtil.isEmpty(queryDto.getOrgOwner())) {
                List<String> orgOwner = new ArrayList<>();
                orgOwner.add(String.valueOf(queryDto.getOrgId()));

                //查询这个企业下所有的部门信息
                List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId)
                        .eq(AsOrg::getCompanyOwner, queryDto.getOrgId()).eq(AsOrg::getIsDelete, 0));
                if (CollUtil.isNotEmpty(orgList)) {
                    orgOwner.addAll(orgList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.toList()));
                }
                //设置资产所属组织查询条件
                queryDto.setOrgOwner(orgOwner);
            }

            //处理入账日期参数
            if (CollUtil.isNotEmpty(queryDto.getBillDate())) {
                List<Integer> billDateInt = new ArrayList<>();
                for (String billDate : queryDto.getBillDate()) {
                    billDateInt.add(Integer.valueOf(billDate.replace("-", "")));
                }
                queryDto.setBillDateInt(billDateInt);
            }

            //资产权限过滤条件
            String permissionCondition = conditionResolver.getPermsSql("a");
            queryDto.setPermissionCondition(permissionCondition);

            IPage<AssetFinanceInfoDto> financeInfoPage = this.getBaseMapper().selectAssetFinance(queryDto.buildIPage(), queryDto);
            if (Objects.isNull(financeInfoPage) || CollUtil.isEmpty(financeInfoPage.getRecords())) {
                return new PageUtils<>(null, 0, 1, 20);
            }

            List<AssetFinanceInfoDto> dataList = new ArrayList<>();
            for (AssetFinanceInfoDto item : financeInfoPage.getRecords()) {
                AssetFinanceInfoDto assetFinanceInfoDto = JSONObject.parseObject(item.getAssetData().toJSONString(), AssetFinanceInfoDto.class);
                assetFinanceInfoDto.setStatus(item.getStatus());
                assetFinanceInfoDto.setStatusText(cacheResourceUtil.getAssetStatusName(assetFinanceInfoDto.getStatus().longValue()));
                if (StrUtil.isNotBlank(assetFinanceInfoDto.getAssetCategory())) {
                    assetFinanceInfoDto.setAssetCategoryText(cacheResourceUtil.getCategoryName(Long.valueOf(assetFinanceInfoDto.getAssetCategory())));
                }

                if (StrUtil.isNotBlank(assetFinanceInfoDto.getStorageArea())) {
                    assetFinanceInfoDto.setStorageAreaText(cacheResourceUtil.getAreaName(Long.valueOf(assetFinanceInfoDto.getStorageArea())));
                }

                if (StrUtil.isNotBlank(assetFinanceInfoDto.getOrgOwner())) {
                    assetFinanceInfoDto.setOrgOwnerText(cacheResourceUtil.getOrgName(Long.valueOf(assetFinanceInfoDto.getOrgOwner())));
                }

                if (StrUtil.isNotBlank(assetFinanceInfoDto.getUseOrg())) {
                    assetFinanceInfoDto.setUseOrgText(cacheResourceUtil.getOrgName(Long.valueOf(assetFinanceInfoDto.getUseOrg())));
                }
                assetFinanceInfoDto.setAssetId(item.getAssetId());
                assetFinanceInfoDto.setBizCode(item.getBizCode());
                assetFinanceInfoDto.setBillStatus(item.getBillStatus());
                assetFinanceInfoDto.setBillStatusText(AssetFinanceStatusEnum.getByCode(item.getBillStatus()).getDesc());
                assetFinanceInfoDto.setSettleStatus(item.getSettleStatus());
                assetFinanceInfoDto.setSettleStatusText(AssetSettleStatusEnum.getByCode(item.getSettleStatus()).getDesc());
                assetFinanceInfoDto.setCreateTime(item.getCreateTime());
                dataList.add(assetFinanceInfoDto);
            }
            return new PageUtils<>(dataList, (int) financeInfoPage.getTotal(), (int) financeInfoPage.getSize(), (int) financeInfoPage.getCurrent());
        }
    }

    @Override
    public PageUtils<AssetInitFinanceInfoDto> queryInitFinanceInfo(AssetFinanceInfoQueryDto queryDto) {
        //所属组织为空时，需要查询这个企业下所有的资产信息，有所属组织时，直接查询所属组织对应的资产信息
        if (CollUtil.isEmpty(queryDto.getOrgOwner())) {
            List<String> orgOwner = new ArrayList<>();
            orgOwner.add(String.valueOf(queryDto.getOrgId()));

            //查询这个企业下所有的部门信息
            List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                    .select(AsOrg::getId)
                    .eq(AsOrg::getCompanyOwner, queryDto.getOrgId()).eq(AsOrg::getIsDelete, 0));
            if (CollUtil.isNotEmpty(orgList)) {
                orgOwner.addAll(orgList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.toList()));
            }
            //设置资产所属组织查询条件
            queryDto.setOrgOwner(orgOwner);
        }

        //处理入账日期参数
        if (CollUtil.isNotEmpty(queryDto.getBillDate())) {
            List<Integer> billDateInt = new ArrayList<>();
            for (String billDate : queryDto.getBillDate()) {
                billDateInt.add(Integer.valueOf(billDate.replace("-", "")));
            }
            queryDto.setBillDateInt(billDateInt);
        }

        //资产权限过滤条件
        String permissionCondition = conditionResolver.getPermsSql("a");
        queryDto.setPermissionCondition(permissionCondition);

        IPage<AssetInitFinanceInfoDto> financeInfoPage = this.getBaseMapper().selectAssetInitFinance(queryDto.buildIPage(), queryDto);
        if (Objects.isNull(financeInfoPage) || CollUtil.isEmpty(financeInfoPage.getRecords())) {
            return new PageUtils<>(null, 0, 1, 20);
        }

        List<AssetInitFinanceInfoDto> dataList = new ArrayList<>();
        for (AssetInitFinanceInfoDto item : financeInfoPage.getRecords()) {
            AssetInitFinanceInfoDto assetInitFinanceInfo = new AssetInitFinanceInfoDto();
            BeanUtils.copyProperties(item, assetInitFinanceInfo);
            if (StrUtil.isNotEmpty(item.getAssetData().getString("assetPhoto"))) {
                assetInitFinanceInfo.setAssetPhoto(item.getAssetData().getJSONArray("assetPhoto").toJavaList(String.class));
            }
            assetInitFinanceInfo.setAssetName(item.getAssetData().getString("assetName"));
            assetInitFinanceInfo.setBrand(item.getAssetData().getString("brand"));
            assetInitFinanceInfo.setModel(item.getAssetData().getString("model"));
            assetInitFinanceInfo.setPrice(item.getAssetData().getBigDecimal("price"));
            assetInitFinanceInfo.setAssetOrigin(item.getAssetData().getString("assetOrigin"));
            if (Objects.nonNull(item.getAssetData().getLong("buyTime"))) {
                Long buyTime = item.getAssetData().getLong("buyTime");
                assetInitFinanceInfo.setBuyTime(TimeUtil.formatTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(buyTime), ZoneId.systemDefault()), "yyyy-MM-dd"));
            }
            assetInitFinanceInfo.setOrgOwner(item.getAssetData().getString("orgOwner"));
            assetInitFinanceInfo.setAssetCategory(item.getAssetData().getString("assetCategory"));

            assetInitFinanceInfo.setStatus(item.getStatus());
            assetInitFinanceInfo.setStatusText(cacheResourceUtil.getAssetStatusName(assetInitFinanceInfo.getStatus().longValue()));
            if (StrUtil.isNotBlank(assetInitFinanceInfo.getAssetCategory())) {
                assetInitFinanceInfo.setAssetCategoryText(cacheResourceUtil.getCategoryName(Long.valueOf(assetInitFinanceInfo.getAssetCategory())));
            }
            if (StrUtil.isNotBlank(assetInitFinanceInfo.getOrgOwner())) {
                assetInitFinanceInfo.setOrgOwnerText(cacheResourceUtil.getOrgName(Long.valueOf(assetInitFinanceInfo.getOrgOwner())));
            }
            if (Objects.nonNull(item.getOrgId())) {
                assetInitFinanceInfo.setOrgName(cacheResourceUtil.getOrgName(item.getOrgId()));
            }
            if (Objects.nonNull(item.getSettleOrgId())) {
                assetInitFinanceInfo.setSettleOrgName(cacheResourceUtil.getOrgName(item.getSettleOrgId()));
            }
            assetInitFinanceInfo.setStrategyDesc(DepreciationStrategyEnum.getByCode(item.getStrategy()).getDesc());
            assetInitFinanceInfo.setBillDateDesc(TimeUtil.formatTime(TimeUtil.transferTime(item.getBillDate()), "yyyy-MM-dd"));
            assetInitFinanceInfo.setBillMonth(Integer.parseInt(String.valueOf(item.getBillDate()).substring(0, 6)));
            assetInitFinanceInfo.setSettleTypeDesc(SettleTypeEnum.getByCode(item.getSettleType()).getDesc());
            assetInitFinanceInfo.setAssetData(null);
            dataList.add(assetInitFinanceInfo);
        }
        return new PageUtils<>(dataList, (int) financeInfoPage.getTotal(), (int) financeInfoPage.getSize(), (int) financeInfoPage.getCurrent());
    }

    @Override
    public PageUtils<AssetMachineAccountDto> queryMachineAccount(AssetFinanceInfoQueryDto queryDto) {
        //所属组织为空时，需要查询这个企业下所有的资产信息，有所属组织时，直接查询所属组织对应的资产信息
        if (CollUtil.isEmpty(queryDto.getOrgOwner())) {
            List<String> orgOwner = new ArrayList<>();
            orgOwner.add(String.valueOf(queryDto.getOrgId()));

            //查询这个企业下所有的部门信息
            List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                    .select(AsOrg::getId)
                    .eq(AsOrg::getCompanyOwner, queryDto.getOrgId()).eq(AsOrg::getIsDelete, 0));
            if (CollUtil.isNotEmpty(orgList)) {
                orgOwner.addAll(orgList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.toList()));
            }
            //设置资产所属组织查询条件
            queryDto.setOrgOwner(orgOwner);
        }

        //前端只能传单个折旧状态，这边做下转换，方便mapper共用条件
        if (Objects.nonNull(queryDto.getSingleSettleStatus())) {
            queryDto.setSettleStatus(Collections.singletonList(queryDto.getSingleSettleStatus()));
        }

        //台账都是查询已入账的资产入账信息
        queryDto.setBillStatus(AssetFinanceStatusEnum.CREDITED.getCode());

        //资产权限过滤条件
        String permissionCondition = conditionResolver.getPermsSql("a");
        queryDto.setPermissionCondition(permissionCondition);

        IPage<AssetMachineAccountDto> financeInfoPage = this.getBaseMapper().selectMachineAccount(queryDto.buildIPage(), queryDto);
        if (Objects.isNull(financeInfoPage) || CollUtil.isEmpty(financeInfoPage.getRecords())) {
            return new PageUtils<>(null, 0, 1, 20);
        }

        List<AssetMachineAccountDto> dataList = new ArrayList<>();
        for (AssetMachineAccountDto item : financeInfoPage.getRecords()) {
            AssetMachineAccountDto machineAccountDto = JSONObject.parseObject(item.getAssetData().toJSONString(), AssetMachineAccountDto.class);
            BeanUtils.copyProperties(item, machineAccountDto);

            machineAccountDto.setAssetName(item.getAssetData().getString("assetName"));
            machineAccountDto.setModel(item.getAssetData().getString("model"));
            machineAccountDto.setAssetCategory(item.getAssetData().getString("assetCategory"));

            machineAccountDto.setStatus(item.getStatus());
            machineAccountDto.setStatusText(cacheResourceUtil.getAssetStatusName(machineAccountDto.getStatus().longValue()));
            if (StrUtil.isNotBlank(machineAccountDto.getAssetCategory())) {
                machineAccountDto.setAssetCategoryText(cacheResourceUtil.getCategoryName(Long.valueOf(machineAccountDto.getAssetCategory())));
            }
            if (Objects.nonNull(item.getOrgId())) {
                machineAccountDto.setOrgName(cacheResourceUtil.getOrgName(item.getOrgId()));
            }
            if (Objects.nonNull(item.getSettleOrgId())) {
                machineAccountDto.setSettleOrgName(cacheResourceUtil.getOrgName(item.getSettleOrgId()));
            }
            machineAccountDto.setSettleStatusText(AssetSettleStatusEnum.getByCode(item.getSettleStatus()).getDesc());
            machineAccountDto.setAssetData(null);
            dataList.add(machineAccountDto);
        }
        return new PageUtils<>(dataList, (int) financeInfoPage.getTotal(), (int) financeInfoPage.getSize(), (int) financeInfoPage.getCurrent());
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importErrorDto.getTaskId());
        importError.setErrorNum(0);
        importError.setImportType(DictConstant.IMPORT_TYPE_ASSET_FINANCE);
        importError.setExcelJson(importErrorDto.getHeadModelList());
        assetImportErrorService.save(importError);
    }

    @Override
    public Boolean alterAssetOrgOwner(AssetAttributeAlterMessageDto messageDto) {
        AsOrg assetOrg = orgService.getById(messageDto.getOrgId());
        if (Objects.isNull(assetOrg)) {
            return false;
        }

        AsOrg beforeOrg = orgService.getById(messageDto.getBeforeOrgOwner());
        if (Objects.isNull(beforeOrg)) {
            return false;
        }

        AsOrg afterOrg = orgService.getById(messageDto.getAfterOrgOwner());
        if (Objects.isNull(afterOrg)) {
            return false;
        }

        //如果资产所属组织，修改前和修改后都是同一个企业，不做处理
        if (beforeOrg.getCompanyOwner().equals(afterOrg.getCompanyOwner())) {
            return Boolean.TRUE;
        }

        //资产由组织A 转移到 组织B
        //需要把资产在 组织A 的入账信息设置为折旧终止
        //同时，如果资产在组织B下有入账信息的时候，需要设置为待入账和待折旧状态，需要删除掉入账信息
        update(Wrappers.lambdaUpdate(AsAssetFinanceInfo.class)
                .set(AsAssetFinanceInfo::getSettleStatus, AssetSettleStatusEnum.STOP.getCode())
                .eq(AsAssetFinanceInfo::getOrgId, assetOrg.getCompanyOwner())
                .eq(AsAssetFinanceInfo::getAssetId, messageDto.getAssetId())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));

        //如果资产在组织B下有入账信息的时候，需要设置为待入账和待折旧状态，需要删除掉入账信息
        update(Wrappers.lambdaUpdate(AsAssetFinanceInfo.class)
                .set(AsAssetFinanceInfo::getIsDelete, 1)
                .eq(AsAssetFinanceInfo::getOrgId, afterOrg.getCompanyOwner())
                .eq(AsAssetFinanceInfo::getAssetId, messageDto.getAssetId())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        return Boolean.TRUE;
    }

    @Override
    public Boolean handleAsset(AssetAttributeAlterMessageDto messageDto) {
        AsOrg assetOrg = orgService.getById(messageDto.getOrgId());
        if (Objects.isNull(assetOrg)) {
            return false;
        }

        //资产被处置，设置处置时间
        update(Wrappers.lambdaUpdate(AsAssetFinanceInfo.class)
                .set(AsAssetFinanceInfo::getHandleTime, messageDto.getHandlerTime())
                .eq(AsAssetFinanceInfo::getOrgId, assetOrg.getCompanyOwner())
                .eq(AsAssetFinanceInfo::getAssetId, messageDto.getAssetId())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        return Boolean.TRUE;
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<AsAssetImportError> list = assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, taskId)
                .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_ASSET_FINANCE));
        return list.stream().map(AsAssetImportError::getExcelJson)
                .collect(Collectors.toList());
    }

    @Override
    public Integer countAssetFinanceByOrgId(List<Long> orgIds) {
        return Convert.toInt(this.count(new QueryWrapper<AsAssetFinanceInfo>().lambda()
                .in(AsAssetFinanceInfo::getSettleOrgId, orgIds)
                .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getIsDelete, 0)));
    }
}

