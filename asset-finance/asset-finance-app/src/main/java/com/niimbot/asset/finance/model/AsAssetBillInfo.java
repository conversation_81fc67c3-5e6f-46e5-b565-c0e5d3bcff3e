package com.niimbot.asset.finance.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_asset_bill_info
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_asset_bill_info", autoResultMap = true)
public class AsAssetBillInfo implements Serializable {

    private static final long serialVersionUID = -7374377844213536566L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属组织
     */
    private Long orgId;

    /**
     * 会计期间
     */
    private Integer settleMonth;

    /**
     * 实际折旧日期
     */
    private Integer settleDate;

    /**
     * 结账日期
     */
    private Integer billDate;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否删除 0-否  1-是
     */
    private Integer isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}