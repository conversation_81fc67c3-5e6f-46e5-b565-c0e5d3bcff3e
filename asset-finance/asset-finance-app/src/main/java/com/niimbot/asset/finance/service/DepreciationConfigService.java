package com.niimbot.asset.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.finance.DisableDepreciationConfigDto;
import com.niimbot.finance.DepreciationConfigDto;
import com.niimbot.asset.finance.model.AsAssetDepreciationConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/6 下午6:01
 */
public interface DepreciationConfigService extends IService<AsAssetDepreciationConfig> {

    /**
     * 保存折旧方案
     * @param configDto
     * @return
     */
    Boolean saveConfig(DepreciationConfigDto configDto);

    /**
     * 修改折旧方案
     * @param configDto
     * @return
     */
    Boolean editConfig(DepreciationConfigDto configDto);


    /**
     * 查询折旧方案配置
     * @param orgId
     * @return
     */
    DepreciationConfigDto queryByOrgId(Long orgId);

    /**
     * 获取验证码
     * @return
     */
    Boolean verificationCode();

    /**
     * 停用折旧方案
     * @param configDto
     * @return
     */
    Boolean removeConfig(DisableDepreciationConfigDto configDto);

    /**
     * 根据组织id校验折旧方案配置是否正常启用中
     * @param orgId
     */
    void verifyConfig(Long orgId);

    /**
     * 查询配置有折旧方案的企业信息
     * @return
     */
    List<Long> queryAllOrgId();

    /**
     * 查询最小启用时间
     * @param orgIdList
     * @return
     */
    Integer queryMinSettleTime(List<Long> orgIdList);

    /**
     * 根据orgId批量查询折旧配置数量
     * @param orgIds
     * @return
     */
    Integer queryConfigByOrg(List<Long> orgIds);
}
