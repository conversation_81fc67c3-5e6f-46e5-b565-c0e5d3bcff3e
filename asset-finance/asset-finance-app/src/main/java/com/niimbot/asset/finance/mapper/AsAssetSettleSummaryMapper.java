package com.niimbot.asset.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.finance.SettleSummaryDeptDto;
import com.niimbot.finance.SettleSummaryDto;
import com.niimbot.finance.SettleSummaryQueryDto;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;
import org.apache.ibatis.annotations.Param;

public interface AsAssetSettleSummaryMapper extends BaseMapper<AsAssetSettleSummary> {

    /**
     * 分页查询折旧汇总数据--所属公司维度
     * @param page
     * @param queryDto
     * @return
     */
    IPage<SettleSummaryDto> pageQuery(IPage<SettleSummaryQueryDto> page, @Param("param")SettleSummaryQueryDto queryDto);

    /**
     * 分页查询折旧汇总--分摊部门维度
     * @param page
     * @param queryDto
     * @return
     */
    IPage<SettleSummaryDeptDto> pageQueryDept(IPage<SettleSummaryQueryDto> page, @Param("param")SettleSummaryQueryDto queryDto);
}