package com.niimbot.asset.finance.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * as_asset_init_finance_info
 * <AUTHOR>
@Data
@TableName(value = "as_asset_init_finance_info", autoResultMap = true)
public class AsAssetInitFinanceInfo implements Serializable {

    private static final long serialVersionUID = -7445109286062420348L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 所属组织
     */
    private Long orgId;

    /**
     * 分摊部门
     */
    private Long settleOrgId;

    /**
     * 资产id
     */
    private Long assetId;

    /**
     * 折旧算法
     */
    private Integer strategy;

    /**
     * 入账类型
     */
    private Integer settleType;

    /**
     * 入账日期
     */
    private Integer billDate;

    /**
     * 本币原值
     */
    private BigDecimal originPrice;

    /**
     * 可抵扣税额
     */
    private BigDecimal tax;

    /**
     * 残值率
     */
    private BigDecimal preResidualRate;

    /**
     * 预计净残值
     */
    private BigDecimal preRemainingAmount;

    /**
     * 预计使用期间数
     */
    private Integer preSettleCount;

    /**
     * 已折旧次数
     */
    private Integer settleCount;

    /**
     * 累积折旧金额
     */
    private BigDecimal accumulationAmount;

    /**
     * 净值
     */
    private BigDecimal remainingAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资产入账状态
     */
    private Integer billStatus;

    /**
     * 资产折旧状态
     */
    private Integer settleStatus;

    /**
     * 资产处置时间
     */
    private LocalDateTime handleTime;

    /**
     * 是否删除 0-否  1-是
     */
    private Integer isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}