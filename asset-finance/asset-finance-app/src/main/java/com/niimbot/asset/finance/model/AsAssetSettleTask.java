package com.niimbot.asset.finance.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_asset_settle_task
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_asset_settle_task", autoResultMap = true)
public class AsAssetSettleTask implements Serializable {

    private static final long serialVersionUID = -7851836275647625092L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业id
     */
    private Long orgId;

    /**
     * 企业名称
     */
    private String orgName;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务参数
     */
    private String taskParameters;

    /**
     * 任务状态（-1：执行失败 0：未执行 1：执行中 2：执行成功）
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 耗时(毫秒)
     */
    private Long totalCost;

    /**
     * 任务调度执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 执行次数
     */
    private Integer handleCount;

    /**
     * 是否删除 0-否  1-是
     */
    private Integer isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}