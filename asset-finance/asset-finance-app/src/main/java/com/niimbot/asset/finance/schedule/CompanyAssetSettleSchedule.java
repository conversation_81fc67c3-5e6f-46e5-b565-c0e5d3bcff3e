package com.niimbot.asset.finance.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.util.concurrent.*;
import com.niimbot.asset.finance.constants.DistributeLockConstant;
import com.niimbot.asset.finance.enums.SettleTaskStatusEnum;
import com.niimbot.asset.finance.enums.SettleTaskTypeEnum;
import com.niimbot.asset.finance.model.AsAssetSettleTask;
import com.niimbot.asset.finance.service.AssetBillInfoService;
import com.niimbot.asset.finance.service.AssetSettleTaskService;
import com.niimbot.asset.finance.service.WarningMessageService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.finance.AssetDepreciationCalculateDto;
import com.niimbot.finance.SettleTaskQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 企业折旧计提和结账任务调度执行，单机执行，效率会有问题
 * <AUTHOR>
 * @date 2023/2/13 下午1:57
 */
@Slf4j
@Component
public class CompanyAssetSettleSchedule {
    private static final ScheduledExecutorService TIMEOUT_SCHEDULER_EXECUTOR = Executors.newScheduledThreadPool(10,
            new ThreadFactoryBuilder().setDaemon(true).setNameFormat("TimeoutScheduler-%d").build());
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private AssetSettleTaskService settleTaskService;
    @Autowired
    private AssetBillInfoService billInfoService;
    @Autowired
    private WarningMessageService messageService;

    @PostConstruct
    public void init() {
        AssetThreadPoolExecutorManager.newThreadPool(SettleTaskTypeEnum.COMPANY_DEPRECIATION.getTaskBizCode(), 10, 10, 10000);
        AssetThreadPoolExecutorManager.newThreadPool(SettleTaskTypeEnum.COMPANY_SETTLE.getTaskBizCode(), 10, 10, 10000);
        AssetThreadPoolExecutorManager.newThreadPool(SettleTaskTypeEnum.MONTH_SETTLE.getTaskBizCode(), 5, 10, 10000);
    }

//    @Scheduled(cron = "*/7 * * * * ?")
    @Async("asyncScheduledThreadPool")
    public void scheduleTask() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

//        log.info("companyAssetSettleSchedule scheduleTask start!");
        //获取分布式锁，这里主要是控制任务的并发，仅允许一台机器拉取任务开始调度，防止同一个任务被多台机器执行
        RLock lock = redissonClient.getLock(DistributeLockConstant.COMPANY_SETTLE_TASK);
        boolean isLocked;
        try {
            isLocked = lock.tryLock(0, 1, TimeUnit.MINUTES);
            if (!isLocked) {
                log.info("companyAssetSettleSchedule run end! Acquire lock fail! currentTime=[{}]", System.currentTimeMillis());
                return;
            }

            //调度-分发-提交任务
            distributeTask();

//            log.info("companyAssetSettleSchedule run companySettle start!");
        } catch (Exception e) {
            log.error("companyAssetSettleSchedule run acquire lock error! exception ", e);
        } finally {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 分发待执行的资产折旧任务
     */
    public void distributeTask() {
//        log.info("companyAssetSettleSchedule distributeTask start!");
//        long startTime = System.currentTimeMillis();
        try {
            //查询今天创建的待执行任务
            SettleTaskQueryDto queryTaskParam = new SettleTaskQueryDto();
            queryTaskParam.setStartTime(LocalDate.now().atStartOfDay());
            queryTaskParam.setStatus(SettleTaskStatusEnum.INIT.getStatusCode());
            List<AsAssetSettleTask> waitExecuteTaskList = settleTaskService.queryWaitExecuteTask(queryTaskParam);
            if (CollUtil.isEmpty(waitExecuteTaskList)) {
//                log.info("companyAssetSettleSchedule distributeTask end! wait execute task empty! costTime=[{}]", System.currentTimeMillis() - startTime);
                return;
            }

            //提交任务，这里没有做任务流控，存在问题
            commitTask(waitExecuteTaskList);
        } catch (Exception e) {
            log.error("companyAssetSettleSchedule distributeTask acquire lock error! exception ", e);
        }
//        log.info("companyAssetSettleSchedule distributeTask end! All data processed! costTime=[{}]", System.currentTimeMillis() - startTime);
    }

    /**
     * 提交待执行任务，并修改任务执行状态
     * @param waitExecuteTaskList
     */
    private void commitTask(List<AsAssetSettleTask> waitExecuteTaskList) {
        for (AsAssetSettleTask item : waitExecuteTaskList) {
            log.info("companyAssetSettleSchedule commitTask taskId=[{}]", item.getId());
            //先更新任务状态，后提交任务执行，以防还没有提交又被调度起来了，但是存在修改了状态没有提交执行，导致任务没有被实际调度执行
            settleTaskService.update(Wrappers.lambdaUpdate(AsAssetSettleTask.class)
                    .set(AsAssetSettleTask::getStatus, SettleTaskStatusEnum.EXECUTE.getStatusCode())
                    .set(AsAssetSettleTask::getExecuteTime, LocalDateTime.now()).eq(AsAssetSettleTask::getId, item.getId()));
            log.info("companyAssetSettleSchedule commitTask update task status execute! taskId=[{}]", item.getId());

            //任务提交到线程池执行
            item.setExecuteTime(LocalDateTime.now());//记录下任务分发时间
            executeTask(item);
            log.info("companyAssetSettleSchedule commitTask success! taskId=[{}]", item.getId());
        }
    }

    /**
     * 执行任务
     * @param settleTask
     */
    private void executeTask(AsAssetSettleTask settleTask) {
        //校验任务参数，一般不会为空
        if (StrUtil.isBlank(settleTask.getTaskParameters())) {
            log.info("companyAssetSettleSchedule executeTask fail! task parameter empty! taskId=[{}]", settleTask.getId());
            return ;
        }

        //校验任务类型，不在这个类型的任务不予执行
        SettleTaskTypeEnum settleTaskType = SettleTaskTypeEnum.getByType(settleTask.getTaskType());
        if (SettleTaskTypeEnum.UN_KNOW.equals(settleTaskType)) {
            log.info("companyAssetSettleSchedule executeTask fail! task type error! taskId=[{}]", settleTask.getId());
        }

        try {
            //结账参数
            AssetDepreciationCalculateDto calculateDto = JSONObject.parseObject(settleTask.getTaskParameters(), AssetDepreciationCalculateDto.class);
            ListenableFuture<Boolean> future = null;

            //提交任务至异步回调线程池
            ListeningExecutorService listeningExecutorService = MoreExecutors.listeningDecorator(AssetThreadPoolExecutorManager.getExecutor(settleTaskType.getTaskBizCode()));
            switch (settleTaskType) {
                case COMPANY_DEPRECIATION :
                    future = listeningExecutorService.submit(() -> billInfoService.confirmDepreciation(calculateDto), true);
                    break;
                case COMPANY_SETTLE :
                case MONTH_SETTLE :
                    future = listeningExecutorService.submit(() -> billInfoService.confirmSettle(calculateDto), true);
                    break;
                default :
                    break;
            }

            if (Objects.nonNull(future)) {
                log.info("companyAssetSettleSchedule executeTask submit task executor! taskId=[{}]", settleTask.getId());
                //1个小时超时时间
                ListenableFuture<Boolean> timeoutFuture = Futures.withTimeout(future, 3600, TimeUnit.SECONDS, TIMEOUT_SCHEDULER_EXECUTOR);

                //任务异步回调
                Futures.addCallback(timeoutFuture, new FutureCallback<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        //更新任务状态
                        settleTaskService.update(Wrappers.lambdaUpdate(AsAssetSettleTask.class)
                                //任务执行状态
                                .set(AsAssetSettleTask::getStatus, SettleTaskStatusEnum.SUCCESS.getStatusCode())
                                //任务执行时间
                                .set(AsAssetSettleTask::getTotalCost, System.currentTimeMillis() - settleTask.getExecuteTime().toInstant(ZoneOffset.of("+8")).toEpochMilli())
                                .eq(AsAssetSettleTask::getId, settleTask.getId()));
                    }

                    @Override
                    public void onFailure(Throwable throwable) {
                        //更新任务状态
                        settleTaskService.update(Wrappers.lambdaUpdate(AsAssetSettleTask.class)
                                //任务执行状态
                                .set(AsAssetSettleTask::getStatus, SettleTaskStatusEnum.FAIL.getStatusCode())
                                //任务执行时间
                                .set(AsAssetSettleTask::getTotalCost, System.currentTimeMillis() - settleTask.getExecuteTime().toInstant(ZoneOffset.of("+8")).toEpochMilli())
                                .eq(AsAssetSettleTask::getId, settleTask.getId()));

                        //发送钉钉告警
                        messageService.sendFinanceWarning(settleTask, throwable.getMessage());
                    }
                }, AssetThreadPoolExecutorManager.getExecutor(settleTaskType.getTaskBizCode()));
            }
        } catch (Exception e) {
            log.error("companyAssetSettleSchedule executeTask error! taskId=[{}] exception ", settleTask.getId(), e);
            //更新任务状态
            settleTaskService.update(Wrappers.lambdaUpdate(AsAssetSettleTask.class)
                    //任务执行状态
                    .set(AsAssetSettleTask::getStatus, SettleTaskStatusEnum.FAIL.getStatusCode())
                    //任务执行时间
                    .set(AsAssetSettleTask::getTotalCost, System.currentTimeMillis() - settleTask.getExecuteTime().toInstant(ZoneOffset.of("+8")).toEpochMilli())
                    .eq(AsAssetSettleTask::getId, settleTask.getId()));

            //发送钉钉告警
            messageService.sendFinanceWarning(settleTask, e.getMessage());
        }
    }
}
