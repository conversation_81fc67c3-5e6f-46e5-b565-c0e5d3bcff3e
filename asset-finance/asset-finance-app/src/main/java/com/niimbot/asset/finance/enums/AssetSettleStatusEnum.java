package com.niimbot.asset.finance.enums;

import java.util.Objects;

/**
 * 资产折旧状态枚举
 *
 * <AUTHOR>
 * @date 2023/2/9 下午1:54
 */
public enum AssetSettleStatusEnum {

    /**
     * 待折旧
     */
    WAIT(1, "待折旧"),
    /**
     * 折旧中
     */
    SETTLEMENT(2, "折旧中"),
    /**
     * 折旧终止
     */
    STOP(3, "折旧终止"),
    /**
     * 折旧提完
     */
    FINISH(4, "折旧提完"),
    /**
     * 未知
     */
    UN_KNOW(-1, "未知"),
    ;

    private Integer code;

    private String desc;

    AssetSettleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AssetSettleStatusEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (AssetSettleStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
