package com.niimbot.asset.finance.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * as_asset_finance_alter_log
 * <AUTHOR>
@Data
@TableName(value = "as_asset_finance_alter_log", autoResultMap = true)
public class AsAssetFinanceAlterLog implements Serializable {

    private static final long serialVersionUID = -4915506329788326073L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资产入账信息编码
     */
    private String assetBillCode;

    /**
     * 变更详情
     */
    private String detail;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}