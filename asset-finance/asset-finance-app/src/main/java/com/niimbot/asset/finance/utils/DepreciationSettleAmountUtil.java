package com.niimbot.asset.finance.utils;

import com.niimbot.asset.finance.enums.DepreciationStrategyEnum;
import com.niimbot.asset.finance.model.AsAssetFinanceInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 折旧费用计算工具类
 * <AUTHOR>
 * @date 2023/2/10 下午3:33
 */
public class DepreciationSettleAmountUtil {

    /**
     * 平均算法计算当前会计期间折旧金额
     * 资产原值 * (1 - 预计残值率) / 期间数
     * @param financeInfo
     * @return
     */
    public static BigDecimal averageSettle(AsAssetFinanceInfo financeInfo) {
        //当前会计期间折旧金额 = 资产原值 * (100 - 残值率) / (预计使用期间数 * 100)
        BigDecimal settleAmount = financeInfo.getOriginPrice().multiply(BigDecimal.valueOf(100L).subtract(financeInfo.getPreResidualRate()))
                .divide(BigDecimal.valueOf(financeInfo.getPreSettleCount()).multiply(BigDecimal.valueOf(100L)), 4, RoundingMode.HALF_DOWN);

        //预计净残值 = 资产原值 * 残值率 / 100
        BigDecimal remainingAmount = financeInfo.getOriginPrice().multiply(financeInfo.getPreResidualRate())
                .divide(BigDecimal.valueOf(100L), 4, RoundingMode.HALF_UP);

        //因为有重新折旧的功能，导致累计折旧实际没有结账，不应该算进去，需要加回来
        //净值 = 资产原值 - 累积折旧值 + 本次折旧金额 - 当前会计期间折旧值
        BigDecimal currentSettleAmount = BigDecimal.ZERO;
        if (Objects.nonNull(financeInfo.getCurrentSettleAmount())) {
            currentSettleAmount = financeInfo.getCurrentSettleAmount();
        }
        BigDecimal currentRemainingAmount = financeInfo.getOriginPrice().subtract(financeInfo.getAccumulationAmount())
                .add(currentSettleAmount).subtract(settleAmount);
        //当前净值 > 预计净值时，取剩余折旧值(资产原值 - 累积折旧值 - 预计净残值)，否则取折旧值
        if (currentRemainingAmount.compareTo(remainingAmount) >= 0) {
            return settleAmount;
        } else {
            return financeInfo.getOriginPrice().subtract(financeInfo.getAccumulationAmount()).subtract(remainingAmount);
        }
    }

    /**
     * 动态平均算法计算当前会计期间折旧金额
     * (资产原值 * (1 - 残值率) - 累积折旧) / 剩余期间数(预计期间数 - 已折旧期间数)
     * @param financeInfo
     * @return
     */
    public static BigDecimal dynamicAverageSettle(AsAssetFinanceInfo financeInfo) {
        //剩余期间数
        int remainingSettleCount = financeInfo.getPreSettleCount() - financeInfo.getSettleCount();

        //当前会计期间折旧金额 = (资产原值 * (100 - 残值率) - 累积折旧金额) / 剩余期间数
        BigDecimal settleAmount = financeInfo.getOriginPrice().multiply(BigDecimal.valueOf(100L).subtract(financeInfo.getPreResidualRate()))
                .divide(BigDecimal.valueOf(100L), 4, RoundingMode.HALF_DOWN).subtract(financeInfo.getAccumulationAmount())
                .divide(BigDecimal.valueOf(remainingSettleCount), 4, RoundingMode.HALF_DOWN);

        //预计净残值 = 资产原值 * 残值率 / 100
        BigDecimal remainingAmount = financeInfo.getOriginPrice().multiply(financeInfo.getPreResidualRate())
                .divide(BigDecimal.valueOf(100L), 4, RoundingMode.HALF_UP);

        //因为有重新折旧的功能，导致累计折旧实际没有结账，不应该算进去，需要加回来
        //净值 = 资产原值 - 累积折旧值 + 本次折旧金额 - 当前会计期间折旧值
        BigDecimal currentSettleAmount = BigDecimal.ZERO;
        if (Objects.nonNull(financeInfo.getCurrentSettleAmount())) {
            currentSettleAmount = financeInfo.getCurrentSettleAmount();
        }
        BigDecimal currentRemainingAmount = financeInfo.getOriginPrice().subtract(financeInfo.getAccumulationAmount())
                .add(currentSettleAmount).subtract(settleAmount);
        //当前净值 > 预计净值时，取剩余折旧值(资产原值 - 累积折旧值 - 预计净残值)，否则取折旧值
        if (currentRemainingAmount.compareTo(remainingAmount) >= 0) {
            return settleAmount;
        } else {
            return financeInfo.getOriginPrice().subtract(financeInfo.getAccumulationAmount()).subtract(remainingAmount);
        }
    }

    /**
     * 计算当前会计期间折旧金额
     * @param financeInfo
     * @return
     */
    public static BigDecimal calculateAmount(AsAssetFinanceInfo financeInfo) {
        if (DepreciationStrategyEnum.AVERAGE.getCode().equals(financeInfo.getStrategy())) {
            //平均折旧算法
            return averageSettle(financeInfo);
        } else {
            //动态平均折旧算法
            return dynamicAverageSettle(financeInfo);
        }
    }
}
