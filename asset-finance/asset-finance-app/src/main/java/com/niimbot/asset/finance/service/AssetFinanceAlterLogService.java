package com.niimbot.asset.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.finance.AlterLogQueryDto;
import com.niimbot.finance.AssetFinanceAlterLogDto;
import com.niimbot.asset.finance.model.AsAssetFinanceAlterLog;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午3:51
 */
public interface AssetFinanceAlterLogService extends IService<AsAssetFinanceAlterLog> {

    /**
     * 分页查询变更日志
     * @param queryDto
     * @return
     */
    IPage<AssetFinanceAlterLogDto> pageQuery(AlterLogQueryDto queryDto);
}
