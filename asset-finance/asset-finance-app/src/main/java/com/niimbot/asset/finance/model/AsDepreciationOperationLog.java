package com.niimbot.asset.finance.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_depreciation_operation_log
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_depreciation_operation_log", autoResultMap = true)
public class AsDepreciationOperationLog implements Serializable {

    private static final long serialVersionUID = -928663742462986097L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务唯一编码
     */
    private String bizCode;

    private String actionName;

    private String actionMethod;

    private String actionParam;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}