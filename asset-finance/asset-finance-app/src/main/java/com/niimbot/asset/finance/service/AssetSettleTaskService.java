package com.niimbot.asset.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.finance.SettleTaskQueryDto;
import com.niimbot.asset.finance.model.AsAssetSettleTask;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:07
 */
public interface AssetSettleTaskService extends IService<AsAssetSettleTask> {

    List<AsAssetSettleTask> queryWaitExecuteTask(SettleTaskQueryDto queryDto);
}
