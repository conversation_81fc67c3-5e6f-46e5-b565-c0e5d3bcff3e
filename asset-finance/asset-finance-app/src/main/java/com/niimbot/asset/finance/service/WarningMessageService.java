package com.niimbot.asset.finance.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.finance.model.AsAssetSettleTask;
import com.niimbot.asset.framework.utils.DingTalkUtils;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/1 上午10:15
 */
@Slf4j
@Component
public class WarningMessageService extends DingTalkUtils {

    //财务折旧告警markdown模板
    private static final String MARKDOWN_TEMPLATE = "#### 财务折旧\n" +
            "- 任务id：**%s**\n" +
            "- 任务名称：**%s**\n" +
            "- 任务参数：%s\n" +
            "- 企业名称：**%s**\n" +
            "- 异常信息：%s";

    //webhook地址
    private static final String BASE_URL = "https://oapi.dingtalk.com/robot/send?access_token=ebd7a3f3b87fa5724213aa1538f9375c4eb90934ed15a2176d12193694ba848c";

    //密钥
    private static final String SECRET_KEY = "SEC0f87243c75c2e4891ae2a3b5125fbd3ebf726d068735ab9de24fb95538abe886";

    /**
     * 发送财务告警消息
     * @param settleTask
     * @param message
     */
    public void sendFinanceWarning(AsAssetSettleTask settleTask, String message) {
        try {
            if (StrUtil.isBlank(message)) {
                message = "无";
            }
            String warningMessage = String.format(MARKDOWN_TEMPLATE, settleTask.getId(), settleTask.getTaskName(),
                    settleTask.getTaskParameters(), settleTask.getOrgName(), message);
            Map<String, Object> contentMap = new HashMap<>(4);
            contentMap.put("title", "财务折旧告警");
            contentMap.put("text", warningMessage);
            sendMsg(BASE_URL, SECRET_KEY, "markdown", contentMap);
        } catch (Exception e) {
            log.warn("warningMessageService sendFinanceWarning error! param=[{}] exception ", JSONObject.toJSONString(settleTask), e);
        }
    }
}
