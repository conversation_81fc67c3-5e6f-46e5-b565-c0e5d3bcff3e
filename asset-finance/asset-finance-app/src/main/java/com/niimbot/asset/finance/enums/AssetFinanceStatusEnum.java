package com.niimbot.asset.finance.enums;

import java.util.Objects;

/**
 * 资产入账状态枚举
 *
 * <AUTHOR>
 * @date 2023/2/9 下午3:18
 */
public enum AssetFinanceStatusEnum {

    /**
     * 待入账
     */
    WAIT(0, "待入账"),
    /**
     * 已入账
     */
    CREDITED(1, "已入账"),
    /**
     * 反入账
     */
    UN_CREDITED(2, "反入账"),
    /**
     * 未知
     */
    UN_KNOW(-1, "未知"),
    ;

    private Integer code;

    private String desc;

    AssetFinanceStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AssetFinanceStatusEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (AssetFinanceStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
