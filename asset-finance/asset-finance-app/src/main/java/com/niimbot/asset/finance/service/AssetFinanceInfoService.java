package com.niimbot.asset.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.finance.*;
import com.niimbot.asset.finance.model.AsAssetFinanceInfo;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午3:49
 */
public interface AssetFinanceInfoService extends IService<AsAssetFinanceInfo> {

    /**
     * 获取资产原值
     * @param assetId
     * @return
     */
    AssetFinanceDefaultDto assetFinanceDefault(Long assetId);

    /**
     * 保存入账信息
     * @param configDto
     * @return
     */
    Boolean saveConfig(AssetFinanceInfoConfigDto configDto);

    /**
     * 保存导入资产入账信息
     * @param importDto
     * @return
     */
    Boolean saveImportData(AssetFinanceImportDto importDto);

    /**
     * 根据业务编码查询入账信息
     * @param bizCode
     * @return
     */
    AssetFinanceInfoConfigDto queryByCode(String bizCode);

    /**
     * 编辑入账信息
     * @param configDto
     * @return
     */
    Boolean edit(AssetFinanceInfoConfigDto configDto);

    /**
     * 变更入账信息
     * @param configDto
     * @return
     */
    Boolean alter(AssetFinanceInfoConfigDto configDto);

    /**
     * 分页查询变更日志
     * @param queryDto
     * @return
     */
    IPage<AssetFinanceAlterLogDto> pageAlterLog(AlterLogQueryDto queryDto);

    /**
     * 资产反入账
     * @param bizCode
     * @return
     */
    Boolean resetConfig(String bizCode);

    /**
     * 分页查询资产入账信息
     * @param request
     * @return
     */
    List<AsAssetFinanceInfo> pageQuery(AssetFinanceInfoQueryDto request);

    /**
     * 重置资产入账信息的累计值
     * @param companyId
     * @return
     */
    Integer resetSettleData(Long companyId);

    /**
     * 查询已入账的资产企业信息
     * @return
     */
    List<Long> queryValidCompany();

    /**
     * 统计新增当期入账资产
     * @param companyId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer countCurrentAssetNum(Long companyId, Integer startDate, Integer endDate);

    /**
     * 统计新增历史入账资产
     * @param companyId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer countHistoryAssetNum(Long companyId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计之前入账资产数
     * @param companyId
     * @param startDate
     * @return
     */
    Integer countPreMonthAssetNum(Long companyId, Integer startDate);

    /**
     * 统计之前入账资产数
     * @param companyId
     * @param startTime
     * @param endTime
     * @return
     */
    Integer countHandleAssetNum(Long companyId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询待入账资产信息
     * @param queryDto
     * @return
     */
    List<List<Object>> queryAssetWait(AssetFinanceInfoQueryDto queryDto);

    /**
     * 查询待入账资产
     * @param queryDto
     * @return
     */
    PageUtils<AssetFinanceInfoDto> queryWaitFinanceAsset(AssetFinanceInfoQueryDto queryDto);

    /**
     * 查询已入账或反入账资产
     * @param queryDto
     * @return
     */
    PageUtils<AssetFinanceInfoDto> queryFinanceInfo(AssetFinanceInfoQueryDto queryDto);

    /**
     * 初始入账信息查询
     * @param queryDto
     * @return
     */
    PageUtils<AssetInitFinanceInfoDto> queryInitFinanceInfo(AssetFinanceInfoQueryDto queryDto);

    /**
     * 查询台账信息
     * @param queryDto
     * @return
     */
    PageUtils<AssetMachineAccountDto> queryMachineAccount(AssetFinanceInfoQueryDto queryDto);

    /**
     * 保存导入excel表头数据
     * @param importErrorDto
     */
    void saveSheetHead(HeadImportErrorDto importErrorDto);

    /**
     * 资产所属组织变更
     * @param messageDto
     * @return
     */
    Boolean alterAssetOrgOwner(AssetAttributeAlterMessageDto messageDto);

    /**
     * 资产处置
     * @param messageDto
     * @return
     */
    Boolean handleAsset(AssetAttributeAlterMessageDto messageDto);

    /**
     * 查询excel导入错误信息
     * @param taskId
     * @return
     */
    List<List<LuckySheetModel>> importError(Long taskId);

    /**
     * 统计分摊部门为该orgId的资产入账信息
     * @param orgIds
     * @return
     */
    Integer countAssetFinanceByOrgId(List<Long> orgIds);
}
