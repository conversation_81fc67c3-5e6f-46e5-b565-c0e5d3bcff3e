package com.niimbot.asset.finance.enums;

import java.util.Objects;

/**
 * 企业结账状态枚举
 *
 * <AUTHOR>
 * @date 2023/2/9 下午7:18
 */
public enum AssetBillStatusEnum {
    /**
     * 待计提
     */
    WAIT_ACCRUAL(1, "待计提"),
    /**
     * 已折旧
     */
    DEPRECIATED(2, "已折旧"),
    /**
     * 已结账
     */
    CHECKED_OUT(3, "已结账"),
    /**
     * 未知
     */
    UN_KNOW(-1, "未知"),
    ;

    private Integer code;

    private String desc;

    AssetBillStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AssetBillStatusEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (AssetBillStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
