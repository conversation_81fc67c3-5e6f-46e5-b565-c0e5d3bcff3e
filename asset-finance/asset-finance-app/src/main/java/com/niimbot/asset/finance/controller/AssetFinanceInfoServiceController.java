package com.niimbot.asset.finance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.finance.service.AssetFinanceInfoService;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.finance.AlterLogQueryDto;
import com.niimbot.finance.AssetFinanceAlterLogDto;
import com.niimbot.finance.AssetFinanceDefaultDto;
import com.niimbot.finance.AssetFinanceImportDto;
import com.niimbot.finance.AssetFinanceInfoConfigDto;
import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.finance.AssetFinanceInfoQueryDto;
import com.niimbot.finance.AssetInitFinanceInfoDto;
import com.niimbot.finance.AssetMachineAccountDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.validate.group.Update;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午3:57
 */
@Api(tags = "资产入账信息")
@RestController
@RequestMapping("server/finance/bill/")
public class AssetFinanceInfoServiceController {

    @Autowired
    private AssetFinanceInfoService financeInfoService;

    @ApiOperation("获取资产入账默认值信息")
    @GetMapping("defaultValue/{assetId}")
    public AssetFinanceDefaultDto assetDefaultValue(@PathVariable Long assetId) {
        return financeInfoService.assetFinanceDefault(assetId);
    }

    @ApiOperation("保存资产入账信息")
    @PostMapping("config")
    public Boolean saveConfig(@RequestBody @Validated AssetFinanceInfoConfigDto configDto) {
        return financeInfoService.saveConfig(configDto);
    }

    @ApiOperation("保存导入资产入账信息")
    @PostMapping("saveImportData")
    public Boolean saveImportData(@RequestBody @Validated AssetFinanceImportDto configDto) {
        return financeInfoService.saveImportData(configDto);
    }

    @ApiOperation("获取资产入账信息详情")
    @GetMapping("detail/{bizCode}")
    public AssetFinanceInfoConfigDto detail(@PathVariable String bizCode) {
        if (StrUtil.isBlank(bizCode)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID);
        }

        return financeInfoService.queryByCode(bizCode);
    }

    @ApiOperation("编辑资产入账信息")
    @PostMapping("edit")
    public Boolean editConfig(@RequestBody @Validated(Update.class) AssetFinanceInfoConfigDto configDto) {
        return financeInfoService.edit(configDto);
    }

    @ApiOperation("变更资产入账信息")
    @PostMapping("alter")
    public Boolean alterConfig(@RequestBody @Validated(Update.class) AssetFinanceInfoConfigDto configDto) {
        return financeInfoService.alter(configDto);
    }

    @ApiOperation("资产变更日志分页列表")
    @GetMapping("pageAlterLog")
    @AutoConvert
    public IPage<AssetFinanceAlterLogDto> pageAlterLog(@Validated AlterLogQueryDto queryDto) {
        return financeInfoService.pageAlterLog(queryDto);
    }

    @ApiOperation("反入账")
    @PostMapping("resetConfig")
    public Boolean resetConfig(@RequestBody AssetFinanceInfoConfigDto configDto) {
        return financeInfoService.resetConfig(configDto.getBizCode());
    }

    @ApiOperation("查询待入账资产信息")
    @PostMapping("queryAssetWait")
    public List<List<Object>> queryAssetWait(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto) {
        return financeInfoService.queryAssetWait(queryDto);
    }

    @ApiOperation("资产入账列表")
    @PostMapping("queryFinanceInfo")
    public PageUtils<AssetFinanceInfoDto> queryFinanceInfo(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto) {
        return financeInfoService.queryFinanceInfo(queryDto);
    }

    @ApiOperation("资产入账初始列表")
    @PostMapping("queryInitFinanceInfo")
    public PageUtils<AssetInitFinanceInfoDto> queryInitFinanceInfo(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto) {
        return financeInfoService.queryInitFinanceInfo(queryDto);
    }

    @ApiOperation("资产财务台账")
    @PostMapping("queryMachineAccount")
    public PageUtils<AssetMachineAccountDto> queryMachineAccount(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto) {
        return financeInfoService.queryMachineAccount(queryDto);
    }

    @ApiOperation("保存导入表头数据")
    @PostMapping(value = "saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        financeInfoService.saveSheetHead(importErrorDto);
    }

    /**
     * 错误记录
     * @param taskId
     * @return
     */
    @GetMapping(value = "importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return financeInfoService.importError(taskId);
    }
}
