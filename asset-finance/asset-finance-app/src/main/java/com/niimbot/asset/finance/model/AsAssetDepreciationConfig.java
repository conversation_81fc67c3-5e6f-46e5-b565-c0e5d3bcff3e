package com.niimbot.asset.finance.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_depreciation_config
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_asset_depreciation_config", autoResultMap = true)
@ApiModel(value = "AsDepreciationConfig对象", description = "折旧配置")
public class AsAssetDepreciationConfig implements Serializable {

    private static final long serialVersionUID = -2913154822032909059L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 适用资产来源
     */
    private String assetOrigin;

    /**
     * 适用资产价值
     */
    private BigDecimal assetPrice;

    /**
     * 折旧算法
     */
    private Integer strategy;

    /**
     * 是否允许手动折旧
     */
    private Integer manualSettle;

    /**
     * 首次折旧日期
     */
    private Integer firstSettleTime;

    /**
     * 折旧参数
     */
    private String depreciationParam;

    /**
     * 状态 0：正常 1：删除中 2：删除
     */
    private Integer status;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}