package com.niimbot.asset.finance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.finance.SettleTaskQueryDto;
import com.niimbot.asset.finance.mapper.AsAssetSettleTaskMapper;
import com.niimbot.asset.finance.model.AsAssetSettleTask;
import com.niimbot.asset.finance.service.AssetSettleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:11
 */
@Slf4j
@Service
public class AssetSettleTaskServiceImpl extends ServiceImpl<AsAssetSettleTaskMapper, AsAssetSettleTask> implements AssetSettleTaskService {

    @Override
    public List<AsAssetSettleTask> queryWaitExecuteTask(SettleTaskQueryDto queryDto) {
        return this.getBaseMapper().selectWaitExecute(queryDto);
    }
}
