package com.niimbot.asset.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.finance.model.AsAssetDepreciationConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsAssetDepreciationConfigMapper extends BaseMapper<AsAssetDepreciationConfig> {

    /**
     * 查询最早结算时间
     * @param orgIdList
     * @return
     */
    Integer selectMinSettleTime(@Param("orgIdList") List<Long> orgIdList);
}