package com.niimbot.asset.finance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.finance.AlterLogQueryDto;
import com.niimbot.finance.AssetFinanceAlterLogDto;
import com.niimbot.asset.finance.mapper.AsAssetFinanceAlterLogMapper;
import com.niimbot.asset.finance.model.AsAssetFinanceAlterLog;
import com.niimbot.asset.finance.service.AssetFinanceAlterLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午3:55
 */
@Slf4j
@Service
public class AssetFinanceAlterLogServiceImpl extends ServiceImpl<AsAssetFinanceAlterLogMapper, AsAssetFinanceAlterLog> implements AssetFinanceAlterLogService {

    @Override
    public IPage<AssetFinanceAlterLogDto> pageQuery(AlterLogQueryDto queryDto) {
        return this.getBaseMapper().pageQuery(queryDto.buildIPage(), queryDto);
    }
}
