package com.niimbot.asset.finance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.finance.mapper.AsAssetSettleSummaryMapper;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;
import com.niimbot.asset.finance.service.AssetSettleSummaryService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.finance.SettleSummaryDeptDto;
import com.niimbot.finance.SettleSummaryDto;
import com.niimbot.finance.SettleSummaryQueryDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:10
 */
@Slf4j
@Service
public class AssetSettleSummaryServiceImpl extends ServiceImpl<AsAssetSettleSummaryMapper, AsAssetSettleSummary> implements AssetSettleSummaryService {

    @Autowired
    private CacheResourceUtil cacheResourceUtil;
    @Autowired
    private OrgService orgService;

    @Override
    public IPage<SettleSummaryDto> pageQueryOrg(SettleSummaryQueryDto queryDto) {
        IPage<SettleSummaryDto> pageResult = new Page<>(queryDto.getPageNum(), queryDto.getPageSize());
        //查询当前用户有权限的企业
        List<OrgDto> orgDtoList = orgService.orgList(new OrgQueryDto().setOrgType(1));
        //当前用户没有可以查看的企业，直接返回空数据
        if (CollUtil.isEmpty(orgDtoList)) {
            return pageResult;
        }
        List<Long> orgIds = orgDtoList.stream().map(OrgDto::getId).collect(Collectors.toList());

        //没有所属企业筛选的时候，默认查询当前用户有权限的企业数据
        if (CollUtil.isEmpty(queryDto.getOrgId())) {
            queryDto.setOrgId(orgIds);
        }

        //折旧月份转换，去掉中间空格
        if (StrUtil.isNotBlank(queryDto.getSettleMonth())) {
            queryDto.setSettleMonthInt(Integer.parseInt(queryDto.getSettleMonth().replace("-", "")));
        }
        pageResult = this.getBaseMapper().pageQuery(queryDto.buildIPage(), queryDto);
        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return pageResult;
        }
        for (SettleSummaryDto item : pageResult.getRecords()) {
            item.setAssetCategoryDesc(cacheResourceUtil.getCategoryName(item.getAssetCategory()));
            item.setOrgName(cacheResourceUtil.getOrgName(item.getOrgId()));
        }
        return pageResult;
    }

    @Override
    public IPage<SettleSummaryDeptDto> pageQueryDept(SettleSummaryQueryDto queryDto) {
        IPage<SettleSummaryDeptDto> pageResult = new Page<>(queryDto.getPageNum(), queryDto.getPageSize());
        //查询当前用户有权限的部门
        List<OrgDto> orgDtoList = orgService.orgList(new OrgQueryDto().setOrgType(2));
        if (CollUtil.isEmpty(orgDtoList)) {
            //部门都没有权限的时候，直接返回空数据，没有数据可展示
            return pageResult;
        }
        List<Long> orgIds = orgDtoList.stream().map(OrgDto::getId).collect(Collectors.toList());
        //没有分摊部门可筛选的时候，默认查询当前用户有权限的部门数据
        if (CollUtil.isEmpty(queryDto.getSettleOrgId())) {
            queryDto.setSettleOrgId(orgIds);
        }

        //折旧月份转换，去掉中间空格
        if (StrUtil.isNotBlank(queryDto.getSettleMonth())) {
            queryDto.setSettleMonthInt(Integer.parseInt(queryDto.getSettleMonth().replace("-", "")));
        }

        pageResult = this.getBaseMapper().pageQueryDept(queryDto.buildIPage(), queryDto);
        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return pageResult;
        }
        for (SettleSummaryDeptDto item : pageResult.getRecords()) {
            item.setAssetCategoryDesc(cacheResourceUtil.getCategoryName(item.getAssetCategory()));
            item.setSettleOrgName(cacheResourceUtil.getOrgName(item.getSettleOrgId()));
        }
        return pageResult;
    }
}
