package com.niimbot.asset.finance.enums;

import java.util.Objects;

/**
 * 折旧算法策略枚举
 *
 * <AUTHOR>
 * @date 2023/2/7 上午9:26
 */
public enum DepreciationStrategyEnum {

    /**
     * 平均年限法
     */
    AVERAGE(1, "平均年限法"),
    /**
     * 动态平均年限法
     */
    DYNAMIC_AVERAGE(2, "动态平均年限法"),
    /**
     * 未知
     */
    UN_KNOW(-1, "未知"),
    ;

    DepreciationStrategyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DepreciationStrategyEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (DepreciationStrategyEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
