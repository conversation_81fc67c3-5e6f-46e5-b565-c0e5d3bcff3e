package com.niimbot.asset.finance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.finance.enums.AssetBillStatusEnum;
import com.niimbot.asset.finance.enums.AssetSettleStatusEnum;
import com.niimbot.asset.finance.enums.DepreciationConfigStatusEnum;
import com.niimbot.asset.finance.enums.DepreciationStrategyEnum;
import com.niimbot.asset.finance.enums.SettleTaskStatusEnum;
import com.niimbot.asset.finance.enums.SettleTaskTypeEnum;
import com.niimbot.asset.finance.enums.SettleTypeEnum;
import com.niimbot.asset.finance.mapper.AsAssetBillInfoMapper;
import com.niimbot.asset.finance.model.AsAssetBillInfo;
import com.niimbot.asset.finance.model.AsAssetDepreciationConfig;
import com.niimbot.asset.finance.model.AsAssetFinanceInfo;
import com.niimbot.asset.finance.model.AsAssetInitFinanceInfo;
import com.niimbot.asset.finance.model.AsAssetSettleDetail;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;
import com.niimbot.asset.finance.model.AsAssetSettleTask;
import com.niimbot.asset.finance.service.AssetBillInfoService;
import com.niimbot.asset.finance.service.AssetFinanceInfoService;
import com.niimbot.asset.finance.service.AssetInitFinanceInfoService;
import com.niimbot.asset.finance.service.AssetSettleDetailService;
import com.niimbot.asset.finance.service.AssetSettleInfoService;
import com.niimbot.asset.finance.service.AssetSettleSummaryService;
import com.niimbot.asset.finance.service.AssetSettleTaskService;
import com.niimbot.asset.finance.service.DepreciationConfigService;
import com.niimbot.asset.finance.utils.DepreciationSettleAmountUtil;
import com.niimbot.asset.finance.utils.TimeUtil;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.finance.AssetAccrualDto;
import com.niimbot.finance.AssetAccrualSubmitDto;
import com.niimbot.finance.AssetBillInfoDto;
import com.niimbot.finance.AssetBillInfoQueryDto;
import com.niimbot.finance.AssetDepreciationCalculateDto;
import com.niimbot.finance.AssetFinanceInfoQueryDto;
import com.niimbot.finance.OrgSettleDateInfoDto;
import com.niimbot.finance.OrgSettleInfoDto;
import com.niimbot.finance.SettleMonthQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:07
 */
@Slf4j
@Service
public class AssetBillInfoServiceImpl extends ServiceImpl<AsAssetBillInfoMapper, AsAssetBillInfo> implements AssetBillInfoService {

    private ExecutorService executorService;

    @Autowired
    private OrgService orgService;
    @Autowired
    private DepreciationConfigService depreciationConfigService;
    @Autowired
    private AsCusEmployeeService cusEmployeeService;
    @Autowired
    private AssetFinanceInfoService financeInfoService;
    @Autowired
    private AssetInitFinanceInfoService initFinanceInfoService;
    @Autowired
    private AssetSettleDetailService settleDetailService;
    @Autowired
    private AssetSettleSummaryService settleSummaryService;
    @Autowired
    private AssetSettleTaskService settleTaskService;
    @Autowired
    private CacheResourceUtil cacheResourceUtil;
    @Autowired
    private AssetSettleInfoService settleInfoService;

    @PostConstruct
    public void init() {
        executorService = AssetThreadPoolExecutorManager.newThreadPool("solution_assetBillInfoService", 8, 8, 10000);
    }

    @Override
    public PageUtils<AssetBillInfoDto> queryBillInfo(AssetBillInfoQueryDto queryDto) {
        //校验组织信息是否存在
        AsOrg org = orgService.getById(queryDto.getOrgId());
        if (Objects.isNull(org)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "当前企业信息不存在");
        }

        //查询企业折旧方案配置
        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, queryDto.getOrgId())
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "企业折旧方案配置不存在");
        }

        //查询当前月份的企业结算信息
        int settleMonth = TimeUtil.formatMonthInteger(LocalDateTime.now());
        AsAssetBillInfo assetBillInfo = getOne(Wrappers.lambdaQuery(AsAssetBillInfo.class)
                .eq(AsAssetBillInfo::getOrgId, queryDto.getOrgId())
                .eq(AsAssetBillInfo::getSettleMonth, settleMonth)
                .eq(AsAssetBillInfo::getIsDelete, 0));

        AssetBillInfoDto result = new AssetBillInfoDto();
        if (Objects.nonNull(assetBillInfo)) {
            BeanUtils.copyProperties(assetBillInfo, result);
            //折旧日期
            if (Objects.nonNull(assetBillInfo.getSettleDate())) {
                result.setSettleDate(TimeUtil.transferTime(assetBillInfo.getSettleDate()).atStartOfDay());
            }
            //结账日期
            if (Objects.nonNull(assetBillInfo.getBillDate()) && assetBillInfo.getBillDate() > 0) {
                result.setBillDate(TimeUtil.transferTime(assetBillInfo.getBillDate()).atStartOfDay());
            }
            result.setBillStatus(assetBillInfo.getStatus());
        } else {
            //查询全部或者是查询待计提数据时，生成一条数据返回
            result.setOrgId(queryDto.getOrgId());
            result.setBillStatus(AssetBillStatusEnum.WAIT_ACCRUAL.getCode());
            result.setSettleMonth(settleMonth);
        }
        result.setOrgCode(org.getOrgCode());
        result.setOrgName(org.getOrgName());
        result.setManualSettle(depreciationConfig.getManualSettle());
        result.setStrategy(depreciationConfig.getStrategy());
        result.setStrategyDesc(DepreciationStrategyEnum.getByCode(depreciationConfig.getStrategy()).getDesc());
        result.setBillStatusDesc(AssetBillStatusEnum.getByCode(result.getBillStatus()).getDesc());
        //预计折旧时间，月初第一天
        result.setPreSettleDate(LocalDateTime.now().plusMonths(1).with(TemporalAdjusters.firstDayOfMonth()));
        List<AssetBillInfoDto> dataResult = new ArrayList<>();
        dataResult.add(result);
        return new PageUtils<>(dataResult, 1,1,1);
    }

    @Override
    public OrgSettleDateInfoDto querySettleDateInfo(Long orgId) {
        //校验折旧方案配置是否存在，不存在给出提示信息
        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, orgId)
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }

        OrgSettleDateInfoDto result = new OrgSettleDateInfoDto();
        result.setOrgId(orgId);
        result.setEnableMonth(Integer.valueOf(String.valueOf(depreciationConfig.getFirstSettleTime()).substring(0, 6)));
        //获取会计期间
        int settleMonth = settleInfoService.queryCurrentSettleMonth(orgId, depreciationConfig.getFirstSettleTime());
        result.setSettleMonth(settleMonth);
        return result;
    }

    @Override
    public void confirmDepreciation(AssetDepreciationCalculateDto calculateDto) {
        long startTime = System.currentTimeMillis();
        //折旧计提前置校验
        calculateAssetVerify(calculateDto);

        //重新折旧前先清除之前生成的当前会计期间的数据
        resetSettleData(calculateDto.getOrgId(), calculateDto.getSettleMonth());

        //数据总数
        int dataSize = 0;
        //已处理数据总数
        int detailSize = 0;

        AssetFinanceInfoQueryDto queryDto = new AssetFinanceInfoQueryDto();
        queryDto.setId(0L);//处理起始id点
        queryDto.setOrgId(calculateDto.getOrgId());//折旧企业id
        queryDto.setCurrent(1);//始终从第一页开始，不能跟起始位点id同时变动
        queryDto.setSize(500L);//每次处理500条数据
        List<AsAssetFinanceInfo> financeInfoList = financeInfoService.pageQuery(queryDto);
        while (CollUtil.isNotEmpty(financeInfoList)) {
            //需要写入到明细列表的数据
            List<AsAssetSettleDetail> settleDetailList = new ArrayList<>();

            //单条处理数据
            for (AsAssetFinanceInfo item : financeInfoList) {
                //单个资产入账信息折旧校验，为0表示校验不通过，不能走下面的折旧计费
                BigDecimal singleVerifyResult = singleCalculateAssetVerify(calculateDto, item);
                if (singleVerifyResult.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("assetBillInfoService confirmDepreciation settleAmount Zero orgId=[{}] financeInfoId=[{}] settleMonth=[{}] autoCalculate=[{}]",
                            item.getOrgId(), item.getId(), calculateDto.getSettleMonth(), calculateDto.getAutoCalculate());
                    continue;
                }

                //单个资产折旧计提明细，折旧金额即使为0，也需要写入一条折旧明细
                AsAssetSettleDetail settleDetail = calculateSingleAsset(calculateDto, item);
                settleDetailList.add(settleDetail);

                //维护累积折旧金额和折旧次数以及净值数据
                LambdaUpdateWrapper<AsAssetFinanceInfo> modifyParam = Wrappers.lambdaUpdate(AsAssetFinanceInfo.class)
                        .set(AsAssetFinanceInfo::getCurrentSettleCount, 1)
                        .set(AsAssetFinanceInfo::getCurrentSettleAmount, settleDetail.getSettleAmount())
                        .set(AsAssetFinanceInfo::getPreviousSettleStatus, item.getSettleStatus())
                        .set(AsAssetFinanceInfo::getAccumulationAmount, item.getAccumulationAmount().add(settleDetail.getSettleAmount()))
                        .set(AsAssetFinanceInfo::getSettleCount, item.getSettleCount() + 1)
                        .set(AsAssetFinanceInfo::getRemainingAmount, item.getRemainingAmount().subtract(settleDetail.getSettleAmount()))
                        .eq(AsAssetFinanceInfo::getId, item.getId());

                //如果是结账，要清除掉当前折旧次数-当前折旧金额-上一次折旧状态，清除掉这些记录，再下一次折旧的时候判断不会有问题
                if (calculateDto.getIsSettle()) {
                    modifyParam.set(AsAssetFinanceInfo::getCurrentSettleCount, 0)
                            .set(AsAssetFinanceInfo::getCurrentSettleAmount, BigDecimal.ZERO)
                            .set(AsAssetFinanceInfo::getPreviousSettleStatus, 0);
                }

                //首次折旧会计期间
                if (Objects.isNull(item.getFirstSettleMonth()) || item.getFirstSettleMonth() == 0) {
                    modifyParam.set(AsAssetFinanceInfo::getFirstSettleMonth, calculateDto.getSettleMonth());
                }

                //折旧金额为0 或 折旧次数等于预计折旧次数，需要更改折旧状态为折旧已提完
                if (settleDetail.getSettleAmount().compareTo(BigDecimal.ZERO) == 0 || item.getPreSettleCount() <= (item.getSettleCount() + 1)) {
                    modifyParam.set(AsAssetFinanceInfo::getSettleStatus, AssetSettleStatusEnum.FINISH.getCode());
                } else {
                    //折旧终止状态在结账的时候写进去，不然重新折旧的时候，会过滤掉折旧终止状态的资产，导致数据统计和计算的不对
                    //资产被处置的时候，需要设置为折旧已终止，当同时出现折旧已提完和折旧已终止的时候，优先展示折旧已提完
                    if (Objects.nonNull(item.getHandleTime()) && calculateDto.getIsSettle()) {
                        int handleMonth = TimeUtil.formatMonthInteger(item.getHandleTime());
                        if (handleMonth <= calculateDto.getSettleMonth()) {
                            modifyParam.set(AsAssetFinanceInfo::getSettleStatus, AssetSettleStatusEnum.STOP.getCode());
                        }
                    } else {
                        modifyParam.set(AsAssetFinanceInfo::getSettleStatus, AssetSettleStatusEnum.SETTLEMENT.getCode());
                    }
                }
                financeInfoService.update(modifyParam);
            }

            //批量写入明细
            if (CollUtil.isNotEmpty(settleDetailList)) {
                settleDetailService.saveBatch(settleDetailList);
            }

            dataSize = dataSize + financeInfoList.size();
            detailSize = detailSize + settleDetailList.size();

            //重置下位点，重新查询，重新处理数据
            queryDto.setId(financeInfoList.get(financeInfoList.size() - 1).getId());
            financeInfoList = financeInfoService.pageQuery(queryDto);
        }

        //有明细了之后，需要统计写入汇总数据
        int summarySize = 0;
        List<AsAssetSettleSummary> settleSummaryList = settleDetailService.queryCompanySummary(calculateDto.getOrgId(), calculateDto.getSettleMonth());
        if (CollUtil.isNotEmpty(settleSummaryList)) {
            //设置下
            settleSummaryList.forEach(item -> item.setOrgName(cacheResourceUtil.getOrgName(item.getOrgId())));
            settleSummaryService.saveBatch(settleSummaryList);
            summarySize = settleSummaryList.size();
        }

        //然后写企业资产结账记录表数据
        AsAssetBillInfo billInfo = new AsAssetBillInfo().setOrgId(calculateDto.getOrgId())
                .setSettleMonth(calculateDto.getSettleMonth()).setSettleDate(TimeUtil.formatDateInteger(LocalDateTime.now()))
                .setStatus(AssetBillStatusEnum.DEPRECIATED.getCode());
        save(billInfo);
        log.info("assetBillInfoService calculateAssetDepreciation completed! companyId=[{}] dataSize=[{}] detailSize=[{}] summarySize=[{}] costTime=[{}]",
                calculateDto.getOrgId(), dataSize, detailSize, summarySize, System.currentTimeMillis() - startTime);
    }

    /**
     * 折旧计提前置校验
     * 1、企业是否开通财务折旧
     * 2、当前会计期间是否已结账，已结账不允许再次折旧计提
     * 3、手动折旧校验：企业配置允许手动折旧、每月15号之后
     * @param calculateDto
     */
    private void calculateAssetVerify(AssetDepreciationCalculateDto calculateDto) {
        //校验折旧方案配置是否正常启用
        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, calculateDto.getOrgId())
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "财务折旧方案配置不存在");
        }

        //校验当前会计期间是否已结账
        List<AsAssetBillInfo> assetBillInfo = this.getBaseMapper().selectList(Wrappers.lambdaQuery(AsAssetBillInfo.class)
                .eq(AsAssetBillInfo::getOrgId, calculateDto.getOrgId())
                .eq(AsAssetBillInfo::getSettleMonth, calculateDto.getSettleMonth())
                .eq(AsAssetBillInfo::getStatus, AssetBillStatusEnum.CHECKED_OUT.getCode())
                .eq(AsAssetBillInfo::getIsDelete, 0));
        if (CollUtil.isNotEmpty(assetBillInfo)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "当前会计期间已结账，不允许再折旧");
        }

        //如果是手动折旧需要校验折旧方案配置是否开启手动和当前时间需要在每个月的15号之后
        if (!calculateDto.getAutoCalculate()) {
            if (depreciationConfig.getManualSettle() != 1) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧配置方案不允许手动折旧");
            }

//            if (LocalDateTime.now().isBefore(TimeUtil.getStartDayOfMonth(LocalDate.now()).plusDays(15L))) {
//                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "仅允许每月15号之后进行手动折旧");
//            }
        }
    }

    /**
     * 重新折旧之前，需要先清空之前的数据
     * @param companyId
     * @param settleMonth
     */
    private void resetSettleData(Long companyId, Integer settleMonth) {
        //查询是否有生成折旧明细，有的话需要清空，先设置删除状态，后续直接硬删除，硬删除需要维护索引，处理较慢，耗时操作留在定时任务里晚上执行
        long settleDetailCount = settleDetailService.count(Wrappers.lambdaQuery(AsAssetSettleDetail.class)
                .eq(AsAssetSettleDetail::getOrgId, companyId)
                .eq(AsAssetSettleDetail::getSettleMonth, settleMonth)
                .eq(AsAssetSettleDetail::getIsDelete, 0));

        //查询已折旧账单
        long billInfoCount = this.getBaseMapper().selectCount(Wrappers.lambdaQuery(AsAssetBillInfo.class)
                .eq(AsAssetBillInfo::getOrgId, companyId)
                .eq(AsAssetBillInfo::getSettleMonth, settleMonth)
                .eq(AsAssetBillInfo::getStatus,AssetBillStatusEnum.DEPRECIATED.getCode())
                .eq(AsAssetBillInfo::getIsDelete, 0));

        //折旧明细和账单都没有的时候，才不用删除前一次折旧产生的数据。有一种情况是有账单，但是没有折旧明细数据。
        if (settleDetailCount <= 0 && billInfoCount <= 0) {
            return ;
        }

        //逻辑删除折旧明细，后续定时任务扫描物理删除
        settleDetailService.update(Wrappers.lambdaUpdate(AsAssetSettleDetail.class).set(AsAssetSettleDetail::getIsDelete, -1)
                .eq(AsAssetSettleDetail::getOrgId, companyId)
                .eq(AsAssetSettleDetail::getSettleMonth, settleMonth));

        //逻辑删除折旧汇总信息，后续定时任务扫描物理删除
        settleSummaryService.update(Wrappers.lambdaUpdate(AsAssetSettleSummary.class).set(AsAssetSettleSummary::getIsDelete, -1)
                .eq(AsAssetSettleSummary::getOrgId, companyId)
                .eq(AsAssetSettleSummary::getSettleMonth, settleMonth));

        //清除企业资产结账记录表
        update(Wrappers.lambdaUpdate(AsAssetBillInfo.class).set(AsAssetBillInfo::getIsDelete, -1)
                .eq(AsAssetBillInfo::getOrgId, companyId)
                .eq(AsAssetBillInfo::getSettleMonth, settleMonth));

        //资产入账信息的累积字段维护
        financeInfoService.resetSettleData(companyId);
    }

    /**
     * 处理单个资产的折旧信息，并生成明细对象返回
     * @param calculateDto
     * @param item
     * @return
     */
    private AsAssetSettleDetail calculateSingleAsset(AssetDepreciationCalculateDto calculateDto, AsAssetFinanceInfo item) {
        //计算折旧金额，可能为0
        BigDecimal settleAmount = DepreciationSettleAmountUtil.calculateAmount(item);

        //组装折旧明细对象
        AsAssetSettleDetail settleDetail = new AsAssetSettleDetail();
        settleDetail.setOrgId(item.getOrgId())
                .setSettleOrgId(item.getSettleOrgId())
                .setAssetId(item.getAssetId()).setAssetCode(item.getAssetCode())
                .setAssetCategory(item.getAssetCategory()).setAssetFinanceCode(item.getBizCode())
                .setOriginPrice(item.getOriginPrice()).setSettleAmount(settleAmount)
                .setPreviousRemainingAmount(item.getRemainingAmount())
                .setRemainingAmount(item.getRemainingAmount().subtract(settleAmount))
                .setAccumulationAmount(item.getAccumulationAmount().add(settleAmount))
                .setSettleMonth(calculateDto.getSettleMonth());
        return settleDetail;
    }

    /**
     * 单个资产折旧算费校验
     * @param calculateDto
     * @param financeInfo
     * @return
     */
    private BigDecimal singleCalculateAssetVerify(AssetDepreciationCalculateDto calculateDto, AsAssetFinanceInfo financeInfo) {
        //当前月份
        int currentMonth = TimeUtil.formatMonthInteger(LocalDateTime.now());
        //当前时间的上一个月份
        int preMonth = TimeUtil.formatMonthInteger(LocalDateTime.now().plusMonths(-1L));

        //当期入账，需要校验当前会计期间和入账月份是否相同
        if (SettleTypeEnum.PERIOD.getCode().equals(financeInfo.getSettleType())) {

            //入账月份
            int billMonth = Integer.parseInt(String.valueOf(financeInfo.getBillDate()).substring(0, 6));
            //手动折旧是当前时间月份所对应的会计期间(譬如当前是2月，手动折旧就是折旧2月会计期间的)，那么1月及之前当期入账的资产可以进行折旧计算
            if (!calculateDto.getAutoCalculate()) {
                if (billMonth > preMonth) {
                    return BigDecimal.ZERO;
                }
            } else {
                //自动折旧的话，只能计算上上个月当期入账的资产
                if (billMonth >= preMonth) {
                    return BigDecimal.ZERO;
                }
            }
        }

        //资产被处置时，只需要折旧到被处置月份即可
        if (Objects.nonNull(financeInfo.getHandleTime())) {
            //资产处置月份
            int handleMonth = TimeUtil.formatMonthInteger(financeInfo.getHandleTime());

            //需要区分自动折旧和手动折旧
            if (calculateDto.getAutoCalculate()) {
                //自动折旧，处置月份需要小于当前月份
                if (handleMonth < currentMonth) {
                    return BigDecimal.ZERO;
                }
            } else {
                //自动折旧，处置月份需要小于当前月份
                if (handleMonth > currentMonth) {
                    return BigDecimal.ZERO;
                }
            }
        }

        //因为有重新折旧的存在，导致折旧终止和折旧已提完的处理变的有些麻烦，没有结账前的本次折旧终止和折旧已提完，重新折旧的时候需要计算上
        //折旧完和折旧终止状态不允许再折旧计算，这里折旧已提完需要注意，第一次手动折旧的时候已提完，重新折旧的时候还是需要再计算，要区分开
        //资产处置的时候，需要设置成折旧终止，重新折旧再跑的时候，折旧终止的需要再计算上
        if ((AssetSettleStatusEnum.STOP.getCode().equals(financeInfo.getSettleStatus())
                && financeInfo.getPreviousSettleStatus() == 0)
                || (AssetSettleStatusEnum.FINISH.getCode().equals(financeInfo.getSettleStatus())
                && financeInfo.getPreviousSettleStatus() == 0)) {
            return BigDecimal.ZERO;
        }

        //走到这里表示可以进行折旧算费了
        return BigDecimal.ONE;
    }

    /**
     * 计算单个资产当前会计期间的折旧金额
     * @param calculateDto
     * @param financeInfo
     * @return
     */
    private BigDecimal calculateSingleSettleAmount(AssetDepreciationCalculateDto calculateDto, AsAssetFinanceInfo financeInfo) {
        //单个折旧算费校验
        BigDecimal verifyResult = singleCalculateAssetVerify(calculateDto, financeInfo);
        if (verifyResult.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        //计算当前会计期间折旧金额
        return DepreciationSettleAmountUtil.calculateAmount(financeInfo);
    }

    @Override
    public void confirmSettle(AssetDepreciationCalculateDto calculateDto) {
        //先进行折旧，生成折旧明细
        confirmDepreciation(calculateDto);

        //修改企业资产结账记录表
        update(Wrappers.lambdaUpdate(AsAssetBillInfo.class)
                .set(AsAssetBillInfo::getBillDate, TimeUtil.formatDateInteger(LocalDateTime.now()))
                .set(AsAssetBillInfo::getStatus, AssetBillStatusEnum.CHECKED_OUT.getCode())
                .eq(AsAssetBillInfo::getOrgId, calculateDto.getOrgId())
                .eq(AsAssetBillInfo::getSettleMonth, calculateDto.getSettleMonth()));
    }

    @Override
    public AssetAccrualDto depreciationAccrual(AssetAccrualSubmitDto accrualSubmitDto) {
        log.info("assetBillInfoService depreciationAccrual start! orgId=[{}]", accrualSubmitDto.getOrgId());
        long initTime = System.currentTimeMillis();
        long startTime = System.currentTimeMillis();
        AssetDepreciationCalculateDto calculateDto = new AssetDepreciationCalculateDto();
        calculateDto.setOrgId(accrualSubmitDto.getOrgId());
        calculateDto.setSettleMonth(accrualSubmitDto.getSettleMonth());
        calculateDto.setAutoCalculate(Boolean.FALSE);
        //手动折旧校验
        calculateAssetVerify(calculateDto);

        Future<Integer> currentAssetNumFuture = null;
        Future<Integer> historyAssetNumFuture = null;
        Future<Integer> preMonthAssetNumFuture = null;
        Future<Integer> handleAssetNumFuture = null;
        //计提需要这些统计值，结账不需要
        if (!accrualSubmitDto.getIsSettle()) {
            //当月第一天
            LocalDateTime currentMonthStartOfDay = TimeUtil.getStartDayOfMonth(LocalDate.now());
            int startDate = TimeUtil.formatDateInteger(currentMonthStartOfDay);

            //当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            //当前月份最后一天日期
            int currentMonthLastDay = TimeUtil.formatDateInteger(LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()));

            currentAssetNumFuture = executorService.submit(() -> financeInfoService.countCurrentAssetNum(accrualSubmitDto.getOrgId(), startDate, currentMonthLastDay));
            historyAssetNumFuture = executorService.submit(() -> financeInfoService.countHistoryAssetNum(accrualSubmitDto.getOrgId(), currentMonthStartOfDay, currentTime));
            preMonthAssetNumFuture = executorService.submit(() -> financeInfoService.countPreMonthAssetNum(accrualSubmitDto.getOrgId(), startDate));
            handleAssetNumFuture = executorService.submit(() -> financeInfoService.countHandleAssetNum(accrualSubmitDto.getOrgId(), currentMonthStartOfDay, currentTime));
        }
        log.info("assetBillInfoService depreciationAccrual submit statistics task! orgId=[{}] cost=[{}]", accrualSubmitDto.getOrgId(), System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        //数据总数
        int totalSize = 0;
        BigDecimal totalSettleAmount = BigDecimal.ZERO;

        AssetFinanceInfoQueryDto queryDto = new AssetFinanceInfoQueryDto();
        queryDto.setId(0L);//处理起始id点
        queryDto.setOrgId(calculateDto.getOrgId());//折旧企业id
        queryDto.setCurrent(1);//始终从第一页开始，跟起始id点不能同时变
        queryDto.setSize(500L);//每次处理500条数据
        List<AsAssetFinanceInfo> financeInfoList = financeInfoService.pageQuery(queryDto);
        while (CollUtil.isNotEmpty(financeInfoList)) {
            //单条处理数据
            for (AsAssetFinanceInfo item : financeInfoList) {
                //计算折旧金额
                BigDecimal settleAmount = calculateSingleSettleAmount(calculateDto, item);
                totalSettleAmount = totalSettleAmount.add(settleAmount);
            }

            totalSize = totalSize + financeInfoList.size();

            //重置下位点，重新查询，重新处理数据
            queryDto.setId(financeInfoList.get(financeInfoList.size() - 1).getId());
            financeInfoList = financeInfoService.pageQuery(queryDto);
        }
        log.info("assetBillInfoService depreciationAccrual calculate settle amount end! orgId=[{}] cost=[{}]", accrualSubmitDto.getOrgId(), System.currentTimeMillis() - startTime);

        AssetAccrualDto result = new AssetAccrualDto();
        result.setOrgId(accrualSubmitDto.getOrgId());
        result.setOrgName(cacheResourceUtil.getOrgName(accrualSubmitDto.getOrgId()));
        result.setSettleMonth(accrualSubmitDto.getSettleMonth());
        result.setSettleAmount(totalSettleAmount);

        //计提需要这些统计值，结账不需要
        if (!accrualSubmitDto.getIsSettle()) {
            try {
                //当期入账：入账日期为当前月份 - 入账类型为当期入账的资产
                if (currentAssetNumFuture != null) {
                    result.setCurrentAssetNum(currentAssetNumFuture.get(20, TimeUnit.SECONDS));
                }
                //历史入账：入账日期为当前月份的上一个月 - 入账类型为历史入账的资产
                if (historyAssetNumFuture != null) {
                    result.setHistoryAssetNum(historyAssetNumFuture.get(20, TimeUnit.SECONDS));
                }
                //待折旧资产：新增历史入账 + 往期历史入账和当期入账(因为历史入账只能选当前月份之前的月份，当期入账只能选当前月份，所以这里一条查询就够了)
                if (preMonthAssetNumFuture != null) {
                    result.setWaitDepreciationNum(preMonthAssetNumFuture.get(20, TimeUnit.SECONDS));
                }
                //新增处置资产：当前月份处置的资产数
                if (handleAssetNumFuture != null) {
                    result.setHandleAssetNum(handleAssetNumFuture.get(20, TimeUnit.SECONDS));
                }
            } catch (Exception e) {
                log.error("assetBillInfoService depreciationAccrual count asset num error! param=[{}] exception ", JSONObject.toJSONString(accrualSubmitDto), e);
            }
        }
        log.info("assetBillInfoService depreciationAccrual end! orgId=[{}] cost=[{}]", accrualSubmitDto.getOrgId(), System.currentTimeMillis() - initTime);
        return result;
    }

    @Override
    public Boolean submitSettle(AssetAccrualSubmitDto accrualSubmitDto) {
        //手动折旧校验
        AssetDepreciationCalculateDto calculateDto = new AssetDepreciationCalculateDto();
        calculateDto.setOrgId(accrualSubmitDto.getOrgId());
        calculateDto.setSettleMonth(accrualSubmitDto.getSettleMonth());
        calculateDto.setAutoCalculate(Boolean.FALSE);
        calculateDto.setIsSettle(accrualSubmitDto.getIsSettle());
        calculateAssetVerify(calculateDto);

        //校验是否有已提交或正在进行中的折旧任务
        List<Integer> statusList = Arrays.asList(SettleTaskStatusEnum.INIT.getStatusCode(), SettleTaskStatusEnum.EXECUTE.getStatusCode());
        List<AsAssetSettleTask> settleTaskList = settleTaskService.list(Wrappers.lambdaQuery(AsAssetSettleTask.class)
                .eq(AsAssetSettleTask::getOrgId, accrualSubmitDto.getOrgId())
                .in(AsAssetSettleTask::getStatus, statusList).eq(AsAssetSettleTask::getIsDelete, 0));
        if (CollUtil.isNotEmpty(settleTaskList)) {
            for (AsAssetSettleTask item : settleTaskList) {
                if (StrUtil.isBlank(item.getTaskParameters())) {
                    continue;
                }

                //任务参数
                AssetDepreciationCalculateDto calculateParam = JSONObject.parseObject(item.getTaskParameters(), AssetDepreciationCalculateDto.class);
                //这里让业务做了妥协，以防后提交的折旧计提任务在结账任务之前执行，因为任务调度是拉取一批任务同时执行的，没有保证任务的执行严格顺序
                if (accrualSubmitDto.getSettleMonth().equals(calculateParam.getSettleMonth())) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "已提交折旧计提任务，请稍后重试");
                }
            }
        }

        //提交异步任务
        AsAssetSettleTask settleTask = new AsAssetSettleTask()
                .setOrgId(accrualSubmitDto.getOrgId())
                .setOrgName(orgService.getById(accrualSubmitDto.getOrgId()).getOrgName())
                .setTaskParameters(JSONObject.toJSONString(calculateDto))
                .setTaskType(SettleTaskTypeEnum.COMPANY_DEPRECIATION.getTaskType())
                .setTaskName(SettleTaskTypeEnum.COMPANY_DEPRECIATION.getTaskDesc());
        if (accrualSubmitDto.getIsSettle()) {
            settleTask.setTaskType(SettleTaskTypeEnum.COMPANY_SETTLE.getTaskType());
            settleTask.setTaskName(SettleTaskTypeEnum.COMPANY_SETTLE.getTaskDesc());
        }
        settleTaskService.save(settleTask);
        return Boolean.TRUE;
    }

    @Override
    public OrgSettleInfoDto querySettleInfo(Long orgId) {
        OrgSettleInfoDto result = new OrgSettleInfoDto();
        result.setOrgName(cacheResourceUtil.getOrgName(orgId));

        //查询当前企业折旧方案配置
        AsAssetDepreciationConfig depreciationConfig = depreciationConfigService.getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, orgId)
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }

        //saas才需要验证码去校验，钉钉和企业微信不需要
        if (Edition.isSaas()) {
            //获取超管信息
            AsCusEmployee administrator = cusEmployeeService.getAdministrator();
            if (Objects.isNull(administrator) || StrUtil.isBlank(administrator.getMobile())) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "超管手机号不存在");
            }
            //超管手机号脱敏，拷贝的hutool工具类
            result.setAdministratorMobile(StrUtil.isBlank(administrator.getMobile()) ? "" : StrUtil.hide(administrator.getMobile(), 3, administrator.getMobile().length() - 4));
            //国际区号返回
            result.setNationalCode(StrUtil.isBlank(administrator.getNationalCode()) ? "+86":administrator.getNationalCode());
        }

        //财务模块开通月份
        int firstSettleMonth = Integer.parseInt(String.valueOf(depreciationConfig.getFirstSettleTime()).substring(0,6));

        //启用会计期间
        result.setFirstSettleMonth(TimeUtil.transferMonth(depreciationConfig.getFirstSettleTime(), "-"));

        //企业结账记录信息
        List<AsAssetBillInfo> billInfoList = this.list(Wrappers.lambdaQuery(AsAssetBillInfo.class)
                .eq(AsAssetBillInfo::getOrgId, orgId)
                .in(AsAssetBillInfo::getStatus, Arrays.asList(AssetBillStatusEnum.DEPRECIATED.getCode(), AssetBillStatusEnum.CHECKED_OUT.getCode()))
                .eq(AsAssetBillInfo::getIsDelete, 0).orderByDesc(AsAssetBillInfo::getSettleMonth));
        if (CollUtil.isNotEmpty(billInfoList)) {
            result.setCurrentSettleMonth(TimeUtil.transferMonth(billInfoList.get(0).getSettleMonth(), "-"));
        } else {
            int currentMonth = TimeUtil.formatMonthInteger(LocalDateTime.now().plusMonths(-1L));
            if (currentMonth <= firstSettleMonth) {
                result.setCurrentSettleMonth(TimeUtil.transferMonth(firstSettleMonth, "-"));
            } else {
                result.setCurrentSettleMonth(TimeUtil.transferMonth(currentMonth, "-"));
            }
        }
        return result;
    }

    @Override
    public Boolean clearAllSettleData(Long orgId) {
        //清除资产入账信息
        financeInfoService.update(Wrappers.lambdaUpdate(AsAssetFinanceInfo.class)
                .set(AsAssetFinanceInfo::getIsDelete, 1)
                .eq(AsAssetFinanceInfo::getOrgId, orgId));

        //清除资产入账初始信息
        initFinanceInfoService.update(Wrappers.lambdaUpdate(AsAssetInitFinanceInfo.class)
                .set(AsAssetInitFinanceInfo::getIsDelete, 1)
                .eq(AsAssetInitFinanceInfo::getOrgId, orgId));

        //清除折旧明细数据
        settleDetailService.update(Wrappers.lambdaUpdate(AsAssetSettleDetail.class)
                .set(AsAssetSettleDetail::getIsDelete, 1)
                .eq(AsAssetSettleDetail::getOrgId, orgId));

        //清除折旧汇总数据
        settleSummaryService.update(Wrappers.lambdaUpdate(AsAssetSettleSummary.class)
                .set(AsAssetSettleSummary::getIsDelete, 1)
                .eq(AsAssetSettleSummary::getOrgId, orgId));

        //清除企业结账信息
        update(Wrappers.lambdaUpdate(AsAssetBillInfo.class)
                .set(AsAssetBillInfo::getIsDelete, 1)
                .eq(AsAssetBillInfo::getOrgId, orgId));

        //修改折旧配置方案状态为已关闭，关闭中
        depreciationConfigService.update(Wrappers.lambdaUpdate(AsAssetDepreciationConfig.class)
                .set(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.CLOSED.getCode())
                .eq(AsAssetDepreciationConfig::getOrgId, orgId));
        return Boolean.TRUE;
    }

    @Override
    public List<String> queryOrgSettleMonth(SettleMonthQueryDto queryDto) {
        List<String> result = new ArrayList<>();
        LocalDate minSettleDate = LocalDate.now();
        LocalDate maxSettleDate = LocalDate.now();
        List<Long> orgIdList = null;
        if (Objects.nonNull(queryDto.getOrgId())) {
            //查询指定企业的
            orgIdList = Collections.singletonList(queryDto.getOrgId());
        } else {
            //查询当前用户有权限的企业, orgType=1:企业
            List<OrgDto> orgDtoList = orgService.orgList(new OrgQueryDto().setOrgType(1));
            if (CollUtil.isNotEmpty(orgDtoList)) {
                orgIdList = orgDtoList.stream().map(OrgDto::getId).collect(Collectors.toList());
            }
        }

        //校验组织id列表
        if (CollUtil.isEmpty(orgIdList)) {
//            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "组织信息为空");
            //不抛异常了，返回个空数组给前端
            return result;
        }

        //查询最小会计期间
        Integer minSettleDateInt = depreciationConfigService.queryMinSettleTime(orgIdList);
        //查询最大结算月份
        Integer maxSettleMonth = this.getBaseMapper().selectMaxSettleMonth(orgIdList);
        if (Objects.nonNull(minSettleDateInt)) {
            LocalDateTime minSettleTime = TimeUtil.transferTime(minSettleDateInt).atStartOfDay();
            minSettleDate = LocalDate.from(minSettleTime.with(TemporalAdjusters.firstDayOfMonth()));

        }
        if (Objects.nonNull(maxSettleMonth)) {
            maxSettleDate = LocalDate.parse(maxSettleMonth + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        while(!maxSettleDate.isBefore(minSettleDate)) {
            result.add(TimeUtil.formatTime(maxSettleDate, "yyyy-MM"));
            maxSettleDate = maxSettleDate.plusMonths(-1L);
        }
        return result;
    }
}
