package com.niimbot.asset.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.finance.SettleSummaryDeptDto;
import com.niimbot.finance.SettleSummaryDto;
import com.niimbot.finance.SettleSummaryQueryDto;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:06
 */
public interface AssetSettleSummaryService extends IService<AsAssetSettleSummary> {

    /**
     * 折旧汇总分页查询--所属公司
     * @param queryDto
     * @return
     */
    IPage<SettleSummaryDto> pageQueryOrg(SettleSummaryQueryDto queryDto);

    /**
     * 折旧汇总分页查询--分摊部门
     * @param queryDto
     * @return
     */
    IPage<SettleSummaryDeptDto> pageQueryDept(SettleSummaryQueryDto queryDto);
}
