package com.niimbot.asset.finance.enums;

/**
 * 折旧方案配置状态枚举
 *
 * <AUTHOR>
 * @date 2023/2/10 下午1:52
 */
public enum DepreciationConfigStatusEnum {

    /**
     * 正常
     */
    NORMAL(0, "正常"),
    /**
     * 关闭中
     */
    CLOSING(1, "关闭中"),
    /**
     * 已关闭
     */
    CLOSED(2, "已关闭"),
    ;

    DepreciationConfigStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
