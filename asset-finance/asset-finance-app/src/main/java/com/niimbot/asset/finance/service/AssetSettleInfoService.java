package com.niimbot.asset.finance.service;

import com.niimbot.asset.finance.mapper.AsAssetBillInfoMapper;
import com.niimbot.asset.finance.model.AsAssetBillInfo;
import com.niimbot.asset.finance.utils.TimeUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/13 上午9:55
 */
@Slf4j
@Component
public class AssetSettleInfoService {

    @Autowired
    private AsAssetBillInfoMapper billInfoMapper;

    /**
     * 获取当前会计期间信息
     * @param orgId 企业id
     * @param firstSettleTime 折旧方案开启时间
     * @return
     */
    public Integer queryCurrentSettleMonth(Long orgId, Integer firstSettleTime) {
        //默认取当前时间上一个月为结算月份
        int result = TimeUtil.formatMonthInteger(LocalDateTime.now().plusMonths(-1L));
        if (Objects.isNull(orgId) || Objects.isNull(firstSettleTime)) {
            return result;
        }

        //查询企业结算信息，按会计期间排序
        AsAssetBillInfo lastedBillInfo = billInfoMapper.queryLastedByOrgId(orgId);
        if (Objects.nonNull(lastedBillInfo)) {
            //当前会计期间需要基于已结算月份往下再推一个月
            LocalDate nextSettleMonth = TimeUtil.transferTime(Integer.parseInt(lastedBillInfo.getSettleMonth() + "01"))
                    .plusMonths(1);
            result = Integer.parseInt(TimeUtil.formatTime(nextSettleMonth, "yyyyMM"));
        }

        //折旧开启月份
        int enableMonth = Integer.parseInt(String.valueOf(firstSettleTime).substring(0, 6));
        //初始开通的时候，这里会出现开通月份是202302，会计期间是202301，这里判断兼容下
        if (result < enableMonth) {
            result = enableMonth;
        }
        return result;
    }
}
