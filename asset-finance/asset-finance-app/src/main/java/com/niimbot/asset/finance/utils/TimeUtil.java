package com.niimbot.asset.finance.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/2/6 下午6:53
 */
public class TimeUtil {

    /**
     * 获取当前时间的年月日，转成整型值
     * @param dateTime
     * @return
     */
    public static Integer formatDateInteger(LocalDateTime dateTime) {
        Integer result = 0;
        if (Objects.isNull(dateTime)) {
            return result;
        }

        String tempResult = formatTime(dateTime, "yyyyMMdd");
        return Integer.parseInt(tempResult);
    }

    /**
     * 获取当前时间的年月，转成整型值
     * @param dateTime
     * @return
     */
    public static Integer formatMonthInteger(LocalDateTime dateTime) {
        Integer result = 0;
        if (Objects.isNull(dateTime)) {
            return result;
        }

        String tempResult = formatTime(dateTime, "yyyyMM");
        return Integer.parseInt(tempResult);
    }

    public static String formatTime(LocalDateTime dateTime, String pattern) {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(pattern);
        return timeFormatter.format(dateTime);
    }

    public static String formatTime(LocalDate date, String pattern) {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(pattern);
        return timeFormatter.format(date);
    }

    /**
     * 日期转月份
     * 20220215 -> 2022-02
     * @param dateValue 日期或月份整型值
     * @param separator 拼接分隔符
     * @return
     */
    public static String transferMonth(Integer dateValue, String separator) {
        if (Objects.isNull(dateValue)) {
            return null;
        }

        String tempValue = String.valueOf(dateValue);
        return String.join(separator, tempValue.substring(0, 4), tempValue.substring(4,6));
    }

    public static LocalDate transferTime(Integer dateValue) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return LocalDate.parse(String.valueOf(dateValue), dateTimeFormatter);
    }

    public static LocalDateTime parseTime(String timeStr, String pattern) {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(timeStr, timeFormatter).atStartOfDay();
    }

    /**
     * 获取月初第一天起始时间
     * @param localDate
     * @return
     */
    public static LocalDateTime getStartDayOfMonth(LocalDate localDate) {
        return LocalDateTime.of(LocalDate.from(localDate.with(TemporalAdjusters.firstDayOfMonth())), LocalTime.MIN);
    }

    /**
     * 获取月初第一天起始时间
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getStartDayOfMonth(LocalDateTime localDateTime) {
        return LocalDateTime.of(LocalDate.from(localDateTime.with(TemporalAdjusters.firstDayOfMonth())), LocalTime.MIN);
    }

    public static void main(String[] args) {
        LocalDate minSettleDate = LocalDate.now();
        LocalDate maxSettleDate = LocalDate.now();
        System.out.println(!maxSettleDate.isBefore(minSettleDate));

        String time = "2023/11/7 23:30:49";
        LocalDateTime localDateTime = parseTime(time, "yyyy/M/d HH:mm:ss");
        System.out.println(formatTime(localDateTime, "yyyy-MM-dd"));
    }
}
