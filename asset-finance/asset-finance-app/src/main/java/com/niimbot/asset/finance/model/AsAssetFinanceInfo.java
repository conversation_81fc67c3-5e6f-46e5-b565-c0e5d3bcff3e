package com.niimbot.asset.finance.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 这个对象的@ApiModelProperty注解不要随意添加和删除，会影响到入账变更记录的展示
 * as_asset_finance_info
 * <AUTHOR>
@Data
@Accessors(chain = true)
@ApiModel(value = "AsAssetFinanceInfo对象", description = "入账信息对象")
@TableName(value = "as_asset_finance_info", autoResultMap = true)
public class AsAssetFinanceInfo implements Serializable {

    private static final long serialVersionUID = 4243542571858337172L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 所属组织
     */
    @ApiModelProperty("所属公司")
    private Long orgId;

    /**
     * 分摊部门
     */
    @ApiModelProperty("分摊部门")
    private Long settleOrgId;

    /**
     * 资产id
     */
    @ApiModelProperty("资产id")
    private Long assetId;

    /**
     * 资产编码
     */
    private String assetCode;

    /**
     * 资产分类
     */
    private Long assetCategory;

    /**
     * 折旧算法
     */
    @ApiModelProperty("折旧算法")
    private Integer strategy;

    /**
     * 入账类型
     */
    @ApiModelProperty("入账类型")
    private Integer settleType;

    /**
     * 入账日期
     */
    @ApiModelProperty("入账日期")
    private Integer billDate;

    /**
     * 本币原值
     */
    @ApiModelProperty("本币原值")
    private BigDecimal originPrice;

    /**
     * 可抵扣税额
     */
    @ApiModelProperty("可抵扣税额")
    private BigDecimal tax;

    /**
     * 残值率
     */
    @ApiModelProperty("残值率")
    private BigDecimal preResidualRate;

    /**
     * 预计净残值
     */
    @ApiModelProperty("预计净残值")
    private BigDecimal preRemainingAmount;

    /**
     * 预计使用期间数
     */
    @ApiModelProperty("预计使用期间数")
    private Integer preSettleCount;

    /**
     * 已折旧次数
     */
    @ApiModelProperty("已折旧次数")
    private Integer settleCount;

    /**
     * 累积折旧金额
     */
    @ApiModelProperty("累积折旧金额")
    private BigDecimal accumulationAmount;

    /**
     * 净值
     */
    @ApiModelProperty("净值")
    private BigDecimal remainingAmount;

    /**
     * 当前会计期间折旧金额
     */
    private BigDecimal currentSettleAmount;

    /**
     * 当前折旧次数
     */
    private Integer currentSettleCount;

    /**
     * 首次折旧月份
     */
    private Integer firstSettleMonth;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 资产入账状态
     */
    private Integer billStatus;

    /**
     * 资产折旧状态
     */
    private Integer settleStatus;

    /**
     * 上一次资产折旧状态
     */
    private Integer previousSettleStatus;

    /**
     * 资产处置时间
     */
    private LocalDateTime handleTime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    private Integer isDelete;
}