package com.niimbot.asset.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.finance.AssetFinanceInfoQueryDto;
import com.niimbot.asset.finance.model.AsAssetFinanceInfo;
import com.niimbot.finance.AssetInitFinanceInfoDto;
import com.niimbot.finance.AssetMachineAccountDto;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface AsAssetFinanceInfoMapper extends BaseMapper<AsAssetFinanceInfo> {

    /**
     * 分页查询资产入账信息
     * @param request
     * @param start
     * @param pageSize
     * @return
     */
    List<AsAssetFinanceInfo> pageQuery(@Param("request") AssetFinanceInfoQueryDto request, @Param("start") Long start, @Param("size") Long pageSize);

    /**
     * 重置资产入账信息：折旧次数、累积折旧金额、净值和当前会计期间折旧金额
     * @param orgId
     * @return
     */
    Integer resetSettleData(@Param("orgId") Long orgId);

    /**
     * 查询有入账资产的企业信息
     * 资产入账信息：没有删除-已入账-没有折旧终止
     * @return
     */
    List<Long> selectValidCompany();

    /**
     * 统计新增当期入账资产
     * @param orgId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer countCurrentAssetNum(@Param("orgId") Long orgId, @Param("startDate") Integer startDate, @Param("endDate") Integer endDate);

    /**
     * 统计新增历史入账资产
     * @param orgId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer countHistoryAssetNum(@Param("orgId") Long orgId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 统计之前入账资产数
     * @param orgId
     * @param startDate
     * @return
     */
    Integer countPreMonthAssetNum(@Param("orgId") Long orgId, @Param("startDate") Integer startDate);

    /**
     * 统计之前入账资产数
     * @param orgId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer countHandleAssetNum(@Param("orgId") Long orgId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查询入账资产列表
     * @param orgId
     * @param billStatusList
     * @return
     */
    List<Long> selectAssetId(@Param("orgId") Long orgId, @Param("billStatusList") List<Integer> billStatusList);

    /**
     * 查询待入账资产
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AsAsset> selectWaitFinanceAsset(IPage<AssetFinanceInfoQueryDto> page, @Param("param") AssetFinanceInfoQueryDto queryDto);

    /**
     * 查询待入账资产
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AssetFinanceInfoDto> selectAssetFinance(IPage<AssetFinanceInfoQueryDto> page, @Param("param") AssetFinanceInfoQueryDto queryDto);

    /**
     * 查询初始入账列表
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AssetInitFinanceInfoDto> selectAssetInitFinance(IPage<AssetFinanceInfoQueryDto> page, @Param("param") AssetFinanceInfoQueryDto queryDto);

    /**
     * 查询台账
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AssetMachineAccountDto> selectMachineAccount(IPage<AssetFinanceInfoQueryDto> page, @Param("param") AssetFinanceInfoQueryDto queryDto);
}