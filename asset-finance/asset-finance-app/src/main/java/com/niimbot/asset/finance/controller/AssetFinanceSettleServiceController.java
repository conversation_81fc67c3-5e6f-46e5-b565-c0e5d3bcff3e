package com.niimbot.asset.finance.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.finance.service.AssetSettleDetailService;
import com.niimbot.asset.finance.service.AssetSettleSummaryService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.finance.*;
import com.niimbot.asset.finance.service.AssetBillInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/2/10 上午9:46
 */
@Api(tags = "资产财务折旧")
@RestController
@RequestMapping("server/finance/settle/")
public class AssetFinanceSettleServiceController {

    @Autowired
    private AssetBillInfoService assetBillInfoService;
    @Autowired
    private AssetSettleDetailService settleDetailService;
    @Autowired
    private AssetSettleSummaryService settleSummaryService;

    @ApiOperation("折旧结账列表")
    @GetMapping("billInfo")
    public PageUtils<AssetBillInfoDto> billInfo(AssetBillInfoQueryDto queryDto) {
        return assetBillInfoService.queryBillInfo(queryDto);
    }

    @ApiOperation("查询企业结算会计期间信息")
    @GetMapping("settleDateInfo")
    public OrgSettleDateInfoDto querySettleDateInfo(Long orgId) {
        return assetBillInfoService.querySettleDateInfo(orgId);
    }

    @ApiOperation("折旧计提")
    @PostMapping("accrual")
    public AssetAccrualDto depreciationAccrual(@RequestBody @Validated AssetAccrualSubmitDto accrualSubmitDto) {
        return assetBillInfoService.depreciationAccrual(accrualSubmitDto);
    }

    @ApiOperation("确认折旧计提")
    @PostMapping("confirmAccrual")
    public Boolean confirmAccrual(@RequestBody @Validated AssetAccrualSubmitDto accrualSubmitDto) {
        return assetBillInfoService.submitSettle(accrualSubmitDto);
    }

    @ApiOperation("折旧明细分页查询")
    @PostMapping("settleDetail")
    public IPage<SettleDetailDto> settleDetail(@RequestBody @Validated SettleDetailQueryDto queryDto) {
        return settleDetailService.pageQuery(queryDto);
    }

    @ApiOperation("折旧汇总分页查询--所属公司")
    @PostMapping("settleSummary")
    public IPage<SettleSummaryDto> settleSummary(@RequestBody @Validated SettleSummaryQueryDto queryDto) {
        return settleSummaryService.pageQueryOrg(queryDto);
    }

    @ApiOperation("折旧汇总分页查询--分摊部门")
    @PostMapping("settleSummaryDept")
    public IPage<SettleSummaryDeptDto> settleSummaryDept(@RequestBody @Validated SettleSummaryQueryDto queryDto) {
        return settleSummaryService.pageQueryDept(queryDto);
    }

    @ApiOperation("查询企业会计期间")
    @GetMapping("queryOrgSettleMonth")
    public List<String> queryOrgSettleMonth(SettleMonthQueryDto queryDto) {
        return assetBillInfoService.queryOrgSettleMonth(queryDto);
    }

    @ApiOperation("手动计提提交")
    @PostMapping("confirmDepreciation")
    public Boolean confirmDepreciation(@RequestBody @Validated AssetDepreciationCalculateDto calculateDto) {
        assetBillInfoService.confirmDepreciation(calculateDto);
        return Boolean.TRUE;
    }
}
