package com.niimbot.asset.finance.utils;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.finance.model.AsAssetFinanceInfo;
import com.niimbot.finance.ComparisonDto;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/9 上午10:38
 */
@Slf4j
public class CompareUtil {

    private final static String DATE_FORMATTER_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     * 比较两个实体属性值，返回有差异的属性
     *
     * @param obj1 进行属性比较的对象1
     * @param obj2 进行属性比较的对象2
     * @return
     */
    public static List<ComparisonDto> compareFields(Object obj1, Object obj2, Class clazz) {
        List<ComparisonDto> list = new ArrayList<>();
        try {
            //获取带有ApiModelProperty注解字段
            List<String> fieldList = getCompareFields(clazz);

            //获取字段描述信息
            Map<String, String> descMap = getFieldSwaggerValue(clazz);

            // 只有两个对象都是同一类型的才有可比性
            if (obj1.getClass() == obj2.getClass()) {
                Class claz = obj1.getClass();
                // 获取object的属性描述
                PropertyDescriptor[] pds = Introspector.getBeanInfo(claz, Object.class).getPropertyDescriptors();
                for (PropertyDescriptor pd : pds) {
                    String name = pd.getName();
                    String label = descMap.get(name);
                    if (CollUtil.isNotEmpty(fieldList) && !fieldList.contains(name)) {
                        continue;
                    }
                    Method readMethod = pd.getReadMethod();
                    Object o1 = readMethod.invoke(obj1);
                    Object o2 = readMethod.invoke(obj2);
                    if (Objects.isNull(o1) && Objects.isNull(o2)) {
                        continue;
                    }
                    if (null == o1 || null == o2) {
                        setResult(o1, o2, name, label, list);
                        continue;
                    }
                    if (compareField(o1, o2)) {
                        setResult(o1, o2, name, label, list);
                    }
                }
            } else {
                throw new Exception("对象类型不一致");
            }
        } catch (Exception e) {
            log.error("compareUtils compareFields error! param1=[{}] param2=[{}] exception ", JSONObject.toJSONString(obj1), JSONObject.toJSONString(obj2), e);
        }
        return list;
    }

    /**
     * 获取比较的类中字段
     * @param objectClass
     * @return
     */
    private static List<String> getCompareFields(Class objectClass) {
        Field[] fields = objectClass.getDeclaredFields();
        List<String> map = new ArrayList<>();
        for (Field f : fields) {
            if (f.isAnnotationPresent(ApiModelProperty.class)) {
                map.add(f.getName());
            }
        }
        return map;
    }

    /**
     * 获取实体类字段的描述
     * @param clazz
     * @return
     */
    private static Map<String, String> getFieldSwaggerValue(Class clazz) {
        Field[] fields = clazz.getDeclaredFields();
        Map<String, String> map = new HashMap<>();
        for (Field f : fields) {
            boolean annotationPresent2 = f.isAnnotationPresent(ApiModelProperty.class);
            if (annotationPresent2) {
                ApiModelProperty name = f.getAnnotation(ApiModelProperty.class);
                String nameStr = name.value();
                map.put(f.getName(), nameStr);
            }
        }
        return map;
    }

    private static void setResult(Object o1, Object o2, String name, String label, List<ComparisonDto> list) {
        Object ot = null;
        String r1 = null, r2 = null;
        if (!Objects.isNull(o1)) {
            ot = o1;
        } else if (!Objects.isNull(o2)) {
            ot = o2;
        }
        if (ot instanceof String) {
            r1 = objectToString(o1);
            r2 = objectToString(o2);
        } else if (ot instanceof BigDecimal) {
            r1 = objectToString(o1);
            r2 = objectToString(o2);
        } else if (ot instanceof Integer) {
            r1 = objectToString(o1);
            r2 = objectToString(o2);
        } else if (ot instanceof Boolean) {
            r1 = booleanToString(o1);
            r2 = booleanToString(o2);
        } else if (ot instanceof Long) {
            r1 = objectToString(o1);
            r2 = objectToString(o2);
        } else if (ot instanceof LocalDateTime) {
            r1 = localDateTimeToString(o1);
            r2 = localDateTimeToString(o2);
        }
        ComparisonDto result = new ComparisonDto();
        result.setKey(name);
        result.setLabel(label);
        result.setPrevious(r1);
        result.setLater(r2);
        list.add(result);
    }

    private static boolean compareField(Object o1, Object o2) {
        boolean sign = false;
        Object ot = null;
        if (!Objects.isNull(o1)) {
            ot = o1;
        } else if (!Objects.isNull(o2)) {
            ot = o2;
        }
        if (ot instanceof String) {
            sign = !o1.equals(o2);
        } else if (ot instanceof BigDecimal) {
            sign = ((BigDecimal) o1).compareTo((BigDecimal) o2) != 0;
        } else if (ot instanceof Integer) {
            sign = ((Integer) o1).compareTo((Integer) o2) != 0;
        } else if (ot instanceof Boolean) {
            sign = ((Boolean) o1).compareTo((Boolean) o2) != 0;
        } else if (ot instanceof Long) {
            sign = ((Long) o1).compareTo((Long) o2) != 0;
        } else if (ot instanceof LocalDateTime) {
            sign = ((LocalDateTime) o1).compareTo((LocalDateTime) o2) != 0;
        }
        return sign;
    }

    private static String objectToString(Object obj) {
        if (null == obj) {
            return "";
        } else {
            return obj.toString();
        }
    }

    private static String booleanToString(Object obj) {
        if (null == obj) {
            return null;
        } else {
            return (Boolean) obj ? "是" : "否";
        }
    }

    private static String localDateTimeToString(Object obj) {
        if (null == obj) {
            return null;
        } else {
            DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMATTER_SECOND);
            LocalDateTime ld = (LocalDateTime) obj;
            return df.format(ld);
        }
    }

    public static void main(String[] args) throws Exception {
        AsAssetFinanceInfo financeInfo1 = new AsAssetFinanceInfo();
        financeInfo1.setId(123L).setBizCode("AAA").setStrategy(1).setCreateTime(LocalDateTime.now());
        AsAssetFinanceInfo financeInfo2 = new AsAssetFinanceInfo();
        LocalDateTime preDate = LocalDateTime.now().plusDays(1L);
        financeInfo2.setId(234L).setBizCode("BBB").setStrategy(2).setCreateTime(preDate);
        List<ComparisonDto> result = compareFields(financeInfo1, financeInfo2, AsAssetFinanceInfo.class);
        System.out.println(JSONObject.toJSONString(result));
    }

}
