package com.niimbot.asset.finance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.finance.enums.DepreciationConfigStatusEnum;
import com.niimbot.asset.finance.mapper.AsAssetDepreciationConfigMapper;
import com.niimbot.asset.finance.model.AsAssetDepreciationConfig;
import com.niimbot.asset.finance.model.AsDepreciationOperationLog;
import com.niimbot.asset.finance.service.AssetBillInfoService;
import com.niimbot.asset.finance.service.DepreciationConfigService;
import com.niimbot.asset.finance.service.DepreciationOperationLogService;
import com.niimbot.asset.finance.utils.TimeUtil;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.SmsCodeService;
import com.niimbot.finance.DepreciationConfigDto;
import com.niimbot.finance.DepreciationConfigParamDto;
import com.niimbot.finance.DisableDepreciationConfigDto;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/6 下午6:02
 */
@Lazy
@Slf4j
@Service
public class DepreciationConfigServiceImpl extends ServiceImpl<AsAssetDepreciationConfigMapper, AsAssetDepreciationConfig> implements DepreciationConfigService {

    private ExecutorService executorService;

    @Autowired
    private AsCusEmployeeService cusEmployeeService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private SmsCodeService smsCodeService;

    @Autowired
    private DepreciationOperationLogService operationLogService;

    @Autowired
    private AssetBillInfoService billInfoService;

    @PostConstruct
    public void init() {
        executorService = AssetThreadPoolExecutorManager.newThreadPool("depreciationConfigService", 3, 3, 1000);
    }

    @Override
    public Boolean saveConfig(DepreciationConfigDto configDto) {
        //校验该组织是否已存在折旧方案配置
        List<AsAssetDepreciationConfig> depreciationConfigList = this.getBaseMapper()
                .selectList(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                        .eq(AsAssetDepreciationConfig::getOrgId, configDto.getOrgId())
                        .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (CollUtil.isNotEmpty(depreciationConfigList)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置已存在");
        }

        //校验组织为企业
        if (!verifyOrg(configDto.getOrgId())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置仅允许企业");
        }

        //校验资产分类配置是否重复
        if (verifyRepeat(configDto)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置错误，资产分类重复");
        }

        AsAssetDepreciationConfig asDepreciationConfig = new AsAssetDepreciationConfig();
        BeanUtils.copyProperties(configDto, asDepreciationConfig);
        //生成业务编码
        asDepreciationConfig.setBizCode(IdUtil.simpleUUID());
        //资产来源，逗号拼接
        if (CollUtil.isNotEmpty(configDto.getAssetOrigin())) {
            asDepreciationConfig.setAssetOrigin(String.join(",", configDto.getAssetOrigin()));
        }
        //设置是否手动折旧
        asDepreciationConfig.setManualSettle(configDto.getManualSettle() ? 1 : 0);
        //设置首次折旧日期为当前年月
        asDepreciationConfig.setFirstSettleTime(TimeUtil.formatDateInteger(LocalDateTime.now()));
        //折旧方案参数
        asDepreciationConfig.setDepreciationParam(JSONObject.toJSONString(configDto.getDepreciationParam()));
        save(asDepreciationConfig);
        return Boolean.TRUE;
    }

    @Override
    public Boolean editConfig(DepreciationConfigDto configDto) {
        //校验该折旧方案配置是否存在
        AsAssetDepreciationConfig depreciationConfig = this.getBaseMapper()
                .selectOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                        .eq(AsAssetDepreciationConfig::getBizCode, configDto.getBizCode())
                        .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }

        //校验组织为企业
        if (!verifyOrg(configDto.getOrgId())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置仅允许企业");
        }

        //校验资产分类配置是否重复
        if (verifyRepeat(configDto)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置错误，资产分类重复");
        }

        //修改数据库记录
        AsAssetDepreciationConfig modifyEntity = new AsAssetDepreciationConfig();
        BeanUtils.copyProperties(configDto, modifyEntity);
        //资产来源，逗号拼接
        if (CollUtil.isNotEmpty(configDto.getAssetOrigin())) {
            modifyEntity.setAssetOrigin(String.join(",", configDto.getAssetOrigin()));
        } else {
            modifyEntity.setAssetOrigin("");
        }
        //设置是否手动折旧
        modifyEntity.setManualSettle(configDto.getManualSettle() ? 1 : 0);
        //折旧方案参数
        modifyEntity.setDepreciationParam(JSONObject.toJSONString(configDto.getDepreciationParam()));
        modifyEntity.setStrategy(null);//折旧算法不允许修改
        modifyEntity.setFirstSettleTime(null);//首次结算日期不可修改
        return update(modifyEntity, Wrappers.lambdaUpdate(AsAssetDepreciationConfig.class).eq(AsAssetDepreciationConfig::getBizCode, configDto.getBizCode()));
    }

    @Override
    public DepreciationConfigDto queryByOrgId(Long orgId) {
        if (Objects.isNull(orgId)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID);
        }

        //查询折旧方案配置
        AsAssetDepreciationConfig depreciationConfig = this.getBaseMapper()
                .selectOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                        .eq(AsAssetDepreciationConfig::getOrgId, orgId)
                        .in(AsAssetDepreciationConfig::getStatus,
                                Arrays.asList(DepreciationConfigStatusEnum.NORMAL.getCode(), DepreciationConfigStatusEnum.CLOSING.getCode())));
        if (Objects.isNull(depreciationConfig)) {
            //详情直接返回空值
            return null;
        }

        //结果转换
        DepreciationConfigDto result = new DepreciationConfigDto();
        BeanUtils.copyProperties(depreciationConfig, result);
        if (StrUtil.isNotBlank(depreciationConfig.getAssetOrigin())) {
            result.setAssetOrigin(Arrays.asList(depreciationConfig.getAssetOrigin().split(",")));
        }
        //是否手动折旧转换
        result.setManualSettle(depreciationConfig.getManualSettle() > 0 ? Boolean.TRUE : Boolean.FALSE);
        result.setFirstSettleMonth(TimeUtil.transferMonth(depreciationConfig.getFirstSettleTime(), "-"));
        result.setDepreciationParam(JSONObject.parseArray(depreciationConfig.getDepreciationParam(), DepreciationConfigParamDto.class));
        return result;
    }

    @Override
    public Boolean verificationCode() {
        //获取超管信息
        AsCusEmployee administrator = cusEmployeeService.getAdministrator();
        if (Objects.isNull(administrator) || StrUtil.isBlank(administrator.getMobile())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "超管手机号不存在");
        }

        log.info("depreciationConfigService verificationCode mobile=[{}]", administrator.getMobile());
        AsCusEmployee employee = cusEmployeeService.getOne(Wrappers.lambdaQuery(AsCusEmployee.class).eq(AsCusEmployee::getMobile, administrator.getMobile()));
        smsCodeService.sendSmsCode(StringUtils.isEmpty(employee.getNationalCode())?"86":employee.getNationalCode().replace("+",""), administrator.getMobile());
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeConfig(DisableDepreciationConfigDto configDto) {
        //校验该折旧方案配置是否存在
        AsAssetDepreciationConfig depreciationConfig = this.getBaseMapper()
                .selectOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                        .eq(AsAssetDepreciationConfig::getBizCode, configDto.getBizCode())
                        .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }

        //saas才需要校验，钉钉版本和企业微信不需要校验
        if (Edition.isSaas()) {
            if (StrUtil.isBlank(configDto.getVerificationCode())) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "短信验证码不能为空");
            }
            //获取超管信息
            AsCusEmployee administrator = cusEmployeeService.getAdministrator();
            //校验验证码信息
            Boolean smsCodeResult = smsCodeService.checkSmsCode(administrator.getMobile(), configDto.getVerificationCode());
            if (!smsCodeResult) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "验证码错误");
            }
        }

        //添加停用操作记录
        AsDepreciationOperationLog operationLog = new AsDepreciationOperationLog();
        operationLog.setBizCode(depreciationConfig.getBizCode())
                .setActionName("企业停用折旧方案").setActionMethod("removeConfig").setActionParam(JSONObject.toJSONString(configDto))
                .setRemark("企业停用折旧方案").setCreateBy(LoginUserThreadLocal.getCurrentUserId()).setCreateTime(LocalDateTime.now());
        operationLogService.save(operationLog);

        //异步逻辑删除财务数据(入账信息、折旧明细信息和折旧汇总信息)
        executorService.submit(() -> billInfoService.clearAllSettleData(depreciationConfig.getOrgId()));

        //停用折旧方案配置
        return update(Wrappers.lambdaUpdate(AsAssetDepreciationConfig.class)
                .set(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.CLOSING.getCode())
                .eq(AsAssetDepreciationConfig::getId, depreciationConfig.getId()));
    }

    @Override
    public void verifyConfig(Long orgId) {
        //查询折旧方案是否正常启用中
        AsAssetDepreciationConfig depreciationConfig = getOne(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getOrgId, orgId)
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode()));
        if (Objects.isNull(depreciationConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "折旧方案配置不存在");
        }
    }

    /**
     * 判断资产分类是否有重复
     * @param configDto
     * @return true:有重复 false:没有重复
     */
    private Boolean verifyRepeat(DepreciationConfigDto configDto) {
        //集合小于两个就不用进行判重，直接就不会有重复
        if (configDto.getDepreciationParam().size() < 2) {
            return Boolean.FALSE;
        }

        for (int i = 0; i < configDto.getDepreciationParam().size() - 1; i++) {
            //参数配置有2个及以上，其中之一资产分类配置为全部时，这个时候就一定有重复
            if (CollUtil.isEmpty(configDto.getDepreciationParam().get(i).getAssetCategory())
                    || CollUtil.isEmpty(configDto.getDepreciationParam().get(i + 1).getAssetCategory())) {
                return Boolean.TRUE;
            }

            //两两求交集，其中一个有交集就表示资产分类有重叠，立即返回
            for(int j = i; j < configDto.getDepreciationParam().size() - 1; j++) {
                //求交集
                boolean disjoint = Collections.disjoint(configDto.getDepreciationParam().get(j).getAssetCategory(), configDto.getDepreciationParam().get(j + 1).getAssetCategory());
                if (!disjoint) {
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 校验折旧方案组织是否为公司
     * @param orgId
     * @return
     */
    private Boolean verifyOrg(Long orgId) {
        if (Objects.isNull(orgId)) {
            return Boolean.FALSE;
        }

        AsOrg org = orgService.getById(orgId);
        //orgType  1：公司 2：部门
        if (Objects.nonNull(org.getOrgType()) && org.getOrgType() == 1) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<Long> queryAllOrgId() {
        List<AsAssetDepreciationConfig> depreciationConfigList = this.getBaseMapper().selectList(Wrappers.lambdaQuery(AsAssetDepreciationConfig.class)
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode())
                .select(AsAssetDepreciationConfig::getOrgId));
        if (CollUtil.isNotEmpty(depreciationConfigList)) {
            return depreciationConfigList.stream().map(AsAssetDepreciationConfig::getOrgId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public Integer queryMinSettleTime(List<Long> orgIdList) {
        return this.getBaseMapper().selectMinSettleTime(orgIdList);
    }

    @Override
    public Integer queryConfigByOrg(List<Long> orgIds) {
        return Convert.toInt(this.count(new QueryWrapper<AsAssetDepreciationConfig>().lambda()
                .in(AsAssetDepreciationConfig::getOrgId, orgIds)
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode())));
    }
}
