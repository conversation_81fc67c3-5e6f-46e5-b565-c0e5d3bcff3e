package com.niimbot.asset.finance.controller;

import com.niimbot.finance.DisableDepreciationConfigDto;
import com.niimbot.finance.OrgSettleInfoDto;
import com.niimbot.finance.DepreciationConfigDto;
import com.niimbot.asset.finance.service.AssetBillInfoService;
import com.niimbot.asset.finance.service.DepreciationConfigService;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/6 下午5:37
 */
@Api(tags = "折旧方案配置")
@RestController
@RequestMapping("server/finance/depreciation/")
public class DepreciationConfigServiceController {

    @Autowired
    private DepreciationConfigService depreciationConfigService;
    @Autowired
    private AssetBillInfoService billInfoService;

    @PostMapping("config")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    @ApiOperation(value = "折旧方案配置")
    public Boolean saveConfig(@RequestBody @Validated(Insert.class) DepreciationConfigDto configDto) {
        return depreciationConfigService.saveConfig(configDto);
    }

    @ApiOperation(value = "编辑折旧方案配置")
    @PostMapping(value = "edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean modifyConfig(@RequestBody @Validated(Update.class) DepreciationConfigDto configDto) {
        return depreciationConfigService.editConfig(configDto);
    }

    @ApiOperation("折旧方案配置详情")
    @GetMapping("{orgId}")
    public DepreciationConfigDto detail(@PathVariable Long orgId) {
        return depreciationConfigService.queryByOrgId(orgId);
    }

    @ApiOperation("获取反启用数据")
    @GetMapping("querySettleInfo/{orgId}")
    public OrgSettleInfoDto querySettleInfo(@PathVariable Long orgId) {
        return billInfoService.querySettleInfo(orgId);
    }

    @ApiOperation("反启用获取验证码")
    @GetMapping("verificationCode")
    public Boolean verificationCode() {
        return depreciationConfigService.verificationCode();
    }

    @ApiOperation("反启用")
    @PostMapping("removeConfig")
    @ResultMessage(ResultConstant.DISABLE_SUCCESS)
    public Boolean removeConfig(@RequestBody @Validated DisableDepreciationConfigDto configDto) {
        return depreciationConfigService.removeConfig(configDto);
    }

    @ApiOperation("查询所有组织id")
    @GetMapping("queryAllOrgId")
    public List<Long> queryAllOrgId() {
        return depreciationConfigService.queryAllOrgId();
    }

}
