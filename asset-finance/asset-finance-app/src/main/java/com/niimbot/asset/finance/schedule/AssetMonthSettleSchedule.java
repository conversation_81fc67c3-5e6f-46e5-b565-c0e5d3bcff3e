package com.niimbot.asset.finance.schedule;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.finance.constants.DistributeLockConstant;
import com.niimbot.asset.finance.enums.AssetBillStatusEnum;
import com.niimbot.asset.finance.enums.SettleTaskTypeEnum;
import com.niimbot.asset.finance.model.AsAssetBillInfo;
import com.niimbot.asset.finance.model.AsAssetSettleTask;
import com.niimbot.asset.finance.service.AssetBillInfoService;
import com.niimbot.asset.finance.service.AssetFinanceInfoService;
import com.niimbot.asset.finance.service.AssetSettleTaskService;
import com.niimbot.asset.finance.utils.TimeUtil;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.finance.AssetDepreciationCalculateDto;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 资产折旧月结任务，生成所有企业需要折旧的子任务，跑所有公司的
 * <AUTHOR>
 * @date 2023/2/13 上午9:44
 */
@Slf4j
@Component
public class AssetMonthSettleSchedule {

    @Autowired
    private AssetSettleTaskService settleTaskService;
    @Autowired
    private AssetBillInfoService billInfoService;
    @Autowired
    private AssetFinanceInfoService financeInfoService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CacheResourceUtil cacheResourceUtil;

    /**
     * 每月1号凌晨开始跑财务折旧计提结账
     */
    @Scheduled(cron = "0 0 0 1 * ?")
    @Async("asyncScheduledThreadPool")
    public void monthSettle() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        long startTime = System.currentTimeMillis();
        log.info("assetDepreciationSettleSchedule monthSettle start! currentTime=[{}]", startTime);

        //获取分布式锁，这里主要是控制任务的并发，仅允许一台机器跑定时任务
        RLock lock = redissonClient.getLock(DistributeLockConstant.DEPRECIATION_MONTH_SETTLE);
        boolean isLocked = Boolean.FALSE;
        try {
            isLocked = lock.tryLock(0, 3, TimeUnit.MINUTES);
            if (!isLocked) {
                log.info("assetDepreciationSettleSchedule monthSettle end! Acquire lock fail! currentTime=[{}]", System.currentTimeMillis());
                return;
            }

            //当前结算会计期间
            int settleMonth = TimeUtil.formatMonthInteger(LocalDateTime.now().plusMonths(-1L));

            //所有有资产入账信息的企业id列表
            List<Long> validCompanyIdList = financeInfoService.queryValidCompany();
            if (CollUtil.isEmpty(validCompanyIdList)) {
                log.info("assetDepreciationSettleSchedule monthSettle end! valid company empty!");
                return ;
            }
            log.info("assetDepreciationSettleSchedule monthSettle valid orgIdList=[{}]", JSONObject.toJSONString(validCompanyIdList));

            //月结任务参数
            AssetDepreciationCalculateDto taskParameters = new AssetDepreciationCalculateDto()
                    .setSettleMonth(settleMonth).setAutoCalculate(Boolean.TRUE).setIsSettle(Boolean.TRUE);

            //当前会计期间已结账的企业
            List<Long> checkedOutCompanyIdList = null;
            List<AsAssetBillInfo> billInfoList = billInfoService.list(Wrappers.lambdaQuery(AsAssetBillInfo.class)
                    .eq(AsAssetBillInfo::getSettleMonth, settleMonth).in(AsAssetBillInfo::getOrgId, validCompanyIdList)
                    .eq(AsAssetBillInfo::getStatus, AssetBillStatusEnum.CHECKED_OUT.getCode()).eq(AsAssetBillInfo::getIsDelete, 0));
            if (CollUtil.isNotEmpty(billInfoList)) {
                checkedOutCompanyIdList = billInfoList.stream().map(AsAssetBillInfo::getOrgId).collect(Collectors.toList());
                log.info("assetDepreciationSettleSchedule monthSettle checked out orgIdList=[{}]", JSONObject.toJSONString(checkedOutCompanyIdList));
            }
            List<Long> finalCheckedOutCompanyIdList = checkedOutCompanyIdList;

            //需要进行月结的任务
            List<AsAssetSettleTask> settleTaskList = validCompanyIdList.stream()
                    .filter(item -> CollUtil.isEmpty(finalCheckedOutCompanyIdList) || !finalCheckedOutCompanyIdList.contains(item))
                    .map(item -> {
                        taskParameters.setOrgId(item);
                        return new AsAssetSettleTask().setOrgId(item).setOrgName(cacheResourceUtil.getOrgName(item))
                                .setTaskType(SettleTaskTypeEnum.MONTH_SETTLE.getTaskType()).setTaskName(SettleTaskTypeEnum.MONTH_SETTLE.getTaskDesc())
                                .setTaskParameters(JSONObject.toJSONString(taskParameters)).setCreateBy(0L);
                    }).collect(Collectors.toList());

            //写入到任务表
            if (CollUtil.isNotEmpty(settleTaskList)) {
                settleTaskService.saveBatch(settleTaskList);
            }
            log.info("assetDepreciationSettleSchedule monthSettle end! saveSize=[{}]", settleTaskList.size());
        } catch (Exception e) {
            log.error("assetDepreciationSettleSchedule monthSettle acquire lock error! exception ", e);
        } finally {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("assetDepreciationSettleSchedule monthSettle end! All data processed! costTime=[{}]", System.currentTimeMillis() - startTime);
    }
}
