package com.niimbot.asset.finance.abs.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.finance.enums.AssetFinanceStatusEnum;
import com.niimbot.asset.finance.enums.DepreciationConfigStatusEnum;
import com.niimbot.asset.finance.mapper.AsAssetDepreciationConfigMapper;
import com.niimbot.asset.finance.mapper.AsAssetFinanceInfoMapper;
import com.niimbot.asset.finance.model.AsAssetDepreciationConfig;
import com.niimbot.asset.finance.model.AsAssetFinanceInfo;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.system.abs.AssetFinanceInfoAbs;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.finance.FinanceStatisticsQueryDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/27 下午4:48
 */
@Slf4j
@RestController
@RequestMapping("/client/abs/finance/assetFinanceInfoAbsImpl/")
@RequiredArgsConstructor
public class AssetFinanceInfoAbsImpl implements AssetFinanceInfoAbs {

    @Autowired
    private AsAssetFinanceInfoMapper financeInfoMapper;

    @Autowired
    private AsAssetDepreciationConfigMapper depreciationConfigMapper;

    @Override
    public AssetFinanceInfoDto queryAssetFinanceInfo(Long assetId) {
        if (Objects.isNull(assetId)) {
            return null;
        }

        AsAsset asset = Db.getById(assetId, AsAsset.class);
        if (Objects.isNull(asset) || Objects.isNull(asset.getAssetData())) {
            return null;
        }

        //查询资产所属组织信息，组织可能为公司，也有可能是部门
        AsOrg org = Db.getById(asset.getAssetData().getLong("orgOwner"), AsOrg.class);
        if (Objects.isNull(org)) {
            return null;
        }
        long orgId = org.getCompanyOwner();

        //查询未删除的资产入账信息
        List<AsAssetFinanceInfo> assetFinanceInfoList = financeInfoMapper.selectList(Wrappers.lambdaQuery(AsAssetFinanceInfo.class)
                .eq(AsAssetFinanceInfo::getAssetId, asset.getId())
                .eq(AsAssetFinanceInfo::getOrgId, orgId)
                .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getIsDelete, 0));
        if (CollUtil.isEmpty(assetFinanceInfoList)) {
            return null;
        }
        AssetFinanceInfoDto result = new AssetFinanceInfoDto();
        BeanUtils.copyProperties(assetFinanceInfoList.get(0), result);
        return result;
    }

    @Override
    public Integer countAssetFinanceByOrg(FinanceStatisticsQueryDto queryDto) {
        Integer result = 0;
        if (Objects.isNull(queryDto) || CollUtil.isEmpty(queryDto.getOrgIds())) {
            return result;
        }

        result = Convert.toInt(financeInfoMapper.selectCount(new QueryWrapper<AsAssetFinanceInfo>().lambda()
                        .in(AsAssetFinanceInfo::getSettleOrgId, queryDto.getOrgIds())
                        .eq(AsAssetFinanceInfo::getBillStatus, AssetFinanceStatusEnum.CREDITED.getCode())
                .eq(AsAssetFinanceInfo::getIsDelete, 0)));
        return result;
    }

    @Override
    public Integer countDepreciationConfigByOrg(FinanceStatisticsQueryDto queryDto) {
        Integer result = 0;
        if (Objects.isNull(queryDto) || CollUtil.isEmpty(queryDto.getOrgIds())) {
            return result;
        }

        //统计数量
        result = Convert.toInt(depreciationConfigMapper.selectCount(new QueryWrapper<AsAssetDepreciationConfig>().lambda()
                .in(AsAssetDepreciationConfig::getOrgId, queryDto.getOrgIds())
                .eq(AsAssetDepreciationConfig::getStatus, DepreciationConfigStatusEnum.NORMAL.getCode())));
        return result;
    }
}
