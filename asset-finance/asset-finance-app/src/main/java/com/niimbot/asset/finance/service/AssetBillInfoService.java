package com.niimbot.asset.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.finance.model.AsAssetBillInfo;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.finance.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午7:05
 */
public interface AssetBillInfoService extends IService<AsAssetBillInfo> {

    /**
     * 查询企业折旧结账信息
     * @param queryDto
     * @return
     */
    PageUtils<AssetBillInfoDto> queryBillInfo(AssetBillInfoQueryDto queryDto);

    /**
     * 查询企业结算会计期间信息
     * @param orgId
     * @return
     */
    OrgSettleDateInfoDto querySettleDateInfo(Long orgId);

    /**
     * 折旧确认
     * @param calculateDto
     */
    void confirmDepreciation(AssetDepreciationCalculateDto calculateDto);

    /**
     * 结账确认
     * @param calculateDto
     */
    void confirmSettle(AssetDepreciationCalculateDto calculateDto);

    /**
     * 折旧计提
     * @param accrualSubmitDto
     * @return
     */
    AssetAccrualDto depreciationAccrual(AssetAccrualSubmitDto accrualSubmitDto);

    /**
     * 提交计提或提交结账
     * @param accrualSubmitDto
     * @return
     */
    Boolean submitSettle(AssetAccrualSubmitDto accrualSubmitDto);

    /**
     * 获取组织结算期间信息
     * @param orgId
     * @return
     */
    OrgSettleInfoDto querySettleInfo(Long orgId);

    /**
     * 清除组织下所有的财务数据
     * @param orgId
     * @return
     */
    Boolean clearAllSettleData(Long orgId);

    /**
     * 查询企业会计期间信息
     * @param queryDto
     * @return
     */
    List<String> queryOrgSettleMonth(SettleMonthQueryDto queryDto);
}
