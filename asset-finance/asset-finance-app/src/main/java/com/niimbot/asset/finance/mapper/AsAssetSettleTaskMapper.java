package com.niimbot.asset.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.finance.SettleTaskQueryDto;
import com.niimbot.asset.finance.model.AsAssetSettleTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsAssetSettleTaskMapper extends BaseMapper<AsAssetSettleTask> {

    /**
     * 查询待执行的任务
     * @param queryDto
     * @return
     */
    List<AsAssetSettleTask> selectWaitExecute(@Param("param") SettleTaskQueryDto queryDto);
}