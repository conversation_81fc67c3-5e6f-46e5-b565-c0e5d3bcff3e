package com.niimbot.asset.finance.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.finance.constants.DistributeLockConstant;
import com.niimbot.asset.finance.model.AsAssetBillInfo;
import com.niimbot.asset.finance.model.AsAssetSettleDetail;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;
import com.niimbot.asset.finance.service.AssetBillInfoService;
import com.niimbot.asset.finance.service.AssetFinanceInfoService;
import com.niimbot.asset.finance.service.AssetSettleDetailService;
import com.niimbot.asset.finance.service.AssetSettleSummaryService;
import com.niimbot.asset.framework.support.Edition;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 资产财务数据删除任务
 * <AUTHOR>
 * @date 2023/2/13 下午5:08
 */
@Slf4j
@Component
public class DeleteSettleDataSchedule {

    @Autowired
    private AssetBillInfoService billInfoService;
    @Autowired
    private AssetFinanceInfoService financeInfoService;
    @Autowired
    private AssetSettleDetailService settleDetailService;
    @Autowired
    private AssetSettleSummaryService settleSummaryService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 物理删除资产财务折旧数据，每天晚上10点去跑
     */
    @Scheduled(cron = "0 0 22 * * ?")
    @Async("asyncScheduledThreadPool")
    public void removeSettleData() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        log.info("deleteSettleDataSchedule removeSettleData start!");
        //获取分布式锁，这里主要是控制任务的并发，仅允许一台机器跑定时任务
        RLock lock = redissonClient.getLock(DistributeLockConstant.REMOVE_SETTLE_DATA);
        boolean isLocked;
        try {
            isLocked = lock.tryLock(0, 3, TimeUnit.MINUTES);
            if (!isLocked) {
                log.info("companyAssetSettleJob run end! Acquire lock fail! currentTime=[{}]", System.currentTimeMillis());
                return;
            }

            //查询需要清除结算数据的企业id列表
            log.info("deleteSettleDataSchedule removeSettleData start!");
            List<Long> companyIdList = financeInfoService.queryValidCompany();
            if (CollUtil.isEmpty(companyIdList)) {
                log.info("deleteSettleDataSchedule removeSettleData end! valid companyId empty!");
                return ;
            }

            for (Long item : companyIdList) {
                //物理删除企业资产结账信息表
                billInfoService.remove(Wrappers.lambdaUpdate(AsAssetBillInfo.class)
                        .eq(AsAssetBillInfo::getOrgId, item).eq(AsAssetBillInfo::getIsDelete, -1));

                //物理删除企业资产折旧明细表
                settleDetailService.remove(Wrappers.lambdaUpdate(AsAssetSettleDetail.class)
                        .eq(AsAssetSettleDetail::getOrgId, item).eq(AsAssetSettleDetail::getIsDelete, -1));

                //物理删除企业资产折旧汇总表
                settleSummaryService.remove(Wrappers.lambdaUpdate(AsAssetSettleSummary.class)
                        .eq(AsAssetSettleSummary::getOrgId, item).eq(AsAssetSettleSummary::getIsDelete, -1));
            }
        } catch (Exception e) {
            log.error("deleteSettleDataSchedule removeSettleData acquire lock error! exception ", e);
        } finally {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("deleteSettleDataSchedule removeSettleData end!");
    }
}
