package com.niimbot.asset.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.finance.model.AsAssetBillInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsAssetBillInfoMapper extends BaseMapper<AsAssetBillInfo> {

    /**
     * 根据orgId查询最近的结算信息
     * @param orgId
     * @return
     */
    AsAssetBillInfo queryLastedByOrgId(@Param("orgId") Long orgId);

    /**
     * 查询已折旧或已结账的最大会计期间
     * @param orgIdList
     * @return
     */
    Integer selectMaxSettleMonth(@Param("orgIdList")List<Long> orgIdList);
}