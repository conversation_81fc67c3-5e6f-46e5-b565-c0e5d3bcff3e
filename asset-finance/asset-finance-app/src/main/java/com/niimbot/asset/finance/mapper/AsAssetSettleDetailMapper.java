package com.niimbot.asset.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.finance.SettleDetailDto;
import com.niimbot.finance.SettleDetailQueryDto;
import com.niimbot.asset.finance.model.AsAssetSettleDetail;
import com.niimbot.asset.finance.model.AsAssetSettleSummary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsAssetSettleDetailMapper extends BaseMapper<AsAssetSettleDetail> {

    /**
     * 折旧明细按分摊部门和资产分类进行汇总
     * @param orgId
     * @param settleMonth
     * @return
     */
    List<AsAssetSettleSummary> selectCompanySummary(@Param("orgId") Long orgId, @Param("settleMonth") Integer settleMonth);

    /**
     * 分页查询
     * @param page
     * @param param
     * @return
     */
    IPage<SettleDetailDto> pageQuery(IPage<SettleDetailQueryDto> page, @Param("param") SettleDetailQueryDto param);
}