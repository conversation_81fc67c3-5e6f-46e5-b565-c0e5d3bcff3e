package com.niimbot.asset.finance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.finance.AlterLogQueryDto;
import com.niimbot.finance.AssetFinanceAlterLogDto;
import com.niimbot.asset.finance.model.AsAssetFinanceAlterLog;
import org.apache.ibatis.annotations.Param;

public interface AsAssetFinanceAlterLogMapper extends BaseMapper<AsAssetFinanceAlterLog> {

    IPage<AssetFinanceAlterLogDto> pageQuery(IPage<AlterLogQueryDto> page, @Param("page") AlterLogQueryDto queryDto);
}