package com.niimbot.asset.finance.enums;

import java.util.Objects;

/**
 * 资产折旧任务类型枚举
 *
 * <AUTHOR>
 * @date 2023/2/13 上午11:21
 */
public enum SettleTaskTypeEnum {

    /**
     * 资产财务折旧月结任务
     */
    MONTH_SETTLE(1, "assetMonthSettle", "资产财务折旧月结任务"),

    /**
     * 企业资产折旧计提任务
     */
    COMPANY_DEPRECIATION(2, "companyAssetDepreciation", "企业资产折旧计提任务"),

    /**
     * 企业资产折旧结账任务
     */
    COMPANY_SETTLE(3, "companyAssetSettle", "企业资产折旧结账任务"),

    /**
     * 未知
     */
    UN_KNOW(-1, "un_know", "未知"),
    ;

    SettleTaskTypeEnum(Integer taskType, String taskBizCode, String taskDesc) {
        this.taskType = taskType;
        this.taskBizCode = taskBizCode;
        this.taskDesc = taskDesc;
    }

    private Integer taskType;

    private String taskBizCode;

    private String taskDesc;

    public Integer getTaskType() {
        return taskType;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public String getTaskBizCode() {
        return taskBizCode;
    }

    public static SettleTaskTypeEnum getByType(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (SettleTaskTypeEnum item : values()) {
            if (item.getTaskType().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
