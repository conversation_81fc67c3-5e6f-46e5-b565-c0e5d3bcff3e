<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.finance.adapter.local.mapper.AsAssetFinanceAttributeLogMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.finance.adapter.local.model.AsAssetFinanceAttributeLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="asset_id" jdbcType="BIGINT" property="assetId" />
    <result column="before_org_owner" jdbcType="BIGINT" property="beforeOrgOwner" />
    <result column="after_org_owner" jdbcType="BIGINT" property="afterOrgOwner" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, asset_id, before_org_owner, after_org_owner, handle_time, `status`, create_time, 
    update_time
  </sql>

  <select id="queryWaitExecute" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from as_asset_finance_attribute_log
    where status = 0 and create_time <![CDATA[>=]]> #{startTime} and create_time <![CDATA[<=]]> #{endTime}
    order by id asc
    limit 10
  </select>

  <update id="batchUpdateStatus">
    update as_asset_finance_attribute_log set status = 1
    where id in
    <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
  </update>
</mapper>