package com.niimbot.asset.finance.adapter.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.finance.adapter.local.model.AsAssetFinanceAttributeLog;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;


public interface AsAssetFinanceAttributeLogMapper extends BaseMapper<AsAssetFinanceAttributeLog> {

    /**
     * 查询待执行的任务
     * @return
     */
    List<AsAssetFinanceAttributeLog> queryWaitExecute(@Param("startTime")LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 批量修改任务状态
     * @param ids
     * @return
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids);
}