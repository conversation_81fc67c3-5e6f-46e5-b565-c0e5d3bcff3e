package com.niimbot.asset.finance.adapter.local.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * as_asset_finance_attibute_log
 * <AUTHOR>
@Data
@TableName(value = "as_asset_finance_attribute_log", autoResultMap = true)
public class AsAssetFinanceAttributeLog implements Serializable {

    private static final long serialVersionUID = -445479353467715538L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属组织
     */
    private Long orgId;

    /**
     * 资产id
     */
    private Long assetId;

    /**
     * 修改前所属组织
     */
    private Long beforeOrgOwner;

    /**
     * 修改后所属组织
     */
    private Long afterOrgOwner;

    /**
     * 资产处置时间
     */
    private LocalDateTime handleTime;

    /**
     * 状态 0:待处理 1:已处理 2:处理失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}