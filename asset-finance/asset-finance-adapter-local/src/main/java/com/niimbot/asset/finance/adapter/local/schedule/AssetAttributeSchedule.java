package com.niimbot.asset.finance.adapter.local.schedule;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.finance.adapter.local.mapper.AsAssetFinanceAttributeLogMapper;
import com.niimbot.asset.finance.adapter.local.model.AsAssetFinanceAttributeLog;
import com.niimbot.asset.finance.service.AssetFinanceInfoService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.finance.AssetAttributeAlterMessageDto;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 周期性处理资产所属组织变更和资产处置操作
 * <AUTHOR>
 * @date 2023/3/3 下午4:34
 */
@Slf4j
@Component
public class AssetAttributeSchedule {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private AsAssetFinanceAttributeLogMapper assetFinanceAttributeLogMapper;
    @Autowired
    private AssetFinanceInfoService financeInfoService;


    @Scheduled(cron = "0 */2 * * * ?")
    @Async("asyncScheduledThreadPool")
    public void scheduleTask() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        log.info("assetAttributeSchedule scheduleTask start!");
        //获取分布式锁，这里主要是控制任务的并发，仅允许一台机器拉取任务执行，防止同一个任务被多台机器执行
        RLock lock = redissonClient.getLock("s-asset-platform:asset_finance_attribute_alter");
        boolean isLocked;
        try {
            isLocked = lock.tryLock(7, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("assetAttributeSchedule scheduleTask end! Acquire lock fail! currentTime=[{}]", System.currentTimeMillis());
                return;
            }

            //查询当前待处理数据
            LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            List<AsAssetFinanceAttributeLog> assetFinanceAttributeLogList = assetFinanceAttributeLogMapper.queryWaitExecute(startTime, endTime);
            if (CollUtil.isEmpty(assetFinanceAttributeLogList)) {
                log.info("assetAttributeSchedule scheduleTask wait execute task empty!");
                return ;
            }

            List<Long> ids = assetFinanceAttributeLogList.stream().map(AsAssetFinanceAttributeLog::getId).collect(Collectors.toList());
            log.info("assetAttributeSchedule scheduleTask ids=[{}]", JSON.toJSONString(ids));
            assetFinanceAttributeLogMapper.batchUpdateStatus(ids);
            log.info("assetAttributeSchedule scheduleTask modify task status success! ids=[{}]", JSON.toJSONString(ids));
            for (AsAssetFinanceAttributeLog item : assetFinanceAttributeLogList) {
                //资产处置
                if (Objects.nonNull(item.getHandleTime())) {
                    AssetAttributeAlterMessageDto modifyParam = new AssetAttributeAlterMessageDto();
                    modifyParam.setOrgId(item.getOrgId());
                    modifyParam.setAssetId(item.getAssetId());
                    modifyParam.setHandlerTime(item.getHandleTime());
                    financeInfoService.handleAsset(modifyParam);
                }

                //资产组织变更
                if (Objects.nonNull(item.getBeforeOrgOwner()) && Objects.nonNull(item.getAfterOrgOwner())) {
                    AssetAttributeAlterMessageDto modifyParam = new AssetAttributeAlterMessageDto();
                    modifyParam.setOrgId(item.getOrgId());
                    modifyParam.setAssetId(item.getAssetId());
                    modifyParam.setBeforeOrgOwner(item.getBeforeOrgOwner());
                    modifyParam.setAfterOrgOwner(item.getAfterOrgOwner());
                    financeInfoService.alterAssetOrgOwner(modifyParam);
                }
            }

            log.info("assetAttributeSchedule scheduleTask success!");
        } catch (Exception e) {
            log.error("assetAttributeSchedule scheduleTask acquire lock error! exception ", e);
        } finally {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
