<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.weixin.base.mapper.WxCorpMapper">

    <select id="selectCodeByCorpId" resultType="java.lang.String">
        SELECT permanent_code FROM weixin_corp WHERE corp_id = #{corpId} AND is_delete = 0
    </select>

    <select id="selectCorpInfoByCompanyId" resultType="com.niimbot.asset.weixin.base.dto.CorpInfo">
        SELECT corp_id, agent_id, permanent_code FROM weixin_corp WHERE company_id = #{companyId} AND is_delete = 0
    </select>

</mapper>
