package com.niimbot.asset.weixin.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.weixin.base.config.WeixinProperties;
import com.niimbot.asset.weixin.base.constant.Jumps;
import com.niimbot.asset.weixin.base.dto.CorpInfo;
import com.niimbot.asset.weixin.base.dto.WxCpTpIdTranslate;
import com.niimbot.asset.weixin.base.dto.WxCpTpIdTranslateResp;
import com.niimbot.asset.weixin.base.dto.WxCpTpJobResult;
import com.niimbot.asset.weixin.base.error.WxResultCode;
import com.niimbot.asset.weixin.base.restops.RestOps;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.asset.weixin.base.support.CorpCaches;
import com.niimbot.asset.weixin.base.support.PropertiesHolder;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.templatecard.TemplateCardJump;
import me.chanjar.weixin.cp.constant.WxCpApiPathConsts;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:43
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxOpenApiServiceImpl implements WxOpenApiService {

    private final WxCpTpService wxCpTpService;
    private final WeixinProperties weixinProperties;

    @Override
    public void sendMessage(Long companyId, WxCpMessage message) {
        CorpInfo corp = CorpCaches.getCorp(companyId);
        message.setAgentId(corp.getAgentId());
        RestOps.handleOrThrow(() -> wxCpTpService.post(
                WxCpApiPathConsts.DEFAULT_CP_BASE_URL + WxCpApiPathConsts.Message.MESSAGE_SEND + "?access_token=" + wxCpTpService.getCorpToken(corp.getCorpId(), corp.getPermanentCode(), false).getAccessToken(),
                message.toJson())
        );
    }

    @Override
    public void sendTplCardTextNoticeSpecialMsg(Long companyId, WxCpMessage msg, Jumps jumps, List<Object> params) {
        try {
            // 收信人为空
            if (StrUtil.isBlank(msg.getToUser())) {
                return;
            }
            CorpInfo corp = CorpCaches.getCorp(companyId);
            params.add(corp.getCorpId());
            // 统一文本卡片
            msg.setCardType(WxConsts.TemplateCardType.TEXT_NOTICE);
            // 补充PC需要的域名地址 放在首位
            params.add(0, PropertiesHolder.getPcDomain());
            String formatPc = jumps.getDetailsUrl().formatPc(params);
            ArrayList<TemplateCardJump> cardJumps = Lists.newArrayList(
                    TemplateCardJump.builder()
                            .type(1)
                            .title("登录电脑端管理后台")
                            .url(formatPc)
                            .build()
            );
            msg.setJumps(cardJumps);
            // 统一卡片链接形式
            msg.setCardActionType(1);
            msg.setCardActionUrl(formatPc);
            msg.setEnableIdTrans(true);
            sendMessage(companyId, msg);
        } catch (Exception e) {
            log.error("发送微信应用卡片文本消息异常", e);
        }
    }

    @Override
    public void sendTplCardTextNoticeMessage(Long companyId, WxCpMessage msg, Jumps jumps, List<Object> params) {
        try {
            // 收信人为空
            if (StrUtil.isBlank(msg.getToUser())) {
                return;
            }
            CorpInfo corp = CorpCaches.getCorp(companyId);
            params.add(corp.getCorpId());
            // 统一文本卡片
            msg.setCardType(WxConsts.TemplateCardType.TEXT_NOTICE);
            // 统一跳转格式
            String formatMini = jumps.getDetailsUrl().formatMini(params);
            // 补充PC需要的域名地址 放在首位
            params.add(0, PropertiesHolder.getPcDomain());
            String formatPc = jumps.getDetailsUrl().formatPc(params);
            ArrayList<TemplateCardJump> cardJumps = Lists.newArrayList(
                    TemplateCardJump.builder()
                            .type(1)
                            .title("登录电脑端管理后台")
                            .url(formatPc)
                            .build(),
                    TemplateCardJump.builder()
                            .type(2)
                            .title("进入小程序工作台")
                            .pagepath(formatMini)
                            .appid(PropertiesHolder.getWxAppId())
                            .build()
            );
            msg.setJumps(cardJumps);
            // 统一卡片链接形式
            msg.setCardActionType(2);
            msg.setCardActionAppid(PropertiesHolder.getWxAppId());
            msg.setCardActionPagepath(formatMini);
            msg.setEnableIdTrans(true);
            sendMessage(companyId, msg);
        } catch (Exception e) {
            log.error("发送微信应用卡片文本消息异常", e);
        }

    }

    @Override
    public String fileTrans(Long companyId, MultipartFile file) {
        String filename = file.getOriginalFilename();
        // String path = this.getClass().getResource("").getPath();
        // File toFile = new File(path + "/fileTrans" + File.separator + "weixin" + File.separator + System.currentTimeMillis() + File.separator + filename);
        File toFile = new File("fileTrans" + File.separator + "weixin" + File.separator + System.currentTimeMillis() + File.separator + filename);
        try {
            FileUtils.copyInputStreamToFile(file.getInputStream(), toFile);
            if (!toFile.exists()) {
                boolean mkdirs = toFile.mkdirs();
            }
            return getUrl(companyId, toFile);
        } catch (Exception e) {
            log.error("weixin fileTrans error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "文件转义失败");
        } finally {
            FileUtil.del(toFile);
        }
    }

    private String getUrl(Long companyId, File outputFile) {
        CorpInfo corp = CorpCaches.getCorp(companyId);

        // 上传到微信服务器
        CuzWxCpTpMediaServiceImpl wxCpTpMediaService = (CuzWxCpTpMediaServiceImpl) wxCpTpService.getWxCpTpMediaService();
        WxMediaUploadResult uploadResult = RestOps.handleOrThrow(() -> wxCpTpMediaService.upload(WxConsts.MediaFileType.FILE, outputFile));
        String mediaId = uploadResult.getMediaId();

        // 获取任务id
        CuzWxCpTpContactServiceImpl wxCpTpContactService = (CuzWxCpTpContactServiceImpl) wxCpTpService.getWxCpTpContactService();
        WxCpTpIdTranslate idTranslate = new WxCpTpIdTranslate();
        idTranslate.setAuthCorpId(corp.getCorpId());
        idTranslate.setMediaIdList(ListUtil.of(mediaId));
        WxCpTpIdTranslateResp idTranslateResp = RestOps.handleOrThrow(() -> wxCpTpContactService.idTranslate(idTranslate));
        String jobId = idTranslateResp.getJobId();

        // 轮询获取转义后的文件地址
        LocalDateTime now = LocalDateTime.now();
        StringBuilder urlBuilder = new StringBuilder(wxCpTpService.getWxCpTpConfigStorage().getApiUrl("/cgi-bin/service/batch/getresult"));
        urlBuilder.append("?provider_access_token=").append(RestOps.handleOrThrow(wxCpTpService::getWxCpProviderToken))
                .append("&jobid=").append(jobId)
                .append("&access_token=").append(RestOps.handleOrThrow(() -> wxCpTpService.getCorpToken(corp.getCorpId(), CorpCaches.getPermanentCode(corp.getCorpId()), false)).getAccessToken());
        while (true) {
            if (Duration.between(now, LocalDateTime.now()).getSeconds() > 10L) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "导出失败");
            }
            String result = RestOps.handleOrThrow(() -> wxCpTpService.get(urlBuilder.toString(), null));
            WxCpTpJobResult jobResult = WxCpTpJobResult.fromJson(result);
            if (jobResult.successful()) {
                return jobResult.getResult().getContactIdTranslate().getUrl();
            }
        }
    }

    @Override
    public String excelTrans(Long companyId, String fileName, ExcelWriter excelWriter) {
        File tempPath = new File("exportTemp/weixin/trans");
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            boolean mkdirs = tempPath.mkdirs();
        }
        File outputFile = new File(tempPath.getPath() + "/" + System.currentTimeMillis() + "/" + fileName);
        try {
            excelWriter.setDestFile(outputFile);
            excelWriter.flush();
            return getUrl(companyId, outputFile);
        } finally {
            FileUtil.del(outputFile);
            excelWriter.close();
            IoUtil.close(excelWriter);
        }
    }

    @Override
    public String multiFileTrans(Long companyId, String filePath, String name) {
        log.info("multiFileTrans before filePath companyId[{}] [{}]", companyId, filePath);
        // 约定: 取最后一个为文件名称
        StringBuilder outFileName = new StringBuilder(new LinkedList<>(Splitter.on("/").splitToList(name)).getLast());
        // 约定: 取倒数第二个为文件目录
        LinkedList<String> paths = new LinkedList<>(Splitter.on(File.separator).splitToList(filePath));
        String lastPath = paths.removeLast();
        if (lastPath.contains(".zip")) {
            paths.addLast(lastPath.replaceAll(".zip", ""));
        }
        filePath = String.join(File.separator, paths);
        log.info("multiFileTrans after filePath companyId[{}] [{}]", companyId, filePath);
//        List<File> files = FileUtil.loopFiles(new File(filePath), file -> file.getName().endsWith(".xlsx"));
        List<File> files = FileUtil.loopFiles(new File(filePath));

        if (CollUtil.isEmpty(files)) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "导出失败，文件为空");
        }
        CorpInfo corp = CorpCaches.getCorp(companyId);

        Set<String> results = new HashSet<>(6);

        List<List<File>> partition = Lists.partition(files, 20);
        AtomicInteger index = new AtomicInteger(0);
        partition.forEach(part -> {
            // 上传到微信服务器
            List<String> mediaIds = new ArrayList<>(files.size());
            part.forEach(v -> {
                CuzWxCpTpMediaServiceImpl wxCpTpMediaService = (CuzWxCpTpMediaServiceImpl) wxCpTpService.getWxCpTpMediaService();
                WxMediaUploadResult uploadResult = RestOps.handleOrThrow(() -> wxCpTpMediaService.upload(WxConsts.MediaFileType.FILE, v));
                mediaIds.add(uploadResult.getMediaId());
            });

            // 获取任务id
            CuzWxCpTpContactServiceImpl wxCpTpContactService = (CuzWxCpTpContactServiceImpl) wxCpTpService.getWxCpTpContactService();
            WxCpTpIdTranslate idTranslate = new WxCpTpIdTranslate();
            idTranslate.setAuthCorpId(corp.getCorpId());
            idTranslate.setMediaIdList(mediaIds);
            if (mediaIds.size() > 1) {
                // 企业微信后台会打包成zip压缩文件，并自动拼接上.zip后缀。
                String tempName = outFileName.toString();
                if (tempName.endsWith(".zip")) {
                    tempName = tempName.replaceAll(".zip", "");
                }
                idTranslate.setOutputFileName(tempName + "-" + index.getAndIncrement());
            }

            WxCpTpIdTranslateResp idTranslateResp = RestOps.handleOrThrow(() -> wxCpTpContactService.idTranslate(idTranslate));
            String jobId = idTranslateResp.getJobId();

            // 轮询获取转义后的文件地址
            LocalDateTime now = LocalDateTime.now();
            StringBuilder urlBuilder = new StringBuilder(wxCpTpService.getWxCpTpConfigStorage().getApiUrl("/cgi-bin/service/batch/getresult"));
            urlBuilder.append("?provider_access_token=").append(RestOps.handleOrThrow(wxCpTpService::getWxCpProviderToken))
                    .append("&jobid=").append(jobId)
                    .append("&access_token=").append(RestOps.handleOrThrow(() -> wxCpTpService.getCorpToken(corp.getCorpId(), CorpCaches.getPermanentCode(corp.getCorpId()), false)).getAccessToken());
            while (Duration.between(now, LocalDateTime.now()).getSeconds() <= 20L) {
                String result = RestOps.handleOrThrow(() -> wxCpTpService.get(urlBuilder.toString(), null));
                WxCpTpJobResult jobResult = WxCpTpJobResult.fromJson(result);
                if (jobResult.successful()) {
                    results.add(jobResult.getResult().getContactIdTranslate().getUrl());
                    break;
                }
            }
        });
        if (CollUtil.isEmpty(results)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "导出文件失败");
        }
        return String.join("|", results);
    }

    @Override
    public String getRegisterCode() throws WxErrorException {
        String url = GET_REGISTER_CODE_URL + "?provider_access_token=" + RestOps.handleOrThrow(wxCpTpService::getWxCpProviderToken);
        String registerTemplate = weixinProperties.getRegisterTemplate();
        if (StrUtil.isEmpty(registerTemplate)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "推广二维码模板尚未配置");
        }
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("template_id", registerTemplate);
        return this.wxCpTpService.post(url, jsonObject.toString());
    }
}
