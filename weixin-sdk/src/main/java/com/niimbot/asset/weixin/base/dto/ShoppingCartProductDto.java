package com.niimbot.asset.weixin.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:36
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ShoppingCartProductDto对象", description = "购物车商品对象")
public class ShoppingCartProductDto implements Serializable {

    private static final long serialVersionUID = 2040107501919046945L;

    @ApiModelProperty( value = "购物车商品id")
    private Long id;

    @ApiModelProperty( value = "商品编码")
    private String skuCode;

    @ApiModelProperty( value = "商品名称")
    private String skuName;

    @ApiModelProperty( value = "商品副标题")
    private String subTitle;

    @ApiModelProperty( value = "商品图片")
    private List<String> url;

    @ApiModelProperty( value = "商品数量")
    private Integer count;

    @ApiModelProperty( value = "商品价值")
    private Integer goodsValue;

    @ApiModelProperty( value = "容量-软件商品才有值")
    private Integer capacity;

    @ApiModelProperty( value = "商品类型 1-软件 2-硬件")
    private Integer goodsType;

    @ApiModelProperty( value = "商品价格")
    private BigDecimal price;

    @ApiModelProperty( value = "商品单价")
    private BigDecimal unitPrice;

    @ApiModelProperty( value = "赠品")
    private List<ShoppingCartBonusDto> productBonus;
}
