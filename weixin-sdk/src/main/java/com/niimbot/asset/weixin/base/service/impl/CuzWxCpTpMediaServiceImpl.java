package com.niimbot.asset.weixin.base.service.impl;

import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.http.MediaUploadRequestExecutor;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpMediaServiceImpl;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2023/7/20 19:07
 */
public class CuzWxCpTpMediaServiceImpl extends WxCpTpMediaServiceImpl {

    private final WxCpTpService wxCpTpService;

    private static final String MEDIA_UPLOAD = "/cgi-bin/service/media/upload?type=";

    public CuzWxCpTpMediaServiceImpl(WxCpTpService wxCpTpService) {
        super(wxCpTpService);
        this.wxCpTpService = wxCpTpService;
    }

    public WxMediaUploadResult upload(String mediaType, File file) throws WxErrorException {
        return this.wxCpTpService.execute(MediaUploadRequestExecutor.create(this.wxCpTpService.getRequestHttp()),
                wxCpTpService.getWxCpTpConfigStorage().getApiUrl(MEDIA_UPLOAD + mediaType) + "&provider_access_token=" + wxCpTpService.getWxCpProviderToken(), file);
    }

}
