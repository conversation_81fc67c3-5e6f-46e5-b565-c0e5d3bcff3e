package com.niimbot.asset.weixin.base.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * created by chen.y on 2021/8/24 10:27
 */
@Data
@Accessors(chain = true)
public class InnerAuthRequest implements Serializable {

    @NotEmpty(message = "账户或手机号不能为空")
    private String account;

    @NotEmpty(message = "认证数据")
    private String secureKey;
}
