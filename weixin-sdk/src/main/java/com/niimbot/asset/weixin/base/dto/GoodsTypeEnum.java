package com.niimbot.asset.weixin.base.dto;

/**
 * <AUTHOR>
 * @date 2023/4/10 上午11:02
 */
public enum GoodsTypeEnum {

    SOFTWARE(1, "软件"),
    HARDWARE(2, "硬件"),
    ;

    GoodsTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
