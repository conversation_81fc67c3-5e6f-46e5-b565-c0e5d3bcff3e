package com.niimbot.asset.weixin.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:44
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ShoppingCartBonusDto对象", description = "购物车商品赠品对象")
public class ShoppingCartBonusDto implements Serializable {

    private static final long serialVersionUID = -7433562560861021098L;

    @ApiModelProperty(value = "购物车赠品id")
    private Long id;

    @ApiModelProperty(value = "主商品id")
    private Long mainProductId;

    @ApiModelProperty(value = "优惠编码")
    private String discountCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "商品价值")
    private Integer goodsValue;
}
