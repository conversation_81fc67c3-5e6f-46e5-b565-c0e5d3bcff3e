package com.niimbot.asset.weixin.base.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpdateEmployeeNo {

    @NotNull(message = "请输入员工id")
    private Long id;

    @Pattern(regexp = "^$|^[a-zA-Z0-9-_]{2,15}$", message = "请输入工号 / 2-15位，只能由字母、数字、符号-_组成")
    private String empNo;

    private String remark;

}
