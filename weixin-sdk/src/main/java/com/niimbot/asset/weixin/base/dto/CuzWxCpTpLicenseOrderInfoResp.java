package com.niimbot.asset.weixin.base.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

/**
 * 订单详情结果 文档：https://developer.work.weixin.qq.com/document/path/95648
 *
 * <AUTHOR>
 * @date 2023/7/11 16:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CuzWxCpTpLicenseOrderInfoResp extends WxCpBaseResp {

    private static final long serialVersionUID = 7000171280773370910L;

    private CuzWxCpTpLicenseOrder order;


    /**
     * From json wx cp tp license order info resp.
     *
     * @param json the json
     * @return the wx cp tp license order info resp
     */
    public static CuzWxCpTpLicenseOrderInfoResp fromJson(String json) {
        return WxCpGsonBuilder.create().fromJson(json, CuzWxCpTpLicenseOrderInfoResp.class);
    }


}
