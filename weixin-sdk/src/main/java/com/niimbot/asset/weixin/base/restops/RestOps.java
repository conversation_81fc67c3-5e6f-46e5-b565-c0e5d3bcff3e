package com.niimbot.asset.weixin.base.restops;

import com.niimbot.asset.framework.utils.JacksonConverter;
import com.niimbot.asset.weixin.base.error.WxResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxError;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * <AUTHOR>
 */
@Slf4j
public class RestOps {

    public static <R> R handleOrThrow(SupplierThrowable<R> supplier) {
        try {
            R r = supplier.get();
            log.info("weixin response : [{}]", JacksonConverter.MAPPER.writeValueAsString(r));
            return r;
        } catch (Exception e) {
            log.warn("请求微信接口异常", e);
            if (e instanceof WxErrorException) {
                WxError error = ((WxErrorException) e).getError();
                log.warn("请求微信接口异常 [{}]", error);
                throw new BusinessException(WxResultCode.WX_COMMON_ERROR, error.getErrorMsgEn());
            } else {
                log.warn("请求微信接口异常", e);
                throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "请求微信异常，请稍后再试");
            }
        }
    }

}
