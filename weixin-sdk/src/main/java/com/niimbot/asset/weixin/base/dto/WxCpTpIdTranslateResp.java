package com.niimbot.asset.weixin.base.dto;

import com.google.gson.annotations.SerializedName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

/**
 * <AUTHOR>
 * @date 2023/7/20 17:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WxCpTpIdTranslateResp extends WxCpBaseResp {

    @SerializedName("jobid")
    private String jobId;

    /**
     * From json wx cp tp contact search resp.
     *
     * @param json the json
     * @return the wx cp tp contact search resp
     */
    public static WxCpTpIdTranslateResp fromJson(String json) {
        return WxCpGsonBuilder.create().fromJson(json, WxCpTpIdTranslateResp.class);
    }

}
