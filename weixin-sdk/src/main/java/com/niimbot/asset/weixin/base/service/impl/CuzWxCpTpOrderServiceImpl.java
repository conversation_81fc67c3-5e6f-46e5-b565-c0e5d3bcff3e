package com.niimbot.asset.weixin.base.service.impl;

import com.google.gson.JsonObject;

import com.niimbot.asset.weixin.base.dto.CuzWxCpTpOrderDetails;

import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpOrderServiceImpl;

import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.Tp.GET_ORDER;

/**
 * <AUTHOR>
 * @date 2023/7/25 10:52
 */
public class CuzWxCpTpOrderServiceImpl extends WxCpTpOrderServiceImpl {

    private final WxCpTpService wxCpTpService;

    public CuzWxCpTpOrderServiceImpl(WxCpTpService wxCpTpService) {
        super(wxCpTpService);
        this.wxCpTpService = wxCpTpService;
    }

    /**
     * 获取订单详情
     * <p>
     * <a href='https://developer.work.weixin.qq.com/document/15219#%E8%8E%B7%E5%8F%96%E8%AE%A2%E5%8D%95%E8%AF%A6%E6%83%85'>文档地址</a>
     * <p/>
     *
     * @param orderId 订单号
     * @return the order
     */
    public CuzWxCpTpOrderDetails getOrderNew(String orderId) throws WxErrorException {
        String url = wxCpTpService.getWxCpTpConfigStorage().getApiUrl(GET_ORDER);
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("orderid", orderId);
        String result = this.wxCpTpService.post(url, jsonObject.toString());
        return CuzWxCpTpOrderDetails.fromJson(result);
    }

}
