package com.niimbot.asset.weixin.base.dto;

import com.niimbot.asset.framework.annotation.DictConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WxSaleOrderQueryResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售单ID")
    private Long id;

    @ApiModelProperty(value = "单据编号：字母前缀ON+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "商品摘要")
    private String summary;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "订单支付金额")
    private BigDecimal payMoney;

    @ApiModelProperty(value = "抵扣金额")
    private BigDecimal discountMoney;

    @ApiModelProperty(value = "支付状态: 1-待付款, 2-已完成, 3-已关闭")
    private Integer status;

    @DictConvert(value = "pay_status", refField = "status")
    private String statusText;

    @ApiModelProperty(value = "支付方式: 1-支付宝, 2-微信, 3-对公转账")
    private Integer payType;

    @DictConvert(value = "pay_type", refField = "payType")
    private String payTypeText;

    @ApiModelProperty(value = "开票状态:0-无需开票，1-待开票，2-开票中，3-已开票")
    private Integer invoiceStatus;

    @DictConvert(value = "sale_order_invoice_status", refField = "invoiceStatus")
    private String invoiceStatusText;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "订购时长（月）")
    private Integer buyTime;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expirationTime;

    @ApiModelProperty(value = "支付链接")
    private String payUrl;

    @ApiModelProperty("有效期-开始")
    private LocalDateTime beginTime;

    @ApiModelProperty("有效期-结束")
    private LocalDateTime endTime;

    @ApiModelProperty("商品类型")
    private Integer productType;

    @ApiModelProperty("商品类型中文")
    private String productTypeText;

    @ApiModelProperty("操作时间：支付/关闭")
    private LocalDateTime operateTime;
}
