package com.niimbot.asset.weixin.base.constant;

/**
 * <AUTHOR>
 * @date 2023/7/10 15:27
 */
public interface WxMqConstant {

    /**
     * 微信回调消息
     */
    String ASSET_WEIXIN_CALLBACK_GROUP = "GID_asset-weixin-callback_p";

    /**
     * 销售单事件 【下单成功通知】 【改单通知】 【支付成功通知】
     */
    String ASSET_WEIXIN_SALEORDER_CONSUMER_GROUP = "GID_asset-weixin-callback-saleOrder_c";
    String ASSET_WEIXIN_SALEORDER_TAG = "weixin-saleOrder";

    /**
     * 企业事件
     * 【授权通知事件 CANCEL_AUTH】
     * 【授权通知事件 CANCEL_AUTH】
     * 【授权通知事件 CHANGE_AUTH】
     * 【授权码事件】
     *
     */
    String ASSET_WEIXIN_CORP_CONSUMER_GROUP = "GID_asset-weixin-callback-corp_c";
    String ASSET_WEIXIN_CORP_TAG = "weixin-corp";

    /**
     * 企业组织架构
     *
     * 企业组织变动
     * 【组织新增】
     * 【组织变更】
     * 【组织删除】
     *
     *  企业员工变动
     *  【员工新增】
     *  【员工变更】
     *  【员工删除】
     */
    String ASSET_WEIXIN_CORP_STRUCTURE_CONSUMER_GROUP = "GID_asset-weixin-callback-corp-structure_c";
    String ASSET_WEIXIN_CORP_STRUCTURE_TAG = "weixin-corp-structure";


}
