package com.niimbot.asset.weixin.base.constant;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public enum Jumps {
    /**
     * 资产领用单
     */
    RECEIVE(
            "1",
            new DetailsUrl
                    (
                            "%s/#/recv-return/recv-view?orderType=1&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=1&corpId=%s",
                            "%s/#/forward?orderType=1&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产退还单
     */
    RETURN(
            "2",
            new DetailsUrl
                    (
                            "%s/#/recv-return/return-view?orderType=2&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=2&corpId=%s",
                            "%s/#/forward?orderType=2&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产借用单
     */
    BORROW(
            "3",
            new DetailsUrl
                    (
                            "%s/#/borrow-revert/borrow-view?orderType=3&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=3&corpId=%s",
                            "%s/#/forward?orderType=3&docId=%d&corpId=%s"
                    )
    ),

    /**
     * 资产归还单
     */
    BACK(
            "4",
            new DetailsUrl
                    (
                            "%s/#/borrow-revert/revert-view?orderType=4&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=4&corpId=%s",
                            "%s/#/forward?orderType=4&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产调拨单
     */
    ALLOCATE(
            "5",
            new DetailsUrl
                    (
                            "%s/#/asset-allot/allot-view?orderType=5&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=5&corpId=%s",
                            "%s/#/forward?orderType=5&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产报修单
     */
    REPAIR_REPORT(
            "6",
            new DetailsUrl
                    (
                            "%s/#/asset-repairs/repair-order-detail?orderType=6&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=6&corpId=%s",
                            "%s/#/forward?orderType=6&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产维修单
     */
    REPAIR(
            "7",
            new DetailsUrl
                    (
                            "%s/#/service-asset/service-asset-order-detail?orderType=7&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=7&corpId=%s",
                            "%s/#/forward?orderType=7&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产处置单
     */
    DISPOSE(
            "8",
            new DetailsUrl
                    (
                            "%s/#/asset-handle/handle-view?orderType=8&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=8&corpId=%s",
                            "%s/#/forward?orderType=8&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产变更单
     */
    CHANGE(
            "9",
            new DetailsUrl
                    (
                            "%s/#/asset-change/change-view?orderType=9&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=9&corpId=%s",
                            "%s/#/forward?orderType=9&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产维保单
     */
    MAINTAIN(
            "11",
            new DetailsUrl
                    (
                            "%s/#/maintenance-asset/maintenance-order-detail?orderType=11&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=11&corpId=%s",
                            "%s/#/forward?orderType=11&docId=%d&corpId=%s&"
                    )
    ),
    /**
     * 资产入库单
     */
    STORE(
            "13",
            new DetailsUrl
                    (
                            "%s/#/asset-storage/detail?orderType=13&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=13&corpId=%s",
                            "%s/#/forward?orderType=13&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 采购申请单
     */
    PURCHASE_APPLY(
            "10",
            new DetailsUrl
                    (
                            "%s/#/purchase-apply/detail?orderType=10&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=10&corpId=%s",
                            "%s/#/forward?orderType=10&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 采购订单
     */
    PURCHASE_ORDER(
            "12",
            new DetailsUrl
                    (
                            "%s/#/purchase-order/detail?orderType=12&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=12&corpId=%s",
                            "%s/#/forward?orderType=12&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材入库单
     */
    RK(
            "31",
            new DetailsUrl
                    (
                            "%s/#/material-rk/view?orderType=31&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=31&corpId=%s",
                            "%s/#/forward?orderType=31&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材出库单
     */
    CK(
            "32",
            new DetailsUrl
                    (
                            "%s/#/material-ck/view?orderType=32&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=32&corpId=%s",
                            "%s/#/forward?orderType=32&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材领用单
     */
    LY(
            "33",
            new DetailsUrl
                    (
                            "%s/#/material-ly/view?orderType=33&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=33&corpId=%s",
                            "%s/#/forward?orderType=33&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材调整单
     */
    TZ(
            "34",
            new DetailsUrl
                    (
                            "%s/#/material-tz/view?orderType=34&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=34&corpId=%s",
                            "%s/#/forward?orderType=34&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材调拨单
     */
    DB(
            "35",
            new DetailsUrl
                    (
                            "%s/#/material-db/view?orderType=35&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=35&corpId=%s",
                            "%s/#/forward?orderType=35&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材报损单
     */
    BS(
            "36",
            new DetailsUrl
                    (
                            "%s/#/material-bs/view?orderType=36&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=36&corpId=%s",
                            "%s/#/forward?orderType=36&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材退库
     */
    TK(
            "37",
            new DetailsUrl
                    (
                            "%s/#/material-ck/tk-view?orderType=37&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=37&corpId=%s",
                            "%s/#/forward?orderType=37&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产盘点
     */
    INVENTORY
            (
                    "99",
                    new DetailsUrl
                            (
                                    "%s#/backlog/inventory-child-task-view?id=%d&corpId=%s",
                                    "/pages/inventory/task-detail/index?inventoryTaskId=%d&corpId=%s",
                                    ""
                            )
            ),
    INVENTORY_APPROVE
            (
                    "96",
                    new DetailsUrl
                            (
                                    "%s#/backlog/inventory-task-view?id=%d&subId=%d&corpId=%s",
                                    "/common-sub/empty/index?subId=%d&inventoryTaskId=%d&corpId=%s",
                                    ""
                            )
            ),
    /**
     * 员工异动
     */
    EMP_CHANGE
            (
                    "200",
                    new DetailsUrl
                            (
                                    "%s/#/emp-change?type=%d&id=%d&corpId=%s",
                                    "/common-sub/transaction-emp/index?type=%d&id=%d&corpId=%s",
                                    ""
                            )
            );
    @Getter
    private final String code;

    @Getter
    private final DetailsUrl detailsUrl;

    public static Jumps of(String code) {
        if (Objects.isNull(code)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "单据编码无效");
        }
        return Arrays.stream(Jumps.values())
                .filter(v -> v.code.equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "单据编码无效"));
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DetailsUrl {
        /**
         * PC端单据详情地址
         * 格式: PC_DOMAIN + 路由地址
         */
        private String pc;
        /**
         * 小程序端单据详情地址
         * 格式: 小程序Scheme + 路由地址
         *
         * @see <a href="https://open.dingtalk.com/document/orgapp/scheme-of-mini-programs">钉钉文档<a/>
         */
        private String mini;
        /**
         * PC端和小程序单据详情中转地址
         * 格式: H5_DOMAIN + 路由
         */
        private String h5;

        public String formatPc(List<Object> params) {
            return String.format(pc, params.toArray());
        }

        public String formatMini(List<Object> params) {
            return String.format(mini, params.toArray());
        }

        public String formatH5(Long orderId, String cropId) {
            return String.format(h5, "", orderId, cropId);
        }
    }

}
