package com.niimbot.asset.weixin.base.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/6/11 10:10
 */
@ToString
@Data
@ConfigurationProperties(prefix = "asset.weixin")
public class WeixinProperties {

    /**
     * 小程序ID
     */
    private String appId;

    /**
     * APP密钥
     */
    private String appSecret;

    /**
     * 公众号ID
     */
    private String h5AppId;

    /**
     * 公众号密钥
     */
    private String h5AppSecret;

    /**
     * 设置企业微信应用的suiteId
     */
    private String suiteId;

    /**
     * 设置企业微信应用的Secret
     */
    private String secret;

    private String corpId;

    private String providerSecret;

    /**
     * 设置企业微信应用的token
     */
    private String token;

    /**
     * 设置企业微信应用的EncodingAESKey
     */
    private String aesKey;

    private String registerTemplate;

    /**
     * 设置企业微信扫描oauth认证
     */
    private WeixinOauth2 oauth2;

    @Data
    static class WeixinOauth2 {
        private String suiteId;
        private String secret;
    }

}
