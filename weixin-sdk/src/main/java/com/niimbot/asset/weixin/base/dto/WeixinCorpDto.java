package com.niimbot.asset.weixin.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/14 11:20
 */
@Data
public class WeixinCorpDto {

    @ApiModelProperty(value = "授权方企业微信id")
    private String corpId;

    @ApiModelProperty(value = "公司Id")
    private Long companyId;

    @ApiModelProperty(value = "授权方企业名称")
    private String corpName;

    @ApiModelProperty(value = "agentId")
    private Integer agentId;

    @ApiModelProperty(value = "企业永久授权码")
    private String permanentCode;

}
