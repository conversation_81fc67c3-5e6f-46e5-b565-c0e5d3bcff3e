package com.niimbot.asset.weixin.base.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpdateOrgNo {

    @NotNull(message = "组织ID不能为空")
    private Long id;

    @NotBlank(message = "请输入组织编码")
    @Pattern(regexp = "^([0-9]|[A-Z]){1,20}$", message = "组织编码请输入1-20个数字、大写字母")
    private String orgCode;

}
