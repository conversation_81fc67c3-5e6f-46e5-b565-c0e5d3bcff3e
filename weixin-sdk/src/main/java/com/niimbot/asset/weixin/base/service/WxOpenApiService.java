package com.niimbot.asset.weixin.base.service;

import com.niimbot.asset.weixin.base.constant.Jumps;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import cn.hutool.poi.excel.ExcelWriter;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:43
 */
public interface WxOpenApiService {

    String GET_REGISTER_CODE_URL = "https://qyapi.weixin.qq.com/cgi-bin/service/get_register_code";

    void sendMessage(Long companyId, WxCpMessage message);

    void sendTplCardTextNoticeSpecialMsg(Long companyId, WxCpMessage msg, Jumps jumps, List<Object> params);

    /**
     * 发送企业微信文本类型的卡片消息
     *
     * @param companyId 企业ID
     * @param msg       消息内容
     * @param jumps     jump to code
     * @param params    jump url params
     */
    void sendTplCardTextNoticeMessage(Long companyId, WxCpMessage msg, Jumps jumps, List<Object> params);

    String fileTrans(Long companyId, MultipartFile file);

    String excelTrans(Long companyId, String fileName, ExcelWriter writer);

    String multiFileTrans(Long companyId, String filePath, String name);

    String getRegisterCode() throws WxErrorException;
}
