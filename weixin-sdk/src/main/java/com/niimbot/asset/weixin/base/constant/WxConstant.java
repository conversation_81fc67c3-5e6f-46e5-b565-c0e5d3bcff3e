package com.niimbot.asset.weixin.base.constant;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:34
 */
public class WxConstant {

    public static final String WX = "WEIXIN";

    public static final String WX_EMP_NAME = "$userName=USERID$";

    private static final String WX_ORG_NAME = "$departmentName=DEPARTMENT_ID$";

    private static final String WX_ORDER_LOCK_KEY = "weixin_order_lock:";

    private static final String WX_CORP_LOCK_KEY = "weixin_corp_lock:";

    public static String getContactEmpName(String name) {
        return WX_EMP_NAME.replaceAll("USERID", name);
    }

    public static String getContactOrgName(String name) {
        return WX_ORG_NAME.replaceAll("DEPARTMENT_ID", name);
    }

    public static final String CORP_CACHE_KEY = "weixin:corp:%d";

    public static final String PMT_CODE_CACHE_KEY = "weixin:permanent_code:%s";

    public static final String SYNC_LOCK = "weixin:full:sync:contacts:%d";

    public static final String WX_ROOT_DEP_ID = "1";

    public static String getWxOrderLockKey(String orderId) {
        return WX_ORDER_LOCK_KEY + orderId;
    }

    public static String getWxCorpLockKey(String corpId) {
        return WX_CORP_LOCK_KEY + corpId;
    }

    public static String getWxLicenseOrderLockKey(String orderId) {
        return "weixin_order_lock:" + orderId;
    }

}
