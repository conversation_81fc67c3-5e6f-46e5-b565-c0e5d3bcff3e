package com.niimbot.asset.weixin.base.dto;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/11 16:18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CuzWxCpTpLicenseAccountDuration implements Serializable {
    private static final long serialVersionUID = 7960912263908286975L;

    private Integer months;

    private Integer days;

    @SerializedName("new_expire_time")
    private Integer newExpireTime;

}

