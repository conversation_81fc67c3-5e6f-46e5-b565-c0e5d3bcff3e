package com.niimbot.asset.weixin.base.error;

import com.niimbot.jf.core.exception.error.details.ResultCode;

import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @date 2023/6/11 10:42
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
public enum WxResultCode implements ResultCode {

    WX_COMMON_ERROR(120001, "%s") {
        @Override
        public HttpStatus getHttpStatus() {
            return HttpStatus.BAD_REQUEST;
        }
    },

    WX_ACTIVE_CODE_ZERO(120002, "使用人数超过付费人数范围，如需更多用户" +
            "使用，请联系您的专属客户经理或电话XXX-XXX-XXX处理。") {
        @Override
        public HttpStatus getHttpStatus() {
            return HttpStatus.BAD_REQUEST;
        }
    },

    WX_ACTIVE_CODE_OVER_LIMIT(120003, "付费用户数仅剩%s个，请返回重新设置") {
        @Override
        public HttpStatus getHttpStatus() {
            return HttpStatus.BAD_REQUEST;
        }
    },
    ;

    private final int code;
    private final String message;

    WxResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

}
