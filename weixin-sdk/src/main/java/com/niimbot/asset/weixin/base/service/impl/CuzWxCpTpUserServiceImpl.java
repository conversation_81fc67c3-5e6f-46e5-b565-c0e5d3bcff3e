package com.niimbot.asset.weixin.base.service.impl;

import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.niimbot.asset.weixin.base.support.CorpCaches;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.GsonParser;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.config.WxCpTpConfigStorage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpUserServiceImpl;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.util.List;

import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.User.USER_SIMPLE_LIST;

/**
 * <AUTHOR>
 * @date 2023/6/11 17:07
 */
public class CuzWxCpTpUserServiceImpl extends WxCpTpUserServiceImpl {

    private final WxCpTpService wxCpTpService;
    public final WxCpTpConfigStorage wxCpTpRedissonConfigImpl;

    public CuzWxCpTpUserServiceImpl(WxCpTpService wxCpTpService,
                                    WxCpTpConfigStorage wxCpTpRedissonConfigImpl) {
        super(wxCpTpService);
        this.wxCpTpService = wxCpTpService;
        this.wxCpTpRedissonConfigImpl = wxCpTpRedissonConfigImpl;
    }

    public List<WxCpUser> listSimpleByDepartment(Long departId, Boolean fetchChild, Integer status, String corpId)
            throws WxErrorException {
        String params = "";
        if (fetchChild != null) {
            params += "&fetch_child=" + (fetchChild ? "1" : "0");
        }
        if (status != null) {
            params += "&status=" + status;
        } else {
            params += "&status=0";
        }
        params += "&access_token=" + wxCpTpService.getCorpToken(corpId, CorpCaches.getPermanentCode(corpId), false).getAccessToken();

        String url = wxCpTpRedissonConfigImpl.getApiUrl(USER_SIMPLE_LIST + departId);
        String responseContent = this.wxCpTpService.get(url, params);
        JsonObject tmpJsonElement = GsonParser.parse(responseContent);
        return WxCpGsonBuilder.create()
                .fromJson(
                        tmpJsonElement.getAsJsonObject().get("userlist"),
                        new TypeToken<List<WxCpUser>>() {
                        }.getType()
                );
    }

}
