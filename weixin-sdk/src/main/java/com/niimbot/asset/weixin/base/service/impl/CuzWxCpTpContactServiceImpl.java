package com.niimbot.asset.weixin.base.service.impl;

import com.niimbot.asset.weixin.base.dto.WxCpTpIdTranslate;
import com.niimbot.asset.weixin.base.dto.WxCpTpIdTranslateResp;

import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpContactServiceImpl;

/**
 * <AUTHOR>
 * @date 2023/7/20 17:47
 */
public class CuzWxCpTpContactServiceImpl extends WxCpTpContactServiceImpl {

    private final WxCpTpService wxCpTpService;

    private static final String ID_TRANSLATE = "/cgi-bin/service/contact/id_translate";

    public CuzWxCpTpContactServiceImpl(WxCpTpService wxCpTpService) {
        super(wxCpTpService);
        this.wxCpTpService = wxCpTpService;
    }

    public WxCpTpIdTranslateResp idTranslate(WxCpTpIdTranslate wxCpTpIdTranslate) throws WxErrorException {
        // 上传服务
        String responseText =
                wxCpTpService.post(wxCpTpService.getWxCpTpConfigStorage().getApiUrl(ID_TRANSLATE) + "?provider_access_token=" + wxCpTpService.getWxCpProviderToken(), wxCpTpIdTranslate.toJson());
        return WxCpTpIdTranslateResp.fromJson(responseText);
    }


}
