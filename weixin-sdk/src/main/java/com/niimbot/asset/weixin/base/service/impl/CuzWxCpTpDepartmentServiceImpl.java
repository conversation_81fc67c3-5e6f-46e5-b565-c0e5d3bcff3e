package com.niimbot.asset.weixin.base.service.impl;

import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.niimbot.asset.weixin.base.support.CorpCaches;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.GsonParser;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.config.WxCpTpConfigStorage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpDepartmentServiceImpl;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.util.List;

import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.Department.DEPARTMENT_GET;
import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.Department.DEPARTMENT_SIMPLE_LIST;

/**
 * 自定义wxCpTp，有些接口未实现，先自己实现
 *
 * <AUTHOR>
 * @date 2023/6/11 16:13
 */
public class CuzWxCpTpDepartmentServiceImpl extends WxCpTpDepartmentServiceImpl {

    private final WxCpTpService wxCpTpService;
    public final WxCpTpConfigStorage wxCpTpRedissonConfigImpl;

    public CuzWxCpTpDepartmentServiceImpl(WxCpTpService wxCpTpService,
                                          WxCpTpConfigStorage wxCpTpRedissonConfigImpl) {
        super(wxCpTpService);
        this.wxCpTpService = wxCpTpService;
        this.wxCpTpRedissonConfigImpl = wxCpTpRedissonConfigImpl;
    }

    public List<WxCpDepart> simpleList(Long id, String corpId) throws WxErrorException {
        String url = wxCpTpRedissonConfigImpl.getApiUrl(DEPARTMENT_SIMPLE_LIST);
        url += "?access_token=" + wxCpTpService.getCorpToken(corpId, CorpCaches.getPermanentCode(corpId), false).getAccessToken();
        if (id != null) {
            url += "&id=" + id;
        }
        String responseContent = this.wxCpTpService.get(url, null);
        JsonObject tmpJsonObject = GsonParser.parse(responseContent);
        return WxCpGsonBuilder.create()
                .fromJson(tmpJsonObject.get("department_id"),
                        new TypeToken<List<WxCpDepart>>() {
                        }.getType()
                );
    }

    public WxCpDepart get(Long id, String corpId) throws WxErrorException {
        String url = String.format(wxCpTpRedissonConfigImpl.getApiUrl(DEPARTMENT_GET), id);
        url = url + "&access_token=" + wxCpTpService.getCorpToken(corpId, CorpCaches.getPermanentCode(corpId), false).getAccessToken();
        String responseContent = this.wxCpTpService.get(url, null);
        JsonObject tmpJsonObject = GsonParser.parse(responseContent);
        return WxCpGsonBuilder.create().fromJson(tmpJsonObject.get("department"), new TypeToken<WxCpDepart>() {
        }.getType());
    }

}
