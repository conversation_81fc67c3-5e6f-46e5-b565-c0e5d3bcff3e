package com.niimbot.asset.weixin.base.service.impl;

import com.google.gson.JsonObject;

import com.niimbot.asset.weixin.base.dto.CuzWxCpTpLicenseOrderInfoResp;

import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpLicenseServiceImpl;

import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.License.GET_ORDER;

/**
 * <AUTHOR>
 * @date 2023/7/11 16:08
 */
public class CuzWxCpTpLicenseServiceImpl extends WxCpTpLicenseServiceImpl {

    private final WxCpTpService wxCpTpService;

    public CuzWxCpTpLicenseServiceImpl(WxCpTpService wxCpTpService) {
        super(wxCpTpService);
        this.wxCpTpService = wxCpTpService;
    }

    public CuzWxCpTpLicenseOrderInfoResp getOrderInfoNew(String orderId) throws WxErrorException {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("order_id", orderId);
        String resultText = wxCpTpService.post(wxCpTpService.getWxCpTpConfigStorage().getApiUrl(GET_ORDER) +
                getProviderAccessToken(), jsonObject.toString());
        return CuzWxCpTpLicenseOrderInfoResp.fromJson(resultText);
    }

    /**
     * 获取服务商token的拼接参数
     *
     * @return url
     * @throws WxErrorException /
     */
    private String getProviderAccessToken() throws WxErrorException {
        return "?provider_access_token=" + wxCpTpService.getWxCpProviderToken();
    }

}
