package com.niimbot.asset.weixin.base.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.experimental.Accessors;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/20 18:01
 */
@Data
@Accessors(chain = true)
public class WxCpTpIdTranslate implements Serializable {
    private static final long serialVersionUID = -4301684507150486556L;

    /**
     * 查询的企业corpid
     */
    @SerializedName("auth_corpid")
    private String authCorpId;


    @SerializedName("media_id_list")
    private List<String> mediaIdList;

    @SerializedName("output_file_name")
    private String outputFileName;

    /**
     * To json string.
     *
     * @return the string
     */
    public String toJson() {
        return WxCpGsonBuilder.create().toJson(this);
    }

}
