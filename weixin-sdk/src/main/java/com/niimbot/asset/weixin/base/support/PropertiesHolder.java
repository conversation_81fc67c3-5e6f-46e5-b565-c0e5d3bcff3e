package com.niimbot.asset.weixin.base.support;

import com.niimbot.asset.weixin.base.config.WeixinProperties;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PropertiesHolder implements ApplicationContextAware, EnvironmentAware {

    private static Environment environment;

    private static String pcDomain;

    private static WeixinProperties weixinProperties;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        weixinProperties = applicationContext.getBean(WeixinProperties.class);
    }

    public static String getWxAppId() {
        return weixinProperties.getAppId();
    }

    public static String getPcDomain() {
        return pcDomain;
    }

    public static String getWxToken() {
        return weixinProperties.getToken();
    }

    public static String getWxSuiteId() {
        return weixinProperties.getSuiteId();
    }

    public static List<String> getActive() {
        return Arrays.stream(environment.getActiveProfiles()).collect(Collectors.toList());
    }

    @Override
    public void setEnvironment(@Nonnull Environment environment) {
        PropertiesHolder.environment = environment;
        pcDomain = environment.getProperty("asset.domain.pc");
    }
}
