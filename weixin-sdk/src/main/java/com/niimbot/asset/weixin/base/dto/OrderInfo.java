package com.niimbot.asset.weixin.base.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OrderInfo implements Serializable {

    private Long orderId;

    private String orderNo;

    private JSONObject orderData;

    private Long createBy;

    private LocalDateTime createTime;

}
