package com.niimbot.asset.weixin.base.support;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.weixin.base.dto.CorpInfo;
import com.niimbot.asset.weixin.base.mapper.WxCorpMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

import static com.niimbot.asset.weixin.base.constant.WxConstant.CORP_CACHE_KEY;
import static com.niimbot.asset.weixin.base.constant.WxConstant.PMT_CODE_CACHE_KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CorpCaches implements ApplicationContextAware {

    private static RedisService redisService;

    private static WxCorpMapper mapper;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        redisService = applicationContext.getBean(RedisService.class);
        mapper = applicationContext.getBean(WxCorpMapper.class);
    }

    public static CorpInfo getCorp(Long companyId) {
        String cacheKey = String.format(CORP_CACHE_KEY, companyId);
        if (redisService.hasKey(cacheKey)) {
            return ((CorpInfo) redisService.get(cacheKey));
        }
        CorpInfo corpInfo = mapper.selectCorpInfoByCompanyId(companyId);
        Affirm.nonNull(corpInfo, "企业不存在");
        redisService.set(cacheKey, corpInfo, TimeUnit.DAYS.toDays(7L) + ThreadLocalRandom.current().nextLong(TimeUnit.MINUTES.toSeconds(2)));
        return corpInfo;
    }

    public static String getPermanentCode(String corpId) {
        String cacheKey = String.format(PMT_CODE_CACHE_KEY, corpId);
        String code = Convert.toStr(redisService.get(cacheKey), "");
        if (StrUtil.isNotBlank(code)) {
            return code;
        }
        String toCache = mapper.selectCodeByCorpId(corpId);
        redisService.set(cacheKey, toCache);
        return toCache;
    }

    public static Integer getAgentId(Long companyId) {
        return getCorp(companyId).getAgentId();
    }

    public static void clean(Long companyId, String corpId) {
        try {
            String cacheKey1 = String.format(CORP_CACHE_KEY, companyId);
            String cacheKey2 = String.format(PMT_CODE_CACHE_KEY, corpId);
            redisService.del(cacheKey1);
            redisService.del(cacheKey2);
        } catch (Exception e) {
            log.error("corpCaches clean error", e);
        }
    }

}
