package com.niimbot.asset.weixin.base.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class WxCpTpJobResult extends WxCpBaseResp {

    @SerializedName("status")
    private Integer status;

    @SerializedName("type")
    private String type;

    @SerializedName("result")
    private Result result;

    public Boolean successful() {
        return Objects.nonNull(status) && status == 3;
    }

    @Getter
    @Setter
    public static class Result implements Serializable {

        @SerializedName("contact_id_translate")
        private Url contactIdTranslate;

    }

    @Getter
    @Setter
    public static class Url implements Serializable {

        @SerializedName("url")
        private String url;

    }

    public static WxCpTpJobResult fromJson(String json) {
        return WxCpGsonBuilder.create().fromJson(json, WxCpTpJobResult.class);
    }
}
