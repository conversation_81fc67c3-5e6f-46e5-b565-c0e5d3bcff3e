package com.niimbot.asset.weixin.base.dto;

import com.google.gson.annotations.SerializedName;

import lombok.Data;
import me.chanjar.weixin.cp.bean.license.WxCpTpLicenseAccountCount;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

/**
 * <AUTHOR>
 * @date 2023/7/11 16:18
 */
@Data
public class CuzWxCpTpLicenseOrder {

    private static final long serialVersionUID = -4094302825442292644L;

    @SerializedName("order_id")
    private String orderId;

    @SerializedName("order_type")
    private Integer orderType;

    @SerializedName("order_status")
    private Integer orderStatus;

    @SerializedName("corpid")
    private String corpId;

    @SerializedName("price")
    private Long price;

    @SerializedName("account_count")
    private WxCpTpLicenseAccountCount accountCount;

    @SerializedName("account_duration")
    private CuzWxCpTpLicenseAccountDuration accountDuration;

    @SerializedName("create_time")
    private Long createTime;

    @SerializedName("pay_time")
    private Long payTime;


    /**
     * To json string.
     *
     * @return the string
     */
    public String toJson() {
        return WxCpGsonBuilder.create().toJson(this);
    }

}
