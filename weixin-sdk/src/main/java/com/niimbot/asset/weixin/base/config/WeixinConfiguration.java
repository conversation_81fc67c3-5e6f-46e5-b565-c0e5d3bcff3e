package com.niimbot.asset.weixin.base.config;

import com.niimbot.asset.weixin.base.service.impl.*;
import me.chanjar.weixin.common.redis.RedissonWxRedisOps;
import me.chanjar.weixin.common.util.http.apache.DefaultApacheHttpClientBuilder;
import me.chanjar.weixin.cp.config.impl.WxCpTpRedissonConfigImpl;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpServiceImpl;
import me.chanjar.weixin.cp.util.crypto.WxCpTpCryptUtil;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/6/11 10:11
 */
@Configuration
@EnableConfigurationProperties(WeixinProperties.class)
public class WeixinConfiguration {

    private final WeixinProperties properties;
    private final RedissonClient redissonClient;

    public WeixinConfiguration(WeixinProperties properties,
                               RedissonClient redissonClient) {
        this.properties = properties;
        this.redissonClient = redissonClient;
    }

    @Bean
    public WxCpTpRedissonConfigImpl wxCpTpRedissonConfigImpl() {
        DefaultApacheHttpClientBuilder httpClientBuilder = DefaultApacheHttpClientBuilder.get();
        httpClientBuilder.setMaxTotalConn(100);
        httpClientBuilder.setConnectionRequestTimeout(5000);
        return WxCpTpRedissonConfigImpl.builder()
                .suiteId(properties.getSuiteId())
                .suiteSecret(properties.getSecret())
                .corpId(properties.getCorpId())
                .keyPrefix("wx-cp-tp")
                .providerSecret(properties.getProviderSecret())
                .token(properties.getToken())
                .aesKey(properties.getAesKey())
                .apacheHttpClientBuilder(httpClientBuilder)
                .wxRedisOps(new RedissonWxRedisOps(redissonClient))
                .build();
    }

    @Bean
    public WxMpService wxMpService() {
        WxMpRedissonConfigImpl configStorage = new WxMpRedissonConfigImpl(redissonClient);
        configStorage.setAppId(properties.getH5AppId());
        configStorage.setSecret(properties.getH5AppSecret());

        WxMpServiceImpl wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(configStorage);
        return wxMpService;
    }

    @Bean
    public WxCpTpService wxOauthService() {
        WxCpTpRedissonConfigImpl build = WxCpTpRedissonConfigImpl.builder()
                .suiteId(properties.getOauth2().getSuiteId())
                .suiteSecret(properties.getOauth2().getSecret())
                .corpId(properties.getCorpId())
                .keyPrefix("wx-cp-tp-oauth")
                .providerSecret(properties.getProviderSecret())
                .token(properties.getToken())
                .aesKey(properties.getAesKey())
                .wxRedisOps(new RedissonWxRedisOps(redissonClient))
                .build();
        WxCpTpService service = new WxCpTpServiceImpl();
        service.setWxCpTpConfigStorage(build);
        return service;
    }

    @Bean
    public WxCpTpService wxCpTpService(WxCpTpRedissonConfigImpl wxCpTpRedissonConfigImpl) {
        WxCpTpService service = new WxCpTpServiceImpl();
        service.setWxCpTpConfigStorage(wxCpTpRedissonConfigImpl);
        service.setWxCpTpDepartmentService(new CuzWxCpTpDepartmentServiceImpl(service, wxCpTpRedissonConfigImpl));
        service.setWxCpTpUserService(new CuzWxCpTpUserServiceImpl(service, wxCpTpRedissonConfigImpl));
        service.setWxCpTpLicenseService(new CuzWxCpTpLicenseServiceImpl(service));
        service.setWxCpTpContactService(new CuzWxCpTpContactServiceImpl(service));
        service.setWxCpTpMediaService(new CuzWxCpTpMediaServiceImpl(service));
        service.setWxCpTpOrderService(new CuzWxCpTpOrderServiceImpl(service));
        return service;
    }

    @Bean
    public WxCpTpCryptUtil wxCpTpCrypt(WxCpTpRedissonConfigImpl wxCpTpRedissonConfigImpl) {
        return new WxCpTpCryptUtil(wxCpTpRedissonConfigImpl);
    }

}
