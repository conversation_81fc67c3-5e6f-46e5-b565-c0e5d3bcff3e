<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dynamicform.mapper.AsFormConfigMapper">

    <select id="checkUnique" resultType="java.lang.Integer">
        select count(*) from ${tableName}
        where
        is_delete = 0
        and company_id = #{companyId}
        and ${field} = #{value}
        <if test="id!=null">
            and id != #{id}
        </if>
        limit 1
    </select>

    <select id="checkUniqueWithRecycle" resultType="java.lang.Integer">
        select count(t2.id) from as_recycle_bin t1 join ${tableName} t2 on t1.res_id = t2.id
        where t2.is_delete = 1 and t1.company_id = #{companyId} and t2.company_id = #{companyId}
        and t2.${field} = #{value}
        <if test="id!=null">
            and t2.id != #{id}
        </if>
        limit 1
    </select>
</mapper>
