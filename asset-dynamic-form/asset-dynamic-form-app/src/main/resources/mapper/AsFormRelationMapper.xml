<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dynamicform.mapper.AsFormRelationMapper">

    <select id="fillOrg" resultType="java.util.Map">
        SELECT
            id,
            orgName,
            orgC<PERSON>,
            director,
            directorText
        FROM
        (
            SELECT
            o.id as id,
            o.org_name AS orgName,
            o.org_code AS orgCode,
            JSON_EXTRACT( o.director, '$[0]' ) AS director,
            e.emp_name AS directorText
            FROM
            as_org o
            LEFT JOIN as_cus_employee e ON JSON_EXTRACT( o.director, '$[0]' ) = e.id
            AND o.company_id = e.company_id
            AND e.is_delete = 0
            WHERE
                o.is_delete = 0
            AND o.company_id = #{companyId}
        ) t
        where ${querySqlText}
        limit 1
    </select>

    <select id="fillArea" resultType="java.util.Map">
        select
            id,
            areaName,
            areaCode,
            orgId,
            orgIdText,
            admins,
            adminsText,
            areaDesc
        from (SELECT a.id,
                     a.area_name AS areaName,
                     a.area_code AS areaCode,
                     a.org_id    AS orgId,
                     o.org_name  as orgIdText,
                     a.admins,
                     e.emp_name  as adminsText,
                     a.area_desc as areaDesc
              FROM as_area a
                       left join as_org o on a.company_id = o.company_id and o.is_delete = 0 and
                                             a.org_id = o.id
                       left join as_cus_employee e
                                 on a.company_id = e.company_id and e.is_delete = 0 and
                                    a.admins = e.id
              WHERE a.is_delete = 0
                AND a.company_id = #{companyId}
             ) t
        where ${querySqlText}
        limit 1
    </select>

    <select id="fillRepo" resultType="java.util.Map">
        select
            id,
            name,
            code,
            managerOwner,
            managerOwnerText,
            admins,
            adminsText,
            remark
        from (SELECT r.id,
                     r.name,
                     r.code,
                     r.manager_owner                as managerOwner,
                     o.org_name                     as managerOwnerText,
                     JSON_EXTRACT(r.admins, '$[0]') as admins,
                     e.emp_name                     as adminsText,
                     r.remark
              FROM as_repository r
                       left join as_org o on r.company_id = o.company_id and o.is_delete = 0 and
                                             r.manager_owner = o.id
                       left join as_cus_employee e
                                 on r.company_id = e.company_id and e.is_delete = 0 and
                                    JSON_EXTRACT(r.admins, '$[0]') = e.id
              WHERE r.is_delete = 0
                AND r.company_id = #{companyId}
             ) t
        where ${querySqlText}
        limit 1
    </select>

    <select id="fillCate" resultType="java.util.Map">
        select
            id,
            categoryName,
            categoryCode
        from (
               SELECT
                   id,
                   category_name as categoryName,
                   category_code as categoryCode
            FROM as_category
            WHERE is_delete = 0
              AND company_id = #{companyId}
        ) t
        where ${querySqlText}
        limit 1
    </select>

    <select id="fillMCate" resultType="java.util.Map">
        select
            id,
            categoryName,
            categoryCode
        from (
                 SELECT
                     id,
                     category_name as categoryName,
                     category_code as categoryCode
                 FROM as_material_category
                 WHERE is_delete = 0
                   AND company_id = #{companyId}
             ) t
        where ${querySqlText}
        limit 1
    </select>

</mapper>
