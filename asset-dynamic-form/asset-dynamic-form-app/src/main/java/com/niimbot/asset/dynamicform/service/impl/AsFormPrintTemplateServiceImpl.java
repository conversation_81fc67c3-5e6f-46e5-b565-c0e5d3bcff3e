package com.niimbot.asset.dynamicform.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.mapper.AsFormPrintTemplateMapper;
import com.niimbot.asset.dynamicform.model.AsFormPrintTemplate;
import com.niimbot.asset.dynamicform.service.AsFormPrintTemplateService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.dynamicform.FormPrintTemplateDto;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;

/**
 * <AUTHOR>
 * @date 2023/7/3 17:03
 */
@Service
public class AsFormPrintTemplateServiceImpl extends ServiceImpl<AsFormPrintTemplateMapper, AsFormPrintTemplate> implements AsFormPrintTemplateService {

    @Override
    public List<AsFormPrintTemplate> printTplList(Integer orderType) {
        // 查询系统模板和自建模板
        return list(Wrappers.lambdaQuery(AsFormPrintTemplate.class)
                .select(AsFormPrintTemplate::getId,
                        AsFormPrintTemplate::getTplName,
                        AsFormPrintTemplate::getCompanyId,
                        AsFormPrintTemplate::getOrderType,
                        AsFormPrintTemplate::getCreateBy,
                        AsFormPrintTemplate::getCreateTime,
                        AsFormPrintTemplate::getUpdateBy,
                        AsFormPrintTemplate::getUpdateTime)
                .eq(AsFormPrintTemplate::getOrderType, orderType)
                .in(AsFormPrintTemplate::getCompanyId, ListUtil.of(0, LoginUserThreadLocal.getCompanyId()))
                .orderByDesc(AsFormPrintTemplate::getUpdateTime));
    }

    @Override
    public AsFormPrintTemplate getPrintTpl(Long tplId) {
        return getOne(Wrappers.lambdaQuery(AsFormPrintTemplate.class)
                .in(AsFormPrintTemplate::getCompanyId, ListUtil.of(0, LoginUserThreadLocal.getCompanyId()))
                .eq(AsFormPrintTemplate::getId, tplId));
    }

    @Override
    public FormPrintTemplateDto add(AsFormPrintTemplate formPrintTemplate) {
        checkNameRepeat(formPrintTemplate.getTplName(), formPrintTemplate.getOrderType(), null);
        checkMaxLimit(formPrintTemplate.getOrderType());
        formPrintTemplate.setId(IdUtils.getId());
        formPrintTemplate.setCompanyId(LoginUserThreadLocal.getCompanyId());
        save(formPrintTemplate);
        return new FormPrintTemplateDto()
                .setId(formPrintTemplate.getId())
                .setOrderType(formPrintTemplate.getOrderType())
                .setTplName(formPrintTemplate.getTplName());
    }

    @Override
    public FormPrintTemplateDto edit(AsFormPrintTemplate formPrintTemplate) {
        AsFormPrintTemplate templateBefore = checkExists(formPrintTemplate.getId());
        checkNameRepeat(formPrintTemplate.getTplName(), templateBefore.getOrderType(), formPrintTemplate.getId());
        update(Wrappers.lambdaUpdate(AsFormPrintTemplate.class)
                .set(AsFormPrintTemplate::getTplName, formPrintTemplate.getTplName())
                .set(AsFormPrintTemplate::getTplConfig, formPrintTemplate.getTplConfig())
                .set(AsFormPrintTemplate::getTplContent, formPrintTemplate.getTplContent())
                .eq(AsFormPrintTemplate::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsFormPrintTemplate::getId, formPrintTemplate.getId()));
        return new FormPrintTemplateDto()
                .setId(formPrintTemplate.getId())
                .setOrderType(templateBefore.getOrderType())
                .setTplName(templateBefore.getTplName());
    }

    @Override
    public FormPrintTemplateDto delete(Long tplId) {
        AsFormPrintTemplate templateBefore = checkExists(tplId);
        removeById(tplId);
        return new FormPrintTemplateDto()
                .setId(tplId)
                .setOrderType(templateBefore.getOrderType())
                .setTplName(templateBefore.getTplName());
    }

    @Override
    public FormPrintTemplateDto copy(Long tplId) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        AsFormPrintTemplate one = getOne(Wrappers.lambdaQuery(AsFormPrintTemplate.class)
                .in(AsFormPrintTemplate::getCompanyId, ListUtil.of(companyId, 0L))
                .eq(AsFormPrintTemplate::getId, tplId));
        if (one == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印模板不存在");
        }
        checkMaxLimit(one.getOrderType());
        // 查询副本
        List<AsFormPrintTemplate> nameList = list(Wrappers.lambdaQuery(AsFormPrintTemplate.class)
                .select(AsFormPrintTemplate::getTplName)
                .eq(AsFormPrintTemplate::getOrderType, one.getOrderType())
                .eq(AsFormPrintTemplate::getCompanyId, companyId)
                .likeRight(AsFormPrintTemplate::getTplName, one.getTplName() + "-副本"));
        Set<String> nameSet = nameList.stream().map(AsFormPrintTemplate::getTplName).collect(Collectors.toSet());
        String name = null;
        int idx = 1;
        while (name == null) {
            String nameTpl = one.getTplName() + "-副本" + idx;
            if (!nameSet.contains(nameTpl)) {
                name = nameTpl;
            }
            idx++;
        }

        AsFormPrintTemplate newTpl = new AsFormPrintTemplate();
        newTpl.setId(IdUtils.getId())
                .setCompanyId(companyId)
                .setOrderType(one.getOrderType())
                .setTplConfig(one.getTplConfig())
                .setTplContent(one.getTplContent())
                .setTplName(name);
        save(newTpl);
        return new FormPrintTemplateDto()
                .setId(newTpl.getId())
                .setOrderType(one.getOrderType())
                .setTplName(one.getTplName());
    }

    private AsFormPrintTemplate checkExists(Long tplId) {
        AsFormPrintTemplate one = getOne(Wrappers.lambdaQuery(AsFormPrintTemplate.class)
                .eq(AsFormPrintTemplate::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsFormPrintTemplate::getId, tplId));
        if (one == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印模板不存在");
        }
        return one;
    }

    private void checkNameRepeat(String tplName, Integer orderType, Long excludeTplId) {
        if (count(Wrappers.lambdaQuery(AsFormPrintTemplate.class)
                .eq(AsFormPrintTemplate::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsFormPrintTemplate::getOrderType, orderType)
                .eq(AsFormPrintTemplate::getTplName, tplName)
                .ne(excludeTplId != null, AsFormPrintTemplate::getId, excludeTplId)) > 0) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印模板名称【" + tplName + "】已存在");
        }
    }

    private void checkMaxLimit(Integer orderType) {
        if (count(Wrappers.lambdaQuery(AsFormPrintTemplate.class)
                .eq(AsFormPrintTemplate::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsFormPrintTemplate::getOrderType, orderType)) >= 10) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "最多新增10个打印模板");
        }
    }
}
