package com.niimbot.asset.dynamicform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.mapper.AsFormConfigMapper;
import com.niimbot.asset.dynamicform.model.AsFormConfig;
import com.niimbot.asset.dynamicform.service.AsFormConfigService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;

import org.springframework.stereotype.Service;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @since 2022/7/5 15:04
 */
@Service
public class AsFormConfigServiceImpl extends ServiceImpl<AsFormConfigMapper, AsFormConfig> implements AsFormConfigService {

    @Override
    public Boolean checkUnique(String tableName, String tableColumn, String fieldCode, String value, Long id) {
        if (StrUtil.isAllNotEmpty(tableName, tableColumn, fieldCode, value)) {
            String field = tableColumn + " ->> '$.\"" + fieldCode + "\"'";
            boolean flag = this.getBaseMapper().checkUnique(
                    tableName, field, value, LoginUserThreadLocal.getCompanyId(), id) > 0;
            // 未回收的重复
            if (flag) {
                return true;
            }
            // 查回收站
            return this.getBaseMapper().checkUniqueWithRecycle(tableName, field, value, LoginUserThreadLocal.getCompanyId(), id) > 0;
        }
        return false;
    }
}
