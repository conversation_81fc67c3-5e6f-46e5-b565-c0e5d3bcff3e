package com.niimbot.asset.dynamicform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageQuery;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.mapper.AsFormRelationMapper;
import com.niimbot.asset.dynamicform.model.AsFormConfig;
import com.niimbot.asset.dynamicform.service.AsFormConfigService;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.filter.SQLFilter;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.dynamicform.FormRelationDto;
import com.niimbot.dynamicform.FormRelationQry;
import com.niimbot.dynamicform.ValidatorModel;
import com.niimbot.easydesign.core.dto.page.PageResult;
import com.niimbot.easydesign.core.exception.api.HttpCode;
import com.niimbot.easydesign.core.exception.api.Result;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCfgCO;
import com.niimbot.easydesign.form.dto.clientobject.FormRelationFieldCO;
import com.niimbot.easydesign.form.dto.sdk.*;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.easydesign.form.dto.viewobject.ResultFailVO;
import com.niimbot.easydesign.form.dto.viewobject.ValidResultVO;
import com.niimbot.easydesign.sdk.client.EdFormClient;
import com.niimbot.equipment.EntMatPlanData;
import com.niimbot.equipment.EntSntPlanData;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetImportDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/7/3 18:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsFormServiceImpl implements AsFormService, ApplicationListener<ApplicationReadyEvent> {

    private static final String REDIS_FORM_CONFIG = "form_config";

    private static final List<String> BIZ_TYPE_LIST = ListUtil.of(
            FormFieldCO.YZC_EMP,
            FormFieldCO.YZC_ORG,
            FormFieldCO.YZC_REPOSITORY,
            FormFieldCO.YZC_ASSET_CATE,
            FormFieldCO.YZC_MATERIAL_CATE
    );

    private static String getRedisFormConfig(String bizType) {
        return REDIS_FORM_CONFIG + ":" + bizType;
    }

    private final EdFormClient edFormClient;
    private final RedisService redisService;
    private final AsFormConfigService formConfigService;
    private final ThreadPoolTaskExecutor taskExecutor;
    private final AsFormRelationMapper formRelationMapper;

    /**
     * 后续替换成动态表单配置
     */
    private final FormVO ENT_MAT_PLAN_FORM = new FormVO();
    private final FormVO ENT_SNT_PLAN_FORM = new FormVO();

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // 设备保养计划 设备巡检计划 临时表单
        entForm();
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return;
        }

        List<AsFormConfig> list = formConfigService.list();
        list.forEach(f -> redisService.set(getRedisFormConfig(f.getBizType()), f));
    }

    void entForm() {
        // 字段类型基础信息
        FormBaseFieldCO input = this.getBaseFieldByType(FormFieldCO.TEXT_INPUT);
        FormBaseFieldCO datetime = this.getBaseFieldByType(FormFieldCO.DATETIME);
        FormBaseFieldCO employee = this.getBaseFieldByType(FormFieldCO.YZC_EMP);
        FormBaseFieldCO select = this.getBaseFieldByType(FormFieldCO.SELECT_DROPDOWN);
        FormBaseFieldCO images = this.getBaseFieldByType(FormFieldCO.IMAGES);
        FormBaseFieldCO files = this.getBaseFieldByType(FormFieldCO.FILES);
        FormBaseFieldCO number = this.getBaseFieldByType(FormFieldCO.NUMBER_INPUT);
        datetime.getFieldProps().put("dateFormatType", "yyyy-MM-dd HH:mm:ss");
        input.getFieldProps().put("hidden", false);

        // 设备保养计划表单
        List<FormFieldCO> entMatPlanFields = new ArrayList<>(16);
        // 方案名称
        FormFieldCO planName = new FormFieldCO();
        planName.setFieldCode(EntMatPlanData.Fields.planName);
        planName.setFieldName("方案名称");
        copy(planName, input);
        planName.setRequiredProps(true);
        entMatPlanFields.add(planName);
        // 循环方式
        FormFieldCO taskType = new FormFieldCO();
        taskType.setFieldCode(EntMatPlanData.Fields.taskType);
        taskType.setFieldName("循环方式");
        copy(taskType, select);
        taskType.getFieldProps().put("defaultValue", "一次性任务");
        taskType.getFieldProps().put("values", Lists.newArrayList("一次性任务", "循环任务"));
        taskType.setRequiredProps(true);
        entMatPlanFields.add(taskType);
        // 任务开始时间
        FormFieldCO taskBeginTime = new FormFieldCO();
        taskBeginTime.setFieldCode(EntMatPlanData.Fields.taskBeginTime);
        taskBeginTime.setFieldName("开始时间");
        copy(taskBeginTime, datetime);
        entMatPlanFields.add(taskBeginTime);
        // 任务结束时间
        FormFieldCO taskEndTime = new FormFieldCO();
        taskEndTime.setFieldCode(EntMatPlanData.Fields.taskEndTime);
        taskEndTime.setFieldName("结束时间");
        copy(taskEndTime, datetime);
        entMatPlanFields.add(taskEndTime);
        // 保养负责人
        FormFieldCO maintainUser = new FormFieldCO();
        maintainUser.setFieldCode(EntMatPlanData.Fields.maintainUser);
        maintainUser.setFieldName("保养负责人");
        copy(maintainUser, employee);
        maintainUser.setRequiredProps(true);
        entMatPlanFields.add(maintainUser);
        // 保养供应商
        FormFieldCO maintainSupplier = new FormFieldCO();
        maintainSupplier.setFieldCode(EntMatPlanData.Fields.maintainSupplier);
        maintainSupplier.setFieldName("保养供应商");
        copy(maintainSupplier, input);
        entMatPlanFields.add(maintainSupplier);
        // 保养联系人
        FormFieldCO maintainContact = new FormFieldCO();
        maintainContact.setFieldCode(EntMatPlanData.Fields.maintainContact);
        maintainContact.setFieldName("保养联系人");
        copy(maintainContact, input);
        entMatPlanFields.add(maintainContact);
        // 保养联系人方式
        FormFieldCO maintainContactWay = new FormFieldCO();
        maintainContactWay.setFieldCode(EntMatPlanData.Fields.maintainContactWay);
        maintainContactWay.setFieldName("保养联系人方式");
        copy(maintainContactWay, input);
        entMatPlanFields.add(maintainContactWay);
        // 描述
        FormFieldCO desc = new FormFieldCO();
        desc.setFieldCode(EntMatPlanData.Fields.desc);
        desc.setFieldName("计划备注");
        copy(desc, input);
        entMatPlanFields.add(desc);
        // 图片
        FormFieldCO maintainImages = new FormFieldCO();
        maintainImages.setFieldCode(EntMatPlanData.Fields.images);
        maintainImages.setFieldName("图片");
        copy(maintainImages, images);
        entMatPlanFields.add(maintainImages);
        // 附件
        FormFieldCO attachment = new FormFieldCO();
        attachment.setFieldCode(EntMatPlanData.Fields.attachment);
        attachment.setFieldName("附件");
        copy(attachment, files);
        entMatPlanFields.add(attachment);
        // 循环周期
        FormFieldCO periodic = new FormFieldCO();
        periodic.setFieldCode(EntMatPlanData.Fields.periodic);
        periodic.setFieldName("循环周期");
        copy(periodic, input);
        periodic.getFieldProps().put("hidden", true);
        entMatPlanFields.add(periodic);
        ENT_MAT_PLAN_FORM.setFormId(-1L);
        ENT_MAT_PLAN_FORM.setApplicationBizId(-1L);
        ENT_MAT_PLAN_FORM.setFormName("设备保养计划");
        ENT_MAT_PLAN_FORM.setFormIntro("设备保养计划");
        ENT_MAT_PLAN_FORM.setFormProps(new JSONObject());
        ENT_MAT_PLAN_FORM.setFormFields(entMatPlanFields);

        // 设备巡检计划表单
        List<FormFieldCO> entSntPlanFields = new ArrayList<>(16);
        // 计划名称
        FormFieldCO entSntPlanName = new FormFieldCO();
        entSntPlanName.setFieldCode(EntSntPlanData.Fields.planName);
        entSntPlanName.setFieldName("方案名称");
        copy(entSntPlanName, input);
        entSntPlanName.setRequiredProps(true);
        entSntPlanFields.add(entSntPlanName);
        // 任务类型
        FormFieldCO entSntPlanTaskType = new FormFieldCO();
        entSntPlanTaskType.setFieldCode(EntSntPlanData.Fields.taskType);
        entSntPlanTaskType.setFieldName("任务类型");
        copy(entSntPlanTaskType, select);
        entSntPlanTaskType.getFieldProps().put("defaultValue", "一次性任务");
        entSntPlanTaskType.getFieldProps().put("values", Lists.newArrayList("一次性任务", "循环任务"));
        entSntPlanTaskType.setRequiredProps(true);
        entSntPlanFields.add(entSntPlanTaskType);
        // 计划开始时间
        FormFieldCO entSntPlanTaskBeginTime = new FormFieldCO();
        entSntPlanTaskBeginTime.setFieldCode(EntSntPlanData.Fields.planBeginTime);
        entSntPlanTaskBeginTime.setFieldName("计划开始时间");
        copy(entSntPlanTaskBeginTime, datetime);
        entSntPlanFields.add(entSntPlanTaskBeginTime);
        // 计划结束时间
        FormFieldCO entSntPlanTaskEndTime = new FormFieldCO();
        entSntPlanTaskEndTime.setFieldCode(EntSntPlanData.Fields.planEndTime);
        entSntPlanTaskEndTime.setFieldName("计划结束时间");
        copy(entSntPlanTaskEndTime, datetime);
        entSntPlanFields.add(entSntPlanTaskEndTime);
        // 巡检管理员
        FormFieldCO entSntPlanManagers = new FormFieldCO();
        entSntPlanManagers.setFieldCode(EntSntPlanData.Fields.managers);
        entSntPlanManagers.setFieldName("巡检管理员");
        copy(entSntPlanManagers, employee);
        entSntPlanManagers.setRequiredProps(true);
        entSntPlanFields.add(entSntPlanManagers);
        // 巡检人
        FormFieldCO entSntPlanExecutors = new FormFieldCO();
        entSntPlanExecutors.setFieldCode(EntSntPlanData.Fields.executors);
        entSntPlanExecutors.setFieldName("巡检人");
        copy(entSntPlanExecutors, employee);
        entSntPlanExecutors.setRequiredProps(true);
        entSntPlanFields.add(entSntPlanExecutors);
        // 图片
        FormFieldCO entSntPlanImages = new FormFieldCO();
        entSntPlanImages.setFieldCode(EntSntPlanData.Fields.images);
        entSntPlanImages.setFieldName("图片");
        copy(entSntPlanImages, images);
        entSntPlanFields.add(entSntPlanImages);
        // 相关附件
        FormFieldCO entSntPlanAttachment = new FormFieldCO();
        entSntPlanAttachment.setFieldCode(EntSntPlanData.Fields.attachment);
        entSntPlanAttachment.setFieldName("附件");
        copy(entSntPlanAttachment, files);
        entSntPlanFields.add(entSntPlanAttachment);
        // 巡检备注
        FormFieldCO entSntPlanRemark = new FormFieldCO();
        entSntPlanRemark.setFieldCode(EntSntPlanData.Fields.remark);
        entSntPlanRemark.setFieldName("巡检备注");
        copy(entSntPlanRemark, input);
        entSntPlanFields.add(entSntPlanRemark);
        // 任务次数
        FormFieldCO entSntPlanTaskCount = new FormFieldCO();
        entSntPlanTaskCount.setFieldCode(EntSntPlanData.Fields.taskCount);
        entSntPlanTaskCount.setFieldName("任务次数");
        copy(entSntPlanTaskCount, number);
        entSntPlanFields.add(entSntPlanTaskCount);
        // 间隔频率
        FormFieldCO entSntPlanPeriodic = new FormFieldCO();
        entSntPlanPeriodic.setFieldCode(EntSntPlanData.Fields.periodic);
        entSntPlanPeriodic.setFieldName("间隔频率");
        copy(entSntPlanPeriodic, input);
        entSntPlanPeriodic.getFieldProps().put("hidden", true);
        entSntPlanFields.add(entSntPlanPeriodic);

        ENT_SNT_PLAN_FORM.setFormId(-1L);
        ENT_SNT_PLAN_FORM.setApplicationBizId(-1L);
        ENT_SNT_PLAN_FORM.setFormName("设备巡检计划");
        ENT_SNT_PLAN_FORM.setFormIntro("设备巡检计划");
        ENT_SNT_PLAN_FORM.setFormProps(new JSONObject());
        ENT_SNT_PLAN_FORM.setFormFields(entSntPlanFields);
    }

    void copy(FormFieldCO target, FormBaseFieldCO source) {
        FormBaseFieldCO copy = BeanUtil.copyProperties(source, FormBaseFieldCO.class);
        target.setApplicationId(0L);
        target.setFieldType(copy.getFieldType());
        target.setFieldTypeText(copy.getFieldTypeText());
        target.setFieldProps(copy.getFieldProps());
        target.setExtProps(copy.getExtProps());
        target.setFieldCfg(commonFieldConfig());
    }

    FormFieldCfgCO commonFieldConfig() {
        FormFieldCfgCO commonFieldCfg = new FormFieldCfgCO();
        commonFieldCfg.setCanCopy(false);
        commonFieldCfg.setCanDelete(false);
        commonFieldCfg.setCanDrag(false);
        commonFieldCfg.setCanModifyHidden(false);
        commonFieldCfg.setCanModifyRequired(false);
        commonFieldCfg.setReadOnly(true);
        commonFieldCfg.setValuesReadOnly(true);
        return commonFieldCfg;
    }

    @Override
    public String getBizCode(String type) {
        AsFormConfig formConfig = getFormConfig(type);
        return formConfig.getCode();
    }

    @Override
    public FormVO assetTpl() {
        return getTpl(BIZ_TYPE_ASSET, ListUtil.of(Convert.toStr(LoginUserThreadLocal.getCompanyId())));
    }

    @Override
    public FormVO materialTpl() {
        return getTpl(BIZ_TYPE_MATERIAL, ListUtil.of(Convert.toStr(LoginUserThreadLocal.getCompanyId())));
    }

    @Override
    public FormVO standardTpl() {
        // 0代表平台数据，1代表运营后台数据
        return getTpl(BIZ_TYPE_STANDARD, ListUtil.of("0"));
    }

    @Override
    public FormVO assetOrderTpl(String bizType) {
        return getTpl(bizType, ListUtil.of(Convert.toStr(LoginUserThreadLocal.getCompanyId())));
    }

    @Override
    public FormVO getTplByType(String type) {
        switch (type) {
            case BIZ_TYPE_ASSET:
                return assetTpl();
            case BIZ_TYPE_MATERIAL:
                return materialTpl();
            case BIZ_TYPE_STANDARD:
                return standardTpl();
            default:
                return getTpl(type, ListUtil.of(Convert.toStr(LoginUserThreadLocal.getCompanyId())));
        }
    }

    @Override
    public PageResult<FormSdkVO> standardPage(List<String> bizTenantIds, String name, Integer pageNum, Integer pageSize) {
        AsFormConfig formConfig = getFormConfig(BIZ_TYPE_STANDARD);
        SdkFormPageGetQry sdkFormPageGetQry = new SdkFormPageGetQry();
        sdkFormPageGetQry.setBizTenantIds(bizTenantIds);
        sdkFormPageGetQry.setFormName(name);
        sdkFormPageGetQry.setBizCode(formConfig.getCode());
        sdkFormPageGetQry.setOrderBy("id");
        sdkFormPageGetQry.setOrderDirection(PageQuery.DESC);
        if (ObjectUtil.isNotNull(pageNum)) {
            sdkFormPageGetQry.setPageIndex(pageNum);
        }
        if (ObjectUtil.isNotNull(pageSize)) {
            sdkFormPageGetQry.setPageSize(pageSize);
        }
        Result<PageResult<FormSdkVO>> pageResult = edFormClient.pageSearch(sdkFormPageGetQry);
        if (pageResult.getCode() == HttpCode.SC_OK) {
            return pageResult.getData();
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误，" + pageResult.getMessage());
        }
    }

    @Override
    public FormVO getByFormId(Long formId, Long companyId, Boolean throwEx) {
        FormByIdQry formByIdQry = new FormByIdQry(formId, Convert.toStr(companyId));
        Result<FormVO> formVOResult = edFormClient.formById(formByIdQry.toMap());
        if (formVOResult.getCode() == HttpCode.SC_OK) {
            return formVOResult.getData();
        } else {
            if (BooleanUtil.isTrue(throwEx)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误，" + formVOResult.getMessage());
            } else {
                return null;
            }
        }
    }

    @Override
    public FormVO getByFormId(Long formId, List<Long> companyIds, Boolean throwEx) {
        if (CollUtil.isEmpty(companyIds)) {
            if (BooleanUtil.isTrue(throwEx)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误");
            } else {
                return null;
            }
        } else {
            FormVO formVO = null;
            for (Long companyId : companyIds) {
                formVO = getByFormId(formId, companyId, false);
                if (ObjectUtil.isNotNull(formVO)) {
                    break;
                }
            }
            if (ObjectUtil.isNull(formVO) && BooleanUtil.isTrue(throwEx)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误");
            } else {
                return formVO;
            }
        }
    }

    @Override
    public List<FormVO> getByFormIds(List<Long> standardIds) {
        if (CollUtil.isEmpty(standardIds)) {
            return new ArrayList<>();
        }
        FormByIdsQry qry = new FormByIdsQry(standardIds);
        Result<List<FormVO>> listResult = edFormClient.formByIds(qry);
        if (listResult.getCode() == HttpCode.SC_OK) {
            return listResult.getData();
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误，" + listResult.getMessage());
        }
    }

    @Override
    public void formValidator(List<JSONObject> formData, Long companyId, FormVO formVO, String formType) {
        AsFormConfig formConfig = getFormConfig(formType);
        Long formId = formVO.getFormId();
        genericValidator(formData, formId, companyId);
        formData.forEach(f -> uniqueValidator(f, formVO.getFormFields(), formConfig, true));
    }

    /**
     * 资产表单校验
     */
    @Override
    public void formValidator(List<JSONObject> formData, FormVO formVO, String formType) {
        formValidator(formData, LoginUserThreadLocal.getCompanyId(), formVO, formType);
    }

    @Override
    public boolean uniqueSerialNo(List<JSONObject> formData, FormVO formVO, String formType) {
        AsFormConfig formConfig = getFormConfig(formType);
        List<FormFieldCO> formFields = formVO.getFormFields();
        Optional<FormFieldCO> first = formFields.stream().filter(f ->
                ListUtil.of(FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_MATERIAL_SERIALNO, FormFieldCO.YZC_SERIALNO)
                        .contains(f.getFieldType())).findFirst();
        if (first.isPresent()) {
            FormFieldCO fieldCO = first.get();
            for (JSONObject f : formData) {
                // 校验唯一性
                String value = Convert.toStr(f.get(fieldCO.getFieldCode()));
                boolean bool = formConfigService.checkUnique(
                        formConfig.getTableName(),
                        formConfig.getTableColumn(),
                        fieldCO.getFieldCode(),
                        value, null);
                if (BooleanUtil.isFalse(bool)) {
                    return bool;
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public void fieldValueValidator(JSONObject formData, Long formId) {
        FormValidatorCmd formValidatorCmd = new FormValidatorCmd(formData, formId,
                Convert.toStr(LoginUserThreadLocal.getCompanyId()));
        Result<ValidResultVO> result = edFormClient.fieldValidator(formValidatorCmd);
        if (HttpCode.SC_OK == result.getCode()) {
            ValidResultVO data = result.getData();
            if (BooleanUtil.isFalse(data.getPassable())) {
                data.getResultFails().stream().findFirst().ifPresent(f -> {
                    throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, f.getMsg());
                });
            }
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, result.getMessage());
        }
    }

    @Override
    public void fieldValidatorWithId(ValidatorModel formData, List<FormFieldCO> fieldList, String formType) {
        AsFormConfig formConfig = getFormConfig(formType);
        fieldValidator(formData.getFormData(), fieldList);
        uniqueValidator(formData, fieldList, formConfig);
    }

    @Override
    public void fieldValidator(List<JSONObject> formData, List<FormFieldCO> fieldList) {
        // 拆分数据
        List<List<JSONObject>> tasksData = Lists.partition(formData, 500);
        // 校验总结
        List<Result<List<ValidResultVO>>> results = new CopyOnWriteArrayList<>();

        CountDownLatch latch = new CountDownLatch(tasksData.size());
        for (List<JSONObject> taskData : tasksData) {
            taskExecutor.submit(() -> {
                FieldMultiValidatorCmd cmd = new FieldMultiValidatorCmd(taskData, fieldList);
                Result<List<ValidResultVO>> listResult = edFormClient.fieldMultiValidator(cmd);
                results.add(listResult);
                latch.countDown();
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("表单数据校验异常", e);
            Thread.currentThread().interrupt();
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单数据校验异常");
        }

        for (Result<List<ValidResultVO>> listResult : results) {
            if (HttpCode.SC_OK == listResult.getCode()) {
                List<ValidResultVO> data = listResult.getData();
                for (ValidResultVO datum : data) {
                    if (BooleanUtil.isFalse(datum.getPassable())) {
                        datum.getResultFails().stream().findFirst().ifPresent(f -> {
                            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, f.getMsg());
                        });
                    }
                }
            } else {
                throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, listResult.getMessage());
            }
        }
    }

    @Override
    public void fieldValidator(JSONObject formData, List<FormFieldCO> fieldList) {
        // 特殊处理图片附件类型
        List<FormFieldCO> fieldCOList = fieldList.stream().filter(f -> {
            if (f.getFieldType().equals(FormFieldCO.IMAGES)
                    || f.getFieldType().equals(FormFieldCO.FILES)
                    || f.getFieldType().equals(FormFieldCO.MULTI_SELECT_DROPDOWN)) {
                if (BooleanUtil.isTrue(f.requiredProps())) {
                    return true;
                } else {
                    return formData.containsKey(f.getFieldCode());
                }
            }
            return true;
        }).collect(Collectors.toList());
        FieldValidatorCmd cmd = new FieldValidatorCmd(formData, fieldCOList);
        Result<ValidResultVO> result = edFormClient.fieldValidator(cmd);
        if (HttpCode.SC_OK == result.getCode()) {
            ValidResultVO data = result.getData();
            if (BooleanUtil.isFalse(data.getPassable())) {
                data.getResultFails().stream().findFirst().ifPresent(f -> {
                    throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, f.getMsg());
                });
            }
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, result.getMessage());
        }
    }

    @Override
    public void fieldFormMultiValidator(List<JSONObject> formDataList, Long formId) {
        // 拆分数据
        List<List<JSONObject>> tasksData = Lists.partition(formDataList, 50);
        // 校验总结
        List<Result<List<ValidResultVO>>> results = new CopyOnWriteArrayList<>();
        CountDownLatch latch = new CountDownLatch(tasksData.size());
        for (List<JSONObject> taskData : tasksData) {
            taskExecutor.submit(() -> {
                FormMultiValidatorCmd cmd = new FormMultiValidatorCmd(taskData, formId,
                        Convert.toStr(LoginUserThreadLocal.getCompanyId()));
                Result<List<ValidResultVO>> listResult = edFormClient.fieldFormMultiValidator(cmd);
                results.add(listResult);
                latch.countDown();
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("表单数据校验异常", e);
            Thread.currentThread().interrupt();
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单数据校验异常");
        }

        for (Result<List<ValidResultVO>> listResult : results) {
            if (HttpCode.SC_OK == listResult.getCode()) {
                List<ValidResultVO> data = listResult.getData();
                for (ValidResultVO datum : data) {
                    if (BooleanUtil.isFalse(datum.getPassable())) {
                        datum.getResultFails().stream().findFirst().ifPresent(f -> {
                            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, f.getMsg());
                        });
                    }
                }
            } else {
                throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, listResult.getMessage());
            }
        }
    }

    @Override
    public void importValidator(JSONObject formData,
                                List<FormFieldCO> baseFieldList,
                                List<FormFieldCO> standardFieldList,
                                Map<String, AssetImportDto.FieldData> fieldDataMap,
                                String bizType) {
        // 1-校验数据
        baseFieldList.addAll(standardFieldList);
        List<FormFieldCO> allField = new ArrayList<>();
        allField.addAll(baseFieldList);
        allField.addAll(standardFieldList);
        FieldValidatorCmd cmd = new FieldValidatorCmd(formData, allField);
        Result<ValidResultVO> result = edFormClient.fieldValidator(cmd);
        if (HttpCode.SC_OK == result.getCode()) {
            ValidResultVO data = result.getData();
            if (BooleanUtil.isFalse(data.getPassable())) {
                List<ResultFailVO> resultFails = data.getResultFails();
                for (ResultFailVO resultFail : resultFails) {
                    if (fieldDataMap.containsKey(resultFail.getFieldCode())) {
                        AssetImportDto.FieldData fieldData = fieldDataMap.get(resultFail.getFieldCode());
                        fieldData.getErrMsg().add(resultFail.getMsg());
                    }
                }
            }
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, result.getMessage());
        }
        AsFormConfig formConfig = getFormConfig(bizType);
        Map<String, String> uniqueMap = uniqueValidator(formData, baseFieldList, formConfig, false);
        uniqueMap.forEach((formCode, msg) -> {
            if (fieldDataMap.containsKey(formCode)) {
                AssetImportDto.FieldData fieldData = fieldDataMap.get(formCode);
                fieldData.getErrMsg().add(msg);
            }
        });
    }

    @Override
    public void importProductValidator(JSONObject formData, FormVO formVO, Long companyId, Map<String, AssetImportDto.FieldData> fieldDataMap) {
        FormMultiValidatorCmd validatorCmd = new FormMultiValidatorCmd(ListUtil.of(formData), formVO.getFormId(), Convert.toStr(companyId));
        Result<List<ValidResultVO>> listResult = edFormClient.formMultiValidator(validatorCmd);
        if (HttpCode.SC_OK == listResult.getCode()) {
            List<ValidResultVO> data = listResult.getData();
            for (ValidResultVO datum : data) {
                if (BooleanUtil.isFalse(datum.getPassable())) {
                    List<ResultFailVO> resultFails = datum.getResultFails();
                    for (ResultFailVO resultFail : resultFails) {
                        if (fieldDataMap.containsKey(resultFail.getFieldCode())) {
                            AssetImportDto.FieldData fieldData = fieldDataMap.get(resultFail.getFieldCode());
                            fieldData.getErrMsg().add(resultFail.getMsg());
                        }
                    }
                }
            }
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, listResult.getMessage());
        }
        AsFormConfig formConfig = getFormConfig(AsFormService.BIZ_TYPE_STANDARD);
        Map<String, String> uniqueMap = uniqueValidator(formData, formVO.getFormFields(), formConfig, false);
        uniqueMap.forEach((formCode, msg) -> {
            if (fieldDataMap.containsKey(formCode)) {
                AssetImportDto.FieldData fieldData = fieldDataMap.get(formCode);
                fieldData.getErrMsg().add(msg);
            }
        });
    }

    @Override
    public void importEditValidator(JSONObject formData,
                                    List<FormFieldCO> baseFieldList,
                                    Map<String, AssetImportDto.FieldData> fieldDataMap,
                                    Long formId, Long bizId, String bizType) {
        FormValidatorCmd formValidatorCmd = new FormValidatorCmd(formData, formId,
                Convert.toStr(LoginUserThreadLocal.getCompanyId()));
        Result<ValidResultVO> result = edFormClient.fieldValidator(formValidatorCmd);
        if (HttpCode.SC_OK == result.getCode()) {
            ValidResultVO data = result.getData();
            if (BooleanUtil.isFalse(data.getPassable())) {
                List<ResultFailVO> resultFails = data.getResultFails();
                for (ResultFailVO resultFail : resultFails) {
                    if (fieldDataMap.containsKey(resultFail.getFieldCode())) {
                        AssetImportDto.FieldData fieldData = fieldDataMap.get(resultFail.getFieldCode());
                        fieldData.getErrMsg().add(resultFail.getMsg());
                    }
                }
            }
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, result.getMessage());
        }
        AsFormConfig formConfig = getFormConfig(bizType);
        List<FormFieldCO> collect = baseFieldList.stream().filter(f -> !f.getFieldType().equals(FormFieldCO.YZC_ASSET_SERIALNO))
                .collect(Collectors.toList());
        Map<String, String> uniqueMap = uniqueValidator(formData, collect, formConfig, bizId, false);
        uniqueMap.forEach((formCode, msg) -> {
            if (fieldDataMap.containsKey(formCode)) {
                AssetImportDto.FieldData fieldData = fieldDataMap.get(formCode);
                fieldData.getErrMsg().add(msg);
            }
        });
    }

    @Override
    public Boolean addForm(SdkFormAddCmd sdkFormAddCmd) {
        Result<Boolean> booleanResult = edFormClient.addForm(sdkFormAddCmd);
        if (HttpCode.SC_OK == booleanResult.getCode()) {
            return booleanResult.getData();
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, booleanResult.getMessage());
        }
    }

    @Override
    public Boolean editForm(SdkFormEditCmd sdkFormEditCmd) {
        Result<Boolean> booleanResult = edFormClient.editForm(sdkFormEditCmd);
        if (HttpCode.SC_OK == booleanResult.getCode()) {
            return booleanResult.getData();
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, booleanResult.getMessage());
        }
    }

    @Override
    public Boolean removeForm(SdkFormDeleteCmd sdkFormDeleteCmd) {
        Result<Boolean> booleanResult = edFormClient.deleteForm(sdkFormDeleteCmd);
        if (HttpCode.SC_OK == booleanResult.getCode()) {
            return booleanResult.getData();
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, booleanResult.getMessage());
        }
    }

    @Override
    public Long copyForm(Long formId, Long fromCompanyId, Long toCompanyId) {
        SdkFormCopyCmd sdkFormCopyCmd = new SdkFormCopyCmd(formId,
                Convert.toStr(fromCompanyId), Convert.toStr(toCompanyId));
        Result<Long> booleanResult = edFormClient.copy(sdkFormCopyCmd);
        if (HttpCode.SC_OK == booleanResult.getCode()) {
            return booleanResult.getData();
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, booleanResult.getMessage());
        }
    }

    @Override
    public FormBaseFieldCO getBaseFieldByType(String type) {
        Result<FormBaseFieldCO> baseFieldByType = edFormClient.getBaseFieldByType(type);
        if (HttpCode.SC_OK == baseFieldByType.getCode()) {
            return baseFieldByType.getData();
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, baseFieldByType.getMessage());
        }
    }

    @Override
    public <T> T getFormFieldPropertyValue(String formType, String fieldType, String fieldCode, String propertyName, Class<T> propertyClass) {
        FormVO tpl = getTplByType(formType);
        return tpl.getFormFields().stream()
                .filter(v -> fieldType.equals(v.getFieldType()) && fieldCode.equals(v.getFieldCode()))
                .findFirst()
                .orElseThrow(() -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单字段" + fieldCode + "不存在"))
                .getFieldProps()
                .getObject(propertyName, propertyClass);
    }

    @Override
    public void validatorForm(FormValidatorCmd cmd) {
        Result<ValidResultVO> result = edFormClient.validatorForm(cmd);
        if (HttpCode.SC_OK == result.getCode()) {
            ValidResultVO data = result.getData();
            if (BooleanUtil.isFalse(data.getPassable())) {
                data.getResultFails().stream().findFirst().ifPresent(f -> {
                    throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, f.getMsg());
                });
            }
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, result.getMessage());
        }
    }

    @Override
    public List<FormRelationDto> relationFill(FormRelationQry qry) {
        List<FormRelationDto> result = new ArrayList<>();
        // 必须要有输入内容，输入类型，关联字段和操作符
        if (StrUtil.isAllNotEmpty(
                qry.getInputData(),
                qry.getInputType(),
                qry.getRelationField(),
                qry.getOperator())) {
            SQLFilter.sqlInject(qry.getRelationField());

            // 查询结果集
            Map<String, String> fillData = new HashMap<>();

            // 1.判断操作符
            String operator;
            switch (qry.getOperator()) {
                case "eq":
                    operator = "=";
                    break;
                case "ne":
                    operator = "!=";
                    break;
                default:
                    return ListUtil.empty();
            }

            // 2.判断关联字段的类型
            String relationFieldType = "";
            Result<Map<String, List<FormRelationFieldCO>>> relationFieldResult = edFormClient.relationField();
            if (HttpCode.SC_OK == relationFieldResult.getCode()) {
                Map<String, List<FormRelationFieldCO>> relationField = relationFieldResult.getData();
                // 当前输入的类型全部表单属性
                List<FormRelationFieldCO> formRelationField = relationField.get(qry.getRelationForm());
                if (CollUtil.isNotEmpty(formRelationField)) {
                    Optional<FormRelationFieldCO> first = formRelationField.stream().filter(f -> f.getFieldCode()
                            .equals(qry.getRelationField())).findFirst();
                    if (first.isPresent()) {
                        relationFieldType = first.get().getFieldType();
                    }
                }
            }

            // 组装SQL，先组装input文本
            List<String> querySql = new ArrayList<>();
            querySql.add(qry.getRelationField() + (BIZ_TYPE_LIST.contains(relationFieldType) ? "Text" : ""));
            querySql.add(operator);
            querySql.add("'" + qry.getInputData() + "'");

            // 组装SQL，再组装inputId类型
            if (StrUtil.isNotEmpty(qry.getInputDataValue())) {
                if (BIZ_TYPE_LIST.contains(relationFieldType) && relationFieldType.equals(qry.getInputType())) {
                    querySql.add("and");
                    querySql.add(qry.getRelationField());
                    querySql.add(operator);
                    querySql.add("'" + qry.getInputDataValue() + "'");
                } else {
                    querySql.add("and");
                    querySql.add("id");
                    querySql.add(operator);
                    querySql.add("'" + qry.getInputDataValue() + "'");
                }
            }
            String querySqlText = String.join(" ", querySql);
            log.info("querySqlText -> {}", querySqlText);
            switch (qry.getRelationForm()) {
                case FormFieldCO.YZC_ORG:
                    fillData = formRelationMapper.fillOrg(querySqlText, LoginUserThreadLocal.getCompanyId());
                    break;
                case FormFieldCO.YZC_AREA:
                    fillData = formRelationMapper.fillArea(querySqlText, LoginUserThreadLocal.getCompanyId());
                    break;
                case FormFieldCO.YZC_REPOSITORY:
                    fillData = formRelationMapper.fillRepo(querySqlText, LoginUserThreadLocal.getCompanyId());
                    break;
                case FormFieldCO.YZC_ASSET_CATE:
                    fillData = formRelationMapper.fillCate(querySqlText, LoginUserThreadLocal.getCompanyId());
                    break;
                case FormFieldCO.YZC_MATERIAL_CATE:
                    fillData = formRelationMapper.fillMCate(querySqlText, LoginUserThreadLocal.getCompanyId());
                    break;
            }
            // 按填充规则返回列表
            if (CollUtil.isNotEmpty(fillData)) {
                for (FormRelationQry.FormRelationFill fillRule : qry.getRelationFills()) {
                    if (StrUtil.isAllNotEmpty(fillRule.getSourceCode(), fillRule.getTargetCode())) {
                        String targetValue = Convert.toStr(fillData.get(fillRule.getSourceCode()), StrUtil.EMPTY);
                        String targetValueName = Convert.toStr(fillData.get(fillRule.getSourceCode() + "Text"), StrUtil.EMPTY);
                        // 过滤没有结果的
                        if (StrUtil.isNotEmpty(targetValue)) {
                            // 结果集
                            FormRelationDto relationDto = new FormRelationDto();
                            relationDto.setTargetCode(fillRule.getTargetCode());
                            relationDto.setTargetValue(targetValue);
                            relationDto.setTargetValueName(targetValueName);
                            result.add(relationDto);
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 通用校验
     *
     * @param formData
     * @param formId
     */
    private void genericValidator(List<JSONObject> formData, Long formId, Long companyId) {
        FormMultiValidatorCmd validatorCmd = new FormMultiValidatorCmd(formData, formId,
                Convert.toStr(companyId));
        Result<List<ValidResultVO>> listResult = edFormClient.formMultiValidator(validatorCmd);
        if (HttpCode.SC_OK == listResult.getCode()) {
            List<ValidResultVO> data = listResult.getData();
            for (ValidResultVO datum : data) {
                if (BooleanUtil.isFalse(datum.getPassable())) {
                    datum.getResultFails().stream().findFirst().ifPresent(f -> {
                        throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, f.getMsg());
                    });
                }
            }
        } else {
            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, listResult.getMessage());
        }
    }

    private Map<String, String> uniqueValidator(JSONObject formData, List<FormFieldCO> formFields, AsFormConfig formConfig, boolean throwEx) {
        return uniqueValidator(formData, formFields, formConfig, null, throwEx);
    }

    /**
     * 唯一性校验
     *
     * @param formData
     * @param formFields
     * @param formConfig
     */
    private Map<String, String> uniqueValidator(JSONObject formData, List<FormFieldCO> formFields, AsFormConfig formConfig, Long id, boolean throwEx) {
        Map<String, String> result = new HashMap<>();
        for (FormFieldCO formField : formFields) {
            JSONObject fieldProps = formField.getFieldProps();
            // 属性值得unique为true，或者流水号类型
            if ((fieldProps.containsKey("unique") && BooleanUtil.isTrue(fieldProps.getBoolean("unique")))
                    || ListUtil.of(FormBaseFieldCO.YZC_SERIALNO,
                            FormBaseFieldCO.YZC_ASSET_SERIALNO,
                            FormBaseFieldCO.YZC_MATERIAL_SERIALNO)
                    .contains(formField.getFieldType())) {
                String value = Convert.toStr(formData.get(formField.getFieldCode()));
                if (StrUtil.isNotBlank(value)
                        && StrUtil.isNotBlank(formConfig.getTableName())
                        && StrUtil.isNotBlank(formConfig.getTableColumn())) {
                    // 校验唯一性 --> 也不能与回收站中的数据重复
                    Boolean bool = formConfigService.checkUnique(
                            formConfig.getTableName(),
                            formConfig.getTableColumn(),
                            formField.getFieldCode(),
                            value, id);
                    if (BooleanUtil.isTrue(bool)) {
                        if (throwEx) {
                            throw new BusinessException(MeansResultCode.DYNAMIC_FORM_UNIQUE_ERROR, formField.getFieldName() + "值" + value + "重复");
                        } else {
                            result.put(formField.getFieldCode(), formField.getFieldName() + "值" + value + "重复");
                        }
                    }
                }
            }
        }
        return result;
    }

    private void uniqueValidator(ValidatorModel model, List<FormFieldCO> formFields, AsFormConfig formConfig) {
        for (FormFieldCO formField : formFields) {
            JSONObject fieldProps = formField.getFieldProps();

            // 属性值得unique为true，或者流水号类型
            if ((fieldProps.containsKey("unique") && BooleanUtil.isTrue(fieldProps.getBoolean("unique")))
                    || ListUtil.of(FormBaseFieldCO.YZC_SERIALNO,
                            FormBaseFieldCO.YZC_ASSET_SERIALNO,
                            FormBaseFieldCO.YZC_MATERIAL_SERIALNO)
                    .contains(formField.getFieldType())) {
                String value = Convert.toStr(model.getFormData().get(formField.getFieldCode()));
                if (StrUtil.isNotBlank(value)
                        && StrUtil.isNotBlank(formConfig.getTableName())
                        && StrUtil.isNotBlank(formConfig.getTableColumn())) {
                    // 校验唯一性
                    Boolean bool = formConfigService.checkUnique(
                            formConfig.getTableName(),
                            formConfig.getTableColumn(),
                            formField.getFieldCode(),
                            value, model.getId());
                    if (BooleanUtil.isTrue(bool)) {
                        throw new BusinessException(MeansResultCode.DYNAMIC_FORM_UNIQUE_ERROR, formField.getFieldName() + "值" + value + "重复");
                    }
                }
            }
        }
    }

    private AsFormConfig getFormConfig(String bizType) {
        AsFormConfig formConfig = null;
        if (redisService.hasKey(getRedisFormConfig(bizType))) {
            Object obj = redisService.get(getRedisFormConfig(bizType));
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(obj));
            jsonObject.remove("@type");
            formConfig = jsonObject.toJavaObject(AsFormConfig.class);
        }
        if (ObjectUtil.isNull(formConfig)) {
            formConfig = formConfigService.getOne(Wrappers.lambdaQuery(AsFormConfig.class)
                    .eq(AsFormConfig::getBizType, bizType));
            if (ObjectUtil.isNull(formConfig)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单[" + bizType + "]配置信息不存在");
            } else {
                redisService.set(getRedisFormConfig(bizType), formConfig);
            }
        }
        return formConfig;
    }

    @Override
    public FormVO getTpl(String bizType, Long bizTenantId) {
        return getTpl(bizType, ListUtil.of(Convert.toStr(bizTenantId)));
    }

    @Override
    public FormVO getTpl(String bizType, List<String> bizTenantIds) {
        if (OrderFormTypeEnum.EQUIPMENT_MAINTAIN_PLAN.getBizType().equalsIgnoreCase(bizType)) {
            return ENT_MAT_PLAN_FORM;
        }
        if (OrderFormTypeEnum.EQUIPMENT_SITE_INSPECT_PLAN.getBizType().equalsIgnoreCase(bizType)) {
            return ENT_SNT_PLAN_FORM;
        }
        AsFormConfig formConfig = getFormConfig(bizType);
        SdkFormPageGetQry sdkFormPageGetQry = new SdkFormPageGetQry();
        sdkFormPageGetQry.setBizTenantIds(bizTenantIds);
        sdkFormPageGetQry.setBizCode(formConfig.getCode());
        Result<PageResult<FormSdkVO>> pageResult = edFormClient.pageSearch(sdkFormPageGetQry);
        if (pageResult.getCode() == HttpCode.SC_OK) {
            PageResult<FormSdkVO> data = pageResult.getData();
            List<FormSdkVO> formList = data.getList();
            if (CollUtil.isNotEmpty(formList)) {
                FormSdkVO formCO = formList.get(0);
                FormByIdQry formByIdQry = new FormByIdQry(formCO.getId(), formCO.getBizTenantId());
                Result<FormVO> formVOResult = edFormClient.formById(formByIdQry.toMap());
                if (formVOResult.getCode() == HttpCode.SC_OK) {
                    return formVOResult.getData();
                } else {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误，" + formVOResult.getMessage());
                }
            } else {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "找不到" + bizType + "类型的表单");
            }
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误，" + pageResult.getMessage());
        }
    }
}
