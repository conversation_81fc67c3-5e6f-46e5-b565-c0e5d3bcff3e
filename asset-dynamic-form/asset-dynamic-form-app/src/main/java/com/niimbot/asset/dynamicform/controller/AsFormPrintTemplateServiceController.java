package com.niimbot.asset.dynamicform.controller;

import com.niimbot.asset.dynamicform.model.AsFormPrintTemplate;
import com.niimbot.asset.dynamicform.service.AsFormPrintTemplateService;
import com.niimbot.dynamicform.FormPrintTemplateDto;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/3 18:00
 */
@RestController
@RequestMapping("/server/dynamicForm/printTemplate")
@RequiredArgsConstructor
public class AsFormPrintTemplateServiceController {

    private final AsFormPrintTemplateService formPrintTemplateService;

    @GetMapping("/{orderType}")
    public List<AsFormPrintTemplate> printTplList(@PathVariable("orderType") Integer orderType) {
        return formPrintTemplateService.printTplList(orderType);
    }

    @GetMapping("/detail/{tplId}")
    public AsFormPrintTemplate getPrintTpl(@PathVariable("tplId") Long tplId) {
        return formPrintTemplateService.getPrintTpl(tplId);
    }

    @PostMapping
    public FormPrintTemplateDto add(@RequestBody AsFormPrintTemplate formPrintTemplate) {
        return formPrintTemplateService.add(formPrintTemplate);
    }

    @PutMapping
    public FormPrintTemplateDto edit(@RequestBody AsFormPrintTemplate formPrintTemplate) {
        return formPrintTemplateService.edit(formPrintTemplate);
    }

    @DeleteMapping("/{tplId}")
    public FormPrintTemplateDto delete(@PathVariable("tplId") Long tplId) {
        return formPrintTemplateService.delete(tplId);
    }

    @PostMapping("/copy/{tplId}")
    public FormPrintTemplateDto copy(@PathVariable("tplId") Long tplId) {
        return formPrintTemplateService.copy(tplId);
    }

}
