package com.niimbot.asset.dynamicform.controller;

import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.dynamicform.FormRelationDto;
import com.niimbot.dynamicform.FormRelationQry;
import com.niimbot.easydesign.core.dto.page.PageResult;
import com.niimbot.easydesign.core.exception.api.HttpCode;
import com.niimbot.easydesign.core.exception.api.Result;
import com.niimbot.easydesign.form.dto.FormTplAddCmd;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.sdk.FieldValidatorCmd;
import com.niimbot.easydesign.form.dto.sdk.FormSdkVO;
import com.niimbot.easydesign.form.dto.sdk.FormValidatorCmd;
import com.niimbot.easydesign.form.dto.sdk.SdkFormEditCmd;
import com.niimbot.easydesign.form.dto.sdk.SdkFormPageGetQry;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.easydesign.sdk.client.EdFormClient;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/form")
@RequiredArgsConstructor
public class AsFormServiceController {

    private final AsFormService formService;

    private final EdFormClient edFormClient;

    @GetMapping("/getByType/{type}")
    public FormVO getTplByType(@PathVariable String type) {
        return formService.getTplByType(type);
    }

    @GetMapping("/getByFormId")
    public FormVO getByFormId(FormByIdQry qry) {
        return formService.getByFormId(qry.getFormId(), qry.getCompanyIds(), false);
    }

    @GetMapping("/listByBizId/{bizId}")
    public List<FormSdkVO> listByBizId(@PathVariable String bizId) {
        SdkFormPageGetQry sdkFormPageGetQry = new SdkFormPageGetQry();
        sdkFormPageGetQry.setBizTenantIds(ListUtil.of(Convert.toStr(LoginUserThreadLocal.getCompanyId())));
        sdkFormPageGetQry.setBizId(bizId);
        sdkFormPageGetQry.setPageIndex(1);
        sdkFormPageGetQry.setPageSize(Integer.MAX_VALUE);
        Result<PageResult<FormSdkVO>> pageResult = edFormClient.pageSearch(sdkFormPageGetQry);
        if (pageResult.getCode() == HttpCode.SC_OK) {
            return pageResult.getData().getList();
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误，" + pageResult.getMessage());
        }
    }

    @PostMapping("/saveForm")
    public Boolean saveForm(@RequestBody FormTplAddCmd cmd) {
        SdkFormEditCmd sdkFormEditCmd = new SdkFormEditCmd(
                cmd.getFormId(),
                cmd.getFormName(),
                Convert.toStr(LoginUserThreadLocal.getCompanyId()),
                cmd.getFormFields(),
                cmd.getFormProps()
        );
        return formService.editForm(sdkFormEditCmd);
    }

    @GetMapping("/getBaseFieldByType/{type}")
    public FormBaseFieldCO getBaseFieldByType(@PathVariable("type") String type) {
        return formService.getBaseFieldByType(type);
    }

    @PostMapping("/validatorForm")
    public void validatorForm(@RequestBody FormValidatorCmd cmd) {
        formService.validatorForm(cmd);
    }

    @PostMapping("/fieldValidator")
    public void fieldValidator(@RequestBody FieldValidatorCmd cmd) {
        formService.fieldValidator(cmd.getFormData(), cmd.getFormFieldList());

    }

    @PostMapping("/relationFill")
    List<FormRelationDto> relationFill(@RequestBody FormRelationQry qry) {
        return formService.relationFill(qry);
    }

}
