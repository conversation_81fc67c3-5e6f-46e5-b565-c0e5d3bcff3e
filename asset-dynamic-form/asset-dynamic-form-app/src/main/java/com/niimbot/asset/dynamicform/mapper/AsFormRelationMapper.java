package com.niimbot.asset.dynamicform.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Map;

public interface AsFormRelationMapper {
    Map<String, String> fillOrg(@Param("querySqlText") String querySqlText,
                                @Param("companyId") Long companyId);

    Map<String, String> fillArea(@Param("querySqlText") String querySqlText,
                                 @Param("companyId") Long companyId);

    Map<String, String> fillRepo(@Param("querySqlText") String querySqlText,
                                 @Param("companyId") Long companyId);

    Map<String, String> fillCate(@Param("querySqlText") String querySqlText,
                                 @Param("companyId") Long companyId);

    Map<String, String> fillMCate(@Param("querySqlText") String querySqlText,
                                  @Param("companyId") Long companyId);
}
