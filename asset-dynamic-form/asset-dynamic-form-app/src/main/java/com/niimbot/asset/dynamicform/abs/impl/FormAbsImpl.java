package com.niimbot.asset.dynamicform.abs.impl;

import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.system.abs.FormAbs;
import com.niimbot.asset.system.dto.BizFormAssetInitCmd;
import com.niimbot.asset.system.dto.BizFormMaterialInitCmd;
import com.niimbot.asset.system.dto.FormGetQry;
import com.niimbot.asset.system.dto.FormInitCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.ListUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/12 20:01
 */
@RestController
@RequestMapping("/client/abs/dynamicform/formAbs/")
@RequiredArgsConstructor
public class FormAbsImpl implements FormAbs {

    private final AsFormService formService;

    @Override
    public FormVO getTplByType(String type) {
        return formService.getTplByType(type);
    }

    @Override
    public FormVO getFormById(FormGetQry qry) {
        return formService.getByFormId(qry.getFormId(), qry.getCompanyIds(), qry.getThrowException());
    }

    @Override
    public void initCompanyForm(FormInitCmd cmd) {
        // 单据表单
        for (OrderFormTypeEnum typeEnum : OrderFormTypeEnum.values()) {
            if (typeEnum.getCanInit()) {
                FormVO assetForm = formService.getTpl(typeEnum.getBizType(), ListUtil.of("0"));
                formService.copyForm(assetForm.getFormId(), 0L, cmd.getCompanyId());
            }
        }
    }

    @Override
    public void initAssetItems(BizFormAssetInitCmd cmd) {
        FormVO assetForm = formService.getTpl(AsFormService.BIZ_TYPE_ASSET, ListUtil.of("0"));
        formService.copyForm(assetForm.getFormId(), 0L, cmd.getCompanyId());
    }

    @Override
    public void initMaterialItems(BizFormMaterialInitCmd cmd) {
        FormVO materialForm = formService.getTpl(AsFormService.BIZ_TYPE_MATERIAL, ListUtil.of("0"));
        formService.copyForm(materialForm.getFormId(), 0L, cmd.getCompanyId());
    }

}
