package com.niimbot.asset.dynamicform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.dynamicform.model.AsFormConfig;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2022/7/5 15:05
 */
public interface AsFormConfigMapper extends BaseMapper<AsFormConfig> {

    int checkUnique(@Param("tableName") String tableName,
                    @Param("field") String field,
                    @Param("value") String value,
                    @Param("companyId") Long companyId,
                    @Param("id") Long id);

    int checkUniqueWithRecycle(@Param("tableName") String tableName,
                    @Param("field") String field,
                    @Param("value") String value,
                    @Param("companyId") Long companyId,
                    @Param("id") Long id);

}
