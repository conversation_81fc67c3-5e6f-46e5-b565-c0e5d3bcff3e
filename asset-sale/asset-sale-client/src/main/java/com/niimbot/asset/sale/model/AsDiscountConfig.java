package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.List;

import com.niimbot.asset.sale.handle.DiscountConditionTypeHandler;
import com.niimbot.asset.sale.handle.DiscountObjectTypeHandler;
import com.niimbot.sale.DiscountConditionItem;
import com.niimbot.sale.DiscountObjectDto;
import lombok.Data;

/**
 * as_discount_config
 *
 * <AUTHOR>
@Data
@TableName(value = "as_discount_config", autoResultMap = true)
public class AsDiscountConfig {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 优惠策略编码
     */
    private String bizCode;

    /**
     * 优惠策略名称
     */
    private String discountName;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;

    /**
     * 优惠条件
     */
    @TableField(typeHandler = DiscountConditionTypeHandler.class)
    private List<DiscountConditionItem> bonusCondition;

    /**
     * 优惠对象类型
     */
    private Integer objectType;

    /**
     * 优惠对象
     */
    @TableField(typeHandler = DiscountObjectTypeHandler.class)
    private DiscountObjectDto bonusObject;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    private Boolean isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}