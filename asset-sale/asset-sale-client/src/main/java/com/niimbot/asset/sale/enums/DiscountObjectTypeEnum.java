package com.niimbot.asset.sale.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/8 上午9:26
 */
@Getter
public enum DiscountObjectTypeEnum {

    ALL(1, "不限"),
    PAYMENT_STATUS(2, "收费状态"),
    ENTERPRISE_SIZE(3, "企业规模"),
    COMPANY_TAG(4, "企业标签"),
    SPECIFIC_CUSTOMER(5, "指定客户"),

    UN_KNOW(-1, "未知"),
    ;

    DiscountObjectTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public static DiscountObjectTypeEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (DiscountObjectTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
