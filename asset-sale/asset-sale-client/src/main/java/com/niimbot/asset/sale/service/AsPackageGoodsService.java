package com.niimbot.asset.sale.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsPackageGoods;
import com.niimbot.sale.PackageGoodsPageDto;

import java.util.List;

/**
 * <p>
 * 商品套餐详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
public interface AsPackageGoodsService extends IService<AsPackageGoods> {


    List<AsPackageGoods> listPackageGoods();

    PackageGoodsPageDto detail(Long id);

}
