package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * as_resource_config
 *
 * <AUTHOR>
@Data
@TableName(value = "as_resource_config", autoResultMap = true)
public class AsResourceConfig {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 资源包编码
     */
    private String bizCode;

    /**
     * sku码
     */
    private String skuCode;

    /**
     * 资源包名称
     */
    private String resourceName;

    /**
     * 资源容量
     */
    private Integer capacity;

    /**
     * 资源包单价
     */
    private BigDecimal unitPrice;

    /**
     * 折算年价
     */
    private BigDecimal annualPrice;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    private Boolean isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否前端用户展示：1：展示 0：不展示
     */
    private Integer frontDisplay;
}