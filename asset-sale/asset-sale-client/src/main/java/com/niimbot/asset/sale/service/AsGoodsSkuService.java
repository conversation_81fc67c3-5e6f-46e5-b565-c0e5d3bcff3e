package com.niimbot.asset.sale.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsGoodsSku;
import com.niimbot.sale.GoodsInfoDto;

import java.util.List;

/**
 * <p>
 * 商品SKU 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
public interface AsGoodsSkuService extends IService<AsGoodsSku> {

    /**
     * 查询sku
     *
     * @return 商品信息
     */
    GoodsInfoDto getSkuOne(Integer type);

    List<GoodsInfoDto> getSkuList(Integer type);
}
