package com.niimbot.asset.sale.handle;

import com.niimbot.asset.framework.handle.AbstractJsonTypeHandler;
import com.niimbot.sale.DiscountConditionItem;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6 上午10:48
 */
@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.OTHER)
public class DiscountConditionTypeHandler extends AbstractJsonTypeHandler<List<DiscountConditionItem>> {
}
