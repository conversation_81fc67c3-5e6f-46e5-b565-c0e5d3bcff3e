package com.niimbot.asset.sale.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 发票信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInvoiceInfo对象", description="发票信息")
public class AsInvoiceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "发票信息id")
    private Long id;

    @ApiModelProperty(value = "发票类型：1-个人；2-普通；3：增值税")
    private Integer type;

    @ApiModelProperty(value = "发票抬头")
    private String title;

    @ApiModelProperty(value = "税号")
    private String taxNum;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "开户行")
    private String bank;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    /*@ApiModelProperty(value = "收件人名称")
    private String receiveName;

    @ApiModelProperty(value = "收件电话")
    private String receivePhone;

    @ApiModelProperty(value = "收件地址")
    private String receiveAddress;*/
}
