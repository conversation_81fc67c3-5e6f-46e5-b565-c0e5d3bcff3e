package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/7 15:23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AsCompanyResource对象", description = "企业资源包")
@TableName(value = "as_company_resource", autoResultMap = true)
public class AsCompanyResource {

    @ApiModelProperty(value = "企业资源ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "SKU编码")
    private String skuCode;

    @ApiModelProperty(value = "资源包名称")
    private String resourceName;

    @ApiModelProperty(value = "资源容量")
    private Integer capacity;

    @ApiModelProperty(value = "关联销售单id")
    private Long saleOrderId;

    @ApiModelProperty(value = "是否体验资源")
    private Boolean experience;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expirationTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveTime;
}
