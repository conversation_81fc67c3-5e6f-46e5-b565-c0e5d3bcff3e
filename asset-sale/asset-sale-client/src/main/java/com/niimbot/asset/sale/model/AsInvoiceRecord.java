package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 发票记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AsInvoiceRecord对象", description="发票记录")
@TableName(value = "as_invoice_record", autoResultMap = true)
public class AsInvoiceRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "开票状态：1-申请中，2-开票失败，3-已完成")
    private Integer status;

    @ApiModelProperty(value = "销售单")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<Long> saleOrderIds;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceMoney;

    @ApiModelProperty(value = "发票类型(虚拟列)：1-个人；2-普通；3：增值税")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer invoiceType;

    @ApiModelProperty(value = "发票抬头(虚拟列)")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String invoiceTitle;

    @ApiModelProperty(value = "发票抬头信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private AsInvoiceInfo invoiceInfo;

    @ApiModelProperty(value = "收件地址信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private AsAddressInfo addressInfo;

    @ApiModelProperty(value = "发票项目")
    private String invoiceProject;

    @ApiModelProperty(value = "发票介质: 1-纸质发票")
    private Integer invoiceMedia;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;

    @ApiModelProperty(value = "物流单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司")
    private Integer expressCompany;

    @ApiModelProperty(value = "发票图片地址")
    private String picture;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
