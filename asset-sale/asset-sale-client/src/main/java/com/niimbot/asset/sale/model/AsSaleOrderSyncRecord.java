package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsSaleOrderSyncRecord", description = "销售单同步记录表")
@TableName(value = "as_sale_order_sync_record", autoResultMap = true)
public class AsSaleOrderSyncRecord implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("销售单ID")
    private Long saleOrderId;

    @ApiModelProperty("同步类型")
    private Integer syncType;

    @ApiModelProperty("同步结果")
    private String syncResult;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
