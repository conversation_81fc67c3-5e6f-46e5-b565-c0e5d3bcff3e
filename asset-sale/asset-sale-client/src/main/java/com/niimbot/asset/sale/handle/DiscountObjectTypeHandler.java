package com.niimbot.asset.sale.handle;

import com.niimbot.asset.framework.handle.AbstractJsonTypeHandler;
import com.niimbot.sale.DiscountObjectDto;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6 上午11:39
 */
@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.OTHER)
public class DiscountObjectTypeHandler extends AbstractJsonTypeHandler<DiscountObjectDto> {
}
