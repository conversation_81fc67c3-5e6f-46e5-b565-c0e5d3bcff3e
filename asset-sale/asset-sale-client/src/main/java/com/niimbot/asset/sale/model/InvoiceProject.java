package com.niimbot.asset.sale.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="InvoiceProject对象", description="开票项目")
public class InvoiceProject implements Serializable {
    private static final long serialVersionUID = 6028187163735651701L;

    @ApiModelProperty(value = "名称")
    private String name;
}
