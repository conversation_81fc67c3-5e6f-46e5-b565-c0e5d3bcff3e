package com.niimbot.asset.sale.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/18 14:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsAddressInfo对象", description = "邮寄地址信息")
public class AsAddressInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收件地址id")
    private Long id;

    @ApiModelProperty(value = "收件人名称")
    private String name;

    @ApiModelProperty(value = "收件电话")
    private String phone;

    @ApiModelProperty(value = "收件地址")
    private String address;

    @ApiModelProperty("邮件地址")
    private String email;
}
