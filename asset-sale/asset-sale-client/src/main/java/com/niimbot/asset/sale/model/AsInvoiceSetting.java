package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.sale.handle.InvoiceProjectListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 开票设置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AsInvoiceSetting对象", description="开票设置")
@TableName(value = "as_invoice_setting", autoResultMap = true)
public class AsInvoiceSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final Long ID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "增值税发表最低金额")
    private BigDecimal vatInvoiceMinMoney;

    @ApiModelProperty(value = "开票项目")
    @TableField(typeHandler = InvoiceProjectListTypeHandler.class)
    private List<InvoiceProject> invoiceProjects;


}
