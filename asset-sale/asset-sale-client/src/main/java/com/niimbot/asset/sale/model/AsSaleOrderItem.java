package com.niimbot.asset.sale.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsSaleOrderDetail对象", description="销售单明细")
@TableName(value = "as_sale_order_item", autoResultMap = true)
public class AsSaleOrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "明细id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "销售单id")
    private Long saleOrderId;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品规格")
    private String model;

    @ApiModelProperty(value = "sku编码")
    private String sku;

    @ApiModelProperty(value = "商品类型: 1-服务版本, 2-资产加油包")
    private Integer type;

    @ApiModelProperty(value = "商品信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject product;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ApiModelProperty(value = "原始单价")
    private BigDecimal originalMoney;

    @ApiModelProperty(value = "优惠信息")
    private String discountInfo;

    @ApiModelProperty(value = "销售单价")
    private BigDecimal saleMoney;

    @ApiModelProperty(value = "原始总价")
    private BigDecimal totalMoney;

}
