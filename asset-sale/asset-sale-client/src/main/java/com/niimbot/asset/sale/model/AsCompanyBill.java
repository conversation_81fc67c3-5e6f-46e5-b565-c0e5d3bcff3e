package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业账单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AsCompanyBill对象", description = "企业账单")
@TableName(value = "as_company_bill", autoResultMap = true)
public class AsCompanyBill implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "统计周期开始日期")
    private LocalDate statisticalStartTime;

    @ApiModelProperty(value = "统计周期结束日期")
    private LocalDate statisticalEndTime;

    @ApiModelProperty(value = "期初额")
    private BigDecimal initialAmount;

    @ApiModelProperty(value = "收入")
    private BigDecimal incomeAmount;

    @ApiModelProperty(value = "支出")
    private BigDecimal expensesAmount;

    @ApiModelProperty(value = "结余")
    private BigDecimal balanceAmount;

    @ApiModelProperty(value = "实付")
    private BigDecimal actuallyPayAmount;

    @ApiModelProperty(value = "待付")
    private BigDecimal waitPayAmount;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
