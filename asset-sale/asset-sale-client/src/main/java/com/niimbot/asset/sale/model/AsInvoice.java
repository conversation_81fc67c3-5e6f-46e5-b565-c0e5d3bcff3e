package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.system.enums.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 发票信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInvoice对象", description="发票信息")
@TableName(value = "as_invoice", autoResultMap = true)
public class AsInvoice implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    /**
     * @see InvoiceTypeEnum
     */
    @ApiModelProperty(value = "发票类型：1-个人；2-普通；3：增值税")
    private Integer type;

    @ApiModelProperty(value = "发票抬头")
    private String title;

    @ApiModelProperty(value = "税号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String taxNum;

    @ApiModelProperty(value = "地址")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String address;

    @ApiModelProperty(value = "电话")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String phone;

    @ApiModelProperty(value = "开户行")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String bank;

    @ApiModelProperty(value = "银行账号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String bankAccount;

    /*@ApiModelProperty(value = "收件人名称")
    private String receiveName;

    @ApiModelProperty(value = "收件电话")
    private String receivePhone;

    @ApiModelProperty(value = "收件地址")
    private String receiveAddress;*/

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
