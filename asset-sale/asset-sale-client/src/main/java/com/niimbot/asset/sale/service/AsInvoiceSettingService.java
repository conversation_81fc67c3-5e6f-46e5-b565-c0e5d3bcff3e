package com.niimbot.asset.sale.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsInvoiceSetting;
import com.niimbot.sale.InvoiceSettingDto;

/**
 * <p>
 * 开票设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
public interface AsInvoiceSettingService extends IService<AsInvoiceSetting> {
    /**
     * 查询
     *
     * @return
     */
    InvoiceSettingDto queryInfo();
}
