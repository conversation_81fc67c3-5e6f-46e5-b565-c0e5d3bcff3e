package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业收支
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AsCompanyIncomeExpenses对象", description = "企业收支")
@TableName(value = "as_company_income_expenses", autoResultMap = true)
public class AsCompanyIncomeExpenses implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "收支类型：1-收入，2-支出")
    private Integer type;

    @ApiModelProperty(value = "业务类型：1-购入，2-调账（转入），3-资产使用费，4-调账（转出）")
    private Integer bizType;

    @ApiModelProperty(value = "业务描述")
    private String bizDesc;

    @ApiModelProperty(value = "交易时间")
    private LocalDateTime tradeTime;

    @ApiModelProperty(value = "交易金额（收入/支出）")
    private BigDecimal tradeAmount;

    @ApiModelProperty(value = "账户余额")
    private BigDecimal balance;

    @ApiModelProperty(value = "欠费金额")
    private BigDecimal arrears;

    @ApiModelProperty(value = "关联订单id")
    private Long saleOrderId;

    @ApiModelProperty(value = "关联订单编号")
    private String saleOrderNo;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
