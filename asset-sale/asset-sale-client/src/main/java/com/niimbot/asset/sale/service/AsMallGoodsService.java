package com.niimbot.asset.sale.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsMallGoods;
import com.niimbot.sale.MallGoodsDto;
import com.niimbot.sale.MallGoodsPageDto;
import com.niimbot.sale.MallGoodsQueryDto;

import java.util.List;

/**
 * <p>
 * 商品详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
public interface AsMallGoodsService extends IService<AsMallGoods> {

    IPage<MallGoodsPageDto> pageQuery(MallGoodsQueryDto query);

    MallGoodsPageDto detail(Long id);

    /**
     * 根据商品编码查询硬件商品信息
     * @param skuCodeList
     * @return
     */
    List<MallGoodsDto> queryBySkuCode(List<String> skuCodeList);

    /**
     * 查询硬件商品信息
     * @return
     */
    List<MallGoodsDto> queryHardware();

}
