package com.niimbot.asset.sale.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsDiscountConfig;
import com.niimbot.sale.DiscountConfigDto;

import java.util.List;

/**
 * 优惠策略配置
 *
 * <AUTHOR>
 * @date 2022/12/1 下午1:50
 */
public interface DiscountConfigService extends IService<AsDiscountConfig> {

    /**
     * 根据企业查询优惠信息
     * @param discountCodeList
     * @param companyId
     * @return
     */
    List<DiscountConfigDto> queryDiscountInfo(List<String> discountCodeList, Long companyId);

    /**
     * 根据购买数量获取满赠数量
     * @param discountInfo
     * @param quantity
     * @return
     */
    Integer queryBonus(List<DiscountConfigDto> discountInfo, Integer quantity);
}
