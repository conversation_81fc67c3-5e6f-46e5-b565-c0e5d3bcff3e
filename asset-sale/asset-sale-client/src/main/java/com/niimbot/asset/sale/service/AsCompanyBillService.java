package com.niimbot.asset.sale.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsCompanyBill;
import com.niimbot.asset.sale.model.AsCompanyBillDetail;
import com.niimbot.sale.CompanyBillQueryDto;

/**
 * <p>
 * 企业账单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
public interface AsCompanyBillService extends IService<AsCompanyBill> {

    IPage<AsCompanyBill> pageAll(CompanyBillQueryDto query);

    IPage<AsCompanyBillDetail> pageIncome(CompanyBillQueryDto query);

    IPage<AsCompanyBillDetail> pageExpenses(CompanyBillQueryDto query);

}
