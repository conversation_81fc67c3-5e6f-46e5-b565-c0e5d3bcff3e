package com.niimbot.asset.sale.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsResourceConfig;
import com.niimbot.sale.ResourcePackDto;

import java.util.List;

/**
 * 资源包配置
 *
 * <AUTHOR>
 * @date 2022/11/30 下午3:58
 */
public interface ResourceConfigService extends IService<AsResourceConfig> {

    List<ResourcePackDto> listPack();

    ResourcePackDto getPackInfo(Long id);

    ResourcePackDto info(Long id);

    /**
     * 根据资源包skuCode批量查询资源包信息
     * @param skuCodeList
     * @return
     */
    List<ResourcePackDto> queryResourceInfo(List<String> skuCodeList);
}
