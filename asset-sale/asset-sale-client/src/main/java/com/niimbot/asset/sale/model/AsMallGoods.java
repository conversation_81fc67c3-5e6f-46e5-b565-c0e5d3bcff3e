package com.niimbot.asset.sale.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AsMallGoods对象", description="商品详情")
@TableName(value = "as_mall_goods", autoResultMap = true)
public class AsMallGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品副标题")
    private String subtitle;

    @ApiModelProperty(value = "商品图地址")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> goodsUrl;

    @ApiModelProperty(value = "SKU信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> sku;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal goodsUnitPrice;

    @ApiModelProperty(value = "商品规格")
    private String goodsModel;

    @ApiModelProperty(value = "商品计价单位")
    private String goodsChargeUnit;

    @ApiModelProperty(value = "商品特点", required = true)
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> goodsTrait;

    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "是否上架 0-上架  1-下架")
    private Boolean status;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
