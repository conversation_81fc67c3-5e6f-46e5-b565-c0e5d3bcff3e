package com.niimbot.asset.sale.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品套餐详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AsPackageGoods对象", description="商品套餐详情")
@TableName(value = "as_package_goods", autoResultMap = true)
public class AsPackageGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "套餐副标题")
    private String subtitle;

    @ApiModelProperty(value = "套餐图地址")
    private String packageUrl;

    @ApiModelProperty(value = "SKU信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> sku;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "适用企业描述")
    private String applyDescribe;

    @ApiModelProperty(value = "适用员工数")
    private String applyEmployee;

    @ApiModelProperty(value = "适用资产数")
    private String applyAsset;

    @ApiModelProperty(value = "营销文案")
    private String marketingPlan;

    @ApiModelProperty(value = "商品图片")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> goodsUrl;

    @ApiModelProperty(value = "商品主图")
    private String goodsMainUrl;

    @ApiModelProperty(value = "商品特点", required = true)
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> goodsTrait;

    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

    @ApiModelProperty(value = "是否上架 0-上架  1-下架")
    private Boolean status;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
