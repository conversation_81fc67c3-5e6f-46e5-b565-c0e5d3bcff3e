package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <p>
 * 版本销售单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsSaleOrder对象", description="版本销售单")
@TableName(value = "as_sale_order", autoResultMap = true)
public class AsSaleOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售单ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "单据编号：字母前缀ON+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "商品摘要")
    private String summary;

    @ApiModelProperty(value = "订单来源: 1-web, 2-android, 3-iOS")
    private Integer source;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "订单支付金额")
    private BigDecimal payMoney;

    @ApiModelProperty(value = "抵扣金额")
    private BigDecimal discountMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "支付方式: 1-支付宝, 2-微信, 3-对公转账")
    private Integer payType;

    @ApiModelProperty(value = "支付过期时间")
    private LocalDateTime expiredTime;

    @ApiModelProperty(value = "交易单号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String tradeNo;

    @ApiModelProperty(value = "收款账号")
    private String receivePaymentNo;

    @ApiModelProperty(value = "操作时间(支付/关闭)")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "支付状态: 1-待付款, 2-已完成, 3-已关闭")
    private Integer status;

    @ApiModelProperty(value = "开票状态:0-无需开票，1-待开票，2-开票中，3-已开票")
    private Integer invoiceStatus;

    @ApiModelProperty("中台订单同步状态")
    private Integer middlendSyncStatus;

    @ApiModelProperty(value = "用户手机")
    private String mobile;

    @ApiModelProperty(value = "支付地址")
    private String payUrl;

//    @ApiModelProperty(value = "发票信息")
//    @TableField(typeHandler = FastjsonTypeHandler.class)
//    private AsInvoiceInfo invoiceInfo;

    @ApiModelProperty(value = "销售员")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private Seller seller;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "用户备注")
    private String customerRemark;

    @ApiModelProperty(value = "备注")
    private String remark;

    @Version
    private Integer version;
}
