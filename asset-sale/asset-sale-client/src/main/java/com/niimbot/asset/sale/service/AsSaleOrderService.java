package com.niimbot.asset.sale.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.sale.SaleOrderDto;
import com.niimbot.sale.SaleOrderPageQueryDto;
import com.niimbot.sale.SaleOrderPaidDto;
import com.niimbot.sale.SaleOrderPaidMessageDto;
import com.niimbot.sale.SaleOrderPayInfoDto;
import com.niimbot.sale.SaleOrderRepayDto;
import com.niimbot.sale.SaleOrderResourcePackCreateDto;

import java.math.BigDecimal;

/**
 * <p>
 * 版本销售单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
public interface AsSaleOrderService extends IService<AsSaleOrder> {

    /**
     * 通过条件查询销售单
     *
     * @param wrapper
     * @return
     */
    AsSaleOrder getEntity(Wrapper<AsSaleOrder> wrapper);

    /**
     * 通过订单编号查询订单
     *
     * @param orderNo
     * @param tradeNo
     * @param payType
     * @return
     */
    default AsSaleOrder getByNo(String orderNo, String tradeNo, Integer payType) {
        return getEntity(
                Wrappers.<AsSaleOrder>lambdaQuery()
                        .eq(AsSaleOrder::getOrderNo, orderNo)
                        .eq(AsSaleOrder::getTradeNo, tradeNo)
                        .eq(AsSaleOrder::getPayType, payType));
    }

    /**
     * 创建销售订单 - 精条
     *
     * @param createDto
     * @return
     */
    SaleOrderPayInfoDto create(SaleOrderResourcePackCreateDto createDto);

    /**
     * 取消订单
     *
     * @param id
     * @return
     */
    Boolean close(Long id);

    /**
     * 重新支付
     *
     * @param repayDto
     * @return
     */
    SaleOrderPayInfoDto repay(SaleOrderRepayDto repayDto);

    /**
     * 查询支付状态
     *
     * @param id
     * @return
     */
    SaleOrderPaidDto getPayStatus(Long id);

    /**
     * 更新订单
     *
     * @param entity
     * @param wrapper
     * @return
     */
    Boolean updateEntity(AsSaleOrder entity, Wrapper<AsSaleOrder> wrapper);

    /**
     * 支付回调方法
     *
     * @param strEvent
     * @return
     */
    String notify(String strEvent);

    /**
     * 处理支付消息
     *
     * @param messageDto
     */
    Boolean handlePaidMessage(SaleOrderPaidMessageDto messageDto);

    IPage<SaleOrderDto> orderPage(SaleOrderPageQueryDto query);

    /**
     * 可开票总金额
     *
     * @return
     */
    BigDecimal queryInvoiceAmount();

}
