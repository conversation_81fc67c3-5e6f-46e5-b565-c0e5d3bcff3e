package com.niimbot.asset.sale.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsSaleOrderItem;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface AsSaleOrderItemService extends IService<AsSaleOrderItem> {
    /**
     * 通过销售单id查询销售单条目
     *
     * @param orderId
     * @return
     */
    default List<AsSaleOrderItem> queryListByOrderId(Long orderId) {
        return list(Wrappers.<AsSaleOrderItem>lambdaQuery()
                .eq(AsSaleOrderItem::getSaleOrderId, orderId)
                .orderByAsc(AsSaleOrderItem::getId));
    }
}
