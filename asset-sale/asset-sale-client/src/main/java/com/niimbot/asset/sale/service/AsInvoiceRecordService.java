package com.niimbot.asset.sale.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsInvoiceRecord;
import com.niimbot.sale.InvoiceRecordDto;
import com.niimbot.sale.InvoiceRecordQueryDto;

/**
 * <p>
 * 发票记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
public interface AsInvoiceRecordService extends IService<AsInvoiceRecord> {
    IPage<InvoiceRecordDto> page(InvoiceRecordQueryDto query);
}
