package com.niimbot.asset.sale.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsCompanyResource;
import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.sale.CompanyResourceDto;

import java.util.List;

/**
 * <p>
 * 公司资源容量 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface AsCompanyResourceService extends IService<AsCompanyResource> {

    CompanyResourceCapacityDto getCapacity();

    List<CompanyResourceDto> getCapacityDetail();
}
