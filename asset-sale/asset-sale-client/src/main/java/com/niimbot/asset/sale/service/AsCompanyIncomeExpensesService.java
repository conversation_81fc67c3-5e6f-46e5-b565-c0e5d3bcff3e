package com.niimbot.asset.sale.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.sale.model.AsCompanyIncomeExpenses;
import com.niimbot.sale.CompanyIncomeExpensesDto;
import com.niimbot.sale.CompanyIncomeExpensesQueryDto;

import java.util.List;

/**
 * <p>
 * 企业收支 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
public interface AsCompanyIncomeExpensesService extends IService<AsCompanyIncomeExpenses> {

    IPage<CompanyIncomeExpensesDto> queryPage(CompanyIncomeExpensesQueryDto query);

    List<Long> listArrearsOverYear(List<Long> companyIds);
}
