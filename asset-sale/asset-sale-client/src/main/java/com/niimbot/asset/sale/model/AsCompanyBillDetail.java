package com.niimbot.asset.sale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业账单明细（收支账单）
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AsCompanyBillDetail对象", description = "企业账单明细（收支账单）")
@TableName(value = "as_company_bill_detail", autoResultMap = true)
public class AsCompanyBillDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "账单id")
    private Long billId;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "统计周期开始日期")
    private LocalDate statisticalStartTime;

    @ApiModelProperty(value = "统计周期结束日期")
    private LocalDate statisticalEndTime;

    @ApiModelProperty(value = "收支类型：1-收入，2-支出")
    private Integer type;

    @ApiModelProperty(value = "收入/支出")
    private BigDecimal incomeOrExpensesAmount;

    @ApiModelProperty(value = "调账")
    private BigDecimal reconciliationAmount;

    @ApiModelProperty(value = "总收入/总支出")
    private BigDecimal totalIncomeOrExpensesAmount;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
