package com.niimbot.asset.sale.adapter.ding.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.mapper.AsSaleOrderMapper;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.service.AsSaleOrderService;
import com.niimbot.enums.OrderPayTypeEnum;
import com.niimbot.enums.SaleOrderStatusEnum;
import com.niimbot.sale.SaleOrderDto;
import com.niimbot.sale.SaleOrderPageQueryDto;
import com.niimbot.sale.SaleOrderPaidDto;
import com.niimbot.sale.SaleOrderPaidMessageDto;
import com.niimbot.sale.SaleOrderPayInfoDto;
import com.niimbot.sale.SaleOrderRepayDto;
import com.niimbot.sale.SaleOrderResourcePackCreateDto;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 */
@Service
public class DingSaleOrderServiceImpl extends ServiceImpl<AsSaleOrderMapper, AsSaleOrder> implements AsSaleOrderService {
    @Override
    public AsSaleOrder getEntity(Wrapper<AsSaleOrder> wrapper) {
        return null;
    }

    @Override
    public SaleOrderPayInfoDto create(SaleOrderResourcePackCreateDto createDto) {
        return null;
    }

    @Override
    public Boolean close(Long id) {
        return null;
    }

    @Override
    public SaleOrderPayInfoDto repay(SaleOrderRepayDto repayDto) {
        return null;
    }

    @Override
    public SaleOrderPaidDto getPayStatus(Long id) {
        return null;
    }

    @Override
    public Boolean updateEntity(AsSaleOrder entity, Wrapper<AsSaleOrder> wrapper) {
        return null;
    }

    @Override
    public String notify(String strEvent) {
        return null;
    }

    @Override
    public Boolean handlePaidMessage(SaleOrderPaidMessageDto messageDto) {
        return null;
    }

    @Override
    public IPage<SaleOrderDto> orderPage(SaleOrderPageQueryDto query) {
        //查询当前企业订单信息
        query.setCompanyId(LoginUserThreadLocal.getCompanyId());
        IPage<SaleOrderDto> saleOrderDtoIPage = this.getBaseMapper().pageQuery(query.buildIPage(), query);
        if (Objects.isNull(saleOrderDtoIPage) || CollUtil.isEmpty(saleOrderDtoIPage.getRecords())) {
            return saleOrderDtoIPage;
        }

        for (SaleOrderDto item : saleOrderDtoIPage.getRecords()) {
            item.setStatusText(SaleOrderStatusEnum.getByCode(item.getStatus()).getDesc());
            item.setPayTypeText(OrderPayTypeEnum.getByCode(item.getPayType()).getDesc());
        }
        return saleOrderDtoIPage;
    }

    @Override
    public BigDecimal queryInvoiceAmount() {
        return null;
    }

}
