package com.niimbot.asset.sale.adapter.ding.service;

import com.niimbot.asset.sale.mapper.AsSaleOrderMapper;
import com.niimbot.asset.sale.service.ResourceOrderService;
import com.niimbot.sale.ResourceSaleOrderDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 上午9:48
 */
@Slf4j
@Service
public class DingResourceOrderServiceImpl implements ResourceOrderService {

    @Autowired
    private AsSaleOrderMapper saleOrderMapper;

    @Override
    public List<ResourceSaleOrderDto> selectResourceOrder(Long companyId) {
        return saleOrderMapper.selectResourceOrder(companyId);
    }
}
