package com.niimbot.asset.sale.adapter.ding.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.service.AsSaleOrderItemService;
import com.niimbot.asset.sale.service.AsSaleOrderService;
import com.niimbot.sale.SaleOrderDetailDto;
import com.niimbot.sale.SaleOrderDto;
import com.niimbot.sale.SaleOrderItemDto;
import com.niimbot.sale.SaleOrderPageQueryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/12 下午4:36
 */
@RestController
@RequestMapping("server/dingtalk/sale/order")
public class DingSaleOrderController {

    @Autowired
    private AsSaleOrderService saleOrderService;

    @Autowired
    private AsSaleOrderItemService saleOrderItemService;

    /**
     * 订单列表分页查询
     * @param query
     * @return
     */
    @PostMapping("pageQuery")
    public IPage<SaleOrderDto> pageQuery(@RequestBody SaleOrderPageQueryDto query) {
        return saleOrderService.orderPage(query);
    }

    @GetMapping("/{id}")
    public SaleOrderDetailDto getById(@PathVariable("id") Long id) {
        AsSaleOrder saleOrder = saleOrderService.getById(id);
        if (saleOrder == null) {
            return new SaleOrderDetailDto();
        }
        SaleOrderDetailDto dto = BeanUtil.copyProperties(saleOrder, SaleOrderDetailDto.class);
        List<SaleOrderItemDto> items = saleOrderItemService.queryListByOrderId(id)
                .stream()
                .map(item -> {
                    SaleOrderItemDto data = BeanUtil.copyProperties(item, SaleOrderItemDto.class);
                    //商品类型为5表示资源包商品，6是硬件商品
                    if (Objects.nonNull(data.getType()) && data.getType() == 5) {
                        //4-代客下单，quantity存的是月份不满一年，不用除以12
                        if (Objects.nonNull(saleOrder.getPayType()) && saleOrder.getPayType() != 4) {
                            data.setQuantity(item.getQuantity() / 12);
                        }
                        data.setGoodsType(1);
                    } else {
                        data.setGoodsType(2);
                    }
                    return data;
                }).collect(Collectors.toList());
        dto.setItems(items);
        return dto;
    }
}
