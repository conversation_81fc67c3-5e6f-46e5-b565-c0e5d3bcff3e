<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsDiscountConfigMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.sale.DiscountConfigDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="discount_name" jdbcType="VARCHAR" property="discountName" />
    <result column="bonus_condition" property="bonusCondition" typeHandler="com.niimbot.asset.sale.handle.DiscountConditionTypeHandler" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="object_type" jdbcType="INTEGER" property="objectType" />
    <result column="bonus_object" property="bonusObject" typeHandler="com.niimbot.asset.sale.handle.DiscountObjectTypeHandler" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_code, discount_name, start_time, end_time, bonus_condition, object_type, bonus_object,
    is_delete, create_by, create_time, update_by, update_time
  </sql>

  <select id="selectDiscountConfig" resultMap="BaseResultMap">
    select adc.id, adc.biz_code, adc.discount_name, adc.start_time,
           adc.end_time, adc.bonus_condition, adc.object_type, adc.bonus_object
    from as_discount_config adc
    <where>
      adc.is_delete = 0 and adc.start_time <![CDATA[<=]]> #{currentTime} AND adc.end_time <![CDATA[>=]]> #{currentTime}
      <if test="discountCodeList != null and discountCodeList.size() > 0">
        and adc.biz_code in
        <foreach collection="discountCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
      </if>
    </where>
    order by adc.create_time desc
  </select>
</mapper>