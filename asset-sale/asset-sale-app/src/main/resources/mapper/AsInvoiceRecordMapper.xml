<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsInvoiceRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.sale.model.AsInvoiceRecord">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="status" property="status" />
        <result column="sale_order_ids" property="saleOrderIds"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="invoice_money" property="invoiceMoney" />
        <result column="invoice_info" property="invoiceInfo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="address_info" property="addressInfo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="invoice_project" property="invoiceProject" />
        <result column="invoice_media" property="invoiceMedia" />
        <result column="remark" property="remark" />
        <result column="invoice_no" property="invoiceNo" />
        <result column="express_no" property="expressNo" />
        <result column="express_company" property="expressCompany" />
        <result column="picture" property="picture" />
        <result column="fail_reason" property="failReason" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="invoice_title" property="invoiceTitle" />
        <result column="invoice_type" property="invoiceType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, status, sale_order_ids, invoice_money, invoice_info, address_info, invoice_project, invoice_media,
remark, invoice_no, express_no, express_company, picture, fail_reason, create_by, create_time, invoice_title, invoice_type
    </sql>

    <select id="page" resultType="com.niimbot.sale.InvoiceRecordDto">
        select <include refid="Base_Column_List" /> from as_invoice_record
        <where>
            <if test="query.kw!=null and query.kw!=''">
                and (invoice_no LIKE concat('%',#{query.kw},'%') or express_no LIKE
                concat('%',#{query.kw},'%'))
            </if>
            <if test="query.createTime!= null and query.createTime.size()==2">
                <if test="query.createTime[0]!=null and query.createTime[0]!='' and query.createTime[0]!='null'">
                    and create_time &gt;= concat(#{query.createTime[0]}, ' 00:00:00')
                </if>
                <if test="query.createTime[1]!=null and query.createTime[1]!='' and query.createTime[1]!='null'">
                    and create_time &lt;= concat(#{query.createTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="query.status!=null">
                and status = #{query.status}
            </if>
            <if test="query.invoiceType!=null">
                and invoice_type = #{query.invoiceType}
            </if>
            <if test="query.invoiceMedia != null">
                and invoice_media = #{query.invoiceMedia}
            </if>
        </where>
        ORDER BY
        create_time DESC
    </select>
</mapper>
