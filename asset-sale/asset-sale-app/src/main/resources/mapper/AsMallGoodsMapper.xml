<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsMallGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.sale.model.AsMallGoods">
        <id column="id" property="id" />
        <result column="goods_name" property="goodsName" />
        <result column="subtitle" property="subtitle" />
        <result column="goods_url" property="goodsUrl" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="sku" property="sku" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="goods_trait" property="goodsTrait" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="goods_detail" property="goodsDetail" />
        <result column="goods_unit_price" property="goodsUnitPrice" />
        <result column="goods_model" property="goodsModel" />
        <result column="goods_charge_unit" property="goodsChargeUnit" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="is_delete" property="isDelete" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <resultMap id="GoodsResultMap" type="com.niimbot.sale.MallGoodsDto">
        <id column="id" property="id" />
        <result column="goods_name" property="goodsName" />
        <result column="subtitle" property="subtitle" />
        <result column="goods_url" property="goodsUrl" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="sku" property="sku" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="goods_trait" property="goodsTrait" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="sku_code" property="skuCode" />
        <result column="goods_detail" property="goodsDetail" />
        <result column="goods_unit_price" property="goodsUnitPrice" />
        <result column="goods_model" property="goodsModel" />
        <result column="goods_charge_unit" property="goodsChargeUnit" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="is_delete" property="isDelete" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_name, subtitle, goods_url, sku, goods_unit_price, goods_model, goods_charge_unit,
        goods_trait, goods_detail, sort_num, status, is_delete, create_by, create_time, update_by, update_time
    </sql>

    <select id="pageQuery" resultMap="GoodsResultMap">
        select <include refid="Base_Column_List"/> from as_mall_goods a where a.status = 0 and a.is_delete=0 order by a.sort_num desc
    </select>

    <select id="selectBySkuCode" resultMap="GoodsResultMap">
        select a.id, a.goods_name, a.goods_url, a.subtitle, a.goods_unit_price, a.goods_model,
                a.goods_detail, a.goods_trait, a.goods_charge_unit, b.middlen_sku_code as sku_code
        from as_mall_goods as a left join as_mall_goods_ext as b on a.id = b.mall_goods_id
        <where>
            a.is_delete = 0 and a.status = 0 and b.is_delete = 0
            <if test="skuCodeList != null and skuCodeList.size() > 0">
                and b.middlen_sku_code in
                <foreach collection="skuCodeList" item="skuCode" index="index" open="(" separator="," close=")">
                    #{skuCode}
                </foreach>
            </if>
        </where>
        order by a.sort_num desc, a.create_time desc
    </select>

</mapper>
