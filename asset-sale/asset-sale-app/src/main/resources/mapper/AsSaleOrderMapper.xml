<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsSaleOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="SaleOrderDto" type="com.niimbot.sale.SaleOrderDto">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="summary" property="summary"/>
        <result column="total_money" property="totalMoney"/>
        <result column="pay_money" property="payMoney"/>
        <result column="discount_money" property="discountMoney"/>
        <result column="pay_type" property="payType"/>
        <result column="status" property="status"/>
        <result column="invoice_status" property="invoiceStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="buy_time" property="buyTime"/>
        <result column="expiration_time" property="expirationTime"/>
    </resultMap>

    <select id="orderPage" resultMap="SaleOrderDto">
        SELECT
        o.id,
        o.order_no,
        o.summary,
        o.total_money,
        o.pay_money,
        o.discount_money,
        o.pay_type,
        o.STATUS,
        o.invoice_status,
        o.create_time,
        i.quantity as buy_time,
        cr.expiration_time
        FROM
        as_sale_order o
        left join as_sale_order_item i on o.id = i.sale_order_id and i.type = 5
        left join as_company_resource cr on cr.sale_order_id = o.id
        <where>
            <if test="ew.kw!=null and ew.kw!=''">
                and (o.order_no LIKE concat('%',#{ew.kw},'%') or o.summary LIKE
                concat('%',#{ew.kw},'%'))
            </if>
            <if test="ew.createTime!= null and ew.createTime.size()==2">
                <if test="ew.createTime[0]!=null and ew.createTime[0]!='' and ew.createTime[0]!='null'">
                    and o.create_time &gt;= concat(#{ew.createTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.createTime[1]!=null and ew.createTime[1]!='' and ew.createTime[1]!='null'">
                    and o.create_time &lt;= concat(#{ew.createTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.operateDate!= null and ew.operateDate.size()==2">
                <if test="ew.operateDate[0]!=null and ew.operateDate[0]!='' and ew.operateDate[0]!='null'">
                    and o.operate_time &gt;= concat(#{ew.operateDate[0]}, ' 00:00:00')
                </if>
                <if test="ew.operateDate[1]!=null and ew.operateDate[1]!='' and ew.operateDate[1]!='null'">
                    and o.operate_time &lt;= concat(#{ew.operateDate[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.expirationTime!= null and ew.expirationTime.size()==2">
                <if test="ew.expirationTime[0]!=null and ew.expirationTime[0]!='' and ew.expirationTime[0]!='null'">
                    and cr.expiration_time &gt;= concat(#{ew.expirationTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.expirationTime[1]!=null and ew.expirationTime[1]!='' and ew.expirationTime[1]!='null'">
                    and cr.expiration_time &lt;= concat(#{ew.expirationTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.payMoney!= null and ew.payMoney.size()==2">
                <if test="ew.payMoney[0]!=null">
                    and o.pay_money &gt;= #{ew.payMoney[0]}
                </if>
                <if test="ew.payMoney[1]!=null">
                    and o.pay_money &lt;= #{ew.payMoney[1]}
                </if>
            </if>
            <if test="ew.buyTime!= null and ew.buyTime.size()==2">
                <if test="ew.buyTime[0]!=null">
                    and i.quantity &gt;= #{ew.buyTime[0]}
                </if>
                <if test="ew.buyTime[1]!=null">
                    and i.quantity &lt;= #{ew.buyTime[1]}
                </if>
            </if>
            <if test="ew.payType!=null">
                and o.pay_type = #{ew.payType}
            </if>
            <if test="ew.status!=null">
                and status = #{ew.status}
            </if>
            <if test="ew.invoiceStatus!=null">
                and o.invoice_status = #{ew.invoiceStatus}
            </if>
        </where>
        ORDER BY
        o.create_time DESC
    </select>

    <select id="pageQuery" resultMap="SaleOrderDto">
        SELECT
        o.id,
        o.order_no,
        o.summary,
        o.pay_money,
        o.pay_type,
        o.`status`,
        o.create_time,
        o.pay_url
        FROM as_sale_order as o
        <where>
            o.id in (
                select o.id
                from as_sale_order a left join as_sale_order_item b on a.id = b.sale_order_id
                where a.company_id = #{ew.companyId} and b.type in (5,6)
            )
            <if test="ew.kw!=null and ew.kw!=''">
                and (o.order_no LIKE concat('%',#{ew.kw},'%') or o.summary LIKE
                concat('%',#{ew.kw},'%'))
            </if>
            <if test="ew.createTime!= null and ew.createTime.size()==2">
                <if test="ew.createTime[0]!=null and ew.createTime[0]!='' and ew.createTime[0]!='null'">
                    and o.create_time &gt;= concat(#{ew.createTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.createTime[1]!=null and ew.createTime[1]!='' and ew.createTime[1]!='null'">
                    and o.create_time &lt;= concat(#{ew.createTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.operateDate!= null and ew.operateDate.size()==2">
                <if test="ew.operateDate[0]!=null and ew.operateDate[0]!='' and ew.operateDate[0]!='null'">
                    and o.operate_time &gt;= concat(#{ew.operateDate[0]}, ' 00:00:00')
                </if>
                <if test="ew.operateDate[1]!=null and ew.operateDate[1]!='' and ew.operateDate[1]!='null'">
                    and o.operate_time &lt;= concat(#{ew.operateDate[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.expirationTime!= null and ew.expirationTime.size()==2">
                <if test="ew.expirationTime[0]!=null and ew.expirationTime[0]!='' and ew.expirationTime[0]!='null'">
                    and cr.expiration_time &gt;= concat(#{ew.expirationTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.expirationTime[1]!=null and ew.expirationTime[1]!='' and ew.expirationTime[1]!='null'">
                    and cr.expiration_time &lt;= concat(#{ew.expirationTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.payMoney!= null and ew.payMoney.size()==2">
                <if test="ew.payMoney[0]!=null">
                    and o.pay_money &gt;= #{ew.payMoney[0]}
                </if>
                <if test="ew.payMoney[1]!=null">
                    and o.pay_money &lt;= #{ew.payMoney[1]}
                </if>
            </if>
            <if test="ew.buyTime!= null and ew.buyTime.size()==2">
                <if test="ew.buyTime[0]!=null">
                    and i.quantity &gt;= #{ew.buyTime[0]}
                </if>
                <if test="ew.buyTime[1]!=null">
                    and i.quantity &lt;= #{ew.buyTime[1]}
                </if>
            </if>
            <if test="ew.payType!=null">
                and o.pay_type = #{ew.payType}
            </if>
            <if test="ew.status!=null">
                and status = #{ew.status}
            </if>
            <if test="ew.invoiceStatus!=null">
                and o.invoice_status = #{ew.invoiceStatus}
            </if>
        </where>
        ORDER BY o.create_time DESC
    </select>

    <select id="queryInvoiceAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(pay_money), 0) from as_sale_order where status = 2 and invoice_status = 1
    </select>

    <select id="getResourceOrder" resultMap="SaleOrderDto" resultType="com.niimbot.sale.SaleOrderPageQueryDto">
        select a.id, a.pay_money, a.create_time, a.operate_time, d.quantity
        from as_sale_order as a
                 inner join (select b.id, sum(ifnull(c.quantity, 0)) as quantity
                            from as_sale_order as b
                                     left join as_sale_order_item as c
                                               on b.id = c.sale_order_id
                            where b.company_id = #{param.companyId}
                              and b.status = #{param.status}
                              and c.type = #{param.orderType}
                            group by b.id) as d
                           on a.id = d.id
        where a.company_id = #{param.companyId}
          and a.status = #{param.status}
        order by a.create_time desc
    </select>

    <select id="selectResourceOrder" parameterType="java.lang.Long" resultType="com.niimbot.sale.ResourceSaleOrderDto">
        select b.id                as id,
               max(b.pay_money)    as payMoney,
               max(b.create_time)  as createTime,
               max(b.operate_time) as operateTime,
               max(b.order_no)     as orderNo,
               sum(IFNULL(c.quantity, 0)) as quantity
                   from as_sale_order as b
                   left join as_sale_order_item as c on b.id = c.sale_order_id
                   left join as_company_resource as d on b.id = d.sale_order_id
                   where b.company_id = #{companyId}
                       and b.status = 2
                       and c.type = 5
                       and d.effective_time <![CDATA[ <= ]]> sysdate()
                   group by b.id
    </select>

</mapper>
