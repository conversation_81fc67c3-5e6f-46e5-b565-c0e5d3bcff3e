<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsCompanyResourceMapper">

    <select id="getCapacity" resultType="com.niimbot.sale.CompanyResourceCapacityDto">
	select t.id as company_id,
	    t.name as company_name,
	(ifnull( t.capacity, 0 ) - ifnull( t.asset_num, 0 )) AS remainder,
    ifnull( t.asset_num, 0 ) AS has_used,
	ifnull( t.capacity, 0 ) AS capacity,
	t.expiration_time,
	ifnull( t.asset_num, 0 ) AS asset_num
	 from (
	select 	c.id, c.name,
     (select count(1) from as_asset  where is_delete = 0 and company_id = c.id) as asset_num,
     (SELECT sum( capacity ) FROM as_company_resource WHERE expiration_time > sysdate() AND effective_time <![CDATA[ <= ]]> sysdate() AND company_id = c.id) as capacity,
     (SELECT max( expiration_time ) FROM as_company_resource WHERE experience = 0 AND expiration_time IS NOT NULL AND company_id = c.id) as expiration_time
     from as_company c WHERE
	    c.id = #{companyId}
     ) t
	</select>

    <select id="getCapacityDetail" resultType="com.niimbot.sale.CompanyResourceDto">
        SELECT
            r.id,
            r.resource_name,
            r.capacity,
            r.expiration_time,
            r.effective_time,
            r.create_time,
            ifnull(i.quantity, timestampdiff(month, r.effective_time, r.expiration_time)) AS buy_time
        FROM
            as_company_resource r left join as_sale_order_item i on r.sale_order_id = i.sale_order_id and r.sku_code = i.sku
        WHERE
            r.experience = 0 and  (i.type = 5 or i.type is null)
        ORDER BY
            r.create_time ASC
    </select>

</mapper>
