<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsResourceConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.sale.ResourcePackDto">
        <id column="id" property="id"/>
        <result column="biz_code" property="resourceCode"/>
        <result column="resource_name" property="resourceName"/>
        <result column="sku_code" property="skuCode"/>
        <result column="capacity" property="capacity"/>
        <result column="annual_price" property="annualPrice"/>
        <result column="unit_price" property="unitPrice"/>
    </resultMap>

    <select id="listPack" resultMap="BaseResultMap">
        SELECT
        rc.id,
        rc.biz_code,
        rc.resource_name,
        rc.sku_code,
        rc.capacity,
        rc.annual_price,
        rc.unit_price
        FROM
        as_resource_config rc
        WHERE
        rc.is_delete = 0 and rc.front_display = 1
        <if test="id!=null">
            AND rc.id = #{id}
        </if>
        ORDER BY rc.capacity ASC, rc.create_time ASC
    </select>

    <select id="selectBySkuCode" resultMap="BaseResultMap">
        SELECT
        rc.id,
        rc.biz_code,
        rc.resource_name,
        rc.sku_code,
        rc.capacity,
        rc.annual_price,
        rc.unit_price
        FROM
        as_resource_config rc
        WHERE
        rc.is_delete = 0 and rc.front_display = 1
        <if test="skuCodeList != null and skuCodeList.size() > 0">
            AND rc.sku_code in
            <foreach collection="skuCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY rc.capacity ASC, rc.create_time ASC
    </select>

</mapper>
