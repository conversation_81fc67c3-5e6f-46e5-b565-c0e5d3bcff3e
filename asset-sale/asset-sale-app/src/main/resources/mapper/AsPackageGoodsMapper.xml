<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsPackageGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.sale.model.AsPackageGoods">
        <id column="id" property="id" />
        <result column="package_name" property="packageName" />
        <result column="subtitle" property="subtitle"/>
        <result column="package_url" property="packageUrl" />
        <result column="sku" property="sku" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="sort_num" property="sortNum" />
        <result column="apply_describe" property="applyDescribe" />
        <result column="apply_employee" property="applyEmployee" />
        <result column="apply_asset" property="applyAsset" />
        <result column="marketing_plan" property="marketingPlan" />
        <result column="goods_url" property="goodsUrl" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="goods_main_url" property="goodsMainUrl"/>
        <result column="goods_trait" property="goodsTrait" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="goods_detail" property="goodsDetail"/>
        <result column="status" property="status" />
        <result column="is_delete" property="isDelete" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, package_name, subtitle, package_url, sku, sort_num, apply_describe, apply_employee, apply_asset, marketing_plan, goods_url,  goods_main_url, goods_trait, goods_detail, status, is_delete, create_by, create_time, update_by, update_time
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from as_package_goods a
        <where>
          and a.status = 0
          and a.is_delete = 0
        </where>
        order by a.sort_num desc
    </select>


</mapper>
