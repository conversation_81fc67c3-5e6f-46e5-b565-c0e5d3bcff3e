<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsCompanyIncomeExpensesMapper">

    <select id="queryPage" resultType="com.niimbot.sale.CompanyIncomeExpensesDto">
        SELECT
        id,
        type,
        biz_type,
        biz_desc,
        trade_time,
        trade_amount,
        balance,
        arrears,
        sale_order_id,
        sale_order_no
        FROM
        as_company_income_expenses
        <where>
            <if test="ew.type!=null">
                and type = #{ew.type}
            </if>
            <if test="ew.bizType!=null">
                and biz_type = #{ew.bizType}
            </if>
            <if test="ew.bizDesc!=null">
                and biz_desc = #{ew.bizDesc}
            </if>
            <if test="ew.tradeTime!= null and ew.tradeTime.size()==2">
                <if test="ew.tradeTime[0]!=null and ew.tradeTime[0]!='' and ew.tradeTime[0]!='null'">
                    and trade_time &gt;= concat(#{ew.tradeTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.tradeTime[1]!=null and ew.tradeTime[1]!='' and ew.tradeTime[1]!='null'">
                    and trade_time &lt;= concat(#{ew.tradeTime[1]}, ' 23:59:59')
                </if>
            </if>
        </where>
        order by trade_time desc
    </select>

    <select id="listArrearsOverYear" resultType="java.lang.Long">
        SELECT
        DISTINCT
        a.company_id
        FROM
        as_company_income_expenses a,
        ( SELECT company_id, MAX( trade_time ) trade_time FROM as_company_income_expenses GROUP BY
        company_id ) b
        WHERE
        a.company_id = b.company_id
        and a.company_id IN
        <foreach collection="companyIds" item="companyId" index="index" open="(" close=")"
                 separator=",">
            #{companyId}
        </foreach>
        AND a.trade_time = b.trade_time
        AND a.biz_type = 3
        AND a.trade_time &lt; DATE_SUB( CURDATE(), INTERVAL 1 YEAR )
    </select>
</mapper>
