<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.sale.mapper.AsGoodsSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GoodsInfoDto" type="com.niimbot.sale.GoodsInfoDto">
        <result column="name" property="name"/>
        <result column="info" property="info"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="title" property="title"/>
        <result column="code" property="code"/>
        <result column="price" property="price"/>
        <result column="asset_num" property="assetNum"/>
    </resultMap>

    <select id="getSku" resultMap="GoodsInfoDto">
        SELECT
            g.NAME,
            g.info,
            gs.title,
            gs.code,
            gs.price,
            gs.asset_num
        FROM
            as_goods g,
            as_goods_sku gs
        WHERE
            g.id = gs.goods_id
            and g.type = #{type}
        ORDER BY
            gs.asset_num asc, gs.price asc
    </select>

</mapper>
