package com.niimbot.asset.sale.strategy;

import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.model.AsSaleOrderItem;
import com.niimbot.asset.sale.service.AsSaleOrderItemService;
import com.niimbot.asset.system.service.AsRecommendRecordService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/12/30 10:24
 */
@Slf4j
@Component
public class OrderNoticeContext {

    private final Map<Integer, OrderStrategy> strategyMap = new ConcurrentHashMap<>();

    private final AsSaleOrderItemService saleOrderItemService;
    private final AsRecommendRecordService recommendRecordService;

    @Autowired
    public OrderNoticeContext(AsSaleOrderItemService saleOrderItemService,
                              ResourcePackOrderStrategy resourcePackOrderStrategy,
                              AsRecommendRecordService recommendRecordService) {
        this.saleOrderItemService = saleOrderItemService;
        this.recommendRecordService = recommendRecordService;
        strategyMap.put(DictConstant.PRODUCT_TYPE_RESOURCE_PACK, resourcePackOrderStrategy);
    }

    public void execute(AsSaleOrder order) {
        try {
            // 老带新记录
            recommendRecordService.tradeRecord(order.getId(), order.getCompanyId(), order.getTotalMoney(), order.getOperateTime());
        } catch (Exception e) {
            log.error("recommend Record error, {}", e.getMessage(), e);
        }

        List<AsSaleOrderItem> saleOrderItems = saleOrderItemService.queryListByOrderId(order.getId());
        saleOrderItems.forEach(item -> {
            if (strategyMap.containsKey(item.getType())) {
                strategyMap.get(item.getType()).handleOrder(order, item);
            }
        });
    }

}
