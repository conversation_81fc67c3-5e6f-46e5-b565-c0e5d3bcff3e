package com.niimbot.asset.sale.controller;



import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.sale.model.AsBanner;
import com.niimbot.asset.sale.service.AsBannerService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * Banner图详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Api(tags = "商城购物")
@RestController
@RequestMapping("server/sale/banner")
@RequiredArgsConstructor
public class AsBannerServiceController {

    private final AsBannerService bannerService;

    @ApiOperation(value = "banner列表")
    @GetMapping("/list/{localId}")
    public List<AsBanner> page(@PathVariable Long localId) {

      return  bannerService.list (new LambdaQueryWrapper<AsBanner>().eq(AsBanner::getIsDelete, false).eq(AsBanner::getStatus, false).eq(AsBanner::getLocationId,localId).orderByDesc(AsBanner::getSortNum));

    }

}
