package com.niimbot.asset.sale.domain.abs.impl;

import com.niimbot.asset.sale.mapstruct.SaleOrderMapStruct;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.service.AsSaleOrderService;
import com.niimbot.asset.system.abs.SaleOrderAbs;
import com.niimbot.asset.system.dto.SaleOrderGetQry;
import com.niimbot.asset.system.dto.clientobject.SaleOrderCO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/client/abs/sale/saleOrderAbs")
@RequiredArgsConstructor
public class SaleOrderAbsImpl implements SaleOrderAbs {

    private final SaleOrderMapStruct saleOrderMapStruct;

    private final AsSaleOrderService saleOrderService;

    @Override
    public SaleOrderCO getSaleOrder(SaleOrderGetQry qry) {
        AsSaleOrder saleOrder = saleOrderService.getById(qry.getId());
        return saleOrderMapStruct.convertSaleOrderModelToCo(saleOrder);
    }

}
