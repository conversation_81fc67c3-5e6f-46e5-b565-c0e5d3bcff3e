package com.niimbot.asset.sale.controller;


import com.niimbot.asset.sale.service.AsInvoiceSettingService;
import com.niimbot.sale.InvoiceSettingDto;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 开票设置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@RestController
@RequestMapping("server/sale/invoiceSetting")
@RequiredArgsConstructor
public class AsInvoiceSettingServiceController {
    private final AsInvoiceSettingService invoiceSettingService;

    @ApiOperation(value = "查询")
    @GetMapping
    public InvoiceSettingDto queryInfo() {
        return invoiceSettingService.queryInfo();
    }
}
