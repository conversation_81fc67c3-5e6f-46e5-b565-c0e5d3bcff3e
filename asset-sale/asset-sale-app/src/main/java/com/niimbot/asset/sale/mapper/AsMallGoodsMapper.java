package com.niimbot.asset.sale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.sale.model.AsMallGoods;
import com.niimbot.sale.MallGoodsDto;
import com.niimbot.sale.MallGoodsPageDto;
import com.niimbot.sale.MallGoodsQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
public interface AsMallGoodsMapper extends BaseMapper<AsMallGoods> {

    /**
     * 商品列表分页管理查询
     *
     * @param buildIPage
     * @param query
     * @return
     */
    IPage<MallGoodsPageDto> pageQuery(@Param("page") Page<MallGoodsPageDto> buildIPage,
                                      @Param("query") MallGoodsQueryDto query);

    /**
     * 根据skuCode查询硬件商品信息
     * @param skuCodeList
     * @return
     */
    List<MallGoodsDto> selectBySkuCode(@Param("skuCodeList") List<String> skuCodeList);
}
