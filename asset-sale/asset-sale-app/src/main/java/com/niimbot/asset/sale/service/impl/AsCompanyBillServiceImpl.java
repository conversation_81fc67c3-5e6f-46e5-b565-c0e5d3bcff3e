package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.sale.mapper.AsCompanyBillMapper;
import com.niimbot.asset.sale.model.AsCompanyBill;
import com.niimbot.asset.sale.model.AsCompanyBillDetail;
import com.niimbot.asset.sale.service.AsCompanyBillDetailService;
import com.niimbot.asset.sale.service.AsCompanyBillService;
import com.niimbot.sale.CompanyBillQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * <p>
 * 企业账单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
public class AsCompanyBillServiceImpl extends ServiceImpl<AsCompanyBillMapper, AsCompanyBill> implements AsCompanyBillService {

    private final AsCompanyBillDetailService companyBillDetailService;

    @Autowired
    public AsCompanyBillServiceImpl(AsCompanyBillDetailService companyBillDetailService) {
        this.companyBillDetailService = companyBillDetailService;
    }

    @Override
    public IPage<AsCompanyBill> pageAll(CompanyBillQueryDto query) {
        return this.page(query.buildIPage(), new LambdaQueryWrapper<AsCompanyBill>()
                .eq(ObjectUtil.isNotNull(query.getYear()), AsCompanyBill::getYear, query.getYear())
                .eq(ObjectUtil.isNotNull(query.getMonth()), AsCompanyBill::getMonth, query.getMonth())
                .and(BooleanUtil.isTrue(query.getNotZero()), wrapper ->
                        wrapper.gt(AsCompanyBill::getIncomeAmount, 0).or().gt(AsCompanyBill::getExpensesAmount, 0)
                ).orderByDesc(AsCompanyBill::getYear).orderByDesc(AsCompanyBill::getMonth));
    }

    @Override
    public IPage<AsCompanyBillDetail> pageIncome(CompanyBillQueryDto query) {
        return this.companyBillDetailService.page(query.buildIPage(), new LambdaQueryWrapper<AsCompanyBillDetail>()
                .eq(AsCompanyBillDetail::getType, DictConstant.INCOME_EXPENSES_TYPE_INCOME)
                .eq(ObjectUtil.isNotNull(query.getYear()), AsCompanyBillDetail::getYear, query.getYear())
                .eq(ObjectUtil.isNotNull(query.getMonth()), AsCompanyBillDetail::getMonth, query.getMonth())
                .and(BooleanUtil.isTrue(query.getNotZero()), wrapper ->
                        wrapper.gt(AsCompanyBillDetail::getTotalIncomeOrExpensesAmount, 0)
                ).orderByDesc(AsCompanyBillDetail::getYear).orderByDesc(AsCompanyBillDetail::getMonth));
    }

    @Override
    public IPage<AsCompanyBillDetail> pageExpenses(CompanyBillQueryDto query) {
        return this.companyBillDetailService.page(query.buildIPage(), new LambdaQueryWrapper<AsCompanyBillDetail>()
                .eq(AsCompanyBillDetail::getType, DictConstant.INCOME_EXPENSES_TYPE_EXPENSES)
                .eq(ObjectUtil.isNotNull(query.getYear()), AsCompanyBillDetail::getYear, query.getYear())
                .eq(ObjectUtil.isNotNull(query.getMonth()), AsCompanyBillDetail::getMonth, query.getMonth())
                .and(BooleanUtil.isTrue(query.getNotZero()), wrapper ->
                        wrapper.gt(AsCompanyBillDetail::getTotalIncomeOrExpensesAmount, 0)
                ).orderByDesc(AsCompanyBillDetail::getYear).orderByDesc(AsCompanyBillDetail::getMonth));
    }

}
