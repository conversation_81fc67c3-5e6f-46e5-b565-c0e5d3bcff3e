package com.niimbot.asset.sale.domain.abs.impl;

import com.niimbot.asset.sale.service.ResourceOrderService;
import com.niimbot.asset.system.abs.ResourceOrderAbs;
import com.niimbot.sale.ResourceSaleOrderDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 上午9:26
 */
@RestController
@RequestMapping("/client/abs/resource/resourceOrderAbs")
@RequiredArgsConstructor
public class ResourceOrderAbsImpl implements ResourceOrderAbs {

    @Autowired
    private ResourceOrderService resourceOrderService;

    @Override
    public List<ResourceSaleOrderDto> queryResourceOrder(Long companyId) {
        return resourceOrderService.selectResourceOrder(companyId);
    }
}
