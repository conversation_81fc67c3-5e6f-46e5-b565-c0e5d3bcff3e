package com.niimbot.asset.sale.controller;


import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.sale.model.AsPackageGoods;
import com.niimbot.asset.sale.service.AsPackageGoodsService;
import com.niimbot.sale.PackageGoodsPageDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 商品套餐详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@RestController
@RequestMapping("server/sale/packageGoods")
@RequiredArgsConstructor
public class AsPackageGoodsController {

    private final AsPackageGoodsService packageGoodsService;

    @ApiOperation(value = "套餐列表")
    @GetMapping("/list")
    @AutoConvert
    public List<AsPackageGoods> page() {
      return  packageGoodsService.listPackageGoods();
    }

    @ApiOperation(value = "套餐详情")
    @GetMapping("/detail/{id}")
    public PackageGoodsPageDto detail(@PathVariable("id") Long id) {
        return packageGoodsService.detail(id);
    }

}
