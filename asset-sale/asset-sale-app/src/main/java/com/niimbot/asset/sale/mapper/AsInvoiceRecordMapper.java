package com.niimbot.asset.sale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.sale.model.AsInvoiceRecord;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.sale.InvoiceRecordDto;
import com.niimbot.sale.InvoiceRecordQueryDto;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 发票记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@EnableDataPerm
public interface AsInvoiceRecordMapper extends BaseMapper<AsInvoiceRecord> {

    IPage<InvoiceRecordDto> page(@Param("page") Page<AsInvoiceRecord> buildIPage,
                                 @Param("query") InvoiceRecordQueryDto query);
}
