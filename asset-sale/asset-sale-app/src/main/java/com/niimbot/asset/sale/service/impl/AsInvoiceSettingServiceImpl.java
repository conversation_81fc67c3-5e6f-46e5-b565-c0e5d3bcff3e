package com.niimbot.asset.sale.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsInvoiceSettingMapper;
import com.niimbot.asset.sale.model.AsInvoiceSetting;
import com.niimbot.asset.sale.service.AsInvoiceSettingService;
import com.niimbot.sale.InvoiceSettingDto;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 开票设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Service
public class AsInvoiceSettingServiceImpl extends ServiceImpl<AsInvoiceSettingMapper, AsInvoiceSetting> implements AsInvoiceSettingService {
    @Override
    public InvoiceSettingDto queryInfo() {
        AsInvoiceSetting invoiceSetting = getById(AsInvoiceSetting.ID);
        if (invoiceSetting == null) {
            return new InvoiceSettingDto();
        }
        return BeanUtil.copyProperties(invoiceSetting, InvoiceSettingDto.class);
    }
}
