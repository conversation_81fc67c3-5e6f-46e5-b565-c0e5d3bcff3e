package com.niimbot.asset.sale.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.sale.model.AsGoodsSku;
import com.niimbot.sale.GoodsInfoDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品SKU Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
public interface AsGoodsSkuMapper extends BaseMapper<AsGoodsSku> {

    List<GoodsInfoDto> getSku(@Param("type") Integer type);
}
