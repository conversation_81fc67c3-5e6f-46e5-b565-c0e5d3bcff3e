package com.niimbot.asset.sale.controller;

import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.model.AsCompanyWallet;
import com.niimbot.asset.sale.service.AsCompanyWalletService;
import com.niimbot.sale.CompanyWalletArrearsDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/12/29 10:10
 */
@RestController
@RequestMapping("server/sale/company/wallet")
@RequiredArgsConstructor
public class AsCompanyWalletServiceController {

    private final AsCompanyWalletService companyWalletService;

    @GetMapping
    public AsCompanyWallet getWallet() {
        return this.companyWalletService.getById(LoginUserThreadLocal.getCompanyId());
    }

    @GetMapping("/arrears/{companyId}")
    public CompanyWalletArrearsDto getWalletArrears(@PathVariable("companyId") Long companyId) {
        return this.companyWalletService.getWalletArrearsDto(companyId);
    }
}
