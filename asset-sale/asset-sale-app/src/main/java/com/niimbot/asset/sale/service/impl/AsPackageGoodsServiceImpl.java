package com.niimbot.asset.sale.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsPackageGoodsMapper;
import com.niimbot.asset.sale.model.AsPackageGoods;
import com.niimbot.asset.sale.service.AsPackageGoodsService;
import com.niimbot.sale.PackageGoodsPageDto;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;

@Service
public class AsPackageGoodsServiceImpl extends ServiceImpl<AsPackageGoodsMapper, AsPackageGoods> implements AsPackageGoodsService {

    @Override
    public List<AsPackageGoods> listPackageGoods() {
        return this.baseMapper.selectList();
    }

    @Override
    public PackageGoodsPageDto detail(Long id) {
        AsPackageGoods packageGoods = this.getBaseMapper().selectById(id);
        if (ObjectUtil.isNotNull(packageGoods)) {
            return BeanUtil.copyProperties(packageGoods, PackageGoodsPageDto.class);
        } else {
            return new PackageGoodsPageDto();
        }

    }
}
