package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsEmailAddressMapper;
import com.niimbot.asset.sale.model.AsEmailAddress;
import com.niimbot.asset.sale.service.AsEmailAddressService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 邮箱地址 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class AsEmailAddressServiceImpl extends ServiceImpl<AsEmailAddressMapper, AsEmailAddress> implements AsEmailAddressService {

}
