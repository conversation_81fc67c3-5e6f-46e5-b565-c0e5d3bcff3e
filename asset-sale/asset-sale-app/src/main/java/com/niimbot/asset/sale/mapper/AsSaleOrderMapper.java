package com.niimbot.asset.sale.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.sale.mapper.provider.AsSaleOrderUpdateEntityProvider;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.sale.ResourceSaleOrderDto;
import com.niimbot.sale.SaleOrderDto;
import com.niimbot.sale.SaleOrderPageQueryDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 版本销售单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@EnableDataPerm(excludeMethodName = {"updateEntity", "getEntity"})
public interface AsSaleOrderMapper extends BaseMapper<AsSaleOrder> {

    /**
     * 更新销售单
     *
     * @param entity
     * @param wrapper
     * @return
     */
    @UpdateProvider(type = AsSaleOrderUpdateEntityProvider.class, method = "updateEntity")
    int updateEntity(@Param(Constants.ENTITY) AsSaleOrder entity,
                     @Param(Constants.WRAPPER) Wrapper<AsSaleOrder> wrapper);

    /**
     * 条件查询销售单
     *
     * @param wrapper
     * @return
     */
    @Select("select id, company_id, company_name, order_no, summary, source, total_money, pay_money, discount_money, " +
            "expired_time, trade_no, receive_payment_no, pay_type, operate_time, status, mobile, invoice_info, " +
            "create_by, create_time, customer_remark, remark, version from as_sale_order ${ew.customSqlSegment}")
    AsSaleOrder getEntity(@Param(Constants.WRAPPER) Wrapper<AsSaleOrder> wrapper);


    IPage<SaleOrderDto> orderPage(@Param("page") Page<Object> buildIPage, @Param("ew") SaleOrderPageQueryDto query);

    /**
     * 订单分页查询，订单-商品：1对多
     * @param buildIPage
     * @param query
     * @return
     */
    IPage<SaleOrderDto> pageQuery(@Param("page") Page<Object> buildIPage, @Param("ew") SaleOrderPageQueryDto query);

    /**
     * 可开票总金额查询
     *
     * @return
     */
    BigDecimal queryInvoiceAmount();

    /**
     * 获取
     * @return
     */
    List<SaleOrderDto> getResourceOrder(@Param("param") SaleOrderPageQueryDto queryDto);

    List<ResourceSaleOrderDto> selectResourceOrder(@Param("companyId") Long companyId);
}
