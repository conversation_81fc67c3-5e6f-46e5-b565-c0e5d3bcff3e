package com.niimbot.asset.sale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.sale.model.AsResourceConfig;
import com.niimbot.sale.ResourcePackDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/30 下午3:33
 */
public interface AsResourceConfigMapper extends BaseMapper<AsResourceConfig> {

    List<ResourcePackDto> listPack(@Param("id") Long id);

    /**
     * 根据skuCode批量查询资源包信息
     * @param skuCodeList
     * @return
     */
    List<ResourcePackDto> selectBySkuCode(@Param("skuCodeList") List<String> skuCodeList);
}