package com.niimbot.asset.sale.domain.abs.impl;

import com.niimbot.asset.sale.mapstruct.CompanyResourceMapStruct;
import com.niimbot.asset.sale.model.AsCompanyResource;
import com.niimbot.asset.sale.service.AsCompanyResourceService;
import com.niimbot.asset.system.abs.CompanyResourceAbs;
import com.niimbot.asset.system.dto.CompanyResourceAddCmd;
import com.niimbot.asset.system.dto.clientobject.CompanyResourceCO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/13 15:07
 */
@RestController
@RequestMapping("/client/abs/sale/companyResourceAbs/")
@RequiredArgsConstructor
public class CompanyResourceAbsImpl implements CompanyResourceAbs {

    private final AsCompanyResourceService companyResourceService;
    private final CompanyResourceMapStruct companyResourceMapStruct;

    /**
     * 保存企业资源包
     *
     * @param cmd
     */
    @Override
    public void saveResource(CompanyResourceAddCmd cmd) {
        CompanyResourceCO companyResource = cmd.getCompanyResource();
        AsCompanyResource resource = companyResourceMapStruct.toEntity(companyResource);
        companyResourceService.save(resource);
    }

}
