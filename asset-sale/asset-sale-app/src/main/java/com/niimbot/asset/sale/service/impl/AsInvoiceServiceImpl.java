package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsInvoiceMapper;
import com.niimbot.asset.sale.model.AsInvoice;
import com.niimbot.asset.sale.service.AsInvoiceService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 发票信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Service
public class AsInvoiceServiceImpl extends ServiceImpl<AsInvoiceMapper, AsInvoice> implements AsInvoiceService {

}
