package com.niimbot.asset.sale.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.model.AsAddress;
import com.niimbot.asset.sale.service.AsAddressService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/12/28 16:12
 */
@RestController
@RequestMapping("server/sale/address")
@RequiredArgsConstructor
public class AsAddressServiceController {

    private final AsAddressService addressService;

    @PostMapping
    public Boolean save(@RequestBody AsAddress address) {
        long count = addressService.count(new LambdaQueryWrapper<AsAddress>()
                .eq(AsAddress::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        // 最多可添加6个发票抬头
        if (count >= 6) {
            throw new BusinessException(SaleResultCode.ADDRESS_MAX_NUM);
        }
        return addressService.save(address);
    }

    @PutMapping
    public Boolean update(@RequestBody AsAddress address) {
        return addressService.updateById(address);
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Long id) {
        return addressService.removeById(id);
    }

    @GetMapping("/list")
    public List<AsAddress> queryList() {
        return addressService.list(
                Wrappers.<AsAddress>lambdaQuery()
                        .orderByDesc(AsAddress::getCreateTime));
    }

    @PutMapping("/default/{id}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefault(@PathVariable("id") Long id) {
        // 全部改为非默认
        this.addressService.update(new LambdaUpdateWrapper<AsAddress>()
                .set(AsAddress::getIsDefault, false)
                .eq(AsAddress::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        // 设置默认
        return this.addressService.update(new LambdaUpdateWrapper<AsAddress>()
                .set(AsAddress::getIsDefault, true)
                .eq(AsAddress::getId, id));
    }

}
