package com.niimbot.asset.sale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.sale.model.AsDiscountConfig;
import com.niimbot.sale.DiscountConfigDto;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/30 下午3:33
 */
public interface AsDiscountConfigMapper extends BaseMapper<AsDiscountConfig> {

    /**
     * 查询优惠策略信息
     * @param discountCodeList
     * @param currentTime
     * @return
     */
    List<DiscountConfigDto> selectDiscountConfig(@Param("discountCodeList") List<String> discountCodeList,
                                                 @Param("currentTime") LocalDateTime currentTime);
}