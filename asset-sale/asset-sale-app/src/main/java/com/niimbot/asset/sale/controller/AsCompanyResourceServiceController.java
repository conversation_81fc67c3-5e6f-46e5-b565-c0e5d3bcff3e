package com.niimbot.asset.sale.controller;

import com.niimbot.asset.sale.service.AsCompanyResourceService;
import com.niimbot.asset.sale.service.ResourceConfigService;
import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.sale.CompanyResourceDto;
import com.niimbot.sale.ResourcePackDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/12/8 14:45
 */
@RestController
@RequestMapping("server/sale/resource")
@RequiredArgsConstructor
public class AsCompanyResourceServiceController {

    private final AsCompanyResourceService companyResourceService;
    private final ResourceConfigService resourceConfigService;

    @GetMapping("/company/capacity")
    public CompanyResourceCapacityDto getCapacity() {
        return companyResourceService.getCapacity();
    }

    @GetMapping("/resourcePack")
    public List<ResourcePackDto> listPack() {
        return resourceConfigService.listPack();
    }

    @GetMapping("/resourcePack/{id}")
    public ResourcePackDto info(@PathVariable("id") Long id) {
        return resourceConfigService.info(id);
    }

    @GetMapping("/company/capacity/detail")
    public List<CompanyResourceDto> getCapacityDetail() {
        return companyResourceService.getCapacityDetail();
    }

}
