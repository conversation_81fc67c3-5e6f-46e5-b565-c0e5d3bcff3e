package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsGoodsSkuMapper;
import com.niimbot.asset.sale.model.AsGoodsSku;
import com.niimbot.asset.sale.service.AsGoodsSkuService;
import com.niimbot.sale.GoodsInfoDto;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 商品SKU 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
public class AsGoodsSkuServiceImpl extends ServiceImpl<AsGoodsSkuMapper, AsGoodsSku> implements AsGoodsSkuService {

    @Override
    public GoodsInfoDto getSkuOne(Integer type) {
        List<GoodsInfoDto> sku = this.getBaseMapper().getSku(type);
        if (CollUtil.isNotEmpty(sku)) {
            return sku.get(0);
        }
        return null;
    }

    @Override
    public List<GoodsInfoDto> getSkuList(Integer type) {
        return this.getBaseMapper().getSku(type);
    }
}
