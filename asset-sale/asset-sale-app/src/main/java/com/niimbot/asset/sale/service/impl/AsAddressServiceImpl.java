package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsAddressMapper;
import com.niimbot.asset.sale.model.AsAddress;
import com.niimbot.asset.sale.service.AsAddressService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 邮寄地址 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class AsAddressServiceImpl extends ServiceImpl<AsAddressMapper, AsAddress> implements AsAddressService {

}
