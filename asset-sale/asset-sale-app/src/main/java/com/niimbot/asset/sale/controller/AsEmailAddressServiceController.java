package com.niimbot.asset.sale.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.model.AsEmailAddress;
import com.niimbot.asset.sale.service.AsEmailAddressService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/12/28 16:12
 */
@RestController
@RequestMapping("server/sale/emailAddress")
@RequiredArgsConstructor
public class AsEmailAddressServiceController {

    private final AsEmailAddressService emailAddressService;

    @PostMapping
    public Boolean save(@RequestBody AsEmailAddress address) {
        long count = emailAddressService.count(new LambdaQueryWrapper<AsEmailAddress>()
                .eq(AsEmailAddress::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        // 最多可添加6个发票抬头
        if (count >= 6) {
            throw new BusinessException(SaleResultCode.EMAIL_ADDRESS_MAX_NUM);
        }
        return emailAddressService.save(address);
    }

    @PutMapping
    public Boolean update(@RequestBody AsEmailAddress address) {
        return emailAddressService.updateById(address);
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Long id) {
        return emailAddressService.removeById(id);
    }

    @GetMapping("/list")
    public List<AsEmailAddress> queryList() {
        return emailAddressService.list(
                Wrappers.<AsEmailAddress>lambdaQuery()
                        .orderByDesc(AsEmailAddress::getCreateTime));
    }

    @PutMapping("/default/{id}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefault(@PathVariable("id") Long id) {
        // 全部改为非默认
        this.emailAddressService.update(new LambdaUpdateWrapper<AsEmailAddress>()
                .set(AsEmailAddress::getIsDefault, false)
                .eq(AsEmailAddress::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        // 设置默认
        return this.emailAddressService.update(new LambdaUpdateWrapper<AsEmailAddress>()
                .set(AsEmailAddress::getIsDefault, true)
                .eq(AsEmailAddress::getId, id));
    }

}
