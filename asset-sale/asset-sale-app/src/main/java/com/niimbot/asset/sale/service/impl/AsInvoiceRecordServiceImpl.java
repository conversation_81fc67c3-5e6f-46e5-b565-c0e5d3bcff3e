package com.niimbot.asset.sale.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsInvoiceRecordMapper;
import com.niimbot.asset.sale.model.AsInvoiceRecord;
import com.niimbot.asset.sale.service.AsInvoiceRecordService;
import com.niimbot.sale.InvoiceRecordDto;
import com.niimbot.sale.InvoiceRecordQueryDto;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发票记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Service
public class AsInvoiceRecordServiceImpl extends ServiceImpl<AsInvoiceRecordMapper, AsInvoiceRecord> implements AsInvoiceRecordService {
    @Override
    public IPage<InvoiceRecordDto> page(InvoiceRecordQueryDto query) {
        return this.getBaseMapper().page(query.buildIPage(), query);
    }
}
