package com.niimbot.asset.sale.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.model.AsInvoice;
import com.niimbot.asset.sale.service.AsInvoiceService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * 发票管理
 *
 * <AUTHOR>
 * @date 2021/10/20 11:24
 */
@RestController
@RequestMapping("server/sale/invoice")
@RequiredArgsConstructor
public class AsInvoiceServiceController {
    private final AsInvoiceService invoiceService;

    @PostMapping
    public Boolean save(@RequestBody AsInvoice invoice) {
        long count = invoiceService.count(new LambdaQueryWrapper<AsInvoice>().eq(AsInvoice::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        // 最多可添加6个发票抬头
        if (count >= 6) {
            throw new BusinessException(SaleResultCode.INVOICE_MAX_NUM);
        }
        return invoiceService.save(invoice);
    }

    @PutMapping
    public Boolean update(@RequestBody AsInvoice invoice) {
        return invoiceService.updateById(invoice);
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Long id) {
        return invoiceService.removeById(id);
    }

    @GetMapping("/list")
    public List<AsInvoice> queryList() {
        return invoiceService.list(
                Wrappers.<AsInvoice>lambdaQuery()
                        .orderByDesc(AsInvoice::getCreateTime));
    }
}
