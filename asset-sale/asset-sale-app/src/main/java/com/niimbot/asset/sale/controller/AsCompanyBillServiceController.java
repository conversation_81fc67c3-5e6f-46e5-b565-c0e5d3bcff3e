package com.niimbot.asset.sale.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.sale.model.AsCompanyBill;
import com.niimbot.asset.sale.model.AsCompanyBillDetail;
import com.niimbot.asset.sale.service.AsCompanyBillService;
import com.niimbot.sale.CompanyBillQueryDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/12/30 16:24
 */
@Slf4j
@RestController
@RequestMapping("server/sale/bill")
@RequiredArgsConstructor
public class AsCompanyBillServiceController {

    private final AsCompanyBillService companyBillService;

    @GetMapping("/page/all")
    public IPage<AsCompanyBill> pageAll(CompanyBillQueryDto query) {
        return this.companyBillService.pageAll(query);
    }

    @GetMapping("/page/income")
    public IPage<AsCompanyBillDetail> pageIncome(CompanyBillQueryDto query) {
        return this.companyBillService.pageIncome(query);
    }

    @GetMapping("/page/expenses")
    public IPage<AsCompanyBillDetail> pageExpenses(CompanyBillQueryDto query) {
        return this.companyBillService.pageExpenses(query);
    }

}
