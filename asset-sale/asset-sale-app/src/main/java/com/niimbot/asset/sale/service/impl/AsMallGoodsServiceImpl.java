package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsMallGoodsMapper;
import com.niimbot.asset.sale.model.AsMallGoods;
import com.niimbot.asset.sale.service.AsMallGoodsService;
import cn.hutool.core.collection.CollUtil;
import com.niimbot.sale.MallGoodsDto;
import com.niimbot.sale.MallGoodsPageDto;
import com.niimbot.sale.MallGoodsQueryDto;

import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;

import java.util.List;

@RequiredArgsConstructor
@Service
public class AsMallGoodsServiceImpl extends ServiceImpl<AsMallGoodsMapper, AsMallGoods> implements AsMallGoodsService {

    @Override
    public IPage<MallGoodsPageDto> pageQuery(MallGoodsQueryDto query) {
        return this.getBaseMapper().pageQuery(query.buildIPage(), query);
    }

    @Override
    public MallGoodsPageDto detail(Long id) {
        AsMallGoods mallGoods = this.getBaseMapper().selectById(id);
        if (ObjectUtil.isNotNull(mallGoods)) {
            return BeanUtil.copyProperties(mallGoods, MallGoodsPageDto.class);
        } else {
            return new MallGoodsPageDto();
        }

    }

    @Override
    public List<MallGoodsDto> queryBySkuCode(List<String> skuCodeList) {
        if (CollUtil.isEmpty(skuCodeList)) {
            return null;
        }
        return this.getBaseMapper().selectBySkuCode(skuCodeList);
    }

    @Override
    public List<MallGoodsDto> queryHardware() {
        //查询所有硬件商品信息
        return this.getBaseMapper().selectBySkuCode(null);
    }
}
