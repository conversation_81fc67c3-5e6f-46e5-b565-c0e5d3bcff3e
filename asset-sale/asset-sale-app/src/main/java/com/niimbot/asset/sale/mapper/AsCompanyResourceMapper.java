package com.niimbot.asset.sale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.sale.model.AsCompanyResource;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.sale.CompanyResourceDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 公司资源容量 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@EnableDataPerm(excludeMethodName = {"getCapacity"})
public interface AsCompanyResourceMapper extends BaseMapper<AsCompanyResource> {

    CompanyResourceCapacityDto getCapacity(@Param("companyId") Long companyId);

    List<CompanyResourceDto> getCapacityDetail();
}
