package com.niimbot.asset.sale.utils;

import com.niimbot.asset.sale.model.AsCompanyWallet;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/1/5 11:31
 */
public class WalletUtils {

    public static final String CASH_IN = "cash_in";
    public static final String CASH_OUT = "cash_out";


    public static void adjust(AsCompanyWallet wallet, BigDecimal money, String type) {
        if (CASH_IN.equals(type)) {
            cashIn(wallet, money);
        } else if (CASH_OUT.equals(type)) {
            cashOut(wallet, money);
        }
    }

    private static void cashIn(AsCompanyWallet wallet, BigDecimal money) {
        // 欠费
        BigDecimal arrears = wallet.getArrears();
        // 余额
        BigDecimal balance = wallet.getBalance();
        // 计算欠费
        if (arrears.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal surplus = arrears.subtract(money);
            if (surplus.compareTo(BigDecimal.ZERO) > 0) {
                arrears = surplus;
            } else if (surplus.compareTo(BigDecimal.ZERO) == 0) {
                arrears = BigDecimal.ZERO;
            } else if (surplus.compareTo(BigDecimal.ZERO) < 0) {
                arrears = BigDecimal.ZERO;
                balance = balance.add(surplus.abs());
            }
        } else {
            balance = balance.add(money);
        }
        wallet.setArrears(arrears);
        wallet.setBalance(balance);
    }

    private static void cashOut(AsCompanyWallet wallet, BigDecimal money) {
        // 欠费
        BigDecimal arrears = wallet.getArrears();
        // 余额
        BigDecimal balance = wallet.getBalance();
        if (balance.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal surplus = balance.subtract(money);
            if (surplus.compareTo(BigDecimal.ZERO) > 0) {
                balance = surplus;
            } else if (surplus.compareTo(BigDecimal.ZERO) == 0) {
                balance = BigDecimal.ZERO;
            } else if (surplus.compareTo(BigDecimal.ZERO) < 0) {
                balance = BigDecimal.ZERO;
                arrears = arrears.add(surplus.abs());
            }
        } else {
            arrears = arrears.add(money);
        }
        wallet.setArrears(arrears);
        wallet.setBalance(balance);
    }

}
