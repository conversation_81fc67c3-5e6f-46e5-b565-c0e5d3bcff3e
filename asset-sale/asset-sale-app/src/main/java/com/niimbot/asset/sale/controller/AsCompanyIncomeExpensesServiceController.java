package com.niimbot.asset.sale.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.sale.service.AsCompanyIncomeExpensesService;
import com.niimbot.sale.CompanyIncomeExpensesDto;
import com.niimbot.sale.CompanyIncomeExpensesQueryDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/12/30 15:40
 */
@RestController
@RequestMapping("server/sale/incomeExpenses")
@RequiredArgsConstructor
public class AsCompanyIncomeExpensesServiceController {

    private final AsCompanyIncomeExpensesService companyIncomeExpensesService;

    @GetMapping("/page")
    public IPage<CompanyIncomeExpensesDto> page(CompanyIncomeExpensesQueryDto query) {
        return this.companyIncomeExpensesService.queryPage(query);
    }

    @GetMapping("/hasIncomeExpenses")
    public Boolean hasIncomeExpenses() {
        return companyIncomeExpensesService.count() > 0;
    }
}
