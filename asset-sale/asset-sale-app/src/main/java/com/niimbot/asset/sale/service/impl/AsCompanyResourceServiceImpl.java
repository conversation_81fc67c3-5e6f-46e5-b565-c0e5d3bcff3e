package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.mapper.AsCompanyResourceMapper;
import com.niimbot.asset.sale.model.AsCompanyResource;
import com.niimbot.asset.sale.service.AsCompanyResourceService;
import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.sale.CompanyResourceDto;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 公司资源容量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class AsCompanyResourceServiceImpl extends ServiceImpl<AsCompanyResourceMapper, AsCompanyResource> implements AsCompanyResourceService {

    @Override
    public CompanyResourceCapacityDto getCapacity() {
        return getBaseMapper().getCapacity(LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public List<CompanyResourceDto> getCapacityDetail() {
        return getBaseMapper().getCapacityDetail();
    }

}
