package com.niimbot.asset.sale.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.sale.mapper.AsCompanyWalletMapper;
import com.niimbot.asset.sale.model.AsCompanyIncomeExpenses;
import com.niimbot.asset.sale.model.AsCompanyWallet;
import com.niimbot.asset.sale.service.AsCompanyIncomeExpensesService;
import com.niimbot.asset.sale.service.AsCompanyWalletService;
import com.niimbot.sale.CompanyIncomeExpensesDto;
import com.niimbot.sale.CompanyWalletArrearsDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import cn.hutool.core.collection.ListUtil;

/**
 * <p>
 * 企业钱包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
public class AsCompanyWalletServiceImpl extends ServiceImpl<AsCompanyWalletMapper, AsCompanyWallet> implements AsCompanyWalletService {

    private final AsCompanyIncomeExpensesService companyIncomeExpensesService;

    @Autowired
    public AsCompanyWalletServiceImpl(AsCompanyIncomeExpensesService companyIncomeExpensesService) {
        this.companyIncomeExpensesService = companyIncomeExpensesService;
    }

    @Override
    public CompanyWalletArrearsDto getWalletArrearsDto(Long companyId) {
        CompanyWalletArrearsDto arrearsDto = new CompanyWalletArrearsDto();
        AsCompanyWallet wallet = this.getById(companyId);
        if (wallet == null) {
            return arrearsDto;
        }
        // 欠费信息
        arrearsDto.setArrears(wallet.getArrears());

        // 存在欠费信息
        if (arrearsDto.getArrears().compareTo(BigDecimal.ZERO) > 0) {
            AsCompanyIncomeExpenses incomeExpenses = companyIncomeExpensesService.getOne(
                    new LambdaQueryWrapper<AsCompanyIncomeExpenses>()
                            .eq(AsCompanyIncomeExpenses::getBizType, DictConstant.INCOME_EXPENSES_BIZ_TYPE_USE)
                            .orderByDesc(AsCompanyIncomeExpenses::getTradeTime), false);
            if (incomeExpenses != null) {
                CompanyIncomeExpensesDto companyIncomeExpensesDto = new CompanyIncomeExpensesDto()
                        .setTradeTime(incomeExpenses.getTradeTime())
                        .setBizType(incomeExpenses.getBizType())
                        .setBizDesc(incomeExpenses.getBizDesc())
                        .setTradeAmount(incomeExpenses.getTradeAmount());
                arrearsDto.setIncomeExpenses(ListUtil.of(companyIncomeExpensesDto));
            }
        }
        return arrearsDto;
    }
}
