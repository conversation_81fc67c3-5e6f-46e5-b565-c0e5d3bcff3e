package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.enums.DiscountObjectTypeEnum;
import com.niimbot.asset.sale.mapper.AsDiscountConfigMapper;
import com.niimbot.asset.sale.model.AsDiscountConfig;
import com.niimbot.asset.sale.service.DiscountConfigService;

import com.niimbot.asset.system.ots.SystemCompanyOts;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.sale.DiscountCompanyItem;
import com.niimbot.sale.DiscountConditionItem;
import com.niimbot.sale.DiscountConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.collection.CollUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/1 下午1:51
 */
@Service
public class DiscountConfigServiceImpl extends ServiceImpl<AsDiscountConfigMapper, AsDiscountConfig> implements DiscountConfigService {

    @Autowired
    private SystemCompanyOts systemCompanyService;
    @Autowired
    private CompanyService companyService;

    @Override
    public List<DiscountConfigDto> queryDiscountInfo(List<String> discountCodeList, Long companyId) {
        if (CollUtil.isEmpty(discountCodeList) || Objects.isNull(companyId)) {
            return null;
        }

        //查询优惠策略配置
        List<DiscountConfigDto> discountConfigDtoList = this.getBaseMapper().selectDiscountConfig(discountCodeList, LocalDateTime.now());
        if (CollUtil.isEmpty(discountConfigDtoList)) {
            return discountConfigDtoList;
        }

        //企业付费状态
        Integer companyPaymentStatus = systemCompanyService.getCompanyPaymentStatus(companyId);

        //企业规模
        Integer enterpriseSize = companyService.queryEnterpriseSize(companyId);

        //企业标签
        List<String> companyTag = companyService.queryCompanyTag(companyId);

        List<DiscountConfigDto> result = new ArrayList<>();
        for (DiscountConfigDto item : discountConfigDtoList) {
            if (DiscountObjectTypeEnum.ALL.getCode().equals(item.getObjectType())) {
                result.add(item);
            } else if (DiscountObjectTypeEnum.PAYMENT_STATUS.getCode().equals(item.getObjectType())) {
                if (item.getBonusObject().getPaymentStatus().contains(companyPaymentStatus)) {
                    result.add(item);
                }
            } else if (DiscountObjectTypeEnum.ENTERPRISE_SIZE.getCode().equals(item.getObjectType())) {
                //企业规模是钉钉版独有的，目前saas这样写也没有问题，因为saas不会配置
                Optional<DiscountCompanyItem> enterpriseSizeOptional = item.getBonusObject().getEnterpriseSize().stream()
                        .filter(companyItem -> enterpriseSize >= companyItem.getMinValue()
                                && (Objects.isNull(companyItem.getMaxValue()) || enterpriseSize <=companyItem.getMaxValue())).findAny();
                if (enterpriseSizeOptional.isPresent()) {
                    result.add(item);
                }
            } else if (DiscountObjectTypeEnum.COMPANY_TAG.getCode().equals(item.getObjectType())) {
                if (!CollUtil.isEmpty(companyTag) && !Collections.disjoint(companyTag, item.getBonusObject().getCompanyTag())) {
                    result.add(item);
                }
            } else if (DiscountObjectTypeEnum.SPECIFIC_CUSTOMER.getCode().equals(item.getObjectType())) {
                if (item.getBonusObject().getSpecificCustomer().contains(String.valueOf(companyId))) {
                    result.add(item);
                }
            }
        }
        return result;
    }

    @Override
    public Integer queryBonus(List<DiscountConfigDto> discountInfo, Integer quantity) {
        if (CollUtil.isEmpty(discountInfo)) {
            return 0;
        }

        //匹配上的优惠信息
        List<DiscountConfigDto> matchedDiscountInfo = discountInfo.stream().filter(item -> {
            Optional<DiscountConditionItem> conditionItemOptional = item.getBonusCondition().stream()
                    .filter(conditionItem -> conditionItem.getPurchaseYears().equals(quantity)).findAny();
            if (conditionItemOptional.isPresent()) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(matchedDiscountInfo)) {
            return 0;
        }

        DiscountConfigDto discountConfigDto = matchedDiscountInfo.get(0);
        for (DiscountConfigDto item : matchedDiscountInfo) {
            if (DiscountObjectTypeEnum.SPECIFIC_CUSTOMER.getCode().equals(item.getObjectType())) {
                discountConfigDto = item;
            }
        }

        Optional<DiscountConditionItem> discountInfoOptional = discountConfigDto.getBonusCondition().stream()
                .filter(conditionItem -> conditionItem.getPurchaseYears().equals(quantity)).findAny();
        if (discountInfoOptional.isPresent()) {
            return discountInfoOptional.get().getBonusMonth();
        } else {
            return 0;
        }
    }
}
