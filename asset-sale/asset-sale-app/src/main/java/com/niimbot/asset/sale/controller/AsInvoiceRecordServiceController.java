package com.niimbot.asset.sale.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.sale.model.*;
import com.niimbot.asset.sale.service.*;
import com.niimbot.asset.system.dto.CompanyMessageRuleGetQry;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.sale.*;
import com.niimbot.system.DingtalkGroupMessageSendDto;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 发票记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@RestController
@RequestMapping("server/sale/invoiceRecord")
@RequiredArgsConstructor
public class AsInvoiceRecordServiceController {
    private final AsInvoiceRecordService invoiceRecordService;
    private final AsInvoiceSettingService invoiceSettingService;
    private final AsInvoiceService invoiceService;
    private final AsAddressService addressService;
    private final AsEmailAddressService emailAddressService;
    private final AsSaleOrderService saleOrderService;
    private final CacheResourceUtil cacheResourceUtil;
    private final CompanyService companyService;
    private final CompanySettingService companySettingService;
    private final RedissonClient redissonClient;
    private final ThreadPoolTaskExecutor taskExecutor;
    private final AsCusEmployeeService cusEmployeeService;
    private final MessageService messageService;

    @Value("${asset.domain.admin}")
    private String adminDomain;

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean apply(@RequestBody InvoiceRecordApplyDto applyDto) {
        RLock rLock = redissonClient.getLock(
                RedisConstant.saleOrderInvoiceLockKey(LoginUserThreadLocal.getCompanyId()));

        if (rLock.isLocked()) {
            BusinessExceptionUtil.throwException("开票正在申请中, 请稍后重试");
        }
        rLock.lock();
        try {
            // 校验订单是否都是待开票订单
            List<AsSaleOrder> saleOrders = saleOrderService.listByIds(applyDto.getSaleOrders());
            List<AsSaleOrder> waitSaleOrders = saleOrders.stream().filter(o -> o.getInvoiceStatus() != DictConstant.SALE_ORDER_INVOICE_STATUS_WAIT)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(waitSaleOrders)) {
                List<String> orderNos = waitSaleOrders.stream().map(AsSaleOrder::getOrderNo).collect(Collectors.toList());
                BusinessExceptionUtil.throwException("订单" + orderNos + "状态不合法");
            }
            // 校验专票与项目
            InvoiceSettingDto invoiceSettingDto = invoiceSettingService.queryInfo();
            List<String> invoiceProjects = invoiceSettingDto.getInvoiceProjects().stream()
                    .map(InvoiceProjectDto::getName).collect(Collectors.toList());
            if (!invoiceProjects.contains(applyDto.getInvoiceProject())) {
                BusinessExceptionUtil.throwException(String.format("开票项目[%s]不合法", applyDto.getInvoiceProject()));
            }
            // 校验专票
            BigDecimal vatInvoiceMinMoney = BigDecimal.valueOf(1000L);
            if (invoiceSettingDto != null) {
                vatInvoiceMinMoney = invoiceSettingDto.getVatInvoiceMinMoney();
            }
            AsInvoice invoice = invoiceService.getById(applyDto.getInvoiceInfo());
            // 开票总金额
            final BigDecimal invoiceMoney = saleOrders.stream()
                    .map(order -> {
                        if (order.getPayMoney() == null) {
                            return BigDecimal.ZERO;
                        }
                        return order.getPayMoney();
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (invoice.getType() == DictConstant.INVOICE_TYPE_VAT
                    && invoiceMoney.compareTo(vatInvoiceMinMoney) < 0) {
                BusinessExceptionUtil.throwException(String.format("开增值税专票，开票金额需大于等于%.2f元，如有疑问，请联系客服", vatInvoiceMinMoney));
            }
            if (invoice.getType() == DictConstant.INVOICE_TYPE_VAT && applyDto.getInvoiceMedia() != DictConstant.INVOICE_MEDIA_PAPER) {
                BusinessExceptionUtil.throwException("开增值税专票，发票介质只允许选择纸质，如有疑问，请联系客服");
            }
            // 构造发票申请记录
            AsInvoiceRecord invoiceRecord = new AsInvoiceRecord();
            AsInvoiceInfo invoiceInfo = BeanUtil.copyProperties(invoice, AsInvoiceInfo.class);
            invoiceRecord.setInvoiceInfo(invoiceInfo);

            invoiceRecord.setInvoiceProject(applyDto.getInvoiceProject());
            invoiceRecord.setInvoiceMedia(applyDto.getInvoiceMedia());
            invoiceRecord.setSaleOrderIds(applyDto.getSaleOrders());
            invoiceRecord.setInvoiceMoney(invoiceMoney);
            AsAddressInfo addressInfo = new AsAddressInfo();
            if (DictConstant.INVOICE_MEDIA_PAPER == applyDto.getInvoiceMedia()) {
                addressInfo = BeanUtil.copyProperties(addressService.getById(applyDto.getAddressInfo()), AsAddressInfo.class);
            }
            if (DictConstant.INVOICE_MEDIA_ELECTRON == applyDto.getInvoiceMedia()) {
                AsEmailAddress emailAddress = emailAddressService.getById(applyDto.getAddressInfo());
                addressInfo.setId(emailAddress.getId()).setName(emailAddress.getName()).setEmail(emailAddress.getEmail());
            }
            invoiceRecord.setAddressInfo(addressInfo);
            invoiceRecord.setRemark(applyDto.getRemark());
            invoiceRecord.setStatus(DictConstant.INVOICE_STATUS_PROCESSING);
            invoiceRecordService.save(invoiceRecord);

            // 更新订单状态
            saleOrders.forEach(o -> o.setInvoiceStatus(DictConstant.SALE_ORDER_INVOICE_STATUS_PROCESSING));
            saleOrderService.updateBatchById(saleOrders);

            // 发送钉钉群消息
            taskExecutor.submit(() -> {
                sendMessage(invoiceRecord);
            });
        } finally {
            rLock.unlock();
        }
        return true;
    }

    @GetMapping("/page")
    public IPage<InvoiceRecordDto> page(InvoiceRecordQueryDto query) {
        return invoiceRecordService.page(query);
    }

    @ApiOperation(value = "开票记录详情查询")
    @GetMapping("/detail/{id}")
    @AutoConvert
    public InvoiceRecordDetailDto detail(@PathVariable("id") Long id) {
        AsInvoiceRecord invoiceRecord = invoiceRecordService.getById(id);
        BusinessExceptionUtil.checkNotNull(invoiceRecord, "开票记录信息不存在");
        InvoiceRecordDetailDto recordDetailDto = BeanUtil.copyProperties(invoiceRecord, InvoiceRecordDetailDto.class);
        List<SaleOrderDto> saleOrders = saleOrderService.listByIds(invoiceRecord.getSaleOrderIds())
                .stream().map(o -> BeanUtil.copyProperties(o, SaleOrderDto.class)).collect(Collectors.toList());
        recordDetailDto.setSaleOrders(saleOrders);
        AsCusEmployee cusEmployee = cusEmployeeService.getById(invoiceRecord.getCreateBy());
        recordDetailDto.setCreatePhone(cusEmployee.getMobile());
        return recordDetailDto;
    }

    private void sendMessage(AsInvoiceRecord invoiceRecord) {
        AsCompanySetting companySetting = companySettingService.getById(invoiceRecord.getCompanyId());
        if (companySetting.getIsTest()) {
            return;
        }
        MessageRuleCO ddkpRule = messageService.getCompanyMessageRuleByCode(new CompanyMessageRuleGetQry(0L, MessageConstant.Code.DDKP.getCode()));
        if (ddkpRule == null) {
            return;
        }
        AsDingtalkMessageRuleConfig ruleConfig = JSONObject.toJavaObject(ddkpRule.getExtConfig(), AsDingtalkMessageRuleConfig.class);

        DingtalkGroupMessageSendDto messageSendDto = new DingtalkGroupMessageSendDto();
        messageSendDto.setCode(ddkpRule.getCode());
        messageSendDto.setType(ddkpRule.getChannel());
        messageSendDto.setBusinessType(ddkpRule.getBusinessType());
        messageSendDto.setNickname(MessageConstant.Nickname.NIIMBOT_CLOUD_ASSETS);
        messageSendDto.setUrl(ruleConfig.getUrl());
        messageSendDto.setSign(ruleConfig.getSign());

        AsCompany company = companyService.getById(invoiceRecord.getCompanyId());

        Map<String, String> mapParam = new HashMap<>();
        mapParam.put(MessageConstant.Template.COMPANY_NAME, company.getName());
        mapParam.put(MessageConstant.Template.INVOICE_MONEY, String.valueOf(invoiceRecord.getInvoiceMoney()));
        mapParam.put(MessageConstant.Template.INVOICE_TYPE, cacheResourceUtil.getDictLabel("invoice_type", invoiceRecord.getInvoiceInfo().getType().toString()));
        mapParam.put(MessageConstant.Template.INVOICE_MEDIA, cacheResourceUtil.getDictLabel("invoice_media", invoiceRecord.getInvoiceMedia().toString()));
        mapParam.put(MessageConstant.Template.APPLY_DATE, invoiceRecord.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        mapParam.put(MessageConstant.Template.DETAIL_URL, String.format(MessageConstant.Code.DDKP.getUrl(),
                this.adminDomain, invoiceRecord.getId()));
        messageSendDto.setMapParam(mapParam);
        messageService.sendDingMsg(messageSendDto);
    }
}
