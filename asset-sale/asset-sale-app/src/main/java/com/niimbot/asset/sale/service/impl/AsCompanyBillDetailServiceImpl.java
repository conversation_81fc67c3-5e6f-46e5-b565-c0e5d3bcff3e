package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsCompanyBillDetailMapper;
import com.niimbot.asset.sale.model.AsCompanyBillDetail;
import com.niimbot.asset.sale.service.AsCompanyBillDetailService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 企业账单明细（收支账单） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
public class AsCompanyBillDetailServiceImpl extends ServiceImpl<AsCompanyBillDetailMapper, AsCompanyBillDetail> implements AsCompanyBillDetailService {

}
