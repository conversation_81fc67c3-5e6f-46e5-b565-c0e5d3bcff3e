package com.niimbot.asset.sale.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.sale.service.AsMallGoodsService;
import com.niimbot.jf.core.exception.category.BusinessException;
import cn.hutool.core.collection.CollUtil;
import com.niimbot.sale.MallGoodsDto;
import com.niimbot.sale.MallGoodsPageDto;
import com.niimbot.sale.MallGoodsQueryDto;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 商品详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@RestController
@RequestMapping("/server/sale/mallgoods")
@RequiredArgsConstructor
public class AsMallGoodsController {

    private final AsMallGoodsService mallGoodsService;

    @ApiOperation(value = "商品列表")
    @GetMapping("/list")
    public IPage<MallGoodsPageDto> page(@Validated MallGoodsQueryDto mallGoodsQueryDto) {
        return mallGoodsService.pageQuery(mallGoodsQueryDto);
    }

    @ApiOperation(value = "商品详情")
    @GetMapping("/detail/{id}")
    public MallGoodsPageDto detail(@PathVariable("id") Long id) {
        return mallGoodsService.detail(id);
    }

    @GetMapping("/hardware")
    public List<MallGoodsDto> hardwareProduct() {
        return mallGoodsService.queryHardware();
    }

    @GetMapping("/detailBySku/{skuCode}")
    public MallGoodsDto hardwareDetail(@PathVariable("skuCode") String skuCode) {
        List<MallGoodsDto> mallGoodsDtoList = mallGoodsService.queryBySkuCode(Collections.singletonList(skuCode));
        if (CollUtil.isEmpty(mallGoodsDtoList)) {
            throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
        }
        return mallGoodsDtoList.get(0);
    }

}
