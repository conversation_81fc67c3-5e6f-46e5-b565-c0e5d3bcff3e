package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsResourceDiscountMapper;
import com.niimbot.asset.sale.model.AsResourceDiscount;
import com.niimbot.asset.sale.service.ResourceDiscountService;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/1 下午1:56
 */
@Service
public class ResourceDiscountServiceImpl extends ServiceImpl<AsResourceDiscountMapper, AsResourceDiscount> implements ResourceDiscountService {

    @Override
    public List<AsResourceDiscount> queryByResourceCode(List<String> resourceCodeList) {
        return list(Wrappers.lambdaQuery(AsResourceDiscount.class)
                .in(AsResourceDiscount::getResourceCode, resourceCodeList));
    }
}
