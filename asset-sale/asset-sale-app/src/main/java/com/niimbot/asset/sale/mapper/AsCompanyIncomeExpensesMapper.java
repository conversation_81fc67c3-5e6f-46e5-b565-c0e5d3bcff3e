package com.niimbot.asset.sale.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.sale.model.AsCompanyIncomeExpenses;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.sale.CompanyIncomeExpensesDto;
import com.niimbot.sale.CompanyIncomeExpensesQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 企业收支 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@EnableDataPerm
public interface AsCompanyIncomeExpensesMapper extends BaseMapper<AsCompanyIncomeExpenses> {

    IPage<CompanyIncomeExpensesDto> queryPage(@Param("page") Page<Object> buildIPage,
                                              @Param("ew") CompanyIncomeExpensesQueryDto query);

    List<Long> listArrearsOverYear(@Param("companyIds") List<Long> companyIds);
}
