package com.niimbot.asset.sale.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.sale.model.AsCompanyResource;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.model.AsSaleOrderItem;
import com.niimbot.asset.sale.service.AsCompanyResourceService;
import com.niimbot.sale.ResourcePackDto;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 精条订单 <AUTHOR>
 *
 * @since 2021/12/30 10:33
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ResourcePackOrderStrategy implements OrderStrategy {

    private final AsCompanyResourceService companyResourceService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleOrder(AsSaleOrder order, AsSaleOrderItem orderItem) {
        // 幂等处理
        if (companyResourceService.count(Wrappers.lambdaQuery(AsCompanyResource.class)
                .eq(AsCompanyResource::getSaleOrderId, order.getId())) > 0) {
            return;
        }

        String discount = orderItem.getDiscountInfo();
        Integer discountNum = Convert.toInt(discount, 0);
        AsCompanyResource resource = new AsCompanyResource();

        JSONObject product = orderItem.getProduct();
        ResourcePackDto packDto = product.toJavaObject(ResourcePackDto.class);

        // 计算时长
        int monthTime = orderItem.getQuantity() + discountNum;
        LocalDateTime now = LocalDate.now().atTime(0, 0, 0);
        LocalDateTime expirationTime = now.plusMonths(monthTime).withHour(23).withMinute(59).withSecond(59).withNano(0);

        resource.setCompanyId(order.getCompanyId())
                .setSkuCode(orderItem.getSku())
                .setResourceName(orderItem.getName())
                .setCapacity(packDto.getCapacity())
                .setSaleOrderId(order.getId())
                .setExperience(false)
                .setCreateTime(now)
                //这里是用户侧购买的订单，立即生效，所以生效时间为创建时间
                .setEffectiveTime(now)
                .setExpirationTime(expirationTime);
        companyResourceService.save(resource);
    }

}
