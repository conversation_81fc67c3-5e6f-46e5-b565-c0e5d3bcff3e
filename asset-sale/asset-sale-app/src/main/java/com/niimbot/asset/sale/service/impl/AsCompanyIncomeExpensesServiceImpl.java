package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.sale.mapper.AsCompanyIncomeExpensesMapper;
import com.niimbot.asset.sale.model.AsCompanyIncomeExpenses;
import com.niimbot.asset.sale.service.AsCompanyIncomeExpensesService;
import com.niimbot.sale.CompanyIncomeExpensesDto;
import com.niimbot.sale.CompanyIncomeExpensesQueryDto;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 企业收支 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Service
public class AsCompanyIncomeExpensesServiceImpl extends ServiceImpl<AsCompanyIncomeExpensesMapper, AsCompanyIncomeExpenses> implements AsCompanyIncomeExpensesService {

    @Override
    public IPage<CompanyIncomeExpensesDto> queryPage(CompanyIncomeExpensesQueryDto query) {
        return this.getBaseMapper().queryPage(query.buildIPage(), query);
    }

    @Override
    public List<Long> listArrearsOverYear(List<Long> companyIds) {
        return this.getBaseMapper().listArrearsOverYear(companyIds);
    }
}
