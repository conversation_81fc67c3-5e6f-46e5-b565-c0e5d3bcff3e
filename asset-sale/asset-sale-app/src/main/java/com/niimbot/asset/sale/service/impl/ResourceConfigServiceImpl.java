package com.niimbot.asset.sale.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.enums.DiscountObjectTypeEnum;
import com.niimbot.asset.sale.mapper.AsResourceConfigMapper;
import com.niimbot.asset.sale.model.AsResourceConfig;
import com.niimbot.asset.sale.model.AsResourceDiscount;
import com.niimbot.asset.sale.service.AsCompanyResourceService;
import com.niimbot.asset.sale.service.DiscountConfigService;
import com.niimbot.asset.sale.service.ResourceConfigService;
import com.niimbot.asset.sale.service.ResourceDiscountService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsConfig;
import com.niimbot.asset.system.service.AsConfigService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.sale.DiscountConfigDto;
import com.niimbot.sale.ResourcePackDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/11/30 下午4:09
 */
@Slf4j
@Service
public class ResourceConfigServiceImpl extends ServiceImpl<AsResourceConfigMapper, AsResourceConfig> implements ResourceConfigService {

    private final AsCompanyResourceService companyResourceService;

    private final DiscountConfigService discountConfigService;

    private final ResourceDiscountService resourceDiscountService;

    private final AsConfigService configService;

    private final CompanyService companyService;

    @Autowired
    public ResourceConfigServiceImpl(AsCompanyResourceService companyResourceService,
                                     DiscountConfigService discountConfigService,
                                     ResourceDiscountService resourceDiscountService,
                                     AsConfigService configService,
                                     CompanyService companyService) {
        this.companyResourceService = companyResourceService;
        this.discountConfigService = discountConfigService;
        this.resourceDiscountService = resourceDiscountService;
        this.configService = configService;
        this.companyService = companyService;
    }

    @Override
    public List<ResourcePackDto> listPack() {
        // 判断当前
        AsConfig releaseTimeConfig = configService.getOne(Wrappers.lambdaQuery(AsConfig.class)
                .eq(AsConfig::getConfigKey, "ding_inside_pay"));

        boolean insidePack = true;
        if (releaseTimeConfig != null) {
            try {
                LocalDateTime releaseTime = LocalDateTime.parse(releaseTimeConfig.getConfigValue(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                AsCompany company = companyService.getById(LoginUserThreadLocal.getCompanyId());
                LocalDateTime companyCreateTime = company.getCreateTime();
                // 企业创建时间比当前需求上线时间早，则视为需要内购
                if (companyCreateTime == null || companyCreateTime.compareTo(releaseTime) > 0) {
                    insidePack = false;
                }
            } catch (Exception e) {
                log.error("parse datetime {} error", releaseTimeConfig.getConfigValue(), e);
            }
        }

        List<ResourcePackDto> resourcePacks = new ArrayList<>();
        if (insidePack) {
            // 全部资源包
            resourcePacks = queryResourcePack(null);
            // 当前公司容量
            CompanyResourceCapacityDto capacity = companyResourceService.getCapacity();
            Integer remainder = capacity.getRemainder();
            if (remainder < 0) {
                for (ResourcePackDto resourcePack : resourcePacks) {
                    if (resourcePack.getCapacity() < Math.abs(remainder)) {
                        resourcePack.setCanBuy(false);
                    }
                }
            }
        }
        return resourcePacks;
    }

    /**
     * 获取软件资源包以及优惠信息
     * @param id
     * @return
     */
    private List<ResourcePackDto> queryResourcePack(Long id) {
        List<ResourcePackDto> result = getBaseMapper().listPack(id);
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        //设置优惠策略信息
        assembleDiscountInfo(result);
        return result;
    }

    /**
     * 组装优惠策略信息
     * @param result
     */
    private void assembleDiscountInfo(List<ResourcePackDto> result) {
        //资源包编码列表
        List<String> resourceCodeList = result.stream().map(ResourcePackDto::getResourceCode).collect(Collectors.toList());

        //查询资源包-优惠策略关联关系
        List<AsResourceDiscount> resourceDiscountList = resourceDiscountService.queryByResourceCode(resourceCodeList);
        //关联关系为空的话就是没有优惠策略，直接返回
        if (CollUtil.isEmpty(resourceDiscountList)) {
            return ;
        }

        //资源包-优惠策略
        Map<String, List<String>> resourceDiscountMap = resourceDiscountList.stream()
                .collect(Collectors.groupingBy(AsResourceDiscount::getResourceCode,
                        Collectors.mapping(AsResourceDiscount::getDiscountCode, Collectors.toList())));

        //优惠策略编码列表
        List<String> discountCodeList = resourceDiscountMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());

        //查询优惠策略信息
        List<DiscountConfigDto> discountInfo = discountConfigService.queryDiscountInfo(discountCodeList, LoginUserThreadLocal.getCompanyId());
        if (CollUtil.isEmpty(discountInfo)) {
            return ;
        }
        //优惠策略分组
        Map<String, DiscountConfigDto> discountInfoMap = discountInfo.stream()
                .collect(Collectors.toMap(DiscountConfigDto::getBizCode, value -> value, (v1, v2) -> v2));

        //为每个资源包设置优惠策略
        for (ResourcePackDto item : result) {
            if (CollUtil.isNotEmpty(resourceDiscountMap.get(item.getResourceCode()))) {
                //当前资源包的优惠策略
                List<DiscountConfigDto> discountConfigDtoList = resourceDiscountMap.get(item.getResourceCode()).stream()
                        .map(discountInfoMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                item.setDiscountInfo(discountConfigDtoList);

                //处理含有指定客户的
                if (CollUtil.isNotEmpty(discountConfigDtoList)) {
                    List<DiscountConfigDto> specificCustomerDiscount = new ArrayList<>();
                    for (DiscountConfigDto discountConfigDto : discountConfigDtoList) {
                        //优先只返回指定客户的
                        if (DiscountObjectTypeEnum.SPECIFIC_CUSTOMER.getCode().equals(discountConfigDto.getObjectType())) {
                            specificCustomerDiscount.add(discountConfigDto);
                        }
                    }

                    //指定客户的优惠策略可能存在多个，并且相互之间没有冲突
                    if (CollUtil.isNotEmpty(specificCustomerDiscount)) {
                        item.setDiscountInfo(specificCustomerDiscount);
                    }
                }
            }
        }
    }


    @Override
    public ResourcePackDto info(Long id) {
        // 全部资源包
        List<ResourcePackDto> resourcePacks = queryResourcePack(id);
        if (CollUtil.isEmpty(resourcePacks)) {
            return null;
        }
        ResourcePackDto resourcePack = resourcePacks.get(0);
        // 当前公司容量
        CompanyResourceCapacityDto capacity = companyResourceService.getCapacity();
        Integer remainder = capacity.getRemainder();
        if (remainder < 0) {
            if (resourcePack.getCapacity() < Math.abs(remainder)) {
                resourcePack.setCanBuy(false);
            }
        }
        return resourcePack;
    }


    @Override
    public ResourcePackDto getPackInfo(Long id) {
        ResourcePackDto packInfo = info(id);
        if (ObjectUtil.isNull(packInfo)) {
            throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
        }
        if (BooleanUtil.isFalse(packInfo.getCanBuy())) {
            throw new BusinessException(SaleResultCode.SALE_OPERATE_FAIL, "当前资源包无法购买，请订购其他资源包");
        }
        return packInfo;
    }

    @Override
    public List<ResourcePackDto> queryResourceInfo(List<String> skuCodeList) {
        //根据skuCode批量查询资源包信息
        List<ResourcePackDto> resourcePackDtoList = this.getBaseMapper().selectBySkuCode(skuCodeList);
        if (CollUtil.isEmpty(resourcePackDtoList)) {
            return resourcePackDtoList;
        }

        //设置优惠策略信息
        assembleDiscountInfo(resourcePackDtoList);

        //当前公司容量
        CompanyResourceCapacityDto capacity = companyResourceService.getCapacity();
        Integer remainder = capacity.getRemainder();

        //当前企业资源包用超的情况下，只允许购买容量大于超出容量的资源包
        if (remainder < 0) {
            Integer summaryCapacity = resourcePackDtoList.stream().map(ResourcePackDto::getCapacity)
                    .reduce(0, Integer::sum);
            if (summaryCapacity < Math.abs(remainder)) {
                throw new BusinessException(SaleResultCode.SALE_OPERATE_FAIL, "当前资源包无法购买，请订购其他资源包");
            }
        }
        return resourcePackDtoList;
    }
}
