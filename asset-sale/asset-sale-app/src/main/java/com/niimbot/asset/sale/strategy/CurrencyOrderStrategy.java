package com.niimbot.asset.sale.strategy;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.sale.model.AsCompanyIncomeExpenses;
import com.niimbot.asset.sale.model.AsCompanyWallet;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.model.AsSaleOrderItem;
import com.niimbot.asset.sale.service.AsCompanyIncomeExpensesService;
import com.niimbot.asset.sale.service.AsCompanyWalletService;
import com.niimbot.asset.sale.utils.WalletUtils;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.CompanySettingService;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import lombok.extern.slf4j.Slf4j;

/**
 * 精条订单 <AUTHOR>
 *
 * @since 2021/12/30 10:33
 */
@Slf4j
@Component
public class CurrencyOrderStrategy implements OrderStrategy {

    private final RedissonClient redissonClient;

    private final AsCompanyIncomeExpensesService companyIncomeExpensesService;

    private final AsCompanyWalletService companyWalletService;

    private final CompanyService companyService;

    private final CompanySettingService companySettingService;

    @Autowired
    public CurrencyOrderStrategy(RedissonClient redissonClient,
                                 AsCompanyIncomeExpensesService companyIncomeExpensesService,
                                 AsCompanyWalletService companyWalletService,
                                 CompanyService companyService,
                                 CompanySettingService companySettingService) {
        this.redissonClient = redissonClient;
        this.companyIncomeExpensesService = companyIncomeExpensesService;
        this.companyWalletService = companyWalletService;
        this.companyService = companyService;
        this.companySettingService = companySettingService;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleOrder(AsSaleOrder order, AsSaleOrderItem orderItem) {
        Long companyId = order.getCompanyId();
        RLock rLock = redissonClient.getLock(
                RedisConstant.companyWalletLockKey(companyId));
        rLock.lock();
        try {
            // 查询企业钱包
            AsCompanyWallet wallet = companyWalletService.getById(companyId);
            if (wallet == null) {
                log.error("企业{}钱包不存在，订单数据{}, 订单详情数据{}", companyId, order.toString(), orderItem.toString());
                return;
            }
            // 金额
            BigDecimal tradeAmount = new BigDecimal(orderItem.getQuantity());
            WalletUtils.adjust(wallet, tradeAmount, WalletUtils.CASH_IN);
            // 写入收支明细
            AsCompanyIncomeExpenses companyIncomeExpenses = new AsCompanyIncomeExpenses();
            companyIncomeExpenses.setType(DictConstant.INCOME_EXPENSES_TYPE_INCOME)
                    .setBizType(DictConstant.INCOME_EXPENSES_BIZ_TYPE_BUY)
                    .setBizDesc("购入精条")
                    .setCompanyId(companyId)
                    .setTradeTime(order.getOperateTime())
                    .setTradeAmount(tradeAmount)
                    .setSaleOrderId(order.getId())
                    .setSaleOrderNo(order.getOrderNo())
                    .setBalance(wallet.getBalance())
                    .setArrears(wallet.getArrears());
            companyIncomeExpensesService.save(companyIncomeExpenses);
            // 更新企业钱包
            companyWalletService.updateById(wallet);
            // 更新企业isPay
            companySettingService.update(new LambdaUpdateWrapper<AsCompanySetting>()
                    .set(AsCompanySetting::getIsPay, true)
                    .eq(AsCompanySetting::getCompanyId, companyId));
            // 更新企业状态
            if (wallet.getBalance().compareTo(BigDecimal.ZERO) > 0) {
                companyService.update(new LambdaUpdateWrapper<AsCompany>()
                        .set(AsCompany::getStatus, DictConstant.COMPANY_STATUS_NORMAL)
                        .eq(AsCompany::getId, companyId));
                // 删除企业状态缓存
                companyService.cleanCompanyStatusCache(companyId);
            }
        } finally {
            rLock.unlock();
        }
    }

}
