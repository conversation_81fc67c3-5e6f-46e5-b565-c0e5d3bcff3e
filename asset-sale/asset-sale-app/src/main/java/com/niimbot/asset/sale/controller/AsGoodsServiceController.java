package com.niimbot.asset.sale.controller;

import com.niimbot.asset.sale.service.AsGoodsSkuService;
import com.niimbot.sale.GoodsInfoDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/12/29 14:22
 */
@RestController
@RequestMapping("server/sale/goods")
@RequiredArgsConstructor
public class AsGoodsServiceController {

    private final AsGoodsSkuService goodsSkuService;

    @GetMapping("/sku/one")
    public GoodsInfoDto getSkuOne(@RequestParam("type") Integer type) {
        return this.goodsSkuService.getSkuOne(type);
    }

    @GetMapping("/sku/list")
    public List<GoodsInfoDto> getSkuList(@RequestParam("type") Integer type) {
        return this.goodsSkuService.getSkuList(type);
    }
}
