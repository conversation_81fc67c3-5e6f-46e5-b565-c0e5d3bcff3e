<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectRangeProjectMapper">


    <delete id="deleteByRangeId">
        DELETE
        FROM as_equipment_site_inspect_range_project
        WHERE range_id = #{rangeId}
    </delete>

    <delete id="deleteByProjectId">
        DELETE
        FROM as_equipment_site_inspect_range_project
        WHERE project_id = #{projectId}
    </delete>

    <delete id="deleteByPlanId">
        DELETE
        FROM as_equipment_site_inspect_range_project
        WHERE range_id IN (SELECT id FROM as_equipment_site_inspect_range WHERE plan_id = #{planId})
    </delete>

</mapper>