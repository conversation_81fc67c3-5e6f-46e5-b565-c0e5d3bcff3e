<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSparePartsMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.equipment.model.AsEquipmentSpareParts">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="asset_id" jdbcType="BIGINT" property="assetId" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="install_location" jdbcType="VARCHAR" property="installLocation" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, asset_id, material_id, num, install_location, is_delete, create_by, 
    create_time, update_by, update_time
  </sql>
</mapper>