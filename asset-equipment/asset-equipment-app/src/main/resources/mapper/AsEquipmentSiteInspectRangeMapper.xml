<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectRangeMapper">

    <resultMap id="EntSntRangeResultMap" type="com.niimbot.equipment.EntSntRange">
        <id column="id" property="id"/>
        <result column="plan_id" property="planId"/>
        <result column="data_id" property="dataId"/>
        <result column="data_type" property="dataType"/>
        <result column="sort" property="sort"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="location" property="location"/>
    </resultMap>

    <delete id="deleteByPlanId">
        DELETE
        FROM as_equipment_site_inspect_range
        WHERE plan_id = #{planId}
    </delete>

    <select id="selectPageSearch" resultMap="EntSntRangeResultMap">
        SELECT t20.*
        FROM (
                 SELECT t1.id,
                        t1.plan_id,
                        t1.data_id,
                        t1.data_type,
                        t1.sort,
                        t1.create_by,
                        t1.create_time,
                        t2.asset_code AS `code`,
                        t2.asset_name AS `name`,
                        CONCAT
                            (
                                IF
                                    (
                                        (t2.asset_data ->> '$.storageLocation' IS NOT NULL AND t2.asset_data ->> '$.storageLocation' != '' AND t2.asset_data ->> '$.storageLocation' != 'null'),
                                        CONCAT(t2.asset_data ->> '$.storageLocation', '&lt;'),
                                        ''
                                    ),
                                t3.area_name,
                                '（',
                                t3.area_code,
                                '）'
                            )         AS `location`
                 FROM as_equipment_site_inspect_range AS t1
                          LEFT JOIN as_asset t2 ON t1.data_id = t2.id
                          LEFT JOIN as_area t3 ON CONVERT(t2.storage_area, DECIMAL (19)) = t3.id
                 WHERE t1.company_id = #{em.companyId}
                   AND t1.plan_id = #{em.planId}
                   AND t1.data_type = 1
                   AND t1.is_delete = 0
                   AND t2.company_id = #{em.companyId}
                   AND t3.company_id = #{em.companyId}
                 UNION ALL
                 SELECT t7.id,
                        t7.plan_id,
                        t7.data_id,
                        t7.data_type,
                        t7.sort,
                        t7.create_by,
                        t7.create_time,
                        t8.point_code AS `code`,
                        t8.point_name AS `name`,
                        CONCAT
                            (
                                t8.specific_location,
                                '&lt;',
                                t9.area_name,
                                '（',
                                t9.area_code,
                                '）'
                            )         AS `location`
                 FROM as_equipment_site_inspect_range AS t7
                          LEFT JOIN as_equipment_site_inspect_point t8 ON t7.data_id = t8.id
                          LEFT JOIN as_area t9 ON t8.pid = t9.id
                 WHERE t7.company_id = #{em.companyId}
                   AND t7.plan_id = #{em.planId}
                   AND t7.is_delete = 0
                   AND t8.company_id = #{em.companyId}
                   AND t9.company_id = #{em.companyId}
             ) AS t20
        ORDER BY t20.sort ASC, t20.create_time DESC
    </select>

    <select id="selectEntLocation" resultType="com.niimbot.equipment.EntSntRangeLocation">
        SELECT t1.asset_code AS `code`,
               t1.asset_name AS `name`,
               CONCAT
                   (
                       IF
                           (
                               (t1.asset_data ->> '$.storageLocation' IS NOT NULL AND t1.asset_data ->> '$.storageLocation' != '' AND t1.asset_data ->> '$.storageLocation' != 'null'),
                               CONCAT(t1.asset_data ->> '$.storageLocation', '&lt;'),
                               ''
                           ),
                       t2.area_name,
                       '（',
                       t2.area_code,
                       '）'
                   )         AS `location`
        FROM as_asset t1
                 LEFT JOIN as_area t2 ON CONVERT(t1.storage_area, DECIMAL (19)) = t2.id
        WHERE t1.id = #{dataId}
    </select>

    <select id="selectPointLocation" resultType="com.niimbot.equipment.EntSntRangeLocation">
        SELECT t1.point_code AS `code`,
               t1.point_name AS `name`,
               CONCAT
                   (
                       t1.specific_location,
                       '&lt;',
                       t2.area_name,
                       '（',
                       t2.area_code,
                       '）'
                   )         AS `location`
        FROM as_equipment_site_inspect_point t1
                 LEFT JOIN as_area t2 ON t1.pid = t2.id
        WHERE t1.id = #{dataId}
    </select>

</mapper>