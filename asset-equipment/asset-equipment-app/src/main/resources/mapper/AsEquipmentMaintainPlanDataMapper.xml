<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanDataMapper">

    <select id="selectPlanIdByKw" resultType="java.lang.Long">
        SELECT
        t8.plan_id
        FROM
        (
            SELECT
            t1.plan_id
            FROM
            as_equipment_maintain_plan_data t1 LEFT JOIN as_asset t2 ON t1.data_id = t2.id
            WHERE
            t1.company_id = #{companyId} AND t1.data_type = 1 AND t2.company_id = #{companyId}
            AND ( t2.asset_data ->> '$.assetCode' LIKE CONCAT('%', #{kw}, '%') OR t2.asset_data ->> '$.assetName' LIKE CONCAT('%', #{kw}, '%') )

            UNION ALL

            SELECT
            t3.plan_id
            FROM
            as_equipment_maintain_plan_data t3
            LEFT JOIN as_category t4 ON t3.data_id = t4.id
            LEFT JOIN as_asset t5 ON t4.id = CONVERT(t5.asset_category, DECIMAL(19))
            WHERE
            t3.company_id = #{companyId} AND t4.company_id = #{companyId} AND t5.company_id = #{companyId}
            AND ( t5.asset_data ->> '$.assetCode' LIKE CONCAT('%', #{kw}, '%') OR t5.asset_data ->> '$.assetName' LIKE CONCAT('%', #{kw}, '%') )
        ) AS t8
    </select>

    <select id="selectPlanIdKeyword1" resultType="java.lang.Long">
        SELECT
            DISTINCT t1.plan_id
        FROM
            as_equipment_maintain_plan_data t1 LEFT JOIN as_asset t2 ON t1.data_id = t2.id
        WHERE
            t1.company_id = #{companyId} AND t1.data_type = 1 AND t2.company_id = #{companyId}
          AND ( t2.asset_data ->> '$.assetCode' LIKE CONCAT('%', #{kw}, '%') OR t2.asset_data ->> '$.assetName' LIKE CONCAT('%', #{kw}, '%') )
    </select>

    <select id="selectPlanIdKeyword2" resultType="java.lang.Long">
        SELECT
            DISTINCT t3.plan_id
        FROM
            as_equipment_maintain_plan_data t3
                LEFT JOIN as_category t4 ON t3.data_id = t4.id
                LEFT JOIN as_asset t5 ON t4.id = CONVERT(t5.asset_category, DECIMAL(19))
        WHERE
            t3.company_id = #{companyId} AND t4.company_id = #{companyId} AND t5.company_id = #{companyId}
          AND ( t5.asset_data ->> '$.assetCode' LIKE CONCAT('%', #{kw}, '%') OR t5.asset_data ->> '$.assetName' LIKE CONCAT('%', #{kw}, '%') )
    </select>

</mapper>