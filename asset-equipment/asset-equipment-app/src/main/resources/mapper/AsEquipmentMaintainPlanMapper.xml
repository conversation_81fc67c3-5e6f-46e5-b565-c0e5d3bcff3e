<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanMapper">

    <resultMap id="EntMatPlanResultMap" type="com.niimbot.equipment.EntMatPlan">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="range_type" property="rangeType"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_status" property="planStatus"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="spare_part_count" property="sparePartCount"/>
        <result column="plan_content" property="planContent"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="plan_data" property="planData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <resultMap id="AsEntMatPlanResultMap" type="com.niimbot.asset.equipment.model.AsEquipmentMaintainPlan">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="range_type" property="rangeType"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_status" property="planStatus"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="plan_content" property="planContent"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="plan_data" property="planData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <sql id="selectCondition">

        <!-- 关键字: 保养计划编码||名称 资产资产编码||名称 -->
        <if test="ew.kw != null and ew.kw != ''">
            AND
            (
                t1.plan_no LIKE CONCAT('%', #{ew.kw}, '%')
                OR
                t1.plan_data ->> '$.planName' LIKE CONCAT('%', #{ew.kw}, '%')
                <if test="ew.keywordsPlanIds != null and ew.keywordsPlanIds.size() > 0">
                    OR t1.id IN
                    <foreach collection="ew.keywordsPlanIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            )
        </if>

        <!-- ID -->
        <if test="ew.includePlanIds != null and ew.includePlanIds.size() > 0">
            AND t1.id IN
            <foreach collection="ew.includePlanIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ew.excludePlanIds != null and ew.excludePlanIds.size() > 0">
            AND t1.id NOT IN
            <foreach collection="ew.excludePlanIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <!-- 状态 -->
        <if test="ew.includePlanStatus != null and ew.includePlanStatus.size() > 0">
            AND t1.plan_status IN
            <foreach collection="ew.includePlanStatus" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        <if test="ew.excludePlanStatus != null and ew.excludePlanStatus.size() > 0">
            AND t1.plan_status NOT IN
            <foreach collection="ew.excludePlanStatus" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>

        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>

    </sql>

    <select id="selectPageSearch" resultMap="EntMatPlanResultMap">
        SELECT
            t1.id,
            t1.company_id,
            t1.plan_no,
            t1.plan_status,
            t1.range_type,
            t1.plan_data,
            t1.plan_content,
            t1.create_by,
            t1.create_time,
            t1.update_by,
            t1.update_time,
        ( SELECT COUNT(1) FROM as_equipment_maintain_plan_data
        WHERE data_type = 2 AND plan_id = t1.id AND company_id = t1.company_id) AS spare_part_count
        FROM
            as_equipment_maintain_plan AS t1
        WHERE
            t1.is_delete = 0
        <include refid="selectCondition"/>
    </select>

    <select id="selectByMaintainUser" resultMap="AsEntMatPlanResultMap">
        SELECT
        *
        FROM
        as_equipment_maintain_plan
        WHERE is_delete = 0 AND CAST(plan_data ->> '$.maintainUser' as DECIMAL(19)) = #{maintainUserId}
    </select>

</mapper>