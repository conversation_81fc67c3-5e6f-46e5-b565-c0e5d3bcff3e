<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskMapper">

    <resultMap id="SiteInspectTaskPageDto" type="com.niimbot.equipment.SiteInspectTaskPageDto">
        <id column="id" property="id"/>
        <result column="task_status" property="taskStatus"/>
        <result column="task_name" property="taskName"/>
        <result column="task_no" property="taskNo"/>
        <result column="managers" property="managers"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="executors" property="executors"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="task_type" property="taskType"/>
        <result column="inspect_num" property="inspectNum"/>
        <result column="abnormal_num" property="abnormalNum"/>
        <result column="create_time" property="createTime"/>
        <result column="plan_finish_time" property="planFinishTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="cancel_time" property="cancelTime"/>
    </resultMap>

    <select id="taskPage" resultMap="SiteInspectTaskPageDto">
    SELECT
        id,
        task_status,
        task_name,
        task_no,
        managers,
        executors,
        task_type,
        inspect_num,
        abnormal_num,
        create_time,
        plan_finish_time,
        finish_time,
        cancel_time
        FROM
        as_equipment_site_inspect_task
        <where>
            <if test="currentUserId != null">
                AND (
                JSON_CONTAINS(managers, JSON_ARRAY(#{currentUserId}))
                or
                JSON_CONTAINS(executors, JSON_ARRAY(#{currentUserId}))
                )
            </if>
            <if test="ew.kw!=null and ew.kw!=''">
                AND (
                task_name like concat('%',#{ew.kw},'%')
                OR task_no like concat('%',#{ew.kw},'%')
                )
            </if>
            <if test="ew.taskStatus!=null and ew.taskStatus.size() > 0">
                and task_status in
                <foreach collection="ew.taskStatus" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="specialStatus!=null and specialStatus == '1'.toString()">
                and NOW() &lt;= plan_finish_time
            </if>
            <if test="specialStatus!=null and specialStatus == '3'.toString()">
                and NOW() &gt;= plan_finish_time
            </if>
            <if test="ew.taskType!=null and ew.taskType!=''">
                and task_type = #{ew.taskType}
            </if>
            <if test="ew.inspectNumMin!=null">
                and inspect_num &gt;= #{ew.inspectNumMin}
            </if>
            <if test="ew.inspectNumMax!=null">
                and inspect_num &lt;= #{ew.inspectNumMax}
            </if>
            <if test="ew.abnormalNumMin!=null">
                and abnormal_num &gt;= #{ew.abnormalNumMin}
            </if>
            <if test="ew.abnormalNumMax!=null">
                and abnormal_num &lt;= #{ew.abnormalNumMax}
            </if>
            <if test="ew.managers!=null and ew.managers.size() > 0">
                and JSON_CONTAINS(managers, JSON_ARRAY(
                <foreach collection="ew.managers" item="id" index="index"
                         separator=",">
                    #{id}
                </foreach>
                ))
            </if>
            <if test="ew.createBy!=null and ew.createBy.size() > 0">
                AND create_by IN
                <foreach collection="ew.createBy" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="ew.executors!=null and ew.executors.size() > 0">
                and JSON_CONTAINS(executors, JSON_ARRAY(
                <foreach collection="ew.executors" item="id" index="index"
                         separator=",">
                    #{id}
                </foreach>
                ))
            </if>
            <if test="ew.createTime!=null and ew.createTime.size==2">
                <!-- 创建开始时间 -->
                <if test="ew.createTime[0]!=null and ew.createTime[0]!=''">
                    and create_time &gt;= CONCAT(#{ew.createTime[0]}, ' 00:00:00')
                </if>
                <!-- 创建结束时间 -->
                <if test="ew.createTime[1]!=null and ew.createTime[1]!=''">
                    and create_time &lt;= CONCAT(#{ew.createTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.planFinishTime!=null and ew.planFinishTime.size==2">
                <if test="ew.planFinishTime[0]!=null and ew.planFinishTime[0]!=''">
                    and plan_finish_time &gt;= CONCAT(#{ew.planFinishTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.planFinishTime[1]!=null and ew.planFinishTime[1]!=''">
                    and plan_finish_time &lt;= CONCAT(#{ew.planFinishTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.finishTime!=null and ew.finishTime.size==2">
                <if test="ew.finishTime[0]!=null and ew.finishTime[0]!=''">
                    and finish_time &gt;= CONCAT(#{ew.finishTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.finishTime[1]!=null and ew.finishTime[1]!=''">
                    and finish_time &lt;= CONCAT(#{ew.finishTime[1]}, ' 23:59:59')
                </if>
            </if>
            <if test="ew.includeIds != null and ew.includeIds.size > 0">
                and id in
                <foreach collection="ew.includeIds" index="index" item="id" open="(" separator=","
                         close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        <if test="ew.sidx == null or ew.sidx == ''">
            order by create_time desc
        </if>
    </select>

    <select id="taskStatistics" resultType="com.niimbot.equipment.SiteInspectTaskStatisticsDto">
        SELECT
        ifnull(sum(IF(task_status = 1 and NOW() &lt;= plan_finish_time , 1, 0)), 0) as waiting_num,
        ifnull(sum(IF(task_status = 2, 1, 0)), 0) as doing_num,
        ifnull(sum(IF(task_status = 1 and NOW() &gt; plan_finish_time , 1, 0)), 0) as expire_num,
        ifnull(sum(IF(task_status = 4, 1, 0)), 0) as finish_num,
        ifnull(sum(IF(task_status = 5, 1, 0)), 0) as cancel_num
        FROM
        as_equipment_site_inspect_task
        <where>
            <if test="currentUserId != null">
                AND (
                JSON_CONTAINS(managers, JSON_ARRAY(#{currentUserId}))
                or
                JSON_CONTAINS(executors, JSON_ARRAY(#{currentUserId}))
                )
            </if>
        </where>
    </select>

    <select id="selectForWaitTaskMsg"
            resultMap="SiteInspectTaskPageDto">
        SELECT * FROM as_equipment_site_inspect_task WHERE company_id = #{companyId} AND task_status
        IN (1, 2)
        <if test="unit == 'day'">
            AND TIMESTAMPDIFF(DAY, CURRENT_DATE(), DATE_FORMAT(plan_finish_time, '%Y-%m-%d 00:00:00')) = #{interval}
        </if>
        <if test="unit == 'hour'">
            AND TIMESTAMPDIFF(HOUR, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00'), DATE_FORMAT(plan_finish_time, '%Y-%m-%d %H:00:00')) = #{interval}
        </if>
    </select>

    <resultMap id="SiteInspectTaskDetailExportDto"
               type="com.niimbot.equipment.SiteInspectTaskDetailExportDto">
        <id column="project_id" property="projectId"/>
        <result column="task_name" property="taskName"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="project_name" property="projectName"/>
        <result column="status" property="status"/>
        <result column="project_code" property="projectCode"/>
        <result column="content" property="content"/>
        <result column="enable_photo" property="enablePhoto"/>
        <result column="config_type" property="configType"/>
        <result column="enable_alarm" property="enableAlarm"/>
        <result column="config_data" property="configData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="inspect_result" property="inspectResult"/>
        <result column="inspect_result_list" property="inspectResultList"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="inspect_image" property="inspectImage"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="inspect_remark" property="inspectRemark"/>
        <result column="executors" property="executors"/>
        <result column="submit_time" property="submitTime"/>
    </resultMap>

    <select id="detailExport" resultMap="SiteInspectTaskDetailExportDto">
        SELECT
            tp.id as project_id,
            t.task_name,
            IF(r.data_type = 1, ast.asset_name, p.point_name) as NAME,
            IF(r.data_type = 1, ast.asset_code, p.point_code) as CODE,
            ip.project_name,
            tp.status,
            ip.project_code,
            ip.content,
            ip.enable_photo,
            ip.config_type,
            ip.enable_alarm,
            ip.config_data,
            tp.inspect_result,
            tp.inspect_result_list,
            tp.inspect_image,
            tp.inspect_remark,
            tp.executors,
            tp.submit_time
         from as_equipment_site_inspect_task t
         join as_equipment_site_inspect_task_range tr on tr.company_id = t.company_id and t.id = tr.task_id
         JOIN as_equipment_site_inspect_range r on tr.range_id = r.id and r.company_id = t.company_id
         join as_equipment_site_inspect_task_project tp on t.company_id = tp.company_id and tr.id = tp.task_range_id
         join as_equipment_site_inspect_project ip on tp.company_id= ip.company_id and tp.project_id = ip.id
         left join as_asset ast on r.data_type = 1 and t.company_id = ast.company_id and ast.id = r.data_id
         left join as_equipment_site_inspect_point p on r.data_type = 2 and t.company_id = p.company_id and p.id = r.data_id
         where t.id = #{taskId}
        order by tr.id asc, tp.id asc
    </select>

    <select id="siteInspectCount" resultType="int">
        SELECT
            count( DISTINCT t.id )
        FROM
            as_equipment_site_inspect_task t
            JOIN as_equipment_site_inspect_task_range tr ON tr.company_id = t.company_id
            AND tr.task_id = t.id
            JOIN as_equipment_site_inspect_range r ON r.company_id = tr.company_id
            AND r.id = tr.range_id
        WHERE
            r.data_type = 1
            AND t.task_status = 4
            AND r.data_id = #{assetId}
    </select>
</mapper>