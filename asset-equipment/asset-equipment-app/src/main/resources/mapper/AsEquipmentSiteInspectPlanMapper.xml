<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectPlanMapper">

    <resultMap id="EntSntPlanResultMap" type="com.niimbot.equipment.EntSntPlan">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_status" property="planStatus"/>
        <result column="route_type" property="routeType"/>
        <result column="issued_task_num" property="issuedTaskNum"/>
        <result column="plan_data" property="planData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="selectConditions">
        <!-- 关键字 -->
        <if test="ew.kw != null and ew.kw != ''">
            AND (t1.plan_no LIKE CONCAT('%', #{ew.kw}, '%') OR t1.plan_data ->> '$.planName' LIKE CONCAT('%', #{ew.kw},
            '%'))
        </if>

        <!-- 计划ID -->
        <if test="ew.includePlanIds != null and ew.includePlanIds.size() > 0">
            AND t1.id IN
            <foreach collection="ew.includePlanIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ew.excludePlanIds != null and ew.excludePlanIds.size() > 0">
            AND t1.id NOT IN
            <foreach collection="ew.excludePlanIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <!-- 计划状态 -->
        <if test="ew.includePlanStatus != null and ew.includePlanStatus.size() > 0">
            AND t1.plan_status IN
            <foreach collection="ew.includePlanStatus" item="planStatus" open="(" separator="," close=")">
                #{planStatus}
            </foreach>
        </if>
        <if test="ew.excludePlanStatus != null and ew.excludePlanStatus.size() > 0">
            AND t1.plan_status NOT IN
            <foreach collection="ew.excludePlanStatus" item="planStatus" open="(" separator="," close=")">
                #{planStatus}
            </foreach>
        </if>

        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>

        <!-- 权限集 -->
        <if test="perms != null and perms != ''">
            ${perms}
        </if>
    </sql>

    <sql id="selectModel">
        <if test="ew.taskType != null and ew.taskType != ''">
            AND t1.plan_data ->> '$.taskType' = #{ew.taskType}
        </if>
        <if test="ew.createBy != null and ew.createBy.size() > 0">
            AND t1.create_by IN
            <foreach collection="ew.createBy" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ew.issuedTaskNum != null and ew.issuedTaskNum.size() > 0">
            <if test="ew.issuedTaskNum.size() >= 1">
                AND t1.issued_task_num &gt;= #{ew.issuedTaskNum[0]}
            </if>
            <if test="ew.issuedTaskNum.size() >= 2">
                AND t1.issued_task_num &lt;= #{ew.issuedTaskNum[1]}
            </if>
        </if>
        <if test="ew.createTime != null and ew.createTime.size() > 0">
            <if test="ew.createTime.size() >= 1">
                AND t1.create_time &gt;= CONCAT(#{ew.createTime[0]}, ' 00:00:00')
            </if>
            <if test="ew.createTime.size() >= 2">
                AND t1.create_time &lt;= CONCAT(#{ew.createTime[1]}, ' 23:59:59')
            </if>
        </if>
        <if test="ew.updateTime != null and ew.updateTime.size() > 0">
            <if test="ew.updateTime.size() >= 1">
                AND t1.update_time &gt;= CONCAT(#{ew.updateTime[0]}, ' 00:00:00')
            </if>
            <if test="ew.updateTime.size() >= 2">
                AND t1.update_time &lt;= CONCAT(#{ew.updateTime[1]}, ' 23:59:59')
            </if>
        </if>
        <if test="ew.planBeginTime != null and ew.planBeginTime.size() > 0">
            AND t1.plan_data ->> '$.planBeginTime' != ''
            <if test="ew.planBeginTime.size() >= 1">
                AND FROM_UNIXTIME(t1.plan_data ->> '$.planBeginTime' / 1000, '%Y-%m-%d %H:%i:%s') &gt;=
                CONCAT(#{ew.planBeginTime[0]}, ' 00:00:00')
            </if>
            <if test="ew.planBeginTime.size() >= 2">
                AND FROM_UNIXTIME(t1.plan_data ->> '$.planBeginTime' / 1000, '%Y-%m-%d %H:%i:%s') &lt;=
                CONCAT(#{ew.planBeginTime[1]}, ' 23:59:59')
            </if>
        </if>
        <if test="ew.planEndTime != null and ew.planEndTime.size() > 0">
            AND t1.plan_data ->> '$.planEndTime' != ''
            <if test="ew.planEndTime.size() >= 1">
                AND FROM_UNIXTIME(t1.plan_data ->> '$.planEndTime' / 1000, '%Y-%m-%d %H:%i:%s') &gt;=
                CONCAT(#{ew.planEndTime[0]}, ' 00:00:00')
            </if>
            <if test="ew.planEndTime.size() >= 2">
                AND FROM_UNIXTIME(t1.plan_data ->> '$.planEndTime' / 1000, '%Y-%m-%d %H:%i:%s') &lt;=
                CONCAT(#{ew.planEndTime[1]}, ' 23:59:59')
            </if>
        </if>
        <if test="ew.managers != null and ew.managers.size() > 0">
            AND JSON_CONTAINS(t1.plan_data ->> '$.managers', JSON_ARRAY(
            <foreach collection="ew.managers" item="id" separator=",">
                CONVERT(#{id}, CHAR)
            </foreach>
            ))
        </if>
        <if test="ew.executors != null and ew.executors.size() > 0">
            AND JSON_CONTAINS(t1.plan_data ->> '$.executors', JSON_ARRAY(
            <foreach collection="ew.executors" item="id" separator=",">
                CONVERT(#{id}, CHAR)
            </foreach>
            ))
        </if>
    </sql>

    <select id="selectPageSearch" resultMap="EntSntPlanResultMap">
        SELECT t1.* FROM (
        SELECT id,
        company_id,
        plan_no,
        CASE
        WHEN plan_status = 3 THEN 3
        WHEN plan_status = 4 THEN 4
        WHEN ( NOW() &lt; FROM_UNIXTIME(plan_data ->> '$.planBeginTime' / 1000, '%Y-%m-%d %H:%i:%s') ) THEN 1
        WHEN ( (NOW() &gt;= FROM_UNIXTIME(plan_data ->> '$.planBeginTime' / 1000, '%Y-%m-%d %H:%i:%s')) AND (NOW()
        &lt;= FROM_UNIXTIME(plan_data ->> '$.planEndTime' / 1000, '%Y-%m-%d %H:%i:%s')) ) THEN 2
        WHEN ( NOW() &gt; FROM_UNIXTIME(plan_data ->> '$.planEndTime' / 1000, '%Y-%m-%d %H:%i:%s') ) THEN 4
        ELSE 0 END AS plan_status,
        route_type,
        issued_task_num,
        plan_data,
        create_by,
        create_time,
        update_by,
        update_time
        FROM as_equipment_site_inspect_plan
        WHERE is_delete = 0 AND company_id = #{ew.companyId}
        ) AS t1
        <where>
            <include refid="selectConditions"/>
            <include refid="selectModel"/>
        </where>
    </select>

    <select id="selectOne" resultMap="EntSntPlanResultMap">
        select
        id,
        company_id,
        plan_no,
        plan_status,
        route_type,
        issued_task_num,
        plan_data,
        create_by,
        create_time,
        update_by,
        update_time
        FROM as_equipment_site_inspect_plan
        where id = #{planId}
        <if test="includeDelete!=null and !includeDelete">
            and is_delete = 0
        </if>
    </select>

    <resultMap id="SiteInspectDistributeTaskRangeDto"
               type="com.niimbot.equipment.SiteInspectDistributeTaskDto$RangeDto">
        <id column="range_id" property="rangeId"/>
        <result column="plan_id" property="planId"/>
        <result column="sort" property="sort"/>
        <collection property="projectIds" javaType="java.util.List" ofType="java.lang.Long">
            <id column="project_id"/>
        </collection>
    </resultMap>

    <select id="distributeTaskRangeList" resultMap="SiteInspectDistributeTaskRangeDto">
        SELECT p.id  as plan_id,
               r.id  as range_id,
               r.sort,
               pj.id as project_id
        FROM as_equipment_site_inspect_plan p
                 JOIN as_equipment_site_inspect_range r ON p.company_id = r.company_id
            AND p.id = r.plan_id
                 JOIN as_equipment_site_inspect_range_project rp ON r.company_id = rp.company_id
            AND r.id = rp.range_id
                 JOIN as_equipment_site_inspect_project pj ON rp.company_id = pj.company_id
            AND rp.project_id = pj.id
        WHERE p.is_delete = 0
          AND r.is_delete = 0
          AND pj.is_delete = 0
          AND p.plan_status in (1, 2)
    </select>
</mapper>