<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskProjectMapper">

    <resultMap id="SiteInspectTaskProjectDto"
               type="com.niimbot.equipment.SiteInspectTaskProjectDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="task_id" property="taskId"/>
        <result column="task_range_id" property="taskRangeId"/>
        <result column="project_id" property="projectId"/>
        <result column="inspect_result" property="inspectResult"/>
        <result column="inspect_result_list" property="inspectResultList"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="inspect_image" property="inspectImage"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="inspect_remark" property="inspectRemark"/>
        <result column="submit_time" property="submitTime"/>
        <result column="executors" property="executors"/>
        <result column="config_type" property="configType"/>
        <result column="config_data" property="configData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="project_name" property="projectName"/>
        <result column="project_code" property="projectCode"/>
        <result column="content" property="content"/>
        <result column="enable_photo" property="enablePhoto"/>
        <result column="enable_alarm" property="enableAlarm"/>
        <result column="abnormal" property="abnormal"/>
    </resultMap>

    <select id="projectPage" resultMap="SiteInspectTaskProjectDto">
        SELECT
        tp.id,
        tp.status,
        tp.task_id,
        tp.task_range_id,
        tp.project_id,
        tp.inspect_result,
        tp.inspect_result_list,
        tp.inspect_image,
        tp.inspect_remark,
        tp.submit_time,
        tp.executors,
        p.config_type,
        p.config_data,
        p.project_name,
        p.project_code,
        p.content,
        p.enable_photo,
        p.enable_alarm,
        if(config_type = 4 and tp.status = 2, 1, 0) as abnormal
        FROM
        as_equipment_site_inspect_task_project tp
        LEFT JOIN as_equipment_site_inspect_project p ON tp.company_id = p.company_id
        AND tp.project_id = p.id
        WHERE
        tp.task_range_id = #{ew.rangeId}
        <if test="ew.status!=null and ew.status.size() > 0">
            and tp.status in
            <foreach collection="ew.status" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test="ew.kw!=null and ew.kw!=''">
            AND (
            p.project_name like concat('%',#{ew.kw},'%')
            OR
            p.project_code like concat('%',#{ew.kw},'%')
            )
        </if>
        order by tp.id asc
    </select>

    <select id="info" resultMap="SiteInspectTaskProjectDto">
        SELECT
            tp.id,
            tp.status,
            tp.task_id,
            tp.task_range_id,
            tp.project_id,
            tp.inspect_result,
            tp.inspect_result_list,
            tp.inspect_image,
            tp.inspect_remark,
            tp.submit_time,
            tp.executors,
            p.config_type,
            p.config_data,
            p.project_name,
            p.project_code,
            p.content,
            p.enable_photo,
            p.enable_alarm,
            if(config_type = 4 and tp.status = 2, 1, 0) as abnormal
        FROM
            as_equipment_site_inspect_task_project tp
            LEFT JOIN as_equipment_site_inspect_project p ON tp.company_id = p.company_id
            AND tp.project_id = p.id
        WHERE
            tp.id = #{id}
    </select>

    <resultMap id="SiteInspectTaskAbnormalDto"
               type="com.niimbot.equipment.SiteInspectTaskAbnormalDto">
        <id column="id" property="id"/>
        <result column="task_range_id" property="taskRangeId"/>
        <result column="project_id" property="projectId"/>
        <result column="inspect_result" property="inspectResult"/>
        <result column="inspect_result_list" property="inspectResultList"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="inspect_image" property="inspectImage"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="inspect_remark" property="inspectRemark"/>
        <result column="config_type" property="configType"/>
        <result column="submit_time" property="submitTime"/>
        <result column="executors" property="executors"/>
        <result column="project_name" property="projectName"/>
        <result column="project_code" property="projectCode"/>
    </resultMap>

    <select id="projectAbnormalPage" resultMap="SiteInspectTaskAbnormalDto">
        SELECT
            tp.id,
            tp.task_range_id,
            tp.project_id,
            tp.inspect_result,
            tp.inspect_result_list,
            tp.inspect_image,
            tp.inspect_remark,
            tp.submit_time,
            tp.executors,
            p.project_name,
            p.project_code,
            p.config_type
        FROM
            as_equipment_site_inspect_task_project tp
            LEFT JOIN as_equipment_site_inspect_project p ON tp.company_id = p.company_id
            AND tp.project_id = p.id
        where tp.task_id = #{ew.taskId}
        and tp.status = 2
    </select>

</mapper>