<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentMaintainTaskMapper">

    <resultMap id="MaintainTaskDto" type="com.niimbot.equipment.MaintainTaskDto">
        <id column="id" property="id"/>
        <result column="task_no" property="taskNo"/>
        <result column="task_status" property="taskStatus"/>
        <result column="plan_start_time" property="planStartTime"/>
        <result column="maintain_duration" property="maintainDuration"/>
        <result column="maintain_user" property="maintainUser"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_name" property="planName"/>
        <result column="asset_id" property="assetId"/>
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="pageTask" resultMap="MaintainTaskDto">
        SELECT
        task.id,
        task.task_no,
        task.task_status,
        task.plan_start_time,
        task.maintain_duration,
        task.maintain_user,
        task.plan_id,
        task.plan_no,
        task.plan_name,
        task.asset_id,
        task.asset_snapshot_data,
        task.order_data,
        task.create_by,
        task.create_time
        FROM
        as_equipment_maintain_task task
        JOIN as_equipment_maintain_plan p ON task.plan_id = p.id
        LEFT JOIN as_asset ast ON task.company_id = ast.company_id AND task.asset_id = ast.id
        WHERE
        task.is_delete = 0
        AND CONCAT(task.task_status, '_', p.filter_dispose, '_', status) not in ('1_1_4', '2_1_4', '3_1_4')
        <include refid="queryCondition"/>
    </select>

    <sql id="queryCondition">
        <if test="query.kw!=null and query.kw!=''">
            and (
            task.plan_no like concat('%', #{query.kw}, '%')
            or
            task.plan_name like concat('%', #{query.kw}, '%')
            or
            task.asset_code like concat('%', #{query.kw}, '%')
            or
            task.asset_name like concat('%', #{query.kw}, '%')
            )
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <!-- 勾选的单据id -->
        <if test="query.ids!=null and query.ids.size() > 0">
            and task.id in
            <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
    </sql>


    <select id="selectListForMsg" resultType="com.niimbot.equipment.EntMatTaskMsg">
        SELECT
        id,
        plan_id,
        plan_no,
        plan_name,
        plan_start_time,
        asset_id,
        maintain_user AS maintain_user_id,
        DATEDIFF(plan_start_time, CURDATE()) AS `interval_day`
        FROM
        as_equipment_maintain_task
        WHERE company_id = #{companyId} AND task_status = 1 AND is_delete = 0 AND
        (
        <if test="code == 'SBBYRWDQTX'">
            DATEDIFF(plan_start_time, CURDATE()) IN
        </if>
        <if test="code == 'SBBYRWGQTX'">
            DATEDIFF(CURDATE(), plan_start_time) IN
        </if>
        <foreach collection="days" item="day" open="(" separator="," close=")">
            #{day}
        </foreach>
        )
    </select>

    <select id="cateNewAssetIds" resultType="long">
        SELECT
            ast.id
        FROM
            as_equipment_maintain_plan_data p
            JOIN as_asset ast ON p.company_id = ast.company_id
            AND ast.asset_category = CONCAT( p.data_id, '' )
        WHERE
            ast.company_id = #{companyId}
            AND ast.is_delete = 0
            AND ast.id NOT IN
            ( SELECT asset_id FROM as_equipment_maintain_task
            WHERE company_id = #{companyId} AND plan_id = #{planId} )
    </select>

</mapper>