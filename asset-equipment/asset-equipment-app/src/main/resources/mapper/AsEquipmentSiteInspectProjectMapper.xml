<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectProjectMapper">

    <resultMap id="EntSntProjectResultMap" type="com.niimbot.equipment.EntSntProject">
        <id column="id" property="id"/>
        <result column="project_name" property="projectName"/>
        <result column="project_code" property="projectCode"/>
        <result column="content" property="content"/>
        <result column="enable_photo" property="enablePhoto"/>
        <result column="enable_alarm" property="enableAlarm"/>
        <result column="config_type" property="configType"/>
        <result column="config_data" property="configData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="selectCountInPlan" resultType="java.lang.Integer">
        SELECT
        COUNT( DISTINCT t3.id )
        FROM
        as_equipment_site_inspect_range_project t1
        LEFT JOIN as_equipment_site_inspect_range t2 ON t1.range_id = t2.id
        LEFT JOIN as_equipment_site_inspect_plan t3 ON t2.plan_id = t3.id
        WHERE
        t1.company_id = #{companyId}
        AND t2.company_id = #{companyId}
        AND t3.company_id = #{companyId}
        AND t2.is_delete = 0
        AND t3.is_delete = 0
        AND t1.project_id IN
        <foreach collection="projectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectPageSearch" resultMap="EntSntProjectResultMap">
        SELECT * FROM as_equipment_site_inspect_project WHERE is_delete = 0
        <if test="em.includeProjectIds != null and em.includeProjectIds.size() > 0">
            AND id IN
            <foreach collection="em.includeProjectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="em.excludeProjectIds != null and em.excludeProjectIds.size() > 0">
            AND id NOT IN
            <foreach collection="em.excludeProjectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="em.kw != null and em.kw != ''">
            AND ( project_code LIKE CONCAT('%', #{em.kw}, '%') OR project_name LIKE CONCAT('%', #{em.kw}, '%') )
        </if>
        <if test="em.planId != null">
            AND id IN ( SELECT id FROM as_equipment_site_inspect_range_project WHERE range_id IN ( SELECT id FROM
            as_equipment_site_inspect_range WHERE plan_id = #{em.planId} AND is_delete = 0) )
        </if>
        order by id desc
    </select>

</mapper>