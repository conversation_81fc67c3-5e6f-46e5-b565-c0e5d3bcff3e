<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectPointMapper">

    <resultMap id="AsEquipmentSiteInspectPointDto" type="com.niimbot.equipment.AsEquipmentSiteInspectPointDto">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="point_name" property="pointName"/>
        <result column="point_code" property="pointCode"/>
        <result column="point_desc" property="pointDesc"/>
        <result column="org_id" property="orgId"/>
        <result column="pid" property="pid"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="specific_location" property="specificLocation"/>
        <result column="area_code" property="areaCode"/>
        <result column="point_images" property="pointImages"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="pagePoint" resultMap="AsEquipmentSiteInspectPointDto">
        SELECT
        p.id,
        p.point_name,
        p.point_code,
        p.point_desc,
        p.org_id,
        p.pid,
        p.specific_location,
        p.point_images,
        p.create_time,
        p.create_by,
        p.update_by,
        p.update_time,
        a.area_code
        FROM
        as_equipment_site_inspect_point p left join as_area a on p.pid = a.id
        <where>
            p.is_delete = 0
            <if test="em.kw != null and em.kw != ''">
                and (p.point_name like concat('%',#{em.kw},'%')
                or
                p.point_code like concat('%',#{em.kw},'%')
                or
                p.specific_location like concat('%',#{em.kw},'%'))
            </if>
            <if test="em.areaId != null and em.areaId != ''">
                and a.id = #{em.areaId}
            </if>
            <if test="em.orgId != null">
                and a.org_id = #{em.orgId}
            </if>
            <if test="em.ids != null and em.ids.size > 0">
                and p.id in
                <foreach collection="em.ids" index="index" item="id" open="(" separator=","
                         close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY create_time asc
    </select>

    <select id="getPointByAreaId" resultType="java.lang.Integer">
        select count(1)
        from as_equipment_site_inspect_point p
                 left join as_area a on p.pid = a.id
        where a.id = #{id}
    </select>

    <select id="getPlanCount" resultType="java.lang.Integer">
        SELECT count(t3.id)
        from as_equipment_site_inspect_point t1
                 left join as_equipment_site_inspect_range t2 on t1.id = t2.data_id
                 LEFT JOIN as_equipment_site_inspect_plan t3 on t2.plan_id = t3.id
        where t2.data_type = 2
          and t3.plan_status in (1, 2) and t1.id = #{id};
    </select>

    <select id="printPage" resultMap="AsEquipmentSiteInspectPointDto">
        SELECT
        p.*
        FROM
        as_equipment_site_inspect_point p left join as_area a on p.pid = a.id
        where p.is_delete = 0 and a.is_delete = 0 and p.company_id = #{em.companyId} and a.company_id = #{em.companyId}
        <if test="em.kw != null and em.kw != ''">
            and (p.point_name like concat('%',#{em.kw},'%')
            or
            p.point_code like concat('%',#{em.kw},'%')
            or
            p.specific_location like concat('%',#{em.kw},'%'))
        </if>
        <if test="em.premAreaIds != null and em.premAreaIds.size() > 0">
            and a.id in
            <foreach collection="em.premAreaIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="em.includePointIds != null and em.includePointIds.size() > 0">
            and p.id in
            <foreach collection="em.includePointIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        ORDER BY create_time asc
    </select>
</mapper>