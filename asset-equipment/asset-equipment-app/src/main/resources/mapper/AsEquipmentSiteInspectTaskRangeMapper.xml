<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskRangeMapper">

    <select id="taskRangePage" resultType="com.niimbot.equipment.SiteInspectTaskRangeDto">
        SELECT
        tr.id,
        tr.range_status,
        tr.task_id,
        r.data_id,
        r.data_type,
        IF(r.data_type = 1, CONCAT(if(ast.status = 4, '【已处置】', ''), ast.asset_name), p.point_name) as NAME,
        IF(r.data_type = 1, ast.asset_code, p.point_code) as CODE,
        IF(r.data_type = 1, ast.asset_data ->> '$.assetPhoto', p.point_images) as image,
        IF(r.data_type = 1, ast.storage_area, p.pid) as area_id,
        tr.inspect_num,
        tr.checked_num,
        tr.normal_num,
        tr.abnormal_num,
        tr.can_site_inspect,
        tr.handle_status,
        tr.sort
        FROM
        as_equipment_site_inspect_task_range tr
        JOIN as_equipment_site_inspect_range r on tr.range_id = r.id and tr.company_id =
        r.company_id
        left join as_asset ast on r.data_type = 1 and tr.company_id = ast.company_id and ast.id =
        r.data_id
        left join as_equipment_site_inspect_point p on r.data_type = 2 and tr.company_id =
        p.company_id and p.id = r.data_id
        where tr.task_id = #{ew.taskId}
        <if test="ew.rangeStatus!=null and ew.rangeStatus!='3'.toString()">
            and tr.range_status = #{ew.rangeStatus}
        </if>
        <if test="ew.rangeStatus!=null and ew.rangeStatus=='3'.toString()">
            and tr.abnormal_num &gt; 0
        </if>
        <if test="ew.handleStatus!=null">
            and tr.handle_status = #{ew.handleStatus}
        </if>
        <if test="ew.kw!=null and ew.kw!=''">
            AND (
            IF(r.data_type = 1, ast.asset_name, p.point_name) like concat('%',#{ew.kw},'%')
            OR
            IF(r.data_type = 1, ast.asset_code, p.point_code) like concat('%',#{ew.kw},'%')
            )
        </if>
        ORDER BY tr.sort asc, tr.id asc
    </select>

    <select id="taskRangeInfo" resultType="com.niimbot.equipment.SiteInspectTaskRangeDto">
        SELECT
        tr.id,
        tr.range_status,
        tr.task_id,
        r.data_id,
        r.data_type,
        IF(r.data_type = 1, ast.asset_name, p.point_name) as NAME,
        IF(r.data_type = 1, ast.asset_code, p.point_code) as CODE,
        IF(r.data_type = 1, ast.asset_data ->> '$.assetPhoto', p.point_images) as image,
        IF(r.data_type = 1, ast.storage_area, p.pid) as area_id,
        tr.inspect_num,
        tr.checked_num,
        tr.normal_num,
        tr.abnormal_num,
        tr.can_site_inspect,
        tr.handle_status,
        r.sort
        FROM
        as_equipment_site_inspect_task_range tr
        JOIN as_equipment_site_inspect_range r on tr.range_id = r.id and tr.company_id =
        r.company_id
        left join as_asset ast on r.data_type = 1 and tr.company_id = ast.company_id and ast.id =
        r.data_id
        left join as_equipment_site_inspect_point p on r.data_type = 2 and tr.company_id =
        p.company_id and p.id = r.data_id
        where tr.id = #{rangeId}
    </select>

    <select id="rangeBaseInfos" resultType="com.niimbot.equipment.SiteInspectTaskRangeDto">
        SELECT
        tr.id,
        IF(r.data_type = 1, ast.asset_name, p.point_name) as NAME,
        IF(r.data_type = 1, ast.asset_code, p.point_code) as CODE,
        r.data_id,
        r.data_type
        FROM
        as_equipment_site_inspect_task_range tr
        JOIN as_equipment_site_inspect_range r on tr.range_id = r.id and tr.company_id =
        r.company_id
        left join as_asset ast on r.data_type = 1 and tr.company_id = ast.company_id and ast.id =
        r.data_id
        left join as_equipment_site_inspect_point p on r.data_type = 2 and tr.company_id =
        p.company_id and p.id = r.data_id
        where tr.id IN
        <foreach collection="rangeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="taskRangeStatistics"
            resultType="com.niimbot.equipment.SiteInspectTaskRangeStatisticsDto">
          SELECT
            sum(if(r.range_status=1, 1, 0)) AS inspect_progress_num,
            sum(if(r.range_status=2, 1, 0)) AS inspect_done_num,
            sum(r.abnormal_num) AS abnormal_num
        FROM
            as_equipment_site_inspect_task_range r
        WHERE
            r.task_id = #{taskId}
    </select>

    <select id="handleCheck" resultType="com.niimbot.means.AssetDto">
   SELECT
	         IF(r.data_type = 1, ast.id, 0) as id,
           ast.status
        FROM
            as_equipment_site_inspect_task_range tr
            JOIN as_equipment_site_inspect_range r ON tr.range_id = r.id
            AND tr.company_id = r.company_id
            left JOIN as_asset ast ON r.data_type = 1 AND tr.company_id = ast.company_id AND ast.id = r.data_id
            left join as_equipment_site_inspect_point p on r.data_type = 2 and tr.company_id = p.company_id and p.id = r.data_id
        WHERE
            tr.id = #{rangeId}
        limit 1
    </select>
</mapper>