package com.niimbot.asset.equipment.schedule;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectPlanMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectPlan;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.equipment.EntSntPlanData;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class EquipmentSiteInspectPlanScheduler {

    private final RedisDistributeLock redisDistributeLock;

    private final AsEquipmentSiteInspectPlanMapper planMapper;

    /**
     * 定时修改设备巡检计划状态
     */
    @Scheduled(cron = "0 10 3 * * ?")
    public void execute() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }
        redisDistributeLock.lock("EquipmentSiteInspectPlanScheduler", 1L, TimeUnit.HOURS, v -> {
            List<AsEquipmentSiteInspectPlan> plans = planMapper.selectList(
                    Wrappers.lambdaQuery(AsEquipmentSiteInspectPlan.class)
                            .notIn(AsEquipmentSiteInspectPlan::getPlanStatus, Lists.newArrayList(EquipmentConstant.SNT_PLAN_STATUS_IS_STOPPED, EquipmentConstant.SNT_PLAN_STATUS_IS_FINISHED))
            );
            if (CollUtil.isEmpty(plans)) {
                return;
            }
            LocalDateTime now = LocalDateTime.now();
            plans.forEach(plan -> {
                JSONObject data = plan.getPlanData();
                LocalDateTime planBeginTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(data.getLong(EntSntPlanData.Fields.planBeginTime)), ZoneId.systemDefault());
                LocalDateTime planEndTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(data.getLong(EntSntPlanData.Fields.planEndTime)), ZoneId.systemDefault());
                // 【执行中】当前时间＜计划结束时间
                if (now.isAfter(planBeginTime) && now.isBefore(planEndTime)) {
                    planMapper.update(null,
                            Wrappers.lambdaUpdate(AsEquipmentSiteInspectPlan.class)
                                    .set(AsEquipmentSiteInspectPlan::getPlanStatus, EquipmentConstant.SNT_PLAN_STATUS_IS_RUNNING)
                                    .eq(AsEquipmentSiteInspectPlan::getId, plan.getId())
                    );
                }
                // 【已结束】当前时间＞计划结束时间
                if (now.isAfter(planEndTime)) {
                    planMapper.update(null,
                            Wrappers.lambdaUpdate(AsEquipmentSiteInspectPlan.class)
                                    .set(AsEquipmentSiteInspectPlan::getPlanStatus, EquipmentConstant.SNT_PLAN_STATUS_IS_FINISHED)
                                    .eq(AsEquipmentSiteInspectPlan::getId, plan.getId())
                    );
                }
            });
        });

    }

}
