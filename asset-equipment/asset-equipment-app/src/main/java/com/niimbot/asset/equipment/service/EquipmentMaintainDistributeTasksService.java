package com.niimbot.asset.equipment.service;

import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlan;

/**
 * <AUTHOR>
 * @date 2023/10/18 11:27
 */
public interface EquipmentMaintainDistributeTasksService {

    void distributeTasksAsync(Long planId, Long companyId);

    Boolean distributeTasks(Long planId, Long companyId);

    void addCateAssetTask(AsEquipmentMaintainPlan maintainPlan);
}
