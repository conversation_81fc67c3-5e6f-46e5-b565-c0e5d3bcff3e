package com.niimbot.asset.equipment.service;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.*;
import com.niimbot.system.DataTransfer;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EquipmentMaintainPlanService {

    /**
     * 预览保养计划
     *
     * @param plan 计划
     * @return preview
     */
    EntMatPlanPreview previewPlan(CreateEntMatPlan plan);

    /**
     * 创建保养计划
     *
     * @param plan 计划体
     * @return true if success
     */
    AuditableCreatePlanResult createPlan(CreateEntMatPlan plan);

    /**
     * 停用保养计划
     *
     * @param union id
     * @return true if success
     */
    AuditableStopPlanResult stopPlan(StopEntMatPlan union);

    /**
     * 删除保养计划
     *
     * @param remove ids
     * @return true if success
     */
    AuditableRemovePlanResult removePlan(RemoveEntMatPlan remove);

    /**
     * 变更保养计划负责人
     *
     * @param change body
     * @return true if success
     */
    Boolean rePlanUser(ReEntMatPlanUser change);

    /**
     * 转移计划与任务的负责人
     *
     * @param oldUserId    old user id
     * @param dataTransfer data transfer
     * @return true if success
     */
    Boolean transformPlan(Long oldUserId, DataTransfer dataTransfer);

    /**
     * 设备保养计划详情
     *
     * @param planId 计划ID
     * @return 设备详情
     */
    EntMatPlanDetail detailPlan(Long planId);

    /**
     * 设备保养计划设备分类信息
     *
     * @param planId 计划ID
     * @return 设备分类
     */
    List<EntMatPlanEntCateData> listEntCate(Long planId);

    /**
     * 设备保养计划数量统计
     *
     * @param planIds 计划ID集合
     * @return counter
     */
    List<EntMatPlanCounter> countPlan(List<Long> planIds);

    /**
     * 设备保养计划分页搜索
     *
     * @param search search
     * @return page
     */
    PageUtils<EntMatPlan> searchPlan(EntMatPlanSearch search);

    /**
     * 设备保养计划已选择的数据
     *
     * @param get 计划ID
     * @return object
     */
    PageUtils<EntMatPlanEntData> pageSelectedEntMatData(GetSelectedEntData get);

    /**
     * 设备保养计划已选择的备件数据
     *
     * @param get 计划ID
     * @return object
     */
    PageUtils<EntMatPlanSrPrData> pageSelectedEntSrPrData(GetSelectedSrPrData get);

}
