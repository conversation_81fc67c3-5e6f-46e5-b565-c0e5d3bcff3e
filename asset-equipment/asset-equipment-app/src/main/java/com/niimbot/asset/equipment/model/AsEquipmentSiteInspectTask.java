package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.system.handle.LongListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:22
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_site_inspect_task", autoResultMap = true)
public class AsEquipmentSiteInspectTask {

    // id
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    // 公司ID（租户ID）
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    // 任务状态（1-待巡检，2-巡检中，3-已逾期，4-已完成，5-已作废）
    private Integer taskStatus;

    // 巡检任务编码
    private String taskNo;

    // 巡检任务名称
    private String taskName;

    // 巡检计划ID
    private Long planId;

    // 巡检路线类型：1-随机，2-按固定顺序
    private Integer routeType;

    // 巡检管理员
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> managers;

    // 巡检人
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> executors;

    // 任务类型
    private String taskType;

    // 点位数量
    private Integer inspectNum;

    // 已完成点位
    private Integer checkedNum;

    // 异常项目数
    private Integer abnormalNum;

    // 计划完成时间
    private LocalDateTime planFinishTime;

    // 实际完成时间
    private LocalDateTime finishTime;

    // 作废时间
    private LocalDateTime cancelTime;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
