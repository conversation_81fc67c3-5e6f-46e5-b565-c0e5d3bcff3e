package com.niimbot.asset.equipment.model;


import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.means.handle.StringListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsEquipmentSiteInspectPoint对象", description = "巡检点位表")
@TableName(value = "as_equipment_site_inspect_point", autoResultMap = true)
public class AsEquipmentSiteInspectPoint implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "巡检点位ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "点位名称")
    private String pointName;

    @ApiModelProperty(value = "点位CODE")
    private String pointCode;

    @ApiModelProperty("点位图片")
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> pointImages = new ArrayList<>();

    @ApiModelProperty("点位描述")
    private String pointDesc;

    @ApiModelProperty(value = "租户ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "组织id(公司)")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long orgId;

    @ApiModelProperty(value = "上级区域")
    private Long pid;

    @ApiModelProperty("点位描述")
    private String specificLocation;

    @ApiModelProperty(" 0-未删除  1：已删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
