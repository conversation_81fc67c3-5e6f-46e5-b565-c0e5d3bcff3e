package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectRangeProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface AsEquipmentSiteInspectRangeProjectMapper extends BaseMapper<AsEquipmentSiteInspectRangeProject> {

    /**
     * 根据巡检范围ID删除
     *
     * @param rangeId rangeId
     */
    void deleteByRangeId(@Param("rangeId") Long rangeId);

    /**
     * 根据巡检项目ID删除
     *
     * @param projectId 项目ID
     */
    void deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据计划ID删除
     *
     * @param planId 计划ID
     */
    void deleteByPlanId(@Param("planId") Long planId);

    /**
     * 根据范围ID获取项目ID集合
     *
     * @param rangeId   rangeId
     * @param companyId companyId
     * @return list
     */
    default List<Long> selectProjectIdByRangeId(Long rangeId, Long companyId) {
        return this.selectList(
                Wrappers.lambdaQuery(AsEquipmentSiteInspectRangeProject.class)
                        .select(AsEquipmentSiteInspectRangeProject::getProjectId)
                        .eq(AsEquipmentSiteInspectRangeProject::getCompanyId, companyId)
                        .eq(AsEquipmentSiteInspectRangeProject::getRangeId, rangeId)
        ).stream().map(AsEquipmentSiteInspectRangeProject::getProjectId).distinct().collect(Collectors.toList());
    }

}
