package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectPlan;
import com.niimbot.equipment.EntSntPlan;
import com.niimbot.equipment.SearchEntSntPlan;
import com.niimbot.equipment.SiteInspectDistributeTaskDto;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@EnableDataPerm(excludeMethodName = {"distributeTaskBaseList", "selectPageSearch"})
public interface AsEquipmentSiteInspectPlanMapper extends BaseMapper<AsEquipmentSiteInspectPlan> {

    /**
     * 分页查询设备巡检计划
     *
     * @param page       分页参数
     * @param search     搜索参数
     * @param perms      权限集
     * @param conditions 动态条件
     * @return result
     */
    IPage<EntSntPlan> selectPageSearch(
            @Param("page") Page<?> page,
            @Param("ew") SearchEntSntPlan search,
            @Param("perms") String perms,
            @Param("conditions") String conditions
    );

    EntSntPlan selectOne(@Param("planId") Long planId,
                         @Param("includeDelete") boolean includeDelete);

    List<SiteInspectDistributeTaskDto.RangeDto> distributeTaskRangeList();
}
