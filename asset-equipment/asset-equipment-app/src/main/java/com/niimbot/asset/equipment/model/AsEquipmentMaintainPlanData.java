package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 保养计划关联的数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_maintain_plan_data", autoResultMap = true)
public class AsEquipmentMaintainPlanData {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业ID
     */
    @TenantFilterColumn
    private Long companyId;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 数据ID
     */
    private Long dataId;

    /**
     * 数据类型 1-设备 2-备件 3-分类
     */
    private Integer dataType;

    /**
     * 备件更换数量
     */
    private BigDecimal replacement;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
