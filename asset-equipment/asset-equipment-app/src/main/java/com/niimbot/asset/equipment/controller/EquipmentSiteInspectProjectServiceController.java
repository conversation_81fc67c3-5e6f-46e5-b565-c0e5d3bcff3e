package com.niimbot.asset.equipment.controller;

import com.niimbot.asset.equipment.service.EquipmentSiteInspectProjectService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.SerialNumberUtils;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.equipment.CreateEntSntProject;
import com.niimbot.equipment.EntSntProject;
import com.niimbot.equipment.EntSntProjectImportDto;
import com.niimbot.equipment.SearchEntSntProject;
import com.niimbot.luckysheet.LuckyMultiSheetModel;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/equipment/site/inspect/project")
public class EquipmentSiteInspectProjectServiceController {

    private final EquipmentSiteInspectProjectService projectService;
    private final AsAssetImportErrorService assetImportErrorService;

    @PostMapping("/create")
    public AuditableOperateResult create(@RequestBody CreateEntSntProject create) {
        return projectService.createProject(create);
    }

    @PostMapping("/remove")
    public List<AuditableOperateResult> remove(@RequestBody List<Long> projectIds) {
        return projectService.removeProject(projectIds);
    }

    @PostMapping("/search")
    public PageUtils<EntSntProject> search(@RequestBody SearchEntSntProject search) {
        return projectService.searchProject(search);
    }

    @GetMapping("/recommendCode")
    public String recommendCode() {
        return SerialNumberUtils.getMaxCode(projectService);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return assetImportErrorService.deleteByTaskId(taskId);
    }

    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        projectService.saveSheetHead(importErrorDto);
    }

    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody EntSntProjectImportDto importDto) {
        return projectService.saveSheetData(importDto);
    }

    @GetMapping(value = "/importError/{taskId}")
    List<LuckyMultiSheetModel> importError(@PathVariable("taskId") Long taskId) {
        return projectService.importError(taskId);
    }

}
