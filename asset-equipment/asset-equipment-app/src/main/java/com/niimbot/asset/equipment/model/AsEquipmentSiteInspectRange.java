package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 设备巡检范围
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_site_inspect_range", autoResultMap = true)
public class AsEquipmentSiteInspectRange {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TenantFilterColumn
    private Long companyId;

    private Long planId;

    private Long dataId;

    /**
     * 数据类型：1-设备，2-点位
     */
    private Integer dataType;

    private Integer sort;

    @TableLogic
    private Boolean isDelete;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
