package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectProject;
import com.niimbot.equipment.EntSntProject;
import com.niimbot.equipment.SearchEntSntProject;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@EnableDataPerm(excludeMethodName = {"selectCountInPlan", "selectMaxCode", "selectMaxCodeByCode"})
public interface AsEquipmentSiteInspectProjectMapper extends BaseMapper<AsEquipmentSiteInspectProject> {

    /**
     * select * from as_equipment_site_inspect_project where company_id = ? and project_code = ?
     *
     * @param projectCode code
     * @return AsEquipmentSiteInspectProject
     */
    default AsEquipmentSiteInspectProject selectByProjectCode(String projectCode) {
        return this.selectOne(
                Wrappers.lambdaQuery(AsEquipmentSiteInspectProject.class)
                        .eq(AsEquipmentSiteInspectProject::getProjectCode, projectCode)
        );
    }

    /**
     * 根据项目名称查询
     *
     * @param projectName 项目名称
     * @return AsEquipmentSiteInspectProject
     */
    default AsEquipmentSiteInspectProject selectByProjectName(String projectName) {
        return this.selectOne(
                Wrappers.lambdaQuery(AsEquipmentSiteInspectProject.class)
                        .eq(AsEquipmentSiteInspectProject::getProjectName, projectName)
        );
    }

    /**
     * count
     *
     * @param companyId  企业ID
     * @param projectIds 项目ID
     * @return count
     */
    Integer selectCountInPlan(@Param("companyId") Long companyId, @Param("projectIds") List<Long> projectIds);

    /**
     * 分页查询
     *
     * @param page   分页参数
     * @param search 查询参数
     * @return result
     */
    IPage<EntSntProject> selectPageSearch(IPage<SearchEntSntProject> page, @Param("em") SearchEntSntProject search);

    /**
     * 获取企业下最大的编码
     *
     * @param companyId 企业ID
     * @return code
     */
    @Select("select max(project_code) from as_equipment_site_inspect_project WHERE project_code REGEXP '^[A-Z]{1}[0-9]{2,3}$' AND company_id = #{companyId}")
    String selectMaxCode(@Param("companyId") Long companyId);

    /**
     * 获取企业下最大的编码
     *
     * @param companyId 企业ID
     * @param code      编码
     * @return code
     */
    @Select("select max(project_code) from as_equipment_site_inspect_project WHERE project_code REGEXP '^[A-Z]{1}[0-9]{2,3}$' AND company_id = #{companyId} and project_code like concat(#{code}, '%')")
    String selectMaxCodeByCode(@Param("companyId") Long companyId, @Param("code") String code);

}
