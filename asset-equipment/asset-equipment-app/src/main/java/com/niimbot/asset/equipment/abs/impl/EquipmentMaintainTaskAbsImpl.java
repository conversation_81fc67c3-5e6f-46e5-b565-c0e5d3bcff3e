package com.niimbot.asset.equipment.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainTask;
import com.niimbot.asset.equipment.model.AsEquipmentSpareParts;
import com.niimbot.asset.equipment.service.EquipmentSparePartsService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.EquipmentMaintainTaskAbs;
import com.niimbot.equipment.SparePartsDropDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/16 下午5:04
 */
@Slf4j
@RestController
@RequestMapping("/client/abs/equipment/maintainTask/")
public class EquipmentMaintainTaskAbsImpl implements EquipmentMaintainTaskAbs {

    @Autowired
    private EquipmentSparePartsService equipmentSparePartsService;

    @Override
    public Integer countMaintainTask(Long assetId) {
        if (Objects.isNull(assetId)) {
            return 0;
        }

        return Convert.toInt(Db.count(Wrappers.lambdaQuery(AsEquipmentMaintainTask.class)
                .eq(AsEquipmentMaintainTask::getAssetId, assetId)
                .in(AsEquipmentMaintainTask::getTaskStatus, ListUtil.of(1, 2, 3))
                .eq(AsEquipmentMaintainTask::getIsDelete, Boolean.FALSE)));
    }

    @Override
    public Boolean removeSpareParts(Long assetId) {
        if (Objects.isNull(assetId)) {
            return Boolean.FALSE;
        }

        SparePartsDropDto sparePartsDropDto = new SparePartsDropDto().setAssetId(assetId);
        return equipmentSparePartsService.dropSpareParts(sparePartsDropDto);
    }

    @Override
    public Boolean removeByMaterialId(List<Long> materialIds) {
        return equipmentSparePartsService.remove(Wrappers.lambdaQuery(AsEquipmentSpareParts.class)
                .eq(AsEquipmentSpareParts::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(AsEquipmentSpareParts::getMaterialId, materialIds));
    }
}
