package com.niimbot.asset.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectPoint;
import com.niimbot.asset.framework.service.CommonCodeService;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.equipment.AuditableRemovePointResult;
import com.niimbot.equipment.EquipmentSiteInspectPointImportDto;
import com.niimbot.equipment.EquipmentSiteInspectPointQueryDto;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;

import java.util.List;

public interface EquipmentSiteInspectPointService extends IService<AsEquipmentSiteInspectPoint>, CommonCodeService {

    IPage<AsEquipmentSiteInspectPointDto> pagePoint(EquipmentSiteInspectPointQueryDto queryDto);

    List<AsEquipmentSiteInspectPointDto> listPoint(EquipmentSiteInspectPointQueryDto queryDto);

    Long add(AsEquipmentSiteInspectPoint inspectPoint);

    boolean edit(AsEquipmentSiteInspectPoint inspectPoint);

    AuditableRemovePointResult remove(Long id);

    List<List<LuckySheetModel>> importError(Long taskId);

    void saveSheetHead(HeadImportErrorDto importErrorDto);

    Boolean saveSheetData(EquipmentSiteInspectPointImportDto importDto);

    List<AsEquipmentSiteInspectPointDto> listAll();

    Integer getPointByAreaId(Long id);

    AsEquipmentSiteInspectPointDto pointDetail(Long pointId);

}
