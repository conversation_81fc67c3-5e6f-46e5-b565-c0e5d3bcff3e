package com.niimbot.asset.equipment.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanMapper;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlan;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.equipment.EntMatPlanData;
import lombok.RequiredArgsConstructor;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class EquipmentMaintainPlanScheduler {

    private final RedisDistributeLock redisDistributeLock;

    private final AsEquipmentMaintainPlanMapper planMapper;

    /**
     * 定时修改设备保养计划状态未已结束
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void execute() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }
        redisDistributeLock.lock("EquipmentMaintainPlanScheduler", 10L, TimeUnit.MINUTES, v -> {
            List<AsEquipmentMaintainPlan> plans = planMapper.selectList(
                    Wrappers.lambdaQuery(AsEquipmentMaintainPlan.class)
                            .eq(AsEquipmentMaintainPlan::getPlanStatus, EquipmentConstant.PLAN_STATUS_IS_EXEC)
            );
            if (CollUtil.isEmpty(plans)) {
                return;
            }
            plans.parallelStream().forEach(p -> {
                LocalDateTime now = LocalDateTime.now();
                String taskType = p.getPlanData().getString(EntMatPlanData.Fields.taskType);
                LocalDateTime beginTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(p.getPlanData().getLong(EntMatPlanData.Fields.taskBeginTime)), ZoneId.systemDefault());
                LocalDateTime endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(p.getPlanData().getLong(EntMatPlanData.Fields.taskEndTime)), ZoneId.systemDefault());
                // 一次性任务的开始时间到达后，变为已结束
                // 循环任务的结束时间到达后。变为已结束
                boolean change = (EquipmentConstant.TASK_TYPE_IS_TIMELINESS.equals(taskType) && beginTime.isBefore(now)) || (EquipmentConstant.TASK_TYPE_IS_PERIODICITY.equals(taskType) && endTime.isBefore(now));
                if (change) {
                    planMapper.update(
                            null,
                            Wrappers.lambdaUpdate(AsEquipmentMaintainPlan.class)
                                    .set(AsEquipmentMaintainPlan::getPlanStatus, EquipmentConstant.PLAN_STATUS_IS_FINISH)
                                    .eq(AsEquipmentMaintainPlan::getId, p.getId())
                    );
                }
            });
        });

    }

}
