package com.niimbot.asset.equipment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.equipment.enums.TaskRangeTypeEnum;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainTaskMapper;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainTask;
import com.niimbot.asset.equipment.model.AsEquipmentReplacement;
import com.niimbot.asset.equipment.model.AsEquipmentSpareParts;
import com.niimbot.asset.equipment.resolver.MySqlEquipmentMaintainTaskConditionResolver;
import com.niimbot.asset.equipment.service.EquipmentMaintainTaskService;
import com.niimbot.asset.equipment.service.EquipmentReplacementService;
import com.niimbot.asset.equipment.service.EquipmentSparePartsService;
import com.niimbot.asset.framework.constant.*;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryConditionType;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.MaterialUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.material.service.AsMaterialService;
import com.niimbot.asset.material.service.AsMaterialStockService;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.sdk.FormValidatorCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.equipment.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainOrderPlanContentDto;
import com.niimbot.material.AdjustStockDto;
import com.niimbot.material.MaterialSparePartsDto;
import com.niimbot.material.MaterialStockDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.system.QueryConditionDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/12 11:17
 */
@RequiredArgsConstructor
@Service
public class EquipmentMaintainTaskServiceImpl extends ServiceImpl<AsEquipmentMaintainTaskMapper, AsEquipmentMaintainTask> implements EquipmentMaintainTaskService {

    private static final String MAINTAIN_CONTENT = "maintainContent";

    private final MySqlEquipmentMaintainTaskConditionResolver conditionResolver;
    private final EquipmentReplacementService equipmentReplacementService;
    private final AsFormService formService;
    private final AsMaterialStockService materialStockService;
    private final AsMaterialService materialService;
    private final MaterialUtil materialUtil;
    private final EquipmentSparePartsService equipmentSparePartsService;
    private final AsAssetLogService assetLogService;
    private final CacheResourceUtil cacheResourceUtil;

    @Override
    public IPage<MaintainTaskDto> pageTask(MaintainTaskQueryDto query) {
        // 处理范围类型
        if (StrUtil.isNotEmpty(query.getRangeType())) {
            QueryConditionDto queryConditionDto = handleRangeType(query.getRangeType());
            if (queryConditionDto != null) {
                List<QueryConditionDto> conditions = query.getConditions();
                if (CollUtil.isEmpty(conditions)) {
                    conditions = new ArrayList<>();
                }
                conditions.add(queryConditionDto);
                query.setConditions(conditions);
            }
        }
        String tableAlias = "task";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<?> page = conditionResolver.buildOrderSort(tableAlias, query);
        return this.getBaseMapper().pageTask(page, query, conditions);
    }

    private QueryConditionDto handleRangeType(String rangeType) {
        QueryConditionDto conditionDto = new QueryConditionDto();
        conditionDto.setCode("planStartTime")
                .setName("计划应开始时间")
                .setQuery(QueryConditionType.BETWEEN.getCode())
                .setType(AssetConstant.ED_DATETIME);
        LocalDate now = LocalDate.now();
        String nowStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (TaskRangeTypeEnum.WEEK.getCode().equals(rangeType)) {
            LocalDate endDate = now.plusDays(7);
            return conditionDto.setQueryData(ListUtil.of(nowStr, endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        } else if (TaskRangeTypeEnum.MONTH.getCode().equals(rangeType)) {
            LocalDate endDate = now.plusMonths(1);
            return conditionDto.setQueryData(ListUtil.of(nowStr, endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        } else if (TaskRangeTypeEnum.SEASON.getCode().equals(rangeType)) {
            LocalDate endDate = now.plusMonths(3);
            return conditionDto.setQueryData(ListUtil.of(nowStr, endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        } else if (TaskRangeTypeEnum.YEAR.getCode().equals(rangeType)) {
            LocalDate endDate = now.plusYears(1);
            return conditionDto.setQueryData(ListUtil.of(nowStr, endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        }
        return null;
    }

    @Override
    public JSONObject equipmentInfo(Long orderId) {
        AsEquipmentMaintainTask task = getById(orderId);
        if (task == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "保养任务不存在");
        }
        return task.getAssetSnapshotData();
    }

    @Override
    public MaintainTaskDto info(Long orderId) {
        AsEquipmentMaintainTask task = getById(orderId);
        if (task == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "保养任务不存在");
        }
        MaintainTaskDto taskDto = new MaintainTaskDto();
        BeanUtil.copyProperties(task, taskDto);
        return taskDto;
    }

    @Override
    public IPage<MaterialSparePartsDto> sparePartsPage(MaintainTaskSparePartsQueryDto queryDto) {
        Page<AsEquipmentReplacement> page = equipmentReplacementService.page(queryDto.buildIPage(),
                Wrappers.lambdaQuery(AsEquipmentReplacement.class).eq(AsEquipmentReplacement::getMaintainTaskId, queryDto.getOrderId())
        );
        List<MaterialSparePartsDto> sparePartsDtos = page.getRecords().stream()
                .map(replacement -> {
                    MaterialSparePartsDto sparePartsDto = new MaterialSparePartsDto();
                    sparePartsDto.setMaterialId(replacement.getMaterialId());
                    sparePartsDto.setReferNum(replacement.getReferNum());
                    sparePartsDto.setMaterialData(replacement.getMaterialSnapshotData());
                    sparePartsDto.setCkNum(replacement.getCkNum());
                    sparePartsDto.setCkPrice(replacement.getCkPrice());
                    sparePartsDto.setCkUnitPrice(replacement.getCkUnitPrice());
                    return sparePartsDto;
                }).collect(Collectors.toList());
        IPage<MaterialSparePartsDto> result = new Page<>();
        BeanUtil.copyProperties(result, page);
        result.setRecords(sparePartsDtos);
        return result;
    }

    @Override
    public List<MaterialSparePartsDto> sparePartsList(MaintainTaskSparePartsQueryDto queryDto) {
        List<MaterialSparePartsDto> result;
        if (queryDto.getOutRepo() == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "仓库不可为空");
        }
        AsEquipmentMaintainTask task = getOne(Wrappers.lambdaQuery(AsEquipmentMaintainTask.class)
                .select(AsEquipmentMaintainTask::getAssetId)
                .eq(AsEquipmentMaintainTask::getId, queryDto.getOrderId()));
        if (task == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "保养任务不存在");
        }
        List<AsEquipmentReplacement> list = equipmentReplacementService.list(
                Wrappers.lambdaQuery(AsEquipmentReplacement.class)
                        .eq(AsEquipmentReplacement::getMaintainTaskId, queryDto.getOrderId())
        );
        // 如果备件为空，则查询系统默认
        if (CollUtil.isEmpty(list)) {
            result = equipmentSparePartsService
                    .stockList(task.getAssetId(), queryDto.getOutRepo(), ListUtil.empty());
        } else {
            List<AsEquipmentSpareParts> sparePartsList = equipmentSparePartsService.list(Wrappers.lambdaQuery(AsEquipmentSpareParts.class)
                    .eq(AsEquipmentSpareParts::getAssetId, task.getAssetId()));
            Map<Long, BigDecimal> referNumMap = sparePartsList.stream().collect(Collectors.toMap(AsEquipmentSpareParts::getMaterialId, AsEquipmentSpareParts::getNum, (v1, v2) -> v2));
            List<Long> materialIds = list.stream().map(AsEquipmentReplacement::getMaterialId).collect(Collectors.toList());
            // 重新拉取库存
            MaterialStockQueryDto materialStockQueryDto = new MaterialStockQueryDto()
                    .setIncludeMaterialIds(materialIds)
                    .setRepositoryId(queryDto.getOutRepo());
            materialStockQueryDto.setPageNum(1);
            materialStockQueryDto.setPageSize(Integer.MAX_VALUE);
            IPage<MaterialStockDto> materialStockDtoIPage = materialStockService.stockPage(materialStockQueryDto);
            Map<Long, MaterialStockDto> stockMap = materialStockDtoIPage.getRecords().stream()
                    .collect(Collectors.toMap(MaterialStockDto::getMaterialId, k -> k, (k1, k2) -> k1));
            result = list.stream()
                    .map(replacement -> {
                        MaterialSparePartsDto sparePartsDto = new MaterialSparePartsDto();
                        sparePartsDto.setMaterialId(replacement.getMaterialId());
                        sparePartsDto.setReferNum(referNumMap.getOrDefault(replacement.getMaterialId(), BigDecimal.ZERO));
                        sparePartsDto.setRepositoryId(queryDto.getOutRepo());
                        sparePartsDto.setMaterialData(replacement.getMaterialSnapshotData());
                        sparePartsDto.setCkNum(replacement.getCkNum());
                        if (stockMap.containsKey(replacement.getMaterialId())) {
                            MaterialStockDto stockDto = stockMap.get(replacement.getMaterialId());
                            sparePartsDto.setCurrentQuantity(stockDto.getCurrentQuantity());
                            sparePartsDto.setAvgPrice(stockDto.getAvgPrice());
                        }
                        return sparePartsDto;
                    }).collect(Collectors.toList());
        }
        // 过滤掉仓库库存为0的耗材。
        return result.stream().filter(r -> BigDecimal.ZERO.compareTo(r.getCurrentQuantity()) < 0).collect(Collectors.toList());
    }

    @Override
    public JSONObject materialInfo(Long orderId, Long materialId) {
        AsEquipmentReplacement replacement = equipmentReplacementService.getOne(Wrappers.lambdaQuery(AsEquipmentReplacement.class)
                .eq(AsEquipmentReplacement::getMaintainTaskId, orderId)
                .eq(AsEquipmentReplacement::getMaterialId, materialId));
        if (replacement == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "使用备件不存在");
        }
        replacement.getMaterialSnapshotData()
                .fluentPut("referNum", replacement.getReferNum())
                .fluentPut("ckNum", replacement.getCkNum())
                .fluentPut("ckPrice", replacement.getCkPrice())
                .fluentPut("ckUnitPrice", replacement.getCkUnitPrice());
        return replacement.getMaterialSnapshotData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableSubmitTaskResult temporary(MaintainTaskCreateDto createDto) {
        return submit(createDto, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableSubmitTaskResult submit(MaintainTaskCreateDto createDto, boolean submit) {
        AsEquipmentMaintainTask maintainTask = getById(createDto.getId());
        BusinessExceptionUtil.checkNotNull(maintainTask, "设备保养任务不存在");
        if (maintainTask.getTaskStatus() == DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_FINISH ||
                maintainTask.getTaskStatus() == DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_CANCEL) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "保养任务已完成或已作废");
        }
        if (submit) {
            maintainTask.setTaskStatus(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_FINISH);
        } else {
            maintainTask.setTaskStatus(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_DOING);
        }

        // 校验并转义单据信息
        JSONObject orderData = createDto.getOrderData();
        verifyAndTranslation(orderData, submit);
        // 自动计算保养用时
        maintainTask.setMaintainDuration(validAndCalcDuration(
                orderData.getLong(MaintainTaskCreateDto.MAINTAIN_START_DATE),
                orderData.getLong(MaintainTaskCreateDto.MAINTAIN_END_DATE)));
        // 计算更换备件数据
        String replacementPart = orderData.getString(MaintainTaskCreateDto.REPLACEMENT_PART);
        if ("否".equals(replacementPart)) {
            // 不更换备件清理掉使用备件
            createDto.getSpareParts().clear();
            equipmentReplacementService.remove(Wrappers.lambdaQuery(AsEquipmentReplacement.class)
                    .eq(AsEquipmentReplacement::getMaintainTaskId, createDto.getId()));
            maintainTask.setTotalNum(BigDecimal.ZERO);
            maintainTask.setTotalPrice(BigDecimal.ZERO);
        } else {
            Long outRepo = orderData.getLong(MaintainTaskCreateDto.OUT_REPO);
            if (submit) {
                if (outRepo == null) {
                    throw new BusinessException(MaterialResultCode.STOCK_QUERY_PARAMS_ERROR, "出库仓库不能为空");
                }
                if (CollUtil.isEmpty(createDto.getSpareParts())) {
                    throw new BusinessException(MaterialResultCode.STOCK_QUERY_PARAMS_ERROR, "使用备件不能为空");
                }
            }
            // 页面提交的备件数据
            List<MaintainTaskSparePartsDto> spareParts = createDto.getSpareParts();
            Map<Long, MaintainTaskSparePartsDto> sparePartsMap = spareParts.stream()
                    .collect(Collectors.toMap(MaintainTaskSparePartsDto::getMaterialId, k -> k, (k1, k2) -> k2));
            // 库存信息
            List<MaterialSparePartsDto> materialSparePartsDtoList = new ArrayList<>();
            if (CollUtil.isNotEmpty(sparePartsMap.keySet())) {
                materialSparePartsDtoList = equipmentSparePartsService.stockList(maintainTask.getAssetId(), outRepo, new ArrayList<>(sparePartsMap.keySet()));
            }
            Map<Long, JSONObject> materialSnapshotMap = materialService.getMaterialSnapshot(new ArrayList<>(sparePartsMap.keySet()));

            // 出库总数量
            BigDecimal totalNum = BigDecimal.ZERO;
            // 出库总金额
            BigDecimal totalPrice = BigDecimal.ZERO;
            // 库存调整集合
            List<AdjustStockDto.AdjustDetail> adjustDetailList = new ArrayList<>();
            // 调整备件集合
            List<AsEquipmentReplacement> saveReplacementList = new ArrayList<>();
            for (MaterialSparePartsDto stock : materialSparePartsDtoList) {
                MaintainTaskSparePartsDto inputSpareParts = sparePartsMap.get(stock.getMaterialId());
                AsEquipmentReplacement replacement = new AsEquipmentReplacement();
                replacement.setReferNum(stock.getReferNum());
                replacement.setMaintainTaskId(maintainTask.getId());
                replacement.setMaterialId(stock.getMaterialId());
                replacement.setCkNum(inputSpareParts.getCkNum());
                // 提交需要计算
                if (submit) {
                    replacement.setCkUnitPrice(stock.getAvgPrice());
                    replacement.setCkPrice(replacement.getCkUnitPrice().multiply(inputSpareParts.getCkNum()));
                    // 扣减库存
                    adjustDetailList.add(new AdjustStockDto.AdjustDetail(stock.getMaterialId(), replacement.getCkNum().negate(), replacement.getCkPrice().negate()));
                    totalNum = totalNum.add(replacement.getCkNum());
                    totalPrice = totalPrice.add(replacement.getCkPrice());
                }
                replacement.setMaterialSnapshotData(materialSnapshotMap.get(stock.getMaterialId()));
                saveReplacementList.add(replacement);
            }

            equipmentReplacementService.remove(Wrappers.lambdaQuery(AsEquipmentReplacement.class)
                    .eq(AsEquipmentReplacement::getMaintainTaskId, createDto.getId()));
            equipmentReplacementService.saveBatch(saveReplacementList);

            // 扣减库存
            if (submit) {
                AdjustStockDto adjustStock = new AdjustStockDto();
                adjustStock.setOrderId(maintainTask.getId())
                        .setOrderNo(maintainTask.getTaskNo())
                        .setCreateBy(LoginUserThreadLocal.getCurrentUserId())
                        .setActionType(MaterialConstant.ACTION_TYPE_EQUIPMENT_MAINTAIN)
                        .setAdjustList(adjustDetailList)
                        .setRepositoryId(outRepo);
                materialStockService.adjustStock(adjustStock, LoginUserThreadLocal.getCompanyId());
                maintainTask.setTotalNum(totalNum);
                maintainTask.setTotalPrice(totalPrice);
            }
        }
        // 写入保养内容
        if (CollUtil.isNotEmpty(createDto.getMaintainContent())) {
            createDto.getOrderData().put(MAINTAIN_CONTENT, createDto.getMaintainContent());
        }
        maintainTask.setOrderData(orderData);
        updateById(maintainTask);
        if (submit) {
            saveActionContent(maintainTask);
        }
        return new AuditableSubmitTaskResult().setOrderNo(maintainTask.getTaskNo());
    }

    private void saveActionContent(AsEquipmentMaintainTask maintainTask) {
        List<String> textList = new ArrayList<>();
        JSONObject orderData = maintainTask.getOrderData();
        try {
            Long maintainStartDate = orderData.getLong("maintainStartDate");
            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(maintainStartDate / 1000, 0, ZoneOffset.of("+8"));
            textList.add("保养开始时间：" + dateTime.toLocalDate());
        } catch (Exception e) {
            textList.add("保养开始时间：----");
        }
        try {
            Long maintainEndDate = orderData.getLong("maintainEndDate");
            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(maintainEndDate / 1000, 0, ZoneOffset.of("+8"));
            textList.add("保养结束时间：" + dateTime.toLocalDate());
        } catch (Exception e) {
            textList.add("保养结束时间：----");
        }
        Long maintainUser = maintainTask.getMaintainUser();
        String userNameAndCode = cacheResourceUtil.getUserNameAndCode(maintainUser);
        textList.add("保养负责人:" + (StrUtil.isNotEmpty(userNameAndCode) ? userNameAndCode : "----"));

        List<MaintainOrderPlanContentDto> maintainContentList = orderData.getJSONArray(MAINTAIN_CONTENT).toJavaList(MaintainOrderPlanContentDto.class);
        if (CollUtil.isNotEmpty(maintainContentList)) {
            for (int i = 0; i < maintainContentList.size(); i++) {
                MaintainOrderPlanContentDto planContentDto = maintainContentList.get(i);
                StringBuffer str = new StringBuffer();
                str.append("保养项目").append(i + 1).append("：");
                str.append(planContentDto.getProject());
                if (ObjectUtil.isNotEmpty(planContentDto.getIsNormal())) {
                    str.append("-").append(planContentDto.getIsNormal() == 1 ? "正常" : "异常");
                } else {
                    str.append("-").append("未执行");
                }
                if (ObjectUtil.isNotEmpty(planContentDto.getRemark())) {
                    str.append("-").append(planContentDto.getRemark());
                }
                textList.add(str.toString());
            }
        }
        textList.add("保养花费：" + maintainTask.getTotalPrice() + "元");
        textList.add("保养时长：" + maintainTask.getMaintainDuration() + "H");
        AsAssetLog asAssetLog = new AsAssetLog()
                .setAssetId(maintainTask.getAssetId())
                .setActionType(EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK)
                .setOrderNo(maintainTask.getTaskNo())
                .setOrderId(maintainTask.getId())
                .setActionName("设备保养")
                .setHandleTime(LocalDateTime.now())
                .setActionContent(String.join("；", textList));
        assetLogService.save(asAssetLog);
    }


    private Integer validAndCalcDuration(Long startTime, Long endTime) {
        if (ObjectUtil.isAllNotEmpty(startTime, endTime)) {
            try {
                if (startTime > endTime) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "开始时间不得大于结束时间");
                }
                LocalDateTime start = LocalDateTime.ofEpochSecond(startTime / 1000, 0, ZoneOffset.of("+8"));
                LocalDateTime end = LocalDateTime.ofEpochSecond(endTime / 1000, 0, ZoneOffset.of("+8"));
                Duration between = Duration.between(start, end);
                BigDecimal hours = new BigDecimal(between.toMinutes()).divide(new BigDecimal("60"), 0, RoundingMode.HALF_UP);
                return hours.intValue();
            } catch (Exception e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "日期格式不正确");
            }
        }
        return null;
    }

    private void verifyAndTranslation(JSONObject orderData, boolean submit) {
        FormVO orderFormVO = formService.getTplByType(OrderFormTypeEnum.EQUIPMENT_MAINTAIN_TASK.getBizType());
        if (orderFormVO == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误");
        }
        // 清理属性
        FormFieldConvert.clearInvalidField(orderData, orderFormVO.getFormFields());
        if (submit) {
            // 动态表单校验
            String replacementPart = orderData.getString(MaintainTaskCreateDto.REPLACEMENT_PART);
            if ("否".equals(replacementPart)) {
                List<FormFieldCO> formFieldCOS = orderFormVO.getFormFields().stream()
                        .filter(f -> !f.isHidden())
                        .filter(f -> !(FormFieldCO.SPLIT_LINE.equals(f.getFieldType()) || MaintainTaskCreateDto.OUT_REPO.equals(f.getFieldCode())))
                        .collect(Collectors.toList());
                formService.fieldValidator(orderData, formFieldCOS);
            } else {
                FormValidatorCmd orderFormValidatorCmd = new FormValidatorCmd(orderData, orderFormVO.getFormId(),
                        Convert.toStr(LoginUserThreadLocal.getCompanyId()));
                formService.validatorForm(orderFormValidatorCmd);
            }
        }
        // translate
        materialUtil.translateMaterialJson(orderData, FormFieldConvert.convertField(orderFormVO.getFormFields()));
    }


    @Override
    public AuditableCancelTaskResult cancel(List<Long> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return new AuditableCancelTaskResult();
        }
        List<AsEquipmentMaintainTask> maintainTask = listByIds(orderIds);
        long count = maintainTask.stream()
                .filter(t -> t.getTaskStatus() != DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING &&
                        t.getTaskStatus() != DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_EXPIRE).count();
        if (count > 0) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "状态非【待保养】或【已逾期】");
        }

        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        boolean isAdmin = BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin());
        long cannotOpt = maintainTask.stream()
                .filter(t -> !isAdmin && !currentUserId.equals(t.getMaintainUser())).count();
        if (cannotOpt > 0) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "只有保养负责人或超管才能作废");
        }

        if (CollUtil.isNotEmpty(maintainTask)) {
            List<String> taskNos = maintainTask.stream().filter(f ->
                    DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING == f.getTaskStatus()
                            || DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_EXPIRE == f.getTaskStatus())
                    .map(AsEquipmentMaintainTask::getTaskNo).collect(Collectors.toList());
            // 只更新【待保养】和【已逾期】为【已作废】
            update(Wrappers.lambdaUpdate(AsEquipmentMaintainTask.class)
                    .set(AsEquipmentMaintainTask::getTaskStatus, DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_CANCEL)
                    .in(AsEquipmentMaintainTask::getTaskStatus, ListUtil.of(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING,
                            DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_EXPIRE))
                    .in(AsEquipmentMaintainTask::getId, orderIds));
            return new AuditableCancelTaskResult().setOrderNos(taskNos);
        }
        return new AuditableCancelTaskResult();
    }

    @Override
    public AuditableResumeTaskResult resume(List<Long> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return new AuditableResumeTaskResult();
        }
        List<AsEquipmentMaintainTask> maintainTask = listByIds(orderIds);
        long count = maintainTask.stream()
                .filter(t -> t.getTaskStatus() != DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_CANCEL).count();
        if (count > 0) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "状态非【已作废】");
        }

        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        boolean isAdmin = BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin());
        long cannotOpt = maintainTask.stream()
                .filter(t -> !isAdmin && !currentUserId.equals(t.getMaintainUser())).count();
        if (cannotOpt > 0) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "只有保养负责人或超管才能恢复");
        }

        if (CollUtil.isNotEmpty(maintainTask)) {
            List<String> taskNos = maintainTask.stream().filter(f ->
                    DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_CANCEL == f.getTaskStatus())
                    .map(AsEquipmentMaintainTask::getTaskNo).collect(Collectors.toList());
            // 只更新【已作废】为【待保养】
            update(Wrappers.lambdaUpdate(AsEquipmentMaintainTask.class)
                    .set(AsEquipmentMaintainTask::getTaskStatus, DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING)
                    .eq(AsEquipmentMaintainTask::getTaskStatus, DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_CANCEL)
                    .in(AsEquipmentMaintainTask::getId, orderIds));
            return new AuditableResumeTaskResult().setOrderNos(taskNos);
        }
        return new AuditableResumeTaskResult();
    }

    @Override
    public List<Long> cateNewAssetIds(Long planId, Long companyId) {
        return getBaseMapper().cateNewAssetIds(planId, companyId);
    }

    @Override
    public Boolean userHasTask(Long userId) {
        if (Objects.isNull(userId)) {
            return false;
        }
        long count = this.count(
                Wrappers.lambdaQuery(AsEquipmentMaintainTask.class)
                        .notIn(AsEquipmentMaintainTask::getTaskStatus, Lists.newArrayList(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_CANCEL, DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_FINISH))
                        .eq(AsEquipmentMaintainTask::getMaintainUser, userId)
        );
        return count > 0;
    }
}
