package com.niimbot.asset.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.equipment.*;
import com.niimbot.asset.equipment.model.AsEquipmentSpareParts;
import com.niimbot.material.MaterialSparePartsDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/11 下午3:36
 */
public interface EquipmentSparePartsService extends IService<AsEquipmentSpareParts> {

    /**
     * 根据资产id查询备件信息
     * @param assetIds
     * @return
     */
    List<EquipmentSparePartsDto> queryByAssetId(List<Long> assetIds);

    /**
     * 创建备件信息
     * @param sparePartsCreateDto
     * @return
     */
    Boolean createSpareParts(SparePartsCreateDto sparePartsCreateDto);

    /**
     * 批量编辑备件信息
     * @param sparePartsCreateDto
     * @return
     */
    Boolean modifySpareParts(SparePartsCreateDto sparePartsCreateDto);

    /**
     * 删除备件信息
     * @param sparePartsDropDto
     * @return
     */
    Boolean dropSpareParts(SparePartsDropDto sparePartsDropDto);

    /**
     * 通过资产id，查询耗材id
     * @param assetId
     * @return
     */
    List<SparePartsMaterialDto> queryMaterialByAssetId(Long assetId);

    /**
     * 设备履历信息
     * @param assetId
     * @return
     */
    SparePartsStatisticsDto statistics(Long assetId);

    /**
     * 查询备件库存信息
     * @param assetId
     * @param repoId
     * @param materialIds
     * @return
     */
    List<MaterialSparePartsDto> stockList(Long assetId, Long repoId, List<Long> materialIds);
}
