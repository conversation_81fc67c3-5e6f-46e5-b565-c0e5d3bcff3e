package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_equipment_spare_parts
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_spare_parts", autoResultMap = true)
public class AsEquipmentSpareParts implements Serializable {

    private static final long serialVersionUID = 1848568419624697139L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公司ID（租户ID）
     */
    @TenantFilterColumn
    private Long companyId;

    /**
     * 资产id
     */
    private Long assetId;

    /**
     * 耗材id
     */
    private Long materialId;

    /**
     * 备件数量
     */
    private BigDecimal num;

    /**
     * 安装部位
     */
    private String installLocation;

    /**
     * 是否删除 0-否  1-是
     */
    private Boolean isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}