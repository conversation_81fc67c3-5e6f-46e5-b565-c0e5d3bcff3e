package com.niimbot.asset.equipment.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.service.EquipmentMaintainTaskService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.equipment.AuditableCancelTaskResult;
import com.niimbot.equipment.AuditableResumeTaskResult;
import com.niimbot.equipment.AuditableSubmitTaskResult;
import com.niimbot.equipment.MaintainTaskCreateDto;
import com.niimbot.equipment.MaintainTaskDto;
import com.niimbot.equipment.MaintainTaskQueryDto;
import com.niimbot.equipment.MaintainTaskSparePartsQueryDto;
import com.niimbot.material.MaterialSparePartsDto;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/12 11:13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/equipment/maintain/task")
public class EquipmentMaintainTaskServiceController {

    private final EquipmentMaintainTaskService equipmentMaintainTaskService;
    private final RedissonClient redissonClient;

    @PostMapping("/page")
    public IPage<MaintainTaskDto> page(@RequestBody MaintainTaskQueryDto query) {
        return equipmentMaintainTaskService.pageTask(query);
    }

    @GetMapping("/equipment/{orderId}")
    public JSONObject equipmentInfo(@PathVariable("orderId") Long orderId) {
        return equipmentMaintainTaskService.equipmentInfo(orderId);
    }

    @GetMapping("/{orderId}")
    public MaintainTaskDto info(@PathVariable("orderId") Long orderId) {
        return equipmentMaintainTaskService.info(orderId);
    }

    @GetMapping("/replacement/sparePartsPage")
    public IPage<MaterialSparePartsDto> sparePartsPage(MaintainTaskSparePartsQueryDto queryDto) {
        return equipmentMaintainTaskService.sparePartsPage(queryDto);
    }

    @GetMapping("/replacement/sparePartsList")
    public List<MaterialSparePartsDto> sparePartsList(MaintainTaskSparePartsQueryDto queryDto) {
        return equipmentMaintainTaskService.sparePartsList(queryDto);
    }

    @GetMapping("/material/{orderId}/{materialId}")
    public JSONObject materialInfo(@PathVariable("orderId") Long orderId,
                                   @PathVariable("materialId") Long materialId) {
        return equipmentMaintainTaskService.materialInfo(orderId, materialId);
    }

    @PostMapping("/temporary")
    public AuditableSubmitTaskResult temporary(@RequestBody MaintainTaskCreateDto createDto) {
        return equipmentMaintainTaskService.temporary(createDto);
    }

    @PostMapping("/submit")
    public AuditableSubmitTaskResult submit(@RequestBody MaintainTaskCreateDto createDto) {
        RLock lock = redissonClient.getLock("material_stock_lock:" + LoginUserThreadLocal.getCompanyId());
        try {
            lock.lock(30, TimeUnit.SECONDS);
            return equipmentMaintainTaskService.submit(createDto, true);
        } finally {
            lock.unlock();
        }
    }

    @PostMapping("/cancel")
    public AuditableCancelTaskResult cancel(@RequestBody List<Long> orderIds) {
        return equipmentMaintainTaskService.cancel(orderIds);
    }

    @PostMapping("/resume")
    public AuditableResumeTaskResult resume(@RequestBody List<Long> orderIds) {
        return equipmentMaintainTaskService.resume(orderIds);
    }

    @GetMapping("/userHasTask")
    public Boolean userHasTask(@RequestParam("userId") Long userId) {
        return equipmentMaintainTaskService.userHasTask(userId);
    }

}
