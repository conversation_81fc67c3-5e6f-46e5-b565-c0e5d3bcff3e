package com.niimbot.asset.equipment.resolver;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.service.EquipmentOrderService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.equipment.EntMatPlanSearch;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 设备保养计划sql解析器
 *
 * <AUTHOR>
 * @date 2023/10/12 11:39
 */
@Component
public class MySqlEquipmentMaintainPlanConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {

    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.plan_data ->> '$.%s'";
    public static final String DATE_JSON_SQL_SEGMENT_TPL = "LPAD(%s.plan_data ->> '$.%s', 13, 0)";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.plan_data ->> '$.%s' as DECIMAL(20,4))";

    @Autowired
    private EquipmentOrderService equipmentOrderService;
//    @Autowired
//    private EquipmentDataPermResolver equipmentDataPermResolver;

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
//        equipmentDataPermResolver.resolvePerms(joiner, tableAlias, OrderFormTypeEnum.EQUIPMENT_MAINTAIN_PLAN.getCode());
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                // 特殊处理planStatus
                if ("planStatus".equals(condition.getCode()) && StrUtil.isNotEmpty(Convert.toStr(condition.getQueryData()))) {
                    Integer planStatus = Convert.toInt(condition.getQueryData(), -1);
                    if (EquipmentConstant.PLAN_STATUS_IS_STOP == planStatus) {
                        joiner.add(" and " + tableAlias + ".plan_status = " + planStatus);
                    } else {
                        long now = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        joiner.add(" and " + tableAlias + ".plan_status in (1, 3) " +
                                " AND (IF(" + tableAlias + ".plan_data->>'$.task_type' = '一次性任务', " +
                                " IF(" + tableAlias + ".plan_data->>'$.taskBeginTime' < " + now + ", 3, 1) , " +
                                " IF(" + tableAlias + ".plan_data->>'$.taskEndTime' < " + now + ", 3, 1))) = " + planStatus);
                    }
                } else {
                    String sqlField = getSqlField(tableAlias, condition);
                    String conditionStr = super.doResolveQueryCondition(
                            sqlField,
                            condition.getQuery(),
                            condition.getQueryData(),
                            condition.getCode(),
                            condition.getType(),
                            QueryFieldConstant.EQUIPMENT_PLAN_EXT_FIELD);
                    if (StrUtil.isNotBlank(conditionStr)) {
                        joiner.add(conditionStr);
                    }
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition) {
        if (QueryFieldConstant.EQUIPMENT_PLAN_EXT_FIELD.containsKey(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        }
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, code);
    }

    public Page<?> buildOrderSort(String tableAlias, EntMatPlanSearch queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_equipment_maintain_plan";
        }
        Page<?> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = equipmentOrderService.sortField(EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_PLAN);
        Map<String, String> codeAndType = querySort.getSortList()
                .stream()
                .collect(Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.EQUIPMENT_PLAN_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.plan_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }

}
