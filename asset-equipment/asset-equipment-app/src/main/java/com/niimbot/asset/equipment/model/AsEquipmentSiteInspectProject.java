package com.niimbot.asset.equipment.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 设备巡检项目
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_site_inspect_project", autoResultMap = true)
public class AsEquipmentSiteInspectProject {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TenantFilterColumn
    private Long companyId;

    private String projectName;

    private String projectCode;

    private String content;

    private Boolean enablePhoto;

    private Boolean enableAlarm;

    /**
     * 填写格式类型：1-数值，2-下拉单选，3-下拉多选，4-单行文本，5-多行文本
     */
    private Integer configType;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject configData;

    @TableLogic
    private Boolean isDelete;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
