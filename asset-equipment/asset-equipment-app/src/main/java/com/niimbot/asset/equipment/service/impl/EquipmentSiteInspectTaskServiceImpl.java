package com.niimbot.asset.equipment.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectPlanMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTask;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.equipment.EntSntPlan;
import com.niimbot.equipment.EntSntPlanData;
import com.niimbot.equipment.EntSntPlanDetails;
import com.niimbot.equipment.SiteInspectTaskDetailExportDto;
import com.niimbot.equipment.SiteInspectTaskEditExecutorsDto;
import com.niimbot.equipment.SiteInspectTaskInfoDto;
import com.niimbot.equipment.SiteInspectTaskPageDto;
import com.niimbot.equipment.SiteInspectTaskQueryDto;
import com.niimbot.equipment.SiteInspectTaskStatisticsDto;
import com.niimbot.system.AuditableOperateResult;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:27
 */
@Service
@RequiredArgsConstructor
public class EquipmentSiteInspectTaskServiceImpl extends ServiceImpl<AsEquipmentSiteInspectTaskMapper, AsEquipmentSiteInspectTask> implements EquipmentSiteInspectTaskService {

    private static final String EDIT_EXECUTORS_TPL = "巡检任务“{}”原巡检人{}转派给{}";

    private final AsEquipmentSiteInspectPlanMapper siteInspectPlanMapper;
    private final CacheResourceUtil cacheResource;
    private final OrgService orgService;

    @Override
    public IPage<SiteInspectTaskPageDto> taskPage(SiteInspectTaskQueryDto query) {
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        Long currentUserId = null;
        if (!BooleanUtil.isTrue(cusUser.getIsAdmin())) {
            currentUserId = cusUser.getId();
        }
        if (CollUtil.isNotEmpty(query.getTaskStatus())) {
            // 如果同时包含进行中和已过期的，忽略
            if (query.getTaskStatus().contains(Convert.toStr(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_EXPIRE))
                    && query.getTaskStatus().contains(Convert.toStr(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_WAITING))) {
                query.getTaskStatus().remove(Convert.toStr(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_EXPIRE));
                // ignore
            } else if (query.getTaskStatus().contains(Convert.toStr(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_EXPIRE))) {
                query.getTaskStatus().remove(Convert.toStr(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_EXPIRE));
                query.getTaskStatus().add(Convert.toStr(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_WAITING));
                return getBaseMapper().taskPage(query.buildIPage(), query, currentUserId, DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_EXPIRE);
            } else if (query.getTaskStatus().contains(Convert.toStr(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_WAITING))) {
                return getBaseMapper().taskPage(query.buildIPage(), query, currentUserId, DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_WAITING);
            }
        }
        return getBaseMapper().taskPage(query.buildIPage(), query, currentUserId, null);
    }

    @Override
    public SiteInspectTaskInfoDto info(Long taskId, Boolean full) {
        SiteInspectTaskInfoDto taskInfoDto = new SiteInspectTaskInfoDto();
        AsEquipmentSiteInspectTask task = getById(taskId);
        BusinessExceptionUtil.checkNotNull(task, "巡检任务不存在");
        taskInfoDto.setTaskId(task.getId())
                .setTaskStatus(task.getTaskStatus())
                .setTaskName(task.getTaskName())
                .setTaskNo(task.getTaskNo())
                .setRouteType(task.getRouteType())
                .setPlanFinishTime(task.getPlanFinishTime())
                .setFinishTime(task.getFinishTime())
                .setCreateTime(task.getCreateTime())
                .setManagers(task.getManagers())
                .setExecutors(task.getExecutors())
                .setInspectNum(task.getInspectNum());
        // 写入额外信息
        if (BooleanUtil.isTrue(full)) {
            EntSntPlan plan = siteInspectPlanMapper.selectOne(task.getPlanId(), true);
            if (plan != null) {
                EntSntPlanData entSntPlanData = plan.getPlanData().toJavaObject(EntSntPlanData.class);
                if (entSntPlanData != null) {
                    taskInfoDto.setPlanNo(plan.getPlanNo())
                            .setPlanName(entSntPlanData.getPlanName())
                            .setTaskType(entSntPlanData.getTaskType())
                            .setTaskCount(entSntPlanData.getTaskCount())
                            .setPeriodic(entSntPlanData.getPeriodic())
                            .setPlanBeginTime(entSntPlanData.getPlanBeginTime())
                            .setPlanEndTime(entSntPlanData.getPlanEndTime())
                            .setImages(entSntPlanData.getImages())
                            .setAttachment(entSntPlanData.getAttachment())
                            .setRemark(entSntPlanData.getRemark());
                }
            }
            List<EntSntPlanDetails.Executor> executorList = new ArrayList<>();
            Map<Long, List<AsOrg>> userOrgMap = orgService.listOrgByEmpIds(new ArrayList<>(task.getExecutors()));
            task.getExecutors().forEach(id -> {
                List<AsOrg> orgList = userOrgMap.getOrDefault(id, new ArrayList<>());
                executorList.add(new EntSntPlanDetails.Executor(id, cacheResource.getUserNameAndCode(id), orgList.stream().map(AsOrg::getOrgName).collect(Collectors.joining(" "))));
            });
            taskInfoDto.setExecutorList(executorList);
        }
        return taskInfoDto;
    }

    @Override
    public SiteInspectTaskStatisticsDto taskStatistics() {
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        Long currentUserId = null;
        if (!BooleanUtil.isTrue(cusUser.getIsAdmin())) {
            currentUserId = cusUser.getId();
        }
        return getBaseMapper().taskStatistics(currentUserId);
    }

    @Override
    public boolean executeUpdateTask(Long taskId, int projectBeforeStatus, int projectStatus) {
        AsEquipmentSiteInspectTask task = getById(taskId);
        BusinessExceptionUtil.checkNotNull(task, "巡检任务不存在");

        // 如果未检，则累加
        if (projectBeforeStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_UNCHECK) {
            task.setCheckedNum(task.getCheckedNum() + 1);
            if (projectStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL) {
                task.setAbnormalNum(task.getAbnormalNum() + 1);
            }
        } else if (projectBeforeStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL &&
                projectStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL) {
            task.setAbnormalNum(task.getAbnormalNum() + 1);
        } else if (projectBeforeStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL &&
                projectStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL) {
            task.setAbnormalNum(task.getAbnormalNum() - 1);
        }
        task.setTaskStatus(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_DOING);
        return updateById(task);
    }

    @Override
    public List<AuditableOperateResult> cancelTask(List<Long> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return ListUtil.empty();
        }
        List<AsEquipmentSiteInspectTask> taskList = list(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTask.class)
                .in(AsEquipmentSiteInspectTask::getTaskStatus, CAN_OPT_STATUS)
                .in(AsEquipmentSiteInspectTask::getId, taskIds));

        update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTask.class)
                .set(AsEquipmentSiteInspectTask::getTaskStatus, DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_CANCEL)
                .in(AsEquipmentSiteInspectTask::getTaskStatus, CAN_OPT_STATUS)
                .in(AsEquipmentSiteInspectTask::getId, taskIds));
        return taskList.stream().map(v -> new AuditableOperateResult(v.getTaskNo(), v.getTaskName())).collect(Collectors.toList());
    }

    @Override
    public List<AuditableOperateResult> editExecutors(SiteInspectTaskEditExecutorsDto editExecutorsList) {
        List<Long> taskIds = editExecutorsList.getTaskIds();
        List<Long> executors = editExecutorsList.getExecutors();

        List<AsEquipmentSiteInspectTask> siteInspectTaskList = list(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTask.class)
                .in(AsEquipmentSiteInspectTask::getTaskStatus, CAN_OPT_STATUS)
                .in(AsEquipmentSiteInspectTask::getId, taskIds));

        List<String> auditableList = new ArrayList<>();
        if (CollUtil.isNotEmpty(siteInspectTaskList)) {
            Map<Long, String> userNameCache = new HashMap<>();
            String afterExecutors = String.join("，", getEmpNameList(executors, userNameCache));
            for (AsEquipmentSiteInspectTask siteInspectTask : siteInspectTaskList) {
                String taskInfo = siteInspectTask.getTaskName() + " " + siteInspectTask.getTaskNo();
                String beforeExecutors = String.join("，", getEmpNameList(siteInspectTask.getExecutors(), userNameCache));
                auditableList.add(StrUtil.format(EDIT_EXECUTORS_TPL, taskInfo, beforeExecutors, afterExecutors));
            }
        }

        update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTask.class)
                .set(AsEquipmentSiteInspectTask::getExecutors, JSONArray.toJSON(executors).toString())
                .in(AsEquipmentSiteInspectTask::getTaskStatus, CAN_OPT_STATUS)
                .in(AsEquipmentSiteInspectTask::getId, taskIds));
        return auditableList.stream().map(f -> new AuditableOperateResult(f, StrUtil.EMPTY)).collect(Collectors.toList());
    }

    private List<String> getEmpNameList(List<Long> empIds, Map<Long, String> userNameCache) {
        List<String> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(empIds)) {
            for (Long empId : empIds) {
                String userNameAndCode;
                if (userNameCache.containsKey(empId)) {
                    userNameAndCode = userNameCache.get(empId);
                } else {
                    userNameAndCode = cacheResource.getUserNameAndCode(empId);
                    userNameCache.put(empId, userNameAndCode);
                }
                if (StrUtil.isNotBlank(userNameAndCode)) {
                    result.add(userNameAndCode);
                }
            }
        }
        return result;
    }

    @Override
    public List<SiteInspectTaskDetailExportDto> detailExport(Long taskId) {
        List<SiteInspectTaskDetailExportDto> siteInspectTaskDetailExportDtos = getBaseMapper().detailExport(taskId);
        // 填充 executorsOrg
        Set<Long> empIds = siteInspectTaskDetailExportDtos.stream().filter(f -> f.getExecutors() != null)
                .map(SiteInspectTaskDetailExportDto::getExecutors).collect(Collectors.toSet());
        Map<Long, List<AsOrg>> userOrgMap = orgService.listOrgByEmpIds(new ArrayList<>(empIds));
        for (SiteInspectTaskDetailExportDto exportDto : siteInspectTaskDetailExportDtos) {
            List<AsOrg> orgList = userOrgMap.getOrDefault(exportDto.getExecutors(), new ArrayList<>());
            exportDto.setExecutorsOrgText(orgList.stream().map(k -> k.getOrgName() + "（" + k.getOrgCode() + "）").collect(Collectors.toList()));
        }
        return siteInspectTaskDetailExportDtos;
    }

}
