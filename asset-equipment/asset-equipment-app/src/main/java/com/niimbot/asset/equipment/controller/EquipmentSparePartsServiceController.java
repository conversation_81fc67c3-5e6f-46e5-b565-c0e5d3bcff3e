package com.niimbot.asset.equipment.controller;

import cn.hutool.core.collection.CollUtil;
import com.niimbot.asset.equipment.service.EquipmentSparePartsService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.equipment.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/11 下午4:15
 */
@RestController
@RequestMapping("server/equipment/spareParts")
public class EquipmentSparePartsServiceController {

    @Autowired
    private EquipmentSparePartsService equipmentSparePartsService;

    @GetMapping("/queryByAssetId")
    public List<EquipmentSparePartsDto> queryByAssetId(@RequestParam("assetIds") List<Long> assetIds) {
        return equipmentSparePartsService.queryByAssetId(assetIds);
    }

    @PostMapping("/create")
    public Boolean createSpareParts(@RequestBody SparePartsCreateDto sparePartsCreateDto) {
        //设备备件信息非空校验
        if (Objects.isNull(sparePartsCreateDto) || CollUtil.isEmpty(sparePartsCreateDto.getMaterialInfo())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "备件信息不能为空");
        }

        return equipmentSparePartsService.createSpareParts(sparePartsCreateDto);
    }

    @PostMapping("/modify")
    public Boolean modifySpareParts(@RequestBody SparePartsCreateDto sparePartsCreateDto) {
        //设备备件信息非空校验
        if (Objects.isNull(sparePartsCreateDto) || CollUtil.isEmpty(sparePartsCreateDto.getMaterialInfo())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "备件信息不能为空");
        }

        return equipmentSparePartsService.modifySpareParts(sparePartsCreateDto);
    }

    @PostMapping("/remove")
    public Boolean dropSpareParts(@RequestBody SparePartsDropDto sparePartsDropDto) {
        if (Objects.isNull(sparePartsDropDto.getAssetId()) || CollUtil.isEmpty(sparePartsDropDto.getMaterialIdList())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "备件信息不能为空");
        }

        return equipmentSparePartsService.dropSpareParts(sparePartsDropDto);
    }

    @GetMapping("/queryMaterialInfo")
    public List<SparePartsMaterialDto> queryMaterialInfo(@RequestParam("assetId") Long assetId) {
        return equipmentSparePartsService.queryMaterialByAssetId(assetId);
    }

    @GetMapping("/statistics")
    public SparePartsStatisticsDto statistics(@RequestParam("assetId") Long assetId) {
        return equipmentSparePartsService.statistics(assetId);
    }

}
