package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectPoint;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.equipment.EquipmentSiteInspectPointQueryDto;
import com.niimbot.system.PrintPointPageQueryDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AsEquipmentSiteInspectPointMapper extends BaseMapper<AsEquipmentSiteInspectPoint> {

    /**
     * 查询区域信息
     *
     * @param page     分页
     * @param queryDto 查询条件
     * @return 资产列表
     */
    IPage<AsEquipmentSiteInspectPointDto> pagePoint(IPage<Object> page, @Param("em") EquipmentSiteInspectPointQueryDto queryDto);

    List<AsEquipmentSiteInspectPointDto> pagePoint(@Param("em") EquipmentSiteInspectPointQueryDto queryDto);

    @Select("select max(point_code) from as_equipment_site_inspect_point WHERE point_code REGEXP '^[A-Z]{1}[0-9]{2,3}$'")
    String getMaxAreaCode();

    Integer getPointByAreaId(@Param("id") Long id);

    Integer getPlanCount(@Param("id") Long id);

    IPage<AsEquipmentSiteInspectPointDto> printPage(IPage<?> page, @Param("em") PrintPointPageQueryDto queryDto);

    /**
     * 查询点位信息
     *
     * @param id id
     * @return point
     */
    default AsEquipmentSiteInspectPoint selectByIdOrElseThrow(Long id) {
        AsEquipmentSiteInspectPoint point = this.selectById(id);
        Affirm.nonNull(point, "巡检点位不存在");
        return point;
    }

}
