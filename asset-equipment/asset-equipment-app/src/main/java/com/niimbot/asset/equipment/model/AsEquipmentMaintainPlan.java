package com.niimbot.asset.equipment.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.framework.dataperm.annonation.UserFilterColumn;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 设备保养计划
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_maintain_plan", autoResultMap = true)
public class AsEquipmentMaintainPlan {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业ID
     */
    @TenantFilterColumn
    private Long companyId;

    /**
     * 计划编号
     */
    private String planNo;

    /**
     * 计划状态 1-执行中 2-已停止
     */
    private Integer planStatus;

    /**
     * 设备选择范围类型 1-设备 2-分类
     */
    private Integer rangeType;

    /**
     * 保养表单信息
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject planData;

    /**
     * 保养内容信息
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray planContent;

    /**
     * 是否过滤已处理设备
     */
    private Boolean filterDispose;

    /**
     * 软删除标记
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @UserFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_EQUIPMENT, subBizCode = AssetConstant.AUTHORITY_CREATE_USER)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
