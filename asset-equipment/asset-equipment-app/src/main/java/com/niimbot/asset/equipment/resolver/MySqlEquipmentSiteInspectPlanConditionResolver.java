package com.niimbot.asset.equipment.resolver;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.service.EquipmentOrderService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.equipment.SearchEntSntPlan;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 设备巡检计划sql解析器
 *
 * <AUTHOR>
 * @date 2023/10/12 11:39
 */
@Component
public class MySqlEquipmentSiteInspectPlanConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {

    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.plan_data ->> '$.%s'";
    public static final String DATE_JSON_SQL_SEGMENT_TPL = "LPAD(%s.plan_data ->> '$.%s', 13, 0)";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.plan_data ->> '$.%s' as DECIMAL(20,4))";

    @Autowired
    private EquipmentOrderService equipmentOrderService;

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition);
                String conditionStr = super.doResolveQueryCondition(
                        sqlField,
                        condition.getQuery(),
                        condition.getQueryData(),
                        condition.getCode(),
                        condition.getType(),
                        QueryFieldConstant.EQUIPMENT_SITE_INSPECT_PLAN_EXT_FIELD);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition) {
        if (QueryFieldConstant.EQUIPMENT_SITE_INSPECT_PLAN_EXT_FIELD.containsKey(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        }
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, code);
    }

    public Page<?> buildOrderSort(String tableAlias, SearchEntSntPlan queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_equipment_site_inspect_plan";
        }
        Page<?> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = equipmentOrderService.sortField(EquipmentConstant.ORDER_TYPE_EQUIPMENT_SITE_INSPECT_PLAN);
        Map<String, String> codeAndType = querySort.getSortList()
                .stream()
                .collect(Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.EQUIPMENT_SITE_INSPECT_PLAN_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.plan_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }

}
