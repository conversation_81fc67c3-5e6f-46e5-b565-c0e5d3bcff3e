package com.niimbot.asset.equipment.message;

import com.google.common.collect.Lists;

import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainTaskMapper;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.PeriodCompanyMessageHandler;
import com.niimbot.equipment.EntMatTaskMsg;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;

/**
 * 设备保养任务消息
 *
 * <AUTHOR>
 */
public abstract class EntMatTaskMessageHandler extends PeriodCompanyMessageHandler {

    @Resource
    private CacheResourceUtil resourceUtil;

    @Resource
    private AsEquipmentMaintainTaskMapper taskMapper;

    @Resource
    private AssetService assetService;

    @Override
    protected boolean skip(MessageRuleCO rule) {
        return CollUtil.isEmpty(rule.getReceiverType());
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        List<EntMatTaskMsg> taskMsgs = taskMapper.selectListForMsg(rule.getCompanyId(), rule.getReminderTime(), rule.getCode());
        if (CollUtil.isEmpty(taskMsgs)) {
            return;
        }
        // 消息接受人列表
        Set<Long> cuzUserIds = new HashSet<>(8);
        // 自定义
        if (rule.includeCuzReceiverType()) {
            cuzUserIds.addAll(rule.resolveCuzReceiver());
        }
        // 按保养计划分组
        Map<Long, List<EntMatTaskMsg>> groupPlan = taskMsgs.stream().collect(Collectors.groupingBy(EntMatTaskMsg::getPlanId));
        groupPlan.forEach((k, v) -> {
            // 计划名称
            String planName = v.get(0).getPlanName();
            String planNo = v.get(0).getPlanNo();
            // 保养负责人
            Long maintainUserId = v.get(0).getMaintainUserId();
            // 任务按时间分组
            Map<LocalDateTime, List<EntMatTaskMsg>> groupStartTime = v.stream().collect(Collectors.groupingBy(EntMatTaskMsg::getPlanStartTime));
            groupStartTime.forEach((startTime, tasks) -> {
                String format = startTime.format(DatePattern.NORM_DATE_FORMATTER);
                Integer intervalDay = tasks.get(0).getIntervalDay();
                Set<Long> toUserIds = new HashSet<>(cuzUserIds);
                // 保养负责人
                if (rule.includeReceiverType(MessageConstant.ReceiverType.MAINTENANCE_PRINCIPAL)) {
                    toUserIds.add(maintainUserId);
                }
                // 资产所属管理员
                if (rule.includeReceiverType(MessageConstant.ReceiverType.ASSET_OWNER)) {
                    List<Long> assetIds = tasks.stream().map(EntMatTaskMsg::getAssetId).distinct().collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(assetIds)) {
                        List<AsAsset> assets = assetService.listByIds(assetIds);
                        if (CollUtil.isNotEmpty(assets)) {
                            Set<Long> ids = assets.stream().map(AsAsset::getManagerOwner).filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toSet());
                            toUserIds.addAll(ids);
                        }
                    }
                }
                Map<String, String> mapParams = new HashMap<>(3);
                mapParams.put(MessageConstant.Template.TASK_NAME, planName);
                mapParams.put(MessageConstant.Template.DAY, String.valueOf(Math.abs(intervalDay)));
                mapParams.put(MessageConstant.Template.URL, pcDomain + "/#/devices-task-list?maintainUser=" + maintainUserId + "&kw=" + planNo + "&planStartTime=" + format + "," + format);
                // 额外参数 maintainUser maintainUserName planStartTime
                Map<String, Object> commonExtMap = new HashMap<>(3);
                commonExtMap.put("kw", planNo);
                commonExtMap.put("maintainUser", maintainUserId);
                commonExtMap.put("maintainUserName", resourceUtil.getUserName(maintainUserId));
                commonExtMap.put("planStartTime", Lists.newArrayList(format, format));
                Body body = Body.builder()
                        .mapParam(mapParams)
                        .userIds(toUserIds)
                        .appExtMapParam(Convert.toMap(String.class, String.class, commonExtMap))
                        .commonExtMap(commonExtMap)
                        .build();
                bodies.add(body);
            });
        });
    }

    /**
     * 【精臣云资产】￥{计划名称}的保养任务在${day}天后将到期，请及时关注，点击查看详情。
     */
    @Component
    public static class EntMatTaskDueMsg extends EntMatTaskMessageHandler {
        @Override
        public String code() {
            return MessageConstant.Code.SBBYRWDQTX.getCode();
        }
    }

    /**
     * 【精臣云资产】￥{计划名称}的保养任务已过期${day}天，请及时关注，点击查看详情。
     */
    @Component
    public static class EntMatTaskOverdueMsg extends EntMatTaskMessageHandler {
        @Override
        public String code() {
            return MessageConstant.Code.SBBYRWGQTX.getCode();
        }
    }

}
