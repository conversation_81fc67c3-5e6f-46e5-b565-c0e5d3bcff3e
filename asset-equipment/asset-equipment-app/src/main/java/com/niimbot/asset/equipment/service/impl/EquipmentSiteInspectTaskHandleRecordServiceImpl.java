package com.niimbot.asset.equipment.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskHandleRecordMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskHandleRecord;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskHandleRecordService;
import com.niimbot.equipment.SiteInspectTaskHandleRecordDto;

import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2023/11/18 11:39
 */
@Service
public class EquipmentSiteInspectTaskHandleRecordServiceImpl extends ServiceImpl<AsEquipmentSiteInspectTaskHandleRecordMapper, AsEquipmentSiteInspectTaskHandleRecord> implements EquipmentSiteInspectTaskHandleRecordService {

    @Override
    public SiteInspectTaskHandleRecordDto handleRecordInfo(Long rangeId) {
        AsEquipmentSiteInspectTaskHandleRecord one = getOne(Wrappers.lambdaQuery(AsEquipmentSiteInspectTaskHandleRecord.class)
                .eq(AsEquipmentSiteInspectTaskHandleRecord::getTaskRangeId, rangeId), false);
        if (one != null) {
            return BeanUtil.copyProperties(one, SiteInspectTaskHandleRecordDto.class);
        }
        return new SiteInspectTaskHandleRecordDto();
    }

}
