package com.niimbot.asset.equipment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.niimbot.asset.equipment.mapper.*;
import com.niimbot.asset.equipment.model.*;
import com.niimbot.asset.equipment.resolver.MySqlEquipmentSiteInspectPlanConditionResolver;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectPlanService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.ots.SystemOrgOts;
import com.niimbot.equipment.*;
import com.niimbot.system.AuditableOperateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EquipmentSiteInspectPlanServiceImpl implements EquipmentSiteInspectPlanService {

    private final AsEquipmentSiteInspectPointMapper pointMapper;

    private final AsEquipmentSiteInspectProjectMapper projectMapper;

    private final AsEquipmentSiteInspectRangeMapper rangeMapper;

    private final AsEquipmentSiteInspectRangeProjectMapper rangeProjectMapper;

    private final AsEquipmentSiteInspectPlanMapper planMapper;

    private final AsEquipmentSiteInspectTaskMapper taskMapper;

    private final AssetService entService;

    private final CacheResourceUtil cacheResource;

    private final MySqlEquipmentSiteInspectPlanConditionResolver conditionResolver;

    private final SystemOrgOts orgService;

    void routeType(Integer routeType) {
        Affirm.nonNull(routeType, "巡检路线类型不能为空");
        Affirm.isTrue(routeType == EquipmentConstant.SNT_PLAN_ROUTE_TYPE_IS_UNORDERED || routeType == EquipmentConstant.SNT_PLAN_ROUTE_TYPE_IS_ORDERED, "不支持的巡检路线类型");
    }

    AsAsset equipment(Long id) {
        AsAsset equipment = entService.getOne(
                Wrappers.lambdaQuery(AsAsset.class)
                        .select(AsAsset::getId, AsAsset::getStatus, AsAsset::getAssetCode, AsAsset::getAssetName)
                        .eq(AsAsset::getId, id)
        );
        Affirm.nonNull(equipment, "设备已被删除");
        return equipment;
    }

    void createRanges(Long planId, Integer routeType, List<EntSntRange> entSntRanges) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        List<AsEquipmentSiteInspectRange> ranges = new ArrayList<>(entSntRanges.size());
        List<AsEquipmentSiteInspectRangeProject> rangeProjects = new ArrayList<>(entSntRanges.size() * 4);
        AtomicInteger sort = new AtomicInteger(1);
        entSntRanges.forEach(v -> {
            AsEquipmentSiteInspectRange range = new AsEquipmentSiteInspectRange();
            range.setId(IdUtils.getId()).setCompanyId(companyId).setPlanId(planId).setDataType(v.getDataType());
            if (routeType == EquipmentConstant.SNT_PLAN_ROUTE_TYPE_IS_ORDERED) {
                range.setSort(sort.getAndIncrement());
            }
            if (v.getDataType() == EquipmentConstant.SNT_RANGE_TYPE_IS_ENT) {
                range.setDataId(equipment(v.getDataId()).getId());
            }
            if (v.getDataType() == EquipmentConstant.SNT_RANGE_TYPE_IS_POINT) {
                range.setDataId(pointMapper.selectByIdOrElseThrow(v.getDataId()).getId());
            }
            ranges.add(range);
            List<AsEquipmentSiteInspectRangeProject> projects = projectMapper.selectBatchIds(v.getProjectIds()).stream().map(project -> new AsEquipmentSiteInspectRangeProject(IdUtils.getId(), range.getId(), project.getId(), companyId)).collect(Collectors.toList());
            Affirm.notEmpty(projects, "巡检项目不存在");
            rangeProjects.addAll(projects);
        });
        // 保存
        ranges.forEach(rangeMapper::insert);
        rangeProjects.forEach(rangeProjectMapper::insert);
    }

    AsEquipmentSiteInspectPlan toPlan(EntSntPlan entSntPlan) {
        // 巡检路线
        routeType(entSntPlan.getRouteType());
        // 巡检计划
        AsEquipmentSiteInspectPlan plan = new AsEquipmentSiteInspectPlan();
        plan.setCompanyId(LoginUserThreadLocal.getCompanyId());
        plan.setPlanStatus(EquipmentConstant.SNT_PLAN_STATUS_IS_WAITING);
        plan.setRouteType(entSntPlan.getRouteType());
        String taskType = entSntPlan.getPlanData().getString(EntSntPlanData.Fields.taskType);
        // 计算循环任务结束时间
        if (EquipmentConstant.TASK_TYPE_IS_PERIODICITY.equals(taskType)) {
            LocalDateTime planBeginTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(entSntPlan.getPlanData().getLong(EntSntPlanData.Fields.planBeginTime)), ZoneId.systemDefault());
            Integer taskCount = entSntPlan.getPlanData().getInteger(EntSntPlanData.Fields.taskCount);
            Periodic periodic = entSntPlan.getPlanData().getJSONObject(EntSntPlanData.Fields.periodic).toJavaObject(Periodic.class);
            Integer interval = periodic.getInterval();
            if (Periodic.Unit.DAY.equals(periodic.getUnit())) {
                entSntPlan.getPlanData().put(EntSntPlanData.Fields.planEndTime, planBeginTime.plusDays(Convert.toLong(taskCount * interval, 1L)).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            }
            if (Periodic.Unit.WEEK.equals(periodic.getUnit())) {
                entSntPlan.getPlanData().put(EntSntPlanData.Fields.planEndTime, planBeginTime.plusDays(Convert.toLong(taskCount * interval * 7, 1L)).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            }
        }
        plan.setPlanData(entSntPlan.getPlanData());
        // 计算状态
        AtomicInteger planStatus = new AtomicInteger(0);
        planStatus(planStatus, plan.getPlanData());
        plan.setPlanStatus(planStatus.get());
        // 翻译字段
        planDataTrans(plan.getPlanData());
        return plan;
    }

    void planStatus(AtomicInteger planStatus, JSONObject data) {
        // 【已停止】最终态 【已结束】最终态
        if (planStatus.get() == EquipmentConstant.SNT_PLAN_STATUS_IS_STOPPED || planStatus.get() == EquipmentConstant.SNT_PLAN_STATUS_IS_FINISHED) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime planBeginTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(data.getLong(EntSntPlanData.Fields.planBeginTime)), ZoneId.systemDefault());
        LocalDateTime planEndTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(data.getLong(EntSntPlanData.Fields.planEndTime)), ZoneId.systemDefault());
        // 【未开始】当前时间＜计划开始时间
        if (now.isBefore(planBeginTime)) {
            planStatus.compareAndSet(planStatus.get(), EquipmentConstant.SNT_PLAN_STATUS_IS_WAITING);
        }
        // 【执行中】当前时间＜计划结束时间
        if (now.isAfter(planBeginTime) && now.isBefore(planEndTime)) {
            planStatus.compareAndSet(planStatus.get(), EquipmentConstant.SNT_PLAN_STATUS_IS_RUNNING);
        }
        // 【已结束】当前时间＞计划结束时间
        if (now.isAfter(planEndTime)) {
            planStatus.compareAndSet(planStatus.get(), EquipmentConstant.SNT_PLAN_STATUS_IS_FINISHED);
        }
    }

    void planStatus(AsEquipmentSiteInspectPlan plan) {
        AtomicInteger planStatus = new AtomicInteger(plan.getPlanStatus());
        planStatus(planStatus, plan.getPlanData());
        plan.setPlanStatus(planStatus.get());
    }

    void planDataTrans(JSONObject data) {
        List<Long> managerIds = data.getJSONArray(EntSntPlanData.Fields.managers).toJavaList(Long.class);
        List<Long> executorIds = data.getJSONArray(EntSntPlanData.Fields.executors).toJavaList(Long.class);
        List<String> managerTrans = managerIds.stream().map(cacheResource::getUserNameAndCode).collect(Collectors.toList());
        List<String> executorTrans = executorIds.stream().map(cacheResource::getUserNameAndCode).collect(Collectors.toList());
        data.put(EntSntPlanData.Fields.managersText, managerTrans);
        data.put(EntSntPlanData.Fields.executorsText, executorTrans);
    }

    AuditableOperateResult result(AsEquipmentSiteInspectPlan plan) {
        return new AuditableOperateResult(plan.getPlanNo(), plan.getPlanData().getString(EntSntPlanData.Fields.planName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableOperateResult createPlan(CreateEntSntPlan create) {
        Long planId = IdUtils.getId();
        // 巡检范围
        createRanges(planId, create.getRouteType(), create.getRanges());
        AsEquipmentSiteInspectPlan plan = toPlan(create);
        plan.setId(planId);
        plan.setPlanNo(StringUtils.getOrderNo("XJ"));
        // 保存
        planMapper.insert(plan);
        return new AuditableOperateResult(plan.getPlanNo(), plan.getPlanData().getString(EntSntPlanData.Fields.planName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableOperateResult editPlan(EditEntSntPlan edit) {
        Long planId = edit.getId();
        AsEquipmentSiteInspectPlan select = planMapper.selectById(planId);
        planStatus(select);
        Affirm.isTrue(EquipmentConstant.SNT_PLAN_STATUS_IS_RUNNING != select.getPlanStatus(), "计划任务正在创建中禁止编辑");
        // 物理删除 巡检范围
        rangeProjectMapper.deleteByPlanId(planId);
        rangeMapper.deleteByPlanId(planId);
        // 巡检范围
        createRanges(planId, edit.getRouteType(), edit.getRanges());
        AsEquipmentSiteInspectPlan plan = toPlan(edit);
        plan.setId(planId);
        planMapper.updateById(plan);
        return new AuditableOperateResult(select.getPlanNo(), edit.getPlanData().getString(EntSntPlanData.Fields.planName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuditableOperateResult> removePlan(RemoveEntSntPlan remove) {
        List<Integer> allowRemoveStatus = Lists.newArrayList(EquipmentConstant.SNT_PLAN_STATUS_IS_STOPPED, EquipmentConstant.SNT_PLAN_STATUS_IS_FINISHED);
        List<AsEquipmentSiteInspectPlan> plans = planMapper.selectBatchIds(remove.getPlanIds());
        plans.forEach(this::planStatus);
        // 仅【已结束 / 已停止】的计划可操作删除
        Affirm.isTrue(plans.stream().allMatch(v -> allowRemoveStatus.contains(v.getPlanStatus())), "当前仅支持删除已结束或已停止的计划");
        List<Long> planIds = plans.stream().map(AsEquipmentSiteInspectPlan::getId).collect(Collectors.toList());
        planMapper.deleteBatchIds(planIds);
        List<AsEquipmentSiteInspectRange> ranges = rangeMapper.selectList(Wrappers.lambdaQuery(AsEquipmentSiteInspectRange.class).in(AsEquipmentSiteInspectRange::getPlanId, planIds));
        if (CollUtil.isNotEmpty(ranges)) {
            List<Long> rangeIds = ranges.stream().map(AsEquipmentSiteInspectRange::getId).distinct().collect(Collectors.toList());
            rangeProjectMapper.delete(Wrappers.lambdaUpdate(AsEquipmentSiteInspectRangeProject.class).in(AsEquipmentSiteInspectRangeProject::getRangeId, rangeIds));
            rangeMapper.deleteBatchIds(rangeIds);
        }
        // 作废任务
        if (remove.getCancelTask()) {
            taskMapper.update(null,
                    Wrappers.lambdaUpdate(AsEquipmentSiteInspectTask.class)
                            .set(AsEquipmentSiteInspectTask::getTaskStatus, DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_CANCEL)
                            .in(AsEquipmentSiteInspectTask::getPlanId, planIds)
                            .in(AsEquipmentSiteInspectTask::getTaskStatus, Lists.newArrayList(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_WAITING, DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_DOING, DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_EXPIRE))
            );
        }
        return plans.stream().map(v -> new AuditableOperateResult(v.getPlanNo(), v.getPlanData().getString(EntSntPlanData.Fields.planName))).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuditableOperateResult> stopPlan(List<Long> planIds) {
        List<Integer> allowStatus = Lists.newArrayList(EquipmentConstant.SNT_PLAN_STATUS_IS_WAITING, EquipmentConstant.SNT_PLAN_STATUS_IS_RUNNING);
        List<AsEquipmentSiteInspectPlan> plans = planMapper.selectBatchIds(planIds);
        plans.forEach(this::planStatus);
        // 仅【未开始/ 执行中】的计划可操作停用
        Affirm.isTrue(plans.stream().allMatch(v -> allowStatus.contains(v.getPlanStatus())), "当前仅支持停用未开始或执行中的计划");
        plans.forEach(v -> {
            v.setPlanStatus(EquipmentConstant.SNT_PLAN_STATUS_IS_STOPPED);
            planMapper.updateById(v);
        });
        return plans.stream().map(v -> new AuditableOperateResult(v.getPlanNo(), v.getPlanData().getString(EntSntPlanData.Fields.planName))).collect(Collectors.toList());
    }

    AsEquipmentSiteInspectPlan getPlanOrElseThroe(Long planId) {
        AsEquipmentSiteInspectPlan plan = planMapper.selectById(planId);
        Affirm.nonNull(plan, "计划不存在");
        return plan;
    }

    @Override
    public AuditableOperateResult editPlanManagers(EditEntSntPlanManagers edit) {
        AsEquipmentSiteInspectPlan plan = getPlanOrElseThroe(edit.getPlanId());
        planStatus(plan);
        Affirm.isTrue(plan.getPlanStatus().equals(EquipmentConstant.SNT_PLAN_STATUS_IS_WAITING) || plan.getPlanStatus().equals(EquipmentConstant.SNT_PLAN_STATUS_IS_RUNNING), "当前仅支持对未开始或执行中的计划可修改巡检管理员");
        plan.getPlanData().put(EntSntPlanData.Fields.managers, edit.getManagerIds().stream().map(String::valueOf).collect(Collectors.toList()));
        plan.getPlanData().put(EntSntPlanData.Fields.managersText, edit.getManagerIds().stream().map(cacheResource::getUserNameAndCode).collect(Collectors.toList()));
        planMapper.updateById(plan);
        // 修改计划下所有任务的管理员
        taskMapper.update(null,
                Wrappers.lambdaUpdate(AsEquipmentSiteInspectTask.class)
                        .set(AsEquipmentSiteInspectTask::getManagers, JSONArray.toJSON(edit.getManagerIds()).toString())
                        .eq(AsEquipmentSiteInspectTask::getPlanId, plan.getId())
        );
        return result(plan);
    }

    @Override
    public AuditableOperateResult editPlanExecutors(EditEntSntPlanExecutors edit) {
        AsEquipmentSiteInspectPlan plan = getPlanOrElseThroe(edit.getPlanId());
        planStatus(plan);
        Affirm.isTrue(plan.getPlanStatus().equals(EquipmentConstant.SNT_PLAN_STATUS_IS_WAITING) || plan.getPlanStatus().equals(EquipmentConstant.SNT_PLAN_STATUS_IS_RUNNING), "当前仅支持对未开始或执行中的计划可修改巡检人");
        plan.getPlanData().put(EntSntPlanData.Fields.executors, edit.getExecutorIds().stream().map(String::valueOf).collect(Collectors.toList()));
        plan.getPlanData().put(EntSntPlanData.Fields.executorsText, edit.getExecutorIds().stream().map(cacheResource::getUserNameAndCode).collect(Collectors.toList()));
        planMapper.updateById(plan);
        return result(plan);
    }

    @Override
    public PageUtils<EntSntPlan> searchPlan(SearchEntSntPlan search) {
        String tableAlias = "t1";
        StringBuilder perms = new StringBuilder();
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, search.getConditions());
        Page<?> page = conditionResolver.buildOrderSort(tableAlias, search);
        // 非超管只能查看自己创建的计划或自己管理的巡检计划
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            perms.append(" AND (");
            perms.append(tableAlias).append(".create_by = ").append(LoginUserThreadLocal.getCurrentUserId());
            perms.append(" OR ").append("JSON_CONTAINS(").append(tableAlias).append(".plan_data ->> '$.managers', ").append("JSON_ARRAY('").append(LoginUserThreadLocal.getCurrentUserId()).append("')").append(")");
            perms.append(" )");
        }
        search.setCompanyId(LoginUserThreadLocal.getCompanyId());
        IPage<EntSntPlan> result = planMapper.selectPageSearch(page, search, perms.toString(), conditions);
        return new PageUtils<>(result);
    }

    @Override
    public EntSntPlanDetails detailPlan(Long planId) {
        AsEquipmentSiteInspectPlan plan = getPlanOrElseThroe(planId);
        planStatus(plan);
        EntSntPlanDetails details = BeanUtil.copyProperties(plan, EntSntPlanDetails.class);
        // 巡检人列表
        List<Long> executorIds = details.getPlanData().getJSONArray(EntSntPlanData.Fields.executors).toJavaList(Long.class);
        List<EntSntPlanDetails.Executor> executorList = new ArrayList<>(executorIds.size());
        executorIds.forEach(id -> {
            List<Long> orgIds = orgService.getEmpOrgIds(id);
            if (CollUtil.isEmpty(orgIds)) {
                return;
            }
            executorList.add(new EntSntPlanDetails.Executor(id, cacheResource.getUserNameAndCode(id), orgIds.stream().map(cacheResource::getOrgName).collect(Collectors.joining(" "))));
        });
        details.setExecutorList(executorList);
        return details;
    }

    @Override
    public PageUtils<EntSntRange> rangeSearch(SearchEntSntRange search) {
        search.setCompanyId(LoginUserThreadLocal.getCompanyId());
        IPage<EntSntRange> page = rangeMapper.selectPageSearch(search.buildIPage(), search);
        if (Objects.nonNull(page) && CollUtil.isNotEmpty(page.getRecords())) {
            page.getRecords().parallelStream().forEach(v -> {
                List<Long> projectIds = rangeProjectMapper.selectProjectIdByRangeId(v.getId(), search.getCompanyId());
                if (CollUtil.isNotEmpty(projectIds)) {
                    List<AsEquipmentSiteInspectProject> projects = projectMapper.selectBatchIds(projectIds);
                    v.setProjectIds(projects.stream().map(AsEquipmentSiteInspectProject::getId).collect(Collectors.toList()));
                    v.setProjectNames(projects.stream().map(AsEquipmentSiteInspectProject::getProjectName).collect(Collectors.toList()));
                }
            });
        }
        return new PageUtils<>(page);
    }

    @Override
    public List<EntSntRange> rangeList(ListEntSntRange search) {
        return rangeMapper.selectList(
                Wrappers.lambdaQuery(AsEquipmentSiteInspectRange.class)
                        .eq(AsEquipmentSiteInspectRange::getCompanyId, search.getCompanyId())
                        .in(AsEquipmentSiteInspectRange::getDataId, search.getDataIds())
        ).stream().map(v -> BeanUtil.copyProperties(v, EntSntRange.class)).collect(Collectors.toList());
    }

    @Override
    public List<SiteInspectDistributeTaskDto> distributeTaskPlanList(LocalDateTime now) {

        // 查询进行中的计划
        List<AsEquipmentSiteInspectPlan> inspectPlans = planMapper.selectList(Wrappers.lambdaQuery(AsEquipmentSiteInspectPlan.class)
                .in(AsEquipmentSiteInspectPlan::getPlanStatus,
                        ListUtil.of(EquipmentConstant.SNT_PLAN_STATUS_IS_WAITING, EquipmentConstant.SNT_PLAN_STATUS_IS_RUNNING)));

        List<SiteInspectDistributeTaskDto.RangeDto> distributeTaskRangeList = planMapper.distributeTaskRangeList();
        Map<Long, List<SiteInspectDistributeTaskDto.RangeDto>> rangeMap = distributeTaskRangeList.stream().collect(Collectors.groupingBy(SiteInspectDistributeTaskDto.RangeDto::getPlanId));

        List<SiteInspectDistributeTaskDto> distributeTaskList = Collections.synchronizedList(new ArrayList<>());
        // 构建任务下发对象
        inspectPlans.parallelStream().forEach(plan -> {
            SiteInspectDistributeTaskDto distributeTaskDto = new SiteInspectDistributeTaskDto();
            if (plan.getPlanData() != null && rangeMap.containsKey(plan.getId())) {
                EntSntPlanData planData = plan.getPlanData().toJavaObject(EntSntPlanData.class);
                // 循环任务
                try {
                    int issuedTaskNum = verifyPlan(distributeTaskDto, plan, planData, now);
                    if (issuedTaskNum > 0) {
                        // 写入任务信息
                        distributeTaskDto.setPlanId(plan.getId())
                                .setCompanyId(plan.getCompanyId())
                                .setPlanNo(plan.getPlanNo())
                                .setRouteType(plan.getRouteType())
                                .setPlanName(planData.getPlanName())
                                .setTaskType(planData.getTaskType())
                                .setPlanBeginTime(planData.getPlanBeginTime())
                                .setPlanEndTime(planData.getPlanEndTime())
                                .setPeriodic(planData.getPeriodic())
                                .setIssuedTaskNum(issuedTaskNum)
                                .setManagers(planData.getManagers())
                                .setExecutors(planData.getExecutors())
                                .setCreateBy(plan.getCreateBy());
                        // 写入任务巡检路线信息
                        distributeTaskDto.setRangeList(rangeMap.get(plan.getId()));
                        distributeTaskList.add(distributeTaskDto);
                    }
                } catch (Exception e) {
                    log.error("构建任务下发对象失败，planId={}, planNo={}, error={}", plan.getId(), plan.getPlanNo(), e.getMessage(), e);
                }
            }
        });
        return distributeTaskList;
    }

    private int verifyPlan(SiteInspectDistributeTaskDto distributeTaskDto,
                           AsEquipmentSiteInspectPlan plan,
                           EntSntPlanData planData,
                           LocalDateTime now) {
        if (EquipmentConstant.TASK_TYPE_IS_TIMELINESS.equals(planData.getTaskType())) {
            planData.setTaskCount(1);
        }
        // 判断计划【已下发的任务数】是否小于【总任务数】
        if (planData.getTaskCount() <= plan.getIssuedTaskNum()) {
            return -1;
        }
        // 判断开始结束时间是否在时间区间内
        LocalDateTime planBeginTime;
        try {
            planBeginTime = LocalDateTime.ofEpochSecond(planData.getPlanBeginTime() / 1000, 0, ZoneOffset.of("+8"))
                    .withSecond(0).withNano(0);
        } catch (Exception e) {
            log.warn("planId={} planBeginTime = {} 转换时间异常", plan.getId(), planData.getPlanBeginTime(), e);
            return -1;
        }
        LocalDateTime planEndTime;
        try {
            planEndTime = LocalDateTime.ofEpochSecond(planData.getPlanEndTime() / 1000, 0, ZoneOffset.of("+8"))
                    .withSecond(0).withNano(0);
        } catch (Exception e) {
            log.warn("planId={} planEndTime = {} 转换时间异常", plan.getId(), planData.getPlanEndTime(), e);
            return -1;
        }
        if (planBeginTime.compareTo(now) > 0 || planEndTime.compareTo(now) < 0) {
            return -1;
        }

        // 判断是一次性任务，还是循环任务
        if (EquipmentConstant.TASK_TYPE_IS_TIMELINESS.equals(planData.getTaskType())) {
            // 已经下发过的不重复下发
            if (plan.getIssuedTaskNum() == 1) {
                return -1;
            }
            // 固定任务
            distributeTaskDto.setPlanFinishTime(planEndTime);
            return Convert.toInt(1, -1);
        } else if (EquipmentConstant.TASK_TYPE_IS_PERIODICITY.equals(planData.getTaskType())) {
            // 计算循环任务周期
            Periodic periodic = planData.getPeriodic();
            Duration between = Duration.between(planBeginTime, now);
            long minutesDiff = between.toMinutes();
            long remainder;
            long quotient;
            if (periodic.getInterval() == 0) {
                return -1;
            }
            if (Periodic.Unit.WEEK.equals(periodic.getUnit())) {
                // 10080 = 7 * 24 * 60
                int product = periodic.getInterval() * 10080;
                remainder = minutesDiff % (product);
                quotient = minutesDiff / (product);
            } else if (Periodic.Unit.DAY.equals(periodic.getUnit())) {
                // 1440 = 24 * 60
                int product = periodic.getInterval() * 1440;
                remainder = minutesDiff % product;
                quotient = minutesDiff / product;
            } else {
                return -1;
            }

            // 没有整除，说明不在当前时间点
            if (remainder != 0) {
                return -1;
            }

            if (Periodic.Unit.WEEK.equals(periodic.getUnit())) {
                distributeTaskDto.setPlanFinishTime(now.plusDays(7 * periodic.getInterval()));
            } else if (Periodic.Unit.DAY.equals(periodic.getUnit())) {
                distributeTaskDto.setPlanFinishTime(now.plusDays(periodic.getInterval()));
            }
            quotient++;

            // 已经下发过的不重复下发
            if (plan.getIssuedTaskNum() == quotient) {
                return -1;
            }
            return Convert.toInt(quotient, -1);
        } else {
            return -1;
        }
    }

}
