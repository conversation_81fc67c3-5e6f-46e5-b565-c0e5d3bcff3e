package com.niimbot.asset.equipment.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.google.common.collect.Lists;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanContentMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanDataMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainTaskMapper;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlan;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlanContent;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlanData;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainTask;
import com.niimbot.asset.equipment.resolver.MySqlEquipmentMaintainPlanConditionResolver;
import com.niimbot.asset.equipment.service.EquipmentMaintainPlanService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.material.model.AsMaterial;
import com.niimbot.asset.material.service.AsMaterialService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.dto.clientobject.EmployeeCO;
import com.niimbot.asset.system.ots.SystemEmployeeOts;
import com.niimbot.equipment.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.DataTransfer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EquipmentMaintainPlanServiceImpl implements EquipmentMaintainPlanService {

    private final AsEquipmentMaintainPlanMapper planMapper;

    private final AsEquipmentMaintainPlanContentMapper contentMapper;

    private final AsEquipmentMaintainPlanDataMapper dataMapper;

    private final AsEquipmentMaintainTaskMapper taskMapper;

    private final CacheResourceUtil resourceUtil;

    @Resource
    private AssetService meansService;

    @Resource
    private AsMaterialService materialService;

    private final SystemEmployeeOts systemEmployeeService;

    private final MySqlEquipmentMaintainPlanConditionResolver resolver;

    @Override
    public EntMatPlanPreview previewPlan(CreateEntMatPlan plan) {
        String taskType = plan.getPlanData().getString(EntMatPlanData.Fields.taskType);
        Affirm.isTrue(EquipmentConstant.TASK_TYPE_IS_TIMELINESS.equals(taskType) || EquipmentConstant.TASK_TYPE_IS_PERIODICITY.equals(taskType), "不支持的循环类型");
        EntMatPlanPreview preview = new EntMatPlanPreview();
        // 循环类型
        preview.setTaskType(taskType);
        // 已选择的设备数
        Integer rangeType = plan.getRangeType();
        if (EquipmentConstant.RANGE_TYPE_IS_MAS == rangeType) {
            long assetNum = meansService.count(
                    Wrappers.lambdaQuery(AsAsset.class)
                            .select(AsAsset::getId)
                            .ne(BooleanUtil.isTrue(plan.getFilterDispose()), AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE)
                            .in(AsAsset::getId, plan.getSelectedEntData())
            );
            preview.setSelectedEntCount(Convert.toInt(assetNum));
        }
        if (EquipmentConstant.RANGE_TYPE_IS_CATE == rangeType) {
            List<AsCategory> meansCategories = Db.list(
                    Wrappers.lambdaQuery(AsCategory.class)
                            .select(AsCategory::getId)
                            .in(AsCategory::getId, plan.getSelectedEntData())
            );
            Affirm.notEmpty(meansCategories, "已选择设备分类不存在或已被删除，请检查");
            plan.setSelectedEntData(meansCategories.stream().map(AsCategory::getId).collect(Collectors.toList()));
            int count = Convert.toInt(meansService.count(
                    Wrappers.lambdaQuery(AsAsset.class)
                            .select(AsAsset::getId)
                            .ne(BooleanUtil.isTrue(plan.getFilterDispose()), AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE)
                            .in(AsAsset::getAssetCategory, plan.getSelectedEntData())
            ));
            preview.setSelectedEntCount(count);
        }
        if (preview.getSelectedEntCount() == 0) {
            if (BooleanUtil.isTrue(plan.getFilterDispose())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "已选择设备不存在或设备状态均为已处置，请重新选择待保养设备。");
            } else {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "已选择设备不存在，请重新选择待保养设备。");
            }
        }
        // 循环时间段 任务数量
        preview.setTaskBeginTime(plan.getPlanData().getLong(EntMatPlanData.Fields.taskBeginTime));
        preview.setTaskCount(preview.getSelectedEntCount());
        if (EquipmentConstant.TASK_TYPE_IS_PERIODICITY.equals(taskType)) {
            preview.setTaskEndTime(plan.getPlanData().getLong(EntMatPlanData.Fields.taskEndTime));
            Periodic periodic = plan.getPlanData().getJSONObject(EntMatPlanData.Fields.periodic).toJavaObject(Periodic.class);
            CalculateTask calculateTask = new CalculateTask(preview.getTaskBeginTime(), preview.getTaskEndTime(), periodic.getInterval(), periodic.getUnit());
            preview.setTaskCount(calculateTask.obtain().size() * preview.getSelectedEntCount());
            preview.setTaskType(periodic.getInterval() + periodic.getUnit() + "一次");
        }
        return preview;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableCreatePlanResult createPlan(CreateEntMatPlan plan) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        Affirm.isTrue(EquipmentConstant.RANGE_TYPE_IS_MAS == plan.getRangeType() || EquipmentConstant.RANGE_TYPE_IS_CATE == plan.getRangeType(), "不支持的设备选择范围");
        // 1.设备存在性校验
        if (EquipmentConstant.RANGE_TYPE_IS_MAS == plan.getRangeType()) {
            List<AsAsset> means = meansService.list(
                    Wrappers.lambdaQuery(AsAsset.class)
                            .select(AsAsset::getId)
                            .ne(BooleanUtil.isTrue(plan.getFilterDispose()), AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE)
                            .in(AsAsset::getId, plan.getSelectedEntData())
            );
            Affirm.notEmpty(means, "已选择设备不存在或已被删除，请检查");
            plan.setSelectedEntData(means.stream().map(AsAsset::getId).distinct().collect(Collectors.toList()));
            Affirm.isTrue(plan.getSelectedEntData().size() <= 100, "当前选择的设备数超过100");
        }
        if (EquipmentConstant.RANGE_TYPE_IS_CATE == plan.getRangeType()) {
            List<AsCategory> meansCategories = Db.list(
                    Wrappers.lambdaQuery(AsCategory.class)
                            .select(AsCategory::getId)
                            .in(AsCategory::getId, plan.getSelectedEntData())
            );
            Affirm.notEmpty(meansCategories, "已选择设备分类不存在或已被删除，请检查");
            plan.setSelectedEntData(meansCategories.stream().map(AsCategory::getId).collect(Collectors.toList()));
            int count = Convert.toInt(meansService.count(
                    Wrappers.lambdaQuery(AsAsset.class)
                            .select(AsAsset::getId)
                            .ne(BooleanUtil.isTrue(plan.getFilterDispose()), AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE)
                            .in(AsAsset::getAssetCategory, plan.getSelectedEntData())
            ));
            Affirm.isTrue(count <= 100, "当前选择的分类包含的设备数超过100");
        }
        // 2.备件存在性校验
        if (CollUtil.isNotEmpty(plan.getSelectedSrPrData())) {
            List<AsMaterial> materials = materialService.list(
                    Wrappers.lambdaQuery(AsMaterial.class)
                            .select(AsMaterial::getId)
                            .in(AsMaterial::getId, plan.getSelectedSrPrData().stream().map(SrPrInfo::getId).collect(Collectors.toList()))
            );
            Affirm.notEmpty(materials, "已选择备件不存在或已被删除，请检查");
            List<Long> srPrIds = materials.stream().map(AsMaterial::getId).distinct().collect(Collectors.toList());
            plan.getSelectedSrPrData().removeIf(v -> !srPrIds.contains(v.getId()));
        }
        Long planId = IdUtils.getId();
        // 3.保养内容
        List<AsEquipmentMaintainPlanContent> contents = plan.getPlanContents().stream().map(v -> {
            AsEquipmentMaintainPlanContent content = new AsEquipmentMaintainPlanContent();
            return content.setCompanyId(companyId).setPlanId(planId).setProject(v.getProject()).setRequirement(v.getRequirement());
        }).collect(Collectors.toList());
        // 4.保养计划数据
        List<AsEquipmentMaintainPlanData> meansData = plan.getSelectedEntData().stream().map(v -> new AsEquipmentMaintainPlanData().setCompanyId(companyId).setPlanId(planId).setDataType(plan.getRangeType()).setDataId(v)).collect(Collectors.toList());
        List<AsEquipmentMaintainPlanData> srTrData = plan.getSelectedSrPrData().stream().map(v -> new AsEquipmentMaintainPlanData().setCompanyId(companyId).setPlanId(planId).setDataType(EquipmentConstant.RANGE_TYPE_IS_MRL).setDataId(v.getId()).setReplacement(v.getQuantity())).collect(Collectors.toList());
        // 5.保养计划
        AsEquipmentMaintainPlan planToSave = new AsEquipmentMaintainPlan();
        planToSave.setId(planId).setCompanyId(companyId)
                .setPlanNo(StringUtils.getOrderNo("BY")).setPlanStatus(EquipmentConstant.PLAN_STATUS_IS_EXEC).setRangeType(plan.getRangeType())
                .setPlanContent(JSONArray.parseArray(JSONObject.toJSONString(contents)))
                .setPlanData(plan.getPlanData())
                .setFilterDispose(BooleanUtil.isTrue(plan.getFilterDispose()));
        // 6.翻译字段
        Long userId = planToSave.getPlanData().getLong(EntMatPlanData.Fields.maintainUser);
        planToSave.getPlanData().put(EntMatPlanData.Fields.maintainUser + "Text", resourceUtil.getUserNameAndCode(userId));
        planMapper.insert(planToSave);
        contents.forEach(contentMapper::insert);
        meansData.forEach(dataMapper::insert);
        if (CollUtil.isNotEmpty(srTrData)) {
            srTrData.forEach(dataMapper::insert);
        }
        return new AuditableCreatePlanResult().setPlanNo(planToSave.getPlanNo()).setPlanId(planId);
    }

    AsEquipmentMaintainPlan getPlan(Long planId) {
        AsEquipmentMaintainPlan plan = planMapper.selectById(planId);
        Affirm.nonNull(plan, "当前设备保养计划不存在");
        return plan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableStopPlanResult stopPlan(StopEntMatPlan union) {
        verifyCanOpt(union.getPlanIds(), "停用");
        int update = planMapper.update(
                null,
                Wrappers.lambdaUpdate(AsEquipmentMaintainPlan.class)
                        .set(AsEquipmentMaintainPlan::getPlanStatus, EquipmentConstant.PLAN_STATUS_IS_STOP)
                        .in(AsEquipmentMaintainPlan::getId, union.getPlanIds())
                        .eq(AsEquipmentMaintainPlan::getCreateBy, LoginUserThreadLocal.getCurrentUserId())
        );
        // 移除待保养的任务
        if (union.getRemoveWaitMaintainTask() && update > 0) {
            taskMapper.deleteWaitStatusTaskByPlanIds(union.getPlanIds());
        }
        return new AuditableStopPlanResult().setPlanNos
                (
                        planMapper
                                .selectList(Wrappers.lambdaQuery(AsEquipmentMaintainPlan.class).select(AsEquipmentMaintainPlan::getPlanNo).in(AsEquipmentMaintainPlan::getId, union.getPlanIds()))
                                .stream().map(AsEquipmentMaintainPlan::getPlanNo).collect(Collectors.toList())
                );
    }

    @Override
    public AuditableRemovePlanResult removePlan(RemoveEntMatPlan remove) {
        List<AsEquipmentMaintainPlan> plans = verifyCanOpt(remove.getPlanIds(), "删除");
        plans.forEach(v -> v.setPlanStatus(planStatus(v.getPlanData(), v.getPlanStatus())));
        plans.removeIf(v -> v.getPlanStatus() == EquipmentConstant.PLAN_STATUS_IS_EXEC);
        Affirm.notEmpty(plans, "执行中的保养计划不允许删除");
        List<Long> ids = plans.stream().map(AsEquipmentMaintainPlan::getId).collect(Collectors.toList());
        planMapper.deleteBatchIds(ids);
        return new AuditableRemovePlanResult().setPlanNos
                (
                        plans.stream().filter(v -> ids.contains(v.getId())).map(AsEquipmentMaintainPlan::getPlanNo).collect(Collectors.toList())
                );
    }

    private List<AsEquipmentMaintainPlan> verifyCanOpt(List<Long> planIds, String msg) {
        if (CollUtil.isEmpty(planIds)) {
            return new ArrayList<>();
        }
        List<AsEquipmentMaintainPlan> plans = planMapper.selectList(
                Wrappers.lambdaQuery(AsEquipmentMaintainPlan.class)
                        .in(AsEquipmentMaintainPlan::getId, planIds));
        if (plans.size() != planIds.size()) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "保养计划不存在");
        }
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
            long count = plans.stream().filter(p -> p.getCreateBy().equals(currentUserId)).count();
            if (count != planIds.size()) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "无权限" + msg);
            }
        }
        return plans;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rePlanUser(ReEntMatPlanUser change) {
        verifyCanOpt(ListUtil.of(change.getPlanId()), "更换保养人");
        Long planId = change.getPlanId();
        AsEquipmentMaintainPlan plan = getPlan(planId);
        Affirm.isTrue(BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin()) || LoginUserThreadLocal.getCurrentUserId().equals(plan.getCreateBy()), "仅允许计划创建人修改保养负责人");
        EmployeeCO user = systemEmployeeService.getById(change.getUserId());
        Affirm.nonNull(user, "保养负责人不存在");
        plan.getPlanData().put(EntMatPlanData.Fields.maintainUser, user.getId());
        plan.getPlanData().put(EntMatPlanData.Fields.maintainUser + "Text", resourceUtil.getUserNameAndCode(user.getId()));
        int planUpdate = planMapper.updateById(plan);
        // 更新待保养任务的保养负责人
        if (planUpdate > 0) {
            taskMapper.updateWaitTaskMaintainUserByPlanId(planId, user.getId());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transformPlan(Long oldUserId, DataTransfer dataTransfer) {
        EmployeeCO user = systemEmployeeService.getById(dataTransfer.getReceiveEmployeeId());
        Affirm.nonNull(user, "新保养负责人不存在，请检查");
        List<AsEquipmentMaintainPlan> plans = planMapper.selectByMaintainUser(oldUserId);
        if (CollUtil.isEmpty(plans)) {
            return true;
        }
        // 计划
        plans.forEach(v -> {
            v.getPlanData().put(EntMatPlanData.Fields.maintainUser, dataTransfer.getReceiveEmployeeId());
            v.getPlanData().put(EntMatPlanData.Fields.maintainUser + "Text", resourceUtil.getUserNameAndCode(dataTransfer.getReceiveEmployeeId()));
        });
        plans.forEach(planMapper::updateById);
        // 任务
        List<Long> planIds = plans.stream().map(AsEquipmentMaintainPlan::getId).collect(Collectors.toList());
        taskMapper.update(
                null,
                Wrappers.lambdaUpdate(AsEquipmentMaintainTask.class)
                        .set(AsEquipmentMaintainTask::getMaintainUser, dataTransfer.getReceiveEmployeeId())
                        .in(AsEquipmentMaintainTask::getPlanId, planIds)
                        .notIn(AsEquipmentMaintainTask::getTaskStatus, Lists.newArrayList(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_FINISH, DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_CANCEL))
        );
        return true;
    }

    int planStatus(JSONObject planData, int planStatus) {
        if (EquipmentConstant.PLAN_STATUS_IS_STOP == planStatus) {
            return planStatus;
        }
        String taskType = planData.getString(EntMatPlanData.Fields.taskType);
        LocalDateTime taskBeginTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(planData.getLong(EntMatPlanData.Fields.taskBeginTime)), ZoneId.systemDefault());
        LocalDateTime taskEndTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(planData.getLong(EntMatPlanData.Fields.taskEndTime)), ZoneId.systemDefault());
        if (EquipmentConstant.TASK_TYPE_IS_TIMELINESS.equals(taskType) && taskBeginTime.isBefore(LocalDateTime.now())) {
            return EquipmentConstant.PLAN_STATUS_IS_FINISH;
        }
        if (EquipmentConstant.TASK_TYPE_IS_PERIODICITY.equals(taskType) && taskEndTime.isBefore(LocalDateTime.now())) {
            return EquipmentConstant.PLAN_STATUS_IS_FINISH;
        }
        return EquipmentConstant.PLAN_STATUS_IS_EXEC;
    }

    @Override
    public EntMatPlanDetail detailPlan(Long planId) {
        // 1.设备保养计划
        AsEquipmentMaintainPlan plan = planMapper.selectById(planId);
        Affirm.nonNull(plan, "设备保养计划已删除");
        // 2.设备保养内容
        List<AsEquipmentMaintainPlanContent> contents = contentMapper.selectByPlanId(planId);
        EntMatPlanDetail entMatPlanDetail = new EntMatPlanDetail();
        entMatPlanDetail.setRangeType(plan.getRangeType());
        entMatPlanDetail.setFilterDispose(plan.getFilterDispose());
        entMatPlanDetail.setPlanContents(contents.stream().map(v -> new EntMatContent().setProject(v.getProject()).setRequirement(v.getRequirement())).collect(Collectors.toList()));
        JSONObject planData = plan.getPlanData();
        planData.put(EntMatPlan.Fields.id, plan.getId());
        planData.put(EntMatPlan.Fields.planNo, plan.getPlanNo());
        int status = planStatus(planData, plan.getPlanStatus());
        planData.put(EntMatPlan.Fields.planStatus, status);
        planData.put(EntMatPlan.Fields.planStatusText, resourceUtil.getDictLabel("equipment_maintain_plan_status", String.valueOf(status)));
        planData.put(EntMatPlan.Fields.createTime, plan.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        entMatPlanDetail.setPlanData(planData);
        return entMatPlanDetail;
    }

    @Override
    public List<EntMatPlanEntCateData> listEntCate(Long planId) {
        AsEquipmentMaintainPlan plan = getPlan(planId);
        Affirm.isTrue(plan.getRangeType() == EquipmentConstant.RANGE_TYPE_IS_CATE, "当前保养计划未选择分类");
        List<Long> ids = dataMapper.selectDataIdByPlanId(planId, plan.getRangeType());
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return Db.list(
                Wrappers.lambdaQuery(AsCategory.class)
                        .select(AsCategory::getId, AsCategory::getCategoryName)
                        .in(AsCategory::getId, ids)
        ).stream().map(v -> new EntMatPlanEntCateData().setCategoryId(v.getId()).setCategoryName(v.getCategoryName())).collect(Collectors.toList());
    }

    @Override
    public List<EntMatPlanCounter> countPlan(List<Long> planIds) {
        return planIds.stream().map(id -> {
            EntMatPlanCounter counter = new EntMatPlanCounter();
            counter.setPlanId(id);
            List<AsEquipmentMaintainPlanData> data = dataMapper.selectByPlanId(id);
            // 备件数量
            long srPrCount = data.stream().filter(v -> v.getDataType() == EquipmentConstant.RANGE_TYPE_IS_MRL).count();
            // 设备数量
            long entCount = data.stream().filter(v -> v.getDataType() == EquipmentConstant.RANGE_TYPE_IS_MAS).count();
            List<String> cateIds = data.stream()
                    .filter(v -> v.getDataType() == EquipmentConstant.RANGE_TYPE_IS_CATE)
                    .map(AsEquipmentMaintainPlanData::getDataId)
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(cateIds)) {
                int cateCount = Convert.toInt(meansService.count(
                        Wrappers.lambdaQuery(AsAsset.class)
                                .eq(AsAsset::getCompanyId, LoginUserThreadLocal.getCompanyId())
                                .in(AsAsset::getAssetCategory, cateIds)
                ));
                entCount = entCount + cateCount;
            }
            counter.setEntCount(entCount).setSrPrCount(srPrCount);
            return counter;
        }).collect(Collectors.toList());
    }

    @Override
    public PageUtils<EntMatPlan> searchPlan(EntMatPlanSearch search) {
        String tableAlias = "t1";
        String conditions = resolver.resolveQueryCondition(tableAlias, search.getConditions());
        Page<?> page = resolver.buildOrderSort(tableAlias, search);
        if (StrUtil.isNotBlank(search.getKw())) {
            Set<Long> ids = new HashSet<>();
            ids.addAll(dataMapper.selectPlanIdKeyword1(LoginUserThreadLocal.getCompanyId(), search.getKw()));
            ids.addAll(dataMapper.selectPlanIdKeyword2(LoginUserThreadLocal.getCompanyId(), search.getKw()));
            search.getKeywordsPlanIds().addAll(new ArrayList<>(ids));
        }
        IPage<EntMatPlan> result = planMapper.selectPageSearch(page, search, conditions);
        result.getRecords().forEach(v -> v.setPlanStatus(planStatus(v.getPlanData(), v.getPlanStatus())));
        return new PageUtils<>(result);
    }

    @Override
    public PageUtils<EntMatPlanEntData> pageSelectedEntMatData(GetSelectedEntData get) {
        AsEquipmentMaintainPlan plan = getPlan(get.getPlanId());
        get.setRangeType(plan.getRangeType());
        return new PageUtils<>(meansService.pageEntMatPlanMeansData(get));
    }

    @Override
    public PageUtils<EntMatPlanSrPrData> pageSelectedEntSrPrData(GetSelectedSrPrData get) {
        getPlan(get.getPlanId());
        return new PageUtils<>(materialService.pageEntMatPlanMrlData(get));
    }

}
