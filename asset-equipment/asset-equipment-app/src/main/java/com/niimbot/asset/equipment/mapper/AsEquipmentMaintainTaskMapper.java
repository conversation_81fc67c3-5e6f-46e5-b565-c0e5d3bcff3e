package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainTask;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.equipment.EntMatTaskMsg;
import com.niimbot.equipment.MaintainTaskDto;
import com.niimbot.equipment.MaintainTaskQueryDto;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2023/10/12 11:16
 */
@EnableDataPerm
public interface AsEquipmentMaintainTaskMapper extends BaseMapper<AsEquipmentMaintainTask> {

    IPage<MaintainTaskDto> pageTask(@Param("page") Page<?> page,
                                    @Param("query") MaintainTaskQueryDto query,
                                    @Param("conditions") String conditions);

    /**
     * 删除计划下待保养的任务
     *
     * @param planIds 计划ID集合
     */
    default void deleteWaitStatusTaskByPlanIds(List<Long> planIds) {
        if (CollUtil.isEmpty(planIds)) {
            return;
        }
        this.delete(
                Wrappers.lambdaUpdate(AsEquipmentMaintainTask.class)
                        .in(AsEquipmentMaintainTask::getPlanId, planIds)
                        .eq(AsEquipmentMaintainTask::getTaskStatus, DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING)
                        .gt(AsEquipmentMaintainTask::getPlanStartTime, LocalDateTime.now())
        );
    }

    /**
     * 更新计划下待保养任务的保养负责人
     *
     * @param planId 保养计划ID
     * @param userId 保养负责人ID
     */
    default void updateWaitTaskMaintainUserByPlanId(Long planId, Long userId) {
        this.update(
                null,
                Wrappers.lambdaUpdate(AsEquipmentMaintainTask.class)
                        .set(AsEquipmentMaintainTask::getMaintainUser, userId)
                        .eq(AsEquipmentMaintainTask::getPlanId, planId)
                        .eq(AsEquipmentMaintainTask::getTaskStatus, DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING)
        );
    }

    /**
     * 查询即将开始的设备保养任务列表
     *
     * @param companyId 企业ID
     * @param days      距离保养任务过期的天数
     * @param code      消息编码
     * @return list
     */
    List<EntMatTaskMsg> selectListForMsg(@Param("companyId") Long companyId, @Param("days") List<Integer> days, @Param("code") String code);

    List<Long> cateNewAssetIds(@Param("planId") Long planId, @Param("companyId") Long companyId);

}
