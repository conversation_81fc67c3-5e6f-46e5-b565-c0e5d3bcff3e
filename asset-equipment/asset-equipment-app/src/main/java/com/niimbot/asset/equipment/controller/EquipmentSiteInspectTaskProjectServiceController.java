package com.niimbot.asset.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskAggregateService;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskProjectService;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.equipment.SiteInspectTaskAbnormalDto;
import com.niimbot.equipment.SiteInspectTaskAbnormalQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectDto;
import com.niimbot.equipment.SiteInspectTaskProjectQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectSubmitDto;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/13 13:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/equipment/siteInspect/task/project")
public class EquipmentSiteInspectTaskProjectServiceController {

    private final EquipmentSiteInspectTaskProjectService siteInspectTaskProjectService;
    private final EquipmentSiteInspectTaskAggregateService siteInspectTaskAggregateService;
    private final RedissonClient redissonClient;

    @GetMapping("/page")
    public IPage<SiteInspectTaskProjectDto> page(SiteInspectTaskProjectQryDto query) {
        return siteInspectTaskProjectService.projectPage(query);
    }

    @GetMapping("/info")
    public SiteInspectTaskProjectDto info(@RequestParam("id") Long id) {
        return siteInspectTaskProjectService.info(id);
    }

    @GetMapping("/abnormal/page")
    public IPage<SiteInspectTaskAbnormalDto> projectAbnormalPage(SiteInspectTaskAbnormalQryDto query) {
        return siteInspectTaskProjectService.projectAbnormalPage(query);
    }

    @PostMapping("/submit")
    public Boolean projectSubmit(@RequestBody SiteInspectTaskProjectSubmitDto submitDto) {
        SiteInspectTaskProjectDto projectDto = siteInspectTaskProjectService.info(submitDto.getId());
        BusinessExceptionUtil.checkNotNull(projectDto, "巡检项目不存在");

        RLock lock = redissonClient.getLock("site_inspect_task_project_submit:" + projectDto.getTaskId());
        lock.lock();
        try {
            return siteInspectTaskAggregateService.projectSubmit(submitDto);
        } finally {
            lock.unlock();
        }
    }

}
