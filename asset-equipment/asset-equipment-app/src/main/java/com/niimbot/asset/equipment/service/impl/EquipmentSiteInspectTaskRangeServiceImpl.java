package com.niimbot.asset.equipment.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectRangeMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskRangeMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTask;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskRange;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskRangeService;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.equipment.EntSntRangeLocation;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.equipment.SiteInspectTaskRangeQryDto;
import com.niimbot.equipment.SiteInspectTaskRangeStatisticsDto;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:08
 */
@Service
@RequiredArgsConstructor
public class EquipmentSiteInspectTaskRangeServiceImpl extends ServiceImpl<AsEquipmentSiteInspectTaskRangeMapper, AsEquipmentSiteInspectTaskRange> implements EquipmentSiteInspectTaskRangeService {

    private final AsEquipmentSiteInspectTaskMapper siteInspectTaskMapper;
    private final AsEquipmentSiteInspectRangeMapper inspectRangeMapper;

    @Override
    public IPage<SiteInspectTaskRangeDto> taskRangeList(SiteInspectTaskRangeQryDto query) {
        AsEquipmentSiteInspectTask siteInspectTask = siteInspectTaskMapper.selectById(query.getTaskId());
        BusinessExceptionUtil.checkNotNull(siteInspectTask, "巡检任务不存在");
        IPage<SiteInspectTaskRangeDto> taskRangePage = getBaseMapper().taskRangePage(query.buildIPage(), query);

        Map<Long, String> rangeLocationCache = new HashMap<>();
        // 是否固定路线
        for (SiteInspectTaskRangeDto record : taskRangePage.getRecords()) {
            String image = StrUtil.EMPTY;
            // 处理照片
            if (StrUtil.isNotEmpty(record.getImage())) {
                try {
                    JSONArray imageList = JSONArray.parseArray(record.getImage());
                    if (imageList.size() > 0) {
                        image = Convert.toStr(imageList.get(0));
                    }
                } catch (Exception e) {
                    // ignore
                }
            }
            record.setImage(image);
            // 写入巡检路线
            record.setRouteType(siteInspectTask.getRouteType());
            // 具体位置
            if (rangeLocationCache.containsKey(record.getDataId())) {
                String location = rangeLocationCache.get(record.getDataId());
                record.setSpecificLocation(location);
            } else {
                EntSntRangeLocation rangeLocation = inspectRangeMapper.selectRangeLocation(record.getDataId(), record.getDataType());
                rangeLocationCache.put(record.getDataId(), rangeLocation.getLocation());
                record.setSpecificLocation(rangeLocation.getLocation());
            }
        }
        return taskRangePage;
    }

    @Override
    public SiteInspectTaskRangeDto taskRangeInfo(Long rangeId) {
        SiteInspectTaskRangeDto taskRangeDto = getBaseMapper().taskRangeInfo(rangeId);
        BusinessExceptionUtil.checkNotNull(taskRangeDto, "巡检详情不存在");

        AsEquipmentSiteInspectTask siteInspectTask = siteInspectTaskMapper.selectById(taskRangeDto.getTaskId());
        BusinessExceptionUtil.checkNotNull(siteInspectTask, "巡检任务不存在");

        String image = StrUtil.EMPTY;
        try {
            JSONArray imageList = JSONArray.parseArray(taskRangeDto.getImage());
            if (imageList.size() > 0) {
                image = Convert.toStr(imageList.get(0));
            }
        } catch (Exception e) {
            // ignore
        }
        taskRangeDto.setImage(image);
        // 是否固定路线
        taskRangeDto.setRouteType(siteInspectTask.getRouteType());
        // 具体位置
        EntSntRangeLocation rangeLocation = inspectRangeMapper.selectRangeLocation(taskRangeDto.getDataId(), taskRangeDto.getDataType());
        taskRangeDto.setSpecificLocation(rangeLocation.getLocation());
        return taskRangeDto;
    }

    @Override
    public SiteInspectTaskRangeStatisticsDto taskRangeStatistics(Long taskId) {
        SiteInspectTaskRangeStatisticsDto statisticsDto = getBaseMapper().taskRangeStatistics(taskId);
        return statisticsDto == null ? new SiteInspectTaskRangeStatisticsDto() : statisticsDto;
    }

    @Override
    public boolean executeUpdateRange(Long taskRangeId, int projectBeforeStatus, int projectStatus) {
        AsEquipmentSiteInspectTaskRange range = getById(taskRangeId);
        BusinessExceptionUtil.checkNotNull(range, "巡检点位不存在");

        // 判断是否可巡
        if (BooleanUtil.isFalse(range.getCanSiteInspect())) {
            BusinessExceptionUtil.checkNotNull(range, "固定路线当前点位不可巡检");
        }

        // 如果未检，则累加
        if (projectBeforeStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_UNCHECK) {
            range.setCheckedNum(range.getCheckedNum() + 1);
            if (projectStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL) {
                range.setNormalNum(range.getNormalNum() + 1);
            } else if (projectStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL) {
                range.setAbnormalNum(range.getAbnormalNum() + 1);
            }
        } else if (projectBeforeStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL &&
                projectStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL) {
            range.setNormalNum(range.getNormalNum() - 1);
            range.setAbnormalNum(range.getAbnormalNum() + 1);
        } else if (projectBeforeStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL &&
                projectStatus == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL) {
            range.setNormalNum(range.getNormalNum() + 1);
            range.setAbnormalNum(range.getAbnormalNum() - 1);
        }

        // 全部巡完，更新状态
        if (range.getCheckedNum().equals(range.getInspectNum())) {
            range.setRangeStatus(2);
        }

        updateById(range);

        // 更新下一步路线
        update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTaskRange.class)
                .set(AsEquipmentSiteInspectTaskRange::getCanSiteInspect, true)
                .eq(AsEquipmentSiteInspectTaskRange::getSort, range.getSort() + 1)
                .eq(AsEquipmentSiteInspectTaskRange::getTaskId, range.getTaskId()));
        return true;
    }
}
