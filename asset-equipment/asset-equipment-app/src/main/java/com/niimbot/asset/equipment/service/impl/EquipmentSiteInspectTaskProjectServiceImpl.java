package com.niimbot.asset.equipment.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectRangeMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskProjectMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskRangeMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskProject;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskProjectService;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.equipment.EntSntProjectData;
import com.niimbot.equipment.EntSntRangeLocation;
import com.niimbot.equipment.SiteInspectTaskAbnormalDto;
import com.niimbot.equipment.SiteInspectTaskAbnormalQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectDto;
import com.niimbot.equipment.SiteInspectTaskProjectQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectSubmitDto;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/14 16:19
 */
@Service
@RequiredArgsConstructor
public class EquipmentSiteInspectTaskProjectServiceImpl extends ServiceImpl<AsEquipmentSiteInspectTaskProjectMapper, AsEquipmentSiteInspectTaskProject> implements EquipmentSiteInspectTaskProjectService {

    private final AsEquipmentSiteInspectTaskRangeMapper siteInspectTaskRangeMapper;
    private final AsEquipmentSiteInspectRangeMapper inspectRangeMapper;
    private final OrgService orgService;

    @Override
    public IPage<SiteInspectTaskProjectDto> projectPage(SiteInspectTaskProjectQryDto query) {
        IPage<SiteInspectTaskProjectDto> projectPage = getBaseMapper().projectPage(query.buildIPage(), query);
        List<SiteInspectTaskProjectDto> projectList = projectPage.getRecords();
        // 填充 executorsOrg
        Set<Long> empIds = projectList.stream().filter(f -> f.getExecutors() != null)
                .map(SiteInspectTaskProjectDto::getExecutors).collect(Collectors.toSet());
        Map<Long, List<AsOrg>> userOrgMap = orgService.listOrgByEmpIds(new ArrayList<>(empIds));
        for (SiteInspectTaskProjectDto projectDto : projectList) {
            List<AsOrg> orgList = userOrgMap.getOrDefault(projectDto.getExecutors(), new ArrayList<>());
            projectDto.setExecutorsOrgText(orgList.stream().map(k -> k.getOrgName() + "（" + k.getOrgCode() + "）").collect(Collectors.toList()));
        }
        return projectPage;
    }

    @Override
    public SiteInspectTaskProjectDto info(Long id) {
        SiteInspectTaskProjectDto info = getBaseMapper().info(id);
        // 填充 executorsOrg
        if (info.getExecutors() != null) {
            Map<Long, List<AsOrg>> userOrgMap = orgService.listOrgByEmpIds(ListUtil.of(info.getExecutors()));
            List<AsOrg> orgList = userOrgMap.getOrDefault(info.getExecutors(), new ArrayList<>());
            info.setExecutorsOrgText(orgList.stream().map(k -> k.getOrgName() + "（" + k.getOrgCode() + "）").collect(Collectors.toList()));
        }
        return info;
    }

    @Override
    public IPage<SiteInspectTaskAbnormalDto> projectAbnormalPage(SiteInspectTaskAbnormalQryDto query) {
        IPage<SiteInspectTaskAbnormalDto> abnormalPage = getBaseMapper().projectAbnormalPage(query.buildIPage(), query);
        List<SiteInspectTaskAbnormalDto> records = abnormalPage.getRecords();
        Set<Long> taskRangeIds = records.stream().map(SiteInspectTaskAbnormalDto::getTaskRangeId).collect(Collectors.toSet());
        // 补齐点位信息
        Map<Long, String> rangeLocationCache = new HashMap<>();
        if (CollUtil.isNotEmpty(taskRangeIds)) {
            List<SiteInspectTaskRangeDto> taskRangeList = siteInspectTaskRangeMapper.rangeBaseInfos(new ArrayList<>(taskRangeIds));
            Map<Long, SiteInspectTaskRangeDto> taskRangeMap = taskRangeList.stream().collect(Collectors.toMap(SiteInspectTaskRangeDto::getId, k -> k, (k1, k2) -> k1));
            records.forEach(ab -> {
                if (taskRangeMap.containsKey(ab.getTaskRangeId())) {
                    SiteInspectTaskRangeDto rangeDto = taskRangeMap.get(ab.getTaskRangeId());
                    ab.setName(rangeDto.getName());
                    ab.setCode(rangeDto.getCode());
                    // 具体位置
                    if (rangeLocationCache.containsKey(rangeDto.getDataId())) {
                        String location = rangeLocationCache.get(rangeDto.getDataId());
                        ab.setSpecificLocation(location);
                    } else {
                        EntSntRangeLocation rangeLocation = inspectRangeMapper.selectRangeLocation(rangeDto.getDataId(), rangeDto.getDataType());
                        rangeLocationCache.put(rangeDto.getDataId(), rangeLocation.getLocation());
                        ab.setSpecificLocation(rangeLocation.getLocation());
                    }
                }
            });
        }
        return abnormalPage;
    }

    @Override
    public int calcInspectResult(SiteInspectTaskProjectSubmitDto submitDto, Integer configType, EntSntProjectData configData) {
        if (configType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER) {
            // 数值类型
            BigDecimal min = configData.getMin();
            BigDecimal max = configData.getMax();
            if (!NumberUtil.isNumber(submitDto.getInspectResult())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检结果非数字类型");
            }
            BigDecimal value = Convert.toBigDecimal(submitDto.getInspectResult());
            if (value.compareTo(max) <= 0 && value.compareTo(min) >= 0) {
                return EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL;
            }
        } else if (configType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_OPTION) {
            // 下拉单选
            if (!configData.getSelected().contains(submitDto.getInspectResult())) {
                return EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL;
            }
        } else if (configType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_MULTIPLE_OPTION) {
            // 下拉多选
            if (CollUtil.intersection(configData.getSelected(), submitDto.getInspectResultList()).size() == 0) {
                return EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL;
            }
        } else if (configType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_TEXT) {
            // 文本
            if (!BooleanUtil.isTrue(submitDto.getAbnormal())) {
                return EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL;
            }
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检项目格式类型不正确");
        }
        return EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL;
    }

}
