package com.niimbot.asset.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTask;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.equipment.SiteInspectTaskDetailExportDto;
import com.niimbot.equipment.SiteInspectTaskEditExecutorsDto;
import com.niimbot.equipment.SiteInspectTaskInfoDto;
import com.niimbot.equipment.SiteInspectTaskPageDto;
import com.niimbot.equipment.SiteInspectTaskQueryDto;
import com.niimbot.equipment.SiteInspectTaskStatisticsDto;
import com.niimbot.system.AuditableOperateResult;

import java.util.List;

import cn.hutool.core.collection.ListUtil;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:27
 */
public interface EquipmentSiteInspectTaskService extends IService<AsEquipmentSiteInspectTask> {

    // 可操作的任务状态
    List<Integer> CAN_OPT_STATUS = ListUtil.of(
            DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_WAITING,
            DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_DOING,
            DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_EXPIRE);

    IPage<SiteInspectTaskPageDto> taskPage(SiteInspectTaskQueryDto query);

    SiteInspectTaskInfoDto info(Long taskId, Boolean full);

    SiteInspectTaskStatisticsDto taskStatistics();

    boolean executeUpdateTask(Long taskId, int projectBeforeStatus, int projectStatus);

    List<AuditableOperateResult> cancelTask(List<Long> taskIds);

    List<AuditableOperateResult> editExecutors(SiteInspectTaskEditExecutorsDto editExecutorsList);

    List<SiteInspectTaskDetailExportDto> detailExport(Long taskId);
}
