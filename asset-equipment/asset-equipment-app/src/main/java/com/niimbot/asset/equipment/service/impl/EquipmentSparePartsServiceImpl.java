package com.niimbot.asset.equipment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainTaskMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSparePartsMapper;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainTask;
import com.niimbot.asset.equipment.model.AsEquipmentSpareParts;
import com.niimbot.asset.equipment.service.EquipmentSparePartsService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.maintenance.service.AsRepairOrderService;
import com.niimbot.asset.material.model.AsMaterial;
import com.niimbot.asset.material.service.AsMaterialService;
import com.niimbot.asset.material.service.AsMaterialStockService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.model.AsAssetRelation;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AssetRelationService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.equipment.EquipmentSparePartsDto;
import com.niimbot.equipment.SparePartsCreateDto;
import com.niimbot.equipment.SparePartsDropDto;
import com.niimbot.equipment.SparePartsMaterialDto;
import com.niimbot.equipment.SparePartsStatisticsDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.RepairOrderDetailDto;
import com.niimbot.material.MaterialSparePartsDto;
import com.niimbot.material.MaterialStockDto;
import com.niimbot.material.MaterialStockQueryDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/11 下午3:43
 */
@Slf4j
@Service
public class EquipmentSparePartsServiceImpl extends ServiceImpl<AsEquipmentSparePartsMapper, AsEquipmentSpareParts> implements EquipmentSparePartsService {

    private static final String MATERIAL_LOG_TPL = "%s(%s)";

    private static final String CONFIG_RELATION_LOG_TPL = "资产 %s 与备件 %s ，增加关联关系";

    private static final String REMOVE_RELATION_LOG_TPL = "资产 %s 与备件 %s ，解除关联";

    @Autowired
    private AssetRelationService assetRelationService;
    @Autowired
    private AsEquipmentMaintainTaskMapper equipmentMaintainTaskMapper;
    @Autowired
    private AsEquipmentSiteInspectTaskMapper siteInspectTaskMapper;
    @Autowired
    private AsRepairOrderService repairOrderService;
    @Autowired
    private AsMaterialStockService materialStockService;
    @Autowired
    private AsMaterialService materialService;
    @Autowired
    private AssetService assetService;
    @Autowired
    private AsAssetLogService assetLogService;

    @Override
    public List<EquipmentSparePartsDto> queryByAssetId(List<Long> assetIds) {
        if (CollUtil.isEmpty(assetIds)) {
            return Collections.emptyList();
        }

        //查询设备bom信息
        Map<Long, Long> assetRelationMap = new HashMap<>();
        List<AsAssetRelation> assetRelationList = assetRelationService.list(Wrappers.lambdaQuery(AsAssetRelation.class).in(AsAssetRelation::getAssetId, assetIds));
        if (CollUtil.isNotEmpty(assetRelationList)) {
            assetRelationMap = assetRelationList.stream().collect(Collectors.groupingBy(AsAssetRelation::getAssetId, Collectors.counting()));
        }

        //查询备件信息
        Map<Long, Long> sparePartsMap = new HashMap<>();
        List<AsEquipmentSpareParts> equipmentSparePartsList = this.list(Wrappers.lambdaQuery(AsEquipmentSpareParts.class).in(AsEquipmentSpareParts::getAssetId, assetIds));
        if (CollUtil.isNotEmpty(equipmentSparePartsList)) {
            sparePartsMap = equipmentSparePartsList.stream().collect(Collectors.groupingBy(AsEquipmentSpareParts::getAssetId, Collectors.counting()));
        }

        List<EquipmentSparePartsDto> result = new ArrayList<>();
        for (Long item : assetIds) {
            EquipmentSparePartsDto equipmentSparePartsDto = new EquipmentSparePartsDto().setAssetId(item);
            //设置bom数量
            if (Objects.nonNull(assetRelationMap.get(item))) {
                equipmentSparePartsDto.setAssetRelationNum(assetRelationMap.get(item).intValue());
            } else {
                equipmentSparePartsDto.setAssetRelationNum(0);
            }

            //设置备件数量
            if (Objects.nonNull(sparePartsMap.get(item))) {
                equipmentSparePartsDto.setSparePartsNum(new BigDecimal(sparePartsMap.get(item)));
            } else {
                equipmentSparePartsDto.setSparePartsNum(BigDecimal.ZERO);
            }
            result.add(equipmentSparePartsDto);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createSpareParts(SparePartsCreateDto sparePartsCreateDto) {
        if (CollUtil.isEmpty(sparePartsCreateDto.getMaterialInfo())) {
            return true;
        }
        List<AsEquipmentSpareParts> sparePartsList = this.list(Wrappers.lambdaQuery(AsEquipmentSpareParts.class)
                .eq(AsEquipmentSpareParts::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsEquipmentSpareParts::getAssetId, sparePartsCreateDto.getAssetId())
                .eq(AsEquipmentSpareParts::getIsDelete, Boolean.FALSE));

        //备件数量
        int materialSize = sparePartsCreateDto.getMaterialInfo().size();

        if (CollUtil.isNotEmpty(sparePartsList)) {
            List<Long> existMaterialIdList = sparePartsList.stream().map(AsEquipmentSpareParts::getMaterialId).collect(Collectors.toList());
            List<Long> materialIdList = sparePartsCreateDto.getMaterialInfo().stream().map(SparePartsMaterialDto::getMaterialId).collect(Collectors.toList());
            //校验耗材是否已经被当前资产进行关联
            if (!Collections.disjoint(existMaterialIdList, materialIdList)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "备件信息已存在");
            }

            //总的备件数量
            materialSize = materialSize + existMaterialIdList.size();
        }

        //校验备件总数
        if (materialSize > 100) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "一个设备最多支持关联100种耗材");
        }

        List<AsEquipmentSpareParts> equipmentSparePartsList = sparePartsCreateDto.getMaterialInfo().stream().map(item -> {
            AsEquipmentSpareParts spareParts = new AsEquipmentSpareParts()
                    .setAssetId(sparePartsCreateDto.getAssetId())
                    .setMaterialId(item.getMaterialId())
                    .setCompanyId(LoginUserThreadLocal.getCompanyId())
                    .setNum(item.getSparePartsNum())
                    .setInstallLocation(item.getInstallLocation());
            return spareParts;
        }).collect(Collectors.toList());

        // 添加处理类型
        saveSparePartsLog(sparePartsCreateDto);
        return this.saveBatch(equipmentSparePartsList);
    }

    private void saveSparePartsLog(SparePartsCreateDto sparePartsCreateDto) {
        List<SparePartsMaterialDto> materialInfo = sparePartsCreateDto.getMaterialInfo();
        List<Long> materialIds = materialInfo.stream().map(SparePartsMaterialDto::getMaterialId).collect(Collectors.toList());
        Map<Long, AsMaterial> materialMap = queryMaterialMap(materialIds);
        AsAsset asset = assetService.getInfoNoPerm(sparePartsCreateDto.getAssetId());
        if (asset == null) {
            return;
        }
        String text = materialInfo.stream().filter(m -> materialMap.containsKey(m.getMaterialId()))
                .map(m -> {
                    AsMaterial material = materialMap.get(m.getMaterialId());
                    JSONObject materialData = material.getMaterialData();
                    return String.format(MATERIAL_LOG_TPL, materialData.getString("materialName"), materialData.getString("materialCode"));
                }).collect(Collectors.joining("，"));
        text = String.format(CONFIG_RELATION_LOG_TPL,
                String.format(MATERIAL_LOG_TPL, asset.getAssetData().getString("assetName"), asset.getAssetData().getString("assetCode")),
                text);
        AsAssetLog mainAssetLog = new AsAssetLog()
                .setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setAssetId(sparePartsCreateDto.getAssetId())
                .setActionType(AssetConstant.OPT_SPARE_PARTS)
                .setActionName("新增备件")
                .setHandleTime(LocalDateTime.now())
                .setActionContent(text);
        assetLogService.save(mainAssetLog);
    }

    /**
     * 获取耗材信息分组
     *
     * @param materialIds
     * @return
     */
    private Map<Long, AsMaterial> queryMaterialMap(List<Long> materialIds) {
        if (CollUtil.isEmpty(materialIds)) {
            return MapUtil.empty();
        }

        List<AsMaterial> materialList = materialService.list(Wrappers.lambdaQuery(AsMaterial.class)
                .in(AsMaterial::getId, materialIds)
                .select(AsMaterial::getId, AsMaterial::getMaterialData));
        if (CollUtil.isEmpty(materialList)) {
            return MapUtil.empty();
        }

        return materialList.stream().collect(Collectors.toMap(AsMaterial::getId, value -> value, (v1, v2) -> v2));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifySpareParts(SparePartsCreateDto sparePartsCreateDto) {
        if (CollUtil.isEmpty(sparePartsCreateDto.getMaterialInfo())) {
            return true;
        }
        List<Long> materialIdList = sparePartsCreateDto.getMaterialInfo().stream().map(SparePartsMaterialDto::getMaterialId).collect(Collectors.toList());
        List<AsEquipmentSpareParts> equipmentSparePartsList = this.list(Wrappers.lambdaQuery(AsEquipmentSpareParts.class)
                .eq(AsEquipmentSpareParts::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsEquipmentSpareParts::getAssetId, sparePartsCreateDto.getAssetId())
                .in(AsEquipmentSpareParts::getMaterialId, materialIdList)
                .eq(AsEquipmentSpareParts::getIsDelete, Boolean.FALSE));
        if (CollUtil.isEmpty(equipmentSparePartsList) || materialIdList.size() != equipmentSparePartsList.size()) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "备件信息不存在");
        }
        Map<Long, AsMaterial> materialMap = queryMaterialMap(materialIdList);

        List<String> assetLogText = new ArrayList<>();

        Map<Long, SparePartsMaterialDto> sparePartsMap = sparePartsCreateDto.getMaterialInfo().stream().collect(Collectors.toMap(SparePartsMaterialDto::getMaterialId, value -> value, (v1, v2) -> v2));
        List<AsEquipmentSpareParts> modifyParamList = new ArrayList<>();
        for (AsEquipmentSpareParts item : equipmentSparePartsList) {
            SparePartsMaterialDto afterSpareParts = sparePartsMap.get(item.getMaterialId());
            if (materialMap.containsKey(item.getMaterialId())) {
                JSONObject materialData = materialMap.get(item.getMaterialId()).getMaterialData();
                List<String> text = new ArrayList<>();
                text.add("修改备件 " + String.format(MATERIAL_LOG_TPL, materialData.getString("materialName"), materialData.getString("materialCode")) + "信息");
                boolean hasChange = false;
                if (!ObjectUtil.equals(item.getNum(), afterSpareParts.getSparePartsNum())) {
                    text.add("数量由" + item.getNum() + "变为" + afterSpareParts.getSparePartsNum());
                    hasChange = true;
                }
                if (!ObjectUtil.equals(item.getInstallLocation(), afterSpareParts.getInstallLocation())) {
                    text.add("部位由" + item.getInstallLocation() + "变为" + afterSpareParts.getInstallLocation());
                    hasChange = true;
                }
                if (hasChange) {
                    assetLogText.add(String.join("，", text));
                }
            }
            AsEquipmentSpareParts modifyParam = new AsEquipmentSpareParts();
            modifyParam.setId(item.getId());
            modifyParam.setNum(afterSpareParts.getSparePartsNum());
            modifyParam.setInstallLocation(afterSpareParts.getInstallLocation());
            modifyParamList.add(modifyParam);
        }
        this.updateBatchById(modifyParamList);

        if (assetLogText.size() > 0) {
            AsAssetLog mainAssetLog = new AsAssetLog()
                    .setCompanyId(LoginUserThreadLocal.getCompanyId())
                    .setAssetId(sparePartsCreateDto.getAssetId())
                    .setActionType(AssetConstant.OPT_SPARE_PARTS)
                    .setActionName("修改备件信息")
                    .setHandleTime(LocalDateTime.now())
                    .setActionContent(String.join("；", assetLogText));
            assetLogService.save(mainAssetLog);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean dropSpareParts(SparePartsDropDto sparePartsDropDto) {
        if (CollUtil.isEmpty(sparePartsDropDto.getMaterialIdList())) {
            return false;
        }
        LambdaQueryWrapper<AsEquipmentSpareParts> removeWrapper = Wrappers.lambdaQuery(AsEquipmentSpareParts.class)
                .eq(AsEquipmentSpareParts::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsEquipmentSpareParts::getAssetId, sparePartsDropDto.getAssetId())
                .in(AsEquipmentSpareParts::getMaterialId, sparePartsDropDto.getMaterialIdList());
        this.remove(removeWrapper);
        saveRemoveLog(sparePartsDropDto);
        return true;
    }

    private void saveRemoveLog(SparePartsDropDto sparePartsDropDto) {
        List<Long> materialIds = sparePartsDropDto.getMaterialIdList();
        Map<Long, AsMaterial> materialMap = queryMaterialMap(materialIds);
        AsAsset asset = assetService.getInfoNoPerm(sparePartsDropDto.getAssetId());
        if (asset == null) {
            return;
        }
        String text = materialIds.stream().filter(materialMap::containsKey)
                .map(m -> {
                    AsMaterial material = materialMap.get(m);
                    JSONObject materialData = material.getMaterialData();
                    return String.format(MATERIAL_LOG_TPL, materialData.getString("materialName"), materialData.getString("materialCode"));
                }).collect(Collectors.joining("，"));
        text = String.format(REMOVE_RELATION_LOG_TPL,
                String.format(MATERIAL_LOG_TPL, asset.getAssetData().getString("assetName"), asset.getAssetData().getString("assetCode")),
                text);
        AsAssetLog mainAssetLog = new AsAssetLog()
                .setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setAssetId(sparePartsDropDto.getAssetId())
                .setActionType(AssetConstant.OPT_SPARE_PARTS)
                .setActionName("移除备件")
                .setHandleTime(LocalDateTime.now())
                .setActionContent(text);
        assetLogService.save(mainAssetLog);
    }

    @Override
    public List<SparePartsMaterialDto> queryMaterialByAssetId(Long assetId) {
        List<AsEquipmentSpareParts> equipmentSparePartsList = this.list(Wrappers.lambdaQuery(AsEquipmentSpareParts.class)
                .eq(AsEquipmentSpareParts::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsEquipmentSpareParts::getAssetId, assetId)
                .eq(AsEquipmentSpareParts::getIsDelete, Boolean.FALSE));
        if (CollUtil.isEmpty(equipmentSparePartsList)) {
            return Collections.emptyList();
        } else {
            return equipmentSparePartsList.stream().map(item ->
                    new SparePartsMaterialDto()
                            .setMaterialId(item.getMaterialId())
                            .setSparePartsNum(item.getNum())
                            .setInstallLocation(item.getInstallLocation())
            ).collect(Collectors.toList());
        }
    }

    @Override
    public SparePartsStatisticsDto statistics(Long assetId) {
        //保养次数
        int maintainCount = 0;
        //保养金额
        BigDecimal maintainPrice = BigDecimal.ZERO;
        //维修次数
        int repairCount = 0;
        //维修金额
        BigDecimal repairPrice = BigDecimal.ZERO;
        //巡检次数
        int siteInspectCount = 0;

        //设备保养次数：保养任务为保养完成的任务总数
        List<AsEquipmentMaintainTask> equipmentMaintainTaskList = equipmentMaintainTaskMapper.selectList(Wrappers.lambdaQuery(AsEquipmentMaintainTask.class)
                .select(AsEquipmentMaintainTask::getId, AsEquipmentMaintainTask::getTotalPrice)
                .eq(AsEquipmentMaintainTask::getAssetId, assetId)
                .eq(AsEquipmentMaintainTask::getTaskStatus, 4)
                .eq(AsEquipmentMaintainTask::getIsDelete, Boolean.FALSE));
        if (CollUtil.isNotEmpty(equipmentMaintainTaskList)) {
            maintainCount = equipmentMaintainTaskList.size();
            maintainPrice = equipmentMaintainTaskList.stream().map(AsEquipmentMaintainTask::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        siteInspectCount = siteInspectTaskMapper.siteInspectCount(assetId);

        List<RepairOrderDetailDto> finishAssetList = repairOrderService.repairFinishAsset(Collections.singletonList(assetId));
        if (CollUtil.isNotEmpty(finishAssetList)) {
            repairCount = finishAssetList.size();
            repairPrice = finishAssetList.stream().map(RepairOrderDetailDto::getRepairMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        BigDecimal totalFee = maintainPrice.add(repairPrice).setScale(4, RoundingMode.HALF_UP);

        // 返回值
        return new SparePartsStatisticsDto()
                .setMaintainCount(maintainCount)
                .setRepairCount(repairCount)
                .setTotalFee(totalFee)
                .setSiteInspectCount(siteInspectCount);
    }

    @Override
    public List<MaterialSparePartsDto> stockList(Long assetId, Long repoId, List<Long> materialIds) {
        if (Objects.isNull(assetId) || Objects.isNull(repoId)) {
            return Collections.emptyList();
        }

        List<AsEquipmentSpareParts> equipmentSparePartsList = this.list(Wrappers.lambdaQuery(AsEquipmentSpareParts.class)
                .eq(AsEquipmentSpareParts::getAssetId, assetId)
                .eq(AsEquipmentSpareParts::getIsDelete, Boolean.FALSE));

        List<Long> includeMaterialIdList;
        if (CollUtil.isEmpty(materialIds)) {
            includeMaterialIdList = equipmentSparePartsList
                    .stream()
                    .map(AsEquipmentSpareParts::getMaterialId)
                    .collect(Collectors.toList());
        } else {
            includeMaterialIdList = materialIds;
        }

        if (CollUtil.isEmpty(includeMaterialIdList)) {
            return ListUtil.empty();
        }

        //库存查询参数
        MaterialStockQueryDto stockQuery = new MaterialStockQueryDto()
                .setRepositoryId(repoId)
                .setIncludeMaterialIds(includeMaterialIdList);
        stockQuery.setPageNum(1L);
        //最多允许100个备件
        stockQuery.setPageSize(100L);

        IPage<MaterialStockDto> materialStockPage = materialStockService.stockPage(stockQuery);
        if (Objects.isNull(materialStockPage) || CollUtil.isEmpty(materialStockPage.getRecords())) {
            return ListUtil.empty();
        }

        //备件数量关联信息
        Map<Long, BigDecimal> sparePartsNumMap = equipmentSparePartsList.stream().collect(Collectors.toMap(AsEquipmentSpareParts::getMaterialId, AsEquipmentSpareParts::getNum, (v1, v2) -> v2));
        return materialStockPage.getRecords().stream().map(item -> {
            MaterialSparePartsDto materialSparePartsDto = new MaterialSparePartsDto();
            BeanUtils.copyProperties(item, materialSparePartsDto);
            if (Objects.nonNull(sparePartsNumMap.get(item.getMaterialId()))) {
                materialSparePartsDto.setReferNum(sparePartsNumMap.get(item.getMaterialId()));
            } else {
                materialSparePartsDto.setReferNum(BigDecimal.ZERO);
            }
            return materialSparePartsDto;
        }).collect(Collectors.toList());
    }
}
