package com.niimbot.asset.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectPointMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectPoint;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectPointService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.equipment.AuditableRemovePointResult;
import com.niimbot.equipment.EquipmentSiteInspectPointImportDto;
import com.niimbot.equipment.EquipmentSiteInspectPointQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;

@Service
public class EquipmentSiteInspectPointServiceImpl extends ServiceImpl<AsEquipmentSiteInspectPointMapper, AsEquipmentSiteInspectPoint> implements EquipmentSiteInspectPointService {

    @Autowired
    private AsAssetImportErrorService assetImportErrorService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AreaService areaService;

    @Override
    public IPage<AsEquipmentSiteInspectPointDto> pagePoint(EquipmentSiteInspectPointQueryDto queryDto) {
        return this.getBaseMapper().pagePoint(queryDto.buildIPage(), queryDto);
    }

    @Override
    public List<AsEquipmentSiteInspectPointDto> listPoint(EquipmentSiteInspectPointQueryDto queryDto) {
        return this.getBaseMapper().pagePoint(queryDto);
    }

    @Override
    public Long add(AsEquipmentSiteInspectPoint point) {
        // 验证数据唯一性
        checkRepeat(point,false);
        Long id = IdUtils.getId();
        point.setId(id);
        if (!save(point)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return point.getId();
    }

    @Override
    public boolean edit(AsEquipmentSiteInspectPoint inspectPoint) {
        // 验证数据唯一性
        checkRepeat(inspectPoint, true);
        AsEquipmentSiteInspectPoint point = new AsEquipmentSiteInspectPoint();
        point.setId(inspectPoint.getId());
        point.setPointName(inspectPoint.getPointName());
        point.setPointCode(inspectPoint.getPointCode());
        point.setPid(inspectPoint.getPid());
        point.setPointDesc(inspectPoint.getPointDesc());
        point.setPointImages(inspectPoint.getPointImages());
        point.setSpecificLocation(inspectPoint.getSpecificLocation());
        this.updateById(inspectPoint);
        return true;
    }

    @Override
    public AuditableRemovePointResult remove(Long id) {
        //校验点位下是否有进行中或未开始的计划
        Integer planCount = this.baseMapper.getPlanCount(id);
        if (planCount > 0){
            throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-巡检点位下有未开始、执行中的计划");
        }
        AsEquipmentSiteInspectPoint byId = this.getById(id);
        this.remove(new QueryWrapper<AsEquipmentSiteInspectPoint>().lambda().eq(AsEquipmentSiteInspectPoint::getId, id));
        return new AuditableRemovePointResult().setPointName(byId.getPointName()).setPointCode(byId.getPointCode());
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<AsAssetImportError> list = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, taskId)
                .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_INSPECT_POINT));
        return list.stream().map(AsAssetImportError::getExcelJson)
                .collect(Collectors.toList());
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importErrorDto.getTaskId());
        importError.setErrorNum(0);
        importError.setImportType(DictConstant.IMPORT_TYPE_INSPECT_POINT);
        importError.setExcelJson(importErrorDto.getHeadModelList());
        this.assetImportErrorService.save(importError);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSheetData(EquipmentSiteInspectPointImportDto importDto) {
        // 转换实体
        AsEquipmentSiteInspectPoint point = BeanUtil.copyProperties(importDto, AsEquipmentSiteInspectPoint.class);
        // 单个企业巡检点位编码不能重复
        if (this.count(new QueryWrapper<AsEquipmentSiteInspectPoint>().lambda()
                .eq(AsEquipmentSiteInspectPoint::getPointCode, point.getPointCode())) > 0) {
            LuckySheetModel codee = importDto.getSheetModelList().get(2);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("巡检点位编码已存在");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }
        if (importDto.getErrorNum() == 0) {
            Long id = IdUtils.getId();
            point.setId(id);
            this.save(point);
            redisService.hIncr(RedisConstant.companyImportKey("inspectPoint", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("inspectPoint", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            return true;
        } else {
            AsAssetImportError importError = copyToAspointImportError(importDto);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("inspectPoint", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }


    /**
     * 获取最大巡检点位编码
     *
     * @return 结果
     */
    @Override
    public String getMaxCode() {
        return this.getBaseMapper().getMaxAreaCode();
    }

    @Override
    public List<AsEquipmentSiteInspectPointDto> listAll() {
        return this.list(Wrappers.<AsEquipmentSiteInspectPoint>lambdaQuery())
                .stream()
                .map(it -> BeanUtil.copyProperties(it, AsEquipmentSiteInspectPointDto.class))
                .collect(Collectors.toList());
    }

    @Override
    public String getMaxCode(String s) {
        return null;
    }

    private void checkRepeat(AsEquipmentSiteInspectPoint point, boolean isEdit) {
        LambdaQueryWrapper<AsEquipmentSiteInspectPoint> wrapper = new LambdaQueryWrapper<>();
        if (isEdit) {
            wrapper.ne(AsEquipmentSiteInspectPoint::getId, point.getId());
        }
        // 巡检点位编码 唯一性校验
        wrapper.eq(AsEquipmentSiteInspectPoint::getPointCode, point.getPointCode());
        AsEquipmentSiteInspectPoint codeRepeat = this.getOne(wrapper);
        if (ObjectUtil.isNotNull(codeRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "巡检点位编码", point.getPointCode());
        }
    }

    private AsAssetImportError copyToAspointImportError(EquipmentSiteInspectPointImportDto importDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_INSPECT_POINT);
        List<LuckySheetModel> sheetModelList = importDto.getSheetModelList();
        importError.setExcelJson(new ArrayList<>(sheetModelList));
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }

    @Override
    public Integer getPointByAreaId(Long id){
        return this.baseMapper.getPointByAreaId(id);
    }

    @Override
    public AsEquipmentSiteInspectPointDto pointDetail(Long pointId) {
        AsEquipmentSiteInspectPoint point = this.baseMapper.selectById(pointId);
        AsEquipmentSiteInspectPointDto pointDto = BeanUtil.copyProperties(point, AsEquipmentSiteInspectPointDto.class);
        return pointDto;
    }
}
