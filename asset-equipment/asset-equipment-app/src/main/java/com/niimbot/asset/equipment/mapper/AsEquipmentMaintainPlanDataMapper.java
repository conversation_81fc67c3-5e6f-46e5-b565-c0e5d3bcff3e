package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlanData;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@EnableDataPerm
public interface AsEquipmentMaintainPlanDataMapper extends BaseMapper<AsEquipmentMaintainPlanData> {

    /**
     * 查询计划下的保养数据
     *
     * @param planId 计划ID
     * @return List<AsEquipmentMaintainPlanData>
     */
    default List<AsEquipmentMaintainPlanData> selectByPlanId(Long planId) {
        return this.selectList(
                Wrappers.lambdaQuery(AsEquipmentMaintainPlanData.class)
                        .eq(AsEquipmentMaintainPlanData::getPlanId, planId)
        );
    }

    /**
     * 查询计划下指定数据类型的保养数据ID集合
     *
     * @param planId   计划ID
     * @param dataType 数据类型
     * @return ids
     */
    default List<Long> selectDataIdByPlanId(Long planId, Integer dataType) {
        return this.selectList(
                Wrappers.lambdaQuery(AsEquipmentMaintainPlanData.class)
                        .eq(AsEquipmentMaintainPlanData::getPlanId, planId)
                        .eq(Objects.nonNull(dataType), AsEquipmentMaintainPlanData::getDataType, dataType)
        ).stream().map(AsEquipmentMaintainPlanData::getDataId).collect(Collectors.toList());
    }

    /**
     * 模糊查询获取计划ID集合
     *
     * @param companyId 企业ID
     * @param kw        关键字
     * @return ids
     */
    List<Long> selectPlanIdByKw(@Param("companyId") Long companyId, @Param("kw") String kw);

    Set<Long> selectPlanIdKeyword1(@Param("companyId") Long companyId, @Param("kw") String kw);

    Set<Long> selectPlanIdKeyword2(@Param("companyId") Long companyId, @Param("kw") String kw);

}
