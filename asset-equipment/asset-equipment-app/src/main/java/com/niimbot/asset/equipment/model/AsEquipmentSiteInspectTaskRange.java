package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:05
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_site_inspect_task_range", autoResultMap = true)
public class AsEquipmentSiteInspectTaskRange {

    // id
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    // 公司ID（租户ID）
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    // 任务ID
    private Long taskId;

    // 1-未巡完，2-已巡完
    private Integer rangeStatus;

    // 计划路线ID
    private Long rangeId;

    // 应巡项目数
    private Integer inspectNum;

    // 已完成项目数
    private Integer checkedNum;

    // 正常项目数
    private Integer normalNum;

    // 异常项目数
    private Integer abnormalNum;

    // 是否可以巡
    private Boolean canSiteInspect;

    // 处理结果（1-未处理，2-已处理，3-已忽略（只有存在异常才可用））
    private Integer handleStatus;

    // 顺序值
    private Integer sort;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
