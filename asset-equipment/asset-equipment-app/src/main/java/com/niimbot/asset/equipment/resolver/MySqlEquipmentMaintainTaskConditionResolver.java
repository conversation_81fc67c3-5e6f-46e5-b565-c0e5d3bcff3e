package com.niimbot.asset.equipment.resolver;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.equipment.service.EquipmentOrderService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.equipment.MaintainTaskQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;

/**
 * 设备保养任务sql解析器
 *
 * <AUTHOR>
 * @date 2023/10/12 11:39
 */
@Component
public class MySqlEquipmentMaintainTaskConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {

    private static final String FIELD_ORDER_DATA = "order_data";
    private static final String FIELD_ASSET_SNAPSHOT_DATA = "asset_snapshot_data";

    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.%s ->> '$.%s'";

    public static final String DATE_JSON_SQL_SEGMENT_TPL = "LPAD(%s.%s ->> '$.%s', 13, 0)";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.%s ->> '$.%s' as DECIMAL(20,4))";

    @Autowired
    private EquipmentOrderService equipmentOrderService;

    @Autowired
    private AsFormService formService;

//    @Autowired
//    private EquipmentDataPermResolver equipmentDataPermResolver;

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
//        equipmentDataPermResolver.resolvePerms(joiner, tableAlias, OrderFormTypeEnum.EQUIPMENT_MAINTAIN_TASK.getCode());
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition);
                // 特殊处理taskStatus
                if ("taskStatus".equals(condition.getCode())) {
                    List<Integer> taskStatusList = Convert.toList(Integer.class, condition.getQueryData());
                    if (taskStatusList.contains(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING)) {
                        joiner.add(" and " + tableAlias + ".plan_start_time >= now()");
                    }
                    if (taskStatusList.contains(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_EXPIRE)) {
                        condition.setQueryData(ListUtil.of(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING));
                        joiner.add(" and " + tableAlias + ".plan_start_time < now()");
                    }
                }
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        QueryFieldConstant.EQUIPMENT_TASK_EXT_FIELD);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private List<String> getOrderField() {
        FormVO orderFormVO = formService.getTplByType(OrderFormTypeEnum.EQUIPMENT_MAINTAIN_TASK.getBizType());
        if (orderFormVO == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单查询错误");
        }
        return orderFormVO.getFormFields().stream().map(FormFieldCO::getFieldCode).collect(Collectors.toList());
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition) {
        List<String> orderField = getOrderField();
        if (QueryFieldConstant.EQUIPMENT_TASK_EXT_FIELD.containsKey(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        } else {
            boolean contains = orderField.contains(condition.getCode());
            return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, contains ? FIELD_ORDER_DATA : FIELD_ASSET_SNAPSHOT_DATA, condition.getCode());
        }
    }

    public Page<?> buildOrderSort(String tableAlias, MaintainTaskQueryDto queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_equipment_maintain_task";
        }
        Page<?> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = equipmentOrderService.sortField(EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK);
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<String> orderField = getOrderField();
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.EQUIPMENT_TASK_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    boolean contains = orderField.contains(column);
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(NUM_JSON_SQL_SEGMENT_TPL, tableAlias, contains ? FIELD_ORDER_DATA : FIELD_ASSET_SNAPSHOT_DATA, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(DATE_JSON_SQL_SEGMENT_TPL, tableAlias, contains ? FIELD_ORDER_DATA : FIELD_ASSET_SNAPSHOT_DATA, column));
                    } else {
                        order.setColumn(String.format(JSON_SQL_SEGMENT_TPL, tableAlias, contains ? FIELD_ORDER_DATA : FIELD_ASSET_SNAPSHOT_DATA, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }

}
