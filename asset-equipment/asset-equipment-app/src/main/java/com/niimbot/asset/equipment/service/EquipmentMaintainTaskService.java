package com.niimbot.asset.equipment.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainTask;
import com.niimbot.equipment.AuditableCancelTaskResult;
import com.niimbot.equipment.AuditableResumeTaskResult;
import com.niimbot.equipment.AuditableSubmitTaskResult;
import com.niimbot.equipment.MaintainTaskCreateDto;
import com.niimbot.equipment.MaintainTaskDto;
import com.niimbot.equipment.MaintainTaskQueryDto;
import com.niimbot.equipment.MaintainTaskSparePartsQueryDto;
import com.niimbot.material.MaterialSparePartsDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/12 11:16
 */
public interface EquipmentMaintainTaskService extends IService<AsEquipmentMaintainTask> {

    IPage<MaintainTaskDto> pageTask(MaintainTaskQueryDto query);

    JSONObject equipmentInfo(Long orderId);

    MaintainTaskDto info(Long orderId);

    /**
     * 备件分页是详情查询，如果备件为空则返回空
     */
    IPage<MaterialSparePartsDto> sparePartsPage(MaintainTaskSparePartsQueryDto queryDto);

    /**
     * 备件分页是新增/编辑查询，如果备件为空则返回默认值
     */
    List<MaterialSparePartsDto> sparePartsList(MaintainTaskSparePartsQueryDto queryDto);

    JSONObject materialInfo(Long orderId, Long materialId);

    AuditableSubmitTaskResult temporary(MaintainTaskCreateDto createDto);

    AuditableSubmitTaskResult submit(MaintainTaskCreateDto createDto, boolean submit);

    AuditableCancelTaskResult cancel(List<Long> orderIds);

    AuditableResumeTaskResult resume(List<Long> orderIds);

    List<Long> cateNewAssetIds(Long planId, Long companyId);

    Boolean userHasTask(Long userId);

}
