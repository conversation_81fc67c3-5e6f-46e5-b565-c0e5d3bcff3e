package com.niimbot.asset.equipment.controller;

import com.niimbot.asset.equipment.service.EquipmentMaintainDistributeTasksService;
import com.niimbot.asset.equipment.service.EquipmentMaintainPlanService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.equipment.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/equipment/maintain/plan")
public class EquipmentMaintainPlanServiceController {

    private final EquipmentMaintainPlanService equipmentMaintainPlanService;


    private final EquipmentMaintainDistributeTasksService distributeTasksService;

    @PostMapping("/preview")
    public EntMatPlanPreview previewPlan(@RequestBody CreateEntMatPlan plan) {
        return equipmentMaintainPlanService.previewPlan(plan);
    }

    @PostMapping("/create")
    public AuditableCreatePlanResult createPlan(@RequestBody CreateEntMatPlan plan) {
        AuditableCreatePlanResult result = equipmentMaintainPlanService.createPlan(plan);
        if (Objects.nonNull(result) && Objects.nonNull(result.getPlanId())) {
            // 下发任务
            distributeTasksService.distributeTasksAsync(result.getPlanId(), LoginUserThreadLocal.getCompanyId());
        }
        return result;
    }

    @PostMapping("/stop")
    public AuditableStopPlanResult stopPlan(@RequestBody StopEntMatPlan union) {
        return equipmentMaintainPlanService.stopPlan(union);
    }

    @PostMapping("/remove")
    public AuditableRemovePlanResult removePlan(@RequestBody RemoveEntMatPlan remove) {
        return equipmentMaintainPlanService.removePlan(remove);
    }

    @PostMapping("/reUser")
    public Boolean rePlanUser(@RequestBody ReEntMatPlanUser change) {
        return equipmentMaintainPlanService.rePlanUser(change);
    }

    @GetMapping("/detail")
    public EntMatPlanDetail detailPlan(@RequestParam("planId") Long planId) {
        return equipmentMaintainPlanService.detailPlan(planId);
    }

    @GetMapping("/cate")
    public List<EntMatPlanEntCateData> listEntCate(@RequestParam("planId") Long planId) {
        return equipmentMaintainPlanService.listEntCate(planId);
    }

    @PostMapping("/count")
    public List<EntMatPlanCounter> countPlan(@RequestBody List<Long> planIds) {
        return equipmentMaintainPlanService.countPlan(planIds);
    }

    @PostMapping("/search")
    public PageUtils<EntMatPlan> searchPlan(@RequestBody EntMatPlanSearch search) {
        return equipmentMaintainPlanService.searchPlan(search);
    }

    @PostMapping("/pageSelectedEntMatData")
    public PageUtils<EntMatPlanEntData> pageSelectedEntMatData(@RequestBody GetSelectedEntData get) {
        return equipmentMaintainPlanService.pageSelectedEntMatData(get);
    }

    @PostMapping("/pageSelectedEntSrPrData")
    public PageUtils<EntMatPlanSrPrData> pageSelectedEntSrPrData(@RequestBody GetSelectedSrPrData get) {
        return equipmentMaintainPlanService.pageSelectedEntSrPrData(get);
    }

}
