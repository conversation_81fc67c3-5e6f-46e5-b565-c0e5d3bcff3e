package com.niimbot.asset.equipment.controller;

import com.niimbot.asset.equipment.service.EquipmentOrderService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/10/11 15:51
 */
@RestController
@RequestMapping("server/equipment/order")
public class EquipmentOrderServiceController {

    private final EquipmentOrderService orderService;

    @Autowired
    public EquipmentOrderServiceController(EquipmentOrderService orderService) {
        this.orderService = orderService;
    }

    /**
     * 单据表单查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "/sortField/{orderType}")
    public QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType) {
        return orderService.sortField(orderType);
    }

    /**
     * 单据表单查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "/form/{orderType}")
    public FormVO getForm(@PathVariable("orderType") Integer orderType) {
        return orderService.getForm(orderType, LoginUserThreadLocal.getCompanyId());
    }


}
