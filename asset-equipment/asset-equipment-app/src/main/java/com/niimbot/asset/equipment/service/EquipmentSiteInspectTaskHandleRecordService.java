package com.niimbot.asset.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskHandleRecord;
import com.niimbot.equipment.SiteInspectTaskHandleRecordDto;

/**
 * <AUTHOR>
 * @date 2023/11/18 11:38
 */
public interface EquipmentSiteInspectTaskHandleRecordService extends IService<AsEquipmentSiteInspectTaskHandleRecord> {

    SiteInspectTaskHandleRecordDto handleRecordInfo(Long rangeId);

}
