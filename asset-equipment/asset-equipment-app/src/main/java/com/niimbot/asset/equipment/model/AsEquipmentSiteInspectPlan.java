package com.niimbot.asset.equipment.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 设备巡检计划
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_site_inspect_plan", autoResultMap = true)
public class AsEquipmentSiteInspectPlan {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TenantFilterColumn
    private Long companyId;

    private String planNo;

    private Integer planStatus;

    private Integer routeType;

    private Integer issuedTaskNum;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject planData;

    @TableLogic
    private Boolean isDelete;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
