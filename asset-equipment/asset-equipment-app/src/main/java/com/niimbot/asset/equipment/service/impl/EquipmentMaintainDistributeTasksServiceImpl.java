package com.niimbot.asset.equipment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanContentMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanDataMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentMaintainPlanMapper;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlan;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlanContent;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlanData;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainTask;
import com.niimbot.asset.equipment.service.EquipmentMaintainDistributeTasksService;
import com.niimbot.asset.equipment.service.EquipmentMaintainTaskService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.equipment.CalculateTask;
import com.niimbot.equipment.EntMatPlanData;
import com.niimbot.equipment.Periodic;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/18 11:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EquipmentMaintainDistributeTasksServiceImpl implements EquipmentMaintainDistributeTasksService {

    private final AsEquipmentMaintainPlanMapper planMapper;
    private final AsEquipmentMaintainPlanContentMapper planContentMapper;
    private final AsEquipmentMaintainPlanDataMapper planDataMapper;
    private final EquipmentMaintainTaskService taskService;
    private final AssetService assetService;

    private final static String MAINTAIN_DISTRIBUTE_TASKS = "maintainDistributeTasks";

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource
    private RedisService redisService;
    @Resource
    private RedisDistributeLock redisDistributeLock;

    /**
     * 如果计划是分类，需要每日定时更新新增的资产数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void execute() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }
        redisDistributeLock.lock(MAINTAIN_DISTRIBUTE_TASKS, 30L, TimeUnit.MINUTES, v -> {
            List<AsEquipmentMaintainPlan> maintainPlanList = planMapper.selectList(Wrappers.lambdaQuery(AsEquipmentMaintainPlan.class)
                    .eq(AsEquipmentMaintainPlan::getRangeType, EquipmentConstant.RANGE_TYPE_IS_CATE)
                    .eq(AsEquipmentMaintainPlan::getPlanStatus, EquipmentConstant.PLAN_STATUS_IS_EXEC));
            EquipmentMaintainDistributeTasksService bean = SpringUtil.getBean(EquipmentMaintainDistributeTasksService.class);
            maintainPlanList.parallelStream().forEach(bean::addCateAssetTask);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCateAssetTask(AsEquipmentMaintainPlan maintainPlan) {
        log.info("addCateAssetTask, company = {}, plan = {}", maintainPlan.getCompanyId(), maintainPlan.getId());
        List<Long> assetIds = taskService.cateNewAssetIds(maintainPlan.getId(), maintainPlan.getCompanyId());
        log.info("addCateAssetTask company = {}, plan = {}, asset size = {}", maintainPlan.getCompanyId(), maintainPlan.getId(), assetIds.size());
        if (CollUtil.isEmpty(assetIds)) {
            return;
        }
        // 生成快照
        List<AsAsset> assetList = assetService.listByIdsNoPerm(assetIds);
        Map<Long, JSONObject> assetSnapshotMap = assetService.getAssetSnapshot(assetList);

        // 查询保养内容
        List<AsEquipmentMaintainPlanContent> planContentList = planContentMapper.selectByPlanId(maintainPlan.getId());
        JSONArray planContentJsonArray = (JSONArray) JSON.toJSON(planContentList);
        JSONObject orderData = new JSONObject();
        orderData.put("maintainContent", planContentJsonArray);
        orderData.put("replacementPart", "否");

        List<AsEquipmentMaintainTask> maintainTaskSaveList = new ArrayList<>();

        AsEquipmentMaintainTask maintainTask = new AsEquipmentMaintainTask();
        maintainTask.setCompanyId(maintainPlan.getCompanyId())
                .setTaskStatus(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING)
                .setPlanId(maintainPlan.getId())
                .setPlanNo(maintainPlan.getPlanNo())
                .setPlanName(maintainPlan.getPlanData().getString(EntMatPlanData.Fields.planName))
                .setMaintainUser(maintainPlan.getPlanData().getLong(EntMatPlanData.Fields.maintainUser));

        AsEquipmentMaintainTask lastMaintainTask = taskService.getOne(Wrappers.lambdaQuery(AsEquipmentMaintainTask.class)
                .eq(AsEquipmentMaintainTask::getCompanyId, maintainPlan.getCompanyId())
                .eq(AsEquipmentMaintainTask::getPlanId, maintainPlan.getId())
                .orderByDesc(AsEquipmentMaintainTask::getId), false);
        AtomicInteger idx = new AtomicInteger(1);
        try {
            int maxIdx = -1;
            if (lastMaintainTask != null && StrUtil.isNotEmpty(lastMaintainTask.getTaskNo())) {
                String taskNo = lastMaintainTask.getTaskNo();
                log.info("addCateAssetTask company = {}, plan = {}, maxTaskNo = {}", maintainPlan.getCompanyId(), maintainPlan.getId(), taskNo);
                maxIdx = Convert.toInt(taskNo.replace(lastMaintainTask.getPlanNo() + "-", ""), -1);
            }
            if (maxIdx < 0) {
                log.error("addCateAssetTask max idx error, company = {}, plan = {}", maintainPlan.getCompanyId(), maintainPlan.getId());
                return;
            }
            idx.set(maxIdx + 1);
        } catch (Exception e) {
            log.error("addCateAssetTask max idx error, company = {}, plan = {}, {}", maintainPlan.getCompanyId(), maintainPlan.getId(), e.getMessage(), e);
            return;
        }

        List<LocalDateTime> obtain = taskDateRange(maintainPlan, LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        for (Map.Entry<Long, JSONObject> entry : assetSnapshotMap.entrySet()) {
            Long assetId = entry.getKey();
            JSONObject data = entry.getValue();
            for (LocalDateTime dateTime : obtain) {
                AsEquipmentMaintainTask saveTask = BeanUtil.copyProperties(maintainTask, AsEquipmentMaintainTask.class);
                saveTask.setTaskNo(maintainPlan.getPlanNo() + idx.getAndIncrement())
                        .setAssetId(assetId)
                        .setAssetSnapshotData(data)
                        .setOrderData(orderData)
                        .setPlanStartTime(dateTime);
                maintainTaskSaveList.add(maintainTask);
            }
        }
        taskService.saveBatch(maintainTaskSaveList);
    }

    private List<LocalDateTime> taskDateRange(AsEquipmentMaintainPlan maintainPlan, Long taskBeginTime) {
        String taskType = maintainPlan.getPlanData().getString(EntMatPlanData.Fields.taskType);
        List<LocalDateTime> obtain = new ArrayList<>();
        // 循环任务
        if (EquipmentConstant.TASK_TYPE_IS_PERIODICITY.equals(taskType)) {
            Long taskEndTime = maintainPlan.getPlanData().getLong(EntMatPlanData.Fields.taskEndTime);
            Periodic periodic = maintainPlan.getPlanData().getJSONObject(EntMatPlanData.Fields.periodic).toJavaObject(Periodic.class);
            CalculateTask calculateTask = new CalculateTask(taskBeginTime, taskEndTime, periodic.getInterval(), periodic.getUnit());
            obtain = calculateTask.obtain();
        } else if (EquipmentConstant.TASK_TYPE_IS_TIMELINESS.equals(taskType)) {
            // 一次性任务
            obtain.add(Instant.ofEpochMilli(taskBeginTime).atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        return obtain;
    }

    @Override
    public void distributeTasksAsync(Long planId, Long companyId) {
        taskExecutor.execute(() -> distributeTasks(planId, companyId));
    }

    @Override
    public Boolean distributeTasks(Long planId, Long companyId) {
        redisService.hSet(MAINTAIN_DISTRIBUTE_TASKS, Convert.toStr(planId), 0L);
        try {
            // 查询是否执行中
            AsEquipmentMaintainPlan maintainPlan = planMapper.selectOne(Wrappers.lambdaQuery(AsEquipmentMaintainPlan.class)
                    .eq(AsEquipmentMaintainPlan::getId, planId)
                    .eq(AsEquipmentMaintainPlan::getCompanyId, companyId)
                    .eq(AsEquipmentMaintainPlan::getPlanStatus, EquipmentConstant.PLAN_STATUS_IS_EXEC));
            if (maintainPlan == null) {
                log.info("maintainPlan is null, planId={}, companyId={}", planId, companyId);
                return true;
            }
            // 查询保养内容
            List<AsEquipmentMaintainPlanContent> planContentList = planContentMapper.selectByPlanId(planId);
            JSONArray planContentJsonArray = (JSONArray) JSON.toJSON(planContentList);
            JSONObject orderData = new JSONObject();
            orderData.put("maintainContent", planContentJsonArray);
            orderData.put("replacementPart", "否");

            // 查询设备/分类
            List<AsEquipmentMaintainPlanData> equipmentMaintainPlanData = planDataMapper.selectList(
                    Wrappers.lambdaQuery(AsEquipmentMaintainPlanData.class)
                            .eq(AsEquipmentMaintainPlanData::getPlanId, planId)
                            .eq(AsEquipmentMaintainPlanData::getCompanyId, companyId)
                            .in(AsEquipmentMaintainPlanData::getDataType, ListUtil.of(EquipmentConstant.RANGE_TYPE_IS_MAS, EquipmentConstant.RANGE_TYPE_IS_CATE)));
            if (CollUtil.isEmpty(equipmentMaintainPlanData)) {
                log.info("equipmentMaintainPlanData is null, planId={}, companyId={}", planId, companyId);
                return true;
            }
            // 判断如果是分类，需要去关联查询
            List<Long> dataIds = equipmentMaintainPlanData.stream().map(AsEquipmentMaintainPlanData::getDataId).collect(Collectors.toList());
            List<AsAsset> assetList;
            if (maintainPlan.getRangeType().equals(EquipmentConstant.RANGE_TYPE_IS_CATE)) {
                assetList = assetService.list(Wrappers.lambdaQuery(AsAsset.class)
                        .eq(AsAsset::getCompanyId, companyId)
                        .ne(BooleanUtil.isTrue(maintainPlan.getFilterDispose()), AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE)
                        .in(AsAsset::getAssetCategory, dataIds.stream().map(Convert::toStr).collect(Collectors.toList()))
                        .orderByAsc(AsAsset::getId));
            } else {
                assetList = assetService.list(Wrappers.lambdaQuery(AsAsset.class)
                        .eq(AsAsset::getCompanyId, companyId)
                        .ne(BooleanUtil.isTrue(maintainPlan.getFilterDispose()), AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE)
                        .in(AsAsset::getId, dataIds)
                        .orderByAsc(AsAsset::getId));
            }

            // 生成快照
            Map<Long, JSONObject> assetSnapshotMap = assetService.getAssetSnapshot(assetList);
            List<AsEquipmentMaintainTask> maintainTaskSaveList = new ArrayList<>();
            AtomicInteger idx = new AtomicInteger(1);
            AsEquipmentMaintainTask maintainTask = new AsEquipmentMaintainTask();
            maintainTask.setCompanyId(companyId)
                    .setTaskStatus(DictConstant.EQUIPMENT_MAINTAIN_TASK_STATUS_WAITING)
                    .setPlanId(maintainPlan.getId())
                    .setPlanNo(maintainPlan.getPlanNo())
                    .setPlanName(maintainPlan.getPlanData().getString(EntMatPlanData.Fields.planName))
                    .setMaintainUser(maintainPlan.getPlanData().getLong(EntMatPlanData.Fields.maintainUser));

            Long taskBeginTime = maintainPlan.getPlanData().getLong(EntMatPlanData.Fields.taskBeginTime);
            List<LocalDateTime> obtain = taskDateRange(maintainPlan, taskBeginTime);
            for (Map.Entry<Long, JSONObject> entry : assetSnapshotMap.entrySet()) {
                Long assetId = entry.getKey();
                JSONObject data = entry.getValue();
                for (LocalDateTime dateTime : obtain) {
                    AsEquipmentMaintainTask saveTask = BeanUtil.copyProperties(maintainTask, AsEquipmentMaintainTask.class);
                    saveTask.setTaskNo(maintainPlan.getPlanNo() + "-" + idx.getAndIncrement())
                            .setAssetId(assetId)
                            .setAssetSnapshotData(data)
                            .setOrderData(orderData)
                            .setPlanStartTime(dateTime);
                    maintainTaskSaveList.add(saveTask);
                    // 1000直接提交
                    if (maintainTaskSaveList.size() == 1000) {
                        taskService.saveBatch(maintainTaskSaveList);
                        maintainTaskSaveList.clear();
                        redisService.hSet(MAINTAIN_DISTRIBUTE_TASKS, Convert.toStr(planId), assetId);
                    }
                }
            }
            taskService.saveBatch(maintainTaskSaveList);
        } finally {
            redisService.hDel(MAINTAIN_DISTRIBUTE_TASKS, Convert.toStr(planId));
        }
        return true;
    }

}
