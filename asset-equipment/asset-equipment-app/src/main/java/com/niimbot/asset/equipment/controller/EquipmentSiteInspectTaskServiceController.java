package com.niimbot.asset.equipment.controller;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.schedule.EquipmentSiteInspectTaskScheduler;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskAggregateService;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.equipment.*;
import com.niimbot.system.AuditableOperateResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 13:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/equipment/siteInspect/task")
public class EquipmentSiteInspectTaskServiceController {

    private final EquipmentSiteInspectTaskService siteInspectTaskService;
    private final EquipmentSiteInspectTaskAggregateService siteInspectTaskAggregateService;
    private final MessageService messageService;

    @GetMapping("/distributeTasks")
    public boolean distributeTasks() {
        SpringUtil.getBean(EquipmentSiteInspectTaskScheduler.class).distributeTasks();
        return true;
    }


    @PostMapping("/page")
    public IPage<SiteInspectTaskPageDto> page(@RequestBody SiteInspectTaskQueryDto query) {
        return siteInspectTaskService.taskPage(query);
    }

    @GetMapping("/info")
    public SiteInspectTaskInfoDto info(@RequestParam("taskId") Long taskId,
                                       @RequestParam(value = "full", required = false, defaultValue = "false") Boolean full) {
        return siteInspectTaskService.info(taskId, full);
    }

    @GetMapping("/statistics")
    public SiteInspectTaskStatisticsDto taskStatistics() {
        return siteInspectTaskService.taskStatistics();
    }

    @PostMapping("/close/{taskId}")
    public Boolean closeTask(@PathVariable("taskId") Long taskId) {
        return siteInspectTaskAggregateService.closeTask(taskId);
    }

    @PostMapping("/cancel")
    public List<AuditableOperateResult> cancelTask(@RequestBody List<Long> taskIds) {
        return siteInspectTaskService.cancelTask(taskIds);
    }

    @PostMapping("/edit/executors")
    public List<AuditableOperateResult> editExecutors(@RequestBody SiteInspectTaskEditExecutorsDto editExecutorsList) {
        List<AuditableOperateResult> results = siteInspectTaskService.editExecutors(editExecutorsList);
        messageService.sendInnerMessage(MsgSendCmd.entSntTaskCreate(editExecutorsList.getTaskIds(), LoginUserThreadLocal.getCompanyId()));
        return results;
    }

    @GetMapping("/detail/export")
    public List<SiteInspectTaskDetailExportDto> detailExport(@RequestParam("taskId") Long taskId) {
        return siteInspectTaskService.detailExport(taskId);
    }

}
