package com.niimbot.asset.equipment.service;

import com.niimbot.equipment.SiteInspectDistributeTaskDto;
import com.niimbot.equipment.SiteInspectTaskHandleOrderDto;
import com.niimbot.equipment.SiteInspectTaskProjectSubmitDto;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.system.AuditableOperateResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/15 16:11
 */
public interface EquipmentSiteInspectTaskAggregateService {

    Boolean closeTask(Long taskId);

    Boolean projectSubmit(SiteInspectTaskProjectSubmitDto submitDto);

    List<Long> saveDistributeTasks(SiteInspectDistributeTaskDto taskDto);

    AuditableOperateResult handleIgnore(Long rangeId);

    SiteInspectTaskRangeDto rangeScan(Long taskId, Long pointId);

    void handleCheck(Long rangeId);

    AuditableOperateResult assetHandle(SiteInspectTaskHandleOrderDto handleOrderDto);
}
