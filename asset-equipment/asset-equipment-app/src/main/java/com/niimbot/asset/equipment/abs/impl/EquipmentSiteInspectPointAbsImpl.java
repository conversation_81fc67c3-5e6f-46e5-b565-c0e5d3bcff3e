package com.niimbot.asset.equipment.abs.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectPoint;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectPointService;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.system.abs.EquipmentSiteInspectPointAbs;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/client/abs/equipment/site/inspect/point")
public class EquipmentSiteInspectPointAbsImpl implements EquipmentSiteInspectPointAbs {

    @Resource
    private EquipmentSiteInspectPointService pointService;

    @Resource
    private AreaService areaService;

    @Override
    public Integer getPointCount(Long id) {
        return pointService.getPointByAreaId(id);
    }

    @Override
    public AsEquipmentSiteInspectPointDto getById(Long id) {
        AsEquipmentSiteInspectPoint point = pointService.getById(id);
        if (Objects.isNull(point)) {
            return new AsEquipmentSiteInspectPointDto();
        }
        AsEquipmentSiteInspectPointDto dto = BeanUtil.copyProperties(point, AsEquipmentSiteInspectPointDto.class);
        AsArea area = areaService.getById(dto.getPid());
        if (Objects.nonNull(area) && StrUtil.isNotBlank(area.getAreaName())) {
            dto.setStorageAreaText(area.getAreaName());
        }
        return dto;
    }
}
