package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlan;
import com.niimbot.equipment.EntMatPlan;
import com.niimbot.equipment.EntMatPlanSearch;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@EnableDataPerm
public interface AsEquipmentMaintainPlanMapper extends BaseMapper<AsEquipmentMaintainPlan> {

    /**
     * 设备保养计划分页搜索
     *
     * @param page       分页参数 + 排序规则
     * @param search     搜索条件
     * @param perms      权限SQL
     * @param conditions 动态条件SQL
     * @return page
     */
    IPage<EntMatPlan> selectPageSearch
    (
            @Param("page") Page<?> page,
            @Param("ew") EntMatPlanSearch search,
            @Param("conditions") String conditions
    );

    /**
     * 获取负责人的计划
     *
     * @param maintainUserId 负责人ID
     * @return list
     */
    List<AsEquipmentMaintainPlan> selectByMaintainUser(@Param("maintainUserId") Long maintainUserId);

}
