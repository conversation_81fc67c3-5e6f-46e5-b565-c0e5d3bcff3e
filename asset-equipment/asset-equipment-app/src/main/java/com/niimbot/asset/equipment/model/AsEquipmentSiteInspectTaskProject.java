package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/14 11:48
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_site_inspect_task_project", autoResultMap = true)
public class AsEquipmentSiteInspectTaskProject {

    // 主键
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    // 企业ID
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    // 状态（0-未检，1-正常，2-异常，3-漏检'）
    private Integer status;

    // 巡检任务ID
    private Long taskId;

    // 巡检路线ID
    private Long taskRangeId;

    // 项目ID
    private Long projectId;

    // 巡检结果
    private String inspectResult;

    // 巡检结果
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> inspectResultList;

    // 巡检照片
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> inspectImage;

    // 巡检备注
    private String inspectRemark;

    // 巡检人
    private Long executors;

    // 提交时间
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
