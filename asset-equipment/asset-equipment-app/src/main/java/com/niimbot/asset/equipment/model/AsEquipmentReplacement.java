package com.niimbot.asset.equipment.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/12 15:37
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_replacement", autoResultMap = true)
public class AsEquipmentReplacement {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公司ID
     */
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    private Long maintainTaskId;

    private Long materialId;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject materialSnapshotData;

    @ApiModelProperty(value = "参考数量")
    private BigDecimal referNum;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal ckNum;

    @ApiModelProperty(value = "出库金额")
    private BigDecimal ckPrice;

    @ApiModelProperty(value = "入库单价")
    private BigDecimal ckUnitPrice;

}
