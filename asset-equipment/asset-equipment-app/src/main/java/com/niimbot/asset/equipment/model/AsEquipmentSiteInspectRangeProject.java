package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 设备巡检范围与项目关联表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "as_equipment_site_inspect_range_project", autoResultMap = true)
public class AsEquipmentSiteInspectRangeProject {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Long rangeId;

    private Long projectId;

    private Long companyId;

}
