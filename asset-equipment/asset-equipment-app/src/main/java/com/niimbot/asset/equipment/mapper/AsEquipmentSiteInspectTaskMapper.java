package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTask;
import com.niimbot.equipment.SiteInspectTaskDetailExportDto;
import com.niimbot.equipment.SiteInspectTaskPageDto;
import com.niimbot.equipment.SiteInspectTaskQueryDto;
import com.niimbot.equipment.SiteInspectTaskStatisticsDto;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:26
 */
@EnableDataPerm(excludeMethodName = {"selectForWaitTaskMsg"})
public interface AsEquipmentSiteInspectTaskMapper extends BaseMapper<AsEquipmentSiteInspectTask> {

    IPage<SiteInspectTaskPageDto> taskPage(Page<Object> buildIPage,
                                           @Param("ew") SiteInspectTaskQueryDto query,
                                           @Param("currentUserId") Long currentUserId,
                                           @Param("specialStatus") Integer specialStatus);

    SiteInspectTaskStatisticsDto taskStatistics(@Param("currentUserId") Long currentUserId);

    List<SiteInspectTaskPageDto> selectForWaitTaskMsg(@Param("companyId") Long companyId, @Param("unit") String unit, @Param("interval") Integer interval);

    List<SiteInspectTaskDetailExportDto> detailExport(@Param("taskId") Long taskId);

    int siteInspectCount(Long assetId);
}
