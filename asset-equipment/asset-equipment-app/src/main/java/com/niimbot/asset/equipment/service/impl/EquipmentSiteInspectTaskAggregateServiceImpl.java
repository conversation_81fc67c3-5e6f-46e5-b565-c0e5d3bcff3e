package com.niimbot.asset.equipment.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectPlanMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectRangeMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskRangeMapper;
import com.niimbot.asset.equipment.model.*;
import com.niimbot.asset.equipment.service.*;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.equipment.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDto;
import com.niimbot.system.AuditableOperateResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/15 16:14
 */
@Service
@RequiredArgsConstructor
public class EquipmentSiteInspectTaskAggregateServiceImpl implements EquipmentSiteInspectTaskAggregateService {

    private final MessageService messageService;
    private final AsEquipmentSiteInspectPlanMapper planMapper;
    private final AsEquipmentSiteInspectRangeMapper rangeMapper;

    private final EquipmentSiteInspectTaskService taskService;
    private final EquipmentSiteInspectTaskRangeService taskRangeService;
    private final AsEquipmentSiteInspectTaskRangeMapper taskRangeMapper;
    private final EquipmentSiteInspectTaskProjectService taskProjectService;
    private final EquipmentSiteInspectTaskHandleRecordService handleRecordService;
    private final CacheResourceUtil cacheResourceUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeTask(Long taskId) {
        // 限制操作人（只有超管，巡检管理员，巡检人能操作）
        AsEquipmentSiteInspectTask task = limitOperator(taskId);

        // 判断任务状态
        if (!EquipmentSiteInspectTaskService.CAN_OPT_STATUS.contains(task.getTaskStatus())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检任务已结束，不允许操作");
        }

        // 更新任务状态
        taskService.update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTask.class)
                .set(AsEquipmentSiteInspectTask::getTaskStatus, DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_FINISH)
                .set(AsEquipmentSiteInspectTask::getFinishTime, LocalDateTime.now())
                .eq(AsEquipmentSiteInspectTask::getId, taskId));

        // 把所有未检转成漏检
        taskProjectService.update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTaskProject.class)
                .set(AsEquipmentSiteInspectTaskProject::getStatus, EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_IGNORE)
                .eq(AsEquipmentSiteInspectTaskProject::getStatus, EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_UNCHECK)
                .eq(AsEquipmentSiteInspectTaskProject::getTaskId, taskId));

        // 发送任务完成消息
        messageService.sendInnerMessage(MsgSendCmd.entSntCloseTaskAbnormal(taskId, task.getCompanyId()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean projectSubmit(SiteInspectTaskProjectSubmitDto submitDto) {
        SiteInspectTaskProjectDto projectDto = taskProjectService.info(submitDto.getId());
        BusinessExceptionUtil.checkNotNull(projectDto, "巡检项目不存在");

        // 限制操作人（只有超管，巡检管理员，巡检人能操作）
        AsEquipmentSiteInspectTask task = limitOperator(projectDto.getTaskId());

        // 判断任务状态
        if (!EquipmentSiteInspectTaskService.CAN_OPT_STATUS.contains(task.getTaskStatus())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检任务已结束，不允许操作");
        }

        if (!projectDto.getConfigType().equals(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_TEXT)) {
            // 校验数据类型
            if (projectDto.getConfigType().equals(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_MULTIPLE_OPTION)) {
                submitDto.setInspectResult(null);
                if (CollUtil.isEmpty(submitDto.getInspectResultList())) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检结果不能为空");
                }
            } else {
                submitDto.setInspectResultList(ListUtil.empty());
                if (StrUtil.isEmpty(submitDto.getInspectResult())) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检结果不能为空");
                }
            }
        }

        // 开启照片，需要判断是否提交照片
        if (BooleanUtil.isTrue(projectDto.getEnablePhoto()) && CollUtil.isEmpty(submitDto.getInspectImage())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检照片不能为空");
        }

        int projectStatus;
        // 判断是否开启报警
        if (BooleanUtil.isTrue(projectDto.getEnableAlarm())) {
            // 表单数据
            EntSntProjectData projectData = null;
            if (projectDto.getConfigData() != null) {
                projectData = projectDto.getConfigData().toJavaObject(EntSntProjectData.class);
            }
            projectStatus = taskProjectService.calcInspectResult(submitDto, projectDto.getConfigType(), projectData);

        } else {
            // 没有开启报警，直接设置正常
            projectStatus = EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_NORMAL;
        }
        // 更新巡检项目
        taskProjectService.update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTaskProject.class)
                .set(AsEquipmentSiteInspectTaskProject::getExecutors, LoginUserThreadLocal.getCurrentUserId())
                .set(AsEquipmentSiteInspectTaskProject::getStatus, projectStatus)
                .set(AsEquipmentSiteInspectTaskProject::getSubmitTime, LocalDateTime.now())
                .set(AsEquipmentSiteInspectTaskProject::getInspectImage, JSONArray.toJSON(submitDto.getInspectImage()).toString())
                .set(AsEquipmentSiteInspectTaskProject::getInspectRemark, submitDto.getInspectRemark())
                .set(AsEquipmentSiteInspectTaskProject::getInspectResult, submitDto.getInspectResult())
                .set(AsEquipmentSiteInspectTaskProject::getInspectResultList, JSONArray.toJSON(submitDto.getInspectResultList()).toString())
                .eq(AsEquipmentSiteInspectTaskProject::getId, submitDto.getId()));

        // 更新巡检路线数据
        taskService.executeUpdateTask(projectDto.getTaskId(), projectDto.getStatus(), projectStatus);
        taskRangeService.executeUpdateRange(projectDto.getTaskRangeId(), projectDto.getStatus(), projectStatus);

        // 发送消息
        messageService.sendInnerMessage(MsgSendCmd.entSntSubmitProjectAbnormal(projectDto.getTaskId(), projectDto.getProjectId(), projectDto.getTaskRangeId(), LoginUserThreadLocal.getCompanyId()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveDistributeTasks(SiteInspectDistributeTaskDto taskDto) {
        List<AsEquipmentSiteInspectTask> saveTaskList = new ArrayList<>();
        List<AsEquipmentSiteInspectTaskRange> saveTaskRangeList = new ArrayList<>();
        List<AsEquipmentSiteInspectTaskProject> saveTaskProjectList = new ArrayList<>();
        // 任务
        EntSntPlan entSntPlan = planMapper.selectOne(taskDto.getPlanId(), false);
        int diff = taskDto.getIssuedTaskNum() - entSntPlan.getIssuedTaskNum();

        if (diff > 0) {
            // 更新计划下发次数
            planMapper.update(null, Wrappers.lambdaUpdate(AsEquipmentSiteInspectPlan.class)
                    .set(AsEquipmentSiteInspectPlan::getIssuedTaskNum, taskDto.getIssuedTaskNum())
                    .eq(AsEquipmentSiteInspectPlan::getId, taskDto.getPlanId()));
            LocalDateTime now = LocalDateTime.now();
            for (int i = 1; i <= diff; i++) {
                AsEquipmentSiteInspectTask task = new AsEquipmentSiteInspectTask();
                task.setId(IdUtils.getId())
                        .setCompanyId(taskDto.getCompanyId())
                        .setTaskStatus(DictConstant.EQUIPMENT_SITE_INSPECT_TASK_STATUS_WAITING)
                        .setTaskNo(taskDto.getPlanNo() + "-" + (entSntPlan.getIssuedTaskNum() + i))
                        .setTaskName(taskDto.getPlanName() + "-" + (entSntPlan.getIssuedTaskNum() + i))
                        .setTaskType(taskDto.getTaskType())
                        .setPlanId(taskDto.getPlanId())
                        .setRouteType(taskDto.getRouteType())
                        .setManagers(taskDto.getManagers())
                        .setExecutors(taskDto.getExecutors())
                        .setInspectNum(taskDto.getRangeList().size())
                        .setCheckedNum(0)
                        .setAbnormalNum(0)
                        .setPlanFinishTime(taskDto.getPlanFinishTime().minusDays(diff - i))
                        .setCreateBy(taskDto.getCreateBy())
                        .setCreateTime(now.minusDays(diff - i))
                        .setUpdateBy(taskDto.getCreateBy())
                        .setUpdateTime(now.minusDays(diff - i));
                saveTaskList.add(task);
                // 路线
                for (int j = 0; j < taskDto.getRangeList().size(); j++) {
                    SiteInspectDistributeTaskDto.RangeDto rangeDto = taskDto.getRangeList().get(j);
                    AsEquipmentSiteInspectTaskRange range = new AsEquipmentSiteInspectTaskRange();
                    range.setId(IdUtils.getId())
                            .setCompanyId(taskDto.getCompanyId())
                            .setTaskId(task.getId())
                            .setRangeStatus(1)
                            .setRangeId(rangeDto.getRangeId())
                            .setInspectNum(rangeDto.getProjectIds().size())
                            .setCheckedNum(0)
                            .setNormalNum(0)
                            .setHandleStatus(1) // 未处理
                            .setSort(rangeDto.getSort());
                    if (task.getRouteType().equals(EquipmentConstant.SNT_PLAN_ROUTE_TYPE_IS_UNORDERED)) {
                        range.setCanSiteInspect(true);
                    } else {
                        if (range.getSort() == 1) {
                            range.setCanSiteInspect(true);
                        } else {
                            range.setCanSiteInspect(false);
                        }
                    }
                    saveTaskRangeList.add(range);

                    // 项目
                    for (Long projectId : rangeDto.getProjectIds()) {
                        AsEquipmentSiteInspectTaskProject taskProject = new AsEquipmentSiteInspectTaskProject();
                        taskProject.setCompanyId(taskDto.getCompanyId())
                                .setStatus(EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_UNCHECK)
                                .setTaskId(task.getId())
                                .setTaskRangeId(range.getId())
                                .setProjectId(projectId);
                        saveTaskProjectList.add(taskProject);
                    }
                }
            }

            taskService.saveBatch(saveTaskList);
            taskRangeService.saveBatch(saveTaskRangeList);
            taskProjectService.saveBatch(saveTaskProjectList);
        }
        return saveTaskList.stream().map(AsEquipmentSiteInspectTask::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableOperateResult handleIgnore(Long rangeId) {
        AsEquipmentSiteInspectTaskRange taskRange = checkRangeOpt(rangeId);
        // 更新处理状态
        taskRange.setHandleStatus(3);
        taskRangeService.update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTaskRange.class)
                .set(AsEquipmentSiteInspectTaskRange::getHandleStatus, 3)
                .eq(AsEquipmentSiteInspectTaskRange::getHandleStatus, 1)
                .eq(AsEquipmentSiteInspectTaskRange::getId, rangeId));

        // 记录操作日志
        AsEquipmentSiteInspectTaskHandleRecord handleRecord = new AsEquipmentSiteInspectTaskHandleRecord();
        handleRecord.setTaskId(taskRange.getTaskId());
        handleRecord.setTaskRangeId(taskRange.getId());
        handleRecord.setHandleResult("已忽略");
        handleRecord.setRelevance(null);
        handleRecord.setHandleUser(LoginUserThreadLocal.getCurrentUserId());
        handleRecord.setHandleTime(LocalDateTime.now());
        handleRecordService.save(handleRecord);

        AsEquipmentSiteInspectTask task = taskService.getById(taskRange.getTaskId());
        return new AuditableOperateResult(task.getTaskNo(), task.getTaskName());
    }

    private AsEquipmentSiteInspectTaskRange checkRangeOpt(Long rangeId) {
        AsEquipmentSiteInspectTaskRange taskRange = taskRangeService.getById(rangeId);
        BusinessExceptionUtil.checkNotNull(taskRange, "巡检设备/点位不存在");

        if (taskRange.getHandleStatus() > 1) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检设备/点位已处理");
        }

        AsEquipmentSiteInspectTask task = limitOperator(taskRange.getTaskId());
        // 判断任务状态
        if (EquipmentSiteInspectTaskService.CAN_OPT_STATUS.contains(task.getTaskStatus())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检任务未完成，不允许操作");
        }

        if (taskRange.getAbnormalNum() == 0) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "无异常项目，无需处理");
        }
        return taskRange;
    }

    @Override
    public void handleCheck(Long rangeId) {
        checkRangeOpt(rangeId);
        AssetDto asset = taskRangeMapper.handleCheck(rangeId);
        if (asset == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检设备不存在");
        }
        if (asset.getId() != 0L) {
            if (!ListUtil.of(AssetConstant.ASSET_STATUS_IDLE, AssetConstant.ASSET_STATUS_USING, AssetConstant.ASSET_STATUS_BORROW)
                    .contains(asset.getStatus())) {
                String assetStatus = cacheResourceUtil.getAssetStatusName(Convert.toLong(asset.getStatus()));
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前设备处于" + assetStatus + "状态，无法操作");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableOperateResult assetHandle(SiteInspectTaskHandleOrderDto handleOrderDto) {
        AsEquipmentSiteInspectTaskRange taskRange = checkRangeOpt(handleOrderDto.getRangeId());
        // 更新处理状态
        taskRange.setHandleStatus(2);
        taskRangeService.update(Wrappers.lambdaUpdate(AsEquipmentSiteInspectTaskRange.class)
                .set(AsEquipmentSiteInspectTaskRange::getHandleStatus, 2)
                .eq(AsEquipmentSiteInspectTaskRange::getHandleStatus, 1)
                .eq(AsEquipmentSiteInspectTaskRange::getId, taskRange.getId()));

        // 记录操作日志
        AsEquipmentSiteInspectTaskHandleRecord handleRecord = new AsEquipmentSiteInspectTaskHandleRecord();
        handleRecord.setTaskId(taskRange.getTaskId());
        handleRecord.setTaskRangeId(taskRange.getId());
        if (handleOrderDto.getOrderDto().getOrderType().equals(AssetConstant.ORDER_TYPE_DISPOSE)) {
            handleRecord.setHandleResult("设备报废");
        } else {
            handleRecord.setHandleResult("设备报修");
        }
        handleRecord.setRelevance(handleOrderDto.getOrderDto().getOrderNo());
        handleRecord.setHandleUser(LoginUserThreadLocal.getCurrentUserId());
        handleRecord.setHandleTime(LocalDateTime.now());
        handleRecordService.save(handleRecord);

        AsEquipmentSiteInspectTask task = taskService.getById(taskRange.getTaskId());
        return new AuditableOperateResult(task.getTaskNo(), task.getTaskName());
    }

    @Override
    public SiteInspectTaskRangeDto rangeScan(Long taskId, Long pointId) {
        List<AsEquipmentSiteInspectRange> inspectRange = rangeMapper.selectList(Wrappers.lambdaQuery(AsEquipmentSiteInspectRange.class)
                .eq(AsEquipmentSiteInspectRange::getDataId, pointId));
        if (CollUtil.isEmpty(inspectRange)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检点位不存在");
        }

        AsEquipmentSiteInspectTaskRange taskRange = taskRangeService.getOne(Wrappers.lambdaQuery(AsEquipmentSiteInspectTaskRange.class)
                .select(AsEquipmentSiteInspectTaskRange::getId, AsEquipmentSiteInspectTaskRange::getTaskId)
                .eq(AsEquipmentSiteInspectTaskRange::getTaskId, taskId)
                .in(AsEquipmentSiteInspectTaskRange::getRangeId, inspectRange.stream().map(AsEquipmentSiteInspectRange::getId).collect(Collectors.toList())), false);
        if (taskRange == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "巡检任务点位不存在");
        }

        limitOperator(taskRange.getTaskId());

        SiteInspectTaskRangeDto siteInspectTaskRangeDto = taskRangeService.taskRangeInfo(taskRange.getId());
        if (BooleanUtil.isFalse(siteInspectTaskRangeDto.getCanSiteInspect())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请按顺序巡检");
        }
        return siteInspectTaskRangeDto;
    }

    private AsEquipmentSiteInspectTask limitOperator(Long taskId) {
        AsEquipmentSiteInspectTask task = taskService.getById(taskId);
        BusinessExceptionUtil.checkNotNull(task, "巡检任务不存在");

        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        if (BooleanUtil.isTrue(cusUser.getIsAdmin())) {
            return task;
        }
        Long empId = cusUser.getId();
        if (!task.getExecutors().contains(empId) && !task.getManagers().contains(empId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "非巡检管理员或巡检人不可操作");
        }
        return task;
    }

}
