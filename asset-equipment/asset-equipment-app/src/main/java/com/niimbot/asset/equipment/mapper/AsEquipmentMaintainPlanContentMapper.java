package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.model.AsEquipmentMaintainPlanContent;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AsEquipmentMaintainPlanContentMapper extends BaseMapper<AsEquipmentMaintainPlanContent> {

    /**
     * 查询计划下的保养内容列表
     *
     * @param planId 计划ID
     * @return List<AsEquipmentMaintainPlanContent>
     */
    default List<AsEquipmentMaintainPlanContent> selectByPlanId(Long planId) {
        return this.selectList(
                Wrappers.lambdaQuery(AsEquipmentMaintainPlanContent.class)
                        .in(AsEquipmentMaintainPlanContent::getPlanId, planId)
        );
    }

}
