package com.niimbot.asset.equipment.message;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskMapper;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectTaskProjectMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTask;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskProject;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.dto.cmd.FilterMsgUserCmd;
import com.niimbot.asset.message.handler.InstantCompanyMessageHandler;
import com.niimbot.asset.message.handler.PeriodCompanyMessageHandler;
import com.niimbot.equipment.SiteInspectTaskPageDto;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapBuilder;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
public interface EntSntTaskMessageHandler {

    /**
     * 解析接收人
     *
     * @param rule      消息规则
     * @param managers  managers
     * @param executors executors
     * @return ids
     */
    default Set<Long> resolveReceivers(MessageRuleCO rule, List<Long> managers, List<Long> executors) {
        Set<Long> userIds = new HashSet<>(32);
        if (rule.includeReceiverType(MessageConstant.ReceiverType.ENT_SNT_TASK_EXECUTOR) && CollUtil.isNotEmpty(executors)) {
            userIds.addAll(executors);
        }
        if (rule.includeReceiverType(MessageConstant.ReceiverType.ENT_SNT_TASK_MANAGER) && CollUtil.isNotEmpty(managers)) {
            userIds.addAll(managers);
        }
        if (rule.includeCuzReceiverType()) {
            userIds.addAll(rule.resolveCuzReceiver());
        }
        return userIds;
    }

    /**
     * 构建消息扩展参数MAP
     *
     * @param id 任务ID
     * @return map
     */
    default Map<String, Object> extMap(Long id) {
        return MapBuilder.create(new HashMap<String, Object>(2))
                .put(TASK_ID, String.valueOf(id))
                .build();
    }

    String UNIT = "unit";
    String TASK_ID = "taskId";

    @Component
    @RequiredArgsConstructor
    class TaskOvertime extends PeriodCompanyMessageHandler implements EntSntTaskMessageHandler {

        private final AsEquipmentSiteInspectTaskMapper taskMapper;

        @Override
        public String period() {
            return HOUR;
        }

        @Override
        protected boolean skip(MessageRuleCO rule) {
            return CollUtil.isEmpty(rule.getReceiverType()) || CollUtil.isEmpty(rule.getReminderTime())
                    || CollUtil.isEmpty(rule.getExtConfig()) || !rule.getExtConfig().containsKey(UNIT)
                    || rule.getReminderTime().size() != 1;
        }

        @Override
        protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
            String unit = rule.getExtConfig().getString(UNIT);
            Integer interval = rule.getReminderTime().get(0);
            if (!DAY.equals(unit) && !HOUR.equals(unit)) {
                return;
            }
            List<SiteInspectTaskPageDto> tasks = taskMapper.selectForWaitTaskMsg(rule.getCompanyId(), unit, interval);
            if (CollUtil.isEmpty(tasks)) {
                return;
            }
            FilterMsgUserCmd filterMsgUserCmd = new FilterMsgUserCmd();
            filterMsgUserCmd.setCode(this.code());
            tasks.forEach(v -> {
                Set<Long> userIds = resolveReceivers(rule, v.getManagers(), v.getExecutors());
                // 过滤已发送过消息的人
                filterMsgUserCmd.setUserIds(userIds);
                filterMsgUserCmd.setBizId(String.valueOf(v.getId()));
                userIds = messageService.filterMsgUser(filterMsgUserCmd);
                if (CollUtil.isEmpty(userIds)) {
                    return;
                }
                Map<String, String> mapParams = MapBuilder.create(new HashMap<String, String>(2))
                        .put(MessageConstant.Template.TASK_NAME, v.getTaskName())
                        .put(MessageConstant.Template.URL, pcDomain + "/#/inspection-tasks/detail?id=" + v.getId())
                        .build();
                Body body = Body.builder()
                        .userIds(userIds)
                        .mapParam(mapParams)
                        .appExtMapParam(Convert.toMap(String.class, String.class, extMap(v.getId())))
                        .commonExtMap(extMap(v.getId()))
                        .build();
                bodies.add(body);
            });
        }

        @Override
        public String code() {
            return MessageConstant.Code.SBXJRWJJCS.getCode();
        }
    }

    @Component
    @RequiredArgsConstructor
    class TaskCreate extends InstantCompanyMessageHandler implements EntSntTaskMessageHandler {

        private final AsEquipmentSiteInspectTaskMapper taskMapper;

        @Override
        protected boolean skip(MessageRuleCO rule) {
            return CollUtil.isEmpty(rule.getReceiverType());
        }

        @Override
        protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
            List<AsEquipmentSiteInspectTask> tasks = taskMapper.selectBatchIds(params.getIds());
            if (CollUtil.isEmpty(tasks)) {
                return;
            }
            tasks.forEach(task -> {
                Set<Long> userIds = resolveReceivers(rule, task.getManagers(), task.getExecutors());
                Map<String, String> mapParams = MapBuilder.create(new HashMap<String, String>(1))
                        .put(MessageConstant.Template.URL, pcDomain + "/#/inspection-tasks/detail?id=" + task.getId())
                        .build();
                Body body = Body.builder()
                        .mapParam(mapParams)
                        .commonExtMap(extMap(task.getId()))
                        .appExtMapParam(Convert.toMap(String.class, String.class, extMap(task.getId())))
                        .userIds(userIds)
                        .build();
                bodies.add(body);
            });
        }

        @Override
        public String code() {
            return MessageConstant.Code.SBXJRWTX.getCode();
        }
    }

    @Component
    @RequiredArgsConstructor
    class TaskAbnormal extends InstantCompanyMessageHandler implements EntSntTaskMessageHandler {

        private final AsEquipmentSiteInspectTaskMapper taskMapper;

        private final AsEquipmentSiteInspectTaskProjectMapper taskProjectMapper;

        @Override
        protected boolean skip(MessageRuleCO rule) {
            return CollUtil.isEmpty(rule.getReceiverType())
                    || CollUtil.isEmpty(rule.getReminderTime()) || rule.getReminderTime().size() != 1;
        }

        @Override
        protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
            // 等待事务提交
            try {
                TimeUnit.SECONDS.sleep(3L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            Long taskId = params.getIds().get(0);
            // 比对发送时机配置
            String type = params.getType();
            Integer reminderTime = rule.getReminderTime().get(0);
            if (!String.valueOf(reminderTime).equals(type)) {
                return;
            }
            AsEquipmentSiteInspectTask task = taskMapper.selectById(taskId);
            if (Objects.isNull(task)) {
                return;
            }
            Integer abnormalNum = 0;
            // 滞后提醒：巡检任务完成后 当前任务异常数大于0时可发送消息
            if (String.valueOf(DictConstant.SYS_ENABLE).equals(type)) {
                abnormalNum = task.getAbnormalNum();
            }
            // 实时提醒：巡检项目提交后 当前任务下项目为异常状态时可发送消息
            if (String.valueOf(DictConstant.SYS_DISABLE).equals(type)) {
                Long projectId = params.getIds().get(1);
                Long taskRangeId = params.getIds().get(2);
                AsEquipmentSiteInspectTaskProject taskProject = taskProjectMapper.selectOne(
                        Wrappers.lambdaQuery(AsEquipmentSiteInspectTaskProject.class)
                                .eq(AsEquipmentSiteInspectTaskProject::getCompanyId, rule.getCompanyId())
                                .eq(AsEquipmentSiteInspectTaskProject::getTaskId, taskId)
                                .eq(AsEquipmentSiteInspectTaskProject::getProjectId, projectId)
                                .eq(AsEquipmentSiteInspectTaskProject::getTaskRangeId, taskRangeId)
                );
                if (Objects.nonNull(taskProject) && taskProject.getStatus() == EquipmentConstant.SITE_INSPECT_TASK_PROJECT_STATUS_ABNORMAL) {
                    abnormalNum = task.getAbnormalNum();
                }
            }
            if (abnormalNum == 0) {
                return;
            }
            Set<Long> userIds = resolveReceivers(rule, task.getManagers(), task.getExecutors());
            Map<String, String> mapParams = MapBuilder.create(new HashMap<String, String>(3))
                    .put(MessageConstant.Template.TASK_NAME, task.getTaskName())
                    .put(MessageConstant.Template.AMOUNT, String.valueOf(abnormalNum))
                    .put(MessageConstant.Template.URL, pcDomain + "/#/inspection-tasks/detail?id=" + task.getId())
                    .build();
            Body body = Body.builder()
                    .mapParam(mapParams)
                    .commonExtMap(extMap(task.getId()))
                    .appExtMapParam(Convert.toMap(String.class, String.class, extMap(task.getId())))
                    .userIds(userIds)
                    .build();
            bodies.add(body);
        }

        @Override
        public String code() {
            return MessageConstant.Code.SBXJRWYCTX.getCode();
        }
    }

}
