package com.niimbot.asset.equipment.abs.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectPointMapper;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.means.abs.PointAbs;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.system.PrintPointPageQueryDto;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/abs/equipment/pointAbs/")
public class PointAbsImpl implements PointAbs {

    private final AsEquipmentSiteInspectPointMapper pointMapper;

    @Override
    public PageUtils<AsEquipmentSiteInspectPointDto> printPage(PrintPointPageQueryDto query) {
        IPage<AsEquipmentSiteInspectPointDto> page = pointMapper.printPage(query.buildIPage(), query);
        return new PageUtils<>(page);
    }

}
