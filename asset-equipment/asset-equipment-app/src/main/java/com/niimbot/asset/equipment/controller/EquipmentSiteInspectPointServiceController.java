package com.niimbot.asset.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectPoint;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectPointService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.SerialNumberUtils;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.equipment.AuditableRemovePointResult;
import com.niimbot.equipment.EquipmentSiteInspectPointImportDto;
import com.niimbot.equipment.EquipmentSiteInspectPointQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("server/equipment/site/inspect/point")
@RequiredArgsConstructor
public class EquipmentSiteInspectPointServiceController {

    @Autowired
    private EquipmentSiteInspectPointService equipmentSiteInspectPointService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private AsAssetImportErrorService assetImportErrorService;

    /**
     * 分页查询巡检点位列表
     *
     * @param queryDto 查询参数
     * @return pageUtils对象
     */
    @PostMapping(value = "/page")
    public IPage<AsEquipmentSiteInspectPointDto> page(@RequestBody EquipmentSiteInspectPointQueryDto queryDto) {
        return equipmentSiteInspectPointService.pagePoint(queryDto);
    }

    @PostMapping(value = "/list")
    public List<AsEquipmentSiteInspectPointDto> list(@RequestBody EquipmentSiteInspectPointQueryDto queryDto) {
        return equipmentSiteInspectPointService.listPoint(queryDto);
    }

    @PostMapping(value = "/ids")
    public List<AsEquipmentSiteInspectPointDto> listIds(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<AsEquipmentSiteInspectPointDto> collect = equipmentSiteInspectPointService.listByIds(ids).stream().map(v -> BeanUtil.copyProperties(v, AsEquipmentSiteInspectPointDto.class)).collect(Collectors.toList());
        for (AsEquipmentSiteInspectPointDto pointDto : collect) {
            AsArea byId = areaService.getById(pointDto.getPid());
            pointDto.setAreaCode(Objects.isNull(byId)?"":byId.getAreaCode());
        }
        return collect;
    }

    /**
     * 添加巡检点位
     *
     * @param point
     * @return true/false
     */
    @PostMapping
    public Long pointAdd(@RequestBody AsEquipmentSiteInspectPoint point) {
        return equipmentSiteInspectPointService.add(point);
    }

    /**
     * 修改巡检点位
     *
     * @param point
     * @return true/false
     */
    @PutMapping
    public boolean edit(@RequestBody AsEquipmentSiteInspectPoint point) {
        return equipmentSiteInspectPointService.edit(point);
    }

    /**
     * 删除巡检点位
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/{id}")
    public AuditableRemovePointResult remove(@PathVariable(value = "id") Long id) {
        if (ObjectUtil.isEmpty(id)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        return equipmentSiteInspectPointService.remove(id);
    }

    /**
     * 获取推荐巡检点位编码
     *
     * @return 编码
     */
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return SerialNumberUtils.getMaxCode(equipmentSiteInspectPointService);
    }

    /**
     * 错误记录
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return equipmentSiteInspectPointService.importError(taskId);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return assetImportErrorService.deleteByTaskId(taskId);
    }

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        equipmentSiteInspectPointService.saveSheetHead(importErrorDto);
    }

    /**
     * 导入数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody EquipmentSiteInspectPointImportDto importDto) {
        return equipmentSiteInspectPointService.saveSheetData(importDto);
    }

    /**
     * 查询所有巡检点位
     */
    @GetMapping(value = "/list/all")
    public List<AsEquipmentSiteInspectPointDto> listAll() {
        return equipmentSiteInspectPointService.listAll();
    }

    /**
     * 巡检点位详情
     *
     * @param
     * @return
     */
    @GetMapping(value = "/detail")
    public AsEquipmentSiteInspectPointDto pointDetail(@RequestParam("pointId") Long pointId) {
        if (ObjectUtil.isEmpty(pointId)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        return equipmentSiteInspectPointService.pointDetail(pointId);
    }

}
