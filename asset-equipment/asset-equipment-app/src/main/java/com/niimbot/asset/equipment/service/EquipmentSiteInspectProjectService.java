package com.niimbot.asset.equipment.service;

import com.niimbot.asset.framework.service.CommonCodeService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.CreateEntSntProject;
import com.niimbot.equipment.EntSntProject;
import com.niimbot.equipment.EntSntProjectImportDto;
import com.niimbot.equipment.SearchEntSntProject;
import com.niimbot.luckysheet.LuckyMultiSheetModel;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EquipmentSiteInspectProjectService extends CommonCodeService {

    /**
     * 创建设备巡检项目
     *
     * @param create body
     * @return result
     */
    AuditableOperateResult createProject(CreateEntSntProject create);

    /**
     * 删除设备巡检项目
     *
     * @param projectIds 巡检项目ID集合
     * @return result
     */
    List<AuditableOperateResult> removeProject(List<Long> projectIds);

    /**
     * 分页搜索设备巡检项目
     *
     * @param search 条件
     * @return result
     */
    PageUtils<EntSntProject> searchProject(SearchEntSntProject search);

    void saveSheetHead(HeadImportErrorDto importErrorDto);

    Boolean saveSheetData(EntSntProjectImportDto importDto);

    List<LuckyMultiSheetModel> importError(Long taskId);
}
