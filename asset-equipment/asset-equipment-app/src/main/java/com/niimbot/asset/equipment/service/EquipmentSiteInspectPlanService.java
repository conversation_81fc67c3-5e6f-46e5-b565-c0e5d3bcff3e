package com.niimbot.asset.equipment.service;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.*;
import com.niimbot.system.AuditableOperateResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface EquipmentSiteInspectPlanService {

    /**
     * 创建设备巡检计划
     *
     * @param create body
     * @return result
     */
    AuditableOperateResult createPlan(CreateEntSntPlan create);

    /**
     * 编辑设备巡检计划
     *
     * @param edit body
     * @return result
     */
    AuditableOperateResult editPlan(EditEntSntPlan edit);

    /**
     * 批量删除设备巡检计划
     *
     * @param remove 计划ID集合
     * @return result
     */
    List<AuditableOperateResult> removePlan(RemoveEntSntPlan remove);

    /**
     * 批量停用设备巡检计划
     *
     * @param planIds 计划ID集合
     * @return result
     */
    List<AuditableOperateResult> stopPlan(List<Long> planIds);

    /**
     * 变更设备巡检计划管理员
     *
     * @param edit body
     * @return result
     */
    AuditableOperateResult editPlanManagers(EditEntSntPlanManagers edit);

    /**
     * 变更设备巡检计划执行人
     *
     * @param edit body
     * @return result
     */
    AuditableOperateResult editPlanExecutors(EditEntSntPlanExecutors edit);

    /**
     * 分页搜索设备巡检计划
     *
     * @param search body
     * @return result
     */
    PageUtils<EntSntPlan> searchPlan(SearchEntSntPlan search);

    /**
     * 计划详情
     *
     * @param planId 计划ID
     * @return result
     */
    EntSntPlanDetails detailPlan(Long planId);

    /**
     * 计划中的范围搜索
     *
     * @param search body
     * @return result
     */
    PageUtils<EntSntRange> rangeSearch(SearchEntSntRange search);

    /**
     * 选件范围列表
     *
     * @param search body
     * @return result
     */
    List<EntSntRange> rangeList(ListEntSntRange search);

    /**
     * 需要分发任务的计划
     */
    List<SiteInspectDistributeTaskDto> distributeTaskPlanList(LocalDateTime now);
}
