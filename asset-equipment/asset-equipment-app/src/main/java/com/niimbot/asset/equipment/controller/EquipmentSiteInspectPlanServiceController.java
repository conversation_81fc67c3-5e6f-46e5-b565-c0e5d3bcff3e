package com.niimbot.asset.equipment.controller;

import com.niimbot.asset.equipment.service.EquipmentSiteInspectPlanService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.*;
import com.niimbot.system.AuditableOperateResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/equipment/site/plan")
public class EquipmentSiteInspectPlanServiceController {

    private final EquipmentSiteInspectPlanService planService;

    @PostMapping("/create")
    public AuditableOperateResult createPlan(@RequestBody CreateEntSntPlan create) {
        return planService.createPlan(create);
    }

    @PostMapping("/edit")
    public AuditableOperateResult editPlan(@RequestBody EditEntSntPlan edit) {
        return planService.editPlan(edit);
    }

    @PostMapping("/remove")
    public List<AuditableOperateResult> removePlan(@RequestBody RemoveEntSntPlan remove) {
        return planService.removePlan(remove);
    }

    @PostMapping("/stop")
    public List<AuditableOperateResult> stopPlan(@RequestBody List<Long> planIds) {
        return planService.stopPlan(planIds);
    }

    @PostMapping("/edit/managers")
    public AuditableOperateResult editPlanManagers(@RequestBody EditEntSntPlanManagers edit) {
        return planService.editPlanManagers(edit);
    }

    @PostMapping("/edit/executors")
    public AuditableOperateResult editPlanExecutors(@RequestBody EditEntSntPlanExecutors edit) {
        return planService.editPlanExecutors(edit);
    }

    @PostMapping("/search")
    public PageUtils<EntSntPlan> searchPlan(@RequestBody SearchEntSntPlan search) {
        return planService.searchPlan(search);
    }

    @GetMapping("/detail")
    public EntSntPlanDetails detailPlan(@RequestParam("planId") Long planId) {
        return planService.detailPlan(planId);
    }

    @PostMapping("/range/search")
    public PageUtils<EntSntRange> rangeSearch(@RequestBody SearchEntSntRange search) {
        return planService.rangeSearch(search);
    }

    @PostMapping("/range/list")
    public List<EntSntRange> rangeList(@RequestBody ListEntSntRange search) {
        return planService.rangeList(search);
    }
}

