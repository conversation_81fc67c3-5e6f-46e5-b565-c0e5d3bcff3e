package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskRange;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.equipment.SiteInspectTaskRangeQryDto;
import com.niimbot.equipment.SiteInspectTaskRangeStatisticsDto;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.AssetDto;

import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:07
 */
@EnableDataPerm
public interface AsEquipmentSiteInspectTaskRangeMapper extends BaseMapper<AsEquipmentSiteInspectTaskRange> {

    IPage<SiteInspectTaskRangeDto> taskRangePage(Page<T> buildIPage, @Param("ew") SiteInspectTaskRangeQryDto query);

    SiteInspectTaskRangeDto taskRangeInfo(@Param("rangeId") Long rangeId);

    List<SiteInspectTaskRangeDto> rangeBaseInfos(@Param("rangeIds") List<Long> rangeIds);

    SiteInspectTaskRangeStatisticsDto taskRangeStatistics(@Param("taskId") Long taskId);

    AssetDto handleCheck(@Param("rangeId") Long rangeId);
}
