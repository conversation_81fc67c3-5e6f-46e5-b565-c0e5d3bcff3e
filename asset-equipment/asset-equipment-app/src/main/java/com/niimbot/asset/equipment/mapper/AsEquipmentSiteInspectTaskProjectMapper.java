package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskProject;
import com.niimbot.equipment.SiteInspectTaskAbnormalDto;
import com.niimbot.equipment.SiteInspectTaskAbnormalQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectDto;
import com.niimbot.equipment.SiteInspectTaskProjectQryDto;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/11/14 16:17
 */
@EnableDataPerm
public interface AsEquipmentSiteInspectTaskProjectMapper extends BaseMapper<AsEquipmentSiteInspectTaskProject> {

    IPage<SiteInspectTaskProjectDto> projectPage(Page<Object> buildIPage,
                                                 @Param("ew") SiteInspectTaskProjectQryDto query);

    SiteInspectTaskProjectDto info(@Param("id") Long id);

    IPage<SiteInspectTaskAbnormalDto> projectAbnormalPage(Page<Object> buildIPage,
                                                          @Param("ew") SiteInspectTaskAbnormalQryDto query);
}
