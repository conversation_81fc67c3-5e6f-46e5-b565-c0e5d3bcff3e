package com.niimbot.asset.equipment.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.framework.dataperm.annonation.UserFilterColumn;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/11 15:03
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_maintain_task", autoResultMap = true)
public class AsEquipmentMaintainTask {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公司ID
     */
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    /**
     * 保养任务编码
     */
    private String taskNo;

    /**
     * 任务状态（1-待保养，2-保养中，3-已逾期，4-保养完成，5-已作废）
     */
    private Integer taskStatus;

    /**
     * 计划应开始时间
     */
    private LocalDateTime planStartTime;

    /**
     * 计划时长
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer maintainDuration;

    /**
     * 保养计划id
     */
    private Long planId;

    /**
     * 保养计划编号
     */
    private String planNo;

    /**
     * 保养计划名称
     */
    private String planName;

    /**
     * 保养负责人
     */
    @UserFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_EQUIPMENT, subBizCode = AssetConstant.AUTHORITY_MAINTAIN_USER)
    private Long maintainUser;

    /**
     * 资产编码ID
     */
    private Long assetId;

    /**
     * 资产快照
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject assetSnapshotData;

    /**
     * 保养任务信息
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject orderData;

    /**
     * 关联出库单据
     */
    private String relationCkOrder;

    private BigDecimal totalPrice;

    private BigDecimal totalNum;

    /**
     * 软删除标记
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
