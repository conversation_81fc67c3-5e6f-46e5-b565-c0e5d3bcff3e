package com.niimbot.asset.equipment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.mapper.AsEquipmentSiteInspectProjectMapper;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectProject;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectProjectService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.equipment.*;
import com.niimbot.luckysheet.LuckyMultiSheetModel;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EquipmentSiteInspectProjectServiceImpl implements EquipmentSiteInspectProjectService {

    private final AsEquipmentSiteInspectProjectMapper projectMapper;
    private final AsAssetImportErrorService assetImportErrorService;
    private final RedisService redisService;

    AsEquipmentSiteInspectProject toProject(EntSntProject obj) {
        AsEquipmentSiteInspectProject project = new AsEquipmentSiteInspectProject();
        project.setProjectName(obj.getProjectName());
        project.setProjectCode(obj.getProjectCode());
        project.setCompanyId(LoginUserThreadLocal.getCompanyId());
        project.setConfigType(obj.getConfigType());
        project.setConfigData(obj.getConfigData());
        project.setContent(obj.getContent());
        project.setEnablePhoto(obj.getEnablePhoto());
        project.setEnableAlarm(obj.getEnableAlarm());
        return project;
    }

    @Override
    public AuditableOperateResult createProject(CreateEntSntProject create) {
        Affirm.isTrue(Objects.isNull(projectMapper.selectByProjectCode(create.getProjectCode())), "项目编码重复");
        Affirm.isTrue(Objects.isNull(projectMapper.selectByProjectName(create.getProjectName())), "项目名称重复");
        AsEquipmentSiteInspectProject project = toProject(create);
        projectMapper.insert(project);
        return new AuditableOperateResult(project.getProjectCode(), project.getProjectName());
    }

    @Override
    public List<AuditableOperateResult> removeProject(List<Long> projectIds) {
        List<AsEquipmentSiteInspectProject> projects = projectMapper.selectBatchIds(projectIds);
        Affirm.notEmpty(projects, "项目不存在");
        projectIds = projects.stream().map(AsEquipmentSiteInspectProject::getId).collect(Collectors.toList());
        Integer countInPlan = projectMapper.selectCountInPlan(LoginUserThreadLocal.getCompanyId(), projectIds);
        Affirm.isTrue(countInPlan == 0, "有关联计划的项目，不允许删除");
        projectMapper.deleteBatchIds(projectIds);
        return projects.stream().map(v -> new AuditableOperateResult(v.getProjectCode(), v.getProjectName())).collect(Collectors.toList());
    }

    @Override
    public PageUtils<EntSntProject> searchProject(SearchEntSntProject search) {
        IPage<EntSntProject> searchResult = projectMapper.selectPageSearch(search.buildIPage(), search);
        return new PageUtils<>(searchResult);
    }

    @Override
    public String getMaxCode() {
        return projectMapper.selectMaxCode(LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public String getMaxCode(String code) {
        return projectMapper.selectMaxCodeByCode(LoginUserThreadLocal.getCompanyId(), code);
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importErrorDto.getTaskId());
        importError.setErrorNum(0);
        importError.setImportType(DictConstant.IMPORT_TYPE_INSPECT_PROJECT);
        importError.setExcelJson(importErrorDto.getHeadModelList());
        importError.setSheetName(importErrorDto.getSheetName());
        importError.setSheetIndex(importErrorDto.getSheetIndex());
        this.assetImportErrorService.save(importError);
    }

    @Override
    public Boolean saveSheetData(EntSntProjectImportDto importDto) {
        AsEquipmentSiteInspectProject project = new AsEquipmentSiteInspectProject();
        project.setProjectName(importDto.getProjectName());
        project.setProjectCode(importDto.getProjectCode());
        project.setCompanyId(LoginUserThreadLocal.getCompanyId());
        project.setConfigType(importDto.getConfigType());
        project.setContent(importDto.getContent());
        project.setEnablePhoto(importDto.getEnablePhoto());
        project.setEnableAlarm(importDto.getEnableAlarm());

        if (StrUtil.isNotEmpty(importDto.getProjectName())) {
            // 名称不能重复
            if (projectMapper.selectCount(new QueryWrapper<AsEquipmentSiteInspectProject>().lambda()
                    .eq(AsEquipmentSiteInspectProject::getProjectName, importDto.getProjectName())) > 0) {
                LuckySheetModel codee = importDto.getSheetModelList().get(0);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("项目名称已存在");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
        }
        if (StrUtil.isNotEmpty(importDto.getProjectCode())) {
            // 编码不能重复
            if (projectMapper.selectCount(new QueryWrapper<AsEquipmentSiteInspectProject>().lambda()
                    .eq(AsEquipmentSiteInspectProject::getProjectCode, importDto.getProjectCode())) > 0) {
                LuckySheetModel codee = importDto.getSheetModelList().get(1);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("项目编码已存在");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
        }
        if (importDto.getErrorNum() == 0) {
            // 写入配置
            EntSntProjectData sntProjectData = BeanUtil.copyProperties(importDto, EntSntProjectData.class);
            project.setConfigData((JSONObject) JSON.toJSON(sntProjectData));
            projectMapper.insert(project);
            redisService.hIncr(RedisConstant.companyImportKey("inspectProject", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("inspectProject", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            return true;
        } else {
            AsAssetImportError importError = copyToAsProjectImportError(importDto);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("inspectProject", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }

    @Override
    public List<LuckyMultiSheetModel> importError(Long taskId) {
        List<LuckyMultiSheetModel> result = new ArrayList<>();
        List<AsAssetImportError> list = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, taskId)
                .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_INSPECT_PROJECT)
                .orderByAsc(AsAssetImportError::getId));
        Map<String, List<List<LuckySheetModel>>> collect = list.stream().collect(
                Collectors.groupingBy(f -> f.getSheetIndex() + "_" + f.getSheetName(),
                        Collectors.mapping(AsAssetImportError::getExcelJson, toList())));
        // sheet_index排序
        List<String> sortList = collect.keySet().stream()
                .sorted(Comparator.comparingInt(f -> Convert.toInt(f.split("_")[0]))).collect(toList());
        for (String sheet : sortList) {
            String[] split = sheet.split("_");
            LuckyMultiSheetModel luckyMultiSheetModel = new LuckyMultiSheetModel(split[1]);
            luckyMultiSheetModel.setCelldata(collect.get(sheet));
            result.add(luckyMultiSheetModel);
        }
        return result;
    }

    private AsAssetImportError copyToAsProjectImportError(EntSntProjectImportDto importDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_INSPECT_PROJECT);
        importError.setSheetIndex(importDto.getSheetIndex());
        importError.setSheetName(importDto.getSheetName());
        List<LuckySheetModel> sheetModelList = importDto.getSheetModelList();
        importError.setExcelJson(new ArrayList<>(sheetModelList));
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }
}
