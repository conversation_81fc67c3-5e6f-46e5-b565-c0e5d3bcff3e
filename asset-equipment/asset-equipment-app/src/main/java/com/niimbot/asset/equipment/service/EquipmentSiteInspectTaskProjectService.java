package com.niimbot.asset.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskProject;
import com.niimbot.equipment.EntSntProjectData;
import com.niimbot.equipment.SiteInspectTaskAbnormalDto;
import com.niimbot.equipment.SiteInspectTaskAbnormalQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectDto;
import com.niimbot.equipment.SiteInspectTaskProjectQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectSubmitDto;

/**
 * <AUTHOR>
 * @date 2023/11/14 16:18
 */
public interface EquipmentSiteInspectTaskProjectService extends IService<AsEquipmentSiteInspectTaskProject> {

    IPage<SiteInspectTaskProjectDto> projectPage(SiteInspectTaskProjectQryDto query);

    SiteInspectTaskProjectDto info(Long projectId);

    IPage<SiteInspectTaskAbnormalDto> projectAbnormalPage(SiteInspectTaskAbnormalQryDto query);

    // 计算巡检结果
    int calcInspectResult(SiteInspectTaskProjectSubmitDto submitDto, Integer configType, EntSntProjectData configData);

}
