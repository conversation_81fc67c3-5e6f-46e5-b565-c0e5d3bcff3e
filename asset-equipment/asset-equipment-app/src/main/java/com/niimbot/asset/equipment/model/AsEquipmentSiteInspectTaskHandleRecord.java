package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/18 11:35
 */
@Data
@TableName(value = "as_equipment_site_inspect_task_handle_record", autoResultMap = true)
public class AsEquipmentSiteInspectTaskHandleRecord {

    @ApiModelProperty("主键ID")
    private Long id;

    // 公司ID（租户ID）
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty("巡检任务ID")
    private Long taskId;

    @ApiModelProperty("巡检路线ID")
    private Long taskRangeId;

    @ApiModelProperty("处理结果")
    private String handleResult;

    @ApiModelProperty("关联数据")
    private String relevance;

    @ApiModelProperty("处理人")
    private Long handleUser;

    @ApiModelProperty("处理时间")
    private LocalDateTime handleTime;

}
