package com.niimbot.asset.equipment.schedule;

import com.niimbot.asset.equipment.service.EquipmentSiteInspectPlanService;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskAggregateService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.equipment.SiteInspectDistributeTaskDto;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 巡检任务下发定时器
 *
 * <AUTHOR>
 * @date 2023/11/16 19:10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EquipmentSiteInspectTaskScheduler {

    private final EquipmentSiteInspectPlanService planService;
    private final EquipmentSiteInspectTaskAggregateService taskAggregateService;

    private final RedisDistributeLock redisDistributeLock;

    private final MessageService messageService;

    @Scheduled(cron = "0 0/15 * * * ?")
    public void distributeTasks() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }

        // 当前时间
        LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
//        LocalDateTime now = LocalDateTime.of(2023, 11, 28, 0, 0, 0, 0);

        List<SiteInspectDistributeTaskDto> distributeTaskDtoList = planService.distributeTaskPlanList(now);
        if (CollUtil.isEmpty(distributeTaskDtoList)) {
            return;
        }
        log.info("siteInspect distribute task num {}", distributeTaskDtoList.size());
        redisDistributeLock.lock("SITE_INSPECT_DISTRIBUTE_TASK", 15L, TimeUnit.MINUTES, v -> {
            distributeTaskDtoList.parallelStream().forEach(taskDto -> {
                try {
                    List<Long> taskIds = taskAggregateService.saveDistributeTasks(taskDto);
                    taskDto.setTaskIds(taskIds);
                } catch (Exception e) {
                    log.error("siteInspect distribute task error, planId = {}, error = {}",
                            taskDto != null ? taskDto.getPlanId() : null, e.getMessage(), e);
                }
            });
            Map<Long, List<Long>> collect = new HashMap<>();
            for (SiteInspectDistributeTaskDto d : distributeTaskDtoList) {
                if (CollUtil.isNotEmpty(d.getTaskIds())) {
                    List<Long> taskIds = collect.getOrDefault(d.getCompanyId(), new ArrayList<>());
                    taskIds.addAll(d.getTaskIds());
                    collect.put(d.getCompanyId(), taskIds);
                }
            }
            collect.forEach((companyId, taskIds) -> messageService.sendInnerMessage(MsgSendCmd.entSntTaskCreate(taskIds, companyId)));
        });
    }

}
