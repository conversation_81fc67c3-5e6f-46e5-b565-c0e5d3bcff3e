package com.niimbot.asset.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskAggregateService;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskHandleRecordService;
import com.niimbot.asset.equipment.service.EquipmentSiteInspectTaskRangeService;
import com.niimbot.equipment.SiteInspectTaskHandleOrderDto;
import com.niimbot.equipment.SiteInspectTaskHandleRecordDto;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.equipment.SiteInspectTaskRangeQryDto;
import com.niimbot.equipment.SiteInspectTaskRangeStatisticsDto;
import com.niimbot.system.AuditableOperateResult;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/13 13:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/equipment/siteInspect/task/range")
public class EquipmentSiteInspectTaskRangeServiceController {

    private final EquipmentSiteInspectTaskRangeService siteInspectTaskRangeService;
    private final EquipmentSiteInspectTaskHandleRecordService handleRecordService;
    private final EquipmentSiteInspectTaskAggregateService taskAggregateService;

    @GetMapping("/page")
    public IPage<SiteInspectTaskRangeDto> taskRangeList(SiteInspectTaskRangeQryDto query) {
        return siteInspectTaskRangeService.taskRangeList(query);
    }

    @GetMapping("/info")
    public SiteInspectTaskRangeDto taskRangeInfo(@RequestParam("rangeId") Long rangeId) {
        return siteInspectTaskRangeService.taskRangeInfo(rangeId);
    }

    @GetMapping("/scan")
    public SiteInspectTaskRangeDto rangeScan(@RequestParam("taskId") Long taskId, @RequestParam("pointId") Long pointId) {
        return taskAggregateService.rangeScan(taskId, pointId);
    }

    @GetMapping("/statistics")
    public SiteInspectTaskRangeStatisticsDto taskRangeStatistics(@RequestParam("taskId") Long taskId) {
        return siteInspectTaskRangeService.taskRangeStatistics(taskId);
    }

    @GetMapping("/handleRecord")
    public SiteInspectTaskHandleRecordDto handleRecordInfo(@RequestParam("rangeId") Long rangeId) {
        return handleRecordService.handleRecordInfo(rangeId);
    }

    @GetMapping("/handle/ignore")
    public AuditableOperateResult handleIgnore(@RequestParam("rangeId") Long rangeId) {
        return taskAggregateService.handleIgnore(rangeId);
    }

    @GetMapping("/handle/check")
    public void handleCheck(@RequestParam("rangeId") Long rangeId) {
        taskAggregateService.handleCheck(rangeId);
    }

    @PostMapping("/handle/assetHandle")
    public AuditableOperateResult assetHandle(@RequestBody SiteInspectTaskHandleOrderDto handleOrderDto) {
        return taskAggregateService.assetHandle(handleOrderDto);
    }

}
