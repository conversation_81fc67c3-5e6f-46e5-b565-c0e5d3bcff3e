package com.niimbot.asset.equipment.service.impl;

import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.equipment.service.EquipmentOrderService;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR>
 * @date 2023/10/11 15:54
 */
@Service
public class EquipmentOrderServiceImpl implements EquipmentOrderService {

    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;

    @Resource
    private AsFormService formService;

    @Override
    public FormVO getForm(Integer orderType, Long companyId) {
        return formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType(), companyId);
    }

    @Override
    public QueryConditionSortDto sortField(Integer orderType) {
        QueryConditionSortDto querySort = new QueryConditionSortDto();
        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.EQUIPMENT_MAINTAIN_ORDER_TYPE_HEAD.get(orderType));
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK == orderType) {
            QueryFieldConstant.Field planStartTime = QueryFieldConstant.EQUIPMENT_TASK_EXT_FIELD.get(QueryFieldConstant.FIELD_MAINTAIN_TASK_PLAN_START_TIME);
            if (ObjectUtil.isNotNull(planStartTime)) {
                querySort.getSortList().add(
                        new QueryConditionSortDto.Field(planStartTime.getName(), planStartTime.getCode(), planStartTime.getType()));
            }
            FormVO assetFormVO = formService.assetTpl();
            assetFormVO.getFormFields().stream()
                    .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                    .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));
        }

        QueryFieldConstant.Field createTimeField = QueryFieldConstant.EQUIPMENT_TASK_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }

        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

}
