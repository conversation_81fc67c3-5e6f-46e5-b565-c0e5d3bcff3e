package com.niimbot.asset.equipment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 设备保养计划内容
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_equipment_maintain_plan_content", autoResultMap = true)
public class AsEquipmentMaintainPlanContent {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 设备保养计划ID
     */
    private Long planId;

    /**
     * 设备保养项目
     */
    private String project;

    /**
     * 设备保养要求
     */
    private String requirement;

}
