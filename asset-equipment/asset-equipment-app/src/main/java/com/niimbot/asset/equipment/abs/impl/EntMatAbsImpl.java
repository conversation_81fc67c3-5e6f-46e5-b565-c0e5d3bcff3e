package com.niimbot.asset.equipment.abs.impl;

import com.niimbot.asset.equipment.service.EquipmentMaintainPlanService;
import com.niimbot.asset.equipment.service.EquipmentMaintainTaskService;
import com.niimbot.asset.system.abs.EntMatAbs;
import com.niimbot.asset.system.dto.EntMatTransferCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/client/abs/equipment/EntMatAbs/")
public class EntMatAbsImpl implements EntMatAbs {

    @Resource
    private EquipmentMaintainPlanService planService;

    @Resource
    private EquipmentMaintainTaskService taskService;

    @Override
    public Boolean entMatUserHasTask(Long userId) {
        return taskService.userHasTask(userId);
    }

    @Override
    public Boolean entMatTransfer(EntMatTransferCmd cmd) {
        return planService.transformPlan(cmd.getRemoveEmployeeId(), cmd.getEntMatTask());
    }
}
