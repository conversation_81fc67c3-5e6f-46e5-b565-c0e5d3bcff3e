package com.niimbot.asset.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectTaskRange;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.equipment.SiteInspectTaskRangeQryDto;
import com.niimbot.equipment.SiteInspectTaskRangeStatisticsDto;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:07
 */
public interface EquipmentSiteInspectTaskRangeService extends IService<AsEquipmentSiteInspectTaskRange> {

    IPage<SiteInspectTaskRangeDto> taskRangeList(SiteInspectTaskRangeQryDto query);

    SiteInspectTaskRangeDto taskRangeInfo(Long rangeId);

    SiteInspectTaskRangeStatisticsDto taskRangeStatistics(Long taskId);

    boolean executeUpdateRange(Long taskRangeId, int projectBeforeStatus, int projectStatus);

}
