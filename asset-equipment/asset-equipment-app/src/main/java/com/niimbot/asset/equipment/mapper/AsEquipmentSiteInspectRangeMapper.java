package com.niimbot.asset.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.equipment.model.AsEquipmentSiteInspectRange;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.equipment.EntSntRange;
import com.niimbot.equipment.EntSntRangeLocation;
import com.niimbot.equipment.SearchEntSntRange;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface AsEquipmentSiteInspectRangeMapper extends BaseMapper<AsEquipmentSiteInspectRange> {

    /**
     * 根据计划ID物理删除
     *
     * @param planId 计划ID
     */
    void deleteByPlanId(@Param("planId") Long planId);

    /**
     * 计划内范围分页查询
     *
     * @param page   分页
     * @param search 查询
     * @return result
     */
    IPage<EntSntRange> selectPageSearch(Page<?> page, @Param("em") SearchEntSntRange search);

    /**
     * 获取范围设备位置信息
     *
     * @param dataId 设备ID
     * @return EntSntRangeLocation
     */
    EntSntRangeLocation selectEntLocation(@Param("dataId") Long dataId);

    /**
     * 获取范围点位位置信息
     *
     * @param dataId 设备ID
     * @return EntSntRangeLocation
     */
    EntSntRangeLocation selectPointLocation(@Param("dataId") Long dataId);

    /**
     * 查询范围位置信息
     *
     * @param dataId   rangeId
     * @param dataType dataType
     * @return EntSntRangeLocation
     */
    default EntSntRangeLocation selectRangeLocation(Long dataId, Integer dataType) {
        if (EquipmentConstant.SNT_RANGE_TYPE_IS_ENT == dataType) {
            return selectEntLocation(dataId);
        }
        if (EquipmentConstant.SNT_RANGE_TYPE_IS_POINT == dataType) {
            return selectPointLocation(dataId);
        }
        throw new BusinessException(SystemResultCode.EXPORT_ERROR, "不支持的范围类型");
    }

}
