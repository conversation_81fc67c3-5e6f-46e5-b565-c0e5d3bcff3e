package com.niimbot.asset.maintenance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.maintenance.model.AsRepairReportOrder;
import com.niimbot.asset.maintenance.model.AsRepairReportOrderDetail;
import com.niimbot.maintenance.RepairReportOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairReportOrderDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.web.bind.annotation.PathVariable;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 资产报修单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
public interface AsRepairReportOrderService extends IService<AsRepairReportOrder> {
    /**
     * 创建报修单
     * @param dto
     * @param enableWorkflow
     * @return
     */
    Boolean create(RepairReportOrderDto dto, Boolean enableWorkflow);

    /**
     * 单据详情
     * @param id
     * @return
     */
    RepairReportOrderDto getOrderDetail(@PathVariable Long id);

    /**
     * 单据明细详情查询
     * @param orderId
     * @param assetId
     * @return
     */
    AsRepairReportOrderDetail getDetail(Long orderId, Long assetId);

    /**
     * 查询明细列表
     * @param id
     * @return
     */
    List<AsRepairReportOrderDetail> getDetailsById(Long id);

    /**
     * 报修单分页查询
     * @param query
     * @return
     */
    IPage<AsRepairReportOrder> page(AsOrderQueryDto query);

    /**
     * 单据明细分页查询
     * @param query
     * @return
     */
    IPage<AsRepairReportOrderDetail> pageDetail(RepairReportOrderDetailPageQueryDto query);

    /**
     * 列表查询--导出使用
     * @param query
     * @return
     */
    List<AsOrderDto> listForExport(AsOrderQueryDto query);

    QueryConditionSortDto sortField();

    List<AsOrderAssetDto> getAssetsByOrderId(Collection<Long> ids);

    AsRepairReportOrder tempGetById(Long orderId);

    AsOrderInfoDto getApproveOrderByAssetId(Long assetId);
}
