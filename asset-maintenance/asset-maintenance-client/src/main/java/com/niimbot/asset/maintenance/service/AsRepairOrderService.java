package com.niimbot.asset.maintenance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.maintenance.model.AsRepairOrder;
import com.niimbot.asset.maintenance.model.AsRepairOrderDetail;
import com.niimbot.maintenance.RepairOrderDetailDto;
import com.niimbot.maintenance.RepairOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairOrderDto;
import com.niimbot.maintenance.RepairOrderFinishDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.web.bind.annotation.PathVariable;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 资产维修单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
public interface AsRepairOrderService extends IService<AsRepairOrder> {
    /**
     * 创建维修
     * @param dto
     * @param enableWorkflow
     * @return
     */
    Boolean create(RepairOrderDto dto, Boolean enableWorkflow);

    /**
     * 完成资产维修
     * @param dto
     * @return
     */
    Boolean repairFinish(RepairOrderFinishDto dto);

    /**
     * 完成资产维修
     * @param dto
     * @return
     */
    void directRepairFinish(RepairOrderDto dto);

    /**
     * 单据详情
     * @param id
     * @return
     */
    RepairOrderDto getOrderDetail(@PathVariable Long id);


    /**
     * 单据明细详情查询
     * @param orderId
     * @param assetId
     * @return
     */
    AsRepairOrderDetail getDetail(Long orderId, Long assetId);

    /**
     * 查询明细列表
     * @param id
     * @return
     */
    List<AsRepairOrderDetail> getDetailsById(Long id);

    /**
     * 维修单分页查询
     * @param query
     * @return
     */
    IPage<AsRepairOrder> page(AsOrderQueryDto query);

    /**
     * 单据明细分页查询
     * @param query
     * @return
     */
    IPage<AsRepairOrderDetail> pageDetail(RepairOrderDetailPageQueryDto query);

    /**
     * 资产单据-用于导出
     *
     * @param dto dto
     * @return 结果
     */
    List<AsOrderDto> listForExport(AsOrderQueryDto dto);

    QueryConditionSortDto sortField();

    List<AsOrderAssetDto> getAssetsByOrderId(Collection<Long> ids);

    AsRepairOrder tempGetById(Long orderId);

    /**
     * 根据资产编码，批量查询维修完成资产信息
     * @param assetIdList
     * @return
     */
    List<RepairOrderDetailDto> repairFinishAsset(List<Long> assetIdList);

    AsOrderInfoDto getApproveOrderByAssetId(Long assetId);
}
