package com.niimbot.asset.maintenance.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.means.model.AbstractAssetSnapshot;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 资产报修单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="AsRepairReportingOrderDetail对象", description="资产报修单明细")
@TableName(value = "as_repair_report_order_detail", autoResultMap = true)
public class AsRepairReportOrderDetail extends AbstractAssetSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "报修单据ID")
    private Long repairReportOrderId;
}
