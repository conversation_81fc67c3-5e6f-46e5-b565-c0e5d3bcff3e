package com.niimbot.asset.maintenance.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.means.model.AbstractAssetSnapshot;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 资产维修单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="AsRepairOrderDetail对象", description="资产维修单明细")
@TableName(value = "as_repair_order_detail", autoResultMap = true)
public class AsRepairOrderDetail extends AbstractAssetSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "维修单据ID")
    private Long repairOrderId;

    @ApiModelProperty(value = "维修花费")
    private BigDecimal repairMoney;

    @ApiModelProperty(value = "维修状态")
    private String repairStatus;

    @ApiModelProperty("维修完成日期")
    private LocalDateTime finishTime;
}
