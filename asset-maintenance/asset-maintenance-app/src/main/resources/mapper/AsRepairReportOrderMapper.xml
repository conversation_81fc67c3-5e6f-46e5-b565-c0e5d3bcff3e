<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.maintenance.mapper.AsRepairReportOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.maintenance.model.AsRepairReportOrder">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="summary" property="summary"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="repair_report_org_text" property="repairReportOrgText"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="orderPage" resultMap="BaseResultMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.create_by,
        a.create_time,
        (select GROUP_CONCAT(org_name SEPARATOR '，') from as_cus_employee b
        left join as_user_org uo on b.id = uo.user_id
        left join as_org o on uo.org_id = o.id
        where b.id = a.send_repair_user
        <if test="query.repairReportOrg != null and query.repairReportOrg.size() > 0">
            and o.id in
            <foreach collection="query.repairReportOrg" item="orgId" index="index" open="("
                     close=")"
                     separator=",">
                #{orgId}
            </foreach>
        </if>
        ) as repair_report_org_text
        from as_repair_report_order a
        <if test="query.kw != null and query.kw != ''">
            join as_repair_report_order_detail b on b.repair_report_order_id = a.id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
    </select>


    <sql id="queryCondition">
        <if test="query.approveStatus != null">
            and a.approve_status = #{query.approveStatus}
        </if>
        <if test="query.kw != null and query.kw != ''">
            and (
            a.order_no like concat('%', #{query.kw}, '%')
            or a.order_data ->> '$.remark' like concat('%', #{query.kw}, '%')
            or b.asset_name like concat('%', #{query.kw}, '%')
            or b.asset_code like concat('%', #{query.kw}, '%')
            )
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <!-- 勾选的单据id -->
        <if test="query.ids!=null and query.ids.size() > 0">
            and a.id in
            <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <resultMap id="OrderDtoMap" type="com.niimbot.means.AsOrderDto">
        <id column="id" property="id"/>
        <id column="order_type" property="orderType"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="approve_status_text" property="approveStatusText"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_text" property="createByText"/>
        <result column="create_time" property="createTime"/>
        <collection property="assets"
                    ofType="com.niimbot.means.AsOrderAssetDto"
                    column="{id=id}"
                    select="selectAssetSnapshot"
                    javaType="java.util.ArrayList">
        </collection>
    </resultMap>

    <resultMap id="AsOrderAssetDto" type="com.niimbot.means.AsOrderAssetDto">
        <id column="asset_id" property="id" />
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="selectAssetSnapshot" resultMap="AsOrderAssetDto">
        select id as asset_id, asset_snapshot_data from as_repair_report_order_detail where repair_report_order_id = #{id}
    </select>

    <select id="listForExport" resultMap="OrderDtoMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        6 as order_type,
        a.approve_status,
        ( SELECT dd.label FROM as_dict_data dd WHERE dd.value = a.approve_status
        and dd.dict_type = 'approve_status' and dd.status = 1 ) AS approve_status_text,
        a.order_no,
        a.order_data,
        a.create_by,
        ( SELECT CASE IFNULL(emp.emp_no, '')
        WHEN '' THEN
        emp.emp_name
        ELSE
        CONCAT(emp.emp_name,"（", emp.emp_no ,"）")
        END as create_by_text
        FROM as_cus_employee emp WHERE emp.id = a.create_by) AS create_by_text,
        a.create_time
        from as_repair_report_order a
        <if test="query.kw != null and query.kw != ''">
            join as_repair_report_order_detail b on b.repair_report_order_id = a.id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="orderBySql != null and orderBySql != ''">
            ${orderBySql}
        </if>
    </select>

    <select id="getOrderByAssetId" resultType="com.niimbot.means.AsOrderInfoDto">
        SELECT
            a.id,
            6 AS order_type,
            a.order_no
        FROM
            as_repair_report_order a
            JOIN as_repair_report_order_detail AS b ON a.id = b.repair_report_order_id
        WHERE
            a.approve_status = 1
          AND b.id = #{assetId}
        limit 1
    </select>

    <select id="getOrderByAssetIdNoPerm" resultType="com.niimbot.means.AsOrderInfoDto">
        SELECT
            a.id,
            6 AS order_type,
            a.order_no
        FROM
            as_repair_report_order a
            JOIN as_repair_report_order_detail AS b ON a.id = b.repair_report_order_id
        WHERE
            a.approve_status = 1
          AND a.company_id = #{companyId}
          AND b.id = #{assetId}
        limit 1
    </select>
</mapper>
