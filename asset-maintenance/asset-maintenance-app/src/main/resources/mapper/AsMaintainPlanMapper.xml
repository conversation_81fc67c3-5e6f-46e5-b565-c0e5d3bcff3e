<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.maintenance.mapper.AsMaintainPlanMapper">

    <resultMap id="MaintainPlanListDto" type="com.niimbot.maintenance.MaintainPlanListDto">
        <id column="id" property="id"/>
        <result column="asset_id" property="assetId"/>
        <result column="asset_status" property="assetStatus"/>
        <result column="maintain_status" property="maintainStatus"/>
        <result column="asset_photo" property="assetPhoto"
                typeHandler="com.niimbot.handle.ListTypeHandler"/>
        <result column="asset_name" property="assetName"/>
        <result column="asset_code" property="assetCode"/>
        <result column="brand" property="brand"/>
        <result column="model" property="model"/>
        <result column="principal_user_id" property="principalUserId"
                typeHandler="com.niimbot.handle.ListTypeHandler"/>
        <result column="level" property="level"/>
        <result column="next_time" property="nextTime"/>
        <result column="frequency" property="frequency"/>
        <result column="frequency_unit" property="frequencyUnit"/>
        <result column="supplier" property="supplier"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_details" property="contactDetails"/>
        <result column="status" property="status"/>
        <result column="storage_area" property="storageArea"/>
        <result column="storage_location" property="storageLocation"/>
        <result column="manager_owner" property="managerOwner"/>
        <result column="org_owner" property="orgOwner"/>
        <result column="use_org" property="useOrg"/>
        <result column="use_person" property="usePerson"/>
        <!--<collection property="planContentList"
                    ofType="com.niimbot.maintenance.MaintainPlanContentDto"
                    column="id"
                    select="selectPlanContentList"
                    javaType="java.util.ArrayList">
        </collection>-->
    </resultMap>

    <!--<select id="selectPlanContentList" resultType="com.niimbot.maintenance.MaintainPlanContentDto">
        select id, plan_id, project, requirement from as_maintain_plan_content where plan_id = #{id}
    </select>-->

    <select id="planPage" resultMap="MaintainPlanListDto">
        select
        p.id,
        p.asset_id,
        p.maintain_status,
        ast.status as asset_status,
        IF(ast.asset_data ->> '$.assetPhoto' = 'null', null, ast.asset_data ->> '$.assetPhoto') as asset_photo,
        IF(ast.asset_data ->> '$.assetName' = 'null', null, ast.asset_data ->> '$.assetName') as asset_name,
        IF(ast.asset_data ->> '$.assetCode' = 'null', null, ast.asset_data ->> '$.assetCode') as asset_code,
        IF(ast.asset_data ->> '$.brand' = 'null', null, ast.asset_data ->> '$.brand') as brand,
        IF(ast.asset_data ->> '$.model' = 'null', null, ast.asset_data ->> '$.model') as model,
        p.principal_user_id,
        p.level,
        p.next_time,
        p.frequency,
        p.frequency_unit,
        p.supplier,
        p.contact_person,
        p.contact_details,
        p.status,
        IF(ast.asset_data ->> '$.storageArea' = 'null', null, ast.asset_data ->> '$.storageArea') as storage_area,
        IF(ast.asset_data ->> '$.storageLocation' = 'null', null, ast.asset_data ->> '$.storageLocation') as storage_location,
        IF(ast.asset_data ->> '$.managerOwner' = 'null', null, ast.asset_data ->> '$.managerOwner') as manager_owner,
        IF(ast.asset_data ->> '$.orgOwner' = 'null', null, ast.asset_data ->> '$.orgOwner') as org_owner,
        IF(ast.asset_data ->> '$.useOrg' = 'null', null, ast.asset_data ->> '$.useOrg') as use_org,
        IF(ast.asset_data ->> '$.usePerson' = 'null', null, ast.asset_data ->> '$.usePerson') as use_person
        from as_maintain_plan p join as_asset ast on p.asset_id = ast.id and p.company_id = ast.company_id
        where
            p.is_delete = 0 and ast.is_delete = 0
        <if test="ew.kw!=null and ew.kw!=''">
            AND (
            asset_code like concat('%',#{ew.kw},'%')
            OR asset_name like concat('%',#{ew.kw},'%')
            )
        </if>
        <if test="ew.status!=null">
            AND p.status = #{ew.status}
        </if>
        <if test="ew.maintainStatus!=null">
            AND p.maintain_status = #{ew.maintainStatus}
        </if>
        <if test="ew.ids != null and ew.ids.size() != 0">
            AND p.id IN
            <foreach collection="ew.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by p.create_time desc
    </select>

    <resultMap id="MaintainPlanDto" type="com.niimbot.maintenance.MaintainPlanInfoDto">
        <id column="id" property="id"/>
        <result column="asset_id" property="assetId"/>
        <result column="principal_user_id" property="principalUserId"
                typeHandler="com.niimbot.handle.ListTypeHandler"/>
        <result column="level" property="level"/>
        <result column="next_time" property="nextTime"/>
        <result column="frequency" property="frequency"/>
        <result column="frequency_unit" property="frequencyUnit"/>
        <result column="supplier" property="supplier"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_details" property="contactDetails"/>
        <result column="status" property="status"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <collection property="planContentList"
                    ofType="com.niimbot.maintenance.MaintainPlanContentDto">
            <result column="content_id" property="id"/>
            <result column="project" property="project"/>
            <result column="requirement" property="requirement"/>
        </collection>
    </resultMap>

    <select id="info" resultMap="MaintainPlanDto">
        SELECT
            p.id,
            p.asset_id,
            p.principal_user_id,
            p.LEVEL,
            p.next_time,
            p.frequency,
            p.frequency_unit,
            p.supplier,
            p.contact_person,
            p.contact_details,
            p.STATUS,
            c.project,
	        c.requirement,
	        c.id as content_id,
	        p.update_by,
	        p.update_time
        FROM
            as_maintain_plan p
            LEFT JOIN as_maintain_plan_content c ON p.id = c.plan_id
        WHERE
            p.is_delete = 0 AND p.id = #{id}
    </select>

    <resultMap id="MaintainPlanMessageDto" type="com.niimbot.maintenance.MaintainPlanMessageDto">
        <id column="asset_id" property="assetId"/>
        <result column="day" property="day"/>
        <result column="use_person" property="usePerson"/>
        <result column="manager_owner" property="managerOwner"/>
        <result column="maintain_person" property="maintainPerson"
                typeHandler="com.niimbot.handle.ListTypeHandler"/>
    </resultMap>


    <select id="countMaintainPlan" resultMap="MaintainPlanMessageDto">
        SELECT t.asset_id,DATEDIFF( NOW(),t.next_time ) AS `day`,t2.use_person,t2.manager_owner,t.principal_user_id AS maintain_person
        FROM as_maintain_plan t LEFT JOIN as_asset t2 ON t.asset_id = t2.id
        WHERE t.company_id = #{companyId}
        AND t.status = 1
        AND t.is_delete = 0
        AND
        <foreach collection="days" item="i" open="( " close=" )" separator="OR">
            <!-- 保养过期：即下次保养时间是当前时间之前 -->
            <if test="type == 'BYGQTX'">
                DATEDIFF(NOW(), t.next_time) = #{i}
            </if>
            <!-- 保养到期：即下次保养时间是当前时间之后 -->
            <if test="type == 'BYDQTX'">
                DATEDIFF(t.next_time, NOW()) = #{i}
            </if>
        </foreach>
    </select>
</mapper>
