<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.maintenance.mapper.AsRepairOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.maintenance.model.AsRepairOrder">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="approve_status" property="approveStatus" />
        <result column="summary" property="summary" />
        <result column="order_no" property="orderNo" />
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="total_repair_money" property="totalRepairMoney" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="page" resultMap="BaseResultMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_repair_money,
        a.create_by,
        a.create_time
        from as_repair_order a
        <if test="query.kw != null and query.kw != ''">
            join as_repair_order_detail b on b.repair_order_id = a.id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
    </select>

    <sql id="queryCondition">
        <if test="query.approveStatus != null">
            and a.approve_status = #{query.approveStatus}
        </if>
        <if test="query.kw != null and query.kw != ''">
            and (
            a.order_no like concat('%', #{query.kw}, '%')
            or a.remark like concat('%', #{query.kw}, '%')
            or b.asset_snapshot_data ->> '$.assetCode' like concat('%', #{query.kw}, '%')
            or b.asset_snapshot_data ->> '$.assetName' like concat('%', #{query.kw}, '%')
            )
        </if>
        <!-- 勾选的单据id -->
        <if test="query.ids!=null and query.ids.size() > 0">
            and a.id in
            <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
    </sql>

    <resultMap id="OrderDtoMap" type="com.niimbot.means.AsOrderDto">
        <id column="id" property="id"/>
        <id column="order_type" property="orderType"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="approve_status_text" property="approveStatusText"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_text" property="createByText"/>
        <result column="create_time" property="createTime"/>
        <collection property="assets"
                    ofType="com.niimbot.means.AsOrderAssetDto"
                    column="{id=id}"
                    select="selectAssetSnapshot"
                    javaType="java.util.ArrayList">
        </collection>
    </resultMap>

    <resultMap id="AsOrderAssetDto" type="com.niimbot.means.AsOrderAssetDto">
        <id column="asset_id" property="id"/>
        <id column="repair_money" property="repairMoney"/>
        <id column="repair_status" property="repairStatus"/>
        <result column="finish_time" property="finishTime"/>
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="selectAssetSnapshot" resultMap="AsOrderAssetDto">
        select id as asset_id, repair_money, repair_status, finish_time,
        asset_snapshot_data from as_repair_order_detail where repair_order_id = #{id}
    </select>

    <select id="listForExport" resultMap="OrderDtoMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        7 as order_type,
        a.approve_status,
        ( SELECT dd.label FROM as_dict_data dd WHERE dd.value = a.approve_status
        and dd.dict_type = 'approve_status' and dd.status = 1 ) AS approve_status_text,
        a.order_no,
        a.order_data,
        a.create_by,
        ( SELECT CASE IFNULL(emp.emp_no, '')
        WHEN '' THEN
        emp.emp_name
        ELSE
        CONCAT(emp.emp_name,"（", emp.emp_no ,"）")
        END as create_by_text
        FROM as_cus_employee emp WHERE emp.id = a.create_by) AS create_by_text,
        a.create_time
        from as_repair_order a
        <if test="query.kw != null and query.kw != ''">
            join as_repair_order_detail b on b.repair_order_id = a.id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="orderBySql != null and orderBySql != ''">
            ${orderBySql}
        </if>
    </select>

    <select id="selectFinishAsset" resultType="com.niimbot.maintenance.RepairOrderDetailDto">
        select d.id, d.repair_order_id, d.repair_money
        from as_repair_order_detail as d join as_repair_order as o on d.repair_order_id = o.id
        <where>
            o.company_id = #{companyId}
            <if test="assetCodeList != null and assetCodeList.size > 0">
                and d.asset_code in
                <foreach collection="assetCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and d.repair_status = '维修完成' and o.approve_status in (0, 3)
        </where>
    </select>

    <select id="getOrderByAssetId" resultType="com.niimbot.means.AsOrderInfoDto">
        SELECT
            a.id,
            7 AS order_type,
            a.order_no
        FROM
            as_repair_order a
            JOIN as_repair_order_detail AS b ON a.id = b.repair_order_id
            JOIN as_asset ast ON ast.company_id = a.company_id AND ast.id = b.id
        WHERE
            ( a.approve_status = 1 OR ast.STATUS = 5 )
         and ast.is_delete = 0
          AND b.id = #{assetId}
        LIMIT 1
    </select>

    <select id="getOrderByAssetIdNoPerm" resultType="com.niimbot.means.AsOrderInfoDto">
        SELECT
            a.id,
            7 AS order_type,
            a.order_no
        FROM
            as_repair_order a
                JOIN as_repair_order_detail AS b ON a.id = b.repair_order_id
                JOIN as_asset ast ON ast.company_id = a.company_id AND ast.id = b.id
        WHERE
            ( a.approve_status = 1 OR ast.STATUS = 5 )
          and ast.is_delete = 0
          AND b.id = #{assetId}
          AND a.company_id = #{companyId}
        LIMIT 1
    </select>
</mapper>
