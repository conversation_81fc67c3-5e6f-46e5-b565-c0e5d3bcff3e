<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.maintenance.mapper.AsMaintainOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.maintenance.model.AsMaintainOrder">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="asset_id" property="assetId"/>
        <result column="summary" property="summary"/>
        <result column="asset_name" property="assetName"/>
        <result column="asset_code" property="assetCode"/>
        <result column="order_no" property="orderNo"/>
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="org_owner" property="orgOwner"/>
    </resultMap>

    <select id="orderPage" resultMap="BaseResultMap">
        SELECT
        id,
        approve_status,
        asset_name as summary,
        asset_name,
        asset_code,
        order_data,
        order_no,
        create_by,
        create_time,
        IF(asset_snapshot_data ->> '$.orgOwner' = 'null', null, asset_snapshot_data ->> '$.orgOwner') as org_owner
        FROM
        as_maintain_order as o
        <where>
            <include refid="queryCondition"/>
        </where>
    </select>

    <resultMap id="MaintainOrderMap" type="com.niimbot.maintenance.MaintainOrderDto">
        <id column="id" property="id"/>
        <result column="asset_id" property="assetId"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="asset_name" property="assetName"/>
        <result column="asset_code" property="assetCode"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_text" property="createByText"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="getInfo" resultMap="MaintainOrderMap">
      SELECT
           	id,
            asset_id,
            approve_status,
            asset_snapshot_data,
            asset_name,
            asset_code,
            order_no,
            order_data,
            create_by,
            ( SELECT emp.emp_name FROM as_cus_employee emp WHERE o.create_by = emp.id ) AS create_by_text,
            create_time
        FROM
            as_maintain_order o where o.id = #{id}
    </select>

    <sql id="queryCondition">
        <if test="query.kw!=null and query.kw!=''">
            AND (
            asset_code like concat('%',#{query.kw},'%')
            OR asset_name like concat('%',#{query.kw},'%')
            OR order_no like concat('%',#{query.kw},'%')
            )
        </if>
        <!-- 勾选的单据id -->
        <if test="query.ids!=null and query.ids.size() > 0">
            and id in
            <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
    </sql>

    <resultMap id="OrderDtoMap" type="com.niimbot.means.AsOrderDto">
        <id column="id" property="id"/>
        <id column="order_type" property="orderType"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_text" property="createByText"/>
        <result column="create_time" property="createTime"/>
        <collection property="assets" ofType="com.niimbot.means.AsOrderAssetDto">
            <id column="asset_id" property="id"/>
            <result column="asset_snapshot_data" property="assetSnapshotData"
                    typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        </collection>
    </resultMap>

    <select id="listForExport" resultMap="OrderDtoMap">
        SELECT
        id,
        11 as order_type,
        approve_status,
        order_no,
        order_data,
        create_by,
        ( SELECT CASE IFNULL(emp.emp_no, '')
        WHEN '' THEN
        emp.emp_name
        ELSE
        CONCAT(emp.emp_name,"（", emp.emp_no ,"）")
        END as create_by_text
        FROM as_cus_employee emp WHERE emp.id = o.create_by) AS create_by_text,
        create_time,
        asset_name,
        asset_code,
        asset_snapshot_data,
        asset_id
        FROM
        as_maintain_order AS o
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="orderBySql != null and orderBySql != ''">
            ${orderBySql}
        </if>
    </select>
</mapper>
