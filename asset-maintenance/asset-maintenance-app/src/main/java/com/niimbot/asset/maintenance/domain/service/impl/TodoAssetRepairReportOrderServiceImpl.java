package com.niimbot.asset.maintenance.domain.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.maintenance.model.AsRepairReportOrder;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.TodoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/6 上午10:23
 */
@Slf4j
@Service
public class TodoAssetRepairReportOrderServiceImpl implements TodoOrderService {

    @Autowired
    private AsRepairReportOrderService repairReportOrderService;

    //当前service支持处理的单据类型
    private static final List<Integer> supportTypeList = ListUtil.of(AssetConstant.ORDER_TYPE_REPAIR_REPORT);

    @Override
    public Boolean supportOrderType(Integer orderType) {
        return supportTypeList.contains(orderType);
    }

    @Override
    public JSONObject getOrderJson(Long businessId) {
        AsRepairReportOrder reportOrder = repairReportOrderService.getById(businessId);

        if (Objects.isNull(reportOrder)) {
            return null;
        }

        JSONObject result = JsonUtil.toJsonObject(reportOrder, o -> {
            JSONObject orderData = o.getJSONObject("orderData");
            o.fluentPutAll(orderData);
            o.fluentRemove("orderData");
        });
        return result;
    }

    @Override
    public AsTodo getOrderTodo(AsTodo todo) {
        AsRepairReportOrder reportOrder = repairReportOrderService.getById(todo.getBusinessId());

        if (Objects.isNull(reportOrder)) {
            return null;
        }

        //如果orderData为空，优先设置下
        if (Objects.isNull(reportOrder.getOrderData())) {
            reportOrder.setOrderData(new JSONObject());
        }

        reportOrder.getOrderData().put("summary", reportOrder.getSummary());
        AsTodo result = new AsTodo()
                .setSummary(reportOrder.getSummary())
                .setCreateBy(reportOrder.getCreateBy())
                .setOrderData(JSONObject.toJSONString(reportOrder.getOrderData()));
        return result;
    }
}
