package com.niimbot.asset.maintenance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.maintenance.model.AsMaintainOrder;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.maintenance.MaintainOrderDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 资产保养单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@EnableDataPerm(excludeMethodName = {"tempGetById"})
public interface AsMaintainOrderMapper extends BaseMapper<AsMaintainOrder> {

    IPage<AsMaintainOrder> orderPage(@Param("page") Page<Object> buildIPage,
                                     @Param("query") AsOrderQueryDto queryDto,
                                     @Param("conditions") String conditions);

    MaintainOrderDto getInfo(Long id);

    List<AsOrderDto> listForExport(@Param("query") AsOrderQueryDto query,
                                   @Param("conditions") String conditions,
                                   @Param("orderBySql") String orderBySql);

    @Select("select * from as_maintain_order where id = #{id}")
    AsMaintainOrder tempGetById(Long id);
}
