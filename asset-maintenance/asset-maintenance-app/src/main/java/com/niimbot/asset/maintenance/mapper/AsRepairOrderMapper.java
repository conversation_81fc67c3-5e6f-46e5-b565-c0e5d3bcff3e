package com.niimbot.asset.maintenance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.maintenance.model.AsRepairOrder;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.maintenance.RepairOrderDetailDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 资产维修单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@EnableDataPerm(excludeMethodName = {"getOrderByAssetIdNoPerm", "tempGetById", "selectFinishAsset", "selectAssetSnapshot"})
public interface AsRepairOrderMapper extends BaseMapper<AsRepairOrder> {
    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @param permsSql
     * @return
     */
    IPage<AsRepairOrder> page(IPage<AsRepairOrder> page,
                                    @Param("query") AsOrderQueryDto query,
                              @Param("conditions") String conditions);

    /**
     * 列表查询--导出使用
     * @param query
     * @return
     */
    List<AsOrderDto> listForExport(@Param("query") AsOrderQueryDto query,
                                   @Param("conditions") String conditions,
                                   @Param("orderBySql") String orderBySql);

    @Select("select * from as_repair_order where id = #{id}")
    AsRepairOrder tempGetById(Long id);

    List<RepairOrderDetailDto> selectFinishAsset(@Param("assetCodeList") List<String> assetCodeList,
                                                 @Param("companyId") Long companyId);

    AsOrderInfoDto getOrderByAssetId(@Param("assetId") Long assetId);

    AsOrderInfoDto getOrderByAssetIdNoPerm(@Param("assetId") Long assetId,
                                           @Param("companyId") Long companyId);
}
