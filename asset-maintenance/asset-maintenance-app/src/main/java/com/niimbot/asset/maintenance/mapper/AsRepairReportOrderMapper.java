package com.niimbot.asset.maintenance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.maintenance.model.AsRepairReportOrder;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 资产报修单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@EnableDataPerm(excludeMethodName = {"getOrderByAssetIdNoPerm", "tempGetById", "selectAssetSnapshot"})
public interface AsRepairReportOrderMapper extends BaseMapper<AsRepairReportOrder> {
    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @param companyId
     * @return
     */
    IPage<AsRepairReportOrder> orderPage(IPage<AsRepairReportOrder> page,
                                         @Param("query") AsOrderQueryDto query,
                                         @Param("conditions") String conditions);

    /**
     * 列表查询--导出使用
     * @param query
     * @return
     */
    List<AsOrderDto> listForExport(@Param("query") AsOrderQueryDto query,
                                   @Param("conditions") String conditions,
                                   @Param("orderBySql") String orderBySql);

    @Select("select * from as_repair_report_order where id = #{id}")
    AsRepairReportOrder tempGetById(Long id);

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @return 结果
     */
    AsOrderInfoDto getOrderByAssetId(@Param("assetId") Long assetId);

    AsOrderInfoDto getOrderByAssetIdNoPerm(@Param("assetId") Long assetId,
                                           @Param("companyId") Long companyId);
}
