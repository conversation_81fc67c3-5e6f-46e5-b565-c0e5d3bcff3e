package com.niimbot.asset.maintenance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.maintenance.model.AsRepairReportOrderDetail;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 资产报修单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsRepairReportOrderDetailMapper.class)
public interface AsRepairReportOrderDetailMapper extends BaseMapper<AsRepairReportOrderDetail> {

}
