package com.niimbot.asset.maintenance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 资产保养计划内容
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsMaintainPlanContent对象", description = "资产保养计划内容")
public class AsMaintainPlanContent implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "保养计划ID")
    private Long planId;

    @ApiModelProperty(value = "保养项目")
    private String project;

    @ApiModelProperty(value = "保养要求")
    private String requirement;


}
