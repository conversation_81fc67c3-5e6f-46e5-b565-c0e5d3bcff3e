package com.niimbot.asset.maintenance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.maintenance.mapper.AsRepairOrderDetailMapper;
import com.niimbot.asset.maintenance.model.AsRepairOrderDetail;
import com.niimbot.asset.maintenance.service.AsRepairOrderDetailService;
import com.niimbot.means.AsOrderAssetDto;

import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产维修单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Service
public class AsRepairOrderDetailServiceImpl extends ServiceImpl<AsRepairOrderDetailMapper, AsRepairOrderDetail> implements AsRepairOrderDetailService {

    @Override
    public List<AsOrderAssetDto> getDetailsByOrderId(Collection<Long> ids) {
        List<AsRepairOrderDetail> list = list(new QueryWrapper<AsRepairOrderDetail>()
                .lambda().in(AsRepairOrderDetail::getRepairOrderId, ids));
        return list.parallelStream().map(orderDetail -> {
            AsOrderAssetDto asOrderAssetDto = new AsOrderAssetDto().setId(orderDetail.getAssetSnapshotData().getLong("id"))
                    .setOrderId(orderDetail.getRepairOrderId())
                    .setRepairMoney(orderDetail.getRepairMoney())
                    .setRepairStatus(orderDetail.getRepairStatus())
                    .setFinishTime(orderDetail.getFinishTime());
            if (Objects.nonNull(orderDetail.getFinishTime())) {
                asOrderAssetDto.setFinishTimeToLong(orderDetail.getFinishTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            }
            asOrderAssetDto.setAssetSnapshotData(orderDetail.getAssetSnapshotData());
            return asOrderAssetDto;
        }).collect(Collectors.toList());
    }
}
