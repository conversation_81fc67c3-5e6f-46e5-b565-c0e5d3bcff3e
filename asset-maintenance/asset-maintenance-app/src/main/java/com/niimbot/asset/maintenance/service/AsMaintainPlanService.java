package com.niimbot.asset.maintenance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.maintenance.model.AsMaintainPlan;
import com.niimbot.maintenance.MaintainPlanDto;
import com.niimbot.maintenance.MaintainPlanEditBatchDto;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.maintenance.MaintainPlanListDto;
import com.niimbot.maintenance.MaintainPlanMessageDto;
import com.niimbot.maintenance.MaintainPlanQueryDto;

import java.util.List;

/**
 * <p>
 * 资产保养计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
public interface AsMaintainPlanService extends IService<AsMaintainPlan> {

    IPage<MaintainPlanListDto> planPage(MaintainPlanQueryDto queryDto);

    MaintainPlanInfoDto info(Long id);

    Boolean insert(MaintainPlanDto optDto);

    Boolean edit(MaintainPlanDto optDto);

    Boolean editBatch(MaintainPlanEditBatchDto optDto);

    Boolean status(MaintainPlanDto optDto);

    void handleMaintainPlanStatus();

    List<MaintainPlanMessageDto> countMaintainPlan(Long companyId, String type, List<Integer> days);

}
