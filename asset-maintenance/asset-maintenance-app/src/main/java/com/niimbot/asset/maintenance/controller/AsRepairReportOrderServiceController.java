package com.niimbot.asset.maintenance.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.activiti.WorkflowExecuteStepDto;
import com.niimbot.activiti.WorkflowStartDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.model.ActWorkflowBusiness;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActWorkflowBusinessService;
import com.niimbot.asset.activiti.service.ActWorkflowCallbackErrorService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.WorkflowUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.maintenance.model.AsRepairReportOrder;
import com.niimbot.asset.maintenance.model.AsRepairReportOrderDetail;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.model.AsOrderType;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AsOrderService;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.RepairReportOrderDetailDto;
import com.niimbot.maintenance.RepairReportOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairReportOrderDto;
import com.niimbot.maintenance.RepairReportOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 资产报修单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Slf4j
@RestController
@RequestMapping("server/maintenance/repair/report")
@RequiredArgsConstructor
public class AsRepairReportOrderServiceController {
    private final AsRepairReportOrderService repairReportOrderService;
    private final AsOrderTypeService orderTypeService;
    private final AsOrderService orderService;
    private final ActWorkflowService workflowService;
    private final AsCusEmployeeService cusEmployeeService;
    private final ActWorkflowBusinessService workflowBusinessService;
    private final AssetService assetService;
    private final AsAssetLogService assetLogService;
    private final MessageService messageService;
    @Resource
    private RedisService redisService;
    @Resource
    private ActWorkflowCallbackErrorService callbackErrorService;
    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, @RequestBody RepairReportOrderDto dto) {
        WorkflowExecuteDto result = new WorkflowExecuteDto();
        String uuid = UUID.fastUUID().toString();
        result.setConditionId(uuid);
        loginUserDto.getCusUser().setOrgId(dto.getOrgId());
        // 查询单据类型
        AsOrderType orderType = orderTypeService.getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsOrderType::getType, dto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), dto.getOrgId(), false);
        if (workflow != null) {
            JSONObject orderData = dto.getOrderData();
            List<String> categoryList = new ArrayList<>();
            List<String> areaList = new ArrayList<>();
            List<String> orgOwnerList = new ArrayList<>();
            BigDecimal totalMoney = BigDecimal.ZERO;

            List<Long> assetIds = dto.getAssets().stream().map(RepairReportOrderDetailDto::getId).collect(toList());
            List<AsAsset> asAssets = assetService.listByIds(assetIds);
            Set<Long> managerOwnerSet = new HashSet<>();
            for (AsAsset asAsset : asAssets) {
                JSONObject assetData = asAsset.getAssetData();
                String assetCategory = assetData.getString("assetCategory");
                if (StrUtil.isNotEmpty(assetCategory)) {
                    categoryList.add(assetCategory);
                }
                String storageArea = assetData.getString("storageArea");
                if (StrUtil.isNotEmpty(storageArea)) {
                    areaList.add(storageArea);
                }
                String orgOwner = assetData.getString("orgOwner");
                if (StrUtil.isNotEmpty(orgOwner)) {
                    orgOwnerList.add(orgOwner);
                }
                String price = assetData.getString("price");
                if (StrUtil.isNotEmpty(price)) {
                    BigDecimal priceNum = new BigDecimal(price);
                    totalMoney = totalMoney.add(priceNum);
                }
                if (managerOwnerSet.size() < 20) {
                    Long manageOwner = assetData.getLong("managerOwner");
                    if (manageOwner != null) {
                        managerOwnerSet.add(manageOwner);
                    }
                }
            }
            JSONObject condition = new JSONObject();
            if (CollUtil.isNotEmpty(categoryList)) {
                condition.put(QueryFieldConstant.ASSET_CATEGORY, categoryList);
            }
            if (CollUtil.isNotEmpty(areaList)) {
                condition.put(QueryFieldConstant.ASSET_AREA, areaList);
            }
            if (CollUtil.isNotEmpty(orgOwnerList)) {
                condition.put(QueryFieldConstant.ASSET_ORG_OWNER, orgOwnerList);
            }
            condition.put(QueryFieldConstant.ASSET_TOTAL_MONEY, totalMoney);
            condition.put(QueryFieldConstant.SUBMITTER_ORG, CollUtil.union(ListUtil.of(LoginUserThreadLocal.getCurrentUserId(), dto.getOrgId()), approveRoleMemberService.getEmpRoleIds(LoginUserThreadLocal.getCurrentUserId())));
            orderData.putAll(condition);
            orderData.put(QueryFieldConstant.ASSET_MANAGER_OWNER, new ArrayList<>(managerOwnerSet));

            String redisKey = RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.hSetAll(redisKey, condition);
            redisService.expire(redisKey, 2, TimeUnit.HOURS);
            result.setWorkflowExecuteStepDto(workflowService.preStartWorkflow(loginUserDto, workflow, orderData, null));
            String stepKey = RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.set(stepKey, result.getWorkflowExecuteStepDto());
            redisService.expire(stepKey, 2, TimeUnit.HOURS);
        }
        return result;
    }

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AuditableCreateOrderResult create(LoginUserDto loginUserDto, @RequestBody RepairReportOrderSubmitDto dto) {
        orderService.verify(dto.getOrderDto());
        orderService.translation(dto.getOrderDto());
        loginUserDto.getCusUser().setOrgId(dto.getOrderDto().getOrgId());
        RepairReportOrderDto orderDto = dto.getOrderDto();

        // 查询单据类型
        AsOrderType orderType = orderTypeService.getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsOrderType::getType, orderDto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), orderDto.getOrgId(), false);

        // 创建报修单
        orderDto.setId(IdUtils.getId());
        repairReportOrderService.create(orderDto, workflow != null);

        List<Long> assetIds = orderDto.getAssets().stream().map(RepairReportOrderDetailDto::getId)
                .collect(Collectors.toList());
        // 流程开启, 走流程审批
        if (workflow != null) {
            if (dto.getConditionId() == null) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_COMMON_ERROR, "流程执行条件Id不能为空");
            }
            if (dto.getExecuteStepDtoList().isEmpty()) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_EXECUTE_STEP_IS_EMPTY);
            }

            // 写入审批人
            List<WorkflowExecuteStepDto> executeStepList = Convert.toList(WorkflowExecuteStepDto.class, redisService.get(
                    RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId())));
            Map<Long, List<WorkflowExecuteStepDto.WorkflowApprover>> submitExecuteStepList = dto.getExecuteStepDtoList().stream()
                    .collect(Collectors.toMap(WorkflowExecuteStepDto::getStepId, WorkflowExecuteStepDto::getApproverList, (k1, k2) -> k1));
            for (WorkflowExecuteStepDto stepDto : executeStepList) {
                if (submitExecuteStepList.containsKey(stepDto.getStepId())) {
                    stepDto.setApproverList(submitExecuteStepList.get(stepDto.getStepId()));
                }
            }

            AsCusEmployee employee = cusEmployeeService.getById(loginUserDto.getCusUser().getId());
            String userName = employee == null ? loginUserDto.getCusUser().getAccount() : employee.getEmpName();

            JSONObject orderData = orderDto.getOrderData();
            Map<Object, Object> map = redisService.hGetAll(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            if (map != null) {
                Map<String, Object> conditionMap = Convert.toMap(String.class, Object.class, map);
                orderData.putAll(conditionMap);
            }
            workflowService.startWorkflow(
                    new WorkflowStartDto()
                            .setUserDto(loginUserDto.getCusUser())
                            .setTitle(getTitle(userName))
                            .setActivitiKey(orderType.getActivitiKey())
                            .setWorkflowId(workflow.getId())
                            .setVersion(workflow.getVersion())
                            .setCallbackUrl(workflow.getCallbackUrl())
                            .setBusinessType(orderType.getType().shortValue())
                            .setBusinessId(orderDto.getId())
                            .setFormData(orderData)
                            .setExecuteStepDtoList(executeStepList));

            // 修改资产状态为审批中
            assetService.updateBatchStatus(assetIds, asset -> {
                asset.setBeforeStatus(asset.getStatus());
                asset.setStatus(AssetConstant.ASSET_STATUS_CHECK);
            });
            redisService.del(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            redisService.del(RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
        }
        // 流程未开启, 直接修改资产状态
        else {
            assetService.updateBatchStatus(assetIds, asset -> {
                asset.setBeforeStatus(asset.getStatus());
                asset.setStatus(AssetConstant.ASSET_STATUS_WAIT_SERVICE);
            });
            // 写入资产履历
            saveAssetLog(orderDto.getId(), orderDto.getOrderNo(), orderDto.getOrderData(), assetIds);
            messageService.sendInnerMessage(MsgSendCmd.bx(orderDto.getId(), assetIds));
        }
        return new AuditableCreateOrderResult(orderDto.getId(), orderDto.getOrderNo());
    }

    private void saveAssetLog(Long orderId, String orderNo, JSONObject orderData, List<Long> assetIds) {
        if (CollUtil.isNotEmpty(assetIds)) {
            String repairContent = orderData.getString("repairContent");
            String repairDateFmt = "----";
            try {
                Long repairDateLong = orderData.getLong("repairDate");
                LocalDateTime repairDate = LocalDateTime.ofEpochSecond(repairDateLong / 1000, 0, ZoneOffset.of("+8"));
                repairDateFmt = repairDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                // ignore
                log.error(e.getMessage());
            }

            String actionContent = String.format("报修时间：%s；报修内容：%s", repairDateFmt, StrUtil.isNotEmpty(repairContent) ? repairContent : "----");

            LocalDateTime now = LocalDateTime.now();
            List<AsAssetLog> assetLogList = new ArrayList<>();
            for (Long assetId : assetIds) {
                // 写入资产履历
                AsAssetLog asAssetLog = new AsAssetLog()
                        .setAssetId(assetId)
                        .setActionType(AssetConstant.ORDER_TYPE_REPAIR_REPORT)
                        .setOrderNo(orderNo)
                        .setOrderId(orderId)
                        .setActionName("报修")
                        .setHandleTime(now)
                        .setActionContent(actionContent);
                assetLogList.add(asAssetLog);
            }
            assetLogService.saveBatch(assetLogList);
        }
    }

    @GetMapping("/{id}")
    public RepairReportOrderDto getById(@PathVariable Long id) {
        return repairReportOrderService.getOrderDetail(id);
    }

    @GetMapping("/detail/{orderId}/{assetId}")
    public AsRepairReportOrderDetail getDetail(@PathVariable("orderId") Long orderId, @PathVariable("assetId") Long assetId) {
        return repairReportOrderService.getDetail(orderId, assetId);
    }

    @PostMapping("/page")
    public IPage<AsRepairReportOrder> page(@RequestBody AsOrderQueryDto query) {
        return repairReportOrderService.page(query);
    }

    @GetMapping("/pageDetail")
    public IPage<AsRepairReportOrderDetail> pageDetail(RepairReportOrderDetailPageQueryDto query) {
        return repairReportOrderService.pageDetail(query);
    }

    /**
     * 保修单回调接口
     *
     * @param callbackDto
     * @return
     */
    @PostMapping("/processCallback")
    @Transactional(rollbackFor = Exception.class)
    public Boolean processCallback(@RequestBody WorkflowCallbackDto callbackDto) {
        AsRepairReportOrder repairReportOrder = repairReportOrderService.getById(Long.parseLong(callbackDto.getBusinessId()));
        if (repairReportOrder == null) {
            return false;
        }
        callbackErrorService.remove(callbackDto);
        if (WorkflowUtil.finishWorkflowCallbackAllowed().contains(repairReportOrder.getApproveStatus())) {
            return true;
        }
        short status = Short.parseShort(callbackDto.getApproveStatus());
        if (!WorkflowUtil.finishWorkflowCallbackAllowed().contains(status)) {
            return false;
        }
        // threadLocal写入公司Id
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(new CusUserDto()
                .setId(repairReportOrder.getCreateBy())
                .setIsAdmin(true)
                .setCompanyId(repairReportOrder.getCompanyId()));
        LoginUserThreadLocal.set(userDto);
        Optional<ActWorkflowBusiness> workflowBusiness = Optional.ofNullable(workflowBusinessService.getById(callbackDto.getProcessInstanceId()));
        workflowBusiness.ifPresent(v -> v.setApproveStatus(status));
        try {
            repairReportOrder.setApproveStatus(status);
            List<AsRepairReportOrderDetail> details = repairReportOrderService.getDetailsById(repairReportOrder.getId());
            List<Long> assetIds = details.stream().map(AsRepairReportOrderDetail::getId).collect(Collectors.toList());
            if (DictConstant.APPROVED == status) {
                // 审批通过, 修改资产状态
                assetService.updateBatchStatus(assetIds, asset -> asset.setStatus(AssetConstant.ASSET_STATUS_WAIT_SERVICE));
                // 写入资产履历
                saveAssetLog(repairReportOrder.getId(),
                        repairReportOrder.getOrderNo(),
                        repairReportOrder.getOrderData(),
                        assetIds);
            } else {
                // 回滚资产状态
                assetService.updateBatchStatus(assetIds, asset -> {
                    asset.setStatus(asset.getBeforeStatus());
                    asset.setBeforeStatus(0);
                });
            }
            workflowBusiness.ifPresent(workflowBusinessService::updateById);
            repairReportOrderService.updateById(repairReportOrder);
            if (Objects.equals(repairReportOrder.getApproveStatus(), DictConstant.APPROVED)) {
                messageService.sendInnerMessage(MsgSendCmd.bx(repairReportOrder.getId(), assetIds));
            }
        } finally {
            LoginUserThreadLocal.remove();
        }
        return true;
    }

    private String getTitle(String userName) {
        return String.format("%s提交的报修单", userName);
    }

    /**
     * 资产单据-用于导出
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/listForExport")
    public List<AsOrderDto> listForExport(@RequestBody AsOrderQueryDto dto) {
        return repairReportOrderService.listForExport(dto);
    }

    @PostMapping("/assets")
    public List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids) {
        return repairReportOrderService.getAssetsByOrderId(ids);
    }

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @param assetId assetId
     * @return 结果
     */
    @GetMapping("/approveOrderByAsset/{assetId}")
    public AsOrderInfoDto getApproveOrderByAssetId(@PathVariable Long assetId) {
        return repairReportOrderService.getApproveOrderByAssetId(assetId);
    }

}
