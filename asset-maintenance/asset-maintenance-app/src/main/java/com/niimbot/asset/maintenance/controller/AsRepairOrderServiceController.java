package com.niimbot.asset.maintenance.controller;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.activiti.WorkflowExecuteStepDto;
import com.niimbot.activiti.WorkflowStartDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.model.ActWorkflowBusiness;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActWorkflowBusinessService;
import com.niimbot.asset.activiti.service.ActWorkflowCallbackErrorService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.WorkflowUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.maintenance.model.AsRepairOrder;
import com.niimbot.asset.maintenance.model.AsRepairOrderDetail;
import com.niimbot.asset.maintenance.service.AsRepairOrderService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.model.AsOrderType;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AsOrderService;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.RepairOrderDetailDto;
import com.niimbot.maintenance.RepairOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairOrderDto;
import com.niimbot.maintenance.RepairOrderFinishDto;
import com.niimbot.maintenance.RepairOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 资产维修单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("server/maintenance/repair")
public class AsRepairOrderServiceController {
    private final AsRepairOrderService repairOrderService;
    private final AsOrderTypeService orderTypeService;
    private final AsOrderService orderService;
    private final ActWorkflowService workflowService;
    private final AsCusEmployeeService cusEmployeeService;
    private final ActWorkflowBusinessService workflowBusinessService;
    private final AssetService assetService;
    private final AsAssetLogService assetLogService;
    @Resource
    private RedisService redisService;
    @Resource
    private ActWorkflowCallbackErrorService callbackErrorService;
    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;
    @Resource
    private MessageService messageService;

    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, @RequestBody RepairOrderDto dto) {
        WorkflowExecuteDto result = new WorkflowExecuteDto();
        String uuid = UUID.fastUUID().toString();
        result.setConditionId(uuid);
        loginUserDto.getCusUser().setOrgId(dto.getOrgId());
        // 查询单据类型
        AsOrderType orderType = orderTypeService.getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsOrderType::getType, dto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), dto.getOrgId(), false);
        if (workflow != null) {
            JSONObject orderData = dto.getOrderData();
            List<String> categoryList = new ArrayList<>();
            List<String> areaList = new ArrayList<>();
            List<String> orgOwnerList = new ArrayList<>();
            BigDecimal totalMoney = BigDecimal.ZERO;

            List<Long> assetIds = dto.getAssets().stream().map(RepairOrderDetailDto::getId).collect(toList());
            List<AsAsset> asAssets = assetService.listByIds(assetIds);
            Set<Long> managerOwnerSet = new HashSet<>();
            for (AsAsset asAsset : asAssets) {
                JSONObject assetData = asAsset.getAssetData();
                String assetCategory = assetData.getString("assetCategory");
                if (StrUtil.isNotEmpty(assetCategory)) {
                    categoryList.add(assetCategory);
                }
                String storageArea = assetData.getString("storageArea");
                if (StrUtil.isNotEmpty(storageArea)) {
                    areaList.add(storageArea);
                }
                String orgOwner = assetData.getString("orgOwner");
                if (StrUtil.isNotEmpty(orgOwner)) {
                    orgOwnerList.add(orgOwner);
                }
                String price = assetData.getString("price");
                if (StrUtil.isNotEmpty(price)) {
                    BigDecimal priceNum = new BigDecimal(price);
                    totalMoney = totalMoney.add(priceNum);
                }
                if (managerOwnerSet.size() < 20) {
                    Long manageOwner = assetData.getLong("managerOwner");
                    if (manageOwner != null) {
                        managerOwnerSet.add(manageOwner);
                    }
                }
            }
            JSONObject condition = new JSONObject();
            if (CollUtil.isNotEmpty(categoryList)) {
                condition.put(QueryFieldConstant.ASSET_CATEGORY, categoryList);
            }
            if (CollUtil.isNotEmpty(areaList)) {
                condition.put(QueryFieldConstant.ASSET_AREA, areaList);
            }
            if (CollUtil.isNotEmpty(orgOwnerList)) {
                condition.put(QueryFieldConstant.ASSET_ORG_OWNER, orgOwnerList);
            }
            condition.put(QueryFieldConstant.ASSET_TOTAL_MONEY, totalMoney);
            condition.put(QueryFieldConstant.SUBMITTER_ORG, CollUtil.union(ListUtil.of(LoginUserThreadLocal.getCurrentUserId(), dto.getOrgId()), approveRoleMemberService.getEmpRoleIds(LoginUserThreadLocal.getCurrentUserId())));
            orderData.putAll(condition);
            orderData.put(QueryFieldConstant.ASSET_MANAGER_OWNER, new ArrayList<>(managerOwnerSet));

            String redisKey = RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.hSetAll(redisKey, condition);
            redisService.expire(redisKey, 2, TimeUnit.HOURS);
            result.setWorkflowExecuteStepDto(workflowService.preStartWorkflow(loginUserDto, workflow, orderData, null));
            String stepKey = RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.set(stepKey, result.getWorkflowExecuteStepDto());
            redisService.expire(stepKey, 2, TimeUnit.HOURS);
        }
        return result;
    }

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AuditableCreateOrderResult create(LoginUserDto loginUserDto, @RequestBody RepairOrderSubmitDto dto) {
        orderService.verify(dto.getOrderDto());
        orderService.translation(dto.getOrderDto());
        loginUserDto.getCusUser().setOrgId(dto.getOrderDto().getOrgId());
        RepairOrderDto orderDto = dto.getOrderDto();

        // 查询单据类型
        AsOrderType orderType = orderTypeService.getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                        .eq(AsOrderType::getType, orderDto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), loginUserDto.getCusUser().getOrgId(), false);

        // 创建维修单
        repairOrderService.create(orderDto, workflow != null);

        // 流程开启, 走流程审批
        if (workflow != null) {
            if (dto.getConditionId() == null) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_COMMON_ERROR, "流程执行条件Id不能为空");
            }
            if (dto.getExecuteStepDtoList().isEmpty()) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_EXECUTE_STEP_IS_EMPTY);
            }
            // 写入审批人
            List<WorkflowExecuteStepDto> executeStepList = Convert.toList(WorkflowExecuteStepDto.class, redisService.get(
                    RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId())));
            Map<Long, List<WorkflowExecuteStepDto.WorkflowApprover>> submitExecuteStepList = dto.getExecuteStepDtoList().stream()
                    .collect(Collectors.toMap(WorkflowExecuteStepDto::getStepId, WorkflowExecuteStepDto::getApproverList, (k1, k2) -> k1));
            for (WorkflowExecuteStepDto stepDto : executeStepList) {
                if (submitExecuteStepList.containsKey(stepDto.getStepId())) {
                    stepDto.setApproverList(submitExecuteStepList.get(stepDto.getStepId()));
                }
            }

            AsCusEmployee employee = cusEmployeeService.getById(loginUserDto.getCusUser().getId());
            String userName = employee == null ? loginUserDto.getCusUser().getAccount() : employee.getEmpName();

            JSONObject orderData = orderDto.getOrderData();
            Map<Object, Object> map = redisService.hGetAll(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            if (map != null) {
                Map<String, Object> conditionMap = Convert.toMap(String.class, Object.class, map);
                orderData.putAll(conditionMap);
            }
            workflowService.startWorkflow(
                    new WorkflowStartDto()
                            .setUserDto(loginUserDto.getCusUser())
                            .setTitle(getTitle(userName))
                            .setActivitiKey(orderType.getActivitiKey())
                            .setWorkflowId(workflow.getId())
                            .setVersion(workflow.getVersion())
                            .setCallbackUrl(workflow.getCallbackUrl())
                            .setBusinessType(orderType.getType().shortValue())
                            .setBusinessId(orderDto.getId())
                            .setFormData(orderData)
                            .setExecuteStepDtoList(executeStepList));

            List<Long> assetIds = orderDto.getAssets().stream().map(RepairOrderDetailDto::getId)
                    .collect(Collectors.toList());
            // 修改资产状态为审批中
            assetService.updateBatchStatus(assetIds, asset -> {
                asset.setRepairBeforeStatus(asset.getBeforeStatus());
                asset.setBeforeStatus(asset.getStatus());
                asset.setStatus(AssetConstant.ASSET_STATUS_CHECK);
            });
            redisService.del(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            redisService.del(RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
        }
        // 流程未开启, 直接修改资产状态
        else {
            repairOrderService.directRepairFinish(orderDto);
            messageService.sendInnerMessage(MsgSendCmd.wx(orderDto.getId(), orderDto.getAssets().stream().map(RepairOrderDetailDto::getId).collect(Collectors.toList())));
        }

        return new AuditableCreateOrderResult(orderDto.getId(), orderDto.getOrderNo());
    }

    @PutMapping("/repairFinish")
    @Transactional(rollbackFor = Exception.class)
    public Boolean repairFinish(@RequestBody RepairOrderFinishDto dto) {
        Boolean result = repairOrderService.repairFinish(dto) &&
                assetService.updateBatchStatus(Collections.singletonList(dto.getAssetId()), asset -> {
                    if (AssetConstant.ASSET_STATUS_WAIT_SERVICE.equals(asset.getBeforeStatus())) {
                        asset.setStatus(asset.getRepairBeforeStatus());
                        asset.setBeforeStatus(0);
                        asset.setRepairBeforeStatus(0);
                    } else {
                        asset.setStatus(asset.getBeforeStatus());
                        asset.setBeforeStatus(asset.getRepairBeforeStatus());
                        asset.setRepairBeforeStatus(0);
                    }
                });
        if (result) {
            messageService.sendInnerMessage(MsgSendCmd.wx(dto.getRepairOrderId(), Lists.newArrayList(dto.getAssetId())));
        }
        return result;
    }

    @GetMapping("/{id}")
    public RepairOrderDto getById(@PathVariable Long id) {
        return repairOrderService.getOrderDetail(id);
    }

    @GetMapping("/detail/{orderId}/{assetId}")
    public AsRepairOrderDetail getDetail(@PathVariable("orderId") Long orderId, @PathVariable("assetId") Long assetId) {
        return repairOrderService.getDetail(orderId, assetId);
    }

    @PostMapping("/page")
    public IPage<AsRepairOrder> page(@RequestBody AsOrderQueryDto query) {
        return repairOrderService.page(query);
    }

    @GetMapping("/pageDetail")
    public IPage<AsRepairOrderDetail> pageDetail(RepairOrderDetailPageQueryDto query) {
        return repairOrderService.pageDetail(query);
    }

    /**
     * 保修单回调接口
     *
     * @param callbackDto
     * @return
     */
    @PostMapping("/processCallback")
    @Transactional(rollbackFor = Exception.class)
    public Boolean processCallback(@RequestBody WorkflowCallbackDto callbackDto) {
        AsRepairOrder repairOrder = repairOrderService.getById(Long.parseLong(callbackDto.getBusinessId()));
        if (repairOrder == null) {
            return false;
        }
        callbackErrorService.remove(callbackDto);
        if (WorkflowUtil.finishWorkflowCallbackAllowed().contains(repairOrder.getApproveStatus())) {
            return true;
        }
        short status = Short.parseShort(callbackDto.getApproveStatus());
        if (!WorkflowUtil.finishWorkflowCallbackAllowed().contains(status)) {
            return false;
        }
        // threadLocal写入公司Id
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(new CusUserDto()
                .setId(repairOrder.getCreateBy())
                .setIsAdmin(true)
                .setCompanyId(repairOrder.getCompanyId()));
        LoginUserThreadLocal.set(userDto);
        try {
            Optional<ActWorkflowBusiness> workflowBusiness = Optional.ofNullable(workflowBusinessService.getById(callbackDto.getProcessInstanceId()));
            workflowBusiness.ifPresent(v -> v.setApproveStatus(status));
            repairOrder.setApproveStatus(status);
            List<AsRepairOrderDetail> details = repairOrderService.getDetailsById(repairOrder.getId());
            List<Long> assetIds = details.stream().map(AsRepairOrderDetail::getId)
                    .collect(Collectors.toList());
            if (DictConstant.APPROVED == status) {
                // 单据是维修中状态, 修改资产状态为维修中
                if (DictConstant.REPAIR_STATUS_REPAIRING.equals(repairOrder.getOrderData().getString(AsRepairOrder.REPAIR_STATUS))) {
                    assetService.updateBatchStatus(assetIds, asset ->
                            asset.setStatus(AssetConstant.ASSET_STATUS_SERVICE));
                }
                // 单据是维修完成状态, 回滚资产状态
                else {
                    assetService.updateBatchStatus(assetIds, asset -> {
                        if (AssetConstant.ASSET_STATUS_WAIT_SERVICE.equals(asset.getBeforeStatus())) {
                            asset.setStatus(asset.getRepairBeforeStatus());
                            asset.setBeforeStatus(0);
                            asset.setRepairBeforeStatus(0);
                        } else {
                            asset.setStatus(asset.getBeforeStatus());
                            asset.setBeforeStatus(asset.getRepairBeforeStatus());
                            asset.setRepairBeforeStatus(0);
                        }
                    });
                    List<AsAssetLog> logs = new ArrayList<>();
                    details.forEach(detail -> {
                        String remark = repairOrder.getOrderData().getString(AsRepairOrder.REMARK);
                        AsAssetLog asAssetLog = new AsAssetLog()
                                .setAssetId(detail.getId())
                                .setActionType(AssetConstant.ORDER_TYPE_REPAIR)
                                .setOrderNo(repairOrder.getOrderNo())
                                .setOrderId(repairOrder.getId())
                                .setCompanyId(repairOrder.getCompanyId())
                                .setActionName("维修")
                                .setActionContent(getActionContent(detail.getRepairMoney(), remark))
                                .setCreateBy(repairOrder.getCreateBy());
                        String handleTimeKey = AssetConstant.ORDER_ASSET_LOG_DATE.get(AssetConstant.ORDER_TYPE_REPAIR);
                        Long handleTime = repairOrder.getOrderData().getLong(handleTimeKey);
                        // 更新处理时间
                        try {
                            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(handleTime / 1000, 0, ZoneOffset.of("+8"));
                            asAssetLog.setHandleTime(dateTime);
                        } catch (Exception ignored) {
                        }
                        logs.add(asAssetLog);
                    });
                    assetLogService.saveBatch(logs);
                }
                messageService.sendInnerMessage(MsgSendCmd.wx(repairOrder.getId(), assetIds));
            } else {
                // 回滚资产状态
                assetService.updateBatchStatus(assetIds, asset -> {
                    asset.setStatus(asset.getBeforeStatus());
                    asset.setBeforeStatus(asset.getRepairBeforeStatus());
                    asset.setRepairBeforeStatus(0);
                });
            }
            workflowBusiness.ifPresent(workflowBusinessService::updateById);
            repairOrderService.updateById(repairOrder);

        } finally {
            LoginUserThreadLocal.remove();
        }
        return true;
    }

    private String getTitle(String userName) {
        return String.format("%s提交的维修单", userName);
    }

    private String getActionContent(BigDecimal repairMoney, String remark) {
        return String.format("维修花费：%.2f元；维修备注：%s",
                Optional.ofNullable(repairMoney).orElse(BigDecimal.ZERO),
                StrUtil.isNotBlank(remark) ? remark : "----");
    }

    /**
     * 资产单据-用于导出
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/listForExport")
    public List<AsOrderDto> listForExport(@RequestBody AsOrderQueryDto dto) {
        return repairOrderService.listForExport(dto);
    }

    @PostMapping("/assets")
    public List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids) {
        return repairOrderService.getAssetsByOrderId(ids);
    }

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @param assetId assetId
     * @return 结果
     */
    @GetMapping("/approveOrderByAsset/{assetId}")
    public AsOrderInfoDto getApproveOrderByAssetId(@PathVariable Long assetId) {
        return repairOrderService.getApproveOrderByAssetId(assetId);
    }

}
