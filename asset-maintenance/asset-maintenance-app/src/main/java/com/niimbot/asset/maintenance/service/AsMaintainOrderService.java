package com.niimbot.asset.maintenance.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.maintenance.model.AsMaintainOrder;
import com.niimbot.maintenance.MaintainOrderDto;
import com.niimbot.maintenance.MaintainOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.system.QueryConditionSortDto;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 资产保养单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
public interface AsMaintainOrderService extends IService<AsMaintainOrder> {

    IPage<AsMaintainOrder> orderPage(AsOrderQueryDto queryDto);

    Boolean insert(LoginUserDto loginUserDto, MaintainOrderSubmitDto submitDto);

    JSONObject getAssetData(Long orderId);

    MaintainOrderDto getInfo(Long id);

    /**
     * 资产单据-用于导出
     *
     * @param dto dto
     * @return 结果
     */
    List<AsOrderDto> listForExport(AsOrderQueryDto dto);

    QueryConditionSortDto sortField();

    List<AsOrderAssetDto> getAssetsByOrderId(Collection<Long> ids);

    WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, MaintainOrderDto orderDto);

    Boolean processCallback(WorkflowCallbackDto callbackDto);

    AsMaintainOrder tempGetById(Long orderId);
}
