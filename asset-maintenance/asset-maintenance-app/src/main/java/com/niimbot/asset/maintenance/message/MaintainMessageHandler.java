package com.niimbot.asset.maintenance.message;

import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.maintenance.service.AsMaintainPlanService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.PeriodCompanyMessageHandler;
import com.niimbot.maintenance.MaintainPlanMessageDto;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;

/**
 * 保养到期消息提醒
 * 保养过期消息提醒
 *
 * <AUTHOR>
 */
public abstract class MaintainMessageHandler extends PeriodCompanyMessageHandler {

    @Resource
    private AsMaintainPlanService maintainPlanService;

    @Override
    protected boolean skip(MessageRuleCO rule) {
        return Objects.isNull(rule.getReceiverType()) || rule.getReceiverType().isEmpty() || Objects.isNull(rule.getReminderTime()) || rule.getReminderTime().isEmpty() || Objects.isNull(rule.getChannel()) || rule.getChannel().isEmpty();
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        List<MaintainPlanMessageDto> maintainPlans = maintainPlanService.countMaintainPlan(rule.getCompanyId(), rule.getCode(), rule.getReminderTime());
        if (CollUtil.isEmpty(maintainPlans)) {
            return;
        }
        Set<Long> userIds = new HashSet<>();
        // 解析接收人
        maintainPlans.forEach(v -> {
            if (rule.includeReceiverType(MessageConstant.ReceiverType.ASSET_USER)) {
                userIds.add(v.getUsePerson());
            }
            if (rule.includeReceiverType(MessageConstant.ReceiverType.ASSET_OWNER)) {
                userIds.add(v.getManagerOwner());
            }
            if (rule.includeReceiverType(MessageConstant.ReceiverType.MAINTENANCE_PRINCIPAL)) {
                userIds.addAll(v.getMaintainPerson());
            }
            if (rule.includeCuzReceiverType()) {
                userIds.addAll(rule.getExtConfig().getJSONArray(MessageConstant.ExtConfig.RECEIVE_EMP_IDS).toJavaList(Long.class));
            }
        });
        Map<Integer, List<MaintainPlanMessageDto>> groupDay = maintainPlans.stream().collect(Collectors.groupingBy(MaintainPlanMessageDto::getDay));
        groupDay.forEach((day, v) -> {
            Map<String, String> map = new HashMap<>(2);
            map.put(MessageConstant.Template.AMOUNT, String.valueOf(v.size()));
            map.put(MessageConstant.Template.DAY, String.valueOf(Math.abs(day)));
            String[] arrayParams = new String[]{String.valueOf(v.size()), String.valueOf(Math.abs(day))};
            Map<String, Object> commonExtMap = new HashMap<>(16);
            commonExtMap.put(MessageConstant.ExtConfig.ASSET_IDS, v.stream().map(MaintainPlanMessageDto::getAssetId).collect(Collectors.toList()));
            bodies.add(Body.builder()
                    .mapParam(map)
                    .arrayParam(arrayParams)
                    .commonExtMap(commonExtMap)
                    .userIds(userIds)
                    .build());
        });
    }

    @Component
    public static class DqTx extends MaintainMessageHandler {
        @Override
        public String code() {
            return MessageConstant.Code.BYDQTX.getCode();
        }
    }

    @Component
    public static class GqTx extends MaintainMessageHandler {
        @Override
        public String code() {
            return MessageConstant.Code.BYGQTX.getCode();
        }
    }
}
