package com.niimbot.asset.maintenance.controller;

import com.niimbot.asset.maintenance.service.MaintainService;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/7/13 13:58
 */
@RestController
@RequestMapping("server/maintenance/maintain")
@RequiredArgsConstructor
public class MaintainServiceController {
    private final MaintainService maintainService;

    @GetMapping(value = "/tag/attrList")
    public TagAttrListCO getTagAttrList(String kw) {
        return maintainService.getAttrList(kw);
    }
}
