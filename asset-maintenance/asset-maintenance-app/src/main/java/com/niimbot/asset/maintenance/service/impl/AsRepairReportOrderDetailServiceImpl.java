package com.niimbot.asset.maintenance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.maintenance.mapper.AsRepairReportOrderDetailMapper;
import com.niimbot.asset.maintenance.model.AsRepairReportOrderDetail;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderDetailService;
import com.niimbot.means.AsOrderAssetDto;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产报修单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Service
public class AsRepairReportOrderDetailServiceImpl extends ServiceImpl<AsRepairReportOrderDetailMapper, AsRepairReportOrderDetail> implements AsRepairReportOrderDetailService {

    @Override
    public List<AsOrderAssetDto> getDetailsByOrderId(Collection<Long> ids) {
        List<AsRepairReportOrderDetail> list = list(new QueryWrapper<AsRepairReportOrderDetail>()
                .lambda().in(AsRepairReportOrderDetail::getRepairReportOrderId, ids));
        return list.parallelStream().map(orderDetail -> {
            AsOrderAssetDto asOrderAssetDto = new AsOrderAssetDto().setId(orderDetail.getAssetSnapshotData().getLong("id"))
                    .setOrderId(orderDetail.getRepairReportOrderId());
            asOrderAssetDto.setAssetSnapshotData(orderDetail.getAssetSnapshotData());
            return asOrderAssetDto;
        }).collect(Collectors.toList());
    }
}
