package com.niimbot.asset.maintenance.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.maintenance.model.AsMaintainPlan;
import com.niimbot.asset.maintenance.model.AsMaintainPlanContent;
import com.niimbot.asset.maintenance.service.AsMaintainPlanContentService;
import com.niimbot.asset.maintenance.service.AsMaintainPlanService;
import com.niimbot.maintenance.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产保养计划 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@RestController
@RequestMapping("server/maintenance/maintain/plan")
public class AsMaintainPlanServiceController {

    private final AsMaintainPlanService maintainPlanService;

    private final AsMaintainPlanContentService maintainPlanContentService;

    @Autowired
    public AsMaintainPlanServiceController(AsMaintainPlanService maintainPlanService,
                                           AsMaintainPlanContentService maintainPlanContentService) {
        this.maintainPlanService = maintainPlanService;
        this.maintainPlanContentService = maintainPlanContentService;
    }

    /**
     * 保养计划分页列表
     *
     * @param queryDto 查询参数
     * @return 保养单列表
     */
    @GetMapping(value = "/page")
    public IPage<MaintainPlanListDto> planPage(MaintainPlanQueryDto queryDto) {
        return maintainPlanService.planPage(queryDto);
    }

    /**
     * 保养计划详情
     *
     * @param id 保养计划id
     * @return 保养计划详情
     */
    @GetMapping(value = "/{id}")
    public MaintainPlanInfoDto info(@PathVariable("id") Long id) {
        return maintainPlanService.info(id);
    }

    /**
     * 保养计划详情
     *
     * @param assetId 资产Id
     * @return 保养计划详情
     */
    @GetMapping(value = "/content/{assetId}")
    public MaintainPlanDetailDto contentInfo(@PathVariable("assetId") Long assetId) {
        AsMaintainPlan plan = maintainPlanService.getOne(new QueryWrapper<AsMaintainPlan>().lambda().eq(AsMaintainPlan::getAssetId, assetId), false);
        if (ObjectUtil.isNull(plan)) {
            return new MaintainPlanDetailDto();
        } else {
            MaintainPlanDetailDto maintainPlanDetailDto = new MaintainPlanDetailDto();
            List<AsMaintainPlanContent> planContentList = maintainPlanContentService.list(new QueryWrapper<AsMaintainPlanContent>().lambda()
                    .eq(AsMaintainPlanContent::getPlanId, plan.getId()));
            List<MaintainPlanContentDto> collect = planContentList.stream().map(it -> BeanUtil.copyProperties(it, MaintainPlanContentDto.class)).collect(Collectors.toList());
            maintainPlanDetailDto.setMaintainPlanContentDtoList(collect);
            LocalDateTime now = LocalDateTimeUtil.now();
            maintainPlanDetailDto.setCurrentMaintainDate(now);
            Integer frequency = plan.getFrequency();
            String frequencyUnit = plan.getFrequencyUnit();
            if (DictConstant.FREQUENCY_UNIT_DAY.equals(frequencyUnit)) {
                maintainPlanDetailDto.setNextMaintainDate(now.plusDays(Convert.toLong(frequency, 0L)));
            } else if (DictConstant.FREQUENCY_UNIT_MON.equals(frequencyUnit)) {
                maintainPlanDetailDto.setNextMaintainDate(now.plusMonths(Convert.toLong(frequency, 0L)));
            }
            return maintainPlanDetailDto;
        }
    }

    @PostMapping("/getByAssetIds")
    public List<MaintainPlanInfoDto> getByAssetIds(@RequestBody List<Long> assetIds) {
        if (CollUtil.isEmpty(assetIds)) {
            return Collections.emptyList();
        }
        List<AsMaintainPlan> plans = maintainPlanService.list(
                Wrappers.lambdaQuery(AsMaintainPlan.class)
                        .select(AsMaintainPlan::getId, AsMaintainPlan::getAssetId)
                        .in(AsMaintainPlan::getAssetId, assetIds)
        );
        return plans.stream().map(v -> BeanUtil.copyProperties(v, MaintainPlanInfoDto.class)).collect(Collectors.toList());
    }

    /**
     * 删除计划
     *
     * @param planIds 计划ID
     * @return 结果
     */
    @DeleteMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(@RequestBody List<Long> planIds) {
        maintainPlanService.removeByIds(planIds);
        maintainPlanContentService.remove(new QueryWrapper<AsMaintainPlanContent>().lambda()
                .in(AsMaintainPlanContent::getPlanId, planIds));
        return true;
    }

    /**
     * 新增保养计划
     *
     * @param optDto dto
     * @return 结果
     */
    @PostMapping
    public Boolean insert(@RequestBody MaintainPlanDto optDto) {
        return maintainPlanService.insert(optDto);
    }

    /**
     * 编辑保养计划
     *
     * @param optDto dto
     * @return 结果
     */
    @PutMapping
    public Boolean edit(@RequestBody MaintainPlanDto optDto) {
        return maintainPlanService.edit(optDto);
    }

    /**
     * 批量设置保养计划
     *
     * @param optDto dto
     * @return 结果
     */
    @PutMapping("/batch")
    public Boolean editBatch(@RequestBody MaintainPlanEditBatchDto optDto) {
        return maintainPlanService.editBatch(optDto);
    }

    /**
     * 新增保养计划
     *
     * @param optDto dto
     * @return 结果
     */
    @PutMapping("/status")
    public Boolean status(@RequestBody MaintainPlanDto optDto) {
        return maintainPlanService.status(optDto);
    }

}
