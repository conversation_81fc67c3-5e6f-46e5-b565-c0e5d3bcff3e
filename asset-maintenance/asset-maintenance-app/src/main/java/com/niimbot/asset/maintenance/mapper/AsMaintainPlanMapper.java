package com.niimbot.asset.maintenance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.maintenance.model.AsMaintainPlan;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.maintenance.MaintainPlanListDto;
import com.niimbot.maintenance.MaintainPlanMessageDto;
import com.niimbot.maintenance.MaintainPlanQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 资产保养计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@EnableDataPerm(excludeMethodName = {"handleMaintainPlanStatus", "selectPlanContentList"})
public interface AsMaintainPlanMapper extends BaseMapper<AsMaintainPlan> {

    IPage<MaintainPlanListDto> planPage(@Param("page") Page<Object> buildIPage, @Param("ew") MaintainPlanQueryDto queryDto);

//    List<MaintainPlanContentDto> selectPlanContentList(Long id);

    MaintainPlanInfoDto info(Long id);

    @Update("UPDATE as_maintain_plan SET maintain_status = 2 " +
            "WHERE is_delete = 0 AND status = 1 AND maintain_status = 1 AND next_time <= DATE_FORMAT(NOW(),'%Y-%m-%d')")
    void handleMaintainPlanStatus();

    List<MaintainPlanMessageDto> countMaintainPlan(@Param("companyId") Long companyId, @Param("type") String type , @Param("days") List<Integer> days);
}
