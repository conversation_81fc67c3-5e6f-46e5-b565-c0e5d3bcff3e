package com.niimbot.asset.maintenance.message;

import com.google.common.collect.Lists;

import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.maintenance.model.AsRepairReportOrder;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.InstantCompanyMessageHandler;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 资产报修提醒
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MeansBxMessageHandler extends InstantCompanyMessageHandler {

    private final AssetService assetService;

    private final AsRepairReportOrderService repairReportOrderService;

    @Override
    public String code() {
        return MessageConstant.Code.ZCBXTX.getCode();
    }

    @Override
    protected boolean include() {
        return true;
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        Long orderId = params.getId();
        AsRepairReportOrder repairReportOrder = repairReportOrderService.getById(orderId);
        if (Objects.isNull(repairReportOrder) || (!repairReportOrder.getApproveStatus().equals(DictConstant.APPROVED) && !repairReportOrder.getApproveStatus().equals(DictConstant.NO_APPROVE_PROCESS))) {
            return;
        }
        List<AsAsset> assets = assetService.listByIds(params.getIds());
        Map<Long, List<AsAsset>> wait = new HashMap<>(assets.size());
        if (rule.getReceiverType().contains(MessageConstant.ReceiverType.ASSET_USER)) {
            wait.putAll(assets.stream().filter(d -> StrUtil.isNotBlank(d.getUsePerson())).collect(Collectors.groupingBy(asset -> Long.parseLong(asset.getUsePerson()))));
        }
        if (rule.getReceiverType().contains(MessageConstant.ReceiverType.ASSET_OWNER)) {
            wait.putAll(assets.stream().filter(d -> StrUtil.isNotBlank(d.getManagerOwner())).collect(Collectors.groupingBy(asset -> Long.parseLong(asset.getManagerOwner()))));
        }
        if (rule.includeCuzReceiverType()) {
            List<Long> toJavaList = rule.getExtConfig().getJSONArray(MessageConstant.ExtConfig.RECEIVE_EMP_IDS).toJavaList(Long.class);
            if (CollUtil.isEmpty(toJavaList)) {
                return;
            }
            for (Long empId : toJavaList) {
                wait.putIfAbsent(empId, assets);
            }
        }
        wait.forEach((k, v) -> {
            Map<String, String> map = new HashMap<>(2);
            map.put(MessageConstant.Template.AMOUNT, String.valueOf(v.size()));
            String[] arrayParams = new String[]{String.valueOf(v.size())};
            Map<String, Object> commonExtMap = new HashMap<>(16);
            commonExtMap.put(MessageConstant.ExtConfig.ASSET_IDS, v.stream().map(AsAsset::getId).collect(Collectors.toList()));
            bodies.add(
                    Body.builder()
                            .mapParam(map)
                            .arrayParam(arrayParams)
                            .commonExtMap(commonExtMap)
                            .userIds(Collections.singleton(k))
                            .urlParam(Lists.newArrayList(rule.getCode()))
                            .build()
            );
        });
    }

}
