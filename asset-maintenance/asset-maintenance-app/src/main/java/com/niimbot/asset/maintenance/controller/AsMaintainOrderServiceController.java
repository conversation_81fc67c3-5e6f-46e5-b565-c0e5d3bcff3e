package com.niimbot.asset.maintenance.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.maintenance.model.AsMaintainOrder;
import com.niimbot.asset.maintenance.service.AsMaintainOrderService;
import com.niimbot.maintenance.MaintainOrderDto;
import com.niimbot.maintenance.MaintainOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 资产保养单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Slf4j
@RestController
@RequestMapping("server/maintenance/maintain/order")
public class AsMaintainOrderServiceController {

    private final AsMaintainOrderService maintainOrderService;

    @Autowired
    public AsMaintainOrderServiceController(AsMaintainOrderService maintainOrderService) {
        this.maintainOrderService = maintainOrderService;
    }

    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, @RequestBody MaintainOrderDto orderDto) {
        loginUserDto.getCusUser().setOrgId(orderDto.getOrgId());
        return maintainOrderService.getWorkflowStepList(loginUserDto, orderDto);
    }

    @PostMapping("/processCallback")
    public Boolean processCallback(@RequestBody WorkflowCallbackDto callbackDto) {
        return maintainOrderService.processCallback(callbackDto);
    }

    /**
     * 保养单分页列表
     *
     * @param queryDto 查询参数
     * @return 保养单列表
     */
    @PostMapping(value = "/page")
    public IPage<AsMaintainOrder> orderPage(@RequestBody AsOrderQueryDto queryDto) {
        return maintainOrderService.orderPage(queryDto);
    }

    @GetMapping(value = "/{id}")
    public MaintainOrderDto info(@PathVariable("id") Long id) {
        return this.maintainOrderService.getInfo(id);
    }

    @GetMapping(value = "/assetData/{orderId}")
    public JSONObject getAssetData(@PathVariable("orderId") Long orderId) {
        return this.maintainOrderService.getAssetData(orderId);
    }

    /**
     * 保养单新增
     *
     * @param orderDto 新增保养单
     * @return 节点信息
     */
    @PostMapping
    public AuditableCreateOrderResult insert(LoginUserDto loginUserDto, @RequestBody MaintainOrderSubmitDto submitDto) {
        maintainOrderService.insert(loginUserDto, submitDto);
        return new AuditableCreateOrderResult(submitDto.getOrderDto().getId(), submitDto.getOrderDto().getOrderNo());
    }

    /**
     * 资产单据-用于导出
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/listForExport")
    public List<AsOrderDto> listForExport(@RequestBody AsOrderQueryDto dto) {
        return maintainOrderService.listForExport(dto);
    }

    @PostMapping("/assets")
    public List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids) {
        return maintainOrderService.getAssetsByOrderId(ids);
    }
}
