package com.niimbot.asset.maintenance.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.maintenance.mapper.AsMaintainPlanContentMapper;
import com.niimbot.asset.maintenance.model.AsMaintainPlanContent;
import com.niimbot.asset.maintenance.service.AsMaintainPlanContentService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 资产保养计划内容 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Service
public class AsMaintainPlanContentServiceImpl extends ServiceImpl<AsMaintainPlanContentMapper, AsMaintainPlanContent> implements AsMaintainPlanContentService {

}
