package com.niimbot.asset.maintenance.service.impl;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.maintenance.service.MaintainService;
import com.niimbot.asset.system.dto.clientobject.TagAttrCO;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/13 13:55
 */
@Service
@RequiredArgsConstructor
public class MaintainServiceImpl implements MaintainService {
    private final AsFormService formService;

    @Override
    public TagAttrListCO getAttrList(String kw) {
        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        List<TagAttrCO> list = Lists.newArrayList();

        // 企业信息
        list.add(new TagAttrCO().setAttrName("企业信息").setAlias("企业信息").setAttrType(AssetConstant.ED_SPLIT_LINE));
        list.add(new TagAttrCO().setAttrCode("companyName").setAttrName("企业名称").setAlias("企业名称"));
        list.add(new TagAttrCO().setAttrCode("customWords").setAttrName("自定义文案").setAlias("自定义文案").setIsCustomize(true).setIsShow(false));

        list.add(new TagAttrCO().setAttrName("耗材基础字段").setAlias("耗材基础字段").setAttrType(AssetConstant.ED_SPLIT_LINE));
        List<String> notShowAttrList = ImmutableList.of(AssetConstant.ED_SPLIT_LINE, AssetConstant.ED_FILES, AssetConstant.ED_IMAGES);
        for (FormFieldCO fieldCO : formFields) {
            if (notShowAttrList.contains(fieldCO.getFieldType())) {
                continue;
            }
            // 属性对象
            TagAttrCO tagAttrCO = new TagAttrCO();
            tagAttrCO.setAttrCode(fieldCO.getFieldCode());
            tagAttrCO.setAttrName(fieldCO.getFieldName());
            tagAttrCO.setAttrType(fieldCO.getFieldType());
            tagAttrCO.setAlias(fieldCO.getFieldName());
            list.add(tagAttrCO);
        }

        if (StrUtil.isNotBlank(kw)) {
            list = list.stream().filter(it -> it.getAlias().contains(kw)).collect(Collectors.toList());
        }

        return new TagAttrListCO().setFormId(formVO.getFormId()).setTagAttrs(list);
    }
}
