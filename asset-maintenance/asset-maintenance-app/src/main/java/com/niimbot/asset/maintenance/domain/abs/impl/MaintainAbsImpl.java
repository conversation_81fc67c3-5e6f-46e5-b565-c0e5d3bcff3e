package com.niimbot.asset.maintenance.domain.abs.impl;

import com.niimbot.asset.maintenance.service.MaintainService;
import com.niimbot.asset.system.abs.MaintainAbs;
import com.niimbot.asset.system.dto.TagAttrListQry;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/7/13 11:57
 */
@RestController
@RequestMapping("/client/abs/maintenance/maintainAbs/")
@RequiredArgsConstructor
public class MaintainAbsImpl implements MaintainAbs {
    private final MaintainService maintainService;

    @Override
    public TagAttrListCO getAttrList(TagAttrListQry qry) {
        return maintainService.getAttrList(qry.getKw());
    }
}
