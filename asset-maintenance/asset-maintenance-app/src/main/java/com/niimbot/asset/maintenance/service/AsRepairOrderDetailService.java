package com.niimbot.asset.maintenance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.maintenance.model.AsRepairOrderDetail;
import com.niimbot.means.AsOrderAssetDto;

import java.util.Collection;
import java.util.List;


/**
 * <p>
 * 资产维修单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
public interface AsRepairOrderDetailService extends IService<AsRepairOrderDetail> {

    /**
     * 通过单据id 获取详情数据
     *
     * @param ids 单据id
     * @return 资产详情
     */
    List<AsOrderAssetDto> getDetailsByOrderId(Collection<Long> ids);
}
