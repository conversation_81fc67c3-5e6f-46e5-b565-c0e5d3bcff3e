package com.niimbot.asset.maintenance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.OrderToStringConverter;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.maintenance.mapper.AsRepairReportOrderMapper;
import com.niimbot.asset.maintenance.model.AsRepairReportOrder;
import com.niimbot.asset.maintenance.model.AsRepairReportOrderDetail;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderDetailService;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.maintenance.RepairReportOrderDetailDto;
import com.niimbot.maintenance.RepairReportOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairReportOrderDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.page.SortQuery;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusUserOrgDto;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 资产报修单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Service
@RequiredArgsConstructor
public class AsRepairReportOrderServiceImpl extends ServiceImpl<AsRepairReportOrderMapper, AsRepairReportOrder> implements AsRepairReportOrderService {
    private final AsRepairReportOrderDetailService repairReportOrderDetailService;
    private final AsCusEmployeeService cusEmployeeService;
    private final MySqlRepairOrderQueryConditionResolver conditionResolver;
    private final AsFormService formService;
    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;

    @Override
    public Boolean create(RepairReportOrderDto dto, Boolean enableWorkflow) {
        // 设置id与编号
        if (StrUtil.isEmpty(dto.getOrderNo())) {
            dto.setOrderNo(StringUtils.getOrderNo("RR"));
        }
        // 补充报修组织
        Long sendRepairUser = dto.getOrderData().getLong("sendRepairUser");
        // 根据报修人获取报修组织
        CusEmployeeDto employeeDto = cusEmployeeService.getInfo(sendRepairUser);
        String orgText = employeeDto.getOrgList().stream().map(CusUserOrgDto::getOrgName).collect(Collectors.joining("，"));
        dto.getOrderData().put("repairReportOrgText", orgText);

        AsRepairReportOrder repairReportOrder = BeanUtil.copyProperties(dto, AsRepairReportOrder.class);

        if (enableWorkflow) {
            repairReportOrder.setApproveStatus(DictConstant.WAIT_APPROVE);
        }

        List<AsRepairReportOrderDetail> details = dto.getAssets().stream()
                .map(detailDto -> BeanUtil.copyProperties(detailDto, AsRepairReportOrderDetail.class)
                        .setRepairReportOrderId(dto.getId()))
                .collect(Collectors.toList());

        return this.save(repairReportOrder) && repairReportOrderDetailService.saveBatch(details);
    }

    @Override
    public RepairReportOrderDto getOrderDetail(Long id) {
        AsRepairReportOrder asRepairReportOrder = getById(id);
        if (asRepairReportOrder != null) {
            RepairReportOrderDto repairReportOrderDto = BeanUtil.copyProperties(asRepairReportOrder, RepairReportOrderDto.class);
            List<RepairReportOrderDetailDto> details = getDetailsById(id).stream()
                    .map(detail -> BeanUtil.copyProperties(detail, RepairReportOrderDetailDto.class))
                    .collect(Collectors.toList());
            repairReportOrderDto.setAssets(details);
            return repairReportOrderDto;
        } else {
            return null;
        }
    }

    @Override
    public AsRepairReportOrderDetail getDetail(Long orderId, Long assetId) {
        return repairReportOrderDetailService.getOne(
                Wrappers.<AsRepairReportOrderDetail>lambdaQuery()
                        .eq(AsRepairReportOrderDetail::getId, assetId)
                        .eq(AsRepairReportOrderDetail::getRepairReportOrderId, orderId));
    }

    @Override
    public List<AsRepairReportOrderDetail> getDetailsById(Long id) {
        return repairReportOrderDetailService.list(
                Wrappers.<AsRepairReportOrderDetail>lambdaQuery()
                        .eq(AsRepairReportOrderDetail::getRepairReportOrderId, id));
    }

    @Override
    public IPage<AsRepairReportOrder> page(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsRepairReportOrder> page = buildOrderSort(tableAlias, query);
        return this.getBaseMapper().orderPage(page, query, conditions);
    }

    @Override
    public IPage<AsRepairReportOrderDetail> pageDetail(RepairReportOrderDetailPageQueryDto query) {
        return repairReportOrderDetailService.page(query.buildIPage(),
                Wrappers.<AsRepairReportOrderDetail>lambdaQuery()
                        .eq(AsRepairReportOrderDetail::getRepairReportOrderId, query.getOrderId())
                        .orderByAsc(AsRepairReportOrderDetail::getId));
    }

    @Override
    public List<AsOrderDto> listForExport(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsRepairReportOrder> page = buildOrderSort(tableAlias, query);
        String orderByStr = OrderToStringConverter.orderByToString(page.getOrders());
        return this.getBaseMapper().listForExport(query, conditions, orderByStr);
    }

    @Override
    public QueryConditionSortDto sortField() {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.ASSET_ORDER_TYPE_HEAD.get(OrderFormTypeEnum.REPAIR_REPORT.getCode()));
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        QueryFieldConstant.Field approveStatusField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
        if (ObjectUtil.isNotNull(approveStatusField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(approveStatusField.getName(), approveStatusField.getCode(), approveStatusField.getType()));
        }

//        QueryFieldConstant.Field summaryField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(QueryFieldConstant.FIELD_SUMMARY_NAME.get(orderType), summaryField.getCode(), summaryField.getType()));

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.REPAIR_REPORT.getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

//        QueryFieldConstant.Field createByField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_BY);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(createByField.getName(), createByField.getCode(), createByField.getType()));

        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    public List<AsOrderAssetDto> getAssetsByOrderId(Collection<Long> ids) {
        return repairReportOrderDetailService.getDetailsByOrderId(ids);
    }

    /**
     * 通用排序处理
     *
     * @param tableAlias
     * @param queryDto
     * @return
     */
    private Page<AsRepairReportOrder> buildOrderSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_repair_report_order";
        }
        Page<AsRepairReportOrder> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = sortField();
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    @Override
    public AsRepairReportOrder tempGetById(Long orderId) {
        return this.getBaseMapper().tempGetById(orderId);
    }

    @Override
    public AsOrderInfoDto getApproveOrderByAssetId(Long assetId) {
        // 查询通用单据
        AsOrderInfoDto order = getBaseMapper().getOrderByAssetId(assetId);
        if (order != null) {
            order.setPerm(true);
            return order;
        }
        // 超管本身查询就是全部,所以如果非超管, 再多查询一次无权限的
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            order = getBaseMapper().getOrderByAssetIdNoPerm(assetId, LoginUserThreadLocal.getCompanyId());
            if (order != null) {
                order.setPerm(false);
                return order;
            }
        }
        return null;
    }
}
