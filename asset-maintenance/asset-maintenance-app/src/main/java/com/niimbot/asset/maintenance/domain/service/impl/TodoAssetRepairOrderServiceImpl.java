package com.niimbot.asset.maintenance.domain.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.maintenance.model.AsRepairOrder;
import com.niimbot.asset.maintenance.service.AsRepairOrderService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.TodoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/6 上午10:41
 */
@Slf4j
@Service
public class TodoAssetRepairOrderServiceImpl implements TodoOrderService {

    //当前service支持处理的单据类型
    private static final List<Integer> supportOrderTypeList = ListUtil.of(AssetConstant.ORDER_TYPE_REPAIR);

    @Autowired
    private AsRepairOrderService repairOrderService;

    @Override
    public Boolean supportOrderType(Integer orderType) {
        return supportOrderTypeList.contains(orderType);
    }

    @Override
    public JSONObject getOrderJson(Long businessId) {
        AsRepairOrder repairOrder = repairOrderService.getById(businessId);
        if (Objects.isNull(repairOrder)) {
            return null;
        }

        JSONObject result = JsonUtil.toJsonObject(repairOrder, o -> {
            JSONObject orderData = o.getJSONObject("orderData");
            o.fluentPutAll(orderData);
            o.fluentRemove("orderData");
        });
        return result;
    }

    @Override
    public AsTodo getOrderTodo(AsTodo todo) {
        AsRepairOrder repairOrder = repairOrderService.getById(todo.getBusinessId());

        if (Objects.isNull(repairOrder)) {
            return null;
        }

        //如果orderData为空，优先设置下
        if (Objects.isNull(repairOrder.getOrderData())) {
            repairOrder.setOrderData(new JSONObject());
        }

        repairOrder.getOrderData().put("summary", repairOrder.getSummary());
        repairOrder.getOrderData().put("totalRepairMoney", repairOrder.getTotalRepairMoney());
        AsTodo result = new AsTodo()
                .setSummary(repairOrder.getSummary())
                .setCreateBy(repairOrder.getCreateBy())
                .setOrderData(JSONObject.toJSONString(repairOrder.getOrderData()));
        return result;
    }
}
