package com.niimbot.asset.maintenance.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.MaintenanceResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.maintenance.mapper.AsMaintainPlanMapper;
import com.niimbot.asset.maintenance.model.AsMaintainPlan;
import com.niimbot.asset.maintenance.model.AsMaintainPlanContent;
import com.niimbot.asset.maintenance.service.AsMaintainPlanContentService;
import com.niimbot.asset.maintenance.service.AsMaintainPlanService;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainPlanContentDto;
import com.niimbot.maintenance.MaintainPlanDto;
import com.niimbot.maintenance.MaintainPlanEditBatchDto;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.maintenance.MaintainPlanListDto;
import com.niimbot.maintenance.MaintainPlanMessageDto;
import com.niimbot.maintenance.MaintainPlanQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 资产保养计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Service
public class AsMaintainPlanServiceImpl extends ServiceImpl<AsMaintainPlanMapper, AsMaintainPlan> implements AsMaintainPlanService {

    private final AsMaintainPlanContentService maintainPlanContentService;

    private final DataScopeStrategyManager strategyManager;

    @Autowired
    public AsMaintainPlanServiceImpl(AsMaintainPlanContentService maintainPlanContentService,
                                     DataScopeStrategyManager strategyManager) {
        this.maintainPlanContentService = maintainPlanContentService;
        this.strategyManager = strategyManager;
    }

    @Override
    public IPage<MaintainPlanListDto> planPage(MaintainPlanQueryDto queryDto) {
//        List<ModelDataScope> modelDataScopes = LoginUserThreadLocal.getModelDataScopes();
//        String whereSql = strategyManager.generalWhereSql(modelDataScopes, "com.niimbot.asset.means.mapper.AsAssetMapper", "ast");
        // 临时修改，老版只有租户权限
        IPage<MaintainPlanListDto> maintainPlanIPage = this.getBaseMapper().planPage(queryDto.buildIPage(), queryDto);
        List<MaintainPlanListDto> maintainPlanList = maintainPlanIPage.getRecords();
        // 批量补齐保养计划内容
        if (CollUtil.isNotEmpty(maintainPlanList)) {
            List<Long> maintainPlanIds = maintainPlanList.stream().map(MaintainPlanListDto::getId).collect(Collectors.toList());
            List<AsMaintainPlanContent> planContentList = maintainPlanContentService.list(Wrappers.lambdaQuery(AsMaintainPlanContent.class)
                    .in(AsMaintainPlanContent::getPlanId, maintainPlanIds));
            Map<Long, List<AsMaintainPlanContent>> planContentMap = planContentList.stream().collect(Collectors.groupingBy(AsMaintainPlanContent::getPlanId));
            for (MaintainPlanListDto listDto : maintainPlanList) {
                List<AsMaintainPlanContent> contents = planContentMap.get(listDto.getId());
                if (CollUtil.isNotEmpty(contents)) {
                    listDto.setPlanContentList(contents.stream().map(f -> new MaintainPlanContentDto()
                            .setId(f.getId()).setProject(f.getProject()).setRequirement(f.getRequirement())
                    ).collect(Collectors.toList()));
                }
            }
        }
        return maintainPlanIPage;
    }

    @Override
    public MaintainPlanInfoDto info(Long id) {
        return this.getBaseMapper().info(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(MaintainPlanDto optDto) {
        // 资产Id
        List<Long> assetIds = optDto.getAssetIds();
        // 查询资产是否有保养计划
        Long count = this.count(new QueryWrapper<AsMaintainPlan>().lambda().in(AsMaintainPlan::getAssetId, assetIds));
        if (count > 0) {
            throw new BusinessException(MaintenanceResultCode.MAINTAIN_ASSET_REF);
        }
        List<Long> planIds = new ArrayList<>();
        List<AsMaintainPlan> planList = assetIds.stream().map(assetId -> {
            AsMaintainPlan maintainPlan = BeanUtil.copyProperties(optDto, AsMaintainPlan.class);
            Long planId = IdUtils.getId();
            maintainPlan.setId(planId);
            maintainPlan.setAssetId(assetId);
            planIds.add(planId);
            return maintainPlan;
        }).collect(Collectors.toList());

        if (!this.saveBatch(planList)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        List<MaintainPlanContentDto> planContentList = optDto.getPlanContentList();
        List<AsMaintainPlanContent> contentList = new ArrayList<>();
        for (Long planId : planIds) {
            planContentList.forEach(content -> {
                AsMaintainPlanContent planContent = new AsMaintainPlanContent()
                        .setPlanId(planId).setProject(content.getProject())
                        .setRequirement(content.getRequirement());
                contentList.add(planContent);
            });
        }

        if (CollUtil.isNotEmpty(contentList) && !this.maintainPlanContentService.saveBatch(contentList)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(MaintainPlanDto optDto) {
        AsMaintainPlan maintainPlan = BeanUtil.copyProperties(optDto, AsMaintainPlan.class);
        maintainPlan.changeMaintainStatus();
        if (!this.updateById(maintainPlan)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        // 删除保养内容
        maintainPlanContentService.remove(new QueryWrapper<AsMaintainPlanContent>().lambda()
                .eq(AsMaintainPlanContent::getPlanId, optDto.getId()));
        List<MaintainPlanContentDto> planContentList = optDto.getPlanContentList();
        List<AsMaintainPlanContent> collect = planContentList.stream()
                .map(content -> {
                    AsMaintainPlanContent planContent = BeanUtil.copyProperties(content, AsMaintainPlanContent.class);
                    planContent.setPlanId(optDto.getId());
                    return planContent;
                }).collect(Collectors.toList());
        if (!maintainPlanContentService.saveBatch(collect)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @Override
    public Boolean editBatch(MaintainPlanEditBatchDto optDto) {
        List<Long> ids = optDto.getIds();
        List<AsMaintainPlan> planList = ids.stream().map(id -> {
            AsMaintainPlan maintainPlan = BeanUtil.copyProperties(optDto, AsMaintainPlan.class);
            maintainPlan.setId(id);
            maintainPlan.changeMaintainStatus();
            return maintainPlan;
        }).collect(Collectors.toList());
        if (!this.updateBatchById(planList)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        // 删除保养内容
        maintainPlanContentService.remove(new QueryWrapper<AsMaintainPlanContent>().lambda()
                .in(AsMaintainPlanContent::getPlanId, ids));
        List<MaintainPlanContentDto> planContentList = optDto.getPlanContentList();
        List<AsMaintainPlanContent> collect = new ArrayList<>();
        ids.forEach(planId -> {
            for (MaintainPlanContentDto maintainPlanContentDto : planContentList) {
                AsMaintainPlanContent planContent = BeanUtil.copyProperties(maintainPlanContentDto, AsMaintainPlanContent.class);
                planContent.setPlanId(planId);
                collect.add(planContent);
            }
        });
        if (!maintainPlanContentService.saveBatch(collect)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @Override
    public Boolean status(MaintainPlanDto optDto) {
        if (!this.update(new UpdateWrapper<AsMaintainPlan>().lambda()
                .set(AsMaintainPlan::getStatus, optDto.getStatus())
                .eq(AsMaintainPlan::getId, optDto.getId()))) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @Override
    public void handleMaintainPlanStatus() {
        this.getBaseMapper().handleMaintainPlanStatus();
    }

    @Override
    public List<MaintainPlanMessageDto> countMaintainPlan(Long companyId, String type, List<Integer> days) {
        return getBaseMapper().countMaintainPlan(companyId, type, days);
    }

}
