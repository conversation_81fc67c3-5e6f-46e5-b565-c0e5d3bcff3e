package com.niimbot.asset.maintenance.service.impl;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.system.QueryConditionDto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.StringJoiner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * MySQL资产查询条件处理器
 *
 * <AUTHOR>
 * @date 2021/12/7 15:42
 */
@Component
public class MySqlRepairOrderQueryConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {
    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.order_data ->> '$.%s'";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.order_data ->> '$.%s' as DECIMAL(20,4))";
    public static final String DATE_JSON_SQL_SEGMENT_TPL = "LPAD(%s.order_data ->> '$.%s', 13, 0)";

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        QueryFieldConstant.ORDER_COMMON_EXT_FIELD);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition) {
        if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampColumn(getSqlColumn(tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return getSqlColumn(tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        }
        return getJsonSqlColumn(tableAlias, condition.getCode());
    }

    /**
     * 获取json内字段
     */
    private String getJsonSqlColumn(String tableAlias, String code) {
        return StrUtil.format(JSON_COLUMN_SQL_TPL, ImmutableMap.of("tName", tableAlias, "tColumn", "order_data", "jsonField", code));
    }

    /**
     * 获取表内字段
     */
    private String getSqlColumn(String tableAlias, String code) {
        return StrUtil.format(COLUMN_SQL_TPL, ImmutableMap.of("tName", tableAlias, "tColumn", code));
    }

    private String unixTimestampColumn(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }
}
