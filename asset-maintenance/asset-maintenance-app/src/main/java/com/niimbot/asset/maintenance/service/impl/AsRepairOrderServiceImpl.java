package com.niimbot.asset.maintenance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableSet;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MaintenanceResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.OrderToStringConverter;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.maintenance.mapper.AsRepairOrderMapper;
import com.niimbot.asset.maintenance.model.AsRepairOrder;
import com.niimbot.asset.maintenance.model.AsRepairOrderDetail;
import com.niimbot.asset.maintenance.service.AsRepairOrderDetailService;
import com.niimbot.asset.maintenance.service.AsRepairOrderService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.RepairOrderDetailDto;
import com.niimbot.maintenance.RepairOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairOrderDto;
import com.niimbot.maintenance.RepairOrderFinishDto;
import com.niimbot.means.*;
import com.niimbot.page.SortQuery;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产维修单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Service
@RequiredArgsConstructor
public class AsRepairOrderServiceImpl extends ServiceImpl<AsRepairOrderMapper, AsRepairOrder> implements AsRepairOrderService {
    private final AsRepairOrderDetailService repairOrderDetailService;
    private final RedissonClient redissonClient;
    private final AsAssetLogService assetLogService;
    @Resource
    private AssetService assetService;
    private final MySqlRepairOrderQueryConditionResolver conditionResolver;
    private final AsFormService formService;
    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;

    private ImmutableSet<Short> allowed = ImmutableSet.<Short>builder()
            .add((short) 0)
            .add(DictConstant.APPROVED)
            .build();

    @Override
    public Boolean create(RepairOrderDto dto, Boolean enableWorkflow) {
        // 设置id与编号
        dto.setId(IdUtils.getId()).setOrderNo(StringUtils.getOrderNo("RO"));
        AsRepairOrder repairOrder = BeanUtil.copyProperties(dto, AsRepairOrder.class);

        if (enableWorkflow) {
            repairOrder.setApproveStatus(DictConstant.WAIT_APPROVE);
        }
        String repairStatus = repairOrder.getOrderData().getString(AsRepairOrder.REPAIR_STATUS);
        List<AsRepairOrderDetail> details = dto.getAssets().stream()
                .map(detailDto -> BeanUtil.copyProperties(detailDto, AsRepairOrderDetail.class)
                        .setRepairOrderId(dto.getId()).setRepairStatus(repairStatus)
                        .setFinishTime(Objects.nonNull(detailDto.getFinishTimestamp()) ? LocalDateTime.ofInstant(Instant.ofEpochMilli(detailDto.getFinishTimestamp()), ZoneId.systemDefault()) : null)
                )
                .collect(Collectors.toList());
        return this.save(repairOrder) && repairOrderDetailService.saveBatch(details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean repairFinish(RepairOrderFinishDto dto) {
        AsRepairOrderDetail orderDetail = getDetail(dto.getRepairOrderId(), dto.getAssetId());
        if (orderDetail == null) {
            throw new BusinessException(MaintenanceResultCode.REPAIR_ORDER_DETAIL_NOT_EXIST);
        }
        orderDetail.setFinishTime(dto.getFinishTime());
        RLock rLock = redissonClient.getLock(
                RedisConstant.repairOrderLockKey(orderDetail.getRepairOrderId()));
        if (rLock.isLocked()) {
            throw new BusinessException(MaintenanceResultCode.REPAIR_ORDER_LOCKED);
        }
        // 加锁
        rLock.lock();
        try {
            AsRepairOrder repairOrder = this.getById(orderDetail.getRepairOrderId());
            if (!allowed.contains(repairOrder.getApproveStatus())) {
                throw new BusinessException(MaintenanceResultCode.WORKFLOW_STATUS_ILLEGAL);
            }
            if (DictConstant.REPAIR_STATUS_FINISH.equals(repairOrder.getOrderData().getString(AsRepairOrder.REPAIR_STATUS))
                    || DictConstant.REPAIR_STATUS_FINISH.equals(orderDetail.getRepairStatus())) {
                throw new BusinessException(MaintenanceResultCode.REPAIR_STATUS_ILLEGAL);
            }
            // 维修完成时间不能晚于维修开始时间
            if (repairOrder.getOrderData().containsKey("repairFinishDate") && Objects.nonNull(dto.getFinishTime())) {
                Long repairFinishDate = repairOrder.getOrderData().getLong("repairFinishDate");
                LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(repairFinishDate), ZoneId.systemDefault());
                Affirm.isTrue(localDateTime.isBefore(dto.getFinishTime()), "维修完成时间不能晚于维修开始时间");
            }
            // 处理附件
            if (CollUtil.isNotEmpty(dto.getAppendix())) {
                FormVO form = formService.getTplByType(OrderFormTypeEnum.REPAIR.getBizType());
                form.getFormFields().stream().filter(v -> "appendix".equals(v.getFieldCode())).findFirst().ifPresent(v -> {
                    if (!v.getFieldProps().containsKey("max")) {
                        return;
                    }
                    List<CommonFileDto> appendixFiles = new ArrayList<>(dto.getAppendix());
                    if (Objects.nonNull(repairOrder.getOrderData()) && repairOrder.getOrderData().containsKey("appendix")) {
                        appendixFiles.addAll(repairOrder.getOrderData().getJSONArray("appendix").toJavaList(CommonFileDto.class));
                    }
                    Integer max = v.getFieldProps().getInteger("max");
                    if (appendixFiles.size() > max) {
                        throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "上传的附件数量大于" + max + "个");
                    }
                    repairOrder.getOrderData().put("appendix", appendixFiles);
                });
            }

            // 处理业务数据
            // 1. 处理单据备注
            if (StrUtil.isNotBlank(dto.getRemark())) {
                repairOrder.getOrderData().put(AsRepairOrder.REMARK, dto.getRemark());
            }
            // 2. 处理单据明细维修金额
            if (ObjectUtil.isNotNull(dto.getRepairMoney())) {
                orderDetail.setRepairMoney(dto.getRepairMoney());
            }

            List<AsRepairOrderDetail> details = getDetailsById(orderDetail.getRepairOrderId());
            long repairingCount = details.stream()
                    .filter(detail -> DictConstant.REPAIR_STATUS_REPAIRING.equals(detail.getRepairStatus()))
                    .count();

            // 3. 处理单据状态
            if (repairingCount <= 1) {
                repairOrder.getOrderData().put(AsRepairOrder.REPAIR_STATUS, DictConstant.REPAIR_STATUS_FINISH);
            }
            orderDetail.setRepairStatus(DictConstant.REPAIR_STATUS_FINISH);

            // 4. 处理单据总金额
            List<AsRepairOrderDetail> resolveTotalList = details.stream()
                    .filter(detail -> !orderDetail.getId().equals(detail.getId())).collect(Collectors.toList());
            resolveTotalList.add(orderDetail);
            repairOrder.setTotalRepairMoney(this.resolveTotalRepairMoney(resolveTotalList));

            // 写入资产履历
            AsAssetLog asAssetLog = new AsAssetLog()
                    .setAssetId(dto.getAssetId())
                    .setActionType(AssetConstant.ORDER_TYPE_REPAIR)
                    .setOrderNo(repairOrder.getOrderNo())
                    .setOrderId(repairOrder.getId())
                    .setActionName("维修")
                    .setActionContent(getActionContent(dto.getRepairMoney(), dto.getRemark()));
            String handleTimeKey = AssetConstant.ORDER_ASSET_LOG_DATE.get(AssetConstant.ORDER_TYPE_REPAIR);
            Long handleTime = repairOrder.getOrderData().getLong(handleTimeKey);
            // 更新处理时间
            try {
                LocalDateTime dateTime = LocalDateTime.ofEpochSecond(handleTime / 1000, 0, ZoneOffset.of("+8"));
                asAssetLog.setHandleTime(dateTime);
            } catch (Exception ignored) {
            }
            return this.updateById(repairOrder) &&
                    repairOrderDetailService.update(orderDetail,
                            Wrappers.<AsRepairOrderDetail>lambdaUpdate()
                                    .eq(AsRepairOrderDetail::getId, orderDetail.getId())
                                    .eq(AsRepairOrderDetail::getRepairOrderId, orderDetail.getRepairOrderId())) &&
                    assetLogService.save(asAssetLog);
        } finally {
            rLock.unlock();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void directRepairFinish(RepairOrderDto dto) {
        List<AsAssetLog> logs = new ArrayList<>();
        List<Long> assetIds = new ArrayList<>();

        dto.getAssets().forEach(detail -> {
            assetIds.add(detail.getId());
            String remark = dto.getOrderData().getString(AsRepairOrder.REMARK);
            AsAssetLog asAssetLog = new AsAssetLog()
                    .setAssetId(detail.getId())
                    .setActionType(AssetConstant.ORDER_TYPE_REPAIR)
                    .setOrderNo(dto.getOrderNo())
                    .setOrderId(dto.getId())
                    .setActionName("维修")
                    .setActionContent(getActionContent(detail.getRepairMoney(), remark));
            String handleTimeKey = AssetConstant.ORDER_ASSET_LOG_DATE.get(AssetConstant.ORDER_TYPE_REPAIR);
            Long handleTime = dto.getOrderData().getLong(handleTimeKey);
            // 更新处理时间
            try {
                LocalDateTime dateTime = LocalDateTime.ofEpochSecond(handleTime / 1000, 0, ZoneOffset.of("+8"));
                asAssetLog.setHandleTime(dateTime);
            } catch (Exception ignored) {
            }
            logs.add(asAssetLog);
        });
        if (DictConstant.REPAIR_STATUS_REPAIRING.equals(dto.getOrderData().getString(AsRepairOrder.REPAIR_STATUS))) {
            assetService.updateBatchStatus(assetIds, asset -> {
                asset.setRepairBeforeStatus(asset.getBeforeStatus());
                asset.setBeforeStatus(asset.getStatus());
                asset.setStatus(AssetConstant.ASSET_STATUS_SERVICE);
            });
        } else {
            assetService.updateBatchStatus(assetIds, asset -> {
                if (AssetConstant.ASSET_STATUS_WAIT_SERVICE.equals(asset.getStatus())) {
                    asset.setStatus(asset.getBeforeStatus());
                }
                asset.setBeforeStatus(0);
            });
            assetLogService.saveBatch(logs);
        }
    }

    @Override
    public RepairOrderDto getOrderDetail(Long id) {
        AsRepairOrder asRepairOrder = getById(id);
        if (asRepairOrder != null) {
            RepairOrderDto repairOrder = BeanUtil.copyProperties(asRepairOrder, RepairOrderDto.class);
            List<RepairOrderDetailDto> details = getDetailsById(id).stream()
                    .map(detail -> BeanUtil.copyProperties(detail, RepairOrderDetailDto.class))
                    .collect(Collectors.toList());
            repairOrder.setAssets(details);
            return repairOrder;
        } else {
            return null;
        }
    }

    @Override
    public AsRepairOrderDetail getDetail(Long orderId, Long assetId) {
        return repairOrderDetailService.getOne(
                Wrappers.<AsRepairOrderDetail>lambdaQuery()
                        .eq(AsRepairOrderDetail::getId, assetId)
                        .eq(AsRepairOrderDetail::getRepairOrderId, orderId));
    }

    @Override
    public List<AsRepairOrderDetail> getDetailsById(Long id) {
        return repairOrderDetailService.list(
                Wrappers.<AsRepairOrderDetail>lambdaQuery()
                        .eq(AsRepairOrderDetail::getRepairOrderId, id));
    }

    @Override
    public IPage<AsRepairOrder> page(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsRepairOrder> page = buildOrderSort(tableAlias, query);
        return this.getBaseMapper().page(page, query, conditions);
    }

    @Override
    public IPage<AsRepairOrderDetail> pageDetail(RepairOrderDetailPageQueryDto query) {
        return repairOrderDetailService.page(query.buildIPage(),
                Wrappers.<AsRepairOrderDetail>lambdaQuery()
                        .eq(AsRepairOrderDetail::getRepairOrderId, query.getOrderId())
                        .orderByAsc(AsRepairOrderDetail::getId));
    }

    private BigDecimal resolveTotalRepairMoney(List<AsRepairOrderDetail> details) {
        return details.stream()
                .map(detail ->
                        detail.getRepairMoney() == null ? BigDecimal.ZERO : detail.getRepairMoney())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private String getActionContent(BigDecimal repairMoney, String remark) {
        return String.format("维修花费：%.2f元；维修备注：%s",
                Optional.ofNullable(repairMoney).orElse(BigDecimal.ZERO),
                StrUtil.isNotBlank(remark) ? remark : "----");
    }

    @Override
    public List<AsOrderDto> listForExport(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsRepairOrder> page = buildOrderSort(tableAlias, query);
        String orderByStr = OrderToStringConverter.orderByToString(page.getOrders());
        return this.getBaseMapper().listForExport(query, conditions, orderByStr);
    }

    @Override
    public QueryConditionSortDto sortField() {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.ASSET_ORDER_TYPE_HEAD.get(OrderFormTypeEnum.REPAIR.getCode()));
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        QueryFieldConstant.Field approveStatusField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
        if (ObjectUtil.isNotNull(approveStatusField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(approveStatusField.getName(), approveStatusField.getCode(), approveStatusField.getType()));
        }

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.REPAIR.getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    public List<AsOrderAssetDto> getAssetsByOrderId(Collection<Long> ids) {
        return repairOrderDetailService.getDetailsByOrderId(ids);
    }

    /**
     * 通用排序处理
     *
     * @param tableAlias
     * @param queryDto
     * @return
     */
    private Page<AsRepairOrder> buildOrderSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_repair_order";
        }
        Page<AsRepairOrder> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = sortField();
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlRepairOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    @Override
    public AsRepairOrder tempGetById(Long orderId) {
        return this.getBaseMapper().tempGetById(orderId);
    }

    @Override
    public List<RepairOrderDetailDto> repairFinishAsset(List<Long> assetIdList) {
        if (CollUtil.isEmpty(assetIdList)) {
            return Collections.emptyList();
        }

        List<AsAsset> assetList = assetService.list(Wrappers.lambdaQuery(AsAsset.class).select(AsAsset::getAssetCode).in(AsAsset::getId, assetIdList));
        if (CollUtil.isEmpty(assetList)) {
            return Collections.emptyList();
        }

        List<String> assetCodeList = assetList.stream().map(AsAsset::getAssetCode).collect(Collectors.toList());
        if (CollUtil.isEmpty(assetCodeList)) {
            return Collections.emptyList();
        }

        return this.getBaseMapper().selectFinishAsset(assetCodeList, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public AsOrderInfoDto getApproveOrderByAssetId(Long assetId) {
        // 查询通用单据
        AsOrderInfoDto order = getBaseMapper().getOrderByAssetId(assetId);
        if (order != null) {
            order.setPerm(true);
            return order;
        }
        // 超管本身查询就是全部,所以如果非超管, 再多查询一次无权限的
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            order = getBaseMapper().getOrderByAssetIdNoPerm(assetId, LoginUserThreadLocal.getCompanyId());
            if (order != null) {
                order.setPerm(false);
                return order;
            }
        }
        return null;
    }
}
