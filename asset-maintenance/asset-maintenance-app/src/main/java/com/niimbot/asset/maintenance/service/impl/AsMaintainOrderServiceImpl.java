package com.niimbot.asset.maintenance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.activiti.WorkflowExecuteStepDto;
import com.niimbot.activiti.WorkflowStartDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.model.ActWorkflowBusiness;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActWorkflowBusinessService;
import com.niimbot.asset.activiti.service.ActWorkflowCallbackErrorService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.OrderToStringConverter;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.utils.WorkflowUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.maintenance.mapper.AsMaintainOrderMapper;
import com.niimbot.asset.maintenance.model.AsMaintainOrder;
import com.niimbot.asset.maintenance.model.AsMaintainPlan;
import com.niimbot.asset.maintenance.model.AsMaintainPlanContent;
import com.niimbot.asset.maintenance.service.AsMaintainOrderService;
import com.niimbot.asset.maintenance.service.AsMaintainPlanContentService;
import com.niimbot.asset.maintenance.service.AsMaintainPlanService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.model.AsOrderType;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AsOrderService;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainOrderDto;
import com.niimbot.maintenance.MaintainOrderPlanContentDto;
import com.niimbot.maintenance.MaintainOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.page.SortQuery;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产保养单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Service
public class AsMaintainOrderServiceImpl extends ServiceImpl<AsMaintainOrderMapper, AsMaintainOrder> implements AsMaintainOrderService {

    private static final String MAINTAIN_CONTENT = "maintainContent";

    private final AssetService assetService;

    private final AsMaintainPlanContentService maintainPlanContentService;

    private final AsMaintainPlanService maintainPlanService;

    private final AsAssetLogService assetLogService;

    @Resource
    private AsOrderService orderService;

    private final AsOrderTypeService orderTypeService;
    private final ActWorkflowService workflowService;
    private final AsCusEmployeeService cusEmployeeService;
    @Resource
    private RedisService redisService;
    @Resource
    private ActWorkflowBusinessService workflowBusinessService;
    @Resource
    private AsFormService formService;
    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;
    @Resource
    private MySqlMaintainOrderQueryConditionResolver conditionResolver;
    @Resource
    private ActWorkflowCallbackErrorService callbackErrorService;
    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Autowired
    public AsMaintainOrderServiceImpl(AssetService assetService,
                                      AsMaintainPlanContentService maintainPlanContentService,
                                      AsMaintainPlanService maintainPlanService,
                                      AsAssetLogService assetLogService,
                                      AsOrderService orderService,
                                      AsOrderTypeService orderTypeService,
                                      ActWorkflowService workflowService,
                                      AsCusEmployeeService cusEmployeeService) {
        this.assetService = assetService;
        this.maintainPlanContentService = maintainPlanContentService;
        this.maintainPlanService = maintainPlanService;
        this.assetLogService = assetLogService;
        this.orderService = orderService;
        this.orderTypeService = orderTypeService;
        this.workflowService = workflowService;
        this.cusEmployeeService = cusEmployeeService;
    }

    @Override
    public IPage<AsMaintainOrder> orderPage(AsOrderQueryDto queryDto) {
        String tableAlias = "o";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        Page<Object> page = buildOrderSort(tableAlias, queryDto);
        return this.getBaseMapper().orderPage(page, queryDto, conditions);
    }

    @Override
    public WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, MaintainOrderDto orderDto) {
        WorkflowExecuteDto result = new WorkflowExecuteDto();
        String uuid = UUID.fastUUID().toString();
        result.setConditionId(uuid);
        // 查询单据类型
        AsOrderType orderType = orderTypeService.getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsOrderType::getType, orderDto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), loginUserDto.getCusUser().getOrgId(), false);
        if (workflow != null) {
            JSONObject orderData = orderDto.getOrderData();
            Long assetId = orderDto.getAssetId();

            JSONObject condition = new JSONObject();
            AsAsset asAsset = assetService.getById(assetId);
            JSONObject assetData = asAsset.getAssetData();
            String assetCategory = assetData.getString("assetCategory");
            if (StrUtil.isNotEmpty(assetCategory)) {
                condition.put(QueryFieldConstant.ASSET_CATEGORY, ListUtil.of(assetCategory));
            }
            String storageArea = assetData.getString("storageArea");
            if (StrUtil.isNotEmpty(storageArea)) {
                condition.put(QueryFieldConstant.ASSET_AREA, ListUtil.of(storageArea));
            }
            String orgOwner = assetData.getString("orgOwner");
            if (StrUtil.isNotEmpty(orgOwner)) {
                condition.put(QueryFieldConstant.ASSET_ORG_OWNER, ListUtil.of(orgOwner));
            }
            String price = assetData.getString("price");
            if (StrUtil.isNotEmpty(price)) {
                BigDecimal priceNum = new BigDecimal(price);
                condition.put(QueryFieldConstant.ASSET_TOTAL_MONEY, priceNum);
            } else {
                condition.put(QueryFieldConstant.ASSET_TOTAL_MONEY, BigDecimal.ZERO);
            }
            Long manageOwner = assetData.getLong("managerOwner");
            if (manageOwner != null) {
                orderData.put(QueryFieldConstant.ASSET_MANAGER_OWNER, ListUtil.of(manageOwner));
            }
            condition.put(QueryFieldConstant.SUBMITTER_ORG, CollUtil.union(ListUtil.of(LoginUserThreadLocal.getCurrentUserId(), orderDto.getOrgId()), approveRoleMemberService.getEmpRoleIds(LoginUserThreadLocal.getCurrentUserId())));
            orderData.putAll(condition);

            String redisKey = RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.hSetAll(redisKey, condition);
            redisService.expire(redisKey, 2, TimeUnit.HOURS);
            result.setWorkflowExecuteStepDto(workflowService.preStartWorkflow(loginUserDto, workflow, orderData, null));
            String stepKey = RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.set(stepKey, result.getWorkflowExecuteStepDto());
            redisService.expire(stepKey, 2, TimeUnit.HOURS);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(LoginUserDto loginUserDto, MaintainOrderSubmitDto submitDto) {
        MaintainOrderDto orderDto = submitDto.getOrderDto();
        loginUserDto.getCusUser().setOrgId(orderDto.getOrgId());

        orderService.verify(orderDto);
        orderService.translation(orderDto);
        Long assetId = orderDto.getAssetId();
        AsAsset asset = assetService.getById(assetId);
        if (ObjectUtil.isNull(asset)) {
            throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
        }
        // 查询单据类型
        AsOrderType orderType = orderTypeService.getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsOrderType::getType, orderDto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), loginUserDto.getCusUser().getOrgId(), false);

        AsMaintainOrder order = BeanUtil.copyProperties(orderDto, AsMaintainOrder.class);
        // 写入保养基础数据
        order.setId(IdUtils.getId()).setOrderNo(StringUtils.getOrderNo("MR"))
                .setAssetName(asset.getAssetName()).setAssetCode(asset.getAssetCode());
        orderDto.setId(order.getId()).setOrderNo(order.getOrderNo());
        // 写入保养内容
        List<MaintainOrderPlanContentDto> maintainContent = orderDto.getMaintainContent();
        if (CollUtil.isNotEmpty(maintainContent)) {
            Map<Long, MaintainOrderPlanContentDto> contentMap = maintainContent.stream().collect(Collectors.toMap(
                    MaintainOrderPlanContentDto::getId, k -> k, (k1, k2) -> k1));
            Set<Long> contentIds = contentMap.keySet();
            List<AsMaintainPlanContent> planContents = maintainPlanContentService.listByIds(contentIds);
            planContents.forEach(content -> {
                MaintainOrderPlanContentDto contentDto = contentMap.get(content.getId());
                contentDto.setProject(content.getProject());
                contentDto.setRequirement(content.getRequirement());
            });
        }
        if (CollUtil.isNotEmpty(orderDto.getMaintainContent())) {
            order.getOrderData().put(MAINTAIN_CONTENT, orderDto.getMaintainContent());
        }
        if (workflow != null) {
            order.setApproveStatus(DictConstant.WAIT_APPROVE);
        }

        if (!this.save(order)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        // 流程开启, 走流程审批
        if (workflow != null) {
            if (submitDto.getConditionId() == null) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_COMMON_ERROR, "流程执行条件Id不能为空");
            }
            if (submitDto.getExecuteStepDtoList().isEmpty()) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_EXECUTE_STEP_IS_EMPTY);
            }

            // 写入审批人
            List<WorkflowExecuteStepDto> executeStepList = Convert.toList(WorkflowExecuteStepDto.class, redisService.get(
                    RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), submitDto.getConditionId())));
            Map<Long, List<WorkflowExecuteStepDto.WorkflowApprover>> submitExecuteStepList = submitDto.getExecuteStepDtoList().stream()
                    .collect(Collectors.toMap(WorkflowExecuteStepDto::getStepId, WorkflowExecuteStepDto::getApproverList, (k1, k2) -> k1));
            for (WorkflowExecuteStepDto stepDto : executeStepList) {
                if (submitExecuteStepList.containsKey(stepDto.getStepId())) {
                    stepDto.setApproverList(submitExecuteStepList.get(stepDto.getStepId()));
                }
            }

            AsCusEmployee employee = cusEmployeeService.getById(loginUserDto.getCusUser().getId());
            String userName = employee == null ? loginUserDto.getCusUser().getAccount() : employee.getEmpName();

            JSONObject orderData = orderDto.getOrderData();
            Map<Object, Object> map = redisService.hGetAll(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), submitDto.getConditionId()));
            if (map != null) {
                Map<String, Object> conditionMap = Convert.toMap(String.class, Object.class, map);
                orderData.putAll(conditionMap);
            }
            workflowService.startWorkflow(
                    new WorkflowStartDto()
                            .setUserDto(loginUserDto.getCusUser())
                            .setTitle(getTitle(userName))
                            .setActivitiKey(orderType.getActivitiKey())
                            .setWorkflowId(workflow.getId())
                            .setVersion(workflow.getVersion())
                            .setCallbackUrl(workflow.getCallbackUrl())
                            .setBusinessType(orderType.getType().shortValue())
                            .setBusinessId(order.getId())
                            .setFormData(orderData)
                            .setExecuteStepDtoList(executeStepList));
            redisService.del(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), submitDto.getConditionId()));
            redisService.del(RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), submitDto.getConditionId()));
        } else {
            // 更新计划状态和下次保养时间
            AsMaintainPlan plan = maintainPlanService.getOne(new QueryWrapper<AsMaintainPlan>().lambda().eq(AsMaintainPlan::getAssetId, assetId), false);
            if (ObjectUtil.isNotNull(plan)) {
                plan.setMaintainStatus((short) 1);
                try {
                    Long nextMaintainDate = order.getOrderData().getLong("nextMaintainDate");
                    LocalDateTime dateTime = LocalDateTime.ofEpochSecond(nextMaintainDate / 1000, 0, ZoneOffset.of("+8"));
                    plan.setNextTime(dateTime.toLocalDate());
                    if (!this.maintainPlanService.updateById(plan)) {
                        throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
                    }
                } catch (Exception ignored) {
                }
            }

            AsAssetLog asAssetLog = new AsAssetLog()
                    .setAssetId(assetId)
                    .setActionType(AssetConstant.ORDER_TYPE_MAINTAIN)
                    .setOrderNo(order.getOrderNo())
                    .setOrderId(order.getId())
                    .setActionName("保养")
                    .setActionContent(getActionContent(order));
            // 写入资产履历
            try {
                String handleTimeKey = AssetConstant.ORDER_ASSET_LOG_DATE.get(AssetConstant.ORDER_TYPE_MAINTAIN);
                Long currentMaintainDate = order.getOrderData().getLong(handleTimeKey);
                LocalDateTime dateTime = LocalDateTime.ofEpochSecond(currentMaintainDate / 1000, 0, ZoneOffset.of("+8"));
                asAssetLog.setHandleTime(dateTime);
            } catch (Exception ignored) {
            }
            this.assetLogService.save(asAssetLog);
        }
        return true;
    }

    private String getTitle(String userName) {
        return String.format("%s提交的保养单", userName);
    }

    @Override
    public JSONObject getAssetData(Long orderId) {
        AsMaintainOrder one = this.getById(orderId);
        if (ObjectUtil.isNull(one)) {
            return null;
        } else {
            return one.getAssetSnapshotData();
        }
    }

    @Override
    public MaintainOrderDto getInfo(Long id) {
        return this.getBaseMapper().getInfo(id);
    }

    private String getActionContent(AsMaintainOrder order) {
        StringBuffer str = new StringBuffer();
        JSONObject orderData = order.getOrderData();
        Long currentMaintainDate = orderData.getLong("currentMaintainDate");
        try {
            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(currentMaintainDate / 1000, 0, ZoneOffset.of("+8"));
            str.append("保养时间：").append(dateTime.toLocalDate());
        } catch (Exception e) {
            str.append("保养时间:----");
        }
        List<MaintainOrderPlanContentDto> maintainContentList = orderData.getJSONArray(MAINTAIN_CONTENT).toJavaList(MaintainOrderPlanContentDto.class);
        if (CollUtil.isNotEmpty(maintainContentList)) {
            for (int i = 0; i < maintainContentList.size(); i++) {
                MaintainOrderPlanContentDto planContentDto = maintainContentList.get(i);
                str.append("；");
                str.append("保养项目").append(i + 1).append("：");
                str.append(planContentDto.getProject());
                if (ObjectUtil.isNotEmpty(planContentDto.getIsNormal())) {
                    str.append("-").append(planContentDto.getIsNormal() == 1 ? "正常" : "异常");
                } else {
                    str.append("-").append("未执行");
                }
                if (ObjectUtil.isNotEmpty(planContentDto.getRemark())) {
                    str.append("-").append(planContentDto.getRemark());
                }
            }
        } else {
            str.append("；");
        }
        return str.toString();
    }

    @Override
    public List<AsOrderDto> listForExport(AsOrderQueryDto query) {
        String tableAlias = "o";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<Object> page = buildOrderSort(tableAlias, query);
        String orderByStr = OrderToStringConverter.orderByToString(page.getOrders());
        return this.getBaseMapper().listForExport(query, conditions, orderByStr);
    }

    @Override
    public QueryConditionSortDto sortField() {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.ASSET_ORDER_TYPE_HEAD.get(OrderFormTypeEnum.MAINTAIN.getCode()));
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        QueryFieldConstant.Field approveStatusField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
        if (ObjectUtil.isNotNull(approveStatusField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(approveStatusField.getName(), approveStatusField.getCode(), approveStatusField.getType()));
        }

//        QueryFieldConstant.Field summaryField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(QueryFieldConstant.FIELD_SUMMARY_NAME.get(orderType), summaryField.getCode(), summaryField.getType()));

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.MAINTAIN.getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

//        QueryFieldConstant.Field createByField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_BY);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(createByField.getName(), createByField.getCode(), createByField.getType()));

        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    public List<AsOrderAssetDto> getAssetsByOrderId(Collection<Long> ids) {
        List<AsMaintainOrder> asMaintainOrders = this.listByIds(ids);
        List<AsOrderAssetDto> assets = new ArrayList<>();
        for (AsMaintainOrder order : asMaintainOrders) {
            AsOrderAssetDto asset = new AsOrderAssetDto();
            asset.setOrderId(order.getId());
            asset.setId(order.getAssetId());
            asset.setAssetSnapshotData(order.getAssetSnapshotData());
            assets.add(asset);
        }
        return assets;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processCallback(WorkflowCallbackDto callbackDto) {
        AsMaintainOrder maintainOrder = this.getById(Long.parseLong(callbackDto.getBusinessId()));
        if (maintainOrder == null) {
            return false;
        }
        callbackErrorService.remove(callbackDto);
        if (WorkflowUtil.finishWorkflowCallbackAllowed().contains(maintainOrder.getApproveStatus())) {
            return true;
        }
        short status = Short.parseShort(callbackDto.getApproveStatus());
        if (!WorkflowUtil.finishWorkflowCallbackAllowed().contains(status)) {
            return false;
        }
        // threadLocal写入公司Id
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(new CusUserDto()
                .setId(maintainOrder.getCreateBy())
                .setIsAdmin(true)
                .setCompanyId(maintainOrder.getCompanyId()));
        LoginUserThreadLocal.set(userDto);
        try {
            Optional<ActWorkflowBusiness> workflowBusiness = Optional.ofNullable(workflowBusinessService.getById(callbackDto.getProcessInstanceId()));
            workflowBusiness.ifPresent(v -> v.setApproveStatus(status));
            maintainOrder.setApproveStatus(status);
            workflowBusiness.ifPresent(workflowBusinessService::updateById);
            this.updateById(maintainOrder);

            if (DictConstant.APPROVED == status) {
                // 更新计划状态和下次保养时间
                AsMaintainPlan plan = maintainPlanService.getOne(new QueryWrapper<AsMaintainPlan>().lambda().eq(AsMaintainPlan::getAssetId, maintainOrder.getAssetId()), false);
                if (ObjectUtil.isNotNull(plan)) {
                    plan.setMaintainStatus((short) 1);
                    try {
                        Long nextMaintainDate = maintainOrder.getOrderData().getLong("nextMaintainDate");
                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(nextMaintainDate / 1000, 0, ZoneOffset.of("+8"));
                        plan.setNextTime(dateTime.toLocalDate());
                        if (!this.maintainPlanService.updateById(plan)) {
                            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
                        }
                    } catch (Exception ignored) {
                    }
                }

                AsAssetLog asAssetLog = new AsAssetLog()
                        .setAssetId(maintainOrder.getAssetId())
                        .setActionType(AssetConstant.ORDER_TYPE_MAINTAIN)
                        .setOrderNo(maintainOrder.getOrderNo())
                        .setOrderId(maintainOrder.getId())
                        .setActionName("保养")
                        .setActionContent(getActionContent(maintainOrder));
                // 写入资产履历
                try {
                    String handleTimeKey = AssetConstant.ORDER_ASSET_LOG_DATE.get(AssetConstant.ORDER_TYPE_MAINTAIN);
                    Long currentMaintainDate = maintainOrder.getOrderData().getLong(handleTimeKey);
                    LocalDateTime dateTime = LocalDateTime.ofEpochSecond(currentMaintainDate / 1000, 0, ZoneOffset.of("+8"));
                    asAssetLog.setHandleTime(dateTime);
                } catch (Exception ignored) {
                }
                this.assetLogService.save(asAssetLog);
            }

        } finally {
            LoginUserThreadLocal.remove();
        }
        return true;
    }

    /**
     * 通用排序处理
     *
     * @param tableAlias
     * @param queryDto
     * @return
     */
    private Page<Object> buildOrderSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_maintain_order";
        }
        Page<Object> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = sortField();
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(MySqlMaintainOrderQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlMaintainOrderQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlMaintainOrderQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlMaintainOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    @Override
    public AsMaintainOrder tempGetById(Long orderId) {
        return this.getBaseMapper().tempGetById(orderId);
    }
}
