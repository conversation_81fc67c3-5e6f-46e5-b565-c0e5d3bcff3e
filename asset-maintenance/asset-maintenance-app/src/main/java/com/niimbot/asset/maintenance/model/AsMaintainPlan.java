package com.niimbot.asset.maintenance.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资产保养计划
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsMaintainPlan对象", description = "资产保养计划")
@TableName(value = "as_maintain_plan", autoResultMap = true)
public class AsMaintainPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "资产ID")
    private Long assetId;

    @ApiModelProperty(value = "负责人id")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<Long> principalUserId;

    @ApiModelProperty(value = "下次保养时间")
    private LocalDate nextTime;

    @ApiModelProperty(value = "保养频次")
    private Integer frequency;

    @ApiModelProperty(value = "保养频次单位")
    private String frequencyUnit;

    @ApiModelProperty(value = "保养等级")
    private String level;

    @ApiModelProperty(value = "保养供应商")
    private String supplier;

    @ApiModelProperty(value = "保养联系人")
    private String contactPerson;

    @ApiModelProperty(value = "保养联系方式")
    private String contactDetails;

    @ApiModelProperty(value = "状态 1:正常  2:禁用 ")
    private Short status;

    @ApiModelProperty(value = "保养状态 1-正常 2-待保养")
    private Short maintainStatus;

    @ApiModelProperty(value = "是否软删除 0-否 1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 下次保养时间大于等于当前时间，保养状态为【正常】，否则为【待保养】；
     */
    public void changeMaintainStatus() {
        if (Objects.isNull(nextTime)) {
            return;
        }
        if (nextTime.isAfter(LocalDate.now())) {
            this.maintainStatus = 1;
        }
    }


}
