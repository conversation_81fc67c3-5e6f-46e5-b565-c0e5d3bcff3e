package com.niimbot.asset.maintenance.schedule;

import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.maintenance.service.AsMaintainPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * 维修保养模块调度器
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Scheduler {

    private final AsMaintainPlanService maintainPlanService;

    private final RedisDistributeLock redisDistributeLock;

    /**
     * 每天晚上12点执行 更新保养状态
     */
    @Scheduled(cron = " 1 0 0 * * ? ")
    @Transactional(rollbackFor = Exception.class)
    public void handleMaintainPlanStatus() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        redisDistributeLock.lock("maintainPlanStatus", 3, TimeUnit.MINUTES, (a) -> maintainPlanService.handleMaintainPlanStatus());
    }

}
