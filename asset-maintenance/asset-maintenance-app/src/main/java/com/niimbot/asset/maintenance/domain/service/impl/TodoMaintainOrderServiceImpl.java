package com.niimbot.asset.maintenance.domain.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.maintenance.model.AsMaintainOrder;
import com.niimbot.asset.maintenance.service.AsMaintainOrderService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.TodoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/6 下午1:49
 */
@Slf4j
@Service
public class TodoMaintainOrderServiceImpl implements TodoOrderService {

    private static final List<Integer> supportTypeList = ListUtil.of(AssetConstant.ORDER_TYPE_MAINTAIN);

    @Autowired
    private AsMaintainOrderService maintainOrderService;

    @Override
    public Boolean supportOrderType(Integer orderType) {
        return supportTypeList.contains(orderType);
    }

    @Override
    public JSONObject getOrderJson(Long businessId) {
        AsMaintainOrder maintainOrder = maintainOrderService.getById(businessId);
        if (Objects.isNull(maintainOrder)) {
            return null;
        }

        JSONObject result = JsonUtil.toJsonObject(maintainOrder, o -> {
            JSONObject orderData = o.getJSONObject("orderData");
            o.fluentPutAll(orderData);
            o.fluentRemove("orderData");
        });
        return result;
    }

    @Override
    public AsTodo getOrderTodo(AsTodo todo) {
        AsMaintainOrder maintainOrder = maintainOrderService.getById(todo.getBusinessId());
        if (Objects.isNull(maintainOrder)) {
            return null;
        }

        //如果orderData为空，优先设置下
        if (Objects.isNull(maintainOrder.getOrderData())) {
            maintainOrder.setOrderData(new JSONObject());
        }

        maintainOrder.getOrderData().put("summary", maintainOrder.getAssetName());
        AsTodo result = new AsTodo()
                .setSummary(maintainOrder.getAssetName())
                .setCreateBy(maintainOrder.getCreateBy())
                .setOrderData(JSONObject.toJSONString(maintainOrder.getOrderData()));
        return result;
    }
}
