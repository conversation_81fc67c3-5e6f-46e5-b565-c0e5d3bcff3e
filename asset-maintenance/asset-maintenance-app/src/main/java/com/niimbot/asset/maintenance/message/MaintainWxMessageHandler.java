package com.niimbot.asset.maintenance.message;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.maintenance.model.AsRepairOrderDetail;
import com.niimbot.asset.maintenance.model.AsRepairReportOrder;
import com.niimbot.asset.maintenance.model.AsRepairReportOrderDetail;
import com.niimbot.asset.maintenance.service.AsRepairOrderDetailService;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderDetailService;
import com.niimbot.asset.maintenance.service.AsRepairReportOrderService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.InstantCompanyMessageHandler;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 维修完成消息
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MaintainWxMessageHandler extends InstantCompanyMessageHandler {

    private final AsRepairOrderDetailService repairOrderDetailService;
    private final AsRepairReportOrderService repairReportOrderService;
    private final AsRepairReportOrderDetailService repairReportOrderDetailService;

    @Override
    public String code() {
        return MessageConstant.Code.WXWCTX.getCode();
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        String status = "完成";
        String statusTextKey = "statusText";
        String statusTextVal = "待维修";
        Long orderId = params.getId();
        List<Long> meansIds = params.getIds();
        // 资产维修单详情 -- 资产快照状态为待维修 -- 单据维修状态为维修完成
        List<AsRepairOrderDetail> details = repairOrderDetailService.list(
                        Wrappers.lambdaQuery(AsRepairOrderDetail.class)
                                .in(AsRepairOrderDetail::getId, meansIds)
                                .eq(AsRepairOrderDetail::getRepairOrderId, orderId)
                )
                .stream()
                .filter(v -> CollUtil.isNotEmpty(v.getAssetSnapshotData()) && v.getAssetSnapshotData().containsKey(statusTextKey) && v.getAssetSnapshotData().getString(statusTextKey).contains(statusTextVal))
                .filter(v -> StrUtil.isNotBlank(v.getRepairStatus()) && v.getRepairStatus().contains(status))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(details)) {
            return;
        }
        meansIds = details.stream().map(AsRepairOrderDetail::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(meansIds)) {
            return;
        }
        // 资产对应的报修单 -- EG: 10条资产至多能找到10条保修单，至少能找到0条保修单
        Set<Long> rrIds = new HashSet<>(meansIds.size());
        meansIds.forEach(v -> {
            List<Long> tempIds = repairReportOrderDetailService.list(Wrappers.lambdaQuery(AsRepairReportOrderDetail.class).select(AsRepairReportOrderDetail::getRepairReportOrderId).eq(AsRepairReportOrderDetail::getId, v))
                    .stream().map(AsRepairReportOrderDetail::getRepairReportOrderId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(tempIds)) {
                return;
            }
            List<Long> reportOrders = repairReportOrderService.list(
                    Wrappers.lambdaQuery(AsRepairReportOrder.class)
                            .select(AsRepairReportOrder::getId)
                            .in(AsRepairReportOrder::getId, tempIds)
                            .orderByDesc(AsRepairReportOrder::getCreateTime)
                            .last("LIMIT 1")
            ).stream().map(AsRepairReportOrder::getId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(reportOrders)) {
                return;
            }
            rrIds.add(reportOrders.get(0));
        });

        if (CollUtil.isEmpty(rrIds)) {
            return;
        }
        List<AsRepairReportOrder> repairReportOrders = repairReportOrderService.list(
                Wrappers.lambdaQuery(AsRepairReportOrder.class)
                        .in(AsRepairReportOrder::getId, rrIds)
                        .eq(AsRepairReportOrder::getCompanyId, rule.getCompanyId())
                        .orderByDesc(AsRepairReportOrder::getCreateTime)
        );
        if (CollUtil.isEmpty(repairReportOrders)) {
            return;
        }
        // 保修单据维度发送消息
        String userKey = "sendRepairUser";
        repairReportOrders.forEach(v -> {
            JSONObject orderData = v.getOrderData();
            if (!orderData.containsKey(userKey)) {
                return;
            }
            Long userId = Convert.toLong(orderData.getString(userKey));
            Map<String, Object> extMap = new HashMap<>(2);
            extMap.put("orderType", "6");
            extMap.put("docId", String.valueOf(v.getId()));
            Map<String, String> conMap = new HashMap<>(2);
            conMap.put(MessageConstant.Template.URL, pcDomain + "/#/asset-repairs/repair-order-detail?orderType=6&docId=" + v.getId());
            bodies.add(
                    Body.builder()
                            .userIds(Collections.singleton(userId))
                            .appMapParamNeedEmpty(true)
                            .appExtMapParam(Convert.toMap(String.class, String.class, extMap))
                            .commonExtMap(extMap)
                            .mapParam(conMap)
                            .build()
            );
        });

    }

}
