package com.niimbot.asset.todo.extend.behaviour.event;

import com.niimbot.asset.system.event.DomainEvent;
import com.niimbot.asset.todo.dto.clientobject.NotifyTodoContextCO;
import com.niimbot.asset.todo.dto.clientobject.NotifyTodoSendCO;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/22 11:13 上午
 * 拓展待办已更新事件
 */
public class TodoUpdatedExtendEvent implements DomainEvent {

    private final NotifyTodoContextCO notifyTodoContextCO;

    private final Date occurredOn;

    public TodoUpdatedExtendEvent(NotifyTodoSendCO notifyTodoSendCO, Date occurredOn) {
        this.notifyTodoContextCO = notifyTodoSendCO;
        this.occurredOn = new Date(occurredOn.getTime());
    }

    @Override
    public Object getContext() {
        return this.notifyTodoContextCO;
    }

    @Override
    public Date occurredOn() {
        return new Date(occurredOn.getTime());
    }

}
