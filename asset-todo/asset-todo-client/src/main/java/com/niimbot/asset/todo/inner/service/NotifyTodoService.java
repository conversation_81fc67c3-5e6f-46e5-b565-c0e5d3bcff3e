package com.niimbot.asset.todo.inner.service;

import com.niimbot.asset.todo.dto.clientobject.NotifyTodoSendCO;
import com.niimbot.asset.todo.dto.clientobject.NotifyTodoAppResult;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2022/5/22 1:02 下午
 */

public interface NotifyTodoService {

    /**
     * 添加一条待办
     *
     * @param notifyTodoSendCO
     * @return
     */
    @PostMapping("addTodo")
    NotifyTodoAppResult sendTodo(NotifyTodoSendCO notifyTodoSendCO);


}
