package com.niimbot.asset.todo.dto.clientobject;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:39 下午
 * 待办操作反馈信息
 */
@Data
public class NotifyTodoAppResult{
    /**
     * 返回状态
     * 说明:
     * 0未操作,1失败,2成功
     */
    private int returnState = 2;

    /**
     * 返回有关信息
     * 说明:
     * 返回状态值为0时，该值返回空。
     * 返回状态值为1时，该值错误信息。
     * 返回状态值为2时，增加,删除,设置已办时返回空。
     * 获取待办信息时,返回待办列表的数据格式为JSON,格式说明:
     * {
     * "rowSize":100,                 	    //总页数页
     * "pageNo":37,                     	//当前页码
     * "count":37001,                   	//总数
     * "data":[							    //列表
     * {
     *          "subject":"请处理：XXXX",	//标题        		不为空
     *          "type":"1",                 //待办类型           不为空
     *          "key":"reviewMain",         //待办关键字         可为空
     *          "param1":"",                //参数1              可为空
     *          "param2":"",                //参数2             	可为空
     *          "modelName":"",             //模型名称           可为空
     *          "modelId":"",               //模型id             不为空
     *          "optTime":"",               //创建时间           不为空
     *          "link":{
     *              "appUrl":"",
     *              "pcUrl":""
     *          },                           //全路径,不为空
     *          "targets":[]                 //组织架构json数组,格式详见发送待办targets数据格式说明.                                                 可为空
     * },
     * ……
     * ]
     * }
     */
    private String message;


}
