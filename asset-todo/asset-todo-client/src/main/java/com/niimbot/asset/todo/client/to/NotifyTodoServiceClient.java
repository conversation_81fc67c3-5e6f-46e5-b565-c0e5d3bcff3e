package com.niimbot.asset.todo.client.to;

import com.niimbot.asset.todo.inner.service.NotifyTodoService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/22 1:11 下午
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.todo.inner.service.NotifyTodoServiceImpl")
@FeignClient(name = "asset-todo", url = "https://{gateway}/client/domain/todo/NotifyTodoService/")
public interface NotifyTodoServiceClient extends NotifyTodoService {

}
