package com.niimbot.asset.todo.client.to;

import com.niimbot.asset.todo.inner.service.TodoService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.todo.inner.service.TodoServiceImpl")
@FeignClient(name = "asset-todo", url = "https://{gateway}/client/domain/todo/service/")
public interface TodoServiceClient extends TodoService {
}
