package com.niimbot.asset.todo.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.todo.model.AsTodo;

/**
 * <AUTHOR>
 * @date 2023/9/5 下午1:53
 */
public interface TodoOrderService {

    /**
     * 优先级
     * @return
     */
    default Integer priority() {
        return 99;
    }

    /**
     * 是否支持当前单据类型
     * @param orderType
     * @return
     */
    Boolean supportOrderType(Integer orderType);

    /**
     * 获取当前单据信息
     * @param businessId
     * @return
     */
    JSONObject getOrderJson(Long businessId);

    /**
     * 获取当前单据信息
     * @param todo
     * @return
     */
    AsTodo getOrderTodo(AsTodo todo);
}
