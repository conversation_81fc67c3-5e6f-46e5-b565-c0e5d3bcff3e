package com.niimbot.asset.todo.dto.clientobject;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:39 下午
 * 置为已办或者删除待办CO
 */

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NotifyTodoRemoveCO implements Serializable {

    /**
     * 待办ID
     */
    private String id;

    /**
     * 待办来源
     * 说明:
     * 不允许为空
     */
    private String appName;

    /**
     * 所属模块
     * 说明:
     * 不允许为空
     */
    private String modelName;

    /**
     * 待办对应主文档的唯一标识
     * 说明:
     * 不允许为空
     */
    private String modelId;


    /**
     * 操作类型
     * 说明:
     * 不允许为空
     * 删除时,
     * 1:表示删除待办操作
     * 2:表示删除指定待办所属人操作
     * 设为已办时,
     * 1:表示设待办为已办操作
     * 2:表示设置目标待办所属人为已办操作
     *
     */
    private int optType;


    /**
     * 关键字
     * 说明:
     * 待办关键字,用于区分同一文档下不同类型待办,
     * 如：会议文档的抄送待办和与会人参加待办属于同一文档的不同类型的待办。
     * 可为空
     */
    private String key;

    /**
     * 参数1,2
     * 说明:
     * 待办附加标识。功能同”关键字”，辅助区分不同类型的待办
     * 可为空
     */
    private String param1;


    private String param2;

    /**
     * 待办类型
     * 说明: 不允许为空
     * 1为审批类待办
     * 2为通知类待办
     */
    private int type;

    /**
     * 待办发送目标
     * 说明:可为空
     * 多值格式为: [{key:类型1,value:值1},{key:类型2:value:值2}... ,]
     * 如: [{"key":"loginName","value":"001"} ,{"key":"keyword","value":"2EDF7"}]。
     * 类型说明:
     * key:id         组织架构唯一表示
     * key:loginName  组织架构登录名
     * key:personNo   组织架构个人编号
     * key:deptNo     组织架构部门编号
     * key:type       类型 user:普通用户、system:系统租户
     * key:keyword    关键字
     * key:ldapDN     和LDAP集成时LDAP中DN值
     */
    private List<NotifyTodoKV> targets;



}
