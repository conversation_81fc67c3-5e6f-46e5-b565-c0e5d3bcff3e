package com.niimbot.asset.todo.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.todo.TodoCompleteMessageDto;
import com.niimbot.todo.TodoCreateMessageDto;
import com.niimbot.todo.TodoDeleteMessageDto;
import com.niimbot.todo.TodoPageQueryDto;

import java.util.List;

/**
 * <p>
 * 待办事项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
public interface AsTodoService extends IService<AsTodo> {

    IPage<AsTodo> pageMy(TodoPageQueryDto query, Long userId);

    /**
     * 创建待办任务
     *
     * @param dto
     * @return
     */
    Boolean createTodo(TodoCreateMessageDto dto);

    /**
     * 创建待办任务
     * @param todo
     * @return
     */
    Boolean createTodo(AsTodo todo);

    /**
     * 创建待办任务, 不走消息中间件
     *
     * @param dto
     * @return
     */
    Boolean doCreateTodo(TodoCreateMessageDto dto);

    /**
     * 完成待办任务
     *
     * @param dto
     * @return
     */
    Boolean completeTodo(TodoCompleteMessageDto dto);

    /**
     * 完成待办任务, 不走消息中间件
     *
     * @param dto
     * @return
     */
    Boolean doCompleteTodo(TodoCompleteMessageDto dto);

    /**
     * 删除待办任务
     *
     * @param dto
     * @return
     */
    Boolean deleteTodo(TodoDeleteMessageDto dto);

    /**
     * 删除待办任务, 不走消息中间件
     *
     * @param dto
     * @return
     */
    Boolean doDeleteTodo(TodoDeleteMessageDto dto);

    List<AsTodo> getWaitCompleteTodo(TodoCompleteMessageDto dto);

    /**
     * 根据审批流程，获取单据信息
     * @param orderType
     * @param businessId
     * @return
     */
    JSONObject orderData(Integer orderType, Long businessId);
}
