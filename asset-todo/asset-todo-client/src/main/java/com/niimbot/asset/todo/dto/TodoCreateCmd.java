package com.niimbot.asset.todo.dto;

import com.niimbot.asset.system.dto.CommonCommand;
import com.niimbot.asset.todo.dto.clientobject.TodoCo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TodoCreateCmd extends CommonCommand {

    public static final String ONLY = "only";

    public static final String ALL = "all";

    private TodoCo todoCo;

    private boolean async = true;

}
