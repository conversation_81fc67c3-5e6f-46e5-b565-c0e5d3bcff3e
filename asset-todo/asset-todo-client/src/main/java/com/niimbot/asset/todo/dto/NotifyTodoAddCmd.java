package com.niimbot.asset.todo.dto;

import com.niimbot.asset.system.dto.CommonCommand;
import com.niimbot.asset.todo.dto.clientobject.NotifyTodoSendCO;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/5/23 9:46 上午
 * 新增待办
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class NotifyTodoAddCmd extends CommonCommand {

    @NotNull
    private NotifyTodoSendCO notifyTodoSendContextCO;

}
