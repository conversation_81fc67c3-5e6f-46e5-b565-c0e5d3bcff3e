package com.niimbot.asset.todo.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TodoCo extends ClientObject {

    /**
     * Id
     */
    private Long id;

    /**
     * 公司ID（租户ID）
     */
    private Long companyId;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 待办类型1:工作节点待办 2:我申请的 3:抄送我的
     */
    private Integer bizType;

    /**
     * 业务类型(对应单据类型)：1：领用，2：退还，3：借用，4：归还，5：调拨，6：报修，
     * 7：维修，8：处置，9：变更, 10: 采购, 11: 保养, 33:耗材领用, 96: 盘点审核,
     * 97: 保养计划, 98: 上报, 99: 盘点
     */
    private Short orderType;

    /**
     * 标题
     */
    private String title;

    /**
     * 单据数据
     */
    private String orderData;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 是否已处理
     */
    private Boolean isHandle;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
