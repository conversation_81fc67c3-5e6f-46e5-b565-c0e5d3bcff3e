package com.niimbot.asset.todo.dto.clientobject;
/**
 * <AUTHOR>
 * @date 2022/5/11 10:39 下午
 */

import java.io.Serializable;
import java.util.List;

import lombok.Data;

@Data
public class NotifyTodoContextCO implements Serializable {

    //必填索引 #######
    /**
     * 待办来源 说明: 待办ID不存在时不允许为空
     */
    private String appName;
    /**
     * 所属模块
     * 说明: 待办ID不存在时不允许为空
     */
    private String modelName;
    /**
     * 待办对应主文档的唯一标识
     * 说明: 待办ID不存在时不允许为空
     */
    private String modelId;
    //必填索引 ####### end


    //辅助索引 #######
    /**
     * 待办关键字,用于区分同一文档下不同类型待办
     * 说明:可为空
     * 如：会议文档的抄送待办和与会人参加待办属于同一文档的不同类型的待办。
     */
    private String key;
    /**
     * 参数1,2
     * 说明: 待办附加标识。
     * 功能同”关键字”，辅助区分不同类型的待办 可为空
     */
    private String param1;

    private String param2;
    //辅助索引 ####### end

    /**
     * 待办类型
     * 1:为审批类待办
     * 2:为通知类待办
     * 说明: 不允许为空
     */
    private Integer type;

    /**
     * 待办标题
     * 说明: 不允许为空
     */
    private String subject;
    /**
     * 链接地址(全路径)
     * 说明: 不允许为空
     */
    private NotifyTodoLink detailUrl;

    /**
     * 创建时间 格式:yyyy-mm-dd HH:mm:ss
     * 说明: 不允许为空
     */
    private String createTime;

    /**
     * 截止时间 格式:yyyy-mm-dd HH:mm:ss
     * 说明:可为空
     */
    private String dueTime;

    /**
     * 待办发送目标
     * 说明:可为空
     * 多值格式为: [{key:类型1,value:值1},{key:类型2:value:值2}... ,]
     * 如: [{"key":"loginName","value":"001"} ,{"key":"keyword","value":"2EDF7"}]。
     * 类型说明:
     * key:id         组织架构唯一表示
     * key:loginName  组织架构登录名
     * key:personNo   组织架构个人编号
     * key:deptNo     组织架构部门编号
     * key:type       类型 user:普通用户、system:系统租户
     * key:keyword    关键字
     * key:ldapDN     和LDAP集成时LDAP中DN值
     */
    private List<NotifyTodoKV> targets;

    /**
     * 待办创建者
     * 说明：可为空
     * 单值格式为: {key:类型,value:值}
     * 如:{"key":"loginName","value":"001"}。
     * 类型说明:
     * key:id         组织架构唯一表示
     * key:loginName  组织架构登录名
     * key:personNo   组织架构个人编号
     * key:deptNo     组织架构部门编号
     * key:type       类型 user:普通用户、system:系统租户
     * key:keyword    关键字
     * key:ldapDN     和LDAP集成时LDAP中DN值
     */
    private NotifyTodoKV docCreator;


    /**
     * 待办优先级
     * 说明:可为空
     * 如：按紧急、急、一般,
     * 1:紧急
     * 2:急
     * 3:一般
     */
    private Integer level;
    /**
     * 消息内容扩展,JSON字符串
     * 说明:可为空
     * 格式为: [{key:类型1,value:值1},{key:类型2:value:值2}... ,]
     * 如: [{"key":"loginName","value":"001"} ,{"key":"keyword","value":"2EDF7"}]。
     */
    private List<NotifyTodoKV> extendContent;


}
