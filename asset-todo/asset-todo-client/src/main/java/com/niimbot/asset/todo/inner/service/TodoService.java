package com.niimbot.asset.todo.inner.service;

import com.niimbot.asset.todo.dto.TodoCompleteCmd;
import com.niimbot.asset.todo.dto.TodoCreateCmd;
import com.niimbot.asset.todo.dto.TodoDeleteCmd;
import com.niimbot.asset.todo.dto.TodoDoDeleteCmd;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TodoService {

    @PostMapping("createTodo")
    Boolean createTodo(@RequestBody TodoCreateCmd cmd);

    @PostMapping("completeTodo")
    Boolean completeTodo(@RequestBody TodoCompleteCmd cmd);

    @PostMapping("completeTodoByIds")
    Boolean completeTodoByIds(List<Long> ids);

    @DeleteMapping("deleteTodo")
    Boolean deleteTodo(@RequestBody TodoDeleteCmd cmd);

    @DeleteMapping("doDeleteTodo")
    Boolean doDeleteTodo(@RequestBody TodoDoDeleteCmd cmd);

    @GetMapping("getWaitCompleteTodoIds")
    List<Long> getWaitCompleteTodoIds(TodoCompleteCmd cmd);
}
