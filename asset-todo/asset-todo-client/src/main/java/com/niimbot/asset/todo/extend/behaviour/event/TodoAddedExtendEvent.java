package com.niimbot.asset.todo.extend.behaviour.event;

import com.niimbot.asset.system.event.DomainEvent;
import com.niimbot.asset.todo.dto.clientobject.NotifyTodoSendCO;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/22 11:13 上午
 * 拓展待办已新增事件
 */
public class TodoAddedExtendEvent implements DomainEvent {

    private final NotifyTodoSendCO notifyTodoSendCO;

    private final Date occurredOn;

    public TodoAddedExtendEvent(NotifyTodoSendCO notifyTodoSendCO, Date occurredOn) {
        this.notifyTodoSendCO = notifyTodoSendCO;
        this.occurredOn = new Date(occurredOn.getTime());
    }

    @Override
    public Object getContext() {
        return this.notifyTodoSendCO;
    }

    @Override
    public Date occurredOn() {
        return new Date(occurredOn.getTime());
    }
}
