package com.niimbot.asset.todo.abs;

import com.niimbot.asset.todo.dto.MeansOrderFieldGetQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MeansOrderAbs {

    /**
     * orderFieldService.list( Wrappers.<AsOrderField>lambdaQuery() .eq(AsOrderField::getCompanyId,
     * dto.getCompanyId()) .eq(AsOrderField::getOrderType, dto.getOrderType())
     * .in(AsOrderField::getCode, codes))
     */
    @GetMapping("listOrderFiled")
    List<FormFieldCO> listOrderFiled(@SpringQueryMap MeansOrderFieldGetQry qry);

}
