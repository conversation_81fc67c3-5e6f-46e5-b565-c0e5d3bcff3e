package com.niimbot.asset.todo.dto;

import com.niimbot.asset.system.dto.CommonCommand;
import com.niimbot.asset.todo.dto.clientobject.TodoCo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TodoCompleteCmd extends CommonCommand {
    public static final String ONLY = "only";

    public static final String ALL = "all";

    @ApiModelProperty(value = "完成类型: only, all")
    private String completeType = ONLY;

    private TodoCo todoCo;
}
