package com.niimbot.asset.todo.dto;

import com.niimbot.asset.system.dto.CommonCommand;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class MeansOrderFieldGetQry extends CommonCommand {

    private Long companyId;

    private Short orderType;

    private List<String> codes;

}
