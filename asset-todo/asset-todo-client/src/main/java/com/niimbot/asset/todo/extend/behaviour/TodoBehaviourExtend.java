package com.niimbot.asset.todo.extend.behaviour;

import com.niimbot.asset.todo.dto.clientobject.NotifyTodoRemoveCO;
import com.niimbot.asset.todo.dto.clientobject.NotifyTodoSendCO;
import com.niimbot.asset.todo.dto.clientobject.NotifyTodoUpdateCO;

/**
 * <AUTHOR>
 * @date 2022/5/22 11:13 上午
 * 待办行为拓展实现接口
 */
public interface TodoBehaviourExtend {

    void addTodo(NotifyTodoSendCO notifyTodoSendContextCO);


    void removeTodo(NotifyTodoRemoveCO notifyTodoRemoveContextCO);


    void updateTodo(NotifyTodoUpdateCO notifyTodoUpdateContextCO);




}
