package com.niimbot.asset.todo.abs;

import com.niimbot.asset.todo.dto.MaterialOrderFieldGetQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TodoMaterialOrderFieldAbs {

    @GetMapping("listOrderFiled")
    List<FormFieldCO> listOrderFiled(@SpringQueryMap MaterialOrderFieldGetQry qry);

}
