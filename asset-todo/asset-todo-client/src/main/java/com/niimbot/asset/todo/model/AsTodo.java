package com.niimbot.asset.todo.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 待办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsTodo对象", description="待办事项")
@TableName(value = "as_todo", autoResultMap = true)
public class AsTodo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "业务ID")
    private Long businessId;

    @ApiModelProperty(value = "子业务ID")
    private Long subBusinessId;

    @ApiModelProperty(value = "待办类型1:工作节点待办 2:我申请的 3:抄送我的")
    private Integer bizType;

    @ApiModelProperty(value = "业务类型(对应单据类型)：1：领用，2：退还，3：借用，4：归还，5：调拨，6：报修，" +
            "7：维修，8：处置，9：变更, 10: 采购, 11: 保养, 33:耗材领用, 96: 盘点审核, 97: 保养计划, 98: 上报, 99: 盘点")
    private Short orderType;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "单据数据")
    private String orderData;

    @ApiModelProperty(value = "单据摘要")
    private String summary;

    @ApiModelProperty(value = "扩展外部参数")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject outParam;

    @ApiModelProperty(value = "扩展内部参数")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject innerParam;

    @ApiModelProperty(value = "处理人")
    private Long handleUser;

    @ApiModelProperty(value = "是否已处理")
    private Boolean isHandle;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
