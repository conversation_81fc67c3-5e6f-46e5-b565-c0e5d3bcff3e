package com.niimbot.asset.inventory.adapter.local.mapper;

import com.niimbot.asset.inventory.model.AsInventoryAsset;
import com.niimbot.ding.InventoryAssetAreaQueryDto;
import com.niimbot.ding.InventoryAssetRFIDSubmitDto;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventorySurplusQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LocalInventoryMapper {

    /**
     * 区域盘点进度
     *
     * @param queryDto
     * @return
     */
    int storageAreaAsset(@Param("param") InventoryAssetAreaQueryDto queryDto);

    /**
     * 根据epcId查询属于当前盘点任务的资产id列表
     *
     * @param request
     * @return
     */
    List<Long> selectAssetIdByEpcId(@Param("param") InventoryAssetRFIDSubmitDto request);

    /**
     * 更改资产盘点状态
     *
     * @param request
     * @return
     */
    int updateCheckedStatus(@Param("param") InventoryAssetRFIDSubmitDto request);

    /**
     * 更改盘点任务资产盘点状态
     *
     * @param request
     * @return
     */
    int updateTaskAssetStatus(@Param("param") InventoryAssetRFIDSubmitDto request);

    /**
     * 根据epcId列表获取资产id列表
     *
     * @param companyId
     * @param epcIdList
     * @return
     */
    List<Long> selectAssetId(@Param("companyId") Long companyId, @Param("epcIdList") List<String> epcIdList);

    /**
     * 根据盘点任务id，修改已盘数量
     *
     * @param inventoryTaskId
     * @param checkedNum
     * @return
     */
    int updateCheckedNumById(@Param("inventoryTaskId") Long inventoryTaskId, @Param("checkedNum") Integer checkedNum);

    /**
     * 根据盘点单id和盘点任务id，查询盘盈在册资产id
     *
     * @param inventoryId
     * @return
     */
    List<Long> selectAssetIdByTaskId(@Param("inventoryId") Long inventoryId);

    AsInventoryAsset appCheckAssetId(@Param("assetId") String assetId,
                                     @Param("inventoryId") Long inventoryId,
                                     @Param("inventoryTaskId") Long inventoryTaskId,
                                     @Param("uniqueCodes") List<String> uniqueCodes);

    /**
     * 资产盘点，每个分类的数据数量
     *
     * @param dto
     * @param conditions
     * @return
     */
    List<InventoryAssetDataCountDto> selectAppAssetAll(@Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);

}
