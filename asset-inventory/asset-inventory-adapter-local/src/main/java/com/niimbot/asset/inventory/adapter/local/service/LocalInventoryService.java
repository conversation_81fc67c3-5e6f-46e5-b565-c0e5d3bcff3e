package com.niimbot.asset.inventory.adapter.local.service;

import com.niimbot.ding.InventoryAssetAreaQueryDto;
import com.niimbot.ding.InventoryAssetAreaStatisticsDto;
import com.niimbot.ding.InventoryAssetRFIDResultDto;
import com.niimbot.ding.InventoryAssetRFIDSubmitDto;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventoryManualBatchDto;
import com.niimbot.inventory.InventoryRemarkBatchDto;
import com.niimbot.inventory.InventorySurplusQueryDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LocalInventoryService {


    /**
     * 当前区域盘点进度
     *
     * @param queryDto
     * @return
     */
    InventoryAssetAreaStatisticsDto storageAreaInventoryAsset(InventoryAssetAreaQueryDto queryDto);

    /**
     * RFID盘点
     *
     * @param request
     * @return
     */
    InventoryAssetRFIDResultDto inventoryAssetRFID(InventoryAssetRFIDSubmitDto request);

    /**
     * 根据epcId列表获取已存在的资产id列表
     *
     * @param companyId
     * @param epcIdList
     * @return
     */
    List<Long> queryAssetIdByEpcId(Long companyId, List<String> epcIdList);

    /**
     * 修改已盘数量
     *
     * @param inventoryTaskId
     * @param checkedNum
     * @return
     */
    Integer updateCheckedNumById(Long inventoryTaskId, Integer checkedNum);


    /**
     * 查询资产id
     *
     * @param inventoryId 盘点单id
     * @return
     */
    List<Long> queryAssetByInventoryId(Long inventoryId);

    long appCheckAssetId(String assetId, Long inventoryId, Long inventoryTaskId);

    /**
     * app盘点资产，查询对应分类code值
     *
     * @param dto
     * @return
     */
    List<InventoryAssetDataCountDto> selectAppAssetCode(InventorySurplusQueryDto dto);

    Boolean manualInventoryBatch(InventoryManualBatchDto dto);

    Boolean updateRemarkBatch(InventoryRemarkBatchDto dto);


}
