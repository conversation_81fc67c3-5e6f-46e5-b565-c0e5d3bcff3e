package com.niimbot.asset.inventory.adapter.local.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.inventory.adapter.local.service.LocalInventoryService;
import com.niimbot.asset.inventory.adapter.local.service.LocalInventoryTaskService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.ding.DingInventoryQueryDto;
import com.niimbot.ding.DingInventoryTaskListDto;
import com.niimbot.ding.InventoryAssetAreaQueryDto;
import com.niimbot.ding.InventoryAssetAreaStatisticsDto;
import com.niimbot.ding.InventoryAssetRFIDResultDto;
import com.niimbot.ding.InventoryAssetRFIDSubmitDto;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventoryManualBatchDto;
import com.niimbot.inventory.InventoryManualDto;
import com.niimbot.inventory.InventoryRemarkBatchDto;
import com.niimbot.inventory.InventorySurplusQueryDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("server/local/inventory")
@RequiredArgsConstructor
public class LocalInventoryServiceController {

    private final LocalInventoryService localInventoryService;
    private final LocalInventoryTaskService localInventoryTaskService;
    private final AsInventoryTaskService inventoryTaskService;

    @PostMapping(value = "/asset/manualInventory/batch")
    public Boolean manualInventoryBatch(@RequestBody InventoryManualBatchDto dto) {
        return localInventoryService.manualInventoryBatch(dto);
    }

    @PostMapping(value = "/asset/updateRemark/batch")
    public Boolean updateRemarkBatch(@RequestBody InventoryRemarkBatchDto dto) {
        return localInventoryService.updateRemarkBatch(dto);
    }

    /**
     * 当前区域盘点进度统计-小程序使用
     *
     * @param queryDto
     * @return
     */
    @GetMapping("/asset/areaStatistics")
    public InventoryAssetAreaStatisticsDto areaStatistics(InventoryAssetAreaQueryDto queryDto) {
        return localInventoryService.storageAreaInventoryAsset(queryDto);
    }

    /**
     * @return
     */
    @PostMapping(value = "/asset/rfid/submit")
    public InventoryAssetRFIDResultDto assetRFID(@RequestBody InventoryAssetRFIDSubmitDto request) {
        return localInventoryService.inventoryAssetRFID(request);
    }


    /**
     * app手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    @PutMapping(value = "/task/appManualInventory")
    public Boolean appManualInventory(@RequestBody InventoryManualDto dto) {
        return inventoryTaskService.manualInventory(dto);
    }

    /**
     * 判断资产id是否在盘点单内
     *
     * @param assetId     盘点资产id
     * @param inventoryId 盘点单id
     * @return 结果
     */
    @GetMapping("/asset/appCheckAssetId")
    public long checkAssetId(
            @RequestParam("assetId") String assetId, @RequestParam("inventoryId") Long inventoryId, @RequestParam("inventoryTaskId") Long inventoryTaskId) {
        return localInventoryService.appCheckAssetId(assetId, inventoryId, inventoryTaskId);
    }

    @PostMapping("/asset/post/appAssetCode")
    public List<InventoryAssetDataCountDto> appAssetCode(@RequestBody InventorySurplusQueryDto dto) {
        return localInventoryService.selectAppAssetCode(dto);
    }

    @GetMapping("/task/page")
    public IPage<DingInventoryTaskListDto> localTaskPage(DingInventoryQueryDto queryDto) {
        return localInventoryTaskService.localTaskPage(queryDto);
    }

}
