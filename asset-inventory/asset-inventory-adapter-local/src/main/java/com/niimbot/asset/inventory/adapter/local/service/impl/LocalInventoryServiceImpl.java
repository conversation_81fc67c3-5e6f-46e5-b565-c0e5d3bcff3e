package com.niimbot.asset.inventory.adapter.local.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.adapter.local.mapper.LocalInventoryMapper;
import com.niimbot.asset.inventory.adapter.local.service.LocalInventoryService;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryAsset;
import com.niimbot.asset.inventory.model.AsInventoryAssetReport;
import com.niimbot.asset.inventory.model.AsInventoryConfig;
import com.niimbot.asset.inventory.model.AsInventorySurplus;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.model.AsInventoryTaskAsset;
import com.niimbot.asset.inventory.service.AsInventoryAssetReportService;
import com.niimbot.asset.inventory.service.AsInventoryAssetService;
import com.niimbot.asset.inventory.service.AsInventoryConfigService;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventorySurplusService;
import com.niimbot.asset.inventory.service.AsInventoryTaskAssetService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.inventory.service.impl.MySqlInventoryAssetQueryConditionResolver;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.ding.InventoryAssetAreaQueryDto;
import com.niimbot.ding.InventoryAssetAreaStatisticsDto;
import com.niimbot.ding.InventoryAssetRFIDResultDto;
import com.niimbot.ding.InventoryAssetRFIDSubmitDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventoryManualBatchDto;
import com.niimbot.inventory.InventoryRemarkBatchDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocalInventoryServiceImpl implements LocalInventoryService {

    private ExecutorService threadPool;

    private final LocalInventoryMapper localInventoryMapper;

    private final AsInventoryService inventoryService;

    private final AsInventoryTaskService inventoryTaskService;

    private final AsInventoryTaskAssetService inventoryTaskAssetService;

    private final AssetService assetService;

    private final AsInventoryAssetReportService assetReportService;

    private final AsInventorySurplusService inventorySurplusService;
    @Resource
    private MySqlInventoryAssetQueryConditionResolver conditionResolver;
    @Resource
    private AsFormService formService;
    @Resource
    private AsInventoryConfigService inventoryConfigService;
    @Resource
    private AsInventoryAssetService inventoryAssetService;
    @Resource
    private AssetUtil assetUtil;

    private static final List<Integer> SUBMIT_STATUS = ListUtil.of(InventoryConstant.IN_PROGRESS, InventoryConstant.REJECTED);

    @PostConstruct
    public void init() {
        threadPool = AssetThreadPoolExecutorManager.newThreadPool("inventoryAssetService", 10, 10, 10000);
    }

    @Override
    public InventoryAssetAreaStatisticsDto storageAreaInventoryAsset(InventoryAssetAreaQueryDto queryDto) {
        //查询待盘资产
        Future<Integer> unCheckedFuture = threadPool.submit(() -> {
            InventoryAssetAreaQueryDto unCheckedParam = new InventoryAssetAreaQueryDto();
            unCheckedParam.setInventoryId(queryDto.getInventoryId())
                    .setInventoryTaskId(queryDto.getInventoryTaskId())
                    .setAreaId(queryDto.getAreaId()).setChecked(Boolean.FALSE);
            return localInventoryMapper.storageAreaAsset(unCheckedParam);
        });

        //查询已盘资产
        Future<Integer> checkedFuture = threadPool.submit(() -> {
            InventoryAssetAreaQueryDto checkedParam = new InventoryAssetAreaQueryDto();
            checkedParam.setInventoryId(queryDto.getInventoryId())
                    .setInventoryTaskId(queryDto.getInventoryTaskId())
                    .setAreaId(queryDto.getAreaId()).setChecked(Boolean.TRUE);
            return localInventoryMapper.storageAreaAsset(checkedParam);
        });

        //查询非当前区域资产
        Future<Integer> nonCurrentAreaFuture = threadPool.submit(() -> {
            InventoryAssetAreaQueryDto nonCurrentAreaParam = new InventoryAssetAreaQueryDto();
            nonCurrentAreaParam.setInventoryId(queryDto.getInventoryId())
                    .setInventoryTaskId(queryDto.getInventoryTaskId())
                    .setNotAreaId(queryDto.getAreaId());
            return localInventoryMapper.storageAreaAsset(nonCurrentAreaParam);
        });
        InventoryAssetAreaStatisticsDto result = new InventoryAssetAreaStatisticsDto();
        result.setAreaId(queryDto.getAreaId());
        try {
            result.setUnCheckedNum(unCheckedFuture.get(60, TimeUnit.SECONDS));
            result.setCheckedNum(checkedFuture.get(60, TimeUnit.SECONDS));
            result.setNonCurrentAreaNum(nonCurrentAreaFuture.get(60, TimeUnit.SECONDS));
        } catch (Exception e) {
            log.error("asInventoryAssetService storageAreaInventoryAsset error! param=[{}] exception ", JSONObject.toJSONString(queryDto), e);
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "区域盘点进度查询失败");
        }
        return result;
    }

    @Override
    public InventoryAssetRFIDResultDto inventoryAssetRFID(InventoryAssetRFIDSubmitDto request) {
        log.info("inventoryAssetService inventoryAssetRFID param=[{}]", JSON.toJSONString(request));
        //盘点单校验，校验盘点单是否存在，以及盘点单状态是在进行中
        AsInventory inventory = inventoryService.getById(request.getInventoryId());
        if (Objects.isNull(inventory)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(inventory.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        //盘点任务校验，校验盘点任务是否存在，以及盘点任务状态是否在进行中
        AsInventoryTask inventoryTask = inventoryTaskService.getById(request.getInventoryTaskId());
        if (Objects.isNull(inventoryTask)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(inventoryTask.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        //获取属于当前盘点任务的资产id
        List<Long> currentTaskAssetIdList = localInventoryMapper.selectAssetIdByEpcId(request);
        request.setAssetIdList(currentTaskAssetIdList);

        //查询属于当前盘点单的资产id
        InventoryAssetRFIDSubmitDto inventoryRequest = new InventoryAssetRFIDSubmitDto();
        BeanUtils.copyProperties(request, inventoryRequest);
        inventoryRequest.setInventoryTaskId(null);
        List<Long> currentInventoryAssetIdList = localInventoryMapper.selectAssetIdByEpcId(inventoryRequest);
        request.setInventoryAssetIdList(currentInventoryAssetIdList);

        //获取RFID盘点进度，以及盘盈数据，提交到异步任务执行
        Future<InventoryAssetRFIDResultDto> resultFuture = threadPool.submit(() -> {
            InventoryAssetRFIDResultDto data = new InventoryAssetRFIDResultDto();
            data.setAssetNum(inventoryTask.getAssetNum());

            //根据epcId列表查询资产id列表，需要根据这个数据来计算盘盈数量
            List<Long> assetIdList = this.queryAssetIdByEpcId(request.getCompanyId(), request.getEpcIdList());
            if (CollUtil.isEmpty(assetIdList)) {
                data.setNonCurrentTaskNum(0);//盘盈数量为0
                return data;
            }

            //属于当前盘点单的资产为空，说明该批次epcId的资产都是盘盈
            if (CollUtil.isEmpty(currentInventoryAssetIdList)) {
                data.setNonCurrentTaskNum(assetIdList.size());//盘盈数量为当前资产数量
                return data;
            }

            //盘盈数量：在资产库，但是不在当前盘点任务
            int nonCurrentTaskNum = (int) assetIdList.stream().filter(item -> !currentInventoryAssetIdList.contains(item)).count();
            data.setNonCurrentTaskNum(nonCurrentTaskNum);
            return data;
        });

        //事务更新已盘数量和资产盘点状态
        int checkedNum = 0;
        if (!CollUtil.isEmpty(request.getAssetIdList())) {
            //修改盘点资产的check状态
            checkedNum = localInventoryMapper.updateCheckedStatus(request);
            //修改资产盘点任务的status状态
            localInventoryMapper.updateTaskAssetStatus(request);
        }
        if (checkedNum > 0) {
            this.updateCheckedNumById(request.getInventoryTaskId(), checkedNum);
        }

        //保存盘盈在册数据
//        saveSurplusAsset(request, currentInventoryAssetIdList);

        try {
            InventoryAssetRFIDResultDto result = resultFuture.get(60, TimeUnit.SECONDS);
            result.setCheckedNum(inventoryTask.getCheckedNum() + checkedNum);
            return result;
        } catch (Exception e) {
            log.error("inventoryAssetService inventoryAssetRFID error! param=[{}] exception ", JSONObject.toJSONString(request), e);
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "RFID盘点失败");
        }
    }

    /**
     * RFID盘点，盘盈在册数据写入
     *
     * @param request            盘点任务数据
     * @param currentAssetIdList 当前盘点单资产数据
     */
    private void saveSurplusAsset(InventoryAssetRFIDSubmitDto request, List<Long> currentAssetIdList) {
        //盘盈在册：在资产库，但是不在当前盘点单的资产数据
        LambdaQueryWrapper<AsAsset> assetQueryWrapper = Wrappers.lambdaQuery(AsAsset.class).in(AsAsset::getLabelEpcid, request.getEpcIdList()).eq(AsAsset::getCompanyId, request.getCompanyId());
        if (!CollUtil.isEmpty(currentAssetIdList)) {
            //过滤当前盘点任务的资产id
            assetQueryWrapper.notIn(AsAsset::getId, currentAssetIdList);
        }

        //需要写入到盘盈在册表资产数据
        List<AsAsset> assetList = assetService.list(assetQueryWrapper);
        log.info("inventoryAssetService saveSurplusAsset assetList=[{}] assetIdList=[{}]", JSON.toJSONString(assetList), JSON.toJSONString(request.getAssetIdList()));
        if (CollUtil.isEmpty(assetList)) {
            return;
        }

        //查询已存在的盘盈在册资产数据
        List<Long> surplusAssetIdList = this.queryAssetByInventoryId(request.getInventoryId());
        log.info("inventoryAssetService saveSurplusAsset surplusAssetIdList=[{}]", JSON.toJSONString(surplusAssetIdList));
        if (!CollUtil.isEmpty(surplusAssetIdList)) {
            //过滤盘盈在册已存在的资产数据
            assetList = assetList.stream().filter(item -> !surplusAssetIdList.contains(item.getId())).collect(toList());
        }
        log.info("inventoryAssetService saveSurplusAsset assetList=[{}]", JSON.toJSONString(assetList));

        if (CollUtil.isEmpty(assetList)) {
            return;
        }

        // 记录履历
        FormVO formVO = formService.assetTpl();
        List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formVO.getFormFields());

        List<AsInventoryAssetReport> assetReportList = assetList.stream().map(item -> {
            AsInventoryAssetReport assetReport = new AsInventoryAssetReport();
            assetReport.setCompanyId(request.getCompanyId());//企业id
            assetReport.setReporter(request.getCurrentUserId());//上报人
            assetReport.setReportType(1);//上报类型：盘点上报
            assetReport.setAssetMark(1);//在册资产
            assetReport.setAssetId(item.getId());//资产id
            assetReport.setInventoryTerminal(5);//盘点终端小程序
//            assetReport.setRemark("RFID盘盈在册");

            JSONObject translate = item.translate();
            // assetUtil.translateAssetJsonView(translate, translations);
            assetUtil.translateAssetJson(translate, translations);
            assetReport.setAssetData(translate);//生成资产快照数据
            return assetReport;
        }).collect(toList());

        //批量写入资产上报表
        assetReportService.saveBatch(assetReportList);

        //批量写入资产盘盈表
        List<AsInventorySurplus> inventorySurplusList = assetReportList.stream().map(item -> {
            AsInventorySurplus surplus = new AsInventorySurplus();
            surplus.setInventoryId(request.getInventoryId());
            surplus.setInventoryTaskId(request.getInventoryTaskId());
            surplus.setReportId(item.getId());
            return surplus;
        }).collect(toList());
        inventorySurplusService.saveBatch(inventorySurplusList);
    }

    @Override
    public List<Long> queryAssetIdByEpcId(Long companyId, List<String> epcIdList) {
        if (CollUtil.isEmpty(epcIdList)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "资产epcId列表为空");
        }
        return localInventoryMapper.selectAssetId(companyId, epcIdList);
    }

    @Override
    public Integer updateCheckedNumById(Long inventoryTaskId, Integer checkedNum) {
        if (Objects.isNull(inventoryTaskId) || Objects.isNull(checkedNum)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "盘点任务信息为空");
        }
        return localInventoryMapper.updateCheckedNumById(inventoryTaskId, checkedNum);
    }

    @Override
    public List<Long> queryAssetByInventoryId(Long inventoryId) {
        if (Objects.isNull(inventoryId)) {
            return null;
        }

        return localInventoryMapper.selectAssetIdByTaskId(inventoryId);
    }

    @Override
    public long appCheckAssetId(String assetId, Long inventoryId, Long inventoryTaskId) {
        FormVO formVO = formService.assetTpl();
        List<String> uniqueCodes = formVO.getFormFields().stream()
                .filter(f -> {
                    if (f.getFieldType().equals(FormFieldCO.YZC_ASSET_SERIALNO)) {
                        return true;
                    }
                    return f.getFieldProps().containsKey("unique") ? f.getFieldProps().getBoolean("unique") : false;
                }).map(FormFieldCO::getFieldCode).collect(toList());
        AsInventoryAsset asset = localInventoryMapper.appCheckAssetId(assetId, inventoryId, inventoryTaskId, uniqueCodes);
        if (asset == null) {
            return -1L;
        } else {
            return asset.getAssetId();
        }
    }

    @Override
    public List<InventoryAssetDataCountDto> selectAppAssetCode(InventorySurplusQueryDto dto) {
        long startTime = System.currentTimeMillis();
        // 获取盘点单详情
        AsInventory byId = inventoryService.getById(dto.getInventoryId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }

        String tableAlias = "a";
//        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        dto.setGroupByColumn(String.join(".", tableAlias, dto.getGroupByColumn()));
        List<InventoryAssetDataCountDto> result = localInventoryMapper.selectAppAssetAll(dto, null);
        log.info("asInventoryAssetService selectAppAssetCode cost=[{}]", System.currentTimeMillis() - startTime);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean manualInventoryBatch(InventoryManualBatchDto dto) {
        if (CollUtil.isEmpty(dto.getAssetIds())) {
            return false;
        }
        Long inventoryId = dto.getInventoryId();
        // 获取盘点单
        AsInventory byId = inventoryService.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 查询盘点配置
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(inventoryId);
        if (!inventoryConfig.getEnableManual()) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_OPEN_MANUAL);
        }
        if (inventoryConfig.getEnableTakePicture()) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NEED_TAKE_PICTURE);
        }

        List<AsInventoryAsset> inventoryAssetList = inventoryAssetService.list(new QueryWrapper<AsInventoryAsset>().lambda()
                .eq(AsInventoryAsset::getInventoryId, inventoryId)
                .in(AsInventoryAsset::getAssetId, dto.getAssetIds()));

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
//        Integer inventoryTerminal = ObjectUtil.isNotNull(dto.getInventoryTerminal()) ? dto.getInventoryTerminal() : InventoryConstant.PC;

        List<AsInventoryAsset> inventoryAssetUpdateList = new ArrayList<>();
        for (AsInventoryAsset asInventoryAsset : inventoryAssetList) {
            // 判断当前用户是否是盘点人
            List<Long> inventoryUser = asInventoryAsset.getInventoryUser();
            if (!inventoryUser.contains(currentUserId)) {
                throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
            }
            AsInventoryAsset inventoryAssetUpdate = new AsInventoryAsset();
            inventoryAssetUpdate.setId(asInventoryAsset.getId())
                    .setChecked(true)
                    .setActualInventoryUser(currentUserId)
                    .setInventoryMode(InventoryConstant.MANUAL)
                    .setInventoryTerminal(ObjectUtil.isEmpty(dto.getInventoryTerminal()) ? InventoryConstant.APPLETS : dto.getInventoryTerminal())
                    .setInventoryTime(LocalDateTime.now())
                    .setRemark(dto.getRemark());
            inventoryAssetUpdateList.add(inventoryAssetUpdate);
        }
        inventoryAssetService.updateBatchById(inventoryAssetUpdateList);

        List<AsInventoryTaskAsset> taskAssetList = inventoryTaskAssetService.list(Wrappers.<AsInventoryTaskAsset>lambdaQuery()
                .eq(AsInventoryTaskAsset::getInventoryId, inventoryId)
                .eq(AsInventoryTaskAsset::getInventoryTaskId, dto.getInventoryTaskId())
                .eq(AsInventoryTaskAsset::getStatus, InventoryConstant.INVENTORY_TASK_ASSET_STATUS_NOT_CHECK)
                .in(AsInventoryTaskAsset::getAssetId, dto.getAssetIds()));
        taskAssetList.forEach(f -> {
            f.setStatus(InventoryConstant.INVENTORY_TASK_ASSET_STATUS_CHECK);
            f.setAssetMark(InventoryConstant.INVENTORY_TASK_ASSET_MARK_NONE);
        });
        inventoryTaskAssetService.updateBatchById(taskAssetList);

        AsInventoryTask task = inventoryTaskService.getById(dto.getInventoryTaskId());
        Integer checkedNum = task.getCheckedNum();
        task.setCheckedNum(checkedNum + inventoryAssetUpdateList.size());
        inventoryTaskService.updateById(task);

        return true;
    }

    @Override
    public Boolean updateRemarkBatch(InventoryRemarkBatchDto dto) {
        Long inventoryId = dto.getInventoryId();
        // 获取盘点单
        AsInventory byId = inventoryService.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 查询盘点配置
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(inventoryId);
        if (null == inventoryConfig) {
            throw new BusinessException(InventoryResultCode.INVENTORY_CONFIG_NOT_EXIST);
        }

        // 获取盘点资产
        List<Long> assetId = dto.getAssetIds();
        List<AsInventoryAsset> inventoryAsset = inventoryAssetService.list(new QueryWrapper<AsInventoryAsset>().lambda()
                .eq(AsInventoryAsset::getInventoryId, inventoryId)
                .in(AsInventoryAsset::getAssetId, assetId));
        if (inventoryAsset.size() != assetId.size()) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        for (AsInventoryAsset asset : inventoryAsset) {
            // 判断当前用户是否是盘点人
            List<Long> inventoryUser = asset.getInventoryUser();
            if (!inventoryUser.contains(currentUserId)) {
                throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
            }
            asset.setRemark(dto.getRemark());
        }
        inventoryAssetService.updateBatchById(inventoryAsset);
        return true;
    }

}
