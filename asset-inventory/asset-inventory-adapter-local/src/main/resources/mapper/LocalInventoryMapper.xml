<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.adapter.local.mapper.LocalInventoryMapper">

    <select id="storageAreaAsset" parameterType="com.niimbot.ding.InventoryAssetAreaQueryDto"
            resultType="java.lang.Integer">
        select count(distinct asset_id) as dataCount
        from as_inventory_asset
        <where>
            <if test="param.inventoryId != null">
                and inventory_id = #{param.inventoryId}
            </if>
            <if test="param.inventoryTaskId != null">
                and inventory_task_id = #{param.inventoryTaskId}
            </if>
            <if test="param.areaId != null">
                and storage_area = #{param.areaId}
            </if>
            <if test="param.notAreaId != null">
                and storage_area <![CDATA[<>]]> #{param.notAreaId}
            </if>
            <if test="param.checked != null">
                and checked = #{param.checked}
            </if>
        </where>
    </select>


    <select id="selectAssetIdByEpcId" parameterType="com.niimbot.ding.InventoryAssetRFIDSubmitDto"
            resultType="java.lang.Long">
        select distinct a.asset_id
        from as_inventory_asset as a left join as_asset as b on a.asset_id = b.id
        <where>
            and b.is_delete = 0 and b.company_id = #{param.companyId}
            <if test="param.inventoryId != null">
                and a.inventory_id = #{param.inventoryId}
            </if>
            <if test="param.inventoryTaskId != null">
                and a.inventory_task_id = #{param.inventoryTaskId}
            </if>
            <if test="param.epcIdList != null and param.epcIdList.size > 0">
                and b.label_epcid in
                <foreach collection="param.epcIdList" item="epcId" index="index" open="("
                         separator="," close=")">
                    #{epcId}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateCheckedStatus" parameterType="com.niimbot.ding.InventoryAssetRFIDSubmitDto">
        update as_inventory_asset
        set checked = 1, inventory_mode = #{param.inventoryMode}, actual_inventory_user =
        #{param.currentUserId},
        inventory_terminal = #{param.inventoryTerminal}
        where checked = 0 and inventory_id = #{param.inventoryId} and inventory_task_id =
        #{param.inventoryTaskId} and
        asset_id in
        <foreach collection="param.assetIdList" item="assetId" index="index" open="(" separator=","
                 close=")">
            #{assetId}
        </foreach>
    </update>

    <update id="updateTaskAssetStatus" parameterType="com.niimbot.ding.InventoryAssetRFIDSubmitDto">
        update as_inventory_task_asset
        set status = 1
        where status = 0 and inventory_id = #{param.inventoryId} and inventory_task_id =
        #{param.inventoryTaskId} and
        asset_id in
        <foreach collection="param.assetIdList" item="assetId" index="index" open="(" separator=","
                 close=")">
            #{assetId}
        </foreach>
    </update>

    <select id="selectAssetId" resultType="java.lang.Long">
        select distinct id
        from as_asset
        where is_delete = 0 and company_id = #{companyId} and label_epcid in
        <foreach collection="epcIdList" item="epcId" index="index" open="(" close=")" separator=",">
            #{epcId}
        </foreach>
    </select>

    <update id="updateCheckedNumById">
        update as_inventory_task
        set checked_num = checked_num + #{checkedNum}
        where id = #{inventoryTaskId}
    </update>

    <select id="selectAssetIdByTaskId" resultType="java.lang.Long">
        select a.asset_id
        from as_inventory_asset_report as a
                 left join as_inventory_surplus as b on a.id = b.report_id
        where b.inventory_id = #{inventoryId}
          and a.asset_mark = 1
    </select>

    <select id="appCheckAssetId" resultType="com.niimbot.asset.inventory.model.AsInventoryAsset">
        SELECT a.asset_id FROM as_inventory_asset a
        left join as_asset_php_ref as r on a.asset_id = r.java_id and r.type = 1
        WHERE a.inventory_id = #{inventoryId}
        and a.inventory_task_id = #{inventoryTaskId}
        <!-- 关键字（编码/名称）-->
        and (a.asset_id = #{assetId}
        or r.php_id = #{assetId}
        <foreach collection="uniqueCodes" item="code" index="index" open="or"
                 separator="or">
            a.asset_snapshot_data ->> '$.${code}' = #{assetId}
        </foreach>
        )
    </select>

    <select id="selectAppAssetAll" resultType="com.niimbot.inventory.InventoryAssetDataCountDto">
        SELECT
        count(a.id) as dataCount,
        ${ew.groupByColumn} as conditionCode
        FROM
        as_inventory_asset AS a
        LEFT JOIN as_inventory AS i ON i.id = a.inventory_id
        LEFT JOIN (select * from as_inventory_task_asset where inventory_task_id = #{ew.taskId}) AS
        t ON t.inventory_id
        = i.id and t.asset_id= a.asset_id
        LEFT JOIN as_asset AS aa ON aa.id = a.asset_id
        <where>
            a.inventory_id = #{ew.inventoryId} AND a.inventory_task_id = #{ew.taskId}
            <if test="ew.inventoryUser!=null and ew.inventoryUser!=''">
                AND JSON_CONTAINS(a.inventory_user, JSON_ARRAY(#{ew.inventoryUser}))
            </if>
            <if test="ew.checked!=null">
                AND t.status = #{ew.checked}
            </if>
            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
        </where>
        group by ${ew.groupByColumn}
    </select>

</mapper>
