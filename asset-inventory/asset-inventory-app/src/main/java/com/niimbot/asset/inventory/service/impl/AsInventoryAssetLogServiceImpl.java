package com.niimbot.asset.inventory.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.inventory.model.AsInventoryAssetLog;
import com.niimbot.asset.inventory.service.AsInventoryAssetLogService;
import com.niimbot.asset.inventory.mapper.AsInventoryAssetLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 盘点资产属性修改日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Service
public class AsInventoryAssetLogServiceImpl extends ServiceImpl<AsInventoryAssetLogMapper, AsInventoryAssetLog> implements AsInventoryAssetLogService {

}
