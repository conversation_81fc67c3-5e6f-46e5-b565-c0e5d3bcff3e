package com.niimbot.asset.inventory.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点任务-资产盘点状态
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryTaskAsset对象", description="盘点任务-资产盘点状态")
@TableName(value = "as_inventory_task_asset", autoResultMap = true)
public class AsInventoryTaskAsset implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "盘点任务id")
    private Long inventoryTaskId;

    @ApiModelProperty(value = "盘点单资产id")
    private Long assetId;

    @ApiModelProperty(value = "资产原始快照")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject assetOriginalData;

    @ApiModelProperty(value = "盘点状态（0：未盘：1：已盘，2：盘盈）")
    private Integer status;

    @ApiModelProperty(value = "资产标记(0:无, 1：在册资产，2：不在册资产)")
    private Integer assetMark;


}
