package com.niimbot.asset.inventory.domain.event;

import com.niimbot.asset.framework.support.SystemEvent;

import lombok.Getter;

/**
 * 钉钉酷应用——盘点任务提交审核
 *
 * <AUTHOR>
 * @date 2023/4/12 17:13
 */
@Getter
public class TaskSubmitEvent extends SystemEvent {

    private Long companyId;
    private Long inventoryId;
    private Long taskId;

    public TaskSubmitEvent(Long companyId, Long inventoryId, Long taskId) {
        super(new Object());
        this.companyId = companyId;
        this.inventoryId = inventoryId;
        this.taskId = taskId;
    }

}
