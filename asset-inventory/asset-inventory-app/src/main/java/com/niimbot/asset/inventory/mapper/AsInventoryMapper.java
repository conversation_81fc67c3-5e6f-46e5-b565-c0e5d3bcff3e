package com.niimbot.asset.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.inventory.InventoryCountDto;
import com.niimbot.inventory.InventoryGroupReportDto;
import com.niimbot.inventory.InventoryResReportDto;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 盘点单持久层接口
 *
 * <AUTHOR>
 * @date 2021/4/9 09:30
 */
@EnableDataPerm(includeMethodName = {"selectList_mpCount", "selectList"})
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsInventoryMapper.class)
public interface AsInventoryMapper extends BaseMapper<AsInventory> {

    /**
     * 删除 as_inventory_approve
     *
     * @param id
     * @return
     */
    @Delete("delete from as_inventory_approve where inventory_id = #{id}")
    void removeInventoryApprove(@Param("id") Long id);

    /**
     * 删除 as_inventory_asset
     *
     * @param id
     * @return
     */
    @Delete("delete from as_inventory_asset where inventory_id = #{id}")
    void removeInventoryAsset(@Param("id") Long id);

    /**
     * 删除 as_inventory_asset_log
     *
     * @param id
     * @return
     */
    @Delete("delete from as_inventory_asset_log where inventory_id = #{id}")
    void removeInventoryAssetLog(@Param("id") Long id);

    /**
     * 删除 as_inventory_config
     *
     * @param id
     * @return
     */
    @Delete("delete from as_inventory_config where id = #{id}")
    void removeInventoryConfig(@Param("id") Long id);

    /**
     * 删除 as_inventory_report
     *
     * @param id
     * @return
     */
    @Delete("delete from as_inventory_report where id = #{id}")
    void removeInventoryReport(@Param("id") Long id);

    /**
     * 删除 as_inventory_surplus
     *
     * @param id
     * @return
     */
    @Delete("delete from as_inventory_surplus where inventory_id = #{id}")
    void removeInventorySurplus(@Param("id") Long id);

    /**
     * 删除 as_inventory_task
     *
     * @param id
     * @return
     */
    @Delete("delete from as_inventory_task where inventory_id = #{id}")
    void removeInventoryTask(@Param("id") Long id);

    /**
     * 盘点头部统计
     *
     * @return 结果
     */
    InventoryCountDto countByStatus(@Param("companyId") Long companyId, @Param("userId") Long userId);

    /**
     * 盘点结果统计
     *
     * @return 结果
     */
    List<InventoryResReportDto> countResReport(@Param("id") Long id);

    /**
     * 盘点结果处理统计
     *
     * @return 结果
     */
    List<InventoryResReportDto> countResHandle(@Param("id") Long id);

    /**
     * 盘点任务概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countTaskReport(@Param("id") Long id);

    /**
     * 各分类资产盘点概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countCategoryReport(@Param("id") Long id);

    /**
     * 各状态资产盘点概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countStatusReport(@Param("id") Long id);

    /**
     * 各区域资产盘点概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countAreaReport(@Param("id") Long id);

    /**
     * 各所属管理组织资产盘点概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countOrgOwnReport(@Param("id") Long id);

    /**
     * 各所属使用组织资产盘点概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countUseOrgReport(@Param("id") Long id);

    /**
     * 各使用人资产盘点概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countUsePersonReport(@Param("id") Long id);

    /**
     * 各所属管理员资产盘点概况
     *
     * @return 结果
     */
    List<InventoryGroupReportDto> countManagerOwnReport(@Param("id") Long id);

    List<AsInventory> selectAllForSendNotice(@Param("ew") LocalDateTime timeout);

    /**
     * 应盘资产数
     * @param id
     * @return
     */
    Integer countAssets(@Param("id") Long id);

    Integer getProcessInventory(@Param("assetId") Long assetId);
}
