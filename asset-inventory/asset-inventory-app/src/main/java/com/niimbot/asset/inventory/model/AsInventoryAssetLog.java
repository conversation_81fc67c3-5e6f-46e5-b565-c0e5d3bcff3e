package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.inventory.handle.InventoryAssetLogDataListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 盘点资产属性修改日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryAssetLog对象", description="盘点资产属性修改日志")
@TableName(value = "as_inventory_asset_log", autoResultMap = true)
public class AsInventoryAssetLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "资产id")
    private Long assetId;

    @ApiModelProperty(value = "修改数据日志")
    @TableField(typeHandler = InventoryAssetLogDataListTypeHandler.class)
    private List<InventoryAssetLogData> logData;
}
