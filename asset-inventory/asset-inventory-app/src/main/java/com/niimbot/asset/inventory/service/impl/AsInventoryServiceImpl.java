package com.niimbot.asset.inventory.service.impl;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryConditionType;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.domain.event.CloseInventoryEvent;
import com.niimbot.asset.inventory.mapper.AsInventoryMapper;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryAsset;
import com.niimbot.asset.inventory.model.AsInventoryConfig;
import com.niimbot.asset.inventory.model.AsInventoryQueryAsset;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.model.AsInventoryTaskAsset;
import com.niimbot.asset.inventory.model.InventoryDispatchRecord;
import com.niimbot.asset.inventory.model.InventoryRangeGroup;
import com.niimbot.asset.inventory.model.InventoryRangeRecord;
import com.niimbot.asset.inventory.service.AsInventoryApproveService;
import com.niimbot.asset.inventory.service.AsInventoryAssetService;
import com.niimbot.asset.inventory.service.AsInventoryConfigService;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventorySurplusService;
import com.niimbot.asset.inventory.service.AsInventoryTaskAssetService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.inventory.service.InventoryQueryAssetService;
import com.niimbot.asset.inventory.service.InventoryTodoService;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsAssetStatusService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.AsTodoService;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryApproveSubmitDto;
import com.niimbot.inventory.InventoryAssetCountDto;
import com.niimbot.inventory.InventoryAssetCountQueryDto;
import com.niimbot.inventory.InventoryConfigDto;
import com.niimbot.inventory.InventoryCountDto;
import com.niimbot.inventory.InventoryCreateAssetQueryDto;
import com.niimbot.inventory.InventoryCreateAssetQueryPageDto;
import com.niimbot.inventory.InventoryDetailDto;
import com.niimbot.inventory.InventoryDispatchDetailDto;
import com.niimbot.inventory.InventoryDispatchRecordDto;
import com.niimbot.inventory.InventoryDto;
import com.niimbot.inventory.InventoryGroupReportDto;
import com.niimbot.inventory.InventoryHandleResultDto;
import com.niimbot.inventory.InventoryHistoryQueryDto;
import com.niimbot.inventory.InventoryRangeGroupCreateDto;
import com.niimbot.inventory.InventoryReportDto;
import com.niimbot.inventory.InventoryResReportDto;
import com.niimbot.inventory.InventorySubmitDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.inventory.InventoryTaskDto;
import com.niimbot.inventory.InventoryUpdateApproverDto;
import com.niimbot.inventory.InventoryView;
import com.niimbot.inventory.NewInventoryResReportDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDetailDto;
import com.niimbot.means.AssetDto;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.means.AssetStatusDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import static java.util.stream.Collectors.toList;

/**
 * 盘点单接口实现
 *
 * <AUTHOR>
 * @date 2021/4/9 09:31
 */
@Service
public class AsInventoryServiceImpl extends ServiceImpl<AsInventoryMapper, AsInventory> implements AsInventoryService {

    @Resource
    private AsInventoryTaskService inventoryTaskService;
    @Resource
    private AsInventoryTaskAssetService inventoryTaskAssetService;

    @Resource
    private AsInventoryConfigService inventoryConfigService;

    @Resource
    private AsInventoryAssetService inventoryAssetService;

    @Resource
    private AsInventoryApproveService inventoryApproveService;

    @Resource
    private AssetService assetService;

    @Resource
    private AsCusEmployeeService cusEmployeeService;

    @Resource
    private AsCusEmployeeExtService employeeExtService;

    @Resource
    private OrgService orgService;

    @Resource
    private RedisService redisService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private AreaService areaService;

    @Resource
    private AsAssetStatusService assetStatusService;

    @Resource
    private AsTodoService todoService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Resource
    private InventoryQueryAssetService inventoryQueryAssetService;

    @Resource
    private AsInventorySurplusService inventorySurplusService;

    @Resource
    private AsAccountEmployeeService accountEmployeeService;

    @Resource
    private InventoryTodoService inventoryTodoService;

    @Resource
    private AssetUtil assetUtil;

    @Resource
    private AsFormService formService;

    @Resource
    MessageService messageService;

    /**
     * 盘点任务明细
     *
     * @param id
     * @return
     */
    @Override
    public InventoryDetailDto getByIdWithoutAsset(Long id) {
        // 获取盘点单详情
        AsInventory byId = this.getById(id);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        InventoryDto inventoryDto = new InventoryDto();
        BeanUtil.copyProperties(byId, inventoryDto);
        // 获取资产盘点进度
        InventoryAssetCountQueryDto inventoryAssetCountQueryDto = new InventoryAssetCountQueryDto();
        inventoryAssetCountQueryDto.setInventoryId(id);
        InventoryAssetCountDto inventoryAssetCount = inventoryAssetService.assetCount(inventoryAssetCountQueryDto);
        long checkedNum = inventoryAssetCount.getCheckedNum();
        long noCheckedNum = inventoryAssetCount.getNoCheckedNum();
        inventoryDto.setCheckedNum(checkedNum)
                .setAssetNum(checkedNum + noCheckedNum);

        // 获取盘点分配方式
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(id);
        inventoryDto.setDispatchMode(inventoryConfig.getDispatchMode());
        inventoryDto.setEnableNoPermLook(inventoryConfig.getEnableNoPermLook());

        // 获取盘点任务明细
        List<AsInventoryTask> list = inventoryTaskService.list(Wrappers.<AsInventoryTask>lambdaQuery()
                .eq(AsInventoryTask::getInventoryId, id)
                .eq(AsInventoryTask::getTaskType, InventoryConstant.TASK_TYPE_1)
        );

        List<InventoryTaskDto> inventoryDtoList = Lists.newArrayList();
        for (AsInventoryTask asInventoryTask : list) {
            InventoryAssetCountQueryDto inventoryAssetCountChildDto = new InventoryAssetCountQueryDto();
            inventoryAssetCountChildDto.setInventoryId(id).setTaskId(asInventoryTask.getId());
            InventoryAssetCountDto inventoryAssetCountByUser =
                    inventoryAssetService.assetCount(inventoryAssetCountChildDto);

            InventoryTaskDto inventoryTaskDto = new InventoryTaskDto();
            BeanUtil.copyProperties(asInventoryTask, inventoryTaskDto);

            List<String> userNames = asInventoryTask.getInventoryUsers()
                    .stream().map(i -> cacheResourceUtil.getUserNameAndCode(i))
                    .collect(toList());

            inventoryTaskDto.setInventoryUserText(String.join("、", userNames));

            long checkedNumByUser = inventoryAssetCountByUser.getCheckedNum();
            long noCheckedNumByUser = inventoryAssetCountByUser.getNoCheckedNum();
            inventoryTaskDto.setCheckedNum((int) checkedNumByUser)
                    .setAssetNum((int) checkedNumByUser + (int) noCheckedNumByUser);
            inventoryDtoList.add(inventoryTaskDto);
        }

        InventoryDetailDto inventoryDetailDto = new InventoryDetailDto();
        inventoryDetailDto.setHeadData(inventoryDto).setList(inventoryDtoList);

        return inventoryDetailDto;
    }

    private List<AssetDto> findInventoryAssets(Integer rangeType, Boolean enableExcludeRepair,
                                               Boolean enableExcludeDispose,
                                               List<AssetQueryConditionDto> assetQueryConditions,
                                               List<InventoryHistoryQueryDto> inventoryHistoryQueries) {
        Set<AssetDto> assets = new CopyOnWriteArraySet<>();
        // 从资产库选择
        if (rangeType == 1) {
            if (CollUtil.isEmpty(assetQueryConditions)) {
                BusinessExceptionUtil.throwException("资产筛选条件不能为空");
            }
            if (assetQueryConditions.size() > 10) {
                BusinessExceptionUtil.throwException("资产筛选条件不能超过10个");
            }
            for (AssetQueryConditionDto queryConditionDto : assetQueryConditions) {
                handleStatus(enableExcludeRepair, enableExcludeDispose, queryConditionDto);
                List<AssetDto> assetDtos = assetService.listPc(queryConditionDto);
                assets.addAll(assetDtos);
            }
        }
        // 从历史盘点单选择
        else {
            if (CollUtil.isEmpty(inventoryHistoryQueries)) {
                BusinessExceptionUtil.throwException("历史盘点单不能为空");
            }
            if (inventoryHistoryQueries.size() > 10) {
                BusinessExceptionUtil.throwException("最多选择10个历史盘点单");
            }
            for (InventoryHistoryQueryDto historyQueryDto : inventoryHistoryQueries) {
                if (historyQueryDto.getType() == null) {
                    BusinessExceptionUtil.throwException("历史盘点单范围不能为空");
                }
                if (CollUtil.isEmpty(historyQueryDto.getInventoryIds())) {
                    BusinessExceptionUtil.throwException("历史盘点单不能为空");
                }
                List<Long> assetIds;
                if (historyQueryDto.getType() == 0) {
                    assetIds = inventoryAssetService.listAssetIds(historyQueryDto.getInventoryIds(), 0);
                } else {
                    assetIds = inventoryAssetService.listAssetIds(historyQueryDto.getInventoryIds(), null);
                }
                if (CollUtil.isNotEmpty(assetIds)) {
                    AssetQueryConditionDto queryConditionDto = new AssetQueryConditionDto();
                    queryConditionDto.setPageSize(1000000L);
                    queryConditionDto.setIncludeAssetIds(assetIds);
                    handleStatus(enableExcludeRepair, enableExcludeDispose, queryConditionDto);
                    List<AssetDto> assetDtos = assetService.listPc(queryConditionDto);
                    assets.addAll(assetDtos);
                }
            }
        }
        return new ArrayList<>(assets);
    }

    @Override
    public List<AssetDto> findInventoryDispatchAssets(
            Integer rangeType,
            Boolean enableExcludeRepair,
            Boolean enableExcludeDispose,
            List<AssetQueryConditionDto> assetQueryConditions,
            List<InventoryHistoryQueryDto> inventoryHistoryQueries) {
        List<AssetDto> assets = new ArrayList<>();
        // 从资产库选择
        if (rangeType == 1) {
            if (CollUtil.isEmpty(assetQueryConditions)) {
                BusinessExceptionUtil.throwException("资产筛选条件不能为空");
            }
            if (assetQueryConditions.size() > 10) {
                BusinessExceptionUtil.throwException("资产筛选条件不能超过10个");
            }
            for (AssetQueryConditionDto queryConditionDto : assetQueryConditions) {
                handleStatus(enableExcludeRepair, enableExcludeDispose, queryConditionDto);
                List<AssetDto> assetDtos = assetService.listInventoryDispatchAssets(queryConditionDto);
                assets.addAll(assetDtos);
            }
        }
        // 从历史盘点单选择
        else {
            if (CollUtil.isEmpty(inventoryHistoryQueries)) {
                BusinessExceptionUtil.throwException("历史盘点单不能为空");
            }
            if (inventoryHistoryQueries.size() > 10) {
                BusinessExceptionUtil.throwException("最多选择10个历史盘点单");
            }
            for (InventoryHistoryQueryDto historyQueryDto : inventoryHistoryQueries) {
                if (historyQueryDto.getType() == null) {
                    BusinessExceptionUtil.throwException("历史盘点单范围不能为空");
                }
                if (CollUtil.isEmpty(historyQueryDto.getInventoryIds())) {
                    BusinessExceptionUtil.throwException("历史盘点单不能为空");
                }
                List<Long> assetIds;
                if (historyQueryDto.getType() == 0) {
                    assetIds = inventoryAssetService.listAssetIds(historyQueryDto.getInventoryIds(), 0);
                } else {
                    assetIds = inventoryAssetService.listAssetIds(historyQueryDto.getInventoryIds(), null);
                }
                if (CollUtil.isNotEmpty(assetIds)) {
                    AssetQueryConditionDto queryConditionDto = new AssetQueryConditionDto();
                    queryConditionDto.setIncludeAssetIds(assetIds);
                    handleStatus(enableExcludeRepair, enableExcludeDispose, queryConditionDto);
                    List<AssetDto> assetDtos = assetService.listInventoryDispatchAssets(queryConditionDto);
                    assets.addAll(assetDtos);
                }
            }
        }
        return new ArrayList<>(assets);
    }

    private void handleStatus(Boolean enableExcludeRepair, Boolean enableExcludeDispose,
                              AssetQueryConditionDto queryConditionDto) {
        // 不存在查询条件
        if (CollUtil.isEmpty(queryConditionDto.getConditions())) {
            List<QueryConditionDto> conditions = new ArrayList<>();
            List<Integer> status = new ArrayList<>();
            if (enableExcludeRepair) {
                status.add(AssetConstant.ASSET_STATUS_SERVICE);
            }
            if (enableExcludeDispose) {
                status.add(AssetConstant.ASSET_STATUS_HANDLE);
            }
            if (CollUtil.isNotEmpty(status)) {
                QueryConditionDto conditionDto = new QueryConditionDto();
                conditionDto.setCode("status");
                conditionDto.setQuery(QueryConditionType.NOT_IN.getCode());
                conditionDto.setQueryData(status);
                conditionDto.setType("status");
                conditions.add(conditionDto);
                queryConditionDto.setConditions(conditions);
            }
        }
        // 存在查询条件
        else {
            List<QueryConditionDto> conditions = queryConditionDto.getConditions();
            Optional<QueryConditionDto> optional =
                    conditions.stream().filter(o -> "status".equals(o.getType()))
                            .findFirst();
            // 存在 status 条件
            if (optional.isPresent()) {
                QueryConditionDto conditionDto = optional.get();
                List<Integer> status = Convert.toList(Integer.class, conditionDto.getQueryData());
                // 资产状态为空
                if (CollUtil.isEmpty(status)) {
                    status = new ArrayList<>();
                    if (enableExcludeRepair) {
                        status.add(AssetConstant.ASSET_STATUS_SERVICE);
                    }
                    if (enableExcludeDispose) {
                        status.add(AssetConstant.ASSET_STATUS_HANDLE);
                    }
                    if (CollUtil.isNotEmpty(status)) {
                        conditionDto.setQuery(QueryConditionType.NOT_IN.getCode());
                        conditionDto.setQueryData(status);
                    }
                }
                // 资产状态不为空
                else {
                    if (QueryConditionType.NOT_IN.getCode().equals(conditionDto.getQuery())) {
                        List<Integer> newStatus = new ArrayList<>();
                        if (enableExcludeRepair) {
                            newStatus.add(AssetConstant.ASSET_STATUS_SERVICE);
                        }
                        if (enableExcludeDispose) {
                            newStatus.add(AssetConstant.ASSET_STATUS_HANDLE);
                        }
                        conditionDto.setQueryData(newStatus);
                    } else {
                        List<Integer> newStatus = new ArrayList<>();
                        for (Integer s : status) {
                            if (enableExcludeRepair &&
                                    AssetConstant.ASSET_STATUS_SERVICE.equals(s)) {
                                continue;
                            }
                            if (enableExcludeDispose &&
                                    AssetConstant.ASSET_STATUS_HANDLE.equals(s)) {
                                continue;
                            }
                            newStatus.add(s);
                        }
                        if (CollUtil.isEmpty(newStatus)) {
                            if (enableExcludeRepair) {
                                newStatus.add(AssetConstant.ASSET_STATUS_SERVICE);
                            }
                            if (enableExcludeDispose) {
                                newStatus.add(AssetConstant.ASSET_STATUS_HANDLE);
                            }
                            conditionDto.setQuery(QueryConditionType.NOT_IN.getCode());
                            conditionDto.setQueryData(newStatus);
                        } else {
                            conditionDto.setQueryData(newStatus);
                        }
                    }
                }
            }
            // 不存在 status 条件
            else {
                List<Integer> status = new ArrayList<>();
                if (enableExcludeRepair) {
                    status.add(AssetConstant.ASSET_STATUS_SERVICE);
                }
                if (enableExcludeDispose) {
                    status.add(AssetConstant.ASSET_STATUS_HANDLE);
                }
                if (CollUtil.isNotEmpty(status)) {
                    QueryConditionDto conditionDto = new QueryConditionDto();
                    conditionDto.setCode("status");
                    conditionDto.setQuery(QueryConditionType.NOT_IN.getCode());
                    conditionDto.setQueryData(status);
                    conditionDto.setType("status");
                    conditions.add(conditionDto);
                }
            }
        }
    }

    /**
     * 创建盘点单
     *
     * @param dto dto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(InventorySubmitDto dto) {
        // 1、判断资产数据是否为空
        // 查询资产数据
        if (CollUtil.isNotEmpty(dto.getAssetQueryConditions())) {
            dto.getAssetQueryConditions().forEach(q -> q.setPageSize(1000000L));
        }
        List<AssetDto> assetDtos = findInventoryAssets(
                dto.getRangeType(),
                dto.getEnableExcludeRepair(),
                dto.getEnableExcludeDispose(),
                dto.getAssetQueryConditions(),
                dto.getInventoryHistoryQueries());
        if (CollUtil.isEmpty(assetDtos)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 2、写入盘点基础数据
        AsInventory asInventory = new AsInventory();
        asInventory.setInventoryNo(StringUtils.getOrderNo("PD"));
        BeanUtil.copyProperties(dto, asInventory);
        asInventory.setCompanyId(companyId);

        // 首先生成id
        Long inventoryId = IdUtils.getId();
        asInventory.setId(inventoryId);
        if (!this.save(asInventory)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }

        // 3、写入盘点配置数据
        dto.getDispatchDetail().forEach(d -> {
            d.setTaskId(IdUtils.getId());
        });
        AsInventoryConfig inventoryConfig = new AsInventoryConfig();
        BeanUtil.copyProperties(dto, inventoryConfig);
        inventoryConfig.setId(inventoryId);
        List<InventoryRangeGroup> rangeGroups =
                createRangeGroup(
                        new InventoryRangeGroupCreateDto()
                                .setEnableExcludeRepair(dto.getEnableExcludeRepair())
                                .setEnableExcludeDispose(dto.getEnableExcludeDispose())
                                .setRangeType(dto.getRangeType())
                                .setAssetQueryConditions(dto.getAssetQueryConditions())
                                .setInventoryHistoryQueries(dto.getInventoryHistoryQueries()));
        inventoryConfig.setInventoryGroup(rangeGroups);
        if (!inventoryConfigService.save(inventoryConfig)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }

        // 4、分配任务
        List<InventoryDispatchRecordDto> dispatchDetail = dto.getDispatchDetail();
        List<AsInventoryTask> inventoryTasks = Lists.newArrayList();
        for (InventoryDispatchRecordDto inventoryDispatchRecordDto : dispatchDetail) {
            AsInventoryTask inventoryTask = new AsInventoryTask();
            inventoryTask.setInventoryId(inventoryId)
                    .setId(inventoryDispatchRecordDto.getTaskId())
                    .setName(inventoryDispatchRecordDto.getTaskName())
                    .setAssetNum(inventoryDispatchRecordDto.getAssetNum())
                    .setInventoryUsers(inventoryDispatchRecordDto.getInventoryUser().stream()
                            .map(InventoryDispatchDetailDto.InventoryDispatchGroupDto::getValue).collect(toList()))
                    .setTaskType(InventoryConstant.TASK_TYPE_1);
            inventoryTasks.add(inventoryTask);
        }

        if (!inventoryTaskService.saveBatch(inventoryTasks)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }

        // 5、批量写入快照盘点资产
        try {
            FormVO formVO = formService.assetTpl();
            List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formVO.getFormFields());
            Map<Long, JSONObject> assetJsonMap = new HashMap<>();
            for (AssetDto assetDto : assetDtos) {
                assetJsonMap.put(assetDto.getId(), assetDto.translate());
            }
            assetUtil.translateAssetJsonBatch(new ArrayList<>(assetJsonMap.values()), translations);

            int size = 4000;
            int len = assetDtos.size();
            int loop = len / size;
            int last = len % size;
            CountDownLatch latch = new CountDownLatch(loop);
            for (int i = 0; i <= loop; i++) {
                final int k = i;
                taskExecutor.execute(() -> {
                    int length = (k + 1) * size;
                    if (k + 1 == loop) {
                        length += last;
                    }
                    if (loop == 0) {
                        length = last;
                    }

                    List<AsInventoryAsset> inventoryAssets = Lists.newArrayList();
                    List<AsInventoryTaskAsset> inventoryTaskAssets = Lists.newArrayList();

                    for (int j = k * size; j < length; j++) {
                        AssetDto assetDto = assetDtos.get(j);
                        AssetDetailDto assetDetailDto = assetDto.getAssetData().toJavaObject(AssetDetailDto.class);

                        JSONObject translate = assetJsonMap.get(assetDto.getId());

                        // 盘点资产数据
                        AsInventoryAsset asInventoryAsset = new AsInventoryAsset();
                        asInventoryAsset.setInventoryId(inventoryId).setAssetId(assetDto.getId())
                                .setStatus(assetDto.getStatus()).setAssetSnapshotData(translate);
                        // 确定盘点任务和盘点人
                        ascertainInventoryUserAndTaskId(asInventoryAsset, assetDetailDto, dto.getDispatchMode(),
                                dispatchDetail);
                        inventoryAssets.add(asInventoryAsset);
                        inventoryTaskAssets.add(
                                new AsInventoryTaskAsset()
                                        .setInventoryId(inventoryId)
                                        .setInventoryTaskId(asInventoryAsset.getInventoryTaskId())
                                        .setAssetId(asInventoryAsset.getAssetId())
                                        .setAssetOriginalData(translate)
                                        .setStatus(InventoryConstant.INVENTORY_TASK_ASSET_STATUS_NOT_CHECK)
                                        .setAssetMark(InventoryConstant.INVENTORY_TASK_ASSET_MARK_NONE));
                    }

                    if (!(inventoryAssetService.saveBatch(inventoryAssets, size)
                            && inventoryTaskAssetService.saveBatch(inventoryTaskAssets, size))) {
                        throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
                    }

                    latch.countDown();
                });

            }

            latch.await();

        } catch (Exception e) {
            log.error("获取盘点资产数据失败", e);
            throw new BusinessException(InventoryResultCode.CREATE_INVENTORY_FAIL);
        }

        // 6、新建盘点单，发送盘点代办消息
        inventoryTodoService.sendInventoryTaskTodoMsg(inventoryTasks, asInventory);

        // 7. 清理预创建盘点单资产
        inventoryQueryAssetService.delete(dto.getQueryId());
        redisService.del(RedisConstant.inventoryAssetTotalNumKey(dto.getQueryId()));
        return inventoryId;
    }

    /**
     * 单个任务驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejected(InventoryTaskApproveDto approveDto) {
        Long taskId = approveDto.getTaskId();
        // 获取盘点任务
        AsInventoryTask byId = inventoryTaskService.getById(taskId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        if (!InventoryConstant.PEND_APPROVAL.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PEND_APPROVAL);
        }

        // 获取盘点单
        AsInventory inventory = this.getById(byId.getInventoryId());
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是审核人
        if (!currentUserId.equals(inventory.getApprover())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_APPROVAL);
        }

        // 1、修改任务状态为进行中
        AsInventoryTask inventoryTask = new AsInventoryTask();
        inventoryTask.setId(taskId).setStatus(InventoryConstant.REJECTED)
                .setLastApproveTime(LocalDateTime.now());
        if (!inventoryTaskService.updateById(inventoryTask)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_APPROVAL_FAIL);
        }

        // 2、修改盘点单状态为进行中
        AsInventory newInventory = new AsInventory();
        newInventory.setId(byId.getInventoryId()).setStatus(InventoryConstant.IN_PROGRESS);
        if (!this.updateById(newInventory)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_APPROVAL_FAIL);
        }

        // 3、添加审核记录表
        inventoryApproveService.addRejectedRecord(byId.getInventoryId(), taskId, approveDto.getOpinion());

        // 4、单个任务驳回, 发送代办
        // 发送单个子任务代办，修改审核人代办为已完成(总任务是待审核时需要修改)
        inventoryTodoService.sendInventoryTaskTodoMsg(ListUtil.of(byId), inventory);

        // 修改审核人代办为已完成(总任务是待审核时需要修改)
//        if (InventoryConstant.PEND_APPROVAL.equals(inventory.getStatus())) {
        inventoryTodoService.updateInventoryApproveTodoMsg(inventory, taskId);
//        }

        return true;
    }

    /**
     * 整单驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejectedAll(InventoryApproveSubmitDto approveDto) {
        Long inventoryId = approveDto.getInventoryId();
        AsInventory byId = this.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!InventoryConstant.PEND_APPROVAL.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PEND_APPROVAL);
        }
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是审核人
        if (!currentUserId.equals(byId.getApprover())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_APPROVAL);
        }

        // 获取盘点任务明细
        List<AsInventoryTask> inventoryTaskList = inventoryTaskService.list(Wrappers.<AsInventoryTask>lambdaQuery()
                .eq(AsInventoryTask::getInventoryId, inventoryId)
                .eq(AsInventoryTask::getTaskType, InventoryConstant.TASK_TYPE_1));
        // 获取任务ids
        List<Long> taskIds = inventoryTaskList.stream().map(AsInventoryTask::getId).collect(Collectors.toList());

        // 1、修改所有任务状态为进行中
        LambdaUpdateWrapper<AsInventoryTask> updateWrapper = Wrappers.<AsInventoryTask>lambdaUpdate()
                .set(AsInventoryTask::getStatus, InventoryConstant.REJECTED)
                .set(AsInventoryTask::getLastApproveTime, LocalDateTime.now())
                .in(AsInventoryTask::getId, taskIds);
        inventoryTaskService.update(updateWrapper);

        // 2、修改盘点单状态为进行中
        AsInventory inventory = new AsInventory();
        inventory.setId(inventoryId).setStatus(InventoryConstant.IN_PROGRESS);
        this.updateById(inventory);

        // 3、添加审核记录表
        inventoryApproveService.addRejectedRecord(inventoryId, taskIds, approveDto.getOpinion());

        // 4、整单驳回，发送代办
        // 发送所有子任务代办
        inventoryTodoService.sendInventoryTaskTodoMsg(inventoryTaskList, byId);
        // 修改审核人代办为已完成
        inventoryTodoService.updateInventoryApproveTodoMsg(byId, null);

        return true;
    }

    /**
     * 整单同意
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean approved(InventoryApproveSubmitDto approveDto) {
        Long inventoryId = approveDto.getInventoryId();
        AsInventory byId = this.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!InventoryConstant.PEND_APPROVAL.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PEND_APPROVAL);
        }
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是审核人
        if (!currentUserId.equals(byId.getApprover())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_APPROVAL);
        }

        // 获取盘点任务明细
        List<AsInventoryTask> inventoryTaskList = inventoryTaskService.list(Wrappers.<AsInventoryTask>lambdaQuery()
                .eq(AsInventoryTask::getInventoryId, inventoryId)
                .eq(AsInventoryTask::getTaskType, InventoryConstant.TASK_TYPE_1));
        // 获取任务ids
        List<Long> taskIds = inventoryTaskList.stream().map(AsInventoryTask::getId).collect(Collectors.toList());

        // 1、修改所有任务状态为已完成
        LambdaUpdateWrapper<AsInventoryTask> updateWrapper = Wrappers.<AsInventoryTask>lambdaUpdate()
                .set(AsInventoryTask::getStatus, InventoryConstant.COMPLETED)
                .set(AsInventoryTask::getLastApproveTime, LocalDateTime.now())
                .eq(AsInventoryTask::getInventoryId, inventoryId);
        if (!inventoryTaskService.update(updateWrapper)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_APPROVAL_FAIL);
        }

        // 2、修改盘点单状态为已完成
        AsInventory inventory = new AsInventory();
        inventory.setId(inventoryId).setStatus(InventoryConstant.COMPLETED).setCompleteTime(LocalDateTime.now());
        this.updateById(inventory);

        handledInventory(inventory.getId());

        // 3、添加审核记录表
        inventoryApproveService.addApprovedRecord(inventoryId, taskIds, approveDto.getOpinion());

        // 4、修改审核人代办为已完成
        inventoryTodoService.updateInventoryApproveTodoMsg(byId, null);

        // 发送盘点审核完成事件
        EventPublishHandler.publish(new CloseInventoryEvent(LoginUserThreadLocal.getCompanyId(),
                inventoryId));
        return true;
    }

    @Override
    public Boolean approvedOne(InventoryTaskApproveDto approveDto) {
        Long taskId = approveDto.getTaskId();
        // 获取盘点任务
        AsInventoryTask byId = inventoryTaskService.getById(taskId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        if (!InventoryConstant.PEND_APPROVAL.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PEND_APPROVAL);
        }

        // 获取盘点单
        AsInventory inventory = this.getById(byId.getInventoryId());
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是审核人
        if (!currentUserId.equals(inventory.getApprover())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_APPROVAL);
        }

        // 1、修改任务状态为审核通过
        AsInventoryTask inventoryTask = new AsInventoryTask();
        inventoryTask.setId(taskId).setStatus(InventoryConstant.COMPLETED)
                .setLastApproveTime(LocalDateTime.now());
        inventoryTaskService.updateById(inventoryTask);

        Boolean handle = handledInventory(inventory.getId());
        if (BooleanUtil.isTrue(handle)) {
            updateById(new AsInventory().setId(byId.getInventoryId()).setCompleteTime(LocalDateTime.now()));
        }

        // 2、添加审核记录表
        inventoryApproveService.addApprovedRecord(byId.getInventoryId(), taskId, approveDto.getOpinion());

        // 3、单个同意，发送代办
        // 发送单个子任务代办，修改审核人代办为已完成(总任务是待审核时需要修改)
        // 先注释，这里不应该发送todo，原先却调用了，2023年3月15日14:24:56
//        inventoryTodoService.sendInventoryTodoMsg(ListUtil.of(byId), inventory);

        // 修改审核人代办为已完成(总任务是待审核时需要修改)
//        if (InventoryConstant.PEND_APPROVAL.equals(inventory.getStatus())) {
        inventoryTodoService.updateInventoryApproveTodoMsg(inventory, taskId);
//        }

        return true;
    }

    @Override
    public Boolean updateApprover(InventoryUpdateApproverDto approveDto) {
        Long inventoryId = approveDto.getId();
        // 修改审核人
        AsInventory byId = this.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        Long originApprover = byId.getApprover();
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是创建人或者超管
        if (!currentUserId.equals(byId.getCreateBy()) && !BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_UPDATE_APPROVER);
        }
        byId.setApprover(approveDto.getApproverId());
        this.updateById(byId);
        // 查询还未处理的待办
        List<AsTodo> todoList = todoService.list(Wrappers.lambdaQuery(AsTodo.class)
                .eq(AsTodo::getOrderType, AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE)
                .eq(AsTodo::getBusinessId, inventoryId)
                .eq(AsTodo::getIsHandle, false));
        // 删除待办消息
        inventoryTodoService.deleteInventoryApproveTodoMsg(inventoryId, originApprover);
        // 发送待办消息
        if (CollUtil.isNotEmpty(todoList)) {
            inventoryTodoService.sendInventoryApproveTodoMsg(byId, todoList);
        }
        /*EventPublishHandler.publish(new UpdateApproverEvent(
                inventoryId, LoginUserThreadLocal.getCompanyId(), originApprover));*/
        return true;
    }

    /**
     * 终止
     *
     * @param inventoryId
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean stop(Long inventoryId) {
        AsInventory byId = this.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        // 进行中和待审核的状态才能终止
        List<Integer> canStatusList = Arrays.asList(InventoryConstant.PEND_APPROVAL, InventoryConstant.IN_PROGRESS);
        if (!canStatusList.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_CAN_STOP);
        }
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是创建人或者超管
        if (!currentUserId.equals(byId.getCreateBy()) && !BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_STOP);
        }

        // 获取所有的子任务id
        List<AsInventoryTask> inventoryTaskList = inventoryTaskService.list(Wrappers.<AsInventoryTask>lambdaQuery()
                .eq(AsInventoryTask::getInventoryId, inventoryId));
        List<Long> taskIds = inventoryTaskList.stream().map(AsInventoryTask::getId).collect(toList());

        // 2、修改所有任务状态为已终止
        LambdaUpdateWrapper<AsInventoryTask> updateWrapper = Wrappers.<AsInventoryTask>lambdaUpdate()
                .set(AsInventoryTask::getStatus, InventoryConstant.TERMINATED)
                .eq(AsInventoryTask::getInventoryId, inventoryId);
        if (!inventoryTaskService.update(updateWrapper)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 3、修改盘点单状态为已终止
        AsInventory inventory = new AsInventory();
        inventory.setId(inventoryId).setStatus(InventoryConstant.TERMINATED);
        if (!this.updateById(inventory)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 4、删除所有子任务代办和审核人代办
        inventoryTodoService.deleteInventoryTodoMsg(taskIds, inventoryId);
        // 5.异步发送消息通知
        messageService.sendInnerMessage(MsgSendCmd.pdzz(inventoryId));
        // 发送盘点审核完成事件
        EventPublishHandler.publish(new CloseInventoryEvent(LoginUserThreadLocal.getCompanyId(),
                inventoryId));
        return true;
    }

    /**
     * 盘点单删除
     *
     * @param inventoryId
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeByInventoryId(Long inventoryId) {
        AsInventory byId = this.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        // 终止状态才能删除
        if (!InventoryConstant.TERMINATED.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_CAN_REMOVE);
        }
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是创建人或者超管
        if (!currentUserId.equals(byId.getCreateBy()) && !BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_REMOVE);
        }

        // 删除盘点单相关表
        this.baseMapper.deleteById(inventoryId);
        this.baseMapper.removeInventoryApprove(inventoryId);
        this.baseMapper.removeInventoryAsset(inventoryId);
        this.baseMapper.removeInventoryAssetLog(inventoryId);
        this.baseMapper.removeInventoryConfig(inventoryId);
        this.baseMapper.removeInventoryReport(inventoryId);
        this.baseMapper.removeInventorySurplus(inventoryId);
        this.baseMapper.removeInventoryTask(inventoryId);
        inventoryTaskAssetService.removeByInventoryId(inventoryId);

        return true;
    }

    /**
     * 盘点单概览
     *
     * @param id
     * @return 结果
     */
    @Override
    public InventoryView getInventoryView(Long id) {
        // 获取盘点单数据
        AsInventory inventory = this.getById(id);
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        // 获取盘点单配置数据
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(id);
        if (null == inventoryConfig) {
            throw new BusinessException(InventoryResultCode.INVENTORY_CONFIG_NOT_EXIST);
        }

        InventoryConfigDto inventoryConfigDto = new InventoryConfigDto();
        BeanUtil.copyProperties(inventoryConfig, inventoryConfigDto);
        // 设置盘点人
        inventoryConfigDto.getDispatchDetail().forEach(d -> {
            StringJoiner inventoryUserText = new StringJoiner(", ");
            d.getInventoryUser().forEach(u -> {
                inventoryUserText.add(cacheResourceUtil.getUserNameAndCode(u.getValue()));
            });
            d.setInventoryUsersText(inventoryUserText.toString());
        });
        InventoryView inventoryView = new InventoryView();
        BeanUtil.copyProperties(inventory, inventoryView);
        inventoryView.setConfig(inventoryConfigDto);

        return inventoryView;
    }

    /**
     * 盘点头部统计
     *
     * @return 结果
     */
    @Override
    public InventoryCountDto countByStatus() {
        return this.baseMapper.countByStatus(LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId());
    }

    /**
     * 设置盘点人
     *
     * @param dto 资产查询条件
     * @return 结果
     */
   /* @Override
    public List<Map<String, ?>> getInventoryUser(InventoryAssetRangeDto dto) {
        // 查询资产数据
        List<AssetDto> assetDtos = findInventoryAssets(
                dto.getRangeType(),
                dto.getEnableExcludeRepair(),
                dto.getEnableExcludeDispose(),
                dto.getAssetQueryConditions(),
                dto.getInventoryHistoryQueries());
        if (CollUtil.isEmpty(assetDtos)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        // 查询所有账号员工
        List<Long> empIdList =
                accountEmployeeService.list(Wrappers.lambdaQuery(AsAccountEmployee.class).eq(AsAccountEmployee::getCompanyId
                                , LoginUserThreadLocal.getCompanyId()))
                        .stream().map(AsAccountEmployee::getEmployeeId)
                        .collect(Collectors.toList());
        // 根据资产ids获取有权限的人
        List<Long> userByAssetIds = getUserByAssetIds(assetDtos, empIdList);

        List<AsCusEmployee> list = cusEmployeeService.listByIds(userByAssetIds);
        return list.stream().map(item ->
                ImmutableMap.of("label", item.getEmpName(), "value", item.getId())
        ).collect(Collectors.toList());
    }*/

    /**
     * 盘点分配方式详细
     *
     * @param dto 资产查询条件
     * @return 结果
     */
    /*@Override
    public InventoryDispatchDetailDto getDispatchDetail(InventoryAssetRangeWithModeDto dto) {
        // 查询资产数据
        List<AssetDto> assetDtos = findInventoryAssets(
                dto.getRangeType(),
                dto.getEnableExcludeRepair(),
                dto.getEnableExcludeDispose(),
                dto.getAssetQueryConditions(),
                dto.getInventoryHistoryQueries());
        if (CollUtil.isEmpty(assetDtos)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        int assetTotal = assetDtos.size();
        // 资产ids集合
        List<Long> assetIds = assetDtos.stream().map(AssetDto::getId).collect(Collectors.toList());

        // 查询user集合
        // 查询所有账号员工
        List<Long> empIdList = accountEmployeeService.list(Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId()))
                .stream().map(AsAccountEmployee::getEmployeeId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(empIdList)) {
            List<Long> ids = employeeExtService.list(
                    Wrappers.lambdaQuery(AsCusEmployeeExt.class)
                            .select(AsCusEmployeeExt::getId)
                            .eq(AsCusEmployeeExt::getAccountStatus, 3)
                            .in(AsCusEmployeeExt::getId, empIdList)
            ).stream().map(AsCusEmployeeExt::getId).collect(toList());
            empIdList.clear();
            empIdList.addAll(ids);
        }
        List<Long> assetAdminIds = cusEmployeeService.getAssetAdminIds(LoginUserThreadLocal.getCompanyId());
        Map<Long, String> cusEmpMap = new ConcurrentHashMap<>();

        // 当前企业下所有的员工
        List<AsCusEmployee> employees = cusEmployeeService.list(Wrappers.lambdaQuery(AsCusEmployee.class)
                .select(AsCusEmployee::getId, AsCusEmployee::getEmpName, AsCusEmployee::getEmpNo)
                .eq(AsCusEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        Map<Long, String> empMap = employees.parallelStream()
                .peek(o -> {
                    if (empIdList.contains(o.getId())) {
                        cusEmpMap.put(o.getId(), o.getEmpName() + (StrUtil.isNotBlank(o.getEmpNo()) ? "（" + o.getEmpNo() + "）" : ""));
                    }
                })
                .collect(Collectors.toConcurrentMap(AsCusEmployee::getId, o -> o.getEmpName() + (StrUtil.isNotBlank(o.getEmpNo()) ? "（" + o.getEmpNo() + "）" : "")));

        String groupByStr;
        List<InventoryDispatchRecordDto> dispatchDetailList = new ArrayList<>();

        // 获取组织数据
        List<AsOrg> orgData = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                .select(AsOrg::getId, AsOrg::getOrgName)
                .eq(AsOrg::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        Map<Long, String> orgMap = orgData.parallelStream().collect(Collectors.toConcurrentMap(AsOrg::getId, AsOrg::getOrgName));
        // 盘点方式
        Integer dispatchMode = dto.getDispatchMode();
        // 排序标记
        List<InventoryDispatchRecordDto> firstList = new CopyOnWriteArrayList<>();
        List<InventoryDispatchRecordDto> secondList = new CopyOnWriteArrayList<>();
        List<InventoryDispatchRecordDto> dispatchDetailTmp = new CopyOnWriteArrayList<>();

        AtomicInteger count = new AtomicInteger(0);
        boolean weixinEdition = Edition.isWeixin();
        switch (dispatchMode) {
            case InventoryConstant.DISPATCH_ORG_OWNER:
                groupByStr = "org_owner";
                List<InventoryDispatchGroupAssetDto> list = assetService.selectAssetNumGroup(groupByStr, assetIds);
                list.forEach(item -> {
                    // 根据id获取有权限的人员
                    Long orgOwner = item.getOrgOwner();
                    String orgOwnerStr = (ObjectUtil.isNull(orgOwner) || 0 == orgOwner) ? "" : orgOwner.toString();

                    // 获取名称
                    String orgName = orgMap.getOrDefault(orgOwner, "所属管理组织为空");
                    if (orgOwner != null && orgOwner != 0 && StrUtil.isBlank(orgMap.get(orgOwner))) {
                        orgName = "所属管理组织已删除";
                    }
                    String taskName = orgName + "（所属管理组织）";
                    if (weixinEdition && orgMap.containsKey(orgOwner)) {
                        taskName = "任务" + count.incrementAndGet() + "（所属管理组织）";
                    }

                    List<AssetDto> asAssetList = assetDtos.stream().filter(asset ->
                            orgOwnerStr.equals(asset.getAssetData().getString("orgOwner"))).collect(Collectors.toList());

                    // 根据资产ids获取有权限的人
                    List<Long> userByAssetIds;
                    if (Objects.equals(true, dto.getEnableNoPermLook()) || cusEmpMap.size() > 500) {
                        userByAssetIds = new ArrayList<>(cusEmpMap.keySet());
                    } else {
                        userByAssetIds = getUserByAssetIds(asAssetList, empIdList);
                    }

                    //处理盘点人
                    Integer assetNum = item.getAssetNum();
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> userDto = getUserDto(userByAssetIds,
                            cusEmpMap, assetNum);

                    // 默认盘点人, 从有权限的人员里面过滤出当前组织下的资产管理员
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> defaultUserDto = new ArrayList<>();
                    if (assetAdminIds.size() <= 500) {
                        List<Long> hasPermAssetAdminIds = getDefaultUserByAssetIds(dispatchMode, asAssetList, assetAdminIds);
                        defaultUserDto = hasPermAssetAdminIds.stream().map(assetAdminId -> {
                            InventoryDispatchDetailDto.InventoryDispatchGroupDto o =
                                    new InventoryDispatchDetailDto.InventoryDispatchGroupDto();
                            String name = cusEmpMap.getOrDefault(assetAdminId, "");
                            o.setValue(assetAdminId).setLabel(name);
                            return o;
                        }).collect(toList());
                    }
                    InventoryDispatchRecordDto inventoryDispatchRecordDto = new InventoryDispatchRecordDto();
                    inventoryDispatchRecordDto.setTaskId(IdUtils.getId())
                            .setIds(ListUtil.of(item.getOrgOwner()))
                            .setNames(ListUtil.of(orgName))
                            .setTaskName(taskName)
                            .setAssetNum(assetNum)
                            .setInventoryUser(userDto)
                            .setDefaultInventoryUser(defaultUserDto);

                    if (orgOwner == null) {
                        firstList.add(inventoryDispatchRecordDto);
                    } else {
                        if (!orgMap.containsKey(orgOwner)) {
                            secondList.add(inventoryDispatchRecordDto);
                        } else {
                            dispatchDetailTmp.add(inventoryDispatchRecordDto);
                        }
                    }
                });
                // 排序
                dispatchDetailList.addAll(firstList);
                dispatchDetailList.addAll(secondList);
                dispatchDetailList.addAll(dispatchDetailTmp);
                break;
            case InventoryConstant.DISPATCH_MANAGER_OWNER:
                groupByStr = "manager_owner";
                List<InventoryDispatchGroupAssetDto> managerList = assetService.selectAssetNumGroup(groupByStr, assetIds);
                managerList.forEach(item -> {
                    // 根据id获取有权限的人员
                    Long managerOwner = item.getManagerOwner();
                    String managerOwnerStr = (ObjectUtil.isNull(managerOwner) || 0 == managerOwner) ? "" :
                            managerOwner.toString();

                    // 获取名称
                    String cusEmployeeName = empMap.getOrDefault(managerOwner, "资产管理员为空");
                    if (managerOwner != null && managerOwner != 0 && StrUtil.isBlank(empMap.get(managerOwner))) {
                        cusEmployeeName = "资产管理员已删除";
                    }

                    String taskName = cusEmployeeName + "（资产管理员）";
                    if (weixinEdition && empMap.containsKey(managerOwner)) {
                        taskName = "任务" + count.incrementAndGet() + "（资产管理员）";
                    }
                    List<AssetDto> asAssetList = assetDtos.stream().filter(asset ->
                            managerOwnerStr.equals(asset.getAssetData().getString("managerOwner"))).collect(Collectors.toList());

                    // 根据资产ids获取有权限的人
                    List<Long> userByAssetIds;
                    if (Objects.equals(true, dto.getEnableNoPermLook()) || cusEmpMap.size() > 500) {
                        userByAssetIds = new ArrayList<>(cusEmpMap.keySet());
                    } else {
                        userByAssetIds = getUserByAssetIds(asAssetList, empIdList);
                    }

                    //处理盘点人
                    Integer assetNum = item.getAssetNum();
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> userDto = getUserDto(userByAssetIds,
                            cusEmpMap, assetNum);
                    // 默认盘点人, 取资产管理员
                    // 默认盘点人, 从有权限的人员里面过滤出当前组织下的资产管理员
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> defaultUserDto = new ArrayList<>();
                    if (assetAdminIds.size() <= 500) {
                        List<Long> hasPermAssetAdminIds = getDefaultUserByAssetIds(dispatchMode, asAssetList, assetAdminIds);
                        if (managerOwner != null && managerOwner != 0) {
                            hasPermAssetAdminIds = ListUtil.toList(managerOwner);
                        }
                        defaultUserDto = hasPermAssetAdminIds.stream().map(assetAdminId -> {
                            InventoryDispatchDetailDto.InventoryDispatchGroupDto o =
                                    new InventoryDispatchDetailDto.InventoryDispatchGroupDto();
                            String name = cusEmpMap.getOrDefault(assetAdminId, "");
                            o.setValue(assetAdminId).setLabel(name);
                            return o;
                        }).collect(toList());
                    }

                    InventoryDispatchRecordDto inventoryDispatchRecordDto = new InventoryDispatchRecordDto();
                    inventoryDispatchRecordDto.setTaskId(IdUtils.getId())
                            .setIds(ListUtil.of(item.getManagerOwner()))
                            .setNames(ListUtil.of(cusEmployeeName))
                            .setTaskName(taskName)
                            .setAssetNum(assetNum)
                            .setInventoryUser(userDto)
                            .setDefaultInventoryUser(defaultUserDto);
                    if (managerOwner == null) {
                        firstList.add(inventoryDispatchRecordDto);
                    } else {
                        if (!empMap.containsKey(managerOwner)) {
                            secondList.add(inventoryDispatchRecordDto);
                        } else {
                            dispatchDetailTmp.add(inventoryDispatchRecordDto);
                        }
                    }
                });
                // 排序
                dispatchDetailList.addAll(firstList);
                dispatchDetailList.addAll(secondList);
                dispatchDetailList.addAll(dispatchDetailTmp);
                break;
            case InventoryConstant.DISPATCH_STORAGE_AREA:
                groupByStr = "storage_area";
                // 获取区域数据
                List<AsArea> areaData = areaService.list();
                Map<Long, String> areaMap = areaData.stream().collect(Collectors.toMap(AsArea::getId,
                        AsArea::getAreaName));

                List<InventoryDispatchGroupAssetDto> areaList = assetService.selectAssetNumGroup(groupByStr, assetIds);
                areaList.forEach(item -> {
                    // 根据id获取有权限的人员
                    Long area = item.getStorageArea();
                    String areaStr = (ObjectUtil.isNull(area) || 0 == area) ? "" : area.toString();

                    // 获取名称
                    String areaName = areaMap.getOrDefault(area, "区域为空");
                    if (area != null && area != 0 && StrUtil.isBlank(areaMap.get(area))) {
                        areaName = "区域已删除";
                    }
                    String taskName = areaName + "（存放区域）";
                    if (weixinEdition && areaMap.containsKey(area)) {
                        taskName = "任务" + count.incrementAndGet() + "（存放区域）";
                    }
                    List<AssetDto> asAssetList = assetDtos.stream().filter(asset ->
                            areaStr.equals(asset.getAssetData().getString("storageArea"))).collect(Collectors.toList());

                    // 根据资产ids获取有权限的人
                    List<Long> userByAssetIds;
                    if (Objects.equals(true, dto.getEnableNoPermLook()) || cusEmpMap.size() > 500) {
                        userByAssetIds = new ArrayList<>(cusEmpMap.keySet());
                    } else {
                        userByAssetIds = getUserByAssetIds(asAssetList, empIdList);
                    }

                    //处理盘点人
                    Integer assetNum = item.getAssetNum();
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> userDto = getUserDto(userByAssetIds,
                            cusEmpMap, assetNum);
                    // 默认盘点人, 取当前区域所属组织下的资产管理员
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> defaultUserDto = new ArrayList<>();
                    if (assetAdminIds.size() <= 500) {
                        List<Long> hasPermAssetAdminIds = getDefaultUserByAssetIds(dispatchMode, asAssetList, assetAdminIds);
                        defaultUserDto = hasPermAssetAdminIds.stream().map(assetAdminId -> {
                            InventoryDispatchDetailDto.InventoryDispatchGroupDto o =
                                    new InventoryDispatchDetailDto.InventoryDispatchGroupDto();
                            String name = cusEmpMap.getOrDefault(assetAdminId, "");
                            o.setValue(assetAdminId).setLabel(name);
                            return o;
                        }).collect(toList());
                    }
                    InventoryDispatchRecordDto inventoryDispatchRecordDto = new InventoryDispatchRecordDto();
                    inventoryDispatchRecordDto.setTaskId(IdUtils.getId())
                            .setIds(ListUtil.of(area))
                            .setNames(ListUtil.of(areaName))
                            .setTaskName(taskName)
                            .setAssetNum(assetNum)
                            .setInventoryUser(userDto)
                            .setDefaultInventoryUser(defaultUserDto);
                    if (area == null) {
                        firstList.add(inventoryDispatchRecordDto);
                    } else {
                        if (StrUtil.isBlank(areaMap.get(area))) {
                            secondList.add(inventoryDispatchRecordDto);
                        } else {
                            dispatchDetailTmp.add(inventoryDispatchRecordDto);
                        }
                    }
                });
                // 排序
                dispatchDetailList.addAll(firstList);
                dispatchDetailList.addAll(secondList);
                dispatchDetailList.addAll(dispatchDetailTmp);
                break;
            case InventoryConstant.DISPATCH_ASSET_CATEGORY:
                groupByStr = "asset_category";

                // 获取分类数据
                List<AsCategory> categoryData = categoryService.list();
                Map<Long, String> categoryMap = categoryData.stream().collect(Collectors.toMap(AsCategory::getId,
                        AsCategory::getCategoryName));

                List<InventoryDispatchGroupAssetDto> cateList = assetService.selectAssetNumGroup(groupByStr, assetIds);
                cateList.forEach(item -> {
                    // 根据id获取有权限的人员
                    Long assetCategory = item.getAssetCategory();
                    String assetCategoryStr = (ObjectUtil.isNull(assetCategory) || 0 == assetCategory) ? "" :
                            assetCategory.toString();

                    // 获取名称
                    String categoryName = categoryMap.getOrDefault(assetCategory, "资产分类为空");
                    if (assetCategory != null && assetCategory != 0 && StrUtil.isBlank(categoryMap.get(assetCategory))) {
                        categoryName = "资产分类已删除";
                    }
                    String taskName = categoryName + "（资产分类）";
                    if (weixinEdition && categoryMap.containsKey(assetCategory)) {
                        taskName = "任务" + count.incrementAndGet() + "（资产分类）";
                    }
                    List<AssetDto> asAssetList = assetDtos.stream().filter(asset ->
                            assetCategoryStr.equals(asset.getAssetData().getString("assetCategory"))).collect(Collectors.toList());

                    // 根据资产ids获取有权限的人
                    List<Long> userByAssetIds;
                    if (Objects.equals(true, dto.getEnableNoPermLook()) || cusEmpMap.size() > 500) {
                        userByAssetIds = new ArrayList<>(cusEmpMap.keySet());
                    } else {
                        userByAssetIds = getUserByAssetIds(asAssetList, empIdList);
                    }

                    //处理盘点人
                    Integer assetNum = item.getAssetNum();
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> userDto = getUserDto(userByAssetIds,
                            cusEmpMap, assetNum);
                    // 默认盘点人, 从有权限的人员里面过滤出资产管理员
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> defaultUserDto = new ArrayList<>();
                    if (assetAdminIds.size() <= 500) {
                        List<Long> hasPermAssetAdminIds = getDefaultUserByAssetIds(dispatchMode, asAssetList, assetAdminIds);
                        defaultUserDto = hasPermAssetAdminIds.stream().map(assetAdminId -> {
                            InventoryDispatchDetailDto.InventoryDispatchGroupDto o =
                                    new InventoryDispatchDetailDto.InventoryDispatchGroupDto();
                            String name = cusEmpMap.getOrDefault(assetAdminId, "");
                            o.setValue(assetAdminId).setLabel(name);
                            return o;
                        }).collect(toList());
                    }
                    InventoryDispatchRecordDto inventoryDispatchRecordDto = new InventoryDispatchRecordDto();
                    inventoryDispatchRecordDto.setTaskId(IdUtils.getId()).setIds(ListUtil.of(assetCategory)).setNames(ListUtil.of(categoryName)).setTaskName(taskName)
                            .setAssetNum(assetNum).setInventoryUser(userDto).setDefaultInventoryUser(defaultUserDto);
                    if (assetCategory == null) {
                        firstList.add(inventoryDispatchRecordDto);
                    } else {
                        if (StrUtil.isBlank(categoryMap.get(assetCategory))) {
                            secondList.add(inventoryDispatchRecordDto);
                        } else {
                            dispatchDetailTmp.add(inventoryDispatchRecordDto);
                        }
                    }
                });
                // 排序
                dispatchDetailList.addAll(firstList);
                dispatchDetailList.addAll(secondList);
                dispatchDetailList.addAll(dispatchDetailTmp);
                break;
            case InventoryConstant.DISPATCH_USE_ORG:
                groupByStr = "use_org";
                List<InventoryDispatchGroupAssetDto> useOrgList = assetService.selectAssetNumGroup(groupByStr, assetIds);
                useOrgList.forEach(item -> {
                    // 根据id获取有权限的人员
                    Long useOrg = item.getUseOrg();
                    String useOrgStr = (ObjectUtil.isNull(useOrg) || 0 == useOrg) ? "" : useOrg.toString();
                    // 获取名称
                    String orgName = orgMap.getOrDefault(useOrg, "使用组织为空");
                    if (useOrg != null && useOrg != 0 && StrUtil.isBlank(orgMap.get(useOrg))) {
                        orgName = "使用组织已删除";
                    }
                    String taskName = orgName + "（使用组织）";
                    if (weixinEdition && orgMap.containsKey(useOrg)) {
                        taskName = "任务" + count.incrementAndGet() + "（使用组织）";
                    }
                    List<AssetDto> asAssetList = assetDtos.stream().filter(asset ->
                            useOrgStr.equals(asset.getAssetData().getString("useOrg"))).collect(Collectors.toList());

                    // 根据资产ids获取有权限的人
                    List<Long> userByAssetIds;
                    if (Objects.equals(true, dto.getEnableNoPermLook()) || cusEmpMap.size() > 500) {
                        userByAssetIds = new ArrayList<>(cusEmpMap.keySet());
                    } else {
                        userByAssetIds = getUserByAssetIds(asAssetList, empIdList);
                    }

                    //处理盘点人
                    Integer assetNum = item.getAssetNum();
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> userDto = getUserDto(userByAssetIds,
                            cusEmpMap, assetNum);
                    // 默认盘点人, 从有权限的人员里面过滤出当前使用组织的资产管理员
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> defaultUserDto = new ArrayList<>();
                    if (assetAdminIds.size() <= 500) {
                        List<Long> hasPermAssetAdminIds = getDefaultUserByAssetIds(dispatchMode, asAssetList, assetAdminIds);
                        defaultUserDto = hasPermAssetAdminIds.stream().map(assetAdminId -> {
                            InventoryDispatchDetailDto.InventoryDispatchGroupDto o =
                                    new InventoryDispatchDetailDto.InventoryDispatchGroupDto();
                            String name = cusEmpMap.getOrDefault(assetAdminId, "");
                            o.setValue(assetAdminId).setLabel(name);
                            return o;
                        }).collect(toList());
                    }
                    InventoryDispatchRecordDto inventoryDispatchRecordDto = new InventoryDispatchRecordDto();
                    inventoryDispatchRecordDto.setTaskId(IdUtils.getId()).setIds(ListUtil.of(item.getUseOrg())).setNames(ListUtil.of(orgName)).setTaskName(taskName)
                            .setAssetNum(assetNum).setInventoryUser(userDto).setDefaultInventoryUser(defaultUserDto);
                    if (useOrg == null) {
                        firstList.add(inventoryDispatchRecordDto);
                    } else {
                        if (StrUtil.isBlank(orgMap.get(useOrg))) {
                            secondList.add(inventoryDispatchRecordDto);
                        } else {
                            dispatchDetailTmp.add(inventoryDispatchRecordDto);
                        }
                    }
                });
                // 排序
                dispatchDetailList.addAll(firstList);
                dispatchDetailList.addAll(secondList);
                dispatchDetailList.addAll(dispatchDetailTmp);
                break;
            case InventoryConstant.DISPATCH_USE_PERSON:
                groupByStr = "use_person";
                List<InventoryDispatchGroupAssetDto> personList = assetService.selectAssetNumGroup(groupByStr,
                        assetIds);
                personList.forEach(item -> {
                    // 根据id获取有权限的人员
                    Long usePerson = item.getUsePerson();
                    String usePersonStr = (ObjectUtil.isNull(usePerson) || 0 == usePerson) ? "" : usePerson.toString();

                    // 获取名称
                    String cusEmployeeName = empMap.getOrDefault(usePerson, "使用人为空");
                    if (usePerson != null && usePerson != 0 && StrUtil.isBlank(empMap.get(usePerson))) {
                        cusEmployeeName = "使用人已删除";
                    }
                    String taskName = cusEmployeeName + "（资产使用人）";
                    if (weixinEdition && empMap.containsKey(usePerson)) {
                        taskName = "任务" + count.incrementAndGet() + "（资产使用人）";
                    }
                    List<AssetDto> asAssetList = assetDtos.stream().filter(asset ->
                            usePersonStr.equals(asset.getAssetData().getString("usePerson"))).collect(Collectors.toList());

                    // 根据资产ids获取有权限的人
                    List<Long> userByAssetIds;
                    if (Objects.equals(true, dto.getEnableNoPermLook()) || cusEmpMap.size() > 500) {
                        userByAssetIds = new ArrayList<>(cusEmpMap.keySet());
                    } else {
                        userByAssetIds = getUserByAssetIds(asAssetList, empIdList);
                    }

                    //处理盘点人
                    Integer assetNum = item.getAssetNum();
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> userDto = getUserDto(userByAssetIds,
                            cusEmpMap, assetNum);
                    // 默认盘点人, 取资产使用人
                    // 默认盘点人, 从有权限的人员里面过滤出当前组织下的资产管理员
                    List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> defaultUserDto = new ArrayList<>();
                    if (assetAdminIds.size() <= 500) {
                        List<Long> hasPermAssetAdminIds = getDefaultUserByAssetIds(dispatchMode, asAssetList, assetAdminIds);
                        if (usePerson != null && usePerson != 0) {
                            hasPermAssetAdminIds = ListUtil.toList(usePerson);
                        }
                        defaultUserDto = hasPermAssetAdminIds.stream().map(assetAdminId -> {
                            InventoryDispatchDetailDto.InventoryDispatchGroupDto o =
                                    new InventoryDispatchDetailDto.InventoryDispatchGroupDto();
                            String name = empMap.getOrDefault(assetAdminId, "");
                            o.setValue(assetAdminId).setLabel(name);
                            return o;
                        }).collect(toList());
                    }
                    InventoryDispatchRecordDto inventoryDispatchRecordDto = new InventoryDispatchRecordDto();
                    inventoryDispatchRecordDto.setTaskId(IdUtils.getId())
                            .setIds(ListUtil.of(item.getUsePerson()))
                            .setNames(ListUtil.of(cusEmployeeName))
                            .setTaskName(taskName)
                            .setAssetNum(assetNum)
                            .setInventoryUser(userDto).setDefaultInventoryUser(defaultUserDto);
                    if (usePerson == null) {
                        firstList.add(inventoryDispatchRecordDto);
                    } else {
                        if (StrUtil.isBlank(empMap.get(usePerson))) {
                            secondList.add(inventoryDispatchRecordDto);
                        } else {
                            dispatchDetailTmp.add(inventoryDispatchRecordDto);
                        }
                    }
                });

                // 排序
                dispatchDetailList.addAll(firstList);
                dispatchDetailList.addAll(secondList);
                dispatchDetailList.addAll(dispatchDetailTmp);
                break;
            default:
                throw new BusinessException(InventoryResultCode.INVENTORY_DISPATCH_NOT_EXIST);
        }

        InventoryDispatchDetailDto inventoryDispatchDetailDto = new InventoryDispatchDetailDto();
        inventoryDispatchDetailDto
                .setDispatchDetail(dispatchDetailList)
                .setAssetTotal(assetTotal)
                .setTotalEmp(cusEmpMap.size());

        return inventoryDispatchDetailDto;
    }*/

    /**
     * 盘点报告
     *
     * @param id
     * @return 结果
     */
    @Override
    public InventoryReportDto getInventoryReport(Long id) {
        // 获取盘点单数据
        AsInventory inventory = this.getById(id);
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        // 获取盘点单配置数据
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(id);
        if (null == inventoryConfig) {
            throw new BusinessException(InventoryResultCode.INVENTORY_CONFIG_NOT_EXIST);
        }

        List<InventoryDispatchRecord> dispatchDetail = inventoryConfig.getDispatchDetail();
        Map<String, String> taskMap = dispatchDetail.stream()
                .collect(Collectors.toMap(o -> Convert.toStr(o.getTaskId()), o -> String.join("、", o.getNames())));
        Map<String, String> inventoryUserMap = new ConcurrentHashMap<>();
        dispatchDetail.parallelStream().forEach(d -> {
            StringJoiner joiner = new StringJoiner("、");
            d.getInventoryUser().forEach(u -> joiner.add(cacheResourceUtil.getUserNameAndCode(u.getValue())));
            inventoryUserMap.put(Convert.toStr(d.getTaskId()), joiner.toString());
        });

        // 盘点单配置
        InventoryConfigDto inventoryConfigDto = new InventoryConfigDto();
        BeanUtil.copyProperties(inventoryConfig, inventoryConfigDto);

        InventoryReportDto inventoryReportDto = new InventoryReportDto();
        BeanUtil.copyProperties(inventory, inventoryReportDto);
        inventoryReportDto.setConfig(inventoryConfigDto);

        // 组织
        List<AsOrg> orgData = orgService.list();
        Map<String, String> orgMap = orgData.stream()
                .collect(Collectors.toConcurrentMap(o -> Convert.toStr(o.getId()), AsOrg::getOrgName));

        // 员工
        List<AsCusEmployee> empData = cusEmployeeService.list();
        Map<String, String> empMap = empData.stream()
                .collect(Collectors.toConcurrentMap(o -> Convert.toStr(o.getId()), o -> {
                    if (StrUtil.isNotBlank(o.getEmpNo())) {
                        return o.getEmpName() + "（" + o.getEmpNo() + "）";
                    }
                    return o.getEmpName();
                }));

        // 资产分类
        List<AsCategory> categoryData = categoryService.list();
        Map<String, String> categoryMap = categoryData.stream()
                .collect(Collectors.toConcurrentMap(o -> Convert.toStr(o.getId()), AsCategory::getCategoryName));

        // 获取区域数据
        List<AsArea> areaData = areaService.list();
        Map<String, String> areaMap = areaData.stream()
                .collect(Collectors.toConcurrentMap(o -> Convert.toStr(o.getId()), AsArea::getAreaName));

        // 获取资产状态数据
        List<AssetStatusDto> assetStatusData = assetStatusService.allStatus();
        Map<String, String> assetStatusMap = assetStatusData.stream()
                .collect(Collectors.toConcurrentMap(o -> Convert.toStr(o.getId()), AssetStatusDto::getName));

        // ------------------------ 盘点总结果 ------------------------
        List<InventoryResReportDto> inventoryResReportDto = this.baseMapper.countResReport(id);
        List<NewInventoryResReportDto> newInventoryResReportDtos = new ArrayList<>();
        NewInventoryResReportDto newInventoryResReportDto = NewInventoryResReportDto.of(false);
        NewInventoryResReportDto rateInventoryResReportDto = NewInventoryResReportDto.of(true);
        // 应盘数
        newInventoryResReportDto.setShallNum(Convert.toStr(this.baseMapper.countAssets(id)));
        // 盘点总数
        long assetTotal = inventoryResReportDto.stream().mapToLong(InventoryResReportDto::getNum).sum();
        // 各个状态盘点数
        for (InventoryResReportDto inventoryResReport : inventoryResReportDto) {
            BigDecimal num = BigDecimal.valueOf(inventoryResReport.getNum());
            String numStr = Convert.toStr(num.intValue());
            BigDecimal rate = divide(num, BigDecimal.valueOf(assetTotal), 4);
            String rateStr = NumberUtil.formatPercent(rate.doubleValue(), 2);
            if (inventoryResReport.getInventoryStatus() == 0) {
                newInventoryResReportDto.setLossNum(numStr);
                rateInventoryResReportDto.setLossNum(rateStr);
            } else if (inventoryResReport.getInventoryStatus() == 1) {
                newInventoryResReportDto.setNormalNum(numStr);
                rateInventoryResReportDto.setNormalNum(rateStr);
            } else if (inventoryResReport.getInventoryStatus() == 3) {
                newInventoryResReportDto.setPlusMarkNum(numStr);
                rateInventoryResReportDto.setPlusMarkNum(rateStr);
            } else if (inventoryResReport.getInventoryStatus() == 33) {
                newInventoryResReportDto.setPlusNotMarkNum(numStr);
                rateInventoryResReportDto.setPlusNotMarkNum(rateStr);
            }
        }

        newInventoryResReportDtos.add(newInventoryResReportDto);
        newInventoryResReportDtos.add(rateInventoryResReportDto);
        inventoryReportDto.setInventoryRes(newInventoryResReportDtos);
        // ------------------------ 盘点总结果 ------------------------

        // 盘点分配方式(1：所属管理组织，2：所属管理员，3：区域，4：资产分类, 5: 所属使用组织, 6: 资产使用人)

        // ------------------------ 所属管理组织盘点概况 ------------------------
        List<InventoryGroupReportDto> inventoryOrgOwnReportDtos;
        if (inventoryConfig.getDispatchMode() == InventoryConstant.DISPATCH_ORG_OWNER) {
            inventoryOrgOwnReportDtos = this.baseMapper.countTaskReport(id);
            processReportData(inventoryOrgOwnReportDtos, taskMap, "所属管理组织为空", inventoryUserMap);
        } else {
            inventoryOrgOwnReportDtos = this.baseMapper.countOrgOwnReport(id);
            processReportData(inventoryOrgOwnReportDtos, orgMap, "所属管理组织为空", null);
        }
        inventoryReportDto.setInventoryOrgOwn(inventoryOrgOwnReportDtos);
        // ------------------------ 所属管理组织盘点概况 ------------------------

        // ------------------------ 所属管理员盘点概况 ------------------------
        List<InventoryGroupReportDto> inventoryManagerOwnReportDtos;
        if (inventoryConfig.getDispatchMode() == InventoryConstant.DISPATCH_MANAGER_OWNER) {
            inventoryManagerOwnReportDtos = this.baseMapper.countTaskReport(id);
            processReportData(inventoryManagerOwnReportDtos, taskMap, "所属管理员为空", inventoryUserMap);
        } else {
            inventoryManagerOwnReportDtos = this.baseMapper.countManagerOwnReport(id);
            processReportData(inventoryManagerOwnReportDtos, empMap, "所属管理员为空", null);
        }
        inventoryReportDto.setInventoryManagerOwn(inventoryManagerOwnReportDtos);
        // ------------------------ 所属管理员盘点概况 ------------------------

        // ------------------------ 资产区域盘点概况 ------------------------
        List<InventoryGroupReportDto> inventoryAreaReportDtos;
        if (inventoryConfig.getDispatchMode() == InventoryConstant.DISPATCH_STORAGE_AREA) {
            inventoryAreaReportDtos = this.baseMapper.countTaskReport(id);
            processReportData(inventoryAreaReportDtos, taskMap, "区域为空", inventoryUserMap);
        } else {
            inventoryAreaReportDtos = this.baseMapper.countAreaReport(id);
            processReportData(inventoryAreaReportDtos, areaMap, "区域为空", null);
        }
        inventoryReportDto.setInventoryArea(inventoryAreaReportDtos);
        // ------------------------ 资产状态盘点概况 ------------------------

        // ------------------------ 资产分类盘点概况 ------------------------
        List<InventoryGroupReportDto> inventoryCategoryReportDtos;
        if (inventoryConfig.getDispatchMode() == InventoryConstant.DISPATCH_ASSET_CATEGORY) {
            inventoryCategoryReportDtos = this.baseMapper.countTaskReport(id);
            processReportData(inventoryCategoryReportDtos, taskMap, "资产分类为空", inventoryUserMap);
        } else {
            inventoryCategoryReportDtos = this.baseMapper.countCategoryReport(id);
            processReportData(inventoryCategoryReportDtos, categoryMap, "资产分类为空", null);
        }
        inventoryReportDto.setInventoryCategory(inventoryCategoryReportDtos);
        // ------------------------ 资产分类盘点概况 ------------------------

        // ------------------------ 所属使用组织盘点概况 ------------------------
        List<InventoryGroupReportDto> inventoryUseOrgReportDtos;
        if (inventoryConfig.getDispatchMode() == InventoryConstant.DISPATCH_USE_ORG) {
            inventoryUseOrgReportDtos = this.baseMapper.countTaskReport(id);
            processReportData(inventoryUseOrgReportDtos, taskMap, "所属使用组织为空", inventoryUserMap);
        } else {
            inventoryUseOrgReportDtos = this.baseMapper.countUseOrgReport(id);
            processReportData(inventoryUseOrgReportDtos, orgMap, "所属使用组织为空", null);
        }
        inventoryReportDto.setInventoryUseOrg(inventoryUseOrgReportDtos);
        // ------------------------ 所属管理组织盘点概况 ------------------------

        // ------------------------ 资产使用人盘点概况 ------------------------
        List<InventoryGroupReportDto> inventoryUsePersonReportDtos;
        if (inventoryConfig.getDispatchMode() == InventoryConstant.DISPATCH_USE_PERSON) {
            inventoryUsePersonReportDtos = this.baseMapper.countTaskReport(id);
            processReportData(inventoryUsePersonReportDtos, taskMap, "使用人为空", inventoryUserMap);
        } else {
            inventoryUsePersonReportDtos = this.baseMapper.countUsePersonReport(id);
            processReportData(inventoryUsePersonReportDtos, empMap, "使用人为空", null);
        }
        inventoryReportDto.setInventoryUsePerson(inventoryUsePersonReportDtos);
        // ------------------------ 所属管理员盘点概况 ------------------------

        // ------------------------ 资产状态盘点概况 ------------------------
        List<InventoryGroupReportDto> inventoryStatusReportDtos = this.baseMapper.countStatusReport(id);
        processReportData(inventoryStatusReportDtos, assetStatusMap, "资产状态为空", null);
        inventoryReportDto.setInventoryStatus(inventoryStatusReportDtos);
        // ------------------------ 资产状态盘点概况 ------------------------

        // 盘点结果处理
        List<InventoryResReportDto> inventoryResHandleDto = this.baseMapper.countResHandle(id);
        long assetHandleTotal = inventoryResHandleDto.parallelStream().mapToLong(InventoryResReportDto::getNum).sum();
        Map<Integer, List<InventoryResReportDto>> handleListMap = inventoryResHandleDto.stream()
                .collect(Collectors.groupingBy(InventoryResReportDto::getInventoryStatus));

        // 首先定义6个盘点状态的数组，保证顺序和数量为0的补全
        List<Integer> inventoryStatusList = ListUtil.of(3, 33, 0, 1);
        Map<Integer, List<Integer>> handleTypeListMap = handleTypeListMap();
        Map<Integer, String> statusTextListMap = statusTextListMap();
        Map<Integer, Map<Integer, String>> handleTypeTextListMap = handleTypeTextListMap();
        List<InventoryHandleResultDto> inventoryHandleResList = new ArrayList<>();
        for (Integer s : inventoryStatusList) {
            InventoryHandleResultDto res = new InventoryHandleResultDto();
            res.setInventoryStatus(s);
            res.setInventoryStatusText(statusTextListMap.get(s));
            List<Integer> handleTypeList = handleTypeListMap.get(s);
            List<InventoryResReportDto> handleList = handleListMap.getOrDefault(s, new ArrayList<>());
            Map<Integer, String> typeTextMap = handleTypeTextListMap.getOrDefault(s, new HashMap<>());
            List<InventoryResReportDto> items = new ArrayList<>();
            for (Integer h : handleTypeList) {
                Optional<InventoryResReportDto> optional = handleList.stream()
                        .filter(o -> o.getHandleType().equals(h)).findFirst();
                InventoryResReportDto resReportDto;
                if (optional.isPresent()) {
                    resReportDto = optional.get();
                    resReportDto.setHandleStatusTypeText(typeTextMap.get(h));
                    BigDecimal rate = divide(BigDecimal.valueOf(resReportDto.getNum()),
                            BigDecimal.valueOf(assetHandleTotal));
                    String rateStr = NumberUtil.formatPercent(rate.doubleValue(), 2);
                    resReportDto.setRate(rateStr);
                } else {
                    resReportDto = new InventoryResReportDto()
                            .setHandleType(h)
                            .setHandleStatusTypeText(typeTextMap.get(h))
                            .setNum(0L)
                            .setRate("0%");
                }
                items.add(resReportDto);
            }
            res.setItems(items);
            inventoryHandleResList.add(res);
        }

        inventoryReportDto.setInventoryResHandle(inventoryHandleResList);

        return inventoryReportDto;
    }

    Map<Integer, List<Integer>> handleTypeListMap() {
        Map<Integer, List<Integer>> map = new HashMap<>();
        map.put(0, ListUtil.of(1, 2, 3, 0));
        map.put(3, ListUtil.of(2, 3, 0));
        map.put(33, ListUtil.of(1, 3, 0));
        map.put(1, ListUtil.of(1, 0));
        return map;
    }

    Map<Integer, Map<Integer, String>> handleTypeTextListMap() {
        Map<Integer, Map<Integer, String>> map = new HashMap<>();
        map.put(0, MapUtil.of(Pair.of(1, "盘亏处置"), Pair.of(2, "无需处置"), Pair.of(3, "已忽略"), Pair.of(0, "未处理")));
        map.put(3, MapUtil.of(Pair.of(2, "更新资产"), Pair.of(3, "已忽略"), Pair.of(0, "未处理")));
        map.put(33, MapUtil.of(Pair.of(1, "新增入库"), Pair.of(3, "已忽略"), Pair.of(0, "未处理")));
        map.put(1, MapUtil.of(new Pair[]{Pair.of(1, "更新资产"), Pair.of(0, "未处理")}));
        return map;
    }

    Map<Integer, String> statusTextListMap() {
        Map<Integer, String> map = new HashMap<>();
        map.put(0, "盘亏（未盘）");
        map.put(3, "盘盈-在册（实盘）");
        map.put(33, "盘盈-不在册（实盘）");
        map.put(1, "正常（实盘）");
        return map;
    }

    private void processReportData(List<InventoryGroupReportDto> inventoryReportDtos, Map<String, String> nameMap,
                                   String normalName, Map<String, String> inventoryUserMap) {
        InventoryGroupReportDto total = new InventoryGroupReportDto();
        total.setAssetKey(1L);
        total.setName("合计");
        total.setYpNum(Convert.toStr(inventoryReportDtos.parallelStream().mapToInt(o -> Convert.toInt(o.getYpNum())).sum()));
        total.setPyZcNum(Convert.toStr(inventoryReportDtos.parallelStream().mapToInt(o -> Convert.toInt(o.getPyZcNum())).sum()));
        total.setPyBzcNum(Convert.toStr(inventoryReportDtos.parallelStream().mapToInt(o -> Convert.toInt(o.getPyBzcNum())).sum()));
        total.setPkNum(Convert.toStr(inventoryReportDtos.parallelStream().mapToInt(o -> Convert.toInt(o.getPkNum())).sum()));
        total.setTotalNum(Convert.toStr(inventoryReportDtos.parallelStream().mapToInt(o -> Convert.toInt(o.getTotalNum())).sum()));
        total.setRate(NumberUtil.formatPercent(1, 2));
        InventoryGroupReportDto rate = new InventoryGroupReportDto();
        rate.setAssetKey(2L);
        rate.setName("占比");
        rate.setYpNum(NumberUtil.formatPercent(divide(BigDecimal.valueOf(Convert.toInt(total.getYpNum())),
                BigDecimal.valueOf(Convert.toInt(total.getTotalNum()))).doubleValue(), 2));
        rate.setPyZcNum(NumberUtil.formatPercent(divide(BigDecimal.valueOf(Convert.toInt(total.getPyZcNum())),
                BigDecimal.valueOf(Convert.toInt(total.getTotalNum()))).doubleValue(), 2));
        rate.setPyBzcNum(NumberUtil.formatPercent(divide(BigDecimal.valueOf(Convert.toInt(total.getPyBzcNum())),
                BigDecimal.valueOf(Convert.toInt(total.getTotalNum()))).doubleValue(), 2));
        rate.setPkNum(NumberUtil.formatPercent(divide(BigDecimal.valueOf(Convert.toInt(total.getPkNum())),
                BigDecimal.valueOf(Convert.toInt(total.getTotalNum()))).doubleValue(), 2));

        for (InventoryGroupReportDto inventoryGroupReportDto : inventoryReportDtos) {
            String name = nameMap.getOrDefault(Convert.toStr(inventoryGroupReportDto.getAssetKey()), normalName);
            inventoryGroupReportDto.setName(name);
            BigDecimal r = divide(BigDecimal.valueOf(Convert.toInt(inventoryGroupReportDto.getTotalNum())),
                    BigDecimal.valueOf(Convert.toInt(total.getTotalNum())));
            inventoryGroupReportDto.setRate(NumberUtil.formatPercent(r.doubleValue(), 2));
            if (CollUtil.isNotEmpty(inventoryUserMap)) {
                String inventoryUserNames =
                        inventoryUserMap.getOrDefault(Convert.toStr(inventoryGroupReportDto.getAssetKey()), "");
                inventoryGroupReportDto.setInventoryUserNames(inventoryUserNames);
            }
        }

        inventoryReportDtos.add(total);
        inventoryReportDtos.add(rate);
    }

    /**
     * 根据id获取有权限的人员
     *
     * @param assetDtos
     * @param empIds
     * @return target 数据
     */
    /*@Override
    public List<Long> getUserByAssetIds(List<AssetDto> assetDtos, List<Long> empIds) {
        Set<Long> assetCategorySet = new HashSet<>();
        Set<Long> storageAreaSet = new HashSet<>();
        Set<Long> orgOwnerSet = new HashSet<>();
        Set<Long> useOrgSet = new HashSet<>();
        Set<Long> usePersonSet = new HashSet<>();
        Set<Long> managerOwnerSet = new HashSet<>();
        for (AssetDto asAsset : assetDtos) {
            JSONObject assetData = asAsset.getAssetData();
            if (assetData.containsKey("assetCategory")) {
                assetCategorySet.add(assetData.getLong("assetCategory"));
            }
            if (assetData.containsKey("storageArea")) {
                storageAreaSet.add(assetData.getLong("storageArea"));
            }
            if (assetData.containsKey("orgOwner")) {
                orgOwnerSet.add(assetData.getLong("orgOwner"));
            }
            if (assetData.containsKey("useOrg")) {
                useOrgSet.add(assetData.getLong("useOrg"));
            }
            if (assetData.containsKey("usePerson")) {
                usePersonSet.add(assetData.getLong("usePerson"));
            }
            if (assetData.containsKey("managerOwner")) {
                managerOwnerSet.add(assetData.getLong("managerOwner"));
            }
        }

        Set<Long> authEmpIds = new ConcurrentHashSet<>();
        // 加入超管
        authEmpIds.add(cusEmployeeService.getAdministrator().getId());
        // 循环用户, 权限
        empIds.parallelStream().forEach(empId -> {
            // 员工权限
            UserDataPermsDto dataPermsDto =
                    dataPermissionCacheService.getUserDataPermsCache(LoginUserThreadLocal.getCompanyId(), empId);
            String dataKey = AssetConstant.DATA_PERMISSION_ASSET + ":";
            // 员工资产权限
            List<String> dataPerms = dataPermsDto.getPermsMap().keySet()
                    .stream()
                    .filter(e -> e.startsWith(dataKey))
                    .collect(toList());

            boolean hasAuth = false;
            // 员工资产权限都满足才有权限
            int authNums = 0;
            for (String perm : dataPerms) {
                String[] keys = perm.split(":");
                List<Long> permData = dataPermsDto.getAuthorityData(perm);
                // 管理部门或使用部门
                if (AssetConstant.AUTHORITY_MANAGER_OR_USE_DEPT.equals(keys[1])) {
                    if ((CollUtil.isNotEmpty(orgOwnerSet) && permData.containsAll(orgOwnerSet))
                            || (CollUtil.isNotEmpty(useOrgSet) && permData.containsAll(useOrgSet))) {
                        authNums++;
                    }
                }
                // 管理部门
                else if (AssetConstant.AUTHORITY_MANAGER_DEPT.equals(keys[1])) {
                    if (CollUtil.isNotEmpty(orgOwnerSet) && permData.containsAll(orgOwnerSet)) {
                        authNums++;
                    }
                }
                // 使用部门
                else if (AssetConstant.AUTHORITY_USE_DEPT.equals(keys[1])) {
                    if (CollUtil.isNotEmpty(useOrgSet) && permData.containsAll(useOrgSet)) {
                        authNums++;
                    }
                }
                // 存放区域
                else if (AssetConstant.AUTHORITY_AREAS.equals(keys[1])) {
                    if (CollUtil.isNotEmpty(storageAreaSet) && permData.containsAll(storageAreaSet)) {
                        authNums++;
                    }
                }
                // 资产分类
                else if (AssetConstant.AUTHORITY_CATE.equals(keys[1])) {
                    if (CollUtil.isNotEmpty(assetCategorySet) && permData.containsAll(assetCategorySet)) {
                        authNums++;
                    }
                }
                // 仅可查看自己的资产数据
                else if (AssetConstant.AUTHORITY_ONLY_ONESELF.equals(keys[1])) {
                    if (permData.contains(Convert.toLong(AssetConstant.AUTHORITY_TYPE_ONLY_ONESELF))) {
                        if ((usePersonSet.size() == 1 && usePersonSet.contains(empId)) || (managerOwnerSet.size() == 1 && managerOwnerSet.contains(empId))) {
                            authNums = dataPerms.size();
                        }
                        break;
                    } else {
                        authNums++;
                    }
                }
            }
            // 具备所有权限项
            if (authNums == dataPerms.size()) {
                hasAuth = true;
            } else {
                hasAuth = (usePersonSet.size() == 1 && usePersonSet.contains(empId)) || (managerOwnerSet.size() == 1 && managerOwnerSet.contains(empId));
                // 是否是使用者或管理者
                // for (AssetDto dto : assetDtos) {
                //     if (empId.equals(dto.getAssetData().getLong("usePerson")) || empId.equals(dto.getAssetData().getLong(
                //             "managerOwner"))) {
                //         hasAuth = true;
                //     } else {
                //         hasAuth = false;
                //         break;
                //     }
                // }
            }

            if (hasAuth) {
                authEmpIds.add(empId);
            }
        });
        return new ArrayList<>(authEmpIds);
    }*/

    /*@Override
    public List<Long> getDefaultUserByAssetIds(int dispatchMode, List<AssetDto> assetDtos, List<Long> empIds) {
        Set<Long> assetCategorySet = new HashSet<>();
        Set<Long> storageAreaSet = new HashSet<>();
        Set<Long> orgOwnerSet = new HashSet<>();
        Set<Long> useOrgSet = new HashSet<>();
        Set<Long> usePersonSet = new HashSet<>();
        Set<Long> managerOwnerSet = new HashSet<>();
        for (AssetDto asAsset : assetDtos) {
            JSONObject assetData = asAsset.getAssetData();
            if (assetData.containsKey("assetCategory")) {
                assetCategorySet.add(assetData.getLong("assetCategory"));
            }
            if (assetData.containsKey("storageArea")) {
                storageAreaSet.add(assetData.getLong("storageArea"));
            }
            if (assetData.containsKey("orgOwner")) {
                orgOwnerSet.add(assetData.getLong("orgOwner"));
            }
            if (assetData.containsKey("useOrg")) {
                useOrgSet.add(assetData.getLong("useOrg"));
            }
            if (assetData.containsKey("usePerson")) {
                usePersonSet.add(assetData.getLong("usePerson"));
            }
            if (assetData.containsKey("managerOwner")) {
                managerOwnerSet.add(assetData.getLong("managerOwner"));
            }
        }

        Set<Long> authEmpIds = new ConcurrentHashSet<>();
        // 循环用户, 权限
        Long companyId = LoginUserThreadLocal.getCompanyId();
        empIds.parallelStream().forEach(empId -> {
            // 员工权限
            UserDataPermsDto dataPermsDto =
                    dataPermissionCacheService.getUserDataPermsCache(companyId, empId);
            String dataKey = AssetConstant.DATA_PERMISSION_ASSET + ":";
            // 员工资产权限
            List<String> dataPerms = dataPermsDto.getPermsMap().keySet()
                    .stream()
                    .filter(e -> e.startsWith(dataKey))
                    .collect(toList());
            // 是否仅可查看自己的资产数据
            List<Long> onlyOneself = dataPermsDto.getAuthorityData(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_ONLY_ONESELF);
            if (CollUtil.isNotEmpty(onlyOneself) && onlyOneself.contains(Convert.toLong(AssetConstant.AUTHORITY_TYPE_ONLY_ONESELF))) {
                if ((CollUtil.isNotEmpty(usePersonSet) && usePersonSet.size() == 1 && usePersonSet.contains(empId)) || (CollUtil.isNotEmpty(managerOwnerSet) && managerOwnerSet.size() == 1 && managerOwnerSet.contains(empId))) {
                    authEmpIds.add(empId);
                }
                return;
            }

            for (String perm : dataPerms) {
                String[] keys = perm.split(":");
                List<Long> permData = dataPermsDto.getAuthorityData(perm);
                // 管理部门或使用部门
                if (InventoryConstant.DISPATCH_ORG_OWNER == dispatchMode || InventoryConstant.DISPATCH_USE_ORG == dispatchMode) {
                    if (AssetConstant.AUTHORITY_MANAGER_OR_USE_DEPT.equals(keys[1])) {
                        if ((CollUtil.isNotEmpty(orgOwnerSet) && permData.containsAll(orgOwnerSet))
                                || (CollUtil.isNotEmpty(useOrgSet) && permData.containsAll(useOrgSet))) {
                            authEmpIds.add(empId);
                        }
                    }
                    // 管理部门
                    else if (AssetConstant.AUTHORITY_MANAGER_DEPT.equals(keys[1])) {
                        if (CollUtil.isNotEmpty(orgOwnerSet) && permData.containsAll(orgOwnerSet)) {
                            authEmpIds.add(empId);
                        }
                    }
                    // 使用部门
                    else if (AssetConstant.AUTHORITY_USE_DEPT.equals(keys[1])) {
                        if (CollUtil.isNotEmpty(useOrgSet) && permData.containsAll(useOrgSet)) {
                            authEmpIds.add(empId);
                        }
                    }
                } else if (InventoryConstant.DISPATCH_STORAGE_AREA == dispatchMode) {
                    if (CollUtil.isNotEmpty(storageAreaSet) && permData.containsAll(storageAreaSet)) {
                        authEmpIds.add(empId);
                    }
                } else if (InventoryConstant.DISPATCH_ASSET_CATEGORY == dispatchMode) {
                    if (CollUtil.isNotEmpty(assetCategorySet) && permData.containsAll(assetCategorySet)) {
                        authEmpIds.add(empId);
                    }
                }
            }
        });
        return new ArrayList<>(authEmpIds);
    }*/

    /**
     * 获取用户名称数组
     *
     * @param userIdList
     * @return target 数据
     */
    /*protected List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> getUserDto(List<Long> userIdList,
                                                                                    Map<Long, String> cusEmpMap,
                                                                                    Integer assetNum) {
        List<InventoryDispatchDetailDto.InventoryDispatchGroupDto> userList = Lists.newArrayList();
        for (Long userId : userIdList) {
            InventoryDispatchDetailDto.InventoryDispatchGroupDto userDto =
                    new InventoryDispatchDetailDto.InventoryDispatchGroupDto();
            String name = cusEmpMap.getOrDefault(userId, "");
            userDto.setValue(userId).setLabel(name).setAssetNum(assetNum);
            userList.add(userDto);
        }
        return userList;
    }*/

    /**
     * 确定盘点资产的判断人和任务id
     */
    protected void ascertainInventoryUserAndTaskId(AsInventoryAsset asInventoryAsset,
                                                   AssetDetailDto assetDetailDto, Integer dispatchMode,
                                                   List<InventoryDispatchRecordDto> dispatchDetail) {
        // 根据id获取对应的盘点人
        long key;
        switch (dispatchMode) {
            case InventoryConstant.DISPATCH_ORG_OWNER:
                key = ObjectUtil.isNotEmpty(assetDetailDto.getOrgOwner()) ?
                        Long.parseLong(assetDetailDto.getOrgOwner()) : 0L;
                break;
            case InventoryConstant.DISPATCH_MANAGER_OWNER:
                key = ObjectUtil.isNotEmpty(assetDetailDto.getManagerOwner()) ?
                        Long.parseLong(assetDetailDto.getManagerOwner()) : 0L;
                break;
            case InventoryConstant.DISPATCH_STORAGE_AREA:
                key = ObjectUtil.isNotEmpty(assetDetailDto.getStorageArea()) ?
                        Long.parseLong(assetDetailDto.getStorageArea()) : 0L;
                break;
            case InventoryConstant.DISPATCH_ASSET_CATEGORY:
                key = ObjectUtil.isNotEmpty(assetDetailDto.getAssetCategory()) ?
                        Long.parseLong(assetDetailDto.getAssetCategory()) : 0L;
                break;
            case InventoryConstant.DISPATCH_USE_ORG:
                key = ObjectUtil.isNotEmpty(assetDetailDto.getUseOrg()) ? Long.parseLong(assetDetailDto.getUseOrg())
                        : 0L;
                break;
            case InventoryConstant.DISPATCH_USE_PERSON:
                key = ObjectUtil.isNotEmpty(assetDetailDto.getUsePerson()) ?
                        Long.parseLong(assetDetailDto.getUsePerson()) : 0L;
                break;
            default:
                throw new BusinessException(InventoryResultCode.INVENTORY_DISPATCH_NOT_EXIST);
        }

        for (InventoryDispatchRecordDto recordDto : dispatchDetail) {
            if (recordDto.getIds().contains(key)) {
                asInventoryAsset.setInventoryTaskId(recordDto.getTaskId());
                asInventoryAsset.setInventoryUser(
                        recordDto.getInventoryUser().stream()
                                .map(InventoryDispatchDetailDto.InventoryDispatchGroupDto::getValue)
                                .distinct()
                                .collect(Collectors.toList()));
                break;
            }
        }
    }

    /*private String transIdToName(String field, String value, Map<String, String> transMap) {
        if (StrUtil.isBlank(value)) {
            return "";
        }
        // 字典项
        if (field.startsWith("dict/")) {
            field = field.replace("dict/", "");
            List<DictDataDto> dictDataDtos = Convert.toList(DictDataDto.class,
                    redisService.get(BaseConstant.SYS_DICT_KEY + field));
            Map<String, String> dict = dictDataDtos.stream()
                    .collect(Collectors.toMap(DictDataDto::getValue, DictDataDto::getLabel, (k1, k2) -> k1));
            return dict.getOrDefault(value, "");
        } else {
            return Convert.toStr(transMap.get(value));
        }
    }*/

    /**
     * @param dividend 被除数
     * @param divisor  除数
     * @param digit    保留位数
     * @return 结果
     */
    private BigDecimal divide(BigDecimal dividend, BigDecimal divisor, Integer digit) {
        BigDecimal calc = new BigDecimal("0");
        try {
            if (0 == dividend.intValue()) {
                return calc;
            } else {
                return dividend
                        .divide(divisor, digit, RoundingMode.HALF_UP);
            }
        } catch (Exception e) {
            return calc;
        }
    }

    /**
     * @param dividend 被除数
     * @param divisor  除数
     * @return 结果
     */
    private BigDecimal divide(BigDecimal dividend, BigDecimal divisor) {
        return divide(dividend, divisor, 4);
    }

    @Override
    public List<AsInventory> getInventoryForSendNotice(LocalDateTime timeout) {
        return this.getBaseMapper().selectAllForSendNotice(timeout);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleInventoryCreateAsset(InventoryCreateAssetQueryDto queryDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 清除旧数据
        inventoryQueryAssetService.delete(queryDto.getQueryId());
        // 预处理，需要缩减范围
        if (CollUtil.isNotEmpty(queryDto.getAssetQueryConditions())) {
            queryDto.getAssetQueryConditions().forEach(q -> q.setPageSize(99L));
        }
        List<AssetDto> assetDtos = findInventoryAssets(
                queryDto.getRangeType(),
                queryDto.getEnableExcludeRepair(),
                queryDto.getEnableExcludeDispose(),
                queryDto.getAssetQueryConditions(),
                queryDto.getInventoryHistoryQueries());
        if (CollUtil.isEmpty(assetDtos)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }
        List<AsInventoryQueryAsset> queryAssets = assetDtos.parallelStream()
                .map(o -> BeanUtil.copyProperties(o, AsInventoryQueryAsset.class, "id", "createTime")
                        .setQueryId(queryDto.getQueryId()))
                .collect(toList());
        List<AsInventoryQueryAsset> saveData = ListUtil.sub(queryAssets, 0, 99);
        inventoryQueryAssetService.saveBatch(saveData);

        int size;
        // 从资产库选择
        if (queryDto.getRangeType() == 1) {
            Set<Long> assetTotal = new HashSet<>();
            // 查询总数量
            for (AssetQueryConditionDto conditionDto : queryDto.getAssetQueryConditions()) {
                conditionDto.setPageSize(1000000L);
                IPage<Long> printAssetIds = assetService.getPrintAssetIds(conditionDto, companyId);
                assetTotal.addAll(printAssetIds.getRecords());
            }
            size = assetTotal.size();
        } else {
            size = queryAssets.size();
        }
        redisService.set(RedisConstant.inventoryAssetTotalNumKey(queryDto.getQueryId()), size, 1, TimeUnit.DAYS);
    }

    @Override
    public IPage<AssetDto> createAssetPage(InventoryCreateAssetQueryPageDto queryDto) {
        return inventoryQueryAssetService.page(queryDto.buildIPage(),
                        Wrappers.<AsInventoryQueryAsset>lambdaQuery()
                                .eq(AsInventoryQueryAsset::getQueryId, queryDto.getQueryId()))
                .convert(o -> BeanUtil.copyProperties(o, AssetDto.class));
    }

    @Override
    public Boolean handledInventory(Long id) {
        int unhandleNum = inventoryAssetService.getUnhandleNum(id);
        int unhandleNum2 = inventoryAssetService.getUnhandleNum2(id);
        int plUnhandleNum = inventorySurplusService.getUnhandleNum(id);
        int unhandlePhotoNum = inventoryAssetService.getUnhandlePhotoNum(id);
        if ((unhandleNum + unhandleNum2 + plUnhandleNum + unhandlePhotoNum) == 0) {
            updateById(new AsInventory().setId(id).setStatus(InventoryConstant.HANDLED));
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<InventoryRangeGroup> createRangeGroup(InventoryRangeGroupCreateDto dto) {
        List<InventoryRangeGroup> rangeGroups = new ArrayList<>();
        List<AssetQueryConditionDto> assetQueryConditions = dto.getAssetQueryConditions();
        List<InventoryHistoryQueryDto> inventoryHistoryQueries = dto.getInventoryHistoryQueries();
        if (dto.getRangeType() == 1) {
            if (CollUtil.isEmpty(assetQueryConditions)) {
                BusinessExceptionUtil.throwException("资产筛选条件不能为空");
            }
            if (assetQueryConditions.size() > 10) {
                BusinessExceptionUtil.throwException("资产筛选条件不能超过10个");
            }

            for (int i = 1; i <= assetQueryConditions.size(); i++) {
                AssetQueryConditionDto queryConditionDto = assetQueryConditions.get(i - 1);
                handleStatus(dto.getEnableExcludeRepair(), dto.getEnableExcludeDispose(), queryConditionDto);
                List<QueryConditionDto> conditions = queryConditionDto.getConditions();

                InventoryRangeGroup rangeGroup = new InventoryRangeGroup().setName("条件分组" + i);
                List<InventoryRangeRecord> rangeRecords = new ArrayList<>();
                if (StrUtil.isNotBlank(queryConditionDto.getKw())) {
                    InventoryRangeRecord kw = new InventoryRangeRecord()
                            .setName("关键字包含")
                            .setKey("kw")
                            .setValue(ListUtil.of(queryConditionDto.getKw()))
                            .setValueText(ListUtil.of(queryConditionDto.getKw()));
                    rangeRecords.add(kw);
                }
                if (CollUtil.isNotEmpty(queryConditionDto.getAssetIds())) {
                    InventoryRangeRecord kw = new InventoryRangeRecord()
                            .setName("勾选资产")
                            .setKey("assetIds")
                            .setValue(ListUtil.of(queryConditionDto.getAssetIds().size() + "条"))
                            .setValueText(ListUtil.of(queryConditionDto.getAssetIds().size() + "条"));
                    rangeRecords.add(kw);
                }
                if (CollUtil.isNotEmpty(conditions)) {
                    for (QueryConditionDto condition : conditions) {
                        if ("status".equals(condition.getCode())) {
                            List<String> value = Convert.toList(String.class, condition.getQueryData());
                            List<String> valueText = value.stream()
                                    .map(id -> transIdToName("status", id))
                                    .collect(toList());
                            String queryName = QueryConditionType.getEnum(condition.getQuery()).getDesc();
                            InventoryRangeRecord status = new InventoryRangeRecord()
                                    .setName("资产状态" + queryName)
                                    .setKey("status")
                                    .setValue(value)
                                    .setValueText(valueText);
                            rangeRecords.add(status);
                        } else {
                            List<String> value = Convert.toList(String.class, condition.getQueryData());
                            List<String> valueText = new ArrayList<>(value);
                            JSONObject fieldProps = condition.getFieldProps();
                            if (fieldProps != null) {
                                JSONObject extProps = fieldProps.getJSONObject("extProps");
                                if (extProps != null && StrUtil.isNotBlank(extProps.getString("translation"))) {
                                    valueText = value.stream()
                                            .map(id -> transIdToName(condition.getType(), id))
                                            .collect(toList());
                                }
                            }

                            String queryName = QueryConditionType.getEnum(condition.getQuery()).getDesc();
                            String name = queryConditionDto.getIsAutoMatch() ? "自动匹配" :
                                    (condition.getName() + queryName);
                            InventoryRangeRecord cdi = new InventoryRangeRecord()
                                    .setName(name)
                                    .setKey(condition.getCode())
                                    .setValue(value)
                                    .setValueText(valueText);
                            rangeRecords.add(cdi);
                        }
                    }
                }
                rangeGroup.setInventoryRange(rangeRecords);
                rangeGroups.add(rangeGroup);
            }
        }
        // 从历史盘点单选择
        else {
            if (CollUtil.isEmpty(inventoryHistoryQueries)) {
                BusinessExceptionUtil.throwException("历史盘点单不能为空");
            }
            if (inventoryHistoryQueries.size() > 10) {
                BusinessExceptionUtil.throwException("最多选择10个历史盘点单");
            }
            for (int i = 1; i <= inventoryHistoryQueries.size(); i++) {
                InventoryHistoryQueryDto historyQueryDto = inventoryHistoryQueries.get(i - 1);
                List<AsInventory> inventories = listByIds(historyQueryDto.getInventoryIds());
                List<String> inventoryIds = inventories.parallelStream()
                        .map(inventory -> Convert.toStr(inventory.getId())).collect(toList());
                List<String> valueText = inventories.parallelStream()
                        .map(inventory -> inventory.getInventoryNo() + "（" + inventory.getName() + "）")
                        .collect(toList());
                InventoryRangeGroup rangeGroup = new InventoryRangeGroup().setName("条件分组" + i);
                List<InventoryRangeRecord> rangeRecords = ListUtil.of(
                        new InventoryRangeRecord()
                                .setName("历史盘点单")
                                .setValue(inventoryIds)
                                .setValueText(valueText));
                rangeGroup.setInventoryRange(rangeRecords);
                rangeGroups.add(rangeGroup);
            }
        }
        return rangeGroups;
    }

    private String transIdToName(String fieldType, String value) {
        if (value == null) {
            return "";
        }
        switch (fieldType) {
            case AssetConstant.ED_YZC_ORG:
                return cacheResourceUtil.getOrgName(Convert.toLong(value));
            case AssetConstant.ED_YZC_EMP:
                return cacheResourceUtil.getUserNameAndCode(Convert.toLong(value));
            case AssetConstant.ED_YZC_AREA:
                return cacheResourceUtil.getAreaName(Convert.toLong(value));
            case AssetConstant.ED_YZC_ASSET_CATE:
                return cacheResourceUtil.getCategoryName(Convert.toLong(value));
            case AssetConstant.ED_YZC_MATERIAL_CATE:
                return cacheResourceUtil.getMaterialCategoryName(Convert.toLong(value));
            case AssetConstant.ED_YZC_REPOSITORY:
                return cacheResourceUtil.getRepositoryName(Convert.toLong(value));
            case "status":
                return Convert.toStr(redisService.hGet(RedisConstant.assetStatusDictKey(), value), StrUtil.EMPTY);
            default:
                return value;
        }
    }

    @Override
    public Integer getProcessInventory(Long assetId){
        return this.baseMapper.getProcessInventory(assetId);
    }
}
