package com.niimbot.asset.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.inventory.mapper.AsInventoryAssetLogMapper;
import com.niimbot.asset.inventory.mapper.AsInventorySurplusMapper;
import com.niimbot.asset.inventory.model.*;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventorySurplusService;
import com.niimbot.inventory.InventoryAssetCountQueryDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventorySurplusSimpleQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 盘点-资产盘盈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Service
public class AsInventorySurplusServiceImpl extends ServiceImpl<AsInventorySurplusMapper, AsInventorySurplus> implements AsInventorySurplusService {
    @Resource
    private AsInventoryService inventoryService;

    @Resource
    private AsInventoryAssetLogMapper inventoryAssetLogMapper;

    @Resource
    private MySqlInventoryAssetQueryConditionResolver conditionResolver;

    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @Override
    public IPage<InventorySurplusDto> getSurplusById(InventorySurplusQueryDto dto) {
        String tableAlias = "r";
        conditionResolver.setAssetData("asset_data");
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        IPage<InventorySurplusDto> tmpReturnList = this.baseMapper.getPlSurplus(dto.buildIPage(), dto, conditions);
        List<InventorySurplusDto> tmpList = tmpReturnList.getRecords();
        List<InventorySurplusDto> tmpListHasLog = this.fixListChangeLog(tmpList);
        tmpReturnList.setRecords(tmpListHasLog);

        return tmpReturnList;
    }

    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @Override
    public List<InventorySurplusDto> getSurplusList(InventorySurplusQueryDto dto) {
        String tableAlias = "r";
        conditionResolver.setAssetData("asset_data");
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        List<InventorySurplusDto> tmpList = this.baseMapper.selectSurplusList(dto, conditions);
        List<InventorySurplusDto> tmpListHasLog = this.fixListChangeLog(tmpList);
        return tmpListHasLog;
    }

    /**
     * 删除当前用户的上报盘盈数据
     *
     * @param userId 当前用户id
     * @return 结果
     */
    @Override
    public boolean removeWithReport(Long userId, Long inventoryId, Integer taskType) {
        return this.baseMapper.removeWithReport(userId, inventoryId, taskType);
    }

    @Override
    public boolean removeAssetByTaskId(Long taskId) {
        return this.baseMapper.removeAssetByTaskId(taskId);
    }

    @Override
    public List<AsInventoryAssetReport> listAssetByTaskId(Long taskId) {
        return this.baseMapper.listAssetByTaskId(taskId);
    }

    /**
     * 盘点损益处理盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @Override
    public IPage<InventorySurplusDto> getPlSurplus(InventorySurplusSimpleQueryDto dto) {
        // 获取盘点单详情
        AsInventory byId = inventoryService.getById(dto.getInventoryId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!ListUtil.of(InventoryConstant.COMPLETED, InventoryConstant.HANDLED).contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_COMPLETED_NO_PL);
        }

        InventorySurplusQueryDto inventorySurplusQueryDto = new InventorySurplusQueryDto();
        BeanUtil.copyProperties(dto, inventorySurplusQueryDto);
//        String tableAlias = "r";
//        conditionResolver.setAssetData("asset_data");
//        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        IPage<InventorySurplusDto> tmpReturnList = this.baseMapper.getPlSurplus(dto.buildIPage(), inventorySurplusQueryDto, null);
        List<InventorySurplusDto> tmpList = tmpReturnList.getRecords();
        List<InventorySurplusDto> tmpListHasLog = this.fixListChangeLog(tmpList);
        tmpReturnList.setRecords(tmpListHasLog);
        return tmpReturnList;
    }

    /**
     * 获取盘点单下所有的在册盘盈资产id
     *
     * @param inventoryId
     * @return 结果
     */
    @Override
    public List<Long> getMarkSurplusAsset(Long inventoryId) {
        return this.baseMapper.getMarkSurplusAsset(inventoryId);
    }

    /**
     * 删除已经存在的盘盈资产
     *
     * @param deleteAssetIds
     * @return 结果
     */
    @Override
    public boolean removeExistAsset(List<Long> deleteAssetIds, Long inventoryId) {
        return this.baseMapper.removeExistAsset(deleteAssetIds, inventoryId);
    }

    /**
     * 获取盘盈数量
     *
     * @param dto
     * @return 结果
     */
    @Override
    public int countAddAsset(InventoryAssetCountQueryDto dto) {
        return this.baseMapper.countAddAsset(dto);
    }

    @Override
    public int getUnhandleNum(Long inventoryId) {
        return this.baseMapper.getUnhandleNum(inventoryId);
    }

    protected List<InventorySurplusDto> fixListChangeLog(List<InventorySurplusDto> pDto){
        if (CollUtil.isEmpty(pDto)) {
            return ListUtil.empty();
        }
        boolean tmpBoolean;
        List<InventorySurplusDto> tmpListReturn = new ArrayList<>();
        List<AsInventoryAssetLog> tmpListLog;
        List<Long> tmpIds = new ArrayList<>();
        List<InventoryAssetLogData> tmpDtoLogDataList;
        JSONObject tmpJsonOrigin;
        JSONObject tmpJsonChanged;

        //查询IDS
        for (InventorySurplusDto rowDto : pDto) {
            if(InventoryConstant.ASSET_MARK_ON.equals(rowDto.getAssetMark())){
                JSONObject json = Optional.of(rowDto.getAssetData()).orElse(new JSONObject());
                tmpIds.add(json.getLong("assetId"));
            }
        }
        LambdaQueryWrapper<AsInventoryAssetLog> tmpWrapper = new QueryWrapper<AsInventoryAssetLog>().lambda()
                .eq(AsInventoryAssetLog::getInventoryId, pDto.get(0).getInventoryId());
        if (CollUtil.isNotEmpty(tmpIds)) {
            tmpWrapper.in(AsInventoryAssetLog::getAssetId, tmpIds);
        }

        tmpListLog = inventoryAssetLogMapper.selectList(tmpWrapper);

        //匹配写入
        for (InventorySurplusDto rowDto : pDto){
            tmpBoolean=false;
            if(CollUtil.isNotEmpty(tmpListLog)){
                for (AsInventoryAssetLog rowDtoLog : tmpListLog){
                    if(ObjectUtil.isNull(rowDto.getAssetId())){
                        tmpBoolean=false;
                    }else{
                        if(rowDto.getInventoryId().equals(rowDtoLog.getInventoryId()) &&
                                rowDto.getAssetId().equals(rowDtoLog.getAssetId())){
                            tmpJsonOrigin=new JSONObject();
                            tmpJsonChanged=new JSONObject();
                            tmpDtoLogDataList=rowDtoLog.getLogData();
                            for (InventoryAssetLogData rowDtoLogData : tmpDtoLogDataList){
                                tmpJsonOrigin.put(rowDtoLogData.getCode(),rowDtoLogData.getOriginalValue());
                                tmpJsonChanged.put(rowDtoLogData.getCode(),rowDtoLogData.getFinalValue());
                            }

                            rowDto.setAssetChangRecordOrigen(tmpJsonOrigin.toJSONString());
                            rowDto.setAssetChangRecord(tmpJsonChanged.toJSONString());
                            tmpListReturn.add(rowDto);
                            tmpBoolean=true;
                        }
                    }

                }
            }

            if(!tmpBoolean){
                rowDto.setAssetChangRecordOrigen("{}");
                rowDto.setAssetChangRecord("{}");
                tmpListReturn.add(rowDto);
            }
        }

        return tmpListReturn;
    }

}
