package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点任务审批记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryApprove对象", description="盘点任务审批记录")
@TableName(value = "as_inventory_approve", autoResultMap = true)
public class AsInventoryApprove implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final int ACTION_SUBMIT = 1;
    public static final int ACTION_REJECTED = 2;
    public static final int ACTION_APPROVED = 3;

    @ApiModelProperty(value = "审批记录主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "审批意见")
    private String opinion;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "操作（1：提交，2：驳回，3：通过）")
    private Integer action;

    @ApiModelProperty(value = "创建者")
    private Long createBy;


}
