package com.niimbot.asset.inventory.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryApprove;
import com.niimbot.inventory.InventoryApproveDto;

import java.util.List;

/**
 * <p>
 * 盘点任务审批记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryApproveService extends IService<AsInventoryApprove> {
    /**
     * 添加提交记录
     *
     * @param inventoryId 盘点单id
     * @param taskId      任务id
     * @param opinion     审批意见
     * @return 是否成功
     */
    boolean addSubmitRecord(Long inventoryId, Long taskId, String opinion);

    /**
     * 添加通过记录
     *
     * @param inventoryId 盘点单id
     * @param taskId      任务id
     * @param opinion     审批意见
     * @return 是否成功
     */
    boolean addApprovedRecord(Long inventoryId, Long taskId, String opinion);

    /**
     * 添加整单通过记录
     *
     * @param inventoryId 盘点单id
     * @param taskIds     任务id
     * @param opinion     审批意见
     * @return 是否成功
     */
    boolean addApprovedRecord(Long inventoryId, List<Long> taskIds, String opinion);

    /**
     * 添加驳回记录
     *
     * @param inventoryId 盘点单id
     * @param taskId      任务id
     * @param opinion     审批意见
     * @return 是否成功
     */
    boolean addRejectedRecord(Long inventoryId, Long taskId, String opinion);

    /**
     * 添加整单驳回记录
     *
     * @param inventoryId 盘点单id
     * @param taskIds     任务id
     * @param opinion     审批意见
     * @return 是否成功
     */
    boolean addRejectedRecord(Long inventoryId, List<Long> taskIds, String opinion);

    /**
     * 添加记录
     *
     * @param inventoryId 盘点单id
     * @param taskId      任务id
     * @param opinion     审批意见
     * @param action      操作
     * @return 是否成功
     */
    boolean addRecord(Long inventoryId, Long taskId, String opinion, Integer action);

    /**
     * 添加整单记录
     *
     * @param inventoryId 盘点单id
     * @param taskIds     任务id
     * @param opinion     审批意见
     * @param action      操作
     * @return 是否成功
     */
    boolean addRecord(Long inventoryId, List<Long> taskIds, String opinion, Integer action);

    /**
     * 审核记录
     *
     * @param inventoryId 盘点单id
     * @param taskId     任务id
     * @return 是否成功
     */
    List<InventoryApproveDto> getList(Long inventoryId, Long taskId);
}
