package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.niimbot.asset.inventory.handle.InventoryDispatchRecordGroupTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 分配方式
 *
 * <AUTHOR>
 * @date 2021/4/9 10:26
 */
@Data
@Accessors(chain = true)
public class InventoryDispatchRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "子任务id")
    private Long taskId;

    @ApiModelProperty(value = "子任务名称")
    @NotEmpty(message = "子任务名称不能为空")
    private String taskName;

    @ApiModelProperty(value = "维度id")
    @NotNull(message = "属性ID不能为空")
    private List<Long> ids;

    @ApiModelProperty(value = "维度名称")
    @NotEmpty(message = "属性名称不能为空")
    private List<String> names;

    @ApiModelProperty(value = "资产数量")
    private Integer assetNum;

    @ApiModelProperty(value = "盘点人")
    @TableField(typeHandler = InventoryDispatchRecordGroupTypeHandler.class)
    private List<InventoryDispatchGroupUser> inventoryUser;

}
