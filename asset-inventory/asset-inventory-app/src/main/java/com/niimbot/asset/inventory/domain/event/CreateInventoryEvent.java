package com.niimbot.asset.inventory.domain.event;

import com.niimbot.asset.framework.support.SystemEvent;

import lombok.Getter;
import lombok.Setter;

/**
 * 钉钉酷应用——盘点首次添加到群会话事件
 *
 * <AUTHOR>
 * @date 2023/4/12 17:13
 */
@Getter
public class CreateInventoryEvent extends SystemEvent {

    private Long companyId;
    private Long inventoryId;
    private String openConversationId;

    @Setter
    private Boolean firstLink = true;

    public CreateInventoryEvent(Long companyId,
                                Long inventoryId,
                                String openConversationId) {
        super(new Object());
        this.companyId = companyId;
        this.inventoryId = inventoryId;
        this.openConversationId = openConversationId;
    }

}
