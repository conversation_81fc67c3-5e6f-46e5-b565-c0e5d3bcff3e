package com.niimbot.asset.inventory.service.impl;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.system.QueryConditionDto;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.StringJoiner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Setter;

/**
 * MySQL资产查询条件处理器
 *
 * <AUTHOR>
 * @date 2021/12/7 15:42
 */
@Component
public class MySqlInventoryAssetQueryConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {
    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.%s ->> '$.%s'";

    private final String defaultAssetData = "asset_snapshot_data";

    @Setter
    private String assetData = defaultAssetData;

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        ImmutableMap.<String, QueryFieldConstant.Field>builder().build());
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        this.assetData = defaultAssetData;
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition) {
/*        // 额外字段
        if (!fieldCodeSet.contains(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(getJsonSqlField(tableAlias, condition.getCode()));
            }
        }*/
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return getJsonSqlSegmentTpl(tableAlias, code, this.assetData);
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }

    private String getJsonSqlSegmentTpl(String tableAlias, String code, String fieldName) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, fieldName, code);
    }
}
