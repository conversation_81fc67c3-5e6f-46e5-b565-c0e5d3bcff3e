package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventorySetting;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventorySettingAddListDto;
import com.niimbot.inventory.InventorySettingDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:22
 */
public interface AsInventorySettingService extends IService<AsInventorySetting> {

    List<InventorySettingDto> settingList(Integer type);

    Boolean save(InventorySettingAddListDto addListDto);

    FormVO inventoryAttrList(Integer type);
}
