package com.niimbot.asset.inventory.domain.abs.impl;

import com.niimbot.asset.inventory.mapstruct.InventoryMapStruct;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.service.AsInventoryAssetService;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.system.abs.InventoryAbs;
import com.niimbot.asset.system.dto.InventoryAssetCountGetQry;
import com.niimbot.asset.system.dto.InventoryGetQry;
import com.niimbot.asset.system.dto.clientobject.InventoryAssetCountCO;
import com.niimbot.asset.system.dto.clientobject.InventoryCO;
import com.niimbot.inventory.InventoryAssetCountDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/13 16:37
 */
@RestController
@RequestMapping("/client/abs/inventory/inventoryAbs/")
@RequiredArgsConstructor
public class InventoryAbsImpl implements InventoryAbs {
    private final InventoryMapStruct inventoryMapStruct;

    @Resource
    private AsInventoryAssetService inventoryAssetService;

    @Autowired
    private AsInventoryService inventoryService;

    @Override
    public InventoryCO getInventory(InventoryGetQry qry) {
        AsInventory inventory = inventoryService.getById(qry.getId());
        return inventoryMapStruct.convertInventoryModelToCo(inventory);
    }

    @Override
    public InventoryAssetCountCO getInventoryAssetCount(InventoryAssetCountGetQry qry) {
        InventoryAssetCountDto dto = inventoryAssetService.assetCount(inventoryMapStruct.convertInventoryAssetCountGetQryToDto(qry));
        return inventoryMapStruct.convertInventoryAssetCountDtoToCo(dto);
    }

    @Override
    public Integer getProcessInventory(Long assetId){
        return inventoryService.getProcessInventory(assetId);
    }
}
