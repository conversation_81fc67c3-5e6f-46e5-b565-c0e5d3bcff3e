package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 盘点报告
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryReport对象", description="盘点报告")
@TableName(value = "as_inventory_report", autoResultMap = true)
public class AsInventoryReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点单id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "资产总数")
    private Integer assetTotalCount;

    @ApiModelProperty(value = "资产总金额")
    private BigDecimal assetTotalMoney;

    @ApiModelProperty(value = "实盘数量")
    private Integer checkedCount;

    @ApiModelProperty(value = "未盘数量")
    private Integer uncheckedCount;

    @ApiModelProperty(value = "实盘金额")
    private BigDecimal checkedMoney;

    @ApiModelProperty(value = "盘亏数量")
    private Integer lossCount;

    @ApiModelProperty(value = "盘亏金额")
    private BigDecimal lossMoney;

    @ApiModelProperty(value = "盘盈数量")
    private Integer surplusCount;

    @ApiModelProperty(value = "盘盈金额")
    private BigDecimal surplusMoney;

    @ApiModelProperty(value = "盘盈数量[在册]")
    private Integer surplusMarkCount;

    @ApiModelProperty(value = "盘盈金额[在册]")
    private BigDecimal surplusMarkMoney;

    @ApiModelProperty(value = "盘盈数量[不在册]")
    private Integer surplusNotMarkCount;

    @ApiModelProperty(value = "盘盈金额[不在册]")
    private BigDecimal surplusNotMarkMoney;

    @ApiModelProperty(value = "已盘闲置数量")
    private Integer idleCount;

    @ApiModelProperty(value = "已盘在用数量")
    private Integer usedCount;

    @ApiModelProperty(value = "已盘借出数量")
    private Integer lendCount;

    @ApiModelProperty(value = "已盘处置数量")
    private Integer disposeCount;

    @ApiModelProperty(value = "已盘维修中数量")
    private Integer repairCount;

    @ApiModelProperty(value = "已盘调拨待审数量")
    private Integer waitAllocateCount;

    @ApiModelProperty(value = "已盘处置待审数量")
    private Integer waitDisposeCount;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
