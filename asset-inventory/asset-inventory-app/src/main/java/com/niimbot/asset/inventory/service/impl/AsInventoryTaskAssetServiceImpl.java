package com.niimbot.asset.inventory.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.inventory.mapper.AsInventoryTaskAssetMapper;
import com.niimbot.asset.inventory.model.AsInventoryTaskAsset;
import com.niimbot.asset.inventory.service.AsInventoryTaskAssetService;

import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 盘点任务-资产盘点状态 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
@Service
public class AsInventoryTaskAssetServiceImpl extends ServiceImpl<AsInventoryTaskAssetMapper, AsInventoryTaskAsset> implements AsInventoryTaskAssetService {

    @Override
    public List<AsInventoryTaskAsset> getInventoryTaskAssets(Long inventoryId, Long taskId) {
        return list(Wrappers.<AsInventoryTaskAsset>lambdaQuery()
                .eq(AsInventoryTaskAsset::getInventoryId, inventoryId)
                .eq(AsInventoryTaskAsset::getInventoryTaskId, taskId));
    }

    @Override
    public List<AsInventoryTaskAsset> getInventoryTaskAssets(Long inventoryId, Long taskId, Collection<Long> assetIds) {
        return list(Wrappers.<AsInventoryTaskAsset>lambdaQuery()
                .eq(AsInventoryTaskAsset::getInventoryId, inventoryId)
                .eq(AsInventoryTaskAsset::getInventoryTaskId, taskId)
                .in(AsInventoryTaskAsset::getAssetId, assetIds));
    }

    @Override
    public AsInventoryTaskAsset getInventoryTaskAsset(Long inventoryId, Long taskId, Long assetId) {
        return getOne(Wrappers.<AsInventoryTaskAsset>lambdaQuery()
                .eq(AsInventoryTaskAsset::getInventoryId, inventoryId)
                .eq(AsInventoryTaskAsset::getInventoryTaskId, taskId)
                .eq(AsInventoryTaskAsset::getAssetId, assetId));
    }

    @Override
    public void removeByInventoryId(Long inventoryId) {
        remove(Wrappers.<AsInventoryTaskAsset>lambdaQuery()
                .eq(AsInventoryTaskAsset::getInventoryId, inventoryId));
    }
}
