package com.niimbot.asset.inventory.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 分配方式
 *
 * <AUTHOR>
 * @date 2021/4/9 10:26
 */
@Data
@Accessors(chain = true)
public class InventoryDispatchGroupUser implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点人")
    private Long value;

    @ApiModelProperty(value = "盘点人-中文")
    private String label;

    @ApiModelProperty(value = "资产数量")
    private Integer assetNum;

}
