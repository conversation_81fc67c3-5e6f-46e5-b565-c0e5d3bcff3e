package com.niimbot.asset.inventory.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.inventory.mapper.AsInventoryHandleRecordMapper;
import com.niimbot.asset.inventory.model.AsInventoryHandleRecord;
import com.niimbot.asset.inventory.service.AsInventoryHandleRecordService;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/3 13:52
 */
@Service
public class AsInventoryHandleRecordServiceImpl extends ServiceImpl<AsInventoryHandleRecordMapper, AsInventoryHandleRecord> implements AsInventoryHandleRecordService {

    @Override
    public Set<Long> hasHandleReportRecord(List<Long> inventoryAssetReportId, Integer bizType) {
        List<AsInventoryHandleRecord> record = list(
                Wrappers.lambdaQuery(AsInventoryHandleRecord.class)
                        .select(AsInventoryHandleRecord::getInventoryAssetReportId)
                        .eq(AsInventoryHandleRecord::getBizType, bizType)
                        .in(AsInventoryHandleRecord::getInventoryAssetReportId, inventoryAssetReportId));
        return record.stream().map(AsInventoryHandleRecord::getInventoryAssetReportId).collect(Collectors.toSet());
    }

    @Override
    public Set<Long> hasHandleInventoryRecord(List<Long> inventoryAssetId, Integer bizType) {
        List<AsInventoryHandleRecord> record = list(
                Wrappers.lambdaQuery(AsInventoryHandleRecord.class)
                        .select(AsInventoryHandleRecord::getInventoryAssetId)
                        .eq(AsInventoryHandleRecord::getBizType, bizType)
                        .in(AsInventoryHandleRecord::getInventoryAssetId, inventoryAssetId));
        return record.stream().map(AsInventoryHandleRecord::getInventoryAssetId).collect(Collectors.toSet());
    }

}
