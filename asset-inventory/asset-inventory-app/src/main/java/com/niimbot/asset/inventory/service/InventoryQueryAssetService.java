package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryQueryAsset;
import com.niimbot.inventory.InventoryCreateAssetQueryPageDto;
import com.niimbot.means.AssetDto;

import java.util.List;

public interface InventoryQueryAssetService extends IService<AsInventoryQueryAsset> {

    /**
     * 盘点单创建资产分页
     *
     * @param queryDto
     * @return
     */
    IPage<AssetDto> assetPage(InventoryCreateAssetQueryPageDto queryDto);

    /**
     * 通过查询id查询创建盘点单资产
     *
     * @param queryId
     * @return
     */
    List<AssetDto> listAsset(Long queryId);

    /**
     * 通过查询id删除创建盘点单资产
     *
     * @param queryId
     */
    void delete(Long queryId);
}