package com.niimbot.asset.inventory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.mapper.OnlineInventoryTaskMapper;
import com.niimbot.asset.inventory.service.OnlineInventoryTaskService;
import com.niimbot.ding.DingInventoryQueryDto;
import com.niimbot.ding.DingInventoryTaskListDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnlineInventoryTaskServiceImpl implements OnlineInventoryTaskService {

    private final OnlineInventoryTaskMapper onlineInventoryTaskMapper;

    @Override
    public IPage<DingInventoryTaskListDto> onlineTaskPage(DingInventoryQueryDto dto) {
        // 查询基础数据
        IPage<DingInventoryTaskListDto> inventoryTaskListDtoIPage = onlineInventoryTaskMapper.onlineTaskPage(dto.buildIPage(),
                LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId(), dto);
        List<DingInventoryTaskListDto> records = inventoryTaskListDtoIPage.getRecords();
//        if (dto.getStatus() == 1 || dto.getStatus() == 2) {
            Map<Long, List<DingInventoryTaskListDto>> map = records.stream().collect(Collectors.groupingBy(DingInventoryTaskListDto::getInventoryId));
            map.forEach(this::fillPdData);
//        }
        return inventoryTaskListDtoIPage;
    }

    private void fillPdData(Long inventoryId, List<DingInventoryTaskListDto> taskList) {
        List<Long> taskIds = taskList.stream().map(DingInventoryTaskListDto::getId).collect(Collectors.toList());
        // 补齐未盘，已盘，盘盈在册，盘盈不在册
        if (CollUtil.isNotEmpty(taskIds)) {

            // 查询未盘已盘
            List<DingInventoryTaskListDto> assetCount = onlineInventoryTaskMapper.assetCount(inventoryId, taskIds);
            Map<Long, DingInventoryTaskListDto> assetCountMap = assetCount.stream()
                    .collect(Collectors.toMap(DingInventoryTaskListDto::getId, k -> k));

            // 查询盘盈在册，盘盈不在册
            List<DingInventoryTaskListDto> countAddAsset = onlineInventoryTaskMapper.countAddAsset(inventoryId, taskIds);
            Map<Long, DingInventoryTaskListDto> countAddAssetMap = countAddAsset.stream()
                    .collect(Collectors.toMap(DingInventoryTaskListDto::getId, k -> k));

            taskList.forEach(task -> {
                if (assetCountMap.containsKey(task.getId())) {
                    DingInventoryTaskListDto assetCountDto = assetCountMap.get(task.getId());
                    task.setNoCheckedNum(assetCountDto.getNoCheckedNum());
                    task.setCheckedNum(assetCountDto.getCheckedNum());
                }

                if (countAddAssetMap.containsKey(task.getId())) {
                    DingInventoryTaskListDto countAddAssetDto = countAddAssetMap.get(task.getId());
                    task.setCheckedAddInMarkNum(countAddAssetDto.getCheckedAddInMarkNum());
                    task.setCheckedAddNotInMarkNum(countAddAssetDto.getCheckedAddNotInMarkNum());
                }
            });
        }
    }

}
