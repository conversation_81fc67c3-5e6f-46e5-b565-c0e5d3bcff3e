package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.InventoryRangeGroup;
import com.niimbot.inventory.InventoryApproveSubmitDto;
import com.niimbot.inventory.InventoryCountDto;
import com.niimbot.inventory.InventoryCreateAssetQueryDto;
import com.niimbot.inventory.InventoryCreateAssetQueryPageDto;
import com.niimbot.inventory.InventoryDetailDto;
import com.niimbot.inventory.InventoryHistoryQueryDto;
import com.niimbot.inventory.InventoryRangeGroupCreateDto;
import com.niimbot.inventory.InventoryReportDto;
import com.niimbot.inventory.InventorySubmitDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.inventory.InventoryUpdateApproverDto;
import com.niimbot.inventory.InventoryView;
import com.niimbot.means.AssetDto;
import com.niimbot.means.AssetQueryConditionDto;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 盘点单接口
 *
 * <AUTHOR>
 * @date 2021/4/9 09:26
 */
public interface AsInventoryService extends IService<AsInventory> {
    /**
     * 盘点任务明细
     *
     * @param id
     * @return
     */
    InventoryDetailDto getByIdWithoutAsset(Long id);

    /**
     * 创建盘点单
     *
     * @param dto dto
     * @return 结果
     */
    Long create(InventorySubmitDto dto);

    /**
     * 单个任务驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    Boolean rejected(InventoryTaskApproveDto approveDto);

    /**
     * 整单驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    Boolean rejectedAll(InventoryApproveSubmitDto approveDto);

    /**
     * 整单同意
     *
     * @param approveDto approveDto
     * @return 结果
     */
    Boolean approved(InventoryApproveSubmitDto approveDto);

    /**
     * 单个同意
     *
     * @param approveDto
     * @return
     */
    Boolean approvedOne(InventoryTaskApproveDto approveDto);

    /**
     * 修改审批人
     *
     * @param approveDto
     * @return
     */
    Boolean updateApprover(InventoryUpdateApproverDto approveDto);

    /**
     * 终止
     *
     * @param inventoryId
     * @return 结果
     */
    Boolean stop(Long inventoryId);

    /**
     * 盘点单删除
     *
     * @param inventoryId
     * @return 结果
     */
    Boolean removeByInventoryId(Long inventoryId);

    /**
     * 盘点单概览
     *
     * @param id
     * @return 结果
     */
    InventoryView getInventoryView(Long id);

    /**
     * 盘点头部统计
     *
     * @return 结果
     */
    InventoryCountDto countByStatus();

    /**
     * 盘点分配方式详细
     *
     * @param dto 资产查询条件
     * @return 结果
     */
//    InventoryDispatchDetailDto getDispatchDetail(InventoryAssetRangeWithModeDto dto);

    /**
     * 盘点报告
     *
     * @param id
     * @return 结果
     */
    InventoryReportDto getInventoryReport(Long id);

//    List<Long> getUserByAssetIds(List<AssetDto> assetDtos, List<Long> empIds);

//    List<Long> getDefaultUserByAssetIds(int dispatchMode, List<AssetDto> assetDtos, List<Long> empIds);

    List<AsInventory> getInventoryForSendNotice(LocalDateTime timeout);

    /**
     * 处理盘点单创建资产
     *
     * @param queryDto
     */
    void handleInventoryCreateAsset(InventoryCreateAssetQueryDto queryDto);

    /**
     * 盘点单创建资产分页查询
     *
     * @param queryDto
     * @return
     */
    IPage<AssetDto> createAssetPage(InventoryCreateAssetQueryPageDto queryDto);

    /**
     * 盘点单已处理
     *
     * @param id
     */
    Boolean handledInventory(Long id);

    /**
     * 盘点范围创建
     *
     * @param dto
     * @return
     */
    List<InventoryRangeGroup> createRangeGroup(InventoryRangeGroupCreateDto dto);

    /**
     * 根据id获取未完成和终止的盘点单数
     * @param assetId
     * @return
     */
    Integer getProcessInventory(Long assetId);

    List<AssetDto> findInventoryDispatchAssets(Integer rangeType, Boolean enableExcludeRepair, Boolean enableExcludeDispose, List<AssetQueryConditionDto> assetQueryConditions, List<InventoryHistoryQueryDto> inventoryHistoryQueries);
}
