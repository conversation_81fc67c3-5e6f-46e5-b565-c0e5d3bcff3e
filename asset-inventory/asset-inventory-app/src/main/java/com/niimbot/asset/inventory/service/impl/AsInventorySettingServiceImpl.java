package com.niimbot.asset.inventory.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.mapper.AsInventorySettingMapper;
import com.niimbot.asset.inventory.mapstruct.InventorySettingMapStruct;
import com.niimbot.asset.inventory.model.AsInventorySetting;
import com.niimbot.asset.inventory.service.AsInventorySettingService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventorySettingAddListDto;
import com.niimbot.inventory.InventorySettingDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import lombok.RequiredArgsConstructor;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:23
 */
@Service
@RequiredArgsConstructor
public class AsInventorySettingServiceImpl extends ServiceImpl<AsInventorySettingMapper, AsInventorySetting> implements AsInventorySettingService {

    private final AsFormService formService;
    private final InventorySettingMapStruct inventorySettingMapStruct;

    private final List<String> excludeFieldType = ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES, FormFieldCO.YZC_SERIALNO, FormFieldCO.YZC_ASSET_SERIALNO);

    @Override
    public List<InventorySettingDto> settingList(Integer type) {
        FormVO formVO = formService.assetTpl();
        List<FormFieldCO> showFields = formVO.getFormFields().stream().filter(
                v -> (!excludeFieldType.contains(v.getFieldType()) && !v.isHidden())
        ).collect(Collectors.toList());
        List<AsInventorySetting> userSetting = list(Wrappers.lambdaQuery(AsInventorySetting.class)
                .eq(AsInventorySetting::getInventoryType, type)
                .eq(AsInventorySetting::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        if (CollUtil.isEmpty(userSetting)) {
            userSetting = list(Wrappers.lambdaQuery(AsInventorySetting.class)
                    .eq(AsInventorySetting::getInventoryType, type)
                    .eq(AsInventorySetting::getCompanyId, 0L));
        }
        Map<String, AsInventorySetting> settingMap = userSetting.stream().collect(
                Collectors.toMap(AsInventorySetting::getFieldCode, k -> k));
        return showFields.stream().map(f -> {
            InventorySettingDto setting = new InventorySettingDto();
            // 存在设置
            if (settingMap.containsKey(f.getFieldCode())) {
                AsInventorySetting inventorySetting = settingMap.get(f.getFieldCode());
                setting = inventorySettingMapStruct.toDto(inventorySetting);
            } else {
                // 不存在设置
                setting.setFieldCode(f.getFieldCode())
                        .setEnableEdit(false)
                        .setRequired(false)
                        .setReadOnly(false);
            }
            setting.setFieldName(f.getFieldName());
            if (f.getFieldProps() != null) {
                setting.setOriginFieldName(f.getFieldProps().getString("originFieldName"));
            } else {
                setting.setOriginFieldName(f.getFieldName());
            }
            return setting;
        }).collect(toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(InventorySettingAddListDto addListDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 删除历史配置
        remove(Wrappers.lambdaQuery(AsInventorySetting.class)
                .eq(AsInventorySetting::getCompanyId, companyId)
                .eq(AsInventorySetting::getInventoryType, addListDto.getInventoryType()));
        // 写入新配置
        List<AsInventorySetting> readOnlySetting = list(Wrappers.lambdaQuery(AsInventorySetting.class)
                .eq(AsInventorySetting::getInventoryType, addListDto.getInventoryType())
                .eq(AsInventorySetting::getReadOnly, true)
                .eq(AsInventorySetting::getCompanyId, 0L));
        Map<String, AsInventorySetting> readOnlyMap = readOnlySetting.stream().peek(s -> s.setId(null)
                .setCompanyId(companyId).setCreateBy(null).setUpdateBy(null)
                .setCreateTime(null).setUpdateTime(null)).collect(Collectors.toMap(AsInventorySetting::getFieldCode, k -> k));

        List<AsInventorySetting> saveList = addListDto.getSettingList().stream().map(addDto -> {
            if (readOnlyMap.containsKey(addDto.getFieldCode())) {
                return readOnlyMap.get(addDto.getFieldCode());
            } else {
                AsInventorySetting inventorySetting = new AsInventorySetting();
                inventorySetting.setInventoryType(addListDto.getInventoryType())
                        .setCompanyId(companyId)
                        .setFieldCode(addDto.getFieldCode())
                        .setReadOnly(false);
                // 编辑
                if (addListDto.getInventoryType() == 1) {
                    inventorySetting.setEnableEdit(addDto.getEnableEdit());
                    inventorySetting.setRequired(false);
                } else {
                    inventorySetting.setEnableEdit(BooleanUtil.isTrue(addDto.getRequired()) ? true : addDto.getEnableEdit());
                    inventorySetting.setRequired(addDto.getRequired());
                }
                return inventorySetting;
            }
        }).collect(toList());
        return saveBatch(saveList);
    }

    @Override
    public FormVO inventoryAttrList(Integer type) {
        // 查询动态属性
        FormVO formVO = formService.assetTpl();
        List<FormFieldCO> formFields = formVO.getFormFields();

        // 查询配置信息
        List<InventorySettingDto> settingList = settingList(type);
        Map<String, InventorySettingDto> settingMap = settingList.stream()
                .collect(Collectors.toMap(InventorySettingDto::getFieldCode, k -> k));

        List<FormFieldCO> required = formFields.stream().filter(field -> {
            // 只有开启编辑的返回
            if (settingMap.containsKey(field.getFieldCode())) {
                InventorySettingDto settingDto = settingMap.get(field.getFieldCode());
                // 如果是盘点上报，重写必填项
                if (type == 2) {
                    JSONObject fieldProps = field.getFieldProps();
                    fieldProps.put("required", BooleanUtil.isTrue(settingDto.getRequired()));
                    field.setFieldProps(fieldProps);
                }
                // 是否开启编辑或者上报
                return BooleanUtil.isTrue(settingDto.getEnableEdit());
            }
            return false;
        }).collect(toList());
        formVO.setFormFields(required);
        return formVO;
    }

}
