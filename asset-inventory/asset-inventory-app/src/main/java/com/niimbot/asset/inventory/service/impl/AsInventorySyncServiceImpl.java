package com.niimbot.asset.inventory.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.model.*;
import com.niimbot.asset.inventory.service.*;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventorySyncAssetDto;
import com.niimbot.inventory.InventorySyncAssetResultDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2024/7/4 11:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsInventorySyncServiceImpl implements AsInventorySyncService {

    private static final List<Integer> SUBMIT_STATUS = ListUtil.of(InventoryConstant.IN_PROGRESS, InventoryConstant.REJECTED);

    private final AssetService assetService;
    private final AssetUtil assetUtil;
    private final AsInventoryService inventoryService;
    private final AsInventoryTaskService inventoryTaskService;
    private final AsInventoryAssetService inventoryAssetService;
    private final AsInventoryTaskAssetService inventoryTaskAssetService;
    private final AsInventorySurplusService inventorySurplusService;
    private final AsInventoryAssetReportService inventoryAssetReportService;
    private final AsFormService formService;
    private final AsInventoryAssetLogService inventoryAssetLogService;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InventorySyncAssetResultDto syncUploadTaskAsset(InventorySyncAssetDto dto,
                                                           String version) {
        InventorySyncAssetResultDto resultDto = new InventorySyncAssetResultDto();

        // 没有盘点数据，直接返回
        if (CollUtil.isEmpty(dto.getSyncAssetData()) && CollUtil.isEmpty(dto.getSyncAddAssetData())) {
            return resultDto;
        }

        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 查询资产表单
        FormVO formVO = formService.assetTpl();
        // ==============================================盘点单校验====================================================
        // 获取盘点单
        AsInventory byId = inventoryService.getById(dto.getInventoryId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (InventoryConstant.TERMINATED.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_IS_TERMINATED);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // ==============================================盘点任务校验====================================================
        // 判断当前任务状态
        AsInventoryTask inventoryTask = inventoryTaskService.getById(dto.getTaskId());
        if (ObjectUtil.isNull(inventoryTask)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(inventoryTask.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }
        log.info("盘点[{}]任务[{}]，同步开始", inventoryTask.getInventoryId(), inventoryTask.getId());

        CountDownLatch countDownLatch = new CountDownLatch(2);
        taskExecutor.execute(() -> {
            try {
                // 盘点资产数据
                if (CollUtil.isNotEmpty(dto.getSyncAssetData())) {
                    resultDto.setConflictTaskAsset(syncUploadPdTaskAsset(inventoryTask, dto.getSyncAssetData(), formVO, version));
                }
            } finally {
                countDownLatch.countDown();
            }
        });

        taskExecutor.execute(() -> {
            try {
                // 盘盈资产数据
                if (CollUtil.isNotEmpty(dto.getSyncAddAssetData())) {
                    // v1版同步
                    if ("v1".equals(version)) {
                        // 1、首先删除当前人上一次盘点任务中的盘盈数据
                        Integer taskType = inventoryTask.getTaskType();
                        inventorySurplusService.removeWithReport(currentUserId, byId.getId(), taskType);
                    }
                    syncUploadPdTaskAddAsset(inventoryTask, dto.getSyncAddAssetData(), formVO);
                }
            } finally {
                countDownLatch.countDown();
            }
        });

        try {
            boolean await = countDownLatch.await(5, TimeUnit.MINUTES);
            if (!await) {
                log.error("盘点[{}]任务[{}]，同步超时", inventoryTask.getInventoryId(), inventoryTask.getId());
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点同步超时");
            }
        } catch (Exception e) {
            log.error("盘点[{}]任务[{}]，同步异常{}", inventoryTask.getInventoryId(), inventoryTask.getId(), e.getMessage(), e);
            Thread.currentThread().interrupt();
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点同步异常");
        }
        log.info("盘点[{}]任务[{}]，同步完成，冲突数据[{}]", inventoryTask.getInventoryId(), inventoryTask.getId(), resultDto);
        return resultDto;
    }

    private List<Long> syncUploadPdTaskAsset(AsInventoryTask inventoryTask,
                                             List<InventorySyncAssetDto.AssetPdDto> syncAssetData,
                                             FormVO formVO,
                                             String version) {
        List<Long> conflictAsset = new ArrayList<>();
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        List<DefaultTranslateUtil.FieldTranslation> translationField = FormFieldConvert.convertField(formVO.getFormFields());

        // 已数据
        Map<Long, InventorySyncAssetDto.AssetPdDto> tempMap = syncAssetData.stream()
                .collect(Collectors.toMap(InventorySyncAssetDto.AssetPdDto::getAssetId, k -> k, (k1, k2) -> k1));

        // 2024年8月6日，查询盘点原始资产信息（如果没有记录，就查询实时资产数据）
        List<AsInventoryTaskAsset> taskAssets = inventoryTaskAssetService.getInventoryTaskAssets(inventoryTask.getInventoryId(), inventoryTask.getId());
        Map<Long, JSONObject> sourceAssetMap = taskAssets.stream().filter(f -> CollUtil.isNotEmpty(f.getAssetOriginalData()))
                .collect(Collectors.toMap(AsInventoryTaskAsset::getAssetId, AsInventoryTaskAsset::getAssetOriginalData, (k1, k2) -> k1));
        List<Long> missingIds = new ArrayList<>(tempMap.keySet());
        missingIds.removeAll(sourceAssetMap.keySet());
        if (CollUtil.isNotEmpty(missingIds)) {
            // 资产原始数据 获取资产实时数据
            List<AsAsset> sourceAssetList = assetService.listByIdsNoPerm(missingIds);
            sourceAssetMap.putAll(covertDtoToJsonBatch(sourceAssetList, translationField));
        }

        // ==============================================盘点资产====================================================
        List<AsInventoryAsset> inventoryAssetList = inventoryAssetService.list(new QueryWrapper<AsInventoryAsset>().lambda()
                .select(AsInventoryAsset::getId, AsInventoryAsset::getAssetId, AsInventoryAsset::getInventoryTime)
                .eq(AsInventoryAsset::getInventoryId, inventoryTask.getInventoryId())
                .eq(AsInventoryAsset::getInventoryTaskId, inventoryTask.getId())
                .in(AsInventoryAsset::getAssetId, tempMap.keySet()));
        if (CollUtil.isEmpty(inventoryAssetList)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        // 待更新盘点资产集合
        List<AsInventoryAsset> updateInventoryAssetList = new ArrayList<>();
        // 需要更新的日志数据
        List<AsInventoryAssetLog> inventoryAssetLogSaveList = new ArrayList<>();
        // 待更新盘点资产状态集合
        List<Long> needUpdateTaskStatusAssetIds = new ArrayList<>();

        // 循环处理盘点资产
        for (AsInventoryAsset inventoryAsset : inventoryAssetList) {
            // 盘点资产数据
            InventorySyncAssetDto.AssetPdDto assetPdDto = tempMap.get(inventoryAsset.getAssetId());
            if (ObjectUtil.isNull(assetPdDto)) {
                continue;
            }
            // 实时资产数据
            JSONObject sourceAssetJson = sourceAssetMap.get(inventoryAsset.getAssetId());
            if (ObjectUtil.isNull(sourceAssetJson)) {
                continue;
            }

            // 校验InventoryTime与当前同步时间，如果当前同步时间早与盘点时间，则说明被其他人更新过，存在冲突
            if (ObjectUtil.isNotNull(inventoryAsset.getInventoryTime()) &&
                    inventoryAsset.getInventoryTime().isAfter(assetPdDto.getInventoryTime())) {
                // 返回冲突的资产Id
                conflictAsset.add(inventoryAsset.getAssetId());
                continue;
            }

            needUpdateTaskStatusAssetIds.add(inventoryAsset.getAssetId());

            // 盘点资产数据
            AsInventoryAsset asInventoryAsset = new AsInventoryAsset();
            asInventoryAsset.setId(inventoryAsset.getId())
                    .setAssetSnapshotData(assetPdDto.getAssetSnapshotData())
                    .setChecked(true)
                    .setActualInventoryUser(currentUserId)
                    .setInventoryMode(assetPdDto.getInventoryMode())
                    .setInventoryTerminal(assetPdDto.getInventoryTerminal())
                    .setPictureUrl(assetPdDto.getPictureUrl())
                    .setRemark(assetPdDto.getRemark());
            if ("v1".equals(version)) {
                // 老版本更新直接写入服务器时间
                asInventoryAsset.setInventoryTime(LocalDateTime.now());
            } else {
                asInventoryAsset.setInventoryTime(assetPdDto.getInventoryTime() != null ? assetPdDto.getInventoryTime() : LocalDateTime.now());
            }
            updateInventoryAssetList.add(asInventoryAsset);

            // 生成修改记录
            List<InventoryAssetLogData> logData = inventoryAssetService.buildAssetLog(sourceAssetJson, assetPdDto.getAssetSnapshotData(), formVO.getFormFields());
            // 判断资产修改日志
            AsInventoryAssetLog inventoryAssetLog = new AsInventoryAssetLog();
            inventoryAssetLog.setInventoryId(inventoryTask.getInventoryId())
                    .setAssetId(inventoryAsset.getAssetId())
                    .setLogData(logData);
            inventoryAssetLogSaveList.add(inventoryAssetLog);
        }

        // 重新计算盘点任务资产盘点状态
        if (CollUtil.isNotEmpty(needUpdateTaskStatusAssetIds)) {
            inventoryTaskAssetService.update(Wrappers.lambdaUpdate(AsInventoryTaskAsset.class)
                    .set(AsInventoryTaskAsset::getStatus, InventoryConstant.INVENTORY_TASK_ASSET_STATUS_CHECK)
                    .set(AsInventoryTaskAsset::getAssetMark, InventoryConstant.INVENTORY_TASK_ASSET_MARK_NONE)
                    .eq(AsInventoryTaskAsset::getInventoryId, inventoryTask.getInventoryId())
                    .eq(AsInventoryTaskAsset::getInventoryTaskId, inventoryTask.getId())
                    .in(AsInventoryTaskAsset::getAssetId, needUpdateTaskStatusAssetIds));
        }

        log.info("盘点[{}]任务[{}]，已盘数据成功更新{}条", inventoryTask.getInventoryId(), inventoryTask.getId(), updateInventoryAssetList.size());
        // 更新盘点资产数据
        inventoryAssetService.updateBatchById(updateInventoryAssetList);
        // 更新盘点单已盘数量
        long checkedNum = inventoryAssetService.count(Wrappers.lambdaQuery(AsInventoryAsset.class)
                .eq(AsInventoryAsset::getInventoryId, inventoryTask.getInventoryId())
                .eq(AsInventoryAsset::getInventoryTaskId, inventoryTask.getId())
                .eq(AsInventoryAsset::getChecked, true));
        inventoryTaskService.update(Wrappers.lambdaUpdate(AsInventoryTask.class)
                .set(AsInventoryTask::getCheckedNum, checkedNum)
                .eq(AsInventoryTask::getId, inventoryTask.getId()));

        if (CollUtil.isNotEmpty(inventoryAssetLogSaveList)) {
            // 重写日志数据
            inventoryAssetLogService.remove(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                    .eq(AsInventoryAssetLog::getInventoryId, inventoryTask.getInventoryId())
                    .in(AsInventoryAssetLog::getAssetId, inventoryAssetLogSaveList.stream()
                            .map(AsInventoryAssetLog::getAssetId).collect(toList())));
            // 新增日志数据
            inventoryAssetLogService.saveBatch(inventoryAssetLogSaveList);
        }
        return conflictAsset;
    }

    private void syncUploadPdTaskAddAsset(AsInventoryTask inventoryTask, List<InventorySyncAssetDto.AddAssetDto> syncAddAssetData, FormVO formVO) {
        List<DefaultTranslateUtil.FieldTranslation> translationField = FormFieldConvert.convertField(formVO.getFormFields());

        // 2024年7月3日，盘盈特殊逻辑，为了兼容APP历史数据，采用以下特殊逻辑判断【盘盈在册】和【上报】
        // 1、场景一，APP上报，所以assetData的Id为空，assetId为空，属于新增
        // 2、场景二，APP上报，所以assetData的Id不为空，assetId为空，属于编辑
        // 3、场景三，APP扫码盘盈在册，assetData的Id和assetId相同，表示未同步到系统，还未分配reportId
        // 4、场景四，APP扫码盘盈在册，assetData的Id和assetId不相同，表示已同步到系统，id为分配后的reportId
        // 如果operationType为空，说明app未更新，不走删除逻辑

        // 新增盘盈不在册
        List<InventorySyncAssetDto.AddAssetDto> surplusNotMarkInsertList = new ArrayList<>();
        // 更新盘盈不在册
        List<InventorySyncAssetDto.AddAssetDto> surplusNotMarkUpdateList = new ArrayList<>();
        // 删除盘盈不在册
        List<InventorySyncAssetDto.AddAssetDto> surplusNotMarkDeleteList = new ArrayList<>();

        // 新增盘盈在册
        List<InventorySyncAssetDto.AddAssetDto> surplusMarkInsertList = new ArrayList<>();
        // 更新盘盈在册
        List<InventorySyncAssetDto.AddAssetDto> surplusMarkUpdateList = new ArrayList<>();
        // 删除盘盈在册
        List<InventorySyncAssetDto.AddAssetDto> surplusMarkDeleteList = new ArrayList<>();

        // 分类处理
        for (InventorySyncAssetDto.AddAssetDto addAssetDatum : syncAddAssetData) {
            Long reportId = addAssetDatum.getAssetData().getLong("id");
            Long assetId = addAssetDatum.getAssetData().getLong("assetId");
            // 场景一, 手动上报新增
            if (reportId == null) {
                addAssetDatum.setAssetMark(InventoryConstant.ASSET_MARK_NOT_ON);
                surplusNotMarkInsertList.add(addAssetDatum);
            }
            // 场景二, 手动上报编辑
            else if (assetId == null) {
                addAssetDatum.setAssetMark(InventoryConstant.ASSET_MARK_NOT_ON);
                // 判断APP是否传递删除操作符，否则都是更新
                if ("delete".equals(addAssetDatum.getOperationType())) {
                    surplusNotMarkDeleteList.add(addAssetDatum);
                } else {
                    surplusNotMarkUpdateList.add(addAssetDatum);
                }
            }
            // 场景三，扫码盘盈在册，未同步过系统，走新增逻辑
            else if (ObjectUtil.equals(reportId, assetId)) {
                addAssetDatum.setAssetMark(InventoryConstant.ASSET_MARK_ON);
                surplusMarkInsertList.add(addAssetDatum);
            }
            // 场景四，扫码盘盈在册，同步过系统，走更新逻辑
            else {
                addAssetDatum.setAssetMark(InventoryConstant.ASSET_MARK_ON);
                // 判断APP是否传递删除操作符，否则都是更新
                if ("delete".equals(addAssetDatum.getOperationType())) {
                    surplusMarkDeleteList.add(addAssetDatum);
                } else {
                    surplusMarkUpdateList.add(addAssetDatum);
                }
            }
        }

        // 盘盈在册的资产，新增编辑都需要记录修改日志，所以需要查询资产实时数据
        Map<Long, JSONObject> surplusMarkAssetSourceMap = new HashMap<>();
        List<InventorySyncAssetDto.AddAssetDto> addAssetDtos = CollUtil.unionAll(surplusMarkInsertList, surplusMarkUpdateList);
        if (CollUtil.isNotEmpty(addAssetDtos)) {
            List<Long> surplusMarkAssetIds = addAssetDtos.stream().map(f -> f.getAssetData().getLong("assetId")).collect(toList());
            // 获取资产实时数据
            List<AsAsset> sourceAssetList = assetService.listByIdsNoPerm(surplusMarkAssetIds);
            surplusMarkAssetSourceMap = covertDtoToJsonBatch(sourceAssetList, translationField);
        }

        // 处理新增
        handleInsert(inventoryTask, surplusNotMarkInsertList, surplusMarkInsertList, surplusMarkAssetSourceMap, formVO);
        // 处理更新
        handleUpdate(inventoryTask, surplusNotMarkUpdateList, surplusMarkUpdateList, surplusMarkAssetSourceMap, formVO);
        // 处理删除
        handleDelete(inventoryTask, surplusNotMarkDeleteList, surplusMarkDeleteList);
    }

    private void handleInsert(AsInventoryTask inventoryTask,
                              List<InventorySyncAssetDto.AddAssetDto> surplusNotMarkInsertList,
                              List<InventorySyncAssetDto.AddAssetDto> surplusMarkInsertList,
                              Map<Long, JSONObject> surplusMarkAssetSourceMap,
                              FormVO formVO) {
        log.info("盘点[{}]任务[{}]，盘盈新增{}条", inventoryTask.getInventoryId(), inventoryTask.getId(), surplusMarkInsertList.size());
        log.info("盘点[{}]任务[{}]，上报新增{}条", inventoryTask.getInventoryId(), inventoryTask.getId(), surplusNotMarkInsertList.size());

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 获取当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();

        List<AsInventoryAssetLog> inventoryAssetLogList = new ArrayList<>();
        List<AsInventoryAssetReport> inventoryAssetReportList = new ArrayList<>();
        List<AsInventorySurplus> inventorySurplusList = new ArrayList<>();
        List<InventorySyncAssetDto.AddAssetDto> addList = CollUtil.unionAll(surplusMarkInsertList, surplusNotMarkInsertList);

        List<Long> assetIds = new ArrayList<>();
        for (InventorySyncAssetDto.AddAssetDto addAssetDto : addList) {
            // 盘盈在册写入修改记录
            if (addAssetDto.getAssetMark().equals(InventoryConstant.ASSET_MARK_ON)) {
                Long assetId = addAssetDto.getAssetData().getLong("assetId");
                // 如果资产Id不存在，忽略
                if (!surplusMarkAssetSourceMap.containsKey(assetId)) {
                    log.warn("同步盘盈新增数据异常，资产Id不存在，[{}]", addAssetDto);
                    continue;
                }
                assetIds.add(assetId);
                List<InventoryAssetLogData> logData = inventoryAssetService.buildAssetLog(surplusMarkAssetSourceMap.get(assetId), addAssetDto.getAssetData(), formVO.getFormFields());
                // 资产修改日志
                AsInventoryAssetLog inventoryAssetLog = new AsInventoryAssetLog();
                inventoryAssetLog.setInventoryId(inventoryTask.getInventoryId())
                        .setAssetId(assetId)
                        .setLogData(logData);
                inventoryAssetLogList.add(inventoryAssetLog);
            }
            Long reportId = IdUtils.getId();
            // 盘盈资产数据
            AsInventoryAssetReport asInventoryAssetReport = new AsInventoryAssetReport();
            asInventoryAssetReport.setId(reportId)
                    .setReportType(InventoryConstant.INVENTORY_REPORT)
                    .setCompanyId(companyId)
                    .setAssetData(addAssetDto.getAssetData())
                    .setReporter(currentUserId)
                    .setAssetMark(addAssetDto.getAssetMark())
                    .setAssetId(addAssetDto.getAssetData().getLong("assetId"))
                    .setTaskType(InventoryConstant.TASK_TYPE_1)
                    .setRemark(addAssetDto.getRemark())
                    .setInventoryTerminal(InventoryConstant.MOBILE_MANAGE);

            inventoryAssetReportList.add(asInventoryAssetReport);

            // 盘盈资产关联数据
            AsInventorySurplus inventorySurplus = new AsInventorySurplus();
            inventorySurplus.setReportId(reportId)
                    .setInventoryId(inventoryTask.getInventoryId())
                    .setInventoryTaskId(inventoryTask.getId());
            inventorySurplusList.add(inventorySurplus);
        }

        // 同一个盘点单内同一个资产盘盈采用覆盖，所以先删除其他存在的盘盈在册数据
        if (CollUtil.isNotEmpty(assetIds)) {
            // 删除已经存在的盘盈资产
            inventorySurplusService.removeExistAsset(assetIds, inventoryTask.getInventoryId());
        }

        inventoryAssetLogService.saveBatch(inventoryAssetLogList);
        inventoryAssetReportService.saveBatch(inventoryAssetReportList);
        inventorySurplusService.saveBatch(inventorySurplusList);
    }

    private void handleUpdate(AsInventoryTask inventoryTask,
                              List<InventorySyncAssetDto.AddAssetDto> surplusNotMarkUpdateList,
                              List<InventorySyncAssetDto.AddAssetDto> surplusMarkUpdateList,
                              Map<Long, JSONObject> surplusMarkAssetSourceMap,
                              FormVO formVO) {
        log.info("盘点[{}]任务[{}]，盘盈更新{}条", inventoryTask.getInventoryId(), inventoryTask.getId(), surplusMarkUpdateList.size());
        log.info("盘点[{}]任务[{}]，上报更新{}条", inventoryTask.getInventoryId(), inventoryTask.getId(), surplusNotMarkUpdateList.size());

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        List<AsInventoryAssetLog> inventoryAssetLogList = new ArrayList<>();
        List<AsInventoryAssetReport> inventoryAssetReportList = new ArrayList<>();
        List<InventorySyncAssetDto.AddAssetDto> editList = CollUtil.unionAll(surplusNotMarkUpdateList, surplusMarkUpdateList);

        List<Long> assetIds = new ArrayList<>();
        for (InventorySyncAssetDto.AddAssetDto addAssetDto : editList) {
            // 盘盈在册写入修改记录
            if (addAssetDto.getAssetMark().equals(InventoryConstant.ASSET_MARK_ON)) {
                Long assetId = addAssetDto.getAssetData().getLong("assetId");
                // 如果资产Id不存在，忽略
                if (!surplusMarkAssetSourceMap.containsKey(assetId)) {
                    log.warn("同步盘盈新增数据异常，资产Id不存在，[{}]", addAssetDto);
                    continue;
                }
                assetIds.add(assetId);
                List<InventoryAssetLogData> logData = inventoryAssetService.buildAssetLog(surplusMarkAssetSourceMap.get(assetId), addAssetDto.getAssetData(), formVO.getFormFields());
                // 资产修改日志
                AsInventoryAssetLog inventoryAssetLog = new AsInventoryAssetLog();
                inventoryAssetLog.setInventoryId(inventoryTask.getInventoryId())
                        .setAssetId(assetId)
                        .setLogData(logData);
                inventoryAssetLogList.add(inventoryAssetLog);
            }
            Long reportId = addAssetDto.getAssetData().getLong("id");
            AsInventoryAssetReport asInventoryAssetReport = new AsInventoryAssetReport();
            asInventoryAssetReport.setId(reportId)
                    .setAssetData(addAssetDto.getAssetData())
                    .setReporter(currentUserId)
                    .setAssetMark(addAssetDto.getAssetMark())
                    .setAssetId(addAssetDto.getAssetData().getLong("assetId"))
                    .setRemark(addAssetDto.getRemark())
                    .setInventoryTerminal(InventoryConstant.MOBILE_MANAGE);
            inventoryAssetReportList.add(asInventoryAssetReport);
        }
        if (CollUtil.isNotEmpty(assetIds)) {
            // 删除日志数据
            inventoryAssetLogService.remove(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                    .eq(AsInventoryAssetLog::getInventoryId, inventoryTask.getInventoryId())
                    .in(AsInventoryAssetLog::getAssetId, assetIds));
        }
        // 新增日志数据
        inventoryAssetLogService.saveBatch(inventoryAssetLogList);
        // 更新上报数据
        inventoryAssetReportService.updateBatchById(inventoryAssetReportList);
    }

    private void handleDelete(AsInventoryTask inventoryTask,
                              List<InventorySyncAssetDto.AddAssetDto> surplusNotMarkDeleteList,
                              List<InventorySyncAssetDto.AddAssetDto> surplusMarkDeleteList) {
        log.info("盘点[{}]任务[{}]，盘盈删除{}条", inventoryTask.getInventoryId(), inventoryTask.getId(), surplusMarkDeleteList.size());
        log.info("盘点[{}]任务[{}]，上报删除{}条", inventoryTask.getInventoryId(), inventoryTask.getId(), surplusNotMarkDeleteList.size());

        List<InventorySyncAssetDto.AddAssetDto> removeList = CollUtil.unionAll(surplusNotMarkDeleteList, surplusMarkDeleteList);
        List<Long> reportIds = new ArrayList<>();
        List<Long> assetIds = new ArrayList<>();
        for (InventorySyncAssetDto.AddAssetDto addAssetDto : removeList) {
            reportIds.add(addAssetDto.getAssetData().getLong("id"));
            assetIds.add(addAssetDto.getAssetData().getLong("assetId"));
        }
        if (CollUtil.isNotEmpty(assetIds)) {
            // 删除日志数据
            inventoryAssetLogService.remove(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                    .eq(AsInventoryAssetLog::getInventoryId, inventoryTask.getInventoryId())
                    .in(AsInventoryAssetLog::getAssetId, assetIds));
        }
        if (CollUtil.isNotEmpty(reportIds)) {
            // 删除上报数据
            inventoryAssetReportService.remove(Wrappers.lambdaQuery(AsInventoryAssetReport.class)
                    .in(AsInventoryAssetReport::getId, reportIds));
            inventorySurplusService.remove(Wrappers.lambdaQuery(AsInventorySurplus.class)
                    .in(AsInventorySurplus::getReportId, reportIds));
        }
    }

    private Map<Long, JSONObject> covertDtoToJsonBatch(List<AsAsset> sourceList, List<DefaultTranslateUtil.FieldTranslation> translationField) {
        List<JSONObject> translateList = new ArrayList<>();
        for (AsAsset source : sourceList) {
            JSONObject translate = source.translate();
            translateList.add(translate);
        }
        assetUtil.translateAssetJsonBatch(translateList, translationField);
        return translateList.stream().collect(Collectors.toMap(f -> f.getLong("id"), k -> k, (k1, k2) -> k1));
    }

}
