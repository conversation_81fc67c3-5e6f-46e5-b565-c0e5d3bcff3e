package com.niimbot.asset.inventory.service;

import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.todo.model.AsTodo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/15 8:54
 */
public interface InventoryTodoService {

    void sendInventoryTaskTodoMsg(List<AsInventoryTask> tasks, AsInventory inventory);

    void sendInventoryTaskTodoMsg(List<AsInventoryTask> tasks, AsInventory inventory, Set<Long> sendMsgUsers);

    void updateInventoryTaskTodoMsg(AsInventoryTask inventory);

    void sendInventoryApproveTodoMsg(AsInventory inventory, AsInventoryTask inventoryTask);

    void sendInventoryApproveTodoMsg(AsInventory inventory, List<AsTodo> todoList);

    void updateInventoryApproveTodoMsg(AsInventory inventory, Long taskId);

    void deleteInventoryTodoMsg(List<Long> taskIds, Long inventoryId);

    void deleteInventoryTaskTodoMsg(AsInventoryTask task);

    void deleteInventoryApproveTodoMsg(Long inventoryId, Long originApprover);

}
