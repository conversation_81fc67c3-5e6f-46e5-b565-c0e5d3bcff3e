package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryTaskAsset;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 盘点任务-资产盘点状态 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
public interface AsInventoryTaskAssetService extends IService<AsInventoryTaskAsset> {
    /**
     * 查询盘点任务资产状态
     * @param inventoryId
     * @param taskId
     * @return
     */
    List<AsInventoryTaskAsset> getInventoryTaskAssets(Long inventoryId, Long taskId);

    /**
     * 查询盘点任务资产状态
     * @param inventoryId
     * @param taskId
     * @param assetIds
     * @return
     */
    List<AsInventoryTaskAsset> getInventoryTaskAssets(Long inventoryId, Long taskId, Collection<Long> assetIds);

    /**
     * 查询盘点任务资产状态
     * @param inventoryId
     * @param taskId
     * @param assetId
     * @return
     */
    AsInventoryTaskAsset getInventoryTaskAsset(Long inventoryId, Long taskId, Long assetId);

    /**
     * 删除盘点任务状态
     * @param inventoryId
     */
    void removeByInventoryId(Long inventoryId);
}
