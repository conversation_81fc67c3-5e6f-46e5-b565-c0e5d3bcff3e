package com.niimbot.asset.inventory.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.inventory.model.AsInventoryAssetReport;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘点-资产盘盈表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryAssetReportMapper extends BaseMapper<AsInventoryAssetReport> {

    List<Long> unHandleIds(@Param("companyId") Long companyId, @Param("ids") List<Long> ids);
}
