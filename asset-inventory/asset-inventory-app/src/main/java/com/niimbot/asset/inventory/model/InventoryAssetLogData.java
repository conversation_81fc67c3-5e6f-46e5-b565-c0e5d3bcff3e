package com.niimbot.asset.inventory.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 修改数据日志
 *
 * <AUTHOR>
 * @date 2021/4/9 10:26
 */
@Data
@Accessors(chain = true)
public class InventoryAssetLogData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资产属性code")
    private String code;

    @ApiModelProperty(value = "资产属性名称")
    private String name;

    @ApiModelProperty(value = "资产原始数据")
    private String originalValue;

    @ApiModelProperty(value = "资产最终数据")
    private String finalValue;
}
