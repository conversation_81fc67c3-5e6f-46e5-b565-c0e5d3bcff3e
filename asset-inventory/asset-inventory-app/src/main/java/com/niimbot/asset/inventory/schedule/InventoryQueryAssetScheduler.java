package com.niimbot.asset.inventory.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.inventory.model.AsInventoryQueryAsset;
import com.niimbot.asset.inventory.service.InventoryQueryAssetService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * 删除昨天以前的预创建盘点单资产数据
 *
 * <AUTHOR>
 * @date 2022/9/14 16:13
 */
@Slf4j
@Component
public class InventoryQueryAssetScheduler {
    @Resource
    private InventoryQueryAssetService inventoryQueryAssetService;
    @Autowired
    private RedisDistributeLock redisDistributeLock;

    @Scheduled(cron = "0 0 1 * * ?")
    public void cleanQueryAssets() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }

        redisDistributeLock.lock("inventoryQueryAsset", 3, TimeUnit.MINUTES, (a) -> {
            // 零点
            LocalDateTime zeroOclick = LocalDateTime.now().withHour(0)
                    .withMinute(0).withSecond(0).withNano(0);
            inventoryQueryAssetService.remove(
                    Wrappers.<AsInventoryQueryAsset>lambdaQuery()
                            .le(AsInventoryQueryAsset::getCreateTime, zeroOclick));
        });
    }
}
