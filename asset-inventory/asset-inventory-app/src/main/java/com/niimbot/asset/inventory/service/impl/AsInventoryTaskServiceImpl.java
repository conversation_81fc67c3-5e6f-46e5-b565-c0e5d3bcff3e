package com.niimbot.asset.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.mapper.AsInventoryTaskMapper;
import com.niimbot.asset.inventory.model.*;
import com.niimbot.asset.inventory.service.*;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 盘点任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Slf4j
@Service
public class AsInventoryTaskServiceImpl extends ServiceImpl<AsInventoryTaskMapper, AsInventoryTask> implements AsInventoryTaskService {

    private static final List<Integer> SUBMIT_STATUS = ListUtil.of(InventoryConstant.IN_PROGRESS, InventoryConstant.REJECTED);

    @Resource
    private AsInventoryService inventoryService;

    @Resource
    private AsInventoryApproveService inventoryApproveService;

    @Resource
    private AsInventoryAssetService inventoryAssetService;

    @Resource
    private AsInventoryTaskAssetService inventoryTaskAssetService;

    @Resource
    private AsInventoryConfigService inventoryConfigService;

    @Resource
    private AsInventorySurplusService inventorySurplusService;

    @Resource
    private AsInventoryAssetReportService inventoryAssetReportService;

    @Resource
    private AsFormService formService;

    @Resource
    private AsInventoryAssetLogService inventoryAssetLogService;

    @Resource
    private AssetService assetService;

    @Resource
    private AsInventorySettingService settingService;

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Resource
    private InventoryTodoService inventoryTodoService;

    @Resource
    private AssetUtil assetUtil;

    /**
     * 盘点任务分页列表
     *
     * @param dto
     * @return
     */
    @Override
    public IPage<InventoryTaskListDto> selectList(InventoryQueryDto dto) {
        // 默认排序
        dto.setOrder("desc");
        dto.setSidx("createTime, id");
        if (dto.getStatus() != null && dto.getStatus().size() == 1) {
            Integer status = dto.getStatus().get(0);
            if (InventoryConstant.PEND_APPROVAL.equals(status)) {
                dto.setSidx("submitTime, id");
            } else if (InventoryConstant.COMPLETED.equals(status)) {
                dto.setSidx("approvedTime, id");
            } else if (InventoryConstant.TERMINATED.equals(status)) {
                dto.setSidx("terminatedTime, id");
            } else if (InventoryConstant.REJECTED.equals(status)) {
                dto.setSidx("rejectedTime, id");
            }
        }
        IPage<InventoryTaskListDto> taskListDtoIPage = this.baseMapper.selectTaskList(
                dto.buildIPage(),
                LoginUserThreadLocal.getCompanyId(),
                LoginUserThreadLocal.getCurrentUserId(),
                dto);
        Map<Long, String> cache = new HashMap<>();
        taskListDtoIPage.getRecords().forEach(o -> {
            StringJoiner inventoryUserText = new StringJoiner(", ");
            o.getInventoryUsers().forEach(userId -> {
                if (cache.containsKey(userId)) {
                    inventoryUserText.add(cache.get(userId));
                } else {
                    String userNameAndCode = cacheResourceUtil.getUserNameAndCode(userId);
                    cache.put(userId, userNameAndCode);
                    inventoryUserText.add(userNameAndCode);
                }
            });
            o.setInventoryUsersText(inventoryUserText.toString());
        });
        return taskListDtoIPage;
    }

    @Override
    public IPage<InventoryTaskListDto> selectListApp(InventoryQueryDto dto) {
        IPage<InventoryTaskListDto> inventoryTaskListDtoIPage = selectList(dto);
        List<InventoryTaskListDto> records = inventoryTaskListDtoIPage.getRecords();

        Map<Long, List<InventoryTaskListDto>> map = records.stream().collect(
                Collectors.groupingBy(InventoryTaskListDto::getInventoryId));
        map.forEach(this::fillPdData);
        return inventoryTaskListDtoIPage;
    }

    private void fillPdData(Long inventoryId, List<InventoryTaskListDto> taskList) {
        List<Long> taskIds = taskList.stream().map(InventoryTaskListDto::getId).collect(Collectors.toList());
        // 补齐未盘，已盘，盘盈在册，盘盈不在册
        if (CollUtil.isNotEmpty(taskIds)) {

            // 查询未盘已盘
            List<InventoryTaskListDto> assetCount = this.baseMapper.assetCount(inventoryId, taskIds);
            Map<Long, InventoryTaskListDto> assetCountMap = assetCount.stream()
                    .collect(Collectors.toMap(InventoryTaskListDto::getId, k -> k));

            // 查询盘盈在册，盘盈不在册
            List<InventoryTaskListDto> countAddAsset = this.baseMapper.countAddAsset(inventoryId, taskIds);
            Map<Long, InventoryTaskListDto> countAddAssetMap = countAddAsset.stream()
                    .collect(Collectors.toMap(InventoryTaskListDto::getId, k -> k));

            taskList.forEach(task -> {
                if (assetCountMap.containsKey(task.getId())) {
                    InventoryTaskListDto assetCountDto = assetCountMap.get(task.getId());
                    task.setNoCheckedNum(assetCountDto.getNoCheckedNum());
                    task.setCheckedNum(assetCountDto.getCheckedNum());
                }

                if (countAddAssetMap.containsKey(task.getId())) {
                    InventoryTaskListDto countAddAssetDto = countAddAssetMap.get(task.getId());
                    task.setCheckedAddInMarkNum(countAddAssetDto.getCheckedAddInMarkNum());
                    task.setCheckedAddNotInMarkNum(countAddAssetDto.getCheckedAddNotInMarkNum());
                }
            });
        }
    }

    /**
     * 提交审核
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submit(InventoryTaskApproveDto approveDto) {
        Long taskId = approveDto.getTaskId();
        // 获取盘点任务
        AsInventoryTask byId = this.getById(taskId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        // 盘点任务已经被终止
        if (InventoryConstant.TERMINATED.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_IS_TERMINATED);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 获取盘点单
        Long inventoryId = byId.getInventoryId();
        AsInventory inventory = inventoryService.getById(inventoryId);
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是盘点人
        List<Long> inventoryUsers = byId.getInventoryUsers();
        if (!inventoryUsers.contains(currentUserId)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
        }

        // 1、修改任务状态为待审核
        AsInventoryTask inventoryTask = new AsInventoryTask();
        inventoryTask.setId(taskId)
                .setStatus(InventoryConstant.PEND_APPROVAL)
                .setLastSubmitTime(LocalDateTime.now());
        this.updateById(inventoryTask);

        if (InventoryConstant.TASK_TYPE_1 == byId.getTaskType()) {
            // 2、如果该盘点任务全都是待审核状态，则修改盘点单状态为待审核
            // 盘点任务数量
            List<AsInventoryTask> taskList = this.list(Wrappers.lambdaQuery(AsInventoryTask.class)
                    .eq(AsInventoryTask::getInventoryId, inventoryId));

            long inProgressTask = 0L;
            for (AsInventoryTask task : taskList) {
                if (task.getId().equals(taskId)) {
                    inventoryTask = task;
                }
                if (InventoryConstant.IN_PROGRESS.equals(task.getStatus()) ||
                        InventoryConstant.REJECTED.equals(task.getStatus())) {
                    inProgressTask++;
                }
            }

            if (inProgressTask == 0L) {
                AsInventory asInventory = new AsInventory();
                asInventory.setId(inventoryId).setStatus(InventoryConstant.PEND_APPROVAL);
                inventoryService.updateById(asInventory);
            }

            // 给审核人发送代办
            inventoryTodoService.sendInventoryApproveTodoMsg(inventory, inventoryTask);

            // 3、添加审核记录表
            inventoryApproveService.addSubmitRecord(byId.getInventoryId(), taskId, approveDto.getOpinion());
        }

        // 修改代办为已完成
        inventoryTodoService.updateInventoryTaskTodoMsg(byId);

        return true;
    }

    /**
     * 手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean manualInventory(InventoryManualDto dto) {
        Long inventoryId = dto.getInventoryId();
        // 获取盘点单
        AsInventory byId = inventoryService.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 查询盘点配置
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(inventoryId);

        if (inventoryConfig.getEnableTakePicture() && StrUtil.isBlank(dto.getPictureUrl())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NEED_TAKE_PICTURE);
        }

        // 获取盘点资产
        Long assetId = dto.getAssetId();
        AsInventoryAsset inventoryAsset = inventoryAssetService.getOne(new QueryWrapper<AsInventoryAsset>().lambda()
                .eq(AsInventoryAsset::getInventoryId, inventoryId)
                .eq(AsInventoryAsset::getAssetId, assetId));
        AsInventoryTaskAsset taskAsset =
                inventoryTaskAssetService.getInventoryTaskAsset(inventoryId, dto.getInventoryTaskId(), assetId);
        if (ObjectUtil.isNull(inventoryAsset)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }
        if (ObjectUtil.isNull(taskAsset)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }
        if (InventoryConstant.INVENTORY_TASK_ASSET_STATUS_CHECK == taskAsset.getStatus()) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_CHECKED);
        }

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是盘点人
        List<Long> inventoryUser = inventoryAsset.getInventoryUser();
        if (!inventoryUser.contains(currentUserId)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
        }

        // 修改盘点资产
        Integer inventoryTerminal = ObjectUtil.isNotNull(dto.getInventoryTerminal()) ? dto.getInventoryTerminal() : InventoryConstant.PC;
        AsInventoryAsset inventoryAssetUpdate = new AsInventoryAsset();
        inventoryAssetUpdate.setId(inventoryAsset.getId()).setChecked(true)
                .setActualInventoryUser(currentUserId)
                .setInventoryMode(Objects.nonNull(dto.getInventoryMode()) ? dto.getInventoryMode() : InventoryConstant.MANUAL)
                .setInventoryTerminal(inventoryTerminal)
                .setInventoryTime(LocalDateTime.now())
                .setPictureUrl(dto.getPictureUrl()).setRemark(dto.getRemark());

        if (!inventoryAssetService.updateById(inventoryAssetUpdate)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_SUBMIT_FAIL);
        }

        // 修改盘点任务资产盘点状态
        taskAsset.setStatus(InventoryConstant.INVENTORY_TASK_ASSET_STATUS_CHECK);
        taskAsset.setAssetMark(InventoryConstant.INVENTORY_TASK_ASSET_MARK_NONE);
        inventoryTaskAssetService.updateById(taskAsset);

        AsInventoryTask task = getById(dto.getInventoryTaskId());
        Integer checkedNum = task.getCheckedNum();
        task.setCheckedNum(checkedNum + 1);
        this.updateById(task);
        return true;
    }

    /**
     * 资产上报
     *
     * @param dto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reportAsset(InventorySurplusDto dto) {
        Long inventoryId = dto.getInventoryId();
        // 获取盘点单
        AsInventory byId = inventoryService.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 获取当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();

        // 首先生成id
        Long reportId = IdUtils.getId();
        // 上报资产数据
        AsInventoryAssetReport asInventoryAssetReport = new AsInventoryAssetReport();
        BeanUtil.copyProperties(dto, asInventoryAssetReport);

        JSONObject filterJson = new JSONObject();
        FormVO formVO = settingService.inventoryAttrList(2);
        formVO.getFormFields().forEach(f -> {
            String fieldCode = f.getFieldCode();
            Object o = dto.getAssetData().get(fieldCode);
            filterJson.put(fieldCode, o);
            String fieldCodeText = fieldCode + "Text";
            if (dto.getAssetData().containsKey(fieldCodeText)) {
                filterJson.put(fieldCodeText, dto.getAssetData().get(fieldCodeText));
            }
        });

        asInventoryAssetReport
                .setId(reportId)
                .setAssetData(filterJson)
                .setReporter(currentUserId)
                .setCompanyId(companyId)
                .setAssetMark(InventoryConstant.ASSET_MARK_NOT_ON)
                .setInventoryTerminal(InventoryConstant.PC);
        inventoryAssetReportService.save(asInventoryAssetReport);

        // 设置上报资产关联数据
        AsInventorySurplus asInventorySurplus = new AsInventorySurplus();
        asInventorySurplus.setInventoryId(inventoryId).setReportId(reportId)
                .setInventoryTaskId(dto.getInventoryTaskId());
        inventorySurplusService.save(asInventorySurplus);
        return true;
    }

    /**
     * 编辑上报资产
     *
     * @param dto
     * @return 结果
     */
    @Override
    public Boolean editReportAsset(InventorySurplusDto dto) {
        Long reportId = dto.getId();
        // 获取盘盈资产
        AsInventoryAssetReport byId = inventoryAssetReportService.getById(dto.getId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        AsInventorySurplus inventorySurplus = inventorySurplusService.getOne(new QueryWrapper<AsInventorySurplus>().lambda()
                .eq(AsInventorySurplus::getReportId, reportId));
        if (null == inventorySurplus) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        Long inventoryId = inventorySurplus.getInventoryId();
        // 获取盘点单
        AsInventory inventory = inventoryService.getById(inventoryId);
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(inventory.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        FormVO formVO;
        if (Objects.equals(dto.getAssetMark(), 1)) {
            formVO = settingService.inventoryAttrList(1);
        } else {
            formVO = settingService.inventoryAttrList(2);
        }

        formVO.getFormFields().forEach(f -> {
            String fieldCode = f.getFieldCode();
            Object o = dto.getAssetData().get(fieldCode);
            byId.getAssetData().put(fieldCode, o);
            String fieldCodeText = fieldCode + "Text";
            if (dto.getAssetData().containsKey(fieldCodeText)) {
                byId.getAssetData().put(fieldCodeText, dto.getAssetData().get(fieldCodeText));
            }
        });

        List<AsAsset> assetList = assetService.listByIdsNoPerm(ListUtil.of(byId.getAssetId()));
        if (CollUtil.isNotEmpty(assetList)) {
            AsInventoryAssetLog assetLog = inventoryAssetLogService.getOne(new QueryWrapper<AsInventoryAssetLog>().lambda()
                    .eq(AsInventoryAssetLog::getInventoryId, inventoryId)
                    .eq(AsInventoryAssetLog::getAssetId, byId.getAssetId()), false);

            AsAsset asAsset = assetList.get(0);
            JSONObject assetOriginalData = covertDtoToJson(asAsset, formVO.getFormFields());
            // 全部固定属性
//            Map<String, String> attrCache = formVO.getFormFields().stream().collect(
//                    Collectors.toMap(FormFieldCO::getFieldCode, FormFieldCO::getFieldName));
            List<InventoryAssetLogData> logData = inventoryAssetService.buildAssetLog(assetOriginalData, byId.getAssetData(), formVO.getFormFields());
            if (assetLog == null) {
                assetLog = new AsInventoryAssetLog()
                        .setInventoryId(inventoryId)
                        .setAssetId(byId.getAssetId());
            }
            assetLog.setLogData(logData);
            inventoryAssetLogService.remove(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                    .eq(AsInventoryAssetLog::getInventoryId, inventoryId)
                    .eq(AsInventoryAssetLog::getAssetId, byId.getAssetId())
            );
            inventoryAssetLogService.save(assetLog);
        }
        return inventoryAssetReportService.updateById(byId);
    }

    /**
     * 删除上报资产
     *
     * @param id
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeReportAsset(Long id) {
        // 获取盘盈资产
        AsInventoryAssetReport byId = inventoryAssetReportService.getById(id);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        AsInventorySurplus inventorySurplus = inventorySurplusService.getOne(new QueryWrapper<AsInventorySurplus>().lambda()
                .eq(AsInventorySurplus::getReportId, id));
        if (null == inventorySurplus) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        Long inventoryId = inventorySurplus.getInventoryId();
        // 获取盘点单
        AsInventory inventory = inventoryService.getById(inventoryId);
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(inventory.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 删除上报资产数据
        if (!inventoryAssetReportService.removeById(id)) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }

        // 删除上报资产关联数据
        if (!inventorySurplusService.remove(new QueryWrapper<AsInventorySurplus>().lambda().eq(AsInventorySurplus::getReportId, id))) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }

        return true;
    }

    /**
     * 批量删除上报资产信息
     *
     * @param ids 资产IDs
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeReportAssets(List<Long> ids) {
        // 获取盘盈资产
        List<AsInventoryAssetReport> asInventoryAssetReports = inventoryAssetReportService.listByIds(ids);
        if (CollUtil.isEmpty(asInventoryAssetReports)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        List<AsInventorySurplus> inventorySurplusList = inventorySurplusService.list(new QueryWrapper<AsInventorySurplus>().lambda()
                .in(AsInventorySurplus::getReportId, ids));
        if (CollUtil.isEmpty(inventorySurplusList)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        Long inventoryId = inventorySurplusList.get(0).getInventoryId();
        // 获取盘点单
        AsInventory inventory = inventoryService.getById(inventoryId);
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(inventory.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 删除上报资产数据
        if (!inventoryAssetReportService.removeByIds(ids)) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }

        // 删除上报资产关联数据
        if (!inventorySurplusService.remove(new QueryWrapper<AsInventorySurplus>().lambda().in(AsInventorySurplus::getReportId, ids))) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncUploadTaskAsset(InventorySyncAssetDto dto) {
        if (CollUtil.isEmpty(dto.getSyncAssetData()) && CollUtil.isEmpty(dto.getSyncAddAssetData())) {
            return false;
        }

        FormVO formVO = formService.assetTpl();
        Long inventoryId = dto.getInventoryId();
        // ==============================================盘点单校验====================================================
        // 获取盘点单
        AsInventory byId = inventoryService.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (InventoryConstant.TERMINATED.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_IS_TERMINATED);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        if (CollUtil.isNotEmpty(dto.getSyncAssetData())) {
            syncUploadPdTaskAsset(dto.getInventoryId(),
                    dto.getTaskId(),
                    dto.getSyncAssetData(),
                    formVO);
        }
        if (CollUtil.isNotEmpty(dto.getSyncAddAssetData())) {
            syncUploadPdTaskAddAsset(dto.getInventoryId(),
                    dto.getTaskId(),
                    dto.getSyncAddAssetData(),
                    formVO);
        }
        return true;
    }

    /**
     * 同步上传盘点任务资产数据
     */
    private void syncUploadPdTaskAsset(Long inventoryId, Long taskId, List<InventorySyncAssetDto.AssetPdDto> syncAssetData, FormVO formVO) {
        Map<Long, InventorySyncAssetDto.AssetPdDto> tempMap = syncAssetData.stream()
                .collect(Collectors.toMap(InventorySyncAssetDto.AssetPdDto::getAssetId, k -> k));
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        // ==============================================盘点任务校验====================================================
        List<AsInventoryTask> inventoryTaskList = this.list(new QueryWrapper<AsInventoryTask>().lambda()
                .eq(AsInventoryTask::getInventoryId, inventoryId));
        if (CollUtil.isEmpty(inventoryTaskList)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }

        // 判断当前任务状态
        AsInventoryTask inventoryTask = inventoryTaskList.stream().filter(f -> f.getId().equals(taskId)).findFirst()
                .orElseThrow(() -> new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST));
        if (!SUBMIT_STATUS.contains(inventoryTask.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 资产原始数据 获取资产实时数据
        Map<Long, AsAsset> assetDataMap = assetService.listByIdsNoPerm(new ArrayList<>(tempMap.keySet()))
                .stream().collect(Collectors.toMap(AsAsset::getId, asset -> asset, (k1, k2) -> k1));

        // ==============================================盘点资产====================================================
        List<AsInventoryAsset> inventoryAssetList = inventoryAssetService.list(new QueryWrapper<AsInventoryAsset>().lambda()
                .eq(AsInventoryAsset::getInventoryId, inventoryId)
                .in(AsInventoryAsset::getAssetId, tempMap.keySet()));
        if (CollUtil.isEmpty(inventoryAssetList)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        // 需要更新的盘点任务已盘数据
        Map<Long, Integer> taskMap = new HashMap<>();
        // 同步资产数据
        List<AsInventoryAsset> asInventoryAssetList = new ArrayList<>();
        // 需要更新的日志数据
        List<AsInventoryAssetLog> inventoryAssetLogSaveList = new ArrayList<>();
        List<Long> needUpdateTaskStatusAssetIds = new ArrayList<>();
        inventoryAssetList.forEach(inventoryAsset -> {
            InventorySyncAssetDto.AssetPdDto assetMap = tempMap.get(inventoryAsset.getAssetId());
            if (ObjectUtil.isNull(assetMap)) {
                return;
            }

            // 资产实时数据
            AsAsset asAsset = assetDataMap.getOrDefault(inventoryAsset.getAssetId(), null);
            if (ObjectUtil.isNull(asAsset)) {
                return;
            }

            needUpdateTaskStatusAssetIds.add(inventoryAsset.getAssetId());
            // 盘点资产数据
            AsInventoryAsset asInventoryAsset = new AsInventoryAsset();
            asInventoryAsset.setId(inventoryAsset.getId())
                    .setAssetSnapshotData(assetMap.getAssetSnapshotData())
                    .setChecked(true)
                    .setActualInventoryUser(currentUserId)
                    .setInventoryMode(assetMap.getInventoryMode())
                    .setInventoryTerminal(assetMap.getInventoryTerminal())
                    .setPictureUrl(assetMap.getPictureUrl())
                    .setRemark(assetMap.getRemark());
            if (ObjectUtil.isNotEmpty(assetMap.getInventoryTime())) {
                asInventoryAsset.setInventoryTime(assetMap.getInventoryTime());
            }

            asInventoryAssetList.add(asInventoryAsset);

            // 获取盘点任务的已盘数量
            // 如果资产已盘，则不更新已盘数量
            if (!inventoryAsset.getChecked()) {
                Long inventoryTaskId = inventoryAsset.getInventoryTaskId();
                taskMap.put(inventoryTaskId, taskMap.getOrDefault(inventoryTaskId, 0) + 1);
            }

            // 转换快照
            JSONObject assetOriginalData = covertDtoToJson(asAsset, formVO.getFormFields());

            // 移动端不会同步标准品信息
            /*Long standardId = asAsset.getStandardId();
            if (standardId != null) {
                FormVO standardVO = formService.getByFormId(standardId, ListUtil.of(1L, LoginUserThreadLocal.getCompanyId()), false);
                // 全部产品属性
                attrCache.putAll(standardVO.getFormFields().stream().collect(
                        Collectors.toMap(FormFieldCO::getFieldCode,
                                FormFieldCO::getFieldName)));
            }*/

            // 全部固定属性
//            Map<String, String> attrCache = formVO.getFormFields().stream().collect(
//                    Collectors.toMap(FormFieldCO::getFieldCode, FormFieldCO::getFieldName));

            List<InventoryAssetLogData> logData = inventoryAssetService.buildAssetLog(assetOriginalData, assetMap.getAssetSnapshotData(), formVO.getFormFields());
            // 判断资产修改日志
            AsInventoryAssetLog inventoryAssetLog = new AsInventoryAssetLog();
            inventoryAssetLog.setInventoryId(inventoryId)
                    .setAssetId(inventoryAsset.getAssetId())
                    .setLogData(logData);
            inventoryAssetLogSaveList.add(inventoryAssetLog);
        });

        // 重新计算盘点任务资产盘点状态
        if (CollUtil.isNotEmpty(needUpdateTaskStatusAssetIds)) {
            List<AsInventoryTaskAsset> taskAssets = inventoryTaskAssetService.getInventoryTaskAssets(inventoryId, taskId, needUpdateTaskStatusAssetIds);

            taskAssets.forEach(asInventoryTaskAsset -> {
                asInventoryTaskAsset.setStatus(InventoryConstant.INVENTORY_TASK_ASSET_STATUS_CHECK);
                asInventoryTaskAsset.setAssetMark(InventoryConstant.INVENTORY_TASK_ASSET_MARK_NONE);
            });

            inventoryTaskAssetService.updateBatchById(taskAssets);
        }

        // 更新数据
        inventoryAssetService.updateBatchById(asInventoryAssetList);

        // 需要更新的盘点人已盘数据
        List<AsInventoryTask> inventoryTaskUpdateList = Lists.newArrayList();
        // 更新盘点人已盘数据
        for (AsInventoryTask task : inventoryTaskList) {
            Integer checkedNum = taskMap.getOrDefault(task.getId(), 0);
            // 盘点任务数据
            AsInventoryTask inventoryTaskUpdate = new AsInventoryTask();
            inventoryTaskUpdate.setId(task.getId())
                    .setCheckedNum(task.getCheckedNum() + checkedNum);
            inventoryTaskUpdateList.add(inventoryTaskUpdate);
        }
        // 更新任务数据
        updateBatchById(inventoryTaskUpdateList);

        // 删除日志数据
        if (CollUtil.isNotEmpty(inventoryAssetLogSaveList)) {
            inventoryAssetLogService.remove(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                    .eq(AsInventoryAssetLog::getInventoryId, inventoryId)
                    .in(AsInventoryAssetLog::getAssetId, inventoryAssetLogSaveList.stream().map(AsInventoryAssetLog::getAssetId).collect(toList())));
            // 新增日志数据
            inventoryAssetLogService.saveBatch(inventoryAssetLogSaveList);
        }
    }

    /**
     * 同步上传新增资产数据
     */
    private void syncUploadPdTaskAddAsset(Long inventoryId, Long taskId, List<InventorySyncAssetDto.AddAssetDto> syncAddAssetData, FormVO formVO) {
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 获取当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();

        // 判断当前任务状态
        AsInventoryTask inventoryTask = getById(taskId);
        if (ObjectUtil.isNull(inventoryTask)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(inventoryTask.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 排除PC的数据
        syncAddAssetData = syncAddAssetData.stream().filter(f -> !"1".equals(f.getAssetData().getString("inventoryTerminal"))).collect(toList());

        // 1、首先删除当前人上一次盘点任务中的盘盈数据 ///
        Integer taskType = inventoryTask.getTaskType();
        inventorySurplusService.removeWithReport(currentUserId, inventoryId, taskType);

        // 2、同一个盘点单内同一个资产盘盈 采用覆盖
        // 首先获取盘点单下所有的在册盘盈资产id
        List<Long> assetIdList = inventorySurplusService.getMarkSurplusAsset(inventoryId);
        // 需要先删除的盘盈资产
        List<Long> deleteAssetIds = Lists.newArrayList();

        // 收集需要后台判断的资产
        List<Long> needQueryAssetIds = syncAddAssetData.stream().filter(f -> ObjectUtil.equals(3, f.getAssetMark()))
                .map(f -> f.getAssetData().getLong("assetId")).collect(toList());
        Set<Long> assetMarkOnIds = new HashSet<>();
        if (CollUtil.isNotEmpty(needQueryAssetIds)) {
            assetMarkOnIds = assetService.listByIdsNoPerm(needQueryAssetIds).stream().map(AsAsset::getId).collect(Collectors.toSet());
        }

        // 收集在册的资产id数组
        Set<Long> finalAssetMarkOnIds = assetMarkOnIds;
        List<Long> markAssetIdList = syncAddAssetData.stream()
                .peek(f -> {
                    if (ObjectUtil.equals(3, f.getAssetMark())) {
                        if (finalAssetMarkOnIds.contains(f.getAssetData().getLong("assetId"))) {
                            f.setAssetMark(InventoryConstant.ASSET_MARK_ON);
                        } else {
                            f.setAssetMark(InventoryConstant.ASSET_MARK_NOT_ON);
                        }
                    }
                })
                .filter(f -> InventoryConstant.ASSET_MARK_ON.equals(f.getAssetMark()))
                .map(f -> f.getAssetData().getLong("assetId")).collect(Collectors.toList());

        Map<Long, AsAsset> assetDataMap = new HashMap<>();
        if (CollUtil.isNotEmpty(markAssetIdList)) {
            // 资产原始数据 获取资产实时数据
            assetDataMap = assetService.listByIdsNoPerm(markAssetIdList)
                    .stream().collect(Collectors.toMap(AsAsset::getId, asset -> asset, (k1, k2) -> k1));
        }

        // 3、保存用户此次提交的盘盈数据
        List<AsInventoryAssetReport> inventoryAssetReportList = new ArrayList<>();
        List<AsInventorySurplus> inventorySurplusList = new ArrayList<>();

        // 需要更新的日志数据
        List<AsInventoryAssetLog> inventoryAssetLogSaveList = new ArrayList<>();
        // 获取资产属性map
        List<Long> needUpdateAssetCheckedIds = new ArrayList<>();
        Map<Long, AsAsset> finalAssetDataMap = assetDataMap;
        syncAddAssetData.forEach(addAssetDto -> {
            // 盘盈数据
            JSONObject assetData = addAssetDto.getAssetData();
            // 是否在册
            Integer assetMark = addAssetDto.getAssetMark();
            Long assetId = null;
            // 如果是在册资产，需要覆盖盘点单内存在的盘盈资产
            if (InventoryConstant.ASSET_MARK_ON.equals(assetMark)) {
                assetId = assetData.getLong("assetId");

                // 在册资产需要生成修改日志
                if (CollUtil.isEmpty(finalAssetDataMap)) {
                    return;
                }

                // 获取修改数据日志
                AsAsset asAsset = finalAssetDataMap.get(assetId);
                if (asAsset == null) {
                    return;
                }

                if (ObjectUtil.isNotNull(assetId) && assetIdList.contains(assetId)) {
                    // 需要先删除的盘盈资产
                    deleteAssetIds.add(assetId);
                }

                JSONObject assetOriginalData = covertDtoToJson(asAsset, formVO.getFormFields());
                List<InventoryAssetLogData> logData = inventoryAssetService.buildAssetLog(assetOriginalData, assetData, formVO.getFormFields());

                // 判断资产修改日志
                AsInventoryAssetLog inventoryAssetLog = new AsInventoryAssetLog();
                inventoryAssetLog.setInventoryId(inventoryId)
                        .setAssetId(assetId)
                        .setLogData(logData);
                inventoryAssetLogSaveList.add(inventoryAssetLog);
            }

            // 首先生成id
            Long reportId = IdUtils.getId();
            // 盘盈资产数据
            AsInventoryAssetReport asInventoryAssetReport = new AsInventoryAssetReport();
            asInventoryAssetReport.setId(reportId).setReportType(InventoryConstant.INVENTORY_REPORT)
                    .setCompanyId(companyId)
                    .setAssetData(assetData)
                    .setReporter(currentUserId)
                    .setAssetMark(assetMark)
                    .setAssetId(assetId)
                    .setTaskType(taskType)
                    .setRemark(addAssetDto.getRemark())
                    .setInventoryTerminal(InventoryConstant.MOBILE_MANAGE);
            needUpdateAssetCheckedIds.add(assetId);
            inventoryAssetReportList.add(asInventoryAssetReport);

            // 盘盈资产关联数据
            AsInventorySurplus inventorySurplus = new AsInventorySurplus();
            inventorySurplus.setReportId(reportId)
                    .setInventoryId(inventoryId)
                    .setInventoryTaskId(taskId);
            inventorySurplusList.add(inventorySurplus);
        });

        // 重新计算盘点任务资产盘点状态
        if (CollUtil.isNotEmpty(needUpdateAssetCheckedIds)) {
            List<AsInventoryAsset> inventoryAssets = inventoryAssetService.getInventoryAssets(inventoryId, needUpdateAssetCheckedIds);
            List<AsInventoryTaskAsset> taskAssets = inventoryTaskAssetService.getInventoryTaskAssets(inventoryId, taskId, needUpdateAssetCheckedIds);
            taskAssets.forEach(asInventoryTaskAsset -> {
                if (markAssetIdList.contains(asInventoryTaskAsset.getAssetId())) {
                    asInventoryTaskAsset.setStatus(InventoryConstant.INVENTORY_TASK_ASSET_STATUS_CHECK_PLUS);
                    asInventoryTaskAsset.setAssetMark(InventoryConstant.INVENTORY_TASK_ASSET_MARK_IN);
                } else {
                    asInventoryTaskAsset.setStatus(InventoryConstant.INVENTORY_TASK_ASSET_STATUS_NOT_CHECK);
                    asInventoryTaskAsset.setAssetMark(InventoryConstant.INVENTORY_TASK_ASSET_MARK_NONE);
                }
            });

            Map<Long, AsInventoryAsset> assetMap = inventoryAssets.stream()
                    .collect(Collectors.toMap(AsInventoryAsset::getAssetId, o -> o, (k1, k2) -> k1));
            inventoryAssets.forEach(asInventoryAsset -> {
                AsInventoryAsset asset = assetMap.get(asInventoryAsset.getAssetId());
                asset.setChecked(true);
                asset.setInventoryTerminal(InventoryConstant.MOBILE_MANAGE);
                asset.setActualInventoryUser(currentUserId);
            });
            inventoryTaskAssetService.updateBatchById(taskAssets);
            inventoryAssetService.updateBatchById(inventoryAssets);
        }

        // 先删除已经存在的盘盈资产
        if (CollUtil.isNotEmpty(deleteAssetIds)) {
            inventorySurplusService.removeExistAsset(deleteAssetIds, inventoryId);
        }

        // 批量新增盘盈资产数据
        inventoryAssetReportService.saveBatch(inventoryAssetReportList);

        // 批量新增盘盈资产关联数据
        inventorySurplusService.saveBatch(inventorySurplusList);

        // 删除日志数据
        if (CollUtil.isNotEmpty(inventoryAssetLogSaveList)) {
            inventoryAssetLogService.remove(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                    .eq(AsInventoryAssetLog::getInventoryId, inventoryId)
                    .in(AsInventoryAssetLog::getAssetId, inventoryAssetLogSaveList.stream().map(AsInventoryAssetLog::getAssetId).collect(toList())));
            // 新增日志数据
            inventoryAssetLogService.saveBatch(inventoryAssetLogSaveList);
        }
    }

    /**
     * 盘点任务详情
     *
     * @param id 盘点任务id
     * @return 结果
     */
    @Override
    public InventoryTaskInfoDto getInfo(Long id) {
        // 获取盘点任务id
        AsInventoryTask inventoryTask = this.getById(id);
        if (null == inventoryTask) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        InventoryTaskInfoDto info = this.baseMapper.getInfo(id);
        StringJoiner inventoryUserText = new StringJoiner(", ");
        info.getInventoryUsers().forEach(userId -> {
            inventoryUserText.add(cacheResourceUtil.getUserNameAndCode(userId));
        });
        info.setInventoryUserText(inventoryUserText.toString());
        return info;
    }

    /**
     * 提交审核
     *
     * @param dto 盘盈资产数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncAddAndSubmit(InventorySyncAddAndSubmitDto dto) {
        // 获取盘点单id
        AsInventoryTask inventoryTask = this.getById(dto.getTaskId());
        if (null == inventoryTask) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }

        InventoryTaskApproveDto inventoryTaskApproveDto = new InventoryTaskApproveDto();
        inventoryTaskApproveDto.setTaskId(dto.getTaskId())
                .setOpinion(dto.getOpinion());
        if (!this.submit(inventoryTaskApproveDto)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        return true;
    }

    /**
     * 转换dto为json并翻译中文
     *
     * @param source 原始json数据
     * @return target 数据
     */
    private JSONObject covertDtoToJson(AsAsset source, List<FormFieldCO> formFields) {
        List<DefaultTranslateUtil.FieldTranslation> translations =
                formFields.stream()
                        .map(f -> new DefaultTranslateUtil.FieldTranslation()
                                .setFieldCode(f.getFieldCode())
                                .setFieldType(f.getFieldType())
                                .setTranslationCode(f.getTranslationCode()))
                        .collect(Collectors.toList());
        AssetDto assetDto = BeanUtil.copyProperties(source, AssetDto.class);
        // 写入资产基础数据翻译
        JSONObject translate = assetDto.translate();
        assetUtil.translateAssetJson(translate, translations);
        return translate;
    }

    @Override
    public Boolean checkInventory(Long userId) {
        List<AsInventory> all = inventoryService.list(
                Wrappers.lambdaQuery(AsInventory.class)
                        .notIn(AsInventory::getStatus, Arrays.asList(InventoryConstant.COMPLETED, InventoryConstant.TERMINATED))
                        .and(wrapper -> wrapper.eq(AsInventory::getCreateBy, userId).or().eq(AsInventory::getApprover, userId)));
        if (all.isEmpty()) {
            return false;
        }

        if (inventoryService.count(new LambdaQueryWrapper<AsInventory>()
                .and(query -> query.eq(AsInventory::getCreateBy, userId)
                        .or().eq(AsInventory::getApprover, userId))
                .in(AsInventory::getStatus, Arrays.asList(InventoryConstant.IN_PROGRESS, InventoryConstant.PEND_APPROVAL))) > 0) {
            return true;
        }

        if (inventoryAssetService.count(new LambdaQueryWrapper<AsInventoryAsset>()
                .eq(AsInventoryAsset::getActualInventoryUser, userId)) > 0) {
            return true;
        }

        if (inventoryAssetReportService.count(new LambdaQueryWrapper<AsInventoryAssetReport>()
                .and(query -> query.eq(AsInventoryAssetReport::getReporter, userId)
                        .or().eq(AsInventoryAssetReport::getHandleUser, userId))) > 0) {
            return true;
        }

        Set<Long> existIds = all.stream().map(AsInventory::getId).collect(Collectors.toSet());
        List<AsInventoryAsset> asInventoryAssets = inventoryAssetService.list(
                Wrappers.lambdaQuery(AsInventoryAsset.class)
                        .select(AsInventoryAsset::getId, AsInventoryAsset::getAssetId, AsInventoryAsset::getInventoryId, AsInventoryAsset::getInventoryUser)
                        .in(AsInventoryAsset::getInventoryId, existIds)
        );
        asInventoryAssets = asInventoryAssets.stream().filter(data -> data.getInventoryUser().contains(userId)).collect(Collectors.toList());
        if (!asInventoryAssets.isEmpty()) {
            return true;
        }

        return false;
    }

    @Override
    public List<AsInventoryTask> listForSendTaskTimeoutMessage(Long companyId, List<Integer> days) {
        return this.getBaseMapper().selectAllForSendTaskTimeoutMessage(companyId, days);
    }

    @Override
    public InventoryTaskView getInventoryTaskView(Long id) {
        // 获取盘点任务数据
        AsInventoryTask inventoryTask = this.getById(id);
        if (null == inventoryTask) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }

        // 获取盘点单数据
        AsInventory inventory = inventoryService.getById(inventoryTask.getInventoryId());
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        // 获取盘点单配置数据
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(inventoryTask.getInventoryId());
        if (null == inventoryConfig) {
            throw new BusinessException(InventoryResultCode.INVENTORY_CONFIG_NOT_EXIST);
        }

        InventoryTaskView inventoryView = new InventoryTaskView();
        BeanUtil.copyProperties(inventory, inventoryView);
        inventoryView.setTaskId(inventoryTask.getId());
        inventoryView.setTaskName(inventoryTask.getName());
        inventoryView.setAssetNum(inventoryTask.getAssetNum());
        inventoryView.setRules(inventoryConfig.getRules());

        StringJoiner inventoryUserText = new StringJoiner(", ");
        inventoryTask.getInventoryUsers().forEach(userId -> {
            inventoryUserText.add(cacheResourceUtil.getUserNameAndCode(userId));
        });
        inventoryView.setInventoryUsersText(inventoryUserText.toString());

        return inventoryView;
    }

    @Override
    public InventoryTaskUpdateUserInfoDto getInventoryUsers(Long taskId) {
        AsInventoryTask inventoryTask = this.getById(taskId);
        if (null == inventoryTask) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        InventoryTaskUpdateUserInfoDto infoDto = new InventoryTaskUpdateUserInfoDto();
        List<Map<String, ?>> originInventoryUsers = inventoryTask.getInventoryUsers().stream().map(userId ->
                ImmutableMap.of("label", cacheResourceUtil.getUserNameAndCode(userId), "value", userId)
        ).collect(toList());
        infoDto.setOriginInventoryUsers(originInventoryUsers);
        return infoDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInventoryUsers(InventoryTaskUpdateUserDto updateUserDto) {
        AsInventoryTask inventoryTask = this.getById(updateUserDto.getTaskId());
        if (null == inventoryTask) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        AsInventory inventory = inventoryService.getById(inventoryTask.getInventoryId());
        Set<Long> newInventoryUsers = updateUserDto.getUpdateUsers()
                .stream().map(InventoryTaskUpdateUserDto.UpdateUser::getNewUserId).collect(Collectors.toSet());
        // 修改盘点配置
        AsInventoryConfig config = inventoryConfigService.getById(inventory.getId());
        config.getDispatchDetail().forEach(d -> {
            if (inventoryTask.getId().equals(d.getTaskId())) {
                List<InventoryDispatchGroupUser> groupUsers =
                        newInventoryUsers.stream().map(userId -> {
                            InventoryDispatchGroupUser o = new InventoryDispatchGroupUser();
                            String name = cacheResourceUtil.getUserNameAndCode(userId);
                            o.setValue(userId).setLabel(name);
                            return o;
                        }).collect(toList());
                d.setInventoryUser(groupUsers);
            }
        });
        inventoryConfigService.updateById(config);
        // 修改盘点任务盘点人
        inventoryTask.setInventoryUsers(new ArrayList<>(newInventoryUsers));
        updateById(inventoryTask);
        if (InventoryTaskUpdateUserDto.TYPE_NOT_RETAIN == updateUserDto.getType()) {
            // 不保留
            // 1. 重置盘点状态
            List<AsInventoryAsset> assets = inventoryAssetService.listByTaskId(updateUserDto.getTaskId());
            assets.forEach(a -> {
                a.setInventoryUser(new ArrayList<>(newInventoryUsers));
                a.setChecked(false);
                a.setActualInventoryUser(null);
                a.setInventoryMode(null);
                a.setInventoryTerminal(null);
                a.setPictureUrl(null);
                a.setRemark(null);
            });
            inventoryAssetService.updateBatchById(assets);
            // 2. 清除盘盈数据
            inventorySurplusService.removeAssetByTaskId(updateUserDto.getTaskId());
            // 3. 清除盘点任务资产盘点状态
            inventoryTaskAssetService.update(Wrappers.lambdaUpdate(AsInventoryTaskAsset.class)
                    .set(AsInventoryTaskAsset::getStatus, InventoryConstant.INVENTORY_TASK_ASSET_STATUS_NOT_CHECK)
                    .set(AsInventoryTaskAsset::getAssetMark, InventoryConstant.INVENTORY_TASK_ASSET_MARK_NONE)
                    .eq(AsInventoryTaskAsset::getInventoryId, inventoryTask.getInventoryId())
                    .eq(AsInventoryTaskAsset::getInventoryTaskId, inventoryTask.getId()));
        } else {
            // 保留
            Map<Long, Long> oldNewUserMap = updateUserDto.getUpdateUsers()
                    .stream().collect(
                            Collectors.toMap(
                                    InventoryTaskUpdateUserDto.UpdateUser::getOldUserId,
                                    InventoryTaskUpdateUserDto.UpdateUser::getNewUserId,
                                    (k1, k2) -> k1));
            // 1. 修改盘点人
            List<AsInventoryAsset> assets = inventoryAssetService.listByTaskId(updateUserDto.getTaskId());
            assets.forEach(a -> a.setInventoryUser(new ArrayList<>(newInventoryUsers)));
            inventoryAssetService.updateBatchById(assets);
            // 2. 修改盘盈资产上报人
            List<AsInventoryAssetReport> plAssets =
                    inventorySurplusService.listAssetByTaskId(updateUserDto.getTaskId());
            plAssets.forEach(a -> {
                Long oldUserId = a.getReporter();
                a.setReporter(oldNewUserMap.get(oldUserId));
            });
            inventoryAssetReportService.updateBatchById(plAssets);
        }
        // 删除待办消息
        inventoryTodoService.deleteInventoryTaskTodoMsg(inventoryTask);
        // 发送待办消息
        inventoryTodoService.sendInventoryTaskTodoMsg(ListUtil.of(inventoryTask), inventory);
        return true;
    }

    @Override
    public Boolean addInventoryUsers(InventoryTaskAddUserDto addUserDto) {
        AsInventoryTask inventoryTask = this.getById(addUserDto.getTaskId());
        if (null == inventoryTask) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        Set<Long> addUserIds = new HashSet<>(addUserDto.getAddUsers());
        // 记录没有发送过消息的人Id
        Set<Long> sendMsgUsers = new HashSet<>();
        AsInventory inventory = inventoryService.getById(inventoryTask.getInventoryId());
        AsInventoryConfig config = inventoryConfigService.getById(inventory.getId());
        for (InventoryDispatchRecord dispatchRecord : config.getDispatchDetail()) {
            if (inventoryTask.getId().equals(dispatchRecord.getTaskId())) {
                List<Long> oldUsers = dispatchRecord.getInventoryUser().stream()
                        .map(InventoryDispatchGroupUser::getValue).collect(toList());
                for (Long addUserId : addUserIds) {
                    if (!oldUsers.contains(addUserId)) {
                        InventoryDispatchGroupUser o = new InventoryDispatchGroupUser();
                        String name = cacheResourceUtil.getUserNameAndCode(addUserId);
                        o.setValue(addUserId).setLabel(name);
                        dispatchRecord.getInventoryUser().add(o);
                        sendMsgUsers.add(addUserId);
                    }
                }
                break;
            }
        }
        ;
        // 更新盘点分配人员
        inventoryConfigService.updateById(config);

        // 更新盘点任务执行人
        inventoryTask.setInventoryUsers(new ArrayList<>(CollUtil.unionDistinct(inventoryTask.getInventoryUsers(), addUserDto.getAddUsers())));
        updateById(inventoryTask);

        // 更新盘点资产人
        List<AsInventoryAsset> assets = inventoryAssetService.listByTaskId(addUserDto.getTaskId());
        assets.forEach(a ->
                a.setInventoryUser(new ArrayList<>(CollUtil.unionDistinct(a.getInventoryUser(), addUserDto.getAddUsers())))
        );
        inventoryAssetService.updateBatchById(assets);

        // 发送待办消息
        inventoryTodoService.sendInventoryTaskTodoMsg(ListUtil.of(inventoryTask), inventory, sendMsgUsers);
        return true;
    }

}
