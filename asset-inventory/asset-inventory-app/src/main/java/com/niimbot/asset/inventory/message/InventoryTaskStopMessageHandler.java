package com.niimbot.asset.inventory.message;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.InstantCompanyMessageHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 盘点终止消息
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class InventoryTaskStopMessageHandler extends InstantCompanyMessageHandler {

    private final AsInventoryService inventoryService;

    private final AsInventoryTaskService inventoryTaskService;

    @Override
    public String code() {
        return MessageConstant.Code.PDRWZZ.getCode();
    }

    @Override
    protected boolean skip(MessageRuleCO rule) {
        return Objects.isNull(rule) || Objects.isNull(rule.getReceiverType()) || rule.getReceiverType().isEmpty();
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        // 审核人与执行人分别发送消息
        if (rule.getReceiverType().contains(MessageConstant.ReceiverType.TASK_REVIEWER)) {
            AsInventory byId = inventoryService.getById(params.getId());
            // 模板内容填充
            Map<String, String> map = new HashMap<>(2);
            map.put(MessageConstant.Template.TASK_NAME, byId.getName());
            Map<String, String> appMap = new HashMap<>(map);
            // 收信人
            Set<Long> userIds = Collections.singleton(byId.getApprover());
            Map<String, String> appExtMapParams = new HashMap<>(2);
            appExtMapParams.put("businessId", String.valueOf(byId.getId()));
            appExtMapParams.put("userType", String.valueOf(MessageConstant.ReceiverType.TASK_REVIEWER));
            // /#/inventory-task/inventory-detail?id=1449989271145549824 审核人 盘点单ID
            map.put(MessageConstant.Template.URL, pcDomain + String.format("/#/backlog/inventory-task-view?id=%s", byId.getId()));
            bodies.add(
                    Body.builder()
                            .mapParam(map)
                            .appMapParam(appMap)
                            .appExtMapParam(appExtMapParams)
                            .userIds(userIds)
                            .build()
            );
        }
        if (rule.getReceiverType().contains(MessageConstant.ReceiverType.TASK_PERFORMER)) {
            List<AsInventoryTask> inventoryTaskList = inventoryTaskService.list(Wrappers.<AsInventoryTask>lambdaQuery().eq(AsInventoryTask::getInventoryId, params.getId()));
            for (AsInventoryTask task : inventoryTaskList) {
                // 模板内容填充
                Map<String, String> map = new HashMap<>(2);
                map.put(MessageConstant.Template.TASK_NAME, task.getName());
                Map<String, String> appMap = new HashMap<>(map);
                Set<Long> userIds = new HashSet<>(task.getInventoryUsers());
                Map<String, String> appExtMapParams = new HashMap<>(2);
                appExtMapParams.put("businessId", String.valueOf(task.getId()));
                appExtMapParams.put("userType", String.valueOf(MessageConstant.ReceiverType.TASK_PERFORMER));
                // /#/inventory-child-task/my-inventory-detail?id=1449989271279767573 执行人 子任务ID
                map.put(MessageConstant.Template.URL, pcDomain + String.format("/#/backlog/inventory-child-task-view?id=%s", task.getId()));
                bodies.add(
                        Body.builder()
                                .mapParam(map)
                                .appMapParam(appMap)
                                .appExtMapParam(appExtMapParams)
                                .userIds(userIds)
                                .build()
                );
            }
        }
    }
}
