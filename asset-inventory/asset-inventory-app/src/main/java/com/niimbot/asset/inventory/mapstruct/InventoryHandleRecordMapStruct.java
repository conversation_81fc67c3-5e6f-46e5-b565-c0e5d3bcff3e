package com.niimbot.asset.inventory.mapstruct;

import com.niimbot.asset.inventory.model.AsInventoryHandleRecord;
import com.niimbot.inventory.InventoryHandleLogDto;

import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/6 9:57
 */
@Mapper(componentModel = "spring")
public interface InventoryHandleRecordMapStruct {

    InventoryHandleLogDto toDTO(AsInventoryHandleRecord record);

    List<InventoryHandleLogDto> toDTO(List<AsInventoryHandleRecord> record);


}
