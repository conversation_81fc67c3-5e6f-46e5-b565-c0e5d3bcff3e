package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.inventory.handle.InventoryDispatchRecordListTypeHandler;
import com.niimbot.asset.inventory.handle.InventoryRangeGroupListTypeHandler;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryConfig对象", description="盘点配置")
@TableName(value = "as_inventory_config", autoResultMap = true)
public class AsInventoryConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点单id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点范围类型(1：从资产库选择，2：从历史盘点单中选择)")
    private Integer rangeType;

    @ApiModelProperty(value = "盘点范围分组")
    @TableField(typeHandler = InventoryRangeGroupListTypeHandler.class)
    private List<InventoryRangeGroup> inventoryGroup;

    @ApiModelProperty(value = "是否需要拍照")
    private Boolean enableTakePicture = false;

    @ApiModelProperty(value = "是否允许手工盘点")
    private Boolean enableManual = false;

    @ApiModelProperty(value = "盘点是否可修改资产数据")
    private Boolean enableUpdateData = false;

    @ApiModelProperty(value = "允许没有资产查看权限人员参与盘点")
    private Boolean enableNoPermLook = false;

    @ApiModelProperty(value = "盘点数据过滤【维修中】的资产")
    private Boolean enableExcludeRepair = false;

    @ApiModelProperty(value = "盘点数据过滤【已处置】的资产")
    private Boolean enableExcludeDispose = false;

    @ApiModelProperty(value = "盘点分配方式(1：所属管理组织，2：所属管理员，3：区域，4：资产分类, 5: 所属使用组织, 6: 资产使用人)")
    private Integer dispatchMode;

    @ApiModelProperty(value = "盘点分配方式详情")
    @TableField(typeHandler = InventoryDispatchRecordListTypeHandler.class)
    private List<InventoryDispatchRecord> dispatchDetail;

    public String getRules() {
        List<String> rules = new ArrayList<>();
        if (this.getEnableManual()) {
            rules.add("允许手动盘点");
        }
        if (this.getEnableTakePicture()) {
            rules.add("盘点时要求对每条资产拍照");
        }
        if (this.getEnableUpdateData()) {
            rules.add("允许修改资产信息");
        }
        if (this.getEnableExcludeRepair()) {
            rules.add("盘点数据过滤【维修中】的资产");
        }
        if (this.getEnableExcludeDispose()) {
            rules.add("盘点数据过滤【已处置】的资产");
        }
        if (this.getEnableNoPermLook()) {
            rules.add("允许没有资产查看权限人员参与盘点");
        }
        return String.join("、", rules);
    }
}
