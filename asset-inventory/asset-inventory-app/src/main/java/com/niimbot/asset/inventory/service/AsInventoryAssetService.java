package com.niimbot.asset.inventory.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryAsset;
import com.niimbot.asset.inventory.model.InventoryAssetLogData;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.inventory.AssetAddDto;
import com.niimbot.inventory.AssetUpdateDto;
import com.niimbot.inventory.IgnoreUpdateDto;
import com.niimbot.inventory.InventoryAssetCountDto;
import com.niimbot.inventory.InventoryAssetCountQueryDto;
import com.niimbot.inventory.InventoryAssetEditDto;
import com.niimbot.inventory.InventoryAssetHandleDto;
import com.niimbot.inventory.InventoryAssetListDto;
import com.niimbot.inventory.InventoryAssetOtherUserDto;
import com.niimbot.inventory.InventoryAssetPcDto;
import com.niimbot.inventory.InventoryHandleLogDto;
import com.niimbot.inventory.InventoryHandleLogQry;
import com.niimbot.inventory.InventoryPictureDto;
import com.niimbot.inventory.InventoryRemarkDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.OtherUserInventoryAssetDto;
import com.niimbot.inventory.PlPhotoDto;
import com.niimbot.inventory.PlUpdateAssetByUserDto;

import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 盘点资产 服务类
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryAssetService extends IService<AsInventoryAsset> {

    /**
     * 盘点资产头部统计
     *
     * @param dto
     * @return 结果
     */
    InventoryAssetCountDto assetCount(InventoryAssetCountQueryDto dto);

    /**
     * 盘点资产盘亏处理
     *
     * @param pDto 盘亏DTO
     * @return 结果
     */
    InventoryAssetHandleDto assetHandle(InventoryAssetHandleDto pDto);

    void assetChangeRollback(InventoryAssetHandleDto pDto);

    /**
     * 查询资产详情快照
     *
     * @param id 盘点资产表Id
     * @return 资产详情集合
     */
    AsInventoryAsset getAssetDetail(Long id);

    /**
     * 盘点资产列表
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    List<InventoryAssetListDto> selectAssetListAll(InventorySurplusQueryDto dto);

    /**
     * app盘点资产
     *
     * @param dto 查询DTO
     * @return 结果集
     */
    IPage<InventoryAssetListDto> selectAppAssetList(InventorySurplusQueryDto dto);

    Map<String, String> inventoryAssetScanRef(Long inventoryId, Long taskId);

    /**
     * APP非当前盘点人已盘资产列表
     *
     * @param dto
     * @return
     */
    OtherUserInventoryAssetDto otherUser(@Validated InventoryAssetOtherUserDto dto);

    /**
     * 损益处理-盘亏资产
     *
     * @param dto 查询DTO
     * @return 结果集
     */
    IPage<InventoryAssetListDto> selectPlAssetList(InventorySurplusQueryDto dto);

    /**
     * 编辑盘点资产
     *
     * @param dto
     * @return 结果
     */
    Boolean editAsset(InventoryAssetEditDto dto);

    /**
     * 已修改资产列表分页单据
     *
     * @param dto dto
     * @return 结果
     */
    IPage<InventoryAssetListDto> modifiedPage(InventorySurplusQueryDto dto);

    IPage<InventoryAssetPcDto> takePhotoPage(InventorySurplusQueryDto dto);

    /**
     * 已修改资产列表单据-不分页
     *
     * @param dto dto
     * @return 结果
     */
    List<InventoryAssetListDto> modifiedPageList(InventorySurplusQueryDto dto);

    /**
     * 忽略修改资产
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    Boolean ignoreUpdate(IgnoreUpdateDto dto);

    /**
     * 修改资产信息
     *
     * @param dto 资产数据
     * @return 结果
     */
    Boolean plUpdateAsset(AssetUpdateDto dto);

    /**
     * 使用盘点人修改的信息
     *
     * @param dto 资产数据
     * @return 结果
     */
    Boolean plUpdateAssetByUser(List<PlUpdateAssetByUserDto> dto);

    /**
     * 盘盈资产新增入库
     *
     * @param dto 资产数据
     * @return 资产ID
     */
    Long plAddAsset(AssetAddDto dto);

    /**
     * PC盘点资产盘亏处理-校验资产是否能盘亏处理
     *
     * @param ids 盘点资产ids
     * @return 结果
     */
    Boolean checkAssetHandle(List<Long> ids, Integer orderType);

    /**
     * 判断资产id是否在盘点单内
     *
     * @param assetId     盘点资产id
     * @param inventoryId 盘点单id
     * @return 结果
     */
    Boolean checkAssetId(Long assetId, Long inventoryId);

    /**
     * 通过盘点单id和盘点状态查询资产id
     *
     * @param inventoryIds
     * @param checked
     * @return
     */
    List<Long> listAssetIds(List<Long> inventoryIds, Integer checked);

    List<AsInventoryAsset> listByTaskId(Long taskId);

    List<AsInventoryAsset> getInventoryAssets(Long inventoryId, Collection<Long> assetIds);

    /**
     * 未处理数量
     *
     * @param inventoryId
     * @return
     */
    int getUnhandleNum(Long inventoryId);

    /**
     * 未处理数量
     *
     * @param inventoryId
     * @return
     */
    int getUnhandleNum2(Long inventoryId);

    int getUnhandlePhotoNum(Long inventoryId);

    Boolean addPhoto(PlPhotoDto dto);

    Boolean replacePhoto(PlPhotoDto dto);

    InventoryHandleLogDto handleLog(InventoryHandleLogQry qry);

    List<InventoryHandleLogDto> handleLogList(Long inventoryId);

    Boolean updateRemark(InventoryRemarkDto dto);

    Boolean updatePicture(InventoryPictureDto dto);

    List<InventoryAssetLogData> buildAssetLog(JSONObject before, JSONObject after, List<FormFieldCO> formFields);
}
