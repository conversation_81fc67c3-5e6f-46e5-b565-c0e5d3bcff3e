package com.niimbot.asset.inventory.controller;

import com.niimbot.asset.inventory.service.AsInventorySettingService;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventorySettingAddListDto;
import com.niimbot.inventory.InventorySettingDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:24
 */
@RestController
@RequestMapping("server/inventory/setting")
@RequiredArgsConstructor
public class AsInventorySettingServiceController {

    private final AsInventorySettingService settingService;

    @GetMapping("/list/{type}")
    public List<InventorySettingDto> list(@PathVariable("type") Integer type) {
        return settingService.settingList(type);
    }

    @PostMapping
    public Boolean save(@RequestBody InventorySettingAddListDto addListDto) {
        return settingService.save(addListDto);
    }

    @GetMapping("/attrList/{type}")
    public FormVO inventoryAttrList(@PathVariable("type") Integer type) {
        return settingService.inventoryAttrList(type);
    }

}
