package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryAssetReport;

import java.util.List;

/**
 * <p>
 * 资产上报 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryAssetReportService extends IService<AsInventoryAssetReport> {

    List<Long> unHandleIds(List<Long> ids);
}
