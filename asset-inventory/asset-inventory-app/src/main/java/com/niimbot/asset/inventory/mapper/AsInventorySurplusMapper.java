package com.niimbot.asset.inventory.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.inventory.model.AsInventoryAssetReport;
import com.niimbot.asset.inventory.model.AsInventorySurplus;
import com.niimbot.inventory.InventoryAssetCountQueryDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘点-资产盘盈表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventorySurplusMapper extends BaseMapper<AsInventorySurplus> {

    /**
     * 删除当前用户的上报盘盈数据
     *
     * @param userId 当前用户id
     * @return 结果
     */
    boolean removeWithReport(@Param("userId") Long userId, @Param("inventoryId") Long inventoryId,
                             @Param("taskType") Integer taskType);

    /**
     * 盘点损益处理盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    IPage<InventorySurplusDto> getPlSurplus(Page<Object> buildIPage, @Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);

    /**
     * 盘盈资产分页列表
     *
     * @param dto
     * @return
     */
    List<InventorySurplusDto> selectSurplusList(@Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);

    /**
     * 获取盘点单下所有的在册盘盈资产id
     *
     * @param inventoryId
     * @return 结果
     */
    List<Long> getMarkSurplusAsset(@Param("inventoryId") Long inventoryId);

    /**
     * 删除已经存在的盘盈资产
     *
     * @param deleteAssetIds
     * @return 结果
     */
    boolean removeExistAsset(@Param("deleteAssetIds") List<Long> deleteAssetIds,
                             @Param("inventoryId") Long inventoryId);

    /**
     * 通过任务id删除盘盈资产
     *
     * @param taskId
     * @return 结果
     */
    boolean removeAssetByTaskId(@Param("taskId") Long taskId);

    /**
     * 通过任务id查询盘盈资产
     * @param taskId
     * @return
     */
    List<AsInventoryAssetReport> listAssetByTaskId(Long taskId);

    /**
     * 获取盘盈数量
     *
     * @param dto
     * @return 结果
     */
    int countAddAsset(@Param("ew") InventoryAssetCountQueryDto dto);

    int getUnhandleNum(@Param("inventoryId") Long inventoryId);
}
