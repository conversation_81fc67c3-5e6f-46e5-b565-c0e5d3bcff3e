package com.niimbot.asset.inventory.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.service.AsInventoryDispatchService;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.inventory.InventoryAssetRangeWithModeDto;
import com.niimbot.inventory.InventoryDispatchDetailDto;
import com.niimbot.inventory.InventoryDispatchGroupAssetNewDto;
import com.niimbot.inventory.InventoryDispatchRecordDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDto;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2024/5/8 17:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsInventoryDispatchServiceImpl implements AsInventoryDispatchService {

    private final AsInventoryService asInventoryService;
    private final AsAccountEmployeeService accountEmployeeService;
    private final AsCusEmployeeService cusEmployeeService;
    private final OrgService orgService;
    private final AsUserOrgService userOrgService;
    private final AreaService areaService;
    private final DataAuthorityService dataAuthorityService;
    private final CategoryService categoryService;
    private final CompanySettingService companySettingService;

    private static final String KEY_DELETE = "已删除";
    private static final String KEY_NULL = "为空";

    @Resource
    private DictConvertUtil dictConvertUtil;
    @Resource(name = "assetTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Data
    public static class DispatchCache {

        // 所有账号
        private List<Long> hasAccountEmpIds = new ArrayList<>();

        // 所有账号的数据权限
        private Map<Long, Map<String, AsDataAuthority>> userDataAuthorityMap = new HashMap<>();

        // 所有资产管理员
        private List<Long> assetAdminIds = new ArrayList<>();

        // 所有账号的所属公司
        private Map<Long, Set<Long>> empCompanyMap = new HashMap<>();

        // 所有账号的所属部门
        private Map<Long, Set<Long>> empOrgMap = new HashMap<>();

        // 所有组织数据
        private List<AsOrg> orgAllList = new ArrayList<>();

        // 所有区域Map数据
        private List<AsArea> areaAllList = new ArrayList<>();
        private Map<Long, List<AsArea>> orgAreaAllMap = new HashMap<>();

        // 延迟加载
        private Map<Long, String> empAllMap = new HashMap<>();
        private Map<Long, String> cateAllMap = new HashMap<>();

    }

    private DispatchCache initLoadCache(String localCacheKey, Long companyId, Boolean enableNoPermLook) {
        // 权限缓存
        DispatchCache dispatchCache = InventoryQueryAssetServiceImpl.GLOBAL_CACHE.get(localCacheKey);
        if (dispatchCache == null) {
            // 权限缓存
            dispatchCache = new DispatchCache();

            // 所有资产管理员
            List<Long> assetAdminIds = cusEmployeeService.getAssetAdminIds(companyId);
            if (assetAdminIds.isEmpty()) {
                // 防止异常
                assetAdminIds.add(-1L);
            }
            dispatchCache.setAssetAdminIds(assetAdminIds);

            // 所有账号
            List<Long> hasAccountEmpIds = accountEmployeeService.hasAccountEmpId(companyId);
            dispatchCache.setHasAccountEmpIds(hasAccountEmpIds);

            // 所有账号的数据权限
            Future<Map<Long, Map<String, AsDataAuthority>>> userDataAuthorityMapFuture = executor.submit(() -> {
                List<AsDataAuthority> dataAuthorityList = dataAuthorityService.listCompanyPerms(companyId, Wrappers.lambdaQuery(AsDataAuthority.class)
                        .in(AsDataAuthority::getAuthorityDataCode,
                                ListUtil.of(AssetConstant.DATA_PERMISSION_ORG, AssetConstant.DATA_PERMISSION_ASSET, AssetConstant.DATA_PERMISSION_AREA))
                        // 如果需要不限制权限，则仅查看资产管理员的权限
                        .in(!BooleanUtil.isFalse(enableNoPermLook), AsDataAuthority::getUserId, assetAdminIds));
                // 生成权限缓存 示例 k1 = 1765613564717752320, k2 = asset_area
                Map<Long, Map<String, AsDataAuthority>> userDataAuthorityMap = new HashMap<>();
                for (AsDataAuthority dataAuthority : dataAuthorityList) {
                    if (AssetConstant.DATA_PERMISSION_ASSET.equals(dataAuthority.getAuthorityDataCode()) ||
                            AssetConstant.DATA_PERMISSION_ORG.equals(dataAuthority.getAuthorityDataCode()) ||
                            AssetConstant.DATA_PERMISSION_AREA.equals(dataAuthority.getAuthorityDataCode())) {
                        Map<String, AsDataAuthority> dataAuthorityMap = userDataAuthorityMap.getOrDefault(dataAuthority.getUserId(), new HashMap<>());
                        dataAuthorityMap.put(dataAuthority.getAuthorityDataCode() + "_" + dataAuthority.getAuthorityCode(), dataAuthority);
                        userDataAuthorityMap.put(dataAuthority.getUserId(), dataAuthorityMap);
                    }
                }
                return userDataAuthorityMap;
            });
            // 所有账号的所属公司
            Future<Map<Long, Set<Long>>> empCompanyMapFuture = executor.submit(() ->
                    userOrgService.orgIdsByUserIds(companyId, BooleanUtil.isFalse(enableNoPermLook) ? ListUtil.empty() : assetAdminIds, 1).stream()
                            .collect(groupingBy(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toSet()))));

            // 所有账号的所属部门
            Future<Map<Long, Set<Long>>> empOrgMapFuture = executor.submit(() ->
                    userOrgService.orgIdsByUserIds(companyId, BooleanUtil.isFalse(enableNoPermLook) ? ListUtil.empty() : assetAdminIds, 2).stream()
                            .collect(groupingBy(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toSet()))));

            // 所有组织数据
            Future<List<AsOrg>> orgCacheFuture = executor.submit(() -> orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                    .select(AsOrg::getId, AsOrg::getOrgName, AsOrg::getOrgCode, AsOrg::getOrgType, AsOrg::getPaths)
                    .eq(AsOrg::getCompanyId, companyId)));

            // 所有区域数据
            Future<List<AsArea>> areaCacheListFuture = executor.submit(() -> areaService.list(Wrappers.lambdaQuery(AsArea.class)
                    .select(AsArea::getId, AsArea::getAreaName, AsArea::getOrgId)
                    .eq(AsArea::getCompanyId, companyId)));

            try {
                Map<Long, Map<String, AsDataAuthority>> userDataAuthorityMap = userDataAuthorityMapFuture.get(50, TimeUnit.SECONDS);
                dispatchCache.setUserDataAuthorityMap(userDataAuthorityMap);

                Map<Long, Set<Long>> empCompanyMap = empCompanyMapFuture.get(50, TimeUnit.SECONDS);
                dispatchCache.setEmpCompanyMap(empCompanyMap);

                Map<Long, Set<Long>> empOrgMap = empOrgMapFuture.get(50, TimeUnit.SECONDS);
                dispatchCache.setEmpOrgMap(empOrgMap);

                List<AsOrg> orgAllList = orgCacheFuture.get(50, TimeUnit.SECONDS);
                dispatchCache.setOrgAllList(orgAllList);

                List<AsArea> areaAllList = areaCacheListFuture.get(50, TimeUnit.SECONDS);
                dispatchCache.setAreaAllList(areaAllList);
                Map<Long, List<AsArea>> orgAreaAllMap = areaAllList.stream().collect(groupingBy(AsArea::getOrgId));
                dispatchCache.setOrgAreaAllMap(orgAreaAllMap);
            } catch (TimeoutException e) {
                log.error("盘点任务分配缓存加载超时，" + e.getMessage(), e);
                throw new BusinessException(InventoryResultCode.INVENTORY_LOSS_FAIL, "盘点任务分配缓存加载超时");
            } catch (Exception e) {
                log.error("盘点任务分配异常，" + e.getMessage(), e);
                throw new BusinessException(InventoryResultCode.INVENTORY_LOSS_FAIL, "盘点任务分配异常");
            }
            InventoryQueryAssetServiceImpl.GLOBAL_CACHE.put(localCacheKey, dispatchCache);
        }
        return dispatchCache;
    }

    @Override
    public InventoryDispatchDetailDto getDispatchDetail(InventoryAssetRangeWithModeDto dto) {
        // 租户
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 查询待盘点资产数据
        List<AssetDto> waitInventoryAssetList = asInventoryService.findInventoryDispatchAssets(
                dto.getRangeType(),
                dto.getEnableExcludeRepair(),
                dto.getEnableExcludeDispose(),
                dto.getAssetQueryConditions(),
                dto.getInventoryHistoryQueries());
        if (CollUtil.isEmpty(waitInventoryAssetList)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_IS_EMPTY);
        }

        String localCacheKey = dto.getQueryId() + (BooleanUtil.isFalse(dto.getEnableNoPermLook()) ? "_perm" : "");

        // 初始化缓存
        DispatchCache dispatchCache = initLoadCache(localCacheKey, companyId, dto.getEnableNoPermLook());
        List<InventoryDispatchRecordDto> dispatchDetailList = new ArrayList<>();
        // 排序标记
        List<InventoryDispatchRecordDto> firstList = new ArrayList<>();
        List<InventoryDispatchRecordDto> secondList = new ArrayList<>();
        List<InventoryDispatchRecordDto> lastList = new ArrayList<>();

        boolean weixinEdition = Edition.isWeixin();

        // 盘点方式
        DispatchEnum dispatchEnum = DispatchEnum.getByType(dto.getDispatchMode());

        // group转List
        List<InventoryDispatchGroupAssetNewDto> dispatchList = groupAsset(localCacheKey, dispatchEnum, waitInventoryAssetList, dispatchCache);

        // 循环计算人员
        AtomicInteger count = new AtomicInteger(0);
        dispatchList.forEach(item -> {
            String taskName = item.getGroupName() + "（" + dispatchEnum.getName() + "）";
            // 企业微信组织名称为乱码，所以使用流水号
            if (weixinEdition && !StrUtil.containsAny(item.getGroupName(), KEY_DELETE, KEY_NULL)) {
                taskName = "任务" + count.incrementAndGet() + "（" + dispatchEnum.getName() + "）";
            }

            InventoryDispatchRecordDto inventoryDispatchRecordDto = new InventoryDispatchRecordDto();
            inventoryDispatchRecordDto.setTaskId(IdUtils.getId())
                    .setIds(ListUtil.of(item.getGroupKey()))
                    .setNames(ListUtil.of(item.getGroupName()))
                    .setTaskName(taskName)
                    .setAssetNum(item.getAssetList().size());

            // 默认盘点人, 从有权限的人员里面过滤出当前组织下的资产管理员
            Set<Long> defaultAssetAdminIds;
            if (DispatchEnum.DISPATCH_USE_PERSON.equals(dispatchEnum)
                    || DispatchEnum.DISPATCH_MANAGER_OWNER.equals(dispatchEnum)) {
                defaultAssetAdminIds = new HashSet<>();
                // 直接写入有账号的人
                Long groupKey = item.getGroupKey();
                if (dispatchCache.getHasAccountEmpIds().contains(groupKey)) {
                    defaultAssetAdminIds.add(groupKey);
                }
                inventoryDispatchRecordDto.setDefaultInventoryUser(defaultAssetAdminIds.stream().map(userId ->
                        new InventoryDispatchDetailDto.InventoryDispatchGroupDto().setValue(userId)
                ).collect(toList()));
                // 允许没有资产查看权限人员参与盘点 = false, 说明需要过滤人
                if (BooleanUtil.isFalse(dto.getEnableNoPermLook())) {
                    // 权限计算比对
                    List<Long> accessEmpList = filterAssetAccountEmpList(item.getAssetList(), dispatchCache);
                    inventoryDispatchRecordDto.setInventoryUser(accessEmpList.stream().map(userId ->
                            new InventoryDispatchDetailDto.InventoryDispatchGroupDto().setValue(userId)
                    ).collect(toList()));
                } else {
                    inventoryDispatchRecordDto.setInventoryUser(inventoryDispatchRecordDto.getDefaultInventoryUser());
                }
            } else {
                // 权限计算比对
                List<Long> accessEmpList = filterAssetAccountEmpList(item.getAssetList(), dispatchCache);
                defaultAssetAdminIds = new HashSet<>(CollUtil.intersection(dispatchCache.getAssetAdminIds(), accessEmpList));
                inventoryDispatchRecordDto.setDefaultInventoryUser(defaultAssetAdminIds.stream().map(userId ->
                        new InventoryDispatchDetailDto.InventoryDispatchGroupDto().setValue(userId)
                ).collect(toList()));
                // 允许没有资产查看权限人员参与盘点 = false, 说明需要过滤人
                if (BooleanUtil.isFalse(dto.getEnableNoPermLook())) {
                    inventoryDispatchRecordDto.setInventoryUser(accessEmpList.stream().map(userId ->
                            new InventoryDispatchDetailDto.InventoryDispatchGroupDto().setValue(userId)
                    ).collect(toList()));
                } else {
                    inventoryDispatchRecordDto.setInventoryUser(inventoryDispatchRecordDto.getDefaultInventoryUser());
                }
            }


            // 为空和已删除的放在最后
            if (StrUtil.contains(item.getGroupName(), KEY_DELETE)) {
                lastList.add(inventoryDispatchRecordDto);
            } else if (StrUtil.contains(item.getGroupName(), KEY_NULL)) {
                secondList.add(inventoryDispatchRecordDto);
            } else {
                firstList.add(inventoryDispatchRecordDto);
            }
        });
        dispatchDetailList.addAll(firstList);
        dispatchDetailList.addAll(secondList);
        dispatchDetailList.addAll(lastList);
        // 排序
        dictConvertUtil.convertToDictionary(dispatchDetailList);

        InventoryDispatchDetailDto inventoryDispatchDetailDto = new InventoryDispatchDetailDto();
        inventoryDispatchDetailDto.setDispatchDetail(dispatchDetailList)
                .setAssetTotal(waitInventoryAssetList.size());
        return inventoryDispatchDetailDto;
    }

    private List<InventoryDispatchGroupAssetNewDto> groupAsset(String localCacheKey,
                                                               DispatchEnum dispatchEnum,
                                                               List<AssetDto> waitInventoryAssetList,
                                                               DispatchCache dispatchCache) {
        List<InventoryDispatchGroupAssetNewDto> result = new ArrayList<>();
        // 数据聚合, key为业务iId
        Map<Long, List<AssetDto>> dispatchAssetMap = waitInventoryAssetList.stream().collect(groupingBy(f ->
                Convert.toLong(f.getAssetData().get(dispatchEnum.getCode()), Long.valueOf("0"))));

        Map<Long, String> idNameMap = new HashMap<>();
        switch (dispatchEnum) {
            case DISPATCH_ORG_OWNER:
            case DISPATCH_USE_ORG:
                List<AsOrg> orgAllList = dispatchCache.getOrgAllList();
                idNameMap = orgAllList.stream()
                        .collect(Collectors.toMap(AsOrg::getId, o -> o.getOrgName() + (StrUtil.isNotBlank(o.getOrgCode()) ? "（" + o.getOrgCode() + "）" : "")));
                break;
            case DISPATCH_MANAGER_OWNER:
            case DISPATCH_USE_PERSON:
                if (CollUtil.isEmpty(dispatchCache.getEmpAllMap())) {
                    idNameMap = cusEmployeeService.list(Wrappers.lambdaQuery(AsCusEmployee.class)
                                    .select(AsCusEmployee::getId, AsCusEmployee::getEmpName, AsCusEmployee::getEmpNo))
                            .stream().collect(Collectors.toMap(AsCusEmployee::getId, o -> o.getEmpName() + (StrUtil.isNotBlank(o.getEmpNo()) ? "（" + o.getEmpNo() + "）" : "")));
                    dispatchCache.setEmpAllMap(idNameMap);
                    InventoryQueryAssetServiceImpl.GLOBAL_CACHE.put(localCacheKey, dispatchCache);
                } else {
                    idNameMap = dispatchCache.getEmpAllMap();
                }
                break;
            case DISPATCH_STORAGE_AREA:
                List<AsArea> areaAllList = dispatchCache.getAreaAllList();
                idNameMap = areaAllList.stream().collect(Collectors.toMap(AsArea::getId, AsArea::getAreaName));
                break;
            case DISPATCH_ASSET_CATEGORY:
                if (CollUtil.isEmpty(dispatchCache.getCateAllMap())) {
                    idNameMap = categoryService.list(Wrappers.lambdaQuery(AsCategory.class)
                                    .select(AsCategory::getId, AsCategory::getCategoryName))
                            .stream()
                            .collect(Collectors.toConcurrentMap(AsCategory::getId, AsCategory::getCategoryName));
                    dispatchCache.setCateAllMap(idNameMap);
                    InventoryQueryAssetServiceImpl.GLOBAL_CACHE.put(localCacheKey, dispatchCache);
                } else {
                    idNameMap = dispatchCache.getCateAllMap();
                }
                break;
        }

        Map<Long, String> finalIdNameMap = idNameMap;
        dispatchAssetMap.forEach((id, list) -> {
            InventoryDispatchGroupAssetNewDto groupAssetDto = new InventoryDispatchGroupAssetNewDto()
                    .setGroupKey(id)
                    .setAssetList(list);
            // 写入名称
            if (finalIdNameMap.containsKey(id)) {
                groupAssetDto.setGroupName(finalIdNameMap.get(id));
            } else if (id > 0) {
                groupAssetDto.setGroupName(dispatchEnum.getName() + KEY_DELETE);
            } else {
                groupAssetDto.setGroupName(dispatchEnum.getName() + KEY_NULL);
            }
            result.add(groupAssetDto);
        });
        // 按照资产数值大小倒序
        ListUtil.sort(result, Comparator.comparing(f -> f.getAssetList().size(), Comparator.reverseOrder()));
        return result;
    }

    private List<Long> filterAssetAccountEmpList(List<AssetDto> assetList,
                                                 DispatchCache dispatchCache) {
        // 查询是否开放闲置资产
        AsCompanySetting setting = companySettingService.getById(LoginUserThreadLocal.getCompanyId());
        boolean enableIdleAsset = setting != null && setting.getExpandSwitch() != null && BooleanUtil.isTrue(setting.getExpandSwitch().getEnableIdleAsset());
        Set<Long> cateSet = new HashSet<>();
        Set<Long> areaSet = new HashSet<>();
        Set<Long> orgOwnerSet = new HashSet<>();
        Set<Long> useOrgSet = new HashSet<>();
        Set<Long> createBySet = new HashSet<>();
        Set<Long> usePersonSet = new HashSet<>();
        Set<Long> managerOwnerSet = new HashSet<>();
        Set<Integer> idleAsset = new HashSet<>();
        for (AssetDto assetDto : assetList) {
            JSONObject assetData = assetDto.getAssetData();
            // 分类
            Long assetCategory = Convert.toLong(assetData.get(DispatchEnum.DISPATCH_ASSET_CATEGORY.getCode()), 0L);
            if (assetCategory > 0) {
                cateSet.add(assetCategory);
            }
            // 区域
            Long storageArea = Convert.toLong(assetData.get(DispatchEnum.DISPATCH_STORAGE_AREA.getCode()), 0L);
            if (storageArea > 0) {
                areaSet.add(storageArea);
            }
            // 管理组织
            Long orgOwner = Convert.toLong(assetData.get(DispatchEnum.DISPATCH_ORG_OWNER.getCode()), 0L);
            if (orgOwner > 0) {
                orgOwnerSet.add(orgOwner);
            }
            // 使用组织
            Long useOrg = Convert.toLong(assetData.get(DispatchEnum.DISPATCH_USE_ORG.getCode()), 0L);
            if (useOrg > 0) {
                useOrgSet.add(useOrg);
            }
            // 资产使用人
            Long usePerson = Convert.toLong(assetData.get(DispatchEnum.DISPATCH_USE_PERSON.getCode()), 0L);
            if (usePerson > 0) {
                usePersonSet.add(usePerson);
            }
            // 资产管理员
            Long managerOwner = Convert.toLong(assetData.get(DispatchEnum.DISPATCH_MANAGER_OWNER.getCode()), 0L);
            if (managerOwner > 0) {
                managerOwnerSet.add(managerOwner);
            }
            // 创建人
            if (assetDto.getCreateBy() != null && assetDto.getCreateBy() > 0) {
                createBySet.add(assetDto.getCreateBy());
            }
            // 闲置资产
            if (assetDto.getStatus() != null) {
                idleAsset.add(assetDto.getStatus());
            }
        }
        // 为空需要写入-1L，防止有权限看见
        if (cateSet.isEmpty()) {
            cateSet.add(-1L);
        }
        if (areaSet.isEmpty()) {
            areaSet.add(-1L);
        }
        if (orgOwnerSet.isEmpty()) {
            orgOwnerSet.add(-1L);
        }
        if (useOrgSet.isEmpty()) {
            useOrgSet.add(-1L);
        }
        if (createBySet.isEmpty()) {
            createBySet.add(-1L);
        }
        if (usePersonSet.isEmpty()) {
            usePersonSet.add(-1L);
        }
        if (managerOwnerSet.isEmpty()) {
            managerOwnerSet.add(-1L);
        }

        // 循环每个人的权限
        List<Long> hasAccessEmpList = new ArrayList<>();
        for (Map.Entry<Long, Map<String, AsDataAuthority>> entry : dispatchCache.getUserDataAuthorityMap().entrySet()) {
            // 当前筛选的人
            Long userId = entry.getKey();
            // 权限数据
            Map<String, AsDataAuthority> permMap = entry.getValue();

            // 如果全是闲置资产，并且开放闲置资产查询，直接有权限
            if (enableIdleAsset && idleAsset.size() == 1) {
                hasAccessEmpList.add(userId);
                continue;
            }

            // ===== 有且仅有所有资产的创建人, 使用人，管理人 都是同一个人的时候，并且是该人，就说明有可见权限
            if ((usePersonSet.size() == 1 && usePersonSet.contains(userId))
                    || (managerOwnerSet.size() == 1 && managerOwnerSet.contains(userId))
                    || (createBySet.size() == 1 && createBySet.contains(userId))) {
                hasAccessEmpList.add(userId);
                continue;
            }
            // 是否有需要查看其他的权限，任意一个不满足直接continue快速失败跳过
            AsDataAuthority onlyOneselfPerms = permMap.get(AssetConstant.DATA_PERMISSION_ASSET + "_" + AssetConstant.AUTHORITY_ONLY_ONESELF);
            if (onlyOneselfPerms != null) {
                // 仅查看自己的
                if (AssetConstant.AUTHORITY_TYPE_ONLY_ONESELF == onlyOneselfPerms.getAuthorityType()) {
                    continue;
                }
            } else {
                continue;
            }

            // ===== 是否有分类权限 =====
            AsDataAuthority catePerms = permMap.get(AssetConstant.DATA_PERMISSION_ASSET + "_" + AssetConstant.AUTHORITY_CATE);
            if (catePerms != null) {
                if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == catePerms.getAuthorityType()) {
                    // 如果自定义的全包含资产集合的所有分类，则有权限
                    if (!CollUtil.containsAll(catePerms.longAuthorityData(), cateSet)) {
                        continue;
                    }
                }
            } else {
                continue;
            }

            // ===== 是否有区域权限 =====
            AsDataAuthority areaPerms = permMap.get(AssetConstant.DATA_PERMISSION_AREA + "_" + AssetConstant.AUTHORITY_DEPTS);
            if (areaPerms != null) {
                if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == areaPerms.getAuthorityType()) {
                    // 本公司区域
                    Set<Long> areaIds = new HashSet<>();
                    for (Long orgId : dispatchCache.getEmpCompanyMap().getOrDefault(userId, new HashSet<>())) {
                        for (AsArea area : dispatchCache.getOrgAreaAllMap().getOrDefault(orgId, ListUtil.empty())) {
                            areaIds.add(area.getId());
                        }
                    }
                    if (!CollUtil.containsAll(areaIds, areaSet)) {
                        continue;
                    }
                } else if (AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == areaPerms.getAuthorityType()) {
                    // 本公司和下属公司区域
                    Set<Long> areaIds = new HashSet<>();
                    for (Long orgId : dispatchCache.getEmpCompanyMap().getOrDefault(userId, new HashSet<>())) {
                        // 查询子公司
                        for (AsOrg org : dispatchCache.getOrgAllList()) {
                            if (AssetConstant.ORG_TYPE_COMPANY.equals(org.getOrgType())) {
                                if (org.getId().equals(orgId) || org.getPaths().contains(orgId.toString())) {
                                    for (AsArea area : dispatchCache.getOrgAreaAllMap().getOrDefault(org.getId(), ListUtil.empty())) {
                                        areaIds.add(area.getId());
                                    }
                                }
                            }
                        }
                    }
                    if (!CollUtil.containsAll(areaIds, areaSet)) {
                        continue;
                    }
                } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == areaPerms.getAuthorityType()) {
                    // 如果自定义的全包含资产集合的所有区域，则有权限
                    if (!CollUtil.containsAll(catePerms.longAuthorityData(), areaSet)) {
                        continue;
                    }
                }
            } else {
                continue;
            }

            // ===== 是否有组织权限 =====
            AsDataAuthority orgPerms = permMap.get(AssetConstant.DATA_PERMISSION_ORG + "_" + AssetConstant.AUTHORITY_DEPTS);
            if (orgPerms != null) {
                boolean managerOrUseBool = permMap.containsKey(AssetConstant.DATA_PERMISSION_ASSET + "_" + AssetConstant.AUTHORITY_MANAGER_OR_USE_DEPT);
                if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == orgPerms.getAuthorityType()) {
                    // 所在组织
                    Set<Long> orgPermsIds = dispatchCache.getEmpOrgMap().getOrDefault(userId, new HashSet<>());
                    if (managerOrUseBool) {
                        if (!(CollUtil.containsAll(orgPermsIds, orgOwnerSet)
                                || CollUtil.containsAll(orgPermsIds, useOrgSet))) {
                            continue;
                        }
                    } else {
                        if (!CollUtil.containsAll(orgPermsIds, orgOwnerSet)) {
                            continue;
                        }
                    }
                } else if (AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == orgPerms.getAuthorityType()) {
                    // 所在组织和子组织
                    Set<Long> orgPermsIds = new HashSet<>();
                    for (Long orgId : dispatchCache.getEmpOrgMap().getOrDefault(userId, new HashSet<>())) {
                        // 查询子部门
                        for (AsOrg org : dispatchCache.getOrgAllList()) {
                            if (org.getId().equals(orgId) || org.getPaths().contains(orgId.toString())) {
                                orgPermsIds.add(org.getId());
                            }
                        }
                    }
                    if (managerOrUseBool) {
                        if (!(CollUtil.containsAll(orgPermsIds, orgOwnerSet)
                                || CollUtil.containsAll(orgPermsIds, useOrgSet))) {
                            continue;
                        }
                    } else {
                        if (!CollUtil.containsAll(orgPermsIds, orgOwnerSet)) {
                            continue;
                        }
                    }
                } else if (AssetConstant.AUTHORITY_TYPE_COMP_AND_CHILD_DEPT == orgPerms.getAuthorityType()) {
                    // 所属公司及其下属组织
                    Set<Long> orgPermsIds = new HashSet<>();
                    for (Long orgId : dispatchCache.getEmpCompanyMap().getOrDefault(userId, new HashSet<>())) {
                        // 查询子部门
                        for (AsOrg org : dispatchCache.getOrgAllList()) {
                            if (org.getId().equals(orgId) || org.getPaths().contains(orgId.toString())) {
                                orgPermsIds.add(org.getId());
                            }
                        }
                    }
                    if (managerOrUseBool) {
                        if (!(CollUtil.containsAll(orgPermsIds, orgOwnerSet)
                                || CollUtil.containsAll(orgPermsIds, useOrgSet))) {
                            continue;
                        }
                    } else {
                        if (!CollUtil.containsAll(orgPermsIds, orgOwnerSet)) {
                            continue;
                        }
                    }
                } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == orgPerms.getAuthorityType()) {
                    // 自定义
                    List<Long> orgPermsIds = orgPerms.longAuthorityData();
                    // 管理组织或使用组织
                    if (managerOrUseBool) {
                        if (!(CollUtil.containsAll(orgPermsIds, orgOwnerSet)
                                || CollUtil.containsAll(orgPermsIds, useOrgSet))) {
                            continue;
                        }
                    } else {
                        if (!CollUtil.containsAll(orgPermsIds, orgOwnerSet)) {
                            continue;
                        }
                    }
                }
            } else {
                continue;
            }
            // 权限都通过则+1
            hasAccessEmpList.add(userId);
        }
        return hasAccessEmpList;
    }

    @Getter
    @AllArgsConstructor
    private enum DispatchEnum {
        DISPATCH_ORG_OWNER(InventoryConstant.DISPATCH_ORG_OWNER, "orgOwner", "所属管理组织"),
        DISPATCH_MANAGER_OWNER(InventoryConstant.DISPATCH_MANAGER_OWNER, "managerOwner", "资产管理员"),
        DISPATCH_STORAGE_AREA(InventoryConstant.DISPATCH_STORAGE_AREA, "storageArea", "存放区域"),
        DISPATCH_ASSET_CATEGORY(InventoryConstant.DISPATCH_ASSET_CATEGORY, "assetCategory", "资产分类"),
        DISPATCH_USE_ORG(InventoryConstant.DISPATCH_USE_ORG, "useOrg", "使用组织"),
        DISPATCH_USE_PERSON(InventoryConstant.DISPATCH_USE_PERSON, "usePerson", "资产使用人"),
        ;

        private final Integer type;
        private final String code;
        private final String name;

        public static DispatchEnum getByType(Integer type) {
            if (Objects.isNull(type)) {
                throw new BusinessException(InventoryResultCode.INVENTORY_DISPATCH_NOT_EXIST);
            }
            for (DispatchEnum item : values()) {
                if (item.getType().equals(type)) {
                    return item;
                }
            }
            throw new BusinessException(InventoryResultCode.INVENTORY_DISPATCH_NOT_EXIST);
        }
    }

}
