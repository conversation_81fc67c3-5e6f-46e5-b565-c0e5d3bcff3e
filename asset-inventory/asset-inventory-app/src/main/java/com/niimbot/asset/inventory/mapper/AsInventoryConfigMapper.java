package com.niimbot.asset.inventory.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.inventory.model.AsInventoryConfig;
import com.niimbot.inventory.InventoryStDto;

import java.util.List;

/**
 * <p>
 * 盘点配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryConfigMapper extends BaseMapper<AsInventoryConfig> {

    List<InventoryStDto> getStrategy();
}
