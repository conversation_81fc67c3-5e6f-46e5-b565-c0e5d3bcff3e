package com.niimbot.asset.inventory.service;

import com.niimbot.inventory.InventoryAssetRangeWithModeDto;
import com.niimbot.inventory.InventoryDispatchDetailDto;

/**
 * <AUTHOR>
 * @date 2024/5/8 17:03
 */
public interface AsInventoryDispatchService {

    /**
     * 盘点分配方式详细
     *
     * @param dto 资产查询条件
     * @return 结果
     */
    InventoryDispatchDetailDto getDispatchDetail(InventoryAssetRangeWithModeDto dto);

}
