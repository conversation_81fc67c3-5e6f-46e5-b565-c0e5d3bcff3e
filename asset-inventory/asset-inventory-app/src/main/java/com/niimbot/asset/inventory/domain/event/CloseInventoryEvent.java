package com.niimbot.asset.inventory.domain.event;

import com.niimbot.asset.framework.support.SystemEvent;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/4/20 15:12
 */
@Getter
public class CloseInventoryEvent extends SystemEvent {

    private Long companyId;
    private Long inventoryId;

    public CloseInventoryEvent(Long companyId,
                               Long inventoryId) {
        super(new Object());
        this.companyId = companyId;
        this.inventoryId = inventoryId;
    }

}
