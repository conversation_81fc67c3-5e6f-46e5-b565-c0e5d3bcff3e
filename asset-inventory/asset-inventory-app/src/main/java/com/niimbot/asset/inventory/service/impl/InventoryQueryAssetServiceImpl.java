package com.niimbot.asset.inventory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.niimbot.asset.inventory.mapper.AsInventoryQueryAssetMapper;
import com.niimbot.asset.inventory.model.AsInventoryQueryAsset;
import com.niimbot.asset.inventory.service.InventoryQueryAssetService;
import com.niimbot.inventory.InventoryCreateAssetQueryPageDto;
import com.niimbot.means.AssetDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 资产表 Service 业务逻辑
 * </p>
 *
 * <AUTHOR>
 * @si nce 2020-11-12
 */
@Slf4j
@Service
public class InventoryQueryAssetServiceImpl extends ServiceImpl<AsInventoryQueryAssetMapper, AsInventoryQueryAsset>
        implements InventoryQueryAssetService {

    public static final LoadingCache<String, AsInventoryDispatchServiceImpl.DispatchCache> GLOBAL_CACHE = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .build(s -> null);
    @Override
    public IPage<AssetDto> assetPage(InventoryCreateAssetQueryPageDto queryDto) {
        return this.page(queryDto.buildIPage(),
                        Wrappers.<AsInventoryQueryAsset>lambdaQuery()
                                .eq(AsInventoryQueryAsset::getQueryId, queryDto.getQueryId()))
                .convert(o -> BeanUtil.copyProperties(o, AssetDto.class));
    }

    @Override
    public List<AssetDto> listAsset(Long queryId) {
        return this.list(Wrappers.<AsInventoryQueryAsset>lambdaQuery()
                .eq(AsInventoryQueryAsset::getQueryId, queryId))
                .parallelStream().map(o -> BeanUtil.copyProperties(o, AssetDto.class))
                .collect(Collectors.toList());
    }

    @Override
    public void delete(Long queryId) {
        this.remove(Wrappers.<AsInventoryQueryAsset>lambdaQuery()
                .eq(AsInventoryQueryAsset::getQueryId, queryId));
        GLOBAL_CACHE.invalidate(queryId.toString());
        GLOBAL_CACHE.invalidate(queryId + "_perm");
    }
}
