package com.niimbot.asset.inventory.controller;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.InventoryRangeGroup;
import com.niimbot.asset.inventory.service.AsInventoryDispatchService;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventorySurplusService;
import com.niimbot.inventory.InventoryApproveSubmitDto;
import com.niimbot.inventory.InventoryAssetRangeWithModeDto;
import com.niimbot.inventory.InventoryCountDto;
import com.niimbot.inventory.InventoryCreateAssetQueryDto;
import com.niimbot.inventory.InventoryCreateAssetQueryPageDto;
import com.niimbot.inventory.InventoryDetailDto;
import com.niimbot.inventory.InventoryDispatchDetailDto;
import com.niimbot.inventory.InventoryQueryDto;
import com.niimbot.inventory.InventoryRangeGroupCreateDto;
import com.niimbot.inventory.InventoryReportDto;
import com.niimbot.inventory.InventorySubmitDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.inventory.InventoryUpdateApproverDto;
import com.niimbot.inventory.InventoryView;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDto;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;

/**
 * 盘点单后台api
 *
 * <AUTHOR>
 * @date 2021/4/9 09:27
 */
@RestController
@RequestMapping("server/inventory")
@RequiredArgsConstructor
public class AsInventoryServiceController {

    private final AsInventoryService inventoryService;
    private final AsInventoryDispatchService inventoryDispatchService;
    private final AsInventorySurplusService inventorySurplusService;

    /**
     * 盘点单分页列表
     *
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public IPage<AsInventory> page(InventoryQueryDto dto) {
        return inventoryService.page(dto.buildIPage(), buildWrapper(dto));
    }

    /**
     * 盘点任务明细
     *
     * @param id
     * @return
     */
    @GetMapping("/withoutAsset/{id}")
    public InventoryDetailDto getByIdWithoutAsset(@PathVariable Long id) {
        return inventoryService.getByIdWithoutAsset(id);
    }

    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @PostMapping("/surplus")
    public IPage<InventorySurplusDto> getSurplusById(@RequestBody InventorySurplusQueryDto dto) {
        return inventorySurplusService.getSurplusById(dto);
    }

    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @PostMapping("/surplus/list")
    public List<InventorySurplusDto> getSurplusList(@RequestBody InventorySurplusQueryDto dto) {
        return inventorySurplusService.getSurplusList(dto);
    }

    /**
     * 单个任务驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping("/rejected")
    public Boolean rejected(@RequestBody InventoryTaskApproveDto approveDto) {
        return inventoryService.rejected(approveDto);
    }

    /**
     * 整单驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping("/rejectedAll")
    public Boolean rejectedAll(@RequestBody InventoryApproveSubmitDto approveDto) {
        return inventoryService.rejectedAll(approveDto);
    }

    /**
     * 整单同意
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping("/approved")
    public Boolean approved(@RequestBody InventoryApproveSubmitDto approveDto) {
        return inventoryService.approved(approveDto);
    }

    /**
     * 单个同意
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping("/approvedOne")
    public Boolean approvedOne(@RequestBody InventoryTaskApproveDto approveDto) {
        return inventoryService.approvedOne(approveDto);
    }

    /**
     * 修改审核人
     *
     * @param approveDto
     * @return
     */
    @PutMapping("/updateApprover")
    public Boolean updateApprover(@RequestBody InventoryUpdateApproverDto approveDto) {
        return inventoryService.updateApprover(approveDto);
    }

    /**
     * 终止
     *
     * @param inventoryId
     * @return 结果
     */
    @PutMapping("/stop/{inventoryId}")
    public Boolean stop(@PathVariable(value = "inventoryId") Long inventoryId) {
        return inventoryService.stop(inventoryId);
    }

    /**
     * 盘点单删除
     *
     * @param inventoryId
     * @return 结果
     */
    @DeleteMapping("/{inventoryId}")
    public Boolean removeById(@PathVariable(value = "inventoryId") Long inventoryId) {
        return inventoryService.removeByInventoryId(inventoryId);
    }


    /**
     * 生成预创建盘点单资产数据
     *
     * @param dto
     */
    @PostMapping("/createInventoryQueryAsset")
    public void createInventoryQueryAsset(@RequestBody InventoryCreateAssetQueryDto dto) {
        inventoryService.handleInventoryCreateAsset(dto);
    }

    /**
     * 预创建盘点单资产数据查询
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/create/asset/page")
    public IPage<AssetDto> createAssetPage(@RequestBody InventoryCreateAssetQueryPageDto queryDto) {
        return inventoryService.createAssetPage(queryDto);
    }

    /**
     * 创建盘点单
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping
    public Long create(@RequestBody InventorySubmitDto dto) {
        return inventoryService.create(dto);
    }

    /**
     * 盘点分配方式详细
     *
     * @param dto 资产查询条件
     * @return 结果
     */
    @PostMapping("/dispatchDetail")
    public InventoryDispatchDetailDto getDispatchDetail(@RequestBody InventoryAssetRangeWithModeDto dto) {
        return inventoryDispatchService.getDispatchDetail(dto);
    }


    /**
     * 盘点单概览
     *
     * @param id
     * @return 结果
     */
    @GetMapping("/view/{id}")
    public InventoryView getInventoryView(@PathVariable(value = "id") Long id) {
        return inventoryService.getInventoryView(id);
    }

    /**
     * 盘点范围创建
     *
     * @param dto
     * @return
     */
    @PostMapping("/range/create")
    public List<InventoryRangeGroup> createRangeGroup(@RequestBody InventoryRangeGroupCreateDto dto) {
        return inventoryService.createRangeGroup(dto);
    }

    private Wrapper<AsInventory> buildWrapper(InventoryQueryDto dto) {
        LambdaQueryWrapper<AsInventory> wrapper = new QueryWrapper<AsInventory>().lambda()
                .in(ObjectUtil.isNotNull(dto.getStatus()), AsInventory::getStatus, dto.getStatus())
                .in(ObjectUtil.isNotNull(dto.getApprovers()), AsInventory::getApprover, dto.getApprovers())
                .in(ObjectUtil.isNotNull(dto.getCreateBys()), AsInventory::getCreateBy, dto.getCreateBys());

        if (CollUtil.isNotEmpty(dto.getCreateTime()) && dto.getCreateTime().size() == 2) {
            wrapper.ge(ObjectUtil.isNotNull(dto.getCreateTime().get(0)), AsInventory::getCreateTime,
                    StringUtils.concat(false, dto.getCreateTime().get(0), " 00:00:00"));
            wrapper.le(ObjectUtil.isNotNull(dto.getCreateTime().get(1)), AsInventory::getCreateTime,
                    StringUtils.concat(false, dto.getCreateTime().get(1), " 23:59:59"));
        }

        wrapper.and(StringUtils.isNotEmpty(dto.getKw()), p ->
                        p.like(AsInventory::getName, dto.getKw())
                                .or()
                                .like(AsInventory::getInventoryNo, dto.getKw()))
                .orderByDesc(AsInventory::getCreateTime);

        return wrapper;
    }

    /**
     * 盘点头部统计
     *
     * @return 结果
     */
    @GetMapping("/count")
    public InventoryCountDto count() {
        return inventoryService.countByStatus();
    }

    /**
     * 盘点单详情
     *
     * @param id 盘点单id
     * @return 结果
     */
    @GetMapping(value = "/info/{id}")
    public AsInventory getInfo(@PathVariable("id") Long id) {
        AsInventory one = inventoryService.getById(id);
        if (one == null) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        } else {
            return one;
        }
    }

    /**
     * 盘点报告
     *
     * @param id
     * @return 结果
     */
    @GetMapping("/report/{id}")
    public InventoryReportDto getInventoryReport(@PathVariable(value = "id") Long id) {
        return inventoryService.getInventoryReport(id);
    }

}
