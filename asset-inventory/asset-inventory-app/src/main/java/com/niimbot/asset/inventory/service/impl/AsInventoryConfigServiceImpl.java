package com.niimbot.asset.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.inventory.mapper.AsInventoryConfigMapper;
import com.niimbot.asset.inventory.model.AsInventoryConfig;
import com.niimbot.asset.inventory.service.AsInventoryConfigService;
import com.niimbot.inventory.InventoryStDto;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 盘点配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Service
public class AsInventoryConfigServiceImpl extends ServiceImpl<AsInventoryConfigMapper, AsInventoryConfig> implements AsInventoryConfigService {

    @Override
    public List<InventoryStDto> getStrategy() {
        return getBaseMapper().getStrategy();
    }
}
