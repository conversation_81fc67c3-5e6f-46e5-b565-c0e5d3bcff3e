package com.niimbot.asset.inventory.controller;


import com.niimbot.asset.inventory.service.AsInventoryApproveService;
import com.niimbot.inventory.InventoryApproveDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 盘点任务审批记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@RestController
@RequestMapping("server/inventory/approve")
@RequiredArgsConstructor
public class AsInventoryApproveServiceController {
    private final AsInventoryApproveService inventoryApproveService;

    /**
     * 审批记录列表
     *
     * @param inventoryId 盘点单id
     * @param taskId      任务id
     * @return 审批记录列表
     */
    @GetMapping("/list/{inventoryId}/{taskId}")
    public List<InventoryApproveDto> list(@PathVariable("inventoryId") Long inventoryId,
                                          @PathVariable("taskId") Long taskId) {
        return inventoryApproveService.getList(inventoryId, taskId);
    }
}
