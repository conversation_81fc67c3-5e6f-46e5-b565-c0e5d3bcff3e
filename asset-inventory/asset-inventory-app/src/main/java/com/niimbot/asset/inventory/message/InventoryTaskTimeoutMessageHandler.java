package com.niimbot.asset.inventory.message;

import com.google.common.collect.Lists;

import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.PeriodCompanyMessageHandler;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 盘点任务超时消息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InventoryTaskTimeoutMessageHandler extends PeriodCompanyMessageHandler {

    private final AsInventoryTaskService inventoryTaskService;

    @Override
    public String code() {
        return MessageConstant.Code.PDRWCSTX.getCode();
    }

    @Override
    protected boolean skip(MessageRuleCO rule) {
        return rule.shouldSkip();
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        // 站内信等消息
        List<AsInventoryTask> tasks = inventoryTaskService.listForSendTaskTimeoutMessage(rule.getCompanyId(), rule.getReminderTime());
        if (CollUtil.isEmpty(tasks)) {
            return;
        }
        Set<Long> userIds = tasks.stream()
                .filter(v -> CollUtil.isNotEmpty(v.getInventoryUsers()))
                .flatMap(v -> v.getInventoryUsers().stream())
                .collect(Collectors.toSet());
        Map<Long, Integer> userTaskNumMap = new HashMap<>();
        Map<Long, List<AsInventoryTask>> userTaskMap = new HashMap<>();
        for (Long userId : userIds) {
            for (AsInventoryTask task : tasks) {
                if (task.getInventoryUsers().contains(userId)) {
                    if (userTaskNumMap.containsKey(userId)) {
                        userTaskNumMap.put(userId, userTaskNumMap.get(userId) + 1);
                    } else {
                        userTaskNumMap.put(userId, 1);
                    }
                    if (userTaskMap.containsKey(userId)) {
                        userTaskMap.get(userId).add(task);
                    } else {
                        userTaskMap.put(userId, ListUtil.toList(task));
                    }
                }
            }
        }
        for (Long userId : userIds) {
            List<AsInventoryTask> part = userTaskMap.get(userId);
            try {
                Map<String, String> mapParams = new HashMap<>(2);
                mapParams.put(MessageConstant.Template.AMOUNT, String.valueOf(part.size()));
                Map<String, Object> commonExtMap = new HashMap<>(1);
                commonExtMap.put("businessId", String.valueOf(tasks.get(0).getId()));
                bodies.add(
                        Body.builder()
                                .mapParam(mapParams)
                                .appExtMapParam(Collections.singletonMap("inventoryTaskId", String.valueOf(tasks.get(0).getId())))
                                .commonExtMap(commonExtMap)
                                .arrayParam(new String[]{String.valueOf(tasks.size())})
                                .userIds(Collections.singleton(userId))
                                .urlParam(Lists.newArrayList(part.get(0).getId()))
                                .build()
                );
            } catch (Exception e) {
                log.error("盘点任务超时业务消息提醒发送失败", e);
            }
        }
    }

}
