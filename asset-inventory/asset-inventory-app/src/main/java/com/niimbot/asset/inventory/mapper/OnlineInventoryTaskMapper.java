package com.niimbot.asset.inventory.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.ding.DingInventoryQueryDto;
import com.niimbot.ding.DingInventoryTaskListDto;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OnlineInventoryTaskMapper {

    IPage<DingInventoryTaskListDto> onlineTaskPage(Page<T> buildIPage,
                                                   @Param("companyId") Long companyId,
                                                   @Param("userId") Long userId,
                                                   @Param("ew") DingInventoryQueryDto dto);

    List<DingInventoryTaskListDto> assetCount(@Param("inventoryId") Long inventoryId,
                                              @Param("taskIds") List<Long> taskIds);

    List<DingInventoryTaskListDto> countAddAsset(@Param("inventoryId") Long inventoryId,
                                                 @Param("taskIds") List<Long> taskIds);

}
