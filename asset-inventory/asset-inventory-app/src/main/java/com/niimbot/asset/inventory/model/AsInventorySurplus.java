package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 盘点-资产盘盈表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventorySurplus对象", description="盘点-资产盘盈表")
@TableName(value = "as_inventory_surplus", autoResultMap = true)
public class AsInventorySurplus implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long reportId;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryTaskId;

}
