package com.niimbot.asset.inventory.domain.event;

import com.niimbot.asset.framework.support.SystemEvent;

import lombok.Getter;

/**
 * 更换审核人事件
 *
 * <AUTHOR>
 * @date 2023/4/25 17:13
 */
@Getter
public class UpdateApproverEvent extends SystemEvent {

    private Long companyId;
    private Long inventoryId;
    private Long sourceUserId;

    public UpdateApproverEvent(Long companyId,
                               Long inventoryId,
                               Long sourceUserId) {
        super(new Object());
        this.companyId = companyId;
        this.inventoryId = inventoryId;
        this.sourceUserId = sourceUserId;
    }
}
