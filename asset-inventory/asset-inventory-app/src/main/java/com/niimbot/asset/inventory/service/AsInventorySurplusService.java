package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryAssetReport;
import com.niimbot.asset.inventory.model.AsInventorySurplus;
import com.niimbot.inventory.InventoryAssetCountQueryDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventorySurplusSimpleQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘点-资产盘盈表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventorySurplusService extends IService<AsInventorySurplus> {
    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    IPage<InventorySurplusDto> getSurplusById(InventorySurplusQueryDto dto);

    /**
     * 删除当前用户的上报盘盈数据
     *
     * @param userId 当前用户id
     * @return 结果
     */
    boolean removeWithReport(Long userId, Long inventoryId, Integer taskType);

    /**
     * 通过任务id删除盘盈资产
     *
     * @param taskId
     * @return
     */
    boolean removeAssetByTaskId(@Param("taskId") Long taskId);

    /**
     * 通过任务id查询盘盈资产
     *
     * @param taskId
     * @return
     */
    List<AsInventoryAssetReport> listAssetByTaskId(@Param("taskId") Long taskId);

    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    List<InventorySurplusDto> getSurplusList(InventorySurplusQueryDto dto);

    /**
     * 盘点损益处理盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    IPage<InventorySurplusDto> getPlSurplus(InventorySurplusSimpleQueryDto dto);

    /**
     * 获取盘点单下所有的在册盘盈资产id
     *
     * @param inventoryId
     * @return 结果
     */
    List<Long> getMarkSurplusAsset(Long inventoryId);

    /**
     * 删除已经存在的盘盈资产
     *
     * @param deleteAssetIds
     * @return 结果
     */
    boolean removeExistAsset(List<Long> deleteAssetIds, Long inventoryId);

    /**
     * 获取盘盈数量
     *
     * @param dto
     * @return 结果
     */
    int countAddAsset(InventoryAssetCountQueryDto dto);

    /**
     * 未处理数量
     *
     * @param inventoryId
     * @return
     */
    int getUnhandleNum(Long inventoryId);
}
