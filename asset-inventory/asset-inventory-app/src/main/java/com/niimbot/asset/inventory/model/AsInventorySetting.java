package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/2/22 15:29
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_inventory_setting", autoResultMap = true)
public class AsInventorySetting {

    /**
     * 主键Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 公司Id
     */
//    @TableField(fill = FieldFill.INSERT)
//    @TenantFilterColumn 这里需要包含全局配置的，所以不用自动权限
    private Long companyId;
    /**
     * 字段编码
     */
    private String fieldCode;
    /**
     * 类型（1-盘点编辑，2-盘点上报）
     */
    private Integer inventoryType;
    /**
     * 开启编辑
     */
    private Boolean enableEdit;
    /**
     * 是否必填
     */
    private Boolean required;
    /**
     * 是否只读
     */
    private Boolean readOnly;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
