package com.niimbot.asset.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.mapper.AsInventoryAssetReportMapper;
import com.niimbot.asset.inventory.model.AsInventoryAssetReport;
import com.niimbot.asset.inventory.service.AsInventoryAssetReportService;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 盘点资产 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Service
public class AsInventoryAssetReportServiceImpl extends ServiceImpl<AsInventoryAssetReportMapper, AsInventoryAssetReport> implements AsInventoryAssetReportService {

    @Override
    public List<Long> unHandleIds(List<Long> ids) {
        return getBaseMapper().unHandleIds(LoginUserThreadLocal.getCompanyId(), ids);
    }
}
