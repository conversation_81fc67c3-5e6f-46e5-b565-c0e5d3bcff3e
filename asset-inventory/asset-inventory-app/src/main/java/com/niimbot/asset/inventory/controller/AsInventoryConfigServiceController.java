package com.niimbot.asset.inventory.controller;


import com.niimbot.asset.inventory.model.AsInventoryConfig;
import com.niimbot.asset.inventory.service.AsInventoryConfigService;
import com.niimbot.inventory.InventoryStDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 盘点配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("server/inventory/config")
public class AsInventoryConfigServiceController {

    private final AsInventoryConfigService inventoryConfigService;

    /**
     * 盘点单详细配置数据
     *
     * @param id
     * @return 结果
     */
    @GetMapping("/{id}")
    public AsInventoryConfig getConfigById(@PathVariable Long id) {
        return inventoryConfigService.getById(id);
    }

    /**
     * 盘点策略
     */
    @GetMapping("/strategy")
    public List<InventoryStDto> getStrategy() {
        return inventoryConfigService.getStrategy();
    }

}
