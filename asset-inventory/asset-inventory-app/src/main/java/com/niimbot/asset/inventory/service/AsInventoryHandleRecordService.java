package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryHandleRecord;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/3 13:52
 */
public interface AsInventoryHandleRecordService extends IService<AsInventoryHandleRecord> {

    Set<Long> hasHandleReportRecord(List<Long> inventoryAssetReportId, Integer bizType);

    Set<Long> hasHandleInventoryRecord(List<Long> inventoryAssetId, Integer bizType);

}
