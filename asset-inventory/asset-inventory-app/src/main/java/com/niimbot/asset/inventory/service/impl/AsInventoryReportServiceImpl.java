package com.niimbot.asset.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.inventory.model.AsInventoryReport;
import com.niimbot.asset.inventory.service.AsInventoryReportService;
import com.niimbot.asset.inventory.mapper.AsInventoryReportMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 盘点报告 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Service
public class AsInventoryReportServiceImpl extends ServiceImpl<AsInventoryReportMapper, AsInventoryReport> implements AsInventoryReportService {

}
