package com.niimbot.asset.inventory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.inventory.model.AsInventoryAsset;
import com.niimbot.asset.inventory.service.AsInventoryAssetService;
import com.niimbot.asset.inventory.service.AsInventorySurplusService;
import com.niimbot.inventory.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 盘点资产 前端控制器
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@RestController
@RequestMapping("server/inventory/asset")
@RequiredArgsConstructor
public class AsInventoryAssetServiceController {

    private final AsInventoryAssetService inventoryAssetService;

    private final AsInventorySurplusService inventorySurplusService;

    /**
     * 盘点资产分页列表
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @PostMapping("/page")
    public IPage<InventoryAssetListDto> page(@RequestBody @Validated InventorySurplusQueryDto dto) {
        return inventoryAssetService.selectAppAssetList(dto);
    }

    /**
     * 盘点资产分页列表
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @PostMapping("/plPage")
    public IPage<InventoryAssetListDto> plPage(
            @RequestBody @Validated InventorySurplusQueryDto dto) {
        return inventoryAssetService.selectPlAssetList(dto);
    }

    /**
     * 盘点资产分页列表
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @PostMapping("/appPage")
    public IPage<InventoryAssetListDto> appPage(@RequestBody InventorySurplusQueryDto dto) {
        return inventoryAssetService.selectAppAssetList(dto);
    }

    @GetMapping("/scan/ref")
    public Map<String, String> inventoryAssetScanRef(@RequestParam(value = "inventoryId", required = false) Long inventoryId,
                                                     @RequestParam("taskId") Long taskId) {
        return inventoryAssetService.inventoryAssetScanRef(inventoryId, taskId);
    }

    /**
     * APP非当前盘点人已盘资产列表
     *
     * @param dto
     * @return
     */
    @GetMapping("/otherUser")
    public OtherUserInventoryAssetDto otherUser(@Validated InventoryAssetOtherUserDto dto) {
        return inventoryAssetService.otherUser(dto);
    }

    /**
     * 盘点资产列表
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @PostMapping("/listAll")
    public List<InventoryAssetListDto> listAll(
            @RequestBody @Validated InventorySurplusQueryDto dto) {
        return inventoryAssetService.selectAssetListAll(dto);
    }

    /**
     * 盘点资产头部统计
     *
     * @param dto
     * @return 结果
     */
    @GetMapping("/count")
    public InventoryAssetCountDto assetCount(InventoryAssetCountQueryDto dto) {
        return inventoryAssetService.assetCount(dto);
    }

    /**
     * 盘点资产盘亏处理
     *
     * @param pDto 盘亏DTO
     * @return 结果
     */
    @PostMapping("/assetHandle")
    public InventoryAssetHandleDto assetHandle(@RequestBody InventoryAssetHandleDto pDto) {
        return inventoryAssetService.assetHandle(pDto);
    }

    @PostMapping("/assetChangeRollback")
    public void assetChangeRollback(@RequestBody InventoryAssetHandleDto pDto) {
        inventoryAssetService.assetChangeRollback(pDto);
    }

    /**
     * PC盘点资产盘亏处理-校验资产是否能盘亏处理
     *
     * @param ids 盘点资产ids
     * @return 结果
     */
    @PutMapping("/checkAssetLoss")
    public Boolean checkAssetLoss(@RequestBody List<Long> ids) {
        return inventoryAssetService.checkAssetHandle(ids, AssetConstant.ORDER_TYPE_DISPOSE);
    }

    @PutMapping("/checkAssetChange")
    public Boolean checkAssetChange(@RequestBody List<Long> ids) {
        return inventoryAssetService.checkAssetHandle(ids, AssetConstant.ORDER_TYPE_CHANGE);
    }

    /**
     * 查询资产详情快照
     *
     * @param id 盘点资产表Id
     * @return 资产详情集合
     */
    @GetMapping("/detail/{id}")
    public AsInventoryAsset getAssetDetail(@PathVariable Long id) {
        return inventoryAssetService.getAssetDetail(id);
    }

    /**
     * 编辑盘点资产
     *
     * @param dto
     * @return 结果
     */
    @PutMapping
    public Boolean editAsset(@RequestBody InventoryAssetEditDto dto) {
        return inventoryAssetService.editAsset(dto);
    }

    /**
     * 盘点损益处理盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @PostMapping("/surplus")
    public IPage<InventorySurplusDto> getPlSurplus(@RequestBody InventorySurplusSimpleQueryDto dto) {
        return inventorySurplusService.getPlSurplus(dto);
    }

    /**
     * 已修改资产列表分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/modifiedPage")
    public IPage<InventoryAssetListDto> modifiedPage(@RequestBody InventorySurplusQueryDto dto) {
        return inventoryAssetService.modifiedPage(dto);
    }

    /**
     * PC损益处理-已拍照资产
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/takePhotoPage")
    public IPage<InventoryAssetPcDto> takePhotoPage(@RequestBody InventorySurplusQueryDto dto) {
        return inventoryAssetService.takePhotoPage(dto);
    }

    /**
     * 已修改资产列表分页单据-不分页
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/modifiedPageList")
    public List<InventoryAssetListDto> modifiedPageList(
            @RequestBody @Validated InventorySurplusQueryDto dto) {
        return inventoryAssetService.modifiedPageList(dto);
    }

    /**
     * 忽略修改资产
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @PutMapping("/ignoreUpdate")
    public Boolean ignoreUpdate(@RequestBody @Validated IgnoreUpdateDto dto) {
        return inventoryAssetService.ignoreUpdate(dto);
    }

    /**
     * 修改资产信息
     *
     * @param dto 资产数据
     * @return 结果
     */
    @PutMapping("/plUpdateAsset")
    public Boolean plUpdateAsset(@RequestBody AssetUpdateDto dto) {
        return inventoryAssetService.plUpdateAsset(dto);
    }

    /**
     * 使用盘点人修改信息
     *
     * @param dto 资产数据
     * @return 结果
     */
    @PutMapping("/plUpdateAssetByUser")
    public Boolean plUpdateAssetByUser(@RequestBody List<PlUpdateAssetByUserDto> dto) {
        return inventoryAssetService.plUpdateAssetByUser(dto);
    }

    /**
     * 盘盈资产新增入库
     *
     * @param dto 资产数据
     * @return 资产ID
     */
    @PostMapping("/plAddAsset")
    public Long plAddAsset(@RequestBody AssetAddDto dto) {
        return inventoryAssetService.plAddAsset(dto);
    }

    /**
     * 判断资产id是否在盘点单内
     *
     * @param assetId 盘点资产id
     * @param inventoryId 盘点单id
     * @return 结果
     */
    @GetMapping("/checkAssetId")
    public Boolean checkAssetId(
            @RequestParam("assetId") Long assetId, @RequestParam("inventoryId") Long inventoryId) {
        return inventoryAssetService.checkAssetId(assetId, inventoryId);
    }

    /**
     * 历史盘点单资产id列表
     *
     * @param dto
     * @return
     */
    @GetMapping("/historyInventoryAssetIds")
    public List<Long> historyInventoryAssetIds(InventoryHistoryQueryDto dto) {
        if (dto.getType() == 0) {
            return inventoryAssetService.listAssetIds(dto.getInventoryIds(), 0);
        } else {
            return inventoryAssetService.listAssetIds(dto.getInventoryIds(), null);
        }
    }

    @PostMapping("/addPhoto")
    public Boolean addPhoto(@RequestBody PlPhotoDto dto) {
        return inventoryAssetService.addPhoto(dto);
    }

    @PostMapping("/replacePhoto")
    public Boolean replacePhoto(@RequestBody PlPhotoDto dto) {
        return inventoryAssetService.replacePhoto(dto);
    }

    @GetMapping("/handleLog")
    public InventoryHandleLogDto handleLog(InventoryHandleLogQry qry) {
        return inventoryAssetService.handleLog(qry);
    }

    @GetMapping("/handleLog/{inventoryId}")
    public List<InventoryHandleLogDto> handleLogList(@PathVariable("inventoryId") Long inventoryId) {
        return inventoryAssetService.handleLogList(inventoryId);
    }

    /**
     * 修改盘点备注
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "/updateRemark")
    public Boolean updateRemark(@RequestBody InventoryRemarkDto dto) {
        return inventoryAssetService.updateRemark(dto);
    }

    /**
     * 修改盘点备注
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "/updatePicture")
    public Boolean updatePicture(@RequestBody InventoryPictureDto dto) {
        return inventoryAssetService.updatePicture(dto);
    }


}
