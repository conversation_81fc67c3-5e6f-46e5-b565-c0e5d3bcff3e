package com.niimbot.asset.inventory.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资产表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsInventoryQueryAsset对象", description = "盘点单创建查询资产表")
@TableName(value = "as_inventory_query_asset", autoResultMap = true)
public class AsInventoryQueryAsset implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "查询id")
    private Long queryId;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "标准品ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long standardId;

    @ApiModelProperty(value = "属性JSON数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject assetData;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "业务调整前状态")
    private Integer beforeStatus;

    @ApiModelProperty(value = "本次维修前状态（冗余字段，可关联最后一次维修前状态）")
    private Integer repairBeforeStatus;

    @ApiModelProperty(value = "RFID-TID")
    private String labelTid;

    @ApiModelProperty(value = "rfid_epcid")
    private String labelEpcid;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "虚拟键——资产编码")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String assetCode;

    @ApiModelProperty(value = "虚拟键——组织ID")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
//    @DeptFilterColumn
    private String orgOwner;

    @ApiModelProperty(value = "虚拟键——员工ID")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
//    @UserFilterColumn
    private String usePerson;

    @ApiModelProperty(value = "虚拟键——资产分类ID")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
//    @CateFilterColumn
    private String assetCategory;

    @ApiModelProperty(value = "虚拟键——资产名称")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String assetName;

    @ApiModelProperty(value = "虚拟键——区域ID")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
//    @AreaFilterColumn
    private String storageArea;

    @ApiModelProperty(value = "虚拟键——资产来源")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String assetOrigin;

    @ApiModelProperty(value = "虚拟键——所属管理员")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
//    @UserFilterColumn
    private String managerOwner;

    @ApiModelProperty(value = "虚拟键——使用组织")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
//    @DeptFilterColumn
    private String useOrg;

}
