package com.niimbot.asset.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.inventory.InventoryQueryDto;
import com.niimbot.inventory.InventoryTaskInfoDto;
import com.niimbot.inventory.InventoryTaskListDto;

import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <p>
 * 盘点任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryTaskMapper extends BaseMapper<AsInventoryTask> {

    /**
     * 盘点任务分页列表
     *
     * @param dto
     * @return
     */
    IPage<InventoryTaskListDto> selectTaskList(Page<T> buildIPage,
                                               @Param("companyId") Long companyId,
                                               @Param("userId") Long userId,
                                               @Param("ew") InventoryQueryDto dto);

    /**
     * 盘点任务详情
     *
     * @param id 盘点任务id
     * @return 结果
     */
    InventoryTaskInfoDto getInfo(@Param("id") Long id);

    List<AsInventoryTask> selectAllForSendTaskTimeoutMessage(@Param("companyId") Long companyId, @Param("days") List<Integer> days);

    List<InventoryTaskListDto> assetCount(@Param("inventoryId") Long inventoryId,
                                          @Param("taskIds") List<Long> taskIds);

    List<InventoryTaskListDto> countAddAsset(@Param("inventoryId") Long inventoryId,
                                             @Param("taskIds") List<Long> taskIds);
}
