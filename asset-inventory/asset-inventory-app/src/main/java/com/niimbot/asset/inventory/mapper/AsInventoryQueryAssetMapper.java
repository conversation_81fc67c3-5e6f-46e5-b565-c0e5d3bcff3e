package com.niimbot.asset.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.inventory.model.AsInventoryQueryAsset;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 资产表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@EnableDataPerm
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsInventoryQueryAssetMapper.class)
public interface AsInventoryQueryAssetMapper extends BaseMapper<AsInventoryQueryAsset> {

}
