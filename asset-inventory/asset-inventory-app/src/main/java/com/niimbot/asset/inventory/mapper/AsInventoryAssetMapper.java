package com.niimbot.asset.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.inventory.model.AsInventoryAsset;
import com.niimbot.inventory.InventoryAssetCountDto;
import com.niimbot.inventory.InventoryAssetCountQueryDto;
import com.niimbot.inventory.InventoryAssetListDto;
import com.niimbot.inventory.InventoryAssetPcDto;
import com.niimbot.inventory.InventoryAssetQueryDto;
import com.niimbot.inventory.InventorySurplusQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 盘点资产 Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryAssetMapper extends BaseMapper<AsInventoryAsset> {

    /**
     * 盘点资产列表
     *
     * @param dto 条件
     * @return 结果集
     */
    List<InventoryAssetListDto> selectAssetList(@Param("iaq") InventoryAssetQueryDto dto);

    /**
     * 获取未盘已盘数量
     *
     * @param dto
     * @return 结果
     */
    InventoryAssetCountDto assetCount(@Param("ew") InventoryAssetCountQueryDto dto);

    /**
     * 获取未盘已盘数量
     *
     * @param dto
     * @return 结果
     */
    InventoryAssetCountDto assetCount2(@Param("ew") InventoryAssetCountQueryDto dto);

    /**
     * 非当前盘点人盘点资产
     *
     * @param inventoryUser
     * @param lastQueryTime
     * @return
     */
    List<InventoryAssetListDto> otherUserAssetList(
            @Param("taskId") Long taskId,
            @Param("inventoryUser") Long inventoryUser,
            @Param("lastQueryTime") String lastQueryTime);

    List<InventoryAssetListDto> selectAppAssetList(@Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);

    IPage<InventoryAssetListDto> selectAppAssetList(Page<Object> buildIPage,
                                                    @Param("ew") InventorySurplusQueryDto dto,
                                                    @Param("conditions") String conditions,
                                                    @Param("currentUserId") Long currentUserId);

    /**
     * 盘点资产列表
     *
     * @param dto 条件
     * @return 结果集
     */
    /*List<InventoryAssetListDto> selectAppAssetList(@Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);
    IPage<InventoryAssetListDto> selectAppAssetList(Page<Object> buildIPage, @Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);

    *//**
     * 盘点资产列表
     *
     * @param dto 条件
     * @return 结果集
     *//*
    List<InventoryAssetListDto> selectAppAssetList2(@Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);
    IPage<InventoryAssetListDto> selectAppAssetList2(Page<Object> buildIPage, @Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);*/
    /**
     * 盘点资产列表
     *
     * @param dto 条件
     * @return 结果集
     */


    /**
     * 已修改资产列表分页单据
     *
     * @param dto dto
     * @return 结果
     */
    IPage<InventoryAssetListDto> modifiedPage(
            Page<Object> buildIPage, @Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);

    IPage<InventoryAssetPcDto> takePhotoPage(
            Page<Object> buildIPage, @Param("ew") InventorySurplusQueryDto dto, @Param("conditions") String conditions);

    /**
     * 已修改资产列表单据
     *
     * @param dto dto
     * @return 结果
     */
    List<InventoryAssetListDto> modifiedPageList(@Param("ew") InventorySurplusQueryDto dto);

    /**
     * 判断资产id是否在盘点单内
     *
     * @param assetId     盘点资产id
     * @param inventoryId 盘点单id
     * @return 结果
     */
    @Select(
            "SELECT COUNT(*) AS count FROM as_inventory_asset WHERE inventory_id = #{inventoryId} AND asset_id = #{assetId}")
    Boolean checkAssetId(@Param("assetId") Long assetId, @Param("inventoryId") Long inventoryId);

    List<Long> listAssetIds(
            @Param("inventoryIds") List<Long> inventoryIds, @Param("checked") Integer checked);

    @Select("SELECT COUNT(*) AS count FROM as_inventory_asset WHERE checked = 0 and  handle_status = 1 and inventory_id = #{inventoryId}")
    int getUnhandleNum(@Param("inventoryId") Long inventoryId);

    @Select("SELECT COUNT(*) AS count FROM as_inventory_asset a WHERE a.checked = 1 and a.handle_status = 1 and exists (select 1 from as_inventory_asset_log b where (b.log_data != JSON_ARRAY() or b.log_data is null) and b.inventory_id = a.inventory_id and b.asset_id = a.asset_id) and inventory_id = #{inventoryId}")
    int getUnhandleNum2(@Param("inventoryId") Long inventoryId);

    @Select("SELECT count(*) FROM as_inventory_asset AS a left join as_inventory_handle_record r on a.id = r.inventory_asset_id and r.biz_type = 4\n" +
            "        WHERE a.inventory_id = #{inventoryId} and r.id is null and a.picture_url is not null and a.picture_url != ''")
    int getUnhandlePhotoNum(@Param("inventoryId") Long inventoryId);

    List<Map<String, Object>> inventoryAssetScanRef(@Param("inventoryId") Long inventoryId,
                                                    @Param("taskId") Long taskId,
                                                    @Param("uniqueCodes") List<String> uniqueCodes,
                                                    @Param("companyId") Long companyId);

    List<Long> unHandleIds(@Param("companyId") Long companyId, @Param("ids") List<Long> ids);
}
