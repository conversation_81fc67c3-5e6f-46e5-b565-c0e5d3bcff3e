package com.niimbot.asset.inventory.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资产上报表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryAssetReport对象", description="资产上报表")
@TableName(value = "as_inventory_asset_report", autoResultMap = true)
public class AsInventoryAssetReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "资产数据")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject assetData;

    @ApiModelProperty(value = "上报人")
    private Long reporter;

    @ApiModelProperty(value = "处理状态（1：未处理，2：已处理）")
    private Integer handleStatus;

    @ApiModelProperty(value = "处理人")
    private Long handleUser;

    @ApiModelProperty(value = "处理时间")
    private LocalDateTime handleTime;

    @ApiModelProperty(value = "处理类型 1-新增入库  2-更新资产")
    private Integer handleType;

    @ApiModelProperty(value = "资产标记(1：在册资产，2：不在册资产)")
    private Integer assetMark;

    @ApiModelProperty(value = "资产上报类型(1:盘点上报，2：日常上报)")
    private Integer reportType;

    @ApiModelProperty(value = "新增资产id")
    private Long assetId;

    @ApiModelProperty(value = "状态 1-未处理 2-已入库 3-作废")
    private Integer status;

    @ApiModelProperty(value = "盘点备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "盘点终端(1：pc端，2：PDA，3：移动管理端，4：移动员工端，5：小程序)")
    private Integer inventoryTerminal;

    @ApiModelProperty(value = "任务类型 1-盘点任务  2-全员盘点任务")
    private Integer taskType;

}
