package com.niimbot.asset.inventory.mapstruct;

import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.system.dto.InventoryAssetCountGetQry;
import com.niimbot.asset.system.dto.clientobject.InventoryAssetCountCO;
import com.niimbot.asset.system.dto.clientobject.InventoryCO;
import com.niimbot.inventory.InventoryAssetCountDto;
import com.niimbot.inventory.InventoryAssetCountQueryDto;

import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface InventoryMapStruct {

    InventoryCO convertInventoryModelToCo(AsInventory inventory);

    InventoryAssetCountQueryDto convertInventoryAssetCountGetQryToDto(InventoryAssetCountGetQry qry);

    InventoryAssetCountCO convertInventoryAssetCountDtoToCo(InventoryAssetCountDto dto);
}
