package com.niimbot.asset.inventory.domain.abs.impl;

import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.system.abs.InventoryTaskAbs;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/client/abs/inventory/inventoryTaskAbs")
@RequiredArgsConstructor
public class InventoryTaskAbsImpl implements InventoryTaskAbs {

    private final AsInventoryTaskService inventoryTaskService;

    @Override
    public Boolean checkInventory(Long userId) {
        return inventoryTaskService.checkInventory(userId);
    }
}
