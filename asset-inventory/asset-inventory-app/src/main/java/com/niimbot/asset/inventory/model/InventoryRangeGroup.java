package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.niimbot.asset.inventory.handle.InventoryRangeRecordListTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 盘点范围
 *
 * <AUTHOR>
 * @date 2021/4/9 10:26
 */
@Data
@Accessors(chain = true)
public class InventoryRangeGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组名称")
    private String name;

    @ApiModelProperty(value = "盘点范围")
    @TableField(typeHandler = InventoryRangeRecordListTypeHandler.class)
    private List<InventoryRangeRecord> inventoryRange;
}
