package com.niimbot.asset.inventory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.inventory.service.OnlineInventoryService;
import com.niimbot.asset.inventory.service.OnlineInventoryTaskService;
import com.niimbot.ding.*;
import com.niimbot.inventory.*;
import com.niimbot.jf.logging.annotation.ApiLogging;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiLogging
@RestController
@RequestMapping("server/online/inventory")
@RequiredArgsConstructor
public class OnlineInventoryServiceController {

    private final OnlineInventoryService onlineInventoryService;
    private final OnlineInventoryTaskService onlineInventoryTaskService;
    private final AsInventoryTaskService inventoryTaskService;

    @PostMapping(value = "/asset/manualInventory/batch")
    public Boolean manualInventoryBatch(@RequestBody InventoryManualBatchDto dto) {
        return onlineInventoryService.manualInventoryBatch(dto);
    }

    @PostMapping(value = "/asset/updateRemark/batch")
    public Boolean updateRemarkBatch(@RequestBody InventoryRemarkBatchDto dto) {
        return onlineInventoryService.updateRemarkBatch(dto);
    }

    /**
     * 当前区域盘点进度统计-小程序使用
     *
     * @param queryDto
     * @return
     */
    @GetMapping("/asset/areaStatistics")
    public InventoryAssetAreaStatisticsDto areaStatistics(InventoryAssetAreaQueryDto queryDto) {
        return onlineInventoryService.storageAreaInventoryAsset(queryDto);
    }

    /**
     * @return
     */
    @PostMapping(value = "/asset/rfid/submit")
    public InventoryAssetRFIDResultDto assetRFID(@RequestBody InventoryAssetRFIDSubmitDto request) {
        return onlineInventoryService.inventoryAssetRFID(request);
    }

    /**
     * app手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    @PutMapping(value = "/task/appManualInventory")
    public Boolean appManualInventory(@RequestBody InventoryManualDto dto) {
        return inventoryTaskService.manualInventory(dto);
    }

    /**
     * 判断资产id是否在盘点单内
     *
     * @param assetId     盘点资产id
     * @param inventoryId 盘点单id
     * @return 结果
     */
    @GetMapping("/asset/appCheckAssetId")
    public long checkAssetId(
            @RequestParam("assetId") String assetId, @RequestParam("inventoryId") Long inventoryId, @RequestParam("inventoryTaskId") Long inventoryTaskId) {
        return onlineInventoryService.appCheckAssetId(assetId, inventoryId, inventoryTaskId);
    }

    @PostMapping("/asset/post/appAssetCode")
    public List<InventoryAssetDataCountDto> appAssetCode(@RequestBody InventorySurplusQueryDto dto) {
        return onlineInventoryService.selectAppAssetCode(dto);
    }

    @GetMapping("/task/page")
    public IPage<DingInventoryTaskListDto> taskPage(DingInventoryQueryDto queryDto) {
        return onlineInventoryTaskService.onlineTaskPage(queryDto);
    }

}
