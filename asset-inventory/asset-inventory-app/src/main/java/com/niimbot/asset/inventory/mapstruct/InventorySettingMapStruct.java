package com.niimbot.asset.inventory.mapstruct;

/**
 * <AUTHOR>
 * @date 2022/5/26 11:12 上午
 */

import com.niimbot.asset.inventory.model.AsInventorySetting;
import com.niimbot.inventory.InventorySettingDto;

import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2022/5/25 7:28 下午
 */
@Mapper(componentModel = "spring")
public interface InventorySettingMapStruct {

    InventorySettingDto toDto(AsInventorySetting setting);

}
