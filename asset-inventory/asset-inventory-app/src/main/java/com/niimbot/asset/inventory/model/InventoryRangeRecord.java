package com.niimbot.asset.inventory.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 盘点范围
 *
 * <AUTHOR>
 * @date 2021/4/9 10:26
 */
@Data
@Accessors(chain = true)
public class InventoryRangeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "范围key")
    private String key;

    @ApiModelProperty(value = "范围name")
    private String name;

    @ApiModelProperty(value = "范围值")
    private List<String> value;

    @ApiModelProperty(value = "范围值名称")
    private List<String> valueText;
}
