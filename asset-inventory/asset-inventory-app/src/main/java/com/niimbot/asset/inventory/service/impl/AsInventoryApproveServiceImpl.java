package com.niimbot.asset.inventory.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.mapper.AsInventoryApproveMapper;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryApprove;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.service.AsInventoryApproveService;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.inventory.InventoryApproveDto;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 盘点任务审批记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Service
public class AsInventoryApproveServiceImpl extends ServiceImpl<AsInventoryApproveMapper, AsInventoryApprove> implements AsInventoryApproveService {
    @Resource
    private AsInventoryTaskService inventoryTaskService;

    @Resource
    private AsInventoryService inventoryService;

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Override
    public boolean addSubmitRecord(Long inventoryId, Long taskId, String opinion) {
        return addRecord(inventoryId, taskId, opinion, AsInventoryApprove.ACTION_SUBMIT);
    }

    @Override
    public boolean addApprovedRecord(Long inventoryId, Long taskId, String opinion) {
        return addRecord(inventoryId, taskId, opinion, AsInventoryApprove.ACTION_APPROVED);
    }

    @Override
    public boolean addApprovedRecord(Long inventoryId, List<Long> taskIds, String opinion) {
        return addRecord(inventoryId, taskIds, opinion, AsInventoryApprove.ACTION_APPROVED);
    }

    @Override
    public boolean addRejectedRecord(Long inventoryId, Long taskId, String opinion) {
        return addRecord(inventoryId, taskId, opinion, AsInventoryApprove.ACTION_REJECTED);
    }

    @Override
    public boolean addRejectedRecord(Long inventoryId, List<Long> taskIds, String opinion) {
        return addRecord(inventoryId, taskIds, opinion, AsInventoryApprove.ACTION_REJECTED);
    }

    @Override
    public boolean addRecord(Long inventoryId, Long taskId, String opinion, Integer action) {
        return this.save(
                new AsInventoryApprove()
                        .setAction(action)
                        .setInventoryId(inventoryId)
                        .setTaskId(taskId)
                        .setOpinion(opinion)
                        .setCreateBy(LoginUserThreadLocal.getCurrentUserId()));
    }

    @Override
    public boolean addRecord(Long inventoryId, List<Long> taskIds, String opinion, Integer action) {
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        return this.saveBatch(taskIds.stream().map(taskId ->
                new AsInventoryApprove()
                        .setAction(action)
                        .setInventoryId(inventoryId)
                        .setTaskId(taskId)
                        .setOpinion(opinion)
                        .setCreateBy(currentUserId))
                .collect(Collectors.toList()));
    }

    /**
     * 审核记录
     *
     * @param inventoryId 盘点单id
     * @param taskId     任务id
     * @return 是否成功
     */
    @Override
    public List<InventoryApproveDto> getList(Long inventoryId, Long taskId) {
        // 获取盘点任务的盘点人
        AsInventoryTask inventoryTask = inventoryTaskService.getById(taskId);
        if (null == inventoryTask) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }

        // 获取盘点任务的审核人
        AsInventory inventory = inventoryService.getById(inventoryId);
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }

        List<AsInventoryApprove> inventoryApproveList = this.list(
                Wrappers.<AsInventoryApprove>lambdaQuery()
                        .eq(AsInventoryApprove::getInventoryId, inventoryId)
                        .eq(AsInventoryApprove::getTaskId, taskId)
                        .orderByDesc(AsInventoryApprove::getCreateTime));

        List<InventoryApproveDto> list = Lists.newArrayList();
        if (CollUtil.isNotEmpty(inventoryApproveList)) {
            for (AsInventoryApprove asInventoryApprove : inventoryApproveList) {
                InventoryApproveDto inventoryApproveDto = new InventoryApproveDto();
                BeanUtil.copyProperties(asInventoryApprove, inventoryApproveDto);
                // 字段新补充，历史如果没有记录createBy，按照历史逻辑
                if (inventoryApproveDto.getCreateBy() == null) {
                    if (InventoryConstant.SUBMIT.equals(inventoryApproveDto.getAction())) {
                        if (CollUtil.isNotEmpty(inventoryTask.getInventoryUsers())) {
                            inventoryApproveDto.setCreateBy(inventoryTask.getInventoryUsers().get(0));
                        } else {
                            inventoryApproveDto.setCreateBy(LoginUserThreadLocal.getCurrentUserId());
                        }
                    } else {
                        inventoryApproveDto.setCreateBy(inventory.getApprover());
                    }
                }
                inventoryApproveDto.setCreateUserText(
                        cacheResourceUtil.getUserNameAndCode(inventoryApproveDto.getCreateBy()));
                list.add(inventoryApproveDto);
            }
        }
        return list;
    }
}
