package com.niimbot.asset.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.mapper.AsInventoryAssetMapper;
import com.niimbot.asset.inventory.mapstruct.InventoryHandleRecordMapStruct;
import com.niimbot.asset.inventory.model.*;
import com.niimbot.asset.inventory.service.*;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.means.service.StandardService;
import com.niimbot.dynamicform.ValidatorModel;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetAddLogDto;
import com.niimbot.system.StoreMode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <p>
 * 盘点资产 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Slf4j
@Service
public class AsInventoryAssetServiceImpl extends ServiceImpl<AsInventoryAssetMapper, AsInventoryAsset> implements AsInventoryAssetService {

    protected static final String USE_PERSON = "usePerson";
    protected static final String USE_ORG = "useOrg";
    private static final String STANDARD_ID = "standardId";
    protected static final String ASSET_PHOTO = "assetPhoto";
    private static final List<Integer> SUBMIT_STATUS = ListUtil.of(InventoryConstant.IN_PROGRESS, InventoryConstant.REJECTED);

    @Resource
    private AsInventorySurplusService inventorySurplusService;

    @Resource
    private AsInventoryAssetReportService inventoryAssetReportService;

    @Resource
    private AsInventoryService inventoryService;

    @Resource
    private AsInventoryTaskAssetService inventoryTaskAssetService;

    @Resource
    private AsInventoryAssetLogService inventoryAssetLogService;

    @Resource
    private AsInventoryHandleRecordService inventoryHandleRecordService;

    @Resource
    private AssetService assetService;

    @Resource
    private AsAssetLogService assetLogService;

    @Resource
    private AsFormService formService;

    @Resource
    private StandardService standardService;

    @Resource
    private AssetUtil assetUtil;

    @Resource
    private AsInventoryAssetReportService assetReportService;

    @Resource
    private MySqlInventoryAssetQueryConditionResolver conditionResolver;

    @Resource
    private InventoryHandleRecordMapStruct inventoryHandleRecordMapStruct;

    @Resource
    private AsInventorySettingService settingService;


    @Resource
    private AsInventoryConfigService inventoryConfigService;

    /**
     * 盘点资产头部统计
     *
     * @param dto
     * @return 结果
     */
    @Override
    public InventoryAssetCountDto assetCount(InventoryAssetCountQueryDto dto) {
        Integer taskType = 1;
        Long taskId = dto.getTaskId();
        if (ObjectUtil.isNotNull(taskId)) {
            AsInventoryTask task = Db.getById(taskId, AsInventoryTask.class);
            if (null == task) {
                throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
            }
            taskType = task.getTaskType();
        }

        dto.setTaskType(taskType);

        // 获取未盘已盘数量
        InventoryAssetCountDto inventoryAssetCountDto;
        if (dto.getTaskId() == null) {
            inventoryAssetCountDto = this.baseMapper.assetCount(dto);
        } else {
            inventoryAssetCountDto = this.baseMapper.assetCount2(dto);
        }
        // 获取盘盈数量
        int checkedAddInNum = inventorySurplusService.countAddAsset(dto.setAssetMark(1));
        int checkedAddNotInNum = inventorySurplusService.countAddAsset(dto.setAssetMark(2));
        inventoryAssetCountDto.setCheckedAddInMarkNum(checkedAddInNum);
        inventoryAssetCountDto.setCheckedAddNotInMarkNum(checkedAddNotInNum);
        return inventoryAssetCountDto;
    }

    /**
     * 盘点资产盘亏处理
     *
     * @param pDto 盘亏DTO
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InventoryAssetHandleDto assetHandle(InventoryAssetHandleDto pDto) {
        // 单据类型
        Integer orderType = pDto.getOrderDto().getOrderType();
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        InventoryAssetHandleDto tmpInventoryAssetLossOutput;
        InventoryAssetOrderDto tmpInventoryAssetOrderOutput;
        List<InventoryAssetOrderAssetDto> tmpInventoryAssetOrderAssetArrayOutput;

        String resultMessage = "";
        List<Long> invertortyAssetIds;
        AsInventory tmpInventory;
        AsInventoryAsset tmpInventoryAsset;
        List<InventoryAssetListDto> inventoryAssetList;
        InventoryAssetQueryDto tmpInventoryAssetQuery = new InventoryAssetQueryDto();

        invertortyAssetIds = pDto.getOrderDto().getInventoryassets().stream().map(InventoryAssetOrderAssetDto::getId).collect(toList());
        tmpInventoryAssetQuery.setIds(invertortyAssetIds);
        inventoryAssetList = this.baseMapper.selectAssetList(tmpInventoryAssetQuery);

        if (inventoryAssetList.size() > 0) {
            tmpInventory = inventoryService.getById(inventoryAssetList.get(0).getInventoryId());
        } else {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }
        if (tmpInventory == null) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        try {
            if (!InventoryConstant.COMPLETED.equals(tmpInventory.getStatus())) {
                throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_STATUS_ERROR);
            }
        } catch (NullPointerException tmpExcept) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_STATUS_ERROR);
        }

        // 创建人才能进行损益处理
        /*if (!currentUserId.equals(tmpInventory.getCreateBy())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
        }*/

        tmpInventoryAssetLossOutput = pDto;
        tmpInventoryAssetOrderOutput = pDto.getOrderDto();
        tmpInventoryAssetOrderAssetArrayOutput = new ArrayList<>();
        List<AsInventoryAsset> updateInventoryAssetList = new ArrayList<>();
        InventoryAssetError inventoryAssetError = new InventoryAssetError();
        for (InventoryAssetListDto rowInventoryAsset : inventoryAssetList) {
            if (rowInventoryAsset.getIsDelete()) {
                inventoryAssetError.getDelete().add(rowInventoryAsset.getAssetCode());
                continue;
            }
            if (InventoryConstant.PROCESSED.equals(rowInventoryAsset.getHandleStatus())) {
                inventoryAssetError.getHandled().add(rowInventoryAsset.getAssetCode());
                continue;
            }

            if (AssetConstant.ASSET_STATUS_SERVICE.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getUnderRepair().add(rowInventoryAsset.getAssetCode());
            } else if (AssetConstant.ASSET_STATUS_CHECK.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getInApproval().add(rowInventoryAsset.getAssetCode());
            } else if (AssetConstant.ASSET_STATUS_WAIT_SERVICE.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getWaitRepair().add(rowInventoryAsset.getAssetCode());
            } else if (AssetConstant.ASSET_STATUS_HANDLE.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getDisposed().add(rowInventoryAsset.getAssetCode());
                // 已处置单据
                if (orderType.equals(AssetConstant.ORDER_TYPE_DISPOSE)) {
                    AsInventoryAsset tmpInventoryAsset1 = new AsInventoryAsset();
                    tmpInventoryAsset1.setId(rowInventoryAsset.getId());
                    tmpInventoryAsset1.setHandleStatus(InventoryConstant.PROCESSED);
                    tmpInventoryAsset1.setHandleType(InventoryConstant.HANDLE_TYPE_NOT_DEAL);
                    tmpInventoryAsset1.setHandleUser(currentUserId);
                    tmpInventoryAsset1.setHandleTime(LocalDateTime.now());
                    this.baseMapper.updateById(tmpInventoryAsset1);
                }
            } else {
                for (InventoryAssetOrderAssetDto rowAssetOrderAsset : pDto.getOrderDto().getInventoryassets()) {
                    if (rowAssetOrderAsset.getId().equals(rowInventoryAsset.getId())) {
                        rowAssetOrderAsset.setAssetId(rowInventoryAsset.getAssetId());
                        tmpInventoryAssetOrderAssetArrayOutput.add(rowAssetOrderAsset);
                    }
                }
                tmpInventoryAsset = new AsInventoryAsset();
                tmpInventoryAsset.setId(rowInventoryAsset.getId());
                tmpInventoryAsset.setHandleStatus(InventoryConstant.PROCESSED);
                tmpInventoryAsset.setHandleType(InventoryConstant.HANDLE_TYPE_DEAL);
                tmpInventoryAsset.setHandleUser(currentUserId);
                tmpInventoryAsset.setHandleTime(LocalDateTime.now());
                updateInventoryAssetList.add(tmpInventoryAsset);
            }
        }
        if (inventoryAssetError.getHandled().size() > 0) {
            resultMessage += inventoryAssetError.getHandleMsg();
        }
        if (inventoryAssetError.getUnderRepair().size() > 0) {
            resultMessage += inventoryAssetError.getUnderRepairMsg();
        }
        if (inventoryAssetError.getInApproval().size() > 0) {
            resultMessage += inventoryAssetError.getInApprovalMsg();
        }
        if (inventoryAssetError.getWaitRepair().size() > 0) {
            resultMessage += inventoryAssetError.getWaitRepairMsg();
        }
        if (inventoryAssetError.getDisposed().size() > 0) {
            resultMessage += inventoryAssetError.getDisposedMsg(orderType);
        }
        if (inventoryAssetError.getDelete().size() > 0) {
            resultMessage += inventoryAssetError.getDeleteMsg();
        }

        tmpInventoryAssetOrderOutput.setInventoryassets(tmpInventoryAssetOrderAssetArrayOutput);
        tmpInventoryAssetOrderOutput.setAssetNum(tmpInventoryAssetOrderAssetArrayOutput.size());
        tmpInventoryAssetOrderOutput.setOrderType(orderType);
        tmpInventoryAssetLossOutput.setOrderDto(tmpInventoryAssetOrderOutput);

        // 没有错误则保存
        if (StrUtil.isBlank(resultMessage)) {
            List<AsInventoryHandleRecord> handleRecordList = tmpInventoryAssetOrderAssetArrayOutput.stream().map(f -> {
                AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord();
                handleRecord.setInventoryAssetId(f.getId())
                        .setInventoryId(tmpInventory.getId())
                        .setBizType(2)
                        .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                        .setRelevance(tmpInventoryAssetOrderOutput.getOrderNo())
                        .setHandleTime(LocalDateTime.now());
                if (orderType == AssetConstant.ORDER_TYPE_DISPOSE) {
                    handleRecord.setHandleResult("盘亏处置");
                } else if (orderType == AssetConstant.ORDER_TYPE_CHANGE) {
                    handleRecord.setHandleResult("资产变更");
                }
                return handleRecord;
            }).collect(toList());
            updateBatchById(updateInventoryAssetList);
            inventoryHandleRecordService.saveBatch(handleRecordList);
            inventoryService.handledInventory(tmpInventory.getId());
        } else {
            throw new BusinessException(InventoryResultCode.INVENTORY_LOSS_FAIL, resultMessage);
        }
        return tmpInventoryAssetLossOutput;
    }

    @Transactional(rollbackFor = Exception.class)
    public void assetChangeRollback(InventoryAssetHandleDto pDto) {
        inventoryHandleRecordService.remove(Wrappers.lambdaQuery(AsInventoryHandleRecord.class)
                .eq(AsInventoryHandleRecord::getBizType, 2)
                .eq(AsInventoryHandleRecord::getHandleUser, LoginUserThreadLocal.getCurrentUserId())
                .eq(AsInventoryHandleRecord::getRelevance, pDto.getOrderDto().getOrderNo()));
        List<Long> invertortyAssetIds = pDto.getOrderDto().getInventoryassets().stream().map(InventoryAssetOrderAssetDto::getId).collect(toList());
        InventoryAssetQueryDto tmpInventoryAssetQuery = new InventoryAssetQueryDto();
        tmpInventoryAssetQuery.setIds(invertortyAssetIds);
        List<InventoryAssetListDto> inventoryAssetList = this.baseMapper.selectAssetList(tmpInventoryAssetQuery);
        if (!inventoryAssetList.isEmpty()) {
            inventoryAssetList.forEach(f ->
                    update(Wrappers.lambdaUpdate(AsInventoryAsset.class)
                            .set(AsInventoryAsset::getHandleStatus, InventoryConstant.UNPROCESSED)
                            .set(AsInventoryAsset::getHandleType, null)
                            .set(AsInventoryAsset::getHandleUser, null)
                            .set(AsInventoryAsset::getHandleTime, null)
                            .eq(AsInventoryAsset::getId, f.getId()))
            );
            inventoryService.updateById(new AsInventory().setId(inventoryAssetList.get(0).getInventoryId())
                    .setStatus(InventoryConstant.COMPLETED));
        }
    }

    @Getter
    private static class InventoryAssetError {

        List<String> handled = new ArrayList<>();
        List<String> underRepair = new ArrayList<>();
        List<String> inApproval = new ArrayList<>();
        List<String> waitRepair = new ArrayList<>();
        List<String> disposed = new ArrayList<>();
        List<String> delete = new ArrayList<>();

        public String getHandleMsg() {
            return "资产编码：" + CollUtil.join(this.handled, "，") + "已处理，无需重复处理;\n";
        }

        public String getUnderRepairMsg() {
            return "资产编码：" + CollUtil.join(this.underRepair, "，") + "维修中，无法处理;\n";
        }

        public String getInApprovalMsg() {
            return "资产编码：" + CollUtil.join(this.inApproval, "，") + "审核中，无法处理;\n";
        }

        public String getWaitRepairMsg() {
            return "资产编码：" + CollUtil.join(this.waitRepair, "，") + "待维修，无法处理;\n";
        }

        public String getDisposedMsg(Integer orderType) {
            if (AssetConstant.ORDER_TYPE_DISPOSE == orderType) {
                return "资产编码：" + CollUtil.join(this.disposed, "，") + "已被处置，系统更新为已处理;\n";
            } else {
                return "资产编码：" + CollUtil.join(this.disposed, "，") + "已被处置，无法处理;\n";
            }
        }

        public String getDeleteMsg() {
            return "资产编码：" + CollUtil.join(this.delete, "，") + "已被删除;\n";
        }

    }

    /**
     * PC盘点资产盘亏处理-校验资产是否能盘亏处理
     *
     * @param ids 盘点资产ids
     * @return 结果
     */
    @Override
    public Boolean checkAssetHandle(List<Long> ids, Integer orderType) {
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        InventoryAssetQueryDto tmpInventoryAssetQuery = new InventoryAssetQueryDto();
        tmpInventoryAssetQuery.setIds(ids);
        List<InventoryAssetListDto> inventoryAssetList = this.baseMapper.selectAssetList(tmpInventoryAssetQuery);

        if (CollUtil.isEmpty(inventoryAssetList)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        // 获取盘点单id
        Long inventoryId = inventoryAssetList.get(0).getInventoryId();
        AsInventory tmpInventory = inventoryService.getById(inventoryId);
        if (null == tmpInventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }

        if (!InventoryConstant.COMPLETED.equals(tmpInventory.getStatus())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_STATUS_ERROR);
        }

        // 创建人才能进行损益处理
        /*if (!currentUserId.equals(tmpInventory.getCreateBy())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
        }*/

        String resultMessage = "";
        InventoryAssetError inventoryAssetError = new InventoryAssetError();
        for (InventoryAssetListDto rowInventoryAsset : inventoryAssetList) {
            if (rowInventoryAsset.getIsDelete()) {
                inventoryAssetError.getDelete().add(rowInventoryAsset.getAssetCode());
                continue;
            }
            if (InventoryConstant.PROCESSED.equals(rowInventoryAsset.getHandleStatus())) {
                inventoryAssetError.getHandled().add(rowInventoryAsset.getAssetCode());
                continue;
            }

            if (AssetConstant.ASSET_STATUS_SERVICE.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getUnderRepair().add(rowInventoryAsset.getAssetCode());
            } else if (AssetConstant.ASSET_STATUS_CHECK.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getInApproval().add(rowInventoryAsset.getAssetCode());
            } else if (AssetConstant.ASSET_STATUS_WAIT_SERVICE.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getWaitRepair().add(rowInventoryAsset.getAssetCode());
            } else if (AssetConstant.ASSET_STATUS_HANDLE.equals(rowInventoryAsset.getAssetStatusRealtime())) {
                inventoryAssetError.getDisposed().add(rowInventoryAsset.getAssetCode());
                // 已处置单据
                if (orderType.equals(AssetConstant.ORDER_TYPE_DISPOSE)) {
                    AsInventoryAsset tmpInventoryAsset = new AsInventoryAsset();
                    tmpInventoryAsset.setId(rowInventoryAsset.getId());
                    tmpInventoryAsset.setHandleStatus(InventoryConstant.PROCESSED);
                    tmpInventoryAsset.setHandleType(InventoryConstant.HANDLE_TYPE_NOT_DEAL);
                    tmpInventoryAsset.setHandleUser(currentUserId);
                    tmpInventoryAsset.setHandleTime(LocalDateTime.now());
                    this.baseMapper.updateById(tmpInventoryAsset);
                }
            }
        }
        if (inventoryAssetError.getHandled().size() > 0) {
            resultMessage += inventoryAssetError.getHandleMsg();
        }
        if (inventoryAssetError.getUnderRepair().size() > 0) {
            resultMessage += inventoryAssetError.getUnderRepairMsg();
        }
        if (inventoryAssetError.getInApproval().size() > 0) {
            resultMessage += inventoryAssetError.getInApprovalMsg();
        }
        if (inventoryAssetError.getWaitRepair().size() > 0) {
            resultMessage += inventoryAssetError.getWaitRepairMsg();
        }
        if (inventoryAssetError.getDisposed().size() > 0) {
            resultMessage += inventoryAssetError.getDisposedMsg(orderType);
        }
        if (inventoryAssetError.getDelete().size() > 0) {
            resultMessage += inventoryAssetError.getDeleteMsg();
        }

        if (StrUtil.isNotBlank(resultMessage)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_LOSS_FAIL, resultMessage);
        }
        return true;
    }

    /**
     * 查询资产详情快照
     *
     * @param id 盘点资产表Id
     * @return 资产详情集合
     */
    @Override
    public AsInventoryAsset getAssetDetail(Long id) {
        AsInventoryAsset byId = this.getById(id);
        if (ObjectUtil.isNull(byId)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        return byId;
    }

    /**
     * 盘点资产列表
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @Override
    public List<InventoryAssetListDto> selectAssetListAll(InventorySurplusQueryDto dto) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        List<InventoryAssetListDto> tmpReturnList;
//        if (dto.getTaskId() == null) {
            tmpReturnList = this.baseMapper.selectAppAssetList(dto, conditions);
//        } else {
//            tmpReturnList = this.baseMapper.selectAppAssetList2(dto, conditions);
//        }
        List<InventoryAssetListDto> tmpListHasLog = this.fixListChangeLog(tmpReturnList);
        return tmpListHasLog;
    }

    /**
     * 盘点资产
     *
     * @param dto 查询条件
     * @return 结果集
     */
    @Override
    public IPage<InventoryAssetListDto> selectAppAssetList(InventorySurplusQueryDto dto) {
        // ==============================================盘点单校验====================================================
        // 获取盘点单
        AsInventory byId = Db.getById(dto.getInventoryId(), AsInventory.class);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }

        // ==============================================盘点任务校验====================================================
        // 判断当前任务状态
        if (dto.getTaskId() != null) {
            AsInventoryTask inventoryTask = Db.getById(dto.getTaskId(), AsInventoryTask.class);
            if (ObjectUtil.isNull(inventoryTask)) {
                throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
            }
        }

        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());

        IPage<InventoryAssetListDto> tmpReturnList;
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        tmpReturnList = this.baseMapper.selectAppAssetList(dto.buildIPage(), dto, conditions, currentUserId);
        List<InventoryAssetListDto> tmpList = tmpReturnList.getRecords();
        List<InventoryAssetListDto> tmpListHasLog = this.fixListChangeLog(tmpList);
        tmpReturnList.setRecords(tmpListHasLog);
        return tmpReturnList;
    }

    @Override
    public Map<String, String> inventoryAssetScanRef(Long inventoryId, Long taskId) {
        Map<String, String> scanMap = new ConcurrentHashMap<>();
        FormVO formVO = formService.assetTpl();
        List<String> uniqueCodes = formVO.getFormFields().stream()
                .filter(f -> {
                    if (f.getFieldType().equals(FormFieldCO.YZC_ASSET_SERIALNO)) {
                        return true;
                    }
                    return f.getFieldProps().containsKey("unique") ? f.getFieldProps().getBoolean("unique") : false;
                }).map(FormFieldCO::getFieldCode).collect(toList());
        List<Map<String, Object>> scanList = getBaseMapper().inventoryAssetScanRef(inventoryId, taskId, uniqueCodes, LoginUserThreadLocal.getCompanyId());
        uniqueCodes.add("php_id");
        scanList.forEach(f -> {
            String id = Convert.toStr(f.get("id"));
            uniqueCodes.forEach(uniqueCode -> {
                String data = Convert.toStr(f.get(uniqueCode));
                if (StrUtil.isNotEmpty(data)) {
                    scanMap.put(data, id);
                }
            });
        });
        return scanMap;
    }

    @Override
    public OtherUserInventoryAssetDto otherUser(InventoryAssetOtherUserDto dto) {
        AsInventoryTask task = Db.getById(dto.getTaskId(), AsInventoryTask.class);
        if (null == task) {
            throw new BusinessException(InventoryResultCode.INVENTORY_TASK_NOT_EXIST);
        }
        Long currentInventoryUser = dto.getCurrentInventoryUser();
        if (currentInventoryUser == null) {
            currentInventoryUser = LoginUserThreadLocal.getCurrentUserId();
        }
        String lastQueryTime = null;
        if (dto.getLastQueryTime() != null) {
            lastQueryTime = dto.getLastQueryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        List<InventoryAssetListDto> tmpList  = this.baseMapper.otherUserAssetList(dto.getTaskId(), currentInventoryUser, lastQueryTime);
        List<InventoryAssetListDto> tmpListHasLog = this.fixListChangeLog(tmpList);
        OtherUserInventoryAssetDto res =
                new OtherUserInventoryAssetDto().setAssets(tmpListHasLog);
        Optional<InventoryAssetListDto> max =
                tmpList.stream().max(Comparator.comparing(InventoryAssetListDto::getInventoryTime));
        if (max.isPresent()) {
            res.setLastQueryTime(max.get().getInventoryTime());
        }
        return res;
    }

    /**
     * 损益处理-盘亏资产
     *
     * @param dto 查询条件
     * @return 结果集
     */
    @Override
    public IPage<InventoryAssetListDto> selectPlAssetList(InventorySurplusQueryDto dto) {
        // 获取盘点单详情
        AsInventory byId = inventoryService.getById(dto.getInventoryId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!ListUtil.of(InventoryConstant.COMPLETED, InventoryConstant.HANDLED).contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_COMPLETED_NO_PL);
        }
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        IPage<InventoryAssetListDto> tmpReturnList;
//        if (dto.getTaskId() == null) {
        tmpReturnList = this.baseMapper.selectAppAssetList(dto.buildIPage(), dto, conditions, null);
//        } else {
//            tmpReturnList = this.baseMapper.selectAppAssetList2(dto.buildIPage(), dto, conditions);
//        }
        List<InventoryAssetListDto> tmpList = tmpReturnList.getRecords();
        List<InventoryAssetListDto> tmpListHasLog = this.fixListChangeLog(tmpList);
        tmpReturnList.setRecords(tmpListHasLog);

        return tmpReturnList;
    }

    /**
     * 编辑盘点资产
     *
     * @param dto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editAsset(InventoryAssetEditDto dto) {
        Long id = dto.getId();
        // 获取盘点资产
        AsInventoryAsset inventoryAsset = this.getById(id);
        if (ObjectUtil.isNull(inventoryAsset)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        // 2024年8月6日，查询盘点原始资产信息（如果没有记录，就查询实时资产数据）
        Long assetId = inventoryAsset.getAssetId();
        AsInventoryTaskAsset taskAsset = inventoryTaskAssetService.getInventoryTaskAsset(inventoryAsset.getInventoryId(), inventoryAsset.getInventoryTaskId(), assetId);
        JSONObject beforeAssetData;
        if (CollUtil.isEmpty(taskAsset.getAssetOriginalData())) {
            AsAsset asAsset = assetService.getInfoNoPerm(assetId);
            if (null == asAsset) {
                throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
            }
            beforeAssetData = asAsset.translate();
        } else {
            beforeAssetData = taskAsset.getAssetOriginalData();
        }

        // 清理数据
        FormVO formVO = settingService.inventoryAttrList(1);
        List<String> finalEditCodes = formVO.getFormFields().stream()
                .filter(f -> !FormFieldCO.SPLIT_LINE.equals(f.getFieldType()))
                .filter(f -> !f.isHidden())
                .map(FormFieldCO::getFieldCode).collect(toList());
        dto.getAssetData().keySet().removeIf(key -> !finalEditCodes.contains(key));
        if (CollUtil.isNotEmpty(finalEditCodes)) {
            // 只校验传入的表单属性
            formService.fieldValidatorWithId(new ValidatorModel(assetId, dto.getAssetData()),
                    formVO.getFormFields(), AsFormService.BIZ_TYPE_ASSET);
        }

        // 原始数据
        JSONObject newAssetData = BeanUtil.copyProperties(beforeAssetData, JSONObject.class);

        // 编辑后的新数据
        newAssetData.putAll(dto.getAssetData());
        // 翻译
        FormVO assetTpl = formService.assetTpl();
        List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(assetTpl.getFormFields());
        assetUtil.translateAssetJson(newAssetData, translations);
        assetUtil.translateAssetJson(beforeAssetData, translations);

        // 设置盘点资产数据
        AsInventoryAsset inventoryAssetNew = new AsInventoryAsset();
        inventoryAssetNew.setId(inventoryAsset.getId())
                .setActualInventoryUser(inventoryAsset.getActualInventoryUser())
                .setAssetSnapshotData(newAssetData);
        if (!this.updateById(inventoryAssetNew)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 获取修改数据日志
        List<InventoryAssetLogData> logData = buildAssetLog(beforeAssetData, inventoryAssetNew.getAssetSnapshotData(), assetTpl.getFormFields());

        // 添加资产修改日志
        AsInventoryAssetLog inventoryAssetLog = new AsInventoryAssetLog();
        inventoryAssetLog.setInventoryId(inventoryAsset.getInventoryId()).setAssetId(assetId)
                .setLogData(logData);
        // 首先查询日志是否存在
        AsInventoryAssetLog asInventoryAssetLog = inventoryAssetLogService.getOne(new QueryWrapper<AsInventoryAssetLog>().lambda()
                .eq(AsInventoryAssetLog::getInventoryId, inventoryAsset.getInventoryId())
                .eq(AsInventoryAssetLog::getAssetId, assetId));
        if (ObjectUtil.isNull(asInventoryAssetLog)) {
            // 不存在，则插入记录
            inventoryAssetLogService.save(inventoryAssetLog);
        } else {
            // 存在，则更新记录
            inventoryAssetLog.setId(asInventoryAssetLog.getId());
            inventoryAssetLogService.updateById(inventoryAssetLog);
        }
        inventoryService.handledInventory(inventoryAsset.getInventoryId());
        return true;
    }

    private List<FormFieldCO> preEditHandle(Long id, JSONObject assetData, Long standardId) {
        List<FormFieldCO> allField = new ArrayList<>();
        // 清理额外属性
        FormVO formVO = formService.assetTpl();
        List<FormFieldCO> formFields = formVO.getFormFields();

        List<FormFieldCO> standardExtField = new ArrayList<>();
        Set<String> standardCodeSet = new HashSet<>();
        if (standardId != null) {
            // 标准品
            standardExtField = standardService.getStandardExtField(formVO.getFormId(), standardId);
            standardCodeSet = standardExtField.stream().map(FormFieldCO::getFieldCode).collect(Collectors.toSet());
            formFields.addAll(standardExtField);
        }

        // 清理数据
        Set<String> finalStandardCodeSet = standardCodeSet;
        List<String> finalEditCodes = formFields.stream()
                .filter(f ->
                        !FormFieldCO.SPLIT_LINE.equals(f.getFieldType())
                                && !FormFieldCO.YZC_ASSET_SERIALNO.equals(f.getFieldType()))
                .filter(f -> !f.isHidden())
                .peek(f -> {
                    if (!finalStandardCodeSet.contains(f.getFieldCode())) {
                        allField.add(f);
                    }
                })
                .map(FormFieldCO::getFieldCode).collect(toList());
        assetData.keySet().removeIf(key -> !finalEditCodes.contains(key));

        if (CollUtil.isNotEmpty(allField)) {
            // 只校验传入的表单属性
            try {
                formService.fieldValidatorWithId(new ValidatorModel(id, assetData),
                        allField, AsFormService.BIZ_TYPE_ASSET);
            } catch (BusinessException ex) {
                if (MeansResultCode.DYNAMIC_FORM_ERROR.equals(ex.getResultCode())) {
                    String message = ex.getMessage();
                    throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, message + "，您的资产设置信息发生了变更，请您通过【操作-手动修改】处理该资产信息");
                }
                throw ex;
            } catch (Exception e) {
                throw e;
            }
        }
        if (CollUtil.isNotEmpty(standardExtField)) {
            // 只校验传入的表单属性
            formService.fieldValidator(assetData, standardExtField);
        }

        return allField;
    }

    /**
     * 已修改资产列表分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @Override
    public IPage<InventoryAssetListDto> modifiedPage(InventorySurplusQueryDto dto) {
        // 获取盘点单详情
        AsInventory byId = inventoryService.getById(dto.getInventoryId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!ListUtil.of(InventoryConstant.COMPLETED, InventoryConstant.HANDLED).contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_COMPLETED_NO_PL);
        }
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        IPage<InventoryAssetListDto> tmpReturnList = this.baseMapper.modifiedPage(dto.buildIPage(), dto, conditions);
        List<InventoryAssetListDto> tmpList = tmpReturnList.getRecords();
        List<InventoryAssetListDto> tmpListHasLog = this.fixListChangeLog(tmpList);
        tmpReturnList.setRecords(tmpListHasLog);

        return tmpReturnList;
    }

    @Override
    public IPage<InventoryAssetPcDto> takePhotoPage(InventorySurplusQueryDto dto) {
        // 获取盘点单详情
        AsInventory byId = inventoryService.getById(dto.getInventoryId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!ListUtil.of(InventoryConstant.COMPLETED, InventoryConstant.HANDLED).contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_COMPLETED_NO_PL);
        }
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, dto.getConditions());
        return this.baseMapper.takePhotoPage(dto.buildIPage(), dto, conditions);
    }

    /**
     * 已修改资产列表单据-不分页
     *
     * @param dto dto
     * @return 结果
     */
    @Override
    public List<InventoryAssetListDto> modifiedPageList(InventorySurplusQueryDto dto) {
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        // 获取盘点单详情
        AsInventory byId = inventoryService.getById(dto.getInventoryId());
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!InventoryConstant.COMPLETED.equals(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_COMPLETED_NO_PL);
        }
        // 创建人才能进行损益处理
//        if (!currentUserId.equals(byId.getCreateBy())) {
//            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
//        }

        List<InventoryAssetListDto> tmpList = this.baseMapper.modifiedPageList(dto);
        return this.fixListChangeLog(tmpList);
    }

    /**
     * 忽略修改资产
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @Override
    public Boolean ignoreUpdate(IgnoreUpdateDto dto) {
        Integer type = dto.getType();
        List<Long> ids = dto.getIds();
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        Long inventoryId = null;
        // 忽略类型 1-盘盈不在册，2-盘亏, 3-已修改资产, 4-已拍照
        if (1 == type) {
            inventoryId = ignoreReportAsset(ids, type);
        } else if (2 == type) {
            inventoryId = ignoreInventoryAsset(ids, type);
        } else if (3 == type) {
            inventoryId = ignoreReportAsset(ids, type);
            Long inventoryId2 = ignoreInventoryAsset(ids, type);
            if (inventoryId == null) {
                inventoryId = inventoryId2;
            }
        } else if (4 == type) {
            inventoryId = ignorePhoto(ids, type);
        }

        if (inventoryId != null) {
            inventoryService.handledInventory(inventoryId);
        }
        return true;
    }

    // 忽略类型 3-已修改资产
    private Long ignoreReportAsset(List<Long> ids, Integer bizType) {
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        List<Long> filterIds = inventoryAssetReportService.list(Wrappers.lambdaQuery(AsInventoryAssetReport.class)
                .eq(AsInventoryAssetReport::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(AsInventoryAssetReport::getId, ids)).stream().map(AsInventoryAssetReport::getId).collect(toList());
        if (filterIds.size() == 0) {
            return null;
        }

        List<AsInventorySurplus> surpluses = inventorySurplusService.list(Wrappers.lambdaQuery(AsInventorySurplus.class)
                .in(AsInventorySurplus::getReportId, filterIds));
        Set<Long> inventoryIdSet = surpluses.stream().map(AsInventorySurplus::getInventoryId).collect(Collectors.toSet());
        if (inventoryIdSet.size() == 0) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (inventoryIdSet.size() > 1) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "数据属于多个盘点单");
        }

        Long inventoryId = new ArrayList<>(inventoryIdSet).get(0);

        // 查询未处理的
        List<Long> unHandleIds = inventoryAssetReportService.unHandleIds(filterIds);
        Set<Long> hasHandleLog = inventoryHandleRecordService.hasHandleReportRecord(unHandleIds, bizType);

        List<AsInventoryAssetReport> updateInventoryAssetReport = new ArrayList<>();
        List<AsInventoryHandleRecord> saveHandleRecord = new ArrayList<>();

        filterIds.forEach(id -> {
            // 修改盘盈资产为已处理
            if (unHandleIds.contains(id)) {
                AsInventoryAssetReport newInventoryAssetReport = new AsInventoryAssetReport();
                newInventoryAssetReport.setId(id).setHandleStatus(InventoryConstant.PROCESSED)
                        .setHandleUser(currentUserId)
                        .setHandleTime(LocalDateTime.now())
                        .setHandleType(InventoryConstant.HANDLE_TYPE_IGNORE);
                updateInventoryAssetReport.add(newInventoryAssetReport);
            }
            // 没处理过的
            if (!hasHandleLog.contains(id)) {
                // 添加记录
                AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord();
                handleRecord.setInventoryAssetReportId(id)
                        .setInventoryId(inventoryId)
                        .setHandleResult("已忽略")
                        .setBizType(bizType)
                        .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                        .setHandleTime(LocalDateTime.now());
                saveHandleRecord.add(handleRecord);
            }
        });
        inventoryAssetReportService.updateBatchById(updateInventoryAssetReport);
        inventoryHandleRecordService.saveBatch(saveHandleRecord);
        return inventoryId;
    }

    // 忽略类型 3-已修改资产
    private Long ignoreInventoryAsset(List<Long> ids, Integer bizType) {
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        List<AsInventoryAsset> list = list(Wrappers.lambdaQuery(AsInventoryAsset.class)
                .in(AsInventoryAsset::getId, ids));

        Map<Long, AsInventoryAsset> filterMap = list.stream().collect(Collectors.toMap(AsInventoryAsset::getId, k -> k));
//        List<Long> filterIds = list.stream().map(AsInventoryAsset::getId).collect(toList());
        if (filterMap.size() == 0) {
            return null;
        }

        Set<Long> inventoryIdSet = list.stream().map(AsInventoryAsset::getInventoryId).collect(Collectors.toSet());
        if (inventoryIdSet.size() == 0) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (inventoryIdSet.size() > 1) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "数据属于多个盘点单");
        }
        Long inventoryId = new ArrayList<>(inventoryIdSet).get(0);
        // 查询未处理的
        List<Long> unHandleIds = getBaseMapper().unHandleIds(LoginUserThreadLocal.getCompanyId(), new ArrayList<>(filterMap.keySet()));
        Set<Long> hasHandleLog = inventoryHandleRecordService.hasHandleInventoryRecord(unHandleIds, bizType);

        List<AsInventoryAsset> updateInventoryAsset = new ArrayList<>();
        List<AsInventoryHandleRecord> saveHandleRecord = new ArrayList<>();

        filterMap.forEach((id, data) -> {
            // 修改盘盈资产为已处理
            if (unHandleIds.contains(id)) {
                AsInventoryAsset newInventoryAsset = new AsInventoryAsset();
                newInventoryAsset.setId(id).setHandleStatus(InventoryConstant.PROCESSED)
                        .setActualInventoryUser(data.getActualInventoryUser())
                        .setHandleUser(currentUserId)
                        .setHandleTime(LocalDateTime.now())
                        .setHandleType(InventoryConstant.HANDLE_TYPE_ASSET_IGNORE);
                updateInventoryAsset.add(newInventoryAsset);
            }
            // 没处理过的
            if (!hasHandleLog.contains(id)) {
                // 添加记录
                AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord();
                handleRecord.setInventoryAssetId(id)
                        .setInventoryId(inventoryId)
                        .setHandleResult("已忽略")
                        .setBizType(bizType)
                        .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                        .setHandleTime(LocalDateTime.now());
                saveHandleRecord.add(handleRecord);
            }
        });
        updateBatchById(updateInventoryAsset);
        inventoryHandleRecordService.saveBatch(saveHandleRecord);
        return inventoryId;
    }

    // 忽略类型 4-已拍照
    private Long ignorePhoto(List<Long> ids, Integer bizType) {
        List<AsInventoryAsset> list = list(Wrappers.lambdaQuery(AsInventoryAsset.class)
                .in(AsInventoryAsset::getId, ids));

        List<Long> filterIds = list.stream().map(AsInventoryAsset::getId).collect(toList());
        if (filterIds.size() == 0) {
            return null;
        }
        Set<Long> inventoryIdSet = list.stream().map(AsInventoryAsset::getInventoryId).collect(Collectors.toSet());
        if (inventoryIdSet.size() == 0) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (inventoryIdSet.size() > 1) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "数据属于多个盘点单");
        }
        Long inventoryId = new ArrayList<>(inventoryIdSet).get(0);

        Set<Long> hasHandleLog = inventoryHandleRecordService.hasHandleInventoryRecord(filterIds, bizType);
        List<AsInventoryHandleRecord> saveList = filterIds.stream()
                .filter(f -> !hasHandleLog.contains(f))
                .map(f -> {
                    AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord();
                    return handleRecord.setInventoryAssetId(f)
                            .setInventoryId(inventoryId)
                            .setHandleResult("已忽略")
                            .setBizType(4)
                            .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                            .setHandleTime(LocalDateTime.now());
                }).collect(toList());
        inventoryHandleRecordService.saveBatch(saveList);
        return inventoryId;
    }

    @Override
    public List<InventoryAssetLogData> buildAssetLog(JSONObject before, JSONObject after, List<FormFieldCO> formFields) {
        List<InventoryAssetLogData> list = Lists.newArrayList();
        for (FormFieldCO formField : formFields) {
            String fieldCode = formField.getFieldCode();
            String fieldName = formField.getFieldName();
            String fieldType = formField.getFieldType();
            // 过滤分割线和隐藏的属性
            if (formField.isHidden() || FormFieldCO.SPLIT_LINE.equals(fieldType)) {
                continue;
            }
            // 原始值
            Object beforeObject = before.get(fieldCode);
            String beforeValue;
            if (ListUtil.of(FormFieldCO.FILES, FormFieldCO.IMAGES, FormFieldCO.MULTI_SELECT_DROPDOWN)
                    .contains(formField.getFieldType())) {
                if (beforeObject instanceof List) {
                    beforeValue = Convert.toStr(before.getJSONArray(fieldCode), "空");
                } else {
                    beforeValue = "[]";
                }
            } else {
                beforeValue = StrUtil.isNotBlank(before.getString(fieldCode)) ? before.getString(fieldCode) : "空";
            }

            // 新值
            Object afterObject = after.get(fieldCode);
            String afterValue;
            if (ListUtil.of(FormFieldCO.FILES, FormFieldCO.IMAGES, FormFieldCO.MULTI_SELECT_DROPDOWN)
                    .contains(formField.getFieldType())) {
                if (afterObject instanceof List) {
                    afterValue = Convert.toStr(after.getJSONArray(fieldCode), "空");
                } else {
                    afterValue = "[]";
                }
            } else {
                afterValue = StrUtil.isNotBlank(after.getString(fieldCode)) ? after.getString(fieldCode) : "空";
            }
            if (!StrUtil.equals(afterValue, beforeValue)) {
                InventoryAssetLogData inventoryAssetLogData = new InventoryAssetLogData();
                switch (fieldType) {
                    case FormFieldCO.IMAGES:
                    case FormFieldCO.FILES:
                    case FormFieldCO.MULTI_SELECT_DROPDOWN:
                        inventoryAssetLogData.setCode(fieldCode)
                                .setName(fieldName);
                        if (beforeObject == null) {
                            inventoryAssetLogData.setOriginalValue("[]");
                        } else if (beforeObject instanceof List) {
                            inventoryAssetLogData.setOriginalValue(JSONObject.toJSONString(beforeObject));
                        } else {
                            inventoryAssetLogData.setOriginalValue("[]");
                        }
                        if (afterObject == null) {
                            inventoryAssetLogData.setFinalValue("[]");
                        } else if (afterObject instanceof List) {
                            inventoryAssetLogData.setFinalValue(JSONObject.toJSONString(afterObject));
                        } else {
                            inventoryAssetLogData.setFinalValue("[]");
                        }
                        list.add(inventoryAssetLogData);
                        break;
                    default:
                        if (before.containsKey(fieldCode + "Text")) {
                            beforeValue = Convert.toStr(before.get(fieldCode + "Text"));
                        }
                        if (after.containsKey(fieldCode + "Text")) {
                            afterValue = Convert.toStr(after.get(fieldCode + "Text"));
                        }
                        inventoryAssetLogData.setCode(fieldCode)
                                .setName(fieldName)
                                .setOriginalValue(beforeValue)
                                .setFinalValue(afterValue);
                        list.add(inventoryAssetLogData);
                        break;
                }

            }
        }
        return list;
    }

    protected List<InventoryAssetListDto> fixListChangeLog(List<InventoryAssetListDto> pDto) {
        if (CollUtil.isEmpty(pDto)) {
            return pDto;
        }
        Map<String, AsInventoryAssetLog> tmpListLog = new HashMap<>();
        Map<Long, List<InventoryAssetListDto>> inventoryGroup = pDto.stream().collect(Collectors.groupingBy(InventoryAssetListDto::getInventoryId));
        inventoryGroup.forEach((inventoryId, list) -> {
            if (CollUtil.isNotEmpty(list)) {
                List<Long> assetIds = list.stream().map(InventoryAssetListDto::getAssetId).collect(toList());
                List<AsInventoryAssetLog> logList = inventoryAssetLogService.list(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                        .eq(AsInventoryAssetLog::getInventoryId, inventoryId)
                        .in(AsInventoryAssetLog::getAssetId, assetIds));
                for (AsInventoryAssetLog assetLog : logList) {
                    tmpListLog.put(assetLog.getInventoryId() + "_" + assetLog.getAssetId(), assetLog);
                }
            }
        });

        //匹配写入
        for (InventoryAssetListDto rowDto : pDto) {
            AsInventoryAssetLog assetLog = tmpListLog.get(rowDto.getInventoryId() + "_" + rowDto.getAssetId());
            if (assetLog != null) {
                JSONObject tmpJsonOrigin = new JSONObject();
                JSONObject tmpJsonChanged = new JSONObject();
                for (InventoryAssetLogData rowDtoLogData : assetLog.getLogData()) {
                    tmpJsonOrigin.put(rowDtoLogData.getCode(), rowDtoLogData.getOriginalValue());
                    tmpJsonChanged.put(rowDtoLogData.getCode(), rowDtoLogData.getFinalValue());
                }
                rowDto.setAssetChangRecordOrigen(tmpJsonOrigin.toJSONString());
                rowDto.setAssetChangRecord(tmpJsonChanged.toJSONString());
            } else {
                rowDto.setAssetChangRecordOrigen("{}");
                rowDto.setAssetChangRecord("{}");
            }
        }
        return pDto;
    }

    /**
     * 修改资产 type 1-盘盈资产修改，2-已修改资产
     *
     * @return 分页数据结构
     */
    @Transactional(rollbackFor = Exception.class)
    public AsInventory plUpdateAssetCommon(Long id, Integer type, int handleType, String modifyText) {
        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord()
                .setBizType(3)
                .setRelevance(modifyText)
                .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                .setHandleTime(LocalDateTime.now());
        if (handleType == InventoryConstant.HANDLE_TYPE_ASSET_UPDATE) {
            handleRecord.setHandleResult("手动修改");
        } else if (handleType == InventoryConstant.HANDLE_TYPE_ASSET_UPDATE_USER) {
            handleRecord.setHandleResult("确认直接修改");
        }
        AsInventory inventory;
        if (1 == type) {
            // 盘盈资产修改
            handleRecord.setInventoryAssetReportId(id);
            // 查询盘盈资产是否存在
            AsInventorySurplus inventorySurplus = inventorySurplusService.getOne(new QueryWrapper<AsInventorySurplus>().lambda()
                    .eq(AsInventorySurplus::getReportId, id));
            if (null == inventorySurplus) {
                throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
            }
            AsInventoryAssetReport inventoryAssetReport = inventoryAssetReportService.getById(id);
            if (null == inventoryAssetReport) {
                throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
            }
            if (InventoryConstant.ASSET_MARK_NOT_ON.equals(inventoryAssetReport.getAssetMark())) {
                throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_MARK_ON);
            }
            if (InventoryConstant.PROCESSED.equals(inventoryAssetReport.getHandleStatus())) {
                throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_HANDLE_PROCESSED);
            }

            //判断盘点单是否已完成
            inventory = inventoryService.getById(inventorySurplus.getInventoryId());
            if (null == inventory) {
                throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
            }
            if (!InventoryConstant.COMPLETED.equals(inventory.getStatus())) {
                throw new BusinessException(InventoryResultCode.NOT_COMPLETED_UPDATE);
            }
            handleRecord.setInventoryId(inventory.getId());

            // 修改盘盈资产为已处理
            AsInventoryAssetReport newInventoryAssetReport = new AsInventoryAssetReport();
            newInventoryAssetReport.setId(id).setHandleStatus(InventoryConstant.PROCESSED)
                    .setHandleUser(currentUserId)
                    .setHandleTime(LocalDateTime.now())
                    .setHandleType(InventoryConstant.HANDLE_TYPE_EDIT);
            if (!inventoryAssetReportService.updateById(newInventoryAssetReport)) {
                throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
            }
            inventoryService.handledInventory(inventory.getId());
        } else {
            // 已修改资产忽略
            handleRecord.setInventoryAssetId(id);
            // 查询盘盈资产是否存在
            AsInventoryAsset inventoryAsset = this.getById(id);
            if (null == inventoryAsset) {
                throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
            }
            if (InventoryConstant.PROCESSED.equals(inventoryAsset.getHandleStatus())) {
                throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_HANDLE_PROCESSED);
            }

            //判断盘点单是否已完成
            inventory = inventoryService.getById(inventoryAsset.getInventoryId());
            if (null == inventory) {
                throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
            }
            if (!InventoryConstant.COMPLETED.equals(inventory.getStatus())) {
                throw new BusinessException(InventoryResultCode.NOT_COMPLETED_IGNORE);
            }
            handleRecord.setInventoryId(inventory.getId());
            // 修改盘点资产为已处理
            AsInventoryAsset newInventoryAsset = new AsInventoryAsset();
            newInventoryAsset.setId(id).setHandleStatus(InventoryConstant.PROCESSED)
                    .setActualInventoryUser(inventoryAsset.getActualInventoryUser())
                    .setHandleUser(currentUserId)
                    .setHandleTime(LocalDateTime.now())
                    .setHandleType(handleType);
            if (!this.updateById(newInventoryAsset)) {
                throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
            }
            inventoryService.handledInventory(inventory.getId());
        }
        inventoryHandleRecordService.save(handleRecord);
        return inventory;
    }

    /**
     * 修改资产
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean plUpdateAsset(AssetUpdateDto dto) {
        AsAsset asset = new AsAsset();
        Long standardId = dto.getAssetData().getLong(STANDARD_ID);
        asset.setId(dto.getAssetId())
                .setAssetData(dto.getAssetData())
                .setStandardId(standardId);

        List<FormFieldCO> formFieldCOS = preEditHandle(asset.getId(), asset.getAssetData(), standardId);

        // 判断是否库里存在
        AsAsset originalAsset = assetService.getById(asset.getId());
        asset.setStandardId(asset.getAssetData().getLong(STANDARD_ID));
        if (ObjectUtil.isNull(originalAsset)) {
            throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
        }

        // 原始数据
        JSONObject assetData = originalAsset.getAssetData();
        JSONObject beforeAssetData = BeanUtil.copyProperties(assetData, JSONObject.class);
        assetData.putAll(asset.getAssetData());

        // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
        if (StrUtil.isNotEmpty(assetData.getString(USE_ORG)) ||
                StrUtil.isNotEmpty(assetData.getString(USE_PERSON))) {
            if (AssetConstant.ASSET_STATUS_IDLE.equals(originalAsset.getStatus())) {
                originalAsset.setStatus(AssetConstant.ASSET_STATUS_USING);
            }
        } else {
            if (AssetConstant.ASSET_STATUS_USING.equals(originalAsset.getStatus()) ||
                    AssetConstant.ASSET_STATUS_BORROW.equals(originalAsset.getStatus())) {
                originalAsset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
            }
        }

        originalAsset.setStandardId(asset.getStandardId());
        // 更新数据
        if (!assetService.updateById(originalAsset)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 记录履历
        List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formFieldCOS);
        assetUtil.translateAssetJsonView(beforeAssetData, translations);
        assetUtil.translateAssetJsonView(assetData, translations);
        String text = assetService.buildAssetLog(beforeAssetData, assetData, formFieldCOS);

        // 写入资产履历
        AsAssetLog asAssetLog = new AsAssetLog()
                .setAssetId(asset.getId())
                .setActionType(AssetConstant.OPT_EDIT)
                .setActionName("盘点修正")
                .setHandleTime(LocalDateTimeUtil.now())
                .setActionContent(text)
                .setOriginalData(beforeAssetData)
                .setChangeData(assetData);
        AsInventory inventory = this.plUpdateAssetCommon(dto.getId(), dto.getType(), InventoryConstant.HANDLE_TYPE_ASSET_UPDATE, text);
        if (inventory != null) {
            asAssetLog.setOrderNo(inventory.getInventoryNo())
                    .setOrderId(inventory.getId());
        }
        return assetLogService.save(asAssetLog);
    }

    /**
     * 修改资产
     *
     * @param dto 盘点资产DTO
     * @return 分页数据结构
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean plUpdateAssetByUser(List<PlUpdateAssetByUserDto> dto) {
        FormVO formVO = formService.assetTpl();
        Map<String, String> formFieldMap = formVO.getFormFields().stream().collect(toMap(FormFieldCO::getFieldCode, FormBaseFieldCO::getFieldType));
        // 临时循环处理，这里需要优化
        for (PlUpdateAssetByUserDto plUpdateAssetByUserDto : dto) {
            Long assetId;
            JSONObject assetData;
            AsInventoryAssetLog inventoryAssetLog;
            if (1 == plUpdateAssetByUserDto.getType()) {
                AsInventoryAssetReport assetDto = assetReportService.getById(plUpdateAssetByUserDto.getId());
                if (ObjectUtil.isNull(assetDto)) {
                    throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
                }
                if (!InventoryConstant.ASSET_MARK_ON.equals(assetDto.getAssetMark())) {
                    throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_MARK_ON);
                }
                assetId = assetDto.getAssetId();
                assetData = assetDto.getAssetData();
                // 查询盘盈资产是否存在
                AsInventorySurplus inventorySurplus = inventorySurplusService.getOne(new QueryWrapper<AsInventorySurplus>().lambda()
                        .eq(AsInventorySurplus::getReportId, plUpdateAssetByUserDto.getId()));
                if (null == inventorySurplus) {
                    throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
                }
                inventoryAssetLog = inventoryAssetLogService.getOne(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                        .eq(AsInventoryAssetLog::getInventoryId, inventorySurplus.getInventoryId())
                        .eq(AsInventoryAssetLog::getAssetId, assetId));
            } else {
                AsInventoryAsset assetDto = this.getById(plUpdateAssetByUserDto.getId());
                if (ObjectUtil.isNull(assetDto)) {
                    throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
                }
                assetId = assetDto.getAssetId();
                assetData = assetDto.getAssetSnapshotData();
                inventoryAssetLog = inventoryAssetLogService.getOne(Wrappers.lambdaQuery(AsInventoryAssetLog.class)
                        .eq(AsInventoryAssetLog::getInventoryId, assetDto.getInventoryId())
                        .eq(AsInventoryAssetLog::getAssetId, assetId));
            }

            AsAsset asset = new AsAsset();
            asset.setId(assetId)
                    .setAssetData(assetData);

            // 判断是否库里存在
            AsAsset originalAsset = assetService.getById(asset.getId());
            if (ObjectUtil.isNull(originalAsset)) {
                throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
            }

            // 原始数据
            JSONObject oldAssetAssetData = originalAsset.getAssetData();
            JSONObject newAssetData = BeanUtil.copyProperties(oldAssetAssetData, JSONObject.class);

            // 如果更新日志不为空，只更新日志内的数据
            if (inventoryAssetLog != null) {
                List<String> updateFieldCodes = inventoryAssetLog.getLogData().stream().map(InventoryAssetLogData::getCode).collect(toList());
                for (String updateFieldCode : updateFieldCodes) {
                    if (formFieldMap.containsKey(updateFieldCode)) {
                        if (assetData.containsKey(updateFieldCode)) {
                            newAssetData.put(updateFieldCode, assetData.get(updateFieldCode));
                        } else {
                            String fieldType = formFieldMap.get(updateFieldCode);
                            if (fieldType.equals(FormFieldCO.MULTI_SELECT_DROPDOWN) ||
                                    fieldType.equals(FormFieldCO.IMAGES) ||
                                    fieldType.equals(FormFieldCO.FILES)) {
                                newAssetData.put(updateFieldCode, new ArrayList<>());
                            } else if (fieldType.equals(FormFieldCO.NUMBER_INPUT)) {
                                newAssetData.remove(updateFieldCode);
                            } else {
                                newAssetData.put(updateFieldCode, "");
                            }
                        }
                    }
                }
            } else {
                newAssetData.putAll(assetData);
            }

            originalAsset.setAssetData(newAssetData);
            // 使用原始标准品校验更新后资产Json
            Long standardId = originalAsset.getStandardId();
            List<FormFieldCO> formFieldCOS = preEditHandle(originalAsset.getId(), BeanUtil.copyProperties(newAssetData, JSONObject.class), standardId);

            // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
            if (StrUtil.isNotEmpty(newAssetData.getString(USE_ORG)) ||
                    StrUtil.isNotEmpty(newAssetData.getString(USE_PERSON))) {
                if (AssetConstant.ASSET_STATUS_IDLE.equals(originalAsset.getStatus())) {
                    originalAsset.setStatus(AssetConstant.ASSET_STATUS_USING);
                }
            } else {
                if (AssetConstant.ASSET_STATUS_USING.equals(originalAsset.getStatus()) ||
                        AssetConstant.ASSET_STATUS_BORROW.equals(originalAsset.getStatus())) {
                    originalAsset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
                }
            }

            // 更新数据
            if (!assetService.updateById(originalAsset)) {
                throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
            }

            // 记录履历
            List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formFieldCOS);
            assetUtil.translateAssetJsonView(oldAssetAssetData, translations);
            assetUtil.translateAssetJsonView(newAssetData, translations);
            String text = assetService.buildAssetLog(oldAssetAssetData, newAssetData, formFieldCOS);

            // 写入资产履历
            AsAssetLog asAssetLog = new AsAssetLog()
                    .setAssetId(asset.getId())
                    .setActionType(AssetConstant.OPT_EDIT)
                    .setActionName("盘点修正")
                    .setHandleTime(LocalDateTimeUtil.now())
                    .setActionContent(text)
                    .setOriginalData(oldAssetAssetData)
                    .setChangeData(newAssetData);
            AsInventory inventory = this.plUpdateAssetCommon(plUpdateAssetByUserDto.getId(), plUpdateAssetByUserDto.getType(), InventoryConstant.HANDLE_TYPE_ASSET_UPDATE_USER, text);
            if (inventory != null) {
                asAssetLog.setOrderNo(inventory.getInventoryNo())
                        .setOrderId(inventory.getId());
            }
            assetLogService.save(asAssetLog);
        }
        return true;
    }

    /**
     * 盘盈资产新增入库
     *
     * @param asset 资产数据
     * @return 资产ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long plAddAsset(AssetAddDto asset) {
        Long reportId = asset.getId();
        // 查询盘盈资产是否存在
        AsInventorySurplus inventorySurplus = inventorySurplusService.getOne(new QueryWrapper<AsInventorySurplus>().lambda()
                .eq(AsInventorySurplus::getReportId, reportId));
        if (null == inventorySurplus) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        AsInventoryAssetReport inventoryAssetReport = inventoryAssetReportService.getById(reportId);
        if (null == inventoryAssetReport) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }
        if (InventoryConstant.ASSET_MARK_ON.equals(inventoryAssetReport.getAssetMark())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_MARK_ON);
        }
        if (InventoryConstant.PROCESSED.equals(inventoryAssetReport.getHandleStatus())) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_HANDLE_PROCESSED);
        }

        //判断盘点单是否已完成
        AsInventory inventory = inventoryService.getById(inventorySurplus.getInventoryId());
        if (null == inventory) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!InventoryConstant.COMPLETED.equals(inventory.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_COMPLETED_NO_PL);
        }

        Long standardId = asset.getAssetData().getLong("standardId");
        AsAsset asAsset = new AsAsset();
        asAsset.setStandardId(standardId);
        asAsset.setAssetData(asset.getAssetData());
        assetService.preAddHandle(ListUtil.of(asAsset), standardId);
        assetService.add(asAsset, StoreMode.INVENTORY, inventory.getId());
        assetService.addLog(asAsset.getId(), asAsset.getAssetData(), new AssetAddLogDto().setActionContent("盘盈资产新增入库"));

        // 回写资产编码
        JSONObject reportAssetData = inventoryAssetReport.getAssetData();
        String assetCode = Convert.toStr(asAsset.getAssetData().get("assetCode"));
        reportAssetData.put("assetCode", assetCode);

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();

        // 修改盘盈资产为已处理
        AsInventoryAssetReport newInventoryAssetReport = new AsInventoryAssetReport();
        newInventoryAssetReport.setId(reportId).setHandleStatus(InventoryConstant.PROCESSED)
                .setAssetData(reportAssetData)
                .setHandleUser(currentUserId)
                .setHandleTime(LocalDateTime.now())
                .setHandleType(InventoryConstant.HANDLE_TYPE_ADD)
                .setAssetId(asAsset.getId());
        if (!inventoryAssetReportService.updateById(newInventoryAssetReport)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        // 写入盘点记录
        AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord();
        handleRecord.setInventoryAssetReportId(reportId)
                .setInventoryId(inventory.getId())
                .setHandleResult("新增入库")
                .setRelevance(assetCode)
                .setBizType(1)
                .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                .setHandleTime(LocalDateTime.now());
        inventoryHandleRecordService.save(handleRecord);
        // 处理盘点单
        inventoryService.handledInventory(inventory.getId());
        return asAsset.getId();
    }

    /**
     * 判断资产id是否在盘点单内
     *
     * @param assetId     盘点资产id
     * @param inventoryId 盘点单id
     * @return 结果
     */
    @Override
    public Boolean checkAssetId(Long assetId, Long inventoryId) {
        return this.getBaseMapper().checkAssetId(assetId, inventoryId);
    }

    @Override
    public List<Long> listAssetIds(List<Long> inventoryIds, Integer checked) {
        return this.getBaseMapper().listAssetIds(inventoryIds, checked);
    }

    @Override
    public List<AsInventoryAsset> listByTaskId(Long taskId) {
        return this.list(
                Wrappers.<AsInventoryAsset>lambdaQuery()
                        .eq(AsInventoryAsset::getInventoryTaskId, taskId));
    }

    @Override
    public List<AsInventoryAsset> getInventoryAssets(Long inventoryId, Collection<Long> assetIds) {
        return this.list(
                Wrappers.<AsInventoryAsset>lambdaQuery()
                        .eq(AsInventoryAsset::getInventoryId, inventoryId)
                        .in(AsInventoryAsset::getAssetId, assetIds));
    }

    @Override
    public int getUnhandleNum(Long inventoryId) {
        return this.getBaseMapper().getUnhandleNum(inventoryId);
    }

    @Override
    public int getUnhandleNum2(Long inventoryId) {
        return this.getBaseMapper().getUnhandleNum2(inventoryId);
    }

    @Override
    public int getUnhandlePhotoNum(Long inventoryId) {
        return this.getBaseMapper().getUnhandlePhotoNum(inventoryId);
    }

    @Override
    public Boolean addPhoto(PlPhotoDto dto) {
        Set<Long> hasHandleSet = inventoryHandleRecordService.hasHandleInventoryRecord(dto.getId(), 4);
        if (CollUtil.isEmpty(hasHandleSet)) {
            List<AsInventoryAsset> inventoryAssetList = listByIds(dto.getId());
            if (CollUtil.isEmpty(inventoryAssetList)) {
                throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
            }
            Map<Long, AsInventoryAsset> inventoryAssetMap = inventoryAssetList.stream().collect(Collectors.toMap(AsInventoryAsset::getAssetId, k -> k));
            // 判断是否库里存在
            List<AsAsset> originalAssetList = assetService.listByIds(new ArrayList<>(inventoryAssetMap.keySet()));
            if (ObjectUtil.isNull(originalAssetList)) {
                throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
            }
            Map<Long, AsAsset> originalAssetMap = originalAssetList.stream().collect(Collectors.toMap(AsAsset::getId, k -> k));
            FormVO formVO = formService.assetTpl();
            List<AsAssetLog> assetLogList = new ArrayList<>();
            List<AsInventoryHandleRecord> inventoryHandleRecordList = new ArrayList<>();
            Set<Long> inventoryIds = new HashSet<>();
            Map<Long, String> inventoryIdNoMap = new HashMap<>();
            for (AsInventoryAsset inventoryAsset : inventoryAssetList) {
                if (StrUtil.isNotEmpty(inventoryAsset.getPictureUrl())) {
                    // 查询盘点名称
                    if (!inventoryAssetMap.containsKey(inventoryAsset.getInventoryId())) {
                        AsInventory inventory = inventoryService.getOne(Wrappers.lambdaQuery(AsInventory.class).select(AsInventory::getInventoryNo).eq(AsInventory::getId, inventoryAsset.getInventoryId()));
                        inventoryIdNoMap.put(inventoryAsset.getInventoryId(), inventory != null ? inventory.getInventoryNo() : StrUtil.EMPTY);
                    }

                    FormFieldCO fieldCO = formVO.getFormFields().stream().filter(f -> ASSET_PHOTO.equals(f.getFieldCode()))
                            .findFirst().orElseThrow(() -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "图片属性assetPhoto不存在"));
                    // 最大图片数量
                    Integer max = fieldCO.getFieldProps().getInteger("max");
                    AsAsset originalAsset = originalAssetMap.get(inventoryAsset.getAssetId());
                    if (originalAsset != null) {
                        JSONObject oriAssetData = originalAsset.getAssetData();
                        List<String> assetPhotos = Convert.toList(String.class, oriAssetData.get(ASSET_PHOTO));
                        if (assetPhotos.size() >= max) {
                            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前资产照片上传已达上限，请选择替换已有照片");
                        }
                        assetPhotos.add(inventoryAsset.getPictureUrl());
                        JSONObject newAssetData = BeanUtil.copyProperties(originalAsset.getAssetData(), JSONObject.class);
                        newAssetData.put(ASSET_PHOTO, assetPhotos);
                        assetService.update(Wrappers.lambdaUpdate(AsAsset.class)
                                .set(AsAsset::getAssetData, newAssetData.toJSONString())
                                .eq(AsAsset::getId, inventoryAsset.getAssetId()));
                        // 写入资产履历
                        AsAssetLog asAssetLog = new AsAssetLog()
                                .setAssetId(inventoryAsset.getAssetId())
                                .setActionType(AssetConstant.OPT_EDIT)
                                .setActionName("盘点修正")
                                .setHandleTime(LocalDateTimeUtil.now())
                                .setActionContent("图片被修改")
                                .setOriginalData(oriAssetData)
                                .setChangeData(newAssetData)
                                .setOrderId(inventoryAsset.getInventoryId())
                                .setOrderNo(inventoryIdNoMap.get(inventoryAsset.getInventoryId()));
                        assetLogList.add(asAssetLog);
                    }
                }

                AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord();
                handleRecord.setInventoryAssetId(inventoryAsset.getId())
                        .setInventoryId(inventoryAsset.getInventoryId())
                        .setHandleResult("添加已有资产照片")
                        .setBizType(4)
                        .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                        .setHandleTime(LocalDateTime.now());
                inventoryHandleRecordList.add(handleRecord);
                inventoryIds.add(inventoryAsset.getInventoryId());
            }
            assetLogService.saveBatch(assetLogList);
            inventoryHandleRecordService.saveBatch(inventoryHandleRecordList);
            for (Long inventoryId : inventoryIds) {
                inventoryService.handledInventory(inventoryId);
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean replacePhoto(PlPhotoDto dto) {
        Set<Long> hasHandleSet = inventoryHandleRecordService.hasHandleInventoryRecord(dto.getId(), 4);
        if (CollUtil.isEmpty(hasHandleSet)) {
            List<AsInventoryAsset> inventoryAssetList = listByIds(dto.getId());
            if (CollUtil.isEmpty(inventoryAssetList)) {
                throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
            }
            Map<Long, AsInventoryAsset> inventoryAssetMap = inventoryAssetList.stream().collect(Collectors.toMap(AsInventoryAsset::getAssetId, k -> k));
            // 判断是否库里存在
            List<AsAsset> originalAssetList = assetService.listByIds(new ArrayList<>(inventoryAssetMap.keySet()));
            if (ObjectUtil.isNull(originalAssetList)) {
                throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
            }
            Map<Long, AsAsset> originalAssetMap = originalAssetList.stream().collect(Collectors.toMap(AsAsset::getId, k -> k));
            List<AsAssetLog> assetLogList = new ArrayList<>();
            List<AsInventoryHandleRecord> inventoryHandleRecordList = new ArrayList<>();
            Set<Long> inventoryIds = new HashSet<>();
            Map<Long, String> inventoryIdNoMap = new HashMap<>();
            for (AsInventoryAsset inventoryAsset : inventoryAssetList) {
                if (StrUtil.isNotEmpty(inventoryAsset.getPictureUrl())) {
                    // 查询盘点名称
                    if (!inventoryAssetMap.containsKey(inventoryAsset.getInventoryId())) {
                        AsInventory inventory = inventoryService.getOne(Wrappers.lambdaQuery(AsInventory.class).select(AsInventory::getInventoryNo).eq(AsInventory::getId, inventoryAsset.getInventoryId()));
                        inventoryIdNoMap.put(inventoryAsset.getInventoryId(), inventory != null ? inventory.getInventoryNo() : StrUtil.EMPTY);
                    }
                    AsAsset originalAsset = originalAssetMap.get(inventoryAsset.getAssetId());
                    if (originalAsset != null) {
                        JSONObject oriAssetData = originalAsset.getAssetData();
                        JSONObject newAssetData = BeanUtil.copyProperties(originalAsset.getAssetData(), JSONObject.class);
                        newAssetData.put(ASSET_PHOTO, ListUtil.of(inventoryAsset.getPictureUrl()));
                        assetService.update(Wrappers.lambdaUpdate(AsAsset.class)
                                .set(AsAsset::getAssetData, newAssetData.toJSONString())
                                .eq(AsAsset::getId, inventoryAsset.getAssetId()));
                        // 写入资产履历
                        AsAssetLog asAssetLog = new AsAssetLog()
                                .setAssetId(inventoryAsset.getAssetId())
                                .setActionType(AssetConstant.OPT_EDIT)
                                .setActionName("盘点修正")
                                .setHandleTime(LocalDateTimeUtil.now())
                                .setActionContent("图片被修改")
                                .setOriginalData(oriAssetData)
                                .setChangeData(newAssetData)
                                .setOrderId(inventoryAsset.getInventoryId())
                                .setOrderNo(inventoryIdNoMap.get(inventoryAsset.getInventoryId()));
                        assetLogList.add(asAssetLog);
                    }
                }

                AsInventoryHandleRecord handleRecord = new AsInventoryHandleRecord();
                handleRecord.setInventoryAssetId(inventoryAsset.getId())
                        .setInventoryId(inventoryAsset.getInventoryId())
                        .setHandleResult("替换已有资产照片")
                        .setBizType(4)
                        .setHandleUser(LoginUserThreadLocal.getCurrentUserId())
                        .setHandleTime(LocalDateTime.now());
                inventoryHandleRecordList.add(handleRecord);
                inventoryIds.add(inventoryAsset.getInventoryId());
            }
            assetLogService.saveBatch(assetLogList);
            inventoryHandleRecordService.saveBatch(inventoryHandleRecordList);
            for (Long inventoryId : inventoryIds) {
                inventoryService.handledInventory(inventoryId);
            }
            return true;
        }
        return false;
    }

    @Override
    public InventoryHandleLogDto handleLog(InventoryHandleLogQry qry) {
        AsInventoryHandleRecord one = inventoryHandleRecordService.getOne(Wrappers.lambdaQuery(AsInventoryHandleRecord.class)
                        .eq(AsInventoryHandleRecord::getBizType, qry.getType())
                        .eq(AsInventoryHandleRecord::getInventoryAssetId, qry.getId())
                        .or()
                        .eq(AsInventoryHandleRecord::getInventoryAssetReportId, qry.getId()),
                false);
        return inventoryHandleRecordMapStruct.toDTO(one);
    }

    @Override
    public List<InventoryHandleLogDto> handleLogList(Long inventoryId) {
        List<AsInventoryHandleRecord> list = inventoryHandleRecordService.list(Wrappers.lambdaQuery(AsInventoryHandleRecord.class)
                .eq(AsInventoryHandleRecord::getInventoryId, inventoryId));
        return inventoryHandleRecordMapStruct.toDTO(list);
    }

    @Override
    public Boolean updateRemark(InventoryRemarkDto dto) {
        // 修改盘点备注
        LambdaUpdateWrapper<AsInventoryAsset> updateWrapper = Wrappers.lambdaUpdate(AsInventoryAsset.class)
                .set(AsInventoryAsset::getRemark, dto.getRemark())
                .set(AsInventoryAsset::getInventoryTime, LocalDateTime.now())
                .eq(AsInventoryAsset::getInventoryId, dto.getInventoryId())
                .eq(AsInventoryAsset::getAssetId, dto.getAssetId());
        return updateAsset(dto.getInventoryId(), dto.getAssetId(), updateWrapper);
    }

    @Override
    public Boolean updatePicture(InventoryPictureDto dto) {
        // 修改盘点照片
        LambdaUpdateWrapper<AsInventoryAsset> updateWrapper = Wrappers.lambdaUpdate(AsInventoryAsset.class)
                .set(AsInventoryAsset::getPictureUrl, dto.getPictureUrl())
                .set(AsInventoryAsset::getInventoryTime, LocalDateTime.now())
                .eq(AsInventoryAsset::getInventoryId, dto.getInventoryId())
                .eq(AsInventoryAsset::getAssetId, dto.getAssetId());
        return updateAsset(dto.getInventoryId(), dto.getAssetId(), updateWrapper);
    }

    private Boolean updateAsset(Long inventoryId, Long assetId, LambdaUpdateWrapper<AsInventoryAsset> updateWrapper) {
        // 获取盘点单
        AsInventory byId = inventoryService.getById(inventoryId);
        if (null == byId) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
        }
        if (!SUBMIT_STATUS.contains(byId.getStatus())) {
            throw new BusinessException(InventoryResultCode.NOT_PROGRESS_SUBMIT);
        }

        // 查询盘点配置
        AsInventoryConfig inventoryConfig = inventoryConfigService.getById(inventoryId);
        if (null == inventoryConfig) {
            throw new BusinessException(InventoryResultCode.INVENTORY_CONFIG_NOT_EXIST);
        }

        // 获取盘点资产
        AsInventoryAsset inventoryAsset = getOne(new QueryWrapper<AsInventoryAsset>().lambda()
                .eq(AsInventoryAsset::getInventoryId, inventoryId)
                .eq(AsInventoryAsset::getAssetId, assetId));
        if (ObjectUtil.isNull(inventoryAsset)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_ASSET_NOT_EXIST);
        }

        // 获取当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 判断当前用户是否是盘点人
        List<Long> inventoryUser = inventoryAsset.getInventoryUser();
        if (!inventoryUser.contains(currentUserId)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_NOT_POWER_ACTION);
        }

        if (!update(updateWrapper)) {
            throw new BusinessException(InventoryResultCode.INVENTORY_SUBMIT_FAIL);
        }
        return true;
    }


}
