package com.niimbot.asset.inventory.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.domain.event.TaskSubmitEvent;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.service.InventoryTodoService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.todo.enums.TodoBizType;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.AsTodoService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.todo.TodoCompleteMessageDto;
import com.niimbot.todo.TodoCreateMessageDto;
import com.niimbot.todo.TodoDeleteMessageDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/15 8:54
 */
@Service
public class
InventoryTodoServiceImpl implements InventoryTodoService {

    @Resource
    private AsTodoService todoService;

    @Resource
    private AsCusEmployeeService cusEmployeeService;

    @Override
    public void sendInventoryTaskTodoMsg(List<AsInventoryTask> tasks, AsInventory inventory) {
        if (CollUtil.isEmpty(tasks) || ObjectUtil.isEmpty(inventory)) {
            return;
        }

        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 获取用户名称
        AsCusEmployee employee = cusEmployeeService.getById(currentUserId);
        // 获取审核人名称
        AsCusEmployee approverEmp = cusEmployeeService.getById(inventory.getApprover());
        if (approverEmp == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "审核人不存在，请联系管理员更换审核人");
        }
        for (AsInventoryTask inventoryTask : tasks) {
            String title = employee.getEmpName() + "发起的盘点任务";
            String summary = "任务名称：" + inventoryTask.getName()
                    + "/r/n盘点审核人：" + approverEmp.getEmpName();

            List<Long> inventoryUsers = inventoryTask.getInventoryUsers();
            for (Long inventoryUser : inventoryUsers) {
                //创建盘点待办，给具体盘点人 生成待办任务，所以这里类型是工作节点待办
                TodoCreateMessageDto messageDto = new TodoCreateMessageDto()
                        .setCompanyId(companyId)
                        .setBusinessId(inventoryTask.getId())
                        .setBizType(TodoBizType.TASK.getBizType())
                        .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY)
                        .setTitle(title)
                        .setOrderData(summary)
                        .setCreateBy(currentUserId)
                        .setHandleUser(inventoryUser);

                // 发送待办
                todoService.createTodo(messageDto);
            }
        }
    }

    /**
     * 给盘点任务指定人发送待办消息
     *
     * @param tasks
     * @param inventory
     * @param sendMsgUsers
     */
    @Override
    public void sendInventoryTaskTodoMsg(List<AsInventoryTask> tasks, AsInventory inventory, Set<Long> sendMsgUsers) {
        if (CollUtil.isEmpty(tasks) || ObjectUtil.isEmpty(inventory) || CollUtil.isEmpty(sendMsgUsers)) {
            return;
        }

        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 获取用户名称
        AsCusEmployee employee = cusEmployeeService.getById(currentUserId);
        // 获取审核人名称
        AsCusEmployee approverEmp = cusEmployeeService.getById(inventory.getApprover());
        if (approverEmp == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "审核人不存在，请联系管理员更换审核人");
        }
        for (AsInventoryTask inventoryTask : tasks) {
            String title = employee.getEmpName() + "发起的盘点任务";
            String summary = "任务名称：" + inventoryTask.getName()
                    + "/r/n盘点审核人：" + approverEmp.getEmpName();

            List<Long> inventoryUsers = inventoryTask.getInventoryUsers();
            for (Long inventoryUser : inventoryUsers) {
                if (sendMsgUsers.contains(inventoryUser)) {
                    //创建盘点待办，给具体盘点人 生成待办任务，所以这里类型是工作节点待办
                    TodoCreateMessageDto messageDto = new TodoCreateMessageDto()
                            .setCompanyId(companyId)
                            .setBusinessId(inventoryTask.getId())
                            .setBizType(TodoBizType.TASK.getBizType())
                            .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY)
                            .setTitle(title)
                            .setOrderData(summary)
                            .setCreateBy(currentUserId)
                            .setHandleUser(inventoryUser);

                    // 发送待办
                    todoService.createTodo(messageDto);
                }
            }
        }
    }


    /**
     * 给审核人发送代办
     *
     * @param inventory 盘点单
     */
    @Override
    public void sendInventoryApproveTodoMsg(AsInventory inventory, AsInventoryTask inventoryTask) {
        if (ObjectUtil.isEmpty(inventory) || ObjectUtil.isEmpty(inventoryTask)) {
            return;
        }

        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 获取用户名称
        AsCusEmployee employee = cusEmployeeService.getById(currentUserId);
        // 获取审核人名称
        AsCusEmployee approverEmp = cusEmployeeService.getById(inventory.getApprover());
        if (approverEmp == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "审核人不存在，请联系管理员更换审核人");
        }
        String summary = "盘点单名称：" + inventory.getName() + "/r/n";
        summary += "盘点审核人：" + approverEmp.getEmpName();

        String title = employee.getEmpName() + "发起的资产盘点审核任务";
        //盘点完成，给具体盘点审核人 生成待办任务，所以这里类型是工作节点待办
        TodoCreateMessageDto messageDto = new TodoCreateMessageDto()
                .setCompanyId(companyId)
                .setBusinessId(inventory.getId())
                .setSubBusinessId(inventoryTask.getId())
                .setBizType(TodoBizType.TASK.getBizType())
                .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE)
                .setTitle(title)
                .setOrderData(summary)
                .setCreateBy(currentUserId)
                .setHandleUser(inventory.getApprover());

        // 发送待办
        todoService.createTodo(messageDto);
        // 酷应用发送
        EventPublishHandler.publish(new TaskSubmitEvent(companyId, inventory.getId(),
                inventoryTask.getId()));
    }


    @Override
    public void sendInventoryApproveTodoMsg(AsInventory inventory, List<AsTodo> todoList) {
        if (ObjectUtil.isEmpty(inventory) || CollUtil.isEmpty(todoList)) {
            return;
        }

        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 获取审核人名称
        AsCusEmployee approverEmp = cusEmployeeService.getById(inventory.getApprover());
        if (approverEmp == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "审核人不存在，请联系管理员更换审核人");
        }
        String summary = "盘点单名称：" + inventory.getName() + "/r/n";
        summary += "盘点审核人：" + approverEmp.getEmpName();

        for (AsTodo asTodo : todoList) {
            //盘点审核转移，给具体盘点审核人 生成待办任务，所以这里类型是工作节点待办
            TodoCreateMessageDto messageDto = new TodoCreateMessageDto()
                    .setCompanyId(companyId)
                    .setBusinessId(inventory.getId())
                    .setBizType(TodoBizType.TASK.getBizType())
                    .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE)
                    .setTitle(asTodo.getTitle())
                    .setOrderData(summary)
                    .setCreateBy(currentUserId)
                    .setHandleUser(inventory.getApprover());

            // 发送待办
            todoService.createTodo(messageDto);
        }
    }

    @Override
    public void updateInventoryApproveTodoMsg(AsInventory inventory, Long taskId) {
        if (ObjectUtil.isEmpty(inventory)) {
            return;
        }
        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        TodoCompleteMessageDto messageDto = new TodoCompleteMessageDto()
                .setCompanyId(companyId)
                .setBusinessId(inventory.getId())
                .setSubBusinessId(taskId)
                .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE)
                .setHandleUser(inventory.getApprover());
        // 发送待办消息
        todoService.completeTodo(messageDto);
    }

    @Override
    public void updateInventoryTaskTodoMsg(AsInventoryTask task) {
        if (ObjectUtil.isEmpty(task)) {
            return;
        }

        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        TodoCompleteMessageDto messageDto = new TodoCompleteMessageDto()
                .setCompanyId(companyId)
                .setBusinessId(task.getId())
                .setCompleteType(TodoCompleteMessageDto.ALL)
                .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY);
        // 发送待办消息
        todoService.completeTodo(messageDto);
    }

    @Override
    public void deleteInventoryTodoMsg(List<Long> taskIds, Long inventoryId) {
        if (CollUtil.isEmpty(taskIds) || taskIds.size() <= 0) {
            return;
        }

        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        for (Long taskId : taskIds) {
            TodoDeleteMessageDto messageDto = new TodoDeleteMessageDto()
                    .setCompanyId(companyId)
                    .setBusinessId(taskId)
                    .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY);
            // 发送待办消息
            todoService.deleteTodo(messageDto);
        }

        // 删除审核人代办
        TodoDeleteMessageDto messageDto = new TodoDeleteMessageDto()
                .setCompanyId(companyId)
                .setBusinessId(inventoryId)
                .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE);
        // 发送待办消息
        todoService.deleteTodo(messageDto);
    }

    @Override
    public void deleteInventoryTaskTodoMsg(AsInventoryTask task) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        TodoDeleteMessageDto messageDto = new TodoDeleteMessageDto()
                .setCompanyId(companyId)
                .setBusinessId(task.getId())
                .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY);

        // 发送待办
        todoService.doDeleteTodo(messageDto);
    }


    @Override
    public void deleteInventoryApproveTodoMsg(Long inventoryId, Long originApprover) {
        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 删除审核人代办
        TodoDeleteMessageDto messageDto = new TodoDeleteMessageDto()
                .setCompanyId(companyId)
                .setBusinessId(inventoryId)
                .setOrderType((short) AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE)
                .setHandleUser(originApprover);
        // 发送待办消息
        todoService.deleteTodo(messageDto);
    }

}
