package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/3/3 13:47
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_inventory_handle_record", autoResultMap = true)
public class AsInventoryHandleRecord {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "盘点单资产ID [as_inventory_asset的id]")
    private Long inventoryAssetId;

    private Long inventoryId;

    @ApiModelProperty(value = "盘点资产上报ID [as_inventory_asset_report的id]")
    private Long inventoryAssetReportId;

    @ApiModelProperty(value = "1-盘盈不在册，2-盘亏资产，3-已修改资产，4-已拍照资产")
    private Integer bizType;

    @ApiModelProperty(value = "处理结果")
    private String handleResult;

    @ApiModelProperty(value = "关联数据")
    private String relevance;

    @ApiModelProperty(value = "处理人")
    private Long handleUser;

    @ApiModelProperty(value = "处理时间")
    private LocalDateTime handleTime;

}
