package com.niimbot.asset.inventory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.inventory.service.AsInventorySyncService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.inventory.InventoryManualDto;
import com.niimbot.inventory.InventoryQueryDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySyncAddAndSubmitDto;
import com.niimbot.inventory.InventorySyncAssetDto;
import com.niimbot.inventory.InventorySyncAssetResultDto;
import com.niimbot.inventory.InventoryTaskAddUserDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.inventory.InventoryTaskInfoDto;
import com.niimbot.inventory.InventoryTaskListDto;
import com.niimbot.inventory.InventoryTaskUpdateUserDto;
import com.niimbot.inventory.InventoryTaskUpdateUserInfoDto;
import com.niimbot.inventory.InventoryTaskView;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * 盘点任务 前端控制器
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@RestController
@RequestMapping("server/inventory/task")
@RequiredArgsConstructor
public class AsInventoryTaskServiceController {

    private static final String SYNC_INVENTORY_TASK_LOCK = "SYNC_INVENTORY_TASK_LOCK";
    private final AsInventoryTaskService inventoryTaskService;
    private final AsInventorySyncService inventorySyncService;
    private final RedissonClient redissonClient;

    /**
     * 盘点任务分页列表
     *
     * @param dto
     * @return
     */
    @GetMapping(value = "/pageApp")
    public IPage<InventoryTaskListDto> taskPageApp(InventoryQueryDto dto) {
        return inventoryTaskService.selectListApp(dto);
    }

    /**
     * 盘点任务分页列表
     *
     * @param dto
     * @return
     */
    @GetMapping(value = "/page")
    public IPage<InventoryTaskListDto> taskPage(InventoryQueryDto dto) {
        return inventoryTaskService.selectList(dto);
    }

    /**
     * 获取子任务详情
     *
     * @param id id
     * @return 结果
     */
    @GetMapping(value = "/{id}")
    public InventoryTaskListDto getById(@PathVariable("id") Long id) {
        IPage<InventoryTaskListDto> page =
                inventoryTaskService.selectListApp(new InventoryQueryDto().setTaskId(id));
        if (CollUtil.isEmpty(page.getRecords())) {
            return new InventoryTaskListDto();
        }
        return page.getRecords().get(0);
    }

    /**
     * 提交审核
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping(value = "/submit")
    public Boolean submit(@RequestBody InventoryTaskApproveDto approveDto) {
        return inventoryTaskService.submit(approveDto);
    }

    /**
     * 手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    @PutMapping(value = "/manualInventory")
    public Boolean manualInventory(@RequestBody InventoryManualDto dto) {
        return inventoryTaskService.manualInventory(dto);
    }

    /**
     * 资产上报
     *
     * @param dto
     * @return 结果
     */
    @PostMapping(value = "/reportAsset")
    public Boolean reportAsset(@RequestBody InventorySurplusDto dto) {
        return inventoryTaskService.reportAsset(dto);
    }

    /**
     * 编辑上报资产
     *
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "/reportAsset")
    public Boolean editReportAsset(@RequestBody InventorySurplusDto dto) {
        return inventoryTaskService.editReportAsset(dto);
    }

    /**
     * 删除上报资产
     *
     * @param id
     * @return 结果
     */
    @DeleteMapping(value = "/reportAsset/{id}")
    public Boolean removeReportAsset(@PathVariable("id") Long id) {
        return inventoryTaskService.removeReportAsset(id);
    }

    /**
     * 批量删除上报资产信息
     *
     * @param ids 资产IDs
     * @return 结果
     */
    @DeleteMapping(value = "/reportAsset")
    public Boolean removeReportAssets(@RequestBody List<Long> ids) {
        return inventoryTaskService.removeReportAssets(ids);
    }

    /**
     * 同步上传盘点任务资产数据
     *
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "/syncUploadPdTaskAsset/{version}")
    public InventorySyncAssetResultDto syncUploadPdTaskAsset(@PathVariable("version") String version,
                                                             @RequestBody InventorySyncAssetDto dto) {
        // 相同盘点任务新增锁
        RLock lock = redissonClient.getLock(SYNC_INVENTORY_TASK_LOCK + ":" + dto.getTaskId());
        try {
            if (lock.tryLock(50, TimeUnit.SECONDS)) {
                return inventorySyncService.syncUploadTaskAsset(dto, version);
            } else {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点任务被其他同步占用中，请稍后重试");
            }
        } catch (InterruptedException e) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点任务被其他同步占用中，请稍后重试");
        } finally {
            lock.unlock();
        }
    }


    /**
     * 盘点任务详情
     *
     * @param id 盘点任务id
     * @return 结果
     */
    @GetMapping(value = "/info/{id}")
    public InventoryTaskInfoDto getInfo(@PathVariable("id") Long id) {
        return inventoryTaskService.getInfo(id);
    }

    /**
     * 同步新增资产+提交审核
     *
     * @param dto 盘盈资产数据
     * @return 结果
     */
    @PostMapping(value = "/syncAddAndSubmit")
    public Boolean syncAddAndSubmit(@RequestBody InventorySyncAddAndSubmitDto dto) {
        return inventoryTaskService.syncAddAndSubmit(dto);
    }

    @GetMapping("/checkInventory/{userId}")
    public Boolean checkInventory(@PathVariable("userId") Long userId) {
        return inventoryTaskService.checkInventory(userId);
    }

    /**
     * 盘点任务概览
     *
     * @param id
     * @return
     */
    @GetMapping("/view/{id}")
    public InventoryTaskView getInventoryTaskView(@PathVariable Long id) {
        return inventoryTaskService.getInventoryTaskView(id);
    }

    /**
     * 查询盘点任务盘点人列表
     *
     * @param taskId
     * @return
     */
    @GetMapping("/inventoryUsers/{taskId}")
    public InventoryTaskUpdateUserInfoDto getInventoryUsers(@PathVariable Long taskId) {
        return inventoryTaskService.getInventoryUsers(taskId);
    }

    /**
     * 重新分配盘点人
     *
     * @param updateUserDto
     * @return
     */
    @PutMapping(value = "/updateInventoryUsers")
    public Boolean updateInventoryUsers(@RequestBody InventoryTaskUpdateUserDto updateUserDto) {
        return inventoryTaskService.updateInventoryUsers(updateUserDto);
    }

    @PutMapping(value = "/addInventoryUsers")
    public Boolean addInventoryUsers(@RequestBody InventoryTaskAddUserDto addUserDto) {
        return inventoryTaskService.addInventoryUsers(addUserDto);
    }

}
