package com.niimbot.asset.inventory.controller;


import com.niimbot.asset.inventory.model.AsInventoryAssetReport;
import com.niimbot.asset.inventory.service.AsInventoryAssetReportService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 资产上报 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-25
 */
@RestController
@RequestMapping("server/inventory/assetReport")
public class AsInventoryAssetReportServiceController {

    @Resource
    private AsInventoryAssetReportService assetReportService;

    /**
     * 查询获取资产信息
     *
     * @param id 资产ID
     * @return AsAsset信息
     */
    @GetMapping(value = "/info/{id}")
    public AsInventoryAssetReport getSurplusInfo(@PathVariable(value = "id") Long id) {
        return assetReportService.getById(id);
    }
}
