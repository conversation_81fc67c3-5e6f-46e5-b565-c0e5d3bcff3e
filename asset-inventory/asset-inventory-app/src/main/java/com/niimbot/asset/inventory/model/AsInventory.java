package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.framework.dataperm.annonation.UserFilterColumn;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 盘点单
 *
 * <AUTHOR>
 * @date 2021/4/9 09:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsInventory对象", description = "盘点单表")
@TableName(value = "as_inventory", autoResultMap = true)
public class AsInventory implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "单据编号")
    private String inventoryNo;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "状态（1：进行中，2：待审核，3：已完成，4：已终止）")
    private Integer status;

    @ApiModelProperty(value = "审核人")
    @UserFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_ASSET_ORDER, subBizCode = AssetConstant.AUTHORITY_APPROVAL_USER)
    private Long approver;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "盘点配置")
    @TableField(exist = false)
    private AsInventoryConfig config;

    @ApiModelProperty(value = "是否软删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    @UserFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_ASSET_ORDER, subBizCode = AssetConstant.AUTHORITY_CREATE_USER)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
