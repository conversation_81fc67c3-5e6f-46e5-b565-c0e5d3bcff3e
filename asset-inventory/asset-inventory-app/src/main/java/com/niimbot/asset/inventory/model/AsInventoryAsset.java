package com.niimbot.asset.inventory.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.niimbot.asset.inventory.handle.InventoryIntegerLongTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 盘点资产
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryAsset对象", description="盘点资产")
@TableName(value = "as_inventory_asset", autoResultMap = true)
public class AsInventoryAsset implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "盘点任务id")
    private Long inventoryTaskId;

    @ApiModelProperty(value = "资产id")
    private Long assetId;

    @ApiModelProperty(value = "资产快照")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject assetSnapshotData;

    @ApiModelProperty(value = "资产状态")
    private Integer status;

    @ApiModelProperty(value = "盘点人列表")
    @TableField(typeHandler = InventoryIntegerLongTypeHandler.class)
    private List<Long> inventoryUser;

    @ApiModelProperty(value = "是否盘点（0：未盘，1：已盘）")
    private Boolean checked;

    @ApiModelProperty(value = "实际盘点人")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long actualInventoryUser;

    @ApiModelProperty(value = "盘点方式(1：rfid，2：红外，3：扫码，4：手动)")
    private Integer inventoryMode;

    @ApiModelProperty(value = "盘点终端(1：pc端，2：PDA，3：移动管理端，4：移动员工端，5：小程序)")
    private Integer inventoryTerminal;

    @ApiModelProperty(value = "盘点时间")
    private LocalDateTime inventoryTime;

    @ApiModelProperty(value = "盘点照片")
    private String pictureUrl;

    @ApiModelProperty(value = "盘点备注")
    private String remark;

    @ApiModelProperty(value = "处理状态（1：未处理，2：已处理）")
    private Integer handleStatus;

    @ApiModelProperty(value = "处理人")
    private Long handleUser;

    @ApiModelProperty(value = "处理时间")
    private LocalDateTime handleTime;

    @ApiModelProperty(value = "处理类型 1-盘亏处置  2-无需处置 3-已忽略 4-已修改资产信息  5-已使用盘点人修改的信息")
    private Integer handleType;

    @ApiModelProperty(value = "虚拟键——资产编码")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String assetCode;

    @ApiModelProperty(value = "虚拟键——资产名称")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String assetName;

    @ApiModelProperty(value = "虚拟键——资产状态")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long assetStatus;

    @ApiModelProperty(value = "虚拟键——资产分类ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String assetCategory;

    @ApiModelProperty(value = "虚拟键——区域ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String storageArea;

    @ApiModelProperty(value = "虚拟键——组织ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String assetOrgOwner;

    @ApiModelProperty(value = "虚拟键——所属管理员")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String assetManagerOwner;

    @ApiModelProperty(value = "虚拟键——使用组织")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String assetUseOrg;

    @ApiModelProperty(value = "虚拟键——员工ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String assetUsePerson;

}
