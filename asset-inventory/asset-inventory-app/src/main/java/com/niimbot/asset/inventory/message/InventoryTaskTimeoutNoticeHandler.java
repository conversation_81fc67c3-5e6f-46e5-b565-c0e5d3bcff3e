package com.niimbot.asset.inventory.message;

import com.niimbot.asset.framework.constant.NoticeConstant;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.message.dto.cmd.GetNoticeSettingCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.dto.ExternalNoticeSendCmd;
import com.niimbot.message.NoticeSettingDto;
import com.niimbot.system.SendExternalNotice;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 盘点超时提醒
 * 钉钉群组消息
 * 现阶段没有整合进消息模块 暂时单独处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InventoryTaskTimeoutNoticeHandler {

    private final AsInventoryService inventoryService;
    private final MessageService messageService;
    private final RedisDistributeLock redisDistributeLock;

    @Scheduled(cron = "0 50 8 * * ?")
    public void execute() {
        redisDistributeLock.lock("InventoryTaskTimeoutNoticeHandler", 10, TimeUnit.MINUTES, v -> {
            // 钉钉群组消息
            // 盘点任务超时发送钉钉消息
            NoticeSettingDto noticeSetting = messageService.getNoticeSetting(new GetNoticeSettingCmd().setType(NoticeConstant.DING_TALK).setEvent(NoticeConstant.INVENTORY_TIMEOUT).setThrow(false));
            if (Objects.isNull(noticeSetting)) {
                return;
            }
            LocalDateTime now = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(0);
            LocalDateTime time = now.plusDays(-noticeSetting.getThreshold());
            List<AsInventory> inventories = inventoryService.getInventoryForSendNotice(time);
            if (Objects.isNull(inventories) || inventories.isEmpty()) {
                return;
            }
            List<SendExternalNotice> sendExternalNotices = inventories.stream().map(inventory -> new SendExternalNotice(inventory.getCompanyId(), inventory.getId(), NoticeConstant.INVENTORY_TIMEOUT, NoticeConstant.DING_TALK)).collect(Collectors.toList());
            for (SendExternalNotice sendExternalNotice : sendExternalNotices) {
                try {
                    ExternalNoticeSendCmd externalNoticeSendCmd = new ExternalNoticeSendCmd().setSendExternalNotice(sendExternalNotice);
                    messageService.sendExternalNotice(externalNoticeSendCmd);
                } catch (Exception e) {
                    log.error("盘点超时[id:{}]任务发送钉钉消息失败", sendExternalNotice.getUid(), e);
                }
            }
        });
    }

}
