package com.niimbot.asset.inventory.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryConfig;
import com.niimbot.inventory.InventoryStDto;

import java.util.List;

/**
 * <p>
 * 盘点配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryConfigService extends IService<AsInventoryConfig> {

    List<InventoryStDto> getStrategy();
}
