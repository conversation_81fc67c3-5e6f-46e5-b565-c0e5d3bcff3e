<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.mapper.AsInventoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.inventory.model.AsInventory">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="inventory_no" property="inventoryNo" />
        <result column="name" property="name" />
        <result column="status" property="status" />
        <result column="approver" property="approver" />
        <result column="remark" property="remark" />
        <result column="complete_time" property="completeTime" />
        <result column="is_delete" property="isDelete" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="countByStatus" resultType="com.niimbot.inventory.InventoryCountDto">
        SELECT
            ifnull(SUM(case when t.status = 1 then 1 else 0 end),0) AS inProgressNum,
            ifnull(SUM(case when t.status = 2 then 1 else 0 end),0) as pendApprovalNum,
            ifnull(SUM(case when t.status = 3 then 1 else 0 end),0) as completedNum,
            ifnull(SUM(case when t.status = 4 then 1 else 0 end),0) as terminatedNum,
            ifnull(SUM(case when t.status = 5 then 1 else 0 end),0) as rejectedNum
        FROM
            as_inventory AS i JOIN as_inventory_task AS t ON i.id = t.inventory_id
        where i.company_id = #{companyId}
            and JSON_CONTAINS(inventory_users, CAST(#{userId} AS CHAR))
    </select>
    <select id="countResReport" resultType="com.niimbot.inventory.InventoryResReportDto">
        SELECT
            checked AS inventory_status,
            COUNT( id ) AS num
        FROM
            (
            ( SELECT checked, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
            ( SELECT case asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked, id FROM as_inventory_asset_report AS r LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id WHERE s.inventory_id = #{id} )
            ) AS a
        GROUP BY
            checked
    </select>
    <select id="countResHandle" resultType="com.niimbot.inventory.InventoryResReportDto">
        select * from (
          SELECT
              checked as inventory_status,
              handle_status,
              handle_type,
              CASE
                  WHEN checked = 0 and handle_status = 2 and handle_type = 1 THEN '盘亏处置'
                  WHEN checked = 0 and handle_status = 2 and handle_type = 2 THEN '无需处置'
                  WHEN checked = 0 and handle_status = 2 and handle_type = 3 THEN '已忽略'
                  WHEN checked = 0 and handle_status = 1 and handle_type = 0 THEN '未处理'
                  WHEN checked = 3 and handle_status = 2 and handle_type = 2 THEN '更新资产'
                  WHEN checked = 3 and handle_status = 2 and handle_type = 3 THEN '已忽略'
                  WHEN checked = 3 and handle_status = 1 and handle_type = 0 THEN '未处理'
                  WHEN checked = 33 and handle_status = 2 and handle_type = 1 THEN '新增入库'
                  WHEN checked = 33 and handle_status = 2 and handle_type = 3 THEN '已忽略'
                  WHEN checked = 33 and handle_status = 1 and handle_type = 0 THEN '未处理'
                  WHEN checked = 1 and handle_status = 2 and handle_type = 1 THEN '更新资产'
                  WHEN checked = 1 and handle_status = 1 and handle_type = 0 THEN '未处理'
                  END AS handle_status_type_text,
              CASE
                  checked
                  WHEN 0 THEN
                      '盘亏'
                  WHEN 1 THEN
                      '正常'
                  WHEN 3 THEN
                      '盘盈-在册'
                  WHEN 33 THEN
                      '盘盈-不在册'
                  WHEN 4 THEN
                      '正常' ELSE ''
                  END AS inventory_status_text,
              COUNT( id ) AS num
          FROM
              (
                  ( SELECT
                           checked,
                           handle_status,
                           case handle_type when 4 then 1 when 5 then 1 else IFNULL(handle_type, 0) end as handle_type,
                           id FROM as_inventory_asset a WHERE exists (select 1 from as_inventory_asset_log b where b.inventory_id = a.inventory_id and b.asset_id = a.asset_id) and inventory_id = #{id}  ) UNION
                  ( SELECT
                        checked,
                        handle_status,
                        case handle_type when 1 then 1 else IFNULL(handle_type, 0) end as handle_type,
                        id FROM as_inventory_asset a WHERE a.checked = 0 and inventory_id = #{id} ) UNION
                  (
                      SELECT
                          case asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
                          handle_status, handle_type,
                          r.id
                      FROM
                          as_inventory_asset_report AS r
                              LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
                      WHERE
                          s.inventory_id = #{id}
                  )
              ) AS lasta
          GROUP BY
              checked,
              handle_status,
              handle_type
      ) a
        order by inventory_status, handle_status, handle_type
    </select>

    <select id="countTaskReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            inventory_task_id as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
            (
                ( SELECT checked, inventory_task_id, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
                (
                    SELECT
                        case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
                        s.inventory_task_id,
                        id
                    FROM
                        as_inventory_asset_report AS r
                            LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
                    WHERE
                        s.inventory_id = #{id}
                )
            ) AS a
        GROUP BY
            inventory_task_id
    </select>

    <select id="countCategoryReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            assetCategory as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
            (
            ( SELECT checked, IFNULL( asset_snapshot_data ->> '$.assetCategory', "" ) AS assetCategory, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
            (
        SELECT
            case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
            IFNULL( asset_data ->> '$.assetCategory', "" ) AS assetCategory,
            id
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
        WHERE
            s.inventory_id = #{id}
            )
            ) AS a
        GROUP BY
            assetCategory
    </select>
    <select id="countStatusReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            status as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
        (
            (
            SELECT
                checked,
                status,
                id
            FROM
                as_inventory_asset
            WHERE
                inventory_id = #{id}
            ) UNION
            (
            SELECT
                case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
                    CASE
            WHEN (asset_data ->> '$.status' IS NULL OR asset_data ->> '$.status' = "")
            THEN
                CASE
                WHEN ((asset_data ->> '$.status' IS NULL OR asset_data ->> '$.status' = "") AND (asset_data ->> '$.usePerson' IS NULL OR asset_data ->> '$.usePerson' = ""))
                    THEN 1
                    ELSE 2
                END
            ELSE
                status
            END AS status,
                r.id
            FROM
                as_inventory_asset_report AS r
                LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
            WHERE
                s.inventory_id = #{id}
            )
        ) AS a
        GROUP BY
        status
    </select>
    <select id="countAreaReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            storageArea as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
            (
            ( SELECT checked, IFNULL( asset_snapshot_data ->> '$.storageArea', "" ) AS storageArea, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
            (
        SELECT
            case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
            IFNULL( asset_data ->> '$.storageArea', "" ) AS storageArea,
            r.id
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
        WHERE
            s.inventory_id = #{id}
            )
            ) AS a
        GROUP BY
            storageArea
    </select>
    <select id="countOrgOwnReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            orgOwner as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
            (
            ( SELECT checked, IFNULL( asset_snapshot_data ->> '$.orgOwner', "" ) AS orgOwner, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
            (
        SELECT
            case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
            IFNULL( asset_data ->> '$.orgOwner', "" ) AS orgOwner,
            r.id
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
        WHERE
            s.inventory_id = #{id}
            )
            ) AS a
        GROUP BY
            orgOwner
    </select>
    <select id="countUseOrgReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            useOrg as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
            (
            ( SELECT checked, IFNULL( asset_snapshot_data ->> '$.useOrg', "" ) AS useOrg, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
            (
        SELECT
            case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
            IFNULL( asset_data ->> '$.useOrg', "" ) AS useOrg,
            r.id
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
        WHERE
            s.inventory_id = #{id}
            )
            ) AS a
        GROUP BY
            useOrg
    </select>
    <select id="countManagerOwnReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            managerOwner as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
            (
            ( SELECT checked, IFNULL( asset_snapshot_data ->> '$.managerOwner', "" ) AS managerOwner, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
            (
        SELECT
            case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
            IFNULL( asset_data ->> '$.managerOwner', "" ) AS managerOwner,
            r.id
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
        WHERE
            s.inventory_id = #{id}
            )
            ) AS a
        GROUP BY
            managerOwner
    </select>
    <select id="countUsePersonReport" resultType="com.niimbot.inventory.InventoryGroupReportDto">
        SELECT
            usePerson as asset_key,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0) AS pk_num,
            ifnull(SUM(case when checked = 1 then 1 else 0 end), 0) AS yp_num,
            ifnull(SUM(case when checked = 3 then 1 else 0 end), 0) AS py_zc_num,
            ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) AS py_bzc_num,
            ifnull(SUM(case when checked = 0 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 1 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 3 then 1 else 0 end), 0)
                + ifnull(SUM(case when checked = 33 then 1 else 0 end), 0) as total_num
        FROM
            (
            ( SELECT checked, IFNULL( asset_snapshot_data ->> '$.usePerson', "" ) AS usePerson, id FROM as_inventory_asset WHERE inventory_id = #{id} ) UNION
            (
        SELECT
            case r.asset_mark when 1 then 3 when 2 then 33 else 3 end AS checked,
            IFNULL( asset_data ->> '$.usePerson', "" ) AS usePerson,
            r.id
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON s.report_id = r.id
        WHERE
            s.inventory_id = #{id}
            )
            ) AS a
        GROUP BY
            usePerson
    </select>

    <select id="selectAllForSendNotice" resultType="com.niimbot.asset.inventory.model.AsInventory">
        SELECT
        t1.*
        FROM
        as_inventory t1
        LEFT JOIN as_company_setting t2 ON t1.company_id = t2.company_id
        WHERE
        t1.id NOT IN (SELECT uid FROM as_notice_record WHERE type = 1 AND `event` = 'inventoryTimeout')
        AND t2.is_test = 0
        AND t1.is_delete = 0
        AND t2.is_delete = 0
        AND t1.status IN (1,2)
        AND t1.create_time &lt;= #{ew}
    </select>

    <select id="countAssets" resultType="java.lang.Integer">
        select count(id) from as_inventory_asset where inventory_id = #{id};
    </select>

    <select id="getProcessInventory" resultType="java.lang.Integer">
        SELECT count(1) from as_inventory i LEFT JOIN as_inventory_asset ia on i.id = ia.inventory_id
        where i.`status` not in ('3','4','6') and ia.asset_id = #{assetId}
    </select>
</mapper>
