<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.mapper.AsInventoryAssetMapper">

    <resultMap id="AssetResultMap" type="com.niimbot.inventory.InventoryAssetListDto">
        <id column="id" property="id" />
        <result column="inventory_id" property="inventoryId" />
        <result column="inventory_task_id" property="inventoryTaskId" />
        <result column="asset_id" property="assetId" />
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="checked" property="checked" />
        <result column="actual_inventory_user" property="actualInventoryUser" />
        <result column="inventory_mode" property="inventoryMode" />
        <result column="inventory_terminal" property="inventoryTerminal"/>
        <result column="inventory_time" property="inventoryTime"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="remark" property="remark"/>
        <result column="handle_status" property="handleStatus"/>
        <result column="handle_user" property="handleUser"/>
        <result column="handle_time" property="handleTime"/>
        <result column="handle_type" property="handleType"/>
        <result column="pl_type" property="plType"/>
    </resultMap>

    <select id="selectAssetList" resultType="com.niimbot.inventory.InventoryAssetListDto">
        SELECT
        A.id,
        A.inventory_id,
        A.inventory_task_id,
        A.asset_id,
        A.asset_snapshot_data,
        A.status,
        A.inventory_user,
        A.checked,
        A.inventory_mode,
        A.inventory_terminal,
        A.picture_url,
        A.remark,
        A.handle_status,
        A.handle_time,
        A.handle_type,
        A.asset_code,
        B.status AS asset_status_realtime,
        B.is_delete
        FROM
        as_inventory_asset AS A
        LEFT JOIN as_asset AS B ON B.id = A.asset_id
        where
        A.id in
        <foreach collection="iaq.ids" item="tmpvalue" index="index" open="(" close=")"
                 separator=",">#{tmpvalue}
        </foreach>
        ORDER BY A.id DESC,A.handle_time DESC
    </select>

    <select id="assetCount" resultType="com.niimbot.inventory.InventoryAssetCountDto">
        SELECT
        ifnull(SUM(case when checked = 0 then 1 else 0 end),0) AS noCheckedNum,
        ifnull(SUM(case when checked = 1 then 1 else 0 end),0) as checkedNum
        FROM
        as_inventory_asset where inventory_id = #{ew.inventoryId}
        <if test="ew.taskId!=null and ew.taskId!=''">
            and inventory_task_id = #{ew.taskId}
        </if>
    </select>

    <select id="assetCount2" resultType="com.niimbot.inventory.InventoryAssetCountDto">
    SELECT
        ifnull(SUM(case when status = 0 then 1 else 0 end),0) AS noCheckedNum,
        ifnull(SUM(case when status = 1 then 1 else 0 end),0) as checkedNum
    FROM
        as_inventory_task_asset a
    where inventory_id = #{ew.inventoryId}
    and inventory_task_id = #{ew.taskId}
    </select>

    <select id="selectAppAssetList" resultMap="AssetResultMap">
        SELECT
        a.id,
        a.inventory_id,
        a.inventory_task_id,
        a.asset_id,
        a.asset_snapshot_data,
        a.STATUS,
        a.inventory_user,
        a.checked,
        a.actual_inventory_user,
        a.inventory_mode,
        a.inventory_terminal,
        a.inventory_time,
        a.picture_url,
        a.remark,
        a.handle_status,
        a.handle_user,
        a.handle_time
        FROM
        as_inventory_asset AS a
        <if test="ew.taskId!=null and ew.taskId!=''">
            LEFT JOIN (select * from as_inventory_task_asset where inventory_task_id = #{ew.taskId}) AS t ON t.inventory_id = a.inventory_id and t.asset_id= a.asset_id
        </if>
        LEFT JOIN as_asset AS aa ON aa.id = a.asset_id
        <where>
            a.inventory_id = #{ew.inventoryId}
            <if test="ew.taskId!=null and ew.taskId!=''">
                AND a.inventory_task_id = #{ew.taskId}
            </if>
            <if test="ew.inventoryUser!=null and ew.inventoryUser!=''">
                AND JSON_CONTAINS(a.inventory_user, JSON_ARRAY(#{ew.inventoryUser}))
            </if>
            <if test="ew.checked!=null">
                <choose>
                    <when test="ew.taskId!=null and ew.taskId!=''">
                        AND t.status = #{ew.checked}
                    </when>
                    <otherwise>
                        AND a.checked = #{ew.checked}
                    </otherwise>
                </choose>
            </if>
            <if test="ew.handleStatus!=null">
                AND a.handle_status = #{ew.handleStatus}
            </if>
            <if test="ew.isAbleHandle!=null and ew.isAbleHandle">
                AND a.handle_status = 1 and aa.status in (1, 2, 3)
            </if>
            <!-- 盘点方式 -->
            <if test="ew.inventoryMode != null and ew.inventoryMode.size() > 0">
                and a.inventory_mode in
                <foreach collection="ew.inventoryMode" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <!-- 有存疑备注 -->
            <if test="ew.hasRemark != null and ew.hasRemark ==1">
                and (a.remark is not null and a.remark != '')
            </if>
            <!-- 无存疑备注 -->
            <if test="ew.hasRemark != null and ew.hasRemark ==2">
                and (a.remark is null or a.remark = '')
            </if>
            <!-- 关键字（编码/名称）-->
            <if test="ew.kw!=null and ew.kw!=''">
                and (
                (a.asset_code like concat('%',#{ew.kw},'%'))
                or
                (a.asset_name like concat('%',#{ew.kw},'%'))
                )
            </if>
            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
            <!-- 增量下发 -->
            <if test="ew.versionTime != null and ew.versionTime != '' and ew.versionTime != 'null'">
                and (a.inventory_time &gt;= FROM_UNIXTIME(#{ew.versionTime} / 1000, '%Y-%m-%d %H:%i:%s')
                    <!-- 增量下发时候，本人盘点外的已盘数据也要下发 -->
                    <if test="currentUserId != null">
                        or a.actual_inventory_user != #{currentUserId}
                    </if>
                )
            </if>
        </where>
        ORDER BY a.id DESC
    </select>

    <!--<select id="selectAppAssetList" resultMap="AssetResultMap">
        SELECT
        ANY_VALUE(a.id) AS id,
        ANY_VALUE(a.inventory_id) AS inventory_id,
        ANY_VALUE(a.inventory_task_id) AS inventory_task_id,
        a.asset_id,
        ANY_VALUE(a.asset_snapshot_data) AS asset_snapshot_data,
        ANY_VALUE(a.STATUS) AS status,
        ANY_VALUE(a.inventory_user) AS inventory_user,
        ANY_VALUE(a.checked) AS checked,
        ANY_VALUE(a.actual_inventory_user) AS actual_inventory_user,
        ANY_VALUE(a.inventory_mode) AS inventory_mode,
        ANY_VALUE(a.inventory_terminal) AS inventory_terminal,
        ANY_VALUE(a.inventory_time) AS inventory_time,
        ANY_VALUE(a.picture_url) AS picture_url,
        ANY_VALUE(a.remark) AS remark,
        ANY_VALUE(a.handle_status) AS handle_status,
        ANY_VALUE(a.handle_user) AS handle_user,
        ANY_VALUE(a.handle_time) AS handle_time
        FROM
        as_inventory_asset AS a
        LEFT JOIN as_inventory AS i ON i.id = a.inventory_id
        LEFT JOIN as_asset AS aa ON aa.id = a.asset_id
        <where>
            a.inventory_id = #{ew.inventoryId}
            <if test="ew.inventoryUser!=null and ew.inventoryUser!=''">
                AND JSON_CONTAINS(a.inventory_user, JSON_ARRAY(#{ew.inventoryUser}))
            </if>
            <if test="ew.taskId!=null and ew.taskId!=''">
                AND a.inventory_task_id = #{ew.taskId}
            </if>

            <if test="ew.checked!=null">
                AND a.checked = #{ew.checked}
            </if>


            <if test="ew.handleStatus!=null">
                AND a.handle_status = #{ew.handleStatus}
            </if>

            <if test="ew.isAbleHandle!=null and ew.isAbleHandle">
                AND a.handle_status = 1 and aa.status in (1, 2, 3)
            </if>

            &lt;!&ndash; 盘点方式 &ndash;&gt;
            <if test="ew.inventoryMode != null and ew.inventoryMode.size() > 0">
                and a.inventory_mode in
                <foreach collection="ew.inventoryMode" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            &lt;!&ndash; 有存疑备注 &ndash;&gt;
            <if test="ew.hasRemark != null and ew.hasRemark ==1">
                and (a.remark is not null and a.remark != '')
            </if>
            &lt;!&ndash; 无存疑备注 &ndash;&gt;
            <if test="ew.hasRemark != null and ew.hasRemark ==2">
                and (a.remark is null or a.remark = '')
            </if>

            &lt;!&ndash; 关键字（编码/名称）&ndash;&gt;
            <if test="ew.kw!=null and ew.kw!=''">
                and (
                (a.asset_code like concat('%',#{ew.kw},'%'))
                or
                (a.asset_name like concat('%',#{ew.kw},'%'))
                )
            </if>

            &lt;!&ndash; 动态条件 &ndash;&gt;
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>

        </where>
        GROUP BY a.asset_id
        ORDER BY id DESC
    </select>-->

    <!--<select id="selectAppAssetList2" resultMap="AssetResultMap">
        SELECT
        ANY_VALUE(a.id) AS id,
        ANY_VALUE(a.inventory_id) AS inventory_id,
        ANY_VALUE(a.inventory_task_id) AS inventory_task_id,
        a.asset_id,
        ANY_VALUE(a.asset_snapshot_data) AS asset_snapshot_data,
        ANY_VALUE(a.STATUS) AS status,
        ANY_VALUE(a.inventory_user) AS inventory_user,
        ANY_VALUE(t.status) AS checked,
        ANY_VALUE(a.actual_inventory_user) AS actual_inventory_user,
        ANY_VALUE(a.inventory_mode) AS inventory_mode,
        ANY_VALUE(a.inventory_terminal) AS inventory_terminal,
        ANY_VALUE(a.inventory_time) AS inventory_time,
        ANY_VALUE(a.picture_url) AS picture_url,
        ANY_VALUE(a.remark) AS remark,
        ANY_VALUE(a.handle_status) AS handle_status,
        ANY_VALUE(a.handle_user) AS handle_user,
        ANY_VALUE(a.handle_time) AS handle_time
        FROM
        as_inventory_asset AS a
        LEFT JOIN as_inventory AS i ON i.id = a.inventory_id
        LEFT JOIN (select * from as_inventory_task_asset where inventory_task_id = #{ew.taskId}) AS t ON t.inventory_id = i.id and t.asset_id= a.asset_id
        LEFT JOIN as_asset AS aa ON aa.id = a.asset_id
        <where>
            a.inventory_id = #{ew.inventoryId} AND a.inventory_task_id = #{ew.taskId}
            <if test="ew.inventoryUser!=null and ew.inventoryUser!=''">
                AND JSON_CONTAINS(a.inventory_user, JSON_ARRAY(#{ew.inventoryUser}))
            </if>

            <if test="ew.checked!=null">
                AND t.status = #{ew.checked}
            </if>


            <if test="ew.handleStatus!=null">
                AND a.handle_status = #{ew.handleStatus}
            </if>

            <if test="ew.isAbleHandle!=null and ew.isAbleHandle">
                AND a.handle_status = 1 and aa.status in (1, 2, 3)
            </if>

            &lt;!&ndash; 盘点方式 &ndash;&gt;
            <if test="ew.inventoryMode != null and ew.inventoryMode.size() > 0">
                and a.inventory_mode in
                <foreach collection="ew.inventoryMode" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            &lt;!&ndash; 有存疑备注 &ndash;&gt;
            <if test="ew.hasRemark != null and ew.hasRemark ==1">
                and (a.remark is not null and a.remark != '')
            </if>
            &lt;!&ndash; 无存疑备注 &ndash;&gt;
            <if test="ew.hasRemark != null and ew.hasRemark ==2">
                and (a.remark is null or a.remark = '')
            </if>

            &lt;!&ndash; 关键字（编码/名称）&ndash;&gt;
            <if test="ew.kw!=null and ew.kw!=''">
                and (
                (a.asset_code like concat('%',#{ew.kw},'%'))
                or
                (a.asset_name like concat('%',#{ew.kw},'%'))
                )
            </if>

            &lt;!&ndash; 动态条件 &ndash;&gt;
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>

        </where>
        GROUP BY a.asset_id
        ORDER BY id DESC
    </select>-->

    <select id="otherUserAssetList" resultMap="AssetResultMap">
        SELECT
        ANY_VALUE(a.id) AS id,
        ANY_VALUE(a.inventory_id) AS inventory_id,
        ANY_VALUE(a.inventory_task_id) AS inventory_task_id,
        a.asset_id,
        ANY_VALUE(a.asset_snapshot_data) AS asset_snapshot_data,
        ANY_VALUE(a.STATUS) AS status,
        ANY_VALUE(a.inventory_user) AS inventory_user,
        ANY_VALUE(a.checked) AS checked,
        ANY_VALUE(a.actual_inventory_user) AS actual_inventory_user,
        ANY_VALUE(a.inventory_mode) AS inventory_mode,
        ANY_VALUE(a.inventory_terminal) AS inventory_terminal,
        ANY_VALUE(a.inventory_time) AS inventory_time,
        ANY_VALUE(a.picture_url) AS picture_url,
        ANY_VALUE(a.remark) AS remark,
        ANY_VALUE(a.handle_status) AS handle_status,
        ANY_VALUE(a.handle_user) AS handle_user,
        ANY_VALUE(a.handle_time) AS handle_time
        FROM
        as_inventory_asset AS a
        <where>
            a.checked = 1
            AND a.inventory_task_id = #{taskId}
            AND a.actual_inventory_user != #{inventoryUser}
            <if test="lastQueryTime!=null and lastQueryTime!=''">
                AND a.handle_time > #{lastQueryTime}
            </if>
        </where>
        GROUP BY a.asset_id
        ORDER BY id DESC
    </select>

    <select id="modifiedPage" resultMap="AssetResultMap">
        <!-- 盘点已修改 -->
        select * from (
        SELECT
        a.id,
        a.inventory_id,
        a.asset_id,
        a.asset_snapshot_data,
        a.actual_inventory_user,
        max(p.create_time) as inventory_time,
        a.handle_status,
        2 as pl_type
        FROM
        as_inventory_asset AS a
        INNER JOIN as_inventory_asset_log AS l ON l.inventory_id = a.inventory_id
        AND l.asset_id = a.asset_id
        AND l.log_data IS NOT NULL
        AND l.log_data != JSON_ARRAY()
        LEFT JOIN as_inventory_approve p on a.inventory_task_id = p.task_id and action = 1
        WHERE
        a.inventory_id = #{ew.inventoryId}
        GROUP BY
        a.id,
        a.inventory_id,
        a.asset_id,
        a.asset_snapshot_data,
        a.actual_inventory_user,
        a.handle_status
        UNION
        <!-- 盘盈在册已修改 -->
        SELECT
        r.id,
        s.inventory_id,
        r.asset_id,
        r.asset_data AS asset_snapshot_data,
        r.reporter AS actual_inventory_user,
        max(p.create_time) as inventory_time,
        r.handle_status,
        1 as pl_type
        FROM
        as_inventory_surplus s
        JOIN as_inventory_asset_report r ON s.report_id = r.id
        JOIN as_inventory_asset_log l ON l.inventory_id = s.inventory_id
        AND l.asset_id = r.asset_id
        LEFT JOIN as_inventory_approve p on s.inventory_task_id = p.task_id and action = 1
        WHERE
        l.log_data IS NOT NULL
        AND l.log_data != JSON_ARRAY()
        AND l.inventory_id = #{ew.inventoryId}
        GROUP BY
        r.id,
        s.inventory_id,
        r.asset_id,
        r.asset_data,
        r.reporter,
        r.handle_status
        ) a
        <where>
            <if test="ew.kw!=null and ew.kw!=''">
                and (
                (a.asset_snapshot_data ->> '$.assetCode' like concat('%',#{ew.kw},'%'))
                or
                (a.asset_snapshot_data ->> '$.assetName' like concat('%',#{ew.kw},'%'))
                )
            </if>
            <if test="ew.handleStatus!=null">
                and a.handle_status = #{ew.handleStatus}
            </if>
            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
        </where>
        order by a.id desc
    </select>


    <resultMap id="InventoryAssetPcDto" type="com.niimbot.inventory.InventoryAssetPcDto">
        <id column="id" property="id"/>
        <result column="inventory_id" property="inventoryId"/>
        <result column="asset_id" property="assetId"/>
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="checked" property="checked"/>
        <result column="actual_inventory_user" property="actualInventoryUser"/>
        <result column="inventory_mode" property="inventoryMode"/>
        <result column="inventory_terminal" property="inventoryTerminal"/>
        <result column="inventory_time" property="inventoryTime"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="remark" property="remark"/>
        <result column="handle_status" property="handleStatus"/>
        <result column="handle_user" property="handleUser"/>
        <result column="handle_time" property="handleTime"/>
        <result column="handle_type" property="handleType"/>
    </resultMap>

    <select id="takePhotoPage" resultMap="InventoryAssetPcDto">
        SELECT
        a.id,
        a.inventory_id,
        a.asset_id,
        a.asset_snapshot_data,
        a.actual_inventory_user,
        a.inventory_time,
        IF(r.id is null, 1, 2) as handle_status,
        a.picture_url
        FROM
        as_inventory_asset AS a
        left join as_inventory_handle_record r on a.id = r.inventory_asset_id and r.biz_type = 4
        WHERE
        a.inventory_id = #{ew.inventoryId}
        AND a.picture_url IS NOT NULL
        AND a.picture_url != ''
        <if test="ew.kw!=null and ew.kw!=''">
            and (
            (a.asset_snapshot_data ->> '$.assetCode' like concat('%',#{ew.kw},'%'))
            or
            (a.asset_snapshot_data ->> '$.assetName' like concat('%',#{ew.kw},'%'))
            )
        </if>
        <if test="ew.handleStatus!=null and ew.handleStatus=='1'.toString()">
            AND r.id is null
        </if>
        <if test="ew.handleStatus!=null and ew.handleStatus=='2'.toString()">
            AND r.id is not null
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        order by a.id desc
    </select>

    <select id="modifiedPageList" resultMap="AssetResultMap">
        <!-- 盘点已修改 -->
        select * from (
        SELECT
        a.id,
        a.inventory_id,
        a.asset_id,
        a.asset_snapshot_data,
        a.actual_inventory_user,
        max(p.create_time) as inventory_time,
        a.handle_status
        FROM
        as_inventory_asset AS a
        INNER JOIN as_inventory_asset_log AS l ON l.inventory_id = a.inventory_id
        AND l.asset_id = a.asset_id
        AND l.log_data IS NOT NULL
        AND l.log_data != JSON_ARRAY()
        LEFT JOIN as_inventory_approve p on a.inventory_task_id = p.task_id and action = 1
        WHERE
        a.inventory_id = #{ew.inventoryId}
        GROUP BY
        a.id,
        a.inventory_id,
        a.asset_id,
        a.asset_snapshot_data,
        a.actual_inventory_user,
        a.handle_status
        UNION
        <!-- 盘盈在册已修改 -->
        SELECT
        r.id,
        s.inventory_id,
        r.asset_id,
        r.asset_data AS asset_snapshot_data,
        r.reporter AS actual_inventory_user,
        max(p.create_time) as inventory_time,
        r.handle_status
        FROM
        as_inventory_surplus s
        JOIN as_inventory_asset_report r ON s.report_id = r.id
        JOIN as_inventory_asset_log l ON l.inventory_id = s.inventory_id AND l.asset_id = r.asset_id
        LEFT JOIN as_inventory_approve p on s.inventory_task_id = p.task_id and action = 1
        WHERE
        l.log_data IS NOT NULL
        AND l.log_data != JSON_ARRAY()
        AND l.inventory_id = #{ew.inventoryId}
        GROUP BY
        r.id,
        s.inventory_id,
        r.asset_id,
        r.asset_data,
        r.reporter,
        r.handle_status
        ) a
        <where>
            <if test="ew.kw!=null and ew.kw!=''">
                and (
                (a.asset_snapshot_data ->> '$.assetCode' like concat('%',#{ew.kw},'%'))
                or
                (a.asset_snapshot_data ->> '$.assetName' like concat('%',#{ew.kw},'%'))
                )
            </if>
            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="listAssetIds" resultType="java.lang.Long">
        select distinct asset_id from as_inventory_asset where inventory_id in
        <foreach collection="inventoryIds" item="tmpvalue" index="index" open="(" close=")"
                 separator=",">#{tmpvalue}
        </foreach>
        <if test="checked!=null">
            and checked = #{checked}
        </if>
    </select>

    <select id="inventoryAssetScanRef" resultType="java.util.Map">
        SELECT
        ast.id, apr.php_id
        <foreach collection="uniqueCodes" item="code" index="index" open=","
                 separator=",">
            ast.asset_data ->> '$.${code}' as ${code}
        </foreach>
        FROM
        as_inventory_asset ivt
        join as_asset ast on ivt.asset_id = ast.id
        LEFT JOIN as_asset_php_ref apr ON (ast.id = apr.java_id
        AND apr.type = 1
        AND ast.company_id = apr.company_id)
        where
        ivt.inventory_task_id = #{taskId}
        <if test="inventoryId != null">
            and ivt.inventory_id = #{inventoryId}
        </if>
        and ast.company_id = #{companyId}
        and ast.is_delete = 0
    </select>

    <select id="unHandleIds" resultType="java.lang.Long">
        select ia.id from as_inventory i join as_inventory_asset ia on i.id = ia.inventory_id
        where i.company_id = #{companyId} and ia.handle_status = 1
        and ia.id in
        <foreach collection="ids" item="id" index="index" open="(" close=")"
                 separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
