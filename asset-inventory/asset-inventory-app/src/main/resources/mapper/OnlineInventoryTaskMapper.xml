<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.mapper.OnlineInventoryTaskMapper">

    <resultMap id="DingInventoryTaskListDto" type="com.niimbot.ding.DingInventoryTaskListDto">
        <id column="id" property="id"/>
        <result column="inventory_id" property="inventoryId"/>
        <result column="name" property="name"/>
        <result column="inventory_users" property="inventoryUsers"
                typeHandler="com.niimbot.asset.inventory.handle.InventoryIntegerLongTypeHandler"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="enable_take_picture" property="enableTakePicture"/>
        <result column="enable_manual" property="enableManual"/>
        <result column="enable_update_data" property="enableUpdateData"/>
        <result column="enable_no_perm_look" property="enableNoPermLook"/>
    </resultMap>

    <select id="onlineTaskPage" resultMap="DingInventoryTaskListDto">
        SELECT
        t.id,
        t.inventory_id,
        t.name,
        t.inventory_users,
        t.`status`,
        i.create_time,
        c.enable_take_picture,
        c.enable_manual,
        c.enable_update_data,
        c.enable_no_perm_look
        FROM
        as_inventory_task AS t
        JOIN as_inventory AS i ON i.id = t.inventory_id
        JOIN as_inventory_config AS c ON c.id = i.id
        where
        i.company_id = #{companyId}
        and JSON_CONTAINS(t.inventory_users, JSON_ARRAY(#{userId}))
        <if test="ew.kw!=null and ew.kw!=''">
            AND t.name like concat('%',#{ew.kw},'%')
        </if>
        <if test="ew.status != null and ew.status != '-1'.toString()">
            and t.status = #{ew.status}
        </if>
        ORDER BY i.create_time DESC
    </select>

    <select id="assetCount" resultType="com.niimbot.ding.DingInventoryTaskListDto">
        SELECT
        inventory_task_id as id,
        ifnull(SUM(case when status = 0 then 1 else 0 end),0) AS no_checked_num,
        ifnull(SUM(case when status = 1 then 1 else 0 end),0) as checked_num
        FROM
        as_inventory_task_asset
        where inventory_id = #{inventoryId}
        <if test="taskIds != null and taskIds.size() > 0">
            AND inventory_task_id in
            <foreach collection="taskIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        group by inventory_task_id
    </select>

    <select id="countAddAsset" resultType="com.niimbot.ding.DingInventoryTaskListDto">
        SELECT
        s.inventory_task_id as id,
        ifnull(SUM(case when r.asset_mark = 1 then 1 else 0 end),0) AS checked_add_in_mark_num,
        ifnull(SUM(case when r.asset_mark = 2 then 1 else 0 end),0) as checked_add_not_in_mark_num
        FROM
        as_inventory_surplus AS s
        JOIN as_inventory_asset_report AS r ON s.report_id = r.id
        where
        s.inventory_id = #{inventoryId}
        <if test="taskIds != null and taskIds.size() > 0">
            AND s.inventory_task_id in
            <foreach collection="taskIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        group by s.inventory_task_id
    </select>
</mapper>
