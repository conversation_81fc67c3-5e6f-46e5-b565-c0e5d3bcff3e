<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.mapper.AsInventoryConfigMapper">

    <resultMap id="InventoryStDto" type="com.niimbot.inventory.InventoryStDto">
        <result column="st_name" property="stName"/>
        <result column="st_code" property="stCode"/>
        <result column="st_desc" property="stDesc"/>
        <collection property="stItems"
                    ofType="com.niimbot.inventory.InventoryStDto$InventoryStItem">
            <result column="st_item_name" property="stItemName"/>
            <result column="st_item_code" property="stItemCode"/>
            <result column="read_only" property="readOnly"/>
            <result column="has_hidden" property="hasHidden"/>
            <result column="has_check" property="hasCheck"/>
        </collection>
    </resultMap>

    <select id="getStrategy" resultMap="InventoryStDto">
        SELECT
            st.st_code,
            st.st_name,
            st.st_desc,
            si.st_item_code,
            si.st_item_name,
            ss.read_only,
            ss.has_hidden,
            ss.has_check
        FROM
            as_inventory_st_setting ss
            JOIN as_inventory_st st ON ss.st_id = st.id
            JOIN as_inventory_st_item si ON ss.st_item_id = si.id
        ORDER BY
            st.id,
            si.id
    </select>

</mapper>