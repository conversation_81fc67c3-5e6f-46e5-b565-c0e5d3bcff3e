<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.mapper.AsInventorySurplusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.inventory.model.AsInventorySurplus">
        <id column="id" property="reportId" />
        <result column="inventory_id" property="inventoryId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, inventory_id, asset_data, reporter, handle_status, handle_user, handle_time, handle_type, asset_mark, asset_id
    </sql>
    <resultMap id="selectSurplusMap" type="com.niimbot.asset.inventory.model.AsInventoryAssetReport">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="reporter" property="reporter"/>
        <result column="handle_status" property="handleStatus"/>
        <result column="handle_user" property="handleUser"/>
        <result column="handle_time" property="handleTime"/>
        <result column="handle_type" property="handleType"/>
        <result column="asset_mark" property="assetMark"/>
        <result column="report_type" property="reportType"/>
        <result column="asset_id" property="assetId"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <resultMap id="selectSurplusDtoMap" type="com.niimbot.inventory.InventorySurplusDto">
        <id column="id" property="id"/>
        <result column="inventory_id" property="inventoryId"/>
        <result column="inventory_task_id" property="inventoryTaskId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="reporter" property="reporter"/>
        <result column="handle_status" property="handleStatus"/>
        <result column="handle_user" property="handleUser"/>
        <result column="handle_time" property="handleTime"/>
        <result column="handle_type" property="handleType"/>
        <result column="asset_mark" property="assetMark"/>
        <result column="report_type" property="reportType"/>
        <result column="asset_id" property="assetId"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="inventory_terminal" property="inventoryTerminal"/>
    </resultMap>

    <select id="getPlSurplus" resultMap="selectSurplusDtoMap">
        SELECT
        r.id,
        s.inventory_id,
        s.inventory_task_id,
        r.asset_data,
        r.reporter,
        r.handle_status,
        r.handle_user,
        r.handle_time,
        r.handle_type,
        r.asset_mark,
        r.report_type,
        r.asset_id,
        r.remark,
        r.create_time,
        r.inventory_terminal
        FROM
        as_inventory_surplus AS s
        LEFT JOIN as_inventory_asset_report AS r ON r.id = s.report_id
        <where>
            r.report_type = 1
            and s.inventory_id = #{ew.inventoryId}
            <choose>
                <when test="ew.taskId!=null and ew.taskId!=''">
                    AND s.inventory_task_id = #{ew.taskId}
                </when>
                <otherwise>
                    and NOT EXISTS (
                    select 1 from as_inventory_asset ais where ais.checked = 1 and ais.asset_id =
                    r.asset_id and ais.inventory_id = #{ew.inventoryId})
                </otherwise>
            </choose>
            <if test="ew.assetMark!=null">
                AND r.asset_mark = #{ew.assetMark}
            </if>
            <if test="ew.inventoryUser!=null and ew.inventoryUser!=''">
                AND r.reporter = #{ew.inventoryUser}
            </if>
            <!-- 有存疑备注 -->
            <if test="ew.hasRemark != null and ew.hasRemark ==1">
                and (r.remark is not null and r.remark != '')
            </if>
            <!-- 无存疑备注 -->
            <if test="ew.hasRemark != null and ew.hasRemark ==2">
                and (r.remark is null or r.remark = '')
            </if>
            <!-- 关键字（编码/名称）-->
            <if test="ew.kw!=null and ew.kw!=''">
                and (
                (r.asset_data ->> '$.assetCode' like concat('%',#{ew.kw},'%'))
                or
                (r.asset_data ->> '$.assetName' like concat('%',#{ew.kw},'%'))
                )
            </if>
            <if test="ew.handleStatus!=null">
                AND r.handle_status = #{ew.handleStatus}
            </if>
            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
        </where>
        ORDER BY r.id DESC
    </select>

    <select id="selectSurplusList" resultMap="selectSurplusDtoMap">
        SELECT
        r.id,
        s.inventory_id,
        s.inventory_task_id,
        r.asset_data,
        r.reporter,
        r.handle_status,
        r.handle_user,
        r.handle_time,
        r.handle_type,
        r.asset_mark,
        r.report_type,
        r.asset_id,
        r.create_time
        FROM
        as_inventory_surplus AS s
        LEFT JOIN as_inventory_asset_report AS r ON r.id = s.report_id
        <where>
            r.report_type = 1 and s.inventory_id = #{ew.inventoryId}
            <if test="ew.assetMark!=null">
                AND r.asset_mark = #{ew.assetMark}
            </if>
            <choose>
                <when test="ew.taskId!=null and ew.taskId!=''">
                    AND s.inventory_task_id = #{ew.taskId}
                </when>
                <otherwise>
                    and NOT EXISTS (
                    select 1 from as_inventory_asset ais where ais.checked = 1 and ais.asset_id =
                    r.asset_id and ais.inventory_id = #{ew.inventoryId})
                </otherwise>
            </choose>
            <if test="ew.inventoryUser!=null and ew.inventoryUser!=''">
                AND r.reporter = #{ew.inventoryUser}
            </if>

            <if test="ew.kw!=null and ew.kw!=''">
                and (
                (r.asset_data ->> '$.assetCode' is not null and r.asset_data ->> '$.assetCode' like concat('%',#{ew.kw},'%'))
                or
                (r.asset_data ->> '$.assetName' is not null and r.asset_data ->> '$.assetName' like concat('%',#{ew.kw},'%'))
                )
            </if>

            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
        </where>
        ORDER BY r.id DESC
    </select>

    <select id="getMarkSurplusAsset" resultType="java.lang.Long">
        SELECT
            r.asset_id
        FROM
            as_inventory_surplus AS s
            LEFT JOIN as_inventory_asset_report AS r ON s.report_id = r.id
        WHERE
            s.inventory_id = #{inventoryId}
            AND r.asset_mark = 1
    </select>
    <select id="countAddAsset" resultType="java.lang.Integer">
        SELECT
            COUNT( r.id )
        FROM
            as_inventory_surplus AS s
            LEFT JOIN as_inventory_asset_report AS r ON s.report_id = r.id
        <where>
            s.inventory_id = #{ew.inventoryId}
            <if test="ew.assetMark!=null and ew.assetMark!=''">
                AND r.asset_mark = #{ew.assetMark}
            </if>
            <choose>
                <when test="ew.taskId!=null and ew.taskId!=''">
                    AND s.inventory_task_id = #{ew.taskId}
                </when>
                <otherwise>
                    and NOT EXISTS (select 1 from as_inventory_asset ais where ais.checked = 1 and ais.asset_id = r.asset_id and ais.inventory_id = #{ew.inventoryId})
                </otherwise>
            </choose>
            <if test="ew.taskType!=null and ew.taskType!=''">
                and r.task_type = #{ew.taskType}
            </if>
        </where>
    </select>

    <delete id="removeWithReport">
        DELETE r, s
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON r.id = s.report_id
        WHERE
            r.reporter = #{userId} and s.inventory_id = #{inventoryId}
            and r.inventory_terminal != 1
            and r.task_type = #{taskType}
    </delete>
    <delete id="removeExistAsset">
        DELETE r,s
        FROM
            as_inventory_asset_report AS r
            LEFT JOIN as_inventory_surplus AS s ON r.id = s.report_id
        WHERE
            r.asset_mark = 1
            AND s.inventory_id = #{inventoryId}
            AND r.asset_id in
            <foreach collection="deleteAssetIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
    </delete>

    <delete id="removeAssetByTaskId">
        DELETE r,s
        FROM
        as_inventory_asset_report AS r
        LEFT JOIN as_inventory_surplus AS s ON r.id = s.report_id
        WHERE
        s.inventory_task_id = #{taskId}
    </delete>

    <select id="listAssetByTaskId" resultMap="selectSurplusMap">
        SELECT
            r.id,
            r.company_id,
            r.asset_data,
            r.reporter,
            r.handle_status,
            r.handle_user,
            r.handle_time,
            r.handle_type,
            r.asset_mark,
            r.report_type,
            r.asset_id
        FROM
            as_inventory_surplus AS s
                LEFT JOIN as_inventory_asset_report AS r ON r.id = s.report_id
        WHERE
            s.inventory_task_id = #{taskId}
    </select>

    <select id="getUnhandleNum" resultType="int">
        SELECT count(*)
        FROM as_inventory_surplus AS s
                 LEFT JOIN
             as_inventory_asset_report AS r
             ON r.id = s.report_id
        WHERE r.report_type = 1
          AND s.inventory_id = #{inventoryId}
          AND r.handle_status = 1
          AND (r.asset_id in (
            SELECT
                1
            FROM
                as_inventory_asset_log b
            WHERE
                ( b.log_data != JSON_ARRAY() OR b.log_data IS NULL )
              AND b.inventory_id = s.inventory_id
        ) or r.asset_mark = 2)
    </select>
</mapper>
