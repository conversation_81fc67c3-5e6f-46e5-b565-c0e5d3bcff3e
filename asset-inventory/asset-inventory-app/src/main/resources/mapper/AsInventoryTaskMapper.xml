<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.mapper.AsInventoryTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.inventory.model.AsInventoryTask">
        <id column="id" property="id" />
        <result column="inventory_id" property="inventoryId" />
        <result column="name" property="name" />
        <result column="asset_num" property="assetNum" />
        <result column="inventory_users" property="inventoryUsers"
                typeHandler="com.niimbot.asset.inventory.handle.InventoryIntegerLongTypeHandler" />
        <result column="status" property="status" />
        <result column="last_submit_time" property="lastSubmitTime" />
        <result column="last_approve_time" property="lastApproveTime" />
        <result column="task_type" property="taskType" />
    </resultMap>


    <resultMap id="InventoryTaskListDtoMap" type="com.niimbot.inventory.InventoryTaskListDto">
        <id column="id" property="id" />
        <result column="inventory_id" property="inventoryId" />
        <result column="name" property="name" />
        <result column="asset_num" property="assetNum" />
        <result column="inventory_users" property="inventoryUsers"
                typeHandler="com.niimbot.asset.inventory.handle.InventoryIntegerLongTypeHandler"/>
        <result column="status" property="status"/>
        <result column="task_type" property="taskType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="enable_take_picture" property="enableTakePicture"/>
        <result column="enable_manual" property="enableManual"/>
        <result column="enable_update_data" property="enableUpdateData"/>
        <result column="enable_no_perm_look" property="enableNoPermLook"/>
        <result column="rejected_time" property="rejectedTime"/>
        <result column="rejected_opinion" property="rejectedOpinion"/>
        <result column="submit_time" property="submitTime"/>
        <result column="approved_time" property="approvedTime"/>
        <result column="terminated_time" property="terminatedTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, inventory_id, name, asset_num, inventory_user, inventory_users, status, last_submit_time, last_approve_time
    </sql>
    <select id="selectTaskList" resultMap="InventoryTaskListDtoMap">
        SELECT
        id,
        inventory_id,
        NAME,
        inventory_users,
        STATUS,
        asset_num,
        task_type,
        approver,
        create_by,
        create_time,
        enable_take_picture,
        enable_manual,
        enable_update_data,
        enable_no_perm_look,
        -- 盘点单终止
        terminated_time,
        -- 盘点任务驳回
        IF ( t.STATUS = 5, max( t.approve_time ), NULL ) AS rejected_time,
        IF ( t.STATUS = 5, (select opinion from as_inventory_approve where id = max( t.approve_id)),
        NULL ) AS rejected_opinion,
        -- 盘点任务待审核
        IF ( t.STATUS = 2, max( t.approve_time ), NULL ) AS submit_time,
        -- 盘点任务已完成
        IF ( t.STATUS = 3, max( t.approve_time ), NULL ) AS approved_time
        FROM
        (
        SELECT
        t.id,
        t.inventory_id,
        t.NAME,
        t.inventory_users,
        t.STATUS,
        t.asset_num,
        t.task_type,
        i.approver,
        i.create_by,
        i.update_time,
        i.create_time,
        c.enable_take_picture,
        c.enable_manual,
        c.enable_update_data,
        c.enable_no_perm_look,
        IF( i.STATUS = 4, i.update_time, NULL ) AS terminated_time,
        ia.create_time AS approve_time,
        ia.opinion,
        ia.id as approve_id
        FROM
        as_inventory_task AS t
        JOIN as_inventory AS i ON i.id = t.inventory_id
        JOIN as_inventory_config AS c ON c.id = i.id
        LEFT JOIN as_inventory_approve ia on (ia.inventory_id = i.id and ia.task_id = t.id)
        <where>
            i.company_id = #{companyId}
            AND JSON_CONTAINS(t.inventory_users, JSON_ARRAY(#{userId}))
            <if test="ew.taskId!=null">
                and t.id = #{ew.taskId}
            </if>
            <if test="ew.kw!=null and ew.kw!=''">
                AND t.name like concat('%',#{ew.kw},'%')
            </if>
            <!-- 状态 -->
            <if test="ew.status != null and ew.status.size() > 0">
                and t.status in
                <foreach collection="ew.status" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <!-- 审核人 -->
            <if test="ew.approvers != null and ew.approvers.size() > 0">
                and i.approver in
                <foreach collection="ew.approvers" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <!-- 创建人 -->
            <if test="ew.createBys != null and ew.createBys.size() > 0">
                and i.create_by in
                <foreach collection="ew.createBys" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>

            <if test="ew.createTime!=null and ew.createTime.size==2">
                <!-- 创建开始时间 -->
                <if test="ew.createTime[0]!=null and ew.createTime[0]!=''">
                    and i.create_time &gt;= CONCAT(#{ew.createTime[0]}, ' 00:00:00')
                </if>
                <!-- 创建结束时间 -->
                <if test="ew.createTime[1]!=null and ew.createTime[1]!=''">
                    and i.create_time &lt;= CONCAT(#{ew.createTime[1]}, ' 23:59:59')
                </if>
            </if>
        </where>
        ) t
        GROUP BY
        t.id
    </select>


    <resultMap id="InventoryTaskInfoDtoMap" type="com.niimbot.inventory.InventoryTaskInfoDto">
        <id column="id" property="id" />
        <result column="inventory_id" property="inventoryId" />
        <result column="name" property="name" />
        <result column="asset_num" property="assetNum" />
        <result column="checked_num" property="checkedNum" />
        <result column="inventory_user" property="inventoryUser" />
        <result column="inventory_users" property="inventoryUsers"
                typeHandler="com.niimbot.asset.inventory.handle.InventoryIntegerLongTypeHandler" />
        <result column="status" property="status" />
        <result column="task_type" property="taskType" />
    </resultMap>
    <select id="getInfo" resultMap="InventoryTaskInfoDtoMap">
        SELECT
            t.id,
            t.inventory_id,
            t.name,
            t.inventory_user,
            t.inventory_users,
            t.`status`,
            t.`asset_num`,
            t.`checked_num`,
            t.`task_type`,
            i.approver,
            i.create_by,
            i.create_time
        FROM
            as_inventory_task AS t
            LEFT JOIN as_inventory AS i ON i.id = t.inventory_id
        WHERE
            t.id = #{id}
    </select>

    <select id="selectAllForSendTaskTimeoutMessage" resultMap="BaseResultMap">
        SELECT
        t1.id,
        t1.inventory_id,
        t1.name,
        t1.inventory_users,
        t2.create_time AS last_submit_time
        FROM
        as_inventory_task t1 LEFT JOIN as_inventory t2 ON t1.inventory_id = t2.id
        WHERE
        t2.company_id = #{companyId}
        AND t2.is_delete = 0
        AND t1.`status` IN (1 ,5)
        AND DATEDIFF(NOW(),t2.create_time) IN
        <foreach collection="days" item="day" open="(" close=")" separator=",">
            #{day}
        </foreach>
    </select>

    <select id="assetCount" resultType="com.niimbot.inventory.InventoryTaskListDto">
        SELECT
        inventory_task_id as id,
        ifnull(SUM(case when status = 0 then 1 else 0 end),0) AS no_checked_num,
        ifnull(SUM(case when status = 1 then 1 else 0 end),0) as checked_num
        FROM
        as_inventory_task_asset
        where inventory_id = #{inventoryId}
        <if test="taskIds != null and taskIds.size() > 0">
            AND inventory_task_id in
            <foreach collection="taskIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        group by inventory_task_id
    </select>

    <select id="countAddAsset" resultType="com.niimbot.inventory.InventoryTaskListDto">
        SELECT
        s.inventory_task_id as id,
        ifnull(SUM(case when r.asset_mark = 1 then 1 else 0 end),0) AS checked_add_in_mark_num,
        ifnull(SUM(case when r.asset_mark = 2 then 1 else 0 end),0) as checked_add_not_in_mark_num
        FROM
        as_inventory_surplus AS s
        JOIN as_inventory_asset_report AS r ON s.report_id = r.id
        where
        s.inventory_id = #{inventoryId}
        <if test="taskIds != null and taskIds.size() > 0">
            AND s.inventory_task_id in
            <foreach collection="taskIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        group by s.inventory_task_id
    </select>

</mapper>
