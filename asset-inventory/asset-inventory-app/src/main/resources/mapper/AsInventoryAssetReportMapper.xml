<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.mapper.AsInventoryAssetReportMapper">

    <select id="unHandleIds" resultType="java.lang.Long">
        select id from as_inventory_asset_report
        where
        status = 1
        and company_Id = #{companyId}
        and id in
        <foreach collection="ids" item="id" index="index" open="(" close=")"
                 separator=",">
            #{id}
        </foreach>
    </select>

</mapper>