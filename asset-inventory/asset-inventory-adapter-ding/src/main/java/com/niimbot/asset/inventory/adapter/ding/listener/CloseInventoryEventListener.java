package com.niimbot.asset.inventory.adapter.ding.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.service.DingMsgOutTrackService;
import com.niimbot.asset.inventory.adapter.ding.model.DingInventoryGroup;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryGroupService;
import com.niimbot.asset.inventory.domain.event.CloseInventoryEvent;

import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

import cn.hutool.core.collection.ListUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/4/20 15:18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CloseInventoryEventListener implements ApplicationListener<CloseInventoryEvent> {

    private final DingMsgOutTrackService dingMsgOutTrackService;
    private final DingInventoryGroupService dingInventoryGroupService;
    private final DingCorpService dingCorpService;

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    @Async
    public void onApplicationEvent(CloseInventoryEvent event) {
        List<DingInventoryGroup> groupList = dingInventoryGroupService.list(Wrappers.lambdaQuery(DingInventoryGroup.class)
                .eq(DingInventoryGroup::getCompanyId, event.getCompanyId())
                .eq(DingInventoryGroup::getInventoryId, event.getInventoryId()));

        for (DingInventoryGroup inventoryGroup : groupList) {
            DingCorp dingCorp = dingCorpService.getOne(Wrappers.lambdaQuery(DingCorp.class)
                    .eq(DingCorp::getCompanyId, inventoryGroup.getCompanyId()), false);
            if (dingCorp != null) {
                // 关闭审核人创建人吊顶
                dingMsgOutTrackService.updateByOpenConversationId(dingCorp.getCorpId(),
                        inventoryGroup.getOpenConversationId(),
                        TemplateTypeEnum.CARD_PD_APPROVER_TOPBOX.getCode(), ListUtil.empty());
                // 关闭盘点人吊顶
                dingMsgOutTrackService.updateByOpenConversationId(dingCorp.getCorpId(),
                        inventoryGroup.getOpenConversationId(),
                        TemplateTypeEnum.CARD_PD_TASK_TOPBOX.getCode(), ListUtil.empty());
            } else {
                log.error("关闭吊顶卡片失败, 未找到企业授权信息, 企业id -> {}", inventoryGroup.getCompanyId());
            }
        }
    }
}
