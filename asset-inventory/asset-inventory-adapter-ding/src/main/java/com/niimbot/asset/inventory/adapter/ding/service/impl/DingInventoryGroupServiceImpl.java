package com.niimbot.asset.inventory.adapter.ding.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.message.model.DingMsgOutTrack;
import com.niimbot.asset.dingtalk.message.service.DingMsgOutTrackService;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryGroupDto;
import com.niimbot.asset.inventory.adapter.ding.mapper.DingInventoryGroupMapper;
import com.niimbot.asset.inventory.adapter.ding.model.DingInventoryGroup;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryGroupService;
import com.niimbot.asset.inventory.domain.event.CreateInventoryEvent;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.ding.DingInventorySaveCmd;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/4/6 17:00
 */
@Slf4j
@Service
public class DingInventoryGroupServiceImpl extends ServiceImpl<DingInventoryGroupMapper, DingInventoryGroup> implements DingInventoryGroupService {

    private static final List<Integer> INVENTORY_CLOSE_STATUS = ListUtil.of(InventoryConstant.COMPLETED, InventoryConstant.TERMINATED, InventoryConstant.HANDLED);
    private final DingCorpService dingCorpService;
    private final DingOpenApiService dingOpenApiService;
    private final DingMsgOutTrackService dingMsgOutTrackService;

    private static final String IM_COOL_APP_INSTALL = "im_cool_app_install";
    private static final String IM_COOL_APP_UNINSTALL = "im_cool_app_uninstall";

    private Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            .retryIfException()
            .retryIfResult(flag -> Objects.equals(flag, false))
            .withWaitStrategy(WaitStrategies.fixedWait(6, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(10))
            .build();

    @Autowired
    public DingInventoryGroupServiceImpl(DingCorpService dingCorpService,
                                         DingOpenApiService dingOpenApiService,
                                         DingMsgOutTrackService dingMsgOutTrackService) {
        this.dingCorpService = dingCorpService;
        this.dingOpenApiService = dingOpenApiService;
        this.dingMsgOutTrackService = dingMsgOutTrackService;
    }

    @Override
    public List<String> groupNameList(Long inventoryId) {
        List<DingInventoryGroup> list = list(Wrappers.lambdaQuery(DingInventoryGroup.class)
                .select(DingInventoryGroup::getOpenConversationName)
                .eq(DingInventoryGroup::getInventoryId, inventoryId)
                .orderByDesc(DingInventoryGroup::getCreateTime));
        return list.stream().map(DingInventoryGroup::getOpenConversationName)
                .collect(Collectors.toList());
    }

    @Override
    public List<DingInventoryGroupDto> listProcess() {
        return getBaseMapper().listProcess();
    }

    @Override
    public Boolean create(DingInventorySaveCmd saveCmd) {
        AsInventory inventory = getBaseMapper().inProcess(saveCmd.getOpenConversationId(), SdkConstant.COOL_APP_CODE);

        boolean replace = false;
        // 存在酷应用
        if (inventory != null) {
            if (INVENTORY_CLOSE_STATUS.contains(inventory.getStatus())) {
                remove(Wrappers.lambdaQuery(DingInventoryGroup.class)
                        .eq(DingInventoryGroup::getOpenConversationId, saveCmd.getOpenConversationId()));
                replace = true;
            } else {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "该群已存在进行中的盘点单，请重新选择群");
            }
        }

        DingCorp dingCorp = dingCorpService.getByCompanyId(LoginUserThreadLocal.getCompanyId());
        String group = dingOpenApiService.queryGroup(saveCmd.getOpenConversationId(), dingCorp.getCorpId());
        if (StrUtil.isEmpty(group)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "查询钉钉群名称异常，请稍后重试");
        }

        DingInventoryGroup dingInventoryGroup = new DingInventoryGroup();
        dingInventoryGroup.setId(IdUtils.getId());
        dingInventoryGroup.setInventoryId(saveCmd.getInventoryId());
        dingInventoryGroup.setOpenConversationId(saveCmd.getOpenConversationId());
        dingInventoryGroup.setOpenConversationName(group);
        dingInventoryGroup.setCoolAppCode(SdkConstant.COOL_APP_CODE);
        boolean save = save(dingInventoryGroup);
        if (save && replace) {
            // 存在酷应用不会收到安装回调，需要重新拉起吊顶
            CreateInventoryEvent event = new CreateInventoryEvent(
                    LoginUserThreadLocal.getCompanyId(),
                    dingInventoryGroup.getInventoryId(),
                    dingInventoryGroup.getOpenConversationId());
            event.setFirstLink(false);
            EventPublishHandler.publish(event);
        }
        return true;
    }

    @Override
    public Boolean chatCallback(JSONObject content) {
        String eventType = content.getString("EventType");
        String corpId = content.getString("CorpId");
        String openConversationId = content.getString("OpenConversationId");
        DingCorp dingCorp = dingCorpService.getOne(new LambdaQueryWrapper<DingCorp>()
                .eq(DingCorp::getCorpId, corpId));
        if (ObjectUtil.isNotNull(dingCorp)) {
            try {
                LoginUserDto userDto = new LoginUserDto();
                userDto.setCusUser(new CusUserDto()
                        .setIsAdmin(true)
                        .setCompanyId(dingCorp.getCompanyId()));
                LoginUserThreadLocal.set(userDto);
                switch (eventType) {
                    case CHAT_DISBAND:
                        remove(Wrappers.lambdaQuery(DingInventoryGroup.class)
                                .eq(DingInventoryGroup::getOpenConversationId, openConversationId));
                        break;
                    case CHAT_UPDATE_TITLE:
                        update(Wrappers.lambdaUpdate(DingInventoryGroup.class)
                                .set(DingInventoryGroup::getOpenConversationName, content.getString("Title"))
                                .eq(DingInventoryGroup::getOpenConversationId, openConversationId));
                        break;
                    default:
                        break;
                }
            } finally {
                LoginUserThreadLocal.remove();
            }
        }
        return true;
    }

    @Override
    public Boolean coolAppCallback(JSONObject content) {
        String syncAction = content.getString("syncAction");
        String openConversationId = content.getString("OpenConversationId");
        String coolAppCode = content.getString("CoolAppCode");
        if (IM_COOL_APP_INSTALL.equals(syncAction)) {
            try {
                retryer.call(() -> {
                    DingInventoryGroup one = getOne(Wrappers.lambdaQuery(DingInventoryGroup.class)
                            .eq(DingInventoryGroup::getOpenConversationId, openConversationId)
                            .eq(DingInventoryGroup::getCoolAppCode, coolAppCode));
                    // 可能会没执行，所以加入重试
                    if (one != null) {
                        EventPublishHandler.publish(new CreateInventoryEvent(
                                one.getCompanyId(),
                                one.getInventoryId(),
                                openConversationId));
                        return true;
                    } else {
                        log.warn("ding_inventory_group not create, waiting retry, {}", openConversationId);
                        return false;
                    }
                });
            } catch (Exception e) {
                log.error("酷应用发送通知重试失败，data = {}", content.toJSONString());
            }
        } else if (IM_COOL_APP_UNINSTALL.equals(syncAction)) {
            remove(Wrappers.lambdaQuery(DingInventoryGroup.class)
                    .eq(DingInventoryGroup::getOpenConversationId, openConversationId)
                    .eq(DingInventoryGroup::getCoolAppCode, coolAppCode));
            dingMsgOutTrackService.remove(Wrappers.lambdaQuery(DingMsgOutTrack.class)
                    .eq(DingMsgOutTrack::getOpenConversationId, openConversationId));
        }
        return true;
    }

}
