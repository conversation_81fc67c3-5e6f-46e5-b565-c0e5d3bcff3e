package com.niimbot.asset.inventory.adapter.ding.dto;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/12 17:26
 */
@Data
public class DingInventoryReceiverDto {

    private String inventoryName;

    // 盘点员
    private Set<String> inventoryUsers = new HashSet<>();

    // 盘点员任务名称列表
    private Map<String, List<String>> inventoryUserTaskMap = new HashMap<>();

    // 原始盘点员Id
    private Map<Long, String> sourceInventoryUserMap = new HashMap<>();

    // 审核人
    private String approver;

    // 原始系统审核人Id
    private Long sourceApprover;

    // 创建人
    private String createBy;

    // 原始系创建人Id
    private Long sourceCreateBy;

}
