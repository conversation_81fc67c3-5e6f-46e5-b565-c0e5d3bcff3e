package com.niimbot.asset.inventory.adapter.ding.dto;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/18 17:40
 */
@Data
public class CardPdTaskTopBoxDto {

    private Long inventoryId;

    private String inventoryName;

    // 全部任务
    private Integer allTask = 0;

    // 未完成任务
    private Integer noFinishTask = 0;

    // 已完成百分比
    private Integer finishTaskPer = 0;

    // 已完成任务
    private Integer finishTask = 0;


    // 已盘
    private Integer checkedNum = 0;

    // 已盘比例
    private Integer checkedNumPer = 0;

    // 未盘
    private Integer noCheckedNum = 0;

    public Integer getFinishTaskPer() {
        if (ObjectUtil.isAllNotEmpty(noFinishTask, allTask)
                && allTask > 0) {
            return (allTask - noFinishTask) * 100 / allTask;
        }
        return 0;
    }

    public Integer getCheckedNumPer() {
        if (ObjectUtil.isAllNotEmpty(checkedNum, noCheckedNum)
                && (checkedNum > 0 || noCheckedNum > 0)) {
            return checkedNum * 100 / (checkedNum + noCheckedNum);
        }
        return 0;
    }

}
