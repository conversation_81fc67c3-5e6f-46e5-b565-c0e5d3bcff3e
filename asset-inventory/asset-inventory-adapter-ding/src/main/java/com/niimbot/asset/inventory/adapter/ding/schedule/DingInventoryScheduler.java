package com.niimbot.asset.inventory.adapter.ding.schedule;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dingtalk.message.DingtalkMessageClient;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.request.card.DingtalkCardMessageRequest;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.adapter.ding.dto.CardPdTaskTopBoxDto;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryGroupDto;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryReceiverDto;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryGroupService;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryService;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryTaskService;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/4/19 18:15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DingInventoryScheduler {

    private final DingInventoryGroupService dingInventoryGroupService;
    private final DingInventoryService dingInventoryService;
    private final DingtalkMessageClient dingtalkMessageClient;
    private final DingInventoryTaskService dingInventoryTaskService;
    private final RedisDistributeLock redisDistributeLock;

    /*
     * 周一到周五，9点10分发送盘点日报
     */
    @Scheduled(cron = "0 10 9 * * MON-FRI")
    public void inventoryDaily() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        redisDistributeLock.lock("inventoryDaily", 3, TimeUnit.MINUTES, (a) -> {
            List<DingInventoryGroupDto> processGroup = dingInventoryGroupService.listProcess();
            final List<Integer> inProgressList = ListUtil.of(InventoryConstant.IN_PROGRESS, InventoryConstant.REJECTED);
            processGroup.parallelStream().forEach(process -> {
                try {
                    LoginUserDto userDto = new LoginUserDto();
                    userDto.setCusUser(new CusUserDto()
                            .setIsAdmin(true)
                            .setCompanyId(process.getCompanyId()));
                    LoginUserThreadLocal.set(userDto);
                    DingInventoryReceiverDto receiverDto = dingInventoryService.dingInventoryReceiver(process.getInventoryId(), inProgressList);
                    CardPdTaskTopBoxDto topBoxDto = dingInventoryTaskService.dingInventoryTopBox(process.getInventoryId());
                    if (receiverDto != null && topBoxDto != null) {

                        JSONObject params = (JSONObject) JSONObject.toJSON(topBoxDto);
                        params.put("date", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

                        // 发送审核人创建人卡片
                        Set<String> sender = new HashSet<>();
                        sender.add(receiverDto.getApprover());
                        sender.add(receiverDto.getCreateBy());
                        DingtalkCardMessageRequest request = new DingtalkCardMessageRequest(process.getCorpId(), process.getOpenConversationId());
                        request.setUserIds(sender.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
                        params.put("type", "1");
                        request.setCardParamMap(Convert.toMap(String.class, String.class, params));
                        dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_PD_DAILY, request);

                        // 发送盘点员卡片
                        if (CollUtil.isNotEmpty(receiverDto.getInventoryUsers())) {
                            request.setUserIds(new ArrayList<>(receiverDto.getInventoryUsers()));
                            params.put("type", "2");
                            request.setCardParamMap(Convert.toMap(String.class, String.class, params));
                            dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_PD_DAILY, request);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    LoginUserThreadLocal.remove();
                }
            });
        });
    }

}
