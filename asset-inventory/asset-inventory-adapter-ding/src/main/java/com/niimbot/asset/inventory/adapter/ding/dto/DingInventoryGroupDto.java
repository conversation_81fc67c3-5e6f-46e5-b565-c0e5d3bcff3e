package com.niimbot.asset.inventory.adapter.ding.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/20 8:38
 */
@Data
public class DingInventoryGroupDto {

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "钉钉公司Id")
    private String corpId;

    @ApiModelProperty(value = "盘点Id")
    private Long inventoryId;

    @ApiModelProperty(value = "群信息")
    private String openConversationId;

}
