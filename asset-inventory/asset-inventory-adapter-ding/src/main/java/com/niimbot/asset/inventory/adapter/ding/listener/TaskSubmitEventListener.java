package com.niimbot.asset.inventory.adapter.ding.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.message.DingtalkMessageClient;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.request.card.DingtalkCardMessageRequest;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryReceiverDto;
import com.niimbot.asset.inventory.adapter.ding.model.DingInventoryGroup;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryGroupService;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryService;
import com.niimbot.asset.inventory.domain.event.TaskSubmitEvent;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.inventory.InventoryTaskInfoDto;

import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/4/15 15:33
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskSubmitEventListener implements ApplicationListener<TaskSubmitEvent> {

    @Resource
    private DingCorpService dingCorpService;

    @Resource
    private DingtalkMessageClient dingtalkMessageClient;

    @Resource
    private DingInventoryService dingInventoryService;

    @Resource
    private AsInventoryTaskService inventoryTaskService;

    @Resource
    private DingInventoryGroupService dingInventoryGroupService;

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    @Async
    public void onApplicationEvent(TaskSubmitEvent event) {
        DingCorp dingCorp = dingCorpService.getByCompanyId(event.getCompanyId());
        if (dingCorp == null) {
            log.error("盘点提交审批工作通知发送失败, 未找到企业授权信息, 企业id -> {}", event.getCompanyId());
            return;
        }
        try {
            LoginUserDto userDto = new LoginUserDto();
            userDto.setCusUser(new CusUserDto()
                    .setIsAdmin(true)
                    .setCompanyId(dingCorp.getCompanyId()));
            LoginUserThreadLocal.set(userDto);
            DingInventoryReceiverDto receiverDto = dingInventoryService.dingInventoryReceiver(event.getInventoryId(), null);
            if (receiverDto != null) {
                List<DingInventoryGroup> list = dingInventoryGroupService.list(Wrappers.lambdaQuery(DingInventoryGroup.class)
                        .eq(DingInventoryGroup::getCompanyId, event.getCompanyId())
                        .eq(DingInventoryGroup::getInventoryId, event.getInventoryId()));
                for (DingInventoryGroup inventoryGroup : list) {
                    // 首次添加到群会话
                    DingtalkCardMessageRequest request = new DingtalkCardMessageRequest(dingCorp.getCorpId(), inventoryGroup.getOpenConversationId());
                    request.setUserIds(ListUtil.of(receiverDto.getApprover()));

                    InventoryTaskInfoDto info = inventoryTaskService.getInfo(event.getTaskId());
                    if (info != null) {
                        request.getCardParamMap().put("taskId", Convert.toStr(info.getId()));
                        request.getCardParamMap().put("inventoryId", Convert.toStr(info.getInventoryId()));
                        request.getCardParamMap().put("taskName", info.getName());
                        request.getCardParamMap().put("inventoryUsers", info.getInventoryUserText());
                        request.getCardParamMap().put("finishTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_PD_APPROVE, request);
                    }
                }
            } else {
                log.warn("receiverDto is null, inventoryId = {}", event.getInventoryId());
            }
        } finally {
            LoginUserThreadLocal.remove();
        }
    }

}
