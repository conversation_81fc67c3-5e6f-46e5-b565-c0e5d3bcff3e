package com.niimbot.asset.inventory.adapter.ding.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryGroupDto;
import com.niimbot.asset.inventory.adapter.ding.model.DingInventoryGroup;
import com.niimbot.ding.DingInventorySaveCmd;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 16:59
 */
public interface DingInventoryGroupService extends IService<DingInventoryGroup> {

    String CHAT_DISBAND = "chat_disband";
    String CHAT_UPDATE_TITLE = "chat_update_title";

    List<String> groupNameList(Long inventoryId);

    List<DingInventoryGroupDto> listProcess();

    Boolean create(DingInventorySaveCmd saveCmd);

    Boolean chatCallback(JSONObject content);

    Boolean coolAppCallback(JSONObject content);

}
