package com.niimbot.asset.inventory.adapter.ding.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.model.DingMsgOutTrack;
import com.niimbot.asset.dingtalk.message.service.DingMsgOutTrackService;
import com.niimbot.asset.inventory.domain.event.UpdateApproverEvent;
import com.niimbot.asset.system.adapter.ding.service.DingEmployeeService;

import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/4/25 17:26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateApproverEventListener implements ApplicationListener<UpdateApproverEvent> {

    private final DingCorpService dingCorpService;
    private final DingMsgOutTrackService dingMsgOutTrackService;
    private final DingEmployeeService dingEmployeeService;

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    @Async
    public void onApplicationEvent(UpdateApproverEvent event) {
        DingCorp dingCorp = dingCorpService.getByCompanyId(event.getCompanyId());
        if (dingCorp == null) {
            log.error("关闭吊顶失败, 未找到企业授权信息, 企业id -> {}", event.getCompanyId());
            return;
        }

        // 查询钉钉用户ID
        Map<Long, String> dingUserIdMap = dingEmployeeService.dingUserIdMap(ListUtil.of(event.getSourceUserId()));
        List<String> userIds = new ArrayList<>(dingUserIdMap.values());
        if (CollUtil.isEmpty(userIds)) {
            return;
        }

        List<DingMsgOutTrack> outTrackList = dingMsgOutTrackService.list(Wrappers.lambdaQuery(DingMsgOutTrack.class)
                .eq(DingMsgOutTrack::getCorpId, dingCorp.getCorpId())
                .eq(DingMsgOutTrack::getBusinessId, event.getInventoryId())
                .eq(DingMsgOutTrack::getTrackType, TemplateTypeEnum.CARD_PD_APPROVER_TOPBOX.getCode())
                .in(DingMsgOutTrack::getUserId, userIds));

        dingMsgOutTrackService.closeUserMsg(dingCorp.getCorpId(), outTrackList);
    }

}
