package com.niimbot.asset.inventory.adapter.ding.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryGroupService;
import com.niimbot.ding.DingInventorySaveCmd;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/6 15:09
 */
@RestController
@RequestMapping("server/dingtalk/inventory/group")
@RequiredArgsConstructor
public class DingInventoryGroupServiceController {

    private final DingInventoryGroupService dingInventoryGroupService;

    @GetMapping("/groupNameList")
    public List<String> groupNameList(@RequestParam("inventoryId") Long inventoryId) {
        return dingInventoryGroupService.groupNameList(inventoryId);
    }

    @PostMapping
    public Boolean create(@RequestBody DingInventorySaveCmd saveCmd) {
        return dingInventoryGroupService.create(saveCmd);
    }

    @PostMapping("/event/chat")
    public Boolean chatCallback(@RequestBody JSONObject content) {
        return dingInventoryGroupService.chatCallback(content);
    }

    @PostMapping("/event/coolApp")
    public Boolean coolAppCallback(@RequestBody JSONObject content) {
        return dingInventoryGroupService.coolAppCallback(content);
    }

}
