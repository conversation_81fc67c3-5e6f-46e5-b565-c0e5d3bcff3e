package com.niimbot.asset.inventory.adapter.ding.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkim_2_0.models.CreateTopboxRequest;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.message.DingtalkMessageClient;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.request.card.DingtalkCardMessageRequest;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.adapter.ding.dto.CardPdApproverTopBoxDto;
import com.niimbot.asset.inventory.adapter.ding.dto.CardPdTaskTopBoxDto;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryReceiverDto;
import com.niimbot.asset.inventory.adapter.ding.model.DingInventoryGroup;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryGroupService;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryService;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryTaskService;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryAsset;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.service.AsInventoryAssetService;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.adapter.ding.service.DingEmployeeService;
import com.niimbot.ding.DingInventoryStatusDto;
import com.niimbot.ding.DingInventoryTaskListDto;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DingInventoryServiceImpl implements DingInventoryService {

    private static final String REMINDER = "inventory:reminder:";

    private String getReminderCache(String openConversationId, Long inventoryId) {
        return REMINDER + openConversationId + ":" + inventoryId;
    }

    private final CacheResourceUtil cacheResourceUtil;

    private final AsInventoryService inventoryService;

    private final AsInventoryTaskService inventoryTaskService;

    private final AssetService assetService;
    @Resource
    private AsInventoryAssetService inventoryAssetService;
    @Resource
    private RedisService redisService;
    @Resource
    private DingtalkMessageClient dingtalkMessageClient;
    @Resource
    private DingInventoryGroupService dingInventoryGroupService;
    @Resource
    private DingCorpService dingCorpService;

    private final DingEmployeeService dingEmployeeService;

    private final DingInventoryTaskService dingInventoryTaskService;

    @Override
    public Map<String, String> coolAppCheckAssetId(String assetId, Long inventoryId, Long inventoryTaskId) {
        // 资产库存不存在
        AsAsset asset = assetService.getInfoNoPermByCodePhp(assetId);

        if (asset == null) {
            return MapUtil.of("id", "-1");
        }

        AsInventoryAsset one = inventoryAssetService.getOne(Wrappers.lambdaQuery(AsInventoryAsset.class)
                .eq(AsInventoryAsset::getInventoryId, inventoryId)
                .eq(AsInventoryAsset::getInventoryTaskId, inventoryTaskId)
                .eq(AsInventoryAsset::getAssetId, asset.getId()));
        if (one != null) {
            return MapUtil.<String, String>builder()
                    .put("id", asset.getId() + "")
                    .put("epcid", "")
                    .build();
        } else {
            return MapUtil.<String, String>builder()
                    .put("id", asset.getId() + "")
                    .put("epcid", asset.getLabelEpcid())
                    .build();
        }
    }

    /**
     * 通过盘点单ID查询全部钉钉接收人（返回钉钉人员的unionId）
     *
     * @param inventoryId 盘点单ID
     * @return 接收人
     */
    @Override
    public DingInventoryReceiverDto dingInventoryReceiver(Long inventoryId, List<Integer> inventoryTaskStatus) {
        if (inventoryId == null) {
            return null;
        }
        AsInventory inventory = inventoryService.getById(inventoryId);
        if (inventory == null) {
            return null;
        }

        Set<Long> userIds = new HashSet<>();
        userIds.add(inventory.getApprover());
        userIds.add(inventory.getCreateBy());

        List<AsInventoryTask> taskList = inventoryTaskService.list(Wrappers.lambdaQuery(AsInventoryTask.class)
                .eq(AsInventoryTask::getInventoryId, inventory.getId())
                .in(CollUtil.isNotEmpty(inventoryTaskStatus), AsInventoryTask::getStatus, inventoryTaskStatus));
        taskList.forEach(task -> userIds.addAll(task.getInventoryUsers()));

        Map<Long, String> dingUserIdMap = dingEmployeeService.dingUserIdMap(new ArrayList<>(userIds));

        // 结果集
        DingInventoryReceiverDto dingInventoryReceiverDto = new DingInventoryReceiverDto();
        dingInventoryReceiverDto.setInventoryName(inventory.getName());

        dingInventoryReceiverDto.setSourceApprover(inventory.getApprover());
        dingInventoryReceiverDto.setApprover(dingUserIdMap.get(inventory.getApprover()));

        dingInventoryReceiverDto.setSourceCreateBy(inventory.getCreateBy());
        dingInventoryReceiverDto.setCreateBy(dingUserIdMap.get(inventory.getCreateBy()));

        taskList.forEach(task ->
                task.getInventoryUsers().forEach(user -> {
                    String dingUserId = dingUserIdMap.get(user);
                    // 写入钉钉盘点员ID
                    dingInventoryReceiverDto.getInventoryUsers().add(dingUserId);
                    // 写入钉钉与云资产员工映射
                    dingInventoryReceiverDto.getSourceInventoryUserMap().put(user, dingUserId);
                    // 写入钉钉员工与任务名称映射
                    List<String> taskName = dingInventoryReceiverDto.getInventoryUserTaskMap().getOrDefault(dingUserId, new ArrayList<>());
                    taskName.add(task.getName());
                    dingInventoryReceiverDto.getInventoryUserTaskMap().put(dingUserId, taskName);
                })
        );
        return dingInventoryReceiverDto;
    }

    @Override
    public DingInventoryStatusDto allStatus(String openConversationId) {
        DingInventoryGroup one = dingInventoryGroupService.getOne(Wrappers.lambdaQuery(DingInventoryGroup.class)
                .eq(DingInventoryGroup::getOpenConversationId, openConversationId), false);
        if (one != null) {
            AsInventory inventory = inventoryService.getById(one.getInventoryId());
            if (inventory == null) {
                throw new BusinessException(InventoryResultCode.INVENTORY_NOT_EXIST);
            }
            DingInventoryStatusDto statusDto = new DingInventoryStatusDto();
            statusDto.setApprover(inventory.getApprover());
            statusDto.setInventoryStatus(inventory.getStatus());
            statusDto.setHasReminder(ObjectUtil.isNotNull(redisService.get(getReminderCache(openConversationId, inventory.getId()))));
            return statusDto;
        }
        return null;
    }

    @Override
    public Boolean reminder(String openConversationId) {
        DingInventoryGroup group = dingInventoryGroupService.getOne(Wrappers.lambdaQuery(DingInventoryGroup.class)
                .eq(DingInventoryGroup::getOpenConversationId, openConversationId), false);
        if (group == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "群内没有关联的盘点单");
        }
        String reminderCache = getReminderCache(openConversationId, group.getInventoryId());
        if (ObjectUtil.isNull(redisService.get(reminderCache))) {
            DingCorp dingCorp = dingCorpService.getByCompanyId(group.getCompanyId());
            DingInventoryReceiverDto receiverDto = dingInventoryReceiver(group.getInventoryId(), ListUtil.of(InventoryConstant.IN_PROGRESS, InventoryConstant.REJECTED));
            if (receiverDto != null) {
                Map<String, List<String>> inventoryUserTaskMap = receiverDto.getInventoryUserTaskMap();
                if (CollUtil.isNotEmpty(inventoryUserTaskMap)) {
                    String sender = cacheResourceUtil.getUserName(LoginUserThreadLocal.getCurrentUserId());
                    String sendTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    for (Map.Entry<String, List<String>> entry : inventoryUserTaskMap.entrySet()) {
                        String userId = entry.getKey();
                        List<String> taskNames = entry.getValue();
                        if (CollUtil.isNotEmpty(taskNames)) {
                            DingtalkCardMessageRequest request = new DingtalkCardMessageRequest(dingCorp.getCorpId(), openConversationId);
                            request.setUserIds(ListUtil.of(userId));
                            request.getCardParamMap().put("taskName", String.join("，", taskNames));
                            request.getCardParamMap().put("sender", sender);
                            request.getCardParamMap().put("sendTime", sendTime);
                            dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_REMINDER, request);
                        }
                    }
                }
            }
            LocalDateTime start = LocalDateTime.now();
            LocalDateTime end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            Duration between = Duration.between(start, end);
            redisService.set(reminderCache, true, between.getSeconds());
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当日已提醒");
        }
        return true;
    }

    @Override
    public String cardReloadCallback(String templateType, JSONObject body) {
        String corpId = body.getString("corpId");
        DingCorp dingCorp = dingCorpService.getOne(Wrappers.lambdaQuery(DingCorp.class)
                .eq(DingCorp::getCorpId, corpId));
        if (dingCorp == null) {
            log.error("吊顶卡片回调失败, 未找到企业授权信息, 企业id -> {}", corpId);
            return null;
        }

        JSONObject params = body.getJSONObject("content").getJSONObject("cardPrivateData").getJSONObject("params");
        try {
            LoginUserDto userDto = new LoginUserDto();
            userDto.setCusUser(new CusUserDto()
                    .setIsAdmin(true)
                    .setCompanyId(dingCorp.getCompanyId()));
            LoginUserThreadLocal.set(userDto);

            if (TemplateTypeEnum.CARD_PD_TASK_TOPBOX.getCode().equals(templateType)) {
                String openConversationId = params.getString("openConversationId");
                Long taskId = params.getLong("taskId");
                if (ObjectUtil.isAllNotEmpty(openConversationId, taskId)) {
                    List<DingInventoryTaskListDto> taskList = dingInventoryTaskService.dingTaskList(openConversationId, taskId);
                    if (CollUtil.isNotEmpty(taskList)) {
                        DingInventoryTaskListDto dingInventoryTask = taskList.get(0);
                        CardPdApproverTopBoxDto topBoxDto = BeanUtil.copyProperties(dingInventoryTask, CardPdApproverTopBoxDto.class);
                        topBoxDto.setTaskName(dingInventoryTask.getName());
                        topBoxDto.setTaskId(dingInventoryTask.getId());
                        JSONObject json = (JSONObject) JSONObject.toJSON(topBoxDto);
                        json.put("openConversationId", openConversationId);
                        json.put("corpId", corpId);
                        json.put("inventoryId", dingInventoryTask.getInventoryId());
                        CreateTopboxRequest.CreateTopboxRequestCardData cardData = new CreateTopboxRequest.CreateTopboxRequestCardData()
                                .setCardParamMap(Convert.toMap(String.class, String.class, json));
                        CreateTopboxRequest createTopboxRequest = new CreateTopboxRequest();
                        createTopboxRequest.setCardData(cardData);
                        return JSONObject.toJSONString(createTopboxRequest);
                    }
                }
            } else if (TemplateTypeEnum.CARD_PD_APPROVER_TOPBOX.getCode().equals(templateType)) {
                String openConversationId = params.getString("openConversationId");
                Long inventoryId = params.getLong("inventoryId");
                if (ObjectUtil.isAllNotEmpty(openConversationId, inventoryId)) {
                    CardPdTaskTopBoxDto inventoryTopBoxDto = dingInventoryTaskService.dingInventoryTopBox(inventoryId);
                    if (inventoryTopBoxDto != null) {
                        JSONObject json = (JSONObject) JSONObject.toJSON(inventoryTopBoxDto);
                        json.put("openConversationId", openConversationId);
                        json.put("corpId", corpId);
                        CreateTopboxRequest.CreateTopboxRequestCardData cardData = new CreateTopboxRequest.CreateTopboxRequestCardData()
                                .setCardParamMap(Convert.toMap(String.class, String.class, json));
                        CreateTopboxRequest createTopboxRequest = new CreateTopboxRequest();
                        createTopboxRequest.setCardData(cardData);
                        return JSONObject.toJSONString(createTopboxRequest);
                    }
                }
            }
        } finally {
            LoginUserThreadLocal.remove();
        }
        return null;
    }

}
