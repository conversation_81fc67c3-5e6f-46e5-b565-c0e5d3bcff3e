package com.niimbot.asset.inventory.adapter.ding.listener;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.message.DingtalkMessageClient;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.model.DingMsgOutTrack;
import com.niimbot.asset.dingtalk.message.request.card.DingtalkCardMessageRequest;
import com.niimbot.asset.dingtalk.message.request.card.DingtalkTopBoxMessageRequest;
import com.niimbot.asset.dingtalk.message.service.DingMsgOutTrackService;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.adapter.ding.dto.CardPdApproverTopBoxDto;
import com.niimbot.asset.inventory.adapter.ding.dto.CardPdTaskTopBoxDto;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryReceiverDto;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryService;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryTaskService;
import com.niimbot.asset.inventory.domain.event.CreateInventoryEvent;
import com.niimbot.ding.DingInventoryTaskListDto;

import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/4/13 10:57
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreateInventoryEventListener implements ApplicationListener<CreateInventoryEvent> {

    @Resource
    private DingCorpService dingCorpService;

    @Resource
    private DingtalkMessageClient dingtalkMessageClient;

    @Resource
    private DingInventoryService dingInventoryService;

    @Resource
    private DingOpenApiService dingOpenApiService;

    @Resource
    private DingInventoryTaskService dingInventoryTaskService;

    @Resource
    private DingMsgOutTrackService dingMsgOutTrackService;

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    @Async
    public void onApplicationEvent(CreateInventoryEvent event) {
        DingCorp dingCorp = dingCorpService.getByCompanyId(event.getCompanyId());
        if (dingCorp == null) {
            log.error("创建盘点通知工作通知发送失败, 未找到企业授权信息, 企业id -> {}", event.getCompanyId());
            return;
        }
        try {
            LoginUserDto userDto = new LoginUserDto();
            userDto.setCusUser(new CusUserDto()
                    .setIsAdmin(true)
                    .setCompanyId(dingCorp.getCompanyId()));
            LoginUserThreadLocal.set(userDto);

            // 添加回调
            dingOpenApiService.registerCallback(dingCorp.getCorpId(), TemplateTypeEnum.CARD_PD_TASK_TOPBOX.getCode());
            dingOpenApiService.registerCallback(dingCorp.getCorpId(), TemplateTypeEnum.CARD_PD_APPROVER_TOPBOX.getCode());
            DingInventoryReceiverDto receiverDto = dingInventoryService.dingInventoryReceiver(event.getInventoryId(), null);
            if (receiverDto != null) {
                if (BooleanUtil.isTrue(event.getFirstLink())) {
                    // 首次添加到群会话
                    DingtalkCardMessageRequest request = new DingtalkCardMessageRequest(dingCorp.getCorpId(), event.getOpenConversationId());
                    Set<String> sender = new HashSet<>(receiverDto.getInventoryUsers());
                    sender.add(receiverDto.getApprover());
                    sender.add(receiverDto.getCreateBy());
                    request.setUserIds(sender.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
                    // 首次添加群
                    dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_PD_COOL_INSTALL, request);

                    // 酷应用关联
                    request.getCardParamMap().put("inventoryId", Convert.toStr(event.getInventoryId()));
                    dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_PD_COOL_RELATED, request);
                }

                // 发送每个任务的盘点员发送吊顶
                List<DingInventoryTaskListDto> inventoryTaskList = dingInventoryTaskService.dingTaskList(event.getOpenConversationId(), null);
                if (CollUtil.isNotEmpty(receiverDto.getInventoryUsers()) && CollUtil.isNotEmpty(inventoryTaskList)) {
                    List<DingMsgOutTrack> outTrackList = new ArrayList<>();
                    for (DingInventoryTaskListDto taskListDto : inventoryTaskList) {
                        List<Long> inventoryUsers = taskListDto.getInventoryUsers();
                        List<String> dingUsers = inventoryUsers.stream().map(u ->
                                receiverDto.getSourceInventoryUserMap().get(u)
                        ).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(dingUsers)) {
                            // 判断发送人
                            DingtalkTopBoxMessageRequest topBoxRequest = new DingtalkTopBoxMessageRequest(dingCorp.getCorpId(), event.getOpenConversationId());
                            topBoxRequest.setOutTrackId(IdUtils.getId().toString());
                            topBoxRequest.setUserIds(dingUsers);
                            topBoxRequest.setBusinessId(taskListDto.getId());
                            CardPdApproverTopBoxDto topBoxDto = BeanUtil.copyProperties(taskListDto, CardPdApproverTopBoxDto.class);
                            topBoxDto.setTaskName(taskListDto.getName());
                            topBoxDto.setTaskId(taskListDto.getId());
                            JSONObject json = (JSONObject) JSONObject.toJSON(topBoxDto);
                            topBoxRequest.getCardParamMap().putAll(Convert.toMap(String.class, String.class, json));
                            topBoxRequest.getCardParamMap().put("taskId", Convert.toStr(taskListDto.getId()));
                            topBoxRequest.getCardParamMap().put("inventoryId", Convert.toStr(event.getInventoryId()));
                            Boolean bool = dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_PD_TASK_TOPBOX, topBoxRequest);
                            if (bool) {
                                for (String dingUser : topBoxRequest.getUserIds()) {
                                    // 记录卡片发送数据
                                    DingMsgOutTrack outTrack = new DingMsgOutTrack();
                                    outTrack.setOutTrackId(topBoxRequest.getOutTrackId());
                                    outTrack.setOpenConversationId(topBoxRequest.getOpenConversationId());
                                    outTrack.setUserId(dingUser);
                                    outTrack.setTrackType(TemplateTypeEnum.CARD_PD_TASK_TOPBOX.getCode());
                                    outTrack.setBusinessId(taskListDto.getId());
                                    outTrack.setCorpId(topBoxRequest.getCorpId());
                                    outTrackList.add(outTrack);
                                }
                            }
                        }
                    }
                    dingMsgOutTrackService.updateByOpenConversationId(dingCorp.getCorpId(),
                            event.getOpenConversationId(),
                            TemplateTypeEnum.CARD_PD_TASK_TOPBOX.getCode(),
                            outTrackList);
                }

                // 给审核人创建人发送吊顶
                CardPdTaskTopBoxDto inventoryTopBoxDto = dingInventoryTaskService.dingInventoryTopBox(event.getInventoryId());
                if (inventoryTopBoxDto != null) {
                    List<DingMsgOutTrack> outTrackList = new ArrayList<>();
                    DingtalkTopBoxMessageRequest topBoxRequest = new DingtalkTopBoxMessageRequest(dingCorp.getCorpId(), event.getOpenConversationId());
                    topBoxRequest.setBusinessId(event.getInventoryId());
                    Set<String> senderTopBox = new HashSet<>();
                    senderTopBox.add(receiverDto.getApprover());
                    senderTopBox.add(receiverDto.getCreateBy());
                    List<String> dingUsers = senderTopBox.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(dingUsers)) {
                        topBoxRequest.setOutTrackId(IdUtils.getId().toString());
                        topBoxRequest.setUserIds(dingUsers);
                        topBoxRequest.setBusinessId(event.getInventoryId());
                        JSONObject json = (JSONObject) JSONObject.toJSON(inventoryTopBoxDto);
                        topBoxRequest.getCardParamMap().putAll(Convert.toMap(String.class, String.class, json));
                        Boolean bool = dingtalkMessageClient.sendCardMessage(TemplateTypeEnum.CARD_PD_APPROVER_TOPBOX, topBoxRequest);
                        if (bool) {
                            for (String dingUser : topBoxRequest.getUserIds()) {
                                // 记录卡片发送数据
                                DingMsgOutTrack outTrack = new DingMsgOutTrack();
                                outTrack.setOutTrackId(topBoxRequest.getOutTrackId());
                                outTrack.setOpenConversationId(topBoxRequest.getOpenConversationId());
                                outTrack.setUserId(dingUser);
                                outTrack.setTrackType(TemplateTypeEnum.CARD_PD_APPROVER_TOPBOX.getCode());
                                outTrack.setBusinessId(event.getInventoryId());
                                outTrack.setCorpId(topBoxRequest.getCorpId());
                                outTrackList.add(outTrack);
                            }
                        }
                    }
                    dingMsgOutTrackService.updateByOpenConversationId(dingCorp.getCorpId(),
                            event.getOpenConversationId(),
                            TemplateTypeEnum.CARD_PD_APPROVER_TOPBOX.getCode(),
                            outTrackList);
                }
            } else {
                log.warn("receiverDto is null, inventoryId = {}", event.getInventoryId());
            }
        } finally {
            LoginUserThreadLocal.remove();
        }
    }

}
