package com.niimbot.asset.inventory.adapter.ding.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryReceiverDto;
import com.niimbot.ding.DingInventoryStatusDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DingInventoryService {

    Map<String, String> coolAppCheckAssetId(String assetId, Long inventoryId, Long inventoryTaskId);

    /**
     * 通过盘点单ID查询全部钉钉接收人（返回钉钉人员的unionId）
     *
     * @param inventoryId 盘点单ID
     * @return 接收人
     */
    DingInventoryReceiverDto dingInventoryReceiver(Long inventoryId, List<Integer> inventoryTaskStatus);

    DingInventoryStatusDto allStatus(String openConversationId);

    Boolean reminder(String openConversationId);

    String cardReloadCallback(String templateType, JSONObject body);

}
