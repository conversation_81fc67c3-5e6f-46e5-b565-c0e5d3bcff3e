package com.niimbot.asset.inventory.adapter.ding.mapper;

import com.niimbot.ding.DingInventoryTaskListDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DingInventoryTaskMapper {

    List<DingInventoryTaskListDto> dingTaskList(@Param("companyId") Long companyId,
                                                @Param("openConversationId") String openConversationId,
                                                @Param("taskId") Long taskId);

    ;
}
