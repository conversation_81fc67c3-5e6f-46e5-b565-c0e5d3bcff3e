package com.niimbot.asset.inventory.adapter.ding.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.ding.DingInventoryStatusDto;
import com.niimbot.ding.DingInventoryStatusQry;
import com.niimbot.inventory.InventoryManualDto;
import com.niimbot.jf.logging.annotation.ApiLogging;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiLogging
@RestController
@RequestMapping("server/dingtalk/inventory")
@RequiredArgsConstructor
public class DingInventoryServiceController {

    private final DingInventoryService dingInventoryService;
    private final AsInventoryTaskService inventoryTaskService;

    /**
     * app手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    @PutMapping(value = "/task/appManualInventory")
    public Boolean appManualInventory(@RequestBody InventoryManualDto dto) {
        return inventoryTaskService.manualInventory(dto);
    }

    @GetMapping("/asset/coolAppCheckAssetId")
    public Map<String, String> coolAppCheckAssetId(
            @RequestParam("assetId") String assetId, @RequestParam("inventoryId") Long inventoryId, @RequestParam("inventoryTaskId") Long inventoryTaskId) {
        return dingInventoryService.coolAppCheckAssetId(assetId, inventoryId, inventoryTaskId);
    }

    @PostMapping(value = "/allStatus")
    public DingInventoryStatusDto allStatus(@RequestBody DingInventoryStatusQry qry) {
        return dingInventoryService.allStatus(qry.getOpenConversationId());
    }

    @PostMapping(value = "/reminder")
    public Boolean reminder(@RequestBody DingInventoryStatusQry qry) {
        return dingInventoryService.reminder(qry.getOpenConversationId());
    }

    @PostMapping("/cardReload/{templateType}")
    public String cardReloadCallback(@PathVariable("templateType") String templateType,
                                     @RequestBody JSONObject body) {
        return dingInventoryService.cardReloadCallback(templateType, body);
    }
}
