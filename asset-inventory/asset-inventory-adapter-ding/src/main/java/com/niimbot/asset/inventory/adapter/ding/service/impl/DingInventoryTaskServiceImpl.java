package com.niimbot.asset.inventory.adapter.ding.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.inventory.adapter.ding.dto.CardPdTaskTopBoxDto;
import com.niimbot.asset.inventory.adapter.ding.mapper.DingInventoryTaskMapper;
import com.niimbot.asset.inventory.adapter.ding.service.DingInventoryTaskService;
import com.niimbot.asset.inventory.mapper.OnlineInventoryTaskMapper;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.ding.DingInventoryTaskListDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/10 11:44
 */
@Service
@RequiredArgsConstructor
public class DingInventoryTaskServiceImpl implements DingInventoryTaskService {

    private final DingInventoryTaskMapper dingInventoryTaskMapper;
    private final OnlineInventoryTaskMapper onlineInventoryTaskMapper;
    private final AsInventoryService inventoryService;
    private final AsInventoryTaskService inventoryTaskService;

    @Override
    public List<DingInventoryTaskListDto> dingTaskList(String openConversationId, Long taskId) {
        List<DingInventoryTaskListDto> taskList = dingInventoryTaskMapper.dingTaskList(LoginUserThreadLocal.getCompanyId(),
                openConversationId, taskId);
        fillPdData(taskList);
        return taskList;
    }

    private void fillPdData(List<DingInventoryTaskListDto> taskList) {
        List<Long> taskIds = taskList.stream().map(DingInventoryTaskListDto::getId).collect(Collectors.toList());
        // 补齐未盘，已盘，盘盈在册，盘盈不在册
        if (CollUtil.isNotEmpty(taskIds)) {
            Long inventoryId = taskList.get(0).getInventoryId();

            // 查询未盘已盘
            List<DingInventoryTaskListDto> assetCount = onlineInventoryTaskMapper.assetCount(inventoryId, taskIds);
            Map<Long, DingInventoryTaskListDto> assetCountMap = assetCount.stream()
                    .collect(Collectors.toMap(DingInventoryTaskListDto::getId, k -> k));

            // 查询盘盈在册，盘盈不在册
            List<DingInventoryTaskListDto> countAddAsset = onlineInventoryTaskMapper.countAddAsset(inventoryId, taskIds);
            Map<Long, DingInventoryTaskListDto> countAddAssetMap = countAddAsset.stream()
                    .collect(Collectors.toMap(DingInventoryTaskListDto::getId, k -> k));

            taskList.forEach(task -> {
                if (assetCountMap.containsKey(task.getId())) {
                    DingInventoryTaskListDto assetCountDto = assetCountMap.get(task.getId());
                    task.setNoCheckedNum(assetCountDto.getNoCheckedNum());
                    task.setCheckedNum(assetCountDto.getCheckedNum());
                }

                if (countAddAssetMap.containsKey(task.getId())) {
                    DingInventoryTaskListDto countAddAssetDto = countAddAssetMap.get(task.getId());
                    task.setCheckedAddInMarkNum(countAddAssetDto.getCheckedAddInMarkNum());
                    task.setCheckedAddNotInMarkNum(countAddAssetDto.getCheckedAddNotInMarkNum());
                }
            });
        }
    }

    @Override
    public CardPdTaskTopBoxDto dingInventoryTopBox(Long inventoryId) {
        AsInventory inventory = inventoryService.getById(inventoryId);
        if (inventory == null) {
            return null;
        }

        CardPdTaskTopBoxDto topBoxDto = new CardPdTaskTopBoxDto();
        topBoxDto.setInventoryId(inventory.getId());
        topBoxDto.setInventoryName(inventory.getName());
        // 盘点任务
        List<AsInventoryTask> taskList = inventoryTaskService.list(Wrappers.lambdaQuery(AsInventoryTask.class)
                .eq(AsInventoryTask::getInventoryId, inventoryId));
        // 全部任务数量
        topBoxDto.setAllTask(taskList.size());
        taskList.forEach(task -> {
            // 未完成的
            if (!task.getStatus().equals(InventoryConstant.COMPLETED)) {
                topBoxDto.setNoFinishTask(topBoxDto.getNoFinishTask() + 1);
            } else {
                topBoxDto.setFinishTask(topBoxDto.getFinishTask() + 1);
            }
        });

        // 查询未盘已盘
        List<DingInventoryTaskListDto> assetCount = onlineInventoryTaskMapper.assetCount(inventoryId, null);
        assetCount.forEach(count -> {
            topBoxDto.setCheckedNum(topBoxDto.getCheckedNum() + Convert.toInt(count.getCheckedNum()));
            topBoxDto.setNoCheckedNum(topBoxDto.getNoCheckedNum() + Convert.toInt(count.getNoCheckedNum()));
        });
        return topBoxDto;
    }

}
