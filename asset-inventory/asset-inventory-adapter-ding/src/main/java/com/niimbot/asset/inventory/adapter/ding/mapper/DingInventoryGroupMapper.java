package com.niimbot.asset.inventory.adapter.ding.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryGroupDto;
import com.niimbot.asset.inventory.adapter.ding.model.DingInventoryGroup;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 16:51
 */
@EnableDataPerm
public interface DingInventoryGroupMapper extends BaseMapper<DingInventoryGroup> {

    AsInventory inProcess(@Param("openConversationId") String openConversationId,
                          @Param("coolAppCode") String coolAppCode);

    /**
     * 查询进行中的盘点群信息
     *
     * @return dto
     */
    List<DingInventoryGroupDto> listProcess();
}
