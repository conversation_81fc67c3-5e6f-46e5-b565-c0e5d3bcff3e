package com.niimbot.asset.inventory.adapter.ding.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.inventory.adapter.ding.dto.CardPdTaskTopBoxDto;
import com.niimbot.ding.DingInventoryQueryDto;
import com.niimbot.ding.DingInventoryTaskListDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/10 11:44
 */
public interface DingInventoryTaskService {

    List<DingInventoryTaskListDto> dingTaskList(String openConversationId, Long taskId);

    CardPdTaskTopBoxDto dingInventoryTopBox(Long inventoryId);

}
