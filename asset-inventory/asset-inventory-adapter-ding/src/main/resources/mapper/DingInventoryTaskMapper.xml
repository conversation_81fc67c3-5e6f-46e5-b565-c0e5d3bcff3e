<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.adapter.ding.mapper.DingInventoryTaskMapper">

    <resultMap id="DingInventoryTaskListDto" type="com.niimbot.ding.DingInventoryTaskListDto">
        <id column="id" property="id"/>
        <result column="inventory_id" property="inventoryId"/>
        <result column="name" property="name"/>
        <result column="inventory_users" property="inventoryUsers"
                typeHandler="com.niimbot.asset.inventory.handle.InventoryIntegerLongTypeHandler"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="enable_take_picture" property="enableTakePicture"/>
        <result column="enable_manual" property="enableManual"/>
        <result column="enable_update_data" property="enableUpdateData"/>
        <result column="enable_no_perm_look" property="enableNoPermLook"/>
    </resultMap>

    <select id="dingTaskPage" resultMap="DingInventoryTaskListDto">
        SELECT
        t.id,
        t.inventory_id,
        t.name,
        t.inventory_users,
        t.`status`,
        i.create_time,
        c.enable_take_picture,
        c.enable_manual,
        c.enable_update_data,
        c.enable_no_perm_look
        FROM
        as_inventory_task AS t
        JOIN as_inventory AS i ON i.id = t.inventory_id and i.status in (1, 2, 5)
        JOIN ding_inventory_group g on g.inventory_id = i.id and g.company_id = i.company_id
        JOIN as_inventory_config AS c ON c.id = i.id
        where
        i.company_id = #{companyId}
        and g.open_conversation_id = #{ew.openConversationId}
        <if test="ew.kw!=null and ew.kw!=''">
            AND t.name like concat('%',#{ew.kw},'%')
        </if>
        <if test="ew.status != null and ew.status != '-1'.toString()">
            and t.status = #{ew.status}
        </if>
        <!-- 审核人 -->
        <if test="ew.inventoryUser != null">
            and JSON_CONTAINS(t.inventory_users, JSON_ARRAY(#{ew.inventoryUser}))
        </if>
    </select>

    <select id="dingTaskList" resultMap="DingInventoryTaskListDto">
        SELECT
        t.id,
        t.inventory_id,
        t.name,
        t.inventory_users,
        t.`status`,
        i.create_time
        FROM
        as_inventory_task AS t
        JOIN as_inventory AS i ON i.id = t.inventory_id
        JOIN ding_inventory_group g on g.inventory_id = i.id and g.company_id = i.company_id
        where
        i.company_id = #{companyId}
        and g.open_conversation_id = #{openConversationId}
        <if test="taskId!=null">
            and t.id = #{taskId}
        </if>
    </select>

</mapper>
