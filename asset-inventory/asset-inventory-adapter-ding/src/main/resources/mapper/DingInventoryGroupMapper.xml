<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.inventory.adapter.ding.mapper.DingInventoryGroupMapper">

    <select id="inProcess" resultType="com.niimbot.asset.inventory.model.AsInventory">
        select i.id, i.status
        from ding_inventory_group g
        join as_inventory i on g.inventory_id = i.id and g.company_id = i.company_id
        where g.open_conversation_id = #{openConversationId}
            and g.cool_app_code = #{coolAppCode}
         limit 1
    </select>

    <select id="listProcess"
            resultType="com.niimbot.asset.inventory.adapter.ding.dto.DingInventoryGroupDto">
        SELECT
            g.company_id,
            c.corp_id,
            g.inventory_id,
            g.open_conversation_id
        FROM
            ding_inventory_group g
            JOIN as_inventory i ON g.company_id = i.company_id
            AND g.inventory_id = i.id
            AND i.STATUS IN ( 1, 2, 5 )
            JOIN ding_corp c ON g.company_id = c.company_id
    </select>

</mapper>
