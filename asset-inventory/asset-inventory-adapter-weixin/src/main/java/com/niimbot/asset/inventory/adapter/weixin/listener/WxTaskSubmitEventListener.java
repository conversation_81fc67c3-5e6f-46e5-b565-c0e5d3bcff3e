package com.niimbot.asset.inventory.adapter.weixin.listener;

import com.google.common.collect.Lists;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.inventory.domain.event.TaskSubmitEvent;
import com.niimbot.asset.inventory.model.AsInventory;
import com.niimbot.asset.inventory.service.AsInventoryService;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.system.ots.SystemEmployeeOts;
import com.niimbot.asset.weixin.base.constant.Jumps;
import com.niimbot.asset.weixin.base.constant.WxConstant;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.inventory.InventoryTaskInfoDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.templatecard.HorizontalContent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/15 15:33
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WxTaskSubmitEventListener implements ApplicationListener<TaskSubmitEvent> {

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Resource
    private AsInventoryService inventoryService;

    @Resource
    private AsInventoryTaskService inventoryTaskService;

    @Resource
    private SystemEmployeeOts systemEmployeeService;

    @Resource
    private WxOpenApiService openApiService;

    @Override
    @Async("assetTaskExecutor")
    public void onApplicationEvent(@Nonnull TaskSubmitEvent event) {
        try {
            AsInventory inventory = inventoryService.getById(event.getInventoryId());
            InventoryTaskInfoDto task = inventoryTaskService.getInfo(event.getTaskId());
            if (Objects.isNull(inventory) || Objects.isNull(task)) {
                return;
            }
            /*
             * XX发起的盘点任务请您审核
             * 盘点单名称：620盘点
             * 盘点审核人：XX
             */
            ArrayList<HorizontalContent> contents = Lists.newArrayList(
                    HorizontalContent.builder()
                            .keyname("盘点单名称：")
                            .value(inventory.getName())
                            .build(),
                    HorizontalContent.builder()
                            .keyname("盘点审核人：")
                            .value(WxConstant.getContactEmpName(systemEmployeeService.getEmpExternalId(inventory.getApprover())))
                            .build()
            );
            WxCpMessage message = WxCpMessage.TEMPLATECARD()
                    .toUser(String.join("|", systemEmployeeService.getEmpExternalIds(Collections.singletonList(inventory.getApprover()))))
                    .mainTitleTitle("盘点消息")
                    .mainTitleDesc(WxConstant.getContactEmpName(systemEmployeeService.getEmpExternalId(task.getInventoryUser())) + "发起的盘点任务请您审核")
                    .horizontalContents(contents)
                    .build();
            openApiService.sendTplCardTextNoticeMessage(event.getCompanyId(), message, Jumps.INVENTORY_APPROVE, Lists.newArrayList(event.getInventoryId(), event.getTaskId()));
        } catch (Exception e) {
            log.error("发送盘点审批微信消息失败", e);
        }
    }

}
