package com.niimbot.asset.inventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.inventory.model.AsInventoryTask;
import com.niimbot.inventory.InventoryManualDto;
import com.niimbot.inventory.InventoryQueryDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySyncAddAndSubmitDto;
import com.niimbot.inventory.InventorySyncAssetDto;
import com.niimbot.inventory.InventoryTaskAddUserDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.inventory.InventoryTaskInfoDto;
import com.niimbot.inventory.InventoryTaskListDto;
import com.niimbot.inventory.InventoryTaskUpdateUserDto;
import com.niimbot.inventory.InventoryTaskUpdateUserInfoDto;
import com.niimbot.inventory.InventoryTaskView;

import java.util.List;

/**
 * <p>
 * 盘点任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
public interface AsInventoryTaskService extends IService<AsInventoryTask> {

    /**
     * 盘点任务分页列表
     *
     * @param dto
     * @return
     */
    IPage<InventoryTaskListDto> selectList(InventoryQueryDto dto);

    IPage<InventoryTaskListDto> selectListApp(InventoryQueryDto dto);

    /**
     * 提交审核
     *
     * @param approveDto approveDto
     * @return 结果
     */
    Boolean submit(InventoryTaskApproveDto approveDto);

    /**
     * 手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    Boolean manualInventory(InventoryManualDto dto);

    /**
     * 资产上报
     *
     * @param dto
     * @return 结果
     */
    Boolean reportAsset(InventorySurplusDto dto);

    /**
     * 编辑上报资产
     *
     * @param dto
     * @return 结果
     */
    Boolean editReportAsset(InventorySurplusDto dto);

    /**
     * 删除上报资产
     *
     * @param id
     * @return 结果
     */
    Boolean removeReportAsset(Long id);

    /**
     * 批量删除上报资产信息
     *
     * @param ids 资产IDs
     * @return 结果
     */
    Boolean removeReportAssets(List<Long> ids);

    /**
     * 同步上传盘点资产数据
     *
     * @param dto
     * @return 结果
     */
    Boolean syncUploadTaskAsset(InventorySyncAssetDto dto);

    /**
     * 盘点任务详情
     *
     * @param id 盘点任务id
     * @return 结果
     */
    InventoryTaskInfoDto getInfo(Long id);

    /**
     * 同步新增资产+提交审核
     *
     * @param dto 盘盈资产数据
     * @return 结果
     */
    Boolean syncAddAndSubmit(InventorySyncAddAndSubmitDto dto);

    Boolean checkInventory(Long userId);

    List<AsInventoryTask> listForSendTaskTimeoutMessage(Long companyId, List<Integer> days);

    InventoryTaskView getInventoryTaskView(Long id);

    /**
     * 查询盘点任务盘点人
     *
     * @param taskId
     * @return
     */
    InventoryTaskUpdateUserInfoDto getInventoryUsers(Long taskId);

    /**
     * 重新分配盘点人
     *
     * @param updateUserDto
     * @return
     */
    Boolean updateInventoryUsers(InventoryTaskUpdateUserDto updateUserDto);

    Boolean addInventoryUsers(InventoryTaskAddUserDto addUserDto);
}
