package com.niimbot.asset.inventory.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.inventory.handle.InventoryIntegerLongTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 盘点任务
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInventoryTask对象", description="盘点任务")
@TableName(value = "as_inventory_task", autoResultMap = true)
public class AsInventoryTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点任务id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "资产数量")
    private Integer assetNum;

    @ApiModelProperty(value = "已盘数量")
    private Integer checkedNum;

//    @ApiModelProperty(value = "盘点人")
//    private Long inventoryUser;

    @ApiModelProperty(value = "盘点人")
    @TableField(typeHandler = InventoryIntegerLongTypeHandler.class)
    private List<Long> inventoryUsers;

    @ApiModelProperty(value = "状态（1：进行中，2：待审核，3：已完成，4：已终止, 5: 被驳回）")
    private Integer status;

    @ApiModelProperty(value = "提交任务时间")
    private LocalDateTime lastSubmitTime;

    @ApiModelProperty(value = "审核任务时间")
    private LocalDateTime lastApproveTime;

    @ApiModelProperty(value = "任务类型 1-盘点任务  2-全员盘点任务")
    private Integer taskType;


}
