package com.niimbot.asset.dingtalk.storage.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.*;
import com.niimbot.asset.dingtalk.storage.config.OssCallbackParam;
import com.niimbot.asset.dingtalk.storage.config.OssCallbackResult;
import com.niimbot.asset.dingtalk.storage.config.OssConfig;
import com.niimbot.asset.dingtalk.storage.config.OssPolicyResult;
import com.niimbot.asset.dingtalk.storage.service.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;

import static com.niimbot.asset.dingtalk.storage.util.FilePathUtils.resolvePath;

/**
 * created by chen.y on 2021/4/6 10:35
 */
@Slf4j
@Service
public class OssServiceImpl implements OssService {

    private final OssConfig ossConfig;

    private final Environment environment;

    @Autowired
    public OssServiceImpl(OssConfig ossConfig, Environment environment) {
        this.ossConfig = ossConfig;
        this.environment = environment;
    }

    @Override
    public OssPolicyResult policy(String bucket, int expire, String filePath, String dirPattern) {
        OssPolicyResult ossPolicyResult = new OssPolicyResult();
        // 存储目录
        String dir = resolvePath(filePath, dirPattern);
        // 签名有效期
        long expireEndTime = System.currentTimeMillis() + NumberUtil.min(expire, ossConfig.getExpire()) * 1000L;
        Date expiration = DateUtil.date(expireEndTime);
        // 文件大小
        int maxSize = ossConfig.getMaxSize() * 1024 * 1024;
        // 回调
        OssCallbackParam ossCallbackParam = new OssCallbackParam();
        ossCallbackParam.setCallbackUrl(ossConfig.getCallback());
        ossCallbackParam.setCallbackBody("filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}");
        ossCallbackParam.setCallbackBodyType("application/x-www-form-urlencoded");
        String action = "https://" + bucket + "." + ossConfig.getHost();
        try {
            PolicyConditions policyConditions = new PolicyConditions();
            policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, maxSize);
            policyConditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);
            OSSClient ossClient = new OSSClient(ossConfig.getHost(), ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
            String postPolicy = ossClient.generatePostPolicy(expiration, policyConditions);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String policy = BinaryUtil.toBase64String(binaryData);
            String signature = ossClient.calculatePostSignature(postPolicy);
            String callbackData = BinaryUtil.toBase64String(JSONUtil.parse(ossCallbackParam).toString().getBytes(StandardCharsets.UTF_8));
            // 返回结果
            ossPolicyResult.setAccessid(ossClient.getCredentialsProvider().getCredentials().getAccessKeyId());
            ossPolicyResult.setPolicy(policy);
            ossPolicyResult.setSignature(signature);
            ossPolicyResult.setDir(dir);
            ossPolicyResult.setCallback(callbackData);
            ossPolicyResult.setHost(action);
        } catch (Exception e) {
            log.error("签名生成失败", e);
        }
        return ossPolicyResult;
    }

    /**
     * oss上传成功回调
     *
     * @param request
     * @return
     */
    @Override
    public OssCallbackResult callback(HttpServletRequest request) {
        String bucket = request.getParameter("bucket");
        OssCallbackResult ossCallbackResult = new OssCallbackResult();
        String filename = request.getParameter("filename");
        filename = "http://".concat(bucket).concat(".").concat(ossConfig.getHost()).concat("/").concat(filename);
        ossCallbackResult.setFilename(filename);
        ossCallbackResult.setSize(request.getParameter("size"));
        ossCallbackResult.setMimeType(request.getParameter("mimeType"));
        ossCallbackResult.setWidth(request.getParameter("width"));
        ossCallbackResult.setHeight(request.getParameter("height"));
        return ossCallbackResult;
    }

    @Override
    public String putFile(String key, String filePath) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(ossConfig.getBucket(), key, new File(filePath));
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
        putObjectRequest.setMetadata(metadata);
        // 测试内网
        boolean pro = Arrays.asList(environment.getActiveProfiles()).contains("dingtalk");
        String endpoint = pro ? "oss-cn-zhangjiakou-internal.aliyuncs.com" : "oss-cn-zhangjiakou.aliyuncs.com";
        OSS client = new OSSClientBuilder().build(endpoint, ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
        client.putObject(putObjectRequest);
        client.shutdown();
        if (!key.startsWith("/")) {
            key = "/" + key;
        }
        return "https://" + ossConfig.getBucket() + "." + ossConfig.getHost() + key;
    }

}
