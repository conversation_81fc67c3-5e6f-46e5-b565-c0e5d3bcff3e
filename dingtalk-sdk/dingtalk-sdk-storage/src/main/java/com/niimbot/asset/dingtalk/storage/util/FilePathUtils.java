package com.niimbot.asset.dingtalk.storage.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/12 3:18 下午
 */
@Slf4j
public final class FilePathUtils {
    private FilePathUtils() {
    }

    public static String resolvePath(String basePath, String dirPattern) {
        if (!basePath.endsWith(File.separator)) {
            basePath = basePath + File.separator;
        }
        //不需要按规则生成目录 , 直接存储在指定的目录下即可
        if (StrUtil.isBlank(dirPattern)) {
            return basePath;
        }
        //添加目录后缀
        String subfix = "";
        try {
            subfix = DateUtil.format(new Date(), dirPattern);
        } catch (Exception e) {
            log.error("格式化文件夹路径: {} 出错,使用指定的路径:{}", dirPattern, basePath);
        }
        return basePath + resolvePath(subfix);
    }


    private static String resolvePath(String subfix) {
        String[] var = StrUtil.split(subfix, " ");
        return String.join(File.separator, var);
    }


}
