package com.niimbot.asset.dingtalk.storage.service;


import com.niimbot.asset.dingtalk.storage.config.OssCallbackResult;
import com.niimbot.asset.dingtalk.storage.config.OssPolicyResult;

import javax.servlet.http.HttpServletRequest;

public interface OssService {

    /**
     * oss 上传生成策略
     *
     * @param filePath : 文件路径
     * @return
     */
    OssPolicyResult policy(String bucket, int expire, String filePath, String dirPattern);

    /**
     * oss上传成功回调
     *
     * @param request
     * @return
     */
    OssCallbackResult callback(HttpServletRequest request);

    String putFile(String key, String filePath);
}

