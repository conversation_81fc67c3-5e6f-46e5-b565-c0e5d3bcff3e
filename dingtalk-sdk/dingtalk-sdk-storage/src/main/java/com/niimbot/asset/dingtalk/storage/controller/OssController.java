package com.niimbot.asset.dingtalk.storage.controller;

import com.niimbot.asset.dingtalk.storage.config.OssCallbackResult;
import com.niimbot.asset.dingtalk.storage.config.OssConfig;
import com.niimbot.asset.dingtalk.storage.config.OssPolicyResult;
import com.niimbot.asset.dingtalk.storage.service.OssService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.io.File;

/**
 * OSS 控制层 created by chen.y on 2021/4/6 9:37
 */
@Api(tags = {"客户端文件相关接口"})
@ResultController
@RequestMapping("api/dingtalk/oss")
public class OssController {

    private final OssService ossService;

    private final OssConfig ossConfig;

    @Autowired
    public OssController(OssService ossService, OssConfig ossConfig) {
        this.ossService = ossService;
        this.ossConfig = ossConfig;
    }

    @ApiOperation(value = "【钉钉】获取token")
    @GetMapping("/policy")
    public OssPolicyResult policy(@ApiParam(name = "module", value = "模块名称")
                                  @RequestParam(value = "module") String module,
                                  @ApiParam(name = "business", value = "业务名称")
                                  @RequestParam(value = "business", required = false) String business,
                                  @ApiParam(name = "dirPattern", value = "文件夹生成后缀，如yyyy-MM-dd")
                                  @RequestParam(value = "dirPattern", required = false, defaultValue = "") String dirPattern) {
        try {
            String filePath = module + File.separator + business;
            return ossService.policy(
                    ossConfig.getBucket(),
                    ossConfig.getExpire(),
                    filePath,
                    dirPattern
            );
        } catch (Exception e) {
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "获取Oss policy失败");
        }
    }

    /**
     * oss上传成功回调
     *
     * @param request
     * @return
     */

    @PostMapping("/callback")
    public OssCallbackResult callback(HttpServletRequest request) {
        OssCallbackResult ossCallbackResult = ossService.callback(request);
        return ossCallbackResult;
    }


}
