package com.niimbot.asset.dingtalk.storage.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "oss")
public class OssConfig {

    private String host;

    private String accessKeyId;

    private String accessKeySecret;

    /**
     * token过期时间, 单位(秒)
     */
    private int expire;

    private String bucket;

    /**
     * 单位 (MB)
     */
    private int maxSize;

    private String callback;
}
