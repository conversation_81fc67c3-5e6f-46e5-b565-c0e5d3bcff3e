package com.niimbot.asset.dingtalk.connector.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ConnectEvent", description = "发送钉钉连接器事件")
public class ConnectEvent implements Serializable {

    @ApiModelProperty("自定义连接器ID即唯一编码")
    private String connectorCode;

    @ApiModelProperty("自定义触发器ID即唯一编码")
    private String triggerCode;

    @ApiModelProperty("符合连接器事件定义的数据模型")
    private Object data;

    @ApiModelProperty("动作：add-增加；delete-删除；update-更新")
    private String action;

    public interface Action {
        String ADD = "add";
        String UPD = "update";
        String DEL = "delete";
    }

    public interface Code {
        String MISC = "misc";
        String ADD_MEANS = "add_means";
    }

}
