package com.niimbot.asset.dingtalk.connector;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkconnector_1_0.Client;
import com.aliyun.dingtalkconnector_1_0.models.SyncDataHeaders;
import com.aliyun.dingtalkconnector_1_0.models.SyncDataRequest;
import com.aliyun.dingtalkconnector_1_0.models.SyncDataResponse;
import com.aliyun.dingtalkconnector_1_0.models.SyncDataResponseBody;
import com.aliyun.teautil.models.RuntimeOptions;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.config.CorpTokenManage;
import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.dingtalk.base.restops.RestOps;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.support.QuietTask;
import com.niimbot.asset.dingtalk.connector.dataobject.DingConnectorConfig;
import com.niimbot.asset.dingtalk.connector.mapper.DingConnectorConfigMapper;
import com.niimbot.asset.dingtalk.connector.model.ConnectorProperties;
import com.niimbot.asset.dingtalk.connector.model.ConnectEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DingConnector implements ApplicationListener<ApplicationStartedEvent> {

    @Resource(name = "connectorClient")
    private Client client;

    @Resource
    private CorpTokenManage corpTokenManage;

    @Resource
    private DingCorpService corpService;

    @Resource
    private DingConnectorConfigMapper configMapper;

    private final Map<String, String> codeIdMap = new ConcurrentHashMap<>(2);

    private final Map<String, String> triggerCodeIdMap = new ConcurrentHashMap<>(4);

    /**
     * 发送钉钉连接器事件
     *
     * @param events 事件集
     */
    public void sendEvents(Long companyId, ConnectEvent... events) {
        QuietTask.syncExecute(() -> {
            if (Objects.isNull(events) || events.length == 0) {
                return;
            }
            SyncDataHeaders headers = new SyncDataHeaders().setXAcsDingtalkAccessToken(corpTokenManage.getV1CorpAccessToken(corpService.getByCompanyId(companyId).getCorpId()));
            SyncDataRequest request = new SyncDataRequest();
            request.setAppId(SdkConstant.APP_ID);
            List<SyncDataRequest.SyncDataRequestTriggerDataList> triggers = Arrays.stream(events)
                    .filter(v -> codeIdMap.containsKey(v.getConnectorCode()) && triggerCodeIdMap.containsKey(v.getConnectorCode() + ":" + v.getTriggerCode()))
                    .map(v -> {
                        SyncDataRequest.SyncDataRequestTriggerDataList data = new SyncDataRequest.SyncDataRequestTriggerDataList();
                        data.setAction(v.getAction());
                        data.setDataGmtCreate(System.currentTimeMillis());
                        data.setDataGmtModified(System.currentTimeMillis());
                        data.setJsonData(JSONObject.toJSONString(v.getData()));
                        data.setCustomTriggerId(v.getConnectorCode() + ":" + v.getTriggerCode());
                        data.setTriggerId(triggerCodeIdMap.get(v.getConnectorCode() + ":" + v.getTriggerCode()));
                        data.setIntegrationObject(null);
                        data.setTriggerCondition(null);
                        return data;
                    }).collect(Collectors.toList());
            request.setTriggerDataList(triggers);
            log.info("send ding connector event request [{}]", JSONObject.toJSONString(request));
            SyncDataResponse response = RestOps.handleOrThrow(() -> client.syncDataWithOptions(request, headers, new RuntimeOptions()));
            List<SyncDataResponseBody.SyncDataResponseBodyList> body = response.getBody().getList();
        });
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        ConfigurableApplicationContext context = event.getApplicationContext();
        List<DingConnectorConfig> configs = configMapper.selectList(Wrappers.emptyWrapper());
        configs.forEach(v -> {
            List<ConnectorProperties> triggerConfigs = v.triggerConfigs();
            codeIdMap.put(v.getCode(), v.getId());
            triggerConfigs.forEach(t -> triggerCodeIdMap.put(v.getCode() + ":" + t.getCode(), t.getId()));
        });
    }
}
