package com.niimbot.asset.dingtalk.connector.dataobject;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.asset.dingtalk.connector.model.ConnectorProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors
@TableName(value = "ding_connector", autoResultMap = true)
public class DingConnectorConfig {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String code;

    private String description;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray triggers;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray actions;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject auth;

    public List<ConnectorProperties> triggerConfigs() {
        return CollUtil.isEmpty(triggers) ? Collections.emptyList() : triggers.toJavaList(ConnectorProperties.class);
    }

    public List<ConnectorProperties> actionConfigs() {
        return CollUtil.isEmpty(actions) ? Collections.emptyList() : actions.toJavaList(ConnectorProperties.class);
    }

}
