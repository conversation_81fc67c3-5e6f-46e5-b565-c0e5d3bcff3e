<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dingtalk.org.mapper.DingAssetMapper">

    <resultMap id="AsAssetMap" type="com.niimbot.means.AssetDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="product_id" property="productId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="attr_structure" property="attrStructure"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="before_status" property="beforeStatus"/>
        <result column="repair_before_status" property="repairBeforeStatus"/>
        <result column="label_tid" property="labelTid"/>
        <result column="label_epcid" property="labelEpcid"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
    </resultMap>

    <select id="selectUseOrgAsset" resultMap="AsAssetMap">
        SELECT t.id,t.asset_data FROM as_asset t
        WHERE t.asset_data ->> '$.useOrg' = CONVERT(#{orgId},CHAR) and t.company_id = #{companyId} and t.is_delete
        = 0
    </select>

    <select id="selectOrgOwnerAsset" resultMap="AsAssetMap">
        SELECT t.id,t.asset_data FROM as_asset t
        WHERE t.asset_data ->> '$.orgOwner' = CONVERT(#{orgId},CHAR) and t.company_id = #{companyId} and
        t.is_delete = 0
    </select>

    <select id="checkUseOrg" resultType="java.lang.Long">
        SELECT t.asset_data ->> '$.useOrg' as use_org FROM as_asset t
        WHERE t.asset_data ->> '$.useOrg' in
        <foreach item="item" index="index" collection="orgIds" open="(" separator=","
                 close=")">
            CONVERT(#{item},CHAR)
        </foreach>
        and t.company_id = #{companyId} and t.is_delete = 0
    </select>

    <select id="checkOrgOwner" resultType="java.lang.Long">
        SELECT t.asset_data ->> '$.orgOwner' as use_org FROM as_asset t
        WHERE t.asset_data ->> '$.orgOwner' in
        <foreach item="item" index="index" collection="orgIds" open="(" separator=","
                 close=")">
            CONVERT(#{item},CHAR)
        </foreach>
        and t.company_id = #{companyId} and t.is_delete = 0
    </select>

    <select id="checkUsePerson" resultType="java.lang.Long">
        SELECT t.asset_data ->> '$.usePerson' as use_person FROM as_asset t
        WHERE CONVERT(t.asset_data ->> '$.usePerson', DECIMAL(19)) in
        <foreach item="item" index="index" collection="userIds" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
        and t.company_id = #{companyId} and t.is_delete = 0
    </select>

    <select id="checkManagerOwner" resultType="java.lang.Long">
        SELECT t.asset_data ->> '$.managerOwner' as manager_owner FROM as_asset t
        WHERE CONVERT(t.asset_data ->> '$.managerOwner', DECIMAL(19)) in
        <foreach item="item" index="index" collection="userIds" open="(" separator=","
                 close=")">
            CONVERT(#{item},CHAR)
        </foreach>
        and t.company_id = #{companyId} and t.is_delete = 0
    </select>

</mapper>
