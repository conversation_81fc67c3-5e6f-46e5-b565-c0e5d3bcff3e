<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dingtalk.org.mapper.AsShoppingCartMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.dingtalk.base.model.AsShoppingCart">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="goods_type" jdbcType="INTEGER" property="goodsType" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="goods_value" jdbcType="INTEGER" property="goodsValue" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="ProductResultMap" type="com.niimbot.asset.dingtalk.base.dto.ShoppingCartProductDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="goods_type" jdbcType="INTEGER" property="goodsType" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="goods_value" jdbcType="INTEGER" property="goodsValue" />
  </resultMap>

  <sql id="Base_Column_List">
    id, company_id, user_id, goods_type, sku_code, sku_name, unit_price, price, `count`, goods_value, `status`,
    is_delete, create_time, update_time
  </sql>

  <select id="selectShoppingCart" resultMap="ProductResultMap" parameterType="java.lang.Long">
    select id, goods_type, sku_code, sku_name, unit_price, price, `count`, goods_value
    from as_shopping_cart
    where user_id = #{userId} and is_delete = 0
    order by create_time desc
  </select>

  <select id="productCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
    select sum(count)
    from as_shopping_cart
    where user_id = #{userId} and is_delete = 0
  </select>

</mapper>