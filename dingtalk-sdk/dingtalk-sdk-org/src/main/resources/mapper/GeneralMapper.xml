<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dingtalk.org.mapper.GeneralMapper">

    <select id="allAdminUser" resultType="com.niimbot.asset.system.model.AsCusUser">
    select pe.user_id as account, c.id as company_id from as_company c
            join as_cus_role r on (c.id = r.company_id and r.role_code = 'admin' )
            join as_user_role ur on r.id = ur.role_id
            join as_third_party_employee pe on (ur.user_id = pe.employee_id and c.id = pe.company_id)
    </select>

    <select id="insideSku" resultType="java.lang.String">
        select ding_sku from ding_sku_ref where sku_type = 2 and is_delete = 0 limit 1
    </select>

    <select id="getResourceConfigByGoodsCode"
            resultType="com.niimbot.asset.sale.model.AsResourceConfig">
        SELECT
            rc.*
        FROM
            ding_sku_ref sr
            JOIN as_resource_config rc ON sr.sku_biz_code = rc.biz_code
        WHERE
            sr.is_delete = 0
            and sr.sku_type = 1
            AND rc.is_delete = 0
            AND sr.ding_sku = #{goodsCode}
        limit 1
    </select>

    <select id="getDingSkuDuration" resultType="java.lang.Integer">
        select ding_sku_duration from ding_sku_ref where is_delete = 0 and ding_sku = #{goodsCode}
    </select>

    <select id="selectDingSkuByGoodsSku" resultType="java.lang.String">
        SELECT
            sr.ding_sku
        FROM
            ding_sku_ref sr
            JOIN as_resource_config rc ON sr.sku_biz_code = rc.biz_code
        WHERE
            sr.is_delete = 0
          and sr.sku_type = 1
          AND rc.is_delete = 0
          AND rc.sku_code = #{skuCode}
        limit 1
    </select>
</mapper>
