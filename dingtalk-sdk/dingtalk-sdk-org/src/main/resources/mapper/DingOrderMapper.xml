<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dingtalk.org.mapper.DingOrderMapper">

    <select id="getResourceIds" resultType="java.lang.Long">
        select cr.id from as_company_resource cr
        join as_sale_order so on cr.company_id = so.company_id and cr.sale_order_id = so.id
        join ding_order o on so.company_id = o.company_id and o.out_trade_no = so.order_no
        where o.company_id = #{companyId}
        and o.source_data not like '%appId%'
    </select>

</mapper>
