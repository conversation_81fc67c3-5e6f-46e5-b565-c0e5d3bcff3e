<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dingtalk.org.mapper.AsShoppingCartBonusMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.dingtalk.base.model.AsShoppingCartBonus">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="main_product_id" jdbcType="BIGINT" property="mainProductId" />
    <result column="discount_code" jdbcType="VARCHAR" property="discountCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="goods_value" jdbcType="INTEGER" property="goodsValue" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="BonusResultMap" type="com.niimbot.asset.dingtalk.base.dto.ShoppingCartBonusDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="main_product_id" jdbcType="BIGINT" property="mainProductId" />
    <result column="discount_code" jdbcType="VARCHAR" property="discountCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="goods_value" jdbcType="INTEGER" property="goodsValue" />
  </resultMap>
  <sql id="Base_Column_List">
    id, main_product_id, discount_code, sku_code, sku_name, price, `count`, goods_value, is_delete,
    create_time, update_time
  </sql>

  <select id="selectByMainProductId" resultMap="BonusResultMap">
    select id, main_product_id, discount_code, sku_code, sku_name, price, `count`, goods_value
    from as_shopping_cart_bonus
    <where>
        is_delete = 0
        <if test="mainProductIdList != null and mainProductIdList.size() > 0">
            and main_product_id in
            <foreach collection="mainProductIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </where>
  </select>

</mapper>