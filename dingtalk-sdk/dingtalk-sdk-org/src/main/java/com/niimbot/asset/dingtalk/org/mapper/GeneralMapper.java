package com.niimbot.asset.dingtalk.org.mapper;

import com.niimbot.asset.sale.model.AsResourceConfig;
import com.niimbot.asset.system.model.AsCusUser;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GeneralMapper {

    List<AsCusUser> allAdminUser();

    String insideSku();

    AsResourceConfig getResourceConfigByGoodsCode(@Param("goodsCode") String goodsCode);

    Integer getDingSkuDuration(@Param("goodsCode") String goodsCode);

    /**
     * 根据中台sku查询钉钉sku
     * @param skuCode
     * @return
     */
    String selectDingSkuByGoodsSku(@Param("skuCode") String skuCode);
}
