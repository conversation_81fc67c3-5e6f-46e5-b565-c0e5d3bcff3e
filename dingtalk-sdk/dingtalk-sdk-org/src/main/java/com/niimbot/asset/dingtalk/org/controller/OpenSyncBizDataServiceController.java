package com.niimbot.asset.dingtalk.org.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.dingtalk.base.model.OpenSyncBizData;
import com.niimbot.asset.dingtalk.base.model.OpenSyncBizDataMedium;
import com.niimbot.asset.dingtalk.org.service.OpenSyncBizDataMediumService;
import com.niimbot.asset.dingtalk.org.service.OpenSyncBizDataService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * created by chen.y on 2021/8/23 9:48
 */
@RestController
@RequestMapping("server/dingtalk/bizData")
public class OpenSyncBizDataServiceController {

    private final OpenSyncBizDataService bizDataService;

    private final OpenSyncBizDataMediumService bizDataMediumService;

    public OpenSyncBizDataServiceController(OpenSyncBizDataService bizDataService,
                                            OpenSyncBizDataMediumService bizDataMediumService) {
        this.bizDataService = bizDataService;
        this.bizDataMediumService = bizDataMediumService;
    }

    @PostMapping
    public Boolean saveOrUpdateBizData(@RequestBody OpenSyncBizData bizData) {
        bizDataService.remove(new LambdaQueryWrapper<OpenSyncBizData>()
                .eq(OpenSyncBizData::getSubscribeId, bizData.getSubscribeId())
                .eq(OpenSyncBizData::getCorpId, bizData.getCorpId())
                .eq(OpenSyncBizData::getBizId, bizData.getBizId())
                .eq(OpenSyncBizData::getBizType, bizData.getBizType()));
        return bizDataService.save(bizData);
    }

    @PostMapping("/medium")
    public Boolean saveOrUpdateBizDataMedium(@RequestBody OpenSyncBizDataMedium bizData) {
        bizDataMediumService.remove(new LambdaQueryWrapper<OpenSyncBizDataMedium>()
                .eq(OpenSyncBizDataMedium::getSubscribeId, bizData.getSubscribeId())
                .eq(OpenSyncBizDataMedium::getCorpId, bizData.getCorpId())
                .eq(OpenSyncBizDataMedium::getBizId, bizData.getBizId())
                .eq(OpenSyncBizDataMedium::getBizType, bizData.getBizType()));
        return bizDataMediumService.save(bizData);
    }

}
