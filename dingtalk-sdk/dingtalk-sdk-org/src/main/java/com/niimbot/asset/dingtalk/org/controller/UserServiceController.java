package com.niimbot.asset.dingtalk.org.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.org.service.DingtalkContactsService;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.event.CompanyRegisterEvent;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusRoleDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import cn.hutool.core.collection.CollUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * created by chen.y on 2021/8/31 11:02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("server/dingtalk/user")
public class UserServiceController {

    private final DingCorpService dingCorpService;
    private final CusUserService userService;
    private final CusUserService accountUserService;
    private final AsAccountEmployeeService accountEmployeeService;
    private final AsCusEmployeeService employeeService;
    private final AsCusEmployeeExtService employeeExtService;
    private final CusRoleService employeeRoleService;
    private final DingtalkContactsService contactsService;

    @GetMapping(value = "/getByUnionId")
    public CusUserDto selectUserByUnionId(@RequestParam("unionId") String unionId,
                                          @RequestParam("corpId") String corpId) {
        DingCorp one = dingCorpService.getOne(new LambdaQueryWrapper<DingCorp>().eq(DingCorp::getCorpId, corpId));
        if (ObjectUtil.isNotNull(one)) {
            Long companyId = one.getCompanyId();
            AsCusUser account = accountUserService.getOne(
                    Wrappers.lambdaQuery(AsCusUser.class)
                            .eq(AsCusUser::getUnionId, unionId));
            if (Objects.isNull(account)) {
                return null;
            }
            // 账号是否关联了员工
            AsAccountEmployee accountEmployee = accountEmployeeService.getOne(
                    Wrappers.lambdaQuery(AsAccountEmployee.class)
                            .eq(AsAccountEmployee::getAccountId, account.getId())
                            .eq(AsAccountEmployee::getCompanyId, companyId));

            if (ObjectUtil.isNull(accountEmployee)) {
                return null;
            }

            AsCusEmployee employee = employeeService.getById(accountEmployee.getEmployeeId());

            // 账号未激活
            AsCusEmployeeExt ext = employeeExtService.getById(accountEmployee.getEmployeeId());
            if (ext.getAccountStatus() == 1) {
                throw new BusinessException(SystemResultCode.EMP_INACTIVE_ACCOUNT);
            }
            return buildUserInfo(account, employee);
        } else {
            return null;
        }
    }

    private CusUserDto buildUserInfo(AsCusUser account, AsCusEmployee employee) {
        if (Objects.isNull(account) || Objects.isNull(employee)) {
            return null;
        }
        CusUserDto dto = new CusUserDto()
                // 账户信息
                .setAccountId(account.getId()).setAccount(account.getAccount()).setUnionId(account.getUnionId()).setPassword(account.getPassword())
                .setEmail(account.getEmail()).setStatus(account.getStatus())
                .setAgreementStatus(account.getAgreementStatus()).setGuideStatus(account.getGuideStatus())
                // 员工信息
                .setId(employee.getId()).setCompanyId(employee.getCompanyId()).setMobile(employee.getMobile()).setDataScope(employee.getDataScope());
        AsCusEmployeeExt employeeExt = employeeExtService.getById(employee.getId());
        if (Objects.nonNull(employeeExt)) {
            // 员工扩展信息
            dto.setDefaultTagId(employeeExt.getDefaultTagId())
                    .setDefaultCftagId(employeeExt.getDefaultCftagId())
                    .setDefaultCftagCode(employeeExt.getDefaultCftagCode())
                    .setDefaultTplId(employeeExt.getDefaultTplId());
        }
        List<CusRoleDto> roles = employeeRoleService.getRoleByEmployeeId(employee.getId());
        if (!CollUtil.isEmpty(roles)) {
            // 员工角色超管标识
            List<String> roleNames = roles.stream().map(CusRoleDto::getRoleCode).collect(Collectors.toList());
            dto.setIsAdmin(roleNames.contains(BaseConstant.ADMIN_ROLE));
        }
        return dto;
    }

    @GetMapping(value = "/{id}")
    public AsCusUser selectUserById(@PathVariable("id") Long id) {
        return userService.getById(id);
    }

    @GetMapping(value = "/company/{unionId}")
    public List<DingCorp> getCompanyByUnionId(@PathVariable("unionId") String unionId) {
        List<AsCusUser> cusUserList = userService.list(new LambdaQueryWrapper<AsCusUser>().eq(AsCusUser::getUnionId, unionId));
        List<Long> accountIds = cusUserList.stream().map(AsCusUser::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(accountIds)) {
            return new ArrayList<>();
        }
        List<AsAccountEmployee> list = accountEmployeeService.list(new LambdaQueryWrapper<AsAccountEmployee>()
                .in(AsAccountEmployee::getAccountId, accountIds));
        List<Long> companyIds = list.stream().map(AsAccountEmployee::getCompanyId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(companyIds)) {
            return dingCorpService.list(new LambdaQueryWrapper<DingCorp>()
                    .select(DingCorp::getCompanyId, DingCorp::getCorpId, DingCorp::getCorpLogoUrl, DingCorp::getCorpName, DingCorp::getFullCorpName)
                    .in(DingCorp::getCompanyId, companyIds));
        } else {
            return new ArrayList<>();
        }
    }

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean user(@RequestParam("corpId") String corpId,
                        @RequestParam("account") String account,
                        @RequestBody JSONObject content) {
        content.put(DingtalkContactsService.Constant.BIZ_ID, account);
        return contactsService.handleUserEvent(corpId, content);
    }

    @PutMapping
    public Boolean update(@RequestBody AsCusUser user) {
        userService.updateById(user);
        AsAccountEmployee one = accountEmployeeService.getOne(new LambdaQueryWrapper<AsAccountEmployee>()
                .eq(AsAccountEmployee::getAccountId, user.getId())
                .eq(AsAccountEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        if (ObjectUtil.isNotNull(one)) {
            employeeService.update(new LambdaUpdateWrapper<AsCusEmployee>()
                    .set(AsCusEmployee::getMobile, user.getMobile())
                    .set(StrUtil.isNotBlank(user.getEmail()), AsCusEmployee::getEmail, user.getEmail())
                    .eq(AsCusEmployee::getId, one.getEmployeeId()));
        }
        EventPublishHandler.publish(new CompanyRegisterEvent(one.getCompanyId()).setRegisterMobile(user.getMobile()));
        return true;
    }

}
