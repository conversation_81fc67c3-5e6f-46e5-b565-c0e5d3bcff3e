package com.niimbot.asset.dingtalk.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.dingtalk.base.dto.ShoppingCartBonusDto;
import com.niimbot.asset.dingtalk.base.model.AsShoppingCartBonus;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:07
 */
public interface ShoppingCartBonusService extends IService<AsShoppingCartBonus> {

    /**
     * 根据购物车商品id查询对应赠品信息
     * @param mainId
     * @return
     */
    List<ShoppingCartBonusDto> queryByMainId(List<Long> mainId);
}
