package com.niimbot.asset.dingtalk.org.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dingtalk.base.dto.*;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingSyncChange;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.support.QuietTask;
import com.niimbot.asset.dingtalk.message.DingtalkMessageClient;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.request.template.DingtalkMsgOaMessageRequest;
import com.niimbot.asset.dingtalk.org.mapper.DingAssetMapper;
import com.niimbot.asset.dingtalk.org.mapper.DingSyncChangeMapper;
import com.niimbot.asset.dingtalk.org.service.DingSyncChangeService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDto;
import com.niimbot.system.CusEmployeeTransferDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.RemoveEmployDto;
import org.checkerframework.checker.units.qual.Temperature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Service
public class DingSyncChangeServiceImpl extends ServiceImpl<DingSyncChangeMapper, DingSyncChange> implements DingSyncChangeService {

    private final OrgService orgService;

    private final AsEmployeeChangeService employeeChangeService;

    private final AsEmployeeAssetChangeService employeeAssetChangeService;

    private final AssetService assetService;

    private final DingAssetMapper dingAssetMapper;

    private final AsAssetLogService assetLogService;

    @Resource
    @Lazy
    private AsCusEmployeeTransferService transferService;

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Resource
    private DingtalkMessageClient dingtalkMessageClient;

    @Resource
    private DingCorpService dingCorpService;

    @Resource
    private CusMenuService menuService;

    @Resource
    private AsRoleMenuService roleMenuService;

    @Resource
    private CusUserRoleService userRoleService;

    @Resource
    private CusRoleService roleService;

    @Resource
    private AsThirdPartyEmployeeService thirdPartyEmployeeService;

    @Resource
    private AsUserOrgService userOrgService;

    @Resource
    private AsCusEmployeeService employeeService;

    @Autowired
    public DingSyncChangeServiceImpl(OrgService orgService,
                                     AsEmployeeChangeService employeeChangeService,
                                     AsEmployeeAssetChangeService employeeAssetChangeService,
                                     AssetService assetService,
                                     DingAssetMapper dingAssetMapper,
                                     AsAssetLogService assetLogService) {
        this.orgService = orgService;
        this.employeeChangeService = employeeChangeService;
        this.employeeAssetChangeService = employeeAssetChangeService;
        this.assetService = assetService;
        this.dingAssetMapper = dingAssetMapper;
        this.assetLogService = assetLogService;
    }

    @Override
    public List<DingSyncChangeDto> listChange(Integer type, Integer status) {
        return this.getBaseMapper().list(type, status);
    }

    @Override
    public DingSyncChangeEmpDto getEmp(Long id) {
        DingSyncChange change = this.getById(id);

        List<Long> fromOrg = change.getFromOrg();
        List<Long> toOrg = change.getToOrg();
        if (CollUtil.isEmpty(toOrg) && change.getType() == 1) {
            toOrg = userOrgService.list(
                    Wrappers.lambdaQuery(AsUserOrg.class)
                            .eq(AsUserOrg::getUserId, change.getResId())
            ).stream().map(AsUserOrg::getOrgId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(toOrg)) {
                AsOrg rootOrg = orgService.getRootOrg(change.getCompanyId());
                toOrg = Collections.singletonList(rootOrg.getId());
            }
        }
        List<OrgDto> toOrgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(toOrg)) {
            if (change.getType() == 1) {
                fromOrg.removeIf(toOrg::contains);
            }
            List<AsOrg> list = orgService.listAllByIds(toOrg);
            toOrgList = list.stream().map(it -> BeanUtil.copyProperties(it, OrgDto.class)).collect(Collectors.toList());
        }
        List<OrgDto> fromOrgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(fromOrg)) {
            List<AsOrg> list = orgService.listAllByIds(fromOrg);
            fromOrgList = list.stream().map(it -> BeanUtil.copyProperties(it, OrgDto.class)).collect(Collectors.toList());
        }
        AsCusEmployee emp = this.getBaseMapper().getEmp(change.getResId(), LoginUserThreadLocal.getCompanyId());
        DingSyncChangeEmpDto changeEmpDto = new DingSyncChangeEmpDto();
        return changeEmpDto.setEmpId(emp.getId())
                .setEmpName(emp.getEmpName())
                .setEmpNo(emp.getEmpNo())
                .setFromOrg(fromOrgList).setToOrg(toOrgList)
                .setIsHandlerDone(change.getStatus() == 2);
    }

    @Override
    public AsOrg getOrg(Long id) {
        return this.getBaseMapper().getOrg(id, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferEmpEdit(EmpTransferDto empTransferDto) {
        DingSyncChange dingSyncChange = this.getOne(new LambdaQueryWrapper<DingSyncChange>()
                .eq(DingSyncChange::getId, empTransferDto.getId())
                .eq(DingSyncChange::getStatus, 1));
        if (ObjectUtil.isNull(dingSyncChange)) {
            throw new BusinessException(ThirdPartyResultCode.SYNC_CHANGE_NOT_FOUND);
        }
        List<TransferDto> transfers = empTransferDto.getTransfer();
        // 写入异动信息
        AsEmployeeChange employeeChange = new AsEmployeeChange();
        employeeChange.setId(IdUtils.getId());
        employeeChange.setType(DictConstant.CHANGE_TYPE_EDIT).setEmpId(dingSyncChange.getResId());

        List<AsOrg> allOrglist = orgService.list();
        Map<Long, String> orgMap = allOrglist.stream().collect(Collectors.toMap(AsOrg::getId, AsOrg::getOrgName, (k1, k2) -> k1));

        // 组织变更
        List<Long> fromOrg = dingSyncChange.getFromOrg();
        List<Long> toOrg = dingSyncChange.getToOrg();
        StringBuilder orgExplain = new StringBuilder();
        orgExplain.append("部门由“").append(fromOrg.stream().map(v -> orgMap.getOrDefault(v, cacheResourceUtil.getOrgName(v) + "（已删除）")).collect(Collectors.joining("，")));
        orgExplain.append("”变成“").append(toOrg.stream().map(v -> orgMap.getOrDefault(v, cacheResourceUtil.getOrgName(v) + "（已删除）")).collect(Collectors.joining("，")));
        orgExplain.append("”");
        employeeChange.setOrgExplain(orgExplain.toString());

        // 资产变更
        List<String> assetExplain = new ArrayList<>();
        for (TransferDto transfer : transfers) {
            StringBuilder buffer = new StringBuilder();
            String formName = orgMap.get(transfer.getFrom());
            String toName = orgMap.get(transfer.getTo());
            if (StrUtil.isBlank(formName) || StrUtil.isBlank(toName)) {
                transfer.setAllowTrans(false);
                buffer.append(orgMap.getOrDefault(transfer.getFrom(), cacheResourceUtil.getOrgName(transfer.getFrom()) + "（已删除）"))
                        .append("资产转移到")
                        .append(orgMap.getOrDefault(transfer.getTo(), cacheResourceUtil.getOrgName(transfer.getTo()) + "（已删除）"));
            } else {
                buffer.append(formName).append("资产转移到").append(toName);
                transfer.setAllowTrans(true);
            }
            assetExplain.add(buffer.toString());
        }
        if (CollUtil.isNotEmpty(assetExplain)) {
            employeeChange.setAssetExplain(String.join(",", assetExplain));
        } else {
            employeeChange.setAssetExplain("无");
        }
        // 转移记录
        employeeChangeService.save(employeeChange);
        this.update(new LambdaUpdateWrapper<DingSyncChange>().set(DingSyncChange::getStatus, 2)
                .eq(DingSyncChange::getId, dingSyncChange.getId()));
        // 转移资产
        List<CusEmployeeTransferDto> collect = transfers.stream()
                .filter(TransferDto::getAllowTrans)
                .map(it -> BeanUtil.copyProperties(it, CusEmployeeTransferDto.class))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            List<AsOrg> orglist = orgService.list();
            assetService.editAssetUseOrg(employeeChange.getId(), dingSyncChange.getResId(), collect, orglist);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferEmpDelete(Long id, RemoveEmployDto employ) {
        DingSyncChange dingSyncChange = this.getOne(new LambdaQueryWrapper<DingSyncChange>()
                .eq(DingSyncChange::getId, id)
                .eq(DingSyncChange::getStatus, 1));
        if (ObjectUtil.isNull(dingSyncChange)) {
            throw new BusinessException(ThirdPartyResultCode.SYNC_CHANGE_NOT_FOUND);
        }
        transferService.transferEmp(BeanUtil.copyProperties(employ, RemoveEmployDto.class));
        this.update(new LambdaUpdateWrapper<DingSyncChange>().set(DingSyncChange::getStatus, 2)
                .eq(DingSyncChange::getId, id));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferOrgDelete(OrgTransferDto orgTransferDto) {
        DingSyncChange dingSyncChange = this.getOne(new LambdaQueryWrapper<DingSyncChange>()
                .eq(DingSyncChange::getId, orgTransferDto.getId())
                .eq(DingSyncChange::getStatus, 1));
        if (ObjectUtil.isNull(dingSyncChange)) {
            throw new BusinessException(ThirdPartyResultCode.SYNC_CHANGE_NOT_FOUND);
        }

        Long changeId = IdUtils.getId();
        Long orgId = orgTransferDto.getOrgId();

        List<String> assetExplainList = new ArrayList<>();
        List<AsAssetLog> assetLogs = new ArrayList<>();

        // 转移使用组织
        if (orgTransferDto.getUseOrg() != null) {
            Long to = orgTransferDto.getUseOrg();
            List<AsAssetLog> assetLogList = assetUseOrgTransfer(changeId, orgId, to);
            assetLogs.addAll(assetLogList);
            String orgName = cacheResourceUtil.getOrgName(to);
            if (StrUtil.isNotEmpty(orgName)) {
                assetExplainList.add("使用资产转移到" + orgName);
            }
        }

        // 转移所属管理组织
        if (orgTransferDto.getOrgOwner() != null) {
            Long to = orgTransferDto.getOrgOwner();
            List<AsAssetLog> assetLogList = assetOrgOwnerTransfer(changeId, orgId, to);
            assetLogs.addAll(assetLogList);
            String orgName = cacheResourceUtil.getOrgName(to);
            if (StrUtil.isNotEmpty(orgName)) {
                assetExplainList.add("管理资产转移到" + orgName);
            }
        }

        Map<Long, AsAssetLog> assetLogMap = new HashMap<>();
        for (AsAssetLog assetLog : assetLogs) {
            Long assetId = assetLog.getAssetId();
            AsAssetLog asAssetLog = assetLogMap.get(assetId);
            if (asAssetLog == null) {
                assetLog.setActionType(AssetConstant.OPT_ORG_CHANGE)
                        .setHandleTime(LocalDateTime.now())
                        .setActionName("员工异动");
                assetLogMap.put(assetId, assetLog);
            } else {
                asAssetLog.setActionContent(asAssetLog.getActionContent() + "；" + assetLog.getActionContent());
            }
        }
        Collection<AsAssetLog> logs = assetLogMap.values();
        assetLogService.saveBatch(logs);

        // 写入异动记录
        AsEmployeeChange employeeChange = new AsEmployeeChange();
        employeeChange.setId(changeId)
                .setType(3)
                .setEmpId(orgId)
                .setOrgExplain("删除组织");
        if (CollUtil.isNotEmpty(assetExplainList)) {
            employeeChange.setAssetExplain(String.join("；", assetExplainList));
        } else {
            employeeChange.setAssetExplain("无");
        }
        employeeChangeService.save(employeeChange);
        // 写入资产变更记录

        this.update(new LambdaUpdateWrapper<DingSyncChange>().set(DingSyncChange::getStatus, 2)
                .eq(DingSyncChange::getId, dingSyncChange.getId()));
        return true;
    }

    private List<AsAssetLog> assetUseOrgTransfer(Long changeId, Long from, Long to) {
        List<AssetDto> useOrg = dingAssetMapper.selectUseOrgAsset(from, LoginUserThreadLocal.getCompanyId());
        List<AsAsset> useOrgAsset = new ArrayList<>();
        List<AsEmployeeAssetChange> assetChanges = new ArrayList<>();
        List<AsAssetLog> assetLogList = new ArrayList<>();
        for (AssetDto assetDto : useOrg) {
            // 更新资产
            AsAsset asAsset = new AsAsset().setId(assetDto.getId()).setAssetData(assetDto.getAssetData());
            JSONObject data = asAsset.getAssetData();

            // 记录原始所属组织
            String fromOrgName = cacheResourceUtil.getOrgName(from);
            String toOrgName = cacheResourceUtil.getOrgName(to);
            String content = "使用组织由" + fromOrgName + "变成" + toOrgName;
            AsAssetLog assetLog = new AsAssetLog().setAssetId(assetDto.getId()).setActionContent(content);
            assetLogList.add(assetLog);

            // 更新使用组织
            data.put("useOrg", to);
            useOrgAsset.add(asAsset);

            // 写入记录
            AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
            assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(changeId).setChangeOrgType(1);
            assetChanges.add(assetChange);
        }

        // 更新
        if (!useOrgAsset.isEmpty()) {
            this.assetService.updateBatchById(useOrgAsset);
            this.employeeAssetChangeService.saveBatch(assetChanges);
        }
        return assetLogList;
    }

    private List<AsAssetLog> assetOrgOwnerTransfer(Long changeId, Long from, Long to) {
        List<AssetDto> orgOwner = dingAssetMapper.selectOrgOwnerAsset(from, LoginUserThreadLocal.getCompanyId());
        List<AsAsset> orgOwnerAsset = new ArrayList<>();
        List<AsEmployeeAssetChange> assetChanges = new ArrayList<>();
        List<AsAssetLog> assetLogList = new ArrayList<>();
        for (AssetDto assetDto : orgOwner) {
            // 更新资产
            AsAsset asAsset = new AsAsset().setId(assetDto.getId()).setAssetData(assetDto.getAssetData());
            JSONObject data = asAsset.getAssetData();

            // 记录原始所属组织
            String fromOrgName = cacheResourceUtil.getOrgName(from);
            String toOrgName = cacheResourceUtil.getOrgName(to);
            String content = "所属管理组织由" + fromOrgName + "变成" + toOrgName;
            AsAssetLog assetLog = new AsAssetLog().setAssetId(assetDto.getId()).setActionContent(content);
            assetLogList.add(assetLog);

            // 更新使用组织
            data.put("orgOwner", to);
            orgOwnerAsset.add(asAsset);

            // 写入记录
            AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
            assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(changeId).setChangeOrgType(2);
            assetChanges.add(assetChange);
        }

        // 更新
        if (!orgOwnerAsset.isEmpty()) {
            this.assetService.updateBatchById(orgOwnerAsset);
            this.employeeAssetChangeService.saveBatch(assetChanges);
        }
        return assetLogList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWithSendMsg(List<DingSyncChange> changes) {
        // 过滤调员工编辑类型且没有使用资产的数据
        changes.removeIf(v -> v.getType() == 1 && (dingAssetMapper.checkUsePerson(Collections.singletonList(v.getResId()), v.getCompanyId()).isEmpty()));
        if (CollUtil.isEmpty(changes)) {
            return;
        }
        changes.forEach(next -> {
            // 1.删除员工
            if (next.getType() == 2) {
                next.setId(IdUtils.getId());
                this.remove(
                        Wrappers.lambdaQuery(DingSyncChange.class)
                                .eq(DingSyncChange::getResId, next.getResId())
                                .eq(DingSyncChange::getType, 1)
                                .eq(DingSyncChange::getStatus, 1)
                );
                return;
            }
            // 2.编辑员工 更新记录
            if (next.getType() == 1) {
                DingSyncChange one = lastUntreatedRecord(next.getCompanyId(), next.getResId(), 1);
                if (Objects.nonNull(one)) {
                    // 合并历史部门
                    Set<Long> form = new HashSet<>(6);
                    if (CollUtil.isNotEmpty(next.getFromOrg())) {
                        form.addAll(next.getFromOrg());
                    }
                    if (CollUtil.isNotEmpty(one.getFromOrg())) {
                        form.addAll(one.getFromOrg());
                    }
                    next.setId(one.getId()).setFromOrg(new ArrayList<>(form));
                } else {
                    next.setId(IdUtils.getId());
                }
                return;
            }
            next.setId(IdUtils.getId());
        });
        this.saveOrUpdateBatch(changes);
        // 菜单
        QuietTask.syncExecute(body -> {
            AsCusMenu menu = menuService.getOne(
                    Wrappers.lambdaQuery(AsCusMenu.class)
                            .eq(AsCusMenu::getMenuCode, "emp-change")
            );
            // 企业角色ID集合
            Map<Long, Set<Long>> roleCompanyGroup = roleService.list(
                    Wrappers.lambdaQuery(AsCusRole.class)
                            .select(AsCusRole::getId, AsCusRole::getCompanyId)
                            .eq(AsCusRole::getStatus, 1)
                            .in(AsCusRole::getCompanyId, changes.stream().map(DingSyncChange::getCompanyId).collect(Collectors.toSet()))
            ).stream().collect(Collectors.groupingBy(AsCusRole::getCompanyId, Collectors.mapping(AsCusRole::getId, Collectors.toSet())));

            Map<Long, List<DingSyncChange>> groupByCompany = changes.stream().collect(Collectors.groupingBy(DingSyncChange::getCompanyId));
            groupByCompany.forEach((k, v) -> {
                // 获取企业配置信息
                DingCorp dingCorp = dingCorpService.getByCompanyId(k);
                // 获取企业能查看员工异动功能权限的人列表
                Set<Long> roleIds = roleMenuService.list(
                        Wrappers.lambdaQuery(AsRoleMenu.class)
                                .select(AsRoleMenu::getRoleId)
                                .eq(AsRoleMenu::getMenuId, menu.getId())
                                .eq(AsRoleMenu::getType, "pc")
                                .in(AsRoleMenu::getRoleId, roleCompanyGroup.get(k))
                ).stream().map(AsRoleMenu::getRoleId).collect(Collectors.toSet());
                Set<Long> empIds = new HashSet<>(100);
                empIds.add(employeeService.getAdministratorByCompanyId(k).getId());
                if (CollUtil.isNotEmpty(roleIds)) {
                    empIds.addAll(
                            userRoleService.list(
                                    Wrappers.lambdaQuery(AsUserRole.class)
                                            .select(AsUserRole::getUserId)
                                            .in(AsUserRole::getRoleId, roleIds)
                            ).stream().map(AsUserRole::getUserId).collect(Collectors.toSet())
                    );
                }
                // 转换id
                Set<String> dingUserIds = thirdPartyEmployeeService.list(
                        Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                                .select(AsThirdPartyEmployee::getUserId)
                                .in(AsThirdPartyEmployee::getEmployeeId, empIds)
                ).stream().map(AsThirdPartyEmployee::getUserId).collect(Collectors.toSet());

                Map<Integer, List<DingSyncChange>> group = v.stream().collect(Collectors.groupingBy(DingSyncChange::getType));
                // 员工离职
                if (group.containsKey(2)) {
                    group.get(2).stream().map(sync -> {
                        DingtalkMsgOaMessageRequest request = new DingtalkMsgOaMessageRequest(dingCorp.getCorpId());
                        request.setAgentId(dingCorp.getAgentId());
                        request.setBusinessId(sync.getId());
                        request.setBusinessType(sync.getType());
                        request.setUserIds(new ArrayList<>(dingUserIds));
                        request.setTitle(cacheResourceUtil.getUserName(sync.getResId()) + "离职资产信息提醒");
                        request.setTemplateFormSize(6);
                        List<DingtalkMsgOaMessageRequest.Form> forms = new ArrayList<>(5);
                        forms.add(new DingtalkMsgOaMessageRequest.Form("员工名称：", cacheResourceUtil.getUserName(sync.getResId())));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("所在部门：", userOrgService.list(Wrappers.lambdaQuery(AsUserOrg.class).eq(AsUserOrg::getUserId, sync.getResId())).stream().map(v1 -> cacheResourceUtil.getOrgName(v1.getOrgId())).collect(Collectors.joining("、"))));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("离职时间：", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd"))));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("管理资产：", dingAssetMapper.checkManagerOwner(Collections.singletonList(sync.getResId()), sync.getCompanyId()).size() + "件"));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("在用资产：", dingAssetMapper.checkUsePerson(Collections.singletonList(sync.getResId()), sync.getCompanyId()).size() + "件"));
                        request.setForm(forms);
                        return request;
                    }).forEach(value -> dingtalkMessageClient.sendMessage(TemplateTypeEnum.SYNC_CHANGE, value));
                }
                // 员工部门异动
                if (group.containsKey(1)) {
                    group.get(1).stream().map(sync -> {
                        DingtalkMsgOaMessageRequest request = new DingtalkMsgOaMessageRequest(dingCorp.getCorpId());
                        request.setAgentId(dingCorp.getAgentId());
                        request.setBusinessId(sync.getId());
                        request.setBusinessType(sync.getType());
                        request.setUserIds(new ArrayList<>(dingUserIds));
                        request.setTitle(cacheResourceUtil.getUserName(sync.getResId()) + "变更组织，资产处理通知");
                        request.setTemplateFormSize(6);
                        List<DingtalkMsgOaMessageRequest.Form> forms = new ArrayList<>(5);
                        forms.add(new DingtalkMsgOaMessageRequest.Form("员工名称：", cacheResourceUtil.getUserName(sync.getResId())));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("旧部门：", sync.getFromOrg().stream().map(j -> cacheResourceUtil.getOrgName(j)).collect(Collectors.joining("、"))));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("新部门：", sync.getToOrg().stream().map(j -> cacheResourceUtil.getOrgName(j)).collect(Collectors.joining("、"))));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("变更时间：", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd"))));
                        forms.add(new DingtalkMsgOaMessageRequest.Form("使用资产：", dingAssetMapper.checkUsePerson(Collections.singletonList(sync.getResId()), sync.getCompanyId()).size() + "件"));
                        request.setForm(forms);
                        return request;
                    }).forEach(msg -> dingtalkMessageClient.sendMessage(TemplateTypeEnum.SYNC_CHANGE, msg));
                }
            });
        }, changes);
    }

    @Override
    public DingSyncChange lastUntreatedRecord(Long companyId, Long resId, Integer type) {
        return this.getOne(
                Wrappers.lambdaQuery(DingSyncChange.class)
                        .eq(DingSyncChange::getResId, resId)
                        .eq(DingSyncChange::getType, type)
                        .eq(DingSyncChange::getStatus, 1)
                        .orderByDesc(DingSyncChange::getId)
                        .last("LIMIT 1")
        );
    }

    @Override
    public void removeUntreatedRecord(Long companyId, Long resId, Integer type) {
        this.remove(
                Wrappers.lambdaQuery(DingSyncChange.class)
                        .eq(DingSyncChange::getCompanyId, companyId)
                        .eq(DingSyncChange::getResId, resId)
                        .eq(DingSyncChange::getType, type)
                        .eq(DingSyncChange::getStatus, 1)
        );
    }
}
