package com.niimbot.asset.dingtalk.org.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.service.CompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * created by chen.y on 2021/8/30 9:22
 */
@Slf4j
@RestController
@RequestMapping("server/dingtalk/orgUpdate")
public class OrgUpdateServiceController {

    private final CompanyService companyService;

    @Autowired
    public OrgUpdateServiceController(CompanyService companyService) {
        this.companyService = companyService;
    }

    @PutMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean orgUpdate(@RequestBody JSONObject content) {
        if ("org_update".equals(content.getString("syncAction"))) {
            DingCorp dingCorp = content.toJavaObject(DingCorp.class);
            // 更新企业
            AsCompany company = new AsCompany();
            company.setId(dingCorp.getCompanyId());
            company.setName(dingCorp.getCorpName());
            company.setLogo(dingCorp.getCorpLogoUrl());
            companyService.updateById(company);
        }
        return true;
    }

}
