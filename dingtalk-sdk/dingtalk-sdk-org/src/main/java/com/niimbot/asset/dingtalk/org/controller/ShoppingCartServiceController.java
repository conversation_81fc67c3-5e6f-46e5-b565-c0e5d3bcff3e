package com.niimbot.asset.dingtalk.org.controller;

import com.niimbot.asset.dingtalk.base.dto.*;
import com.niimbot.asset.dingtalk.org.service.ShoppingCartService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:32
 */
@RestController
@RequestMapping("server/dingtalk/shoppingCart/")
public class ShoppingCartServiceController {

    @Autowired
    private ShoppingCartService shoppingCartService;

    @PostMapping("addProduct")
    public Boolean addProduct(@RequestBody OrderRequestDto orderRequestDto) {
        return shoppingCartService.addProduct(orderRequestDto);
    }

    @PostMapping("editProduct")
    public Boolean editProduct(@RequestBody OrderProductRequestDto orderProductRequestDto) {
        return shoppingCartService.editProduct(orderProductRequestDto);
    }

    @PostMapping("removeProduct")
    public Boolean removeProduct(@RequestBody OrderProductRequestDto orderProductRequestDto) {
        return shoppingCartService.removeProduct(orderProductRequestDto);
    }

    @PostMapping("clear")
    public Boolean clearShoppingCart() {
        return shoppingCartService.clearShoppingCart();
    }

    @GetMapping("productCount")
    public Integer productCount() {
        return shoppingCartService.productCount();
    }

    @GetMapping("list")
    public List<ShoppingCartProductDto> list() {
        return shoppingCartService.queryProduct();
    }

    @PostMapping("queryProduct")
    public ShoppingCartDto queryProduct(@RequestBody List<Long> productIdList) {
        return shoppingCartService.queryProductById(productIdList);
    }

    @PostMapping("settleProduct")
    public ShoppingCartDto settleProduct(@RequestBody OrderRequestDto orderRequestDto) {
        if (orderRequestDto.getImmediatelySettle()) {
            //立即购买
            return shoppingCartService.immediatelySettleProduct(orderRequestDto);
        } else {
            //购物车购买
            return shoppingCartService.settleProduct(orderRequestDto);
        }
    }

    @PostMapping("savePayUrl")
    public Boolean savePayUrl(@RequestBody ResourcePayDto resourcePayDto) {
        if (Objects.isNull(resourcePayDto) || Objects.isNull(resourcePayDto.getOrderNo()) || Objects.isNull(resourcePayDto.getUrl())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID);
        }

        //保存支付地址
        return shoppingCartService.savePayUrl(resourcePayDto.getOrderNo(), resourcePayDto.getUrl());
    }

}
