package com.niimbot.asset.dingtalk.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.dingtalk.base.dto.OrderProductRequestDto;
import com.niimbot.asset.dingtalk.base.dto.OrderRequestDto;
import com.niimbot.asset.dingtalk.base.dto.ShoppingCartDto;
import com.niimbot.asset.dingtalk.base.dto.ShoppingCartProductDto;
import com.niimbot.asset.dingtalk.base.model.AsShoppingCart;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:03
 */
public interface ShoppingCartService extends IService<AsShoppingCart> {

    /**
     * 购物车添加商品
     * @param orderRequestDto
     * @return
     */
    Boolean addProduct(OrderRequestDto orderRequestDto);

    /**
     * 购物车商品编辑
     * @param orderProductRequestDto
     * @return
     */
    Boolean editProduct(OrderProductRequestDto orderProductRequestDto);

    /**
     * 购物车商品删除
     * @param orderProductRequestDto
     * @return
     */
    Boolean removeProduct(OrderProductRequestDto orderProductRequestDto);

    /**
     * 清空购物车
     * @return
     */
    Boolean clearShoppingCart();

    /**
     * 购物车商品数量
     * @return
     */
    Integer productCount();

    /**
     * 查询购物车商品信息
     * @return
     */
    List<ShoppingCartProductDto> queryProduct();

    /**
     * 购物车商品id查询
     * @param productIdList
     * @return
     */
    ShoppingCartDto queryProductById(List<Long> productIdList);

    /**
     * 购物车商品结算
     * @param orderRequestDto
     * @return
     */
    ShoppingCartDto settleProduct(OrderRequestDto orderRequestDto);

    /**
     * 立即购买
     * @param orderRequestDto
     * @return
     */
    ShoppingCartDto immediatelySettleProduct(OrderRequestDto orderRequestDto);

    /**
     * 保存支付链接
     * @param orderNo
     * @param payUrl
     * @return
     */
    Boolean savePayUrl(String orderNo, String payUrl);
}
