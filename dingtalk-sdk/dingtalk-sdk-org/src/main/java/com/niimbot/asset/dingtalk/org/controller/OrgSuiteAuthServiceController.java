package com.niimbot.asset.dingtalk.org.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.org.service.DingtalkContactsService;
import com.niimbot.asset.dingtalk.org.service.OrgSuiteAuthService;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * created by chen.y on 2021/8/27 14:49
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("server/dingtalk/suiteAuth")
public class OrgSuiteAuthServiceController {

    private static final String ORG_SUITE_AUTH = "org_suite_auth";
    private static final String ORG_SUITE_CHANGE = "org_suite_change";
    private static final String ORG_SUITE_RELIEVE = "org_suite_relieve";

    private final OrgSuiteAuthService orgSuiteAuthService;
    private final RedissonClient redissonClient;
    private final DingCorpService dingCorpService;
    private final DingtalkContactsService contactsService;

    @PostMapping
    public Boolean suiteAuth(@RequestParam(value = "corpId") String corpId, @RequestBody JSONObject content) {
        String syncAction = content.getString("syncAction");
        if (ORG_SUITE_AUTH.equals(syncAction)) {
            Long companyId = register(content);
            if (contactsService.processing(companyId)) {
                return true;
            }
            contactsService.background(companyId);
        } else if (ORG_SUITE_CHANGE.equals(syncAction)) {
            DingCorp corp = dingCorpService.getOne(new LambdaQueryWrapper<DingCorp>().eq(DingCorp::getCorpId, corpId));
            if (corp != null) {
                if (contactsService.processing(corp.getCompanyId())) {
                    return true;
                }
                contactsService.background(corp.getCompanyId());
            } else {
                Long companyId = register(content);
                if (contactsService.processing(companyId)) {
                    return true;
                }
                contactsService.background(companyId);
            }
        }
        return true;
    }

    @PostMapping("/load")
    public Boolean loadSuiteAuth(@RequestBody JSONObject content) {
        Long companyId = register(content);
        // 异步同步
        if (contactsService.processing(companyId)) {
            return true;
        }
        contactsService.background(companyId);
        return true;
    }

    private Long register(JSONObject content) {
        DingCorp dingCorp = content.getJSONObject("auth_corp_info").toJavaObject(DingCorp.class);
        RLock rLock = redissonClient.getLock("dingtalk-register:" + dingCorp.getCorpId());
        rLock.lock();
        try {
            return orgSuiteAuthService.register(content);
        } finally {
            rLock.unlock();
        }
    }

}
