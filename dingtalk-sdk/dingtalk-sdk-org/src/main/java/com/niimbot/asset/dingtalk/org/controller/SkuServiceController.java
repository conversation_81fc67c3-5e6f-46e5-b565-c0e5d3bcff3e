package com.niimbot.asset.dingtalk.org.controller;

import com.niimbot.asset.dingtalk.base.dto.DingResourcePayDto;
import com.niimbot.asset.dingtalk.org.service.DingOrderService;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.sale.service.AsGoodsSkuService;
import com.niimbot.asset.sale.service.AsMallGoodsService;
import com.niimbot.jf.core.exception.category.BusinessException;
import cn.hutool.core.collection.CollUtil;
import com.niimbot.sale.GoodsInfoDto;

import com.niimbot.sale.MallGoodsDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * created by chen.y on 2022/5/17 15:26
 */
@RestController
@RequestMapping("server/dingtalk/sku")
public class SkuServiceController {

    private final AsGoodsSkuService goodsSkuService;

    private final DingOrderService dingOrderService;

    private final AsMallGoodsService goodsService;

    @Autowired
    public SkuServiceController(AsGoodsSkuService goodsSkuService,
                                DingOrderService dingOrderService,
                                AsMallGoodsService goodsService) {
        this.goodsSkuService = goodsSkuService;
        this.dingOrderService = dingOrderService;
        this.goodsService = goodsService;
    }

    @GetMapping("/{type}")
    public GoodsInfoDto getSkuOne(@PathVariable("type") Integer type) {
        return this.goodsSkuService.getSkuOne(type);
    }

    @PostMapping("/resourcePay")
    public DingResourcePayDto getResourcePay(@RequestBody DingResourcePayDto payDto) {
        return dingOrderService.getResourcePay(payDto);
    }

    @GetMapping("/checkOrderStatus/{orderNo}")
    public Boolean checkOrderStatus(@PathVariable("orderNo") String orderNo) {
        return dingOrderService.checkOrderStatus(orderNo);
    }

    @GetMapping("/hardware")
    public List<MallGoodsDto> hardwareProduct() {
        return goodsService.queryHardware();
    }

    @GetMapping("/detail/{skuCode}")
    public MallGoodsDto hardwareDetail(@PathVariable("skuCode") String skuCode) {
        List<MallGoodsDto> mallGoodsDtoList = goodsService.queryBySkuCode(Collections.singletonList(skuCode));
        if (CollUtil.isEmpty(mallGoodsDtoList)) {
            throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
        }
        return mallGoodsDtoList.get(0);
    }
}
