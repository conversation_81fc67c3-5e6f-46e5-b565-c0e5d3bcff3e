package com.niimbot.asset.dingtalk.org.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.org.service.SyncApproveRoleService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("server/dingtalk/syncApproveRole")
@RequiredArgsConstructor
public class SyncApproveRoleServiceController {

    private final SyncApproveRoleService syncApproveRoleService;

    private final DingCorpService dingCorpService;

    private final CompanySettingService companySettingService;

    private final RedissonClient redissonClient;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @PostMapping("/manualSyncFromDing/{companyId}")
    public boolean manualSyncFromDing(@PathVariable Long companyId) {
        RLock lock = redissonClient.getLock("sync_approve_role_from_ding:" + companyId);
        if (lock.isLocked()) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "角色正在同步中，请耐心等待");
        }
        threadPoolTaskExecutor.execute(() -> {
            try {
                lock.lock();
                syncApproveRoleService.manualSyncFromDing(companyId);
            } catch (Exception e) {
                log.error("审批角色同步失败", e);
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        });
        return true;
    }

    @PostMapping("/event")
    @Transactional(rollbackFor = Exception.class)
    public boolean roleEvent(@RequestParam("corpId") String corpId,
                             @RequestParam("account") String account,
                             @RequestBody JSONObject content) {

        String syncAction = content.getString("syncAction");
        DingCorp corp = dingCorpService.getOne(new LambdaQueryWrapper<DingCorp>()
                .eq(DingCorp::getCorpId, corpId));
        Long companyId = 0L;
        if (ObjectUtil.isNotNull(corp)) {
            companyId = corp.getCompanyId();
        } else {
            log.error("ding corp [{}] not exists ", corpId);
            return false;
        }
        CompanySwitch companySwitch = companySettingService.getSwitchSettingWithCache(companyId);
        if (!companySwitch.getEnableSyncApproveRole()) {
            return true;
        }
        switch (syncAction) {
            case "org_role_add":
                return syncApproveRoleService.roleCreateEvent(companyId, account, content);
            case "org_role_remove":
                return syncApproveRoleService.roleRemoveEvent(companyId, account, content);
            case "org_role_modify":
                return syncApproveRoleService.roleUpdateEvent(companyId, account, content);
            default:
                return true;
        }

    }
}
