package com.niimbot.asset.dingtalk.org.scheduler;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.niimbot.asset.dingtalk.base.config.CorpTokenManage;
import com.niimbot.asset.dingtalk.base.model.ConsumedInfo;
import com.niimbot.asset.dingtalk.base.model.CurrencyGoodsInfo;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingOrder;
import com.niimbot.asset.dingtalk.base.model.DingOrderConsume;
import com.niimbot.asset.dingtalk.base.model.GoodsItemCode;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.org.service.DingCompanyConsumeBillService;
import com.niimbot.asset.dingtalk.org.service.DingOrderConsumeService;
import com.niimbot.asset.dingtalk.org.service.DingOrderService;
import com.niimbot.asset.dingtalk.org.service.GeneralService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.sale.service.AsCompanyBillDetailService;
import com.niimbot.asset.sale.service.AsCompanyBillService;
import com.niimbot.asset.sale.service.AsCompanyIncomeExpensesService;
import com.niimbot.asset.sale.service.AsCompanyWalletService;
import com.niimbot.asset.sale.service.AsGoodsSkuService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.sale.GoodsInfoDto;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 账单调度器 created by chen.y on 2021/12/30 16:20
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BillScheduler {

    private final AsGoodsSkuService goodsSkuService;
    private final AsCompanyWalletService companyWalletService;
    private final AsCompanyBillService companyBillService;
    private final AsCompanyBillDetailService companyBillDetailService;
    private final AsCompanyIncomeExpensesService incomeExpensesService;
    private final AssetService assetService;
    private final CompanyService companyService;
    private final GeneralService generalService;

    private final DingOrderService orderService;
    private final DingOrderConsumeService consumeService;
    private final DingCorpService dingCorpService;
    private final DingOpenApiService dingOpenApiService;
    private final CorpTokenManage corpTokenManage;
    private final DingCompanyConsumeBillService dingCompanyConsumeBillService;

    private final RedissonClient redissonClient;
    private final BigDecimal DEDUCTION_CEILING = BigDecimal.valueOf(12800);

    /**
     * 时间：每天凌晨 计算流失企业，统计欠费超过1年的转为流失用户
     */
//    @Scheduled(cron = "1 0 0 * * ?")
//    @SingleExecutor
    public void lossCompany() {
        // 查询欠费企业
        List<AsCompany> list = companyService.list(new LambdaQueryWrapper<AsCompany>()
                .select(AsCompany::getId)
                .eq(AsCompany::getStatus, DictConstant.COMPANY_STATUS_ARREARS));
        List<Long> companyIds = list.stream().map(AsCompany::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(companyIds)) {
            List<Long> arrearsCompany = incomeExpensesService.listArrearsOverYear(companyIds);
            if (CollUtil.isNotEmpty(arrearsCompany)) {
                companyService.update(new LambdaUpdateWrapper<AsCompany>()
                        .set(AsCompany::getStatus, DictConstant.COMPANY_STATUS_DRAIN)
                        .in(AsCompany::getId, arrearsCompany));
                companyService.cleanCompanyStatusCache(arrearsCompany);
            }
        }
    }

    /**
     * 时间：每月1号凌晨 计算账单
     */
//    @Scheduled(cron = "1 0 0 1 * ?")
//    @SingleExecutor
    public void calculateBill() {
        RLock rLock = redissonClient.getLock(RedisConstant.companyBillStatisticsLockKey());
        rLock.lock();
        try {
            // 资产使用费单价
            List<GoodsInfoDto> skuList = goodsSkuService.getSkuList(DictConstant.PRODUCT_TYPE_ASSET_USE_COST);
            GoodsInfoDto currency = goodsSkuService.getSkuOne(DictConstant.PRODUCT_TYPE_ASSET_CURRENCY);
            JSONArray currencyList = currency.getInfo().getJSONArray("itemCode");
            if (CollUtil.isEmpty(skuList) || CollUtil.isEmpty(currencyList)) {
                // 如果为空默认给sku
                log.error("资产使用费或精条信息不存在");
            }
            List<GoodsItemCode> goodsItemCodes = currencyList.toJavaList(GoodsItemCode.class);

            // 查询企业超管
            Map<Long, String> adminUserMap = new HashMap<>();
            for (AsCusUser cusUser : generalService.allAdminUser()) {
                adminUserMap.put(cusUser.getCompanyId(), cusUser.getAccount());
            }

            // 查询企业数据
            List<DingCorp> allCompany = dingCorpService.list();
            Map<Long, String> companyMap = allCompany.stream().collect(Collectors.toMap(DingCorp::getCompanyId, DingCorp::getCorpId));
            LocalDate lastMon = LocalDate.now().minusMonths(1L);
            companyMap.forEach((companyId, corpId) -> {
                try {
                    SpringUtil.getBean(BillScheduler.class).calculateBill(companyId,
                            corpId,
                            adminUserMap.get(companyId),
                            skuList,
                            goodsItemCodes,
                            currency.getCode(),
                            lastMon);
                } catch (Exception e) {
                    log.error("计算账单失败, {}", e.getMessage(), e);
                } finally {
                    log.info("计算账单{}条", companyId);
                }
            });
        } finally {
            log.info("计算账单完成");
            rLock.unlock();
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void calculateBill(Long companyId, String corpId, String adminUserId, List<GoodsInfoDto> skuList, List<GoodsItemCode> itemCode, String currencySkuCode, LocalDate lastMon) {
        /*int year = lastMon.getYear();
        int mon = lastMon.getMonthValue();
        LocalDate firstDay = LocalDate.of(year, mon, 1);
        LocalDate lastDay = lastMon.with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime tradeTime = lastDay.atTime(23, 59, 59);

        // 查询企业上个月账单和账单明细
        List<AsCompanyBill> lastCompanyBills = companyBillService.listNoDataPerm(new LambdaQueryWrapper<AsCompanyBill>()
                .select(AsCompanyBill::getId).eq(AsCompanyBill::getYear, year).eq(AsCompanyBill::getMonth, mon)
                .eq(AsCompanyBill::getCompanyId, companyId));
        Set<Long> lastCompanyBillSet = lastCompanyBills.stream().map(AsCompanyBill::getCompanyId).collect(Collectors.toSet());

        // 查询企业上上月总账
        List<AsCompanyBill> lastMonthBill = companyBillService.lastBill(ListUtil.of(companyId), lastMon);
        Map<Long, AsCompanyBill> billMap = lastMonthBill.stream()
                .collect(Collectors.toMap(AsCompanyBill::getCompanyId, k -> k));

        // 查询企业钱包
        AsCompanyWallet companyWallet = companyWalletService.getById(companyId);
        if (companyWallet == null) {
            log.error("company {} wallet not exists", companyId);
            return;
        }

        // 查询企业上个月流水（排除结算费用）
        List<CompanyIncomeExpensesStatisticsDto> statisticsList = incomeExpensesService.lastMonthStatistics(ListUtil.of(companyId),
                firstDay.atTime(0, 0, 0),
                lastDay.atTime(23, 59, 59));
        Map<Long, CompanyIncomeExpensesStatisticsDto> statisticsMap = statisticsList.stream()
                .collect(Collectors.toMap(CompanyIncomeExpensesStatisticsDto::getCompanyId, k -> k));

        // 查询企业资产总数
        Map<Long, Integer> assetCountMap = assetService.countCompanyAsset(ListUtil.of(companyId));
        boolean isArrears = false;
        //计算企业过去12个月的账单综合
        List<AsCompanyBill> past12MonthBills = companyBillService.past12MonthBill(ListUtil.of(companyId));
        Map<Long, BigDecimal> past12MonthBillsMap = past12MonthBills.stream()
                .collect(Collectors.toMap(AsCompanyBill::getCompanyId, AsCompanyBill::getExpensesAmount));

        // 【存在钱包】并且【上个月未生成过总账单】进入账单统计
        if (!lastCompanyBillSet.contains(companyId)) {

            // 上十二个月的支出总费用
            BigDecimal last12MonthTotalExpenses = past12MonthBillsMap.getOrDefault(companyId, BigDecimal.ZERO);
            // 超过阈值不扣费
            boolean isDeduction = DEDUCTION_CEILING.compareTo(last12MonthTotalExpenses) > 0;

            // 总账单
            AsCompanyBill companyBill = new AsCompanyBill()
                    .setId(IdUtils.getId())
                    .setCompanyId(companyId)
                    .setYear(year).setMonth(mon)
                    .setStatisticalStartTime(firstDay)
                    .setStatisticalEndTime(lastDay);
            // 收入账单
            AsCompanyBillDetail income = new AsCompanyBillDetail()
                    .setCompanyId(companyId)
                    .setBillId(companyBill.getId())
                    .setType(DictConstant.INCOME_EXPENSES_TYPE_INCOME)
                    .setYear(year).setMonth(mon)
                    .setStatisticalStartTime(firstDay)
                    .setStatisticalEndTime(lastDay);
            // 支出账单
            AsCompanyBillDetail expenses = new AsCompanyBillDetail()
                    .setCompanyId(companyId)
                    .setBillId(companyBill.getId())
                    .setType(DictConstant.INCOME_EXPENSES_TYPE_EXPENSES)
                    .setYear(year).setMonth(mon)
                    .setStatisticalStartTime(firstDay)
                    .setStatisticalEndTime(lastDay);

            // 账户余额
            BigDecimal balance = companyWallet.getBalance();
            // 欠费金额
            BigDecimal arrears = companyWallet.getArrears();
            // 可用余额
            BigDecimal availableBalance = balance.subtract(arrears);

            // 结算资产使用费（余额是否小于0，当小于0，则资产使用费默认为0）
            BigDecimal useCostTotalPrice = BigDecimal.ZERO;
            // 可用余额大于零，并且可以扣费
            if (availableBalance.compareTo(BigDecimal.ZERO) >= 0 && BooleanUtil.isTrue(isDeduction)) {
                // 公司资产总数量
                Integer assetCount = assetCountMap.getOrDefault(companyId, 0);
                useCostTotalPrice = calcUseCost(assetCount, skuList);
                // 资产使用费大于零
                if (useCostTotalPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // 判断【资产使用费】+【前12个月的总费用】是否会超过总上限12800的阈值
                    BigDecimal useCostCache = new BigDecimal(useCostTotalPrice.toPlainString());
                    BigDecimal overFee = useCostTotalPrice.add(last12MonthTotalExpenses).subtract(DEDUCTION_CEILING);
                    // 超过了阈值
                    if (overFee.compareTo(BigDecimal.ZERO) >= 0) {
                        useCostTotalPrice = DEDUCTION_CEILING.subtract(last12MonthTotalExpenses);
                    }

                    // 计算企业钱包
                    WalletUtils.adjust(companyWallet, useCostTotalPrice, WalletUtils.CASH_OUT);
                    if (companyWallet.getArrears().compareTo(BigDecimal.ZERO) > 0) {
                        // 记录欠费企业
                        isArrears = true;
                    }
                    // 生成收支明细
                    AsCompanyIncomeExpenses companyIncomeExpense = new AsCompanyIncomeExpenses();
                    companyIncomeExpense.setType(DictConstant.INCOME_EXPENSES_TYPE_EXPENSES)
                            .setCompanyId(companyId)
                            .setBizType(DictConstant.INCOME_EXPENSES_BIZ_TYPE_USE)
                            .setTradeTime(tradeTime)
                            .setTradeAmount(useCostTotalPrice)
                            .setBalance(companyWallet.getBalance())
                            .setArrears(companyWallet.getArrears());
                    if (overFee.compareTo(BigDecimal.ZERO) >= 0) {
                        companyIncomeExpense.setBizDesc("截止上月底，资产数为" + assetCount + "条，" +
                                "资产使用费为" + useCostCache.toPlainString() + "精条，因最近12个月资产使用费超出上限，" +
                                "故超出部分不再计费，实际费用为" + useCostTotalPrice.toPlainString() + "精条");
                    } else {
                        companyIncomeExpense.setBizDesc("截止上月底，资产数为" + assetCount + "条");
                    }
                    incomeExpensesService.save(companyIncomeExpense);
                }
            }

            // ==============================总账单==========================================
            CompanyIncomeExpensesStatisticsDto statistics = statisticsMap.getOrDefault(companyId, new CompanyIncomeExpensesStatisticsDto());
            statistics.setUse(useCostTotalPrice);
            // 期初
            if (billMap.containsKey(companyId)) {
                AsCompanyBill bill = billMap.get(companyId);
                // 结余设置为当前的期初额
                companyBill.setInitialAmount(bill.getBalanceAmount());
            } else {
                companyBill.setInitialAmount(BigDecimal.ZERO);
            }

            // 总收入
            companyBill.setIncomeAmount(statistics.getAdjustIn().add(statistics.getBuy()));

            // 总支出
            companyBill.setExpensesAmount(statistics.getAdjustOut().add(statistics.getUse()));
            // 结余精条数 = 期初精条数 + 总收入精条数 - 总支出精条数
            companyBill.setBalanceAmount(companyBill.getInitialAmount()
                    .add(companyBill.getIncomeAmount())
                    .subtract(companyBill.getExpensesAmount()));
            // 待付精条数  =  欠费金额
            companyBill.setWaitPayAmount(companyWallet.getArrears());
            // 应付精条 = 实付精条 + 待付精条 =  总支出精条数  =  生成账单时结算资产使用费 +运营平台调账扣款；
            BigDecimal actuallyPayAmount = companyBill.getExpensesAmount().subtract(companyBill.getWaitPayAmount());
            companyBill.setActuallyPayAmount(actuallyPayAmount.compareTo(BigDecimal.ZERO) > 0 ? actuallyPayAmount : BigDecimal.ZERO);
            companyBillService.save(companyBill);

            // ==============================收入账单==========================================
            income.setIncomeOrExpensesAmount(statistics.getBuy())
                    .setReconciliationAmount(statistics.getAdjustIn())
                    .setTotalIncomeOrExpensesAmount(companyBill.getIncomeAmount());
            companyBillDetailService.save(income);

            // ==============================支出账单==========================================
            expenses.setIncomeOrExpensesAmount(statistics.getUse())
                    .setReconciliationAmount(statistics.getAdjustOut())
                    .setTotalIncomeOrExpensesAmount(companyBill.getExpensesAmount());
            companyBillDetailService.save(expenses);

            // 更新企业的状态为欠费
            if (isArrears) {
                companyService.update(new LambdaUpdateWrapper<AsCompany>().set(AsCompany::getStatus, DictConstant.COMPANY_STATUS_ARREARS)
                        .in(AsCompany::getId, companyId));
                // 删除企业状态缓存
                companyService.cleanCompanyStatusCache(ListUtil.of(companyId));
            }
            // 更新企业钱包
            companyWalletService.updateById(companyWallet);
            // 核销
            if (useCostTotalPrice.compareTo(BigDecimal.ZERO) > 0) {
                // orderConsume(corpId, adminUserId, itemCode, useCostTotalPrice);
                try {
                    Map<String, Integer> itemCodeMap = itemCode.stream().collect(Collectors.toMap(GoodsItemCode::getCode, GoodsItemCode::getNum));
                    // 查询订单
                    List<DingOrder> orderList = orderService.list(new LambdaQueryWrapper<DingOrder>()
                            .eq(DingOrder::getCorpId, corpId)
                            .in(DingOrder::getItemCode, itemCodeMap.keySet())
                            .eq(DingOrder::getStatus, 3)
                            .ne(DingOrder::getConsumeStatus, 3)
                            .orderByAsc(DingOrder::getCreateTime));
                    // 单价高放前面
                    orderList.sort((o1, o2) -> -itemCodeMap.get(o1.getItemCode()).compareTo(itemCodeMap.get(o2.getItemCode())));
                    DingCompanyConsumeBill companyGoodsBill = new DingCompanyConsumeBill();
                    companyGoodsBill.setYear(year).setMonth(mon).setStatisticalStartTime(firstDay).setStatisticalEndTime(lastDay).setGoodsSkuCode(currencySkuCode).setCompanyId(companyId);
                    // 上个月剩余未核销的花费
                    List<DingCompanyConsumeBill> lastMonthConsumeBill = dingCompanyConsumeBillService.list(
                            Wrappers.lambdaQuery(DingCompanyConsumeBill.class)
                                    .eq(DingCompanyConsumeBill::getCompanyId, companyId)
                                    .orderByDesc(DingCompanyConsumeBill::getCreateTime)
                                    .last("LIMIT 1")
                    );
                    BigDecimal remainingNotConsumedLastMonth = BigDecimal.ZERO;
                    if (CollUtil.isNotEmpty(lastMonthConsumeBill)) {
                        remainingNotConsumedLastMonth = lastMonthConsumeBill.get(0).convertBillInfoToCurrency().getRemainingNotConsumedThisMonth();
                    }
                    MonthlyCurrencyBillInfo currencyBillInfo = new MonthlyCurrencyBillInfo();
                    // 本月理论花费的精条数
                    currencyBillInfo.setCostConsumed(useCostTotalPrice);
                    // 本月允许核销的精条数 减去欠费的
                    if (useCostTotalPrice.compareTo(availableBalance) >= 0) {
                        currencyBillInfo.setAllowConsumed(availableBalance.subtract(arrears));
                    } else {
                        currencyBillInfo.setAllowConsumed(useCostTotalPrice.subtract(arrears));
                    }
                    // 上个月未核销的精条数
                    currencyBillInfo.setRemainingNotConsumedLastMonth(remainingNotConsumedLastMonth);
                    // 本月应该去钉钉核销的精条数 = 能核销的精条数 + 上个月未核销的精条数
                    currencyBillInfo.setNeedConsumed(currencyBillInfo.getAllowConsumed().add(remainingNotConsumedLastMonth));
                    List<DingOrder> dingOrders = new ArrayList<>();
                    List<DingOrderConsume> dingOrderConsumes = new ArrayList<>();
                    // 本月能去钉钉核销的精条数 = 需要核销的精条数取整
                    int price = currencyBillInfo.getNeedConsumed().intValue();
                    int actualConsumed = 0;
                    for (DingOrder order : orderList) {
                        if (price <= 0) {
                            break;
                        }
                        // 允许核销的商品数
                        int allowConsumeNum = order.getSubQuantity() - order.getConsumeQuantity();
                        // 已全部核销，不处理
                        if (allowConsumeNum <= 0) {
                            continue;
                        }
                        // 去核销
                        Integer unitPrice = itemCodeMap.get(order.getItemCode());
                        if (price == unitPrice) {
                            if (doConsume(corpId, order, 1, unitPrice, adminUserId, dingOrders, dingOrderConsumes)) {
                                actualConsumed = actualConsumed + unitPrice;
                                break;
                            }
                        }
                        if (price < unitPrice) {
                            continue;
                        }
                        // price > unitPrice
                        int needConsumeNum = price / unitPrice;
                        if (needConsumeNum <= 0) {
                            continue;
                        }
                        if (needConsumeNum <= allowConsumeNum) {
                            if (doConsume(corpId, order, needConsumeNum, unitPrice, adminUserId, dingOrders, dingOrderConsumes)) {
                                price = price - (needConsumeNum * unitPrice);
                                actualConsumed = actualConsumed + (needConsumeNum * unitPrice);
                            }
                        } else {
                            if (doConsume(corpId, order, allowConsumeNum, unitPrice, adminUserId, dingOrders, dingOrderConsumes)) {
                                price = price - (allowConsumeNum * unitPrice);
                                actualConsumed = actualConsumed + (allowConsumeNum * unitPrice);
                            }
                        }
                    }
                    Long consumeBillId = IdUtils.getId();
                    companyGoodsBill.setId(consumeBillId);
                    orderService.updateBatchById(dingOrders);
                    dingOrderConsumes.forEach(s -> s.setConsumeBillId(consumeBillId));
                    consumeService.saveBatch(dingOrderConsumes);
                    // 本月实际核销的精条数
                    currencyBillInfo.setActualConsumed(actualConsumed);
                    // 本月未向钉钉核销的精条数零头
                    currencyBillInfo.setRemainingNotConsumedThisMonth(currencyBillInfo.getNeedConsumed().subtract(new BigDecimal(actualConsumed)));
                    companyGoodsBill.convertBillInfoToJsonObject(currencyBillInfo);
                    dingCompanyConsumeBillService.save(companyGoodsBill);
                } catch (Exception e) {
                    log.error("consume company {} error, itemCode={}, adminUserId={} useCostTotalPrice={}",
                            corpId, itemCode, adminUserId, useCostTotalPrice, e);
                }
            }
        }*/
    }

    public boolean doConsume(String corpId, DingOrder order, int thisTimeConsumeNum, Integer unitPrice, String adminUserId, List<DingOrder> dingOrders, List<DingOrderConsume> dingOrderConsumes) {
        String corpToken = corpTokenManage.getCorpToken(corpId);
        DingOrderConsume orderConsume = new DingOrderConsume();
        ConsumedInfo consumedInfo = ConsumedInfo.builder()
                .currency(thisTimeConsumeNum * unitPrice)
                .build();
        orderConsume
                .setOrderId(order.getOrderId())
                .setNum(thisTimeConsumeNum)
                .setAdminUserId(adminUserId)
                .setConsume(false)
                .setCorpId(order.getCorpId())
                .setInfo(consumedInfo.getDesignate());
        Boolean consume = dingOpenApiService.orderConsume(order.getOrderId(), adminUserId, thisTimeConsumeNum, corpToken);
        orderConsume.setConsume(consume);
        dingOrderConsumes.add(orderConsume);
        if (consume) {
            order.setConsumeQuantity(order.getConsumeQuantity() + thisTimeConsumeNum);
            order.consumeStatus();

            CurrencyGoodsInfo currencyGoodsInfo = order.convertGoodInfoToCurrency();
            currencyGoodsInfo.setAmountOfCurrencyConsumed(order.getConsumeQuantity() * unitPrice);
            order.convertGoodsInfoToJsonObject(currencyGoodsInfo);

            dingOrders.add(order);
        }
        return consume;
    }

    private static BigDecimal calcUseCost(Integer assetCount, List<GoodsInfoDto> skuList) {
        // 通过资产数量获取价格
        BigDecimal useCostPrice = null;
        for (GoodsInfoDto infoDto : skuList) {
            if (assetCount <= infoDto.getAssetNum()) {
                useCostPrice = infoDto.getPrice();
                break;
            }
        }
        if (useCostPrice == null) {
            useCostPrice = skuList.get(skuList.size() - 1).getPrice();
        }
        return useCostPrice.multiply(new BigDecimal(assetCount));
    }

}
