package com.niimbot.asset.dingtalk.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.dingtalk.base.dto.DingSyncChangeDto;
import com.niimbot.asset.dingtalk.base.dto.DingSyncChangeEmpDto;
import com.niimbot.asset.dingtalk.base.dto.EmpTransferDto;
import com.niimbot.asset.dingtalk.base.dto.OrgTransferDto;
import com.niimbot.asset.dingtalk.base.model.DingSyncChange;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.system.RemoveEmployDto;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
public interface DingSyncChangeService extends IService<DingSyncChange> {

    List<DingSyncChangeDto> listChange(Integer type, Integer status);

    DingSyncChangeEmpDto getEmp(Long id);

    AsOrg getOrg(Long id);

    Boolean transferEmpEdit(EmpTransferDto empTransferDto);

    Boolean transferEmpDelete(Long id, RemoveEmployDto employ);

    Boolean transferOrgDelete(OrgTransferDto orgTransferDto);

    void saveWithSendMsg(List<DingSyncChange> changes);

    DingSyncChange lastUntreatedRecord(Long companyId, Long resId, Integer type);

    void removeUntreatedRecord(Long companyId, Long resId, Integer type);
}
