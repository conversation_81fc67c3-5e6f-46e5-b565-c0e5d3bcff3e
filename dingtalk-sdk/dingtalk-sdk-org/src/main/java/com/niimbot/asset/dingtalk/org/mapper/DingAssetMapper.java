package com.niimbot.asset.dingtalk.org.mapper;

import com.niimbot.means.AssetDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * created by chen.y on 2021/10/8 11:03
 */
public interface DingAssetMapper {

    List<AssetDto> selectUseOrgAsset(@Param("orgId") Long orgId, @Param("companyId") Long companyId);

    List<AssetDto> selectOrgOwnerAsset(@Param("orgId") Long orgId, @Param("companyId") Long companyId);

    List<Long> checkUseOrg(@Param("orgIds") List<Long> orgIds, @Param("companyId") Long companyId);

    List<Long> checkOrgOwner(@Param("orgIds") List<Long> orgIds, @Param("companyId") Long companyId);

    List<Long> checkUsePerson(@Param("userIds") List<Long> userIds, @Param("companyId") Long companyId);

    List<Long> checkManagerOwner(@Param("userIds") List<Long> userIds, @Param("companyId") Long companyId);

}
