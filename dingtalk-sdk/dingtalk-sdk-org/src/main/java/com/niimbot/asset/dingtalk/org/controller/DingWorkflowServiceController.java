package com.niimbot.asset.dingtalk.org.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dingtalk.base.dto.*;
import com.niimbot.asset.dingtalk.workflow.service.OfficialWorkflowService;
import com.niimbot.asset.dingtalk.workflow.service.OwnWorkflowService;
import com.niimbot.asset.dingtalk.workflow.service.WorkflowDataReviseService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/dingtalk/workflow")
@RequiredArgsConstructor
public class DingWorkflowServiceController {

    private final OfficialWorkflowService workflowService;

    private final OwnWorkflowService ownWorkflowService;

    private final WorkflowDataReviseService reviseService;

    @PostMapping("/node")
    public DingWorkflowNode workflowNode(@RequestBody GetDingWorkflowNode getDingWorkflowNode) {
        DingWorkflow dingWorkflow = DingWorkflow.buildForGetWorkflowNode(
                LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId(), getDingWorkflowNode.getDepId(), getDingWorkflowNode.getBizType(), getDingWorkflowNode.getOrderTypeId(), getDingWorkflowNode.getOrderType(), getDingWorkflowNode.getOrderData(), getDingWorkflowNode.getBizData()
        );
        return workflowService.getResolveWorkflowNode(dingWorkflow);
    }

    @PostMapping("/startProcess")
    public boolean startProcess(@RequestBody StartWorkflowProcess startWorkflowProcess) {
        DingWorkflow dingWorkflow = DingWorkflow.buildForStartProcessInstance
                (
                        LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId(), startWorkflowProcess.getDepId(),
                        startWorkflowProcess.getUid(), startWorkflowProcess.getBizType(),
                        startWorkflowProcess.getOrderTypeId(), startWorkflowProcess.getOrderType(),
                        startWorkflowProcess.getOrderId(),
                        startWorkflowProcess.getOrderData(),
                        startWorkflowProcess.getBizData()
                ).setActors(startWorkflowProcess.getActors());
        workflowService.startProcessInstance(dingWorkflow);
        return true;
    }

    @PostMapping("/revocationProcess")
    public Boolean revocationProcess(@RequestBody RevocationWorkflowProcess revocationWorkflowProcess) {
        DingWorkflow dingWorkflow = DingWorkflow.buildForRevocationProcessInstance
                (
                        LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId(),
                        revocationWorkflowProcess.getBizType(), revocationWorkflowProcess.getOrderId(),
                        revocationWorkflowProcess.getProcInstId()
                );
        return workflowService.revocationProcessInstance(dingWorkflow);
    }

    @PostMapping("/event")
    public Boolean workflowEvent(@RequestBody JSONObject event) {
        return workflowService.workflowEventHandler(event);
    }

    @GetMapping("/getUrl/{orderId}")
    public DingWorkflowUrl getWorkflowUrl(@PathVariable("orderId") Long id) {
        return workflowService.getWorkflowInstanceUrl(id);
    }

    @GetMapping("/getForms")
    public Object getWorkflowForms(@RequestParam(value = "empId", required = false) Long empId) {
        DingWorkflow dingWorkflow = new DingWorkflow().setEmpId(empId).setCompanyId(LoginUserThreadLocal.getCompanyId());
        return workflowService.getWorkflowForm(dingWorkflow);
    }

    @GetMapping("/useVersion")
    public Integer getUseVersion(@RequestParam("companyId") Long companyId) {
        return workflowService.getUseVersion(companyId);
    }

    @PostMapping("/upgradeVersion")
    public Boolean upgradeWorkflowVersion(@RequestParam("companyId") Long companyId) {
        return workflowService.upgradeVersion(companyId);
    }

    @GetMapping("/isEnabled")
    public Boolean workflowIsEnable(@RequestParam("bizType") Integer bizType, @RequestParam("companyId") Long companyId, @RequestParam("orderType") Integer orderType) {
        return workflowService.getOrderEnableWorkflow(bizType, companyId, orderType);
    }

    @PostMapping("/changeEnable")
    public Boolean changeEnable(@RequestParam("bizType") Integer bizType, @RequestParam("companyId") Long companyId, @RequestParam("orderType") Integer orderType, @RequestParam("change") Integer change) {
        return workflowService.changeEnable(bizType, companyId, orderType, change);
    }

    @PostMapping("/own/batchUpdateForm")
    public Boolean ownBatchUpdate() {
        return ownWorkflowService.updateForm(null);
    }

    @PostMapping("/revise/v2/{bizId}")
    public Boolean reviseV2(@PathVariable Long bizId) {
        return reviseService.ownWorkflowDataRevise(bizId);
    }

    @PostMapping("/own/updateForm")
    public Boolean ownUpdateForm(@RequestParam("companyId") Long companyId) {
        return ownWorkflowService.updateForm(companyId);
    }
}
