package com.niimbot.asset.dingtalk.org.service;

import com.niimbot.asset.sale.model.AsResourceConfig;
import com.niimbot.asset.system.model.AsCusUser;

import java.util.List;

/**
 * created by chen.y on 2022/5/20 14:30
 */
public interface GeneralService {

    List<AsCusUser> allAdminUser();

    String insideSku();

    AsResourceConfig getResourceConfigByGoodsCode(String goodsCode);

    Integer getDingSkuDuration(String goodsCode);

    String getDingSkuByGoodsSku(String skuCode);
}
