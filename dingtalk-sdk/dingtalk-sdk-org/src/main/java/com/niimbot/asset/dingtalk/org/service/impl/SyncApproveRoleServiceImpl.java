package com.niimbot.asset.dingtalk.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiRoleListResponse;
import com.dingtalk.api.response.OapiRoleSimplelistResponse;
import com.niimbot.asset.activiti.model.ActApproveRole;
import com.niimbot.asset.activiti.model.ActApproveRoleMember;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.dingtalk.base.dto.DingRole;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.org.service.SyncApproveRoleService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.dto.clientobject.ExternalRelation;
import com.niimbot.asset.system.ots.SystemEmployeeOts;
import com.niimbot.asset.system.ots.SystemOrgOts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncApproveRoleServiceImpl implements SyncApproveRoleService {

    @Resource
    private DingOpenApiService dingOpenApiService;

    @Resource
    private ActApproveRoleService approveRoleService;

    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Resource
    private SystemOrgOts systemOrgService;

    @Resource
    private SystemEmployeeOts systemEmployeeService;

    @Resource
    private DingCorpService dingCorpService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manualSyncFromDing(Long companyId) {
        DingCorp corp = dingCorpService.getByCompanyId(companyId);
        // 钉钉侧数据
        List<OapiRoleListResponse.OpenRoleGroup> roles = dingOpenApiService.getRoles(corp.getCorpId());
        if (CollUtil.isNotEmpty(roles)) {
            log.info("钉钉的审批角色列表 [{}]", JSONObject.toJSONString(roles));
        }
        List<ActApproveRole> dingRoles = new ArrayList<>();
        roles.forEach(v -> dingRoles.addAll(v.getRoles().stream().map(openRole -> new ActApproveRole().setExternalId(String.valueOf(openRole.getId())).setName(openRole.getName())).collect(Collectors.toList())));
        // 系统侧数据 处理角色
        List<ActApproveRole> systemRoles = approveRoleService.list(Wrappers.lambdaQuery(ActApproveRole.class).eq(ActApproveRole::getCompanyId, companyId));
        List<String> systemRoleIds = systemRoles.stream().map(ActApproveRole::getExternalId).collect(Collectors.toList());
        Map<String, ActApproveRole> dingRoleMap = dingRoles.stream().collect(Collectors.toMap(ActApproveRole::getExternalId, v -> v));
        // 新增 钉钉侧有系统侧没有的数据
        List<ActApproveRole> forAdd = dingRoles.stream().filter(v -> !systemRoleIds.contains(v.getExternalId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(forAdd)) {
            forAdd.forEach(v -> v.setId(IdUtils.getId()));
            approveRoleService.recommendRoleCode(companyId, forAdd);
        }
        // 更新 钉钉侧有系统侧也有的数据 更新角色名称
        List<ActApproveRole> forUpdate = systemRoles.stream()
                .filter(v -> Objects.nonNull(v.getExternalId()) && dingRoleMap.containsKey(v.getExternalId()))
                .peek(v -> v.setName(dingRoleMap.get(v.getExternalId()).getName()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(forUpdate)) {
            approveRoleService.updateBatchById(forUpdate);
        }
        // 删除 系统侧有钉钉侧没有的数据
        List<Long> forDelete = systemRoles.stream().filter(v -> Objects.nonNull(v.getExternalId()) && !dingRoleMap.containsKey(v.getExternalId())).map(ActApproveRole::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(forDelete)) {
            approveRoleService.removeByIds(forDelete);
            approveRoleMemberService.remove(Wrappers.lambdaUpdate(ActApproveRoleMember.class).in(ActApproveRoleMember::getApproveRoleId, forDelete));
        }
        // 循环处理角色中的人
        forUpdate.addAll(forAdd);
        handleUserRole(companyId, forUpdate);
        log.info("钉钉角色 forUpdate[{}]", JSONObject.toJSONString(forUpdate));
        return true;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    private void handleUserRole(Long companyId, List<ActApproveRole> roles) {
        DingCorp corp = dingCorpService.getByCompanyId(companyId);
        Map<String, Long> roleRelationMap = roles.stream().collect(Collectors.toMap(ActApproveRole::getExternalId, ActApproveRole::getId));
        log.info("钉钉角色MAP[{}]", JSONObject.toJSONString(roleRelationMap));
        roleRelationMap.forEach((k, v) -> {
            // 钉钉角色下的人
            List<OapiRoleSimplelistResponse.OpenEmpSimple> roleUsers = dingOpenApiService.getRoleUsers(corp.getCorpId(), Convert.toLong(k));
            if (CollUtil.isEmpty(roleUsers)) {
                return;
            }
            log.info("钉钉角色[{}]下的员工[{}]", k, JSONObject.toJSONString(roleUsers));
            roleUsers = roleUsers.stream().filter(distinctByKey(OapiRoleSimplelistResponse.OpenEmpSimple::getUserid)).collect(Collectors.toList());
            if (CollUtil.isEmpty(roleUsers)) {
                return;
            }
            log.info("钉钉角色[{}]下的去重后员工[{}]", k, JSONObject.toJSONString(roleUsers));
            List<String> dingUserIds = new ArrayList<>();
            List<String> dingUserDepIds = new ArrayList<>();
            roleUsers.forEach(u -> {
                dingUserIds.add(u.getUserid());
                if (CollUtil.isEmpty(u.getManageScopes())) {
                    u.setManageScopes(Collections.emptyList());
                }
                dingUserDepIds.addAll(u.getManageScopes().stream().map(d -> String.valueOf(d.getDeptId())).collect(Collectors.toList()));
            });
            // 组织ID映射关系
            List<ExternalRelation> orgRelations = systemOrgService.getOrgExternalRelation(companyId, dingUserDepIds);
            Map<String, Long> orgRelationMap = orgRelations.stream().collect(Collectors.toMap(ExternalRelation::getExternalId, ExternalRelation::getId));
            // 员工ID映射关系
            List<ExternalRelation> empRelation = systemEmployeeService.getEmpExternalRelation(companyId, dingUserIds);
            Map<String, Long> empRelationMap = empRelation.stream().collect(Collectors.toMap(ExternalRelation::getExternalId, ExternalRelation::getId));
            // 转换成人
            List<ActApproveRoleMember> toDb = roleUsers.stream()
                    .filter(u -> empRelationMap.containsKey(u.getUserid()))
                    .map(a -> {
                        ActApproveRoleMember member = new ActApproveRoleMember();
                        member.setMemberId(empRelationMap.get(a.getUserid()));
                        member.setApproveRoleId(v);
                        member.setOrgScope(a.getManageScopes().stream().filter(d -> orgRelationMap.containsKey(String.valueOf(d.getDeptId()))).map(d -> orgRelationMap.get(String.valueOf(d.getDeptId()))).collect(Collectors.toList()));
                        return member;
                    }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(toDb)) {
                log.info("钉钉角色关系保存[{}]", JSONObject.toJSONString(toDb));
            }
            toDb = toDb.stream().filter(distinctByKey((Function<ActApproveRoleMember, String>) r -> r.getApproveRoleId() + "" + r.getMemberId())).collect(Collectors.toList());
            // 先删除在重建
            approveRoleMemberService.remove(
                    Wrappers.lambdaUpdate(ActApproveRoleMember.class)
                            .eq(ActApproveRoleMember::getApproveRoleId, v)
            );
            if (CollUtil.isNotEmpty(toDb)) {
                log.info("钉钉角色关系保存 toDb[{}]", JSONObject.toJSONString(toDb));
            }
            approveRoleMemberService.saveBatch(toDb);
        });
    }

    @Override
    public boolean syncFromDing(Long companyId, List<Long> extRoleIds) {
        DingCorp corp = dingCorpService.getByCompanyId(companyId);
        List<DingRole> roles = dingOpenApiService.getRoles(corp.getCorpId(), extRoleIds);
        if (CollUtil.isEmpty(roles)) {
            return true;
        }
        Map<Long, String> roleMap = roles.stream().collect(Collectors.toMap(DingRole::getId, DingRole::getName));
        // 过滤掉在云资产系统中没有的
        List<ActApproveRole> systemRoles = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .in(ActApproveRole::getExternalId, roleMap.keySet().stream().map(String::valueOf).collect(Collectors.toList()))
        );
        handleUserRole(companyId, systemRoles);
        return false;
    }

    @Override
    public boolean roleCreateEvent(Long companyId, String account, JSONObject content) {
        String roleId = content.getString("role_id");
        String roleName = content.getString("role_name");
        if (!content.containsKey("group_id")) {
            return false;
        }
        // 只新增了角色组没有新增角色
        Long groupId = content.getLong("group_id");
        if (Objects.isNull(groupId) || groupId == 1 || groupId == -1) {
            return true;
        }
        List<ActApproveRole> roles = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .eq(ActApproveRole::getExternalId, roleId)
        );
        if (CollUtil.isEmpty(roles)) {
            ActApproveRole role = new ActApproveRole().setId(IdUtils.getId()).setExternalId(roleId).setName(roleName).setCompanyId(companyId);
            role.setCode(approveRoleService.recommendRoleCode(companyId));
            approveRoleService.save(role);
            return true;
        }
        return false;
    }

    @Override
    public boolean roleUpdateEvent(Long companyId, String account, JSONObject content) {
        String roleId = content.getString("role_id");
        String roleName = content.getString("role_name");
        if (!content.containsKey("group_id")) {
            return false;
        }
        // 只新增了角色组没有新增角色
        Long groupId = content.getLong("group_id");
        if (Objects.isNull(groupId) || groupId == 1 || groupId == -1) {
            return true;
        }
        approveRoleService.update(
                Wrappers.lambdaUpdate(ActApproveRole.class)
                        .set(ActApproveRole::getName, roleName)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .eq(ActApproveRole::getExternalId, roleId)
        );
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean roleRemoveEvent(Long companyId, String account, JSONObject content) {
        // 删除角色时钉钉没有返回角色ID
        String roleId = content.getString("role_id");
        if (StrUtil.isBlank(roleId)) {
            log.warn("ding role remove event roleId is null");
            return true;
        }
        // if (!content.containsKey("group_id")) {
        //     return false;
        // }
        // // 只新增了角色组没有新增角色
        // Long groupId = content.getLong("group_id");
        // if (Objects.isNull(groupId) || groupId == 1 || groupId == -1) {
        //     return true;
        // }
        List<ActApproveRole> roles = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .eq(ActApproveRole::getExternalId, roleId)
        );
        if (CollUtil.isEmpty(roles)) {
            return true;
        }
        List<Long> ids = roles.stream().map(ActApproveRole::getId).collect(Collectors.toList());
        approveRoleService.removeByIds(ids);
        approveRoleMemberService.remove(
                Wrappers.lambdaQuery(ActApproveRoleMember.class)
                        .in(ActApproveRoleMember::getApproveRoleId, ids)
        );
        return true;
        // return this.manualSyncFromDing(companyId);
    }
}
