package com.niimbot.asset.dingtalk.org.service;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SyncApproveRoleService {

    boolean manualSyncFromDing(Long companyId);

    boolean syncFromDing(Long companyId, List<Long> extRoleIds);

    boolean roleCreateEvent(Long companyId, String account, JSONObject content);

    boolean roleUpdateEvent(Long companyId, String account, JSONObject content);

    boolean roleRemoveEvent(Long companyId, String account, JSONObject content);
}
