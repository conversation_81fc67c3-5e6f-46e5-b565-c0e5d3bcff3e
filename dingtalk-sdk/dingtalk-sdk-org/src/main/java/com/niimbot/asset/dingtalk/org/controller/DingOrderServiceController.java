package com.niimbot.asset.dingtalk.org.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingOrder;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.org.service.DingOrderService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

/**
 * created by chen.y on 2021/8/26 18:28
 */
@Slf4j
@RestController
@RequestMapping("server/dingtalk/dingOrder")
public class DingOrderServiceController {

    private final DingOrderService dingOrderService;
    private final DingCorpService dingCorpService;

    @Autowired
    public DingOrderServiceController(DingOrderService dingOrderService,
                                      DingCorpService dingCorpService) {
        this.dingOrderService = dingOrderService;
        this.dingCorpService = dingCorpService;
    }

    @PostMapping
    public Boolean save(@RequestBody DingOrder dingOrder) {
        DingCorp one = dingCorpService.getOne(new LambdaQueryWrapper<DingCorp>().eq(DingCorp::getCorpId, dingOrder.getCorpId()));
        if (one != null) {
            dingOrder.setCompanyId(one.getCompanyId());
            // 如果订单包含 mainCorpId 说明是个人版, 会写 mainCorpId 到 dingCorp
            /*if (dingOrder.getSourceData().containsKey(DingCorp.MAIN_CORP_ID)) {
                log.info("个人版 MAIN_CORP_ID --> {}", dingOrder.getSourceData().getString(DingCorp.MAIN_CORP_ID));
                one.setMainCorpId(dingOrder.getSourceData().getString(DingCorp.MAIN_CORP_ID));
                dingCorpService.updateById(one);
            }*/
        }
        return this.dingOrderService.handleOrder(dingOrder);
    }

}
