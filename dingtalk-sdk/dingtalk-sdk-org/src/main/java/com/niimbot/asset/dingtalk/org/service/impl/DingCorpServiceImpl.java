package com.niimbot.asset.dingtalk.org.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.org.mapper.DingCorpMapper;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.CusAccountService;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Service
public class DingCorpServiceImpl extends ServiceImpl<DingCorpMapper, DingCorp> implements DingCorpService {

    @Override
    public Long getCompanyId(String corpId) {
        if (StrUtil.isBlank(corpId)) {
            return null;
        }
        DingCorp corp = this.getOne(
                Wrappers.lambdaQuery(DingCorp.class)
                        .select(DingCorp::getCompanyId)
                        .eq(DingCorp::getCorpId, corpId)
        );
        return Objects.isNull(corp) ? null : corp.getCompanyId();
    }

    @Override
    public DingCorp getByCompanyId(Long companyId) {
        DingCorp dingCorp = this.getById(companyId);
        if (Objects.isNull(dingCorp)) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        return dingCorp;
    }

    @Override
    public String getCorpId(Long companyId) {
        DingCorp corp = this.getOne(
                Wrappers.lambdaQuery(DingCorp.class)
                        .select(DingCorp::getCorpId)
                        .eq(DingCorp::getCompanyId, companyId)
        );
        if (Objects.isNull(corp)) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        return corp.getCorpId();
    }

    @Override
    public Boolean removeCorp(String corpId, JSONObject content) {
        DingCorp dingCorp = this.getOne(
                Wrappers.lambdaQuery(DingCorp.class)
                        .eq(DingCorp::getCorpId, corpId)
        );
        if (Objects.isNull(dingCorp)) {
            return true;
        }
        Long agentId = content.getLong("agentId");
        if (Objects.isNull(dingCorp.getAgentId()) || !dingCorp.getAgentId().equals(agentId)) {
            return false;
        }
        dingCorp.setAppIsRemove(true);
        boolean updateById = this.updateById(dingCorp);
        if (updateById) {
            // 禁用企业
            SpringUtil.getBean(CompanyService.class).update(
                    Wrappers.lambdaUpdate(AsCompany.class)
                            .set(AsCompany::getStatus, ManageConstant.COMPANY_STATUS_DISABLE)
                            .eq(AsCompany::getId, dingCorp.getCompanyId())
            );
            // 踢出登录
            List<AsCusUser> users = SpringUtil.getBean(AsAccountEmployeeService.class).list(
                            Wrappers.lambdaQuery(AsAccountEmployee.class)
                                    .eq(AsAccountEmployee::getCompanyId, dingCorp.getCompanyId())
                    ).stream().map(v -> new AsCusUser().setId(v.getEmployeeId()).setCompanyId(v.getCompanyId()))
                    .collect(Collectors.toList());
            SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(users);
        }
        return updateById;
    }
}
