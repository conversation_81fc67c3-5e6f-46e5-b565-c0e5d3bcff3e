package com.niimbot.asset.dingtalk.org.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.dingtalk.base.dto.ShoppingCartBonusDto;
import com.niimbot.asset.dingtalk.base.model.AsShoppingCartBonus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsShoppingCartBonusMapper extends BaseMapper<AsShoppingCartBonus> {

    /**
     * 查询赠品信息
     * @param mainProductIdList
     * @return
     */
    List<ShoppingCartBonusDto> selectByMainProductId(@Param("mainProductIdList") List<Long> mainProductIdList);
}