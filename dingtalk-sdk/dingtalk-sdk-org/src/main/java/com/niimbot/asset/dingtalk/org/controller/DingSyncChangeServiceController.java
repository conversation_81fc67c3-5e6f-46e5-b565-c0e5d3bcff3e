package com.niimbot.asset.dingtalk.org.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.dingtalk.base.dto.DingSyncChangeCountDto;
import com.niimbot.asset.dingtalk.base.dto.DingSyncChangeDto;
import com.niimbot.asset.dingtalk.base.dto.DingSyncChangeEmpDto;
import com.niimbot.asset.dingtalk.base.dto.EmpTransferDto;
import com.niimbot.asset.dingtalk.base.dto.OrgTransferDto;
import com.niimbot.asset.dingtalk.base.model.DingSyncChange;
import com.niimbot.asset.dingtalk.org.service.DingSyncChangeService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.system.RemoveEmployDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;

/**
 * created by chen.y on 2021/9/29 15:36
 */
@RestController
@RequestMapping("server/dingtalk/syncChange")
public class DingSyncChangeServiceController {

    private final DingSyncChangeService dingSyncChangeService;

    private final AsCusEmployeeService employeeService;

    @Autowired
    public DingSyncChangeServiceController(DingSyncChangeService dingSyncChangeService, AsCusEmployeeService employeeService) {
        this.dingSyncChangeService = dingSyncChangeService;
        this.employeeService = employeeService;
    }

    @GetMapping("/list")
    public List<DingSyncChangeDto> list(@RequestParam("type") Integer type, @RequestParam("status") Integer status) {
        return dingSyncChangeService.listChange(type, status);
    }

    @GetMapping("/emp/{id}")
    public DingSyncChangeEmpDto getEmp(@PathVariable("id") Long id) {
        return dingSyncChangeService.getEmp(id);
    }

    @GetMapping("/org/{id}")
    public AsOrg getOrg(@PathVariable("id") Long id) {
        return dingSyncChangeService.getOrg(id);
    }

    @PostMapping("/emp/transfer/edit")
    public Boolean transferEmpEdit(@RequestBody EmpTransferDto empTransferDto) {
        return dingSyncChangeService.transferEmpEdit(empTransferDto);
    }

    @PostMapping("/emp/transfer/remove/{id}")
    public Boolean transferEmpDelete(@PathVariable("id") Long id, @RequestBody RemoveEmployDto employ) {
        return dingSyncChangeService.transferEmpDelete(id, employ);
    }

    @PostMapping("/org/transfer/remove")
    public Boolean transferOrgDelete(@RequestBody OrgTransferDto orgTransferDto) {
        return dingSyncChangeService.transferOrgDelete(orgTransferDto);
    }

    @GetMapping("/count")
    public DingSyncChangeCountDto count() {
        if (BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            List<DingSyncChange> dingSyncChanges = dingSyncChangeService.list(
                    new LambdaQueryWrapper<DingSyncChange>().eq(DingSyncChange::getStatus, 1));
            int emp = 0;
            int org = 0;
            int minTime = 0;
            for (DingSyncChange dingSyncChange : dingSyncChanges) {
                Integer type = dingSyncChange.getType();
                if (ListUtil.of(1, 2).contains(type)) {
                    emp++;
                } else if (type == 3) {
                    org++;
                }
            }
            return new DingSyncChangeCountDto().setEmp(emp).setOrg(org).setTime(minTime);
        } else {
            return new DingSyncChangeCountDto().setEmp(0).setOrg(0).setTime(0);
        }
    }

    @GetMapping("/empImageUrl/{id}")
    public String empImageUrl(@PathVariable Long id) {
        AsCusEmployee employee = employeeService.getDeleted(id);
        return Objects.isNull(employee) ? "" : employee.getImage();
    }

    // @GetMapping("/{id}/{type}")
    // public Object getByIdWithType(@PathVariable Long id, @PathVariable Integer type) {
    //     DingSyncChange change = dingSyncChangeService.getById(id);
    //     if (Objects.isNull(change)) {
    //         throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "异动信息不存在");
    //     }
    //     SyncChangeInfo info = new SyncChangeInfo();
    //     info.setId(change.getId()).setType(change.getType());
    //     info.setEmpName(cacheResourceUtil.getUserName(change.getResId()));
    //     List<SyncChangeInfo.Each> eaches = new ArrayList<>(1);
    //     // 员工部门异动
    //     if (change.getType() == 1) {
    //         eaches = change.getFromOrg().stream().map(v -> {
    //             SyncChangeInfo.Each each = new SyncChangeInfo.Each().setId(v).setKey(cacheResourceUtil.getOrgName(v));
    //             each.setValue(dingAssetMapper.checkUseOrg(Collections.singletonList(v), change.getCompanyId()).size());
    //             return each;
    //         }).collect(Collectors.toList());
    //     }
    //     // 员工离职
    //     if (change.getType() == 2) {
    //         eaches = Stream.of("他管理的资产", "他在用的资产", "他审批的单据");
    //         eaches.add(new SyncChangeInfo.Each().setKey("他管理的资产").setValue(dingAssetMapper.checkManagerOwner(Collections.singletonList(change.getResId()), change.getCompanyId()).size()));
    //         eaches.add(new SyncChangeInfo.Each().setKey("他在用的资产").setValue(dingAssetMapper.checkUsePerson(Collections.singletonList(change.getResId()), change.getCompanyId()).size()));
    //         eaches.add(new SyncChangeInfo.Each().setKey("他审批的单据").setValue())
    //     }
    //
    // }
}
