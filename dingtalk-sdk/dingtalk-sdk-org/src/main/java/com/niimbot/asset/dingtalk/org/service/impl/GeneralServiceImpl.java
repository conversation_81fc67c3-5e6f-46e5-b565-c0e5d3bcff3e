package com.niimbot.asset.dingtalk.org.service.impl;

import com.niimbot.asset.dingtalk.org.mapper.GeneralMapper;
import com.niimbot.asset.dingtalk.org.service.GeneralService;
import com.niimbot.asset.sale.model.AsResourceConfig;
import com.niimbot.asset.system.model.AsCusUser;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * created by chen.y on 2022/5/20 14:31
 */
@Service
public class GeneralServiceImpl implements GeneralService {

    private final GeneralMapper generalMapper;

    @Autowired
    public GeneralServiceImpl(GeneralMapper generalMapper) {
        this.generalMapper = generalMapper;
    }

    @Override
    public List<AsCusUser> allAdminUser() {
        return this.generalMapper.allAdminUser();
    }

    @Override
    public String insideSku() {
        return generalMapper.insideSku();
    }

    @Override
    public AsResourceConfig getResourceConfigByGoodsCode(String goodsCode) {
        return generalMapper.getResourceConfigByGoodsCode(goodsCode);
    }

    @Override
    public Integer getDingSkuDuration(String goodsCode) {
        return generalMapper.getDingSkuDuration(goodsCode);
    }

    @Override
    public String getDingSkuByGoodsSku(String skuCode) {
        return generalMapper.selectDingSkuByGoodsSku(skuCode);
    }
}
