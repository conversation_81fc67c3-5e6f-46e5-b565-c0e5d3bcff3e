package com.niimbot.asset.dingtalk.org.service.impl;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.activiti.service.ActWorkflowFieldService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingOrder;
import com.niimbot.asset.dingtalk.base.model.GoodsTryout;
import com.niimbot.asset.dingtalk.base.model.constant.RedisConstant;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.org.service.DingEmpService;
import com.niimbot.asset.dingtalk.org.service.DingOrderService;
import com.niimbot.asset.dingtalk.org.service.OrgSuiteAuthService;
import com.niimbot.asset.dynamicform.service.AsBizFormAssetService;
import com.niimbot.asset.dynamicform.service.AsBizFormMaterialService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.DateUtils;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.material.model.AsRepository;
import com.niimbot.asset.material.service.AsMaterialOrderTypeService;
import com.niimbot.asset.material.service.AsRepositoryService;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.asset.system.abs.AssetQueryViewAbs;
import com.niimbot.asset.system.abs.CompanyResourceAbs;
import com.niimbot.asset.system.abs.FormAbs;
import com.niimbot.asset.system.abs.MessageAbs;
import com.niimbot.asset.system.abs.PurchaseOrderTypeAbs;
import com.niimbot.asset.system.dto.AssetQueryViewInitCmd;
import com.niimbot.asset.system.dto.BizFormAssetInitCmd;
import com.niimbot.asset.system.dto.BizFormMaterialInitCmd;
import com.niimbot.asset.system.dto.CompanyResourceAddCmd;
import com.niimbot.asset.system.dto.FormInitCmd;
import com.niimbot.asset.system.dto.PurchaseOrderTypeInitCmd;
import com.niimbot.asset.system.dto.clientobject.CompanyResourceCO;
import com.niimbot.asset.system.event.CompanyRegisterEvent;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCompanyChannel;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsIndustry;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsPromoteChannel;
import com.niimbot.asset.system.model.AsPromoteChannelSource;
import com.niimbot.asset.system.model.AsUserReports;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCompanyChannelService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsRoleDataAuthorityService;
import com.niimbot.asset.system.service.AsRoleMenuService;
import com.niimbot.asset.system.service.CompanyNewbieTaskService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.CusRoleService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.IndustryService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.UserReportsService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * created by chen.y on 2021/8/27 14:49
 */
@Slf4j
@Service
public class OrgSuiteAuthServiceImpl implements OrgSuiteAuthService {

    private final ThreadPoolTaskExecutor taskExecutor;

    private final OrgService orgService;

    private final DingOpenApiService dingOpenApiService;

    private final DingEmpService dingEmpService;

    private final DingCorpService dingCorpService;

    private final DingOrderService dingOrderService;

    private final RedisService redisService;

    @Autowired
    public OrgSuiteAuthServiceImpl(ThreadPoolTaskExecutor taskExecutor,
                                   OrgService orgService,
                                   DingOpenApiService dingOpenApiService,
                                   DingEmpService dingEmpService,
                                   DingCorpService dingCorpService,
                                   DingOrderService dingOrderService,
                                   RedisService redisService) {
        this.taskExecutor = taskExecutor;
        this.orgService = orgService;
        this.dingOpenApiService = dingOpenApiService;
        this.dingEmpService = dingEmpService;
        this.dingCorpService = dingCorpService;
        this.dingOrderService = dingOrderService;
        this.redisService = redisService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long register(JSONObject content) {
        // 获取企业信息
        DingCorp dingCorp = content.getJSONObject("auth_corp_info").toJavaObject(DingCorp.class);
        JSONArray agent = content.getJSONObject("auth_info").getJSONArray("agent");
        Long agentId = ((JSONObject) JSON.toJSON(agent.get(0))).getLong("agentid");
        dingCorp.setAgentId(agentId);
        DingCorp corp = dingCorpService.getOne(new LambdaQueryWrapper<DingCorp>().eq(DingCorp::getCorpId, dingCorp.getCorpId()));

        if (corp != null) {
            log.info("register，企业【{}】已经注册", corp.getCorpName());
            // 更新APP删除标记为未删除，同时更新授权信息
            if (corp.getAppIsRemove()) {
                log.info("register，企业【{}】重新开通", corp.getCorpName());
                dingCorp.setAppIsRemove(false).setCompanyId(corp.getCompanyId());
                dingCorpService.updateById(dingCorp);
                // 更新企业为企业状态
                SpringUtil.getBean(CompanyService.class).update(
                        Wrappers.lambdaUpdate(AsCompany.class)
                                .set(AsCompany::getStatus, ManageConstant.COMPANY_STATUS_ENABLE)
                                .eq(AsCompany::getId, dingCorp.getCompanyId())
                );
                // 强制刷新token
                try {
                    redisService.del(RedisConstant.getCorpTokenKey(dingCorp.getCorpId()));
                    redisService.del(RedisConstant.getV1CorpTokenKey(dingCorp.getCorpId()));
                } catch (Exception e) {
                    log.warn("register，企业【{}】清除缓存中的token失败", dingCorp.getCorpName(), e);
                }
            }
            return corp.getCompanyId();
        }
        // ========================= 1、创建公司 =====================================
        Long companyId = initCompany(dingCorp);
        log.debug("init company success {}", companyId);

        initCompanyChannel(companyId, 1851L, 9233L);

        // ========================= 2、公司组织表 ====================================
        Long orgId = initOrg(companyId, dingCorp);
        log.debug("init org success {}", orgId);

        // ========================= 3、公司角色表、管理员角色信息、公司用户表 =============
        initCompanyRole(companyId);
        log.debug("init company role success");

        // ========================= 4、用户账号表、员工表、用户扩展表、用户首页setting相关 =============
        // 开通的管理员，设置为系统超级管理员
        String registerUserId = content.getJSONObject("auth_user_info").getString("userId");
        Long empId = initAccount(registerUserId, dingCorp.getCorpId(), companyId, orgId);
        log.debug("init user and employee success");

        // ========================= 5、合同表 、角色菜单信息 ============================
        initCompanyContract(companyId, dingCorp.getCorpName());
        log.debug("init company contract success");

        // ========================= 6、分类表 =========================================
        initCategory(companyId);
        log.debug("init company category success");

        // ========================= 6、区域表 =========================================
        initCompanyArea(companyId, orgId);
        log.debug("init company area success");

        // ========================= 仓库表 =========================================
        initRepository(companyId, orgId);

        // ========================= 7、用户统计表 =========================================
        initUserReports(companyId);
        log.debug("init user report success");

        // 用户资产查询视图
        initAssetQueryView(companyId, empId);

        // ========================= 8、资产配置 =========================================
        SpringUtil.getBean(FormAbs.class).initAssetItems(new BizFormAssetInitCmd()
                .setCompanyId(companyId));
        log.debug("init assetAttr success");

        // ========================= 9、耗材配置 =========================================
        SpringUtil.getBean(FormAbs.class).initMaterialItems(new BizFormMaterialInitCmd()
                .setCompanyId(companyId));
        log.debug("init materialAssetAttr success");

        // ========================= 10、单据配置 =========================================
        SpringUtil.getBean(AsOrderTypeService.class).initCompanyOrderType(companyId);
        log.debug("init orderType and orderField success");

        // 表单初始化
        SpringUtil.getBean(FormAbs.class).initCompanyForm(new FormInitCmd().setCompanyId(companyId));

        // ========================= 11、耗材单据配置 =========================================
        SpringUtil.getBean(AsMaterialOrderTypeService.class).initCompanyOrderType(companyId);
        log.debug("init material orderType and orderField success");

        // 采购单据配置
        SpringUtil.getBean(PurchaseOrderTypeAbs.class).initCompanyOrderType(new PurchaseOrderTypeInitCmd().setCompanyId(companyId));

        // ========================= 12、流程条件配置 =========================================
        SpringUtil.getBean(ActWorkflowFieldService.class).initCompanyOrderField(companyId);
        log.debug("init workflow field  success");

        // ========================= 13、审批流配置 =========================================
        SpringUtil.getBean(ActWorkflowService.class).initActWorkflow(companyId);
        log.debug("init workflow success");

        // 15、审批角色初始化
        SpringUtil.getBean(ActApproveRoleService.class).initRoles(companyId);

        // ========================= 14、消息配置 =========================================
        SpringUtil.getBean(MessageAbs.class).companyMessageRuleDataInit(companyId);
        log.debug("init message rule success");

        // 新手任务录入
        SpringUtil.getBean(CompanyNewbieTaskService.class).init(companyId);

        // 处理订单
        DingOrder one = dingOrderService.getOne(Wrappers.lambdaQuery(DingOrder.class)
                .eq(DingOrder::getCorpId, dingCorp.getCorpId())
                .eq(DingOrder::getStatus, com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_1));
        if (one != null) {
            one.setCompanyId(companyId);
            dingOrderService.handleOrder(one);
        }

        // 防止biz_63与订单的先后顺序不一致
        String tryoutType = Convert.toStr(redisService.get(RedisConstant.getGoodsTryout(dingCorp.getCorpId())));
        if (StrUtil.isNotEmpty(tryoutType)) {
            dingCorpService.update(Wrappers.lambdaUpdate(DingCorp.class)
                    .set(DingCorp::getAuthChannelType, tryoutType)
                    .eq(DingCorp::getCorpId, dingCorp.getCorpId())
                    .ne(DingCorp::getAuthChannelType, GoodsTryout.TryoutType.ENTERPRISE_TRYOUT.getValue()));
        }
        registerEvent(empId, companyId);
        return companyId;
    }

    private void registerEvent(Long adminId, Long companyId) {
        try {
            String mobile = "";
            // 先找员工的手机号
            AsCusEmployee admin = SpringUtil.getBean(AsCusEmployeeService.class).getById(adminId);
            if (Objects.isNull(admin)) {
                log.warn("registerEvent admin is null");
                return;
            }
            mobile = admin.getMobile();
            if (StrUtil.isBlank(mobile)) {
                // 找账号的手机号
                AsAccountEmployee accountEmployee = SpringUtil.getBean(AsAccountEmployeeService.class)
                        .getOne(
                                Wrappers.lambdaQuery(AsAccountEmployee.class)
                                        .select(AsAccountEmployee::getAccountId)
                                        .eq(AsAccountEmployee::getEmployeeId, adminId)
                                , false
                        );
                if (Objects.nonNull(accountEmployee) && Objects.nonNull(accountEmployee.getAccountId())) {
                    AsCusUser cusUser = SpringUtil.getBean(CusUserService.class)
                            .getById(accountEmployee.getAccountId());
                    if (Objects.nonNull(cusUser)) {
                        mobile = cusUser.getMobile();
                    }
                }
            }
            if (StrUtil.isNotBlank(mobile)) {
                CompanyRegisterEvent event = new CompanyRegisterEvent(companyId);
                event.setRegisterMobile(mobile);
                EventPublishHandler.publish(event);
            }
        } catch (Exception e) {
            log.warn("ding register push event error", e);
        }
    }

    private void initCategory(Long companyId) {
        AsCategory cate = new AsCategory();
        cate.setCompanyId(companyId)
                .setIndustryId(1L)
                .setCategoryName("默认分类")
                .setCategoryCode("A01")
                .setSourceId(0L)
                .setPid(0L);
        cate.setPaths("0,");
        cate.setLevel(0);
        SpringUtil.getBean(CategoryService.class).save(cate);
    }

    private void initRepository(Long companyId, Long orgId) {
        AsRepository repository = new AsRepository();
        repository.setName("默认仓库")
                .setCode("A01")
                .setCompanyId(companyId)
                .setManagerOwner(orgId);
        AsRepositoryService repositoryService = SpringUtil.getBean(AsRepositoryService.class);
        repositoryService.save(repository);
        taskExecutor.execute(() -> repositoryService.loadRepoCache(ListUtil.of(repository)));
    }

    private Long initCompany(DingCorp dingCorp) {
        IndustryService industryService = SpringUtil.getBean(IndustryService.class);

        // 查询行业信息
        List<AsIndustry> industryList = industryService.list();
        Long industryId = -1L;
        String industryName = "";
        if (CollUtil.isNotEmpty(industryList)) {
            industryId = industryList.get(0).getId();
            industryName = industryList.get(0).getIndustryName();
        }

        // 创建公司数据
        CompanyService companyService = SpringUtil.getBean(CompanyService.class);
        Long companyId = IdUtils.getId();
        AsCompany company = new AsCompany()
                .setId(companyId)
                .setName(dingCorp.getCorpName())
                .setLogo(dingCorp.getCorpLogoUrl())
                .setIndustryId(industryId)
                .setIndustryName(industryName);
        companyService.save(company);

        // 记录钉钉企业
        dingCorp.setCompanyId(companyId);
        // 如果订单包含 mainCorpId 说明是个人版, 会写 mainCorpId 到 dingCorp
        /*DingOrder individualOrder = dingOrderService.getOne(Wrappers.<DingOrder>lambdaQuery()
                .eq(DingOrder::getCorpId, dingCorp.getCorpId())
                .eq(DingOrder::getItemName, "免费规格"));
        if (individualOrder != null && individualOrder.getSourceData().containsKey(DingCorp.MAIN_CORP_ID)) {
            log.info("个人版 MAIN_CORP_ID --> {}", individualOrder.getSourceData().getString(DingCorp.MAIN_CORP_ID));
            dingCorp.setMainCorpId(individualOrder.getSourceData().getString(DingCorp.MAIN_CORP_ID));
        }*/
        dingCorp.setAuthChannelType(StrUtil.EMPTY);
        dingCorpService.save(dingCorp);

        // 赠送企业资源包
        CompanyResourceCO resourceCO = new CompanyResourceCO();
        resourceCO.setCompanyId(companyId)
                .setSkuCode("Trial")
                .setResourceName("体验版")
                .setCapacity(10)
                .setExperience(true)
                .setEffectiveTime(LocalDateTime.now())
                .setExpirationTime(LocalDateTime.now().plusYears(10).withHour(23).withMinute(59).withSecond(59).withNano(0));
        SpringUtil.getBean(CompanyResourceAbs.class).saveResource(new CompanyResourceAddCmd().setCompanyResource(resourceCO));

        return companyId;
    }

    private Long initAccount(String userId, String corpId, Long companyId, Long orgId) {
        OapiV2UserGetResponse.UserGetResponse response = dingOpenApiService.getUserByUserId(userId, corpId, null);
        return dingEmpService.saveAdmin(companyId, response, orgId);
    }

    private Long initOrg(Long companyId, DingCorp dingCorp) {
//        String corpToken = corpTokenManage.getCorpToken(corpId);
//        AsOrg rootOrg = dingOrgService.getRootOrg(companyId, corpToken);
        // 查询当前节点
        AsOrg org = new AsOrg();
        org.setId(IdUtils.getId());
        org.setExternalOrgId("1");
        org.setOrgName(dingCorp.getFullCorpName());
        org.setPid(0L);
        org.setExternalPid("0");
        org.setPaths("0,");
        org.setLevel(0);
        org.setOrgType(AssetConstant.ORG_TYPE_COMPANY);
        org.setCompanyId(companyId);
        org.setCompanyOwner(org.getId());
        orgService.save(org);
        return org.getId();
    }

    private void initCompanyRole(Long companyId) {
        Long adminRoleId = IdUtils.getId();
        Long assetAdminRoleId = IdUtils.getId();
        Long commonRoleId = IdUtils.getId();
        // 公司角色信息
        List<AsCusRole> roles = Lists.newArrayList(
                new AsCusRole()
                        .setId(adminRoleId)
                        .setRoleName("超级管理员").setRoleCode(BaseConstant.ADMIN_ROLE)
                        .setCanDelete(false)
                        .setCompanyId(companyId),
                new AsCusRole()
                        .setId(assetAdminRoleId)
                        .setRoleName("资产管理员").setRoleCode(BaseConstant.ASSET_ADMIN_ROLE)
                        .setCanDelete(false)
                        .setCompanyId(companyId),
                new AsCusRole()
                        .setId(commonRoleId)
                        .setRoleName("普通员工").setRoleCode(BaseConstant.COMMON_ROLE)
                        .setCanDelete(false)
                        .setIsDefault(Boolean.TRUE)
                        .setCompanyId(companyId)
        );
        SpringUtil.getBean(CusRoleService.class).saveBatch(roles);

        // 添加资产管理员初始菜单
        SpringUtil.getBean(AsRoleMenuService.class).initAssetRole(assetAdminRoleId);
        // 初始化资产管理员角色数据权限
        SpringUtil.getBean(AsRoleDataAuthorityService.class).initRoleDataAuth(companyId, assetAdminRoleId, BaseConstant.ASSET_ADMIN_ROLE);

        // 添加员工初始菜单
        SpringUtil.getBean(AsRoleMenuService.class).initCommonRole(commonRoleId);
        // 初始化普通员工角色数据权限
        SpringUtil.getBean(AsRoleDataAuthorityService.class).initRoleDataAuth(companyId, commonRoleId, BaseConstant.COMMON_ROLE);

    }

    private void initCompanyContract(Long companyId, String corpName) {
//        SpringUtil.getBean(AsContractService.class).createContract(companyId, corpName);
        // 公司setting
        AsCompanySetting asCompanySetting = new AsCompanySetting().setCompanyId(companyId)
                .setExpandSwitch(new CompanySwitch());
        SpringUtil.getBean(CompanySettingService.class).save(asCompanySetting);
    }

    private void initCompanyArea(Long companyId, Long orgId) {
        AsArea area = new AsArea();
        area.setId(IdUtils.getId());
        area.setCompanyId(companyId).setAreaCode("01").setAreaName("默认区域")
                .setOrgId(orgId)
                .setPid(0L);
        area.setLevel(0);
        area.setPaths("0,");
        AreaService areaService = SpringUtil.getBean(AreaService.class);
        areaService.save(area);
        // 写入redis
        taskExecutor.execute(() -> areaService.loadAreaCache(ListUtil.of(area)));
    }

    private void initUserReports(Long companyId) {
        AsUserReports userReports = new AsUserReports().setCompanyId(companyId).setDayTime(LocalDateTime.now())
                .setWeekTime(DateUtils.getWeeksNum(DateUtils.now()));
        SpringUtil.getBean(UserReportsService.class).save(userReports);
    }

    private void initAssetQueryView(Long companyId, Long userId) {
        SpringUtil.getBean(AssetQueryViewAbs.class).initUserView(new AssetQueryViewInitCmd().setCompanyId(companyId)
                .setEmployeeId(userId));
    }

    private void initCompanyChannel(Long companyId, Long ch, Long ag) {
        AsCompanyChannel channel = new AsCompanyChannel();
        channel.setId(companyId);
        AsPromoteChannel promoteChannel = SpringUtil.getBean(AsCompanyChannelService.class)
                .getPromoteChannelByCode(ch);
        AsPromoteChannelSource source = SpringUtil.getBean(AsCompanyChannelService.class)
                .getAsPromoteChannelSourceByCode(ag);
        if ((promoteChannel == null || source == null) || !source.getPromoteChannelId().equals(promoteChannel.getId())) {
            channel.setChannelCode(0L);
            channel.setSourceCode(0L);
        } else {
            channel.setChannelCode(ch);
            channel.setSourceCode(ag);
        }
        SpringUtil.getBean(AsCompanyChannelService.class).save(channel);
    }

}
