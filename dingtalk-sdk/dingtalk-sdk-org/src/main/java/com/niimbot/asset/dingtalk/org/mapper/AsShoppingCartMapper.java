package com.niimbot.asset.dingtalk.org.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.dingtalk.base.dto.ShoppingCartProductDto;
import com.niimbot.asset.dingtalk.base.model.AsShoppingCart;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsShoppingCartMapper extends BaseMapper<AsShoppingCart> {

    /**
     * 查询当前用户的购物车商品信息
     * @param userId
     * @return
     */
    List<ShoppingCartProductDto> selectShoppingCart(@Param("userId") Long userId);

    /**
     * 用户购物车商品数量
     * @param userId
     * @return
     */
    Integer productCount(@Param("userId") Long userId);
}