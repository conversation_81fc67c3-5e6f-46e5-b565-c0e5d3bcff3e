package com.niimbot.asset.dingtalk.org.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dingtalk.base.dto.*;
import com.niimbot.asset.dingtalk.base.enums.GoodsTypeEnum;
import com.niimbot.asset.dingtalk.base.model.AsShoppingCart;
import com.niimbot.asset.dingtalk.base.model.AsShoppingCartBonus;
import com.niimbot.asset.dingtalk.org.mapper.AsShoppingCartMapper;
import com.niimbot.asset.dingtalk.org.service.GeneralService;
import com.niimbot.asset.dingtalk.org.service.ShoppingCartBonusService;
import com.niimbot.asset.dingtalk.org.service.ShoppingCartService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.model.AsSaleOrderItem;
import com.niimbot.asset.sale.service.*;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.jf.core.exception.category.BusinessException;
import cn.hutool.core.collection.CollUtil;
import com.niimbot.sale.MallGoodsDto;
import com.niimbot.sale.ResourcePackDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:09
 */
@Slf4j
@Service
public class ShoppingCartServiceImpl extends ServiceImpl<AsShoppingCartMapper, AsShoppingCart> implements ShoppingCartService {

    @Autowired
    private ResourceConfigService resourceConfigService;
    @Autowired
    private ShoppingCartBonusService shoppingCartBonusService;
    @Autowired
    private AsMallGoodsService mallGoodsService;
    @Autowired
    private GeneralService generalService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private AsSaleOrderService saleOrderService;
    @Autowired
    private AsSaleOrderItemService saleOrderItemService;
    @Autowired
    private AsCusEmployeeService cusEmployeeService;
    @Autowired
    private DiscountConfigService discountConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addProduct(OrderRequestDto orderRequestDto) {
        //商品按软件-硬件类型进行分类
        Map<Integer, List<OrderProductRequestDto>> productTypeMap = orderRequestDto.getProductList().stream()
                .collect(Collectors.groupingBy(OrderProductRequestDto::getGoodsType));

        //商品编码列表
        List<String> productSkuCodeList = orderRequestDto.getProductList().stream()
                .map(OrderProductRequestDto::getSkuCode).collect(Collectors.toList());

        //查询当前用户购物车当前存在的商品信息
        List<AsShoppingCart> existProductList = this.list(Wrappers.lambdaQuery(AsShoppingCart.class)
                .eq(AsShoppingCart::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .in(AsShoppingCart::getSkuCode, productSkuCodeList).eq(AsShoppingCart::getIsDelete, 0));
        Map<String, AsShoppingCart> existProductMap = null;
        if (CollUtil.isNotEmpty(existProductList)) {
            existProductMap = existProductList.stream().collect(Collectors.toMap(AsShoppingCart::getSkuCode, value -> value, (v1, v2) -> v1));
        }

        //软件商品不为空时，需要处理赠品逻辑
        if (CollUtil.isNotEmpty(productTypeMap.get(GoodsTypeEnum.SOFTWARE.getCode()))) {
            //商品编码列表
            List<String> skuCodeList = productTypeMap.get(GoodsTypeEnum.SOFTWARE.getCode())
                    .stream().map(OrderProductRequestDto::getSkuCode).collect(Collectors.toList());

            //根据sku获取资源包信息
            List<ResourcePackDto> resourcePackDtoList = resourceConfigService.queryResourceInfo(skuCodeList);
            if (CollUtil.isEmpty(resourcePackDtoList)) {
                throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
            }

            //资源包按skuCode进行分组
            Map<String, ResourcePackDto> resourcePackDtoMap = resourcePackDtoList.stream()
                    .collect(Collectors.toMap(ResourcePackDto::getSkuCode, value -> value, (v1, v2) -> v1));
            for (OrderProductRequestDto item : productTypeMap.get(GoodsTypeEnum.SOFTWARE.getCode())) {
                //校验单个资源包信息是否存在
                if (Objects.isNull(resourcePackDtoMap.get(item.getSkuCode()))) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, item.getSkuName() + "商品不存在");
                }

                int buyCount = item.getCount();
                //购物车已存在当前商品信息，数量和钱进行累加
                boolean existProduct = Objects.nonNull(existProductMap) && Objects.nonNull(existProductMap.get(item.getSkuCode()));

                if (existProduct) {
                    buyCount = buyCount + existProductMap.get(item.getSkuCode()).getCount();
                }

                //校验商品购买数量
                verifyProductCount(GoodsTypeEnum.SOFTWARE.getCode(), buyCount, item.getSkuName());

                //支付价格
                BigDecimal price = resourcePackDtoMap.get(item.getSkuCode()).getAnnualPrice()
                        .multiply(new BigDecimal(Convert.toStr(buyCount)));

                AsShoppingCart shoppingCartProduct = new AsShoppingCart();
                if (existProduct) {
                    //购物车已有当前商品，更新数量和价格
                    shoppingCartProduct.setId(existProductMap.get(item.getSkuCode()).getId())
                            .setSkuName(resourcePackDtoMap.get(item.getSkuCode()).getResourceName())
                            .setUnitPrice(resourcePackDtoMap.get(item.getSkuCode()).getAnnualPrice())
                            .setPrice(price)
                            .setCount(buyCount);
                    this.updateById(shoppingCartProduct);
                } else {
                    //购物车没有当前商品，添加一条记录
                    shoppingCartProduct.setCompanyId(LoginUserThreadLocal.getCompanyId())
                            .setUserId(LoginUserThreadLocal.getCurrentUserId())
                            //软件
                            .setGoodsType(GoodsTypeEnum.SOFTWARE.getCode())
                            .setSkuCode(item.getSkuCode())
                            .setSkuName(resourcePackDtoMap.get(item.getSkuCode()).getResourceName())
                            .setUnitPrice(resourcePackDtoMap.get(item.getSkuCode()).getAnnualPrice())
                            .setPrice(price)
                            .setCount(buyCount)
                            .setGoodsValue(buyCount * resourcePackDtoMap.get(item.getSkuCode()).getCapacity());
                    this.save(shoppingCartProduct);
                }

                //判断当前购买数量是否命中优惠
                int bonus = discountConfigService.queryBonus(resourcePackDtoMap.get(item.getSkuCode()).getDiscountInfo(), buyCount);

                //处理并保存赠品信息
                if (bonus > 0) {
                    //先清空当前商品对应的已存在的赠品信息
                    shoppingCartBonusService.update(Wrappers.lambdaUpdate(AsShoppingCartBonus.class)
                            .in(AsShoppingCartBonus::getMainProductId, shoppingCartProduct.getId()).set(AsShoppingCartBonus::getIsDelete, 1));

                    BigDecimal discountMoney = new BigDecimal(Convert.toStr(bonus)).multiply(resourcePackDtoMap.get(item.getSkuCode()).getUnitPrice());
                    //添加赠品信息
                    AsShoppingCartBonus bonusProduct = new AsShoppingCartBonus()
                            .setMainProductId(shoppingCartProduct.getId())
                            .setSkuCode(item.getSkuCode())
                            .setSkuName(shoppingCartProduct.getSkuName())
                            .setPrice(discountMoney)
                            .setGoodsValue(bonus);
                    shoppingCartBonusService.save(bonusProduct);
                }
            }
        }

        //处理硬件
        if (CollUtil.isNotEmpty(productTypeMap.get(GoodsTypeEnum.HARDWARE.getCode()))) {
            //硬件商品列表
            List<String> skuCodeList = productTypeMap.get(GoodsTypeEnum.HARDWARE.getCode()).stream()
                    .map(OrderProductRequestDto::getSkuCode).collect(Collectors.toList());
            //硬件商品信息
            List<MallGoodsDto> mallGoodsDtoList = mallGoodsService.queryBySkuCode(skuCodeList);
            if (CollUtil.isEmpty(mallGoodsDtoList)) {
                throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
            }
            Map<String, MallGoodsDto> hardwareProductMap = mallGoodsDtoList.stream()
                    .collect(Collectors.toMap(MallGoodsDto::getSkuCode, value -> value, (v1, v2) -> v1));

            for (OrderProductRequestDto item : productTypeMap.get(GoodsTypeEnum.HARDWARE.getCode())) {
                MallGoodsDto hardwareProduct = hardwareProductMap.get(item.getSkuCode());
                if (Objects.isNull(hardwareProduct)) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, item.getSkuName() + "商品不存在");
                }

                //购物车已存在当前商品信息，数量和钱进行累加
                boolean existProduct = Objects.nonNull(existProductMap) && Objects.nonNull(existProductMap.get(item.getSkuCode()));
                int buyCount = item.getCount();
                if (existProduct) {
                    buyCount = buyCount + existProductMap.get(item.getSkuCode()).getCount();
                }

                //校验商品购买数量
                verifyProductCount(GoodsTypeEnum.HARDWARE.getCode(), buyCount, item.getSkuName());

                //购物车硬件商品
                AsShoppingCart shoppingCartProduct = new AsShoppingCart();
                if (existProduct) {
                    shoppingCartProduct.setId(existProductMap.get(item.getSkuCode()).getId())
                            .setSkuName(hardwareProduct.getGoodsName())
                            .setUnitPrice(hardwareProduct.getGoodsUnitPrice())
                            .setPrice(hardwareProduct.getGoodsUnitPrice().multiply(BigDecimal.valueOf(buyCount)))
                            .setCount(buyCount);
                    this.updateById(shoppingCartProduct);
                } else {
                    shoppingCartProduct.setCompanyId(LoginUserThreadLocal.getCompanyId())
                            .setUserId(LoginUserThreadLocal.getCurrentUserId())
                            //硬件
                            .setGoodsType(GoodsTypeEnum.HARDWARE.getCode())
                            .setSkuCode(item.getSkuCode())
                            .setSkuName(hardwareProduct.getGoodsName())
                            .setUnitPrice(hardwareProduct.getGoodsUnitPrice())
                            .setPrice(hardwareProduct.getGoodsUnitPrice().multiply(BigDecimal.valueOf(buyCount)))
                            .setCount(buyCount);
                    this.save(shoppingCartProduct);
                }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean editProduct(OrderProductRequestDto orderProductRequestDto) {
        //校验商品购买数量
        verifyProductCount(orderProductRequestDto.getGoodsType(), orderProductRequestDto.getCount(), orderProductRequestDto.getSkuName());

        AsShoppingCart shoppingCart = this.getOne(Wrappers.lambdaQuery(AsShoppingCart.class)
                .eq(AsShoppingCart::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .eq(AsShoppingCart::getSkuCode, orderProductRequestDto.getSkuCode()).eq(AsShoppingCart::getIsDelete, 0));
        if (Objects.isNull(shoppingCart)) {
            throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
        }

        //校验是否当前用户操作
        if (!LoginUserThreadLocal.getCurrentUserId().equals(shoppingCart.getUserId())) {
            throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
        }

        //商品编码
        List<String> skuCodeList = Collections.singletonList(orderProductRequestDto.getSkuCode());

        //如果商品是硬件
        if (GoodsTypeEnum.HARDWARE.getCode().equals(orderProductRequestDto.getGoodsType())) {
            //硬件商品信息
            List<MallGoodsDto> mallGoodsDtoList = mallGoodsService.queryBySkuCode(skuCodeList);
            if (CollUtil.isEmpty(mallGoodsDtoList)) {
                throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
            }
            BigDecimal unitPrice = mallGoodsDtoList.get(0).getGoodsUnitPrice();
            BigDecimal price = unitPrice.multiply(BigDecimal.valueOf(orderProductRequestDto.getCount().longValue()));

            this.update(Wrappers.lambdaUpdate(AsShoppingCart.class)
                    .eq(AsShoppingCart::getId, shoppingCart.getId())
                    .set(AsShoppingCart::getPrice, price)
                    .set(AsShoppingCart::getUnitPrice, unitPrice)
                    .set(AsShoppingCart::getCount, orderProductRequestDto.getCount()));
        } else if (GoodsTypeEnum.SOFTWARE.getCode().equals(orderProductRequestDto.getGoodsType())){
            //处理软件商品
            //根据sku获取资源包信息
            List<ResourcePackDto> resourcePackDtoList = resourceConfigService.queryResourceInfo(Collections.singletonList(shoppingCart.getSkuCode()));
            if (CollUtil.isEmpty(resourcePackDtoList)) {
                throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
            }

            //支付价格
            BigDecimal price = resourcePackDtoList.get(0).getAnnualPrice()
                    .multiply(new BigDecimal(Convert.toStr(orderProductRequestDto.getCount())));

            //购物车软件商品信息更新
            AsShoppingCart shoppingCartProduct = new AsShoppingCart().setId(shoppingCart.getId())
                    .setSkuName(resourcePackDtoList.get(0).getResourceName())
                    .setUnitPrice(resourcePackDtoList.get(0).getAnnualPrice())
                    .setPrice(price)
                    .setGoodsValue(orderProductRequestDto.getCount() * resourcePackDtoList.get(0).getCapacity())
                    .setCount(orderProductRequestDto.getCount());
            this.updateById(shoppingCartProduct);

            //判断当前购买数量是否命中优惠
            int bonus = discountConfigService.queryBonus(resourcePackDtoList.get(0).getDiscountInfo(), orderProductRequestDto.getCount());

            //删除原有赠品
            shoppingCartBonusService.update(Wrappers.lambdaUpdate(AsShoppingCartBonus.class)
                    .eq(AsShoppingCartBonus::getMainProductId, shoppingCart.getId())
                    .eq(AsShoppingCartBonus::getIsDelete, 0)
                    .set(AsShoppingCartBonus::getIsDelete, 1));

            if (bonus > 0) {
                BigDecimal discountMoney = new BigDecimal(Convert.toStr(bonus)).multiply(resourcePackDtoList.get(0).getUnitPrice());
                AsShoppingCartBonus bonusProduct = new AsShoppingCartBonus()
                        .setMainProductId(shoppingCart.getId())
                        .setSkuCode(resourcePackDtoList.get(0).getSkuCode())
                        .setSkuName(resourcePackDtoList.get(0).getResourceName())
                        .setPrice(discountMoney)
                        .setGoodsValue(bonus);
                //添加新的赠品
                shoppingCartBonusService.save(bonusProduct);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeProduct(OrderProductRequestDto orderProductRequestDto) {
        AsShoppingCart shoppingCart = this.getOne(Wrappers.lambdaQuery(AsShoppingCart.class)
                .eq(AsShoppingCart::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .eq(AsShoppingCart::getSkuCode, orderProductRequestDto.getSkuCode()).eq(AsShoppingCart::getIsDelete, 0));
        if (Objects.isNull(shoppingCart)) {
            throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
        }

        //删除购物车商品
        this.update(Wrappers.lambdaUpdate(AsShoppingCart.class).set(AsShoppingCart::getIsDelete, 1)
                .eq(AsShoppingCart::getSkuCode, orderProductRequestDto.getSkuCode()));
        //软件商品需要处理赠品
        if (GoodsTypeEnum.SOFTWARE.getCode().equals(orderProductRequestDto.getGoodsType())) {
            shoppingCartBonusService.update(Wrappers.lambdaUpdate(AsShoppingCartBonus.class)
                    .set(AsShoppingCartBonus::getIsDelete, 1)
                    .eq(AsShoppingCartBonus::getMainProductId, shoppingCart.getId())
                    .eq(AsShoppingCartBonus::getIsDelete, 0));
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean clearShoppingCart() {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        List<AsShoppingCart> shoppingCartList = this.list(Wrappers.lambdaQuery(AsShoppingCart.class).eq(AsShoppingCart::getUserId, userId)
                .eq(AsShoppingCart::getIsDelete, 0));
        if (CollUtil.isEmpty(shoppingCartList)) {
            return Boolean.TRUE;
        }

        //清空购物车商品
        this.update(Wrappers.lambdaUpdate(AsShoppingCart.class)
                .set(AsShoppingCart::getIsDelete, 1)
                .eq(AsShoppingCart::getUserId, userId)
                .eq(AsShoppingCart::getIsDelete, 0));

        //清空购物车商品赠品
        shoppingCartBonusService.update(Wrappers.lambdaUpdate(AsShoppingCartBonus.class)
                .set(AsShoppingCartBonus::getIsDelete, 1)
                .in(AsShoppingCartBonus::getMainProductId, shoppingCartList.stream().map(AsShoppingCart::getId).collect(Collectors.toList())));
        return Boolean.TRUE;
    }

    @Override
    public Integer productCount() {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        //查询购物车
        List<ShoppingCartProductDto> shoppingCartProduct = this.getBaseMapper().selectShoppingCart(userId);
        if (CollUtil.isEmpty(shoppingCartProduct)) {
            return 0;
        }

        //商品编码列表
        List<String> skuCodeList = shoppingCartProduct.stream().map(ShoppingCartProductDto::getSkuCode).collect(Collectors.toList());

        //查询硬件商品信息
        List<MallGoodsDto> mallGoodsList = mallGoodsService.queryBySkuCode(skuCodeList);
        List<String> hardwareSkuCodeList = null;
        if (CollUtil.isNotEmpty(mallGoodsList)) {
            hardwareSkuCodeList = mallGoodsList.stream().map(MallGoodsDto::getSkuCode).collect(Collectors.toList());
        }

        //查询软件商品信息
        List<ResourcePackDto> resourcePackDtoList = resourceConfigService.queryResourceInfo(skuCodeList);
        List<String> softwareSkuCodeList = null;
        if (CollUtil.isNotEmpty(resourcePackDtoList)) {
            softwareSkuCodeList = resourcePackDtoList.stream().map(ResourcePackDto::getSkuCode).collect(Collectors.toList());
        }

        //购物车数量，需要过滤已下架商品
        int result = 0;
        for (ShoppingCartProductDto item : shoppingCartProduct) {
            if (GoodsTypeEnum.HARDWARE.getCode().equals(item.getGoodsType())) {
                if (CollUtil.isNotEmpty(hardwareSkuCodeList) && hardwareSkuCodeList.contains(item.getSkuCode())) {
                    result = result + item.getCount();
                }
            } else {
                if (CollUtil.isNotEmpty(softwareSkuCodeList) && softwareSkuCodeList.contains(item.getSkuCode())) {
                    result = result + item.getCount();
                }
            }
        }
        return result;
    }

    @Override
    public ShoppingCartDto queryProductById(List<Long> productIdList) {
        if (CollUtil.isEmpty(productIdList)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID);
        }

        ShoppingCartDto result = new ShoppingCartDto();

        //查询当前用户购物车商品信息
        List<ShoppingCartProductDto> productDtoList = queryProduct();
        if (CollUtil.isEmpty(productDtoList)) {
            return result;
        }

        int productCount = 0;
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal discountMoney = BigDecimal.ZERO;
        List<ShoppingCartProductDto> data = new ArrayList<>();
        for (ShoppingCartProductDto item : productDtoList) {
            if (!productIdList.contains(item.getId())) {
                continue;
            }

            productCount = productCount + item.getCount();
            totalMoney = totalMoney.add(item.getPrice());
            //处理赠品费用
            if (CollUtil.isNotEmpty(item.getProductBonus())) {
                discountMoney = discountMoney.add(item.getProductBonus().stream()
                        .map(ShoppingCartBonusDto::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            data.add(item);
        }

        result.setProductList(data);
        result.setCount(productCount);
        result.setTotalMoney(totalMoney.add(discountMoney));
        result.setPayMoney(totalMoney);
        result.setBonus(discountMoney);
        return result;
    }

    @Override
    public List<ShoppingCartProductDto> queryProduct() {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        //查询购物车
        List<ShoppingCartProductDto> result = this.getBaseMapper().selectShoppingCart(userId);
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        //主商品id列表
        List<Long> mainIdList = result.stream().map(ShoppingCartProductDto::getId).collect(Collectors.toList());
        //商品编码列表
        List<String> skuCodeList = result.stream().map(ShoppingCartProductDto::getSkuCode).collect(Collectors.toList());

        //查询硬件商品信息
        List<MallGoodsDto> mallGoodsList = mallGoodsService.queryBySkuCode(skuCodeList);
        Map<String, MallGoodsDto> mallGoodsDtoMap = null;
        if (CollUtil.isNotEmpty(mallGoodsList)) {
            mallGoodsDtoMap = mallGoodsList.stream().collect(Collectors.toMap(MallGoodsDto::getSkuCode, value -> value, (v1, v2) -> v2));
        }

        //查询软件商品信息
        List<ResourcePackDto> resourcePackDtoList = resourceConfigService.queryResourceInfo(skuCodeList);
        Map<String, ResourcePackDto> resourceMap = null;
        if (CollUtil.isNotEmpty(resourcePackDtoList)) {
            resourceMap = resourcePackDtoList.stream().collect(Collectors.toMap(ResourcePackDto::getSkuCode, value -> value, (v1, v2) -> v1));
        }

        //需要删除赠品的主商品id列表
        List<Long> removeBonusMainIdList = new ArrayList<>();
        //新赠品信息
        List<AsShoppingCartBonus> newShoppingCartBonusList = new ArrayList<>();
        //软件商品处理下赠品，删除之前的赠品，然后新增赠品信息
        for (ShoppingCartProductDto item : result) {
            //软件商品处理赠品信息
            if (GoodsTypeEnum.SOFTWARE.getCode().equals(item.getGoodsType()) && Objects.nonNull(resourceMap)) {
                //主商品对应的赠品需要进行删除
                removeBonusMainIdList.add(item.getId());

                //资源包有可能被下架了
                if (Objects.isNull(resourceMap.get(item.getSkuCode()))) {
                    continue;
                }

                //判断当前购买数量是否命中优惠
                int bonus = discountConfigService.queryBonus(resourceMap.get(item.getSkuCode()).getDiscountInfo(), item.getCount());

                //存在优惠的时候，新增优惠信息
                if (bonus > 0) {
                    BigDecimal discountMoney = new BigDecimal(Convert.toStr(bonus)).multiply(resourceMap.get(item.getSkuCode()).getUnitPrice());
                    AsShoppingCartBonus bonusProduct = new AsShoppingCartBonus()
                            .setMainProductId(item.getId())
                            .setSkuCode(item.getSkuCode())
                            .setSkuName(item.getSkuName())
                            .setPrice(discountMoney)
                            .setGoodsValue(bonus);
                    //添加新的赠品
                    newShoppingCartBonusList.add(bonusProduct);
                }
            }
        }

        //删除原有赠品
        if (CollUtil.isNotEmpty(removeBonusMainIdList)) {
            shoppingCartBonusService.update(Wrappers.lambdaUpdate(AsShoppingCartBonus.class)
                    .in(AsShoppingCartBonus::getMainProductId, removeBonusMainIdList)
                    .eq(AsShoppingCartBonus::getIsDelete, 0)
                    .set(AsShoppingCartBonus::getIsDelete, 1));
        }

        //保存赠品信息
        if (CollUtil.isNotEmpty(newShoppingCartBonusList)) {
            shoppingCartBonusService.saveBatch(newShoppingCartBonusList);
        }

        //查询赠品信息
        List<ShoppingCartBonusDto> shoppingCartBonusDtoList = shoppingCartBonusService.queryByMainId(mainIdList);
        Map<Long, List<ShoppingCartBonusDto>> shoppingCartBonusMap = null;
        if (CollUtil.isNotEmpty(shoppingCartBonusDtoList)) {
            //赠品按主商品id进行分组
            shoppingCartBonusMap = shoppingCartBonusDtoList.stream().collect(Collectors.groupingBy(ShoppingCartBonusDto::getMainProductId));
        }

        //返回数据结果
        List<ShoppingCartProductDto> dataResult = new ArrayList<>();
        for (ShoppingCartProductDto item : result) {
            if (GoodsTypeEnum.SOFTWARE.getCode().equals(item.getGoodsType())) {
                //过滤不存在或已下架的软件商品
                if (Objects.isNull(resourceMap) || Objects.isNull(resourceMap.get(item.getSkuCode()))) {
                    continue;
                }
                //设置软件商品容量信息
                item.setCapacity(resourceMap.get(item.getSkuCode()).getCapacity());

                //设置赠品信息
                if (Objects.nonNull(shoppingCartBonusMap) && CollUtil.isNotEmpty(shoppingCartBonusMap.get(item.getId()))) {
                    item.setProductBonus(shoppingCartBonusMap.get(item.getId()));
                }
            } else {
                //过滤不存在或已下架的硬件商品
                if (Objects.isNull(mallGoodsDtoMap) || Objects.isNull(mallGoodsDtoMap.get(item.getSkuCode()))) {
                    continue;
                }
                //设置硬件商品图片信息
                item.setUrl(mallGoodsDtoMap.get(item.getSkuCode()).getGoodsUrl().stream()
                        .map(mallGoods -> mallGoods.getString("url")).collect(Collectors.toList()));
                item.setSubTitle(mallGoodsDtoMap.get(item.getSkuCode()).getSubtitle());
            }
            dataResult.add(item);
        }
        return dataResult;
    }

    @Override
    public ShoppingCartDto settleProduct(OrderRequestDto orderRequestDto) {
        //生成订单信息
        ShoppingCartDto result = immediatelySettleProduct(orderRequestDto);

        //商品编码列表
        List<String> skuCodeList = orderRequestDto.getProductList().stream().map(OrderProductRequestDto::getSkuCode).collect(Collectors.toList());
        List<AsShoppingCart> shoppingCartList = this.list(Wrappers.lambdaQuery(AsShoppingCart.class)
                .in(AsShoppingCart::getSkuCode, skuCodeList).eq(AsShoppingCart::getIsDelete, 0));

        //购物车有该商品信息时，需要删除购物车信息以及购物车赠品信息
        if (CollUtil.isNotEmpty(shoppingCartList)) {
            this.update(Wrappers.lambdaUpdate(AsShoppingCart.class).set(AsShoppingCart::getIsDelete, 1).in(AsShoppingCart::getSkuCode, skuCodeList));
            AsShoppingCartBonus bonusModifyParam = new AsShoppingCartBonus();
            bonusModifyParam.setIsDelete(1);
            List<Long> mainProductIdList = shoppingCartList.stream().map(AsShoppingCart::getId).collect(Collectors.toList());
            shoppingCartBonusService.getBaseMapper().update(bonusModifyParam, Wrappers.lambdaUpdate(AsShoppingCartBonus.class)
                    .in(AsShoppingCartBonus::getMainProductId, mainProductIdList));
        }
        return result;
    }

    @Override
    public ShoppingCartDto immediatelySettleProduct(OrderRequestDto orderRequestDto) {
        //商品分组，软硬件分组
        Map<Integer, List<OrderProductRequestDto>> orderProductDtoMap = orderRequestDto.getProductList().stream()
                .collect(Collectors.groupingBy(OrderProductRequestDto::getGoodsType));

        //校验软件商品是否存在
        Map<String, ResourcePackDto> resourcePackMap = new HashMap<>();
        Map<String, MallGoodsDto> hardwareProductMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orderProductDtoMap.get(GoodsTypeEnum.SOFTWARE.getCode()))) {
            List<OrderProductRequestDto> softwareProductList = orderProductDtoMap.get(GoodsTypeEnum.SOFTWARE.getCode());
            List<String> resourceSkuCodeList = softwareProductList.stream().map(OrderProductRequestDto::getSkuCode).collect(Collectors.toList());
            //查询资源包信息
            List<ResourcePackDto> resourcePackDtoList = resourceConfigService.queryResourceInfo(resourceSkuCodeList);
            if (CollUtil.isEmpty(resourcePackDtoList)) {
                throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
            }
            resourcePackMap = resourcePackDtoList.stream().collect(Collectors.toMap(ResourcePackDto::getSkuCode, value -> value, (v1, v2) -> v1));
        }

        //校验硬件商品是否存在
        if (CollUtil.isNotEmpty(orderProductDtoMap.get(GoodsTypeEnum.HARDWARE.getCode()))) {
            List<OrderProductRequestDto> hardwareProductList = orderProductDtoMap.get(GoodsTypeEnum.HARDWARE.getCode());
            List<String> hardwareSkuList = hardwareProductList.stream().map(OrderProductRequestDto::getSkuCode).collect(Collectors.toList());
            //查询硬件商品信息
            List<MallGoodsDto> mallGoodsList = mallGoodsService.queryBySkuCode(hardwareSkuList);
            if (CollUtil.isEmpty(mallGoodsList)) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "硬件商品信息不存在");
            }
            hardwareProductMap = mallGoodsList.stream().collect(Collectors.toMap(MallGoodsDto::getSkuCode, value -> value, (v1, v2) -> v1));
        }

        // 当前公司
        AsCompany company = companyService.getById(LoginUserThreadLocal.getCompanyId());
        // 当前员工信息
        AsCusEmployee employee = cusEmployeeService.getById(LoginUserThreadLocal.getCurrentUserId());

        //是否非固定规格购买
        boolean isNonFixedPurchase = isNonFixedPurchase(orderRequestDto);
        if (isNonFixedPurchase) {
            //非固定规格购买
            return nonFixedPurchase(orderRequestDto, company, employee, orderProductDtoMap, resourcePackMap, hardwareProductMap);
        } else {
            //固定规格购买
            return fixedPurchase(orderRequestDto, company, employee, resourcePackMap);
        }
    }

    /**
     * 非固定规格下单
     * @param orderRequestDto
     * @param company
     * @param employee
     * @param orderProductDtoMap
     * @param resourcePackMap
     * @param hardwareProductMap
     * @return
     */
    private ShoppingCartDto nonFixedPurchase(OrderRequestDto orderRequestDto, AsCompany company, AsCusEmployee employee,
                                             Map<Integer, List<OrderProductRequestDto>> orderProductDtoMap,
                                             Map<String, ResourcePackDto> resourcePackMap, Map<String, MallGoodsDto> hardwareProductMap) {
        //内购商品sku，非固定规格
        String insideSku = generalService.insideSku();
        if (StrUtil.isEmpty(insideSku)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "内购Sku不存在");
        }

        // 创建新订单
        Long orderId = IdUtils.getId();
        AsSaleOrder saleOrder = new AsSaleOrder()
                .setId(orderId)
                .setOrderNo(StringUtils.getOrderNo("ON"))
                //订单来源，2-内购
                .setSource(2)
                .setStatus(ManageConstant.SALE_ORDER_STATUS_WAIT)
                .setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setCompanyName(company.getName())
                //支付方式，1-支付宝
                .setPayType(1)
                .setCustomerRemark(orderRequestDto.getRemark())
                .setReceivePaymentNo("dingtalk_default")
                .setCreateTime(LocalDateTime.now());
        //这里要兼容钉钉没有绑定手机号的情况
        if (Objects.nonNull(employee)) {
            saleOrder.setMobile(employee.getMobile());
        }

        //总金额
        BigDecimal totalMoney = BigDecimal.ZERO;
        //支付金额
        BigDecimal payMoney = BigDecimal.ZERO;
        //优惠金额
        BigDecimal discountMoney = BigDecimal.ZERO;
        //订单摘要
        StringBuilder orderSummary = new StringBuilder();
        List<AsSaleOrderItem> saleOrderItemList = new ArrayList<>();
        //软件商品信息
        if (CollUtil.isNotEmpty(orderProductDtoMap.get(GoodsTypeEnum.SOFTWARE.getCode()))) {
            //获取软件商品优惠信息
            for (OrderProductRequestDto item : orderProductDtoMap.get(GoodsTypeEnum.SOFTWARE.getCode())) {
                ResourcePackDto resourcePackDto = resourcePackMap.get(item.getSkuCode());
                if (Objects.isNull(resourcePackDto)) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, item.getSkuName() + "商品信息不存在");
                }

                //校验商品购买数量
                verifyProductCount(GoodsTypeEnum.SOFTWARE.getCode(), item.getCount(), item.getSkuName());

                AsSaleOrderItem saleOrderItem = new AsSaleOrderItem();
                saleOrderItem.setName(resourcePackDto.getResourceName());
                orderSummary.append(resourcePackDto.getResourceName()).append(",");
                saleOrderItem.setSku(item.getSkuCode());
                saleOrderItem.setQuantity(item.getCount() * 12);
                saleOrderItem.setSaleOrderId(orderId);
                saleOrderItem.setModel(resourcePackMap.get(item.getSkuCode()).getCapacity().toString());
                saleOrderItem.setOriginalMoney(resourcePackMap.get(item.getSkuCode()).getAnnualPrice());
                saleOrderItem.setSaleMoney(resourcePackMap.get(item.getSkuCode()).getAnnualPrice());
                saleOrderItem.setType(DictConstant.PRODUCT_TYPE_RESOURCE_PACK);
                saleOrderItem.setProduct(JsonUtil.toJsonObject(resourcePackMap.get(item.getSkuCode())));
                BigDecimal productTotalMoney = resourcePackMap.get(item.getSkuCode()).getAnnualPrice().multiply(BigDecimal.valueOf(item.getCount().longValue()));
                //判断当前购买数量是否命中优惠
                int bonus = discountConfigService.queryBonus(resourcePackMap.get(item.getSkuCode()).getDiscountInfo(), item.getCount());
                //商品应付金额累加
                payMoney = payMoney.add(productTotalMoney);
                if (bonus > 0) {
                    saleOrderItem.setDiscountInfo(String.valueOf(bonus));
                    //商品优惠金额
                    BigDecimal productDiscountMoney = resourcePackMap.get(item.getSkuCode()).getUnitPrice()
                            .multiply(BigDecimal.valueOf(bonus));
                    //商品总金额 = 商品应付金额 + 商品优惠金额
                    productTotalMoney = productTotalMoney.add(productDiscountMoney);
                    discountMoney = discountMoney.add(productDiscountMoney);
                }
                //商品总金额
                totalMoney = totalMoney.add(productTotalMoney);
                saleOrderItem.setTotalMoney(productTotalMoney);
                saleOrderItemList.add(saleOrderItem);
            }
        }
        //硬件商品信息
        if (CollUtil.isNotEmpty(orderProductDtoMap.get(GoodsTypeEnum.HARDWARE.getCode()))) {
            for (OrderProductRequestDto item : orderProductDtoMap.get(GoodsTypeEnum.HARDWARE.getCode())) {
                MallGoodsDto hardwareProduct = hardwareProductMap.get(item.getSkuCode());
                if (Objects.isNull(hardwareProduct)) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, item.getSkuName() + "商品信息不存在");
                }

                //校验商品购买数量
                verifyProductCount(GoodsTypeEnum.HARDWARE.getCode(), item.getCount(), item.getSkuName());

                AsSaleOrderItem saleOrderItem = new AsSaleOrderItem();
                saleOrderItem.setName(hardwareProduct.getGoodsName());
                orderSummary.append(hardwareProduct.getGoodsName()).append(",");
                saleOrderItem.setSku(item.getSkuCode());
                saleOrderItem.setQuantity(item.getCount());
                saleOrderItem.setSaleOrderId(orderId);
                saleOrderItem.setModel(hardwareProduct.getGoodsModel());
                saleOrderItem.setOriginalMoney(hardwareProduct.getGoodsUnitPrice());
                BigDecimal productMoney = hardwareProduct.getGoodsUnitPrice()
                        .multiply(BigDecimal.valueOf(item.getCount().longValue()));
                saleOrderItem.setSaleMoney(productMoney);
                saleOrderItem.setTotalMoney(productMoney);
                //硬件商品类型
                saleOrderItem.setType(6);
                saleOrderItem.setProduct(JsonUtil.toJsonObject(hardwareProduct));
                payMoney = payMoney.add(productMoney);
                totalMoney = totalMoney.add(productMoney);

                saleOrderItemList.add(saleOrderItem);
            }
        }
        saleOrder.setTotalMoney(totalMoney);
        saleOrder.setPayMoney(payMoney);
        saleOrder.setDiscountMoney(discountMoney);
        saleOrder.setSummary(orderSummary.substring(0, orderSummary.length() - 1));
        saleOrderService.save(saleOrder);
        saleOrderItemService.saveBatch(saleOrderItemList);

        ShoppingCartDto result = new ShoppingCartDto();
        result.setSkuCode(insideSku);
        result.setOrderNo(saleOrder.getOrderNo());
        result.setTotalMoney(totalMoney);
        result.setPayMoney(payMoney);
        return result;
    }

    /**
     * 固定规格下单
     * @param orderRequestDto
     * @param company
     * @param employee
     * @param resourcePackMap
     * @return
     */
    private ShoppingCartDto fixedPurchase(OrderRequestDto orderRequestDto, AsCompany company, AsCusEmployee employee,
                                             Map<String, ResourcePackDto> resourcePackMap) {
        OrderProductRequestDto productRequestDto = orderRequestDto.getProductList().get(0);
        ResourcePackDto resourcePackDto = resourcePackMap.get(productRequestDto.getSkuCode());
        if (Objects.isNull(resourcePackDto)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, productRequestDto.getSkuName() + "商品信息不存在");
        }

        //校验商品购买数量
        verifyProductCount(GoodsTypeEnum.SOFTWARE.getCode(), productRequestDto.getCount(), productRequestDto.getSkuName());

        //固定规格sku
        String fixedSku = generalService.getDingSkuByGoodsSku(productRequestDto.getSkuCode());
        if (StrUtil.isBlank(fixedSku)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "固定规格编码不存在");
        }

        // 创建新订单
        Long orderId = IdUtils.getId();
        AsSaleOrder saleOrder = new AsSaleOrder()
                .setId(orderId)
                .setOrderNo(StringUtils.getOrderNo("ON"))
                .setSummary(resourcePackDto.getResourceName())
                //订单来源，2-内购
                .setSource(2)
                .setStatus(ManageConstant.SALE_ORDER_STATUS_WAIT)
                .setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setCompanyName(company.getName())
                //支付方式，1-支付宝
                .setPayType(1)
                .setCustomerRemark(orderRequestDto.getRemark())
                .setReceivePaymentNo("dingtalk_default")
                .setCreateTime(LocalDateTime.now());
        //这里要兼容钉钉没有绑定手机号的情况
        if (Objects.nonNull(employee)) {
            saleOrder.setMobile(employee.getMobile());
        }

        AsSaleOrderItem saleOrderItem = new AsSaleOrderItem();
        saleOrderItem.setName(resourcePackDto.getResourceName());
        saleOrderItem.setSku(resourcePackDto.getSkuCode());
        saleOrderItem.setQuantity(productRequestDto.getCount() * 12);
        saleOrderItem.setSaleOrderId(orderId);
        saleOrderItem.setModel(resourcePackDto.getCapacity().toString());
        saleOrderItem.setOriginalMoney(resourcePackDto.getAnnualPrice());
        saleOrderItem.setSaleMoney(resourcePackDto.getAnnualPrice());
        saleOrderItem.setType(DictConstant.PRODUCT_TYPE_RESOURCE_PACK);
        saleOrderItem.setProduct(JsonUtil.toJsonObject(resourcePackDto));
        //商品优惠金额
        BigDecimal productDiscountMoney = BigDecimal.ZERO;
        //商品总金额
        BigDecimal productPayMoney = resourcePackDto.getAnnualPrice().multiply(BigDecimal.valueOf(productRequestDto.getCount().longValue()));
        //判断当前购买数量是否命中优惠
        int bonus = discountConfigService.queryBonus(resourcePackDto.getDiscountInfo(), productRequestDto.getCount());
        if (bonus > 0) {
            //资源包优惠时长
            saleOrderItem.setDiscountInfo(String.valueOf(bonus));
            //商品优惠金额
            productDiscountMoney = resourcePackDto.getUnitPrice()
                    .multiply(BigDecimal.valueOf(bonus));
        }
        BigDecimal productTotalMoney = productPayMoney.add(productDiscountMoney);
        saleOrderItem.setTotalMoney(productTotalMoney);

        saleOrder.setDiscountMoney(productDiscountMoney);
        saleOrder.setPayMoney(productPayMoney);
        saleOrder.setTotalMoney(productTotalMoney);

        saleOrderService.save(saleOrder);
        saleOrderItemService.save(saleOrderItem);

        ShoppingCartDto result = new ShoppingCartDto();
        result.setSkuCode(fixedSku);
        result.setOrderNo(saleOrder.getOrderNo());
        result.setTotalMoney(saleOrder.getTotalMoney());
        result.setPayMoney(saleOrder.getPayMoney());
        return result;
    }

    /**
     * 校验是否非固定规格购买
     * 固定规格：商品为软件商品、购买一件，并且是在后台商品配置里有钉钉固定规格sku
     * @return
     */
    private Boolean isNonFixedPurchase(OrderRequestDto orderRequestDto) {
        if (orderRequestDto.getProductList().size() > 1) {
            return Boolean.TRUE;
        }

        //固定规格：仅购买一件商品
        if (orderRequestDto.getProductList().get(0).getCount() > 1) {
            return Boolean.TRUE;
        }

        if (GoodsTypeEnum.HARDWARE.getCode().equals(orderRequestDto.getProductList().get(0).getGoodsType())) {
            return Boolean.TRUE;
        }

        //查询是否存在钉钉固定规格sku
        String dingSku = generalService.getDingSkuByGoodsSku(orderRequestDto.getProductList().get(0).getSkuCode());
        if (StrUtil.isNotBlank(dingSku)) {
            //存在是固定规格
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

    /**
     * 校验软件-硬件商品购买数量
     * @param goodsType
     * @param count
     * @param skuName
     */
    private void verifyProductCount(Integer goodsType, Integer count, String skuName) {
        if (GoodsTypeEnum.SOFTWARE.getCode().equals(goodsType)) {
            if (count > 10) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, skuName + " 商品数量最大仅允许购买10个");
            }
        } else if (GoodsTypeEnum.HARDWARE.getCode().equals(goodsType)) {
            if (count > 999) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, skuName + " 商品数量最大仅允许购买999个");
            }
        }
    }

    @Override
    public Boolean savePayUrl(String orderNo, String payUrl) {
        AsSaleOrder saleOrder = saleOrderService.getOne(Wrappers.lambdaQuery(AsSaleOrder.class).eq(AsSaleOrder::getOrderNo, orderNo));
        if (Objects.isNull(saleOrder)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "订单不存在");
        }

        //更新支付链接地址
        return saleOrderService.update(Wrappers.lambdaUpdate(AsSaleOrder.class).set(AsSaleOrder::getPayUrl, payUrl)
                .eq(AsSaleOrder::getId, saleOrder.getId()));
    }
}
