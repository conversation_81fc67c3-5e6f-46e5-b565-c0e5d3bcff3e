package com.niimbot.asset.dingtalk.org.controller;

import com.niimbot.asset.dingtalk.base.dto.EmployeeUpdateDto;
import com.niimbot.asset.dingtalk.base.dto.OrgUpdateDto;
import com.niimbot.asset.dingtalk.org.service.DingtalkContactsService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * created by chen.y on 2021/8/24 11:05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/dingtalk/syncOrgEmp")
public class SyncOrgEmpServiceController {

    private final DingtalkContactsService contactsService;

    @PutMapping
    public Boolean manualSyncData() {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        if (contactsService.processing(companyId)) {
            throw new BusinessException(SystemResultCode.DING_HAS_SYNCING);
        }
        contactsService.background(companyId);
        return true;
    }

    @PostMapping("/updateEmpNo")
    public Boolean updateEmpNo(@RequestBody EmployeeUpdateDto dto) {
        return contactsService.updateEmpNo(dto);
    }

    @PostMapping("/updateOrgCode")
    public Boolean updateOrgCode(@RequestBody OrgUpdateDto dto) {
        return contactsService.updateOrgCode(dto);
    }

}
