package com.niimbot.asset.dingtalk.org.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.response.OapiAppstoreInternalUnfinishedorderListResponse;
import com.niimbot.asset.dingtalk.base.config.CorpTokenManage;
import com.niimbot.asset.dingtalk.base.dto.DingResourcePayDto;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingOrder;
import com.niimbot.asset.dingtalk.base.model.GoodsTryout;
import com.niimbot.asset.dingtalk.base.model.constant.RedisConstant;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.org.mapper.DingOrderMapper;
import com.niimbot.asset.dingtalk.org.service.DingOrderService;
import com.niimbot.asset.dingtalk.org.service.GeneralService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.sale.model.AsCompanyResource;
import com.niimbot.asset.sale.model.AsResourceConfig;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.model.AsSaleOrderItem;
import com.niimbot.asset.sale.service.AsCompanyResourceService;
import com.niimbot.asset.sale.service.AsSaleOrderItemService;
import com.niimbot.asset.sale.service.AsSaleOrderService;
import com.niimbot.asset.sale.service.DiscountConfigService;
import com.niimbot.asset.sale.service.ResourceConfigService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.service.AsRecommendRecordService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.sale.ResourcePackDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 钉钉订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DingOrderServiceImpl extends ServiceImpl<DingOrderMapper, DingOrder> implements DingOrderService {

    private final RedisService redisService;
    private final CompanyService companyService;
    private final DingOpenApiService dingOpenApiService;
    private final CorpTokenManage corpTokenManage;
    private final DingCorpService dingCorpService;

    private final AsSaleOrderService saleOrderService;
    private final AsSaleOrderItemService saleOrderItemService;
    private final ResourceConfigService resourceConfigService;
    private final GeneralService generalService;
    private static final String ORDER_SUMMARY = "%s条容量 %s元/年";
    private final AsCompanyResourceService companyResourceService;
    private final DiscountConfigService discountConfigService;
    private final AsRecommendRecordService recommendRecordService;

    @Autowired
    private RedisDistributeLock redisDistributeLock;

    /**
     * 订单补偿
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    public void compensate() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        //控制集群并发
        redisDistributeLock.lock("dingOrderCompensate", 5, TimeUnit.MINUTES, (a) -> {
            // 处理应用内授权补偿
            Set<String> keys = redisService.scan(RedisConstant.GOODS_TRYOUT + ":*");
            keys.forEach(corpId -> {
                String tryoutType = Convert.toStr(redisService.get(RedisConstant.getGoodsTryout(corpId)));
                if (StrUtil.isNotEmpty(tryoutType)) {
                    dingCorpService.update(Wrappers.lambdaUpdate(DingCorp.class)
                            .set(DingCorp::getAuthChannelType, tryoutType)
                            .eq(DingCorp::getCorpId, corpId)
                            .ne(DingCorp::getAuthChannelType, GoodsTryout.TryoutType.ENTERPRISE_TRYOUT.getValue()));
                }
            });
            // 只处理内购订单信息
            String insideSku = generalService.insideSku();
            if (insideSku != null) {
                List<OapiAppstoreInternalUnfinishedorderListResponse.InAppGoodsOrderVO> unfinishedorder
                        = dingOpenApiService.unfinishedorder(StrUtil.EMPTY);
                unfinishedorder.forEach(order -> {
                    if (insideSku.equals(order.getGoodsCode())) {
                        DingOrder dingOrder = this.getOne(new LambdaQueryWrapper<DingOrder>()
                                .eq(DingOrder::getOrderId, Convert.toStr(order.getBizOrderId())));
                        if (dingOrder != null) {
                            if (com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_2.equals(dingOrder.getStatus())) {
                                String corpToken = corpTokenManage.getCorpToken(dingOrder.getCorpId());
                                Boolean finish = dingOpenApiService.orderFinish(dingOrder.getOrderId(), corpToken);
                                if (BooleanUtil.isTrue(finish)) {
                                    this.update(new LambdaUpdateWrapper<DingOrder>().set(DingOrder::getStatus,
                                                    com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_3)
                                            .eq(DingOrder::getId, dingOrder.getId()));
                                }
                            } else if (com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_1.equals(dingOrder.getStatus())) {
                                SpringUtil.getBean(DingOrderServiceImpl.class).createByLock(dingOrder);
                            }
                        }
                    }
                });
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DingResourcePayDto getResourcePay(DingResourcePayDto payDto) {
        String insideSku = generalService.insideSku();
        if (StrUtil.isEmpty(insideSku)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "内购Sku不存在");
        }

        // 资源包信息
        ResourcePackDto packInfo = resourceConfigService.getPackInfo(payDto.getResourceId());
        // 订单支付金额
        BigDecimal payMoney = packInfo.getAnnualPrice().multiply(new BigDecimal(Convert.toStr(payDto.getNum())));

        // 优惠金额
        BigDecimal discountMoney = BigDecimal.ZERO;
        int bonus = discountConfigService.queryBonus(packInfo.getDiscountInfo(), payDto.getNum());
        if (bonus > 0) {
            discountMoney = new BigDecimal(Convert.toStr(bonus)).multiply(packInfo.getUnitPrice());
        }

        // 当前公司
        AsCompany company = companyService.getById(LoginUserThreadLocal.getCompanyId());
        // 创建新订单
        Long orderId = IdUtils.getId();
        AsSaleOrder order = new AsSaleOrder()
                .setId(orderId)
                .setOrderNo(StringUtils.getOrderNo("ON"))
                .setSource(1)
                .setStatus(ManageConstant.SALE_ORDER_STATUS_WAIT)
                .setCompanyName(company.getName())
                .setPayType(1)
                .setReceivePaymentNo("dingtalk_default")
                .setTotalMoney(payMoney.add(discountMoney).setScale(4, RoundingMode.HALF_UP))
                .setPayMoney(payMoney.setScale(4, RoundingMode.HALF_UP))
                .setDiscountMoney(discountMoney.setScale(4, RoundingMode.HALF_UP))
                .setSummary(String.format(ORDER_SUMMARY, packInfo.getCapacity(), packInfo.getAnnualPrice()))
                .setCreateTime(LocalDateTime.now());

        // 添加资产包到商品明细
        AsSaleOrderItem saleOrderItem = new AsSaleOrderItem()
                .setName(packInfo.getResourceName())
                .setModel(Convert.toStr(packInfo.getCapacity()))
                .setSku(packInfo.getSkuCode())
                .setQuantity(payDto.getNum() * 12)
                .setSaleOrderId(orderId)
                .setOriginalMoney(packInfo.getAnnualPrice())
                .setSaleMoney(packInfo.getAnnualPrice())
                .setType(DictConstant.PRODUCT_TYPE_RESOURCE_PACK)
                .setProduct(JsonUtil.toJsonObject(packInfo))
                .setTotalMoney(order.getTotalMoney());
        if (bonus > 0) {
            saleOrderItem.setDiscountInfo(Convert.toStr(bonus));
        }
        saleOrderService.save(order);
        saleOrderItemService.save(saleOrderItem);
        payDto.setOrderNo(order.getOrderNo());
        payDto.setPayFee(order.getPayMoney());
        payDto.setSkuCode(insideSku);
        return payDto;
    }

    @Override
    public Boolean checkOrderStatus(String orderNo) {
        return saleOrderService.count(Wrappers.lambdaQuery(AsSaleOrder.class)
                .eq(AsSaleOrder::getOrderNo, orderNo)
                .eq(AsSaleOrder::getStatus, ManageConstant.SALE_ORDER_STATUS_COMPLETE)) > 0;
    }

    @Override
    public Boolean handleOrder(DingOrder dingOrder) {
        redisDistributeLock.lock("dingHandleOrder:" + dingOrder.getCompanyId(), 3, TimeUnit.MINUTES, (a) -> {
            DingOrder one = this.getOne(new LambdaQueryWrapper<DingOrder>().eq(DingOrder::getOrderId, dingOrder.getOrderId()));
            // 订单不存在，保存订单
            if (one == null) {
                dingOrder.setStatus(com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_1);
                save(dingOrder);
                one = dingOrder;
            }
            // 订单存在判断状态
            Integer status = one.getStatus();
            if (com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_1.equals(status)) {
                SpringUtil.getBean(DingOrderServiceImpl.class).createByLock(dingOrder);
            }
        });
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void createByLock(DingOrder dingOrder) {
        JSONObject sourceData = dingOrder.getSourceData();
        String extendParam = null;
        if (sourceData.containsKey("extendParam")) {
            String extendParamStr = sourceData.getString("extendParam");
            if (StrUtil.isNotEmpty(extendParamStr)) {
                try {
                    extendParam = URLDecoder.decode(extendParamStr, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    log.error("dingOrder => {}, extendParam => {} decode error", dingOrder.getOrderId(), extendParamStr, e);
                    return;
                }
            }
        }

        //存在ext属性说明是内购，内购又分固定规格和非固定规格
        if (StrUtil.isNotEmpty(extendParam)) {
            createInside(dingOrder, extendParam);
        } else {
            //没有ext属性说明是在钉钉侧触发的订单，分为试用赠送和天元平台代客下单x
            createInit(dingOrder);
        }
    }

    private void createInside(DingOrder dingOrder, String extend) {
        //获取扩展参数，这个是云资产调用获取支付地址时传给钉钉的
        JSONObject extendParam = (JSONObject) JSONObject.parse(extend);
        String orderNo = extendParam.getString("orderNo");
        //没有订单号，就是异常数据，不进行处理
        if (StrUtil.isBlank(orderNo)) {
            log.error("extendParam saleOrder  ==>> {} not exists, dingOrderId ==>> {}", orderNo, dingOrder.getId());
            return ;
        }

        //获取云资产订单信息
        AsSaleOrder saleOrder = saleOrderService.getOne(Wrappers.lambdaQuery(AsSaleOrder.class)
                .eq(AsSaleOrder::getOrderNo, orderNo));
        if (saleOrder == null) {
            log.error("saleOrder  ==>> {} not exists, dingOrderId ==>> {}", orderNo, dingOrder.getId());
            return;
        }
        if (ManageConstant.SALE_ORDER_STATUS_WAIT != saleOrder.getStatus()) {
            log.error("saleOrder  ==>> {} has handle", orderNo);
            return;
        }

        //获取企业id
        if (dingOrder.getCorpId() == null) {
            DingCorp one = dingCorpService.getOne(Wrappers.lambdaQuery(DingCorp.class)
                    .eq(DingCorp::getCompanyId, saleOrder.getCompanyId()));
            dingOrder.setCorpId(one.getCorpId());
            dingOrder.setCompanyId(saleOrder.getCompanyId());
        }

        //支付成功，资源包立即到账
        handleSaleOrder(saleOrder.getId(), dingOrder);

        //修改钉钉订单状态
        LambdaUpdateWrapper<DingOrder> dingOrderUpdateWrapper = Wrappers.lambdaUpdate(DingOrder.class)
                .set(DingOrder::getStatus, com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_2)
                .set(DingOrder::getCompanyId, dingOrder.getCompanyId())
                .set(DingOrder::getCorpId, dingOrder.getCorpId())
                .set(DingOrder::getOrderType, "INSIDE")
                .set(DingOrder::getOutTradeNo, orderNo)
                .eq(DingOrder::getId, dingOrder.getId())
                //加上状态判断条件，防止钉钉回调超时重试
                .eq(DingOrder::getStatus,com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_1);

        //钉钉端订单处理完成
        String corpToken = corpTokenManage.getCorpToken(dingOrder.getCorpId());
        Boolean finish = dingOpenApiService.orderFinish(dingOrder.getOrderId(), corpToken);
        if (BooleanUtil.isTrue(finish)) {
            dingOrderUpdateWrapper.set(DingOrder::getStatus, com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_3);
        }

        //更新订单状态
        this.update(dingOrderUpdateWrapper);

        // 老带新记录
        recommendRecordService.tradeRecord(saleOrder.getId(), saleOrder.getCompanyId(), saleOrder.getTotalMoney(), LocalDateTime.now());
    }

    /**
     * 订单支付成功，资源包立即到账
     * @param orderId
     * @param dingOrder
     */
    private void handleSaleOrder(Long orderId, DingOrder dingOrder) {
        //查询当前订单软件商品
        List<AsSaleOrderItem> saleOrderItemList = saleOrderItemService.list(Wrappers.lambdaQuery(AsSaleOrderItem.class)
                .eq(AsSaleOrderItem::getSaleOrderId, orderId).eq(AsSaleOrderItem::getType, 5));
        //没有软件产品，只有硬件产品就直接修改订单状态即可
        if (CollUtil.isEmpty(saleOrderItemList)) {
            //修改订单状态，这里只能去修改待支付状态订单，防止钉钉重复回调
            saleOrderService.update(Wrappers.lambdaUpdate(AsSaleOrder.class)
                    .set(AsSaleOrder::getStatus, ManageConstant.SALE_ORDER_STATUS_COMPLETE)
                    .set(AsSaleOrder::getOperateTime, LocalDateTime.now())
                    .eq(AsSaleOrder::getId, orderId)
                    .eq(AsSaleOrder::getStatus, ManageConstant.SALE_ORDER_STATUS_WAIT));
            return ;
        }

        //修改订单状态和支付时间
        LambdaUpdateWrapper<AsSaleOrder> modifyParam = Wrappers.lambdaUpdate(AsSaleOrder.class)
                .set(AsSaleOrder::getStatus, ManageConstant.SALE_ORDER_STATUS_COMPLETE)
                .set(AsSaleOrder::getOperateTime, LocalDateTime.now())
                .eq(AsSaleOrder::getId, orderId)
                .eq(AsSaleOrder::getStatus, ManageConstant.SALE_ORDER_STATUS_WAIT);

        //有软件产品，要区分是固定规格，还是非固定规格，固定规格直接资源包到账即可，非固定规格还要判断在钉钉侧是否使用了优惠
        //是否固定规格并且包含优惠，有钉钉侧优惠还需要调整订单金额和商品表的金额，以及去除优惠写入
        boolean isFixedContainDiscount = isFixedContainDiscount(saleOrderItemList, dingOrder);
        log.info("dingOrderService handleSaleOrder isFixedContainDiscount=[{}]", isFixedContainDiscount);
        if (isFixedContainDiscount) {
            ResourcePackDto resourcePackDto = JSONObject.parseObject(saleOrderItemList.get(0).getProduct().toJSONString()
                    , ResourcePackDto.class);
            BigDecimal oneHundred = BigDecimal.valueOf(100L);
            BigDecimal payMoney = dingOrder.getSourceData().getBigDecimal("payFee").divide(oneHundred).setScale(2, RoundingMode.HALF_UP);
            BigDecimal discountMoney = dingOrder.getSourceData().getBigDecimal("discountFee").divide(oneHundred).setScale(2, RoundingMode.HALF_UP);
            BigDecimal totalMoney = resourcePackDto.getAnnualPrice();

            //更新订单商品优惠信息和订单金额
            saleOrderItemService.update(Wrappers.lambdaUpdate(AsSaleOrderItem.class)
                    .set(AsSaleOrderItem::getDiscountInfo, "")
                    .set(AsSaleOrderItem::getTotalMoney, totalMoney)
                    .eq(AsSaleOrderItem::getId, saleOrderItemList.get(0).getId()));

            //更新订单总金额、支付金额和优惠金额
            modifyParam.set(AsSaleOrder::getTotalMoney, totalMoney)
                    .set(AsSaleOrder::getPayMoney, payMoney)
                    .set(AsSaleOrder::getDiscountMoney, discountMoney);

            LocalDateTime now = LocalDate.now().atTime(0, 0, 0);
            int monthTime = saleOrderItemList.get(0).getQuantity();
            LocalDateTime expirationTime = now.plusMonths(monthTime).withHour(23).withMinute(59).withSecond(59).withNano(0);
            // 生成容量记录
            AsCompanyResource resource = new AsCompanyResource();
            resource.setCompanyId(dingOrder.getCompanyId())
                    .setSkuCode(saleOrderItemList.get(0).getSku())
                    .setResourceName(saleOrderItemList.get(0).getName())
                    .setCapacity(resourcePackDto.getCapacity())
                    .setSaleOrderId(orderId)
                    .setExperience(false)
                    .setCreateTime(now)
                    .setEffectiveTime(now)
                    .setExpirationTime(expirationTime);
            companyResourceService.save(resource);
        } else {
            for (AsSaleOrderItem item : saleOrderItemList) {
                String discount = item.getDiscountInfo();
                Integer discountNum = Convert.toInt(discount, 0);

                JSONObject product = item.getProduct();
                ResourcePackDto packDto = product.toJavaObject(ResourcePackDto.class);

                LocalDateTime now = LocalDate.now().atTime(0, 0, 0);
                int monthTime = item.getQuantity() + discountNum;
                LocalDateTime expirationTime = now.plusMonths(monthTime).withHour(23).withMinute(59).withSecond(59).withNano(0);

                // 生成容量记录
                AsCompanyResource resource = new AsCompanyResource();
                resource.setCompanyId(dingOrder.getCompanyId())
                        .setSkuCode(item.getSku())
                        .setResourceName(item.getName())
                        .setCapacity(packDto.getCapacity())
                        .setSaleOrderId(orderId)
                        .setExperience(false)
                        .setCreateTime(now)
                        .setEffectiveTime(now)
                        .setExpirationTime(expirationTime);
                companyResourceService.save(resource);
            }
        }
        //更新订单状态
        saleOrderService.update(modifyParam);

    }

    /**
     * 是否固定规格且包含优惠
     * @return
     */
    private Boolean isFixedContainDiscount(List<AsSaleOrderItem> saleOrderItemList, DingOrder dingOrder) {
        //固定规格只能购买一件并且是只有一个商品
        if (saleOrderItemList.size() > 1) {
            return Boolean.FALSE;
        }

        //固定规格：仅购买一件商品
        if (saleOrderItemList.get(0).getQuantity() > 12) {
            return Boolean.FALSE;
        }

        //获取固定规格资源包配置
        AsResourceConfig resourceConfig = generalService.getResourceConfigByGoodsCode(dingOrder.getGoodsCode());
        if (Objects.isNull(resourceConfig)) {
            return Boolean.FALSE;
        }

        //如果是没有优惠，或者优惠金额为0
        if (Objects.isNull(dingOrder.getSourceData().get("discountFee"))
                || dingOrder.getSourceData().getBigDecimal("discountFee").compareTo(BigDecimal.ZERO) == 0) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void createInit(DingOrder dingOrder) {
        // 查询注册关联SKU信息
        String itemCode = dingOrder.getItemCode();
        AsResourceConfig resourceConfig = generalService.getResourceConfigByGoodsCode(itemCode);
        // 存在资源包关联
        if (dingOrder.getCompanyId() != null && resourceConfig != null) {
//            Integer dingSkuDuration = generalService.getDingSkuDuration(itemCode);
//            if (dingSkuDuration == null) {
//                dingSkuDuration = 99;
//            }
            AsCompany company = companyService.getById(dingOrder.getCompanyId());

            // 订单支付金额
            BigDecimal payMoney = new BigDecimal(dingOrder.getPayFee() / 100);

            // 判断是否赠送订单
            Integer orderLabel = dingOrder.getSourceData().getInteger("orderLabel");
            boolean giveOrder = false;
            if (orderLabel != null && orderLabel == 1) {
                giveOrder = true;
            }

            LocalDateTime start = LocalDateTimeUtil.of(dingOrder.getServiceStartTime());
            LocalDateTime end = LocalDateTimeUtil.of(dingOrder.getServiceStopTime());
            Period between = Period.between(start.toLocalDate(), end.toLocalDate());
            Integer dingSkuDuration = between.getYears() * 12 + between.getMonths();

            AsSaleOrder order = new AsSaleOrder()
                    .setId(IdUtils.getId())
                    .setOrderNo(StringUtils.getOrderNo("ON"))
                    .setCompanyId(dingOrder.getCompanyId())
                    .setSource(1)
                    .setStatus(ManageConstant.SALE_ORDER_STATUS_COMPLETE)
                    .setCompanyName(company.getName())
                    .setPayType(1)
                    .setReceivePaymentNo("dingtalk_default")
                    .setOperateTime(LocalDateTime.now())
                    .setTotalMoney(payMoney.setScale(4, RoundingMode.HALF_UP))
                    .setPayMoney(payMoney.setScale(4, RoundingMode.HALF_UP))
                    .setDiscountMoney(BigDecimal.ZERO)
                    .setSummary(String.format(ORDER_SUMMARY, resourceConfig.getCapacity(), resourceConfig.getAnnualPrice()))
                    .setCreateTime(LocalDateTime.now());
            saleOrderService.save(order);

            // 添加资产包到商品明细
            AsSaleOrderItem saleOrderItem = new AsSaleOrderItem()
                    .setName(resourceConfig.getResourceName())
                    .setModel(Convert.toStr(resourceConfig.getCapacity()))
                    .setSku(resourceConfig.getSkuCode())
                    .setQuantity(dingSkuDuration)
                    .setSaleOrderId(order.getId())
                    .setOriginalMoney(resourceConfig.getAnnualPrice())
                    .setSaleMoney(giveOrder ? BigDecimal.ZERO : resourceConfig.getAnnualPrice())
                    .setType(DictConstant.PRODUCT_TYPE_RESOURCE_PACK)
                    .setProduct(JsonUtil.toJsonObject(resourceConfig))
                    .setTotalMoney(order.getTotalMoney());
            // 赠送写入折扣信息
            if (giveOrder) {
                saleOrderItem.setDiscountInfo(Convert.toStr(dingSkuDuration));
            }
            saleOrderItemService.save(saleOrderItem);

            // 判断是否非新购
            if (!dingOrder.getOrderType().equals("BUY") && dingOrder.getCompanyId() != null) {
                // 需要把历史容量包过期，重新生成
                List<Long> resourceIds = getBaseMapper().getResourceIds(dingOrder.getCompanyId());
                if (CollUtil.isNotEmpty(resourceIds)) {
                    companyResourceService.update(Wrappers.lambdaUpdate(AsCompanyResource.class)
                            .set(AsCompanyResource::getExpirationTime, LocalDateTimeUtil.of(dingOrder.getServiceStartTime()))
                            .in(AsCompanyResource::getId, resourceIds));
                }
            }

            // 生成容量记录
            AsCompanyResource resource = new AsCompanyResource();
            resource.setCompanyId(dingOrder.getCompanyId())
                    .setSkuCode(resourceConfig.getSkuCode())
                    .setResourceName(resourceConfig.getResourceName())
                    .setCapacity(resourceConfig.getCapacity())
                    .setSaleOrderId(order.getId())
                    .setExperience(false)
                    .setCreateTime(LocalDateTimeUtil.of(dingOrder.getServiceStartTime()))
                    .setEffectiveTime(LocalDateTimeUtil.of(dingOrder.getServiceStartTime()))
                    .setExpirationTime(LocalDateTimeUtil.of(dingOrder.getServiceStopTime()));
            companyResourceService.save(resource);

            // 更新订单状态
            this.update(new LambdaUpdateWrapper<DingOrder>()
                    .set(DingOrder::getStatus, com.niimbot.asset.dingtalk.base.model.constant.DictConstant.ORDER_STATUS_3)
                    .set(DingOrder::getCompanyId, dingOrder.getCompanyId())
                    .set(DingOrder::getOrderType, dingOrder.getOrderType())
                    .set(DingOrder::getOutTradeNo, order.getOrderNo())
                    .eq(DingOrder::getId, dingOrder.getId()));

            // 老带新记录
            recommendRecordService.tradeRecord(order.getId(), order.getCompanyId(), order.getTotalMoney(), order.getOperateTime());
        } else {
            log.error("dingOrder companyId isnull or resourceConfig is null, dingOrderId = {}", dingOrder.getId());
        }
    }
}
