package com.niimbot.asset.dingtalk.org.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dingtalk.org.service.DingtalkContactsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * created by chen.y on 2021/8/30 9:48
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("server/dingtalk/orgDept")
public class OrgDeptServiceController {

    private final DingtalkContactsService contactsService;

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean orgDept(@RequestParam(value = "corpId") String corpId,
                           @RequestParam(value = "deptId") Long deptId,
                           @RequestBody JSONObject content) {
        content.put(DingtalkContactsService.Constant.BIZ_ID, deptId);
        return contactsService.handleDepartEvent(corpId, content);
    }

}
