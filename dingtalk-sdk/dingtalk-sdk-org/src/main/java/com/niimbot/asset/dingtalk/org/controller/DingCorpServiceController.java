package com.niimbot.asset.dingtalk.org.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.GoodsTryout;
import com.niimbot.asset.dingtalk.base.model.constant.RedisConstant;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * created by chen.y on 2021/8/26 18:28
 */
@RestController
@RequestMapping("server/dingtalk/dingCorp")
public class DingCorpServiceController {

    private final DingCorpService dingCorpService;
    private final RedisService redisService;

    @Autowired
    public DingCorpServiceController(DingCorpService dingCorpService,
                                     RedisService redisService) {
        this.dingCorpService = dingCorpService;
        this.redisService = redisService;
    }

    @GetMapping
    public DingCorp getByCorp(DingCorp dingCorp) {
        return this.dingCorpService.getOne(new LambdaQueryWrapper<DingCorp>()
                        .eq(StrUtil.isNotBlank(dingCorp.getCorpId()), DingCorp::getCorpId, dingCorp.getCorpId())
                        .eq(ObjectUtil.isNotNull(dingCorp.getCompanyId()), DingCorp::getCompanyId, dingCorp.getCompanyId()),
                false);
    }

    @PostMapping("/goodsTryout")
    public Boolean goodsTryout(@RequestBody JSONObject content) {
        if (content == null) {
            return false;
        }
        GoodsTryout goodsTryout = content.toJavaObject(GoodsTryout.class);
        int count = Convert.toInt(dingCorpService.count(Wrappers.lambdaQuery(DingCorp.class)
                .eq(DingCorp::getCorpId, goodsTryout.getCorpId())));
        // 存在就更新，不存在写入缓存
        if (count > 0) {
            return dingCorpService.update(Wrappers.lambdaUpdate(DingCorp.class)
                    .set(DingCorp::getAuthChannelType, goodsTryout.getTryoutType())
                    .eq(DingCorp::getCorpId, goodsTryout.getCorpId())
                    .ne(DingCorp::getAuthChannelType, GoodsTryout.TryoutType.ENTERPRISE_TRYOUT.getValue()));
        } else {
            redisService.set(RedisConstant.getGoodsTryout(goodsTryout.getCorpId()), goodsTryout.getTryoutType());
            return true;
        }
    }

    @GetMapping("/tryoutType")
    public String getTryoutType() {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        DingCorp dingCorp = dingCorpService.getByCompanyId(companyId);
        return dingCorp.getAuthChannelType();
    }

    @PostMapping("/remove/{corpId}")
    public Boolean remove(@PathVariable("corpId") String corpId, @RequestBody JSONObject content) {
        return dingCorpService.removeCorp(corpId, content);
    }
}
