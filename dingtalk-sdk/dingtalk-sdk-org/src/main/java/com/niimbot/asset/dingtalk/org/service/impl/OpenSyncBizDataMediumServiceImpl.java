package com.niimbot.asset.dingtalk.org.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dingtalk.base.model.OpenSyncBizDataMedium;
import com.niimbot.asset.dingtalk.org.mapper.OpenSyncBizDataMediumMapper;
import com.niimbot.asset.dingtalk.org.service.OpenSyncBizDataMediumService;
import org.springframework.stereotype.Service;

/**
 * created by chen.y on 2021/8/23 15:15
 */
@Service
public class OpenSyncBizDataMediumServiceImpl extends ServiceImpl<OpenSyncBizDataMediumMapper, OpenSyncBizDataMedium> implements OpenSyncBizDataMediumService {
}
