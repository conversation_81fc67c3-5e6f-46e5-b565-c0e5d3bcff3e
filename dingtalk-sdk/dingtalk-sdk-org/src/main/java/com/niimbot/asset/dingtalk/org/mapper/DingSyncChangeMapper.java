package com.niimbot.asset.dingtalk.org.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.dingtalk.base.dto.DingSyncChangeDto;
import com.niimbot.asset.dingtalk.base.model.DingSyncChange;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@EnableDataPerm(excludeMethodName = {"getEmp", "getOrg"})
public interface DingSyncChangeMapper extends BaseMapper<DingSyncChange> {

    /**
     * 查询异动记录
     *
     * @param type   异动类型
     * @param status 状态
     * @return
     */
    List<DingSyncChangeDto> list(@Param("type") Integer type, @Param("status") Integer status);

    @Select("select id, emp_name, emp_no from as_cus_employee where id = #{id} and company_id = #{companyId}")
    AsCusEmployee getEmp(@Param("id") Long id, @Param("companyId") Long companyId);

    @Select("select id, org_name, external_org_id from as_org where id = #{id} and company_id = #{companyId}")
    AsOrg getOrg(@Param("id") Long id, @Param("companyId") Long companyId);
}
