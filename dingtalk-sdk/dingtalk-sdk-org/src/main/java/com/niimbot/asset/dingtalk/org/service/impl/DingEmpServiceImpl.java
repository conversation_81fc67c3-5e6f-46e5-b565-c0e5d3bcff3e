package com.niimbot.asset.dingtalk.org.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.google.common.collect.Lists;
import com.niimbot.asset.dingtalk.base.model.constant.DictConstant;
import com.niimbot.asset.dingtalk.org.service.DingEmpService;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * created by chen.y on 2021/8/24 17:38
 */
@Slf4j
@Service
public class DingEmpServiceImpl implements DingEmpService {

    @Resource
    private AsCusEmployeeService employeeService;

    @Resource
    private AsCusEmployeeExtService employeeExtService;

    @Resource
    private AsCusEmployeeSettingService employeeSettingService;

    @Resource
    private AsThirdPartyEmployeeService thirdPartyEmployeeService;

    @Resource
    private CusUserService userService;

    @Resource
    private AsAccountEmployeeService accountEmployeeService;

    @Resource
    private CusRoleService roleService;

    @Resource
    private CusUserRoleService userRoleService;

    @Resource
    private AsUserOrgService userOrgService;

    @Resource
    private CusUserCompanyService cusUserCompanyService;

    @Resource
    private AsDataPermissionService dataPermissionService;

    private String getType() {
        return "DINGTALK";
    }

    @Override
    public Long saveAdmin(Long companyId, OapiV2UserGetResponse.UserGetResponse response, Long orgId) {
        Long empId = IdUtils.getId();
        // ================================员工=======================================
        AsCusEmployee cusEmployee = new AsCusEmployee();
        cusEmployee.setId(empId)
                .setCompanyId(companyId)
                .setImage(response.getAvatar())
                .setEmpName(response.getName())
//                .setEmpNo(response.getJobNumber())
                .setPosition(response.getTitle())
                .setImage(response.getAvatar())
                .setDataScope(DataPermRuleType.TYPE_ALL.getValue().shortValue())
                .setRemark(response.getAdmin() ? DictConstant.DING_ADMIN : "");

        AsThirdPartyEmployee thirdPartyEmployee = new AsThirdPartyEmployee()
                .setId(IdUtils.getId()).setCompanyId(companyId).setEmployeeId(empId).setUserId(response.getUserid()).setType(getType());

        employeeService.save(cusEmployee);
        thirdPartyEmployeeService.save(thirdPartyEmployee);

        // ================================员工组织=======================================
        userOrgService.save(new AsUserOrg().setOrgId(orgId).setUserId(empId));

        // ================================账户=======================================
        // 判断是否有账户
        AsCusUser one = userService.getOne(new LambdaQueryWrapper<AsCusUser>().eq(AsCusUser::getUnionId, response.getUnionid()));
        if (ObjectUtil.isNull(one)) {
            one = new AsCusUser();
            one.setId(IdUtils.getId())
                    .setAccount(response.getUserid())
                    .setUnionId(response.getUnionid())
                    .setNickname(response.getName())
                    .setImage(response.getAvatar())
                    .setCompanyId(companyId)
                    .setStatus((short) 1)
                    .setSource(1);
            userService.save(one);
        }
        AsAccountEmployee accountEmployee = new AsAccountEmployee();
        accountEmployee.setCompanyId(companyId).setAccountId(one.getId()).setEmployeeId(empId);
        accountEmployeeService.save(accountEmployee);

        // ================================账户扩展=======================================
        AsCusEmployeeExt employeeExt = new AsCusEmployeeExt();
        employeeExt.setId(empId).setAgentId(0);
        employeeExtService.save(employeeExt);

        // ================================账户设置=======================================
        AsCusEmployeeSetting employeeSetting = new AsCusEmployeeSetting();
        employeeSetting.setUserId(empId)
                .setAppToolbox(Lists.newArrayList())
                .setPcToolbox(Lists.newArrayList())
                .setPcHome(Lists.newArrayList())
                .setAssetHead(Lists.newArrayList())
                .setMaterialHead(Lists.newArrayList());
        employeeSettingService.save(employeeSetting);

        // ================================角色关联=======================================
        AsCusRole role = roleService.getOne(new LambdaQueryWrapper<AsCusRole>()
                .eq(AsCusRole::getCompanyId, companyId).eq(AsCusRole::getRoleCode, BaseConstant.ADMIN_ROLE));
        AsUserRole userRole = new AsUserRole();
        userRole.setUserId(empId);
        userRole.setRoleId(role.getId());
        userRoleService.save(userRole);

        // ================================用户公司=======================================
        AsUserCompany asUserCompany = new AsUserCompany().setUserId(empId).setCompanyId(companyId);
        cusUserCompanyService.save(asUserCompany);

        // ================================数据权限=======================================
        dataPermissionService.initDataPermission(cusEmployee.getCompanyId(), cusEmployee.getId(), BaseConstant.ADMIN_ROLE);

        return empId;
    }
}
