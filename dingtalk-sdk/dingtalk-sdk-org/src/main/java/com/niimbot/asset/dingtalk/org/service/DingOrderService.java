package com.niimbot.asset.dingtalk.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.dingtalk.base.dto.DingResourcePayDto;
import com.niimbot.asset.dingtalk.base.model.DingOrder;

/**
 * <p>
 * 钉钉订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
public interface DingOrderService extends IService<DingOrder> {

    Boolean handleOrder(DingOrder dingOrder);

    DingResourcePayDto getResourcePay(DingResourcePayDto payDto);

    Boolean checkOrderStatus(String orderNo);
}
