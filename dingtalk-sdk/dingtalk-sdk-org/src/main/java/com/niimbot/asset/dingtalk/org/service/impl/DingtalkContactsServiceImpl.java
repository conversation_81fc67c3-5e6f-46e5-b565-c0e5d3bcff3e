package com.niimbot.asset.dingtalk.org.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
import com.dingtalk.api.request.OapiV2DepartmentListsubidRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubidResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.google.common.collect.Lists;
import com.niimbot.asset.activiti.model.ActApproveRole;
import com.niimbot.asset.activiti.model.ActApproveRoleMember;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.dingtalk.base.config.CorpTokenManage;
import com.niimbot.asset.dingtalk.base.dto.AuthScopes;
import com.niimbot.asset.dingtalk.base.dto.EmployeeUpdateDto;
import com.niimbot.asset.dingtalk.base.dto.OrgUpdateDto;
import com.niimbot.asset.dingtalk.base.model.DingSyncChange;
import com.niimbot.asset.dingtalk.base.model.GoodsTryout;
import com.niimbot.asset.dingtalk.base.model.constant.DictConstant;
import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.dingtalk.base.service.DingCorpService;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.org.mapper.DingAssetMapper;
import com.niimbot.asset.dingtalk.org.service.DingSyncChangeService;
import com.niimbot.asset.dingtalk.org.service.DingtalkContactsService;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.DeduplicationUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheEmpStrategy;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.inventory.service.AsInventoryTaskService;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.abs.EntMatAbs;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusEmployeeTransferDto;
import com.niimbot.system.OrgDto;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.niimbot.asset.dingtalk.org.service.DingtalkContactsService.Constant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DingtalkContactsServiceImpl implements DingtalkContactsService {

    @Resource
    private CorpTokenManage corpTokenManage;

    @Resource
    private DingOpenApiService openApiService;

    @Resource
    private DingCorpService corpService;

    @Resource
    private AsCusEmployeeService employeeService;

    @Resource
    private AsUserOrgService employeeOrgService;

    @Resource
    private AsCusEmployeeExtService employeeExtService;

    @Resource
    private AsCusEmployeeSettingService employeeSettingService;

    @Resource
    private CusUserService accountService;

    @Resource
    private AsThirdPartyEmployeeService thirdPartyEmployeeService;

    @Resource
    private AsAccountEmployeeService accountEmployeeService;

    @Resource
    private CusRoleService roleService;

    @Resource
    private CusUserRoleService employeeRoleService;

    @Resource
    private AsDataPermissionService dataPermissionService;

    @Resource
    private DataAuthorityService dataAuthorityService;

    @Resource
    private UserSensitiveAuthorityService userSensitiveAuthorityService;

    @Resource
    private DingSyncChangeService dingSyncChangeService;

    @Resource
    private AsInventoryTaskService inventoryTaskService;

    @Resource
    private ActWorkflowService workflowService;

    @Resource
    private ActApproveRoleService approveRoleService;

    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Resource
    private EntMatAbs entMatAbs;

    @Resource
    private MessageService messageService;

    @Resource
    private DingAssetMapper dingAssetMapper;

    @Resource
    private OrgService orgService;

    @Resource
    private ModelDataScopeServiceImpl modelDataScopeService;

    @Resource
    private RedissonClient redissonClient;

    @Resource(name = "assetTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    private RLock getLock(Long companyId) {
        String lockKey = String.format(Constant.PULL_LOCK_KEY, companyId);
        return redissonClient.getLock(lockKey);
    }

    @Override
    public Boolean processing(Long companyId) {
        RLock lock = getLock(companyId);
        return lock.isLocked();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pull(Long companyId) {
        RLock lock = getLock(companyId);
        try {
            if (lock.isLocked()) {
                log.warn("ding pull contacts is locked company[{}]", companyId);
                return;
            }
            lock.lock();
            // 1.获取企业corpId
            String corpId = corpService.getCorpId(companyId);
            // 2.获取可见范围
            AuthScopes authScope = getAuthScope(corpId);
            log.info("ding pull contacts authScope[{}]", JSONObject.toJSONString(authScope));
            // 2.获取通讯录组织列表
            List<AsOrg> orgs = getDingtalkOrgs(companyId, corpId, authScope.getAuthedDept());
            Map<String, Long> orgIdMap = new ConcurrentHashMap<>(orgs.size());
            List<Long> orgDel = new ArrayList<>(50);
            orgs.forEach(v -> {
                if (v.getRemark().contains(FOR_DELETE)) {
                    orgDel.add(v.getId());
                } else {
                    orgIdMap.put(v.getExternalOrgId(), v.getId());
                }
            });
            // 3.获取通讯录用户列表
            InnerUser innerUser = getDingtalkUsers(companyId, corpId, orgIdMap.keySet(), authScope.getAuthedUser());
            List<AsCusUser> accounts = innerUser.getAccounts();
            List<AsCusEmployee> employees = innerUser.getEmployees();
            List<UserMapping> mappings = innerUser.getUserMap();
            // 4.处理数据
            handleOrg(orgs, mappings.stream().filter(DeduplicationUtil.distinctByKey(UserMapping::getUserId)).collect(Collectors.toConcurrentMap(UserMapping::getUserId, UserMapping::getId)));
            handleEmployee(companyId, employees, mappings, orgIdMap);
            handleOrgDelete(companyId, orgDel);
            handleAccount(companyId, accounts);
        } finally {
            reloadDictCache(companyId);
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void background(Long companyId) {
        executor.execute(() -> {
            try {
                SpringUtil.getBean(DingtalkContactsService.class).pull(companyId);
            } catch (Exception e) {
                log.error("ding pull contacts error", e);
            }
        });
    }

    private void handleOrg(List<AsOrg> orgs, Map<String, Long> remoteUserIdMap) {
        if (CollUtil.isEmpty(orgs)) {
            return;
        }
        // 遍历一次
        int initialCapacity = orgs.size() / 2;
        List<AsOrg> orgForCreate = new ArrayList<>(initialCapacity);
        List<AsOrg> orgForUpdate = new ArrayList<>(initialCapacity);
        orgs.forEach(v -> {
            // 回填主管
            if (CollUtil.isNotEmpty(v.getExternalDirector())) {
                v.setDirector(v.getExternalDirector().stream().filter(remoteUserIdMap::containsKey).map(remoteUserIdMap::get).collect(Collectors.toList()));
            }
            // 新增
            if (v.getRemark().contains(FOR_CREATE)) {
                orgForCreate.add(v);
            }
            // 更新
            if (v.getRemark().contains(FOR_UPDATE)) {
                orgForUpdate.add(v);
            }
            v.setRemark(null);
        });
        if (CollUtil.isNotEmpty(orgForCreate)) {
            orgService.saveBatch(orgForCreate);
        }
        if (CollUtil.isNotEmpty(orgForUpdate)) {
            orgService.updateBatchById(orgForUpdate);
        }
    }

    private void handleOrgDelete(Long companyId, List<Long> orgForDel) {
        if (CollUtil.isEmpty(orgForDel)) {
            return;
        }
        orgService.removeBatchByIds(orgForDel);
        // 异动记录
        List<DingSyncChange> dingSyncChanges = new ArrayList<>();
        Set<Long> syncChangeOrgIds = checkOrgAsset(orgForDel, companyId);
        syncChangeOrgIds.forEach(orgId -> {
            // 写入异动记录
            DingSyncChange syncChange = new DingSyncChange();
            DingSyncChange dingSyncChange = syncChange.setResId(orgId).setCompanyId(companyId).setFromOrg(new ArrayList<>()).setToOrg(new ArrayList<>()).setType(3);
            dingSyncChanges.add(dingSyncChange);
        });
        if (CollUtil.isNotEmpty(dingSyncChanges)) {
            this.dingSyncChangeService.saveBatch(dingSyncChanges);
        }
    }

    private Set<Long> checkOrgAsset(List<Long> removeOrgIds, Long companyId) {
        // 使用组织
        List<Long> useOrgIds = dingAssetMapper.checkUseOrg(removeOrgIds, companyId);
        // 管理组织
        List<Long> orgOwnerIds = dingAssetMapper.checkOrgOwner(removeOrgIds, companyId);
        useOrgIds.addAll(orgOwnerIds);
        return new HashSet<>(useOrgIds);
    }

    private void handleEmployee(Long companyId, List<AsCusEmployee> employees, List<UserMapping> mappings, Map<String, Long> remoteOrgIdMap) {
        if (CollUtil.isEmpty(employees)) {
            return;
        }
        // 如果远程组织数据为空，可能是用户没有授权
        if (CollUtil.isEmpty(remoteOrgIdMap)) {
            remoteOrgIdMap = orgService.list(
                            Wrappers.lambdaQuery(AsOrg.class)
                                    .select(AsOrg::getExternalOrgId, AsOrg::getId)
                                    .eq(AsOrg::getCompanyId, companyId)
                                    .isNotNull(AsOrg::getExternalOrgId)
                    ).stream()
                    .filter(DeduplicationUtil.distinctByKey(AsOrg::getExternalOrgId))
                    .collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, AsOrg::getId));
        }
        Map<Long, String> employeeIdUserIdMap = mappings.stream().collect(Collectors.toConcurrentMap(UserMapping::getId, UserMapping::getUserId));
        Map<Long, Long> employeeIdAccountIdMap = mappings.stream().collect(Collectors.toConcurrentMap(UserMapping::getId, UserMapping::getAccountId));
        Map<Long, List<String>> employeeIdDepartIdsMap = mappings.stream().collect(Collectors.toConcurrentMap(UserMapping::getId, UserMapping::getOrgIds));
        int initCapacity = employees.size() / 2;
        List<AsCusEmployee> employeeForCreate = new ArrayList<>(initCapacity);
        List<AsCusEmployee> employeeForUpdate = new ArrayList<>(initCapacity);
        List<Long> employeeForDelete = new ArrayList<>(initCapacity);
        employees.forEach(v -> {
            if (v.getRemark().contains(FOR_CREATE)) {
                employeeForCreate.add(v);
            }
            if (v.getRemark().contains(FOR_UPDATE)) {
                employeeForUpdate.add(v);
            }
            if (v.getRemark().contains(FOR_DELETE)) {
                employeeForDelete.add(v.getId());
            }
            // 写入钉钉管理员标识
            if (v.getRemark().contains(DictConstant.DING_ADMIN)) {
                v.setRemark(DictConstant.DING_ADMIN);
            } else {
                v.setRemark(null);
            }
        });
        handleEmployeeCreate(companyId, employeeForCreate, employeeIdUserIdMap, employeeIdAccountIdMap, employeeIdDepartIdsMap, remoteOrgIdMap);
        handleEmployeeUpdate(companyId, employeeForUpdate, employeeIdDepartIdsMap, employeeIdAccountIdMap, remoteOrgIdMap);
        handleEmployeeDelete(companyId, employeeForDelete);
    }

    private void handleEmployeeCreate(Long companyId, List<AsCusEmployee> employees, Map<Long, String> employeeIdUserIdMap, Map<Long, Long> employeeIdAccountIdMap, Map<Long, List<String>> employeeIdDepartIdsMap, Map<String, Long> departIdOrgIdMap) {
        if (CollUtil.isEmpty(employees)) {
            return;
        }
        int initCapacity = employees.size();
        List<AsCusEmployeeExt> exts = new ArrayList<>(initCapacity);
        List<AsCusEmployeeSetting> settings = new ArrayList<>(initCapacity);
        List<AsThirdPartyEmployee> parties = new ArrayList<>(initCapacity);
        List<AsAccountEmployee> accountEmployees = new ArrayList<>(initCapacity);
        employees.forEach(v -> {
            exts.add(new AsCusEmployeeExt().setId(v.getId()).setType(1).setAccountStatus(3));
            settings.add(new AsCusEmployeeSetting().setUserId(v.getId()));
            if (employeeIdUserIdMap.containsKey(v.getId())) {
                parties.add(new AsThirdPartyEmployee().setId(IdUtils.getId()).setEmployeeId(v.getId()).setCompanyId(companyId).setType("DINGTALK").setUserId(employeeIdUserIdMap.get(v.getId())));
            }
            if (employeeIdAccountIdMap.containsKey(v.getId())) {
                accountEmployees.add(new AsAccountEmployee().setId(IdUtils.getId()).setEmployeeId(v.getId()).setCompanyId(companyId).setAccountId(employeeIdAccountIdMap.get(v.getId())));
            }
        });
        employeeService.saveBatch(employees);
        employeeExtService.saveBatch(exts);
        employeeSettingService.saveBatch(settings);
        thirdPartyEmployeeService.saveBatch(parties);
        accountEmployeeService.saveBatch(accountEmployees);
        employeeOrgService.saveBatch(toUserOrgs(companyId, employees, employeeIdDepartIdsMap, departIdOrgIdMap));
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(companyId, employees.stream().map(AsCusEmployee::getId).collect(Collectors.toList()));
        // 初始化角色
        String roleCode = BaseConstant.COMMON_ROLE;
        String authChannelType = corpService.getByCompanyId(companyId).getAuthChannelType();
        if (GoodsTryout.TryoutType.PERSONAL_TRYOUT.getValue().equals(authChannelType)) {
            roleCode = BaseConstant.ASSET_ADMIN_ROLE;
        }
        for (AsCusEmployee employee : employees) {
            dataPermissionService.initDataPermission(companyId, employee.getId(), roleCode);
        }
    }

    private void handleEmployeeUpdate(Long companyId, List<AsCusEmployee> employees, Map<Long, List<String>> employeeIdDepartIdsMap, Map<Long, Long> employeeIdAccountIdMap, Map<String, Long> departIdOrgIdMap) {
        if (CollUtil.isEmpty(employees)) {
            return;
        }
        // 员工异动
        List<Long> ids = employees.stream().map(AsCusEmployee::getId).collect(Collectors.toList());
        List<AsUserOrg> userOrgs = toUserOrgs(companyId, employees, employeeIdDepartIdsMap, departIdOrgIdMap);
        Map<Long, List<Long>> oldGroup = employeeOrgService.groupByUser(ids);
        Map<Long, List<Long>> newGroup = userOrgs.stream().collect(Collectors.groupingByConcurrent(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toList())));
        handleEmployeeChange(companyId, oldGroup, newGroup);
        employeeService.updateBatchById(employees);
        employeeOrgService.removeByUser(ids);
        employeeOrgService.saveBatch(userOrgs);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(companyId, ids);
        // 账号检查
        List<Long> hasAccountIds = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .select(AsAccountEmployee::getEmployeeId)
                        .eq(AsAccountEmployee::getCompanyId, companyId)
                        .in(AsAccountEmployee::getEmployeeId, ids)
        ).stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toList());
        List<AsAccountEmployee> accountEmployees = ids.stream()
                .filter(v -> !hasAccountIds.contains(v))
                .filter(v -> employeeIdAccountIdMap.containsKey(v) && Objects.nonNull(employeeIdAccountIdMap.get(v)))
                .map(v -> new AsAccountEmployee(employeeIdAccountIdMap.get(v), companyId, v))
                .collect(Collectors.toList());
        accountEmployeeService.saveBatch(accountEmployees);
    }

    private void handleEmployeeDelete(Long companyId, List<Long> employees) {
        if (CollUtil.isEmpty(employees)) {
            return;
        }
        AsCusEmployee admin = employeeService.getAdministratorByCompanyId(companyId);
        // 超管被删除时直接转移,取有钉钉超管标记的第一个员工
        handleTransferAdministrators(employees.contains(admin.getId()), companyId);
        // 员工异动
        Set<Long> ids = checkRemoveUserResource(employees, companyId);
        List<DingSyncChange> dingSyncChangeList = employees.stream()
                .filter(ids::contains)
                .map(userId -> new DingSyncChange().setResId(userId).setCompanyId(companyId).setFromOrg(ListUtil.empty()).setToOrg(ListUtil.empty()).setType(2))
                .collect(Collectors.toList());
        // 删除数据
        employeeService.removeByIds(employees);
        employeeExtService.removeByIds(employees);
        employeeSettingService.removeByIds(employees);
        thirdPartyEmployeeService.remove(Wrappers.lambdaUpdate(AsThirdPartyEmployee.class).in(AsThirdPartyEmployee::getEmployeeId, employees));
        accountEmployeeService.batchUnbindEmploy(employees);
        employeeRoleService.remove(Wrappers.lambdaUpdate(AsUserRole.class).in(AsUserRole::getUserId, employees));
        employeeOrgService.removeByUser(employees);
        dataAuthorityService.removeByUserIds(employees);
        userSensitiveAuthorityService.removeByUserIds(employees, companyId);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(companyId, employees);
        // 清理主管
        List<OrgDto> orgDtoList = orgService.listByDirectors(employees, companyId);
        List<AsOrg> updateOrgList = new ArrayList<>();
        for (OrgDto orgDto : orgDtoList) {
            List<Long> director = orgDto.getDirector();
            if (CollUtil.isNotEmpty(director)) {
                director.removeAll(employees);
                AsOrg org = new AsOrg();
                org.setId(orgDto.getId());
                org.setDirector(director);
                updateOrgList.add(org);
            }
        }
        orgService.updateBatchById(updateOrgList);
        // 踢出登录
        SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(employees.stream().map(v -> new AsCusUser().setId(v).setCompanyId(companyId)).collect(Collectors.toList()));
        // 最后发消息
        if (CollUtil.isNotEmpty(dingSyncChangeList)) {
            dingSyncChangeService.saveWithSendMsg(dingSyncChangeList);
            messageService.sendInnerMessage(MsgSendCmd.yd(companyId));
        }
    }

    private void handleTransferAdministrators(Boolean change, Long companyId) {
        if (!change) {
            return;
        }
        LambdaQueryWrapper<AsCusEmployee> wrapper = Wrappers.lambdaQuery(AsCusEmployee.class)
                .select(AsCusEmployee::getId)
                .eq(AsCusEmployee::getCompanyId, companyId)
                .orderByAsc(AsCusEmployee::getId)
                .last("LIMIT 1");
        Long newAdminId;
        AsCusEmployee first = employeeService.getOne(wrapper);
        wrapper.eq(AsCusEmployee::getRemark, DictConstant.DING_ADMIN);
        AsCusEmployee hadRemark = employeeService.getOne(wrapper);
        if (Objects.nonNull(hadRemark)) {
            newAdminId = hadRemark.getId();
        } else {
            newAdminId = first.getId();
        }
        if (Objects.isNull(newAdminId)) {
            return;
        }
        AsCusRole adminRole = roleService.getOne(Wrappers.<AsCusRole>lambdaQuery().eq(AsCusRole::getCompanyId, companyId).eq(AsCusRole::getRoleCode, BaseConstant.ADMIN_ROLE));
        // 设置新超管
        employeeRoleService.remove(Wrappers.lambdaUpdate(AsUserRole.class).eq(AsUserRole::getUserId, newAdminId));
        employeeRoleService.save(new AsUserRole().setUserId(newAdminId).setRoleId(adminRole.getId()));
        employeeService.update(Wrappers.lambdaUpdate(AsCusEmployee.class).set(AsCusEmployee::getDataScope, 0).eq(AsCusEmployee::getId, newAdminId));
        dataAuthorityService.remove(Wrappers.lambdaUpdate(AsDataAuthority.class).eq(AsDataAuthority::getUserId, newAdminId));
        dataPermissionService.initDataPermission(companyId, newAdminId, BaseConstant.ADMIN_ROLE);
        // 删除敏感数据
        userSensitiveAuthorityService.removeByUserIds(ListUtil.of(newAdminId), companyId);
    }

    private void handleEmployeeChange(Long companyId, Map<Long, List<Long>> oldGroup, Map<Long, List<Long>> newGroup) {
        List<AsOrg> orgs = orgService.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getOrgCode, AsOrg::getOrgName)
                        .eq(AsOrg::getCompanyId, companyId)
        );
        Set<Long> ids = checkEditUserResource(new ArrayList<>(oldGroup.keySet()), companyId);
        List<DingSyncChange> changes = new ArrayList<>(oldGroup.size());
        List<Long> kickoff = new ArrayList<>();
        oldGroup.forEach((k, v) -> {
            // 新增员工不用处理
            if (!newGroup.containsKey(k)) {
                return;
            }
            List<Long> form = new ArrayList<>(v);
            List<Long> to = new ArrayList<>(newGroup.get(k));
            String newOrgIdsToString = to.stream().sorted(Long::compare).map(String::valueOf).collect(Collectors.joining());
            String oldOrgIdsToString = new ArrayList<>(v).stream().sorted(Long::compare).map(String::valueOf).collect(Collectors.joining());
            // 没有变化不处理
            if (newOrgIdsToString.equals(oldOrgIdsToString)) {
                return;
            }
            // 异动的组织包含之前的组织不处理 A -> A || AB -> ABC || A -> ABC
            if (to.size() > 1 && new HashSet<>(to).containsAll(form)) {
                return;
            }
            // 自动处理 A->B, AB->B, 多个部门合并为一个【ABC->B】
            if (to.size() == 1) {
                // 最后一次未处理的员工部门异动
                DingSyncChange lastUntreatedRecord = dingSyncChangeService.lastUntreatedRecord(companyId, k, 1);
                if (Objects.nonNull(lastUntreatedRecord) && CollUtil.isNotEmpty(lastUntreatedRecord.getFromOrg())) {
                    form.addAll(lastUntreatedRecord.getFromOrg().stream().distinct().filter(id -> !form.contains(id)).collect(Collectors.toList()));
                }
                form.removeAll(to);
                // 原部门
                List<AsOrg> formOrgs = CollUtil.isEmpty(form) ? new ArrayList<>(6) : orgs.stream().filter(org -> form.contains(org.getId())).collect(Collectors.toList());
                // 现部门
                List<AsOrg> toOrgs = CollUtil.isEmpty(to) ? new ArrayList<>(6) : orgs.stream().filter(org -> to.contains(org.getId())).collect(Collectors.toList());
                List<CusEmployeeTransferDto> trans = form.stream().map(id -> new CusEmployeeTransferDto().setFrom(id).setTo(to.get(0))).collect(Collectors.toList());
                employeeService.changeOrgLog(formOrgs, toOrgs, trans, k, companyId);
                // 删除还未处理的员工编辑异动记录
                dingSyncChangeService.removeUntreatedRecord(companyId, k, 1);
                kickoff.add(k);
                return;
            }
            // 异动处理 A->BC, AB->BC, AB-CD
            // 如果没有使用的资产，也不记录异动信息，也不发消息
            if (ids.contains(k)) {
                DingSyncChange syncChange = new DingSyncChange();
                DingSyncChange dingSyncChange = syncChange.setResId(k).setCompanyId(companyId).setFromOrg(form).setToOrg(to).setType(1);
                changes.add(dingSyncChange);
                kickoff.add(k);
            }
        });
        if (CollUtil.isNotEmpty(changes)) {
            dingSyncChangeService.saveWithSendMsg(changes);
            messageService.sendInnerMessage(MsgSendCmd.yd(companyId));
        }
        if (CollUtil.isNotEmpty(kickoff)) {
            SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(
                    kickoff.stream().map(v -> new AsCusUser().setId(v).setCompanyId(companyId)).collect(Collectors.toList())
            );
        }
    }

    private void handleAccount(Long companyId, List<AsCusUser> accounts) {
        if (CollUtil.isEmpty(accounts)) {
            return;
        }
        int initCapacity = accounts.size() / 2;
        List<AsCusUser> accountForCreate = new ArrayList<>(initCapacity);
        List<AsCusUser> accountForUpdate = new ArrayList<>(initCapacity);
        List<Long> accountForDelete = new ArrayList<>(initCapacity);
        accounts.forEach(v -> {
            if (v.getRemark().contains(FOR_CREATE)) {
                accountForCreate.add(v);
            }
            if (v.getRemark().contains(FOR_UPDATE)) {
                accountForUpdate.add(v);
            }
            if (v.getRemark().contains(FOR_DELETE)) {
                accountForDelete.add(v.getId());
            }
            v.setRemark(null);
        });
        handleAccountCreate(companyId, accountForCreate);
        handleAccountUpdate(accountForUpdate);
        handleAccountDelete(companyId, accountForDelete);
    }

    private void handleAccountCreate(Long companyId, List<AsCusUser> accounts) {
        if (CollUtil.isEmpty(accounts)) {
            return;
        }
        accountService.saveBatch(accounts);
    }

    private void handleAccountUpdate(List<AsCusUser> accounts) {
        if (CollUtil.isEmpty(accounts)) {
            return;
        }
        accountService.updateBatchById(accounts);
    }

    private void handleAccountDelete(Long companyId, List<Long> accounts) {
        if (CollUtil.isEmpty(accounts)) {
            return;
        }
        List<AsCusUser> kickOffUser = accountEmployeeService.list(
                        Wrappers.lambdaQuery(AsAccountEmployee.class)
                                .eq(AsAccountEmployee::getCompanyId, companyId)
                                .in(AsAccountEmployee::getAccountId, accounts)
                )
                .stream().map(v -> new AsCusUser().setId(v.getEmployeeId()).setCompanyId(v.getCompanyId()))
                .collect(Collectors.toList());
        accountService.removeByIds(accounts);
        accountEmployeeService.remove(
                Wrappers.lambdaUpdate(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getCompanyId, companyId)
                        .in(AsAccountEmployee::getAccountId, accounts)
        );
        // 踢出登录
        SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(kickOffUser);
    }

    private Set<Long> checkEditUserResource(List<Long> editUserIds, Long companyId) {
        // 使用人
        List<Long> usePersonIds = dingAssetMapper.checkUsePerson(editUserIds, companyId);
        // 所属管理员
        List<Long> managerOwnerIds = dingAssetMapper.checkManagerOwner(editUserIds, companyId);
        usePersonIds.addAll(managerOwnerIds);
        return new HashSet<>(usePersonIds);
    }

    private Set<Long> checkRemoveUserResource(List<Long> removeUserIds, Long companyId) {
        // 使用人
        List<Long> usePersonIds = dingAssetMapper.checkUsePerson(removeUserIds, companyId);
        // 所属管理员
        List<Long> managerOwnerIds = dingAssetMapper.checkManagerOwner(removeUserIds, companyId);
        usePersonIds.addAll(managerOwnerIds);
        HashSet<Long> removeUserSet = new HashSet<>(usePersonIds);
        for (Long removeUserId : removeUserIds) {
            // 盘点
            if (!removeUserSet.contains(removeUserId)
                    && inventoryTaskService.checkInventory(removeUserId)) {
                removeUserSet.add(removeUserId);
            }
            // 审批流
            if (!removeUserSet.contains(removeUserId)
                    && workflowService.checkWorkflow(removeUserId)) {
                removeUserSet.add(removeUserId);
            }
            // 设备保养任务
            if (!removeUserSet.contains(removeUserId) && entMatAbs.entMatUserHasTask(removeUserId)) {
                removeUserSet.add(removeUserId);
            }
        }
        return removeUserSet;
    }

    private List<AsUserOrg> toUserOrgs(Long companyId, List<AsCusEmployee> employees, Map<Long, List<String>> employeeIdDepartIdsMap, Map<String, Long> departIdOrgIdMap) {
        Long rootOrgId = orgService.getRootOrg(companyId).getId();
        return employees.stream()
                .filter(v -> employeeIdDepartIdsMap.containsKey(v.getId()))
                .map(v -> {
                    List<AsUserOrg> userOrgs = employeeIdDepartIdsMap.get(v.getId())
                            .stream()
                            .filter(departIdOrgIdMap::containsKey)
                            .map(departIdOrgIdMap::get)
                            .map(k -> new AsUserOrg(v.getId(), k))
                            .collect(Collectors.toList());
                    // 说明同步过来的用户部门为空或者部门与本次同步的任意一个部门匹配，默认挂到根组织上
                    return CollUtil.isEmpty(userOrgs) ? Collections.singletonList(new AsUserOrg(v.getId(), rootOrgId)) : userOrgs;
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private AuthScopes getAuthScope(String corpId) {
        String corpToken = corpTokenManage.getCorpToken(corpId);
        return openApiService.getAuthScopes(corpToken);
    }

    private List<AsOrg> getDingtalkOrgs(Long companyId, String corpId, List<Long> auth) {
        log.info("pull dingtalk contacts auth org is [{}]", JSONObject.toJSONString(auth));
        if (CollUtil.isEmpty(auth)) {
            return Collections.emptyList();
        }
        // 当前组织列表
        List<AsOrg> localOrgs = orgService.getAll(companyId);
        AsOrg localRootOrg = orgService.getRootOrg(companyId);
        Map<String, Long> idMap = localOrgs.stream().filter(DeduplicationUtil.distinctByKey(AsOrg::getExternalOrgId)).collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, AsOrg::getId));
        // 组织可见范围
        Collections.reverse(auth);
        Set<String> remoteOrgIds = Collections.synchronizedSet(new HashSet<>(500));
        List<OapiV2DepartmentGetResponse.DeptGetResponse> remoteOrgs = new ArrayList<>(500);
        getRemoteOrgs(corpId, new HashSet<>(auth), remoteOrgIds, remoteOrgs);
        if (CollUtil.isEmpty(remoteOrgs)) {
            log.warn("pull ding depart is empty");
            return Collections.emptyList();
        }
        // 去重
        if (remoteOrgIds.size() != remoteOrgs.size()) {
            remoteOrgs = remoteOrgs.stream().filter(DeduplicationUtil.distinctByKey(OapiV2DepartmentGetResponse.DeptGetResponse::getDeptId)).collect(Collectors.toList());
        }
        log.info("ding pull contacts departs [{}]", JSONObject.toJSONString(remoteOrgs));
        // 转换
        List<AsOrg> convertOrgs = remoteOrgs.stream().map(v -> toOrg(idMap.get(String.valueOf(v.getDeptId())), localRootOrg.getId(), companyId, remoteOrgIds, v)).collect(Collectors.toList());
        // 清除
        remoteOrgs.clear();
        if (!remoteOrgIds.contains(Constant.DING_ROOT_ORG)) {
            convertOrgs.add(localRootOrg.setRemark(Constant.FOR_UPDATE));
        }
        convertOrgs = handleOrgTree(convertOrgs);
        List<AsOrg> forDel = localOrgs.stream().filter(v -> !remoteOrgIds.contains(v.getExternalOrgId()) && !Constant.DING_ROOT_ORG.equals(v.getExternalOrgId())).peek(v -> v.setRemark(Constant.FOR_DELETE)).collect(Collectors.toList());
        convertOrgs.addAll(forDel);
        return convertOrgs;
    }

    private void getRemoteOrgs(String corpId, Set<Long> remoteParentIds, Set<String> remoteOrgIds, List<OapiV2DepartmentGetResponse.DeptGetResponse> remoteOrgs) {
        OapiV2DepartmentGetResponse.DeptGetResponse parent = null;
        List<OapiV2DepartmentGetResponse.DeptGetResponse> child;
        for (Long pid : remoteParentIds) {
            // 当前组织
            if (!remoteOrgIds.contains(String.valueOf(pid))) {
                parent = getRemoteOrg(corpId, pid);
            }
            if (Objects.nonNull(parent)) {
                remoteOrgs.add(parent);
                remoteOrgIds.add(String.valueOf(pid));
            }
            // 子节点
            child = getRemoteChildOrgs(corpId, pid);
            try {
                TimeUnit.MILLISECONDS.sleep(50L);
            } catch (InterruptedException e) {
                log.warn("ding get child depart sleep error", e);
                continue;
            }
            if (CollUtil.isEmpty(child)) {
                continue;
            }
            remoteOrgs.addAll(child);
            Set<Long> nextParentIds = new HashSet<>(child.size());
            child.forEach(c -> {
                nextParentIds.add(c.getDeptId());
                remoteOrgIds.add(String.valueOf(c.getDeptId()));
            });
            getRemoteOrgs(corpId, nextParentIds, remoteOrgIds, remoteOrgs);
        }
    }

    private List<OapiV2DepartmentGetResponse.DeptGetResponse> getRemoteChildOrgs(String corpId, Long pid) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.DEPARTMENT_LIST_SUB_ID);
        OapiV2DepartmentListsubidRequest req = new OapiV2DepartmentListsubidRequest();
        req.setDeptId(pid);
        OapiV2DepartmentListsubidResponse rsp;
        try {
            String corpToken = corpTokenManage.getCorpToken(corpId);
            log.info("ding get child depart request[{}]", JSONObject.toJSONString(req));
            rsp = client.execute(req, corpToken);
        } catch (Exception e) {
            log.error("ding get child depart ids error", e);
            return null;
        }
        if (Objects.isNull(rsp)) {
            log.error("ding get child depart ids response is null");
            return null;
        }
        if (!rsp.isSuccess()) {
            log.error("ding get child depart ids response is fail[{}]", JSONObject.toJSONString(rsp));
            return null;
        }
        log.info("ding get child depart response[{}]", JSONObject.toJSONString(rsp));
        return rsp.getResult().getDeptIdList().stream()
                .filter(v -> v > 0)
                .map(v -> getRemoteOrg(corpId, v)).collect(Collectors.toList());
    }

    private OapiV2DepartmentGetResponse.DeptGetResponse getRemoteOrg(String corpId, Long id) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_DEPARTMENT);
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(id);
        OapiV2DepartmentGetResponse rsp;
        try {
            log.info("ding get depart request[{}]", JSONObject.toJSONString(req));
            rsp = client.execute(req, corpTokenManage.getCorpToken(corpId));
        } catch (Exception e) {
            log.error("ding get depart error", e);
            return null;
        }
        if (Objects.isNull(rsp)) {
            log.error("ding get depart response is null");
            return null;
        }
        if (!rsp.isSuccess()) {
            log.error("ding get depart response is fail[{}]", JSONObject.toJSONString(rsp));
            return null;
        }
        log.info("ding get depart response[{}]", JSONObject.toJSONString(rsp));
        return rsp.getResult();
    }

    private AsOrg toOrg(Long id, Long localRootOrgId, Long companyId, Set<String> remoteIds, OapiV2DepartmentGetResponse.DeptGetResponse remote) {
        AsOrg org = new AsOrg();
        org.setCompanyId(companyId);
        org.setExternalOrgId(String.valueOf(remote.getDeptId()));
        // 当前节点是根节点
        if (DING_ROOT_ORG.equals(String.valueOf(remote.getDeptId()))) {
            org.setExternalPid("0");
        }
        // 当前节点不是根节点 且 其父节点不在本地同步的范围内 直接挂靠在根节点下
        else if (!Constant.DING_ROOT_ORG.equals(String.valueOf(remote.getDeptId())) && Objects.nonNull(remote.getParentId()) && !remoteIds.contains(String.valueOf(remote.getParentId()))) {
            org.setExternalPid(Constant.DING_ROOT_ORG);
        }
        // 普通节点
        else {
            org.setExternalPid(Objects.isNull(remote.getParentId()) ? Constant.DING_ROOT_ORG : String.valueOf(remote.getParentId()));
        }
        org.setSortNum(Objects.isNull(remote.getOrder()) ? 100 : Convert.toInt(remote.getOrder(), 100));
        org.setOrgName(remote.getName());
        org.setCompanyOwner(localRootOrgId);
        org.setExternalDirector(remote.getDeptManagerUseridList());
        org.setId(Objects.isNull(id) ? IdUtils.getId() : id);
        org.setRemark(Objects.isNull(id) ? FOR_CREATE : Constant.FOR_UPDATE);
        org.setOrgType(Constant.DING_ROOT_ORG.equals(String.valueOf(remote.getDeptId())) ? 1 : 2);
        org.setSourceType(1);
        return org;
    }

    private List<AsOrg> handleOrgTree(List<AsOrg> orgs) {
        List<Tree<String>> trees = TreeUtil.build(orgs, "0", (org, treeNode) -> {
            treeNode.setId(org.getExternalOrgId());
            treeNode.setParentId(org.getExternalPid());
            treeNode.setWeight(org.getSortNum());
        });
        Map<String, AsOrg> idMap = orgs.stream().collect(Collectors.toMap(AsOrg::getExternalOrgId, v -> v));
        trees.forEach(node -> treeStructure(idMap, node, new Relation(0, "0,", 0L)));
        return new ArrayList<>(idMap.values());
    }

    private void treeStructure(Map<String, AsOrg> idMap, Tree<String> node, Relation relation) {
        String id = node.getId();
        AsOrg oneself = idMap.get(id);
        oneself.setPid(relation.getPid());
        oneself.setLevel(relation.getLevel());
        oneself.setPaths(relation.getPaths());
        if (oneself.getPid().equals(0L) && (oneself.getLevel().equals(0) || oneself.getLevel().equals(1))) {
            oneself.setOrgType(1);
        } else {
            oneself.setOrgType(2);
        }
        List<Tree<String>> children = node.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            Relation sonRelation = new Relation(relation.getLevel() + 1, relation.getPaths() + oneself.getId() + ",", oneself.getId());
            children.forEach(son -> treeStructure(idMap, son, sonRelation));
        }
    }

    private InnerUser getDingtalkUsers(Long companyId, String corpId, @NotNull Set<String> orgIds, @NotNull List<String> auth) {
        if (CollUtil.isEmpty(orgIds) && CollUtil.isEmpty(auth)) {
            return InnerUser.empty();
        }
        // 遍历获取员工
        int initialCapacity = orgIds.size() * 10;
        Set<String> remoteUserIds = Collections.synchronizedSet(new HashSet<>(initialCapacity));
        Set<String> remoteUnionIds = Collections.synchronizedSet(new HashSet<>(initialCapacity));
        List<OapiV2UserListResponse.ListUserResponse> remoteUsers = Collections.synchronizedList(new ArrayList<>(initialCapacity));
        orgIds.parallelStream().forEach(v -> remoteUsers.addAll(getRemoteUsers(corpId, v, remoteUserIds, remoteUnionIds)));
        // 合并可见范围内的用户
        remoteUsers.addAll(getRemoteAuthUsers(corpId, auth, remoteUserIds, remoteUnionIds));
        if (CollUtil.isEmpty(remoteUsers)) {
            log.warn("ding pull contacts user is empty");
            return InnerUser.empty();
        }
        log.info("ding pull contacts users [{}]", JSONObject.toJSONString(remoteUsers));
        // 自建员工没有这种记录
        List<AsThirdPartyEmployee> partyEmployees = thirdPartyEmployeeService.listByCompanyId(companyId, "DINGTALK");
        // 账号对应多个企业下的员工，这里的查询其实只能判断出本次账号数据的新增与更新，不判断是否需要删除，账号不删除
        List<AsCusUser> localAccounts = accountService.list(
                Wrappers.lambdaQuery(AsCusUser.class)
                        .select(AsCusUser::getId, AsCusUser::getAccount, AsCusUser::getUnionId)
                        .in(AsCusUser::getUnionId, remoteUnionIds)
        );
        Map<String, Long> localUserIdMap = partyEmployees.stream()
                .filter(DeduplicationUtil.distinctByKey(AsThirdPartyEmployee::getUserId))
                .collect(Collectors.toConcurrentMap(AsThirdPartyEmployee::getUserId, AsThirdPartyEmployee::getEmployeeId));
        Map<String, Long> localUnionIdMap = localAccounts.stream()
                .filter(DeduplicationUtil.distinctByKey(AsCusUser::getUnionId))
                .collect(Collectors.toConcurrentMap(AsCusUser::getUnionId, AsCusUser::getId));
        List<AsCusUser> accounts = new ArrayList<>(initialCapacity);
        List<AsCusEmployee> employees = new ArrayList<>(initialCapacity);
        List<UserMapping> mappings = new ArrayList<>(initialCapacity);
        remoteUsers.forEach(v -> {
            AsCusUser account = toAccount(localUnionIdMap.get(v.getUnionid()), companyId, v);
            accounts.add(account);
            AsCusEmployee employee = toUser(localUserIdMap.get(v.getUserid()), companyId, v);
            employees.add(employee);
            UserMapping userMapping = new UserMapping()
                    .setId(employee.getId())
                    .setAccountId(account.getId())
                    .setUserId(v.getUserid())
                    .setOpenUserId(v.getUnionid())
                    .setOrgIds(CollUtil.isEmpty(v.getDeptIdList()) ? Collections.emptyList() : v.getDeptIdList().stream().map(String::valueOf).collect(Collectors.toList()));
            mappings.add(userMapping);
        });
        remoteUsers.clear();
        employees.addAll(partyEmployees.stream()
                .filter(v -> !remoteUserIds.contains(v.getUserId()))
                .map(v -> new AsCusEmployee().setId(v.getEmployeeId()).setCompanyId(v.getCompanyId()).setRemark(FOR_DELETE))
                .collect(Collectors.toList()));
        // empty
        accounts.addAll(localAccounts.stream()
                .filter(v -> !remoteUnionIds.contains(v.getUnionId()))
                .peek(v -> v.setRemark(FOR_DELETE))
                .collect(Collectors.toList()));
        return new InnerUser(accounts, employees, mappings);
    }

    private AsCusUser toAccount(Long id, Long companyId, OapiV2UserListResponse.ListUserResponse remote) {
        AsCusUser account = new AsCusUser();
        account.setAccount(remote.getUserid());
        account.setUnionId(remote.getUnionid());
        account.setMobile(remote.getMobile());
        account.setStatus((short) 1);
        account.setNickname(remote.getName());
        account.setImage(remote.getAvatar());
        account.setSource(1);
        account.setCompanyId(companyId);
        account.setId(Objects.nonNull(id) ? id : IdUtils.getId());
        account.setRemark(Objects.nonNull(id) ? FOR_UPDATE : FOR_CREATE);
        return account;
    }

    private AsCusEmployee toUser(Long id, Long companyId, OapiV2UserListResponse.ListUserResponse remote) {
        AsCusEmployee employee = new AsCusEmployee();
        employee.setEmpName(remote.getName());
        employee.setPosition(remote.getTitle());
        employee.setEmail(remote.getEmail());
        employee.setMobile(remote.getMobile());
        employee.setDataScope(DataPermRuleType.TYPE_ALL.getValue().shortValue());
        employee.setCompanyId(companyId);
        employee.setImage(remote.getAvatar());
        employee.setId(Objects.nonNull(id) ? id : IdUtils.getId());
        employee.setRemark(Objects.nonNull(id) ? FOR_UPDATE : FOR_CREATE);
        if (remote.getAdmin()) {
            employee.setRemark(employee.getRemark() + ":" + DictConstant.DING_ADMIN);
        }
        return employee;
    }

    private List<OapiV2UserListResponse.ListUserResponse> getRemoteUsers(String corpId, String departId, Set<String> remoteUserIds, Set<String> remoteUnionIds) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.USER_LIST);
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(Convert.toLong(departId));
        req.setOrderField("entry_asc");
        req.setCursor(0L);
        req.setSize(100L);
        OapiV2UserListResponse rsp;
        List<OapiV2UserListResponse.ListUserResponse> result = new ArrayList<>(50);
        boolean hasMore = true;
        while (hasMore) {
            try {
                String corpToken = corpTokenManage.getCorpToken(corpId);
                log.info("ding get depart users request[{}]", JSONObject.toJSONString(req));
                rsp = client.execute(req, corpToken);
            } catch (Exception e) {
                log.error("ding get depart users error", e);
                hasMore = false;
                continue;
            }
            if (Objects.isNull(rsp)) {
                log.error("ding get depart users response is null");
                hasMore = false;
                continue;
            }
            if (!rsp.isSuccess()) {
                log.error("ding get depart users response is fail[{}]", JSONObject.toJSONString(rsp));
                hasMore = false;
                continue;
            }
            result.addAll(rsp.getResult().getList());
            hasMore = rsp.getResult().getHasMore();
            req.setCursor(rsp.getResult().getNextCursor());
            log.info("ding get depart users response[{}]", JSONObject.toJSONString(rsp));
        }
        // userId去重
        result.removeIf(v -> remoteUserIds.contains(v.getUserid()));
        remoteUserIds.addAll(result.stream().map(OapiV2UserListResponse.ListUserResponse::getUserid).collect(Collectors.toSet()));
        remoteUnionIds.addAll(result.stream().map(OapiV2UserListResponse.ListUserResponse::getUnionid).collect(Collectors.toSet()));
        return result;
    }

    private List<OapiV2UserListResponse.ListUserResponse> getRemoteAuthUsers(String corpId, List<String> auth, Set<String> remoteUserIds, Set<String> remoteUnionIds) {
        if (CollUtil.isEmpty(auth)) {
            return Collections.emptyList();
        }
        auth.removeIf(remoteUserIds::contains);
        if (CollUtil.isEmpty(auth)) {
            return Collections.emptyList();
        }
        List<OapiV2UserListResponse.ListUserResponse> result = auth.stream().map(v -> getRemoteUser(corpId, v)).filter(Objects::nonNull).map(v -> BeanUtil.copyProperties(v, OapiV2UserListResponse.ListUserResponse.class)).collect(Collectors.toList());
        remoteUserIds.addAll(result.stream().map(OapiV2UserListResponse.ListUserResponse::getUserid).collect(Collectors.toSet()));
        remoteUnionIds.addAll(result.stream().map(OapiV2UserListResponse.ListUserResponse::getUnionid).collect(Collectors.toSet()));
        return result;
    }

    private OapiV2UserGetResponse.UserGetResponse getRemoteUser(String corpId, String userId) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_USER_BY_USER_ID);
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        OapiV2UserGetResponse rsp;
        try {
            String corpToken = corpTokenManage.getCorpToken(corpId);
            rsp = client.execute(req, corpToken);
        } catch (Exception e) {
            log.error("ding get user error", e);
            return null;
        }
        if (Objects.isNull(rsp)) {
            log.error("ding get user response is null");
            return null;
        }
        if (!rsp.isSuccess()) {
            log.error("ding get user response is fail");
            return null;
        }
        return rsp.getResult();
    }

    @Override
    public void reloadDictCache(Long companyId) {
        try {
            executor.execute(() -> {
                orgService.loadOrgCache(orgService.list(new LambdaQueryWrapper<AsOrg>().select(AsOrg::getOrgCode, AsOrg::getOrgName, AsOrg::getId).eq(AsOrg::getCompanyId, companyId)));
                employeeService.loadEmpCache(employeeService.list(new LambdaQueryWrapper<AsCusEmployee>().select(AsCusEmployee::getEmpNo, AsCusEmployee::getEmpName, AsCusEmployee::getId).eq(AsCusEmployee::getCompanyId, companyId)));
            });
        } catch (Exception e) {
            log.error("reloadDictCache error", e);
        }
    }

    @Override
    public Boolean handleDepartEvent(String corpId, JSONObject content) {
        try {
            log.info("ding handleDepartEvent corpId[{}] content[{}]", corpId, content);
            Long companyId = corpService.getCompanyId(corpId);
            if (Objects.isNull(companyId)) {
                log.warn("ding handleDepartEvent company not exist");
                return true;
            }
            if (processing(companyId)) {
                log.warn("ding handleDepartEvent pullContacts processing");
                return true;
            }
            String action = content.getString(EVENT_ACTION);
            switch (action) {
                case ORG_DEPT_CREATE:
                    return createDepartEvent(companyId, content);
                case ORG_DEPT_MODIFY:
                    return updateDepartEvent(companyId, content);
                case ORG_DEPT_REMOVE:
                    return deleteDepartEvent(companyId, content);
                default:
                    log.warn("ding handleDepartEvent action[{}] not match", action);
                    return true;
            }
        } catch (Exception e) {
            log.error("ding handle depart event error", e);
            return true;
        }
    }

    @Override
    public Boolean createDepartEvent(Long companyId, JSONObject content) {
        String id = content.getString(BIZ_ID);
        if (StrUtil.isBlank(id)) {
            log.warn("ding handleCreateDepartEvent content id is empty");
            return false;
        }
        AsOrg org = getOrg(id, companyId);
        if (Objects.nonNull(org)) {
            log.warn("ding handleCreateDepartEvent id[{}] is exist", id);
            return false;
        }
        Long rootOrgId = orgService.getRootOrg(companyId).getId();
        org = toOrg(null, companyId, rootOrgId, content);
        orgService.save(org);
        return true;
    }

    @Override
    public Boolean updateDepartEvent(Long companyId, JSONObject content) {
        String id = content.getString(BIZ_ID);
        if (StrUtil.isBlank(id)) {
            log.warn("ding handleUpdateDepartEvent content id is empty");
            return false;
        }
        AsOrg org = getOrg(id, companyId);
        if (Objects.isNull(org)) {
            log.warn("ding handleUpdateDepartEvent id[{}] not exist", id);
            return createDepartEvent(companyId, content);
        }
        Long rootOrgId = orgService.getRootOrg(companyId).getId();
        // 原来的父节点ID
        Long pid = org.getPid();
        org = toOrg(org.getId(), companyId, rootOrgId, content);
        // 父节点发生变化
        if (!pid.equals(org.getPid())) {
            // 子节点
            String childPath = org.getPaths() + org.getId() + ",";
            List<AsOrg> sonList = orgService.list(new QueryWrapper<AsOrg>().lambda()
                    .eq(AsOrg::getCompanyId, companyId)
                    .likeRight(AsOrg::getPaths, childPath));
            List<AsOrg> updateSonList = new ArrayList<>();
            for (AsOrg son : sonList) {
                String p = son.getPaths().replace(childPath, org.getPaths() + org.getId() + ",");
                int level = p.split(",").length - 1;
                AsOrg updateSon = new AsOrg();
                updateSon.setId(son.getId());
                updateSon.setLevel(level);
                updateSon.setPaths(p);
                updateSonList.add(updateSon);
            }
            // 更新子节点数据
            orgService.updateBatchById(updateSonList);
        }
        orgService.updateById(org);
        return true;
    }

    private LambdaQueryWrapper<AsOrg> getOrgQueryWrapper(String eid, Long companyId) {
        return Wrappers.lambdaQuery(AsOrg.class)
                .eq(AsOrg::getCompanyId, companyId)
                .eq(AsOrg::getExternalOrgId, eid)
                .last(LIMIT);
    }

    private AsOrg getOrg(String eid, Long companyId) {
        return orgService.getOne(
                getOrgQueryWrapper(eid, companyId), false
        );
    }

    private AsOrg toOrg(Long id, Long companyId, Long rootOrgId, JSONObject content) {
        AsOrg org = new AsOrg();
        String eId = content.getString(ID);
        String pid = content.getString(PARENT_ID);
        String name = content.getString(NAME);
        Integer order = content.getInteger(ORDER);
        org.setId(Objects.nonNull(id) ? id : IdUtils.getId());
        // org.setRemark(Objects.nonNull(id) ? FOR_UPDATE : FOR_CREATE);
        org.setCompanyId(companyId);
        org.setOrgName(name);
        org.setSourceType(1);
        org.setSortNum(order);
        org.setExternalOrgId(eId);
        // parent
        AsOrg parent = orgService.getOne(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getExternalOrgId, pid),
                false
        );
        if (Objects.isNull(parent) && Objects.isNull(id)) {
            parent = orgService.getRootOrg(companyId);
        }
        if (Objects.nonNull(parent)) {
            org.setPid(parent.getId());
            org.setPaths(parent.getPaths() + parent.getId() + ",");
            org.setExternalPid(parent.getExternalPid());
            org.setCompanyOwner(rootOrgId);
            org.setLevel(parent.getLevel() + 1);
        }
        // 主管
        List<String> manager = resolveDepartManagers(content);
        Map<String, Long> externalMapping = thirdPartyEmployeeService.getExternalMapping(companyId, manager);
        org.setDirector(manager.stream().filter(externalMapping::containsKey).map(externalMapping::get).collect(Collectors.toList()));
        return org;
    }

    @Override
    public Boolean deleteDepartEvent(Long companyId, JSONObject content) {
        String id = content.getString(BIZ_ID);
        AsOrg org = orgService.getOne(
                Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId)
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getExternalOrgId, id)
                , false
        );
        if (Objects.nonNull(org)) {
            handleOrgDelete(companyId, Lists.newArrayList(org.getId()));
        }
        return true;
    }

    @Override
    public Boolean handleUserEvent(String corpId, JSONObject content) {
        try {
            log.info("ding handleDepartEvent corpId[{}] content[{}]", corpId, content);
            Long companyId = corpService.getCompanyId(corpId);
            if (Objects.isNull(companyId)) {
                log.warn("ding handleDepartEvent companyId not exist");
                return true;
            }
            if (processing(companyId)) {
                log.warn("ding handleDepartEvent pullContacts processing");
                return true;
            }
            String action = content.getString(EVENT_ACTION);
            switch (action) {
                case USER_ADD_ORG:
                    return createUserEvent(companyId, content);
                case USER_MODIFY_ORG:
                case USER_DEPT_CHANGE:
                    return updateUserEvent(companyId, content);
                case USER_LEAVE_ORG:
                    return deleteUserEvent(companyId, content);
                case USER_ROLE_CHANGE:
                    return updateRoleEvent(companyId, content);
                default:
                    log.warn("ding handleUserEvent action[{}] non match", action);
                    return true;
            }
        } catch (Exception e) {
            log.error("ding handle user event error", e);
            return true;
        }
    }

    @Override
    public Boolean createUserEvent(Long companyId, JSONObject content) {
        String userId = content.getString(BIZ_ID);
        if (StrUtil.isBlank(userId)) {
            log.warn("ding handleCreateUserEvent userId is empty");
            return false;
        }
        Optional<AsThirdPartyEmployee> optional = thirdPartyEmployeeService.getOne(DINGTALK, userId, companyId);
        if (optional.isPresent()) {
            log.warn("ding handleCreateUserEvent userId[{}] is exists", userId);
            return true;
        }
        String unionId = content.getString(UNION_ID);
        List<String> depart = resolveUserDeparts(content);
        AsCusEmployee employee = toUser(null, companyId, content);
        AsCusUser account = toAccount(getAccountId(unionId), companyId, content);
        handleEmployeeCreate
                (
                        companyId,
                        Collections.singletonList(employee),
                        Collections.singletonMap(employee.getId(), userId),
                        Collections.singletonMap(employee.getId(), account.getId()),
                        Collections.singletonMap(employee.getId(), depart),
                        getOrgIdMap(companyId, depart)
                );
        handleAccount
                (
                        companyId,
                        Collections.singletonList(account)
                );
        return true;
    }

    @Override
    public Boolean updateUserEvent(Long companyId, JSONObject content) {
        String userId = content.getString(BIZ_ID);
        if (StrUtil.isBlank(userId)) {
            log.warn("ding handleUpdateUserEvent userId is empty");
            return false;
        }
        Optional<AsThirdPartyEmployee> optional = thirdPartyEmployeeService.getOne(DINGTALK, userId, companyId);
        if (!optional.isPresent()) {
            log.warn("ding handleCreateUserEvent userId[{}] is exists", userId);
            return true;
        }
        Long id = optional.get().getEmployeeId();
        List<String> depart = resolveUserDeparts(content);
        AsCusEmployee employee = toUser(id, companyId, content);
        AsCusUser account = toAccount(getAccountId(content.getString(UNION_ID)), companyId, content);
        handleEmployeeUpdate
                (
                        companyId,
                        Collections.singletonList(employee),
                        Collections.singletonMap(id, depart),
                        Collections.singletonMap(id, account.getId()),
                        getOrgIdMap(companyId, depart)
                );
        SpringUtil.getBean(CacheEmpStrategy.class).evictCache(id);
        return true;
    }

    @Override
    public Boolean deleteUserEvent(Long companyId, JSONObject content) {
        String userId = content.getString(BIZ_ID);
        if (StrUtil.isBlank(userId)) {
            log.warn("ding handleDeleteUserEvent userId is empty");
            return false;
        }
        Optional<AsThirdPartyEmployee> optional = thirdPartyEmployeeService.getOne(DINGTALK, userId, companyId);
        if (!optional.isPresent()) {
            log.warn("ding handleDeleteUserEvent userId[{}] is exists", userId);
            return true;
        }
        Long id = optional.get().getEmployeeId();
        handleEmployeeDelete
                (
                        companyId,
                        Collections.singletonList(id)
                );
        SpringUtil.getBean(CacheEmpStrategy.class).evictCache(id);
        return true;
    }

    @Override
    public Boolean updateRoleEvent(Long companyId, JSONObject content) {
        String userId = content.getString(BIZ_ID);
        if (StrUtil.isBlank(userId)) {
            log.warn("ding updateRoleEvent userId is empty");
            return false;
        }
        Optional<AsThirdPartyEmployee> optional = thirdPartyEmployeeService.getOne(DINGTALK, userId, companyId);
        if (!optional.isPresent()) {
            log.warn("ding updateRoleEvent userId[{}] not exists", userId);
            return true;
        }
        Long empId = optional.get().getEmployeeId();
        // 经测试 有返回 roles时代表是添加用户到角色中
        // 经测试 无返回 roles时代表是从角色中移除用户 但是不知道是从那个角色中移除了用户
        String corpId = corpService.getCorpId(companyId);
        OapiV2UserGetResponse.UserGetResponse user = openApiService.getUserByUserId(userId, corpId, null);
        // 无角色时直接返回  因为用户可以在云资产系统中把这个用户加到其他角色中 不一定会与钉钉中的一致，直接删除后会有问题
        if (CollUtil.isEmpty(user.getRoleList())) {
            // 删除所有关联的钉钉侧同步过来的角色
            List<Long> ids = approveRoleService.list(
                    Wrappers.lambdaQuery(ActApproveRole.class)
                            .select(ActApproveRole::getId)
                            .eq(ActApproveRole::getCompanyId, companyId)
                            .isNotNull(ActApproveRole::getExternalId)
            ).stream().map(ActApproveRole::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ids)) {
                approveRoleMemberService.remove(
                        Wrappers.lambdaQuery(ActApproveRoleMember.class)
                                .eq(ActApproveRoleMember::getMemberId, empId)
                                .in(ActApproveRoleMember::getApproveRoleId, ids)
                );
            }
            return true;
        }
        // 有角色删除后重建 钉钉用户当前所在的角色列表
        List<String> roleIds = user.getRoleList().stream().map(userRole -> String.valueOf(userRole.getId())).collect(Collectors.toList());
        List<ActApproveRole> existRoles = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .in(ActApproveRole::getExternalId, roleIds)
        );
        if (CollUtil.isEmpty(roleIds)) {
            return true;
        }
        List<Long> ids = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .select(ActApproveRole::getId)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .isNotNull(ActApproveRole::getExternalId)
        ).stream().map(ActApproveRole::getId).collect(Collectors.toList());

        List<ActApproveRoleMember> roleMembers = existRoles.stream().map(v -> new ActApproveRoleMember().setApproveRoleId(v.getId()).setMemberId(empId).setOrgScope(Collections.emptyList())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ids)) {
            approveRoleMemberService.remove(
                    Wrappers.lambdaUpdate(ActApproveRoleMember.class)
                            .eq(ActApproveRoleMember::getMemberId, empId)
                            .in(ActApproveRoleMember::getApproveRoleId, ids)
            );
        }
        approveRoleMemberService.saveBatch(roleMembers);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEmpNo(EmployeeUpdateDto dto) {
        // 查询当前数据
        AsCusEmployee emp = employeeService.getById(dto.getId());
        if (emp == null) {
            return false;
        }
        if (StrUtil.isNotBlank(dto.getEmpNo())) {
            if (employeeService.count(new QueryWrapper<AsCusEmployee>().lambda()
                    .eq(AsCusEmployee::getEmpNo, dto.getEmpNo())
                    .ne(AsCusEmployee::getId, dto.getId())) > 0) {
                throw new BusinessException(SystemResultCode.PARAM_EXISTS, "员工工号", dto.getEmpNo());
            }
        }
        employeeService.update(Wrappers.lambdaUpdate(AsCusEmployee.class)
                .set(AsCusEmployee::getEmpNo, StrUtil.isNotBlank(dto.getEmpNo()) ? dto.getEmpNo() : "")
                .set(StrUtil.isNotBlank(dto.getRemark()), AsCusEmployee::getRemark, dto.getRemark())
                .eq(AsCusEmployee::getId, dto.getId()));
        SpringUtil.getBean(CacheEmpStrategy.class).evictCache(dto.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrgCode(OrgUpdateDto dto) {
        // 查询当前数据
        AsOrg org = orgService.getById(dto.getId());
        if (org == null) {
            return false;
        }
        if (StrUtil.isNotBlank(dto.getOrgCode())) {
            if (orgService.count(new QueryWrapper<AsOrg>().lambda()
                    .eq(AsOrg::getOrgCode, dto.getOrgCode())
                    .ne(AsOrg::getId, dto.getId())) > 0) {
                throw new BusinessException(SystemResultCode.PARAM_EXISTS, "组织编码", dto.getOrgCode());
            }
        }
        orgService.update(Wrappers.lambdaUpdate(AsOrg.class)
                .set(AsOrg::getOrgCode, StrUtil.isNotBlank(dto.getOrgCode()) ? dto.getOrgCode() : "")
                .eq(AsOrg::getId, dto.getId()));
        SpringUtil.getBean(CacheOrgStrategy.class).evictCache(dto.getId());
        return true;
    }

    private List<String> resolveDepartManagers(JSONObject content) {
        String var = content.getString(MANAGER);
        if (StrUtil.isBlank(var)) {
            return Collections.emptyList();
        }
        return Arrays.stream(var.split("\\|")).distinct().collect(Collectors.toList());
    }

    private List<String> resolveUserDeparts(JSONObject content) {
        if (!content.containsKey(DEPART)) {
            return Collections.emptyList();
        }
        JSONArray var = content.getJSONArray(DEPART);
        if (CollUtil.isEmpty(var)) {
            return Collections.emptyList();
        }
        return var.toJavaList(String.class).stream().distinct().collect(Collectors.toList());
    }

    private Map<String, Long> getOrgIdMap(Long companyId, List<String> depart) {
        if (CollUtil.isEmpty(depart)) {
            return Collections.emptyMap();
        }
        return orgService.list(
                        Wrappers.lambdaQuery(AsOrg.class)
                                .select(AsOrg::getId, AsOrg::getExternalOrgId)
                                .eq(AsOrg::getCompanyId, companyId)
                                .in(AsOrg::getExternalOrgId, depart)
                ).stream()
                .filter(DeduplicationUtil.distinctByKey(AsOrg::getExternalOrgId))
                .collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, AsOrg::getId));
    }

    private Long getAccountId(String unionId) {
        AsCusUser account = accountService.getOne(
                Wrappers.lambdaQuery(AsCusUser.class)
                        .select(AsCusUser::getId)
                        .eq(AsCusUser::getUnionId, unionId)
                        .last(LIMIT), false
        );
        return Objects.nonNull(account) ? account.getId() : null;
    }

    private AsCusEmployee toUser(Long id, Long companyId, JSONObject content) {
        String name = content.getString(NAME);
        String position = content.getString(POSITION);
        String images = content.getString(AVATAR);
        AsCusEmployee employee = new AsCusEmployee();
        employee.setId(Objects.nonNull(id) ? id : IdUtils.getId());
        employee.setCompanyId(companyId);
        employee.setImage(images);
        employee.setEmpName(name);
        employee.setPosition(position);
        employee.setDataScope(DataPermRuleType.TYPE_ALL.getValue().shortValue());
        employee.setRemark(Objects.nonNull(id) ? FOR_UPDATE : FOR_CREATE);
        return employee;
    }

    private AsCusUser toAccount(Long id, Long companyId, JSONObject content) {
        AsCusUser account = new AsCusUser();
        account.setId(Objects.nonNull(id) ? id : IdUtils.getId());
        account.setCompanyId(companyId);
        account.setAccount(content.getString(USER_ID));
        account.setUnionId(content.getString(UNION_ID));
        account.setStatus((short) 1);
        account.setNickname(content.getString(NAME));
        account.setImage(content.getString(AVATAR));
        account.setSource(1);
        account.setRemark(Objects.nonNull(id) ? FOR_UPDATE : FOR_CREATE);
        return account;
    }
}
