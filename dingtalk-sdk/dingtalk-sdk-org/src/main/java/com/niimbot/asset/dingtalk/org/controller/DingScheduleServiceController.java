package com.niimbot.asset.dingtalk.org.controller;

import com.niimbot.asset.dingtalk.org.scheduler.BillScheduler;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/dingtalk/schedule")
@RequiredArgsConstructor
public class DingScheduleServiceController {

    private final Environment environment;

    private final BillScheduler billScheduler;

    @PostMapping("/bill")
    public Boolean billSchedule() {
        if (Arrays.asList(environment.getActiveProfiles()).contains("dingtalk")) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "生成环境不允许执行");
        }
        billScheduler.calculateBill();
        return true;
    }

}
