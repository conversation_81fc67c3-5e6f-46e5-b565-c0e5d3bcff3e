package com.niimbot.asset.dingtalk.org.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dingtalk.base.dto.EmployeeUpdateDto;
import com.niimbot.asset.dingtalk.base.dto.OrgUpdateDto;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * 钉钉通讯录服务
 *
 * <AUTHOR>
 */
public interface DingtalkContactsService {

    /**
     * 是否正在进行中
     *
     * @param companyId 企业ID
     * @return ture
     */
    Boolean processing(Long companyId);

    /**
     * 拉取通讯录
     *
     * @param companyId 企业ID
     */
    void pull(Long companyId);

    /**
     * 异步拉取通讯录
     *
     * @param companyId 企业ID
     */
    void background(Long companyId);

    /**
     * 重新加载字典项缓存
     *
     * @param companyId 企业ID
     */
    void reloadDictCache(Long companyId);

    /**
     * 处理通讯录部门事件
     *
     * @param corpId  企业ID
     * @param content 事件内容
     * @return true if success
     */
    Boolean handleDepartEvent(String corpId, JSONObject content);

    /**
     * 新增部门事件
     *
     * @param companyId 企业ID
     * @param content   事件内容
     * @return true if success
     */
    Boolean createDepartEvent(Long companyId, JSONObject content);

    /**
     * 更新部门事件
     *
     * @param companyId 企业ID
     * @param content   事件内容
     * @return true if success
     */
    Boolean updateDepartEvent(Long companyId, JSONObject content);

    /**
     * 删除部门事件
     *
     * @param companyId 企业ID
     * @param content   事件内容
     * @return true if success
     */
    Boolean deleteDepartEvent(Long companyId, JSONObject content);

    /**
     * 处理用户事件
     *
     * @param corpId  企业ID
     * @param content 事件内容
     * @return true if success
     */
    Boolean handleUserEvent(String corpId, JSONObject content);

    /**
     * 新增员工事件
     *
     * @param companyId 企业ID
     * @param content   事件内容
     * @return true if success
     */
    Boolean createUserEvent(Long companyId, JSONObject content);

    /**
     * 更新员工事件
     *
     * @param companyId 企业ID
     * @param content   事件内容
     * @return true if success
     */
    Boolean updateUserEvent(Long companyId, JSONObject content);

    /**
     * 删除员工事件
     *
     * @param companyId 企业ID
     * @param content   事件内容
     * @return true if success
     */
    Boolean deleteUserEvent(Long companyId, JSONObject content);

    /**
     * 更新员工角色事件
     *
     * @param companyId 企业ID
     * @param content   事件内容
     * @return true if success
     */
    Boolean updateRoleEvent(Long companyId, JSONObject content);

    /**
     * 更新工号
     *
     * @param dto dto
     * @return true if success
     */
    Boolean updateEmpNo(EmployeeUpdateDto dto);

    /**
     * 更新编码
     *
     * @param dto dto
     * @return true if success
     */
    Boolean updateOrgCode(OrgUpdateDto dto);

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Relation {
        Integer level;
        String paths;
        Long pid;
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    class UserMapping {
        Long id;
        Long accountId;
        String userId;
        String openUserId;
        List<String> orgIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class InnerUser {
        List<AsCusUser> accounts;
        List<AsCusEmployee> employees;
        List<UserMapping> userMap;

        public static InnerUser empty() {
            InnerUser innerUser = new InnerUser();
            innerUser.setAccounts(Collections.emptyList());
            innerUser.setEmployees(Collections.emptyList());
            innerUser.setUserMap(Collections.emptyList());
            return innerUser;
        }
    }

    interface Constant {
        String DINGTALK = "DINGTALK";
        String FOR_CREATE = "for_create";
        String FOR_UPDATE = "for_update";
        String FOR_DELETE = "for_delete";
        String PULL_LOCK_KEY = "ding:pull:contacts:%d";
        String DING_ROOT_ORG = "1";
        String EVENT_ACTION = "syncAction";
        String ORG_DEPT_CREATE = "org_dept_create";
        String ORG_DEPT_MODIFY = "org_dept_modify";
        String ORG_DEPT_REMOVE = "org_dept_remove";
        String USER_ADD_ORG = "user_add_org";
        String USER_MODIFY_ORG = "user_modify_org";
        String USER_DEPT_CHANGE = "user_dept_change";
        String USER_ACTIVE_ORG = "user_active_org";
        String USER_LEAVE_ORG = "user_leave_org";
        String USER_ROLE_CHANGE = "user_role_change";
        String ID = "id";
        String PARENT_ID = "parentid";
        String NAME = "name";
        String ORDER = "order";
        String BIZ_ID = "bizId";
        String MANAGER = "deptManagerUseridList";
        String USER_ID = "userid";
        String UNION_ID = "unionid";
        String POSITION = "position";
        String DEPART = "department";
        String AVATAR = "avatar";
        String LIMIT = "LIMIT 1";
    }
}
