package com.niimbot.asset.dingtalk.org.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dingtalk.base.model.OpenSyncBizData;
import com.niimbot.asset.dingtalk.org.mapper.OpenSyncBizDataMapper;
import com.niimbot.asset.dingtalk.org.service.OpenSyncBizDataService;
import org.springframework.stereotype.Service;

/**
 * created by chen.y on 2021/8/23 15:14
 */
@Service
public class OpenSyncBizDataServiceImpl extends ServiceImpl<OpenSyncBizDataMapper, OpenSyncBizData> implements OpenSyncBizDataService {
}
