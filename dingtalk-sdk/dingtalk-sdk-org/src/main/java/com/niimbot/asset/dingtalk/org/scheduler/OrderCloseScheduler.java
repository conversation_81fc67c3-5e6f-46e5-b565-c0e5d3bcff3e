package com.niimbot.asset.dingtalk.org.scheduler;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.sale.model.AsSaleOrder;
import com.niimbot.asset.sale.service.AsSaleOrderService;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 待支付订单超过7天，关闭订单
 * <AUTHOR>
 * @date 2023/4/12 上午9:26
 */
@Slf4j
@Component
public class OrderCloseScheduler {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AsSaleOrderService saleOrderService;

    @Scheduled(cron = "0 0 */1 * * ?")
    @Async("asyncScheduledThreadPool")
    public void closeOrder() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        //获取分布式锁，这里主要是控制任务的并发，仅允许一台机器跑定时任务
        RLock lock = redissonClient.getLock("s-asset-platform:ding:closeOrder");
        boolean isLocked;
        try {
            isLocked = lock.tryLock(3, TimeUnit.MINUTES);
            if (!isLocked) {
                log.info("orderCloseScheduler closeOrder end! Acquire lock fail! currentTime=[{}]", System.currentTimeMillis());
                return;
            }

            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime startTime = currentTime.plusDays(-8);
            LocalDateTime endTime = currentTime.plusDays(-7);

            //查询超过7天待支付订单
            List<AsSaleOrder> waitPayOrderList = saleOrderService.list(Wrappers.lambdaQuery(AsSaleOrder.class)
                    .between(AsSaleOrder::getCreateTime, startTime, endTime)
                    .eq(AsSaleOrder::getStatus, 1));
            if (CollUtil.isEmpty(waitPayOrderList)) {
                return ;
            }
            List<Long> orderIdList = waitPayOrderList.stream().map(AsSaleOrder::getId).collect(Collectors.toList());
            AsSaleOrder modifyParam = new AsSaleOrder();
            modifyParam.setStatus(3);
            modifyParam.setRemark("订单待支付超时7天，后台自动关闭");
            //关闭订单
            saleOrderService.update(Wrappers.lambdaUpdate(AsSaleOrder.class)
                    .set(AsSaleOrder::getStatus, 3)
                    .set(AsSaleOrder::getRemark, "订单待支付超时7天，后台自动关闭")
                    .in(AsSaleOrder::getId, orderIdList));
        } catch (Exception e) {
            log.error("orderCloseScheduler closeOrder acquire lock error! exception ", e);
        } finally {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
