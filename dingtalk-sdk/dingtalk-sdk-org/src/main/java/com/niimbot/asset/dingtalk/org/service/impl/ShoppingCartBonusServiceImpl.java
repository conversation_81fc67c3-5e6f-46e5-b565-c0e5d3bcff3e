package com.niimbot.asset.dingtalk.org.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dingtalk.base.dto.ShoppingCartBonusDto;
import com.niimbot.asset.dingtalk.base.model.AsShoppingCartBonus;
import com.niimbot.asset.dingtalk.org.mapper.AsShoppingCartBonusMapper;
import com.niimbot.asset.dingtalk.org.service.ShoppingCartBonusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import cn.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:08
 */
@Slf4j
@Service
public class ShoppingCartBonusServiceImpl extends ServiceImpl<AsShoppingCartBonusMapper, AsShoppingCartBonus> implements ShoppingCartBonusService {

    @Override
    public List<ShoppingCartBonusDto> queryByMainId(List<Long> mainId) {
        if (CollUtil.isEmpty(mainId)) {
            return null;
        }

        return this.getBaseMapper().selectByMainProductId(mainId);
    }
}
