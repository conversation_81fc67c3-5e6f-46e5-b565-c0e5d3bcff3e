package com.niimbot.asset.dingtalk.workflow.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DingWorkflowEvent implements Serializable {

    private Long orderId;

    private String orderType;

    private Integer approveStatus;

    private Long startUserId;
}
