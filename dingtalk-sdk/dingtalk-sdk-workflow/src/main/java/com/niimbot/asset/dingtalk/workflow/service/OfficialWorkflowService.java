package com.niimbot.asset.dingtalk.workflow.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.FormComponent;
import com.aliyun.dingtalkworkflow_1_0.models.FormComponentProps;
import com.aliyun.dingtalkworkflow_1_0.models.FormCreateHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.FormCreateRequest;
import com.aliyun.dingtalkworkflow_1_0.models.FormCreateResponse;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.aliyun.dingtalkworkflow_1_0.models.ListUserVisibleBpmsProcessesHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.ListUserVisibleBpmsProcessesRequest;
import com.aliyun.dingtalkworkflow_1_0.models.ListUserVisibleBpmsProcessesResponse;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastRequest;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastResponse;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastResponseBody;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.TerminateProcessInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.TerminateProcessInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.TerminateProcessInstanceResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflow;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflowActor;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflowFormInfo;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflowNode;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflowUrl;
import com.niimbot.asset.dingtalk.base.dto.EmpInfo;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingOrderType;
import com.niimbot.asset.dingtalk.base.model.DingWorkflowBusiness;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.dingtalk.workflow.event.DingWorkflowEvent;
import com.niimbot.asset.dingtalk.workflow.model.DingProcInstMapping;
import com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant;
import com.niimbot.asset.dingtalk.workflow.model.WorkflowField;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

import static com.niimbot.asset.dingtalk.base.restops.RestOps.handleOrThrow;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Cache.WORKFLOW_NODE;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.AGREE;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.ALL_STAFF;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.APPROVALS;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.APPROVER;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.COMPLETED;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.FORM_VALUES_OMITTED;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.ISV_BPMS_CANCEL;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.LABELS;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.OPERATION_RECORDS;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.PROCESS_INSTANCE_ID;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.REFUSE;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.RESULT;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.STATUS;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.SYNC_ACTION;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.TARGET_SELECT;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.TERMINATE_PROCESS_INSTANCE;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.TYPE;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Vale.URL;

/**
 * 官方OA审批
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OfficialWorkflowService extends WorkflowService {

    private final AsFormService formService;

    private final DingOpenApiService dingOpenApiService;

    private final List<DingWorkflowEventHandler> eventHandlers;

    private final RedisDistributeLock redisDistributeLock;

    /**
     * 创建或更新钉钉流程表单
     *
     * @param dingWorkflow 企业ID,单据类型
     * @return 生成企业内唯一的审批模板编码，可使用此processCode发起表单实例。
     * @see <a href="https://open.dingtalk.com/document/orgapp-server/create-an-approval-form-template">钉钉文档</a>
     */
    public String createOrModifyWorkflowForm(DingWorkflow dingWorkflow) {
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        DingOrderType dingOrderType = dingOrderTypeMapper.selectByIdAndWay(dingWorkflow.getOrderTypeId(), 1);
        // 1.云资产单据字段转换成钉钉表单中间值
        List<WorkflowField> workflowFields = getOrderWorkflowFields(dingWorkflow);
        // 2.生成创建表单请求体
        List<FormComponent> components = workflowFields.stream().map(WorkflowField::convertToComponent).collect(Collectors.toList());
        components.add(getChildrenForm(dingWorkflow));
        FormCreateRequest.FormCreateRequestTemplateConfig templateConfig = new FormCreateRequest.FormCreateRequestTemplateConfig()
                .setDisableFormEdit(true).setHidden(false).setDisableStopProcessButton(true).setDisableDeleteProcess(false).setDisableResubmit(true).setDisableHomepage(false);
        FormCreateRequest request = new FormCreateRequest()
                .setProcessCode(Objects.nonNull(dingOrderType) ? dingOrderType.getProcessCode() : null)
                .setFormComponents(components)
                .setName(dingWorkflow.getFormName())
                .setDescription(dingWorkflow.getFormDesc())
                .setTemplateConfig(templateConfig);
        if (ASSET.equals(dingWorkflow.getBizType()) || PRODUCT.equals(dingWorkflow.getBizType())) {
            request.setName(dingWorkflow.getFormName() + NAME);
        }
        if (MATERIAL.equals(dingWorkflow.getBizType())) {
            request.setName(dingWorkflow.getFormName() + NAME);
        }
        // 升级为OA审批状态
        if (dingWorkflow.getUpgrade()) {
            request.getTemplateConfig().setDisableDeleteProcess(true).setDisableHomepage(true).setHidden(true);
            request.setName(request.getName() + "（旧）");
        }
        // 3.请求创建表单
        FormCreateResponse response = handleOrThrow(() -> workflowClient.formCreateWithOptions(request, FormCreateHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        String processCode = response.getBody().getResult().getProcessCode();
        // 4.保存企业钉钉单据模板编码
        if (Objects.isNull(dingOrderType)) {
            dingOrderTypeMapper.insert(new DingOrderType(dingWorkflow.getOrderTypeId(), dingWorkflow.getCompanyId(), processCode).setWay(1));
        }
        return processCode;
    }

    /**
     * 获取企业内或指定成员全部可见表单列表
     *
     * @param dingWorkflow userId
     * @return object
     */
    public Object getWorkflowForm(DingWorkflow dingWorkflow) {
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        ListUserVisibleBpmsProcessesRequest request = new ListUserVisibleBpmsProcessesRequest();
        request.setMaxResults(100L).setNextToken(0L);
        if (Objects.nonNull(dingWorkflow.getEmpId())) {
            request.setUserId(adapterMapper.selectDingUserIdByAssetEmpId(dingWorkflow.getEmpId()));
        }
        ListUserVisibleBpmsProcessesResponse response = handleOrThrow(() -> workflowClient.listUserVisibleBpmsProcessesWithOptions(request, ListUserVisibleBpmsProcessesHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        return response.getBody().getResult();
    }

    /**
     * 获取钉钉审批节点信息
     *
     * @param dingWorkflow 单据类型ID，员工ID、员工部门ID
     * @return 审批节点信息
     * @see <a href="https://open.dingtalk.com/document/orgapp-server/approval-process-prediction">钉钉文档</a>
     */
    public ProcessForecastResponseBody getWorkflowNodes(DingWorkflow dingWorkflow) {
        DingFormMapping formMapping = getDingFormMapping(dingWorkflow.setWay(1));
        DingUserInfo dingUserInfo = getDingUserInfo(dingWorkflow.getEmpId(), dingWorkflow.getDepId());
        List<WorkflowField> workflowFields = getOrderWorkflowFields(dingWorkflow);
        List<ProcessForecastRequest.ProcessForecastRequestFormComponentValues> values = workflowFields.stream().map(v -> v.processForecast(dingWorkflow.getOrderData())).collect(Collectors.toList());
        values.add(getChildrenFromValueForGetNode(dingWorkflow));
        ProcessForecastRequest request = new ProcessForecastRequest()
                .setProcessCode(formMapping.getProcessCode())
                .setDeptId(dingUserInfo.getDepId())
                .setUserId(dingUserInfo.getUserId())
                .setFormComponentValues(values);
        ProcessForecastResponse response = handleOrThrow(() -> workflowClient.processForecastWithOptions(request, ProcessForecastHeaders.build(headers(formMapping.getCorpId())), new RuntimeOptions()));
        ProcessForecastResponseBody.ProcessForecastResponseBodyResult result = response.getBody().getResult();
        // 预测成功时保存流程节点信息缓存
        if (!result.getIsForecastSuccess()) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "钉钉审批流程预测失败，请稍后再试");
        }
        return response.getBody();
    }

    private void writeWorkflowNodeCache(String uid, ProcessForecastResponseBody.ProcessForecastResponseBodyResult result) {
        try {
            redisService.set(WORKFLOW_NODE + uid, JSONObject.toJSONString(result), 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("钉钉审批流程节点预测信息写入缓存失败", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "钉钉审批流程预测失败，请稍后再试");
        }
    }

    private ProcessForecastResponseBody.ProcessForecastResponseBodyResult readWorkflowNodeCache(String uid) {
        if (!redisService.hasKey(WORKFLOW_NODE + uid)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "钉钉审批流程预测信息失效，请重新提交");
        }
        Object json = redisService.get(WORKFLOW_NODE + uid);
        return JSONObject.parseObject(String.valueOf(json), ProcessForecastResponseBody.ProcessForecastResponseBodyResult.class);
    }

    /**
     * 获取钉钉审批节点信息并解析自选节点
     *
     * @param dingWorkflow dingWorkflow
     * @return 审批节点信息
     */
    public DingWorkflowNode getResolveWorkflowNode(DingWorkflow dingWorkflow) {
        ProcessForecastResponseBody response = getWorkflowNodes(dingWorkflow);
        String uid = IdUtil.fastSimpleUUID();
        writeWorkflowNodeCache(uid, response.getResult());
        List<ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules> workflowActivityRules = response.getResult().getWorkflowActivityRules();
        // 过滤掉办理人自选和抄送人自选的节点 钉钉接口暂不支持
        List<ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules> activityRules = workflowActivityRules.stream().filter(rules -> TARGET_SELECT.equalsIgnoreCase(rules.getActivityType()) && rules.getIsTargetSelect() && !"audit".equalsIgnoreCase(rules.getWorkflowActor().getActorType()) && !"notifier".equalsIgnoreCase(rules.getWorkflowActor().getActorType())).collect(Collectors.toList());
        DingWorkflowNode dingWorkflowNode = new DingWorkflowNode().setResult(response.getResult()).setUid(uid);
        if (CollUtil.isEmpty(activityRules)) {
            return dingWorkflowNode.setResult(null);
        }
        DingWorkflowNode node = new DingWorkflowNode().setResult(response.getResult());
        Map<String, DingWorkflowNode.Actor> userMap = new HashMap<>();
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        activityRules.forEach(v -> {
            ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRulesWorkflowActor actor = v.getWorkflowActor();
            String actorSelectionType = actor.getActorSelectionType();
            if (APPROVER.equalsIgnoreCase(actor.getActorType()) && StrUtil.isBlank(actor.getActorKey())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先至钉钉后台保存并发布对应单据的审批流程");
            }
            if (StrUtil.isBlank(actorSelectionType)) {
                return;
            }
            ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRulesWorkflowActorActorSelectionRange range = actor.getActorSelectionRange();
            // 全公司不处理
            if (actorSelectionType.equals(ALL_STAFF)) {
                return;
            }
            // 指定范围
            if (actorSelectionType.equals(APPROVALS)) {
                Set<String> userIds = range.getApprovals().stream().map(ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRulesWorkflowActorActorSelectionRangeApprovals::getWorkNo).collect(Collectors.toSet());
                List<EmpInfo> empInfos = convertAssetEmp(userIds, dingCorp.getCompanyId());
                userMap.put(v.getActivityId(), new DingWorkflowNode.Actor(actor.getActorKey(), empInfos));
            }
            // 指定角色
            if (actorSelectionType.equals(LABELS)) {
                List<String> roleIds = range.getLabels().stream().map(ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRulesWorkflowActorActorSelectionRangeLabels::getLabels).collect(Collectors.toList());
                Set<String> userIds = roleIds.stream().map(s -> dingOpenApiService.getUserIdsByRole(s, corpTokenManage.getCorpToken(dingCorp.getCorpId()))).flatMap((Function<Set<String>, Stream<String>>) Collection::stream).collect(Collectors.toSet());
                List<EmpInfo> empInfos = convertAssetEmp(userIds, dingCorp.getCompanyId());
                userMap.put(v.getActivityId(), new DingWorkflowNode.Actor(actor.getActorKey(), empInfos));
            }
        });
        ProcessForecastResponseBody.ProcessForecastResponseBodyResult result = response.getResult();
        result.setWorkflowActivityRules(activityRules);
        node.setResult(result);
        node.setUserMap(userMap);
        return dingWorkflowNode.setUserMap(userMap);
    }

    /**
     * 发起钉钉审批实例
     *
     * @param dingWorkflow 企业ID
     * @return instanceId   审批实例id
     * @see <a href="https://open.dingtalk.com/document/orgapp-server/create-an-approval-instance">钉钉文档</a>
     */
    public String startProcessInstance(DingWorkflow dingWorkflow) {
        ProcessForecastResponseBody.ProcessForecastResponseBodyResult forecast = readWorkflowNodeCache(dingWorkflow.getForecastUid());
        DingFormMapping formMapping = getDingFormMapping(dingWorkflow.setWay(1));
        DingUserInfo dingUserInfo = getDingUserInfo(dingWorkflow.getEmpId(), dingWorkflow.getDepId());
        List<WorkflowField> workflowFields = getOrderWorkflowFields(dingWorkflow);
        List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues> values = workflowFields.stream().map(v -> v.startProcessInstance(dingWorkflow.getOrderData())).collect(Collectors.toList());
        values.add(getChildrenFromValueForStartInst(dingWorkflow));
        StartProcessInstanceRequest request = new StartProcessInstanceRequest()
                .setProcessCode(formMapping.getProcessCode())
                .setOriginatorUserId(dingUserInfo.getUserId())
                .setDeptId(Convert.toLong(dingUserInfo.getDepId()))
                .setMicroappAgentId(formMapping.getAgentId())
                .setFormComponentValues(values);
        // 指定审批人列表与抄送人列表
        if (CollUtil.isNotEmpty(dingWorkflow.getActors())) {
            List<DingWorkflowActor> approver = dingWorkflow.getActors().stream().filter(v -> "approver".equalsIgnoreCase(v.getActorType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(approver)) {
                List<StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners> targetSelectActioners = approver.stream().map(dingWorkflowActor -> new StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners().setActionerKey(dingWorkflowActor.getActorKey()).setActionerUserIds(convertDingUserIds(dingWorkflowActor.getEmpIds()))).collect(Collectors.toList());
                request.setTargetSelectActioners(targetSelectActioners);
            }
            List<DingWorkflowActor> notifier = dingWorkflow.getActors().stream().filter(v -> "notifier".equalsIgnoreCase(v.getActorType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notifier)) {
                List<Long> empIds = notifier.stream().map(DingWorkflowActor::getEmpIds).flatMap((Function<List<Long>, Stream<Long>>) Collection::stream).collect(Collectors.toList());
                List<String> dingUserIds = convertDingUserIds(empIds);
                request.setCcList(dingUserIds.stream().distinct().collect(Collectors.toList()));
                request.setCcPosition("FINISH");
            }
        }
        // 自选节点指定审批人列表
        if (CollUtil.isNotEmpty(dingWorkflow.getApprovers())) {
            List<StartProcessInstanceRequest.StartProcessInstanceRequestApprovers> approvers = dingWorkflow.getApprovers().stream().map(dingWorkflowActor -> new StartProcessInstanceRequest.StartProcessInstanceRequestApprovers().setActionType(dingWorkflowActor.getActionType()).setUserIds(convertDingUserIds(dingWorkflowActor.getEmpIds()))).collect(Collectors.toList());
            request.setApprovers(approvers);
        }
        StartProcessInstanceResponse response = handleOrThrow(() -> workflowClient.startProcessInstanceWithOptions(request, StartProcessInstanceHeaders.build(headers(formMapping.getCorpId())), new RuntimeOptions()));
        String instanceId = response.getBody().getInstanceId();
        // 保存
        DingWorkflowBusiness business = new DingWorkflowBusiness(instanceId, dingWorkflow.getCompanyId(), dingWorkflow.getCompanyId(), 1)
                .setBizId(dingWorkflow.getOrderId()).setBizType(dingWorkflow.getBizType()).setOrderType(dingWorkflow.getOrderType())
                .setNode((JSONObject) JSONObject.toJSON(forecast)).setWay(1);
        try {
            Object details = getProcessInstance(dingWorkflow.setProInstId(instanceId));
            business.setDetails((JSONObject) JSONObject.toJSON(details));
        } catch (Exception e) {
            log.error("发起审批后立即查询流程实例异常", e);
        }
        dingWorkflowBusinessMapper.insert(business);
        return instanceId;
    }

    /**
     * 撤销审批实例
     *
     * @param dingWorkflow 单据ID
     * @return true or false
     * @see <a href="https://open.dingtalk.com/document/orgapp-server/revoke-an-approval-instance">钉钉文档</a>
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean revocationProcessInstance(DingWorkflow dingWorkflow) {
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        if (StrUtil.isBlank(dingWorkflow.getProInstId()) && StrUtil.isNotBlank(dingWorkflow.getBizType()) && Objects.nonNull(dingWorkflow.getOrderId())) {
            DingProcInstMapping procInstMapping = getDingProcInstMapping(dingWorkflow);
            dingWorkflow.setProInstId(procInstMapping.getProcInsId());
            Duration between = Duration.between(procInstMapping.getCreateTime(), LocalDateTime.now());
            if (between.toMillis() < DURATION) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "20秒内不允许撤回 ");
            }
        }
        TerminateProcessInstanceRequest request = new TerminateProcessInstanceRequest()
                .setProcessInstanceId(dingWorkflow.getProInstId())
                .setRemark(dingWorkflow.getRemark())
                .setIsSystem(false)
                .setOperatingUserId(adapterMapper.selectDingUserIdByEmpIdOrElseThrow(dingWorkflow.getEmpId()));
        // 先处理业务数据：业务数据处理异常时，不向钉钉请求。
        // 先处理业务数据：防止请求钉钉撤销后，钉钉回调接口执行速度会快于此方法。导致业务数据被重复处理
        DingWorkflowBusiness business = dingWorkflowBusinessMapper.selectByProcInstId(dingWorkflow.getProInstId());
        business.setApproveStatus(4);
        dingWorkflowBusinessMapper.updateById(business);
        DingWorkflowEvent event = new DingWorkflowEvent(business.getBizId(), business.getOrderType(), business.getApproveStatus(), business.getCreateBy());
        bizEventHandler(event);
        TerminateProcessInstanceResponse response = handleOrThrow(() -> workflowClient.terminateProcessInstanceWithOptions(request, TerminateProcessInstanceHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        Boolean result = response.getBody().getResult();
        // 后向钉钉请求撤销审批，撤销失败时抛出异常回滚业务数据
        if (!result) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "钉钉审批撤销失败，请稍后再试");
        }
        return true;
    }

    /**
     * 获取钉钉流程实例
     *
     * @param dingWorkflow 流程实例ID
     * @return 钉钉流程实例
     * @see <a href="https://open.dingtalk.com/document/orgapp-server/obtains-the-details-of-a-single-approval-instance-pop">钉钉文档</a>
     */
    public GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult getProcessInstance(DingWorkflow dingWorkflow) {
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        GetProcessInstanceRequest request = new GetProcessInstanceRequest().setProcessInstanceId(dingWorkflow.getProInstId());
        GetProcessInstanceResponse response = handleOrThrow(() -> workflowClient.getProcessInstanceWithOptions(request, GetProcessInstanceHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        return response.getBody().getResult();
    }

    /**
     * 获取钉钉审批流详情链接
     *
     * @param orderId 单据ID
     * @return url
     */
    public DingWorkflowUrl getWorkflowInstanceUrl(Long orderId) {
        DingWorkflowBusiness business = dingWorkflowBusinessMapper.selectOne(
                Wrappers.lambdaQuery(DingWorkflowBusiness.class)
                        .select(DingWorkflowBusiness::getBizId, DingWorkflowBusiness::getProcInstId, DingWorkflowBusiness::getUrl)
                        .eq(DingWorkflowBusiness::getBizId, orderId)
                        .eq(DingWorkflowBusiness::getWay, 1)
        );
        if (Objects.isNull(business)) {
            log.warn("单据[{}]没有对应的审批实例", orderId);
            return new DingWorkflowUrl();
        }
        return new DingWorkflowUrl(business.getUrl());
    }

    /**
     * 审批事件推送
     *
     * @param event 事件
     * @return true or false
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean workflowEventHandler(JSONObject event) {
        if (!event.containsKey(PROCESS_INSTANCE_ID)) {
            log.error("审批事件中没有实例ID");
            return true;
        }
        String procInstId = event.getString(PROCESS_INSTANCE_ID);
        DingWorkflowBusiness business = dingWorkflowBusinessMapper.selectByProcInstId(procInstId);
        if (event.containsKey(URL)) {
            String url = event.getString(URL);
            business.setUrl(url);
        }
        if (event.containsKey(STATUS)) {
            String procInstStatus = event.getString(STATUS);
            business.setInstStatus(procInstStatus);
        }
        int newApproveStatus = 0;
        int oldApproveStatus = business.getApproveStatus();
        if (event.containsKey(RESULT) && StrUtil.isNotBlank(business.getInstStatus()) && business.getInstStatus().equalsIgnoreCase(COMPLETED)) {
            String result = event.getString(RESULT);
            if (AGREE.equalsIgnoreCase(result)) {
                business.setApproveStatus(3);
                newApproveStatus = 3;
            }
            if (REFUSE.equalsIgnoreCase(result)) {
                business.setApproveStatus(2);
                newApproveStatus = 2;
            }
        }
        // 审批撤销
        if (event.containsKey(SYNC_ACTION)) {
            String action = event.getString(SYNC_ACTION);
            if (ISV_BPMS_CANCEL.equalsIgnoreCase(action)) {
                business.setApproveStatus(4);
                newApproveStatus = 4;
            }
        }
        Set<String> types = event.getJSONArray(OPERATION_RECORDS).stream().map(o -> ((JSONObject) JSONObject.toJSON(o)).getString(TYPE)).collect(Collectors.toSet());
        if (types.contains(TERMINATE_PROCESS_INSTANCE)) {
            newApproveStatus = 4;
        }
        // 表单数据被隐藏了
        if (event.containsKey(FORM_VALUES_OMITTED)) {
            event = (JSONObject) JSONObject.toJSON(getProcessInstance(new DingWorkflow().setCompanyId(business.getCompanyId()).setProInstId(procInstId)));
        }
        business.setDetails(event);
        dingWorkflowBusinessMapper.updateById(business);
        // 流程状态未变更或状态已处理
        if (newApproveStatus == 0 || newApproveStatus == oldApproveStatus) {
            return true;
        }
        DingWorkflowEvent dingWorkflowEvent = new DingWorkflowEvent().setOrderId(business.getBizId()).setOrderType(business.getOrderType()).setApproveStatus(newApproveStatus).setStartUserId(business.getCreateBy());
        bizEventHandler(dingWorkflowEvent);
        return true;
    }

    /**
     * 业务数据事件处理
     *
     * @param event 事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void bizEventHandler(DingWorkflowEvent event) {
        // 业务数据回调更新
        eventHandlers.forEach(handler -> {
            Set<String> orderTypes = handler.orderTypes();
            if (!orderTypes.contains(event.getOrderType())) {
                return;
            }
            Boolean handle = handler.handle(event);
            if (!handle) {
                log.error("钉钉审批回调业务单据信息处理失败 bizId : [{}]", event.getOrderId());
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean upgradeVersion(Long companyId) {
        // 标记为自有OA审批
        adapterMapper.updateWorkflowVersion(companyId);
        // 处理单据设置状态
        adapterMapper.updateOrderTypeStatus("as_order_type", companyId);
        adapterMapper.updateOrderTypeStatus("as_material_order_type", companyId);
        adapterMapper.updateOrderTypeStatus("as_purchase_order_type", companyId);
        // 修改所有表单名称并隐藏
        List<DingWorkflowFormInfo> form = dingOrderTypeMapper.selectCompanyOldForm(companyId);
        List<DingWorkflow> dingWorkflows = new ArrayList<>();
        for (DingWorkflowFormInfo v : form) {
            DingWorkflow dingWorkflow = DingWorkflow.buildForForm(companyId, v.getBizType(), v.getOrderTypeId(), String.valueOf(v.getType()), v.getName(), v.getDesc());
            dingWorkflow.setUpgrade(true);
            try {
                this.createOrModifyWorkflowForm(dingWorkflow);
                log.info("企业单据官方OA审批表单信息变更成功[{}]", v);
            } catch (Exception e) {
                dingWorkflows.add(dingWorkflow);
                log.error("企业单据官方OA审批表单信息变更失败[{}]", v, e);
            }
        }
        if (CollUtil.isNotEmpty(dingWorkflows)) {
            redisService.set("ding_workflow_upgrade_failure", JSONObject.toJSONString(dingWorkflows));
        }
        return true;
    }

    @Scheduled(cron = "0 0/15 * * * ?")
    public void handleUpgradeFailure() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }

        redisDistributeLock.lock("workflowUpgradeFailure", 3, TimeUnit.MINUTES, (a) -> {
            if (!redisService.hasKey("ding_workflow_upgrade_failure")) {
                return;
            }
            Object failure = redisService.get("ding_workflow_upgrade_failure");
            List<DingWorkflow> dingWorkflows = JSONArray.parseArray(String.valueOf(failure), DingWorkflow.class);
            List<DingWorkflow> error = new ArrayList<>();
            for (DingWorkflow dingWorkflow : dingWorkflows) {
                try {
                    this.createOrModifyWorkflowForm(dingWorkflow);
                    log.info("企业单据官方OA审批表单信息变更成功[{}]", dingWorkflow);
                } catch (Exception e) {
                    dingWorkflows.add(dingWorkflow);
                    log.error("企业单据官方OA审批表单信息变更失败[{}]", dingWorkflow, e);
                    error.add(dingWorkflow);
                }
            }
            try {
                redisService.del("ding_workflow_upgrade_failure");
            } catch (Exception e) {
                log.error("handleUpgradeFailure set redis error", e);
            }
            if (CollUtil.isNotEmpty(error)) {
                redisService.set("ding_workflow_upgrade_failure", JSONObject.toJSONString(error));
            }
        });
    }

    public Boolean getOrderEnableWorkflow(Integer bizType, Long companyId, Integer orderType) {
        return adapterMapper.selectOrderEnableWorkflow(bizType, companyId, orderType);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean changeEnable(Integer bizType, Long companyId, Integer orderType, Integer change) {
        adapterMapper.updateOrderTypeEnableWorkflow(bizType, companyId, orderType, change);
        if (change == 1) {
            // 根据 bizType 和 orderType 获取OrderTypeId
            WorkflowConstant.OrderFixedForm v1 = WorkflowConstant.BizField.ORDER_FIXED_FIELDS.get(orderType);
            String v2 = "";
            if (bizType == 1) {
                v2 = ASSET;
            }
            if (bizType == 2) {
                v2 = MATERIAL;
            }
            if (bizType == 3) {
                v2 = PRODUCT;
            }
            String v3 = v2.equals(PRODUCT) ? "purchase" : v2;
            Long id = adapterMapper.selectOrderTypeIdOrElseThrow(v3, companyId, String.valueOf(orderType));
            DingWorkflow dingWorkflow = DingWorkflow.buildForForm(companyId, v2, id, String.valueOf(orderType), v1.getName(), v1.getDesc());
            dingWorkflow.setUpgrade(false);
            createOrModifyWorkflowForm(dingWorkflow);
        }
        return true;
    }

    private List<WorkflowField> getOrderWorkflowFields(DingWorkflow dingWorkflow) {
        OrderFormTypeEnum anEnum = OrderFormTypeEnum.getOrderFormTypeEnum(Convert.toInt(dingWorkflow.getOrderType()));
        FormVO form = formService.getTpl(anEnum.getBizType(), Collections.singletonList(String.valueOf(dingWorkflow.getCompanyId())));
        dingWorkflow.setFormName(form.getFormName()).setFormDesc(form.getFormIntro());
        List<WorkflowField> workflowFields = form.getFormFields().stream().filter(v -> (!v.getFieldType().equals(FormFieldCO.SPLIT_LINE) && !v.isHidden())).map(WorkflowField::new).collect(Collectors.toList());
        if (CollUtil.isEmpty(workflowFields)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先配置单据表单");
        }
        if (workflowFields.size() > MAX_COMPONENT) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "钉钉审批流表单最多支持20个组件");
        }
        workflowFields.forEach(v -> v.setIsRequired(false));
        return workflowFields;
    }

    private List<WorkflowField> getBizWorkflowFormFields(DingWorkflow dingWorkflow) {
        AtomicReference<String> type = new AtomicReference<>(dingWorkflow.getBizType());
        if (PRODUCT.equals(dingWorkflow.getBizType())) {
            type.set(STANDARD);
        }
        // 业务表单固定字段
        // FormVO bizForm = formService.getTplByType(type.get());
        List<String> ids = type.get().equals(STANDARD) ? Arrays.asList("0", String.valueOf(dingWorkflow.getCompanyId())) : Collections.singletonList(String.valueOf(dingWorkflow.getCompanyId()));
        FormVO bizForm = formService.getTpl(type.get(), ids);
        List<WorkflowField> workflowFields = bizForm.getFormFields().stream().filter(v -> !v.getFieldType().equals(FormFieldCO.SPLIT_LINE)).filter(field -> {
            switch (type.get()) {
                case ASSET:
                    return WorkflowConstant.BizField.ASSETS_FIXED_FIELDS.contains(field.getFieldCode());
                case MATERIAL:
                    return WorkflowConstant.BizField.MATERIALS_FIXED_FIELDS.contains(field.getFieldCode());
                case STANDARD:
                    return WorkflowConstant.BizField.STANDARD_FIXED_FIELDS.contains(field.getFieldCode());
                default:
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不支持的业务类型");
            }
        }).map(WorkflowField::new).collect(Collectors.toList());
        if (CollUtil.isEmpty(workflowFields)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "业务表单字段为空");
        }
        if (STANDARD.equals(type.get())) {
            workflowFields.stream().filter(v -> "name".equals(v.getCode())).findFirst().ifPresent(v -> v.setLabel("物品名称"));
        }
        // 单据额外字段
        String id = dingWorkflow.getCompanyId() + "_" + dingWorkflow.getBizType() + "_" + dingWorkflow.getOrderType();
        switch (dingWorkflow.getOrderType()) {
            // 耗材领用单
            case "33":
                workflowFields.add(new WorkflowField("lyNum", id + "_lyNum", "领用申请数量"));
                break;
            // 耗材出库
            case "32":
                workflowFields.add(new WorkflowField("ckNum", id + "_ckNum", "出库数量"));
                workflowFields.add(new WorkflowField("ckUnitPrice", id + "_ckUnitPrice", "本次出库单价"));
                workflowFields.add(new WorkflowField("ckAmount", id + "_ckAmount", "出库金额"));
                break;
            // 耗材入库
            case "31":
                workflowFields.add(new WorkflowField("rkNum", id + "_rkNum", "入库数量"));
                workflowFields.add(new WorkflowField("rkUnitPrice", id + "_rkUnitPrice", "本次入库单价"));
                workflowFields.add(new WorkflowField("rkPrice", id + "_rkAmount", "入库金额"));
                break;
            // 采购申请
            case "10":
                workflowFields.add(new WorkflowField("quantity", id + "_quantity", "申购数量"));
                workflowFields.add(new WorkflowField("price", id + "_price", "预估单价"));
                workflowFields.add(new WorkflowField("expectedArrivalDate", id + "_expectedArrivalDate", "期待到货日期"));
                workflowFields.add(new WorkflowField("remark", id + "_remark", "备注"));
                break;
            default:
                // nothing
        }
        workflowFields.forEach(v -> v.setIsRequired(false));
        return workflowFields;
    }

    private FormComponent getChildrenForm(DingWorkflow dingWorkflow) {
        List<FormComponent> texts = getBizWorkflowFormFields(dingWorkflow).stream().map(WorkflowField::convertToComponent).collect(Collectors.toList());
        FormComponentProps props = new FormComponentProps().setComponentId(dingWorkflow.getBizType() + "_" + dingWorkflow.getCompanyId()).setVerticalPrint(true).setTableViewMode("table");
        if (ASSET.equalsIgnoreCase(dingWorkflow.getBizType())) {
            props.setLabel("资产列表");
        }
        if (MATERIAL.equalsIgnoreCase(dingWorkflow.getBizType())) {
            props.setLabel("耗材列表");
        }
        if (PRODUCT.equalsIgnoreCase(dingWorkflow.getBizType())) {
            props.setLabel("产品列表");
        }
        return new FormComponent().setComponentType(WorkflowConstant.FieldType.TABLE).setProps(props).setChildren(texts);
    }

    private Map<String, Object> getChildrenFromValue(DingWorkflow dingWorkflow) {
        Map<String, Object> result = new HashMap<>(2);
        if (ASSET.equalsIgnoreCase(dingWorkflow.getBizType())) {
            result.put(WorkflowConstant.Params.NAME, "资产列表");
        }
        if (MATERIAL.equalsIgnoreCase(dingWorkflow.getBizType())) {
            result.put(WorkflowConstant.Params.NAME, "耗材列表");
        }
        if (PRODUCT.equalsIgnoreCase(dingWorkflow.getBizType())) {
            result.put(WorkflowConstant.Params.NAME, "产品列表");
        }
        List<WorkflowField> fields = getBizWorkflowFormFields(dingWorkflow);
        List<JSONObject> bizData = dingWorkflow.getBizData();
        JSONArray value = new JSONArray();
        bizData.forEach(v -> {
            List<JSONObject> objects = fields.stream().map(k -> k.componentValuesMap(v)).map(JSONObject::new).collect(Collectors.toList());
            value.add(objects);
        });
        result.put(WorkflowConstant.Params.VALUE, value.toJSONString());
        return result;
    }

    private ProcessForecastRequest.ProcessForecastRequestFormComponentValues getChildrenFromValueForGetNode(DingWorkflow dingWorkflow) {
        Map<String, Object> map = getChildrenFromValue(dingWorkflow);
        try {
            return ProcessForecastRequest.ProcessForecastRequestFormComponentValues.build(map);
        } catch (Exception e) {
            throw new RuntimeException("转换参数异常");
        }
    }

    private StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues getChildrenFromValueForStartInst(DingWorkflow dingWorkflow) {
        Map<String, Object> map = getChildrenFromValue(dingWorkflow);
        try {
            return StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues.build(map);
        } catch (Exception e) {
            throw new RuntimeException("转换参数异常");
        }
    }

    private DingProcInstMapping getDingProcInstMapping(DingWorkflow dingWorkflow) {
        if (!ALLOW.contains(dingWorkflow.getBizType())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不支持的单据业务类型");
        }
        DingWorkflowBusiness business = dingWorkflowBusinessMapper.selectByBusinessId(dingWorkflow.getOrderId());
        return new DingProcInstMapping(dingWorkflow.getOrderId(), business.getProcInstId(), business.getOrderType(), business.isStart(), business.getNode(), business.getCreateTime(), business.getCompanyId(), business.getCreateBy());
    }

}
