package com.niimbot.asset.dingtalk.workflow.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflow;
import com.niimbot.asset.dingtalk.base.dto.WorkflowBusiness;
import com.niimbot.asset.dingtalk.base.dto.WorkflowStep;
import com.niimbot.asset.dingtalk.base.model.DingWorkflowBusiness;
import com.niimbot.asset.dingtalk.workflow.mapper.AdapterMapper;
import com.niimbot.asset.dingtalk.workflow.mapper.DingWorkflowBusinessMapper;
import com.niimbot.asset.framework.constant.DictConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WorkflowDataReviseService {

    private final AdapterMapper adapterMapper;

    private final DingWorkflowBusinessMapper dingWorkflowBusinessMapper;

    private final OwnWorkflowService ownWorkflowService;

    @Transactional(rollbackFor = Exception.class)
    public Boolean ownWorkflowDataRevise(Long orderId) {
        Integer waitApprove = Convert.toInt(DictConstant.WAIT_APPROVE, 0);
        DingWorkflowBusiness dingWorkflowBusiness = dingWorkflowBusinessMapper.selectOne(
                Wrappers.lambdaQuery(DingWorkflowBusiness.class)
                        .eq(DingWorkflowBusiness::getBizId, orderId)
                        .eq(DingWorkflowBusiness::getWay, 2)
        );
        // 钉钉审批不存在或不是待审批状态
        if (Objects.isNull(dingWorkflowBusiness) || !Objects.equals(waitApprove, dingWorkflowBusiness.getApproveStatus())) {
            return false;
        }
        WorkflowBusiness workflowBusiness = adapterMapper.selectWorkflowBusinessByBizId(orderId);
        // 云资产审批不存在或是未完成状态
        if (Objects.isNull(workflowBusiness) || Objects.equals(waitApprove, workflowBusiness.getApproveStatus())) {
            return false;
        }
        List<WorkflowStep> steps = workflowBusiness.getSteps().toJavaList(WorkflowStep.class);
        steps.removeIf(v -> !WorkflowService.ALLOW_SEND_TASK_TYPES.contains(v.getType()));

        // 处理钉钉审批任务 不处理抄送节点的消息发送
        if (CollUtil.isNotEmpty(steps)) {
            steps.forEach(step -> {
                if (Convert.toInt(DictConstant.REVOKED).equals(workflowBusiness.getApproveStatus())) {
                    ownWorkflowService.handleRevokedTask(DingWorkflow.buildForOwnHandleRevokedTask(workflowBusiness.getProcInstId()));
                    return;
                }
                List<Long> ids = step.resolveApproveEmpIds();
                if (CollUtil.isEmpty(ids)) {
                    return;
                }
                ids.forEach(id -> {
                    if (Convert.toInt(DictConstant.APPROVED).equals(workflowBusiness.getApproveStatus())) {
                        ownWorkflowService.handleApprovedTask(DingWorkflow.buildForOwnHandleApprovedTask(workflowBusiness.getProcInstId(), step.getStepId(), id, false));
                    }
                    if (Convert.toInt(DictConstant.REJECTED).equals(workflowBusiness.getApproveStatus())) {
                        ownWorkflowService.handleRejectedTask(DingWorkflow.buildForOwnHandleRejectedTask(workflowBusiness.getProcInstId(), step.getStepId(), id, false));
                    }
                });
            });
        }
        // 处理钉钉审批实例
        ownWorkflowService.endProcInst(DingWorkflow.buildForOwnEndProcInst(orderId, workflowBusiness.getApproveStatus().toString()));
        return true;
    }

}
