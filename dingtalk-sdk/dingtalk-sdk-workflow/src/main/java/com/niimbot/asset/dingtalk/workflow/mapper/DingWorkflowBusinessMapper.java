package com.niimbot.asset.dingtalk.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.model.DingWorkflowBusiness;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.core.exception.category.BusinessException;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@EnableDataPerm
public interface DingWorkflowBusinessMapper extends BaseMapper<DingWorkflowBusiness> {

    default DingWorkflowBusiness selectByIdIfNotExistThrow(Serializable id) {
        DingWorkflowBusiness dingWorkflowBusiness = this.selectById(id);
        if (Objects.isNull(dingWorkflowBusiness)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "");
        }
        return dingWorkflowBusiness;
    }

    default DingWorkflowBusiness selectByBusinessId(Long businessId) {
        DingWorkflowBusiness business = this.selectOne(
                Wrappers.lambdaQuery(DingWorkflowBusiness.class)
                        .eq(DingWorkflowBusiness::getBizId, businessId)
        );
        if (Objects.isNull(business)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "单据未创建审批流程");
        }
        return business;
    }

    default DingWorkflowBusiness selectByProcInstId(String procInstId) {
        DingWorkflowBusiness business = this.selectOne(
                Wrappers.lambdaQuery(DingWorkflowBusiness.class)
                        .eq(DingWorkflowBusiness::getProcInstId, procInstId)
        );
        if (Objects.isNull(business)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "审批实例不存在");
        }
        return business;
    }
}
