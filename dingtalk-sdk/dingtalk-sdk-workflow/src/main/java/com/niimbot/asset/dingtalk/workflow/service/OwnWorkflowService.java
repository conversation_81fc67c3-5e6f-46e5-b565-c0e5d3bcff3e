package com.niimbot.asset.dingtalk.workflow.service;

import com.aliyun.dingtalkworkflow_1_0.models.CreateIntegratedTaskHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.CreateIntegratedTaskRequest;
import com.aliyun.dingtalkworkflow_1_0.models.CreateIntegratedTaskResponse;
import com.aliyun.dingtalkworkflow_1_0.models.FormComponent;
import com.aliyun.dingtalkworkflow_1_0.models.SaveIntegratedInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.SaveIntegratedInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.SaveIntegratedInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.SaveProcessHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.SaveProcessRequest;
import com.aliyun.dingtalkworkflow_1_0.models.SaveProcessResponse;
import com.aliyun.dingtalkworkflow_1_0.models.UpdateIntegratedTaskHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.UpdateIntegratedTaskRequest;
import com.aliyun.dingtalkworkflow_1_0.models.UpdateIntegratedTaskResponse;
import com.aliyun.dingtalkworkflow_1_0.models.UpdateProcessInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.UpdateProcessInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.UpdateProcessInstanceResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.niimbot.asset.dingtalk.base.dto.DingOrderTypeExt;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflow;
import com.niimbot.asset.dingtalk.base.dto.OrderInfo;
import com.niimbot.asset.dingtalk.base.dto.WorkflowBusiness;
import com.niimbot.asset.dingtalk.base.dto.WorkflowStep;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingOrderType;
import com.niimbot.asset.dingtalk.base.model.DingWorkflowBusiness;
import com.niimbot.asset.dingtalk.base.model.DingWorkflowTask;
import com.niimbot.asset.dingtalk.base.model.constant.DingUrl;
import com.niimbot.asset.dingtalk.base.model.constant.enums.OrderUrl;
import com.niimbot.asset.dingtalk.base.restops.RestOps;
import com.niimbot.asset.dingtalk.message.DingtalkMessageClient;
import com.niimbot.asset.dingtalk.message.constant.Placeholder;
import com.niimbot.asset.dingtalk.message.constant.TemplateTypeEnum;
import com.niimbot.asset.dingtalk.message.request.template.DingtalkMsgOaMessageRequest;
import com.niimbot.asset.dingtalk.workflow.mapper.DingWorkflowTaskMapper;
import com.niimbot.asset.dingtalk.workflow.model.DingProcInstMapping;
import com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant;
import com.niimbot.asset.dingtalk.workflow.model.WorkflowField;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.JacksonConverter;

import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OwnWorkflowService extends WorkflowService {

    private final CacheResourceUtil cacheResourceUtil;

    private final DingWorkflowTaskMapper dingWorkflowTaskMapper;

    private final DingtalkMessageClient dingtalkMessageClient;

    public Boolean updateForm(Long companyId) {
        // 1.所有创建了的表单
        List<DingOrderType> orderTypes = dingOrderTypeMapper.selectForRefreshOaForm(companyId);
        if (CollUtil.isEmpty(orderTypes)) {
            log.info("没有已开通的自有OA审批单据");
            return true;
        }
        Map<Long, DingOrderType> map = orderTypes.stream().collect(Collectors.toMap(DingOrderType::getOrderTypeId, v -> v));
        List<Long> ids = orderTypes.stream().map(DingOrderType::getOrderTypeId).collect(Collectors.toList());
        // 2.循环更新
        List<DingOrderTypeExt> m1 = adapterMapper.selectOrderTypeExt("as_order_type", ids);
        List<DingOrderTypeExt> m2 = adapterMapper.selectOrderTypeExt("as_material_order_type", ids);
        List<DingOrderTypeExt> m3 = adapterMapper.selectOrderTypeExt("as_purchase_order_type", ids);
        List<DingOrderTypeExt> all = new ArrayList<>();
        all.addAll(m1);
        all.addAll(m2);
        all.addAll(m3);
        all.stream().filter(v -> ids.contains(v.getOrderTypeId())).forEach(v -> {
            DingOrderType dingOrderType = map.get(v.getOrderTypeId());
            DingWorkflow dingWorkflow = new DingWorkflow().setOrderType(String.valueOf(v.getType())).setCompanyId(dingOrderType.getCompanyId());
            try {
                doCreateForm(dingWorkflow, dingOrderType.getProcessCode());
            } catch (Exception e) {
                log.error("更新用户OA表单模板请求异常[{}]", dingOrderType.getOrderTypeId(), e);
            }
        });

        return true;
    }

    /**
     * 创建或更新钉钉流程表单
     *
     * @param dingWorkflow 企业ID,单据类型
     * @return 生成企业内唯一的审批模板编码，可使用此processCode发起表单实例。
     * @see <a href="https://open.dingtalk.com/document/orgapp-server/create-an-approval-form-template">钉钉文档</a>
     */
    public String createForm(DingWorkflow dingWorkflow) {
        String activitiKey = adapterMapper.selectActivityKeyByWorkflowId(dingWorkflow.getWorkflowId());
        String orderBizType = WorkflowConstant.BizActivitiKey.getOrderBizType(activitiKey);
        int orderType = WorkflowConstant.BizActivitiKey.getOrderType(activitiKey);
        Long orderTypeId = dingOrderTypeMapper.selectOrderTypeId(orderBizType, dingWorkflow.getCompanyId(), orderType);
        // 2.获取企业单据模板code
        DingOrderType dingOrderType = dingOrderTypeMapper.selectByBizType(orderTypeId, 2);
        if (!dingWorkflow.getFlag() && Objects.nonNull(dingOrderType) && StrUtil.isNotBlank(dingOrderType.getProcessCode())) {
            log.info("企业[{}]单据已创建过钉钉审批模板", dingWorkflow.getCompanyId());
            return dingOrderType.getProcessCode();
        }
        dingWorkflow.setOrderType(String.valueOf(orderType)).setOrderTypeId(orderTypeId);
        String code = doCreateForm(dingWorkflow, Objects.isNull(dingOrderType) ? null : dingOrderType.getProcessCode());
        if (Objects.isNull(dingOrderType)) {
            dingOrderTypeMapper.insert(new DingOrderType(dingWorkflow.getOrderTypeId(), dingWorkflow.getCompanyId(), code).setWay(2));
        }
        return code;
    }

    public String doCreateForm(DingWorkflow dingWorkflow, String code) {
        // 1.获取企业cropId
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        // 3.获取单据表单固定字段
        WorkflowConstant.OrderFixedForm orderFixedForm = WorkflowConstant.BizField.ORDER_FIXED_FIELDS.get(Integer.parseInt(dingWorkflow.getOrderType()));
        OrderUrl orderUrl = OrderUrl.of(dingWorkflow.getOrderType());
        List<FormComponent> components = orderFixedForm.getFields().stream().map(WorkflowField::convertToComponent).collect(Collectors.toList());
        SaveProcessRequest.SaveProcessRequestProcessFeatureConfigFeatures features = new SaveProcessRequest.SaveProcessRequestProcessFeatureConfigFeatures();
        features.setName("TASK_EXECUTE").setRunType("REDIRECT")
                .setPcUrl(orderUrl.getSubmitUrl().formatPc(dingCorp.getCorpId()))
                .setMobileUrl(DingUrl.getMicroAppJumpUrl(dingCorp.getCorpId(), orderUrl.getSubmitUrl().formatMini(dingCorp.getCorpId())));
        // SaveProcessRequest.SaveProcessRequestProcessFeatureConfigFeaturesCallback callback = new SaveProcessRequest.SaveProcessRequestProcessFeatureConfigFeaturesCallback();
        // callback.setApiKey("envtestoacallback").setAppUuid("dingac870976e8f8af4d ").setVersion("1");
        // features.setName("TASK_EXECUTE").setRunType("ORIGIN")
        //         .setPcUrl(orderUrl.getSubmitUrl().formatPc(dingCorp.getCorpId()))
        //         .setMobileUrl(DingUrl.getMicroAppJumpUrl(dingCorp.getCorpId(), orderUrl.getSubmitUrl().formatMini(dingCorp.getCorpId())))
        //         .setCallback(callback);
        SaveProcessRequest.SaveProcessRequestTemplateConfig templateConfig = new SaveProcessRequest.SaveProcessRequestTemplateConfig()
                .setHidden(false)
                // 不发送工作卡片通知
                .setDisableSendCard(true)
                .setTemplateEditUrl(orderUrl.getEditUrl().formUrl())
                .setCreateInstancePcUrl(orderUrl.getSubmitUrl().formatPc(dingCorp.getCorpId()))
                .setCreateInstanceMobileUrl(DingUrl.getMicroAppJumpUrl(dingCorp.getCorpId(), orderUrl.getSubmitUrl().formatMini(dingCorp.getCorpId())));
        SaveProcessRequest.SaveProcessRequestProcessFeatureConfig config = new SaveProcessRequest.SaveProcessRequestProcessFeatureConfig().setFeatures(Collections.singletonList(features));
        SaveProcessRequest request = new SaveProcessRequest().setName(orderFixedForm.getName() + NAME).setDescription(orderFixedForm.getDesc()).setFormComponents(components).setProcessFeatureConfig(config).setTemplateConfig(templateConfig).setProcessCode(StrUtil.isBlank(code) ? null : code);
        printLog("ding create form", request);
        SaveProcessResponse response = RestOps.handleOrThrow(() -> workflowClient.saveProcessWithOptions(request, SaveProcessHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        return response.getBody().getResult().getProcessCode();
    }

    /**
     * 发起钉钉审批实例
     *
     * @param dingWorkflow 企业ID
     * @return instanceId   审批实例id
     * @see <a href="https://open.dingtalk.com/document/orgapp-server/create-an-approval-instance">钉钉文档</a>
     */
    public String startProcInst(DingWorkflow dingWorkflow) {
        dingWorkflow.setBizType(WorkflowConstant.BizActivitiKey.getOrderBizType(dingWorkflow.getActivitiKey())).setOrderType(String.valueOf(WorkflowConstant.BizActivitiKey.getOrderType(dingWorkflow.getActivitiKey())));
        DingFormMapping formMapping = getDingFormMapping(dingWorkflow.setWay(2));
        DingUserInfo dingUserInfo = getDingUserInfo(dingWorkflow.getEmpId(), null);
        WorkflowConstant.OrderFixedForm orderFixedForm = WorkflowConstant.BizField.ORDER_FIXED_FIELDS.get(Integer.parseInt(dingWorkflow.getOrderType()));
        // 补充创建人与创建时间
        dingWorkflow.getOrderData().put("createBy", dingWorkflow.getEmpId());
        dingWorkflow.getOrderData().put("createByText", cacheResourceUtil.getUserName(dingWorkflow.getEmpId()));
        dingWorkflow.getOrderData().put("createTime", System.currentTimeMillis());
        List<SaveIntegratedInstanceRequest.SaveIntegratedInstanceRequestFormComponentValueList> components = orderFixedForm.getFields().stream().map(v -> v.ownStartProcessInstance(dingWorkflow.getOrderData())).collect(Collectors.toList());
        SaveIntegratedInstanceRequest request = new SaveIntegratedInstanceRequest()
                .setProcessCode(formMapping.getProcessCode())
                .setOriginatorUserId(dingUserInfo.getUserId())
                .setFormComponentValueList(components)
                .setTitle(dingWorkflow.getTitle())
                .setUrl(OrderUrl.of(dingWorkflow.getOrderType()).getDetailsUrl().formatH5(dingWorkflow.getOrderId(), formMapping.getCorpId()));
        printLog("ding start proc inst", request);
        SaveIntegratedInstanceResponse response = RestOps.handleOrThrow(() -> workflowClient.saveIntegratedInstanceWithOptions(request, SaveIntegratedInstanceHeaders.build(headers(formMapping.getCorpId())), new RuntimeOptions()));
        // 保存
        DingWorkflowBusiness business = new DingWorkflowBusiness(response.body.result.processInstanceId, dingWorkflow.getCompanyId(), dingWorkflow.getCompanyId(), 1)
                .setBizId(dingWorkflow.getOrderId()).setBizType(dingWorkflow.getBizType()).setOrderType(dingWorkflow.getOrderType()).setWay(2);
        dingWorkflowBusinessMapper.insert(business);
        return response.getBody().getResult().getProcessInstanceId();
    }

    /**
     * 结束审批实例修改审批最终状态
     *
     * @param dingWorkflow dingWorkflow
     * @return true
     */
    public Boolean endProcInst(DingWorkflow dingWorkflow) {
        RLock lock = redissonClient.getLock("ding-end-proc-inst:" + dingWorkflow.getOrderId());
        if (lock.isLocked()) {
            log.info("钉钉审批结束正在处理[{}]", dingWorkflow.getOrderId());
            return false;
        }
        try {
            if (!lock.tryLock(2, TimeUnit.MINUTES)) {
                log.info("钉钉审批结束处理获取锁已达最大时间限制");
                return false;
            }
            DingWorkflowBusiness dingWorkflowBusiness = dingWorkflowBusinessMapper.selectByBusinessId(dingWorkflow.getOrderId());
            if (!Objects.equals(dingWorkflowBusiness.getApproveStatus(), Convert.toInt(DictConstant.WAIT_APPROVE))) {
                log.info("钉钉审批已处理[{}]", dingWorkflowBusiness.getProcInstId());
                return true;
            }
            DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflowBusiness.getCompanyId());
            UpdateProcessInstanceRequest request = new UpdateProcessInstanceRequest().setProcessInstanceId(dingWorkflowBusiness.getProcInstId());
            // 审批通过
            if (String.valueOf(DictConstant.APPROVED).equals(dingWorkflow.getApproveStatus())) {
                request.setStatus("COMPLETED").setResult("agree");
            }
            // 审批拒绝
            if (String.valueOf(DictConstant.REJECTED).equals(dingWorkflow.getApproveStatus())) {
                request.setStatus("COMPLETED").setResult("refuse");
            }
            // 审批终止
            if (String.valueOf(DictConstant.REVOKED).equals(dingWorkflow.getApproveStatus())) {
                request.setStatus("TERMINATED").setResult("refuse");
            }
            printLog("ding end proc inst", request);
            UpdateProcessInstanceResponse response = RestOps.handleOrThrow(() -> workflowClient.updateProcessInstanceWithOptions(request, UpdateProcessInstanceHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
            dingWorkflowBusiness.setApproveStatus(Convert.toInt(dingWorkflow.getApproveStatus()));
            dingWorkflowBusinessMapper.updateById(dingWorkflowBusiness);
            // 发送审批完成通知
            if (response.body.success) {
                Long createBy = dingWorkflowBusiness.getCreateBy();
                String formName = WorkflowConstant.BizField.ORDER_FIXED_FIELDS.get(Integer.parseInt(dingWorkflowBusiness.getOrderType())).getName();
                sendMsg(dingWorkflow.getOrderId(), dingWorkflowBusiness.getOrderType(), dingCorp.getCorpId(), dingCorp.getAgentId(), Collections.singletonList(createBy), cacheResourceUtil.getUserName(createBy), "您发起的" + formName + "已结束，请知晓");
            }
            return response.body.success;
        } catch (Exception e) {
            log.info("钉钉审批结束处理获取锁失败", e);
        } finally {
            lock.unlock();
        }
        return false;
    }

    /**
     * 创建流程中心任务
     *
     * @param dingWorkflow dingWorkflow
     * @return true or false
     */
    public Boolean createTask(DingWorkflow dingWorkflow) {
        WorkflowBusiness workflowBusiness = adapterMapper.selectWorkflowBusinessByProcInstId(dingWorkflow.getProInstId());
        Map<Long, WorkflowStep> stepMap = workflowBusiness.getSteps().toJavaList(WorkflowStep.class).stream().collect(Collectors.toMap(WorkflowStep::getStepId, v -> v));
        if (!stepMap.containsKey(dingWorkflow.getStepId())) {
            return false;
        }
        WorkflowStep step = stepMap.get(dingWorkflow.getStepId());
        if (!ALLOW_SEND_TASK_TYPES.contains(step.getType())) {
            return true;
        }
        dingWorkflow.setEmpIds(step.resolveApproveEmpIds());
        List<DingWorkflowTask> existingTasks = dingWorkflowTaskMapper.selectList(Wrappers.lambdaQuery(DingWorkflowTask.class).eq(DingWorkflowTask::getAssetProcInstId, dingWorkflow.getProInstId()).eq(DingWorkflowTask::getStepId, dingWorkflow.getStepId()));
        Set<Long> existUserIds = existingTasks.stream().map(DingWorkflowTask::getEmpId).collect(Collectors.toSet());
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        dingWorkflow.getEmpIds().removeIf(existUserIds::contains);
        if (CollUtil.isEmpty(dingWorkflow.getEmpIds())) {
            return true;
        }
        Map<String, Long> userIdMap = new HashMap<>(dingWorkflow.getEmpIds().size());
        Map<Long, String> empIdMap = new HashMap<>(dingWorkflow.getEmpIds().size());
        dingWorkflow.getEmpIds().forEach(v -> {
            String userId = convertDingUserId(v);
            userIdMap.put(userId, v);
            empIdMap.put(v, userId);
        });
        DingProcInstMapping dingProcInstMapping = getDingWorkflowProcInstId(dingWorkflow.getProInstId());
        OrderUrl orderUrl = OrderUrl.of(dingProcInstMapping.getOrderType());
        String h5DetailUrl = orderUrl.getDetailsUrl().formatH5(dingProcInstMapping.getOrderId(), dingCorp.getCorpId());
        List<CreateIntegratedTaskRequest.CreateIntegratedTaskRequestTasks> tasks = dingWorkflow.getEmpIds().stream().map(s -> new CreateIntegratedTaskRequest.CreateIntegratedTaskRequestTasks().setUserId(empIdMap.get(s)).setUrl(h5DetailUrl)).collect(Collectors.toList());
        CreateIntegratedTaskRequest request = new CreateIntegratedTaskRequest().setProcessInstanceId(dingProcInstMapping.getProcInsId()).setTasks(tasks).setActivityId(String.valueOf(dingWorkflow.getStepId()));
        printLog("ding create center task", request);
        CreateIntegratedTaskResponse response = RestOps.handleOrThrow(() -> workflowClient.createIntegratedTaskWithOptions(request, CreateIntegratedTaskHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        List<DingWorkflowTask> dingWorkflowTasks = response.body.result.stream().map(result -> new DingWorkflowTask().setCompanyId(dingWorkflow.getCompanyId()).setAssetProcInstId(dingWorkflow.getProInstId()).setTaskId(result.taskId).setUserId(result.userId).setEmpId(userIdMap.get(result.userId)).setStepId(dingWorkflow.getStepId()).setDingProcInstId(dingProcInstMapping.getProcInsId()).setStatus("CREATE")).collect(Collectors.toList());
        dingWorkflowTasks.forEach(dingWorkflowTaskMapper::insert);
        // 发送通知
        DingWorkflowBusiness business = dingWorkflowBusinessMapper.selectByBusinessId(workflowBusiness.getBizId());
        String formName = WorkflowConstant.BizField.ORDER_FIXED_FIELDS.get(Integer.parseInt(workflowBusiness.getType())).getName();
        sendMsg(workflowBusiness.getBizId(), workflowBusiness.getType(), dingCorp.getCorpId(), dingCorp.getAgentId(), dingWorkflow.getEmpIds(), "系统", cacheResourceUtil.getUserName(business.getCreateBy()) + "提交的" + formName + "，请您审批");
        return true;
    }

    /**
     * 通用的任务处理方法
     * procInstId
     * stepId
     * flag
     * empId
     * empIds
     * approveStatus
     * status
     *
     * @param dingWorkflow dingWorkflow
     * @return true
     */
    private Boolean handleTask(DingWorkflow dingWorkflow) {
        // 1.已创建的流程实例某一步骤的中心任务列表
        List<DingWorkflowTask> tasks = dingWorkflowTaskMapper.selectList(
                Wrappers.lambdaQuery(DingWorkflowTask.class)
                        .eq(DingWorkflowTask::getAssetProcInstId, dingWorkflow.getProInstId())
                        .eq(DingWorkflowTask::getStepId, dingWorkflow.getStepId())
                        .eq(DingWorkflowTask::getStatus, "CREATE")
        );
        // 2.或签 完成当前审批人的任务 取消其他审批人的任务
        if (dingWorkflow.getFlag()) {
            dingWorkflow.setEmpIds(tasks.stream().map(DingWorkflowTask::getEmpId).filter(empId -> !Objects.equals(empId, dingWorkflow.getEmpId())).collect(Collectors.toList()));
            return modifyDingTask(tasks, dingWorkflow);
        }
        // 3.会签 完成当前审批人的任务
        tasks.stream()
                .filter(v -> Objects.equals(dingWorkflow.getEmpId(), v.getEmpId()))
                .findFirst()
                .ifPresent(task -> modifyDingTask(new ArrayList<DingWorkflowTask>() {{
                    this.add(task);
                }}, dingWorkflow));
        return true;
    }

    /**
     * 处理审批通过的中心任务
     * 完成审批人的中心任务，或签时取消其他人的中心任务
     * procInstId
     * stepId
     * empId
     * flag
     *
     * @param dingWorkflow dingWorkflow
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleApprovedTask(DingWorkflow dingWorkflow) {
        dingWorkflow.setApproveStatus(String.valueOf(DictConstant.APPROVED));
        dingWorkflow.setStatus(String.valueOf(DictConstant.REVOKED));
        handleTask(dingWorkflow);
    }

    /**
     * 处理审批拒绝的中心任务
     * procInstId
     * stepId
     * empId
     * flag
     *
     * @param dingWorkflow dingWorkflow
     * @return true
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleRejectedTask(DingWorkflow dingWorkflow) {
        dingWorkflow.setApproveStatus(String.valueOf(DictConstant.REJECTED));
        dingWorkflow.setStatus(String.valueOf(DictConstant.REJECTED));
        handleTask(dingWorkflow);
    }

    /**
     * 处理转办的中心任务
     * procInstId
     * stepId
     * empId
     * toEmpId
     *
     * @param dingWorkflow dingWorkflow
     * @return true
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleForwardTask(DingWorkflow dingWorkflow) {
        DingWorkflowTask task = dingWorkflowTaskMapper.selectOne(
                Wrappers.lambdaQuery(DingWorkflowTask.class)
                        .eq(DingWorkflowTask::getAssetProcInstId, dingWorkflow.getProInstId())
                        .eq(DingWorkflowTask::getStepId, dingWorkflow.getStepId())
                        .eq(DingWorkflowTask::getEmpId, dingWorkflow.getEmpId())
                        .eq(DingWorkflowTask::getStatus, "CREATE")
        );
        if (Objects.isNull(task)) {
            log.info("实例[{}]步骤[{}]员工[{}]无钉钉审批中心任务", dingWorkflow.getProInstId(), dingWorkflow.getStepId(), dingWorkflow.getEmpId());
            return;
        }
        // 取消当前人的任务
        dingWorkflow.setApproveStatus(String.valueOf(DictConstant.REVOKED));
        modifyDingTask(new ArrayList<DingWorkflowTask>() {{
            this.add(task);
        }}, dingWorkflow);
        // 创建转办人的任务
        dingWorkflow.setEmpId(null).setEmpIds(Collections.singletonList(dingWorkflow.getToEmpId())).setCompanyId(task.getCompanyId());
        createTask(dingWorkflow);
    }

    /**
     * 撤销审批实例即取消该实例下的所有任务
     * assetProcInstId
     * companyId
     *
     * @param dingWorkflow dingWorkflow
     * @return true
     * @see <a href="https://open.dingtalk.com/document/isvapp-server/batch-cancel-oa-approval-to-do-tasks">钉钉文档</a>
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleRevokedTask(DingWorkflow dingWorkflow) {
        // 1.实例下所有未完成的步骤
        List<DingWorkflowTask> tasks = dingWorkflowTaskMapper.selectList(
                Wrappers.lambdaQuery(DingWorkflowTask.class)
                        .eq(DingWorkflowTask::getAssetProcInstId, dingWorkflow.getProInstId())
                        .eq(DingWorkflowTask::getStatus, "CREATE")
        );
        if (CollUtil.isEmpty(tasks)) {
            return;
        }
        // 2.取消所有未完成的任务
        // List<String> activityIds = tasks.stream().map(task -> String.valueOf(task.getStepId())).collect(Collectors.toList());
        // DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(tasks.get(0).getCompanyId());
        // tasks.forEach(v -> {
        //     try {
        //         CancelIntegratedTaskRequest request = new CancelIntegratedTaskRequest().setActivityId(String.valueOf(v.getStepId()))/*.setActivityIds(activityIds)*/.setProcessInstanceId(tasks.get(0).getDingProcInstId());
        //         printLog("ding revoked task", request);
        //         CancelIntegratedTaskResponse response = RestOps.handleOrThrow(() -> workflowClient.cancelIntegratedTaskWithOptions(request, CancelIntegratedTaskHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        //         if (response.body.success) {
        //             v.setStatus("CANCELED");
        //             dingWorkflowTaskMapper.updateById(v);
        //         }
        //     } catch (Exception e) {
        //         log.error("钉钉审批撤销任务异常[{}]", v.getId());
        //     }
        // });
        // 2.根据taskId撤销任务
        dingWorkflow.setApproveStatus(String.valueOf(DictConstant.REVOKED));
        modifyDingTask(tasks, dingWorkflow);
        DingProcInstMapping procInstMapping = adapterMapper.selectDingProcInstIdByAssetProcInstId(dingWorkflow.getProInstId());
        if (Objects.nonNull(procInstMapping)) {
            // 3.结束审批实例
            dingWorkflow.setOrderId(procInstMapping.getOrderId());
            endProcInst(dingWorkflow);
        }
    }

    /**
     * 修改钉钉流程中心任务状态
     *
     * @param tasks        同一流程实例下的任务列表
     * @param dingWorkflow dingWorkflow
     * @return true
     * @see <a href="https://open.dingtalk.com/document/isvapp-server/update-process-center-task-status"/>钉钉文档</a>
     */
    public Boolean modifyDingTask(List<DingWorkflowTask> tasks, DingWorkflow dingWorkflow) {
        tasks.removeIf(v -> !"CREATE".equals(v.getStatus()));
        if (CollUtil.isEmpty(tasks)) {
            return true;
        }
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(tasks.get(0).getCompanyId());
        List<UpdateIntegratedTaskRequest.UpdateIntegratedTaskRequestTasks> list = tasks.stream().map(dingWorkflowTask -> {
            UpdateIntegratedTaskRequest.UpdateIntegratedTaskRequestTasks v = new UpdateIntegratedTaskRequest.UpdateIntegratedTaskRequestTasks().setTaskId(dingWorkflowTask.getTaskId());
            String approveStatus = dingWorkflow.getApproveStatus();
            if (StrUtil.isNotBlank(dingWorkflow.getStatus()) && CollUtil.isNotEmpty(dingWorkflow.getEmpIds()) && dingWorkflow.getEmpIds().contains(dingWorkflowTask.getEmpId())) {
                approveStatus = dingWorkflow.getStatus();
            }
            if (String.valueOf(DictConstant.APPROVED).equals(approveStatus)) {
                v.setStatus("COMPLETED").setResult("AGREE");
                dingWorkflowTask.setStatus("AGREE");
            }
            if (String.valueOf(DictConstant.REJECTED).equals(approveStatus)) {
                v.setStatus("COMPLETED").setResult("REFUSE");
                dingWorkflowTask.setStatus("REFUSE");
            }
            if (String.valueOf(DictConstant.REVOKED).equals(approveStatus)) {
                v.setStatus("CANCELED");
                dingWorkflowTask.setStatus("CANCELED");
            }
            return v;
        }).collect(Collectors.toList());
        UpdateIntegratedTaskRequest request = new UpdateIntegratedTaskRequest().setProcessInstanceId(tasks.get(0).getDingProcInstId()).setTasks(list);
        printLog("ding modify center task", request);
        UpdateIntegratedTaskResponse response = RestOps.handleOrThrow(() -> workflowClient.updateIntegratedTaskWithOptions(request, UpdateIntegratedTaskHeaders.build(headers(dingCorp.getCorpId())), new RuntimeOptions()));
        tasks.forEach(dingWorkflowTaskMapper::updateById);
        return response.body.success;
    }

    /**
     * 处理审批抄送
     *
     * @param dingWorkflow dingWorkflow
     */
    public void handleCarbonCopy(DingWorkflow dingWorkflow) {
        String proInstId = dingWorkflow.getProInstId();
        List<Long> empIds = dingWorkflow.getEmpIds();
        // 审批单信息
        DingProcInstMapping mapping = adapterMapper.selectDingProcInstIdByAssetProcInstId(proInstId);
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyId(mapping.getCompanyId());
        String userName = cacheResourceUtil.getUserName(mapping.getCreateBy());
        String formName = WorkflowConstant.BizField.ORDER_FIXED_FIELDS.get(Integer.parseInt(mapping.getOrderType())).getName();
        sendMsg(mapping.getOrderId(), mapping.getOrderType(), dingCorp.getCorpId(), dingCorp.getAgentId(), empIds, userName, userName + "的" + formName + "抄送给您，请查阅");
    }

    private void sendMsg(Long orderId, String orderType, String cropId, Long agentId, List<Long> userIds, String userName, String title) {
        // 获取单据编号
        String orderTableName = WorkflowConstant.TableName.getOrderTableName(orderType);
        DingtalkMsgOaMessageRequest messageRequest = new DingtalkMsgOaMessageRequest(cropId);
        messageRequest.setAgentId(agentId);
        messageRequest.setUserIds(convertDingUserIds(userIds));
        messageRequest.setBusinessId(orderId);
        messageRequest.setBusinessType(Convert.toInt(orderType));
        messageRequest.setTitle(title);
        messageRequest.setTemplateFormSize(4);
        messageRequest.getExtParams().put(Placeholder.USER_NAME, userName);

        OrderInfo info = adapterMapper.selectOrderInfo(orderId, orderTableName);
        if (Objects.nonNull(info)) {
            info.getOrderData().put("createByText", cacheResourceUtil.getUserName(info.getCreateBy()));
            info.getOrderData().put("createTime", info.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            messageRequest.getExtParams().put("content", "单据编号：" + info.getOrderNo());
            WorkflowConstant.OrderFixedForm orderFixedForm = WorkflowConstant.BizField.ORDER_FIXED_FIELDS.get(Convert.toInt(orderType));
            List<DingtalkMsgOaMessageRequest.Form> forms = orderFixedForm.getFields().stream().map(v -> {
                Map<String, Object> map = v.componentValuesMap(info.getOrderData());
                return new DingtalkMsgOaMessageRequest.Form(map.get(WorkflowConstant.Params.NAME) + "：", String.valueOf(map.get(WorkflowConstant.Params.VALUE)));
            }).collect(Collectors.toList());
            messageRequest.setForm(forms);
            messageRequest.setTemplateFormSize(6);
        }

        dingtalkMessageClient.sendMessage(TemplateTypeEnum.TODO_WORKFLOW_TPL, messageRequest);
    }

    private void printLog(String desc, Object request) {
        try {
            log.info(desc + " request [{}]", JacksonConverter.MAPPER.writeValueAsString(request));
        } catch (JsonProcessingException e) {
            log.error("ding print request log error");
        }
    }

}
