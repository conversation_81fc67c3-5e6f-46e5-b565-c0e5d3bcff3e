package com.niimbot.asset.dingtalk.workflow.service;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Sets;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.asset.dingtalk.workflow.event.DingWorkflowEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ForwardHandler extends DingWorkflowEventHandler implements InitializingBean {

    private RestTemplate restTemplate;

    private final Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            // retryIf 重试条件
            .retryIfException()
            .retryIfRuntimeException()
            .retryIfExceptionOfType(Exception.class)
            .retryIfException(v -> Objects.equals(v, new Exception()))
            .retryIfResult(v -> Objects.equals(v, false))
            // 等待策略：间隔5秒，每次递增5s
            .withWaitStrategy(WaitStrategies.incrementingWait(2, TimeUnit.SECONDS, 2, TimeUnit.SECONDS))
            // 停止策略 : 尝试请求4次
            .withStopStrategy(StopStrategies.stopAfterAttempt(5))
            // 时间限制 : 每次请求不得超过1min
            // .withAttemptTimeLimiter(AttemptTimeLimiters.fixedTimeLimit(1L, TimeUnit.MINUTES))
            .build();

    private final Set<String> means = Sets.newHashSet("1", "2", "3", "4", "5", "8", "9");

    private final Map<Set<String>, String> forwardMap = new HashMap<>(16);

    @Override
    public Set<String> orderTypes() {
        return Sets.newHashSet(
                "1", "2", "3", "4", "5", "8", "9",
                "31", "32", "33", "34", "35", "36",
                "10", "19",
                "6", "7"

        );
    }

    @Override
    public Boolean handle(DingWorkflowEvent event) {
        forwardMap.forEach((k, v) -> {
            if (k.contains(event.getOrderType())) {
                WorkflowCallbackDto dto = new WorkflowCallbackDto().setStartUserId(String.valueOf(event.getStartUserId()))
                        .setBusinessId(String.valueOf(event.getOrderId()))
                        .setApproveStatus(String.valueOf(event.getApproveStatus()))
                        .setProcessInstanceId("ding-not-exist");
                BusinessCallback callback = new BusinessCallback(v, dto);
                try {
                    this.retryer.call(callback);
                } catch (Exception e) {
                    log.error("钉钉官方OA审批回调处理业务数据失败", e);
                }
            }
        });
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 资产单据
        forwardMap.put(means, "http://127.0.0.1:8082/server/means/assetOrder/processCallback");
        // 耗材单据
        forwardMap.put(Sets.newHashSet("31"), "http://127.0.0.1:8082/server/material/order/rk/processCallback");
        forwardMap.put(Sets.newHashSet("32"), "http://127.0.0.1:8082/server/material/order/ck/processCallback");
        forwardMap.put(Sets.newHashSet("33"), "http://127.0.0.1:8082/server/material/order/ly/processCallback");
        forwardMap.put(Sets.newHashSet("34"), "http://127.0.0.1:8082/server/material/order/tz/processCallback");
        forwardMap.put(Sets.newHashSet("35"), "http://127.0.0.1:8082/server/material/order/db/processCallback");
        forwardMap.put(Sets.newHashSet("36"), "http://127.0.0.1:8082/server/material/order/bs/processCallback");
        // 采购
        forwardMap.put(Sets.newHashSet("10"), "http://127.0.0.1:8082/server/purchase/apply/processApplyCallback");
        forwardMap.put(Sets.newHashSet("12"), "http://127.0.0.1:8082/server/purchase/order/processCallback");
        // 保养
        forwardMap.put(Sets.newHashSet("6"), "http://127.0.0.1:8082/server/maintenance/repair/report/processCallback");
        forwardMap.put(Sets.newHashSet("7"), "http://127.0.0.1:8082/server/maintenance/repair/processCallback");

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        requestFactory.setReadTimeout(5000);
        this.restTemplate = new RestTemplate(requestFactory);
    }

    class BusinessCallback implements Callable<Boolean> {
        private final String callbackUrl;
        private final WorkflowCallbackDto callbackDto;

        public BusinessCallback(String callbackUrl, WorkflowCallbackDto callbackDto) {
            this.callbackUrl = callbackUrl;
            this.callbackDto = callbackDto;
        }

        @Override
        public Boolean call() throws Exception {
            log.info("业务回调 callbackUrl = [{}], instanceId = [{}], businessId = [{}], approveStatus = [{}]",
                    callbackUrl, callbackDto.getProcessInstanceId(), callbackDto.getBusinessId(),
                    callbackDto.getApproveStatus());
            ResponseEntity<Boolean> responseEntity = restTemplate.postForEntity(
                    callbackUrl,
                    callbackDto,
                    Boolean.class);
            return responseEntity.getBody();
        }
    }
}
