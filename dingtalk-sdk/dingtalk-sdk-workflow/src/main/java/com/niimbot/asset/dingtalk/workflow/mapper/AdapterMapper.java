package com.niimbot.asset.dingtalk.workflow.mapper;

import cn.hutool.core.util.StrUtil;
import com.niimbot.asset.dingtalk.base.dto.DingOrderTypeExt;
import com.niimbot.asset.dingtalk.base.dto.EmpInfo;
import com.niimbot.asset.dingtalk.base.dto.OrderInfo;
import com.niimbot.asset.dingtalk.base.dto.WorkflowBusiness;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.workflow.model.DingProcInstMapping;
import com.niimbot.asset.dingtalk.workflow.model.WorkflowField;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface AdapterMapper {

    DingCorp selectDingCorpByCompanyId(@Param("companyId") Long companyId);

    default DingCorp selectDingCorpByCompanyIdOrElseThrow(Long companyId) {
        return Optional.ofNullable(selectDingCorpByCompanyId(companyId)).orElseThrow(() -> new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST));
    }

    Long selectOrderTypeIdByCompanyIdAndOrderType(@Param("bizType") String bizType, @Param("companyId") Long companyId, @Param("orderType") String orderType);

    default Long selectOrderTypeIdOrElseThrow(String bizType, Long companyId, String orderType) {
        return Optional.ofNullable(selectOrderTypeIdByCompanyIdAndOrderType(bizType, companyId, orderType)).orElseThrow(() -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "单据类型不存在"));
    }

    List<WorkflowField> selectAssetOrderFieldByCompanyIdAndType(@Param("companyId") Long companyId, @Param("type") Short type);

    List<WorkflowField> selectMaterialOrderFieldByCompanyIdAndType(@Param("companyId") Long companyId, @Param("type") Integer type);

    String selectDingUserIdByAssetEmpId(@Param("empId") Long empId);

    default String selectDingUserIdByEmpIdOrElseThrow(Long empId) {
        String dingUserId = selectDingUserIdByAssetEmpId(empId);
        if (StrUtil.isBlank(dingUserId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "员工未同步");
        }
        return dingUserId;
    }

    String selectDingUnionIdByAssetEmpId(@Param("empId") Long empId);

    default String selectDingUnionIdByEmpIdOrElseThrow(long empId) {
        String dingUnionId = selectDingUnionIdByAssetEmpId(empId);
        if (StrUtil.isBlank(dingUnionId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "员工未同步");
        }
        return dingUnionId;
    }

    List<String> selectDingUserIdsByAssetIdIn(@Param("empIds") List<Long> ids);

    String selectDingUserId(@Param("empId") Long empId);

    List<EmpInfo> selectAssetEmpByDingUserIdsIn(@Param("companyId") Long companyId, @Param("userIds") List<String> ids);

    List<Long> selectOrgIdsByEmpId(@Param("empId") Long empId);

    String selectDingOrgIdByAssetOrgId(@Param("orgId") Long orgId);

    void updateApproveStatusByCondition(@Param("bizType") String bizType, @Param("orderType") String orderType, @Param("id") Long id, @Param("status") Integer status);

    List<WorkflowField> selectAssetFormFieldByCompanyId(@Param("companyId") Long companyId);

    List<WorkflowField> selectMaterialFormFieldByCompanyId(@Param("companyId") Long companyId);

    List<WorkflowField> selectBaseProductFormField();

    DingProcInstMapping selectDingProcInstIdByAssetProcInstId(@Param("assetProcInstId") String assetProcInstId);

    Integer selectStepTypeById(@Param("id") Long stepId);

    WorkflowBusiness selectWorkflowBusinessByProcInstId(@Param("procInstId") String procInstId);

    WorkflowBusiness selectWorkflowBusinessByBizId(@Param("bizId") Long bizId);

    String selectActivityKeyByWorkflowId(@Param("id") Long id);

    OrderInfo selectOrderInfo(@Param("id") Long id, @Param("tableName") String tableName);

    Integer selectUseWorkflowVersion(@Param("companyId") Long companyId);

    void updateWorkflowVersion(@Param("companyId") Long companyId);

    void updateOrderTypeStatus(@Param("tableName") String tableName, @Param("companyId") Long companyId);

    Boolean selectOrderEnableWorkflow(@Param("bizType") Integer bizType, @Param("companyId") Long companyId, @Param("orderType") Integer orderType);

    void updateOrderTypeEnableWorkflow(@Param("bizType") Integer bizType, @Param("companyId") Long companyId, @Param("orderType") Integer orderType, @Param("en") Integer en);

    List<DingOrderTypeExt> selectOrderTypeExt(@Param("tableName") String tableName, @Param("ids") List<Long> ids);

}
