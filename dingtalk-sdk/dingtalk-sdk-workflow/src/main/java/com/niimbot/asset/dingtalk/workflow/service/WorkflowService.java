package com.niimbot.asset.dingtalk.workflow.service;

import com.aliyun.dingtalkworkflow_1_0.models.DeleteProcessHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.DeleteProcessRequest;
import com.aliyun.dingtalkworkflow_1_0.models.DeleteProcessResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.niimbot.asset.dingtalk.base.config.CorpTokenManage;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflow;
import com.niimbot.asset.dingtalk.base.dto.EmpInfo;
import com.niimbot.asset.dingtalk.base.model.DingCorp;
import com.niimbot.asset.dingtalk.base.model.DingOrderType;
import com.niimbot.asset.dingtalk.base.restops.RestOps;
import com.niimbot.asset.dingtalk.workflow.mapper.AdapterMapper;
import com.niimbot.asset.dingtalk.workflow.mapper.DingOrderTypeMapper;
import com.niimbot.asset.dingtalk.workflow.mapper.DingWorkflowBusinessMapper;
import com.niimbot.asset.dingtalk.workflow.model.DingProcInstMapping;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
public abstract class WorkflowService {

    public static final String NAME = "-精臣云资产";
    public static final String ASSET = "asset";
    public static final String MATERIAL = "material";
    public static final String PRODUCT = "product";
    public static final String STANDARD = "standard";
    public static final List<String> ALLOW = Arrays.asList(ASSET, MATERIAL, PRODUCT, STANDARD);
    public static final Integer MAX_COMPONENT = 20;
    public static final Long DURATION = TimeUnit.SECONDS.toMillis(20L);
    public static final List<Integer> ALLOW_SEND_TASK_TYPES = Arrays.asList(3, 4, 5, 6);
    public static final Logger log = LoggerFactory.getLogger(WorkflowService.class);
    public static final ThreadPoolExecutor THREAD_POOL = AssetThreadPoolExecutorManager.newThreadPool("DingWorkflow", 5, 10, 15);

    @Resource
    protected CorpTokenManage corpTokenManage;

    @Resource
    protected DingOrderTypeMapper dingOrderTypeMapper;

    @Resource
    protected AdapterMapper adapterMapper;

    @Resource
    protected RedisService redisService;

    @Resource
    protected DingWorkflowBusinessMapper dingWorkflowBusinessMapper;

    @Resource
    protected RedissonClient redissonClient;

    @Resource
    protected com.aliyun.dingtalkworkflow_1_0.Client workflowClient;

    protected Map<String, Object> headers(String corpId) {
        String token = corpTokenManage.getV1CorpAccessToken(corpId);
        Map<String, Object> map = new HashMap<>(1);
        map.put("x-acs-dingtalk-access-token", token);
        return map;
    }

    protected DingUserInfo getDingUserInfo(Long empId, Long depId) {
        String dingUserId = adapterMapper.selectDingUserIdByEmpIdOrElseThrow(empId);
        String dingUnionId = adapterMapper.selectDingUnionIdByEmpIdOrElseThrow(empId);
        if (Objects.isNull(depId)) {
            DingUserInfo dingUserInfo = new DingUserInfo();
            dingUserInfo.setUserId(dingUserId);
            dingUserInfo.setUnionId(dingUnionId);
            return dingUserInfo;
        }
        String dingOrgId = adapterMapper.selectDingOrgIdByAssetOrgId(depId);
        if (StrUtil.isBlank(dingOrgId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "员工组织不存在");
        }
        return new DingUserInfo(dingUserId, dingOrgId, dingUnionId);
    }

    protected List<String> convertDingUserIds(List<Long> empIds) {
        return adapterMapper.selectDingUserIdsByAssetIdIn(empIds);
    }

    protected String convertDingUserId(Long empId) {
        return adapterMapper.selectDingUserId(empId);
    }

    protected List<EmpInfo> convertAssetEmp(Set<String> userIds, Long companyId) {
        return adapterMapper.selectAssetEmpByDingUserIdsIn(companyId, new ArrayList<>(userIds));
    }

    protected DingFormMapping getDingFormMapping(DingWorkflow dingWorkflow) {
        DingCorp dingCorp = adapterMapper.selectDingCorpByCompanyIdOrElseThrow(dingWorkflow.getCompanyId());
        String type = dingWorkflow.getBizType();
        if (PRODUCT.equalsIgnoreCase(dingWorkflow.getBizType())) {
            type = "purchase";
        }
        Long orderTypeId = adapterMapper.selectOrderTypeIdOrElseThrow(type, dingWorkflow.getCompanyId(), dingWorkflow.getOrderType());
        dingWorkflow.setOrderTypeId(orderTypeId);
        DingOrderType dingOrderType = dingOrderTypeMapper.selectByIfNotExistThrow(orderTypeId, dingWorkflow.getWay());
        return new DingFormMapping(dingWorkflow.getOrderTypeId(), dingCorp.getCorpId(), dingOrderType.getProcessCode(), dingCorp.getAgentId());
    }

    protected DingProcInstMapping getDingWorkflowProcInstId(String assetProcInstId) {
        return adapterMapper.selectDingProcInstIdByAssetProcInstId(assetProcInstId);
    }

    /**
     * 删除表单模板
     *
     * @param code 模板code
     * @return true
     */
    public Boolean deleteWorkflowForm(String cropId, String code) {
        DeleteProcessRequest request = new DeleteProcessRequest().setProcessCode(code).setCleanRunningTask(false);
        DeleteProcessResponse response = RestOps.handleOrThrow(() -> workflowClient.deleteProcessWithOptions(request, DeleteProcessHeaders.build(headers(cropId)), new RuntimeOptions()));
        return Objects.nonNull(response) && StrUtil.isNotBlank(response.getBody().getResult().getProcessCode());
    }

    public Integer getUseVersion(Long companyId) {
        return adapterMapper.selectUseWorkflowVersion(companyId);
    }

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DingUserInfo {
        private String userId;
        private Integer depId;
        private String unionId;
        private String userName;
        private String depName;

        public DingUserInfo(String userId, String depId, String unionId) {
            this.userId = userId;
            this.depId = Integer.parseInt(depId);
            this.unionId = unionId;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DingFormMapping {
        private Long orderTypeId;
        private String corpId;
        private String processCode;
        private Long agentId;
    }

}
