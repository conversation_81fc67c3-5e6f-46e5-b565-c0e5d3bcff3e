package com.niimbot.asset.dingtalk.workflow.model;

import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 钉钉审批流程常量
 *
 * <AUTHOR>
 */
public interface WorkflowConstant {

    interface Cache {
        String WORKFLOW_NODE = "ding_workflow_";
    }

    /**
     * 钉钉审批表单控件类型
     */
    interface FieldType {
        /**
         * 单行文本
         */
        String TEXT = "TextField";

        /**
         * 多行文本
         */
        String TEXTAREA = "TextareaField";

        /**
         * 数字
         */
        String NUMBER = "NumberField";
        /**
         * 单选
         */
        String SELECT = "DDSelectField";
        /**
         * 多选
         */
        String MULTI_SELECT = "DDMultiSelectField";
        /**
         * 日期
         */
        String DATE = "DDDateField";
        /**
         * 时间区间
         */
        String DATE_RANGE = "DDDateRangeField";
        /**
         * 文字说明
         */
        String TEXT_NOTE = "TextNote";
        /**
         * 电话
         */
        String PHONE = "PhoneField";
        /**
         * 图片
         */
        String PHOTO = "DDPhotoField";
        /**
         * 金额
         */
        String MONEY = "MoneyField";
        /**
         * 明细
         */
        String TABLE = "TableField";
        /**
         * 附件
         */
        String ATTACHMENT = "DDAttachment";
        /**
         * 联系人
         */
        String INNER_CONTACT = "InnerContactField";
        /**
         * 部门
         */
        String DEPARTMENT = "DepartmentField";
        /**
         * 关联审批单
         */
        String RELATE = "RelateField";
        /**
         * 省市区
         */
        String ADDRESS = "AddressField";
        /**
         * 评分
         */
        String STAR_RATING = "StarRatingField";
    }

    /**
     * 钉钉审批请求参数
     */
    interface Params {
        // 表单组件
        String CHILDREN = "children";
        String COMPONENT_TYPE = "componentType";
        String PROPS = "props";
        // 表单组件属性
        String ADDRESS_MODEL = "addressModel";

        String ALIGN = "align";

        String ASYNC_CONDITION = "asyncCondition";

        // List<AvaliableTemplate> availableTemplates;

        String BIZ_ALIAS = "bizAlias";

        String BIZ_TYPE = "bizType";

        String CHOICE = "choice";

        String COMMON_BIZ_TYPE = "commonBizType";

        String COMPONENT_ID = "componentId";

        String CONTENT = "content";

        // FormDataSource dataSource;

        String DISABLED = "disabled";

        String DURATION = "duration";

        String FORMAT = "format";

        String FORMULA = "formula";

        String INVISIBLE = "invisible";

        String LABEL = "label";

        String LIMIT = "limit";

        String LINK = "link";

        String MODE = "mode";

        String MULTIPLE = "multiple";

        // List<SelectOption> options;

        String PLACEHOLDER = "placeholder";

        String PRINT = "print";

        String REQUIRED = "required";

        // List<FormComponentProps.FormComponentPropsStatField> statField;

        String TABLE_VIEW_MODE = "tableViewMode";

        String UNIT = "unit";

        String UPPER = "upper";

        String VERTICAL_PRINT = "verticalPrint";
        // 表单值
        String ID = "id";
        String EXT_VALUE = "extValue";
        String NAME = "name";
        String VALUE = "value";
    }

    interface Vale {
        /**
         * 自选节点类型
         */
        String TARGET_SELECT = "target_select";

        /**
         * 全公司
         */
        String ALL_STAFF = "allStaff";

        /**
         * 指定角色
         */
        String LABELS = "labels";

        /**
         * 指定成员
         */
        String APPROVALS = "approvals";

        String URL = "url";

        String STATUS = "status";

        String PROCESS_INSTANCE_ID = "processInstanceId";

        String RESULT = "result";

        String COMPLETED = "completed";

        String AGREE = "agree";

        String REFUSE = "refuse";

        String SYNC_ACTION = "syncAction";

        String ISV_BPMS_CANCEL = "isv_bpms_cancel";

        String ISV_BPMS = "isv_bpms";

        String FORM_VALUES_OMITTED = "formValuesOmitted";

        String OPERATION_RECORDS = "operationRecords";

        String TYPE = "type";

        String TERMINATE_PROCESS_INSTANCE = "TERMINATE_PROCESS_INSTANCE";

        String APPROVER = "approver";
    }

    interface BizField {

        /**
         * 资产业务表单固定字段
         */
        List<String> ASSETS_FIXED_FIELDS = Stream.of("assetCode", "assetName", "assetCategory", "brand", "model", "unit", "price", "storageArea", "orgOwner", "managerOwner", "useOrg", "usePerson").collect(Collectors.toList());

        /**
         * 耗材业务表单固定字段
         */
        List<String> MATERIALS_FIXED_FIELDS = Stream.of("materialCode", "materialName", "materialCategory", "model", "barCode", "brand").collect(Collectors.toList());

        /**
         * 产品业务表单固定字段
         */
        List<String> STANDARD_FIXED_FIELDS = Stream.of("name", "brand", "model", "unit").collect(Collectors.toList());

        static WorkflowField createByField() {
            return new WorkflowField("createBy", FormFieldCO.TEXT_INPUT, "创建人", true);
        }

        static WorkflowField createTimeField() {
            return new WorkflowField("createTime", FormFieldCO.DATETIME, "创建时间", false);
        }

        /**
         * 单据固定字段
         */
        Map<Integer, OrderFixedForm> ORDER_FIXED_FIELDS = new HashMap<Integer, OrderFixedForm>() {{
            // 资产领用单： 领用日期、领用组织、创建人、创建时间
            this.put(OrderFormTypeEnum.RECEIVE.getCode(), new OrderFixedForm("资产领用单", "资产领用单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("receiveDate", FormFieldCO.DATETIME, "领用日期", false));
                this.add(new WorkflowField("receiveOrg", FormFieldCO.TEXT_INPUT, "领用组织", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产退还单：退还日期、创建人、创建时间
            this.put(OrderFormTypeEnum.RETURN.getCode(), new OrderFixedForm("资产退还单", "资产退还单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("returnDate", FormFieldCO.DATETIME, "退还日期", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产借用单：借用日期、借用组织、创建人、创建时间
            this.put(OrderFormTypeEnum.BORROW.getCode(), new OrderFixedForm("资产借用单", "资产借用单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("borrowDate", FormFieldCO.DATETIME, "借用日期", false));
                this.add(new WorkflowField("borrowOrg", FormFieldCO.TEXT_INPUT, "借用组织", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产归还单  归还日期、创建人、创建时间
            this.put(OrderFormTypeEnum.BACK.getCode(), new OrderFixedForm("资产归还单", "资产归还单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("backDate", FormFieldCO.DATETIME, "归还日期", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产调拨单  调拨日期、调出组织、调入所属组织、创建人、创建时间
            this.put(OrderFormTypeEnum.ALLOCATE.getCode(), new OrderFixedForm("资产调拨单", "资产调拨单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("allocateDate", FormFieldCO.DATETIME, "调拨日期", false));
                this.add(new WorkflowField("allocateOutOrg", FormFieldCO.TEXT_INPUT, "调出组织", true));
                this.add(new WorkflowField("allocateInOrg", FormFieldCO.TEXT_INPUT, "调入所属组织", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产报修单  报修人、报修日期、报修内容、创建人、创建时间
            this.put(OrderFormTypeEnum.REPAIR_REPORT.getCode(), new OrderFixedForm("资产报修单", "资产报修单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("sendRepairUser", FormFieldCO.TEXT_INPUT, "报修人", true));
                this.add(new WorkflowField("repairDate", FormFieldCO.DATETIME, "报修日期", false));
                this.add(new WorkflowField("repairContent", FormFieldCO.TEXT_INPUT, "报修内容", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产维修单  维修日期、当前维修状态、创建人、创建时间
            this.put(OrderFormTypeEnum.REPAIR.getCode(), new OrderFixedForm("资产维修单", "资产维修单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("repairFinishDate", FormFieldCO.DATETIME, "维修日期", false));
                this.add(new WorkflowField("repairStatus", FormFieldCO.TEXT_INPUT, "当前维修状态", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产处置单  处置日期、处置类型、创建人、创建时间
            this.put(OrderFormTypeEnum.DISPOSE.getCode(), new OrderFixedForm("资产处置单", "资产处置单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("disposeDate", FormFieldCO.DATETIME, "处置日期", false));
                this.add(new WorkflowField("disposeType", FormFieldCO.TEXT_INPUT, "处置类型", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产变更单  创建人、创建时间
            this.put(OrderFormTypeEnum.CHANGE.getCode(), new OrderFixedForm("资产变更单", "资产变更单", new ArrayList<WorkflowField>() {{
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产保养单  本次保养日期、下次保养日期、创建人、创建时间
            this.put(OrderFormTypeEnum.MAINTAIN.getCode(), new OrderFixedForm("资产保养单", "资产保养单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("currentMaintainDate", FormFieldCO.DATETIME, "本次保养日期", false));
                this.add(new WorkflowField("nextMaintainDate", FormFieldCO.DATETIME, "下次保养日期", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 资产入库单  入库类型、入库日期、创建人、创建时间
            this.put(OrderFormTypeEnum.STORE.getCode(), new OrderFixedForm("资产入库单", "资产入库单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("rkType", FormFieldCO.TEXT_INPUT, "入库类型", false));
                this.add(new WorkflowField("storeDate", FormFieldCO.DATETIME, "入库日期", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 耗材入库单  入库类型、入库日期、入库仓库、创建人、创建时间
            this.put(OrderFormTypeEnum.RK.getCode(), new OrderFixedForm("耗材入库单", "耗材入库单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("rkType", FormFieldCO.TEXT_INPUT, "入库类型", false));
                this.add(new WorkflowField("storageTime", FormFieldCO.DATETIME, "入库日期", false));
                this.add(new WorkflowField("inRepo", FormFieldCO.TEXT_INPUT, "入库仓库", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 耗材出库单  出库类型、出库仓库、出库日期、领用组织、创建人、创建时间
            this.put(OrderFormTypeEnum.CK.getCode(), new OrderFixedForm("耗材出库单", "耗材出库单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("ckType", FormFieldCO.TEXT_INPUT, "出库类型", false));
                this.add(new WorkflowField("outRepo", FormFieldCO.TEXT_INPUT, "出库仓库", true));
                this.add(new WorkflowField("outRepoTime", FormFieldCO.DATETIME, "出库日期", false));
                this.add(new WorkflowField("receiveOrg", FormFieldCO.TEXT_INPUT, "领用组织", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 耗材领用申请单  领用组织、创建人、创建时间
            this.put(OrderFormTypeEnum.LY.getCode(), new OrderFixedForm("耗材领用单", "耗材领用单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("receiveOrg", FormFieldCO.TEXT_INPUT, "领用组织", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 耗材调拨单  调拨日期、调出仓库、调入仓库、创建人、创建时间
            this.put(OrderFormTypeEnum.DB.getCode(), new OrderFixedForm("耗材调拨单", "耗材调拨单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("allocateDate", FormFieldCO.DATETIME, "调拨日期", false));
                this.add(new WorkflowField("outRepo", FormFieldCO.TEXT_INPUT, "调出仓库", true));
                this.add(new WorkflowField("inRepo", FormFieldCO.TEXT_INPUT, "调入仓库", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 耗材库存调整单  调整日期、调整仓库、调整人、调整原因、创建人、创建时间
            this.put(OrderFormTypeEnum.TZ.getCode(), new OrderFixedForm("耗材调整单", "耗材调整单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("tzDate", FormFieldCO.DATETIME, "调整日期", false));
                this.add(new WorkflowField("tzRepo", FormFieldCO.TEXT_INPUT, "调整仓库", true));
                this.add(new WorkflowField("tzUser", FormFieldCO.TEXT_INPUT, "调整人", true));
                this.add(new WorkflowField("reason", FormFieldCO.TEXT_INPUT, "调整原因", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 耗材报损单  报损日期、报损组织、报损原因、创建人、创建时间
            this.put(OrderFormTypeEnum.BS.getCode(), new OrderFixedForm("耗材报损单", "耗材报损单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("bsDate", FormFieldCO.DATETIME, "报损日期", false));
                this.add(new WorkflowField("bsOrg", FormFieldCO.TEXT_INPUT, "报损组织", true));
                this.add(new WorkflowField("reason", FormFieldCO.TEXT_INPUT, "报损原因", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 耗材退库单  关联单据、退库日期、退库备注、创建人、创建时间
            this.put(OrderFormTypeEnum.TK.getCode(), new OrderFixedForm("耗材退库单", "耗材退库单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("ckOrderNo", FormFieldCO.TEXT_INPUT, "关联单据", false));
                this.add(new WorkflowField("tkDate", FormFieldCO.DATETIME, "退库日期", false));
                this.add(new WorkflowField("remark", FormFieldCO.TEXT_INPUT, "退库备注", false));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 采购申请单  申请日期、申请组织、创建人、创建时间
            this.put(OrderFormTypeEnum.PURCHASE_APPLY.getCode(), new OrderFixedForm("采购申请单", "采购申请单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("applyDate", FormFieldCO.DATETIME, "申请日期", false));
                this.add(new WorkflowField("applyOrg", FormFieldCO.TEXT_INPUT, "申请组织", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
            // 采购单  采购类型、采购日期、供应商、采购执行公司、创建人、创建时间
            this.put(OrderFormTypeEnum.PURCHASE_ORDER.getCode(), new OrderFixedForm("采购订单", "采购订单", new ArrayList<WorkflowField>() {{
                this.add(new WorkflowField("mode", FormFieldCO.TEXT_INPUT, "采购类型", false));
                this.add(new WorkflowField("purchaseDate", FormFieldCO.DATETIME, "采购日期", false));
                this.add(new WorkflowField("supplier", FormFieldCO.TEXT_INPUT, "供应商", true));
                this.add(new WorkflowField("executeOrg", FormFieldCO.TEXT_INPUT, "采购执行公司", true));
                this.add(createByField());
                this.add(createTimeField());
            }}));
        }};
    }

    interface BizActivitiKey {

        List<String> ASSETS = Stream.of("receive_workflow", "return_workflow", "borrow_workflow", "back_workflow", "allocate_workflow", "repair_report_workflow", "repair_workflow", "dispose_workflow", "change_workflow", "store_workflow", "maintain_workflow").collect(Collectors.toList());

        List<String> MATERIALS = Stream.of("material_receive_workflow", "material_rk_workflow", "material_ck_workflow", "material_tz_workflow", "material_db_workflow", "material_bs_workflow", "material_tk_workflow").collect(Collectors.toList());

        List<String> PURCHASES = Stream.of("purchase_apply_workflow", "purchase_order_workflow").collect(Collectors.toList());

        /**
         * 获取单据业务类型
         *
         * @param activitiKey 流程key
         * @return biz
         */
        static String getOrderBizType(String activitiKey) {
            if (ASSETS.contains(activitiKey)) {
                return "asset";
            }
            if (MATERIALS.contains(activitiKey)) {
                return "material";
            }
            if (PURCHASES.contains(activitiKey)) {
                return "purchase";
            }
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不支持的单据类型");
        }

        static int getOrderType(String activitiKey) {
            switch (activitiKey) {
                case "receive_workflow":
                    return OrderFormTypeEnum.RECEIVE.getCode();
                case "return_workflow":
                    return OrderFormTypeEnum.RETURN.getCode();
                case "borrow_workflow":
                    return OrderFormTypeEnum.BORROW.getCode();
                case "back_workflow":
                    return OrderFormTypeEnum.BACK.getCode();
                case "allocate_workflow":
                    return OrderFormTypeEnum.ALLOCATE.getCode();
                case "repair_report_workflow":
                    return OrderFormTypeEnum.REPAIR_REPORT.getCode();
                case "repair_workflow":
                    return OrderFormTypeEnum.REPAIR.getCode();
                case "dispose_workflow":
                    return OrderFormTypeEnum.DISPOSE.getCode();
                case "change_workflow":
                    return OrderFormTypeEnum.CHANGE.getCode();
                case "purchase_apply_workflow":
                    return OrderFormTypeEnum.PURCHASE_APPLY.getCode();
                case "material_receive_workflow":
                    return OrderFormTypeEnum.LY.getCode();
                case "material_rk_workflow":
                    return OrderFormTypeEnum.RK.getCode();
                case "material_ck_workflow":
                    return OrderFormTypeEnum.CK.getCode();
                case "material_tz_workflow":
                    return OrderFormTypeEnum.TZ.getCode();
                case "material_db_workflow":
                    return OrderFormTypeEnum.DB.getCode();
                case "material_bs_workflow":
                    return OrderFormTypeEnum.BS.getCode();
                case "material_tk_workflow":
                    return OrderFormTypeEnum.TK.getCode();
                case "store_workflow":
                    return OrderFormTypeEnum.STORE.getCode();
                case "maintain_workflow":
                    return OrderFormTypeEnum.MAINTAIN.getCode();
                case "purchase_order_workflow":
                    return OrderFormTypeEnum.PURCHASE_ORDER.getCode();
                default:
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不支持的单据类型");
            }
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class OrderFixedForm {
        private String name;
        private String desc;
        private List<WorkflowField> fields;
    }

    interface TableName {

        List<String> M1 = Arrays.asList("1", "2", "3", "4", "5", "8", "9");

        static String getOrderTableName(String orderType) {
            if (M1.contains(orderType)) {
                return "as_order";
            }
            if ("13".equals(orderType)) {
                return "as_store_order";
            }
            // 报修
            if ("6".equals(orderType)) {
                return "as_repair_report_order";
            }
            // 维修
            if ("7".equals(orderType)) {
                return "as_repair_order";
            }
            // 保养
            if ("11".equals(orderType)) {
                return "as_maintain_order";
            }
            // 耗材
            if ("33".equals(orderType)) {
                return "as_material_ly_order";
            }
            if ("31".equals(orderType)) {
                return "as_material_rk_order";
            }
            if ("32".equals(orderType)) {
                return "as_material_ck_order";
            }
            if ("34".equals(orderType)) {
                return "as_material_tz_order";
            }
            if ("35".equals(orderType)) {
                return "as_material_db_order";
            }
            if ("36".equals(orderType)) {
                return "as_material_bs_order";
            }
            if ("37".equals(orderType)) {
                return "as_material_tk_order";
            }
            // 采购
            if ("10".equals(orderType)) {
                return "as_purchase_apply";
            }
            if ("12".equals(orderType)) {
                return "as_purchase_order";
            }
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前单据" + orderType + "没有对应得表");
        }

    }

}
