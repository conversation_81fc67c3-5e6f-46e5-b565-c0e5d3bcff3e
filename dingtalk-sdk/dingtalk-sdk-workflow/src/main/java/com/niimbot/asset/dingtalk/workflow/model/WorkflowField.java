package com.niimbot.asset.dingtalk.workflow.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.FieldType.*;
import static com.niimbot.asset.dingtalk.workflow.model.WorkflowConstant.Params.*;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowField implements Serializable {

    private String code;

    private String type;

    private String id;

    private Boolean isRequired = false;

    private String placeholder = "请输入";

    private String label;

    private Boolean hasText = false;

    private String textSuffix = "Text";

    public WorkflowField(FormFieldCO field) {
        this.id = String.valueOf(field.getId());
        this.label = field.getFieldName();
        this.code = field.getFieldCode();
        this.type = field.getFieldType();
        this.isRequired = field.requiredProps();
        this.hasText = field.hasTranslation();
        if (this.hasText) {
            this.textSuffix = field.getFieldProps().getJSONObject(FormFieldCO.FIELD_PROPERTY_EXT_PROPS).getString(FormFieldCO.FIELD_PROPERTY_TRANSLATION);
        }
    }

    public WorkflowField(String code, String id, String label) {
        this.code = code;
        this.type = FormFieldCO.TEXT_INPUT;
        this.id = id;
        this.label = label;
    }

    public WorkflowField(String code, String type, String label, Boolean hasText) {
        this.code = code;
        this.type = type;
        this.label = label;
        this.hasText = hasText;
    }

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public FormComponent convertToComponent() {
        return FIELD_TYPE_MAPPINGS.get(this.getType()).apply(this);
    }

    public Map<String, Object> componentValuesMap(JSONObject data) {
        Map<String, Object> map = new HashMap<>(6);
//        map.put(ID, this.id);
        map.put(NAME, this.label);
        if (hasText && data.containsKey(code + textSuffix)) {
            map.put(VALUE, data.getString(code + textSuffix));
        } else {
            map.put(VALUE, StrUtil.isBlank(data.getString(code)) ? "" : data.getString(code));
        }
        // 数组转移特殊处理
        if (FormFieldCO.IMAGES.equals(type) && data.containsKey(code) && CollUtil.isNotEmpty(data.getJSONArray(code))) {
            map.put(VALUE, data.getJSONArray(code).toJSONString());
        }
        // 日期特殊处理
        if (("expectedArrivalDate".equals(code) || FormFieldCO.DATETIME.equals(type)) && data.containsKey(code)) {
            Long time = data.getLong(code);
            if (Objects.nonNull(time)) {
                LocalDateTime localDateTime = new Date(time).toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
                String format = localDateTime.format(DATE_TIME_FORMATTER);
                map.put(VALUE, format);
            }
        }
        // TODO: 2022/11/17  附件类型需要接入钉钉存储
        if (FormFieldCO.FILES.equals(type) && data.containsKey(code)) {
            if (CollUtil.isNotEmpty(data.getJSONArray(code))) {
                JSONArray files = data.getJSONArray(code);
                StringJoiner joiner = new StringJoiner(" \n");
                joiner.add("如需查看，请复制完整文件链接至浏览器打开。");
                files.forEach(v -> {
                    JSONObject json = (JSONObject) JSONObject.toJSON(v);
                    if (json.containsKey("url")) {
                        String url = json.getString("url");
                        joiner.add(url);
                    }
                });
                if (joiner.length() > 0) {
                    map.put(VALUE, joiner.toString());
                }
            } else {
                map.put(VALUE, "");
            }
        }
        // 多选类型处理
        if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(type) && data.containsKey(code)) {
            String value = data.getJSONArray(code).stream().map(Object::toString).collect(Collectors.joining("、"));
            map.put(VALUE, value);
        }
        return map;
    }

    public ProcessForecastRequest.ProcessForecastRequestFormComponentValues processForecast(JSONObject data) {
        Map<String, Object> map = componentValuesMap(data);
        try {
            return ProcessForecastRequest.ProcessForecastRequestFormComponentValues.build(map);
        } catch (Exception e) {
            throw new RuntimeException("转换参数异常");
        }
    }

    public StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues startProcessInstance(JSONObject data) {
        Map<String, Object> map = componentValuesMap(data);
        try {
            return StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues.build(map);
        } catch (Exception e) {
            throw new RuntimeException("转换参数异常");
        }
    }

    public SaveIntegratedInstanceRequest.SaveIntegratedInstanceRequestFormComponentValueList ownStartProcessInstance(JSONObject data) {
        Map<String, Object> map = componentValuesMap(data);
        try {
            return SaveIntegratedInstanceRequest.SaveIntegratedInstanceRequestFormComponentValueList.build(map);
        } catch (Exception e) {
            throw new RuntimeException("转换参数异常");
        }
    }

    private static FormComponentProps componentProps(WorkflowField workflowField) {
        return new FormComponentProps().setComponentId(workflowField.getId())
                .setLabel(workflowField.getLabel())
                .setPlaceholder(workflowField.getPlaceholder())
                .setRequired(workflowField.getIsRequired());
    }

    private static final Map<String, Function<WorkflowField, FormComponent>> FIELD_TYPE_MAPPINGS = new ConcurrentHashMap<>();

    static {
        // 单行文本
        Function<WorkflowField, FormComponent> textComponent = workflowField -> {
            FormComponent component = new FormComponent().setComponentType(TEXT);
            return component.setProps(componentProps(workflowField));
        };
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.TEXT_INPUT, textComponent);
        // 多行文本
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.TEXT_AREA, workflowField -> {
            FormComponent component = new FormComponent().setComponentType(TEXTAREA);
            return component.setProps(componentProps(workflowField));
        });
        // 数字
        // FIELD_TYPE_MAPPINGS.put(FormFieldCO.NUMBER_INPUT, workflowField -> {
        //     FormComponent component = new FormComponent().setComponentType(NUMBER);
        //     FormComponentProps props = componentProps(workflowField);
        //     props.setUnit("元");
        //     return component;
        // });
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.NUMBER_INPUT, textComponent);
        Function<WorkflowField, FormComponent> selectComponent = workflowField -> {
            FormComponent component = new FormComponent().setComponentType(SELECT);
            SelectOption option = new SelectOption().setKey("").setValue("");
            FormComponentProps props = componentProps(workflowField);
            props.setPlaceholder(StrUtil.isBlank(workflowField.getPlaceholder()) ? "请选择" : workflowField.getPlaceholder());
            props.setOptions(Collections.singletonList(option));
            return component.setProps(props);
        };
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.SELECT_DROPDOWN, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.MULTI_SELECT_DROPDOWN, textComponent);
        // FIELD_TYPE_MAPPINGS.put(FormFieldCO.SELECT_DROPDOWN, selectComponent);
        // FIELD_TYPE_MAPPINGS.put(FormFieldCO.MULTI_SELECT_DROPDOWN, workflowField -> {
        //     FormComponent component = new FormComponent().setComponentType(MULTI_SELECT);
        //     FormComponentProps props = componentProps(workflowField).setPlaceholder(StrUtil.isBlank(workflowField.getPlaceholder()) ? "请选择" : workflowField.getPlaceholder());
        //     return component.setProps(props);
        // });
        // FIELD_TYPE_MAPPINGS.put("date", workflowField -> {
        //     FormComponent component = new FormComponent().setComponentType(DATE);
        //     FormComponentProps props = componentProps(workflowField);
        //     props.setUnit("小时").setFormat("yyyy-MM-dd HH:mm");
        //     return component.setProps(props);
        // });
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.DATETIME, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.IMAGES, workflowField -> {
            FormComponent component = new FormComponent().setComponentType(PHOTO);
            FormComponentProps props = componentProps(workflowField);
            props.setPlaceholder(null);
            return component.setProps(props);
        });
        // FIELD_TYPE_MAPPINGS.put("files", workflowField -> {
        //     FormComponent component = new FormComponent().setComponentType(ATTACHMENT);
        //     FormComponentProps props = componentProps(workflowField).setPlaceholder(null);
        //     return component.setProps(props);
        // });
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.FILES, FIELD_TYPE_MAPPINGS.get(FormFieldCO.TEXT_AREA));
        // FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_EMP, workflowField -> {
        //     FormComponent component = new FormComponent().setComponentType(INNER_CONTACT);
        //     FormComponentProps props = componentProps(workflowField);
        //     props.setChoice("0");
        //     return component.setProps(props);
        // });
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_EMP, textComponent);
        // FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_ORG, workflowField -> {
        //     FormComponent component = new FormComponent().setComponentType(DEPARTMENT);
        //     FormComponentProps props = componentProps(workflowField);
        //     props.setMultiple(false);
        //     return component.setProps(props);
        // });
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_ORG, textComponent);
        // yzc_area、yzc_supplier、yzc_asset_cate、yzc_material_cate、yzc_repository 业务单选
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_AREA, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_SUPPLIER, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_ASSET_CATE, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_MATERIAL_CATE, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_ASSET_SERIALNO, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_MATERIAL_SERIALNO, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_SERIALNO, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_REPOSITORY, textComponent);
        // yzc_asset_serialNo、yzc_material_serialNo、yzc_asset_name、yzc_material_name 单行文本
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_ASSET_NAME, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_MATERIAL_NAME, textComponent);
        FIELD_TYPE_MAPPINGS.put(FormFieldCO.YZC_ASSOCIATION_TABLE, textComponent);
    }
}
