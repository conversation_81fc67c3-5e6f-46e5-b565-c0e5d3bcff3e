package com.niimbot.asset.dingtalk.workflow.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DingProcInstMapping implements Serializable {
    private Long orderId;
    private String procInsId;
    private String orderType;
    private Boolean isApproved = false;
    private JSONObject body;
    private LocalDateTime createTime;
    private Long companyId;
    private Long createBy;
}
