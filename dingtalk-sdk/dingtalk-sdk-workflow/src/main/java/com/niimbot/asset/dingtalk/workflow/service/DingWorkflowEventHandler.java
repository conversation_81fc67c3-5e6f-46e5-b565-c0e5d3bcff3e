package com.niimbot.asset.dingtalk.workflow.service;

import com.niimbot.asset.dingtalk.workflow.event.DingWorkflowEvent;

import java.util.Set;

/**
 * <AUTHOR>
 */
public abstract class DingWorkflowEventHandler {

    /**
     * 能处理的单据类型
     *
     * @return set
     */
    public abstract Set<String> orderTypes();

    /**
     * 业务数据处理
     *
     * @param event 事件
     * @return true or false
     */
    public abstract Boolean handle(DingWorkflowEvent event);
}
