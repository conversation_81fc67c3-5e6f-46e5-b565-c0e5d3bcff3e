package com.niimbot.asset.dingtalk.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflowFormInfo;
import com.niimbot.asset.dingtalk.base.model.DingOrderType;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@EnableDataPerm(excludeMethodName = {"selectOrderTypeId", "selectCompanyOldForm", "selectForRefreshOaForm"})
public interface DingOrderTypeMapper extends BaseMapper<DingOrderType> {

    /**
     * select by id
     *
     * @param id id
     * @return 钉钉单据类型
     */
    default DingOrderType selectByIfNotExistThrow(Serializable id, Integer way) {
        DingOrderType dingOrderType = this.selectByIdAndWay(id, way);
        if (Objects.isNull(dingOrderType)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "钉钉单据类型表单未创建");
        }
        return dingOrderType;
    }

    default DingOrderType selectByIdAndWay(Serializable id, Integer way) {
        return this.selectOne(
                Wrappers.lambdaQuery(DingOrderType.class)
                        .eq(DingOrderType::getOrderTypeId, id)
                        .eq(DingOrderType::getWay, way)
        );
    }

    /**
     * selectByActivitiKey
     *
     * @param companyId companyId
     * @return dingOrderType
     */
    DingOrderType selectByBizType(@Param("orderTypeId") Long orderTypeId, @Param("way") Integer way);

    Long selectOrderTypeId(@Param("bizType") String bizType, @Param("companyId") Long companyId, @Param("orderType") Integer orderType);

    default List<DingWorkflowFormInfo> selectCompanyOldForm(Long companyId) {
        List<Integer> list = Arrays.asList(1, 2, 3);
        List<DingWorkflowFormInfo> infos = new ArrayList<>();
        for (Integer v : list) {
            List<DingWorkflowFormInfo> k = selectCompanyOldForm(companyId, v);
            infos.addAll(k);
        }
        return infos;
    }

    List<DingWorkflowFormInfo> selectCompanyOldForm(@Param("companyId") Long companyId, @Param("em") Integer em);

    List<DingOrderType> selectForRefreshOaForm(@Param("companyId") Long companyId);

}
