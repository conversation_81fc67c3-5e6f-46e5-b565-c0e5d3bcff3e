<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dingtalk.workflow.mapper.DingOrderTypeMapper">


    <select id="selectByBizType" resultType="com.niimbot.asset.dingtalk.base.model.DingOrderType">
        SELECT * FROM ding_order_type WHERE way = #{way} AND order_type_id = #{orderTypeId}
    </select>

    <select id="selectOrderTypeId" resultType="java.lang.Long">
        <if test="bizType == 'asset'.toString()">
            SELECT id FROM as_order_type WHERE company_id  = #{companyId} AND type = #{orderType}
        </if>
        <if test="bizType == 'material'.toString()">
            SELECT id FROM as_material_order_type WHERE company_id = #{companyId} AND type = #{orderType}
        </if>
        <if test="bizType == 'purchase'.toString()">
            SELECT id FROM as_purchase_order_type WHERE company_id = #{companyId} AND type = #{orderType}
        </if>
    </select>

    <resultMap id="DingWorkflowFormResultMap" type="com.niimbot.asset.dingtalk.base.dto.DingWorkflowFormInfo">
        <id column="order_type_id" property="orderTypeId"/>
        <result column="company_id" property="companyId"/>
        <result column="process_code" property="processCode"/>
        <result column="way" property="way"/>
        <result column="type" property="type"/>
        <result column="biz_type" property="bizType"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
    </resultMap>

    <select id="selectCompanyOldForm" resultMap="DingWorkflowFormResultMap">
        <if test="em == 1">
            SELECT t1.*, t2.type, IF(t2.type = 10,'product','asset') AS biz_type, t2.`name`, t2.description AS `desc` FROM ding_order_type t1 LEFT JOIN as_order_type t2 ON t1.order_type_id = t2.id WHERE t1.company_id = #{companyId} AND t1.way = 1 AND t2.company_id = #{companyId} AND t2.type IS NOT NULL
        </if>
        <if test="em == 2">
            SELECT t1.*, t3.type, 'material' AS biz_type, t3.`name`, t3.description AS `desc` FROM ding_order_type t1 LEFT JOIN as_material_order_type t3 ON t1.order_type_id = t3.id WHERE t1.company_id = #{companyId} AND t1.way = 1 AND t3.company_id = #{companyId} AND t3.type IS NOT NULL
        </if>
        <if test="em == 3">
            SELECT t1.*, t4.type, IF(t4.type = 10,'product','asset') AS biz_type, t4.`name`, t4.description AS `desc` FROM ding_order_type t1 LEFT JOIN as_purchase_order_type t4 ON t1.order_type_id = t4.id WHERE t1.company_id = #{companyId} AND t1.way =1 AND t4.company_id = #{companyId} AND t4.type IS NOT NULL
        </if>
    </select>

    <select id="selectForRefreshOaForm" resultType="com.niimbot.asset.dingtalk.base.model.DingOrderType">
        SELECT * FROM ding_order_type WHERE way = 2
        <if test="companyId != null">
            AND company_id = #{companyId}
        </if>
    </select>
</mapper>
