<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.dingtalk.workflow.mapper.AdapterMapper">

    <select id="selectDingCorpByCompanyId" resultType="com.niimbot.asset.dingtalk.base.model.DingCorp">
        SELECT *
        FROM ding_corp
        WHERE company_id = #{companyId}
    </select>

    <resultMap id="orderFieldResult" type="com.niimbot.asset.dingtalk.workflow.model.WorkflowField">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="is_required" property="isRequired"/>
        <result column="name" property="label"/>
        <result column="has_text" property="hasText"/>
    </resultMap>

    <select id="selectAssetOrderFieldByCompanyIdAndType" resultMap="orderFieldResult">
        SELECT id, code, CAST(`type` AS CHAR) AS `type`, is_required, name, IF(text_type = 0, 0, 1) AS has_text
        FROM as_order_field
        WHERE company_id = #{companyId}
          AND order_type = #{type}
          AND is_show = 1
          AND is_delete = 0
        ORDER BY sort_num ASC
    </select>

    <select id="selectMaterialOrderFieldByCompanyIdAndType" resultMap="orderFieldResult">
        SELECT id, code, CAST(`type` AS CHAR) AS `type`, is_required, name, IF(ISNULL(`translate`), 0, 1) AS has_text
        FROM as_material_order_field
        WHERE company_id = #{companyId}
          AND order_type = #{type}
          AND is_show = 1
          AND is_delete = 0
        ORDER BY sort_num ASC
    </select>

    <select id="selectDingUserIdByAssetEmpId" resultType="java.lang.String">
        SELECT user_id
        FROM as_third_party_employee
        WHERE employee_id = #{empId}
    </select>

    <select id="selectOrgIdsByEmpId" resultType="java.lang.Long">
        SELECT org_id
        FROM as_user_org
        WHERE user_id = #{empId}
    </select>

    <select id="selectDingOrgIdByAssetOrgId" resultType="java.lang.String">
        SELECT external_org_id
        FROM as_org
        WHERE id = #{orgId}
          AND is_delete = 0
    </select>

    <select id="selectDingUserIdsByAssetIdIn" resultType="java.lang.String">
        SELECT user_id FROM as_third_party_employee WHERE employee_id IN
        <foreach collection="empIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectDingUserId" resultType="java.lang.String">
        SELECT user_id FROM as_third_party_employee WHERE employee_id = #{empId}
    </select>

    <select id="selectAssetEmpByDingUserIdsIn" resultType="com.niimbot.asset.dingtalk.base.dto.EmpInfo">
        SELECT t1.employee_id AS emp_id, t1.user_id, t2.emp_name
        FROM as_third_party_employee t1
        LEFT JOIN as_cus_employee t2 ON t1.employee_id = t2.id AND t1.company_id = t2.company_id
        WHERE t1.company_Id = #{companyId} AND t2.company_id = #{companyId} AND t1.user_id IN
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND t2.is_delete = 0
    </select>

    <select id="selectOrderTypeIdByCompanyIdAndOrderType" resultType="java.lang.Long">
        <if test="bizType == 'asset'">
            SELECT id FROM as_order_type WHERE company_id = #{companyId} AND type = #{orderType}
        </if>
        <if test="bizType == 'material'">
            SELECT id FROM as_material_order_type WHERE company_id = #{companyId} AND type = #{orderType}
        </if>
        <if test="bizType == 'purchase'">
            SELECT id FROM as_purchase_order_type WHERE company_id = #{companyId} AND type = #{orderType}
        </if>
    </select>

    <update id="updateApproveStatusByCondition">
        <if test="bizType == 'asset'">
            <choose>
                <when test="orderType == 10">
                    UPDATE as_purchase_apply SET approve_status = #{status} WHERE id = #{id}
                </when>
                <when test="orderType == 11">

                </when>
                <otherwise>
                    UPDATE as_order SET approve_status = #{status} WHERE id = #{id}
                </otherwise>
            </choose>
        </if>
        <if test="bizType == 'material'">
            <if test="orderType == 31">
                UPDATE as_material_rk_order SET approve_status = #{status} WHERE id = #{id}
            </if>
            <if test="orderType == 32">
                UPDATE as_material_ck_order SET approve_status = #{status} WHERE id = #{id}
            </if>
            <if test="orderType == 33">
                UPDATE as_material_ly_order SET approve_status = #{status} WHERE id = #{id}
            </if>
        </if>
    </update>

    <update id="updateBizDataStatus">
        <if test="bizType == 'asset'">

        </if>
        <if test="bizType == 'material'">

        </if>
    </update>

    <select id="selectBizIdsByCondition" resultType="java.lang.Long">
        <if test="bizType == 'asset'">
            <choose>
                <when test="">

                </when>
            </choose>
        </if>
        <if test="bizType == 'material'">

        </if>
    </select>

    <select id="selectDingUnionIdByAssetEmpId" resultType="java.lang.String">
        SELECT t2.union_id
        FROM as_account_employee t1
                 LEFT JOIN as_cus_user t2 ON t1.account_id = t2.id
        WHERE t1.employee_id = #{empId}
    </select>

    <!-- 资产编码、资产名称、资产分类、品牌、规格型号、计量单位、价值、存放区域、所属管理组织、所属管理员、使用组织、使用人 -->
    <select id="selectAssetFormFieldByCompanyId" resultMap="orderFieldResult">
        SELECT t2.id, t1.item_code AS code, t1.item_type AS type, t1.item_name AS name
        FROM as_biz_form_item t1
                 LEFT JOIN as_biz_form_asset t2 on t1.item_code = t2.item_code
        WHERE t1.form_id = 1
          AND t2.company_id = #{companyId}
          AND t2.item_code IN
              ('assetCode', 'assetName', 'assetCategory', 'brand', 'model', 'unit', 'price', 'storageArea',
               'orgOwner', 'managerOwner', 'useOrg', 'usePerson')
        ORDER BY t2.sort ASC
    </select>

    <!-- 耗材编码、耗材名称、耗材分类、规格型号、商品条码、品牌 -->
    <select id="selectMaterialFormFieldByCompanyId" resultMap="orderFieldResult">
        SELECT t2.id, t1.item_code AS code, t1.item_type AS type, t1.item_name AS name
        FROM as_biz_form_item t1
                 LEFT JOIN as_biz_form_material t2 on t1.item_code = t2.item_code
        WHERE t1.form_id = 2
          AND t2.company_id = #{companyId}
          AND t2.item_code IN ('materialCode', 'materialName', 'materialCategory', 'model', 'barCode', 'brand')
        ORDER BY t2.sort ASC
    </select>

    <!-- 物品名称、品牌、规格型号、计量单位、申购数量、预估单价、期待到货日期、备注 -->
    <select id="selectBaseProductFormField" resultMap="orderFieldResult">
        SELECT id,
               item_code                                                       AS code,
               item_type                                                       AS type,
               item_name                                                       AS name
        FROM as_biz_form_item
        WHERE form_id = 3
          AND item_code IN ('name', 'brand', 'model', 'unit')
        ORDER BY sort ASC
    </select>

    <resultMap id="DingProcInstMappingResultMap" type="com.niimbot.asset.dingtalk.workflow.model.DingProcInstMapping">
        <id column="order_id" property="orderId"/>
        <result column="proc_inst_id" property="procInsId"/>
        <result column="order_type" property="orderType"/>
        <result column="company_id" property="companyId"/>
        <result column="create_by" property="createBy"/>
    </resultMap>

    <select id="selectDingProcInstIdByAssetProcInstId" resultMap="DingProcInstMappingResultMap">
        SELECT proc_inst_id, biz_id AS order_id, order_type AS order_type, company_id, create_by FROM ding_workflow_business WHERE biz_id = ( SELECT business_id FROM act_workflow_business WHERE proc_inst_id = #{assetProcInstId} )
    </select>

    <select id="selectStepTypeById" resultType="java.lang.Integer">
        SELECT type FROM act_workflow_step WHERE id = #{id}
    </select>

    <resultMap id="WorkflowBusinessResultMap"
               type="com.niimbot.asset.dingtalk.base.dto.WorkflowBusiness">
        <id column="proc_inst_id" property="procInstId"/>
        <result column="business_id" property="bizId"/>
        <result column="steps" property="steps" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="type" property="type"/>
        <result column="approve_status" property="approveStatus"/>
    </resultMap>

    <select id="selectWorkflowBusinessByProcInstId" resultMap="WorkflowBusinessResultMap">
        SELECT proc_inst_id, business_id, steps, type FROM act_workflow_business WHERE proc_inst_id = #{procInstId}
    </select>

    <select id="selectActivityKeyByWorkflowId" resultType="java.lang.String">
        SELECT activiti_key FROM act_workflow WHERE id = #{id}
    </select>

    <resultMap id="orderInfoResultMap" type="com.niimbot.asset.dingtalk.base.dto.OrderInfo">
        <id column="id" property="orderId"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_data" property="orderData" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
    </resultMap>

    <select id="selectOrderInfo" resultMap="orderInfoResultMap">
        SELECT id, order_no, order_data, create_by, create_time FROM ${tableName} WHERE id = #{id}
    </select>

    <select id="selectUseWorkflowVersion" resultType="java.lang.Integer">
        SELECT workflow_version FROM ding_corp WHERE company_id = #{companyId}
    </select>

    <update id="updateWorkflowVersion">
        UPDATE ding_corp SET workflow_version = 2 WHERE company_id = #{companyId}
    </update>

    <update id="updateOrderTypeStatus">
        UPDATE ${tableName} SET enable_workflow_config = 1 WHERE company_id = #{companyId}
    </update>

    <select id="selectOrderEnableWorkflow" resultType="java.lang.Boolean">
        <if test="bizType == 1">
            SELECT enable_workflow FROM as_order_type WHERE company_id = #{companyId} AND `type` = #{orderType}
        </if>
        <if test="bizType == 2">
            SELECT enable_workflow FROM as_material_order_type WHERE company_id = #{companyId} AND `type` = #{orderType}
        </if>
        <if test="bizType == 3">
            SELECT enable_workflow FROM as_purchase_order_type WHERE company_id = #{companyId} AND `type` = #{orderType}
        </if>
    </select>

    <update id="updateOrderTypeEnableWorkflow">
        <if test="bizType == 1">
            UPDATE as_order_type SET enable_workflow = #{en} WHERE company_id = #{companyId} AND `type` = #{orderType}
        </if>
        <if test="bizType == 2">
            UPDATE as_material_order_type SET enable_workflow = #{en}  WHERE company_id = #{companyId} AND `type` = #{orderType}
        </if>
        <if test="bizType == 3">
            UPDATE as_purchase_order_type SET  enable_workflow = #{en}  WHERE company_id = #{companyId} AND `type` = #{orderType}
        </if>
    </update>

    <select id="selectOrderTypeExt" resultType="com.niimbot.asset.dingtalk.base.dto.DingOrderTypeExt">
        SELECT id AS order_type_id, `type` FROM ${tableName} WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectWorkflowBusinessByBizId" resultMap="WorkflowBusinessResultMap">
        SELECT proc_inst_id, business_id, steps, type, approve_status FROM act_workflow_business WHERE business_id = #{bizId}
    </select>

</mapper>
