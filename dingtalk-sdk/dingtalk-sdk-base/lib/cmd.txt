mvn deploy:deploy-file -Dmaven.test.skip=true -Dfile="/Users/<USER>/Downloads/code/solution/Mirror/backend/solution-asset-platform-mirror/dingtalk-sdk/dingtalk-sdk-base/lib/taobao-sdk-java-auto_1479188381469-20211220.jar"  -DgroupId="com.dingtalk.open"  -DartifactId="taobao-sdk-java-auto" -Dversion="1479188381469-20211220" -Dpackaging=jar -DrepositoryId="2423548-snapshot-X3Sgnq"  -Durl="https://packages.aliyun.com/652637a2940b4e1cb0ce7664/maven/2423548-snapshot-x3sgnq"



mvn deploy:deploy-file -Dmaven.test.skip=true -Dfile="/Users/<USER>/Downloads/code/solution/Mirror/backend/solution-asset-platform-mirror/dingtalk-sdk/dingtalk-sdk-base/lib/taobao-sdk-java-auto_1479188381469-20211220.jar"  -DgroupId="com.taobao.top"  -DartifactId="lippi-oapi-encrpt" -Dversion="dingtalk" -Dpackaging=jar -DrepositoryId="2423548-snapshot-X3Sgnq"  -Durl="https://packages.aliyun.com/652637a2940b4e1cb0ce7664/maven/2423548-snapshot-x3sgnq"