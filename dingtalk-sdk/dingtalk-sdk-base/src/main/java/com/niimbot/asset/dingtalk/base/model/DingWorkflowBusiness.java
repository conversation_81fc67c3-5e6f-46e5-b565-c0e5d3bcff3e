package com.niimbot.asset.dingtalk.base.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "钉钉审批实例信息", description = "DingWorkflowBusiness")
@TableName(value = "ding_workflow_business", autoResultMap = true)
public class DingWorkflowBusiness implements Serializable {

    @ApiModelProperty("单据ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long bizId;

    @ApiModelProperty("单据业务类型")
    private String bizType;

    @ApiModelProperty("单据类型")
    private String orderType;

    @ApiModelProperty("钉钉审批流程实例ID")
    private String procInstId;

    @ApiModelProperty("接入钉钉审批的方式: 1-官方OA审批，2-自有OA审批")
    private Integer way;

    @ApiModelProperty("企业ID")
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty("审批状态：1：审批中，2：审批拒绝，3：审批通过，4：审批已撤销，5：审批已转交")
    private Integer approveStatus;

    @ApiModelProperty("审批流程节点信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject node;

    @ApiModelProperty("发起审批流程后任务详情")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject details;

    @ApiModelProperty("发起钉钉审批实例后页面地址")
    private String url;

    @ApiModelProperty("钉钉审批实例状态：start、finish、terminate")
    private String instStatus;

    @ApiModelProperty("钉钉审批任务状态：start、finish、cancel、agree、refuse、redirect")
    private String taskStatus;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public DingWorkflowBusiness(String procInstId, Long companyId, Long bizId, Integer approveStatus) {
        this.procInstId = procInstId;
        this.companyId = companyId;
        this.bizId = bizId;
        this.approveStatus = approveStatus;
    }

    public boolean isStart() {
        return StrUtil.isNotBlank(instStatus) && "start".equals(instStatus);
    }

}
