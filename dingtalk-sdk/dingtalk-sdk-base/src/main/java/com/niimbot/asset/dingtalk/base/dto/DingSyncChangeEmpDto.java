package com.niimbot.asset.dingtalk.base.dto;

import com.niimbot.system.OrgDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * created by chen.y on 2021/9/30 15:12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DingSyncChangeEmpDto对象", description = "钉钉异动员工查询")
public class DingSyncChangeEmpDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "员工ID")
    private Long empId;

    @ApiModelProperty(value = "员工姓名")
    private String empName;

    @ApiModelProperty(value = "员工工号")
    private String empNo;

    @ApiModelProperty(value = "原始部门")
    private List<OrgDto> fromOrg;

    @ApiModelProperty(value = "新部门")
    private List<OrgDto> toOrg;

    @ApiModelProperty("是否出处理完成")
    private Boolean isHandlerDone;

}
