package com.niimbot.asset.dingtalk.base.model.constant;

/**
 * created by chen.y on 2021/8/20 16:02
 */
public class RedisConstant {

    public static final String V1_CORP_TOKEN_KEY = "v1_corp_token";

    public static final String CORP_TOKEN_KEY = "corp_token";

    public static final String USER_TOKEN_KEY = "user_token";

    public static String getUserTokenKey(Long userId) {
        return USER_TOKEN_KEY + ":" + userId;
    }

    public static String getCorpTokenKey(String corpId) {
        return CORP_TOKEN_KEY + ":" + corpId;
    }

    public static String getV1CorpTokenKey(String corpId) {
        return V1_CORP_TOKEN_KEY + ":" + corpId;
    }

    public static final String JS_TICKET_KEY = "js_ticket";

    public static String getJsTicketKey(String corpId) {
        return JS_TICKET_KEY + ":" + corpId;
    }

    public static final String SUITE_TICKET_KEY = "suite_ticket";

    public static final String SUITE_ACCESS_TOKEN_KEY = "suite_access_token";

    public static final String QR_SNS_CODE = "qr_sns_code";

    public static final String GOODS_TRYOUT = "goods_try_out";

    public static String getGoodsTryout(String corpId) {
        return GOODS_TRYOUT + ":" + corpId;
    }

    public static final String DING_MSG_TPL = "ding_msg_tpl";

}
