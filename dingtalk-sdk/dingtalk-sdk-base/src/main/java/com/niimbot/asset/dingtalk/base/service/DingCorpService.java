package com.niimbot.asset.dingtalk.base.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.dingtalk.base.model.DingCorp;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface DingCorpService extends IService<DingCorp> {

    Long getCompanyId(String corpId);

    DingCorp getByCompanyId(Long companyId);

    String getCorpId(Long companyId);

    Boolean removeCorp(String corpId, JSONObject content);

}
