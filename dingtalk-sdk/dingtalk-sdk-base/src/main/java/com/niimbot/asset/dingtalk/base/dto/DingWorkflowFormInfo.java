package com.niimbot.asset.dingtalk.base.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DingWorkflowFormInfo implements Serializable {

    private Long orderTypeId;

    private Long companyId;

    private String processCode;

    private Integer way;

    private Integer type;

    private String bizType;

    private String name;

    private String desc;

}
