package com.niimbot.asset.dingtalk.base.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_shopping_cart_bonus
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_shopping_cart_bonus", autoResultMap = true)
public class AsShoppingCartBonus implements Serializable {

    private static final long serialVersionUID = 4774136288365074800L;

    /**
     * 主键Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 主商品id
     */
    private Long mainProductId;

    /**
     * 优惠编码
     */
    private String discountCode;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 商品价值-软件商品
     */
    private Integer goodsValue;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}