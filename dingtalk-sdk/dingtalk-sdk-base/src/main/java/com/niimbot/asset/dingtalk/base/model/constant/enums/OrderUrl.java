package com.niimbot.asset.dingtalk.base.model.constant.enums;

import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderUrl {
    /**
     * 资产领用单
     */
    RECEIVE(
            "1",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=receive_workflow&orderType=1"),
            new SubmitUrl
                    (
                            "%s/#/recv-return/recv-add?orderType=1&corpId=%s",
                            "/webview/create-order/index?orderType=1&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/recv-return/recv-view?orderType=1&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=1&corpId=%s",
                            "%s/#/forward?orderType=1&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产退还单
     */
    RETURN(
            "2",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=return_workflow&orderType=2"),
            new SubmitUrl
                    (
                            "%s/#/recv-return/return-add?orderType=2&corpId=%s",
                            "/webview/create-order/index?orderType=2&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/recv-return/return-view?orderType=2&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=2&corpId=%s",
                            "%s/#/forward?orderType=2&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产借用单
     */
    BORROW(
            "3",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=borrow_workflow&orderType=3"),
            new SubmitUrl
                    (
                            "%s/#/borrow-revert/borrow-add?orderType=3&corpId=%s",
                            "/webview/create-order/index?orderType=3&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/borrow-revert/borrow-view?orderType=3&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=3&corpId=%s",
                            "%s/#/forward?orderType=3&docId=%d&corpId=%s"
                    )
    ),

    /**
     * 资产归还单
     */
    BACK(
            "4",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=back_workflow&orderType=4"),
            new SubmitUrl
                    (
                            "%s/#/borrow-revert/revert-add?orderType=4&corpId=%s",
                            "/webview/create-order/index?orderType=4&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/borrow-revert/revert-view?orderType=4&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=4&corpId=%s",
                            "%s/#/forward?orderType=4&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产调拨单
     */
    ALLOCATE(
            "5",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=allocate_workflow&orderType=5"),
            new SubmitUrl
                    (
                            "%s/#/asset-allot/allot-add?orderType=5&corpId=%s",
                            "/webview/create-order/index?orderType=5&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/asset-allot/allot-view?orderType=5&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=5&corpId=%s",
                            "%s/#/forward?orderType=5&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产报修单
     */
    REPAIR_REPORT(
            "6",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=repair_report_workflow&orderType=6"),
            new SubmitUrl
                    (
                            "%s/#/asset-repairs/add-asset-repairs?orderType=6&corpId=%s",
                            "/webview/create-order/index?orderType=6&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/asset-repairs/repair-order-detail?orderType=6&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=6&corpId=%s",
                            "%s/#/forward?orderType=6&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产维修单
     */
    REPAIR(
            "7",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=repair_workflow&orderType=7"),
            new SubmitUrl
                    (
                            "%s/#/service-asset/add-service-asset?orderType=7&corpId=%s",
                            "/webview/create-order/index?orderType=7&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/service-asset/service-asset-order-detail?orderType=7&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=7&corpId=%s",
                            "%s/#/forward?orderType=7&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产处置单
     */
    DISPOSE(
            "8",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=dispose_workflow&orderType=8"),
            new SubmitUrl
                    (
                            "%s/#/asset-handle/handle-add?orderType=8&corpId=%s",
                            "/webview/create-order/index?orderType=8&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/asset-handle/handle-view?orderType=8&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=8&corpId=%s",
                            "%s/#/forward?orderType=8&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产变更单
     */
    CHANGE(
            "9",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=change_workflow&orderType=9"),
            new SubmitUrl
                    (
                            "%s/#/asset-change/change-add?orderType=9&corpId=%s",
                            "/webview/create-order/index?orderType=9&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/asset-change/change-view?orderType=9&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=9&corpId=%s",
                            "%s/#/forward?orderType=9&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 资产维保单
     */
    MAINTAIN(
            "11",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=maintain_workflow&orderType=11"),
            new SubmitUrl
                    (
                            "%s/#/maintenance-asset/add-maintenance-order?orderType=11&corpId=%s",
                            "/webview/create-order/index?orderType=11&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/maintenance-asset/maintenance-order-detail?orderType=11&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=11&corpId=%s",
                            "%s/#/forward?orderType=11&docId=%d&corpId=%s&"
                    )
    ),
    /**
     * 资产入库单
     */
    STORE(
            "13",
            new EditUrl("%s/#/workflow-asset-order/workflow-setting?activitiKey=store_workflow&orderType=13"),
            new SubmitUrl
                    (
                            "%s/#/asset-storage/add?orderType=13&corpId=%s",
                            "/webview/create-order/index?orderType=13&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/asset-storage/detail?orderType=13&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=13&corpId=%s",
                            "%s/#/forward?orderType=13&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 采购申请单
     */
    PURCHASE_APPLY(
            "10",
            new EditUrl("%s/#/workflow-purchase-order/workflow-setting?activitiKey=purchase_apply_workflow&orderType=10"),
            new SubmitUrl
                    (
                            "%s/#/purchase-apply/add?orderType=10&corpId=%s",
                            "/webview/create-order/index?orderType=10&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/purchase-apply/detail?orderType=10&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=10&corpId=%s",
                            "%s/#/forward?orderType=10&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 采购订单
     */
    PURCHASE_ORDER(
            "12",
            new EditUrl("%s/#/workflow-purchase-order/workflow-setting?activitiKey=purchase_order_workflow&orderType=12"),
            new SubmitUrl
                    (
                            "%s/#/purchase-order/add?orderType=12&corpId=%s",
                            "/webview/create-order/index?orderType=12&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/purchase-order/detail?orderType=12&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=12&corpId=%s",
                            "%s/#/forward?orderType=12&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材入库单
     */
    RK(
            "31",
            new EditUrl("%s/#/workflow-material-order/workflow-setting?activitiKey=material_rk_workflow&orderType=31"),
            new SubmitUrl
                    (
                            "%s/#/material-rk/add?orderType=31&corpId=%s",
                            "/webview/create-order/index?orderType=31&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/material-rk/view?orderType=31&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=31&corpId=%s",
                            "%s/#/forward?orderType=31&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材出库单
     */
    CK(
            "32",
            new EditUrl("%s/#/workflow-material-order/workflow-setting?activitiKey=material_ck_workflow&orderType=32"),
            new SubmitUrl
                    (
                            "%s/#/material-ck/add?orderType=32&corpId=%s",
                            "/webview/create-order/index?orderType=32&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/material-ck/view?orderType=32&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=32&corpId=%s",
                            "%s/#/forward?orderType=32&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材领用单
     */
    LY(
            "33",
            new EditUrl("%s/#/workflow-material-order/workflow-setting?activitiKey=material_receive_workflow&orderType=33"),
            new SubmitUrl
                    (
                            "%s/#/material-ly/add?orderType=33&corpId=%s",
                            "/webview/create-order/index?orderType=33&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/material-ly/view?orderType=33&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=33&corpId=%s",
                            "%s/#/forward?orderType=33&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材调整单
     */
    TZ(
            "34",
            new EditUrl("%s/#/workflow-material-order/workflow-setting?activitiKey=material_tz_workflow&orderType=34"),
            new SubmitUrl
                    (
                            "%s/#/material-tz/add?orderType=34&corpId=%s",
                            "/webview/create-order/index?orderType=34&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/material-tz/view?orderType=34&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=34&corpId=%s",
                            "%s/#/forward?orderType=34&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材调拨单
     */
    DB(
            "35",
            new EditUrl("%s/#/workflow-material-order/workflow-setting?activitiKey=material_db_workflow&orderType=35"),
            new SubmitUrl
                    (
                            "%s/#/material-db/add?orderType=35&corpId=%s",
                            "/webview/create-order/index?orderType=35&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/material-db/view?orderType=35&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=35&corpId=%s",
                            "%s/#/forward?orderType=35&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材报损单
     */
    BS(
            "36",
            new EditUrl("%s/#/workflow-material-order/workflow-setting?activitiKey=material_bs_workflow&orderType=36"),
            new SubmitUrl
                    (
                            "%s/#/material-bs/add?orderType=36&corpId=%s",
                            "/webview/create-order/index?orderType=36&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/material-bs/view?orderType=36&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=36&corpId=%s",
                            "%s/#/forward?orderType=36&docId=%d&corpId=%s"
                    )
    ),
    /**
     * 耗材报损单
     */
    TK(
            "37",
            new EditUrl("%s/#/workflow-material-order/workflow-setting?activitiKey=material_tk_workflow&orderType=37"),
            new SubmitUrl
                    (
                            "%s/#/material-ck/tk-add?orderType=37&corpId=%s",
                            "/webview/create-order/index?orderType=37&corpId=%s"
                    ),
            new DetailsUrl
                    (
                            "%s/#/material-ck/tk-view?orderType=37&docId=%d&corpId=%s",
                            "/webview/order-detail/index?id=%d&orderType=37&corpId=%s",
                            "%s/#/forward?orderType=37&docId=%d&corpId=%s"
                    )
    );

    private String code;

    private EditUrl editUrl;

    private SubmitUrl submitUrl;

    private DetailsUrl detailsUrl;

    public static OrderUrl of(String code) {
        if (Objects.isNull(code)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "单据编码无效");
        }
        return Arrays.stream(OrderUrl.values())
                .filter(v -> v.code.equals(code))
                .findFirst()
                .orElseThrow(() -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "单据编码无效"));
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EditUrl {
        private String pc;

        public String formUrl() {
            return String.format(pc, SdkConstant.DOMAIN_PC);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubmitUrl {
        /**
         * PC端创建单据的链接地址
         * 格式: PC_DOMAIN + 路由
         */
        private String pc;
        /**
         * 小程序端创建单据的链接地址
         * 格式: 小程序Scheme + 路由地址
         *
         * @see <a href="https://open.dingtalk.com/document/orgapp/scheme-of-mini-programs">钉钉文档<a/>
         */
        private String mini;

        public String formatPc(String cropId) {
            return String.format(pc, SdkConstant.DOMAIN_PC, cropId);
        }

        public String formatMini(String cropId) {
            return String.format(mini, cropId);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DetailsUrl {
        /**
         * PC端单据详情地址
         * 格式: PC_DOMAIN + 路由地址
         */
        private String pc;
        /**
         * 小程序端单据详情地址
         * 格式: 小程序Scheme + 路由地址
         *
         * @see <a href="https://open.dingtalk.com/document/orgapp/scheme-of-mini-programs">钉钉文档<a/>
         */
        private String mini;
        /**
         * PC端和小程序单据详情中转地址
         * 格式: H5_DOMAIN + 路由
         */
        private String h5;

        public String formatPc(Long orderId, String cropId) {
            return String.format(pc, SdkConstant.DOMAIN_PC, orderId, cropId);
        }

        public String formatMini(Long orderId, String cropId) {
            return String.format(mini, orderId, cropId);
        }

        public String formatH5(Long orderId, String cropId) {
            return String.format(h5, SdkConstant.DOMAIN_H5, orderId, cropId);
        }
    }

}
