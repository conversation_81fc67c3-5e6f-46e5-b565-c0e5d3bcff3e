package com.niimbot.asset.dingtalk.base.support;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.dingtalk.base.dto.DingWorkflow;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DingWorkflowHolder {

    private static final ThreadLocal<DingWorkflow> CONTEXT = new TransmittableThreadLocal<>();

    public static void set(DingWorkflow dingWorkflow) {
        CONTEXT.set(dingWorkflow);
    }

    public static DingWorkflow get() {
        return Objects.isNull(CONTEXT.get()) ? new DingWorkflow() : CONTEXT.get();
    }

    public static void remove() {
        CONTEXT.remove();
    }

}
