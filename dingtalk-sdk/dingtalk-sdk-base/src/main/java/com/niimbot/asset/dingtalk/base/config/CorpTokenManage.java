package com.niimbot.asset.dingtalk.base.config;

import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetCorpAccessTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetCorpAccessTokenResponse;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiServiceGetCorpTokenRequest;
import com.dingtalk.api.response.OapiServiceGetCorpTokenResponse;
import com.niimbot.asset.dingtalk.base.model.constant.RedisConstant;
import com.niimbot.asset.dingtalk.base.model.constant.ResponseCode;
import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.taobao.api.ApiException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * created by chen.y on 2021/8/27 16:37
 */
@Slf4j
@Component
public class CorpTokenManage {

    private final RedisService redisService;

    private final com.aliyun.dingtalkoauth2_1_0.Client oauthClient;

    @Autowired
    public CorpTokenManage(RedisService redisService, Client oauthClient) {
        this.redisService = redisService;
        this.oauthClient = oauthClient;
    }

    /**
     * https://developers.dingtalk.com/document/app/obtains-the-enterprise-authorized-credential
     * 获取第三方应用授权企业的access_token
     *
     * @param corpId 公司Id
     * @return access_token
     */
    public String getCorpToken(String corpId) {
        // 查询缓存是否存在suite_access_token
        String corpToken = Convert.toStr(redisService.get(RedisConstant.getCorpTokenKey(corpId)));
        if (StrUtil.isBlank(corpToken)) {
            DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_CORP_TOKEN);
            OapiServiceGetCorpTokenRequest req = new OapiServiceGetCorpTokenRequest();
            req.setAuthCorpid(corpId);
            try {
                String suiteTicket = Convert.toStr(redisService.get(RedisConstant.SUITE_TICKET_KEY));

                OapiServiceGetCorpTokenResponse rsp = client.execute(req, SdkConstant.SUITE_KEY, SdkConstant.SUITE_SECRET, suiteTicket);
                if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                    corpToken = rsp.getAccessToken();
                    // 提前5分钟过期
                    long expiresIn = rsp.getExpiresIn() - 300;
                    redisService.set(RedisConstant.getCorpTokenKey(corpId), corpToken, expiresIn, TimeUnit.SECONDS);
                } else {
                    log.warn("get corp token error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                    throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
                }
            } catch (ApiException e) {
                log.error("get corp token error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
            }
        }
        return corpToken;
    }

    public String getV1CorpAccessToken(String cropId) {
        String token = Convert.toStr(redisService.get(RedisConstant.getV1CorpTokenKey(cropId)));
        if (StrUtil.isBlank(token)) {
            try {
                GetCorpAccessTokenRequest request = new GetCorpAccessTokenRequest();
                request.setAuthCorpId(cropId);
                String suiteTicket = Convert.toStr(redisService.get(RedisConstant.SUITE_TICKET_KEY));
                request.setSuiteTicket(suiteTicket);
                request.setSuiteKey(SdkConstant.SUITE_KEY).setSuiteSecret(SdkConstant.SUITE_SECRET);
                GetCorpAccessTokenResponse response = oauthClient.getCorpAccessToken(request);
                token = response.getBody().getAccessToken();
                // 提前5分钟过期
                long expiresIn = response.getBody().getExpireIn() - 300;
                redisService.set(RedisConstant.getV1CorpTokenKey(cropId), token, expiresIn, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("get v1 corp access token error", e);
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取企业V1版token异常");
            }
        }
        return token;
    }

}
