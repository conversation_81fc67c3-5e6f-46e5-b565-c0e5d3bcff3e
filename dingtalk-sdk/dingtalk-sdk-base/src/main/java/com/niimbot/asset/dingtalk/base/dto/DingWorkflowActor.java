package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "DingWorkflowActor", description = "钉钉审批流节点操作人信息")
public class DingWorkflowActor implements Serializable {

    @ApiModelProperty("节点操作人key")
    private String actorKey;

    @ApiModelProperty("审批人列表（云资产系统员工ID集合）")
    private List<Long> empIds;

    @ApiModelProperty("审批类型")
    private String actionType;

    @ApiModelProperty("节点操作人类型")
    private String actorType;

}
