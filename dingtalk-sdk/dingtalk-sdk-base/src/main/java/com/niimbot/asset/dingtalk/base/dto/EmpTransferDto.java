package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * created by chen.y on 2021/9/30 15:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "EmpTransferDto对象", description = "钉钉员工异动转移对象")
public class EmpTransferDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "异动Id")
    @NotNull(message = "异动Id不能为空")
    private Long id;

    @ApiModelProperty(value = "转移部门")
    @NotEmpty(message = "转移部门为空")
    private List<@Valid TransferDto> transfer;

}
