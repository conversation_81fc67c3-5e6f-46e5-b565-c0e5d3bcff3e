package com.niimbot.asset.dingtalk.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkcard_1_0.models.RegisterCallbackHeaders;
import com.aliyun.dingtalkcard_1_0.models.RegisterCallbackRequest;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponse;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkim_1_0.models.GetSceneGroupInfoHeaders;
import com.aliyun.dingtalkim_1_0.models.GetSceneGroupInfoRequest;
import com.aliyun.dingtalkim_1_0.models.GetSceneGroupInfoResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.niimbot.asset.dingtalk.base.config.CorpTokenManage;
import com.niimbot.asset.dingtalk.base.dto.AuthScopes;
import com.niimbot.asset.dingtalk.base.dto.DingResourcePayDto;
import com.niimbot.asset.dingtalk.base.dto.DingRole;
import com.niimbot.asset.dingtalk.base.model.constant.RedisConstant;
import com.niimbot.asset.dingtalk.base.model.constant.ResponseCode;
import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.dingtalk.base.restops.RestOps;
import com.niimbot.asset.dingtalk.base.service.DingOpenApiService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.taobao.api.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by chen.y on 2021/8/23 17:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DingOpenApiServiceImpl implements DingOpenApiService {

    private final com.aliyun.dingtalkcontact_1_0.Client contactClient;
    private final com.aliyun.dingtalkim_1_0.Client imV1Client;
    private final com.aliyun.dingtalkcard_1_0.Client cardV1Client;

    private final RedisService redisService;
    private final CorpTokenManage corpTokenManage;

    /**
     * https://developers.dingtalk.com/document/app/query-user-details 根据userid获取用户详情
     *
     * @param corpId 企业Id
     * @param userId 用户Id
     * @return 用户信息
     */
    @Override
    public OapiV2UserGetResponse.UserGetResponse getUserByUserId(String userId, String corpId, String corpToken) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_USER_BY_USER_ID);
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        try {
            if (StrUtil.isEmpty(corpToken)) {
                corpToken = corpTokenManage.getCorpToken(corpId);
            }
            OapiV2UserGetResponse rsp = client.execute(req, corpToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                OapiV2UserGetResponse.UserGetResponse userGetResponse = rsp.getResult();
                return userGetResponse;
            } else {
                log.warn("get user, userId{} corpId{} error ==>> [{}] {}", userId, corpId, rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("get user, userId{} corpId{} error ==>> [{}] {}", userId, corpId, e.getErrCode(), e.getErrMsg());
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
        }
    }

    /**
     * https://developers.dingtalk.com/document/app/obtain-application-suite-ticket
     * 获取第三方企业应用的suite_access_token
     *
     * @return suite_access_token
     */
    @Override
    public synchronized String getSuiteToken() {
        // 查询缓存是否存在suite_access_token
        String suiteAccessToken = Convert.toStr(redisService.get(RedisConstant.SUITE_ACCESS_TOKEN_KEY));
        if (StrUtil.isBlank(suiteAccessToken)) {
            DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_SUITE_TOKEN);
            OapiServiceGetSuiteTokenRequest req = new OapiServiceGetSuiteTokenRequest();
            req.setSuiteKey(SdkConstant.SUITE_KEY);
            req.setSuiteSecret(SdkConstant.SUITE_SECRET);
            req.setSuiteTicket(Convert.toStr(redisService.get(RedisConstant.SUITE_TICKET_KEY)));
            try {
                OapiServiceGetSuiteTokenResponse rsp = client.execute(req);
                if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                    suiteAccessToken = rsp.getSuiteAccessToken();
                    // 提前5分钟过期
                    long expiresIn = rsp.getExpiresIn() - 300;
                    redisService.set(RedisConstant.SUITE_ACCESS_TOKEN_KEY, suiteAccessToken, expiresIn, TimeUnit.SECONDS);
                } else {
                    log.warn("get suite_access_token error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                    throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
                }
            } catch (ApiException e) {
                log.error("get suite_access_token error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
            }
        }
        return suiteAccessToken;
    }

    /**
     * https://developers.dingtalk.com/document/app/obtain-the-userid-of-a-user-by-using-the-log-free
     * 通过免登码获取用户信息(v2)
     *
     * @param code      免登授权码
     * @param corpToken 企业access_token
     * @return 用户信息
     */
    @Override
    public OapiV2UserGetuserinfoResponse.UserGetByCodeResponse getUserInfo(String code, String corpToken) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_USER_INFO);
        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        req.setCode(code);
        try {
            OapiV2UserGetuserinfoResponse rsp = client.execute(req, corpToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                return rsp.getResult();
            } else {
                log.warn("get suite_access_token error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(SystemResultCode.PERMISSION_NO_ACCESS.getCode(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("get suite_access_token error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            throw new BusinessException(SystemResultCode.PERMISSION_NO_ACCESS.getCode(), e.getErrMsg());
        }
    }

    /**
     * https://developers.dingtalk.com/document/app/obtain-the-user-information-based-on-the-sns-temporary-authorization
     * 根据sns临时授权码获取用户信息
     *
     * @param tmpAuthCode 临时授权码
     * @return 用户信息
     */
    @Override
    public OapiSnsGetuserinfoBycodeResponse.UserInfo getUserInfoByCode(String tmpAuthCode) {
        DefaultDingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_USERINFO_BY_CODE);
        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
        req.setTmpAuthCode(tmpAuthCode);
        try {
            OapiSnsGetuserinfoBycodeResponse rsp = client.execute(req,
                    SdkConstant.SUITE_KEY,
                    SdkConstant.SUITE_SECRET);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                return rsp.getUserInfo();
            } else {
                log.warn("get_user_info_by_code error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("get get_user_info_by_code error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
        }
    }

    @Override
    public AuthScopes getAuthScopes(String corpToken) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_AUTH_SCOPES);
        OapiAuthScopesRequest req = new OapiAuthScopesRequest();
        req.setHttpMethod("GET");
        try {
            OapiAuthScopesResponse rsp = client.execute(req, corpToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                OapiAuthScopesResponse.AuthOrgScopes authOrgScopes = rsp.getAuthOrgScopes();
                List<Long> authedDept = authOrgScopes.getAuthedDept();
                List<String> authedUser = authOrgScopes.getAuthedUser();
                AuthScopes authScopes = new AuthScopes();
                authScopes.setAuthedDept(authedDept);
                authScopes.setAuthedUser(authedUser);
                return authScopes;
            } else {
                log.warn("get auth scopes error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("get auth scopes error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
        }
    }

    @Override
    public GetUserResponseBody getContactUser(String unionId, String accessToken) {
        try {
            GetUserHeaders getUserHeaders = new GetUserHeaders();
            getUserHeaders.xAcsDingtalkAccessToken = accessToken;
            GetUserResponse user = contactClient.getUserWithOptions(unionId, getUserHeaders, new RuntimeOptions());
            if (user == null) {
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "用户信息获取失败");
            }
            return user.getBody();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("get contact user error ==>> [{}] {}", err.code, err.message);
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), err.message);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("get contact user error ==>> [{}] {}", err.code, err.message);
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), err.message);
            }
            log.error("get contact user error", _err);
        }
        throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "用户信息获取失败");
    }

    /**
     * https://oapi.dingtalk.com/topapi/appstore/internal/skupage/get 获取内购商品SKU页面地址
     *
     * @param code      sku code
     * @param corpToken 企业access_token
     * @return 跳转地址
     */
    @Override
    public String getSku(String code, String callback, String corpToken) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_SKU);
        OapiAppstoreInternalSkupageGetRequest req = new OapiAppstoreInternalSkupageGetRequest();
        req.setGoodsCode(code);
        try {
            callback = URLEncoder.encode(callback, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage());
        }
        req.setCallbackPage(callback);
        try {
            OapiAppstoreInternalSkupageGetResponse rsp = client.execute(req, corpToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                return rsp.getResult();
            } else {
                log.warn("get sku page error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("get sku page error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
        }
    }

    /**
     * https://oapi.dingtalk.com/topapi/appstore/internal/order/finish 内购商品订单处理完成
     *
     * @param orderId 内购订单号。
     * @return
     */
    @Override
    public Boolean orderFinish(String orderId, String corpToken) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.ORDER_FINISH);
        OapiAppstoreInternalOrderFinishRequest req = new OapiAppstoreInternalOrderFinishRequest();
        req.setBizOrderId(Convert.toLong(orderId));
        try {
            OapiAppstoreInternalOrderFinishResponse rsp = client.execute(req, corpToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                return true;
            } else {
                log.warn("order finish error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                return false;
            }
        } catch (ApiException e) {
            log.error("order finish error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            return false;
        }
    }

    /**
     * https://oapi.dingtalk.com/topapi/appstore/internal/unfinishedorder/list 获取未处理的已支付订单
     *
     * @param itemCode 内购订单号。
     * @return
     */
    @Override
    public List<OapiAppstoreInternalUnfinishedorderListResponse.InAppGoodsOrderVO> unfinishedorder(String itemCode) {
        String suiteToken = getSuiteToken();
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.UNFINISHED_ORDER_LIST);
        OapiAppstoreInternalUnfinishedorderListRequest req = new OapiAppstoreInternalUnfinishedorderListRequest();
        req.setItemCode(itemCode);
        req.setPage(1L);
        req.setPageSize(100L);
        try {
            OapiAppstoreInternalUnfinishedorderListResponse rsp = client.execute(req, suiteToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                return rsp.getResult().getItems();
            } else {
                log.warn("unfinishe dorder error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                return new ArrayList<>();
            }
        } catch (ApiException e) {
            log.error("unfinishe dorder error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            return new ArrayList<>();
        }
    }

    /**
     * https://oapi.dingtalk.com/topapi/appstore/internal/order/consume 应用内购商品核销
     *
     * @param orderId 订单号。
     * @param userId  员工Id。
     * @param number  数量。
     * @return
     */
    @Override
    public Boolean orderConsume(String orderId, String userId, Integer number, String corpToken) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.ORDER_CONSUME);
        OapiAppstoreInternalOrderConsumeRequest req = new OapiAppstoreInternalOrderConsumeRequest();
        req.setBizOrderId(Convert.toLong(orderId));
        req.setQuantity(Convert.toLong(number));
        req.setRequestId(IdUtils.getId().toString());
        req.setUserid(userId);
        try {
            OapiAppstoreInternalOrderConsumeResponse rsp = client.execute(req, corpToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                return true;
            } else {
                log.warn("order consume error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                return false;
            }
        } catch (ApiException e) {
            log.error("order consume error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            return false;
        }
    }

    @Override
    public Set<String> getUserIdsByRole(String roleId, String corpToken) {
        Set<String> result = new HashSet<>();
        OapiRoleSimplelistRequest req = new OapiRoleSimplelistRequest();
        req.setRoleId(Convert.toLong(roleId, 0L));
        req.setSize(100L);
        Long nextCursor = 0L;
        while (nextCursor >= 0L) {
            req.setOffset(nextCursor);
            nextCursor = add(req, corpToken, result);
        }
        return result;
    }

    @Override
    public String getPayUrl(DingResourcePayDto payDto, String corpToken) {
        DingTalkClient client = new DefaultDingTalkClient(SdkConstant.GET_SKU);
        OapiAppstoreInternalSkupageGetRequest req = new OapiAppstoreInternalSkupageGetRequest();
        req.setGoodsCode(payDto.getSkuCode());
        try {
            JSONObject extParam = new JSONObject();
            extParam.put("outDefinedPrice", payDto.getPayFee().multiply(new BigDecimal("100")).longValue());
            extParam.put("orderNo", payDto.getOrderNo());
            String extParamStr = extParam.toJSONString();
            req.setExtendParam(URLEncoder.encode(extParamStr, "UTF-8"));
            String callback = payDto.getCallbackPage();
            if (StrUtil.isNotEmpty(callback)) {
                callback = URLEncoder.encode(payDto.getCallbackPage(), "UTF-8");
                req.setCallbackPage(callback);
            }
            log.info("dingOpenApiService getPayUrl param=[{}] extendParam=[{}]", JSONObject.toJSONString(req), extParamStr);
            OapiAppstoreInternalSkupageGetResponse rsp = client.execute(req, corpToken);
            if (ResponseCode.SUCCESS.equals(rsp.getErrcode())) {
                return rsp.getResult();
            } else {
                log.warn("get sku page error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("get sku page error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        }
    }

    @Override
    public String queryGroup(String openConversationId, String corpId) {
        GetSceneGroupInfoHeaders groupHeaders = new GetSceneGroupInfoHeaders();
        groupHeaders.xAcsDingtalkAccessToken = corpTokenManage.getV1CorpAccessToken(corpId);

        GetSceneGroupInfoRequest req = new GetSceneGroupInfoRequest();
        req.setOpenConversationId(openConversationId);
        req.setCoolAppCode(SdkConstant.COOL_APP_CODE);
        try {
            GetSceneGroupInfoResponse info = imV1Client.getSceneGroupInfoWithOptions(req, groupHeaders, new RuntimeOptions());
            return info.getBody().getTitle();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("query group tea error, code = {}, msg = {}", err.getCode(), err.getMessage());
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("query group error, code = {}, msg = {}", err.getCode(), err.getMessage());
            }
        }
        return null;
    }

    @Override
    public void registerCallback(String corpId, String templateType) {
        RegisterCallbackHeaders registerCallbackHeaders = new RegisterCallbackHeaders();
        registerCallbackHeaders.xAcsDingtalkAccessToken = corpTokenManage.getV1CorpAccessToken(corpId);
        RegisterCallbackRequest registerCallbackRequest = new RegisterCallbackRequest()
                .setCallbackRouteKey(templateType)
                .setCallbackUrl(StrUtil.format(SdkConstant.COOL_APP_CALLBACK, templateType))
                .setApiSecret(SdkConstant.SUITE_SECRET)
                .setForceUpdate(true);
        try {
            cardV1Client.registerCallbackWithOptions(registerCallbackRequest, registerCallbackHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            if (!Common.empty(err.code) && !Common.empty(err.message)) {
                log.error("register callback tea error, code = {}, msg = {}", err.getCode(), err.getMessage());
                log.error("register callback tea error, url = {}", StrUtil.format(SdkConstant.COOL_APP_CALLBACK, templateType));
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!Common.empty(err.code) && !Common.empty(err.message)) {
                log.error("register callback tea error, code = {}, msg = {}", err.getCode(), err.getMessage());
                log.error("register callback tea error, url = {}", StrUtil.format(SdkConstant.COOL_APP_CALLBACK, templateType));
            }
        }
    }

    private Long add(OapiRoleSimplelistRequest req, String corpToken, Set<String> result) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/simplelist");
            OapiRoleSimplelistResponse rsp = client.execute(req, corpToken);
            Set<String> userIds = rsp.getResult().getList().stream().map(OapiRoleSimplelistResponse.OpenEmpSimple::getUserid).collect(Collectors.toSet());
            result.addAll(userIds);
            if (rsp.getResult().getHasMore()) {
                return req.getOffset() + req.getSize();
            } else {
                return -1L;
            }
        } catch (ApiException e) {
            log.error("获取指定角色的员工列表ID异常", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取指定角色的员工列表ID异常");
        }
    }

    @Override
    public List<OapiRoleListResponse.OpenRoleGroup> getRoles(String corpId) {
        String corpToken = corpTokenManage.getCorpToken(corpId);
        OapiRoleListRequest req = new OapiRoleListRequest();
        req.setSize(200L);
        Long nextCursor = 0L;
        List<OapiRoleListResponse.OpenRoleGroup> result = new ArrayList<>();
        while (nextCursor >= 0L) {
            req.setOffset(nextCursor);
            nextCursor = handleRolesNextCursor(corpToken, req, result);
        }
        return result;
    }

    private Long handleRolesNextCursor(String token, OapiRoleListRequest req, List<OapiRoleListResponse.OpenRoleGroup> result) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/list");
        OapiRoleListResponse response = RestOps.handleOrThrow(() -> {
            OapiRoleListResponse rep = client.execute(req, token);
            return (Objects.nonNull(rep) && rep.isSuccess()) ? rep : new OapiRoleListResponse();
        });
        if (Objects.isNull(response.getResult())) {
            return -1L;
        }
        result.addAll(response.getResult().getList());
        if (Objects.nonNull(response.getResult().getHasMore()) && response.getResult().getHasMore()) {
            return req.getOffset() + req.getSize();
        }
        return -1L;
    }

    @Override
    public List<OapiRoleSimplelistResponse.OpenEmpSimple> getRoleUsers(String corpId, Long roleId) {
        String corpToken = corpTokenManage.getCorpToken(corpId);
        OapiRoleSimplelistRequest req = new OapiRoleSimplelistRequest();
        req.setRoleId(roleId);
        req.setSize(100L);
        req.setOffset(0L);
        List<OapiRoleSimplelistResponse.OpenEmpSimple> result = new ArrayList<>();
        recursionRoleUsers(corpToken, req, result);
        return result;
    }

    private void recursionRoleUsers(String token, OapiRoleSimplelistRequest req, List<OapiRoleSimplelistResponse.OpenEmpSimple> result) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/simplelist");
        OapiRoleSimplelistResponse response = RestOps.handleOrThrow(() -> {
            OapiRoleSimplelistResponse rep = client.execute(req, token);
            return (Objects.nonNull(rep) && rep.isSuccess()) ? rep : new OapiRoleSimplelistResponse();
        });
        if (Objects.isNull(response)) {
            return;
        }
        if (Objects.isNull(response.getResult())) {
            return;
        }
        Boolean hasMore = response.getResult().getHasMore();
        if (CollUtil.isNotEmpty(response.getResult().getList())) {
            result.addAll(response.getResult().getList());
        }
        if (Objects.isNull(hasMore) || !hasMore) {
            return;
        }
        req.setOffset(req.getOffset() + 1L);
        recursionRoleUsers(token, req, result);
    }

    @Override
    public OapiRoleGetrolegroupResponse.OpenRoleGroup getRolesByGroupId(String corpId, Long roleGroupId) {
        String corpToken = corpTokenManage.getCorpToken(corpId);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/getrolegroup");
        OapiRoleGetrolegroupRequest req = new OapiRoleGetrolegroupRequest();
        req.setGroupId(roleGroupId);
        OapiRoleGetrolegroupResponse response = RestOps.handleOrThrow(() -> {
            OapiRoleGetrolegroupResponse result = client.execute(req, corpToken);
            return (Objects.nonNull(result) && result.isSuccess()) ? result : new OapiRoleGetrolegroupResponse();
        });
        return response.getRoleGroup();
    }

    @Override
    public OapiRoleGetroleResponse.OpenRole getRoleById(String corpId, Long roleId) {
        String corpToken = corpTokenManage.getCorpToken(corpId);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/getrole");
        OapiRoleGetroleRequest req = new OapiRoleGetroleRequest();
        req.setRoleId(roleId);
        OapiRoleGetroleResponse response = RestOps.handleOrThrow(() -> {
            OapiRoleGetroleResponse result = client.execute(req, corpToken);
            return (Objects.nonNull(result) && result.isSuccess()) ? result : new OapiRoleGetroleResponse();
        });
        return response.getRole();
    }

    @Override
    public List<DingRole> getRoles(String corpId, List<Long> ids) {
        List<DingRole> toDb = new ArrayList<>();
        // 角色或角色组ID集合
        // 先根据角色组ID去查询
        ids.forEach(id -> {
            OapiRoleGetrolegroupResponse.OpenRoleGroup group = this.getRolesByGroupId(corpId, id);
            if (Objects.isNull(group) || CollUtil.isEmpty(group.getRoles())) {
                return;
            }
            List<DingRole> roles = group.getRoles().stream()
                    .map(v -> new DingRole(v.getRoleId(), v.getRoleName()))
                    .collect(Collectors.toList());
            toDb.addAll(roles);
        });
        // 根据角色ID查寻
        ids.forEach(id -> {
            OapiRoleGetroleResponse.OpenRole r = this.getRoleById(corpId, id);
            if (Objects.isNull(r) || Objects.isNull(r.getGroupId()) || r.getGroupId() == -1) {
                return;
            }
            toDb.add(new DingRole(id, r.getName()));
        });
        return toDb.stream().distinct().collect(Collectors.toList());
    }
}
