package com.niimbot.asset.dingtalk.base.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SyncChangeInfo {

    private Long id;

    private Integer type;

    private String empName;

    @Data
    @Accessors(chain = true)
    public static class Each {

        private Long id;

        private String key;

        private Integer value;

    }

}
