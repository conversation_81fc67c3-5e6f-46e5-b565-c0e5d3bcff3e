package com.niimbot.asset.dingtalk.base.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName(value = "ding_order_type", autoResultMap = true)
public class DingOrderType implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private Long orderTypeId;

    @TenantFilterColumn
    private Long companyId;

    private String processCode;

    private Integer way;

    public DingOrderType(Long orderTypeId, Long companyId, String processCode) {
        this.orderTypeId = orderTypeId;
        this.companyId = companyId;
        this.processCode = processCode;
    }
}
