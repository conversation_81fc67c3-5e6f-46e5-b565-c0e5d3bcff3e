package com.niimbot.asset.dingtalk.base.model;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DingCorp对象", description = "")
@TableName(value = "ding_corp", autoResultMap = true)
public class DingCorp implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String MAIN_CORP_ID = "mainCorpId";
    public static final int CORP_TYPE_HIDDEN = 1;

    @ApiModelProperty(value = "公司Id")
    @TableId(value = "company_id", type = IdType.ASSIGN_ID)
    private Long companyId;

    @ApiModelProperty(value = "钉钉公司Id")
    private String corpId;

    @ApiModelProperty(value = "主组织的corpId - 在个人版下 此字段记录的是用户实际所在企业的corpId, 而 corpId 则是虚拟的")
    private String mainCorpId;

    @ApiModelProperty(value = "agentId")
    private Long agentId;

    @ApiModelProperty(value = "企业所属行业")
    private String industry;

    @ApiModelProperty(value = "授权方企业名称")
    private String corpName;

    @ApiModelProperty(value = "企业类型")
    private Integer corpType;

    @ApiModelProperty(value = "企业认证等级")
    private Integer authLevel;

    @ApiModelProperty(value = "企业邀请链接")
    private String inviteUrl;

    @ApiModelProperty(value = "邀请码")
    private String inviteCode;

    @ApiModelProperty(value = "渠道码")
    private String authChannel;

    @ApiModelProperty(value = "序列号")
    private String licenseCode;

    @ApiModelProperty(value = "企业logo")
    private String corpLogoUrl;

    @ApiModelProperty(value = "授权方企业名称")
    private String fullCorpName;

    @ApiModelProperty(value = "企业是否认证")
    private Boolean isAuthenticated;

    @ApiModelProperty(value = "渠道类型")
    private String authChannelType;

    @ApiModelProperty("应用是否已经被删除")
    private Boolean appIsRemove;

    public boolean individual() {
        return this.corpType == CORP_TYPE_HIDDEN && StrUtil.isNotBlank(mainCorpId);
    }
}
