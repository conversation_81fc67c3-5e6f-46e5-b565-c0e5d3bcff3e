package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/10 上午10:12
 */
@Data
@ApiModel(value = "OrderRequestDto对象", description = "订单结算对象")
public class OrderRequestDto implements Serializable {

    private static final long serialVersionUID = -5758501410741970011L;

    @NotEmpty(message = "购买商品为空")
    @ApiModelProperty(value = "订单商品信息")
    private List<OrderProductRequestDto> productList;

    @ApiModelProperty(value = "立即购买 true-立即购买 false-购物车购买")
    private Boolean immediatelySettle = Boolean.FALSE;

    @ApiModelProperty(value = "订单备注信息")
    private String remark;

    @ApiModelProperty(value = "成功跳转路由")
    private String callbackPage;

    @ApiModelProperty(hidden = true)
    private BigDecimal payFee;

    @ApiModelProperty(hidden = true)
    private String skuCode;

    @ApiModelProperty(hidden = true)
    private String orderNo;
}
