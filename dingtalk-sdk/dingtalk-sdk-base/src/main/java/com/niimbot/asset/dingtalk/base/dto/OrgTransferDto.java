package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * created by chen.y on 2021/9/30 16:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OrgTransferDto对象", description = "钉钉组织异动转移对象")
public class OrgTransferDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "异动Id")
    @NotNull(message = "异动Id不能为空")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    @NotNull(message = "组织不能为空")
    private Long orgId;

    @ApiModelProperty(value = "接受的使用部门")
    @NotNull(message = "接受的使用部门不能为空")
    private Long useOrg;

    @ApiModelProperty(value = "接受的所属管理部门")
    @NotNull(message = "接受的所属管理部门不能为空")
    private Long orgOwner;

}
