package com.niimbot.asset.dingtalk.base.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * created by chen.y on 2021/8/23 14:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AbsOpenSyncBizData {

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订阅方ID")
    private String subscribeId;

    @ApiModelProperty(value = "企业ID")
    private String corpId;

    @ApiModelProperty(value = "业务ID")
    private String bizId;

    @ApiModelProperty(value = "业务类型")
    private Integer bizType;

    @ApiModelProperty(value = "业务数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject bizData;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "对账游标")
    private Long openCursor;

    @ApiModelProperty(value = "处理状态0为未处理。其他状态开发者自行定义")
    private Integer status;


}
