package com.niimbot.asset.dingtalk.base.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/1 16:15
 */
@Data
public class OrgUpdateDto {

    @ApiModelProperty(value = "组织编号ID")
    @NotNull(message = "组织ID不能为空")
    private Long id;

    @ApiModelProperty(value = "组织编码", required = true)
    @NotBlank(message = "请输入组织编码")
    @Pattern(regexp = "^([0-9]|[A-Z]){1,20}$", message = "组织编码请输入1-20个数字、大写字母")
    private String orgCode;

}
