package com.niimbot.asset.dingtalk.base.dto;

import java.math.BigDecimal;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/7 18:23
 */
@Data
public class DingResourcePayDto {

    @ApiModelProperty(value = "订购时长")
    @NotNull(message = "订购时长请输入1-99999的整数")
    @Min(value = 1, message = "订购时长请输入1-99999的整数")
    @Max(value = 99999, message = "订购时长请输入1-99999的整数")
    private Integer num = 1;

    @ApiModelProperty(value = "资源Id")
    @NotNull(message = "资源Id不能为空")
    private Long resourceId;

    @ApiModelProperty(value = "成功跳转路由")
    private String callbackPage;

    @ApiModelProperty(hidden = true)
    private BigDecimal payFee;

    @ApiModelProperty(hidden = true)
    private String skuCode;

    @ApiModelProperty(hidden = true)
    private String orderNo;

}
