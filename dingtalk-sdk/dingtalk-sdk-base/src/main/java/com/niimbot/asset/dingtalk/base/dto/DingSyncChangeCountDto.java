package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * created by chen.y on 2021/10/14 17:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DingSyncChangeDto对象", description = "钉钉组织员工异动记录")
public class DingSyncChangeCountDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "员工异动数量")
    private Integer emp;

    @ApiModelProperty(value = "组织异动数量")
    private Integer org;

    @ApiModelProperty(value = "时间差（秒）")
    private Integer time;

}
