package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * created by chen.y on 2021/12/9 15:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AuthScopes对象", description = "授权范围")
public class AuthScopes {

    List<Long> authedDept;

    List<String> authedUser;

}
