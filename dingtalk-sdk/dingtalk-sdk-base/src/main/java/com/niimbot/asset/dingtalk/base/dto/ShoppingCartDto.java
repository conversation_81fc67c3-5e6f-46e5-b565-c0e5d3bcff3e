package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 上午11:33
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ShoppingCartDto对象", description = "购物车对象")
public class ShoppingCartDto implements Serializable {

    private static final long serialVersionUID = 2139579330764065576L;

    @ApiModelProperty("商品列表")
    private List<ShoppingCartProductDto> productList;

    @ApiModelProperty(value = "商品数量")
    private Integer count;

    @ApiModelProperty(value = "商品总价")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payMoney;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal bonus;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 非固定规格skuCode，传给钉钉用
     */
    @ApiModelProperty(hidden = true)
    private String skuCode;
}
