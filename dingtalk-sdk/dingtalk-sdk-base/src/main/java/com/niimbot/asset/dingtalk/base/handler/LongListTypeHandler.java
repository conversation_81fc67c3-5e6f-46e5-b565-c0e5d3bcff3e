package com.niimbot.asset.dingtalk.base.handler;

import com.niimbot.asset.framework.handle.AbstractJsonTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * 盘点范围列表类型处理器
 *
 * <AUTHOR>
 * @date 2021/4/9 11:30
 */
@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.OTHER)
public class LongListTypeHandler extends AbstractJsonTypeHandler<List<Long>> {

}
