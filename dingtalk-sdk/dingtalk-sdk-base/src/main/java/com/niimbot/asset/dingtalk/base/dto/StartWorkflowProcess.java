package com.niimbot.asset.dingtalk.base.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class StartWorkflowProcess extends GetDingWorkflowNode implements Serializable {

    private String uid;

    private Long depId;

    private Long orderId;

    private List<DingWorkflowActor> actors;

}
