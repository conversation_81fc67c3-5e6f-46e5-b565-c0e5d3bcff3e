package com.niimbot.asset.dingtalk.base.config;

import com.aliyun.teaopenapi.models.Config;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @date 2023/4/13 11:23
 */
@Slf4j
@Configuration
public class ClientManage {

    @Bean("oauthClient")
    @Primary
    public com.aliyun.dingtalkoauth2_1_0.Client oauthClient() {
        Config config = new Config().setProtocol("https").setRegionId("central");
        try {
            return new com.aliyun.dingtalkoauth2_1_0.Client(config);
        } catch (Exception e) {
            log.error("init ding dingtalkoauth2_1_0 error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "init ding dingtalkoauth2_1_0 error");
        }
    }

    @Bean("workflowClient")
    @Primary
    public com.aliyun.dingtalkworkflow_1_0.Client workflowClient() {
        Config config = new Config().setProtocol("https").setRegionId("central");
        try {
            return new com.aliyun.dingtalkworkflow_1_0.Client(config);
        } catch (Exception e) {
            log.error("init ding dingtalkworkflow_1_0 error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "init ding dingtalkworkflow_1_0 error");
        }
    }

    @Bean("imV1Client")
    @Primary
    public com.aliyun.dingtalkim_1_0.Client imV1Client() {
        Config config = new Config().setProtocol("https").setRegionId("central");
        try {
            return new com.aliyun.dingtalkim_1_0.Client(config);
        } catch (Exception e) {
            log.error("init ding dingtalkim_1_0 error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "init ding dingtalkim_1_0 error");
        }
    }

    @Bean("imV2Client")
    @Primary
    public com.aliyun.dingtalkim_2_0.Client imV2Client() {
        Config config = new Config().setProtocol("https").setRegionId("central");
        try {
            return new com.aliyun.dingtalkim_2_0.Client(config);
        } catch (Exception e) {
            log.error("init ding dingtalkim_2_0 error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "init ding dingtalkim_2_0 error");
        }
    }

    @Bean("cardV1Client")
    @Primary
    public com.aliyun.dingtalkcard_1_0.Client cardV1Client() {
        Config config = new Config().setProtocol("https").setRegionId("central");
        try {
            return new com.aliyun.dingtalkcard_1_0.Client(config);
        } catch (Exception e) {
            log.error("init ding dingtalkcard_1_0 error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "init ding dingtalkcard_1_0 error");
        }
    }

    @Bean("contactClient")
    @Primary
    public com.aliyun.dingtalkcontact_1_0.Client contactClient() {
        Config config = new Config().setProtocol("https").setRegionId("central");
        try {
            return new com.aliyun.dingtalkcontact_1_0.Client(config);
        } catch (Exception e) {
            log.error("init ding dingtalkcontact_1_0 error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "init ding dingtalkcontact_1_0 error");
        }
    }

    @Bean("connectorClient")
    @Primary
    public com.aliyun.dingtalkconnector_1_0.Client connectorClient() {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        try {
            return new com.aliyun.dingtalkconnector_1_0.Client(config);
        } catch (Exception e) {
            log.error("init ding connector client error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "init ding connector client error");
        }
    }

}
