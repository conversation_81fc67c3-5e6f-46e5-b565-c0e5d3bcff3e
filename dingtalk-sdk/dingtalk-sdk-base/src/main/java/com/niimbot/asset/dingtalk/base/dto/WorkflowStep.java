package com.niimbot.asset.dingtalk.base.dto;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowStep {
    private Integer type;
    private Long stepId;
    private String stepDesc;
    private String stepName;
    private Integer handleType;
    private List<JSONObject> approverList;
    private List<JSONObject> condition;

    public List<Long> resolveApproveEmpIds() {
        if (CollUtil.isEmpty(approverList)) {
            return Collections.emptyList();
        }
        return approverList.stream().map(o -> o.getLong("id")).collect(Collectors.toList());
    }

}
