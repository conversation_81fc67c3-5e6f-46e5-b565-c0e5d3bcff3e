package com.niimbot.asset.dingtalk.base.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.dingtalk.base.handler.LongListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DingSyncChange对象", description = "钉钉组织员工异动记录")
@TableName(value = "ding_sync_change", autoResultMap = true)
public class DingSyncChange implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织员工异动ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "资源ID（员工，组织）")
    private Long resId;

    @ApiModelProperty(value = "异动类型（1-编辑员工，2-删除员工，3-组织异动）")
    private Integer type;

    @ApiModelProperty(value = "原始部门")
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> fromOrg;

    @ApiModelProperty(value = "新部门")
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> toOrg;

    @ApiModelProperty(value = "状态（1-待处理，2-已处理）")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
