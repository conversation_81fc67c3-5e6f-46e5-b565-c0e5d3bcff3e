package com.niimbot.asset.dingtalk.base.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/2/24 11:44
 */
@Data
@Accessors(chain = true)
public class GoodsTryout {

    // mLyKVGiiwEXQ5mT0Nwxxxx
    private String buyerUnionId;

    // 698002
    private String suiteId;

    // dingd678857e4250521135c2f465xxxx
    private String corpId;

    // 2020-12-04 10:59:25
    private String fromDate;

    // 2020-12-19 10:59:25
    private String endDate;

    // goods_tryout
    private String EventType;

    // 94
    private String appId;

    // enterprise_tryout
    private String tryoutType;

    // FW_GOODS-100030xxxx
    private String goodsCode;

    // charge_goods_free_item
    private String itemType;

    // 160705076562
    private String timeStamp;

    public enum TryoutType {
        /**
         * 个人开通
         */
        PERSONAL_TRYOUT("personal_tryout"),
        /**
         * 企业开通（管理员）
         */
        ENTERPRISE_TRYOUT("enterprise_tryout");

        private final String value;

        public String getValue() {
            return this.value;
        }

        TryoutType(String value) {
            this.value = value;
        }
    }

}
