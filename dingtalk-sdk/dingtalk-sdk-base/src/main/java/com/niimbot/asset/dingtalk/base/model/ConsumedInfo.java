package com.niimbot.asset.dingtalk.base.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ConsumedInfo implements Serializable {

    private JSONObject designate = new JSONObject();

    public static ConsumedInfoBuild builder() {
        return new ConsumedInfoBuild();
    }

    public static class ConsumedInfoBuild {

        /**
         * 精条数量
         */
        private final JSONObject object = new JSONObject();

        public ConsumedInfoBuild currency(Integer currency) {
            object.put("currencyQuantity", currency);
            return this;
        }

        public ConsumedInfoBuild clean() {
            this.object.clear();
            return this;
        }

        public ConsumedInfo build() {
            return new ConsumedInfo(object);
        }
    }

}
