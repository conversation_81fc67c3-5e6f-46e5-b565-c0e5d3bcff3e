package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2023/2/1 16:15
 */
@Data
public class EmployeeUpdateDto {

    @ApiModelProperty(value = "员工id")
    @NotNull(message = "请输入员工id")
    private Long id;

    @ApiModelProperty(value = "工号")
    @Pattern(regexp = "^$|^[a-zA-Z0-9-_]{2,15}$", message = "请输入工号 / 2-15位，只能由字母、数字、符号-_组成")
    private String empNo;

    private String remark;

}
