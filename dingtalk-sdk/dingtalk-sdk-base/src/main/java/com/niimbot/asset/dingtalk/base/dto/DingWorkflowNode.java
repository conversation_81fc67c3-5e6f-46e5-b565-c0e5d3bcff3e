package com.niimbot.asset.dingtalk.base.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DingWorkflowNode implements Serializable {

    /**
     * 获取成功后返回的唯一标识，在发起审批实例时需要传
     */
    private String uid;

    /**
     * 钉钉响应结果
     */
    private Object result;

    /**
     * activityId -> ( actorKey -> List )
     */
    private Map<String, Actor> userMap;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Actor implements Serializable {
        private String actorKey;
        private List<EmpInfo> empInfos;
    }

}
