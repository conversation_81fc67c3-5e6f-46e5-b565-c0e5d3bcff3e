package com.niimbot.asset.dingtalk.base.config;

import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponseBody;
import com.aliyun.tea.TeaException;
import com.niimbot.asset.dingtalk.base.model.constant.RedisConstant;
import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户access_token管理
 *
 * <AUTHOR>
 * @date 2021/12/22 17:23
 */
@Slf4j
@Component
public class UserAuthManage {

    private final RedisService redisService;

    private final com.aliyun.dingtalkoauth2_1_0.Client oauthClient;

    @Autowired
    public UserAuthManage(RedisService redisService, Client oauthClient) {
        this.redisService = redisService;
        this.oauthClient = oauthClient;
    }

    /**
     * https://open.dingtalk.com/document/orgapp-server/obtain-user-token
     * 获取用户 access_token
     * @param userId 用户id
     * @param authCode 授权码
     * @return
     */
    public String getUserToken(Long userId, String authCode) {
        // 查询缓存是否存在user_access_token
        String userToken = Convert.toStr(redisService.get(RedisConstant.getUserTokenKey(userId)));
        if (StrUtil.isBlank(userToken)) {
            GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                    .setClientSecret(SdkConstant.SUITE_SECRET)
                    .setClientId(SdkConstant.SUITE_KEY)
                    .setCode(authCode)
                    .setGrantType("authorization_code");
            try {
                GetUserTokenResponse rsp = oauthClient.getUserToken(getUserTokenRequest);
                if (rsp != null) {
                    GetUserTokenResponseBody responseBody = rsp.getBody();
                    userToken = responseBody.getAccessToken();
                    long expiresIn = responseBody.getExpireIn() - 300;
                    redisService.set(RedisConstant.getUserTokenKey(userId), userToken, expiresIn, TimeUnit.SECONDS);
                } else {
                    log.warn("get user token error, response ==>> {}", rsp);
                    throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "获取用户Access_Token失败");
                }
            } catch (TeaException err) {
                if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    log.error("get user token error ==>> [{}] {}", err.code, err.message);
                    throw new BusinessException(HttpStatus.BAD_REQUEST.value(), err.message);
                }
            } catch (Exception _err) {
                TeaException err = new TeaException(_err.getMessage(), _err);
                if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    log.error("get user token error ==>> [{}] {}", err.code, err.message);
                    throw new BusinessException(HttpStatus.BAD_REQUEST.value(), err.message);
                }
                log.error("get user token error", _err);
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), _err.getMessage());
            }
        }
        return userToken;
    }
}
