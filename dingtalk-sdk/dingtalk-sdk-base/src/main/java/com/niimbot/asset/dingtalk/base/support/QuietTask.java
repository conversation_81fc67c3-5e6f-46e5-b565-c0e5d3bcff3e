package com.niimbot.asset.dingtalk.base.support;

import com.niimbot.asset.framework.support.Executable;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutor;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 静默任务执行类
 *
 * <AUTHOR>
 */
@Slf4j
public class QuietTask {

    private QuietTask() {
    }

    private static final AssetThreadPoolExecutor QUITE_TASK_THREAD_POOL = AssetThreadPoolExecutorManager.newThreadPool("QuietTask", 10, 20, 20);

    public static <T> void syncExecute(Consumer<T> consumer, T params) {
        try {
            consumer.accept(params);
        } catch (Exception e) {
            log.warn("quiet task execute error", e);
        }
    }

    public static <T> void asyncExecute(Consumer<T> consumer, T params) {
        QUITE_TASK_THREAD_POOL.execute(() -> syncExecute(consumer, params));
    }

    public static <T> void asyncExecute(Executable executable) {
        QUITE_TASK_THREAD_POOL.execute(() -> {
            try {
                executable.execute();
            } catch (Exception e) {
                log.warn("quiet task execute error", e);
            }
        });
    }

    public static <T> void syncExecute(Executable executable) {
        try {
            executable.execute();
        } catch (Exception e) {
            log.warn("quiet task execute error", e);
        }
    }

    public static <T> T syncExecuteS(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.warn("quiet task execute error", e);
            return null;
        }
    }

    public static <T> T syncExecuteS(Supplier<T> supplier, T defaultValue) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.warn("quiet task execute error", e);
            return defaultValue;
        }
    }

    public static <T, R> R syncExecuteF(Function<T, R> function, T params) {
        try {
            return function.apply(params);
        } catch (Exception e) {
            return null;
        }
    }

    public static <T, R> R syncExecuteF(Function<T, R> function, T params, R defaultValue) {
        try {
            return function.apply(params);
        } catch (Exception e) {
            log.warn("quiet task execute error", e);
            return defaultValue;
        }
    }

}
