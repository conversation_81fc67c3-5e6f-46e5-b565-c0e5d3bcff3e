package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/10 上午10:12
 */
@Data
@ApiModel(value = "OrderProductRequestDto对象", description = "订单商品对象")
public class OrderProductRequestDto implements Serializable {

    private static final long serialVersionUID = 3731714192815347699L;

    @NotBlank(message = "商品编码为空")
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @NotBlank(message = "商品名称为空")
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "购买数量")
    private Integer count;

    @NotNull(message = "商品类型为空")
    @ApiModelProperty(value = "商品类型 1-软件 2-硬件")
    private Integer goodsType;
}
