package com.niimbot.asset.dingtalk.base.model.constant.enums;

import java.util.Arrays;

/**
 * 消息跳转地址枚举
 *
 * <AUTHOR>
 * @date 2021/8/26 16:01
 */
public enum MessageUrl {
    /**
     * 单据类型 ———— 领用
     */
    ORDER_TYPE_RECEIVE(1, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 退还
     */
    ORDER_TYPE_RETURN(2, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 借用
     */
    ORDER_TYPE_BORROW(3, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 归还
     */
    ORDER_TYPE_BACK(4, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 调拨
     */
    ORDER_TYPE_ALLOCATE(5, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 报修
     */
    ORDER_TYPE_REPAIR_REPORT(6, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 维修
     */
    ORDER_TYPE_REPAIR(7, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 处置
     */
    ORDER_TYPE_DISPOSE(8, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 变更
     */
    ORDER_TYPE_CHANGE(9, "%sdocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-order-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ———— 采购
     */
    ORDER_TYPE_PURCHASE(10, "%semptyPage/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/purchase-apply-info?orderType=%d&docId=%d"),
    /**
     * 单据类型 ————31-耗材入库
     */
    ORDER_TYPE_MATERIAL_RK(31, "%smaterialSubPages/DocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/material-order-detail?orderType=%d&docId=%d"),
    /**
     * 单据类型 ————32-耗材出库
     */
    ORDER_TYPE_MATERIAL_CK(32, "%smaterialSubPages/DocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/material-order-detail?orderType=%d&docId=%d"),
    /**
     * 单据类型 ————33-耗材领用
     */
    ORDER_TYPE_MATERIAL_LY(33, "%smaterialSubPages/DocumentDetail/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/material-order-detail?orderType=%d&docId=%d"),
    /**
     * 资产上报
     */
    ORDER_TYPE_ASSET_REPORT(98, "%sinventorySubPages/InventoryReport/index?taskId=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/asset-report-child-task-view?id=%d"),
    /**
     * 资产盘点
     */
    ORDER_TYPE_ASSET_INVENTORY(99, "%spages/inventory/task-detail/index?inventoryTaskId=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/inventory-child-task-view?id=%d"),
    /**
     * 资产盘点审核
     */
    ORDER_TYPE_ASSET_INVENTORY_EXAMINE(96, "%scommon-sub/empty/index?id=%d&orderType=%d&corpId=%s", "%s?corpId=%s#/backlog/inventory-task-view?id=%d");

    private final int code;
    private final String appUrl;
    private final String pcUrl;

    MessageUrl(int code, String appUrl, String pcUrl) {
        this.code = code;
        this.appUrl = appUrl;
        this.pcUrl = pcUrl;
    }

    public int getCode() {
        return code;
    }

    public String getAppUrl() {
        return appUrl;
    }

    public String getPcUrl() {
        return pcUrl;
    }

    public static MessageUrl getEnum(int code) {
        return Arrays.stream(MessageUrl.values()).filter(item -> item.code == code).findFirst().orElse(null);
    }
}
