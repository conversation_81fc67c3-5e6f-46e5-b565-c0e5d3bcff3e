package com.niimbot.asset.dingtalk.base.model.constant;

import cn.hutool.core.util.URLUtil;

/**
 * 钉钉URL统一跳转协议
 *
 * <AUTHOR>
 */
public interface DingUrl {

    /**
     * 小程序统一跳转协议
     */
    String MICRO_APP = "dingtalk://dingtalkclient/action/open_micro_app?corpId=%s&appId=%s&miniAppId=%s&page=%s";

    /**
     * PC链接跳转至工作台
     */
    String WEB_PC = "dingtalk://dingtalkclient/action/openapp?corpid=%s&container_type=work_platform&app_id=%s&redirect_type=jump&redirect_url=%s";

    /**
     * 获取小程序跳转链接
     *
     * @param corpId corpId
     * @param url    小程序路由
     * @return jump url
     */
    static String getMicroAppJumpUrl(String corpId, String url) {
        return String.format(MICRO_APP, corpId, SdkConstant.APP_ID, SdkConstant.MINI_APP_ID, URLUtil.encodeAll(url));
    }

    /**
     * 获取PC端工作台打开方式跳转链接
     *
     * @param corpId corpId
     * @param url    PC路由
     * @return jump url
     */
    static String getWebPcJumpUrl(String corpId, String url) {
        return String.format(WEB_PC, corpId, SdkConstant.APP_ID, URLUtil.encodeAll(url));
    }

}
