package com.niimbot.asset.dingtalk.base.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DingCompanyGoodsBill", description = "钉钉企业商品月账单统计")
@TableName(value = "ding_company_consume_bill", autoResultMap = true)
public class DingCompanyConsumeBill implements Serializable {

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("企业ID")
    private Long companyId;

    @ApiModelProperty("商品ID")
    private String goodsSkuCode;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("统计周期开始日期")
    private LocalDate statisticalStartTime;

    @ApiModelProperty("统计周期结束日期")
    private LocalDate statisticalEndTime;

    @ApiModelProperty("信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject info;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public MonthlyCurrencyBillInfo convertBillInfoToCurrency() {
        return this.info.toJavaObject(MonthlyCurrencyBillInfo.class);
    }

    public void convertBillInfoToJsonObject(Object bill) {
        this.info = (JSONObject) JSONObject.toJSON(bill);
    }

}
