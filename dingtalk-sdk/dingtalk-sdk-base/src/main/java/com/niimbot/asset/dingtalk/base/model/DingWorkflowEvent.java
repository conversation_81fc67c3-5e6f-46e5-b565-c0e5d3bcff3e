package com.niimbot.asset.dingtalk.base.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 钉钉审批事件
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class DingWorkflowEvent implements Serializable {

    private String eventType;

    private String processInstanceId;

    private String corpId;

    private Long createTime;

    private String title;

    private String staffId;

    private String url;

    private String processCode;

    private Long finishTime;

    private String type;

    private String result;

    private String remark;

}
