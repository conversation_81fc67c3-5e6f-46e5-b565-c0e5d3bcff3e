package com.niimbot.asset.dingtalk.base.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class DingOrderTypeExt {

    private Long orderTypeId;

    private Long companyId;

    private String processCode;

    private Integer way;

    private Integer type;

}
