package com.niimbot.asset.dingtalk.base.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * created by chen.y on 2022/5/23 15:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DingOrderConsume对象", description = "钉钉订单核销")
public class DingOrderConsume {

    @ApiModelProperty(value = "核销ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "企业ID")
    private String corpId;

    @ApiModelProperty(value = "核销人员ID")
    private String adminUserId;

    @ApiModelProperty(value = "核销数量")
    private Integer num;

    @ApiModelProperty(value = "是否核销")
    private Boolean consume;

    @ApiModelProperty("核销账单ID")
    private Long consumeBillId;

    @ApiModelProperty("核销详情")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject info;

    @ApiModelProperty(value = "操作时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
