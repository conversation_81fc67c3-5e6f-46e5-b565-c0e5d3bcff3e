package com.niimbot.asset.dingtalk.base.service;

import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.dingtalk.api.response.*;
import com.niimbot.asset.dingtalk.base.dto.AuthScopes;
import com.niimbot.asset.dingtalk.base.dto.DingResourcePayDto;
import com.niimbot.asset.dingtalk.base.dto.DingRole;

import java.util.List;
import java.util.Set;

/**
 * created by chen.y on 2021/8/23 17:00
 */
public interface DingOpenApiService {

    /**
     * https://developers.dingtalk.com/document/app/query-user-details 根据userid获取用户详情
     *
     * @param corpId 企业Id
     * @param userId 用户Id
     * @return 用户信息
     */
    OapiV2UserGetResponse.UserGetResponse getUserByUserId(String userId, String corpId, String corpToken);

    /**
     * https://developers.dingtalk.com/document/app/obtain-application-suite-ticket
     * 获取第三方企业应用的suite_access_token
     *
     * @return suite_access_token
     */
    String getSuiteToken();

    /**
     * https://developers.dingtalk.com/document/app/obtain-the-userid-of-a-user-by-using-the-log-free
     * 通过免登码获取用户信息(v2)
     *
     * @param code      免登授权码
     * @param corpToken 企业access_token
     * @return 用户信息
     */
    OapiV2UserGetuserinfoResponse.UserGetByCodeResponse getUserInfo(String code, String corpToken);

    /**
     * https://developers.dingtalk.com/document/app/obtain-the-user-information-based-on-the-sns-temporary-authorization
     * 根据sns临时授权码获取用户信息
     *
     * @param tmpAuthCode 临时授权码
     * @return 用户信息
     */
    OapiSnsGetuserinfoBycodeResponse.UserInfo getUserInfoByCode(String tmpAuthCode);

    /**
     * https://open.dingtalk.com/document/app/obtain-corpsecret-authorization-scope 获取通讯录权限范围
     *
     * @param corpToken 企业访问授权码
     * @return 用户信息
     */
    AuthScopes getAuthScopes(String corpToken);

    /**
     * https://open.dingtalk.com/document/isvapp-server/dingtalk-retrieve-user-information
     * 获取通讯录用户信息
     *
     * @param unionId     钉钉的openId
     * @param accessToken 用户token
     * @return
     */
    GetUserResponseBody getContactUser(String unionId, String accessToken);

    String getSku(String code, String callback, String corpToken);

    Boolean orderFinish(String orderId, String corpToken);

    List<OapiAppstoreInternalUnfinishedorderListResponse.InAppGoodsOrderVO> unfinishedorder(String itemCode);

    Boolean orderConsume(String orderId, String userId, Integer number, String corpToken);

    Set<String> getUserIdsByRole(String roleId, String corpToken);

    String getPayUrl(DingResourcePayDto payDto, String corpToken);

    /**
     * https://open.dingtalk.com/document/isvapp/querying-group-information
     *
     * @param openConversationId
     * @param corpId
     * @return
     */
    String queryGroup(String openConversationId, String corpId);

    void registerCallback(String corpId, String templateType);

    List<OapiRoleListResponse.OpenRoleGroup> getRoles(String corpId);

    List<OapiRoleSimplelistResponse.OpenEmpSimple> getRoleUsers(String corpId, Long roleId);

    OapiRoleGetrolegroupResponse.OpenRoleGroup getRolesByGroupId(String corpId, Long roleGroupId);

    OapiRoleGetroleResponse.OpenRole getRoleById(String corpId, Long roleId);

    List<DingRole> getRoles(String corpId, List<Long> ids);
}
