package com.niimbot.asset.dingtalk.base.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_shopping_cart
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_shopping_cart", autoResultMap = true)
public class AsShoppingCart implements Serializable {

    private static final long serialVersionUID = 5750242001392874151L;

    /**
     * 主键Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商品类型 1-软件 2-硬件
     */
    private Integer goodsType;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 商品价值，软件商品资源包容量
     */
    private Integer goodsValue;

    /**
     * 购买状态
     */
    private Integer status;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}