package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * created by chen.y on 2021/9/30 16:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "TransferDto对象", description = "异动转移对象")
public class TransferDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "被转移部门")
    @NotNull(message = "被转移部门不能为空")
    private Long from;

    @ApiModelProperty(value = "接受部门")
    @NotNull(message = "接受部门不能为空")
    private Long to;

    @ApiModelProperty(hidden = true)
    private Boolean allowTrans;

}
