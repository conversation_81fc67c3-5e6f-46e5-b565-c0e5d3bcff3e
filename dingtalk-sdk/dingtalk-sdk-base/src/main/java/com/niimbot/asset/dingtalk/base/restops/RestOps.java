package com.niimbot.asset.dingtalk.base.restops;

import com.aliyun.tea.TeaException;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.JacksonConverter;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class RestOps {

    public static <R> R handleOrThrow(SupplierThrowable<R> supplier) {
        try {
            R r = supplier.get();
            log.info("ding response : [{}]", JacksonConverter.MAPPER.writeValueAsString(r));
            return r;
        } catch (Exception e) {
            if (e instanceof TeaException) {
                TeaException teaException = (TeaException) e;
                String code = teaException.getCode();
                String message = teaException.getMessage();
                log.error("ding request error, code : [{}], message : [{}]", code, message);
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, message);
            } else {
                log.error("ding request error", e);
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请求钉钉异常，请稍后再试");
            }
        }
    }

}
