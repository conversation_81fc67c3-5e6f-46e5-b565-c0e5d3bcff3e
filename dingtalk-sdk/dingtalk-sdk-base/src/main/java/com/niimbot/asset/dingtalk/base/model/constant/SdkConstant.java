package com.niimbot.asset.dingtalk.base.model.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class SdkConstant {

    public static final String IP_LOCK = "ip_lock";

    public static String API_URL = "https://oapi.dingtalk.com";

    public static String SUITE_SECRET = "";

    public static String SUITE_KEY = "";

    public static String MINI_APP_ID = "";

    public static String APP_ID = "";

    public static String SUITE_ID = "";

    public static String TOKEN = "";

    public static String AES_KEY = "";

    public static String DOMAIN_PC = "https://localhost:8080";

    public static String DOMAIN_APP = "https://localhost:8080";

    public static String DOMAIN_H5 = "https://localhost:8080";

    public static String ROBOT_CODE = "";

    public static String COOL_APP_CODE = "";

    public static String COOL_APP_CALLBACK = "";

    @Value("${asset.dingtalk.suiteSecret}")
    public void setSuiteSecret(String suiteSecret) {
        SdkConstant.SUITE_SECRET = suiteSecret;
    }

    @Value("${asset.dingtalk.suiteKey}")
    public void setSuiteKey(String suiteKey) {
        SdkConstant.SUITE_KEY = suiteKey;
    }

    @Value("${asset.dingtalk.miniAppId}")
    public void setMiniAppId(String miniAppId) {
        SdkConstant.MINI_APP_ID = miniAppId;
    }

    @Value("${asset.dingtalk.appId}")
    public void setAppId(String appId) {
        SdkConstant.APP_ID = appId;
    }

    @Value("${asset.dingtalk.suiteId}")
    public void setSuiteId(String suiteId) {
        SdkConstant.SUITE_ID = suiteId;
    }

    @Value("${asset.dingtalk.token}")
    public void setToken(String token) {
        SdkConstant.TOKEN = token;
    }

    @Value("${asset.dingtalk.aesKey}")
    public void setAesKey(String aesKey) {
        SdkConstant.AES_KEY = aesKey;
    }

    @Value("${asset.domain.pc:#{null}}")
    public void setDomainPc(String pc) {
        SdkConstant.DOMAIN_PC = pc;
    }

    @Value("${asset.domain.app:#{null}}")
    public void setDomainApp(String app) {
        SdkConstant.DOMAIN_APP = app;
    }

    @Value("${asset.domain.h5:#{null}}")
    public void setDomainH5(String h5) {
        SdkConstant.DOMAIN_H5 = h5;
    }

    @Value("${asset.dingtalk.robotCode}")
    public void setRobotCode(String robotCode) {
        SdkConstant.ROBOT_CODE = robotCode;
    }

    @Value("${asset.dingtalk.coolAppCode}")
    public void setCoolAppCode(String coolAppCode) {
        SdkConstant.COOL_APP_CODE = coolAppCode;
    }

    @Value("${asset.dingtalk.coolAppCallback}")
    public void setCoolAppCallback(String coolAppCallback) {
        SdkConstant.COOL_APP_CALLBACK = coolAppCallback;
    }

    public static final String GET_SUITE_TOKEN = API_URL + "/service/get_suite_token";

    public static final String GET_CORP_TOKEN = API_URL + "/service/get_corp_token";

    public static final String GET_USER_INFO = API_URL + "/topapi/v2/user/getuserinfo";

    public static final String DEPARTMENT_LIST_SUB = API_URL + "/topapi/v2/department/listsub";

    public static final String DEPARTMENT_LIST_SUB_ID = API_URL + "/topapi/v2/department/listsubid";

    public static final String GET_DEPARTMENT = API_URL + "/topapi/v2/department/get";

    public static final String USER_LIST = API_URL + "/topapi/v2/user/list";

    public static final String GET_USER_BY_USER_ID = API_URL + "/topapi/v2/user/get";

    public static final String SEND_MESSAGE = API_URL + "/topapi/message/corpconversation/sendbytemplate";

    public static final String GET_USERINFO_BY_CODE = API_URL + "/sns/getuserinfo_bycode";

    public static final String GET_AUTH_SCOPES = API_URL + "/auth/scopes";

    public static final String GET_SKU = API_URL + "/topapi/appstore/internal/skupage/get";

    public static final String ORDER_FINISH = API_URL + "/topapi/appstore/internal/order/finish";

    public static final String UNFINISHED_ORDER_LIST = API_URL + "/topapi/appstore/internal/unfinishedorder/list";

    public static final String ORDER_CONSUME = API_URL + "/topapi/appstore/internal/order/consume";

    public static final String GET_AUTH_INFO = API_URL + "/service/get_auth_info";
}
