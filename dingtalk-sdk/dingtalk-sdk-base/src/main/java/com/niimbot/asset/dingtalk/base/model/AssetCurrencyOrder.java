package com.niimbot.asset.dingtalk.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AssetCurrencyOrder implements Serializable {

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("购买的精条数")
    private Integer totalNum;

    @ApiModelProperty("已核销的精条数")
    private Integer consumeNum;

    @ApiModelProperty("剩余的精条数")
    private Integer surplusNum;

}
