package com.niimbot.asset.dingtalk.base.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "GetDingWorkflowNode", description = "获取钉钉表单节点信息")
public class GetDingWorkflowNode implements Serializable {

    private Long depId;

    private Long orderTypeId;

    @ApiModelProperty("单据业务类型：asset | material")
    @NotBlank(message = "单据业务类型不能为空")
    @Pattern(regexp = "asset|material", message = "当前仅支持资产或耗材单据")
    private String bizType;

    @ApiModelProperty("单据类型")
    @NotBlank(message = "单据类型不能为空")
    private String orderType;

    @ApiModelProperty("单据表单内容")
    @NotNull(message = "单据表单信息不能为空")
    private JSONObject orderData;

    @ApiModelProperty("业务表单内容")
    private List<JSONObject> bizData;
}
