package com.niimbot.asset.dingtalk.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "RevocationWorkflowProcess", description = "撤销钉钉审批实例")
public class RevocationWorkflowProcess implements Serializable {

    private Long orderId;

    private String bizType;

    @ApiModelProperty("钉钉实例ID")
    private String procInstId;

}
