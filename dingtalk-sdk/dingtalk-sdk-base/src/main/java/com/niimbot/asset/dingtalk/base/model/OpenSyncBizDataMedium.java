package com.niimbot.asset.dingtalk.base.model;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * created by chen.y on 2021/8/23 9:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OpenSyncBizDataMedium对象", description = "OpenSyncBizDataMedium对象")
@TableName(value = "open_sync_biz_data_medium", autoResultMap = true)
public class OpenSyncBizDataMedium extends AbsOpenSyncBizData {

    public OpenSyncBizDataMedium jsonToObj(JSONObject json) {
        this.setId(json.getLong("id"));
        this.setSubscribeId(json.getString("subscribe_id"));
        this.setCorpId(json.getString("corp_id"));
        this.setBizId(json.getString("biz_id"));
        this.setBizType(json.getInteger("biz_type"));
        this.setBizData(json.getJSONObject("biz_data"));
        this.setGmtCreate(LocalDateTimeUtil.of(json.getLong("gmt_create")));
        this.setGmtModified(LocalDateTimeUtil.of(json.getLong("gmt_modified")));
        this.setOpenCursor(json.getLong("open_cursor"));
        this.setStatus(json.getInteger("status"));
        return this;
    }

}
