package com.niimbot.asset.dingtalk.base.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 钉钉订单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DingOrder对象", description = "钉钉订单")
@TableName(value = "ding_order", autoResultMap = true)
public class DingOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司Id")
    private Long companyId;

    @ApiModelProperty(value = "订单Id")
    private String orderId;

    @ApiModelProperty(value = "用户购买套件的suiteId")
    private String suiteId;

    @ApiModelProperty(value = "用户购买套件的suiteKey")
    private String suiteKey;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品码")
    private String goodsCode;

    @ApiModelProperty("购买的商品")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject goodsInfo;

    @ApiModelProperty(value = "规格名称")
    private String itemName;

    @ApiModelProperty(value = "规格码")
    private String itemCode;

    @ApiModelProperty(value = "购买数量")
    private Integer subQuantity;

    @ApiModelProperty(value = "核销数量")
    private Integer consumeQuantity = 0;

    @ApiModelProperty(value = "购买企业的corpId")
    private String corpId;

    @ApiModelProperty(value = "支付时间（单位：毫秒）")
    private Long paidTime;

    @ApiModelProperty(value = "服务结束时间（单位：毫秒）")
    private Long serviceStopTime;

    @ApiModelProperty(value = "实际支付价格（单位：分）")
    private Integer payFee;

    @ApiModelProperty(value = "服务开始时间（单位：毫秒）")
    private Long serviceStartTime;

    @ApiModelProperty(value = "订单类型  BUY：新购，RENEW：续费，UPGRADE：升级，RENEW_UPGRADE：续费升配，RENEW_DEGRADE：续费降配")
    private String orderType;

    @ApiModelProperty(value = "下单人unionId")
    private String unionId;

    @ApiModelProperty(value = "外部订单号")
    private String outTradeNo;

    @ApiModelProperty(value = "下单人在企业内的工号")
    private String buyUserId;

    @ApiModelProperty("订单来源")
    private String orderCreatSource;

    @ApiModelProperty(value = "商机来源")
    private String leadsFrom;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "订单状态（1-系统未处理，2-系统已处理，3-已推送完成，4-已核销）")
    private Integer status;

    @ApiModelProperty("订单核销状态(1-未核销 2-部分核销 3-已核销)")
    private Integer consumeStatus;

    @ApiModelProperty(value = "原始数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject sourceData;

    public void consumeStatus() {
        if (Objects.nonNull(this.consumeStatus) && this.consumeStatus == 3) {
            return;
        }
        if (this.consumeQuantity == 0) {
            this.consumeStatus = 1;
        }
        if (this.consumeQuantity > 0 && this.consumeQuantity < this.subQuantity) {
            this.consumeStatus = 2;
        }
        if (this.consumeQuantity.equals(this.subQuantity)) {
            this.consumeStatus = 3;
        }
    }

    public void convertGoodsInfoToJsonObject(Object object) {
        this.goodsInfo = ((JSONObject) JSONObject.toJSON(object));
    }

    public CurrencyGoodsInfo convertGoodInfoToCurrency() {
        if (Objects.isNull(this.goodsInfo)) {
            return new CurrencyGoodsInfo();
        }
        return this.goodsInfo.toJavaObject(CurrencyGoodsInfo.class);
    }

}
