package com.niimbot.asset.dingtalk.base.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MonthlyCurrencyBillInfo implements Serializable {

    @ApiModelProperty("本月花费的销精条数（云资产）")
    private BigDecimal costConsumed;

    @ApiModelProperty("本月能核销的精条数（云资产）")
    private BigDecimal allowConsumed;

    @ApiModelProperty("上月剩余未核销精条数（钉钉）")
    private BigDecimal remainingNotConsumedLastMonth;

    @ApiModelProperty("需核销精条数（钉钉）")
    private BigDecimal needConsumed;

    @ApiModelProperty("实际核销精条数（钉钉）")
    private Integer actualConsumed;

    @ApiModelProperty("本月剩余未核销精条数（钉钉）")
    private BigDecimal remainingNotConsumedThisMonth;

}
