package com.niimbot.asset.dingtalk.base.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * id 确定唯一
 * assetProcInstId 确定唯一
 * dingProcInstId 确定唯一
 * <p>
 * 多个流程实例中的stepId可能相同
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DingWorkflowTask", description = "钉钉自有OA审批流程中心任务记录表")
@TableName(value = "ding_workflow_task")
public class DingWorkflowTask implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("企业ID")
    private Long companyId;

    @ApiModelProperty("云资产")
    private String assetProcInstId;

    @ApiModelProperty("钉钉流程实例ID")
    private String dingProcInstId;

    @ApiModelProperty("云资产流程节点ID")
    private Long stepId;

    @ApiModelProperty("钉钉流程中心任务ID")
    private Long taskId;

    @ApiModelProperty("钉钉用户ID")
    private String userId;

    @ApiModelProperty("云资产员工ID")
    private Long empId;

    @ApiModelProperty("钉钉流程中心任务状态")
    private String status;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
