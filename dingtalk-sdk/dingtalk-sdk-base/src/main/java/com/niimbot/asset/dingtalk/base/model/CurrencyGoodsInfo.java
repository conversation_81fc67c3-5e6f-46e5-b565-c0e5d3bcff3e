package com.niimbot.asset.dingtalk.base.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyGoodsInfo implements Serializable {

    /**
     * 购买的精条数量
     */
    private Integer amountOfCurrencyPurchased = 0;

    /**
     * 已核销的精条数量
     */
    private Integer amountOfCurrencyConsumed = 0;

}
