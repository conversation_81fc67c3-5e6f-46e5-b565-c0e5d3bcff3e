package com.niimbot.asset.dingtalk.base.dto;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dingtalk.base.model.constant.SdkConstant;
import com.niimbot.asset.dingtalk.base.model.constant.enums.MessageUrl;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DingWorkflow implements Serializable {

    private Long workflowId;

    private Integer way;

    /**
     * 状态
     */
    private String status;

    /**
     * 标记
     */
    private Boolean flag = false;

    /**
     * 流程KEY
     */
    private String activitiKey;

    /**
     * 节点ID
     */
    private Long stepId;

    /**
     * 员工列表
     */
    private List<Long> empIds;

    /**
     * 审批实例标题
     */
    private String title;

    /**
     * 最终审批状态
     */
    private String approveStatus;

    /**
     * 单据ID
     */
    private Long orderId;

    /**
     * 单据类型ID
     */
    private Long orderTypeId;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 业务类型：asset || material
     */
    private String bizType;

    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 单据数据
     */
    private JSONObject orderData;

    /**
     * 业务数据
     */
    private List<JSONObject> bizData;

    /**
     * 员工ID
     */
    private Long empId;

    private Long toEmpId;

    /**
     * 钉钉UserId
     */
    private String dingUserId;

    /**
     * 部门ID
     */
    private Long depId;

    /**
     * 钉钉审批流程获取节点信息后的唯一标识
     */
    private String forecastUid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 钉钉流程实例ID
     */
    private String proInstId;

    /**
     * 自选节点审批人设置
     */
    private List<DingWorkflowActor> actors;

    /**
     * 流程指定审批人设置
     */
    private List<DingWorkflowActor> approvers;

    /**
     * 流程抄送人列表
     */
    private List<Long> ccEmpIds;

    private String formId;

    private String formName;

    private String formDesc;

    private Boolean upgrade = false;

    public void detailsUrl(String corpId) {
        this.getOrderData().put("pcDetailsUrl", String.format(MessageUrl.getEnum(Integer.parseInt(this.getOrderType())).getPcUrl(), SdkConstant.DOMAIN_PC, corpId, Convert.toInt(this.getOrderType(), 0), Convert.toLong(this.getOrderId(), 0L)));
    }

    public static DingWorkflow buildForForm(Long companyId, String bizType, Long orderTypeId, String orderType, String formName, String formDesc) {
        return new DingWorkflow().setCompanyId(companyId).setOrderTypeId(orderTypeId).setBizType(bizType).setOrderType(orderType).setFormName(formName).setFormDesc(formDesc);
    }

    public static DingWorkflow buildForGetWorkflowNode(Long companyId, Long empId, Long depId, String bizType, Long orderTypeId, String orderType, JSONObject data, List<JSONObject> bizData) {
        return new DingWorkflow().setCompanyId(companyId).setOrderTypeId(orderTypeId).setEmpId(empId).setDepId(depId).setBizType(bizType).setOrderType(orderType).setOrderData(data).setBizData(bizData);
    }

    public static DingWorkflow buildForStartProcessInstance(Long companyId, Long empId, Long depId, String uid, String bizType, Long orderTypeId, String orderType, Long orderId, JSONObject data, List<JSONObject> bizData) {
        return new DingWorkflow().setCompanyId(companyId).setForecastUid(uid).setOrderTypeId(orderTypeId).setEmpId(empId).setDepId(depId).setBizType(bizType).setOrderType(orderType).setOrderId(orderId).setOrderData(data).setBizData(bizData);
    }

    public static DingWorkflow buildForGetProcessInstance(Long companyId, Long empId, String bizType, Long orderId) {
        return new DingWorkflow().setCompanyId(companyId).setOrderId(orderId).setEmpId(empId).setBizType(bizType);
    }

    public static DingWorkflow buildForRevocationProcessInstance(Long companyId, Long empId, String bizType, Long orderId, String proInstId) {
        return new DingWorkflow().setCompanyId(companyId).setOrderId(orderId).setEmpId(empId).setBizType(bizType);
    }

    public static DingWorkflow buildForRevocationProcessInstance(String proInstId) {
        return new DingWorkflow().setProInstId(proInstId);
    }

    public static DingWorkflow buildForOwnCreateForm(Long companyId, Long workflowId) {
        return new DingWorkflow().setCompanyId(companyId).setWorkflowId(workflowId);
    }

    public static DingWorkflow buildForOwnStartProcInst(Long companyId, Long empId, String activitiKey, Long orderId, JSONObject orderData, String title) {
        return new DingWorkflow().setCompanyId(companyId).setEmpId(empId).setActivitiKey(activitiKey).setOrderId(orderId).setOrderData(orderData).setTitle(title);
    }

    public static DingWorkflow buildForOwnEndProcInst(Long orderId, String approveStatus) {
        return new DingWorkflow().setOrderId(orderId).setApproveStatus(approveStatus);
    }

    public static DingWorkflow buildForOwnCreateTask(Long companyId, Long stepId, String proInstId) {
        return new DingWorkflow().setCompanyId(companyId).setStepId(stepId).setProInstId(proInstId);
    }

    public static DingWorkflow buildForOwnHandleApprovedTask(String procInstId, Long stepId, Long empId, Boolean flag) {
        return new DingWorkflow().setProInstId(procInstId).setStepId(stepId).setEmpId(empId).setFlag(flag);
    }

    public static DingWorkflow buildForOwnHandleRejectedTask(String procInstId, Long stepId, Long empId, Boolean flag) {
        return new DingWorkflow().setProInstId(procInstId).setStepId(stepId).setEmpId(empId).setFlag(flag);
    }

    public static DingWorkflow buildForOwnHandleForwardTask(String procInstId, Long stepId, Long empId, Long toEmpId) {
        return new DingWorkflow().setProInstId(procInstId).setStepId(stepId).setEmpId(empId).setToEmpId(toEmpId);
    }

    public static DingWorkflow buildForOwnHandleRevokedTask(String procInstId) {
        return new DingWorkflow().setProInstId(procInstId);
    }

    public static DingWorkflow buildForHandleCarbonCopy(String procInstId, List<Long> empIds) {
        return new DingWorkflow().setProInstId(procInstId).setEmpIds(empIds);
    }
}
