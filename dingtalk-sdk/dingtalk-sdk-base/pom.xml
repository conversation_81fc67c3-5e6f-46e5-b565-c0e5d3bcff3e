<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dingtalk-sdk</artifactId>
        <groupId>com.niimbot.asset.dingtalk</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dingtalk-sdk-base</artifactId>
    <description>钉钉-基础</description>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>com.niimbot.asset</groupId>
            <artifactId>asset-framework-common</artifactId>
            <version>${asset-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.niimbot.asset</groupId>
            <artifactId>asset-dto</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>1479188381469-20211220</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.5.59</version>
        </dependency>

        <dependency>
            <artifactId>commons-codec</artifactId>
            <groupId>commons-codec</groupId>
            <version>1.15</version>
        </dependency>

        <dependency>
            <groupId>com.taobao.top</groupId>
            <artifactId>lippi-oapi-encrpt</artifactId>
            <version>dingtalk</version>
        </dependency>
    </dependencies>

</project>