package com.niimbot.asset.customer.adapter.weixin.controller;

import com.niimbot.asset.customer.adapter.weixin.service.WxFeignClient;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.weixin.base.dto.WeixinCorpDto;
import com.niimbot.jf.core.component.annotation.ResultController;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/17 9:55
 */
@Slf4j
@Api(tags = "【企业微信】企业接口")
@ResultController
@RequestMapping("api/weixin/corp")
@RequiredArgsConstructor
@Validated
public class WxCorpController {

    private final WxFeignClient wxFeignClient;

    @ApiOperation(value = "获取微信企业信息")
    @GetMapping
    public WeixinCorpDto getCorp() {
        WeixinCorpDto corp = wxFeignClient.getCorp(null, LoginUserThreadLocal.getCompanyId());
        corp.setPermanentCode(null);
        corp.setAgentId(null);
        return corp;
    }

}
