package com.niimbot.asset.customer.adapter.weixin.controller.export;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetExcelEditService;
import com.niimbot.asset.service.AssetExcelService;
import com.niimbot.asset.service.AssetQueryFieldService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.system.AsCusUserSettingDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportDto;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/20 9:28
 */
@Slf4j
@Api(tags = "【企业微信】资产管理")
@ResultController
@RequestMapping("api/weixin/asset")
@RequiredArgsConstructor
@Validated
public class WxAssetExcelController {

    private final AssetExcelService assetExcelService;

    private final AssetExcelEditService assetExcelEditService;

    private final CusUserSettingFeignClient settingFeignClient;

    private final AssetQueryFieldService queryFieldService;

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    private final RedisService redisService;

    private final WxOpenApiService openApiService;

    private final FormFeignClient formFeignClient;

    private final DesensitizationDataUtil desensitizationDataUtil;

    @ApiOperation(value = "【PC】导出资产模板")
    @GetMapping(value = "/exportTemplate")
    public String exportTemplate(
            @ApiParam(name = "standardId", value = "标准品ID")
            @RequestParam(value = "standardId", required = false) Long standardId) {
        String fileName = "资产导入模板.xlsx";
        if (standardId != null) {
            FormVO standard = formFeignClient.getByFormId(new FormByIdQry(standardId,
                    ListUtil.of(1L, LoginUserThreadLocal.getCompanyId())));
            if (standard != null) {
                fileName = standard.getFormName() + "-" + fileName;
            }
        }
        ExcelWriter writer = assetExcelService.buildExcelWriter(standardId);
        return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName, writer);
    }

    @ApiOperation(value = "【PC】导入资产模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public void importTemplate(@ApiParam(name = "standardId", value = "标准品ID")
                               @RequestParam(value = "standardId", required = false) Long standardId,
                               @RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }

        try (InputStream stream = file.getInputStream()) {
            assetExcelService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId, standardId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels, HttpServletRequest request) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        assetExcelService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.ASSET));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation(value = "【PC】导出资产编辑模板")
    @GetMapping(value = "/exportEditTemplate")
    public String exportEditTemplate() {
        ExcelWriter writer = assetExcelEditService.buildEditExcelWriter();
        return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), "资产编辑模板.xlsx", writer);
    }

    @ApiOperation(value = "【PC】导入资产编辑模板")
    @PostMapping(value = "/importEditTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public void importEditTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }

        try (InputStream stream = file.getInputStream()) {
            assetExcelEditService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】批量导入错误编辑数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importEditError/{taskId}")
    public ImportDto importEditError(@PathVariable("taskId") Long taskId,
                                     @RequestBody List<List<Object>> sheetModels, HttpServletRequest request) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        assetExcelEditService.importEditErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.ASSET_EDIT));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation(value = "【PC】资产导出")
    @PostMapping("/export")
    public String export(@RequestBody AssetQueryConditionDto queryDto) {
        try {
            List<QueryConditionDto> queryCondition = queryFieldService.assetAllHeadField();
            if (CollUtil.isNotEmpty(queryDto.getExpStandardIds())) {
                try {
                    List<QueryConditionStandardDto> standardDtos = queryFieldService.standardAllField(queryDto.getExpStandardIds(), false, true);
                    for (QueryConditionStandardDto standardDto : standardDtos) {
                        queryCondition.addAll(standardDto.getConditions());
                    }
                } catch (Exception e) {
                    log.error("export asset excel error, {}", e.getMessage(), e);
                }
            }
            queryCondition = queryCondition.stream()
                    .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                    .filter(f -> queryDto.getExpCodes().contains(f.getCode()))
                    .collect(Collectors.toList());
            // 查询head
            LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
            for (QueryConditionDto condition : queryCondition) {
                headerData.put(condition.getCode(), condition.getName());
            }
            // 查询数据
            List<JSONObject> excel = assetExcelService.getExcelData(queryDto, queryDto.getExpStandardIds());
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.ASSET.getCode());
            Set<String> keySet = headerData.keySet();
            excel.forEach(json -> keySet.forEach(key -> json.putIfAbsent(key, null)));
            String fileName = "资产-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            settingFeignClient.updateSimplify(new AsCusUserSettingDto()
                    .setAssetExportField(queryDto.getExpCodes()));
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            if (CollUtil.isNotEmpty(excel)) {
                AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(excel.size()))));
            }
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】导出资产入库单")
    @PostMapping("/rk/export")
    public String rkExport(@RequestBody AssetQueryConditionDto queryDto) {
        List<AssetHeadDto> headDtos = queryFieldService.assetHeadView();
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_HEAD);
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        Long standardId = -1L;
        if (generalDto.getStandardId() != null) {
            standardId = generalDto.getStandardId();
        }

        Map<String, String> codeTypeMap = new HashMap<>();
        headDtos = headDtos.stream()
                .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                .peek(f -> codeTypeMap.put(f.getCode(), f.getType()))
                .collect(Collectors.toList());
        // 查询head
        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
        for (AssetHeadDto head : headDtos) {
            if (!QueryFieldConstant.FIELD_CREATE_TIME.equals(head.getCode())) {
                headerData.put(head.getCode(), head.getName());
            }
        }
        headerData.put(QueryFieldConstant.FIELD_CREATE_TIME, "入库日期");
        // 查询数据
        List<JSONObject> excel = assetExcelService.getExcelData(queryDto, ListUtil.of(standardId));
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.ASSET.getCode());
        try {
            // "资产入库单.xlsx"

            int headLen = headerData.size();
            ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(true);
            Sheet sheet = writer.getSheet();
            // 设置边框
            StyleSet styleSet = writer.getStyleSet();
            styleSet.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
            styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.TOP);

            // 设置表头的cellStyle
            CellStyle cellStyle = writer.createCellStyle();
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);

            // 写入文件标题
            writer.merge(0, 0, 0, headLen - 1, "资产入库单", false);
            Cell title = writer.getCell(0, 0);
            CellStyle commentStyle = writer.createCellStyle();
            commentStyle.setAlignment(HorizontalAlignment.CENTER);
            commentStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
            title.setCellStyle(commentStyle);

            // 写入表头
            AtomicInteger rowIdx = new AtomicInteger(1);
            List<String> headCodeList = new ArrayList<>();
            AtomicInteger cellIdx = new AtomicInteger(0);
            Row header = writer.getOrCreateRow(rowIdx.getAndIncrement());
            headerData.forEach((k, v) -> {
                int idx = cellIdx.getAndIncrement();
                headCodeList.add(k);
                Cell cell = header.createCell(idx);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(v);
                // 调整每一列宽度
                sheet.autoSizeColumn((short) idx);
                // 解决自动设置列宽中文失效的问题
                sheet.setColumnWidth(idx, sheet.getColumnWidth(idx) * 17 / 10);
            });
            excel.forEach(f -> {
                int idx = rowIdx.getAndIncrement();
                Row row = writer.getOrCreateRow(idx);
                for (int i = 0; i < headCodeList.size(); i++) {
                    String code = headCodeList.get(i);
                    Cell cell = row.createCell(i);
                    cell.setCellStyle(cellStyle);
                    if (FormFieldCO.NUMBER_INPUT.equals(codeTypeMap.get(code))) {
                        Double num = Convert.toDouble(f.get(code));
                        if (num != null) {
                            cell.setCellValue(num);
                        } else {
                            String numStr = Convert.toStr(f.get(code));
                            if (StrUtil.isNotEmpty(numStr)) {
                                cell.setCellValue(numStr);
                            }
                        }
                    } else {
                        String str = Convert.toStr(f.get(code));
                        if (StrUtil.isNotEmpty(str)) {
                            cell.setCellValue(str);
                        }
                    }
                }
            });

            if (CollUtil.isNotEmpty(excel)) {
                AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS_RK, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(excel.size()))));
            }

            // 写入尾部
            int foot = rowIdx.get();
            writer.merge(foot, foot + 3, 0, headLen - 1, "入库须知（备注）：", false);
            rowIdx.set(foot + 3);
            rowIdx.incrementAndGet();
            int splitNum = headLen / 3;
            writer.merge(rowIdx.get(), rowIdx.get(), 0, splitNum - 1, "采购员（签字）：", false);
            writer.merge(rowIdx.get(), rowIdx.get(), splitNum, splitNum * 2 - 1, "管理员（签字）：", false);
            writer.merge(rowIdx.get(), rowIdx.get(), splitNum * 2, headLen - 1, "验收人员（签字）：", false);
            rowIdx.incrementAndGet();
            writer.merge(rowIdx.get(), rowIdx.get(), 0, splitNum - 1, "入账公司：", false);
            writer.merge(rowIdx.get(), rowIdx.get(), splitNum, headLen - 1, "验收意见：", false);

            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), "资产入库单.xlsx", writer);
        } catch (Exception e) {
            log.error("exportData======", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
