package com.niimbot.asset.customer.adapter.weixin.controller;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionGeneralDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/9/5 14:49
 */
@Slf4j
@Validated
@Api(tags = "【企业微信】耗材库存字段管理")
@ResultController
@RequestMapping("api/weixin/queryField/material/stock")
@RequiredArgsConstructor
public class MaterialStockQueryFieldWeixinController {

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;
    private final MaterialQueryFieldService queryFieldService;

    @ApiOperation(value = "【企业微信】筛选项配置-保存")
    @PostMapping("/query/field")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@RequestBody List<String> conditions) {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_STOCK_QUERY, "weixin");
        if (one != null) {
            JSON conditionJson = one.getConditions();
            QueryConditionGeneralDto generalDto = conditionJson.toJavaObject(QueryConditionGeneralDto.class);
            generalDto.setConditions(conditions);
            one.setConditions(generalDto.toJson());
        } else {
            one = new QueryConditionConfigDto();
            one.setType(QueryFieldConstant.TYPE_MATERIAL_STOCK_QUERY);
            QueryConditionGeneralDto generalDto = new QueryConditionGeneralDto();
            generalDto.setConditions(conditions);
            one.setConditions(generalDto.toJson());
        }
        return queryConditionConfigFeignClient.saveOrUpdate(one);
    }

    @ApiOperation(value = "【企业微信】筛选项配置-查询")
    @GetMapping("/query/field")
    public List<String> getQueryField() {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_STOCK_QUERY, "weixin");
        JSON conditionJson = one.getConditions();
        QueryConditionGeneralDto generalDto = conditionJson.toJavaObject(QueryConditionGeneralDto.class);
        return generalDto.getConditions();
    }

    @ApiOperation(value = "【企业微信】筛选项-渲染")
    @GetMapping("/query/field/view")
    public List<QueryConditionDto> materialQueryView() {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> materialAllQueryField = queryFieldService.stockAllQueryField();
        Map<String, QueryConditionDto> materialAllQueryFieldMap = materialAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_STOCK_QUERY, "weixin");
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (materialAllQueryFieldMap.containsKey(f)) {
                result.add(materialAllQueryFieldMap.get(f));
            }
        });

        return result;
    }

}
