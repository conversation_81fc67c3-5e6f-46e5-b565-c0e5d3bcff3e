package com.niimbot.asset.customer.adapter.weixin.controller;

import com.niimbot.asset.customer.adapter.weixin.service.WxFeignClient;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.MallGoodsFeignClient;
import com.niimbot.asset.weixin.base.dto.WxSaleOrderQueryResult;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.MallGoodsDto;
import com.niimbot.sale.SaleOrderPageQueryDto;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Api(tags = "【企业微信员工】")
@ResultController
@RequiredArgsConstructor
@RequestMapping("/api/weixin")
public class WxSpecialController {

    private final CusEmployeeFeignClient feignClient;

    private final WxFeignClient wxFeignClient;

    private final MallGoodsFeignClient mallGoodsFeignClient;

    @ApiOperation(value = "数据查询分页列表")
    @GetMapping(value = "/emp/page")
    public PageUtils<CusEmployeeDto> page(@Validated CusEmployeeQueryDto dto) {
        CusEmployeeDto adminInfo = feignClient.getAdminInfo();
        PageUtils<CusEmployeeDto> page = feignClient.page(dto);
        page.getList().forEach(cusEmployeeDto -> cusEmployeeDto.setIsAdmin(cusEmployeeDto.getId().equals(adminInfo.getId())));
        return page;
    }

    @ApiOperation(value = "订单记录")
    @PostMapping("/order/pageQuery")
    @AutoConvert
    public PageUtils<WxSaleOrderQueryResult> page(@RequestBody SaleOrderPageQueryDto query) {
        return wxFeignClient.saleOrderPage(query);
    }

    @ApiOperation(value = "硬件商品列表")
    @GetMapping("/sku/hardware")
    public List<MallGoodsDto> hardwareProduct() {
        return mallGoodsFeignClient.hardwareProduct();
    }

    @ApiOperation(value = "硬件商品详情")
    @GetMapping("/sku/detail/{skuCode}")
    public MallGoodsDto hardwareDetail(@PathVariable("skuCode") String skuCode) {
        return mallGoodsFeignClient.hardwareDetail(skuCode);
    }
}
