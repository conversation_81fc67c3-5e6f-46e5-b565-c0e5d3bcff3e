package com.niimbot.asset.customer.adapter.weixin.controller;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.service.feign.CusRegisterFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.RecommendRegisterDto;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/1 16:03
 */
@Slf4j
@Api(tags = "Weixin邀请注册")
@ResultController
@RequestMapping("/api/weixin/recommend")
@RequiredArgsConstructor
public class WeixinRecommendController {

    @Resource
    private CusRegisterFeignClient registerFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private RedisService redisService;

    @ApiOperation(value = "企业注册", httpMethod = "POST")
    @RepeatSubmit
    @PostMapping("/register")
    @ResultMessage("注册成功")
    public Boolean recommendRegister(@RequestBody @Validated(RecommendRegisterDto.OtherChannel.class) RecommendRegisterDto dto) {
        log.info("企业邀请注册");
        NationalCodeValidate.checkCNMobile(dto.getNationalCode(), dto.getMobile());
        if (!smsCodeFeignClient.checkSmsCode(dto.getMobile(), dto.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        //校验区号和手机号一致
        String registerKey = "register_key:" + dto.getMobile();
        if (redisService.hasKey(registerKey)) {
            if (!redisService.get(registerKey).equals(dto.getNationalCode())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "国际区号与手机号不匹配，请检查国际区号！");
            }
        }
        // saas渠道
        dto.setSaasChannel(false);
        registerFeignClient.recommendRegister(dto);
        return true;
    }

}
