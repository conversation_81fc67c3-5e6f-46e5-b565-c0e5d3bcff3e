package com.niimbot.asset.customer.adapter.weixin.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.impl.EmployeeServiceImpl;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/22 14:47
 */
// @Slf4j
// @Service("wxEmployeeExcelServiceImpl")
public class WxEmployeeExcelServiceImpl {

}
