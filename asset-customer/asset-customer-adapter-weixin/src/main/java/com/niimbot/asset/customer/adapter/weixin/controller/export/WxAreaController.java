package com.niimbot.asset.customer.adapter.weixin.controller.export;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AreaService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.AreaFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.means.CategoryExportDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/27 20:20
 */
@Slf4j
@Api(tags = "区域管理")
@ResultController
@RequestMapping("/api/weixin/area")
@Validated
@RequiredArgsConstructor
public class WxAreaController {

    private final AreaFeignClient areaFeignClient;

    private final AreaService areaService;

    private final RedisService redisService;

    private final WxOpenApiService openApiService;

    @Autowired
    private DictConvertUtil dictConvertUtil;

    @ApiOperation(value = "【PC】导出区域模板")
    @GetMapping(value = "/exportTemplate")
    public String exportTemplate() {
        ExcelWriter excelWriter = areaService.buildWriter();
        return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), "区域导入模板.xlsx", excelWriter);
    }

    @ApiOperation(value = "【PC】导入区域模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.AREA, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.AREA, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            areaService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error("import area error:", e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.AREA, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.AREA, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        areaService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.AREA, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.AREA));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation(value = "导出")
    @GetMapping("/export")
    public String areaExport(HttpServletResponse response) {
        try {
            List<AreaDto> list = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(true));
            Map<Long, String> idToName = list.stream().collect(Collectors.toMap(AreaDto::getId, AreaDto::getAreaName));
            Map<Long, String> idToCode = list.stream().collect(Collectors.toMap(AreaDto::getId, AreaDto::getAreaCode));
            List<CategoryExportDto> excel = new ArrayList<>();

            for (AreaDto areaDto : list) {
                Long pid = areaDto.getPid();
                CategoryExportDto categoryExportDto = new CategoryExportDto().setCategoryCode(areaDto.getAreaCode())
                        .setCategoryName(areaDto.getAreaName())
                        .setCategoryPidCode(idToCode.get(pid))
                        .setCategoryPidName(idToName.get(pid))
                        .setAreaDesc(areaDto.getAreaDesc())
                        .setOrgId(areaDto.getOrgId())
                        .setAdmins(areaDto.getAdmins());
                excel.add(categoryExportDto);
            }
            dictConvertUtil.convertToDictionary(excel);
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS_AREA));
            LinkedHashMap<String, String> header = new LinkedHashMap<>();
            header.put("orgName", "所属公司");
            header.put("categoryCode", "区域编码");
            header.put("categoryName", "区域名称");
            header.put("adminsText", "管理员");
            header.put("categoryPidCode", "上级区域编码");
            header.put("categoryPidName", "上级区域名称");
            header.put("areaDesc", "区域描述");
            ExcelWriter writer = WxExcels.export(new ExcelExportDto(header, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), "区域信息-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx", writer);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

}
