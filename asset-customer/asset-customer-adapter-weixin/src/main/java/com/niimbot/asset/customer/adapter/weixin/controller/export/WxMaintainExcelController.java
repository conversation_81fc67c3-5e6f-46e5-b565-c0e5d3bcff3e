package com.niimbot.asset.customer.adapter.weixin.controller.export;

import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.MaintainPlanFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainPlanContentDto;
import com.niimbot.maintenance.MaintainPlanExport;
import com.niimbot.maintenance.MaintainPlanListDto;
import com.niimbot.maintenance.MaintainPlanQueryDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.poi.excel.ExcelWriter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Api(tags = "微信保养导出")
@Validated
@ResultController
@RequestMapping("api/weixin/maintenance/maintain")
@RequiredArgsConstructor
public class WxMaintainExcelController {

    private final MaintainPlanFeignClient maintainPlanFeignClient;

    private final DictConvertUtil dictConvertUtil;

    private final WxOpenApiService openApiService;

    @ApiOperation("保养计划导出")
    @PostMapping("/plan/export")
    public String export(@RequestBody MaintainPlanQueryDto queryDto, HttpServletResponse response) {
        PageUtils<MaintainPlanListDto> page = maintainPlanFeignClient.page(queryDto);
        LinkedHashMap<String, String> head = ExcelUtils.buildExcelHead(MaintainPlanExport.class);
        String filename = "保养计划清单（" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "）";
        if (queryDto.getPageNum() > 1) {
            filename = filename + "-1";
        }
        List<MaintainPlanListDto> list = page.getList();
        dictConvertUtil.convertToDictionary(list);
        List<MaintainPlanExport> exportList = list.stream().map(v -> {
            MaintainPlanExport export = BeanUtil.copyProperties(v, MaintainPlanExport.class);
            if (Convert.toInt(v.getStatus()) == DictConstant.SYS_ENABLE) {
                export.setPlanStatusText("正常");
            }
            if (Convert.toInt(v.getStatus()) == DictConstant.SYS_DISABLE) {
                export.setPlanStatusText("禁用");
            }
            export.setPlanUserText(String.join("，", v.getPrincipalUserId()));
            export.setFrequencyUnit(v.getFrequency() + v.getFrequencyUnit());
            export.setContent(v.getPlanContentList().stream().map(MaintainPlanContentDto::getProject).collect(Collectors.joining(",")));
            return export;
        }).collect(Collectors.toList());
        try {
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(head, exportList));
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS_PLAN));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), filename + ".xlsx", excelWriter);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
