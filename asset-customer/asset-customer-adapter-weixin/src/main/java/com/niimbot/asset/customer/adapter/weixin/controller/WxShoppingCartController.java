package com.niimbot.asset.customer.adapter.weixin.controller;

import com.niimbot.asset.customer.adapter.weixin.service.WxShoppingCartFeignClient;
import com.niimbot.asset.weixin.base.dto.OrderProductRequestDto;
import com.niimbot.asset.weixin.base.dto.OrderRequestDto;
import com.niimbot.asset.weixin.base.dto.ShoppingCartDto;
import com.niimbot.asset.weixin.base.dto.ShoppingCartProductDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/7 上午9:47
 */
@Slf4j
@Api(tags = "购物车")
@ResultController
@RequestMapping("api/weixin/shoppingCart/")
public class WxShoppingCartController {

    @Autowired
    private WxShoppingCartFeignClient shoppingCartFeignClient;

    @ApiOperation(value = "添加商品")
    @PostMapping("addProduct")
    public Boolean addProduct(@RequestBody @Validated OrderRequestDto orderRequestDto) {
        return shoppingCartFeignClient.addProduct(orderRequestDto);
    }

    @ApiOperation(value = "编辑商品")
    @PostMapping("editProduct")
    public Boolean editProduct(@RequestBody @Validated OrderProductRequestDto orderProductRequestDto) {
        return shoppingCartFeignClient.editProduct(orderProductRequestDto);
    }

    @ApiOperation(value = "删除商品")
    @PostMapping("removeProduct")
    public Boolean removeProduct(@RequestBody @Validated OrderProductRequestDto orderProductRequestDto) {
        return shoppingCartFeignClient.removeProduct(orderProductRequestDto);
    }

    @ApiOperation(value = "清空购物车")
    @PostMapping("clear")
    public Boolean clearShoppingCart() {
        return shoppingCartFeignClient.clearShoppingCart();
    }

    @ApiOperation(value = "购物车商品数量")
    @GetMapping("productCount")
    public Integer productCount() {
        return shoppingCartFeignClient.productCount();
    }

    @ApiOperation(value = "购物车清单")
    @GetMapping("list")
    public List<ShoppingCartProductDto> list() {
        return shoppingCartFeignClient.list();
    }

    @ApiOperation(value = "购物车勾选商品数据查询")
    @PostMapping("queryProduct")
    public ShoppingCartDto queryProduct(@RequestBody List<Long> productIdList) {
        return shoppingCartFeignClient.queryProduct(productIdList);
    }

    @ApiOperation(value = "生成订单")
    @PostMapping("settleProduct")
    public ShoppingCartDto settleProduct(@RequestBody @Validated OrderRequestDto orderRequestDto) {
        return shoppingCartFeignClient.settleProduct(orderRequestDto);
    }
}
