package com.niimbot.asset.customer.adapter.weixin.controller.export;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectPlanFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectTaskFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.equipment.EntSntPlan;
import com.niimbot.equipment.ExportEntSntPlan;
import com.niimbot.equipment.SearchEntSntPlan;
import com.niimbot.equipment.SiteInspectTaskDetailExportDto;
import com.niimbot.equipment.SiteInspectTaskPageDto;
import com.niimbot.equipment.SiteInspectTaskPageExportDto;
import com.niimbot.equipment.SiteInspectTaskQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelWriter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "微信设备导入导出")
@ResultController
@RequestMapping("api/weixin/equipment/site/inspect")
@Validated
@RequiredArgsConstructor
public class WxEntSntExcelController {

    private final DictConvertUtil dictConvertUtil;

    private final EquipmentSiteInspectPlanFeignClient planFeignClient;
    private final EquipmentSiteInspectTaskFeignClient taskFeignClient;

    private final WxOpenApiService openApiService;

    @ApiOperation("导出")
    @PostMapping("/plan/export")
    public String exportPlan(@RequestBody SearchEntSntPlan search, HttpServletResponse response) {
        PageUtils<EntSntPlan> page = planFeignClient.searchPlan(search);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> json = page.getList().stream().map(EntSntPlan::translate).collect(Collectors.toList());
        Affirm.notEmpty(json, "列表为空");
        List<ExportEntSntPlan> list = json.stream().map(v -> {
            ExportEntSntPlan exportEntSntPlan = v.toJavaObject(ExportEntSntPlan.class);
            exportEntSntPlan.convert(v);
            return exportEntSntPlan;
        }).collect(Collectors.toList());
        try {
            LinkedHashMap<String, String> excelHead = ExcelUtils.buildExcelHead(ExportEntSntPlan.class);
            // excel 为空就生成一个空文件
            String fileName = "设备巡检计划-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (search.getPageNum() > 1) {
                fileName += "-" + search.getPageNum();
            }
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_ENT_SNT_PLAN, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(list.size()))));
            ExcelWriter writer = WxExcels.export(new ExcelExportDto(excelHead, list));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", writer);
        } catch (Exception e) {
            log.warn("设备巡检计划导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "巡检任务——导出")
    @PostMapping("/task/export")
    public String export(@RequestBody SiteInspectTaskQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SiteInspectTaskPageExportDto.class);
            // 查询数据
            query.setPageNum(1);
            query.setPageSize(Integer.MAX_VALUE);
            PageUtils<SiteInspectTaskPageDto> taskPage = taskFeignClient.page(query);
            List<SiteInspectTaskPageDto> list = taskPage.getList();
            dictConvertUtil.convertToDictionary(list);
            List<SiteInspectTaskPageExportDto> exportList = list.stream().map(f -> BeanUtil.copyProperties(f, SiteInspectTaskPageExportDto.class)).collect(Collectors.toList());
            String fileName = "巡检任务-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            ExcelWriter writer = WxExcels.export(new ExcelExportDto(headerData, exportList));
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_SITE_INSPECT_TASK, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(exportList.size()))));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", writer);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }


    @ApiOperation(value = "巡检任务——导出详情")
    @GetMapping("/task/detail/export")
    public String export(@RequestParam("taskId") Long taskId, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SiteInspectTaskDetailExportDto.class);
            // 查询数据
            List<SiteInspectTaskDetailExportDto> exportList = taskFeignClient.detailExport(taskId);
            dictConvertUtil.convertToDictionary(exportList);
            String name = CollUtil.isNotEmpty(exportList) ? exportList.get(0).getTaskName() : "巡检任务详情";
            String fileName = name + "-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            ExcelWriter writer = WxExcels.export(new ExcelExportDto(headerData, exportList));
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_SITE_INSPECT_TASK_DETAIL, Collections.singletonMap(Auditable.Tpl.CONTENT, name)));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", writer);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
