package com.niimbot.asset.customer.adapter.weixin.controller.export;

import cn.hutool.core.date.DatePattern;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.equipment.EquipmentMaintainPlanFeignClient;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.equipment.EntMatPlan;
import com.niimbot.equipment.EntMatPlanCounter;
import com.niimbot.equipment.EntMatPlanExport;
import com.niimbot.equipment.ExportEntMatPlan;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "微信设备导入导出")
@ResultController
@RequestMapping("api/weixin/equipment/maintain")
@Validated
@RequiredArgsConstructor
public class WxEntMatExcelController {

    private final DictConvertUtil dictConvertUtil;

    private final EquipmentMaintainPlanFeignClient planFeignClient;

    private final WxOpenApiService openApiService;

    @RepeatSubmit
    @ApiOperation("设备保养计划导出")
    @PostMapping("/plan/export")
    public String exportPlan(@RequestBody @Validated EntMatPlanExport export, HttpServletResponse response) {
        PageUtils<EntMatPlan> page = planFeignClient.searchPlan(export);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> json = page.getList().stream().map(EntMatPlan::translate).collect(Collectors.toList());
        Affirm.notEmpty(json, "列表为空");
        List<ExportEntMatPlan> list = json.stream().map(v -> v.toJavaObject(ExportEntMatPlan.class)).collect(Collectors.toList());
        EntMatPlanCounter defaultCounter = new EntMatPlanCounter();
        Map<Long, EntMatPlanCounter> counterMap = planFeignClient.countPlan(list.stream().map(ExportEntMatPlan::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(EntMatPlanCounter::getPlanId, v -> v));
        list.forEach(v -> {
            if (Objects.nonNull(v.getCreateTime())) {
                v.setCreateTimeText(v.getCreateTime().format(DatePattern.NORM_DATETIME_FORMATTER));
            }
            if (Objects.nonNull(v.getTaskBeginTime())) {
                v.setTaskBeginTimeText(v.getTaskBeginTime().format(DatePattern.NORM_DATETIME_FORMATTER));
            }
            if (Objects.nonNull(v.getTaskEndTime())) {
                v.setTaskEndTimeText(v.getTaskEndTime().format(DatePattern.NORM_DATETIME_FORMATTER));
            }
            EntMatPlanCounter counter = counterMap.getOrDefault(v.getId(), defaultCounter);
            v.setEntCount(counter.getEntCount());
            v.setSrPrCount(counter.getSrPrCount());
        });
        try {
            LinkedHashMap<String, String> excelHead = ExcelUtils.buildExcelHead(ExportEntMatPlan.class);
            // excel 为空就生成一个空文件
            String fileName = "设备保养计划-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (export.getPageNum() > 1) {
                fileName += "-" + export.getPageNum();
            }
            ExcelWriter writer = WxExcels.export(new ExcelExportDto(excelHead, list));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", writer);
        } catch (Exception e) {
            log.warn("设备保养计划导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}

