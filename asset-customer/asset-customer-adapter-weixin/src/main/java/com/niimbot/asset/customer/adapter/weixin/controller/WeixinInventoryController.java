package com.niimbot.asset.customer.adapter.weixin.controller;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.OnlineInventoryFeignClient;
import com.niimbot.ding.DingInventoryQueryDto;
import com.niimbot.ding.DingInventoryTaskListDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "企微盘点管理")
@ResultController
@RequestMapping("api/weixin/inventory")
@RequiredArgsConstructor
public class WeixinInventoryController {

    // private final WeixinInventoryFeignClient weixinInventoryFeignClient;
    private final OnlineInventoryFeignClient onlineInventoryFeignClient;

    @ApiOperation(value = "企微盘点任务分页列表")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<DingInventoryTaskListDto> weixinTaskPage(DingInventoryQueryDto queryDto) {
        return onlineInventoryFeignClient.onlineTaskPage(queryDto);
        // return weixinInventoryFeignClient.weixinTaskPage(queryDto);
    }
}
