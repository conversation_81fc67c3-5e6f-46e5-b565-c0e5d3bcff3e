package com.niimbot.asset.customer.adapter.weixin.controller.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.MaterialExcelService;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialQueryDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "微信耗材导入导出")
@ResultController
@RequestMapping("api/weixin/material")
@Validated
@RequiredArgsConstructor
public class WxMaterialExcelController {

    private final MaterialQueryFieldService queryFieldService;

    private final MaterialExcelService materialExcelService;

    private final CusUserSettingFeignClient settingFeignClient;

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    private final WxOpenApiService openApiService;

    private final DesensitizationDataUtil desensitizationDataUtil;

    @ApiOperation(value = "导出")
    @PostMapping("/export")
    public String export(@RequestBody MaterialQueryDto queryDto, HttpServletResponse response) {
        try {
            List<QueryConditionDto> queryCondition = queryFieldService.materialAllHeadField();
            List<QueryConditionDto> conditions = queryDto.getConditions();
            Long standardId = -1L;
            if (CollUtil.isNotEmpty(conditions)) {
                Optional<QueryConditionDto> standard = conditions.stream()
                        .filter(f -> f.getType().equals(QueryFieldConstant.MATERIAL_FILED_STANDARD))
                        .findFirst();
                if (standard.isPresent()) {
                    QueryConditionDto conditionDto = standard.get();
                    standardId = Convert.toLong(conditionDto.getQueryData(), -1L);
                    QueryConditionStandardDto standardDto = queryFieldService.standardAllField(standardId, false, true);
                    queryCondition.addAll(standardDto.getConditions());
                }
            }
            queryCondition = queryCondition.stream()
                    .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                    .filter(f -> queryDto.getExpCodes().contains(f.getCode()))
                    .collect(Collectors.toList());
            // 查询head
            LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
            for (QueryConditionDto condition : queryCondition) {
                headerData.put(condition.getCode(), condition.getName());
            }
            // 查询数据
            List<JSONObject> excel = materialExcelService.getExcelData(queryDto, standardId);
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.MATERIAL.getCode());
            Set<String> keySet = headerData.keySet();
            excel.forEach(json -> keySet.forEach(key -> json.putIfAbsent(key, null)));
            String fileName = "耗材档案-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MRL));
            settingFeignClient.updateSimplify(new AsCusUserSettingDto()
                    .setMaterialExportField(queryDto.getExpCodes()));
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "导出")
    @PostMapping("/stock/export")
    public String export(@RequestBody @Validated MaterialStockQueryDto queryDto, HttpServletResponse response) {
        try {
            List<AssetHeadDto> headDtos = queryFieldService.materialHeadView();
            // 查询配置项
            QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_HEAD);
            QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
            Long standardId = -1L;
            if (generalDto.getStandardId() != null) {
                standardId = generalDto.getStandardId();
            }
            headDtos = headDtos.stream()
                    .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                    .collect(Collectors.toList());
            // 查询head
            LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
            for (AssetHeadDto head : headDtos) {
                headerData.put(head.getCode(), head.getName());
            }

            headerData.put("avgPrice", "价值（加权平均）");
            headerData.put("currentQuantity", "当前库存");
            headerData.put("totalMoney", "库存总金额（元）");
            headerData.put("status", "库存预警状态");
            // 查询数据
            List<JSONObject> excel = materialExcelService.getStockExcelData(queryDto, standardId);
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.MATERIAL.getCode());

            String fileName = "实时库存-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MRL_STOCK));
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
