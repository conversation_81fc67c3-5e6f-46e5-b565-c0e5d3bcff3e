package com.niimbot.asset.customer.adapter.weixin.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import com.niimbot.asset.customer.adapter.weixin.mq.WxCallbackProducer;
import com.niimbot.asset.weixin.base.constant.WxMqConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.constant.WxCpTpConsts;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/4 10:56
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WxCallbackContext {

    private static List<String> SALE_ORDER_EVENT = ListUtil.of(
            WxCpTpConsts.InfoType.OPEN_ORDER,
            WxCpTpConsts.InfoType.CHANGE_ORDER,
            WxCpTpConsts.InfoType.PAY_FOR_APP_SUCCESS,
            WxCpTpConsts.InfoType.LICENSE_PAY_SUCCESS,
            WxCpTpConsts.InfoType.LICENSE_REFUND,
            WxCpTpConsts.InfoType.UNLICENSED_NOTIFY);

    private static List<String> CORP_EVENT = ListUtil.of(
            WxCpTpConsts.InfoType.SUITE_TICKET,
            WxCpTpConsts.InfoType.CREATE_AUTH,
            WxCpTpConsts.InfoType.CANCEL_AUTH,
            WxCpTpConsts.InfoType.CHANGE_AUTH);

    private static List<String> CORP_STRUCTURE_EVENT = ListUtil.of(
            WxCpTpConsts.InfoType.CHANGE_CONTACT, WxCpTpConsts.InfoType.CHANGE_EDITION);

    private final WxFeignClient wxFeignClient;
    private final WxCpTpService wxCpTpService;
    private final WxCpTpService wxOauthService;
    private final WxCallbackProducer wxCallbackProducer;

    public void execute(WxCpTpXmlMessage message) {
        log.info("接收到的企业微信回调消息 : {}", JSONObject.toJSONString(message));
        if (StrUtil.isNotEmpty(message.getInfoType())) {
            try {
                SendResult sendResult = null;
                if (SALE_ORDER_EVENT.contains(message.getInfoType())) {
                    sendResult = sendSaleOrderMessage(message);
                } else if (CORP_EVENT.contains(message.getInfoType())) {
                    sendResult = sendCorpMessage(message);
                } else if (CORP_STRUCTURE_EVENT.contains(message.getInfoType())) {
                    sendResult = sendCorpStructureMessage(message);
                }
                if (sendResult != null) {
                    log.info("Send {} Callback Success By RockerMq : Message[msgId:{}]", message.getInfoType(), sendResult.getMessageId());
                }
            } catch (Exception e) {
                log.error("Send {} Callback Error By RockerMq : Details[{}]", message.getInfoType(), JSONObject.toJSONString(message), e);
            }
            // 写入原始数据记录
            try {
                wxFeignClient.saveCallback(message);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    private SendResult sendSaleOrderMessage(WxCpTpXmlMessage message) {
        String key = message.getInfoType() + "_" + message.getOrderId();
        if (WxCpTpConsts.InfoType.CHANGE_ORDER.equals(message.getInfoType())) {
            key = message.getInfoType() + "_" + message.getOldOrderId() + "_" + message.getNewOrderId();
        }
        return wxCallbackProducer.sendMsg(WxMqConstant.ASSET_WEIXIN_SALEORDER_TAG,
                key, message);
    }

    private SendResult sendCorpMessage(WxCpTpXmlMessage message) {
        if (WxCpTpConsts.InfoType.SUITE_TICKET.equals(message.getInfoType())) {
            if (message.getSuiteId().equals(wxCpTpService.getWxCpTpConfigStorage().getSuiteId())) {
                wxCpTpService.setSuiteTicket(message.getSuiteTicket());
            } else if (message.getSuiteId().equals(wxOauthService.getWxCpTpConfigStorage().getSuiteId())) {
                wxOauthService.setSuiteTicket(message.getSuiteTicket());
            }
            return null;
        } else {
            String key = message.getInfoType();
            return wxCallbackProducer.sendMsg(WxMqConstant.ASSET_WEIXIN_CORP_TAG,
                    key, message);
        }
    }

    private SendResult sendCorpStructureMessage(WxCpTpXmlMessage message) {
        String key = message.getInfoType() + "_" + message.getOrderId();
        return wxCallbackProducer.sendMsg(WxMqConstant.ASSET_WEIXIN_CORP_STRUCTURE_TAG,
                key, message);
    }

}
