package com.niimbot.asset.customer.adapter.weixin.service.impl;

/**
 * <AUTHOR>
 * @date 2023/7/22 13:37
 */
// @Slf4j
// @Service("wxMaterialRepositoryServiceImpl")
// public class WxMaterialRepositoryServiceImpl extends MaterialRepositoryServiceImpl {
//
//     private LinkedHashMap<String, String> tableHeader;
//
//     {
//         tableHeader = new LinkedHashMap<>();
//         tableHeader.putAll(ImmutableMap.<String, String>builder()
//                 .put("所属公司", "managerOwnerText")
//                 .put("所属公司名称", "managerOwnerIgnore")
//                 .put("仓库编码", "code")
//                 .put("仓库名称", "name")
//                 .put("管理员", "adminNames")
//                 .put("管理员名称", "adminNameIgnore")
//                 .put("备注信息", "remark")
//                 .build());
//     }
//
//     private final RedisService redisService;
//     private final MaterialRepositoryFeignClient repositoryFeignClient;
//     private final OrgFeignClient orgFeignClient;
//     private final CusEmployeeFeignClient employeeFeignClient;
//     private final ImportService importService;
//     private final ImportTaskFeignClient importTaskFeignClient;
//
//     public WxMaterialRepositoryServiceImpl(RedisService redisService,
//                                            MaterialRepositoryFeignClient repositoryFeignClient,
//                                            OrgFeignClient orgFeignClient,
//                                            CusEmployeeFeignClient employeeFeignClient,
//                                            ImportService importService,
//                                            ImportTaskFeignClient importTaskFeignClient) {
//         super(redisService, repositoryFeignClient, orgFeignClient, employeeFeignClient, importService, importTaskFeignClient);
//         this.redisService = redisService;
//         this.repositoryFeignClient = repositoryFeignClient;
//         this.orgFeignClient = orgFeignClient;
//         this.employeeFeignClient = employeeFeignClient;
//         this.importService = importService;
//         this.importTaskFeignClient = importTaskFeignClient;
//     }
//
//     @Override
//     public void exportTemplate(HttpServletResponse response) {
//         ClassPathResource templateSource = new ClassPathResource("/excelTemplate/weixin_material_repository_template.xlsx");
//         try (OutputStream out = response.getOutputStream();
//              InputStream inputStream = templateSource.getInputStream()) {
//             String fileName = "耗材仓库导入模板.xlsx";
//             response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
//             response.setHeader("Content-Disposition", "attachment; filename="
//                     + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
//             response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
//             response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
//             ExcelReader reader = ExcelUtil.getReader(inputStream);
//             ExcelWriter writer = reader.getWriter();
//             writer.flush(out, true);
//             writer.close();
//             IoUtil.close(out);
//         } catch (Exception e) {
//             throw new BusinessException(SystemResultCode.EXPORT_ERROR);
//         }
//
//     }
//
//     @Override
//     public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
//         ExcelReader reader = ExcelUtil.getReader(stream)
//                 .setHeaderAlias(tableHeader);
//         // 校验表头
//         List<List<Object>> read = reader.read(1, 1);
//         if (read.size() > 0) {
//             List<Object> header = ExcelUtils.clearEmpty(read.get(0));
//             if (header.size() != tableHeader.size()) {
//                 throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//             }
//             header.forEach(it -> {
//                 if (!tableHeader.containsValue(Convert.toStr(it))) {
//                     throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//                 }
//             });
//         } else {
//             throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//         }
//         // 读取数据并校验
//         List<WxMaterialRepositoryImportDto> repositoryList = reader.read(1, 1, WxMaterialRepositoryImportDto.class);
//         this.importExcel(null, repositoryList, fileName, fileSize, companyId, true);
//     }
//
//     @Override
//     public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
//         List<Object> header = sheetModels.get(0);
//         // 去除表头尾部空列
//         int lastIndex = header.size();
//         for (int i = header.size() - 1; i >= 0; i--) {
//             if (StrUtil.isBlank(Convert.toStr(header.get(i)))) {
//                 lastIndex = i;
//             } else {
//                 break;
//             }
//         }
//         header = header.subList(0, lastIndex);
//         List<WxMaterialRepositoryImportDto> repositoryList = new ArrayList<>();
//         for (int i = 1; i < sheetModels.size(); i++) {
//             List<Object> data = sheetModels.get(i);
//             WxMaterialRepositoryImportDto materialRepositoryImportDto = new WxMaterialRepositoryImportDto();
//             for (int j = 0; j < header.size(); j++) {
//                 String head = tableHeader.get(Convert.toStr(header.get(j)));
//                 if (StrUtil.isBlank(head)) {
//                     throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//                 } else {
//                     if (j < data.size()) {
//                         ReflectUtil.setFieldValue(materialRepositoryImportDto, head, data.get(j));
//                     }
//                 }
//             }
//             repositoryList.add(materialRepositoryImportDto);
//         }
//
//         this.importExcel(taskId, repositoryList, "耗材分类在线编辑保存", 0L, companyId, false);
//         return true;
//     }
//
//     private void importExcel(Long taskId,
//                              List<WxMaterialRepositoryImportDto> read,
//                              String fileName,
//                              Long fileSize,
//                              Long companyId,
//                              boolean async) {
//         // 判断是否超过最大上传条数，一次限制1000
//         if (read.size() > MAX_BATCH) {
//             throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "耗材仓库", Convert.toStr(MAX_BATCH));
//         }
//         // 删除历史导入信息
//         if (taskId != null) {
//             repositoryFeignClient.importErrorDeleteAll(taskId);
//         }
//         GlobalCache global = new GlobalCache().setCompany(companyId);
//         globalCache.set(global);
//         if (async) {
//             // 异步启动
//             new Thread(() -> {
//                 ImportDto importCache = new ImportDto();
//                 importCache.setFileName(fileName);
//                 importCache.setImportType(DictConstant.IMPORT_TYPE_MATERIAL_REPOSITORY);
//                 importCache.setFileSize(fileSize);
//                 importCache.setCount(read.size());
//                 importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
//                 startImport(taskId, read, importCache);
//             }).start();
//         } else {
//             // 同步启动
//             ImportDto importCache = new ImportDto();
//             importCache.setFileName(fileName);
//             importCache.setImportType(DictConstant.IMPORT_TYPE_MATERIAL_REPOSITORY);
//             importCache.setFileSize(fileSize);
//             importCache.setCount(read.size());
//             importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
//             startImport(taskId, read, importCache);
//         }
//     }
//
//     private void startImport(Long taskId, List<WxMaterialRepositoryImportDto> read, ImportDto importCache) {
//         // 导入任务
//         ImportTaskDto importTaskDto = null;
//         if (taskId != null) {
//             importTaskDto = importTaskFeignClient.queryById(taskId);
//         }
//         try {
//             // 设置锁
//             redisService.hSetAll(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany()),
//                     (JSONObject) JSON.toJSON(importCache));
//             // 创建导入任务
//             if (importTaskDto == null) {
//                 importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
//                 Long id = importTaskFeignClient.save(importTaskDto);
//                 importTaskDto.setId(id);
//             }
//
//             // 写入表头数据
//             this.saveLuckySheetHead(importTaskDto.getId());
//             // 记录一行错误信息，生成LuckySheet格式数据
//             AtomicInteger successNum = new AtomicInteger(0);
//             importService.sendMaterialRepository(globalCache.get().getCompany());
//             List<OrgDto> orgs = orgFeignClient.areaPermsList();
//             Map<String, List<Long>> orgMap = new HashMap<>();
//             orgs.forEach(o -> {
//                 String orgCode = o.getOrgCode();
//                 if (StrUtil.isNotEmpty(orgCode)) {
//                     List<Long> orgCodes = orgMap.getOrDefault(orgCode, new ArrayList<>());
//                     orgCodes.add(o.getId());
//                     orgMap.put(orgCode, orgCodes);
//                 }
//
//                 String orgName = o.getOrgName();
//                 List<Long> orgIds = orgMap.getOrDefault(orgName, new ArrayList<>());
//                 orgIds.add(o.getId());
//                 orgMap.put(orgName, orgIds);
//
//                 String orgNameAndCode = o.getOrgName() + "（" + o.getOrgCode() + "）";
//                 List<Long> orgCodeIds = orgMap.getOrDefault(orgNameAndCode, new ArrayList<>());
//                 orgCodeIds.add(o.getId());
//                 orgMap.put(orgNameAndCode, orgCodeIds);
//             });
//
//             List<CusEmployeeDto> empAll = employeeFeignClient.list(new CusEmployeeQueryDto().setStatus(1));
//             Map<String, List<Long>> empMap = new HashMap<>();
//             empAll.forEach(e -> {
//                 String empNo = e.getEmpNo();
//                 if (StrUtil.isNotEmpty(empNo)) {
//                     List<Long> empNos = empMap.getOrDefault(empNo, new ArrayList<>());
//                     empNos.add(e.getId());
//                     empMap.put(empNo, empNos);
//                 }
//
//                 String empName = e.getEmpName();
//                 List<Long> empIds = empMap.getOrDefault(empName, new ArrayList<>());
//                 empIds.add(e.getId());
//                 empMap.put(empName, empIds);
//
//                 String empNameAndCode = e.getEmpName() + "（" + e.getEmpNo() + "）";
//                 List<Long> empCodeIds = empMap.getOrDefault(empNameAndCode, new ArrayList<>());
//                 empCodeIds.add(e.getId());
//                 empMap.put(empNameAndCode, empCodeIds);
//             });
//
//             // 循环处理行数据
//             ImportTaskDto finalImportTaskDto = importTaskDto;
//             IntStream.range(0, read.size()).forEach(idx -> {
//                 WxMaterialRepositoryImportDto importDto = read.get(idx);
//                 importDto.setTaskId(finalImportTaskDto.getId());
//                 importDto.setErrorNum(0);
//                 List<LuckySheetModel> luckySheetModelRow = new ArrayList<>();
//                 // 调用validator
//                 Set<ConstraintViolation<WxMaterialRepositoryImportDto>> validate = VALIDATOR.validate(importDto);
//                 Map<String, String> validateData = validate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));
//
//                 // 所属公司
//                 LuckySheetModel managerOwner = validateTransField(idx + 1 - successNum.get(), 0, importDto.getManagerOwnerText(), importDto, "managerOwnerText", validateData, orgMap, null);
//                 luckySheetModelRow.add(managerOwner);
//                 LuckySheetModel managerOwnerIgnore = validateField(idx + 1 - successNum.get(), 1, importDto.getManagerOwnerIgnore(), importDto, "", validateData);
//                 luckySheetModelRow.add(managerOwnerIgnore);
//
//                 // 仓库编码
//                 LuckySheetModel code = validateField(idx + 1 - successNum.get(), 2, importDto.getCode(), importDto, "code", validateData);
//                 luckySheetModelRow.add(code);
//
//                 // 仓库名称
//                 LuckySheetModel name = validateField(idx + 1 - successNum.get(), 3, importDto.getName(), importDto, "name", validateData);
//                 luckySheetModelRow.add(name);
//
//                 // 所属管理员
//                 LuckySheetModel admins = validateTransField(idx + 1 - successNum.get(), 4, importDto.getAdminNames(), importDto, "admins", validateData, null, empMap);
//                 luckySheetModelRow.add(admins);
//                 LuckySheetModel adminsIgnore = validateField(idx + 1 - successNum.get(), 5, importDto.getAdminNameIgnore(), importDto, "", validateData);
//                 luckySheetModelRow.add(adminsIgnore);
//                 // 备注信息
//                 LuckySheetModel remark = validateField(idx + 1 - successNum.get(), 6, importDto.getRemark(), importDto, "remark", validateData);
//                 luckySheetModelRow.add(remark);
//
//                 importDto.setSheetModelList(luckySheetModelRow);
//
//                 Boolean success = repositoryFeignClient.saveSheetData(importDto);
//                 if (BooleanUtil.isTrue(success)) {
//                     successNum.getAndIncrement();
//                 }
//                 importService.sendMaterialRepository(globalCache.get().getCompany());
//             });
//             AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MRL_REP, new AuditableImportResult(successNum.get())));
//         } catch (Exception e) {
//             log.error(e.getMessage(), e);
//         } finally {
//             redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany()), "notice", true);
//             redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany()), "finish", true);
//             importService.sendMaterialRepository(globalCache.get().getCompany());
//             // 更新任务状态
//             if (importTaskDto != null && importTaskDto.getId() != null) {
//                 String key = RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany());
//                 Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
//                 JSONObject json = new JSONObject(map);
//                 ImportDto importDto = json.toJavaObject(ImportDto.class);
//                 importTaskDto.setTaskImportStatus(importDto);
//                 importTaskFeignClient.update(importTaskDto);
//             }
//             this.clearThreadLocal();
//         }
//     }
//
//     private void saveLuckySheetHead(Long taskId) {
//         HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
//         List<LuckySheetModel> headModelList = new ArrayList<>();
//         AtomicInteger cellIndex = new AtomicInteger(0);
//         List<String> must = ListUtil.of("managerOwnerText", "code", "name");
//         tableHeader.forEach((k, v) -> {
//             LuckySheetModel luckySheetModel = new LuckySheetModel();
//             luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
//             LuckySheetModel.Value modelV = luckySheetModel.getV();
//             modelV.setV(k);
//             if (must.contains(v)) {
//                 modelV.setFc("#ff0000");
//             }
//             headModelList.add(luckySheetModel);
//         });
//         importErrorDto.setHeadModelList(headModelList);
//         this.repositoryFeignClient.saveSheetHead(importErrorDto);
//
//     }
//
// }
