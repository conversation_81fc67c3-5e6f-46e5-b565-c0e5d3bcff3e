package com.niimbot.asset.customer.adapter.weixin.service;

import com.niimbot.asset.weixin.base.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/7 上午9:32
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface WxShoppingCartFeignClient {

    /**
     * 购物车添加商品
     *
     * @param orderRequestDto
     * @return
     */
    @PostMapping("server/weixin/shoppingCart/addProduct")
    Boolean addProduct(@RequestBody OrderRequestDto orderRequestDto);

    /**
     * 购物车编辑商品
     *
     * @param orderProductRequestDto
     * @return
     */
    @PostMapping("server/weixin/shoppingCart/editProduct")
    Boolean editProduct(@RequestBody OrderProductRequestDto orderProductRequestDto);

    /**
     * 购物车删除商品
     *
     * @param orderProductRequestDto
     * @return
     */
    @PostMapping("server/weixin/shoppingCart/removeProduct")
    Boolean removeProduct(@RequestBody OrderProductRequestDto orderProductRequestDto);

    /**
     * 清空购物车
     *
     * @return
     */
    @PostMapping("server/weixin/shoppingCart/clear")
    Boolean clearShoppingCart();

    /**
     * 购物车商品数量
     *
     * @return
     */
    @GetMapping("server/weixin/shoppingCart/productCount")
    Integer productCount();

    /**
     * 购物车商品列表
     *
     * @return
     */
    @GetMapping("server/weixin/shoppingCart/list")
    List<ShoppingCartProductDto> list();

    /**
     * 购物车商品列表，带id列表过滤
     *
     * @return
     */
    @PostMapping("server/weixin/shoppingCart/queryProduct")
    ShoppingCartDto queryProduct(@RequestBody List<Long> productIdList);

    /**
     * 商品结算
     *
     * @param orderRequestDto
     * @return
     */
    @PostMapping("server/weixin/shoppingCart/settleProduct")
    ShoppingCartDto settleProduct(@RequestBody OrderRequestDto orderRequestDto);
}
