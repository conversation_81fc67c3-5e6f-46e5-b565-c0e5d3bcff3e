package com.niimbot.asset.customer.adapter.weixin.utils;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.model.ExcelExportDto;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class WxExcels {

    public static ExcelWriter export(ExcelExportDto excelExportDto) {
        ExcelWriter writer = ExcelUtil.getBigWriter(5000);
        writer.writeHeadRow(excelExportDto.getHeaderData().values());
        List<String> codes = new ArrayList<>(excelExportDto.getHeaderData().keySet());
        List<?> rows = excelExportDto.getRows();
        for (Object row : rows) {
            JSONObject json = (JSONObject) JSONObject.toJSON(row);
            List<Object> rowData = new ArrayList<>();
            for (String code : codes) {
                rowData.add(json.get(code));
            }
            writer.writeRow(rowData);
        }
        return writer;
    }

}
