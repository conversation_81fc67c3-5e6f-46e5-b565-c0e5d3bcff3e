package com.niimbot.asset.customer.adapter.weixin.service.impl;

/**
 * <AUTHOR>
 * @date 2023/7/27 20:21
 */
// @Slf4j
// @Service("wxAreaExcelServiceImpl")
// public class WxAreaExcelServiceImpl extends AreaServiceImpl {
//
//     private LinkedHashMap<String, String> tableHeader;
//
//     {
//         tableHeader = new LinkedHashMap<>();
//         tableHeader.putAll(ImmutableMap.<String, String>builder()
//                 .put("所属公司编码", "orgName")
//                 .put("所属公司名称", "orgNameIgnore")
//                 .put("区域编码", "areaCode")
//                 .put("区域名称", "areaName")
//                 .put("上级区域编码", "pidCode")
//                 .put("上级区域名称", "pidName")
//                 .put("区域描述", "areaDesc")
//                 .build());
//     }
//
//     private final RedisService redisService;
//     private final AreaFeignClient areaFeignClient;
//     private final OrgFeignClient orgFeignClient;
//     private final ImportService importService;
//     private final ImportTaskFeignClient importTaskFeignClient;
//
//     public WxAreaExcelServiceImpl(RedisService redisService,
//                                   AreaFeignClient areaFeignClient,
//                                   OrgFeignClient orgFeignClient,
//                                   ImportService importService,
//                                   ImportTaskFeignClient importTaskFeignClient) {
//         super(redisService, areaFeignClient, orgFeignClient, importService, importTaskFeignClient);
//         this.redisService = redisService;
//         this.areaFeignClient = areaFeignClient;
//         this.orgFeignClient = orgFeignClient;
//         this.importService = importService;
//         this.importTaskFeignClient = importTaskFeignClient;
//     }
//
//     @Override
//     public void exportTemplate(HttpServletResponse response) {
//         ClassPathResource templateSource = new ClassPathResource("/excelTemplate/weixin_area_template.xlsx");
//         try (OutputStream out = response.getOutputStream();
//              InputStream inputStream = templateSource.getInputStream()) {
//             String fileName = "区域导入模板.xlsx";
//             response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
//             response.setHeader("Content-Disposition", "attachment; filename="
//                     + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
//             response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
//             response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
//             ExcelReader reader = ExcelUtil.getReader(inputStream);
//             ExcelWriter writer = reader.getWriter();
//             writer.setSheet("区域");
//             List<AreaDto> areaAll = areaFeignClient.areaList(new AreaQueryDto());
//             LinkedHashMap<String, String> areaHead = ExcelUtils.buildExcelHead(AreaExport.class);
//             areaHead.forEach(writer::addHeaderAlias);
//             List<AreaExport> areaData = areaAll.stream().map(area ->
//                     new AreaExport(area.getAreaCode(), area.getAreaName()))
//                     .collect(Collectors.toList());
//             writer.setOnlyAlias(true);
//             writer.write(areaData);
//             writer.autoSizeColumnAll();
//
//             writer.setSheet("所属公司");
//             List<OrgDto> orgs = orgFeignClient.areaPermsList();
//             LinkedHashMap<String, String> orgHead = ExcelUtils.buildExcelHead(OrgExport.class);
//             orgHead.forEach(writer::addHeaderAlias);
//             List<OrgExport> orgData = orgs.stream().map(org ->
//                     new OrgExport(org.getOrgName()))
//                     .collect(Collectors.toList());
//             writer.setOnlyAlias(true);
//             writer.write(orgData);
//
//             writer.autoSizeColumnAll();
//             writer.flush(out, true);
//             writer.close();
//             IoUtil.close(out);
//         } catch (Exception e) {
//             throw new BusinessException(SystemResultCode.EXPORT_ERROR);
//         }
//     }
//
//     @Override
//     public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
//         ExcelReader reader = ExcelUtil.getReader(stream)
//                 .setHeaderAlias(tableHeader);
//         // 校验表头
//         List<List<Object>> read = reader.read(1, 1);
//         if (read.size() > 0) {
//             List<Object> header = ExcelUtils.clearEmpty(read.get(0));
//             if (header.size() != tableHeader.size()) {
//                 throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//             }
//             header.forEach(it -> {
//                 if (!tableHeader.containsValue(Convert.toStr(it))) {
//                     throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//                 }
//             });
//         } else {
//             throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//         }
//         // 读取数据并校验
//         List<WxAreaImportDto> areaList = reader.read(1, 1, WxAreaImportDto.class);
//         this.importExcel(null, areaList, fileName, fileSize, companyId, true);
//     }
//
//
//     private void importExcel(Long taskId,
//                              List<WxAreaImportDto> read,
//                              String fileName,
//                              Long fileSize,
//                              Long companyId,
//                              boolean async) {
//         // 判断是否超过最大上传条数，一次限制1000
//         if (read.size() > MAX_BATCH) {
//             throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "区域", Convert.toStr(MAX_BATCH));
//         }
//         // 删除历史导入信息
//         if (taskId != null) {
//             areaFeignClient.importErrorDeleteAll(taskId);
//         }
//         GlobalCache global = new GlobalCache().setCompany(companyId);
//         globalCache.set(global);
//         if (async) {
//             // 异步启动
//             new Thread(() -> {
//                 ImportDto importCache = new ImportDto();
//                 importCache.setFileName(fileName);
//                 importCache.setImportType(DictConstant.IMPORT_TYPE_AREA);
//                 importCache.setFileSize(fileSize);
//                 importCache.setCount(read.size());
//                 importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
//                 startImport(taskId, read, importCache);
//             }).start();
//         } else {
//             // 同步启动
//             ImportDto importCache = new ImportDto();
//             importCache.setFileName(fileName);
//             importCache.setImportType(DictConstant.IMPORT_TYPE_AREA);
//             importCache.setFileSize(fileSize);
//             importCache.setCount(read.size());
//             importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
//             startImport(taskId, read, importCache);
//         }
//     }
//
//     @Override
//     public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
//         List<Object> header = sheetModels.get(0);
//         // 去除表头尾部空列
//         int lastIndex = header.size();
//         for (int i = header.size() - 1; i >= 0; i--) {
//             if (StrUtil.isBlank(Convert.toStr(header.get(i)))) {
//                 lastIndex = i;
//             } else {
//                 break;
//             }
//         }
//         header = header.subList(0, lastIndex);
//         List<WxAreaImportDto> areaList = new ArrayList<>();
//         for (int i = 1; i < sheetModels.size(); i++) {
//             List<Object> data = sheetModels.get(i);
//             WxAreaImportDto categoryImportDto = new WxAreaImportDto();
//             for (int j = 0; j < header.size(); j++) {
//                 String head = tableHeader.get(Convert.toStr(header.get(j)));
//                 if (StrUtil.isBlank(head)) {
//                     throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
//                 } else {
//                     if (j < data.size()) {
//                         ReflectUtil.setFieldValue(categoryImportDto, head, data.get(j));
//                     }
//                 }
//             }
//             areaList.add(categoryImportDto);
//         }
//
//         this.importExcel(taskId, areaList, "区域在线编辑保存", 0L, companyId, false);
//         return true;
//     }
//
//     private void startImport(Long taskId, List<WxAreaImportDto> read, ImportDto importCache) {
//         // 导入任务
//         ImportTaskDto importTaskDto = null;
//         if (taskId != null) {
//             importTaskDto = importTaskFeignClient.queryById(taskId);
//         }
//         try {
//             // 设置锁
//             redisService.hSetAll(RedisConstant.companyImportKey(ImportService.AREA, globalCache.get().getCompany()),
//                     (JSONObject) JSON.toJSON(importCache));
//             // 创建导入任务
//             if (importTaskDto == null) {
//                 importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
//                 Long id = importTaskFeignClient.save(importTaskDto);
//                 importTaskDto.setId(id);
//             }
//
//             // 写入表头数据
//             this.saveLuckySheetHead(importTaskDto.getId());
//             // 记录一行错误信息，生成LuckySheet格式数据
//             AtomicInteger successNum = new AtomicInteger(0);
//             importService.sendArea(globalCache.get().getCompany());
//             // 循环处理行数据
//             List<OrgDto> orgs = orgFeignClient.areaPermsList();
//             Map<String, List<Long>> orgMap = new HashMap<>();
//             orgs.forEach(o -> {
//                 String orgCode = o.getOrgCode();
//                 if (StrUtil.isNotEmpty(orgCode)) {
//                     List<Long> orgCodes = orgMap.getOrDefault(orgCode, new ArrayList<>());
//                     orgCodes.add(o.getId());
//                     orgMap.put(orgCode, orgCodes);
//                 }
//
//                 String orgName = o.getOrgName();
//                 List<Long> orgIds = orgMap.getOrDefault(orgName, new ArrayList<>());
//                 orgIds.add(o.getId());
//                 orgMap.put(orgName, orgIds);
//
//                 String orgNameAndCode = o.getOrgName() + "（" + o.getOrgCode() + "）";
//                 List<Long> orgCodeIds = orgMap.getOrDefault(orgNameAndCode, new ArrayList<>());
//                 orgCodeIds.add(o.getId());
//                 orgMap.put(orgNameAndCode, orgCodeIds);
//             });
//             ImportTaskDto finalImportTaskDto = importTaskDto;
//             IntStream.range(0, read.size()).forEach(idx -> {
//                 WxAreaImportDto importDto = read.get(idx);
//                 importDto.setTaskId(finalImportTaskDto.getId());
//                 importDto.setErrorNum(0);
//                 List<LuckySheetModel> luckySheetModelRow = new ArrayList<>();
//                 // 调用validator
//                 Set<ConstraintViolation<AreaImportDto>> validate = VALIDATOR.validate(importDto);
//                 Map<String, String> validateData = validate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));
//
//                 // 所属公司
//                 LuckySheetModel orgName = validateOrgField(idx + 1 - successNum.get(), 0, importDto.getOrgName(), importDto, "orgName", validateData, orgMap);
//                 luckySheetModelRow.add(orgName);
//                 LuckySheetModel orgNameIgnore = validateField(idx + 1 - successNum.get(), 1, importDto.getOrgNameIgnore(), importDto, "", validateData);
//                 luckySheetModelRow.add(orgNameIgnore);
//                 // 区域编码
//                 LuckySheetModel areaCode = validateField(idx + 1 - successNum.get(), 2, importDto.getAreaCode(), importDto, "areaCode", validateData);
//                 luckySheetModelRow.add(areaCode);
//
//                 // 区域名称
//                 LuckySheetModel areaName = validateField(idx + 1 - successNum.get(), 3, importDto.getAreaName(), importDto, "areaName", validateData);
//                 luckySheetModelRow.add(areaName);
//
//                 // 上级编码
//                 LuckySheetModel pidCode = validateField(idx + 1 - successNum.get(), 4, importDto.getPidCode(), importDto, "pidCode", validateData);
//                 luckySheetModelRow.add(pidCode);
//
//                 // 上级区域名称
//                 LuckySheetModel pidName = validateField(idx + 1 - successNum.get(), 5, importDto.getPidName(), importDto, "pidName", validateData);
//                 luckySheetModelRow.add(pidName);
//
//                 // 区域描述
//                 LuckySheetModel areaDesc = validateField(idx + 1 - successNum.get(), 6, importDto.getAreaDesc(), importDto, "areaDesc", validateData);
//                 luckySheetModelRow.add(areaDesc);
//
//                 importDto.setSheetModelList(luckySheetModelRow);
//
//                 Boolean success = areaFeignClient.saveSheetData(importDto);
//                 if (BooleanUtil.isTrue(success)) {
//                     successNum.getAndIncrement();
//                 }
//                 importService.sendArea(globalCache.get().getCompany());
//             });
//         } catch (Exception e) {
//             log.error(e.getMessage(), e);
//         } finally {
//             redisService.hSet(RedisConstant.companyImportKey(ImportService.AREA, globalCache.get().getCompany()), "notice", true);
//             redisService.hSet(RedisConstant.companyImportKey(ImportService.AREA, globalCache.get().getCompany()), "finish", true);
//             importService.sendArea(globalCache.get().getCompany());
//             // 更新任务状态
//             if (importTaskDto != null && importTaskDto.getId() != null) {
//                 String key = RedisConstant.companyImportKey(ImportService.AREA, globalCache.get().getCompany());
//                 Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
//                 JSONObject json = new JSONObject(map);
//                 ImportDto importDto = json.toJavaObject(ImportDto.class);
//                 importTaskDto.setTaskImportStatus(importDto);
//                 importTaskFeignClient.update(importTaskDto);
//             }
//             this.clearThreadLocal();
//         }
//     }
//
//     private void saveLuckySheetHead(Long taskId) {
//         HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
//         List<LuckySheetModel> headModelList = new ArrayList<>();
//         AtomicInteger cellIndex = new AtomicInteger(0);
//         List<String> must = ListUtil.of("areaCode", "areaName", "orgName");
//         tableHeader.forEach((k, v) -> {
//             LuckySheetModel luckySheetModel = new LuckySheetModel();
//             luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
//             LuckySheetModel.Value modelV = luckySheetModel.getV();
//             modelV.setV(k);
//             if (must.contains(v)) {
//                 modelV.setFc("#ff0000");
//             }
//             headModelList.add(luckySheetModel);
//         });
//         importErrorDto.setHeadModelList(headModelList);
//         this.areaFeignClient.saveSheetHead(importErrorDto);
//
//     }
//
// }
