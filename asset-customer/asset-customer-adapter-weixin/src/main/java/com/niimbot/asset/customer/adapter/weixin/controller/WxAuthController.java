package com.niimbot.asset.customer.adapter.weixin.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.LoginRecord;
import com.niimbot.asset.customer.adapter.weixin.model.AuthCodeLoginRequest;
import com.niimbot.asset.customer.adapter.weixin.service.AuthenticationService;
import com.niimbot.asset.customer.adapter.weixin.service.WxFeignClient;
import com.niimbot.asset.weixin.base.dto.InnerAuthRequest;
import com.niimbot.asset.weixin.base.dto.WeixinCorpDto;
import com.niimbot.asset.weixin.base.error.WxResultCode;
import com.niimbot.asset.weixin.base.restops.RestOps;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.net.URL;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.cp.bean.WxCpMaJsCode2SessionResult;
import me.chanjar.weixin.cp.bean.WxCpTpUserInfo;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.mp.api.WxMpService;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:41
 */
@Slf4j
@Api(tags = "【企业微信】认证接口")
@ResultController
@RequestMapping("api/weixin/auth")
@RequiredArgsConstructor
@Validated
public class WxAuthController {

    private final WxMpService wxMpService;
    private final WxCpTpService wxCpTpService;
    private final WxCpTpService wxOauthService;
    private final AuthenticationService authenticationService;
    private final WxFeignClient wxFeignClient;
    private final WxOpenApiService wxOpenApiService;

    @LoginRecord
    @ApiOperation(value = "企业应用内免登")
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody @Validated AuthCodeLoginRequest loginRequest) {
        log.info("第三方企业应用免登, AuthCode =>> [{}]", loginRequest.getAuthCode());
        WxCpTpUserInfo wxCpTpUserInfo = RestOps.handleOrThrow(() -> wxCpTpService.getUserInfo3rd(loginRequest.getAuthCode()));
        String openId = wxCpTpUserInfo.getOpenUserId();
        String corpId = wxCpTpUserInfo.getCorpId();
        log.info("第三方企业应用免登，AuthCode转换openId=[{}], corpId=[{}]", openId, corpId);
        /*if (corp == null) {
            // 补偿企业未同步
            loadAuthInfo(loginRequest.getCorpId());
        }*/
        String token = authenticationService.loadUserByOpenId(openId, corpId);
        Map<String, Object> map = new HashMap<>();
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        map.put("corpId", corpId);
        return map;
    }

    @LoginRecord
    @ApiOperation(value = "企业扫码免登")
    @PostMapping("/scan/login")
    public Map<String, Object> scanLogin(@RequestBody @Validated AuthCodeLoginRequest loginRequest) {
        log.info("第三方企业应用免登, AuthCode =>> [{}]", loginRequest.getAuthCode());
        WxCpTpUserInfo wxCpTpUserInfo = RestOps.handleOrThrow(() -> wxOauthService.getUserInfo3rd(loginRequest.getAuthCode()));
        String openId = wxCpTpUserInfo.getOpenUserId();
        String corpId = wxCpTpUserInfo.getCorpId();
        log.info("第三方企业应用免登，AuthCode转换openId=[{}], corpId=[{}]", openId, corpId);
        /*if (corp == null) {
            // 补偿企业未同步
            loadAuthInfo(loginRequest.getCorpId());
        }*/
        String token = authenticationService.loadUserByOpenId(openId, corpId);
        Map<String, Object> map = new HashMap<>();
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        map.put("corpId", corpId);
        return map;
    }

    @LoginRecord
    @ApiOperation(value = "小程序内免登企业应用免登")
    @PostMapping("/miniapp/login")
    public Map<String, Object> miniappLogin(@RequestBody @Validated AuthCodeLoginRequest loginRequest) {
        log.info("小程序应用免登, AuthCode =>> [{}]", loginRequest.getAuthCode());
        WxCpMaJsCode2SessionResult code2SessionResult = RestOps.handleOrThrow(() -> wxCpTpService.jsCode2Session(loginRequest.getAuthCode()));
        String userId = code2SessionResult.getUserId();
        String corpId = code2SessionResult.getCorpId();
        log.info("小程序应用免登，AuthCode转换openId=[{}], corpId=[{}]", userId, corpId);
        /*if (corp == null) {
            // 补偿企业未同步
            loadAuthInfo(loginRequest.getCorpId());
        }*/
        String token = authenticationService.loadUserByOpenId(userId, corpId);
        Map<String, Object> map = new HashMap<>();
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        map.put("corpId", corpId);
        return map;
    }

    @ApiOperation(value = "获取jsApi")
    @GetMapping("/jsApi")
    public JsApiDto jsApi(
            @RequestParam("corpId") String corpId,
            @RequestParam("url") String url) {
        WeixinCorpDto corp = wxFeignClient.getCorp(corpId, null);
        String ticket = RestOps.handleOrThrow(() -> {
            wxCpTpService.getCorpToken(corpId, corp.getPermanentCode(), false);
            return wxCpTpService.getAuthCorpJsApiTicket(corpId);
        });
        String nonceStr = "weixin-asset";
        long timeStamp = LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();
        try {
            String plain = "jsapi_ticket=" + ticket + "&noncestr=" + nonceStr + "&timestamp=" + timeStamp
                    + "&url=" + decodeUrl(url);
            JsApiDto jsApiDto = new JsApiDto();
            jsApiDto.setCorpId(corpId);
            jsApiDto.setTimeStamp(timeStamp);
            jsApiDto.setNonceStr(nonceStr);
            jsApiDto.setSignature(DigestUtils.sha1Hex(plain));
            jsApiDto.setAgentId(corp.getAgentId());
            jsApiDto.setUrl(url);
            return jsApiDto;
        } catch (Exception e) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "获取jsTicket签名失败");
        }
    }

    @ApiOperation(value = "获取wxConfig")
    @PostMapping("/wxConfig")
    public WxJsapiSignature appJsApi(@RequestBody JSONObject json) {
        if (!json.containsKey("url")) {
            return null;
        }
        return RestOps.handleOrThrow(() -> wxMpService.createJsapiSignature(json.getString("url")));
    }

    @ApiOperation(value = "内部认证")
    @PostMapping("/innerAuth")
    public Map<String, Object> login(@RequestBody @Validated InnerAuthRequest authRequest) {
        return MapUtil.of("access_token", authenticationService.inner(authRequest));
    }

    @ApiOperation(value = "获取agent_jsApi")
    @GetMapping("/agent/jsApi")
    public JsApiDto agentJsApi(
            @RequestParam("corpId") String corpId,
            @RequestParam("url") String url) {
        WeixinCorpDto corp = wxFeignClient.getCorp(corpId, null);
        String ticket = RestOps.handleOrThrow(() -> {
            wxCpTpService.getCorpToken(corpId, corp.getPermanentCode(), false);
            return wxCpTpService.getSuiteJsApiTicket(corpId);
        });
        String nonceStr = "weixin-asset";
        long timeStamp = LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();
        try {
            String plain = "jsapi_ticket=" + ticket + "&noncestr=" + nonceStr + "&timestamp=" + timeStamp
                    + "&url=" + decodeUrl(url);
            JsApiDto jsApiDto = new JsApiDto();
            jsApiDto.setCorpId(corpId);
            jsApiDto.setTimeStamp(timeStamp);
            jsApiDto.setNonceStr(nonceStr);
            jsApiDto.setSignature(DigestUtils.sha1Hex(plain));
            jsApiDto.setAgentId(corp.getAgentId());
            jsApiDto.setUrl(url);
            return jsApiDto;
        } catch (Exception e) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "获取jsTicket签名失败");
        }
    }

    @ApiOperation(value = "获取agent_jsApi")
    @PostMapping("/agent/jsApi")
    public JsApiDto agentJsApi(@RequestBody JSONObject json) {
        String corpId = json.getString("corpId");
        String url = json.getString("url");
        WeixinCorpDto corp = wxFeignClient.getCorp(corpId, null);
        String ticket = RestOps.handleOrThrow(() -> {
            wxCpTpService.getCorpToken(corpId, corp.getPermanentCode(), false);
            return wxCpTpService.getSuiteJsApiTicket(corpId);
        });
        String nonceStr = "weixin-asset";
        long timeStamp = LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();
        try {
            String plain = "jsapi_ticket=" + ticket + "&noncestr=" + nonceStr + "&timestamp=" + timeStamp
                    + "&url=" + decodeUrl(url);
            JsApiDto jsApiDto = new JsApiDto();
            jsApiDto.setCorpId(corpId);
            jsApiDto.setTimeStamp(timeStamp);
            jsApiDto.setNonceStr(nonceStr);
            jsApiDto.setSignature(DigestUtils.sha1Hex(plain));
            jsApiDto.setAgentId(corp.getAgentId());
            jsApiDto.setUrl(url);
            return jsApiDto;
        } catch (Exception e) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "获取jsTicket签名失败");
        }
    }

    @ApiOperation(value = "获取推广包生成注册码")
    @GetMapping("/registerCode")
    public JSONObject registerCode() {
        String registerCode;
        try {
            registerCode = wxOpenApiService.getRegisterCode();
            log.info("registerCode = {}", registerCode);
            return (JSONObject) JSONObject.parse(registerCode);
        } catch (BusinessException ex) {
            throw ex;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "获取推广注册码失败");
        }
    }

    /**
     * 因为ios端上传递的url是encode过的，android是原始的url。开发者使用的也是原始url, 所以需要把参数进行一般urlDecode
     *
     * @param url
     * @return
     * @throws Exception
     */
    private static String decodeUrl(String url) throws Exception {
        URL urler = new URL(url);
        StringBuilder urlBuffer = new StringBuilder();
        urlBuffer.append(urler.getProtocol());
        urlBuffer.append(":");
        if (urler.getAuthority() != null && urler.getAuthority().length() > 0) {
            urlBuffer.append("//");
            urlBuffer.append(urler.getAuthority());
        }
        if (urler.getPath() != null) {
            urlBuffer.append(urler.getPath());
        }
        if (urler.getQuery() != null) {
            urlBuffer.append('?');
            urlBuffer.append(URLDecoder.decode(urler.getQuery(), "utf-8"));
        }
        return urlBuffer.toString();
    }

    @Data
    static class JsApiDto {
        private Integer agentId;
        private String corpId;
        private Long timeStamp;
        private String nonceStr;
        private String signature;
        private String url;
    }

}
