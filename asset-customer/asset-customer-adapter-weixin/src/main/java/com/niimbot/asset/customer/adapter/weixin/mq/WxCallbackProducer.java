package com.niimbot.asset.customer.adapter.weixin.mq;

import com.niimbot.asset.framework.annotation.MessageProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.AbstractRocketMqProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqPropertiesConfig;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.asset.weixin.base.constant.WxMqConstant;

/**
 * <AUTHOR>
 * @date 2023/7/10 18:54
 */
@MessageProducer(topic = MqConstant.ASSET_TOPIC, group = WxMqConstant.ASSET_WEIXIN_CALLBACK_GROUP)
public class WxCallbackProducer extends AbstractRocketMqProducer {

    public WxCallbackProducer(RocketMqPropertiesConfig propertiesConfig) {
        super(propertiesConfig);
    }

}
