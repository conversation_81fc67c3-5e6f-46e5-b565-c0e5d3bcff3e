package com.niimbot.asset.customer.adapter.weixin.service;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.weixin.base.dto.UpdateEmployeeNo;
import com.niimbot.asset.weixin.base.dto.UpdateOrgNo;
import com.niimbot.asset.weixin.base.dto.WeixinCorpDto;
import com.niimbot.asset.weixin.base.dto.WxSaleOrderQueryResult;
import com.niimbot.sale.SaleOrderPageQueryDto;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/7/4 10:50
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface WxFeignClient {

    @PostMapping("server/weixin/callback")
    Long saveCallback(WxCpTpXmlMessage message);

    @GetMapping("server/weixin/user/getByOpenId")
    CusUserDto selectUserByOpenId(@RequestParam("openId") String openId,
                                  @RequestParam("corpId") String corpId);

    @PostMapping("server/weixin/contacts/manual/sync")
    Boolean manualSync();

    @GetMapping("server/weixin/corp")
    WeixinCorpDto getCorp(@RequestParam(value = "corpId", required = false) String corpId,
                          @RequestParam(value = "companyId", required = false) Long companyId);

    @GetMapping(value = "server/weixin/system/employee/page")
    PageUtils<CusEmployeeDto> page(@SpringQueryMap CusEmployeeQueryDto dto);

    @PostMapping("server/weixin/sale/order/page")
    PageUtils<WxSaleOrderQueryResult> saleOrderPage(SaleOrderPageQueryDto query);

    @PostMapping("/server/weixin/message/test")
    boolean sendMessage();

    @PostMapping("server/weixin/contacts/emp/updateNo")
    Boolean updateEmpNo(@RequestBody UpdateEmployeeNo updateEmployeeNo);

    @PostMapping("server/weixin/contacts/org/updateNo")
    Boolean updateOrgNo(@RequestBody UpdateOrgNo updateOrgNo);

}
