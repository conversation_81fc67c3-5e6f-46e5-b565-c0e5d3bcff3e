package com.niimbot.asset.customer.adapter.weixin.oss;

import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Profile({Edition.WEIXIN})
public class WxOssServiceImpl implements FileUploadService {

    private final WxOpenApiService openApiService;

    @Override
    public String putFile(String key, String filePath) {
        return openApiService.multiFileTrans(LoginUserThreadLocal.getCompanyId(), filePath, key);
    }
}
