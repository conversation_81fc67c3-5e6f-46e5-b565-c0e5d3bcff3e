package com.niimbot.asset.customer.adapter.weixin.controller;

import com.niimbot.asset.customer.adapter.weixin.service.WxFeignClient;
import com.niimbot.asset.weixin.base.dto.UpdateEmployeeNo;
import com.niimbot.asset.weixin.base.dto.UpdateOrgNo;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Api(tags = "【企业微信通讯录】")
@ResultController
@RequiredArgsConstructor
@RequestMapping("/api/weixin/contacts")
public class WxContactsController {

    private final WxFeignClient wxFeignClient;

    @PostMapping("/manual/sync")
    public Boolean manualSync() {
        return wxFeignClient.manualSync();
    }

    @PostMapping("/emp/updateNo")
    public Boolean empUpdateNo(@Validated @RequestBody UpdateEmployeeNo dto) {
        return wxFeignClient.updateEmpNo(dto);
    }

    @PostMapping("/org/updateNo")
    public Boolean orgUpdateNo(@Validated @RequestBody UpdateOrgNo dto) {
        return wxFeignClient.updateOrgNo(dto);
    }

}
