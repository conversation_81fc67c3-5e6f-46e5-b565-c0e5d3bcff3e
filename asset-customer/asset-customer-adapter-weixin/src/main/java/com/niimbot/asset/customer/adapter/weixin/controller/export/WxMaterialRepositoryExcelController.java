package com.niimbot.asset.customer.adapter.weixin.controller.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialRepositoryService;
import com.niimbot.asset.service.feign.MaterialRepositoryFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.material.MaterialRepositorySearchDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/22 13:31
 */
@Slf4j
@Api(tags = "【耗材】仓库管理")
@ResultController
@RequestMapping("/api/weixin/material/repository")
@Validated
@RequiredArgsConstructor
public class WxMaterialRepositoryExcelController {

    private final MaterialRepositoryService repositoryService;

    private final RedisService redisService;

    private final WxOpenApiService openApiService;

    private final CacheResourceUtil cacheResourceUtil;

    private final MaterialRepositoryFeignClient feignClient;

    @ApiOperation(value = "【PC】导出耗材仓库模板")
    @GetMapping(value = "/exportTemplate")
    public String exportTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = repositoryService.buildWriter();
        return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), "耗材仓库导入模板.xlsx", excelWriter);
    }

    @ApiOperation(value = "【PC】导入耗材仓库模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            repositoryService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        repositoryService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.MATERIAL_REPOSITORY));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }


    @ApiOperation("导出仓库Excel")
    @PostMapping(value = "/excel")
    public String excel(
            @RequestBody MaterialRepositorySearchDto dto, HttpServletResponse response) {
        List<MaterialRepositoryDto> all = feignClient.excel(dto);
        all.forEach(v -> {
            List<Long> admins = v.getAdmins();
            if (CollUtil.isEmpty(admins)) {
                return;
            }
            String text = admins.stream().map(cacheResourceUtil::getUserName).collect(Collectors.joining(","));
            v.setAdminText(text);
        });
        // excel名称 与 格式
        String excelBaseName = "仓库清单";
        String excelSuffix = ".xlsx";
        // excel头
        LinkedHashMap<String, String> headers = new LinkedHashMap<>(3);
        headers.put("managerOwnerText", "所属公司");
        headers.put("code", "仓库编码");
        headers.put("name", "仓库名称");
        headers.put("adminText", "仓库管理员");
        headers.put("remark", "仓库备注");
        // 审计日志
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MRL_REP));
        // excel
        ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headers, all));
        return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), excelBaseName + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + excelSuffix, excelWriter);
    }

}
