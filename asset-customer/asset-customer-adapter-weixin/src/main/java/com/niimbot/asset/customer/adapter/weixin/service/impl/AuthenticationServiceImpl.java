package com.niimbot.asset.customer.adapter.weixin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.niimbot.asset.customer.adapter.weixin.service.AuthenticationService;
import com.niimbot.asset.customer.adapter.weixin.service.WxFeignClient;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.service.AbstractDefaultLoginService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.CusRoleFeignClient;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.asset.weixin.base.dto.InnerAuthRequest;
import com.niimbot.asset.weixin.base.support.PropertiesHolder;
import com.niimbot.framework.dataperm.core.model.ModelDataScope;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CompanyDto;
import com.niimbot.system.CusRoleDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * created by chen.y on 2021/8/24 10:49
 */
@Slf4j
@Service
public class AuthenticationServiceImpl extends AbstractDefaultLoginService implements AuthenticationService {

    private final WxFeignClient wxFeignClient;
    private final CompanyFeignClient companyFeignClient;
    private final CusRoleFeignClient roleFeignClient;
    private final AbstractTokenService tokenService;
    private final AccountCenterFeignClient accountCenterFeignClient;

    public AuthenticationServiceImpl(WxFeignClient wxFeignClient,
                                     CompanyFeignClient companyFeignClient,
                                     CusRoleFeignClient roleFeignClient,
                                     CusUserFeignClient userFeignClient,
                                     RedisService redisService,
                                     AbstractTokenService tokenService,
                                     AccountCenterFeignClient accountCenterFeignClient) {
        super(redisService, userFeignClient);
        this.wxFeignClient = wxFeignClient;
        this.companyFeignClient = companyFeignClient;
        this.roleFeignClient = roleFeignClient;
        this.tokenService = tokenService;
        this.accountCenterFeignClient = accountCenterFeignClient;
    }

    @Override
    public String loadUserByOpenId(String openId, String corpId) {
        // 查询用户
        CusUserDto cusUser = wxFeignClient.selectUserByOpenId(openId, corpId);
        if (cusUser != null) {
            // 查询角色
            List<CusRoleDto> roleList = roleFeignClient.getRoleByUserId(cusUser.getId());
            // 判断是否超级管理员
            List<String> roleNames = roleList.stream().map(CusRoleDto::getRoleCode).collect(Collectors.toList());
            cusUser.setIsAdmin(roleNames.contains(BaseConstant.ADMIN_ROLE));
        }

        // 账户信息不存在
        if (cusUser == null) {
            log.info("登录用户：userId : {} 不存在.", openId);
            throw new BusinessException(SystemResultCode.USER_NOT_EXIST.getCode(), SystemResultCode.USER_NOT_EXIST.getMessage());
        } else if (cusUser.getStatus().shortValue() == DictConstant.SYS_DISABLE) {
            log.info("登录用户：{} 已被停用.", openId);
            throw new BusinessException(SystemResultCode.USER_ACCOUNT_FORBIDDEN.getCode(), SystemResultCode.USER_ACCOUNT_FORBIDDEN.getMessage());
        } else if (ObjectUtil.isNull(cusUser.getCompanyId())) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_REGISTER.getCode(), SystemResultCode.USER_COMPANY_NOT_REGISTER.getMessage());
        } else {
            CompanyDto companyInfo = companyFeignClient.getCompanyInfo(cusUser.getCompanyId());
            if (ManageConstant.COMPANY_STATUS_DISABLE == companyInfo.getStatus()) {
                throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN.getCode(), SystemResultCode.COMPANY_FORBIDDEN.getMessage());
            }
        }
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(cusUser);
        // 登录成功时，添加全局水平权限配置
//        List<ModelDataScope> dataScopes = buildDataScope(userDto);
//        userDto.setModelDataScopes(dataScopes);

        log.info("用户 {} 登录成功", userDto.getCusUser().getAccount());
        super.loginAfterRecord(userDto.getCusUser(), "weixin");
        // 生成token
        return tokenService.createToken(userDto);

    }

    @Override
    public String inner(InnerAuthRequest authRequest) {
        String md5 = DigestUtil.md5Hex(authRequest.getAccount() + PropertiesHolder.getWxToken());
        if (!md5.equals(authRequest.getSecureKey())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "MD5值不匹配");
        }

        CusUserDto cusUser = accountCenterFeignClient.getLoginInfoByWay(authRequest.getAccount());
        if (cusUser != null) {
            // 查询角色
            List<CusRoleDto> roleList = roleFeignClient.getRoleByUserId(cusUser.getId());
            // 判断是否超级管理员
            List<String> roleNames = roleList.stream().map(CusRoleDto::getRoleCode).collect(Collectors.toList());
            cusUser.setIsAdmin(roleNames.contains(BaseConstant.ADMIN_ROLE));
        }

        // 账户信息不存在
        if (cusUser == null) {
            throw new BusinessException(SystemResultCode.USER_NOT_EXIST.getCode(), SystemResultCode.USER_NOT_EXIST.getMessage());
        } else if (cusUser.getStatus() == DictConstant.SYS_DISABLE) {
            throw new BusinessException(SystemResultCode.USER_ACCOUNT_FORBIDDEN.getCode(), SystemResultCode.USER_ACCOUNT_FORBIDDEN.getMessage());
        } else if (ObjectUtil.isNull(cusUser.getCompanyId())) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_REGISTER.getCode(), SystemResultCode.USER_COMPANY_NOT_REGISTER.getMessage());
        } else {
            CompanyDto companyInfo = companyFeignClient.getCompanyInfo(cusUser.getCompanyId());
            if (ManageConstant.COMPANY_STATUS_DISABLE == companyInfo.getStatus()) {
                throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN.getCode(), SystemResultCode.COMPANY_FORBIDDEN.getMessage());
            }
        }
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(cusUser);
        // 登录成功时，添加全局水平权限配置
//        List<ModelDataScope> dataScopes = buildDataScope(userDto);
//        userDto.setModelDataScopes(dataScopes);
        // 生成token
        userDto.getCusUser().setMobile(userDto.getCusUser().getUnionId());
        return tokenService.createToken(userDto);
    }
}
