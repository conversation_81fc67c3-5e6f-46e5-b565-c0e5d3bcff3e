package com.niimbot.asset.customer.adapter.weixin.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.*;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.SimpleTimeLimiter;
import com.google.common.util.concurrent.TimeLimiter;
import com.niimbot.asset.customer.adapter.weixin.service.WxCallbackContext;
import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.asset.weixin.base.support.PropertiesHolder;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.util.crypto.WxCpTpCryptUtil;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nonnull;
import javax.annotation.concurrent.Immutable;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2023/7/4 10:40
 */
@Slf4j
@Api(tags = "【企业微信】接口回调")
@RestController
@RequestMapping("api/weixin/callback")
@RequiredArgsConstructor
@Validated
public class WxCallbackController {

    private final WxCpTpService wxCpTpService;
    private final WxCpTpCryptUtil wxCpTpCrypt;
    private final WxCallbackContext wxCallbackContext;
    private final RestTemplate restTemplate;
    private final ThreadPoolTaskExecutor taskExecutor;

    @GetMapping(value = "/call", produces = "application/xml; charset=UTF-8")
    public String get(@RequestParam(name = "msg_signature", required = false) String signature,
                      @RequestParam(name = "timestamp", required = false) String timestamp,
                      @RequestParam(name = "nonce", required = false) String nonce,
                      @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);
        if (!StrUtil.isAllNotEmpty(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        if (wxCpTpService.checkSignature(signature, timestamp, nonce, echostr)) {
            return wxCpTpCrypt.decrypt(echostr);
        }

        return "非法请求";
    }

    @PostMapping(value = "/call", produces = "application/xml; charset=UTF-8")
    public String post(@RequestBody String requestBody,
                       @RequestParam("msg_signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce) {

        String decryptXml = wxCpTpCrypt.decryptXml(signature, timestamp, nonce, requestBody);
        WxCpTpXmlMessage message = WxCpTpXmlMessage.fromXml(decryptXml);
        log.info("接收到的企业微信回调消息 : {}", JSONObject.toJSONString(message));
        wxCallbackContext.execute(message);
        return "success";
    }

    @GetMapping(value = "/auth", produces = "application/xml; charset=UTF-8")
    public String authGet(@RequestParam(name = "msg_signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);

        if (!StrUtil.isAllNotEmpty(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        if (wxCpTpService.checkSignature(signature, timestamp, nonce, echostr)) {
            return wxCpTpCrypt.decrypt(echostr);
        }

        return "非法请求";
    }

    @PostMapping(value = "/auth", produces = "application/xml; charset=UTF-8")
    public String authPost(@RequestBody String requestBody,
                           @RequestParam("msg_signature") String signature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce) {
        String decryptXml = wxCpTpCrypt.decryptXml(signature, timestamp, nonce, requestBody);
        WxCpTpXmlMessage message = WxCpTpXmlMessage.fromXml(decryptXml);
        if (needForward(message)) {
            forward("/api/weixin/callback/auth", requestBody, signature, timestamp, nonce);
        }
        log.info("接收到的企业微信回调消息 : {}", JSONObject.toJSONString(message));
        wxCallbackContext.execute(message);
        return "success";
    }

    @GetMapping(value = "/common", produces = "application/xml; charset=UTF-8")
    public String getCommon(@RequestParam(name = "msg_signature", required = false) String signature,
                            @RequestParam(name = "timestamp", required = false) String timestamp,
                            @RequestParam(name = "nonce", required = false) String nonce,
                            @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);

        if (!StrUtil.isAllNotEmpty(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        if (wxCpTpService.checkSignature(signature, timestamp, nonce, echostr)) {
            return wxCpTpCrypt.decrypt(echostr);
        }

        return "非法请求";
    }

    @PostMapping(value = "/common", produces = "application/xml; charset=UTF-8")
    public String postCommon(@RequestBody String requestBody,
                             @RequestParam("msg_signature") String signature,
                             @RequestParam("timestamp") String timestamp,
                             @RequestParam("nonce") String nonce) {
        String decryptXml = wxCpTpCrypt.decryptXml(signature, timestamp, nonce, requestBody);
        WxCpTpXmlMessage message = WxCpTpXmlMessage.fromXml(decryptXml);
        if (needForward(message)) {
            forward("/api/weixin/callback/common", requestBody, signature, timestamp, nonce);
            return "success";
        }
        log.info("接收到的企业微信回调消息 : {}", JSONObject.toJSONString(message));
        wxCallbackContext.execute(message);
        return "success";
    }

    private boolean needForward(WxCpTpXmlMessage message) {
        try {
            return PropertiesHolder.getActive().stream().anyMatch("weixin"::equals) && !PropertiesHolder.getWxSuiteId().equals(message.getSuiteId());
        } catch (Exception e) {
            log.error("weixin call back needForward error", e);
            return false;
        }
    }

    private final ThreadPoolExecutor forwardThreadPool = AssetThreadPoolExecutorManager.newThreadPool(
            "wx-cb-fd", 2, 2, 1000
    );

    private final Retryer<String> retryer = RetryerBuilder.<String>newBuilder()
            // retryIf 重试条件
            .retryIfException()
            .retryIfRuntimeException()
            .retryIfExceptionOfType(Exception.class)
            .retryIfException(throwable -> Objects.equals(throwable, new Exception()))
            .retryIfResult(v -> !"success".equals(v))
            // 等待策略：间隔5秒，每次递增5s
            .withWaitStrategy(WaitStrategies.incrementingWait(2, TimeUnit.SECONDS, 2, TimeUnit.SECONDS))
            // 停止策略 : 尝试请求4次
            .withStopStrategy(StopStrategies.stopAfterAttempt(5))
            .withAttemptTimeLimiter(new CustomFixedAttemptTimeLimit<>(1, TimeUnit.MINUTES, forwardThreadPool))
            .build();

    @Immutable
    private static final class CustomFixedAttemptTimeLimit<T> implements AttemptTimeLimiter<T> {
        private final TimeLimiter timeLimiter;
        private final long duration;
        private final TimeUnit timeUnit;

        public CustomFixedAttemptTimeLimit(long duration, @Nonnull TimeUnit timeUnit, @Nonnull ExecutorService executorService) {
            this(SimpleTimeLimiter.create(executorService), duration, timeUnit);
        }

        private CustomFixedAttemptTimeLimit(@Nonnull TimeLimiter timeLimiter, long duration, @Nonnull TimeUnit timeUnit) {
            Preconditions.checkNotNull(timeLimiter);
            Preconditions.checkNotNull(timeUnit);
            this.timeLimiter = timeLimiter;
            this.duration = duration;
            this.timeUnit = timeUnit;
        }

        @Override
        public T call(Callable<T> callable) throws Exception {
            return timeLimiter.callWithTimeout(callable, duration, timeUnit);
        }
    }

    private void forward(String url, String requestBody, String signature, String timestamp, String nonce) {
        taskExecutor.execute(() -> {
            StringBuilder builder = new StringBuilder("https://asweixin.jc-test.cn/customer");
            try {
                retryer.call(() -> {
                    try {
                        builder.append(url);
                        builder.append("?msg_signature=").append(signature)
                                .append("&timestamp=").append(timestamp)
                                .append("&nonce=").append(nonce);
                        HttpHeaders headers = new HttpHeaders();
                        headers.setContentType(MediaType.APPLICATION_XML);
                        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
                        return restTemplate.exchange(builder.toString(), HttpMethod.POST, entity, String.class).getBody();
                    } catch (Exception e) {
                        log.error("weixin callback forward error", e);
                        return "error";
                    }
                });
            } catch (ExecutionException | RetryException e) {
                log.error("weixin callback forward retry exception", e);
            }
        });
    }

}
