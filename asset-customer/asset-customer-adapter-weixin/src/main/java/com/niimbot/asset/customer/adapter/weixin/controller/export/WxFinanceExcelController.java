package com.niimbot.asset.customer.adapter.weixin.controller.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.finance.AssetFinanceInfoFeignClient;
import com.niimbot.asset.service.feign.finance.AssetFinanceSettleFeignClient;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.finance.*;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "企业微信财务导出")
@ResultController
@RequestMapping("api/weixin/finance")
@RequiredArgsConstructor
public class WxFinanceExcelController {

    private final AssetFinanceInfoFeignClient financeInfoFeignClient;

    private final AssetFinanceSettleFeignClient financeSettleFeignClient;

    private final WxOpenApiService openApiService;

    @ApiOperation("折旧明细导出")
    @PostMapping("/settle/settleDetailExport")
    public String settleDetailExport(@RequestBody @Validated SettleDetailQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SettleDetailDto.class);
            // 查询数据
            PageUtils<SettleDetailDto> details = financeSettleFeignClient.settleDetail(query);
            List<SettleDetailDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "折旧明细-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getPageNum() > 1) {
                fileName += "-" + query.getPageNum();
            }
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("折旧汇总导出--所属公司")
    @PostMapping("/settle/settleSummaryExport")
    public String settleSummaryExport(@RequestBody @Validated SettleSummaryQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SettleSummaryDto.class);
            // 查询数据
            PageUtils<SettleSummaryDto> details = financeSettleFeignClient.settleSummary(query);
            List<SettleSummaryDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "折旧汇总-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getPageNum() > 1) {
                fileName += "-" + query.getPageNum();
            }
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("资产财务台账导出")
    @PostMapping("/bill/queryMachineAccountExport")
    public String queryMachineAccountExport(@RequestBody @Validated AssetFinanceInfoQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(AssetMachineAccountDto.class);
            // 查询数据
            PageUtils<AssetMachineAccountDto> details = financeInfoFeignClient.queryMachineAccount(query);
            List<AssetMachineAccountDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "资产财务台账-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getCurrent() > 1) {
                fileName += "-" + query.getCurrent();
            }
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("资产初始入账列表导出")
    @PostMapping("/bill/queryInitFinanceInfoExport")
    public String accumulatedUsedDetailsExport(@RequestBody @Validated AssetFinanceInfoQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(AssetInitFinanceInfoDto.class);
            // 查询数据
            PageUtils<AssetInitFinanceInfoDto> details = financeInfoFeignClient.queryInitFinanceInfo(query);
            List<AssetInitFinanceInfoDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "资产初始入账-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getCurrent() > 1) {
                fileName += "-" + query.getCurrent();
            }
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }


    @ApiOperation("折旧汇总导出--分摊部门")
    @PostMapping("/settle/settleSummaryDeptExport")
    public String settleSummaryDeptExport(@RequestBody @Validated SettleSummaryQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SettleSummaryDeptDto.class);
            // 查询数据
            PageUtils<SettleSummaryDeptDto> details = financeSettleFeignClient.settleSummaryDept(query);
            List<SettleSummaryDeptDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "折旧汇总-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getPageNum() > 1) {
                fileName += "-" + query.getPageNum();
            }
            ExcelWriter excelWriter = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", excelWriter);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
