package com.niimbot.asset.customer.adapter.weixin.controller.export;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.EquipmentSiteInspectPointService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectPointFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.equipment.EquipmentSiteInspectPointExportDto;
import com.niimbot.equipment.EquipmentSiteInspectPointQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
@Api(tags = "点位管理")
@ResultController
@RequestMapping("/api/weixin/equipment/site/inspect/point")
@Validated
@RequiredArgsConstructor
public class WxEntSntPointController {

    private final EquipmentSiteInspectPointService inspectPointService;

    private final RedisService redisService;
    private final WxOpenApiService openApiService;

    private final EquipmentSiteInspectPointFeignClient feignClient;

    private final CacheResourceUtil cacheResourceUtil;

    @ApiOperation(value = "【PC】导出区域模板")
    @GetMapping(value = "/exportTemplate")
    public String exportTemplate() {
        ExcelWriter excelWriter = inspectPointService.buildWriter();
        return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), "巡检点位导入模板.xlsx", excelWriter);
    }


    @ApiOperation(value = "【PC】导入巡检点位")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            inspectPointService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error("import point error:", e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "巡检点位导出")
    @PostMapping("/export")
    public String pointExport(@RequestBody EquipmentSiteInspectPointQueryDto queryDto, HttpServletResponse response) {
        try {
            // 查询数据
            queryDto.setPageNum(1);
            queryDto.setPageSize(Integer.MAX_VALUE);
            PageUtils<AsEquipmentSiteInspectPointDto> pagePoints = feignClient.pagePoint(queryDto);
            List<EquipmentSiteInspectPointExportDto> excel = new ArrayList<>();
            for (AsEquipmentSiteInspectPointDto pointDto : pagePoints.getList()) {
                EquipmentSiteInspectPointExportDto pointExportDto = new EquipmentSiteInspectPointExportDto().setPointCode(pointDto.getPointCode())
                        .setPointName(pointDto.getPointName())
                        .setOrgName(cacheResourceUtil.getOrgName(pointDto.getOrgId()))
                        .setPidName(cacheResourceUtil.getAreaName(pointDto.getPid()))
                        .setSpecificLocation(pointDto.getSpecificLocation())
                        .setPointDesc(pointDto.getPointDesc())
                        .setPointImages(StringUtils.join(pointDto.getPointImages(),","));
                excel.add(pointExportDto);
            }
            // 查询head
            LinkedHashMap<String, String> header = ExcelUtils.buildExcelHead(EquipmentSiteInspectPointExportDto.class);
            String fileName = "点位信息-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            ExcelWriter writer = WxExcels.export(new ExcelExportDto(header, excel));
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_SITE_INSPECT_POINT, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(pagePoints.getList().size()))));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", writer);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        inspectPointService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.INSPECT_POINT));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
