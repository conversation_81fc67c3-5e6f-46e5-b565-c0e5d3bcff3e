package com.niimbot.asset.customer.adapter.weixin.controller;

import com.niimbot.asset.customer.adapter.weixin.service.WxFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "【企业微信】企业接口")
@ResultController
@RequestMapping("api/weixin/message")
@RequiredArgsConstructor
public class WxMessageController {

    private final WxFeignClient wxFeignClient;

    @PostMapping("/test")
    public boolean sendMessage() {
        return wxFeignClient.sendMessage();
    }

}
