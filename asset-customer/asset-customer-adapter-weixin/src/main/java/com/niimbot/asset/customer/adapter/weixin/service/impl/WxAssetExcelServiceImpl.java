package com.niimbot.asset.customer.adapter.weixin.service.impl;

/**
 * <AUTHOR>
 * @since 2021/3/2 15:43
 */
// @Slf4j
// @Service("wxAssetExcelServiceImpl")
// public class WxAssetExcelServiceImpl extends AssetExcelServiceImpl {
//
//     private final ImportTaskFeignClient importTaskFeignClient;
//
//     public WxAssetExcelServiceImpl(RedisService redisService,
//                                    ImportService importService,
//                                    ImportTaskFeignClient importTaskFeignClient,
//                                    DataAuthorityFeignClient dataAuthorityFeignClient,
//                                    StoreRecordFeignClient storeRecordFeignClient) {
//         super(redisService, importService, importTaskFeignClient, dataAuthorityFeignClient, storeRecordFeignClient);
//         this.importTaskFeignClient = importTaskFeignClient;
//     }
//
//     /**
//      * 导出资产模板
//      *
//      * @return excel
//      */
//     @Override
//     public ExcelWriter buildExcelWriter(Long standardId) {
//         // 获取 writer
//         ExcelWriter writer = ExcelUtil.getWriter(true);
//         // 获取 sheet
//         Sheet sheet = writer.getSheet();
//         // 获取 styleSet
//         StyleSet styleSet = writer.getStyleSet();
//         // 设置边框
//         styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
//         styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);
//         Workbook workbook = sheet.getWorkbook();
//         DataFormat format = workbook.createDataFormat();
//         CellStyle style = workbook.createCellStyle();
//         style.setDataFormat(format.getFormat("@"));
//
//         // 设置表头的cellStyle
//         CellStyle redHeadStyle = writer.createCellStyle();
//         redHeadStyle.setAlignment(HorizontalAlignment.CENTER);
//         redHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//         redHeadStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
//         redHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//         redHeadStyle.setBorderBottom(BorderStyle.THIN);
//         redHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
//         redHeadStyle.setBorderTop(BorderStyle.THIN);
//         redHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         redHeadStyle.setBorderLeft(BorderStyle.THIN);
//         redHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         redHeadStyle.setBorderRight(BorderStyle.THIN);
//         redHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         Font red = writer.createFont();
//         red.setFontHeightInPoints((short) 13);
//         red.setColor(IndexedColors.RED.getIndex());
//         redHeadStyle.setFont(red);
//
//         CellStyle blackHeadStyle = writer.createCellStyle();
//         blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
//         blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//         blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
//         blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//         blackHeadStyle.setBorderBottom(BorderStyle.THIN);
//         blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
//         blackHeadStyle.setBorderTop(BorderStyle.THIN);
//         blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         blackHeadStyle.setBorderLeft(BorderStyle.THIN);
//         blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         blackHeadStyle.setBorderRight(BorderStyle.THIN);
//         blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         Font black = writer.createFont();
//         black.setFontHeightInPoints((short) 13);
//         black.setColor(IndexedColors.BLACK.getIndex());
//         blackHeadStyle.setFont(black);
//
//         // 写入文件注意事项
//         Row attentionRow = writer.getOrCreateRow(0);
//         attentionRow.setHeight((short) 3000);
//         Cell attentionCell = attentionRow.createCell(0);
//         CellStyle attention = writer.createCellStyle();
//         attention.setVerticalAlignment(VerticalAlignment.CENTER);
//         attention.setWrapText(true);
//         attention.setFont(black);
//         attentionCell.setCellStyle(attention);
//         String text = "注意事项:\n" +
//                 "1、红色字体为必填项，黑色字体为选填项\n" +
//                 "2、输入员工时，需填写员工工号，如：001；\n" +
//                 "3、资产编码填写不可重复，当需要系统自动根据编码规则生成时，资产编码请填写以下中文：系统自动生成；\n" +
//                 "4、若资产是在用/借用状态，请务必填写使用组织；若填写了使用人，使用组织必填；\n" +
//                 "5、多选字段，多个字段以中文或英文逗号隔开；\n" +
//                 "6、单次最多可导入5000条数据，文件不可超过1MB；\n" +
//                 "7、具体字段填写要求可参考表头单元格批注信息。";
//         XSSFRichTextString xssfRichTextString = new XSSFRichTextString(text);
//         Font titleRed = writer.createFont();
//         titleRed.setFontHeightInPoints((short) 13);
//         titleRed.setColor(IndexedColors.RED.getIndex());
//         xssfRichTextString.applyFont(0, 91, black);
//         xssfRichTextString.applyFont(91, 97, titleRed);
//         xssfRichTextString.applyFont(97, text.length() - 1, black);
//         attentionCell.setCellValue(xssfRichTextString);
//         writer.merge(26);
//
//         // 查询录入标准数据项
//         FormVO formVO = getAssetFormFields();
//         List<FormFieldCO> formFields = formVO.getFormFields();
//         // 查询录入的标准品扩展数据
//         if (ObjectUtil.isNotNull(standardId)) {
//             List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
//             standardExtField = standardExtField.stream()
//                     .filter(attr -> !attr.isHidden())
//                     .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
//                             .contains(attr.getFieldType())).collect(Collectors.toList());
//             formFields.addAll(standardExtField);
//         }
//
//         Row headRow = writer.getOrCreateRow(1);
//         int realCol = 0;
//         for (FormFieldCO attr : formFields) {
//             // ============================== 设置表头 ===================================
//             // 写入表头
//             Cell cell = headRow.createCell(realCol);
//             cell.setCellStyle(attr.requiredProps() ? redHeadStyle : blackHeadStyle);
//             cell.setCellValue(attr.getFieldName());
//
//             String tplComment = AssetExcelTplEnum.getWeixinTplComment(attr.getFieldCode());
//             if (StrUtil.isNotEmpty(tplComment)) {
//                 Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
//                 Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6));
//                 comment.setString(new XSSFRichTextString(tplComment));
//                 cell.setCellComment(comment);
//             }
//
//             // 调整每一列宽度
//             sheet.autoSizeColumn((short) realCol);
//             // 解决自动设置列宽中文失效的问题
//             sheet.setColumnWidth(realCol, sheet.getColumnWidth(realCol) * 17 / 10);
//             sheet.setDefaultColumnStyle(realCol, style);
//
//             if (FormFieldCO.SELECT_DROPDOWN.equals(attr.getFieldType())) {
//                 JSONArray values = attr.getFieldProps().getJSONArray("values");
//                 String[] selected = values.toArray(new String[]{});
//                 writer.addSelect(new CellRangeAddressList(2, 5000, realCol, realCol), selected);
//             }
//             realCol += 1;
//         }
//         // 写入其他sheet
//         buildSheet(writer, formFields);
//         return writer;
//     }
//
//     private FormVO getAssetFormFields() {
//         // 查询录入标准数据项
//         FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
//         List<FormFieldCO> formFields = formVO.getFormFields();
//         formFields = formFields.stream()
//                 .filter(attr -> !attr.isHidden())
//                 .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
//                         .contains(attr.getFieldType())).collect(Collectors.toList());
//         List<FormFieldCO> wxFormFields = new ArrayList<>();
//         formFields.forEach(attr -> {
//             if (FormFieldCO.YZC_ORG.equals(attr.getFieldType()) || FormFieldCO.YZC_EMP.equals(attr.getFieldType())) {
//                 wxFormFields.add(attr);
//                 // 写入名称
//                 FormFieldCO name = new FormFieldCO();
//                 name.setFieldCode(attr.getFieldCode() + "名称");
//                 name.setFieldName(attr.getFieldName() + "名称");
//                 name.setFieldProps(new JSONObject());
//                 name.setFieldType(FormFieldCO.TEXT_INPUT);
//                 wxFormFields.add(name);
//             } else {
//                 wxFormFields.add(attr);
//             }
//         });
//         formVO.setFormFields(wxFormFields);
//         return formVO;
//     }
//
//     @Override
//     public void parseExcelStream(InputStream stream, String fileName, Long fileSize,
//                                  Long companyId, Long standardId) {
//         // 导入对象
//         ImportInfo importInfo = new ImportInfo();
//         importInfo.setFileName(fileName);
//         importInfo.setFileSize(fileSize);
//         importInfo.setCompanyId(companyId);
//         importInfo.setStandardId(standardId);
//
//         List<FormFieldCO> formFields = getStandardAttr(standardId);
//         // 扩展特殊字段
//         List<FormFieldCO> wxFormFields = new ArrayList<>();
//         formFields.forEach(attr -> {
//             if (FormFieldCO.YZC_ORG.equals(attr.getFieldType()) || FormFieldCO.YZC_EMP.equals(attr.getFieldType())) {
//                 wxFormFields.add(attr);
//                 // 写入名称
//                 FormFieldCO name = new FormFieldCO();
//                 name.setFieldCode(attr.getFieldCode() + "名称");
//                 name.setFieldName(attr.getFieldName() + "名称");
//                 name.setFieldProps(new JSONObject());
//                 name.setFieldType(EXCLUDE_FIELD);
//                 wxFormFields.add(name);
//             } else {
//                 wxFormFields.add(attr);
//             }
//         });
//         Map<String, List<FormFieldCO>> assetAttrMap = wxFormFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));
//
//         // 解析的模板导入
//         ExcelReader reader = ExcelUtil.getReader(stream);
//         List<List<Object>> read = reader.read(1);
//         if (CollUtil.isEmpty(read)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
//         }
//         // =============================  读取Excel表头并校验 ================================
//         List<Object> header = ExcelUtils.clearEmpty(read.get(0));
//         int noNullHeaderSize = Convert.toInt(header.stream().filter(it -> StrUtil.isNotBlank(Convert.toStr(it))).count());
//         // 表头字段映射
//         Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
//         // 匹配动态属性和表头数据是否一致，防止动态属性被修改
//         if (wxFormFields.size() != noNullHeaderSize) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
//         } else {
//             for (int i = 0; i < header.size(); i++) {
//                 String headName = Convert.toStr(header.get(i), "").trim();
//                 if (StrUtil.isBlank(Convert.toStr(headName))) {
//                     continue;
//                 }
//                 if (!assetAttrMap.containsKey(headName)) {
//                     throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
//                 }
//                 List<FormFieldCO> formFieldCOList = assetAttrMap.get(headName);
//                 if (CollUtil.isNotEmpty(formFieldCOList)) {
//                     FormFieldCO formFieldCO = formFieldCOList.get(0);
//                     headerMapping.put(i, formFieldCO);
//                     formFieldCOList.remove(0);
//                     assetAttrMap.put(headName, formFieldCOList);
//                 }
//             }
//         }
//         importInfo.setHeaderMapping(headerMapping);
//         importInfo.setRead(read);
//         importExcel(importInfo, true);
//     }
//
//     @Override
//     public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
//         ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
//         if (importTaskDto == null) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "导入任务" + taskId + "不存在");
//         }
//         if (CollUtil.isEmpty(sheetModels)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
//         }
//
//         // 导入对象
//         ImportInfo importInfo = new ImportInfo();
//         importInfo.setTaskId(taskId);
//         importInfo.setFileName("资产在线编辑保存");
//         importInfo.setFileSize(0L);
//         importInfo.setCompanyId(companyId);
//         importInfo.setRead(sheetModels);
//
//         ImportTaskExtInfoDto extInfo = importTaskDto.getExtInfo();
//         Long standardId = null;
//         if (extInfo != null && extInfo.getStandardId() != null) {
//             standardId = extInfo.getStandardId();
//         }
//         importInfo.setStandardId(standardId);
//
//         List<FormFieldCO> formFields = getStandardAttr(standardId);
//         // 扩展特殊字段
//         List<FormFieldCO> wxFormFields = new ArrayList<>();
//         formFields.forEach(attr -> {
//             if (FormFieldCO.YZC_ORG.equals(attr.getFieldType()) || FormFieldCO.YZC_EMP.equals(attr.getFieldType())) {
//                 wxFormFields.add(attr);
//                 // 写入名称
//                 FormFieldCO name = new FormFieldCO();
//                 name.setFieldCode(attr.getFieldCode() + "名称");
//                 name.setFieldName(attr.getFieldName() + "名称");
//                 name.setFieldProps(new JSONObject());
//                 name.setFieldType(EXCLUDE_FIELD);
//                 wxFormFields.add(name);
//             } else {
//                 wxFormFields.add(attr);
//             }
//         });
//         Map<String, List<FormFieldCO>> assetAttrMap = wxFormFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));
//
//         // 必填项code
//         Set<String> requiredCodes = formFields.stream().filter(attr -> BooleanUtil.isTrue(attr.requiredProps()))
//                 .map(FormFieldCO::getFieldCode).collect(Collectors.toSet());
//
//         // =============================  读取Excel表头并校验 ================================
//         List<Object> header = sheetModels.get(0);
//         // 表头字段映射
//         Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
//         // 匹配动态属性和表头数据是否一致，防止动态属性被修改
//         for (int i = 0; i < header.size(); i++) {
//             String headName = Convert.toStr(header.get(i), "").trim();
//             if (StrUtil.isBlank(Convert.toStr(headName))) {
//                 continue;
//             }
//             if (!assetAttrMap.containsKey(headName)) {
//                 throw new BusinessException(MeansResultCode.ASSET_IMPORT_TPL_CHANGE);
//             }
//             List<FormFieldCO> bizFormAssetConfigs = assetAttrMap.get(headName);
//             if (CollUtil.isNotEmpty(bizFormAssetConfigs)) {
//                 FormFieldCO bizFormAssetConfig = bizFormAssetConfigs.get(0);
//                 headerMapping.put(i, bizFormAssetConfig);
//                 requiredCodes.remove(bizFormAssetConfig.getFieldCode());
//                 bizFormAssetConfigs.remove(0);
//                 assetAttrMap.put(headName, bizFormAssetConfigs);
//             }
//         }
//
//         if (requiredCodes.size() > 0) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "存在必填项字段未设置");
//         }
//
//         importInfo.setHeaderMapping(headerMapping);
//         // 校验表头数据
//         this.importExcel(importInfo, false);
//         return true;
//     }
//
// }
