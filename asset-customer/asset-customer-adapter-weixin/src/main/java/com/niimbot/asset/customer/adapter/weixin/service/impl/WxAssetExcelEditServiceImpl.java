package com.niimbot.asset.customer.adapter.weixin.service.impl;

/**
 * <AUTHOR>
 * @date 2023/7/24 16:17
 */
// @Slf4j
// @Service("wxAssetExcelEditServiceImpl")
// public class WxAssetExcelEditServiceImpl extends AssetExcelEditServiceImpl {
//
//     private final ImportTaskFeignClient importTaskFeignClient;
//
//     public WxAssetExcelEditServiceImpl(RedisService redisService,
//                                        ImportService importService,
//                                        ImportTaskFeignClient importTaskFeignClient) {
//         super(redisService, importService, importTaskFeignClient);
//         this.importTaskFeignClient = importTaskFeignClient;
//     }
//
//     @Override
//     public ExcelWriter buildEditExcelWriter() {
//         // 获取 writer
//         ExcelWriter writer = ExcelUtil.getWriter(true);
//         // 获取 sheet
//         Sheet sheet = writer.getSheet();
//         // 获取 styleSet
//         StyleSet styleSet = writer.getStyleSet();
//         // 设置边框
//         styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
//         styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);
//         Workbook workbook = sheet.getWorkbook();
//         DataFormat format = workbook.createDataFormat();
//         CellStyle style = workbook.createCellStyle();
//         style.setDataFormat(format.getFormat("@"));
//
//         // 设置表头的cellStyle
//         CellStyle redHeadStyle = writer.createCellStyle();
//         redHeadStyle.setAlignment(HorizontalAlignment.CENTER);
//         redHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//         redHeadStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
//         redHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//         redHeadStyle.setBorderBottom(BorderStyle.THIN);
//         redHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
//         redHeadStyle.setBorderTop(BorderStyle.THIN);
//         redHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         redHeadStyle.setBorderLeft(BorderStyle.THIN);
//         redHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         redHeadStyle.setBorderRight(BorderStyle.THIN);
//         redHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         Font red = writer.createFont();
//         red.setFontHeightInPoints((short) 13);
//         red.setColor(IndexedColors.RED.getIndex());
//         redHeadStyle.setFont(red);
//
//         CellStyle blackHeadStyle = writer.createCellStyle();
//         blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
//         blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//         blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
//         blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//         blackHeadStyle.setBorderBottom(BorderStyle.THIN);
//         blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
//         blackHeadStyle.setBorderTop(BorderStyle.THIN);
//         blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         blackHeadStyle.setBorderLeft(BorderStyle.THIN);
//         blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         blackHeadStyle.setBorderRight(BorderStyle.THIN);
//         blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
//         Font black = writer.createFont();
//         black.setFontHeightInPoints((short) 13);
//         black.setColor(IndexedColors.BLACK.getIndex());
//         blackHeadStyle.setFont(black);
//
//         // 写入文件注意事项
//         Row attentionRow = writer.getOrCreateRow(0);
//         attentionRow.setHeight((short) 2400);
//         Cell attentionCell = attentionRow.createCell(0);
//         CellStyle attention = writer.createCellStyle();
//         attention.setVerticalAlignment(VerticalAlignment.CENTER);
//         attention.setWrapText(true);
//         attention.setFont(black);
//         attentionCell.setCellStyle(attention);
//         String text = "注意事项:\n" +
//                 "1、更新时资产编码是必填字段，资产编码本身不支持修改，请确认填写的编码已经在系统中；\n" +
//                 "2、导出表格的字段全部支持编辑。其他字段，如需更新请前往系统--资产--资产变更，提交资产变更单进行编辑；\n" +
//                 "3、不需要更新的字段，不填写则视为不更新；\n" +
//                 "4、如需删除某个字段的值，则请填写：删除 这两个字；\n" +
//                 "5、单次最多可导入5000条数据，文件不可超过1MB；";
//         XSSFRichTextString xssfRichTextString = new XSSFRichTextString(text);
//         Font titleRed = writer.createFont();
//         titleRed.setFontHeightInPoints((short) 13);
//         titleRed.setColor(IndexedColors.RED.getIndex());
//         xssfRichTextString.applyFont(0, 143, black);
//         xssfRichTextString.applyFont(143, 145, titleRed);
//         xssfRichTextString.applyFont(145, text.length() - 1, black);
//         attentionCell.setCellValue(xssfRichTextString);
//         writer.merge(26);
//
//         FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
//         JSONObject formProps = formVO.getFormProps();
//         List<String> editCodes = new ArrayList<>();
//         if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
//             editCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
//                     .toJavaList(EditFieldRules.class)
//                     .stream()
//                     .filter(editFieldRules -> editFieldRules.getValue() == 1)
//                     .map(EditFieldRules::getFieldCode)
//                     .collect(toList());
//         }
//
//         // 可编辑的属性
//         List<FormFieldCO> formFields = new ArrayList<>();
//         for (FormFieldCO f : formVO.getFormFields()) {
//             if (FormFieldCO.YZC_ASSET_SERIALNO.equals(f.getFieldType())) {
//                 formFields.add(0, f);
//             }
//             if (editCodes.contains(f.getFieldCode())
//                     && !f.isHidden()
//                     && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
//                     .contains(f.getFieldType())) {
//                 formFields.add(f);
//             }
//         }
//
//         if (CollUtil.isEmpty(formFields)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "未配置可编辑字段");
//         }
//
//         // 扩展特殊字段
//         List<FormFieldCO> wxFormFields = new ArrayList<>();
//         formFields.forEach(attr -> {
//             if (FormFieldCO.YZC_ORG.equals(attr.getFieldType()) || FormFieldCO.YZC_EMP.equals(attr.getFieldType())) {
//                 wxFormFields.add(attr);
//                 // 写入名称
//                 FormFieldCO name = new FormFieldCO();
//                 name.setFieldCode(attr.getFieldCode() + "名称");
//                 name.setFieldName(attr.getFieldName() + "名称");
//                 name.setFieldProps(new JSONObject());
//                 name.setFieldType(EXCLUDE_FIELD);
//                 wxFormFields.add(name);
//             } else {
//                 wxFormFields.add(attr);
//             }
//         });
//
//         Row headRow = writer.getOrCreateRow(1);
//         int realCol = 0;
//         for (FormFieldCO attr : wxFormFields) {
//             // ============================== 设置表头 ===================================
//             // 写入表头
//             Cell cell = headRow.createCell(realCol);
//             cell.setCellStyle(attr.getFieldType().equals(AssetConstant.ED_YZC_ASSET_SERIALNO) ? redHeadStyle : blackHeadStyle);
//             cell.setCellValue(attr.getFieldName());
//
//             String tplComment = AssetExcelTplEnum.getEditTplComment(attr.getFieldCode());
//             if (StrUtil.isNotEmpty(tplComment)) {
//                 Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
//                 Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6));
//                 comment.setString(new XSSFRichTextString(tplComment));
//                 cell.setCellComment(comment);
//             }
//
//             // 调整每一列宽度
//             sheet.autoSizeColumn((short) realCol);
//             // 解决自动设置列宽中文失效的问题
//             sheet.setColumnWidth(realCol, sheet.getColumnWidth(realCol) * 17 / 10);
//             sheet.setDefaultColumnStyle(realCol, style);
//
//             if (FormFieldCO.SELECT_DROPDOWN.equals(attr.getFieldType())) {
//                 JSONArray values = attr.getFieldProps().getJSONArray("values");
//                 String[] selected = values.toArray(new String[]{});
//                 writer.addSelect(new CellRangeAddressList(2, 5000, realCol, realCol), selected);
//             }
//             realCol += 1;
//         }
//         // 写入其他sheet
//         return writer;
//     }
//
//     @Override
//     public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
//         // 导入对象
//         ImportInfo importInfo = new ImportInfo();
//         importInfo.setFileName(fileName);
//         importInfo.setFileSize(fileSize);
//         importInfo.setCompanyId(companyId);
//
//         // 解析的模板导入
//         ExcelReader reader = ExcelUtil.getReader(stream);
//         List<List<Object>> read = reader.read(1);
//         if (CollUtil.isEmpty(read)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
//         }
//         // =============================  读取Excel表头并校验 ================================
//         List<Object> header = ExcelUtils.clearEmpty(read.get(0));
//         if (CollUtil.isEmpty(header)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到数据");
//         }
//
//         FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
//         JSONObject formProps = formVO.getFormProps();
//         List<String> editCodes = new ArrayList<>();
//         if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
//             editCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
//                     .toJavaList(EditFieldRules.class)
//                     .stream()
//                     .filter(editFieldRules -> editFieldRules.getValue() == 1)
//                     .map(EditFieldRules::getFieldCode)
//                     .collect(toList());
//         }
//
//         // 可编辑的属性
//         FormFieldCO assetSerialNoCode = new FormFieldCO();
//         List<FormFieldCO> formFields = new ArrayList<>();
//         List<String> allFieldName = new ArrayList<>();
//         for (FormFieldCO f : formVO.getFormFields()) {
//             allFieldName.add(f.getFieldName());
//             if (FormFieldCO.YZC_ASSET_SERIALNO.equals(f.getFieldType())) {
//                 formFields.add(f);
//                 assetSerialNoCode = f;
//             }
//             if (editCodes.contains(f.getFieldCode())
//                     && !f.isHidden()
//                     && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
//                     .contains(f.getFieldType())) {
//                 formFields.add(f);
//             }
//         }
//
//         // 扩展特殊字段
//         List<FormFieldCO> wxFormFields = new ArrayList<>();
//         formFields.forEach(attr -> {
//             if (FormFieldCO.YZC_ORG.equals(attr.getFieldType()) || FormFieldCO.YZC_EMP.equals(attr.getFieldType())) {
//                 wxFormFields.add(attr);
//                 // 写入名称
//                 FormFieldCO name = new FormFieldCO();
//                 name.setFieldCode(attr.getFieldCode() + "名称");
//                 name.setFieldName(attr.getFieldName() + "名称");
//                 name.setFieldProps(new JSONObject());
//                 name.setFieldType(EXCLUDE_FIELD);
//                 wxFormFields.add(name);
//             } else {
//                 wxFormFields.add(attr);
//             }
//         });
//
//         if (CollUtil.isEmpty(wxFormFields)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "未配置可编辑字段");
//         }
//         Map<String, List<FormFieldCO>> assetAttrMap = wxFormFields.stream()
//                 .collect(Collectors.groupingBy(FormFieldCO::getFieldName));
//
//         // 表头字段映射
//         formFieldMap.set(MapUtil.of("asset", new ArrayList<>()));
//         Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
//         List<String> noUpdate = new ArrayList<>();
//         for (int i = 0; i < header.size(); i++) {
//             String headName = Convert.toStr(header.get(i), "").trim();
//             if (StrUtil.isBlank(Convert.toStr(headName))) {
//                 continue;
//             }
//             if (assetAttrMap.containsKey(headName)) {
//                 List<FormFieldCO> formFieldCOList = assetAttrMap.get(headName);
//                 if (CollUtil.isNotEmpty(formFieldCOList)) {
//                     FormFieldCO formFieldCO = formFieldCOList.get(0);
//                     headerMapping.put(i, formFieldCO);
//                     formFieldCOList.remove(0);
//                     assetAttrMap.put(headName, formFieldCOList);
//                     formFieldMap.get().get("asset").add(formFieldCO);
//                 }
//             } else if (allFieldName.contains(headName)) {
//                 noUpdate.add(headName);
//             }
//         }
//         if (CollUtil.isNotEmpty(noUpdate)) {
//             String join = String.join("，", noUpdate);
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "当前表格的以下字段不支持更新：" + join);
//         }
//
//         if (CollUtil.isEmpty(headerMapping)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到可编辑属性");
//         }
//         FormFieldCO finalAssetSerialNoCode = assetSerialNoCode;
//         boolean find = headerMapping.values().stream().anyMatch(f -> f.getFieldCode().equals(finalAssetSerialNoCode.getFieldCode()));
//         if (!find) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到必填字段" + finalAssetSerialNoCode.getFieldName());
//         }
//         importInfo.setHeaderMapping(headerMapping);
//         importInfo.setRead(read);
//         this.importExcel(importInfo, true);
//     }
//
//     @Override
//     public void importEditErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
//         ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
//         if (importTaskDto == null) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "导入任务" + taskId + "不存在");
//         }
//         if (CollUtil.isEmpty(sheetModels)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
//         }
//
//         // =============================  读取Excel表头并校验 ================================
//         List<Object> header = sheetModels.get(0);
//         if (CollUtil.isEmpty(header)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到数据");
//         }
//
//         // 导入对象
//         ImportInfo importInfo = new ImportInfo();
//         importInfo.setTaskId(taskId);
//         importInfo.setFileName("编辑资产在线编辑保存");
//         importInfo.setFileSize(0L);
//         importInfo.setCompanyId(companyId);
//         importInfo.setRead(sheetModels);
//
//         FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
//         JSONObject formProps = formVO.getFormProps();
//         List<String> editCodes = new ArrayList<>();
//         if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
//             editCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
//                     .toJavaList(EditFieldRules.class)
//                     .stream()
//                     .filter(editFieldRules -> editFieldRules.getValue() == 1)
//                     .map(EditFieldRules::getFieldCode)
//                     .collect(toList());
//         }
//
//         // 可编辑的属性
//         FormFieldCO assetSerialNoCode = new FormFieldCO();
//         List<FormFieldCO> formFields = new ArrayList<>();
//         List<String> allFieldName = new ArrayList<>();
//         for (FormFieldCO f : formVO.getFormFields()) {
//             allFieldName.add(f.getFieldName());
//             if (FormFieldCO.YZC_ASSET_SERIALNO.equals(f.getFieldType())) {
//                 formFields.add(f);
//                 assetSerialNoCode = f;
//             }
//             if (editCodes.contains(f.getFieldCode())
//                     && !f.isHidden()
//                     && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
//                     .contains(f.getFieldType())) {
//                 formFields.add(f);
//             }
//         }
//
//         // 扩展特殊字段
//         List<FormFieldCO> wxFormFields = new ArrayList<>();
//         formFields.forEach(attr -> {
//             if (FormFieldCO.YZC_ORG.equals(attr.getFieldType()) || FormFieldCO.YZC_EMP.equals(attr.getFieldType())) {
//                 wxFormFields.add(attr);
//                 // 写入名称
//                 FormFieldCO name = new FormFieldCO();
//                 name.setFieldCode(attr.getFieldCode() + "名称");
//                 name.setFieldName(attr.getFieldName() + "名称");
//                 name.setFieldProps(new JSONObject());
//                 name.setFieldType(EXCLUDE_FIELD);
//                 wxFormFields.add(name);
//             } else {
//                 wxFormFields.add(attr);
//             }
//         });
//
//         if (CollUtil.isEmpty(wxFormFields)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "未配置可编辑字段");
//         }
//         Map<String, List<FormFieldCO>> assetAttrMap = wxFormFields.stream()
//                 .collect(Collectors.groupingBy(FormFieldCO::getFieldName));
//
//         // 表头字段映射
//         formFieldMap.set(MapUtil.of("asset", new ArrayList<>()));
//         Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
//         List<String> noUpdate = new ArrayList<>();
//         for (int i = 0; i < header.size(); i++) {
//             String headName = Convert.toStr(header.get(i), "").trim();
//             if (StrUtil.isBlank(Convert.toStr(headName))) {
//                 continue;
//             }
//             if (assetAttrMap.containsKey(headName)) {
//                 List<FormFieldCO> formFieldCOList = assetAttrMap.get(headName);
//                 if (CollUtil.isNotEmpty(formFieldCOList)) {
//                     FormFieldCO formFieldCO = formFieldCOList.get(0);
//                     headerMapping.put(i, formFieldCO);
//                     formFieldCOList.remove(0);
//                     assetAttrMap.put(headName, formFieldCOList);
//                     formFieldMap.get().get("asset").add(formFieldCO);
//                 }
//             } else if (allFieldName.contains(headName)) {
//                 noUpdate.add(headName);
//             }
//         }
//         if (CollUtil.isNotEmpty(noUpdate)) {
//             String join = String.join("，", noUpdate);
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "当前表格的以下字段不支持更新：" + join);
//         }
//
//         if (CollUtil.isEmpty(headerMapping)) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到可编辑属性");
//         }
//         FormFieldCO finalAssetSerialNoCode = assetSerialNoCode;
//         boolean find = headerMapping.values().stream().anyMatch(f -> f.getFieldCode().equals(finalAssetSerialNoCode.getFieldCode()));
//         if (!find) {
//             throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到必填字段" + finalAssetSerialNoCode.getFieldName());
//         }
//
//         importInfo.setHeaderMapping(headerMapping);
//         // 校验表头数据
//         this.importExcel(importInfo, false);
//     }
//
// }
