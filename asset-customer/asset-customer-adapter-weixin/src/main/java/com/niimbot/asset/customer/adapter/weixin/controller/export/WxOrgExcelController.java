package com.niimbot.asset.customer.adapter.weixin.controller.export;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.customer.adapter.weixin.utils.WxExcels;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.impl.OrgServiceImpl;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.Auditable;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgExportDto;
import com.niimbot.system.OrgQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "【微信】组织导出")
@ResultController
@RequestMapping("/api/weixin/org")
@Validated
@RequiredArgsConstructor
public class WxOrgExcelController {

    private final OrgFeignClient orgFeignClient;

    private final WxOpenApiService openApiService;

    @ApiOperation(value = "【PC】导出组织模板")
    @GetMapping(value = "/exportTemplate")
    public String exportTemplate() {
        try {
            ClassPathResource templateSource = new ClassPathResource("/excelTemplate/org_template.xlsx");
            InputStream inputStream = templateSource.getInputStream();
            String fileName = "组织导入模板.xlsx";
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            writer.setSheet("所属组织");
            List<OrgDto> orgDtoList = orgFeignClient.list(new OrgQueryDto());
            LinkedHashMap<String, String> areaHead = ExcelUtils.buildExcelHead(OrgServiceImpl.OrgExport.class);
            areaHead.forEach(writer::addHeaderAlias);
            List<OrgServiceImpl.OrgExport> orgData = orgDtoList.stream().map(org ->
                            new OrgServiceImpl.OrgExport(org.getOrgCode(),
                                    org.getOrgType() == 1 ? "公司" : "部门", org.getOrgName()))
                    .collect(Collectors.toList());
            writer.setOnlyAlias(true);
            writer.write(orgData);
            writer.autoSizeColumnAll();
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName, writer);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "导出")
    @GetMapping("/export")
    @AuditLog(Auditable.Action.EXP_ORG_LT)
    public String export(OrgQueryDto queryDto) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(OrgExportDto.class);
            // 查询数据
            List<OrgExportDto> excel = orgFeignClient.getExcelData(queryDto);
            String fileName = "组织-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            ExcelWriter writer = WxExcels.export(new ExcelExportDto(headerData, excel));
            return openApiService.excelTrans(LoginUserThreadLocal.getCompanyId(), fileName + ".xlsx", writer);
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
