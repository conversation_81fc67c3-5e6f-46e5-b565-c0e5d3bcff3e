package com.niimbot.asset.customer.adapter.weixin.model;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * created by chen.y on 2021/8/24 10:27
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AuthCodeLoginRequest", description = "第三方企业应用免登请求类")
public class AuthCodeLoginRequest implements Serializable {

    @ApiModelProperty("授权码")
    @NotEmpty(message = "授权码不能为空")
    private String authCode;

}
