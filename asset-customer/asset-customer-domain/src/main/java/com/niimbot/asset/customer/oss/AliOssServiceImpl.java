package com.niimbot.asset.customer.oss;

import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.OssUploadUtils;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Profile({Edition.SAAS, Edition.DING})
public class AliOssServiceImpl implements FileUploadService {
    @Override
    public String putFile(String key, String filePath) {
        return OssUploadUtils.putFile(key, filePath);
    }
}
