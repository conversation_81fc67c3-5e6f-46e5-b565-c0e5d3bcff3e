package com.niimbot.asset.customer.oss;

import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.support.Edition;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;

import cn.hutool.core.io.FileUtil;

/**
 * <AUTHOR>
 * @date 2024/5/22 17:12
 */
@Component
@Profile({Edition.LOCAL})
public class LocalOssServiceImpl implements FileUploadService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Override
    public String putFile(String key, String filePath) {
        File file = FileUtil.file(fileUploadConfig.getPath(), key);
        FileUtil.copy(filePath, file.getAbsolutePath(), true);

        return FileUtil.file(BaseConstant.RESOURCE_PREFIX, key).getPath().replace("\\", "/");
    }

}