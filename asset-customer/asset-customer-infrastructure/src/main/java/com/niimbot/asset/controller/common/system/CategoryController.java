package com.niimbot.asset.controller.common.system;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.TreeUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.CategoryService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.CategoryFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryExportDto;
import com.niimbot.means.CategoryQueryDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/11/26 14:13
 */
@Slf4j
@Api(tags = "资产分类管理")
@ResultController
@RequestMapping("api/common/category")
public class CategoryController {

    private final CategoryFeignClient categoryFeignClient;

    private final CategoryService categoryService;

    private final RedisService redisService;

    @Autowired
    public CategoryController(CategoryFeignClient categoryFeignClient,
                              CategoryService categoryService,
                              RedisService redisService) {
        this.categoryFeignClient = categoryFeignClient;
        this.categoryService = categoryService;
        this.redisService = redisService;
    }

    @ApiOperation(value = "通过Id数据查询")
    @GetMapping(value = "/{categoryId}")
    public CategoryDto getInfo(@PathVariable("categoryId") Long categoryId) {
        return categoryFeignClient.getInfo(categoryId);
    }

    @ApiOperation(value = "新增资产分类数据")
    @RepeatSubmit
    @PostMapping()
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    @AuditLog(Auditable.Action.ADD_MEANS_CATE)
    public CategoryDto add(@RequestBody @Validated(value = {Insert.class}) CategoryDto categoryDto) {
        return categoryFeignClient.add(categoryDto);
    }

    @ApiOperation(value = "编辑资产分类数据")
    @PutMapping()
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_MEANS_CATE)
    public CategoryDto edit(@RequestBody @Validated(value = {Update.class}) CategoryDto categoryDto) {
        return categoryFeignClient.edit(categoryDto);
    }

    @ApiOperation(value = "排序")
    @PutMapping("/sort")
    public Boolean sort(@RequestBody List<Long> categoryIds) {
        return categoryFeignClient.sort(categoryIds);
    }

    @ApiOperation(value = "分类导出")
    @GetMapping("/export")
    public void assetReportPageExport(HttpServletResponse response) {
        try {
            List<CategoryDto> list = categoryFeignClient.list(new CategoryQueryDto());
            Map<Long, String> idToName = list.stream().collect(Collectors.toMap(CategoryDto::getId, CategoryDto::getCategoryName));
            Map<Long, String> idToCode = list.stream().collect(Collectors.toMap(CategoryDto::getId, CategoryDto::getCategoryCode));
            List<CategoryExportDto> excel = new ArrayList<>();

            for (CategoryDto category : list) {
                Long pid = category.getPid();
                CategoryExportDto categoryExportDto = new CategoryExportDto().setCategoryCode(category.getCategoryCode())
                        .setCategoryName(category.getCategoryName())
                        .setCategoryPidCode(idToCode.get(pid))
                        .setCategoryPidName(idToName.get(pid));
                excel.add(categoryExportDto);
            }
            LinkedHashMap<String, String> header = new LinkedHashMap<>();
            header.put("categoryCode", "分类编码");
            header.put("categoryName", "分类名称");
            header.put("categoryPidCode", "上级编码");
            header.put("categoryPidName", "上级分类");
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS_CATE));
            ExcelUtils.export(response, new ExcelExportDto(header, excel), "分类信息-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx");

        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.error("资产分类导出失败, {}", e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    @ApiOperation(value = "删除资产分类数据")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @AuditLog(Auditable.Action.DEL_MEANS_CATE)
    public List<CategoryDto> remove(@RequestBody List<Long> categoryIds) {
        return categoryFeignClient.delete(categoryIds);
    }

    @ApiOperation(value = "新增获取推荐分类编码")
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return categoryFeignClient.recommendCode();
    }

    @ApiOperation(value = "查询资产分类下拉树")
    @GetMapping("/select/tree")
    public List<Tree<String>> selectTree(@ApiParam(name = "kw", value = "名称")
                                         @RequestParam(value = "kw", required = false) String kw) {
        CategoryQueryDto queryDto = new CategoryQueryDto()
                .setKw(kw).setFilterPerm(true);
        if (StrUtil.isNotBlank(kw)) {
            queryDto.setBuildTree(false);
            List<CategoryDto> categoryDtos = categoryFeignClient.list(queryDto);
            return categoryDtos.stream().map(category -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(category.getId()));
                tree.setParentId(Convert.toStr(category.getPid()));
                tree.setName(category.getCategoryName());
                tree.setWeight(category.getSortNum());
                tree.putExtra("title", category.getCategoryName());
                tree.putExtra("value", Convert.toStr(category.getId()));
                tree.putExtra("code", category.getCategoryCode());
                tree.putExtra("level", category.getLevel());
                tree.putExtra("disabled", category.getDisabled());
                return tree;
            }).collect(Collectors.toList());
        } else {
            // 查询全部数据
            queryDto.setBuildTree(true);
            List<CategoryDto> categoryDtos = categoryFeignClient.list(queryDto);
            // 构建树结构
            return TreeUtils.build(categoryDtos, (category, tree) -> {
                tree.setId(Convert.toStr(category.getId()));
                tree.setParentId(Convert.toStr(category.getPid()));
                tree.setName(category.getCategoryName());
                tree.setWeight(category.getSortNum());
                tree.putExtra("title", category.getCategoryName());
                tree.putExtra("value", Convert.toStr(category.getId()));
                tree.putExtra("code", category.getCategoryCode());
                tree.putExtra("level", category.getLevel());
                tree.putExtra("disabled", category.getDisabled());
            });
        }
    }

    @ApiOperation(value = "查询资产分类下拉树-盘点专用")
    @GetMapping("/select/list")
    public List<CategoryDto> list() {
        // 查询全部数据
        CategoryQueryDto queryDto = new CategoryQueryDto()
                .setFilterPerm(true);
        return categoryFeignClient.list(queryDto);
    }

    @ApiOperation(value = "通过Ids查询资产分类数据")
    @PostMapping("/ids")
    public List<Map<String, ?>> listByIds(@RequestBody List<Long> cateIds) {
        if (CollUtil.isEmpty(cateIds)) {
            return ListUtil.empty();
        }
        return categoryFeignClient.listByIds(cateIds).stream().map(item ->
                ImmutableMap.of("id", item.getId(), "name", item.getCategoryName(), "code", item.getCategoryCode())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "查询资产分类树")
    @GetMapping("/tree")
    public List<Tree<String>> tree(@ApiParam(name = "kw", value = "名称")
                                   @RequestParam(value = "kw", required = false) String kw) {
        CategoryQueryDto queryDto = new CategoryQueryDto()
                .setKw(kw).setFilterPerm(false);
        if (StrUtil.isNotBlank(kw)) {
            queryDto.setBuildTree(false);
            List<CategoryDto> list = categoryFeignClient.list(queryDto);
            // 构建树结构
            return list.stream().map(category -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(category.getId()));
                tree.setParentId(Convert.toStr(category.getPid()));
                tree.setName(category.getCategoryName());
                tree.setWeight(category.getSortNum());
                tree.putExtra("title", category.getCategoryName());
                tree.putExtra("value", Convert.toStr(category.getId()));
                tree.putExtra("code", category.getCategoryCode());
                tree.putExtra("level", category.getLevel());
                tree.putExtra("disabled", category.getDisabled());
                return tree;
            }).collect(Collectors.toList());
        } else {
            // 查询全部数据
            queryDto.setBuildTree(true);
            List<CategoryDto> list = categoryFeignClient.list(queryDto);
            return TreeUtils.build(list, (category, tree) -> {
                tree.setId(Convert.toStr(category.getId()));
                tree.setParentId(Convert.toStr(category.getPid()));
                tree.setName(category.getCategoryName());
                tree.setWeight(category.getSortNum());
                tree.putExtra("title", category.getCategoryName());
                tree.putExtra("value", Convert.toStr(category.getId()));
                tree.putExtra("code", category.getCategoryCode());
                tree.putExtra("level", category.getLevel());
                tree.putExtra("disabled", category.getDisabled());
            });
        }
    }

    @ApiOperation(value = "【PC】导出资产分类模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        categoryService.exportTemplate(response);
    }

    @ApiOperation(value = "【PC】导入资产分类模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_CATEGORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_CATEGORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            categoryService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return categoryService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_CATEGORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_CATEGORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        categoryService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_CATEGORY, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.ASSET_CATEGORY));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
