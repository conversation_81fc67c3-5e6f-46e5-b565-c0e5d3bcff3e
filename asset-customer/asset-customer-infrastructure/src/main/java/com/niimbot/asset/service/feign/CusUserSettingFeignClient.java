package com.niimbot.asset.service.feign;

import com.niimbot.system.AsCusUserSettingDto;
import com.niimbot.system.AsToolboxDto;
import com.niimbot.system.AssetAdvertiseDto;
import com.niimbot.system.ToolboxGroupDto;
import com.niimbot.system.statistics.ToolboxStatisticsDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-12-08
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CusUserSettingFeignClient {

    /**
     * 新增或更新首页配置数据
     *
     * @param dto 对象
     * @return 结果
     */
    @PostMapping(value = "server/system/userSetting")
    Boolean saveOrUpdate(AsCusUserSettingDto dto);

    /**
     * 获取app用户工具箱
     *
     * @return 工具箱
     */
    @Deprecated
    @GetMapping(value = "server/system/userSetting/app/getUserToolBox")
    List<AsToolboxDto> getUserAppToolBox();

    @GetMapping(value = "server/system/userSetting/app/getUserToolBox/v2")
    List<ToolboxGroupDto> getUserAppToolBoxV2();

    /**
     * 获取pc用户工具箱
     *
     * @return 工具箱
     */
    @GetMapping(value = "server/system/userSetting/pc/getUserToolBox")
    List<AsToolboxDto> getUserPcToolBox();

    /**
     * 获取工作台
     * @return
     */
    @GetMapping(value = "server/system/userSetting/app/getWorkbench")
    List<ToolboxGroupDto> getWorkbench();

    /**
     * 更新首页布局数据
     *
     * @param dto 对象
     * @return 结果
     */
    @PutMapping(value = "server/system/userSetting/updatePcHome")
    Boolean updatePcHome(AsCusUserSettingDto dto);

    /**
     * 获取app用户工具箱
     *
     * @return 工具箱
     */
    @GetMapping(value = "server/system/userSetting/getPcHome")
    List<Map<String, Object>> getPcHome();

    /**
     * 更新极简模式
     *
     * @param settingDto
     * @return
     */
    @PutMapping(value = "server/system/userSetting/simplify")
    Boolean updateSimplify(AsCusUserSettingDto settingDto);

    @GetMapping(value = "server/system/userSetting")
    AsCusUserSettingDto getSetting();

    /**
     * 工具箱统计
     * @param toolboxStatisticsDto
     * @return
     */
    @PostMapping(value = "server/system/userSetting/toolbox/statistics")
    Boolean toolboxStatistics(@RequestBody ToolboxStatisticsDto toolboxStatisticsDto);

    /**
     * 最近使用菜单
     * @return
     */
    @GetMapping(value = "server/system/userSetting/toolbox/recentlyUsed")
    List<AsToolboxDto> recentlyUsed();

    @GetMapping(value = "server/system/userSetting/app/commonlyUsed")
    List<AsToolboxDto> commonlyUsed();

    /**
     * 首页广告
     * @param type 1-app个人中心 2-pc首页
     * @return
     */
    @GetMapping(value = "server/system/userSetting/advertise")
    List<AssetAdvertiseDto> advertiseUrl(@RequestParam("type") Integer type);

}
