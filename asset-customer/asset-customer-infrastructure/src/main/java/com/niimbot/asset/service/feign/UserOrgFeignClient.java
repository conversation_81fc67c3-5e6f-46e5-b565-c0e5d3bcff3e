package com.niimbot.asset.service.feign;

import com.niimbot.system.UserOrgDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 用户组织
 * <AUTHOR>
 * @date 2022/2/23 13:57
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface UserOrgFeignClient {

    @GetMapping(value = "server/system/userOrg/{userId}")
    List<UserOrgDto> getByUserId(@PathVariable("userId") Long userId);

    @PostMapping(value = "server/system/userOrg/getByOrgs")
    List<UserOrgDto> getByOrgs(@RequestBody List<Long> orgIds);
}
