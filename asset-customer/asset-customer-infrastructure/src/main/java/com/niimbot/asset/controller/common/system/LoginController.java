package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.annotation.LoginRecord;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.model.LoginSocial;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * APP登录模块
 *
 * <AUTHOR>
 * @since 2020/11/13 18:37
 */
@Slf4j
@Api(tags = {"登录接口"})
@ResultController
@RequestMapping("api/common")
public class LoginController {

    @Autowired
    private CusLoginService loginService;


    /**
     * 三方认证登录
     * @param loginBody 登录信息
     * @return token
     */
    @LoginRecord
    @PostMapping("/login/third")
    @ApiOperation(value = "三方认证登录", notes = "使用三方认证信息登录")
    public Map<String, Object> loginByThird(@Validated @RequestBody Map<String, Object> loginBody) {
        return loginService.loginByThird(loginBody);
    }



    /**
     * 社交账号绑定
     *
     * @param social
     * @return
     */
    @PostMapping("/login/socialBind")
    @ApiOperation(value = "社交账号绑定", notes = "社交账号绑定")
    @ResultMessage(value = "绑定成功")
    public Boolean socialBind(LoginUserDto userDto,
                              @RequestBody @Validated LoginSocial social) {
        if (log.isDebugEnabled()) {
            log.info("---------------------------------------------------------------");
            log.info("provider = {}, appId = {}, code = {}", social.getProvider(), social.getAppId(), social.getCode());
            log.info("---------------------------------------------------------------");
        }
        loginService.socialBind(userDto.getCusUser().getUnionId(), social.getProvider(),
                social.getAppId(), social.getCode());
        return true;
    }

    /**
     * 社交账号解绑
     *
     * @param provider 社交平台
     * @return token
     */
    @PostMapping("/login/socialUnbind/{provider}")
    @ApiOperation(value = "社交账号解绑", notes = "社交账号解绑")
    @ResultMessage(value = "解绑成功")
    public Boolean socialUnbind(LoginUserDto userDto, @PathVariable("provider") String provider) {
        loginService.socialUnbind(userDto.getCusUser().getUnionId(), provider);
        return true;
    }
}
