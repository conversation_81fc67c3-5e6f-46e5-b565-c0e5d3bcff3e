package com.niimbot.asset.service.feign;

import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.purchase.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 采购申请feign客户端
 *
 * <AUTHOR>
 * @date 2021/5/18 09:34
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PurchaseApplyFeignClient {
    /**
     * 设置审批流信息获取
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/purchase/apply/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(@RequestBody PurchaseApplyDto dto);

    /**
     * 创建采购申请
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/purchase/apply")
    Boolean create(@RequestBody PurchaseApplySubmitDto dto);

    /**
     * 采购申请详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/purchase/apply/{id}")
    PurchaseApplyDto getById(@PathVariable("id") Long id);

    /**
     * 采购申请分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/purchase/apply/page")
    PageUtils<PurchaseApplyDto> page(@RequestBody PurchaseOrderQueryDto query);

    /**
     * 采购申请列表查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/purchase/apply/list")
    List<PurchaseApplyDto> list(@RequestBody PurchaseOrderQueryDto query);

    /**
     * 采购申请分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/purchase/apply/page/select")
    PageUtils<PurchaseApplyDetailSelectDto> pageSelect(@RequestBody PurchaseOrderDetailQueryDto query);

    /**
     * 采购申请明细查询
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "server/purchase/apply/listDetails")
    List<PurchaseApplyDetailDto> listDetailsByOrderId(@RequestBody Collection<Long> ids);

    /**
     * 采购申请明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "server/purchase/apply/pageDetail")
    PageUtils<PurchaseApplyDetailDto> pageDetail(@SpringQueryMap PurchaseDetailPageQueryDto dto);

    /**
     * 入库中数量明细查询
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/purchase/apply/link/order")
    List<PurchaseLinkOrderAmountDto> listOrderAmount(@RequestBody PurchaseLinkOrderAmountQueryDto dto);
}
