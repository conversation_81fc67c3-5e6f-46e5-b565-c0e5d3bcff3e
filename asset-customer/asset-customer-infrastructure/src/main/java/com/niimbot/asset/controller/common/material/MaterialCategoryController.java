package com.niimbot.asset.controller.common.material;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialCategoryService;
import com.niimbot.asset.service.feign.MaterialCategoryFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialCategoryDto;
import com.niimbot.means.CategoryExportDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.ImportDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "【耗材】分类管理")
@ResultController
@RequestMapping("/api/common/material/category")
@Validated
public class MaterialCategoryController {

    private final MaterialCategoryFeignClient materialCategoryFeignClient;

    private final MaterialCategoryService categoryService;

    private final RedisService redisService;

    public MaterialCategoryController(MaterialCategoryFeignClient materialCategoryFeignClient,
                                      MaterialCategoryService categoryService,
                                      RedisService redisService) {
        this.materialCategoryFeignClient = materialCategoryFeignClient;
        this.categoryService = categoryService;
        this.redisService = redisService;
    }

    /**
     * 新增分类
     *
     * @param params 耗材管理接受类
     * @return true or false
     * <AUTHOR>
     */
    @ApiOperation(value = "新增耗材分类数据")
    @RepeatSubmit
    @PostMapping()
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    @AuditLog(Auditable.Action.ADD_MRL_CATE)
    public MaterialCategoryDto add(@RequestBody @Validated(Insert.class) MaterialCategoryDto params) {
        return new MaterialCategoryDto().setId(materialCategoryFeignClient.add(params));
    }

    /**
     * 根据ID删除分类
     *
     * @param id 分类ID
     * @return true or false
     */
    @ApiOperation(value = "删除单个耗材分类数据")
    @DeleteMapping(value = "/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean deleteById(@PathVariable Long id) {
        List<AuditableOperateResult> results = materialCategoryFeignClient.deleteById(id);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MRL_CATE, results));
        return CollUtil.isNotEmpty(results);
    }

    /**
     * 删除多个记录
     *
     * @param ids ids
     * @return true or false
     * <AUTHOR>
     */
    @ApiOperation(value = "删除多个耗材分类数据")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean deleteList(@RequestBody List<Long> ids) {
        List<AuditableOperateResult> results = materialCategoryFeignClient.deleteList(ids);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MRL_CATE, results));
        return CollUtil.isNotEmpty(results);
    }

    /**
     * 更新分类
     *
     * @param params 耗材分类DTO
     * @return true or false
     * <AUTHOR>
     */
    @ApiOperation("更新单个耗材分类数据")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_MRL_CATE)
    public Boolean updateOne(@RequestBody @Validated(Update.class) MaterialCategoryDto params) {
        return materialCategoryFeignClient.updateOne(params);
    }

    /**
     * 分类详情
     *
     * @param id ID
     * @return 耗材分类
     * <AUTHOR>
     */
    @ApiOperation("获取单个耗材分类数据")
    @GetMapping(value = "/{id}")
    public MaterialCategoryDto detail(@PathVariable Long id) {
        return materialCategoryFeignClient.detail(id);
    }

    /**
     * 获取全部数据
     *
     * @return {@link List <MaterialCategoryDto>}
     * <AUTHOR>
     */
    @ApiOperation("获取耗材分类树")
    @GetMapping(value = "/tree")
    public List<Tree<String>> tree(@ApiParam(name = "kw", value = "名称")
                                   @RequestParam(value = "kw", required = false) String kw) {
        List<MaterialCategoryDto> all = materialCategoryFeignClient.all(kw);
        if (StrUtil.isNotBlank(kw)) {
            return all.stream().map(categoryDto -> {
                Tree<String> treeNode = new Tree<>();
                treeNode.setId(Convert.toStr(categoryDto.getId()));
                treeNode.setParentId(Convert.toStr(categoryDto.getPid()));
                treeNode.setName(categoryDto.getCategoryName());
                treeNode.setWeight(categoryDto.getSortNum());
                treeNode.putExtra("title", categoryDto.getCategoryName());
                treeNode.putExtra("value", Convert.toStr(categoryDto.getId()));
                treeNode.putExtra("code", categoryDto.getCategoryCode());
                treeNode.putExtra("level", categoryDto.getLevel());
                return treeNode;
            }).collect(Collectors.toList());
        } else {
            return TreeUtil.build(all, "0", (categoryDto, treeNode) -> {
                treeNode.setId(Convert.toStr(categoryDto.getId()));
                treeNode.setParentId(Convert.toStr(categoryDto.getPid()));
                treeNode.setName(categoryDto.getCategoryName());
                treeNode.setWeight(categoryDto.getSortNum());
                treeNode.putExtra("title", categoryDto.getCategoryName());
                treeNode.putExtra("value", Convert.toStr(categoryDto.getId()));
                treeNode.putExtra("code", categoryDto.getCategoryCode());
                treeNode.putExtra("level", categoryDto.getLevel());
            });
        }
    }

    /**
     * 从数据库获取字典键值对集合
     *
     * @return 给前端使用的字典结构
     * <AUTHOR>
     */
    @ApiOperation("获取耗材分类字典结构键值对集合")
    @GetMapping(value = "/dict")
    public List<Map<String, ?>> dict() {
        List<MaterialCategoryDto> all = materialCategoryFeignClient.all(null);
        return all.stream().map(categoryDto -> ImmutableMap.of("label", categoryDto.getCategoryName(), "value", categoryDto.getId())).collect(Collectors.toList());
    }

    /**
     * 排序
     *
     * @param ids ids
     * @return true or false
     * <AUTHOR>
     */
    @ApiOperation("多个耗材数据排序")
    @PutMapping(value = "/sort")
    public Boolean sort(@RequestBody List<Long> ids) {
        return materialCategoryFeignClient.sort(ids);
    }

    @ApiOperation(value = "通过Ids查询耗材分类数据")
    @PostMapping("/ids")
    public List<Map<String, ?>> listByIds(@RequestBody List<Long> cateIds) {
        if (CollUtil.isEmpty(cateIds)) {
            return ListUtil.empty();
        }
        return materialCategoryFeignClient.listByIds(cateIds).stream().map(item ->
                ImmutableMap.of("id", item.getId(), "name", item.getCategoryName(), "code", item.getCategoryCode())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "耗材分类导出")
    @GetMapping("/export")
    public void assetReportPageExport(HttpServletResponse response) {
        try {
            List<MaterialCategoryDto> all = materialCategoryFeignClient.all(null);
            // 包含code与name ，希望变更name的值
            Map<Long, String> idToName = all.stream().collect(Collectors.toMap(MaterialCategoryDto::getId, MaterialCategoryDto::getCategoryName));
            Map<Long, String> idToCode = all.stream().collect(Collectors.toMap(MaterialCategoryDto::getId, MaterialCategoryDto::getCategoryCode));
            List<CategoryExportDto> excel = new ArrayList<>();
            for (MaterialCategoryDto category : all) {
                Long pid = category.getPid();
                CategoryExportDto categoryExportDto = new CategoryExportDto().setCategoryCode(category.getCategoryCode())
                        .setCategoryName(category.getCategoryName())
                        .setCategoryPidCode(idToCode.get(pid))
                        .setCategoryPidName(idToName.get(pid));
                excel.add(categoryExportDto);
            }
            LinkedHashMap<String, String> header = new LinkedHashMap<>();
            header.put("categoryCode", "分类编码");
            header.put("categoryName", "分类名称");
            header.put("categoryPidCode", "上级编码");
            header.put("categoryPidName", "上级分类");
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MRL_CATE));
            ExcelUtils.export(response, new ExcelExportDto(header, excel), "耗材分类信息-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.error("耗材分类导出失败, {}", e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    /**
     * 获取推荐吗
     *
     * @return max code
     * <AUTHOR>
     */
    @ApiOperation("获取耗材分类推荐码")
    @GetMapping(value = "/recommendCode")
    public String recommendCode() {
        return materialCategoryFeignClient.recommendCode();
    }

    @ApiOperation(value = "【PC】导出耗材分类模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        categoryService.exportTemplate(response);
    }

    @ApiOperation(value = "【PC】导入耗材分类模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            categoryService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return categoryService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        categoryService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.MATERIAL_CATEGORY));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
