package com.niimbot.asset.service.impl;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.JacksonConverter;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.InventoryAssetService;
import com.niimbot.asset.service.InventoryResultHandleService;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.InventoryAssetFeignClient;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.dynamicform.BuildAssetLogDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryAssetPcDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventorySurplusSimpleQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/10 16:57
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryResultHandleServiceImpl implements InventoryResultHandleService {

    private static final String STANDARD_ID = "standardId";
    private final InventoryAssetFeignClient inventoryAssetFeignClient;
    private final InventoryFeignClient inventoryFeignClient;

    private final InventoryAssetService inventoryAssetService;
    private final FormFeignClient formFeignClient;
    private final AssetFeignClient assetFeignClient;
    private final DictConvertUtil dictConvertUtil;

    @Override
    public PageUtils<JSONObject> plPage(InventorySurplusQueryDto dto) {
        // 获取盘亏数据
        dto.setChecked(0L);
        PageUtils<InventoryAssetPcDto> tmpPage = inventoryAssetFeignClient.plPage(dto);
        List<InventoryAssetPcDto> tmpList = tmpPage.getList();
        List<JSONObject> tmpListReturn = new ArrayList<>();
        //查询属性
        dictConvertUtil.convertToDictionary(tmpList);
        for (InventoryAssetPcDto rowDto : tmpList) {
            // 资产json数据
            JSONObject assetData = Optional.of(rowDto.getAssetSnapshotData()).orElse(new JSONObject());
            String jackson;
            try {
                jackson = JacksonConverter.MAPPER.writeValueAsString(rowDto);
            } catch (JsonProcessingException e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点资产数据异常");
            }
            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
            json.remove("assetSnapshotData");
            assetData.putAll(json);
            tmpListReturn.add(assetData);
        }
        PageUtils<JSONObject> returnPage = new PageUtils<>();
        BeanUtil.copyProperties(tmpPage, returnPage);
        returnPage.setList(tmpListReturn);
        return returnPage;
    }

    @Override
    public PageUtils<JSONObject> getPlSurplus(InventorySurplusSimpleQueryDto dto) {

        PageUtils<InventorySurplusDto> inventorySurplusDto = inventoryFeignClient.getPlSurplus(dto);
        List<InventorySurplusDto> inventorySurplusDtoList = inventorySurplusDto.getList();
        List<JSONObject> surplusData = Lists.newArrayList();
        //查询属性
        try {
            // 字典转换
            dictConvertUtil.convertToDictionary(inventorySurplusDtoList);
            for (InventorySurplusDto surplusDto : inventorySurplusDtoList) {
                JSONObject assetData = surplusDto.getAssetData();

                // 资产json数据
                String jackson = JacksonConverter.MAPPER.writeValueAsString(surplusDto);
                JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
                json.remove("assetData");
                assetData.putAll(json);
                surplusData.add(assetData);
            }
        } catch (Exception e) {
            log.error("获取盘盈资产数据失败", e);
        }

        PageUtils<JSONObject> surplusPage = new PageUtils<>();
        BeanUtil.copyProperties(inventorySurplusDto, surplusPage);
        surplusPage.setList(surplusData);
        return surplusPage;
    }

    @Override
    public PageUtils<JSONObject> modifiedPage(InventorySurplusQueryDto dto) {
        PageUtils<InventoryAssetPcDto> tmpPage = inventoryAssetFeignClient.modifiedPage(dto);
        List<InventoryAssetPcDto> tmpList = tmpPage.getList();
        List<JSONObject> tmpListReturn = new ArrayList<>();

        //查询属性
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        dictConvertUtil.convertToDictionary(tmpList);
        for (InventoryAssetPcDto rowDto : tmpList) {
            // 资产json数据
            JSONObject assetData = Optional.of(rowDto.getAssetSnapshotData()).orElse(new JSONObject());
            Long standardId = assetData.getLong(STANDARD_ID);
            List<FormFieldCO> formFieldCOS = inventoryAssetService.buildAttrCache(formVO.getFormFields(), formVO.getFormId(), standardId);

            // 获取资产修改内容
            JSONObject tmpJsonOrigin = JSONObject.parseObject(rowDto.getAssetChangRecordOrigen());
            JSONObject tmpJsonChanged = JSONObject.parseObject(rowDto.getAssetChangRecord());
            String tmpStrJson = assetFeignClient.buildAssetLog(new BuildAssetLogDto(tmpJsonOrigin, tmpJsonChanged, formFieldCOS));
            rowDto.setAssetChangRecordText(tmpStrJson);
            String jackson;
            try {
                jackson = JacksonConverter.MAPPER.writeValueAsString(rowDto);
            } catch (JsonProcessingException e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点资产数据异常");
            }
            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
            json.remove("assetSnapshotData");
            assetData.putAll(json);
            tmpListReturn.add(assetData);
        }
        PageUtils<JSONObject> resultPage = new PageUtils<>();
        resultPage.setTotalCount(tmpPage.getTotalCount());
        resultPage.setCurrPage(tmpPage.getCurrPage());
        resultPage.setPageSize(tmpPage.getPageSize());
        resultPage.setTotalPage(tmpPage.getTotalPage());
        resultPage.setList(tmpListReturn);
        return resultPage;
    }

    @Override
    public PageUtils<JSONObject> takePhotoPage(InventorySurplusQueryDto dto) {
        PageUtils<InventoryAssetPcDto> tmpPage = inventoryAssetFeignClient.takePhotoPage(dto);
        List<InventoryAssetPcDto> tmpList = tmpPage.getList();
        List<JSONObject> tmpListReturn = new ArrayList<>();

        //查询属性
        dictConvertUtil.convertToDictionary(tmpList);
        for (InventoryAssetPcDto rowDto : tmpList) {
            // 资产json数据
            JSONObject assetData = Optional.of(rowDto.getAssetSnapshotData()).orElse(new JSONObject());
            String jackson;
            try {
                jackson = JacksonConverter.MAPPER.writeValueAsString(rowDto);
            } catch (JsonProcessingException e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点资产数据异常");
            }
            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
            json.remove("assetSnapshotData");
            assetData.putAll(json);
            tmpListReturn.add(assetData);
        }
        PageUtils<JSONObject> resultPage = new PageUtils<>();
        resultPage.setTotalCount(tmpPage.getTotalCount());
        resultPage.setCurrPage(tmpPage.getCurrPage());
        resultPage.setPageSize(tmpPage.getPageSize());
        resultPage.setTotalPage(tmpPage.getTotalPage());
        resultPage.setList(tmpListReturn);
        return resultPage;
    }


}
