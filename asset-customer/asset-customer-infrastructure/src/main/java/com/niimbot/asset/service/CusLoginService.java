package com.niimbot.asset.service;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.model.LoginAndBandingBody;
import com.niimbot.asset.openapi.dto.AccessTokenDto;
import com.niimbot.asset.openapi.dto.GetAccessTokenDto;
import com.niimbot.kalimdor.magneto.model.UserInfoResponse;
import com.niimbot.system.LoginByMobileResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/10/27 10:09
 */
public interface CusLoginService {

    String loginByEmployee(CusUserDto cusUserDto, String terminal);

    /**
     * 账号密码登录验证
     *
     * @param account  用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @param terminal 终端
     * @param pushId   推送id
     * @return 结果
     */
    String loginByPwd(String account, String password, String code, String uuid, String terminal, String pushId);

    /**
     * 手机号登录验证
     *
     * @param mobile   用户名
     * @param sms      验证码
     * @param terminal 终端
     * @param pushId   推送id
     * @return 结果
     */
    LoginByMobileResult loginByMobile(String mobile, String sms, String terminal, String pushId);

    /**
     * 获取登录用户信息
     *
     * @return 登录用户
     */
    LoginUserDto getLoginUser();

    /**
     * 获取二维码UUID
     *
     * @return 二维码UUID
     */
    String getQrCode();

    /**
     * 获取二维码状态
     *
     * @param uuid 二维码UUID
     * @return 状态
     */
    Map<String, Object> checkQrCode(String uuid);

    /**
     * 扫描
     *
     * @param uuid 二维码UUID
     * @return 状态
     */
    Map<String, Object> scanQrCode(String uuid);

    /**
     * 确认登录
     *
     * @param uuid    二维码UUID
     * @param confirm 是否确认
     * @return 状态
     */
    Map<String, Object> loginByConfirm(String uuid, boolean confirm);

    /**
     * 社交绑定
     *
     * @param unionId
     * @param provider
     * @param appId
     * @param code
     * @return
     */
    UserInfoResponse socialBind(String unionId, String provider, String appId, String code);

    /**
     * 社交解绑
     *
     * @param unionId
     * @param provider
     * @return
     */
    UserInfoResponse socialUnbind(String unionId, String provider);

    /**
     * 社交登录登录验证
     *
     * @param provider 社交登录提供方,eg.QQ ,WEIXIN
     * @param appId    第三方平台appid
     * @param code     授权码
     * @param terminal 终端
     * @param pushId   推送id
     * @return 结果
     */
    String loginBySocial(String provider, String appId, String code, String terminal, String pushId);

    /**
     * 登录并绑定社交账号
     *
     * @param loginAndBandingBody
     * @return
     */
    String loginAndBanding(LoginAndBandingBody loginAndBandingBody);

    AccessTokenDto loginByOpenApi(GetAccessTokenDto getAccessTokenDto);

    /**
     * 三方认证登录
     * @param type 认证类型 cas ad
     * @param client 认证端 pc app
     * @return
     */
    Map<String,Object> loginByThird(Map<String,Object> loginBody);

}
