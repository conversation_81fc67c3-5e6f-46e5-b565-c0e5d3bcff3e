package com.niimbot.asset.service.feign.equipment;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.*;
import com.niimbot.system.AuditableOperateResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentSiteInspectPlanFeignClient {

    @PostMapping("/server/equipment/site/plan/create")
    AuditableOperateResult createPlan(@RequestBody CreateEntSntPlan create);

    @PostMapping("/server/equipment/site/plan/edit")
    AuditableOperateResult editPlan(@RequestBody EditEntSntPlan edit);

    @PostMapping("/server/equipment/site/plan/remove")
    List<AuditableOperateResult> removePlan(@RequestBody RemoveEntSntPlan remove);

    @PostMapping("/server/equipment/site/plan/stop")
    List<AuditableOperateResult> stopPlan(@RequestBody List<Long> planIds);

    @PostMapping("/server/equipment/site/plan/edit/managers")
    AuditableOperateResult editPlanManagers(@RequestBody EditEntSntPlanManagers edit);

    @PostMapping("/server/equipment/site/plan/edit/executors")
    AuditableOperateResult editPlanExecutors(@RequestBody EditEntSntPlanExecutors edit);

    @PostMapping("/server/equipment/site/plan/search")
    PageUtils<EntSntPlan> searchPlan(@RequestBody SearchEntSntPlan search);

    @GetMapping("/server/equipment/site/plan/detail")
    EntSntPlanDetails detailPlan(@RequestParam("planId") Long planId);

    @PostMapping("/server/equipment/site/plan/range/search")
    PageUtils<EntSntRange> rangeSearch(@RequestBody SearchEntSntRange search);

    @PostMapping("/server/equipment/site/plan/range/list")
    List<EntSntRange> rangeList(@RequestBody ListEntSntRange search);
}
