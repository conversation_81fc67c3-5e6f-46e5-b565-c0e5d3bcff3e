package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.EmailAddressFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.sale.EmailAddressDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/28 17:19
 */
@Api(tags = "【服务中心】邮箱地址")
@ResultController
@RequestMapping("api/common/email")
@RequiredArgsConstructor
public class EmailAddressController {

    private final EmailAddressFeignClient addressFeignClient;

    @ApiOperation(value = "新增邮箱地址")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean save(@RequestBody @Validated(value = {Insert.class}) EmailAddressDto addressDto) {
        return addressFeignClient.save(addressDto);
    }

    @ApiOperation(value = "编辑邮箱地址")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean update(@RequestBody @Validated(value = {Update.class}) EmailAddressDto addressDto) {
        return addressFeignClient.update(addressDto);
    }

    @ApiOperation(value = "删除邮箱地址")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("id") Long id) {
        return addressFeignClient.delete(id);
    }

    @ApiOperation(value = "收货邮箱地址")
    @GetMapping("/list")
    public List<EmailAddressDto> queryList() {
        return addressFeignClient.queryList();
    }

    @ApiOperation(value = "设置默认")
    @PutMapping("/default/{id}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean setDefault(@PathVariable("id") Long id) {
        return addressFeignClient.setDefault(id);
    }

}
