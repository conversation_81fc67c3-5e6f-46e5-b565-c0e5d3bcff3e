package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.PredefinedReportExportService;
import com.niimbot.asset.service.PredefinedReportService;
import com.niimbot.asset.service.ReportQueryFieldService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.report.MaterialPredefinedReportFeignClient;
import com.niimbot.asset.service.feign.report.PredefinedReportFeignClient;
import com.niimbot.asset.utils.ExcelExportUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AsAssetLogDto;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.report.*;
import com.niimbot.system.ImportTaskDto;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/8 下午4:02
 */
@Slf4j
@Service
public class PredefinedReportExportServiceImpl extends AbstractExcelExportService implements PredefinedReportExportService {

    private final ImportTaskFeignClient importTaskFeignClient;
    private final DictConvertUtil dictConvertUtil;

    @Autowired
    private PredefinedReportService predefinedReportService;
    @Autowired
    private ReportQueryFieldService reportQueryFieldService;
    @Autowired
    private ExcelExportUtil excelExportUtil;
    @Autowired
    private PredefinedReportFeignClient predefinedReportFeignClient;
    @Autowired
    private MaterialPredefinedReportFeignClient materialPredefinedReportFeignClient;
    @Autowired
    private FormFeignClient formFeignClient;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private FileUploadConfig fileUploadConfig;


    protected PredefinedReportExportServiceImpl(ImportTaskFeignClient importTaskFeignClient,
                                                DictConvertUtil dictConvertUtil) {
        super(importTaskFeignClient);
        this.importTaskFeignClient = importTaskFeignClient;
        this.dictConvertUtil = dictConvertUtil;
    }

    @Override
    public ExportResponse exportAssetLogReport(AssetLogReportQueryDto queryDto) {
        //获取资产履历报表数据
        queryDto.setPageNum(1);
        queryDto.setPageSize(Long.MAX_VALUE);
        PageUtils<JSONObject> assetLogPage = predefinedReportService.assetLogReport(queryDto);
        if (Objects.isNull(assetLogPage) || CollUtil.isEmpty(assetLogPage.getList())) {
            BusinessExceptionUtil.throwException("资产履历报表无数据, 无法导出");
        }

        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.ASSET_LOG_REPORT), OrderTypeNewEnum.ASSET_LOG_REPORT.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(queryDto)));
        exportParams.setExportUrl(OrderTypeNewEnum.ASSET_LOG_REPORT.getDetailExportUrl());
        List<AbsExportData> assetLogExportDataList = assetLogPage.getList().stream().map(PredefinedReportExportData::new).collect(Collectors.toList());
        exportParams.setExportDataList(assetLogExportDataList);

        //大于1000条，异步导出
        if (assetLogPage.getTotalCount() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeAssetLogReportExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeAssetLogReportExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    /**
     * 导出excel并上传到oss
     *
     * @param taskId
     * @param exportParams
     * @return
     */
    private String executeAssetLogReportExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;

        //获取动态表头，不包含处理记录静态表头
        List<AssetHeadDto> headView = reportQueryFieldService.reportHeadView(ReportEnum.ASSET_LOG.getCode());

        //拿出数据
        List<JSONObject> dataList = exportParams.getExportDataList().stream().map(item -> {
            PredefinedReportExportData assetLogExportData = (PredefinedReportExportData) item;
            return assetLogExportData.getData();
        }).collect(Collectors.toList());

        ossPath = writeAssetLog(headView, dataList, exportParams);

        // 更新任务url
        if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
            ImportTaskDto importTaskUpdate = new ImportTaskDto();
            importTaskUpdate.setId(taskId).setUrl(ossPath);
            importTaskFeignClient.update(importTaskUpdate);
        }
        return ossPath;
    }

    @Getter
    @AllArgsConstructor
    private static class PredefinedReportExportData extends AbsExportData {
        private JSONObject data;
    }

    @Getter
    @AllArgsConstructor
    private static class MaterialReportExportData extends AbsExportData {
        private GroupReportResult data;
    }

    private String writeAssetLog(List<AssetHeadDto> headView, List<JSONObject> assetList, ExportParams exportParams) {
        String ossPath = StrUtil.EMPTY;

        /** 第一步，创建一个Workbook，对应一个Excel文件  */
        XSSFWorkbook wb = new XSSFWorkbook();

        /** 第二步，在Workbook中添加一个sheet,对应Excel文件中的sheet  */
        XSSFSheet sheet = wb.createSheet("资产履历汇总报表");

        /** 第三步，设置样式以及字体样式*/
        XSSFCellStyle titleStyle = createTitleCellStyle(wb);
        XSSFCellStyle contentStyle = createContentCellStyle(wb);

        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(9, 15 * 256);
        sheet.setColumnWidth(11, 30 * 256);
        sheet.setColumnWidth(12, 25 * 256);

        // 行号
        int rowNum = 0;

        //处理记录开始列
        int rowNumLogs = headView.size();

        // 创建第一页的第一行，索引从0开始
        XSSFRow row1 = sheet.createRow(rowNum++);
        //这些字段需要取text字段的值
        List<String> transferName = ListUtil.of("assetCategory", "orgOwner", "status", "storageArea", "useOrg", "usePerson", "managerOwner", "createBy");
        LinkedHashMap<String, String> rowFirst = new LinkedHashMap<>();
        Map<String, AssetHeadDto> rowFirstCodeMap = new HashMap<>();
        for (AssetHeadDto headDto : headView) {
            //图片过滤
            if (FormFieldCO.IMAGES.equalsIgnoreCase(headDto.getType())) {
                rowNumLogs--;
                continue;
            }
            if (transferName.contains(headDto.getCode())) {
                rowFirst.put(String.join("", headDto.getCode(), "Text"), headDto.getName());
            } else {
                rowFirst.put(headDto.getCode(), headDto.getName());
                rowFirstCodeMap.put(headDto.getCode(), headDto);
            }
        }

        int i = 0;
        for (String key : rowFirst.keySet()) {
            XSSFCell tempCell = row1.createCell(i);
            tempCell.setCellStyle(titleStyle);
            tempCell.setCellValue(rowFirst.get(key));
            // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
            sheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
            i++;
        }

        String title = "处理记录";
        XSSFCell c00 = row1.createCell(rowNumLogs);
        c00.setCellValue(title);
        c00.setCellStyle(titleStyle);
        // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
        sheet.addMergedRegion(new CellRangeAddress(0, 0, rowNumLogs, rowNumLogs + 4));//标题合并单元格操作

        XSSFRow rowLogs = sheet.createRow(1);
        LinkedHashMap<String, String> rowFirstLogs = new LinkedHashMap<>();
        rowFirstLogs.put("actionName", "处理类型");
        rowFirstLogs.put("createTime", "处理时间");
        rowFirstLogs.put("createUserText", "处理人");
        rowFirstLogs.put("actionContent", "处理内容");
        rowFirstLogs.put("orderNo", "关联单据");
        int j = 0;
        for (String key : rowFirstLogs.keySet()) {
            XSSFCell tempCell = rowLogs.createCell(j + rowNumLogs);
            tempCell.setCellStyle(titleStyle);
            tempCell.setCellValue(rowFirstLogs.get(key));
            j++;
        }

        //循环每一行数据
        int mainRowNum = 2;
        int childRowNum = 2;
        if (CollUtil.isNotEmpty(assetList)) {
            for (JSONObject assetLogReportDto : assetList) {
                List<AsAssetLogDto> logs = JSONObject.parseArray(JSONObject.toJSONString(assetLogReportDto.get("logs")), AsAssetLogDto.class);

                int logsSize = logs.size();

                List<XSSFRow> rowList = new ArrayList<>();
                // 写处理记录数据
                for (AsAssetLogDto asAssetLogDto : logs) {
                    XSSFRow tempRow = sheet.createRow(childRowNum);
                    rowList.add(tempRow);

                    j = 0;
                    for (String key : rowFirstLogs.keySet()) {
                        XSSFCell tempChildCell = tempRow.createCell(j + rowNumLogs);
                        tempChildCell.setCellStyle(contentStyle);

                        String tempValue = "";
                        Method method = ReflectUtil.getMethod(AsAssetLogDto.class, true, "get" + key);
                        try {
                            Object invoke = method.invoke(asAssetLogDto);
                            tempValue = Convert.toStr(invoke);
                        } catch (IllegalAccessException | InvocationTargetException e) {
                            log.error(e.getMessage(), e);
                        }

                        if (key.equals("createTime")) {
                            // 处理时间格式
                            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            tempChildCell.setCellValue(df.format(asAssetLogDto.getCreateTime()));
                        } else {
                            tempChildCell.setCellValue(tempValue);
                        }

                        j++;
                    }

                    childRowNum++;
                }

                // 写资产数据
                i = 0;
                for (String key : rowFirst.keySet()) {
                    XSSFCell tempCell = rowList.get(0).createCell(i);
                    tempCell.setCellStyle(contentStyle);
                    String tempValue = assetLogReportDto.getString(key);
                    //处理时间类型，做时间戳转换
                    if (StrUtil.isNotBlank(tempValue) && !"null".equalsIgnoreCase(tempValue) && Objects.nonNull(rowFirstCodeMap.get(key))
                            && FormFieldCO.DATETIME.equalsIgnoreCase(rowFirstCodeMap.get(key).getType())) {
                        tempCell.setCellValue(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tempValue)), ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    } else {
                        tempCell.setCellValue(tempValue);
                    }
                    if (logsSize > 1) {
                        // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
                        sheet.addMergedRegion(new CellRangeAddress(mainRowNum, mainRowNum + logsSize - 1, i, i));
                    }
                    i++;
                }

                mainRowNum += logsSize;
            }
        }

        String nowDay = LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = OrderTypeNewEnum.ASSET_LOG_REPORT.getName() + nowDay + ".xlsx";
        File downloadFile = null;
        FileOutputStream fileOutputStream = null;
        // 导出
        try {
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);
            fileOutputStream = new FileOutputStream(downloadFile);
            wb.write(fileOutputStream);// 将数据写出去
            wb.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());
        } catch (Exception e) {
            log.error("predefinedReportExportService writeAssetLog exception ", e);
        } finally {
            try {
                if (Objects.nonNull(fileOutputStream)) {
                    fileOutputStream.close();
                }
            } catch (Exception e) {
                log.error("predefinedReportExportService writeAssetLog close stream exception  ", e);
            }
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    /**
     * 创建标题样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createTitleCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直对齐
        XSSFFont headerFont1 = (XSSFFont) wb.createFont(); // 创建字体样式
        headerFont1.setBold(true); //字体加粗
        cellStyle.setFont(headerFont1); // 为标题样式设置字体样式

        return cellStyle;
    }

    /**
     * 创建内容样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createContentCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 水平居中
        cellStyle.setWrapText(true);// 设置自动换行
        return cellStyle;
    }

    @Override
    public ExportResponse exportMaterialCategoryReport(MaterialCategoryReportSearch search) {
        //查询耗材分类统计信息
        GroupReportResult materialCategoryResult = predefinedReportFeignClient.materialCategoryFixedHeadReport(search);
        if (Objects.isNull(materialCategoryResult) || Objects.isNull(materialCategoryResult.getItems())) {
            BusinessExceptionUtil.throwException("耗材分类统计表无数据, 无法导出");
        }

        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.MATERIAL_CATEGORY_REPORT), OrderTypeNewEnum.MATERIAL_CATEGORY_REPORT.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(search)));
        exportParams.setExportUrl(OrderTypeNewEnum.MATERIAL_CATEGORY_REPORT.getDetailExportUrl());

        //导出数据
        exportParams.setExportDataList(Collections.singletonList(new MaterialReportExportData(materialCategoryResult)));

        //导出数据
        List<JSONObject> dataList = JSONObject.parseArray(JSONObject.toJSONString(materialCategoryResult.getItems()), JSONObject.class);
        //写入导出数据总数
        exportParams.setCount(dataList.size());

        //大于1000条，异步导出
        if (dataList.size() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeMaterialCategoryExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeMaterialCategoryExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    /**
     * 耗材分类统计导出excel到本地磁盘，并上传到oss
     *
     * @param taskId
     * @param exportParams
     * @return
     */
    private String executeMaterialCategoryExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;

        //表头信息
        LinkedHashMap<String, String> header = new LinkedHashMap<>();
        header.put("categoryName", "耗材分类");
        header.put("openingQuantity", "期初数量");
        header.put("openingUnitPrice", "期初单价");
        header.put("openingAmount", "期初金额");
        header.put("increasedQuantity", "增加数量");
        header.put("increasedAmount", "增加金额");
        header.put("reducedQuantity", "减少数量");
        header.put("reducedAmount", "减少金额");
        header.put("endingQuantity", "期末数量");
        header.put("endingUnitPrice", "期末单价");
        header.put("endingAmount", "期末金额");

        //拿出数据
        GroupReportResult materialGroupResult = ((MaterialReportExportData) exportParams.getExportDataList().get(0)).getData();
        //列表数据
        List<JSONObject> dataList = JSONObject.parseArray(JSONObject.toJSONString(materialGroupResult.getItems()), JSONObject.class);
        JSONObject summaryData = JSONObject.parseObject(JSONObject.toJSONString(materialGroupResult.getSummary()), JSONObject.class);
        //文件名称
        String fileName = String.join("",
                OrderTypeNewEnum.MATERIAL_CATEGORY_REPORT.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try {
            //写入到磁盘文件
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);

            ExcelWriter writer = excelExportUtil.createWriter(header);

            //excel写入数据
            excelExportUtil.writeData(writer, header, dataList);
            //写入合计
            excelExportUtil.writeData(writer, header, Collections.singletonList(summaryData));

            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("predefinedReportExportService executeMaterialCategoryExport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    @Override
    public ExportResponse exportMaterialRepositoryReport(MaterialRepositoryReportSearch search) {
        //查询耗材出入库统计信息
        GroupReportResult materialRepositoryResult = predefinedReportFeignClient.materialRepositoryFixedHeadReport(search);
        if (Objects.isNull(materialRepositoryResult) || Objects.isNull(materialRepositoryResult.getItems())) {
            BusinessExceptionUtil.throwException("耗材出入库统计表数据为空, 无法导出");
        }

        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.MATERIAL_REPOSITORY_REPORT), OrderTypeNewEnum.MATERIAL_REPOSITORY_REPORT.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(search)));
        exportParams.setExportUrl(OrderTypeNewEnum.MATERIAL_REPOSITORY_REPORT.getDetailExportUrl());

        PageUtils<?> materialRepositoryPageResult = JSONObject.parseObject(JSONObject.toJSONString(materialRepositoryResult.getItems()), PageUtils.class);
        exportParams.setCount(materialRepositoryPageResult.getTotalCount());

        //大于1000条，异步导出
        if (materialRepositoryPageResult.getTotalCount() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeMaterialRepositoryExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeMaterialRepositoryExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }


    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    private static class MaterialRkManifestExport extends AbsExportData {
        private MaterialRkManifestDto materialRkManifestDto;
    }

    @Override
    public ExportResponse materialRkManifestExport(MaterialRkManifestQueryDto queryDto) {
        //获取耗材入库清单
        queryDto.setPageNum(1);
        queryDto.setPageSize(Long.MAX_VALUE);
        PageUtils<MaterialRkManifestDto> pageUtils = materialPredefinedReportFeignClient.materialRkManifest(queryDto);
        if (Objects.isNull(pageUtils) || CollUtil.isEmpty(pageUtils.getList())) {
            BusinessExceptionUtil.throwException("耗材入库清单无数据, 无法导出");
        }
        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.MATERIAL_RK_MANIFEST), OrderTypeNewEnum.MATERIAL_RK_MANIFEST.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(queryDto)));
        exportParams.setExportUrl(OrderTypeNewEnum.MATERIAL_RK_MANIFEST.getDetailExportUrl());
        exportParams.setCount(pageUtils.getTotalCount());
        exportParams.setExportDataList(pageUtils.getList().stream().map(MaterialRkManifestExport::new).collect(Collectors.toList()));

        //大于1000条，异步导出
        if (pageUtils.getTotalCount() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeMaterialRkManifestExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeMaterialRkManifestExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    private String executeMaterialRkManifestExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;
        // 查询head
        LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(MaterialRkManifestDto.class);
        //文件名称
        String fileName = String.join("", OrderTypeNewEnum.MATERIAL_RK_MANIFEST.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try {
            //写入到磁盘文件
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);
            ExcelWriter writer = excelExportUtil.createWriter(headerData);
            List<MaterialRkManifestDto> rkManifestDtoList = exportParams.getExportDataList().stream()
                    .map(f -> ((MaterialRkManifestExport) f).getMaterialRkManifestDto())
                    .collect(Collectors.toList());
            //excel写入数据
            excelExportUtil.writeData(writer, headerData, rkManifestDtoList);
            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("predefinedReportExportService executeMaterialRkManifestExport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    private static class MaterialCkManifestExport extends AbsExportData {
        private MaterialCkManifestDto materialCkManifestDto;
    }

    @Override
    public ExportResponse materialCkManifestExport(MaterialCkManifestQueryDto queryDto) {
        //获取耗材入库清单
        queryDto.setPageNum(1);
        queryDto.setPageSize(Long.MAX_VALUE);
        PageUtils<MaterialCkManifestDto> pageUtils = materialPredefinedReportFeignClient.materialCkManifest(queryDto);
        if (Objects.isNull(pageUtils) || CollUtil.isEmpty(pageUtils.getList())) {
            BusinessExceptionUtil.throwException("耗材出库清单无数据, 无法导出");
        }
        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.MATERIAL_CK_MANIFEST), OrderTypeNewEnum.MATERIAL_CK_MANIFEST.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(queryDto)));
        exportParams.setExportUrl(OrderTypeNewEnum.MATERIAL_CK_MANIFEST.getDetailExportUrl());
        exportParams.setCount(pageUtils.getTotalCount());
        exportParams.setExportDataList(pageUtils.getList().stream().map(MaterialCkManifestExport::new).collect(Collectors.toList()));

        //大于1000条，异步导出
        if (pageUtils.getTotalCount() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeMaterialCkManifestExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeMaterialCkManifestExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    private String executeMaterialCkManifestExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;
        // 查询head
        LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(MaterialCkManifestDto.class);
        //文件名称
        String fileName = String.join("", OrderTypeNewEnum.MATERIAL_CK_MANIFEST.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try {
            //写入到磁盘文件
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);
            ExcelWriter writer = excelExportUtil.createWriter(headerData);
            List<MaterialCkManifestDto> ckManifestDtoList = exportParams.getExportDataList().stream()
                    .map(f -> ((MaterialCkManifestExport) f).getMaterialCkManifestDto())
                    .collect(Collectors.toList());
            //excel写入数据
            excelExportUtil.writeData(writer, headerData, ckManifestDtoList);
            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("predefinedReportExportService executeMaterialCkManifestExport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    private static class MaterialRkStatisticsDtoExport extends AbsExportData {
        private MaterialRkStatisticsDto materialRkStatisticsDto;
    }

    @Override
    public ExportResponse materialRkStatisticsExport(MaterialRkStatisticsQueryDto queryDto) {
        //获取耗材入库统计
        List<MaterialRkStatisticsDto> list = materialPredefinedReportFeignClient.materialRkStatistics(queryDto);
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("耗材入库统计无数据, 无法导出");
        }
        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.MATERIAL_RK_STATISTICS), OrderTypeNewEnum.MATERIAL_RK_STATISTICS.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(queryDto)));
        exportParams.setExportUrl(OrderTypeNewEnum.MATERIAL_RK_STATISTICS.getDetailExportUrl());
        exportParams.setCount(list.size());
        exportParams.setExportDataList(list.stream().map(MaterialRkStatisticsDtoExport::new).collect(Collectors.toList()));

        //大于1000条，异步导出
        if (list.size() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeMaterialRkStatisticsExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeMaterialRkStatisticsExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    private String executeMaterialRkStatisticsExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;
        // 查询head
        LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(MaterialRkStatisticsDto.class);
        //文件名称
        String fileName = String.join("", OrderTypeNewEnum.MATERIAL_RK_STATISTICS.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try {
            //写入到磁盘文件
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);
            ExcelWriter writer = ExcelUtil.getBigWriter(5000);
            List<MaterialRkStatisticsDto> rkStatisticsDtoList = exportParams.getExportDataList().stream()
                    .map(f -> ((MaterialRkStatisticsDtoExport) f).getMaterialRkStatisticsDto())
                    .collect(Collectors.toList());
            //excel写入数据
            writer.writeHeadRow(headerData.values());
            List<String> codes = new ArrayList<>(headerData.keySet());
            if (CollUtil.isNotEmpty(rkStatisticsDtoList)) {
                int mergeStart = 1;
                int mergeEnd = 1;
                String lastKey = "";
                for (MaterialRkStatisticsDto row : rkStatisticsDtoList) {
                    JSONObject json = (JSONObject) JSONObject.toJSON(row);
                    List<Object> rowData = new ArrayList<>();
                    for (String code : codes) {
                        rowData.add(json.get(code));
                    }
                    writer.writeRow(rowData);
                    if (!"".equals(lastKey) && !lastKey.equals(row.getRkTime())) {
                        if (mergeEnd - 1 > mergeStart) {
                            writer.merge(mergeStart, mergeEnd - 1, 0, 0, lastKey, false);
                        }
                        mergeStart = mergeEnd;
                    }
                    lastKey = row.getRkTime();
                    mergeEnd++;
                }
                if (mergeEnd - 1 > mergeStart) {
                    writer.merge(mergeStart, mergeEnd - 1, 0, 0, lastKey, false);
                }
                writer.merge(mergeEnd - 1, mergeEnd - 1, 0, 2, "总计", false);
            }
            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }

        } catch (Exception e) {
            log.error("predefinedReportExportService executeMaterialRkStatisticsExport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    private static class MaterialCkStatisticsDtoExport extends AbsExportData {
        private MaterialCkStatisticsDto materialCkStatisticsDto;
    }

    @Override
    public ExportResponse materialCkStatisticsExport(MaterialCkStatisticsQueryDto queryDto) {
        //获取耗材入库统计
        List<MaterialCkStatisticsDto> list = materialPredefinedReportFeignClient.materialCkStatistics(queryDto);
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("耗材出库统计无数据, 无法导出");
        }
        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.MATERIAL_CK_STATISTICS), OrderTypeNewEnum.MATERIAL_CK_STATISTICS.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(queryDto)));
        exportParams.setExportUrl(OrderTypeNewEnum.MATERIAL_CK_STATISTICS.getDetailExportUrl());
        exportParams.setCount(list.size());
        exportParams.setExportDataList(list.stream().map(MaterialCkStatisticsDtoExport::new).collect(Collectors.toList()));

        //大于1000条，异步导出
        if (list.size() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeMaterialCkStatisticsExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeMaterialCkStatisticsExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    private String executeMaterialCkStatisticsExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;
        // 查询head
        LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(MaterialCkStatisticsDto.class);
        //文件名称
        String fileName = String.join("", OrderTypeNewEnum.MATERIAL_CK_STATISTICS.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try {
            //写入到磁盘文件
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);
            ExcelWriter writer = ExcelUtil.getBigWriter(5000);
            List<MaterialCkStatisticsDto> ckStatisticsDtoList = exportParams.getExportDataList().stream()
                    .map(f -> ((MaterialCkStatisticsDtoExport) f).getMaterialCkStatisticsDto())
                    .collect(Collectors.toList());
            //excel写入数据
            writer.writeHeadRow(headerData.values());
            List<String> codes = new ArrayList<>(headerData.keySet());
            if (CollUtil.isNotEmpty(ckStatisticsDtoList)) {
                int mergeStart = 1;
                int mergeEnd = 1;
                String lastKey = "";
                for (MaterialCkStatisticsDto row : ckStatisticsDtoList) {
                    JSONObject json = (JSONObject) JSONObject.toJSON(row);
                    List<Object> rowData = new ArrayList<>();
                    for (String code : codes) {
                        rowData.add(json.get(code));
                    }
                    writer.writeRow(rowData);
                    if (!"".equals(lastKey) && !lastKey.equals(row.getCkTime())) {
                        if (mergeEnd - 1 > mergeStart) {
                            writer.merge(mergeStart, mergeEnd - 1, 0, 0, lastKey, false);
                        }
                        mergeStart = mergeEnd;
                    }
                    lastKey = row.getCkTime();
                    mergeEnd++;
                }
                if (mergeEnd - 1 > mergeStart) {
                    writer.merge(mergeStart, mergeEnd - 1, 0, 0, lastKey, false);
                }
                writer.merge(mergeEnd - 1, mergeEnd - 1, 0, 2, "总计", false);
            }
            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }

        } catch (Exception e) {
            log.error("predefinedReportExportService executeMaterialCkStatisticsExport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    private String executeMaterialRepositoryExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;

        //表头信息
        LinkedHashMap<String, String> header = new LinkedHashMap<>();
        header.put("repositoryName", "所属仓库");
        header.put("materialCode", "耗材编码");
        header.put("materialName", "耗材名称");
        header.put("materialCategoryText", "耗材分类");
        header.put("openingQuantity", "期初数量");
        header.put("openingUnitPrice", "期初单价");
        header.put("openingAmount", "期初金额");
        header.put("increasedQuantity", "增加数量");
        header.put("increasedAmount", "增加金额");
        header.put("reducedQuantity", "减少数量");
        header.put("reducedAmount", "减少金额");
        header.put("endingQuantity", "期末数量");
        header.put("endingUnitPrice", "期末单价");
        header.put("endingAmount", "期末金额");
        List<String> headCodeList = new ArrayList<>(header.keySet());

        //需要从耗材字段取数据
        List<String> materialCodeList = ListUtil.of("picture", "materialCode", "materialName", "materialCategoryText");

        //合计头部字段
        LinkedHashMap<String, String> summaryHead = new LinkedHashMap<>();
        summaryHead.put("openingQuantity", "期初数量");
        summaryHead.put("openingUnitPrice", "期初单价");
        summaryHead.put("openingAmount", "期初金额");
        summaryHead.put("increasedQuantity", "增加数量");
        summaryHead.put("increasedAmount", "增加金额");
        summaryHead.put("reducedQuantity", "减少数量");
        summaryHead.put("reducedAmount", "减少金额");
        summaryHead.put("endingQuantity", "期末数量");
        summaryHead.put("endingUnitPrice", "期末单价");
        summaryHead.put("endingAmount", "期末金额");

        //文件名称
        String fileName = String.join("", OrderTypeNewEnum.MATERIAL_REPOSITORY_REPORT.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try {
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);

            ExcelWriter writer = excelExportUtil.createWriter(header);

            //导出参数
            MaterialRepositoryReportSearch search =
                    JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), MaterialRepositoryReportSearch.class);
            //分页导出，初始化从第一页开始导出，并且设置每页大小1000
            int startPage = 1;
            search.setPageNum(startPage);
            search.setPageSize(100L);

            GroupReportResult materialRepositoryResult = predefinedReportFeignClient.materialRepositoryFixedHeadReport(search);
            if (Objects.nonNull(materialRepositoryResult) && Objects.nonNull(materialRepositoryResult.getItems())) {
                PageUtils materialRepositoryPageResult = JSONObject.parseObject(JSONObject.toJSONString(materialRepositoryResult.getItems()), PageUtils.class);

                while (Objects.nonNull(materialRepositoryPageResult) && CollUtil.isNotEmpty(materialRepositoryPageResult.getList())) {
                    List<JSONObject> dataList = JSONObject.parseArray(JSONObject.toJSONString(materialRepositoryPageResult.getList()), JSONObject.class);

                    for (JSONObject row : dataList) {
                        JSONObject materialData = row.getJSONObject("materialData");
                        List<Object> rowData = new ArrayList<>();
                        for (String code : headCodeList) {
                            if (materialCodeList.contains(code)) {
                                rowData.add(materialData.get(code));
                            } else {
                                rowData.add(row.get(code));
                            }
                        }
                        writer.writeRow(rowData);
                    }

                    startPage++;
                    search.setPageNum(startPage);
                    materialRepositoryResult = predefinedReportFeignClient.materialRepositoryFixedHeadReport(search);
                    materialRepositoryPageResult = JSONObject.parseObject(JSONObject.toJSONString(materialRepositoryResult.getItems()), PageUtils.class);
                }
            }

            //写入合计数据
            JSONObject summaryData = (JSONObject) JSONObject.toJSON(materialRepositoryResult.getSummary());
            writeMaterialRepositorySummary(writer, summaryData, summaryHead);

            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("predefinedReportExportService executeMaterialRepositoryExport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    /**
     * 耗材仓库合计数据
     *
     * @param writer
     * @param data
     * @param summaryHeader
     */
    private void writeMaterialRepositorySummary(ExcelWriter writer, JSONObject data, LinkedHashMap<String, String> summaryHeader) {
        List<String> summaryHeadCode = new ArrayList<>(summaryHeader.keySet());
        List<Object> rowData = new ArrayList<>();
        rowData.add("合计");
        rowData.add("");
        rowData.add("");
        rowData.add("");
        for (String code : summaryHeadCode) {
            rowData.add(data.get(code));
        }
        writer.writeRow(rowData);
    }

    @Override
    public <T> ExportResponse exportHandleAssetReport(T search, OrderTypeNewEnum type) {
        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(type), type.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(search)));
        exportParams.setExportUrl(type.getDetailExportUrl());

        //动态列头字段
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);

        //提前查询下数据，看下数据总量
        PageUtils<JSONObject> pageResult = assetData(exportParams, type, formVO, 1, 1);
        if (Objects.isNull(pageResult) || pageResult.getTotalCount() == 0) {
            BusinessExceptionUtil.throwException("统计表数据为空, 无法导出");
        }

        //设置导出总数
        exportParams.setCount(pageResult.getTotalCount());

        //大于1000条，异步导出
        if (pageResult.getTotalCount() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeHandleAssetReport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeHandleAssetReport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    private String executeHandleAssetReport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;

        //业务类型
        OrderTypeNewEnum orderType = OrderTypeNewEnum.getByType(exportParams.getOrderType());
        ReportEnum assetType = getByOrderType(orderType);

        //动态列头字段
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);

        //获取表头数据
        List<AssetHeadDto> allHead = reportQueryFieldService.reportHeadView(assetType.getCode());

        //表头字段映射
        Map<String, AssetHeadDto> fieldMap = allHead.stream().collect(Collectors.toMap(AssetHeadDto::getCode, value -> value, (v1, v2) -> v2));

        //表头
        LinkedHashMap<String, String> header = getAssetHeader(allHead, formVO.getFormFields());

        //文件名称
        String fileName = String.join("", orderType.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try {
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);

            ExcelWriter writer = excelExportUtil.createWriter(header);

            //分页导出，初始化从第一页开始导出，并且设置每页大小1000
            int startPage = 1;
            PageUtils<JSONObject> handleAssetPage = assetData(exportParams, orderType, formVO, startPage, 100);
            while (Objects.nonNull(handleAssetPage) && CollUtil.isNotEmpty(handleAssetPage.getList())) {
                //excel写入数据
                excelExportUtil.writeData(writer, header, handleAssetPage.getList(), fieldMap);

                startPage++;
                handleAssetPage = assetData(exportParams, orderType, formVO, startPage, 100);
            }

            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("predefinedReportExportService executeHandleAssetReport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    /**
     * 获取导出报表名称，在任务中心展示
     *
     * @param orderType
     * @return
     */
    private String getName(OrderTypeNewEnum orderType) {
        // 获取流水号
        int count = importTaskFeignClient.count(DictConstant.TASK_TYPE_EXPORT, orderType.getValue());
        String serialNo = StrUtil.padPre(String.valueOf(++count), 5, "0");
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return String.join("", orderType.getName(), currentTime, serialNo);
    }

    /**
     * 获取导出报表本地磁盘文件夹
     *
     * @return
     */
    private File getFilePath() {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()),
                "excelTemp/predefined_report",
                currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    /**
     * 获取oss路径
     *
     * @param fileName
     * @param exportParams
     * @return
     */
    private String getOssPath(String fileName, AbstractExcelExportService.ExportParams exportParams) {
        return String.join("/", String.valueOf(exportParams.getCompanyId()), "customer_report", DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN), fileName);
    }

    /**
     * 获取资产导出表头
     *
     * @param allHead
     * @param formFieldCOList 这个是动态表单里面的，固定字段没有包含
     * @return
     */
    private LinkedHashMap<String, String> getAssetHeader(List<AssetHeadDto> allHead, List<FormFieldCO> formFieldCOList) {
        if (CollUtil.isEmpty(allHead)) {
            BusinessExceptionUtil.throwException("表头数据为空, 无法导出");
        }

        //报表字段分组
        Map<String, FormFieldCO> formFieldMap = new HashMap<>();
        if (CollUtil.isNotEmpty(formFieldCOList)) {
            formFieldMap = formFieldCOList.stream().collect(Collectors.toMap(FormFieldCO::getFieldCode, value -> value, (v1, v2) -> v2));
        }

        //固定字段，需要进行name转换处理
        List<String> fixedField = ListUtil.of("createBy", "status");

        LinkedHashMap<String, String> result = new LinkedHashMap<>();
        result.put("assetCode", "资产编码");
        result.put("assetName", "资产名称");
        for (AssetHeadDto item : allHead) {
            if (FormFieldCO.IMAGES.equalsIgnoreCase(item.getType())) {
                continue;
            }

            //固定字段处理
            if (fixedField.contains(item.getCode())) {
                result.put(item.getCode() + "Text", item.getName());
            } else {
                //根据转换code获取名称，不然没法翻译
                if (Objects.nonNull(formFieldMap.get(item.getCode())) && StrUtil.isNotBlank(formFieldMap.get(item.getCode()).getTranslationCode())) {
                    result.put(formFieldMap.get(item.getCode()).getTranslationCode(), item.getName());
                } else {
                    result.put(item.getCode(), item.getName());
                }
            }
        }
        return result;
    }

    /**
     * @param exportParams
     * @param orderType
     * @param formVO
     * @return
     */
    private PageUtils<JSONObject> assetData(ExportParams exportParams, OrderTypeNewEnum orderType, FormVO formVO, int start, int pageSize) {
        if (OrderTypeNewEnum.WAIT_HANDLE_ASSET_LOG_REPORT.equals(orderType)) {
            WaitHandleAssetLogQueryDto search = JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), WaitHandleAssetLogQueryDto.class);
            search.setPageNum(start);
            search.setPageSize(pageSize);
            return predefinedReportService.waitHandleAssetLog(search, formVO);
        } else if (OrderTypeNewEnum.HANDLE_ASSET_LOG_REPORT.equals(orderType)) {
            HandleAssetLogQueryDto search = JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), HandleAssetLogQueryDto.class);
            search.setPageNum(start);
            search.setPageSize(pageSize);
            return predefinedReportService.handleAssetLog(search, formVO);
        } else if (OrderTypeNewEnum.WAIT_RETURN_ASSET_LOG_REPORT.equals(orderType)) {
            WaitReturnAssetLogQueryDto search = JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), WaitReturnAssetLogQueryDto.class);
            search.setPageNum(start);
            search.setPageSize(pageSize);
            return predefinedReportService.waitReturnAssetLog(search, formVO);
        } else if (OrderTypeNewEnum.REPAIR_ASSET_LOG_REPORT.equals(orderType)) {
            RepairAssetLogQueryDto search = JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), RepairAssetLogQueryDto.class);
            search.setPageNum(start);
            search.setPageSize(pageSize);
            return predefinedReportService.repairAssetLog(search, formVO);
        } else if (OrderTypeNewEnum.REPAIR_ASSET_RECORD_REPORT.equals(orderType)) {
            RepairAssetRecordQueryDto search = JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), RepairAssetRecordQueryDto.class);
            search.setPageNum(start);
            search.setPageSize(pageSize);
            return predefinedReportService.repairAssetRecord(search, formVO);
        } else {
            return new PageUtils<>();
        }
    }

    /**
     * 获取订单类型
     *
     * @param orderType
     * @return
     */
    private ReportEnum getByOrderType(OrderTypeNewEnum orderType) {
        if (OrderTypeNewEnum.WAIT_HANDLE_ASSET_LOG_REPORT.equals(orderType)) {
            return ReportEnum.WAIT_HANDLE_ASSET_LOG;
        } else if (OrderTypeNewEnum.HANDLE_ASSET_LOG_REPORT.equals(orderType)) {
            return ReportEnum.HANDLE_ASSET_LOG;
        } else if (OrderTypeNewEnum.WAIT_RETURN_ASSET_LOG_REPORT.equals(orderType)) {
            return ReportEnum.WAIT_RETURN_ASSET_LOG;
        } else if (OrderTypeNewEnum.REPAIR_ASSET_LOG_REPORT.equals(orderType)) {
            return ReportEnum.REPAIR_ASSET_LOG;
        } else if (OrderTypeNewEnum.REPAIR_ASSET_RECORD_REPORT.equals(orderType)) {
            return ReportEnum.REPAIR_ASSET_RECORD;
        } else {
            log.error(orderType + "类型不存在");
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, orderType + "类型不存在");
        }
    }
}
