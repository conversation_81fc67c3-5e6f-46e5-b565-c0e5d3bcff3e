package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.system.DataAuthorityDto;
import com.niimbot.system.DataPermFilterDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/10/21 16:27
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface DataAuthorityFeignClient {

    /**
     * 根据用户Id查询数据权限集
     *
     * @param userId 用户Id
     * @return 公司集合
     */
    @GetMapping(value = "server/system/dataAuthority/{userId}")
    List<DataAuthorityDto> selectAuthorityById(@PathVariable("userId") Long userId);

    @GetMapping("server/system/dataAuthority/getByUserAndDataCodeAndCode")
    DataAuthorityDto getByUserAndDataCodeAndCode(@RequestParam("userId") Long userId,
                                                 @RequestParam("dataCode") String dataCode,
                                                 @RequestParam("code") String code);

    @PostMapping("server/system/dataAuthority/filterNoPermData")
    List<JSONObject> filterNoPermData(DataPermFilterDto dataPermFilterDto);

}
