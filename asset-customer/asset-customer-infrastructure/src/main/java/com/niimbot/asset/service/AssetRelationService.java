package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.means.AssetRelationAppDto;
import com.niimbot.means.AssetRelationQueryConditionDto;

/**
 * 关联资产
 * <AUTHOR>
 * @date 2023/8/10 上午9:53
 */
public interface AssetRelationService {

    /**
     * 关联资产分页查询
     * @param queryDto
     * @return
     */
    PageUtils<JSONObject> assetRelationPage(AssetQueryConditionDto queryDto);

    /**
     * 查询子资产列表
     * @param queryConditionDto
     * @return
     */
    PageUtils<JSONObject> subAsset(AssetRelationQueryConditionDto queryConditionDto);

    /**
     * 关联资产分页查询-app
     * @param queryDto
     * @return
     */
    PageUtils<AssetRelationAppDto> assetRelationAppPage(AssetQueryConditionDto queryDto);

    /**
     * 查询子资产列表-app
     * @param queryConditionDto
     * @return
     */
    PageUtils<AssetRelationAppDto> subAssetAppPage(AssetRelationQueryConditionDto queryConditionDto);

    /**
     * 根据子资产id查询主资产信息
     * @param subAssetId
     * @return
     */
    AssetRelationAppDto queryBySubAsset(Long subAssetId);
}
