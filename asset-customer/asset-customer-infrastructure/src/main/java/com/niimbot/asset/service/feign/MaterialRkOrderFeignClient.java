package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2021/7/12 13:50
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialRkOrderFeignClient {
    /**
     * 设置审批流信息获取
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/material/order/rk/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(@RequestBody MaterialRkOrderDto dto);

    @PostMapping(value = "server/material/order/rk")
    AuditableCreateOrderResult create(@RequestBody MaterialRkOrderSubmitDto submitDto);

    @GetMapping(value = "server/material/order/rk/{id}")
    MaterialRkOrderDto getById(@PathVariable("id") Long id);

    @GetMapping(value = "server/material/order/rk/detail/{orderId}/{materialId}")
    MaterialRkOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);

    @PostMapping(value = "server/material/order/rk/page")
    PageUtils<MaterialRkOrderDto> page(@RequestBody MaterialOrderQueryDto query);

    @GetMapping(value = "server/material/order/rk/pageDetail")
    PageUtils<MaterialRkOrderDetailDto> pageDetail(@SpringQueryMap MaterialOrderDetailQueryDto dto);

    /**
     * 耗材入库单据-用于导出
     *
     * @param query
     * @return 结果
     */
    @PostMapping(value = "server/material/order/rk/listForExport")
    PageUtils<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto query);
}
