package com.niimbot.asset.service;

import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionStandardDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/27 11:00
 */
public interface ReportQueryFieldService {

    // --------------报表筛选项---------------
    List<QueryConditionDto> reportAllQueryField(String type);

    List<QueryConditionDto> reportQueryView(String type);

    // --------------报表列表设置---------------
    List<QueryConditionDto> reportAllHeadField(String type);

    List<AssetHeadDto> reportHeadView(String type);

    // --------------标准品---------------
    QueryConditionStandardDto standardAllField(String baseType, Long standardId, boolean needName, boolean filterFile);


}
