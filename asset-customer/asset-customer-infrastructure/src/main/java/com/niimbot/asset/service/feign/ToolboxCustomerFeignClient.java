package com.niimbot.asset.service.feign;

import com.niimbot.system.AsToolboxDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-12-08
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ToolboxCustomerFeignClient {

    /**
     * App员工端工具箱列表数据
     *
     * @param params 查询条件
     * @return 列表数据
     */
    @GetMapping(value = "server/system/toolbox/app/list")
    List<AsToolboxDto> list(@RequestParam Map<String, Object> params);

    /**
     * App编辑工具箱
     *
     * @param list 批量列表
     * @return 结果
     */
    @PutMapping(value = "server/system/toolbox/app/batch")
    Boolean editBatch(List<AsToolboxDto> list);
}
