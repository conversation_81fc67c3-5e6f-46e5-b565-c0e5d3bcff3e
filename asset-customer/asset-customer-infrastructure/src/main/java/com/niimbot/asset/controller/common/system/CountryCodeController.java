package com.niimbot.asset.controller.common.system;


import com.niimbot.asset.service.feign.AsCountryCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.CountryCodeDto;
import com.niimbot.system.CountryCodeQueryDto;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "运营后台国家区号")
@ResultController
@RequestMapping("api/common/asCountryCode")
public class CountryCodeController {
    @Resource
    private  AsCountryCodeFeignClient countryCodeFeignClient;

    @PostMapping(value = "/list")
    public List<CountryCodeDto> page(@RequestBody CountryCodeQueryDto dto) {
        return countryCodeFeignClient.list(dto);
    }

    @PostMapping(value = "/save")
    public boolean save(@RequestBody CountryCodeQueryDto dto) {
        return countryCodeFeignClient.save(dto);
    }
}
