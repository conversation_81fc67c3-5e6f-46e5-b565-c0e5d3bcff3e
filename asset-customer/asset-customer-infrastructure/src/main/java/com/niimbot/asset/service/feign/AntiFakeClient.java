package com.niimbot.asset.service.feign;

import com.niimbot.system.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 标签打印管理feign客户端
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AntiFakeClient {

    /**
     * 碳带防伪判断
     *
     * @param antiFakeCheckDto 碳带防伪判断请求对象
     * @return Boolean
     */
    @PostMapping(value = "server/system/antiFake/check")
    AntiFakeResDto checkAntiFake(AntiFakeCheckDto antiFakeCheckDto);

    /**
     * 判断是否进行固件升级
     *
     * @param machineDetailDto 固件升级请求对象
     * @return Boolean
     */
    @PostMapping(value = "server/system/antiFake/machineCascadeDetail")
    MachineDetailResDto machineCascadeDetail(MachineDetailDto machineDetailDto);

    /**
     * 将打印信息上传云打印服务
     *
     * @param printInfoDto 打印信息对象
     * @return Boolean
     */
    @PostMapping(value = "server/system/antiFake/uploadPrintInfo")
    Boolean uploadPrintInfo(PrintInfoDto printInfoDto);

    /**
     * 授权设备检查
     *
     * @param antiFakeCheckDto 授权设备检查请求对象
     * @return Boolean
     */
    @PostMapping(value = "/server/system/print/task/checkAuthEquipment")
    Boolean checkAuthEquipment(AntiFakeCheckDto antiFakeCheckDto);

    /**
     * 第三方设备授权检查
     *
     * @param thirdPartyCheckDto dto
     * @return 是否授权
     */
    @PostMapping(value = "/server/system/print/task/checkAuthEquipmentThirdParty")
    Boolean checkAuthEquipmentThirdParty(ThirdPartyCheckDto thirdPartyCheckDto);
}
