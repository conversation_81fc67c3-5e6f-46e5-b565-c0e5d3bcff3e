package com.niimbot.asset.websocket.msg;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 审批签名完成消息
 *
 * <AUTHOR>
 * @date 2024/8/2 14:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class SignatureMessage extends Message {

    private String authCode;
    private String signatureLink;

    public SignatureMessage() {
        super(Message.TYPE_SIGNATURE);
    }

    public SignatureMessage(String authCode,
                            String signatureLink) {
        this();
        this.authCode = authCode;
        this.signatureLink = signatureLink;
    }

}
