package com.niimbot.asset.controller.common.sale;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.service.http.NiimbotShopHttpClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 商城订单前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Api(tags = "商城购物")
@RestController
@RequestMapping("/api/niimbotshop")
@RequiredArgsConstructor
@Slf4j
public class NiimbotShopOrderController {

    private final NiimbotShopHttpClient niimbotShopHttpClient;

    @ApiOperation(value = "订单列表")
    @GetMapping("/orderlist")
    public JSONObject getOrderList(@RequestParam("order_status") String orderStatus) {
        return niimbotShopHttpClient.getBusinessOrderList(orderStatus);
    }

    @ApiOperation(value = "订单详情")
    @GetMapping("/orderlist/{orderSn}")
    public JSONObject detail(@PathVariable("orderSn") String orderSn) {
        return niimbotShopHttpClient.getOrderDetail(orderSn);
    }

    @ApiOperation(value = "订单物流信息")
    @GetMapping("/getLogisticsDetails/{order_id}")
    public JSONObject getLogisticsDetails(@PathVariable("order_id") String orderId) {
        return niimbotShopHttpClient.getLogisticsDetails(orderId);
    }

}
