package com.niimbot.asset.controller.pc.report;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.PredefinedReportExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.report.MaterialPredefinedReportFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.report.MaterialCkManifestDto;
import com.niimbot.report.MaterialCkManifestQueryDto;
import com.niimbot.report.MaterialCkStatisticsDto;
import com.niimbot.report.MaterialCkStatisticsQueryDto;
import com.niimbot.report.MaterialManifestTotalDto;
import com.niimbot.report.MaterialRkManifestDto;
import com.niimbot.report.MaterialRkManifestQueryDto;
import com.niimbot.report.MaterialRkStatisticsDto;
import com.niimbot.report.MaterialRkStatisticsQueryDto;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/1/23 10:02
 */
@Slf4j
@Api(tags = "【报表】耗材预定义报表")
@ResultController
@RequestMapping("api/pc/report/predefined/material")
@RequiredArgsConstructor
public class MaterialPredefinedReportController {

    private final MaterialPredefinedReportFeignClient predefinedReportFeignClient;
    private final PredefinedReportExportService predefinedReportExportService;

    @ApiOperation("耗材入库清单")
    @PostMapping("/rk/manifest")
    public PageUtils<MaterialRkManifestDto> materialRkManifest(@RequestBody MaterialRkManifestQueryDto queryDto) {
        return predefinedReportFeignClient.materialRkManifest(queryDto);
    }

    @ApiOperation("耗材入库清单统计")
    @PostMapping("/rk/manifest/total")
    public MaterialManifestTotalDto materialRkManifestTotal(@RequestBody MaterialRkManifestQueryDto queryDto) {
        return predefinedReportFeignClient.materialRkManifestTotal(queryDto);
    }

    @ApiOperation("耗材入库清单导出")
    @PostMapping("/rk/manifest/export")
    public ExportResponse materialRkManifestExport(@RequestBody MaterialRkManifestQueryDto queryDto) {
        return predefinedReportExportService.materialRkManifestExport(queryDto);
    }

    @ApiOperation("耗材入库统计")
    @PostMapping("/rk/statistics")
    public List<MaterialRkStatisticsDto> materialRkStatistics(@RequestBody MaterialRkStatisticsQueryDto queryDto) {
        return predefinedReportFeignClient.materialRkStatistics(queryDto);
    }

    @ApiOperation("耗材入库统计导出")
    @PostMapping("/rk/statistics/export")
    public ExportResponse materialRkStatisticsExport(@RequestBody MaterialRkStatisticsQueryDto queryDto) {
        return predefinedReportExportService.materialRkStatisticsExport(queryDto);
    }


    @ApiOperation("耗材出库清单")
    @PostMapping("/ck/manifest")
    public PageUtils<MaterialCkManifestDto> materialCkManifest(@RequestBody MaterialCkManifestQueryDto queryDto) {
        return predefinedReportFeignClient.materialCkManifest(queryDto);
    }

    @ApiOperation("耗材出库清单统计")
    @PostMapping("/ck/manifest/total")
    public MaterialManifestTotalDto materialCkManifestTotal(@RequestBody MaterialCkManifestQueryDto queryDto) {
        return predefinedReportFeignClient.materialCkManifestTotal(queryDto);
    }

    @ApiOperation("耗材出库清单导出")
    @PostMapping("/ck/manifest/export")
    public ExportResponse materialCkManifestExport(@RequestBody MaterialCkManifestQueryDto queryDto) {
        return predefinedReportExportService.materialCkManifestExport(queryDto);
    }

    @ApiOperation("耗材出库统计")
    @PostMapping("/ck/statistics")
    public List<MaterialCkStatisticsDto> materialCkStatistics(@RequestBody MaterialCkStatisticsQueryDto queryDto) {
        return predefinedReportFeignClient.materialCkStatistics(queryDto);
    }

    @ApiOperation("耗材出库统计导出")
    @PostMapping("/ck/statistics/export")
    public ExportResponse materialCkStatisticsExport(@RequestBody MaterialCkStatisticsQueryDto queryDto) {
        return predefinedReportExportService.materialCkStatisticsExport(queryDto);
    }


}
