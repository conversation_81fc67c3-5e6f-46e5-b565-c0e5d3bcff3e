package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfWriter;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.NetalPrintService;
import com.niimbot.asset.service.PrintTagService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.PrintDataViewNewDto;
import com.niimbot.means.PrintPdfDto;
import com.niimbot.system.ImportTaskDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @Since 2024-10-19
 */
@Slf4j
@Service
public class PrintTagServiceImpl extends AbstractExcelExportService implements PrintTagService {

    private final ImportTaskFeignClient importTaskFeignClient;
    @Autowired
    protected AssetFeignClient assetFeignClient;
    @Resource
    private PrintFeignClient printFeignClient;

    @Autowired
    protected AsAssetUtil assetUtil;

    @Resource
    private NetalPrintService netalPrintService;

    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private FileUploadConfig fileUploadConfig;

    protected PrintTagServiceImpl(ImportTaskFeignClient importTaskFeignClient) {
        super(importTaskFeignClient);
        this.importTaskFeignClient = importTaskFeignClient;
    }

    @Override
    public ExportResponse exportTagPdf(PrintPdfDto dto) {
        // 查询数量集
        List<JSONObject> printDataList = printFeignClient.getPrintData(dto);
        if (CollUtil.isEmpty(printDataList)) {
            throw new BusinessException(SystemResultCode.REOCRD_RESULT_CODE, "查询打印数据为空");
        }
        if (printDataList.size() > 500) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "最多支持打印500条数据");
        }
        dto.setDataJson(printDataList);
        ExportResponse exportResponse = new ExportResponse();
        ExportParams exportParams = new ExportParams(
                LoginUserThreadLocal.getCompanyId(),
                getTaskName(dto.getPrintType()),
                OrderTypeNewEnum.TAG_PRINT_PDF.getValue());
        exportParams.setCount(printDataList.size());
        exportParams.setQueryCondition(JsonUtil.toJsonObject(dto));
        exportParams.setExportUrl(OrderTypeNewEnum.TAG_PRINT_PDF.getExportUrl());
        TagPrintExportData exportData = new TagPrintExportData(dto);
        List<AbsExportData> exportDataList = ListUtil.of(exportData);
        exportParams.setExportDataList(exportDataList);
        // 同步异步启动
        if (printDataList.size() > 100) {
            exportResponse.setSync(true);
            sync(exportParams, this::executeExport);
        } else {
            exportResponse.setSync(false);
            exportResponse.setPath(async(() ->
                    executeExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(exportResponse.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return exportResponse;
    }

    private String executeExport(Long taskId, ExportParams exportParams) {
        String path = StrUtil.EMPTY;
        File tempPath = getTempPath();
        File outputFile = null;
        try {
            TagPrintExportData printPdfDto = (TagPrintExportData) exportParams.getExportDataList().get(0);
            PrintPdfDto pdfDto = printPdfDto.getPrintPdfDto();

            // 生成标签文档
            List<PrintDataViewNewDto> resultList = printFeignClient.getPrintJson(pdfDto);
            String fileName = getName(resultList.size(), pdfDto.getPrintType());
            outputFile = new File(tempPath.getPath() + "/" + fileName);
            String localPath = outputFile.getPath();

            // json数据转base64
            List<String> pixesBase64List = new ArrayList<>();
            for (PrintDataViewNewDto dataDto : resultList) {
                String dataDtoJsonObject = dataDto.getJsonObject();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("ratio", 11.81);
                jsonObject.put("printRatio", 11.81);
                jsonObject.put("preview", true);
                jsonObject.put("orientation", 0);
                jsonObject.put("printerMargin", ImmutableList.of(0, 0, 0, 0));
                jsonObject.put("printerOffset", ImmutableList.of(0, 0, 0, 0));
                jsonObject.put("printJson", dataDtoJsonObject);
                JSONObject picJsonObject = netalPrintService.netalPrintTransform(jsonObject.toJSONString(), false);
                String pixesBase64 = picJsonObject.getString("pixes");
                pixesBase64List.add(pixesBase64);
            }
            imagesToPdf(pixesBase64List, new File(localPath));
            path = fileUploadService.putFile(getTagPrintDestPath(exportParams, fileName), localPath);
            // 更新任务url
            if (taskId != null && StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("标签打印导出失败, {}", e.getMessage(), e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, e.getMessage());
        } finally {
            FileUtil.del(outputFile);
        }
        return path;
    }

    private void imagesToPdf(List<String> imageBase64List, File pdfFile) {
        // 检查列表是否为空
        if (imageBase64List.isEmpty()) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印标签Pdf列表为空");
        }

        // 解码第一张图片并获取其尺寸
        byte[] firstImageBytes = Base64.getDecoder().decode(imageBase64List.get(0));
        PdfWriter writer = null;
        Document document = null;
        ByteArrayInputStream bis = null;
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(pdfFile);
            bis = new ByteArrayInputStream(firstImageBytes);
            BufferedImage firstImage = ImageIO.read(bis);
            float imageWidth = firstImage.getWidth();
            float imageHeight = firstImage.getHeight();
            // 初始化文档和写入器
            document = new Document(new Rectangle(imageWidth, imageHeight));
            writer = PdfWriter.getInstance(document, fos);
            document.open();
            // 添加第一张图片（已根据页面大小调整）
            Image firstPdfImage = Image.getInstance(firstImageBytes);
            firstPdfImage.setAbsolutePosition(0, 0);
            document.add(firstPdfImage);
            document.newPage();
            // 遍历剩余图像列表并添加到PDF（这里简单缩放以适应已设置的页面大小）
            for (int i = 1; i < imageBase64List.size(); i++) {
                byte[] imageBytes = Base64.getDecoder().decode(imageBase64List.get(i));
                ImageIO.read(new ByteArrayInputStream(imageBytes));
                Image pdfImage = Image.getInstance(imageBytes);
                pdfImage.setAbsolutePosition(0, 0);
                document.add(pdfImage);
                document.newPage();
            }
        } catch (Exception e) {
            log.error("打印标签Pdf生成异常, {}", e.getMessage(), e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印标签Pdf生成异常");
        } finally {
            // 确保文档和写入器被关闭
            if (document != null && document.isOpen()) {
                document.close();
            }
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "PDF文件流异常");
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "PDF文件流异常");
                }
            }
            if (writer != null && !writer.isCloseStream()) {
                writer.close();
            }
        }
    }

    @Getter
    @AllArgsConstructor
    private static class TagPrintExportData extends AbsExportData {

        private PrintPdfDto printPdfDto;
    }


    private String getTagPrintDestPath(ExportParams exportParams, String fileName) {
        return exportParams.getCompanyId() + "/tagPrintPdf" + DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN) + "/" + fileName;
    }


    private String getName(int count, Short printType) {
        String unique = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN) + RandomUtil.randomString(6);
        return getPrintTypeName(printType) + "标签打印(" + count + "张)" + unique + ".pdf";
    }

    private String getTaskName(Short printType) {
        String name = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return getPrintTypeName(printType) + "标签打印" + name;
    }

    private String getPrintTypeName(Short printType) {
        String name = StrUtil.EMPTY;
        if (DictConstant.PRINT_TYPE_ASSET == printType) {
            name = "资产";
        } else if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
            name = "耗材";
        } else if (DictConstant.PRINT_TYPE_AREA == printType) {
            name = "区域";
        }
        return name;
    }

    private File getTempPath() {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", "tagPrint", currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

}
