package com.niimbot.asset.controller.h5;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.OnlineInventoryFeignClient;
import com.niimbot.inventory.InventoryAssetAppQueryDto;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.QueryConditionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "员工管理")
@ResultController
@RequestMapping("api/common/employee")
@RequiredArgsConstructor
public class H5EmployeeController {
    private final CusEmployeeFeignClient feignClient;
    private final OnlineInventoryFeignClient onlineInventoryFeignClient;
    private final CacheResourceUtil cacheResourceUtil;

    @ApiOperation(value = "查询组织员工字典-盘点小程序专用")
    @GetMapping("/query/list")
    public List<Map<String, Object>> queryList(@Validated InventoryAssetAppQueryDto queryDto) {
        List<Map<String, Object>> collect = new ArrayList<>();
        // 获取数据权限
        List<CusEmployeeDto> allList = feignClient.actList(new CusEmployeeQueryDto().setFilterPerm(true));
        List<String> personIdList = new ArrayList<>();
        for (CusEmployeeDto emp : allList) {
            Map<String, Object> map = new HashMap<>(16);
            map.put("label", emp.getEmpName());
            map.put("value", emp.getId());
            map.put("empNo", emp.getEmpNo());
            map.put("relation", emp.getOrgList());
            collect.add(map);

            personIdList.add(emp.getId().toString());
        }

        InventorySurplusQueryDto queryParam = new InventorySurplusQueryDto();
        BeanUtils.copyProperties(queryDto, queryParam);
        QueryConditionDto queryConditionDto = new QueryConditionDto();
        queryConditionDto.setQuery("in");
        queryConditionDto.setQueryData(personIdList);
        queryParam.setConditions(Lists.newArrayList(queryConditionDto));
        if ("managerOwner".equalsIgnoreCase(queryDto.getConditionCode())) {
            queryParam.setGroupByColumn("asset_manager_owner");
            queryConditionDto.setCode("managerOwner");
        } else {
            queryParam.setGroupByColumn("asset_use_person");
            queryConditionDto.setCode("usePerson");
        }
        List<InventoryAssetDataCountDto> assetDataCountDtoList = onlineInventoryFeignClient.appAssetCode(queryParam);
        log.info("cusEmployeeController queryList success! param=[{}] result=[{}]", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(assetDataCountDtoList));
        Map<String, Integer> dataCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(assetDataCountDtoList)) {
            dataCountMap = assetDataCountDtoList.stream().collect(Collectors.toMap(InventoryAssetDataCountDto::getConditionCode, InventoryAssetDataCountDto::getDataCount, (v1, v2) -> v2));
        }

        for (Map<String, Object> item : collect) {
            Object valueData = item.get("value");
            if (valueData != null && dataCountMap.get(((Long) valueData).toString()) != null) {
                item.put("dataCount", dataCountMap.get(((Long) valueData).toString()));
            } else {
                item.put("dataCount", 0);
            }
        }
        // 统计数量为0的分组为其他
        // 如果dataCountMap中id被删除，也应该记录在这里
        dataCountMap.forEach((id, count) -> {
            if (!personIdList.contains(id) && StrUtil.isNotEmpty(id)) {
                Long idLong = Convert.toLong(id);
                Map<String, Object> map = new HashMap<>(16);
                map.put("label", cacheResourceUtil.getUserName(idLong));
                map.put("value", id);
                map.put("empNo", cacheResourceUtil.getUserName(idLong));
                map.put("relation", Collections.emptyList());
                map.put("dataCount", count);
                collect.add(map);
            }
        });
        Integer other = assetDataCountDtoList.stream().filter(v -> StrUtil.isBlank(v.getConditionCode())).map(InventoryAssetDataCountDto::getDataCount).reduce(0, Integer::sum);
        Map<String, Object> map = new HashMap<>(16);
        map.put("label", "其他");
        map.put("value", -1L);
        map.put("empNo", "");
        map.put("dataCount", other);
        map.put("relation", Collections.emptyList());
        collect.add(map);
        return collect;
    }

}
