package com.niimbot.asset.controller.common.todo;

import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.TodoFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.todo.TodoCountDto;
import com.niimbot.todo.TodoPageQueryDto;
import com.niimbot.todo.TodoTimeOutSettingDto;
import com.niimbot.todo.TodoWorkCenterDto;
import com.niimbot.todo.TodoWorkCenterV2Dto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Objects;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * 待办事项
 *
 * <AUTHOR>
 * @date 2021/7/12 10:29
 */
@Api(tags = "待办事项管理")
@ResultController
@RequestMapping("api/common/todo")
@RequiredArgsConstructor
public class TodoController {
    private final TodoFeignClient todoFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;

    @ApiOperation(value = "超时设置查询")
    @GetMapping("/timeout")
    public TodoTimeOutSettingDto getTimeoutSetting() {
        return todoFeignClient.getTimeoutSetting();
    }

    @ApiOperation(value = "超时设置修改")
    @PutMapping("/timeout")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean timeoutSetting(@RequestBody TodoTimeOutSettingDto timeOutSettingDto) {
        return todoFeignClient.timeoutSetting(timeOutSettingDto);
    }

    @ApiOperation(value = "我的待办事项分页查询")
    @GetMapping("/pageMy")
    @AutoConvert
    public PageUtils<TodoWorkCenterDto> pageMy(TodoPageQueryDto query) {
        return todoFeignClient.pageMy(query);
    }

    @ApiOperation(value = "我的待办事数量查询")
    @GetMapping("/count")
    public TodoCountDto getTodoCount() {
        return todoFeignClient.getTodoCount(new TodoPageQueryDto());
    }

    @ApiOperation(value = "待办中心待办事项")
    @GetMapping("/workCenter")
    @Deprecated
    @AutoConvert
    public TodoWorkCenterDto workCenter() {
        TodoWorkCenterDto result = todoFeignClient.workCenter();
        //单据id和单据类型不为空的时候，查询下流程相关信息，用于审批前端传参，盘点单据是没有流程实例信息
        if (Objects.nonNull(result.getBusinessId()) && Objects.nonNull(result.getOrderType())) {
            WorkflowApproveInfoDto approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo(result.getOrderType(), result.getBusinessId());
            if (approveInfoDto == null) {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo(result.getOrderType(), result.getBusinessId());
            }

            result.setApproveInfo(approveInfoDto);
        }
        return result;
    }

    @ApiOperation(value = "待办中心待办事项")
    @GetMapping("/workCenter/v2")
    @AutoConvert
    public TodoWorkCenterV2Dto workCenterV2(@RequestParam(value = "limit", required = false, defaultValue = "5") Integer limit) {
        return todoFeignClient.workCenterV2(limit);
    }

}
