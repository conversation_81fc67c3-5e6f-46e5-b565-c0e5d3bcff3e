package com.niimbot.asset.service.feign;

import com.niimbot.means.OrderFieldDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 审批流条件字段
 *
 * <AUTHOR>
 * @date 2021/6/22 18:12
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ActWorkflowFieldFeignClient {
    /**
     * 审批流选择单据条件
     *
     * @param orderType 单据类型
     * @return 审批流分支条件
     */
    @GetMapping(value = "server/activiti/field/branchCondition/{orderType}")
    List<OrderFieldDto> branchCondition(@PathVariable("orderType") Integer orderType);

}
