package com.niimbot.asset.controller.common.system;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.autoconfig.StsPropertiesConfig;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.ResultUtils;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.EmailCodeConsumerContext;
import com.niimbot.asset.service.SmsCodeConsumerContext;
import com.niimbot.asset.service.feign.AsCountryCodeFeignClient;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.asset.service.feign.LoginRecordClient;
import com.niimbot.asset.service.feign.PrivacyAgreementFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.result.Result;
import com.niimbot.kalimdor.heimdallr.HeimdallrKamClient;
import com.niimbot.kalimdor.heimdallr.model.STSTokenRequest;
import com.niimbot.kalimdor.heimdallr.model.STSTokenResponse;
import com.niimbot.system.PrivacyAgreementDto;
import com.niimbot.system.UserPasswordDto;
import com.niimbot.system.crm.CrmPushMessage;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 公共控制器【验证码、短信等】 <AUTHOR>
 *
 * <AUTHOR>
 * @since 2020/10/29 14:02
 */
@Api(tags = {"公共接口"})
@ResultController
@RequestMapping("api/common")
@Validated
@Slf4j
public class CommonController {

    @Resource
    private RedisService redisService;

    @Resource
    private CusUserFeignClient userFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private CompanyFeignClient companyFeignClient;

    @Resource
    private PrivacyAgreementFeignClient privacyAgreementFeignClient;

    @Resource
    private LoginRecordClient loginRecordClient;

    @Resource
    private AsCountryCodeFeignClient countryCodeFeignClient;

    @GetMapping("/captchaImage")
    @ApiOperation(value = "获取验证码", notes = "获取验证码base64和uuid")
    public Map<String, Object> captchaImage() {
        // 返回结果集
        Map<String, Object> result = new HashMap<>();

        // 保存验证码信息
        String uuid = UUID.fastUUID().toString();
        String verifyKey = BaseConstant.CAPTCHA_CODE_KEY + uuid;

        // 圆圈干扰线验证码
        CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(100, 30, 4, 20);
        // 获取验证码值
        String captchaStr = captcha.getCode();
        // 写入Redis缓存
        redisService.set(verifyKey, captchaStr, BaseConstant.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);

        result.put("uuid", uuid);
        result.put("img", captcha.getImageBase64());
        return result;
    }

    /**
     * 获取验证码 type参考 {@link SmsCodeConsumerContext}
     *
     * @param mobile 手机号
     * @param type   获取验证码的用途 【注册】- reg、【忘记密码】- resetPassword  etc...
     * @return 成功-true 失败-false
     */
    @GetMapping("/smsCode")
    @ApiOperation(value = "获取手机验证码", notes = "四位手机验证码")
    public Boolean getSmsVerifyCode(HttpServletRequest request,
                                    @NotBlank(message = "请输入手机号")
                                    @Size(max = 11, message = "手机号最多11位") String mobile,
                                    @NotBlank(message = "请输入区号") String nationalCode,
                                    @NotBlank(message = "请输入获取验证码业务类型") String type) {
        String nonce = request.getHeader("nonce");
        String timestamp = request.getHeader("timestamp");
        String signature = request.getHeader("signature");
        //只校验+86的手机号
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        Integer count = countryCodeFeignClient.queryCode(nationalCode);
        if (count <= 0) {
            throw new BusinessException(SystemResultCode.NOTICE_SETTING_ERROR, "暂未开放此地区，如需开通，请拨打客服电话：4008608800 转2");
        }
        if (!ObjectUtil.isAllNotEmpty(nonce, timestamp, signature)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "缺少签名参数");
        }
        String signatureText = SecurityUtils.decryptRSA(signature);
        try {
            JSONObject signatureJSON = (JSONObject) JSONObject.parse(signatureText);
            // 解密后数据和header不一致报错
            if (!StrUtil.equals(signatureJSON.getString("nonce"), nonce)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "签名nonce错误");
            }
            if (!StrUtil.equals(signatureJSON.getString("timestamp"), timestamp)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "签名timestamp错误");
            }

            String redisKey = "signature:" + mobile + ":" + nonce;
            if (redisService.hasKey(redisKey)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "签名已失效，请重试");
            } else {
                redisService.set("signature:" + mobile + ":" + nonce, nonce, 5, TimeUnit.MINUTES);
            }

            long timeStamp = LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
            long difference = timeStamp - Convert.toLong(timestamp, 0L);

            // 五分钟有效
            if (difference > 300000) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "签名已失效，请重试");
            }
        } catch (BusinessException bx) {
            throw bx;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "签名解密失败");
        }
        // 手机号前置校验
        SmsCodeConsumerContext.checkMobile(type, mobile);
        smsCodeFeignClient.sendSmsCode(nationalCode.replace("+", ""), mobile);
        //注册发送验证码时记录区号与手机号
        String registerKey = "register_key:" +mobile;
        redisService.set(registerKey,nationalCode,6,TimeUnit.MINUTES);
        return true;
    }


    /**
     * 邮箱获取验证码
     *
     * @param email 邮箱
     * @return 成功-true 失败-false
     */
    @GetMapping("/emailCode")
    @ApiOperation(value = "通过邮箱获取验证码【设置/修改密码，邮箱发送验证码】", notes = "四位验证码")
    public Boolean getEmailVerifyCode(@NotBlank(message = "请输入邮箱")
                                          @Pattern(regexp = com.niimbot.validate.ValidationUtils.EMAIL_REG, message = "邮箱格式不正确")
                                          @Size(max = 255, message = "邮箱不得超过255个字符") String email,
                                      @NotBlank(message = "请输入获取验证码业务类型") String type) {
        // 校验邮箱
        EmailCodeConsumerContext.checkEmail(type, email);
        smsCodeFeignClient.sendCommonCode("email", email);
        return true;
    }

    @ApiOperation(value = "校验验证码")
    @GetMapping("/checkSmsCode")
    public void checkSmsCode(@NotBlank(message = "请输入手机号")
                                 @Size(max = 11, message = "手机号最多11位") String mobile,
                             @NotBlank(message = "请输入区号") String nationalCode,
                                @Size(min = 4, max = 4, message = "验证码为4位")
                                @NotBlank(message = "验证码不能为空") String smsCode) {
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        if (!smsCodeFeignClient.checkSmsCode(mobile, smsCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
    }

    @ApiOperation(value = "校验手机验证码、邮箱验证码")
    @GetMapping("/checkCode")
    public void checkCode(
            @NotBlank(message = "手机号/邮箱不能为空")
            String addr,
            @NotBlank(message = "请输入区号") String nationalCode,
            @Size(min = 4, max = 4, message = "验证码为4位")
            @NotBlank(message = "验证码不能为空") String code) {
        NationalCodeValidate.checkCNMobile(nationalCode,addr);
        if ((!addr.contains("@") && addr.length() > 11) || (addr.contains("@") && !Validator.isEmail(addr))) {
            throw new BusinessException(SystemResultCode.USER_MOBILE_EMAIL_INVALID);
        }
        if (!smsCodeFeignClient.checkSmsCode(addr, code)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
    }

    @ApiOperation(value = "修改用户密码")
    @PutMapping("/user/password")
    public Boolean changeUserPassword(@RequestBody @Validated UserPasswordDto dto) {
        String oldPassword = SecurityUtils.decryptPassword(dto.getOldPassword());
        String newPassword = SecurityUtils.decryptPassword(dto.getNewPassword());
//        ValidationUtils.checkPassword(SystemResultCode.USER_PASSWORD_ERROR, oldPassword, newPassword);
        String rawPassword = userFeignClient.checkPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, rawPassword)) {
            throw new BusinessException(SystemResultCode.USER_OLD_PASSWORD_ERROR);
        }
        return userFeignClient.changeCurrentUserPassword(newPassword);
    }

    @ApiOperation(value = "初始化同步CRM线索")
    @PostMapping("/pushClue")
    public void pushClue() {
        companyFeignClient.pushClue();
    }


    @ApiOperation(value = "测试推送")
    @PostMapping("/pushClueTest")
    public void pushClueTest(@RequestBody CrmPushMessage messageDto) {
        companyFeignClient.pushClueTest(messageDto);
    }


    @ApiOperation(value = "查询最新隐私协议")
    @GetMapping("/lastPrivacyAgreement/{platform}")
    public PrivacyAgreementDto lastPrivacyAgreement(@PathVariable("platform") Integer platform) {
        PrivacyAgreementDto agreementDto = privacyAgreementFeignClient.last(platform);
        return agreementDto != null ? agreementDto :
                new PrivacyAgreementDto().setPlatform(platform).setVersionNo(0);
    }

    @ApiOperation(value = "数据埋点授权获取")
    @GetMapping("/stsToken")
    public Result stsToken(HttpServletRequest request, HttpServletResponse response) {
        try {
            if (!Edition.isLocal()) {
                StsPropertiesConfig stsPropertiesConfig = SpringUtil.getBean(StsPropertiesConfig.class);
                STSTokenRequest tokenRequest = new STSTokenRequest(stsPropertiesConfig.getAppKey(),
                    stsPropertiesConfig.getPolicy(), Long.parseLong(String.valueOf(stsPropertiesConfig.getExpire())));

                HeimdallrKamClient heimdallrKamClient = SpringUtil.getBean(HeimdallrKamClient.class);
                STSTokenResponse stsTokenResponse = heimdallrKamClient.stsToken(tokenRequest);
                return Result.ofSuccess(stsTokenResponse);
            } else {
                return Result.ofSuccess(null);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultUtils.parseFailure(SystemResultCode.INTERNAL_SERVER_ERROR, request, response);
        }
    }

    @GetMapping("/isFirstLogin")
    public Boolean isFirstLogin() {
        return loginRecordClient.isFirstLogin();
    }

}
