package com.niimbot.asset.controller.pc.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AppActivateConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.service.feign.AppActivateFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AsCusUserSettingDto;
import com.niimbot.system.AsToolboxDto;
import com.niimbot.system.AssetAdvertiseDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/10 上午11:44
 */
@Api(tags = "Pc首页布局管理")
@ResultController
@RequestMapping("api/pc/homeLayoutSetting")
@Validated
@RequiredArgsConstructor
public class HomeLayoutSettingController {
    private final CusUserSettingFeignClient cusUserSettingFeignClient;
    private final AbstractFileUtils fileUtils;
    private final AppActivateFeignClient appActivateFeignClient;

    @Value("${asset.homeLayout}")
    private List<Integer> homeLayout;

    @ApiOperation(value = "Pc首页布局编辑")
    @PutMapping
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editPcHome(@RequestBody @NotEmpty(message = "首页布局不能隐藏所有模块") List<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            if (!homeLayout.contains(Integer.parseInt(String.valueOf(map.get("id"))))) {
                throw new BusinessException(SystemResultCode.HOME_LAYOUT_CONTAINS_NOT_EXISTS_PAGE);
            }
        }
        return cusUserSettingFeignClient.updatePcHome(new AsCusUserSettingDto().setPcHome(list));
    }

    @ApiOperation(value = "Pc首页布局列表")
    @GetMapping
    public List<Map<String, Object>> getPcHome() {
        List<Map<String, Object>> pcHome = cusUserSettingFeignClient.getPcHome();
        if (CollUtil.isEmpty(pcHome)) {
            if (Edition.isLocal()) {
                return ListUtil.of(
                        MapUtil.of("id", 1),
                        MapUtil.of("id", 3),
                        MapUtil.of("id", 6),
                        MapUtil.of("id", 9),
                        MapUtil.of("id", 10)
                );
            } else {
                return ListUtil.of(
                        MapUtil.of("id", 1),
                        MapUtil.of("id", 2),
                        MapUtil.of("id", 4),
                        MapUtil.of("id", 3),
                        MapUtil.of("id", 11),
                        MapUtil.of("id", 8),
                        MapUtil.of("id", 7),
                        MapUtil.of("id", 10),
                        MapUtil.of("id", 9),
                        MapUtil.of("id", 6)
                );
            }
        } else {
            return pcHome;
        }
    }

    @ApiOperation(value = "编辑工具箱", notes = "请将元素数据修改排序后传回,position字段表示显示位置 1 -工具箱  2-常用工具")
    @PostMapping("/toolbox/edit")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @NotEmpty List<AsToolboxDto> list) {
        return cusUserSettingFeignClient.saveOrUpdate(new AsCusUserSettingDto().setPcToolbox(list));
    }

    @ApiOperation(value = "工具箱列表", notes = "position字段表示显示位置 1 -工具箱  2-常用工具")
    @GetMapping("/toolbox/list")
    public List<AsToolboxDto> get() {
        List<AsToolboxDto> toolBoxList = cusUserSettingFeignClient.getUserPcToolBox();
        //判断当前企业是否开通耗材盘点
        List<String> activeAppList = appActivateFeignClient.configStatus();
        if (!activeAppList.contains(AppActivateConstant.MATERIAL_INVENTORY)) {
            toolBoxList = toolBoxList.stream()
                    .filter(item1 -> !"1010".equals(Convert.toStr(item1.getOrderType()))).collect(Collectors.toList());

        }
        toolBoxList.forEach(toolBox -> {
            String toolboxIcon = toolBox.getToolboxIcon();
            toolBox.setToolboxIcon(fileUtils.convertToDownloadUrl(toolboxIcon));
        });
        return toolBoxList;
    }

    @ApiOperation(value = "pc首页广告")
    @GetMapping("/advertiseUrl")
    public List<AssetAdvertiseDto> advertiseUrl() {
        return cusUserSettingFeignClient.advertiseUrl(2);
    }
}
