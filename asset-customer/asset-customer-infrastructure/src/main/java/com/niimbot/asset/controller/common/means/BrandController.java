package com.niimbot.asset.controller.common.means;

import com.niimbot.asset.service.feign.BrandFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AsBrandDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/8 15:55
 */
@Api(tags = "品牌管理")
@ResultController
@RequestMapping("api/common/brand")
public class BrandController {

    private final BrandFeignClient brandFeignClient;

    @Autowired
    public BrandController(BrandFeignClient brandFeignClient) {
        this.brandFeignClient = brandFeignClient;
    }

    @ApiOperation(value = "品牌搜索")
    @GetMapping(value = "/search")
    public List<AsBrandDto> search(
            @ApiParam(name = "name", value = "品牌名称")
            @RequestParam(value = "name", required = false) String name) {
        return brandFeignClient.search(name);
    }

}
