package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetQueryFieldService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 杂项Excel导出
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MiscExcelExportService extends AbstractExcelExportService {

    @Resource
    private AssetQueryFieldService queryFieldService;

    @Resource
    private AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @Resource
    private FormFeignClient formFeignClient;

    @Resource
    private StandardFeignClient standardFeignClient;

    @Resource
    private AsAssetUtil asAssetUtil;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private FileUploadConfig fileUploadConfig;
    private final ImportTaskFeignClient importTaskFeignClient;

    protected MiscExcelExportService(ImportTaskFeignClient importTaskFeignClient) {
        super(importTaskFeignClient);
        this.importTaskFeignClient = importTaskFeignClient;
    }

    public ExportResponse meansRkListExport(String taskName, Object query, List<MeansRkData> data) {
        if (CollUtil.isNotEmpty(data)) {
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(data.stream()
                    .map(MeansRkData::getData).flatMap(Collection::stream).collect(Collectors.toList()),
                    SensitiveObjectTypeEnum.ASSET.getCode());
        }
        // 导出参数
        ExportParams exportParams = new ExportParams();
        exportParams.setTaskName(taskName);
        exportParams.setOrderType(200);
        exportParams.setCompanyId(LoginUserThreadLocal.getCompanyId());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(query)));
        exportParams.setExportDataList(data.stream().map(v -> (AbsExportData) v).collect(Collectors.toList()));
        ExportResponse exportResponse = new ExportResponse();
        if (data.size() > 1000) {
            exportResponse.setSync(true);
            sync(exportParams, this::export);
        } else {
            exportResponse.setSync(false);
            exportResponse.setPath(async(() -> this.export(null, exportParams)));
        }
        // 导出结果，默认为异步
        return exportResponse;
    }

    private List<FormFieldCO> handleData(Long standardId) {
        // 查询固定属性
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (standardId != null && standardId != -1L) {
            List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(standardExtField);
        }
        FormFieldCO createTime = new FormFieldCO();
        createTime.setFieldCode(QueryFieldConstant.FIELD_CREATE_TIME);
        createTime.setFieldName("创建时间");
        createTime.setFieldType(FormFieldCO.DATETIME);
        createTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(createTime);

        FormFieldCO updateTime = new FormFieldCO();
        updateTime.setFieldCode(QueryFieldConstant.FIELD_UPDATE_TIME);
        updateTime.setFieldName("更新时间");
        updateTime.setFieldType(FormFieldCO.DATETIME);
        updateTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(updateTime);

        FormFieldCO lastPrintTime = new FormFieldCO();
        lastPrintTime.setFieldCode(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME);
        lastPrintTime.setFieldName("最近打印时间");
        lastPrintTime.setFieldType(FormFieldCO.DATETIME);
        lastPrintTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(lastPrintTime);
        return formFields;
    }

    private String export(Long taskId, ExportParams exportParams) {
        String path = null;
        List<AssetHeadDto> headDtos = queryFieldService.assetHeadView();
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_HEAD);
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        Long standardId = -1L;
        if (generalDto.getStandardId() != null) {
            standardId = generalDto.getStandardId();
        }
        List<FormFieldCO> formFieldCOS = handleData(standardId);
        Map<String, String> codeTypeMap = new HashMap<>();
        headDtos = headDtos.stream()
                .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                .peek(f -> codeTypeMap.put(f.getCode(), f.getType()))
                .collect(Collectors.toList());
        // 查询head
        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
        for (AssetHeadDto head : headDtos) {
            if (QueryFieldConstant.ASSET_FIELD_STATUS.equals(head.getCode())) {
                headerData.put(head.getCode() + "Text", head.getName());
            } else if (!QueryFieldConstant.FIELD_CREATE_TIME.equals(head.getCode())) {
                headerData.put(head.getCode(), head.getName());
            }
        }
        headerData.put(QueryFieldConstant.FIELD_CREATE_TIME, "入库日期");
        // 获取临时存放路径
        File tempPath = getTempPath(exportParams);
        File outputFile = null;
        try {
            // 本地文件路径
            String localPath = "";
            for (AbsExportData datum : exportParams.getExportDataList()) {
                MeansRkData meansRkData = (MeansRkData) datum;
                String excelName = exportParams.getTaskName() + "-" + meansRkData.getSerialNo() + ".xlsx";
                outputFile = new File(tempPath.getPath() + "/" + excelName);
                localPath = outputFile.getPath();

                int headLen = headerData.size();
                ExcelWriter writer = ExcelUtil.getWriter(true);
                Sheet sheet = writer.getSheet();
                // 设置边框
                StyleSet styleSet = writer.getStyleSet();
                styleSet.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
                styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.TOP);

                // 设置表头的cellStyle
                CellStyle cellStyle = writer.createCellStyle();
                cellStyle.setBorderRight(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);

                // 写入文件标题
                writer.merge(0, 0, 0, headLen - 1, "资产入库单", false);
                Cell title = writer.getCell(0, 0);
                CellStyle commentStyle = writer.createCellStyle();
                commentStyle.setAlignment(HorizontalAlignment.CENTER);
                commentStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
                title.setCellStyle(commentStyle);

                // 写入表头
                AtomicInteger rowIdx = new AtomicInteger(1);
                List<String> headCodeList = new ArrayList<>();
                AtomicInteger cellIdx = new AtomicInteger(0);
                Row header = writer.getOrCreateRow(rowIdx.getAndIncrement());
                headerData.forEach((k, v) -> {
                    int idx = cellIdx.getAndIncrement();
                    headCodeList.add(k);
                    Cell cell = header.createCell(idx);
                    cell.setCellStyle(cellStyle);
                    cell.setCellValue(v);
                    // 调整每一列宽度
                    sheet.autoSizeColumn((short) idx);
                    // 解决自动设置列宽中文失效的问题
                    sheet.setColumnWidth(idx, sheet.getColumnWidth(idx) * 17 / 10);
                });
                List<JSONObject> data = meansRkData.getData();
                asAssetUtil.translateAssetJsonFormatBatch(data, formFieldCOS, true);
                data.forEach(f -> {
                    int idx = rowIdx.getAndIncrement();
                    Row row = writer.getOrCreateRow(idx);
                    for (int i = 0; i < headCodeList.size(); i++) {
                        String code = headCodeList.get(i);
                        Cell cell = row.createCell(i);
                        cell.setCellStyle(cellStyle);
                        if (FormFieldCO.NUMBER_INPUT.equals(codeTypeMap.get(code))) {
                            Double num = Convert.toDouble(f.get(code));
                            if (num != null) {
                                cell.setCellValue(num);
                            } else {
                                String numStr = Convert.toStr(f.get(code));
                                if (StrUtil.isNotEmpty(numStr)) {
                                    cell.setCellValue(numStr);
                                }
                            }
                        } else {
                            String str = Convert.toStr(f.get(code));
                            if (StrUtil.isNotEmpty(str)) {
                                cell.setCellValue(str);
                            }
                        }
                    }
                });

                // 写入尾部
                int foot = rowIdx.get();
                writer.merge(foot, foot + 3, 0, headLen - 1, "入库须知（备注）：", false);
                rowIdx.set(foot + 3);
                rowIdx.incrementAndGet();
                int splitNum = headLen / 3;
                writer.merge(rowIdx.get(), rowIdx.get(), 0, splitNum - 1, "采购员（签字）：", false);
                writer.merge(rowIdx.get(), rowIdx.get(), splitNum, splitNum * 2 - 1, "管理员（签字）：", false);
                writer.merge(rowIdx.get(), rowIdx.get(), splitNum * 2, headLen - 1, "验收人员（签字）：", false);
                rowIdx.incrementAndGet();
                writer.merge(rowIdx.get(), rowIdx.get(), 0, splitNum - 1, "入账公司：", false);
                writer.merge(rowIdx.get(), rowIdx.get(), splitNum, headLen - 1, "验收意见：", false);

                //设置输出文件路径
                writer.setDestFile(outputFile);
                writer.close();
            }
            String key = null;
            if (CollUtil.isEmpty(exportParams.getExportDataList())) {
                key = exportParams.getTaskName() + ".xlsx";
            } else {
                key = exportParams.getTaskName() + "-" + ((MeansRkData) exportParams.getExportDataList().get(0)).getSerialNo() + ".xlsx";
            }
            // 多个需要压缩成zip
            if (exportParams.getExportDataList().size() > 1) {
                File zip = ZipUtil.zip(tempPath);
                localPath = zip.getPath();
                key = exportParams.getTaskName() + ".zip";
            }
            key = exportParams.getCompanyId() + "/" + "store_record/" + "means/" + DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN) + "/" + key;
            // 上传文件到oss 单个文件或者zip包
            path = fileUploadService.putFile(key, localPath);
            // 更新任务url
            if (taskId != null && StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(path);
                this.importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("exportData======", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        } finally {
            FileUtil.del(outputFile);
        }
        return path;
    }

    private File getTempPath(ExportParams exportParams) {
        String currentTime = cn.hutool.core.date.DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()),
                "excelTemp/store/record", exportParams.getCompanyId().toString(), exportParams.getOrderType().toString(), currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class MeansRkData extends AbsExportData {

        private String serialNo;

        private List<JSONObject> data;

    }

}
