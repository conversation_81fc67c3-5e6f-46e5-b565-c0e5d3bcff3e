package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.InvoiceRecordFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.sale.InvoiceRecordApplyDto;
import com.niimbot.sale.InvoiceRecordDetailDto;
import com.niimbot.sale.InvoiceRecordDto;
import com.niimbot.sale.InvoiceRecordQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/3/21 21:23
 */
@Api(tags = "开票记录")
@ResultController
@RequestMapping("api/common/invoiceRecord")
@RequiredArgsConstructor
public class InvoiceRecordController {
    private final InvoiceRecordFeignClient invoiceRecordFeignClient;

    @ApiOperation(value = "开票申请")
    @PostMapping
    @RepeatSubmit
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean apply(@RequestBody @Validated InvoiceRecordApplyDto applyDto) {
        return invoiceRecordFeignClient.apply(applyDto);
    }

    @ApiOperation(value = "开票记录分页查询")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<InvoiceRecordDto> page(InvoiceRecordQueryDto query) {
        return invoiceRecordFeignClient.page(query);
    }

    @ApiOperation(value = "开票记录详情查询")
    @GetMapping("/detail/{id}")
    @AutoConvert
    public InvoiceRecordDetailDto detail(@PathVariable("id") Long id) {
        return invoiceRecordFeignClient.detail(id);
    }
}
