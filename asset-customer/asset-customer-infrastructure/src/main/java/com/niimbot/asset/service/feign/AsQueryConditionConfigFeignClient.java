package com.niimbot.asset.service.feign;

import com.niimbot.system.QueryConditionConfigDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022/9/5 11:37
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AsQueryConditionConfigFeignClient {

    @GetMapping("server/system/queryCondition/config/{type}")
    QueryConditionConfigDto getByType(@PathVariable("type") String type);

    @GetMapping("server/system/queryCondition/config/terminal/{type}")
    QueryConditionConfigDto getByType(@PathVariable("type") String type,
                                      @RequestParam("terminal") String terminal);

    @PostMapping("server/system/queryCondition/config")
    Boolean saveOrUpdate(QueryConditionConfigDto queryCondition);

}
