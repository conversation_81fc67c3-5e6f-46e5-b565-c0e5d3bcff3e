package com.niimbot.asset.service.feign;

import com.niimbot.activiti.*;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.inventory.MaterialOrderPreCheckDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/2/23 18:02
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ActWorkflowFeignClient {

    /**
     * 查询流程数据列表
     *
     * @param activitiKey 流程Key
     * @return 流程数据
     */
    @GetMapping(value = "server/activiti/workflow/list/{activitiKey}")
    List<ActWorkflowDto> list(@PathVariable("activitiKey") String activitiKey);

    /**
     * 查询流程数据详情
     *
     * @param id 流程Id
     * @return 流程数据
     */
    @GetMapping(value = "server/activiti/workflow/{flowId}")
    ActWorkflowGraphDto getInfo(@PathVariable("flowId") Long flowId);

    @PostMapping(value = "server/activiti/workflow")
    Boolean addFlow(ActWorkflowDto actWorkflowDto);

    @PutMapping(value = "server/activiti/workflow")
    Boolean editFlow(ActWorkflowDto actWorkflowDto);

    @PutMapping(value = "server/activiti/workflow/status")
    Boolean updateStatus(ActWorkflowDto workflowDto);

    @DeleteMapping(value = "server/activiti/workflow/{flowId}")
    Boolean delete(@PathVariable("flowId") Long flowId);

    /**
     * 流程部署
     *
     * @param workflowDto 流程对象
     * @return
     */
    @PutMapping("server/activiti/workflow/saveAndDeploy")
    Boolean saveAndDeploy(ActWorkflowGraphDto graphDto);

    /**
     * 审批通过
     *
     * @param approveDto 审批信息
     * @return
     */
    @PutMapping("server/activiti/workflow/approved")
    Boolean approved(@RequestBody WorkflowApproveDto approveDto);

    /**
     * 审批驳回
     *
     * @param approveDto 审批信息
     * @return
     */
    @PutMapping("server/activiti/workflow/rejected")
    Boolean rejected(@RequestBody WorkflowApproveDto approveDto);

    /**
     * 流程撤销
     *
     * @param processInstanceId 实例id
     * @return
     */
    @PutMapping("server/activiti/workflow/revoked/{processInstanceId}")
    Boolean revoked(@PathVariable("processInstanceId") String processInstanceId);

    /**
     * 流程转办
     *
     * @param forwardDto 转办信息
     * @return
     */
    @PutMapping("server/activiti/workflow/forward")
    Boolean forward(@RequestBody WorkflowForwardDto forwardDto);

    /**
     * 获取运行时流程执行信息
     *
     * @param type       业务类型
     * @param businessId 业务id
     * @return
     */
    @GetMapping("server/activiti/workflow/runWorkflowApproveInfo/{type}/{businessId}")
    WorkflowApproveInfoDto getRunWorkflowApproveInfo(@PathVariable("type") Short type,
                                                     @PathVariable("businessId") Long businessId);

    /**
     * 获取历史流程执行信息
     *
     * @param type       业务类型
     * @param businessId 业务id
     * @return
     */
    @GetMapping("server/activiti/workflow/hiWorkflowApproveInfo/{type}/{businessId}")
    WorkflowApproveInfoDto getHiWorkflowApproveInfo(@PathVariable("type") Short type,
                                                    @PathVariable("businessId") Long businessId);

    /**
     * 是否配置审批流
     *
     * @param key 流程标识
     * @return
     */
    @GetMapping("server/activiti/workflow/hasWorkflow/{key}")
    Boolean hasWorkflow(@PathVariable("key") String key);

    /**
     * 审批状态翻译带人名
     * @param convertDto
     * @return
     */
    @PostMapping("server/activiti/workflow/workflowConvert")
    WorkflowConvertDto workflowConvert(@RequestBody WorkflowConvertDto convertDto);

    /**
     * 我的流程申请
     * @param query
     * @return
     */
    @GetMapping(value = "server/activiti/workflow/my")
    PageUtils<ActWorkflowRequestViewDto> pageMyRequest(@SpringQueryMap ActWorkflowRequestQueryDto query);

    /**
     * 我的流程申请数量
     * @return
     */
    @GetMapping(value = "server/activiti/workflow/my/count")
    WorkflowCountDto countMyWorkflow();

    /**
     * 我的流程抄送
     * @param query
     * @return
     */
    @GetMapping(value = "server/activiti/workflow/copy/my")
    PageUtils<ActWorkflowCopyViewDto> pageMyCopy(@SpringQueryMap ActWorkflowCopyQueryDto query);

    /**
     * 流程抄送抄送已读
     *
     * @param processInstanceId 实例id
     * @return
     */
    @PutMapping("server/activiti/workflow/copy/read/{processInstanceId}")
    Boolean copyRead(@PathVariable("processInstanceId") String processInstanceId);

    /**
     * 流程抄送抄送全部已读
     *
     * @return
     */
    @PutMapping("server/activiti/workflow/copy/read/all")
    Boolean copyReadAll();

    @PutMapping("server/activiti/workflow/batch/transfer")
    Boolean batchWorkflowTransfer(@RequestBody List<WorkflowTransfer> transfers);

    @GetMapping("server/activiti/workflow/checkWorkflow/{userId}")
    Boolean checkWorkflow(@PathVariable("userId") Long userId);

    /**
     * 校验员工是否存在
     * @param approveDto
     * @return
     */
    @PostMapping("server/activiti/workflow/verifyEmployeeExist")
    String verifyEmployeeExist(@RequestBody WorkflowApproveDto approveDto);

    @GetMapping("server/activiti/workflow/signature/authCode")
    ActWorkflowSignAuthCodeDto signAuthCode(@RequestParam("orderId") Long orderId);

    @GetMapping("server/activiti/workflow/signature/authCode/link")
    ActWorkflowSignAuthCodeDto signAuthCodeLink(@RequestParam("authCode") String authCode);

    @PostMapping("server/activiti/workflow/signature/authCode/verify")
    ActWorkflowSignAuthCodeDto signAuthCodeVerify(@RequestParam("authCode") String authCode);

    @PostMapping("server/activiti/workflow/signature/submit")
    ActWorkflowSignSubmitDto signatureSubmit(@RequestBody ActWorkflowSignSubmitDto submitDto);

    @PostMapping("/server/material/inventory/order/preCheck")
    MaterialOrderPreCheckDto materialOrderPreCheck(@RequestBody WorkflowApproveDto approveDto);
}
