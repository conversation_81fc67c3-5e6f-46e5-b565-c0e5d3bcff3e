package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.DateUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.service.AbstractAssetService;
import com.niimbot.asset.service.InventoryAssetService;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.inventory.InventoryGroupReportExcelDto;
import com.niimbot.inventory.InventoryRangeGroupDto;
import com.niimbot.inventory.InventoryRangeRecordDto;
import com.niimbot.inventory.InventoryReportDto;
import com.niimbot.inventory.NewInventoryResReportDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/12/15 16:45
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryAssetServiceImpl extends AbstractAssetService implements InventoryAssetService {
    private final RedisService redisService;
    private final InventoryFeignClient inventoryFeignClient;
    private final StandardFeignClient standardFeignClient;
    private final CacheResourceUtil cacheResourceUtil;

    @Override
    public List<FormFieldCO> buildAttrCache(List<FormFieldCO> formFields, Long assetFormId, Long standardId) {
        // 标准品属性
        List<FormFieldCO> standardExtField = new ArrayList<>();
        if (standardId != null) {
            standardExtField = standardFeignClient.getStandardExtField(assetFormId, standardId);
        }

        formFields.addAll(standardExtField);
        formFields = formFields.stream().filter(f -> !f.isHidden()).collect(Collectors.toList());
        return formFields;
    }

    /**
     * 写入盘点概述
     *
     * @return excel
     */
    @Override
    public ExcelWriter buildInventoryViewExcel(Long id) {
        Object status = redisService.get(BaseConstant.SYS_DICT_KEY + "inventory_status");
        Map<Integer, String> statusMap = new HashMap<>();
        if(ObjectUtil.isNotEmpty(status)){
            List<JSONObject> assetMarkList = (List<JSONObject>) status;
            statusMap = assetMarkList.stream().collect(Collectors.toMap(
                    k -> k.getInteger("value"), k -> k.getString("label"),
                    (k1, k2) -> k1));
        }
        // 获取盘点报告数据
        InventoryReportDto inventoryReport = inventoryFeignClient.getInventoryReport(id);

        // 创建人
        inventoryReport.setCreateUserText(cacheResourceUtil.getUserNameAndCode(inventoryReport.getCreateBy()));
        // 审核人
        inventoryReport.setApproverText(cacheResourceUtil.getUserNameAndCode(inventoryReport.getApprover()));
        // 创建时间
        String createTime = DateUtils.format(inventoryReport.getCreateTime(), DatePattern.NORM_DATE_PATTERN);
        // 完成时间
        String completeTime = "";
        if (inventoryReport.getCompleteTime() != null) {
            completeTime = DateUtils.format(inventoryReport.getCompleteTime(), DatePattern.NORM_DATE_PATTERN);
        }

        // 获取 writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 获取 sheet
        Sheet sheet = writer.getSheet();
        // 获取 styleSet
        StyleSet styleSet = writer.getStyleSet();
        // 设置边框
        styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
//        styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);

        // 获取基本信息和盘点范围的cellStyle
        CellStyle baseStyle = writer.createCellStyle();
        baseStyle.setAlignment(HorizontalAlignment.LEFT);
        baseStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        Font grey = writer.createFont();
        grey.setFontHeightInPoints((short) 12);
//        grey.setColor(IndexedColors.GREY_40_PERCENT.getIndex());
        baseStyle.setFont(grey);

        // 获取大标题的cellStyle
        CellStyle titleStyle = writer.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.LEFT);
        titleStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        Font blue = writer.createFont();
        blue.setFontHeightInPoints((short) 14);
        blue.setColor(IndexedColors.BLUE.getIndex());
        blue.setBold(true);
        titleStyle.setFont(blue);

        // 获取加粗的cellStyle
        CellStyle blodStyle = writer.createCellStyle();
        blodStyle.setAlignment(HorizontalAlignment.LEFT);
        blodStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        Font blod = writer.createFont();
        blod.setFontHeightInPoints((short) 12);
        blod.setBold(true);
        blodStyle.setFont(blod);

        // 写入基本信息
        writer.merge(0, 0, 0, 26, "盘点单名称：" + inventoryReport.getName(), false)
                .merge(1, 1, 0, 26, "一、盘点单基本信息", false)
                .writeCellValue(0, 2, "盘点单名称：" + inventoryReport.getName())
                .writeCellValue(1, 2, "盘点单状态：" + statusMap.getOrDefault(inventoryReport.getStatus(), ""))
                .writeCellValue(0, 3, "盘点规则：" + inventoryReport.getRules())
                .merge(4, 4, 0, 26, "盘点单备注：" + inventoryReport.getRemark(), false)
                .writeCellValue(0, 5, "创建人：" + inventoryReport.getCreateUserText())
                .writeCellValue(1, 5, "创建时间：" + createTime)
                .writeCellValue(2, 5, "审核人：" + inventoryReport.getApproverText())
                .writeCellValue(3, 5, "完成时间：" + completeTime)
                .merge(6, 6, 0, 26, "二、盘点范围", false);
        writer.setStyle(titleStyle, "A" + 1);
        writer.setStyle(titleStyle, "A" + 2);
        writer.setStyle(titleStyle, "A" + 7);
        IntStream.range(3, 7).forEach(i -> writer.setStyle(baseStyle, "A" + i));
        writer.setStyle(baseStyle, "B" + 3);
        writer.setStyle(baseStyle, "B" + 6);
        writer.setStyle(baseStyle, "C" + 6);
        writer.setStyle(baseStyle, "D" + 6);

        int resRowNum = 7;
        List<InventoryRangeGroupDto> inventoryGroups = inventoryReport.getConfig().getInventoryGroup();
        if(CollUtil.isNotEmpty(inventoryGroups)){
            for (int i = 0; i < inventoryGroups.size(); i++) {
                InventoryRangeGroupDto groupDto = inventoryGroups.get(i);
                List<String> groupText = new ArrayList<>();
                List<InventoryRangeRecordDto> inventoryRange = groupDto.getInventoryRange();
                if (CollUtil.isEmpty(inventoryRange)) {
                    writer.merge(resRowNum, resRowNum, 0, 26, "全部范围", false);
                } else {
                    for (int j = 0; j < inventoryRange.size(); j++) {
                        InventoryRangeRecordDto inventoryRangeDto = inventoryRange.get(j);
                        String val = "";
                        if(ObjectUtil.isNotNull(inventoryRangeDto.getValueText())){
                            val = StringUtils.join("，", inventoryRangeDto.getValueText());
                        }
                        groupText.add(inventoryRangeDto.getName() + "：" + val);
                    }
                    writer.merge(resRowNum, resRowNum, 0, 26, String.join("、", groupText), false);
                }
                writer.setStyle(baseStyle, "A" + (resRowNum + 1));
                resRowNum++;
            }
        }
        // 标题
        writer.merge(resRowNum, resRowNum, 0, 26, "三、盘点情况概述", false)
                .merge(resRowNum + 1, resRowNum + 1, 0, 26, "1、盘点总结果", false);
        writer.setStyle(titleStyle, "A" + (resRowNum + 1));
        writer.setStyle(blodStyle, "A" + (resRowNum + 2));
        resRowNum += 2;

        // 盘点结果
        // 盘点总结果表头
        writer.merge(resRowNum, resRowNum + 1, 0, 0, "应盘数量", false)
                .merge(resRowNum, resRowNum, 1, 3, "实盘数量", false)
                .writeCellValue(4, resRowNum, "未盘数量")
                .writeCellValue(1, resRowNum + 1, "正常")
                .writeCellValue(2, resRowNum + 1, "盘盈-在册")
                .writeCellValue(3, resRowNum + 1, "盘盈-不在册")
                .writeCellValue(4, resRowNum + 1, "盘亏");
        resRowNum += 2;

        // 盘点总结果数据
        List<NewInventoryResReportDto> inventoryRes = inventoryReport.getInventoryRes();
        for (NewInventoryResReportDto resReportDto : inventoryRes) {
            writer.writeCellValue(0, resRowNum, resReportDto.getShallNum());
            writer.writeCellValue(1, resRowNum, resReportDto.getNormalNum());
            writer.writeCellValue(2, resRowNum, resReportDto.getPlusMarkNum());
            writer.writeCellValue(3, resRowNum, resReportDto.getPlusNotMarkNum());
            writer.writeCellValue(4, resRowNum, resReportDto.getLossNum());
            resRowNum++;
        }

        // ---------------------- 所属管理组织 ----------------------
        writer.merge(resRowNum, resRowNum, 0, 26, "2、各所属管理组织盘点概况", false);
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum++;
        writer.setCurrentRow(resRowNum);

        List<InventoryGroupReportExcelDto> inventoryOrgOwn = inventoryReport.getInventoryOrgOwn()
                .stream().map(o -> BeanUtil.copyProperties(o, InventoryGroupReportExcelDto.class))
                .collect(Collectors.toList());
        writer.writeRow(this.getInventoryGroupResHead(InventoryConstant.DISPATCH_ORG_OWNER,
                inventoryReport.getConfig().getDispatchMode()).values());
        writer.write(inventoryOrgOwn, false);
        resRowNum += inventoryOrgOwn.size() + 1;
        // ---------------------- 所属管理组织 ----------------------

        // ---------------------- 所属使用组织 ----------------------
        writer.merge(resRowNum, resRowNum, 0, 26, "3、各使用组织织盘点概况", false);
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum++;
        writer.setCurrentRow(resRowNum);

        List<InventoryGroupReportExcelDto> inventoryUseOrg = inventoryReport.getInventoryUseOrg()
                .stream().map(o -> BeanUtil.copyProperties(o, InventoryGroupReportExcelDto.class))
                .collect(Collectors.toList());
        writer.writeRow(this.getInventoryGroupResHead(InventoryConstant.DISPATCH_USE_ORG,
                inventoryReport.getConfig().getDispatchMode()).values());
        writer.write(inventoryUseOrg, false);
        resRowNum += inventoryUseOrg.size() + 1;
        // ---------------------- 所属使用组织 ----------------------

        // ---------------------- 所属管理员 ----------------------
        writer.merge(resRowNum, resRowNum, 0, 26, "4、各资产管理员盘点概况", false);
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum++;
        writer.setCurrentRow(resRowNum);

        List<InventoryGroupReportExcelDto> inventoryManagerOwn = inventoryReport.getInventoryManagerOwn()
                .stream().map(o -> BeanUtil.copyProperties(o, InventoryGroupReportExcelDto.class))
                .collect(Collectors.toList());
        writer.writeRow(this.getInventoryGroupResHead(InventoryConstant.DISPATCH_MANAGER_OWNER,
                inventoryReport.getConfig().getDispatchMode()).values());
        writer.write(inventoryManagerOwn, false);
        resRowNum += inventoryManagerOwn.size() + 1;
        // ---------------------- 所属管理员 ----------------------

        // ---------------------- 资产使用人 ----------------------
        writer.merge(resRowNum, resRowNum, 0, 26, "5、各资产使用人盘点概况", false);
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum++;
        writer.setCurrentRow(resRowNum);

        List<InventoryGroupReportExcelDto> inventoryUsePerson = inventoryReport.getInventoryUsePerson()
                .stream().map(o -> BeanUtil.copyProperties(o, InventoryGroupReportExcelDto.class))
                .collect(Collectors.toList());
        writer.writeRow(this.getInventoryGroupResHead(InventoryConstant.DISPATCH_USE_PERSON,
                inventoryReport.getConfig().getDispatchMode()).values());
        writer.write(inventoryUsePerson, false);
        resRowNum += inventoryUsePerson.size() + 1;
        // ---------------------- 资产使用人 ----------------------

        // ---------------------- 区域 ----------------------
        writer.merge(resRowNum, resRowNum, 0, 26, "6、各区域盘点概况", false);
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum++;
        writer.setCurrentRow(resRowNum);

        List<InventoryGroupReportExcelDto> inventoryArea = inventoryReport.getInventoryArea()
                .stream().map(o -> BeanUtil.copyProperties(o, InventoryGroupReportExcelDto.class))
                .collect(Collectors.toList());
        writer.writeRow(this.getInventoryGroupResHead(InventoryConstant.DISPATCH_STORAGE_AREA,
                inventoryReport.getConfig().getDispatchMode()).values());
        writer.write(inventoryArea, false);
        resRowNum += inventoryArea.size() + 1;
        // ---------------------- 区域 ----------------------

        // ---------------------- 资产分类 ----------------------
        writer.merge(resRowNum, resRowNum, 0, 26, "7、各资产分类盘点概况", false);
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum++;
        writer.setCurrentRow(resRowNum);

        List<InventoryGroupReportExcelDto> inventoryCategory = inventoryReport.getInventoryCategory()
                .stream().map(o -> BeanUtil.copyProperties(o, InventoryGroupReportExcelDto.class))
                .collect(Collectors.toList());
        writer.writeRow(this.getInventoryGroupResHead(InventoryConstant.DISPATCH_ASSET_CATEGORY,
                inventoryReport.getConfig().getDispatchMode()).values());
        writer.write(inventoryCategory, false);
        resRowNum += inventoryCategory.size() + 1;
        // ---------------------- 资产分类 ----------------------

        // ---------------------- 资产状态 ----------------------
        writer.merge(resRowNum, resRowNum, 0, 26, "8、各资产状态盘点概况", false);
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum++;
        writer.setCurrentRow(resRowNum);

        List<InventoryGroupReportExcelDto> inventoryStatus = inventoryReport.getInventoryStatus()
                .stream().map(o -> BeanUtil.copyProperties(o, InventoryGroupReportExcelDto.class))
                .collect(Collectors.toList());
        writer.writeRow(this.getInventoryGroupResHead(0, inventoryReport.getConfig().getDispatchMode()).values());
        writer.write(inventoryStatus, false);
        resRowNum += inventoryStatus.size() + 1;
        // ---------------------- 资产状态 ----------------------

        // ---------------------- 审核人 ----------------------
        writer.writeCellValue(5, ++resRowNum, "盘点单审核人：");

        // ---------------------- 审核人 ----------------------

        // ---------------------- 盘点结果处理 ----------------------
        /*writer.merge(resRowNum, resRowNum, 0, 26, "9、盘点结果处理概况", false)
                .writeCellValue(0, resRowNum + 1, "")
                .writeCellValue(1, resRowNum + 1, "处理结果")
                .writeCellValue(2, resRowNum + 1, "资产数量")
                .writeCellValue(3, resRowNum + 1, "占比");
        writer.setStyle(blodStyle, "A" + (resRowNum + 1));
        resRowNum += 2;

        List<InventoryHandleResultDto> inventoryResHandle = inventoryReport.getInventoryResHandle();
        for (int i = 0; i < inventoryResHandle.size(); i++) {
            InventoryHandleResultDto resultDto = inventoryResHandle.get(i);
            int itemsSize = resultDto.getItems().size();
            writer.merge(resRowNum, (resRowNum + itemsSize - 1), 0, 0, resultDto.getInventoryStatusText(), false);
            for (InventoryResReportDto resReportDto : resultDto.getItems()) {
                writer.writeCellValue(1, resRowNum, resReportDto.getHandleStatusTypeText());
                writer.writeCellValue(2, resRowNum, resReportDto.getNum());
                writer.writeCellValue(3, resRowNum, resReportDto.getRate());
                resRowNum++;
            }
        }*/
        // ---------------------- 盘点结果处理 ----------------------

        return writer;
    }

    /**
     * 导出-获取各分类/状态/区域资产盘点概况头部数据
     *
     * @return LinkedHashMap
     */
    private LinkedHashMap<String, String> getInventoryGroupResHead(int dispatchMode, int currentDispatchMode) {
        LinkedHashMap<String, String> head = new LinkedHashMap<>();
        if (dispatchMode == InventoryConstant.DISPATCH_ORG_OWNER) {
            head.put("name", "所属管理组织");
        } else if (dispatchMode == InventoryConstant.DISPATCH_MANAGER_OWNER) {
            head.put("name", "资产管理员");
        } else if (dispatchMode == InventoryConstant.DISPATCH_STORAGE_AREA) {
            head.put("name", "所在区域");
        } else if (dispatchMode == InventoryConstant.DISPATCH_ASSET_CATEGORY) {
            head.put("name", "资产分类");
        } else if (dispatchMode == InventoryConstant.DISPATCH_USE_ORG) {
            head.put("name", "所属使用组织");
        } else if (dispatchMode == InventoryConstant.DISPATCH_USE_PERSON) {
            head.put("name", "资产使用人");
        } else if (dispatchMode == 0) {
            head.put("name", "资产状态");
        }
        head.put("ypNum", "正常(实盘)");
        head.put("pyZcNum", "盘盈-在册(实盘)");
        head.put("pyBzcNum", "盘盈-不在册(实盘)");
        head.put("pkNum", "盘亏(未盘)");
        head.put("totalNum", "合计");
        head.put("rate", "占比");
        if (dispatchMode == currentDispatchMode) {
            head.put("inventoryUserNames", "盘点人");
        }
        return head;
    }

}
