package com.niimbot.asset.controller.common.system;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.OrgService;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.feign.finance.DepreciationConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.exception.category.FeignClientException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.Auditable;
import com.niimbot.system.EditRootOrg;
import com.niimbot.system.ImportDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgExportDto;
import com.niimbot.system.OrgQueryDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 20201112
 */
@Slf4j
@Api(tags = "企业组织管理")
@ResultController
@RequestMapping("api/common/org")
public class OrgController {

    private final OrgFeignClient orgFeignClient;

    private final DepreciationConfigFeignClient depreciationConfigFeignClient;

    private final OrgService orgService;

    private final RedisService redisService;


    public OrgController(OrgFeignClient orgFeignClient,
                         OrgService orgService,
                         RedisService redisService,
                         DepreciationConfigFeignClient depreciationConfigFeignClient) {
        this.orgFeignClient = orgFeignClient;
        this.orgService = orgService;
        this.redisService = redisService;
        this.depreciationConfigFeignClient = depreciationConfigFeignClient;
    }

    @ApiOperation(value = "新增组织数据")
    @RepeatSubmit
    @PostMapping()
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean add(@RequestBody @Validated(Insert.class) OrgDto org) {
        return orgFeignClient.add(org);
    }

    @ApiOperation(value = "新增组织数据2.0(OpenAPI)")
    @RepeatSubmit
    @PostMapping("v2")
    public String addV2(@RequestBody @Validated(Insert.class) OrgDto org) {
        try {
            return orgFeignClient.addV2(org);
        } catch (FeignClientException e) {
            throw new BusinessException(e.getFailureResult().getCode(),e.getFailureResult().getMessage());
        }
    }

    @ApiOperation(value = "编辑组织数据")
    @PutMapping()
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated(Update.class) OrgDto org) {
        return orgFeignClient.edit(org);
    }

    @ApiOperation(value = "编辑组织数据")
    @PutMapping("/editRootOrg")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editRootOrg(@RequestBody @Validated EditRootOrg org) {
        return orgFeignClient.editRootOrg(org);
    }

    @ApiOperation(value = "删除组织数据")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> orgIds) {
        return orgFeignClient.delete(orgIds);
    }

    @ApiOperation(value = "数据查询分页列表")
    @GetMapping(value = "/page")
    @AutoConvert
    public PageUtils<OrgDto> page(OrgQueryDto queryDto) {
        return orgFeignClient.page(queryDto);
    }

    @ApiOperation(value = "全部数据查询列表")
    @GetMapping(value = "/list")
    @AutoConvert
    public List<OrgDto> list(OrgQueryDto queryDto) {
        return orgFeignClient.list(queryDto);
    }

    @ApiOperation(value = "通过Id数据查询")
    @GetMapping(value = "/{orgId}")
    @AutoConvert
    public OrgDto getInfo(@PathVariable("orgId") Long orgId) {
        return orgFeignClient.getInfo(orgId);
    }

    @ApiOperation(value = "新增获取推荐组织编码")
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return orgFeignClient.recommendCode();
    }

    @ApiOperation(value = "排序")
    @PutMapping("/sort")
    public Boolean sort(@RequestBody List<Long> orgIds) {
        return orgFeignClient.sort(orgIds);
    }

    @ApiOperation(value = "查询组织下拉树【带权限】")
    @GetMapping("/select/tree")
    public List<Tree<String>> tree(@ApiParam(name = "kw", value = "[名称、编码]")
                                   @RequestParam(value = "kw", required = false) String kw,
                                   @ApiParam(name = "countEmp", value = "是否启用员工数量统计（废弃）")
                                   @RequestParam(value = "countEmp", required = false) Boolean count,
                                   @ApiParam(name = "orgType", value = "组织类型:1-公司, 2-部门")
                                   @RequestParam(value = "orgType", required = false) Integer orgType,
                                   @RequestParam(value = "isOnlyShowEmpWithAccount", required = false) boolean isOnlyShowEmpWithAccount) {
        OrgQueryDto queryDto = new OrgQueryDto()
                .setKw(kw)
                .setOrgType(orgType)
                .setIsShowEmpCount(count)
                .setFilterPerm(true)
                .setIsOnlyShowCurrentNodeEmpCount(false);
        if (StrUtil.isNotBlank(kw)) {
            queryDto.setBuildTree(false);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return list.stream().map(org -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", org.getEmpNumber());
                tree.putExtra("disabled", false);
                return tree;
            }).collect(Collectors.toList());
        } else {
            // 构建树结构
            queryDto.setBuildTree(true);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return TreeUtil.build(list, "0", (org, tree) -> {
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", org.getEmpNumber());
                tree.putExtra("disabled", org.getDisabled());
            });
        }
    }

    @ApiOperation(value = "查询组织树")
    @GetMapping("/tree")
    public List<Tree<String>> tree(@ApiParam(name = "kw", value = "[名称、编码]")
                                   @RequestParam(value = "kw", required = false) String kw,
                                   @ApiParam(name = "countEmp", value = "是否启用员工数量统计（废弃）")
                                   @RequestParam(value = "countEmp", required = false) Boolean count,
                                   @ApiParam(value = "是否显示人数-新版")
                                       @RequestParam(value = "isShowEmpCount", required = false) boolean isShowEmpCount,
                                   @ApiParam(name = "orgType", value = "组织类型:1-公司, 2-部门")
                                   @RequestParam(value = "orgType", required = false) Integer orgType) {
        OrgQueryDto queryDto = new OrgQueryDto()
                .setKw(kw)
                .setOrgType(orgType)
                .setIsShowEmpCount(count != null ? count : isShowEmpCount)
                .setFilterPerm(false)
                .setIsOnlyShowCurrentNodeEmpCount(false);
        if (StrUtil.isNotBlank(kw)) {
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return list.stream().map(org -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", org.getEmpNumber());
                tree.putExtra("disabled", false);
                return tree;
            }).collect(Collectors.toList());
        } else {
            // 构建树结构
            queryDto.setBuildTree(true);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return TreeUtil.build(list, "0", (org, tree) -> {
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", org.getEmpNumber());
                tree.putExtra("disabled", false);
            });
        }
    }

    @ApiOperation("自建部门时获取上级组织树")
    @GetMapping("tree/permission/self")
    public List<Tree<String>> selfOrgTree(@ApiParam(name = "kw", value = "[名称、编码]")
                                          @RequestParam(value = "kw", required = false) String kw) {
        OrgQueryDto queryDto = new OrgQueryDto()
                .setKw(kw)
                .setIsShowEmpCount(false)
                .setFilterPerm(true);
        // 构建成数
        if (StrUtil.isNotBlank(kw)) {
            queryDto.setBuildTree(false);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            // 过滤掉非自建的部门
            list.removeIf(v -> v.getSourceType().equals(1) && v.getPid() != 0L && v.getLevel() != 0);
            return list.stream().map(org -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", 0);
                tree.putExtra("disabled", org.getDisabled());
                tree.putExtra("empNumber", 0);
                tree.putExtra("sourceType", org.getSourceType());
                return tree;
            }).collect(Collectors.toList());
        } else {
            queryDto.setBuildTree(true);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            // 过滤掉非自建的部门
            list.removeIf(v -> v.getSourceType().equals(1) && v.getPid() != 0L && v.getLevel() != 0);
            // 构建树结构
            return TreeUtil.build(list, "0", (org, tree) -> {
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", 0);
                tree.putExtra("disabled", org.getDisabled());
                tree.putExtra("empNumber", 0);
                tree.putExtra("sourceType", org.getSourceType());
            });
        }
    }

    @ApiOperation(value = "查询组织树[带权限]")
    @GetMapping("/tree/permission")
    public List<Tree<String>> permissionTree(@ApiParam(name = "kw", value = "[名称、编码]")
                                             @RequestParam(value = "kw", required = false) String kw,
                                             @ApiParam(name = "countEmp", value = "是否启用员工数量统计（废弃）")
                                             @RequestParam(value = "countEmp", required = false) Boolean count,
                                             @ApiParam(name = "orgType", value = "组织类型:1-公司, 2-部门")
                                             @RequestParam(value = "orgType", required = false) Integer orgType,
                                             @ApiParam(value = "是否显示人数-新版")
                                             @RequestParam(value = "isShowEmpCount", required = false) boolean isShowEmpCount,
                                             @ApiParam(value = "是否仅显示当前节点的人数")
                                             @RequestParam(value = "isOnlyShowCurrentNodeEmpCount", required = false) boolean isOnlyShowCurrentNodeEmpCount) {
        OrgQueryDto queryDto = new OrgQueryDto()
                .setKw(kw)
                .setOrgType(orgType)
                .setIsShowEmpCount(count != null ? count : isShowEmpCount)
                .setFilterPerm(true)
                .setIsOnlyShowCurrentNodeEmpCount(isOnlyShowCurrentNodeEmpCount);
        if (StrUtil.isNotBlank(kw)) {
            queryDto.setBuildTree(false);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return list.stream().map(org -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", org.getEmpNumber());
                tree.putExtra("disabled", org.getDisabled());
                tree.putExtra("empNumber", org.getEmpNumber());
                tree.putExtra("sourceType", org.getSourceType());
                return tree;
            }).collect(Collectors.toList());
        } else {
            queryDto.setBuildTree(true);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            // 构建树结构
            return TreeUtil.build(list, "0", (org, tree) -> {
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                tree.putExtra("empCount", org.getEmpNumber());
                tree.putExtra("disabled", org.getDisabled());
                tree.putExtra("empNumber", org.getEmpNumber());
                tree.putExtra("sourceType", org.getSourceType());
            });
        }
    }

    @ApiOperation(value = "查询组织树[带权限]财务模块")
    @GetMapping("/tree/financeConfig")
    public List<Tree<String>> financeConfigTree(@ApiParam(name = "kw", value = "[名称、编码]")
                                                @RequestParam(value = "kw", required = false) String kw,
                                                @ApiParam(name = "orgId", value = "[当前企业]")
                                                @RequestParam(value = "orgId", required = false) Long orgId,
                                                @ApiParam(name = "orgType", value = "组织类型:1-公司, 2-部门")
                                                @RequestParam(value = "orgType", required = true) Integer orgType) {
        //查询组织信息
        OrgQueryDto queryDto = new OrgQueryDto()
                .setKw(kw)
                .setOrgType(orgType)
                .setFilterPerm(true);
        if (orgType == 1) {
            queryDto.setOrgType(orgType);
        }

        //查询企业的时候，企业需要根据折旧方案进行过滤
        List<Long> financeConfigOrgIdList = null;
        if (orgType == 1) {
            financeConfigOrgIdList = depreciationConfigFeignClient.queryAllOrgId();
        }

        String parentId = "0";
        if (Objects.nonNull(orgId)) {
            parentId = String.valueOf(orgId);
        }

        if (StrUtil.isNotBlank(kw)) {
            List<Long> finalFinanceConfigOrgIdList = financeConfigOrgIdList;
            queryDto.setBuildTree(false);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return list.stream().map(org -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                //公司需要按折旧方案配置企业进行过滤
                if (orgType == 1) {
                    if (CollUtil.isNotEmpty(finalFinanceConfigOrgIdList) && finalFinanceConfigOrgIdList.contains(org.getId())) {
                        tree.putExtra("disabled", org.getDisabled());
                    } else {
                        tree.putExtra("disabled", Boolean.TRUE);
                    }
                } else {
                    //查询部门的时候，需要把公司禁用掉
                    if (Objects.nonNull(org.getOrgType()) && org.getOrgType() == 2) {
                        tree.putExtra("disabled", org.getDisabled());
                    } else {
                        tree.putExtra("disabled", Boolean.TRUE);
                    }
                }
                return tree;
            }).collect(Collectors.toList());
        } else {
            // 构建树结构
            List<Long> finalFinanceConfigOrgIdList1 = financeConfigOrgIdList;
            queryDto.setBuildTree(true);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return TreeUtil.build(list, parentId, (org, tree) -> {
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                //公司需要按折旧方案配置企业进行过滤
                if (orgType == 1) {
                    if (CollUtil.isNotEmpty(finalFinanceConfigOrgIdList1) && finalFinanceConfigOrgIdList1.contains(org.getId())) {
                        tree.putExtra("disabled", org.getDisabled());
                    } else {
                        tree.putExtra("disabled", Boolean.TRUE);
                    }
                } else {
                    //查询部门的时候，需要把公司禁用掉
                    if (Objects.nonNull(org.getOrgType()) && org.getOrgType() == 2) {
                        tree.putExtra("disabled", org.getDisabled());
                    } else {
                        tree.putExtra("disabled", Boolean.TRUE);
                    }
                }
            });
        }
    }

    @ApiOperation(value = "查询组织树[不需要权限]财务模块")
    @GetMapping("/tree/financeConfig/all")
    public List<Tree<String>> financeConfigTreeALL(@ApiParam(name = "kw", value = "[名称、编码]")
                                                   @RequestParam(value = "kw", required = false) String kw,
                                                   @ApiParam(name = "orgId", value = "[当前企业]")
                                                   @RequestParam(value = "orgId", required = false) Long orgId,
                                                   @ApiParam(name = "orgType", value = "组织类型:1-公司, 2-部门")
                                                   @RequestParam(value = "orgType", required = true) Integer orgType) {
        OrgQueryDto queryDto = new OrgQueryDto()
                .setKw(kw)
                .setOrgType(orgType)
                .setFilterPerm(false);
        if (StrUtil.isNotBlank(kw)) {
            queryDto.setBuildTree(false);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return list.stream().map(org -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                if (Objects.equals(org.getOrgType(), orgType)) {
                    tree.putExtra("disabled", Boolean.FALSE);
                } else {
                    tree.putExtra("disabled", Boolean.TRUE);
                }
                return tree;
            }).collect(Collectors.toList());
        } else {
            // 构建树结构
            queryDto.setBuildTree(true);
            List<OrgDto> list = orgFeignClient.list(queryDto);
            return TreeUtil.build(list, "0", (org, tree) -> {
                tree.setId(Convert.toStr(org.getId()));
                tree.setParentId(Convert.toStr(org.getPid()));
                tree.setName(org.getOrgName());
                tree.setWeight(org.getSortNum());
                tree.putExtra("title", org.getOrgName());
                tree.putExtra("value", Convert.toStr(org.getId()));
                tree.putExtra("code", org.getOrgCode());
                tree.putExtra("level", org.getLevel());
                tree.putExtra("orgType", org.getOrgType());
                tree.putExtra("pidType", org.getPidType());
                if (Objects.equals(org.getOrgType(), orgType)) {
                    tree.putExtra("disabled", Boolean.FALSE);
                } else {
                    tree.putExtra("disabled", Boolean.TRUE);
                }
            });
        }
    }

    @ApiOperation(value = "查询组织树[带权限-盘点专用]")
    @GetMapping("/list/permission")
    public List<OrgDto> permissionList() {
        OrgQueryDto queryDto = new OrgQueryDto()
                .setFilterPerm(true);
        return orgFeignClient.list(queryDto);
    }

    @ApiOperation(value = "通过Ids查询资组织数据")
    @PostMapping("/ids")
    public List<Map<String, ?>> listByIds(@RequestBody List<Long> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            return ListUtil.empty();
        }
        return orgFeignClient.listByIds(orgIds).stream().map(item ->
                ImmutableMap.of("id", item.getId(), "name", item.getOrgName(), "code", item.getOrgCode())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "查询存放区域公司树")
    @GetMapping("/tree/area/permission")
    public List<Tree<String>> permissionAreaOrgTree() {
        List<OrgDto> list = orgFeignClient.areaPermsList();
        // 构建树结构
        return TreeUtil.build(list, "0", (org, tree) -> {
            tree.setId(Convert.toStr(org.getId()));
            tree.setParentId(Convert.toStr(org.getPid()));
            tree.setName(org.getOrgName());
            tree.setWeight(org.getSortNum());
            tree.putExtra("title", org.getOrgName());
            tree.putExtra("value", Convert.toStr(org.getId()));
            tree.putExtra("code", org.getOrgCode());
            tree.putExtra("level", org.getLevel());
            tree.putExtra("orgType", org.getOrgType());
            tree.putExtra("pidType", org.getPidType());
            tree.putExtra("disabled", org.getDisabled());
        });
    }

    @ApiOperation(value = "查询耗材仓库公司树")
    @GetMapping("/tree/store/permission")
    public List<Tree<String>> permissionStoreOrgTree() {
        List<OrgDto> list = orgFeignClient.storePermsList();
        // 构建树结构
        return TreeUtil.build(list, "0", (org, tree) -> {
            tree.setId(Convert.toStr(org.getId()));
            tree.setParentId(Convert.toStr(org.getPid()));
            tree.setName(org.getOrgName());
            tree.setWeight(org.getSortNum());
            tree.putExtra("title", org.getOrgName());
            tree.putExtra("value", Convert.toStr(org.getId()));
            tree.putExtra("code", org.getOrgCode());
            tree.putExtra("level", org.getLevel());
            tree.putExtra("orgType", org.getOrgType());
            tree.putExtra("pidType", org.getPidType());
            tree.putExtra("disabled", org.getDisabled());
        });
    }

    @ApiOperation(value = "查询采购执行公司树")
    @GetMapping("/tree/purchase/permission")
    public List<Tree<String>> permissionPurchaseOrgTree() {
        List<OrgDto> list = orgFeignClient.list(new OrgQueryDto()
                .setOrgType(1)
                .setFilterPerm(true)
                .setBuildTree(true));
        // 构建树结构
        return TreeUtil.build(list, "0", (org, tree) -> {
            tree.setId(Convert.toStr(org.getId()));
            tree.setParentId(Convert.toStr(org.getPid()));
            tree.setName(org.getOrgName());
            tree.setWeight(org.getSortNum());
            tree.putExtra("title", org.getOrgName());
            tree.putExtra("value", Convert.toStr(org.getId()));
            tree.putExtra("code", org.getOrgCode());
            tree.putExtra("level", org.getLevel());
            tree.putExtra("orgType", org.getOrgType());
            tree.putExtra("pidType", org.getPidType());
            tree.putExtra("disabled", org.getDisabled());
        });
    }

    @ApiOperation(value = "导出")
    @GetMapping("/export")
    @AuditLog(Auditable.Action.EXP_ORG_LT)
    public void export(OrgQueryDto queryDto, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(OrgExportDto.class);
            // 查询数据
            List<OrgExportDto> excel = orgFeignClient.getExcelData(queryDto);
            String fileName = "组织-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】导出组织模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        orgService.exportTemplate(response);
    }

    @ApiOperation(value = "【PC】导入组织模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ORG, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ORG, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            orgService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return orgService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ORG, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ORG, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        orgService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.ORG, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.ORG));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
