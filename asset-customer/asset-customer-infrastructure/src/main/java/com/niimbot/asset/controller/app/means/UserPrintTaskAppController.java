package com.niimbot.asset.controller.app.means;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 打印任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Api(tags = "【APP】资产打印任务接口")
@ResultController
@RequestMapping("api/app/print/task")
@RequiredArgsConstructor
@Validated
public class UserPrintTaskAppController {

    private final PrintFeignClient printFeignClient;

    @ApiOperation(value = "【APP】打印任务列表")
    @AutoConvert
    @PostMapping(value = "list/{printType}/{taskStatus}")
    public List<UserPrintTaskAppViewDto> list(@PathVariable(required = false) Short printType, @PathVariable("taskStatus") Short taskStatus, @RequestBody(required = false) List<LastPrintTaskQueryDto> queryList) {
        if (null == queryList) {
            queryList = Collections.emptyList();
        }
        if (Objects.isNull(printType)) {
            printType = DictConstant.PRINT_TYPE_ASSET;
        }
        List<UserPrintTaskAppViewDto> list = printFeignClient.listTaskApp(printType, taskStatus, queryList);
        list.forEach(UserPrintTaskAppViewDto::convert);
        return list;
    }

    @ApiOperation(value = "【App】继续打印更改任务状态")
    @GetMapping(value = "continue/changeTaskStatus")
    public Boolean changeTaskStatus(
            @NotNull(message = "打印任务不能为空") Long taskId) {
        return printFeignClient.changeTaskStatus(taskId, 2);
    }

    @ApiOperation(value = "【App】打印状态中取消打印")
    @GetMapping(value = "cancel")
    public Boolean cancel(
            @NotNull(message = "打印任务不能为空") Long taskId) {
        // 更改打印任务状态
        return printFeignClient.cancelTaskWithoutCheck(taskId);
    }

    @ApiOperation(value = "【APP】更改配置并打印任务")
    @PostMapping(value = "config/continue")
    public Boolean appConfigContinueTask(
            @RequestBody @Validated UserPrintTaskConfigDto printTask) {
        return printFeignClient.configAppContinueTask(printTask);
    }

    @ApiOperation("任务详情-未打印-已打印-分页列表")
    @GetMapping("/group/page")
    public PageUtils<UserPrintTaskGroupStatusResult> groupList(UserPrintTaskGroupByStatusPageQuery query) {
        // 1. 未打印：异常暂停之后的所有数据算未打印

        // 2. 已打印：正常打印与异常暂停的所有数据算已打印
        return printFeignClient.taskDetailsPage(query);

        // 3. 异常暂停：包含异常暂停当前数据与前后一条数据
    }

    @ApiOperation("任务详情-异常暂停-列表-无分页")
    @GetMapping("/listOfDataWithStatusPausedInTaskDetails")
    public List<Object> listOfDataWithStatusPausedInTaskDetails(@RequestParam("taskId") Long taskId) {
        return printFeignClient.listOfDataWithStatusPausedInTaskDetails(taskId);
    }
}
