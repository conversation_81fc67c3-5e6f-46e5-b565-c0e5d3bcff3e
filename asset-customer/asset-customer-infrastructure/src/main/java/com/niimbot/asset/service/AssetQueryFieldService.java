package com.niimbot.asset.service;

import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.system.QueryHeadConfigDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/2 16:04
 */
public interface AssetQueryFieldService {

    // --------------资产筛选项---------------
    List<QueryConditionDto> assetAllQueryField();

    List<QueryConditionDto> assetQueryView(Long standardId);

    // --------------资产列表设置---------------
    Boolean assetHeadField(QueryHeadConfigDto config);

    QueryHeadConfigDto assetHeadField();

    List<QueryConditionDto> assetAllHeadField();

    List<AssetHeadDto> assetHeadView();

    List<AssetHeadDto> exportHeadField(Long standardId);

    // --------------资产分组---------------
    List<QueryConditionDto> assetAllGroupField();

    // --------------标准品---------------
    QueryConditionStandardDto standardAllField(Long standardId, boolean needName, boolean filterFile);

    List<QueryConditionStandardDto> standardAllField(List<Long> standardIds, boolean needName, boolean filterFile);

    List<QueryConditionDto> assetSearchAllQueryField();

    List<AssetHeadDto> equipmentHeadView(Integer type, Long standardId);

    List<QueryConditionDto> equipmentAllHeadView(Integer type);
}
