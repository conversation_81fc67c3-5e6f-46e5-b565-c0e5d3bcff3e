package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.feign.CompanyPasswordSettingFeignClient;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.Auditable;
import com.niimbot.system.CompanyPasswordSettingDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = {"企业密码配置"})
@ResultController
@RequestMapping("api/common/companyPasswordSetting")
public class CompanyPasswordSettingController {

    @Resource
    private CompanyPasswordSettingFeignClient feignClient;

    @Resource
    private CusUserFeignClient userFeignClient;

    @ApiOperation(value = "新增企业密码设置")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    @AuditLog(Auditable.Action.COMPANY_PASSWORD_SETTING)
    public Boolean saveOrUpdate(@RequestBody @Validated() CompanyPasswordSettingDto settingDto) {
        //超管才能修改密码规则
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION,"非超管不能修改企业密码规则");
        }
        //解析前端传过来的密码
        String decryptPassword = SecurityUtils.decryptPassword(settingDto.getPassword());
        String rawPassword = userFeignClient.checkPassword();
        if (!SecurityUtils.matchesPassword(decryptPassword, rawPassword)) {
            throw new BusinessException(SystemResultCode.USER_OLD_PASSWORD_ERROR);
        }
        return feignClient.saveOrUpdate(settingDto);
    }

    @ApiOperation(value = "获取密码配置详情")
    @GetMapping("/getSettingDetail")
    public CompanyPasswordSettingDto getSettingDetail() {
        return feignClient.getSettingDetail();
    }

    @ApiOperation(value = "关闭密码安全级别")
    @PostMapping("/closeSwitch")
    public Boolean closeSwitch() {
        return feignClient.closeSwitch();
    }

    @ApiOperation(value = "获取密码文案")
    @GetMapping("/getLimitWords")
    public Map<String, Object> getLimitWords() {
        String limitWords = feignClient.getLimitWords();
        return MapUtil.of("limitWords", limitWords);
    }

    @ApiOperation(value = "通过手机号获取密码文案")
    @GetMapping("/getLimitWordsByMobile")
    public Map<String, Object> getLimitWordsByMobile(@RequestParam(value = "mobile") String mobile) {
        String limitWords = feignClient.getLimitWordsByMobile(mobile);
        return MapUtil.of("limitWords", limitWords);
    }
}
