package com.niimbot.asset.service;

import com.niimbot.asset.model.OssCallbackResult;
import com.niimbot.asset.model.OssPolicyResult;

import javax.servlet.http.HttpServletRequest;

public interface OssService {

    /**
     * oss 上传生成策略
     *
     * @param filePath : 文件路径
     * @return
     */
    OssPolicyResult policy(String bucket, int expire, String filePath, String dirPattern);

    /**
     * oss上传成功回调
     *
     * @param request
     * @return
     */
    OssCallbackResult callback(HttpServletRequest request);
}

