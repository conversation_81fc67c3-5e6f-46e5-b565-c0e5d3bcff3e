package com.niimbot.asset.controller.common.maintenance;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.MaintainPlanFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainPlanContentDto;
import com.niimbot.maintenance.MaintainPlanDetailDto;
import com.niimbot.maintenance.MaintainPlanDto;
import com.niimbot.maintenance.MaintainPlanEditBatchDto;
import com.niimbot.maintenance.MaintainPlanExport;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.maintenance.MaintainPlanListDto;
import com.niimbot.maintenance.MaintainPlanQueryDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.validate.group.Insert;

import org.apache.ibatis.annotations.Update;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 资产保养计划 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Api(tags = "保养计划管理")
@Validated
@ResultController
@RequestMapping("api/common/maintenance/maintain/plan")
@RequiredArgsConstructor
public class MaintainPlanController {

    private final MaintainPlanFeignClient maintainPlanFeignClient;

    private final DictConvertUtil dictConvertUtil;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @ApiOperation(value = "保养计划分页")
    @GetMapping("/page")
    public PageUtils<JSONObject> page(MaintainPlanQueryDto queryDto) {
        PageUtils<MaintainPlanListDto> maintainPlanPage = maintainPlanFeignClient.page(queryDto);
        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtils.copyProperties(maintainPlanPage, result);
        //数据脱敏处理
        if (CollUtil.isNotEmpty(maintainPlanPage.getList())) {
            List<JSONObject> dataList = new ArrayList<>();
            for (MaintainPlanListDto item : maintainPlanPage.getList()) {
                dictConvertUtil.convertToDictionary(item);
                dataList.add(JsonUtil.toJsonObject(item));
            }
            desensitizationDataUtil.handleSensitiveField(dataList, SensitiveObjectTypeEnum.ASSET.getCode());
            result.setList(dataList);
        } else {
            result.setList(ListUtil.empty());
        }
        return result;
    }

    @ApiOperation(value = "保养计划详情")
    @AutoConvert
    @GetMapping("/{id}")
    public MaintainPlanInfoDto info(@PathVariable("id") Long id) {
        return maintainPlanFeignClient.info(id);
    }

    @ApiOperation(value = "资产ID查询保养内容")
    @GetMapping("/content/{assetId}")
    public MaintainPlanDetailDto contentInfo(@PathVariable("assetId") Long assetId) {
        return maintainPlanFeignClient.contentInfo(assetId);
    }

    @ApiOperation(value = "删除保养计划")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody @NotEmpty(message = "请选择一条保养计划") List<Long> planIds) {
        Map<String, String> params = AuditLogs.sendMeansMaintainPlanRemoveRecord(planIds);
        Boolean delete = maintainPlanFeignClient.delete(planIds);
        if (delete) {
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MEANS_MAT_PLAN, params));
        }
        return delete;
    }

    @ApiOperation(value = "新增保养计划")
    @PostMapping
    @RepeatSubmit
    @ResultMessage("提交成功")
    public Boolean insert(@RequestBody @Validated(Insert.class) MaintainPlanDto optDto) {
        maintainPlanFeignClient.insert(optDto);
        AuditLogs.sendMeansMaintainPlanCreateRecord(optDto);
        return true;
    }

    @ApiOperation(value = "编辑保养计划")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated(Update.class) MaintainPlanDto optDto) {
        maintainPlanFeignClient.edit(optDto);
        AuditLogs.sendMeansMaintainPlanEditRecord(optDto);
        return true;
    }

    @ApiOperation(value = "批量设置保养计划")
    @PutMapping("/batch")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editBatch(@RequestBody MaintainPlanEditBatchDto optDto) {
        maintainPlanFeignClient.editBatch(optDto);
        AuditLogs.sendMeansMaintainPlanBatchEditRecord(optDto);
        return true;
    }

    @ApiOperation(value = "启用/禁用")
    @PutMapping("/status")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean status(@RequestBody @Validated(MaintainPlanDto.StatusUpdate.class) MaintainPlanDto optDto) {
        return maintainPlanFeignClient.status(optDto);
    }

    @ApiOperation("保养计划导出")
    @PostMapping("/export")
    public void export(@RequestBody MaintainPlanQueryDto queryDto, HttpServletResponse response) {
        PageUtils<MaintainPlanListDto> page = maintainPlanFeignClient.page(queryDto);
        LinkedHashMap<String, String> head = ExcelUtils.buildExcelHead(MaintainPlanExport.class);
        String filename = "保养计划清单（" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "）";
        if (queryDto.getPageNum() > 1) {
            filename = filename + "-1";
        }
        List<MaintainPlanListDto> list = page.getList();
        dictConvertUtil.convertToDictionary(list);
        List<JSONObject> exportList = list.stream().map(v -> {
            MaintainPlanExport export = BeanUtil.copyProperties(v, MaintainPlanExport.class);
            if (Convert.toInt(v.getStatus()) == DictConstant.SYS_ENABLE) {
                export.setPlanStatusText("正常");
            }
            if (Convert.toInt(v.getStatus()) == DictConstant.SYS_DISABLE) {
                export.setPlanStatusText("禁用");
            }
            export.setPlanUserText(String.join("，", v.getPrincipalUserId()));
            export.setFrequencyUnit(v.getFrequency() + v.getFrequencyUnit());
            export.setContent(v.getPlanContentList().stream().map(MaintainPlanContentDto::getProject).collect(Collectors.joining(",")));
            JSONObject result = JsonUtil.toJsonObject(export);
            //时间处理下
            if (Objects.nonNull(v.getNextTime())) {
                result.put("nextTime", v.getNextTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            return result;
        }).collect(Collectors.toList());
        //数据脱敏处理
        if (CollUtil.isNotEmpty(exportList)) {
            desensitizationDataUtil.handleSensitiveField(exportList, SensitiveObjectTypeEnum.ASSET.getCode());
        }
        try {
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS_PLAN));
            ExcelUtils.export(response, new ExcelExportDto(head, exportList), filename + ".xlsx");
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
