package com.niimbot.asset.controller.common.inventory;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.InventoryAssetFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryAssetDto;
import com.niimbot.inventory.InventoryPictureDto;
import com.niimbot.inventory.InventoryRemarkDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Optional;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/8/16 13:57
 */
@Slf4j
@Api(tags = "盘点资产管理")
@ResultController
@RequestMapping("api/common/inventory/asset")
@RequiredArgsConstructor
public class InventoryAssetCommonController {

    private final InventoryAssetFeignClient inventoryAssetFeignClient;
    private final FormFeignClient formFeignClient;
    private final AsAssetUtil assetUtil;


    @ApiOperation(value = "盘点单资产详情")
    @GetMapping(value = "/detail/{id}")
    public JSONObject getAssetDetail(@PathVariable Long id) {
        InventoryAssetDto assetDetail = inventoryAssetFeignClient.getAssetDetail(id);
        JSONObject json = Optional.of(assetDetail.getAssetSnapshotData()).orElse(new JSONObject());
        json.put("id", assetDetail.getId());
        json.put("inventoryId", assetDetail.getInventoryId());
        json.put("assetId", assetDetail.getAssetId());
        json.put("inventoryRemark", assetDetail.getRemark());
        json.put("pictureUrl", assetDetail.getPictureUrl());
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        assetUtil.translateAssetJson(json, formVO.getFormFields());
        return json;
    }

    @ApiOperation(value = "修改盘点照片")
    @PostMapping("/updatePicture")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @RepeatSubmit
    public Boolean updatePicture(@RequestBody @Validated InventoryPictureDto dto) {
        return inventoryAssetFeignClient.updatePicture(dto);

    }

    @ApiOperation(value = "修改存疑备注")
    @PostMapping("/updateRemark")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @RepeatSubmit
    public Boolean updateRemark(@RequestBody @Validated InventoryRemarkDto dto) {
        return inventoryAssetFeignClient.updateRemark(dto);
    }

}
