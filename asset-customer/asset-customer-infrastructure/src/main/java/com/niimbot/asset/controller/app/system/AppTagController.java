package com.niimbot.asset.controller.app.system;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.PrintTagFeignClient;
import com.niimbot.asset.service.feign.TagFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.SizeDto;
import com.niimbot.system.UserPrintTagDto;
import com.niimbot.system.UserTagDto;
import com.niimbot.system.UserTagResDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "APP标签模板管理")
@ResultController
@RequestMapping("api/app/tag")
public class AppTagController {

    private final TagFeignClient tagFeignClient;

    private final PrintTagFeignClient printTagFeignClient;

    private final AbstractFileUtils fileUtils;

    @Autowired
    public AppTagController(TagFeignClient tagFeignClient, PrintTagFeignClient printTagFeignClient, AbstractFileUtils fileUtils) {
        this.tagFeignClient = tagFeignClient;
        this.printTagFeignClient = printTagFeignClient;
        this.fileUtils = fileUtils;
    }

    @ApiOperation(value = "数据查询列表")
    @GetMapping
    public UserTagResDto appTagList(@ApiParam(name = "sizeId", value = "尺寸类型")
                                    @RequestParam(value = "sizeId", required = false, defaultValue = "1") Long sizeId,
                                    @ApiParam(name = "tagType", value = "模板类型 1-系统默认模板  2-用户自定义模板")
                                    @RequestParam(value = "tagType", required = false) Integer tagType,
                                    @ApiParam(name = "kw", value = "[模板名称]关键词")
                                    @RequestParam(value = "kw", required = false) String kw,
                                    @ApiParam(name = "printerName", value = "打印机名称")
                                    @RequestParam(value = "printerName", required = false, defaultValue = "") String printerName) {
        UserTagResDto userTagResDto = tagFeignClient.appTagList(DictConstant.PRINT_TYPE_ASSET, sizeId, tagType, kw, printerName);
        List<UserTagDto> groupData = userTagResDto.getGroupData();

        groupData.forEach(val -> {
            // 补全标签路径
            val.setTagUrl(fileUtils.convertToDownloadUrl(val.getTagUrl()));
        });
        return userTagResDto;
    }

    @ApiOperation(value = "数据查询列表")
    @GetMapping("/material")
    public UserTagResDto appMaterialTagList(@ApiParam(name = "sizeId", value = "尺寸类型")
                                            @RequestParam(value = "sizeId", required = false, defaultValue = "1") Long sizeId,
                                            @ApiParam(name = "tagType", value = "模板类型 1-系统默认模板  2-用户自定义模板")
                                            @RequestParam(value = "tagType", required = false) Integer tagType,
                                            @ApiParam(name = "kw", value = "[模板名称]关键词")
                                            @RequestParam(value = "kw", required = false) String kw,
                                            @ApiParam(name = "printerName", value = "打印机名称")
                                            @RequestParam(value = "printerName", required = true, defaultValue = "") String printerName) {
        UserTagResDto userTagResDto = tagFeignClient.appTagList(DictConstant.PRINT_TYPE_MATERIAL, sizeId, tagType, kw, printerName);
        List<UserTagDto> groupData = userTagResDto.getGroupData();

        groupData.forEach(val -> {
            // 补全标签路径
            val.setTagUrl(fileUtils.convertToDownloadUrl(val.getTagUrl()));
        });
        return userTagResDto;
    }

    @ApiOperation(value = "app标签尺寸列表")
    @GetMapping("/sizeList")
    public List<SizeDto> appSizeList(@ApiParam(name = "printerName", value = "打印机名称")
                                     @RequestParam(value = "printerName", required = false) String printerName) {
        return tagFeignClient.appSizeList(printerName);
    }

    @ApiOperation(value = "新增标签模板")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean add(@RequestBody @Validated UserPrintTagDto userPrintTagDto) {
        return printTagFeignClient.add(userPrintTagDto);
    }

    @ApiOperation(value = "编辑标签模板")
    @RepeatSubmit
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated UserPrintTagDto userPrintTagDto) {
        return printTagFeignClient.edit(userPrintTagDto);
    }

    @ApiOperation(value = "标签模板列表")
    @GetMapping("/list")
    public List<UserPrintTagDto> tagList() {
        return printTagFeignClient.tagList();
    }

    @ApiOperation(value = "获取单个标签模板")
    @GetMapping("/{id}")
    public UserPrintTagDto getById(@PathVariable("id") Long id) {
        return printTagFeignClient.getById(id);
    }

}
