package com.niimbot.asset.service.feign.finance;

import com.niimbot.finance.DepreciationConfigDto;
import com.niimbot.finance.DisableDepreciationConfigDto;
import com.niimbot.finance.OrgSettleInfoDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/15 下午2:26
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface DepreciationConfigFeignClient {

    /**
     * 折旧方案配置
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/depreciation/config")
    Boolean saveConfig(@RequestBody DepreciationConfigDto configDto);

    /**
     * 编辑折旧方案配置
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/depreciation/edit")
    Boolean modifyConfig(@RequestBody DepreciationConfigDto configDto);

    /**
     * 折旧方案配置详情
     * @param orgId
     * @return
     */
    @GetMapping(value = "server/finance/depreciation/{orgId}")
    DepreciationConfigDto detail(@PathVariable(value = "orgId") Long orgId);

    /**
     * 获取反启用数据
     * @param orgId
     * @return
     */
    @GetMapping(value = "server/finance/depreciation/querySettleInfo/{orgId}")
    OrgSettleInfoDto querySettleInfo(@PathVariable(value = "orgId") Long orgId);

    /**
     * 反启用获取验证码
     * @return
     */
    @GetMapping(value = "server/finance/depreciation/verificationCode")
    Boolean verificationCode();

    /**
     * 反启用
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/depreciation/removeConfig")
    Boolean removeConfig(@RequestBody DisableDepreciationConfigDto configDto);

    /**
     * 查询配置了折旧方案的所有企业id
     * @return
     */
    @GetMapping("server/finance/depreciation/queryAllOrgId")
    List<Long> queryAllOrgId();
}
