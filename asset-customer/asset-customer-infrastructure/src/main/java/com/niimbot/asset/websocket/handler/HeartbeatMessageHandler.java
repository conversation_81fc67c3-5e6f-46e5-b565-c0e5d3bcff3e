package com.niimbot.asset.websocket.handler;

import com.niimbot.asset.websocket.WebSocketServer;
import com.niimbot.asset.websocket.msg.HeartbeatMessage;
import com.niimbot.asset.websocket.msg.Message;

import javax.websocket.EncodeException;
import javax.websocket.Session;
import java.io.IOException;

/**
 * 心跳处理器
 *
 * <AUTHOR>
 * @date 2021/9/2 15:39
 */
public class HeartbeatMessageHandler implements MessageHandler {
    @Override
    public void handle(Session session, Long userId, Long companyId, Message message) throws IOException, EncodeException {
        HeartbeatMessage heartbeatMessage = (HeartbeatMessage) message;
        if (HeartbeatMessage.PING.equals(heartbeatMessage.getHearbeat())) {
            heartbeatMessage.setHearbeat(HeartbeatMessage.PONG);
            session.getBasicRemote().sendObject(heartbeatMessage);
            WebSocketServer.reHeart(session.getId());
        }
    }
}
