package com.niimbot.asset.controller.common.means;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetRelationService;
import com.niimbot.asset.service.feign.AssetRelationFeignClient;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AlterAssetRelationDto;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.means.AssetRelationConfigDto;
import com.niimbot.means.AssetRelationQueryConditionDto;
import com.niimbot.means.CancelAssetRelationDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Collections;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/8/8 下午4:04
 */
@Slf4j
@Api(tags = "关联资产")
@ResultController
@RequestMapping("api/common/assetRelation/")
public class AssetRelationController {

    @Autowired
    private AssetRelationFeignClient assetRelationFeignClient;
    @Autowired
    private AssetRelationService assetRelationService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @ApiOperation("配置资产关联关系")
    @PostMapping("config")
    public Boolean configRelation(@RequestBody @Validated AssetRelationConfigDto relationConfigDto) {
        return assetRelationFeignClient.configRelation(relationConfigDto);
    }

    @ApiOperation("批量修改资产关联关系")
    @PostMapping("alterRelationType")
    public Boolean alterRelationType(@RequestBody @Validated AlterAssetRelationDto relationDto) {
        return assetRelationFeignClient.alterRelationType(relationDto);
    }

    @ApiOperation("取消组合关系")
    @PostMapping("cancelRelation")
    public Boolean cancelRelation(@RequestBody @Validated CancelAssetRelationDto relationDto) {
        return assetRelationFeignClient.cancelRelation(relationDto);
    }

    @ApiOperation("解除关联关系")
    @PostMapping("remove")
    public Boolean remove(@RequestBody @Validated CancelAssetRelationDto relationDto) {
        return assetRelationFeignClient.removeRelation(relationDto);
    }

    @ApiOperation("资产关联关系分页查询")
    @PostMapping("page")
    public PageUtils<JSONObject> page(@RequestBody AssetQueryConditionDto queryDto) {
        queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        PageUtils<JSONObject> pageResult = assetRelationService.assetRelationPage(queryDto);
        //数据脱敏处理
        if (Objects.nonNull(pageResult)) {
            desensitizationDataUtil.handleSensitiveField(pageResult.getList(), SensitiveObjectTypeEnum.ASSET.getCode());
        }
        return pageResult;
    }

    @ApiOperation("子资产分页查询")
    @PostMapping("subAssetPage")
    public PageUtils<JSONObject> subAssetPage(@RequestBody AssetRelationQueryConditionDto queryDto) {
        queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        PageUtils<JSONObject> pageResult = assetRelationService.subAsset(queryDto);
        //数据脱敏处理
        if (Objects.nonNull(pageResult)) {
            desensitizationDataUtil.handleSensitiveField(pageResult.getList(), SensitiveObjectTypeEnum.ASSET.getCode());
        }
        return pageResult;
    }

    @ApiOperation("资产详情-设备bom列表")
    @PostMapping("allAssetPage")
    public PageUtils<JSONObject> allAssetPage(@RequestBody AssetQueryConditionDto queryDto) {
        PageUtils<JSONObject> result = new PageUtils<>();
        if (CollUtil.isEmpty(queryDto.getAssetIds())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID);
        }
        queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());

        //查询主资产id
        Long mainAssetId = assetRelationFeignClient.getMainAssetId(queryDto.getAssetIds().get(0));

        //如果主资产为空，说明没有关联信息，直接返回空数据
        if (Objects.isNull(mainAssetId)) {
            result.setList(Collections.emptyList());
            result.setTotalPage(0);
            result.setTotalCount(0);
            result.setCurrPage((int) queryDto.getPageNum());
            result.setPageSize((int) queryDto.getPageSize());
            return result;
        } else {
            //说明当前资产是主资产，所以查询子资产信息
            if (mainAssetId.equals(queryDto.getAssetIds().get(0))) {
                AssetRelationQueryConditionDto queryParam = new AssetRelationQueryConditionDto();
                BeanUtils.copyProperties(queryDto, queryParam);
                queryParam.setMainAssetId(mainAssetId);
                queryParam.setAssetIds(Collections.emptyList());
                result = assetRelationService.subAsset(queryParam);
            } else {
                //说明当前资产是子资产，所以查询主资产信息
                queryDto.setAssetIds(Collections.singletonList(mainAssetId));
                result = assetRelationService.assetRelationPage(queryDto);
            }

            //数据脱敏处理
            if (Objects.nonNull(result) && CollUtil.isNotEmpty(result.getList())) {
                desensitizationDataUtil.handleSensitiveField(result.getList(), SensitiveObjectTypeEnum.ASSET.getCode());
            }
            return result;
        }
    }
}
