package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.sale.MallGoodsDto;
import com.niimbot.sale.MallGoodsPageDto;
import com.niimbot.sale.MallGoodsQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 商城购物
 *
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MallGoodsFeignClient {

    String BASE_URL = "server/sale/mallgoods";

    @GetMapping(BASE_URL + "/list")
    PageUtils<MallGoodsPageDto> pageQuery(@SpringQueryMap MallGoodsQueryDto mallGoodsQueryDto);

    @GetMapping(BASE_URL + "/detail/{id}")
    MallGoodsPageDto detail(@PathVariable("id") Long id);

    @GetMapping(BASE_URL + "/hardware")
    List<MallGoodsDto> hardwareProduct();

    @GetMapping(BASE_URL + "/detailBySku/{skuCode}")
    MallGoodsDto hardwareDetail(@PathVariable("skuCode") String skuCode);

}
