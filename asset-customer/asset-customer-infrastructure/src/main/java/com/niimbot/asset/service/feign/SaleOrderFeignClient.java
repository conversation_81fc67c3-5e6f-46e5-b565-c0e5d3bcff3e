package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.sale.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 销售单 feign
 *
 * <AUTHOR>
 * @date 2021/8/9 14:24
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface SaleOrderFeignClient {

    /**
     * 购买记录
     *
     * @param query
     * @return
     */
    @PostMapping("server/sale/order/page")
    PageUtils<SaleOrderDto> page(SaleOrderPageQueryDto query);

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    @GetMapping("server/sale/order/{id}")
    SaleOrderDetailDto getById(@PathVariable("id") Long id);

    /**
     * 创建资源包订单
     *
     * @param createDto
     * @return
     */
    @PostMapping("server/sale/order/resourcePack")
    SaleOrderPayInfoDto createResourcePackOrder(@RequestBody SaleOrderResourcePackCreateDto createDto);


    /**
     * 取消订单
     *
     * @param id
     * @return
     */
    @PutMapping("server/sale/order/close/{id}")
    Boolean closeOrder(@PathVariable("id") Long id);

    /**
     * 重新支付
     *
     * @param repayDto
     * @return
     */
    @PutMapping("server/sale/order/repay")
    SaleOrderPayInfoDto repay(@RequestBody SaleOrderRepayDto repayDto);

    /**
     * 销售单支付状态
     *
     * @param id
     * @return
     */
    @GetMapping("server/sale/order/getPayStatus/{id}")
    SaleOrderPaidDto getPayStatus(@PathVariable("id") Long id);

    /**
     * 支付回调地址
     *
     * @param event
     * @return
     */
    @PostMapping("server/sale/order/notify")
    String notify(@RequestBody String event);

    /**
     * 可开票总金额查询
     *
     * @return
     */
    @GetMapping("server/sale/order/invoiceAmount")
    BigDecimal queryInvoiceAmount();
}
