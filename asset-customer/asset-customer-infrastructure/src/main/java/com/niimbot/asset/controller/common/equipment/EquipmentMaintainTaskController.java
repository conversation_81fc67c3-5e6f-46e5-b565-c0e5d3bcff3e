package com.niimbot.asset.controller.common.equipment;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.EquipmentMaintainOrderService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentMaintainTaskFeignClient;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.equipment.AuditableCancelTaskResult;
import com.niimbot.equipment.AuditableResumeTaskResult;
import com.niimbot.equipment.AuditableSubmitTaskResult;
import com.niimbot.equipment.MaintainTaskCreateDto;
import com.niimbot.equipment.MaintainTaskDto;
import com.niimbot.equipment.MaintainTaskQueryDto;
import com.niimbot.equipment.MaintainTaskSparePartsQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.material.MaterialSparePartsDto;
import com.niimbot.system.Auditable;
import com.niimbot.validate.GroupValidate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/11 15:38
 */
@Api(tags = "【设备管理】设备保养任务")
@Slf4j
@RequiredArgsConstructor
@ResultController
@RequestMapping("api/common/equipment/maintain/task")
public class EquipmentMaintainTaskController {

    private final EquipmentMaintainTaskFeignClient maintainTaskFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final DesensitizationDataUtil desensitizationDataUtil;
    private final EquipmentMaintainOrderService maintainOrderService;

    @Autowired
    private FormFeignClient formFeignClient;
    @Autowired
    private AsMaterialUtil materialUtil;

    @ApiOperation(value = "设备保养任务分页")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody MaintainTaskQueryDto query) {
        PageUtils<MaintainTaskDto> page = maintainTaskFeignClient.page(query);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (MaintainTaskDto taskDto : page.getList()) {
            taskDto.setOrderType(OrderFormTypeEnum.EQUIPMENT_MAINTAIN_TASK.getCode());
            list.add(taskDto.toJSONObject());
        }
        desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.ASSET.getCode());
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "设备保养保存")
    @RepeatSubmit
    @PostMapping("/temporary")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public AuditableSubmitTaskResult temporary(@RequestBody @Validated(GroupValidate.Save.class) MaintainTaskCreateDto createDto) {
        return maintainTaskFeignClient.temporary(createDto);
    }

    @ApiOperation(value = "设备保养提交")
    @RepeatSubmit
    @PostMapping("/submit")
    @ResultMessage("提交成功")
    @AuditLog(Auditable.Action.SUBMIT_EQUIPMENT_TASK)
    public AuditableSubmitTaskResult submit(@RequestBody @Validated(GroupValidate.Submit.class) MaintainTaskCreateDto createDto) {
        return maintainTaskFeignClient.submit(createDto);
    }

    @ApiOperation(value = "设备保养作废")
    @PostMapping("/cancel")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @AuditLog(Auditable.Action.CANCEL_EQUIPMENT_TASK)
    public AuditableCancelTaskResult cancel(@RequestBody List<Long> orderIds) {
        return maintainTaskFeignClient.cancel(orderIds);
    }

    @ApiOperation(value = "设备保养恢复")
    @PostMapping("/resume")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @AuditLog(Auditable.Action.RESUME_EQUIPMENT_TASK)
    public AuditableResumeTaskResult resume(@RequestBody List<Long> orderIds) {
        return maintainTaskFeignClient.resume(orderIds);
    }

    @ApiOperation(value = "设备信息查询")
    @GetMapping("/equipment/{orderId}")
    public JSONObject equipmentInfo(@PathVariable("orderId") Long orderId) {
        JSONObject jsonObject = maintainTaskFeignClient.equipmentInfo(orderId);
        desensitizationDataUtil.handleSensitiveField(ListUtil.of(jsonObject), SensitiveObjectTypeEnum.ASSET.getCode());
        return jsonObject;
    }

    @ApiOperation(value = "保养任务信息查询")
    @AutoConvert
    @GetMapping("/{orderId}")
    public MaintainTaskDto info(@PathVariable("orderId") Long orderId) {
        MaintainTaskDto info = maintainTaskFeignClient.info(orderId);
        desensitizationDataUtil.handleSensitiveField(ListUtil.of(info.getAssetSnapshotData()), SensitiveObjectTypeEnum.ASSET.getCode());
        return info;
    }

    @ApiOperation(value = "更换备件快照查询")
    @GetMapping("/replacement/spareParts/{orderId}/{materialId}")
    public JSONObject materialInfo(@PathVariable("orderId") Long orderId,
                                   @PathVariable("materialId") Long materialId) {
        JSONObject jsonObject = maintainTaskFeignClient.materialInfo(orderId, materialId);
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(ListUtil.of(jsonObject), SensitiveObjectTypeEnum.MATERIAL.getCode());
        return jsonObject;
    }

    @ApiOperation(value = "更换备件分页")
    @GetMapping("/replacement/sparePartsPage")
    public PageUtils<JSONObject> sparePartsPage(@Validated MaintainTaskSparePartsQueryDto queryDto) {
        PageUtils<MaterialSparePartsDto> materialSparePartsPage = maintainTaskFeignClient.sparePartsPage(queryDto);
        List<MaterialSparePartsDto> list = materialSparePartsPage.getList();
        if (CollUtil.isEmpty(list)) {
            return new PageUtils<>();
        }
        // 返回结果集
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<JSONObject> materialDataList = list.stream()
                .map(MaterialSparePartsDto::translate).collect(Collectors.toList());
        materialUtil.translateMaterialJsonBatch(materialDataList, material.getFormFields());
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(materialDataList, SensitiveObjectTypeEnum.MATERIAL.getCode());
        PageUtils<JSONObject> jsonPage = new PageUtils<>();
        BeanUtil.copyProperties(materialSparePartsPage, jsonPage);
        jsonPage.setList(materialDataList);
        return jsonPage;
    }

    @ApiOperation(value = "更换备件列表")
    @GetMapping("/replacement/sparePartsList")
    public List<JSONObject> sparePartsList(@RequestParam("orderId") Long orderId,
                                           @RequestParam(value = "outRepo", required = false) Long outRepo) {
        MaintainTaskSparePartsQueryDto partsQueryDto = new MaintainTaskSparePartsQueryDto();
        partsQueryDto.setOrderId(orderId);
        partsQueryDto.setOutRepo(outRepo);
        List<MaterialSparePartsDto> materialSparePartsList = maintainTaskFeignClient.sparePartsList(partsQueryDto);
        if (CollUtil.isEmpty(materialSparePartsList)) {
            return ListUtil.empty();
        }
        // 返回结果集
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<JSONObject> materialDataList = materialSparePartsList.stream()
                .map(MaterialSparePartsDto::translate).collect(Collectors.toList());
        materialUtil.translateMaterialJsonBatch(materialDataList, material.getFormFields());
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(materialDataList, SensitiveObjectTypeEnum.MATERIAL.getCode());
        return materialDataList;
    }

    @ApiOperation(value = "【PC】导出备件保养单据列表")
    @PostMapping(value = "/exportOrder/task")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderAssets(@RequestBody @Validated MaintainTaskQueryDto queryDto) {
        PageUtils<MaintainTaskDto> page = maintainTaskFeignClient.page(queryDto);
        dictConvertUtil.convertToDictionary(page.getList());
        return maintainOrderService.exportMaintainTask(page.getList(), queryDto);
    }

}
