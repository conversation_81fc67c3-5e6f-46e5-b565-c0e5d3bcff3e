package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.service.feign.AntiFakeClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.AntiFakeCheckDto;
import com.niimbot.system.ThirdPartyCheckDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

@Api(tags = "第三方设备授权判断接口")
@ResultController
@RequestMapping("api/common/authEquipment")
public class AuthEquipmentController {

    @Resource
    private AntiFakeClient antiFakeClient;

    @ApiOperation(value = "授权设备检查")
    @PostMapping("/check")
    public Boolean checkAuthEquipment(@RequestBody @Validated AntiFakeCheckDto antiFakeCheckDto) {
        return antiFakeClient.checkAuthEquipment(antiFakeCheckDto);
    }

    @ApiOperation(value = "第三方设备授权【佐藤、兄弟等】")
    @PostMapping("/check/thirdParty")
    public Boolean checkAuthEquipmentThirdParty(@RequestBody @Validated ThirdPartyCheckDto thirdPartyCheckDto) {
        return antiFakeClient.checkAuthEquipmentThirdParty(thirdPartyCheckDto);
    }
}