package com.niimbot.asset.controller.common.means;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.AssetQueryFieldService;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 资产查询字段
 *
 * <AUTHOR>
 * @date 2021/12/6 11:46
 */
@Slf4j
@Validated
@Api(tags = "资产字段管理")
@ResultController
@RequestMapping("api/common/queryField/asset")
@RequiredArgsConstructor
public class AssetQueryFieldController {

    private final AssetQueryFieldService queryFieldService;
    private final AssetFeignClient assetFeignClient;

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all")
    public List<QueryConditionDto> assetAllQueryField() {
        return queryFieldService.assetAllQueryField();
    }

    @ApiOperation(value = "高级搜索项配置-所有字段")
    @GetMapping("/query/field/search/all")
    public List<QueryConditionDto> assetSearchAllQueryField() {
        return queryFieldService.assetSearchAllQueryField();
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view")
    public List<QueryConditionDto> assetQueryView(@RequestParam(value = "standardId", required = false) Long standardId) {
        return queryFieldService.assetQueryView(standardId);
    }

    @ApiOperation(value = "列表设置-保存")
    @PostMapping("/head/field")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean assetHeadField(@RequestBody @Validated QueryHeadConfigDto config) {
        return queryFieldService.assetHeadField(config);
    }

    @ApiOperation(value = "列表设置-查询")
    @GetMapping("/head/field")
    public QueryHeadConfigDto assetHeadField() {
        return queryFieldService.assetHeadField();
    }

    @ApiOperation(value = "列表设置-所有字段")
    @GetMapping("/head/all")
    public List<QueryConditionDto> assetAllHeadField(@RequestParam(value = "type", required = false, defaultValue = "1") Integer type) {
        return queryFieldService.equipmentAllHeadView(type);
    }

    @ApiOperation(value = "列表-渲染")
    @GetMapping("/head/view")
    public List<AssetHeadDto> assetHeadView(@RequestParam(value = "type", required = false, defaultValue = "1") Integer type,
                                            @RequestParam(value = "standardId", required = false) Long standardId) {
        return queryFieldService.equipmentHeadView(type, standardId);
    }

    @ApiOperation(value = "条件分组-所有字段")
    @GetMapping("/group/field/all")
    public List<QueryConditionDto> assetAllGroupField() {
        return queryFieldService.assetAllGroupField();
    }

    @ApiOperation(value = "标准品属性-所有字段")
    @GetMapping("/standard/{standardId}")
    public QueryConditionStandardDto standardAllField(@PathVariable("standardId") Long standardId) {
        return queryFieldService.standardAllField(standardId, true, false);
    }

    @ApiOperation(value = "标准品属性-所有字段")
    @PostMapping("/standard/batch")
    public List<QueryConditionStandardDto> standardAllField(@RequestBody List<Long> standardIds) {
        return queryFieldService.standardAllField(standardIds, true, false);
    }

    @ApiOperation(value = "排序字段")
    @GetMapping("/sortField")
    public QueryConditionSortDto sortField() {
        return assetFeignClient.sortField();
    }

}
