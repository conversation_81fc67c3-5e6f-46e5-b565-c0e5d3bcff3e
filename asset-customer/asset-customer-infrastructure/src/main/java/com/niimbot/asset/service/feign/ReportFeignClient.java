package com.niimbot.asset.service.feign;

import com.niimbot.means.AssetIncrReportDto;
import com.niimbot.means.AssetOrgReportDto;
import com.niimbot.report.AssetReportDto;
import com.niimbot.report.AssetStatusReportDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/17 15:57
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ReportFeignClient {

    /**
     * 首页-资产状态统计
     *
     * @return 资产状态统计对象
     */
    @GetMapping(value = "server/means/reports/assetStatusReport")
    AssetReportDto<AssetStatusReportDto> assetStatusReport();

    /**
     * 首页-资产增量统计
     *
     * @param year 年
     * @return 资产增量统计对象
     */
    @GetMapping(value = "server/means/reports/assetIncrReport/{year}")
    AssetIncrReportDto assetIncrReport(@PathVariable("year") Long year);

    /**
     * 首页-资产使用组织统计
     *
     * @param pid   父id
     * @return 资产使用组织统计对象
     */
    @GetMapping(value = "server/means/reports/assetReportByUseOrg")
    List<AssetOrgReportDto> assetReportByUseOrg(@RequestParam(value = "orgId") Long rootId,
                                                @RequestParam(value = "pid", required = false) Long pid);

    /**
     * 首页-资产分类统计
     *
     * @param pid 父id
     * @return 资产分类统计对象
     */
    @GetMapping(value = "server/means/reports/assetReportByCategory")
    List<AssetOrgReportDto> assetReportByCategory(@RequestParam(value = "pid", required = false) Long pid);

}
