package com.niimbot.asset.controller.pc.means;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.ValidationUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.*;
import com.niimbot.asset.service.feign.*;
import com.niimbot.asset.service.feign.equipment.EquipmentSparePartsFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.equipment.EquipmentSparePartsDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.means.*;
import com.niimbot.system.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.manager.Constants;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2020/12/9 17:39
 */
@Slf4j
@Api(tags = "资产管理")
@ResultController
@RequestMapping("api/pc/asset")
@Validated
public class AssetPcController {

    private static final List<String> excludeOperation = Stream.of("borrow_add", "recv_add", "return_add", "revert_add").collect(Collectors.toList());

    private final AssetService assetService;
    private final AssetFeignClient assetFeignClient;
    private final AssetQueryFieldService queryFieldService;
    private final AssetExcelService assetExcelService;
    private final AssetExcelEditService assetExcelEditService;
    private final RedisService redisService;
    private final CusMenuFeignClient menuFeignClient;
    private final FormFeignClient formFeignClient;
    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;
    private final MaintainPlanFeignClient maintainPlanFeignClient;
    private final CusUserSettingFeignClient settingFeignClient;
    private final DesensitizationDataUtil desensitizationDataUtil;
    private final EquipmentSparePartsFeignClient equipmentSparePartsFeignClient;
    private final AssetRelationFeignClient assetRelationFeignClient;

    @Autowired
    public AssetPcController(AssetService assetService,
                             AssetFeignClient assetFeignClient,
                             AssetQueryFieldService queryFieldService,
                             AssetExcelService assetExcelService,
                             AssetExcelEditService assetExcelEditService,
                             RedisService redisService,
                             CusMenuFeignClient menuFeignClient,
                             FormFeignClient formFeignClient,
                             AsQueryConditionConfigFeignClient queryConditionConfigFeignClient,
                             MaintainPlanFeignClient maintainPlanFeignClient,
                             CusUserSettingFeignClient settingFeignClient,
                             DesensitizationDataUtil desensitizationDataUtil,
                             EquipmentSparePartsFeignClient equipmentSparePartsFeignClient,
                             AssetRelationFeignClient assetRelationFeignClient) {
        this.assetService = assetService;
        this.assetFeignClient = assetFeignClient;
        this.queryFieldService = queryFieldService;
        this.assetExcelService = assetExcelService;
        this.assetExcelEditService = assetExcelEditService;
        this.redisService = redisService;
        this.menuFeignClient = menuFeignClient;
        this.formFeignClient = formFeignClient;
        this.queryConditionConfigFeignClient = queryConditionConfigFeignClient;
        this.maintainPlanFeignClient = maintainPlanFeignClient;
        this.settingFeignClient = settingFeignClient;
        this.desensitizationDataUtil = desensitizationDataUtil;
        this.equipmentSparePartsFeignClient = equipmentSparePartsFeignClient;
        this.assetRelationFeignClient = assetRelationFeignClient;
    }

    @ApiOperation(value = "【PC】唯一键属性列表")
    @GetMapping(value = "/field/unique")
    public List<Map<String, Object>> assetFieldUnique() {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        // 过滤唯一键字段
        return formFields.stream().filter(f -> {
                    if (ListUtil.of(FormFieldCO.YZC_ASSET_SERIALNO,
                            FormFieldCO.YZC_MATERIAL_SERIALNO,
                            FormFieldCO.YZC_SERIALNO).contains(f.getFieldType())) {
                        return true;
                    }
                    boolean isUnique = f.getFieldProps().containsKey("unique") ? f.getFieldProps().getBoolean("unique") :
                            false;
                    return !FormFieldCO.SPLIT_LINE.equals(f.getFieldType()) && !f.isHidden() && !f.requiredProps() && isUnique;
                })
                .map(f -> ImmutableMap.<String, Object>builder()
                        .put("fieldCode", f.getFieldCode())
                        .put("fieldType", f.getFieldType())
                        .put("fieldName", f.getFieldName())
                        .build()
                ).collect(Collectors.toList());
    }

    @ApiOperation(value = "【PC】数据查询分页列表【仅包含固定属性】")
    @PostMapping(value = "/page")
    public PageUtils<JSONObject> page(@RequestBody AssetQueryConditionDto queryDto) {
        PageUtils<JSONObject> pageResult = assetService.assetPcPage(queryDto);
        if (Objects.nonNull(pageResult)) {
            //设置备件信息
            assembleSparePartsInfo(pageResult.getList());
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(pageResult.getList(), SensitiveObjectTypeEnum.ASSET.getCode());
        }
        return pageResult;
    }

    /**
     * 设置备件信息
     *
     * @param dataList
     */
    private void assembleSparePartsInfo(List<JSONObject> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }

        //资产id列表
        List<Long> assetIdList = dataList.stream().map(item -> item.getLong("id")).collect(Collectors.toList());
        List<EquipmentSparePartsDto> equipmentSparePartsDtoList = equipmentSparePartsFeignClient.queryByAssetId(assetIdList);
        if (CollUtil.isEmpty(equipmentSparePartsDtoList)) {
            return;
        }

        //查询当前资产是否为子资产，返回子资产id列表
        List<Long> subAssetIdList = assetRelationFeignClient.existSubAsset(assetIdList);

        Map<Long, EquipmentSparePartsDto> equipmentSparePartsMap = equipmentSparePartsDtoList.stream().collect(Collectors.toMap(EquipmentSparePartsDto::getAssetId, value -> value, (v1, v2) -> v2));
        for (JSONObject item : dataList) {
            //是否为子资产
            boolean subAsset = Boolean.FALSE;
            if (!CollUtil.isEmpty(subAssetIdList) && subAssetIdList.contains(item.getLong("id"))) {
                subAsset = Boolean.TRUE;
            }
            item.put("subAsset", subAsset);

            if (Objects.isNull(equipmentSparePartsMap.get(item.getLong("id")))) {
                item.put("sparePartsNum", 0);
                item.put("assetRelationNum", 0);
            } else {
                item.put("assetRelationNum", equipmentSparePartsMap.get(item.getLong("id")).getAssetRelationNum());
                item.put("sparePartsNum", equipmentSparePartsMap.get(item.getLong("id")).getSparePartsNum().intValue());
            }
            //前端展示逻辑，子资产需要显示为1
            if (subAsset) {
                item.put("assetRelationNum", 1);
            }
        }
    }

    @ApiOperation("员工资产列表清单")
    @GetMapping("/empAssetList/{empId}")
    public Map<String, List<JSONObject>> empAssetList(@PathVariable("empId") Long empId) {
        return assetService.empAssetList(empId);
    }

    @ApiOperation(value = "【PC】查询导出资产动态列")
    @GetMapping(value = "/export/head")
    public List<ExportAssetHeadDto> assetExportHead(
            @ApiParam(name = "standardId", value = "标准品ID")
            @RequestParam(value = "standardId", required = false) Long standardId) {
        List<AssetHeadDto> headDtos = queryFieldService.exportHeadField(standardId);
        List<ExportAssetHeadDto> result = headDtos.stream().map(f -> BeanUtil.copyProperties(f, ExportAssetHeadDto.class))
                .collect(Collectors.toList());
        AsCusUserSettingDto setting = settingFeignClient.getSetting();
        if (setting != null) {
            List<String> exportField = setting.getAssetExportField();
            if (CollUtil.isNotEmpty(exportField)) {
                result.forEach(f ->
                        f.setCheck(exportField.contains(f.getCode()))
                );
            }
        }
        return result;
    }

    @ApiOperation(value = "【PC】通过资产ID查询资产操作")
    @PostMapping(value = "/getAssetOptByAssetId")
    public List<AssetOperationDto> getAssetOptByAssetId(@RequestBody AssetOperationQueryDto optQueryDto) {
        List<AssetOperationDto> optByAssetId = assetFeignClient.getAssetOptByAssetId(optQueryDto.getIds());
        //设备需要去除掉领用、借用等操作
        if (Objects.nonNull(optQueryDto.getType()) && optQueryDto.getType() == 2) {
            optByAssetId = optByAssetId.stream().filter(item -> !excludeOperation.contains(item.getCode())).collect(Collectors.toList());
        }
        List<CusMenuDto> cusMenuDtos = menuFeignClient.userMenuPcList();
        List<String> buttonList = cusMenuDtos.stream()
                .filter(m -> m.getMenuType().equalsIgnoreCase(DictConstant.MENU_TYPE_BUTTON))
                .map(CusMenuDto::getMenuCode)
                .collect(Collectors.toList());
        List<AssetOperationDto> opts = new ArrayList<>();
        Integer delete = AssetConstant.OPT_DELETE;
        Integer edit = AssetConstant.OPT_EDIT;
        Integer copy = AssetConstant.OPT_COPY;
        int assetSize = optQueryDto.getIds().size();
        for (AssetOperationDto item : optByAssetId) {
            // 添加菜单权限控制
            if (!BooleanUtil.isTrue(item.getIsDisable())) {
                item.setIsDisable(!buttonList.contains(item.getCode()));
            }

            // 资产复制菜单权限
            if (copy.equals(Convert.toInt(item.getOrderType()))) {
                if (1 == assetSize && buttonList.contains(item.getCode())) {
                    item.setIsDisable(false);
                } else {
                    item.setIsDisable(true);
                }
            }

            // 行级操作去除编辑按钮
            if (BooleanUtil.isTrue(optQueryDto.getIsRow())) {
                if (!edit.equals(item.getId())) {
                    item.setName("资产" + item.getName());
                    opts.add(item);
                }
            } else {
                if (edit.equals(Convert.toInt(item.getOrderType())) || delete.equals(Convert.toInt(item.getId()))) {
                    item.setName("批量" + item.getName());
                } else {
                    item.setName("资产" + item.getName());
                }
                if ((!Convert.toInt(item.getOrderType(), -1).equals(AssetConstant.ORDER_TYPE_MAINTAIN)) &&
                        (AssetConstant.ORDER_TYPE_ALLOCATE != Convert.toInt(item.getOrderType()))) {
                    opts.add(item);
                }
            }
        }
        // 有无保养计划
        List<MaintainPlanInfoDto> maintainPlans = maintainPlanFeignClient.getByAssetIds(optQueryDto.getIds());
        if (CollUtil.isNotEmpty(maintainPlans)) {
            opts.forEach(v -> {
                if (Objects.equals(delete, v.getId())) {
                    v.setIsDisable(true);
                }
            });
        }
        return opts;
    }

    @ApiOperation(value = "【PC】导出资产模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response,
                               @ApiParam(name = "standardId", value = "标准品ID")
                               @RequestParam(value = "standardId", required = false) Long standardId) {
        try (OutputStream out = response.getOutputStream()) {
            String fileName = "资产导入模板.xlsx";
            if (standardId != null) {
                FormVO standard = formFeignClient.getByFormId(new FormByIdQry(standardId,
                        ListUtil.of(1L, LoginUserThreadLocal.getCompanyId())));
                if (standard != null) {
                    fileName = standard.getFormName() + "-" + fileName;
                }
            }
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
            ExcelWriter writer = assetExcelService.buildExcelWriter(standardId);
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】导入资产模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public void importTemplate(@ApiParam(name = "standardId", value = "标准品ID")
                               @RequestParam(value = "standardId", required = false) Long standardId,
                               @RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }

        try (InputStream stream = file.getInputStream()) {
            assetExcelService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId, standardId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】线下资产模板解析")
    @PostMapping(value = "/resolveOfflineTemplate", consumes = "multipart/form-data")
    @ResultMessage(value = "解析成功")
    public List<List<LuckySheetModel>> importOfflineTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        try (InputStream stream = file.getInputStream()) {
            return assetExcelService.resolveExcelStream(stream);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "导入模板解析失败");
        }
    }

    @ApiOperation(value = "【PC】导入线下资产模板")
    @PostMapping(value = "/importOfflineTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public void importOfflineTemplate(@ApiParam(name = "standardId", value = "标准品ID")
                                      @RequestParam(value = "standardId", required = false) Long standardId,
                                      @ApiParam(name = "mapping", value = "字段映射")
                                      @RequestParam(value = "mapping") String mappingStr,
                                      @RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        List<ImportMappingDto> mapping;
        try {
            mapping = JSONArray.parseArray(mappingStr).toJavaList(ImportMappingDto.class);
            for (ImportMappingDto importMappingDto : mapping) {
                ValidationUtils.validateDto(importMappingDto);
            }
        } catch (BusinessException bx) {
            throw bx;
        } catch (Exception e) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "字段映射解析错误");
        }

        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }

        try (InputStream stream = file.getInputStream()) {
            assetExcelService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId, standardId, mapping);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return assetExcelService.importError(taskId);
    }

    @ApiOperation(value = "【PC】重新导入错误数据查询")
    @GetMapping(value = "/reImport/{taskId}")
    public ReImportDto reImport(@PathVariable("taskId") Long taskId) {
        return assetExcelService.reImport(taskId);
    }

    @ApiOperation(value = "【PC】批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels, HttpServletRequest request) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        assetExcelService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.ASSET));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation(value = "【PC】资产导出")
    @PostMapping("/export")
    public void export(@RequestBody AssetQueryConditionDto queryDto, HttpServletResponse response) {
        try {
            List<QueryConditionDto> queryCondition = queryFieldService.assetAllHeadField();
            if (CollUtil.isNotEmpty(queryDto.getExpStandardIds())) {
                try {
                    List<QueryConditionStandardDto> standardDtos = queryFieldService.standardAllField(queryDto.getExpStandardIds(), false, true);
                    for (QueryConditionStandardDto standardDto : standardDtos) {
                        queryCondition.addAll(standardDto.getConditions());
                    }
                } catch (Exception e) {
                    log.error("export asset excel error, {}", e.getMessage(), e);
                }
            }
            queryCondition = queryCondition.stream()
                    .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                    .filter(f -> queryDto.getExpCodes().contains(f.getCode()))
                    .collect(Collectors.toList());
            // 查询head
            LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
            for (QueryConditionDto condition : queryCondition) {
                headerData.put(condition.getCode(), condition.getName());
            }
            // 查询数据
            List<JSONObject> excel = assetExcelService.getExcelData(queryDto, queryDto.getExpStandardIds());
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.ASSET.getCode());
            Set<String> keySet = headerData.keySet();
            excel.forEach(json -> keySet.forEach(key -> json.putIfAbsent(key, null)));
            String fileName = "资产-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            settingFeignClient.updateSimplify(new AsCusUserSettingDto()
                    .setAssetExportField(queryDto.getExpCodes()));
            if (CollUtil.isNotEmpty(excel)) {
                AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(excel.size()))));
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】导出资产入库单")
    @PostMapping("/rk/export")
    public void rkExport(@RequestBody AssetQueryConditionDto queryDto, HttpServletResponse response) {
        List<AssetHeadDto> headDtos = queryFieldService.assetHeadView();
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_HEAD);
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        Long standardId = -1L;
        if (generalDto.getStandardId() != null) {
            standardId = generalDto.getStandardId();
        }

        Map<String, String> codeTypeMap = new HashMap<>();
        headDtos = headDtos.stream()
                .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                .peek(f -> codeTypeMap.put(f.getCode(), f.getType()))
                .collect(Collectors.toList());
        // 查询head
        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
        for (AssetHeadDto head : headDtos) {
            if (!QueryFieldConstant.FIELD_CREATE_TIME.equals(head.getCode())) {
                headerData.put(head.getCode(), head.getName());
            }
        }
        headerData.put(QueryFieldConstant.FIELD_CREATE_TIME, "入库日期");
        // 查询数据
        List<JSONObject> excel = assetExcelService.getExcelData(queryDto, ListUtil.of(standardId));
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.ASSET.getCode());
        try (OutputStream out = response.getOutputStream()) {
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode("资产入库单.xlsx", StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode("资产入库单.xlsx", StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");

            int headLen = headerData.size();
            ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(true);
            Sheet sheet = writer.getSheet();
            // 设置边框
            StyleSet styleSet = writer.getStyleSet();
            styleSet.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
            styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.TOP);

            // 设置表头的cellStyle
            CellStyle cellStyle = writer.createCellStyle();
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);

            // 写入文件标题
            writer.merge(0, 0, 0, headLen - 1, "资产入库单", false);
            Cell title = writer.getCell(0, 0);
            CellStyle commentStyle = writer.createCellStyle();
            commentStyle.setAlignment(HorizontalAlignment.CENTER);
            commentStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
            title.setCellStyle(commentStyle);

            // 写入表头
            AtomicInteger rowIdx = new AtomicInteger(1);
            List<String> headCodeList = new ArrayList<>();
            AtomicInteger cellIdx = new AtomicInteger(0);
            Row header = writer.getOrCreateRow(rowIdx.getAndIncrement());
            headerData.forEach((k, v) -> {
                int idx = cellIdx.getAndIncrement();
                headCodeList.add(k);
                Cell cell = header.createCell(idx);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(v);
                // 调整每一列宽度
                sheet.autoSizeColumn((short) idx);
                // 解决自动设置列宽中文失效的问题
                sheet.setColumnWidth(idx, sheet.getColumnWidth(idx) * 17 / 10);
            });
            excel.forEach(f -> {
                int idx = rowIdx.getAndIncrement();
                Row row = writer.getOrCreateRow(idx);
                for (int i = 0; i < headCodeList.size(); i++) {
                    String code = headCodeList.get(i);
                    Cell cell = row.createCell(i);
                    cell.setCellStyle(cellStyle);
                    if (FormFieldCO.NUMBER_INPUT.equals(codeTypeMap.get(code))) {
                        Double num = Convert.toDouble(f.get(code));
                        if (num != null) {
                            cell.setCellValue(num);
                        } else {
                            String numStr = Convert.toStr(f.get(code));
                            if (StrUtil.isNotEmpty(numStr)) {
                                cell.setCellValue(numStr);
                            }
                        }
                    } else {
                        String str = Convert.toStr(f.get(code));
                        if (StrUtil.isNotEmpty(str)) {
                            cell.setCellValue(str);
                        }
                    }
                }
            });

            if (CollUtil.isNotEmpty(excel)) {
                AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS_RK, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(excel.size()))));
            }

            // 写入尾部
            int foot = rowIdx.get();
            writer.merge(foot, foot + 3, 0, headLen - 1, "入库须知（备注）：", false);
            rowIdx.set(foot + 3);
            rowIdx.incrementAndGet();
            int splitNum = headLen / 3;
            writer.merge(rowIdx.get(), rowIdx.get(), 0, splitNum - 1, "采购员（签字）：", false);
            writer.merge(rowIdx.get(), rowIdx.get(), splitNum, splitNum * 2 - 1, "管理员（签字）：", false);
            writer.merge(rowIdx.get(), rowIdx.get(), splitNum * 2, headLen - 1, "验收人员（签字）：", false);
            rowIdx.incrementAndGet();
            writer.merge(rowIdx.get(), rowIdx.get(), 0, splitNum - 1, "入账公司：", false);
            writer.merge(rowIdx.get(), rowIdx.get(), splitNum, headLen - 1, "验收意见：", false);

            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            log.error("exportData======", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】导出资产编辑模板")
    @GetMapping(value = "/exportEditTemplate")
    public void exportEditTemplate(HttpServletResponse response) {
        try (OutputStream out = response.getOutputStream()) {
            String fileName = "资产编辑模板.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");

            ExcelWriter writer = assetExcelEditService.buildEditExcelWriter();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】导入资产编辑模板")
    @PostMapping(value = "/importEditTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public void importEditTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }

        try (InputStream stream = file.getInputStream()) {
            assetExcelEditService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "【PC】批量导入错误编辑数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importEditError/{taskId}")
    public ImportDto importEditError(@PathVariable("taskId") Long taskId,
                                     @RequestBody List<List<Object>> sheetModels, HttpServletRequest request) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        assetExcelEditService.importEditErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.ASSET_EDIT));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
