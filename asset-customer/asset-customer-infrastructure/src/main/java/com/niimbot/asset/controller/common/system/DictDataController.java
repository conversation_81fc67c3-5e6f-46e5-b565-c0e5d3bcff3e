package com.niimbot.asset.controller.common.system;

import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.service.feign.DictDataFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.DictDataDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/11/9 10:33
 */
@Api(tags = "字典数据接口")
@RequestMapping("api/common/dictData")
@ResultController
public class DictDataController {

    @Autowired
    private DictDataFeignClient dictDataFeignClient;

    @ApiOperation(value = "通过type数据查询")
    @GetMapping("type/{dictType}")
    public List<Map<String, ?>> dict(@PathVariable("dictType") String dictType) {
        List<DictDataDto> list = dictDataFeignClient.selectDictDataByType(dictType);
        return list.stream().map(item ->
                ImmutableMap.of("label", item.getLabel(), "value", item.getValue())
        ).collect(Collectors.toList());
    }
}
