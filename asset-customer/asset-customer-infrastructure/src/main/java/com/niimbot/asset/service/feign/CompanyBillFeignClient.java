package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.sale.CompanyBillDetailDto;
import com.niimbot.sale.CompanyBillDto;
import com.niimbot.sale.CompanyBillQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/12/31 10:45
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CompanyBillFeignClient {


    /**
     * 总账单
     *
     * @param query
     * @return
     */
    @GetMapping("server/sale/bill/page/all")
    PageUtils<CompanyBillDto> pageAll(@SpringQueryMap CompanyBillQueryDto query);


    /**
     * 收入账单
     *
     * @param query
     * @return
     */
    @GetMapping("server/sale/bill/page/income")
    PageUtils<CompanyBillDetailDto> pageIncome(@SpringQueryMap CompanyBillQueryDto query);


    /**
     * 支出账单
     *
     * @param query
     * @return
     */
    @GetMapping("server/sale/bill/page/expenses")
    PageUtils<CompanyBillDetailDto> pageExpenses(@SpringQueryMap CompanyBillQueryDto query);

    @PostMapping("server/sale/bill/temp")
    Boolean temp(@RequestParam("companyId") Long companyId);
}
