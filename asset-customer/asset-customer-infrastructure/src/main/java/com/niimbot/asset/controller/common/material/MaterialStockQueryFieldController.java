package com.niimbot.asset.controller.common.material;

import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.QueryConditionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/6 15:58
 */
@Slf4j
@Validated
@Api(tags = "耗材库存字段管理")
@ResultController
@RequestMapping("api/common/queryField/material/stock")
@RequiredArgsConstructor
public class MaterialStockQueryFieldController {

    private final MaterialQueryFieldService queryFieldService;

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all")
    public List<QueryConditionDto> stockAllQueryField() {
        return queryFieldService.stockAllQueryField();
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view")
    public List<QueryConditionDto> stockQueryView() {
        return queryFieldService.stockQueryView();
    }

}
