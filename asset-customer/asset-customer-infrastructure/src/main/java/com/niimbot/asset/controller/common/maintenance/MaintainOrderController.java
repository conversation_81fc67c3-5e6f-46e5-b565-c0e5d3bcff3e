package com.niimbot.asset.controller.common.maintenance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.*;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.OrderService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.*;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainOrderDto;
import com.niimbot.maintenance.MaintainOrderSubmitDto;
import com.niimbot.means.*;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产保养单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Slf4j
@Api(tags = "保养单管理")
@ResultController
@RequestMapping("api/common/maintenance/maintain/order")
@RequiredArgsConstructor
public class MaintainOrderController {

    private final MaintainOrderFeignClient maintainOrderFeignClient;

    private final OrderSettingFeignClient orderSettingFeignClient;

    private final AssetService assetService;

    private final AsOrderUtil orderUtil;

    private final CacheResourceUtil cacheResourceUtil;

    private final OrderService orderService;

    private final AsOrderUtil asOrderUtil;

    private final DictConvertUtil dictConvertUtil;

    private final AsOrderFeignClient orderFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;
    @Autowired
    protected FormFeignClient formFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @ApiOperation(value = "【保养单】设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated MaintainOrderDto orderDto) {
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_MAINTAIN);
        return maintainOrderFeignClient.getWorkflowStepList(orderDto);
    }

    @ApiOperation(value = "保养单新增")
    @PostMapping
    @RepeatSubmit
    @ResultMessage("提交成功")
    public Boolean insert(@RequestBody @Validated MaintainOrderSubmitDto submitDto) {
        MaintainOrderDto orderDto = submitDto.getOrderDto();
        JSONObject assetData = assetService.getInfo(String.valueOf(orderDto.getAssetId()));
        List<OrderFieldDto> fields = orderSettingFeignClient.listDynamicOrderField(orderDto.getOrderType());
        Optional<OrderFieldDto> first = fields.stream().filter(f -> AssetConstant.FIELD_JSON == f.getType()).findFirst();
        first.ifPresent(orderFieldDto -> {
            Boolean isRequired = orderFieldDto.getIsRequired();
            if (BooleanUtil.isTrue(isRequired) && orderDto.getMaintainContent().size() == 0) {
                throw new BusinessException(SystemResultCode.ORDER_DATA_REQUIRED, orderFieldDto.getName());
            }
        });

        orderDto.setAssetSnapshotData(assetData);
        AuditableCreateOrderResult result = maintainOrderFeignClient.insert(submitDto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.OR_MAS_BY, result));
        return Objects.nonNull(result) && StrUtil.isNotBlank(result.getOrderNo());
    }

    @ApiOperation(value = "保养单详情")
    @GetMapping("/{id}")
    public AsOrderDetailDto info(@PathVariable("id") Long id) {
        MaintainOrderDto orderDto = maintainOrderFeignClient.info(id);
        //数据脱敏处理
        if (Objects.nonNull(orderDto) && Objects.nonNull(orderDto.getAssetSnapshotData())) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderDto.getAssetSnapshotData()), SensitiveObjectTypeEnum.ASSET.getCode());
        }
        if (ObjectUtil.isNull(orderDto.getId())) {
            return new AsOrderDetailDto()
                    .setOrder(new JSONObject())
                    .setApproveInfo(new WorkflowApproveInfoDto());
        }
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_MAINTAIN);
        dictConvertUtil.convertToDictionary(orderDto);

        JSONObject orderJson = orderUtil.toJSONObject(orderDto);
        if (orderDto.getApproveStatus() == 0) {
            return new AsOrderDetailDto()
                    .setOrder(orderJson)
                    .setApproveInfo(new WorkflowApproveInfoDto());
        }
        WorkflowApproveInfoDto approveInfoDto;
        if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
            approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MAINTAIN, id);
            if (approveInfoDto == null || CollUtil.isEmpty(approveInfoDto.getExecuteList())) {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MAINTAIN, id);
            }
        } else {
            approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MAINTAIN, id);
            if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                approveInfoDto.getExecuteList().stream()
                        .filter(step -> step.getType() == 8)
                        .forEach(step -> {
                            step.setStatus(orderDto.getApproveStatus());
                            dictConvertUtil.convertToDictionary(step);
                        });
            }
        }
        return new AsOrderDetailDto()
                .setOrder(orderJson)
                .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
    }

    @ApiOperation(value = "保养单批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<AsOrderDetailDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_MAINTAIN);
        return requestDto.getOrderIds().stream().map(this::info).collect(Collectors.toList());
    }

    @ApiOperation(value = "保养单分页列表")
    @AutoConvert
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody AsOrderQueryDto queryDto) {
        queryDto.setOrderType(OrderFormTypeEnum.MAINTAIN.getCode());
        PageUtils<AsOrderDto> page = maintainOrderFeignClient.page(queryDto);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (AsOrderDto orderDto : page.getList()) {
            orderDto.setOrderType(11);
            JSONObject orderJson = asOrderUtil.toJSONObject(orderDto);
            list.add(orderJson);
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "保养单分页列表")
    @AutoConvert
    @GetMapping("/page")
    public PageUtils<JSONObject> pageGet(AsOrderQueryDto dto) {
        return page(dto);
    }

    @ApiOperation(value = "单据ID查询资产快照")
    @GetMapping("/assetData/{orderId}")
    public JSONObject getAssetData(@PathVariable("orderId") Long orderId) {
        return maintainOrderFeignClient.getAssetData(orderId);
    }

    @ApiOperation(value = "【PC】导出资产保养单据")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrder(@RequestBody AsOrderQueryDto query) {
        return orderService.exportMaintainOrderCard(query);
    }

    @ApiOperation(value = "【PC】导出资产保养单据资产")
    @PostMapping(value = "/exportOrder/assets")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderAssets(@RequestBody @Validated AsOrderQueryDto dto) {
        dto.setOrderType(OrderFormTypeEnum.MAINTAIN.getCode());
        PageUtils<AsOrderDto> page = maintainOrderFeignClient.page(dto);
        List<AsOrderDto> list = page.getList();
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        list.forEach(o -> o.setOrderType(OrderFormTypeEnum.MAINTAIN.getCode()));
        dictConvertUtil.convertToDictionary(list);
        FormVO orderForm = orderFeignClient.getForm(dto.getOrderType());
        Map<String, String> orderDateFormatType = new HashMap<>();
        Set<String> orderMultiSelectSet = new HashSet<>();
        Map<String, Boolean> orderNumberMap = new HashMap<>();
        for (FormFieldCO it : orderForm.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                orderDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                orderMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                orderNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        orderDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        orderDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        List<Long> orderIds = new ArrayList<>();
        List<JSONObject> orders = new ArrayList<>();
        for (AsOrderDto order : list) {
            orderIds.add(order.getId());
            JSONObject orderJson = asOrderUtil.toJSONObject(order);
            if (Objects.nonNull(order.getCreateTime())) {
                long timeVal = order.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("createTime", timeVal);
                }
            }
            if (Objects.nonNull(order.getUpdateTime())) {
                long timeVal = order.getUpdateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("updateTime", timeVal);
                }
            }
            orderNumberMap.forEach((code, percentage) -> {
                Number number = Convert.toNumber(orderJson.get(code));
                if (ObjectUtil.isNotNull(number)) {
                    if (BooleanUtil.isTrue(percentage)) {
                        orderJson.put(code, number + "%");
                    } else {
                        orderJson.put(code, number);
                    }
                }
            });
            orderDateFormatType.forEach((code, fmt) -> {
                String date = orderJson.getString(code);
                if (StrUtil.isNotEmpty(date)) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                        orderJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换时间异常", code, date);
                    }
                }
            });
            orderMultiSelectSet.forEach(code -> {
                try {
                    JSONArray jsonArray = orderJson.getJSONArray(code);
                    if (CollUtil.isNotEmpty(jsonArray)) {
                        List<String> strings = jsonArray.toJavaList(String.class);
                        String collect = String.join(",", strings);
                        orderJson.put(code, collect);
                    } else {
                        orderJson.put(code, null);
                    }
                } catch (Exception e) {
                    log.warn("[{}] [{}]转换数组异常", code, orderJson.get(code));
                }
            });
            orders.add(orderJson);
        }

        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(orders, SensitiveObjectTypeEnum.ASSET.getCode());

        OrderJsonUtil.convertJsonKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, orders);

        List<AsOrderAssetDto> assets = maintainOrderFeignClient.getAssetsByOrderId(orderIds);
        FormVO assetFormVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        Map<String, String> assetDateFormatType = new HashMap<>();
        Set<String> assetMultiSelectSet = new HashSet<>();
        Map<String, Boolean> assetNumberMap = new HashMap<>();
        for (FormFieldCO it : assetFormVO.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                assetDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                assetMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                assetNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        assetDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        assetDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        Map<Long, List<AsOrderAssetDto>> assetsMap = assets.stream().collect(Collectors.groupingBy(AsOrderAssetDto::getOrderId));
        Map<Long, List<JSONObject>> assetsJsonMap = new HashMap<>();

        Map<String, String> translationCodeMap = assetFormVO.getFormFields().stream()
                .filter(f -> StrUtil.isNotEmpty(f.getTranslationCode()))
                .collect(Collectors.toMap(FormFieldCO::getTranslationCode, FormFieldCO::getFieldCode));

        Map<Long, String> empNameCache = new ConcurrentHashMap<>();
        assetsMap.forEach((key, assetDtoList) -> {
            List<JSONObject> assetJsons = new ArrayList<>();
            for (AsOrderAssetDto assetDto : assetDtoList) {
                JSONObject assetJson = asOrderUtil.toJSONObject(assetDto);
                translationCodeMap.forEach((k, v) -> assetJson.put(v, assetJson.get(k)));
                // 特殊处理创建人，不在表单内
                if (assetJson.containsKey(AssetConstant.ASSET_FIELD_CREATE_BY)) {
                    Long createBy = assetJson.getLong(AssetConstant.ASSET_FIELD_CREATE_BY);
                    if (empNameCache.containsKey(createBy)) {
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, empNameCache.get(createBy));
                    } else {
                        String userNameAndCode = cacheResourceUtil.getUserNameAndCode(createBy);
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, userNameAndCode);
                        empNameCache.put(createBy, userNameAndCode);
                    }
                }
//                assetUtil.translateAssetJsonView(assetJson, assetFormVO.getFormFields());

                assetNumberMap.forEach((code, percentage) -> {
                    Number number = Convert.toNumber(assetJson.get(code));
                    if (ObjectUtil.isNotNull(number)) {
                        if (BooleanUtil.isTrue(percentage)) {
                            assetJson.put(code, number + "%");
                        } else {
                            assetJson.put(code, number);
                        }
                    }
                });
                assetDateFormatType.forEach((code, fmt) -> {
                    String date = assetJson.getString(code);
                    if (StrUtil.isNotEmpty(date)) {
                        try {
                            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                            assetJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                        } catch (Exception e) {
                            log.warn("[{}] [{}]转换时间异常", code, date);
                        }
                    }
                });
                assetMultiSelectSet.forEach(code -> {
                    try {
                        JSONArray jsonArray = assetJson.getJSONArray(code);
                        if (CollUtil.isNotEmpty(jsonArray)) {
                            List<String> strings = jsonArray.toJavaList(String.class);
                            String collect = String.join(",", strings);
                            assetJson.put(code, collect);
                        } else {
                            assetJson.put(code, null);
                        }
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换数组异常", code, assetJson.get(code));
                    }
                });

                assetJsons.add(assetJson);
            }
            assetsJsonMap.put(key, assetJsons);
        });
        return orderService.exportOrderAssets(dto, orders, assetsJsonMap);
    }

}
