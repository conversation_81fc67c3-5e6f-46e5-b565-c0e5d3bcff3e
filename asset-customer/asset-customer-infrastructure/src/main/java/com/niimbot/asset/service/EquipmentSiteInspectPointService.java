package com.niimbot.asset.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.niimbot.luckysheet.LuckySheetModel;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

public interface EquipmentSiteInspectPointService {

    int MAX_BATCH = 1000;

    ExcelWriter buildWriter();

    void exportTemplate(HttpServletResponse response);

    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId);

    List<List<LuckySheetModel>> importError(Long taskId);

    Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);

    Boolean importErrorDelete(Long taskId);
}
