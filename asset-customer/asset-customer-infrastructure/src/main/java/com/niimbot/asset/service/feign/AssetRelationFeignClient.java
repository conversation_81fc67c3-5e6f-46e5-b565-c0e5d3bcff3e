package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/8 下午3:45
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetRelationFeignClient {

    /**
     * 获取已存在关联关系的资产id
     * @param type
     * @return
     */
    @GetMapping("/server/system/assetRelation/getExistAsset")
    List<Long> getExistAsset(@RequestParam("type") Integer type);

    /**
     * 配置资产关联关系
     * @param relationConfigDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/config")
    Boolean configRelation(@RequestBody AssetRelationConfigDto relationConfigDto);

    /**
     * 批量修改资产关联关系
     * @param relationDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/alterRelationType")
    Boolean alterRelationType(@RequestBody AlterAssetRelationDto relationDto);

    /**
     * 取消组合关系
     * @param relationDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/cancelRelation")
    Boolean cancelRelation(@RequestBody CancelAssetRelationDto relationDto);

    /**
     * 解除资产关联关系
     * @param relationDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/remove")
    Boolean removeRelation(@RequestBody CancelAssetRelationDto relationDto);

    /**
     * 资产关联关系分页查询
     * @param assetQueryConditionDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/page")
    PageUtils<AssetRelationDto> relationPage(@RequestBody AssetQueryConditionDto assetQueryConditionDto);

    /**
     * 子资产分页查询
     * @param relationQueryConditionDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/subAssetPage")
    PageUtils<AssetRelationDto> subAssetPage(@RequestBody AssetRelationQueryConditionDto relationQueryConditionDto);

    /**
     * 资产关联关系分页查询-app
     * @param assetQueryConditionDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/appPage")
    PageUtils<AssetRelationAppDto> relationAppPage(@RequestBody AssetQueryConditionDto assetQueryConditionDto);

    /**
     * 子资产分页查询-app
     * @param relationQueryConditionDto
     * @return
     */
    @PostMapping("/server/system/assetRelation/subAssetAppPage")
    PageUtils<AssetRelationAppDto> subAssetAppPage(@RequestBody AssetRelationQueryConditionDto relationQueryConditionDto);

    /**
     * 根据子资产查询主资产信息
     * @param subAssetId
     * @return
     */
    @GetMapping("/server/system/assetRelation/getBySubAssetIdApp/{subAssetId}")
    AssetRelationAppDto getBySubAssetIdApp(@PathVariable("subAssetId") Long subAssetId);

    /**
     * 查询当前资产是否为子资产
     * @param assetIdList
     * @return
     */
    @GetMapping("/server/system/assetRelation/existSubAsset")
    List<Long> existSubAsset(@RequestParam("assetIdList") List<Long> assetIdList);

    /**
     * 查询主资产id
     * @param assetId
     * @return
     */
    @GetMapping("/server/system/assetRelation/getMainAssetId")
    Long getMainAssetId(@RequestParam("assetId") Long assetId);
}
