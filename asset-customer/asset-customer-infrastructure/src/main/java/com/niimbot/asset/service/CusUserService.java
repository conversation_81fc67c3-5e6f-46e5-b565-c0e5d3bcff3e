package com.niimbot.asset.service;

import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusUserDetailDto;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 * @date 2021/1/15 09:09
 */
public interface CusUserService {
    /**
     * 通过用户名查询用户
     *
     * @param account 用户名
     * @return 用户对象信息
     */
    CusUserDto selectUserByAccount(String account);

    /**
     * 通过unionId查询用户
     *
     * @param unionId 用户名
     * @return 用户对象信息
     */
    CusUserDto selectUserByUnionId(String unionId);

    /**
     * 用户详情信息
     *
     * @return 用户登录详情
     */
    CusUserDetailDto personDetail();

    /**
     * sso 检查账号
     *
     * @param account
     * @return 登录账号信息
     */
    default CusUserDto checkAccount(String account) {
        CusUserDto cusUser = selectUserByAccount(account);
        // 账户信息不存在
        if (cusUser == null) {
            CusEmployeeDto emp = SpringUtil.getBean(CusEmployeeFeignClient.class).checkMobile(account);
            // 判断是否存在用户信息
            if (ObjectUtil.isNotEmpty(emp)) {
                throw new BusinessException(SystemResultCode.USER_REGISTER_WITHOUT_ACCOUNT);
            } else {
                throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
            }
        } else if (cusUser.getStatus().shortValue() == DictConstant.SYS_DISABLE) {
            throw new BusinessException(SystemResultCode.USER_ACCOUNT_FORBIDDEN);
        } else if (ObjectUtil.isNull(cusUser.getCompanyId())) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_REGISTER);
        } /*else if (ObjectUtil.isNull(cusUser.getOrgId())) {
            throw new BusinessException(SystemResultCode.USER_HAS_NO_ROLE);
        }*/
        return cusUser;
    }

    CusUserDto selectUserByAppKey(String appKey, String appSecret);
}
