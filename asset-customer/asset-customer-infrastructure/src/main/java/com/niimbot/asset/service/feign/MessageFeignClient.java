package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.message.HomePageMsg;
import com.niimbot.message.HomePageMsgV2;
import com.niimbot.message.UserMsgStat;
import com.niimbot.system.MessageDto;
import com.niimbot.system.MessageIsNoticeDto;
import com.niimbot.system.MessagePageDto;
import com.niimbot.system.MessageReadDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 消息feign客户端
 *
 * <AUTHOR>
 * @date 2021/4/19 15:28
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MessageFeignClient {

    @GetMapping("/server/message/page/{clientType}")
    PageUtils<MessageDto> page(@SpringQueryMap MessagePageDto dto);

    @PutMapping("/server/message/allRead/{clientType}/{businessType}")
    Boolean allRead(@PathVariable("clientType") Integer clientType, @PathVariable("businessType") Integer businessType);

    @GetMapping("/server/message/unread/count/{clientType}")
    Integer unreadCount(@PathVariable("clientType") Integer clientType);

    @GetMapping("/server/message/latestNotice/{clientType}")
    MessageIsNoticeDto latestNotice(@PathVariable("clientType") Integer clientType);

    /**
     * 站内信查询
     * @return
     */
    @GetMapping("server/message")
    List<MessageDto> getMessage();

    /**
     * 站内信已读
     * @param readDto
     * @return
     */
    @PutMapping("server/message/read")
    Boolean readMessage(@RequestBody MessageReadDto readDto);

    /**
     * 站内信详情
     * @param messageId
     * @param type
     * @return
     */
    @GetMapping("server/message/messageDetail")
    MessageDto getMessageDetail(@RequestParam(value = "messageId", required = false) String messageId,
                                @RequestParam(value = "type", required = false) Integer type,
                                @RequestParam(value = "businessType") Integer businessType);

    @GetMapping("server/message/getMessageBodyForQuery/{msgId}")
    MessageDto getMessageBodyForQuery(@PathVariable("msgId") Long msgId);

    @GetMapping("server/message/getUserMsgStat")
    List<UserMsgStat> getUserMessage(@RequestParam("companyId") Long companyId, @RequestParam("userId") Long userId);

    @Deprecated
    @GetMapping("server/message/homePageMsg")
    HomePageMsg homePageMsg(@RequestParam("userId") Long userId);

    @GetMapping("server/message/homePageMsg/v2")
    HomePageMsgV2 homePageMsgV2(@RequestParam("limit") Integer limit);

    @PostMapping("server/message/manual/schedule")
    Boolean schedule();
}
