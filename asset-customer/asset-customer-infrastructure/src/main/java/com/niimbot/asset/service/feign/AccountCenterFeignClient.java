package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.system.*;
import com.niimbot.thirdparty.ThirdPartyUser;
import com.niimbot.thirdparty.ThirdPartyUserAuth;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账户中心
 *
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AccountCenterFeignClient {

    String BASE_URL = "/system/server/account/center";

    @GetMapping(BASE_URL + "/by/id/{id}")
    AccountDto getAccountById(@PathVariable("id") Long id);

    @GetMapping(BASE_URL + "/by/way/{way}")
    AccountDto getAccountByWay(@PathVariable("way") String way);

    @GetMapping(BASE_URL + "/by/way2/{way}")
    AccountDto getAccountByWay2(@PathVariable("way") String way);

    @GetMapping(BASE_URL + "/byThirdPartyUserUnionId/{type}/{unionId}")
    AccountDto getAccountByThirdPartyUserUnionId(@PathVariable("type") String type, @PathVariable("unionId") String unionId);

    @GetMapping(BASE_URL + "/getLoginInfoByWay/{way}")
    CusUserDto getLoginInfoByWay(@PathVariable("way") String way);

    @GetMapping(BASE_URL + "/getLoginInfoByAppKey/{appKey}/{appSecret}")
    CusUserDto getLoginInfoByAppKey(@PathVariable("appKey") String appKey, @PathVariable("appSecret") String appSecret);

    @GetMapping(BASE_URL + "/getLoginInfoByMobile/{mobile}/{smsCode}")
    CusUserDto getLoginInfoByMobile(@PathVariable("mobile") String mobile, @PathVariable("smsCode") String smsCode);

    @GetMapping(BASE_URL + "/getLoginInfoByThirdParty/{type}/{uniqueId}")
    CusUserDto getLoginInfoByThirdParty(@PathVariable("type") String type, @PathVariable("uniqueId") String uniqueId);

    @GetMapping(BASE_URL + "/getEmpLoginInfo/{empId}")
    CusUserDto getEmpLoginInfo(@PathVariable("empId") Long empId);

    @GetMapping(BASE_URL + "/getCompanyEmpLoginInfo/{accountId}/{companyId}")
    CusUserDto getCompanyEmpLoginInfo(@PathVariable("accountId") Long accountId, @PathVariable("companyId") Long companyId);

    @GetMapping(BASE_URL + "/companyList/{accountId}")
    List<AccountEmployeeDto> companyList(@PathVariable("accountId") Long accountId);

    @DeleteMapping(BASE_URL + "/deleteEmployeeAccount/{empId}")
    Boolean deleteEmployeeAccount(@PathVariable("empId") Long empId);

    @DeleteMapping(BASE_URL + "/batchDeleteEmployeeAccount")
    Boolean batchDeleteEmployeeAccount(@RequestBody List<Long> empIds);

    @DeleteMapping(BASE_URL + "/deleteCompanyAccount/{companyId}/{accountId}")
    Boolean deleteCompanyAccount(@PathVariable("companyId") Long companyId, @PathVariable("accountId") Long accountId);

    @GetMapping(BASE_URL + "/basicInfo/{accountId}/{employeeId}")
    AccountBasicInfo getAccountBasicInfo(@PathVariable("accountId") Long accountId, @PathVariable("employeeId") Long employeeId);

    @PostMapping(BASE_URL + "/bindMobile")
    Boolean bindMobile(@RequestBody AccountBind accountBind);

    @PostMapping(BASE_URL + "/bindEmail")
    Boolean bindEmail(@RequestBody AccountBind accountBind);

    @PostMapping(BASE_URL + "/bindThirdParty")
    Boolean bindThirdParty(@RequestBody ThirdPartyUser thirdPartyUser);

    @PutMapping(BASE_URL + "/updatePwdAndNickname")
    Boolean updatePwdAndNickname(@RequestBody AccountDto dto);

    @PutMapping(BASE_URL + "/updateLastCompany/{accountId}/{companyId}")
    void updateLastCompany(@PathVariable("accountId") Long accountId, @PathVariable("companyId") Long companyId);

    @PostMapping(BASE_URL + "/sendInviteLink/{companyId}/{operatorId}")
    InviteLink sendInviteLink(@PathVariable("companyId") Long companyId, @PathVariable("operatorId") Long operatorId);

    @PutMapping(BASE_URL + "/preActivatedAccount/{companyId}/{empId}")
    Boolean preActivatedAccount(@PathVariable("companyId") Long companyId, @PathVariable("empId") Long empId);

    @PostMapping(BASE_URL + "/activateAccountByMobile")
    ActivateAccountResult activateAccountByMobile(@RequestBody ActivateAccount activateAccount);

    @PostMapping(BASE_URL + "/activateAccountByThirdParty")
    ActivateAccountResult activateAccountByThirdParty(@RequestBody ActivateAccount activateAccount);

    @PostMapping(BASE_URL + "/activateAccountByThirdPartyBindMobile")
    ActivateAccountResult activateAccountByThirdPartyBindMobile(@RequestBody ActivateAccount activateAccount);

    @PostMapping(BASE_URL + "/bindAccountForThirdParty")
    CusUserDto bindThirdPartyAccount(@RequestBody ThirdPartyUser thirdPartyUser);

    @PostMapping(BASE_URL + "/bindOrCreateAccountThirdPartyByMobile/{mobile}/{smsCode}")
    CusUserDto bindOrCreateAccountThirdPartyByMobile(@PathVariable("mobile") String mobile, @PathVariable("smsCode") String smsCode, @RequestBody ThirdPartyUser thirdPartyUser);

    @PutMapping(BASE_URL + "/unbind/{way}/{accountId}")
    Boolean unbindWay(@PathVariable("way") Integer way, @PathVariable("accountId") Long accountId);

    @PutMapping(BASE_URL + "/unbindThirdParty/{type}/{accountId}")
    Boolean unbindThirdParty(@PathVariable("type") String type, @PathVariable("accountId") Long accountId);

    @PutMapping(BASE_URL + "/updateNickname")
    Boolean updateNickname(@RequestBody AccountDto dto);

    @PostMapping("/server/thirdparty/user/auth")
    ThirdPartyUser thirdPartyUserAuth(@RequestBody ThirdPartyUserAuth userAuth);

    @GetMapping("/server/thirdparty/user/auth/getConfig/{companyId}/{type}")
    Object getConfig(@PathVariable("companyId") Long companyId, @PathVariable("type") String type);

    @GetMapping("/server/thirdparty/user/auth/scanGetUserId/{companyId}/{type}/{code}")
    String scanGetUserId(@PathVariable("companyId") Long companyId, @PathVariable("type") String type, @PathVariable("code") String code);

    @GetMapping("/server/thirdparty/user/auth/h5AppAuth/{companyId}/{type}/{code}")
    String h5AppAuth(@PathVariable("companyId") Long companyId, @PathVariable("type") String type, @PathVariable("code") String code);
}
