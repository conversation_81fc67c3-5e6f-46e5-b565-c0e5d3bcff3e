package com.niimbot.asset.controller.pc.inventory;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.JacksonConverter;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.InventoryAssetService;
import com.niimbot.asset.service.InventoryExcelService;
import com.niimbot.asset.service.feign.AsOrderFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.InventoryAssetFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.dynamicform.BuildAssetLogDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryAssetDto;
import com.niimbot.inventory.InventoryAssetEditDto;
import com.niimbot.inventory.InventoryAssetHandleDto;
import com.niimbot.inventory.InventoryAssetOrderAssetDto;
import com.niimbot.inventory.InventoryAssetPageExportDto;
import com.niimbot.inventory.InventoryAssetPcDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderSubmitDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 盘点资产后台api
 *
 * <AUTHOR>
 * @date 2021/4/12 11:50
 */
@Slf4j
@Api(tags = "盘点资产管理")
@ResultController
@RequestMapping("api/pc/inventory/asset")
@RequiredArgsConstructor
public class InventoryAssetController {
    private static final String STANDARD_ID = "standardId";

    private final InventoryAssetFeignClient inventoryAssetFeignClient;
    private final AsAssetUtil assetUtil;

    private final AsOrderFeignClient orderFeignClient;
    private final AsOrderUtil asOrderUtil;
    private final AssetService assetService;
    private final InventoryAssetService inventoryAssetService;
    private final FormFeignClient formFeignClient;
    private final AssetFeignClient assetFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final InventoryExcelService inventoryExcelService;


    @ApiOperation(value = "PC盘点资产分页列表")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody @Validated InventorySurplusQueryDto dto) {
        PageUtils<InventoryAssetPcDto> tmpPage = inventoryAssetFeignClient.page(dto);
        List<InventoryAssetPcDto> tmpList = tmpPage.getList();
        List<JSONObject> tmpListReturn = new ArrayList<>();
        //查询属性
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        dictConvertUtil.convertToDictionary(tmpList);
        for (InventoryAssetPcDto rowDto : tmpList) {
            // 资产json数据
            JSONObject assetData = Optional.of(rowDto.getAssetSnapshotData()).orElse(new JSONObject());
            // assetUtil.translateAssetJson(assetData, formVO.getFormFields());

            Long standardId = assetData.getLong(STANDARD_ID);
            List<FormFieldCO> formFieldCOS = inventoryAssetService.buildAttrCache(formVO.getFormFields(), formVO.getFormId(), standardId);

            // 获取资产修改内容
            JSONObject tmpJsonOrigin = JSONObject.parseObject(rowDto.getAssetChangRecordOrigen());
            JSONObject tmpJsonChanged = JSONObject.parseObject(rowDto.getAssetChangRecord());
            String tmpStrJson = assetFeignClient.buildAssetLog(new BuildAssetLogDto(tmpJsonOrigin, tmpJsonChanged, formFieldCOS));
            rowDto.setAssetChangRecordText(tmpStrJson);
            String jackson;
            try {
                jackson = JacksonConverter.MAPPER.writeValueAsString(rowDto);
            } catch (JsonProcessingException e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点资产数据异常");
            }
            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
            json.remove("assetSnapshotData");
            assetData.putAll(json);
            tmpListReturn.add(assetData);
        }
        PageUtils<JSONObject> resultPage = new PageUtils<>();
        resultPage.setTotalCount(tmpPage.getTotalCount());
        resultPage.setCurrPage(tmpPage.getCurrPage());
        resultPage.setPageSize(tmpPage.getPageSize());
        resultPage.setTotalPage(tmpPage.getTotalPage());
        resultPage.setList(tmpListReturn);
        return resultPage;
    }

    @ApiOperation(value = "PC盘点资产盘亏处理")
    @RepeatSubmit
    @PostMapping("/assetloss")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean assetLoss(@RequestBody @Valid InventoryAssetHandleDto pDto) {
        String orderNo = StringUtils.getOrderNo("CZ");
        pDto.getOrderDto().setOrderType(AssetConstant.ORDER_TYPE_DISPOSE);
        //盘点操作
        pDto.getOrderDto().setOrderNo(orderNo);
        InventoryAssetHandleDto tmpDto = inventoryAssetFeignClient.assetHandle(pDto);
        if (tmpDto == null) {
            throw new BusinessException(InventoryResultCode.INVENTORY_SUBMIT_FAIL);
        }
        //盘点结构转资产单据结构
        AsOrderSubmitDto dto = new AsOrderSubmitDto();
        BeanUtil.copyProperties(tmpDto, dto);

        List<AsOrderAssetDto> tmpOrderAssetAr = new ArrayList<>();
        AsOrderAssetDto tmpOrderAsset;
        for (InventoryAssetOrderAssetDto rowInverntoryAsset : tmpDto.getOrderDto().getInventoryassets()) {
            tmpOrderAsset = new AsOrderAssetDto();
            tmpOrderAsset.setId(rowInverntoryAsset.getAssetId());
            tmpOrderAsset.setAssetSnapshotData(rowInverntoryAsset.getAssetSnapshotData());
            tmpOrderAssetAr.add(tmpOrderAsset);
        }
        dto.getOrderDto().setAssetNum(tmpOrderAssetAr.size());
        dto.getOrderDto().setAssets(tmpOrderAssetAr);

        //新增单据操作
        List<Long> assetIds = dto.getOrderDto().getAssets().stream().map(AsOrderAssetDto::getId)
                .collect(Collectors.toList());
        List<JSONObject> assetList = assetService.getInfoList(assetIds);
        dto.getOrderDto().setSummary(asOrderUtil.buildSummary(assetList));
        dto.getOrderDto().setOrderNo(orderNo);
        dto.setConditionId(pDto.getConditionId());
        dto.setExecuteStepDtoList(pDto.getExecuteStepDtoList());
        dto.getOrderDto().getAssets().forEach(asOrderAssetDto -> {
            Long id = asOrderAssetDto.getId();
            JSONObject match = assetList.stream().filter(jsonObject ->
                    jsonObject.getLong("id").longValue() == id.longValue())
                    .findFirst().orElse(new JSONObject());
            asOrderAssetDto.setAssetSnapshotData(match);
        });
        dto.getOrderDto().setOrgId(pDto.getOrderDto().getOrgId());
        orderFeignClient.insert(dto);
        return true;
    }


    @ApiOperation(value = "PC盘点资产盘亏处理-校验资产是否能盘亏处理")
    @RepeatSubmit
    @PutMapping("/checkAssetLoss")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean checkAssetLoss(@RequestBody @NotEmpty(message = "请选择要处理的盘点资产") List<Long> ids) {
        return inventoryAssetFeignClient.checkAssetLoss(ids);
    }

    @ApiOperation(value = "PC盘点资产变更处理")
    @RepeatSubmit
    @PostMapping("/assetChange")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean assetChange(@RequestBody @Valid InventoryAssetHandleDto pDto) {
        String orderNo = StringUtils.getOrderNo("BG");
        pDto.getOrderDto().setOrderType(AssetConstant.ORDER_TYPE_CHANGE);
        //盘点操作
        pDto.getOrderDto().setOrderNo(orderNo);
        InventoryAssetHandleDto tmpDto = inventoryAssetFeignClient.assetHandle(pDto);
        if (tmpDto == null) {
            throw new BusinessException(InventoryResultCode.INVENTORY_SUBMIT_FAIL);
        }

        //盘点结构转资产单据结构
        AsOrderSubmitDto dto = new AsOrderSubmitDto();
        BeanUtil.copyProperties(tmpDto, dto);

        List<AsOrderAssetDto> tmpOrderAssetAr = new ArrayList<>();
        AsOrderAssetDto tmpOrderAsset;
        for (InventoryAssetOrderAssetDto rowInverntoryAsset : tmpDto.getOrderDto().getInventoryassets()) {
            tmpOrderAsset = new AsOrderAssetDto();
            tmpOrderAsset.setId(rowInverntoryAsset.getAssetId());
            tmpOrderAsset.setAssetSnapshotData(rowInverntoryAsset.getAssetSnapshotData());
            tmpOrderAssetAr.add(tmpOrderAsset);
        }
        dto.getOrderDto().setAssetNum(tmpOrderAssetAr.size());
        dto.getOrderDto().setAssets(tmpOrderAssetAr);

        //新增单据操作
        List<Long> assetIds = dto.getOrderDto().getAssets().stream().map(AsOrderAssetDto::getId)
                .collect(Collectors.toList());
        List<JSONObject> assetList = assetService.getInfoList(assetIds);
        dto.getOrderDto().setSummary(asOrderUtil.buildSummary(assetList));
        dto.getOrderDto().setOrderNo(orderNo);
        dto.setConditionId(pDto.getConditionId());
        dto.setExecuteStepDtoList(pDto.getExecuteStepDtoList());
        dto.getOrderDto().getAssets().forEach(asOrderAssetDto -> {
            Long id = asOrderAssetDto.getId();
            JSONObject match = assetList.stream().filter(jsonObject ->
                    jsonObject.getLong("id").longValue() == id.longValue())
                    .findFirst().orElse(new JSONObject());
            asOrderAssetDto.setAssetSnapshotData(match);
        });
        dto.getOrderDto().setOrgId(pDto.getOrderDto().getOrgId());
        try {
            orderFeignClient.insert(dto);
        } catch (Exception e) {
            inventoryAssetFeignClient.assetChangeRollback(pDto);
            throw e;
        }
        return true;
    }

    @ApiOperation(value = "PC盘点资产变更处理-校验资产是否能变更处理")
    @RepeatSubmit
    @PutMapping("/checkAssetChange")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean checkAssetChange(@RequestBody @NotEmpty(message = "请选择要处理的盘点资产") List<Long> ids) {
        return inventoryAssetFeignClient.checkAssetChange(ids);
    }

    /**
     * Todo..
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "PC盘点单资产详情")
    @GetMapping(value = "/detail/{id}")
    public JSONObject getAssetDetail(@PathVariable Long id) {
        InventoryAssetDto assetDetail = inventoryAssetFeignClient.getAssetDetail(id);
        JSONObject json = Optional.of(assetDetail.getAssetSnapshotData()).orElse(new JSONObject());
        json.put("id", assetDetail.getId());
        json.put("inventoryId", assetDetail.getInventoryId());
        json.put("assetId", assetDetail.getAssetId());
        json.put("inventoryRemark", assetDetail.getRemark());
        json.put("pictureUrl", assetDetail.getPictureUrl());
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        assetUtil.translateAssetJson(json, formVO.getFormFields());
        return json;
    }

    @ApiOperation(value = "PC盘点资产列表导出")
    @PostMapping("/export")
    public void assetPageExport(@RequestBody @Validated InventoryAssetPageExportDto dto) {
        inventoryExcelService.inventoryAssetReportExport(dto);
    }

    @ApiOperation(value = "PC编辑盘点资产")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @RepeatSubmit
    public Boolean editAsset(@RequestBody @Validated InventoryAssetEditDto dto) {
        return inventoryAssetFeignClient.editAsset(dto);
    }

    /**
     * 盘盈资产详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "盘点-PC盘盈资产详情")
    @GetMapping(value = "/plDetail/{id}")
    public JSONObject getAssetPlDetail(@PathVariable Long id) {
        InventorySurplusDto assetDetail = inventoryAssetFeignClient.getSurplusInfo(id);
        JSONObject json = Optional.of(assetDetail.getAssetData()).orElse(new JSONObject());
        json.put("id", assetDetail.getId());
        json.put("inventoryId", assetDetail.getInventoryId());
        json.put("assetId", assetDetail.getAssetId());
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        assetUtil.translateAssetJson(json, formVO.getFormFields());
        return json;
    }

}
