package com.niimbot.asset.controller.pc.system;

import cn.hutool.core.collection.CollUtil;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AppActivateConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AbstractPermissionChangedService;
import com.niimbot.asset.service.feign.AppActivateFeignClient;
import com.niimbot.asset.service.feign.DataPermissionFeignClient;
import com.niimbot.asset.service.feign.SensitiveAuthorityFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.DataPermissionAuthorizeDto;
import com.niimbot.system.DataPermissionDto;
import com.niimbot.system.SensitiveDataPermissionDto;
import com.niimbot.system.SensitivePermissionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 数据权限
 *
 * <AUTHOR>
 * @date 2022/2/14 16:33
 */
@Slf4j
@Api(tags = {"数据权限"})
@ResultController
@RequestMapping("api/pc/permission")
@RequiredArgsConstructor
public class DataPermissionController {

    private final DataPermissionFeignClient dataPermissionFeignClient;
    private final SensitiveAuthorityFeignClient sensitiveAuthorityFeignClient;
    private final AppActivateFeignClient appActivateFeignClient;

    @Resource(name = "rolePermissionChangedService")
    private AbstractPermissionChangedService rolePermissionChangedService;

    @ApiOperation(value = "用户数据权限查询")
    @GetMapping("/user/{empId}")
//    @AutoConvert
    public SensitiveDataPermissionDto queryUserDataPermission(@PathVariable("empId") Long empId) {
        List<DataPermissionDto> dataPermissionDtoList = dataPermissionFeignClient.queryUserDataPermission(empId);
        //判断当前企业是否开通设备管理
        List<String> activeAppList = appActivateFeignClient.configStatus();
        if (!activeAppList.contains(AppActivateConstant.EQUIPMENT) && CollUtil.isNotEmpty(dataPermissionDtoList)) {
            dataPermissionDtoList = dataPermissionDtoList.stream().filter(item -> !item.getName().contains("设备任务")).collect(Collectors.toList());
        }
        List<SensitivePermissionDto> userSensitiveAuthority = sensitiveAuthorityFeignClient.userAuthority(empId);
        SensitiveDataPermissionDto result = new SensitiveDataPermissionDto();
        result.setDataPermission(dataPermissionDtoList);
        result.setSensitivePermission(userSensitiveAuthority);
        return result;
    }

    @ApiOperation(value = "数据权限信息查询")
    @GetMapping
    public SensitiveDataPermissionDto queryDataPermission() {
        SensitiveDataPermissionDto result = new SensitiveDataPermissionDto();
        List<DataPermissionDto> dataPermissionDtoList = dataPermissionFeignClient.queryDataPermission();
        result.setDataPermission(dataPermissionDtoList);
        return result;
    }

    /**
     * 数据授权
     *
     * @param authorizeDto 授权参数
     * @return 成功与否
     */
    @ApiOperation(value = "用户数据授权")
    @PostMapping("/user/authorize")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean authorize(@RequestBody @Validated DataPermissionAuthorizeDto authorizeDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        //过滤自身，防止自己可以操作自己
        int index = authorizeDto.getEmpIds().indexOf(LoginUserThreadLocal.getCurrentUserId());
        if (index > -1) {
            authorizeDto.getEmpIds().remove(index);
        }
        if (CollUtil.isEmpty(authorizeDto.getEmpIds())) {
            return Boolean.TRUE;
        }
        dataPermissionFeignClient.authorize(authorizeDto);
        List<CusUserDto> userDtos = new ArrayList<>();
        for (Long empId : authorizeDto.getEmpIds()) {
            userDtos.add(new CusUserDto()
                    .setId(empId)
                    .setCompanyId(companyId));
        }
        rolePermissionChangedService.permissionChange(userDtos);
        return true;
    }
}
