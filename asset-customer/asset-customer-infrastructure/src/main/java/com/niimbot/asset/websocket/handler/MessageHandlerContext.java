package com.niimbot.asset.websocket.handler;

import com.niimbot.asset.websocket.msg.HeartbeatMessage;
import com.niimbot.asset.websocket.msg.ImportImagesMessage;
import com.niimbot.asset.websocket.msg.Message;
import org.springframework.stereotype.Component;

import javax.websocket.EncodeException;
import javax.websocket.Session;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 消息处理上下文
 *
 * <AUTHOR>
 * @date 2021/9/2 14:49
 */
@Component
public class MessageHandlerContext implements MessageHandler {

    private final Map<Class<?>, MessageHandler> handlerMap = new ConcurrentHashMap<>();

    public MessageHandlerContext() {
        handlerMap.put(HeartbeatMessage.class, new HeartbeatMessageHandler());
        handlerMap.put(ImportImagesMessage.class, new ImportImagesMessageHandler());
    }

    @Override
    public void handle(Session session, Long userId, Long companyId, Message message) throws IOException, EncodeException {
        handlerMap.get(message.getClass()).handle(session, userId, companyId, message);
    }
}
