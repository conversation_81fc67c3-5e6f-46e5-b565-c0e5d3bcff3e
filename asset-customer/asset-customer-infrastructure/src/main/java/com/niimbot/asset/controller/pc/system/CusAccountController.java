package com.niimbot.asset.controller.pc.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.AbstractPermissionChangedService;
import com.niimbot.asset.service.feign.*;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.result.Result;
import com.niimbot.system.*;
import com.niimbot.validate.NationalCodeValidate;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * 账号管理控制器
 *
 * <AUTHOR>
 * @Date 2020/11/18
 */
@Api(tags = "账号管理")
@ResultController
@RequestMapping("api/pc/system/account")
public class CusAccountController {

    @Resource
    private CusUserFeignClient cusUserFeignClient;

    @Resource
    private CusEmployeeFeignClient employeeFeignClient;

    @Resource
    private CusAccountFeignClient accountFeignClient;

    @Resource
    private CusRoleFeignClient roleFeignClient;

    @Resource
    private FileUploadConfig fileUploadConfig;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource(name = "kickOffPermissionChangedService")
    private AbstractPermissionChangedService kickOffPermissionChangedService;

    @Resource(name = "rolePermissionChangedService")
    private AbstractPermissionChangedService rolePermissionChangedService;

    @Resource
    private AbstractTokenService tokenService;

    @Resource
    private RedisService redisService;

    @ApiOperation(value = "开通账号")
    @PostMapping
    @RepeatSubmit
    @ResultMessage("开通成功")
    public Map<String, String> save(@RequestBody @Validated(Insert.class) CusAccountDto account) {
        checkPassword(account);
        String newAccountNo = accountFeignClient.save(account);
        if (StringUtils.equals(account.getAccount(), newAccountNo)) {
            return ImmutableMap.of("account", newAccountNo);
        }
        return ImmutableMap.of("account", String.format("账号[%s]已存在，已给您生成最新账号[%s]",
                account.getAccount(), newAccountNo));
    }

    /**
     * 修改密码或角色
     *
     * @param account account
     * @return 修改是否成功
     */
    @ApiOperation(value = "编辑账号")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @Deprecated
    public Boolean edit(@RequestBody @Validated(Update.class) CusAccountDto account) {
        if (StringUtils.isNotEmpty(account.getPassword())) {
            checkPassword(account);
        }
        return accountFeignClient.edit(account);
    }

    @ApiOperation(value = "修改角色")
    @PutMapping("/role")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editRole(@RequestBody @Validated(CusAccountDto.ChangeRole.class) CusAccountDto account) {
        Boolean isRoleChanged = accountFeignClient.editRole(account);
        CusEmployeeDto info = employeeFeignClient.getInfo(account.getEmpId());
        if (isRoleChanged && Objects.nonNull(info)) {
            CusUserDto cusUserDto = new CusUserDto()
                    .setCompanyId(LoginUserThreadLocal.getCompanyId())
                    .setId(info.getId());
            rolePermissionChangedService.permissionChange(Collections.singletonList(cusUserDto));
        }
        return true;
    }

    @ApiOperation(value = "批量添加角色")
    @PutMapping("/addRoleBatch")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean addRoleBatch(LoginUserDto userDto, @RequestBody @Validated CusAccountRoleBatchDto account) {
        // 自动过滤自己
        int index = account.getEmpIds().indexOf(userDto.getCusUser().getId());
        if (index > -1) {
            account.getEmpIds().remove(index);
        }
        if (CollUtil.isEmpty(account.getEmpIds())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不可操作本人权限");
        }
        return accountFeignClient.addRoleBatch(account);
    }

    @ApiOperation(value = "批量删除角色")
    @PutMapping("/removeRoleBatch")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean removeRoleBatch(LoginUserDto userDto, @RequestBody @Validated CusAccountRoleBatchDto account) {
        // 自动过滤自己
        int index = account.getEmpIds().indexOf(userDto.getCusUser().getId());
        if (index > -1) {
            account.getEmpIds().remove(index);
        }
        if (CollUtil.isEmpty(account.getEmpIds())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不可操作本人权限");
        }
        return accountFeignClient.removeRoleBatch(account);
    }

    @ApiOperation(value = "修改密码")
    @PutMapping("/password")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editPassword(@RequestBody @Validated(CusAccountDto.ChangePassword.class) CusAccountDto account) {
        // 修改密码 剔除当前登录用户
        CusUserDto byId = cusUserFeignClient.getById(account.getAccountId());
        if (byId.getStatus() == AssetConstant.ACCOUNT_DISABLE && StrUtil.isBlank(byId.getPassword())) {
            throw new BusinessException(SystemResultCode.USER_SET_PASSWORD_CONFIRM_ERROR);
        }
        String password = SecurityUtils.decryptPassword(account.getPassword());
        String rePassword = SecurityUtils.decryptPassword(account.getRePassword());
        if (!StringUtils.equals(password, rePassword)) {
            throw new BusinessException(SystemResultCode.USER_PASSWORD_CONFIRM_ERROR);
        }
        account.setDecryptPassword(password);
        account.setPassword(SecurityUtils.encryptPassword(password));
        accountFeignClient.edit(account);
        kickOffPermissionChangedService.permissionChange(Collections.singletonList(byId));
        return true;
    }

    @Deprecated
    @ApiOperation(value = "重置密码")
    @PutMapping("/resetPassword/{accountId}")
    @ResultMessage(ResultConstant.RESET_PASSWORD_SUCCESS)
    public Boolean resetPassword(@PathVariable("accountId") Long accountId) {
        // 修改密码 剔除当前登录用户
        CusUserDto byId = cusUserFeignClient.getById(accountId);
        if (byId.getStatus() == AssetConstant.ACCOUNT_DISABLE && StrUtil.isBlank(byId.getPassword())) {
            throw new BusinessException(SystemResultCode.USER_SET_PASSWORD_CONFIRM_ERROR);
        }

        CusAccountDto account = new CusAccountDto();
        account.setAccountId(byId.getAccountId()).setDecryptPassword("jc123456");
        accountFeignClient.edit(account);
        kickOffPermissionChangedService.permissionChange(Collections.singletonList(byId));
        // 重置密码后、账号锁定状态
        redisService.del(BaseConstant.LOGIN_USER_ERROR_COUNT + byId.getMobile());
        redisService.del(BaseConstant.LOGIN_USER_ERROR_COUNT + byId.getAccount());
        return true;
    }

    @ApiOperation(value = "账号列表查询")
    @Deprecated
    @AutoConvert
    @GetMapping("/list")
    public List<CusAccountDto> list(CusAccountPageQueryDto queryDto) {
        return ListUtil.empty();
    }

    @ApiOperation(value = "账号关键字查询")
    @GetMapping("/list/{kw}")
    public List<CusAccountBaseDto> listKw(@PathVariable("kw") String kw) {
        return accountFeignClient.selectListKw(kw);
    }

    /**
     * 账号分页查询
     *
     * @param queryDto 分页、查询参数
     * @return pageUtils
     */
    @ApiOperation(value = "账号分页列表查询")
    @GetMapping(value = "/page")
    public PageUtils<CusAccountPageDto> page(CusAccountPageQueryDto queryDto) {
        return accountFeignClient.selectPage(queryDto);
    }

    /**
     * 禁用或启用账号
     *
     * @param account 1:启用 2:禁用
     * @return 成功与否
     */
    @ApiOperation(value = "禁用/启用账号")
    @PutMapping("/status")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean enableOrDisable(LoginUserDto userDto, @RequestBody @Validated CusAccountEnableDto account) {
        if (ObjectUtil.equal(userDto.getCusUser().getId(), account.getAccountId())) {
            throw new BusinessException(SystemResultCode.EMP_ACCOUNT_CANT_SET_SELF);
        }
        String url = fileUploadConfig.getDomain().replace("customer", "");
        accountFeignClient.enableOrDisable(account, url);
        // 账号被禁用
        if (AssetConstant.ACCOUNT_DISABLE == account.getAccountStatus()) {
            CusUserDto byId = cusUserFeignClient.getById(account.getAccountId());
            kickOffPermissionChangedService.permissionChange(Collections.singletonList(byId));
        }
        return true;
    }

    /**
     * 禁用或启用账号
     *
     * @param account 1:启用 2:禁用
     * @return 成功与否
     */
    @ApiOperation(value = "批量禁用/启用账号")
    @PutMapping("/status/batch")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean enableOrDisableBatch(LoginUserDto userDto, @RequestBody @Validated CusAccountEnableBatchDto account) {
        // 自动过滤自己
        int index = account.getAccountIds().indexOf(userDto.getCusUser().getId());
        if (index > -1) {
            account.getAccountIds().remove(index);
        }
        String url = fileUploadConfig.getDomain().replace("customer", "");
        accountFeignClient.enableOrDisableBatch(account, url);
        // 账号被禁用
        if (AssetConstant.ACCOUNT_DISABLE == account.getAccountStatus() && CollUtil.isNotEmpty(account.getAccountIds())) {
            List<CusUserDto> byIds = cusUserFeignClient.getByIds(account.getAccountIds());
            kickOffPermissionChangedService.permissionChange(byIds);
        }
        return true;
    }

    /**
     * 账号授权
     *
     * @param account 授权参数
     * @return 成功与否
     */
    @ApiOperation(value = "账号数据授权")
    @PostMapping("/authorize")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean authorize(@RequestBody @Validated(CusAccountDto.Authorization.class) CusAccountDto account) {
        accountFeignClient.authorize(account);
        CusEmployeeDto info = employeeFeignClient.getInfo(account.getEmpId());
        if (Objects.nonNull(info)) {
            CusUserDto cusUserDto = new CusUserDto().setCompanyId(LoginUserThreadLocal.getCompanyId())
                    .setId(info.getId());
            rolePermissionChangedService.permissionChange(Collections.singletonList(cusUserDto));
        }
        return true;
    }

    @ApiOperation("批量账号数据授权")
    @PostMapping("/batchAuthorize")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean batchAuthorize(@RequestBody @Validated(CusAccountDto.Authorization.class) List<@Valid CusAccountDto> accountDtos) {
        for (CusAccountDto dto : accountDtos) {
            this.authorize(dto);
        }
        return true;
    }

    /**
     * 获取推荐账号
     *
     * @return 账号
     */
    @ApiOperation(value = "获取推荐账号")
    @GetMapping("/getAccountNo")
    public String getAccountNo() {
        return accountFeignClient.getAccountNo();
    }

    @ApiOperation(value = "【新增员工时开通账号设置】修改配置", notes = "【新增员工时开通账号设置】修改配置")
    @PostMapping("/switchSetting")
    @ResultMessage("设置成功")
    public Boolean switchAccountCompanySetting(@RequestBody @Validated CompanySwitchDto dto) {
        return accountFeignClient.switchAccountCompanySetting(dto);
    }

    /**
     * 返回raw password
     *
     * @param account account
     * @return 原始密码
     */
    private String checkPassword(CusAccountDto account) {
        // 设置密码
        String password = SecurityUtils.decryptPassword(account.getPassword());
        account.setPassword(SecurityUtils.encryptPassword(password));
        account.setDecryptPassword(password);
        return password;
    }

    @ApiOperation("超管转让")
    @PutMapping("/supertubeTransfer")
    public void supertubeTransfer(HttpServletRequest request, HttpServletResponse response, @Validated @RequestBody SupertubeTransferDto dto) {
        //校验验证码
        Edition.saas(() -> {
            if (StringUtils.isEmpty(dto.getNationalCode())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "国际区号不能为空");
            }
            NationalCodeValidate.checkCNMobile(dto.getNationalCode(), dto.getMobile());
            if (!smsCodeFeignClient.checkSmsCode(dto.getMobile(), dto.getSmsCode())) {
                throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
            }
        });
        CusEmployeeDto adminInfo = employeeFeignClient.getAdminInfo();
        // 修改用户对应的角色
        Map<String, Long> map = MapUtil.builder(new HashMap<String, Long>()).put("oldAdmin", adminInfo.getId()).put("newAdmin", dto.getNewAdminId()).map();
        roleFeignClient.supertubeTransfer(map);
        // 踢出当前用户 与 转让成功的用户
        tokenService.delLoginUser(request);
        CusUserDto cusUserDto = cusUserFeignClient.getById(dto.getNewAdminId());
        kickOffPermissionChangedService.permissionChange(Collections.singletonList(cusUserDto));
        ServletUtils.renderString(response, JSON.toJSONString(Result.ofSuccess("转让超管成功")));
    }
}
