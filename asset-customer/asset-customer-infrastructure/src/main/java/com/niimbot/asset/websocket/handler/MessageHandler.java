package com.niimbot.asset.websocket.handler;

import com.niimbot.asset.websocket.msg.Message;

import javax.websocket.EncodeException;
import javax.websocket.Session;
import java.io.IOException;

/**
 * 消息处理接口
 *
 * <AUTHOR>
 * @date 2021/9/2 15:36
 */
public interface MessageHandler {
    /**
     * 消息处理
     * @param session
     * @param userId
     * @param message
     * @throws IOException
     * @throws EncodeException
     */
    void handle(Session session, Long userId, Long companyId, Message message) throws IOException, EncodeException;
}
