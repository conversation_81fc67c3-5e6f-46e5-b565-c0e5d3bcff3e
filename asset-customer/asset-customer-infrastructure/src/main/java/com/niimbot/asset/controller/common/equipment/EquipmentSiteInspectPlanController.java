package com.niimbot.asset.controller.common.equipment;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectPlanFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.equipment.CreateEntSntPlan;
import com.niimbot.equipment.EditEntSntPlan;
import com.niimbot.equipment.EditEntSntPlanExecutors;
import com.niimbot.equipment.EditEntSntPlanManagers;
import com.niimbot.equipment.EntSntPlan;
import com.niimbot.equipment.EntSntPlanDetails;
import com.niimbot.equipment.EntSntRange;
import com.niimbot.equipment.ExportEntSntPlan;
import com.niimbot.equipment.RemoveEntSntPlan;
import com.niimbot.equipment.SearchEntSntPlan;
import com.niimbot.equipment.SearchEntSntRange;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "设备巡检计划管理")
@ResultController
@RequestMapping("api/common/equipment/site/inspect/plan")
@RequiredArgsConstructor
public class EquipmentSiteInspectPlanController {

    private final DictConvertUtil dictConvertUtil;

    private final CacheResourceUtil cacheResource;

    private final EquipmentSiteInspectPlanFeignClient planFeignClient;

    @ApiOperation("创建设备巡检计划")
    @PostMapping("/create")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean createPlan(@RequestBody @Validated(Insert.class) CreateEntSntPlan create) {
        create.validPlanData();
        AuditableOperateResult result = planFeignClient.createPlan(create);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.ADD_ENT_SNT_PLAN, result));
        return result.successful();
    }

    @ApiOperation("编辑设备巡检计划")
    @PostMapping("/edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editPlan(@RequestBody @Validated(Update.class) EditEntSntPlan edit) {
        AuditableOperateResult result = planFeignClient.editPlan(edit);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.UPT_ENT_SNT_PLAN, result));
        return result.successful();
    }

    @ApiOperation("删除设备巡检计划")
    @PostMapping("/remove")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean removePlan(@RequestBody @Validated RemoveEntSntPlan remove) {
        List<AuditableOperateResult> results = planFeignClient.removePlan(remove);
        AuditLogs.sendRecord(() -> {
            Map<String, Object> params = Auditable.Resolver.resolveParams(results);
            if (remove.getCancelTask()) {
                params.put(Auditable.Tpl.CONTENT, params.getOrDefault(Auditable.Tpl.CONTENT, "") + "，并作废全部任务");
            }
            return AuditLogRecord.create(Auditable.Action.DEL_ENT_SNT_PLAN, params);
        });
        return CollUtil.isNotEmpty(results);
    }

    @ApiOperation("停止设备巡检计划")
    @PostMapping("/stop")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean stopPlan(@RequestBody @Validated @NotEmpty(message = "计划ID集合不能为空") List<Long> planIds) {
        List<AuditableOperateResult> results = planFeignClient.stopPlan(planIds);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.STOP_ENT_SNT_PLAN, results));
        return CollUtil.isNotEmpty(results);
    }

    @ApiOperation("变更巡检管理员")
    @PostMapping("/edit/managers")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean editPlanManagers(@RequestBody EditEntSntPlanManagers edit) {
        AuditableOperateResult result = planFeignClient.editPlanManagers(edit);
        AuditLogs.sendRecord(() -> {
            Map<String, Object> params = Auditable.Resolver.resolveParams(result);
            params.put(Auditable.Tpl.BEFORE, String.join("、", edit.getOldManagersText()));
            params.put(Auditable.Tpl.AFTER, edit.getManagerIds().stream().map(cacheResource::getUserNameAndCode).collect(Collectors.joining("、")));
            return AuditLogRecord.create(Auditable.Action.UPT_ENT_SNT_PLAN_MANAGERS, params);
        });
        return result.successful();
    }

    @ApiOperation("变更巡检人")
    @PostMapping("/edit/executors")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean editPlanExecutors(@RequestBody EditEntSntPlanExecutors edit) {
        AuditableOperateResult result = planFeignClient.editPlanExecutors(edit);
        AuditLogs.sendRecord(() -> {
            Map<String, Object> params = Auditable.Resolver.resolveParams(result);
            params.put(Auditable.Tpl.BEFORE, String.join("、", edit.getOldExecutorsText()));
            params.put(Auditable.Tpl.AFTER, edit.getExecutorIds().stream().map(cacheResource::getUserNameAndCode).collect(Collectors.joining("、")));
            return AuditLogRecord.create(Auditable.Action.UPT_ENT_SNT_PLAN_EXECUTORS, params);
        });
        return result.successful();
    }

    @ApiOperation("计划列表分页搜索")
    @PostMapping("/search")
    public PageUtils<JSONObject> searchPlan(@RequestBody SearchEntSntPlan search) {
        PageUtils<EntSntPlan> result = planFeignClient.searchPlan(search);
        dictConvertUtil.convertToDictionary(result.getList());
        List<JSONObject> json = result.getList().stream().map(EntSntPlan::translate).collect(Collectors.toList());
        return new PageUtils<>(json, result.getTotalCount(), result.getPageSize(), result.getCurrPage());
    }

    @ApiOperation("计划详情")
    @GetMapping("/detail")
    public JSONObject detailPlan(@RequestParam("planId") Long planId) {
        EntSntPlanDetails detailPlan = planFeignClient.detailPlan(planId);
        dictConvertUtil.convertToDictionary(detailPlan);
        return detailPlan.translate();
    }

    @ApiOperation("设备巡检范围分页")
    @PostMapping("/range/search")
    public PageUtils<EntSntRange> rangeSearch(@RequestBody SearchEntSntRange search) {
        return planFeignClient.rangeSearch(search);
    }

    @ApiOperation("导出")
    @PostMapping("/export")
    public void exportPlan(@RequestBody SearchEntSntPlan search, HttpServletResponse response) {
        List<JSONObject> json = searchPlan(search).getList();
        Affirm.notEmpty(json, "列表为空");
        List<ExportEntSntPlan> list = json.stream().map(v -> {
            ExportEntSntPlan exportEntSntPlan = v.toJavaObject(ExportEntSntPlan.class);
            exportEntSntPlan.convert(v);
            return exportEntSntPlan;
        }).collect(Collectors.toList());
        try {
            LinkedHashMap<String, String> excelHead = ExcelUtils.buildExcelHead(ExportEntSntPlan.class);
            // excel 为空就生成一个空文件
            String fileName = "设备巡检计划-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (search.getPageNum() > 1) {
                fileName += "-" + search.getPageNum();
            }
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_ENT_SNT_PLAN, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(list.size()))));
            ExcelUtils.export(response, new ExcelExportDto(excelHead, list), fileName + ".xlsx");
        } catch (Exception e) {
            log.warn("设备巡检计划导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }
}
