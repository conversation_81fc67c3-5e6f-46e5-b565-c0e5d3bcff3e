package com.niimbot.asset.controller.pc.report;

import com.niimbot.asset.service.feign.report.DynamicFieldFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.report.DynamicFieldQueryDto;
import com.niimbot.system.QueryConditionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/27 上午10:05
 */
@Slf4j
@Api(tags = "动态字段")
@ResultController
@RequestMapping("api/pc/report/dynamic/field/")
@RequiredArgsConstructor
public class DynamicFieldController {

    private final DynamicFieldFeignClient dynamicFieldFeignClient;

    @ApiOperation(value = "动态表单字段查询")
    @GetMapping("query")
    public List<QueryConditionDto> query(@Validated DynamicFieldQueryDto queryDto) {
        return dynamicFieldFeignClient.query(queryDto);
    }

    @ApiOperation(value = "耗材档案字段")
    @GetMapping("material")
    public List<QueryConditionDto> material() {
        return dynamicFieldFeignClient.material();
    }
}
