package com.niimbot.asset.websocket.msg;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/9/1 18:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class ExportTaskMessage extends Message {
    private static final long serialVersionUID = 6330555425872236660L;

    private Long taskId;

    private Integer taskStatus;

    public ExportTaskMessage() {
        super(Message.TYPE_EXPORT_TASK);
    }

    public ExportTaskMessage(Long taskId, Integer taskStatus) {
        this();
        this.taskId = taskId;
        this.taskStatus = taskStatus;
    }
}
