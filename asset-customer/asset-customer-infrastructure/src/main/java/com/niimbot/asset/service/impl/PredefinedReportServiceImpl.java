package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.PredefinedReportService;
import com.niimbot.asset.service.feign.AsAssetLogFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.report.PredefinedReportFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AsAssetLogDto;
import com.niimbot.means.AsAssetLogQueryDto;
import com.niimbot.report.AssetLogReportDto;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.HandleAssetLogDto;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.RepairAssetLogDto;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.WaitHandleAssetLogDto;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/7/8 下午3:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PredefinedReportServiceImpl implements PredefinedReportService {

    private final PredefinedReportFeignClient predefinedReportFeignClient;
    private final AsAssetLogFeignClient assetLogFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final FormFeignClient formFeignClient;
    private final AsAssetUtil assetUtil;

    @Override
    public PageUtils<JSONObject> assetLogReport(AssetLogReportQueryDto queryDto) {
        PageUtils<AssetLogReportDto> assetLogReportPage = predefinedReportFeignClient.assetLogReport(queryDto);
        List<AssetLogReportDto> assetList = assetLogReportPage.getList();
        if (CollUtil.isEmpty(assetList)) {
            return new PageUtils<>();
        }

        // 获取资产ids
        List<Long> assetIds = assetList.stream().map(AssetLogReportDto::getId).collect(toList());

        // 获取履历数据
        AsAssetLogQueryDto asAssetLogQueryDto = new AsAssetLogQueryDto();
        BeanUtil.copyProperties(queryDto, asAssetLogQueryDto);
        asAssetLogQueryDto.setAssetIds(assetIds);
        List<AsAssetLogDto> assetLogDtos = assetLogFeignClient.list(asAssetLogQueryDto);
        dictConvertUtil.convertToDictionary(assetLogDtos);

        Map<Long, List<AsAssetLogDto>> assetLogMap = new HashMap<>();
        for (AsAssetLogDto k : assetLogDtos) {
            if (assetLogMap.containsKey(k.getAssetId())) {
                assetLogMap.get(k.getAssetId()).add(k);
            } else {
                List<AsAssetLogDto> list = new ArrayList<>();
                list.add(k);
                assetLogMap.put(k.getAssetId(), list);
            }
        }

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<JSONObject> assetDataList = assetList.stream().map(AssetLogReportDto::translate).collect(toList());
        assetUtil.translateAssetJsonBatch(assetDataList, formVO.getFormFields());

        assetDataList.forEach(assetDto -> {
            List<AsAssetLogDto> asAssetLogDtos = assetLogMap.get(assetDto.getLong("id"));
            List<JSONObject> asAssetLogJson = asAssetLogDtos.stream().map(JsonUtil::toJsonObject).collect(toList());
            assetDto.put("logs", asAssetLogJson);
        });

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(assetLogReportPage, result);
        result.setList(assetDataList);
        return result;
    }

    @Override
    public PageUtils<JSONObject> waitHandleAssetLog(WaitHandleAssetLogQueryDto queryDto, FormVO formVO) {
        PageUtils<WaitHandleAssetLogDto> waitHandleAssetLogPage = predefinedReportFeignClient.waitHandleAssetLog(queryDto);
        List<JSONObject> assetList = waitHandleAssetLogPage.getList().stream().map(WaitHandleAssetLogDto::translate).collect(toList());;
        assetUtil.translateAssetJsonBatch(assetList, formVO.getFormFields());

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(waitHandleAssetLogPage, result);
        result.setList(assetList);
        return result;
    }

    @Override
    public PageUtils<JSONObject> handleAssetLog(HandleAssetLogQueryDto queryDto, FormVO formVO) {
        PageUtils<HandleAssetLogDto> handleAssetLogPage = predefinedReportFeignClient.handleAssetLog(queryDto);
        List<JSONObject> assetList = handleAssetLogPage.getList().stream().map(HandleAssetLogDto::translate).collect(toList());;
        assetUtil.translateAssetJsonBatch(assetList, formVO.getFormFields());

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(handleAssetLogPage, result);
        result.setList(assetList);
        return result;
    }

    @Override
    public PageUtils<JSONObject> waitReturnAssetLog(WaitReturnAssetLogQueryDto queryDto, FormVO formVO) {
        PageUtils<WaitReturnAssetLogDto> waitReturnAssetLogPage = predefinedReportFeignClient.waitReturnAssetLog(queryDto);
        List<JSONObject> assetList = waitReturnAssetLogPage.getList().stream().map(WaitReturnAssetLogDto::translate).collect(toList());;
        assetUtil.translateAssetJsonBatch(assetList, formVO.getFormFields());

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(waitReturnAssetLogPage, result);
        result.setList(assetList);
        return result;
    }

    @Override
    public PageUtils<JSONObject> repairAssetLog(RepairAssetLogQueryDto queryDto, FormVO formVO) {
        PageUtils<RepairAssetLogDto> repairAssetLogPage = predefinedReportFeignClient.repairAssetLog(queryDto);
        List<JSONObject> assetList = repairAssetLogPage.getList().stream().map(RepairAssetLogDto::translate).collect(toList());
        assetUtil.translateAssetJsonBatch(assetList, formVO.getFormFields());

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(repairAssetLogPage, result);
        result.setList(assetList);
        return result;
    }

    @Override
    public PageUtils<JSONObject> repairAssetRecord(RepairAssetRecordQueryDto queryDto, FormVO formVO) {
        PageUtils<RepairAssetRecordDto> repairAssetRecordPage = predefinedReportFeignClient.repairAssetRecord(queryDto);
        List<JSONObject> assetList = repairAssetRecordPage.getList().stream().map(RepairAssetRecordDto::translate).collect(toList());
        assetUtil.translateAssetJsonBatch(assetList, formVO.getFormFields());

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(repairAssetRecordPage, result);
        result.setList(assetList);
        return result;
    }
}
