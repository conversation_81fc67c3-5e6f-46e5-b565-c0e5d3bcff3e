package com.niimbot.asset.controller.pc.purchase;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.PurchaseResultCode;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.PurchaseOrderTypeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.CusOrderTypeEnableWorkflowDto;
import com.niimbot.purchase.PurchaseOrderTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:26
 */
@Api(tags = "采购单据配置管理接口")
@RequestMapping("api/pc/purchase/orderType")
@ResultController
@Validated
@RequiredArgsConstructor
public class PurchaseOrderTypeController {
    private final PurchaseOrderTypeFeignClient purchaseOrderTypeFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;
//    private final ActWorkflowFieldFeignClient workflowFieldFeignClient;

    @ApiOperation(value = "单据类型列表")
    @GetMapping(value = "/list")
    public List<PurchaseOrderTypeDto> listOrderType() {
        return purchaseOrderTypeFeignClient.listOrderType();
    }

    @ApiOperation(value = "单据类型是否开启审批流")
    @GetMapping(value = "/enableWorkflow/{orderType}")
    public Boolean enableWorkflow(@PathVariable("orderType") Integer orderType) {
        return purchaseOrderTypeFeignClient.enableWorkflow(orderType);
    }

    @ApiOperation(value = "单据类型下拉字典")
    @GetMapping(value = "/dict")
    public List<Map<String, ?>> orderTypeDict() {
        List<PurchaseOrderTypeDto> orderType = purchaseOrderTypeFeignClient.listOrderType();
        return orderType.stream().map(item ->
                ImmutableMap.of("value", item.getType(), "label", StrUtil.replace(item.getName(), "单", ""))
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "启用单据流程")
    @PutMapping("/enableOrderTypeWorkflow")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean enableOrderTypeWorkflow(@RequestBody CusOrderTypeEnableWorkflowDto dto) {
        PurchaseOrderTypeDto type = purchaseOrderTypeFeignClient.getOrderTypeById(dto.getId());
        if (type == null) {
            throw new BusinessException(PurchaseResultCode.ORDER_TYPE_NOT_EXISTS, String.valueOf(dto.getId()));
        }
        if (dto.getEnableWorkflow() && !workflowFeignClient.hasWorkflow(type.getActivitiKey())) {
            return false;
        }
        PurchaseOrderTypeDto orderType = BeanUtil.copyProperties(dto, PurchaseOrderTypeDto.class);
        return purchaseOrderTypeFeignClient.updateOrderType(orderType);
    }
}
