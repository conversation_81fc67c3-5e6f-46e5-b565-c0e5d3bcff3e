package com.niimbot.asset.controller.app.system;

import com.niimbot.asset.annotation.LoginRecord;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.model.LoginAndBandingBody;
import com.niimbot.asset.model.LoginBody;
import com.niimbot.asset.model.MobileBody;
import com.niimbot.asset.model.ScanQrBody;
import com.niimbot.asset.security.enmu.QrTypeEnum;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.LoginByMobileResult;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * APP登录模块
 *
 * <AUTHOR>
 * @since 2020/11/13 18:37
 */
@Slf4j
@Api(tags = {"手机登录接口"})
@ResultController
@RequestMapping("api/app")
public class AppLoginController {

    @Autowired
    private CusLoginService loginService;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private RedisService redisService;

    /**
     * 账号密码验证码登录
     *
     * @param loginBody 登录信息
     * @return token
     */
    @LoginRecord
    @PostMapping("/login")
    @ApiOperation(value = "账号密码登录", notes = "使用账号密码登录")
    public Map<String, Object> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> map = new HashMap<>();
        String token = loginService.loginByPwd(loginBody.getAccount(),
                loginBody.getPassword(),
                loginBody.getCode(),
                loginBody.getUuid(),
                AssetConstant.TERMINAL_APP,
                loginBody.getPushId());
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        return map;

    }

    /**
     * 手机短信登录
     *
     * @param mobileBody 登录信息
     * @return token
     */
    @LoginRecord
    @PostMapping("/login/sms")
    @ApiOperation(value = "短信验证码登录", notes = "使用短信验证码登录")
    public LoginByMobileResult loginByPhone(@Validated @RequestBody MobileBody mobileBody) {
        NationalCodeValidate.checkCNMobile(mobileBody.getNationalCode(),mobileBody.getMobile());
        //校验区号和手机号一致
        String registerKey = "register_key:" +mobileBody.getMobile();
        if (redisService.hasKey(registerKey)){
            if (!redisService.get(registerKey).equals(mobileBody.getNationalCode())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "国际区号与手机号不匹配，请检查国际区号！");
            }
        }
        if (!smsCodeFeignClient.checkSmsCode(mobileBody.getMobile(), mobileBody.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return loginService.loginByMobile(mobileBody.getMobile(), mobileBody.getSmsCode(), AssetConstant.TERMINAL_APP,
                mobileBody.getPushId());
    }

    /**
     * 扫码确认登录
     *
     * @param scanQrBody 扫码登录信息
     * @return token
     */
    @LoginRecord
    @PostMapping("/login/qrCode")
    @ApiOperation(value = "扫码确认登录", notes = "扫码确认登录")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Map<String, Object> loginByConfirm(@RequestBody ScanQrBody scanQrBody) {
        return loginService.loginByConfirm(scanQrBody.getUuid().replace(QrTypeEnum.LOGIN.getType() + ":", ""), scanQrBody.isConfirm());
    }

    /**
     * 扫描二维码
     *
     * @param scanQrBody 扫码登录信息
     * @return token
     */
    @PostMapping("/login/qrCode/scan")
    @ApiOperation(value = "扫描二维码", notes = "扫描二维码")
    public Map<String, Object> scanQrCode(@RequestBody ScanQrBody scanQrBody) {
        return loginService.scanQrCode(scanQrBody.getUuid().replace(QrTypeEnum.LOGIN.getType() + ":", ""));
    }

    /**
     * 社交账号绑定
     *
     * @param provider 社交平台: QQ ,WEIXIN
     * @param appId    社交平台AppId
     * @param code     社交平台授权码
     * @return
     */
    @PostMapping("/login/socialBind")
    @ApiOperation(value = "社交账号绑定", notes = "社交账号绑定")
    public Boolean socialBind(LoginUserDto userDto,
                              @RequestParam("provider") String provider,
                              @RequestParam("appId") String appId,
                              @RequestParam("code") String code) {
        if (log.isDebugEnabled()) {
            log.info("---------------------------------------------------------------");
            log.info("provider = {}, appId = {}, code = {}", provider, appId, code);
            log.info("---------------------------------------------------------------");
        }
        loginService.socialBind(userDto.getCusUser().getUnionId(), provider, appId, code);
        return true;
    }

    /**
     * 社交账号解绑
     *
     * @param provider 社交平台
     * @return token
     */
    @PostMapping("/login/socialUnbind")
    @ApiOperation(value = "社交账号解绑", notes = "社交账号解绑")
    public Boolean socialUnbind(LoginUserDto userDto, @RequestParam("provider") String provider) {
        loginService.socialUnbind(userDto.getCusUser().getUnionId(), provider);
        return true;
    }

//    /**
//     * 社交登录
//     *
//     * @param provider 社交平台: QQ ,WEIXIN
//     * @param appId    社交平台AppId
//     * @param code     社交平台授权码
//     * @param pushId   极光推送id
//     * @return token
//     */
//    @LoginRecord
//    @PostMapping("/login/social")
//    @ApiOperation(value = "社交登录", notes = "社交登录")
//    public Map<String, Object> loginBySocial(
//            @ApiParam(name = "provider", value = "社交平台: QQ ,WEIXIN", required = true)
//            @RequestParam("provider") String provider,
//            @ApiParam(name = "appId", value = "社交平台AppId", required = true)
//            @RequestParam("appId") String appId,
//            @ApiParam(name = "code", value = "社交平台授权码", required = true)
//            @RequestParam("code") String code,
//            @ApiParam(name = "pushId", value = "极光推送唯一id")
//            @RequestParam(value = "pushId", required = false) String pushId) {
//        Map<String, Object> map = new HashMap<>();
//        try {
//            String token = loginService.loginBySocial(provider, appId, code,
//                    AssetConstant.TERMINAL_APP, pushId);
//            map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
//        } catch (BusinessException e) {
//            // 移动端需要正常包含 code = 异常码的数据拉起用户密码框
//            if (SsoExceptionUtils.NOT_REGISTER_CODE == e.getCode()) {
//                map.put("code", SsoExceptionUtils.NOT_REGISTER_CODE);
//            } else {
//                throw e;
//            }
//        }
//        return map;
//    }

    /**
     * 登录并绑定社交账号
     *
     * @param bandingBody 请求参数
     * @return
     */
    @LoginRecord
    @PostMapping("/login/banding")
    @ApiOperation(value = "登录并绑定社交账号", notes = "登录并绑定社交账号")
    public Map<String, Object> loginAndBanding(@Validated @RequestBody LoginAndBandingBody bandingBody) {
        Map<String, Object> map = new HashMap<>();
        bandingBody.setTerminal(AssetConstant.TERMINAL_APP);
        String token = loginService.loginAndBanding(bandingBody);
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        return map;
    }

    /**
     * 账号密码登录
     *
     * @param username 用户名
     * @param password 密码
     * @return
     */
    @LoginRecord
    @PostMapping("/login/password")
    @ApiOperation(value = "账号密码登录", notes = "使用账号密码登录")
    public Map<String, Object> loginByPassword(@RequestParam("username") String username,
                                               @RequestParam("password") String password,
                                               @RequestParam(value = "pushId", required = false) String pushId) {
        Map<String, Object> map = new HashMap<>();
        String token = loginService.loginByPwd(username,
                password,
                null,
                null,
                AssetConstant.TERMINAL_APP,
                pushId);
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        return map;
    }

}
