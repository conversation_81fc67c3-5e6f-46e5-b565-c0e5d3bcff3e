package com.niimbot.asset.service.impl;

import com.niimbot.asset.service.CompanyResourceService;
import com.niimbot.asset.service.feign.CompanyResourceFeignClient;
import com.niimbot.sale.CompanyResourceCapacityDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/12/18 15:16
 */
@Service
public class CompanyResourceServiceImpl implements CompanyResourceService {

    private final CompanyResourceFeignClient resourceFeignClient;

    @Autowired
    public CompanyResourceServiceImpl(CompanyResourceFeignClient resourceFeignClient) {
        this.resourceFeignClient = resourceFeignClient;
    }

    @Override
    public void checkCapacity(int num) {
        CompanyResourceCapacityDto capacity = resourceFeignClient.getCapacity();
        if (capacity.getHasUsed() + num > capacity.getCapacity()) {
//            throw new BusinessException();
        }
    }

}
