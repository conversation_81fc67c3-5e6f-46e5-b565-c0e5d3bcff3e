package com.niimbot.asset.service.feign;

import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialCategoryDto;
import com.niimbot.material.MaterialCategoryImportDto;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialCategoryFeignClient {

    /**
     * 新增分类
     *
     * @param params 耗材管理接受类
     * @return true or false
     * <AUTHOR>
     */
    @PostMapping("/server/material/category")
    Long add(@RequestBody MaterialCategoryDto params);

    /**
     * 根据ID删除分类
     *
     * @param id 分类ID
     * @return true or false
     */
    @DeleteMapping(value = "/server/material/category/{id}")
    List<AuditableOperateResult> deleteById(@PathVariable(value = "id") Long id);

    /**
     * 删除多个记录
     *
     * @param ids ids
     * @return true or false
     * <AUTHOR>
     */
    @DeleteMapping(value = "/server/material/category")
    List<AuditableOperateResult> deleteList(@RequestBody List<Long> ids);

    /**
     * 更新分类
     *
     * @param params 耗材分类DTO
     * @return true or false
     * <AUTHOR>
     */
    @PutMapping("/server/material/category")
    Boolean updateOne(@RequestBody MaterialCategoryDto params);

    /**
     * 分类详情
     *
     * @param id ID
     * @return 耗材分类
     * <AUTHOR>
     */
    @GetMapping(value = "/server/material/category/{id}")
    MaterialCategoryDto detail(@PathVariable(value = "id") Long id);

    /**
     * 获取全部数据
     *
     * @return {@link List<MaterialCategoryDto>}
     * <AUTHOR>
     */
    @GetMapping(value = "/server/material/category/all")
    List<MaterialCategoryDto> all(@RequestParam(value = "kw", required = false) String kw);

    /**
     * 排序
     *
     * @param ids ids
     * @return true or false
     * <AUTHOR>
     */
    @PutMapping(value = "/server/material/category/sort")
    Boolean sort(@RequestBody List<Long> ids);

    /**
     * 获取最大的 categoryCode
     *
     * @return max category code
     * <AUTHOR>
     */
    @GetMapping(value = "/server/material/category/recommendCode")
    String recommendCode();

    @GetMapping(value = "server/material/category/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @DeleteMapping(value = "server/material/category/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/material/category/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/material/category/saveSheetData")
    Boolean saveSheetData(MaterialCategoryImportDto importDto);

    @PostMapping(value = "server/material/category/listByIds")
    List<MaterialCategoryDto> listByIds(List<Long> cateIds);
}
