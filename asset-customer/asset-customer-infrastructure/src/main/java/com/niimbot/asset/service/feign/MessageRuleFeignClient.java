package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.MessageRuleDto;
import com.niimbot.system.MessageRuleListDto;
import com.niimbot.system.MessageRulePageDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MessageRuleFeignClient {

    @GetMapping("/server/message/rule/available/list")
    PageUtils<MessageRuleListDto> page(@SpringQueryMap MessageRulePageDto dto);

    @GetMapping("/server/message/rule/{id}")
    MessageRuleDto detail(@PathVariable("id") Long id);

    @PutMapping("/server/message/rule")
    Boolean edit(@RequestBody MessageRuleDto dto);

    /**
     * PC端租户 禁用自己的code消息
     */
    @PutMapping("/server/message/rule/disable/{id}")
    Boolean disable(@PathVariable("id") Long id);

    /**
     * PC端租户 启用自己的code消息
     */
    @PutMapping("/server/message/rule/enable/{id}")
    Boolean enable(@PathVariable("id") Long id);

    @GetMapping("/server/message/template/available/{code}")
    List<Map<String, Object>> availableChannelByCode(@PathVariable("code") String code);

    @GetMapping("/server/message/rule/check/{companyId}/{code}")
    MessageRuleDto checkCompanyMessageRuleIsAvailable(@PathVariable("companyId") Long companyId, @PathVariable("code") String code);

    @GetMapping("/server/message/rule/{companyId}/{code}")
    MessageRuleDto currentCompanyMessageConfig(@PathVariable("companyId") Long companyId, @PathVariable("code") String code);
}
