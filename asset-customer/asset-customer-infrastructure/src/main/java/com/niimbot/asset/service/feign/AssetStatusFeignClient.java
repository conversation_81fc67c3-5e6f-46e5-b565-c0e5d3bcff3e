package com.niimbot.asset.service.feign;

import com.niimbot.means.AssetStatusDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/12/29 12:28
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetStatusFeignClient {

    /**
     * 查询全部资产状态
     *
     * @return 状态集合
     */
    @GetMapping(value = "server/means/assetStatus/list")
    List<AssetStatusDto> allStatus();

    /**
     * 通过单据类型查询资产状态
     *
     * @param orderType 单据类型
     * @return 资产状态
     */
    @GetMapping(value = "server/means/assetStatus/getAssetStatusByOrderType")
    List<Integer> getAssetStatusByOrderType(@RequestParam("orderType") Integer orderType);

}
