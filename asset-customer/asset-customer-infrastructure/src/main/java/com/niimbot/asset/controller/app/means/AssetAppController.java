package com.niimbot.asset.controller.app.means;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.AssetQueryFieldService;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.asset.service.feign.MaintainPlanFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.material.MaterialDto;
import com.niimbot.means.*;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.CusMenuDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryKwSearchDto;
import com.niimbot.system.enums.SearchTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since on 2020/12/9 17:39
 */
@Slf4j
@Api(tags = "资产管理")
@ResultController
@RequestMapping("api/app/asset")
public class AssetAppController {

    //设备管理需要去除的操作
    private static final List<String> excludeOperation = Stream.of("borrow_add", "recv_add", "return_add", "revert_add").collect(Collectors.toList());

    private final AssetService assetService;

    private final AssetFeignClient assetFeignClient;

    private final CusMenuFeignClient menuFeignClient;

    private final MaterialFeignClient materialFeignClient;

    private final AbstractFileUtils fileUtils;

    private final MaintainPlanFeignClient maintainPlanFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private AssetQueryFieldService queryFieldService;

    @Resource
    private DictConvertUtil dictConvertUtil;

    @Autowired
    public AssetAppController(AssetService assetService,
                              AssetFeignClient assetFeignClient,
                              CusMenuFeignClient menuFeignClient,
                              MaterialFeignClient materialFeignClient,
                              AbstractFileUtils fileUtils, MaintainPlanFeignClient maintainPlanFeignClient) {
        this.assetService = assetService;
        this.assetFeignClient = assetFeignClient;
        this.menuFeignClient = menuFeignClient;
        this.materialFeignClient = materialFeignClient;
        this.fileUtils = fileUtils;
        this.maintainPlanFeignClient = maintainPlanFeignClient;
    }

    /**
     * 参数： 资产编码、资产名称、使用人、状态
     *
     * @param queryDto 查询参数
     * @return 分页数据
     */
    @ApiOperation(value = "【APP】数据查询分页列表【仅包含固定属性】")
    @PostMapping(value = "/page")
    public PageUtils<AssetAppPageDto> page(@RequestBody AssetQueryConditionDto queryDto) {
        //设置kw搜索字段类型
        setKwFieldType(queryDto);
        return assetService.assetAppPage(queryDto);
    }

    /**
     * 设置kw搜索字段的类型
     *
     * @param queryDto
     */
    private void setKwFieldType(AssetQueryConditionDto queryDto) {
        if (CollUtil.isEmpty(queryDto.getKwFiled())) {
            return;
        }

        Map<String, QueryConditionDto> queryFieldMap = new HashMap<>();
        List<QueryConditionDto> allQueryField = queryFieldService.assetSearchAllQueryField();
        if (!CollUtil.isEmpty(allQueryField)) {
            queryFieldMap = allQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, value -> value, (v1, v2) -> v2));
        }

        for (QueryKwSearchDto kwField : queryDto.getKwFiled()) {
            //设置字段类型
            if (Objects.nonNull(queryFieldMap.get(kwField.getCode()))) {
                kwField.setType(SearchTypeEnum.getByCode(queryFieldMap.get(kwField.getCode()).getType()));
            }
        }
    }

    @ApiOperation(value = "【APP】通过资产ID查询资产操作")
    @GetMapping(value = "/getAssetOptByAssetId")
    public List<AssetOperationDto> getAssetOptByAssetId(@RequestParam @NotEmpty List<Long> ids,
                                                        @RequestParam(value = "type", required = false, defaultValue = "1") Integer type) {
        List<AssetOperationDto> optByAssetId = assetFeignClient.getAssetOptByAssetId(ids);
        //设备需要去除掉领用、借用等操作
        if (Objects.nonNull(type) && type == 2) {
            optByAssetId = optByAssetId.stream().filter(item -> !excludeOperation.contains(item.getCode())).collect(Collectors.toList());
        }
        AppCusMenuDto appCusMenuDto = menuFeignClient.userMenuAppList();
        List<String> buttonList = appCusMenuDto.getMenus().stream()
                .filter(m -> m.getMenuType().equalsIgnoreCase(DictConstant.MENU_TYPE_BUTTON))
                .map(CusMenuDto::getMenuCode)
                .collect(Collectors.toList());
        // 有无保养计划
        List<MaintainPlanInfoDto> maintainPlans = maintainPlanFeignClient.getByAssetIds(ids);
        int assetSize = ids.size();
        optByAssetId = optByAssetId.stream()
                .peek(item -> {
                    if (CollUtil.isNotEmpty(maintainPlans) && Convert.toInt(item.getOrderType()).equals(AssetConstant.OPT_DELETE)) {
                        item.setIsDisable(true);
                    }
                    // 添加菜单权限控制
                    if (!BooleanUtil.isTrue(item.getIsDisable())) {
                        item.setIsDisable(!buttonList.contains(item.getCode()));
                    }

                    // 资产复制菜单权限
                    if (AssetConstant.OPT_COPY.equals(Convert.toInt(item.getOrderType()))) {
                        if (1 == assetSize && buttonList.contains(item.getCode())) {
                            item.setIsDisable(false);
                        } else {
                            item.setIsDisable(true);
                        }
                    }

                    item.setIcon(fileUtils.convertToDownloadUrl(item.getIcon()));
                    item.setName("资产" + item.getName());
                }).filter(item -> !BooleanUtil.isTrue(item.getIsDisable())).collect(Collectors.toList());
        return optByAssetId;
    }

    @ApiOperation(value = "【APP】获取当前总金额")
    @PostMapping(value = "/amountMoney")
    public JSONObject amountMoney(@RequestBody AssetQueryConditionDto queryDto) {
        //设置搜索参数
        setKwFieldType(queryDto);
        String amountMoney = assetFeignClient.amountMoney(queryDto);
        JSONObject result = new JSONObject();
        //处理金额为4位小数
        result.put("amount", new BigDecimal(amountMoney).setScale(4, RoundingMode.HALF_UP));
        //数据脱敏处理
        desensitizationDataUtil.desensitizationAssetAmountField(Collections.singletonList(result));
        return result;
    }

    @ApiOperation(value = "根据epcid获取资产详情")
    @GetMapping(value = "/info")
    public JSONObject getInfo(@ApiParam(name = "epcId", value = "资产epcId")
                              @RequestParam("epcId") String epcId) {
        if (StrUtil.isBlank(epcId)) {
            throw new BusinessException(MeansResultCode.ASSET_ATTR_NOT_NULL, "epcId");
        }
        epcId = epcId.substring(0, epcId.length() - 1);
        return assetService.getInfo(epcId);
    }

    @ApiOperation(value = "根据资产id字符串获取资产详情")
    @GetMapping(value = "/infoById")
    public JSONObject getInfoById(
            @ApiParam(name = "assetId", value = "资产Id")
            @RequestParam("assetId") String assetId,
            @ApiParam(name = "orderType", value = "单据类型，非必填")
            @RequestParam(value = "orderType", required = false) Integer orderType,
            @ApiParam(name = "hasPerm", value = "是否需要权限")
            @RequestParam(value = "hasPerm", required = false) Boolean hasPerm) {
        // 2021年8月18日19:18:43 添加是否需要权限判断
        return assetService.getInfoNoPerm(assetId, orderType, hasPerm);
    }

    @ApiOperation(value = "rfid或扫码快速识别", notes = "盘点rfid快速识别")
    @PostMapping(value = "/quicksearch")
    public List<JSONObject> quickSearch(@RequestBody AssetQuickSearchDto quickSearchDto) {
        if (CollUtil.isEmpty(quickSearchDto.getEpcIds())
                && CollUtil.isEmpty(quickSearchDto.getUniqueIds())) {
            return ListUtil.empty();
        }
        return assetService.quickSearch(quickSearchDto);

    }

    @ApiOperation(value = "扫码后获取资产或耗材详情")
    @GetMapping(value = "/assetOrMaterialInfo")
    public Map<String, Object> assetOrMaterialInfo(
            @ApiParam(name = "assetId", value = "资产Id")
            @RequestParam("assetId") String assetId,
            @ApiParam(name = "orderType", value = "单据类型，非必填")
            @RequestParam(value = "orderType", required = false) Integer orderType,
            @ApiParam(name = "hasPerm", value = "是否需要权限")
            @RequestParam(value = "hasPerm", required = false) Boolean hasPerm) {
        // 1.先查资产
        Map<String, Object> result = new HashMap<>(4);
        if (StrUtil.isEmpty(assetId)) {
            return result;
        }
        JSONObject infoNoPerm = assetService.getInfoNoPermPhp(assetId, orderType, hasPerm);
        // 2.资产不为空直接返回资产数据
        if (!CollUtil.isEmpty(infoNoPerm)) {
            result.put("type", "asset");
            result.put("value", infoNoPerm);
            return result;
        }
        // 3.资产为空去查耗材
        result.put("type", "material");
        result.put("value", new JSONObject());
        MaterialDto code = materialFeignClient.getInfoNoPermPhp(assetId);
        if (Objects.nonNull(code)) {
            result.put("value", code);
        }
        return result;
    }

    @ApiOperation("根据区域ID查询资产列表")
    @GetMapping("/list/byArea/{areaId}/{range}")
    @AutoConvert
    public AppAssetAreaDto listByArea(@PathVariable String areaId, @PathVariable(required = false) Integer range) {
        long id;
        try {
            if (areaId.startsWith("area:")) {
                areaId = areaId.replaceAll("area:", "");
            }
            id = Long.parseLong(areaId);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.AREA_NOT_EXIST);
        }
        AppAssetAreaDto area = assetFeignClient.listByArea(id, range);
        if (Objects.isNull(area)) {
            throw new BusinessException(SystemResultCode.AREA_NOT_EXIST);
        }
        if (!CollUtil.isEmpty(area.getAssets())) {
            area.getAssets().forEach(asset -> {
                if (StrUtil.isBlank(asset.getAssetPhoto())) {
                    return;
                }
                String photo = asset.getAssetPhoto();
                List<String> array = JSONObject.parseArray(photo, String.class);
                List<String> photos = new ArrayList<>(array.size());
                // 给一张图片
                if (!CollUtil.isEmpty(array)) {
                    photos.add(array.get(0));
                }
                asset.setPhotos(photos);
                asset.setAssetPhoto(null);
            });
        }
        return area;
    }

    @ApiOperation("根据区域ID查询资产数量统计")
    @PostMapping("/stat/byArea")
    public AppAssetAreaStatDto staat(@RequestBody SearchAppAssetAreaDto dto) {
        dto.convertAreaId();
        return assetFeignClient.statByArea(dto);
    }

    @ApiOperation("根据区域ID查询资产分页列表")
    @PostMapping("/page/byArea")
    public PageUtils<AssetValue> pageByArea(@RequestBody SearchAppAssetAreaDto dto) {
        dto.convertAreaId();
        PageUtils<AssetValue> page = assetFeignClient.pageByArea(dto);
        if (Objects.isNull(page)) {
            throw new BusinessException(SystemResultCode.AREA_NOT_EXIST);
        }

        if (!CollUtil.isEmpty(page.getList())) {
            dictConvertUtil.convertToDictionary(page.getList());
            page.getList().forEach(asset -> {
                if (StrUtil.isBlank(asset.getAssetPhoto())) {
                    return;
                }
                String photo = asset.getAssetPhoto();
                List<String> array = JSONObject.parseArray(photo, String.class);
                List<String> photos = new ArrayList<>(array.size());
                // 给一张图片
                if (!CollUtil.isEmpty(array)) {
                    photos.add(array.get(0));
                }
                asset.setPhotos(photos);
                asset.setAssetPhoto(null);
            });
        }
        return page;
    }

}
