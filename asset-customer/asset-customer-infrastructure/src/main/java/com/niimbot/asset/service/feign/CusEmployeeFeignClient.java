package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.CompanyDetailDto;
import com.niimbot.system.CusEmpRegisterDto;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeOptDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.EmployeeImportDto;
import com.niimbot.system.EmployeeModifyDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.RemoveEmployDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 员工管理feign客户端
 *
 * <AUTHOR>
 * @Date 2020/11/12
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CusEmployeeFeignClient {

    @GetMapping("server/system/employee/list/by/mobile/{mobile}")
    List<CusEmployeeDto> listByMobile(@PathVariable("mobile") String mobile);

    @GetMapping("server/system/employee/adminInfo")
    CusEmployeeDto getAdminInfo();

    /**
     * 新增员工
     *
     * @param employeeDto
     * @return 是否成功
     */
    @PostMapping(value = "server/system/employee")
    Boolean save(CusEmployeeOptDto employeeDto);



    /**
     * 新增员工2.0
     *
     * @param employeeDto
     * @return 是否成功
     */
    @PostMapping(value = "server/system/employee/v2")
    String saveV2(CusEmployeeOptDto employeeDto);

    /**
     * 修改员工
     *
     * @param employeeDto
     * @return 是否成功
     */
    @PutMapping(value = "server/system/employee")
    Boolean edit(CusEmployeeOptDto employeeDto);

    @PutMapping("server/system/employee/batchModifyOrg")
    Boolean batchModifyOrg(@RequestBody List<CusEmployeeOptDto> employeeDtos);

    /**
     * 删除员工
     *
     * @param employ
     * @return 是否删除成功
     */
    @DeleteMapping(value = "server/system/employee")
    Boolean remove(RemoveEmployDto employ);

    @PutMapping("server/system/employee/transferEmp")
    void transferEmp(RemoveEmployDto employDto);

    /**
     * 根据条件查询员工分页列表
     *
     * @param dto 查询参数
     * @return 列表信息
     */
//    @GetMapping(value = "server/system/employee/list")
//    List<CusEmployeeDto> list(@SpringQueryMap CusEmployeeQueryDto dto);

    @PostMapping("server/system/employee/listHasAccount")
    List<CusEmployeeDto> listHasAccount(@RequestBody CusEmployeeQueryDto dto);

    /**
     * 根据条件查询员工分页列表
     *
     * @param dto 查询参数
     * @return 列表信息
     */
    @GetMapping(value = "server/system/employee/act/list")
    List<CusEmployeeDto> actList(@SpringQueryMap CusEmployeeQueryDto dto);

    /**
     * 根据条件查询员工分页列表
     *
     * @param dto 查询参数
     * @return 列表信息
     */
    @GetMapping(value = "server/system/employee/page")
    PageUtils<CusEmployeeDto> page(@SpringQueryMap CusEmployeeQueryDto dto);

    /**
     * 新增获取员工推荐工号
     *
     * @return 推荐工号
     */
    @GetMapping(value = "server/system/employee/recommendEmpNo")
    String recommendEmpNo();

    /**
     * 检查手机号是否存在
     *
     * @param mobile 手机号
     * @return 是否存在
     */
    @GetMapping(value = "server/system/employee/checkPhone")
    CusEmployeeDto checkMobile(@RequestParam("mobile") String mobile);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    @GetMapping(value = "server/system/employee/checkEmail")
    CusEmployeeDto checkEmail(@RequestParam("email") String email);

    /**
     * 查询员工
     *
     * @param empId 员工Id
     * @return 员工信息
     */
    @GetMapping(value = "server/system/employee/{empId}")
    CusEmployeeDto getInfo(@PathVariable("empId") Long empId);

    /**
     * 修改用户名
     *
     * @param username 名称
     * @return 结果
     */
    @PutMapping(value = "server/system/employee/changeName")
    Boolean changeName(@RequestParam("username") String username);

    /**
     * 根据员工id集合查询员工
     *
     * @param ids 查询参数
     * @return 组织
     */
    @PostMapping(value = "server/system/employee/listByIds")
    List<CusEmployeeDto> listByIds(List<Long> ids);

    /**
     * 修改头像
     *
     * @param image image url
     * @return url
     */
    @GetMapping(value = "server/system/employee/changeImage")
    Boolean changeImage(@RequestParam("image") String image);

    /**
     * 组织员工关联查询
     *
     * @param orgIds 组织Id
     * @return 组织员工集合
     */
    /*@GetMapping(value = "server/system/employee/orgEmpList")
    List<CusEmployeeDto> orgEmpList(@RequestParam(value = "orgIds", required = false) List<Long> orgIds,
                                    @RequestParam(value = "kw", required = false) String kw);*/

    /**
     * 根据组织id获取主管信息
     *
     * @param orgId 组织id
     * @return 主管信息
     */
    @GetMapping(value = "server/system/employee/getDirectorByOrgId")
    List<CusEmployeeDto> getDirectorByOrgId(@RequestParam("orgId") Long orgId);

    /**
     * 新增获取员工推荐工号
     *
     * @return 推荐工号
     */
    @GetMapping(value = "server/system/employee/getRecommendEmpNo/{companyId}")
    String getRecommendEmpNo(@PathVariable("companyId") Long companyId);

    /**
     * 二维码邀请员工-注册开通账号
     *
     * @return map
     */
    @PostMapping(value = "server/system/employee/scanInvite/openAccount")
    Map<String, String> inviteOpenAccount(CusEmpRegisterDto cusEmpRegisterDto);

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping(value = "server/system/employee/currentUserInfo")
    CusEmployeeDto currentUserInfo();

    /**
     * 查询会员详情
     *
     * @param companyId 企业id
     * @return 会员详情
     */
    @GetMapping("server/system/company/detail/{companyId}")
    CompanyDetailDto getCompanyDetail(@PathVariable("companyId") Long companyId);

    /**
     * 修改邮箱
     *
     * @param email 邮箱
     * @return 结果
     */
    @PutMapping("server/system/employee/changeEmail/{email}")
    Boolean changeEmail(@PathVariable("email") String email);

    @GetMapping(value = "server/system/employee/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @DeleteMapping(value = "server/system/employee/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/system/employee/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/system/employee/saveSheetData")
    Boolean saveSheetData(EmployeeImportDto importDto);


    @PutMapping("server/system/employee/changeEmpMobile/{newMobile}")
    Boolean changeEmpMobile(@PathVariable("newMobile") String newMobile);

    @PostMapping("/server/system/employee/listByCodes")
    List<CusEmployeeDto> listByCodes(@RequestBody List<String> codes);

    @PostMapping("server/system/employee/modifyEmployeeNo")
    Boolean modifyEmployeeNo(@RequestBody EmployeeModifyDto employeeModifyDto);

    @PostMapping("server/system/employee/verifyEmpName")
    Boolean verifyEmpName(@RequestBody CusEmployeeOptDto employee);

}
