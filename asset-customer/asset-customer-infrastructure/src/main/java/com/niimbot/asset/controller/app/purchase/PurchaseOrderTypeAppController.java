package com.niimbot.asset.controller.app.purchase;

import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.asset.service.feign.PurchaseOrderTypeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.purchase.PurchaseOrderTypeDto;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.CusMenuDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023/5/4 14:56
 */
@Api(tags = "采购单据配置管理接口")
@RequestMapping("api/app/purchase/orderType")
@ResultController
@Validated
public class PurchaseOrderTypeAppController {

    private final Map<Integer, String> orderTypeMenu = new HashMap<>();

    {
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_PURCHASE, "purchase-apply");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_PURCHASE_ORDER, "purchase-order");
    }

    private final CusMenuFeignClient menuFeignClient;
    private final PurchaseOrderTypeFeignClient purchaseOrderTypeFeignClient;

    @Autowired
    public PurchaseOrderTypeAppController(CusMenuFeignClient menuFeignClient,
                                          PurchaseOrderTypeFeignClient purchaseOrderTypeFeignClient) {
        this.menuFeignClient = menuFeignClient;
        this.purchaseOrderTypeFeignClient = purchaseOrderTypeFeignClient;
    }

    @ApiOperation(value = "单据类型列表")
    @GetMapping(value = "/list")
    public List<PurchaseOrderTypeDto> listOrderType() {
        AppCusMenuDto appCusMenuDto = menuFeignClient.userMenuAppList();
        List<CusMenuDto> menus = appCusMenuDto.getMenus();
        List<String> menuCodes = menus.stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        return purchaseOrderTypeFeignClient.listOrderType().stream()
                .filter(f -> {
                    String menu = orderTypeMenu.get(f.getType());
                    return menuCodes.contains(menu);
                }).collect(Collectors.toList());
    }


}
