package com.niimbot.asset.service.feign;

import com.niimbot.sale.InvoiceSettingDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2022/3/21 18:31
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface InvoiceSettingFeignClient {

    /**
     * 开票设置查询
     *
     * @return
     */
    @GetMapping("server/sale/invoiceSetting")
    InvoiceSettingDto queryInfo();
}
