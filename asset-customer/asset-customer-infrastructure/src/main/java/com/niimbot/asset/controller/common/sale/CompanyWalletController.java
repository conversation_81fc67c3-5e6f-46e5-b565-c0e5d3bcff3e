package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.CompanyWalletFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.CompanyWalletArrearsDto;
import com.niimbot.sale.CompanyWalletDto;
import com.niimbot.system.CompanyDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @since 2021/12/29 10:14
 */
@Api(tags = "【服务中心】企业钱包")
@ResultController
@RequestMapping("api/common/wallet")
@RequiredArgsConstructor
public class CompanyWalletController {

    private final CompanyWalletFeignClient companyWalletFeign;

    private final CompanyFeignClient companyFeignClient;

    @ApiOperation(value = "查询钱包信息")
    @AutoConvert
    @GetMapping
    public CompanyWalletDto getWallet() {
        CompanyDto companyInfo = companyFeignClient.getCompanyInfo(LoginUserThreadLocal.getCompanyId());
        CompanyWalletDto wallet = this.companyWalletFeign.getWallet();
        wallet.setCompanyName(companyInfo.getName())
                .setStatus(companyInfo.getStatus());
        return wallet;
    }

    @ApiOperation(value = "欠费状态")
    @AutoConvert
    @GetMapping("/arrears")
    public CompanyWalletArrearsDto getWalletArrears() {
        return companyWalletFeign.getWalletArrears(LoginUserThreadLocal.getCompanyId());
    }

}
