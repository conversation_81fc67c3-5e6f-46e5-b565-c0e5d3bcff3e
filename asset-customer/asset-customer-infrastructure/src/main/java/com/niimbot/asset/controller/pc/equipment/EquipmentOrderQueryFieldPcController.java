package com.niimbot.asset.controller.pc.equipment;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.OrderQueryConditionGeneralDto;
import com.niimbot.system.QueryConditionConfigDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/17 17:15
 */
@Api(tags = "【设备管理】单据字段管理")
@ResultController
@RequestMapping("api/pc/queryField/equipmentOrder")
@RequiredArgsConstructor
public class EquipmentOrderQueryFieldPcController {

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @ApiOperation(value = "【PC】筛选项配置-保存")
    @RepeatSubmit
    @PostMapping("/query/field")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@RequestBody @Validated OrderQueryConditionGeneralDto condition) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(),
                QueryFieldConstant.EQUIPMENT_MAINTAIN_ORDER_TYPE_QUERY.get(condition.getOrderType())));
    }

    @ApiOperation(value = "【PC】筛选项配置-查询")
    @GetMapping("/query/field/{orderType}")
    public OrderQueryConditionGeneralDto getQueryField(@PathVariable("orderType") Integer orderType) {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(
                QueryFieldConstant.EQUIPMENT_MAINTAIN_ORDER_TYPE_QUERY.get(orderType));
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(OrderQueryConditionGeneralDto.class);
    }


}
