package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.*;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.MaterialOrderService;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.material.MaterialOrderResponseDto;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.means.CommonFileDto;
import com.niimbot.means.ExportOrderAssetsHeaderDto;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportTaskDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/4 15:23
 */
@Slf4j
@Service
public class MaterialOrderServiceImpl extends AbstractExcelExportService implements MaterialOrderService {

    private static final String MATERIALS = "materials";

    private final ImportTaskFeignClient importTaskFeignClient;
    @Autowired
    private FileUploadConfig fileUploadConfig;
    @Autowired
    private AsOrderUtil asOrderUtil;
    @Autowired
    private AsMaterialUtil asMaterialUtil;
    @Autowired
    private DictConvertUtil dictConvertUtil;
    @Autowired
    private ActWorkflowFeignClient workflowFeignClient;
    @Autowired
    private RedisService redisService;
    @Autowired
    private FormFeignClient formFeignClient;
    @Autowired
    private MaterialOrderQueryFieldServiceImpl orderQueryFieldService;
    @Autowired
    private MaterialQueryFieldService materialQueryFieldService;
    @Autowired
    private CacheResourceUtil cacheResourceUtil;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private FileUploadService fileUploadService;

    protected MaterialOrderServiceImpl(ImportTaskFeignClient importTaskFeignClient) {
        super(importTaskFeignClient);
        this.importTaskFeignClient = importTaskFeignClient;
    }


    @Getter
    @AllArgsConstructor
    private static class OrderCardExportData extends AbsExportData {
        private JSONObject orderData;
        private WorkflowApproveInfoDto approveInfo;
    }


    @Getter
    @AllArgsConstructor
    private static class OrderExportData extends AbsExportData {
        private Long orderId;
        private JSONObject orderData;
        private List<JSONObject> materialList;
    }


    @Override
    public ExportResponse exportOrderCard(List<MaterialOrderExportDto> list, MaterialOrderQueryDto queryDto, int orderType) {
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        Edition.weixin(() -> {
            if (list.size() > 100) {
                BusinessExceptionUtil.throwException("单据卡片单次最多允许下载100条数据。超过100条请分次下载");
            }
        });
        ExportResponse exportResponse = new ExportResponse();
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(orderType, "card"), orderType);
        exportParams.setQueryCondition(JsonUtil.toJsonObject(queryDto));
        exportParams.setExportUrl(OrderTypeNewEnum.getByType(orderType).getExportUrl());
        // 补上审批流数据
        List<MaterialOrderResponseDto> orderDetailDtoList = getWorkFlowData(list, orderType);

        //敏感数据处理
        if (CollUtil.isNotEmpty(orderDetailDtoList)) {
            desensitizationDataUtil.handleSensitiveField(orderDetailDtoList.stream().map(MaterialOrderResponseDto::getOrder).collect(Collectors.toList()), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }

        List<AbsExportData> exportDataList = orderDetailDtoList.stream().map(dto ->
                new OrderCardExportData(dto.getOrder(), dto.getApproveInfo())).collect(Collectors.toList());
        exportParams.setExportDataList(exportDataList);
        if (list.size() > 1000) {
            exportResponse.setSync(true);
            sync(exportParams, this::executeOrderCardExport);
        } else {
            exportResponse.setSync(false);
            exportResponse.setPath(async(() ->
                    executeOrderCardExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(exportResponse.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return exportResponse;
    }

    @Override
    public ExportResponse exportOrder(List<MaterialOrderExportDto> list, MaterialOrderQueryDto query, int orderType) {
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        ExportResponse exportResponse = new ExportResponse();
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(orderType, "list"), orderType);
        exportParams.setQueryCondition(JsonUtil.toJsonObject(query));
        exportParams.setExportUrl(OrderTypeNewEnum.getByType(orderType).getDetailExportUrl());
        List<AbsExportData> exportDataList = list.stream().map(dto -> {
            List<JSONObject> materialList = dto.getMaterials().stream().map(asMaterialUtil::toJSONObject)
                    .collect(Collectors.toList());
            JSONObject jsonObject = asOrderUtil.toJSONObject(dto);

            List<JSONObject> dataList = new ArrayList<>(materialList);
            dataList.add(jsonObject);
            //敏感数据处理
            desensitizationDataUtil.handleSensitiveField(dataList, SensitiveObjectTypeEnum.MATERIAL.getCode());
            // 特殊处理创建时间
            if (Objects.nonNull(dto.getCreateTime())) {
                long timeVal = dto.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    jsonObject.putIfAbsent("createTime", timeVal);
                }
            }
            return new OrderExportData(dto.getId(), jsonObject, materialList);
        }).collect(Collectors.toList());
        exportParams.setExportDataList(exportDataList);
        if (list.size() > 1000) {
            exportResponse.setSync(true);
            sync(exportParams, this::executeOrderExport);
        } else {
            exportResponse.setSync(false);
            exportResponse.setPath(async(() ->
                    executeOrderExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(exportResponse.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return exportResponse;
    }

    private String executeOrderExport(Long taskId, ExportParams exportParams) {
        FormVO form = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(exportParams.getOrderType()).getBizType());

        List<OrderExportData> exportDataList = exportParams.getExportDataList().stream()
                .map(f -> (OrderExportData) f).collect(Collectors.toList());

        List<FormFieldCO> orderFormFieldList = new ArrayList<>(form.getFormFields());

        FormFieldCO createTime = new FormFieldCO();
        createTime.setFieldCode(QueryFieldConstant.FIELD_CREATE_TIME);
        createTime.setFieldName("创建时间");
        createTime.setFieldType(FormFieldCO.DATETIME);
        createTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        orderFormFieldList.add(createTime);

        FormFieldCO updateTime = new FormFieldCO();
        updateTime.setFieldCode(QueryFieldConstant.FIELD_UPDATE_TIME);
        updateTime.setFieldName("更新时间");
        updateTime.setFieldType(FormFieldCO.DATETIME);
        updateTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        orderFormFieldList.add(updateTime);

        List<JSONObject> orderDataList = exportDataList.stream().map(OrderExportData::getOrderData).collect(Collectors.toList());
        asMaterialUtil.formatJson(orderDataList, orderFormFieldList);
        OrderJsonUtil.convertJsonKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, orderDataList);

        FormVO materialFormVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> materialFieldList = new ArrayList<>(materialFormVO.getFormFields());
        materialFieldList.add(createTime);
        materialFieldList.add(updateTime);

        Map<String, String> translationCodeMap = materialFieldList.stream()
                .filter(f -> StrUtil.isNotEmpty(f.getTranslationCode()))
                .collect(Collectors.toMap(FormFieldCO::getTranslationCode, FormFieldCO::getFieldCode));
        Map<Long, String> empNameCache = new ConcurrentHashMap<>();
        exportDataList.parallelStream().forEach(exp -> {
            List<JSONObject> materialList = exp.getMaterialList();
            // 本身是快照，直接返回
            for (JSONObject jsonObject : materialList) {
                translationCodeMap.forEach((k, v) -> jsonObject.put(v, jsonObject.get(k)));
                // 特殊处理创建人，不在表单内
                if (jsonObject.containsKey(AssetConstant.ASSET_FIELD_CREATE_BY)) {
                    Long createBy = jsonObject.getLong(AssetConstant.ASSET_FIELD_CREATE_BY);
                    if (empNameCache.containsKey(createBy)) {
                        jsonObject.put(AssetConstant.ASSET_FIELD_CREATE_BY, empNameCache.get(createBy));
                    } else {
                        String userNameAndCode = cacheResourceUtil.getUserNameAndCode(createBy);
                        jsonObject.put(AssetConstant.ASSET_FIELD_CREATE_BY, userNameAndCode);
                        empNameCache.put(createBy, userNameAndCode);
                    }
                }
            }
            asMaterialUtil.formatJson(materialList, materialFieldList);
        });
        return executeExportOrderMaterials(taskId, exportDataList, exportParams);
    }

    private String executeExportOrderMaterials(Long taskId, List<OrderExportData> exportDataList,
                                               ExportParams exportParams) {
        String path = StrUtil.EMPTY;
        File tempPath = getTempPath(3);
        Integer orderType = exportParams.getOrderType();

        ExportOrderAssetsHeaderDto orderMaterialsHeader = getOrderMaterialsHeader(orderType);
        LinkedHashMap<String, String> headerData = orderMaterialsHeader.getHeader();
        Map<String, String> codeTypeMap = orderMaterialsHeader.getCodeTypeMap();
        File outputFile = null;
        try {
            String excelName = getOrderMaterialsExcelName(exportParams.getOrderType());
            outputFile = new File(tempPath.getPath() + "/" + excelName);
            // 本地文件路径
            String localPath = outputFile.getPath();
            int headLen = headerData.size();
            ExcelWriter writer = ExcelUtil.getWriter(true);
            Sheet sheet = writer.getSheet();
            // 设置边框
            StyleSet styleSet = writer.getStyleSet();
            styleSet.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
            styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.TOP);

            // 设置表头的cellStyle
            CellStyle cellStyle = writer.createCellStyle();
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);

            // 写入文件标题
            String headerText = String.format("%s单",
                    OrderTypeNewEnum.getByType(orderType).getName());
            writer.merge(0, 0, 0, headLen - 1, headerText, false);
            Cell title = writer.getCell(0, 0);
            CellStyle commentStyle = writer.createCellStyle();
            commentStyle.setAlignment(HorizontalAlignment.CENTER);
            commentStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
            title.setCellStyle(commentStyle);

            // 写入表头
            AtomicInteger rowIdx = new AtomicInteger(1);
            List<String> headCodeList = new ArrayList<>();
            AtomicInteger cellIdx = new AtomicInteger(0);
            Row header = writer.getOrCreateRow(rowIdx.getAndIncrement());
            headerData.forEach((k, v) -> {
                int idx = cellIdx.getAndIncrement();
                headCodeList.add(k);
                Cell cell = header.createCell(idx);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(v);
                // 调整每一列宽度
                sheet.autoSizeColumn((short) idx);
                // 解决自动设置列宽中文失效的问题
                sheet.setColumnWidth(idx, sheet.getColumnWidth(idx) * 17 / 10);
            });

            Map<Integer, JSONObject> mergeOrderMap = new LinkedHashMap<>();
            Map<Integer, Integer> mergeRangeMap = new LinkedHashMap<>();
            int idx = rowIdx.get();
            int materialStart = orderMaterialsHeader.getOrderHeaderSize() - 1;

            for (OrderExportData exportData : exportDataList) {
                List<JSONObject> materialList = exportData.getMaterialList();
                int materialSize = materialList.size();
                for (JSONObject material : materialList) {
                    idx = rowIdx.getAndIncrement();
                    Row row = writer.getOrCreateRow(idx);
                    for (int i = materialStart; i < headCodeList.size(); i++) {
                        String code = headCodeList.get(i);
                        Cell cell = row.createCell(i);
                        cell.setCellStyle(cellStyle);
                        if (FormFieldCO.NUMBER_INPUT.equals(codeTypeMap.get(code))) {
                            Double num = Convert.toDouble(material.get(code));
                            if (num != null) {
                                cell.setCellValue(num);
                            } else {
                                String numStr = Convert.toStr(material.get(code));
                                if (StrUtil.isNotEmpty(numStr)) {
                                    cell.setCellValue(numStr);
                                }
                            }
                        } else {
                            String str = Convert.toStr(material.get(code));
                            if (StrUtil.isNotEmpty(str)) {
                                cell.setCellValue(str);
                            }
                        }
                    }
                }
                // 需要跨行的单据
                int num = idx - materialSize + 1;
                mergeOrderMap.put(num, exportData.getOrderData());
                mergeRangeMap.put(num, materialSize);
            }

            mergeOrderMap.forEach((rowNum, order) -> {
                Integer mergeRows = mergeRangeMap.get(rowNum);
                Row row = writer.getOrCreateRow(rowNum);
                for (int i = 0; i < orderMaterialsHeader.getOrderHeaderSize(); i++) {
                    String code = headCodeList.get(i);
                    Cell cell = row.createCell(i);
                    cell.setCellStyle(cellStyle);
                    if (FormFieldCO.NUMBER_INPUT.equals(codeTypeMap.get(code))) {
                        Double num = Convert.toDouble(order.get(code));
                        if (num != null) {
                            if (mergeRows > 1) {
                                writer.merge(rowNum, (rowNum + mergeRows - 1), i, i, num, false);
                            } else {
                                cell.setCellValue(num);
                            }
                        } else {
                            String numStr = Convert.toStr(order.get(code));
                            if (StrUtil.isNotEmpty(numStr)) {
                                if (mergeRows > 1) {
                                    writer.merge(rowNum, (rowNum + mergeRows - 1), i, i, numStr, false);
                                } else {
                                    cell.setCellValue(numStr);
                                }
                            }
                        }
                    } else {
                        String str = Convert.toStr(order.get(code));
                        if (StrUtil.isNotEmpty(str)) {
                            if (mergeRows > 1) {
                                writer.merge(rowNum, (rowNum + mergeRows - 1), i, i, str, false);
                            } else {
                                cell.setCellValue(str);
                            }
                        }
                    }
                }
            });

            //设置输出文件路径
            writer.setDestFile(outputFile);
            writer.flush();
            writer.close();

            // 上传文件到oss 单个文件或者zip包
            path = fileUploadService.putFile(getOrderMaterialsDestPath(exportParams), localPath);
            // 更新任务url
            if (taskId != null && StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
            AuditLogs.sendOrderExportRecord(exportParams.getOrderType(), exportParams.getExportDataList().size(), Auditable.Action.EXP_OR_LT);
        } catch (Exception e) {
            log.error("单据耗材列表导出失败, {}", e.getMessage(), e);
        } finally {
            FileUtil.del(outputFile);
        }
        return path;
    }

    private ExportOrderAssetsHeaderDto getOrderMaterialsHeader(Integer orderType) {
        List<AssetHeadDto> orderHeadDtos = orderQueryFieldService.orderHeadView(orderType);
        List<AssetHeadDto> assetHeadDtos = materialQueryFieldService.materialHeadView();

        Map<String, String> codeTypeMap = new HashMap<>();
        orderHeadDtos = orderHeadDtos.stream()
                .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                .collect(Collectors.toList());

        assetHeadDtos = assetHeadDtos.stream()
                .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                .peek(f -> codeTypeMap.put(f.getCode(), f.getType()))
                .collect(Collectors.toList());

        // 查询head
        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();

        for (AssetHeadDto head : orderHeadDtos) {
            String code = OrderJsonUtil.convertKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, head.getCode());
            if (QueryFieldConstant.FIELD_APPROVE_STATUS.equals(head.getCode())) {
                headerData.put(code + "Text", head.getName());
                codeTypeMap.put(code + "Text", head.getType());
            } else {
                if (StrUtil.isNotBlank(head.getTranslationCode())) {
                    String translationCode = OrderJsonUtil.convertKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, head.getTranslationCode());
                    headerData.put(translationCode, head.getName());
                    codeTypeMap.put(translationCode, head.getType());
                } else {
                    headerData.put(code, head.getName());
                    codeTypeMap.put(code, head.getType());
                }
            }
        }
        for (AssetHeadDto head : assetHeadDtos) {
            headerData.put(head.getCode(), head.getName());
            codeTypeMap.put(head.getCode(), head.getType());
        }
        if (AssetConstant.ORDER_TYPE_MATERIAL_LY == orderType) {
            codeTypeMap.put("lyNum", FormFieldCO.TEXT_INPUT);
            headerData.put("lyNum", "领用数量");
            codeTypeMap.put("grantNum", FormFieldCO.TEXT_INPUT);
            headerData.put("grantNum", "已发放数量");
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_RK == orderType) {
            codeTypeMap.put("avgPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("avgPrice", "加权平均价值");
            codeTypeMap.put("rkNum", FormFieldCO.TEXT_INPUT);
            headerData.put("rkNum", "入库数量");
            codeTypeMap.put("rkPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("rkPrice", "入库金额");
            codeTypeMap.put("rkUnitPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("rkUnitPrice", "入库单价");
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_CK == orderType) {
            codeTypeMap.put("ckNum", FormFieldCO.TEXT_INPUT);
            headerData.put("ckNum", "出库数量");
            codeTypeMap.put("ckPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("ckPrice", "出库金额");
            codeTypeMap.put("ckUnitPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("ckUnitPrice", "出库单价");
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_TZ == orderType) {
            codeTypeMap.put("tzPreStockNum", FormFieldCO.TEXT_INPUT);
            headerData.put("tzPreStockNum", "调整前库存");
            codeTypeMap.put("tzPreAvgPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("tzPreAvgPrice", "调整前单价");
            codeTypeMap.put("tzStockNum", FormFieldCO.TEXT_INPUT);
            headerData.put("tzStockNum", "调整后库存");
            codeTypeMap.put("tzAvgPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("tzAvgPrice", "调整后单价");
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_DB == orderType) {
            codeTypeMap.put("dbNum", FormFieldCO.TEXT_INPUT);
            headerData.put("dbNum", "调拨数量");
            codeTypeMap.put("dbAvgPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("dbAvgPrice", "调拨单价");
            codeTypeMap.put("dbPrice", FormFieldCO.TEXT_INPUT);
            headerData.put("dbPrice", "调拨金额");
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_BS == orderType) {
            codeTypeMap.put("bsNum", FormFieldCO.TEXT_INPUT);
            headerData.put("bsNum", "报损数量");
            codeTypeMap.put("bsRemark", FormFieldCO.TEXT_INPUT);
            headerData.put("bsRemark", "报损说明");
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_TK == orderType) {
            codeTypeMap.put("tkNum", FormFieldCO.TEXT_INPUT);
            headerData.put("tkNum", "退库数量");
        }
        return new ExportOrderAssetsHeaderDto()
                .setOrderHeaderSize(orderHeadDtos.size())
                .setHeader(headerData)
                .setCodeTypeMap(codeTypeMap);
    }


    private String getOrderMaterialsDestPath(ExportParams exportParams) {
        String templateStr = "%s单导出";
        String tempName = String.format(templateStr, OrderTypeNewEnum.getByType(exportParams.getOrderType()).getName());
        String name = tempName + "(" +
                DateUtil.format(DateUtil.date(), "MMddHHmmss") + ")" + ".xlsx";
        return exportParams.getCompanyId() + "/" + exportParams.getOrderTypeCode() + "_order_materials/" + DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN) + "/" + name;
    }

    /**
     * 耗材入库报表导出
     *
     * @param query
     * @return 结果
     */

    /**
     * 写入导出数据--首先写入到临时文件(多个文件则打包压缩)，然后上传到oss
     */
    private String executeOrderCardExport(Long taskId, ExportParams exportParams) {
        String path = StrUtil.EMPTY;
        // 获取临时存放路径
        File tempPath = getTempPath(1);
        // 单据类型
        Integer orderType = exportParams.getOrderType();

        // 获取单据动态表头数据
        FormVO form = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<FormFieldCO> headDtos = form.getFormFields().stream()
                .filter(f -> !AssetConstant.ED_SPLIT_LINE.equals(f.getFieldType()))
                .filter(f -> !f.isHidden())
                .collect(Collectors.toList());

        // 获取耗材动态表头数据
        FormVO materialVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        Map<String, String> materialHeads = materialVO.getFormFields().stream()
                .filter(f -> !FormFieldCO.SPLIT_LINE.equals(f.getFieldType()))
                .collect(Collectors.toMap(FormFieldCO::getFieldCode, FormFieldCO::getFieldName));
        File outputFile = null;
        try {
            // 本地文件路径
            String localPath = "";
            for (AbsExportData absExportData : exportParams.getExportDataList()) {
                OrderCardExportData exportData = (OrderCardExportData) absExportData;
                JSONObject order = exportData.getOrderData();
                List<WorkflowApproveInfoDto.WorkflowExecuteStep> executeList =
                        exportData.getApproveInfo().getExecuteList();
                ExcelWriter writer = ExcelUtil.getBigWriter(5000);

                String excelName = getExcelName(exportParams.getOrderType(), order);
                outputFile = new File(tempPath.getPath() + "/" + excelName);
                localPath = outputFile.getPath();

                writer.setColumnWidth(-1, 25);
                // 获取 styleSet
                StyleSet styleSet = writer.getStyleSet();
                // 设置单元格文本自动换行
                styleSet.setWrapText();
                styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
                writer.setStyleSet(styleSet);

                // 设置表头的cellStyle
                CellStyle blackHeadStyle = writer.createCellStyle();
                blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
                blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                blackHeadStyle.setBorderBottom(BorderStyle.THIN);
                blackHeadStyle.setBorderTop(BorderStyle.THIN);
                blackHeadStyle.setBorderLeft(BorderStyle.THIN);
                blackHeadStyle.setBorderRight(BorderStyle.THIN);
                Font black = writer.createFont();
                black.setBold(true);
                black.setFontHeightInPoints((short) 15);
                black.setColor(IndexedColors.BLACK.getIndex());
                blackHeadStyle.setFont(black);

                CellStyle blackHeadStyle2 = writer.createCellStyle();
                blackHeadStyle2.setAlignment(HorizontalAlignment.LEFT);
                blackHeadStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
                blackHeadStyle2.setBorderBottom(BorderStyle.THIN);
                blackHeadStyle2.setBorderTop(BorderStyle.THIN);
                blackHeadStyle2.setBorderLeft(BorderStyle.THIN);
                blackHeadStyle2.setBorderRight(BorderStyle.THIN);
                Font black2 = writer.createFont();
                black2.setFontHeightInPoints((short) 13);
                black2.setBold(true);
                black2.setColor(IndexedColors.BLACK.getIndex());
                blackHeadStyle2.setFont(black2);

                CellStyle blackHeadStyle3 = writer.createCellStyle();
                blackHeadStyle3.setAlignment(HorizontalAlignment.LEFT);
                blackHeadStyle3.setVerticalAlignment(VerticalAlignment.CENTER);
                blackHeadStyle3.setBorderBottom(BorderStyle.THIN);
                blackHeadStyle3.setBorderTop(BorderStyle.THIN);
                blackHeadStyle3.setBorderLeft(BorderStyle.THIN);
                blackHeadStyle3.setBorderRight(BorderStyle.THIN);
                Font black3 = writer.createFont();
                black3.setFontHeightInPoints((short) 13);
                black3.setColor(IndexedColors.BLACK.getIndex());
                blackHeadStyle3.setFont(black3);
                blackHeadStyle3.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
                blackHeadStyle3.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                CellStyle cellStyle = writer.createCellStyle();
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setWrapText(true);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);

                CellStyle cellStyle1 = writer.createCellStyle();
                cellStyle1.setAlignment(HorizontalAlignment.LEFT);
                cellStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle1.setBorderBottom(BorderStyle.THIN);
                cellStyle1.setBorderTop(BorderStyle.THIN);
                cellStyle1.setBorderLeft(BorderStyle.THIN);
                cellStyle1.setBorderRight(BorderStyle.THIN);
                cellStyle1.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
                cellStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                int rowIndex = 0;

                // 写入单据信息
                rowIndex = buildOrderExcel(blackHeadStyle, blackHeadStyle2, cellStyle1, writer, rowIndex, order,
                        headDtos, exportParams);

                // 写入耗材信息
                rowIndex = buildMaterialExcel(blackHeadStyle2, blackHeadStyle3, cellStyle, writer, rowIndex, order,
                        materialHeads, exportParams);

                // 写入审批信息
                buildApproveExcel(blackHeadStyle2, cellStyle, writer, rowIndex, order, executeList);

                //设置输出文件路径
                writer.setDestFile(outputFile);
                writer.close();
            }

            // 多个需要压缩成zip
            if (exportParams.getExportDataList().size() > 1) {
                File zip = ZipUtil.zip(tempPath);
                localPath = zip.getPath();
            }

            // 上传文件到oss 单个文件或者zip包
            path = fileUploadService.putFile(getDestPath(exportParams), localPath);

            // 更新任务url
            if (taskId != null && StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
            AuditLogs.sendOrderExportRecord(exportParams.getOrderType(), exportParams.getExportDataList().size(), Auditable.Action.EXP_OR_CD);
        } catch (Exception e) {
            log.error("单据卡片导出失败, {}", e.getMessage(), e);
        } finally {
            if (outputFile != null) {
                FileUtil.del(outputFile);
            }
        }
        return path;
    }

    /**
     * 写入单据信息
     *
     * @param writer 导出文件对象
     * @param order  单据数据
     */
    private int buildOrderExcel(CellStyle headStyle,
                                CellStyle titleStyle,
                                CellStyle cellStyle,
                                ExcelWriter writer,
                                int rowIndex,
                                JSONObject order,
                                List<FormFieldCO> orderHeads,
                                ExportParams exportParams) {
        String title = OrderTypeNewEnum.getByType(exportParams.getOrderType()).getName();
        String orderNo = order.getString("orderNo");
        Long createTime = order.getLong("createTime");
        String createTimeFormat = DateUtil.format(new Date(createTime), DatePattern.NORM_DATETIME_PATTERN);
        String createByText = order.getString("createByText");

        writer.merge(rowIndex, rowIndex++, 0, 5, title, false);
        writer.setStyle(headStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);
        writer.merge(rowIndex, rowIndex++, 0, 5, "单据信息", false);
        writer.setStyle(titleStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);

        writer.setCurrentRow(rowIndex);
        writer.writeCellValue("A" + (rowIndex + 1), "单据号：");
        writer.setStyle(cellStyle, "A" + (rowIndex + 1));
        writer.writeCellValue("B" + (rowIndex + 1), orderNo);
        writer.writeCellValue("C" + (rowIndex + 1), "创建时间：");
        writer.setStyle(cellStyle, "C" + (rowIndex + 1));
        writer.writeCellValue("D" + (rowIndex + 1), createTimeFormat);
        writer.writeCellValue("E" + (rowIndex + 1), "创建人：");
        writer.setStyle(cellStyle, "E" + (rowIndex + 1));
        writer.writeCellValue("F" + (rowIndex + 1), createByText);
        writer.setRowHeight(rowIndex, 25);
        rowIndex++;

        return writeDynamicField(cellStyle, writer, rowIndex, order, orderHeads);
    }

    private int writeDynamicField(CellStyle cellStyle,
                                  ExcelWriter writer,
                                  int rowIndex,
                                  JSONObject order,
                                  List<FormFieldCO> orderHeads) {
        int fieldColumns = 3;
        int fieldAmount = orderHeads.size();
        int fieldRows = (fieldAmount % fieldColumns > 0) ?
                (fieldAmount / fieldColumns) + 1 : (fieldAmount / 3);
        int fieldIndex = 0;
        for (int i = 0; i < fieldRows; i++) {
            writer.setCurrentRow(rowIndex);
            FormFieldCO first = orderHeads.get(fieldIndex++);
            writer.writeCellValue("A" + (rowIndex + 1), first.getFieldName() + "：");
            writer.setStyle(cellStyle, "A" + (rowIndex + 1));
            writer.writeCellValue("B" + (rowIndex + 1), analysisValue(first, order));
            if (fieldIndex < fieldAmount) {
                FormFieldCO second = orderHeads.get(fieldIndex++);
                writer.writeCellValue("C" + (rowIndex + 1), second.getFieldName() + "：");
                writer.setStyle(cellStyle, "C" + (rowIndex + 1));
                writer.writeCellValue("D" + (rowIndex + 1), analysisValue(second, order));
            } else {
                writer.writeCellValue("C" + (rowIndex + 1), "");
                writer.writeCellValue("D" + (rowIndex + 1), "");
            }
            if (fieldIndex < fieldAmount) {
                FormFieldCO third = orderHeads.get(fieldIndex++);
                writer.writeCellValue("E" + (rowIndex + 1), third.getFieldName() + "：");
                writer.setStyle(cellStyle, "E" + (rowIndex + 1));
                writer.writeCellValue("F" + (rowIndex + 1), analysisValue(third, order));
            } else {
                writer.writeCellValue("E" + (rowIndex + 1), "");
                writer.writeCellValue("F" + (rowIndex + 1), "");
            }
            writer.setRowHeight(rowIndex, 25);
            rowIndex++;
        }
        return rowIndex;
    }

    private String analysisValue(FormFieldCO field, JSONObject order) {
        JSONObject props = field.getFieldProps();
        String value = "";
        if (FormFieldCO.FILES.equals(field.getFieldType())) {
            JSONArray jsonArray = order.getJSONArray(field.getFieldCode());
            if (CollUtil.isNotEmpty(jsonArray)) {
                List<CommonFileDto> orderFile = jsonArray.toJavaList(CommonFileDto.class);
                List<String> data = Lists.newArrayList();
                orderFile.forEach(fileDto -> {
                    data.add(fileDto.getUrl());
                });
                value = StrUtil.join("\n", data);
            }
        } else if (FormFieldCO.IMAGES.equals(field.getFieldType())) {
            JSONArray jsonArray = order.getJSONArray(field.getFieldCode());
            if (CollUtil.isNotEmpty(jsonArray)) {
                List<String> data = jsonArray.toJavaList(String.class);
                value = StrUtil.join("\n", data);
            }
        } else if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(field.getFieldType())) {
            JSONArray jsonArray = order.getJSONArray(field.getFieldCode());
            if (CollUtil.isNotEmpty(jsonArray)) {
                List<String> data = jsonArray.toJavaList(String.class);
                value = StrUtil.join("、", data);
            }
        } else if (FormFieldCO.DATETIME.equals(field.getFieldType())) {
            String fmt = props.getString("dateFormatType");
            if (StrUtil.isBlank(fmt)) {
                fmt = "yyyy-MM-dd";
            }
            Long date = order.getLong(field.getFieldCode());
            if (ObjectUtil.isNotNull(date)) {
                LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                value = dateTime.format(DateTimeFormatter.ofPattern(fmt));
            }
        } else {
            if (field.hasTranslation()) {
                value = order.getString(field.getTranslationCode());
            } else {
                value = order.getString(field.getFieldCode());
            }
        }
        return value;
    }

    /**
     * 写入耗材信息
     *
     * @param writer 导出文件对象
     * @param order  单据数据
     */
    private int buildMaterialExcel(CellStyle headStyle,
                                   CellStyle titleStyle,
                                   CellStyle cellStyle,
                                   ExcelWriter writer,
                                   int rowIndex,
                                   JSONObject order,
                                   Map<String, String> materialHeads,
                                   ExportParams exportParams) {
        writer.merge(rowIndex, rowIndex++, 0, 5, "耗材信息", false);
        writer.setStyle(headStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);

        writer.setCurrentRow(rowIndex);
        writer.writeCellValue("A" + (rowIndex + 1), materialHeads.get("materialCode"));
        writer.setStyle(titleStyle, "A" + (rowIndex + 1));
        writer.writeCellValue("B" + (rowIndex + 1), materialHeads.get("materialName"));
        writer.setStyle(titleStyle, "B" + (rowIndex + 1));
        writer.writeCellValue("C" + (rowIndex + 1), materialHeads.get("brand"));
        writer.setStyle(titleStyle, "C" + (rowIndex + 1));
        writer.writeCellValue("D" + (rowIndex + 1), materialHeads.get("model"));
        writer.setStyle(titleStyle, "D" + (rowIndex + 1));
        writer.writeCellValue("E" + (rowIndex + 1), materialHeads.get("unit"));
        writer.setStyle(titleStyle, "E" + (rowIndex + 1));
        writer.writeCellValue("F" + (rowIndex + 1), getMaterialHeadName(exportParams.getOrderType()));
        writer.setStyle(titleStyle, "F" + (rowIndex + 1));
        writer.setRowHeight(rowIndex, 25);
        rowIndex++;

        if (order.containsKey(MATERIALS) && ObjectUtil.isNotEmpty(order.getJSONArray(MATERIALS))) {
            List<JSONObject> orderMaterial = order.getJSONArray(MATERIALS).toJavaList(JSONObject.class);
            writer.setCurrentRow(rowIndex);
            for (JSONObject materialDto : orderMaterial) {
                writer.writeCellValue("A" + (rowIndex + 1), materialDto.getJSONObject("materialSnapshotData").getString("materialCode"));
                writer.setStyle(cellStyle, "A" + (rowIndex + 1));
                writer.writeCellValue("B" + (rowIndex + 1), materialDto.getJSONObject("materialSnapshotData").getString("materialName"));
                writer.setStyle(cellStyle, "B" + (rowIndex + 1));
                writer.writeCellValue("C" + (rowIndex + 1), materialDto.getJSONObject("materialSnapshotData").getString("brand"));
                writer.setStyle(cellStyle, "C" + (rowIndex + 1));
                writer.writeCellValue("D" + (rowIndex + 1), materialDto.getJSONObject("materialSnapshotData").getString("model"));
                writer.setStyle(cellStyle, "D" + (rowIndex + 1));
                writer.writeCellValue("E" + (rowIndex + 1), materialDto.getJSONObject("materialSnapshotData").getString("unit"));
                writer.setStyle(cellStyle, "E" + (rowIndex + 1));
                // 其他信息
                String assetInfo = "";
                switch (exportParams.getOrderType()) {
                    case AssetConstant.ORDER_TYPE_MATERIAL_RK:
                        assetInfo = "入库数量：" + materialDto.getString("rkNum") + "\n" +
                                "本次入库单价：" + materialDto.getString("rkUnitPrice") + "\n" +
                                "入库金额：" + materialDto.getString("rkPrice");
                        break;
                    case AssetConstant.ORDER_TYPE_MATERIAL_CK:
                        assetInfo = "出库数量：" + materialDto.getString("ckNum") + "\n" +
                                "本次出库单价：" + materialDto.getString("ckUnitPrice") + "\n" +
                                "出库金额：" + materialDto.getString("ckPrice");
                        break;
                    case AssetConstant.ORDER_TYPE_MATERIAL_LY:
                        assetInfo = "领用数量：" + materialDto.getString("lyNum") + "\n" +
                                "已发放数量：" + materialDto.getString("grantNum");
                        break;
                    case AssetConstant.ORDER_TYPE_MATERIAL_TZ:
                        assetInfo = "调整前库存：" + materialDto.getString("tzPreStockNum") + "\n" +
                                "调整前单价：" + materialDto.getString("tzPreAvgPrice") + "\n" +
                                "调整后库存：" + materialDto.getString("tzStockNum") + "\n" +
                                "调整后单价：" + materialDto.getString("tzAvgPrice");
                        break;
                    case AssetConstant.ORDER_TYPE_MATERIAL_DB:
                        assetInfo = Convert.toStr(materialDto.getString("dbNum"));
                        break;
                    case AssetConstant.ORDER_TYPE_MATERIAL_BS:
                        assetInfo = Convert.toStr(materialDto.getString("bsNum"));
                        break;
                    case AssetConstant.ORDER_TYPE_MATERIAL_TK:
                        assetInfo = Convert.toStr(materialDto.getString("tkNum"));
                        break;
                    default:
                        break;
                }
                writer.writeCellValue("F" + (rowIndex + 1), assetInfo);
                writer.setStyle(cellStyle, "F" + (rowIndex + 1));
                writer.setRowHeight(rowIndex, 100);
                rowIndex++;
            }
        }
        return rowIndex;
    }

    private String getMaterialHeadName(int orderType) {
        switch (orderType) {
            case AssetConstant.ORDER_TYPE_MATERIAL_RK:
                return "入库信息";
            case AssetConstant.ORDER_TYPE_MATERIAL_CK:
                return "出库信息";
            case AssetConstant.ORDER_TYPE_MATERIAL_LY:
                return "领用信息";
            case AssetConstant.ORDER_TYPE_MATERIAL_TZ:
                return "调整信息";
            case AssetConstant.ORDER_TYPE_MATERIAL_DB:
                return "调拨数量";
            case AssetConstant.ORDER_TYPE_MATERIAL_BS:
                return "报损数量";
            case AssetConstant.ORDER_TYPE_MATERIAL_TK:
                return "退库数量";
            default:
                return StrUtil.EMPTY;
        }
    }


    /**
     * 写入审批信息
     */
    private void buildApproveExcel(CellStyle headStyle,
                                   CellStyle cellStyle,
                                   ExcelWriter writer,
                                   int rowIndex,
                                   JSONObject order,
                                   List<WorkflowApproveInfoDto.WorkflowExecuteStep> executeList) {
        if (CollUtil.isEmpty(executeList)) {
            return;
        }

        writer.merge(rowIndex, rowIndex++, 0, 5, "审批信息", false);
        writer.setStyle(headStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);

        writer.setCurrentRow(rowIndex);
        writer.writeCellValue("A" + (rowIndex + 1), "审批结果：");
        writer.setStyle(cellStyle, "A" + (rowIndex + 1));
        String approveStatusText = Convert.toStr(order.getOrDefault("approveStatusText", ""));
        writer.merge(rowIndex, rowIndex, 1, 5, approveStatusText, false);
        writer.setStyle(cellStyle, "B" + (rowIndex + 1));
        writer.setRowHeight(rowIndex++, 25);

        int startRow = rowIndex, endRow = rowIndex;
        // 获取审批状态
        Object approveStatus = redisService.get(BaseConstant.SYS_DICT_KEY + "approve_status");
        Map<Short, String> approveStatusMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(approveStatus)) {
            List<JSONObject> approveStatusList = (List<JSONObject>) approveStatus;
            approveStatusMap = approveStatusList.stream().collect(Collectors.toMap(
                    k -> k.getShort("value"), k -> k.getString("label"),
                    (k1, k2) -> k1));
        }

        for (WorkflowApproveInfoDto.WorkflowExecuteStep workflowExecuteStep : executeList) {
            List<WorkflowApproveInfoDto.Approve> approveList = workflowExecuteStep.getApproveList();
            if (CollUtil.isEmpty(approveList) || workflowExecuteStep.getType().equals(1)) {
                continue;
            }

            if (workflowExecuteStep.getType().equals(7)) {
                writer.setCurrentRow(rowIndex);
                StringBuilder copyStep = new StringBuilder();
                String nameText =
                        approveList.stream().map(WorkflowApproveInfoDto.Approve::getName).collect(Collectors.joining(
                                "，"));
                copyStep.append("抄送").append("  ").append(nameText);

                writer.merge(rowIndex, rowIndex, 1, 4, copyStep.toString(), false);
                writer.setStyle(cellStyle, "B" + (rowIndex + 1));
                writer.writeCellValue("F" + (rowIndex + 1), "");
                writer.setStyle(cellStyle, "F" + (rowIndex + 1));
                writer.setRowHeight(rowIndex++, 25);

                endRow++;
                rowIndex++;
            } else {
                for (WorkflowApproveInfoDto.Approve approve : approveList) {
                    writer.setCurrentRow(rowIndex);
                    StringBuilder step = new StringBuilder();
                    Short status = (ObjectUtil.isNull(approve.getStatus()) || 0 == approve.getStatus()) ? 1 :
                            approve.getStatus();
                    String statusText = ObjectUtil.isEmpty(approve.getStatusText()) ?
                            approveStatusMap.getOrDefault(status, "") : approve.getStatusText();
                    String comment = ObjectUtil.isEmpty(approve.getComment()) ? "" : approve.getComment();

                    String approveDateFormat = "";
                    if (ObjectUtil.isNotNull(approve.getApproveDate())) {
                        LocalDateTime approveDate = approve.getApproveDate();
                        approveDateFormat = DateUtil.format(approveDate, DatePattern.NORM_DATETIME_PATTERN);
                    }

                    step.append(approve.getName()).append("  ").append(statusText);
                    if (StrUtil.isNotBlank(comment)) {
                        step.append(" ，").append(comment);
                    }

                    writer.merge(rowIndex, rowIndex, 1, 3, step.toString(), false);
                    writer.setStyle(cellStyle, "B" + (rowIndex + 1));
                    Cell cell = writer.getCell("E" + (rowIndex + 1), true);
                    if (StrUtil.isNotEmpty(approve.getSignatureLink())) {
                        try {
                            Drawing<?> drawing = writer.getSheet().createDrawingPatriarch();
                            ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, cell.getColumnIndex(), rowIndex, cell.getColumnIndex() + 1, rowIndex + 1);
                            byte[] bytes = HttpUtil.downloadBytes(approve.getSignatureLink());
                            int pictureIdx = writer.getWorkbook().addPicture(bytes, Workbook.PICTURE_TYPE_JPEG);
                            Picture picture = drawing.createPicture(anchor, pictureIdx);
                            picture.resize(1, 1);
                        } catch (Exception e) {
                            log.error("导出审批信息图片异常, {}", e.getMessage(), e);
                        }
                    }
                    writer.setStyle(cellStyle, "E" + (rowIndex + 1));
                    writer.writeCellValue("F" + (rowIndex + 1), approveDateFormat);
                    writer.setStyle(cellStyle, "F" + (rowIndex + 1));
                    writer.setRowHeight(rowIndex, StrUtil.isNotEmpty(approve.getSignatureLink()) ? 80 : 25);
                    endRow++;
                    rowIndex++;
                }
            }
        }
        if (endRow != startRow) {
            if ((endRow - startRow) == 1) {
                writer.writeCellValue("A" + endRow, "审批明细：");
            } else {
                writer.merge(startRow, endRow - 1, 0, 0, "审批明细：", false);
            }
        }
        writer.setStyle(cellStyle, "A" + startRow);
    }

    /**
     * 获取任务名称
     *
     * @param orderType 单据类型
     */
    private String getName(Integer orderType, String exportType) {
        // 获取流水号
        int count = importTaskFeignClient.count(DictConstant.TASK_TYPE_EXPORT, orderType);
        String serialNo = StrUtil.padPre(String.valueOf(++count), 5, "0");
        String templateStr;
        if ("card".equals(exportType)) {
            templateStr = "%s卡片导出（%s-%s）";
        } else {
            templateStr = "%s单导出（%s-%s）";
        }

        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return String.format(templateStr, OrderTypeNewEnum.getByType(orderType).getName(), currentTime, serialNo);
    }

    /**
     * 获取表格名称
     */
    private String getExcelName(Integer orderType, JSONObject order) {
        String templateStr = "%s单";
        String tempName = String.format(templateStr, OrderTypeNewEnum.getByType(orderType).getName());
        String orderNo = order.getString("orderNo");
        return tempName + "-" + orderNo + ".xlsx";
    }

    private String getOrderMaterialsExcelName(Integer orderType) {
        String templateStr = "%s单导出";
        String tempName = String.format(templateStr, OrderTypeNewEnum.getByType(orderType).getName());
        return tempName + "(" +
                DateUtil.format(DateUtil.date(), "MMddHHmmss") + ")" + ".xlsx";
    }

    /**
     * 获取临时存放路径
     *
     * @param type 1-单据卡片 2-报表, 3-单据列表
     */
    private File getTempPath(Integer type) {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        String name;
        if (type == 1) {
            name = "order";
        } else if (type == 2) {
            name = "report";
        } else {
            name = "order_materials";
        }
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", name, currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    /**
     * 获取目标文件存储路径
     *
     */
    private String getDestPath(ExportParams exportParams) {
        String name;
        if (exportParams.getExportDataList().size() > 1) {
            // 多个单据需要打包成压缩包
            name = exportParams.getTaskName() + ".zip";
        } else {
            String templateStr = "%s单";
            String tempName = String.format(templateStr, OrderTypeNewEnum.getByType(exportParams.getOrderType()).getName());
            AbsExportData absExportData = exportParams.getExportDataList().get(0);
            name = tempName + "-" + ((OrderCardExportData) absExportData).getOrderData().getString("orderNo") + "(" +
                    DateUtil.format(DateUtil.date(), DatePattern.PURE_TIME_PATTERN) + ")" + ".xlsx";
        }

        return exportParams.getCompanyId() + "/" + exportParams.getOrderTypeCode() + "_order/" + DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN) + "/" + name;
    }

    @Data
    @Accessors(chain = true)
    private static class GlobalCache {
        private Long company;
        private String downUrl;
        private Integer orderType;
    }

    /**
     * 补上审批流数据
     *
     * @param orderList 单据数据
     */
    private List<MaterialOrderResponseDto> getWorkFlowData(List<MaterialOrderExportDto> orderList, Integer orderType) {
        List<MaterialOrderResponseDto> orderDetailDtoList = Lists.newArrayList();
        for (MaterialOrderExportDto orderDto : orderList) {
            JSONObject orderJson = asOrderUtil.toJSONObject(orderDto);
            if (orderDto.getApproveStatus() == 0) {
                MaterialOrderResponseDto asOrderDetailDto = new MaterialOrderResponseDto()
                        .setOrder(orderJson)
                        .setApproveInfo(new WorkflowApproveInfoDto());
                orderDetailDtoList.add(asOrderDetailDto);
                continue;
            }

            WorkflowApproveInfoDto approveInfoDto;
            Long id = orderDto.getId();
            if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo(Convert.toShort(orderType), id);
                if (approveInfoDto == null || approveInfoDto.getExecuteList().isEmpty()) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo(Convert.toShort(orderType), id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo(Convert.toShort(orderType), id);
                if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(orderDto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
            MaterialOrderResponseDto asOrderDetailDto = new MaterialOrderResponseDto()
                    .setOrder(orderJson)
                    .setApproveInfo(approveInfoDto);
            orderDetailDtoList.add(asOrderDetailDto);
        }

        if (CollUtil.isEmpty(orderDetailDtoList)) {
            throw new BusinessException(MeansResultCode.ORDER_DATA_NOT_EXIST);
        }

        return orderDetailDtoList;
    }

}
