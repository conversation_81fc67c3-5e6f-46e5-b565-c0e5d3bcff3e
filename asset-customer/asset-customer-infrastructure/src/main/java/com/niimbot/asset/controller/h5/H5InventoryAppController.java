package com.niimbot.asset.controller.h5;

import cn.hutool.core.map.MapUtil;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.OnlineInventoryFeignClient;
import com.niimbot.ding.InventoryAssetAreaQueryDto;
import com.niimbot.ding.InventoryAssetAreaStatisticsDto;
import com.niimbot.ding.InventoryAssetRFIDResultDto;
import com.niimbot.ding.InventoryAssetRFIDSubmitDto;
import com.niimbot.inventory.InventoryManualBatchDto;
import com.niimbot.inventory.InventoryRemarkBatchDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "APP盘点管理")
@ResultController
@RequestMapping("api/app/inventory")
@RequiredArgsConstructor
public class H5InventoryAppController {

    private final OnlineInventoryFeignClient onlineInventoryFeignClient;

    @ApiOperation(value = "当前区域盘点进度")
    @GetMapping(value = "/asset/areaStatistics")
    public InventoryAssetAreaStatisticsDto areaStatistics(@Validated InventoryAssetAreaQueryDto queryDto) {
        return onlineInventoryFeignClient.areaStatistics(queryDto);
    }

    @ApiOperation(value = "RFID盘点")
    @PostMapping(value = "/asset/rfid")
    public InventoryAssetRFIDResultDto assetRFID(@RequestBody @Validated InventoryAssetRFIDSubmitDto submitDto) {
        //设置当前登录用户和企业信息
        submitDto.setCurrentUserId(LoginUserThreadLocal.getCurrentUserId());
        submitDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        return onlineInventoryFeignClient.assetRFID(submitDto);
    }

    @ApiOperation(value = "判断资产id是否在盘点单内")
    @GetMapping(value = "/checkAssetId")
    public Map<String, String> checkAssetId(
            @ApiParam(name = "assetId", value = "资产Id")
            @RequestParam("assetId") String assetId,
            @ApiParam(name = "inventoryId", value = "盘点单id")
            @RequestParam(value = "inventoryId") String inventoryId,
            @ApiParam(name = "inventoryTaskId", value = "盘点任务id")
            @RequestParam(value = "inventoryTaskId") String inventoryTaskId) {
        long inventoryIdLong;
        long inventoryTaskIdLong;
        try {
            inventoryIdLong = Long.parseLong(inventoryId);
            inventoryTaskIdLong = Long.parseLong(inventoryTaskId);
        } catch (Exception e) {
            log.warn("string convert to long error ==> {}", e.getMessage());
            return MapUtil.of("id", "-1");
        }
        return MapUtil.of("id", onlineInventoryFeignClient.appCheckAssetId(assetId, inventoryIdLong, inventoryTaskIdLong) + "");
    }


    @ApiOperation(value = "批量已盘")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @PostMapping(value = "/manualInventory/batch")
    @RepeatSubmit
    public Boolean manualInventoryBatch(@RequestBody @Validated InventoryManualBatchDto dto) {
        return onlineInventoryFeignClient.manualInventoryBatch(dto);
    }

    @ApiOperation(value = "批量添加存疑备注")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @PostMapping(value = "/updateRemark/batch")
    @RepeatSubmit
    public Boolean updateRemarkBatch(@RequestBody @Validated InventoryRemarkBatchDto dto) {
        return onlineInventoryFeignClient.updateRemarkBatch(dto);
    }

}
