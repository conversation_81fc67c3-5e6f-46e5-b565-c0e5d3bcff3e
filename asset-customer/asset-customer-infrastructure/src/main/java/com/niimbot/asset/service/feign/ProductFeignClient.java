package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AsProductDto;
import com.niimbot.means.AsProductInfoDto;
import com.niimbot.means.AsProductQueryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.ImportImages;
import com.niimbot.means.ProductListByIdQueryDto;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12 15:55
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ProductFeignClient {

    @PostMapping(value = "server/means/product/page")
    PageUtils<LinkedHashMap<String, Object>> page(AsProductQueryDto queryDto);

    @GetMapping(value = "server/means/product/list")
    List<AsProductInfoDto> list(@RequestParam(value = "name", required = false) String name);

    @GetMapping(value = "server/means/product/{id}")
    AsProductInfoDto info(@PathVariable("id") Long id);

    @PostMapping(value = "server/means/product/listById")
    List<AsProductInfoDto> listById(@RequestBody ProductListByIdQueryDto queryDto);

    @PostMapping(value = "server/means/product")
    AsProductInfoDto add(@RequestBody AsProductDto product);

    @PutMapping(value = "server/means/product")
    Boolean edit(AsProductDto product);

    @DeleteMapping(value = "server/means/product")
    Boolean delete(@RequestBody List<Long> ids);

    @PostMapping(value = "server/means/product/select/{productId}")
    Boolean select(@PathVariable("productId") Long productId);

    /**
     * 删除全部
     */
    @DeleteMapping(value = "server/means/product/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    /**
     * 导入资产数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "server/means/product/saveSheetData")
    Boolean saveSheetData(AssetImportDto importDto);

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "server/means/product/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    /**
     * 查询导入错误数据
     *
     * @return 错误数据
     */
    @GetMapping(value = "server/means/product/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @PostMapping("server/means/product/images/check/{action}")
    List<ImportImages> productImagesCheck(@RequestBody List<ImportImages> codes, @PathVariable("action") Integer action, @RequestHeader("Authorization") String token);

    @PostMapping("server/means/product/images/import/{action}")
    List<ImportImages> productImagesModify(@RequestBody List<ImportImages> images, @PathVariable("action") Integer action, @RequestHeader("Authorization") String token);
}
