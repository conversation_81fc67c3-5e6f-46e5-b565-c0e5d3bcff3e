package com.niimbot.asset.service.feign.report;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.report.MaterialCkManifestDto;
import com.niimbot.report.MaterialCkManifestQueryDto;
import com.niimbot.report.MaterialCkStatisticsDto;
import com.niimbot.report.MaterialCkStatisticsQueryDto;
import com.niimbot.report.MaterialManifestTotalDto;
import com.niimbot.report.MaterialRkManifestDto;
import com.niimbot.report.MaterialRkManifestQueryDto;
import com.niimbot.report.MaterialRkStatisticsDto;
import com.niimbot.report.MaterialRkStatisticsQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialPredefinedReportFeignClient {

    @PostMapping("server/report/predefined/material/rk/manifest")
    PageUtils<MaterialRkManifestDto> materialRkManifest(MaterialRkManifestQueryDto queryDto);

    @PostMapping("server/report/predefined/material/rk/manifest/total")
    MaterialManifestTotalDto materialRkManifestTotal(MaterialRkManifestQueryDto queryDto);

    @PostMapping("server/report/predefined/material/ck/manifest")
    PageUtils<MaterialCkManifestDto> materialCkManifest(MaterialCkManifestQueryDto queryDto);

    @PostMapping("server/report/predefined/material/ck/manifest/total")
    MaterialManifestTotalDto materialCkManifestTotal(MaterialCkManifestQueryDto queryDto);

    @PostMapping("server/report/predefined/material/rk/statistics")
    List<MaterialRkStatisticsDto> materialRkStatistics(MaterialRkStatisticsQueryDto queryDto);

    @PostMapping("server/report/predefined/material/ck/statistics")
    List<MaterialCkStatisticsDto> materialCkStatistics(MaterialCkStatisticsQueryDto queryDto);
}
