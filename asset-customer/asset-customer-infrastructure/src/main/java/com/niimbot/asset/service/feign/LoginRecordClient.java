package com.niimbot.asset.service.feign;

import com.niimbot.system.UserLoginRecordDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 登录记录feign客户端
 *
 * <AUTHOR>
 * @Date 2021/03/31
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface LoginRecordClient {

    /**
     * 添加登录记录
     *
     * @param userLoginRecordDto 登录记录对象
     * @return Boolean
     */
    @PostMapping(value = "server/system/loginRecord")
    Boolean saveLoginRecord(UserLoginRecordDto userLoginRecordDto);

    @GetMapping("server/system/loginRecord/isFirstLogin")
    Boolean isFirstLogin();

}
