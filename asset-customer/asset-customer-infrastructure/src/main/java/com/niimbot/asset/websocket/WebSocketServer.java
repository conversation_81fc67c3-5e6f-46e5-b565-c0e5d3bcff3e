package com.niimbot.asset.websocket;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.websocket.codec.MessageTextDecoder;
import com.niimbot.asset.websocket.codec.MessageTextEncoder;
import com.niimbot.asset.websocket.handler.MessageHandlerContext;
import com.niimbot.asset.websocket.msg.ConnectMessage;
import com.niimbot.asset.websocket.msg.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.naming.AuthenticationException;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * websocket站点
 *
 * <AUTHOR>
 * @date 2021/9/1 10:10
 */
@Slf4j
@Component
@ServerEndpoint(
        value = "/api/webSocket/{token}",
        encoders = {MessageTextEncoder.class},
        decoders = {MessageTextDecoder.class})
public class WebSocketServer {
    /**
     * UserId -> WebSocketServer  Map
     */
    private static final ConcurrentHashMap<String, WebSocketServer> WEB_SOCKET_MAP = new ConcurrentHashMap<>();

    private static final ConcurrentHashMap<Long, List<String>> USER_SESSION_MAP = new ConcurrentHashMap<>();

    /**
     * 企业下UserId列表
     */
    private static final ConcurrentHashMap<Long, List<String>> COMPANY_SESSION_MAP = new ConcurrentHashMap<>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    /**
     * 接收 userId
     */
    private Long userId = null;
    /**
     * 接收 companyId
     */
    private Long companyId = null;
    /**
     * 最后心跳包接收时间
     */
    private Long lastHeart = null;

    private static AbstractTokenService tokenService;

    private static MessageHandlerContext handlerContext;

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token)
            throws AuthenticationException {
        LoginUserDto loginUser = resolveUser(token);
        this.session = session;
        this.userId = loginUser.getCusUser().getId();
        this.companyId = loginUser.getCusUser().getCompanyId();
        this.lastHeart = System.currentTimeMillis();

        if (log.isDebugEnabled()) {
            log.debug("WebSocket -> 用户: {} 打开连接", userId);
        }

        WEB_SOCKET_MAP.put(session.getId(), this);
        List<String> userSessions = USER_SESSION_MAP.computeIfAbsent(this.userId, k -> new ArrayList<>());
        userSessions.add(session.getId());

        List<String> companySessions = COMPANY_SESSION_MAP.computeIfAbsent(this.companyId, k -> new ArrayList<>());
        companySessions.add(session.getId());

        try {
            ConnectMessage connect = new ConnectMessage();
            connect.setIsSuccess(true);
            connect.setMsg("连接成功");
            doSendMessage(connect);
            WebSocketHeartChecker.start();
        } catch (IOException | EncodeException e) {
            log.warn("WebSocket -> 用户: {} 网络异常", userId);
        }
    }

    private LoginUserDto resolveUser(String token) throws AuthenticationException {
        LoginUserDto loginUser = tokenService.getLoginUserByToken(token);
        if (loginUser != null) {
            return loginUser;
        }
        throw new AuthenticationException("WebSocket连接异常, 用户鉴权失败");
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (Objects.isNull(session)) {
            log.info("webSocket session is null");
            return;
        }
        if (StrUtil.isBlank(session.getId())) {
            log.info("webSocket session id is null");
            return;
        }
        WEB_SOCKET_MAP.remove(session.getId());
        if (COMPANY_SESSION_MAP.containsKey(companyId)) {
            List<String> sessionsIds = COMPANY_SESSION_MAP.get(companyId);
            sessionsIds.remove(session.getId());
            if (sessionsIds.size() == 0) {
                COMPANY_SESSION_MAP.remove(companyId);
            }
        }
        if (USER_SESSION_MAP.containsKey(userId)) {
            List<String> sessionsIds = USER_SESSION_MAP.get(userId);
            sessionsIds.remove(session.getId());
            if (sessionsIds.size() == 0) {
                USER_SESSION_MAP.remove(userId);
            }
        }
        try {
            if (log.isDebugEnabled()) {
                log.debug("WebSocket -> 用户: {} 关闭连接", this.userId);
            }
            if (this.session.isOpen()) {
                this.session.close();
            }
        } catch (IOException e) {
            log.error("WebSocket -> 关闭连接", e);
        }
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param session
     * @param message
     */
    @OnMessage
    public void onMessage(Session session, Message message) {
        if (log.isDebugEnabled()) {
            log.debug("WebSocket -> 接受用户: {}, 消息: {}", this.userId, message);
        }
        try {
            handlerContext.handle(session, this.userId, this.companyId, message);
        } catch (IOException | EncodeException e) {
            log.error("WebSocket -> 处理用户: {} 消息 {} 异常", this.userId, message);
        }
    }

    /**
     * 发生错误
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.warn("WebSocket -> 用户: {}, 错误信息: {}", this.userId, error.getMessage(), error);
    }

    public static void sendMessage(@PathParam("sessionId") String sessionId, Message message) throws IOException, EncodeException {
        if (!WEB_SOCKET_MAP.containsKey(sessionId)) {
            log.warn("WebSocket -> sessionId : {} not exist", sessionId);
            return;
        }
        WEB_SOCKET_MAP.get(sessionId).doSendMessage(message);
    }

    /**
     * 消息推送
     */
    public static void sendMessage(@PathParam("userId") Long userId, Message message) throws IOException, EncodeException {
        if (log.isDebugEnabled()) {
            log.debug("WebSocket -> 用户: {}, 推送消息: {}", userId, message);
        }
        if (!USER_SESSION_MAP.containsKey(userId)) {
            return;
        }
        List<String> sessionIds = USER_SESSION_MAP.get(userId);
        for (String sessionId : sessionIds) {
            sendMessage(sessionId, message);
        }
    }

    /**
     * 消息推送
     */
    public static void sendMessage(@PathParam("userId") List<Long> userIds, Message message) throws IOException, EncodeException {
        if (log.isDebugEnabled()) {
            log.debug("WebSocket -> 用户: {}, 推送消息: {}", userIds, message);
        }
        if (CollUtil.isEmpty(userIds)) {
            log.debug("WebSocket -> userIds is empty");
            return;
        }
        for (Long id : userIds) {
            sendMessage(id, message);
        }
    }

    /**
     * 企业消息推送
     */
    public static void sendCompanyMessage(@PathParam("companyId") Long companyId, Message message) throws IOException, EncodeException {
        if (log.isDebugEnabled()) {
            log.debug("WebSocket -> 向公司: {}, 推送消息: {}", companyId, message);
        }
        if (COMPANY_SESSION_MAP.containsKey(companyId)) {
            List<String> sessionsIds = COMPANY_SESSION_MAP.getOrDefault(companyId, new ArrayList<>());
            for (String sessionId : sessionsIds) {
                sendMessage(sessionId, message);
            }
        }
    }

    public static void reHeart(@PathParam("sessionId") String sessionId) {
        Optional.ofNullable(WEB_SOCKET_MAP.get(sessionId))
                .ifPresent(WebSocketServer::doReHeart);
    }

    public static ConcurrentHashMap<String, WebSocketServer> getCollections() {
        return WEB_SOCKET_MAP;
    }

    public Long getLastHeart() {
        return this.lastHeart;
    }

    public Long getUserId() {
        return this.userId;
    }

    private void doReHeart() {
        this.lastHeart = System.currentTimeMillis();
    }

    /**
     * 消息推送
     */
    private void doSendMessage(Message message) throws IOException, EncodeException {
        this.session.getBasicRemote().sendObject(message);
    }

    public static void setTokenService(AbstractTokenService tokenService) {
        WebSocketServer.tokenService = tokenService;
    }

    public static void setHandlerContext(MessageHandlerContext handlerContext) {
        WebSocketServer.handlerContext = handlerContext;
    }
}
