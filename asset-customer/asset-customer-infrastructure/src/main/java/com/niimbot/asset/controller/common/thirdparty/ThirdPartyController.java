package com.niimbot.asset.controller.common.thirdparty;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.service.feign.ThirdpartyFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.thirdparty.AssetThirdPartyEmpMapping;
import com.niimbot.thirdparty.AssetThirdPartyOrgMapping;
import com.niimbot.thirdparty.BindThirdPartyResult;
import com.niimbot.thirdparty.DingtalkBindDto;
import com.niimbot.thirdparty.DingtalkReplenish;
import com.niimbot.thirdparty.FeishuBindDto;
import com.niimbot.thirdparty.FeishuJsAuth;
import com.niimbot.thirdparty.FeishuJsTicketDto;
import com.niimbot.thirdparty.ThirdInitSyncRequest;
import com.niimbot.thirdparty.ThirdPartyDto;
import com.niimbot.thirdparty.WeChatBindDto;
import com.niimbot.thirdparty.WechatJsAuth;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import feign.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @since 2021/11/9 16:52
 */
@Api(tags = "【三方对接】组织同步对接")
@ResultController
@RequestMapping("api/common/thirdparty")
public class ThirdPartyController {

    private final ThirdpartyFeignClient thirdpartyFeignClient;

    @Autowired
    public ThirdPartyController(ThirdpartyFeignClient thirdpartyFeignClient) {
        this.thirdpartyFeignClient = thirdpartyFeignClient;
    }

    @ApiOperation(value = "初始同步组织架构第一步：【钉钉】绑定钉钉")
    @RepeatSubmit
    @PostMapping("/bind/dingtalk")
    public BindThirdPartyResult bingDingtalk(@RequestBody @Validated DingtalkBindDto dingtalk) {
        return thirdpartyFeignClient.bindDingtalk(dingtalk);
    }

    @ApiOperation("补充钉钉agentId与corpId")
    @RepeatSubmit
    @PostMapping("/replenish/dingtalk")
    public Boolean bindDingtalkAgentId(@RequestBody @Validated DingtalkReplenish dto) {
        return thirdpartyFeignClient.replenishDingtalk(dto);
    }

    @ApiOperation(value = "查询组织架构绑定数据")
    @GetMapping("/bind/info")
    public ThirdPartyDto getBindInfo(@RequestParam(value = "type", required = false) String type) {
        return thirdpartyFeignClient.getBindInfo(type);
    }

    @ApiOperation(value = "【钉钉】解除绑定")
    @ResultMessage("解绑成功")
    @DeleteMapping("/bind/dingtalk")
    public Boolean removeDingtalk() {
        return thirdpartyFeignClient.removeDingtalk();
    }

    @ApiOperation(value = "初始同步组织架构第一步：【微信】绑定微信")
    @RepeatSubmit
    @PostMapping("/bind/wechat")
    public BindThirdPartyResult bingDingtalk(@RequestBody @Validated WeChatBindDto wechat) {
        return thirdpartyFeignClient.bindWeChat(wechat);
    }

    @ApiOperation(value = "【微信】解除绑定")
    @ResultMessage("解绑成功")
    @DeleteMapping("/bind/wechat")
    public Boolean removeWeChat() {
        return thirdpartyFeignClient.removeWeChat();
    }

    @ApiOperation(value = "初始同步组织架构第一步：【飞书】绑定飞书")
    @RepeatSubmit
    @PostMapping("/bind/feishu")
    public BindThirdPartyResult bingFeishu(@RequestBody @Validated FeishuBindDto feishu) {
        return thirdpartyFeignClient.bingFeishu(feishu);
    }

    @ApiOperation(value = "【飞书】解除绑定")
    @ResultMessage("解绑成功")
    @DeleteMapping("/bind/feishu")
    public Boolean removeFeishu() {
        return thirdpartyFeignClient.removeFeishu();
    }

    @ApiOperation("初始同步组织架构第二步：获取组织架构映射关系")
    @RepeatSubmit
    @PostMapping("/org/getMapping")
    public AssetThirdPartyOrgMapping getOrgMapping(@RequestBody ThirdInitSyncRequest request) {
        return thirdpartyFeignClient.getOrgMapping(
                new Request.Options(15L, TimeUnit.MINUTES, 15L, TimeUnit.MINUTES, true),
                request.getType()
        );
    }

    @ApiOperation("初始同步组织架构第三步：处理组织架构映射关系")
    @RepeatSubmit
    @PostMapping("/org/handleMapping")
    public Boolean handleOrgMapping(@RequestBody ThirdInitSyncRequest request) {
        return thirdpartyFeignClient.handleOrgMapping(
                new Request.Options(15L, TimeUnit.MINUTES, 15L, TimeUnit.MINUTES, true),
                request.getType(),
                request.getMappings()
        );
    }

    @ApiOperation("初始同步组织架构第四步：获取员工映射关系")
    @RepeatSubmit
    @PostMapping("/emp/getMapping")
    public AssetThirdPartyEmpMapping getEmpMapping(@RequestBody ThirdInitSyncRequest request) {
        return thirdpartyFeignClient.getEmpMapping(
                new Request.Options(15L, TimeUnit.MINUTES, 15L, TimeUnit.MINUTES, true),
                request.getType()
        );
    }

    @ApiOperation("初始同步组织架构第五步：处理数据保存")
    @RepeatSubmit
    @PostMapping("/handle")
    @ResultMessage("同步成功")
    public Boolean handle(@RequestBody ThirdInitSyncRequest request) {
        return thirdpartyFeignClient.handle(
                new Request.Options(15L, TimeUnit.MINUTES, 15L, TimeUnit.MINUTES, true),
                request.getType(),
                request.getTransMismatchEmps()
        );
    }

    @ApiOperation("查看当前初始同步组织架构已处理的步骤")
    @RepeatSubmit
    @GetMapping("/syncStep/{type}")
    public Map<String, Object> process(@PathVariable("type") String type) {
        return thirdpartyFeignClient.process(type);
    }

    @ApiOperation("初始同步完成后手动同步")
    @RepeatSubmit
    @PostMapping("/overallHandle")
    @ResultMessage("信息同步中！您可以去做业务操作，您可稍后来查看最新组织信息。")
    public Boolean overallHandle(@RequestParam("type") String type) {
        return thirdpartyFeignClient.overallHandle(type);
    }

    @ApiOperation("清除同步过程中的缓存数据")
    @DeleteMapping("/syncStep/clean")
    @RepeatSubmit
    @ResultMessage("清除成功")
    public Boolean cleanStepCache(@RequestParam("type") String type, @RequestParam(value = "companyId", required = false) Long companyId) {
        return thirdpartyFeignClient.cleanSyncStepCache(type, companyId);
    }

    @ApiOperation("获取企业微信js签名")
    @PostMapping("/wechat/getSignature")
    public Map<String, String> getWechatJsSignature(@RequestBody @Validated WechatJsAuth wechatJsAuth) {
        return thirdpartyFeignClient.getWechatJsSignature(wechatJsAuth);
    }

    @ApiOperation("获取飞书js签名")
    @PostMapping("/feishu/getSignature")
    public FeishuJsTicketDto getFeishuJsSignature(@RequestBody @Validated FeishuJsAuth feishuJsAuth) {
        return thirdpartyFeignClient.getFeishuJsSignature(feishuJsAuth);
    }

}
