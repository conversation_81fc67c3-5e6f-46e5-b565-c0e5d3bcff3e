package com.niimbot.asset.controller.pc.report;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.PredefinedReportExportService;
import com.niimbot.asset.service.PredefinedReportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.report.PredefinedReportFeignClient;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.GroupReportResult;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.MaterialCategoryReportSearch;
import com.niimbot.report.MaterialRepositoryReportSearch;
import com.niimbot.report.MeansCategoryReportSearch;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.UseOrgGroupReportSearch;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Api(tags = "【报表】预定义报表")
@ResultController
@RequestMapping("api/pc/report/predefined")
@RequiredArgsConstructor
public class PredefinedReportController {

    private final PredefinedReportFeignClient predefinedReportFeignClient;
    private final FormFeignClient formFeignClient;
    private final PredefinedReportService predefinedReportService;
    private final PredefinedReportExportService predefinedReportExportService;

    @ApiOperation("资产部门增减报表")
    @PostMapping("/means/useOrgFixedHeadReport")
    public GroupReportResult meansUseOrgFixedHeadReport(@RequestBody UseOrgGroupReportSearch search) {
        return predefinedReportFeignClient.meansUseOrgFixedHeadReport(search);
    }

    @ApiOperation("资产分类增减报表")
    @PostMapping("/means/categoryFixedHeadReport")
    public GroupReportResult meansCategoryFixedHead(@RequestBody MeansCategoryReportSearch search) {
        return predefinedReportFeignClient.meansCategoryFixedHeadReport(search);
    }

    @ApiOperation("耗材分类增减报表")
    @PostMapping("/material/categoryFixedHeadReport")
    public GroupReportResult materialCategoryFixedHeadReport(@RequestBody MaterialCategoryReportSearch search) {
        return predefinedReportFeignClient.materialCategoryFixedHeadReport(search);
    }

    @ApiOperation(value = "耗材分类统计表导出")
    @PostMapping("/material/categoryFixedHeadReport/export")
    public ExportResponse materialCategoryExport(@RequestBody MaterialCategoryReportSearch search) {
        return predefinedReportExportService.exportMaterialCategoryReport(search);
    }

    @ApiOperation("耗材仓库增减报表")
    @PostMapping("/material/repositoryFixedHeadReport")
    public GroupReportResult materialRepositoryFixedHeadReport(@RequestBody MaterialRepositoryReportSearch search) {
        return predefinedReportFeignClient.materialRepositoryFixedHeadReport(search);
    }

    @ApiOperation(value = "耗材出入库统计表导出")
    @PostMapping("/material/repositoryFixedHeadReport/export")
    public ExportResponse materialRepositoryExport(@RequestBody MaterialRepositoryReportSearch search) {
        return predefinedReportExportService.exportMaterialRepositoryReport(search);
    }

    @ApiOperation(value = "资产履历报表")
    @PostMapping("/means/assetLog")
    public PageUtils<JSONObject> assetLogReport(@RequestBody AssetLogReportQueryDto queryDto) {
        return predefinedReportService.assetLogReport(queryDto);
    }

    @ApiOperation(value = "资产履历报表导出")
    @PostMapping("/means/assetLog/export")
    public ExportResponse assetLogReportExport(@RequestBody @Validated AssetLogReportQueryDto queryDto) {
        return predefinedReportExportService.exportAssetLogReport(queryDto);
    }

    @ApiOperation(value = "待处置资产清单")
    @PostMapping("/means/waitHandleAssetLog")
    public PageUtils<JSONObject> waitHandleAssetLog(@RequestBody WaitHandleAssetLogQueryDto queryDto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return predefinedReportService.waitHandleAssetLog(queryDto, formVO);
    }

    @ApiOperation(value = "待处置资产清单导出")
    @PostMapping("/means/waitHandleAssetLog/export")
    public ExportResponse waitHandleAssetLogExport(@RequestBody WaitHandleAssetLogQueryDto queryDto) {
        return predefinedReportExportService.exportHandleAssetReport(queryDto, OrderTypeNewEnum.WAIT_HANDLE_ASSET_LOG_REPORT);
    }

    @ApiOperation(value = "已处置资产清单")
    @PostMapping("/means/handleAssetLog")
    public PageUtils<JSONObject> handleAssetLog(@RequestBody HandleAssetLogQueryDto queryDto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return predefinedReportService.handleAssetLog(queryDto, formVO);
    }

    @ApiOperation(value = "已处置资产清单导出")
    @PostMapping("/means/handleAssetLog/export")
    public ExportResponse handleAssetLogExport(@RequestBody HandleAssetLogQueryDto queryDto) {
        return predefinedReportExportService.exportHandleAssetReport(queryDto, OrderTypeNewEnum.HANDLE_ASSET_LOG_REPORT);
    }

    @ApiOperation(value = "待归还资产清单")
    @PostMapping("/means/waitReturnAssetLog")
    public PageUtils<JSONObject> waitReturnAssetLog(@RequestBody WaitReturnAssetLogQueryDto queryDto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return predefinedReportService.waitReturnAssetLog(queryDto, formVO);
    }

    @ApiOperation(value = "待归还资产清单导出")
    @PostMapping("/means/waitReturnAssetLog/export")
    public ExportResponse waitReturnAssetLogExport(@RequestBody WaitReturnAssetLogQueryDto queryDto) {
        return predefinedReportExportService.exportHandleAssetReport(queryDto, OrderTypeNewEnum.WAIT_RETURN_ASSET_LOG_REPORT);
    }

    @ApiOperation(value = "维修资产报表")
    @PostMapping("/means/repairAssetLog")
    public PageUtils<JSONObject> repairAssetLog(@RequestBody RepairAssetLogQueryDto queryDto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return predefinedReportService.repairAssetLog(queryDto, formVO);
    }

    @ApiOperation(value = "维修资产报表导出")
    @PostMapping("/means/repairAssetLog/export")
    public ExportResponse repairAssetLogExport(@RequestBody RepairAssetLogQueryDto queryDto) {
        return predefinedReportExportService.exportHandleAssetReport(queryDto, OrderTypeNewEnum.REPAIR_ASSET_LOG_REPORT);
    }

    @ApiOperation(value = "资产维修记录报表")
    @PostMapping("/means/repairAssetRecord")
    public PageUtils<JSONObject> repairAssetRecord(@RequestBody RepairAssetRecordQueryDto queryDto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return predefinedReportService.repairAssetRecord(queryDto, formVO);
    }

    @ApiOperation(value = "资产维修记录报表导出")
    @PostMapping("/means/repairAssetRecord/export")
    public ExportResponse repairAssetRecordExport(@RequestBody RepairAssetRecordQueryDto queryDto) {
        return predefinedReportExportService.exportHandleAssetReport(queryDto, OrderTypeNewEnum.REPAIR_ASSET_RECORD_REPORT);
    }

}
