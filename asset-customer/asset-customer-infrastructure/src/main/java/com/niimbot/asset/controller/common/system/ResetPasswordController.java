package com.niimbot.asset.controller.common.system;

import cn.hutool.core.convert.Convert;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.ResetPasswordDto;
import com.niimbot.validate.NationalCodeValidate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Objects;

/**
 * 忘记密码控制器
 *
 * <AUTHOR>
 * @Date 2020/11/16
 */
@Api(tags = {"忘记密码修改接口"})
@ResultController
@RequestMapping("api/common/password")
@Slf4j
@Validated
public class ResetPasswordController {

    @Resource
    private CusUserFeignClient userFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private CusEmployeeFeignClient employeeFeignClient;

    @Resource
    private RedisService redisService;

    @ApiOperation(value = "修改密码")
    @PutMapping
    @ResultMessage(ResultConstant.RESET_PASSWORD_SUCCESS)
    public Boolean resetPassword(@RequestBody
                                 @Validated(value = {ResetPasswordDto.GroupSmsCodeReset.class})
                                         ResetPasswordDto dto) {
        NationalCodeValidate.checkCNMobile(dto.getNationalCode(), dto.getMobile());
        // 验证验证码
        if (!smsCodeFeignClient.checkSmsCode(dto.getMobile(), dto.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        // 确认密码
        String password = SecurityUtils.decryptPassword(dto.getPassword());
        String rePassword = SecurityUtils.decryptPassword(dto.getRePassword());
        if (!Objects.equals(password, rePassword)) {
            throw new BusinessException(SystemResultCode.USER_PASSWORD_CONFIRM_ERROR);
        }
//        ValidationUtils.checkPassword(SystemResultCode.USER_PASSWORD_ERROR, password);
        dto.setPassword(password);
        // 修改密码、修改sso密码
        userFeignClient.reSetPassword(dto);
        // [bug ID1028946] 重置密码后、账号锁定状态
        redisService.del(BaseConstant.LOGIN_USER_ERROR_COUNT + dto.getMobile());
        return true;
    }

    /**
     * 校验手机号
     *
     * @param mobile  手机号
     * @param smsCode 验证码
     * @return 校验手机号是否正确
     */
    @ApiOperation(value = "【忘记密码、第一步】、校验手机号")
    @GetMapping("/verifyMobile")
    public Boolean verifyMobile(@NotBlank(message = "请输入手机号")
                                    @Size(max = 11, message = "手机号最多11位") String mobile,
                                @NotBlank(message = "请输入区号") String nationalCode,
                                @Size(min = 4, max = 4, message = "验证码为4位")
                                @NotBlank(message = "验证码不能为空") String smsCode) {
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        if (!smsCodeFeignClient.checkSmsCode(mobile, smsCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        CusUserDto byMobile = userFeignClient.getByMobile(mobile);
        if (null == byMobile) {
            throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        }
        return true;
    }

    /**
     * 校验手机号
     *
     * @param email     手机号
     * @param emailCode 验证码
     * @return 校验手机号是否正确
     */
    @ApiOperation(value = "【忘记密码、第一步】、校验邮箱、验证码")
    @GetMapping("/verifyEmail")
    public Boolean verifyEmail(@NotBlank(message = "请输入邮箱")
                                   @Pattern(regexp = com.niimbot.validate.ValidationUtils.EMAIL_REG, message = "邮箱格式不正确")
                                   @Size(max = 255, message = "邮箱不得超过255个字符") String email,
                               @Size(min = 4, max = 4, message = "验证码为4位")
                                   @NotBlank(message = "验证码不能为空") String emailCode,
                               @ApiIgnore LoginUserDto userDto) {
        // 校验邮箱
        CusEmployeeDto info = employeeFeignClient.getInfo(userDto.getCusUser().getId());
        if (StringUtils.equals(email, info.getEmail())) {
            throw new BusinessException(SystemResultCode.USER_EMAIL_NOT_SET_ERROR);
        }
        Object code = redisService.get(BaseConstant.EMAIL_CODE_KEY + email);
        if (Objects.isNull(code)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_EXPIRE);
        }
        if (!StringUtils.equals(Convert.toStr(code), emailCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        redisService.del(BaseConstant.EMAIL_CODE_KEY + email);
        return true;
    }
}
