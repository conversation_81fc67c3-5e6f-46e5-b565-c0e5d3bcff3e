package com.niimbot.asset.service.feign;

import com.niimbot.purchase.PurchaseOrderTypeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:22
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PurchaseOrderTypeFeignClient {

    /**
     * 单据类型详情
     * @param id
     * @return
     */
    @GetMapping(value = "server/purchase/orderType/getById/{id}")
    PurchaseOrderTypeDto getOrderTypeById(@PathVariable("id") Long id);

    /**
     * 更新单据类型
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "server/purchase/orderType")
    Boolean updateOrderType(PurchaseOrderTypeDto dto);

    /**
     * 单据类型查询
     * @return
     */
    @GetMapping(value = "server/purchase/orderType/list")
    List<PurchaseOrderTypeDto> listOrderType();

    /**
     * 此类型是否开启审批流
     *
     * @param orderType 单据类型
     * @return 是否开启审批流
     */
    @GetMapping(value = "server/purchase/orderType/enableWorkflow/{orderType}")
    Boolean enableWorkflow(@PathVariable("orderType") Integer orderType);
}
