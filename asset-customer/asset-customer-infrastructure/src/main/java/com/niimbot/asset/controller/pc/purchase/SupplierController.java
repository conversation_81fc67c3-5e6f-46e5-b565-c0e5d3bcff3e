package com.niimbot.asset.controller.pc.purchase;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.SupplierFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.purchase.SupplierDto;
import com.niimbot.purchase.SupplierPageQueryDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * 供应商前端控制器
 *
 * <AUTHOR>
 * @date 2021/5/18 09:33
 */
@Api(tags = "供应商管理")
@ResultController
@RequestMapping("api/pc/purchase/supplier")
@RequiredArgsConstructor
public class SupplierController {
    private final SupplierFeignClient supplierFeignClient;

    @ApiOperation(value = "新增")
    @PostMapping
    @RepeatSubmit
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public SupplierDto save(@RequestBody @Validated(value = {SupplierDto.Save.class}) SupplierDto dto) {
        return new SupplierDto().setId(supplierFeignClient.save(dto));
    }

    @ApiOperation(value = "编辑")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean update(@RequestBody @Validated(value = {SupplierDto.Update.class}) SupplierDto dto) {
        return supplierFeignClient.update(dto);
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("id") Long id) {
        return supplierFeignClient.delete(id);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/{id}")
    public SupplierDto getById(@PathVariable("id") Long id) {
        return supplierFeignClient.getById(id);
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public PageUtils<SupplierDto> page(SupplierPageQueryDto query) {
        return supplierFeignClient.page(query);
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public List<SupplierDto> list(@RequestParam(value = "name", required = false) String name) {
        return supplierFeignClient.list(name);
    }

    @ApiOperation(value = "通过Ids查询资组织数据")
    @PostMapping("/list/ids")
    public List<Map<String, ?>> listByIds(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ListUtil.empty();
        }
        return supplierFeignClient.listByIds(ids).stream().map(item ->
                ImmutableMap.of("id", item.getId(), "name", item.getName())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "列表查询，资产录入动态属性")
    @GetMapping("/dict")
    public List<Map<String, ?>> dict(@RequestParam(value = "kw", required = false) String kw) {
        List<SupplierDto> list = supplierFeignClient.list(kw);
        return list.stream().map(supplier -> ImmutableMap.of("value", supplier.getId(), "label", supplier.getName()))
                .collect(Collectors.toList());
    }
}
