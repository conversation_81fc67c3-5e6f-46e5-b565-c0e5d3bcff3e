package com.niimbot.asset.websocket.msg;

import cn.hutool.core.collection.CollUtil;
import com.niimbot.means.ImportImages;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "ImportImagesMessage", description = "图片导入消息")
public class ImportImagesMessage extends Message {

    @ApiModelProperty("导入图片类型：1-资产；2-耗材；3-产品")
    private Integer importType;

    @ApiModelProperty("动作：1-新增；2-更新")
    private Integer action;

    @ApiModelProperty("数据唯一编码")
    private List<ImportImages> checks = new ArrayList<>();

    @ApiModelProperty("错误结果集")
    private List<ImportImages> error;

    @ApiModelProperty("允许保存的")
    private List<ImportImages> save = new ArrayList<>();

    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty("步骤")
    private Integer step;

    @ApiModelProperty("导入成功数量")
    private Integer successCount = 0;

    public ImportImagesMessage() {
        super(Message.TYPE_IMPORT_IMAGES);
    }

    public void clean() {
        if (CollUtil.isNotEmpty(this.save)) {
            this.save.forEach(v -> v.setUrl(null).setCode(null));
        }
        this.token = null;
    }
}
