package com.niimbot.asset.controller.pc.system;

import cn.hutool.core.collection.CollUtil;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.PrintTaskQueryDto;
import com.niimbot.means.UserPrintTaskViewDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.ImportTaskPageQueryDto;
import com.niimbot.system.TaskAmountDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/7 9:03
 */
@Api(tags = "导入管理")
@ResultController
@RequestMapping("api/pc/import")
public class ImportController {

    private final ImportTaskFeignClient importTaskFeignClient;
    private final PrintFeignClient printFeignClient;

    @Autowired
    public ImportController(ImportTaskFeignClient importTaskFeignClient,
                            PrintFeignClient printFeignClient) {
        this.importTaskFeignClient = importTaskFeignClient;
        this.printFeignClient = printFeignClient;
    }

    @ApiOperation(value = "【PC】查询导入任务列表")
    @GetMapping(value = "/task")
    @AutoConvert
    public PageUtils<ImportTaskDto> importTask(@Validated ImportTaskPageQueryDto queryDto) {
        PageUtils<ImportTaskDto> page = importTaskFeignClient.page(queryDto);
        // 由于现在每个单据都有两种导出, 现在导出接口地址在创建导出任务的时候写入
//        page.getList().forEach(val -> {
//            val.setTaskStatusText(getTaskStatusText(val));
//            if (val.getType().equals(DictConstant.TASK_TYPE_EXPORT)) {
//                val.setExportUrl(OrderTypeNewEnum.getByType(val.getImportType()).getExportUrl());
//            }
//        });
        return page;
    }

    @ApiOperation(value = "【PC】清空导入任务")
    @DeleteMapping(value = "/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("id") Long id) {
        return importTaskFeignClient.delete(id);
    }

    @ApiOperation(value = "【PC】清空导入任务")
    @DeleteMapping(value = "/all/{type}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean deleteAll(@PathVariable("type") Integer type) {
        return importTaskFeignClient.deleteAll(type);
    }

    @ApiOperation(value = "【PC】查询导入任务列表")
    @GetMapping(value = "/task/amount")
    @AutoConvert
    public TaskAmountDto taskAmount() {
        PrintTaskQueryDto taskQueryDto = new PrintTaskQueryDto();
        taskQueryDto.setPageNum(1);
        taskQueryDto.setPageSize(1000);
        taskQueryDto.setIsGoing(true);
        List<UserPrintTaskViewDto> list = printFeignClient.listTaskPc(taskQueryDto);
        list.forEach(viewDto -> viewDto.convert(taskQueryDto.getIsGoing()));
        return new TaskAmountDto()
                .setImportAmount(importTaskFeignClient.amount(DictConstant.TASK_TYPE_IMPORT))
                .setExportAmount(importTaskFeignClient.amount(DictConstant.TASK_TYPE_EXPORT))
                .setPrintAmount(CollUtil.isNotEmpty(list) ? list.size() : 0);
    }

}
