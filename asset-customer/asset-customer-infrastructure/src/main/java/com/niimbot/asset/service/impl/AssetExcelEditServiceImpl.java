package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.annotation.ExcelField;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.model.AssetExcelTplEnum;
import com.niimbot.asset.service.AbstractAssetService;
import com.niimbot.asset.service.AssetExcelEditService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormPropsCO;
import com.niimbot.easydesign.form.dto.formprops.EditFieldRules;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AssetImportDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableImportResult;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.ImportTaskExtInfoDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @since 2022/8/26 15:04
 */
@Slf4j
@Service
@Primary
public class AssetExcelEditServiceImpl extends AbstractAssetService implements AssetExcelEditService {

    private static ThreadLocal<ImportInfo> globalCache = new TransmittableThreadLocal<>();

    private final RedisService redisService;
    private final ImportService importService;
    private final ImportTaskFeignClient importTaskFeignClient;

    @Autowired
    public AssetExcelEditServiceImpl(RedisService redisService,
                                     ImportService importService,
                                     ImportTaskFeignClient importTaskFeignClient) {
        this.redisService = redisService;
        this.importService = importService;
        this.importTaskFeignClient = importTaskFeignClient;
    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setFileName(fileName);
        importInfo.setFileSize(fileSize);
        importInfo.setCompanyId(companyId);

        // 解析的模板导入
        ExcelReader reader = ExcelUtil.getReader(stream);
        List<List<Object>> read = reader.read(1);
        if (CollUtil.isEmpty(read)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
        }
        // =============================  读取Excel表头并校验 ================================
        List<Object> header = ExcelUtils.clearEmpty(read.get(0));
        if (CollUtil.isEmpty(header)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到数据");
        }

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject formProps = formVO.getFormProps();
        List<String> editCodes = new ArrayList<>();
        if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
            editCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
                    .toJavaList(EditFieldRules.class)
                    .stream()
                    .filter(editFieldRules -> editFieldRules.getValue() == 1)
                    .map(EditFieldRules::getFieldCode)
                    .collect(toList());
        }

        // 可编辑的属性
        FormFieldCO assetSerialNoCode = new FormFieldCO();
        List<FormFieldCO> formFields = new ArrayList<>();
        List<String> allFieldName = new ArrayList<>();
        for (FormFieldCO f : formVO.getFormFields()) {
            allFieldName.add(f.getFieldName());
            if (FormFieldCO.YZC_ASSET_SERIALNO.equals(f.getFieldType())) {
                formFields.add(f);
                assetSerialNoCode = f;
            }
            if (editCodes.contains(f.getFieldCode())
                    && !f.isHidden()
                    && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                    .contains(f.getFieldType())) {
                formFields.add(f);
            }
        }

        if (CollUtil.isEmpty(formFields)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "未配置可编辑字段");
        }
        Map<String, List<FormFieldCO>> assetAttrMap = formFields.stream()
                .collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 表头字段映射
        formFieldMap.set(MapUtil.of("asset", new ArrayList<>()));
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        List<String> noUpdate = new ArrayList<>();
        for (int i = 0; i < header.size(); i++) {
            String headName = Convert.toStr(header.get(i), "").trim();
            if (StrUtil.isBlank(Convert.toStr(headName))) {
                continue;
            }
            if (assetAttrMap.containsKey(headName)) {
                List<FormFieldCO> formFieldCOList = assetAttrMap.get(headName);
                if (CollUtil.isNotEmpty(formFieldCOList)) {
                    FormFieldCO formFieldCO = formFieldCOList.get(0);
                    headerMapping.put(i, formFieldCO);
                    formFieldCOList.remove(0);
                    assetAttrMap.put(headName, formFieldCOList);
                    formFieldMap.get().get("asset").add(formFieldCO);
                }
            } else if (allFieldName.contains(headName)) {
                noUpdate.add(headName);
            }
        }
        if (CollUtil.isNotEmpty(noUpdate)) {
            String join = String.join("，", noUpdate);
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "当前表格的以下字段不支持更新：" + join);
        }

        if (CollUtil.isEmpty(headerMapping)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到可编辑属性");
        }
        FormFieldCO finalAssetSerialNoCode = assetSerialNoCode;
        boolean find = headerMapping.values().stream().anyMatch(f -> f.getFieldCode().equals(finalAssetSerialNoCode.getFieldCode()));
        if (!find) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到必填字段" + finalAssetSerialNoCode.getFieldName());
        }
        importInfo.setHeaderMapping(headerMapping);
        importInfo.setRead(read);
        List<OrgExport> orgExports = new ArrayList<>();
        List<EmpExport> empExports = new ArrayList<>();
        Edition.weixin(() -> {
            List<String> sheetNames = reader.getSheetNames();
            sheetNames.stream()
                    .filter("组织"::equals)
                    .findFirst()
                    .ifPresent(name -> {
                        List<OrgExport> readOrg = reader.setSheet(name).setHeaderAlias(OrgExport.headerMap()).read(0, 1, OrgExport.class);
                        orgExports.addAll(readOrg);
                        readOrg.forEach(v -> {
                            OrgExport copy = BeanUtil.copyProperties(v, OrgExport.class);
                            copy.setOrgName(copy.getOrgName() + "（" + copy.getOrgCode() + "）");
                            orgExports.add(copy);
                        });
                    });
            sheetNames.stream()
                    .filter("员工"::equals)
                    .findFirst()
                    .ifPresent(name -> {
                        List<EmpExport> readEmp = reader.setSheet(name).setHeaderAlias(EmpExport.headerMap()).read(0, 1, EmpExport.class);
                        empExports.addAll(readEmp);
                        // name && name（）
                        readEmp.forEach(v -> {
                            EmpExport copy = BeanUtil.copyProperties(v, EmpExport.class);
                            copy.setEmpName(copy.getEmpName() + "（" + copy.getEmpCode() + "）");
                            empExports.add(copy);
                        });
                    });
        });
        this.importExcel(importInfo, true, orgExports, empExports);
    }

    @Override
    public void importEditErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (importTaskDto == null) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "导入任务" + taskId + "不存在");
        }
        if (CollUtil.isEmpty(sheetModels)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
        }

        // =============================  读取Excel表头并校验 ================================
        List<Object> header = sheetModels.get(0);
        if (CollUtil.isEmpty(header)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到数据");
        }

        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setTaskId(taskId);
        importInfo.setFileName("编辑资产在线编辑保存");
        importInfo.setFileSize(0L);
        importInfo.setCompanyId(companyId);
        importInfo.setRead(sheetModels);

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject formProps = formVO.getFormProps();
        List<String> editCodes = new ArrayList<>();
        if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
            editCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
                    .toJavaList(EditFieldRules.class)
                    .stream()
                    .filter(editFieldRules -> editFieldRules.getValue() == 1)
                    .map(EditFieldRules::getFieldCode)
                    .collect(toList());
        }

        // 可编辑的属性
        FormFieldCO assetSerialNoCode = new FormFieldCO();
        List<FormFieldCO> formFields = new ArrayList<>();
        List<String> allFieldName = new ArrayList<>();
        for (FormFieldCO f : formVO.getFormFields()) {
            allFieldName.add(f.getFieldName());
            if (FormFieldCO.YZC_ASSET_SERIALNO.equals(f.getFieldType())) {
                formFields.add(f);
                assetSerialNoCode = f;
            }
            if (editCodes.contains(f.getFieldCode())
                    && !f.isHidden()
                    && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                    .contains(f.getFieldType())) {
                formFields.add(f);
            }
        }

        if (CollUtil.isEmpty(formFields)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "未配置可编辑字段");
        }
        Map<String, List<FormFieldCO>> assetAttrMap = formFields.stream()
                .collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 表头字段映射
        formFieldMap.set(MapUtil.of("asset", new ArrayList<>()));
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        List<String> noUpdate = new ArrayList<>();
        for (int i = 0; i < header.size(); i++) {
            String headName = Convert.toStr(header.get(i), "").trim();
            if (StrUtil.isBlank(Convert.toStr(headName))) {
                continue;
            }
            if (assetAttrMap.containsKey(headName)) {
                List<FormFieldCO> formFieldCOList = assetAttrMap.get(headName);
                if (CollUtil.isNotEmpty(formFieldCOList)) {
                    FormFieldCO formFieldCO = formFieldCOList.get(0);
                    headerMapping.put(i, formFieldCO);
                    formFieldCOList.remove(0);
                    assetAttrMap.put(headName, formFieldCOList);
                    formFieldMap.get().get("asset").add(formFieldCO);
                }
            } else if (allFieldName.contains(headName)) {
                noUpdate.add(headName);
            }
        }
        if (CollUtil.isNotEmpty(noUpdate)) {
            String join = String.join("，", noUpdate);
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "当前表格的以下字段不支持更新：" + join);
        }

        if (CollUtil.isEmpty(headerMapping)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表头未检测到可编辑属性");
        }
        FormFieldCO finalAssetSerialNoCode = assetSerialNoCode;
        boolean find = headerMapping.values().stream().anyMatch(f -> f.getFieldCode().equals(finalAssetSerialNoCode.getFieldCode()));
        if (!find) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到必填字段" + finalAssetSerialNoCode.getFieldName());
        }

        importInfo.setHeaderMapping(headerMapping);
        // 校验表头数据
        List<OrgExport> orgExports = new ArrayList<>();
        List<EmpExport> empExports = new ArrayList<>();
        Edition.weixin(() -> {
            Object orgs = redisService.get("weixin:import:asset:upt:org_id_trans:" + importInfo.getTaskId());
            Object emps = redisService.get("weixin:import:asset:upt:emp_id_trans:" + importInfo.getTaskId());
            if (Objects.isNull(orgs) || Objects.isNull(emps)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "文件已过期，请重新导入");
            }
            orgExports.addAll(JSONArray.parseArray(orgs.toString(), OrgExport.class));
            empExports.addAll(JSONArray.parseArray(emps.toString(), EmpExport.class));
        });
        this.importExcel(importInfo, false, orgExports, empExports);
    }

    private static List<String> needSheets = ListUtil.of(
            AssetConstant.ED_YZC_EMP,
            AssetConstant.ED_YZC_ORG);

    @Data
    @AllArgsConstructor
    public static class OrgExport {
        @ExcelField(header = "组织编码", ordinal = 1)
        public String orgCode;
        @ExcelField(header = "组织名称", ordinal = 2)
        public String orgName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(2);
            map.put("组织编码", "orgCode");
            map.put("组织名称", "orgName");
            return map;
        }
    }

    @Data
    @AllArgsConstructor
    public static class EmpExport {
        @ExcelField(header = "员工编码", ordinal = 1)
        public String empCode;
        @ExcelField(header = "员工名称", ordinal = 2)
        public String empName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(2);
            map.put("员工编码", "empCode");
            map.put("员工名称", "empName");
            return map;
        }
    }

    protected void buildSheet(ExcelWriter writer, List<FormFieldCO> formFields) {
        formFields.stream()
                .filter(f -> needSheets.contains(f.getFieldType()))
                .map(FormFieldCO::getFieldType).distinct().forEach(f -> {
                    switch (f) {
                        case AssetConstant.ED_YZC_ORG:
                            writer.setSheet("组织");
                            List<OrgDto> orgAll = orgFeignClient.list(new OrgQueryDto().setFilterPerm(true));
                            LinkedHashMap<String, String> orgHead = ExcelUtils.buildExcelHead(OrgExport.class);
                            orgHead.forEach(writer::addHeaderAlias);
                            List<OrgExport> orgData = orgAll.stream().map(org ->
                                            new OrgExport(org.getOrgCode(), org.getOrgName()))
                                    .collect(Collectors.toList());
                            writer.setOnlyAlias(true);
                            writer.write(orgData);
                            writer.autoSizeColumnAll();
                            break;
                        case AssetConstant.ED_YZC_EMP:
                            writer.setSheet("员工");
                            List<CusEmployeeDto> empAll = employeeFeignClient.actList(new CusEmployeeQueryDto().setFilterPerm(true));
                            LinkedHashMap<String, String> empHead = ExcelUtils.buildExcelHead(EmpExport.class);
                            empHead.forEach(writer::addHeaderAlias);
                            List<EmpExport> empData = empAll.stream().map(v -> new EmpExport(v.getEmpNo(), v.getEmpName())).collect(Collectors.toList());
                            writer.setOnlyAlias(true);
                            writer.write(empData);
                            writer.autoSizeColumnAll();
                            break;
                        default:
                            break;
                    }
                });
    }

    @Override
    public ExcelWriter buildEditExcelWriter() {
        // 获取 writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 获取 sheet
        Sheet sheet = writer.getSheet();
        // 获取 styleSet
        StyleSet styleSet = writer.getStyleSet();
        // 设置边框
        styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
        styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);
        Workbook workbook = sheet.getWorkbook();
        DataFormat format = workbook.createDataFormat();
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(format.getFormat("@"));

        // 设置表头的cellStyle
        CellStyle redHeadStyle = writer.createCellStyle();
        redHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        redHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        redHeadStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        redHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        redHeadStyle.setBorderBottom(BorderStyle.THIN);
        redHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        redHeadStyle.setBorderTop(BorderStyle.THIN);
        redHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderLeft(BorderStyle.THIN);
        redHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderRight(BorderStyle.THIN);
        redHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font red = writer.createFont();
        red.setFontHeightInPoints((short) 13);
        red.setColor(IndexedColors.RED.getIndex());
        redHeadStyle.setFont(red);

        CellStyle blackHeadStyle = writer.createCellStyle();
        blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        blackHeadStyle.setBorderBottom(BorderStyle.THIN);
        blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setBorderTop(BorderStyle.THIN);
        blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderLeft(BorderStyle.THIN);
        blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderRight(BorderStyle.THIN);
        blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font black = writer.createFont();
        black.setFontHeightInPoints((short) 13);
        black.setColor(IndexedColors.BLACK.getIndex());
        blackHeadStyle.setFont(black);

        // 写入文件注意事项
        Row attentionRow = writer.getOrCreateRow(0);
        attentionRow.setHeight((short) 2400);
        Cell attentionCell = attentionRow.createCell(0);
        CellStyle attention = writer.createCellStyle();
        attention.setVerticalAlignment(VerticalAlignment.CENTER);
        attention.setWrapText(true);
        attention.setFont(black);
        attentionCell.setCellStyle(attention);
        String text = "注意事项:\n" +
                "1、更新时资产编码是必填字段，资产编码本身不支持修改，请确认填写的编码已经在系统中；\n" +
                "2、导出表格的字段全部支持编辑。其他字段，如需更新请前往系统--资产--资产变更，提交资产变更单进行编辑；\n" +
                "3、不需要更新的字段，不填写则视为不更新；\n" +
                "4、如需删除某个字段的值，则请填写：删除 这两个字；\n" +
                "5、单次最多可导入5000条数据，文件不可超过1MB；";
        XSSFRichTextString xssfRichTextString = new XSSFRichTextString(text);
        Font titleRed = writer.createFont();
        titleRed.setFontHeightInPoints((short) 13);
        titleRed.setColor(IndexedColors.RED.getIndex());
        xssfRichTextString.applyFont(0, 143, black);
        xssfRichTextString.applyFont(143, 145, titleRed);
        xssfRichTextString.applyFont(145, text.length() - 1, black);
        attentionCell.setCellValue(xssfRichTextString);
        writer.merge(26);

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject formProps = formVO.getFormProps();
        List<String> editCodes = new ArrayList<>();
        if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
            editCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
                    .toJavaList(EditFieldRules.class)
                    .stream()
                    .filter(editFieldRules -> editFieldRules.getValue() == 1)
                    .map(EditFieldRules::getFieldCode)
                    .collect(toList());
        }

        // 可编辑的属性
        List<FormFieldCO> formFields = new ArrayList<>();
        for (FormFieldCO f : formVO.getFormFields()) {
            if (FormFieldCO.YZC_ASSET_SERIALNO.equals(f.getFieldType())) {
                formFields.add(0, f);
            }
            if (editCodes.contains(f.getFieldCode())
                    && !f.isHidden()
                    && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                    .contains(f.getFieldType())) {
                formFields.add(f);
            }
        }

        if (CollUtil.isEmpty(formFields)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "未配置可编辑字段");
        }

        Row headRow = writer.getOrCreateRow(1);
        int realCol = 0;
        for (FormFieldCO attr : formFields) {
            // ============================== 设置表头 ===================================
            // 写入表头
            Cell cell = headRow.createCell(realCol);
            cell.setCellStyle(attr.getFieldType().equals(AssetConstant.ED_YZC_ASSET_SERIALNO) ? redHeadStyle : blackHeadStyle);
            cell.setCellValue(attr.getFieldName());

            String tplComment = AssetExcelTplEnum.getEditTplComment(attr.getFieldCode());
            if (StrUtil.isNotEmpty(tplComment)) {
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6));
                comment.setString(new XSSFRichTextString(tplComment));
                cell.setCellComment(comment);
            }

            // 调整每一列宽度
            sheet.autoSizeColumn((short) realCol);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(realCol, sheet.getColumnWidth(realCol) * 17 / 10);
            sheet.setDefaultColumnStyle(realCol, style);

            if (FormFieldCO.SELECT_DROPDOWN.equals(attr.getFieldType())) {
                JSONArray values = attr.getFieldProps().getJSONArray("values");
                String[] selected = values.toArray(new String[]{});
                writer.addSelect(new CellRangeAddressList(2, 5000, realCol, realCol), selected);
            }
            realCol += 1;
        }
        // 写入其他sheet
        Edition.weixin(() -> buildSheet(writer, formFields));
        return writer;
    }

    protected void importExcel(ImportInfo importInfo, Boolean async, List<OrgExport> orgExports, List<EmpExport> empExports) {
        // 判断是否超过最大上传条数，一次限制5000
        if (importInfo.getRead().size() > MAX_BATCH + 1) {
            throw new BusinessException(MeansResultCode.IMPORT_MAX_LIMIT);
        }
        // 删除历史导入信息
        if (importInfo.getTaskId() != null) {
            assetFeignClient.importErrorDeleteAll(importInfo.getTaskId());
        }
        globalCache.set(importInfo);
        dropDownCache.set(new HashMap<>());
        orgRefEmpCache.set(new HashMap<>());
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto()
                        .setFileName(importInfo.getFileName())
                        .setImportType(DictConstant.IMPORT_TYPE_ASSET_EDIT)
                        .setFileSize(importInfo.getFileSize())
                        .setCount(importInfo.getRead().size() - 1);
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(importCache, orgExports, empExports);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto()
                    .setFileName(importInfo.getFileName())
                    .setImportType(DictConstant.IMPORT_TYPE_ASSET_EDIT)
                    .setFileSize(importInfo.getFileSize())
                    .setCount(importInfo.getRead().size() - 1);
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(importCache, orgExports, empExports);
        }
    }

    /**
     * 导入
     *
     * @param importCache 导入结果集
     */
    private void startImport(ImportDto importCache, List<OrgExport> orgExports, List<EmpExport> empExports) {
        ImportInfo importInfo = globalCache.get();
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (importInfo.getTaskId() != null) {
            importTaskDto = importTaskFeignClient.queryById(importInfo.getTaskId());
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, importInfo.getCompanyId()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                ImportTaskExtInfoDto extInfoDto = new ImportTaskExtInfoDto();
                extInfoDto.setStandardId(importInfo.getStandardId());
                importTaskDto.setExtInfo(extInfoDto);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
                Edition.weixin(() -> {
                    redisService.set("weixin:import:asset:upt:org_id_trans:" + id, JSONObject.toJSONString(orgExports), 3, TimeUnit.DAYS);
                    redisService.set("weixin:import:asset:upt:emp_id_trans:" + id, JSONObject.toJSONString(empExports), 3, TimeUnit.DAYS);
                });
            }
            List<AssetImportDto> tableData = new ArrayList<>();
            // 构建资产JSON数据
            for (int idx = 1; idx < importInfo.getRead().size(); idx++) {
                // 行数据
                List<Object> dataRow = importInfo.getRead().get(idx);
                // 写入固定属性数据
                JSONObject assetData = new JSONObject();
                IntStream.range(0, dataRow.size()).forEach(nameIdx -> {
                    FormFieldCO assetConfig = importInfo.getHeaderMapping().get(nameIdx);
                    if (assetConfig != null) {
                        Object cell = dataRow.get(nameIdx);
                        assetData.put(assetConfig.getFieldCode(), cell);
                    }
                });
                // 创建属性对象
                AssetImportDto importDto = new AssetImportDto();
                importDto.setTaskId(importTaskDto.getId())
                        .setStandardId(importInfo.getStandardId())
                        .setErrorNum(0)
                        .setAssetData(assetData);
                tableData.add(importDto);
            }
            // 执行
            importService.sendAssetEditMsg(importInfo.getCompanyId());
            this.executeTableData(importTaskDto.getId(), tableData, importInfo, orgExports, empExports);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, importInfo.getCompanyId()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_EDIT, importInfo.getCompanyId()), "finish", true);
            importService.sendAssetEditMsg(importInfo.getCompanyId());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.ASSET_EDIT, importInfo.getCompanyId());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
                if (Edition.isWeixin() && importTaskDto.getTaskStatus() == DictConstant.IMPORT_STATUS_SUCC) {
                    redisService.del("weixin:import:asset:upt:org_id_trans:" + importTaskDto.getId());
                    redisService.del("weixin:import:asset:upt:emp_id_trans:" + importTaskDto.getId());
                }
            }
            this.clearThreadLocal();
        }
    }

    /**
     * 处理校验入库
     *
     * @param tableData 数据
     */
    private void executeTableData(Long taskId, List<AssetImportDto> tableData, ImportInfo importInfo, List<OrgExport> orgExports, List<EmpExport> empExports) {

        // 有序集合
        LinkedHashMap<String, FormFieldCO> codeToAssetAttrMap = new LinkedHashMap<>();

        // 临时缓存
        Map<String, Map<String, List<IdNameCache>>> tmpCache = new HashMap<>();
        for (Map.Entry<Integer, FormFieldCO> entry : importInfo.getHeaderMapping().entrySet()) {
            FormFieldCO fieldCO = entry.getValue();
            // 写入本地缓存
            loadSelectCache(fieldCO.getFieldType(), fieldCO.getFieldName(), tmpCache, fieldCO.getFieldProps());
            codeToAssetAttrMap.putIfAbsent(fieldCO.getFieldCode(), fieldCO);
        }

        Edition.weixin(() -> {
            if (CollUtil.isNotEmpty(orgExports)) {
                // 组织
                Map<String, List<OrgExport>> orgGroup = orgExports.stream().collect(Collectors.groupingBy(OrgExport::getOrgCode));
                Map<String, List<IdNameCache>> orgCache = orgFeignClient.listByCodes(new ArrayList<>(orgGroup.keySet()))
                        .stream()
                        .filter(v -> orgGroup.containsKey(v.getOrgCode()))
                        .map(v -> orgGroup.get(v.getOrgCode()).stream().map(t -> new IdNameCache(Convert.toStr(v.getId()), t.getOrgName(), true)).collect(toList()))
                        .flatMap(Collection::stream)
                        .collect(Collectors.groupingBy(IdNameCache::getName));
                importInfo.getHeaderMapping().values().stream()
                        .filter(v -> FormFieldCO.YZC_ORG.equals(v.getFieldType()))
                        .collect(Collectors.toList())
                        .forEach(v -> {
                            dropDownCache.get().remove(v.getFieldName());
                            dropDownCache.get().put(v.getFieldName(), orgCache);
                        });
            }
            if (CollUtil.isNotEmpty(empExports)) {
                // 人
                Map<String, List<EmpExport>> empGroup = empExports.stream().collect(Collectors.groupingBy(EmpExport::getEmpCode));
                Map<String, List<IdNameCache>> empCache = employeeFeignClient.listByCodes(new ArrayList<>(empGroup.keySet()))
                        .stream()
                        .filter(v -> empGroup.containsKey(v.getEmpNo()))
                        .map(v -> empGroup.get(v.getEmpNo()).stream().map(t -> new IdNameCache(Convert.toStr(v.getId()), t.getEmpName(), true)).collect(Collectors.toList()))
                        .flatMap(Collection::stream)
                        .collect(Collectors.groupingBy(IdNameCache::getName));

                importInfo.getHeaderMapping().values().stream()
                        .filter(v -> FormFieldCO.YZC_EMP.equals(v.getFieldType()))
                        .collect(Collectors.toList())
                        .forEach(v -> {
                            dropDownCache.get().remove(v.getFieldName());
                            dropDownCache.get().put(v.getFieldName(), empCache);
                        });
            }
        });

        // 下拉Name和ID映射，Map<字段名称, Map<名称, List<映射>>
        Map<String, Map<String, List<IdNameCache>>> fieldNameCache = dropDownCache.get();
        // 写入表头数据
        this.saveLuckySheetHead(taskId, importInfo.getHeaderMapping());
        AtomicInteger successNum = new AtomicInteger(0);
        // 循环处理行数据
        IntStream.range(0, tableData.size()).forEach(idx -> {
            // 获取导入数据
            AssetImportDto importDto = tableData.get(idx);
            // 数据
            JSONObject assetData = importDto.getAssetData();
            List<AssetImportDto.FieldData> fieldDataList = new ArrayList<>();
            codeToAssetAttrMap.forEach((attrCode, fieldCO) -> {
                AssetImportDto.FieldData fieldData = new AssetImportDto.FieldData();
                Object attrVal = assetData.getOrDefault(attrCode, null);
                if (fieldCO.getFieldType().equals(EXCLUDE_FIELD)) {
                    assetData.remove(attrCode);
                }
                fieldData.setSource(attrVal);
                if (attrVal instanceof String) {
                    fieldData.setTarget(StrUtil.trim(((String) attrVal)));
                } else {
                    fieldData.setTarget(attrVal);
                }
                fieldData.setFieldName(fieldCO.getFieldName());
                fieldData.setFieldCode(fieldCO.getFieldCode());

                // 删除操作不处理
                if (!"删除".equals(Convert.toStr(attrVal))) {
                    // 处理日期类型
                    if (FormFieldCO.DATETIME.equals(fieldCO.getFieldType())) {
                        dateConvert(fieldData, attrVal, fieldCO.getFieldProps().getString("dateFormatType"));
                    }
                    // 处理多选
                    if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(fieldCO.getFieldType())) {
                        if (fieldData.getTarget() != null) {
                            String val = Convert.toStr(attrVal);
                            val = val.replace("，", ",");
                            String[] split = val.split(",");
                            fieldData.setTarget(new ArrayList<>(Arrays.asList(split)));
                        } else {
                            fieldData.setTarget(new ArrayList<>());
                        }
                    }
                    // 翻译业务组件数据
                    String attrValStr = StrUtil.trim(Convert.toStr(attrVal));
                    if (fieldNameCache.containsKey(fieldCO.getFieldName()) && StrUtil.isNotBlank(attrValStr)) {
                        fieldData.setTarget(null);
                        Map<String, List<IdNameCache>> idNameCacheMap = fieldNameCache.getOrDefault(fieldCO.getFieldName(), new HashMap<>());
                        List<IdNameCache> idNameCaches;
                        if (idNameCacheMap.containsKey(attrValStr)) {
                            // 1.优先全匹配文本
                            idNameCaches = idNameCacheMap.get(attrValStr);
                        } else {
                            // 2.匹配括号内容是否大写字符加数字类型
                            idNameCaches = idNameCacheMap.get(ExcelUtils.matchCodeAndReplace(attrValStr));
                        }

                        if (CollUtil.isEmpty(idNameCaches)) {
                            fieldData.getErrMsg().add("当前数据不存在，请先添加");
                        } else if (idNameCaches.size() > 1) {
                            List<IdNameCache> collect = idNameCaches.stream().filter(IdNameCache::getHasPerm).collect(Collectors.toList());
                            // 多条数据都没权限
                            if (CollUtil.isEmpty(collect)) {
                                fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                            } else {
                                switch (fieldCO.getFieldType()) {
                                    case FormFieldCO.YZC_EMP:
                                        if (collect.size() > 1) {
                                            fieldData.getErrMsg().add("当前数据有重名，请输入员工姓名和员工工号，示例：李白（001）");
                                        } else {
                                            IdNameCache idName = collect.get(0);
                                            fieldData.setTarget(idName.getId());
                                        }
                                        break;
                                    case FormFieldCO.YZC_ORG:
                                        if (collect.size() > 1) {
                                            fieldData.getErrMsg().add("当前数据有重名，请输入组织名称和组织编码，示例：销售部（001）");
                                        } else {
                                            IdNameCache idName = collect.get(0);
                                            fieldData.setTarget(idName.getId());
                                        }
                                        break;
                                    case FormFieldCO.YZC_ASSET_CATE:
                                        if (collect.size() > 1) {
                                            fieldData.getErrMsg().add("当前数据有重名，请输入资产分类名称和分类编码，示例：电子产品（001）");
                                        } else {
                                            IdNameCache idName = collect.get(0);
                                            fieldData.setTarget(idName.getId());
                                        }
                                        break;
                                    case FormFieldCO.YZC_AREA:
                                        if (collect.size() > 1) {
                                            fieldData.getErrMsg().add("当前数据有重名，请输入区域名称和区域编码，示例：二楼（A01）");
                                        } else {
                                            IdNameCache idName = collect.get(0);
                                            fieldData.setTarget(idName.getId());
                                        }
                                        break;
                                    default:
                                        fieldData.getErrMsg().add("当前数据有重名");
                                }
                            }
                        } else {
                            IdNameCache idName = idNameCaches.get(0);
                            if (idName.getHasPerm()) {
                                fieldData.setTarget(idName.getId());
                            } else {
                                fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                            }
                        }
                    }
                }
                fieldDataList.add(fieldData);
            });
            importDto.setFieldDataList(fieldDataList);
            importDto.setRowNum(idx + 1 - successNum.get());
            importDto.setFormFieldMap(formFieldMap.get());
            Boolean success = assetFeignClient.saveEditSheetData(importDto);
            if (BooleanUtil.isTrue(success)) {
                successNum.getAndIncrement();
            }
            importService.sendAssetEditMsg(importInfo.getCompanyId());
        });
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_UPT_MEANS, new AuditableImportResult(successNum.get())));
    }

    private void saveLuckySheetHead(Long taskId, Map<Integer, FormFieldCO> headerMapping) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new LinkedList<>();

        List<Integer> headerList = new ArrayList<>(headerMapping.keySet());
        headerList = headerList.stream().sorted().collect(Collectors.toList());

        AtomicInteger cellIndex = new AtomicInteger(0);
        for (Integer index : headerList) {
            FormFieldCO config = headerMapping.get(index);
            // 记录当前属性
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(config.getFieldName());
            headModelList.add(luckySheetModel);
        }
        importErrorDto.setHeadModelList(headModelList);
        this.assetFeignClient.saveSheetHead(importErrorDto);
    }

    /**
     * 清理本地缓存
     */
    private void clearThreadLocal() {
        dropDownCache.remove();
        orgRefEmpCache.remove();
        globalCache.remove();
        formFieldMap.remove();
    }

}
