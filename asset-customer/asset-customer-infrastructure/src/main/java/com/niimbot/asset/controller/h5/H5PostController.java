package com.niimbot.asset.controller.h5;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.activiti.WorkflowApproveDto;
import com.niimbot.activiti.WorkflowForwardDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.AreaFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.CategoryFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.InventoryAssetFeignClient;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.asset.service.feign.MessageFeignClient;
import com.niimbot.asset.service.feign.OnlineInventoryFeignClient;
import com.niimbot.asset.service.feign.PrintTagFeignClient;
import com.niimbot.asset.service.feign.RepairOrderFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryAssetEditDto;
import com.niimbot.inventory.InventoryAssetListDto;
import com.niimbot.inventory.InventoryManualDto;
import com.niimbot.inventory.InventoryRemarkDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.maintenance.RepairOrderDto;
import com.niimbot.maintenance.RepairOrderFinishDto;
import com.niimbot.material.MaterialDto;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AssetDto;
import com.niimbot.means.CategoryDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.MessageReadDto;
import com.niimbot.system.UserPrintTagDto;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@ResultController
@RequiredArgsConstructor
public class H5PostController {

    private final AsAssetUtil assetUtil;
    private final AssetService assetService;
    private final FormFeignClient formFeignClient;
    private final InventoryFeignClient inventoryFeignClient;
    private final RepairOrderFeignClient repairOrderFeignClient;
    private final PrintTagFeignClient printTagFeignClient;
    private final InventoryAssetFeignClient inventoryAssetFeignClient;
    private final MaterialService materialService;
    private final MessageFeignClient messageFeignClient;
    private final ActWorkflowFeignClient actWorkflowFeignClient;
    private final AreaFeignClient areaFeignClient;
    private final CategoryFeignClient categoryFeignClient;
    private final MaterialFeignClient materialFeignClient;
    private final AssetFeignClient assetFeignClient;
    private final OnlineInventoryFeignClient onlineInventoryFeignClient;

    @ApiOperation(value = "站内信已读")
    @PostMapping("/api/common/message/read/edit")
    @ResultMessage(value = ResultConstant.OPERATION_SUCCESS)
    public Boolean postReadMessage(@RequestBody @Validated MessageReadDto dto) {
        return messageFeignClient.readMessage(dto);
    }

    @ApiOperation(value = "APP盘点资产分页列表")
    @PostMapping("/api/app/inventory/asset/post/page")
    public PageUtils<InventoryAssetListDto> pagePost(@RequestBody InventorySurplusQueryDto dto) {
        // 处理查询条件
        if (CollUtil.isNotEmpty(dto.getConditions())) {
            dto.getConditions().forEach(v -> {
                Object queryData = v.getQueryData();
                // 是数组
                if (queryData.getClass().isArray() || queryData instanceof Collection) {
                    List<String> list = Convert.toList(Object.class, queryData).stream().map(Object::toString).collect(Collectors.toList());
                    if (list.contains("-1")) {
                        v.setQuery("isNull");
                        v.setQueryData(Collections.emptyList());
                    }
                }
                // 是字符
                if (queryData instanceof String || queryData instanceof Number) {
                    String toString = String.valueOf(queryData);
                    if ("-1".equals(toString)) {
                        v.setQuery("isNull");
                        v.setQueryData(Collections.emptyList());
                    }
                }
            });
        }
        return inventoryAssetFeignClient.appPage(dto);
    }

    @ApiOperation(value = "编辑资产数据")
    @PostMapping("/api/common/asset/edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean postEdit(@RequestBody @Validated({Update.class}) AssetDto assetDto) {
        assetService.edit(assetDto);
        AuditLogs.sendRecord(() -> {
            AssetDto dto = assetFeignClient.getInfoNoPerm(assetDto.getId());
            return AuditLogRecord.create(Auditable.Action.UPT_MEANS, dto);
        });
        return true;
    }

    @ApiOperation(value = "编辑上报资产")
    @PostMapping("/api/pc/inventory/task/reportAsset/edit")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean postEditReportAsset(@RequestBody @Validated({Update.class}) InventorySurplusDto dto) {
        // 盘盈数据
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject assetData = dto.getAssetData();
        assetUtil.translateAssetJson(assetData, formVO.getFormFields());
        /* 翻译数据 */
        return inventoryFeignClient.editReportAsset(dto);
    }

    @ApiOperation(value = "完成维修")
    @PostMapping("/api/common/maintenance/repair/repairFinish/edit")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean postRepairFinish(@RequestBody @Validated RepairOrderFinishDto dto) {
        if (Objects.nonNull(dto.getFinishTimestamp())) {
            Instant instant = Instant.ofEpochMilli(dto.getFinishTimestamp());
            dto.setFinishTime(LocalDateTime.ofInstant(instant, ZoneId.systemDefault()));
        }
        Boolean result = repairOrderFeignClient.repairFinish(dto);
        if (result) {
            AuditLogs.sendRecord(LoginUserThreadLocal.get(), loginUser -> {
                RepairOrderDto orderDto = repairOrderFeignClient.getById(dto.getRepairOrderId());
                String content = "维修完成：资产维修单" + orderDto.getOrderNo();
                return AuditLogRecord.create(Auditable.Action.OR_MAS_WX_FINISH, Collections.singletonMap(Auditable.Tpl.CONTENT, content), loginUser);
            });
        }
        return result;
    }

    @ApiOperation(value = "提交审核")
    @PostMapping("/api/app/inventory/submit/edit")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean postSubmit(@RequestBody @Validated InventoryTaskApproveDto approveDto) {
        return inventoryFeignClient.submit(approveDto);
    }



    @ApiOperation(value = "APP编辑盘点资产")
    @PostMapping("/api/app/inventory/updateAsset")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @RepeatSubmit
    public Boolean appEditAsset(@RequestBody @Validated InventoryAssetEditDto dto) {
        return inventoryAssetFeignClient.editAsset(dto);
    }

    @ApiOperation(value = "编辑标签模板")
    @RepeatSubmit
    @PostMapping("/api/app/tag/edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean postEdit(@RequestBody @Validated UserPrintTagDto userPrintTagDto) {
        return printTagFeignClient.edit(userPrintTagDto);
    }

    @ApiOperation(value = "编辑耗材数据")
    @PostMapping("/api/common/material/edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_MRL)
    public Boolean postEdit(@RequestBody @Validated({Update.class}) MaterialDto materialDto) {
        return materialService.edit(materialDto);
    }

    @ApiOperation("全部已读")
    @PostMapping("/api/common/message/allRead/edit/{businessType}")
    public Boolean postAllRead(@PathVariable(value = "businessType") Integer businessType) {
        return messageFeignClient.allRead(ServletUtils.getClientSource().getValue(), businessType);
    }

    @ApiOperation(value = "抄送全部已读")
    @PostMapping("/api/common/workflow/copy/read/all/edit")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean postCopyReadAll() {
        return actWorkflowFeignClient.copyReadAll();
    }

    @ApiOperation(value = "抄送已读")
    @PostMapping("/api/common/workflow/copy/read/{processInstanceId}")
    public Boolean postCopyRead(@PathVariable("processInstanceId") String processInstanceId) {
        return actWorkflowFeignClient.copyRead(processInstanceId);
    }

    @ApiOperation(value = "流程撤销")
    @PostMapping("/api/common/workflow/revoked/{processInstanceId}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean postRevoked(@PathVariable("processInstanceId") String processInstanceId) {
        return actWorkflowFeignClient.revoked(processInstanceId);
    }

    @ApiOperation(value = "流程审批通过")
    @PostMapping("/api/common/workflow/approved")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean postApproved(@RequestBody WorkflowApproveDto approveDto) {
        return actWorkflowFeignClient.approved(approveDto);
    }

    @ApiOperation(value = "流程转办")
    @PostMapping("/api/common/workflow/forward")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean postForward(@RequestBody WorkflowForwardDto forwardDto) {
        return actWorkflowFeignClient.forward(forwardDto);
    }

    @ApiOperation(value = "流程审批驳回")
    @PostMapping("/api/common/workflow/rejected")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean postRejected(@RequestBody WorkflowApproveDto approveDto) {
        return actWorkflowFeignClient.rejected(approveDto);
    }

    @ApiOperation(value = "编辑区域数据")
    @PostMapping("/api/common/area/edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_MEANS_AREA)
    public Boolean postEdit(@Validated(Update.class) @RequestBody AreaDto area) {
        return areaFeignClient.updateArea(area);
    }

    @ApiOperation(value = "编辑资产分类数据")
    @PostMapping("/api/common/category/edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_MEANS_CATE)
    public CategoryDto postEdit(@RequestBody @Validated(value = {Update.class}) CategoryDto categoryDto) {
        return categoryFeignClient.edit(categoryDto);
    }

    @ApiOperation(value = "耗材删除")
    @PostMapping("/api/common/material/post/delete")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> materialIds) {
        List<AuditableOperateResult> results = materialFeignClient.remove(materialIds, true);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MRL, results));
        return CollUtil.isNotEmpty(results);
    }


    @ApiOperation(value = "APP手动盘点")
    @PostMapping("/api/app/inventory/manualInventory/edit")
    @RepeatSubmit
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean postManualInventory(@RequestBody @Validated InventoryManualDto dto) {
        return onlineInventoryFeignClient.appManualInventory(dto);
    }

    @ApiOperation(value = "APP修改盘点备注")
    @PostMapping("/api/app/inventory/updateRemark")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @RepeatSubmit
    public Boolean updateRemark(@RequestBody @Validated InventoryRemarkDto dto) {
        return onlineInventoryFeignClient.updateRemark(dto);
    }

}