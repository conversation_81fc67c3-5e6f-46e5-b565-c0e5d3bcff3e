package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.easydesign.form.dto.FormTplAddCmd;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.StandardListDto;
import com.niimbot.means.StandardQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/8 15:56
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface StandardFeignClient {

    @GetMapping(value = "server/means/standard/page")
    PageUtils<StandardListDto> page(@SpringQueryMap StandardQueryDto queryDto);

    @GetMapping(value = "server/means/standard/list")
    List<StandardListDto> list(@RequestParam("name") String name);

    @GetMapping(value = "server/means/standard/form")
    FormVO form(@RequestParam(value = "standardId", required = false) Long standardId,
                @RequestParam(value = "throwEx", required = false) Boolean throwEx);

    @PostMapping(value = "server/means/standard")
    Boolean add(@RequestBody FormTplAddCmd formTplAddCmd);

    @PutMapping(value = "server/means/standard")
    Boolean edit(@RequestBody FormTplAddCmd formTplAddCmd);

    @DeleteMapping(value = "server/means/standard/{id}")
    Boolean delete(@PathVariable("id") Long id);

    @PostMapping(value = "server/means/standard/copySys/{id}")
    StandardListDto copySys(@PathVariable("id") Long id);

    @GetMapping(value = "server/means/standard/extension")
    List<FormFieldCO> getStandardExtField(@RequestParam("mappingFormId") Long mappingFormId,
                                          @RequestParam("standardId") Long standardId);
}
