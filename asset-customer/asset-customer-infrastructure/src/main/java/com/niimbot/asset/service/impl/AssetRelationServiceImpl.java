package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.query.QueryConditionType;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetRelationService;
import com.niimbot.asset.service.feign.AreaFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.AssetRelationFeignClient;
import com.niimbot.asset.service.feign.AssetStatusFeignClient;
import com.niimbot.asset.service.feign.CategoryFeignClient;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.means.AssetRelationAppDto;
import com.niimbot.means.AssetRelationDto;
import com.niimbot.means.AssetRelationQueryConditionDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/8/10 上午9:55
 */
@Slf4j
@Service
public class AssetRelationServiceImpl implements AssetRelationService {

    @Autowired
    protected AssetFeignClient assetFeignClient;
    @Autowired
    protected CusEmployeeFeignClient employeeFeignClient;
    @Autowired
    protected CategoryFeignClient categoryFeignClient;
    @Autowired
    protected OrgFeignClient orgFeignClient;
    @Autowired
    protected AreaFeignClient areaFeignClient;
    @Autowired
    protected DictConvertUtil dictConvertUtil;
    @Autowired
    protected CusUserSettingFeignClient settingFeignClient;
    @Autowired
    protected FormFeignClient formFeignClient;
    @Autowired
    protected StandardFeignClient standardFeignClient;
    @Autowired
    protected AsAssetUtil assetUtil;
    @Autowired
    protected AssetRelationFeignClient assetRelationFeignClient;
    @Autowired
    private AssetStatusFeignClient assetStatusFeignClient;

    @Override
    public PageUtils<JSONObject> assetRelationPage(AssetQueryConditionDto queryDto) {
        //获取资产动态表单
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);

        //处理单据类型对应的资产状态，以及接口查询状态
        if (ObjectUtil.isNotNull(queryDto.getOrderType())) {
            List<Integer> assetStatus = new ArrayList<>();
            if (AssetConstant.ORDER_TYPE_MAINTAIN_PLAN != queryDto.getOrderType()
                    && AssetConstant.ORDER_TYPE_MAINTAIN != queryDto.getOrderType()) {
                assetStatus = this.assetStatusFeignClient.getAssetStatusByOrderType(queryDto.getOrderType());
            } else {
                //资产保养计划：仅可选【闲置、借用、在用、维修中、审批中、待维修】状态的资产
                assetStatus = ListUtil.of(1,2,3,5,6,7);
            }

            //根据单据类型查询单据类型对应的资产状态

            QueryConditionDto statusCondition = null;
            if (CollUtil.isNotEmpty(queryDto.getConditions())) {
                statusCondition = queryDto.getConditions().stream()
                        .filter(condition -> AssetConstant.STATUS.equals(condition.getCode()))
                        .findAny()
                        .orElse(null);
            }

            //状态为空，直接返回空数据
            if (CollUtil.isEmpty(assetStatus)) {
                return new PageUtils<>();
            } else if (Objects.nonNull(statusCondition)) {
                List<Integer> queryData = new ArrayList<>();
                if (statusCondition.getQueryData() instanceof List) {
                    queryData = ((List<?>) statusCondition.getQueryData())
                            .stream()
                            .map(o -> Integer.parseInt(o.toString()))
                            .collect(Collectors.toList());
                }
                if (CollUtil.isNotEmpty(queryData)) {
                    //取单据类型对应资产状态和查询状态的交集
                    List<Integer> status = (List<Integer>) CollectionUtil.intersection(assetStatus, queryData);
                    statusCondition.setQueryData(status);
                } else {
                    statusCondition.setQueryData(assetStatus);
                }
            } else {
                statusCondition =
                        new QueryConditionDto()
                                .setQuery(QueryConditionType.IN.getCode())
                                .setCode(AssetConstant.STATUS)
                                .setType(AssetConstant.ED_SELECT_DROPDOWN)
                                .setQueryData(assetStatus);
                if (CollUtil.isNotEmpty(queryDto.getConditions())) {
                    queryDto.getConditions().add(statusCondition);
                } else {
                    queryDto.setConditions(Collections.singletonList(statusCondition));
                }
            }
        }

        PageUtils<AssetRelationDto> mainAssetPage = assetRelationFeignClient.relationPage(queryDto);
        List<JSONObject> assetDataList = mainAssetPage.getList().stream().map(AssetRelationDto::translate).collect(Collectors.toList());
        assetUtil.translateAssetJsonBatch(assetDataList, formVO.getFormFields());

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(mainAssetPage, result);
        result.setList(assetDataList);
        return result;
    }

    @Override
    public PageUtils<JSONObject> subAsset(AssetRelationQueryConditionDto queryConditionDto) {
        //获取资产动态表单
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);

        List<AssetRelationDto> dataList = new ArrayList<>();
        //如果包含主资产信息
        if (queryConditionDto.getContainMainAsset()) {
            AssetQueryConditionDto assetQueryConditionDto = new AssetQueryConditionDto()
                    .setAssetIds(Collections.singletonList(queryConditionDto.getMainAssetId()))
                    .setCompanyId(LoginUserThreadLocal.getCompanyId());
            PageUtils<AssetRelationDto> mainAssetPage = assetRelationFeignClient.relationPage(assetQueryConditionDto);
            if (Objects.nonNull(mainAssetPage) && CollUtil.isNotEmpty(mainAssetPage.getList())) {
                dataList.addAll(mainAssetPage.getList());
            }
        }

        //处理单据类型对应的资产状态，以及接口查询状态
        if (ObjectUtil.isNotNull(queryConditionDto.getOrderType())) {
            List<Integer> assetStatus = new ArrayList<>();
            if (AssetConstant.ORDER_TYPE_MAINTAIN_PLAN != queryConditionDto.getOrderType()
                    && AssetConstant.ORDER_TYPE_MAINTAIN != queryConditionDto.getOrderType()) {
                assetStatus = this.assetStatusFeignClient.getAssetStatusByOrderType(queryConditionDto.getOrderType());
            } else {
                //资产保养计划：仅可选【闲置、借用、在用、维修中、审批中、待维修】状态的资产
                assetStatus = ListUtil.of(1,2,3,5,6,7);
            }

            //根据单据类型查询单据类型对应的资产状态
            QueryConditionDto statusCondition = null;
            if (CollUtil.isNotEmpty(queryConditionDto.getConditions())) {
                statusCondition = queryConditionDto.getConditions().stream()
                        .filter(condition -> AssetConstant.STATUS.equals(condition.getCode()))
                        .findAny()
                        .orElse(null);
            }

            //状态为空，直接返回空数据
            if (CollUtil.isEmpty(assetStatus)) {
                return new PageUtils<>();
            } else if (Objects.nonNull(statusCondition)) {
                List<Integer> queryData = new ArrayList<>();
                if (statusCondition.getQueryData() instanceof List) {
                    queryData = ((List<?>) statusCondition.getQueryData())
                            .stream()
                            .map(o -> Integer.parseInt(o.toString()))
                            .collect(Collectors.toList());
                }
                if (CollUtil.isNotEmpty(queryData)) {
                    //取单据类型对应资产状态和查询状态的交集
                    List<Integer> status = (List<Integer>) CollectionUtil.intersection(assetStatus, queryData);
                    statusCondition.setQueryData(status);
                } else {
                    statusCondition.setQueryData(assetStatus);
                }
            } else {
                statusCondition =
                        new QueryConditionDto()
                                .setQuery(QueryConditionType.IN.getCode())
                                .setCode(AssetConstant.STATUS)
                                .setType(AssetConstant.ED_SELECT_DROPDOWN)
                                .setQueryData(assetStatus);
                if (CollUtil.isNotEmpty(queryConditionDto.getConditions())) {
                    queryConditionDto.getConditions().add(statusCondition);
                } else {
                    queryConditionDto.setConditions(Collections.singletonList(statusCondition));
                }
            }
        }

        //子资产信息
        PageUtils<AssetRelationDto> subAssetPage = assetRelationFeignClient.subAssetPage(queryConditionDto);
        if (Objects.nonNull(subAssetPage) && CollUtil.isNotEmpty(subAssetPage.getList())) {
            dataList.addAll(subAssetPage.getList());
        }

        List<JSONObject> assetDataList = dataList.stream().map(AssetRelationDto::translate).collect(Collectors.toList());

        assetUtil.translateAssetJsonBatch(assetDataList, formVO.getFormFields());

        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtil.copyProperties(subAssetPage, result);
        result.setList(assetDataList);
        return result;
    }

    @Override
    public PageUtils<AssetRelationAppDto> assetRelationAppPage(AssetQueryConditionDto queryDto) {
        //处理单据类型对应的资产状态，以及接口查询状态
        if (ObjectUtil.isNotNull(queryDto.getOrderType())) {
            List<Integer> assetStatus = new ArrayList<>();
            if (AssetConstant.ORDER_TYPE_MAINTAIN_PLAN != queryDto.getOrderType()
                    && AssetConstant.ORDER_TYPE_MAINTAIN != queryDto.getOrderType()) {
                assetStatus = this.assetStatusFeignClient.getAssetStatusByOrderType(queryDto.getOrderType());
            } else {
                //资产保养计划：仅可选【闲置、借用、在用、维修中、审批中、待维修】状态的资产
                assetStatus = ListUtil.of(1,2,3,5,6,7);
            }

            //根据单据类型查询单据类型对应的资产状态

            QueryConditionDto statusCondition = null;
            if (CollUtil.isNotEmpty(queryDto.getConditions())) {
                statusCondition = queryDto.getConditions().stream()
                        .filter(condition -> AssetConstant.STATUS.equals(condition.getCode()))
                        .findAny()
                        .orElse(null);
            }

            //状态为空，直接返回空数据
            if (CollUtil.isEmpty(assetStatus)) {
                return new PageUtils<>();
            } else if (Objects.nonNull(statusCondition)) {
                List<Integer> queryData = new ArrayList<>();
                if (statusCondition.getQueryData() instanceof List) {
                    queryData = ((List<?>) statusCondition.getQueryData())
                            .stream()
                            .map(o -> Integer.parseInt(o.toString()))
                            .collect(Collectors.toList());
                }
                if (CollUtil.isNotEmpty(queryData)) {
                    //取单据类型对应资产状态和查询状态的交集
                    List<Integer> status = (List<Integer>) CollectionUtil.intersection(assetStatus, queryData);
                    statusCondition.setQueryData(status);
                } else {
                    statusCondition.setQueryData(assetStatus);
                }
            } else {
                statusCondition =
                        new QueryConditionDto()
                                .setQuery(QueryConditionType.IN.getCode())
                                .setCode(AssetConstant.STATUS)
                                .setType(AssetConstant.ED_SELECT_DROPDOWN)
                                .setQueryData(assetStatus);
                if (CollUtil.isNotEmpty(queryDto.getConditions())) {
                    queryDto.getConditions().add(statusCondition);
                } else {
                    queryDto.setConditions(Collections.singletonList(statusCondition));
                }
            }
        }
        return assetRelationFeignClient.relationAppPage(queryDto);
    }

    @Override
    public PageUtils<AssetRelationAppDto> subAssetAppPage(AssetRelationQueryConditionDto queryDto) {
        //处理单据类型对应的资产状态，以及接口查询状态
        if (ObjectUtil.isNotNull(queryDto.getOrderType())) {
            List<Integer> assetStatus = new ArrayList<>();
            if (AssetConstant.ORDER_TYPE_MAINTAIN_PLAN != queryDto.getOrderType()
                    && AssetConstant.ORDER_TYPE_MAINTAIN != queryDto.getOrderType()) {
                assetStatus = this.assetStatusFeignClient.getAssetStatusByOrderType(queryDto.getOrderType());
            } else {
                //资产保养计划：仅可选【闲置、借用、在用、维修中、审批中、待维修】状态的资产
                assetStatus = ListUtil.of(1,2,3,5,6,7);
            }

            //根据单据类型查询单据类型对应的资产状态

            QueryConditionDto statusCondition = null;
            if (CollUtil.isNotEmpty(queryDto.getConditions())) {
                statusCondition = queryDto.getConditions().stream()
                        .filter(condition -> AssetConstant.STATUS.equals(condition.getCode()))
                        .findAny()
                        .orElse(null);
            }

            //状态为空，直接返回空数据
            if (CollUtil.isEmpty(assetStatus)) {
                return new PageUtils<>();
            } else if (Objects.nonNull(statusCondition)) {
                List<Integer> queryData = new ArrayList<>();
                if (statusCondition.getQueryData() instanceof List) {
                    queryData = ((List<?>) statusCondition.getQueryData())
                            .stream()
                            .map(o -> Integer.parseInt(o.toString()))
                            .collect(Collectors.toList());
                }
                if (CollUtil.isNotEmpty(queryData)) {
                    //取单据类型对应资产状态和查询状态的交集
                    List<Integer> status = (List<Integer>) CollectionUtil.intersection(assetStatus, queryData);
                    statusCondition.setQueryData(status);
                } else {
                    statusCondition.setQueryData(assetStatus);
                }
            } else {
                statusCondition =
                        new QueryConditionDto()
                                .setQuery(QueryConditionType.IN.getCode())
                                .setCode(AssetConstant.STATUS)
                                .setType(AssetConstant.ED_SELECT_DROPDOWN)
                                .setQueryData(assetStatus);
                if (CollUtil.isNotEmpty(queryDto.getConditions())) {
                    queryDto.getConditions().add(statusCondition);
                } else {
                    queryDto.setConditions(Collections.singletonList(statusCondition));
                }
            }
        }
        return assetRelationFeignClient.subAssetAppPage(queryDto);
    }

    @Override
    public AssetRelationAppDto queryBySubAsset(Long subAssetId) {
        return assetRelationFeignClient.getBySubAssetIdApp(subAssetId);
    }
}
