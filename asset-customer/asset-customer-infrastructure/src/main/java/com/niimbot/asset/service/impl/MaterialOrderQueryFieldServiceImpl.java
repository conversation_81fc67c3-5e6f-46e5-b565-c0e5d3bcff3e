package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.QueryConditionType;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.AbstractOrderQueryFieldService;
import com.niimbot.asset.service.feign.MaterialOrderFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/17 15:25
 */
@Slf4j
@Service
public class MaterialOrderQueryFieldServiceImpl extends AbstractOrderQueryFieldService {

    @Autowired
    private MaterialOrderFeignClient orderFeignClient;

    @Override
    public List<QueryConditionDto> orderQueryView(Integer orderType) {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllQueryField = orderQueryField(orderType);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.MATERIAL_ORDER_TYPE_QUERY.get(orderType));
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });
        // 查询仓库不需要盘点过滤
        result.stream().filter(f -> FormFieldCO.YZC_REPOSITORY.equals(f.getType()))
                .forEach(cd -> {
                    JSONObject fieldProps = cd.getFieldProps();
                    if (fieldProps != null) {
                        JSONObject extProps = fieldProps.getJSONObject("extProps");
                        if (extProps != null) {
                            // 更新url为不过滤仓库
                            String url = extProps.getString("url");
                            if (url.endsWith("/withOutInventory")) {
                                extProps.put("url", url.replace("/withOutInventory", ""));
                            }
                            // 更新selectorUrl为不过滤仓库
                            JSONObject selectorUrl = extProps.getJSONObject("selectorUrl");
                            if (selectorUrl != null && selectorUrl.containsKey("get")) {
                                String getUrl = selectorUrl.getString("get");
                                if (getUrl.endsWith("/filter")) {
                                    selectorUrl.put("get", getUrl.replace("/filter", ""));
                                    extProps.put("selectorUrl", selectorUrl);
                                }
                            }
                        }
                    }
                });
        return result;
    }

    @Override
    public Boolean orderHeadField(OrderQueryHeadConfigDto config) {
        return queryConditionConfigFeignClient.saveOrUpdate(
                new QueryConditionConfigDto(config.toJson(), QueryFieldConstant.MATERIAL_ORDER_TYPE_HEAD.get(config.getOrderType())));
    }

    @Override
    public QueryHeadConfigDto orderHeadField(Integer orderType) {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(
                QueryFieldConstant.MATERIAL_ORDER_TYPE_HEAD.get(orderType));
        JSON conditionJson = one.getConditions();
        QueryHeadConfigDto headConfigDto = conditionJson.toJavaObject(QueryHeadConfigDto.class);
        if (BooleanUtil.isTrue(one.getSysConfig())) {
            List<String> conditions = headConfigDto.getConditions();
            FormVO formVO = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
            List<QueryConditionDto> assetAllHeadField = allHeadField(orderType, formVO);
            // 添加 top10
            int len = Math.min(assetAllHeadField.size(), 10);
            AtomicInteger count = new AtomicInteger(len);
            for (QueryConditionDto f : assetAllHeadField) {
                if (!conditions.contains(f.getCode())) {
                    if (count.getAndDecrement() >= 0) {
                        conditions.add(f.getCode());
                    } else {
                        break;
                    }
                }
            }
        }
        return headConfigDto;
    }


    @Override
    public List<AssetHeadDto> orderHeadView(Integer orderType) {
        FormVO formVO = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        QueryConditionSortDto querySort = orderFeignClient.sortField(orderType);
        List<String> querySortList = querySort.getSortList().stream()
                .map(QueryConditionSortDto.Field::getValue).collect(Collectors.toList());

        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllHeadField = allHeadField(orderType, formVO);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllHeadField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.MATERIAL_ORDER_TYPE_HEAD.get(orderType));
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        List<String> selectConditions = generalDto.getConditions();
        selectConditions.forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });
        // 如果是系统的，需要补属性
        if (BooleanUtil.isTrue(configDto.getSysConfig())) {
            // 添加 top10
            int len = Math.min(assetAllHeadField.size(), 10);
            AtomicInteger count = new AtomicInteger(len);
            for (QueryConditionDto f : assetAllHeadField) {
                if (!selectConditions.contains(f.getCode())) {
                    if (count.getAndDecrement() >= 0) {
                        result.add(f);
                    } else {
                        break;
                    }
                }
            }
        }

        Map<String, String> transCodeMap = new HashMap<>();
        transCodeMap.put(QueryFieldConstant.FIELD_CREATE_BY, QueryFieldConstant.FIELD_CREATE_BY + "Text");
        Map<String, JSONObject> fieldPropsMap = new HashMap<>();
        JSONObject dateFormat = new JSONObject();
        dateFormat.put("dateFormatType", "yyyy-MM-dd");
        fieldPropsMap.put(QueryFieldConstant.FIELD_CREATE_TIME, dateFormat);
        for (FormFieldCO formFieldCO : formVO.getFormFields()) {
            fieldPropsMap.put(formFieldCO.getFieldCode(), formFieldCO.getFieldProps());
            if (StrUtil.isNotEmpty(formFieldCO.getTranslationCode())) {
                transCodeMap.put(formFieldCO.getFieldCode(), formFieldCO.getTranslationCode());
            }
        }

        List<AssetHeadDto> headList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            QueryConditionDto conditionDto = result.get(i);
            AssetHeadDto headDto = new AssetHeadDto();
            headDto.setCode(conditionDto.getCode());
            headDto.setName(conditionDto.getName());
            headDto.setType(conditionDto.getType());
            headDto.setIsLock(false);
            headDto.setFieldProps(fieldPropsMap.get(conditionDto.getCode()));
            headDto.setSort(querySortList.contains(conditionDto.getCode()));
            String tranCode = transCodeMap.get(conditionDto.getCode());
            if (FormFieldCO.NUMBER_INPUT.equals(conditionDto.getType())
                    && "2".equals(conditionDto.getFieldProps().getString("numberFormatType"))) {
                tranCode = "%";
            }
            headDto.setTranslationCode(
                    StrUtil.isNotEmpty(tranCode) ? tranCode : "");
            if (i < generalDto.getLockNum()) {
                headDto.setIsLock(true);
            }
            if (ListUtil.of(QueryFieldConstant.FIELD_APPROVE_STATUS, QueryFieldConstant.FIELD_GRANT_STATUS)
                    .contains(conditionDto.getCode())) {
                headDto.setTranslationCode(conditionDto.getCode() + "Text");
            }
            headList.add(headDto);
        }
        return headList;
    }


    @Override
    public List<QueryConditionDto> activitiQueryField(Integer orderType) {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();
        queryConditionDtos.add(toActivitiConditionDto(QueryFieldConstant.SUBMITTER_ORG));
        queryConditionDtos.add(toActivitiConditionDto(QueryFieldConstant.MATERIAL_CATEGORY));
        if (AssetConstant.ORDER_TYPE_MATERIAL_CK == orderType
                || AssetConstant.ORDER_TYPE_MATERIAL_DB == orderType
                || AssetConstant.ORDER_TYPE_MATERIAL_LY == orderType
                || AssetConstant.ORDER_TYPE_MATERIAL_RK == orderType) {
            queryConditionDtos.add(toActivitiConditionDto(QueryFieldConstant.MATERIALTOTAL_MONEY));
        }
        FormVO formVO = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE,
                    FormFieldCO.YZC_MATERIAL_SERIALNO,
                    FormFieldCO.YZC_MATERIAL_NAME,
                    FormFieldCO.YZC_ASSOCIATION_TABLE)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        // 日期没有动态时间操作符
        List<QueryTypeDto> dateQueryType = operatorMap.get(FormFieldCO.DATETIME);
        if (CollUtil.isNotEmpty(dateQueryType)) {
            dateQueryType = dateQueryType.stream().filter(f -> !QueryConditionType.LAST_DAYS.getCode().equals(f.getQuery())).collect(Collectors.toList());
            operatorMap.put(FormFieldCO.DATETIME, dateQueryType);
        }
        // 员工没有为自己的
        List<QueryTypeDto> empQueryType = operatorMap.get(FormFieldCO.YZC_EMP);
        if (CollUtil.isNotEmpty(empQueryType)) {
            empQueryType = empQueryType.stream().filter(f -> !QueryConditionType.IS_ME.getCode().equals(f.getQuery())).collect(Collectors.toList());
            operatorMap.put(FormFieldCO.YZC_EMP, empQueryType);
        }
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }


    private QueryConditionDto toActivitiConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.MATERIAL_ACTIVITI_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            if (AssetConstant.ED_YZC_MATERIAL_CATE.equals(field.getType())) {
                FormBaseFieldCO baseFieldCO = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_MATERIAL_CATE);
                conditionDto.setFieldProps(baseFieldCO.getFieldProps());
            } else {
                conditionDto.setFieldProps(new JSONObject());
            }
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    @Override
    protected void specialOrderQueryField(Integer orderType, List<QueryConditionDto> queryConditionDtos) {
        if (AssetConstant.ORDER_TYPE_MATERIAL_LY == orderType) {
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_GRANT_STATUS));
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_TK == orderType) {
            FormBaseFieldCO repoBaseField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_REPOSITORY);
            QueryConditionDto queryConditionDto = toQueryConditionDto(QueryFieldConstant.FIELD_TK_IN_REPO);
            queryConditionDto.setFieldProps(repoBaseField.getFieldProps());
            queryConditionDtos.add(queryConditionDto);
        }
    }

    @Override
    protected List<QueryConditionDto> allHeadField(Integer orderType, FormVO formVO) {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();

        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_APPROVE_STATUS));
        if (AssetConstant.ORDER_TYPE_MATERIAL_LY == orderType) {
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_GRANT_STATUS));
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_TK == orderType) {
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_TK_IN_REPO));
        }

        QueryConditionDto fieldSummaryCondition = toQueryConditionDto(QueryFieldConstant.FIELD_SUMMARY);
        if (fieldSummaryCondition != null) {
            queryConditionDtos.add(fieldSummaryCondition.setName(QueryFieldConstant.FIELD_SUMMARY_NAME.get(orderType)));
        }
        List<FormFieldCO> formFields = formVO.getFormFields();

        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_ORDER_NO));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }
}
