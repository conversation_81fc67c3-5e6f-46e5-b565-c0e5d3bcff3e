package com.niimbot.asset.security.service;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.system.CusMenuDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 * @since 2020/10/26 16:07
 */
@Component
public class SysPermissionService {

    @Autowired
    private CusMenuFeignClient cusMenuFeignClient;

    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(CusUserDto user) {
        Set<String> perms = new HashSet<>();
        // 管理员拥有所有权限
        if (BooleanUtil.isTrue(user.getIsAdmin())) {
            perms.add("*:*:*");
        } else {
            List<CusMenuDto> pcMenus = cusMenuFeignClient.configRoleMenuAppList();
            List<CusMenuDto> appMenus = cusMenuFeignClient.configRoleMenuPcList();
            pcMenus.addAll(appMenus);
            pcMenus.forEach(perm -> {
                if (StrUtil.isNotEmpty(perm.getPerms())) {
                    perms.addAll(Arrays.asList(perm.getPerms().trim().split(",")));
                }
            });
        }
        return perms;
    }

}
