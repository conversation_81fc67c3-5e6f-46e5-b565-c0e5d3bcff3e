package com.niimbot.asset.websocket.codec;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.websocket.msg.ConnectMessage;
import com.niimbot.asset.websocket.msg.HeartbeatMessage;
import com.niimbot.asset.websocket.msg.ImportImagesMessage;
import com.niimbot.asset.websocket.msg.ImportTaskMessage;
import com.niimbot.asset.websocket.msg.Message;
import com.niimbot.asset.websocket.msg.SignatureMessage;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.websocket.DecodeException;
import javax.websocket.Decoder;
import javax.websocket.EndpointConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息解码器
 *
 * <AUTHOR>
 * @date 2021/9/1 16:48
 */
@Slf4j
public class MessageTextDecoder implements Decoder.Text<Message> {
    private final Map<String, Type> typeMap = new ConcurrentHashMap<>();

    public MessageTextDecoder() {
        typeMap.put(Message.TYPE_CONNECT, ConnectMessage.class);
        typeMap.put(Message.TYPE_HEARTBEAT, HeartbeatMessage.class);
        typeMap.put(Message.TYPE_IMPORT_TASK, ImportTaskMessage.class);
        typeMap.put(Message.TYPE_IMPORT_IMAGES, ImportImagesMessage.class);
        typeMap.put(Message.TYPE_SIGNATURE, SignatureMessage.class);
    }

    @Override
    public Message decode(String s) throws DecodeException {
        JSONObject object = JSON.parseObject(s);
        String classKey = getClassKey(object);
        if (typeMap.containsKey(classKey)) {
            return JSONObject.parseObject(s, typeMap.get(classKey));
        }
        throw new ClassCastException(s);
    }

    @Override
    public boolean willDecode(String s) {
        return true;
    }

    @Override
    public void init(EndpointConfig endpointConfig) {

    }

    @Override
    public void destroy() {

    }

//    private String getClassKey(Class<?> clazz) {
//        Field[] fields = ReflectUtil.getFields(clazz);
//        return Stream.of(fields).filter(f -> !(f.getModifiers() == Modifier.FINAL
//                || f.getModifiers() == Modifier.STATIC
//                || f.getModifiers() == (Modifier.PRIVATE | Modifier.STATIC | Modifier.FINAL)))
//                .map(Field::getName)
//                .sorted().collect(Collectors.joining());
//    }

//    private String getClassKey(JSONObject object) {
//        List<String> keys = new ArrayList<>(object.keySet());
//        return keys.stream().sorted().collect(Collectors.joining());
//    }

    private String getClassKey(JSONObject object) {
        return object.getString("type");
    }
}
