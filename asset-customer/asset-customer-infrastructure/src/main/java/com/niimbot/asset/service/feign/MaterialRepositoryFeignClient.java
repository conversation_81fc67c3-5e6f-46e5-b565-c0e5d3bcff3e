package com.niimbot.asset.service.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.material.MaterialRepositoryImportDto;
import com.niimbot.material.MaterialRepositorySearchDto;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialRepositoryFeignClient {

    /**
     * 添加仓库
     *
     * @param repository 请求参数
     * @return true or false
     * <AUTHOR>
     */
    @PostMapping("/server/material/repository")
    Long add(@RequestBody MaterialRepositoryDto repository);

    /**
     * 删除多个仓库
     *
     * @param ids 仓库IDs
     * @return true or false
     */
    @DeleteMapping("/server/material/repository")
    List<AuditableOperateResult> delete(@RequestBody List<Long> ids);

    /**
     * 更新仓库
     *
     * @param repository 仓库信息
     * @return true or false
     * <AUTHOR>
     */
    @PutMapping("/server/material/repository")
    Boolean update(@RequestBody MaterialRepositoryDto repository);

    /**
     * 仓库详情
     *
     * @param id 仓库ID
     * @return {@link MaterialRepositoryDto}
     * <AUTHOR>
     */
    @GetMapping(value = "/server/material/repository/{id}")
    MaterialRepositoryDto detail(@PathVariable(value = "id") Long id);

    @PostMapping(value = "/server/material/repository/listByIds")
    List<MaterialRepositoryDto> listByIds(List<Long> repoIds);

    /**
     * 列表查询 带权限
     *
     * @return {@link List<MaterialRepositoryDto>}
     * <AUTHOR>
     */
    @GetMapping(value = "/server/material/repository/listPermission")
    List<MaterialRepositoryDto> listPermission(@SpringQueryMap MaterialRepositorySearchDto searchDto);

    @GetMapping(value = "/server/material/repository/listAll")
    List<MaterialRepositoryDto> listAll();

    /**
     * 报表导出
     *
     * @return {@link List<MaterialRepositoryDto>}
     * <AUTHOR>
     */
    @PostMapping(value = "/server/material/repository/excel")
    List<MaterialRepositoryDto> excel(@RequestBody MaterialRepositorySearchDto dto);

    /**
     * 搜索分页
     *
     * @return {@link Page <MaterialRepositoryDto>}
     */
    @GetMapping("/server/material/repository/search")
    PageUtils<MaterialRepositoryDto> search(@SpringQueryMap MaterialRepositorySearchDto dto);

    /**
     * 获取推荐码
     *
     * @return code
     */
    @GetMapping("/server/material/repository/recommendCode")
    String recommendCode();

    @GetMapping(value = "server/material/repository/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @DeleteMapping(value = "server/material/repository/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/material/repository/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/material/repository/saveSheetData")
    Boolean saveSheetData(MaterialRepositoryImportDto importDto);

}
