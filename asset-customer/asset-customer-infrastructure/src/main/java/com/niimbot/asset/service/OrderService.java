package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.means.ExportOrderAssetsHeaderDto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/** created by chen.y on 2021/3/4 15:05 */
public interface OrderService {

    /**
     * 查询app单据列表
     *
     * @param dto 分页查询
     * @return 单据列表
     */
    PageUtils<Record> page(AsOrderQueryDto dto);

    /**
     * 转换单据为App显示
     *
     * @param page 单据分页结果
     * @return app记录信息
     */
    PageUtils<Record> handleAppRecord(PageUtils<AsOrderDto> page);

    /**
     * 转换单据为App显示
     *
     * @param list 单据
     * @return app记录信息
     */
    List<Record> handleAppRecord(List<AsOrderDto> list);

    @Data
    @Accessors(chain = true)
    class Record implements Serializable {
        private Value id;
        private Value orderType;
        private Value orderNo;
        private Value approveStatus;
        private List<Value> list;
    }

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    class Value implements Serializable {
        private String code;
        private String name;
        private Object data;
    }

    /**
     * 资产单据导出
     *
     * @param orderQueryDto
     * @return 结果
     */
    ExportResponse exportOrderCard(AsOrderQueryDto orderQueryDto);

    /**
     * 资产单据资产列表导出表头
     *
     * @param orderType
     * @return
     */
    ExportOrderAssetsHeaderDto getOrderAssetsHeader(Integer orderType);

    /**
     * 资产单据资产列表导出
     *
     * @param dto
     * @param orders
     * @param assetsMap
     * @return 结果
     */
    ExportResponse exportOrderAssets(
            AsOrderQueryDto dto,
            List<JSONObject> orders,
            Map<Long, List<JSONObject>> assetsMap);

    /**
     * 资产报修单据导出
     *
     * @param orderQueryDto
     * @return 结果
     */
    ExportResponse exportRepairReportOrderCard(AsOrderQueryDto orderQueryDto);

    /**
     * 资产维修单据导出
     *
     * @param orderQueryDto
     * @return 结果
     */
    ExportResponse exportRepairOrderCard(AsOrderQueryDto orderQueryDto);

    /**
     * 资产保养单据导出
     *
     * @param orderQueryDto
     * @return 结果
     */
    ExportResponse exportMaintainOrderCard(AsOrderQueryDto orderQueryDto);

    /**
     * 资产入库单据导出
     *
     * @param orderQueryDto
     * @return 结果
     */
    ExportResponse exportStoreOrderCard(AsOrderQueryDto orderQueryDto);
}
