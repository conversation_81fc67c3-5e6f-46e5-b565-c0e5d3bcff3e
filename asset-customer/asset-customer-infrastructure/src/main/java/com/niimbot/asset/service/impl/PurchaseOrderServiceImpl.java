package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.niimbot.AbstractIdOrderDto;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.OrderJsonUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.PurchaseOrderService;
import com.niimbot.asset.service.PurchaseQueryFieldService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.*;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AsOrderDetailDto;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.means.CommonFileDto;
import com.niimbot.means.ExportOrderAssetsHeaderDto;
import com.niimbot.purchase.*;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportTaskDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/20 15:08
 */
@Slf4j
@Service
public class PurchaseOrderServiceImpl extends AbstractExcelExportService implements PurchaseOrderService {

    @Autowired
    private PurchaseApplyFeignClient purchaseApplyFeignClient;
    @Autowired
    private PurchaseOrderFeignClient purchaseOrderFeignClient;
    @Autowired
    private PurchaseCommonFeignClient purchaseCommonFeignClient;
    @Autowired
    private PurchaseQueryFieldService purchaseQueryFieldService;
    @Autowired
    private AsOrderUtil asOrderUtil;
    @Autowired
    private ActWorkflowFeignClient workflowFeignClient;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DictConvertUtil dictConvertUtil;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private FileUploadConfig fileUploadConfig;
    private final ImportTaskFeignClient importTaskFeignClient;

    protected PurchaseOrderServiceImpl(ImportTaskFeignClient importTaskFeignClient) {
        super(importTaskFeignClient);
        this.importTaskFeignClient = importTaskFeignClient;
    }

    @Getter
    @AllArgsConstructor
    private static class OrderCardExportData extends AbsExportData {
        private JSONObject orderData;
        private WorkflowApproveInfoDto approveInfo;
    }

    @Getter
    @AllArgsConstructor
    private static class OrderExportData extends AbsExportData {
        private List<JSONObject> orders;
        private Map<Long, List<JSONObject>> assetsMap;
    }

    private static final String DETAILS = "details";
    private static final String ID = "id";
    private static final String ORDER_TYPE = "orderType";
    private static final String ORDER_NO = "orderNo";
    private static final String CREATE_TIME = "createTime";

    @Data
    @Accessors(chain = true)
    private static class GlobalCache {
        private Long company;
        private String downUrl;
    }

    @Override
    public ExportResponse exportOrderCard(PurchaseOrderQueryDto orderQueryDto) {
        List<JSONObject> dataList = new ArrayList<>();
        List<AsOrderDetailDto> orderDetailDtoList;
        if (AssetConstant.ORDER_TYPE_PURCHASE == orderQueryDto.getOrderType()) {
            List<PurchaseApplyDto> list = purchaseApplyFeignClient.list(orderQueryDto);
            if (CollUtil.isEmpty(list)) {
                BusinessExceptionUtil.throwException("无单据数据, 无法导出");
            }
            dictConvertUtil.convertToDictionary(list);
            List<Long> orderIds = new ArrayList<>();
            list.forEach(o -> {
                orderIds.add(o.getId());
                o.setOrderType(AssetConstant.ORDER_TYPE_PURCHASE);
            });
            List<PurchaseApplyDetailDto> details = purchaseApplyFeignClient.listDetailsByOrderId(orderIds);
            Map<Long, List<PurchaseApplyDetailDto>> detailMap = details.stream().collect(
                    Collectors.groupingBy(PurchaseApplyDetailDto::getPurchaseApplyId));
            // 补上审批流数据
            orderDetailDtoList = getWorkFlowData(list);
            orderDetailDtoList.forEach(o -> {
                List<JSONObject> data = detailMap.get(o.getOrder().getLong(ID)).stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
                dataList.addAll(data);
                o.getOrder().put(DETAILS, data);
            });
        } else {
            List<PurchaseOrderDto> list = purchaseOrderFeignClient.list(orderQueryDto);
            if (CollUtil.isEmpty(list)) {
                BusinessExceptionUtil.throwException("无单据数据, 无法导出");
            }
            dictConvertUtil.convertToDictionary(list);
            List<Long> orderIds = new ArrayList<>();
            list.forEach(o -> {
                orderIds.add(o.getId());
                o.setOrderType(AssetConstant.ORDER_TYPE_PURCHASE_ORDER);
            });
            List<PurchaseOrderDetailDto> details = purchaseOrderFeignClient.listDetailsByOrderId(orderIds);
            Map<Long, List<PurchaseOrderDetailDto>> detailMap = details.stream().collect(
                    Collectors.groupingBy(PurchaseOrderDetailDto::getPurchaseOrderId));
            // 补上审批流数据
            orderDetailDtoList = getWorkFlowData(list);
            orderDetailDtoList.forEach(o -> {
                List<JSONObject> data = detailMap.get(o.getOrder().getLong(ID)).stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
                dataList.addAll(data);
                o.getOrder().put(DETAILS, data);
            });
        }
        if (CollUtil.isEmpty(orderDetailDtoList)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        Edition.weixin(() -> {
            if (orderDetailDtoList.size() > 100) {
                BusinessExceptionUtil.throwException("单据卡片单次最多允许下载100条数据。超过100条请分次下载");
            }
        });

        ExportResponse exportResponse = new ExportResponse();
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(orderQueryDto.getOrderType(), "card"), orderQueryDto.getOrderType());
        exportParams.setQueryCondition(JsonUtil.toJsonObject(orderQueryDto));
        exportParams.setExportUrl(OrderTypeNewEnum.getByType(orderQueryDto.getOrderType()).getExportUrl());

        //敏感数据处理
        if (CollUtil.isNotEmpty(orderDetailDtoList)) {
            desensitizationDataUtil.handleSensitiveField(orderDetailDtoList.stream().map(AsOrderDetailDto::getOrder).collect(Collectors.toList()), SensitiveObjectTypeEnum.PURCHASE.getCode());
        }
        if (CollUtil.isNotEmpty(dataList)) {
            desensitizationDataUtil.handleSensitiveField(dataList, SensitiveObjectTypeEnum.PURCHASE.getCode());
        }

        List<AbsExportData> exportDataList = orderDetailDtoList.stream().map(dto ->
                new OrderCardExportData(dto.getOrder(), dto.getApproveInfo())).collect(Collectors.toList());
        exportParams.setExportDataList(exportDataList);

        if (orderDetailDtoList.size() > 1000) {
            exportResponse.setSync(true);
            sync(exportParams, this::executeOrderCardExport);
        } else {
            exportResponse.setSync(false);
            exportResponse.setPath(async(() ->
                    executeOrderCardExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(exportResponse.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return exportResponse;
    }

    @Override
    public ExportResponse exportOrderDetail(PurchaseOrderQueryDto dto, List<JSONObject> orders, Map<Long, List<JSONObject>> itemsMap) {
        //数据脱敏处理
        if (CollUtil.isNotEmpty(itemsMap) && CollUtil.isNotEmpty(itemsMap.values())) {
            List<JSONObject> assetData = itemsMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            desensitizationDataUtil.handleSensitiveField(assetData, SensitiveObjectTypeEnum.PURCHASE.getCode());
        }

        ExportResponse exportResponse = new ExportResponse();
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(dto.getOrderType(), "list"), dto.getOrderType());
        exportParams.setQueryCondition(JsonUtil.toJsonObject(dto));
        exportParams.setExportUrl(OrderTypeNewEnum.getByType(dto.getOrderType()).getDetailExportUrl());
        exportParams.setExportDataList(ListUtil.of(new OrderExportData(orders, itemsMap)));
        exportParams.setCount(orders.size());
        if (orders.size() > 1000) {
            exportResponse.setSync(true);
            sync(exportParams, this::executeOrderAssetsExport);
        } else {
            exportResponse.setSync(false);
            exportResponse.setPath(async(() ->
                    executeOrderAssetsExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(exportResponse.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return exportResponse;
    }

    private String executeOrderAssetsExport(Long taskId, ExportParams exportParams) {
        String path = StrUtil.EMPTY;
        Integer orderType = exportParams.getOrderType();
        File tempPath = getOrderDetailsTempPath(exportParams);

        ExportOrderAssetsHeaderDto orderAssetsHeader = getOrderDetailsHeader(orderType);
        LinkedHashMap<String, String> headerData = orderAssetsHeader.getHeader();
        Map<String, String> codeTypeMap = orderAssetsHeader.getCodeTypeMap();
        File outputFile = null;
        try {
            String excelName = getOrderDetailsExcelName(exportParams.getOrderType());
            outputFile = new File(tempPath.getPath() + "/" + excelName);
            // 本地文件路径
            String localPath = outputFile.getPath();
            int headLen = headerData.size();
            ExcelWriter writer = ExcelUtil.getWriter(true);
            Sheet sheet = writer.getSheet();
            // 设置边框
            StyleSet styleSet = writer.getStyleSet();
            styleSet.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
            styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.TOP);

            // 设置表头的cellStyle
            CellStyle cellStyle = writer.createCellStyle();
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setAlignment(HorizontalAlignment.LEFT);

            // 写入文件标题
            String headerText = OrderTypeNewEnum.getByType(exportParams.getOrderType()).getName();
            writer.merge(0, 0, 0, headLen - 1, headerText, false);
            Cell title = writer.getCell(0, 0);
            CellStyle commentStyle = writer.createCellStyle();
            commentStyle.setAlignment(HorizontalAlignment.CENTER);
            commentStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
            title.setCellStyle(commentStyle);

            // 写入表头
            AtomicInteger rowIdx = new AtomicInteger(1);
            List<String> headCodeList = new ArrayList<>();
            AtomicInteger cellIdx = new AtomicInteger(0);
            Row header = writer.getOrCreateRow(rowIdx.getAndIncrement());
            headerData.forEach((k, v) -> {
                int idx = cellIdx.getAndIncrement();
                headCodeList.add(k);
                Cell cell = header.createCell(idx);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(v);
                // 调整每一列宽度
                sheet.autoSizeColumn((short) idx);
                // 解决自动设置列宽中文失效的问题
                sheet.setColumnWidth(idx, sheet.getColumnWidth(idx) * 17 / 10);
            });

            Map<Integer, JSONObject> mergeOrderMap = new LinkedHashMap<>();
            Map<Integer, Integer> mergeRangeMap = new LinkedHashMap<>();
            int idx = rowIdx.get();
            int assetStart = orderAssetsHeader.getOrderHeaderSize() - 1;

            OrderExportData orderExportData = (OrderExportData) exportParams.getExportDataList().get(0);
            List<JSONObject> orders = orderExportData.getOrders();
            Map<Long, List<JSONObject>> itemsMap = orderExportData.getAssetsMap();
            for (JSONObject order : orders) {
                Long orderId = order.getLong(OrderJsonUtil.convertKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, "id"));
                List<JSONObject> assets = itemsMap.get(orderId);
                int assetSize = assets.size();
                for (JSONObject asset : assets) {
                    idx = rowIdx.getAndIncrement();
                    Row row = writer.getOrCreateRow(idx);
                    for (int i = assetStart; i < headCodeList.size(); i++) {
                        String code = headCodeList.get(i);
                        Cell cell = row.createCell(i);
                        cell.setCellStyle(cellStyle);
                        if (FormFieldCO.NUMBER_INPUT.equals(codeTypeMap.get(code))) {
                            Double num = Convert.toDouble(asset.get(code));
                            if (num != null) {
                                cell.setCellValue(num);
                            } else {
                                String numStr = Convert.toStr(asset.get(code), StrUtil.EMPTY);
                                cell.setCellValue(numStr);
                            }
                        } else {
                            String str = Convert.toStr(asset.get(code), StrUtil.EMPTY);
                            cell.setCellValue(str);
                        }
                    }
                }
                // 需要跨行的单据
                int num = idx - assetSize + 1;
                mergeOrderMap.put(num, order);
                mergeRangeMap.put(num, assetSize);
            }

            mergeOrderMap.forEach((rowNum, order) -> {
                Integer mergeRows = mergeRangeMap.get(rowNum);
                Row row = writer.getOrCreateRow(rowNum);
                for (int i = 0; i < orderAssetsHeader.getOrderHeaderSize(); i++) {
                    String code = headCodeList.get(i);
                    Cell cell = row.createCell(i);
                    cell.setCellStyle(cellStyle);
                    if (FormFieldCO.NUMBER_INPUT.equals(codeTypeMap.get(code))) {
                        Double num = Convert.toDouble(order.get(code));
                        if (num != null) {
                            if (mergeRows > 1) {
                                writer.merge(rowNum, (rowNum + mergeRows - 1), i, i, num, false);
                            } else {
                                cell.setCellValue(num);
                            }
                        } else {
                            String numStr = Convert.toStr(order.get(code), StrUtil.EMPTY);
                            if (mergeRows > 1) {
                                writer.merge(rowNum, (rowNum + mergeRows - 1), i, i, numStr, false);
                            } else {
                                cell.setCellValue(numStr);
                            }
                        }
                    } else {
                        String str = Convert.toStr(order.get(code), StrUtil.EMPTY);
                        if (mergeRows > 1) {
                            writer.merge(rowNum, (rowNum + mergeRows - 1), i, i, str, false);
                        } else {
                            cell.setCellValue(str);
                        }
                    }
                }
            });

            //设置输出文件路径
            writer.setDestFile(outputFile);
            writer.flush();
            writer.close();

            // 上传文件到oss 单个文件或者zip包
            path = fileUploadService.putFile(getOrderDetailsDestPath(exportParams), localPath);
            // 更新任务url
            if (taskId != null && StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
            AuditLogs.sendOrderExportRecord(exportParams.getOrderType(), exportParams.getExportDataList().size(), Auditable.Action.EXP_OR_LT);
        } catch (Exception e) {
            log.error("采购单据明细列表导出失败", e);
        } finally {
            FileUtil.del(outputFile);
        }
        return path;
    }

    private ExportOrderAssetsHeaderDto getOrderDetailsHeader(int orderType) {
        List<AssetHeadDto> orderHeadDtos = purchaseQueryFieldService.orderHeadView(orderType);
        Map<String, String> codeTypeMap = new HashMap<>();
        orderHeadDtos = orderHeadDtos.stream()
                .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                .collect(Collectors.toList());

        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
        for (AssetHeadDto head : orderHeadDtos) {
            String code = OrderJsonUtil.convertKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, head.getCode());
            if (QueryFieldConstant.FIELD_APPROVE_STATUS.equals(head.getCode())) {
                headerData.put(code + "Text", head.getName());
                codeTypeMap.put(code + "Text", head.getType());
            } else {
                if (StrUtil.isNotBlank(head.getTranslationCode())) {
                    String translationCode = OrderJsonUtil.convertKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, head.getTranslationCode());
                    headerData.put(translationCode, head.getName());
                    codeTypeMap.put(translationCode, head.getType());
                } else {
                    headerData.put(code, head.getName());
                    codeTypeMap.put(code, head.getType());
                }
            }
        }

        // 明细表头
        if (AssetConstant.ORDER_TYPE_PURCHASE == orderType) {
            headerData.putAll(PurchaseApplyDetailDto.getExportHeader());
        } else {
            headerData.putAll(PurchaseOrderDetailDto.getExportHeader());
        }
        return new ExportOrderAssetsHeaderDto()
                .setOrderHeaderSize(orderHeadDtos.size())
                .setHeader(headerData)
                .setCodeTypeMap(codeTypeMap);
    }

    private String executeOrderCardExport(Long taskId, ExportParams exportParams) {
        String path = StrUtil.EMPTY;
        // 获取临时存放路径
        File tempPath = getTempPath(exportParams);
        // 单据类型
        Integer orderType = exportParams.getOrderType();

        // 获取单据动态表头数据
        FormVO form = purchaseCommonFeignClient.getForm(orderType);
        List<FormFieldCO> headDtos = form.getFormFields().stream()
                .filter(f -> !AssetConstant.ED_SPLIT_LINE.equals(f.getFieldType()))
                .filter(f -> !f.isHidden())
                .collect(Collectors.toList());
        File outputFile = null;
        try {
            // 本地文件路径
            String localPath = "";
            for (AbsExportData absExportData : exportParams.getExportDataList()) {
                OrderCardExportData exportData = (OrderCardExportData) absExportData;
                JSONObject order = exportData.getOrderData();
                List<WorkflowApproveInfoDto.WorkflowExecuteStep> executeList =
                        exportData.getApproveInfo().getExecuteList();
                ExcelWriter writer = ExcelUtil.getBigWriter(5000);

                String excelName = getExcelName(exportParams.getOrderType(), order);
                outputFile = new File(tempPath.getPath() + "/" + excelName);
                localPath = outputFile.getPath();

                writer.setColumnWidth(-1, 25);
                // 获取 styleSet
                StyleSet styleSet = writer.getStyleSet();
                // 设置单元格文本自动换行
                styleSet.setWrapText();
                styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
                writer.setStyleSet(styleSet);

                // 设置表头的cellStyle
                CellStyle blackHeadStyle = writer.createCellStyle();
                blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
                blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                blackHeadStyle.setBorderBottom(BorderStyle.THIN);
                blackHeadStyle.setBorderTop(BorderStyle.THIN);
                blackHeadStyle.setBorderLeft(BorderStyle.THIN);
                blackHeadStyle.setBorderRight(BorderStyle.THIN);
                Font black = writer.createFont();
                black.setBold(true);
                black.setFontHeightInPoints((short) 15);
                black.setColor(IndexedColors.BLACK.getIndex());
                blackHeadStyle.setFont(black);

                CellStyle blackHeadStyle2 = writer.createCellStyle();
                blackHeadStyle2.setAlignment(HorizontalAlignment.LEFT);
                blackHeadStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
                blackHeadStyle2.setBorderBottom(BorderStyle.THIN);
                blackHeadStyle2.setBorderTop(BorderStyle.THIN);
                blackHeadStyle2.setBorderLeft(BorderStyle.THIN);
                blackHeadStyle2.setBorderRight(BorderStyle.THIN);
                Font black2 = writer.createFont();
                black2.setFontHeightInPoints((short) 13);
                black2.setBold(true);
                black2.setColor(IndexedColors.BLACK.getIndex());
                blackHeadStyle2.setFont(black2);

                CellStyle blackHeadStyle3 = writer.createCellStyle();
                blackHeadStyle3.setAlignment(HorizontalAlignment.LEFT);
                blackHeadStyle3.setVerticalAlignment(VerticalAlignment.CENTER);
                blackHeadStyle3.setBorderBottom(BorderStyle.THIN);
                blackHeadStyle3.setBorderTop(BorderStyle.THIN);
                blackHeadStyle3.setBorderLeft(BorderStyle.THIN);
                blackHeadStyle3.setBorderRight(BorderStyle.THIN);
                Font black3 = writer.createFont();
                black3.setFontHeightInPoints((short) 13);
                black3.setColor(IndexedColors.BLACK.getIndex());
                blackHeadStyle3.setFont(black3);
                blackHeadStyle3.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
                blackHeadStyle3.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                CellStyle cellStyle = writer.createCellStyle();
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setWrapText(true);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);

                CellStyle cellStyle1 = writer.createCellStyle();
                cellStyle1.setAlignment(HorizontalAlignment.LEFT);
                cellStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle1.setBorderBottom(BorderStyle.THIN);
                cellStyle1.setBorderTop(BorderStyle.THIN);
                cellStyle1.setBorderLeft(BorderStyle.THIN);
                cellStyle1.setBorderRight(BorderStyle.THIN);
                cellStyle1.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
                cellStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                int rowIndex = 0;

                // 写入单据信息
                rowIndex = buildOrderExcel(blackHeadStyle, blackHeadStyle2, cellStyle1, writer, rowIndex, order,
                        headDtos, exportParams);
                // 写入明细
                rowIndex = buildDetailExcel(blackHeadStyle2, blackHeadStyle3, cellStyle, writer, rowIndex, order);
                // 写入审批信息
                buildApproveExcel(blackHeadStyle2, cellStyle, writer, rowIndex, order, executeList);

                //设置输出文件路径
                writer.setDestFile(outputFile);
                writer.close();
            }

            // 多个需要压缩成zip
            if (exportParams.getExportDataList().size() > 1) {
                File zip = ZipUtil.zip(tempPath);
                localPath = zip.getPath();
            }

            // 上传文件到oss 单个文件或者zip包
            path = fileUploadService.putFile(getDestPath(exportParams), localPath);
            // 更新任务url
            if (taskId != null && StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
            AuditLogs.sendOrderExportRecord(exportParams.getOrderType(), exportParams.getExportDataList().size(), Auditable.Action.EXP_OR_CD);
        } catch (Exception e) {
            log.error("采购单据卡片导出失败", e);
        } finally {
            FileUtil.del(outputFile);
        }
        return path;
    }

    private int buildDetailExcel(CellStyle headStyle,
                                 CellStyle titleStyle,
                                 CellStyle cellStyle,
                                 ExcelWriter writer,
                                 int rowIndex,
                                 JSONObject order) {
        Integer orderType = order.getInteger(ORDER_TYPE);

        writer.merge(rowIndex, rowIndex++, 0, 5, "物品信息", false);
        writer.setStyle(headStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);

        writer.setCurrentRow(rowIndex);
        writer.writeCellValue("A" + (rowIndex + 1), "物品编码");
        writer.setStyle(titleStyle, "A" + (rowIndex + 1));
        writer.writeCellValue("B" + (rowIndex + 1), "物品名称");
        writer.setStyle(titleStyle, "B" + (rowIndex + 1));
        writer.writeCellValue("C" + (rowIndex + 1), "物品信息");
        writer.setStyle(titleStyle, "C" + (rowIndex + 1));
        if (AssetConstant.ORDER_TYPE_PURCHASE == orderType) {
            writer.writeCellValue("D" + (rowIndex + 1), "申购数量");
            writer.setStyle(titleStyle, "D" + (rowIndex + 1));
            writer.merge(rowIndex, rowIndex, 4, 5, "申购信息", false);
            writer.setStyle(titleStyle, "E" + (rowIndex + 1));
        } else {
            writer.writeCellValue("D" + (rowIndex + 1), "采购数量");
            writer.setStyle(titleStyle, "D" + (rowIndex + 1));
            writer.writeCellValue("E" + (rowIndex + 1), "采购单价");
            writer.setStyle(titleStyle, "E" + (rowIndex + 1));
            writer.writeCellValue("F" + (rowIndex + 1), "采购金额");
            writer.setStyle(titleStyle, "F" + (rowIndex + 1));
        }

        writer.setRowHeight(rowIndex++, 25);
        if (order.containsKey(DETAILS) && ObjectUtil.isNotEmpty(order.getJSONArray(DETAILS))) {
            List<JSONObject> detailDtoList = order.getJSONArray(DETAILS).toJavaList(JSONObject.class);
//            if (AssetConstant.ORDER_TYPE_PURCHASE == orderType) {
//                detailDtoList = order.getJSONArray(DETAILS)
//                        .toJavaList(PurchaseApplyDetailDto.class);
//            } else {
//                detailDtoList = order.getJSONArray(DETAILS)
//                        .toJavaList(PurchaseOrderDetailDto.class);
//            }
            writer.setCurrentRow(rowIndex);
            for (JSONObject dto : detailDtoList) {
                writer.writeCellValue("A" + (rowIndex + 1), dto.getString("code"));
                writer.setStyle(cellStyle, "A" + (rowIndex + 1));
                writer.writeCellValue("B" + (rowIndex + 1), dto.getString("name"));
                writer.setStyle(cellStyle, "B" + (rowIndex + 1));
                // 资产信息
                String itemInfo = "[品牌]：" + dto.getString("brand") + "\n" +
                        "[规格型号]：" + dto.getString("model") + "\n" +
                        "[计量单位]：" + (StrUtil.isNotBlank(dto.getString("unit")) ? dto.getString("unit") : "");
                writer.writeCellValue("C" + (rowIndex + 1), itemInfo);
                writer.setStyle(cellStyle, "C" + (rowIndex + 1));
                if (AssetConstant.ORDER_TYPE_PURCHASE == orderType) {
//                    PurchaseApplyDetailDto applyDetailDto = (PurchaseApplyDetailDto) dto;
                    writer.writeCellValue("D" + (rowIndex + 1), dto.getString("quantity"));
                    String expectedArrivalDateStr = "";
                    if (Objects.nonNull(dto.getDate("expectedArrivalDate"))) {
                        Date date = dto.getDate("expectedArrivalDate");
                        expectedArrivalDateStr = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    }
                    String purchaseInfo = "[预估单价]：" + (ObjectUtil.isNotNull(dto.getString("price")) ? dto.getString("price") : "") + "\n" +
                            "[期待到货时间]：" + expectedArrivalDateStr + "\n" +
                            "[备注]：" + (StrUtil.isNotBlank(dto.getString("remark")) ? dto.getString("remark") : "");
                    writer.merge(rowIndex, rowIndex, 4, 5, purchaseInfo, false);
                    writer.setStyle(cellStyle, "E" + (rowIndex + 1));
                } else {
//                    PurchaseOrderDetailDto orderDetailDto = (PurchaseOrderDetailDto) dto;
                    writer.writeCellValue("D" + (rowIndex + 1), dto.getString("quantity"));
                    writer.setStyle(cellStyle, "D" + (rowIndex + 1));
                    writer.writeCellValue("E" + (rowIndex + 1), (ObjectUtil.isNotNull(dto.getString("price")) ? dto.getString("price") : ""));
                    writer.setStyle(cellStyle, "E" + (rowIndex + 1));
                    if (StrUtil.isNotBlank(dto.getString("price")) && !"******".equalsIgnoreCase(dto.getString("price"))) {
                        writer.writeCellValue("F" + (rowIndex + 1), dto.getBigDecimal("price").multiply(BigDecimal.valueOf(dto.getLong("quantity"))).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                    } else {
                        writer.writeCellValue("F" + (rowIndex + 1), dto.getString("price"));
                    }
                    writer.setStyle(cellStyle, "F" + (rowIndex + 1));
                }
                writer.setRowHeight(rowIndex, 100);
                rowIndex++;
            }
        }
        return rowIndex;
    }

    private void buildApproveExcel(CellStyle headStyle,
                                   CellStyle cellStyle,
                                   ExcelWriter writer,
                                   int rowIndex,
                                   JSONObject order,
                                   List<WorkflowApproveInfoDto.WorkflowExecuteStep> executeList) {
        if (CollUtil.isEmpty(executeList)) {
            return;
        }

        writer.merge(rowIndex, rowIndex++, 0, 5, "审批信息", false);
        writer.setStyle(headStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);

        writer.setCurrentRow(rowIndex);
        writer.writeCellValue("A" + (rowIndex + 1), "审批结果：");
        writer.setStyle(cellStyle, "A" + (rowIndex + 1));
        String approveStatusText = Convert.toStr(order.getOrDefault("approveStatusText", ""));
        writer.merge(rowIndex, rowIndex, 1, 5, approveStatusText, false);
        writer.setStyle(cellStyle, "B" + (rowIndex + 1));
        writer.setRowHeight(rowIndex++, 25);

        int startRow = rowIndex, endRow = rowIndex;
        // 获取审批状态
        Object approveStatus = redisService.get(BaseConstant.SYS_DICT_KEY + "approve_status");
        Map<Short, String> approveStatusMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(approveStatus)) {
            List<JSONObject> approveStatusList = (List<JSONObject>) approveStatus;
            approveStatusMap = approveStatusList.stream().collect(Collectors.toMap(
                    k -> k.getShort("value"), k -> k.getString("label"),
                    (k1, k2) -> k1));
        }

        for (WorkflowApproveInfoDto.WorkflowExecuteStep workflowExecuteStep : executeList) {
            List<WorkflowApproveInfoDto.Approve> approveList = workflowExecuteStep.getApproveList();
            if (CollUtil.isEmpty(approveList) || workflowExecuteStep.getType().equals(1)) {
                continue;
            }

            if (workflowExecuteStep.getType().equals(7)) {
                writer.setCurrentRow(rowIndex);
                StringBuilder copyStep = new StringBuilder();
                String nameText =
                        approveList.stream().map(WorkflowApproveInfoDto.Approve::getName).collect(Collectors.joining(
                                "，"));
                copyStep.append("抄送").append("  ").append(nameText);

                writer.merge(rowIndex, rowIndex, 1, 4, copyStep.toString(), false);
                writer.setStyle(cellStyle, "B" + (rowIndex + 1));
                writer.writeCellValue("F" + (rowIndex + 1), "");
                writer.setStyle(cellStyle, "F" + (rowIndex + 1));
                writer.setRowHeight(rowIndex++, 25);

                endRow++;
                rowIndex++;
            } else {
                for (WorkflowApproveInfoDto.Approve approve : approveList) {
                    writer.setCurrentRow(rowIndex);
                    StringBuilder step = new StringBuilder();
                    Short status = (ObjectUtil.isNull(approve.getStatus()) || 0 == approve.getStatus()) ? 1 :
                            approve.getStatus();
                    String statusText = ObjectUtil.isEmpty(approve.getStatusText()) ?
                            approveStatusMap.getOrDefault(status, "") : approve.getStatusText();
                    String comment = ObjectUtil.isEmpty(approve.getComment()) ? "" : approve.getComment();

                    String approveDateFormat = "";
                    if (ObjectUtil.isNotNull(approve.getApproveDate())) {
                        LocalDateTime approveDate = approve.getApproveDate();
                        approveDateFormat = DateUtil.format(approveDate, DatePattern.NORM_DATETIME_PATTERN);
                    }

                    step.append(approve.getName()).append("  ").append(statusText);
                    if (StrUtil.isNotBlank(comment)) {
                        step.append(" ，").append(comment);
                    }

                    writer.merge(rowIndex, rowIndex, 1, 3, step.toString(), false);
                    writer.setStyle(cellStyle, "B" + (rowIndex + 1));
                    Cell cell = writer.getCell("E" + (rowIndex + 1), true);
                    if (StrUtil.isNotEmpty(approve.getSignatureLink())) {
                        try {
                            Drawing<?> drawing = writer.getSheet().createDrawingPatriarch();
                            ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, cell.getColumnIndex(), rowIndex, cell.getColumnIndex() + 1, rowIndex + 1);
                            byte[] bytes = HttpUtil.downloadBytes(approve.getSignatureLink());
                            int pictureIdx = writer.getWorkbook().addPicture(bytes, Workbook.PICTURE_TYPE_JPEG);
                            Picture picture = drawing.createPicture(anchor, pictureIdx);
                            picture.resize(1, 1);
                        } catch (Exception e) {
                            log.error("导出审批信息图片异常, {}", e.getMessage(), e);
                        }
                    }
                    writer.setStyle(cellStyle, "E" + (rowIndex + 1));
                    writer.writeCellValue("F" + (rowIndex + 1), approveDateFormat);
                    writer.setStyle(cellStyle, "F" + (rowIndex + 1));
                    writer.setRowHeight(rowIndex, StrUtil.isNotEmpty(approve.getSignatureLink()) ? 80 : 25);
                    endRow++;
                    rowIndex++;
                }
            }
        }
        if (endRow != startRow) {
            if ((endRow - startRow) == 1) {
                writer.writeCellValue("A" + endRow, "审批明细：");
            } else {
                writer.merge(startRow, endRow - 1, 0, 0, "审批明细：", false);
            }
        }
        writer.setStyle(cellStyle, "A" + startRow);
    }

    /**
     * 写入单据信息
     *
     * @param writer 导出文件对象
     * @param order  单据数据
     */
    private int buildOrderExcel(CellStyle headStyle,
                                CellStyle titleStyle,
                                CellStyle cellStyle,
                                ExcelWriter writer,
                                int rowIndex,
                                JSONObject order,
                                List<FormFieldCO> orderHeads,
                                ExportParams exportParams) {
        String title = OrderTypeNewEnum.getByType(exportParams.getOrderType()).getName();
        String orderNo = order.getString(ORDER_NO);
        Long createTime = order.getLong(CREATE_TIME);
        String createTimeFormat = DateUtil.format(new Date(createTime), DatePattern.NORM_DATETIME_PATTERN);
        String createByText = order.getString("createByText");

        writer.merge(rowIndex, rowIndex++, 0, 5, title, false);
        writer.setStyle(headStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);
        writer.merge(rowIndex, rowIndex++, 0, 5, "单据信息", false);
        writer.setStyle(titleStyle, "A" + rowIndex);
        writer.setRowHeight(rowIndex - 1, 25);

        writer.setCurrentRow(rowIndex);
        writer.writeCellValue("A" + (rowIndex + 1), "单据号：");
        writer.setStyle(cellStyle, "A" + (rowIndex + 1));
        writer.writeCellValue("B" + (rowIndex + 1), orderNo);
        writer.writeCellValue("C" + (rowIndex + 1), "创建时间：");
        writer.setStyle(cellStyle, "C" + (rowIndex + 1));
        writer.writeCellValue("D" + (rowIndex + 1), createTimeFormat);
        writer.writeCellValue("E" + (rowIndex + 1), "创建人：");
        writer.setStyle(cellStyle, "E" + (rowIndex + 1));
        writer.writeCellValue("F" + (rowIndex + 1), createByText);
        writer.setRowHeight(rowIndex, 25);
        rowIndex++;

        return writeDynamicField(cellStyle, writer, rowIndex, order, orderHeads);
    }

    private int writeDynamicField(CellStyle cellStyle,
                                  ExcelWriter writer,
                                  int rowIndex,
                                  JSONObject order,
                                  List<FormFieldCO> orderHeads) {
        int fieldColumns = 3;
        int fieldAmount = orderHeads.size();
        int fieldRows = (fieldAmount % fieldColumns > 0) ?
                (fieldAmount / fieldColumns) + 1 : (fieldAmount / 3);
        int fieldIndex = 0;
        for (int i = 0; i < fieldRows; i++) {
            writer.setCurrentRow(rowIndex);
            FormFieldCO first = orderHeads.get(fieldIndex++);
            writer.writeCellValue("A" + (rowIndex + 1), first.getFieldName() + "：");
            writer.setStyle(cellStyle, "A" + (rowIndex + 1));
            writer.writeCellValue("B" + (rowIndex + 1), analysisValue(first, order));
            if (fieldIndex < fieldAmount) {
                FormFieldCO second = orderHeads.get(fieldIndex++);
                writer.writeCellValue("C" + (rowIndex + 1), second.getFieldName() + "：");
                writer.setStyle(cellStyle, "C" + (rowIndex + 1));
                writer.writeCellValue("D" + (rowIndex + 1), analysisValue(second, order));
            } else {
                writer.writeCellValue("C" + (rowIndex + 1), "");
                writer.writeCellValue("D" + (rowIndex + 1), "");
            }
            if (fieldIndex < fieldAmount) {
                FormFieldCO third = orderHeads.get(fieldIndex++);
                writer.writeCellValue("E" + (rowIndex + 1), third.getFieldName() + "：");
                writer.setStyle(cellStyle, "E" + (rowIndex + 1));
                writer.writeCellValue("F" + (rowIndex + 1), analysisValue(third, order));
            } else {
                writer.writeCellValue("E" + (rowIndex + 1), "");
                writer.writeCellValue("F" + (rowIndex + 1), "");
            }
            writer.setRowHeight(rowIndex, 25);
            rowIndex++;
        }
        return rowIndex;
    }

    private String analysisValue(FormFieldCO field, JSONObject order) {
        JSONObject props = field.getFieldProps();
        String value = "";
        if (FormFieldCO.FILES.equals(field.getFieldType())) {
            JSONArray jsonArray = order.getJSONArray(field.getFieldCode());
            if (CollUtil.isNotEmpty(jsonArray)) {
                List<CommonFileDto> orderFile = jsonArray.toJavaList(CommonFileDto.class);
                List<String> data = Lists.newArrayList();
                orderFile.forEach(fileDto -> {
                    data.add(fileDto.getUrl());
                });
                value = StrUtil.join("\n", data);
            }
        } else if (FormFieldCO.IMAGES.equals(field.getFieldType())) {
            JSONArray jsonArray = order.getJSONArray(field.getFieldCode());
            if (CollUtil.isNotEmpty(jsonArray)) {
                List<String> data = jsonArray.toJavaList(String.class);
                value = StrUtil.join("\n", data);
            }
        } else if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(field.getFieldType())) {
            JSONArray jsonArray = order.getJSONArray(field.getFieldCode());
            if (CollUtil.isNotEmpty(jsonArray)) {
                List<String> data = jsonArray.toJavaList(String.class);
                value = StrUtil.join("、", data);
            }
        } else if (FormFieldCO.DATETIME.equals(field.getFieldType())) {
            String fmt = props.getString("dateFormatType");
            if (StrUtil.isBlank(fmt)) {
                fmt = "yyyy-MM-dd";
            }
            Long date = order.getLong(field.getFieldCode());
            if (ObjectUtil.isNotNull(date)) {
                LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                value = dateTime.format(DateTimeFormatter.ofPattern(fmt));
            }
        } else {
            if (field.hasTranslation()) {
                value = order.getString(field.getTranslationCode());
            } else {
                value = order.getString(field.getFieldCode());
            }
        }
        return value;
    }

    private String getDestPath(ExportParams exportParams) {
        String name;
        if (exportParams.getExportDataList().size() > 1) {
            name = exportParams.getTaskName() + ".zip";
        } else {
            String tempName = OrderTypeNewEnum.getByType(exportParams.getOrderType()).getName();
            AbsExportData absExportData = exportParams.getExportDataList().get(0);
            name = tempName + "-" + ((OrderCardExportData) absExportData).getOrderData().getString("orderNo") + "(" +
                    DateUtil.format(DateUtil.date(), DatePattern.PURE_TIME_PATTERN) + ")" + ".xlsx";
        }

        return exportParams.getCompanyId() + "/" + exportParams.getOrderTypeCode() + "_order/" + DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN) + "/" + name;
    }

    /**
     * 获取表格名称
     */
    private String getExcelName(Integer orderType, JSONObject order) {
        String tempName = OrderTypeNewEnum.getByType(orderType).getName();
        String orderNo = order.getString("orderNo");
        return tempName + "-" + orderNo + ".xlsx";
    }

    private String getOrderDetailsDestPath(ExportParams exportParams) {
        String name = getOrderDetailsExcelName(exportParams.getOrderType());
        return exportParams.getCompanyId() + "/" + exportParams.getOrderTypeCode() + "_purchase_details/" + DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN) + "/" + name;
    }

    /**
     * 获取表格名称
     */
    private String getOrderDetailsExcelName(Integer orderType) {
        String templateStr = "%s导出";
        String tempName = String.format(templateStr, OrderTypeNewEnum.getByType(orderType).getName());
        return tempName + "(" +
                DateUtil.format(DateUtil.date(), "MMddHHmmss") + ")" + ".xlsx";
    }

    /**
     * 获取临时存放路径
     */
    private File getTempPath(ExportParams exportParams) {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()),
                "excelTemp/purchase",
                exportParams.getCompanyId().toString(),
                exportParams.getOrderType().toString(),
                currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    private File getOrderDetailsTempPath(ExportParams exportParams) {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()),
                "excelTemp/purchase_details",
                exportParams.getCompanyId().toString(),
                exportParams.getOrderType().toString(),
                currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    /**
     * 获取任务名称
     *
     * @param orderType 单据类型
     */
    private String getName(Integer orderType, String exportType) {
        // 获取流水号
        int count = importTaskFeignClient.count(DictConstant.TASK_TYPE_EXPORT, orderType);
        String serialNo = StrUtil.padPre(String.valueOf(++count), 5, "0");
        String templateStr;
        if ("card".equals(exportType)) {
            templateStr = "%s卡片导出（%s-%s）";
        } else {
            templateStr = "%s导出（%s-%s）";
        }
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return String.format(templateStr, OrderTypeNewEnum.getByType(orderType).getName(), currentTime, serialNo);
    }

    /**
     * 补上审批流数据
     *
     * @param orderList 单据数据
     */
    private List<AsOrderDetailDto> getWorkFlowData(List<? extends AbstractIdOrderDto> orderList) {
        List<AsOrderDetailDto> orderDetailDtoList = Lists.newArrayList();
        for (AbstractIdOrderDto dto : orderList) {
            JSONObject orderJson = asOrderUtil.toJSONObject(dto);
            if (dto.getApproveStatus() == 0) {
                AsOrderDetailDto asOrderDetailDto = new AsOrderDetailDto()
                        .setOrder(orderJson)
                        .setApproveInfo(new WorkflowApproveInfoDto());
                orderDetailDtoList.add(asOrderDetailDto);
                continue;
            }

            WorkflowApproveInfoDto approveInfoDto;
            // 单据类型
            Short orderType = Convert.toShort(dto.getOrderType());
            // 单据id
            Long id = dto.getId();
            if (DictConstant.WAIT_APPROVE.equals(dto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo(orderType, id);
                if (approveInfoDto == null || approveInfoDto.getExecuteList().isEmpty()) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo(orderType, id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo(orderType, id);
                if (ObjectUtil.isNotNull(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(dto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
            AsOrderDetailDto asOrderDetailDto = new AsOrderDetailDto()
                    .setOrder(orderJson)
                    .setApproveInfo(approveInfoDto);
            orderDetailDtoList.add(asOrderDetailDto);
        }

        if (CollUtil.isEmpty(orderDetailDtoList)) {
            throw new BusinessException(MeansResultCode.ORDER_DATA_NOT_EXIST);
        }

        return orderDetailDtoList;
    }
}
