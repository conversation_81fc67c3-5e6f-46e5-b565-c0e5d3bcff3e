package com.niimbot.asset.controller.pc.inventory;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryManualDto;
import com.niimbot.inventory.InventoryQueryDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventoryTaskAddUserDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.inventory.InventoryTaskInfoDto;
import com.niimbot.inventory.InventoryTaskListDto;
import com.niimbot.inventory.InventoryTaskUpdateUserDto;
import com.niimbot.inventory.InventoryTaskUpdateUserInfoDto;
import com.niimbot.inventory.InventoryTaskView;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * 盘点单后台api
 *
 * <AUTHOR>
 * @date 2021/4/12 11:50
 */
@Api(tags = "盘点任务")
@ResultController
@RequestMapping("api/pc/inventory/task")
@RequiredArgsConstructor
public class InventoryTaskController {

    private final InventoryFeignClient inventoryFeignClient;
    private final AsAssetUtil assetUtil;
    private final FormFeignClient formFeignClient;

    @ApiOperation(value = "PC盘点任务分页列表")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<InventoryTaskListDto> page(InventoryQueryDto dto) {
        return inventoryFeignClient.taskPage(dto);
    }

    @ApiOperation(value = "提交审核")
    @PutMapping("/submit")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean submit(@RequestBody @Validated InventoryTaskApproveDto approveDto) {
        return inventoryFeignClient.submit(approveDto);
    }

    @ApiOperation(value = "手动盘点")
    @PutMapping("/manualInventory")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean manualInventory(@RequestBody @Validated InventoryManualDto dto) {
        return inventoryFeignClient.manualInventory(dto);
    }

    @ApiOperation(value = "资产上报")
    @PostMapping("/reportAsset")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean reportAsset(@RequestBody @Validated({Insert.class}) InventorySurplusDto dto) {
        // 盘盈数据
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject assetData = dto.getAssetData();
        assetUtil.translateAssetJson(assetData, formVO.getFormFields());
        /* 翻译数据 */
        return inventoryFeignClient.reportAsset(dto);
    }

    @ApiOperation(value = "编辑上报资产")
    @PutMapping("/reportAsset")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean editReportAsset(@RequestBody @Validated({Update.class}) InventorySurplusDto dto) {
        // 盘盈数据
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject assetData = dto.getAssetData();
        assetUtil.translateAssetJson(assetData, formVO.getFormFields());
        /* 翻译数据 */
        return inventoryFeignClient.editReportAsset(dto);
    }

    @ApiOperation(value = "删除上报资产")
    @DeleteMapping("/reportAsset/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @RepeatSubmit
    public Boolean removeReportAsset(@PathVariable("id") Long id) {
        return inventoryFeignClient.removeReportAsset(id);
    }

    @ApiOperation(value = "批量删除上报资产")
    @DeleteMapping("/reportAsset")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @RepeatSubmit
    public Boolean removeReportAssets(@RequestBody List<Long> ids) {
        return inventoryFeignClient.removeReportAssets(ids);
    }

    @ApiOperation(value = "查询盘点任务盘点人列表")
    @GetMapping("/inventoryUsers/{taskId}")
    public InventoryTaskUpdateUserInfoDto getInventoryUsers(@PathVariable Long taskId) {
        return inventoryFeignClient.getInventoryUsers(taskId);
    }

    @ApiOperation(value = "重新分配盘点人")
    @PutMapping("/updateInventoryUsers")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean updateInventoryUsers(@RequestBody @Validated InventoryTaskUpdateUserDto updateUserDto) {
        if (InventoryTaskUpdateUserDto.TYPE_RETAIN == updateUserDto.getType()) {
            List<InventoryTaskUpdateUserDto.UpdateUser> updateUsers = updateUserDto.getUpdateUsers();
            updateUsers.forEach(u -> {
                if (u.getOldUserId() == null) {
                    BusinessExceptionUtil.throwException("原始盘点人不能为空");
                }
            });
        }
        return inventoryFeignClient.updateInventoryUsers(updateUserDto);
    }

    @ApiOperation(value = "新增盘点人")
    @PutMapping("/addInventoryUsers")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean addInventoryUsers(@RequestBody @Validated InventoryTaskAddUserDto addUserDto) {
        return inventoryFeignClient.addInventoryUsers(addUserDto);
    }

    @ApiOperation(value = "盘点子任务详情")
    @GetMapping("/info/{id}")
    @AutoConvert
    public InventoryTaskInfoDto getInfo(@PathVariable Long id) {
        return inventoryFeignClient.getInfo(id);
    }


    @ApiOperation(value = "盘点任务概览")
    @GetMapping("/view/{id}")
    @AutoConvert
    public InventoryTaskView getInventoryTaskView(@PathVariable Long id) {
        return inventoryFeignClient.getInventoryTaskView(id);
    }
}
