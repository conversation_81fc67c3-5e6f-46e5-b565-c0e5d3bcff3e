package com.niimbot.asset.controller.h5;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.CategoryFeignClient;
import com.niimbot.asset.service.feign.OnlineInventoryFeignClient;
import com.niimbot.inventory.InventoryAssetAppQueryDto;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryQueryDto;
import com.niimbot.system.Auditable;
import com.niimbot.system.QueryConditionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "资产分类管理")
@ResultController
@RequestMapping("api/common/category")
@RequiredArgsConstructor
public class H5CategoryController {

    private final CategoryFeignClient categoryFeignClient;
    private final OnlineInventoryFeignClient onlineInventoryFeignClient;

    @ApiOperation(value = "删除资产分类数据")
    @PostMapping("/delete")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @AuditLog(Auditable.Action.DEL_MEANS_CATE)
    public List<CategoryDto> remove(@RequestBody List<Long> categoryIds) {
        return categoryFeignClient.delete(categoryIds);
    }

    @ApiOperation(value = "查询资产分类下拉树-盘点专用-新")
    @GetMapping("/query/list")
    public List<CategoryDto> queryList(@Validated InventoryAssetAppQueryDto queryDto) {
        // 查询全部数据
        List<CategoryDto> result = categoryFeignClient.list(new CategoryQueryDto().setFilterPerm(true));
        List<String> categoryIdList = result.stream().map(item -> item.getId().toString()).collect(Collectors.toList());
        InventorySurplusQueryDto queryParam = new InventorySurplusQueryDto();
        BeanUtils.copyProperties(queryDto, queryParam);
        queryParam.setGroupByColumn("asset_category");
        QueryConditionDto queryConditionDto = new QueryConditionDto();
        queryConditionDto.setCode("assetCategory");
        queryConditionDto.setQuery("in");
        queryConditionDto.setQueryData(categoryIdList);
        queryParam.setConditions(Lists.newArrayList(queryConditionDto));
        List<InventoryAssetDataCountDto> assetDataCountDtoList = onlineInventoryFeignClient.appAssetCode(queryParam);
        log.info("categoryController queryList success! param=[{}] result=[{}]", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(assetDataCountDtoList));
        Map<String, Integer> dataCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(assetDataCountDtoList)) {
            dataCountMap = assetDataCountDtoList.stream().collect(Collectors.toMap(InventoryAssetDataCountDto::getConditionCode, InventoryAssetDataCountDto::getDataCount, (v1, v2) -> v2));
        }
        for (CategoryDto item : result) {
            item.setDataCount(dataCountMap.get(item.getId().toString()));
        }
        // 统计数量为0的分组为其他
        Integer other = assetDataCountDtoList.stream().filter(v -> StrUtil.isBlank(v.getConditionCode())).map(InventoryAssetDataCountDto::getDataCount).reduce(0, Integer::sum);
        result.add(new CategoryDto().setCategoryName("其他").setId(-1L).setDataCount(other));
        return result;
    }
}
