package com.niimbot.asset.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.ReImportDto;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/31 17:47
 */
public interface ProductExcelService {

    ExcelWriter buildExcelWriter(Long standardId);

    void parseExcelStream(InputStream stream, String fileName, Long fileSize,
                          Long companyId, Long standardId);

    List<List<LuckySheetModel>> importError(Long taskId);

    ReImportDto reImport(Long taskId);

    void importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);
}
