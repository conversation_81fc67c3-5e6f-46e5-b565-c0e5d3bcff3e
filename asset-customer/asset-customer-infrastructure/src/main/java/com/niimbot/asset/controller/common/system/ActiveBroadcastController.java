package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.service.feign.ActiveBroadcastFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.ActiveBroadcastDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11 下午3:36
 */
@Api(tags = "活动广播")
@ResultController
@RequestMapping("api/common/broadcast/")
public class ActiveBroadcastController {

    @Autowired
    private ActiveBroadcastFeignClient broadcastFeignClient;

    @ApiOperation(value = "广播列表")
    @GetMapping("list")
    public List<ActiveBroadcastDto> list() {
        return broadcastFeignClient.list();
    }

    @ApiOperation(value = "活动广播统计")
    @PostMapping("broadcastAction")
    public Boolean broadcastAction(@RequestBody Long broadcastId) {
        return broadcastFeignClient.broadcastAction(broadcastId);
    }
}
