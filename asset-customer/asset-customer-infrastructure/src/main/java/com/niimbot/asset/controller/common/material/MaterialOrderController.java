package com.niimbot.asset.controller.common.material;

import com.niimbot.asset.service.feign.MaterialOrderFeignClient;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2022/10/17 16:05
 */
@Api(tags = "【耗材】单据管理")
@ResultController
@RequestMapping("api/common/material/order")
@RequiredArgsConstructor
@Validated
public class MaterialOrderController {

    private final MaterialOrderFeignClient orderFeignClient;

    @ApiOperation(value = "查询耗材单据表单")
    @GetMapping(value = "/form/{orderType}")
    public FormVO getForm(@PathVariable("orderType") Integer orderType) {
        // 查询设置显示的数据
        return orderFeignClient.getForm(orderType);
    }


}
