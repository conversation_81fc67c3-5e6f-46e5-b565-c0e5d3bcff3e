package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.MaterialOrderStockDto;
import com.niimbot.material.MaterialOrderStockQueryDto;
import com.niimbot.material.MaterialStockDetailDto;
import com.niimbot.material.MaterialStockDetailQueryDto;
import com.niimbot.material.MaterialStockDto;
import com.niimbot.material.MaterialStockInfoDto;
import com.niimbot.material.MaterialStockLogDto;
import com.niimbot.material.MaterialStockLogQueryDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.material.MaterialTotalStockDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2021/7/9 16:49
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialStockFeignClient {

    /**
     * 实时库存分页列表
     *
     * @param queryDto 查询参数
     * @return 实时库存列表
     */
    @PostMapping(value = "server/material/stock/page")
    PageUtils<MaterialStockDto> page(MaterialStockQueryDto queryDto);

    /**
     * 实时库存总量
     *
     * @param queryDto 查询参数
     * @return 实时库存总量
     */
    @PostMapping(value = "server/material/stock/total")
    MaterialTotalStockDto total(MaterialStockQueryDto queryDto);

    /**
     * 领用单实时库存分页列表
     *
     * @param queryDto 查询参数
     * @return 实时库存列表
     */
    @PostMapping(value = "server/material/stock/order/page")
    PageUtils<MaterialOrderStockDto> orderStockPage(MaterialOrderStockQueryDto queryDto);

    /**
     * 实时库存出入库记录
     *
     * @param queryDto 查询参数
     * @return 出入库记录
     */
    @GetMapping(value = "server/material/stock/log")
    PageUtils<MaterialStockLogDto> stockLog(@SpringQueryMap MaterialStockLogQueryDto queryDto);

    /**
     * 实时库存出入库记录
     *
     * @param queryDto 查询参数
     * @return 出入库记录
     */
    @GetMapping(value = "server/material/stock/log/detail")
    PageUtils<MaterialStockInfoDto> stockLogDetail(@SpringQueryMap MaterialStockLogQueryDto queryDto);

    /**
     * 单个耗材的库存分布情况
     *
     * @param materialId 耗材ID
     * @return
     */
    @GetMapping(value = "server/material/stock/detail")
    PageUtils<MaterialStockDetailDto> stockDetail(@SpringQueryMap MaterialStockDetailQueryDto queryDto);
}
