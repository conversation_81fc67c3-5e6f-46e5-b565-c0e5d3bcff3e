package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.purchase.PurchaseDetailDataConvertDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkApplyAmountDto;
import com.niimbot.purchase.PurchaseLinkStoreAmountDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;
import com.niimbot.purchase.PurchaseOrderDetailExtInfoQueryDto;
import com.niimbot.purchase.PurchaseOrderDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;
import com.niimbot.purchase.PurchaseOrderSubmitDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 采购单feign客户端
 *
 * <AUTHOR>
 * @date 2021/5/24 16:02
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PurchaseOrderFeignClient {
    /**
     * 创建采购单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/purchase/order")
    Boolean create(@RequestBody PurchaseOrderSubmitDto dto);

    /**
     * 采购单详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/purchase/order/{id}")
    PurchaseOrderDto getById(@PathVariable("id") Long id);

    /**
     * 采购单详情
     *
     * @param orderNo
     * @return
     */
    @GetMapping(value = "server/purchase/order/getByNo/{orderNo}")
    PurchaseOrderDto getByOrderNo(@PathVariable("orderNo") String orderNo);

    /**
     * 采购单分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/purchase/order/page")
    PageUtils<PurchaseOrderDto> page(@RequestBody PurchaseOrderQueryDto query);

    /**
     * 采购单列表查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/purchase/order/list")
    List<PurchaseOrderDto> list(@RequestBody PurchaseOrderQueryDto query);

    /**
     * 采购单明细查询
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "server/purchase/order/listDetails")
    List<PurchaseOrderDetailDto> listDetailsByOrderId(@RequestBody Collection<Long> ids);

    /**
     * 采购单明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/purchase/order/pageDetail")
    PageUtils<PurchaseOrderDetailDto> pageDetail(@RequestBody PurchaseDetailPageQueryDto dto);

    /**
     * 采购单明细数据转换
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/purchase/order/convert")
    List<JSONObject> dataConvert(@RequestBody PurchaseDetailDataConvertDto dto);

    /**
     * 采购数量明细查询
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/purchase/order/link/apply")
    List<PurchaseLinkApplyAmountDto> listApplyAmount(@RequestBody PurchaseLinkAmountQueryDto dto);

    /**
     * 入库中数量明细查询
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/purchase/order/link/store")
    List<PurchaseLinkStoreAmountDto> listStoreAmount(@RequestBody PurchaseLinkAmountQueryDto dto);

    @PostMapping(value = "server/purchase/order/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(PurchaseOrderDto dto);

    @PostMapping(value = "server/purchase/order/listExtInfo")
    List<PurchaseOrderDetailDto> listExtInfo(PurchaseOrderDetailExtInfoQueryDto queryDto);
}
