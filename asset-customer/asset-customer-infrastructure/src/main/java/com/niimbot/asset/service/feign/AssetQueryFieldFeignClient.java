package com.niimbot.asset.service.feign;

import com.niimbot.system.QueryTypeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.Map;

/**
 * 资产查询字段
 *
 * <AUTHOR>
 * @date 2021/12/6 11:48
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetQueryFieldFeignClient {

    /**
     * 字段可选条件查询
     *
     * @return
     */
    @GetMapping("server/means/queryField/getOperators/{type}")
    List<QueryTypeDto> getOperators(@PathVariable("type") String type);

    /**
     * 字段可选条件查询
     *
     * @return
     */
    @GetMapping("server/means/queryField/getOperatorMap")
    Map<String, List<QueryTypeDto>> getOperatorMap();
}
