package com.niimbot.asset.service.impl;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.model.LoginUser;
import com.niimbot.asset.security.service.SysPermissionService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.exception.category.FeignClientException;
import com.niimbot.jf.core.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/1/12 10:52
 */
@Slf4j
@Service
public class SmsUserDetailsServiceImpl {

    private final SysPermissionService sysPermissionService;

    private final AccountCenterFeignClient accountCenterFeignClient;

    @Autowired
    public SmsUserDetailsServiceImpl(SysPermissionService sysPermissionService, AccountCenterFeignClient accountCenterFeignClient) {
        this.sysPermissionService = sysPermissionService;
        this.accountCenterFeignClient = accountCenterFeignClient;
    }

    public UserDetails loadUserByUsername(String account, String smsCode) {
        CusUserDto cusUser = checkMobile(account, smsCode);
        return createLoginUser(cusUser);
    }

    public CusUserDto checkMobile(String mobile, String smsCode) {
        // 获取账户信息
        CusUserDto cusUser;
        try {
            log.info("验证码登录发送请求 : {}", mobile);
            cusUser = accountCenterFeignClient.getLoginInfoByMobile(mobile, smsCode);
        } catch (FeignClientException e) {
            // 处理特殊异常
            Result failureResult = e.getFailureResult();
            if (Objects.nonNull(failureResult) && Objects.nonNull(failureResult.getCode()) && failureResult.getCode().equals(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP.getCode())) {
                throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
            }
            if (Objects.nonNull(failureResult) && Objects.nonNull(failureResult.getCode()) && failureResult.getCode().equals(SystemResultCode.COMPANY_FORBIDDEN.getCode())) {
                throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN);
            }
            if (Objects.nonNull(failureResult) && Objects.nonNull(failureResult.getCode()) && failureResult.getCode().equals(SystemResultCode.PARAM_REPEAT_SUBMIT.getCode())) {
                throw new BusinessException(SystemResultCode.PARAM_REPEAT_SUBMIT);
            }
            throw e;
        }
        return cusUser;
    }

    public UserDetails createLoginUser(CusUserDto cusUser) {
        return new LoginUser(cusUser, sysPermissionService.getMenuPermission(cusUser));
    }
}
