package com.niimbot.asset.service.excel;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.ExportService;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.system.ImportTaskDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2023/6/14 14:36
 */
@Slf4j
public abstract class AbstractExcelExportService {

    @Autowired
    private ExportService exportService;
    private final ImportTaskFeignClient importTaskFeignClient;

    protected AbstractExcelExportService(ImportTaskFeignClient importTaskFeignClient) {
        this.importTaskFeignClient = importTaskFeignClient;
    }

    public String async(Supplier<String> supplier) {
        return supplier.get();
    }

    public void sync(ExportParams exportParams, BiConsumer<Long, ExportParams> biConsumer) {
        new Thread(() -> {
            // 导出任务是否成功
            Long taskId = null;
            // 导出是否报错
            boolean error = false;
            int orderType = exportParams.getOrderType();
            try {
                // 创建导出任务
                ImportTaskDto importTaskDto = new ImportTaskDto()
                        .setName(exportParams.getTaskName())
                        .setType(DictConstant.TASK_TYPE_EXPORT)
                        .setImportType(orderType)
                        .setTotal(exportParams.getCount())
                        .setExportUrl(exportParams.getExportUrl())
                        .setQueryData(exportParams.getQueryCondition());
                taskId = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(taskId);
                biConsumer.accept(taskId, exportParams);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if (ObjectUtil.isNotNull(taskId)) {
                    ImportTaskDto importTaskUpdate = new ImportTaskDto();
                    importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_FAIL);
                    importTaskFeignClient.update(importTaskUpdate);
                    exportService.sendAssetOrderMsg(exportParams.getCompanyId(), taskId, DictConstant.IMPORT_STATUS_FAIL);
                    error = true;
                }
            } finally {
                // 更新任务状态
                if (ObjectUtil.isNotNull(taskId) && !error) {
                    ImportTaskDto importTaskUpdate = new ImportTaskDto();
                    importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_SUCC)
                            .setFinishTime(LocalDateTime.now());
                    importTaskFeignClient.update(importTaskUpdate);
                    exportService.sendAssetOrderMsg(exportParams.getCompanyId(), taskId, DictConstant.IMPORT_STATUS_SUCC);
                }
            }
        }).start();
    }

    @Data
    @NoArgsConstructor
    protected static class ExportParams {
        private Long companyId;
        private String taskName;
        private JSONObject queryCondition;
        private String downUrl;
        private Integer orderType;
        private String exportUrl;
        private Integer count;

        public Integer getCount() {
            if (this.count != null) {
                return this.count;
            } else {
                return exportDataList.size();
            }
        }

        public ExportParams(Long companyId, String taskName, Integer orderType) {
            this.companyId = companyId;
            this.taskName = taskName;
            this.orderType = orderType;
        }

        private List<AbsExportData> exportDataList = new ArrayList<>();

        public String getOrderTypeCode() {
            return OrderTypeNewEnum.ORDER_KEY_CODE.get(getOrderType());
        }
    }

    protected static abstract class AbsExportData {

    }

}
