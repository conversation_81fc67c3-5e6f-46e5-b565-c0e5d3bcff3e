package com.niimbot.asset.controller.app.equipment;

import com.niimbot.jf.core.component.annotation.ResultController;

import org.springframework.web.bind.annotation.RequestMapping;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/17 17:19
 */
@Api(tags = "【设备管理】单据字段管理")
@ResultController
@RequestMapping("api/app/queryField/equipmentOrder")
@RequiredArgsConstructor
public class EquipmentOrderQueryFieldAppController {


}
