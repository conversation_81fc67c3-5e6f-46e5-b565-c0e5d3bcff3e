package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.MaterialConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.DateUtils;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.ExportService;
import com.niimbot.asset.service.MaterialInventoryExcelService;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.MaterialInventoryFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.inventory.InventoryDetailQueryDto;
import com.niimbot.material.inventory.MaterialInventoryDetailPageDto;
import com.niimbot.material.inventory.MaterialInventoryResultReportDto;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.ImportTaskDto;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/18 16:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialInventoryExcelServiceImpl implements MaterialInventoryExcelService {

    private final MaterialInventoryFeignClient materialInventoryFeignClient;
    private final FormFeignClient formFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final ImportTaskFeignClient importTaskFeignClient;
    private final ExportService exportService;
    private final RedisService redisService;
    private final FileUploadService fileUploadService;
    private final MaterialQueryFieldService queryFieldService;
    @Autowired
    private FileUploadConfig fileUploadConfig;
    private static ThreadLocal<GlobalCache> companyCache = new TransmittableThreadLocal<>();

    @Data
    @Accessors(chain = true)
    private static class GlobalCache {
        private Long company;
        private FormVO formVO;
        private String module = "material_inventory";
    }

    @Override
    public void resultReportExport(Long inventoryId) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        GlobalCache globalCache = new GlobalCache();
        globalCache.setCompany(companyId)
                .setFormVO(formVO);
        companyCache.set(globalCache);
        // 异步启动
        new Thread(() -> {
            startResultReportExport(inventoryId);
        }).start();
    }

    private void startResultReportExport(Long inventoryId) {
        // 导出任务是否成功
        Long taskId = null;
        // 导出是否报错
        boolean error = false;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", inventoryId);
            // 创建导出任务
            MaterialInventoryResultReportDto resultReportDto = materialInventoryFeignClient.resultReport(inventoryId);
            String fileName = StrUtil.format("{}-耗材盘点报告（{}）", resultReportDto.getName(), LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyyMMddhhmmss")));
            ImportTaskDto importTaskDto = new ImportTaskDto()
                    .setName(fileName)
                    .setType(DictConstant.TASK_TYPE_EXPORT)
                    .setQueryData(jsonObject)
                    .setExportUrl(OrderTypeNewEnum.EXPORT_TYPE_MATERIAL_INVENTORY_REPORT.getExportUrl())
                    .setImportType(MaterialConstant.ORDER_TYPE_MATERIAL_INVENTORY);
            taskId = importTaskFeignClient.save(importTaskDto);
            importTaskDto.setId(taskId);
            executeInventoryReportExport(importTaskDto, resultReportDto);
            importTaskFeignClient.update(importTaskDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (ObjectUtil.isNotNull(taskId)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_FAIL);
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_FAIL);
                error = true;
            }
        } finally {
            // 更新任务状态
            if (ObjectUtil.isNotNull(taskId) && !error) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_SUCC)
                        .setFinishTime(LocalDateTime.now());
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_SUCC);
                FileUtil.del(FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", companyCache.get().getModule()));
            }
            this.clearThreadLocal();
        }
    }

    private void executeInventoryReportExport(ImportTaskDto importTaskDto,
                                              MaterialInventoryResultReportDto resultReportDto) {
        // 转义
        dictConvertUtil.convertToDictionary(resultReportDto);

        // 查询盘点详情
        InventoryDetailQueryDto query = new InventoryDetailQueryDto();
        query.setInventoryId(resultReportDto.getId());
        query.setPageSize(Integer.MAX_VALUE);
        PageUtils<MaterialInventoryDetailPageDto> detailPage = materialInventoryFeignClient.detailPage(query);
        List<MaterialInventoryDetailPageDto> inventoryDetailList = detailPage.getList();
        dictConvertUtil.convertToDictionary(inventoryDetailList);
        // 动态表头
        List<AssetHeadDto> headDtos = queryFieldService.materialHeadView();
        headDtos = headDtos.stream()
                .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                .collect(Collectors.toList());
        // 查询head
        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
        headerData.put("repositoryIdText", "仓库");
        for (AssetHeadDto head : headDtos) {
            if (StrUtil.isNotEmpty(head.getTranslationCode())) {
                headerData.put(head.getTranslationCode(), head.getName());
            } else {
                headerData.put(head.getCode(), head.getName());
            }
        }
        File tempPath = getTempPath(companyCache.get().getModule());
        // 写入
        try {
            String fileName = importTaskDto.getName() + ".xlsx";
            File outputFile = new File(tempPath.getPath() + "/" + fileName);
            String localPath = outputFile.getPath();

            // 写入盘点概述
            ExcelWriter writer = buildInventoryExcel(resultReportDto);
            writer.setSheet(0);
            writer.renameSheet(0, "盘点概述");

            // 写入正常数据
            List<ExcelExportDto> inventoryDetailExcel = loadInventoryDetailExcel(inventoryDetailList, headerData, headDtos);
            importTaskDto.setTotal(inventoryDetailExcel.stream().map(ExcelExportDto::getRows).mapToInt(List::size).sum());

            for (int i = 0; i < inventoryDetailExcel.size(); i++) {
                ExcelExportDto excelExportDto = inventoryDetailExcel.get(i);
                writer.setSheet(i + 1);
                writer.renameSheet(i + 1, excelExportDto.getSheetName());
                writer.writeHeadRow(excelExportDto.getHeaderData().values());
                List<String> codes = new ArrayList<>(excelExportDto.getHeaderData().keySet());
                List<?> rows = excelExportDto.getRows();
                for (Object row : rows) {
                    JSONObject json = (JSONObject) JSONObject.toJSON(row);
                    List<Object> rowData = new ArrayList<>();
                    for (String code : codes) {
                        rowData.add(json.get(code));
                    }
                    writer.writeRow(rowData);
                }
            }

            writer.setDestFile(outputFile);
            writer.flush();
            writer.close();

            // 上传文件到oss 单个文件或者zip包
            String path = fileUploadService.putFile(getDestPath(companyCache.get().getModule(), fileName), localPath);
            // 更新任务url
            if (StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(importTaskDto.getId()).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("耗材盘点报告导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    private ExcelWriter buildInventoryExcel(MaterialInventoryResultReportDto resultReportDto) {
        // 获取 writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        Sheet sheet = writer.getSheet();
        sheet.setDefaultColumnWidth(20 * 256);
        StyleSet styleSet = writer.getStyleSet();
        styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);

        // 获取基本信息和盘点范围的cellStyle
        CellStyle baseStyle = writer.createCellStyle();
        baseStyle.setAlignment(HorizontalAlignment.LEFT);
        baseStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        Font grey = writer.createFont();
        grey.setFontHeightInPoints((short) 12);
        baseStyle.setFont(grey);

        // 获取大标题的cellStyle
        CellStyle titleStyle = writer.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.LEFT);
        titleStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        Font blue = writer.createFont();
        blue.setFontHeightInPoints((short) 14);
        blue.setColor(IndexedColors.BLUE.getIndex());
        blue.setBold(true);
        titleStyle.setFont(blue);

        // 获取加粗的cellStyle
        CellStyle blodStyle = writer.createCellStyle();
        blodStyle.setAlignment(HorizontalAlignment.LEFT);
        blodStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        Font blod = writer.createFont();
        blod.setFontHeightInPoints((short) 12);
        blod.setBold(true);
        blodStyle.setFont(blod);

        // 写入基本信息
        writer.merge(0, 0, 0, 26, "盘点单名称：" + resultReportDto.getName(), false)
                .merge(1, 1, 0, 26, "一、盘点单基本信息", false)
                .writeCellValue(0, 2, "盘点单名称：" + resultReportDto.getName())
                .writeCellValue(1, 2, "盘点单状态：" + resultReportDto.getStatusText())
                .writeCellValue(2, 2, "盘点人：" + (CollUtil.isNotEmpty(resultReportDto.getInventoryUsersText()) ? String.join("，", resultReportDto.getInventoryUsersText()) : ""))
                .writeCellValue(0, 3, "创建人：" + resultReportDto.getCreateByText())
                .writeCellValue(1, 3, "创建时间：" + (resultReportDto.getCreateTime() != null ? DateUtils.format(resultReportDto.getCreateTime(), DatePattern.NORM_DATE_PATTERN) : ""))
                .writeCellValue(2, 3, "完成时间：" + (resultReportDto.getCompleteTime() != null ? DateUtils.format(resultReportDto.getCompleteTime(), DatePattern.NORM_DATE_PATTERN) : ""))
                .merge(4, 4, 0, 26, "二、盘点范围", false)
                .writeCellValue(0, 5, "耗材仓库：" + (CollUtil.isNotEmpty(resultReportDto.getInventoryRepoText()) ? String.join("，", resultReportDto.getInventoryRepoText()) : ""))
                .writeCellValue(0, 6, "盘点方式：" + resultReportDto.getInventoryTypeText());
        // 抽盘
        if (resultReportDto.getInventoryType().equals(2)) {
            writer.writeCellValue(1, 6, "抽盘比例：" + resultReportDto.getSpotCheckRatio() + "%");
        }
        writer.merge(7, 7, 0, 26, "三、盘点情况概述", false);
        writer.setStyle(titleStyle, "A1");
        writer.setStyle(titleStyle, "A2");
        writer.setStyle(titleStyle, "A5");
        writer.setStyle(titleStyle, "A8");

        int resRowNum = 8;
        // 盘点结果
        writer.writeCellValue(0, resRowNum, "1、盘点总结果");
        writer.setStyle(blodStyle, "A" + ++resRowNum);
        writer.merge(resRowNum, resRowNum + 1, 0, 0, "应盘数量", false)
                .merge(resRowNum, resRowNum, 1, 4, "实盘数量", false)
                .writeCellValue(1, resRowNum + 1, "正常")
                .writeCellValue(2, resRowNum + 1, "盘亏")
                .writeCellValue(3, resRowNum + 1, "盘盈-在册")
                .writeCellValue(4, resRowNum + 1, "上报盘盈")
                .merge(resRowNum, resRowNum + 1, 5, 5, "未盘数量", false);
        resRowNum += 2;
        List<MaterialInventoryResultReportDto.ResReport> inventoryRes = resultReportDto.getInventoryRes();
        for (MaterialInventoryResultReportDto.ResReport resReport : inventoryRes) {
            writer.writeCellValue(0, resRowNum, resReport.getShallNum());
            writer.writeCellValue(1, resRowNum, resReport.getCheckedNum());
            writer.writeCellValue(2, resRowNum, resReport.getLossNum());
            writer.writeCellValue(3, resRowNum, resReport.getSurplusNum());
            writer.writeCellValue(4, resRowNum, resReport.getReportNum());
            writer.writeCellValue(5, resRowNum, resReport.getUnCheckedNum());
            resRowNum++;
        }

        // 按仓库统计盘点
        writer.writeCellValue(0, resRowNum, "2、按仓库统计盘点情况");
        writer.setStyle(blodStyle, "A" + ++resRowNum);
        writer.writeCellValue(0, resRowNum, "仓库名称")
                .writeCellValue(1, resRowNum, "正常（实盘）")
                .writeCellValue(2, resRowNum, "盘亏（实盘）")
                .writeCellValue(3, resRowNum, "盘盈-在册（实盘）")
                .writeCellValue(4, resRowNum, "上报盘盈（实盘）")
                .writeCellValue(5, resRowNum, "盘亏（未盘）")
                .writeCellValue(6, resRowNum, "合计");
        resRowNum++;
        List<MaterialInventoryResultReportDto.GroupReport> inventoryRepositoryList = resultReportDto.getInventoryRepository();
        for (MaterialInventoryResultReportDto.GroupReport groupReport : inventoryRepositoryList) {
            writer.writeCellValue(0, resRowNum, groupReport.getName());
            writer.writeCellValue(1, resRowNum, groupReport.getCheckedNum());
            writer.writeCellValue(2, resRowNum, groupReport.getLossNum());
            writer.writeCellValue(3, resRowNum, groupReport.getSurplusNum());
            writer.writeCellValue(4, resRowNum, groupReport.getReportNum());
            writer.writeCellValue(5, resRowNum, groupReport.getUnCheckedNum());
            writer.writeCellValue(6, resRowNum, groupReport.getTotalNum());
            resRowNum++;
        }

        // 按分类统计盘点
        writer.writeCellValue(0, resRowNum, "3、按分类统计盘点情况");
        writer.setStyle(blodStyle, "A" + ++resRowNum);
        writer.writeCellValue(0, resRowNum, "分类名称")
                .writeCellValue(1, resRowNum, "正常（实盘）")
                .writeCellValue(2, resRowNum, "盘亏（实盘）")
                .writeCellValue(3, resRowNum, "盘盈-在册（实盘）")
                .writeCellValue(4, resRowNum, "上报盘盈（实盘）")
                .writeCellValue(5, resRowNum, "盘亏（未盘）")
                .writeCellValue(6, resRowNum, "合计");
        resRowNum++;
        List<MaterialInventoryResultReportDto.GroupReport> inventoryCategoryList = resultReportDto.getInventoryCategory();
        for (MaterialInventoryResultReportDto.GroupReport groupReport : inventoryCategoryList) {
            writer.writeCellValue(0, resRowNum, groupReport.getName());
            writer.writeCellValue(1, resRowNum, groupReport.getCheckedNum());
            writer.writeCellValue(2, resRowNum, groupReport.getLossNum());
            writer.writeCellValue(3, resRowNum, groupReport.getSurplusNum());
            writer.writeCellValue(4, resRowNum, groupReport.getReportNum());
            writer.writeCellValue(5, resRowNum, groupReport.getUnCheckedNum());
            writer.writeCellValue(6, resRowNum, groupReport.getTotalNum());
            resRowNum++;
        }

        // 按仓库分类统计盘点
        writer.writeCellValue(0, resRowNum, "4、按各仓库各分类的耗材统计盘点情况");
        writer.setStyle(blodStyle, "A" + ++resRowNum);
        writer.writeCellValue(0, resRowNum, "仓库名称")
                .writeCellValue(1, resRowNum, "分类名称")
                .writeCellValue(2, resRowNum, "正常（实盘）")
                .writeCellValue(3, resRowNum, "盘亏（实盘）")
                .writeCellValue(4, resRowNum, "盘盈-在册（实盘）")
                .writeCellValue(5, resRowNum, "上报盘盈（实盘）")
                .writeCellValue(6, resRowNum, "盘亏（未盘）")
                .writeCellValue(7, resRowNum, "合计");
        resRowNum++;
        List<MaterialInventoryResultReportDto.RepoCateReport> repositoryCategory = resultReportDto.getRepositoryCategory();

        Map<String, Long> resultReportGroup = repositoryCategory.stream()
                .filter(f -> f.getRepoId() != null)
                .collect(Collectors.groupingBy(MaterialInventoryResultReportDto.RepoCateReport::getRepoId,
                        Collectors.counting()));
        // 计算仓库合并
        int repoSize = 0;
        int cateSize = 0;
        for (MaterialInventoryResultReportDto.RepoCateReport repoCateReport : repositoryCategory) {
            repoSize++;
            cateSize++;
            if ("合计".equals(repoCateReport.getRepoName())) {
                writer.merge(resRowNum, resRowNum, 0, 1, repoCateReport.getRepoName(), false);
            } else if ("占比".equals(repoCateReport.getRepoName())) {
                writer.merge(resRowNum, resRowNum, 0, 1, repoCateReport.getRepoName(), false);
            } else {
                writer.writeCellValue(0, resRowNum, repoCateReport.getRepoName());
                writer.writeCellValue(1, resRowNum, repoCateReport.getCateName());
            }
            writer.writeCellValue(2, resRowNum, repoCateReport.getCheckedNum());
            writer.writeCellValue(3, resRowNum, repoCateReport.getLossNum());
            writer.writeCellValue(4, resRowNum, repoCateReport.getSurplusNum());
            writer.writeCellValue(5, resRowNum, repoCateReport.getReportNum());
            writer.writeCellValue(6, resRowNum, repoCateReport.getUnCheckedNum());
            writer.writeCellValue(7, resRowNum, repoCateReport.getTotalNum());
            if (resultReportGroup.containsKey(repoCateReport.getRepoId())) {
                long size = resultReportGroup.get(repoCateReport.getRepoId());
                if (size == repoSize) {
                    writer.merge(resRowNum - repoSize + 1, resRowNum, 0, 0, repoCateReport.getRepoName(), false);
                    // 重置
                    repoSize = 0;
                }
                if (cateSize == 2) {
                    writer.merge(resRowNum - 1, resRowNum, 1, 1, repoCateReport.getCateName(), false);
                    // 重置
                    cateSize = 0;
                }
            }
            resRowNum++;
        }
        // ---------------------- 审核人 ----------------------
        writer.writeCellValue(5, ++resRowNum, "盘点单审核人：");
        return writer;
    }

    private List<ExcelExportDto> loadInventoryDetailExcel(List<MaterialInventoryDetailPageDto> inventoryDetailList,
                                                          LinkedHashMap<String, String> headerData,
                                                          List<AssetHeadDto> headDtos) {
        List<ExcelExportDto> result = new ArrayList<>();
        // 正常
        List<MaterialInventoryDetailPageDto> checkedList = new ArrayList<>();
        // 盘盈在册
        List<MaterialInventoryDetailPageDto> surplusList = new ArrayList<>();
        // 上报盘盈
        List<MaterialInventoryDetailPageDto> reportList = new ArrayList<>();
        // 盘亏
        List<MaterialInventoryDetailPageDto> lossList = new ArrayList<>();

        for (MaterialInventoryDetailPageDto detailPageDto : inventoryDetailList) {
            if (detailPageDto.getInventoryStatus() == 1) {
                // 正常
                checkedList.add(detailPageDto);
            } else if (detailPageDto.getInventoryStatus() == 2 && detailPageDto.getMaterialSource() == 1) {
                // 盘盈在册
                surplusList.add(detailPageDto);
            } else if (detailPageDto.getInventoryStatus() == 2 && detailPageDto.getMaterialSource() == 2) {
                // 上报盘盈
                reportList.add(detailPageDto);
            } else if (detailPageDto.getInventoryStatus() == 3) {
                // 盘亏
                lossList.add(detailPageDto);
            }
        }

        headDtos.add(new AssetHeadDto()
                .setCode("inventoryTime")
                .setName("盘点时间")
                .setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"))
                .setType(FormFieldCO.DATETIME));
        Cache cache = localTransCache(headDtos);

        // 写入正常
        LinkedHashMap<String, String> checkedExcelHeader = new LinkedHashMap<>(headerData);
        checkedExcelHeader.put("avgPrice", "价值（加权平均）");
        checkedExcelHeader.put("currentQuantity", "当前库存");
        checkedExcelHeader.put("totalMoney", "库存总金额（元）");
        checkedExcelHeader.put("actualQuantity", "实盘库存");
        checkedExcelHeader.put("inventoryTerminalText", "盘点终端");
        checkedExcelHeader.put("actualInventoryUserText", "盘点人");
        checkedExcelHeader.put("inventoryModeText", "盘点方式");
        checkedExcelHeader.put("inventoryTime", "盘点时间");
        ExcelExportDto checkedExcel = new ExcelExportDto();
        checkedExcel.setSheetName("正常（" + checkedList.size() + "）")
                .setHeaderData(checkedExcelHeader)
                .setRows(checkedList.parallelStream()
                        .map(f -> {
                            JSONObject translate = f.translate();
                            convertExcelData(translate, cache);
                            return translate;
                        }).collect(Collectors.toList()));

        // 写入盘盈在册数据
        LinkedHashMap<String, String> surplusExcelHeader = new LinkedHashMap<>(headerData);
        surplusExcelHeader.put("avgPrice", "价值（加权平均）");
        surplusExcelHeader.put("currentQuantity", "当前库存");
        surplusExcelHeader.put("totalMoney", "库存总金额（元）");
        surplusExcelHeader.put("actualQuantity", "实盘库存");
        surplusExcelHeader.put("surplusQuantity", "盘盈数量");
        surplusExcelHeader.put("inventoryTerminalText", "盘点终端");
        surplusExcelHeader.put("actualInventoryUserText", "盘点人");
        surplusExcelHeader.put("inventoryModeText", "盘点方式");
        surplusExcelHeader.put("inventoryTime", "盘点时间");
        ExcelExportDto surplusExcel = new ExcelExportDto();
        surplusExcel.setSheetName("盘盈-在册（" + surplusList.size() + "）")
                .setHeaderData(surplusExcelHeader)
                .setRows(surplusList.parallelStream()
                        .map(f -> {
                            JSONObject translate = f.translate();
                            convertExcelData(translate, cache);
                            return translate;
                        }).collect(Collectors.toList()));


        // 写入上报盘盈数据
        LinkedHashMap<String, String> reportExcelHeader = new LinkedHashMap<>(headerData);
        reportExcelHeader.put("actualQuantity", "实盘库存");
        reportExcelHeader.put("surplusQuantity", "盘盈数量");
        reportExcelHeader.put("inventoryTerminalText", "盘点终端");
        reportExcelHeader.put("actualInventoryUserText", "盘点人");
        reportExcelHeader.put("inventoryModeText", "盘点方式");
        reportExcelHeader.put("inventoryTime", "盘点时间");
        ExcelExportDto reportExcel = new ExcelExportDto();
        reportExcel.setSheetName("上报盘盈（" + reportList.size() + "）")
                .setHeaderData(reportExcelHeader)
                .setRows(reportList.parallelStream()
                        .map(f -> {
                            JSONObject translate = f.translate();
                            convertExcelData(translate, cache);
                            return translate;
                        }).collect(Collectors.toList()));

        // 写入盘亏数据
        LinkedHashMap<String, String> lossExcelHeader = new LinkedHashMap<>(headerData);
        lossExcelHeader.put("avgPrice", "价值（加权平均）");
        lossExcelHeader.put("currentQuantity", "当前库存");
        lossExcelHeader.put("totalMoney", "库存总金额（元）");
        lossExcelHeader.put("actualQuantity", "实盘库存");
        lossExcelHeader.put("lossQuantity", "盘亏数量");
        lossExcelHeader.put("inventoryTerminalText", "盘点终端");
        lossExcelHeader.put("actualInventoryUserText", "盘点人");
        lossExcelHeader.put("inventoryModeText", "盘点方式");
        lossExcelHeader.put("inventoryTime", "盘点时间");
        ExcelExportDto lossExcel = new ExcelExportDto();
        lossExcel.setSheetName("盘亏（" + lossList.size() + "）")
                .setHeaderData(lossExcelHeader)
                .setRows(lossList.parallelStream()
                        .map(f -> {
                            JSONObject translate = f.translate();
                            convertExcelData(translate, cache);
                            return translate;
                        }).collect(Collectors.toList()));
        result.add(checkedExcel);
        result.add(surplusExcel);
        result.add(reportExcel);
        result.add(lossExcel);
        return result;
    }

    private Cache localTransCache(List<AssetHeadDto> assetHeadView) {
        Cache cache = new Cache();
        // 查询日期类型
        for (AssetHeadDto it : assetHeadView) {
            if (FormFieldCO.DATETIME.equals(it.getType())) {
                cache.getDateFormatType().put(it.getCode(), it.getFieldProps().getString("dateFormatType"));
            } else if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getType())) {
                cache.getMultiSelectSet().add(it.getCode());
            } else if (FormFieldCO.NUMBER_INPUT.equals(it.getType())) {
                if (it.getFieldProps() != null) {
                    cache.getNumberMap().put(it.getCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
                }
            } else if (FormFieldCO.IMAGES.equals(it.getType())
                    || FormFieldCO.FILES.equals(it.getType())) {
                cache.getImageSet().add(it.getCode());
            }
        }
        return cache;
    }

    @Data
    private static class Cache {
        private Map<String, String> dateFormatType = new HashMap<>();
        private Set<String> multiSelectSet = new HashSet<>();
        private Set<String> imageSet = new HashSet<>();
        private Map<String, Boolean> numberMap = new HashMap<>();
    }

    private void convertExcelData(JSONObject snapshotData, Cache cache) {
        cache.getNumberMap().forEach((code, percentage) -> {
            Number number = Convert.toNumber(snapshotData.get(code));
            if (ObjectUtil.isNotNull(number)) {
                if (BooleanUtil.isTrue(percentage)) {
                    snapshotData.put(code, number + "%");
                } else {
                    snapshotData.put(code, number);
                }
            }
        });
        cache.getDateFormatType().forEach((code, fmt) -> {
            String date = snapshotData.getString(code);
            if (StrUtil.isNotEmpty(date)) {
                try {
                    LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                    snapshotData.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                } catch (Exception e) {
                    log.warn("[{}] [{}]转换时间异常", code, date);
                }
            }
        });
        cache.getMultiSelectSet().forEach(code -> {
            try {
                JSONArray jsonArray = snapshotData.getJSONArray(code);
                if (CollUtil.isNotEmpty(jsonArray)) {
                    List<String> strings = jsonArray.toJavaList(String.class);
                    String collect = String.join(",", strings);
                    snapshotData.put(code, collect);
                } else {
                    snapshotData.put(code, null);
                }
            } catch (Exception e) {
                log.warn("[{}] [{}]转换数组异常", code, snapshotData.get(code));
            }
        });
        cache.getImageSet().forEach(code -> {
            try {
                JSONArray jsonArray = snapshotData.getJSONArray(code);
                if (CollUtil.isNotEmpty(jsonArray)) {
                    List<String> strings = jsonArray.toJavaList(String.class);
                    String collect = String.join("\n", strings);
                    snapshotData.put(code, collect);
                } else {
                    snapshotData.put(code, null);
                }
            } catch (Exception e) {
                log.warn("[{}] [{}]转换数组异常", code, snapshotData.get(code));
            }
        });
    }

    private void clearThreadLocal() {
        companyCache.remove();
    }

    private String getDestPath(String module, String fileName) {
        Long companyId = companyCache.get().getCompany();
        return companyId + "/" + module + "/" + DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN) + "/" + fileName;
    }

    /**
     * 获取临时存放路径
     */
    private File getTempPath(String module) {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", module, currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

}
