package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.SolutionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.SolutionConfigDto;
import com.niimbot.system.SolutionDetailDto;
import com.niimbot.system.SolutionQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午4:32
 */
@Api(tags = "解决方案配置")
@ResultController
@RequestMapping("/api/solutionConfig/")
@RequiredArgsConstructor
public class SolutionConfigController {

    private final SolutionConfigFeignClient solutionConfigFeignClient;

    @ApiOperation(value = "解决方案配置列表")
    @GetMapping(value = "query")
    public PageUtils<SolutionConfigDto> pageQuery(SolutionQueryDto request) {
        return solutionConfigFeignClient.query(request);
    }

    @ApiOperation(value = "解决方案配置详情")
    @GetMapping(value = "detail/{configId}")
    public SolutionDetailDto detail(@PathVariable("configId") Long configId) {
        return solutionConfigFeignClient.detail(configId);
    }
}
