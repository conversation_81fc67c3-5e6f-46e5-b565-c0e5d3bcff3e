package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.service.feign.MessageFeignClient;
import com.niimbot.asset.service.feign.VersionManagementFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.MessageIsNoticeDto;
import com.niimbot.system.VersionManagementDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api("【PC端拉取消息】")
@ResultController
@RequestMapping("/api/pc/message")
@RequiredArgsConstructor
public class PcMessageController {

    private final VersionManagementFeignClient versionManagementFeignClient;

    private final MessageFeignClient messageFeignClient;

    @ApiOperation("PC端拉取弹窗消息")
    @GetMapping("/alert")
    public Map<String, Object> getNewMessage(){
        VersionManagementDto versionManagementDto = versionManagementFeignClient.latestVersionMessage();
        MessageIsNoticeDto messageIsNoticeDto = messageFeignClient.latestNotice(ServletUtils.getClientSource().getValue());
        if (versionManagementDto == null && messageIsNoticeDto == null){
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        if (messageIsNoticeDto != null){
            messageIsNoticeDto.setCode(MessageConstant.Code.YYGG.getCode());
            result.put("notice",messageIsNoticeDto);
            result.put("alert", "notice");
        }
        if (versionManagementDto != null){
            result.put("version", versionManagementDto);
            result.put("alert", "version");
        }
        return result;
    }


    @ApiModelProperty("版本消息已读")
    @PutMapping("/version/read/{id}")
    public Boolean versionMessageRead(@PathVariable Long id) {
           return versionManagementFeignClient.versionMessageRead(id);
    }
}
