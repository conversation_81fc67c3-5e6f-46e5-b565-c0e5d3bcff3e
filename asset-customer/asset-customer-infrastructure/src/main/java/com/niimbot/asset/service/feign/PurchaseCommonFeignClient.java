package com.niimbot.asset.service.feign;

import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionSortDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2022/10/18 16:46
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PurchaseCommonFeignClient {

    /**
     * 单据表单查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "server/purchase/common/form/{orderType}")
    FormVO getForm(@PathVariable("orderType") Integer orderType);

    /**
     * 排序字段
     *
     * @param orderType
     * @return
     */
    @GetMapping("server/purchase/common/sortField/{orderType}")
    QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType);
}
