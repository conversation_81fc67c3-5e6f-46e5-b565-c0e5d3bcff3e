package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.MaterialDto;
import com.niimbot.material.MaterialQueryDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/7/7 10:17
 */
public interface MaterialService {

    PageUtils<JSONObject> materialPage(MaterialQueryDto queryDto);

    JSONObject getInfo(Long materialId);

    JSONObject getInfo(Long repositoryId, Long materialId);

    JSONObject getInfoNoPerm(String materialId);

    Map<Long, JSONObject> getInfoMap(List<Long> materialIds);

    MaterialDto add(MaterialDto materialDto);

    Boolean edit(MaterialDto materialDto);
}
