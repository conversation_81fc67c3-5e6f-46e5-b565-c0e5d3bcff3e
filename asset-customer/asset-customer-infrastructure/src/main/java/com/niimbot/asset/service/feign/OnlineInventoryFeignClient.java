package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.ding.*;
import com.niimbot.inventory.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface OnlineInventoryFeignClient {
    /**
     * app手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    @PutMapping(value = "server/online/inventory/task/appManualInventory")
    Boolean appManualInventory(@RequestBody InventoryManualDto dto);

    /**
     * 修改盘点备注
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/updateRemark")
    Boolean updateRemark(@RequestBody InventoryRemarkDto dto);

    /**
     * 当前区域盘点进度统计-小程序使用
     *
     * @param queryDto
     * @return
     */
    @GetMapping("server/online/inventory/asset/areaStatistics")
    InventoryAssetAreaStatisticsDto areaStatistics(@SpringQueryMap InventoryAssetAreaQueryDto queryDto);

    /**
     * RFID盘点
     *
     * @param submitDto
     * @return
     */
    @PostMapping("server/online/inventory/asset/rfid/submit")
    InventoryAssetRFIDResultDto assetRFID(@RequestBody InventoryAssetRFIDSubmitDto submitDto);

    @GetMapping(value = "server/online/inventory/asset/appCheckAssetId")
    long appCheckAssetId(@RequestParam("assetId") String assetId,
                         @RequestParam("inventoryId") Long inventoryId,
                         @RequestParam("inventoryTaskId") Long inventoryTaskId);

    @PostMapping(value = "server/online/inventory/asset/post/appAssetCode")
    List<InventoryAssetDataCountDto> appAssetCode(@RequestBody InventorySurplusQueryDto dto);

    @PostMapping(value = "server/online/inventory/asset/manualInventory/batch")
    Boolean manualInventoryBatch(InventoryManualBatchDto dto);

    @PostMapping(value = "server/online/inventory/asset/updateRemark/batch")
    Boolean updateRemarkBatch(InventoryRemarkBatchDto dto);

    @GetMapping(value = "server/online/inventory/task/page")
    PageUtils<DingInventoryTaskListDto> onlineTaskPage(@SpringQueryMap DingInventoryQueryDto queryDto);
}
