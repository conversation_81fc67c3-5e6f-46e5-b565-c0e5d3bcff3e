package com.niimbot.asset.service;

import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.luckysheet.LuckySheetModel;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/19 下午3:56
 */
public interface AssetFinanceService {

    int MAX_BATCH = 5000;

    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId);

    List<List<LuckySheetModel>> importError(Long taskId);

    Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);

    /**
     * 导出待入账资产
     * @param orgId
     * @return
     */
    ExportResponse exportWaitAsset(Long orgId);
}
