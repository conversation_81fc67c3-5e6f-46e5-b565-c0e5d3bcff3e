package com.niimbot.asset.service.feign.equipment;

import com.niimbot.equipment.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/11 下午4:26
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentSparePartsFeignClient {

    /**
     * 根据设备id，查询耗材信息
     * @param assetId
     * @return
     */
    @GetMapping("server/equipment/spareParts/queryMaterialInfo")
    List<SparePartsMaterialDto> queryMaterialInfo(@RequestParam("assetId") Long assetId);

    /**
     * 批量查询设备备件信息
     * @param assetIds
     * @return
     */
    @GetMapping("server/equipment/spareParts/queryByAssetId")
    List<EquipmentSparePartsDto> queryByAssetId(@RequestParam("assetIds") List<Long> assetIds);

    /**
     * 添加备件信息
     * @param sparePartsCreateDto
     * @return
     */
    @PostMapping("server/equipment/spareParts/create")
    Boolean createSpareParts(@RequestBody SparePartsCreateDto sparePartsCreateDto);

    /**
     * 修改备件信息
     * @param sparePartsCreateDto
     * @return
     */
    @PostMapping("server/equipment/spareParts/modify")
    Boolean modifySpareParts(@RequestBody SparePartsCreateDto sparePartsCreateDto);

    /**
     * 删除备件信息
     * @param sparePartsDropDto
     * @return
     */
    @PostMapping("server/equipment/spareParts/remove")
    Boolean dropSpareParts(@RequestBody SparePartsDropDto sparePartsDropDto);

    /**
     * 设备履历信息
     *
     * @param assetId
     * @return
     */
    @GetMapping("server/equipment/spareParts/statistics")
    SparePartsStatisticsDto statistics(@RequestParam("assetId") Long assetId);

}
