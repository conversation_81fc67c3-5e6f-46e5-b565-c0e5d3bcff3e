package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.finance.AssetFinanceDefaultDto;
import com.niimbot.system.SolutionConfigDto;
import com.niimbot.system.SolutionDetailDto;
import com.niimbot.system.SolutionQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午4:37
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface SolutionConfigFeignClient {

    /**
     * 解决方案分页查询
     * @param queryDto
     * @return
     */
    @GetMapping(value = "server/system/solution/query")
    PageUtils<SolutionConfigDto> query(@SpringQueryMap SolutionQueryDto queryDto);

    /**
     * 解决方案详情查询
     * @param configId
     * @return
     */
    @GetMapping(value = "server/system/solution/detail/{configId}")
    SolutionDetailDto detail(@PathVariable("configId") Long configId);
}
