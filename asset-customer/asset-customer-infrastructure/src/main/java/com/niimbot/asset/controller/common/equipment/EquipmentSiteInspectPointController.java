package com.niimbot.asset.controller.common.equipment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.EquipmentSiteInspectPointService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectPointFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.equipment.AuditableRemovePointResult;
import com.niimbot.equipment.EquipmentSiteInspectPointExportDto;
import com.niimbot.equipment.EquipmentSiteInspectPointQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.ImportDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.swing.text.StyledEditorKit;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "巡检点位管理")
@ResultController
@RequestMapping("api/common/equipment/site/inspect/point")
@Slf4j
public class EquipmentSiteInspectPointController {

    @Autowired
    private EquipmentSiteInspectPointFeignClient feignClient;

    @Autowired
    private EquipmentSiteInspectPointService inspectPointService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CacheResourceUtil cacheResourceUtil;

    @Autowired
    private DictConvertUtil dictConvertUtil;

    @ApiOperation(value = "巡检点位分页列表")
    @AutoConvert
    @PostMapping(value = "/page")
    public PageUtils<AsEquipmentSiteInspectPointDto> page(@RequestBody EquipmentSiteInspectPointQueryDto queryDto) {
        return feignClient.pagePoint(queryDto);
    }

    @ApiOperation(value = "巡检点位分页列表")
    @AutoConvert
    @GetMapping(value = "/list")
    public List<AsEquipmentSiteInspectPointDto> list(EquipmentSiteInspectPointQueryDto queryDto) {
        return feignClient.listPoint(queryDto);
    }

    @ApiOperation(value = "巡检点位分页列表")
    @PostMapping(value = "/ids")
    public List<Map<String, Object>> ids(@RequestBody List<Long> ids) {
        List<AsEquipmentSiteInspectPointDto> list = feignClient.listIds(ids);
        dictConvertUtil.convertToDictionary(list);
        return list.stream().map(v -> MapBuilder.create(new HashMap<String, Object>(3))
                .put("id", String.valueOf(v.getId()))
                .put("name", v.getPointCode())
                .put("code", v.getPointName())
                .put("extra", ImmutableMap.of("specificLocation", v.getSpecificLocation(), "storageAreaText", v.getStorageAreaText(),"areaCode",StringUtils.isNotEmpty(v.getAreaCode())?v.getAreaCode():""))
                .build()).collect(Collectors.toList());
    }

    @ApiOperation(value = "新增巡检点位数据")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    @AuditLog(Auditable.Action.ADD_SITE_INSPECT_POINT)
    public AsEquipmentSiteInspectPointDto pointAdd(@Validated(Insert.class) @RequestBody AsEquipmentSiteInspectPointDto dto) {
        return new AsEquipmentSiteInspectPointDto().setId(feignClient.insertInspectPoint(dto));
    }

    @ApiOperation(value = "编辑巡检点位数据")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_SITE_INSPECT_POINT)
    public Boolean edit(@Validated(Update.class) @RequestBody AsEquipmentSiteInspectPointDto dto) {
        return feignClient.updateInspectPoint(dto);
    }

    @ApiOperation(value = "删除巡检点位数据")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@PathVariable("id") Long id) {
        AuditableRemovePointResult result = feignClient.deleteInspectPointById(id);
        if (!Objects.isNull(result)) {
            AuditLogs.sendRecord(() -> {
                Map<String, Object> params = Auditable.Resolver.resolveParams(result);
                return AuditLogRecord.create(Auditable.Action.DEL_SITE_INSPECT_POINT, params);
            });
            return true;
        } else {
            return false;
        }
    }

    @ApiOperation(value = "获取巡检点位编码")
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return feignClient.recommendCode();
    }

    @ApiOperation(value = "巡检点位导出")
    @PostMapping("/export")
    public void pointExport(@RequestBody EquipmentSiteInspectPointQueryDto queryDto, HttpServletResponse response) {
        try {
            // 查询数据
            queryDto.setPageNum(1);
            queryDto.setPageSize(Integer.MAX_VALUE);
            PageUtils<AsEquipmentSiteInspectPointDto> pagePoints = feignClient.pagePoint(queryDto);
            List<EquipmentSiteInspectPointExportDto> excel = new ArrayList<>();
            for (AsEquipmentSiteInspectPointDto pointDto : pagePoints.getList()) {
                EquipmentSiteInspectPointExportDto pointExportDto = new EquipmentSiteInspectPointExportDto().setPointCode(pointDto.getPointCode())
                        .setPointName(pointDto.getPointName())
                        .setOrgName(cacheResourceUtil.getOrgName(pointDto.getOrgId()))
                        .setPidName(cacheResourceUtil.getAreaName(pointDto.getPid()))
                        .setSpecificLocation(pointDto.getSpecificLocation())
                        .setPointDesc(pointDto.getPointDesc())
                        .setPointImages(StringUtils.join(pointDto.getPointImages(),","));
                excel.add(pointExportDto);
            }
            // 查询head
            LinkedHashMap<String, String> header = ExcelUtils.buildExcelHead(EquipmentSiteInspectPointExportDto.class);
            ExcelUtils.export(response, new ExcelExportDto(header, excel), "点位信息-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx");
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_SITE_INSPECT_POINT, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(pagePoints.getList().size()))));
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    @ApiOperation(value = "【PC】导出巡检点位模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        inspectPointService.exportTemplate(response);
    }

    @ApiOperation(value = "【PC】导入巡检点位")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            inspectPointService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error("import point error:", e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return inspectPointService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        inspectPointService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.INSPECT_POINT));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation("巡检点位详情")
    @AutoConvert
    @GetMapping("/detail")
    public AsEquipmentSiteInspectPointDto pointDetail(@RequestParam("pointId") Long pointId) {
        return feignClient.pointDetail(pointId);
    }
}
