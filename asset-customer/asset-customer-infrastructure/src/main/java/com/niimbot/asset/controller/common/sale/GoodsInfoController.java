package com.niimbot.asset.controller.common.sale;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.GoodsInfoFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.sale.GoodsCurrencyInfoDto;
import com.niimbot.sale.GoodsInfoDto;
import com.niimbot.system.CompanyDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/29 14:46
 */
@Api(tags = "【服务中心】Sku信息")
@ResultController
@RequestMapping("api/common/goods")
@RequiredArgsConstructor
public class GoodsInfoController {

    private final GoodsInfoFeignClient goodsInfoFeignClient;

    private final CompanyFeignClient companyFeignClient;

    @ApiOperation(value = "精条")
    @GetMapping("/currency")
    public GoodsCurrencyInfoDto getCurrency() {
        GoodsInfoDto sku = goodsInfoFeignClient.getSkuOne(DictConstant.PRODUCT_TYPE_ASSET_CURRENCY);
        if (sku == null) {
            throw new BusinessException(SaleResultCode.SKU_NOT_FOUND);
        }
        GoodsCurrencyInfoDto infoDto = new GoodsCurrencyInfoDto();
        JSONObject info = sku.getInfo();
        infoDto.setPrice(sku.getPrice());
        // 付费 payUserMin | 未付费 nonPayUserMin
        CompanyDto companyInfo = companyFeignClient.getCompanyInfo(LoginUserThreadLocal.getCompanyId());
        infoDto.setMin(info.getInteger(BooleanUtil.isTrue(companyInfo.getIsPay()) ? "payUserMin" : "nonPayUserMin"));
        return infoDto;
    }

    @ApiOperation(value = "资产使用费")
    @GetMapping("/useCost")
    public List<GoodsInfoDto> getUseCost() {
        return goodsInfoFeignClient.getSkuList(DictConstant.PRODUCT_TYPE_ASSET_USE_COST);
    }

}
