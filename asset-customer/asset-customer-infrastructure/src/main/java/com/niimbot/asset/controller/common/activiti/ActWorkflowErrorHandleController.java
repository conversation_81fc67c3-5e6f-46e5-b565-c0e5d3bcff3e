package com.niimbot.asset.controller.common.activiti;

import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.service.feign.ActWorkflowErrorHandleFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 审批流手工处理错误回调数据（内部人员使用）
 *
 * <AUTHOR>
 * @date 2023/2/15 15:53
 */
@ResultController
@RequestMapping("api/common/workflow")
public class ActWorkflowErrorHandleController {

    private final ActWorkflowErrorHandleFeignClient feignClient;

    public ActWorkflowErrorHandleController(ActWorkflowErrorHandleFeignClient feignClient) {
        this.feignClient = feignClient;
    }

    @PostMapping("/callback/error/{orderType}")
    public Boolean callBackError(@PathVariable("orderType") Integer orderType,
                                 @RequestBody WorkflowCallbackDto callbackDto) {
        switch (orderType) {
            case AssetConstant.ORDER_TYPE_RECEIVE:
            case AssetConstant.ORDER_TYPE_RETURN:
            case AssetConstant.ORDER_TYPE_BORROW:
            case AssetConstant.ORDER_TYPE_BACK:
            case AssetConstant.ORDER_TYPE_ALLOCATE:
            case AssetConstant.ORDER_TYPE_DISPOSE:
            case AssetConstant.ORDER_TYPE_CHANGE:
                return feignClient.assetOrder(callbackDto);
            case AssetConstant.ORDER_TYPE_REPAIR_REPORT:
                return feignClient.orderRepairReport(callbackDto);
            case AssetConstant.ORDER_TYPE_REPAIR:
                return feignClient.orderRepair(callbackDto);
            case AssetConstant.ORDER_TYPE_PURCHASE:
                return feignClient.orderPurchase(callbackDto);
            case AssetConstant.ORDER_TYPE_PURCHASE_ORDER:
                return feignClient.orderPurchaseOrder(callbackDto);
            case AssetConstant.ORDER_TYPE_MAINTAIN:
                return feignClient.orderMaintain(callbackDto);
            case AssetConstant.ORDER_TYPE_STORE:
                return feignClient.orderStore(callbackDto);
            case AssetConstant.ORDER_TYPE_MATERIAL_RK:
                return feignClient.orderMaterialRk(callbackDto);
            case AssetConstant.ORDER_TYPE_MATERIAL_CK:
                return feignClient.orderMaterialCk(callbackDto);
            case AssetConstant.ORDER_TYPE_MATERIAL_LY:
                return feignClient.orderMaterialLy(callbackDto);
            case AssetConstant.ORDER_TYPE_MATERIAL_TZ:
                return feignClient.orderMaterialTz(callbackDto);
            case AssetConstant.ORDER_TYPE_MATERIAL_DB:
                return feignClient.orderMaterialDb(callbackDto);
            case AssetConstant.ORDER_TYPE_MATERIAL_BS:
                return feignClient.orderMaterialBs(callbackDto);
            default:
                return false;
        }
    }

}
