package com.niimbot.asset.websocket;

import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 心跳检测器
 *
 * <AUTHOR>
 * @date 2021/12/24 17:35
 */
@Slf4j
public class WebSocketHeartChecker implements Runnable {
    /**
     * 后台清理线程是否已经开启.
     */
    private static AtomicBoolean started = new AtomicBoolean(false);
    /**
     * 失效时间3分钟
     */
    private static final Long EXPIRED = 3 * 60 * 1000L;


    public static void start() {
        if (WebSocketHeartChecker.started.getAndSet(true)) {
            return;
        }
        new Thread(new WebSocketHeartChecker()).start();
    }

    @Override
    public void run() {
        log.info("WebSocket 心跳检测器已启动");
        while (true) {
            try {
                ConcurrentHashMap<String, WebSocketServer> collections = WebSocketServer.getCollections();
                for (WebSocketServer socketServer : collections.values()) {
//                    if (log.isDebugEnabled()) {
//                        log.debug("WebSocket -> 用户: {}, 检测心跳", socketServer.getUserId());
//                    }
                    LocalDateTime now = LocalDateTime.now();
                    long currentTimeMillis = now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    long socketLast = socketServer.getLastHeart();
                    long duration = currentTimeMillis - socketLast;
                    if (duration > EXPIRED) {
                        String lastTime = Instant.ofEpochMilli(socketLast).atZone(ZoneId.systemDefault())
                                .toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        String nowStr  = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        log.info("WebSocket -> 用户: {}, 当前检测时间: {}, 最后一次心跳时间: {} ,已超时, 关闭连接",
                                socketServer.getUserId(), nowStr, lastTime);
                        socketServer.onClose();
                    }
                }
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }
    }
}
