package com.niimbot.asset.service.feign;

import com.niimbot.activiti.ActWorkflowCopyViewDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/9/15 上午10:19
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface WorkflowTodoFeignClient {

    @PostMapping("server/activiti/workflow/sync/copy")
    Integer syncCopy(@RequestBody ActWorkflowCopyViewDto workflowCopy);

    @PostMapping("server/activiti/workflow/sync/copyProc")
    Boolean syncProc(@RequestBody ActWorkflowCopyViewDto workflowCopy);

    @PostMapping("server/activiti/workflow/sync/copyRequest")
    Integer copyRequest(@RequestBody ActWorkflowCopyViewDto workflowCopy);

    @PostMapping("server/activiti/workflow/sync/copyRequestProc")
    Boolean copyRequestProc(@RequestBody ActWorkflowCopyViewDto workflowCopy);
}
