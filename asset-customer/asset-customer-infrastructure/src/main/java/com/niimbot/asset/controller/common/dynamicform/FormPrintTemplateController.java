package com.niimbot.asset.controller.common.dynamicform;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.FormPrintTemplateFeignClient;
import com.niimbot.dynamicform.FormPrintDefaultTplDto;
import com.niimbot.dynamicform.FormPrintTemplateDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AsCusUserSettingDto;
import com.niimbot.system.Auditable;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023/7/15 12:24
 */
@Api(tags = "自定义单据模板管理")
@ResultController
@RequestMapping("api/common/printTemplate")
@Validated
public class FormPrintTemplateController {

    private final FormPrintTemplateFeignClient formPrintTemplateFeignClient;
    private final CusUserSettingFeignClient settingFeignClient;

    public FormPrintTemplateController(FormPrintTemplateFeignClient formPrintTemplateFeignClient,
                                       CusUserSettingFeignClient settingFeignClient) {
        this.formPrintTemplateFeignClient = formPrintTemplateFeignClient;
        this.settingFeignClient = settingFeignClient;
    }

    @ApiOperation(value = "查询模板")
    @GetMapping("/{orderType}")
    public List<FormPrintTemplateDto> printTplList(@PathVariable("orderType") Integer orderType) {
        return formPrintTemplateFeignClient.printTplList(orderType);
    }

    @ApiOperation(value = "查询模板详情")
    @GetMapping("/detail/{tplId}")
    public FormPrintTemplateDto getPrintTpl(@PathVariable("tplId") Long tplId) {
        return formPrintTemplateFeignClient.getPrintTpl(tplId);
    }

    @ApiOperation(value = "新增模板")
    @RepeatSubmit
    @PostMapping
    @AuditLog(Auditable.Action.ADD_PRINT_TPL)
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public FormPrintTemplateDto add(@RequestBody @Validated(Insert.class) FormPrintTemplateDto formPrintTemplate) {
        return formPrintTemplateFeignClient.add(formPrintTemplate);
    }

    @ApiOperation(value = "更新模板")
    @PutMapping
    @AuditLog(Auditable.Action.UPT_PRINT_TPL)
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public FormPrintTemplateDto edit(@RequestBody @Validated(Update.class) FormPrintTemplateDto formPrintTemplate) {
        return formPrintTemplateFeignClient.edit(formPrintTemplate);
    }

    @ApiOperation(value = "删除模板")
    @DeleteMapping("/{tplId}")
    @AuditLog(Auditable.Action.DEL_PRINT_TPL)
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public FormPrintTemplateDto delete(@PathVariable("tplId") Long tplId) {
        return formPrintTemplateFeignClient.delete(tplId);
    }

    @ApiOperation(value = "复制模板")
    @PostMapping("/copy/{tplId}")
    @AuditLog(Auditable.Action.COPY_PRINT_TPL)
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public FormPrintTemplateDto copy(@PathVariable("tplId") Long tplId) {
        return formPrintTemplateFeignClient.copy(tplId);
    }

    @ApiOperation(value = "默认配置保存")
    @PostMapping(value = "/default/{orderType}")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean defaultTplSave(@PathVariable("orderType") Integer orderType,
                                  @RequestBody @Validated FormPrintDefaultTplDto tplDto) {
        List<FormPrintTemplateDto> templateDtos = formPrintTemplateFeignClient.printTplList(orderType);
        boolean present = templateDtos.stream().anyMatch(tpl -> tpl.getId().equals(tplDto.getId()));
        if (present) {
            AsCusUserSettingDto setting = settingFeignClient.getSetting();
            JSONObject printDefaultTpl = setting.getOrderPrintDefaultTpl();
            if (printDefaultTpl == null) {
                printDefaultTpl = new JSONObject();
            }
            printDefaultTpl.put(Convert.toStr(orderType), tplDto.getId());
            return settingFeignClient.updateSimplify(new AsCusUserSettingDto().setOrderPrintDefaultTpl(printDefaultTpl));
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "找不到对应的模板ID");
        }
    }

    @ApiOperation(value = "默认配置查询")
    @GetMapping(value = "/default/{orderType}")
    public FormPrintDefaultTplDto defaultTplGet(@PathVariable("orderType") Integer orderType) {
        List<FormPrintTemplateDto> templateDtos = formPrintTemplateFeignClient.printTplList(orderType);
        FormPrintDefaultTplDto defaultTplDto = new FormPrintDefaultTplDto();
        Set<Long> tplIds = new HashSet<>();
        for (FormPrintTemplateDto templateDto : templateDtos) {
            tplIds.add(templateDto.getId());
            if (templateDto.getCompanyId() == 0L) {
                defaultTplDto.setId(templateDto.getId());
            }
        }
        AsCusUserSettingDto setting = settingFeignClient.getSetting();
        JSONObject printDefaultTpl = setting.getOrderPrintDefaultTpl();
        if (printDefaultTpl != null) {
            Long defaultId = printDefaultTpl.getLong(Convert.toStr(orderType));
            if (tplIds.contains(defaultId)) {
                return defaultTplDto.setId(defaultId);
            }
        }
        return defaultTplDto;
    }

}
