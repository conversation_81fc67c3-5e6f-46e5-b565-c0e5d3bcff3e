package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.IndustryCaseConfigDto;
import com.niimbot.system.IndustryCaseDetailDto;
import com.niimbot.system.IndustryCaseQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午5:46
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface IndustryCaseConfigFeignClient {

    /**
     * 解决方案分页查询
     * @param queryDto
     * @return
     */
    @GetMapping(value = "server/system/industryCase/query")
    PageUtils<IndustryCaseConfigDto> query(@SpringQueryMap IndustryCaseQueryDto queryDto);

    /**
     * 解决方案详情查询
     * @param configId
     * @return
     */
    @GetMapping(value = "server/system/industryCase/detail/{configId}")
    IndustryCaseDetailDto detail(@PathVariable("configId") Long configId);
}
