package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.purchase.SupplierDto;
import com.niimbot.purchase.SupplierPageQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 供应商管理feign客户端
 *
 * <AUTHOR>
 * @date 2021/5/18 09:34
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface SupplierFeignClient {
    /**
     * 新增供应商
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/purchase/supplier")
    Long save(@RequestBody SupplierDto dto);

    /**
     * 更新供应商
     *
     * @param dto
     * @return
     */
    @PutMapping(value = "server/purchase/supplier")
    Boolean update(@RequestBody SupplierDto dto);

    /**
     * 删除供应商
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "server/purchase/supplier/{id}")
    Boolean delete(@PathVariable("id") Long id);

    /**
     * 供应商详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/purchase/supplier/{id}")
    SupplierDto getById(@PathVariable("id") Long id);

    /**
     * 供应商分页查询
     *
     * @param query
     * @return
     */
    @GetMapping(value = "server/purchase/supplier/page")
    PageUtils<SupplierDto> page(@SpringQueryMap SupplierPageQueryDto query);

    /**
     * 供应商列表查询
     *
     * @return
     */
    @GetMapping(value = "server/purchase/supplier/list")
    List<SupplierDto> list(@RequestParam(value = "name", required = false) String name);

    /**
     * 供应商列表查询
     *
     * @return
     */
    @PostMapping(value = "server/purchase/supplier/listByIds")
    List<SupplierDto> listByIds(@RequestBody List<Long> ids);
}
