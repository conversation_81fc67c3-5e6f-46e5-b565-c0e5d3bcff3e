package com.niimbot.asset.controller.common.material;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.MaterialOrderService;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.MaterialTkOrderFeignClient;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.material.MaterialOrderResponseDto;
import com.niimbot.material.MaterialTkOrderDetailDto;
import com.niimbot.material.MaterialTkOrderDto;
import com.niimbot.material.MaterialTkOrderSubmitDto;
import com.niimbot.system.Auditable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023/8/2 10:14
 */
@Api(tags = "【耗材】单据")
@ResultController
@RequestMapping("api/common/material/order/tk")
@Validated
public class MaterialTkOrderController {

    private final MaterialTkOrderFeignClient materialTkOrderFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final AsMaterialUtil materialUtil;
    private final MaterialService materialService;
    private final MaterialOrderService materialOrderService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @Autowired
    public MaterialTkOrderController(MaterialTkOrderFeignClient materialTkOrderFeignClient,
                                     ActWorkflowFeignClient workflowFeignClient,
                                     DictConvertUtil dictConvertUtil,
                                     AsMaterialUtil materialUtil,
                                     MaterialService materialService,
                                     MaterialOrderService materialOrderService) {
        this.materialTkOrderFeignClient = materialTkOrderFeignClient;
        this.workflowFeignClient = workflowFeignClient;
        this.dictConvertUtil = dictConvertUtil;
        this.materialUtil = materialUtil;
        this.materialService = materialService;
        this.materialOrderService = materialOrderService;
    }

    @ApiOperation(value = "【退库单】设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated MaterialTkOrderDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_TK);
        return materialTkOrderFeignClient.getWorkflowStepList(dto);

    }

    @ApiOperation(value = "【退库单】创建退库单")
    @PostMapping
    @RepeatSubmit
    @AuditLog(Auditable.Action.OR_MRL_TK)
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public AuditableCreateOrderResult create(@RequestBody @Validated MaterialTkOrderSubmitDto submitDto) {
        MaterialTkOrderDto orderDto = submitDto.getOrderDto();
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_TK);
        // 校验单据
        materialUtil.verifyAndTranslation(orderDto, AssetConstant.ORDER_TYPE_MATERIAL_TK);
        // 查询耗材信息
        List<Long> materialIds = orderDto.getMaterials().stream()
                .map(MaterialTkOrderDetailDto::getMaterialId).collect(Collectors.toList());
        Map<Long, JSONObject> infoMap = materialService.getInfoMap(materialIds);
        orderDto.getMaterials().forEach(detailDto -> {
            JSONObject data = infoMap.get(detailDto.getMaterialId());
            detailDto.setMaterialSnapshotData(data);
        });

        // 总种类
        int totalType = 0;
        // 总数量
        BigDecimal totalNum = BigDecimal.ZERO;
        for (MaterialTkOrderDetailDto m : orderDto.getMaterials()) {
            totalType += 1;
            totalNum = totalNum.add(m.getTkNum());
        }

        orderDto.setTotalNum(totalNum).setTotalType(totalType);

        List<JSONObject> sortList = materialIds.stream().map(infoMap::get).collect(Collectors.toList());
        orderDto.setSummary(materialUtil.buildSummary(sortList));
        return materialTkOrderFeignClient.create(submitDto);
    }

    @ApiOperation(value = "【退库单】退库单详情")
    @GetMapping("/{id}")
    @AutoConvert
    public MaterialOrderResponseDto getById(@PathVariable("id") Long id) {
        MaterialTkOrderDto orderDto = materialTkOrderFeignClient.getById(id);
        if (ObjectUtil.isNotNull(orderDto)) {
            dictConvertUtil.convertToDictionary(orderDto);
            orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_TK);
            // 翻译耗材
            JSONObject orderJson = materialUtil.toJSONObject(orderDto);
            //数据脱敏处理
            if (Objects.nonNull(orderJson)) {
                desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderJson), SensitiveObjectTypeEnum.MATERIAL.getCode());
            }
            if (orderDto.getApproveStatus() == 0) {
                return new MaterialOrderResponseDto()
                        .setOrder(orderJson)
                        .setApproveInfo(new WorkflowApproveInfoDto());
            }
            WorkflowApproveInfoDto approveInfoDto;
            if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_TK, id);
                if (approveInfoDto == null || approveInfoDto.getExecuteList().isEmpty()) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_TK, id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_TK, id);
                if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(orderDto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
            return new MaterialOrderResponseDto()
                    .setOrder(orderJson)
                    .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
        } else {
            throw new BusinessException(MaterialResultCode.ORDER_NOT_EXISTS, "退库单" + id);
        }
    }

    @ApiOperation(value = "【退库单】退库单批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<MaterialOrderResponseDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_TK);
        return requestDto.getOrderIds().stream().map(this::getById).collect(Collectors.toList());
    }

    @ApiOperation(value = "【退库单】退库单耗材快照")
    @GetMapping("/detail/{orderId}/{materialId}")
    public JSONObject getDetail(@PathVariable("orderId") Long orderId,
                                @PathVariable("materialId") Long materialId) {
        MaterialTkOrderDetailDto detailDto = materialTkOrderFeignClient.getDetail(orderId, materialId);
        if (detailDto == null) {
            throw new BusinessException(MaterialResultCode.ORDER_DETAIL_NOT_EXIST);
        }
        JSONObject result = materialUtil.toJSONObject(detailDto);
        //数据脱敏处理
        if (Objects.nonNull(result)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return result;
    }

    @ApiOperation(value = "【退库单】退库单分页")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialTkOrderDto> page = materialTkOrderFeignClient.page(query);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (MaterialTkOrderDto orderDto : page.getList()) {
            orderDto.setOrderType(OrderFormTypeEnum.TK.getCode());
            list.add(materialUtil.toJSONObject(orderDto));
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【退库单】退库单分页")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<JSONObject> pageGet(MaterialOrderQueryDto dto) {
        return page(dto);
    }

    @ApiOperation(value = "【退库单】退库单耗材详情")
    @GetMapping("/detail/page")
    public PageUtils<JSONObject> pageDetail(@Validated MaterialOrderDetailQueryDto query) {
        PageUtils<MaterialTkOrderDetailDto> page = materialTkOrderFeignClient.pageDetail(query);
        List<JSONObject> list = new ArrayList<>();
        for (MaterialTkOrderDetailDto detailDto : page.getList()) {
            JSONObject jsonObject = materialUtil.toJSONObject(detailDto);
            list.add(jsonObject);
        }
        //数据脱敏处理
        if (CollUtil.isNotEmpty(list)) {
            desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【导出】导出退库单据卡片")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderCard(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialTkOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrderCard(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_TK);
    }

    @ApiOperation(value = "【导出】导出退库单耗材")
    @PostMapping(value = "/exportOrder/materials")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrder(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialTkOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrder(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_TK);
    }

}
