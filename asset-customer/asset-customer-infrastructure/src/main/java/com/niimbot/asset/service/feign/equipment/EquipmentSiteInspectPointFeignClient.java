package com.niimbot.asset.service.feign.equipment;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.equipment.AuditableRemovePointResult;
import com.niimbot.equipment.EquipmentSiteInspectPointImportDto;
import com.niimbot.equipment.EquipmentSiteInspectPointQueryDto;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentSiteInspectPointFeignClient {


    /**
     * 分页查询巡检点位列表
     *
     * @param queryDto 参数
     * @return pageUtils对象
     */
    @PostMapping(value = "server/equipment/site/inspect/point/page")
    PageUtils<AsEquipmentSiteInspectPointDto> pagePoint(@RequestBody EquipmentSiteInspectPointQueryDto queryDto);


    @PostMapping(value = "server/equipment/site/inspect/point/list")
    List<AsEquipmentSiteInspectPointDto> listPoint(@RequestBody EquipmentSiteInspectPointQueryDto queryDto);

    @PostMapping(value = "server/equipment/site/inspect/point/ids")
    List<AsEquipmentSiteInspectPointDto> listIds(@RequestBody List<Long> ids);

    /**
     * 添加巡检点位
     *
     * @param dto
     * @return true/false
     */
    @PostMapping(value = "server/equipment/site/inspect/point")
    Long insertInspectPoint(@RequestBody AsEquipmentSiteInspectPointDto dto);

    /**
     * 修改巡检点位
     *
     * @param dto
     * @return true/false
     */

    @PutMapping(value = "server/equipment/site/inspect/point", produces = MediaType.APPLICATION_JSON_VALUE)
    boolean updateInspectPoint(@RequestBody AsEquipmentSiteInspectPointDto dto);

    /**
     * 删除巡检点位
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "server/equipment/site/inspect/point/{id}")
    AuditableRemovePointResult deleteInspectPointById(@PathVariable("id") Long id);

    /**
     * 获取推荐巡检点位编码
     *
     * @return 编码
     */
    @GetMapping(value = "server/equipment/site/inspect/point/recommendCode")
    String recommendCode();

    @GetMapping(value = "server/equipment/site/inspect/point/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @DeleteMapping(value = "server/equipment/site/inspect/point/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/equipment/site/inspect/point/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/equipment/site/inspect/point/saveSheetData")
    Boolean saveSheetData(@RequestBody EquipmentSiteInspectPointImportDto importDto);

    /**
     * 查询列表list集合
     *
     * @return 列表
     */
    @GetMapping(value = "server/equipment/site/inspect/point/list/all")
    List<AsEquipmentSiteInspectPointDto> selectPointListAll();

    @GetMapping(value = "server/equipment/site/inspect/point/detail")
    AsEquipmentSiteInspectPointDto pointDetail(@RequestParam("pointId") Long pointId);

}
