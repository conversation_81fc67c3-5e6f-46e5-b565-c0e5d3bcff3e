package com.niimbot.asset.service.feign;

import com.niimbot.system.AsNavigationDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/9 下午3:12
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface NavigationFeignClient {

    /**
     * 编辑导航栏
     * @param dto 对象
     * @return 结果
     */
    @PutMapping(value = "server/system/navigation")
    Boolean edit(AsNavigationDto dto);

    /**
     * 批量编辑排序
     * @param list 编辑列表
     * @return 结果
     */
    @PutMapping(value = "server/system/navigation/batch")
    Boolean editBatch(List<AsNavigationDto> list);

    /**
     * 批量编辑禁用/启用
     * @param list 编辑列表
     * @return 结果
     */
    @PutMapping(value = "server/system/navigation/enable")
    Boolean editEnable(List<AsNavigationDto> list);

    /**
     * 导航栏列表数据
     *
     * @param params 查询条件
     * @return 列表数据
     */
    @GetMapping(value = "server/system/navigation/list")
    List<AsNavigationDto> list(@RequestParam Map<String, Object> params);
}
