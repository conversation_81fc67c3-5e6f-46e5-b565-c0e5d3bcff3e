package com.niimbot.asset.controller.common.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.constant.AppActivateConstant;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.controller.BaseController;
import com.niimbot.asset.service.CusMenuService;
import com.niimbot.asset.service.feign.AppActivateFeignClient;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.CusMenuDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/10/27 17:33
 */
@Api(tags = {"菜单管理接口"})
@ResultController
@RequestMapping("api/common/cusMenu")
public class CusMenuController extends BaseController {

    //设备菜单
    private static final List<String> equipmentMenu = ListUtil.of(
            "devices-manage",
            "devices-list",
            "devices-task-list",
            "devices-plan-list",
            "execute-device-task",
            "device-task-view",
            "devices-plan-list-add",
            "equipment",
            "equipment-list",
            "maintenance-task",
            "maintenance-task-add");

    //耗材盘点
    private static final List<String> materialInventoryMenu = ListUtil.of(
            "material-inventory",
            "material-inventory-detail",
            "backlog-material-inventory",
            "material-inventory-order-create",
            "material-inventory-order-report",
            "material-inventory-order-delete",
            "material-inventory-order-export",
            "m-inventory",
            "material-inventory");

    private final CusMenuFeignClient cusMenuFeignClient;
    private final CusMenuService cusMenuService;
    private final AppActivateFeignClient appActivateFeignClient;

    @Autowired
    public CusMenuController(CusMenuFeignClient cusMenuFeignClient,
                             CusMenuService cusMenuService,
                             AppActivateFeignClient appActivateFeignClient) {
        this.cusMenuFeignClient = cusMenuFeignClient;
        this.cusMenuService = cusMenuService;
        this.appActivateFeignClient = appActivateFeignClient;
    }

    @ApiOperation(value = "查询当前配置角色菜单权所用树，按app,pc分开")
    @GetMapping("/tree/all/type")
    public Map<String, List<Tree<String>>> contractMenuTreeByType() {
        List<CusMenuDto> pcMenus = cusMenuFeignClient.configRoleMenuPcList();
        pcMenus = pcMenus.stream().filter(it -> !"org-synchro".equals(it.getMenuCode()) && !it.getIsPublic()).collect(Collectors.toList());
        pcMenus = filterMenu(pcMenus);
        List<CusMenuDto> appMenus = cusMenuFeignClient.configRoleMenuAppList();
        appMenus = appMenus.stream().filter(item -> !item.getIsPublic()).collect(Collectors.toList());
        appMenus = filterMenu(appMenus);
        List<Tree<String>> treePc = buildMenusTree(pcMenus);
        List<Tree<String>> treeApp = buildMenusTree(appMenus);
        return ImmutableMap.of(AssetConstant.TERMINAL_APP, treeApp,
                AssetConstant.TERMINAL_PC, treePc);
    }

    @ApiOperation(value = "查询当前用户菜单树")
    @GetMapping("/tree/user")
    public List<Tree<String>> userMenuTree() {
        List<CusMenuDto> menuList = cusMenuService.queryPermissionMenu();
        menuList = filterMenu(menuList);
        return buildMenusTree(menuList);
    }

    @ApiOperation(value = "查询当前用户按钮列表")
    @GetMapping("/tree/button")
    public List<String> userButtonList() {
        List<CusMenuDto> menus = cusMenuFeignClient.userMenuPcList();
        return menus.stream()
                .filter(m -> m.getMenuType().equalsIgnoreCase(DictConstant.MENU_TYPE_BUTTON))
                .map(CusMenuDto::getMenuCode)
                .collect(Collectors.toList());
    }

    private List<Tree<String>> buildMenusTree(List<CusMenuDto> menus) {
        // 构建树结构
        return TreeUtil.build(menus, "0", (menu, tree) -> {
            tree.setId(Convert.toStr(menu.getId()));
            tree.setParentId(Convert.toStr(menu.getPid()));
            tree.setName(menu.getMenuName());
            tree.setWeight(menu.getOrderNum());
            tree.putExtra("title", menu.getMenuName());
            tree.putExtra("value", Convert.toStr(menu.getId()));
            tree.putExtra("level", menu.getLevel());
            tree.putExtra("path", menu.getFeRoute());
            tree.putExtra("menuType", menu.getMenuType());
            tree.putExtra("isCache", menu.getIsCache());
            tree.putExtra("isShowChildren", menu.getIsShowChildren());
        });
    }

    /**
     * 过滤设备管理菜单
     * @param menuList
     * @return
     */
    private List<CusMenuDto> filterMenu(List<CusMenuDto> menuList) {
        if (CollUtil.isEmpty(menuList)) {
            return menuList;
        }

        //判断当前企业是否开通设备管理
        List<String> activeAppList = appActivateFeignClient.configStatus();
        //如果没有开通设备管理，需要过滤掉设备菜单
        if (!activeAppList.contains(AppActivateConstant.EQUIPMENT)) {
            menuList = menuList.stream().filter(item -> !equipmentMenu.contains(item.getMenuCode())).collect(Collectors.toList());
        }
        if (!activeAppList.contains(AppActivateConstant.MATERIAL_INVENTORY)) {
            menuList = menuList.stream().filter(item -> !materialInventoryMenu.contains(item.getMenuCode())).collect(Collectors.toList());
        }
        return menuList;
    }
}
