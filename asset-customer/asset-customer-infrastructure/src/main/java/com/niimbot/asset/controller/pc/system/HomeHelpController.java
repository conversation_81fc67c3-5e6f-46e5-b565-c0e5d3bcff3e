package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.service.CusMenuService;
import com.niimbot.asset.service.feign.HomeHelpFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.AssetHelpDto;
import com.niimbot.system.CusMenuDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 首页帮助服务
 * <AUTHOR>
 * @date 2023/9/8 下午2:46
 */
@Api(tags = "帮助和服务")
@ResultController
@RequestMapping("api/pc/help")
public class HomeHelpController {

    @Autowired
    private HomeHelpFeignClient helpFeignClient;
    @Autowired
    private CusMenuService cusMenuService;

    @ApiOperation("根据关键字搜索菜单或帮助文档")
    @GetMapping("/queryByTitle")
    public List<AssetHelpDto> queryByTitle(@RequestParam("title") String title) {
        //查询当前用户有权限的菜单树
        List<CusMenuDto> menuDtoList = cusMenuService.queryPermissionMenu();
        Set<CusMenuDto> menuList = new HashSet<>();
        if (CollUtil.isNotEmpty(menuDtoList) && StrUtil.isNotBlank(title)) {
            //找出名称包含搜索关键字的菜单
            List<CusMenuDto> correctMenuList = menuDtoList.stream().filter(item -> item.getMenuName().contains(title)
                    && (item.getMenuType().equalsIgnoreCase("C") || item.getMenuType().equalsIgnoreCase("M")))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(correctMenuList)) {
                //过滤出能直接点击跳转的页面
                List<CusMenuDto> correctCMenuList = correctMenuList.stream()
                        .filter(item -> item.getMenuType().equalsIgnoreCase("C"))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(correctCMenuList)) {
                    menuList.addAll(correctCMenuList);
                }

                //找出包含关键字的菜单
                List<Long> menuIdList = correctMenuList.stream()
                        .filter(item -> item.getMenuType().equalsIgnoreCase("M"))
                        .map(CusMenuDto::getId)
                        .collect(Collectors.toList());

                //需要找到满足条件的子菜单页面
                for (CusMenuDto item : menuDtoList) {
                    if (!item.getMenuType().equalsIgnoreCase("C")) {
                        continue;
                    }

                    List<Long> parentMenuIdList = Arrays.stream(item.getPaths().split(",")).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
                    //符合搜索条件的子页面也包含进去
                    if (!Collections.disjoint(parentMenuIdList, menuIdList)) {
                        menuList.add(item);
                    }
                }
            }
        } else if (CollUtil.isNotEmpty(menuDtoList)) {
            List<CusMenuDto> correctCMenuList = menuDtoList.stream()
                    .filter(item -> item.getMenuType().equalsIgnoreCase("C"))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(correctCMenuList)) {
                menuList.addAll(correctCMenuList);
            }
        }

        List<AssetHelpDto> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(menuList)) {
            result.addAll(menuList.stream().map(item -> {
                AssetHelpDto menuDto = new AssetHelpDto()
                        .setId(item.getId())
                        .setTitle(item.getMenuName())
                        .setPath(item.getFeRoute())
                        .setType(1);
                return menuDto;
            }).collect(Collectors.toList()));
        }

        //搜索帮助文档
        List<AssetHelpDto> helpDtoList = helpFeignClient.queryByTitle(title);
        if (CollUtil.isNotEmpty(helpDtoList)) {
            result.addAll(helpDtoList);
        }
        return result;
    }

    @ApiOperation("查询所有帮助文档")
    @GetMapping("/allDoc")
    public List<AssetHelpDto> allDoc() {
        return helpFeignClient.queryByTitle("");
    }
}
