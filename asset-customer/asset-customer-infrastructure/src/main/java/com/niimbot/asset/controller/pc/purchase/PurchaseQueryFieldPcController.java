package com.niimbot.asset.controller.pc.purchase;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.PurchaseQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * created by chen.y on 2022/9/5 14:49
 */
@Slf4j
@Validated
@Api(tags = "采购字段管理")
@ResultController
@RequestMapping("api/pc/queryField/purchase")
@RequiredArgsConstructor
public class PurchaseQueryFieldPcController {

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;
    private final PurchaseQueryFieldService purchaseQueryFieldService;

    @ApiOperation(value = "审批流条件分支-所有字段")
    @GetMapping("/query/field/activiti/{orderType}")
    public List<QueryConditionDto> activitiQueryField(@PathVariable("orderType") Integer orderType) {
        return purchaseQueryFieldService.activitiQueryField(orderType);
    }

    @ApiOperation(value = "【PC】筛选项配置-保存")
    @PostMapping("/query/field")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@RequestBody @Validated OrderQueryConditionGeneralDto condition) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(),
                QueryFieldConstant.PURCHASE_ORDER_TYPE_QUERY.get(condition.getOrderType())));
    }

    @ApiOperation(value = "【PC】筛选项配置-查询")
    @GetMapping("/query/field/{orderType}")
    public OrderQueryConditionGeneralDto getQueryField(@PathVariable("orderType") Integer orderType) {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(
                QueryFieldConstant.PURCHASE_ORDER_TYPE_QUERY.get(orderType));
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(OrderQueryConditionGeneralDto.class);
    }

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all/{orderType}")
    public List<QueryConditionDto> orderAllQueryField(@PathVariable("orderType") Integer orderType) {
        return purchaseQueryFieldService.orderQueryField(orderType);
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view/{orderType}")
    public List<QueryConditionDto> orderQueryView(@PathVariable("orderType") Integer orderType) {
        return purchaseQueryFieldService.orderQueryView(orderType);
    }

    @ApiOperation(value = "列表设置-保存")
    @PostMapping("/head/field")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean orderHeadField(@RequestBody @Validated OrderQueryHeadConfigDto config) {
        return purchaseQueryFieldService.orderHeadField(config);
    }

    @ApiOperation(value = "列表设置-查询")
    @GetMapping("/head/field/{orderType}")
    public QueryHeadConfigDto orderHeadField(@PathVariable("orderType") Integer orderType) {
        return purchaseQueryFieldService.orderHeadField(orderType);
    }

    @ApiOperation(value = "列表设置-所有字段")
    @GetMapping("/head/all/{orderType}")
    public List<QueryConditionDto> orderAllHeadField(@PathVariable("orderType") Integer orderType) {
        return purchaseQueryFieldService.orderAllHeadField(orderType);
    }

    @ApiOperation(value = "列表-渲染")
    @GetMapping("/head/view/{orderType}")
    public List<AssetHeadDto> orderHeadView(@PathVariable("orderType") Integer orderType) {
        return purchaseQueryFieldService.orderHeadView(orderType);
    }

    @ApiOperation(value = "排序字段")
    @GetMapping("/sortField/{orderType}")
    public QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType) {
        return purchaseQueryFieldService.sortField(orderType);
    }

}
