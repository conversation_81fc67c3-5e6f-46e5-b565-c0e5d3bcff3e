package com.niimbot.asset.service;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AccountDto;

import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Stream;

import cn.hutool.extra.spring.SpringUtil;

import static cn.hutool.core.util.StrUtil.isNotBlank;

/**
 * 验证码发送前置校验验证
 *
 * <AUTHOR>
 * @Date 2021/2/1
 */
public enum EmailCodeConsumerContext {

    /**
     * 【首页-忘记密码重置】
     */
    RESET_PASSWORD("resetPassword", email -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(email);
        // CusEmployeeDto byEmail = SpringUtil.getBean(CusEmployeeFeignClient.class).checkEmail(email);
        if (account == null) {
            throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        }
    }),
    /**
     * 【注册】
     */
    REGISTER("register", email -> {
        /*AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(email);
        if (account != null) {
            throw new BusinessException(SystemResultCode.USER_REGISTER_EMAIL_WITHOUT_ACCOUNT);
        }*/
        // SpringUtil.getBean(CusRegisterFeignClient.class).checkRegisterEmail(email);
    }
    ),
    /**
     * 【登录】
     */
    LOGIN("login", email -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(email);
        if (account == null) {
            throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        }
        // CusEmployeeDto employee = SpringUtil.getBean(CusEmployeeFeignClient.class).checkEmail(email);
        // if (employee == null) {
        //     throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        // }
        // CusUserDto byEmail = SpringUtil.getBean(CusUserFeignClient.class).getById(employee.getId());
        // // 账户信息不存在
        // if (byEmail == null) {
        //     throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        // } else if (byEmail.getStatus().shortValue() == DictConstant.SYS_DISABLE) {
        //     throw new BusinessException(SystemResultCode.USER_ACCOUNT_FORBIDDEN);
        // } else if (ObjectUtil.isNull(byEmail.getCompanyId())) {
        //     throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_REGISTER);
        // } /*else if (ObjectUtil.isNull(byEmail.getOrgId())) {
        //     throw new BusinessException(SystemResultCode.USER_HAS_NO_ROLE);
        // }*/
    }),
    /**
     * 【绑定邮箱】
     */
    BIND_MOBILE("bindEmail", email -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(email);
        if (account != null) {
            throw new BusinessException(SystemResultCode.USER_REGISTER_EMAIL_COMPLETE);
        }
        // CusEmployeeDto employee = SpringUtil.getBean(CusEmployeeFeignClient.class).checkEmail(email);
        // if (employee != null) {
        //     throw new BusinessException(SystemResultCode.USER_REGISTER_EMAIL_COMPLETE);
        // }
    }),
    /**
     * 【用户中心-更换邮箱、确认原邮箱】
     */
    CHANGE_MOBILE_OLD("changeEmailOld", email -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(email);
        // CusEmployeeDto employeeDto = SpringUtil.getBean(CusEmployeeFeignClient.class).checkEmail(email);
        if (account != null && !Objects.equals(email, account.getEmail())) {
            throw new BusinessException(SystemResultCode.USER_EMAIL_CHANGE_OLD);
        }
    }),
    /**
     * 【用户中心-更换邮箱、确认新邮箱】
     */
    CHANGE_MOBILE_NEW("changeEmailNew", email -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(email);
        // CusEmployeeDto employeeDto = SpringUtil.getBean(CusEmployeeFeignClient.class).checkEmail(email);
        if (account != null && isNotBlank(account.getEmail())) {
            throw new BusinessException(SystemResultCode.USER_REGISTER_EMAIL_COMPLETE);
        }
    });

    private final String type;
    private final Consumer<String> consumer;

    EmailCodeConsumerContext(String type, Consumer<String> consumer) {
        this.type = type;
        this.consumer = consumer;
    }

    public String getType() {
        return type;
    }

    public Consumer<String> getConsumer() {
        return consumer;
    }

    /**
     * 根据类型校验邮箱
     *
     * @param type  类型
     * @param email 邮箱
     */
    public static void checkEmail(String type, String email) {
        EmailCodeConsumerContext[] enums = EmailCodeConsumerContext.values();
        Stream.of(enums)
                .filter(context -> Objects.equals(context.getType(), type))
                .findFirst().ifPresent(context -> context.getConsumer().accept(email));
    }
}
