package com.niimbot.asset.service.impl;

import com.niimbot.framework.dataperm.core.model.InitSQL;
import com.niimbot.framework.dataperm.core.model.OrderInitSQL;
import com.niimbot.framework.dataperm.core.model.SQLScriptManager;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/8 16:52
 */
@Service
public class SQLScriptManagerImpl implements SQLScriptManager {
    @Override
    public InitSQL areaScript() {
        return null;
    }

    @Override
    public InitSQL cateScript() {
        return null;
    }

    @Override
    public InitSQL deptScript() {
        return null;
    }

    @Override
    public InitSQL storeScript() {
        return null;
    }

    @Override
    public InitSQL userScript() {
        return null;
    }

    @Override
    public OrderInitSQL orderScript() {
        return null;
    }
}
