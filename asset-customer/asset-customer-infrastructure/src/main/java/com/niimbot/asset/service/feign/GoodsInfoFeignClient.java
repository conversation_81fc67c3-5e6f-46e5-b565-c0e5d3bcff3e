package com.niimbot.asset.service.feign;

import com.niimbot.sale.GoodsInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/29 10:20
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface GoodsInfoFeignClient {

    @GetMapping(value = "server/sale/goods/sku/one")
    GoodsInfoDto getSkuOne(@RequestParam("type") Integer type);

    @GetMapping(value = "server/sale/goods/sku/list")
    List<GoodsInfoDto> getSkuList(@RequestParam("type") Integer type);

}
