package com.niimbot.asset.service.impl;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryConditionType;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.MaterialExcelTplEnum;
import com.niimbot.asset.service.AbstractMaterialService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialExcelService;
import com.niimbot.asset.service.feign.DataAuthorityFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.MaterialStockFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.ExcelExportUtil;
import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialCategoryDto;
import com.niimbot.material.MaterialDto;
import com.niimbot.material.MaterialQueryDto;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.material.MaterialStockDto;
import com.niimbot.material.MaterialStockImportDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.ReImportDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableImportResult;
import com.niimbot.system.DataAuthorityDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.ImportTaskExtInfoDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.QueryConditionDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/7/19 9:11
 */
@Slf4j
@Service
public class MaterialExcelServiceImpl extends AbstractMaterialService implements MaterialExcelService {

    private LinkedHashMap<String, String> tableHeader;

    {
        tableHeader = new LinkedHashMap<>();
        tableHeader.putAll(ImmutableMap.<String, String>builder()
                .put("耗材编码", "materialCode")
                .put("耗材名称", "materialName")
                .put("入库数量", "rkNum")
                .put("入库总价", "rkPrice")
                .build());
    }

    private static ThreadLocal<ImportInfo> globalCache = new TransmittableThreadLocal<>();
    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    private final RedisService redisService;
    private final ImportService importService;
    private final ImportTaskFeignClient importTaskFeignClient;

    private final DataAuthorityFeignClient dataAuthorityFeignClient;
    private final MaterialStockFeignClient materialStockFeignClient;

    public MaterialExcelServiceImpl(RedisService redisService,
                                    ImportService importService,
                                    ImportTaskFeignClient importTaskFeignClient,
                                    DataAuthorityFeignClient dataAuthorityFeignClient,
                                    MaterialStockFeignClient materialStockFeignClient) {
        this.redisService = redisService;
        this.importService = importService;
        this.importTaskFeignClient = importTaskFeignClient;
        this.dataAuthorityFeignClient = dataAuthorityFeignClient;
        this.materialStockFeignClient = materialStockFeignClient;
    }

    /**
     * 导出资产模板
     *
     * @return excel
     */
    @Override
    public ExcelWriter buildExcelWriter(Long standardId) {
        // 获取 writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 获取 sheet
        Sheet sheet = writer.getSheet();
        // 获取 styleSet
        StyleSet styleSet = writer.getStyleSet();
        // 设置边框
        styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
        styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);
        Workbook workbook = sheet.getWorkbook();
        DataFormat format = workbook.createDataFormat();
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(format.getFormat("@"));

        // 设置表头的cellStyle
        CellStyle redHeadStyle = writer.createCellStyle();
        redHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        redHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        redHeadStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        redHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        redHeadStyle.setBorderBottom(BorderStyle.THIN);
        redHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        redHeadStyle.setBorderTop(BorderStyle.THIN);
        redHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderLeft(BorderStyle.THIN);
        redHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderRight(BorderStyle.THIN);
        redHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font red = writer.createFont();
        red.setFontHeightInPoints((short) 13);
        red.setColor(IndexedColors.RED.getIndex());
        redHeadStyle.setFont(red);

        CellStyle blackHeadStyle = writer.createCellStyle();
        blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        blackHeadStyle.setBorderBottom(BorderStyle.THIN);
        blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setBorderTop(BorderStyle.THIN);
        blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderLeft(BorderStyle.THIN);
        blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderRight(BorderStyle.THIN);
        blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font black = writer.createFont();
        black.setFontHeightInPoints((short) 13);
        black.setColor(IndexedColors.BLACK.getIndex());
        blackHeadStyle.setFont(black);

        // 写入文件注意事项
        Row attentionRow = writer.getOrCreateRow(0);
        attentionRow.setHeight((short) 3000);
        Cell attentionCell = attentionRow.createCell(0);
        CellStyle attention = writer.createCellStyle();
        attention.setVerticalAlignment(VerticalAlignment.CENTER);
        attention.setWrapText(true);
        attention.setFont(black);
        attentionCell.setCellStyle(attention);
        String text = "注意事项:\n" +
                "1、红色字体为必填项，黑色字体为选填项\n" +
                "2、输入员工时，同名员工需填写员工工号，如：李白（001）；\n" +
                "3、耗材编码不可重复，当需要系统自动根据编码规则生成时，耗材编码请填写以下中文：系统自动生成；\n" +
                "4、 多选字段，多个字段以中文或英文逗号隔开；\n" +
                "5、单次最多可导入5000条数据，文件不可超过1MB；\n" +
                "6、具体字段填写要求可参考表头单元格批注信息。";
        XSSFRichTextString xssfRichTextString = new XSSFRichTextString(text);
        Font titleRed = writer.createFont();
        titleRed.setFontHeightInPoints((short) 13);
        titleRed.setColor(IndexedColors.RED.getIndex());
        xssfRichTextString.applyFont(0, 97, black);
        xssfRichTextString.applyFont(97, 103, titleRed);
        xssfRichTextString.applyFont(103, text.length() - 1, black);
        attentionCell.setCellValue(xssfRichTextString);
        writer.merge(26);

        // 查询录入标准数据项
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        // 查询录入的标准品扩展数据
        if (ObjectUtil.isNotNull(standardId)) {
            List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(standardExtField);
        }

        formFields = formFields.stream()
                .filter(attr -> !attr.isHidden())
                .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                        .contains(attr.getFieldType())).collect(Collectors.toList());
        Row headRow = writer.getOrCreateRow(1);
        int realCol = 0;
        for (FormFieldCO attr : formFields) {
            // ============================== 设置表头 ===================================
            // 写入表头
            Cell cell = headRow.createCell(realCol);
            cell.setCellStyle(attr.requiredProps() ? redHeadStyle : blackHeadStyle);
            cell.setCellValue(attr.getFieldName());

            String tplComment = MaterialExcelTplEnum.getTplComment(attr.getFieldCode());
            if (StrUtil.isNotEmpty(tplComment)) {
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6));
                comment.setString(new XSSFRichTextString(tplComment));
                cell.setCellComment(comment);
            }

            // 调整每一列宽度
            sheet.autoSizeColumn((short) realCol);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(realCol, sheet.getColumnWidth(realCol) * 17 / 10);
            sheet.setDefaultColumnStyle(realCol, style);

            if (FormFieldCO.SELECT_DROPDOWN.equals(attr.getFieldType())) {
                JSONArray values = attr.getFieldProps().getJSONArray("values");
                String[] selected = values.toArray(new String[]{});
                writer.addSelect(new CellRangeAddressList(2, 5000, realCol, realCol), selected);
            }
            realCol += 1;
        }
        // 写入其他sheet
        buildSheet(writer, formFields);
        return writer;
    }

    /**
     * 导入资产模板
     *
     * @param stream    文件流
     * @param fileName  文件名
     * @param fileSize
     * @param companyId 公司Id
     */
    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize,
                                 Long companyId, Long standardId) {
        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setFileName(fileName);
        importInfo.setFileSize(fileSize);
        importInfo.setCompanyId(companyId);
        importInfo.setStandardId(standardId);

        List<FormFieldCO> formFields = getStandardAttr(standardId);
        Map<String, List<FormFieldCO>> materialAttrMap = formFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 解析的模板导入
        ExcelReader reader = ExcelUtil.getReader(stream);
        List<List<Object>> read = reader.read(1);
        if (CollUtil.isEmpty(read)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表格未检测到数据");
        }
        // =============================  读取Excel表头并校验 ================================
        List<Object> header = ExcelUtils.clearEmpty(read.get(0));
        int noNullHeaderSize = Convert.toInt(header.stream().filter(it -> StrUtil.isNotBlank(Convert.toStr(it))).count());
        // 表头字段映射
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        // 匹配动态属性和表头数据是否一致，防止动态属性被修改
        if (formFields.size() != noNullHeaderSize) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
        } else {
            for (int i = 0; i < header.size(); i++) {
                String headName = Convert.toStr(header.get(i), "").trim();
                if (StrUtil.isBlank(Convert.toStr(headName))) {
                    continue;
                }
                if (!materialAttrMap.containsKey(headName)) {
                    throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
                }
                List<FormFieldCO> formFieldCOList = materialAttrMap.get(headName);
                if (CollUtil.isNotEmpty(formFieldCOList)) {
                    FormFieldCO formFieldCO = formFieldCOList.get(0);
                    headerMapping.put(i, formFieldCO);
                    formFieldCOList.remove(0);
                    materialAttrMap.put(headName, formFieldCOList);
                }
            }
        }
        importInfo.setHeaderMapping(headerMapping);
        importInfo.setRead(read);
        this.importExcel(importInfo, true);
    }

    private void importExcel(ImportInfo importInfo, boolean async) {
        // 判断是否超过最大上传条数，一次限制5000
        if (importInfo.getRead().size() > MAX_BATCH + 1) {
            throw new BusinessException(MaterialResultCode.IMPORT_MAX_LIMIT);
        }

        // 删除历史导入信息
        if (importInfo.getTaskId() != null) {
            materialFeignClient.importErrorDeleteAll(importInfo.getTaskId());
        }
        globalCache.set(importInfo);
        dropDownCache.set(new HashMap<>());
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto()
                        .setFileName(importInfo.getFileName())
                        .setImportType(DictConstant.IMPORT_TYPE_MATERIAL)
                        .setFileSize(importInfo.getFileSize())
                        .setCount(importInfo.getRead().size() - 1);
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(importCache);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto()
                    .setFileName(importInfo.getFileName())
                    .setImportType(DictConstant.IMPORT_TYPE_MATERIAL)
                    .setFileSize(importInfo.getFileSize())
                    .setCount(importInfo.getRead().size() - 1);
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(importCache);
        }
    }

    private void startImport(ImportDto importCache) {
        ImportInfo importInfo = globalCache.get();
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (importInfo.getTaskId() != null) {
            importTaskDto = importTaskFeignClient.queryById(importInfo.getTaskId());
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.MATERIAL, importInfo.getCompanyId()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                ImportTaskExtInfoDto extInfoDto = new ImportTaskExtInfoDto();
                extInfoDto.setStandardId(importInfo.getStandardId());
                importTaskDto.setExtInfo(extInfoDto);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
            }

            List<AssetImportDto> tableData = new ArrayList<>();
            // 构建资产JSON数据
            for (int idx = 1; idx < importInfo.getRead().size(); idx++) {
                // 行数据
                List<Object> dataRow = importInfo.getRead().get(idx);
                // 写入固定属性数据
                JSONObject materialData = new JSONObject();
                IntStream.range(0, dataRow.size()).forEach(nameIdx -> {
                    FormFieldCO materialConfig = importInfo.getHeaderMapping().get(nameIdx);
                    if (materialConfig != null) {
                        Object cell = dataRow.get(nameIdx);
                        materialData.put(materialConfig.getFieldCode(), cell);
                    }
                });
                // 创建属性对象
                AssetImportDto importDto = new AssetImportDto();
                importDto.setTaskId(importTaskDto.getId())
                        .setStandardId(importInfo.getStandardId())
                        .setErrorNum(0)
                        .setAssetData(materialData);
                tableData.add(importDto);
            }
            // 执行
            importService.sendMaterialMsg(importInfo.getCompanyId());
            this.executeTableData(importTaskDto.getId(), tableData, importInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL, importInfo.getCompanyId()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL, importInfo.getCompanyId()), "finish", true);
            importService.sendMaterialMsg(importInfo.getCompanyId());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.MATERIAL, importInfo.getCompanyId());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
            }
            this.clearThreadLocal();
        }
    }

    private void executeTableData(Long taskId, List<AssetImportDto> tableData, ImportInfo importInfo) {
        // 仓库权限
        DataAuthorityDto repoPerm = dataAuthorityFeignClient.getByUserAndDataCodeAndCode(LoginUserThreadLocal.getCurrentUserId(), "material", "store");
        // 加载缓存，有序集合
        LinkedHashMap<String, FormFieldCO> codeToMaterialAttrMap = new LinkedHashMap<>();
        Map<String, Map<String, List<IdNameCache>>> fieldCodeCache = dropDownCache.get();
        for (Map.Entry<Integer, FormFieldCO> entry : importInfo.getHeaderMapping().entrySet()) {
            FormFieldCO fieldCO = entry.getValue();
            loadSelectCache(fieldCO.getFieldType());
            codeToMaterialAttrMap.putIfAbsent(fieldCO.getFieldCode(), fieldCO);
        }

        // 写入表头数据
        this.saveLuckySheetHead(taskId, importInfo.getHeaderMapping());
        AtomicInteger successNum = new AtomicInteger(0);
        List<OrgDto> orgDtoList = orgFeignClient.storePermsList();
        // 循环处理行数据
        IntStream.range(0, tableData.size()).forEach(idx -> {
            // 获取导入数据
            AssetImportDto importDto = tableData.get(idx);
            // 数据
            JSONObject materialData = importDto.getAssetData();
            List<AssetImportDto.FieldData> fieldDataList = new ArrayList<>();
            codeToMaterialAttrMap.forEach((attrCode, fieldCO) -> {
                // 新增数据，直接新增，不参与事务。2022年7月19日
                boolean needSave = BooleanUtil.isTrue(fieldCO.getFieldProps().getBoolean("saveData"));

                AssetImportDto.FieldData fieldData = new AssetImportDto.FieldData();
                Object attrVal = materialData.getOrDefault(attrCode, null);
                fieldData.setSource(attrVal);
                if (attrVal instanceof String) {
                    fieldData.setTarget(StrUtil.trim(((String) attrVal)));
                } else {
                    fieldData.setTarget(attrVal);
                }
                fieldData.setFieldName(fieldCO.getFieldName());
                fieldData.setFieldCode(fieldCO.getFieldCode());

                // 处理日期类型
                if (FormFieldCO.DATETIME.equals(fieldCO.getFieldType())) {
                    dateConvert(fieldData, attrVal, fieldCO.getFieldProps().getString("dateFormatType"));
                }
                // 处理多选
                if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(fieldCO.getFieldType())) {
                    if (fieldData.getTarget() != null) {
                        String val = Convert.toStr(attrVal);
                        val = val.replace("，", ",");
                        String[] split = val.split(",");
                        fieldData.setTarget(new ArrayList<>(Arrays.asList(split)));
                    } else {
                        fieldData.setTarget(new ArrayList<>());
                    }
                }
                // 翻译业务组件数据
                String attrValStr = StrUtil.trim(Convert.toStr(attrVal));
                if (fieldCodeCache.containsKey(fieldCO.getFieldType()) && StrUtil.isNotBlank(attrValStr)) {
                    Map<String, List<IdNameCache>> idNameCacheMap = fieldCodeCache.getOrDefault(fieldCO.getFieldType(), new HashMap<>());
                    List<IdNameCache> idNameCaches;
                    if (idNameCacheMap.containsKey(attrValStr)) {
                        // 1.优先全匹配文本
                        idNameCaches = idNameCacheMap.get(attrValStr);
                    } else {
                        // 2.匹配括号内容是否大写字符加数字类型
                        idNameCaches = idNameCacheMap.get(ExcelUtils.matchCodeAndReplace(attrValStr));
                    }
                    if (CollUtil.isEmpty(idNameCaches)) {
                        if (needSave) {
                            // db插入数据
                            switch (fieldCO.getFieldType()) {
                                case FormFieldCO.YZC_MATERIAL_CATE:
                                    String cateCode = materialCategoryFeignClient.recommendCode();
                                    MaterialCategoryDto cateDto = new MaterialCategoryDto();
                                    cateDto.setCategoryCode(cateCode)
                                            .setCategoryName(attrValStr)
                                            .setPid(0L);
                                    if (attrValStr.length() > 50 || attrValStr.length() < 1) {
                                        fieldData.getErrMsg().add("分类名称请输入1-50位");
                                    } else {
                                        try {
                                            Long cateId = materialCategoryFeignClient.add(cateDto);
                                            cateDto.setId(cateId);
                                            idNameCacheMap.put(attrValStr,
                                                    ListUtil.of(new IdNameCache(Convert.toStr(cateDto.getId()),
                                                            cateDto.getCategoryName(), true)));
                                            fieldData.setTarget(Convert.toStr(cateDto.getId()));
                                        } catch (Exception e) {
                                            log.error("耗材分类自动新增异常, {}", e.getMessage(), e);
                                            fieldData.getErrMsg().add("耗材分类自动新增异常，请重试");
                                        }
                                    }
                                    break;
                                case FormFieldCO.YZC_REPOSITORY:
                                    if (CollUtil.isNotEmpty(orgDtoList)) {
                                        String repoCode = materialRepositoryFeignClient.recommendCode();
                                        MaterialRepositoryDto repositoryDto = new MaterialRepositoryDto();
                                        repositoryDto.setName(attrValStr)
                                                .setCode(repoCode)
                                                .setManagerOwner(orgDtoList.get(0).getId());
                                        if (attrValStr.length() > 20 || attrValStr.length() < 2) {
                                            fieldData.getErrMsg().add("仓库名称请输入2-20位");
                                        } else {
                                            try {
                                                Long repoId = materialRepositoryFeignClient.add(repositoryDto);
                                                repositoryDto.setId(repoId);
                                                idNameCacheMap.put(attrValStr,
                                                        ListUtil.of(new IdNameCache(Convert.toStr(repositoryDto.getId()),
                                                                repositoryDto.getName(),
                                                                repoPerm.getAuthorityType() == 0)));
                                                if (repoPerm.getAuthorityType() == 0) {
                                                    fieldData.setTarget(Convert.toStr(repositoryDto.getId()));
                                                } else {
                                                    fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                                                }
                                            } catch (Exception e) {
                                                log.error("仓库自动新增异常, {}", e.getMessage(), e);
                                                fieldData.getErrMsg().add("仓库自动新增异常，请重试");
                                            }
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        } else {
                            fieldData.getErrMsg().add("当前数据不存在，请先添加");
                        }
                    } else if (idNameCaches.size() > 1) {
                        switch (fieldCO.getFieldType()) {
                            case FormFieldCO.YZC_MATERIAL_CATE:
                                fieldData.getErrMsg().add("当前数据有重名，请输入耗材分类和耗材编码，示例：办公用品（A10）");
                                break;
                            case FormFieldCO.YZC_REPOSITORY:
                                fieldData.getErrMsg().add("当前数据有重名，请输入耗材仓库名称和耗材仓库编码，示例：仓库（A10）");
                                break;
                            default:
                                fieldData.getErrMsg().add("当前数据有重名");
                                break;
                        }
                    } else {
                        IdNameCache idName = idNameCaches.get(0);
                        if (idName.getHasPerm()) {
                            fieldData.setTarget(idName.getId());
                        } else {
                            fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                        }
                    }
                }
                fieldDataList.add(fieldData);
            });
            importDto.setFieldDataList(fieldDataList);
            importDto.setRowNum(idx + 1 - successNum.get());
            importDto.setFormFieldMap(formFieldMap.get());
            Boolean success = materialFeignClient.saveSheetData(importDto);
            if (BooleanUtil.isTrue(success)) {
                successNum.getAndIncrement();
            }
            importService.sendMaterialMsg(importInfo.getCompanyId());
        });
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MRL_ADD, new AuditableImportResult(successNum.get())));
    }

    private void saveLuckySheetHead(Long taskId, Map<Integer, FormFieldCO> headerMapping) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new LinkedList<>();

        List<Integer> headerList = new ArrayList<>(headerMapping.keySet());
        headerList = headerList.stream().sorted().collect(Collectors.toList());

        AtomicInteger cellIndex = new AtomicInteger(0);
        for (Integer index : headerList) {
            FormFieldCO config = headerMapping.get(index);
            // 记录当前属性
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(config.getFieldName());
            if (config.requiredProps()) {
                modelV.setFc("#ff0000");
            }
            headModelList.add(luckySheetModel);
        }
        importErrorDto.setHeadModelList(headModelList);
        this.materialFeignClient.saveSheetHead(importErrorDto);
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        return materialFeignClient.importError(taskId);
    }

    @Override
    public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (importTaskDto == null) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "导入任务" + taskId + "不存在");
        }
        if (CollUtil.isEmpty(sheetModels)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表格未检测到数据");
        }

        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setTaskId(taskId);
        importInfo.setFileName("耗材在线编辑保存");
        importInfo.setFileSize(0L);
        importInfo.setCompanyId(companyId);
        importInfo.setRead(sheetModels);


        ImportTaskExtInfoDto extInfo = importTaskDto.getExtInfo();
        Long standardId = null;
        if (extInfo != null && extInfo.getStandardId() != null) {
            standardId = extInfo.getStandardId();
        }
        importInfo.setStandardId(standardId);

        List<FormFieldCO> formFields = getStandardAttr(standardId);
        Map<String, List<FormFieldCO>> materialAttrMap = formFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 必填项code
        Set<String> requiredCodes = formFields.stream().filter(attr -> BooleanUtil.isTrue(attr.requiredProps()))
                .map(FormFieldCO::getFieldCode).collect(Collectors.toSet());

        // =============================  读取Excel表头并校验 ================================
        List<Object> header = sheetModels.get(0);
        // 表头字段映射
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        // 匹配动态属性和表头数据是否一致，防止动态属性被修改
        for (int i = 0; i < header.size(); i++) {
            String headName = Convert.toStr(header.get(i), "").trim();
            if (StrUtil.isBlank(Convert.toStr(headName))) {
                continue;
            }
            if (!materialAttrMap.containsKey(headName)) {
                throw new BusinessException(MeansResultCode.ASSET_IMPORT_TPL_CHANGE);
            }
            List<FormFieldCO> bizFormAssetConfigs = materialAttrMap.get(headName);
            if (CollUtil.isNotEmpty(bizFormAssetConfigs)) {
                FormFieldCO bizFormAssetConfig = bizFormAssetConfigs.get(0);
                headerMapping.put(i, bizFormAssetConfig);
                requiredCodes.remove(bizFormAssetConfig.getFieldCode());
                bizFormAssetConfigs.remove(0);
                materialAttrMap.put(headName, bizFormAssetConfigs);
            }
        }

        if (requiredCodes.size() > 0) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "存在必填项字段未设置");
        }

        importInfo.setHeaderMapping(headerMapping);
        // 校验表头数据
        this.importExcel(importInfo, false);
        return true;
    }

    @Override
    public Boolean importErrorDelete(Long taskId) {
        return materialFeignClient.importErrorDeleteAll(taskId);
    }

    @Override
    public List<JSONObject> getExcelData(MaterialQueryDto queryDto, Long standardId) {
        // 查询固定属性
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (standardId != null && standardId != -1L) {
            List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(standardExtField);
        }
        FormFieldCO createTime = new FormFieldCO();
        createTime.setFieldCode(QueryFieldConstant.FIELD_CREATE_TIME);
        createTime.setFieldName("创建时间");
        createTime.setFieldType(FormFieldCO.DATETIME);
        createTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(createTime);

        FormFieldCO updateTime = new FormFieldCO();
        updateTime.setFieldCode(QueryFieldConstant.FIELD_UPDATE_TIME);
        updateTime.setFieldName("更新时间");
        updateTime.setFieldType(FormFieldCO.DATETIME);
        updateTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(updateTime);
        List<JSONObject> materialDataList = materialFeignClient.materialPage(queryDto).getList()
                .stream().map(MaterialDto::translate).collect(Collectors.toList());
        materialUtil.translateMaterialJsonFormatBatch(materialDataList, formFields, true);
        //导出的时候价值字段为整数时拼接00如：(10.00)
        materialDataList.forEach(v -> {
            if (StringUtils.isNotEmpty(v.getString("price"))) {
                if (ExcelExportUtil.isNumeric(v.getString("price"))) {
                    v.put("price", v.getString("price") + ".00");
                }else {
                    v.put("price", v.getString("price") + " ");
                }
            }
        });
        return materialDataList;
    }

    @Override
    public List<JSONObject> getStockExcelData(MaterialStockQueryDto queryDto, Long standardId) {
        // 查询固定属性
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (standardId != null && standardId != -1L) {
            List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(standardExtField);
        }
        FormFieldCO createTime = new FormFieldCO();
        createTime.setFieldCode(QueryFieldConstant.FIELD_CREATE_TIME);
        createTime.setFieldName("创建时间");
        createTime.setFieldType(FormFieldCO.DATETIME);
        createTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(createTime);

        FormFieldCO updateTime = new FormFieldCO();
        updateTime.setFieldCode(QueryFieldConstant.FIELD_UPDATE_TIME);
        updateTime.setFieldName("更新时间");
        updateTime.setFieldType(FormFieldCO.DATETIME);
        updateTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(updateTime);

        List<JSONObject> stockList = materialStockFeignClient.page(queryDto).getList()
                .stream().map(MaterialStockDto::translate).collect(Collectors.toList());
        materialUtil.translateMaterialJsonFormatBatch(stockList, formFields, true);
        //导出的时候价值字段为整数时拼接00如：(10.00)
        stockList.forEach(v -> {
            if (StringUtils.isNotEmpty(v.getString("price"))) {
                if (ExcelExportUtil.isNumeric(v.getString("price"))) {
                    v.put("price", v.getString("price") + ".00");
                }else {
                    v.put("price", v.getString("price") + " ");
                }
            }
        });
        return stockList;
    }

    @Override
    public ReImportDto reImport(Long taskId) {
        ReImportDto reImportDto = new ReImportDto();
        List<List<LuckySheetModel>> lists = materialFeignClient.importError(taskId);
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (CollUtil.isNotEmpty(lists) && ObjectUtil.isNotNull(importTaskDto)) {
            reImportDto.setExcel(lists);
            if (ObjectUtil.isNotNull(importTaskDto.getExtInfo())) {
                ImportTaskExtInfoDto extInfo = importTaskDto.getExtInfo();
                if (extInfo.getStandardId() != null) {
                    FormVO standardVO = formFeignClient.getByFormId(new FormByIdQry(importTaskDto.getExtInfo().getStandardId(),
                            ListUtil.of(1L, LoginUserThreadLocal.getCompanyId())));
                    if (standardVO != null) {
                        reImportDto.setStandardId(importTaskDto.getExtInfo().getStandardId());
                        reImportDto.setStandardName(standardVO.getFormName());
                    }
                }
            }
        }
        return reImportDto;
    }

    @Override
    public List<MaterialStockImportDto> parseStockStream(InputStream stream) {
        ExcelReader reader = ExcelUtil.getReader(stream);
        reader.setHeaderAlias(tableHeader);
        // 校验表头
        List<List<Object>> read = reader.read(1, 1);
        if (read.size() > 0) {
            List<Object> header = ExcelUtils.clearEmpty(read.get(0));
            if (header.size() != tableHeader.size()) {
                throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
            }
            header.forEach(it -> {
                if (!tableHeader.containsValue(Convert.toStr(it))) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                }
            });
        } else {
            throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
        }
        // 读取数据并校验
        List<MaterialStockImportDto> stockList = reader.read(1, 1, MaterialStockImportDto.class);
        if (stockList.size() > 100) {
            throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "耗材入库数量和入库总价", Convert.toStr(100));
        }
        List<MaterialStockImportDto> allImportList = new ArrayList<>();
        // 静态解析
        stockList.forEach(stock -> {
            Set<ConstraintViolation<MaterialStockImportDto>> validate = VALIDATOR.validate(stock);
            if (validate.size() > 0) {
                List<String> errorMsg = validate.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
                stock.setErrorMsg(errorMsg);
                allImportList.add(stock);
            } else {
                allImportList.add(stock);
            }
        });

        Set<String> materialCodeCache = new HashSet<>();
        List<String> allImportCodes = new ArrayList<>();
        for (MaterialStockImportDto materialStockImportDto : allImportList) {
            String materialCode = materialStockImportDto.getMaterialCode();
            if (materialCode != null) {
                allImportCodes.add(StrUtil.trim(materialCode));
                materialStockImportDto.setMaterialCode(StrUtil.trim(materialCode));
            } else {
                allImportCodes.add(null);
            }
        }
        // 动态解析
        MaterialStockQueryDto queryDto = new MaterialStockQueryDto();
        QueryConditionDto codeQry = new QueryConditionDto();
        codeQry.setType(AssetConstant.ED_YZC_MATERIAL_SERIALNO)
                .setName("耗材编码")
                .setCode(MATERIAL_CODE)
                .setQuery(QueryConditionType.IN.getCode())
                .setQueryData(allImportCodes);
        queryDto.setConditions(ListUtil.of(codeQry));
        queryDto.setPageSize(Integer.MAX_VALUE);
        PageUtils<MaterialStockDto> page = materialStockFeignClient.page(queryDto);
        List<MaterialStockDto> stockDtoList = page.getList();
        Map<String, MaterialStockDto> inDbMap = stockDtoList.stream().collect(Collectors
                .toMap(f -> StrUtil.trim(f.getMaterialData().getString(MATERIAL_CODE)), k -> k, (k1, k2) -> k1));
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        IntStream.range(0, allImportCodes.size()).forEach(idx -> {
            MaterialStockImportDto importDto = allImportList.get(idx);
            // 判断编码重复
            if (materialCodeCache.contains(StrUtil.trim(importDto.getMaterialCode()))) {
                importDto.getErrorMsg().add("耗材编码重复");
            }
            // 判断入库数量
            BigDecimal rkNum = Convert.toBigDecimal(importDto.getRkNum());
            if (rkNum != null) {
                if (rkNum.compareTo(new BigDecimal("0.01")) < 0 || rkNum.compareTo(new BigDecimal("999999.99")) > 0) {
                    importDto.getErrorMsg().add("入库数量输入不合规，仅支持0.01-999999.99的整数");
                } else {
                    // 判断本次入库总价
                    BigDecimal rkPrice = Convert.toBigDecimal(importDto.getRkPrice());
                    if (rkPrice != null) {
                        if (rkPrice.scale() > 4) {
                            importDto.getErrorMsg().add("入库总价小数不合规，允许输入0-999999999999.9999的数字");
                        }
                        if (rkPrice.compareTo(BigDecimal.ZERO) < 0 || rkPrice.compareTo(new BigDecimal("999999999999.9999")) > 0) {
                            importDto.getErrorMsg().add("入库总价不合规，允许输入0-999999999999.9999的数字");
                        }
                        // 判断单价是否会变成0
                        BigDecimal rkUnitPrice = rkPrice.divide(new BigDecimal(rkNum + ""), 4, RoundingMode.HALF_UP);
                        if (BigDecimal.ZERO.compareTo(rkPrice) != 0 && BigDecimal.ZERO.compareTo(rkUnitPrice) == 0) {
                            importDto.getErrorMsg().add("入库单价计算后为0，请调整数量和总价");
                        } else if (rkUnitPrice.compareTo(new BigDecimal("999999999999.9999")) > 0) {
                            importDto.getErrorMsg().add("入库单价大于999999999999.9999，请调整数量和总价");
                        }
                    } else {
                        importDto.getErrorMsg().add("入库总价不合规，允许输入0-999999999999.9999的数字");
                    }
                }
            } else {
                importDto.getErrorMsg().add("入库数量输入不合规，仅支持0.01-999999.99的整数");
            }

            if (!inDbMap.containsKey(importDto.getMaterialCode())) {
                if (StrUtil.isNotEmpty(importDto.getMaterialCode())) {
                    importDto.getErrorMsg().add("耗材编码不存在");
                }
            } else {
                if (CollUtil.isEmpty(importDto.getErrorMsg())) {
                    MaterialStockDto materialStockDto = inDbMap.get(importDto.getMaterialCode());
                    JSONObject json = materialStockDto.translate();
                    materialUtil.translateMaterialJson(json, material.getFormFields());
                    importDto.setMaterial(json);
                }
            }
            if (StrUtil.isNotEmpty(importDto.getMaterialCode())) {
                materialCodeCache.add(importDto.getMaterialCode());
            }
        });
        return allImportList;
    }

    /**
     * 清理本地缓存
     */
    private void clearThreadLocal() {
        dropDownCache.remove();
        globalCache.remove();
        formFieldMap.remove();
    }

}
