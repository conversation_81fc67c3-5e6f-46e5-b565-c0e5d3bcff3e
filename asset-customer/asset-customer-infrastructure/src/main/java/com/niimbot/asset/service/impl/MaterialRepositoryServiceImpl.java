package com.niimbot.asset.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.annotation.ExcelField;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialRepositoryService;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.MaterialRepositoryFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialRepositoryImportDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableImportResult;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.OrgDto;

import org.apache.catalina.manager.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/9/27 18:29
 */
@Slf4j
@Service
@Primary
public class MaterialRepositoryServiceImpl implements MaterialRepositoryService {

    private LinkedHashMap<String, String> tableHeader;

    {
        tableHeader = new LinkedHashMap<>();
        tableHeader.putAll(ImmutableMap.<String, String>builder()
                .put("所属公司", "managerOwnerText")
                .put("仓库编码", "code")
                .put("仓库名称", "name")
                .put("管理员", "adminNames")
                .put("备注信息", "remark")
                .build());
    }

    protected static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    protected static ThreadLocal<GlobalCache> globalCache = new TransmittableThreadLocal<>();

    private final RedisService redisService;

    private final MaterialRepositoryFeignClient repositoryFeignClient;

    private final OrgFeignClient orgFeignClient;
    private final CusEmployeeFeignClient employeeFeignClient;

    private final ImportService importService;

    private final ImportTaskFeignClient importTaskFeignClient;

    @Autowired
    public MaterialRepositoryServiceImpl(RedisService redisService,
                                         MaterialRepositoryFeignClient repositoryFeignClient,
                                         OrgFeignClient orgFeignClient,
                                         CusEmployeeFeignClient employeeFeignClient,
                                         ImportService importService,
                                         ImportTaskFeignClient importTaskFeignClient) {
        this.redisService = redisService;
        this.repositoryFeignClient = repositoryFeignClient;
        this.orgFeignClient = orgFeignClient;
        this.employeeFeignClient = employeeFeignClient;
        this.importService = importService;
        this.importTaskFeignClient = importTaskFeignClient;
    }

    @Data
    @AllArgsConstructor
    public static class OrgExport {
        @ExcelField(header = "公司编码", ordinal = 1)
        public String orgCode;
        @ExcelField(header = "公司名称", ordinal = 2)
        public String orgName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(2);
            map.put("公司编码", "orgCode");
            map.put("公司名称", "orgName");
            return map;
        }
    }

    @Data
    @AllArgsConstructor
    public static class EmpExport {
        @ExcelField(header = "员工编码", ordinal = 1)
        public String empCode;
        @ExcelField(header = "员工名称", ordinal = 2)
        public String empName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(2);
            map.put("员工编码", "empCode");
            map.put("员工名称", "empName");
            return map;
        }
    }

    @Override
    public ExcelWriter buildWriter() {
        ClassPathResource templateSource = new ClassPathResource("/excelTemplate/material_repository_template.xlsx");
        try (InputStream inputStream = templateSource.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            writer.setSheet("所属公司");
            List<OrgDto> orgAll = orgFeignClient.areaPermsList();
            LinkedHashMap<String, String> orgHead = ExcelUtils.buildExcelHead(OrgExport.class);
            orgHead.forEach(writer::addHeaderAlias);
            List<OrgExport> orgData = orgAll.stream().map(org ->
                            new OrgExport(org.getOrgCode(), org.getOrgName()))
                    .collect(Collectors.toList());
            writer.setOnlyAlias(true);
            writer.write(orgData);
            writer.autoSizeColumnAll();
            // 企业微信还要写入员工Sheet
            Edition.weixin(() -> {
                writer.setSheet("员工");
                List<CusEmployeeDto> empAll = employeeFeignClient.actList(new CusEmployeeQueryDto().setFilterPerm(true));
                LinkedHashMap<String, String> empHead = ExcelUtils.buildExcelHead(AssetExcelServiceImpl.EmpExport.class);
                empHead.forEach(writer::addHeaderAlias);
                List<EmpExport> empData = empAll.stream().map(v -> new EmpExport(v.getEmpNo(), v.getEmpName())).collect(Collectors.toList());
                writer.setOnlyAlias(true);
                writer.write(empData);
                writer.autoSizeColumnAll();
            });
            return writer;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ExcelWriter writer = buildWriter();
        try (OutputStream out = response.getOutputStream()) {
            String fileName = "耗材仓库导入模板.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
        ExcelReader reader = ExcelUtil.getReader(stream);
        reader.setHeaderAlias(tableHeader);
        // 校验表头
        List<List<Object>> read = reader.read(1, 1);
        if (read.size() > 0) {
            List<Object> header = ExcelUtils.clearEmpty(read.get(0));
            if (header.size() != tableHeader.size()) {
                throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
            }
            header.forEach(it -> {
                if (!tableHeader.containsValue(Convert.toStr(it))) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                }
            });
        } else {
            throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
        }
        // 读取数据并校验
        List<MaterialRepositoryImportDto> repositoryList = reader.read(1, 1, MaterialRepositoryImportDto.class);
        List<OrgExport> orgExports = new ArrayList<>();
        List<EmpExport> empExports = new ArrayList<>();
        Edition.weixin(() -> {
            List<OrgExport> readOrg = reader.setSheet("所属公司").setHeaderAlias(OrgExport.headerMap()).read(0, 1, OrgExport.class);
            orgExports.addAll(readOrg);
            readOrg.forEach(v -> {
                OrgExport copy = BeanUtil.copyProperties(v, OrgExport.class);
                copy.setOrgName(v.getOrgName() + "（" + v.getOrgCode() + "）");
                orgExports.add(copy);
            });
        });
        Edition.weixin(() -> {
            List<EmpExport> readEmp = reader.setSheet("员工").setHeaderAlias(EmpExport.headerMap()).read(0, 1, EmpExport.class);
            // name && name（）
            empExports.addAll(readEmp);
            readEmp.forEach(v -> {
                EmpExport copy = BeanUtil.copyProperties(v, EmpExport.class);
                copy.setEmpName(copy.getEmpName() + "（" + copy.getEmpCode() + "）");
                empExports.add(copy);
            });
        });
        this.importExcel(null, repositoryList, fileName, fileSize, companyId, true, orgExports, empExports);
    }

    private void importExcel(Long taskId,
                             List<MaterialRepositoryImportDto> read,
                             String fileName,
                             Long fileSize,
                             Long companyId,
                             boolean async,
                             List<OrgExport> orgExports,
                             List<EmpExport> empExports) {
        // 判断是否超过最大上传条数，一次限制1000
        if (read.size() > MAX_BATCH) {
            throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "耗材仓库", Convert.toStr(MAX_BATCH));
        }
        // 删除历史导入信息
        if (taskId != null) {
            repositoryFeignClient.importErrorDeleteAll(taskId);
        }
        GlobalCache global = new GlobalCache().setCompany(companyId);
        globalCache.set(global);
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto();
                importCache.setFileName(fileName);
                importCache.setImportType(DictConstant.IMPORT_TYPE_MATERIAL_REPOSITORY);
                importCache.setFileSize(fileSize);
                importCache.setCount(read.size());
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(taskId, read, importCache, orgExports, empExports);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto();
            importCache.setFileName(fileName);
            importCache.setImportType(DictConstant.IMPORT_TYPE_MATERIAL_REPOSITORY);
            importCache.setFileSize(fileSize);
            importCache.setCount(read.size());
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(taskId, read, importCache, orgExports, empExports);
        }
    }

    private void startImport(Long taskId, List<MaterialRepositoryImportDto> read, ImportDto importCache, List<OrgExport> orgExports, List<EmpExport> empExports) {
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (taskId != null) {
            importTaskDto = importTaskFeignClient.queryById(taskId);
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
                Edition.weixin(() -> redisService.set("weixin:import:mal:add:org_id_trans:" + id, JSONObject.toJSONString(orgExports), 3, TimeUnit.DAYS));
                Edition.weixin(() -> redisService.set("weixin:import:mal:add:emp_id_trans:" + id, JSONObject.toJSONString(empExports), 3, TimeUnit.DAYS));
            }

            // 写入表头数据
            this.saveLuckySheetHead(importTaskDto.getId());
            // 记录一行错误信息，生成LuckySheet格式数据
            AtomicInteger successNum = new AtomicInteger(0);
            importService.sendMaterialRepository(globalCache.get().getCompany());
            List<OrgDto> orgs = orgFeignClient.areaPermsList();
            Map<String, List<Long>> orgMap = new HashMap<>();
            orgs.forEach(o -> {
                String orgName = o.getOrgName();
                List<Long> orgIds = orgMap.getOrDefault(orgName, new ArrayList<>());
                orgIds.add(o.getId());
                orgMap.put(orgName, orgIds);

                String orgNameAndCode = o.getOrgName() + "（" + o.getOrgCode() + "）";
                List<Long> orgCodeIds = orgMap.getOrDefault(orgNameAndCode, new ArrayList<>());
                orgCodeIds.add(o.getId());
                orgMap.put(orgNameAndCode, orgCodeIds);
            });

            Edition.weixin(() -> {
                orgMap.clear();
                Map<String, Long> codeIdMap = orgs.stream().collect(Collectors.toMap(OrgDto::getOrgCode, OrgDto::getId));
                orgExports.forEach(v -> {
                    if (!codeIdMap.containsKey(v.getOrgCode())) {
                        return;
                    }
                    List<Long> ids = orgMap.getOrDefault(v.getOrgName(), new ArrayList<>());
                    ids.add(codeIdMap.get(v.getOrgCode()));
                    orgMap.put(v.getOrgName(), ids);
                });
            });

            List<CusEmployeeDto> empAll = employeeFeignClient.actList(new CusEmployeeQueryDto().setStatus(1).setFilterPerm(false));
            Map<String, List<Long>> empMap = new HashMap<>();
            empAll.forEach(e -> {
                String empName = e.getEmpName();
                List<Long> empIds = empMap.getOrDefault(empName, new ArrayList<>());
                empIds.add(e.getId());
                empMap.put(empName, empIds);

                String empNameAndCode = e.getEmpName() + "（" + e.getEmpNo() + "）";
                List<Long> empCodeIds = empMap.getOrDefault(empNameAndCode, new ArrayList<>());
                empCodeIds.add(e.getId());
                empMap.put(empNameAndCode, empCodeIds);
            });

            Edition.weixin(() -> {
                empMap.clear();
                Map<String, Long> codeIdMap = empAll.stream().collect(Collectors.toMap(CusEmployeeDto::getEmpNo, CusEmployeeDto::getId));
                empExports.forEach(v -> {
                    if (!codeIdMap.containsKey(v.getEmpCode())) {
                        return;
                    }
                    List<Long> ids = empMap.getOrDefault(v.getEmpName(), new ArrayList<>());
                    ids.add(codeIdMap.get(v.getEmpCode()));
                    empMap.put(v.getEmpName(), ids);
                });
            });

            // 循环处理行数据
            ImportTaskDto finalImportTaskDto = importTaskDto;
            IntStream.range(0, read.size()).forEach(idx -> {
                MaterialRepositoryImportDto importDto = read.get(idx);
                importDto.setTaskId(finalImportTaskDto.getId());
                importDto.setErrorNum(0);
                List<LuckySheetModel> luckySheetModelRow = new ArrayList<>();
                // 调用validator
                Set<ConstraintViolation<MaterialRepositoryImportDto>> validate = VALIDATOR.validate(importDto);
                Map<String, String> validateData = validate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));

                // 所属公司
                LuckySheetModel managerOwner = validateTransField(idx + 1 - successNum.get(), 0, importDto.getManagerOwnerText(), importDto, "managerOwnerText", validateData, orgMap, null);
                luckySheetModelRow.add(managerOwner);

                // 仓库编码
                LuckySheetModel code = validateField(idx + 1 - successNum.get(), 1, importDto.getCode(), importDto, "code", validateData);
                luckySheetModelRow.add(code);

                // 仓库名称
                LuckySheetModel name = validateField(idx + 1 - successNum.get(), 2, importDto.getName(), importDto, "name", validateData);
                luckySheetModelRow.add(name);

                // 所属管理员
                LuckySheetModel admins = validateTransField(idx + 1 - successNum.get(), 3, importDto.getAdminNames(), importDto, "admins", validateData, null, empMap);
                luckySheetModelRow.add(admins);

                // 备注信息
                LuckySheetModel remark = validateField(idx + 1 - successNum.get(), 4, importDto.getRemark(), importDto, "remark", validateData);
                luckySheetModelRow.add(remark);

                importDto.setSheetModelList(luckySheetModelRow);

                Boolean success = repositoryFeignClient.saveSheetData(importDto);
                if (BooleanUtil.isTrue(success)) {
                    successNum.getAndIncrement();
                }
                importService.sendMaterialRepository(globalCache.get().getCompany());
            });
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MRL_REP, new AuditableImportResult(successNum.get())));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany()), "finish", true);
            importService.sendMaterialRepository(globalCache.get().getCompany());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, globalCache.get().getCompany());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
                if (Edition.isWeixin() && importTaskDto.getTaskStatus() == DictConstant.IMPORT_STATUS_SUCC) {
                    redisService.del("weixin:import:mal:add:org_id_trans:" + importTaskDto.getId());
                    redisService.del("weixin:import:mal:add:emp_id_trans:" + importTaskDto.getId());
                }
            }
            this.clearThreadLocal();
        }
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        return repositoryFeignClient.importError(taskId);
    }

    @Override
    public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        List<Object> header = sheetModels.get(0);
        // 去除表头尾部空列
        int lastIndex = header.size();
        for (int i = header.size() - 1; i >= 0; i--) {
            if (StrUtil.isBlank(Convert.toStr(header.get(i)))) {
                lastIndex = i;
            } else {
                break;
            }
        }
        header = header.subList(0, lastIndex);
        List<MaterialRepositoryImportDto> repositoryList = new ArrayList<>();
        for (int i = 1; i < sheetModels.size(); i++) {
            List<Object> data = sheetModels.get(i);
            MaterialRepositoryImportDto materialRepositoryImportDto = new MaterialRepositoryImportDto();
            for (int j = 0; j < header.size(); j++) {
                String head = tableHeader.get(Convert.toStr(header.get(j)));
                if (StrUtil.isBlank(head)) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                } else {
                    if (j < data.size()) {
                        ReflectUtil.setFieldValue(materialRepositoryImportDto, head, data.get(j));
                    }
                }
            }
            repositoryList.add(materialRepositoryImportDto);
        }
        List<OrgExport> orgExports = new ArrayList<>();
        List<EmpExport> empExports = new ArrayList<>();
        Edition.weixin(() -> {
            Object orgCache = redisService.get("weixin:import:mal:add:org_id_trans:" + taskId);
            Object empCache = redisService.get("weixin:import:mal:add:emp_id_trans:" + taskId);
            if (Objects.isNull(orgCache) || Objects.isNull(empCache)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "文件已过期，请重新导入");
            }
            orgExports.addAll(JSONArray.parseArray(orgCache.toString(), OrgExport.class));
            empExports.addAll(JSONObject.parseArray(empCache.toString(), EmpExport.class));
        });
        this.importExcel(taskId, repositoryList, "耗材分类在线编辑保存", 0L, companyId, false, orgExports, empExports);
        return true;
    }

    @Override
    public Boolean importErrorDelete(Long taskId) {
        return repositoryFeignClient.importErrorDeleteAll(taskId);
    }

    private void saveLuckySheetHead(Long taskId) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new ArrayList<>();
        AtomicInteger cellIndex = new AtomicInteger(0);
        List<String> must = ListUtil.of("managerOwnerText", "code", "name");
        tableHeader.forEach((k, v) -> {
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(k);
            if (must.contains(v)) {
                modelV.setFc("#ff0000");
            }
            headModelList.add(luckySheetModel);
        });
        importErrorDto.setHeadModelList(headModelList);
        this.repositoryFeignClient.saveSheetHead(importErrorDto);

    }

    protected LuckySheetModel validateField(int r, int c, String data, MaterialRepositoryImportDto importDto, String errorCode, Map<String, String> error) {
        return validateTransField(r, c, data, importDto, errorCode, error, null, null);
    }

    protected LuckySheetModel validateTransField(int r, int c, String data, MaterialRepositoryImportDto importDto,
                                                 String errorCode,
                                                 Map<String, String> error,
                                                 Map<String, List<Long>> orgMap,
                                                 Map<String, List<Long>> empMap) {
        String trim = StrUtil.trim(data);
        LuckySheetModel model = new LuckySheetModel();
        LuckySheetModel.Value modelV = model.getV();
        model.setR(r).setC(c);
        modelV.setV(trim);
        if (error.containsKey(errorCode)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(error.get(errorCode));
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
        if (trim != null) {
            if (CollUtil.isNotEmpty(orgMap)) {
                checkOrg(trim, orgMap, importDto, model);
            }
            if (StrUtil.isNotEmpty(trim) && CollUtil.isNotEmpty(empMap)) {
                trim = trim.replace(",", "，");
                String[] empArrays = trim.split("，");
                checkEmp(empArrays, empMap, importDto, model);
            }
        }
        return model;
    }

    private void checkOrg(String orgName, Map<String, List<Long>> orgMap, MaterialRepositoryImportDto importDto, LuckySheetModel model) {
        String errMsg = null;
        List<Long> ids;
        if (orgMap.containsKey(orgName)) {
            // 1.优先全匹配文本
            ids = orgMap.get(orgName);
        } else {
            // 2.匹配括号内容是否大写字符加数字类型
            ids = orgMap.get(ExcelUtils.matchCodeAndReplace(orgName));
        }
        if (CollUtil.isEmpty(ids)) {
            errMsg = "当前数据不存在，请先添加";
        } else if (ids.size() > 1) {
            errMsg = "当前数据有重名，请输入组织名称和组织编码，示例：销售部（001）";
        } else {
            importDto.setManagerOwner(ids.get(0));
        }
        if (StrUtil.isNotBlank(errMsg)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(errMsg);
            LuckySheetModel.Value modelV = model.getV();
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
    }

    private void checkEmp(String[] empArrays, Map<String, List<Long>> empMap, MaterialRepositoryImportDto importDto, LuckySheetModel model) {
        List<String> errMsg = new ArrayList<>();
        if (empArrays.length > 5) {
            errMsg.add("最多可设置5个管理员");
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(String.join("，\n", errMsg));
            LuckySheetModel.Value modelV = model.getV();
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        } else {
            for (String empName : empArrays) {
                empName = StrUtil.trim(empName);
                List<Long> ids;
                if (empMap.containsKey(empName)) {
                    // 1.优先全匹配文本
                    ids = empMap.get(empName);
                } else {
                    // 2.匹配括号内容是否大写字符加数字类型
                    ids = empMap.get(ExcelUtils.matchCodeAndReplace(empName));
                }

                if (CollUtil.isEmpty(ids)) {
                    errMsg.add(empName + "当前数据不存在，请先添加");
                } else if (ids.size() > 1) {
                    errMsg.add(empName + "有重名，请输入员工姓名和员工工号，示例：李白（001）");
                } else {
                    List<Long> admins = importDto.getAdmins();
                    if (admins == null) {
                        admins = new ArrayList<>();
                    }
                    admins.add(ids.get(0));
                    importDto.setAdmins(admins);
                }
            }
            if (CollUtil.isNotEmpty(errMsg)) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment(String.join("，\n", errMsg));
                LuckySheetModel.Value modelV = model.getV();
                modelV.setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }
    }

    /**
     * 清理本地缓存
     */
    protected void clearThreadLocal() {
        globalCache.remove();
    }

    @Data
    @Accessors(chain = true)
    protected static class GlobalCache {
        private Long company;
    }
}
