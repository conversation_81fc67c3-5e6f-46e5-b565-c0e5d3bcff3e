package com.niimbot.asset.controller.app.means;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetRelationService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.means.AssetRelationAppDto;
import com.niimbot.means.AssetRelationQueryConditionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2023/8/11 上午9:39
 */
@Slf4j
@Api(tags = "关联资产-app接口")
@ResultController
@RequestMapping("api/app/assetRelation/")
public class AssetRelationAppController {
    @Autowired
    private AssetRelationService assetRelationService;

    @ApiOperation("资产关联关系分页查询")
    @PostMapping("page")
    public PageUtils<AssetRelationAppDto> page(@RequestBody AssetQueryConditionDto queryDto) {
        queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        return assetRelationService.assetRelationAppPage(queryDto);
    }

    @ApiOperation("子资产分页查询")
    @PostMapping("subAssetPage")
    public PageUtils<AssetRelationAppDto> subAssetPage(@RequestBody AssetRelationQueryConditionDto queryDto) {
        queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        return assetRelationService.subAssetAppPage(queryDto);
    }

    @ApiOperation("子资产查询主资产信息")
    @GetMapping("getBySubAssetId/{subAssetId}")
    public AssetRelationAppDto getBySubAssetId(@PathVariable("subAssetId") Long subAssetId) {
        return assetRelationService.queryBySubAsset(subAssetId);
    }
}
