package com.niimbot.asset.service.feign;

import com.niimbot.system.AsConfigDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/10 14:08
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AsConfigFeignClient {

    @GetMapping(value = "server/system/config/{type}")
    List<AsConfigDto> listByType(@PathVariable("type") Integer type);

}
