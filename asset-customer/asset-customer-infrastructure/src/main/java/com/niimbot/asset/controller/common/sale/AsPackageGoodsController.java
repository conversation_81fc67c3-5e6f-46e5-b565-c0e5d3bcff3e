package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.service.feign.PackageGoodsFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.PackageGoodsPageDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <p>
 * 商品套餐详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Api(tags = "商城购物")
@ResultController
@RequestMapping("/api/packageGoods")
@RequiredArgsConstructor
public class AsPackageGoodsController {

    private final PackageGoodsFeignClient packageGoodsFeignClient;

    @ApiOperation(value = "套餐列表")
    @GetMapping("/list")
    public List<PackageGoodsPageDto> page() {
        return packageGoodsFeignClient.getPackageGoodsList();
    }

    @ApiOperation(value = "套餐详情")
    @GetMapping("/detail/{id}")
    public PackageGoodsPageDto detail(@PathVariable("id") Long id) {
        return packageGoodsFeignClient.detail(id);
    }
}
