package com.niimbot.asset.controller.common.system;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.service.AbstractPermissionChangedService;
import com.niimbot.asset.service.feign.CusAccountFeignClient;
import com.niimbot.asset.service.feign.CusRoleFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.*;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/11/18 9:04
 */
@Slf4j
@Api(tags = "用户角色管理")
@ResultController
@RequestMapping("api/common/cusRole")
public class CusRoleController {

    @Resource
    private CusRoleFeignClient cusRoleFeignClient;

    @Resource
    private CusAccountFeignClient accountFeignClient;

    @Resource(name = "rolePermissionChangedService")
    private AbstractPermissionChangedService rolePermissionChangedService;

    @Resource
    private ThreadPoolTaskExecutor assetThreadPool;

    @ApiOperation(value = "全部数据查询列表")
    @GetMapping("/list")
    public List<CusRoleDto> list() {
        return cusRoleFeignClient.list();
    }

    @ApiOperation(value = "角色账户列表")
    @GetMapping("/roleAccountList")
    public List<CusRoleAccountDto> roleAccountList(CusRoleAccountQueryDto roleAccountQueryDto) {
        return cusRoleFeignClient.roleAccountList(roleAccountQueryDto);
    }

    @ApiOperation(value = "查询角色字典")
    @GetMapping("/dict")
    public List<Map<String, ?>> dict() {
        List<CusRoleDto> list = cusRoleFeignClient.list();
        return list.stream().filter(item -> !BaseConstant.ADMIN_ROLE.equals(item.getRoleCode())).map(item ->
                ImmutableMap.of("label", item.getRoleName(), "value", item.getId())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "通过Id数据查询")
    @GetMapping("/{roleId}")
    public CusRoleDto getInfo(@PathVariable("roleId") Long roleId) {
        return cusRoleFeignClient.getInfo(roleId);
    }

    @ApiOperation(value = "通过Id查询权限与账号数量")
    @GetMapping("/amount/{roleId}")
    public CusRoleAmountDto getAmount(@PathVariable("roleId") Long roleId) {
        CusRoleDto info = cusRoleFeignClient.getInfo(roleId);
        int roleAmount = (CollUtil.isNotEmpty(info.getAppMenuIds()) ? info.getAppMenuIds().size() : 0) +
                (CollUtil.isNotEmpty(info.getPcMenuIds()) ? info.getPcMenuIds().size() : 0);
        return new CusRoleAmountDto()
                .setRoleAmount(roleAmount)
                .setAccountAmount(accountFeignClient.getAccountAmount(roleId));
    }

    @ApiOperation(value = "新增角色数据")
    @RepeatSubmit
    @PostMapping()
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean add(@RequestBody @Validated(Insert.class) CusRoleDto cusRole) {
        return cusRoleFeignClient.add(cusRole);
    }

    @ApiOperation(value = "查询角色权限设置")
    @GetMapping("/setting/{roleId}")
    public CusMenuRoleDto getSetting(@PathVariable("roleId") Long roleId) {
        return cusRoleFeignClient.getSetting(roleId);
    }

    @ApiOperation(value = "编辑角色数据")
    @PutMapping()
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated(Update.class) CusRoleDto cusRole) {
        List<String> changeRole = cusRoleFeignClient.edit(cusRole);
        // 更新角色想关联的redis信息
        if (!changeRole.isEmpty()) {
            assetThreadPool.execute(() -> {
                try {
                    List<CusUserDto> userDtoList = cusRoleFeignClient.listUserByRoleId(cusRole.getId());
                    rolePermissionChangedService.permissionChange(userDtoList);
                } catch (Exception e) {
                    log.error("edit role change perm error", e);
                }
            });
        }
        return true;
    }

    @ApiOperation(value = "删除角色数据")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> roleIds) {
        return cusRoleFeignClient.delete(roleIds);
    }

    @ApiOperation(value = "设置为默认角色")
    @PostMapping("/configDefault")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean configDefault(@RequestBody RoleDataAuthorityConfigDto roleDataAuthorityConfigDto) {
        return cusRoleFeignClient.configDefault(roleDataAuthorityConfigDto.getRoleId());
    }
}
