package com.niimbot.asset.service.feign;

import com.niimbot.dynamicform.FormPrintTemplateDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/15 12:27
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface FormPrintTemplateFeignClient {

    @GetMapping("/server/dynamicForm/printTemplate/{orderType}")
    List<FormPrintTemplateDto> printTplList(@PathVariable("orderType") Integer orderType);

    @GetMapping("/server/dynamicForm/printTemplate/detail/{tplId}")
    FormPrintTemplateDto getPrintTpl(@PathVariable("tplId") Long tplId);

    @PostMapping("/server/dynamicForm/printTemplate")
    FormPrintTemplateDto add(@RequestBody FormPrintTemplateDto formPrintTemplate);

    @PutMapping("/server/dynamicForm/printTemplate")
    FormPrintTemplateDto edit(@RequestBody FormPrintTemplateDto formPrintTemplate);

    @DeleteMapping("/server/dynamicForm/printTemplate/{tplId}")
    FormPrintTemplateDto delete(@PathVariable("tplId") Long tplId);

    @PostMapping("/server/dynamicForm/printTemplate/copy/{tplId}")
    FormPrintTemplateDto copy(@PathVariable("tplId") Long tplId);
}
