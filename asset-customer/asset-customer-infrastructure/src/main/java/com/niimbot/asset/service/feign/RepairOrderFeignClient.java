package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.maintenance.RepairOrderDetailDto;
import com.niimbot.maintenance.RepairOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairOrderDto;
import com.niimbot.maintenance.RepairOrderFinishDto;
import com.niimbot.maintenance.RepairOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/25 14:28
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface RepairOrderFeignClient {
    /**
     * 设置审批流信息获取
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/maintenance/repair/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(@RequestBody RepairOrderDto dto);

    /**
     * 创建维修单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/maintenance/repair")
    AuditableCreateOrderResult create(@RequestBody RepairOrderSubmitDto dto);

    /**
     * 完成维修
     *
     * @param dto
     * @return
     */
    @PutMapping(value = "server/maintenance/repair/repairFinish")
    Boolean repairFinish(@RequestBody RepairOrderFinishDto dto);

    /**
     * 维修单详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/maintenance/repair/{id}")
    RepairOrderDto getById(@PathVariable("id") Long id);

    /**
     * 维修单明细详情
     * @param orderId
     * @param assetId
     * @return
     */
    @GetMapping(value = "server/maintenance/repair/detail/{orderId}/{assetId}")
    RepairOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("assetId") Long assetId);

    /**
     * 维修单分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/maintenance/repair/page")
    PageUtils<AsOrderDto> page(@RequestBody AsOrderQueryDto query);

    /**
     * 维修单明细分页查询
     *
     * @param query
     * @return
     */
    @GetMapping(value = "server/maintenance/repair/pageDetail")
    PageUtils<RepairOrderDetailDto> pageDetail(@SpringQueryMap RepairOrderDetailPageQueryDto query);

    /**
     * 资产单据-用于导出
     *
     * @param orderQueryDto orderQueryDto
     * @return 结果
     */
    @PostMapping(value = "server/maintenance/repair/listForExport")
    List<AsOrderDto> listForExport(AsOrderQueryDto orderQueryDto);

    /**
     * 单据资产列表查询
     *
     * @param ids
     * @return 结果
     */
    @PostMapping(value = "server/maintenance/repair/assets")
    List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids);

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @param assetId assetId
     * @return 结果
     */
    @GetMapping(value = "server/maintenance/repair/approveOrderByAsset/{assetId}")
    AsOrderInfoDto getApproveOrderByAssetId(@PathVariable("assetId") Long assetId);

}
