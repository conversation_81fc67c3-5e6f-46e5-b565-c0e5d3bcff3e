package com.niimbot.asset.controller.pc.means;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.view.SimpleExcelView;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.View;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 打印任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Api(tags = "【PC】资产打印任务接口")
@ResultController
@RequestMapping("api/pc/print/task")
@RequiredArgsConstructor
@Validated
public class UserPrintTaskPcController {

    private final PrintFeignClient printFeignClient;

    private final DictConvertUtil dictConvertUtil;

    private final FormFeignClient formFeignClient;

    private final AsAssetUtil assetUtil;

    private final AsMaterialUtil materialUtil;

    @ApiOperation(value = "打印任务列表")
    @PostMapping(value = "list")
    @AutoConvert
    public List<UserPrintTaskViewDto> list(@RequestBody PrintTaskQueryDto dto) {
        List<UserPrintTaskViewDto> list = printFeignClient.listTaskPc(dto);
        list.forEach(viewDto -> viewDto.convert(dto.getIsGoing()));
        return list;
    }

    @ApiOperation(value = "打印任务分页列表")
    @PostMapping(value = "page")
    @AutoConvert
    public PageUtils<UserPrintTaskViewDto> page(@RequestBody PrintTaskQueryDto dto) {
        PageUtils<UserPrintTaskViewDto> pageUtils = printFeignClient.pageTaskPc(dto);
        List<UserPrintTaskViewDto> list = pageUtils.getList();
        list.forEach(viewDto -> viewDto.convert(dto.getIsGoing()));
        return pageUtils;
    }

    @ApiOperation(value = "修改打印任务状态")
    @PostMapping(value = "status")
    public Boolean changeTaskStatus(@RequestBody @Validated PrintTaskChangeStatusDto statusDto) {
        return printFeignClient.changeTaskStatus(statusDto.getId(), statusDto.getTaskStatus());
    }

    @ApiOperation(value = "【PC】修改当前用户全部任务状态【仅支持暂停、取消全部】")
    @GetMapping(value = "status/all")
    public Boolean changeAllTaskStatus(@ApiParam(
            value = "0-取消所有任务，1-暂停所有任务,当前仅支持0、1两值【默认值1】", required = true)
                                       @RequestParam(defaultValue = "1") Integer type) {
        return printFeignClient.changeAllTaskStatus(type);
    }

    @ApiOperation(value = "打印任务记录下载")
    @PostMapping("down")
    public View down(@RequestBody PrintTaskQueryDto dto) {
        List<UserPrintTaskViewDto> list = printFeignClient.down(dto);
        dictConvertUtil.convertToDictionary(list);
        list.forEach(vo -> vo.convert(false));
        List<String> header = ImmutableList.of("打印任务", "打印时间", "操作人", "打印终端", "打印机型号", "资产数量", "打印进度", "状态");
        return new SimpleExcelView<>(list, header, "打印任务导出",
                (sheet, data) -> {
                    int index = 1;
                    for (int i = 0; i < data.size(); i++) {
                        UserPrintTaskViewDto taskViewDto = data.get(i);
                        Row row = sheet.createRow(index++);
                        row.createCell(0).setCellValue(taskViewDto.getTaskName());
                        LocalDateTime printTime = taskViewDto.getPrintTime();
                        String timeForUse = "";
                        if (Objects.nonNull(printTime)) {
                            timeForUse = DateUtil.format(printTime, "yyyy-MM-dd HH:mm:ss");
                        }
                        row.createCell(1).setCellValue(timeForUse);
                        row.createCell(2).setCellValue(taskViewDto.getUserIdText());
                        row.createCell(3).setCellValue(taskViewDto.getPrintSourceText());
                        row.createCell(4).setCellValue(taskViewDto.getPrinterName());
                        row.createCell(5).setCellValue(taskViewDto.getAssetNum());
                        row.createCell(6).setCellValue(taskViewDto.getProgress());
                        row.createCell(7).setCellValue(taskViewDto.getTaskStatusText());
                    }
                });
    }

    @GetMapping("/details/page")
    public PageUtils<UserPrintTaskGroupStatusResult> taskDetailsPage(UserPrintTaskGroupByStatusPageQuery query) {
        PageUtils<UserPrintTaskGroupStatusResult> page = printFeignClient.taskDetailsPage(query);
        for (UserPrintTaskGroupStatusResult result : page.getList()) {
            if (result.getIsTranslation()) {
                continue;
            }
            JSONObject data = result.getData();
            if (query.getPrintType() == DictConstant.PRINT_TYPE_ASSET) {
                FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
                assetUtil.translateAssetJson(data, formVO.getFormFields());
            }
            if (query.getPrintType() == DictConstant.PRINT_TYPE_MATERIAL) {
                FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
                materialUtil.translateMaterialJson(data, formVO.getFormFields());
            }
            result.setData(data);
        }
        return page;
    }
}
