package com.niimbot.asset.controller.common.equipment;

import com.google.common.collect.ImmutableSet;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MaintenanceResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.feign.AsOrderFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.RepairReportOrderFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectTaskFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.equipment.SiteInspectTaskHandleOrderDto;
import com.niimbot.equipment.SiteInspectTaskHandleRecordDto;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.equipment.SiteInspectTaskRangeQryDto;
import com.niimbot.equipment.SiteInspectTaskRangeStatisticsDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.RepairReportOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderSubmitDto;
import com.niimbot.means.AssetDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.validation.Valid;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/9 15:31
 */
@Api(tags = "【设备管理】巡检任务")
@Slf4j
@RequiredArgsConstructor
@ResultController
@RequestMapping("api/common/equipment/siteInspect/task")
public class EquipmentSiteInspectTaskRangeController {

    private final EquipmentSiteInspectTaskFeignClient feignClient;
    private final AsOrderFeignClient orderFeignClient;
    private final AsOrderUtil asOrderUtil;
    private final AssetService assetService;
    private final AssetFeignClient assetFeignClient;
    private final RepairReportOrderFeignClient repairReportOrderFeignClient;

    private ImmutableSet<Integer> allowed = ImmutableSet.<Integer>builder()
            .add(AssetConstant.ASSET_STATUS_IDLE)
            .add(AssetConstant.ASSET_STATUS_USING)
            .add(AssetConstant.ASSET_STATUS_BORROW)
            .build();

    @ApiOperation(value = "巡检路线——列表")
    @GetMapping("/range/page")
    @AutoConvert
    public PageUtils<SiteInspectTaskRangeDto> taskRangePage(@Validated SiteInspectTaskRangeQryDto query) {
        return feignClient.taskRangePage(query);
    }

    @ApiOperation(value = "巡检路线——详情")
    @GetMapping("/range/info")
    @AutoConvert
    public SiteInspectTaskRangeDto taskRangeInfo(@RequestParam("rangeId") Long rangeId) {
        return feignClient.taskRangeInfo(rangeId);
    }

    @ApiOperation(value = "巡检路线——统计")
    @GetMapping("/range/statistics")
    public SiteInspectTaskRangeStatisticsDto taskRangeStatistics(@RequestParam("taskId") Long taskId) {
        return feignClient.taskRangeStatistics(taskId);
    }

    @ApiOperation(value = "巡检路线——点位扫码")
    @GetMapping("/range/scan")
    public SiteInspectTaskRangeDto rangeScan(@RequestParam("taskId") String taskId, @RequestParam("pointId") String pointId) {
        long taskIdLong = Convert.toLong(taskId, -1L);
        // 点位码
        if (pointId.startsWith("point:")) {
            pointId = pointId.replace("point:", "");
            return feignClient.rangeScan(taskIdLong, Convert.toLong(pointId, -1L));
        } else {
            AssetDto infoNoPermByCode = assetFeignClient.getInfoNoPermByCodePhp(pointId);
            return feignClient.rangeScan(taskIdLong, infoNoPermByCode != null ? infoNoPermByCode.getId() : -1L);
        }

    }

    @ApiOperation(value = "巡检路线——忽略")
    @PostMapping("/range/handle/ignore")
    public Boolean handleIgnore(@RequestParam("rangeId") Long rangeId) {
        AuditableOperateResult result = feignClient.handleIgnore(rangeId);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.HANDLE_IGNORE_SITE_INSPECT_TASK, result));
        return true;
    }

    @ApiOperation(value = "巡检路线——查看处理结果")
    @AutoConvert
    @GetMapping("/range/handleRecord")
    public SiteInspectTaskHandleRecordDto handleRecordInfo(@RequestParam("rangeId") Long rangeId) {
        return feignClient.handleRecordInfo(rangeId);
    }

    @ApiOperation(value = "巡检路线——报修报废前置校验")
    @PostMapping("/range/handle/check")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean handleCheck(@RequestParam("rangeId") Long rangeId) {
        feignClient.handleCheck(rangeId);
        return true;
    }

    @ApiOperation(value = "巡检路线——报修")
    @RepeatSubmit
    @PostMapping("/range/handle/repair")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean repair(@RequestBody @Valid SiteInspectTaskHandleOrderDto handleOrderDto) {
        String orderNo = StringUtils.getOrderNo("RR");
        handleOrderDto.getOrderDto().setOrderType(AssetConstant.ORDER_TYPE_REPAIR_REPORT);
        //添加单据号
        handleOrderDto.getOrderDto().setOrderNo(orderNo);

        List<Long> assetIds = handleOrderDto.getOrderDto().getAssets().stream().map(AsOrderAssetDto::getId)
                .collect(Collectors.toList());
        if (assetIds.size() > AssetConstant.ORDER_ASSET_MAX) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_MAX_UPPER_LIMIT,
                    String.valueOf(AssetConstant.ORDER_ASSET_MAX));
        }
        Map<Long, JSONObject> assetMap = assetService.getInfoMap(assetIds);
        if (CollUtil.isNotEmpty(assetMap) && assetMap.values().size() != assetIds.size()) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_LIST_NOT_EXISTS);
        }
        this.checkAssetStatus(assetMap.values(), "报修");

        //盘点结构转资产单据结构
        RepairReportOrderSubmitDto dto = new RepairReportOrderSubmitDto();
        BeanUtil.copyProperties(handleOrderDto, dto);
        dto.getOrderDto().getAssets().forEach(detailDto -> {
            JSONObject data = assetMap.get(detailDto.getId());
            detailDto.setAssetSnapshotData(data);
        });
        dto.getOrderDto().setSummary(asOrderUtil.buildSummary(assetMap.values()));
        // 记录操作信息
        AuditableOperateResult result = feignClient.assetHandle(handleOrderDto);
        repairReportOrderFeignClient.create(dto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.HANDLE_REPAIR_SITE_INSPECT_TASK, result));
        return true;
    }

    @ApiOperation(value = "巡检路线——处置")
    @RepeatSubmit
    @PostMapping("/range/handle/assetLoss")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean assetLoss(@RequestBody @Valid SiteInspectTaskHandleOrderDto handleOrderDto) {
        String orderNo = StringUtils.getOrderNo("CZ");
        handleOrderDto.getOrderDto().setOrderType(AssetConstant.ORDER_TYPE_DISPOSE);
        //添加单据号
        handleOrderDto.getOrderDto().setOrderNo(orderNo);

        List<Long> assetIds = handleOrderDto.getOrderDto().getAssets().stream().map(AsOrderAssetDto::getId)
                .collect(Collectors.toList());
        if (assetIds.size() > AssetConstant.ORDER_ASSET_MAX) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_MAX_UPPER_LIMIT,
                    String.valueOf(AssetConstant.ORDER_ASSET_MAX));
        }
        Map<Long, JSONObject> assetMap = assetService.getInfoMap(assetIds);
        if (CollUtil.isNotEmpty(assetMap) && assetMap.values().size() != assetIds.size()) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_LIST_NOT_EXISTS);
        }
        this.checkAssetStatus(assetMap.values(), "处置");

        //盘点结构转资产单据结构
        AsOrderSubmitDto dto = new AsOrderSubmitDto();
        BeanUtil.copyProperties(handleOrderDto, dto);
        dto.getOrderDto().getAssets().forEach(detailDto -> {
            JSONObject data = assetMap.get(detailDto.getId());
            detailDto.setAssetSnapshotData(data);
        });
        dto.getOrderDto().setSummary(asOrderUtil.buildSummary(assetMap.values()));
        dto.getOrderDto().setAssetNum(assetMap.size());
        // 记录操作信息
        AuditableOperateResult result = feignClient.assetHandle(handleOrderDto);
        orderFeignClient.insert(dto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.HANDLE_LOSS_SITE_INSPECT_TASK, result));
        return true;
    }

    private void checkAssetStatus(Collection<JSONObject> assets, String orderText) {
        assets.forEach(asset -> {
            if (!allowed.contains(asset.getInteger("status"))) {
                throw new BusinessException(MaintenanceResultCode.REPAIR_REPORT_ASSET_STATUS_ILLEGAL, orderText);
            }
        });
    }

}
