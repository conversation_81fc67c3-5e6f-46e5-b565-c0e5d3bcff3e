package com.niimbot.asset.service.feign;

import com.niimbot.sale.EmailAddressDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收货地址
 *
 * <AUTHOR>
 * @date 2021/12/28 10:56
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EmailAddressFeignClient {

    /**
     * 新增收货地址
     *
     * @param address
     * @return
     */
    @PostMapping("server/sale/emailAddress")
    Boolean save(@RequestBody EmailAddressDto address);

    /**
     * 修改收货地址
     *
     * @param address
     * @return
     */
    @PutMapping("server/sale/emailAddress")
    Boolean update(@RequestBody EmailAddressDto address);

    /**
     * 删除收货地址
     *
     * @param id
     * @return
     */
    @DeleteMapping("server/sale/emailAddress/{id}")
    Boolean delete(@PathVariable("id") Long id);

    /**
     * 收货地址列表
     *
     * @return
     */
    @GetMapping("server/sale/emailAddress/list")
    List<EmailAddressDto> queryList();

    /**
     * 设置默认
     *
     * @return
     */
    @PutMapping("server/sale/emailAddress/default/{id}")
    Boolean setDefault(@PathVariable("id") Long id);
}
