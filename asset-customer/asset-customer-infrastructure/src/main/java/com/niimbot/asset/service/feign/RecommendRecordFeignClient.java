package com.niimbot.asset.service.feign;

import com.niimbot.system.RecommendRecordDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface RecommendRecordFeignClient {

    @GetMapping(value = "server/system/recommend/register/list")
    List<RecommendRecordDto> registerList(@RequestParam("recommendEmpId") Long recommendEmpId);

    @GetMapping(value = "server/system/recommend/pay/list")
    List<RecommendRecordDto> payList(@RequestParam("recommendEmpId") Long recommendEmpId);

    @PostMapping(value = "server/system/recommend/verifyMobile")
    Boolean verifyMobile(@RequestParam("recommendEmpId") Long recommendEmpId,
                         @RequestParam("mobile") String mobile);
}
