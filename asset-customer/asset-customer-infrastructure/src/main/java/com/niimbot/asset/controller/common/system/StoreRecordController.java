package com.niimbot.asset.controller.common.system;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.StoreRecordFeignClient;
import com.niimbot.asset.service.impl.MiscExcelExportService;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.StoreRecordDto;
import com.niimbot.means.StoreRecordSearch;
import com.niimbot.means.StoreSnapshotSearch;

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Api(tags = "【入库记录】")
@ResultController
@RequiredArgsConstructor
@RequestMapping("/api/common/store")
public class StoreRecordController {

    private final StoreRecordFeignClient storeRecordFeignClient;

    private final MiscExcelExportService miscExcelExportService;

    private final DesensitizationDataUtil desensitizationDataUtil;

    @AutoConvert
    @ApiOperation("入库记录分页搜索")
    @PostMapping("/record/search")
    public PageUtils<StoreRecordDto> recordSearch(@RequestBody StoreRecordSearch search) {
        return storeRecordFeignClient.recordSearch(search);
    }

    @ApiOperation("单个入库记录对应的快照数据分页搜索")
    @PostMapping("/snapshot/search")
    public PageUtils<JSONObject> snapshotSearch(@RequestBody StoreSnapshotSearch search) {
        PageUtils<JSONObject> result = new PageUtils<>();
        PageUtils<Object> pageResult = storeRecordFeignClient.snapshotSearch(search);
        BeanUtils.copyProperties(pageResult, result);
        //数据脱敏处理
        if (CollUtil.isNotEmpty(pageResult.getList())) {
            List<JSONObject> dataList = pageResult.getList().stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
            desensitizationDataUtil.handleSensitiveField(dataList, SensitiveObjectTypeEnum.ASSET.getCode());
            result.setList(dataList);
        } else {
            result.setList(Collections.EMPTY_LIST);
        }
        return result;
    }

    @ApiOperation("资产入库记录导出")
    @PostMapping("/record/means/export")
    public ExportResponse recordMeansExport(@RequestBody StoreRecordSearch search) {
        PageUtils<StoreRecordDto> result = storeRecordFeignClient.recordSearch(search);
        List<StoreRecordDto> records = result.getList();
        List<MiscExcelExportService.MeansRkData> data = records.stream().map(v -> {
            List<JSONObject> json = storeRecordFeignClient.snapshotList(v.getId());
            return new MiscExcelExportService.MeansRkData(v.getStoreNo(), json);
        }).collect(Collectors.toList());
        if (CollUtil.isEmpty(data)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "无满足条件的资产入库单记录，无法导出");
        }
        return miscExcelExportService.meansRkListExport("资产入库记录导出", search, data);
    }
}
