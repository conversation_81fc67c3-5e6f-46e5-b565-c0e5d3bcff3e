
package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.BiMap;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.service.EquipmentProjectExcelService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectProjectFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.equipment.EntSntProjectImportDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckyMultiSheetImportModel;
import com.niimbot.luckysheet.LuckyMultiSheetModel;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.*;
import com.niimbot.validate.group.Insert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.manager.Constants;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Since 2024-08-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EquipmentProjectExcelServiceImpl implements EquipmentProjectExcelService {

    private Map<Integer, List<TableHeader>> tableHeaderMap;
    private BiMap<String, Integer> projectTypeMap;

    {
        TableHeader projectName = new TableHeader("项目名称", "projectName", ListUtil.empty(), true);
        TableHeader projectCode = new TableHeader("项目编码", "projectCode", ListUtil.empty(), true);
        TableHeader content = new TableHeader("巡检要求", "content", ListUtil.empty(), false);
        TableHeader enablePhoto = new TableHeader("要求拍照", "enablePhotoText", ListUtil.empty(), true);
        TableHeader decimal = new TableHeader("小数位数", "decimal", ListUtil.of(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER), true);
        TableHeader unit = new TableHeader("数值单位", "unit", ListUtil.of(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER), false);
        TableHeader max = new TableHeader("阈值上限", "max", ListUtil.of(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER), false);
        TableHeader min = new TableHeader("阈值下限", "min", ListUtil.of(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER), false);
        TableHeader values = new TableHeader("选项", "valuesText", ListUtil.of(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_OPTION, EquipmentConstant.SNT_PJT_CFG_TYPE_IS_MULTIPLE_OPTION), true);
        TableHeader selected = new TableHeader("异常项", "selectedText", ListUtil.of(EquipmentConstant.SNT_PJT_CFG_TYPE_IS_OPTION, EquipmentConstant.SNT_PJT_CFG_TYPE_IS_MULTIPLE_OPTION), false);
        TableHeader enableAlarm = new TableHeader("异常报警", "enableAlarmText", ListUtil.empty(), true);
        tableHeaderMap = ImmutableMap.of(
                EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER,
                ListUtil.of(projectName, projectCode, content, enablePhoto, decimal, unit, enableAlarm, min, max),
                EquipmentConstant.SNT_PJT_CFG_TYPE_IS_OPTION,
                ListUtil.of(projectName, projectCode, content, enablePhoto, values, enableAlarm, selected),
                EquipmentConstant.SNT_PJT_CFG_TYPE_IS_MULTIPLE_OPTION,
                ListUtil.of(projectName, projectCode, content, enablePhoto, values, enableAlarm, selected),
                EquipmentConstant.SNT_PJT_CFG_TYPE_IS_TEXT,
                ListUtil.of(projectName, projectCode, content, enablePhoto, enableAlarm)
        );
        Map<String, Integer> map = new HashMap<>();
        map.put("数值类型", EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER);
        map.put("单选类型", EquipmentConstant.SNT_PJT_CFG_TYPE_IS_OPTION);
        map.put("多选类型", EquipmentConstant.SNT_PJT_CFG_TYPE_IS_MULTIPLE_OPTION);
        map.put("文本类型", EquipmentConstant.SNT_PJT_CFG_TYPE_IS_TEXT);
        projectTypeMap = new BiMap<>(map);
    }

    protected static ThreadLocal<GlobalCache> globalCache = new TransmittableThreadLocal<>();
    protected static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    private final EquipmentSiteInspectProjectFeignClient projectFeignClient;
    private final RedisService redisService;
    private final ImportService importService;
    private final ImportTaskFeignClient importTaskFeignClient;

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ClassPathResource templateSource = new ClassPathResource("/excelTemplate/equipment_project.xlsx");
        try (OutputStream out = response.getOutputStream();
             InputStream inputStream = templateSource.getInputStream()) {
            String fileName = "设备巡检项目导入模板.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
        ExcelReader reader = ExcelUtil.getReader(stream);
        // 读取数据并校验
        List<EntSntProjectImportDto> entSntProjectImportList = new ArrayList<>();
        for (Sheet sheet : reader.getSheets()) {
            if (projectTypeMap.containsKey(sheet.getSheetName())) {
                reader.setSheet(sheet);
                Integer type = projectTypeMap.get(sheet.getSheetName());
                reader.setHeaderAlias(tableHeaderMap.get(type).stream().collect(Collectors.toMap(TableHeader::getName, TableHeader::getCode)));
                List<EntSntProjectImportDto> entSntProjectImports = reader.read(2, 2, EntSntProjectImportDto.class);
                entSntProjectImports.forEach(p -> p.setConfigType(projectTypeMap.get(sheet.getSheetName())));
                entSntProjectImportList.addAll(entSntProjectImports);
            }
        }
        this.importExcel(null, entSntProjectImportList, fileName, fileSize, companyId, true);
    }

    @Override
    public List<LuckyMultiSheetModel> importError(Long taskId) {
        return projectFeignClient.importError(taskId);
    }

    @Override
    public void importErrorSave(Long taskId, List<LuckyMultiSheetImportModel> sheetModels, Long companyId) {
        List<EntSntProjectImportDto> projectList = new ArrayList<>();
        // 循环sheet页
        for (LuckyMultiSheetImportModel sheetModel : sheetModels) {
            if (projectTypeMap.containsKey(sheetModel.getName()) &&
                    CollUtil.isNotEmpty(sheetModel.getCelldata())) {
                List<List<Object>> celldataList = sheetModel.getCelldata();
                // 项目类型
                Integer projectType = projectTypeMap.get(sheetModel.getName());
                List<TableHeader> tableHeaders = tableHeaderMap.get(projectType);
                for (int i = 1; i < celldataList.size(); i++) {
                    List<Object> data = celldataList.get(i);
                    EntSntProjectImportDto projectImportDto = new EntSntProjectImportDto();
                    for (int j = 0; j < tableHeaders.size(); j++) {
                        TableHeader tableHeader = tableHeaders.get(j);
                        if (data.size() > j) {
                            ReflectUtil.setFieldValue(projectImportDto, tableHeader.getCode(), data.get(j));
                        }
                    }
                    projectImportDto.setConfigType(projectType);
                    projectList.add(projectImportDto);
                }
            }
        }
        this.importExcel(taskId, projectList, "设备巡检项目在线编辑保存", 0L, companyId, false);
    }

    private void importExcel(Long taskId,
                             List<EntSntProjectImportDto> read,
                             String fileName,
                             Long fileSize,
                             Long companyId,
                             boolean async) {
        // 判断是否超过最大上传条数
        if (read.size() > MAX_BATCH) {
            throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "巡检项目", Convert.toStr(MAX_BATCH));
        }
        // 删除历史导入信息
        if (taskId != null) {
            projectFeignClient.importErrorDeleteAll(taskId);
        }
        GlobalCache global = new GlobalCache().setCompany(companyId);
        globalCache.set(global);
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto();
                importCache.setFileName(fileName);
                importCache.setImportType(DictConstant.IMPORT_TYPE_INSPECT_PROJECT);
                importCache.setFileSize(fileSize);
                importCache.setCount(read.size());
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(taskId, read, importCache);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto();
            importCache.setFileName(fileName);
            importCache.setImportType(DictConstant.IMPORT_TYPE_INSPECT_PROJECT);
            importCache.setFileSize(fileSize);
            importCache.setCount(read.size());
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(taskId, read, importCache);
        }
    }

    private void startImport(Long taskId, List<EntSntProjectImportDto> read, ImportDto importCache) {
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (taskId != null) {
            importTaskDto = importTaskFeignClient.queryById(taskId);
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, globalCache.get().getCompany()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
            }
            importService.sendInspectProject(globalCache.get().getCompany());
            // 循环处理行数据
            ImportTaskDto finalImportTaskDto = importTaskDto;
            Map<Integer, List<EntSntProjectImportDto>> readMap = read.stream().collect(Collectors.groupingBy(EntSntProjectImportDto::getConfigType));
            // 按照sheet页顺序处理
            List<Integer> sheetTypes = projectTypeMap.values().stream().sorted(Comparator.comparingInt(f -> f)).collect(Collectors.toList());
            AtomicInteger allSuccessNum = new AtomicInteger(0);
            for (Integer sheetType : sheetTypes) {
                // 记录一行错误信息，生成LuckySheet格式数据
                AtomicInteger successNum = new AtomicInteger(0);
                // 写入表头数据
                this.saveLuckySheetHead(importTaskDto.getId(), sheetType);
                // 获取数据
                List<EntSntProjectImportDto> projectImportList = readMap.getOrDefault(sheetType, ListUtil.empty());
                String sheetName = projectTypeMap.getKey(sheetType);
                IntStream.range(0, projectImportList.size()).forEach(idx -> {
                    EntSntProjectImportDto importDto = projectImportList.get(idx);
                    importDto.setTaskId(finalImportTaskDto.getId());
                    importDto.setErrorNum(0);
                    importDto.setSheetIndex(sheetType);
                    importDto.setSheetName(sheetName);
                    List<LuckySheetModel> luckySheetModelRow = new ArrayList<>();
                    // 调用validator
                    Set<ConstraintViolation<EntSntProjectImportDto>> validate = VALIDATOR.validate(importDto, Insert.class);
                    Map<String, String> validateData = validate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));
                    // 项目名称
                    LuckySheetModel projectName = validateField(idx + 1 - successNum.get(), 0, importDto.getProjectName(), importDto, "projectName", validateData);
                    luckySheetModelRow.add(projectName);
                    // 项目编码
                    LuckySheetModel projectCode = validateField(idx + 1 - successNum.get(), 1, importDto.getProjectCode(), importDto, "projectCode", validateData);
                    luckySheetModelRow.add(projectCode);
                    // 巡检要求
                    LuckySheetModel content = validateField(idx + 1 - successNum.get(), 2, importDto.getContent(), importDto, "content", validateData);
                    luckySheetModelRow.add(content);
                    // 要求拍照
                    LuckySheetModel enablePhotoText = validateField(idx + 1 - successNum.get(), 3, importDto.getEnablePhotoText(), importDto, "enablePhotoText", validateData);
                    luckySheetModelRow.add(enablePhotoText);
                    // 特殊：数值类型
                    if (sheetType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_NUMBER) {
                        Set<ConstraintViolation<EntSntProjectImportDto>> numberValidate = VALIDATOR.validate(importDto, EntSntProjectImportDto.NumberType.class);
                        Map<String, String> numberValidateMap = numberValidate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));
                        // 小数位数
                        LuckySheetModel decimal = validateField(idx + 1 - successNum.get(), 4, importDto.getDecimal(), importDto, "decimal", numberValidateMap);
                        luckySheetModelRow.add(decimal);
                        // 数值单位
                        LuckySheetModel unit = validateField(idx + 1 - successNum.get(), 5, importDto.getUnit(), importDto, "unit", numberValidateMap);
                        luckySheetModelRow.add(unit);
                        // 异常报警
                        LuckySheetModel enableAlarmText = validateField(idx + 1 - successNum.get(), 6, importDto.getEnableAlarmText(), importDto, "enableAlarmText", validateData);
                        luckySheetModelRow.add(enableAlarmText);
                        // 阈值上下限
                        if (BooleanUtil.isTrue(importDto.getEnableAlarm())) {
                            LuckySheetModel min = validateField(idx + 1 - successNum.get(), 7, importDto.getMin(), importDto, "min", numberValidateMap);
                            luckySheetModelRow.add(min);
                            LuckySheetModel max = validateField(idx + 1 - successNum.get(), 8, importDto.getMax(), importDto, "max", numberValidateMap);
                            luckySheetModelRow.add(max);
                        } else {
                            // 清理配置
                            importDto.setMin(null).setMax(null);
                        }
                    }
                    // 特殊：单选类型
                    // 特殊：多选类型
                    else if (sheetType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_OPTION ||
                            sheetType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_MULTIPLE_OPTION) {
                        Set<ConstraintViolation<EntSntProjectImportDto>> optionValidate = VALIDATOR.validate(importDto, EntSntProjectImportDto.OptionType.class);
                        Map<String, String> optionValidateMap = optionValidate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));
                        // 选项
                        if (importDto.getValues().size() > 20) {
                            optionValidateMap.put("valuesText", "最多可添加20项");
                        }
                        LuckySheetModel valuesText = validateField(idx + 1 - successNum.get(), 4, importDto.getValuesText(), importDto, "valuesText", optionValidateMap);
                        luckySheetModelRow.add(valuesText);
                        // 异常报警
                        LuckySheetModel enableAlarmText = validateField(idx + 1 - successNum.get(), 5, importDto.getEnableAlarmText(), importDto, "enableAlarmText", validateData);
                        luckySheetModelRow.add(enableAlarmText);
                        if (BooleanUtil.isTrue(importDto.getEnableAlarm())) {
                            List<String> selected = new ArrayList<>(importDto.getSelected());
                            selected.removeAll(importDto.getValues());
                            if (!selected.isEmpty()) {
                                optionValidateMap.put("selectedText", "异常项目" + String.join("，", selected) + "不在选项内");
                            }
                            // 异常项目
                            LuckySheetModel selectedText = validateField(idx + 1 - successNum.get(), 6, importDto.getSelectedText(), importDto, "selectedText", optionValidateMap);
                            luckySheetModelRow.add(selectedText);
                        } else {
                            // 清理配置
                            importDto.setSelectedText(null);
                        }
                    }
                    // 特殊：文本类型
                    else if (sheetType == EquipmentConstant.SNT_PJT_CFG_TYPE_IS_TEXT) {
                        // 异常报警
                        LuckySheetModel enableAlarmText = validateField(idx + 1 - successNum.get(), 4, importDto.getEnableAlarmText(), importDto, "enableAlarmText", validateData);
                        luckySheetModelRow.add(enableAlarmText);
                    }
                    importDto.setSheetModelList(luckySheetModelRow);
                    Boolean success = projectFeignClient.saveSheetData(importDto);
                    if (BooleanUtil.isTrue(success)) {
                        successNum.getAndIncrement();
                    }
                    importService.sendInspectProject(globalCache.get().getCompany());
                });
                allSuccessNum.addAndGet(successNum.get());
            }
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_ENT_SNT_PROJECT, new AuditableImportResult(allSuccessNum.get())));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, globalCache.get().getCompany()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, globalCache.get().getCompany()), "finish", true);
            importService.sendInspectProject(globalCache.get().getCompany());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, globalCache.get().getCompany());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
            }
            this.clearThreadLocal();
        }
    }

    private void saveLuckySheetHead(Long taskId, Integer sheetType) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        String sheetName = projectTypeMap.getKey(sheetType);
        importErrorDto.setSheetIndex(sheetType).setSheetName(sheetName);
        List<TableHeader> tableHeaders = tableHeaderMap.get(sheetType);
        List<LuckySheetModel> headModelList = new ArrayList<>();
        for (int i = 0; i < tableHeaders.size(); i++) {
            TableHeader header = tableHeaders.get(i);
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(i);
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(header.name);
            if (header.required) {
                modelV.setFc("#ff0000");
            }
            headModelList.add(luckySheetModel);
        }
        importErrorDto.setHeadModelList(headModelList);
        this.projectFeignClient.saveSheetHead(importErrorDto);
    }

    protected LuckySheetModel validateField(int r, int c, String data, EntSntProjectImportDto importDto, String errorCode, Map<String, String> error) {
        String trim = StrUtil.trim(data);
        LuckySheetModel model = new LuckySheetModel();
        LuckySheetModel.Value modelV = model.getV();
        model.setR(r).setC(c);
        modelV.setV(trim);
        if (error.containsKey(errorCode)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(error.get(errorCode));
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
        return model;
    }

    /**
     * 清理本地缓存
     */
    protected void clearThreadLocal() {
        globalCache.remove();
    }

    @Data
    @Accessors(chain = true)
    protected static class GlobalCache {
        private Long company;
    }

    @Getter
    @AllArgsConstructor
    private static class TableHeader {
        private String name;
        private String code;
        private List<Integer> types;
        private boolean required;
    }

}
