package com.niimbot.asset.controller.common.material;

import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.utils.IOUtils;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.asset.service.feign.TagFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.PrintDataSetTagDto;
import com.niimbot.system.UserTagPrintDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 耗材打印
 *
 * <AUTHOR>
 * @date 2021/11/24 10:38
 */
@Api(tags = "耗材打印公共管理")
@ResultController
@RequestMapping("api/common/print/material")
public class MaterialPrintCommonController {

    private final PrintFeignClient printFeignClient;

    private final TagFeignClient tagFeignClient;

    private final AbstractFileUtils fileUtils;

    private final FileUploadConfig uploadConfig;

    @Value("${server.port}")
    private String port;

    @Autowired
    public MaterialPrintCommonController(PrintFeignClient printFeignClient, TagFeignClient tagFeignClient,
                                         AbstractFileUtils fileUtils, FileUploadConfig uploadConfig) {
        this.printFeignClient = printFeignClient;
        this.tagFeignClient = tagFeignClient;
        this.fileUtils = fileUtils;
        this.uploadConfig = uploadConfig;
    }

    /**
     * 根据打印设备获取打印默认数据
     *
     * @param printerName 设备名称
     * @return 打印默认数据
     */
    @ApiOperation(value = "获取用户的打印模板")
    @GetMapping("/getPrintTpl")
    public UserTagPrintDto getPrintTpl(
            @ApiParam(name = "printerName", value = "打印机名称")
            @RequestParam(value = "printerName") String printerName) {
        UserTagPrintDto printTpl = printFeignClient.getPrintTpl(DictConstant.PRINT_TYPE_MATERIAL, printerName);

        // 补全标签路径
        String tagUrl = printTpl.getTagUrl();
        String url = fileUtils.convertToDownloadUrl(tagUrl);
        printTpl.setTagUrl(url);

        // 线上环境远程调用获取不到本地资源
        String replace = url.replace(uploadConfig.getDomain(), "http://127.0.0.1:" + port);
        // 获取远程图片的宽高
        Map<String, Integer> sourceImg = IOUtils.getWidthAndHeight(replace);
        printTpl.setWidth(sourceImg.getOrDefault("width", 0));
        printTpl.setHeight(sourceImg.getOrDefault("height", 0));

        return printTpl;
    }

    /**
     * 设置默认标签模板
     *
     * @return Boolean
     */
    @ApiOperation(value = "设置默认标签模板")
    // @PostMapping("/setDefaultTag")
    @RequestMapping(method = {RequestMethod.PUT, RequestMethod.POST}, value = "/setDefaultTag")
    @ResultMessage("设置默认标签模板成功")
    public Boolean setDefaultTag(@RequestBody @Validated PrintDataSetTagDto printDataSetTagDto) {
        return tagFeignClient.setDefaultCftag(printDataSetTagDto);
    }
}
