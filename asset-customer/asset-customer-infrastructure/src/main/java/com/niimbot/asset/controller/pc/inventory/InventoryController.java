package com.niimbot.asset.controller.pc.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.InventoryResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.*;
import com.niimbot.asset.service.AssetQueryFieldService;
import com.niimbot.asset.service.InventoryAssetService;
import com.niimbot.asset.service.InventoryExcelService;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.dynamicform.BuildAssetLogDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.*;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDto;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.system.QueryConditionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 盘点单后台api
 *
 * <AUTHOR>
 * @date 2021/4/12 11:50
 */
@Slf4j
@Api(tags = "盘点管理")
@ResultController
@RequestMapping("api/pc/inventory")
@RequiredArgsConstructor
public class InventoryController {

    private final InventoryFeignClient inventoryFeignClient;

    private final FormFeignClient formFeignClient;

    private final AsAssetUtil assetUtil;

    private final InventoryAssetService inventoryAssetService;

    private final AssetFeignClient assetFeignClient;

    private final DictConvertUtil dictConvertUtil;

    private final RedisService redisService;

    private final AssetQueryFieldService assetQueryFieldService;

    private final InventoryExcelService inventoryExcelService;

    private static final String STANDARD_ID = "standardId";

    @ApiOperation(value = "PC盘点单分页列表")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<InventoryDto> page(InventoryQueryDto dto) {
        return inventoryFeignClient.page(dto);
    }

    @ApiOperation(value = "预创建盘点单资产查询id")
    @GetMapping("/getQueryId")
    public InventoryQueryIdDto getQueryId() {
        return new InventoryQueryIdDto().setQueryId(IdUtils.getId());
    }


    @ApiOperation(value = "生成预创建盘点单资产数据")
    @PostMapping("/createInventoryQueryAsset")
    public void createInventoryQueryAsset(@RequestBody @Valid InventoryCreateAssetQueryDto dto) {
        inventoryFeignClient.createInventoryQueryAsset(dto);
    }

    @ApiOperation(value = "预创建盘点单资产数据分页")
    @PostMapping(value = "/create/asset/page")
    public PageUtils<JSONObject> assetPage(@RequestBody @Valid InventoryCreateAssetQueryPageDto queryDto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        PageUtils<AssetDto> pageUtils = inventoryFeignClient.createAssetPage(queryDto);
        List<AssetDto> list = pageUtils.getList();
        // 返回结果集
        List<JSONObject> assetDataList = list.stream().map(AssetDto::translate).collect(Collectors.toList());
        assetUtil.translateAssetJsonBatch(assetDataList, formVO.getFormFields());

        PageUtils<JSONObject> assetPcPage = new PageUtils<>();
        BeanUtil.copyProperties(pageUtils, assetPcPage);
        assetPcPage.setList(assetDataList);
        Object o = redisService.get(RedisConstant.inventoryAssetTotalNumKey(queryDto.getQueryId()));
        assetPcPage.setTotalCount(Convert.toInt(o));
        redisService.del(RedisConstant.inventoryAssetTotalNumKey(queryDto.getQueryId()));
        return assetPcPage;
    }

    @ApiOperation(value = "创建盘点单")
    @PostMapping
    @RepeatSubmit
    @ResultMessage("提交成功")
    public InventoryDto create(@RequestBody @Valid InventorySubmitDto dto) {
        if (dto.getRangeType() == 1) {
            List<AssetQueryConditionDto> assetQueryConditions = dto.getAssetQueryConditions();
            if (CollUtil.isEmpty(assetQueryConditions)) {
                BusinessExceptionUtil.throwException("资产筛选条件不能为空");
            }
            if (assetQueryConditions.size() > 10) {
                BusinessExceptionUtil.throwException("资产筛选条件不能超过10个");
            }
            List<QueryConditionDto> queryConditionDtos = assetQueryFieldService.assetQueryView(null);
            Map<String, QueryConditionDto> conditionDtoMap = queryConditionDtos.stream()
                    .collect(Collectors.toMap(QueryConditionDto::getCode, o -> o, (k1, k2) -> k1));

            for (AssetQueryConditionDto queryConditionDto : assetQueryConditions) {
                List<QueryConditionDto> conditions = queryConditionDto.getConditions();
                if (CollUtil.isNotEmpty(conditions)) {
                    for (QueryConditionDto condition : conditions) {
                        if (conditionDtoMap.containsKey(condition.getCode())) {
                            QueryConditionDto conditionDto = conditionDtoMap.get(condition.getCode());
                            condition.setName(conditionDto.getName());
                            condition.setType(conditionDto.getType());
                            condition.setFieldProps(conditionDto.getFieldProps());
                        }
                    }
                }
            }
        }
        Long inventoryId = inventoryFeignClient.create(dto);
        return new InventoryDto().setId(inventoryId);
    }

    @ApiOperation(value = "获取盘点审核人")
    @Deprecated
    @PostMapping("/user")
    public List<Map<String, ?>> getInventoryUser(@RequestBody InventoryAssetRangeDto dto) {
        // 前端排查没用到，先返回空
        return ListUtil.empty();
    }

    @ApiOperation(value = "盘点分配方式详细")
    @PostMapping("/dispatchDetail")
    public InventoryDispatchDetailDto getDispatchDetail(@RequestBody @Validated InventoryAssetRangeWithModeDto dto) {
        return inventoryFeignClient.getDispatchDetail(dto);
    }

    @ApiOperation(value = "盘点任务明细")
    @GetMapping("/detail/{id}")
    @AutoConvert
    public InventoryDetailDto getDetailById(@PathVariable Long id) {
        return inventoryFeignClient.getDetailWithoutAsset(id);
    }

    @ApiOperation(value = "盘点单盘盈资产数据")
    @PostMapping("/detail/surplus")
    public PageUtils<JSONObject> getSurplusById(@RequestBody @Validated InventorySurplusQueryDto dto) {
        PageUtils<InventorySurplusDto> inventorySurplusDto = inventoryFeignClient.getSurplusById(dto);
        List<InventorySurplusDto> inventorySurplusDtoList = inventorySurplusDto.getList();
        List<JSONObject> surplusData = Lists.newArrayList();
        //查询属性
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        try {
            dictConvertUtil.convertToDictionary(inventorySurplusDtoList);
            for (InventorySurplusDto surplusDto : inventorySurplusDtoList) {
                JSONObject assetData = surplusDto.getAssetData();

                // 资产json数据（取快照数据）
                // assetUtil.translateAssetJson(assetData, formVO.getFormFields());

                String jackson = JacksonConverter.MAPPER.writeValueAsString(surplusDto);
                JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
                json.remove("assetData");
                json.remove("remark");
                assetData.putAll(json);

                if (InventoryConstant.ASSET_MARK_ON.equals(surplusDto.getAssetMark())) {
                    Long standardId = assetData.getLong(STANDARD_ID);
                    List<FormFieldCO> formFieldCOS = inventoryAssetService.buildAttrCache(formVO.getFormFields(), formVO.getFormId(), standardId);
                    // 获取资产修改内容
                    JSONObject tmpJsonOrigin = JSONObject.parseObject(surplusDto.getAssetChangRecordOrigen());
                    JSONObject tmpJsonChanged = JSONObject.parseObject(surplusDto.getAssetChangRecord());
                    String tmpStrJson = assetFeignClient.buildAssetLog(new BuildAssetLogDto(tmpJsonOrigin, tmpJsonChanged, formFieldCOS));
                    assetData.put("assetChangRecordText", tmpStrJson);
                }
                surplusData.add(assetData);
            }
        } catch (Exception e){
            log.error("获取盘盈资产数据失败", e);
        }

        PageUtils<JSONObject> surplusPage = new PageUtils<>();
        BeanUtil.copyProperties(inventorySurplusDto, surplusPage);
        surplusPage.setList(surplusData);
        return surplusPage;
    }

    @ApiOperation(value = "单个任务驳回")
    @PutMapping("/rejected")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean rejected(@RequestBody @Validated({InventoryTaskApproveDto.RejectedData.class}) InventoryTaskApproveDto approveDto) {
        return inventoryFeignClient.rejected(approveDto);
    }

    @ApiOperation(value = "整单驳回")
    @PutMapping("/rejectedAll")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean rejected(@RequestBody @Validated InventoryApproveSubmitDto approveDto) {
        if(StrUtil.isBlank(approveDto.getOpinion())){
            throw new BusinessException(InventoryResultCode.INVENTORY_APPROVAL_IS_EMPTY);
        }
        return inventoryFeignClient.rejectedAll(approveDto);
    }

    @ApiOperation(value = "整单同意")
    @PutMapping("/approved")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean approved(@RequestBody @Validated InventoryApproveSubmitDto approveDto) {
        return inventoryFeignClient.approved(approveDto);
    }

    @ApiOperation(value = "单个任务同意")
    @PutMapping("/approvedOne")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean approvedOne(@RequestBody @Validated InventoryTaskApproveDto approveDto) {
        return inventoryFeignClient.approvedOne(approveDto);
    }

    @ApiOperation(value = "修改审核人")
    @PutMapping("/updateApprover")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean updateApprover(@RequestBody @Validated InventoryUpdateApproverDto approveDto) {
        return inventoryFeignClient.updateApprover(approveDto);
    }

    @ApiOperation(value = "盘点单终止")
    @PutMapping("/stop/{inventoryId}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean stop(@PathVariable Long inventoryId) {
        return inventoryFeignClient.stop(inventoryId);
    }

    @ApiOperation(value = "盘点单删除")
    @DeleteMapping("/{inventoryId}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @RepeatSubmit
    public Boolean removeById(@PathVariable("inventoryId") Long inventoryId) {
        return inventoryFeignClient.removeById(inventoryId);
    }

    @ApiOperation(value = "盘点单概览")
    @GetMapping("/view/{id}")
    @AutoConvert
    public InventoryView getInventoryView(@PathVariable Long id) {
        return inventoryFeignClient.getInventoryView(id);
    }

    @ApiOperation(value = "盘点报告")
    @GetMapping("/report/{id}")
    @AutoConvert
    public InventoryReportDto getInventoryReport(@PathVariable Long id) {
        return inventoryFeignClient.getInventoryReport(id);
    }

    @ApiOperation(value = "盘点报告导出")
    @PostMapping("/report/export")
    public void inventoryReportExport(@RequestBody @Valid  InventoryReportExportDto dto) {
        inventoryExcelService.inventoryReportExport(dto.getId());
    }

    @ApiOperation(value = "盘点范围创建")
    @PostMapping("/range/create")
    public List<InventoryRangeGroupDto> createRangeGroup(@RequestBody @Valid InventoryRangeGroupCreateDto dto) {
        if (dto.getRangeType() == 1) {
            List<AssetQueryConditionDto> assetQueryConditions = dto.getAssetQueryConditions();
            if (CollUtil.isEmpty(assetQueryConditions)) {
                BusinessExceptionUtil.throwException("资产筛选条件不能为空");
            }
            if (assetQueryConditions.size() > 10) {
                BusinessExceptionUtil.throwException("资产筛选条件不能超过10个");
            }
            List<QueryConditionDto> queryConditionDtos = assetQueryFieldService.assetQueryView(null);
            Map<String, QueryConditionDto> conditionDtoMap = queryConditionDtos.stream()
                    .collect(Collectors.toMap(QueryConditionDto::getCode, o -> o, (k1, k2) -> k1));

            for (AssetQueryConditionDto queryConditionDto : assetQueryConditions) {
                List<QueryConditionDto> conditions = queryConditionDto.getConditions();
                if (CollUtil.isNotEmpty(conditions)) {
                    for (QueryConditionDto condition : conditions) {
                        if (conditionDtoMap.containsKey(condition.getCode())) {
                            QueryConditionDto conditionDto = conditionDtoMap.get(condition.getCode());
                            condition.setName(conditionDto.getName());
                            condition.setType(conditionDto.getType());
                            condition.setFieldProps(conditionDto.getFieldProps());
                        }
                    }
                }
            }
        }
        return inventoryFeignClient.createRangeGroup(dto);
    }
}
