package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.maintenance.MaintainOrderDto;
import com.niimbot.maintenance.MaintainOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/21 18:23
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaintainOrderFeignClient {

    /**
     * 保养单分页列表
     *
     * @param queryDto 查询参数
     * @return 保养单列表
     */
    @PostMapping(value = "server/maintenance/maintain/order/page")
    PageUtils<AsOrderDto> page(AsOrderQueryDto queryDto);

    @PostMapping(value = "server/maintenance/maintain/order/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(MaintainOrderDto orderDto);

    @PostMapping(value = "server/maintenance/maintain/order")
    AuditableCreateOrderResult insert(MaintainOrderSubmitDto dto);

    @GetMapping(value = "server/maintenance/maintain/order/{id}")
    MaintainOrderDto info(@PathVariable("id") Long id);

    @GetMapping(value = "server/maintenance/maintain/order/assetData/{orderId}")
    JSONObject getAssetData(@PathVariable("orderId") Long orderId);

    /**
     * 资产单据-用于导出
     *
     * @param orderQueryDto orderQueryDto
     * @return 结果
     */
    @PostMapping(value = "server/maintenance/maintain/order/listForExport")
    List<AsOrderDto> listForExport(AsOrderQueryDto orderQueryDto);

    /**
     * 单据资产列表查询
     *
     * @param ids
     * @return 结果
     */
    @PostMapping(value = "server/maintenance/maintain/order/assets")
    List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids);
}
