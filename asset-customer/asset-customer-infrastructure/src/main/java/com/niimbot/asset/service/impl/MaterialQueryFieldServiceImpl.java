package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.feign.*;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/9/6 15:34
 */
@Service
public class MaterialQueryFieldServiceImpl implements MaterialQueryFieldService {

    @Autowired
    private MaterialFeignClient materialFeignClient;

    @Autowired
    private FormFeignClient formFeignClient;

    @Autowired
    private StandardFeignClient standardFeignClient;

    @Autowired
    private AssetQueryFieldFeignClient assetQueryFieldFeignClient;

    @Autowired
    private AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @Value("${asset.upload.domain}")
    private String domain;

    @Override
    public List<QueryConditionDto> materialAllQueryField() {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.MATERIAL_FILED_STANDARD));

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_MATERIAL_SERIALNO, FormFieldCO.YZC_MATERIAL_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }

        // 补齐 标准品，创建人，创建时间
        FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);
        QueryConditionDto createBy = toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            queryConditionDtos.add(createBy);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    @Override
    public List<QueryConditionDto> materialSearchAllQueryField() {
        LinkedList<QueryConditionDto> queryConditionDtos = new LinkedList<>(this.materialAllQueryField());
        LinkedList<QueryConditionDto> queryConditionDtosClone = (LinkedList<QueryConditionDto>) queryConditionDtos.clone();
        /**
         * 过滤出的字段
         */
        List<String> excludes = ListUtil.of("standardId","images","files","date");
        List<String> lastExcludes = ListUtil.of("yzc_emp","yzc_org","yzc_area","yzc_asset_cate","yzc_material_cate");
        List<QueryConditionDto> lastList = new ArrayList<>();
        queryConditionDtosClone.forEach(t->{
            if (excludes.contains(t.getType())){
                queryConditionDtos.remove(t);
            }else if(lastExcludes.contains(t.getType())){
                queryConditionDtos.remove(t);
                lastList.add(t);
            }
        });
        QueryConditionDto q1 = new QueryConditionDto();
        q1.setType("input");
        q1.setCode("materialName");
        q1.setName("耗材名称");
        QueryConditionDto q2 = new QueryConditionDto();
        q2.setType("input");
        q2.setCode("materialCode");
        q2.setName("耗材编码");
        queryConditionDtos.addFirst(q1);
        queryConditionDtos.addFirst(q2);
        lastList.forEach(t->{
            if ("createBy".equals(t.getCode())){
                t.setCode("create_by");
            }
            queryConditionDtos.addLast(t);
        });
        return queryConditionDtos;
    }

    @Override
    public List<QueryConditionDto> materialQueryView() {
        return materialQueryView(QueryFieldConstant.TYPE_MATERIAL_QUERY);
    }

    private List<QueryConditionDto> materialQueryView(String type) {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> materialAllQueryField = materialAllQueryField();
        Map<String, QueryConditionDto> materialAllQueryFieldMap = materialAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(type);
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (materialAllQueryFieldMap.containsKey(f)) {
                result.add(materialAllQueryFieldMap.get(f));
            }
        });

        // 判断是否有标准品
        if (generalDto.getStandardId() != null && generalDto.getStandardId() > 0L) {
            QueryConditionStandardDto standardDto = standardAllField(generalDto.getStandardId(), false, false);
            Map<String, QueryConditionDto> standardAllFieldMap = standardDto.getConditions()
                    .stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
            generalDto.getStandardCondition().forEach(f -> {
                if (standardAllFieldMap.containsKey(f)) {
                    result.add(standardAllFieldMap.get(f));
                }
            });
        }
        return result;
    }

    @Override
    public Boolean materialHeadField(QueryHeadConfigDto config) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(config.toJson(), QueryFieldConstant.TYPE_MATERIAL_HEAD));
    }

    @Override
    public QueryHeadConfigDto materialHeadField() {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_HEAD);
        JSON conditionJson = one.getConditions();
        QueryHeadConfigDto headConfigDto = conditionJson.toJavaObject(QueryHeadConfigDto.class);
        if (BooleanUtil.isTrue(one.getSysConfig())) {
            List<String> conditions = headConfigDto.getConditions();
            FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
            List<QueryConditionDto> materialAllHeadField = materialAllHeadField(formVO);
            // 添加 top10
            int len = Math.min(materialAllHeadField.size(), 10);
            AtomicInteger count = new AtomicInteger(len);
            for (QueryConditionDto f : materialAllHeadField) {
                if (!conditions.contains(f.getCode())) {
                    if (count.getAndDecrement() >= 0) {
                        conditions.add(f.getCode());
                    } else {
                        break;
                    }
                }
            }
        }
        return headConfigDto;
    }

    @Override
    public List<QueryConditionDto> materialAllHeadField() {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        return materialAllHeadField(formVO);
    }

    public List<QueryConditionDto> materialAllHeadField(FormVO formVO) {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();

        // 补齐 创建人，创建时间
        List<FormFieldCO> formFields = formVO.getFormFields();

        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    @Override
    public List<AssetHeadDto> materialHeadView() {
        return materialHeadView(QueryFieldConstant.TYPE_MATERIAL_HEAD);
    }

    @Override
    public List<AssetHeadDto> exportHeadField(Long standardId) {
        List<AssetHeadDto> tableHead = new ArrayList<>();
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();

        formFields.stream()
                .filter(it -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(it.getFieldType()))
                .map(it -> {
                    AssetHeadDto headDto = new AssetHeadDto();
                    return headDto.setCode(it.getFieldCode())
                            .setName(it.getFieldName())
                            .setType(it.getFieldType());
                })
                .forEach(tableHead::add);

        ListUtil.of(
                QueryFieldConstant.FIELD_CREATE_BY,
                QueryFieldConstant.FIELD_CREATE_TIME).forEach(f -> {
            QueryFieldConstant.Field field = QueryFieldConstant.MATERIAL_EXT_FIELD.get(f);
            if (field != null) {
                tableHead.add(new AssetHeadDto()
                        .setName(field.getName())
                        .setCode(field.getCode())
                        .setType(field.getType()));
            }
        });

        if (ObjectUtil.isNotNull(standardId)) {
            List<FormFieldCO> standardFields = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            standardFields.stream()
                    .filter(it -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(it.getFieldType()))
                    .map(it -> {
                        AssetHeadDto headDto = new AssetHeadDto();
                        return headDto.setCode(it.getFieldCode())
                                .setName(it.getFieldName())
                                .setType(it.getFieldType())
                                .setAttrGroup(2);
                    })
                    .forEach(tableHead::add);
        }
        return tableHead;
    }

    private List<AssetHeadDto> materialHeadView(String type) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        QueryConditionSortDto querySort = materialFeignClient.sortField();
        List<String> querySortList = querySort.getSortList().stream()
                .map(QueryConditionSortDto.Field::getValue).collect(Collectors.toList());

        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> materialAllHeadField = materialAllHeadField(formVO);
        Map<String, QueryConditionDto> materialAllQueryFieldMap = materialAllHeadField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(type);
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        List<String> selectConditions = generalDto.getConditions();
        selectConditions.forEach(f -> {
            if (materialAllQueryFieldMap.containsKey(f)) {
                result.add(materialAllQueryFieldMap.get(f));
            }
        });
        // 如果是系统的，需要补属性
        if (BooleanUtil.isTrue(configDto.getSysConfig())) {
            // 添加 top10
            int len = Math.min(materialAllHeadField.size(), 10);
            AtomicInteger count = new AtomicInteger(len);
            for (QueryConditionDto f : materialAllHeadField) {
                if (!selectConditions.contains(f.getCode())) {
                    if (count.getAndDecrement() >= 0) {
                        result.add(f);
                    } else {
                        break;
                    }
                }
            }
        }

        Map<String, String> transCodeMap = new HashMap<>();
        transCodeMap.put(QueryFieldConstant.FIELD_CREATE_BY, QueryFieldConstant.FIELD_CREATE_BY + "Text");
        Map<String, JSONObject> fieldPropsMap = new HashMap<>();
        JSONObject dateFormat = new JSONObject();
        dateFormat.put("dateFormatType", "yyyy-MM-dd");
        fieldPropsMap.put(QueryFieldConstant.FIELD_CREATE_TIME, dateFormat);
        fieldPropsMap.put(QueryFieldConstant.FIELD_UPDATE_TIME, dateFormat);
        for (FormFieldCO formFieldCO : formVO.getFormFields()) {
            fieldPropsMap.put(formFieldCO.getFieldCode(), formFieldCO.getFieldProps());
            if (StrUtil.isNotEmpty(formFieldCO.getTranslationCode())) {
                transCodeMap.put(formFieldCO.getFieldCode(), formFieldCO.getTranslationCode());
            }
        }

        // 判断是否有标准品
        if (generalDto.getStandardId() != null && generalDto.getStandardId() > 0L) {
            QueryConditionStandardDto standardDto = standardAllField(generalDto.getStandardId(), false, true);
            Map<String, QueryConditionDto> standardAllFieldMap = standardDto.getConditions()
                    .stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
            generalDto.getStandardCondition().forEach(f -> {
                if (standardAllFieldMap.containsKey(f)) {
                    QueryConditionDto queryCondition = standardAllFieldMap.get(f);
                    result.add(queryCondition);
                    fieldPropsMap.put(queryCondition.getCode(), queryCondition.getFieldProps());
                }
            });
        }

        List<AssetHeadDto> headList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            QueryConditionDto conditionDto = result.get(i);
            AssetHeadDto headDto = new AssetHeadDto();
            headDto.setCode(conditionDto.getCode());
            headDto.setName(conditionDto.getName());
            headDto.setType(conditionDto.getType());
            headDto.setFieldProps(fieldPropsMap.get(conditionDto.getCode()));
            headDto.setIsLock(false);
            headDto.setSort(querySortList.contains(conditionDto.getCode()));
            String tranCode = transCodeMap.get(conditionDto.getCode());
            if (FormFieldCO.NUMBER_INPUT.equals(conditionDto.getType())
                    && "2".equals(conditionDto.getFieldProps().getString("numberFormatType"))) {
                tranCode = "%";
            }
            headDto.setTranslationCode(
                    StrUtil.isNotEmpty(tranCode) ? tranCode : "");
            if (i < generalDto.getLockNum()) {
                headDto.setIsLock(true);
            }
            headList.add(headDto);
        }
        return headList;
    }

    @Override
    public List<QueryConditionDto> stockAllQueryField() {
        // 和耗材档案一致
        return materialAllQueryField();
    }

    @Override
    public List<QueryConditionDto> stockQueryView() {
        return materialQueryView(QueryFieldConstant.TYPE_MATERIAL_STOCK_QUERY);
    }

    @Override
    public QueryConditionStandardDto standardAllField(Long standardId, boolean needName, boolean filterFile) {
        QueryConditionStandardDto standardDto = new QueryConditionStandardDto();
        if (standardId == null || standardId == 0L) {
            return standardDto;
        }
        FormVO materialForm = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        if (needName) {
            FormVO form = standardFeignClient.form(standardId, false);
            if (ObjectUtil.isNotNull(form)) {
                standardDto.setStandardName(form.getFormName());
            }
        }
        List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(materialForm.getFormId(), standardId);
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        List<String> filterType = new ArrayList<>();
        filterType.add(FormFieldCO.SPLIT_LINE);
        if (filterFile) {
            filterType.add(FormFieldCO.FILES);
        }
        List<QueryConditionDto> collect = standardExtField.stream().filter(f ->
                !filterType.contains(f.getFieldType())
                        && !f.isHidden()
        ).map(f -> {
            QueryConditionDto queryConditionDto = buildQueryCondition(f);
            queryConditionDto.setOperators(operatorMap.get(queryConditionDto.getType()));
            return queryConditionDto;
        }).collect(Collectors.toList());
        standardDto.setConditions(collect);
        return standardDto;
    }



    private QueryConditionDto buildQueryCondition(FormFieldCO formFieldCO) {
        return new QueryConditionDto()
                .setName(formFieldCO.getFieldName())
                .setCode(formFieldCO.getFieldCode())
                .setType(formFieldCO.getFieldType())
                .setFieldProps(formFieldCO.getFieldProps());
    }

    private QueryConditionDto toQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.MATERIAL_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    @Override
    public List<QueryConditionDto> inventoryAllQueryField() {
        // 和耗材档案一致
        return materialAllQueryField();
    }

    @Override
    public List<QueryConditionDto> inventoryQueryView() {
        return materialQueryView(QueryFieldConstant.TYPE_MATERIAL_INVENTORY_QUERY);
    }
}
