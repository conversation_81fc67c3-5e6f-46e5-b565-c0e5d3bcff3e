package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.service.feign.RecommendRecordFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.RecommendRecordDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/2 10:57
 */
@Slf4j
@Api(tags = "邀请注册")
@ResultController
@RequestMapping("/api/common/recommend")
@RequiredArgsConstructor
public class RecommendController {

    private final RecommendRecordFeignClient recommendRecordFeignClient;

    @ApiOperation(value = "已注册")
    @GetMapping("/register/list")
    public List<RecommendRecordDto> registerList(@RequestParam("recommendEmpId") Long recommendEmpId) {
        return recommendRecordFeignClient.registerList(recommendEmpId);
    }

    @ApiOperation(value = "已签单")
    @GetMapping("/pay/list")
    public List<RecommendRecordDto> payList(@RequestParam("recommendEmpId") Long recommendEmpId) {
        return recommendRecordFeignClient.payList(recommendEmpId);
    }

    @ApiOperation(value = "校验手机号")
    @PostMapping("/verifyMobile")
    public Boolean verifyMobile(@RequestParam("recommendEmpId") Long recommendEmpId,
                                @RequestParam("mobile") String mobile) {
        return recommendRecordFeignClient.verifyMobile(recommendEmpId, mobile);
    }

}
