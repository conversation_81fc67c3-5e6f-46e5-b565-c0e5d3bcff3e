package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.service.feign.InvoiceSettingFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.InvoiceSettingDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2022/3/21 18:35
 */
@Api(tags = "发票设置")
@ResultController
@RequestMapping("api/common/invoiceSetting")
@RequiredArgsConstructor
public class InvoiceSettingController {
    private final InvoiceSettingFeignClient invoiceSettingFeignClient;

    @ApiOperation(value = "发票设置信息查询")
    @GetMapping
    public InvoiceSettingDto queryInfo() {
        return invoiceSettingFeignClient.queryInfo();
    }
}
