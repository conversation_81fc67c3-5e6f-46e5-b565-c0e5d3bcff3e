package com.niimbot.asset.service;

import com.niimbot.luckysheet.LuckyMultiSheetImportModel;
import com.niimbot.luckysheet.LuckyMultiSheetModel;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @Since 2024-08-29
 */
public interface EquipmentProjectExcelService {

    int MAX_BATCH = 10000;

    void exportTemplate(HttpServletResponse response);

    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId);

    List<LuckyMultiSheetModel> importError(Long taskId);

    void importErrorSave(Long taskId, List<LuckyMultiSheetImportModel> sheetModels, Long companyId);
}
