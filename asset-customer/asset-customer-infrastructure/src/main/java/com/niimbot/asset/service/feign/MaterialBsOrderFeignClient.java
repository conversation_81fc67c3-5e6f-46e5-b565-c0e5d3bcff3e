package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2022/10/31 16:04
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialBsOrderFeignClient {

    @PostMapping(value = "server/material/order/bs")
    AuditableCreateOrderResult create(MaterialBsOrderSubmitDto submitDto);

    @GetMapping(value = "server/material/order/bs/{id}")
    MaterialBsOrderDto getById(@PathVariable("id") Long id);

    @GetMapping(value = "server/material/order/bs/pageDetail")
    PageUtils<MaterialBsOrderDetailDto> pageDetail(@SpringQueryMap MaterialOrderDetailQueryDto query);

    @GetMapping(value = "server/material/order/bs/detail/{orderId}/{materialId}")
    MaterialBsOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);

    @PostMapping(value = "server/material/order/bs/page")
    PageUtils<MaterialBsOrderDto> page(MaterialOrderQueryDto query);

    @PostMapping(value = "server/material/order/bs/listForExport")
    PageUtils<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto query);

    @PostMapping(value = "server/material/order/bs/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(MaterialBsOrderDto dto);
}
