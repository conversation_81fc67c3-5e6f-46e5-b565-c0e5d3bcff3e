package com.niimbot.asset.controller.pc.report;

import com.niimbot.asset.service.feign.report.ReportChartConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.report.ReportChartConfigDto;
import com.niimbot.report.ReportChartQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/27 上午10:05
 */
@Slf4j
@Api(tags = "报表图表类型配置")
@ResultController
@RequestMapping("api/pc/report/chart/")
@RequiredArgsConstructor
public class ReportChartConfigController {

    private final ReportChartConfigFeignClient reportChartConfigFeignClient;

    @ApiOperation("配置报表图表类型")
    @RequestMapping("config")
    public Boolean config(@RequestBody @Validated ReportChartConfigDto reportChartConfigDto) {
        return reportChartConfigFeignClient.config(reportChartConfigDto);
    }

    @ApiOperation("修改报表图表类型")
    @RequestMapping("edit")
    public Boolean edit(@RequestBody @Validated ReportChartConfigDto reportChartConfigDto) {
        return reportChartConfigFeignClient.edit(reportChartConfigDto);
    }

    @ApiOperation("查询所有的图表配置")
    @GetMapping("queryAll")
    public List<ReportChartConfigDto> queryAll() {
        return reportChartConfigFeignClient.queryAll();
    }

    @ApiOperation("根据条件查询报表支持图表")
    @GetMapping("queryByCondition")
    public List<ReportChartConfigDto> queryByCondition(ReportChartQueryDto queryDto) {
        return reportChartConfigFeignClient.queryByCondition(queryDto);
    }
}
