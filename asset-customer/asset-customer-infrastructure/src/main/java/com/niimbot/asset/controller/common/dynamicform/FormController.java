package com.niimbot.asset.controller.common.dynamicform;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.dynamicform.FormRelationDto;
import com.niimbot.dynamicform.FormRelationQry;
import com.niimbot.easydesign.form.dto.FormTplAddCmd;
import com.niimbot.easydesign.form.dto.sdk.FormSdkVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @since 2022/7/11 13:57
 */
@Api(tags = "动态表单管理")
@ResultController
@RequestMapping("api/common/form")
@Validated
public class FormController {

    private final FormFeignClient formFeignClient;

    public FormController(FormFeignClient formFeignClient) {
        this.formFeignClient = formFeignClient;
    }

    @GetMapping("/listByBizId/{bizId}")
    public List<FormSdkVO> listByBizId(@PathVariable String bizId) {
        return formFeignClient.listByBizId(bizId);
    }

    @ApiOperation(value = "新增标准品数据")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean saveForm(@RequestBody @Validated FormTplAddCmd formTplAddCmd) {
        return formFeignClient.saveForm(formTplAddCmd);
    }

    @ApiOperation(value = "关联填充")
    @PostMapping("/relationFill")
    public List<FormRelationDto> relationFill(@RequestBody FormRelationQry qry) {
        return formFeignClient.relationFill(qry);
    }
}
