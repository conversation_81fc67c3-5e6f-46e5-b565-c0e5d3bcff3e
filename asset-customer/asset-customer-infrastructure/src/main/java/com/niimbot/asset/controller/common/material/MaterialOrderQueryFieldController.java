package com.niimbot.asset.controller.common.material;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.MaterialOrderFeignClient;
import com.niimbot.asset.service.impl.MaterialOrderQueryFieldServiceImpl;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.OrderQueryHeadConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 资产单据查询字段
 *
 * <AUTHOR>
 * @date 2022/10/17 14:36
 */
@Slf4j
@Validated
@Api(tags = "耗材单据字段管理")
@ResultController
@RequestMapping("api/common/queryField/materialOrder")
@RequiredArgsConstructor
public class MaterialOrderQueryFieldController {

    private final MaterialOrderQueryFieldServiceImpl orderQueryFieldService;
    private final MaterialOrderFeignClient orderFeignClient;

    @ApiOperation(value = "审批流条件分支-所有字段")
    @GetMapping("/query/field/activiti/{orderType}")
    public List<QueryConditionDto> activitiQueryField(@PathVariable("orderType") Integer orderType) {
        return orderQueryFieldService.activitiQueryField(orderType);
    }

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all/{orderType}")
    public List<QueryConditionDto> materialOrderAllQueryField(@PathVariable("orderType") Integer orderType) {
        return orderQueryFieldService.orderQueryField(orderType);
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view/{orderType}")
    public List<QueryConditionDto> materialOrderQueryView(@PathVariable("orderType") Integer orderType) {
        return orderQueryFieldService.orderQueryView(orderType);
    }

    @ApiOperation(value = "列表设置-保存")
    @PostMapping("/head/field")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean materialOrderHeadField(@RequestBody @Validated OrderQueryHeadConfigDto config) {
        return orderQueryFieldService.orderHeadField(config);
    }

    @ApiOperation(value = "列表设置-查询")
    @GetMapping("/head/field/{orderType}")
    public QueryHeadConfigDto materialOrderHeadField(@PathVariable("orderType") Integer orderType) {
        return orderQueryFieldService.orderHeadField(orderType);
    }

    @ApiOperation(value = "列表设置-所有字段")
    @GetMapping("/head/all/{orderType}")
    public List<QueryConditionDto> materialOrderAllHeadField(@PathVariable("orderType") Integer orderType) {
        return orderQueryFieldService.orderAllHeadField(orderType);
    }

    @ApiOperation(value = "列表-渲染")
    @GetMapping("/head/view/{orderType}")
    public List<AssetHeadDto> materialOrderHeadView(@PathVariable("orderType") Integer orderType) {
        return orderQueryFieldService.orderHeadView(orderType);
    }

    @ApiOperation(value = "排序字段")
    @GetMapping("/sortField/{orderType}")
    public QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType) {
        return orderFeignClient.sortField(orderType);
    }

}
