package com.niimbot.asset.service.impl;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.model.LoginUser;
import com.niimbot.asset.security.service.SysPermissionService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.exception.category.FeignClientException;
import com.niimbot.jf.core.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/10/27 10:41
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private final SysPermissionService sysPermissionService;
    private final AccountCenterFeignClient accountCenterFeignClient;

    @Autowired
    public UserDetailsServiceImpl(SysPermissionService sysPermissionService, AccountCenterFeignClient accountCenterFeignClient) {
        this.sysPermissionService = sysPermissionService;
        this.accountCenterFeignClient = accountCenterFeignClient;
    }

    @Override
    public UserDetails loadUserByUsername(String account) {
        // 获取账户信息
        CusUserDto cusUser;
        try {
            cusUser = accountCenterFeignClient.getLoginInfoByWay(account);
        } catch (FeignClientException e) {
            // 处理特殊异常
            Result failureResult = e.getFailureResult();
            if (Objects.nonNull(failureResult) && Objects.nonNull(failureResult.getCode()) && failureResult.getCode().equals(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP.getCode())) {
                throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
            }
            if (Objects.nonNull(failureResult) && Objects.nonNull(failureResult.getCode()) && failureResult.getCode().equals(SystemResultCode.COMPANY_FORBIDDEN.getCode())) {
                throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN);
            }
            throw e;
        }
        if (cusUser == null) {
            log.info("登录用户：{} 不存在.", account);
            throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
        }
        // else if (cusUser.getStatus().shortValue() == DictConstant.SYS_DISABLE) {
        //     log.info("登录用户：{} 已被停用.", account);
        //     throw new BusinessException(SystemResultCode.USER_ACCOUNT_FORBIDDEN);
        // } else if (ObjectUtil.isNull(cusUser.getCompanyId())) {
        //     throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_REGISTER);
        // }
        return createLoginUser(cusUser);
    }

    public UserDetails createLoginUser(CusUserDto cusUser) {
        return new LoginUser(cusUser, sysPermissionService.getMenuPermission(cusUser));
    }
}
