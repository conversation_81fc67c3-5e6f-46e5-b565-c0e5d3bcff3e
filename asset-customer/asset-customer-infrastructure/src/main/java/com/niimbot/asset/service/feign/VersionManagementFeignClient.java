package com.niimbot.asset.service.feign;

import com.niimbot.system.VersionManagementDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface VersionManagementFeignClient {

    @GetMapping("/server/system/version/management/latestVersion/{version}/{clientType}")
    VersionManagementDto latestVersion(@PathVariable("version") String version, @PathVariable("clientType") Integer clientType);

    @GetMapping("/server/system/version/management/pc/latestVersionMessage")
    VersionManagementDto latestVersionMessage();

    @PutMapping("/server/system/version/management/pc/versionMessageRead/{id}")
    Boolean versionMessageRead(@PathVariable("id") Long id);
}
