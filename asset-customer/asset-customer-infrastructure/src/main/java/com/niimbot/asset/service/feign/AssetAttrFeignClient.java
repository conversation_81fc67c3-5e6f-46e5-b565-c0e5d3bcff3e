package com.niimbot.asset.service.feign;

import com.niimbot.dynamicform.BizFormAssetConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/12/9 17:06
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetAttrFeignClient {

    /**
     * 资产录入查询的资产固定属性
     *
     * @param isShow 是否显示
     * @return 资产属性列表
     */
    @GetMapping
    @Deprecated
    List<BizFormAssetConfig> listAssetAttr(@RequestParam(value = "isShow", required = false) Boolean isShow);

    /**
     * 查询报表属性下拉
     *
     * @param code 编码
     * @return 资产属性列表
     */
    @GetMapping(value = "server/system/assetAttr/name/{code}")
    String codeToName(@PathVariable("code") String code);

    /**
     * 查询报表属性下拉
     *
     * @return 资产属性列表
     */
    @GetMapping(value = "server/system/assetAttr/reports/attr")
    List<BizFormAssetConfig> reportAttrList();

}
