package com.niimbot.asset.controller.app.todo;

import cn.hutool.core.collection.ListUtil;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.MaterialConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.TodoFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.todo.TodoCountDto;
import com.niimbot.todo.TodoPageQueryDto;
import com.niimbot.todo.TodoWorkCenterDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 待办事项
 *
 * <AUTHOR>
 * @date 2021/7/12 10:29
 */
@Api(tags = "待办事项管理")
@ResultController
@RequestMapping("api/app/todo")
@RequiredArgsConstructor
public class TodoAppController {

    private final TodoFeignClient todoFeignClient;
    private static final List<Integer> APP_EXCLUDE_TYPE = ListUtil.of(AssetConstant.ORDER_TYPE_ASSET_INVENTORY,
            AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE,
            MaterialConstant.ORDER_TYPE_MATERIAL_INVENTORY);

    @ApiOperation(value = "我的待办事项分页查询")
    @GetMapping("/pageMy")
    @AutoConvert
    public PageUtils<TodoWorkCenterDto> pageMy(TodoPageQueryDto query) {
        query.setExcludeOrderType(APP_EXCLUDE_TYPE);
        return todoFeignClient.pageMy(query);
    }

    @ApiOperation(value = "我的待办事数量查询")
    @GetMapping("/count")
    public TodoCountDto getTodoCount() {
        TodoPageQueryDto query = new TodoPageQueryDto();
        query.setExcludeOrderType(APP_EXCLUDE_TYPE);
        return todoFeignClient.getTodoCount(query);
    }

}
