package com.niimbot.asset.service.feign;

import com.niimbot.system.ActiveBroadcastDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11 下午3:37
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ActiveBroadcastFeignClient {

    /**
     * 活动广播列表
     * @return
     */
    @GetMapping(value = "server/system/broadcast/list")
    List<ActiveBroadcastDto> list();

    /**
     * 活动广播统计
     * @return
     */
    @GetMapping(value = "server/system/broadcast/broadcastAction")
    Boolean broadcastAction(@RequestBody Long broadcastId);
}
