package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.service.feign.AsOpsNoticeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.AsOpsNoticeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "【运维通知】")
@ResultController
@RequiredArgsConstructor
@RequestMapping("/api/common/ops/notice")
public class AsOpsNoticeController {

    private final AsOpsNoticeFeignClient opsNoticeFeignClient;

    @ApiOperation("获取近期一条启用的运维通知")
    @GetMapping("/available")
    public AsOpsNoticeDto available() {
        return opsNoticeFeignClient.available();
    }

    @GetMapping("/list")
    public List<AsOpsNoticeDto> list() {
        return opsNoticeFeignClient.list();
    }

}
