package com.niimbot.asset.service;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.annotation.ExcelField;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.MaterialCategoryFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.asset.service.feign.MaterialRepositoryFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.asset.service.impl.MaterialExcelServiceImpl;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.TimeUtil;
import com.niimbot.easydesign.form.dto.clientobject.DateTimeCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.material.MaterialCategoryDto;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.material.MaterialRepositorySearchDto;
import com.niimbot.means.AssetImportDto;

import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/8/31 14:56
 */
@Slf4j
public abstract class AbstractMaterialService {

    protected static final int MAX_BATCH = 5000;
    protected static final String MATERIAL_CODE = "materialCode";
    private static List<String> needSheets = ListUtil.of(AssetConstant.ED_YZC_MATERIAL_CATE);

    @Autowired
    protected MaterialFeignClient materialFeignClient;
    @Autowired
    protected FormFeignClient formFeignClient;
    @Autowired
    protected StandardFeignClient standardFeignClient;
    @Autowired
    protected AsMaterialUtil materialUtil;
    @Autowired
    protected MaterialRepositoryFeignClient materialRepositoryFeignClient;
    @Autowired
    protected MaterialCategoryFeignClient materialCategoryFeignClient;
    @Autowired
    protected OrgFeignClient orgFeignClient;

    @Data
    @Accessors(chain = true)
    protected static class ImportInfo {
        private Long taskId;
        private String fileName;
        private Long fileSize;
        private Long companyId;
        private Long standardId;
        private List<List<Object>> read = new ArrayList<>();
        private Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
    }

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    protected static class IdNameCache {
        private String id;
        private String name;
        private Boolean hasPerm;
    }

    // material-耗材属性，standard-自定义属性
    protected static ThreadLocal<Map<String, List<FormFieldCO>>> formFieldMap = new TransmittableThreadLocal<>();

    protected List<FormFieldCO> getStandardAttr(Long standardId) {
        formFieldMap.set(new HashMap<>());
        // 查询录入标准数据项
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields = formFields.stream()
                .filter(attr -> !attr.isHidden())
                .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                        .contains(attr.getFieldType())).collect(Collectors.toList());
        formFieldMap.get().put("material", formFields);
        // 查询录入的标准品扩展数据
        List<FormFieldCO> standardExtField = new ArrayList<>();
        if (ObjectUtil.isNotNull(standardId)) {
            standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            standardExtField = standardExtField.stream()
                    .filter(attr -> !attr.isHidden())
                    .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                            .contains(attr.getFieldType())).collect(Collectors.toList());
        }
        formFieldMap.get().put("standard", standardExtField);

        List<FormFieldCO> allField = new ArrayList<>();
        allField.addAll(formFields);
        allField.addAll(standardExtField);
        return allField;
    }

    /**
     * 下拉Name和ID映射，Map<字段类型, Map<名称, List<映射>>
     */
    protected static ThreadLocal<Map<String, Map<String, List<IdNameCache>>>> dropDownCache = new TransmittableThreadLocal<>();

    protected void loadSelectCache(String fieldType) {
        if (dropDownCache.get().containsKey(fieldType)) {
            return;
        }
        List<IdNameCache> idNameCaches = new ArrayList<>();
        boolean isMatch = true;
        switch (fieldType) {
            case FormFieldCO.YZC_MATERIAL_CATE:
                List<MaterialCategoryDto> cateAll = materialCategoryFeignClient.all(null);
                for (MaterialCategoryDto categoryDto : cateAll) {
                    IdNameCache idNameCache = new IdNameCache(Convert.toStr(categoryDto.getId()),
                            categoryDto.getCategoryName(), true);
                    IdNameCache idNameCodeCache = new IdNameCache(Convert.toStr(categoryDto.getId()),
                            categoryDto.getCategoryName() + "（" + categoryDto.getCategoryCode() + "）",
                            true);
                    idNameCaches.add(idNameCache);
                    idNameCaches.add(idNameCodeCache);
                }
                break;
            case FormFieldCO.YZC_REPOSITORY:
                List<MaterialRepositoryDto> repoAll = materialRepositoryFeignClient.listAll();
                List<MaterialRepositoryDto> repoPermission = materialRepositoryFeignClient.listPermission(new MaterialRepositorySearchDto());
                Set<Long> repoIdSet = repoPermission.stream().map(MaterialRepositoryDto::getId).collect(Collectors.toSet());
                for (MaterialRepositoryDto repositoryDto : repoAll) {
                    IdNameCache idNameCache = new IdNameCache(Convert.toStr(repositoryDto.getId()),
                            repositoryDto.getName(),
                            repoIdSet.contains(repositoryDto.getId()));
                    IdNameCache idNameCodeCache = new IdNameCache(Convert.toStr(repositoryDto.getId()),
                            repositoryDto.getName() + "（" + repositoryDto.getCode() + "）",
                            repoIdSet.contains(repositoryDto.getId()));
                    idNameCaches.add(idNameCache);
                    idNameCaches.add(idNameCodeCache);
                }
                break;
            default:
                isMatch = false;
                break;
        }
        if (isMatch) {
            Map<String, List<IdNameCache>> collect = idNameCaches.stream().collect(Collectors.groupingBy(IdNameCache::getName));
            dropDownCache.get().put(fieldType, collect);
        }
    }

    protected void dateConvert(AssetImportDto.FieldData fieldData, Object value, String dateFormatType) {
        String dateStr = StrUtil.trim(Convert.toStr(value));
        if (StrUtil.isEmpty(dateStr)) {
            return;
        }
        /*String regex = DateTimeCO.regexMap.get(dateFormatType);*/
        // 导入的日期格式兼容，自动对齐配置的日期格式
        if (value instanceof Date) {
            try {
                Date date = Convert.toDate(value);
                SimpleDateFormat sdf = new SimpleDateFormat(dateFormatType);
                dateStr = sdf.format(date);
            } catch (Exception e) {
                fieldData.getErrMsg().add("值不是日期类型");
                return;
            }
        }

        /*if (regex == null || !dateStr.matches(regex)) {
            fieldData.getErrMsg().add("日期格式仅支持" + dateFormatType);
            return;
        }*/

        String dateStrFmt = TimeUtil.convertDate(dateStr);
        switch (dateFormatType) {
            case DateTimeCO.yyyyMMdd:
                try {
                    LocalDate yyyyMMddParse = LocalDate.parse(dateStrFmt.endsWith("-") ? dateStrFmt.substring(0, dateStrFmt.length() - 1) : dateStrFmt, DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyMMddLong = yyyyMMddParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyMMddLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM-dd，yyyy/MM/dd，yyyy年MM月dd日");
                }
                return;
            case DateTimeCO.yyyyMM:
                try {
                    LocalDate yyyyMMParse = LocalDate.parse(dateStrFmt + (dateStrFmt.endsWith("-") ? "01" : "-01"), DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyMMLong = yyyyMMParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyMMLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM，yyyy/MM，yyyy年MM月");
                }
                return;
            case DateTimeCO.yyyy:
                try {
                    LocalDate yyyyParse = LocalDate.parse(dateStrFmt + (dateStrFmt.endsWith("-") ? "01-01" : "-01-01"), DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyLong = yyyyParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy，yyyy年");
                }
                return;
            case DateTimeCO.yyyyMMddHHmmss:
                try {
                    LocalDateTime parse = LocalDateTime.parse(dateStrFmt, DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMddHHmmss));
                    Long time = parse.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(time);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM-dd HH:mm:ss，yyyy/MM/dd HH:mm:ss，yyyy年MM月dd日 HH时mm分ss秒");
                }
                return;
            default:
                fieldData.getErrMsg().add("不支持的日期格式，例" + dateFormatType);
        }
    }


    @Data
    @AllArgsConstructor
    protected static class CateExport {
        @ExcelField(header = "分类编码", ordinal = 1)
        private String cateCode;
        @ExcelField(header = "分类名称", ordinal = 2)
        private String cateName;
    }

    protected void buildSheet(ExcelWriter writer, List<FormFieldCO> formFields) {
        formFields.stream()
                .filter(f -> needSheets.contains(f.getFieldType()))
                .map(FormFieldCO::getFieldType).distinct().forEach(f -> {
            switch (f) {
                case AssetConstant.ED_YZC_MATERIAL_CATE:
                    writer.setSheet("耗材分类");
                    List<MaterialCategoryDto> cateAll = materialCategoryFeignClient.all(null);
                    LinkedHashMap<String, String> cateHead = ExcelUtils.buildExcelHead(MaterialExcelServiceImpl.CateExport.class);
                    cateHead.forEach(writer::addHeaderAlias);
                    List<CateExport> cateData = cateAll.stream().map(cate ->
                            new CateExport(cate.getCategoryCode(), cate.getCategoryName()))
                            .collect(Collectors.toList());
                    writer.setOnlyAlias(true);
                    writer.write(cateData);
                    writer.autoSizeColumnAll();
                    break;
                default:
                    break;
            }
        });
    }

}
