package com.niimbot.asset.service.feign;

import com.niimbot.activiti.WorkflowCallbackDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2023/2/15 15:59
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ActWorkflowErrorHandleFeignClient {

    @PostMapping("server/means/assetOrder/processCallback")
    Boolean assetOrder(WorkflowCallbackDto callbackDto);

    @PostMapping("server/maintenance/repair/report/processCallback")
    Boolean orderRepairReport(WorkflowCallbackDto callbackDto);

    @PostMapping("server/maintenance/repair/processCallback")
    Boolean orderRepair(WorkflowCallbackDto callbackDto);

    @PostMapping("server/purchase/apply/processApplyCallback")
    Boolean orderPurchase(WorkflowCallbackDto callbackDto);

    @PostMapping("server/purchase/order/processCallback")
    Boolean orderPurchaseOrder(WorkflowCallbackDto callbackDto);

    @PostMapping("server/maintenance/maintain/order/processCallback")
    Boolean orderMaintain(WorkflowCallbackDto callbackDto);

    @PostMapping("server/means/storeOrder/processCallback")
    Boolean orderStore(WorkflowCallbackDto callbackDto);

    @PostMapping("server/material/order/rk/processCallback")
    Boolean orderMaterialRk(WorkflowCallbackDto callbackDto);

    @PostMapping("server/material/order/ck/processCallback")
    Boolean orderMaterialCk(WorkflowCallbackDto callbackDto);

    @PostMapping("server/material/order/ly/processCallback")
    Boolean orderMaterialLy(WorkflowCallbackDto callbackDto);

    @PostMapping("server/material/order/tz/processCallback")
    Boolean orderMaterialTz(WorkflowCallbackDto callbackDto);

    @PostMapping("server/material/order/db/processCallback")
    Boolean orderMaterialDb(WorkflowCallbackDto callbackDto);

    @PostMapping("server/material/order/bs/processCallback")
    Boolean orderMaterialBs(WorkflowCallbackDto callbackDto);
}
