package com.niimbot.asset.service.feign;

import com.niimbot.system.CountryCodeDto;
import com.niimbot.system.CountryCodeQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AsCountryCodeFeignClient {

    @PostMapping(value = "server/system/asCountryCode/list")
    List<CountryCodeDto> list(@RequestBody CountryCodeQueryDto dto);

    @PostMapping(value = "server/system/asCountryCode/save")
    boolean save(@RequestBody CountryCodeQueryDto dto);

    @PostMapping(value = "server/system/asCountryCode/queryCode")
    Integer queryCode(@RequestParam("nationalCode") String nationalCode);
}
