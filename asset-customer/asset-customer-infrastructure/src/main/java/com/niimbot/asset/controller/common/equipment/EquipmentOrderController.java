package com.niimbot.asset.controller.common.equipment;

import com.niimbot.asset.service.feign.equipment.EquipmentOrderFeignClient;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/11 15:46
 */
@Api(tags = "【设备管理】单据管理")
@ResultController
@RequestMapping("api/common/equipment/order")
@RequiredArgsConstructor
public class EquipmentOrderController {

    private final EquipmentOrderFeignClient orderFeignClient;

    @ApiOperation(value = "查询设备单据表单")
    @GetMapping(value = "/form/{orderType}")
    public FormVO getForm(@PathVariable("orderType") Integer orderType) {
        // 查询设置显示的数据
        return orderFeignClient.getForm(orderType);
    }


}
