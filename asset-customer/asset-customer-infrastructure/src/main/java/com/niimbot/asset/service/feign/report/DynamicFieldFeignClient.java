package com.niimbot.asset.service.feign.report;

import com.niimbot.report.DynamicFieldQueryDto;
import com.niimbot.system.QueryConditionDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/27 上午10:10
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface DynamicFieldFeignClient {

    /**
     * 动态表单字段查询
     * @param queryDto
     * @return
     */
    @GetMapping("server/report/dynamic/field/query")
    List<QueryConditionDto> query(@SpringQueryMap @Validated DynamicFieldQueryDto queryDto);

    /**
     * 耗材档案字段
     * @return
     */
    @GetMapping("server/report/dynamic/field/material")
    List<QueryConditionDto> material();
}
