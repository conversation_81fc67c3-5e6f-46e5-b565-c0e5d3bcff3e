package com.niimbot.asset.controller.common.equipment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.EquipmentProjectExcelService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectProjectFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.equipment.CreateEntSntProject;
import com.niimbot.equipment.EntSntProject;
import com.niimbot.equipment.SearchEntSntProject;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckyMultiSheetImportModel;
import com.niimbot.luckysheet.LuckyMultiSheetModel;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.ImportDto;
import com.niimbot.validate.group.Insert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "【设备管理】设备巡检项目")
@Slf4j
@ResultController
@RequiredArgsConstructor
@RequestMapping("api/common/equipment/site/inspect/project")
public class EquipmentSiteInspectProjectController {

    private final EquipmentSiteInspectProjectFeignClient projectFeignClient;
    private final EquipmentProjectExcelService equipmentProjectExcelService;
    private final RedisService redisService;

    @ApiOperation("创建巡检项目")
    @PostMapping("/create")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean create(@RequestBody @Validated(Insert.class) CreateEntSntProject create) {
        create.configDataCheck();
        AuditableOperateResult operateResult = projectFeignClient.create(create);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.ADD_ENT_SNT_PROJECT, operateResult));
        return operateResult.successful();
    }

    @ApiOperation("删除巡检项目")
    @PostMapping("/remove")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> projectIds) {
        List<AuditableOperateResult> operateResults = projectFeignClient.remove(projectIds);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_ENT_SNT_PROJECT, operateResults));
        return CollUtil.isNotEmpty(operateResults);
    }

    @ApiOperation("分页搜索巡检项目")
    @PostMapping("/search")
    @AutoConvert
    public PageUtils<EntSntProject> search(@RequestBody SearchEntSntProject search) {
        return projectFeignClient.search(search);
    }

    @ApiOperation("获取巡检项目最大编码")
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return projectFeignClient.recommendCode();
    }

    @ApiOperation(value = "导出巡检项目模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        equipmentProjectExcelService.exportTemplate(response);
    }

    @ApiOperation(value = "导入巡检项目模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            equipmentProjectExcelService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<LuckyMultiSheetModel> importError(@PathVariable("taskId") Long taskId) {
        return equipmentProjectExcelService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody String sheetModelStr) {
        List<LuckyMultiSheetImportModel> sheetModels = JSONArray.parseArray(sheetModelStr).toJavaList(LuckyMultiSheetImportModel.class);
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        equipmentProjectExcelService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.INSPECT_PROJECT, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.INSPECT_PROJECT));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
