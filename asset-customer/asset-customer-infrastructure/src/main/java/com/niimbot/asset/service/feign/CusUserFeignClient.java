package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.system.AccountInfoDto;
import com.niimbot.system.CusUserDetailDto;
import com.niimbot.system.ResetPasswordDto;
import com.niimbot.system.UserCenterAPPDto;
import com.niimbot.system.UserCenterPCDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/11/4 15:18
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CusUserFeignClient {

    /**
     * 通过手机号查询用户
     *
     * @param mobile 手机号
     * @return 用户
     */
    @GetMapping(value = "server/system/cusUser/getByMobile")
    CusUserDto getByMobile(@RequestParam("mobile") String mobile);

    /**
     * 通过邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户
     */
    @GetMapping(value = "server/system/cusUser/getByEmail")
    CusUserDto getByEmail(@RequestParam("email") String email);

    /**
     * 通过账号更新用户信息
     *
     * @param userDto
     * @return
     */
    @PutMapping(value = "server/system/cusUser/updateByAccount")
    Boolean updateByAccount(CusUserDto userDto);

    /**
     * 根据注册中心uid查询用户
     *
     * @param unionId 注册中心uid
     * @return 结果
     */
    @GetMapping(value = "server/system/cusUser/getByUnionId")
    CusUserDto selectUserByUnionId(@RequestParam("unionId") String unionId);

    /**
     * 修改密码
     *
     * @param dto 修改密码
     * @return 结果
     */
    @PutMapping(value = "server/system/cusUser/reSetPassword")
    Boolean reSetPassword(ResetPasswordDto dto);

    /**
     * 修改app用户中心密码
     *
     * @param newPassword 新密码
     * @return 结果
     */
    @GetMapping("server/system/cusUser/changeAppCenterPassword")
    Boolean changeCurrentUserPassword(@RequestParam("newPassword") String newPassword);

    /**
     * 检查旧密码是否正确
     *
     * @return 密码
     */
    @GetMapping("server/system/cusUser/checkPassword")
    String checkPassword();

    /**
     * 修改手机号
     *
     * @param mobile 手机号
     * @return 结果
     */
    @GetMapping("server/system/cusUser/changeMobile")
    Boolean changeMobile(@RequestParam("mobile") String mobile);

    /**
     * 绑定手机号
     *
     * @param mobile 手机号
     * @return 结果
     */
    @GetMapping("server/system/cusUser/bindMobile")
    Boolean bindMobile(@RequestParam("mobile") String mobile);

    /**
     * 绑定邮箱
     *
     * @param email 邮箱
     * @return 结果
     */
    @GetMapping("server/system/cusUser/bindEmail")
    Boolean bindEmail(@RequestParam("email") String email);

    /**
     * 用户反馈
     *
     * @param dto 数据
     * @return 结果
     */
    @PostMapping("server/system/cusUser/feedback")
    Boolean feedBack(UserCenterAPPDto dto);

    /**
     * 用户详情信息
     *
     * @return 用户登录详情
     */
    @GetMapping(value = "server/system/cusUser/personDetail")
    CusUserDetailDto personDetail();

    /**
     * 二维码邀请员工注册-用户详情信息
     *
     * @return 用户登录详情
     */
    // @GetMapping(value = "server/system/cusUser/userInfo/{userId}")
    // CusUserDetailDto userInfo(@PathVariable("userId") Long userId);

    /**
     * 通过id查询用户
     *
     * @param userId userId
     * @return CusUserDto
     */
    @GetMapping(value = "server/system/cusUser/{userId}")
    CusUserDto getById(@PathVariable("userId") Long userId);


    /**
     * 通过ids获取用户列表
     *
     * @param userIds userIds
     * @return 用户列表
     */
    @PostMapping(value = "server/system/cusUser/getByIds")
    List<CusUserDto> getByIds(List<Long> userIds);


    /** App用户中心接口 */

    /**
     * 获取用户中心数据
     *
     * @return 用户中心数据
     */
    @GetMapping(value = "server/system/cusUser/app/getUserCenterInfo")
    UserCenterAPPDto getUserCenterAPPInfo();


    /**
     * 获取用户中心数据
     *
     * @return 用户中心数据
     */
    @GetMapping(value = "server/system/cusUser/pc/getUserCenterInfo")
    UserCenterPCDto getUserCenterPCInfo();

    /**
     * 登录后置处理
     *
     * @param userId 用户Id
     */
    @PostMapping(value = "server/system/cusUser/loginAfterRecord")
    void loginAfterRecord(Long userId);

    /**
     * PC端用户中心-账号信息
     *
     * @return AccountInfoDto
     */
    @GetMapping(value = "server/system/cusUser/accountInfo")
    AccountInfoDto currentAccountInfo();
}
