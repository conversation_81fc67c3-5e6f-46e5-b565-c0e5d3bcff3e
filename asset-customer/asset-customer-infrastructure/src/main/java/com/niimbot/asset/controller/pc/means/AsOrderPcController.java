package com.niimbot.asset.controller.pc.means;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.AsOrderFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AsOrderAssetQueryDto;
import com.niimbot.means.AsOrderDto;

import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Objects;

import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/1/5 11:24
 */
@Slf4j
@Api(tags = "单据管理")
@ResultController
@RequestMapping("api/pc/assetOrder")
@RequiredArgsConstructor
@CacheConfig(cacheNames = {"global:cache:default"}, cacheManager = "caffeineCacheManager")
@Validated
public class AsOrderPcController {
    private final AsOrderFeignClient orderFeignClient;
    private final AsOrderUtil asOrderUtil;
    private final AsAssetUtil assetUtil;
    private final DictConvertUtil dictConvertUtil;
    private final DesensitizationDataUtil desensitizationDataUtil;

//    @Deprecated
//    @ApiOperation(value = "[已作废]Pc单据分页列表")
//    @GetMapping("/page")
//    public PageUtils<JSONObject> page(AsOrderQueryDto dto) {
//        PageUtils<AsOrderDto> page = orderFeignClient.page(dto);
//        if (Objects.nonNull(page.getList()) && page.getList().size() > 0) {
//            asOrderUtil.translation(page.getList());
//        }
//        dictConvertUtil.convertToDictionary(page.getList());
//        List<JSONObject> list = new ArrayList<>();
//        for (AsOrderDto orderDto : page.getList()) {
//            list.add(asOrderUtil.toJSONObject(orderDto));
//        }
//        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
//    }


    @ApiOperation(value = "单据详情")
    @GetMapping("/detail/{id}")
    public JSONObject getDetailById(@PathVariable Long id) {
        AsOrderDto dto = orderFeignClient.getDetailWithoutAsset(id);
        dictConvertUtil.convertToDictionary(dto);
//        asOrderUtil.translation(Collections.singletonList(dto), false);
        JSONObject jsonObject = asOrderUtil.toJSONObject(dto);
        jsonObject.fluentRemove("assets");
        return jsonObject;
    }

    /**
     * 单据id、资产id获取资产快照详情
     *
     * @param orderId 单据id
     * @param assetId 资产id
     * @return 资产快照详情
     */
    @ApiOperation(value = "单据资产详情接口-参数url?orderId=xx&assetId=xx")
    @GetMapping("/detail/asset")
    @Cacheable(key = "targetClass.simpleName +':' + methodName + ':' + args[0]+':'+args[1]")
    public JSONObject getAssetDetail(@NotNull(message = "单据ID不能为空") Long orderId,
                                     @NotNull(message = "资产ID不能为空") Long assetId) {
        JSONObject assetDetail = orderFeignClient.getAssetDetail(orderId, assetId);
        return assetDetail;
    }

    @ApiOperation(value = "根据单据id获取资产分页数据")
    @GetMapping("/asset/page")
    public PageUtils<JSONObject> getAssetPageByOrderId(AsOrderAssetQueryDto dto) {
        if (ObjectUtil.isNull(dto.getOrderId())) {
            return new PageUtils<>();
        }
        PageUtils<JSONObject> result = orderFeignClient.pageAsset(dto);
        //数据脱敏处理
        if (Objects.nonNull(result) && CollUtil.isNotEmpty(result.getList())) {
            desensitizationDataUtil.handleSensitiveField(result.getList(), SensitiveObjectTypeEnum.ASSET.getCode());
        }
        return result;
    }
}
