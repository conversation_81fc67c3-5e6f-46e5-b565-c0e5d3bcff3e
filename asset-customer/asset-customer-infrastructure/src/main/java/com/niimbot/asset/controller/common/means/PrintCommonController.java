package com.niimbot.asset.controller.common.means;

import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.utils.IOUtils;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.asset.service.feign.TagFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.PrintDataSetDto;
import com.niimbot.means.PrintDataSetTagDto;
import com.niimbot.system.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 打印公共控制器
 *
 * <AUTHOR>
 */
@Api(tags = "标签打印公共管理")
@ResultController
@RequestMapping("api/common/print")
public class PrintCommonController {

    private final PrintFeignClient printFeignClient;

    private final TagFeignClient tagFeignClient;

    private final AbstractFileUtils fileUtils;

    private final FileUploadConfig uploadConfig;

    @Value("${server.port}")
    private String port;

    @Autowired
    public PrintCommonController(PrintFeignClient printFeignClient, TagFeignClient tagFeignClient,
                                 AbstractFileUtils fileUtils, FileUploadConfig uploadConfig) {
        this.printFeignClient = printFeignClient;
        this.tagFeignClient = tagFeignClient;
        this.fileUtils = fileUtils;
        this.uploadConfig = uploadConfig;
    }

    /**
     * 设置默认材质和浓度
     *
     * @param printDataSetDto 设置打印数据dto
     * @return 结果
     */
    @ApiOperation(value = "设置默认材质和浓度")
    // @PostMapping("/setDefaultMaterial")
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT}, value = "/setDefaultMaterial")
    public TagMaterialDto setDefaultMaterial(@RequestBody @Validated PrintDataSetDto printDataSetDto) {
        return printFeignClient.setDefaultMaterial(printDataSetDto);
    }

    /**
     * 材质列表
     *
     * @return 材质列表
     */
    @ApiOperation(value = "获取标签材质列表")
    @GetMapping("/getTagMaterial")
    public List<TagMaterialDto> getTagMaterial(@ApiParam(name = "printerName", value = "打印机名称")
                                               @RequestParam(value = "printerName", required = false, defaultValue = "") String printerName) {
        List<TagMaterialDto> tagMaterial = printFeignClient.getTagMaterial(printerName);
        tagMaterial.forEach(val -> {
            // 补全标签路径
            String photoUrl = val.getPhotoUrl();
            val.setPhotoUrl(fileUtils.convertToDownloadUrl(photoUrl));
        });
        return tagMaterial;
    }

    /**
     * 根据打印设备获取打印默认数据
     *
     * @param printerName 设备名称
     * @return 打印默认数据
     */
    @ApiOperation(value = "获取用户的打印模板")
    @GetMapping("/getPrintTpl")
    public UserTagPrintDto getPrintTpl(
            @ApiParam(name = "printerName", value = "打印机名称")
            @RequestParam(value = "printerName", required = false, defaultValue = "") String printerName,
            @ApiParam(name = "printType", value = "打印类型")
            @NotNull(message = "打印类型不能为空")
            @RequestParam(value = "printType", defaultValue = "1") Short printType) {
        UserTagPrintDto printTpl = printFeignClient.getPrintTpl(printType, printerName);

        // 补全标签路径
        String tagUrl = printTpl.getTagUrl();
        String url = fileUtils.convertToDownloadUrl(tagUrl);
        printTpl.setTagUrl(url);

        // 线上环境远程调用获取不到本地资源
        String replace = url.replace(uploadConfig.getDomain(), "http://127.0.0.1:" + port);
        // 获取远程图片的宽高
        Map<String, Integer> sourceImg = IOUtils.getWidthAndHeight(replace);
        printTpl.setWidth(sourceImg.getOrDefault("width", 0));
        printTpl.setHeight(sourceImg.getOrDefault("height", 0));

        return printTpl;
    }

    /**
     * 设置默认标签模板
     *
     * @return Boolean
     */
    @ApiOperation(value = "设置默认标签模板")
    // @PostMapping("/setDefaultTag")
    @RequestMapping(method = {RequestMethod.PUT, RequestMethod.POST}, value = "/setDefaultTag")
    @ResultMessage("设置默认标签模板成功")
    public Boolean setDefaultTag(@RequestBody @Validated PrintDataSetTagDto printDataSetTagDto) {
        return tagFeignClient.setDefaultTag(printDataSetTagDto);
    }

    /**
     * 获取设备详情--主要给前端提供认证方式
     *
     * @return 设备详情
     */
    @ApiOperation(value = "获取设备详情")
    @GetMapping("/getPrinterDetail")
    public AdminPrinterDto getPrinterDetail(@ApiParam(name = "model", value = "设备型号")
                                            @RequestParam("model") String model) {
        return printFeignClient.getPrinterDetail(model);
    }

    @ApiOperation("获取打印设备内部版本号")
    @GetMapping("/getPrinterInnerModel")
    public List<PrinterInnerModeInfo> getPrinterInnerModel() {
        return printFeignClient.getInnerMode();
    }

    @ApiOperation("获取最大打印驱动信息")
    @GetMapping("/getMaxDriveVersion")
    public MaxDriveVersion getMaxDriveVersion() {
        return printFeignClient.getMaxDriveVersion();
    }
}
