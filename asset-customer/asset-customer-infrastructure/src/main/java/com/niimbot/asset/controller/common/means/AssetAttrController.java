package com.niimbot.asset.controller.common.means;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/12/9 17:39
 */
@Slf4j
@Api(tags = "资产属性管理")
@ResultController
@RequestMapping("api/common/assetAttr")
@Validated
public class AssetAttrController {

    private final FormFeignClient formFeignClient;

    private final AsAssetUtil assetUtil;

    @Autowired
    public AssetAttrController(FormFeignClient formFeignClient, AsAssetUtil assetUtil) {
        this.formFeignClient = formFeignClient;
        this.assetUtil = assetUtil;
    }


    @ApiOperation(value = "查询资产属性")
    @GetMapping(value = "/list")
    public FormVO assetAttr() {
        return formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
    }

    @ApiOperation(value = "查询资产属性")
    @GetMapping(value = "/editBatch/list")
    public FormVO editBatch() {
        FormVO assetVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = assetVO.getFormFields();
        formFields.forEach(f -> f.setRequiredProps(false));
        return assetVO;
    }

    @ApiOperation(value = "查询资产来源下拉列表")
    @GetMapping(value = "/assetOrigin")
    public Object assetOrigin() {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        FormFieldCO assetOriginField = assetUtil.getFieldPropsByCode(formVO, "assetOrigin");
        if (Objects.isNull(assetOriginField) || Objects.isNull(assetOriginField.getFieldProps())) {
            return null;
        }
        return assetOriginField.getFieldProps().get("values");
    }

}
