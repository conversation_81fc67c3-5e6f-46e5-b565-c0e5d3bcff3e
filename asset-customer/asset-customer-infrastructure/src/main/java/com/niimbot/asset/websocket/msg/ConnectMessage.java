package com.niimbot.asset.websocket.msg;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 连接事件
 *
 * <AUTHOR>
 * @date 2021/9/1 10:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class ConnectMessage extends Message {
    private static final long serialVersionUID = 6750981085799473821L;

    public ConnectMessage() {
        super(Message.TYPE_CONNECT);
    }

    private Boolean isSuccess;
    private String msg;

}
