package com.niimbot.asset.controller.common.system;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import com.niimbot.asset.service.feign.IndustryFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.IndustryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 行业获取
 *
 * <AUTHOR>
 */
@Api(tags = {"行业接口"})
@ResultController
@RequestMapping("api/common/industry")
public class IndustryController {

    @Autowired
    private IndustryFeignClient industryFeignClient;

    /**
     * @return 行业列表
     */
    @ApiOperation(value = "查询行业树")
    @GetMapping("/tree")
    public List<Tree<String>> list() {
        List<IndustryDto> list = industryFeignClient.listIndustry();
        // 构建树结构
        return TreeUtil.build(list, "0", (industry, tree) -> {
            tree.setId(Convert.toStr(industry.getId()));
            tree.setParentId(Convert.toStr(industry.getPid()));
            tree.setName(industry.getIndustryName());
            tree.setWeight(industry.getSortNum());
            tree.putExtra("title", industry.getIndustryName());
            tree.putExtra("value", Convert.toStr(industry.getId()));
            tree.putExtra("level", industry.getLevel());
        });
    }
}