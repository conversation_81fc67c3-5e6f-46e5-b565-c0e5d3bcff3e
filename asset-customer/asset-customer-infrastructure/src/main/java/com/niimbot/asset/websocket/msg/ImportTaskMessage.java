package com.niimbot.asset.websocket.msg;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/9/1 18:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class ImportTaskMessage extends Message {
    private static final long serialVersionUID = 6330555425872236660L;

    private Integer importType;

    private String importTypeText;

    private Map<Object, Object> importList = new HashMap<>();

    public ImportTaskMessage() {
        super(Message.TYPE_IMPORT_TASK);
    }

    public ImportTaskMessage(Integer importType, String importTypeText, Map<Object, Object> importList) {
        this();
        this.importType = importType;
        this.importTypeText = importTypeText;
        this.importList = importList;
    }
}
