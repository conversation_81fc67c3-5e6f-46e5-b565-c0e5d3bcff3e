package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.AbstractOrderQueryFieldService;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentOrderFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/11 16:05
 */
@Service
public class EquipmentOrderQueryFieldServiceImpl extends AbstractOrderQueryFieldService {

    @Autowired
    private EquipmentOrderFeignClient orderFeignClient;

    @Override
    protected void specialOrderQueryField(Integer orderType, List<QueryConditionDto> queryConditionDtos) {
        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_PLAN == orderType) {
            // 补充固定字段 编号 状态 范围
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_NO, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_STATUS, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_SR_PR_COUNT, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_ENT_LIST, orderType));
        }
        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_SITE_INSPECT_PLAN == orderType) {
            // 查询 补充固定字段 编号 更新时间 已下发任务次数
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_NO, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_ISSUED_TASK_NUM, orderType));
        }
        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK == orderType) {
            // 补齐 状态, 保养任务编码, 保养负责人，保养计划编码，计划名称，保养用时，计划应开始时间
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_TASK_NO, orderType));
            FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);
            QueryConditionDto maintainUser = toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_MAINTAIN_USER, orderType);
            if (maintainUser != null) {
                maintainUser.setFieldProps(empField.getFieldProps());
                queryConditionDtos.add(maintainUser);
            }
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_TASK_MAINTAIN_DURATION, orderType));
            QueryConditionDto planStartTime = toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_TASK_PLAN_START_TIME, orderType);
            planStartTime.getFieldProps().put("dateFormatType", "yyyy-MM-dd HH:mm:ss");
            queryConditionDtos.add(planStartTime);

            // 补齐资产表单数据
            FormVO assetFormVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
            for (FormFieldCO formFieldCO : assetFormVO.getFormFields()) {
                if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                        .contains(formFieldCO.getFieldType())) {
                    continue;
                }
                if (formFieldCO.isHidden()) {
                    continue;
                }
                QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
                queryConditionDtos.add(queryConditionDto);
            }
        }
    }

    @Override
    protected List<QueryConditionDto> allHeadField(Integer orderType, FormVO formVO) {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();
        List<FormFieldCO> formFields = formVO.getFormFields();

        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK == orderType) {
            // 补齐资产表单数据
            FormVO assetFormVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
            formFields.addAll(assetFormVO.getFormFields());
        }

        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }

        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_PLAN == orderType) {
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_NO, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_STATUS, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_SR_PR_COUNT, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_ENT_LIST, orderType));
        }

        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_SITE_INSPECT_PLAN == orderType) {
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_NO, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_STATUS, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_RANGE_LIST, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_ISSUED_TASK_NUM, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME, orderType));
        }

        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK == orderType) {
            // 补齐 状态, 保养任务编码, 保养负责人，保养计划编码，计划名称，保养用时，计划应开始时间
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_TASK_STATUS, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_TASK_NO, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_MAINTAIN_USER, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_NO, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_PLAN_NAME, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_TASK_MAINTAIN_DURATION, orderType));
            queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_MAINTAIN_TASK_PLAN_START_TIME, orderType));
        }

        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY, orderType));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME, orderType));

        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    @Override
    public List<QueryConditionDto> activitiQueryField(Integer orderType) {
        return new ArrayList<>();
    }

    @Override
    public List<QueryConditionDto> orderQueryView(Integer orderType) {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllQueryField = orderQueryField(orderType);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.EQUIPMENT_MAINTAIN_ORDER_TYPE_QUERY.get(orderType));
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });
        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_PLAN == orderType) {
            result.removeIf(v -> QueryFieldConstant.FIELD_MAINTAIN_PLAN_STATUS.equals(v.getCode()) || QueryFieldConstant.FIELD_MAINTAIN_PLAN_ENT_LIST.equals(v.getCode()) || QueryFieldConstant.FIELD_MAINTAIN_PLAN_SR_PR_COUNT.equals(v.getCode()));
        }
        return result;
    }

    @Override
    public Boolean orderHeadField(OrderQueryHeadConfigDto config) {
        return queryConditionConfigFeignClient.saveOrUpdate(
                new QueryConditionConfigDto(config.toJson(), QueryFieldConstant.EQUIPMENT_MAINTAIN_ORDER_TYPE_HEAD.get(config.getOrderType())));
    }

    @Override
    public QueryHeadConfigDto orderHeadField(Integer orderType) {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(
                QueryFieldConstant.EQUIPMENT_MAINTAIN_ORDER_TYPE_HEAD.get(orderType));
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(QueryHeadConfigDto.class);
    }

    @Override
    public List<AssetHeadDto> orderHeadView(Integer orderType) {
        QueryConditionSortDto querySort = orderFeignClient.sortField(orderType);
        List<String> querySortList = querySort.getSortList().stream()
                .map(QueryConditionSortDto.Field::getValue).collect(Collectors.toList());

        List<QueryConditionDto> result = new ArrayList<>();
        FormVO formVO = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<QueryConditionDto> assetAllHeadField = allHeadField(orderType, formVO);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllHeadField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.EQUIPMENT_MAINTAIN_ORDER_TYPE_HEAD.get(orderType));
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        List<String> selectConditions = generalDto.getConditions();
        selectConditions.forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });

        Map<String, String> transCodeMap = new HashMap<>();
        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK == orderType) {
            transCodeMap.put(QueryFieldConstant.FIELD_MAINTAIN_TASK_STATUS, QueryFieldConstant.FIELD_MAINTAIN_TASK_STATUS + "Text");
            transCodeMap.put(QueryFieldConstant.FIELD_MAINTAIN_PLAN_MAINTAIN_USER, QueryFieldConstant.FIELD_MAINTAIN_PLAN_MAINTAIN_USER + "Text");
        }
        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_PLAN == orderType) {
            transCodeMap.put(QueryFieldConstant.FIELD_MAINTAIN_PLAN_STATUS, QueryFieldConstant.FIELD_MAINTAIN_PLAN_STATUS + "Text");
            // transCodeMap.put(QueryFieldConstant.FIELD_MAINTAIN_PLAN_RANGE_TYPE, QueryFieldConstant.FIELD_MAINTAIN_PLAN_RANGE_TYPE + "Text");
        }
        if (EquipmentConstant.ORDER_TYPE_EQUIPMENT_SITE_INSPECT_PLAN == orderType) {
            transCodeMap.put(QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_STATUS, QueryFieldConstant.FIELD_SITE_INSPECT_PLAN_STATUS + "Text");
        }
        transCodeMap.put(QueryFieldConstant.FIELD_CREATE_BY, QueryFieldConstant.FIELD_CREATE_BY + "Text");
        Map<String, JSONObject> fieldPropsMap = new HashMap<>();
        JSONObject dateFormat = new JSONObject();
        dateFormat.put("dateFormatType", "yyyy-MM-dd");
        fieldPropsMap.put(QueryFieldConstant.FIELD_CREATE_TIME, dateFormat);
        JSONObject planStartTimeProps = new JSONObject();
        planStartTimeProps.put("dateFormatType", "yyyy-MM-dd HH:mm:ss");
        fieldPropsMap.put(QueryFieldConstant.FIELD_MAINTAIN_TASK_PLAN_START_TIME, planStartTimeProps);
        for (FormFieldCO formFieldCO : formVO.getFormFields()) {
            fieldPropsMap.put(formFieldCO.getFieldCode(), formFieldCO.getFieldProps());
            if (StrUtil.isNotEmpty(formFieldCO.getTranslationCode())) {
                transCodeMap.put(formFieldCO.getFieldCode(), formFieldCO.getTranslationCode());
            }
        }

        List<AssetHeadDto> headList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            QueryConditionDto conditionDto = result.get(i);
            AssetHeadDto headDto = new AssetHeadDto();
            headDto.setCode(conditionDto.getCode());
            headDto.setName(conditionDto.getName());
            headDto.setType(conditionDto.getType());
            headDto.setIsLock(false);
            headDto.setFieldProps(fieldPropsMap.get(conditionDto.getCode()));
            headDto.setSort(querySortList.contains(conditionDto.getCode()));
            String tranCode = transCodeMap.get(conditionDto.getCode());
            if (FormFieldCO.NUMBER_INPUT.equals(conditionDto.getType())
                    && "2".equals(conditionDto.getFieldProps().getString("numberFormatType"))) {
                tranCode = "%";
            }
            headDto.setTranslationCode(
                    StrUtil.isNotEmpty(tranCode) ? tranCode : "");
            if (i < generalDto.getLockNum()) {
                headDto.setIsLock(true);
            }
            headList.add(headDto);
        }
        return headList;
    }

    protected QueryConditionDto toQueryConditionDto(String code, Integer orderType) {
        QueryFieldConstant.Field field = null;
        if (OrderFormTypeEnum.EQUIPMENT_MAINTAIN_TASK.getCode() == orderType) {
            field = QueryFieldConstant.EQUIPMENT_TASK_EXT_FIELD.get(code);
        } else if (OrderFormTypeEnum.EQUIPMENT_MAINTAIN_PLAN.getCode() == orderType) {
            field = QueryFieldConstant.EQUIPMENT_PLAN_EXT_FIELD.get(code);
        } else if (OrderFormTypeEnum.EQUIPMENT_SITE_INSPECT_PLAN.getCode() == orderType) {
            field = QueryFieldConstant.EQUIPMENT_SITE_INSPECT_PLAN_EXT_FIELD.get(code);
        }
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

}
