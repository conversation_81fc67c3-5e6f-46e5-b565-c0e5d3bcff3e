package com.niimbot.asset.controller.common.system;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.CusEmployeeChangeFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.AsEmployeeResignRecordsDto;
import com.niimbot.system.CusEmployeeChangeDto;
import com.niimbot.system.CusEmployeeChangeQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/16 11:10
 */
@Api(tags = "员工异动管理")
@ResultController
@RequestMapping("api/common/employee/change")
@Validated
public class CusEmployeeChangeController {

    private final CusEmployeeChangeFeignClient employeeChangeFeignClient;

    private final FormFeignClient formFeignClient;

    private final AsAssetUtil assetUtil;

    @Autowired
    public CusEmployeeChangeController(CusEmployeeChangeFeignClient employeeChangeFeignClient,
                                       FormFeignClient formFeignClient,
                                       AsAssetUtil assetUtil) {
        this.employeeChangeFeignClient = employeeChangeFeignClient;
        this.formFeignClient = formFeignClient;
        this.assetUtil = assetUtil;
    }

    @ApiOperation(value = "员工异动分页列表")
    @AutoConvert
    @GetMapping(value = "/page")
    public PageUtils<CusEmployeeChangeDto> page(CusEmployeeChangeQueryDto query) {
        return employeeChangeFeignClient.page(query);
    }

    @ApiOperation(value = "查看异动资产")
    @GetMapping(value = "/asset")
    public PageUtils<JSONObject> changeAsset(CusEmployeeChangeQueryDto query) {
        PageUtils<JSONObject> jsonObjectPage = employeeChangeFeignClient.changeAsset(query);
        List<JSONObject> list = jsonObjectPage.getList();
        List<JSONObject> assetDataList = new ArrayList<>();
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        for (JSONObject json : list) {
            assetUtil.translateAssetJson(json, formVO.getFormFields());
            assetDataList.add(json);
        }
        jsonObjectPage.setList(assetDataList);
        return jsonObjectPage;
    }

    @ApiOperation(value = "查看删除操作记录")
    @AutoConvert
    @GetMapping(value = "/operation/{changeId}")
    public List<AsEmployeeResignRecordsDto> operation(@PathVariable("changeId") Long changeId) {
        return employeeChangeFeignClient.operation(changeId);
    }
}
