package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.thirdparty.DingCallbackDto;
import com.niimbot.thirdparty.FeishuCallbackDto;
import com.niimbot.thirdparty.WeChatCallbackDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/9 10:01
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ThirdpartyCallbackFeignClient {

    /**
     * 钉钉回调
     *
     * @param dingCallback dto
     * @return 结果
     */
    @PostMapping(value = "server/thirdparty/callback/dingtalk")
    Map<String, String> dingCallback(DingCallbackDto dingCallback);

    /**
     * 微信回调
     *
     * @param wechatCallbackDto dto
     * @return 结果
     */
    @GetMapping(value = "server/thirdparty/callback/wechat")
    String wechatCallbackGet(@SpringQueryMap WeChatCallbackDto wechatCallbackDto);

    /**
     * 微信回调
     *
     * @param wechatCallbackDto dto
     * @return 结果
     */
    @PostMapping(value = "server/thirdparty/callback/wechat")
    String wechatCallbackPost(WeChatCallbackDto wechatCallbackDto);

    @GetMapping(value = "server/thirdparty/callback/wechatCallRequest")
    String wechatCallGetRequest(@SpringQueryMap WeChatCallbackDto wechatCallbackDto);

    @PostMapping(value = "server/thirdparty/callback/wechatCallRequest")
    String wechatCallPostRequest(@RequestBody WeChatCallbackDto wechatCallbackDto);

    @PostMapping(value = "server/thirdparty/callback/feishu")
    JSONObject feishuCallback(@RequestBody FeishuCallbackDto feishuCallbackDto);
}
