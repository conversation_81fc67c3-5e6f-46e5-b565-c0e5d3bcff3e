package com.niimbot.asset.controller.common.thirdparty;

import com.niimbot.asset.service.feign.SyncChangeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.OrgDto;
import com.niimbot.system.RemoveEmployDto;
import com.niimbot.thirdparty.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/19 13:54
 */
@Api(tags = "【三方对接】员工异动记录")
@ResultController
@RequestMapping("api/common/thirdparty/syncChange")
@Validated
public class AsSyncChangeController {

    private final SyncChangeFeignClient syncChangeFeignClient;

    @Autowired
    public AsSyncChangeController(SyncChangeFeignClient syncChangeFeignClient) {
        this.syncChangeFeignClient = syncChangeFeignClient;
    }

    @ApiOperation(value = "员工异动记录")
    @GetMapping("/list")
    public List<SyncChangeDto> list(@ApiParam(name = "type", value = "异动类型（1-编辑员工，2-删除员工，3-组织异动）")
                                    @RequestParam("type") Integer type,
                                    @ApiParam(name = "status", value = "状态（1-待处理，2-已处理）")
                                    @RequestParam("status") Integer status) {
        return syncChangeFeignClient.list(type, status);
    }

    @ApiOperation(value = "异动员工信息")
    @GetMapping("/emp/{id}")
    public SyncChangeEmpDto getEmp(@PathVariable("id") Long id) {
        return syncChangeFeignClient.getEmp(id);
    }

    @ApiOperation(value = "更换组织的员工转移资产")
    @PostMapping("/emp/transfer/edit")
    public Boolean transferEmp(@RequestBody @Validated EmpTransferDto empTransferDto) {
        return syncChangeFeignClient.transferEmpEdit(empTransferDto);
    }

    @ApiOperation(value = "被删除的员工转移资产")
    @PostMapping("/emp/transfer/delete/{id}")
    public Boolean transferEmpDelete(@PathVariable("id") Long id, @RequestBody @Validated RemoveEmployDto employ) {
        return syncChangeFeignClient.transferEmpDelete(id, employ);
    }

    @ApiOperation(value = "组织删除转移")
    @PostMapping("/org/transfer/delete")
    public Boolean transferOrgDelete(@RequestBody @Validated OrgTransferDto orgTransferDto) {
        return syncChangeFeignClient.transferOrgDelete(orgTransferDto);
    }

    @ApiOperation(value = "异动组织信息")
    @GetMapping("/org/{id}")
    public OrgDto getOrg(@PathVariable("id") Long id) {
        return syncChangeFeignClient.getOrg(id);
    }

    @ApiOperation(value = "组织员工异动数量")
    @GetMapping("/count")
    public SyncChangeCountDto count() {
        return syncChangeFeignClient.count();
    }

}
