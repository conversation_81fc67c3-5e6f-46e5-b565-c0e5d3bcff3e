package com.niimbot.asset.controller.common.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.autoconfig.OssPropertiesConfig;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.FileUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.model.OssCallbackResult;
import com.niimbot.asset.model.OssPolicyResult;
import com.niimbot.asset.service.OssService;
import com.niimbot.asset.service.feign.FileFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.result.Result;
import com.niimbot.system.AsFileDownDto;
import com.niimbot.system.AsFileDto;
import com.niimbot.system.FileParamDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * PC端文件控制器
 *
 * <AUTHOR>
 * @Date 2020/12/1
 */
@Api(tags = {"客户端文件相关接口"})
@Controller
@RequestMapping("api/common/file")
@Slf4j
@Validated
@RequiredArgsConstructor
public class CustomFileController {

    private final FileUploadConfig uploadConfig;

    private final FileFeignClient fileFeignClient;

    private final OssPropertiesConfig ossPropertiesConfig;

    private final AbstractFileUtils fileUtils;

    private final OssService ossService;

    private final RedisService redisService;

    @ApiOperation(value = "获取token")
    @GetMapping("/policy")
    @ResponseBody
    public Result policy(@ApiParam(name = "module", value = "模块名称")
                                  @RequestParam(value = "module") String module,
                                  @ApiParam(name = "business", value = "业务名称")
                                  @RequestParam(value = "business", required = false) String business,
                                  @ApiParam(name = "dirPattern", value = "文件夹生成后缀，如yyyy-MM-dd")
                                  @RequestParam(value = "dirPattern", required = false, defaultValue = "") String dirPattern) {
        try {
            String filePath = (Edition.isWeixin() ? Edition.WEIXIN + File.separator : StrUtil.EMPTY) + module + File.separator + business;
            OssPolicyResult policy = ossService.policy(
                    ossPropertiesConfig.getBucket(),
                    ossPropertiesConfig.getExpire(),
                    filePath,
                    dirPattern
            );
            return Result.ofSuccess(policy);
        } catch (Exception e) {
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "获取Oss policy失败");
        }
    }

    @PostMapping("/callback")
    public OssCallbackResult callback(HttpServletRequest request) {
        return ossService.callback(request);
    }

    @ApiOperation(value = "单文件上传")
    @PostMapping(value = "/upload/single", consumes = "multipart/form-data")
    @ResponseBody
    public Result uploadSingle(@RequestParam(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file,
                               FileParamDto fileParamDto) {
        List<AsFileDto> fileDtoList = fileFeignClient.uploadFile(new MultipartFile[]{file}, fileParamDto);
        String urlPrefix;
        if (fileParamDto.getIsFile()) {
            urlPrefix = AbstractFileUtils.CUSTOMER_URL_FILE_PREFIX;
        } else {
            urlPrefix = AbstractFileUtils.CUSTOMER_URL_PREFIX;
        }
        fileDtoList.forEach(dto -> {
            dto.setUrl(urlPrefix + dto.getId());
            dto.setHost(fileUtils.getHost());
        });
        return Result.ofSuccess(fileDtoList.get(0));
    }

    @ApiOperation(value = "单文件上传(不需要token)")
    @PostMapping(value = "/upload/single/{authCode}", consumes = "multipart/form-data")
    @ResponseBody
    public Result uploadSingleNoToken(@RequestParam(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file,
                                      @PathVariable("authCode") String authCode,
                                      FileParamDto fileParamDto) {
        // 校验
        String cacheKey = "signAuthCode:" + authCode;
        Map<Object, Object> dataObj = redisService.hGetAll(cacheKey);
        if (dataObj == null || CollUtil.isEmpty(dataObj)
                || BooleanUtil.isTrue(Convert.toBool(dataObj.get("expire")))) {
            throw new BusinessException(ActivitiResultCode.WORKFLOW_COMMON_ERROR, "临时授权码已失效");
        }
        List<AsFileDto> fileDtoList = fileFeignClient.uploadFile(new MultipartFile[]{file}, fileParamDto);
        String urlPrefix;
        if (fileParamDto.getIsFile()) {
            urlPrefix = AbstractFileUtils.CUSTOMER_URL_FILE_PREFIX;
        } else {
            urlPrefix = AbstractFileUtils.CUSTOMER_URL_PREFIX;
        }
        fileDtoList.forEach(dto -> {
            dto.setUrl(urlPrefix + dto.getId());
            dto.setHost(fileUtils.getHost());
        });
        return Result.ofSuccess(fileDtoList.get(0));
    }

    @ApiOperation(value = "指定文件id下载")
    @GetMapping("/download/{id}")
    public void fileDownload(@PathVariable Long id,
                             @RequestParam(required = false, defaultValue = "false") Boolean delete,
                             HttpServletResponse response, HttpServletRequest request) {
        AsFileDownDto fileDto = fileFeignClient.feignFileDownload(id, delete);
        FileUtils.writeBytesToAttachment(request, response, fileDto.getOriginalName(), fileDto.getContent());
    }

    @ApiOperation(value = "获取指定图片、文件")
    @GetMapping("{id}")
    public String getById(@PathVariable("id") String id) {
        Long convert = Convert.convert(Long.class, id);
        AsFileDownDto fileDto = fileFeignClient.feignFileDownload(convert, null);
        String persistName = fileDto.getPersistName();
        String idFilePath = uploadConfig.getPath() + "/local/" + persistName;
        if (!FileUtils.exist(idFilePath)) {
            FileUtils.writeBytes(fileDto.getContent(), idFilePath);
        }
        return StringUtils.concat(true, BaseConstant.RESOURCE_PREFIX, "/local/", persistName);
    }

}
