package com.niimbot.asset.controller.pc.finance;

import cn.hutool.core.collection.CollUtil;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.finance.AssetFinanceSettleFeignClient;
import com.niimbot.finance.*;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/15 下午7:29
 */
@Api(tags = "折旧方案配置")
@ResultController
@RequestMapping("api/finance/settle/")
@RequiredArgsConstructor
public class AssetFinanceSettleController {

    private final AssetFinanceSettleFeignClient financeSettleFeignClient;

    @ApiOperation("折旧结账列表")
    @GetMapping("billInfo")
    public PageUtils<AssetBillInfoDto> billInfo(AssetBillInfoQueryDto queryDto) {
        return financeSettleFeignClient.billInfo(queryDto);
    }

    @ApiOperation("查询企业结算会计期间信息")
    @GetMapping("settleDateInfo")
    public OrgSettleDateInfoDto querySettleDateInfo(Long orgId) {
        return financeSettleFeignClient.querySettleDateInfo(orgId);
    }

    @ApiOperation("折旧计提")
    @PostMapping("accrual")
    public AssetAccrualDto depreciationAccrual(@RequestBody @Validated AssetAccrualSubmitDto accrualSubmitDto) {
        return financeSettleFeignClient.depreciationAccrual(accrualSubmitDto);
    }

    @ApiOperation("确认折旧计提")
    @PostMapping("confirmAccrual")
    public Boolean confirmAccrual(@RequestBody @Validated AssetAccrualSubmitDto accrualSubmitDto) {
        return financeSettleFeignClient.confirmAccrual(accrualSubmitDto);
    }

    @ApiOperation("折旧明细分页查询")
    @PostMapping("settleDetail")
    public PageUtils<SettleDetailDto> settleDetail(@RequestBody @Validated SettleDetailQueryDto queryDto) {
        return financeSettleFeignClient.settleDetail(queryDto);
    }

    @ApiOperation("折旧明细导出")
    @PostMapping("settleDetailExport")
    public void settleDetailExport(@RequestBody @Validated SettleDetailQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SettleDetailDto.class);
            // 查询数据
            PageUtils<SettleDetailDto> details = financeSettleFeignClient.settleDetail(query);
            List<SettleDetailDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "折旧明细-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getPageNum() > 1) {
                fileName += "-" + query.getPageNum();
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("折旧汇总分页查询--所属公司")
    @PostMapping("settleSummary")
    public PageUtils<SettleSummaryDto> settleSummary(@RequestBody @Validated SettleSummaryQueryDto queryDto) {
        return financeSettleFeignClient.settleSummary(queryDto);
    }

    @ApiOperation("折旧汇总分页查询--分摊部门")
    @PostMapping("settleSummaryDept")
    public PageUtils<SettleSummaryDeptDto> settleSummaryDept(@RequestBody @Validated SettleSummaryQueryDto queryDto) {
        return financeSettleFeignClient.settleSummaryDept(queryDto);
    }

    @ApiOperation("折旧汇总导出--所属公司")
    @PostMapping("settleSummaryExport")
    public void settleSummaryExport(@RequestBody @Validated SettleSummaryQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SettleSummaryDto.class);
            // 查询数据
            PageUtils<SettleSummaryDto> details = financeSettleFeignClient.settleSummary(query);
            List<SettleSummaryDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "折旧汇总-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getPageNum() > 1) {
                fileName += "-" + query.getPageNum();
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("折旧汇总导出--分摊部门")
    @PostMapping("settleSummaryDeptExport")
    public void settleSummaryDeptExport(@RequestBody @Validated SettleSummaryQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SettleSummaryDeptDto.class);
            // 查询数据
            PageUtils<SettleSummaryDeptDto> details = financeSettleFeignClient.settleSummaryDept(query);
            List<SettleSummaryDeptDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "折旧汇总-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getPageNum() > 1) {
                fileName += "-" + query.getPageNum();
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("查询企业会计期间")
    @GetMapping("queryOrgSettleMonth")
    public List<String> queryOrgSettleMonth(SettleMonthQueryDto queryDto) {
        return financeSettleFeignClient.queryOrgSettleMonth(queryDto);
    }
}
