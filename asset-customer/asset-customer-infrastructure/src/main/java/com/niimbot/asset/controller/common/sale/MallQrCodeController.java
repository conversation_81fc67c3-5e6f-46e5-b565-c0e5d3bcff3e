package com.niimbot.asset.controller.common.sale;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.service.http.NiimbotShopHttpClient;
import com.niimbot.asset.utils.QRCodeUtil;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 购物 动态二维码
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Api(tags = "商城购物")
@RequestMapping("/api/sale/mallqrcode")
@RequiredArgsConstructor
@ResultController
@Slf4j
public class MallQrCodeController {

    private final NiimbotShopHttpClient niimbotShopHttpClient;

    /**
     * 根据 url 生成 普通二维码
     */
    @ApiOperation(value = "商城购物动态二维码 商品SN")
    @GetMapping("/createQRCode/{sn}")
    public void createQRCode(HttpServletResponse response, @PathVariable("sn") String sn) throws Exception {

        /**
         * 只有精臣的才需要去进行鉴权，并拿到跳转的URL
         */
        JSONObject obj = niimbotShopHttpClient.getGoodsUrl(sn);
        String url = obj.getString("data");
        if (StrUtil.isNotBlank(url)) {
            ServletOutputStream stream = null;
            try {
                stream = response.getOutputStream();
                //使用工具类生成二维码
                QRCodeUtil.encode(url, stream);
            } catch (Exception e) {
                e.getStackTrace();
            } finally {
                if (stream != null) {
                    stream.flush();
                    stream.close();
                }
            }
        } else {
            log.error("获取URL失败");
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取URL失败");
        }
    }

}
