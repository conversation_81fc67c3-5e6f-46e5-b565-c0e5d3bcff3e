package com.niimbot.asset.controller.app.system;

import cn.hutool.core.util.ObjectUtil;
import com.niimbot.asset.framework.constant.SourceEnum;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.service.feign.VersionManagementFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.VersionManagementDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Api("【APP端版本管理】")
@ResultController
@RequestMapping("/api/app/version/management")
@RequiredArgsConstructor
public class VersionManagementController {

    private final AbstractFileUtils fileUtils;

    private final VersionManagementFeignClient versionManagementFeignClient;

    @ApiOperation("获取最新版本")
    @GetMapping("/latestVersion/{version}")
    public VersionManagementDto latestVersion(@PathVariable String version) {
        Integer clientType = ServletUtils.getClientSource().getValue();
        if (SourceEnum.PDA.getValue().equals(clientType)) {
            clientType = SourceEnum.ANDROID.getValue();
        }
        VersionManagementDto dto = versionManagementFeignClient.latestVersion(version, clientType);
        if (ObjectUtil.isNotNull(dto)) {
            dto.setPath(fileUtils.convertToDownloadUrl(dto.getPath()));
        }
        return dto;
    }

}
