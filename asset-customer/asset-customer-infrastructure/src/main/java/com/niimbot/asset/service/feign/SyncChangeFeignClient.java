package com.niimbot.asset.service.feign;

import com.niimbot.system.OrgDto;
import com.niimbot.system.RemoveEmployDto;
import com.niimbot.thirdparty.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface SyncChangeFeignClient {

    @GetMapping("server/thirdparty/syncChange/list")
    List<SyncChangeDto> list(@RequestParam("type") Integer type, @RequestParam("status") Integer status);

    @GetMapping("server/thirdparty/syncChange/emp/{id}")
    SyncChangeEmpDto getEmp(@PathVariable("id") Long id);

    @GetMapping("server/thirdparty/syncChange/org/{id}")
    OrgDto getOrg(@PathVariable("id") Long id);

    @PostMapping("server/thirdparty/syncChange/emp/transfer/edit")
    Boolean transferEmpEdit(EmpTransferDto empTransferDto);

    @PostMapping("server/thirdparty/syncChange/emp/transfer/remove/{id}")
    Boolean transferEmpDelete(@PathVariable("id") Long id, RemoveEmployDto employ);

    @PostMapping("server/thirdparty/syncChange/org/transfer/remove")
    Boolean transferOrgDelete(OrgTransferDto orgTransferDto);

    @GetMapping("server/thirdparty/syncChange/count")
    SyncChangeCountDto count();

}
