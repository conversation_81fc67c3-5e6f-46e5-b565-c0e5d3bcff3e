package com.niimbot.asset.service;

import com.google.common.collect.Lists;

import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;

import org.springframework.data.redis.core.ValueOperations;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;

/**
 * 权限变化服务
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
public abstract class AbstractPermissionChangedService extends AbstractLoginService {

    private final RedisService redisService;

    private final String supportFlag;

    protected AbstractPermissionChangedService(RedisService redisService,
                                               @NonNull String supportFlag) {
        super(redisService);
        this.redisService = redisService;
        this.supportFlag = supportFlag;
    }

    public final void permissionChange(@NonNull List<CusUserDto> list) {
        change(list, redisService);
    }

    /**
     * 是否支持
     *
     * @param supportFlags role、kickOff、dataScope
     * @return 是否支持
     */
    protected boolean support(String... supportFlags) {
        return ObjectUtil.isNotNull(supportFlags) && Arrays.asList(supportFlags).contains(supportFlag);
    }

    /**
     * redis操作
     */
    protected abstract void change(List<CusUserDto> list, RedisService redisService);

    @Service("rolePermissionChangedService")
    public static class RolePermissionChangedService extends AbstractPermissionChangedService {

        public static final String SUPPORT_FLAG = "role";

        private final ThreadPoolTaskExecutor taskExecutor;

        public RolePermissionChangedService(RedisService redisService, ThreadPoolTaskExecutor taskExecutor) {
            super(redisService, SUPPORT_FLAG);
            this.taskExecutor = taskExecutor;
        }

        @Override
        public void change(List<CusUserDto> list, RedisService redisService) {
            taskExecutor.execute(() -> {
                Stream<CusUserDto> stream;
                if (list.size() > 500) {
                    stream = list.parallelStream();
                } else {
                    stream = list.stream();
                }
                // 查询出所有的用户redis key
                List<String> userKey = Collections.synchronizedList(new ArrayList<>());
                stream.forEach(k -> {
                    Set<Object> set = redisService.sMembers(BaseConstant.LOGIN_TOKEN_KEY_INDEX + k.getCompanyId() + ":" + k.getId());
                    userKey.addAll(set.stream().map(Convert::toStr).collect(Collectors.toList()));
                });

                // 切分
                List<List<String>> partition = Lists.partition(userKey, 200);

                ValueOperations<String, Object> valueOperations = redisService.getRedisTemplate().opsForValue();
                // 分片更新
                for (List<String> userKeyPartition : partition) {
                    List<LoginUserDto> userDataList = Convert.toList(LoginUserDto.class, valueOperations.multiGet(userKeyPartition));
                    Map<String, LoginUserDto> updateMap = new HashMap<>();
                    // 这里必须相等
                    if (userKeyPartition.size() == userDataList.size()) {
                        for (int i = 0; i < userKeyPartition.size(); i++) {
                            try {
                                LoginUserDto loginUserDto = userDataList.get(i);
                                if (ObjectUtil.isNotNull(loginUserDto)) {
                                    loginUserDto.getCusUser().setRoleChanged(true);
                                    updateMap.put(userKeyPartition.get(i), loginUserDto);
                                }
                            } catch (Exception e) {
                                // ignore
                            }
                        }
                    }
                    // 更新数据
                    valueOperations.multiSet(updateMap);
                }
            });
        }
    }

    @Service("kickOffPermissionChangedService")
    public static class KickOffPermissionChangedService extends AbstractPermissionChangedService {

        public static final String SUPPORT_FLAG = "kickOff";

        private final ThreadPoolTaskExecutor taskExecutor;

        public KickOffPermissionChangedService(RedisService redisService, ThreadPoolTaskExecutor taskExecutor) {
            super(redisService, SUPPORT_FLAG);
            this.taskExecutor = taskExecutor;
        }

        @Override
        public void change(List<CusUserDto> list, RedisService redisService) {
            taskExecutor.execute(() -> {
                Stream<CusUserDto> stream;
                if (list.size() > 500) {
                    stream = list.parallelStream();
                } else {
                    stream = list.stream();
                }
                // 查询出所有的用户redis key
                List<String> userKey = Collections.synchronizedList(new ArrayList<>());
                stream.forEach(k -> {
                    Set<Object> set = redisService.sMembers(BaseConstant.LOGIN_TOKEN_KEY_INDEX + k.getCompanyId() + ":" + k.getId());
                    userKey.addAll(set.stream().map(Convert::toStr).collect(Collectors.toList()));
                });

                // 切分
                List<List<String>> partition = Lists.partition(userKey, 200);
                for (List<String> userKeyPartition : partition) {
                    redisService.del(userKeyPartition);
                }
            });
        }
    }
}
