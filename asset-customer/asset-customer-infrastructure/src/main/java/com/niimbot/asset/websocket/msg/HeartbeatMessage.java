package com.niimbot.asset.websocket.msg;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 连接事件
 *
 * <AUTHOR>
 * @date 2021/9/1 10:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class HeartbeatMessage extends Message {
    private static final long serialVersionUID = 6330555425872236660L;

    public static final String PING = "ping";
    public static final String PONG = "pong";

    public HeartbeatMessage() {
        super(Message.TYPE_HEARTBEAT);
    }

    private String hearbeat;
}
