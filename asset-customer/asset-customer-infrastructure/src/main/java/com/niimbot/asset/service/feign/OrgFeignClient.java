package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.EditRootOrg;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgExportDto;
import com.niimbot.system.OrgImportDto;
import com.niimbot.system.OrgQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20201112
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface OrgFeignClient {

    /**
     * 查询组织列表list集合
     *
     * @param queryDto 查询参数
     * @return 组织列表
     */
    @GetMapping(value = "server/system/org/list")
    List<OrgDto> list(@SpringQueryMap OrgQueryDto queryDto);

    /**
     * 查询组织列表list集合
     *
     * @param queryDto 查询参数
     * @return 组织列表
     */
    /*@Deprecated
    @PostMapping(value = "server/system/org/countEmp/list")
    List<OrgDto> countEmpList(@RequestParam(value = "countEmp", required = false) Boolean countEmp,
                              @RequestBody OrgQueryDto queryDto);*/

    /**
     * 查询组织列表list集合[带权限]
     *
     * @param queryDto 查询参数
     * @return 组织列表
     */
   /* @Deprecated
    @PostMapping(value = "server/system/org/countEmp/list/permission")
    List<OrgDto> countPermsEmpList(@RequestParam(value = "countEmp", required = false) Boolean countEmp,
                                   @RequestBody OrgQueryDto queryDto);*/

    /**
     * 查询存放区域组织列表list集合[带权限]
     *
     * @return 组织列表
     */
    @GetMapping(value = "server/system/org/area/list/permission")
    List<OrgDto> areaPermsList();

    /**
     * 查询耗材仓库组织列表list集合[带权限]
     *
     * @return 组织列表
     */
    @GetMapping(value = "server/system/org/store/list/permission")
    List<OrgDto> storePermsList();

    /**
     * 查询采购执行公司列表list集合[带权限]
     *
     * @return 组织列表
     */
    @GetMapping(value = "server/system/org/purchase/list/permission")
    List<OrgDto> permissionPurchaseOrgTree();

    /**
     * 查询组织分页列表
     *
     * @param queryDto 查询参数
     * @return 组织列表
     */
    @GetMapping(value = "server/system/org/page")
    PageUtils<OrgDto> page(@SpringQueryMap OrgQueryDto queryDto);

    /**
     * 根据组织Id查询组织详情
     *
     * @param orgId 组织Id
     * @return 组织数据
     */
    @GetMapping(value = "server/system/org/{orgId}")
    OrgDto getInfo(@PathVariable("orgId") Long orgId);

    /**
     * 批量删除组织数据
     *
     * @param orgIds 需要删除的组织id
     * @return 结果
     */
    @DeleteMapping(value = "server/system/org")
    Boolean delete(List<Long> orgIds);

    /**
     * 新增保存组织数据
     *
     * @param orgDto 组织数据
     * @return 结果
     */
    @PostMapping(value = "server/system/org")
    Boolean add(OrgDto orgDto);

    @PostMapping(value = "server/system/org/v2")
    String addV2(OrgDto org);

    /**
     * 修改保存组织数据
     *
     * @param orgDto 组织数据
     * @return 结果
     */
    @PutMapping(value = "server/system/org")
    Boolean edit(OrgDto orgDto);

    @PutMapping("server/system/org/editRootOrg")
    Boolean editRootOrg(@RequestBody EditRootOrg org);

    /**
     * 查询根组织
     *
     * @return 组织数据
     */
    @GetMapping(value = "server/system/org/rootOrg")
    OrgDto getRootOrg();

    /**
     * 根据组织id集合查询组织
     *
     * @param orgIds 查询参数
     * @return 组织
     */
    @PostMapping(value = "server/system/org/listByIds")
    List<OrgDto> listByIds(List<Long> orgIds);

    /**
     * 获取推荐区域编码
     *
     * @return 编码
     */
    @GetMapping(value = "server/system/org/recommendCode")
    String recommendCode();

    @PostMapping(value = "server/system/org/export")
    List<OrgExportDto> getExcelData(OrgQueryDto queryDto);

    @GetMapping(value = "server/system/org/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @DeleteMapping(value = "server/system/org/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/system/org/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/system/org/saveSheetData")
    Boolean saveSheetData(OrgImportDto importDto);

    @PutMapping(value = "server/system/org/sort")
    Boolean sort(List<Long> orgIds);

    @PostMapping("/server/system/org/listByCodes")
    List<OrgDto> listByCodes(@RequestBody List<String> codes);


}
