package com.niimbot.asset.controller.h5;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.service.feign.AreaFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.OnlineInventoryFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.inventory.InventoryAssetAppQueryDto;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.means.AssetQueryDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "区域管理")
@ResultController
@RequestMapping("api/common/area")
@RequiredArgsConstructor
public class H5AreaController {

    private final AreaFeignClient areaFeignClient;
    private final OnlineInventoryFeignClient onlineInventoryFeignClient;
    private final AssetFeignClient assetFeignClient;

    @ApiOperation(value = "删除区域数据")
    @PostMapping("/delete")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> ids) {
        // 该区域下存在非报废、丢失、出售、赠送状态的资产不可删除
        // todo... 加入资产状态值
        AssetQueryDto assetQueryDto = new AssetQueryDto();
        for (Long areaId : ids) {
            if (assetFeignClient.checkAsset(AssetConstant.CHECK_ASSET_AREA, areaId, assetQueryDto)) {
                throw new BusinessException(MeansResultCode.DELETE_SOURCE_CHECK_ASSET, "区域");
            }
        }
        List<AuditableOperateResult> results = areaFeignClient.deleteAreaByIds(ids);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MEANS_AREA, results));
        return true;
    }

    @ApiOperation(value = "查询区域树[带权限-盘点专用]-新")
    @GetMapping("/query/list")
    @AutoConvert
    public List<AreaDto> queryList(@Validated InventoryAssetAppQueryDto queryDto) {
        List<AreaDto> areas = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(true));
        List<String> areaIdList = areas.stream().map(item -> item.getId().toString()).collect(Collectors.toList());
        InventorySurplusQueryDto queryParam = new InventorySurplusQueryDto();
        BeanUtils.copyProperties(queryDto, queryParam);
        queryParam.setGroupByColumn("storage_area");
        QueryConditionDto queryConditionDto = new QueryConditionDto();
        queryConditionDto.setCode("storageArea");
        queryConditionDto.setQuery("in");
        queryConditionDto.setQueryData(areaIdList);
        queryParam.setConditions(Lists.newArrayList(queryConditionDto));
        List<InventoryAssetDataCountDto> assetDataCountDtoList = onlineInventoryFeignClient.appAssetCode(queryParam);
        log.info("areaController queryList success! param=[{}] result=[{}]", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(assetDataCountDtoList));
        Map<String, Integer> dataCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(assetDataCountDtoList)) {
            dataCountMap = assetDataCountDtoList.stream().collect(Collectors.toMap(InventoryAssetDataCountDto::getConditionCode, InventoryAssetDataCountDto::getDataCount, (v1, v2) -> v2));
        }
        for (AreaDto item : areas) {
            item.setDataCount(dataCountMap.get(item.getId().toString()));
        }
        // 统计数量为0的分组为其他
        Integer other = assetDataCountDtoList.stream().filter(v -> StrUtil.isBlank(v.getConditionCode())).map(InventoryAssetDataCountDto::getDataCount).reduce(0, Integer::sum);
        areas.add(new AreaDto().setAreaName("其他").setId(-1L).setDataCount(other));
        return areas;
    }

}
