package com.niimbot.asset.controller.common.equipment;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.asset.service.feign.MaintainPlanFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentSparePartsFeignClient;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.equipment.EquipmentMaterialQueryDto;
import com.niimbot.equipment.SparePartsCreateDto;
import com.niimbot.equipment.SparePartsDropDto;
import com.niimbot.equipment.SparePartsMaterialDto;
import com.niimbot.equipment.SparePartsStatisticsDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.means.AssetOperationDto;
import com.niimbot.means.AssetOperationQueryDto;
import com.niimbot.system.CusMenuDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/11 上午11:04
 */
@Api(tags = "备件管理")
@Slf4j
@ResultController
@RequestMapping("api/common/equipment/spareParts")
public class EquipmentSparePartsController {

    //需要排除的操作
    private static final List<String> excludeOperation = Stream.of("borrow_add", "recv_add", "return_add", "revert_add").collect(Collectors.toList());

    @Autowired
    private MaintainPlanFeignClient maintainPlanFeignClient;
    @Autowired
    private AssetFeignClient assetFeignClient;
    @Autowired
    private CusMenuFeignClient menuFeignClient;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private EquipmentSparePartsFeignClient equipmentSparePartsFeignClient;

    @ApiOperation(value = "【PC】通过资产ID查询资产操作")
    @PostMapping(value = "/getAssetOptByAssetId")
    public List<AssetOperationDto> getAssetOptByAssetId(@RequestBody AssetOperationQueryDto optQueryDto) {
        List<AssetOperationDto> opts = new ArrayList<>();
        List<AssetOperationDto> optByAssetId = assetFeignClient.getAssetOptByAssetId(optQueryDto.getIds());
        //设备这里需要去除掉 借用、领用、退还和归还操作
        if (CollUtil.isNotEmpty(optByAssetId)) {
            optByAssetId = optByAssetId.stream().filter(item -> !excludeOperation.contains(item.getCode())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(optByAssetId)) {
            return opts;
        }
        List<CusMenuDto> menus = menuFeignClient.userMenuPcList();
        List<String> buttonList = menus.stream()
                .filter(m -> m.getMenuType().equalsIgnoreCase(DictConstant.MENU_TYPE_BUTTON))
                .map(CusMenuDto::getMenuCode)
                .collect(Collectors.toList());
        Integer delete = AssetConstant.OPT_DELETE;
        Integer edit = AssetConstant.OPT_EDIT;
        Integer copy = AssetConstant.OPT_COPY;
        int assetSize = optQueryDto.getIds().size();
        for (AssetOperationDto item : optByAssetId) {
            // 添加菜单权限控制
            if (!BooleanUtil.isTrue(item.getIsDisable())) {
                item.setIsDisable(!buttonList.contains(item.getCode()));
            }

            // 资产复制菜单权限
            if (copy.equals(Convert.toInt(item.getOrderType()))) {
                if (1 == assetSize && buttonList.contains(item.getCode())) {
                    item.setIsDisable(false);
                } else {
                    item.setIsDisable(true);
                }
            }

            // 行级操作去除编辑按钮
            if (BooleanUtil.isTrue(optQueryDto.getIsRow())) {
                if (!edit.equals(item.getId())) {
                    item.setName("资产" + item.getName());
                    opts.add(item);
                }
            } else {
                if (edit.equals(Convert.toInt(item.getOrderType())) || delete.equals(Convert.toInt(item.getId()))) {
                    item.setName("批量" + item.getName());
                } else {
                    item.setName("资产" + item.getName());
                }
                if ((!Convert.toInt(item.getOrderType(), -1).equals(AssetConstant.ORDER_TYPE_MAINTAIN)) &&
                        (AssetConstant.ORDER_TYPE_ALLOCATE != Convert.toInt(item.getOrderType()))) {
                    opts.add(item);
                }
            }
        }
        // 有无保养计划
        List<MaintainPlanInfoDto> maintainPlans = maintainPlanFeignClient.getByAssetIds(optQueryDto.getIds());
        if (CollUtil.isNotEmpty(maintainPlans)) {
            opts.forEach(v -> {
                if (Objects.equals(delete, v.getId())) {
                    v.setIsDisable(true);
                }
            });
        }
        return opts;
    }

    @ApiOperation(value = "备件管理列表查询")
    @PostMapping(value = "/material/page")
    public PageUtils<JSONObject> materialPage(@RequestBody EquipmentMaterialQueryDto queryDto) {
        PageUtils<JSONObject> result = new PageUtils<>();
        result.setCurrPage((int)queryDto.getPageNum());
        result.setPageSize((int)queryDto.getPageSize());
        List<SparePartsMaterialDto> sparePartsMaterialDtoList = equipmentSparePartsFeignClient.queryMaterialInfo(queryDto.getAssetId());
        //如果备件信息为空，直接返回，不继续查询耗材信息
        if (CollUtil.isEmpty(sparePartsMaterialDtoList)) {
            result.setTotalCount(0);
            result.setTotalPage(0);
            result.setList(Collections.EMPTY_LIST);
            return result;
        } else {
            queryDto.getIncludeMaterialIds().addAll(sparePartsMaterialDtoList.stream().map(SparePartsMaterialDto::getMaterialId).collect(Collectors.toList()));
        }

        PageUtils<JSONObject> materialPage = materialService.materialPage(queryDto);
        if (Objects.nonNull(materialPage) && CollUtil.isNotEmpty(materialPage.getList())) {
            //设置备件信息
            assembleSpareParts(sparePartsMaterialDtoList, materialPage.getList());
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(materialPage.getList(), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return materialPage;
    }

    /**
     * 设置备件信息
     * @param sparePartsMaterialDtoList
     * @param materialInfoList
     */
    private void assembleSpareParts(List<SparePartsMaterialDto> sparePartsMaterialDtoList, List<JSONObject> materialInfoList) {
        if (CollUtil.isEmpty(sparePartsMaterialDtoList) || CollUtil.isEmpty(materialInfoList)) {
            return ;
        }

        Map<Long, SparePartsMaterialDto> sparePartsMap = sparePartsMaterialDtoList.stream().collect(Collectors.toMap(SparePartsMaterialDto::getMaterialId, value -> value, (value1, value2) -> value1));
        for (JSONObject item : materialInfoList) {
            if (Objects.nonNull(sparePartsMap.get(item.getLong("id")))) {
                item.put("sparePartsNum", sparePartsMap.get(item.getLong("id")).getSparePartsNum());
                item.put("installLocation", sparePartsMap.get(item.getLong("id")).getInstallLocation());
            } else {
                item.put("sparePartsNum", 0);
                item.put("installLocation", "");
            }
        }
    }

    @ApiOperation(value = "新增备件")
    @PostMapping("/create")
    public Boolean createSpareParts(@RequestBody @Validated SparePartsCreateDto sparePartsCreateDto) {
        return equipmentSparePartsFeignClient.createSpareParts(sparePartsCreateDto);
    }

    @ApiOperation(value = "修改备件")
    @PostMapping("/modify")
    public Boolean modifySpareParts(@RequestBody @Validated SparePartsCreateDto sparePartsCreateDto) {
        return equipmentSparePartsFeignClient.modifySpareParts(sparePartsCreateDto);
    }

    @ApiOperation(value = "删除备件")
    @PostMapping("/drop")
    public Boolean dropSpareParts(@RequestBody @Validated SparePartsDropDto sparePartsDropDto) {
        return equipmentSparePartsFeignClient.dropSpareParts(sparePartsDropDto);
    }

    @ApiOperation(value = "设备履历")
    @GetMapping("/log/{assetId}")
    public SparePartsStatisticsDto statisticsLog(@PathVariable("assetId") Long assetId) {
        return equipmentSparePartsFeignClient.statistics(assetId);
    }

}
