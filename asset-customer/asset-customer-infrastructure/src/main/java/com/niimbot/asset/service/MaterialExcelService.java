package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialQueryDto;
import com.niimbot.material.MaterialStockImportDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.means.ReImportDto;

import java.io.InputStream;
import java.util.List;

import cn.hutool.poi.excel.ExcelWriter;

public interface MaterialExcelService {

    /**
     * 导出资产模板
     *
     * @return excel
     */
    ExcelWriter buildExcelWriter(Long standardId);

    /**
     * 导入资产模板
     *
     * @param stream    文件流
     * @param fileName  文件名
     * @param companyId 公司Id
     */
    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId,
                          Long standardId);

    List<List<LuckySheetModel>> importError(Long taskId);

    Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);

    Boolean importErrorDelete(Long taskId);

    List<JSONObject> getExcelData(MaterialQueryDto queryDto, Long standardId);

    List<JSONObject> getStockExcelData(MaterialStockQueryDto queryDto, Long standardId);

    ReImportDto reImport(Long taskId);

    List<MaterialStockImportDto> parseStockStream(InputStream stream);
}
