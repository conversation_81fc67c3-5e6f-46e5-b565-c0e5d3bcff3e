package com.niimbot.asset.controller.common.means;

import com.niimbot.asset.service.feign.UnitFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AsUnitDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/8 15:55
 */
@Api(tags = "计量单位管理")
@ResultController
@RequestMapping("api/common/unit")
public class UnitController {

    private final UnitFeignClient unitFeignClient;

    @Autowired
    public UnitController(UnitFeignClient unitFeignClient) {
        this.unitFeignClient = unitFeignClient;
    }

    @ApiOperation(value = "计量单位搜索")
    @GetMapping(value = "/search")
    public List<AsUnitDto> search(
            @ApiParam(name = "name", value = "计量单位")
            @RequestParam(value = "name", required = false) String name) {
        return unitFeignClient.search(name);
    }

}
