package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventorySurplusSimpleQueryDto;

import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/3/10 16:57
 */
public interface InventoryResultHandleService {

    PageUtils<JSONObject> plPage(@RequestBody InventorySurplusQueryDto dto);

    PageUtils<JSONObject> getPlSurplus(InventorySurplusSimpleQueryDto dto);

    PageUtils<JSONObject> modifiedPage(InventorySurplusQueryDto dto);

    PageUtils<JSONObject> takePhotoPage(InventorySurplusQueryDto dto);
}
