package com.niimbot.asset.service.feign;

import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryImportDto;
import com.niimbot.means.CategoryQueryDto;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/26 14:08
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CategoryFeignClient {

    /**
     * 查询资产分类list集合
     *
     * @return 资产分类列表
     */
    @GetMapping(value = "server/means/category/list")
    List<CategoryDto> list(@SpringQueryMap CategoryQueryDto queryDto);

    @PostMapping(value = "server/means/category/listByIds")
    List<CategoryDto> listByIds(List<Long> ids);

    /**
     * 根据资产分类Id查询资产分类详情
     *
     * @param categoryId 资产分类Id
     * @return 资产分类数据
     */
    @GetMapping(value = "server/means/category/{categoryId}")
    CategoryDto getInfo(@PathVariable("categoryId") Long categoryId);

    /**
     * 批量删除资产分类数据
     *
     * @param categoryIds 需要删除的资产分类id
     * @return 结果
     */
    @DeleteMapping(value = "server/means/category")
    List<CategoryDto> delete(List<Long> categoryIds);

    /**
     * 新增保存资产分类数据
     *
     * @param categoryDto 资产分类数据
     * @return 结果
     */
    @PostMapping(value = "server/means/category")
    CategoryDto add(@RequestBody CategoryDto categoryDto);

    /**
     * 修改保存资产分类数据
     *
     * @param categoryDto 资产分类数据
     * @return 结果
     */
    @PutMapping(value = "server/means/category")
    CategoryDto edit(@RequestBody CategoryDto categoryDto);


    /**
     * 获取推荐分类编码
     *
     * @return 编码
     */
    @GetMapping(value = "server/means/category/recommendCode")
    String recommendCode();

    @PutMapping(value = "server/means/category/sort")
    Boolean sort(@RequestBody List<Long> categoryIds);

    @GetMapping(value = "server/means/category/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @DeleteMapping(value = "server/means/category/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/means/category/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/means/category/saveSheetData")
    Boolean saveSheetData(CategoryImportDto importDto);
}
