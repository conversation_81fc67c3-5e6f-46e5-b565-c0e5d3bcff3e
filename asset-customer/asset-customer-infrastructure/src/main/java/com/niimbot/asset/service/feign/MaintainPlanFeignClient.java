package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.maintenance.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/21 18:23
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaintainPlanFeignClient {

    /**
     * 保养计划分页列表
     *
     * @param queryDto 查询参数
     * @return 保养计划列表
     */
    @GetMapping(value = "server/maintenance/maintain/plan/page")
    PageUtils<MaintainPlanListDto> page(@SpringQueryMap MaintainPlanQueryDto queryDto);

    /**
     * 保养计划详情
     *
     * @param id 保养计划id
     * @return 保养计划详情
     */
    @GetMapping(value = "server/maintenance/maintain/plan/{id}")
    MaintainPlanInfoDto info(@PathVariable("id") Long id);

    @GetMapping(value = "server/maintenance/maintain/plan/content/{assetId}")
    MaintainPlanDetailDto contentInfo(@PathVariable("assetId") Long assetId);

    @PostMapping("server/maintenance/maintain/plan/getByAssetIds")
    List<MaintainPlanInfoDto> getByAssetIds(@RequestBody List<Long> assetIds);

    /**
     * 删除保养计划
     *
     * @param planIds 计划ID
     * @return 结果
     */
    @DeleteMapping(value = "server/maintenance/maintain/plan")
    Boolean delete(List<Long> planIds);

    /**
     * 新增保养计划
     *
     * @param optDto dto
     * @return 结果
     */
    @PostMapping(value = "server/maintenance/maintain/plan")
    Boolean insert(MaintainPlanDto optDto);

    /**
     * 编辑保养计划
     *
     * @param optDto dto
     * @return 结果
     */
    @PutMapping(value = "server/maintenance/maintain/plan")
    Boolean edit(MaintainPlanDto optDto);

    /**
     * 批量设置保养计划
     *
     * @param optDto dto
     * @return 结果
     */
    @PutMapping(value = "server/maintenance/maintain/plan/batch")
    Boolean editBatch(MaintainPlanEditBatchDto optDto);

    /**
     * 启用/禁用
     *
     * @param optDto dto
     * @return 结果
     */
    @PutMapping(value = "server/maintenance/maintain/plan/status")
    Boolean status(MaintainPlanDto optDto);

}
