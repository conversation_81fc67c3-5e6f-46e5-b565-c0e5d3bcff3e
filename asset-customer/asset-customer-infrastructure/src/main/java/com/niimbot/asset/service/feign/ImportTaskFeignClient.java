package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.ImportTaskPageQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

/**
 * 导入导出任务接口
 *
 * <AUTHOR>
 * @date 2021/12/13 15:31
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ImportTaskFeignClient {
    /**
     * 新增导入导出任务
     *
     * @param taskDto
     * @return
     */
    @PostMapping(value = "server/system/import")
    Long save(@RequestBody ImportTaskDto taskDto);

    /**
     * 更新
     *
     * @param taskDto
     * @return
     */
    @PutMapping(value = "server/system/import")
    Boolean update(@RequestBody ImportTaskDto taskDto);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "server/system/import/{id}")
    Boolean delete(@PathVariable("id") Long id);

    /**
     * 删除全部(不包含导入中的)
     *
     * @return
     */
    @DeleteMapping(value = "server/system/import/all/{type}")
    Boolean deleteAll(@PathVariable("type") Integer type);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @GetMapping("server/system/import/page")
    PageUtils<ImportTaskDto> page(@SpringQueryMap ImportTaskPageQueryDto query);

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @GetMapping("server/system/import/{id}")
    ImportTaskDto queryById(@PathVariable("id") Long id);

    /**
     * 任务数量
     *
     * @return
     */
    @GetMapping("server/system/import/amount/{type}")
    Integer amount(@PathVariable("type") Integer type);

    /**
     * 任务数量
     *
     * @return
     */
    @GetMapping("server/system/import/count/{type}/{orderType}")
    Integer count(@PathVariable("type") Integer type, @PathVariable("orderType") Integer orderType);
}
