package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.service.feign.CompanyResourceFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.sale.CompanyResourceDto;
import com.niimbot.sale.ResourcePackDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/8 14:26
 */
@Api(tags = "【服务中心】资源信息")
@ResultController
@RequestMapping("api/common/resource")
@RequiredArgsConstructor
public class CompanyResourceController {

    private final CompanyResourceFeignClient companyResourceFeignClient;

    @ApiOperation(value = "查询企业容量信息")
    @GetMapping("/company/capacity")
    public CompanyResourceCapacityDto getCapacity() {
        CompanyResourceCapacityDto capacity = companyResourceFeignClient.getCapacity();
        if (capacity.getRemainder() < 0) {
            capacity.setRemainder(0);
        }
        return capacity;
    }

    @ApiOperation(value = "查询企业容量详情")
    @GetMapping("/company/capacity/detail")
    public List<CompanyResourceDto> getCapacityDetail() {
        return companyResourceFeignClient.getCapacityDetail();
    }

    @ApiOperation(value = "查询资源包列表")
    @GetMapping("/resourcePack")
    public List<ResourcePackDto> listPack() {
        return companyResourceFeignClient.listPack();
    }

    @ApiOperation(value = "查询资源包详情")
    @GetMapping("/resourcePack/{id}")
    public ResourcePackDto info(@PathVariable("id") Long id) {
        return companyResourceFeignClient.info(id);
    }

}
