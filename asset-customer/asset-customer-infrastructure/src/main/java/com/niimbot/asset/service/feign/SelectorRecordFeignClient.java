package com.niimbot.asset.service.feign;

import com.niimbot.system.AddSelectorRecord;
import com.niimbot.system.GetSelectorRecord;
import com.niimbot.system.SelectorRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface SelectorRecordFeignClient {

    @PostMapping("/system/selectorRecord/add")
    Boolean record(@RequestBody AddSelectorRecord record);

    @GetMapping("/system/selectorRecord/get")
    List<SelectorRecord> records(@SpringQueryMap GetSelectorRecord record);

}
