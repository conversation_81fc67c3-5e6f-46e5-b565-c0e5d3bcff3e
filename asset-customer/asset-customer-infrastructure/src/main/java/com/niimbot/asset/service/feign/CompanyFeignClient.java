package com.niimbot.asset.service.feign;

import com.niimbot.system.CompanyDto;
import com.niimbot.system.CompanySettingDto;
import com.niimbot.system.DataAuthorityDto;
import com.niimbot.system.DataPermissionDto;
import com.niimbot.system.RoleDataAuthorityConfigDto;
import com.niimbot.system.SalesOwnerDto;
import com.niimbot.system.crm.CrmPushMessage;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:26
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CompanyFeignClient {
    /**
     * 根据公司Id查询公司扩展信息
     *
     * @param companyId 公司信息
     * @return 公司扩展信息
     */
    @GetMapping(value = "server/system/company/setting/{companyId}")
    CompanySettingDto selectCompanySettingById(@PathVariable("companyId") Long companyId);

    @GetMapping("server/system/permission/companyDefault/{companyId}")
    List<DataPermissionDto> getCompanyDefaultDataAuth(@PathVariable("companyId") Long companyId);

    @GetMapping("server/system/permission/getRoleDataAuth")
    List<DataPermissionDto> getRoleDataAuth(@RequestParam("roleId") Long roleId);

    @PutMapping("server/system/company/enableIdleAsset/{enable}")
    Boolean modifyEnableIdleAsset(@PathVariable("enable") Boolean enable);

    @PutMapping("server/system/company/enableSyncApproveRole/{enable}")
    Boolean enableSyncApproveRole(@PathVariable("enable") Boolean enable);

    @GetMapping("server/system/company/getIdleAssetSwitch/{companyId}")
    Boolean getIdleAssetSwitch(@PathVariable("companyId") Long companyId);

    @PutMapping("server/system/company/configDefaultDataAuth/{companyId}")
    Boolean configureDefaultDataAuth(@RequestBody List<DataAuthorityDto> dataAuthorities, @PathVariable("companyId") Long companyId);

    @PostMapping("server/system/permission/configRoleDataAuth")
    Boolean configRoleDataAuth(@RequestBody RoleDataAuthorityConfigDto roleDataAuthorityConfigDto);

    /**
     * 修改公司所在行业
     *
     * @param industryId industryId
     * @return 结果
     */
    @PutMapping("server/system/company/industry/{industryId}")
    Boolean changeIndustry(@PathVariable(value = "industryId") Long industryId);

    /**
     * 修改企业logo
     *
     * @param logo logo
     * @return url路径
     */
    @GetMapping(value = "server/system/company/changeLogo")
    Boolean changeLogo(@RequestParam("logo") String logo);

    /**
     * 获取企业信息
     *
     * @param companyId companyId
     * @return 企业信息
     */
    @GetMapping(value = "server/system/company/{companyId}")
    CompanyDto getCompanyInfo(@PathVariable("companyId") Long companyId);

    /**
     * 初始化同步CRM线索
     *
     * @return
     */
    @PostMapping(value = "server/system/company/pushClue")
    void pushClue();

    /**
     * 测试推送
     *
     * @return
     */
    @PostMapping(value = "server/system/company/pushClueTest")
    void pushClueTest(@RequestBody CrmPushMessage messageDto);

    /**
     * 修改企业名称
     *
     * @param name name
     * @return 成功与否
     */
    @PutMapping(value = "server/system/company/changeCompanyName")
    Boolean changeCompanyName(@RequestParam("name") String name);

    @GetMapping(value = "server/system/company/getSalesOwner")
    SalesOwnerDto getSalesOwner();
}
