package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.MaterialExcelTplEnum;
import com.niimbot.asset.service.AbstractMaterialService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialExcelEditService;
import com.niimbot.asset.service.feign.DataAuthorityFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialCategoryDto;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.system.DataAuthorityDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.ImportTaskExtInfoDto;
import com.niimbot.system.OrgDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/8/31 14:51
 */
@Slf4j
@Service
public class MaterialExcelEditServiceImpl extends AbstractMaterialService implements MaterialExcelEditService {

    private static ThreadLocal<ImportInfo> globalCache = new TransmittableThreadLocal<>();

    private final RedisService redisService;
    private final ImportService importService;
    private final ImportTaskFeignClient importTaskFeignClient;
    private final DataAuthorityFeignClient dataAuthorityFeignClient;

    @Autowired
    public MaterialExcelEditServiceImpl(RedisService redisService,
                                        ImportService importService,
                                        ImportTaskFeignClient importTaskFeignClient,
                                        DataAuthorityFeignClient dataAuthorityFeignClient) {
        this.redisService = redisService;
        this.importService = importService;
        this.importTaskFeignClient = importTaskFeignClient;
        this.dataAuthorityFeignClient = dataAuthorityFeignClient;
    }


    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setFileName(fileName);
        importInfo.setFileSize(fileSize);
        importInfo.setCompanyId(companyId);

        // 解析的模板导入
        ExcelReader reader = ExcelUtil.getReader(stream);
        List<List<Object>> read = reader.read(1);
        if (CollUtil.isEmpty(read)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表格未检测到数据");
        }
        // =============================  读取Excel表头并校验 ================================
        List<Object> header = ExcelUtils.clearEmpty(read.get(0));
        if (CollUtil.isEmpty(header)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表头未检测到数据");
        }

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        // 可编辑的属性
        FormFieldCO materialSerialNoCode = new FormFieldCO();
        List<FormFieldCO> formFields = new ArrayList<>();
        for (FormFieldCO f : formVO.getFormFields()) {
            if (FormFieldCO.YZC_MATERIAL_SERIALNO.equals(f.getFieldType())) {
                formFields.add(f);
                materialSerialNoCode = f;
            }
            if (!FormFieldCO.YZC_MATERIAL_SERIALNO.equals(f.getFieldType())
                    && !f.isHidden()
                    && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                    .contains(f.getFieldType())) {
                formFields.add(f);
            }
        }

        if (CollUtil.isEmpty(formFields)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "未配置可编辑字段");
        }
        Map<String, List<FormFieldCO>> materialAttrMap = formFields.stream()
                .collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 表头字段映射
        formFieldMap.set(MapUtil.of("material", new ArrayList<>()));
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        for (int i = 0; i < header.size(); i++) {
            String headName = Convert.toStr(header.get(i), "").trim();
            if (StrUtil.isBlank(Convert.toStr(headName))) {
                continue;
            }
            if (materialAttrMap.containsKey(headName)) {
                List<FormFieldCO> formFieldCOList = materialAttrMap.get(headName);
                if (CollUtil.isNotEmpty(formFieldCOList)) {
                    FormFieldCO formFieldCO = formFieldCOList.get(0);
                    headerMapping.put(i, formFieldCO);
                    formFieldCOList.remove(0);
                    materialAttrMap.put(headName, formFieldCOList);
                    formFieldMap.get().get("material").add(formFieldCO);
                }
            }
        }
        if (CollUtil.isEmpty(headerMapping)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表头未检测到可编辑属性");
        }
        FormFieldCO finalMaterialSerialNoCode = materialSerialNoCode;
        boolean find = headerMapping.values().stream().anyMatch(f -> f.getFieldCode().equals(finalMaterialSerialNoCode.getFieldCode()));
        if (!find) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表格未检测到必填字段" + finalMaterialSerialNoCode.getFieldName());
        }
        importInfo.setHeaderMapping(headerMapping);
        importInfo.setRead(read);
        this.importExcel(importInfo, true);
    }

    @Override
    public void importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (importTaskDto == null) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "导入任务" + taskId + "不存在");
        }
        if (CollUtil.isEmpty(sheetModels)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表格未检测到数据");
        }

        // =============================  读取Excel表头并校验 ================================
        List<Object> header = sheetModels.get(0);
        if (CollUtil.isEmpty(header)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表头未检测到数据");
        }

        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setTaskId(taskId);
        importInfo.setFileName("编辑耗材在线编辑保存");
        importInfo.setFileSize(0L);
        importInfo.setCompanyId(companyId);
        importInfo.setRead(sheetModels);

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        // 可编辑的属性
        FormFieldCO materialSerialNoCode = new FormFieldCO();
        List<FormFieldCO> formFields = new ArrayList<>();
        for (FormFieldCO f : formVO.getFormFields()) {
            if (FormFieldCO.YZC_MATERIAL_SERIALNO.equals(f.getFieldType())) {
                formFields.add(f);
                materialSerialNoCode = f;
            }
            if (!FormFieldCO.YZC_MATERIAL_SERIALNO.equals(f.getFieldType())
                    && !f.isHidden()
                    && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                    .contains(f.getFieldType())) {
                formFields.add(f);
            }
        }

        if (CollUtil.isEmpty(formFields)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "未配置可编辑字段");
        }
        Map<String, List<FormFieldCO>> materialAttrMap = formFields.stream()
                .collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 表头字段映射
        formFieldMap.set(MapUtil.of("material", new ArrayList<>()));
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        for (int i = 0; i < header.size(); i++) {
            String headName = Convert.toStr(header.get(i), "").trim();
            if (StrUtil.isBlank(Convert.toStr(headName))) {
                continue;
            }
            if (materialAttrMap.containsKey(headName)) {
                List<FormFieldCO> formFieldCOList = materialAttrMap.get(headName);
                if (CollUtil.isNotEmpty(formFieldCOList)) {
                    FormFieldCO formFieldCO = formFieldCOList.get(0);
                    headerMapping.put(i, formFieldCO);
                    formFieldCOList.remove(0);
                    materialAttrMap.put(headName, formFieldCOList);
                    formFieldMap.get().get("material").add(formFieldCO);
                }
            }
        }
        if (CollUtil.isEmpty(headerMapping)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表头未检测到可编辑属性");
        }
        FormFieldCO finalMaterialSerialNoCode = materialSerialNoCode;
        boolean find = headerMapping.values().stream().anyMatch(f -> f.getFieldCode().equals(finalMaterialSerialNoCode.getFieldCode()));
        if (!find) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "表格未检测到必填字段" + finalMaterialSerialNoCode.getFieldName());
        }

        importInfo.setHeaderMapping(headerMapping);
        // 校验表头数据
        this.importExcel(importInfo, false);
    }

    @Override
    public ExcelWriter buildEditExcelWriter() {
        // 获取 writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 获取 sheet
        Sheet sheet = writer.getSheet();
        // 获取 styleSet
        StyleSet styleSet = writer.getStyleSet();
        // 设置边框
        styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
        styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);
        Workbook workbook = sheet.getWorkbook();
        DataFormat format = workbook.createDataFormat();
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(format.getFormat("@"));

        // 设置表头的cellStyle
        CellStyle redHeadStyle = writer.createCellStyle();
        redHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        redHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        redHeadStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        redHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        redHeadStyle.setBorderBottom(BorderStyle.THIN);
        redHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        redHeadStyle.setBorderTop(BorderStyle.THIN);
        redHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderLeft(BorderStyle.THIN);
        redHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderRight(BorderStyle.THIN);
        redHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font red = writer.createFont();
        red.setFontHeightInPoints((short) 13);
        red.setColor(IndexedColors.RED.getIndex());
        redHeadStyle.setFont(red);

        CellStyle blackHeadStyle = writer.createCellStyle();
        blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        blackHeadStyle.setBorderBottom(BorderStyle.THIN);
        blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setBorderTop(BorderStyle.THIN);
        blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderLeft(BorderStyle.THIN);
        blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderRight(BorderStyle.THIN);
        blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font black = writer.createFont();
        black.setFontHeightInPoints((short) 13);
        black.setColor(IndexedColors.BLACK.getIndex());
        blackHeadStyle.setFont(black);

        // 写入文件注意事项
        Row attentionRow = writer.getOrCreateRow(0);
        attentionRow.setHeight((short) 2400);
        Cell attentionCell = attentionRow.createCell(0);
        CellStyle attention = writer.createCellStyle();
        attention.setVerticalAlignment(VerticalAlignment.CENTER);
        attention.setWrapText(true);
        attention.setFont(black);
        attentionCell.setCellStyle(attention);
        String text = "注意事项:\r\n" +
                "1、更新时耗材编码是必填字段，耗材编码本身不支持修改，请确认填写的编码已经在系统中；\r\n" +
                "2、不需要更新的字段，不填写则视为不更新；\r\n" +
                "3、如需删除某个字段的值，则请填写：删除 这两个字；\r\n" +
                "4、单次最多可导入5000条数据，文件不可超过1MB。";
        XSSFRichTextString xssfRichTextString = new XSSFRichTextString(text);
        Font titleRed = writer.createFont();
        titleRed.setFontHeightInPoints((short) 13);
        titleRed.setColor(IndexedColors.RED.getIndex());
        xssfRichTextString.applyFont(0, 92, black);
        xssfRichTextString.applyFont(92, 94, titleRed);
        xssfRichTextString.applyFont(94, text.length() - 1, black);
        attentionCell.setCellValue(xssfRichTextString);
        writer.merge(26);

        // 查询录入标准数据项
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = new ArrayList<>();
        for (FormFieldCO f : formVO.getFormFields()) {
            if (FormFieldCO.YZC_MATERIAL_SERIALNO.equals(f.getFieldType())) {
                formFields.add(0, f);
            } else if (!f.isHidden()
                    && !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                    .contains(f.getFieldType())) {
                formFields.add(f);
            }
        }

        if (CollUtil.isEmpty(formFields)) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "未配置可编辑字段");
        }
        Row headRow = writer.getOrCreateRow(1);
        int realCol = 0;
        for (FormFieldCO attr : formFields) {
            // ============================== 设置表头 ===================================
            // 写入表头
            Cell cell = headRow.createCell(realCol);
            cell.setCellStyle(attr.getFieldType().equals(AssetConstant.ED_YZC_MATERIAL_SERIALNO) ? redHeadStyle : blackHeadStyle);
            cell.setCellValue(attr.getFieldName());

            String tplComment = MaterialExcelTplEnum.getEditTplComment(attr.getFieldCode());
            if (StrUtil.isNotEmpty(tplComment)) {
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6));
                comment.setString(new XSSFRichTextString(tplComment));
                cell.setCellComment(comment);
            }

            // 调整每一列宽度
            sheet.autoSizeColumn((short) realCol);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(realCol, sheet.getColumnWidth(realCol) * 17 / 10);
            sheet.setDefaultColumnStyle(realCol, style);

            if (FormFieldCO.SELECT_DROPDOWN.equals(attr.getFieldType())) {
                JSONArray values = attr.getFieldProps().getJSONArray("values");
                String[] selected = values.toArray(new String[]{});
                writer.addSelect(new CellRangeAddressList(2, 5000, realCol, realCol), selected);
            }
            realCol += 1;
        }
        // 写入其他sheet
        buildSheet(writer, formFields);
        return writer;
    }

    private void importExcel(ImportInfo importInfo, Boolean async) {
        // 判断是否超过最大上传条数，一次限制5000
        if (importInfo.getRead().size() > MAX_BATCH + 1) {
            throw new BusinessException(MaterialResultCode.IMPORT_MAX_LIMIT);
        }

        // 删除历史导入信息
        if (importInfo.getTaskId() != null) {
            materialFeignClient.importErrorDeleteAll(importInfo.getTaskId());
        }
        globalCache.set(importInfo);
        dropDownCache.set(new HashMap<>());
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto()
                        .setFileName(importInfo.getFileName())
                        .setImportType(DictConstant.IMPORT_TYPE_MATERIAL_EDIT)
                        .setFileSize(importInfo.getFileSize())
                        .setCount(importInfo.getRead().size() - 1);
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(importCache);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto()
                    .setFileName(importInfo.getFileName())
                    .setImportType(DictConstant.IMPORT_TYPE_MATERIAL_EDIT)
                    .setFileSize(importInfo.getFileSize())
                    .setCount(importInfo.getRead().size() - 1);
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(importCache);
        }
    }

    private void startImport(ImportDto importCache) {
        ImportInfo importInfo = globalCache.get();
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (importInfo.getTaskId() != null) {
            importTaskDto = importTaskFeignClient.queryById(importInfo.getTaskId());
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, importInfo.getCompanyId()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                ImportTaskExtInfoDto extInfoDto = new ImportTaskExtInfoDto();
                extInfoDto.setStandardId(importInfo.getStandardId());
                importTaskDto.setExtInfo(extInfoDto);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
            }
            List<AssetImportDto> tableData = new ArrayList<>();
            // 构建资产JSON数据
            for (int idx = 1; idx < importInfo.getRead().size(); idx++) {
                // 行数据
                List<Object> dataRow = importInfo.getRead().get(idx);
                // 写入固定属性数据
                JSONObject assetData = new JSONObject();
                IntStream.range(0, dataRow.size()).forEach(nameIdx -> {
                    FormFieldCO assetConfig = importInfo.getHeaderMapping().get(nameIdx);
                    if (assetConfig != null) {
                        Object cell = dataRow.get(nameIdx);
                        assetData.put(assetConfig.getFieldCode(), cell);
                    }
                });
                // 创建属性对象
                AssetImportDto importDto = new AssetImportDto();
                importDto.setTaskId(importTaskDto.getId())
                        .setStandardId(importInfo.getStandardId())
                        .setErrorNum(0)
                        .setAssetData(assetData);
                tableData.add(importDto);
            }
            // 执行
            importService.sendMaterialEditMsg(importInfo.getCompanyId());
            this.executeTableData(importTaskDto.getId(), tableData, importInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, importInfo.getCompanyId()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, importInfo.getCompanyId()), "finish", true);
            importService.sendMaterialEditMsg(importInfo.getCompanyId());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, importInfo.getCompanyId());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
            }
            this.clearThreadLocal();
        }
    }

    /**
     * 处理校验入库
     *
     * @param tableData 数据
     */
    private void executeTableData(Long taskId, List<AssetImportDto> tableData, ImportInfo importInfo) {
        // 仓库权限
        DataAuthorityDto repoPerm = dataAuthorityFeignClient.getByUserAndDataCodeAndCode(LoginUserThreadLocal.getCurrentUserId(), "material", "store");
        // 加载缓存，有序集合
        LinkedHashMap<String, FormFieldCO> codeToMaterialAttrMap = new LinkedHashMap<>();
        Map<String, Map<String, List<IdNameCache>>> fieldCodeCache = dropDownCache.get();
        for (Map.Entry<Integer, FormFieldCO> entry : importInfo.getHeaderMapping().entrySet()) {
            FormFieldCO fieldCO = entry.getValue();
            loadSelectCache(fieldCO.getFieldType());
            codeToMaterialAttrMap.putIfAbsent(fieldCO.getFieldCode(), fieldCO);
        }

        // 写入表头数据
        this.saveLuckySheetHead(taskId, importInfo.getHeaderMapping());
        AtomicInteger successNum = new AtomicInteger(0);
        List<OrgDto> orgDtoList = orgFeignClient.storePermsList();
        // 循环处理行数据
        IntStream.range(0, tableData.size()).forEach(idx -> {
            // 获取导入数据
            AssetImportDto importDto = tableData.get(idx);
            // 数据
            JSONObject materialData = importDto.getAssetData();
            List<AssetImportDto.FieldData> fieldDataList = new ArrayList<>();
            codeToMaterialAttrMap.forEach((attrCode, fieldCO) -> {
                // 新增数据，直接新增，不参与事务。2022年7月19日
                boolean needSave = BooleanUtil.isTrue(fieldCO.getFieldProps().getBoolean("saveData"));

                AssetImportDto.FieldData fieldData = new AssetImportDto.FieldData();
                Object attrVal = materialData.getOrDefault(attrCode, null);
                fieldData.setSource(attrVal);
                if (attrVal instanceof String) {
                    fieldData.setTarget(StrUtil.trim(((String) attrVal)));
                } else {
                    fieldData.setTarget(attrVal);
                }
                fieldData.setFieldName(fieldCO.getFieldName());
                fieldData.setFieldCode(fieldCO.getFieldCode());

                // 删除操作不处理
                if (!"删除".equals(Convert.toStr(attrVal))) {
                    // 处理日期类型
                    if (FormFieldCO.DATETIME.equals(fieldCO.getFieldType())) {
                        dateConvert(fieldData, attrVal, fieldCO.getFieldProps().getString("dateFormatType"));
                    }
                    // 处理多选
                    if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(fieldCO.getFieldType())) {
                        if (fieldData.getTarget() != null) {
                            String val = Convert.toStr(attrVal);
                            val = val.replace("，", ",");
                            String[] split = val.split(",");
                            fieldData.setTarget(new ArrayList<>(Arrays.asList(split)));
                        } else {
                            fieldData.setTarget(new ArrayList<>());
                        }
                    }
                    // 翻译业务组件数据
                    String attrValStr = StrUtil.trim(Convert.toStr(attrVal));
                    if (fieldCodeCache.containsKey(fieldCO.getFieldType()) && StrUtil.isNotBlank(attrValStr)) {
                        Map<String, List<IdNameCache>> idNameCacheMap = fieldCodeCache.getOrDefault(fieldCO.getFieldType(), new HashMap<>());
                        List<IdNameCache> idNameCaches;
                        if (idNameCacheMap.containsKey(attrValStr)) {
                            // 1.优先全匹配文本
                            idNameCaches = idNameCacheMap.get(attrValStr);
                        } else {
                            // 2.匹配括号内容是否大写字符加数字类型
                            idNameCaches = idNameCacheMap.get(ExcelUtils.matchCodeAndReplace(attrValStr));
                        }
                        if (CollUtil.isEmpty(idNameCaches)) {
                            if (needSave) {
                                // db插入数据
                                switch (fieldCO.getFieldType()) {
                                    case FormFieldCO.YZC_MATERIAL_CATE:
                                        String cateCode = materialCategoryFeignClient.recommendCode();
                                        MaterialCategoryDto cateDto = new MaterialCategoryDto();
                                        cateDto.setCategoryCode(cateCode)
                                                .setCategoryName(attrValStr)
                                                .setPid(0L);
                                        if (attrValStr.length() > 50 || attrValStr.length() < 1) {
                                            fieldData.getErrMsg().add("分类名称请输入1-50位");
                                        } else {
                                            try {
                                                Long cateId = materialCategoryFeignClient.add(cateDto);
                                                cateDto.setId(cateId);
                                                idNameCacheMap.put(attrValStr,
                                                        ListUtil.of(new IdNameCache(Convert.toStr(cateDto.getId()),
                                                                cateDto.getCategoryName(), true)));
                                                fieldData.setTarget(Convert.toStr(cateDto.getId()));
                                            } catch (Exception e) {
                                                log.error("耗材分类自动新增异常, {}", e.getMessage(), e);
                                                fieldData.getErrMsg().add("耗材分类自动新增异常，请重试");
                                            }
                                        }
                                        break;
                                    case FormFieldCO.YZC_REPOSITORY:
                                        if (CollUtil.isNotEmpty(orgDtoList)) {
                                            String repoCode = materialRepositoryFeignClient.recommendCode();
                                            MaterialRepositoryDto repositoryDto = new MaterialRepositoryDto();
                                            repositoryDto.setName(attrValStr)
                                                    .setCode(repoCode)
                                                    .setManagerOwner(orgDtoList.get(0).getId());
                                            if (attrValStr.length() > 20 || attrValStr.length() < 2) {
                                                fieldData.getErrMsg().add("仓库名称请输入2-20位");
                                            } else {
                                                try {
                                                    Long repoId = materialRepositoryFeignClient.add(repositoryDto);
                                                    repositoryDto.setId(repoId);
                                                    idNameCacheMap.put(attrValStr,
                                                            ListUtil.of(new IdNameCache(Convert.toStr(repositoryDto.getId()),
                                                                    repositoryDto.getName(),
                                                                    repoPerm.getAuthorityType() == 0)));
                                                    if (repoPerm.getAuthorityType() == 0) {
                                                        fieldData.setTarget(Convert.toStr(repositoryDto.getId()));
                                                    } else {
                                                        fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                                                    }
                                                } catch (Exception e) {
                                                    log.error("仓库自动新增异常, {}", e.getMessage(), e);
                                                    fieldData.getErrMsg().add("仓库自动新增异常，请重试");
                                                }
                                            }
                                        }
                                        break;
                                    default:
                                        break;
                                }
                            } else {
                                fieldData.getErrMsg().add("当前数据不存在，请先添加");
                            }
                        } else if (idNameCaches.size() > 1) {
                            if (FormFieldCO.YZC_MATERIAL_CATE.equals(fieldCO.getFieldType())) {
                                fieldData.getErrMsg().add("当前数据有重名，请输入耗材分类和耗材编码，示例：办公用品（A10）");
                            } else {
                                fieldData.getErrMsg().add("当前数据有重名");
                            }
                        } else {
                            IdNameCache idName = idNameCaches.get(0);
                            if (idName.getHasPerm()) {
                                fieldData.setTarget(idName.getId());
                            } else {
                                fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                            }
                        }
                    }
                }
                fieldDataList.add(fieldData);
            });
            importDto.setFieldDataList(fieldDataList);
            importDto.setRowNum(idx + 1 - successNum.get());
            importDto.setFormFieldMap(formFieldMap.get());
            Boolean success = materialFeignClient.saveEditSheetData(importDto);
            if (BooleanUtil.isTrue(success)) {
                successNum.getAndIncrement();
            }
            importService.sendMaterialEditMsg(importInfo.getCompanyId());
        });
    }

    private void saveLuckySheetHead(Long taskId, Map<Integer, FormFieldCO> headerMapping) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new LinkedList<>();

        List<Integer> headerList = new ArrayList<>(headerMapping.keySet());
        headerList = headerList.stream().sorted().collect(Collectors.toList());

        AtomicInteger cellIndex = new AtomicInteger(0);
        for (Integer index : headerList) {
            FormFieldCO config = headerMapping.get(index);
            // 记录当前属性
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(config.getFieldName());
            headModelList.add(luckySheetModel);
        }
        importErrorDto.setHeadModelList(headModelList);
        this.materialFeignClient.saveSheetHead(importErrorDto);
    }

    /**
     * 清理本地缓存
     */
    private void clearThreadLocal() {
        dropDownCache.remove();
        globalCache.remove();
        formFieldMap.remove();
    }

}
