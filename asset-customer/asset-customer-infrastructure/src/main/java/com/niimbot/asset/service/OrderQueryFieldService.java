package com.niimbot.asset.service;

import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.OrderQueryHeadConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryHeadConfigDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/26 10:57
 */
public interface OrderQueryFieldService {
    // --------------审批流---------------
    List<QueryConditionDto> activitiQueryField(Integer orderType);

    // --------------资产单据筛选选项---------------
    List<QueryConditionDto> orderQueryField(Integer orderType);

    List<QueryConditionDto> orderQueryView(Integer orderType);

    // --------------资产单据列表设置---------------
    Boolean orderHeadField(OrderQueryHeadConfigDto config);

    QueryHeadConfigDto orderHeadField(Integer orderType);

    List<QueryConditionDto> orderAllHeadField(Integer orderType);

    List<AssetHeadDto> orderHeadView(Integer orderType);
}
