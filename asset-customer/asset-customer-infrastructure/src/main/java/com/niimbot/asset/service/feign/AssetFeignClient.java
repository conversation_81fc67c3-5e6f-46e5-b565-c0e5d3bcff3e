package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.dynamicform.BuildAssetLogDto;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.*;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.TagAttrListDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/16
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetFeignClient {

    /**
     * 查询获取资产信息
     *
     * @param id 资产ID
     * @return 资产信息
     */
    @GetMapping(value = "server/means/asset/{assetId}")
    AssetDto getInfo(@PathVariable(value = "assetId") Long id);

    @GetMapping(value = "server/means/asset/code/{assetCode}")
    AssetDto getInfoByCode(@PathVariable(value = "assetCode") String assetCode);

    @GetMapping(value = "server/means/asset/byId/{assetId}")
    AssetDto getInfoNoPerm(@PathVariable(value = "assetId") Long id);

    @GetMapping(value = "server/means/asset/inRecycleBin/{id}")
    AssetDto getInRecycleBin(@PathVariable(value = "id") Long id);

    /**
     * 查询获取资产信息
     *
     * @param ids 资产ID
     * @return 资产信息
     */
    @PostMapping(value = "server/means/asset/list")
    List<AssetDto> getInfoList(@RequestParam(value = "ids") List<Long> ids);

    /**
     * 检查资源下是否有资产信息
     *
     * @param type      资源类型
     * @param genericId 查询Id
     * @param queryDto  查询条件
     * @return 是否存在资产
     */
    @GetMapping(value = "server/means/asset/checkAsset/{type}/{genericId}")
    Boolean checkAsset(@PathVariable("type") String type, @PathVariable("genericId") Long genericId,
                       @SpringQueryMap AssetQueryDto queryDto);

    /**
     * 新增资产
     *
     * @param asset 资产录入
     * @return 结果
     */
    @PostMapping(value = "server/means/asset")
    AssetDto add(AssetDto asset);

    @PostMapping("server/means/asset/buildAssetLog")
    String buildAssetLog(@RequestBody BuildAssetLogDto logDto);

    /**
     * 批量资产
     *
     * @param asset 资产录入
     * @return 结果
     */
    @PutMapping(value = "server/means/asset")
    Boolean edit(@RequestBody AssetDto asset);

    /**
     * 批量编辑资产
     *
     * @param assetBatchDto 资产批量录入
     * @return 结果
     */
    @PutMapping(value = "server/means/asset/batch")
    List<AuditableOperateResult> editBatch(@RequestBody AssetBatchDto assetBatchDto);

    /**
     * 分页查询
     *
     * @param queryDto 查询条件
     * @return 结果
     */
    @PostMapping(value = "server/means/asset/page/pc")
    PageUtils<AssetDto> pcPage(@RequestBody AssetQueryConditionDto queryDto);

    /**
     * 分页查询
     *
     * @param queryDto 查询条件
     * @return 结果
     */
    @PostMapping(value = "server/means/asset/page/app")
    PageUtils<AssetAppPageDto> appPage(@RequestBody AssetQueryConditionDto queryDto);

    @GetMapping("server/means/asset/emp/{empId}")
    List<AssetDto> empAssetList(@PathVariable("empId") Long empId);

    /**
     * 批量复制
     *
     * @param copyDto 复制实体
     * @return 结果
     */
    @PostMapping(value = "server/means/asset/copy")
    List<AuditableOperateResult> copy(AssetCopyDto copyDto);


    /**
     * 根据资产状态获取对应的操作
     *
     * @param ids 资产ids
     * @return 对应的操作list
     */
    @PostMapping(value = "server/means/asset/getAssetOptByAssetId")
    List<AssetOperationDto> getAssetOptByAssetId(List<Long> ids);

    /**
     * 查询全部资产操作
     *
     * @return 返回操作list
     */
    @GetMapping(value = "server/means/asset/allAssetOpt")
    List<AssetOperationDto> allAssetOpt();

    /**
     * 删除资产信息
     *
     * @param assetId 资产ID
     * @return 结果
     */
    @DeleteMapping(value = "server/means/asset/{assetId}")
    AuditableOperateResult removeById(@PathVariable("assetId") Long assetId);

    /**
     * 批量删除资产信息
     *
     * @param assetIds 资产IDs
     * @return 结果
     */
    @DeleteMapping(value = "server/means/asset")
    List<AuditableOperateResult> remove(@RequestBody List<Long> assetIds);

    /**
     * 金额统计
     *
     * @param queryDto 查询条件
     * @return 结果
     */
    @PostMapping(value = "server/means/asset/amountMoney")
    String amountMoney(@RequestBody AssetQueryConditionDto queryDto);

    /**
     * 导入资产数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "server/means/asset/saveSheetData")
    Boolean saveSheetData(AssetImportDto importDto);

    /**
     * 导入资产数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "server/means/asset/saveEditSheetData")
    Boolean saveEditSheetData(AssetImportDto importDto);

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "server/means/asset/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    /**
     * 删除全部
     */
    @DeleteMapping(value = "server/means/asset/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    /**
     * 查询导入错误数据
     *
     * @return 错误数据
     */
    @GetMapping(value = "server/means/asset/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    /**
     * 根据资产编码或资产phpId获取资产详情-不带权限过滤
     * @param assetCode 资产编码或phpId
     * @return
     */
    @GetMapping(value = "server/means/asset/noPermCodePhp")
    AssetDto getInfoNoPermByCodePhp(@RequestParam(value = "assetCode") String assetCode);

    @GetMapping("server/means/asset/checkManageAsset/{userId}")
    Boolean checkManageAsset(@PathVariable("userId") Long userId);

    @GetMapping("server/means/asset/checkUseAsset/{userId}")
    Boolean checkUseAsset(@PathVariable("userId") Long userId);

    @GetMapping("server/means/asset/list/byArea/{areaId}/{range}")
    AppAssetAreaDto listByArea(@PathVariable("areaId") Long areaId, @PathVariable(value = "range", required = false) Integer range);

    @PostMapping("server/means/asset/stata/byArea")
    AppAssetAreaStatDto statByArea(@RequestBody SearchAppAssetAreaDto dto);

    @PostMapping("server/means/asset/page/byArea")
    PageUtils<AssetValue> pageByArea(@RequestBody SearchAppAssetAreaDto dto);

    @GetMapping("server/means/asset/tag/attrList")
    TagAttrListDto getTagAttrList(@RequestParam(value = "kw", required = false) String kw);

    @GetMapping("server/means/asset/sortField")
    QueryConditionSortDto sortField();

    @PostMapping("server/means/asset/images/check/{action}")
    List<ImportImages> assetImagesCheck(@RequestBody List<ImportImages> images, @PathVariable("action") Integer action, @RequestHeader("Authorization") String token);

    @PostMapping("server/means/asset/images/import/{action}")
    List<ImportImages> assetImagesModify(@RequestBody List<ImportImages> images, @PathVariable("action") Integer action, @RequestHeader("Authorization") String token);

    @PostMapping("server/means/asset/quickSearch")
    List<AssetDto> quickSearch(AssetQuickSearchDto quickSearchDto);
}
