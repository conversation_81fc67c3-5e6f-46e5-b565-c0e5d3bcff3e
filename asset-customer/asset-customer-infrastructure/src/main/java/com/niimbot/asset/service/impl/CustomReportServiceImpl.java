package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.CustomReportService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.report.CustomReportFeignClient;
import com.niimbot.asset.utils.ExcelExportUtil;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.system.ImportTaskDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/6 上午10:35
 */
@Slf4j
@Service
public class CustomReportServiceImpl extends AbstractExcelExportService implements CustomReportService {

    private final ImportTaskFeignClient importTaskFeignClient;

    @Autowired
    private CustomReportFeignClient customReportFeignClient;
    @Autowired
    private ExcelExportUtil excelExportUtil;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private FileUploadConfig fileUploadConfig;

    protected CustomReportServiceImpl(ImportTaskFeignClient importTaskFeignClient) {
        super(importTaskFeignClient);
        this.importTaskFeignClient = importTaskFeignClient;
    }

    @Override
    public ExportResponse exportReportDetail(CustomReportConfigDto customReportConfigDto) {
        //查询报表配置信息
        CustomReportConfigDto reportConfig = customReportFeignClient.queryReportConfig(customReportConfigDto.getId());
        if (Objects.isNull(reportConfig)) {
            BusinessExceptionUtil.throwException("明细报表配置信息不存在, 无法导出");
        }

        //报表字段
        customReportConfigDto.setDimension(reportConfig.getDimension());
        customReportConfigDto.setReportName(reportConfig.getReportName());

        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(customReportConfigDto, OrderTypeNewEnum.CUSTOM_REPORT),
                OrderTypeNewEnum.CUSTOM_REPORT.getValue());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(customReportConfigDto)));
        exportParams.setExportUrl(OrderTypeNewEnum.CUSTOM_REPORT.getDetailExportUrl());

        //提前探下数据量，调用分页接口，查询数据总量，超过1000条，异步导出
        int startPage = 1;
        customReportConfigDto.setCurrent(startPage);
        customReportConfigDto.setSize(1L);
        AssetStatisticsDataDto statisticsDataDto = customReportFeignClient.queryDetailReport(customReportConfigDto);
        if (Objects.isNull(statisticsDataDto) || statisticsDataDto.getTotalCount() == 0) {
            BusinessExceptionUtil.throwException("明细报表无数据, 无法导出");
        }
        //设置导出总数据量
        exportParams.setCount(statisticsDataDto.getTotalCount());

        //大于1000条，异步导出
        if (statisticsDataDto.getTotalCount() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeReportDetail);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeReportDetail(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    /**
     * 导出excel，并上传到oss
     * @param taskId
     * @param exportParams
     * @return
     */
    private String executeReportDetail(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;

        //导出参数
        CustomReportConfigDto customReportConfigDto =
                JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), CustomReportConfigDto.class);

        //需要进行转换获取名称
        List<String> translateFieldCode = ListUtil.of("status", "approveStatus", "grantStatus");

        //获取导出表头
        LinkedHashMap<String, String> header = new LinkedHashMap<>();
        for (DimensionItemDto dimensionItemDto : customReportConfigDto.getDimension()) {
            //状态描述，需要换个字段获取
            if (translateFieldCode.contains(dimensionItemDto.getCode())) {
                header.put(dimensionItemDto.getCode() + "Text", dimensionItemDto.getName());
            } else {
                header.put(dimensionItemDto.getCode(), dimensionItemDto.getName());
            }
        }

        //文件名称
        String fileName = String.join("", customReportConfigDto.getReportName(),
                DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN), ".xlsx");

        File downloadFile = null;
        try {
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);

            ExcelWriter writer = excelExportUtil.createWriter(header);

            //分页导出，初始化从第一页开始导出，并且设置每页大小1000
            int startPage = 1;
            customReportConfigDto.setCurrent(startPage);
            customReportConfigDto.setSize(100L);

            AssetStatisticsDataDto statisticsDataDto = customReportFeignClient.queryDetailReport(customReportConfigDto);
            while (Objects.nonNull(statisticsDataDto) && CollUtil.isNotEmpty(statisticsDataDto.getList())) {
                //写数据
                excelExportUtil.writeData(writer, header, statisticsDataDto.getList());

                startPage++;
                customReportConfigDto.setCurrent(startPage);
                statisticsDataDto = customReportFeignClient.queryDetailReport(customReportConfigDto);
            }

            //设置输出文件路径
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("customerReportService executeReportDetail exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    /**
     * 获取导出报表名称，在任务中心展示
     * @param customReportConfigDto
     * @param orderType
     * @return
     */
    private String getName(CustomReportConfigDto customReportConfigDto, OrderTypeNewEnum orderType) {
        // 获取流水号
        int count = importTaskFeignClient.count(DictConstant.TASK_TYPE_EXPORT, orderType.getValue());
        String serialNo = StrUtil.padPre(String.valueOf(++count), 5, "0");
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return String.join("", customReportConfigDto.getReportName(), currentTime, serialNo);
    }

    /**
     * 获取导出报表本地磁盘文件夹
     * @return
     */
    private File getFilePath() {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", "customer_report", currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    /**
     * 获取oss路径
     * @param fileName
     * @param exportParams
     * @return
     */
    private String getOssPath(String fileName, ExportParams exportParams) {
        return String.join("/", String.valueOf(exportParams.getCompanyId()), "customer_report", DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN), fileName);
    }
}
