package com.niimbot.asset.controller.common.material;

import com.google.common.net.HttpHeaders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialExcelEditService;
import com.niimbot.asset.service.MaterialExcelService;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.asset.service.feign.MaterialOperationFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentSparePartsFeignClient;
import com.niimbot.asset.service.http.ProductMarketHttpClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.equipment.SparePartsMaterialDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialDto;
import com.niimbot.material.MaterialOperationDto;
import com.niimbot.material.MaterialQueryDto;
import com.niimbot.material.ProductDto;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.means.ExportAssetHeadDto;
import com.niimbot.means.ReImportDto;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.AsCusUserSettingDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.CusMenuDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.apache.catalina.manager.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/7/7 9:08
 */
@Slf4j
@Api(tags = "【耗材】耗材档案")
@ResultController
@RequestMapping("api/common/material")
@Validated
public class MaterialController {

    private final MaterialFeignClient materialFeignClient;
    private final MaterialService materialService;
    private final MaterialExcelService materialExcelService;
    private final MaterialExcelEditService materialEditExcelService;
    private final RedisService redisService;
    private final CusUserSettingFeignClient settingFeignClient;
    private final ProductMarketHttpClient productMarketHttpClient;
    private final FormFeignClient formFeignClient;
    private final MaterialQueryFieldService queryFieldService;

    @Autowired
    private EquipmentSparePartsFeignClient equipmentSparePartsFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private CusMenuFeignClient menuFeignClient;
    @Autowired
    private MaterialOperationFeignClient materialOperationFeignClient;

    @Autowired
    private AbstractFileUtils fileUtils;

    @Autowired
    public MaterialController(MaterialFeignClient materialFeignClient,
                              MaterialService materialService,
                              MaterialExcelService materialExcelService,
                              MaterialExcelEditService materialEditExcelService,
                              RedisService redisService,
                              CusUserSettingFeignClient settingFeignClient,
                              ProductMarketHttpClient productMarketHttpClient,
                              FormFeignClient formFeignClient,
                              MaterialQueryFieldService queryFieldService) {
        this.materialFeignClient = materialFeignClient;
        this.materialService = materialService;
        this.materialExcelService = materialExcelService;
        this.materialEditExcelService = materialEditExcelService;
        this.queryFieldService = queryFieldService;
        this.redisService = redisService;
        this.settingFeignClient = settingFeignClient;
        this.productMarketHttpClient = productMarketHttpClient;
        this.formFeignClient = formFeignClient;
    }

    @ApiOperation(value = "查询耗材分页列表")
    @PostMapping(value = "/page")
    public PageUtils<JSONObject> page(@RequestBody MaterialQueryDto queryDto) {
        //设备关联备件，选择备件时，需要过滤掉已选择的耗材
        if (Objects.nonNull(queryDto.getAssetId())) {
            List<SparePartsMaterialDto> sparePartsMaterialList = equipmentSparePartsFeignClient.queryMaterialInfo(queryDto.getAssetId());
            if (CollUtil.isNotEmpty(sparePartsMaterialList)) {
                queryDto.getExcludeMaterialIds().addAll(sparePartsMaterialList.stream().map(SparePartsMaterialDto::getMaterialId).collect(Collectors.toList()));
            }
        }
        PageUtils<JSONObject> materialPage = materialService.materialPage(queryDto);
        //数据脱敏处理
        if (Objects.nonNull(materialPage) && CollUtil.isNotEmpty(materialPage.getList())) {
            desensitizationDataUtil.handleSensitiveField(materialPage.getList(), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return materialPage;
    }

    @ApiOperation(value = "查询耗材导出资产动态列")
    @GetMapping(value = "/export/head")
    public List<ExportAssetHeadDto> assetExportHead(
            @ApiParam(name = "standardId", value = "标准品ID")
            @RequestParam(value = "standardId", required = false) Long standardId) {
        List<AssetHeadDto> headDtos = queryFieldService.exportHeadField(standardId);
        List<ExportAssetHeadDto> result = headDtos.stream().map(f -> BeanUtil.copyProperties(f, ExportAssetHeadDto.class))
                .collect(Collectors.toList());
        AsCusUserSettingDto setting = settingFeignClient.getSetting();
        if (setting != null) {
            List<String> exportField = setting.getMaterialExportField();
            if (CollUtil.isNotEmpty(exportField)) {
                result.forEach(f ->
                        f.setCheck(exportField.contains(f.getCode()))
                );
            }
        }
        return result;
    }

    @ApiOperation(value = "耗材详情")
    @GetMapping(value = "/{materialId}")
    public JSONObject getInfo(@PathVariable(value = "materialId") Long materialId,
                              @ApiParam(name = "isEdit", value = "是否编辑")
                              @RequestParam(value = "isEdit", required = false, defaultValue = "false") Boolean isEdit) {
        JSONObject result = materialService.getInfo(materialId);
        //数据脱敏处理
        if (!isEdit && Objects.nonNull(result)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return result;
    }

    @ApiOperation(value = "耗材详情[带库存]")
    @GetMapping(value = "/{repositoryId}/{materialId}")
    public JSONObject getInfo(@PathVariable(value = "repositoryId") Long repositoryId,
                              @PathVariable(value = "materialId") Long materialId) {
        JSONObject result = materialService.getInfo(repositoryId, materialId);
        //敏感数据处理
        if (Objects.nonNull(result)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return result;
    }

    @ApiOperation(value = "自动获取耗材编码")
    @GetMapping(value = "/materialCode")
    public String getMaterialCode() {
        return "系统自动生成";
    }

    @ApiOperation(value = "新增耗材数据")
    @RepeatSubmit
    @PostMapping()
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public MaterialDto add(@RequestBody @Validated({Insert.class}) MaterialDto materialDto) {
        MaterialDto result = materialService.add(materialDto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.ADD_MRL, result));
        return result;
    }

    @ApiOperation(value = "编辑耗材数据")
    @PutMapping()
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_MRL)
    public Boolean edit(@RequestBody @Validated({Update.class}) MaterialDto materialDto) {
        return materialService.edit(materialDto);
    }

    @ApiOperation(value = "耗材删除")
    @DeleteMapping()
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> materialIds) {
        List<AuditableOperateResult> results = materialFeignClient.remove(materialIds, true);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MRL, results));
        return CollUtil.isNotEmpty(results);
    }

    @ApiOperation("耗材删除检查，true 可以删除 false 不可删除")
    @PostMapping("/removePre")
    public Boolean removePre(@RequestBody List<Long> ids) {
        return materialFeignClient.removePre(ids);
    }

    @ApiOperation(value = "扫码获取耗材详情")
    @GetMapping(value = "/scan/{id}")
    public JSONObject getInfoById(@PathVariable("id") String id) {
        return materialService.getInfoNoPerm(id);
    }

    @ApiOperation(value = "商品库扫码查询")
    @GetMapping(value = "/product/scan")
    public ProductDto productScan(@ApiParam(name = "barcode", value = "编码")
                                  @RequestParam("barcode") String barcode) {
        try {
            // 查缓存
            ProductDto cache = (ProductDto) redisService.hGet(RedisConstant.PRODUCT_LIBRARY, barcode);
            // 缓存没有查询接口
            if (cache == null) {
                Map<String, Object> scan = productMarketHttpClient.productScan(barcode);
                Object result = scan.get("result");
                cache = JSON.parseObject(JSON.toJSONString(result), ProductDto.class);
                // 写缓存
                redisService.hSet(RedisConstant.PRODUCT_LIBRARY, barcode, cache, 7, TimeUnit.DAYS);
            }
            return cache;
        } catch (Exception e) {
            log.warn("商品库查询失败, barCode = {}", barcode, e);
        }
        return null;
    }

    @ApiOperation(value = "导出耗材模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response,
                               @ApiParam(name = "standardId", value = "标准品ID")
                               @RequestParam(value = "standardId", required = false) Long standardId) {
        try (OutputStream out = response.getOutputStream()) {
            String fileName = "耗材导入模板.xlsx";
            if (standardId != null) {
                FormVO standard = formFeignClient.getByFormId(new FormByIdQry(standardId,
                        ListUtil.of(1L, LoginUserThreadLocal.getCompanyId())));
                if (standard != null) {
                    fileName = standard.getFormName() + "-" + fileName;
                }
            }
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");

            ExcelWriter writer = materialExcelService.buildExcelWriter(standardId);
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "导入耗材模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public void importTemplate(@ApiParam(name = "standardId", value = "标准品ID")
                               @RequestParam(value = "standardId", required = false) Long standardId,
                               @RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "文件名超长");
        }

        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            materialExcelService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId, standardId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return materialExcelService.importError(taskId);
    }

    @ApiOperation(value = "重新导入错误数据查询")
    @GetMapping(value = "/reImport/{taskId}")
    public ReImportDto reImport(@PathVariable("taskId") Long taskId) {
        return materialExcelService.reImport(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        materialExcelService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.MATERIAL));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation(value = "导出")
    @PostMapping("/export")
    public void export(@RequestBody MaterialQueryDto queryDto, HttpServletResponse response) {
        try {
            List<QueryConditionDto> queryCondition = queryFieldService.materialAllHeadField();
            List<QueryConditionDto> conditions = queryDto.getConditions();
            Long standardId = -1L;
            if (CollUtil.isNotEmpty(conditions)) {
                Optional<QueryConditionDto> standard = conditions.stream()
                        .filter(f -> f.getType().equals(QueryFieldConstant.MATERIAL_FILED_STANDARD))
                        .findFirst();
                if (standard.isPresent()) {
                    QueryConditionDto conditionDto = standard.get();
                    standardId = Convert.toLong(conditionDto.getQueryData(), -1L);
                    QueryConditionStandardDto standardDto = queryFieldService.standardAllField(standardId, false, true);
                    queryCondition.addAll(standardDto.getConditions());
                }
            }
            queryCondition = queryCondition.stream()
                    .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                    .filter(f -> queryDto.getExpCodes().contains(f.getCode()))
                    .collect(Collectors.toList());
            // 查询head
            LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
            for (QueryConditionDto condition : queryCondition) {
                headerData.put(condition.getCode(), condition.getName());
            }
            // 查询数据
            List<JSONObject> excel = materialExcelService.getExcelData(queryDto, standardId);
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.MATERIAL.getCode());
            Set<String> keySet = headerData.keySet();
            excel.forEach(json -> keySet.forEach(key -> json.putIfAbsent(key, null)));
            String fileName = "耗材档案-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MRL));
            settingFeignClient.updateSimplify(new AsCusUserSettingDto()
                    .setMaterialExportField(queryDto.getExpCodes()));
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "导出耗材编辑模板")
    @GetMapping(value = "/exportEditTemplate")
    public void exportEditTemplate(HttpServletResponse response) {
        try (OutputStream out = response.getOutputStream()) {
            String fileName = "耗材编辑模板.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");

            ExcelWriter writer = materialEditExcelService.buildEditExcelWriter();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "导入耗材编辑模板")
    @PostMapping(value = "/importEditTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public void importEditTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MaterialResultCode.MATERIAL_IMPORT_ERROR, "文件名超长");
        }

        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            materialEditExcelService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId);
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MRL_UPT));
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误编辑数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importEditError/{taskId}")
    public ImportDto importEditError(@PathVariable("taskId") Long taskId,
                                     @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        materialEditExcelService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_EDIT, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.MATERIAL_EDIT));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }


    @ApiOperation(value = "耗材列表搜索配置")
    @PostMapping(value = "/listConfig")
    public Boolean updateConfig(@RequestBody List<String> configDto) {
        return settingFeignClient.updateSimplify(new AsCusUserSettingDto().setMaterialSearchField(new HashSet<>(configDto).stream().collect(Collectors.toList())));
    }

    @ApiOperation(value = "耗材列表搜索配置查询")
    @GetMapping(value = "/listConfig")
    public List<String> listConfig() {
        AsCusUserSettingDto setting = settingFeignClient.getSetting();
        if (setting != null && setting.getMaterialSearchField() != null) {
            return setting.getMaterialSearchField();
        } else {
            settingFeignClient.saveOrUpdate(new AsCusUserSettingDto().setMaterialSearchField(ListUtil.of("materialName", "materialCode")));
            return ListUtil.of("materialName", "materialCode");
        }
    }

    @ApiOperation(value = "查询全部耗材操作")
    @GetMapping(value = "/allMaterialOpt")
    public List<MaterialOperationDto> allMaterialOpt() {
        AppCusMenuDto appCusMenuDto = menuFeignClient.userMenuAppList();
        List<CusMenuDto> menus = appCusMenuDto.getMenus();
        List<String> menuCodes = menus.stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        menuCodes.add("material_delete");
        List<MaterialOperationDto> materialOperationList = materialOperationFeignClient.allType();
        return materialOperationList.stream()
                .filter(f -> menuCodes.contains(f.getCode()))
                .peek(f -> f.setIcon(fileUtils.convertToDownloadUrl(f.getIcon())))
                .collect(Collectors.toList());
    }

}
