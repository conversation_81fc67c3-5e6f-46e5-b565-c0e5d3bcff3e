package com.niimbot.asset.service.feign;

import com.niimbot.means.AssetQueryViewDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资产查询视图
 *
 * <AUTHOR>
 * @date 2021/12/7 10:44
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetQueryViewFeignClient {
    /**
     * 保存
     *
     * @return
     */
    @PostMapping("server/means/queryView")
    Boolean save(@RequestBody AssetQueryViewDto queryView);

    /**
     * 更新
     *
     * @return
     */
    @PutMapping("server/means/queryView")
    Boolean update(@RequestBody AssetQueryViewDto queryView);

    /**
     * 是否展示
     * @param id
     * @param isShow
     * @return
     */
    @PutMapping("server/means/queryView/{id}/{isShow}")
    Boolean isShow(@PathVariable("id") Long id, @PathVariable("isShow") Boolean isShow);

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("server/means/queryView/{id}")
    Boolean delete(@PathVariable("id") Long id);

    /**
     * 详情
     * @param id
     * @return
     */
    @GetMapping("server/means/queryView/{id}")
    AssetQueryViewDto getById(@PathVariable("id") Long id);

    /**
     * 排序
     * @param ids
     * @return
     */
    @PutMapping("server/means/queryView/sort")
    Boolean sort(@RequestBody List<Long> ids);

    /**
     * 数据配置列表
     *
     * @return
     */
    @GetMapping(value = "server/means/queryView/list/config")
    List<AssetQueryViewDto> configList();

    /**
     * tab展示列表
     *
     * @return
     */
    @GetMapping(value = "server/means/queryView/list/tab")
    List<AssetQueryViewDto> tabList();
}
