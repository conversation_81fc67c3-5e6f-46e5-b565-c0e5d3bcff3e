package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.EquipmentMaintainOrderService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.equipment.MaintainTaskDto;
import com.niimbot.equipment.MaintainTaskQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.ImportTaskDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/19 14:48
 */
@Slf4j
@Service
public class EquipmentMaintainOrderServiceImpl extends AbstractExcelExportService implements EquipmentMaintainOrderService {

    private final ImportTaskFeignClient importTaskFeignClient;

    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private EquipmentOrderQueryFieldServiceImpl orderQueryFieldService;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private FileUploadConfig fileUploadConfig;

    protected EquipmentMaintainOrderServiceImpl(ImportTaskFeignClient importTaskFeignClient) {
        super(importTaskFeignClient);
        this.importTaskFeignClient = importTaskFeignClient;
    }

    @Getter
    @AllArgsConstructor
    private static class OrderExportData extends AbsExportData {
        private Long orderId;
        private JSONObject orderData;
    }

    @Override
    public ExportResponse exportMaintainTask(List<MaintainTaskDto> list, MaintainTaskQueryDto query) {
        Integer orderType = EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK;
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        ExportResponse exportResponse = new ExportResponse();
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(orderType, "list"), orderType);
        exportParams.setQueryCondition(JsonUtil.toJsonObject(query));
        exportParams.setExportUrl(OrderTypeNewEnum.getByType(orderType).getDetailExportUrl());

        List<JSONObject> taskList = new ArrayList<>();
        for (MaintainTaskDto maintainTaskDto : list) {
            JSONObject toJSONObject = maintainTaskDto.toJSONObject();
            taskList.add(toJSONObject);
        }
        //敏感数据处理
        desensitizationDataUtil.handleSensitiveField(taskList, SensitiveObjectTypeEnum.ASSET.getCode());

        List<AbsExportData> exportDataList = taskList.stream().map(json ->
                new OrderExportData(json.getLong("id"), json)
        ).collect(Collectors.toList());
        exportParams.setExportDataList(exportDataList);
        if (list.size() > 1000) {
            exportResponse.setSync(true);
            sync(exportParams, this::executeOrderExport);
        } else {
            exportResponse.setSync(false);
            exportResponse.setPath(async(() ->
                    executeOrderExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(exportResponse.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return exportResponse;
    }

    private String executeOrderExport(Long taskId, ExportParams exportParams) {
        String path = StrUtil.EMPTY;
        File tempPath = getTempPath(3);
        Integer orderType = exportParams.getOrderType();

        ExportOrderAssetsHeaderDto orderHeader = getOrderHeader(exportParams.getOrderType());
        LinkedHashMap<String, String> headerData = orderHeader.getHeader();
        Map<String, AssetHeadDto> codeTypeMap = orderHeader.getCodeTypeMap();
        File outputFile = null;
        try {
            String excelName = getOrderExcelName(exportParams.getOrderType());
            outputFile = new File(tempPath.getPath() + "/" + excelName);
            // 本地文件路径
            String localPath = outputFile.getPath();
            int headLen = headerData.size();
            ExcelWriter writer = ExcelUtil.getWriter(true);
            Sheet sheet = writer.getSheet();
            // 设置边框
            StyleSet styleSet = writer.getStyleSet();
            styleSet.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
            styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.TOP);

            // 设置表头的cellStyle
            CellStyle cellStyle = writer.createCellStyle();
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);

            // 写入文件标题
            String headerText = String.format("%s单",
                    OrderTypeNewEnum.getByType(orderType).getName());
            writer.merge(0, 0, 0, headLen - 1, headerText, false);
            Cell title = writer.getCell(0, 0);
            CellStyle commentStyle = writer.createCellStyle();
            commentStyle.setAlignment(HorizontalAlignment.CENTER);
            commentStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
            title.setCellStyle(commentStyle);

            // 写入表头
            AtomicInteger rowIdx = new AtomicInteger(1);
            List<String> headCodeList = new ArrayList<>();
            AtomicInteger cellIdx = new AtomicInteger(0);
            Row header = writer.getOrCreateRow(rowIdx.getAndIncrement());
            headerData.forEach((k, v) -> {
                int idx = cellIdx.getAndIncrement();
                headCodeList.add(k);
                Cell cell = header.createCell(idx);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(v);
                // 调整每一列宽度
                sheet.autoSizeColumn((short) idx);
                // 解决自动设置列宽中文失效的问题
                sheet.setColumnWidth(idx, sheet.getColumnWidth(idx) * 17 / 10);
            });

            for (AbsExportData absExportData : exportParams.getExportDataList()) {
                OrderExportData exportData = (OrderExportData) absExportData;
                JSONObject orderData = exportData.getOrderData();
                int idx = rowIdx.getAndIncrement();
                Row row = writer.getOrCreateRow(idx);
                for (int i = 0; i < headCodeList.size(); i++) {
                    String code = headCodeList.get(i);
                    Cell cell = row.createCell(i);
                    cell.setCellStyle(cellStyle);

                    AssetHeadDto assetHeadDto = codeTypeMap.get(code);
                    if (assetHeadDto == null) {
                        continue;
                    }
                    if (FormFieldCO.NUMBER_INPUT.equals(assetHeadDto.getType())) {
                        Double num = Convert.toDouble(orderData.get(code));
                        if (num != null) {
                            cell.setCellValue(num);
                        } else {
                            String numStr = Convert.toStr(orderData.get(code));
                            if (StrUtil.isNotEmpty(numStr)) {
                                cell.setCellValue(numStr);
                            }
                        }
                    } else if (FormFieldCO.DATETIME.equals(assetHeadDto.getType())) {
                        String date = Convert.toStr(orderData.get(code));
                        if (StrUtil.isNotEmpty(date)) {
                            try {
                                String fmt = "yyyy-MM-dd";
                                if (assetHeadDto.getFieldProps() != null) {
                                    fmt = assetHeadDto.getFieldProps().getString("dateFormatType");
                                }

                                LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                                cell.setCellValue(dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                            } catch (Exception e) {
                                log.warn("[{}] [{}]转换时间异常", code, date);
                            }
                        }
                    } else if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(assetHeadDto.getType())) {
                        try {
                            JSONArray jsonArray = orderData.getJSONArray(code);
                            if (CollUtil.isNotEmpty(jsonArray)) {
                                List<String> strings = jsonArray.toJavaList(String.class);
                                cell.setCellValue(String.join(",", strings));
                            }
                        } catch (Exception e) {
                            log.warn("[{}]转换数组异常", code);
                        }
                    } else {
                        String str = Convert.toStr(orderData.get(code));
                        if (StrUtil.isNotEmpty(str)) {
                            cell.setCellValue(str);
                        }
                    }
                }
            }

            //设置输出文件路径
            writer.setDestFile(outputFile);
            writer.flush();
            writer.close();

            // 上传文件到oss 单个文件或者zip包
            path = fileUploadService.putFile(getOrderDestPath(exportParams), localPath);
            // 更新任务url
            if (taskId != null && StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
//            AuditLogs.sendOrderExportRecord(exportParams.getOrderType(), exportParams.getExportDataList().size(), Auditable.Action.EXP_OR_LT);
        } catch (Exception e) {
            log.error("设备保养任务列表导出失败, {}", e.getMessage(), e);
        } finally {
            FileUtil.del(outputFile);
        }
        return path;
    }

    private ExportOrderAssetsHeaderDto getOrderHeader(Integer orderType) {
        if (orderType.equals(EquipmentConstant.ORDER_TYPE_EQUIPMENT_MAINTAIN_TASK)) {
            List<AssetHeadDto> headView = orderQueryFieldService.orderHeadView(orderType);
            // 查询head
            LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
            Map<String, AssetHeadDto> codeTypeMap = new HashMap<>();

            for (AssetHeadDto head : headView) {
                if (StrUtil.isNotBlank(head.getTranslationCode())) {
                    headerData.put(head.getTranslationCode(), head.getName());
                    codeTypeMap.put(head.getTranslationCode(), head);
                } else {
                    headerData.put(head.getCode(), head.getName());
                    codeTypeMap.put(head.getCode(), head);
                }
            }
            return new ExportOrderAssetsHeaderDto()
                    .setOrderHeaderSize(headView.size())
                    .setHeader(headerData)
                    .setCodeTypeMap(codeTypeMap);
        }
        return null;
    }

    private String getOrderExcelName(Integer orderType) {
        String templateStr = "%s单导出";
        String tempName = String.format(templateStr, OrderTypeNewEnum.getByType(orderType).getName());
        return tempName + "(" +
                DateUtil.format(DateUtil.date(), "MMddHHmmss") + ")" + ".xlsx";
    }

    private String getOrderDestPath(ExportParams exportParams) {
        String templateStr = "%s单导出";
        String tempName = String.format(templateStr, OrderTypeNewEnum.getByType(exportParams.getOrderType()).getName());
        String name = tempName + "(" +
                DateUtil.format(DateUtil.date(), "MMddHHmmss") + ")" + ".xlsx";
        return exportParams.getCompanyId() + "/" + exportParams.getOrderTypeCode() + "_order/" + DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN) + "/" + name;
    }

    /**
     * 获取临时存放路径
     *
     * @param type 1-单据卡片 2-报表, 3-单据列表
     */
    private File getTempPath(Integer type) {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        String name;
        if (type == 1) {
            name = "equipmentMaintainCard";
        } else if (type == 2) {
            name = "report";
        } else {
            name = "equipmentMaintain";
        }
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", name, currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    /**
     * 获取任务名称
     *
     * @param orderType 单据类型
     */
    private String getName(Integer orderType, String exportType) {
        // 获取流水号
        int count = importTaskFeignClient.count(DictConstant.TASK_TYPE_EXPORT, orderType);
        String serialNo = StrUtil.padPre(String.valueOf(++count), 5, "0");
        String templateStr;
        if ("card".equals(exportType)) {
            templateStr = "%s卡片导出（%s-%s）";
        } else {
            templateStr = "%s单导出（%s-%s）";
        }

        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return String.format(templateStr, OrderTypeNewEnum.getByType(orderType).getName(), currentTime, serialNo);
    }

    @Data
    @Accessors(chain = true)
    private static class GlobalCache {
        private Long company;
        private String downUrl;
        private Integer orderType;
    }

    @Data
    @Accessors(chain = true)
    private static class ExportOrderAssetsHeaderDto {

        private Integer orderHeaderSize;

        private LinkedHashMap<String, String> header;

        private Map<String, AssetHeadDto> codeTypeMap;
    }


}
