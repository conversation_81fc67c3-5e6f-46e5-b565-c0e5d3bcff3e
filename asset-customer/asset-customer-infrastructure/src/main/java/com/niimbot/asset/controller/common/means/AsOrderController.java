package com.niimbot.asset.controller.common.means;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.*;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.OrderService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.*;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import com.niimbot.system.Auditable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 单据控制器
 *
 * <AUTHOR>
 * @since 2020-12-24
 */
@Slf4j
@Api(tags = "单据管理")
@ResultController
@RequestMapping("api/common/assetOrder")
@RequiredArgsConstructor
@Validated
public class AsOrderController {

    private final AsOrderFeignClient orderFeignClient;
    private final RepairReportOrderFeignClient repairReportOrderFeignClient;
    private final RepairOrderFeignClient repairOrderFeignClient;

    private final AsOrderUtil asOrderUtil;
    private final AssetService assetService;
    private final ActWorkflowFeignClient workflowFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final OrderService orderService;
    private final CacheResourceUtil cacheResourceUtil;
    @Autowired
    protected FormFeignClient formFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @ApiOperation(value = "单据分页列表")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody AsOrderQueryDto dto) {
        PageUtils<AsOrderDto> page = orderFeignClient.page(dto);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (AsOrderDto orderDto : page.getList()) {
            JSONObject orderJson = asOrderUtil.toJSONObject(orderDto);
            list.add(orderJson);
        }
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.ASSET.getCode());
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "单据分页列表")
    @GetMapping("/page")
    public PageUtils<JSONObject> pageGet(AsOrderQueryDto dto) {
        return page(dto);
    }

    @ApiOperation(value = "设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated AsOrderDto dto) {
        return orderFeignClient.getWorkflowStepList(dto);
    }

    @ApiOperation(value = "单据新增", notes = "新增请传关联资产 id 和json数据")
    @PostMapping
    @RepeatSubmit
    @ResultMessage("提交成功")
    public Boolean insert(@RequestBody @Validated AsOrderSubmitDto dto) {
        List<Long> assetIds = dto.getOrderDto().getAssets().stream().map(AsOrderAssetDto::getId)
                .collect(Collectors.toList());
        if (assetIds.size() > AssetConstant.ORDER_ASSET_MAX) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_MAX_UPPER_LIMIT,
                    String.valueOf(AssetConstant.ORDER_ASSET_MAX));
        }
        List<JSONObject> assetList = assetService.getInfoList(assetIds);
        if (log.isDebugEnabled()) {
            log.debug("--------------提交的资产信息-------------");
            log.debug("assetIds = [{}]", assetIds);
            log.debug("assetList = [{}]", assetList);
            log.debug("--------------提交的资产信息-------------");
        }
        if (CollUtil.isEmpty(assetList) || (CollUtil.isNotEmpty(assetList) && assetList.size() != assetIds.size())) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_LIST_NOT_EXISTS);
        }
        dto.getOrderDto().setOrderNo(null);
        dto.getOrderDto().getAssets().forEach(asOrderAssetDto -> {
            Long id = asOrderAssetDto.getId();
            JSONObject match = assetList.stream().filter(jsonObject ->
                            jsonObject.getLong("id").longValue() == id.longValue())
                    .findFirst().orElse(new JSONObject());
            asOrderAssetDto.setAssetSnapshotData(match);
        });
        dto.getOrderDto().setSummary(asOrderUtil.buildSummary(assetList));
        AuditableCreateOrderResult result = orderFeignClient.insert(dto);
        AuditLogs.sendMeansOrderCreateRecord(result, dto.getOrderDto().getOrderType());
        return true;
    }

    // @ApiOperation(value = "单据详情(编辑使用)")

    /**
     * TODO 前段未使用，保留一段时间后再删除 ouyangbo 2021-06-21
     *
     * @param id
     * @return
     */
    @AutoConvert
    @GetMapping("/{id}")
    public AsOrderDetailDto getById(@PathVariable Long id) {
        AsOrderDto dto = orderFeignClient.getDetail(id);
        dictConvertUtil.convertToDictionary(dto);
        return new AsOrderDetailDto()
                .setOrder(asOrderUtil.toJSONObject(dto));
    }

    @ApiOperation(value = "单据详情")
    @AutoConvert
    @GetMapping("/detail/{orderType}/{id}")
    public AsOrderDetailDto getDetailById(@PathVariable("orderType") Short orderType, @PathVariable("id") Long id) {
        AsOrderDto dto = orderFeignClient.getDetail(id);
        if (ObjectUtil.isNull(dto)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "单据数据不存在");
        }
        dictConvertUtil.convertToDictionary(dto);
        JSONObject orderJson = asOrderUtil.toJSONObject(dto);
        //数据脱敏处理
        if (Objects.nonNull(orderJson)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderJson), SensitiveObjectTypeEnum.ASSET.getCode());
        }
        if (dto.getApproveStatus() == 0) {
            return new AsOrderDetailDto()
                    .setOrder(orderJson)
                    .setApproveInfo(new WorkflowApproveInfoDto());
        }
        WorkflowApproveInfoDto approveInfoDto;
        if (DictConstant.WAIT_APPROVE.equals(dto.getApproveStatus())) {
            approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo(orderType, id);
            if (approveInfoDto == null || CollUtil.isEmpty(approveInfoDto.getExecuteList())) {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo(orderType, id);
            }
        } else {
            approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo(orderType, id);
            if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                approveInfoDto.getExecuteList().stream()
                        .filter(step -> step.getType() == 8)
                        .forEach(step -> {
                            step.setStatus(dto.getApproveStatus());
                            dictConvertUtil.convertToDictionary(step);
                        });
            }
        }
        return new AsOrderDetailDto()
                .setOrder(orderJson)
                .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
    }

    @ApiOperation(value = "单据批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detail/{orderType}")
    public List<AsOrderDetailDto> getDetailByIds(@PathVariable("orderType") Short orderType,
                                                 @RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(Convert.toInt(orderType));
        return requestDto.getOrderIds().stream().map(id -> getDetailById(orderType, id)).collect(Collectors.toList());
    }

    /**
     * 根据资产id获取单据审批详情
     *
     * @param assetId
     * @return
     */
    @ApiOperation(value = "根据资产id获取单据审批详情")
    @GetMapping("/asset/{assetId}")
    public AsOrderInfoDto getByAssetId(@PathVariable Long assetId) {
        // 通用单据
        AsOrderInfoDto orderDto = orderFeignClient.getApproveOrderByAssetId(assetId);
        if (orderDto == null) {
            // 报修单
            orderDto = repairReportOrderFeignClient.getApproveOrderByAssetId(assetId);
            if (orderDto == null) {
                // 维修单
                orderDto = repairOrderFeignClient.getApproveOrderByAssetId(assetId);
            }
        }
        return orderDto;
    }

    @ApiOperation(value = "【PC】导出资产单据卡片")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderCard(@RequestBody @Validated AsOrderQueryDto queryDto) {
        return orderService.exportOrderCard(queryDto);
    }

    @ApiOperation(value = "【PC】导出单据资产")
    @PostMapping(value = "/exportOrder/assets")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderAssets(@RequestBody @Validated AsOrderQueryDto dto) {
        PageUtils<AsOrderDto> page = orderFeignClient.page(dto);
        List<AsOrderDto> list = page.getList();
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        dictConvertUtil.convertToDictionary(list);
        FormVO orderForm = getForm(dto.getOrderType(), false);
        Map<String, String> orderDateFormatType = new HashMap<>();
        Set<String> orderMultiSelectSet = new HashSet<>();
        Map<String, Boolean> orderNumberMap = new HashMap<>();
        for (FormFieldCO it : orderForm.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                orderDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                orderMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                orderNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        orderDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        orderDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        List<Long> orderIds = new ArrayList<>();
        List<JSONObject> orders = new ArrayList<>();
        for (AsOrderDto order : list) {
            orderIds.add(order.getId());
            JSONObject orderJson = asOrderUtil.toJSONObject(order);
            if (Objects.nonNull(order.getCreateTime())) {
                long timeVal = order.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("createTime", timeVal);
                }
            }
            if (Objects.nonNull(order.getUpdateTime())) {
                long timeVal = order.getUpdateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("updateTime", timeVal);
                }
            }
            orderNumberMap.forEach((code, percentage) -> {
                Number number = Convert.toNumber(orderJson.get(code));
                if (ObjectUtil.isNotNull(number)) {
                    if (BooleanUtil.isTrue(percentage)) {
                        orderJson.put(code, number + "%");
                    } else {
                        orderJson.put(code, number);
                    }
                }
            });
            orderDateFormatType.forEach((code, fmt) -> {
                String date = orderJson.getString(code);
                if (StrUtil.isNotEmpty(date)) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                        orderJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换时间异常", code, date);
                    }
                }
            });
            orderMultiSelectSet.forEach(code -> {
                try {
                    JSONArray jsonArray = orderJson.getJSONArray(code);
                    if (CollUtil.isNotEmpty(jsonArray)) {
                        List<String> strings = jsonArray.toJavaList(String.class);
                        String collect = String.join(",", strings);
                        orderJson.put(code, collect);
                    } else {
                        orderJson.put(code, null);
                    }
                } catch (Exception e) {
                    log.warn("[{}] [{}]转换数组异常", code, orderJson.get(code));
                }
            });
            orders.add(orderJson);
        }

        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(orders, SensitiveObjectTypeEnum.ASSET.getCode());

        OrderJsonUtil.convertJsonKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, orders);

        List<AsOrderAssetDto> assets = orderFeignClient.getAssetsByOrderId(orderIds);
        FormVO assetFormVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        Map<String, String> assetDateFormatType = new HashMap<>();
        Set<String> assetMultiSelectSet = new HashSet<>();
        Map<String, Boolean> assetNumberMap = new HashMap<>();
        for (FormFieldCO it : assetFormVO.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                assetDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                assetMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                assetNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        assetDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        assetDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        assetDateFormatType.put(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME, "yyyy-MM-dd");
        Map<Long, List<AsOrderAssetDto>> assetsMap = assets.stream().collect(Collectors.groupingBy(AsOrderAssetDto::getOrderId));
        Map<Long, List<JSONObject>> assetsJsonMap = new HashMap<>();

        Map<String, String> translationCodeMap = assetFormVO.getFormFields().stream()
                .filter(f -> StrUtil.isNotEmpty(f.getTranslationCode()))
                .collect(Collectors.toMap(FormFieldCO::getTranslationCode, FormFieldCO::getFieldCode));

        Map<Long, String> empNameCache = new ConcurrentHashMap<>();
        assetsMap.forEach((key, assetDtoList) -> {
            List<JSONObject> assetJsons = new ArrayList<>();
            for (AsOrderAssetDto assetDto : assetDtoList) {
                JSONObject assetJson = asOrderUtil.toJSONObject(assetDto);
                translationCodeMap.forEach((k, v) -> assetJson.put(v, assetJson.get(k)));
                // 特殊处理创建人，不在表单内
                if (assetJson.containsKey(AssetConstant.ASSET_FIELD_CREATE_BY)) {
                    Long createBy = assetJson.getLong(AssetConstant.ASSET_FIELD_CREATE_BY);
                    if (empNameCache.containsKey(createBy)) {
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, empNameCache.get(createBy));
                    } else {
                        String userNameAndCode = cacheResourceUtil.getUserNameAndCode(createBy);
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, userNameAndCode);
                        empNameCache.put(createBy, userNameAndCode);
                    }
                }
//                assetUtil.translateAssetJsonView(assetJson, assetFormVO.getFormFields());

                assetNumberMap.forEach((code, percentage) -> {
                    Number number = Convert.toNumber(assetJson.get(code));
                    if (ObjectUtil.isNotNull(number)) {
                        if (BooleanUtil.isTrue(percentage)) {
                            assetJson.put(code, number + "%");
                        } else {
                            assetJson.put(code, number);
                        }
                    }
                });
                assetDateFormatType.forEach((code, fmt) -> {
                    String date = assetJson.getString(code);
                    if (StrUtil.isNotEmpty(date)) {
                        try {
                            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                            assetJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                        } catch (Exception e) {
                            log.warn("[{}] [{}]转换时间异常", code, date);
                        }
                    }
                });
                assetMultiSelectSet.forEach(code -> {
                    try {
                        JSONArray jsonArray = assetJson.getJSONArray(code);
                        if (CollUtil.isNotEmpty(jsonArray)) {
                            List<String> strings = jsonArray.toJavaList(String.class);
                            String collect = String.join(",", strings);
                            assetJson.put(code, collect);
                        } else {
                            assetJson.put(code, null);
                        }
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换数组异常", code, assetJson.get(code));
                    }
                });

                assetJsons.add(assetJson);
            }
            assetsJsonMap.put(key, assetJsons);
        });
        return orderService.exportOrderAssets(dto, orders, assetsJsonMap);
    }

    @ApiOperation(value = "查询资产单据表单")
    @GetMapping(value = "/form/{orderType}")
    public FormVO getForm(@PathVariable("orderType") Integer orderType,
                          @RequestParam(value = "singRow", required = false) Boolean singRow) {
        // 查询设置显示的数据
        if (orderType == AssetConstant.ORDER_TYPE_CHANGE) {
            return orderFeignClient.getOrderChangeForm(orderType, singRow);
        }
        return orderFeignClient.getForm(orderType);
    }
    @ApiOperation(value = "查询资产变更单表单[表单设计器专用]")
    @GetMapping(value = "/changeForm")
    public FormVO getChangeForm() {
        // 查询设置显示的数据
        return orderFeignClient.getChangeForm();
    }

    @ApiOperation(value = "查询资产单据表头")
    @GetMapping(value = "/head/{orderType}")
    public List<OrderFormHeadDto> getHead(@PathVariable("orderType") Integer orderType) {
        FormVO form = orderFeignClient.getForm(orderType);
        return form.getFormFields().stream()
                .filter(f -> !AssetConstant.ED_HEAD_NOT_SHOW.contains(f.getFieldType()))
                .map(f -> new OrderFormHeadDto()
                        .setCode(f.getFieldCode())
                        .setType(f.getFieldType())
                        .setName(f.getFieldName())
                        .setTranslationCode(f.getTranslationCode()))
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "处置类型")
    @GetMapping("/disposeType/list")
    public List<String> disposeTypeList() {
        FormVO form = orderFeignClient.getForm(OrderFormTypeEnum.DISPOSE.getCode());
        if (form != null) {
            FormFieldCO fieldCO = form.getFormFields().stream().filter(f -> "disposeType".equals(f.getFieldCode())).findFirst().orElse(null);
            if (fieldCO != null) {
                return fieldCO.getFieldProps().getJSONArray("values").toJavaList(String.class);
            }
        }
        return ListUtil.empty();
    }
}
