package com.niimbot.asset.service.feign;

import com.niimbot.inventory.InventoryApproveDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:22
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface InventoryApproveFeignClient {

    /**
     * 新增审批记录
     *
     * @param dto 审批记录
     * @return 结果
     */
//    @PostMapping(value = "server/inventory/approve/save")
//    Boolean save(InventoryApproveDto dto);

    /**
     * 审批记录列表
     *
     * @param inventoryId 盘点单id
     * @param taskId      盘点任务id
     * @return 审批记录列表
     */
    @GetMapping(value = "server/inventory/approve/list/{inventoryId}/{taskId}")
    List<InventoryApproveDto> list(@PathVariable("inventoryId") Long inventoryId,
                                   @PathVariable("taskId") Long taskId);
}
