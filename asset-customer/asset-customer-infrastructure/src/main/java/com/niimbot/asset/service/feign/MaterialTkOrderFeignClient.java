package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.material.MaterialTkOrderDetailDto;
import com.niimbot.material.MaterialTkOrderDto;
import com.niimbot.material.MaterialTkOrderSubmitDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2022/10/31 16:04
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialTkOrderFeignClient {

    @PostMapping(value = "server/material/order/tk")
    AuditableCreateOrderResult create(MaterialTkOrderSubmitDto submitDto);

    @GetMapping(value = "server/material/order/tk/{id}")
    MaterialTkOrderDto getById(@PathVariable("id") Long id);

    @GetMapping(value = "server/material/order/tk/pageDetail")
    PageUtils<MaterialTkOrderDetailDto> pageDetail(@SpringQueryMap MaterialOrderDetailQueryDto query);

    @GetMapping(value = "server/material/order/tk/detail/{orderId}/{materialId}")
    MaterialTkOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);

    @PostMapping(value = "server/material/order/tk/page")
    PageUtils<MaterialTkOrderDto> page(MaterialOrderQueryDto query);

    @PostMapping(value = "server/material/order/tk/listForExport")
    PageUtils<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto query);

    @PostMapping(value = "server/material/order/tk/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(MaterialTkOrderDto dto);
}
