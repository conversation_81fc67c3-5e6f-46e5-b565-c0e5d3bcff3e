package com.niimbot.asset.service.feign;

import com.niimbot.system.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface RecycleBinFeign {

    @PostMapping("/server/system/recycleBin/recycle")
    Boolean recycle(@RequestBody ResRecycle resRecycle);

    @PostMapping("/server/system/recycleBin/restore")
    Boolean restore(@RequestBody ResRestore resRestore);

    @PostMapping("/server/system/recycleBin/release")
    Boolean release(@RequestBody ResRelease resRelease);

    @GetMapping("/server/system/recycleBin/details")
    RecycleBin details(@SpringQueryMap GetRecycleBins get);

    @GetMapping("/server/system/recycleBin//getByResId")
    RecycleBin getWithResData(@SpringQueryMap GetRecycleBins getRecycleBins);

    @GetMapping("/server/system/recycleBin/ids")
    List<Long> getRecycleIds(@SpringQueryMap GetRecycleBins getRecycleBins);

}
