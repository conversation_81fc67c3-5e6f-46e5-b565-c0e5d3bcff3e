package com.niimbot.asset.service.feign;

import com.niimbot.system.NewbieGuideDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface NewbieGuideFeignClient {

    @PutMapping("/server/system/newbie/guide/click/{id}")
    Boolean click(@PathVariable("id") Long id);

    @GetMapping("/server/system/newbie/guide/all")
    List<NewbieGuideDto> allGuide();

}
