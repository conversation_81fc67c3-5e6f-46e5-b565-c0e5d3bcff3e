package com.niimbot.asset.support;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.support.Executable;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.AuditLogService;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.MaintainPlanFeignClient;
import com.niimbot.maintenance.*;
import com.niimbot.means.AssetDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AuditLogs implements ApplicationContextAware {

    private static AuditLogService auditLogService;

    public static void sendRecord(AuditLogRecord record) {
        Auditable.POOL.execute(() -> auditLogService.sendRecord(record));
    }

    public static void sendRecord(Supplier<AuditLogRecord> supplier) {
        // 异步获取参数的过程 可能会导致 线程池里绑定的内容不一致 提前在主线程中获取用户信息
        LoginUserDto loginUser = LoginUserThreadLocal.get();
        Auditable.POOL.execute(() -> {
            AuditLogRecord params = supplier.get();
            if (Objects.isNull(params) || StrUtil.isBlank(params.getActionCode())) {
                return;
            }
            // 覆盖操作人信息
            params.setCompanyId(loginUser.getCusUser().getCompanyId());
            params.setOperatorId(loginUser.getCusUser().getId());
            params.setTerminal(loginUser.getTerminal());
            auditLogService.sendRecord(params);
        });
    }

    public static void sendRecord(LoginUserDto loginUser, Function<LoginUserDto, AuditLogRecord> function) {
        Auditable.POOL.execute(() -> {
            AuditLogRecord params = function.apply(loginUser);
            if (Objects.isNull(params) || StrUtil.isBlank(params.getActionCode())) {
                return;
            }
            auditLogService.sendRecord(params);
        });
    }

    static void ignoreError(Executable executable) {
        try {
            executable.execute();
        } catch (Exception e) {
            log.warn("AuditLogs Send Error", e);
        }
    }

    public static void sendOrderExportRecord(Integer orderType, Integer count, String actionCode) {
        try {
            OrderTypeNewEnum type = OrderTypeNewEnum.getByType(orderType);
            Map<String, Object> tplMap = new HashMap<>(2);
            String typeName = (type.getName().length() == 2) ? "资产" + type.getName() : type.getName();
            tplMap.put(Auditable.Tpl.TYPE, typeName.contains("单") ? typeName : typeName + "单");
            tplMap.put(Auditable.Tpl.COUNT, count);
            sendRecord(AuditLogRecord.create(actionCode, tplMap));
        } catch (Exception e) {
            log.warn("AuditLogs sendOrderCardExportRecord Error", e);
        }
    }

    public static void sendMeansOrderCreateRecord(AuditableCreateOrderResult result, Integer orderType) {
        ignoreError(() -> {
            AuditLogRecord record = null;
            OrderTypeNewEnum anEnum = OrderTypeNewEnum.getByType(orderType);
            switch (anEnum) {
                case LY:
                    record = AuditLogRecord.create(Auditable.Action.OR_MAS_LY, result);
                    break;
                case TH:
                    record = AuditLogRecord.create(Auditable.Action.OR_MAS_TH, result);
                    break;
                case JY:
                    record = AuditLogRecord.create(Auditable.Action.OR_MAS_JY, result);
                    break;
                case GH:
                    record = AuditLogRecord.create(Auditable.Action.OR_MAS_GH, result);
                    break;
                case DB:
                    record = AuditLogRecord.create(Auditable.Action.OR_MAS_DB, result);
                    break;
                case CZ:
                    record = AuditLogRecord.create(Auditable.Action.OR_MAS_CZ, result);
                    break;
                case BG:
                    record = AuditLogRecord.create(Auditable.Action.OR_MAS_BG, result);
                    break;
                default:
            }
            if (Objects.isNull(record)) {
                return;
            }
            sendRecord(record);
        });
    }

    public static void sendMeansMaintainPlanCreateRecord(MaintainPlanDto dto) {
        LoginUserDto loginUser = LoginUserThreadLocal.get();
        ignoreError(() -> Auditable.POOL.execute(() -> {
            String means = SpringUtil.getBean(AssetFeignClient.class).getInfoList(dto.getAssetIds()).stream().map(v -> v.getAssetData().getString("assetName") + v.getAssetData().getString("assetCode")).collect(Collectors.joining("、"));
            Map<String, String> params = MapBuilder.create(new HashMap<String, String>(2))
                    .put(Auditable.Tpl.CONTENT, means)
                    .put(Auditable.Tpl.MAT_CNT, dto.getPlanContentList().stream().map(MaintainPlanContentDto::getProject).collect(Collectors.joining("、")))
                    .build();
            auditLogService.sendRecord(AuditLogRecord.create(Auditable.Action.ADD_MAS_MAT_PLAN, params, loginUser));
        }));
    }

    public static void sendMeansMaintainPlanEditRecord(MaintainPlanDto dto) {
        LoginUserDto loginUser = LoginUserThreadLocal.get();
        ignoreError(() -> Auditable.POOL.execute(() -> {
            MaintainPlanInfoDto plan = SpringUtil.getBean(MaintainPlanFeignClient.class).info(dto.getId());
            Long meansId = plan.getAssetId();
            AssetDto means = SpringUtil.getBean(AssetFeignClient.class).getInfo(meansId);
            Map<String, String> params = MapBuilder.create(new HashMap<String, String>(2))
                    .put(Auditable.Tpl.CONTENT, means.getAssetData().getString("assetName") + means.getAssetData().getString("assetCode"))
                    .put(Auditable.Tpl.MAT_CNT, dto.getPlanContentList().stream().map(MaintainPlanContentDto::getProject).collect(Collectors.joining("、")))
                    .build();
            auditLogService.sendRecord(AuditLogRecord.create(Auditable.Action.UPT_MEANS_MAT_PLAN, params, loginUser));
        }));
    }

    public static void sendMeansMaintainPlanBatchEditRecord(MaintainPlanEditBatchDto dto) {
        LoginUserDto loginUser = LoginUserThreadLocal.get();
        ignoreError(() -> Auditable.POOL.execute(() -> {
            MaintainPlanQueryDto queryDto = new MaintainPlanQueryDto();
            queryDto.setIds(dto.getIds());
            queryDto.setPageSize(100L);
            List<Long> meansIds = SpringUtil.getBean(MaintainPlanFeignClient.class).page(queryDto).getList().stream().map(MaintainPlanListDto::getAssetId).collect(Collectors.toList());
            String means = SpringUtil.getBean(AssetFeignClient.class).getInfoList(meansIds).stream().map(v -> v.getAssetData().getString("assetName") + v.getAssetData().getString("assetCode")).collect(Collectors.joining("、"));
            Map<String, String> params = MapBuilder.create(new HashMap<String, String>(3))
                    .put(Auditable.Tpl.COUNT, String.valueOf(dto.getIds().size()))
                    .put(Auditable.Tpl.MAT_CNT, dto.getPlanContentList().stream().map(MaintainPlanContentDto::getProject).collect(Collectors.joining("、")))
                    .put(Auditable.Tpl.CONTENT, means)
                    .build();
            auditLogService.sendRecord(AuditLogRecord.create(Auditable.Action.UPT_MEANS_MAT_PLAN_BATCH, params, loginUser));
        }));
    }

    public static Map<String, String> sendMeansMaintainPlanRemoveRecord(List<Long> ids) {
        try {
            MaintainPlanQueryDto queryDto = new MaintainPlanQueryDto();
            queryDto.setIds(ids);
            queryDto.setPageSize(100L);
            List<MaintainPlanListDto> plans = SpringUtil.getBean(MaintainPlanFeignClient.class).page(queryDto).getList();
            String means = SpringUtil.getBean(AssetFeignClient.class).getInfoList(plans.stream().map(MaintainPlanListDto::getAssetId).collect(Collectors.toList())).stream().map(v -> v.getAssetData().getString("assetName") + v.getAssetData().getString("assetCode")).collect(Collectors.joining("、"));
            return MapBuilder.create(new HashMap<String, String>(2))
                    .put(Auditable.Tpl.CONTENT, means)
                    .put(Auditable.Tpl.MAT_CNT, plans.stream().map(MaintainPlanListDto::getPlanContentList).flatMap(Collection::stream).map(MaintainPlanContentDto::getProject).collect(Collectors.joining("、")))
                    .build();
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        auditLogService = applicationContext.getBean(AuditLogService.class);
    }
}
