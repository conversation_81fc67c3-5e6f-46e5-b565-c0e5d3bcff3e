package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.AssetQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.AssetQueryFieldFeignClient;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.CusMenuDto;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionGeneralDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.system.QueryHeadConfigDto;
import com.niimbot.system.QueryTypeDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/9/2 16:05
 */
@Slf4j
@Service
public class AssetQueryFieldServiceImpl implements AssetQueryFieldService {

    @Autowired
    private FormFeignClient formFeignClient;

    @Autowired
    private StandardFeignClient standardFeignClient;

    @Autowired
    private AssetQueryFieldFeignClient assetQueryFieldFeignClient;
    @Autowired
    private CusMenuFeignClient cusMenuFeignClient;

    @Autowired
    private AssetFeignClient assetFeignClient;

    @Autowired
    private AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @Value("${asset.upload.domain}")
    private String domain;

    @Override
    public List<QueryConditionDto> assetAllQueryField() {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_STANDARD));

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }

        // 补齐 标准品，创建人，创建时间，更新时间
        FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);
        QueryConditionDto createBy = toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            queryConditionDtos.add(createBy);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    @Override
    public List<QueryConditionDto> assetSearchAllQueryField() {
        LinkedList<QueryConditionDto> queryConditionDtos = new LinkedList<>(this.assetAllQueryField());
        LinkedList<QueryConditionDto> queryConditionDtosClone = (LinkedList<QueryConditionDto>) queryConditionDtos.clone();
        /**
         * 过滤出的字段
         */
        List<String> excludes = ListUtil.of("standardId","images","files","date");
        List<String> lastExcludes = ListUtil.of("yzc_emp","yzc_org","yzc_area","yzc_asset_cate","yzc_material_cate");
        List<QueryConditionDto> lastList = new ArrayList<>();
        queryConditionDtosClone.forEach(t->{
            if (excludes.contains(t.getType())){
                queryConditionDtos.remove(t);
            }else if(lastExcludes.contains(t.getType())){
                queryConditionDtos.remove(t);
                lastList.add(t);
            }
        });

        QueryConditionDto q1 = new QueryConditionDto();
        q1.setType("fixed");
        q1.setCode("asset_name");
        q1.setName("资产名称");
        QueryConditionDto q2 = new QueryConditionDto();
        q2.setType("fixed");
        q2.setCode("asset_code");
        q2.setName("资产编码");
        queryConditionDtos.addFirst(q1);
        queryConditionDtos.addFirst(q2);
        lastList.forEach(t->{
            if ("createBy".equals(t.getCode())){
                t.setCode("create_by");
            }
            queryConditionDtos.addLast(t);
        });
        return queryConditionDtos;
    }

    @Override
    public List<QueryConditionDto> assetQueryView(Long standardId) {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllQueryField = assetAllQueryField();
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_QUERY);
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });

        // 判断是否有标准品
        if (standardId != null) {
            QueryConditionStandardDto standardDto = standardAllField(standardId, false, true);
            List<QueryConditionDto> conditions = standardDto.getConditions();
            result.addAll(conditions);
        } else if (generalDto.getStandardId() != null && generalDto.getStandardId() > 0L) {
            QueryConditionStandardDto standardDto = standardAllField(generalDto.getStandardId(), false, false);
            Map<String, QueryConditionDto> standardAllFieldMap = standardDto.getConditions()
                    .stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
            generalDto.getStandardCondition().forEach(f -> {
                if (standardAllFieldMap.containsKey(f)) {
                    result.add(standardAllFieldMap.get(f));
                }
            });
        }

        return result;
    }

    @Override
    public Boolean assetHeadField(QueryHeadConfigDto config) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(config.toJson(), QueryFieldConstant.TYPE_ASSET_HEAD));
    }

    @Override
    public QueryHeadConfigDto assetHeadField() {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_HEAD);
        JSON conditionJson = one.getConditions();
        QueryHeadConfigDto headConfigDto = conditionJson.toJavaObject(QueryHeadConfigDto.class);
        if (BooleanUtil.isTrue(one.getSysConfig())) {
            List<String> conditions = headConfigDto.getConditions();
            FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
            List<QueryConditionDto> assetAllHeadField = assetAllHeadField(formVO);
            // 添加 top10
            int len = Math.min(assetAllHeadField.size(), 10);
            AtomicInteger count = new AtomicInteger(len);
            for (QueryConditionDto f : assetAllHeadField) {
                if (!conditions.contains(f.getCode())) {
                    if (count.getAndDecrement() >= 0) {
                        conditions.add(f.getCode());
                    } else {
                        break;
                    }
                }
            }
        }
        return headConfigDto;
    }

    @Override
    public List<QueryConditionDto> assetAllHeadField() {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return assetAllHeadField(formVO);
    }

    public List<QueryConditionDto> assetAllHeadField(FormVO formVO) {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();

        // 补齐 资产状态，创建人，创建时间，更新时间，最近打印时间
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));
        List<FormFieldCO> formFields = formVO.getFormFields();

        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }


    @Override
    public List<AssetHeadDto> assetHeadView() {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        QueryConditionSortDto querySort = assetFeignClient.sortField();
        List<String> querySortList = querySort.getSortList().stream()
                .map(QueryConditionSortDto.Field::getValue).collect(Collectors.toList());

        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllHeadField = assetAllHeadField(formVO);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllHeadField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_HEAD);
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        List<String> selectConditions = generalDto.getConditions();
        selectConditions.forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });
        // 如果是系统的，需要补属性
        if (BooleanUtil.isTrue(configDto.getSysConfig())) {
            // 添加 top10
            int len = Math.min(assetAllHeadField.size(), 10);
            AtomicInteger count = new AtomicInteger(len);
            for (QueryConditionDto f : assetAllHeadField) {
                if (!selectConditions.contains(f.getCode())) {
                    if (count.getAndDecrement() >= 0) {
                        result.add(f);
                    } else {
                        break;
                    }
                }
            }
        }

        Map<String, String> transCodeMap = new HashMap<>();
        transCodeMap.put(QueryFieldConstant.FIELD_CREATE_BY, QueryFieldConstant.FIELD_CREATE_BY + "Text");
        Map<String, JSONObject> fieldPropsMap = new HashMap<>();
        JSONObject dateFormat = new JSONObject();
        dateFormat.put("dateFormatType", "yyyy-MM-dd");
        fieldPropsMap.put(QueryFieldConstant.FIELD_CREATE_TIME, dateFormat);
        fieldPropsMap.put(QueryFieldConstant.FIELD_UPDATE_TIME, dateFormat);
        fieldPropsMap.put(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME, dateFormat);
        for (FormFieldCO formFieldCO : formVO.getFormFields()) {
            fieldPropsMap.put(formFieldCO.getFieldCode(), formFieldCO.getFieldProps());
            if (StrUtil.isNotEmpty(formFieldCO.getTranslationCode())) {
                transCodeMap.put(formFieldCO.getFieldCode(), formFieldCO.getTranslationCode());
            }
        }

        // 判断是否有标准品
        if (generalDto.getStandardId() != null && generalDto.getStandardId() > 0L) {
            QueryConditionStandardDto standardDto = standardAllField(generalDto.getStandardId(), false, true);
            Map<String, QueryConditionDto> standardAllFieldMap = standardDto.getConditions()
                    .stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
            generalDto.getStandardCondition().forEach(f -> {
                if (standardAllFieldMap.containsKey(f)) {
                    QueryConditionDto queryCondition = standardAllFieldMap.get(f);
                    result.add(queryCondition);
                    fieldPropsMap.put(queryCondition.getCode(), queryCondition.getFieldProps());
                }
            });
        }

        List<AssetHeadDto> headList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            QueryConditionDto conditionDto = result.get(i);
            AssetHeadDto headDto = new AssetHeadDto();
            headDto.setCode(conditionDto.getCode());
            headDto.setName(conditionDto.getName());
            headDto.setType(conditionDto.getType());
            headDto.setIsLock(false);
            headDto.setFieldProps(fieldPropsMap.get(conditionDto.getCode()));
            headDto.setSort(querySortList.contains(conditionDto.getCode()));
            String tranCode = transCodeMap.get(conditionDto.getCode());
            if (FormFieldCO.NUMBER_INPUT.equals(conditionDto.getType())
                    && "2".equals(conditionDto.getFieldProps().getString("numberFormatType"))) {
                tranCode = "%";
            }
            headDto.setTranslationCode(
                    StrUtil.isNotEmpty(tranCode) ? tranCode : "");
            if (i < generalDto.getLockNum()) {
                headDto.setIsLock(true);
            }
            headList.add(headDto);
        }
        return headList;
    }

    @Override
    public List<AssetHeadDto> exportHeadField(Long standardId) {
        List<AssetHeadDto> tableHead = new ArrayList<>();
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();

        QueryFieldConstant.Field status = QueryFieldConstant.ASSET_EXT_FIELD.get(QueryFieldConstant.ASSET_FIELD_STATUS);
        if (status != null) {
            tableHead.add(
                    new AssetHeadDto()
                            .setName(status.getName())
                            .setCode(status.getCode())
                            .setType(status.getType())
                            .setAttrGroup(1));
        }
        formFields.stream()
                .filter(it -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(it.getFieldType()))
                .map(it -> {
                    AssetHeadDto headDto = new AssetHeadDto();
                    return headDto.setCode(it.getFieldCode())
                            .setName(it.getFieldName())
                            .setType(it.getFieldType());
                })
                .forEach(tableHead::add);

        ListUtil.of(
                QueryFieldConstant.FIELD_CREATE_BY,
                QueryFieldConstant.FIELD_CREATE_TIME).forEach(f -> {
            QueryFieldConstant.Field field = QueryFieldConstant.ASSET_EXT_FIELD.get(f);
            if (field != null) {
                tableHead.add(new AssetHeadDto()
                        .setName(field.getName())
                        .setCode(field.getCode())
                        .setType(field.getType()));
            }
        });

        if (ObjectUtil.isNotNull(standardId)) {
            FormVO form = standardFeignClient.form(standardId, false);
            if (form != null) {
                tableHead.add(new AssetHeadDto()
                        .setName("【" + form.getFormName() + "】标准品字段")
                        .setCode("standard_split")
                        .setType(FormFieldCO.SPLIT_LINE)
                        .setAttrGroup(2));
                List<FormFieldCO> standardFields = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
                standardFields.stream()
                        .filter(it -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE).contains(it.getFieldType()))
                        .map(it -> {
                            AssetHeadDto headDto = new AssetHeadDto();
                            return headDto.setCode(it.getFieldCode())
                                    .setName(it.getFieldName())
                                    .setType(it.getFieldType())
                                    .setAttrGroup(2);
                        }).forEach(tableHead::add);
            }
        }
        return tableHead;
    }

    @Override
    public List<QueryConditionDto> assetAllGroupField() {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();

        // 补齐 资产状态，创建人，创建时间，更新时间，最近打印时间
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_STANDARD));

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);

        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        QueryConditionDto createBy = toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            queryConditionDtos.add(createBy);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    @Override
    public QueryConditionStandardDto standardAllField(Long standardId, boolean needName, boolean filterFile) {
        if (standardId == null || standardId == 0L) {
            return new QueryConditionStandardDto();
        }
        List<QueryConditionStandardDto> list = standardAllField(ListUtil.of(standardId), needName, filterFile);
        return CollUtil.isNotEmpty(list) ? list.get(0) : new QueryConditionStandardDto();
    }

    @Override
    public List<QueryConditionStandardDto> standardAllField(List<Long> standardIds, boolean needName, boolean filterFile) {
        List<QueryConditionStandardDto> list = new ArrayList<>();
        if (CollUtil.isEmpty(standardIds)) {
            return list;
        }
        FormVO assetForm = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        for (Long standardId : standardIds) {
            QueryConditionStandardDto standardDto = new QueryConditionStandardDto();
            if (needName) {
                FormVO form = standardFeignClient.form(standardId, false);
                if (ObjectUtil.isNotNull(form)) {
                    standardDto.setStandardName(form.getFormName());
                }
            }
            List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(assetForm.getFormId(), standardId);
            List<String> filterType = new ArrayList<>();
            filterType.add(FormFieldCO.SPLIT_LINE);
            if (filterFile) {
                filterType.add(FormFieldCO.FILES);
            }
            List<QueryConditionDto> collect = standardExtField.stream().filter(f ->
                    !filterType.contains(f.getFieldType())
                            && !f.isHidden()
            ).map(f -> {
                QueryConditionDto queryConditionDto = buildQueryCondition(f);
                queryConditionDto.setOperators(operatorMap.get(queryConditionDto.getType()));
                return queryConditionDto;
            }).collect(Collectors.toList());
            standardDto.setConditions(collect);
            list.add(standardDto);
        }
        return list;
    }

    @Override
    public List<AssetHeadDto> equipmentHeadView(Integer type, Long standardId) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        QueryConditionSortDto querySort = assetFeignClient.sortField();
        List<String> querySortList = querySort.getSortList().stream()
                .map(QueryConditionSortDto.Field::getValue).collect(Collectors.toList());

        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllHeadField = equipmentAllHeadField(formVO, type);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllHeadField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_HEAD);
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        List<String> selectConditions = generalDto.getConditions();
        selectConditions.forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });
        // 如果是系统的，需要补属性
        if (BooleanUtil.isTrue(configDto.getSysConfig())) {
            // 添加 top10
            int len = Math.min(assetAllHeadField.size(), 10);
            AtomicInteger count = new AtomicInteger(len);
            for (QueryConditionDto f : assetAllHeadField) {
                if (!selectConditions.contains(f.getCode())) {
                    if (count.getAndDecrement() >= 0) {
                        result.add(f);
                    } else {
                        break;
                    }
                }
            }
        }

        Map<String, String> transCodeMap = new HashMap<>();
        transCodeMap.put(QueryFieldConstant.FIELD_CREATE_BY, QueryFieldConstant.FIELD_CREATE_BY + "Text");
        Map<String, JSONObject> fieldPropsMap = new HashMap<>();
        JSONObject dateFormat = new JSONObject();
        dateFormat.put("dateFormatType", "yyyy-MM-dd");
        fieldPropsMap.put(QueryFieldConstant.FIELD_CREATE_TIME, dateFormat);
        fieldPropsMap.put(QueryFieldConstant.FIELD_UPDATE_TIME, dateFormat);
        fieldPropsMap.put(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME, dateFormat);
        for (FormFieldCO formFieldCO : formVO.getFormFields()) {
            fieldPropsMap.put(formFieldCO.getFieldCode(), formFieldCO.getFieldProps());
            if (StrUtil.isNotEmpty(formFieldCO.getTranslationCode())) {
                transCodeMap.put(formFieldCO.getFieldCode(), formFieldCO.getTranslationCode());
            }
        }

        List<QueryConditionDto> standardFieldList = new ArrayList<>();
        // 判断是否有标准品
        if (standardId != null) {
            QueryConditionStandardDto standardDto = standardAllField(standardId, false, true);
            List<QueryConditionDto> conditions = standardDto.getConditions();
            conditions.forEach(v -> {
                result.add(v);
                standardFieldList.add(v);
                fieldPropsMap.put(v.getCode(), v.getFieldProps());
            });
        } else if (generalDto.getStandardId() != null && generalDto.getStandardId() > 0L) {
            QueryConditionStandardDto standardDto = standardAllField(generalDto.getStandardId(), false, true);
            Map<String, QueryConditionDto> standardAllFieldMap = standardDto.getConditions()
                    .stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
            generalDto.getStandardCondition().forEach(f -> {
                if (standardAllFieldMap.containsKey(f)) {
                    QueryConditionDto queryCondition = standardAllFieldMap.get(f);
                    result.add(queryCondition);
                    fieldPropsMap.put(queryCondition.getCode(), queryCondition.getFieldProps());
                }
            });
        }

        List<AssetHeadDto> headList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            QueryConditionDto conditionDto = result.get(i);
            AssetHeadDto headDto = new AssetHeadDto();
            headDto.setCode(conditionDto.getCode());
            headDto.setName(conditionDto.getName());
            headDto.setType(conditionDto.getType());
            headDto.setIsLock(false);
            headDto.setFieldProps(fieldPropsMap.get(conditionDto.getCode()));
            headDto.setSort(querySortList.contains(conditionDto.getCode()));
            String tranCode = transCodeMap.get(conditionDto.getCode());
            if (FormFieldCO.NUMBER_INPUT.equals(conditionDto.getType())
                    && "2".equals(conditionDto.getFieldProps().getString("numberFormatType"))) {
                tranCode = "%";
            }
            headDto.setTranslationCode(
                    StrUtil.isNotEmpty(tranCode) ? tranCode : "");
            if (i < generalDto.getLockNum()) {
                headDto.setIsLock(true);
            }
            headDto.setBaseField(!standardFieldList.contains(conditionDto));
            headList.add(headDto);
        }
        return headList;
    }

    @Override
    public List<QueryConditionDto> equipmentAllHeadView(Integer type) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return equipmentAllHeadField(formVO, type);
    }

    private QueryConditionDto buildQueryCondition(FormFieldCO formFieldCO) {
        return new QueryConditionDto()
                .setName(formFieldCO.getFieldName())
                .setCode(formFieldCO.getFieldCode())
                .setType(formFieldCO.getFieldType())
                .setFieldProps(formFieldCO.getFieldProps());
    }

    private QueryConditionDto toQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ASSET_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    private QueryConditionDto buildAssetRelationCondition(Integer type) {
        QueryConditionDto result = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("assetRelationNum")
                .setType("num_input")
                .setFieldProps(new JSONObject());
        if (Objects.nonNull(type) && type == 2) {
            result.setName("设备bom");
        } else {
            result.setName("资产组合");
        }
        return result;
    }

    private QueryConditionDto buildSparePartsCondition() {
         return new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setName("备件")
                .setCode("sparePartsNum")
                .setType("num_input")
                .setFieldProps(new JSONObject());
    }

    private List<QueryConditionDto> equipmentAllHeadField(FormVO formVO, Integer type) {
        List<CusMenuDto> pcMenus = cusMenuFeignClient.userMenuPcList();

        boolean relationMenu = pcMenus.stream().anyMatch(item -> item.getMenuCode().equalsIgnoreCase("asset_relation"));
        boolean sparePartsMenu = pcMenus.stream().anyMatch(item -> item.getMenuCode().equalsIgnoreCase("equipment_spareparts"));

        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();

        // 补齐 资产状态，创建人，创建时间，更新时间，最近打印时间
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));
        List<FormFieldCO> formFields = formVO.getFormFields();

        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            if ("asset_relation".equalsIgnoreCase(formFieldCO.getFieldCode()) && !relationMenu) {
                continue;
            }
            if ("equipment_spareparts".equalsIgnoreCase(formFieldCO.getFieldCode()) && !sparePartsMenu) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME));
        //添加资产组合字段
        if (relationMenu) {
            queryConditionDtos.add(buildAssetRelationCondition(type));
        }
        //添加备件字段
        if (sparePartsMenu) {
            queryConditionDtos.add(buildSparePartsCondition());
        }
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }
}
