package com.niimbot.asset.websocket.codec;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.websocket.msg.Message;

import javax.websocket.EncodeException;
import javax.websocket.Encoder;
import javax.websocket.EndpointConfig;

/**
 * 消息编码器
 *
 * <AUTHOR>
 * @date 2021/9/1 16:46
 */
public class MessageTextEncoder implements Encoder.Text<Message> {
    @Override
    public String encode(Message object) throws EncodeException {
        return JSON.toJSONString(object);
    }

    @Override
    public void init(EndpointConfig endpointConfig) {

    }

    @Override
    public void destroy() {

    }
}
