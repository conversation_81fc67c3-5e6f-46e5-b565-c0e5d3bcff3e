package com.niimbot.asset.controller.common.activiti;

import com.niimbot.activiti.ActWorkflowCopyViewDto;
import com.niimbot.asset.service.feign.WorkflowTodoFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2023/9/15 上午10:18
 */
@Slf4j
@Api(tags = "待办")
@ResultController
@RequestMapping("api/common/workflow/todo")
@RequiredArgsConstructor
public class WorkflowTodoController {

    @Autowired
    private WorkflowTodoFeignClient workflowTodoFeignClient;

    /**
     * 同步抄送我的数据
     * @param workflowCopy
     * @return
     */
    @PostMapping("/sync/copy")
    public Integer syncCopy(@RequestBody ActWorkflowCopyViewDto workflowCopy) {
        log.info("workflowTodoController syncCopy createTime=[{}]", workflowCopy.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return workflowTodoFeignClient.syncCopy(workflowCopy);
    }

    /**
     * 同步具体某条抄送我的数据
     * @param workflowCopy
     * @return
     */
    @PostMapping("/sync/copyProc")
    public Boolean syncProc(@RequestBody ActWorkflowCopyViewDto workflowCopy) {
        return workflowTodoFeignClient.syncProc(workflowCopy);
    }

    /**
     * 同步我申请的数据
     * @param workflowCopy
     * @return
     */
    @PostMapping("/sync/copyRequest")
    public Integer copyRequest(@RequestBody ActWorkflowCopyViewDto workflowCopy) {
        log.info("workflowTodoController copyRequest createTime=[{}]", workflowCopy.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return workflowTodoFeignClient.copyRequest(workflowCopy);
    }

    /**
     * 同步具体某条我申请的数据
     * @param workflowCopy
     * @return
     */
    @PostMapping("/sync/copyRequestProc")
    public Boolean copyRequestProc(@RequestBody ActWorkflowCopyViewDto workflowCopy) {
        return workflowTodoFeignClient.copyRequestProc(workflowCopy);
    }
}
