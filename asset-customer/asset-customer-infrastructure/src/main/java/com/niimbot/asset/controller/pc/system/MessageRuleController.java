package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.MessageRuleFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.MessageRuleDto;
import com.niimbot.system.MessageRuleListDto;
import com.niimbot.system.MessageRulePageDto;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Api(tags = "【PC端消息规则管理】")
@ResultController
@RequestMapping("/api/pc/message/rule")
@RequiredArgsConstructor
public class MessageRuleController {

    private final MessageRuleFeignClient messageRuleFeignClient;

    @ApiOperation("获取租户可选的提醒方式")
    @GetMapping("/available/channel/{code}")
    public List<Map<String, Object>> availableChannelByCode(@Validated @NotEmpty(message = "消息编码不能为空") @PathVariable String code) {
        return messageRuleFeignClient.availableChannelByCode(code);
    }

    @ApiOperation("分页获取当前租户有效的消息规则列表")
    @GetMapping("/available/list")
    @AutoConvert
    public PageUtils<MessageRuleListDto> availableList(MessageRulePageDto dto) {
        return messageRuleFeignClient.page(dto);
    }

    @ApiOperation("消息规则详情")
    @GetMapping("/{id}")
    @AutoConvert
    public MessageRuleDto detail(@PathVariable Long id) {
        return messageRuleFeignClient.detail(id);
    }

    @ApiOperation("检查当前消息规则是否可使用")
    @GetMapping("/check/{code}")
    public MessageRuleDto checkCompanyMessageRuleIsAvailable(@ApiParam(name = "code", value = "消息编码") @Validated @NotNull(message = "消息编码不能为空") @PathVariable String code) {
        return messageRuleFeignClient.checkCompanyMessageRuleIsAvailable(LoginUserThreadLocal.getCompanyId(), code);
    }

    @ApiOperation("获取当前企业消息规则配置信息")
    @GetMapping("/currentCompanyMessageConfig/{code}")
    @AutoConvert
    public MessageRuleDto currentCompanyMessageConfig(@ApiParam(name = "code", value = "消息编码") @Validated @NotNull(message = "消息编码不能为空") @PathVariable String code) {
        return messageRuleFeignClient.currentCompanyMessageConfig(LoginUserThreadLocal.getCompanyId(), code);
    }

    @ApiOperation("更新消息规则")
    @RepeatSubmit
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated(Update.class) MessageRuleDto dto) {
        return messageRuleFeignClient.edit(dto);
    }

    @ApiOperation("禁用当前消息规则")
    @PutMapping("/disable/{id}")
    @ResultMessage(ResultConstant.DISABLE_SUCCESS)
    public Boolean disable(@PathVariable Long id) {
        return messageRuleFeignClient.disable(id);
    }

    @ApiOperation("启用当前消息规则")
    @PutMapping("/enable/{id}")
    @ResultMessage(ResultConstant.ENABLE_SUCCESS)
    public Boolean enable(@PathVariable Long id) {
        return messageRuleFeignClient.enable(id);
    }
}
