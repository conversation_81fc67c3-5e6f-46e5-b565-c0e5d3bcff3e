package com.niimbot.asset.service;


import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.MaterialCategoryReportSearch;
import com.niimbot.report.MaterialCkManifestQueryDto;
import com.niimbot.report.MaterialCkStatisticsQueryDto;
import com.niimbot.report.MaterialRepositoryReportSearch;
import com.niimbot.report.MaterialRkManifestQueryDto;
import com.niimbot.report.MaterialRkStatisticsQueryDto;

/**
 * 预定义报表导出
 * <AUTHOR>
 * @date 2023/7/8 下午3:48
 */
public interface PredefinedReportExportService {

    /**
     * 资产履历报表导出
     * @param queryDto
     * @param
     */
    ExportResponse exportAssetLogReport(AssetLogReportQueryDto queryDto);

    /**
     * 耗材分类增减报表导出
     * @param search
     * @return
     */
    ExportResponse exportMaterialCategoryReport(MaterialCategoryReportSearch search);

    /**
     * 耗材出入库统计表
     * @param search
     * @return
     */
    ExportResponse exportMaterialRepositoryReport(MaterialRepositoryReportSearch search);

    ExportResponse materialRkManifestExport(MaterialRkManifestQueryDto queryDto);

    ExportResponse materialCkManifestExport(MaterialCkManifestQueryDto queryDto);

    ExportResponse materialRkStatisticsExport(MaterialRkStatisticsQueryDto queryDto);

    ExportResponse materialCkStatisticsExport(MaterialCkStatisticsQueryDto queryDto);

    /**
     * 待处置资产清单等报表导出
     * @param search
     * @param type
     * @return
     * @param <T>
     */
    <T> ExportResponse exportHandleAssetReport(T search, OrderTypeNewEnum type);

}
