package com.niimbot.asset.service;


import java.io.InputStream;
import java.util.List;

import cn.hutool.poi.excel.ExcelWriter;

/**
 * <AUTHOR>
 * @since 2022/8/26 15:02
 */
public interface AssetExcelEditService {

    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId);

    void importEditErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);

    ExcelWriter buildEditExcelWriter();
}
