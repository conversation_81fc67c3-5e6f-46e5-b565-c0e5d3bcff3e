package com.niimbot.asset.service.feign;

import com.niimbot.system.PrivacyAgreementDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2021/11/9 15:19
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PrivacyAgreementFeignClient {

    /**
     * 查询平台最新隐私协议版本号
     *
     * @param platform
     * @return
     */
    @GetMapping(value = "server/system/privacyAgreement/last/{platform}")
    PrivacyAgreementDto last(@PathVariable("platform") Integer platform);
}
