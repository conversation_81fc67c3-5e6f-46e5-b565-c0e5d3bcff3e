package com.niimbot.asset.controller.common.system;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.annotation.LoginRecord;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AbstractPermissionChangedService;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.CompanyWalletFeignClient;
import com.niimbot.asset.service.feign.CusAccountFeignClient;
import com.niimbot.asset.service.feign.CusRegisterFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.asset.service.feign.ThirdpartyFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.sale.CompanyWalletArrearsDto;
import com.niimbot.system.AccountEmployeeDto;
import com.niimbot.system.CancelUserDto;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.RegisterDto;
import com.niimbot.system.RegisterProgressDto;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 注册控制器
 *
 * <AUTHOR>
 * @since 2021-01-04
 */
@Api(tags = {"企业注册接口"})
@Slf4j
@ResultController
@RequestMapping("api/common")
public class CusRegisterController {

    @Resource
    private CusRegisterFeignClient registerFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private CusLoginService loginService;

    @Resource
    private ThirdpartyFeignClient thirdpartyFeignClient;

    @Resource
    private CusAccountFeignClient accountFeignClient;

    @Resource
    private CompanyWalletFeignClient companyWalletFeign;

    @Resource
    private CusRegisterFeignClient cusUserFeignClient;
    @Resource
    private AccountCenterFeignClient accountCenterFeignClient;

    @Resource
    private RedisService redisService;

    @Resource(name = "kickOffPermissionChangedService")
    private AbstractPermissionChangedService kickOffPermissionChangedService; // 踢人登出

    @LoginRecord
    @ApiOperation(value = "企业注册", httpMethod = "POST")
    @RepeatSubmit
    @PostMapping("/register")
    @ResultMessage("注册成功,请登录")
    public Map<String, String> register(@RequestBody @Validated(RegisterDto.Register.class) RegisterDto dto) {
        log.info("企业注册");
        NationalCodeValidate.checkCNMobile(dto.getNationalCode(), dto.getMobile());
        if (!smsCodeFeignClient.checkSmsCode(dto.getMobile(), dto.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        //校验区号和手机号一致
        String registerKey = "register_key:" +dto.getMobile();
        if (redisService.hasKey(registerKey)){
            if (!redisService.get(registerKey).equals(dto.getNationalCode())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "国际区号与手机号不匹配，请检查国际区号！");
            }
        }
        String terminal = "";
        if (ObjectUtil.isNotNull(dto.getSource())) {
            if (dto.getSource() == 2) {
                terminal = AssetConstant.TERMINAL_APP;
            }
            if (dto.getSource() == 1) {
                terminal = AssetConstant.TERMINAL_PC;
            }
        }
        // 这里id是账户id
        CusUserDto register = registerFeignClient.register(dto);
        // 更新最后一次登录的企业ID
        accountCenterFeignClient.updateLastCompany(register.getId(), register.getCompanyId());
        String token = loginService.loginByMobile(dto.getMobile(), dto.getSmsCode(), terminal, dto.getPushId()).getAccess_token();
        return ImmutableMap.of(
                "companyName", dto.getCompanyName(),
                "accountNo", register.getAccount(),
                "mobile", dto.getMobile(),
                OAuth2AccessToken.ACCESS_TOKEN, token
        );
    }

    @ApiOperation(value = "企业注册状态轮询")
    @GetMapping("/register/progress")
    public RegisterProgressDto registerProgress(@Validated(RegisterDto.Register.class) RegisterDto dto) {
        List<String> progress = Convert.toList(String.class, redisService.lRange("register:progress:" + dto.getMobile() + "_" + dto.getSmsCode(), 0, -1));
        RegisterProgressDto progressDto = new RegisterProgressDto();
        progressDto.setProgress(progress);
        return progressDto;
    }

    @ApiOperation(value = "校验手机号")
    @GetMapping("/register/checkPhone")
    public Boolean checkPhone(@NotBlank(message = "手机号格式不能为空")
                                  @Size(max = 11, message = "手机号最多11位") String mobile,
                              @NotBlank(message = "请输入区号") String nationalCode) {
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        registerFeignClient.checkRegisterMobile(mobile);
        return true;
    }

    @ApiOperation(value = "注销账号校验", httpMethod = "POST")
    @RepeatSubmit
    @PostMapping("/cancelcheck/user")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public void cancelcheckUser() {

        boolean b = thirdpartyFeignClient.list();
        //判断是否有绑定的第三方账号
        if (b) {
            // 有绑定账号请先解绑
            throw new BusinessException(SystemResultCode.USER_CANCEL_CHECK_THIRDPARTY);
        }
        final Long accountId = LoginUserThreadLocal.getAccountId();

        //获取账号关联的所有企业
        List<AccountEmployeeDto> companyList = accountCenterFeignClient.companyList(accountId);

        if (CollectionUtil.isNotEmpty(companyList)) {
            for (AccountEmployeeDto accountEmployeeDto : companyList) {
                Long companyId = accountEmployeeDto.getCompanyId();
                //查询当前企业的管理员员工
                CusEmployeeDto cusEmployeeDto = accountFeignClient.getAdministratorByCompanyId(companyId);
                Long accountIdAdmin = accountFeignClient.getEmployAccount(cusEmployeeDto.getId());
                //登录者当前账号是否和管理员账号一致
                if (accountIdAdmin.equals(accountId)) {
                    //是超管判断企业是否欠费
                    // 查看企业是否欠费
                    CompanyWalletArrearsDto walletArrearsDto = companyWalletFeign.getWalletArrears(companyId);
                    //只有是管理员同时企业欠费才不让注销
                    if (walletArrearsDto.getArrears().compareTo(BigDecimal.ZERO) > 0) {
                        throw new BusinessException(SystemResultCode.USER_CANCEL_CHECK_ARREARS);
                    }
                    //判断企业是否还有其他账号信息
                    Integer accountTotal = accountFeignClient.accountTotal(companyId);

                    //是管理员但是企业下面还有多个账号就不能注销，需要先转让管理员
                    if (accountTotal > 1) {
                        //当前用户是超管且企业下账号有多个不能注销
                        throw new BusinessException(SystemResultCode.USER_CANCEL_CHECK_ADMIN);
                    } else {
                        Boolean bindThirdParty = thirdpartyFeignClient.isBindThirdParty(companyId);
                        if (Objects.nonNull(bindThirdParty) && bindThirdParty) {
                            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, accountEmployeeDto.getCompanyName() + "有绑定第三方组织架构，请先解除绑定");
                        }
                    }
                }
            }
        }
    }

    @ApiOperation(value = "注销账号", httpMethod = "POST")
    @RepeatSubmit
    @PostMapping("/cancel/user")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public void cancelUser(@ApiParam(name = "注销账号", value = "json格式", required = true)
                           @RequestBody @Validated CancelUserDto info) {
        NationalCodeValidate.checkCNMobile(info.getNationalCode(),info.getMobile());
        if (!smsCodeFeignClient.checkSmsCode(info.getMobile(), info.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        //执行注销逻辑
        boolean a = cusUserFeignClient.cancelUser();
        // 踢出登录用户
        if (a) {
            kickOffPermissionChangedService.permissionChange(Arrays.asList(LoginUserThreadLocal.getCusUser()));
        }
    }

}
