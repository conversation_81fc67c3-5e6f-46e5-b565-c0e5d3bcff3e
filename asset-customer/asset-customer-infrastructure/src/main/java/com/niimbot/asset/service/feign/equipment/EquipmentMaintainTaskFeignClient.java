package com.niimbot.asset.service.feign.equipment;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.*;
import com.niimbot.material.MaterialSparePartsDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/11 17:48
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentMaintainTaskFeignClient {

    @PostMapping(value = "server/equipment/maintain/task/page")
    PageUtils<MaintainTaskDto> page(MaintainTaskQueryDto query);

    @GetMapping(value = "server/equipment/maintain/task/equipment/{orderId}")
    JSONObject equipmentInfo(@PathVariable("orderId") Long orderId);

    @GetMapping(value = "server/equipment/maintain/task/{orderId}")
    MaintainTaskDto info(@PathVariable("orderId") Long orderId);

    @GetMapping(value = "server/equipment/maintain/task/replacement/sparePartsPage")
    PageUtils<MaterialSparePartsDto> sparePartsPage(@SpringQueryMap MaintainTaskSparePartsQueryDto queryDto);

    @GetMapping(value = "server/equipment/maintain/task/replacement/sparePartsList")
    List<MaterialSparePartsDto> sparePartsList(@SpringQueryMap MaintainTaskSparePartsQueryDto partsQueryDto);

    @GetMapping(value = "server/equipment/maintain/task/material/{orderId}/{materialId}")
    JSONObject materialInfo(@PathVariable("orderId") Long orderId,
                            @PathVariable("materialId") Long materialId);

    @PostMapping(value = "server/equipment/maintain/task/temporary")
    AuditableSubmitTaskResult temporary(MaintainTaskCreateDto createDto);

    @PostMapping(value = "server/equipment/maintain/task/submit")
    AuditableSubmitTaskResult submit(MaintainTaskCreateDto createDto);

    @PostMapping(value = "server/equipment/maintain/task/cancel")
    AuditableCancelTaskResult cancel(List<Long> orderIds);

    @PostMapping(value = "server/equipment/maintain/task/resume")
    AuditableResumeTaskResult resume(List<Long> orderIds);

    @GetMapping("server/equipment/maintain/task/userHasTask")
    Boolean userHasTask(@RequestParam("userId") Long userId);

}
