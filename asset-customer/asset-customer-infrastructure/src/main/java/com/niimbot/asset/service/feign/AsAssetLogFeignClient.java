package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.AsAssetLogDto;
import com.niimbot.means.AsAssetLogQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-12-25
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AsAssetLogFeignClient {

    /**
     * 分页查询
     *
     * @param dto 查询对象
     * @return 分页
     */
    @GetMapping(value = "server/means/assetLog/page")
    PageUtils<AsAssetLogDto> page(@SpringQueryMap AsAssetLogQueryDto dto);

    /**
     * 列表查询
     *
     * @param dto 查询对象
     * @return 列表
     */
    @PostMapping(value = "server/means/assetLog/list")
    List<AsAssetLogDto> list(@RequestBody AsAssetLogQueryDto dto);

    /**
     * 新增对象
     *
     * @param dto 日志
     * @return 结果
     */
    @GetMapping(value = "server/means/assetLog")
    Boolean insert(AsAssetLogDto dto);
}
