package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.StoreRecordDto;
import com.niimbot.means.StoreRecordSearch;
import com.niimbot.means.StoreSnapshotSearch;
import com.niimbot.system.StoreRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface StoreRecordFeignClient {

    @PostMapping("server/system/store/record/search")
    PageUtils<StoreRecordDto> recordSearch(@RequestBody StoreRecordSearch search);

    @PostMapping("/server/system/store/snapshot/search")
    PageUtils<Object> snapshotSearch(@RequestBody StoreSnapshotSearch search);

    @PostMapping("/server/system/store/record/save")
    void save(@RequestBody StoreRecord save);

    @GetMapping("/server/system/store/snapshot/list/{id}")
    List<JSONObject> snapshotList(@PathVariable("id") Long id);

}
