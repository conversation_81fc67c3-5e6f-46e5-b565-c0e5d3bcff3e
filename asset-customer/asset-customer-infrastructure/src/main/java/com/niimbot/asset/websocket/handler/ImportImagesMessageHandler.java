package com.niimbot.asset.websocket.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.asset.service.feign.ProductFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.websocket.WebSocketServer;
import com.niimbot.asset.websocket.msg.ImportImagesMessage;
import com.niimbot.asset.websocket.msg.Message;
import com.niimbot.means.ImportImages;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableImportResult;

import javax.websocket.EncodeException;
import javax.websocket.Session;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 处理批量图片上传消息
 *
 * <AUTHOR>
 */
public class ImportImagesMessageHandler implements MessageHandler {

    private final Map<Integer, Function<ImportImagesMessage, List<ImportImages>>> map = new HashMap<>(4);

    public ImportImagesMessageHandler() {
        map.put(1, message -> {
            List<ImportImages> errors = new ArrayList<>();
            AssetFeignClient client = SpringUtil.getBean(AssetFeignClient.class);
            errors.addAll(client.assetImagesCheck(message.getChecks(), message.getAction(), message.getToken()));
            errors.addAll(client.assetImagesModify(message.getSave(), message.getAction(), message.getToken()));
            return errors;
        });
        map.put(2, message -> {
            List<ImportImages> errors = new ArrayList<>();
            MaterialFeignClient client = SpringUtil.getBean(MaterialFeignClient.class);
            errors.addAll(client.materialImagesCheck(message.getChecks(), message.getAction(), message.getToken()));
            errors.addAll(client.materialImagesModify(message.getSave(), message.getAction(), message.getToken()));
            return errors;
        });
        map.put(3, message -> {
            List<ImportImages> errors = new ArrayList<>();
            ProductFeignClient client = SpringUtil.getBean(ProductFeignClient.class);
            errors.addAll(client.productImagesCheck(message.getChecks(), message.getAction(), message.getToken()));
            errors.addAll(client.productImagesModify(message.getSave(), message.getAction(), message.getToken()));
            return errors;
        });
    }

    @Override
    public void handle(Session session, Long userId, Long companyId, Message message) throws IOException, EncodeException {
        if (!(message instanceof ImportImagesMessage)) {
            return;
        }
        ImportImagesMessage imagesMessage = (ImportImagesMessage) message;
        // 是否是已经全部导入完成了
        if (imagesMessage.getStep() == 3) {
            if (imagesMessage.getImportType() == 1) {
                AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MEANS_IMAGES, new AuditableImportResult(imagesMessage.getSuccessCount()), companyId, userId));
            }
            if (imagesMessage.getImportType() == 2) {
                AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MRL_IMAGES, new AuditableImportResult(imagesMessage.getSuccessCount()), companyId, userId));
            }
            return;
        }
        if (!map.containsKey(imagesMessage.getImportType())) {
            return;
        }
        List<ImportImages> error = map.get(imagesMessage.getImportType()).apply(imagesMessage);
        imagesMessage.clean();
        imagesMessage.setError(error);
        WebSocketServer.sendMessage(session.getId(), imagesMessage);

    }
}
