package com.niimbot.asset.controller.h5;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.service.feign.OnlineInventoryFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.inventory.InventoryAssetAppQueryDto;
import com.niimbot.inventory.InventoryAssetDataCountDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "企业组织管理")
@ResultController
@RequestMapping("api/common/org")
@RequiredArgsConstructor
public class H5OrgController {

    private final OrgFeignClient orgFeignClient;
    private final OnlineInventoryFeignClient onlineInventoryFeignClient;
    private final CacheResourceUtil cacheResourceUtil;

    @ApiOperation(value = "查询组织树[带权限-盘点专用]-新")
    @GetMapping("/query/list")
    public List<OrgDto> queryList(@Validated InventoryAssetAppQueryDto queryDto) {
        List<OrgDto> result = orgFeignClient.list(new OrgQueryDto().setFilterPerm(true));
        List<String> orgIdList = result.stream().map(item -> item.getId().toString()).collect(Collectors.toList());

        InventorySurplusQueryDto queryParam = new InventorySurplusQueryDto();
        BeanUtils.copyProperties(queryDto, queryParam);
        QueryConditionDto queryConditionDto = new QueryConditionDto();
        queryConditionDto.setQuery("in");
        queryConditionDto.setQueryData(orgIdList);
        queryParam.setConditions(Lists.newArrayList(queryConditionDto));
        if ("orgOwner".equalsIgnoreCase(queryDto.getConditionCode())) {
            queryParam.setGroupByColumn("asset_org_owner");
            queryConditionDto.setCode("orgOwner");
        } else {
            queryParam.setGroupByColumn("asset_use_org");
            queryConditionDto.setCode("useOrg");
        }
        List<InventoryAssetDataCountDto> assetDataCountDtoList = onlineInventoryFeignClient.appAssetCode(queryParam);
        log.info("orgController queryList success! param=[{}] result=[{}]", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(assetDataCountDtoList));
        Map<String, Integer> dataCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(assetDataCountDtoList)) {
            dataCountMap = assetDataCountDtoList.stream().collect(Collectors.toMap(InventoryAssetDataCountDto::getConditionCode, InventoryAssetDataCountDto::getDataCount, (v1, v2) -> v2));
        }
        for (OrgDto item : result) {
            item.setDataCount(dataCountMap.get(item.getId().toString()));
        }
        // 统计数量为0的分组为其他
        // 如果dataCountMap中id被删除，也应该记录在这里
        Set<Long> orgIdSet = result.stream().map(OrgDto::getId).collect(Collectors.toSet());
        dataCountMap.forEach((id, count) -> {
            Long idLong = Convert.toLong(id);
            if (!orgIdSet.contains(idLong) && idLong != null) {
                result.add(new OrgDto().setOrgName(cacheResourceUtil.getOrgName(idLong)).setId(idLong).setDataCount(count));
            }
        });
        Integer other = assetDataCountDtoList.stream().filter(v -> StrUtil.isBlank(v.getConditionCode())).map(InventoryAssetDataCountDto::getDataCount).reduce(0, Integer::sum);
        result.add(new OrgDto().setOrgName("其他").setId(-1L).setDataCount(other));
        return result;
    }

}
