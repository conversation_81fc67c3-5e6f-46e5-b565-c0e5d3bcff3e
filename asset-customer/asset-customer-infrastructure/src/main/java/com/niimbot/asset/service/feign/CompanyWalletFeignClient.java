package com.niimbot.asset.service.feign;

import com.niimbot.sale.CompanyWalletArrearsDto;
import com.niimbot.sale.CompanyWalletDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @since 2021/12/29 10:20
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CompanyWalletFeignClient {

    @GetMapping(value = "server/sale/company/wallet")
    CompanyWalletDto getWallet();

    @GetMapping(value = "server/sale/company/wallet/arrears/{companyId}")
    CompanyWalletArrearsDto getWalletArrears(@PathVariable("companyId") Long companyId);
}
