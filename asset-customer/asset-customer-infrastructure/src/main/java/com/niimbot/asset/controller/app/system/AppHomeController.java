package com.niimbot.asset.controller.app.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableList;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AppActivateConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.AppActivateFeignClient;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.ReportFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.report.AssetReportDto;
import com.niimbot.report.AssetStatusReportDto;
import com.niimbot.system.*;
import com.niimbot.system.statistics.ToolboxStatisticsDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工具箱控制器
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Api(tags = "APP首页")
@ResultController
@RequestMapping("api/app/home")
@Slf4j
@Validated
public class AppHomeController {

    private final CusUserSettingFeignClient userSettingFeignClient;

    private final AbstractFileUtils fileUtils;

    private final ReportFeignClient reportFeignClient;

    private final CusMenuFeignClient cusMenuFeignClient;
    private final AppActivateFeignClient appActivateFeignClient;

    @Autowired
    public AppHomeController(CusUserSettingFeignClient userSettingFeignClient,
                             AbstractFileUtils fileUtils,
                             ReportFeignClient reportFeignClient,
                             CusMenuFeignClient cusMenuFeignClient,
                             AppActivateFeignClient appActivateFeignClient) {
        this.userSettingFeignClient = userSettingFeignClient;
        this.fileUtils = fileUtils;
        this.reportFeignClient = reportFeignClient;
        this.cusMenuFeignClient = cusMenuFeignClient;
        this.appActivateFeignClient = appActivateFeignClient;
    }

    @ApiOperation(value = "编辑工具箱", notes = "请将元素数据修改排序后传回,position字段表示显示位置 1 -工具箱  2-常用工具")
    @PostMapping("/toolbox/edit")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @NotEmpty List<AsToolboxDto> list) {
        return userSettingFeignClient.saveOrUpdate(new AsCusUserSettingDto().setAppToolbox(list));
    }

    @Deprecated
    @ApiOperation(value = "工具箱列表", notes = "position字段表示显示位置 1 -工具箱  2-常用工具")
    @GetMapping("/toolbox/list")
    public List<AsToolboxDto> get() {
        List<AsToolboxDto> toolBoxList = userSettingFeignClient.getUserAppToolBox();
        toolBoxList.forEach(toolBox -> {
            String toolboxIcon = toolBox.getToolboxIcon();
            toolBox.setToolboxIcon(fileUtils.convertToDownloadUrl(toolboxIcon));
        });
        return toolBoxList;
    }

    @ApiOperation(value = "工具箱列表", notes = "position字段表示显示位置 1 -工具箱  2-常用工具")
    @GetMapping("/toolbox/list/v2")
    public List<ToolboxGroupDto> getV2() {
        List<ToolboxGroupDto> result = userSettingFeignClient.getUserAppToolBoxV2();
        if (CollUtil.isEmpty(result)) {
            return result;
        }
        return filterActivateToolbox(result);
    }

    private List<ToolboxGroupDto> filterActivateToolbox(List<ToolboxGroupDto> result) {
        //判断当前企业是否开通设备管理
        List<String> activeAppList = appActivateFeignClient.configStatus();
        if (!activeAppList.contains(AppActivateConstant.EQUIPMENT)) {
            result = result.stream().filter(item -> !item.getGroupName().contains("设备")).collect(Collectors.toList());
        }
        if (!activeAppList.contains(AppActivateConstant.MATERIAL_INVENTORY)) {
            result.forEach(item -> item.setToolboxGroupItemDtoList(
                    item.getToolboxGroupItemDtoList().stream()
                            .filter(item1 -> !"1010".equals(item1.getOrderType()))
                            .collect(Collectors.toList())));
        }
        return result;
    }

    @ApiOperation(value = "APP工作台")
    @GetMapping("/toolbox/workbench")
    public List<ToolboxGroupDto> getWorkbench() {
        List<ToolboxGroupDto> result = userSettingFeignClient.getWorkbench();
        if (CollUtil.isEmpty(result)) {
            return result;
        }
        return filterActivateToolbox(result);
    }

    @ApiOperation(value = "菜单列表")
    @GetMapping("/menu")
    public AppCusMenuDto getMenus() {
        return cusMenuFeignClient.userMenuAppList();
    }

    @ApiOperation(value = "消息列表", notes = "消息测试数据")
    @GetMapping("/message/list")
    public List<String> messageList() {
        return ImmutableList.of("测试消息1", "测试消息2");
    }

    @ApiOperation(value = "资产概览")
    @GetMapping("/asset/statistics")
    public AssetReportDto<AssetStatusReportDto> assetList() {
        AssetReportDto<AssetStatusReportDto> assetReportDto = reportFeignClient.assetStatusReport();
        String assetTotalWorth = assetReportDto.getAssetTotalWorth();
        assetReportDto.setAssetTotalWorth(formatWorthValue(assetTotalWorth));
        List<AssetStatusReportDto> list = assetReportDto.getList();
        list.stream().forEach(dto -> dto.setWorth(formatWorthValue(dto.getWorth())));
        return assetReportDto;
    }

    private String formatWorthValue(String originValue) {
        if (StrUtil.isBlank(originValue)) {
            return originValue;
        }
        String[] split = originValue.split("\\.");
        if (split.length == 1) {
            return originValue.replaceAll("(?<=\\d)(?=(?:\\d{3})+$)", ",");
        }
        String first = split[0].replaceAll("(?<=\\d)(?=(?:\\d{3})+$)", ",");
        return first + "." + split[1];
    }

    @ApiOperation(value = "工具箱使用统计")
    @PostMapping("/toolbox/statistics")
    public Boolean toolboxStatistics(@RequestBody @Validated ToolboxStatisticsDto toolboxStatisticsDto) {
        toolboxStatisticsDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        toolboxStatisticsDto.setUserId(LoginUserThreadLocal.getCurrentUserId());
        return userSettingFeignClient.toolboxStatistics(toolboxStatisticsDto);
    }

    @ApiOperation(value = "工具箱常用列表")
    @GetMapping("/toolbox/commonlyUsed")
    public List<AsToolboxDto> commonlyUsed() {
        return userSettingFeignClient.commonlyUsed();
    }

    @ApiOperation(value = "最近使用菜单")
    @GetMapping("/toolbox/recentlyUsed")
    public List<AsToolboxDto> recentlyUsed() {
        List<AsToolboxDto> result = userSettingFeignClient.recentlyUsed();
        //判断当前企业是否开通设备管理
        List<String> activeAppList = appActivateFeignClient.configStatus();
        if (!activeAppList.contains(AppActivateConstant.EQUIPMENT)) {
            return result.stream().filter(item -> !item.getShowName().contains("设备")).collect(Collectors.toList());
        }
        if (!activeAppList.contains(AppActivateConstant.MATERIAL_INVENTORY)) {
            return result.stream().filter(item -> !"material-inventory".equals(item.getToolboxCode())).collect(Collectors.toList());
        }
        return result;
    }

    @ApiOperation(value = "个人中心广告地址")
    @GetMapping("/advertiseUrl")
    public List<AssetAdvertiseDto> advertiseUrl() {
        return userSettingFeignClient.advertiseUrl(1);
    }
}
