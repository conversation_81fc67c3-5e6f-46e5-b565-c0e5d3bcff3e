package com.niimbot.asset.controller.common.means;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.niimbot.activiti.ActWorkflowDto;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.ActWorkflowFieldFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.OrderSettingFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:26
 */
@Api(tags = "单据配置管理接口")
@RequestMapping("api/common/orderSetting")
@ResultController
@Validated
public class OrderSettingController {

    private final OrderSettingFeignClient orderSettingFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;
    private final ActWorkflowFieldFeignClient workflowFieldFeignClient;
    private final FormFeignClient formFeignClient;

    @Autowired
    public OrderSettingController(OrderSettingFeignClient orderSettingFeignClient,
                                  ActWorkflowFeignClient workflowFeignClient,
                                  ActWorkflowFieldFeignClient workflowFieldFeignClient,
                                  FormFeignClient formFeignClient) {
        this.orderSettingFeignClient = orderSettingFeignClient;
        this.workflowFeignClient = workflowFeignClient;
        this.workflowFieldFeignClient = workflowFieldFeignClient;
        this.formFeignClient = formFeignClient;
    }

    @ApiOperation(value = "单据类型列表")
    @GetMapping(value = "/orderType/list")
    public List<OrderTypeDto> listOrderType() {
        return orderSettingFeignClient.listOrderType();
    }

    @ApiOperation(value = "单据类型是否开启审批流")
    @GetMapping(value = "/orderType/enableWorkflow/{orderType}")
    public Boolean enableWorkflow(@PathVariable("orderType") Integer orderType) {
        return orderSettingFeignClient.enableWorkflow(orderType);
    }

    @ApiOperation(value = "是否存在已启用的审批流")
    @GetMapping("/hasEnable/{orderType}")
    public Boolean hasEnableFlow(@PathVariable("orderType") Integer orderType) {
        List<OrderTypeDto> orderTypeDtos = orderSettingFeignClient.listOrderType();
        Optional<OrderTypeDto> first = orderTypeDtos.stream().filter(f -> f.getType().equals(orderType)).findFirst();
        if (first.isPresent()) {
            String activitiKey = first.get().getActivitiKey();
            List<ActWorkflowDto> workflowDtoList = workflowFeignClient.list(activitiKey);
            for (ActWorkflowDto actWorkflowDto : workflowDtoList) {
                if (actWorkflowDto.getStatus().equals(DictConstant.SYS_ENABLE)) {
                    return true;
                }
            }
        }
        return false;
    }

    @ApiOperation(value = "单据类型下拉字典")
    @GetMapping(value = "/orderType/dict")
    public List<Map<String, ?>> orderTypeDict() {
        List<OrderTypeDto> orderType = orderSettingFeignClient.listOrderType();
        return orderType.stream().map(item ->
                ImmutableMap.of("value", item.getType(), "label", "资产" + StrUtil.replace(item.getName(), "单", ""))
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "通过单据类型查询字段列表")
    @GetMapping(value = "/orderField/list/{orderType}")
    @AutoConvert
    public OrderFieldListDto listOrderField(@PathVariable("orderType") Integer orderType) {
        List<OrderFieldDto> fields = orderSettingFeignClient.listOrderField(orderType);
        OrderFieldDto fieldDto = fields.stream().filter(dto -> dto.getUpdateTime() != null)
                .max(Comparator.comparingLong(dto ->
                        dto.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                .orElse(null);
        return new OrderFieldListDto()
                .setUpdateBy(fieldDto != null ? fieldDto.getUpdateBy() : null)
                .setUpdateTime(fieldDto != null ? fieldDto.getUpdateTime() : null)
                .setFields(fields);
    }

    @ApiOperation(value = "动态表单")
    @GetMapping(value = "/dynamicField/form/{orderType}")
    public List<OrderFieldDto> listDynamicForm(@PathVariable("orderType") Integer orderType) {
        return orderSettingFeignClient.listDynamicOrderField(orderType)
                .stream()
                .peek(field -> field.setName(Optional.ofNullable(field.getRename()).orElse(field.getName())))
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "动态表头")
    @GetMapping(value = "/dynamicField/head/{orderType}")
    public List<OrderHeadDto> listDynamicHead(@PathVariable("orderType") Integer orderType) {
        return orderSettingFeignClient.listDynamicOrderField(orderType).stream()
                .filter(filed -> !AssetConstant.APPENDIX_FIELD_CODE.equals(filed.getCode()))
                .map(field ->
                        new OrderHeadDto()
                                .setCode(field.getCode())
                                .setName(Optional.ofNullable(field.getRename()).orElse(field.getName()))
                                .setType(field.getType())
                ).collect(Collectors.toList());
    }

    @ApiOperation(value = "更新单据类型")
    @PutMapping("/updateOrderType")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean updateOrderType(@RequestBody OrderTypeDto dto) {
        return orderSettingFeignClient.updateOrderType(dto);
    }

    @ApiOperation(value = "启用单据流程")
    @PutMapping("/enableOrderTypeWorkflow")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean enableOrderTypeWorkflow(@RequestBody CusOrderTypeEnableWorkflowDto dto) {
        OrderTypeDto type = orderSettingFeignClient.getOrderTypeById(dto.getId());
        if (type == null) {
            throw new BusinessException(MeansResultCode.ORDER_TYPE_NOT_EXISTS, String.valueOf(dto.getId()));
        }
        if (dto.getEnableWorkflow() && !workflowFeignClient.hasWorkflow(type.getActivitiKey())) {
//            throw new BusinessException(SystemResultCode.ORDER_TYPE_NO_EXISTS_WORKFLOW);
            return false;
        }
        OrderTypeDto orderType = BeanUtil.copyProperties(dto, OrderTypeDto.class);
        return orderSettingFeignClient.updateOrderType(orderType);
    }

    @ApiOperation(value = "批量更新单据字段")
    @PutMapping("/updateOrderField/batch")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean updateOrderFieldBatch(@RequestBody
                                         @NotEmpty(message = "单据字段列表不能为空")
                                         List<@Valid OrderFieldDto> dtos) {
        return orderSettingFeignClient.updateOrderFieldBatch(dtos);
    }

    @ApiOperation(value = "更新单据字段")
    @PutMapping("/updateOrderField")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean updateOrderField(@RequestBody OrderFieldDto dto) {
        return orderSettingFeignClient.updateOrderField(dto);
    }

    @Deprecated
    @ApiOperation(value = "查询单据类型下的全部分支条件")
    @GetMapping("/workflow/branchCondition/{orderType}")
    public List<OrderFieldDto> branchCondition(@PathVariable("orderType") Integer orderType) {
        return workflowFieldFeignClient.branchCondition(orderType);
    }

    @ApiOperation(value = "排序单据字段")
    @PutMapping("/sortFields")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean sortFields(@RequestBody
                              @NotEmpty(message = "排序列表不能为空")
                              List<Long> fieldIds) {
        return orderSettingFeignClient.sortFields(fieldIds);
    }

    @ApiOperation(value = "查询单据类型下的表单中人员")
    @GetMapping("/workflow/formPerson/{orderType}")
    public List<OrderFieldDto> formPerson(@PathVariable("orderType") Integer orderType) {
        OrderFormTypeEnum orderFormTypeEnum = OrderFormTypeEnum.getOrderFormTypeEnum(orderType);
        FormVO formVO = formFeignClient.getTplByType(orderFormTypeEnum.getBizType());
        List<OrderFieldDto> orderFieldDtoList = formVO.getFormFields().stream().filter(f ->
                f.getFieldType().equals(FormFieldCO.YZC_EMP))
                .map(f -> {
                    OrderFieldDto fieldDto = new OrderFieldDto();
                    fieldDto.setName(f.getFieldName());
                    fieldDto.setCode(f.getFieldCode());
                    return fieldDto;
                }).collect(Collectors.toList());
        if ("asset".equals(orderFormTypeEnum.getModule())) {
            OrderFieldDto fieldDto = new OrderFieldDto();
            fieldDto.setName("资产管理员");
            fieldDto.setCode(QueryFieldConstant.ASSET_MANAGER_OWNER);
            orderFieldDtoList.add(fieldDto);
        }
        return orderFieldDtoList;
    }


    @ApiOperation(value = "查询单据类型下的表单中组织")
    @GetMapping("/workflow/formOrg/{orderType}")
    public List<OrderFieldDto> formOrg(@PathVariable("orderType") Integer orderType) {
        OrderFormTypeEnum orderFormTypeEnum = OrderFormTypeEnum.getOrderFormTypeEnum(orderType);
        FormVO formVO = formFeignClient.getTplByType(orderFormTypeEnum.getBizType());
        return formVO.getFormFields().stream().filter(f ->
                        f.getFieldType().equals(FormFieldCO.YZC_ORG))
                .map(f -> {
                    OrderFieldDto fieldDto = new OrderFieldDto();
                    fieldDto.setName(f.getFieldName());
                    fieldDto.setCode(f.getFieldCode());
                    return fieldDto;
                }).collect(Collectors.toList());
    }
}
