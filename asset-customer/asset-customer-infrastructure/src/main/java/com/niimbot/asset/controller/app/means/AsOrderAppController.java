package com.niimbot.asset.controller.app.means;

import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2021/1/5 11:24
 */

@Api(tags = "单据管理")
@ResultController
@RequestMapping("api/app/assetOrder")
@RequiredArgsConstructor
public class AsOrderAppController {

//    private final OrderService orderService;
//
//    @Deprecated
//    @ApiOperation(value = "[已作废]App单据分页列表")
//    @AutoConvert
//    @GetMapping("/page")
//    public PageUtils<OrderService.Record> page(AsOrderQueryDto dto) {
//        return orderService.page(dto);
//    }



}
