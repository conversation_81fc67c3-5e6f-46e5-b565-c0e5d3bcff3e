package com.niimbot.asset.controller.common.system;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AbstractPermissionChangedService;
import com.niimbot.asset.service.EmployeeService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentMaintainTaskFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.exception.category.FeignClientException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.CusEmpRegisterDto;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeExcelDto;
import com.niimbot.system.CusEmployeeOptDto;
import com.niimbot.system.CusEmployeePreDelCheck;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.CusEmployeeTransferDto;
import com.niimbot.system.CusUserOrgDto;
import com.niimbot.system.EmployeeModifyDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ModifyEmployeeAndTransferData;
import com.niimbot.system.RemoveEmployDto;
import com.niimbot.system.UserCenterPCDto;
import com.niimbot.validate.NationalCodeValidate;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 员工管理
 *
 * <AUTHOR>
 * @Date 2020/11/12
 */
@Api(tags = "员工管理")
@ResultController
@RequestMapping("api/common/employee")
@Validated
@Slf4j
public class CusEmployeeController {

    @Resource
    private CusEmployeeFeignClient feignClient;

    @Resource
    private AssetFeignClient assetFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private FileUploadConfig uploadConfig;

    @Resource
    private RedisService redisService;

    @Resource
    private EmployeeService employeeService;

    @Resource(name = "kickOffPermissionChangedService")
    private AbstractPermissionChangedService kickOffPermissionChangedService;

    @Resource
    private ActWorkflowFeignClient actWorkflowFeignClient;

    @Resource
    private AccountCenterFeignClient accountCenterFeignClient;

    @Resource
    private EquipmentMaintainTaskFeignClient equipmentMaintainTaskFeignClient;

    @ApiOperation(value = "新增员工")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean save(@RequestBody @Validated({Insert.class}) CusEmployeeOptDto employee) {
        return feignClient.save(employee);
    }

    @ApiOperation(value = "新增员工2.0（OpenAPI）")
    @RepeatSubmit
    @PostMapping("v2")
    public String saveV2(@RequestBody @Validated({Insert.class}) CusEmployeeOptDto employee) {
        try {
            return feignClient.saveV2(employee);
        } catch (FeignClientException e) {
            throw new BusinessException(e.getFailureResult().getCode(),e.getFailureResult().getMessage());
        }
    }

    @ApiOperation(value = "修改员工")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated({Update.class}) CusEmployeeOptDto employee) {
        CusEmployeeDto info = feignClient.getInfo(employee.getId());
        if (info == null) {
            throw new BusinessException(SystemResultCode.EMP_NOT_EXISTS);
        }
        // 检测场景
        List<CusEmployeeTransferDto> transfers = employee.getTransfers();
        if (CollUtil.isNotEmpty(transfers)) {
            Map<Long, Long> transferMap = transfers.stream().collect(Collectors.toMap(CusEmployeeTransferDto::getFrom, CusEmployeeTransferDto::getTo));
            List<Long> orgIds = info.getOrgList().stream().map(CusUserOrgDto::getOrgId).collect(Collectors.toList());
            Set<Long> fromIds = transferMap.keySet();
            if (!new HashSet<>(orgIds).containsAll(fromIds)) {
                throw new BusinessException(SystemResultCode.EMP_TRANSFER_ERROR);
            }
        }
        feignClient.edit(employee);
        return true;
    }

    @ApiOperation("批变更员工组织")
    @PutMapping("/list")
    public Boolean batchModifyOrg(@RequestBody @Validated({CusEmployeeOptDto.ModifyOrg.class}) List<@Valid CusEmployeeOptDto> employees) {
        return feignClient.batchModifyOrg(employees);
    }

    @ApiOperation(value = "删除前检查员工资源")
    @GetMapping("/preDeleteCheck/{userId}")
    public CusEmployeePreDelCheck preDeleteCheck(@PathVariable("userId") Long userId) {
        Boolean assetUnderManagement = assetFeignClient.checkManageAsset(userId);
        Boolean assetUser = assetFeignClient.checkUseAsset(userId);
//        Boolean inventoryTask = inventoryFeignClient.checkInventory(userId);
        Boolean approvalTask = actWorkflowFeignClient.checkWorkflow(userId);
        Boolean userHasTask = equipmentMaintainTaskFeignClient.userHasTask(userId);
        return new CusEmployeePreDelCheck()
                .setAssetUnderManagement(assetUnderManagement)
                .setAssetUser(assetUser)
                // 由于异动逻辑里盘点任务转移逻辑被屏蔽了 所以这里页暂时屏蔽
                .setInventoryTask(false)
                .setApprovalTask(approvalTask)
                .setEntMatTask(userHasTask);
    }

    @ApiOperation("批量删除或变更员工组织前的检查")
    @PostMapping("/preBatchCheck/{type}")
    public Map<String, List<ModifyEmployeeAndTransferData>> preBatchDelete(@RequestParam(required = false) String spread, @PathVariable String type, @ApiParam(name = "ID不能为空", value = "ModifyEmployeeAndTransferData") @RequestBody List<ModifyEmployeeAndTransferData> modifyEmployeeAndTransferData) {
        boolean typeIsAll = "all".equals(type);
        // 1.获取当前企业超管信息
        CusEmployeeDto admin = feignClient.getAdminInfo();
        // 2.被删除员工中包含超管，不允许删除
        boolean isAdmin = modifyEmployeeAndTransferData.stream().anyMatch(data -> data.getId().equals(admin.getId()));
        if (typeIsAll && isAdmin) {
            throw new BusinessException(400, "员工" + admin.getEmpName() + "超管");
        }
        // 3.检查资产关系
        Map<String, List<ModifyEmployeeAndTransferData>> result = new HashMap<>(5);
        for (ModifyEmployeeAndTransferData data : modifyEmployeeAndTransferData) {
            data.setNameNo(data.getName() + " (" + data.getNo() + ") ");
            // 管理的资产
            if ((typeIsAll || "manageAsset".equals(type)) && assetFeignClient.checkManageAsset(data.getId())) {
                List<ModifyEmployeeAndTransferData> manageAsset = result.getOrDefault("manageAsset", new ArrayList<>());
                manageAsset.add(data);
                result.put("manageAsset", manageAsset);
            }
            // 使用的资产
            if ((typeIsAll || "useAsset".equals(type)) && assetFeignClient.checkUseAsset(data.getId())) {
                List<ModifyEmployeeAndTransferData> useAsset = result.getOrDefault("useAsset", new ArrayList<>());
                useAsset.add(data);
                result.put("useAsset", useAsset);
            }
            // 审批任务
            if ((typeIsAll || "approvalTask".equals(type)) && actWorkflowFeignClient.checkWorkflow(data.getId())) {
                List<ModifyEmployeeAndTransferData> approvalTask = result.getOrDefault("approvalTask", new ArrayList<>());
                approvalTask.add(data);
                result.put("approvalTask", approvalTask);
            }
            // 设备保养任务
            if ((typeIsAll || "entMatTask".equals(type)) && equipmentMaintainTaskFeignClient.userHasTask(data.getId())) {
                List<ModifyEmployeeAndTransferData> entMatTask = result.getOrDefault("entMatTask", new ArrayList<>());
                entMatTask.add(data);
                result.put("entMatTask", entMatTask);
            }
        }
        if (!CollUtil.isEmpty(result) && "spread".equals(spread)) {
            result.forEach((k, v) -> {
                List<ModifyEmployeeAndTransferData> copy = new ArrayList<>(v);
                v.clear();
                for (ModifyEmployeeAndTransferData data : copy) {
                    if (Objects.nonNull(data.getOrgList()) && data.getOrgList().size() == 1) {
                        ModifyEmployeeAndTransferData.OrgInFo orgInFo = data.getOrgList().get(0);
                        data.setOrgId(orgInFo.getOrgId()).setOrgName(orgInFo.getOrgName()).setOrgList(null);
                        v.add(data);
                    }
                    if (Objects.nonNull(data.getOrgList()) && data.getOrgList().size() > 1) {
                        for (ModifyEmployeeAndTransferData.OrgInFo orgInFo : data.getOrgList()) {
                            v.add(new ModifyEmployeeAndTransferData().setId(data.getId()).setName(data.getName()).setNo(data.getNo()).setNameNo(data.getNameNo()).setOrgId(orgInFo.getOrgId()).setOrgName(orgInFo.getOrgName()));
                        }
                    }
                }
            });
        }
        // 4.结果为空时可直接变更员工
        return result;
    }

    @ApiOperation(value = "删除员工")
    @RepeatSubmit
    @DeleteMapping("/one")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean removeOne(@RequestBody @Validated RemoveEmployDto employ) {
        // 查出员工的登录信息
        CusUserDto cusUserDto = accountCenterFeignClient.getEmpLoginInfo(employ.getEmployeeId());
        // 删除员工
        Boolean remove = feignClient.remove(employ);
        // 删除员工成功且该员工有关联账号时，解除账号并踢出登录
        if (remove && Objects.nonNull(cusUserDto)) {
            // 踢出登录
            kickOffPermissionChangedService.permissionChange(Collections.singletonList(cusUserDto));
        }
        return remove;
    }

    @ApiOperation("批量删除员工")
    @RepeatSubmit
    @DeleteMapping("/list")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean removeList(@RequestBody @Validated List<RemoveEmployDto> employDtos) {
        List<RemoveEmployDto> result = new ArrayList<>();
        Map<Long, List<RemoveEmployDto>> map = employDtos.stream().collect(Collectors.groupingBy(RemoveEmployDto::getEmployeeId));
        map.forEach((k, v) -> {
            if (v.size() == 1) {
                result.add(v.get(0));
            }
            if (v.size() > 1) {
                RemoveEmployDto temp = new RemoveEmployDto();
                temp.setEmployeeId(k);
                temp.setEmployeeName(v.get(0).getEmployeeName());
                for (RemoveEmployDto dto : v) {
                    if (Objects.nonNull(dto.getManageAsset())) {
                        temp.setManageAsset(dto.getManageAsset());
                    }
                    if (Objects.nonNull(dto.getUseAsset())) {
                        temp.setUseAsset(dto.getUseAsset());
                    }
                    if (Objects.nonNull(dto.getApprovalTask())) {
                        temp.setApprovalTask(dto.getApprovalTask());
                    }
                    if (Objects.nonNull(dto.getInventoryTask())) {
                        temp.setInventoryTask(dto.getInventoryTask());
                    }
                    if (Objects.nonNull(dto.getEntMatTask())) {
                        temp.setEntMatTask(dto.getEntMatTask());
                    }
                }
                result.add(temp);
            }
        });
        for (RemoveEmployDto dto : result) {
            try {
                this.removeOne(dto);
            } catch (Exception e) {
                // 删除员工错误
            }
        }
        return true;
    }

    @ApiOperation(value = "数据查询分页列表")
    @GetMapping(value = "/page")
    public PageUtils<CusEmployeeDto> page(@Validated CusEmployeeQueryDto dto) {
        CusEmployeeDto adminInfo = feignClient.getAdminInfo();
        PageUtils<CusEmployeeDto> page = feignClient.page(dto);
        page.getList().forEach(cusEmployeeDto -> cusEmployeeDto.setIsAdmin(cusEmployeeDto.getId().equals(adminInfo.getId())));
        return page;
    }

    // todo 员工列表，感觉废弃了，需要前端一起排查
    @ApiOperation(value = "员工列表，感觉废弃了，需要前端一起排查")
    @GetMapping(value = "/list")
    public List<CusEmployeeDto> list(CusEmployeeQueryDto dto) {
        CusEmployeeDto adminInfo = feignClient.getAdminInfo();
        List<CusEmployeeDto> list = feignClient.actList(dto);
        list.forEach(cusEmployeeDto -> cusEmployeeDto.setIsAdmin(cusEmployeeDto.getId().equals(adminInfo.getId())));
        return list;
    }

    // todo 不包括超管的员工列表, 这里业务也要换成选择器
    @ApiOperation(value = "不包括超管的员工列表, 这里业务也要换成选择器")
    @GetMapping(value = "/listBySupertubeTransfer")
    public List<CusEmployeeDto> listBySupertubeTransfer(CusEmployeeQueryDto dto) {
        // 账号已激活的员工
        dto.setPageSize(50);
        List<CusEmployeeDto> list = feignClient.listHasAccount(dto);
        CusEmployeeDto adminInfo = feignClient.getAdminInfo();
        list.removeIf(cusEmployeeDto -> cusEmployeeDto.getId().equals(adminInfo.getId()));
        return list;
    }

    @ApiOperation(value = "【工作流】员工列表")
    @GetMapping(value = "/list/activiti")
    public List<CusEmployeeDto> actList(CusEmployeeQueryDto dto) {
        CusEmployeeDto adminInfo = feignClient.getAdminInfo();
        // orgId为空时默认查前20条
        if (Objects.isNull(dto.getOrgId())) {
            dto.setIsOnlyShowTop(true).setPageSize(20);
        }
        List<CusEmployeeDto> list = feignClient.actList(dto);
        // 不展示没有账号的员工
        if (dto.getIsNotShowNoAccountEmp()) {
            list.removeIf(emp -> Objects.isNull(emp.getAccountId()) || emp.getAccountStatus() != 3);
        }
        list.forEach(cusEmployeeDto -> cusEmployeeDto.setIsAdmin(cusEmployeeDto.getId().equals(adminInfo.getId())));
        return list;
    }

    @ApiOperation(value = "员工详情")
    @GetMapping(value = "/{id}")
    public CusEmployeeDto getById(@PathVariable Long id) {
        List<CusEmployeeDto> list = feignClient.actList(new CusEmployeeQueryDto().setId(id));
        if (CollUtil.isEmpty(list)) {
            return new CusEmployeeDto();
        }
        return list.get(0);
    }

    @ApiOperation(value = "当前员工信息")
    @GetMapping(value = "/currentUserInfo")
    public CusEmployeeDto currentUserInfo() {
        return feignClient.currentUserInfo();
    }

    @ApiOperation(value = "新增获取推荐工号")
    @GetMapping("/recommendEmpNo")
    public String recommendEmpNo() {
        return feignClient.recommendEmpNo();
    }

    @ApiOperation(value = "通过Ids查询员工数据")
    @PostMapping("/ids")
    public List<Map<String, ?>> listByIds(@RequestBody List<Long> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return ListUtil.empty();
        }
        return feignClient.listByIds(empIds).stream().map(item ->
                ImmutableMap.of("id", item.getId(), "name", item.getEmpName(), "code", item.getEmpNo())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "查询组织员工字典")
    @GetMapping("/dict")
    public List<Map<String, Object>> dict(
            @ApiParam(name = "kw", value = "[名称]")
            @RequestParam(value = "kw", required = false) String kw,
            @ApiParam(name = "id", value = "组织ID")
            @RequestParam(value = "id", required = false) Long orgId,
            @RequestParam(required = false) boolean isNotShowNoAccountEmp) {
        List<Map<String, Object>> collect = new ArrayList<>();
        List<CusEmployeeDto> list = feignClient.actList(new CusEmployeeQueryDto().setKw(kw).setOrgId(orgId).setFilterPerm(false));
        if (isNotShowNoAccountEmp) {
            list.removeIf(v -> Objects.isNull(v.getAccountId()) || v.getAccountStatus() != 3);
        }
        for (CusEmployeeDto emp : list) {
            Map<String, Object> map = new HashMap<>(4);
            map.put("label", emp.getEmpName());
            map.put("value", emp.getId());
            map.put("empNo", emp.getEmpNo());
            map.put("relation", emp.getOrgList());
            collect.add(map);
        }
        return collect;
    }

    @ApiOperation(value = "查询组织员工字典")
    @GetMapping("/select/dict")
    public List<Map<String, Object>> selectDict(
            @ApiParam(name = "kw", value = "[名称]")
            @RequestParam(value = "kw", required = false) String kw,
            @ApiParam(name = "id", value = "组织ID")
            @RequestParam(value = "id", required = false) Long orgId,
            @ApiParam(name = "isNotShowNoAccountEmp", value = "是否展示无账号的员工")
            @RequestParam(value = "isNotShowNoAccountEmp", required = false) boolean isNotShowNoAccountEmp) {
        List<Map<String, Object>> collect = new ArrayList<>();
        List<CusEmployeeDto> list = feignClient.actList(new CusEmployeeQueryDto().setKw(kw).setOrgId(orgId).setFilterPerm(true));
        // 不展示
        if (isNotShowNoAccountEmp) {
            list.removeIf(emp -> Objects.isNull(emp.getAccountId()) || emp.getAccountStatus() != 3);
        }
        for (CusEmployeeDto emp : list) {
            Map<String, Object> map = new HashMap<>(4);
            map.put("label", emp.getEmpName());
            map.put("value", emp.getId());
            map.put("empNo", emp.getEmpNo());
            map.put("relation", emp.getOrgList());
            map.put("disabled", false);
            collect.add(map);
        }
        return collect;
    }

    @ApiOperation(value = "获取组织下主管信息")
    @GetMapping("/directorInfo")
    public List<CusEmployeeDto> getDirectorByOrgId(@NotNull(message = "组织不能为空") Long orgId) {
        return feignClient.getDirectorByOrgId(orgId);
    }

    @ApiOperation(value = "二维码邀请员工-注册开通账号")
    @PostMapping(value = "/scanInvite/openAccount")
    @RepeatSubmit
    @ResultMessage("开通成功")
    public Map<String, String> inviteOpenAccount(@RequestBody @Validated CusEmpRegisterDto dto) {
        NationalCodeValidate.checkCNMobile(dto.getNationalCode(), dto.getMobile());
        if (!smsCodeFeignClient.checkSmsCode(dto.getMobile(), dto.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        // 单独补充注册来源
        dto.setSource(11);
        return feignClient.inviteOpenAccount(dto);
    }

    @ApiOperation(value = "二维码邀请员工-生成二维码链接")
    @GetMapping(value = "/scanInvite/generateQRLink/{empId}")
    public String generateQRLink(@ApiParam(name = "empId", value = "员工id")
                                 @PathVariable("empId") Long empId) throws MalformedURLException {
        String domain = uploadConfig.getDomain();
        URI host = URLUtil.getHost(new URL(domain));
        return host + "/#/scan-invite?inviteKey=" + empId;
    }

    @ApiOperation(value = "【PC】导出员工模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        employeeService.exportTemplate(response);
    }

    @ApiOperation(value = "【PC】导入员工模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.EMPLOYEE, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.EMPLOYEE, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
            redisService.del(RedisConstant.companyImportKey(ImportService.EMPLOYEE, companyId));
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            employeeService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return employeeService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.EMPLOYEE, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.EMPLOYEE, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
            redisService.del(RedisConstant.companyImportKey(ImportService.EMPLOYEE, companyId));
        }
        employeeService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.EMPLOYEE, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.EMPLOYEE));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation(value = "导出")
    @PostMapping("/export")
    public void export(@RequestBody CusEmployeeQueryDto queryDto, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(CusEmployeeExcelDto.class);
            // 查询数据
            List<CusEmployeeDto> employeeList;
            if (!CollUtil.isEmpty(queryDto.getIds())) {
                employeeList = feignClient.listByIds(queryDto.getIds());
            } else {
                PageUtils<CusEmployeeDto> excel = feignClient.page(queryDto);
                employeeList = excel.getList();
            }
            String fileName = "员工-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }

            List<CusEmployeeExcelDto> collect = employeeList.stream().map(emp -> {
                CusEmployeeExcelDto excelDto = new CusEmployeeExcelDto()
                        .setId(emp.getId())
                        .setAccountActivationStatus(emp.getAccountActivationStatus())
                        .setEmpNo(emp.getEmpNo())
                        .setEmpName(emp.getEmpName())
                        .setEmail(emp.getEmail())
                        .setMobile(emp.getMobile())
                        .setPosition(emp.getPosition());
                String orgNames = emp.getOrgList().stream().map(CusUserOrgDto::getOrgName).collect(Collectors.joining("，"));
                excelDto.setOrgNames(orgNames);
                return excelDto;
            }).collect(Collectors.toList());
            ExcelUtils.export(response, new ExcelExportDto(headerData, collect), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "更换手机号【第一步】、校验旧手机号")
    @GetMapping("/verifyOldMobile")
    public Boolean confirmOld(@NotBlank(message = "请输入手机号")
                              @Size(max = 11, message = "手机号最多11位") String oldMobile,
                              @NotBlank(message = "请输入区号") String nationalCode,
                              @Size(min = 4, max = 4, message = "验证码为4位")
                              @NotBlank(message = "验证码不能为空") String smsCode) {
        NationalCodeValidate.checkCNMobile(nationalCode, oldMobile);
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        if (cusUser != null) {
            Long employeeId = cusUser.getId();
            CusEmployeeDto info = feignClient.getInfo(employeeId);
            if (!Objects.equals(oldMobile, info.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_PHONE_CHANGE_OLD);
            }
        }
        if (!smsCodeFeignClient.checkSmsCode(oldMobile, smsCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return true;
    }

    @ApiOperation(value = "更换手机号【第二步】")
    @PostMapping("/changeMobile")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeMobile(@RequestBody UserCenterPCDto.ChangeMobileDTO dto) {
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        if (cusUser != null) {
            CusEmployeeDto info = feignClient.getInfo(cusUser.getId());
            if (Objects.equals(dto.getNewMobile(), info.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_PHONE_CHANGE_NEW_SAME);
            }
        }
        NationalCodeValidate.checkCNMobile(dto.getNationalCode(), dto.getNewMobile());
        CusEmployeeDto employeeDto = feignClient.checkMobile(dto.getNewMobile());
        if (employeeDto != null) {
            throw new BusinessException(SystemResultCode.EMPLOYEE_FIELD_EXIST, "手机号");
        }
        if (!smsCodeFeignClient.checkSmsCode(dto.getNewMobile(), dto.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return feignClient.changeEmpMobile(dto.getNewMobile());
    }

    @ApiOperation(value = "修改员工工号")
    @PostMapping("/modifyEmpNo")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean updateNo(@RequestBody EmployeeModifyDto employeeModifyDto) {
        return feignClient.modifyEmployeeNo(employeeModifyDto);
    }

    @ApiOperation(value = "校验员工是否重名")
    @PostMapping("/verifyEmpName")
    public Boolean verifyEmpName(@RequestBody CusEmployeeOptDto employee) {
        if (StrUtil.isBlank(employee.getEmpName())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "请填写姓名");
        }
        return feignClient.verifyEmpName(employee);
    }

}
