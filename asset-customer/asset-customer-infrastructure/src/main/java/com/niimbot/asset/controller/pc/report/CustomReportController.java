package com.niimbot.asset.controller.pc.report;

import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.CustomReportService;
import com.niimbot.asset.service.MaterialReportQueryFieldService;
import com.niimbot.asset.service.ReportQueryFieldService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.report.CustomReportFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;
import com.niimbot.report.DimensionFieldDto;
import com.niimbot.report.NormItemDto;
import com.niimbot.report.ReportConfigItemDto;
import com.niimbot.report.StatisticsConditionDescDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/27 上午10:05
 */
@Slf4j
@Api(tags = "自定义报表")
@ResultController
@RequestMapping("api/pc/report/custom/")
@RequiredArgsConstructor
public class CustomReportController {

    private final CustomReportFeignClient customReportFeignClient;

    private final MaterialReportQueryFieldService materialReportQueryFieldService;

    private final ReportQueryFieldService reportQueryFieldService;

    private final CustomReportService customReportService;

    /**
     * 资产自定义统计图和统计表配置
     *
     * @param configDto
     * @return
     */
    @ApiOperation(value = "自定义报表配置")
    @PostMapping("config")
    public DimensionDataDto configReport(@RequestBody CustomReportConfigDto configDto) {
        return customReportFeignClient.configReport(configDto);
    }

    /**
     * 编辑资产自定义统计图和统计表配置
     *
     * @param configDto
     * @return
     */
    @ApiOperation(value = "编辑自定义报表")
    @PostMapping("edit")
    public DimensionDataDto editReport(@RequestBody CustomReportConfigDto configDto) {
        return customReportFeignClient.editReport(configDto);
    }

    /**
     * 查询资产自定义统计图和统计表数据
     *
     * @param configDto
     * @return
     */
    @ApiOperation(value = "自定义报表查询")
    @PostMapping("query")
    public DimensionDataDto query(@RequestBody CustomReportConfigDto configDto) {
        return customReportFeignClient.query(configDto);
    }

    @ApiOperation(value = "资产自定义明细报表配置")
    @PostMapping("detailReportConfig")
    public AssetStatisticsDataDto detailReportConfig(@RequestBody CustomReportConfigDto customReportConfigDto) {
        return customReportFeignClient.detailReportConfig(customReportConfigDto);
    }

    @ApiOperation(value = "编辑资产自定义明细报表")
    @PostMapping("editDetail")
    public AssetStatisticsDataDto editDetailReport(@RequestBody CustomReportConfigDto customReportConfigDto) {
        return customReportFeignClient.editDetailReport(customReportConfigDto);
    }

    @ApiOperation(value = "资产自定义明细报表查询")
    @PostMapping("queryDetailReport")
    public AssetStatisticsDataDto queryDetailReport(@RequestBody CustomReportConfigDto customReportConfigDto) {
        return customReportFeignClient.queryDetailReport(customReportConfigDto);
    }

    @ApiOperation(value = "查询报表配置信息")
    @GetMapping("queryReportConfig/{id}")
    public CustomReportConfigDto queryReportConfig(@PathVariable("id") Long id) {
        CustomReportConfigDto result = customReportFeignClient.queryReportConfig(id);
        //统计范围需要额外给前端进行处理下
        if (CollUtil.isEmpty(result.getStatisticCondition())) {
            return result;
        }

        List<QueryConditionDto> allQueryCondition = new ArrayList<>();
        if (result.getBizType() == 1) {
            allQueryCondition = reportQueryFieldService.reportAllQueryField(QueryFieldConstant.TYPE_ASSET_REPORT_QUERY);
        } else if (result.getBizType() == 2) {
            allQueryCondition = materialReportQueryFieldService.allQueryField(QueryFieldConstant.TYPE_MATERIAL_STOCK_REPORT_QUERY);
        } else if (result.getBizType() == 3) {
            allQueryCondition = materialReportQueryFieldService.allQueryField(QueryFieldConstant.TYPE_MATERIAL_STORAGE_REPORT_QUERY);
        } else if (result.getBizType() == 4) {
            allQueryCondition = materialReportQueryFieldService.allQueryField(QueryFieldConstant.TYPE_MATERIAL_OUT_REPORT_QUERY);
        } else {
            allQueryCondition = materialReportQueryFieldService.allQueryField(QueryFieldConstant.TYPE_MATERIAL_RECEIPT_REPORT_QUERY);
        }
        Map<String, QueryConditionDto> allQueryConditionMap = allQueryCondition.stream()
                .collect(Collectors.toMap(QueryConditionDto::getCode, value -> value, (v1, v2) -> v2));
        for (QueryConditionDto item : result.getStatisticCondition()) {
            item.setFieldProps(allQueryConditionMap.get(item.getCode()).getFieldProps());
            item.setOperators(allQueryConditionMap.get(item.getCode()).getOperators());
        }
        return result;
    }

    @ApiOperation(value = "分析维度筛选条件查询")
    @GetMapping("dimension/query/field")
    public DimensionFieldDto dimensionQueryField(Long reportId) {
        return customReportFeignClient.dimensionQueryField(reportId);
    }

    @ApiOperation(value = "数据指标字段查询")
    @GetMapping("norm/query/field")
    public List<NormItemDto> normQueryField(Long reportId) {
        return customReportFeignClient.normQueryField(reportId);
    }

    @ApiOperation(value = "统计范围字段查询")
    @GetMapping("statisticsCondition/query/field")
    public List<QueryConditionDto> statisticsConditionField(Long reportId) {
        return customReportFeignClient.statisticsConditionField(reportId);
    }

    @ApiOperation(value = "查询企业自定义报表列表")
    @GetMapping("queryAll")
    public List<ReportConfigItemDto> queryAll() {
        return customReportFeignClient.queryAll();
    }

    @ApiOperation(value = "删除自定义报表配置")
    @DeleteMapping("remove/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean dropConfig(@PathVariable Long id) {
        return customReportFeignClient.dropConfig(id);
    }

    @ApiOperation(value = "统计范围描述")
    @GetMapping("statisticsConditionDesc/{id}")
    public List<StatisticsConditionDescDto> queryStatisticsConditionDesc(@PathVariable Long id) {
        return customReportFeignClient.statisticsConditionDesc(id);
    }

    @ApiOperation(value = "明细表数据导出")
    @PostMapping("detailExport")
    public ExportResponse detailExport(@RequestBody CustomReportConfigDto customReportConfigDto) {
        return customReportService.exportReportDetail(customReportConfigDto);
    }
}
