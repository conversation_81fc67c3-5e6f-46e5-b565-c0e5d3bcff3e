package com.niimbot.asset.controller.common.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.TreeUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AreaService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.AreaFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.means.AssetQueryDto;
import com.niimbot.means.CategoryExportDto;
import com.niimbot.system.*;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 20201111
 */
@Api(tags = "区域管理")
@ResultController
@RequestMapping("api/common/area")
@Slf4j
public class AreaController {

    private final AreaFeignClient areaFeignClient;

    private final AssetFeignClient assetFeignClient;

    private final RedisService redisService;

    private final AreaService areaService;

    @Autowired
    private DictConvertUtil dictConvertUtil;

    @Autowired
    public AreaController(AreaFeignClient areaFeignClient,
                          AssetFeignClient assetFeignClient,
                          RedisService redisService,
                          AreaService areaService) {
        this.areaFeignClient = areaFeignClient;
        this.assetFeignClient = assetFeignClient;
        this.redisService = redisService;
        this.areaService = areaService;
    }

    @ApiOperation(value = "数据查询分页列表")
    @AutoConvert
    @GetMapping(value = "/page")
    public PageUtils<AreaDto> page(AreaQueryDto queryDto) {
        return areaFeignClient.selectAreaPage(queryDto);
    }

    @ApiOperation("区域打印列表查询")
    @GetMapping("/printAreaPage")
    public PageUtils<AreaAssetDto> printAreaPage(PrintAreaPageQueryDto dto) {
        return areaFeignClient.printAreaPage(dto);
    }

    @ApiOperation("获取一级区域内满足条件的所有区域ID")
    @GetMapping("/printAreaIds")
    public List<Long> areaIds(PrintAreaPageQueryDto dto) {
        return areaFeignClient.printAreaIds(dto);
    }

    @ApiOperation("区域打印--一级区域列表")
    @GetMapping("/root/node/list")
    public List<AreaDto> rootNodeList(@RequestParam Long companyId) {
        return areaFeignClient.rootNodeList(companyId);
    }

    @ApiOperation(value = "全部数据查询列表")
    @GetMapping(value = "/list")
    public List<AreaDto> list(AreaQueryDto queryDto) {
        return areaFeignClient.areaList(queryDto.setFilterPerm(false));
    }

    @ApiOperation(value = "通过Id数据查询")
    @GetMapping(value = "/{id}")
    @AutoConvert
    public AreaDto getInfo(@PathVariable(value = "id") Long id) {
        return areaFeignClient.selectAreaById(id);
    }

    @ApiOperation(value = "新增区域数据")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    @AuditLog(Auditable.Action.ADD_MEANS_AREA)
    public AreaDto areaAdd(@Validated(Insert.class) @RequestBody AreaDto area) {
        return new AreaDto().setId(areaFeignClient.insertArea(area));
    }

    @ApiOperation(value = "编辑区域数据")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @AuditLog(Auditable.Action.UPT_MEANS_AREA)
    public Boolean edit(@Validated(Update.class) @RequestBody AreaDto area) {
        return areaFeignClient.updateArea(area);
    }

    @ApiOperation(value = "删除区域数据")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> ids) {
        // 该区域下存在非报废、丢失、出售、赠送状态的资产不可删除
        // todo... 加入资产状态值
        AssetQueryDto assetQueryDto = new AssetQueryDto();
        for (Long areaId : ids) {
            if (assetFeignClient.checkAsset(AssetConstant.CHECK_ASSET_AREA, areaId, assetQueryDto)) {
                throw new BusinessException(MeansResultCode.DELETE_SOURCE_CHECK_ASSET, "区域");
            }
        }
        List<AuditableOperateResult> results = areaFeignClient.deleteAreaByIds(ids);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MEANS_AREA, results));
        return true;
    }

    @ApiOperation(value = "新增获取推荐区域编码")
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return areaFeignClient.recommendCode();
    }

    @ApiOperation(value = "查询区域树")
    @GetMapping("/tree")
    @AutoConvert
    public List<Tree<String>> tree(
            @ApiParam(name = "orgId", value = "[公司id]")
            @RequestParam(value = "orgId", required = false) Long orgId,
            @ApiParam(name = "kw", value = "[名称、编码]")
            @RequestParam(value = "kw", required = false) String kw) {
        // 查询全部数据
        AreaQueryDto queryDto = new AreaQueryDto()
                .setKw(kw).setOrgId(orgId).setFilterPerm(false);
        if (StrUtil.isNotBlank(kw)) {
            // 查询全部数据
            queryDto.setBuildTree(false);
            List<AreaDto> list = areaFeignClient.areaList(queryDto);
            return list.stream().filter(f -> BooleanUtil.isFalse(f.getDisabled())).map(area -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(area.getId()));
                tree.setParentId(Convert.toStr(area.getPid()));
                tree.setName(area.getAreaName());
                tree.setWeight(area.getSortNum());
                tree.putExtra("title", area.getAreaName());
                tree.putExtra("value", Convert.toStr(area.getId()));
                tree.putExtra("code", area.getAreaCode());
                tree.putExtra("sortNum", area.getSortNum());
                tree.putExtra("level", area.getLevel());
                tree.putExtra("disabled", area.getDisabled());
                return tree;
            }).collect(Collectors.toList());
        } else {
            if (orgId == null) {
                return ListUtil.empty();
            }
//            BusinessExceptionUtil.checkNotNull(orgId, "公司Id不能为空");
            // 查询全部数据
            queryDto.setBuildTree(true);
            List<AreaDto> list = areaFeignClient.areaList(queryDto);
            // 构建树结构
            return TreeUtils.build(list, (area, tree) -> {
                tree.setId(Convert.toStr(area.getId()));
                tree.setParentId(Convert.toStr(area.getPid()));
                tree.setName(area.getAreaName());
                tree.setWeight(area.getSortNum());
                tree.putExtra("title", area.getAreaName());
                tree.putExtra("value", Convert.toStr(area.getId()));
                tree.putExtra("code", area.getAreaCode());
                tree.putExtra("sortNum", area.getSortNum());
                tree.putExtra("level", area.getLevel());
                tree.putExtra("disabled", area.getDisabled());
            });
        }
    }

    @ApiOperation(value = "查询区域树[带权限]")
    @GetMapping("/tree/permission")
    @AutoConvert
    public List<Tree<String>> permissionTree(
            @ApiParam(name = "orgId", value = "[公司id]")
            @RequestParam(value = "orgId", required = false) Long orgId,
            @ApiParam(name = "kw", value = "[名称、编码]")
            @RequestParam(value = "kw", required = false) String kw) {
        AreaQueryDto queryDto = new AreaQueryDto()
                .setKw(kw).setOrgId(orgId).setFilterPerm(true);
        if (StrUtil.isNotBlank(kw)) {
            // 查询全部数据
            queryDto.setBuildTree(false);
            List<AreaDto> list = areaFeignClient.areaList(queryDto);
            return list.stream().filter(f -> BooleanUtil.isFalse(f.getDisabled())).map(area -> {
                Tree<String> tree = new Tree<>();
                tree.setId(Convert.toStr(area.getId()));
                tree.setParentId(Convert.toStr(area.getPid()));
                tree.setName(area.getAreaName());
                tree.setWeight(area.getSortNum());
                tree.putExtra("title", area.getAreaName());
                tree.putExtra("value", Convert.toStr(area.getId()));
                tree.putExtra("code", area.getAreaCode());
                tree.putExtra("sortNum", area.getSortNum());
                tree.putExtra("level", area.getLevel());
                tree.putExtra("disabled", area.getDisabled());
                return tree;
            }).collect(Collectors.toList());
        } else {
            if (orgId == null) {
                return ListUtil.empty();
            }
//            BusinessExceptionUtil.checkNotNull(orgId, "公司Id不能为空");
            // 查询全部数据
            queryDto.setBuildTree(true);
            List<AreaDto> list = areaFeignClient.areaList(queryDto);
            // 构建树结构
            return TreeUtils.build(list, (area, tree) -> {
                tree.setId(Convert.toStr(area.getId()));
                tree.setParentId(Convert.toStr(area.getPid()));
                tree.setName(area.getAreaName());
                tree.setWeight(area.getSortNum());
                tree.putExtra("title", area.getAreaName());
                tree.putExtra("value", Convert.toStr(area.getId()));
                tree.putExtra("code", area.getAreaCode());
                tree.putExtra("sortNum", area.getSortNum());
                tree.putExtra("level", area.getLevel());
                tree.putExtra("disabled", area.getDisabled());
            });
        }
    }

    @ApiOperation(value = "查询区域树[带权限-盘点专用]")
    @GetMapping("/list/permission")
    @AutoConvert
    public List<AreaDto> permissionTree() {
        AreaQueryDto queryDto = new AreaQueryDto()
                .setFilterPerm(true);
        return areaFeignClient.areaList(queryDto);
    }

    @ApiOperation(value = "通过Ids查询区域数据")
    @PostMapping("/ids")
    public List<Map<String, ?>> listByIds(@RequestBody List<Long> areaIds) {
        if (CollUtil.isEmpty(areaIds)) {
            return ListUtil.empty();
        }
        return areaFeignClient.listByIds(areaIds).stream().map(item ->
                ImmutableMap.of("id", item.getId(), "name", item.getAreaName(), "code", item.getAreaCode())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "查询区域字典")
    @GetMapping("/dict")
    public List<Map<String, ?>> dict() {
        List<AreaDto> list = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(true));
        return list.stream().map(item ->
                ImmutableMap.of("label", item.getAreaName(), "value", item.getId())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "排序")
    @PutMapping("/sort")
    public Boolean sort(@RequestBody List<Long> areaIds) {
        return areaFeignClient.sort(areaIds);
    }

    @ApiOperation(value = "区域导出")
    @GetMapping("/export")
    public void areaExport(HttpServletResponse response) {
        try {
            List<AreaDto> list = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(true));
            Map<Long, String> idToName = list.stream().collect(Collectors.toMap(AreaDto::getId, AreaDto::getAreaName));
            Map<Long, String> idToCode = list.stream().collect(Collectors.toMap(AreaDto::getId, AreaDto::getAreaCode));
            List<CategoryExportDto> excel = new ArrayList<>();

            for (AreaDto areaDto : list) {
                Long pid = areaDto.getPid();
                CategoryExportDto categoryExportDto = new CategoryExportDto().setCategoryCode(areaDto.getAreaCode())
                        .setCategoryName(areaDto.getAreaName())
                        .setCategoryPidCode(idToCode.get(pid))
                        .setCategoryPidName(idToName.get(pid))
                        .setAreaDesc(areaDto.getAreaDesc())
                        .setOrgId(areaDto.getOrgId())
                        .setAdmins(areaDto.getAdmins());
                excel.add(categoryExportDto);
            }
            dictConvertUtil.convertToDictionary(excel);
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MEANS_AREA));
            LinkedHashMap<String, String> header = new LinkedHashMap<>();
            header.put("orgName", "所属公司");
            header.put("categoryCode", "区域编码");
            header.put("categoryName", "区域名称");
            header.put("adminsText", "管理员");
            header.put("categoryPidCode", "上级区域编码");
            header.put("categoryPidName", "上级区域名称");
            header.put("areaDesc", "区域描述");
            ExcelUtils.export(response, new ExcelExportDto(header, excel), "区域信息-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    @ApiOperation(value = "【PC】导出区域模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        areaService.exportTemplate(response);
    }

    @ApiOperation(value = "【PC】导入区域模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.AREA, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.AREA, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            areaService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error("import area error:", e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return areaService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.AREA, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.AREA, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        areaService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.AREA, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.AREA));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
