package com.niimbot.asset.controller.pc.system;

import com.google.common.collect.Maps;

import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.feign.ReportFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AssetIncrReportDto;
import com.niimbot.means.AssetOrgReportDto;
import com.niimbot.report.AssetReportDto;
import com.niimbot.report.AssetStatusReportDto;
import com.niimbot.system.OrgDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "首页统计数据")
@ResultController
@RequestMapping("api/pc/statistics")
public class HomeStatisticsController {

    private final ReportFeignClient reportFeignClient;

    private final OrgFeignClient orgFeignClient;


    @Autowired
    public HomeStatisticsController(ReportFeignClient reportFeignClient, OrgFeignClient orgFeignClient) {
        this.reportFeignClient = reportFeignClient;
        this.orgFeignClient = orgFeignClient;
    }

    /**
     * 首页-资产状态统计
     *
     * @return 资产状态统计对象
     */
    @ApiOperation(value = "资产状态分布数据")
    @GetMapping("/assetStatus")
    public AssetReportDto<AssetStatusReportDto> assetStatusReport() {
        // 查询资产状态统计数据
        return reportFeignClient.assetStatusReport();
    }

    /**
     * 首页-资产增量统计
     *
     * @param year 年
     * @return 资产增量统计对象
     */
    @ApiOperation(value = "资产增量统计数据")
    @GetMapping("/assetIncr/{year}")
    public AssetIncrReportDto assetIncrReport(@PathVariable("year") Long year) {
        return reportFeignClient.assetIncrReport(year);
    }

    /**
     * 首页-资产使用组织统计
     *
     * @param pid 父id
     * @return 资产使用组织统计对象
     */
    @ApiOperation(value = "资产使用组织统计")
    @GetMapping("/assetUseOrg")
    public Map<String, Object> assetReportByUseOrg(@RequestParam(value = "pid", required = false) Long pid) {
        // 补充公司顶级id
        OrgDto infoByPid = orgFeignClient.getRootOrg();
        // 查询资产使用组织统计数据
        List<AssetOrgReportDto> assetOrgReportDtos = reportFeignClient.assetReportByUseOrg(infoByPid.getId(), pid);

        return calculateTotal(assetOrgReportDtos);
    }

    /**
     * 首页-资产分类统计
     *
     * @param pid 父id
     * @return 资产分类统计对象
     */
    @ApiOperation(value = "资产分类统计")
    @GetMapping("/assetCategory")
    public Map<String, Object> assetReportByCategory(@RequestParam(value = "pid", required = false) Long pid) {
        // 查询资产使用组织统计数据
        List<AssetOrgReportDto> assetCategoryReportDtos = reportFeignClient.assetReportByCategory(pid);
        int assetTotal = 0;
        BigDecimal assetTotalWorth = new BigDecimal("0.0000");
        for (AssetOrgReportDto val : assetCategoryReportDtos) {
            assetTotal += NumberUtil.parseInt(val.getAssetTotal());
            assetTotalWorth = assetTotalWorth.add(new BigDecimal(val.getWorthTotal()));
        }

        // 按总数倒叙
        ListUtil.sort(assetCategoryReportDtos, Comparator.comparing(f -> Convert.toLong(f.getAssetTotal()), Comparator.reverseOrder()));

        // 初始化资产使用组织分布数据
        Map<String, Object> result = Maps.newLinkedHashMap();
        // 资产总量
        result.put("assetTotal", NumberUtil.decimalFormat(",###", Double.parseDouble(String.valueOf(assetTotal))));
        // 资产总价值
        result.put("assetTotalWorth", assetTotalWorth.toString());
        result.put("list", assetCategoryReportDtos);

        return result;
    }


    /**
     * 计算总数和总价值
     *
     * @return 各类统计数据对象
     */
    private Map<String, Object> calculateTotal(List<AssetOrgReportDto> assetReportDtos) {
        int assetTotal = 0;
        BigDecimal assetTotalWorth = new BigDecimal("0.0000");
        for (AssetOrgReportDto val : assetReportDtos) {
            assetTotal += NumberUtil.parseInt(val.getAssetTotal());
            assetTotalWorth = assetTotalWorth.add(new BigDecimal(val.getWorthTotal()));
        }

        // 初始化资产使用组织分布数据
        Map<String, Object> result = Maps.newLinkedHashMap();
        // 资产总量
        result.put("assetTotal", NumberUtil.decimalFormat(",###", Double.parseDouble(String.valueOf(assetTotal))));
        // 资产总价值
        result.put("assetTotalWorth", assetTotalWorth.toString());
        result.put("list", assetReportDtos);

        return result;
    }
}
