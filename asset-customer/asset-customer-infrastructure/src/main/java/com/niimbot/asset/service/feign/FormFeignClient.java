package com.niimbot.asset.service.feign;

import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.dynamicform.FormRelationDto;
import com.niimbot.dynamicform.FormRelationQry;
import com.niimbot.easydesign.form.dto.FormTplAddCmd;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.sdk.FieldValidatorCmd;
import com.niimbot.easydesign.form.dto.sdk.FormSdkVO;
import com.niimbot.easydesign.form.dto.sdk.FormValidatorCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface FormFeignClient {

    String BIZ_TYPE_ASSET = "asset";
    String BIZ_TYPE_MATERIAL = "material";
    String BIZ_TYPE_STANDARD = "standard";

    @GetMapping("/server/form/getByType/{type}")
    FormVO getTplByType(@PathVariable("type") String type);

    @GetMapping("/server/form/listByBizId/{bizId}")
    List<FormSdkVO> listByBizId(@PathVariable("bizId") String bizId);

    @GetMapping("/server/form/getByFormId")
    FormVO getByFormId(@SpringQueryMap FormByIdQry qry);

    @PostMapping("/server/form/saveForm")
    Boolean saveForm(FormTplAddCmd formTplAddCmd);

    @GetMapping("/server/form/getBaseFieldByType/{type}")
    FormBaseFieldCO getBaseFieldByType(@PathVariable("type") String type);

    @PostMapping("/server/form/validatorForm")
    void validatorForm(@RequestBody FormValidatorCmd cmd);

    @PostMapping("/server/form/fieldValidator")
    void fieldValidator(@RequestBody FieldValidatorCmd cmd);

    @PostMapping("/server/form/relationFill")
    List<FormRelationDto> relationFill(@RequestBody FormRelationQry qry);
}
