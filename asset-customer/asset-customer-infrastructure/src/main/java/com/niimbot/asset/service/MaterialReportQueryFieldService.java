package com.niimbot.asset.service;

import com.niimbot.system.QueryConditionDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/29 下午3:00
 */
public interface MaterialReportQueryFieldService {

    /**
     * 查询所有字段
     * @param type
     * @return
     */
    List<QueryConditionDto> allQueryField(String type);

    /**
     * 查询已选字段
     * @param type
     * @return
     */
    List<QueryConditionDto> queryView(String type);
}
