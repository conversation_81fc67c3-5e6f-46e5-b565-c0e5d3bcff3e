package com.niimbot.asset.service.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Since 2024-09-05
 */

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AppActivateFeignClient {

    @GetMapping("server/system/appActivate/configStatus")
    List<String> configStatus();

}
