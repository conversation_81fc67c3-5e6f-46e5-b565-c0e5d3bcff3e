package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.CompanySwitchDto;
import com.niimbot.system.CusAccountBaseDto;
import com.niimbot.system.CusAccountDto;
import com.niimbot.system.CusAccountEnableBatchDto;
import com.niimbot.system.CusAccountEnableDto;
import com.niimbot.system.CusAccountPageDto;
import com.niimbot.system.CusAccountPageQueryDto;
import com.niimbot.system.CusAccountRoleBatchDto;
import com.niimbot.system.CusEmployeeDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import io.swagger.annotations.ApiOperation;

/**
 * 账号管理feign客户端
 *
 * <AUTHOR>
 * @Date 2020/11/18
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CusAccountFeignClient {

    /**
     * 新增账号
     *
     * @param account 账号信息
     * @return 返回账号信息
     */
    @PostMapping(value = "server/system/account")
    String save(CusAccountDto account);

    /**
     * 修改账号
     * - 当前修改密码和角色 since 2020-11-18
     *
     * @param account 账号信息
     * @return true or false
     */
    @PutMapping(value = "server/system/account")
    Boolean edit(CusAccountDto account);

    /**
     * 修改账号
     * - 当前修改密码和角色 since 2020-11-18
     *
     * @param account 账号信息
     * @return 角色信息是否与之前一样
     */
    @PutMapping(value = "server/system/account/role")
    Boolean editRole(CusAccountDto account);

    /**
     * 批量添加角色
     *
     * @param account
     * @return
     */
    @PutMapping(value = "server/system/account/addRoleBatch")
    Boolean addRoleBatch(@RequestBody CusAccountRoleBatchDto account);

    /**
     * 批量删除角色
     *
     * @param account
     * @return
     */
    @PutMapping(value = "server/system/account/removeRoleBatch")
    Boolean removeRoleBatch(@RequestBody CusAccountRoleBatchDto account);


    /**
     * 账号列表查询
     *
     * @param queryDto 分页参数或查询参数
     * @return 分页数据
     */
    @GetMapping(value = "server/system/account/page")
    PageUtils<CusAccountPageDto> selectPage(@SpringQueryMap CusAccountPageQueryDto queryDto);


    /**
     * 通过roleId查询账号数量
     *
     * @param roleId 角色id
     * @return 分页数据
     */
    @GetMapping(value = "server/system/account/amount/{roleId}")
    Integer getAccountAmount(@PathVariable("roleId") Long roleId);

    /**
     * 账号列表
     * @return list
     */
    @ApiOperation(value = "企业员工账号总数")
    @GetMapping(value = "server/system/account/accountTotal/{companyId}")
    Integer accountTotal(@PathVariable("companyId") Long companyId);

    @ApiOperation(value = "查询企业超管")
    @GetMapping(value = "server/system/account/getAdministratorByCompanyId/{companyId}")
    CusEmployeeDto getAdministratorByCompanyId(@PathVariable("companyId") Long companyId);

    @ApiOperation(value = "根据员工ID查询他的系统账号信息")
    @GetMapping(value = "server/system/account/getEmployAccount/{employeeId}")
    Long getEmployAccount(@PathVariable("employeeId") Long employeeId);


    /**
     * 账号关键字查询
     *
     * @param kw 关键字
     * @return 列表数据
     */
    @GetMapping(value = "server/system/account/list/{kw}")
    List<CusAccountBaseDto> selectListKw(@PathVariable("kw") String kw);

    /**
     * 禁用或启用账号
     *
     * @param account true/启用 false/禁用
     * @return 成功或失败
     */
    @PutMapping(value = "server/system/account/status")
    Boolean enableOrDisable(CusAccountEnableDto account, @RequestParam("domain") String domain);

    /**
     * 禁用或启用账号
     *
     * @param account true/启用 false/禁用
     * @return 成功或失败
     */
    @PutMapping(value = "server/system/account/status/batch")
    Boolean enableOrDisableBatch(CusAccountEnableBatchDto account, @RequestParam("domain") String domain);

    /**
     * 账号授权
     *
     * @param account 授权数据
     * @return 成功或失败
     */
    @PostMapping("server/system/account/authorize")
    Boolean authorize(CusAccountDto account);

    /**
     * 获取账号
     *
     * @return 登录账号
     */
    @GetMapping(value = "server/system/account/getAccountNo")
    String getAccountNo();

    /**
     * 修改公司账号配置
     *
     * @param dto dto
     * @return true
     */
    @PostMapping(value = "server/system/account/switchSetting")
    Boolean switchAccountCompanySetting(CompanySwitchDto dto);

}
