package com.niimbot.asset.controller.common.inventory;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.service.feign.InventoryAssetFeignClient;
import com.niimbot.asset.service.feign.InventoryConfigFeignClient;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryAssetCountDto;
import com.niimbot.inventory.InventoryAssetCountQueryDto;
import com.niimbot.inventory.InventoryConfigDto;
import com.niimbot.inventory.InventoryCountDto;
import com.niimbot.inventory.InventorySettingAddListDto;
import com.niimbot.inventory.InventorySettingDto;
import com.niimbot.inventory.InventoryStDto;
import com.niimbot.jf.core.component.annotation.ResultController;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

/**
 * 盘点
 *
 * <AUTHOR>
 * @date 2021/4/14 15:19
 */
@Api(tags = "盘点公共")
@ResultController
@RequestMapping("api/common/inventory")
@RequiredArgsConstructor
public class InventoryCommonController {
    private final InventoryFeignClient inventoryFeignClient;
    private final InventoryAssetFeignClient inventoryAssetFeignClient;
    private final InventoryConfigFeignClient inventoryConfigFeignClient;

    @ApiOperation(value = "盘点头部统计")
    @GetMapping("/count")
    public InventoryCountDto actBacklogTagCount() {
        return inventoryFeignClient.count();
    }

    @ApiOperation(value = "盘点资产头部统计")
    @GetMapping("/asset/count")
    public InventoryAssetCountDto assetCount(InventoryAssetCountQueryDto dto) {
        return inventoryAssetFeignClient.assetCount(dto);
    }

    @ApiOperation(value = "盘点单详细配置数据")
    @GetMapping("/config/{id}")
    @AutoConvert
    public InventoryConfigDto getConfigById(@PathVariable Long id) {
        return inventoryConfigFeignClient.getConfigById(id);
    }

    @ApiOperation(value = "盘点策略")
    @GetMapping("/strategy")
    public List<InventoryStDto> getStrategy() {
        return inventoryConfigFeignClient.getStrategy();
    }

    @ApiOperation(value = "盘点上报/编辑设置查询")
    @GetMapping("/setting/list/{type}")
    public List<InventorySettingDto> list(@ApiParam(name = "type", value = "1-编辑，2-上报")
                                          @PathVariable("type") Integer type) {
        return inventoryConfigFeignClient.settingList(type);
    }


    @ApiOperation(value = "盘点上报/编辑设置保存")
    @PostMapping("/setting")
    @RepeatSubmit
    public Boolean save(@RequestBody InventorySettingAddListDto addListDto) {
        return inventoryConfigFeignClient.save(addListDto);
    }

    @ApiOperation(value = "盘点上报/编辑属性详情")
    @GetMapping("/attrList/{type}")
    public FormVO inventoryAttrList(@ApiParam(name = "type", value = "1-编辑，2-上报")
                                    @PathVariable(value = "type") String type) {
        return inventoryConfigFeignClient.inventoryAttrList(type);
    }

}
