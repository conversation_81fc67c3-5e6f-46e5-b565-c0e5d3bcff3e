package com.niimbot.asset.service.feign;

import com.niimbot.system.DataPermissionAuthorizeDto;
import com.niimbot.system.DataPermissionDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 数据权限
 *
 * <AUTHOR>
 * @date 2022/2/14 16:34
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface DataPermissionFeignClient {
    /**
     * 根据用户Id查询数据权限集
     *
     * @param userId
     * @return
     */
    @GetMapping(value = "server/system/permission/user/{empId}")
    List<DataPermissionDto> queryUserDataPermission(@PathVariable("empId") Long userId);

    /**
     * 查询数据权限集
     *
     * @return
     */
    @GetMapping(value = "server/system/permission")
    List<DataPermissionDto> queryDataPermission();

    /**
     * 数据授权
     *
     * @param authorizeDto
     * @return
     */
    @PostMapping(value = "server/system/permission/user/authorize")
    Boolean authorize(@RequestBody DataPermissionAuthorizeDto authorizeDto);

    /**
     * 用户权限初始化
     *
     */
    @GetMapping(value = "server/system/permission/user/initDataPermission")
    void initDataPermission();

}
