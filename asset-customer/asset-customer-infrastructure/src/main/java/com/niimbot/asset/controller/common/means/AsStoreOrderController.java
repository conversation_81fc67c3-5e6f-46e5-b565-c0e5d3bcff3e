package com.niimbot.asset.controller.common.means;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.PurchaseResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.*;
import com.niimbot.asset.service.OrderService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.*;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import com.niimbot.purchase.AbstractPurchaseProductDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;
import com.niimbot.purchase.PurchaseOrderDetailExtInfoQueryDto;
import com.niimbot.purchase.PurchaseOrderDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.DataPermFilterDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/27 10:20
 */
@Slf4j
@Api(tags = "资产入库管理")
@ResultController
@RequestMapping("api/common/storeOrder")
@RequiredArgsConstructor
public class AsStoreOrderController {
    private final AsStoreOrderFeignClient storeOrderFeignClient;
    private final AsOrderUtil orderUtil;
    private final DictConvertUtil dictConvertUtil;
    private final OrderService orderService;
    private final AsOrderFeignClient orderFeignClient;
    private final CacheResourceUtil cacheResourceUtil;
    private final AsAssetUtil assetUtil;
    protected final AssetFeignClient assetFeignClient;
    private final PurchaseOrderFeignClient purchaseOrderFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;

    @Autowired
    protected FormFeignClient formFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    private final DataAuthorityFeignClient dataAuthorityFeignClient;

    public static final String RK_TYPE = "rkType";
    public static final String PURCHASE_ORDER_NO = "purchaseOrderNo";
    public static final String RK_TYPE_STRAIGHT = "直接入库";
    public static final String RK_TYPE_LINK_PURCHASE = "采购入库";

    @ApiOperation(value = "【资产入库单】设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated AsStoreOrderDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_STORE);
        return storeOrderFeignClient.getWorkflowStepList(dto);
    }

    @ApiOperation(value = "创建入库单")
    @PostMapping
    @RepeatSubmit
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean create(@RequestBody @Validated AsStoreOrderSubmitDto dto) {
        this.beforeCheckAndHandle(dto.getOrderDto());
        AuditableCreateOrderResult result = storeOrderFeignClient.create(dto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.OR_MAS_RK, result));
        return true;
    }

    @ApiOperation(value = "资产入库单详情")
    @GetMapping("/{id}")
    public AsOrderDetailDto getById(@PathVariable("id") Long id) {
        AsStoreOrderDto orderDto = storeOrderFeignClient.getById(id);
        if (ObjectUtil.isNull(orderDto.getId())) {
            return new AsOrderDetailDto()
                    .setOrder(new JSONObject())
                    .setApproveInfo(new WorkflowApproveInfoDto());
        }
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_STORE);
        dictConvertUtil.convertToDictionary(orderDto);

        JSONObject orderJson = orderUtil.toJSONObject(orderDto);
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderJson), SensitiveObjectTypeEnum.ASSET.getCode());
        if (orderDto.getApproveStatus() == 0) {
            return new AsOrderDetailDto()
                    .setOrder(orderJson)
                    .setApproveInfo(new WorkflowApproveInfoDto());
        }
        WorkflowApproveInfoDto approveInfoDto = null;
        if (!DictConstant.NO_APPROVE_PROCESS.equals(orderDto.getApproveStatus())) {
            if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_STORE, id);
                if (approveInfoDto == null || CollUtil.isEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_STORE, id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_STORE, id);
                if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(orderDto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
        }
        return new AsOrderDetailDto()
                .setOrder(orderJson)
                .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
    }

    @ApiOperation(value = "单据批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<AsOrderDetailDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto,
                                                 @RequestParam(value = "assetDetail", required = false) Boolean assetDetail) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_STORE);
        List<AsOrderDetailDto> list = new ArrayList<>();
        for (Long orderId : requestDto.getOrderIds()) {
            AsOrderDetailDto byId = getById(orderId);
            if (BooleanUtil.isTrue(assetDetail)) {
                StoreDetailPageQueryDto queryDto = new StoreDetailPageQueryDto();
                queryDto.setOrderId(orderId);
                queryDto.setPageNum(1);
                queryDto.setPageSize(9999L);
                PageUtils<AsStoreOrderAssetDetailDto> pageAssetDetail = pageAssetDetail(queryDto);
                byId.getOrder().put("assetDetail", pageAssetDetail.getList());
            }
            list.add(byId);
        }
        return list;
    }

    @ApiOperation(value = "资产入库分页")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody AsOrderQueryDto query) {
        PageUtils<AsStoreOrderDto> page = storeOrderFeignClient.page(query);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (AsStoreOrderDto orderDto : page.getList()) {
            orderDto.setOrderType(13);
            list.add(orderUtil.toJSONObject(orderDto));
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "资产入库汇总明细分页查询")
    @GetMapping("/detail/summary/page")
    public PageUtils<JSONObject> pageSummaryDetail(StoreDetailPageQueryDto dto) {
        if (ObjectUtil.isNull(dto.getOrderId())) {
            return new PageUtils<>();
        }
        PageUtils<JSONObject> result = new PageUtils<>();
        PageUtils<AsStoreOrderSummaryDetailDto> summaryPage = storeOrderFeignClient.pageSummaryDetail(dto);
        BeanUtils.copyProperties(summaryPage, result);
        //数据脱敏处理
        if (CollUtil.isNotEmpty(summaryPage.getList())) {
            List<JSONObject> dataList = summaryPage.getList().stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
            desensitizationDataUtil.handleSensitiveField(dataList, SensitiveObjectTypeEnum.ASSET.getCode());
            result.setList(dataList);
        } else {
            result.setList(Collections.emptyList());
        }
        return result;
    }

    @ApiOperation(value = "资产入库汇总明细分页查询", notes = "重新提交数据过滤")
    @GetMapping("/detail/summary/page/reSubmit")
    public PageUtils<JSONObject> reSubmitPageSummaryDetail(StoreDetailPageQueryDto dto) {
        PageUtils<JSONObject> result = new PageUtils<>();
        if (ObjectUtil.isNull(dto.getOrderId())) {
            return result;
        }
        // 查询详情信息
        PageUtils<AsStoreOrderSummaryDetailDto> summaryPage = storeOrderFeignClient.pageSummaryDetail(dto);

        // 查询单据信息，主要为了拿到是否关联采购单
        AsStoreOrderDto orderDto = storeOrderFeignClient.getById(dto.getOrderId());
        if (orderDto == null) {
            return result;
        }

        List<Long> productId = summaryPage.getList().stream().map(AsStoreOrderSummaryDetailDto::getProductId).collect(Collectors.toList());
        String purchaseOrderNo = orderDto.getOrderData().getString(PURCHASE_ORDER_NO);
        if (StrUtil.isNotEmpty(purchaseOrderNo) && CollUtil.isNotEmpty(productId)) {
            // 回填采购数量，待入库数量
            PurchaseOrderDetailExtInfoQueryDto queryDto = new PurchaseOrderDetailExtInfoQueryDto();
            queryDto.setPurchaseOrderNo(purchaseOrderNo);
            queryDto.setProductId(productId);
            queryDto.setProductType(1);
            List<PurchaseOrderDetailDto> extInfo = purchaseOrderFeignClient.listExtInfo(queryDto);
            Map<Long, PurchaseOrderDetailDto> detailMap = extInfo.stream()
                    .collect(Collectors.toMap(AbstractPurchaseProductDto::getProductId, k -> k, (k1, k2) -> k2));
            for (AsStoreOrderSummaryDetailDto summaryDetailDto : summaryPage.getList()) {
                if (detailMap.containsKey(summaryDetailDto.getProductId())) {
                    PurchaseOrderDetailDto detailDto = detailMap.get(summaryDetailDto.getProductId());
                    summaryDetailDto.getAssetSnapshotData()
                            .fluentPut("storedQuantity", detailDto.getStoredQuantity())
                            .fluentPut("storingQuantity", detailDto.getStoringQuantity())
                            .fluentPut("waitStoreQuantity", detailDto.getWaitStoreQuantity())
                            .fluentPut("quantity", detailDto.getQuantity());
                }
            }
        }
        // 有数据则处理
        if (CollUtil.isNotEmpty(summaryPage.getList())) {
            // 处理json数据
            List<JSONObject> assetSnapshotList = new ArrayList<>();
            for (AsStoreOrderSummaryDetailDto asStoreOrderSummaryDetailDto : summaryPage.getList()) {
                assetSnapshotList.add(asStoreOrderSummaryDetailDto.getAssetSnapshotData());
            }
            // 数据过滤
            FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
            Map<String, List<String>> selectValue = new HashMap<>();
            Map<String, List<String>> multiSelectValue = new HashMap<>();
            List<FormFieldCO> filterCode = formVO.getFormFields().stream()
                    .peek(f -> {
                        if (FormFieldCO.SELECT_DROPDOWN.equals(f.getFieldType())) {
                            selectValue.put(f.getFieldCode(), f.getFieldProps().getJSONArray("values").toJavaList(String.class));
                        } else if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(f.getFieldType())) {
                            multiSelectValue.put(f.getFieldCode(), f.getFieldProps().getJSONArray("values").toJavaList(String.class));
                        }
                    })
                    .filter(f -> FormFieldCO.YZC_ASSET_CATE.equals(f.getFieldType())
                            || FormFieldCO.YZC_ORG.equals(f.getFieldType())
                            || FormFieldCO.YZC_EMP.equals(f.getFieldType())
                            || FormFieldCO.YZC_AREA.equals(f.getFieldType())).collect(Collectors.toList());
            assetSnapshotList = dataAuthorityFeignClient.filterNoPermData(new DataPermFilterDto(assetSnapshotList, filterCode));
            for (int i = 0; i < summaryPage.getList().size(); i++) {
                JSONObject jsonObject = assetSnapshotList.get(i);
                // 下拉单选
                selectValue.forEach((key, values) -> {
                    String v = jsonObject.getString(key);
                    if (!values.contains(v)) {
                        jsonObject.put(key, "");
                    }
                });
                // 下拉多选
                multiSelectValue.forEach((key, values) -> {
                    if (jsonObject.containsKey(key)) {
                        List<String> list = jsonObject.getJSONArray(key).toJavaList(String.class);
                        list = list.stream().filter(values::contains).collect(Collectors.toList());
                        jsonObject.put(key, list);
                    }
                });
                summaryPage.getList().get(i).setAssetSnapshotData(jsonObject);
            }
            List<JSONObject> dataList = summaryPage.getList().stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
            // 数据脱敏
            desensitizationDataUtil.handleSensitiveField(dataList, SensitiveObjectTypeEnum.ASSET.getCode());
            result.setList(dataList);
        } else {
            result.setList(Collections.emptyList());
        }
        return result;
    }

    @ApiOperation(value = "资产入库资产明细分页查询")
    @GetMapping("/detail/asset/page")
    @AutoConvert
    public PageUtils<AsStoreOrderAssetDetailDto> pageAssetDetail(StoreDetailPageQueryDto dto) {
        if (ObjectUtil.isNull(dto.getOrderId())) {
            return new PageUtils<>();
        }
        PageUtils<AsStoreOrderAssetDetailDto> page = storeOrderFeignClient.pageAssetDetail(dto);
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        page.getList().forEach(detail -> {
            assetUtil.translateAssetJson(detail.getAssetSnapshotData(), formVO.getFormFields());
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(detail.getAssetSnapshotData()), SensitiveObjectTypeEnum.ASSET.getCode());
        });
        return page;
    }

    @ApiOperation(value = "【PC】导出资产单据卡片")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderCard(@RequestBody @Validated AsOrderQueryDto queryDto) {
        return orderService.exportStoreOrderCard(queryDto);
    }

    @ApiOperation(value = "【PC】导出单据资产")
    @PostMapping(value = "/exportOrder/assets")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderAssets(@RequestBody @Validated AsOrderQueryDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_STORE);
        PageUtils<AsStoreOrderDto> page = storeOrderFeignClient.page(dto);
        List<AsStoreOrderDto> list = page.getList();
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        dictConvertUtil.convertToDictionary(list);
        FormVO orderForm = orderFeignClient.getForm(dto.getOrderType());
        Map<String, String> orderDateFormatType = new HashMap<>();
        Set<String> orderMultiSelectSet = new HashSet<>();
        Map<String, Boolean> orderNumberMap = new HashMap<>();
        for (FormFieldCO it : orderForm.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                orderDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                orderMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                orderNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        orderDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        orderDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        List<Long> orderIds = new ArrayList<>();
        List<JSONObject> orders = new ArrayList<>();
        for (AsStoreOrderDto order : list) {
            orderIds.add(order.getId());
            JSONObject orderJson = orderUtil.toJSONObject(order);
            if (Objects.nonNull(order.getCreateTime())) {
                long timeVal = order.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("createTime", timeVal);
                }
            }
//            if (Objects.nonNull(order.getUpdateTime())) {
//                long timeVal = order.getUpdateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
//                if (timeVal > 0L) {
//                    orderJson.putIfAbsent("updateTime", timeVal);
//                }
//            }
            orderNumberMap.forEach((code, percentage) -> {
                Number number = Convert.toNumber(orderJson.get(code));
                if (ObjectUtil.isNotNull(number)) {
                    if (BooleanUtil.isTrue(percentage)) {
                        orderJson.put(code, number + "%");
                    } else {
                        orderJson.put(code, number);
                    }
                }
            });
            orderDateFormatType.forEach((code, fmt) -> {
                String date = orderJson.getString(code);
                if (StrUtil.isNotEmpty(date)) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                        orderJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换时间异常", code, date);
                    }
                }
            });
            orderMultiSelectSet.forEach(code -> {
                try {
                    JSONArray jsonArray = orderJson.getJSONArray(code);
                    if (CollUtil.isNotEmpty(jsonArray)) {
                        List<String> strings = jsonArray.toJavaList(String.class);
                        String collect = String.join(",", strings);
                        orderJson.put(code, collect);
                    } else {
                        orderJson.put(code, null);
                    }
                } catch (Exception e) {
                    log.warn("[{}] [{}]转换数组异常", code, orderJson.get(code));
                }
            });

            orders.add(orderJson);
        }

        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(orders, SensitiveObjectTypeEnum.ASSET.getCode());

        OrderJsonUtil.convertJsonKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, orders);

        List<AsStoreOrderSummaryDetailDto> details = storeOrderFeignClient.listSummaryDetailsByOrderId(orderIds);
        FormVO assetFormVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        Map<String, String> assetDateFormatType = new HashMap<>();
        Set<String> assetMultiSelectSet = new HashSet<>();
        Map<String, Boolean> assetNumberMap = new HashMap<>();
        for (FormFieldCO it : assetFormVO.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                assetDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                assetMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                assetNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        assetDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        assetDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        Map<Long, List<AsStoreOrderSummaryDetailDto>> assetsMap = details.stream().collect(
                Collectors.groupingBy(AsStoreOrderSummaryDetailDto::getStoreOrderId));
        Map<Long, List<JSONObject>> assetsJsonMap = new HashMap<>();

        Map<String, String> translationCodeMap = assetFormVO.getFormFields().stream()
                .filter(f -> StrUtil.isNotEmpty(f.getTranslationCode()))
                .collect(Collectors.toMap(FormFieldCO::getTranslationCode, FormFieldCO::getFieldCode));

        Map<Long, String> empNameCache = new ConcurrentHashMap<>();
        assetsMap.forEach((key, assetDtoList) -> {
            List<JSONObject> assetJsons = new ArrayList<>();
            for (AsStoreOrderSummaryDetailDto assetDto : assetDtoList) {
                JSONObject assetJson = orderUtil.toJSONObject(assetDto);
                translationCodeMap.forEach((k, v) -> assetJson.put(v, assetJson.get(k)));
                // 特殊处理创建人，不在表单内
                if (assetJson.containsKey(AssetConstant.ASSET_FIELD_CREATE_BY)) {
                    Long createBy = assetJson.getLong(AssetConstant.ASSET_FIELD_CREATE_BY);
                    if (empNameCache.containsKey(createBy)) {
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, empNameCache.get(createBy));
                    } else {
                        String userNameAndCode = cacheResourceUtil.getUserNameAndCode(createBy);
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, userNameAndCode);
                        empNameCache.put(createBy, userNameAndCode);
                    }
                }
//                assetUtil.translateAssetJsonView(assetJson, assetFormVO.getFormFields());

                assetNumberMap.forEach((code, percentage) -> {
                    Number number = Convert.toNumber(assetJson.get(code));
                    if (ObjectUtil.isNotNull(number)) {
                        if (BooleanUtil.isTrue(percentage)) {
                            assetJson.put(code, number + "%");
                        } else {
                            assetJson.put(code, number);
                        }
                    }
                });
                assetDateFormatType.forEach((code, fmt) -> {
                    String date = assetJson.getString(code);
                    if (StrUtil.isNotEmpty(date)) {
                        try {
                            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                            assetJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                        } catch (Exception e) {
                            log.warn("[{}] [{}]转换时间异常", code, date);
                        }
                    }
                });
                assetMultiSelectSet.forEach(code -> {
                    try {
                        JSONArray jsonArray = assetJson.getJSONArray(code);
                        if (CollUtil.isNotEmpty(jsonArray)) {
                            List<String> strings = jsonArray.toJavaList(String.class);
                            String collect = String.join(",", strings);
                            assetJson.put(code, collect);
                        } else {
                            assetJson.put(code, null);
                        }
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换数组异常", code, assetJson.get(code));
                    }
                });

                assetJsons.add(assetJson);
            }
            assetsJsonMap.put(key, assetJsons);
        });
        return orderService.exportOrderAssets(dto, orders, assetsJsonMap);
    }

    private void beforeCheckAndHandle(AsStoreOrderDto orderDto) {
        List<AsStoreOrderAssetDetailDto> assets = new ArrayList<>();
        StringJoiner summary = new StringJoiner("、");

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);

        String rkType = orderDto.getOrderData().getString(RK_TYPE);
        if (RK_TYPE_LINK_PURCHASE.equals(rkType)) {
            // 校验采购单状态
            String purchaseOrderNo = orderDto.getOrderData().getString(PURCHASE_ORDER_NO);
            if (StrUtil.isBlank(purchaseOrderNo)) {
                BusinessExceptionUtil.throwException("关联单号不能为空");
            }
            PurchaseOrderDto purchaseOrderDto = purchaseOrderFeignClient.getByOrderNo(purchaseOrderNo);
            if (!ListUtil.of(DictConstant.NO_APPROVE_PROCESS, DictConstant.APPROVED)
                    .contains(purchaseOrderDto.getApproveStatus())) {
                throw new BusinessException(PurchaseResultCode.PURCHASE_ORDER_STATUS_NOT_SUPPORT_STORE);
            }
            Map<Long, PurchaseOrderDetailDto> purchaseOrderDetailDtoMap =
                    purchaseOrderDto.getProductDetail().stream().collect(
                            Collectors.toMap(PurchaseOrderDetailDto::getProductId, o -> o));

            for (AsStoreOrderSummaryDetailDto summaryDetail : orderDto.getSummaryDetail()) {
                if (ObjectUtil.isNull(summaryDetail.getProductId())) {
                    BusinessExceptionUtil.throwException("产品id不能为空");
                }
                PurchaseOrderDetailDto purchaseOrderDetail = purchaseOrderDetailDtoMap.get(summaryDetail.getProductId());
                // 校验入库数量
                if (new BigDecimal(summaryDetail.getQuantity()).compareTo(purchaseOrderDetail.getWaitStoreQuantity()) > 0) {
                    String tpl = "[%s]入库数量不能大于待入库数量";
                    BusinessExceptionUtil.throwException(String.format(tpl, purchaseOrderDetail.getName()));
                }
                summaryDetail.setMoney(summaryDetail.getStorePrice().multiply(BigDecimal.valueOf(summaryDetail.getQuantity())));
                summary.add(summaryDetail.getAssetSnapshotData().getString("assetName"));
                assetUtil.translateAssetJson(summaryDetail.getAssetSnapshotData(), formVO.getFormFields());
                for (int i = 0; i < summaryDetail.getQuantity(); i++) {
                    AsStoreOrderAssetDetailDto asset = new AsStoreOrderAssetDetailDto();
                    asset.setAssetSnapshotData(summaryDetail.getAssetSnapshotData());
                    if (ObjectUtil.isNull(summaryDetail.getProductId())) {
                        BusinessExceptionUtil.throwException("产品id不能为空");
                    }
                    asset.setStandardId(purchaseOrderDetail.getStandardId());
                    asset.getAssetSnapshotData().put("assetCode", "系统自动生成");
                    //价值字段有值时取价值，没有就取入库单价
                    if (StrUtil.isEmpty(summaryDetail.getAssetSnapshotData().getString("price"))) {
                        asset.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_PRICE,
                                summaryDetail.getStorePrice().setScale(4, RoundingMode.HALF_UP));
                    }
//                    asset.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_ASSET_ORIGIN, DictConstant.ASSET_ORIGIN_BUY_TEXT);
                    asset.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_ORIGIN_VOUCHER, purchaseOrderDto.getOrderNo());
                    asset.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_BUY_TIME, purchaseOrderDto.getOrderData().getOrDefault(PurchaseOrderDto.PURCHASE_DATE, ""));
                    assets.add(asset);
                }
            }
        } else {
            for (AsStoreOrderSummaryDetailDto summaryDetail : orderDto.getSummaryDetail()) {
                summaryDetail.setMoney(summaryDetail.getStorePrice().multiply(BigDecimal.valueOf(summaryDetail.getQuantity())));
                summary.add(summaryDetail.getAssetSnapshotData().getString("assetName"));
                assetUtil.translateAssetJson(summaryDetail.getAssetSnapshotData(), formVO.getFormFields());
                for (int i = 0; i < summaryDetail.getQuantity(); i++) {
                    AsStoreOrderAssetDetailDto asset = new AsStoreOrderAssetDetailDto();
                    asset.setAssetSnapshotData(summaryDetail.getAssetSnapshotData());
                    asset.setStandardId(summaryDetail.getStandardId());
                    asset.getAssetSnapshotData().put("assetCode", "系统自动生成");
                    //价值字段有值时取价值，没有就取入库单价
                    if (StrUtil.isEmpty(summaryDetail.getAssetSnapshotData().getString("price"))) {
                        asset.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_PRICE,
                                summaryDetail.getStorePrice().setScale(4, RoundingMode.HALF_UP));
                    }
                    assets.add(asset);
                }
            }
        }

        final BigDecimal totalMoney = orderDto.getSummaryDetail().stream()
                .map(detailDto -> {
                    if (detailDto.getStorePrice() == null) {
                        return BigDecimal.ZERO;
                    }
                    return detailDto.getStorePrice().multiply(BigDecimal.valueOf(detailDto.getQuantity()));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        final int totalQuantity = orderDto.getSummaryDetail().stream()
                .mapToInt(AsStoreOrderSummaryDetailDto::getQuantity).sum();

        if (totalQuantity > 500) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "入库总数量不能超过500条");
        }

        orderDto.setTotalMoney(totalMoney);
        orderDto.setTotalQuantity(totalQuantity);
        orderDto.setSummary(summary.toString());
        orderDto.setAssetDetail(assets);
    }
}
