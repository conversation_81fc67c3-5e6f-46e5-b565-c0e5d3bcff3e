package com.niimbot.asset.security.authentication;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.model.LoginUser;
import com.niimbot.asset.security.service.SysPermissionService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 扫码登陆鉴权 Provider
 *
 * <AUTHOR>
 * @since 2020/11/2 20:27
 */
@Component
public class QrAuthenticationProvider implements AuthenticationProvider {

    @Resource
    private AccountCenterFeignClient accountCenterFeignClient;

    @Resource
    private SysPermissionService sysPermissionService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // 获取用户Id
        QrAuthenticationToken authenticationToken = (QrAuthenticationToken) authentication;
        String employeeId = (String) authenticationToken.getPrincipal();
        CusUserDto cusUserDto = accountCenterFeignClient.getEmpLoginInfo(Long.parseLong(employeeId));
        if (Objects.isNull(cusUserDto)) {
            throw new BusinessException(SystemResultCode.EMP_NOT_EXISTS);
        }
        UserDetails userDetails = new LoginUser(cusUserDto, sysPermissionService.getMenuPermission(cusUserDto));
        // 此时鉴权成功后，应当重新 new 一个拥有鉴权的 authenticationResult 返回
        QrAuthenticationToken authenticationResult = new QrAuthenticationToken(userDetails, userDetails.getAuthorities());
        authenticationResult.setDetails(authenticationToken.getDetails());
        return authenticationResult;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        // 判断 authentication 是不是 QrAuthenticationProvider 的子类或子接口
        return QrAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
