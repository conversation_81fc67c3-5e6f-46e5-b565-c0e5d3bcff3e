package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;

/**
 * <AUTHOR>
 * @date 2023/7/8 下午3:52
 */
public interface PredefinedReportService {

    /**
     * 资产履历报表
     * @param queryDto
     * @return
     */
    PageUtils<JSONObject> assetLogReport(AssetLogReportQueryDto queryDto);

    /**
     * 待处置资产清单
     * @param queryDto
     * @return
     */
    PageUtils<JSONObject> waitHandleAssetLog(WaitHandleAssetLogQueryDto queryDto, FormVO formVO);

    /**
     * 已处置资产清单
     * @param queryDto
     * @return
     */
    PageUtils<JSONObject> handleAssetLog(HandleAssetLogQueryDto queryDto, FormVO formVO);

    /**
     * 待归还资产清单
     * @param queryDto
     * @return
     */
    PageUtils<JSONObject> waitReturnAssetLog(WaitReturnAssetLogQueryDto queryDto, FormVO formVO);

    /**
     * 维修资产报表
     *
     * @param queryDto
     * @return
     */
    PageUtils<JSONObject> repairAssetLog(RepairAssetLogQueryDto queryDto, FormVO formVO);

    /**
     * 维修资产记录报表
     *
     * @param queryDto
     * @return
     */
    PageUtils<JSONObject> repairAssetRecord(RepairAssetRecordQueryDto queryDto, FormVO formVO);
}
