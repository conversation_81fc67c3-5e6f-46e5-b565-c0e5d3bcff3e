package com.niimbot.asset.controller.common.system;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.core.enums.SaleResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.CompanyResourceFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.MaintainPlanFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.asset.service.feign.RecycleBinFeign;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectPlanFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.equipment.EntSntRange;
import com.niimbot.equipment.ListEntSntRange;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.material.MaterialDto;
import com.niimbot.means.AssetDto;
import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.GetRecycleBins;
import com.niimbot.system.RecycleBin;
import com.niimbot.system.ResRecycle;
import com.niimbot.system.ResRelease;
import com.niimbot.system.ResRestore;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Api(tags = "【回收站】")
@ResultController
@RequestMapping("/api/common/recycleBin")
@RequiredArgsConstructor
public class RecycleBinController {

    private final RecycleBinFeign recycleBinFeign;

    private final AssetFeignClient assetFeignClient;

    private final FormFeignClient formFeignClient;

    private final MaintainPlanFeignClient maintainPlanFeignClient;

    private final MaterialFeignClient materialFeignClient;

    private final CompanyResourceFeignClient companyResourceFeignClient;

    private final AsAssetUtil assetUtil;

    private final AsMaterialUtil materialUtil;

    private final CacheResourceUtil cacheResourceUtil;
    
    private final EquipmentSiteInspectPlanFeignClient planFeignClient;

    @ApiOperation("移至回收站")
    @PostMapping("/recycle")
    public Boolean recycle(@Validated @RequestBody ResRecycle resRecycle) {
        // 1.先删除业务数据
        List<AuditableOperateResult> result = new ArrayList<>();
        if (resRecycle.getResType() == 1) {
            List<MaintainPlanInfoDto> plans = maintainPlanFeignClient.getByAssetIds(resRecycle.getResIds());
            if (CollUtil.isNotEmpty(plans)) {
                throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-资产存在保养计划");
            }
            List<EntSntRange> ranges = planFeignClient.rangeList(new ListEntSntRange().setCompanyId(LoginUserThreadLocal.getCompanyId()).setDataIds(resRecycle.getResIds()));
            if (CollUtil.isNotEmpty(ranges)) {
                throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-设备存在巡检计划");
            }
            result = assetFeignClient.remove(resRecycle.getResIds());
        }
        if (resRecycle.getResType() == 2) {
            result = materialFeignClient.remove(resRecycle.getResIds(), false);
        }
        // 2.放入回收站
        if (CollUtil.isNotEmpty(result)) {
            recycleBinFeign.recycle(resRecycle);
        }
        // 记录操作日志
        Map<String, String> params = Collections.singletonMap(Auditable.Tpl.CONTENT, result.stream().map(AuditableOperateResult::operate).collect(Collectors.joining("、")));
        if (resRecycle.getResType() == 1) {
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.MOVE_MEANS_TO_RECYCLE_BIN, params));
        }
        if (resRecycle.getResType() == 2) {
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.MOVE_MATERIAL_TO_RECYCLE_BIN, params));
        }
        return CollUtil.isNotEmpty(result);
    }

    @ApiOperation("还原")
    @PostMapping("/restore")
    public Boolean restore(@Validated @RequestBody ResRestore resRestore) {
        // 是否会超限制，本地部署不需要
        if (!Edition.isLocal()) {
            CompanyResourceCapacityDto capacity = companyResourceFeignClient.getCapacity();
            if (capacity.getCapacity() < (capacity.getHasUsed() + new HashSet<>(resRestore.getResIds()).size())) {
                throw new BusinessException(SaleResultCode.CAPACITY_ARREARS);
            }
        }
        // 记录操作日志
        Boolean result = recycleBinFeign.restore(resRestore.setCompanyId(LoginUserThreadLocal.getCompanyId()));
        if (Boolean.logicalAnd(result, resRestore.getResType() == 1)) {
            String content = assetFeignClient.getInfoList(resRestore.getResIds()).stream().map(v -> v.getAssetData().getString("assetName") + v.getAssetData().getString("assetCode")).collect(Collectors.joining("、"));
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.RECOVER_MEANS_FROM_RECYCLE_BIN, Collections.singletonMap(Auditable.Tpl.CONTENT, content)));
        }
        if (Boolean.logicalAnd(result, resRestore.getResType() == 2)) {
            String content = materialFeignClient.getInfoList(resRestore.getResIds()).stream().map(v -> v.getMaterialData().getString("materialName") + v.getMaterialData().getString("materialCode")).collect(Collectors.joining("、"));
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.RECOVER_MATERIAL_FROM_RECYCLE_BIN, Collections.singletonMap(Auditable.Tpl.CONTENT, content)));
        }
        return result;
    }

    @ApiOperation("回收站列表-彻底删除")
    @PostMapping("/release")
    public Boolean release(@Validated @RequestBody ResRelease resRelease) {
        if (resRelease.getResType() == 2 && materialFeignClient.hasOrder(resRelease.getResIds())) {
            throw new BusinessException(MaterialResultCode.STOCK_QUERY_PARAMS_ERROR, "存在关联单据，无法彻底删除");
        }
        Boolean result = recycleBinFeign.release(resRelease.setCompanyId(LoginUserThreadLocal.getCompanyId()));
        if (result) {
            AuditLogs.sendRecord(() -> {
                if (resRelease.getResType() == 1) {
                    String content = resRelease.getResIds().stream().map(assetFeignClient::getInRecycleBin).map(v -> v.getAssetData().getString("assetName") + v.getAssetData().getString("assetCode")).collect(Collectors.joining("、"));
                    return AuditLogRecord.create(Auditable.Action.DEL_MEANS, Collections.singletonMap(Auditable.Tpl.CONTENT, content));
                }
                if (resRelease.getResType() == 2) {
                    String content = resRelease.getResIds().stream().map(materialFeignClient::getInRecycleBin).map(v -> v.getMaterialData().getString("materialName") + v.getMaterialData().getString("materialCode")).collect(Collectors.joining("、"));
                    return AuditLogRecord.create(Auditable.Action.DEL_MRL, Collections.singletonMap(Auditable.Tpl.CONTENT, content));
                }
                return new AuditLogRecord();
            });
        }
        return result;
    }

    @ApiOperation("查看原因")
    @GetMapping("/details")
    public RecycleBin details(@Validated GetRecycleBins get) {
        RecycleBin details = recycleBinFeign.details(get.setCompanyId(LoginUserThreadLocal.getCompanyId()));
        if (Objects.nonNull(details)) {
            details.setCreateByText(cacheResourceUtil.getUserNameAndCode(details.getCreateBy()));
        }
        return details;
    }

    @ApiOperation("回收站中-数据详情")
    @GetMapping("/data")
    public Object getData(@Validated @NotNull(message = "资源ID不能为空") @RequestParam("resId") Long resId, @NotNull(message = "资源类型不能为空") @RequestParam("resType") Integer resType) {
        if (resType == 1) {
            AssetDto assetDto = assetFeignClient.getInRecycleBin(resId);
            if (ObjectUtil.isEmpty(assetDto)) {
                return new JSONObject();
            }
            JSONObject translate = assetDto.translate();
            FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
            assetUtil.translateAssetJson(translate, formVO.getFormFields());
            return translate;
        }
        if (resType == 2) {
            MaterialDto bin = materialFeignClient.getInRecycleBin(resId);
            if (ObjectUtil.isEmpty(bin)) {
                return new JSONObject();
            }
            FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
            JSONObject translate = bin.translate();
            materialUtil.translateMaterialJson(translate, material.getFormFields());
            return translate;
        }
        return new JSONObject();
    }

}
