package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.todo.TodoCountDto;
import com.niimbot.todo.TodoPageQueryDto;
import com.niimbot.todo.TodoTimeOutSettingDto;
import com.niimbot.todo.TodoWorkCenterDto;
import com.niimbot.todo.TodoWorkCenterV2Dto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 待办事项
 *
 * <AUTHOR>
 * @date 2021/7/12 10:30
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface TodoFeignClient {

    /**
     * 超时设置查询
     * @return
     */
    @GetMapping("server/todo/timeout")
    TodoTimeOutSettingDto getTimeoutSetting();

    /**
     * 超时设置修改
     * @param timeOutSettingDto
     * @return
     */
    @PutMapping("server/todo/timeout")
    Boolean timeoutSetting(@RequestBody TodoTimeOutSettingDto timeOutSettingDto);

    /**
     * 我的待办事项分页查询
     * @param query
     * @return
     */
    @GetMapping("server/todo/pageMy")
    PageUtils<TodoWorkCenterDto> pageMy(@SpringQueryMap TodoPageQueryDto query);

    /**
     * 待办事项数量
     * @return
     */
    @GetMapping("server/todo/count")
    TodoCountDto getTodoCount(@SpringQueryMap TodoPageQueryDto query);

    /**
     * 工作中心待办
     * @return
     */
    @Deprecated
    @GetMapping("server/todo/workCenter")
    TodoWorkCenterDto workCenter();

    @GetMapping("server/todo/workCenter/v2")
    TodoWorkCenterV2Dto workCenterV2(@RequestParam("limit") Integer limit);
}
