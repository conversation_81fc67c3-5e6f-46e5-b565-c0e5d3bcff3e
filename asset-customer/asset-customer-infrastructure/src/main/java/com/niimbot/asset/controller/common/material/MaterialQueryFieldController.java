package com.niimbot.asset.controller.common.material;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/5 14:03
 */
@Slf4j
@Validated
@Api(tags = "耗材档案字段管理")
@ResultController
@RequestMapping("api/common/queryField/material")
@RequiredArgsConstructor
public class MaterialQueryFieldController {

    private final MaterialFeignClient materialFeignClient;
    private final MaterialQueryFieldService queryFieldService;
    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @ApiOperation(value = "【pc】筛选项配置-保存")
    @RepeatSubmit
    @PostMapping("/query/field")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@RequestBody QueryConditionGeneralDto condition) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(), QueryFieldConstant.TYPE_MATERIAL_QUERY));
    }

    @ApiOperation(value = "【pc】筛选项配置-查询")
    @GetMapping("/query/field")
    public QueryConditionGeneralDto getQueryField() {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_QUERY);
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(QueryConditionGeneralDto.class);
    }

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all")
    public List<QueryConditionDto> materialAllQueryField() {
        return queryFieldService.materialAllQueryField();
    }

    @ApiOperation(value = "高级搜索项配置-所有字段")
    @GetMapping("/query/field/search/all")
    public List<QueryConditionDto> materialSearchAllQueryField() {
        return queryFieldService.materialSearchAllQueryField();
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view")
    public List<QueryConditionDto> materialQueryView() {
        return queryFieldService.materialQueryView();
    }

    @ApiOperation(value = "列表设置-保存")
    @PostMapping("/head/field")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean materialHeadField(@RequestBody @Validated QueryHeadConfigDto config) {
        return queryFieldService.materialHeadField(config);
    }

    @ApiOperation(value = "列表设置-查询")
    @GetMapping("/head/field")
    public QueryHeadConfigDto materialHeadField() {
        return queryFieldService.materialHeadField();
    }

    @ApiOperation(value = "列表设置-所有字段")
    @GetMapping("/head/all")
    public List<QueryConditionDto> materialAllHeadField() {
        return queryFieldService.materialAllHeadField();
    }

    @ApiOperation(value = "列表-渲染")
    @GetMapping("/head/view")
    public List<AssetHeadDto> materialHeadView() {
        return queryFieldService.materialHeadView();
    }

    @ApiOperation(value = "标准品属性-所有字段")
    @GetMapping("/standard/{standardId}")
    public QueryConditionStandardDto standardAllField(@PathVariable("standardId") Long standardId) {
        return queryFieldService.standardAllField(standardId, true, false);
    }

    @ApiOperation(value = "排序字段")
    @GetMapping("/sortField")
    public QueryConditionSortDto sortField() {
        return materialFeignClient.sortField();
    }
}
