package com.niimbot.asset.controller.common.system;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.service.NetalPrintService;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 在线图像库转换控制器
 *
 * <AUTHOR>
 * @since 2023/06/27 14:02
 */
@Api(tags = {"公共接口"})
@ResultController
@RequestMapping("api/common")
@Validated
@Slf4j
public class NetalPrintController {

    private final NetalPrintService netalPrintService;

    public NetalPrintController(NetalPrintService netalPrintService) {
        this.netalPrintService = netalPrintService;
    }

    @ApiOperation(value = "在线图像库转换接口")
    @PostMapping("/netal/print/transform")
    public JSONObject netalPrintTransform(@RequestBody @Validated String dto) {
        return netalPrintService.netalPrintTransform(dto, true);
    }


}
