package com.niimbot.asset.service.feign.finance;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.finance.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/15 下午7:25
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetFinanceSettleFeignClient {

    /**
     * 折旧结账列表
     * @param queryDto
     * @return
     */
    @GetMapping("server/finance/settle/billInfo")
    PageUtils<AssetBillInfoDto> billInfo(@SpringQueryMap AssetBillInfoQueryDto queryDto);

    /**
     * 查询企业结算会计期间信息
     * @return
     */
    @GetMapping("server/finance/settle/settleDateInfo")
    OrgSettleDateInfoDto querySettleDateInfo(@RequestParam(value = "orgId") Long orgId);

    /**
     * 折旧计提
     * @param accrualSubmitDto
     * @return
     */
    @PostMapping("server/finance/settle/accrual")
    AssetAccrualDto depreciationAccrual(@RequestBody AssetAccrualSubmitDto accrualSubmitDto);

    /**
     * 确认折旧计提
     * @param accrualSubmitDto
     * @return
     */
    @PostMapping("server/finance/settle/confirmAccrual")
    Boolean confirmAccrual(@RequestBody AssetAccrualSubmitDto accrualSubmitDto);

    /**
     * 折旧明细分页查询
      * @param queryDto
     * @return
     */
    @PostMapping("server/finance/settle/settleDetail")
    PageUtils<SettleDetailDto> settleDetail(@RequestBody SettleDetailQueryDto queryDto);

    /**
     * 折旧汇总分页查询--所属公司
     * @param queryDto
     * @return
     */
    @PostMapping("server/finance/settle/settleSummary")
    PageUtils<SettleSummaryDto> settleSummary(@RequestBody SettleSummaryQueryDto queryDto);

    /**
     * 折旧汇总分页查询--分摊部门
     * @param queryDto
     * @return
     */
    @PostMapping("server/finance/settle/settleSummaryDept")
    PageUtils<SettleSummaryDeptDto> settleSummaryDept(@RequestBody SettleSummaryQueryDto queryDto);

    /**
     * 查询企业会计期间
     * @param queryDto
     * @return
     */
    @GetMapping("server/finance/settle/queryOrgSettleMonth")
    List<String> queryOrgSettleMonth(@SpringQueryMap SettleMonthQueryDto queryDto);
}
