package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.PrintAssetLogQueryDto;
import com.niimbot.means.UserPrintLogDto;
import com.niimbot.means.UserPrintLogViewDto;
import com.niimbot.system.FullPrintLogDto;
import com.niimbot.system.PrintLogQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 标签模板管理feign客户端
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface UserPrintLogFeignClient {

    /**
     * 资产打印日志列表
     *
     * @param dto dto
     * @return 列表
     */
    @GetMapping("/server/system/print/log/list")
    List<UserPrintLogViewDto> assetLogList(@SpringQueryMap PrintAssetLogQueryDto dto);

    /**
     * 资产打印日志分页
     *
     * @param dto dto
     * @return 分页
     */
    @GetMapping("/server/system/print/log/page")
    PageUtils<UserPrintLogViewDto> assetLogPage(@SpringQueryMap PrintAssetLogQueryDto dto);

    @GetMapping("/server/system/print/log/full/page")
    PageUtils<FullPrintLogDto> fullPage(@SpringQueryMap PrintLogQuery query);

    /**
     * 新增打印日志
     *
     * @param logs 打印数据
     * @return 结果
     */
    @PostMapping("/server/system/print/log")
    Boolean addPrintLog(List<UserPrintLogDto> logs);

    /**
     * 根据打印日志ID查询资产快照数据
     *
     * @param printTaskId 任务id
     * @param assetId     资产id
     * @return json数据
     */
    @GetMapping(value = "/server/system/print/log/{printTaskId}/{assetId}")
    JSONObject getPrintAssetSnapShot(@PathVariable("printTaskId") Long printTaskId,
                                     @PathVariable("assetId") Long assetId);
}
