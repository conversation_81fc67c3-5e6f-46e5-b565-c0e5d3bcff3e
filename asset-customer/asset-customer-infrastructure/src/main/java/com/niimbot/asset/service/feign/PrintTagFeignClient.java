package com.niimbot.asset.service.feign;

import com.niimbot.system.UserPrintTagDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;

/**
 * 打印标签模板管理feign客户端
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PrintTagFeignClient {

    /**
     * 新增标签模板
     *
     * @param userPrintTagDto 标签模板对象
     * @return Boolean
     */
    @PostMapping(value = "server/system/tag/add")
    Boolean add(UserPrintTagDto userPrintTagDto);

    /**
     * 编辑标签模板
     *
     * @param userPrintTagDto 标签模板对象
     * @return Boolean
     */
    @PutMapping(value = "server/system/tag/edit")
    Boolean edit(UserPrintTagDto userPrintTagDto);

    /**
     * 标签模板列表
     *
     * @return List<UserPrintTagDto>
     */
    @GetMapping(value = "server/system/tag/list")
    List<UserPrintTagDto> tagList();

    /**
     * 获取单个标签模板
     *
     * @return UserPrintTagDto
     */
    @GetMapping(value = "server/system/tag/one/{id}")
    UserPrintTagDto getById(@PathVariable("id") Long id);
}
