package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.MaintainFeignClient;
import com.niimbot.asset.service.feign.TagFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.SizeResDto;
import com.niimbot.system.TagAttrListDto;
import com.niimbot.system.UserTagDetailDto;
import com.niimbot.system.UserTagDto;
import com.niimbot.system.UserTagNameDto;
import com.niimbot.system.UserTagResDto;
import com.niimbot.system.UserTagSaveDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

@Api(tags = "标签模板管理")
@ResultController
@RequestMapping("api/pc/tag")
@Validated
@RequiredArgsConstructor
public class TagController {
    private final TagFeignClient tagFeignClient;
    private final AbstractFileUtils fileUtils;
    private final AssetFeignClient assetFeignClient;
    private final MaintainFeignClient maintainFeignClient;

    @ApiOperation(value = "全部数据查询列表")
    @GetMapping("/{sizeId}")
    @AutoConvert
    public UserTagResDto getBySizeId(@PathVariable("sizeId") Long sizeId,
                                     @ApiParam(name = "tagType", value = "模板类型 1-系统默认模板  2-用户自定义模板")
                                     @RequestParam(value = "tagType", required = false) Integer tagType,
                                     @ApiParam(name = "kw", value = "[模板名称]关键词")
                                     @RequestParam(value = "kw", required = false) String kw,
                                     @ApiParam(name = "printerName", value = "打印机名称")
                                     @RequestParam(value = "printerName", required = true, defaultValue = "") String printerName,
                                     @ApiParam(name = "printType", value = "1-资产模板 2-耗材模板 3-区域模板")
                                     @Validated @NotNull(message = "标签模板类型不能为空")
                                     @RequestParam(value = "printType") Short printType) {
        UserTagResDto userTagResDto = tagFeignClient.getBySizeId(printType, sizeId, tagType, kw, printerName);
        List<UserTagDto> groupData = userTagResDto.getGroupData();
        groupData.forEach(val -> {
            // 补全标签路径
            String tagUrl = val.getTagUrl();
            val.setTagUrl(fileUtils.convertToDownloadUrl(tagUrl));
        });
        return userTagResDto;
    }

    @ApiOperation(value = "标签尺寸列表")
    @GetMapping("/sizeList")
    @AutoConvert
    public SizeResDto getSizeList(@RequestParam(value = "printerIds", required = false) List<Long> printerIds,
                                  @RequestParam(value = "printerName", required = false) String printerName,
                                  @Validated @NotNull(message = "标签模板类型不能为空")
                                  @RequestParam(value = "printType") Short printType) {
        if (CollUtil.isEmpty(printerIds) && StrUtil.isBlank(printerName)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        return tagFeignClient.getSizeList(printType, printerIds, printerName);
    }

    @ApiOperation(value = "标签属性列表")
    @GetMapping("/attrList")
    public TagAttrListDto getAttrList(@ApiParam(name = "kw", value = "[属性名称]关键词")
                                        @RequestParam(value = "kw", required = false) String kw,
                                      @ApiParam(name = "printType", value = "标签模板类型：1-资产模板 2-耗材模板")
                                        @Validated @NotNull(message = "标签模板类型不能为空") Short printType) {
        if (DictConstant.PRINT_TYPE_ASSET == printType) {
            return assetFeignClient.getTagAttrList(kw);
        }
        if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
            return maintainFeignClient.getTagAttrList(kw);
        }
        return new TagAttrListDto().setTagAttrs(Collections.emptyList());
    }

    @ApiOperation(value = "保存用户自定义标签模板")
    @RepeatSubmit
    @PostMapping()
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean saveUserTag(@RequestBody @Validated(Insert.class) UserTagSaveDto userTagSaveDto) {
        return tagFeignClient.saveUserTag(userTagSaveDto);
    }

    @ApiOperation(value = "编辑用户自定义标签模板")
    @RepeatSubmit
    @PutMapping()
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editUserTag(@RequestBody @Validated(Update.class) UserTagSaveDto userTagSaveDto) {
        return tagFeignClient.editUserTag(userTagSaveDto);
    }

    @ApiOperation(value = "删除用户自定义标签模板")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean deleteUserTag(@PathVariable("id") Long id) {
        return tagFeignClient.deleteUserTag(id);
    }

    @ApiOperation(value = "获取PDF模版")
    @GetMapping("/getPdfTag}")
    public Map<String, List<UserTagDto>> getPdfTag(@RequestParam("printerId") Long printerId) {
        HashMap<String, List<UserTagDto>> pdfTag = tagFeignClient.getPdfTag(DictConstant.PRINT_TYPE_ASSET, printerId);
        // Pdf标签只有 50*30
        List<UserTagDto> userTagList = pdfTag.get("50*30");
        userTagList.forEach(val -> {
            // 补全标签路径
            String tagUrl = val.getTagUrl();
            val.setTagUrl(fileUtils.convertToDownloadUrl(tagUrl));
        });

        return pdfTag;
    }

    @ApiOperation(value = "修改标签模板名称")
    @RepeatSubmit
    @PutMapping("/updateTagName")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean updateTagName(@RequestBody @Validated UserTagNameDto userTagNameDto) {
        return tagFeignClient.updateTagName(userTagNameDto);
    }

    @ApiOperation(value = "全部去重数据查询列表")
    @GetMapping("/distinct/{sizeId}/{printType}/{printerId}")
    public List<UserTagDto> getDistinctBySizeId(@PathVariable("sizeId") Long sizeId, @PathVariable Short printType, @PathVariable("printerId") Long printerId) {
        List<UserTagDto> userTagDtoList = tagFeignClient.getDistinctBySizeId(printType, sizeId, printerId);
        userTagDtoList.forEach(val -> {
            // 补全标签路径
            val.setTagUrl(fileUtils.convertToDownloadUrl(val.getTagUrl()));
        });
        return userTagDtoList;
    }

    @ApiOperation(value = "标签模板详情")
    @GetMapping("/detail/{id}")
    public UserTagDetailDto getDetail(@PathVariable("id") Long id) {
        UserTagDetailDto detail = tagFeignClient.getDetail(id);
        // 补全标签路径
        detail.setTagUrl(fileUtils.convertToDownloadUrl(detail.getTagUrl()));
        return detail;
    }
}
