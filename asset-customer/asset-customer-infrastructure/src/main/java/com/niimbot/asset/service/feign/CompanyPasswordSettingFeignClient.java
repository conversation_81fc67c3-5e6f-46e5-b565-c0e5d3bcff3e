package com.niimbot.asset.service.feign;

import com.niimbot.system.CompanyPasswordSettingDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CompanyPasswordSettingFeignClient {

    /**
     * 新增企业密码设置
     *
     * @param settingDto
     * @return 是否成功
     */
    @PostMapping(value = "server/system/companyPasswordSetting")
    Boolean saveOrUpdate(CompanyPasswordSettingDto settingDto);

    /**
     * 查看企业密码设置详情
     *
     * @return
     */
    @GetMapping(value = "server/system/companyPasswordSetting/getSettingDetail")
    CompanyPasswordSettingDto getSettingDetail();

    /**
     * 关闭企业密码设置开关
     *
     * @return
     */
    @PostMapping(value = "server/system/companyPasswordSetting/closeSwitch")
    Boolean closeSwitch();

    /**
     * 获取密码限制提醒
     *
     * @return
     */
    @GetMapping(value = "server/system/companyPasswordSetting/getLimitWords")
    String getLimitWords();

    @GetMapping(value = "server/system/companyPasswordSetting/getLimitWordsByMobile")
    String getLimitWordsByMobile(@RequestParam(value = "mobile") String mobile);
}
