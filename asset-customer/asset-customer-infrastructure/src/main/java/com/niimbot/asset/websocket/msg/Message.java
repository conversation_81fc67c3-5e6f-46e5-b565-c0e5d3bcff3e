package com.niimbot.asset.websocket.msg;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * websocket事件
 *
 * <AUTHOR>
 * @date 2021/9/1 10:55
 */
@Getter
@Setter
@ToString
public abstract class Message implements Serializable {
    private static final long serialVersionUID = -3636721540988759410L;

    public static final String TYPE_CONNECT = "Connect";
    public static final String TYPE_HEARTBEAT = "Heartbeat";
    public static final String TYPE_IMPORT_TASK = "ImportTask";
    public static final String TYPE_EXPORT_TASK = "ExportTask";
    public static final String TYPE_IMPORT_IMAGES = "ImportImages";
    public static final String TYPE_SIGNATURE = "Signature";


    private String type;

    public Message() {
    }

    public Message(String type) {
        this.type = type;
    }
}
