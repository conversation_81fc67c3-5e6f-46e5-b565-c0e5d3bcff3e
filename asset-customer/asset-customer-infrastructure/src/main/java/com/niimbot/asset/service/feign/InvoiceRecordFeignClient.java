package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.sale.InvoiceRecordApplyDto;
import com.niimbot.sale.InvoiceRecordDetailDto;
import com.niimbot.sale.InvoiceRecordDto;
import com.niimbot.sale.InvoiceRecordQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/3/21 21:22
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface InvoiceRecordFeignClient {

    /**
     * 开票申请
     *
     * @param applyDto
     * @return
     */
    @PostMapping("server/sale/invoiceRecord")
    Boolean apply(@RequestBody InvoiceRecordApplyDto applyDto);

    /**
     * 开票记录分页查询
     *
     * @param query
     * @return
     */
    @GetMapping("server/sale/invoiceRecord/page")
    PageUtils<InvoiceRecordDto> page(@SpringQueryMap InvoiceRecordQueryDto query);

    /**
     * 开票记录详情查询
     *
     * @param id
     * @return
     */
    @GetMapping("server/sale/invoiceRecord/detail/{id}")
    InvoiceRecordDetailDto detail(@PathVariable("id") Long id);
}
