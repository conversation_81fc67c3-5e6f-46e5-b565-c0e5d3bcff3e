package com.niimbot.asset.controller.common.means;

import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.service.feign.AssetStatusFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AssetStatusDto;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/3/25 14:04
 */
@Api(tags = "资产状态管理")
@ResultController
@RequestMapping("api/common/assetStatus")
public class AssetStatusController {

    private final AssetStatusFeignClient assetStatusFeignClient;

    @Autowired
    public AssetStatusController(AssetStatusFeignClient assetStatusFeignClient) {
        this.assetStatusFeignClient = assetStatusFeignClient;
    }

    @GetMapping("/dict")
    public List<Map<String, ?>> allStatus() {
        List<AssetStatusDto> list = assetStatusFeignClient.allStatus();
        return list.stream().map(item ->
                ImmutableMap.of("label", item.getName(), "value", item.getId())
        ).collect(Collectors.toList());
    }
}
