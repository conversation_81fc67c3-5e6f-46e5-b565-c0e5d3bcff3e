package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.means.ReImportDto;
import com.niimbot.system.ImportMappingDto;

import java.io.InputStream;
import java.util.List;

import cn.hutool.poi.excel.ExcelWriter;

/**
 * <AUTHOR>
 * @since 2021/1/19 13:46
 */
public interface AssetExcelService {

    /**
     * 导出资产模板
     *
     * @return excel
     */
    ExcelWriter buildExcelWriter(Long standardId);

    /**
     * 导入资产模板
     *
     * @param stream     文件流
     * @param fileName   文件名
     * @param companyId  公司Id
     * @param standardId 标准品Id
     */
    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId,
                          Long standardId);

    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId,
                          Long standardId, List<ImportMappingDto> mapping);

    Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);

    List<List<LuckySheetModel>> importError(Long taskId);

    ReImportDto reImport(Long taskId);

    Boolean importErrorDelete(Long taskId);

    List<JSONObject> getExcelData(AssetQueryConditionDto queryDto, List<Long> standardIds);

    List<List<LuckySheetModel>> resolveExcelStream(InputStream stream);

}
