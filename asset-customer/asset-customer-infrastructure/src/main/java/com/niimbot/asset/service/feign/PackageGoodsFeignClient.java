package com.niimbot.asset.service.feign;

import com.niimbot.sale.PackageGoodsPageDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 商城购物
 *
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PackageGoodsFeignClient {

    String BASE_URL = "server/sale/packageGoods";
    @GetMapping(BASE_URL + "/list")
    List<PackageGoodsPageDto> getPackageGoodsList();

    @GetMapping(BASE_URL + "/detail/{id}")
    PackageGoodsPageDto detail(@PathVariable("id") Long id);

}
