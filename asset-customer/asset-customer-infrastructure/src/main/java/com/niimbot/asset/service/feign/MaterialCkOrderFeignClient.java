package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/12 13:50
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialCkOrderFeignClient {

    /**
     * 设置审批流信息获取
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/material/order/ck/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(@RequestBody SensitiveMaterialCkOrderDto dto);

    @PostMapping(value = "server/material/order/ck")
    AuditableCreateOrderResult create(@RequestBody SensitiveMaterialCkOrderSubmitDto submitDto);

    @GetMapping(value = "server/material/order/ck/{id}")
    MaterialCkOrderDto getById(@PathVariable("id") Long id);

    @GetMapping(value = "server/material/order/ck/detail/{orderId}/{materialId}")
    MaterialCkOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);

    @PostMapping(value = "server/material/order/ck/page")
    PageUtils<MaterialCkOrderDto> page(@RequestBody MaterialOrderQueryDto query);

    @GetMapping(value = "server/material/order/ck/pageDetail")
    PageUtils<MaterialCkOrderDetailDto> pageDetail(@SpringQueryMap MaterialOrderDetailQueryDto dto);

    /**
     * 耗材出库单据-用于导出
     *
     * @param query
     * @return 结果
     */
    @PostMapping(value = "server/material/order/ck/listForExport")
    PageUtils<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto query);

    @GetMapping(value = "server/material/order/ck/granting/{orderId}/{materialId}")
    List<GrantingDto> grantingDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);

    @GetMapping(value = "server/material/order/ck/grant/{orderId}/{materialId}")
    List<GrantingDto> grantDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);
}
