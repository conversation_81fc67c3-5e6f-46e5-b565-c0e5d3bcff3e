package com.niimbot.asset.service;

import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.service.RedisService;

/**
 * <AUTHOR>
 * @date 2021/1/15 09:33
 */
public abstract class AbstractLoginService {
    protected final RedisService redisService;

    public AbstractLoginService(RedisService redisService) {
        this.redisService = redisService;
    }


    /**
     * @param account 用户名称
     */
    protected void delLoginCount(String account) {
        redisService.del(BaseConstant.LOGIN_USER_ERROR_COUNT + account);
    }
}
