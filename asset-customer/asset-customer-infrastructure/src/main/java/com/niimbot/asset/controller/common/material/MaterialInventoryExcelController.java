package com.niimbot.asset.controller.common.material;

import com.niimbot.asset.service.MaterialInventoryExcelService;
import com.niimbot.inventory.InventoryReportExportDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;

;

/**
 * <AUTHOR>
 * @date 2024/1/18 16:38
 */
@Slf4j
@Validated
@Api(tags = "耗材盘点")
@ResultController
@RequestMapping("api/common/material/inventory")
@RequiredArgsConstructor
public class MaterialInventoryExcelController {

    private final MaterialInventoryExcelService materialInventoryExcelService;

    @ApiOperation(value = "盘点结果报告")
    @PostMapping("/result/report/export")
    public void resultReportExport(@RequestBody @Valid InventoryReportExportDto dto) {
        materialInventoryExcelService.resultReportExport(dto.getId());
    }

}
