package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.system.AsFileDownDto;
import com.niimbot.system.AsFileDto;
import com.niimbot.system.FileParamDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件客户端
 *
 * <AUTHOR>
 * @Date 2020/11/30
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface FileFeignClient {

    /**
     * 批量上传文件
     *
     * @param files        文件数组
     * @param fileParamDto 文件上传相关控制参数
     * @return 结果
     */
    @PostMapping(value = "server/system/file/upload", consumes = "multipart/form-data")
    List<AsFileDto> uploadFile(@RequestPart(FileUploadConfig.FRONT_MULTI_PARAM_NAME) MultipartFile[] files,
                               @SpringQueryMap FileParamDto fileParamDto);

    /**
     * 文件下载
     *
     * @param id     文件id
     * @param delete 下载后是否删除原文件
     * @return response流信息
     */
    @GetMapping(value = "server/system/file/download/feign")
    AsFileDownDto feignFileDownload(@RequestParam("id") Long id, @RequestParam("delete") Boolean delete);

    /**
     * 文件下载
     *
     * @param ids 文件id
     * @return zip文件流信息
     */
    @PostMapping(value = "server/system/file/download/feign/batch")
    byte[] feignFileDownloadBatch(List<Long> ids);

    /**
     * 根据id获取文件信息
     *
     * @param id id
     * @return 文件信息
     */
    @GetMapping(value = "server/system/file/{id}")
    AsFileDto getById(@PathVariable("id") Long id);

    /**
     * 根据idList获取文件信息列表
     *
     * @param ids id
     * @return 文件信息列表
     */
    @PostMapping(value = "server/system/file/list")
    List<AsFileDownDto> getList(List<Long> ids);
}
