package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.service.NetalPrintService;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2022/10/20 15:08
 */
@Slf4j
@Service
public class NetalPrintServiceImpl implements NetalPrintService {

    @Value("${netal.url:empty}")
    private String netalUrl;

    @Value("${netal.uncompressed-url:empty}")
    private String uncompressedUrl;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public JSONObject netalPrintTransform(String dto, boolean compress) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(dto, headers);
        String url = compress ? netalUrl : uncompressedUrl;
        String body = restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody();
        JSONObject result;
        try {
            result = (JSONObject) JSON.parse(body);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "参数错误或JSON格式转换异常");
        }
        return result;
    }
}
