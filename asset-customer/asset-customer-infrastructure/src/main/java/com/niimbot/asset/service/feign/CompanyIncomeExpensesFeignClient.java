package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.sale.CompanyIncomeExpensesDto;
import com.niimbot.sale.CompanyIncomeExpensesQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @since 2021/12/30 16:06
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CompanyIncomeExpensesFeignClient {


    /**
     * 收支明细
     *
     * @param query
     * @return
     */
    @GetMapping("server/sale/incomeExpenses/page")
    PageUtils<CompanyIncomeExpensesDto> page(@SpringQueryMap CompanyIncomeExpensesQueryDto query);

    /**
     * 企业是否存在收支明细
     *
     * @return
     */
    @GetMapping("server/sale/incomeExpenses/hasIncomeExpenses")
    Boolean hasIncomeExpenses();
}
