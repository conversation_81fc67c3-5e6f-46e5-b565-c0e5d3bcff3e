package com.niimbot.asset.controller.common.equipment;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectTaskFeignClient;
import com.niimbot.equipment.SiteInspectTaskAbnormalDto;
import com.niimbot.equipment.SiteInspectTaskAbnormalQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectDto;
import com.niimbot.equipment.SiteInspectTaskProjectQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectSubmitDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/9 15:31
 */
@Api(tags = "【设备管理】巡检任务")
@Slf4j
@RequiredArgsConstructor
@ResultController
@RequestMapping("api/common/equipment/siteInspect/task")
public class EquipmentSiteInspectTaskProjectController {

    private final EquipmentSiteInspectTaskFeignClient feignClient;

    @ApiOperation(value = "巡检项目——列表")
    @GetMapping("/project/page")
    @AutoConvert
    public PageUtils<SiteInspectTaskProjectDto> projectPage(@Validated SiteInspectTaskProjectQryDto query) {
        return feignClient.projectPage(query);
    }

    @ApiOperation(value = "巡检项目——详情")
    @GetMapping("/project/info")
    @AutoConvert
    public SiteInspectTaskProjectDto projectInfo(@RequestParam("id") Long id) {
        return feignClient.projectInfo(id);
    }

    @ApiOperation(value = "巡检项目——异常项目")
    @GetMapping("/project/abnormal/page")
    @AutoConvert
    public PageUtils<SiteInspectTaskAbnormalDto> projectAbnormalPage(@Validated SiteInspectTaskAbnormalQryDto query) {
        return feignClient.projectAbnormalPage(query);
    }

    @ApiOperation(value = "巡检项目——保存提交")
    @PostMapping("/project/submit")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean projectSubmit(@RequestBody @Validated SiteInspectTaskProjectSubmitDto submitDto) {
        return feignClient.projectSubmit(submitDto);
    }

}
