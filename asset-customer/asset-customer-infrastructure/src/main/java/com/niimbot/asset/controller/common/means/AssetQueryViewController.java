package com.niimbot.asset.controller.common.means;

import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.service.feign.AssetQueryViewFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.AssetQueryViewDto;
import com.niimbot.system.QueryConditionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产查询视图 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@Slf4j
@Api(tags = "资产查询分组")
@ResultController
@RequestMapping("api/common/queryView")
@RequiredArgsConstructor
@Validated
public class AssetQueryViewController {
    private final AssetQueryViewFeignClient assetQueryViewFeignClient;

    @ApiOperation(value = "新增查询分组")
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean save(@RequestBody @Validated({AssetQueryViewDto.Save.class}) AssetQueryViewDto dto) {
        checkQueryCondition(dto);
        return assetQueryViewFeignClient.save(dto);
    }

    @ApiOperation(value = "编辑查询分组")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean update(@RequestBody @Validated({AssetQueryViewDto.Update.class}) AssetQueryViewDto dto) {
        checkQueryCondition(dto);
        return assetQueryViewFeignClient.update(dto);
    }

    @ApiOperation(value = "查询分组是否展示")
    @PutMapping("/{id}/{isShow}")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean isShow(@PathVariable("id") Long id, @PathVariable("isShow") Boolean isShow) {
        return assetQueryViewFeignClient.isShow(id, isShow);
    }

    @ApiOperation(value = "删除查询分组")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("id") Long id) {
        return assetQueryViewFeignClient.delete(id);
    }

    @ApiOperation(value = "查询分组详情")
    @GetMapping("/{id}")
    public AssetQueryViewDto getById(@PathVariable("id") Long id) {
        return assetQueryViewFeignClient.getById(id);
    }

    @ApiOperation(value = "查询分组排序")
    @PutMapping("/sort")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean sort(@RequestBody @NotEmpty(message = "排序列表不能为空") List<Long> ids) {
        return assetQueryViewFeignClient.sort(ids);
    }

    @ApiOperation(value = "查询分组配置列表")
    @GetMapping(value = "/list/config")
    public List<AssetQueryViewDto> configList() {
        return assetQueryViewFeignClient.configList();
    }

    @ApiOperation(value = "查询分组展示列表")
    @GetMapping(value = "/list/tab")
    public List<AssetQueryViewDto> tabList() {
        return assetQueryViewFeignClient.tabList();
    }

    private void checkQueryCondition(AssetQueryViewDto dto) {
        List<QueryConditionDto> conditions = dto.getConditions();
        Map<String, Long> conditionMap = conditions.stream()
                .collect(Collectors.groupingBy(QueryConditionDto::getCode, Collectors.counting()));
        conditionMap.values().forEach(num -> {
            if (num > 1) {
                BusinessExceptionUtil.throwException("每个筛选条件不允许重复添加");
            }
        });
    }
}
