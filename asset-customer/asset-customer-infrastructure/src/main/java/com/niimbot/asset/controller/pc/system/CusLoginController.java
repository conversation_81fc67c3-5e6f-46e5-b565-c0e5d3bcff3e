package com.niimbot.asset.controller.pc.system;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.niimbot.asset.annotation.LoginRecord;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.autoconfig.AssetConfig;
import com.niimbot.asset.framework.constant.AppActivateConstant;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.model.LoginAndBandingBody;
import com.niimbot.asset.model.LoginBody;
import com.niimbot.asset.model.LoginSocial;
import com.niimbot.asset.model.MobileBody;
import com.niimbot.asset.security.enmu.QrTypeEnum;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.asset.service.CusUserService;
import com.niimbot.asset.service.feign.AppActivateFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.DataPermissionFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusUserDetailDto;
import com.niimbot.system.LoginByMobileResult;
import com.niimbot.validate.NationalCodeValidate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统登录模块 <AUTHOR>
 *
 * @since 2020/10/27 9:57
 */
@Api(tags = {"用户登录接口"})
@Slf4j
@ResultController
@RequestMapping("api/pc")
public class CusLoginController {

    @Autowired
    private CusLoginService loginService;

    @Autowired
    private CusUserService userService;

    @Autowired
    private AssetConfig assetConfig;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private DataPermissionFeignClient dataPermissionFeignClient;

    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private CusUserSettingFeignClient userSettingFeignClient;
    @Autowired
    private AppActivateFeignClient appActivateFeignClient;

    /**
     * 账号密码验证码登录
     *
     * @param loginBody 登录信息
     * @return token
     */
    @LoginRecord
    @PostMapping("/login")
    @ApiOperation(value = "账号密码登录", notes = "使用账号密码登录")
    public Map<String, Object> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> map = new HashMap<>();
        String token = loginService.loginByPwd(loginBody.getAccount(),
                loginBody.getPassword(),
                loginBody.getCode(),
                loginBody.getUuid(),
                AssetConstant.TERMINAL_PC,
                null);
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        return map;
    }

    /**
     * 手机短信登录
     *
     * @param mobileBody 登录信息
     * @return token
     */
    @LoginRecord
    @PostMapping("/login/sms")
    @ApiOperation(value = "短信验证码登录", notes = "使用短信验证码登录")
    public LoginByMobileResult loginByPhone(@Validated @RequestBody MobileBody mobileBody) {
        NationalCodeValidate.checkCNMobile(mobileBody.getNationalCode(),mobileBody.getMobile());
        log.info("使用短信验证码登录 : {}", mobileBody);
        if (!smsCodeFeignClient.checkSmsCode(mobileBody.getMobile(), mobileBody.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return loginService.loginByMobile(mobileBody.getMobile(), mobileBody.getSmsCode(), AssetConstant.TERMINAL_PC, null);
    }

    /**
     * 获取生成二维码数据
     *
     * @return token
     */
    @GetMapping("/login/qrCode")
    @ApiOperation(value = "获取生成二维码数据", notes = "获取生成二维码数据")
    public Map<String, Object> getQrCode() {
        Map<String, Object> map = new HashMap<>();
        String uuid = loginService.getQrCode();
        map.put("uuid", QrTypeEnum.LOGIN.getType() + ":" + uuid);
        map.put("expireTime", assetConfig.getQrCodeExpireTime());
        return map;
    }

    /**
     * 检查二维码状态
     *
     * @return 校验结果
     */
    @GetMapping("/login/qrCode/check")
    @ApiOperation(value = "轮询校验是否登录", notes = "轮询校验是否登录")
    public Map<String, Object> checkQrCode(@RequestParam("uuid") String uuid) {
        return loginService.checkQrCode(uuid.replace(QrTypeEnum.LOGIN.getType() + ":", ""));
    }

    @GetMapping("/login/rsa/encrypt")
    @ApiOperation(value = "使用RSA加密密码", notes = "仅测试使用")
    public String rsaEncode(@RequestParam("password") String password) {
        RSA rsa = SecureUtil.rsa(null, assetConfig.getRsaPublicKey());
        byte[] encrypt = rsa.encrypt(password, CharsetUtil.UTF_8, KeyType.PublicKey);
        return Base64.encode(encrypt);
    }

    @GetMapping("/login/rsa/decrypt")
    @ApiOperation(value = "使用RSA解密密码", notes = "仅测试使用")
    public String rsaDecode(@RequestParam("password") String password) {
        return SecurityUtils.decryptPassword(password);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/personDetail")
    @AutoConvert
    @ApiOperation(value = "获取用户信息", notes = "获取用户信息")
    public CusUserDetailDto personDetail() {
        CusUserDetailDto userDetailDto = userService.personDetail();
        if (Edition.isLocal()) {
            dataPermissionFeignClient.initDataPermission();
        }

        //首页广告地址
        userDetailDto.setAdvertiseUrl(userSettingFeignClient.advertiseUrl(2));

        //用户是否需要脱敏资产价值数据权限，返回前端给标记，首页处理需要用到
        Boolean desensitizationAssetPrice = desensitizationDataUtil.desensitizationAssetPrice(Long.valueOf(userDetailDto.getEmpId()));
        userDetailDto.setDesensitizationAssetPrice(desensitizationAssetPrice);
        List<String> activeAppList = appActivateFeignClient.configStatus();
        userDetailDto.setEnableEquipmentManage(activeAppList.contains(AppActivateConstant.EQUIPMENT));
        userDetailDto.setEnableMaterialInventory(activeAppList.contains(AppActivateConstant.MATERIAL_INVENTORY));
        return userDetailDto;
    }

    /**
     * 社交登录
     *
     * @param social 请求参数
     * @return
     */
    @LoginRecord
    @PostMapping("/login/social")
    @ApiOperation(value = "社交登录", notes = "社交登录")
    public Map<String, Object> loginBySocial(@RequestBody @Validated LoginSocial social) {
        Map<String, Object> map = new HashMap<>();
        String token = loginService.loginBySocial(social.getProvider(), social.getAppId(), social.getCode(),
                AssetConstant.TERMINAL_PC, null);
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        return map;
    }

    /**
     * 登录并绑定社交账号
     *
     * @param bandingBody 请求参数
     * @return
     */
    @LoginRecord
    @PostMapping("/login/banding")
    @ApiOperation(value = "登录并绑定社交账号", notes = "登录并绑定社交账号")
    public Map<String, Object> loginAndBanding(@RequestBody @Validated LoginAndBandingBody bandingBody) {
        Map<String, Object> map = new HashMap<>();
        bandingBody.setTerminal(AssetConstant.TERMINAL_PC);
        String token = loginService.loginAndBanding(bandingBody);
        map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
        return map;
    }
}
