package com.niimbot.asset.controller.common.system;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.customer.event.LoginSuccessEvent;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.LoginAndBandingBody;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.AbstractPermissionChangedService;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.CusRegisterFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AccountBasicInfo;
import com.niimbot.system.AccountBind;
import com.niimbot.system.AccountDto;
import com.niimbot.system.AccountEmployeeDto;
import com.niimbot.system.ActivateAccount;
import com.niimbot.system.ActivateAccountResult;
import com.niimbot.system.InviteLink;
import com.niimbot.system.RegisterDto;
import com.niimbot.system.RemoveEmployeeAccount;
import com.niimbot.thirdparty.ThirdPartyUser;
import com.niimbot.thirdparty.ThirdPartyUserAuth;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "【通用的账户中心接口】")
@ResultController
@RequestMapping("/api/common/account/center")
public class AccountCenterCommonController {

    @Resource
    private RedisService redisService;

    @Resource
    private CusLoginService loginService;

    @Resource
    private SmsCodeFeignClient codeFeignClient;

    @Resource
    private CusRegisterFeignClient registerFeignClient;

    @Resource
    private AccountCenterFeignClient accountCenterFeignClient;

    @Resource(name = "kickOffPermissionChangedService")
    private AbstractPermissionChangedService kickOffPermissionChangedService; // 踢人登出

    @ApiOperation("钉钉扫码认证授权后的回调地址 仅测试使用")
    @GetMapping("/dingtalk/auth")
    public void dingTalkAuth(@RequestParam(value = "authCode") String authCode) {
        ThirdPartyUserAuth userAuth = new ThirdPartyUserAuth().setProvider("dingtalk").setCode(authCode);
        ThirdPartyUser thirdPartyUser = accountCenterFeignClient.thirdPartyUserAuth(userAuth);
        System.out.println(thirdPartyUser);
    }

    @ApiOperation("账号对应的企业列表")
    @GetMapping("/companyList/{accountId}")
    public List<AccountEmployeeDto> companyList(@Validated @NotNull(message = "账户ID不能为空") @PathVariable Long accountId) {
        return accountCenterFeignClient.companyList(accountId);
    }

    @ApiOperation("账号切换企业登录")
    @PostMapping("/switchCompany")
    public Map<String, Object> switchCompany(@Validated(value = AccountEmployeeDto.SwitchCompany.class) @RequestBody AccountEmployeeDto dto) {
        // 构建切换后的用户登录信息
        CusUserDto newCusUserDto = accountCenterFeignClient.getCompanyEmpLoginInfo(dto.getAccountId(), dto.getCompanyId());
        if (Objects.isNull(newCusUserDto)) {
            throw new BusinessException(SystemResultCode.SWITCH_COMPANY_ERROR);
        }
        String token = loginService.loginByEmployee(newCusUserDto, dto.getTerminal());
        // 更新最后一次登录的企业ID
        accountCenterFeignClient.updateLastCompany(dto.getAccountId(), dto.getCompanyId());
        return ImmutableMap.of(OAuth2AccessToken.ACCESS_TOKEN, token);
    }

    @ApiOperation("账户信息")
    @GetMapping("/basicInfo")
    public AccountBasicInfo accountBasicInfo(@RequestParam(required = false, value = "accountId") Long accountId, @RequestParam(value = "empId") Long empId) {
        if (Objects.isNull(accountId)) {
            accountId = LoginUserThreadLocal.getAccountId();
        }
        return accountCenterFeignClient.getAccountBasicInfo(accountId, empId);
    }

    @Deprecated
    @ApiOperation("绑定手机号")
    @PostMapping("/bindMobile")
    public Boolean bindMobile(@Validated(AccountBind.BindMobile.class) @RequestBody AccountBind accountBind) {
        NationalCodeValidate.checkCNMobile(accountBind.getNationalCode(), accountBind.getMobile());
        if (codeFeignClient.checkSmsCode(accountBind.getMobile(), accountBind.getMobileCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return accountCenterFeignClient.bindMobile(accountBind.setAccountId(LoginUserThreadLocal.getAccountId()));
    }

    @Deprecated
    @ApiOperation("绑定邮箱号")
    @PostMapping("/bindEmail")
    public Boolean bindEmail(@Validated(AccountBind.BindEmail.class) @RequestBody AccountBind accountBind) {
        if (codeFeignClient.checkSmsCode(accountBind.getEmail(), accountBind.getEmailCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return accountCenterFeignClient.bindEmail(accountBind.setAccountId(LoginUserThreadLocal.getAccountId()));
    }

    @ApiOperation("绑定第三方")
    @PostMapping("/bindThirdParty")
    @ResultMessage("绑定成功")
    public Boolean bindThirdParty(@Validated(AccountBind.BindThirdParty.class) @RequestBody AccountBind accountBind) {
        // 用户扫码认证获取信息
        ThirdPartyUserAuth userAuth = new ThirdPartyUserAuth().setProvider(accountBind.getThirdType()).setCode(accountBind.getCode()).setTerminal(accountBind.getTerminal());
        ThirdPartyUser thirdPartyUser = accountCenterFeignClient.thirdPartyUserAuth(userAuth);
        thirdPartyUser.setAccountId(LoginUserThreadLocal.getAccountId());
        return accountCenterFeignClient.bindThirdParty(thirdPartyUser);
    }

    @ApiOperation("解除账号绑定的手机号或邮箱")
    @PutMapping("/unbind/{way}")
    public Boolean unbindWay(@PathVariable Integer way) {
        return accountCenterFeignClient.unbindWay(way, LoginUserThreadLocal.getAccountId());
    }

    @ApiOperation("解除账号绑定的第三方平台 当前仅支持钉钉与企业微信")
    @PutMapping("/unbindThirdParty/{type}")
    @ResultMessage("解绑成功")
    public Boolean unbindThirdParty(@PathVariable String type) {
        return accountCenterFeignClient.unbindThirdParty(type, LoginUserThreadLocal.getAccountId());
    }

    @ApiOperation("通过手机号激活账号")
    @RepeatSubmit
    @PostMapping("/activateAccountByMobile")
    public ActivateAccountResult activateAccountByMobile(@Validated(ActivateAccount.ByMobile.class) @RequestBody ActivateAccount activateAccount) {
        log.info("通过手机号激活账号 : {}", activateAccount);
        NationalCodeValidate.checkCNMobile(activateAccount.getNationalCode(), activateAccount.getMobile());
        if (!codeFeignClient.checkSmsCode(activateAccount.getMobile(), activateAccount.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        ActivateAccountResult result = accountCenterFeignClient.activateAccountByMobile(activateAccount);
        String token = loginService.loginByEmployee(result.getUserDto(), AssetConstant.TERMINAL_PC);
        result.setUserDto(null);
        return result.setToken(token);
    }

    @ApiOperation("通过第三方H5激活账号")
    @PostMapping("/activateAccountByThirdPartyH5")
    public ActivateAccountResult activateAccountByThirdPartyH5(@Validated(ActivateAccount.ByThirdParty.class) @RequestBody ActivateAccount activateAccount) {
        if ("WECHAT".equals(activateAccount.getThirdType())) {
            // 获取用户在第三方通讯录中的userId
            String userId = accountCenterFeignClient.scanGetUserId(activateAccount.getCompanyId(), activateAccount.getThirdType(), activateAccount.getCode());
            return activateAccountResult(activateAccount, userId, "H5");
        }
        // 获取用户在第三方通讯录中的userId
        String userId = accountCenterFeignClient.h5AppAuth(activateAccount.getCompanyId(), activateAccount.getThirdType(), activateAccount.getCode());
        return activateAccountResult(activateAccount, userId, "H5");
    }

    private ActivateAccountResult activateAccountResult(ActivateAccount activateAccount, String userId, String terminal) {
        activateAccount.setThirdPartyUser(new ThirdPartyUser().setUserId(userId).setType(activateAccount.getThirdType()));
        // 尝试激活账号
        ActivateAccountResult result = accountCenterFeignClient.activateAccountByThirdParty(activateAccount);
        // 如果不需要绑定账号
        if (!result.getNeedBindAccount()) {
            String token = loginService.loginByEmployee(result.getUserDto(), terminal);
            result.setToken(token).setUserDto(null);
            return result;
        }
        return result;
    }

    @ApiOperation("通过第三方扫码激活账号")
    @PostMapping("/activateAccountByThirdParty")
    public ActivateAccountResult activateAccountByThirdParty(@Validated(ActivateAccount.ByThirdParty.class) @RequestBody ActivateAccount activateAccount) {
        // 获取用户在第三方通讯录中的userId
        String userId = accountCenterFeignClient.scanGetUserId(activateAccount.getCompanyId(), activateAccount.getThirdType(), activateAccount.getCode());
        return activateAccountResult(activateAccount, userId, AssetConstant.TERMINAL_PC);
    }

    @ApiOperation("通过第三方激活账号，需要绑定手机时。返回Token")
    @RepeatSubmit
    @PostMapping("/activateAccountByThirdPartyBindMobile")
    public ActivateAccountResult activateAccountByThirdPartyBindMobile(@Validated(ActivateAccount.ThirdPartyBindMobile.class) @RequestBody ActivateAccount activateAccount) {
        log.info("通过第三方激活账号，需要绑定手机时。返回Token : {}", activateAccount);
        // 校验手机验证码
        if (!codeFeignClient.checkSmsCode(activateAccount.getMobile(), activateAccount.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        ActivateAccountResult result = accountCenterFeignClient.activateAccountByThirdPartyBindMobile(activateAccount);
        String token = loginService.loginByEmployee(result.getUserDto(), AssetConstant.TERMINAL_PC);
        result.setToken(token);
        result.setUserDto(null);
        return result;
    }

    private String getLoginInfoByThirdPartyKey(String code, String type) {
        return "loginInfo_By_ThirdParty:" + type + ":" + code;
    }

    private void deleteRedisCache(String key) {
        try {
            redisService.del(key);
        } catch (Exception e) {
            // ignore
        }
    }

    @ApiOperation("社交扫码登录")
    @PostMapping("/loginByThirdParty")
    @RepeatSubmit
    public Map<String, Object> loginByThirdParty(@Validated @RequestBody ThirdPartyUserAuth userAuth) {
        ThirdPartyUser thirdPartyUser = accountCenterFeignClient.thirdPartyUserAuth(userAuth);
        String key = getLoginInfoByThirdPartyKey(userAuth.getCode(), userAuth.getProvider());
        redisService.set(key, thirdPartyUser, TimeUnit.MINUTES.toSeconds(5));
        CusUserDto cusUserDto = accountCenterFeignClient.getLoginInfoByThirdParty(userAuth.getProvider(), thirdPartyUser.getUniqueId());
        String token = loginService.loginByEmployee(cusUserDto, userAuth.getTerminal());
        deleteRedisCache(key);
        EventPublishHandler.publish(new LoginSuccessEvent(cusUserDto).setJpushId(userAuth.getPushId()));
        return ImmutableMap.of(OAuth2AccessToken.ACCESS_TOKEN, token);
    }

    @ApiOperation("绑定钉钉或企业微信并登录")
    @RepeatSubmit
    @PostMapping("/bindThirdPartyAndLogin/{way}")
    public Map<String, Object> bindThirdPartyAndLogin(@Validated @NotBlank(message = "绑定方式不能为空") @PathVariable String way, @RequestBody LoginAndBandingBody body) {
        log.info("绑定钉钉或企业微信并登录");
        // 获取用户第三方唯一标识
        String key = getLoginInfoByThirdPartyKey(body.getCode(), body.getProvider());
        if (!redisService.hasKey(key)) {
            throw new BusinessException(SystemResultCode.AUTH_CODE_EXPIRES);
        }
        ThirdPartyUser thirdPartyUser = (ThirdPartyUser) redisService.get(key);
        // 表示是通过账号、手机号或邮箱绑定，此时如果账号不存在或密码输入错误都无法绑定成功
        CusUserDto cusUserDto = null;
        if ("account".equals(way)) {
            // 检查账户是否存在，不存在时抛出异常
            AccountDto accountByWay = accountCenterFeignClient.getAccountByWay(body.getAccount());
            // 解密前端传输的密码和查到的账户密码匹配
            String decryptPassword = SecurityUtils.decryptPassword(body.getPassword());
            if (!SecurityUtils.matchesPassword(decryptPassword, accountByWay.getPassword())) {
                throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
            }
            // 绑定账号与第三方 绑定成功后 选择员工组成登录信息 如果账号下没有员工报错提示
            thirdPartyUser.setAccountId(accountByWay.getId());
            cusUserDto = accountCenterFeignClient.bindThirdPartyAccount(thirdPartyUser);
        }
        // 表示是通过验证码绑定，此时验证通过后，如果账号不存在需要新建账号后返回Token
        if ("mobile".equals(way)) {
            if (StrUtil.isBlank(body.getMobile()) || StrUtil.isBlank(body.getSmsCode())) {
                throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
            }
            if (!codeFeignClient.checkSmsCode(body.getMobile(), body.getSmsCode())) {
                throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
            }
            log.info("通过验证码绑定 : {}", body.getMobile());
            cusUserDto = accountCenterFeignClient.bindOrCreateAccountThirdPartyByMobile(body.getMobile(), body.getSmsCode(), thirdPartyUser);
        }
        // 没有对应的员工
        if (Objects.isNull(cusUserDto)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
        }
        // 登录生成TOKEN返回
        String token = loginService.loginByEmployee(cusUserDto, body.getTerminal());
        deleteRedisCache(key);
        EventPublishHandler.publish(new LoginSuccessEvent(cusUserDto).setJpushId(body.getPushId()));
        return ImmutableMap.of(OAuth2AccessToken.ACCESS_TOKEN, token);
    }

    @ApiOperation("已有账号时绑定新企业")
    @PostMapping("/accountExistBindCompany")
    @RepeatSubmit
    public Map<String, Object> accountExistBindCompany(@Validated(RegisterDto.BindCompany.class) @RequestBody RegisterDto dto) {
        log.info("已有账号时绑定新企业");
        // 查询账号信息
        AccountDto account = null;
        if ("account".equalsIgnoreCase(dto.getType())) {
            if (StrUtil.isBlank(dto.getAccount())) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "登录账号");
            }
            account = accountCenterFeignClient.getAccountByWay(dto.getAccount());
        }
        if ("thirdParty".equalsIgnoreCase(dto.getType())) {
            if (StrUtil.isBlank(dto.getThirdPartyCode()) || StrUtil.isBlank(dto.getThirdPartyType())) {
                throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "第三方授权登录时临时授权码与三方类型不能为空");
            }
            String key = getLoginInfoByThirdPartyKey(dto.getThirdPartyCode(), dto.getThirdPartyType());
            if (!redisService.hasKey(key)) {
                throw new BusinessException(SystemResultCode.AUTH_CODE_EXPIRES);
            }
            ThirdPartyUser thirdPartyUser = (ThirdPartyUser) redisService.get(getLoginInfoByThirdPartyKey(dto.getThirdPartyCode(), dto.getThirdPartyType()));
            account = accountCenterFeignClient.getAccountByThirdPartyUserUnionId(dto.getThirdPartyType(), thirdPartyUser.getUniqueId());
        }
        if (Objects.isNull(account)) {
            throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        }
        // 设置注册手机号
        dto.setMobile(account.getMobile());
        // 去注册
        String terminal = Optional.ofNullable(dto.getSource()).map(sc -> {
            if (sc == 2) {
                return AssetConstant.TERMINAL_APP;
            } else if (sc == 1) {
                return AssetConstant.TERMINAL_PC;
            } else {
                return null;
            }
        }).orElse("");
        CusUserDto register = registerFeignClient.register(dto);
        CusUserDto byWay = accountCenterFeignClient.getLoginInfoByWay(register.getAccount());
        String token = loginService.loginByEmployee(byWay, terminal);
        // 注册推送
        EventPublishHandler.publish(new LoginSuccessEvent(byWay).setJpushId(dto.getPushId()));
        return ImmutableMap.of(
                "companyName", dto.getCompanyName(),
                "accountNo", register.getAccount(),
                "mobile", dto.getMobile(),
                OAuth2AccessToken.ACCESS_TOKEN, token
        );
    }

    @ApiOperation("完善账户密码与昵称")
    @PutMapping("/updatePwdAndNickname")
    public Boolean updatePwdAndNickname(@Validated(AccountDto.CompletePersonalInformation.class) @RequestBody AccountDto dto) {
        Long accountId = LoginUserThreadLocal.getAccountId();
        // 解密
        String decryptPassword = SecurityUtils.decryptPassword(dto.getPassword());
//        ValidationUtils.checkPassword(SystemResultCode.USER_PASSWORD_ERROR, decryptPassword);
//        String encryptPassword = SecurityUtils.encryptPassword(decryptPassword);
        dto.setPassword(decryptPassword);
        dto.setId(accountId);
        // 修改账户信息
        return accountCenterFeignClient.updatePwdAndNickname(dto);
    }

    @ApiOperation("获取账号激活链接")
    @PostMapping("/sendInviteLink")
    public String sendInviteLink() {
        InviteLink inviteLink = accountCenterFeignClient.sendInviteLink(LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId());
        return String.format(inviteLink.getPcDomain() + MessageConstant.Code.JHZH.getUrl(), inviteLink.getCompanyId(), inviteLink.getCompanyName(), inviteLink.getNickname(), URLUtil.encode(inviteLink.getImage(), StandardCharsets.UTF_8), inviteLink.getWay());
    }

    @ApiOperation("预激活员工账号")
    @PutMapping("/preActivatedAccount/{empId}")
    public Boolean preActivatedAccount(@Validated @NotNull(message = "empId不能为空") @PathVariable("empId") Long empId) {
        return accountCenterFeignClient.preActivatedAccount(LoginUserThreadLocal.getCompanyId(), empId);
    }

    @ApiOperation("批量员工删除账号关联")
    @DeleteMapping("/batchDeleteEmployeeAccount")
    @ResultMessage("删除成功")
    @RepeatSubmit
    public Boolean batchDeleteEmployeeAccount(@Validated @RequestBody List<@Valid RemoveEmployeeAccount> removeEmployeeAccounts) {
        List<Long> empIds = removeEmployeeAccounts.stream().map(RemoveEmployeeAccount::getEmpId).collect(Collectors.toList());
        Boolean flag = accountCenterFeignClient.batchDeleteEmployeeAccount(empIds);
        if (flag) {
            List<CusUserDto> collect = removeEmployeeAccounts.stream().map(removeEmployeeAccount -> {
                CusUserDto dto = new CusUserDto();
                dto.setId(removeEmployeeAccount.getEmpId());
                Long companyId = Objects.isNull(removeEmployeeAccount.getCompanyId()) ? LoginUserThreadLocal.getCompanyId() : removeEmployeeAccount.getCompanyId();
                return dto.setCompanyId(companyId);
            }).collect(Collectors.toList());
            if (!CollUtil.isEmpty(collect)) {
                kickOffPermissionChangedService.permissionChange(collect);
            }
        }
        return flag;
    }

    @ApiOperation("更新账户昵称")
    @PutMapping("/updateNickname")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @RepeatSubmit
    public Boolean updateNickname(@Validated(AccountDto.UpdateNickname.class) @RequestBody AccountDto dto) {
        if (Objects.isNull(dto.getId())) {
            dto.setId(LoginUserThreadLocal.getAccountId());
        }
        return accountCenterFeignClient.updateNickname(dto);
    }

    @ApiOperation("激活账号时获取当前企业配置的应用信息，用于构建授权二维码")
    @GetMapping("/getCompanyConfig/{companyId}/{type}")
    public Object getWeChatAgentInfo(@PathVariable Long companyId, @PathVariable String type) {
        return accountCenterFeignClient.getConfig(companyId, type);
    }
}
