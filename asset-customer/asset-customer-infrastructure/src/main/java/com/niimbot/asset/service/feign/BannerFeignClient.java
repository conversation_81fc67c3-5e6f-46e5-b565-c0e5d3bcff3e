package com.niimbot.asset.service.feign;

import com.niimbot.sale.BannerPageDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 商城购物
 *
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface BannerFeignClient {

    String BASE_URL = "server/sale/banner";
    @GetMapping(BASE_URL + "/list/{localId}")
    List<BannerPageDto> getBannerList(@PathVariable("localId") Long localId);

}
