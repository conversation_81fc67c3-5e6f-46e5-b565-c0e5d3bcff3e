package com.niimbot.asset.controller.common.sale;

import cn.hutool.core.collection.CollUtil;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.InvoiceFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.sale.AsInvoiceInfoDto;
import com.niimbot.system.enums.InvoiceTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 发票管理
 *
 * <AUTHOR>
 * @date 2021/10/20 14:06
 */
@Api(tags = "【服务中心】发票管理")
@ResultController
@RequestMapping("api/common/invoice")
@RequiredArgsConstructor
public class InvoiceController {

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    private final InvoiceFeignClient invoiceFeignClient;

    @ApiOperation(value = "新增发票")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean save(@RequestBody
                        @Validated(value = {AsInvoiceInfoDto.Save.class})
                                AsInvoiceInfoDto invoice) {
        validate(invoice);
        return invoiceFeignClient.save(invoice);
    }

    @ApiOperation(value = "编辑发票")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean update(@RequestBody
                          @Validated(value = {AsInvoiceInfoDto.Update.class})
                                  AsInvoiceInfoDto invoice) {
        validate(invoice);
        return invoiceFeignClient.update(invoice);
    }

    @ApiOperation(value = "删除发票")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("id") Long id) {
        return invoiceFeignClient.delete(id);
    }

    @ApiOperation(value = "发票列表")
    @GetMapping("/list")
    @AutoConvert
    public List<AsInvoiceInfoDto> queryList() {
        return invoiceFeignClient.queryList();
    }

    private void validate(AsInvoiceInfoDto invoice) {
        Set<ConstraintViolation<AsInvoiceInfoDto>> validate = new HashSet<>();
        if (InvoiceTypeEnum.PERSONAL.getCode().equals(invoice.getType())) {
            invoice.setTaxNum(null);
            invoice.setBank(null);
            invoice.setPhone(null);
            invoice.setBankAccount(null);
            invoice.setAddress(null);
        } else if (InvoiceTypeEnum.ORDINARY.getCode().equals(invoice.getType())) {
            validate = VALIDATOR.validate(invoice, AsInvoiceInfoDto.Ordinary.class);
        } else if (InvoiceTypeEnum.VAT.getCode().equals(invoice.getType())) {
            validate = VALIDATOR.validate(invoice, AsInvoiceInfoDto.Vat.class);
        }
        if (CollUtil.isNotEmpty(validate)) {
            validate.forEach(cons -> {
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), cons.getMessage());
            });
        }
    }
}
