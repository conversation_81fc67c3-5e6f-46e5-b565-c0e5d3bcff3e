package com.niimbot.asset.controller.app.system;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.DateUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.feign.CusRegisterFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.util.RequestContextUtils;
import com.niimbot.system.RegisterCompanyDto;
import com.niimbot.system.RegisterUserDto;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.useragent.Platform;
import cn.hutool.http.useragent.UserAgentUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 企业注册控制器
 *
 * <AUTHOR>
 * @Date 2020/10/30
 */
@Api(tags = {"企业App端注册接口"})
@ResultController
@RequestMapping("api/app")
@Slf4j
public class AppRegisterController {

    @Resource
    private CusRegisterFeignClient cusUserFeignClient;

    @Resource
    private RedisService redisService;

    @Value("${asset.password.pattern:^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$}")
    private String passwordPattern;

    /**
     * 验证码错误次数
     */
    public static final String SMS_ERROR = "register_sms_code_error:";

    @Deprecated
    @ApiOperation(value = "注册用户", httpMethod = "POST")
    @RepeatSubmit
    @PostMapping("/register/user")
    public RegisterUserDto registerUser(@ApiParam(name = "用户注册对象", value = "json格式", required = true)
                                        @RequestBody @Validated RegisterUserDto info) {
        // 验证验证码
        String mobile = info.getMobile();
        NationalCodeValidate.checkCNMobile(info.getNationalCode(),mobile);
        String str = Convert.toStr(redisService.get(mobile));
        String redisCode = str == null ? "8888" : str;
        String errorCode = SMS_ERROR + mobile;
        if (!StringUtils.equals(redisCode, info.getSmsCode())) {
            Long incr = redisService.incr(errorCode, 1);
            if (incr > 5L) {
                redisService.expire(errorCode, 180, TimeUnit.SECONDS);
                redisService.del(mobile);
                throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR_OVERTIME);
            }
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }

        // 设置密码
        String password = SecurityUtils.decryptPassword(info.getPassword());
        if (!Pattern.compile(passwordPattern).matcher(password).matches()) {
            throw new BusinessException(SystemResultCode.USER_PASSWORD_ERROR);
        }
        info.setPassword(SecurityUtils.encryptPassword(password));


        // 调用注册中心接口,获取unionId
        long unionId = 9999999999L;
        info.setUnionId(unionId);

        // 判断 注册中心是否成功
        String errorCount = BaseConstant.REGISTER_USER_ERROR_COUNT + info.getMobile();
        // CheckRegisterErrorAccount(errorCount);

        RegisterUserDto dto = cusUserFeignClient.registerCusUser(info);
        redisService.del(errorCount);
        redisService.del(errorCode);
        dto.setPassword("").setSmsCode("").setUsername(info.getUsername());
        return dto;
    }

    private void setSource(RegisterUserDto info) {
        // 获取来源
        Platform platform = UserAgentUtil.parse(RequestContextUtils.getRequest().getHeader("User-Agent"))
                .getPlatform();
        int source = 1;
        if (platform.isAndroid()) {
            source = 2;
        } else if (platform.isIos() || platform.isIPad() || platform.isIPhoneOrIPod()) {
            source = 3;
        }
        info.setSource(source);
    }

    private void checkRegisterErrorCount(String errorCount) {
        // 验证错误次数，同一手机号一天内错误超过10次，操作过于频繁，请明天再试
        Long incr = redisService.incr(errorCount, 1);
        if (incr > 10L) {
            redisService.expire(errorCount, DateUtils.getOffSetOfDay(), TimeUnit.MILLISECONDS);
            throw new BusinessException(SystemResultCode.USER_REGISTER_ERROR_OVERTIME);
        }
    }

    @Deprecated
    @ApiOperation(value = "注册企业")
    @RepeatSubmit
    @PostMapping("/company")
    @ResultMessage(ResultConstant.REGISTER_SUCCESS)
    public Map<String, String> registerCompany(@RequestBody @Validated RegisterCompanyDto companyDto) {
        String account = cusUserFeignClient.registerCompany(companyDto);
        return ImmutableMap.of("account", account);
    }
}
