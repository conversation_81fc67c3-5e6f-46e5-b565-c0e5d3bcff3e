package com.niimbot.asset.service;

import com.niimbot.luckysheet.LuckySheetModel;

import java.io.InputStream;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2021/8/6 9:22
 */
public interface OrgService {

    int MAX_BATCH = 1000;

    void exportTemplate(HttpServletResponse response);

    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId);

    List<List<LuckySheetModel>> importError(Long taskId);

    Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);

    Boolean importErrorDelete(Long taskId);
}
