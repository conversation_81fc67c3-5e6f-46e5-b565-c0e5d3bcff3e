package com.niimbot.asset.service.feign;

import com.niimbot.system.DictDataDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/11/9 10:42
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface DictDataFeignClient {

    /**
     * 根据字典类型查询字典详情
     *
     * @param dictType 字典类型
     * @return 字典数据集合
     */
    @GetMapping(value = "server/system/dictData/type/{dictType}")
    List<DictDataDto> selectDictDataByType(@PathVariable("dictType") String dictType);

}
