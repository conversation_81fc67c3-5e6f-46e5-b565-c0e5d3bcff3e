package com.niimbot.asset.controller.common.material;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialRepositoryService;
import com.niimbot.asset.service.feign.MaterialInventoryFeignClient;
import com.niimbot.asset.service.feign.MaterialRepositoryFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.material.MaterialRepositorySearchDto;
import com.niimbot.system.*;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 耗材仓库管理
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "【耗材】仓库管理")
@ResultController
@RequestMapping("/api/common/material/repository")
@Validated
public class MaterialRepositoryController {

    private final MaterialRepositoryFeignClient feignClient;

    private final MaterialInventoryFeignClient inventoryFeignClient;

    private final OrgFeignClient orgFeignClient;

    private final CacheResourceUtil cacheResourceUtil;

    private final MaterialRepositoryService repositoryService;

    private final RedisService redisService;

    public MaterialRepositoryController(MaterialRepositoryFeignClient feignClient,
                                        MaterialInventoryFeignClient inventoryFeignClient,
                                        OrgFeignClient orgFeignClient,
                                        CacheResourceUtil cacheResourceUtil,
                                        MaterialRepositoryService repositoryService,
                                        RedisService redisService) {
        this.feignClient = feignClient;
        this.inventoryFeignClient = inventoryFeignClient;
        this.orgFeignClient = orgFeignClient;
        this.cacheResourceUtil = cacheResourceUtil;
        this.repositoryService = repositoryService;
        this.redisService = redisService;
    }

    /**
     * 添加仓库
     *
     * @param repository 请求参数
     * @return true or false
     * <AUTHOR>
     */
    @ApiOperation("新增仓库")
    @RepeatSubmit
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    @PostMapping
    @AuditLog(Auditable.Action.ADD_MRL_REP)
    public MaterialRepositoryDto add(@RequestBody @Validated(Insert.class) MaterialRepositoryDto repository) {
        return new MaterialRepositoryDto().setId(feignClient.add(repository));
    }

    /**
     * 更新仓库
     *
     * @param repository 仓库信息
     * @return true or false
     * <AUTHOR>
     */
    @ApiOperation("更新仓库")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @PutMapping
    @AuditLog(Auditable.Action.UPE_MRL_REP)
    public Boolean update(@RequestBody @Validated(Update.class) MaterialRepositoryDto repository) {
        return feignClient.update(repository);
    }

    /**
     * 删除多个仓库
     *
     * @param ids 仓库IDs
     * @return true or false
     */
    @ApiOperation("删除多个仓库")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @DeleteMapping
    public Boolean delete(@RequestBody List<Long> ids) {
        List<AuditableOperateResult> results = feignClient.delete(ids);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MRL_REP, results));
        return CollUtil.isNotEmpty(results);
    }

    /**
     * 仓库详情
     *
     * @param id 仓库ID
     * @return {@link MaterialRepositoryDto}
     * <AUTHOR>
     */
    @ApiOperation("仓库详情")
    @AutoConvert
    @GetMapping(value = "/{id}")
    public MaterialRepositoryDto detail(@PathVariable(value = "id") Long id) {
        return feignClient.detail(id);
    }

    @ApiOperation(value = "通过Ids查询资产分类数据")
    @PostMapping("/ids")
    public List<Map<String, ?>> listByIds(@RequestBody List<Long> repoIds) {
        if (CollUtil.isEmpty(repoIds)) {
            return ListUtil.empty();
        }
        return feignClient.listByIds(repoIds).stream().map(item ->
                ImmutableMap.of("id", item.getId(), "name", item.getName(), "code", item.getCode())
        ).collect(Collectors.toList());
    }

    /**
     * 获取推荐码
     *
     * @return code
     */
    @ApiOperation("获取仓库推荐码")
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return feignClient.recommendCode();
    }

    /**
     * 分页搜索
     *
     * @param dto 查询条件
     * @return {@link PageUtils<MaterialRepositoryDto>}
     * <AUTHOR>
     */
    @ApiOperation("分页搜索仓库信息")
    @GetMapping(value = "/search")
    @AutoConvert
    public PageUtils<MaterialRepositoryDto> search(MaterialRepositorySearchDto dto) {
        PageUtils<MaterialRepositoryDto> search = feignClient.search(dto);
        List<MaterialRepositoryDto> list = search.getList();
        list.forEach(v -> {
            List<Long> admins = v.getAdmins();
            if (CollUtil.isEmpty(admins)) {
                return;
            }
            List<String> strings = admins.stream().map(cacheResourceUtil::getUserNameAndCode).collect(Collectors.toList());
            v.setAdminsText(strings);
        });
        return search;
    }

    @ApiOperation("获取耗材仓库字典结构键值对集合")
    @GetMapping(value = "/company/dict")
    public List<Tree<String>> companyRepositoryTree(@RequestParam(value = "kw", required = false) String kw) {
        // 存放区域的公司树
        List<OrgDto> orgList = orgFeignClient.storePermsList();
        List<MaterialRepositoryDto> repoList = feignClient.listPermission(new MaterialRepositorySearchDto().setManagerOwner(null).setCodeOrName(kw));
        Map<Long, List<MaterialRepositoryDto>> repoMap = repoList.stream().collect(Collectors.groupingBy(MaterialRepositoryDto::getManagerOwner));
        return TreeUtil.build(orgList, "0", (org, tree) -> {
            tree.setId(Convert.toStr(org.getId()));
            tree.setParentId(Convert.toStr(org.getPid()));
            tree.setName(org.getOrgName());
            tree.setWeight(org.getSortNum());
            tree.putExtra("title", org.getOrgName());
            tree.putExtra("value", Convert.toStr(org.getId()));
            tree.putExtra("code", org.getOrgCode());
            tree.putExtra("level", org.getLevel());
            tree.putExtra("orgType", org.getOrgType());
            tree.putExtra("pidType", org.getPidType());
            tree.putExtra("disabled", true);
            tree.putExtra("repository", repoMap.containsKey(org.getId()) ? new ArrayList<>(repoMap.get(org.getId())) : ListUtil.empty());
        });
    }

    /**
     * 获取仓库字典键值对集合
     *
     * @return 耗材仓库分类字典键值对
     * <AUTHOR>
     */
    @ApiOperation("获取耗材仓库字典结构键值对集合")
    @GetMapping(value = "/dict")
    public List<Map<String, ?>> dict(@RequestParam(value = "orgId", required = false) Long orgId,
                                     @RequestParam(value = "kw", required = false) String kw) {
        // 获取数据权限
        List<MaterialRepositoryDto> all = feignClient.listPermission(new MaterialRepositorySearchDto().setManagerOwner(orgId).setCodeOrName(kw));
        return all.stream().map((Function<MaterialRepositoryDto, ImmutableMap<String, ?>>) materialRepositoryDto ->
                ImmutableMap.of("label", materialRepositoryDto.getName(),
                        "value", materialRepositoryDto.getId(),
                        "code", materialRepositoryDto.getCode(),
                        "doInventory", false,
                        "disabled", false)
        ).collect(Collectors.toList());
    }

    @ApiOperation("排除进行中盘点单的仓库列表")
    @GetMapping(value = "/dict/withOutInventory")
    public List<Map<String, ?>> dictWithOutInventory(
            @RequestParam(value = "orgId", required = false) Long orgId,
            @RequestParam(value = "kw", required = false) String kw) {
        // 获取数据权限
        List<MaterialRepositoryDto> all = feignClient.listPermission(new MaterialRepositorySearchDto().setManagerOwner(orgId).setCodeOrName(kw));
        List<Long> progressRepoList = inventoryFeignClient.progressRepoList();
        return all.stream().map((Function<MaterialRepositoryDto, ImmutableMap<String, ?>>) materialRepositoryDto ->
                ImmutableMap.of("label", materialRepositoryDto.getName(),
                        "value", materialRepositoryDto.getId(),
                        "code", materialRepositoryDto.getCode(),
                        "doInventory", progressRepoList.contains(materialRepositoryDto.getId()),
                        "disabled", progressRepoList.contains(materialRepositoryDto.getId()))
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "获取耗材仓库字典结构键值对集合", notes = "上个接口带权限，这里新增没权限接口")
    @GetMapping(value = "/dict/noPerms")
    public List<Map<String, ?>> dictNoPerms(@RequestParam(value = "orgId", required = false) Long orgId,
                                     @RequestParam(value = "kw", required = false) String kw) {
        // 获取数据权限
        List<MaterialRepositoryDto> all = feignClient.listPermission(new MaterialRepositorySearchDto()
                .setManagerOwner(orgId)
                .setCodeOrName(kw)
                .setFilterPerm(false));
        return all.stream().map((Function<MaterialRepositoryDto, ImmutableMap<String, ?>>) materialRepositoryDto ->
                ImmutableMap.of("label", materialRepositoryDto.getName(),
                        "value", materialRepositoryDto.getId(),
                        "code", materialRepositoryDto.getCode(),
                        "doInventory", false,
                        "disabled", false)
        ).collect(Collectors.toList());
    }

    @ApiOperation("排除进行中盘点单的仓库列表")
    @GetMapping(value = "/dict/noPerms/withOutInventory")
    public List<Map<String, ?>> dictNoPermsWithOutInventory(
            @RequestParam(value = "orgId", required = false) Long orgId,
            @RequestParam(value = "kw", required = false) String kw) {
        // 获取数据权限
        List<MaterialRepositoryDto> all = feignClient.listPermission(new MaterialRepositorySearchDto()
                .setManagerOwner(orgId)
                .setCodeOrName(kw)
                .setFilterPerm(false));
        List<Long> progressRepoList = inventoryFeignClient.progressRepoList();
        return all.stream().map((Function<MaterialRepositoryDto, ImmutableMap<String, ?>>) materialRepositoryDto ->
                ImmutableMap.of("label", materialRepositoryDto.getName(),
                        "value", materialRepositoryDto.getId(),
                        "code", materialRepositoryDto.getCode(),
                        "doInventory", progressRepoList.contains(materialRepositoryDto.getId()),
                        "disabled", progressRepoList.contains(materialRepositoryDto.getId()))
        ).collect(Collectors.toList());
    }

    /**
     * 分页导出Excel
     *
     * <AUTHOR>
     */
    @ApiOperation("导出仓库Excel")
    @PostMapping(value = "/excel")
    public void excel(
            @RequestBody MaterialRepositorySearchDto dto, HttpServletResponse response) {
        List<MaterialRepositoryDto> all = feignClient.excel(dto);
        all.forEach(v -> {
            List<Long> admins = v.getAdmins();
            if (CollUtil.isEmpty(admins)) {
                return;
            }
            String text = admins.stream().map(cacheResourceUtil::getUserName).collect(Collectors.joining(","));
            v.setAdminText(text);
        });
        // excel名称 与 格式
        String excelBaseName = "仓库清单";
        String excelSuffix = ".xlsx";
        // excel头
        LinkedHashMap<String, String> headers = new LinkedHashMap<>(3);
        headers.put("managerOwnerText", "所属公司");
        headers.put("code", "仓库编码");
        headers.put("name", "仓库名称");
        headers.put("adminText", "仓库管理员");
        headers.put("remark", "仓库备注");
        // 审计日志
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MRL_REP));
        // excel
        ExcelUtils.export(response, new ExcelExportDto(headers, all), excelBaseName + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + excelSuffix);
    }


    @ApiOperation(value = "【PC】导出耗材仓库模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        repositoryService.exportTemplate(response);
    }

    @ApiOperation(value = "【PC】导入耗材仓库模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            repositoryService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return repositoryService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        repositoryService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_REPOSITORY, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.MATERIAL_REPOSITORY));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation("仓库信息列表")
    @GetMapping(value = "/list/{managerOwner}")
    @AutoConvert
    public List<MaterialRepositoryDto> list(@PathVariable("managerOwner") Long managerOwner) {
        return feignClient.listPermission(new MaterialRepositorySearchDto().setFilterPerm(false).setManagerOwner(managerOwner));
    }

}
