package com.niimbot.asset.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.niimbot.luckysheet.LuckySheetModel;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/12/15 16:44
 */
public interface EmployeeService {


    int MAX_BATCH = 1000;

    ExcelWriter buildExportExcelTplWriter(HttpServletResponse response);

    /**
     * 导出资产模板
     *
     * @return excel
     */
    void exportTemplate(HttpServletResponse response);

    /**
     * 导入员工模板
     *
     * @param stream    文件流
     * @param fileName  文件名
     * @param companyId 公司Id
     */
    void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId);

    List<List<LuckySheetModel>> importError(Long taskId);

    Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId);

    Boolean importErrorDelete(Long taskId);


}
