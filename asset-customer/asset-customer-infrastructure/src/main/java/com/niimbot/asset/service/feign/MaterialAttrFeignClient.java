package com.niimbot.asset.service.feign;

import com.niimbot.dynamicform.BizFormMaterialConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/12/9 17:06
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialAttrFeignClient {

    /**
     * 耗材录入查询的耗材固定属性
     *
     * @param isShow 是否显示
     * @return 耗材属性列表
     */
    @GetMapping
    @Deprecated
    List<BizFormMaterialConfig> listMaterialAttr(@RequestParam(value = "isShow", required = false) Boolean isShow);

}
