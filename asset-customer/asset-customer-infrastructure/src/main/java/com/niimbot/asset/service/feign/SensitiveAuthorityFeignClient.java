package com.niimbot.asset.service.feign;

import com.niimbot.system.SensitiveConfigDto;
import com.niimbot.system.SensitivePermissionDto;
import com.niimbot.system.UserSensitivePermissionDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24 上午10:58
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface SensitiveAuthorityFeignClient {

    @GetMapping("server/system/sensitive/config")
    List<SensitiveConfigDto> sensitiveConfig();

    @GetMapping("server/system/sensitive/roleAuthority")
    List<SensitivePermissionDto> roleAuthority(@RequestParam("roleId") Long roleId);

    @GetMapping("server/system/sensitive/userAuthority")
    List<SensitivePermissionDto> userAuthority(@RequestParam("userId") Long userId);

    @GetMapping("server/system/sensitive/userSensitivePermission")
    List<UserSensitivePermissionDto> userSensitivePermission(@RequestParam("userId") Long userId);
}
