package com.niimbot.asset.controller.app.system;

import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AppActivateConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.AbstractPermissionChangedService;
import com.niimbot.asset.service.feign.*;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AsConfigDto;
import com.niimbot.system.UserCenterAPPDto;
import com.niimbot.validate.NationalCodeValidate;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.*;
import java.util.stream.Collectors;

/**
 * App用户中心
 *
 * <AUTHOR>
 * @Date 2020/11/25
 */
@Api(tags = {"App用户中心接口"})
@ResultController
@RequestMapping("api/app/userCenter")
@Slf4j
@Validated
public class UserCenterAPPController {

    @Resource
    private CusUserFeignClient userFeignClient;

    @Resource
    private CusEmployeeFeignClient employeeFeignClient;

    @Resource
    private AsConfigFeignClient configFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private PrintFeignClient printFeignClient;

    @Resource(name = "kickOffPermissionChangedService")
    private AbstractPermissionChangedService kickOffPermissionChangedService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;
    @Autowired
    private CusUserSettingFeignClient userSettingFeignClient;
    @Autowired
    private AppActivateFeignClient appActivateFeignClient;

    @ApiOperation(value = "用户中心")
    @GetMapping("/center")
    public UserCenterAPPDto center() {
        UserCenterAPPDto result = userFeignClient.getUserCenterAPPInfo();

        //个人中心广告地址
        result.setAdvertiseUrl(userSettingFeignClient.advertiseUrl(1));

        //用户是否需要脱敏资产价值数据权限，返回前端给标记，首页处理需要用到
        Boolean desensitizationAssetPrice = desensitizationDataUtil.desensitizationAssetPrice(result.getEmpId());
        result.setDesensitizationAssetPrice(desensitizationAssetPrice);
        List<String> activeAppList = appActivateFeignClient.configStatus();
        result.setEnableEquipmentManage(activeAppList.contains(AppActivateConstant.EQUIPMENT));
        result.setEnableMaterialInventory(activeAppList.contains(AppActivateConstant.MATERIAL_INVENTORY));
        return result;
    }


    @ApiOperation(value = "用户中心【打印任务条数】")
    @GetMapping("/center/printTaskCount")
    public Integer printTaskCount() {
        Integer assetPrintCount = printFeignClient.countAppTask(DictConstant.PRINT_TYPE_ASSET);
        Integer materialPrintCount = printFeignClient.countAppTask(DictConstant.PRINT_TYPE_MATERIAL);
        if (Objects.isNull(assetPrintCount)) {
            return Objects.isNull(materialPrintCount) ? 0 : materialPrintCount;
        }
        if (Objects.isNull(materialPrintCount)) {
            return assetPrintCount;
        }
        return assetPrintCount + materialPrintCount;
    }

    @ApiOperation(value = "修改姓名")
    @PutMapping("/changeName")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeName(@RequestBody @Validated(UserCenterAPPDto.ChangeUserName.class) UserCenterAPPDto dto) {
        return employeeFeignClient.changeName(dto.getUsername());
    }

    @ApiOperation(value = "修改密码")
    @PutMapping("/changePassword")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changePassword(@RequestBody @Validated(Update.class) UserCenterAPPDto dto,
                                  @ApiIgnore LoginUserDto loginUserDto) {
        String newPassword = SecurityUtils.decryptPassword(dto.getNewPassword());
        String oldPassword = SecurityUtils.decryptPassword(dto.getOldPassword());
//        ValidationUtils.checkPassword(SystemResultCode.USER_PASSWORD_ERROR, oldPassword, newPassword);
        String rawPassword = userFeignClient.checkPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, rawPassword)) {
            throw new BusinessException(SystemResultCode.USER_OLD_PASSWORD_ERROR);
        }
        userFeignClient.changeCurrentUserPassword(newPassword);
        CusUserDto cusUser = loginUserDto.getCusUser();
        kickOffPermissionChangedService.permissionChange(Collections.singletonList(cusUser));
        return true;
    }

    @ApiOperation(value = "校验验证码")
    @GetMapping("/checkSmsCode")
    public Boolean checkSmsCode(@NotBlank(message = "请输入手机号")
                                @Size(max = 11, message = "手机号最多11位") String mobile,
                                @NotBlank(message = "请输入区号") String nationalCode,
                                @Size(min = 4, max = 4, message = "验证码为4位") String smsCode) {
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        if (!smsCodeFeignClient.checkSmsCode(mobile, smsCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return true;
    }

    @ApiOperation(value = "绑定手机号")
    @GetMapping("/bindMobile")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean bindMobile(@NotBlank(message = "请输入手机号")
                              @Size(max = 11, message = "手机号最多11位") String mobile,
                              @NotBlank(message = "请输入区号") String nationalCode,
                              @Size(min = 4, max = 4, message = "验证码为4位") String smsCode) {
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        checkSmsCode(mobile,nationalCode, smsCode);
        return userFeignClient.bindMobile(mobile);
    }

    @ApiOperation(value = "更换手机号")
    @GetMapping("/changeMobile")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeMobile(@NotBlank(message = "请输入手机号")
                                @Size(max = 11, message = "手机号最多11位") String mobile,
                                @NotBlank(message = "请输入区号") String nationalCode,
                                @Size(min = 4, max = 4, message = "验证码为4位") String smsCode) {
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        checkSmsCode(mobile, nationalCode,smsCode);
        return userFeignClient.changeMobile(mobile);
    }


    @ApiOperation(value = "意见反馈")
    @RepeatSubmit
    @PostMapping("/feedBack")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean feedBack(@RequestBody @Validated(Insert.class) UserCenterAPPDto dto) {
        return userFeignClient.feedBack(dto);
    }

    @ApiOperation(value = "联系我们")
    @GetMapping("/about")
    public Map<String, String> feedBack() {
        List<AsConfigDto> config = configFeignClient.listByType(1);
        return config.stream().collect(Collectors.toMap(AsConfigDto::getConfigKey, AsConfigDto::getConfigValue, (k1, k2) -> k1));
    }

    @ApiOperation(value = "推荐邀请URL")
    @GetMapping("/recommendUrl")
    public Map<String, String> recommendUrl() {
        List<AsConfigDto> config = configFeignClient.listByType(2);
        Optional<AsConfigDto> firstConfig = config.stream().filter(c -> "recommend_url".equals(c.getConfigKey())).findFirst();
        String url = "";
        if (firstConfig.isPresent()) {
            AsConfigDto asConfigDto = firstConfig.get();
            url = asConfigDto.getConfigValue();
        }
        return ImmutableMap.of("url", url);
    }
}
