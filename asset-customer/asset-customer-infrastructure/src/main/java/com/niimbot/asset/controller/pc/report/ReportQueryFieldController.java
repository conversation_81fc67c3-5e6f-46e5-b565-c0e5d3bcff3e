package com.niimbot.asset.controller.pc.report;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.ReportQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.report.PredefinedReportFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.report.ReportEnum;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionGeneralDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/27 10:55
 */
@Slf4j
@Validated
@Api(tags = "报表字段管理")
@ResultController
@RequestMapping("api/pc/queryField/report")
@RequiredArgsConstructor
public class ReportQueryFieldController {

    private final ReportQueryFieldService queryFieldService;
    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;
    private final PredefinedReportFeignClient predefinedReportFeignClient;

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all/{type}")
    public List<QueryConditionDto> reportAllQueryField(@PathVariable("type") String type) {
        return queryFieldService.reportAllQueryField(type);
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view/{type}")
    public List<QueryConditionDto> reportQueryView(@PathVariable("type") String type) {
        return queryFieldService.reportQueryView(type);
    }

    @ApiOperation(value = "筛选项配置-保存")
    @PostMapping("/query/field/{type}")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@PathVariable("type") String type,
                              @RequestBody QueryConditionGeneralDto condition) {
        ReportEnum reportEnum = ReportEnum.getByType(type);
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(), reportEnum.getQuery()));
    }

    @ApiOperation(value = "筛选项配置-查询")
    @GetMapping("/query/field/{type}")
    public QueryConditionGeneralDto getQueryField(@PathVariable("type") String type) {
        ReportEnum reportEnum = ReportEnum.getByType(type);
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(reportEnum.getQuery());
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(QueryConditionGeneralDto.class);
    }

    @ApiOperation(value = "列表设置-所有字段")
    @GetMapping("/head/all/{type}")
    public List<QueryConditionDto> reportAllHeadField(@PathVariable("type") String type) {
        return queryFieldService.reportAllHeadField(type);
    }

    @ApiOperation(value = "列表-渲染")
    @GetMapping("/head/view/{type}")
    public List<AssetHeadDto> reportHeadView(@PathVariable("type") String type) {
        return queryFieldService.reportHeadView(type);
    }

    @ApiOperation(value = "列表设置-保存")
    @PostMapping("/head/field/{type}")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean reportHeadField(@PathVariable("type") String type,
                                   @RequestBody @Validated QueryHeadConfigDto condition) {
        ReportEnum reportEnum = ReportEnum.getByType(type);
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(), reportEnum.getHead()));
    }

    @ApiOperation(value = "列表设置-查询")
    @GetMapping("/head/field/{type}")
    public QueryHeadConfigDto reportHeadField(@PathVariable("type") String type) {
        ReportEnum reportEnum = ReportEnum.getByType(type);
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(reportEnum.getHead());
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(QueryHeadConfigDto.class);
    }

    @ApiOperation(value = "标准品属性-所有字段")
    @GetMapping("/standard/{standardId}")
    public QueryConditionStandardDto standardAllField(@PathVariable("standardId") Long standardId) {
        return queryFieldService.standardAllField(FormFeignClient.BIZ_TYPE_ASSET,
                standardId, true, false);
    }

    @ApiOperation(value = "排序字段")
    @GetMapping("/sortField/{type}")
    public QueryConditionSortDto sortField(@PathVariable("type") String type) {
        return predefinedReportFeignClient.assetSortField(type);
    }

}
