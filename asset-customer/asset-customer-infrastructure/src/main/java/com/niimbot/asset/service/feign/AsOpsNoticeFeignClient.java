package com.niimbot.asset.service.feign;

import com.niimbot.system.AsOpsNoticeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AsOpsNoticeFeignClient {

    @GetMapping("/server/system/opsNotice/available")
    AsOpsNoticeDto available();

    @GetMapping("/server/system/opsNotice/list")
    List<AsOpsNoticeDto> list();

}
