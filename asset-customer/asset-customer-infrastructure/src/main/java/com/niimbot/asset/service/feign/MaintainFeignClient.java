package com.niimbot.asset.service.feign;

import com.niimbot.system.TagAttrListDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2022/7/13 14:11
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaintainFeignClient {

    @GetMapping("server/maintenance/maintain/tag/attrList")
    TagAttrListDto getTagAttrList(@RequestParam(value = "kw", required = false) String kw);
}
