package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.*;
import com.niimbot.system.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标签打印管理feign客户端
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PrintFeignClient {

    /**
     * 设置默认材质
     *
     * @param printDataSetDto 设置打印数据dto
     * @return Boolean
     */
    @PutMapping(value = "server/system/print/setDefaultMaterial")
    TagMaterialDto setDefaultMaterial(PrintDataSetDto printDataSetDto);

    /**
     * 获取标签材质列表
     *
     * @return TagMaterialDto
     */
    @GetMapping(value = "server/system/print/getTagMaterial")
    List<TagMaterialDto> getTagMaterial(@RequestParam("printerName") String printerName);

    /**
     * 获取用户的打印模板
     *
     * @param printerName 设备名称
     * @return UserTagPrintDto
     */
    @GetMapping(value = "server/system/print/getPrintTpl/{printType}")
    UserTagPrintDto getPrintTpl(@PathVariable("printType") Short printType, @RequestParam("printerName") String printerName);

    /** 打印任务 开始 */
    /**
     * 任务列表
     *
     * @param dto 查询dto
     * @return 任务列表
     */
    @PostMapping(value = "/server/system/print/task/pageTaskPc")
    PageUtils<UserPrintTaskViewDto> pageTaskPc(PrintTaskQueryDto dto);

    /**
     * 任务列表
     *
     * @param dto 查询dto
     * @return 任务列表
     */
    @PostMapping(value = "/server/system/print/task/listTaskPc")
    List<UserPrintTaskViewDto> listTaskPc(PrintTaskQueryDto dto);

    /**
     * APP任务列表
     *
     * @param queryList 打印任务最后一次打印任务
     * @return 任务列表
     */
    @PostMapping(value = "/server/system/print/task/listTaskApp/{printType}/{taskStatus}")
    List<UserPrintTaskAppViewDto> listTaskApp(@PathVariable("printType") Short printType, @PathVariable("taskStatus") Short taskStatus, @RequestBody(required = false) List<LastPrintTaskQueryDto> queryList);

    /**
     * 开启任务
     *
     * @param configDto 查询参数
     * @return 打印数据
     */
    @PostMapping(value = "/server/system/print/task/start")
    PrintDataViewDto startTask(UserPrintTaskConfigDto configDto);

    /**
     * 机型打印
     *
     * @param configDto 任务id
     * @return 打印数据
     */
    @PostMapping(value = "/server/system/print/task/continue")
    PrintDataViewDto continueTask(UserPrintTaskConfigDto configDto);

    /**
     * 暂停任务
     *
     * @param logs 任务ID
     * @return true
     */
    @PostMapping(value = "/server/system/print/task/pause")
    Boolean pauseTask(List<UserPrintLogDto> logs);

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return true
     */
    @GetMapping(value = "/server/system/print/task/cancel")
    Boolean cancelTask(@RequestParam("taskId") Long taskId);

    /**
     * 新增打印
     *
     * @param printTask 新增打印
     * @return 结果
     */
    @PostMapping(value = "/server/system/print/task/add")
    PrintDataViewDto save(UserPrintTaskDto printTask);

    /**
     * 修改当前任务的打印配置
     *
     * @param printTask 打印配置
     * @return 结果
     */
    @PutMapping(value = "/server/system/print/task/config")
    Boolean configTask(UserPrintTaskConfigDto printTask);

    /**
     * 打印全部资产
     *
     * @param printTask 打印数据
     * @return 打印所需数据
     */
    // @PostMapping(value = "/server/system/print/task/all")
    // PrintDataViewDto printAll(UserPrintTaskDto printTask);

    /**
     * 继续打印校验
     *
     * @param taskId      任务id
     * @param printerName 打印机名称
     * @return true
     */
    @GetMapping(value = "/server/system/print/task/continue/check")
    UserPrintCheckInfoDto continueTaskCheck(@RequestParam("taskId") Long taskId,
                                            @RequestParam("printerName") String printerName);

    /**
     * APP 配置打印参数并打印
     *
     * @param printTask 打印任务
     * @return 打印数据
     */
    @PostMapping(value = "/server/system/print/task/configAppContinueTask")
    Boolean configAppContinueTask(UserPrintTaskConfigDto printTask);

    /**
     * APP 暂停任务
     *
     * @param logs 任务ID
     * @return true
     */
    @PostMapping(value = "/server/system/print/task/pauseAppTask")
    Boolean pauseAppTask(List<UserPrintLogAppDto> logs);

    /**
     * APP 更改打印设置
     *
     * @param taskId 任务id
     * @return 以前的打印任务信息
     */
    @GetMapping(value = "/server/system/print/task/{taskId}")
    UserPrintTaskInfoDto taskInfo(@PathVariable("taskId") Long taskId);

    /**
     * 取消打印、直接删除打印任务、不做check
     *
     * @param taskId taskId
     * @return true
     */
    @GetMapping(value = "/server/system/print/task/cancelTaskWithoutCheck")
    Boolean cancelTaskWithoutCheck(@RequestParam("taskId") Long taskId);

    /**
     * 查询app 打印任务列表条数
     *
     * @return number
     */
    @GetMapping("/server/system/print/task/app/printTaskCount/{printType}")
    Integer countAppTask(@PathVariable("printType") Short printType);

    /**
     * 查询所有打印机数据
     *
     * @return 所有数据
     */
    @GetMapping("server/system/printer/list")
    List<Map<String, Object>> listPrinter();

    /**
     * 更改打印任务状态
     *
     * @param taskId     打印任务Id
     * @param taskStatus 任务状态
     * @return true
     */
    @GetMapping(value = "/server/system/print/task/changeTaskStatus")
    Boolean changeTaskStatus(@RequestParam("taskId") Long taskId, @RequestParam("taskStatus") Integer taskStatus);

    /**
     * PC端用
     * 更改用户任务列表状态
     *
     * @param type 更改类型
     * @return true
     */
    @GetMapping(value = "/server/system/print/task/status/all/{printType}")
    Boolean changeAllTaskStatus(@PathVariable("printType") Short printType, @RequestParam("type") Integer type);

    @PutMapping(value = "/server/system/print/task/status/all")
    Boolean changeAllTaskStatus(@RequestParam("type") Integer type);

    /**
     * 打印任务列表下载
     *
     * @param dto dto
     * @return 列表
     */
    @PostMapping(value = "/server/system/print/task/down")
    List<UserPrintTaskViewDto> down(PrintTaskQueryDto dto);

    /**
     * 设置用户默认打印机系列
     *
     * @param id id
     * @return Boolean
     */
    @PutMapping(value = "server/system/printer/setDefaultSeries/{id}")
    Boolean setDefaultSeries(@PathVariable("id") Long id);

    /**
     * 获取设备详情
     *
     * @param model model
     * @return AdminPrinterDto
     */
    @GetMapping(value = "/server/system/printer/getPrinterDetail")
    AdminPrinterDto getPrinterDetail(@RequestParam("model") String model);

    @GetMapping("/server/system/print/task/app/listOfDataWithStatusPausedInTaskDetails")
    List<Object> listOfDataWithStatusPausedInTaskDetails(@RequestParam("taskId") Long taskId);

    @GetMapping("/server/system/print/task/details/page")
    PageUtils<UserPrintTaskGroupStatusResult> taskDetailsPage(@SpringQueryMap UserPrintTaskGroupByStatusPageQuery query);

    @PostMapping("/server/system/print/task/againTask")
    PrintDataViewDto againTask(@RequestBody UserPrintTaskDto printTask);

    @PostMapping("/server/system/print/task/continueTaskPrintOne")
    PrintDataViewDto continueTaskPrintOne(@RequestBody UserPrintTaskConfigDto configDto);

    @GetMapping("/server/system/print/task/printJson/{tagSizeId}")
    JSONObject getPrintJsonByTagSizeId(@PathVariable("tagSizeId") Long tagSizeId);

    @GetMapping("/server/system/printer/getInnerMode")
    List<PrinterInnerModeInfo> getInnerMode();

    @GetMapping("/server/system/printer/getMaxDriveVersion")
    MaxDriveVersion getMaxDriveVersion();

    @PostMapping("/server/system/print/task/changeAssetTagQrCodeValue/{tagId}")
    Boolean changeAssetTagQrCodeValue(@PathVariable("tagId") Long tagId);

    @PostMapping("/server/system/print/task/isSupportRfid/{type}")
    Boolean isSupportRfid(@RequestBody UserPrintTaskDto printTask, @PathVariable("type") String type);

    @PostMapping("/server/system/print/task/getPrintJson")
    List<PrintDataViewNewDto> getPrintJson(@RequestBody PrintPdfDto dto);

    @PostMapping("/server/system/print/task/getPrintData")
    List<JSONObject> getPrintData(@RequestBody PrintPdfDto dto);
}
