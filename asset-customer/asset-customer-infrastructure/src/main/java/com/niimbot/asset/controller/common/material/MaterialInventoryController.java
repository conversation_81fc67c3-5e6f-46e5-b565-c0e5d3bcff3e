package com.niimbot.asset.controller.common.material;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.MaterialInventoryFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.material.inventory.*;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2024/1/6 17:50
 */
@Slf4j
@Validated
@Api(tags = "耗材盘点")
@ResultController
@RequestMapping("api/common/material/inventory")
@RequiredArgsConstructor
public class MaterialInventoryController {

    private final MaterialInventoryFeignClient materialInventoryFeignClient;
    private final DictConvertUtil dictConvertUtil;
    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @ApiOperation("盘点单仓库列表")
    @GetMapping(value = "/repository/{inventoryId}")
    public List<Map<String, ?>> repositoryList(@PathVariable(value = "inventoryId") Long inventoryId) {
        // 获取数据权限
        List<MaterialRepositoryDto> all = materialInventoryFeignClient.repositoryList(inventoryId);
        return all.stream().map((Function<MaterialRepositoryDto, ImmutableMap<String, ?>>) materialRepositoryDto ->
                ImmutableMap.of("label", materialRepositoryDto.getName(),
                        "value", materialRepositoryDto.getId(),
                        "code", materialRepositoryDto.getCode())
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "查询仓库列表[无权限-盘点专用]")
    @PostMapping("/group/repository")
    public List<MaterialInventoryGroupDto> repositoryGroup(@RequestBody @Validated MaterialInventoryGroupQueryDto queryDto) {
        return materialInventoryFeignClient.repositoryGroup(queryDto);
    }


    @ApiOperation(value = "【PC】盘点分页列表")
    @AutoConvert
    @PostMapping("/page")
    public PageUtils<MaterialInventoryPageDto> page(@RequestBody InventoryQueryDto qry) {
        return materialInventoryFeignClient.page(qry);
    }

    @ApiOperation(value = "【PC】盘点详情列表")
    @PostMapping("/detail/page")
    public PageUtils<JSONObject> detailPage(@RequestBody @Validated InventoryDetailQueryDto qry) {
        PageUtils<MaterialInventoryDetailPageDto> pageUtils = materialInventoryFeignClient.detailPage(qry);
        List<MaterialInventoryDetailPageDto> list = pageUtils.getList();
        dictConvertUtil.convertToDictionary(list);
        List<JSONObject> jsonList = list.stream().map(MaterialInventoryDetailPageDto::translate).collect(Collectors.toList());
        PageUtils<JSONObject> page = new PageUtils<>();
        BeanUtil.copyProperties(pageUtils, page);
        page.setList(jsonList);
        return page;
    }

    @ApiOperation(value = "【APP】盘点分页列表")
    @AutoConvert
    @PostMapping("/app/page")
    public PageUtils<MaterialInventoryAppPageDto> appPage(@RequestBody InventoryQueryDto qry) {
        return materialInventoryFeignClient.appPage(qry);
    }

    @ApiOperation(value = "【APP】盘点详情列表")
    @AutoConvert
    @PostMapping("/detail/app/page")
    public PageUtils<MaterialInventoryDetailAppPageDto> detailAppPage(@RequestBody @Validated InventoryDetailAppQueryDto qry) {
        return materialInventoryFeignClient.detailAppPage(qry);
    }

    @ApiOperation(value = "盘点单盘点耗材详情")
    @GetMapping("/detail/material/info/{detailId}")
    public JSONObject detailMaterialInfo(@PathVariable("detailId") Long detailId) {
        MaterialInventoryMaterialInfoDto materialInfoDto = materialInventoryFeignClient.detailMaterialInfo(detailId);
        return materialInfoDto != null ? materialInfoDto.translate() : new JSONObject();
    }

    @ApiOperation(value = "扫码盘点耗材")
    @GetMapping("/scan/material")
    public MaterialScanMaterialDto scanMaterial(@ApiParam(name = "inventoryId", value = "盘点单id")
                                                @RequestParam(value = "inventoryId") String inventoryId,
                                                @ApiParam(name = "materialId", value = "耗材")
                                                @RequestParam("materialId") String materialId,
                                                @ApiParam(name = "repositoryId", value = "仓库ID")
                                                @RequestParam("repositoryId") String repositoryId) {
        return materialInventoryFeignClient.scanMaterial(inventoryId, materialId, repositoryId);
    }

    @ApiOperation(value = "盘点状态统计")
    @GetMapping("/status/statistics")
    public MaterialInventoryStatusStatisticsDto statusStatistics() {
        return materialInventoryFeignClient.statusStatistics();
    }

    @ApiOperation(value = "盘点详情统计")
    @GetMapping("/statistics/{inventoryId}")
    public MaterialInventoryStatisticsDto statistics(@PathVariable("inventoryId") Long inventoryId) {
        return materialInventoryFeignClient.statistics(inventoryId);
    }

    @ApiOperation(value = "盘点详情")
    @AutoConvert
    @GetMapping("/info/{inventoryId}")
    public MaterialInventoryInfoDto info(@PathVariable("inventoryId") Long inventoryId) {
        return materialInventoryFeignClient.info(inventoryId);
    }

    @ApiOperation(value = "创建盘点单")
    @RepeatSubmit
    @PostMapping("/create")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean create(@RequestBody @Validated MaterialInventoryCreateDto createDto) {
        MaterialInventoryInfoDto info = materialInventoryFeignClient.create(createDto);
        AuditLogs.sendRecord(() -> {
            Map<String, Object> params = new HashMap<>(2);
            params.put(Auditable.Tpl.CONTENT, info.getInventoryNo());
            return AuditLogRecord.create(Auditable.Action.ADD_MATERIAL_INVENTORY, params);
        });
        return true;
    }

    @ApiOperation(value = "取消盘点单")
    @RepeatSubmit
    @PostMapping("/cancel/{inventoryId}")
    @ResultMessage("取消成功")
    public Boolean cancel(@PathVariable("inventoryId") Long inventoryId) {
        MaterialInventoryInfoDto info = materialInventoryFeignClient.cancel(inventoryId);
        if (info != null) {
            AuditLogs.sendRecord(() -> {
                Map<String, Object> params = new HashMap<>(2);
                params.put(Auditable.Tpl.CONTENT, info.getInventoryNo());
                return AuditLogRecord.create(Auditable.Action.CANCEL_MATERIAL_INVENTORY, params);
            });
        }
        return true;
    }

    @ApiOperation(value = "删除盘点单")
    @RepeatSubmit
    @PostMapping("/delete/{inventoryId}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("inventoryId") Long inventoryId) {
        MaterialInventoryInfoDto info = materialInventoryFeignClient.delete(inventoryId);
        if (info != null) {
            AuditLogs.sendRecord(() -> {
                Map<String, Object> params = new HashMap<>(2);
                params.put(Auditable.Tpl.CONTENT, info.getInventoryNo());
                return AuditLogRecord.create(Auditable.Action.DEL_MATERIAL_INVENTORY, params);
            });
        }
        return true;
    }

    @ApiOperation(value = "上报耗材")
    @RepeatSubmit
    @PostMapping("/report")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean report(@RequestBody @Validated MaterialInventoryReportDto reportDto) {
        MaterialInventoryReportDto reportMaterial = materialInventoryFeignClient.report(reportDto);
        if (CollUtil.isNotEmpty(reportMaterial.getReportMaterials())) {
            AuditLogs.sendRecord(() -> {
                Map<String, Object> params = new HashMap<>(2);
                params.put(Auditable.Tpl.ORDER_NO, reportMaterial.getInventoryNo());
                params.put(Auditable.Tpl.CONTENT, reportMaterial.getReportMaterials().stream()
                        .map(m -> "上报耗材" + m.getMaterialName() + m.getReportNum() + "个")
                        .collect(Collectors.joining("，")));
                return AuditLogRecord.create(Auditable.Action.REPORT_MATERIAL_INVENTORY, params);
            });
        }
        return true;
    }

    @ApiOperation(value = "手动盘点")
    @RepeatSubmit
    @PostMapping("/manualInventory")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean manualInventory(@RequestBody @Validated MaterialInventoryManualDto inventoryManualDto) {
        return materialInventoryFeignClient.manualInventory(inventoryManualDto);
    }

    @ApiOperation(value = "查询盘点单盘点人列表")
    @GetMapping("/inventoryUsers/{inventoryId}")
    public List<Map<String, ?>> getInventoryUsers(@PathVariable("inventoryId") Long inventoryId) {
        MaterialInventoryInfoDto info = materialInventoryFeignClient.info(inventoryId);
        return info.getInventoryUsers().stream().map(userId ->
                ImmutableMap.of("label", cacheResourceUtil.getUserNameAndCode(userId), "value", userId)
        ).collect(toList());
    }

    @ApiOperation(value = "重新分配盘点人")
    @RepeatSubmit
    @PutMapping("/updateInventoryUsers")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean updateInventoryUsers(@RequestBody @Validated MaterialInventoryUpdateUserDto updateUserDto) {
        if (MaterialInventoryUpdateUserDto.TYPE_RETAIN == updateUserDto.getType()) {
            List<MaterialInventoryUpdateUserDto.UpdateUser> updateUsers = updateUserDto.getUpdateUsers();
            updateUsers.forEach(u -> {
                if (u.getOldUserId() == null) {
                    BusinessExceptionUtil.throwException("原始盘点人不能为空");
                }
            });
        }
        MaterialInventoryInfoDto info = materialInventoryFeignClient.updateInventoryUsers(updateUserDto);
        if (info != null) {
            AuditLogs.sendRecord(() -> {
                Map<String, Object> params = new HashMap<>(2);
                params.put(Auditable.Tpl.CONTENT, info.getInventoryNo());
                return AuditLogRecord.create(Auditable.Action.UPT_MATERIAL_INVENTORY_USER, params);
            });
        }
        return true;
    }

    @ApiOperation(value = "新增盘点人")
    @PostMapping("/addInventoryUsers")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean addInventoryUsers(@RequestBody @Validated MaterialInventoryAddUserDto addUserDto) {
        return materialInventoryFeignClient.addInventoryUsers(addUserDto);
    }


    @ApiOperation(value = "提交盘点单")
    @RepeatSubmit
    @PostMapping("/submit")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean submit(@RequestBody @Validated MaterialInventorySubmitDto submitDto) {
        MaterialInventoryInfoDto info = materialInventoryFeignClient.submit(submitDto);
        if (info != null) {
            AuditLogs.sendRecord(() -> {
                Map<String, Object> params = new HashMap<>(2);
                params.put(Auditable.Tpl.CONTENT, info.getInventoryNo());
                return AuditLogRecord.create(Auditable.Action.SUBMIT_MATERIAL_INVENTORY, params);
            });
        }
        return true;
    }

    @ApiOperation(value = "盘点结果报告")
    @AutoConvert
    @GetMapping("/result/report/{inventoryId}")
    public MaterialInventoryResultReportDto resultReport(@PathVariable("inventoryId") Long inventoryId) {
        return materialInventoryFeignClient.resultReport(inventoryId);
    }

}
