package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.system.RecommendRegisterDto;
import com.niimbot.system.RegisterCompanyDto;
import com.niimbot.system.RegisterDto;
import com.niimbot.system.RegisterUserDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2020/10/30
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CusRegisterFeignClient {

    /**
     * 保存注册用户信息
     *
     * @param registerUserDto 用户信息
     * @return 保持用户成功
     */
    @PostMapping(value = "server/system/cusUser/user")
    RegisterUserDto registerCusUser(RegisterUserDto registerUserDto);

    /**
     * 保持用户公司信息
     *
     * @param companyDto 用户公司信息
     * @return 结果
     */
    @PostMapping(value = "server/system/cusUser/company")
    String registerCompany(RegisterCompanyDto companyDto);

    /**
     * 查询手机是否已经注册
     *
     * @param mobile 查询条件
     * @return 用户信息
     */
    @GetMapping(value = "server/system/cusUser/getByMobile")
    RegisterUserDto getCusUserByMobile(@RequestParam("mobile") String mobile);

    /**
     * 企业注册
     *
     * @param dto dto
     * @return 注册账号
     */
    @PostMapping(value = "server/system/cusUser")
    CusUserDto register(RegisterDto dto);

    @PostMapping(value = "server/system/cusUser/recommendRegister")
    void recommendRegister(RecommendRegisterDto dto);


    /**
     * 注销账号
     *
     * @return 注册账号
     */
    @PostMapping(value = "server/system/cusUser/canceluser")
    boolean cancelUser();

    /**
     *  【注册】发送验证码校验手机号
     *
     * @param mobile
     */
    @GetMapping(value = "server/system/employee/checkRegisterMobile")
    void checkRegisterMobile(@RequestParam("mobile") String mobile);

    /**
     *  【注册】发送验证码校验邮箱
     *
     * @param email
     */
    @GetMapping(value = "server/system/employee/checkRegisterEmail")
    void checkRegisterEmail(@RequestParam("email") String email);

}
