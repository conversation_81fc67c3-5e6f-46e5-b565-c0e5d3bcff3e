package com.niimbot.asset.controller.common.means;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.easydesign.form.dto.FormTplAddCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.StandardListDto;
import com.niimbot.means.StandardQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @since 2021/1/8 15:55
 */
@Api(tags = "标准品管理")
@ResultController
@RequestMapping("api/common/standard")
public class StandardController {

    private final StandardFeignClient standardFeignClient;

    @Autowired
    public StandardController(StandardFeignClient standardFeignClient) {
        this.standardFeignClient = standardFeignClient;
    }

    @ApiOperation(value = "标准品分页查询")
    @GetMapping(value = "/page")
    public PageUtils<StandardListDto> page(StandardQueryDto queryDto) {
        return standardFeignClient.page(queryDto);
    }

    @ApiOperation(value = "标准品列表查询")
    @GetMapping(value = "/list")
    public List<StandardListDto> list(@ApiParam(name = "name", value = "标准品名称")
                                      @RequestParam(value = "name", required = false) String name) {
        return standardFeignClient.list(name);
    }

    @ApiOperation(value = "标准品动态表单")
    @GetMapping(value = "/form")
    public FormVO form(@ApiParam(name = "standardId", value = "标准品ID（不传返回标准属性）")
                       @RequestParam(value = "standardId", required = false) Long standardId) {
        return standardFeignClient.form(standardId, true);
    }

    @ApiOperation(value = "新增标准品数据")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean add(@RequestBody @Validated FormTplAddCmd formTplAddCmd) {
        return standardFeignClient.add(formTplAddCmd);
    }

    @ApiOperation(value = "更新标准品数据")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated FormTplAddCmd formTplAddCmd) {
        return standardFeignClient.edit(formTplAddCmd);
    }

    @ApiOperation(value = "删除标准品数据")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable Long id) {
        return standardFeignClient.delete(id);
    }


}
