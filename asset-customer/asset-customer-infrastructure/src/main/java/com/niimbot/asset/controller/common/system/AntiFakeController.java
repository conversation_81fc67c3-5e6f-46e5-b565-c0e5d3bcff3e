package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.service.feign.AntiFakeClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@Api(tags = "防伪打印公共管理")
@ResultController
@RequestMapping("api/common/antiFake")
public class AntiFakeController {

    private final AntiFakeClient antiFakeClient;

    private final AbstractFileUtils fileUtils;

    @Autowired
    public AntiFakeController(AntiFakeClient antiFakeClient, AbstractFileUtils fileUtils) {
        this.antiFakeClient = antiFakeClient;
        this.fileUtils = fileUtils;
    }

    @ApiOperation(value = "碳带防伪判断")
    @PostMapping("/check")
    public AntiFakeResDto checkAntiFake(@RequestBody @Validated AntiFakeCheckDto antiFakeCheckDto) {
        return antiFakeClient.checkAntiFake(antiFakeCheckDto);
    }

    @ApiOperation(value = "判断是否进行固件升级")
    @PostMapping("/machineCascadeDetail")
    public MachineDetailResDto machineCascadeDetail(@RequestBody @Validated MachineDetailDto machineDetailDto) {
        MachineDetailResDto machineDetailResDto = antiFakeClient.machineCascadeDetail(machineDetailDto);
        // 补全标签路径
        String printUrl = "/print/" + machineDetailResDto.getPrintName() + ".png";
        machineDetailResDto.setPhotoUrl(fileUtils.convertToDownloadUrl(printUrl));
        return machineDetailResDto;
    }

    @ApiOperation(value = "将打印信息上传云打印服务")
    @PostMapping("/uploadPrintInfo")
    public Boolean uploadPrintInfo(@RequestBody PrintInfoDto printInfoDto) {
        return antiFakeClient.uploadPrintInfo(printInfoDto);
    }

}
