package com.niimbot.asset.controller.common.inner;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@ResultController
@RequiredArgsConstructor
@RequestMapping("/api/inner/redis")
public class RedisController {

    private final RedisService redisService;

    public static final String CODE = "asset-!sdasfolmkh";

    private void checkCode(String code) {
        if (!CODE.equals(code)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "UNKNOWN");
        }
    }

    @PostMapping("/get")
    public Object get(@RequestBody GetCache getCache) {
        checkCode(getCache.getCode());
        return redisService.get(getCache.getKey());
    }

    @PostMapping("/del")
    public Boolean del(@RequestBody GetCache getCache) {
        checkCode(getCache.getCode());
        redisService.del(getCache.getKey());
        return true;
    }

    @PostMapping("/hGet")
    public Object hGet(@RequestBody GetCache getCache) {
        checkCode(getCache.getCode());
        return redisService.hGet(getCache.getKey(), getCache.getHashKey());
    }

    @PostMapping("/hDel")
    public Boolean hDel(@RequestBody GetCache getCache) {
        checkCode(getCache.getCode());
        redisService.hDel(getCache.getKey(), getCache.getHashKey());
        return true;
    }

}
