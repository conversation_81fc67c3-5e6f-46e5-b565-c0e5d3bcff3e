package com.niimbot.asset.controller.common.means;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.annotation.RequestJson;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.MaintainPlanFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectPlanFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.equipment.EntSntRange;
import com.niimbot.equipment.ListEntSntRange;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.MaintainPlanInfoDto;
import com.niimbot.means.AssetBatchDto;
import com.niimbot.means.AssetCopyDto;
import com.niimbot.means.AssetDto;
import com.niimbot.means.AssetOperationDto;
import com.niimbot.system.AsCusUserSettingDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

;

/**
 * <AUTHOR>
 * @since 2020/12/9 17:39
 */
@Api(tags = "资产管理")
@Validated
@ResultController
@RequestMapping("api/common/asset")
public class AssetController {

    private final FormFeignClient formFeignClient;

    private final AssetService assetService;

    private final AssetFeignClient assetFeignClient;

    private final CusUserSettingFeignClient settingFeignClient;

    private final StandardFeignClient standardFeignClient;

    private final MaintainPlanFeignClient maintainPlanFeignClient;
    private final DesensitizationDataUtil desensitizationDataUtil;

    @Resource
    private EquipmentSiteInspectPlanFeignClient planFeignClient;

    @Autowired
    public AssetController(FormFeignClient formFeignClient, AssetService assetService,
                           AssetFeignClient assetFeignClient,
                           CusUserSettingFeignClient settingFeignClient,
                           StandardFeignClient standardFeignClient,
                           MaintainPlanFeignClient maintainPlanFeignClient, DesensitizationDataUtil desensitizationDataUtil) {
        this.formFeignClient = formFeignClient;
        this.assetService = assetService;
        this.assetFeignClient = assetFeignClient;
        this.settingFeignClient = settingFeignClient;
        this.standardFeignClient = standardFeignClient;
        this.maintainPlanFeignClient = maintainPlanFeignClient;
        this.desensitizationDataUtil = desensitizationDataUtil;
    }

    @ApiOperation(value = "资产详情")
    @GetMapping(value = "/info")
    public JSONObject getInfo(
            @NotNull(message = "资产ID不能为空")
            @ApiParam(name = "assetId", value = "资产Id")
            @RequestParam("assetId") String assetId,
            @ApiParam(name = "isEdit", value = "是否编辑")
            @RequestParam(value = "isEdit", required = false, defaultValue = "false") Boolean isEdit) {
        JSONObject result = assetService.getInfo(assetId);
        //数据脱敏处理，变更的时候，返回详情不能数据脱敏，否则编辑提交的时候，会把脱敏字段数据提交上来，会产生很多脏数据
        if (!isEdit && Objects.nonNull(result)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.ASSET.getCode());
        }
        return result;
    }

    @ApiOperation(value = "更新用户极简模式")
    @PutMapping(value = "/simplify")
    public Boolean updateSimplify(
            @NotNull(message = "是否极简模式不能为空")
            @ApiParam(name = "simplify", value = "是否极简模式")
            @RequestJson("simplify") Boolean simplify) {
        return settingFeignClient.updateSimplify(new AsCusUserSettingDto().setAssetSimplify(simplify));
    }

    @ApiOperation(value = "自动获取资产编码")
    @GetMapping(value = "/assetCode")
    public String getAssetCode() {
        return "系统自动生成";
    }

    @ApiOperation(value = "新增资产数据")
    @RepeatSubmit
    @PostMapping()
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public AssetDto add(@RequestBody @Validated({Insert.class}) AssetDto assetDto) {
        AssetDto result = assetService.add(assetDto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.ADD_MEANS, result));
        return result;
    }

    @ApiOperation(value = "编辑资产数据")
    @PutMapping()
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated({Update.class}) AssetDto assetDto) {
        assetService.edit(assetDto);
        AuditLogs.sendRecord(() -> {
            AssetDto dto = assetFeignClient.getInfoNoPerm(assetDto.getId());
            return AuditLogRecord.create(Auditable.Action.UPT_MEANS, dto);
        });
        return true;
    }

    @ApiOperation(value = "批量编辑资产数据")
    @PutMapping("/batch")
    @ResultMessage(ResultConstant.EDIT_BATCH_SUCCESS)
    public Boolean editBatch(@RequestBody @Validated AssetBatchDto assetBatchDto) {
        List<AuditableOperateResult> results = assetService.editBatch(assetBatchDto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.UPT_MEANS_BATCH, results));
        return CollUtil.isNotEmpty(results);
    }

    public void removePre(List<Long> assetIds) {
        List<MaintainPlanInfoDto> plans = maintainPlanFeignClient.getByAssetIds(assetIds);
        if (CollUtil.isNotEmpty(plans)) {
            throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-资产存在资产保养计划");
        }
        List<EntSntRange> ranges = planFeignClient.rangeList(new ListEntSntRange().setCompanyId(LoginUserThreadLocal.getCompanyId()).setDataIds(assetIds));
        if (CollUtil.isNotEmpty(ranges)) {
            throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-设备存在巡检计划");
        }
    }

    @ApiOperation(value = "资产删除")
    @DeleteMapping("/{assetId}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean removeById(@PathVariable("assetId") Long assetId) {
        removePre(Collections.singletonList(assetId));
        AuditableOperateResult result = assetFeignClient.removeById(assetId);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MEANS, result));
        return result.successful();
    }

    @ApiOperation(value = "资产批量删除")
    @DeleteMapping()
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody List<Long> assetIds) {
        removePre(assetIds);
        List<AuditableOperateResult> results = assetFeignClient.remove(assetIds);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.DEL_MEANS, results));
        return CollUtil.isNotEmpty(results);
    }

    @ApiOperation(value = "批量复制")
    @RepeatSubmit
    @PostMapping("/copy")
    @ResultMessage("批量复制成功")
    public List<Long> copy(@RequestBody @Validated AssetCopyDto copyDto) {
        List<AuditableOperateResult> result = assetService.copy(copyDto);
        AuditLogs.sendRecord(() -> {
            Map<String, Object> params = Auditable.Resolver.resolveParams(result);
            params.put(Auditable.Tpl.COUNT, String.valueOf(result.size()));
            return AuditLogRecord.create(Auditable.Action.COPY_MEANS, params);
        });
        return result.stream().map(AuditableOperateResult::getId).collect(Collectors.toList());
    }

    @ApiOperation(value = "批量复制字段列表")
    @GetMapping("/copy/fields/{assetId}")
    public List<Map<String, Object>> copyFields(@PathVariable("assetId") Long assetId) {
        AssetDto assetDto = assetFeignClient.getInfo(assetId);
        if (ObjectUtil.isNull(assetDto)) {
            throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
        }
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();

        Long standardId = assetDto.getStandardId();
        if (standardId != null) {
            List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(standardExtField);
        }
        // 过滤必填和隐藏的字段 + 唯一性字段
        return formFields.stream().filter(f ->
                        (!FormFieldCO.SPLIT_LINE.equals(f.getFieldType()) && !f.isHidden() && !f.requiredProps() && !(f.getFieldProps().containsKey("unique") && BooleanUtil.isTrue(f.getFieldProps().getBoolean("unique")))))
                .map(f -> ImmutableMap.<String, Object>builder()
                        .put("id", f.getId())
                        .put("fieldCode", f.getFieldCode())
                        .put("fieldName", f.getFieldName())
                        .build()
                ).collect(Collectors.toList());
    }

    @ApiOperation(value = "查询全部资产操作")
    @GetMapping(value = "/allAssetOpt")
    public List<AssetOperationDto> allAssetOpt() {
        List<AssetOperationDto> allAssetOpt = assetFeignClient.allAssetOpt();
        AssetOperationDto removeItem = null;
        for (AssetOperationDto item : allAssetOpt) {
            if (Convert.toInt(item.getOrderType(), -1).equals(AssetConstant.ORDER_TYPE_MAINTAIN)) {
                removeItem = item;
            }
            if (item.getName().length() <= 2) {
                item.setName("可" + item.getName() + "资产");
            }
        }
        if (removeItem != null) {
            allAssetOpt.remove(removeItem);
        }
        return allAssetOpt;
    }

    @ApiOperation(value = "资产列表搜索配置")
    @PostMapping(value = "/listConfig")
    public Boolean updateConfig(@RequestBody List<String> configDto) {
        return settingFeignClient.updateSimplify(new AsCusUserSettingDto().setAssetSearchField(new HashSet<>(configDto).stream().collect(Collectors.toList())));
    }

    @ApiOperation(value = "资产列表搜索配置查询")
    @GetMapping(value = "/listConfig")
    public List<String> listConfig() {
        AsCusUserSettingDto setting = settingFeignClient.getSetting();
        if (setting != null && setting.getAssetSearchField() != null) {
            return setting.getAssetSearchField();
        } else {
            settingFeignClient.saveOrUpdate(new AsCusUserSettingDto().setAssetSearchField(ListUtil.of("asset_name", "asset_code")));
            return ListUtil.of("asset_name", "asset_code");
        }
    }

}
