package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.MaterialInventoryFeignClient;
import com.niimbot.asset.service.feign.SelectorRecordFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.AddSelectorRecord;
import com.niimbot.system.GetSelectorRecord;
import com.niimbot.system.SelectorRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "【选择器记录】")
@ResultController
@RequiredArgsConstructor
@RequestMapping("/api/common/selector/record")
public class SelectorRecordController {

    private final SelectorRecordFeignClient selectorRecordFeignClient;
    private final MaterialInventoryFeignClient inventoryFeignClient;

    @ApiOperation("保存选择器记录")
    @PostMapping("/add")
    public Boolean record(@Validated @RequestBody AddSelectorRecord record) {
        record.setCompanyId(LoginUserThreadLocal.getCompanyId());
        record.setEmpId(LoginUserThreadLocal.getCurrentUserId());
        return selectorRecordFeignClient.record(record);
    }

    @ApiOperation("获取选择器记录")
    @GetMapping("/get")
    public List<SelectorRecord> records(@Validated GetSelectorRecord record) {
        record.setCompanyId(LoginUserThreadLocal.getCompanyId());
        record.setEmpId(LoginUserThreadLocal.getCurrentUserId());
        return selectorRecordFeignClient.records(record);
    }

    @ApiOperation("获取选择器记录(盘点仓库用)")
    @GetMapping("/get/filter")
    public List<SelectorRecord> filterRecords(@Validated GetSelectorRecord record) {
        record.setCompanyId(LoginUserThreadLocal.getCompanyId());
        record.setEmpId(LoginUserThreadLocal.getCurrentUserId());
        List<SelectorRecord> records = selectorRecordFeignClient.records(record);
        if ("repository".equalsIgnoreCase(record.getType())) {
            List<Long> progressRepoList = inventoryFeignClient.progressRepoList();
            return records.stream().filter(f -> !progressRepoList.contains(f.getId())).collect(Collectors.toList());
        } else {
            return records;
        }
    }

}
