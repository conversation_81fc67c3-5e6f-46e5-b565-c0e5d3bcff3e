package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2021/7/7 10:18
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialLyOrderFeignClient {
    /**
     * 设置审批流信息获取
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/material/order/ly/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(@RequestBody MaterialLyOrderDto dto);

    /**
     * 创建耗材领用单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/material/order/ly")
    AuditableCreateOrderResult create(@RequestBody MaterialLyOrderSubmitDto dto);

    /**
     * 耗材领用单详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/material/order/ly/{id}")
    MaterialLyOrderDto getById(@PathVariable("id") Long id);

    /**
     * 耗材领用单明细详情
     *
     * @param orderId
     * @param materialId
     * @return
     */
    @GetMapping(value = "server/material/order/ly/detail/{orderId}/{materialId}")
    MaterialLyOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);

    /**
     * 耗材领用单分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/material/order/ly/page")
    PageUtils<MaterialLyOrderDto> page(@RequestBody MaterialOrderQueryDto query);


    /**
     * 耗材领用单明细分页查询
     *
     * @param query
     * @return
     */
    @GetMapping(value = "server/material/order/ly/pageDetail")
    PageUtils<MaterialLyOrderDetailDto> pageDetail(@SpringQueryMap MaterialOrderDetailQueryDto query);

    /**
     * 资产单据-用于导出
     *
     * @param query
     * @return 结果
     */
    @PostMapping(value = "server/material/order/ly/listForExport")
    PageUtils<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto query);
}
