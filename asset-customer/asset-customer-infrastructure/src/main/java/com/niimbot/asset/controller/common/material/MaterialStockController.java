package com.niimbot.asset.controller.common.material;

import com.google.common.net.HttpHeaders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.MaterialExcelService;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.MaterialStockFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialOrderStockDto;
import com.niimbot.material.MaterialOrderStockQueryDto;
import com.niimbot.material.MaterialStockDetailDto;
import com.niimbot.material.MaterialStockDetailQueryDto;
import com.niimbot.material.MaterialStockDto;
import com.niimbot.material.MaterialStockImportDto;
import com.niimbot.material.MaterialStockInfoDto;
import com.niimbot.material.MaterialStockListConfigDto;
import com.niimbot.material.MaterialStockLogDto;
import com.niimbot.material.MaterialStockLogQueryDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.material.MaterialTotalStockDto;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.AsCusUserSettingDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryHeadConfigDto;
import com.niimbot.system.QueryKwSearchDto;
import com.niimbot.system.enums.SearchTypeEnum;

import org.apache.catalina.manager.Constants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/7/8 15:19
 */
@Slf4j
@Api(tags = "【耗材】库存管理")
@RequestMapping("api/common/material/stock")
@ResultController
@Validated
public class MaterialStockController {

    private final MaterialStockFeignClient materialStockFeignClient;
    private final FormFeignClient formFeignClient;
    private final AsMaterialUtil materialUtil;
    private final MaterialExcelService materialExcelService;
    private final MaterialQueryFieldService queryFieldService;
    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;
    private final CusUserSettingFeignClient settingFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @Autowired
    public MaterialStockController(MaterialStockFeignClient materialStockFeignClient,
                                   FormFeignClient formFeignClient,
                                   AsMaterialUtil materialUtil,
                                   MaterialExcelService materialExcelService,
                                   MaterialQueryFieldService queryFieldService,
                                   AsQueryConditionConfigFeignClient queryConditionConfigFeignClient,
                                   CusUserSettingFeignClient settingFeignClient) {
        this.materialStockFeignClient = materialStockFeignClient;
        this.formFeignClient = formFeignClient;
        this.materialUtil = materialUtil;
        this.materialExcelService = materialExcelService;
        this.queryFieldService = queryFieldService;
        this.queryConditionConfigFeignClient = queryConditionConfigFeignClient;
        this.settingFeignClient = settingFeignClient;
    }

    @ApiOperation(value = "实时库存列表")
    @AutoConvert
    @PostMapping(value = "/page")
    public PageUtils<JSONObject> page(@RequestBody @Validated MaterialStockQueryDto queryDto) {
        //设置kw搜索字段类型
        setKwFieldType(queryDto);
        PageUtils<MaterialStockDto> page = materialStockFeignClient.page(queryDto);
        // 返回结果集
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<JSONObject> materialDataList = page.getList().stream()
                .map(MaterialStockDto::translate).collect(Collectors.toList());
        materialUtil.translateMaterialJsonBatch(materialDataList, material.getFormFields());
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(materialDataList, SensitiveObjectTypeEnum.MATERIAL.getCode());
        PageUtils<JSONObject> materialPage = new PageUtils<>();
        BeanUtil.copyProperties(page, materialPage);
        materialPage.setList(materialDataList);
        return materialPage;
    }

    /**
     * 设置kw搜索字段的类型
     * @param queryDto
     */
    private void setKwFieldType(MaterialStockQueryDto queryDto) {
        if (CollUtil.isEmpty(queryDto.getKwFiled())) {
            return ;
        }

        Map<String, QueryConditionDto> queryFieldMap = new HashMap<>();
        List<QueryConditionDto> allQueryField = queryFieldService.materialSearchAllQueryField();
        if (CollUtil.isNotEmpty(allQueryField)) {
            queryFieldMap = allQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, value -> value, (v1, v2) -> v2));
        }

        for (QueryKwSearchDto kwField : queryDto.getKwFiled()) {
            //设置字段类型
            if (Objects.nonNull(queryFieldMap.get(kwField.getCode()))) {
                kwField.setType(SearchTypeEnum.getByCode(queryFieldMap.get(kwField.getCode()).getType()));
            }
        }
    }

    @ApiOperation(value = "实时库存扫码查询")
    @AutoConvert
    @GetMapping(value = "/scan")
    public JSONObject scan(
            @ApiParam(name = "repositoryId", value = "仓库id", required = true)
            @RequestParam("repositoryId") Long repositoryId,
            @ApiParam(name = "materialId", value = "耗材id", required = true)
            @RequestParam("materialId") String materialId,
            @ApiParam(name = "stock", value = "当前库存值，1为有库存 ，2为无库存")
            @RequestParam(value = "stock", required = false) Integer stock) {
        MaterialStockQueryDto stockQueryDto = new MaterialStockQueryDto();
        stockQueryDto.setRepositoryId(repositoryId);
        stockQueryDto.setMaterialScan(materialId);
        QueryConditionDto queryConditionDto = new QueryConditionDto();
        queryConditionDto.setCode(QueryFieldConstant.MATERIAL_STOCK_FILED_STOCK)
                .setType(FormFieldCO.IMAGES)
                .setQuery(stock != null ? Convert.toStr(stock) : "all")
                .setQueryData(StrUtil.EMPTY);
        stockQueryDto.setConditions(ListUtil.of(queryConditionDto));
        PageUtils<MaterialStockDto> page = materialStockFeignClient.page(stockQueryDto);
        List<MaterialStockDto> stockDtos = page.getList();
        if (CollUtil.isEmpty(stockDtos)) {
            return new JSONObject();
        }
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        JSONObject translate = stockDtos.get(0).translate();
        materialUtil.translateMaterialJson(translate, material.getFormFields());
        return translate;
    }

    @ApiOperation(value = "实时库存总量")
    @PostMapping(value = "/total")
    public JSONObject total(@RequestBody @Validated MaterialStockQueryDto queryDto) {
        //设置kw搜索字段类型
        setKwFieldType(queryDto);
        MaterialTotalStockDto stockDto = materialStockFeignClient.total(queryDto);
        JSONObject result = JsonUtil.toJsonObject(stockDto);
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.MATERIAL.getCode());
        return result;
    }

    @ApiOperation(value = "单据特殊耗材列表")
    @PostMapping(value = "/order/page")
    public PageUtils<JSONObject> orderStockPage(@RequestBody @Validated MaterialOrderStockQueryDto queryDto) {
        PageUtils<MaterialOrderStockDto> page = materialStockFeignClient.orderStockPage(queryDto);
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<JSONObject> collect = page.getList().stream().map(f -> {
            JSONObject translate = f.translate();
            translate.putAll(f.getExtension());
            return translate;
        }).collect(Collectors.toList());
        materialUtil.translateMaterialJsonBatch(collect, material.getFormFields());
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(collect, SensitiveObjectTypeEnum.MATERIAL.getCode());
        return new PageUtils<>(collect, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "实时库存出入库记录")
    @AutoConvert
    @GetMapping(value = "/log")
    public PageUtils<JSONObject> stockLog(@Validated MaterialStockLogQueryDto queryDto) {
        PageUtils<MaterialStockLogDto> materialPage = materialStockFeignClient.stockLog(queryDto);
        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtils.copyProperties(materialPage, result);
        //数据脱敏处理
        if (CollUtil.isNotEmpty(materialPage.getList())) {
            List<JSONObject> materialLogList = materialPage.getList().stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
            desensitizationDataUtil.handleSensitiveField(materialLogList, SensitiveObjectTypeEnum.MATERIAL.getCode());
            result.setList(materialLogList);
        } else {
            result.setList(Collections.emptyList());
        }
        return result;
    }

    @ApiOperation(value = "实时库存出入库记录固定数据")
    @GetMapping(value = "/log/detail")
    public PageUtils<JSONObject> stockLogDetail(MaterialStockLogQueryDto queryDto) {
        PageUtils<MaterialStockInfoDto> stockLogResult = materialStockFeignClient.stockLogDetail(queryDto);
        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtils.copyProperties(stockLogResult, result);
        //数据脱敏处理
        if (CollUtil.isNotEmpty(stockLogResult.getList())) {
            List<JSONObject> stockLogList = stockLogResult.getList().stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
            desensitizationDataUtil.handleSensitiveField(stockLogList, SensitiveObjectTypeEnum.MATERIAL.getCode());
            result.setList(stockLogList);
        } else {
            result.setList(Collections.emptyList());
        }
        return result;
    }

    @ApiOperation(value = "导出")
    @PostMapping("/export")
    public void export(@RequestBody @Validated MaterialStockQueryDto queryDto, HttpServletResponse response) {
        try {
            List<AssetHeadDto> headDtos = queryFieldService.materialHeadView();
            // 查询配置项
            QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_HEAD);
            QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
            Long standardId = -1L;
            if (generalDto.getStandardId() != null) {
                standardId = generalDto.getStandardId();
            }
            headDtos = headDtos.stream()
                    .filter(f -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES).contains(f.getType()))
                    .collect(Collectors.toList());
            // 查询head
            LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
            for (AssetHeadDto head : headDtos) {
                headerData.put(head.getCode(), head.getName());
            }

            headerData.put("avgPrice", "价值（加权平均）");
            headerData.put("currentQuantity", "当前库存");
            headerData.put("totalMoney", "库存总金额（元）");
            headerData.put("status", "库存预警状态");
            // 查询数据
            List<JSONObject> excel = materialExcelService.getStockExcelData(queryDto, standardId);
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(excel, SensitiveObjectTypeEnum.MATERIAL.getCode());

            String fileName = "实时库存-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (queryDto.getPageNum() > 1) {
                fileName += "-" + queryDto.getPageNum();
            }
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_MRL_STOCK));
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "单个耗材的库存分布情况")
    @GetMapping(value = "/detail")
    public PageUtils<MaterialStockDetailDto> stockDetail(MaterialStockDetailQueryDto queryDto) {
        return materialStockFeignClient.stockDetail(queryDto);
    }

    @ApiOperation(value = "导出耗材库存模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        ClassPathResource templateSource = new ClassPathResource("/excelTemplate/material_stock_template.xlsx");
        try (OutputStream out = response.getOutputStream();
             InputStream inputStream = templateSource.getInputStream()) {
            String fileName = "耗材入库数量和入库总价导入模板.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "导入耗材库存模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    public List<MaterialStockImportDto> importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        String fileName = file.getOriginalFilename();
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            // 设置锁
            return materialExcelService.parseStockStream(stream);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error("import stock error:", e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "导出耗材错误数据")
    @PostMapping(value = "/exportErrorData")
    public void exportErrorData(HttpServletResponse response,
                                @RequestBody List<MaterialStockImportDto> importDtoList) {
        ClassPathResource templateSource = new ClassPathResource("/excelTemplate/material_stock_error_template.xlsx");
        try (OutputStream out = response.getOutputStream();
             InputStream inputStream = templateSource.getInputStream()) {
            String fileName = "耗材入库数量和入库总价错误数据.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            writer.setCurrentRow(2);
            LinkedHashMap<String, String> stockHead = ExcelUtils.buildExcelHead(MaterialStockImportDto.class);
            List<String> keyList = new ArrayList<>(stockHead.keySet());
            importDtoList.forEach(f -> {
                JSONObject json = (JSONObject) JSON.toJSON(f);
                List<String> rowData = keyList.stream()
                        .map(d -> {
                            Object o = json.get(d);
                            if (o != null) {
                                if (o instanceof JSONArray) {
                                    List<String> list = ((JSONArray) o).toJavaList(String.class);
                                    return String.join("；", list);
                                } else {
                                    return o.toString();
                                }
                            } else {
                                return null;
                            }
                        }).collect(Collectors.toList());
                writer.writeRow(rowData);
            });

            writer.autoSizeColumnAll();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "列表配置")
    @PostMapping(value = "/listConfig")
    public Boolean updateConfig(@RequestBody MaterialStockListConfigDto configDto) {
        return settingFeignClient.updateSimplify(new AsCusUserSettingDto()
                .setFilterZeroStock(configDto.getFilterZeroStock())
                .setFilterNullStock(configDto.getFilterNullStock()));
    }

    @ApiOperation(value = "配置查询")
    @GetMapping(value = "/listConfig")
    public MaterialStockListConfigDto listConfig() {
        AsCusUserSettingDto setting = settingFeignClient.getSetting();
        if (setting != null) {
            return new MaterialStockListConfigDto()
                    .setFilterNullStock(setting.getFilterNullStock())
                    .setFilterZeroStock(setting.getFilterZeroStock());
        }
        return null;
    }

}
