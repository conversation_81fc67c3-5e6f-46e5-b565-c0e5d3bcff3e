package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.maintenance.RepairReportOrderDetailDto;
import com.niimbot.maintenance.RepairReportOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairReportOrderDto;
import com.niimbot.maintenance.RepairReportOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/24 10:41
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface RepairReportOrderFeignClient {
    /**
     * 设置审批流信息获取
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/maintenance/repair/report/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(@RequestBody RepairReportOrderDto dto);

    /**
     * 创建报修单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/maintenance/repair/report")
    AuditableCreateOrderResult create(@RequestBody RepairReportOrderSubmitDto dto);

    /**
     * 报修单详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/maintenance/repair/report/{id}")
    RepairReportOrderDto getById(@PathVariable("id") Long id);

    /**
     * 报修单明细详情
     * @param orderId
     * @param assetId
     * @return
     */
    @GetMapping(value = "server/maintenance/repair/report/detail/{orderId}/{assetId}")
    RepairReportOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("assetId") Long assetId);

    /**
     * 报修单分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/maintenance/repair/report/page")
    PageUtils<AsOrderDto> page(@RequestBody AsOrderQueryDto query);

    /**
     * 报修单明细分页查询
     *
     * @param query
     * @return
     */
    @GetMapping(value = "server/maintenance/repair/report/pageDetail")
    PageUtils<RepairReportOrderDetailDto> pageDetail(@SpringQueryMap RepairReportOrderDetailPageQueryDto query);

    /**
     * 资产报修单据-用于导出
     *
     * @param orderQueryDto orderQueryDto
     * @return 结果
     */
    @PostMapping(value = "server/maintenance/repair/report/listForExport")
    List<AsOrderDto> listForExport(@RequestBody AsOrderQueryDto orderQueryDto);

    /**
     * 单据资产列表查询
     *
     * @param ids
     * @return 结果
     */
    @PostMapping(value = "server/maintenance/repair/report/assets")
    List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids);

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @param assetId assetId
     * @return 结果
     */
    @GetMapping(value = "server/maintenance/repair/report/approveOrderByAsset/{assetId}")
    AsOrderInfoDto getApproveOrderByAssetId(@PathVariable("assetId") Long assetId);
}
