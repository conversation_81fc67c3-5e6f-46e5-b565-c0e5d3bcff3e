package com.niimbot.asset.service.feign;

import com.niimbot.sale.CompanyResourceCapacityDto;
import com.niimbot.sale.CompanyResourceDto;
import com.niimbot.sale.ResourcePackDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/8 14:39
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CompanyResourceFeignClient {

    @GetMapping(value = "server/sale/resource/company/capacity")
    CompanyResourceCapacityDto getCapacity();

    @GetMapping(value = "server/sale/resource/resourcePack")
    List<ResourcePackDto> listPack();

    @GetMapping(value = "server/sale/resource/company/capacity/detail")
    List<CompanyResourceDto> getCapacityDetail();

    @GetMapping(value = "server/sale/resource/resourcePack/{id}")
    ResourcePackDto info(@PathVariable("id") Long id);
}
