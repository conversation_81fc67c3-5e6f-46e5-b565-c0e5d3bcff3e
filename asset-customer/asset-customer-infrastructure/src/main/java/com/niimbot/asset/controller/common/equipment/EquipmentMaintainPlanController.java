package com.niimbot.asset.controller.common.equipment;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentMaintainPlanFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.equipment.*;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.Auditable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "【设备管理】设备保养计划")
@Slf4j
@ResultController
@RequiredArgsConstructor
@RequestMapping("api/common/equipment/maintain/plan")
public class EquipmentMaintainPlanController {

    private final AsAssetUtil assetUti;

    private final AsMaterialUtil materialUtil;

    private final DictConvertUtil dictConvertUtil;

    private final DesensitizationDataUtil desensitizationDataUtil;

    private final EquipmentMaintainPlanFeignClient planFeignClient;

    private final FormFeignClient formFeignClient;

    @RepeatSubmit
    @ApiOperation("预览设备保养计划")
    @PostMapping("/preview")
    public EntMatPlanPreview previewPlan(@RequestBody @Validated CreateEntMatPlan plan) {
        plan.depthCheck();
        return planFeignClient.previewPlan(plan);
    }

    @RepeatSubmit
    @ApiOperation("创建设备保养计划")
    @PostMapping("/create")
    @AuditLog(Auditable.Action.ADD_ENT_MAT_PLAN)
    public AuditableCreatePlanResult createPlan(@RequestBody @Validated CreateEntMatPlan plan) {
        plan.depthCheck();
        return planFeignClient.createPlan(plan);
    }

    @RepeatSubmit
    @ApiOperation("停止设备保养计划")
    @PostMapping("/stop")
    @AuditLog(Auditable.Action.STOP_ENT_MAT_PLAN)
    public AuditableStopPlanResult stopPlan(@RequestBody @Validated StopEntMatPlan union) {
        return planFeignClient.stopPlan(union);
    }

    @RepeatSubmit
    @ApiOperation("删除设备保养计划")
    @PostMapping("/remove")
    @AuditLog(Auditable.Action.DEL_ENT_MAT_PLAN)
    public AuditableRemovePlanResult removePlan(@RequestBody @Validated RemoveEntMatPlan remove) {
        return planFeignClient.removePlan(remove);
    }

    @RepeatSubmit
    @ApiOperation("设置设备保养计划保养负责人")
    @PostMapping("/reUser")
    public Boolean rePlanUser(@RequestBody @Validated ReEntMatPlanUser change) {
        return planFeignClient.rePlanUser(change);
    }

    @ApiOperation("设备保养计划详情")
    @GetMapping("/detail")
    public EntMatPlanDetail detailPlan(@RequestParam("planId") @NotNull(message = "计划ID不能为空") Long planId) {
        return planFeignClient.detailPlan(planId);
    }

    @ApiOperation(("设备保养计划已选择的设备分类列表"))
    @GetMapping("/cate")
    public List<EntMatPlanEntCateData> listEntCate(@RequestParam("planId") @NotNull(message = "计划ID不能为空") Long planId) {
        return planFeignClient.listEntCate(planId);
    }

    @ApiOperation("设备保养计划分页列表搜索")
    @PostMapping("/search")
    public PageUtils<JSONObject> searchPlan(@RequestBody @Validated EntMatPlanSearch search) {
        PageUtils<EntMatPlan> page = planFeignClient.searchPlan(search);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> json = page.getList().stream().map(EntMatPlan::translate).collect(Collectors.toList());
        return new PageUtils<>(json, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @RepeatSubmit
    @ApiOperation("设备保养计划导出")
    @PostMapping("/export")
    public void exportPlan(@RequestBody @Validated EntMatPlanExport export, HttpServletResponse response) {
        List<JSONObject> json = searchPlan(export).getList();
        Affirm.notEmpty(json, "列表为空");
        List<ExportEntMatPlan> list = json.stream().map(v -> v.toJavaObject(ExportEntMatPlan.class)).collect(Collectors.toList());
        EntMatPlanCounter defaultCounter = new EntMatPlanCounter();
        Map<Long, EntMatPlanCounter> counterMap = planFeignClient.countPlan(list.stream().map(ExportEntMatPlan::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(EntMatPlanCounter::getPlanId, v -> v));
        list.forEach(v -> {
            if (Objects.nonNull(v.getCreateTime())) {
                v.setCreateTimeText(v.getCreateTime().format(DatePattern.NORM_DATETIME_FORMATTER));
            }
            if (Objects.nonNull(v.getTaskBeginTime())) {
                v.setTaskBeginTimeText(v.getTaskBeginTime().format(DatePattern.NORM_DATETIME_FORMATTER));
            }
            if (Objects.nonNull(v.getTaskEndTime())) {
                v.setTaskEndTimeText(v.getTaskEndTime().format(DatePattern.NORM_DATETIME_FORMATTER));
            }
            EntMatPlanCounter counter = counterMap.getOrDefault(v.getId(), defaultCounter);
            v.setEntCount(counter.getEntCount());
            v.setSrPrCount(counter.getSrPrCount());
        });
        try {
            LinkedHashMap<String, String> excelHead = ExcelUtils.buildExcelHead(ExportEntMatPlan.class);
            // excel 为空就生成一个空文件
            String fileName = "设备保养计划-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (export.getPageNum() > 1) {
                fileName += "-" + export.getPageNum();
            }
            ExcelUtils.export(response, new ExcelExportDto(excelHead, list), fileName + ".xlsx");
        } catch (Exception e) {
            log.warn("设备保养计划导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("设备保养计划中已选择的设备数据分页搜索")
    @PostMapping("/pageSelectedEntMatData")
    public PageUtils<JSONObject> pageSelectedEntMatData(@RequestBody @Validated GetSelectedEntData get) {
        FormVO meanForm = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        PageUtils<EntMatPlanEntData> page = planFeignClient.pageSelectedEntMatData(get);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> json = page.getList().stream().map(EntMatPlanEntData::translate).collect(Collectors.toList());
        assetUti.translateAssetJsonBatch(json, meanForm.getFormFields());
        desensitizationDataUtil.handleSensitiveField(json, SensitiveObjectTypeEnum.ASSET.getCode());
        return new PageUtils<>(json, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation("设备保养计划中已选择的备件数据分页搜索")
    @PostMapping("/pageSelectedEntSrPrData")
    public PageUtils<JSONObject> pageSelectedEntSrPrData(@RequestBody @Validated GetSelectedSrPrData get) {
        FormVO materialForm = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        PageUtils<EntMatPlanSrPrData> page = planFeignClient.pageSelectedEntSrPrData(get);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> json = page.getList().stream().map(EntMatPlanSrPrData::translate).collect(Collectors.toList());
        materialUtil.translateMaterialJsonBatch(json, materialForm.getFormFields());
        desensitizationDataUtil.handleSensitiveField(json, SensitiveObjectTypeEnum.MATERIAL.getCode());
        return new PageUtils<>(json, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

}
