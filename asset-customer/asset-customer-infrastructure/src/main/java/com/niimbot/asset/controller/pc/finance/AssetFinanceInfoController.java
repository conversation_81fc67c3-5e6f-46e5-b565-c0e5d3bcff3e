package com.niimbot.asset.controller.pc.finance;


import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetFinanceService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.finance.AssetFinanceInfoFeignClient;
import com.niimbot.finance.AlterLogQueryDto;
import com.niimbot.finance.AssetFinanceAlterLogDto;
import com.niimbot.finance.AssetFinanceDefaultDto;
import com.niimbot.finance.AssetFinanceInfoConfigDto;
import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.finance.AssetFinanceInfoQueryDto;
import com.niimbot.finance.AssetInitFinanceInfoDto;
import com.niimbot.finance.AssetMachineAccountDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.ImportDto;
import com.niimbot.validate.group.Update;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/15 下午4:46
 */
@Slf4j
@Api(tags = "资产入账")
@ResultController
@RequestMapping("api/finance/bill/")
@RequiredArgsConstructor
public class AssetFinanceInfoController {

    private final AssetFinanceInfoFeignClient financeInfoFeignClient;
    private final AssetFinanceService assetFinanceService;
    private final RedisService redisService;

    @ApiOperation("获取资产入账默认值信息")
    @GetMapping("defaultValue/{assetId}")
    public AssetFinanceDefaultDto assetDefaultValue(@PathVariable Long assetId) {
        return financeInfoFeignClient.assetDefaultValue(assetId);
    }

    @ApiOperation("保存资产入账信息")
    @PostMapping("config")
    public Boolean saveConfig(@RequestBody @Validated AssetFinanceInfoConfigDto configDto) {
        return financeInfoFeignClient.saveConfig(configDto);
    }

    @ApiOperation("获取资产入账信息详情")
    @GetMapping("detail/{bizCode}")
    public AssetFinanceInfoConfigDto detail(@PathVariable String bizCode) {
        if (StrUtil.isBlank(bizCode)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID);
        }

        return financeInfoFeignClient.detail(bizCode);
    }

    @ApiOperation("编辑资产入账信息")
    @PostMapping("edit")
    public Boolean editConfig(@RequestBody @Validated(Update.class) AssetFinanceInfoConfigDto configDto) {
        return financeInfoFeignClient.editConfig(configDto);
    }

    @ApiOperation("变更资产入账信息")
    @PostMapping("alter")
    public Boolean alterConfig(@RequestBody @Validated(Update.class) AssetFinanceInfoConfigDto configDto) {
        return financeInfoFeignClient.alterConfig(configDto);
    }

    @ApiOperation("资产变更日志分页列表")
    @GetMapping("pageAlterLog")
    @AutoConvert
    public PageUtils<AssetFinanceAlterLogDto> pageAlterLog(@Validated AlterLogQueryDto queryDto) {
        return financeInfoFeignClient.pageAlterLog(queryDto);
    }

    @ApiOperation("反入账")
    @PostMapping("resetConfig")
    public Boolean resetConfig(@RequestBody AssetFinanceInfoConfigDto configDto) {
        return financeInfoFeignClient.resetConfig(configDto);
    }

    @ApiOperation("导出资产入账信息模板")
    @GetMapping("downloadFinanceTemplate")
    public ExportResponse downloadFinanceTemplate(@ApiParam(name = "orgId", value = "组织id") @RequestParam(value = "orgId", required = true) Long orgId) {
        return assetFinanceService.exportWaitAsset(orgId);
    }

    @ApiOperation(value = "资产入账导入")
    @PostMapping(value = "/importFinanceData", consumes = "multipart/form-data")
    public void importTemplate(@RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isBlank(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "文件名超长");
        }
        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        try (InputStream stream = file.getInputStream()) {
            //解析excel数据导入
            assetFinanceService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(), companyId);
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error("assetFinanceController importTemplate error! exception ", e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return assetFinanceService.importError(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        assetFinanceService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.ASSET_FINANCE));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

    @ApiOperation("资产入账列表")
    @PostMapping("queryFinanceInfo")
    public PageUtils<AssetFinanceInfoDto> queryFinanceInfo(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto) {
        return financeInfoFeignClient.queryFinanceInfo(queryDto);
    }

    @ApiOperation("资产初始入账列表")
    @PostMapping("queryInitFinanceInfo")
    public PageUtils<AssetInitFinanceInfoDto> queryInitFinanceInfo(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto) {
        return financeInfoFeignClient.queryInitFinanceInfo(queryDto);
    }

    @ApiOperation("资产初始入账列表导出")
    @PostMapping("/queryInitFinanceInfoExport")
    public void accumulatedUsedDetailsExport(@RequestBody @Validated AssetFinanceInfoQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(AssetInitFinanceInfoDto.class);
            // 查询数据
            PageUtils<AssetInitFinanceInfoDto> details = financeInfoFeignClient.queryInitFinanceInfo(query);
            List<AssetInitFinanceInfoDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "资产初始入账-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getCurrent() > 1) {
                fileName += "-" + query.getCurrent();
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation("资产财务台账")
    @PostMapping("queryMachineAccount")
    public PageUtils<AssetMachineAccountDto> queryMachineAccount(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto) {
        return financeInfoFeignClient.queryMachineAccount(queryDto);
    }

    @ApiOperation("资产财务台账导出")
    @PostMapping("queryMachineAccountExport")
    public void queryMachineAccountExport(@RequestBody @Validated AssetFinanceInfoQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(AssetMachineAccountDto.class);
            // 查询数据
            PageUtils<AssetMachineAccountDto> details = financeInfoFeignClient.queryMachineAccount(query);
            List<AssetMachineAccountDto> excel = CollUtil.isEmpty(details.getList()) ? Collections.emptyList() : details.getList();
            // excel 为空就生成一个空文件
            String fileName = "资产财务台账-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (query.getCurrent() > 1) {
                fileName += "-" + query.getCurrent();
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, excel), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
