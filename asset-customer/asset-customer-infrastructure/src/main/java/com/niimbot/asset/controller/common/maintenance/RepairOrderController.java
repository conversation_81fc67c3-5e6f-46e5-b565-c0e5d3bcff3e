package com.niimbot.asset.controller.common.maintenance;

import com.google.common.collect.ImmutableSet;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MaintenanceResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.OrderJsonUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.OrderService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.AsOrderFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.RepairOrderFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.maintenance.RepairOrderDetailDto;
import com.niimbot.maintenance.RepairOrderDetailPageQueryDto;
import com.niimbot.maintenance.RepairOrderDto;
import com.niimbot.maintenance.RepairOrderFinishDto;
import com.niimbot.maintenance.RepairOrderResponseDto;
import com.niimbot.maintenance.RepairOrderShowDto;
import com.niimbot.maintenance.RepairOrderSubmitDto;
import com.niimbot.means.AsOrderAssetDto;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 维修单
 *
 * <AUTHOR>
 * @date 2021/6/25 14:36
 */
@Slf4j
@Api(tags = "维修单管理")
@ResultController
@RequestMapping("api/common/maintenance/repair")
@RequiredArgsConstructor
public class RepairOrderController {
    private final RepairOrderFeignClient repairOrderFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;
    private final AssetService assetService;
    private final AsOrderUtil orderUtil;
    private final DictConvertUtil dictConvertUtil;
    private final OrderService orderService;
    private final AsOrderFeignClient orderFeignClient;
    private final CacheResourceUtil cacheResourceUtil;
    @Autowired
    protected FormFeignClient formFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @Autowired
    private RedisService redisService;

    private ImmutableSet<Integer> allowed = ImmutableSet.<Integer>builder()
            .add(AssetConstant.ASSET_STATUS_IDLE)
            .add(AssetConstant.ASSET_STATUS_USING)
            .add(AssetConstant.ASSET_STATUS_BORROW)
            .add(AssetConstant.ASSET_STATUS_WAIT_SERVICE)
            .build();

    @ApiOperation(value = "设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated RepairOrderDto dto) {
        return repairOrderFeignClient.getWorkflowStepList(dto);
    }

    @ApiOperation(value = "创建维修单")
    @PostMapping
    @RepeatSubmit
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean create(@RequestBody @Validated RepairOrderSubmitDto dto) {
        List<Long> assetIds = dto.getOrderDto().getAssets().stream().map(RepairOrderDetailDto::getId)
                .collect(Collectors.toList());
        if (assetIds.size() > AssetConstant.ORDER_ASSET_MAX) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_MAX_UPPER_LIMIT,
                    String.valueOf(AssetConstant.ORDER_ASSET_MAX));
        }
        Map<Long, JSONObject> assetMap = assetService.getInfoMap(assetIds);
        if (CollUtil.isNotEmpty(assetMap) && assetMap.values().size() != assetIds.size()) {
            throw new BusinessException(SystemResultCode.ORDER_ASSET_LIST_NOT_EXISTS);
        }
        this.checkAssetStatus(assetMap.values(), dto.getOrderDto().getOrderData());
        dto.getOrderDto().getAssets().forEach(detailDto -> {
            JSONObject data = assetMap.get(detailDto.getId());
            detailDto.setAssetSnapshotData(data);
        });
        dto.getOrderDto().setSummary(orderUtil.buildSummary(assetMap.values()));
        dto.getOrderDto().setTotalRepairMoney(this.resolveTotalRepairMoney(dto.getOrderDto().getAssets()));
        AuditableCreateOrderResult result = repairOrderFeignClient.create(dto);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.OR_MAS_WX, result));
        return Objects.nonNull(result) && result.successful();
    }

    @ApiOperation(value = "完成维修")
    @PutMapping("/repairFinish")
    @RepeatSubmit
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean repairFinish(@RequestBody @Validated RepairOrderFinishDto dto) {
        if (Objects.nonNull(dto.getFinishTimestamp())) {
            Instant instant = Instant.ofEpochMilli(dto.getFinishTimestamp());
            dto.setFinishTime(LocalDateTime.ofInstant(instant, ZoneId.systemDefault()));
        }
        Boolean result = repairOrderFeignClient.repairFinish(dto);
        if (result) {
            AuditLogs.sendRecord(LoginUserThreadLocal.get(), loginUser -> {
                RepairOrderDto orderDto = repairOrderFeignClient.getById(dto.getRepairOrderId());
                String content = "维修完成：资产维修单" + orderDto.getOrderNo();
                return AuditLogRecord.create(Auditable.Action.OR_MAS_WX_FINISH, Collections.singletonMap(Auditable.Tpl.CONTENT, content), loginUser);
            });
        }
        return result;
    }

    @ApiOperation(value = "维修单详情")
    @GetMapping("/{id}")
    @AutoConvert
    public RepairOrderResponseDto getById(@PathVariable("id") Long id) {
        RepairOrderDto orderDto = repairOrderFeignClient.getById(id);
        if (ObjectUtil.isNull(orderDto)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "维修单据数据不存在");
        }
        dictConvertUtil.convertToDictionary(orderDto);
        //数据脱敏处理
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_REPAIR);
        JSONObject orderInfo = orderUtil.toJSONObject(orderDto);
        desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderInfo), SensitiveObjectTypeEnum.ASSET.getCode());
        if (orderDto.getApproveStatus() == 0) {
            return new RepairOrderResponseDto()
                    .setOrder(orderInfo)
                    .setApproveInfo(new WorkflowApproveInfoDto());
        }

        WorkflowApproveInfoDto approveInfoDto = null;
        if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
            approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_REPAIR, id);
            if (approveInfoDto == null || approveInfoDto.getExecuteList().isEmpty()) {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_REPAIR, id);
            }
        } else {
            approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_REPAIR, id);
            if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                approveInfoDto.getExecuteList().stream()
                        .filter(step -> step.getType() == 8)
                        .forEach(step -> {
                            step.setStatus(orderDto.getApproveStatus());
                            dictConvertUtil.convertToDictionary(step);
                        });
            }
        }
        return new RepairOrderResponseDto()
                .setOrder(orderInfo)
                .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
    }

    @ApiOperation(value = "维修单批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<RepairOrderResponseDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_REPAIR);
        return requestDto.getOrderIds().stream().map(this::getById).peek(v -> {
            // 计算维修时长
            JSONObject data = v.getOrder();
            if (!data.containsKey("assets") || !data.containsKey("repairFinishDate")) {
                return;
            }
            LocalDateTime repairFinishDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(data.getLong("repairFinishDate")), ZoneId.systemDefault());
            JSONArray assets = data.getJSONArray("assets");
            for (int i = 0; i < assets.size(); i++) {
                JSONObject details = assets.getJSONObject(i);
                if (details.containsKey("finishTime") && Objects.nonNull(details.getLong("finishTime")) && details.getLong("finishTime") > 0L) {
                    LocalDateTime finishTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(details.getLong("finishTime")), ZoneId.systemDefault());
                    // 30分钟以上记一小时以下不计
                    details.put("repairHour", Duration.between(repairFinishDate, finishTime).toHours());
                }
            }
            data.put("totalNum", assets.size());
        }).collect(Collectors.toList());
    }

    @ApiOperation(value = "维修单明细详情")
    @GetMapping("/detail/{orderId}/{assetId}")
    public JSONObject getDetail(@PathVariable("orderId") Long orderId, @PathVariable("assetId") Long assetId) {
        RepairOrderDetailDto detailDto = repairOrderFeignClient.getDetail(orderId, assetId);
        if (detailDto == null) {
            throw new BusinessException(MaintenanceResultCode.REPAIR_ORDER_DETAIL_NOT_EXIST);
        }
        JSONObject result = orderUtil.toJSONObject(detailDto);
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.ASSET.getCode());
        return result;
    }

    @ApiOperation(value = "维修单分页")
    @PostMapping("/page")
    @AutoConvert
    public PageUtils<JSONObject> page(@RequestBody AsOrderQueryDto query) {
        query.setOrderType(AssetConstant.ORDER_TYPE_REPAIR);
        PageUtils<AsOrderDto> page = repairOrderFeignClient.page(query);
        page.getList().forEach(dto -> dto.setOrderType(AssetConstant.ORDER_TYPE_REPAIR));
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (AsOrderDto orderDto : page.getList()) {
            list.add(orderUtil.toJSONObject(orderDto));
        }
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.ASSET.getCode());
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "维修单分页")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<JSONObject> pageGet(AsOrderQueryDto dto) {
        return page(dto);
    }

    @ApiOperation(value = "维修单明细分页")
    @GetMapping("/pageDetail")
    public PageUtils<JSONObject> pageDetail(@Validated RepairOrderDetailPageQueryDto query) {
        PageUtils<RepairOrderDetailDto> page = repairOrderFeignClient.pageDetail(query);
        List<JSONObject> list = new ArrayList<>();
        for (RepairOrderDetailDto detailDto : page.getList()) {
            JSONObject data = orderUtil.toJSONObject(detailDto);
            //数据脱敏处理
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(data), SensitiveObjectTypeEnum.ASSET.getCode());
            list.add(data);
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    private void checkAssetStatus(Collection<JSONObject> assets, JSONObject orderData) {
        assets.forEach(asset -> {
            if ("无需维修".equals(orderData.getString("repairStatus"))) {
                if (!AssetConstant.ASSET_STATUS_WAIT_SERVICE.equals(asset.getInteger("status"))) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "无需维修单据仅允许选择待维修资产");
                }
            } else {
                if (!allowed.contains(asset.getInteger("status"))) {
                    throw new BusinessException(MaintenanceResultCode.REPAIR_REPORT_ASSET_STATUS_ILLEGAL, "维修");
                }
            }
        });
    }

    private BigDecimal resolveTotalRepairMoney(List<RepairOrderDetailDto> details) {
        return details.stream()
                .map(detail ->
                        detail.getRepairMoney() == null ? BigDecimal.ZERO : detail.getRepairMoney())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @ApiOperation(value = "【PC】导出资产维修单据")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrder(@RequestBody AsOrderQueryDto query) {
        return orderService.exportRepairOrderCard(query);
    }

    @ApiOperation(value = "【PC】导出资产维修单据资产")
    @PostMapping(value = "/exportOrder/assets")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderAssets(@RequestBody @Validated AsOrderQueryDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_REPAIR);
        PageUtils<AsOrderDto> page = repairOrderFeignClient.page(dto);
        List<AsOrderDto> list = page.getList();
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        list.forEach(o -> o.setOrderType(AssetConstant.ORDER_TYPE_REPAIR));
        dictConvertUtil.convertToDictionary(list);
        FormVO orderForm = orderFeignClient.getForm(dto.getOrderType());
        Map<String, String> orderDateFormatType = new HashMap<>();
        Set<String> orderMultiSelectSet = new HashSet<>();
        Map<String, Boolean> orderNumberMap = new HashMap<>();
        for (FormFieldCO it : orderForm.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                orderDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                orderMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                orderNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        orderDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        orderDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        List<Long> orderIds = new ArrayList<>();
        Map<Long, AsOrderDto> orderDtoMap = new HashMap<>(list.size());
        List<JSONObject> orders = new ArrayList<>();
        for (AsOrderDto order : list) {
            orderDtoMap.put(order.getId(), order);
            orderIds.add(order.getId());
            JSONObject orderJson = orderUtil.toJSONObject(order);
            if (Objects.nonNull(order.getCreateTime())) {
                long timeVal = order.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("createTime", timeVal);
                }
            }
            if (Objects.nonNull(order.getUpdateTime())) {
                long timeVal = order.getUpdateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("updateTime", timeVal);
                }
            }
            orderNumberMap.forEach((code, percentage) -> {
                Number number = Convert.toNumber(orderJson.get(code));
                if (ObjectUtil.isNotNull(number)) {
                    if (BooleanUtil.isTrue(percentage)) {
                        orderJson.put(code, number + "%");
                    } else {
                        orderJson.put(code, number);
                    }
                }
            });
            orderDateFormatType.forEach((code, fmt) -> {
                String date = orderJson.getString(code);
                if (StrUtil.isNotEmpty(date)) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                        orderJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换时间异常", code, date);
                    }
                }
            });
            orderMultiSelectSet.forEach(code -> {
                try {
                    JSONArray jsonArray = orderJson.getJSONArray(code);
                    if (CollUtil.isNotEmpty(jsonArray)) {
                        List<String> strings = jsonArray.toJavaList(String.class);
                        String collect = String.join(",", strings);
                        orderJson.put(code, collect);
                    } else {
                        orderJson.put(code, null);
                    }
                } catch (Exception e) {
                    log.warn("[{}] [{}]转换数组异常", code, orderJson.get(code));
                }
            });
            orders.add(orderJson);
        }

        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(orders, SensitiveObjectTypeEnum.ASSET.getCode());

        OrderJsonUtil.convertJsonKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, orders);

        List<AsOrderAssetDto> assets = repairOrderFeignClient.getAssetsByOrderId(orderIds);
        FormVO assetFormVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        Map<String, String> assetDateFormatType = new HashMap<>();
        Set<String> assetMultiSelectSet = new HashSet<>();
        Map<String, Boolean> assetNumberMap = new HashMap<>();
        for (FormFieldCO it : assetFormVO.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                assetDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                assetMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                assetNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        assetDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        assetDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        assetDateFormatType.put("finishTimeToLong", "yyyy-MM-dd HH:mm:ss");
        Map<Long, List<AsOrderAssetDto>> assetsMap = assets.stream().collect(Collectors.groupingBy(AsOrderAssetDto::getOrderId));

        Map<Long, List<JSONObject>> assetsJsonMap = new HashMap<>();

        Map<String, String> translationCodeMap = assetFormVO.getFormFields().stream()
                .filter(f -> StrUtil.isNotEmpty(f.getTranslationCode()))
                .collect(Collectors.toMap(FormFieldCO::getTranslationCode, FormFieldCO::getFieldCode));

        Map<Long, String> empNameCache = new ConcurrentHashMap<>();

        assetsMap.forEach((key, assetDtoList) -> {
            List<JSONObject> assetJsons = new ArrayList<>();
            for (AsOrderAssetDto assetDto : assetDtoList) {
                JSONObject assetJson = orderUtil.toJSONObject(assetDto);
                translationCodeMap.forEach((k, v) -> assetJson.put(v, assetJson.get(k)));
                // 特殊处理创建人，不在表单内
                if (assetJson.containsKey(AssetConstant.ASSET_FIELD_CREATE_BY)) {
                    Long createBy = assetJson.getLong(AssetConstant.ASSET_FIELD_CREATE_BY);
                    if (empNameCache.containsKey(createBy)) {
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, empNameCache.get(createBy));
                    } else {
                        String userNameAndCode = cacheResourceUtil.getUserNameAndCode(createBy);
                        assetJson.put(AssetConstant.ASSET_FIELD_CREATE_BY, userNameAndCode);
                        empNameCache.put(createBy, userNameAndCode);
                    }
                }
//                assetUtil.translateAssetJsonView(assetJson, assetFormVO.getFormFields());
                assetNumberMap.forEach((code, percentage) -> {
                    Number number = Convert.toNumber(assetJson.get(code));
                    if (ObjectUtil.isNotNull(number)) {
                        if (BooleanUtil.isTrue(percentage)) {
                            assetJson.put(code, number + "%");
                        } else {
                            assetJson.put(code, number);
                        }
                    }
                });
                assetDateFormatType.forEach((code, fmt) -> {
                    String date = assetJson.getString(code);
                    if (StrUtil.isNotEmpty(date)) {
                        try {
                            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                            assetJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                        } catch (Exception e) {
                            log.warn("[{}] [{}]转换时间异常", code, date);
                        }
                    }
                });
                assetMultiSelectSet.forEach(code -> {
                    try {
                        JSONArray jsonArray = assetJson.getJSONArray(code);
                        if (CollUtil.isNotEmpty(jsonArray)) {
                            List<String> strings = jsonArray.toJavaList(String.class);
                            String collect = String.join(",", strings);
                            assetJson.put(code, collect);
                        } else {
                            assetJson.put(code, null);
                        }
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换数组异常", code, assetJson.get(code));
                    }
                });
                // 维修时长数据填充
                if (orderDtoMap.containsKey(key)) {
                    JSONObject orderData = orderDtoMap.get(key).getOrderData();
                    if (orderData.containsKey("repairFinishDate") && Objects.nonNull(orderData.getLong("repairFinishDate")) && assetJson.containsKey("finishTimeToLong") && Objects.nonNull(assetJson.getLong("finishTimeToLong"))) {
                        LocalDateTime repairFinishDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(orderData.getLong("repairFinishDate")), ZoneId.systemDefault());
                        LocalDateTime finishTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(assetJson.getLong("finishTime")), ZoneId.systemDefault());
                        long minutes = Duration.between(repairFinishDate, finishTime).toMinutes();
                        // 30分钟以上记一小时以下不计
                        assetJson.put("repairHour", Duration.between(repairFinishDate, finishTime).toHours());
                    }
                }

                assetJsons.add(assetJson);
            }
            assetsJsonMap.put(key, assetJsons);
        });

        return orderService.exportOrderAssets(dto, orders, assetsJsonMap);
    }

    @ApiOperation(value = "编辑待维修资产优先展示")
    @PostMapping("/updateSwitch")
    public void updateSwitch(@RequestBody AssetQueryConditionDto dto) {
        String showRepairedKey = "showRepaired:" + LoginUserThreadLocal.getCurrentUserId();
        redisService.set(showRepairedKey, dto.getShowRepaired());
    }

    @ApiOperation(value = "待维修资产优先展示状态")
    @GetMapping("/getShowRepaired")
    public RepairOrderShowDto getShowRepaired() {
        String showRepairedKey = "showRepaired:" + LoginUserThreadLocal.getCurrentUserId();
        RepairOrderShowDto dto = new RepairOrderShowDto();
        dto.setShowRepaired((Boolean) redisService.get(showRepairedKey));
        return dto;
    }
}
