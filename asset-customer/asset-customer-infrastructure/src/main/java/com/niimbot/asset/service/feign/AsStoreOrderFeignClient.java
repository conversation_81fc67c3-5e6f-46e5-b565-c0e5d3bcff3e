package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 10:47
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AsStoreOrderFeignClient {
    /**
     * 创建维修单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "server/means/storeOrder")
    AuditableCreateOrderResult create(@RequestBody AsStoreOrderSubmitDto dto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/means/storeOrder/{id}")
    AsStoreOrderDto getById(@PathVariable("id") Long id);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/means/storeOrder/page")
    PageUtils<AsStoreOrderDto> page(@RequestBody AsOrderQueryDto query);

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "server/means/storeOrder/list")
    List<AsStoreOrderDto> list(@RequestBody AsOrderQueryDto query);

    /**
     * 汇总明细查询
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "server/means/storeOrder/listSummaryDetailsByOrderId")
    List<AsStoreOrderSummaryDetailDto> listSummaryDetailsByOrderId(@RequestBody Collection<Long> ids);

    /**
     * 汇总明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "server/means/storeOrder/pageSummaryDetail")
    PageUtils<AsStoreOrderSummaryDetailDto> pageSummaryDetail(@SpringQueryMap StoreDetailPageQueryDto dto);

    /**
     * 资产明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "server/means/storeOrder/pageAssetDetail")
    PageUtils<AsStoreOrderAssetDetailDto> pageAssetDetail(@SpringQueryMap StoreDetailPageQueryDto dto);

    @PostMapping(value = "server/means/storeOrder/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(AsStoreOrderDto dto);
}
