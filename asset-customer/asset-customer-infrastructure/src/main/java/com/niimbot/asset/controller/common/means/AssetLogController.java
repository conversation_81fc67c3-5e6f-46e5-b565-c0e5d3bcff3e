package com.niimbot.asset.controller.common.means;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.AsAssetLogFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AsAssetLogDto;
import com.niimbot.means.AsAssetLogQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import java.util.List;

/**
 * 资产日志控制器
 *
 * <AUTHOR>
 * @since 2020-12-25
 */
@Api(tags = "资产履历管理")
@ResultController
@RequestMapping("api/common/assetLog")
@RequiredArgsConstructor
@Validated
public class AssetLogController {

    private final AsAssetLogFeignClient logFeignClient;

    @ApiOperation(value = "履历分页列表")
    @AutoConvert
    @GetMapping("/page")
    public PageUtils<AsAssetLogDto> page(@Valid AsAssetLogQueryDto dto) {
        return logFeignClient.page(dto);
    }

    @ApiOperation(value = "履历列表")
    @AutoConvert
    @GetMapping("/list")
    public List<AsAssetLogDto> list(@Valid AsAssetLogQueryDto dto) {
        return logFeignClient.list(dto);
    }
}
