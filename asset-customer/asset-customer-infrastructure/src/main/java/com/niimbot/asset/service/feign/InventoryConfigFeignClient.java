package com.niimbot.asset.service.feign;

import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.InventoryConfigDto;
import com.niimbot.inventory.InventorySettingAddListDto;
import com.niimbot.inventory.InventorySettingDto;
import com.niimbot.inventory.InventoryStDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/12 11:50
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface InventoryConfigFeignClient {

    /**
     * 盘点单详细配置数据
     *
     * @param id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/config/{id}")
    InventoryConfigDto getConfigById(@PathVariable("id") Long id);

    /**
     * 盘点策略
     */
    @GetMapping(value = "server/inventory/config/strategy")
    List<InventoryStDto> getStrategy();

    @GetMapping("server/inventory/setting/list/{type}")
    List<InventorySettingDto> settingList(@PathVariable("type") Integer type);

    @PostMapping("server/inventory/setting")
    Boolean save(InventorySettingAddListDto addListDto);

    @GetMapping("server/inventory/setting/attrList/{type}")
    FormVO inventoryAttrList(@PathVariable("type") String type);
}
