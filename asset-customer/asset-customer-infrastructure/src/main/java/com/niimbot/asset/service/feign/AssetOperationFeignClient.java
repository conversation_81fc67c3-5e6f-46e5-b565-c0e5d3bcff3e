package com.niimbot.asset.service.feign;

import com.niimbot.means.AssetOperationDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/12 16:25
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetOperationFeignClient {

    /**
     * 查询资产操作集合列表
     *
     * @return 组织列表
     */
    @GetMapping(value = "server/means/assetOperation/list")
    List<AssetOperationDto> list();

}
