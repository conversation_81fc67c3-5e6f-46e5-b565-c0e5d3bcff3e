package com.niimbot.asset.controller.pc.means;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.PrintTagService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.PrintPdfDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 打印公共控制器
 *
 * <AUTHOR>
 */
@Api(tags = "【PC】打印公共管理")
@ResultController
@RequestMapping("api/pc/print")
public class PrintPcController {

    @Resource
    private PrintFeignClient printFeignClient;

    @Resource
    private AbstractFileUtils fileUtils;

    @Resource
    private PrintTagService printTagService;

    /**
     * 获取打印机系列
     *
     * @return 系列printerType serialName
     */
    @ApiOperation(value = "获取打印机系列")
    @GetMapping("/printerSerial")
    public List<Map<String, Object>> getPrinterSerial() {
        List<Map<String, Object>> maps = printFeignClient.listPrinter();
        for (Map<String, Object> map : maps) {
            String pictureUrl = (String) map.getOrDefault("pictureUrl", "");
            pictureUrl = fileUtils.convertToDownloadUrl(pictureUrl);
            map.put("pictureUrl", pictureUrl);
            Object list = map.getOrDefault("list", new ArrayList<>());
            JSONArray array = JSON.parseArray(JSON.toJSONString(list));
            for (int i = 0; i < array.size(); i++) {
                JSONObject o = array.getJSONObject(i);
                String raw = o.getString("pictureUrl");
                o.put("pictureUrl", fileUtils.convertToDownloadUrl(raw));
            }
            map.put("list", array);
        }
        return maps;
    }

    /**
     * 设置用户默认打印机系列
     *
     * @return Boolean
     */
    @ApiOperation(value = "设置用户默认打印机系列")
    @PutMapping("/setDefaultSeries/{id}")
    @ResultMessage("设置默认打印机成功")
    public Boolean setDefaultSeries(@PathVariable("id") Long id) {
        return printFeignClient.setDefaultSeries(id);
    }

    @ApiOperation(value = "导出打印标签PDF")
    @PostMapping(value = "/exportTagPdf")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportTagPdf(@RequestBody @Validated PrintPdfDto dto) {
        return printTagService.exportTagPdf(dto);
    }

}
