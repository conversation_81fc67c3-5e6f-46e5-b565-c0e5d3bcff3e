package com.niimbot.asset.controller.common.material;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.MaterialOrderService;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.MaterialRkOrderFeignClient;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.material.MaterialOrderResponseDto;
import com.niimbot.material.MaterialRkOrderDetailDto;
import com.niimbot.material.MaterialRkOrderDto;
import com.niimbot.material.MaterialRkOrderSubmitDto;
import com.niimbot.system.Auditable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/7/12 13:49
 */
@Slf4j
@Api(tags = "【耗材】单据")
@ResultController
@RequestMapping("api/common/material/order/rk")
@Validated
public class MaterialRkOrderController {

    private final MaterialRkOrderFeignClient materialRkOrderFeignClient;

    private final ActWorkflowFeignClient workflowFeignClient;

    private final AsMaterialUtil materialUtil;

    private final DictConvertUtil dictConvertUtil;

    private final MaterialService materialService;

    private final MaterialOrderService materialOrderService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @Autowired
    public MaterialRkOrderController(MaterialRkOrderFeignClient materialRkOrderFeignClient,
                                     ActWorkflowFeignClient workflowFeignClient,
                                     AsMaterialUtil materialUtil,
                                     DictConvertUtil dictConvertUtil,
                                     MaterialService materialService,
                                     MaterialOrderService materialOrderService) {
        this.materialRkOrderFeignClient = materialRkOrderFeignClient;
        this.workflowFeignClient = workflowFeignClient;
        this.materialUtil = materialUtil;
        this.dictConvertUtil = dictConvertUtil;
        this.materialService = materialService;
        this.materialOrderService = materialOrderService;
    }

    @ApiOperation(value = "【入库单】设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated MaterialRkOrderDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_RK);
        return materialRkOrderFeignClient.getWorkflowStepList(dto);
    }

    @ApiOperation(value = "【入库单】入库单新增")
    @PostMapping
    @RepeatSubmit
    @AuditLog(Auditable.Action.OR_MRL_RK)
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public AuditableCreateOrderResult create(@RequestBody @Validated MaterialRkOrderSubmitDto submitDto) {
        MaterialRkOrderDto orderDto = submitDto.getOrderDto();
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_RK);
        // 校验单据
        materialUtil.verifyAndTranslation(orderDto, AssetConstant.ORDER_TYPE_MATERIAL_RK);
        // 查询耗材信息
        List<Long> materialIds = orderDto.getMaterials().stream().map(MaterialRkOrderDetailDto::getMaterialId).collect(Collectors.toList());
        Map<Long, JSONObject> infoMap = materialService.getInfoMap(materialIds);
        orderDto.getMaterials().forEach(detailDto -> {
            JSONObject data = infoMap.get(detailDto.getMaterialId());
            detailDto.setMaterialSnapshotData(data);
        });

        // 总种类
        int totalType = 0;
        // 总数量
        BigDecimal totalNum = BigDecimal.ZERO;
        // 总金额
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (MaterialRkOrderDetailDto m : orderDto.getMaterials()) {
            totalType += 1;
            totalNum = totalNum.add(m.getRkNum());
            // 计算单价
            BigDecimal rkUnitPrice = m.getRkPrice().divide(m.getRkNum(), 4, RoundingMode.HALF_UP);
            if (BigDecimal.ZERO.compareTo(m.getRkPrice()) != 0 && BigDecimal.ZERO.compareTo(rkUnitPrice) == 0) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION,
                        m.getMaterialSnapshotData().getString("materialName") + "单价" + rkUnitPrice + "不支持，请重新设置");
            } else if (rkUnitPrice.compareTo(new BigDecimal("999999999999.9999")) > 0) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION,
                        m.getMaterialSnapshotData().getString("materialName") + "单价" + rkUnitPrice + "不支持，请重新设置");
            }
            m.setRkUnitPrice(rkUnitPrice);
            totalPrice = totalPrice.add(m.getRkPrice());
        }

        orderDto.setTotalNum(totalNum).setTotalType(totalType).setTotalPrice(totalPrice);

        List<JSONObject> sortList = materialIds.stream().map(infoMap::get).collect(Collectors.toList());
        orderDto.setSummary(materialUtil.buildSummary(sortList));
        return materialRkOrderFeignClient.create(submitDto);
    }

    @ApiOperation(value = "【入库单】入库单详情")
    @GetMapping("/{id}")
    @AutoConvert
    public MaterialOrderResponseDto getById(@PathVariable Long id) {
        MaterialRkOrderDto orderDto = materialRkOrderFeignClient.getById(id);
        if (ObjectUtil.isNotNull(orderDto)) {
            dictConvertUtil.convertToDictionary(orderDto);
            orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_RK);
            // 翻译耗材
            JSONObject orderJson = materialUtil.toJSONObject(orderDto);
            //数据脱敏处理
            if (Objects.nonNull(orderJson)) {
                desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderJson), SensitiveObjectTypeEnum.MATERIAL.getCode());
            }
            if (orderDto.getApproveStatus() == 0) {
                return new MaterialOrderResponseDto()
                        .setOrder(orderJson)
                        .setApproveInfo(new WorkflowApproveInfoDto());
            }
            WorkflowApproveInfoDto approveInfoDto;
            if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_RK, id);
                if (approveInfoDto == null || approveInfoDto.getExecuteList().isEmpty()) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_RK, id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_RK, id);
                if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(orderDto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
            return new MaterialOrderResponseDto()
                    .setOrder(orderJson)
                    .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
        } else {
            throw new BusinessException(MaterialResultCode.ORDER_NOT_EXISTS, "入库单" + id);
        }
    }

    @ApiOperation(value = "【入库单】入库单批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<MaterialOrderResponseDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_RK);
        return requestDto.getOrderIds().stream().map(this::getById).collect(Collectors.toList());
    }

    @ApiOperation(value = "【入库单】入库单明细详情")
    @GetMapping("/detail/{orderId}/{materialId}")
    public JSONObject getDetail(@PathVariable("orderId") Long orderId,
                                @PathVariable("materialId") Long materialId) {
        MaterialRkOrderDetailDto detailDto = materialRkOrderFeignClient.getDetail(orderId, materialId);
        if (detailDto == null) {
            throw new BusinessException(MaterialResultCode.ORDER_DETAIL_NOT_EXIST);
        }
        JSONObject result = materialUtil.toJSONObject(detailDto);
        //数据脱敏处理
        if (Objects.nonNull(result)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return result;
    }

    @ApiOperation(value = "【入库单】入库单分页")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody @Validated MaterialOrderQueryDto query) {
//        resolveDate(query);
        PageUtils<MaterialRkOrderDto> page = materialRkOrderFeignClient.page(query);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (MaterialRkOrderDto orderDto : page.getList()) {
            orderDto.setOrderType(OrderFormTypeEnum.RK.getCode());
            //数据脱敏处理
//            if (CollUtil.isNotEmpty(orderDto.getMaterials())) {
//                desensitizationDataUtil.handleSensitiveField(orderDto.getMaterials()
//                        .stream()
//                        .map(MaterialRkOrderDetailDto::getMaterialSnapshotData)
//                        .collect(Collectors.toList()), SensitiveObjectTypeEnum.MATERIAL.getCode());
//            }
            list.add(materialUtil.toJSONObject(orderDto));
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【入库单】入库单分页")
    @GetMapping("/page")
    public PageUtils<JSONObject> pageGet(MaterialOrderQueryDto dto) {
        return page(dto);
    }

    @ApiOperation(value = "【入库单】入库单耗材详情")
    @GetMapping("/detail/page")
    public PageUtils<JSONObject> materialPage(@Validated MaterialOrderDetailQueryDto dto) {
        PageUtils<MaterialRkOrderDetailDto> page = materialRkOrderFeignClient.pageDetail(dto);
        List<JSONObject> list = new ArrayList<>();
        for (MaterialRkOrderDetailDto detailDto : page.getList()) {
            JSONObject jsonObject = materialUtil.toJSONObject(detailDto);
            list.add(jsonObject);
        }
        //数据脱敏处理
        if (CollUtil.isNotEmpty(list)) {
            desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【导出】导出入库单据卡片")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderCard(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialRkOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrderCard(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_RK);
    }

    @ApiOperation(value = "【导出】导出入库单耗材")
    @PostMapping(value = "/exportOrder/materials")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrder(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialRkOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrder(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_RK);
    }
}
