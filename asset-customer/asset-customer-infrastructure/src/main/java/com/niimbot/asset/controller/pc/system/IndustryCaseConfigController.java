package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.IndustryCaseConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午5:00
 */
@Api(tags = "行业案例配置")
@ResultController
@RequestMapping("/api/industryCase/")
@RequiredArgsConstructor
public class IndustryCaseConfigController {

    private final IndustryCaseConfigFeignClient industryCaseConfigFeignClient;

    @ApiOperation(value = "行业案例配置列表")
    @GetMapping(value = "query")
    public PageUtils<IndustryCaseConfigDto> pageQuery(IndustryCaseQueryDto request) {
        return industryCaseConfigFeignClient.query(request);
    }

    @ApiOperation(value = "行业案例配置详情")
    @GetMapping(value = "detail/{configId}")
    public IndustryCaseDetailDto detail(@PathVariable("configId") Long configId) {
        return industryCaseConfigFeignClient.detail(configId);
    }
}
