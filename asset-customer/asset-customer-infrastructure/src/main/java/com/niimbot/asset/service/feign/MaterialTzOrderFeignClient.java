package com.niimbot.asset.service.feign;

import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2022/10/31 16:04
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialTzOrderFeignClient {

    @PostMapping(value = "server/material/order/tz")
    AuditableCreateOrderResult create(SensitiveMaterialTzOrderSubmitDto submitDto);

    @GetMapping(value = "server/material/order/tz/{id}")
    MaterialTzOrderDto getById(@PathVariable("id") Long id);

    @GetMapping(value = "server/material/order/tz/pageDetail")
    PageUtils<MaterialTzOrderDetailDto> pageDetail(@SpringQueryMap MaterialOrderDetailQueryDto query);

    @GetMapping(value = "server/material/order/tz/detail/{orderId}/{materialId}")
    MaterialTzOrderDetailDto getDetail(@PathVariable("orderId") Long orderId, @PathVariable("materialId") Long materialId);

    @PostMapping(value = "server/material/order/tz/page")
    PageUtils<MaterialTzOrderDto> page(MaterialOrderQueryDto query);

    @PostMapping(value = "server/material/order/tz/listForExport")
    PageUtils<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto query);

    @PostMapping(value = "server/material/order/tz/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(SensitiveMaterialTzOrderDto dto);
}
