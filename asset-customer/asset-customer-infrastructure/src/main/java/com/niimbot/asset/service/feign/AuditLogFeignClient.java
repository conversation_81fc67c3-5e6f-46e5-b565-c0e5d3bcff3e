package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.AuditLogSearch;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AuditLogFeignClient {

    @PostMapping("/server/auditLog/search")
    PageUtils<AuditLogDto> search(@RequestBody AuditLogSearch search);

    @PostMapping("/server/auditLog/record")
    boolean record(@RequestBody AuditLogRecord record);

}
