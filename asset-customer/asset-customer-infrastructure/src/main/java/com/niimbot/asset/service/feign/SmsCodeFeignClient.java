package com.niimbot.asset.service.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 短信验证码客户端
 *
 * <AUTHOR>
 * @date 2021/4/28 14:01
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface SmsCodeFeignClient {

    /**
     * 发送短信验证码
     *
     * @param iddCode 国际字冠
     * @param mobile  手机号
     */
    @GetMapping("server/system/sms/send/{iddCode}/{mobile}")
    void sendSmsCode(@PathVariable("iddCode") String iddCode, @PathVariable("mobile") String mobile);


    /**
     * 发送邮箱验证码、手机号验证码
     *
     * @param type email/mobile
     * @param addr 邮箱/手机号
     * @return true
     */
    @GetMapping("server/system/sms/code/{type}/{addr}")
    void sendCommonCode(@PathVariable("type") String type, @PathVariable("addr") String addr);

    /**
     * 校验验证码
     *
     * @param mobile 手机号
     * @param code   验证码
     * @return 验证码是否合法
     */
    @GetMapping("server/system/sms/check/{mobile}/{code}")
    boolean checkSmsCode(@PathVariable("mobile") String mobile, @PathVariable("code") String code);

}
