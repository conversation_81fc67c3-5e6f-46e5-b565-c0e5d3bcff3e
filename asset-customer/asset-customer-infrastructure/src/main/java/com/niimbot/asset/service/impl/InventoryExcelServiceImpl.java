package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.InventoryConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.JacksonConverter;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.*;
import com.niimbot.asset.service.feign.*;
import com.niimbot.dynamicform.BuildAssetLogDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.inventory.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.ImportTaskDto;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/7 15:29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryExcelServiceImpl implements InventoryExcelService {

    private static final String STANDARD_ID = "standardId";

    private final AssetQueryFieldService assetQueryFieldService;
    private final InventoryAssetFeignClient inventoryAssetFeignClient;
    private final ImportTaskFeignClient importTaskFeignClient;
    private final ExportService exportService;
    private final InventoryConfigFeignClient inventoryConfigFeignClient;
    private final InventoryFeignClient inventoryFeignClient;
    private final FormFeignClient formFeignClient;
    private final AssetFeignClient assetFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final InventoryAssetService inventoryAssetService;
    private final FileUploadService fileUploadService;

    private final InventoryResultHandleService resultHandleService;
    @Autowired
    private FileUploadConfig fileUploadConfig;
    private static ThreadLocal<GlobalCache> companyCache = new TransmittableThreadLocal<>();

    @Data
    @Accessors(chain = true)
    private static class GlobalCache {
        private Long company;
        private FormVO formVO;
        private String module;
    }

    @Override
    public void inventoryReportExport(Long id) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        GlobalCache globalCache = new GlobalCache();
        globalCache.setCompany(companyId)
                .setFormVO(formVO)
                .setModule("inventory_report");
        companyCache.set(globalCache);
        // 异步启动
        new Thread(() -> {
            startInventoryReportExport(id);
        }).start();
    }

    private void startInventoryReportExport(Long id) {
        // 导出任务是否成功
        Long taskId = null;
        // 导出是否报错
        boolean error = false;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", id);
            // 创建导出任务
            InventoryDto inventoryInfo = inventoryFeignClient.getInventoryInfo(id);
            String fileName = StrUtil.format("{}-盘点报告（{}）", inventoryInfo.getName(), LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyyMMddhhmmss")));
            ImportTaskDto importTaskDto = new ImportTaskDto()
                    .setName(fileName)
                    .setType(DictConstant.TASK_TYPE_EXPORT)
                    .setQueryData(jsonObject)
                    .setExportUrl(OrderTypeNewEnum.EXPORT_TYPE_INVENTORY_REPORT.getExportUrl())
                    .setImportType(AssetConstant.EXPORT_TYPE_INVENTORY_REPORT);
            taskId = importTaskFeignClient.save(importTaskDto);
            importTaskDto.setId(taskId);
            executeInventoryReportExport(importTaskDto, id);
            importTaskFeignClient.update(importTaskDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (ObjectUtil.isNotNull(taskId)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_FAIL);
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_FAIL);
                error = true;
            }
        } finally {
            // 更新任务状态
            if (ObjectUtil.isNotNull(taskId) && !error) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_SUCC)
                        .setFinishTime(LocalDateTime.now());
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_SUCC);
                FileUtil.del(FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", companyCache.get().getModule()));
            }
            this.clearThreadLocal();
        }
    }

    private void executeInventoryReportExport(ImportTaskDto importTask, Long id) {
        // 资产列表表头
        List<AssetHeadDto> assetHeadView = assetQueryFieldService.assetHeadView();
        for (AssetHeadDto headDto : assetHeadView) {
            // 特殊处理资产状态
            if ("status".equals(headDto.getCode())) {
                headDto.setTranslationCode("statusText");
                break;
            }
        }
        // 临时过滤掉资产状态表头
        assetHeadView.removeIf(v -> "status".equals(v.getCode()));
        // 上报表头
        List<FormFieldCO> reportHeadView = inventoryConfigFeignClient.inventoryAttrList("2").getFormFields();

        InventorySurplusQueryDto dto = new InventorySurplusQueryDto();
        dto.setInventoryId(id);
        // 获取未盘数据
        ExcelExportDto wp = getWp(dto, assetHeadView, "");
        wp.setSheetName("盘亏（" + wp.getRows().size() + "）");

        // 获取已盘数据
        ExcelExportDto yp = getYp(dto, assetHeadView, "");
        yp.setSheetName("正常（" + yp.getRows().size() + "）");

        // 获取盘盈在册
        ExcelExportDto pyZc = getPyZc(dto, assetHeadView, "");
        pyZc.setSheetName("盘盈-在册（" + pyZc.getRows().size() + "）");

        // 获取盘盈不在册
        ExcelExportDto pyBzc = getPyBzc(dto, reportHeadView, "");
        pyBzc.setSheetName("盘盈-不在册（" + pyBzc.getRows().size() + "）");

        List<ExcelExportDto> exportList = new ArrayList<>();
        exportList.add(yp);
        exportList.add(pyZc);
        exportList.add(pyBzc);
        exportList.add(wp);

        importTask.setTotal(wp.getRows().size() + yp.getRows().size() + pyZc.getRows().size() + pyBzc.getRows().size());
        File tempPath = getTempPath(companyCache.get().getModule());
        // 写入
        try {
            String fileName = importTask.getName() + ".xlsx";
            File outputFile = new File(tempPath.getPath() + "/" + fileName);
            String localPath = outputFile.getPath();

            // 写入盘点概述
            ExcelWriter writer = inventoryAssetService.buildInventoryViewExcel(id);
            writer.setSheet(0);
            writer.renameSheet(0, "盘点概述");
            for (int i = 0; i < exportList.size(); i++) {
                ExcelExportDto excelExportDto = exportList.get(i);
                writer.setSheet(i + 1);
                writer.renameSheet(i + 1, excelExportDto.getSheetName());
                writer.writeHeadRow(excelExportDto.getHeaderData().values());
                List<String> codes = new ArrayList<>(excelExportDto.getHeaderData().keySet());
                List<?> rows = excelExportDto.getRows();
                for (Object row : rows) {
                    JSONObject json = (JSONObject) JSONObject.toJSON(row);
                    List<Object> rowData = new ArrayList<>();
                    for (String code : codes) {
                        rowData.add(json.get(code));
                    }
                    writer.writeRow(rowData);
                }
            }
            writer.setDestFile(outputFile);
            writer.flush();
            writer.close();

            // 上传文件到oss 单个文件或者zip包
            String path = fileUploadService.putFile(getDestPath(companyCache.get().getModule(), fileName), localPath);
            // 更新任务url
            if (StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(importTask.getId()).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("盘点报告导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @Override
    public void inventoryAssetReportExport(InventoryAssetPageExportDto dto) {
//        dto.setConditions(ListUtil.empty());
        Long companyId = LoginUserThreadLocal.getCompanyId();
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        GlobalCache globalCache = new GlobalCache();
        globalCache.setCompany(companyId)
                .setFormVO(formVO)
                .setModule("inventory_report_asset");
        companyCache.set(globalCache);
        // 异步启动
        new Thread(() -> {
            startInventoryAssetReportExport(dto);
        }).start();
    }

    private void startInventoryAssetReportExport(InventoryAssetPageExportDto dto) {
        // 导出任务是否成功
        Long taskId = null;
        // 导出是否报错
        boolean error = false;
        try {
            JSONObject jsonObject = JsonUtil.toJsonObject(dto);
            String name;
            if (dto.getTaskId() == null) {
                InventoryDto inventoryInfo = inventoryFeignClient.getInventoryInfo(dto.getInventoryId());
                name = StrUtil.format("{}-盘点单（{}）", inventoryInfo.getName(), LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyyMMddhhmmss")));
            } else {
                InventoryTaskInfoDto info = inventoryFeignClient.getInfo(dto.getTaskId());
                name = StrUtil.format("{}-盘点任务（{}）", info.getName(), LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyyMMddhhmmss")));
            }
            // 创建导出任务
            ImportTaskDto importTaskDto = new ImportTaskDto()
                    .setName(name)
                    .setType(DictConstant.TASK_TYPE_EXPORT)
                    .setQueryData(jsonObject)
                    .setExportUrl(OrderTypeNewEnum.EXPORT_TYPE_INVENTORY_ASSET.getExportUrl())
                    .setImportType(AssetConstant.EXPORT_TYPE_INVENTORY_ASSET);
            taskId = importTaskFeignClient.save(importTaskDto);
            importTaskDto.setId(taskId);
            executeInventoryAssetReportExport(importTaskDto, dto);
            importTaskFeignClient.update(importTaskDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (ObjectUtil.isNotNull(taskId)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_FAIL);
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_FAIL);
                error = true;
            }
        } finally {
            // 更新任务状态
            if (ObjectUtil.isNotNull(taskId) && !error) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_SUCC)
                        .setFinishTime(LocalDateTime.now());
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_SUCC);
                FileUtil.del(FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", companyCache.get().getModule()));
            }
            this.clearThreadLocal();
        }
    }

    private void executeInventoryAssetReportExport(ImportTaskDto importTask, InventoryAssetPageExportDto dto) {
        // 资产列表表头
        List<AssetHeadDto> assetHeadView = assetQueryFieldService.assetHeadView();
        for (AssetHeadDto headDto : assetHeadView) {
            // 特殊处理资产状态
            if ("status".equals(headDto.getCode())) {
                headDto.setTranslationCode("statusText");
                break;
            }
        }
        // 临时过滤掉资产状态表头
        assetHeadView.removeIf(v -> "status".equals(v.getCode()));
        // 上报表头
        List<FormFieldCO> reportHeadView = inventoryConfigFeignClient.inventoryAttrList("2").getFormFields();
        // 获取未盘数据
        InventorySurplusQueryDto noChecked = dto.getNoChecked();
        noChecked.setInventoryId(dto.getInventoryId()).setTaskId(dto.getTaskId());
        ExcelExportDto wp = getWp(noChecked, assetHeadView, "未盘");
        // 获取已盘数据
        InventorySurplusQueryDto checked = dto.getChecked();
        checked.setInventoryId(dto.getInventoryId()).setTaskId(dto.getTaskId());
        ExcelExportDto yp = getYp(checked, assetHeadView, "已盘");
        // 获取盘盈在册
        InventorySurplusQueryDto checkedAddInMark = dto.getCheckedAddInMark();
        checkedAddInMark.setInventoryId(dto.getInventoryId()).setTaskId(dto.getTaskId());
        ExcelExportDto pyZc = getPyZc(checkedAddInMark, assetHeadView, "盘盈-在册");
        // 获取盘盈不在册
        InventorySurplusQueryDto checkedAddNotInMark = dto.getCheckedAddNotInMark();
        checkedAddNotInMark.setInventoryId(dto.getInventoryId()).setTaskId(dto.getTaskId());
        ExcelExportDto pyBzc = getPyBzc(checkedAddNotInMark, reportHeadView, "盘盈-不在册");

        List<ExcelExportDto> exportList = new ArrayList<>();
        exportList.add(wp);
        exportList.add(yp);
        exportList.add(pyZc);
        exportList.add(pyBzc);

        importTask.setTotal(wp.getRows().size() + yp.getRows().size() + pyZc.getRows().size() + pyBzc.getRows().size());
        File tempPath = getTempPath(companyCache.get().getModule());
        try {
            String fileName = importTask.getName() + ".xlsx";
            File outputFile = new File(tempPath.getPath() + "/" + fileName);
            String localPath = outputFile.getPath();
            // 获取 writer
            ExcelWriter writer = ExcelUtil.getBigWriter(5000);
            for (int i = 0; i < exportList.size(); i++) {
                ExcelExportDto excelExportDto = exportList.get(i);
                writer.setSheet(i);
                writer.renameSheet(i, excelExportDto.getSheetName());
                writer.writeHeadRow(excelExportDto.getHeaderData().values());
                List<String> codes = new ArrayList<>(excelExportDto.getHeaderData().keySet());
                List<?> rows = excelExportDto.getRows();
                for (Object row : rows) {
                    JSONObject json = (JSONObject) JSONObject.toJSON(row);
                    List<Object> rowData = new ArrayList<>();
                    for (String code : codes) {
                        rowData.add(json.get(code));
                    }
                    writer.writeRow(rowData);
                }
            }
            //设置输出文件路径
            writer.setDestFile(outputFile);
            writer.flush();
            writer.close();

            // 上传文件到oss 单个文件或者zip包
            String path = fileUploadService.putFile(getDestPath(companyCache.get().getModule(), fileName), localPath);
            // 更新任务url
            if (StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(importTask.getId()).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("盘点资产导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    @Override
    public void assetPlExport(InventorySurplusQueryDto dto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        GlobalCache globalCache = new GlobalCache();
        globalCache.setCompany(companyId)
                .setFormVO(formVO)
                .setModule("inventory_report_pl_asset");
        companyCache.set(globalCache);
        // 异步启动
        new Thread(() -> {
            startAssetPlExport(dto);
        }).start();
    }

    private void startAssetPlExport(InventorySurplusQueryDto dto) {
        // 导出任务是否成功
        Long taskId = null;
        // 导出是否报错
        boolean error = false;
        try {
            JSONObject jsonObject = JsonUtil.toJsonObject(dto);
            String name;
            if (dto.getTaskId() == null) {
                InventoryDto inventoryInfo = inventoryFeignClient.getInventoryInfo(dto.getInventoryId());
                name = inventoryInfo.getName();
            } else {
                InventoryTaskInfoDto info = inventoryFeignClient.getInfo(dto.getTaskId());
                name = info.getName();
            }
            // 创建导出任务
            ImportTaskDto importTaskDto = new ImportTaskDto()
                    .setName(StrUtil.format("[{}]盘点损益处理（{}）", name, LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyyMMddhhmmss"))))
                    .setType(DictConstant.TASK_TYPE_EXPORT)
                    .setQueryData(jsonObject)
                    .setExportUrl(OrderTypeNewEnum.EXPORT_TYPE_INVENTORY_PL_ASSET.getExportUrl())
                    .setImportType(AssetConstant.EXPORT_TYPE_INVENTORY_PL_ASSET);
            taskId = importTaskFeignClient.save(importTaskDto);
            importTaskDto.setId(taskId);
            executeAssetPlExport(importTaskDto, dto);
            importTaskFeignClient.update(importTaskDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (ObjectUtil.isNotNull(taskId)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_FAIL);
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_FAIL);
                error = true;
            }
        } finally {
            // 更新任务状态
            if (ObjectUtil.isNotNull(taskId) && !error) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setTaskStatus(DictConstant.IMPORT_STATUS_SUCC)
                        .setFinishTime(LocalDateTime.now());
                importTaskFeignClient.update(importTaskUpdate);
                exportService.sendAssetOrderMsg(companyCache.get().getCompany(), taskId, DictConstant.IMPORT_STATUS_SUCC);
                FileUtil.del(FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", companyCache.get().getModule()));
            }
            this.clearThreadLocal();
        }
    }

    private void executeAssetPlExport(ImportTaskDto importTask, InventorySurplusQueryDto dto) {
        // 资产列表表头
        List<AssetHeadDto> assetHeadView = assetQueryFieldService.assetHeadView();
        for (AssetHeadDto headDto : assetHeadView) {
            // 特殊处理资产状态
            if ("status".equals(headDto.getCode())) {
                headDto.setTranslationCode("statusText");
                break;
            }
        }
        // 临时过滤掉资产状态表头
        assetHeadView.removeIf(v -> "status".equals(v.getCode()));

        // 上报表头
        List<FormFieldCO> reportHeadView = inventoryConfigFeignClient.inventoryAttrList("2").getFormFields();

        // 处理记录
        List<InventoryHandleLogDto> inventoryHandleLogs = inventoryFeignClient.handleLogList(dto.getInventoryId());
        dictConvertUtil.convertToDictionary(inventoryHandleLogs);
        Map<Integer, List<InventoryHandleLogDto>> handleLogMap = inventoryHandleLogs.stream()
                .collect(Collectors.groupingBy(InventoryHandleLogDto::getBizType));

        // 盘盈不在册
        ExcelExportDto plSurplus = plSurplus(dto.getInventoryId(), reportHeadView, handleLogMap.getOrDefault(1, ListUtil.empty()));

        // 盘亏资产
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);

        List<AssetHeadDto> inventoryHeadView = new ArrayList<>();
        inventoryHeadView.add(new AssetHeadDto()
                .setCode("handleStatusText")
                .setName("处理状态")
                .setType(FormFieldCO.TEXT_INPUT));
        inventoryHeadView.add(new AssetHeadDto()
                .setCode("handleResult")
                .setName("处理结果")
                .setType(FormFieldCO.TEXT_INPUT));
        inventoryHeadView.add(new AssetHeadDto()
                .setCode("handleTime")
                .setName("处理时间")
                .setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"))
                .setType(FormFieldCO.DATETIME));
        inventoryHeadView.add(new AssetHeadDto()
                .setCode("handleUserText")
                .setName("处理人")
                .setType(FormFieldCO.TEXT_INPUT));
        ExcelExportDto plPage = plPage(dto, inventoryHeadView, assetHeadView, handleLogMap.getOrDefault(2, ListUtil.empty()));

        // 已修改资产处理
        ExcelExportDto modifiedPage = modifiedPage(dto, inventoryHeadView, assetHeadView, handleLogMap.getOrDefault(3, ListUtil.empty()));

        // 已拍照资产
        ExcelExportDto takePhotoPage = takePhotoPage(dto, inventoryHeadView, assetHeadView, handleLogMap.getOrDefault(4, ListUtil.empty()));

        List<ExcelExportDto> exportList = new ArrayList<>();
        exportList.add(plSurplus);
        exportList.add(plPage);
        exportList.add(modifiedPage);
        exportList.add(takePhotoPage);

        importTask.setTotal(plSurplus.getRows().size() + plPage.getRows().size() + modifiedPage.getRows().size() + takePhotoPage.getRows().size());
        File tempPath = getTempPath(companyCache.get().getModule());
        try {
            String fileName = importTask.getName() + ".xlsx";
            File outputFile = new File(tempPath.getPath() + "/" + fileName);
            String localPath = outputFile.getPath();
            // 获取 writer
            ExcelWriter writer = ExcelUtil.getBigWriter(5000);
            for (int i = 0; i < exportList.size(); i++) {
                ExcelExportDto excelExportDto = exportList.get(i);
                writer.setSheet(i);
                writer.renameSheet(i, excelExportDto.getSheetName());
                writer.writeHeadRow(excelExportDto.getHeaderData().values());
                List<String> codes = new ArrayList<>(excelExportDto.getHeaderData().keySet());
                List<?> rows = excelExportDto.getRows();
                for (Object row : rows) {
                    JSONObject json = (JSONObject) JSONObject.toJSON(row);
                    List<Object> rowData = new ArrayList<>();
                    for (String code : codes) {
                        rowData.add(json.get(code));
                    }
                    writer.writeRow(rowData);
                }
            }
            //设置输出文件路径
            writer.setDestFile(outputFile);
            writer.flush();
            writer.close();

            // 上传文件到oss 单个文件或者zip包
            String path = fileUploadService.putFile(getDestPath(companyCache.get().getModule(), fileName), localPath);
            // 更新任务url
            if (StrUtil.isNotBlank(path)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(importTask.getId()).setUrl(path);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("盘点损益处理导出失败", e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    private ExcelExportDto plSurplus(Long inventoryId, List<FormFieldCO> reportHeadView, List<InventoryHandleLogDto> inventoryHandleLogs) {
        Map<Long, InventoryHandleLogDto> inventoryHandleLogMap = inventoryHandleLogs.stream()
                .collect(Collectors.toMap(InventoryHandleLogDto::getInventoryAssetReportId, k -> k));

        InventorySurplusSimpleQueryDto simpleQueryDto = new InventorySurplusSimpleQueryDto();
        simpleQueryDto.setInventoryId(inventoryId);
        simpleQueryDto.setAssetMark(2);
        simpleQueryDto.setPageNum(1);
        simpleQueryDto.setPageSize(Integer.MAX_VALUE);
        PageUtils<JSONObject> plSurplus = resultHandleService.getPlSurplus(simpleQueryDto);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName("盘盈不在册资产");

        List<FormFieldCO> assetHeadList = new ArrayList<>();
        FormFieldCO handleStatus = new FormFieldCO();
        handleStatus.setFieldCode("handleStatusText");
        handleStatus.setFieldName("处理状态");
        handleStatus.setFieldProps(new JSONObject());
        assetHeadList.add(handleStatus);

        FormFieldCO handleResult = new FormFieldCO();
        handleResult.setFieldCode("handleResult");
        handleResult.setFieldName("处理结果");
        handleResult.setFieldProps(new JSONObject());
        assetHeadList.add(handleResult);

        FormFieldCO handleTime = new FormFieldCO();
        handleTime.setFieldCode("handleTime");
        handleTime.setFieldName("处理时间");
        handleTime.setFieldType(FormFieldCO.DATETIME);
        handleTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"));
        assetHeadList.add(handleTime);

        FormFieldCO handleUserText = new FormFieldCO();
        handleUserText.setFieldCode("handleUserText");
        handleUserText.setFieldName("处理人");
        handleUserText.setFieldProps(new JSONObject());
        assetHeadList.add(handleUserText);

        FormFieldCO inventoryTime = new FormFieldCO();
        inventoryTime.setFieldCode("createTime");
        inventoryTime.setFieldName("盘点时间");
        inventoryTime.setFieldType(FormFieldCO.DATETIME);
        inventoryTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"));
        assetHeadList.add(inventoryTime);

        FormFieldCO actualInventoryUserText = new FormFieldCO();
        actualInventoryUserText.setFieldCode("reporterText");
        actualInventoryUserText.setFieldProps(new JSONObject());
        actualInventoryUserText.setFieldName("盘点人");
        assetHeadList.add(actualInventoryUserText);

        assetHeadList.addAll(reportHeadView);
        excelExportDto.setHeaderData(assetHeadFormFieldToMap(assetHeadList));
        // 属性缓存
        Cache cache = localFormFieldTransCache(assetHeadList);
        List<JSONObject> excelRows = plSurplus.getList().parallelStream()
                .peek(f -> {
                    Long id = f.getLong("id");
                    InventoryHandleLogDto handleLogDto = inventoryHandleLogMap.get(id);
                    if (handleLogDto != null) {
                        String jackson;
                        try {
                            jackson = JacksonConverter.MAPPER.writeValueAsString(handleLogDto);
                            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
                            f.putAll(json);
                        } catch (Exception e) {
                            log.warn(e.getMessage(), e);
                        }
                    }
                    convertExcelData(f, cache);
                })
                .collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }

    private ExcelExportDto plPage(InventorySurplusQueryDto dto,
                                  List<AssetHeadDto> inventoryHeadView,
                                  List<AssetHeadDto> assetHeadView,
                                  List<InventoryHandleLogDto> inventoryHandleLogs) {
        Map<Long, InventoryHandleLogDto> inventoryHandleLogMap = inventoryHandleLogs.stream()
                .collect(Collectors.toMap(InventoryHandleLogDto::getInventoryAssetId, k -> k));

        PageUtils<JSONObject> plPage = resultHandleService.plPage(dto);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName("盘亏资产");
        List<AssetHeadDto> assetHeadList = new ArrayList<>(inventoryHeadView);
        assetHeadList.addAll(assetHeadView);

        excelExportDto.setHeaderData(assetHeadToMap(assetHeadList));
        // 属性缓存
        Cache cache = localTransCache(assetHeadList);
        List<JSONObject> excelRows = plPage.getList().parallelStream()
                .peek(f -> {
                    Long id = f.getLong("id");
                    InventoryHandleLogDto handleLogDto = inventoryHandleLogMap.get(id);
                    if (handleLogDto != null) {
                        String jackson;
                        try {
                            jackson = JacksonConverter.MAPPER.writeValueAsString(handleLogDto);
                            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
                            f.putAll(json);
                        } catch (Exception e) {
                            log.warn(e.getMessage(), e);
                        }
                    }
                    convertExcelData(f, cache);
                })
                .collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }


    private ExcelExportDto modifiedPage(InventorySurplusQueryDto dto,
                                        List<AssetHeadDto> inventoryHeadView,
                                        List<AssetHeadDto> assetHeadView,
                                        List<InventoryHandleLogDto> inventoryHandleLogs) {
        Map<Long, InventoryHandleLogDto> assetReportLogMap = new HashMap<>();
        Map<Long, InventoryHandleLogDto> assetLogMap = new HashMap<>();
        for (InventoryHandleLogDto k : inventoryHandleLogs) {
            assetReportLogMap.put(k.getInventoryAssetReportId(), k);
            assetLogMap.put(k.getInventoryAssetId(), k);

        }

        PageUtils<JSONObject> modifiedPage = resultHandleService.modifiedPage(dto);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName("已修改资产");
        List<AssetHeadDto> assetHeadList = new ArrayList<>(inventoryHeadView);
        assetHeadList.add(new AssetHeadDto()
                .setCode("actualInventoryUserText")
                .setName("盘点人")
                .setType(FormFieldCO.TEXT_INPUT));
        assetHeadList.add(new AssetHeadDto()
                .setCode("inventoryTime")
                .setName("盘点时间")
                .setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"))
                .setType(FormFieldCO.DATETIME));
        assetHeadList.addAll(assetHeadView);
        excelExportDto.setHeaderData(assetHeadToMap(assetHeadList));
        // 属性缓存
        Cache cache = localTransCache(assetHeadList);
        List<JSONObject> excelRows = modifiedPage.getList().parallelStream()
                .peek(f -> {
                    Long id = f.getLong("id");
                    InventoryHandleLogDto handleLogDto = assetReportLogMap.get(id);
                    if (handleLogDto == null) {
                        handleLogDto = assetLogMap.get(id);
                    }

                    if (handleLogDto != null) {
                        String jackson;
                        try {
                            jackson = JacksonConverter.MAPPER.writeValueAsString(handleLogDto);
                            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
                            f.putAll(json);
                        } catch (Exception e) {
                            log.warn(e.getMessage(), e);
                        }
                    }
                    convertExcelData(f, cache);
                })
                .collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }

    private ExcelExportDto takePhotoPage(InventorySurplusQueryDto dto,
                                         List<AssetHeadDto> inventoryHeadView,
                                         List<AssetHeadDto> assetHeadView,
                                         List<InventoryHandleLogDto> inventoryHandleLogs) {
        Map<Long, InventoryHandleLogDto> inventoryHandleLogMap = inventoryHandleLogs.stream()
                .collect(Collectors.toMap(InventoryHandleLogDto::getInventoryAssetId, k -> k));
        PageUtils<JSONObject> takePhotoPage = resultHandleService.takePhotoPage(dto);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName("已拍照资产");
        List<AssetHeadDto> assetHeadList = new ArrayList<>(inventoryHeadView);
        assetHeadList.add(new AssetHeadDto()
                .setCode("actualInventoryUserText")
                .setName("盘点人")
                .setType(FormFieldCO.TEXT_INPUT));
        assetHeadList.add(new AssetHeadDto()
                .setCode("inventoryTime")
                .setName("盘点时间")
                .setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"))
                .setType(FormFieldCO.DATETIME));
        assetHeadList.addAll(assetHeadView);
        excelExportDto.setHeaderData(assetHeadToMap(assetHeadList));
        // 属性缓存
        Cache cache = localTransCache(assetHeadList);
        List<JSONObject> excelRows = takePhotoPage.getList().parallelStream()
                .peek(f -> {
                    Long id = f.getLong("id");
                    InventoryHandleLogDto handleLogDto = inventoryHandleLogMap.get(id);
                    if (handleLogDto != null) {
                        String jackson;
                        try {
                            jackson = JacksonConverter.MAPPER.writeValueAsString(handleLogDto);
                            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
                            f.putAll(json);
                        } catch (Exception e) {
                            log.warn(e.getMessage(), e);
                        }
                    }
                    convertExcelData(f, cache);
                })
                .collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }

    private void clearThreadLocal() {
        companyCache.remove();
    }

    private String getDestPath(String module, String fileName) {
        Long companyId = companyCache.get().getCompany();
        return companyId + "/" + module + "/" + DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN) + "/" + fileName;
    }

    /**
     * 获取临时存放路径
     */
    private File getTempPath(String module) {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 临时文件夹路径
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", module, currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    /**
     * 未盘数据
     */
    private ExcelExportDto getWp(InventorySurplusQueryDto dto, List<AssetHeadDto> assetHeadView, String sheetName) {
        dto.setChecked(0L);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName(sheetName);
        // 写入表头
        excelExportDto.setHeaderData(assetHeadToMap(assetHeadView));
        // 写入数据
        List<InventoryAssetListDto> wpInventoryAssetList = inventoryAssetFeignClient.assetListAll(dto);

        Cache cache = localTransCache(assetHeadView);
        List<JSONObject> excelRows = wpInventoryAssetList.parallelStream()
                .map(f -> {
                    JSONObject assetSnapshotData = f.getAssetSnapshotData();
                    convertExcelData(assetSnapshotData, cache);
                    return assetSnapshotData;
                }).collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }

    private Cache localTransCache(List<AssetHeadDto> assetHeadView) {
        Cache cache = new Cache();
        // 查询日期类型
        for (AssetHeadDto it : assetHeadView) {
            if (FormFieldCO.DATETIME.equals(it.getType())) {
                cache.getDateFormatType().put(it.getCode(), it.getFieldProps().getString("dateFormatType"));
            } else if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getType())) {
                cache.getMultiSelectSet().add(it.getCode());
            } else if (FormFieldCO.NUMBER_INPUT.equals(it.getType())) {
                cache.getNumberMap().put(it.getCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            } else if (FormFieldCO.IMAGES.equals(it.getType())
                    || FormFieldCO.FILES.equals(it.getType())) {
                cache.getImageSet().add(it.getCode());
            }
        }
        return cache;
    }

    private Cache localFormFieldTransCache(List<FormFieldCO> assetHeadView) {
        Cache cache = new Cache();
        // 查询日期类型
        for (FormFieldCO it : assetHeadView) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                cache.getDateFormatType().put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            } else if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                cache.getMultiSelectSet().add(it.getFieldCode());
            } else if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                cache.getNumberMap().put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            } else if (FormFieldCO.IMAGES.equals(it.getFieldType())
                    || FormFieldCO.FILES.equals(it.getFieldType())) {
                cache.getImageSet().add(it.getFieldCode());
            }
        }
        return cache;
    }

    @Data
    private static class Cache {
        private Map<String, String> dateFormatType = new HashMap<>();
        private Set<String> multiSelectSet = new HashSet<>();
        private Set<String> imageSet = new HashSet<>();
        private Map<String, Boolean> numberMap = new HashMap<>();
    }

    private void convertExcelData(JSONObject assetSnapshotData, Cache cache) {
        cache.getNumberMap().forEach((code, percentage) -> {
            Number number = Convert.toNumber(assetSnapshotData.get(code));
            if (ObjectUtil.isNotNull(number)) {
                if (BooleanUtil.isTrue(percentage)) {
                    assetSnapshotData.put(code, number + "%");
                } else {
                    assetSnapshotData.put(code, number);
                }
            }
        });
        cache.getDateFormatType().forEach((code, fmt) -> {
            String date = assetSnapshotData.getString(code);
            if (StrUtil.isNotEmpty(date)) {
                try {
                    LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                    assetSnapshotData.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                } catch (Exception e) {
                    log.warn("[{}] [{}]转换时间异常", code, date);
                }
            }
        });
        cache.getMultiSelectSet().forEach(code -> {
            try {
                JSONArray jsonArray = assetSnapshotData.getJSONArray(code);
                if (CollUtil.isNotEmpty(jsonArray)) {
                    List<String> strings = jsonArray.toJavaList(String.class);
                    String collect = String.join(",", strings);
                    assetSnapshotData.put(code, collect);
                } else {
                    assetSnapshotData.put(code, null);
                }
            } catch (Exception e) {
                log.warn("[{}] [{}]转换数组异常", code, assetSnapshotData.get(code));
            }
        });
        cache.getImageSet().forEach(code -> {
            try {
                JSONArray jsonArray = assetSnapshotData.getJSONArray(code);
                if (CollUtil.isNotEmpty(jsonArray)) {
                    List<String> strings = jsonArray.toJavaList(String.class);
                    String collect = String.join("\n", strings);
                    assetSnapshotData.put(code, collect);
                } else {
                    assetSnapshotData.put(code, null);
                }
            } catch (Exception e) {
                log.warn("[{}] [{}]转换数组异常", code, assetSnapshotData.get(code));
            }
        });
    }


    /**
     * 已盘数据
     */
    private ExcelExportDto getYp(InventorySurplusQueryDto dto, List<AssetHeadDto> assetHeadView, String sheetName) {
        dto.setChecked(1L);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName(sheetName);
        // 写入表头
        List<AssetHeadDto> assetHeadList = new ArrayList<>();
        assetHeadList.add(new AssetHeadDto()
                .setCode("inventoryTime")
                .setName("盘点时间")
                .setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"))
                .setType(FormFieldCO.DATETIME));
        assetHeadList.add(new AssetHeadDto()
                .setCode("actualInventoryUserText")
                .setType(FormFieldCO.TEXT_INPUT)
                .setName("盘点人"));
        assetHeadList.add(new AssetHeadDto()
                .setCode("inventoryModeText")
                .setType(FormFieldCO.TEXT_INPUT)
                .setName("盘点方式"));
        assetHeadList.add(new AssetHeadDto()
                .setCode("remark")
                .setType(FormFieldCO.TEXT_INPUT)
                .setName("存疑备注"));
        assetHeadList.add(new AssetHeadDto()
                .setCode("assetChangRecordText")
                .setType(FormFieldCO.TEXT_INPUT)
                .setName("盘点人修改的信息"));
        assetHeadList.addAll(assetHeadView);
        excelExportDto.setHeaderData(assetHeadToMap(assetHeadList));

        // 写入数据
        List<InventoryAssetListDto> ypInventoryAssetList = inventoryAssetFeignClient.assetListAll(dto);
        Cache cache = localTransCache(assetHeadList);
        FormVO formVO = companyCache.get().getFormVO();
        // 属性缓存
        Map<Long, List<FormFieldCO>> formFieldCache = new HashMap<>();
        formFieldCache.put(0L, inventoryAssetService.buildAttrCache(formVO.getFormFields(), formVO.getFormId(), null));
        dictConvertUtil.convertToDictionary(ypInventoryAssetList);
        List<JSONObject> excelRows = ypInventoryAssetList.stream().map(f -> {
            JSONObject assetData = Optional.of(f.getAssetSnapshotData()).orElse(new JSONObject());

            // 获取资产修改内容
            Long standardId = assetData.getLong(STANDARD_ID);
            List<FormFieldCO> formFieldCOS;
            if (standardId != null) {
                if (formFieldCache.containsKey(standardId)) {
                    formFieldCOS = formFieldCache.get(standardId);
                } else {
                    formFieldCOS = inventoryAssetService.buildAttrCache(formVO.getFormFields(), formVO.getFormId(), standardId);
                    formFieldCache.put(standardId, formFieldCOS);
                }
            } else {
                formFieldCOS = formFieldCache.get(0L);
            }
            JSONObject tmpJsonOrigin = JSONObject.parseObject(f.getAssetChangRecordOrigen());
            JSONObject tmpJsonChanged = JSONObject.parseObject(f.getAssetChangRecord());
            String tmpStrJson = assetFeignClient.buildAssetLog(new BuildAssetLogDto(tmpJsonOrigin, tmpJsonChanged, formFieldCOS));
            f.setAssetChangRecordText(tmpStrJson);
            String jackson;
            try {
                jackson = JacksonConverter.MAPPER.writeValueAsString(f);
            } catch (JsonProcessingException e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点资产数据异常");
            }
            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
            // 特殊处理
            json.remove("assetSnapshotData");
            json.remove("assetCode");
            assetData.putAll(json);
            convertExcelData(assetData, cache);
            return assetData;
        }).collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }

    private ExcelExportDto getPyZc(InventorySurplusQueryDto dto, List<AssetHeadDto> assetHeadView, String sheetName) {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName(sheetName);
        dto.setAssetMark(InventoryConstant.ASSET_MARK_ON);
        List<InventorySurplusDto> surplusList = inventoryFeignClient.getSurplusList(dto);
        // 写入表头
        List<AssetHeadDto> assetHeadList = new ArrayList<>();
        assetHeadList.add(new AssetHeadDto()
                .setCode("createTime")
                .setName("盘点时间")
                .setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"))
                .setType(FormFieldCO.DATETIME));
        assetHeadList.add(new AssetHeadDto()
                .setCode("reporterText")
                .setType(FormFieldCO.TEXT_INPUT)
                .setName("盘点人"));
        assetHeadList.add(new AssetHeadDto()
                .setCode("remark")
                .setType(FormFieldCO.TEXT_INPUT)
                .setName("存疑备注"));
        assetHeadList.add(new AssetHeadDto()
                .setCode("assetChangRecordText")
                .setType(FormFieldCO.TEXT_INPUT)
                .setName("盘点人修改的信息"));
        assetHeadList.addAll(assetHeadView);
        excelExportDto.setHeaderData(assetHeadToMap(assetHeadList));
        // 属性缓存
        Map<Long, List<FormFieldCO>> formFieldCache = new HashMap<>();
        Cache cache = localTransCache(assetHeadList);
        FormVO formVO = companyCache.get().getFormVO();
        formFieldCache.put(0L, inventoryAssetService.buildAttrCache(formVO.getFormFields(), formVO.getFormId(), null));
        dictConvertUtil.convertToDictionary(surplusList);
        List<JSONObject> excelRows = surplusList.stream().map(f -> {
            JSONObject assetData = Optional.of(f.getAssetData()).orElse(new JSONObject());

            // 获取资产修改内容
            Long standardId = assetData.getLong(STANDARD_ID);
            List<FormFieldCO> formFieldCOS;
            if (standardId != null) {
                if (formFieldCache.containsKey(standardId)) {
                    formFieldCOS = formFieldCache.get(standardId);
                } else {
                    formFieldCOS = inventoryAssetService.buildAttrCache(formVO.getFormFields(), formVO.getFormId(), standardId);
                    formFieldCache.put(standardId, formFieldCOS);
                }
            } else {
                formFieldCOS = formFieldCache.get(0L);
            }
            JSONObject tmpJsonOrigin = JSONObject.parseObject(f.getAssetChangRecordOrigen());
            JSONObject tmpJsonChanged = JSONObject.parseObject(f.getAssetChangRecord());
            String tmpStrJson = assetFeignClient.buildAssetLog(new BuildAssetLogDto(tmpJsonOrigin, tmpJsonChanged, formFieldCOS));
            f.setAssetChangRecordText(tmpStrJson);
            String jackson;
            try {
                jackson = JacksonConverter.MAPPER.writeValueAsString(f);
            } catch (JsonProcessingException e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点资产数据异常");
            }
            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
            json.remove("assetSnapshotData");
            assetData.putAll(json);
            convertExcelData(assetData, cache);
            return assetData;
        }).collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }

    private ExcelExportDto getPyBzc(InventorySurplusQueryDto dto, List<FormFieldCO> assetHeadView, String sheetName) {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setSheetName(sheetName);
        dto.setAssetMark(InventoryConstant.ASSET_MARK_NOT_ON);
        List<InventorySurplusDto> surplusList = inventoryFeignClient.getSurplusList(dto);
        // 写入表头
        List<FormFieldCO> assetHeadList = new ArrayList<>();
        FormFieldCO inventoryTime = new FormFieldCO();
        inventoryTime.setFieldCode("createTime");
        inventoryTime.setFieldName("盘点时间");
        inventoryTime.setFieldType(FormFieldCO.DATETIME);
        inventoryTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd HH:mm:ss"));
        assetHeadList.add(inventoryTime);

        FormFieldCO actualInventoryUserText = new FormFieldCO();
        actualInventoryUserText.setFieldCode("reporterText");
        actualInventoryUserText.setFieldProps(new JSONObject());
        actualInventoryUserText.setFieldName("盘点人");
        assetHeadList.add(actualInventoryUserText);

        assetHeadList.addAll(assetHeadView);
        excelExportDto.setHeaderData(assetHeadFormFieldToMap(assetHeadList));
        // 属性缓存
        Cache cache = localFormFieldTransCache(assetHeadList);
        dictConvertUtil.convertToDictionary(surplusList);
        List<JSONObject> excelRows = surplusList.stream().map(f -> {
            JSONObject assetData = Optional.of(f.getAssetData()).orElse(new JSONObject());
            String jackson;
            try {
                jackson = JacksonConverter.MAPPER.writeValueAsString(f);
            } catch (JsonProcessingException e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "盘点资产数据异常");
            }
            JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
            json.remove("assetData");
            assetData.putAll(json);
            convertExcelData(assetData, cache);
            return assetData;
        }).collect(Collectors.toList());
        excelExportDto.setRows(excelRows);
        return excelExportDto;
    }

    private LinkedHashMap<String, String> assetHeadFormFieldToMap(List<FormFieldCO> assetHeadView) {
        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
        assetHeadView.forEach(f -> {
            if (StrUtil.isNotEmpty(f.getTranslationCode())) {
                headerData.put(f.getTranslationCode(), f.getFieldName());
            } else {
                headerData.put(f.getFieldCode(), f.getFieldName());
            }
        });
        return headerData;
    }

    private LinkedHashMap<String, String> assetHeadToMap(List<AssetHeadDto> assetHeadView) {
        LinkedHashMap<String, String> headerData = new LinkedHashMap<>();
        assetHeadView.forEach(f -> {
            if (StrUtil.isNotEmpty(f.getTranslationCode())) {
                headerData.put(f.getTranslationCode(), f.getName());
            } else {
                headerData.put(f.getCode(), f.getName());
            }
        });
        return headerData;
    }

}
