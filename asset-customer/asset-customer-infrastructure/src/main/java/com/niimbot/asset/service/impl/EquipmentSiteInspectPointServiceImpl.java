package com.niimbot.asset.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.annotation.ExcelField;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.service.EquipmentSiteInspectPointService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.AreaFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectPointFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.equipment.EquipmentSiteInspectPointImportDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableImportResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.OrgDto;

import org.apache.catalina.manager.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Primary
@Service
public class EquipmentSiteInspectPointServiceImpl implements EquipmentSiteInspectPointService {

    @Autowired
    private EquipmentSiteInspectPointFeignClient feignClient;

    @Autowired
    private RedisService redisService;

    @Autowired
    private OrgFeignClient orgFeignClient;

    @Autowired
    private ImportService importService;

    @Autowired
    private ImportTaskFeignClient importTaskFeignClient;

    @Autowired
    private AreaFeignClient areaFeignClient;

    private LinkedHashMap<String, String> tableHeader;

    {
        tableHeader = new LinkedHashMap<>();
        tableHeader.putAll(ImmutableMap.<String, String>builder()
                .put("所属公司", "orgName")
                .put("点位名称", "pointName")
                .put("点位编码", "pointCode")
                .put("所属区域", "pidName")
                .put("具体位置", "specificLocation")
                .put("点位描述", "pointDesc")
                .build());
    }

    @Data
    @AllArgsConstructor
    public static class OrgExport {
        @ExcelField(header = "公司编码", ordinal = 1)
        public String orgCode;

        @ExcelField(header = "公司名称", ordinal = 2)
        public String orgName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(2);
            map.put("公司编码", "orgCode");
            map.put("公司名称", "orgName");
            return map;
        }
    }

    protected static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    protected static ThreadLocal<EquipmentSiteInspectPointServiceImpl.GlobalCache> globalCache = new TransmittableThreadLocal<>();

    @Override
    public ExcelWriter buildWriter() {
        ClassPathResource templateSource = new ClassPathResource("/excelTemplate/inspect_point_template.xlsx");
        try (InputStream inputStream = templateSource.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            writer.setSheet("区域");
            List<AreaDto> areaAll = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(true));
            LinkedHashMap<String, String> areaHead = ExcelUtils.buildExcelHead(AreaServiceImpl.AreaExport.class);
            areaHead.forEach(writer::addHeaderAlias);
            List<AreaServiceImpl.AreaExport> areaData = areaAll.stream().map(area ->
                            new AreaServiceImpl.AreaExport(area.getAreaCode(), area.getAreaName()))
                    .collect(Collectors.toList());
            writer.setOnlyAlias(true);
            writer.write(areaData);
            writer.autoSizeColumnAll();

            writer.setSheet("所属公司");
            List<OrgDto> orgs = orgFeignClient.areaPermsList();
            LinkedHashMap<String, String> orgHead = ExcelUtils.buildExcelHead(AreaServiceImpl.OrgExport.class);
            orgHead.forEach(writer::addHeaderAlias);
            List<AreaServiceImpl.OrgExport> orgData = orgs.stream().map(org ->
                            new AreaServiceImpl.OrgExport(org.getOrgCode(), org.getOrgName()))
                    .collect(Collectors.toList());
            writer.setOnlyAlias(true);
            writer.write(orgData);
            writer.autoSizeColumnAll();
            return writer;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ExcelWriter writer = buildWriter();
        try (OutputStream out = response.getOutputStream()) {
            String fileName = "巡检点位导入模板.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
        ExcelReader reader = ExcelUtil.getReader(stream);
        reader.setHeaderAlias(tableHeader);
        // 校验表头
        List<List<Object>> read = reader.read(1, 1);
        if (read.size() > 0) {
            List<Object> header = ExcelUtils.clearEmpty(read.get(0));
            if (header.size() != tableHeader.size()) {
                throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
            }
            header.forEach(it -> {
                if (!tableHeader.containsValue(Convert.toStr(it))) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                }
            });
        } else {
            throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
        }
        // 读取数据并校验
        List<EquipmentSiteInspectPointImportDto> list = reader.read(1, 1, EquipmentSiteInspectPointImportDto.class);
        List<EquipmentSiteInspectPointServiceImpl.OrgExport> orgExports = new ArrayList<>();
        Edition.weixin(() -> {
            List<EquipmentSiteInspectPointServiceImpl.OrgExport> readOrg = reader.setSheet("所属公司").setHeaderAlias(EquipmentSiteInspectPointServiceImpl.OrgExport.headerMap()).read(0, 1, EquipmentSiteInspectPointServiceImpl.OrgExport.class);
            orgExports.addAll(readOrg);
            readOrg.forEach(v -> {
                EquipmentSiteInspectPointServiceImpl.OrgExport copy = BeanUtil.copyProperties(v, EquipmentSiteInspectPointServiceImpl.OrgExport.class);
                copy.setOrgName(copy.getOrgName() + "（" + copy.getOrgCode() + "）");
                orgExports.add(copy);
            });
        });
        this.importExcel(null, list, fileName, fileSize, companyId, true, orgExports);
    }

    private void importExcel(Long taskId,
                             List<EquipmentSiteInspectPointImportDto> read,
                             String fileName,
                             Long fileSize,
                             Long companyId,
                             boolean async,
                             List<EquipmentSiteInspectPointServiceImpl.OrgExport> orgExports) {
        // 判断是否超过最大上传条数，一次限制1000
        if (read.size() > MAX_BATCH) {
            throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "巡检点位", Convert.toStr(MAX_BATCH));
        }
        // 删除历史导入信息
        if (taskId != null) {
            feignClient.importErrorDeleteAll(taskId);
        }
        EquipmentSiteInspectPointServiceImpl.GlobalCache global = new EquipmentSiteInspectPointServiceImpl.GlobalCache().setCompany(companyId);
        globalCache.set(global);
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto();
                importCache.setFileName(fileName);
                importCache.setImportType(DictConstant.IMPORT_TYPE_INSPECT_POINT);
                importCache.setFileSize(fileSize);
                importCache.setCount(read.size());
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(taskId, read, importCache, orgExports);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto();
            importCache.setFileName(fileName);
            importCache.setImportType(DictConstant.IMPORT_TYPE_INSPECT_POINT);
            importCache.setFileSize(fileSize);
            importCache.setCount(read.size());
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(taskId, read, importCache, orgExports);
        }
    }

    private void startImport(Long taskId, List<EquipmentSiteInspectPointImportDto> read, ImportDto importCache, List<EquipmentSiteInspectPointServiceImpl.OrgExport> orgExports) {
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (taskId != null) {
            importTaskDto = importTaskFeignClient.queryById(taskId);
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, globalCache.get().getCompany()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
                Edition.weixin(() -> redisService.set("weixin:import:inspectPoint:add:org_id_trans:" + id, JSONObject.toJSONString(orgExports), 3, TimeUnit.DAYS));
            }

            // 写入表头数据
            this.saveLuckySheetHead(importTaskDto.getId());
            // 记录一行错误信息，生成LuckySheet格式数据
            AtomicInteger successNum = new AtomicInteger(0);
            importService.sendInspectPoint(globalCache.get().getCompany());
            // 循环处理行数据
            List<OrgDto> orgs = orgFeignClient.areaPermsList();
            Map<String, List<Long>> orgMap = new HashMap<>();
            orgs.forEach(o -> {
                String orgName = o.getOrgName();
                List<Long> orgIds = orgMap.getOrDefault(orgName, new ArrayList<>());
                orgIds.add(o.getId());
                orgMap.put(orgName, orgIds);

                String orgNameAndCode = o.getOrgName() + "（" + o.getOrgCode() + "）";
                List<Long> orgCodeIds = orgMap.getOrDefault(orgNameAndCode, new ArrayList<>());
                orgCodeIds.add(o.getId());
                orgMap.put(orgNameAndCode, orgCodeIds);
            });

            List<AreaDto> areaDtos = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(true));
            Map<Long, Map<String, List<Long>>> areaMap = new HashMap<>();
            areaDtos.forEach(o -> {
                Map<String, List<Long>> orgAreaMap = areaMap.getOrDefault(o.getOrgId(), new HashMap<>());
                String areaName = o.getAreaName();
                List<Long> areaIds = orgAreaMap.getOrDefault(areaName, new ArrayList<>());
                areaIds.add(o.getId());
                orgAreaMap.put(areaName, areaIds);

                String areaNameAndCode = o.getAreaName() + "（" + o.getAreaCode() + "）";
                List<Long> areaCodeIds = orgAreaMap.getOrDefault(areaNameAndCode, new ArrayList<>());
                areaCodeIds.add(o.getId());
                orgAreaMap.put(areaNameAndCode, areaCodeIds);

                areaMap.put(o.getOrgId(), orgAreaMap);
            });

            Edition.weixin(() -> {
                orgMap.clear();
                Map<String, Long> codeIdMap = orgs.stream().collect(Collectors.toMap(OrgDto::getOrgCode, OrgDto::getId));
                orgExports.forEach(v -> {
                    if (!codeIdMap.containsKey(v.getOrgCode())) {
                        return;
                    }
                    List<Long> ids = orgMap.getOrDefault(v.getOrgName(), new ArrayList<>());
                    ids.add(codeIdMap.get(v.getOrgCode()));
                    orgMap.put(v.getOrgName(), ids);
                });
            });

            ImportTaskDto finalImportTaskDto = importTaskDto;
            IntStream.range(0, read.size()).forEach(idx -> {
                EquipmentSiteInspectPointImportDto importDto = read.get(idx);
                importDto.setTaskId(finalImportTaskDto.getId());
                importDto.setErrorNum(0);
                List<LuckySheetModel> luckySheetModelRow = new ArrayList<>();
                // 调用validator
                Set<ConstraintViolation<EquipmentSiteInspectPointImportDto>> validate = VALIDATOR.validate(importDto);
                Map<String, String> validateData = validate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));

                // 所属公司
                LuckySheetModel orgName = validateTransField(idx + 1 - successNum.get(), 0, importDto.getOrgName(), importDto, "orgName", validateData, orgMap, null);
                luckySheetModelRow.add(orgName);

                // 点位名称
                LuckySheetModel pointName = validateField(idx + 1 - successNum.get(), 1, importDto.getPointName(), importDto, "pointName", validateData);
                luckySheetModelRow.add(pointName);

                // 点位编码
                LuckySheetModel pointCode = validateField(idx + 1 - successNum.get(), 2, importDto.getPointCode(), importDto, "pointCode", validateData);
                luckySheetModelRow.add(pointCode);

                // 上级区域编码
                LuckySheetModel pidName = validateTransField(idx + 1 - successNum.get(), 3, importDto.getPidName(), importDto, "pidName", validateData, null, areaMap.get(importDto.getOrgId()));
                luckySheetModelRow.add(pidName);

                // 具体位置
                LuckySheetModel specificLocation = validateField(idx + 1 - successNum.get(), 4, importDto.getSpecificLocation(), importDto, "specificLocation", validateData);
                luckySheetModelRow.add(specificLocation);

                // 点位描述
                LuckySheetModel pointDesc = validateField(idx + 1 - successNum.get(), 5, importDto.getPointDesc(), importDto, "pointDesc", validateData);
                luckySheetModelRow.add(pointDesc);

                importDto.setSheetModelList(luckySheetModelRow);

                Boolean success = feignClient.saveSheetData(importDto);
                if (BooleanUtil.isTrue(success)) {
                    successNum.getAndIncrement();
                }
                importService.sendInspectPoint(globalCache.get().getCompany());
            });
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_SITE_INSPECT_POINT, new AuditableImportResult(successNum.get())));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, globalCache.get().getCompany()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.INSPECT_POINT, globalCache.get().getCompany()), "finish", true);
            importService.sendInspectPoint(globalCache.get().getCompany());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.INSPECT_POINT, globalCache.get().getCompany());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
                if (Edition.isWeixin() && importTaskDto.getTaskStatus() == DictConstant.IMPORT_STATUS_SUCC) {
                    redisService.del("weixin:import:inspectPoint:add:org_id_trans:" + importTaskDto.getId());
                }
            }
            this.clearThreadLocal();
        }
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        return feignClient.importError(taskId);
    }

    @Override
    public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        List<Object> header = sheetModels.get(0);
        // 去除表头尾部空列
        int lastIndex = header.size();
        for (int i = header.size() - 1; i >= 0; i--) {
            if (StrUtil.isBlank(Convert.toStr(header.get(i)))) {
                lastIndex = i;
            } else {
                break;
            }
        }
        header = header.subList(0, lastIndex);
        List<EquipmentSiteInspectPointImportDto> areaList = new ArrayList<>();
        for (int i = 1; i < sheetModels.size(); i++) {
            List<Object> data = sheetModels.get(i);
            EquipmentSiteInspectPointImportDto pointImportDto = new EquipmentSiteInspectPointImportDto();
            for (int j = 0; j < header.size(); j++) {
                String head = tableHeader.get(Convert.toStr(header.get(j)));
                if (StrUtil.isBlank(head)) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                } else {
                    if (j < data.size()) {
                        ReflectUtil.setFieldValue(pointImportDto, head, data.get(j));
                    }
                }
            }
            areaList.add(pointImportDto);
        }
        List<EquipmentSiteInspectPointServiceImpl.OrgExport> orgExports = new ArrayList<>();
        Edition.weixin(() -> {
            Object cache = redisService.get("weixin:import:inspectPoint:add:org_id_trans:" + taskId);
            if (Objects.isNull(cache)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "文件已过期，请重新导入");
            }
            orgExports.addAll(JSONArray.parseArray(cache.toString(), EquipmentSiteInspectPointServiceImpl.OrgExport.class));
        });
        this.importExcel(taskId, areaList, "点位在线编辑保存", 0L, companyId, false, orgExports);
        return true;
    }

    @Override
    public Boolean importErrorDelete(Long taskId) {
        return feignClient.importErrorDeleteAll(taskId);
    }

    private void saveLuckySheetHead(Long taskId) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new ArrayList<>();
        AtomicInteger cellIndex = new AtomicInteger(0);
        List<String> must = ListUtil.of("orgName", "pointName", "pointCode", "pidName", "specificLocation");
        tableHeader.forEach((k, v) -> {
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(k);
            if (must.contains(v)) {
                modelV.setFc("#ff0000");
            }
            headModelList.add(luckySheetModel);
        });
        importErrorDto.setHeadModelList(headModelList);
        this.feignClient.saveSheetHead(importErrorDto);

    }

    protected LuckySheetModel validateField(int r, int c, String data, EquipmentSiteInspectPointImportDto importDto, String errorCode, Map<String, String> error) {
        LuckySheetModel model = new LuckySheetModel();
        LuckySheetModel.Value modelV = model.getV();
        model.setR(r).setC(c);
        modelV.setV(StrUtil.trim(data));
        if (error.containsKey(errorCode)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(error.get(errorCode));
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
        return model;
    }

    protected LuckySheetModel validateTransField(int r, int c, String data,
                                                 EquipmentSiteInspectPointImportDto importDto,
                                                 String errorCode,
                                                 Map<String, String> error,
                                                 Map<String, List<Long>> orgMap,
                                                 Map<String, List<Long>> areaMap) {
        String trim = StrUtil.trim(data);
        LuckySheetModel model = new LuckySheetModel();
        LuckySheetModel.Value modelV = model.getV();
        model.setR(r).setC(c);
        modelV.setV(trim);
        if (error.containsKey(errorCode)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(error.get(errorCode));
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
        if (StrUtil.isNotEmpty(trim)) {
            if (CollUtil.isNotEmpty(orgMap)) {
                checkOrg(trim, orgMap, importDto, model);
            }
            if (CollUtil.isNotEmpty(areaMap)) {
                checkArea(trim, areaMap, importDto, model);
            }
        }
        return model;
    }

    private void checkOrg(String orgName, Map<String, List<Long>> orgMap, EquipmentSiteInspectPointImportDto importDto, LuckySheetModel model) {
        String errMsg = null;
        List<Long> ids;
        if (orgMap.containsKey(orgName)) {
            // 1.优先全匹配文本
            ids = orgMap.get(orgName);
        } else {
            // 2.匹配括号内容是否大写字符加数字类型
            ids = orgMap.get(ExcelUtils.matchCodeAndReplace(orgName));
        }
        if (CollUtil.isEmpty(ids)) {
            errMsg = "当前数据不存在，请先添加";
        } else if (ids.size() > 1) {
            errMsg = "当前数据有重名，请输入组织名称和组织编码，示例：销售部（001）";
        } else {
            importDto.setOrgId(ids.get(0));
        }
        if (StrUtil.isNotBlank(errMsg)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(errMsg);
            LuckySheetModel.Value modelV = model.getV();
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
    }

    private void checkArea(String areaName, Map<String, List<Long>> areaMap, EquipmentSiteInspectPointImportDto importDto, LuckySheetModel model) {
        String errMsg = null;
        List<Long> ids;
        if (areaMap.containsKey(areaName)) {
            // 1.优先全匹配文本
            ids = areaMap.get(areaName);
        } else {
            // 2.匹配括号内容是否大写字符加数字类型
            ids = areaMap.get(ExcelUtils.matchCodeAndReplace(areaName));
        }
        if (CollUtil.isEmpty(ids)) {
            errMsg = "当前数据不存在，请先添加";
        } else if (ids.size() > 1) {
            errMsg = "当前数据有重名，请输入区域名称和区域编码，示例：默认区域（001）";
        } else {
            importDto.setPid(ids.get(0));
        }
        if (StrUtil.isNotBlank(errMsg)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(errMsg);
            LuckySheetModel.Value modelV = model.getV();
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
    }


    /**
     * 清理本地缓存
     */
    protected void clearThreadLocal() {
        globalCache.remove();
    }

    @Data
    @Accessors(chain = true)
    protected static class GlobalCache {
        private Long company;
    }
}
