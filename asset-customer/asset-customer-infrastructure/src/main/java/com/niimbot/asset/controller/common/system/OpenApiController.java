package com.niimbot.asset.controller.common.system;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.openapi.dto.AccessTokenDto;
import com.niimbot.asset.openapi.dto.GetAccessTokenDto;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.OpenApiFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/9/4 15:03
 */
@Slf4j
@Api(tags = "OpenApi登录管理")
@ResultController
@RequestMapping("api/common/openApi")
public class OpenApiController {

    @Autowired
    private CusLoginService loginService;

    @Autowired
    private OpenApiFeignClient openApiFeignClient;

    @Autowired
    private AccountCenterFeignClient accountCenterFeignClient;

    @Autowired
    private AbstractTokenService tokenService;

    @PostMapping("/login")
    public AccessTokenDto login(@Validated @RequestBody GetAccessTokenDto getAccessTokenDto) {
        return loginService.loginByOpenApi(getAccessTokenDto);
    }

    @PostMapping("/openApi")
    public Boolean add(@Validated @RequestBody OpenApiCreateDto createDto) {
        return openApiFeignClient.add(createDto);
    }

    @PostMapping("/oauth2.0/getAuthCode")
    public Map<String, Object> getAuthCode(@RequestBody @Validated OpenApiAuthCodeDto authCodeDto) {
        log.info("get AuthCode params = [{}]", authCodeDto);
        String redirectUrl = openApiFeignClient.getAuthCode(authCodeDto);
        log.info("get redirectUrl [{}]", redirectUrl);
        return MapUtil.of("redirectUrl", redirectUrl);
    }

    @PostMapping("/oauth2.0/getReverseAuthCode")
    public Map<String, Object> getReverseAuthCode(@RequestBody @Validated OpenApiReverseAuthDto authDto) {
        log.info("get authDto params = [{}]", authDto);
        OpenApiDto openApiDto = openApiFeignClient.getOpenApiByAgentId(Long.valueOf(authDto.getAgentId()));
        OpenApiAuthCodeDto openApiAuthCodeDto = new OpenApiAuthCodeDto();
        openApiAuthCodeDto.setClientId(openApiDto.getAppKey());
        openApiAuthCodeDto.setRedirectUrl(openApiDto.getRedirectUrl());
        openApiAuthCodeDto.setResponseType(authDto.getResponseType());
        openApiAuthCodeDto.setState(authDto.getState());
        LoginUserDto loginUserDto = new LoginUserDto();
        CusUserDto cusUser = accountCenterFeignClient.getLoginInfoByWay(authDto.getLoginName());
        loginUserDto.setCusUser(cusUser);
        openApiAuthCodeDto.setLoginUser(loginUserDto);
        String code = openApiFeignClient.getAuthCode(openApiAuthCodeDto);
        return MapUtil.of("code", code);
    }


    @PostMapping("/oauth2.0/reverseAuthByCode")
    public Map<String, Object> reverseAuthByCode(@RequestBody @Validated OpenApiReverseCodeDto codeDto) {
        log.info("get codeDto params = [{}]", codeDto);
        OpenApiDto openApiDto = openApiFeignClient.getOpenApiByAgentId(Long.valueOf(codeDto.getAgentId()));
        OpenApiGetUserInfoDto getUserInfoDto = new OpenApiGetUserInfoDto();
        getUserInfoDto.setCode(codeDto.getCode());
        getUserInfoDto.setAppKey(openApiDto.getAppKey());
        getUserInfoDto.setCompanyId(openApiDto.getCompanyId());
        OpenApiUserInfoDto userInfoDto = openApiFeignClient.getUserInfoByReverseCode(getUserInfoDto);
        LoginUserDto loginUserDto = new LoginUserDto();
        CusUserDto cusUser = accountCenterFeignClient.getLoginInfoByWay(userInfoDto.getEmpNo());
        loginUserDto.setCusUser(cusUser);
        Map<String, Object> map  = MapUtil.of("redirectUrl", codeDto.getRedirectUrl());
        map.put("token", tokenService.createToken(loginUserDto));
        return map;
    }

    @PutMapping
    public Boolean edit() {
        return true;
    }

    @DeleteMapping
    public Boolean remove() {
        return true;
    }

}