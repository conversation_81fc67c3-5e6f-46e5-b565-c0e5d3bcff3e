package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaImportDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.system.AreaAssetDto;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.PrintAreaPageQueryDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20201111
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AreaFeignClient {
    /**
     * 添加资产区域
     *
     * @param area
     * @return true/false
     */
    @PostMapping(value = "server/mean/area")
    Long insertArea(AreaDto area);

    /**
     * 修改资产区域
     * @param area
     * @return true/false
     */

    @PutMapping(value = "server/mean/area", produces = MediaType.APPLICATION_JSON_VALUE)
    boolean updateArea(AreaDto area);

    /**
     * 删除资产区域
     *
     * @param ids
     * @return
     */
    @DeleteMapping(value = "server/mean/area", produces = MediaType.APPLICATION_JSON_VALUE)
    List<AuditableOperateResult> deleteAreaByIds(List<Long> ids);

    /**
     * 查询获取资产区域
     *
     * @param id
     * @return AreaDto信息
     */
    @GetMapping(value = "server/mean/area/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    AreaDto selectAreaById(@PathVariable("id") Long id);

    /**
     * 分页查询区域列表
     *
     * @param queryDto area参数
     * @return pageUtils对象
     */
    @GetMapping(value = "server/mean/area/page")
    PageUtils<AreaDto> selectAreaPage(@SpringQueryMap AreaQueryDto queryDto);

    @GetMapping("server/mean/area/printAreaPage")
    PageUtils<AreaAssetDto> printAreaPage(@SpringQueryMap PrintAreaPageQueryDto dto);

    @GetMapping("server/mean/area/printAreaIds")
    List<Long> printAreaIds(@SpringQueryMap PrintAreaPageQueryDto dto);

    @GetMapping("server/mean/area/root/node/list/{companyId}")
    List<AreaDto> rootNodeList(@PathVariable("companyId") Long companyId);

    /**
     * 查询区域列表list集合
     *
     * @param queryDto 参数实体
     * @return 地区列表
     */
    @GetMapping(value = "server/mean/area/list")
    List<AreaDto> areaList(@SpringQueryMap AreaQueryDto queryDto);

    /**
     * 获取推荐区域编码
     *
     * @return 编码
     */
    @GetMapping(value = "server/mean/area/recommendCode")
    String recommendCode();

    /**
     * 根据区域id集合查询区域
     *
     * @param ids 查询参数
     * @return 区域
     */
    @PostMapping(value = "server/mean/area/listByIds")
    List<AreaDto> listByIds(List<Long> ids);

    /**
     * 排序
     *
     * @param areaIds 查询参数
     * @return 区域
     */
    @PutMapping(value = "server/mean/area/sort")
    Boolean sort(@RequestBody List<Long> areaIds);


    @GetMapping(value = "server/mean/area/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @DeleteMapping(value = "server/mean/area/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/mean/area/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/mean/area/saveSheetData")
    Boolean saveSheetData(AreaImportDto importDto);

}
