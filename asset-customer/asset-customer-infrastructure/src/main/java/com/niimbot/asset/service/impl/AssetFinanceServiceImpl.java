package com.niimbot.asset.service.impl;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.customer.oss.FileUploadService;
import com.niimbot.asset.framework.annotation.ExcelField;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.OrderTypeNewEnum;
import com.niimbot.asset.service.AssetFinanceService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.excel.AbstractExcelExportService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.CategoryFeignClient;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.feign.finance.AssetFinanceInfoFeignClient;
import com.niimbot.asset.utils.TimeUtil;
import com.niimbot.finance.AssetFinanceImportDto;
import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.finance.AssetFinanceInfoQueryDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/19 下午3:57
 */
@Slf4j
@Service
public class AssetFinanceServiceImpl extends AbstractExcelExportService implements AssetFinanceService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    private static LinkedHashMap<String, String> excelHeader = new LinkedHashMap<>(ImmutableMap.<String, String>builder()
            .put("资产编码", "assetCode")
            .put("资产名称", "assetName")
            .put("资产分类", "assetCategoryDesc")
            .put("折旧算法", "strategyName")
            .put("所属公司", "orgName")
            .put("入账类型", "settleTypeDesc")
            .put("分摊部门", "settleOrgName")
            .put("入账日期", "billDate")
            .put("本币原值", "originPrice")
            .put("可抵扣税额", "tax")
            .put("预计残值率（%）", "preResidualRate")
            .put("预计使用期间数", "preSettleCount")
            .put("已折旧期间数", "settleCount")
            .put("累计折旧", "accumulationAmount")
            .put("备注", "remark")
            .build());

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    private final RedisService redisService;

    private final ImportService importService;

    private final ImportTaskFeignClient importTaskFeignClient;

    private final AssetFinanceInfoFeignClient financeInfoFeignClient;

    private final CategoryFeignClient categoryFeignClient;
    private final OrgFeignClient orgFeignClient;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    public AssetFinanceServiceImpl(RedisService redisService,
                                   ImportService importService,
                                   ImportTaskFeignClient importTaskFeignClient,
                                   AssetFinanceInfoFeignClient financeInfoFeignClient,
                                   CategoryFeignClient categoryFeignClient, OrgFeignClient orgFeignClient) {
        super(importTaskFeignClient);
        this.redisService = redisService;
        this.importService = importService;
        this.importTaskFeignClient = importTaskFeignClient;
        this.financeInfoFeignClient = financeInfoFeignClient;
        this.categoryFeignClient = categoryFeignClient;
        this.orgFeignClient = orgFeignClient;
    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
        ExcelReader reader = ExcelUtil.getReader(stream);
        reader.setHeaderAlias(excelHeader);

        //读取表头
        List<List<Object>> headerRow = reader.read(1, 1);

        //校验excel列
        if (CollUtil.isNotEmpty(headerRow)) {
            List<Object> headerCell = headerRow.get(0);
            //校验表头列个数
            if (CollUtil.isEmpty(headerCell) || headerCell.size() != excelHeader.size()) {
                throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
            }

            //校验表头列名是否都对应
            headerCell.forEach(item -> {
                if (!excelHeader.containsValue(Convert.toStr(item))) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                }
            });
        } else {
            throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
        }

        //读取数据
        List<AssetFinanceImportDto> rowDataList = reader.read(1, 1, AssetFinanceImportDto.class);
        //校验空数据文件
        if (CollUtil.isEmpty(rowDataList)) {
            throw new BusinessException(SystemResultCode.IMPORT_ERROR, "导入文件数据为空");
        }
        List<OrgExport> orgExports = new ArrayList<>();
        List<OrgExport> orgExportList = new ArrayList<>();
        Edition.weixin(() -> {
            orgExports.addAll(reader.setSheet("所属组织").setHeaderAlias(OrgExport.headerMap()).read(0, 1, OrgExport.class));
            orgExportList.addAll(orgExports);

            for (OrgExport item : orgExports) {
                OrgExport orgExport = new OrgExport(item.getOrgCode(), item.getOrgType(), item.getOrgName() + "（" + item.getOrgCode() + "）");
                OrgExport orgExportLow = new OrgExport(item.getOrgCode(), item.getOrgType(), item.getOrgName() + "(" + item.getOrgCode() + ")");
                orgExportList.add(orgExport);
                orgExportList.add(orgExportLow);
            }
        });
        importData(null, rowDataList, fileName, fileSize, companyId, Boolean.TRUE, orgExportList);
    }

    private void importData(Long taskId,
                            List<AssetFinanceImportDto> rowDataList,
                            String fileName,
                            Long fileSize,
                            Long companyId,
                            boolean async,
                            List<OrgExport> orgExports) {
        // 判断是否超过最大上传条数，一次限制5000
        if (CollUtil.isEmpty(rowDataList) || rowDataList.size() > MAX_BATCH) {
            throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "资产入账", Convert.toStr(MAX_BATCH));
        }
        // 删除历史导入信息
        if (taskId != null) {
            categoryFeignClient.importErrorDeleteAll(taskId);
        }
        if (async) {
            //异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto();
                importCache.setFileName(fileName);
                importCache.setImportType(DictConstant.IMPORT_TYPE_ASSET_FINANCE);
                importCache.setFileSize(fileSize);
                importCache.setCount(rowDataList.size());
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(companyId, taskId, rowDataList, importCache, orgExports);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto();
            importCache.setFileName(fileName);
            importCache.setImportType(DictConstant.IMPORT_TYPE_ASSET_FINANCE);
            importCache.setFileSize(fileSize);
            importCache.setCount(rowDataList.size());
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(companyId, taskId, rowDataList, importCache, orgExports);
        }
    }

    private void startImport(Long companyId, Long taskId, List<AssetFinanceImportDto> read, ImportDto importCache, List<OrgExport> orgExports) {
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (Objects.nonNull(taskId)) {
            importTaskDto = importTaskFeignClient.queryById(taskId);
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId),
                    (JSONObject) JSON.toJSON(importCache));
            //创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
                Edition.weixin(() -> redisService.set("weixin:import:finance:org_id_trans:" + id, JSONObject.toJSONString(orgExports), 3, TimeUnit.DAYS));
            }

            Map<String, List<OrgExport>> orgGroup = orgExports.stream().collect(Collectors.groupingBy(OrgExport::getOrgName));

            //写入表头数据
            this.saveLuckySheetHead(importTaskDto.getId());
            // 记录一行错误信息，生成LuckySheet格式数据
            AtomicInteger successNum = new AtomicInteger(0);
            importService.sendAssetFinance(companyId);
            // 循环处理行数据
            ImportTaskDto finalImportTaskDto = importTaskDto;
            IntStream.range(0, read.size()).forEach(idx -> {
                AssetFinanceImportDto rowData = read.get(idx);
                rowData.setTaskId(finalImportTaskDto.getId());
                //给到start模块使用
                rowData.setCompanyId(companyId);
                rowData.setErrorNum(0);

                // 调用validator做参数校验
                Set<ConstraintViolation<AssetFinanceImportDto>> validate = VALIDATOR.validate(rowData);
                Map<String, String> validateData = validate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));

                //校验每行数据，生成LuckSheetModel
                rowData.setSheetModelList(validateRow(idx + 1 - successNum.get(), rowData, validateData));

                Edition.weixin(() -> {
                    String settleOrgName = rowData.getSettleOrgName();
                    String settleOrgNameLow = rowData.getSettleOrgName();
                    boolean containOrgCode = ExcelUtils.matchCode(rowData.getSettleOrgName());
                    if (containOrgCode) {
                        settleOrgName = rowData.getSettleOrgName().replace("(", "（");
                        settleOrgName = settleOrgName.replace(")", "）");

                        settleOrgNameLow = rowData.getSettleOrgName().replace("（", "(");
                        settleOrgNameLow = settleOrgNameLow.replace("）", ")");
                    }
                    if (orgGroup.containsKey(rowData.getOrgName())) {
                        rowData.setOrgCode(orgGroup.get(rowData.getOrgName()).get(0).getOrgCode());
                    }

                    //为了解决组织名称本身带有括号的问题
                    if (orgGroup.containsKey(settleOrgName)) {
                        rowData.setSettleOrgCode(orgGroup.get(settleOrgName).get(0).getOrgCode());
                    } else if (orgGroup.containsKey(settleOrgNameLow)) {
                        rowData.setSettleOrgCode(orgGroup.get(settleOrgNameLow).get(0).getOrgCode());
                    }
                });

                Boolean success = financeInfoFeignClient.saveImportData(rowData);
                log.info("assetFinanceService startImport result=[{}]", success);
                if (BooleanUtil.isTrue(success)) {
                    successNum.getAndIncrement();
                }
                importService.sendAssetFinance(companyId);
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId), "finish", true);
            importService.sendAssetFinance(companyId);
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.ASSET_FINANCE, companyId);
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
                if (Edition.isWeixin() && importTaskDto.getTaskStatus() == DictConstant.IMPORT_STATUS_SUCC) {
                    redisService.del("weixin:import:finance:org_id_trans:" + importTaskDto.getId());
                }
            }
        }
    }

    private void saveLuckySheetHead(Long taskId) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new ArrayList<>();
        AtomicInteger cellIndex = new AtomicInteger(0);
        excelHeader.forEach((k, v) -> {
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(k);
            headModelList.add(luckySheetModel);
        });
        importErrorDto.setHeadModelList(headModelList);
        //保存表头数据
        financeInfoFeignClient.saveSheetHead(importErrorDto);

    }

    /**
     * 校验每行数据
     *
     * @param rowIndex
     * @param rowData
     * @param error
     * @return
     */
    private List<LuckySheetModel> validateRow(int rowIndex, AssetFinanceImportDto rowData, Map<String, String> error) {
        List<LuckySheetModel> result = new ArrayList<>();
        int cellIndex = 0;
        for (Map.Entry<String, String> entry : excelHeader.entrySet()) {
            String fieldValue = null;
            Object fieldValueObject = ReflectUtil.getFieldValue(rowData, entry.getValue());
            if (Objects.nonNull(fieldValueObject)) {
                if (fieldValueObject instanceof BigDecimal) {
                    fieldValue = ((BigDecimal) fieldValueObject).toString();
                } else if (fieldValueObject instanceof Integer) {
                    fieldValue = ((Integer) fieldValueObject).toString();
                } else {
                    if (entry.getValue().equalsIgnoreCase("billDate")) {
                        fieldValue = formatBillDate((String) fieldValueObject);
                    } else {
                        fieldValue = (String) fieldValueObject;
                    }
                }
            }
            LuckySheetModel luckySheetModel = validateField(rowIndex, cellIndex, fieldValue, rowData, entry.getValue(), error);
            cellIndex++;
            result.add(luckySheetModel);
        }
        return result;
    }

    /**
     * 格式化换处理excel读取时间问题
     *
     * @param billDate
     * @return
     */
    private String formatBillDate(String billDate) {
        if (StrUtil.isBlank(billDate)) {
            return billDate;
        }

        try {
            LocalDateTime billTime = TimeUtil.parseTime(billDate, "yyyy-M-d HH:mm:ss");
            if (Objects.nonNull(billTime)) {
                return TimeUtil.formatTime(billTime, "yyyy/MM/dd");
            }
        } catch (Exception e) {

        }
        return billDate;
    }

    private LuckySheetModel validateField(int r, int c, String data, AssetFinanceImportDto importDto, String errorCode, Map<String, String> error) {
        LuckySheetModel model = new LuckySheetModel();
        LuckySheetModel.Value modelV = model.getV();
        model.setR(r).setC(c);
        modelV.setV(StrUtil.trim(data));
        if (error.containsKey(errorCode)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(error.get(errorCode));
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
        return model;
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        return financeInfoFeignClient.importError(taskId);
    }

    @Override
    public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        List<Object> header = sheetModels.get(0);
        // 去除表头尾部空列
        int lastIndex = header.size();
        for (int i = header.size() - 1; i >= 0; i--) {
            if (StrUtil.isBlank(Convert.toStr(header.get(i)))) {
                lastIndex = i;
            } else {
                break;
            }
        }
        header = header.subList(0, lastIndex);
        List<AssetFinanceImportDto> rowDataList = new ArrayList<>();
        for (int i = 1; i < sheetModels.size(); i++) {
            List<Object> data = sheetModels.get(i);
            AssetFinanceImportDto rowData = new AssetFinanceImportDto();
            for (int j = 0; j < header.size(); j++) {
                String head = excelHeader.get(Convert.toStr(header.get(j)));
                if (StrUtil.isBlank(head)) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                } else {
                    if (j < data.size()) {
                        ReflectUtil.setFieldValue(rowData, head, data.get(j));
                    }
                }
            }
            rowDataList.add(rowData);
        }
        List<OrgExport> orgExports = new ArrayList<>();
        Edition.weixin(() -> {
            Object cache = redisService.get("weixin:import:finance:org_id_trans:" + taskId);
            if (Objects.isNull(cache)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "文件已过期，请重新导入");
            }
            orgExports.addAll(JSONArray.parseArray(cache.toString(), OrgExport.class));
        });
        this.importData(taskId, rowDataList, "资产入账在线编辑保存", 0L, companyId, false, orgExports);
        return true;
    }

    @Override
    public ExportResponse exportWaitAsset(Long orgId) {
        AssetFinanceInfoQueryDto queryDto = new AssetFinanceInfoQueryDto();
        queryDto.setOrgId(orgId);
        queryDto.setBillStatus(0);
        queryDto.setCurrent(1);
        queryDto.setSize(10);

        //查询待入账资产
        PageUtils<AssetFinanceInfoDto> waitAssetPage = financeInfoFeignClient.queryFinanceInfo(queryDto);
        if (Objects.isNull(waitAssetPage) || waitAssetPage.getTotalCount() == 0) {
            BusinessExceptionUtil.throwException("待入账资产无数据, 无法导出");
        }

        ExportResponse result = new ExportResponse();
        //导出参数信息，后续用于点击再导一次
        ExportParams exportParams = new ExportParams(LoginUserThreadLocal.getCompanyId(),
                getName(OrderTypeNewEnum.WAIT_FINANCE_ASSET), OrderTypeNewEnum.WAIT_FINANCE_ASSET.getValue());
        exportParams.setCount(waitAssetPage.getTotalCount());
        exportParams.setQueryCondition(JSONObject.parseObject(JSONObject.toJSONString(queryDto)));
        exportParams.setExportUrl(OrderTypeNewEnum.WAIT_FINANCE_ASSET.getDetailExportUrl());

        //大于1000条，异步导出
        if (waitAssetPage.getTotalCount() > 1000) {
            result.setSync(true);
            sync(exportParams, this::executeWaitAssetExport);
        } else {
            //小于1000条，同步导出
            result.setSync(false);
            result.setPath(async(() ->
                    executeWaitAssetExport(null, exportParams)
            ));
            if (StrUtil.isEmpty(result.getPath())) {
                throw new BusinessException(SystemResultCode.EXPORT_ERROR);
            }
        }
        return result;
    }

    private String executeWaitAssetExport(Long taskId, ExportParams exportParams) {
        //返回oss路径
        String ossPath = StrUtil.EMPTY;

        ClassPathResource templateSource = new ClassPathResource("/excelTemplate/finance_template.xlsx");

        //解析出参数
        AssetFinanceInfoQueryDto queryDto = JSONObject.parseObject(JSONObject.toJSONString(exportParams.getQueryCondition()), AssetFinanceInfoQueryDto.class);

        //文件名称
        String fileName = String.join("", OrderTypeNewEnum.WAIT_FINANCE_ASSET.getName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        File downloadFile = null;
        try (InputStream inputStream = templateSource.getInputStream()) {
            downloadFile = new File(getFilePath().getPath() + "/" + fileName);

            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            int totalSize = 0;
            int pageNum = 1;

            AssetFinanceInfoQueryDto queryParam = new AssetFinanceInfoQueryDto();
            queryParam.setOrgId(queryDto.getOrgId());
            queryParam.setCurrent(pageNum);
            queryParam.setSize(100);

            //查询待入账资产
            List<List<Object>> waitAssetList = financeInfoFeignClient.queryAssetWait(queryParam);
            while (CollUtil.isNotEmpty(waitAssetList)) {
                //设置excel起始行
                writer.setCurrentRow(1 + totalSize + 1);
                //写入数据到excel
                writer.write(waitAssetList);
                totalSize = totalSize + waitAssetList.size();

                pageNum++;
                queryParam.setCurrent(pageNum);
                waitAssetList = financeInfoFeignClient.queryAssetWait(queryParam);
            }

            CellStyle blackHeadStyle = writer.createCellStyle();
            blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
            blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            blackHeadStyle.setBorderBottom(BorderStyle.THIN);
            blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
            blackHeadStyle.setBorderTop(BorderStyle.THIN);
            blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
            blackHeadStyle.setBorderLeft(BorderStyle.THIN);
            blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
            blackHeadStyle.setBorderRight(BorderStyle.THIN);
            blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
            Font black = writer.createFont();
            black.setFontHeightInPoints((short) 13);
            black.setColor(IndexedColors.BLACK.getIndex());
            blackHeadStyle.setFont(black);

            for (int i = 2; i < totalSize + 2; i++) {
                for (int j = 0; j < 5; j++) {
                    Cell cell = writer.getCell(j, i);
                    if (cell != null) {
                        cell.setCellStyle(blackHeadStyle);
                    }
                }
            }

            writer.setSheet("所属组织");
            List<OrgDto> orgDtoList = orgFeignClient.list(new OrgQueryDto());
            LinkedHashMap<String, String> areaHead = ExcelUtils.buildExcelHead(OrgExport.class);
            areaHead.forEach(writer::addHeaderAlias);
            List<OrgExport> orgData = orgDtoList.stream().map(org ->
                            new OrgExport(org.getOrgCode(),
                                    org.getOrgType() == 1 ? "公司" : "部门", org.getOrgName()))
                    .collect(Collectors.toList());
            writer.setOnlyAlias(true);
            writer.write(orgData);
            writer.autoSizeColumnAll();
            writer.setDestFile(downloadFile);
            writer.flush();
            writer.close();

            //本地磁盘文件上传到oss
            ossPath = fileUploadService.putFile(getOssPath(fileName, exportParams), downloadFile.getPath());

            // 更新任务url
            if (Objects.nonNull(taskId) && StrUtil.isNotBlank(ossPath)) {
                ImportTaskDto importTaskUpdate = new ImportTaskDto();
                importTaskUpdate.setId(taskId).setUrl(ossPath);
                importTaskFeignClient.update(importTaskUpdate);
            }
        } catch (Exception e) {
            log.error("assetFinanceService executeWaitAssetExport exception ", e);
        } finally {
            //删除本地磁盘文件
            FileUtil.del(downloadFile);
        }
        return ossPath;
    }

    /**
     * 获取导出报表名称，在任务中心展示
     *
     * @param orderType
     * @return
     */
    private String getName(OrderTypeNewEnum orderType) {
        // 获取流水号
        int count = importTaskFeignClient.count(DictConstant.TASK_TYPE_EXPORT, orderType.getValue());
        String serialNo = StrUtil.padPre(String.valueOf(++count), 5, "0");
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return String.join("", orderType.getName(), currentTime, serialNo);
    }

    /**
     * 获取导出报表本地磁盘文件夹
     *
     * @return
     */
    private File getFilePath() {
        String currentTime = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        File tempPath = FileUtil.file(new File(fileUploadConfig.getTempPath()), "excelTemp", "finance", currentTime);
        // 文件夹不存在则创建
        if (!tempPath.exists()) {
            tempPath.mkdirs();
        }
        return tempPath;
    }

    /**
     * 获取oss路径
     *
     * @param fileName
     * @param exportParams
     * @return
     */
    private String getOssPath(String fileName, AbstractExcelExportService.ExportParams exportParams) {
        return String.join("/", String.valueOf(exportParams.getCompanyId()), "finance", DateUtil.format(DateUtil.date(),
                DatePattern.NORM_DATE_PATTERN), fileName);
    }

    @Data
    @AllArgsConstructor
    public static class OrgExport {
        @ExcelField(header = "组织编码", ordinal = 1)
        public String orgCode;
        @ExcelField(header = "组织类型", ordinal = 2)
        public String orgType;
        @ExcelField(header = "组织名称", ordinal = 3)
        public String orgName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(3);
            map.put("组织编码", "orgCode");
            map.put("组织类型", "orgType");
            map.put("组织名称", "orgName");
            return map;
        }

    }
}
