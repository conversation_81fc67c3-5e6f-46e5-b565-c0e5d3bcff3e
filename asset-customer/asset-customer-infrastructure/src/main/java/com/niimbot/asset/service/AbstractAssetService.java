package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.service.feign.AreaFeignClient;
import com.niimbot.asset.service.feign.AssetFeignClient;
import com.niimbot.asset.service.feign.AssetRelationFeignClient;
import com.niimbot.asset.service.feign.CategoryFeignClient;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.CusUserSettingFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.OrgFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.TimeUtil;
import com.niimbot.easydesign.form.dto.clientobject.DateTimeCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryQueryDto;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.CusUserOrgDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;

import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.CusUserOrgDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgQueryDto;

import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/1/19 13:49
 */
@Slf4j
public abstract class AbstractAssetService {

    protected static final String EXCLUDE_FIELD = "excludeField";

    protected static final int MAX_BATCH = 5000;

    @Autowired
    protected AssetFeignClient assetFeignClient;
    @Autowired
    protected CusEmployeeFeignClient employeeFeignClient;
    @Autowired
    protected CategoryFeignClient categoryFeignClient;
    @Autowired
    protected OrgFeignClient orgFeignClient;
    @Autowired
    protected AreaFeignClient areaFeignClient;
    @Autowired
    protected DictConvertUtil dictConvertUtil;
    @Autowired
    protected CusUserSettingFeignClient settingFeignClient;
    @Autowired
    protected FormFeignClient formFeignClient;
    @Autowired
    protected StandardFeignClient standardFeignClient;
    @Autowired
    protected AsAssetUtil assetUtil;
    @Autowired
    protected AssetRelationFeignClient assetRelationFeignClient;

    @Data
    @Accessors(chain = true)
    protected static class ImportInfo {
        private Long taskId;
        private String fileName;
        private Long fileSize;
        private Long companyId;
        private Long standardId;
        private List<List<Object>> read = new ArrayList<>();
        private Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
    }

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    protected static class IdNameCache {
        private String id;
        private String name;
        private Boolean hasPerm;
    }

    // asset-资产属性，standard-自定义属性
    protected static ThreadLocal<Map<String, List<FormFieldCO>>> formFieldMap = new TransmittableThreadLocal<>();

    protected List<FormFieldCO> getStandardAttr(Long standardId) {
        formFieldMap.set(new HashMap<>());
        // 查询录入标准数据项
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields = formFields.stream()
                .filter(attr -> !attr.isHidden())
                .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                        .contains(attr.getFieldType())).collect(Collectors.toList());
        formFieldMap.get().put("asset", formFields);
        // 查询录入的标准品扩展数据
        List<FormFieldCO> standardExtField = new ArrayList<>();
        if (ObjectUtil.isNotNull(standardId)) {
            standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            standardExtField = standardExtField.stream()
                    .filter(attr -> !attr.isHidden())
                    .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                            .contains(attr.getFieldType())).collect(Collectors.toList());
        }
        formFieldMap.get().put("standard", standardExtField);

        List<FormFieldCO> allField = new ArrayList<>();
        allField.addAll(formFields);
        allField.addAll(standardExtField);
        return allField;
    }

    /**
     * 下拉Name和ID映射，Map<字段名称, Map<名称, List<映射>>
     */
    protected static ThreadLocal<Map<String, Map<String, List<IdNameCache>>>> dropDownCache = new TransmittableThreadLocal<>();

    /**
     * 多员工关系缓存[员工姓名：组织Ids]
     */
    protected static ThreadLocal<Map<Long, Set<Long>>> orgRefEmpCache = new TransmittableThreadLocal<>();

    protected void loadSelectCache(String fieldType, String fieldName,
                                   Map<String, Map<String, List<IdNameCache>>> tmpCache,
                                   JSONObject fieldProps) {
        Map<String, List<IdNameCache>> idNameCaches = new HashMap<>();
        boolean isMatch = true;
        switch (fieldType) {
            // 组织类型
            case FormFieldCO.YZC_ORG:
                Integer dataScope = fieldProps.getInteger("dataScope");
                String cacheKey = fieldType + ":" + dataScope;
                if (tmpCache.containsKey(cacheKey)) {
                    idNameCaches = tmpCache.get(cacheKey);
                } else {
                    List<OrgDto> orgAll = orgFeignClient.list(new OrgQueryDto().setFilterPerm(false));
                    // 1-全部, 2-跟随用户数据权限
                    Set<Long> orgPermsSet;
                    if (dataScope == 1) {
                        orgPermsSet = orgAll.stream().map(OrgDto::getId).collect(Collectors.toSet());
                    } else {
                        List<OrgDto> orgList = orgFeignClient.list(new OrgQueryDto().setFilterPerm(true));
                        orgPermsSet = orgList.stream().map(OrgDto::getId).collect(Collectors.toSet());
                    }

                    List<IdNameCache> caches = new ArrayList<>();
                    for (OrgDto orgDto : orgAll) {
                        boolean hasPerm = orgPermsSet.contains(orgDto.getId());

                        IdNameCache idNameCache = new IdNameCache(Convert.toStr(orgDto.getId()), orgDto.getOrgName(),
                                hasPerm);
                        caches.add(idNameCache);

                        IdNameCache idNameCodeCache = new IdNameCache(Convert.toStr(orgDto.getId()),
                                orgDto.getOrgName() + "（" + orgDto.getOrgCode() + "）",
                                hasPerm);
                        caches.add(idNameCodeCache);
                    }
                    idNameCaches = caches.stream().collect(Collectors.groupingBy(IdNameCache::getName));
                    tmpCache.put(cacheKey, idNameCaches);
                }
                break;
            // 员工类型
            case FormFieldCO.YZC_EMP:
                if (tmpCache.containsKey(fieldType)) {
                    idNameCaches = tmpCache.get(fieldType);
                } else {
                    // 全部权限
                    List<CusEmployeeDto> empAll = employeeFeignClient.actList(new CusEmployeeQueryDto().setStatus(1).setFilterPerm(false));
                    // 有组织的权限
                    List<CusEmployeeDto> empPerm = employeeFeignClient.actList(new CusEmployeeQueryDto().setStatus(1).setFilterPerm(true));
                    Set<Long> hasPermEmpSet = empPerm.stream().map(CusEmployeeDto::getId).collect(Collectors.toSet());
                    List<IdNameCache> caches = new ArrayList<>();
                    for (CusEmployeeDto employeeDto : empAll) {
                        boolean hasPerm = hasPermEmpSet.contains(employeeDto.getId());
                        if (StrUtil.isNotEmpty(employeeDto.getEmpNo())) {
                            IdNameCache idNoCache = new IdNameCache(Convert.toStr(employeeDto.getId()), employeeDto.getEmpNo(), hasPerm);
                            caches.add(idNoCache);
                        }

                        IdNameCache idNameCache = new IdNameCache(Convert.toStr(employeeDto.getId()), employeeDto.getEmpName(), hasPerm);
                        caches.add(idNameCache);

                        IdNameCache idNameCodeCache = new IdNameCache(Convert.toStr(employeeDto.getId()),
                                employeeDto.getEmpName() + "（" + employeeDto.getEmpNo() + "）", hasPerm);
                        caches.add(idNameCodeCache);

                        Map<Long, Set<Long>> orgRefEmp = orgRefEmpCache.get();
                        for (CusUserOrgDto orgDto : employeeDto.getOrgList()) {
                            Long orgId = orgDto.getOrgId();
                            Set<Long> empIds = orgRefEmp.getOrDefault(orgId, new HashSet<>());
                            empIds.add(employeeDto.getId());
                            orgRefEmp.put(orgId, empIds);
                        }
                    }
                    idNameCaches = caches.stream().collect(Collectors.groupingBy(IdNameCache::getName));
                    tmpCache.put(fieldType, idNameCaches);
                }
                break;
            // 区域类型
            case FormFieldCO.YZC_AREA:
                if (tmpCache.containsKey(fieldType)) {
                    idNameCaches = tmpCache.get(fieldType);
                } else {
                    List<AreaDto> areaAll = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(false));
                    List<AreaDto> areaPermission = areaFeignClient.areaList(new AreaQueryDto().setFilterPerm(true));
                    Set<Long> areaIdSet = areaPermission.stream().map(AreaDto::getId).collect(Collectors.toSet());
                    List<IdNameCache> caches = new ArrayList<>();
                    for (AreaDto areaDto : areaAll) {
                        IdNameCache idNameCache = new IdNameCache(Convert.toStr(areaDto.getId()),
                                areaDto.getAreaName(),
                                areaIdSet.contains(areaDto.getId()));
                        IdNameCache idNameCodeCache = new IdNameCache(Convert.toStr(areaDto.getId()),
                                areaDto.getAreaName() + "（" + areaDto.getAreaCode() + "）",
                                areaIdSet.contains(areaDto.getId()));
                        caches.add(idNameCache);
                        caches.add(idNameCodeCache);
                    }
                    idNameCaches = caches.stream().collect(Collectors.groupingBy(IdNameCache::getName));
                    tmpCache.put(fieldType, idNameCaches);
                }
                break;
            // 资产分类类型
            case FormFieldCO.YZC_ASSET_CATE:
                if (tmpCache.containsKey(fieldType)) {
                    idNameCaches = tmpCache.get(fieldType);
                } else {
                    List<CategoryDto> cateAll = categoryFeignClient.list(new CategoryQueryDto().setFilterPerm(false));
                    List<CategoryDto> catePermission = categoryFeignClient.list(new CategoryQueryDto().setFilterPerm(true));
                    Set<Long> cateIdSet = catePermission.stream().map(CategoryDto::getId).collect(Collectors.toSet());
                    List<IdNameCache> caches = new ArrayList<>();
                    for (CategoryDto categoryDto : cateAll) {
                        IdNameCache idNameCache = new IdNameCache(Convert.toStr(categoryDto.getId()),
                                categoryDto.getCategoryName(),
                                cateIdSet.contains(categoryDto.getId()));
                        IdNameCache idNameCodeCache = new IdNameCache(Convert.toStr(categoryDto.getId()),
                                categoryDto.getCategoryName() + "（" + categoryDto.getCategoryCode() + "）",
                                cateIdSet.contains(categoryDto.getId()));
                        caches.add(idNameCache);
                        caches.add(idNameCodeCache);
                    }
                    idNameCaches = caches.stream().collect(Collectors.groupingBy(IdNameCache::getName));
                    tmpCache.put(fieldType, idNameCaches);
                }
                break;
            default:
                isMatch = false;
                break;
        }
        if (isMatch) {
            dropDownCache.get().put(fieldName, idNameCaches);
        }
    }

    protected void dateConvert(AssetImportDto.FieldData fieldData, Object value, String dateFormatType) {
        String dateStr = StrUtil.trim(Convert.toStr(value));
        if (StrUtil.isEmpty(dateStr)) {
            return;
        }
        /*String regex = DateTimeCO.regexMap.get(dateFormatType);*/
        // 导入的日期格式兼容，自动对齐配置的日期格式
        if (value instanceof Date) {
            try {
                Date date = Convert.toDate(value);
                SimpleDateFormat sdf = new SimpleDateFormat(dateFormatType);
                dateStr = sdf.format(date);
            } catch (Exception e) {
                fieldData.getErrMsg().add("值不是日期类型");
                return;
            }
        }

        /*if (regex == null || !dateStr.matches(regex)) {
            fieldData.getErrMsg().add("日期格式仅支持" + dateFormatType);
            return;
        }*/

        String dateStrFmt = TimeUtil.convertDate(dateStr);
        switch (dateFormatType) {
            case DateTimeCO.yyyyMMdd:
                try {
                    LocalDate yyyyMMddParse = LocalDate.parse(dateStrFmt.endsWith("-") ? dateStrFmt.substring(0, dateStrFmt.length() - 1) : dateStrFmt, DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyMMddLong = yyyyMMddParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyMMddLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM-dd，yyyy/MM/dd，yyyy年MM月dd日");
                }
                return;
            case DateTimeCO.yyyyMM:
                try {
                    LocalDate yyyyMMParse = LocalDate.parse(dateStrFmt + (dateStrFmt.endsWith("-") ? "01" : "-01"), DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyMMLong = yyyyMMParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyMMLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM，yyyy/MM，yyyy年MM月");
                }
                return;
            case DateTimeCO.yyyy:
                try {
                    LocalDate yyyyParse = LocalDate.parse(dateStrFmt + (dateStrFmt.endsWith("-") ? "01-01" : "-01-01"), DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyLong = yyyyParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy，yyyy年");
                }
                return;
            case DateTimeCO.yyyyMMddHHmmss:
                try {
                    LocalDateTime parse = LocalDateTime.parse(dateStrFmt, DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMddHHmmss));
                    Long time = parse.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(time);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM-dd HH:mm:ss，yyyy/MM/dd HH:mm:ss，yyyy年MM月dd日 HH时mm分ss秒");
                }
                return;
            default:
                fieldData.getErrMsg().add("不支持的日期格式，例" + dateFormatType);
        }
    }
}
