package com.niimbot.asset.service.feign.equipment;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentMaintainPlanFeignClient {

    @PostMapping("/server/equipment/maintain/plan/preview")
    EntMatPlanPreview previewPlan(@RequestBody CreateEntMatPlan plan);

    @PostMapping("/server/equipment/maintain/plan/create")
    AuditableCreatePlanResult createPlan(@RequestBody CreateEntMatPlan plan);

    @PostMapping("/server/equipment/maintain/plan/stop")
    AuditableStopPlanResult stopPlan(@RequestBody StopEntMatPlan union);

    @PostMapping("/server/equipment/maintain/plan/remove")
    AuditableRemovePlanResult removePlan(@RequestBody RemoveEntMatPlan remove);

    @PostMapping("/server/equipment/maintain/plan/reUser")
    Boolean rePlanUser(@RequestBody ReEntMatPlanUser change);

    @GetMapping("/server/equipment/maintain/plan/detail")
    EntMatPlanDetail detailPlan(@RequestParam("planId") Long planId);

    @GetMapping("/server/equipment/maintain/plan/cate")
    List<EntMatPlanEntCateData> listEntCate(@RequestParam("planId") Long planId);

    @PostMapping("/server/equipment/maintain/plan/count")
    List<EntMatPlanCounter> countPlan(@RequestBody List<Long> planIds);

    @PostMapping("/server/equipment/maintain/plan/search")
    PageUtils<EntMatPlan> searchPlan(@RequestBody EntMatPlanSearch search);

    @PostMapping("/server/equipment/maintain/plan/pageSelectedEntMatData")
    PageUtils<EntMatPlanEntData> pageSelectedEntMatData(@RequestBody GetSelectedEntData get);

    @PostMapping("/server/equipment/maintain/plan/pageSelectedEntSrPrData")
    PageUtils<EntMatPlanSrPrData> pageSelectedEntSrPrData(@RequestBody GetSelectedSrPrData get);

}
