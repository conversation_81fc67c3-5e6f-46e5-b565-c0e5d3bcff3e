package com.niimbot.asset.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.MaterialReportQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.AssetQueryFieldFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionGeneralDto;
import com.niimbot.system.QueryTypeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/29 下午3:01
 */
@Slf4j
@Service
public class MaterialReportQueryFieldServiceImpl implements MaterialReportQueryFieldService {

    @Value("${asset.upload.domain}")
    private String domain;
    @Autowired
    private FormFeignClient formFeignClient;
    @Autowired
    private AssetQueryFieldFeignClient assetQueryFieldFeignClient;
    @Autowired
    private AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @Override
    public List<QueryConditionDto> allQueryField(String type) {
        if (QueryFieldConstant.TYPE_MATERIAL_STOCK_REPORT_QUERY.equalsIgnoreCase(type)) {
            return materialQueryCondition(true);
        } else if (QueryFieldConstant.TYPE_MATERIAL_REPOSITORY_REPORT_QUERY.equalsIgnoreCase(type)) {
            return materialQueryCondition(false);
        } else {
            return materialOrderQueryCondition(type);
        }
    }

    @Override
    public List<QueryConditionDto> queryView(String type) {
        if (QueryFieldConstant.TYPE_MATERIAL_STOCK_REPORT_QUERY.equalsIgnoreCase(type) || QueryFieldConstant.TYPE_MATERIAL_REPOSITORY_REPORT_QUERY.equalsIgnoreCase(type)) {
            return materialStockQueryView(type);
        } else {
            return materialOrderQueryView(type);
        }
    }

    /**
     * 耗材库存固定筛选字段
     * @return
     */
    private List<QueryConditionDto> materialStockFixedQueryCondition() {
        List<QueryConditionDto> result = new ArrayList<>();

        QueryConditionDto repositoryIdCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("repositoryId")
                .setName("耗材仓库")
                .setType(FormFieldCO.YZC_REPOSITORY)
                .setFieldProps(new JSONObject());
        FormBaseFieldCO repositoryField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_REPOSITORY);
        // 查询仓库不需要盘点过滤
        JSONObject fieldProps = repositoryField.getFieldProps();
        if (fieldProps != null) {
            JSONObject extProps = fieldProps.getJSONObject("extProps");
            if (extProps != null) {
                // 更新url为不过滤仓库
                String url = extProps.getString("url");
                if (url.endsWith("/withOutInventory")) {
                    extProps.put("url", url.replace("/withOutInventory", ""));
                }
                // 更新selectorUrl为不过滤仓库
                JSONObject selectorUrl = extProps.getJSONObject("selectorUrl");
                if (selectorUrl != null && selectorUrl.containsKey("get")) {
                    String getUrl = selectorUrl.getString("get");
                    if (getUrl.endsWith("/filter")) {
                        selectorUrl.put("get", getUrl.replace("/filter", ""));
                        extProps.put("selectorUrl", selectorUrl);
                    }
                }
            }
        }
        repositoryIdCondition.setFieldProps(fieldProps);

        QueryConditionDto stockNumCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("stockNum")
                .setName("库存数量")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());

        QueryConditionDto avgPriceCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("avgPrice")
                .setName("加权平均价值")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());

        QueryConditionDto stockPriceCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("stockPrice")
                .setName("库存金额")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());
        result.add(repositoryIdCondition);
        result.add(stockNumCondition);
        result.add(avgPriceCondition);
        result.add(stockPriceCondition);
        return result;
    }

    /**
     * 耗材库存查询所有字段
     * @return
     */
    private List<QueryConditionDto> materialQueryCondition(boolean appendStockField) {
        List<QueryConditionDto> result = new ArrayList<>();
        //筛选条件固定字段
        if (BooleanUtil.isTrue(appendStockField)) {
            result.addAll(materialStockFixedQueryCondition());
        }

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            result.add(queryConditionDto);
        }

        //补齐 标准品，创建人，创建时间
        FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);
        QueryConditionDto createBy = toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            result.add(createBy);
        }
        result.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        result.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    /**
     * 耗材库存查询已选字段
     * @return
     */
    private List<QueryConditionDto> materialStockQueryView(String type) {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> materialAllQueryField = materialQueryCondition(true);
        Map<String, QueryConditionDto> materialAllQueryFieldMap = materialAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(type);
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (materialAllQueryFieldMap.containsKey(f)) {
                result.add(materialAllQueryFieldMap.get(f));
            }
        });
        return result;
    }

    private List<QueryConditionDto> materialOrderQueryCondition(String type) {
        List<QueryConditionDto> result = new ArrayList<>();
        int orderType = AssetConstant.ORDER_TYPE_MATERIAL_RK;
        if (QueryFieldConstant.TYPE_MATERIAL_STORAGE_REPORT_QUERY.equalsIgnoreCase(type)) {
            orderType = AssetConstant.ORDER_TYPE_MATERIAL_RK;
            result.addAll(materialStorageFixedCondition());
        } else if (QueryFieldConstant.TYPE_MATERIAL_OUT_REPORT_QUERY.equalsIgnoreCase(type)) {
            orderType = AssetConstant.ORDER_TYPE_MATERIAL_CK;
            result.addAll(materialOutFixedCondition());
        } else if (QueryFieldConstant.TYPE_MATERIAL_RECEIPT_REPORT_QUERY.equalsIgnoreCase(type)) {
            orderType = AssetConstant.ORDER_TYPE_MATERIAL_LY;
            result.addAll(materialReceiptFixedCondition());
        }

        FormVO formVO = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES, FormFieldCO.YZC_ASSOCIATION_TABLE)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            result.add(queryConditionDto);
        }
        if (AssetConstant.ORDER_TYPE_MATERIAL_LY == orderType) {
            result.add(orderToQueryConditionDto(QueryFieldConstant.FIELD_GRANT_STATUS));
        }
        FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);
        QueryConditionDto createBy = orderToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            result.add(createBy);
        }
        result.add(orderToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    /**
     * 入库固定筛选字段
     * @return
     */
    private List<QueryConditionDto> materialStorageFixedCondition() {
        List<QueryConditionDto> result = new ArrayList<>();
        QueryConditionDto storageNumCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("storageNum")
                .setName("入库数量")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());

        QueryConditionDto storagePriceCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("storagePrice")
                .setName("入库金额")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());
        result.add(storageNumCondition);
        result.add(storagePriceCondition);
        return result;
    }

    /**
     * 出库筛选固定字段
     * @return
     */
    private List<QueryConditionDto> materialOutFixedCondition() {
        List<QueryConditionDto> result = new ArrayList<>();
        QueryConditionDto outNumCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("outNum")
                .setName("出库数量")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());

        QueryConditionDto outPriceCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("outPrice")
                .setName("出库金额")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());
        result.add(outNumCondition);
        result.add(outPriceCondition);
        return result;
    }

    private List<QueryConditionDto> materialReceiptFixedCondition() {
        List<QueryConditionDto> result = new ArrayList<>();
        QueryConditionDto receiptNumCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("receiptNum")
                .setName("申请数量")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());

        QueryConditionDto grantNumCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("grantNum")
                .setName("已发放数量")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());

        QueryConditionDto grantingNumCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("grantingNum")
                .setName("发放中数量")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());

        QueryConditionDto waitGrantNumCondition = new QueryConditionDto()
                .setFixedField(Boolean.TRUE)
                .setCode("waitGrantNum")
                .setName("待发放数量")
                .setType(FormFieldCO.NUMBER_INPUT)
                .setFieldProps(new JSONObject());
        result.add(receiptNumCondition);
        result.add(grantNumCondition);
        result.add(grantingNumCondition);
        result.add(waitGrantNumCondition);
        return result;
    }
    
    private List<QueryConditionDto> materialOrderQueryView(String type) {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllQueryField = materialOrderQueryCondition(type);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(type);
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });
        return result;
    }

    private QueryConditionDto buildQueryCondition(FormFieldCO formFieldCO) {
        return new QueryConditionDto()
                .setName(formFieldCO.getFieldName())
                .setCode(formFieldCO.getFieldCode())
                .setType(formFieldCO.getFieldType())
                .setFieldProps(formFieldCO.getFieldProps());
    }

    private QueryConditionDto toQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.MATERIAL_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    private QueryConditionDto orderToQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }
}
