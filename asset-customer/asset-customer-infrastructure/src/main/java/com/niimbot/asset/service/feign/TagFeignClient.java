package com.niimbot.asset.service.feign;

import com.niimbot.means.PrintDataSetTagDto;
import com.niimbot.system.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 标签模板管理feign客户端
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface TagFeignClient {

    /**
     * 通过sizeId
     *
     * @param sizeId 尺寸Id
     * @return 账号信息
     */
    @GetMapping(value = "server/system/tag/{printType}/{sizeId}")
    UserTagResDto getBySizeId(@PathVariable("printType") Short printType,
                              @PathVariable("sizeId") Long sizeId,
                              @RequestParam("tagType") Integer tagType,
                              @RequestParam("kw") String kw,
                              @RequestParam("printerName") String printerName);

    /**
     * 获取尺寸列表
     *
     * @param printerIds  打印机ids
     * @param printerName printerName
     * @return 尺寸信息
     */
    @GetMapping(value = "server/system/tag/sizeList/{printType}")
    SizeResDto getSizeList(@PathVariable("printType") Short printType,
                           @RequestParam("printerIds") List<Long> printerIds,
                           @RequestParam("printerName") String printerName);

    /**
     * 获取app尺寸列表
     *
     * @return 尺寸信息
     */
    @GetMapping(value = "server/system/tag/appSizeList")
    List<SizeDto> appSizeList(@RequestParam("printerName") String printerName);

    /**
     * 设置资产默认标签模板
     *
     * @param printDataSetTagDto 设置默认标签dto
     * @return Boolean
     */
    @PutMapping(value = "server/system/tag/setDefaultTag")
    Boolean setDefaultTag(@RequestBody PrintDataSetTagDto printDataSetTagDto);

    /**
     * 设置耗材默认标签模板
     *
     * @param printDataSetTagDto 设置默认标签dto
     * @return Boolean
     */
    @PutMapping(value = "server/system/tag/setDefaultCftag")
    Boolean setDefaultCftag(@RequestBody PrintDataSetTagDto printDataSetTagDto);

    /**
     * 保存用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Boolean
     */
    @PostMapping(value = "server/system/tag")
    Boolean saveUserTag(UserTagSaveDto userTagSaveDto);

    /**
     * 获取移动端标签列表
     *
     * @param sizeId  尺寸类型
     * @param tagType 标签类型
     * @param kw      关键字
     * @return 移动端标签列表
     */
    @GetMapping(value = "server/system/tag/appTagList/{printType}")
    UserTagResDto appTagList(@PathVariable("printType") Short printType,
                             @RequestParam("sizeId") Long sizeId,
                             @RequestParam("tagType") Integer tagType,
                             @RequestParam("kw") String kw,
                             @RequestParam("printerName") String printerName);

    /**
     * 获取PDF模版
     *
     * @return 获取PDF模版
     */
    @GetMapping(value = "server/system/tag/getPdfTag/{printType}/{printerId}")
    HashMap<String, List<UserTagDto>> getPdfTag(@PathVariable("printType") Short printType, @PathVariable("printerId") Long printerId);

    /**
     * 修改标签模板名称
     *
     * @param userTagNameDto 修改标签模板名称对象
     * @return Boolean
     */
    @PutMapping(value = "server/system/tag/updateTagName")
    Boolean updateTagName(UserTagNameDto userTagNameDto);

    /**
     * 编辑用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Boolean
     */
    @PutMapping(value = "server/system/tag")
    Boolean editUserTag(@RequestBody UserTagSaveDto userTagSaveDto);

    /**
     * 删除用户自定义标签模板
     *
     * @param id 用户自定义标签id
     * @return Boolean
     */
    @DeleteMapping(value = "server/system/tag/{id}")
    Boolean deleteUserTag(@PathVariable("id") Long id);

    /**
     * 全部去重数据查询列表
     *
     * @param sizeId 尺寸id
     * @return 标签模板信息
     */
    @GetMapping(value = "server/system/tag/distinct/{printType}/{sizeId}/{printerId}")
    List<UserTagDto> getDistinctBySizeId(@PathVariable("printType") Short printType,
                                         @PathVariable("sizeId") Long sizeId,
                                         @PathVariable("printerId") Long printerId);

    /**
     * 标签模板详情
     *
     * @param id 模板id
     * @return 标签模板信息
     */
    @GetMapping(value = "server/system/tag/detail/{id}")
    UserTagDetailDto getDetail(@PathVariable("id") Long id);
}
