package com.niimbot.asset.service.feign;

import com.niimbot.material.MaterialOperationDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/12 下午2:04
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialOperationFeignClient {

    @GetMapping("server/material/operation/allType")
    List<MaterialOperationDto> allType();
}
