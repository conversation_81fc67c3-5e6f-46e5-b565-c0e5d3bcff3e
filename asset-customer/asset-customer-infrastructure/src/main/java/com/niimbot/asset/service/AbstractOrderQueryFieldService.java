package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.AssetQueryFieldFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryTypeDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2022/10/17 14:53
 */
public abstract class AbstractOrderQueryFieldService implements OrderQueryFieldService {

    @Autowired
    protected FormFeignClient formFeignClient;

    @Autowired
    protected AssetQueryFieldFeignClient assetQueryFieldFeignClient;

    @Autowired
    protected AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @Value("${asset.upload.domain}")
    protected String domain;

    protected abstract void specialOrderQueryField(Integer orderType, List<QueryConditionDto> queryConditionDtos);

    protected abstract List<QueryConditionDto> allHeadField(Integer orderType, FormVO formVO);


    @Override
    public List<QueryConditionDto> orderQueryField(Integer orderType) {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();

        FormVO formVO = formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        specialOrderQueryField(orderType, queryConditionDtos);
        FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);
        QueryConditionDto createBy = toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            queryConditionDtos.add(createBy);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    @Override
    public List<QueryConditionDto> orderAllHeadField(Integer orderType) {
        FormVO formVO = formFeignClient.getTplByType(
                OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        return allHeadField(orderType, formVO);
    }


    protected QueryConditionDto buildQueryCondition(FormFieldCO formFieldCO) {
        return new QueryConditionDto()
                .setName(formFieldCO.getFieldName())
                .setCode(formFieldCO.getFieldCode())
                .setType(formFieldCO.getFieldType())
                .setFieldProps(formFieldCO.getFieldProps());
    }

    protected QueryConditionDto toQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

}
