package com.niimbot.asset.controller.common.means;

import com.google.common.net.HttpHeaders;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.ProductExcelService;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.ProductFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AsProductDto;
import com.niimbot.means.AsProductInfoDto;
import com.niimbot.means.AsProductQueryDto;
import com.niimbot.means.ProductListByIdQueryDto;
import com.niimbot.means.ReImportDto;
import com.niimbot.means.StandardListDto;
import com.niimbot.system.ImportDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import org.apache.catalina.manager.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品管理接口
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@Api(tags = "产品管理接口")
@ResultController
@RequestMapping("api/common/product")
public class ProductController {

    private final ProductFeignClient productFeignClient;
    private final StandardFeignClient standardFeignClient;
    private final ProductExcelService productExcelService;
    private final RedisService redisService;
    private final FormFeignClient formFeignClient;

    @Autowired
    public ProductController(ProductFeignClient productFeignClient,
                             StandardFeignClient standardFeignClient,
                             ProductExcelService productExcelService,
                             RedisService redisService,
                             FormFeignClient formFeignClient) {
        this.productFeignClient = productFeignClient;
        this.standardFeignClient = standardFeignClient;
        this.productExcelService = productExcelService;
        this.redisService = redisService;
        this.formFeignClient = formFeignClient;
    }

    @ApiOperation(value = "产品分页列表")
    @PostMapping(value = "/page")
    public PageUtils<LinkedHashMap<String, Object>> page(@RequestBody AsProductQueryDto queryDto) {
        return productFeignClient.page(queryDto);
    }

    @ApiOperation(value = "产品下拉")
    @GetMapping(value = "/list")
    public List<JSONObject> page(@RequestParam(value = "name", required = false) String name) {
        List<AsProductInfoDto> list = productFeignClient.list(name);
        return list.stream().map(f -> {
            JSONObject json = new JSONObject();
            json.put("id", f.getId());
            json.put("standardId", f.getStandardId());
            json.put("name", f.getName());
            return json;
        }).collect(Collectors.toList());
    }

    @ApiOperation(value = "产品详情")
    @GetMapping(value = "/{id}")
    public JSONObject info(@PathVariable("id") Long id) {
        JSONObject result = new JSONObject();
        AsProductInfoDto info = productFeignClient.info(id);
        if (info == null) {
            return result;
        }
        JSONObject object = info.translateInfo();
        object.put("images", info.getImages());
        return object;
    }

    @ApiOperation(value = "通过id列表查询产品列表")
    @PostMapping(value = "/listById")
    public List<JSONObject> listById(@RequestBody @Validated ProductListByIdQueryDto queryDto) {
        List<JSONObject> result = new ArrayList<>();
        List<AsProductInfoDto> infos = productFeignClient.listById(queryDto);
        if (CollUtil.isNotEmpty(infos)) {
            infos.forEach(o -> result.add(o.translateInfo()));
        }
        return result;
    }

    @ApiOperation(value = "选择产品，记录用户习惯")
    @PostMapping("/select/{productId}")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean select(@PathVariable("productId") Long productId) {
        return productFeignClient.select(productId);
    }

    @ApiOperation(value = "新增产品")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public AsProductInfoDto add(@RequestBody @Validated(Insert.class) AsProductDto product) {
        return productFeignClient.add(product);
    }

    @ApiOperation(value = "编辑产品", notes = "系统数据不允许编辑")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean edit(@RequestBody @Validated(Update.class) AsProductDto product) {
        return productFeignClient.edit(product);
    }

    @ApiOperation(value = "删除产品")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        return productFeignClient.delete(ids);
    }

    @ApiOperation(value = "导出产品模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response,
                               @ApiParam(name = "standardId", value = "标准品ID")
                               @NotNull(message = "标准品ID不能为空")
                               @RequestParam(value = "standardId") Long standardId) {
        try (OutputStream out = response.getOutputStream()) {
            String fileName = "导入产品模板.xlsx";
            if (standardId != null) {
                FormVO standard = formFeignClient.getByFormId(new FormByIdQry(standardId,
                        ListUtil.of(1L, LoginUserThreadLocal.getCompanyId())));
                if (standard != null) {
                    fileName = standard.getFormName() + "-" + fileName;
                }
            }
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");

            ExcelWriter writer = productExcelService.buildExcelWriter(standardId);
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "导入产品模板")
    @PostMapping(value = "/importTemplate", consumes = "multipart/form-data")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public void importTemplate(@ApiParam(name = "standardId", value = "标准品ID")
                                   @NotNull(message = "标准品ID不能为空")
                                   @RequestParam(value = "standardId") Long standardId,
                               @RequestPart(FileUploadConfig.FRONT_SINGLE_PARAM_NAME) MultipartFile file) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String fileName = file.getOriginalFilename();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.PRODUCT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.PRODUCT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        // 上传文件为空
        if (StrUtil.isEmpty(fileName)) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_NULL);
        }
        if (fileName.length() > 32) {
            throw new BusinessException(MeansResultCode.PRODUCT_IMPORT_ERROR, "文件名超长");
        }

        //上传文件大小为1M数据
        if (file.getSize() > 1024 << 10) {
            throw new BusinessException(SystemResultCode.FILE_UPLOAD_FILE_SIZE_EXCEED);
        }
        // 先校验并复制标准品
        StandardListDto standardListDto = standardFeignClient.copySys(standardId);
        try (InputStream stream = file.getInputStream()) {
            productExcelService.parseExcelStream(stream, file.getOriginalFilename(), file.getSize(),
                    companyId, standardListDto.getId());
        } catch (BusinessException busExp) {
            throw busExp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.IMPORT_ERROR);
        }
    }

    @ApiOperation(value = "批量导入错误数据查询")
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return productExcelService.importError(taskId);
    }

    @ApiOperation(value = "重新导入错误数据查询")
    @GetMapping(value = "/reImport/{taskId}")
    public ReImportDto reImport(@PathVariable("taskId") Long taskId) {
        return productExcelService.reImport(taskId);
    }

    @ApiOperation(value = "批量导入错误数据保存")
    @ResultMessage("导入完成")
    @PostMapping(value = "/importError/{taskId}")
    public ImportDto importError(@PathVariable("taskId") Long taskId,
                                 @RequestBody List<List<Object>> sheetModels) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 判断是否导入中
        if (redisService.hasKey(RedisConstant.companyImportKey(ImportService.PRODUCT, companyId))) {
            Boolean finish = (Boolean) redisService.hGet(RedisConstant.companyImportKey(ImportService.PRODUCT, companyId), "finish");
            if (!finish) {
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }
        productExcelService.importErrorSave(taskId, sheetModels, companyId);
        redisService.hSet(RedisConstant.companyImportKey(ImportService.PRODUCT, companyId), "notice", false);
        Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(RedisConstant.companyImportAll(companyId) + ":" + ImportService.PRODUCT));
        JSONObject json = new JSONObject(map);
        return json.toJavaObject(ImportDto.class);
    }

}
