package com.niimbot.asset.service.feign;

import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionSortDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2022/10/17 15:34
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialOrderFeignClient {

    @GetMapping("server/material/order/sortField/{orderType}")
    QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType);


    @GetMapping(value = "server/material/order/form/{orderType}")
    FormVO getForm(@PathVariable("orderType") Integer orderType);

}
