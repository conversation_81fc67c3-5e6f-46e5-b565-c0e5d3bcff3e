package com.niimbot.asset.controller.common.means;

import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.service.feign.AssetOperationFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.means.AssetOperationDto;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/3/12 16:24
 */
@Api(tags = "资产操作管理")
@ResultController
@RequestMapping("api/common/assetOperation")
public class AssetOperationController {

    private final AssetOperationFeignClient assetOperationFeignClient;

    @Autowired
    public AssetOperationController(AssetOperationFeignClient assetOperationFeignClient) {
        this.assetOperationFeignClient = assetOperationFeignClient;
    }

    /**
     * 查询资产操作集合列表
     *
     * @return 操作集合
     */
    @GetMapping("/dict")
    public List<Map<String, ?>> dict() {
        List<AssetOperationDto> list = assetOperationFeignClient.list();
        return list.stream().map(item ->
                ImmutableMap.of("label", item.getName(), "value", item.getId())
        ).collect(Collectors.toList());
    }

}
