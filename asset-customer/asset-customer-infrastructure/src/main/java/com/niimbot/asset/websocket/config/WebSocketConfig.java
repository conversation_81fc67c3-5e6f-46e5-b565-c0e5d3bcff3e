package com.niimbot.asset.websocket.config;

import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.websocket.WebSocketServer;
import com.niimbot.asset.websocket.handler.MessageHandlerContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * websocket配置
 *
 * <AUTHOR>
 * @date 2021/9/1 10:08
 */
@Configuration
public class WebSocketConfig {
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Autowired
    public void setTokenService(AbstractTokenService tokenService) {
        WebSocketServer.setTokenService(tokenService);
    }

    @Autowired
    public void setHandlerContext(MessageHandlerContext handlerContext) {
        WebSocketServer.setHandlerContext(handlerContext);
    }
}
