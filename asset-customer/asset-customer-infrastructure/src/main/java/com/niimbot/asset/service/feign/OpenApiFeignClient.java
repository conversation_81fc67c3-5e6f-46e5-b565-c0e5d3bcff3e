package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.system.*;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/11 14:10
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface OpenApiFeignClient {

    @PostMapping("server/system/openApi/")
    Boolean add(OpenApiCreateDto createDto);

    @PostMapping("server/system/openApi/getAuthCode")
    String getAuthCode(OpenApiAuthCodeDto authCodeDto);

    @PostMapping("server/system/openApi/getUserInfoByReverseCode")
    OpenApiUserInfoDto getUserInfoByReverseCode(OpenApiGetUserInfoDto getUserInfoDto);

    @GetMapping("server/system/openApi/getOpenApiByAgentId")
    OpenApiDto getOpenApiByAgentId(@RequestParam("agentId") Long agentId);


}
