package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.inventory.AssetAddDto;
import com.niimbot.inventory.AssetUpdateDto;
import com.niimbot.inventory.IgnoreUpdateDto;
import com.niimbot.inventory.InventoryApproveSubmitDto;
import com.niimbot.inventory.InventoryAssetRangeWithModeDto;
import com.niimbot.inventory.InventoryCountDto;
import com.niimbot.inventory.InventoryCreateAssetQueryDto;
import com.niimbot.inventory.InventoryCreateAssetQueryPageDto;
import com.niimbot.inventory.InventoryDetailDto;
import com.niimbot.inventory.InventoryDispatchDetailDto;
import com.niimbot.inventory.InventoryDto;
import com.niimbot.inventory.InventoryHandleLogDto;
import com.niimbot.inventory.InventoryHandleLogQry;
import com.niimbot.inventory.InventoryManualDto;
import com.niimbot.inventory.InventoryQueryDto;
import com.niimbot.inventory.InventoryRangeGroupCreateDto;
import com.niimbot.inventory.InventoryRangeGroupDto;
import com.niimbot.inventory.InventoryReportDto;
import com.niimbot.inventory.InventorySubmitDto;
import com.niimbot.inventory.InventorySurplusDto;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventorySurplusSimpleQueryDto;
import com.niimbot.inventory.InventorySyncAddAndSubmitDto;
import com.niimbot.inventory.InventorySyncAssetDto;
import com.niimbot.inventory.InventorySyncAssetResultDto;
import com.niimbot.inventory.InventoryTaskAddUserDto;
import com.niimbot.inventory.InventoryTaskApproveDto;
import com.niimbot.inventory.InventoryTaskInfoDto;
import com.niimbot.inventory.InventoryTaskListDto;
import com.niimbot.inventory.InventoryTaskUpdateUserDto;
import com.niimbot.inventory.InventoryTaskUpdateUserInfoDto;
import com.niimbot.inventory.InventoryTaskView;
import com.niimbot.inventory.InventoryUpdateApproverDto;
import com.niimbot.inventory.InventoryView;
import com.niimbot.inventory.PlPhotoDto;
import com.niimbot.inventory.PlUpdateAssetByUserDto;
import com.niimbot.means.AssetDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import feign.Request;

/**
 * <AUTHOR>
 * @date 2021/4/12 11:50
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface InventoryFeignClient {
    /**
     * 分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "server/inventory/page")
    PageUtils<InventoryDto> page(@SpringQueryMap InventoryQueryDto dto);

    /**
     * 盘点任务明细
     *
     * @param id id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/withoutAsset/{id}")
    InventoryDetailDto getDetailWithoutAsset(@PathVariable("id") Long id);

    /**
     * 生成预创建盘点单资产数据
     *
     * @param dto
     */
    @PostMapping("server/inventory/createInventoryQueryAsset")
    void createInventoryQueryAsset(@RequestBody InventoryCreateAssetQueryDto dto);

    /**
     * 预创建盘点单资产数据分页
     *
     * @param queryDto
     * @return
     */
    @PostMapping(value = "server/inventory/create/asset/page")
    PageUtils<AssetDto> createAssetPage(@RequestBody InventoryCreateAssetQueryPageDto queryDto);


    /**
     * 创建盘点单
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory")
    Long create(InventorySubmitDto dto);

    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/surplus")
    PageUtils<InventorySurplusDto> getSurplusById(InventorySurplusQueryDto dto);

    /**
     * 盘点单盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/surplus/list")
    List<InventorySurplusDto> getSurplusList(InventorySurplusQueryDto dto);

    /**
     * 单个任务驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/rejected")
    Boolean rejected(InventoryTaskApproveDto approveDto);

    /**
     * 整单驳回
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/rejectedAll")
    Boolean rejectedAll(InventoryApproveSubmitDto approveDto);

    /**
     * 整单同意
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/approved")
    Boolean approved(InventoryApproveSubmitDto approveDto);

    /**
     * 单个同意
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/approvedOne")
    Boolean approvedOne(InventoryTaskApproveDto approveDto);

    /**
     * 修改审批人
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/updateApprover")
    Boolean updateApprover(InventoryUpdateApproverDto approveDto);

    /**
     * 终止
     *
     * @param inventoryId
     * @return 结果
     */
    @PutMapping(value = "server/inventory/stop/{inventoryId}")
    Boolean stop(@PathVariable("inventoryId") Long inventoryId);

    /**
     * 盘点单删除
     *
     * @param inventoryId
     * @return 结果
     */
    @DeleteMapping(value = "server/inventory/{inventoryId}")
    Boolean removeById(@PathVariable("inventoryId") Long inventoryId);

    /**
     * 盘点任务分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "server/inventory/task/page")
    PageUtils<InventoryTaskListDto> taskPage(@SpringQueryMap InventoryQueryDto dto);

    @GetMapping(value = "server/inventory/task/pageApp")
    PageUtils<InventoryTaskListDto> taskPageApp(@SpringQueryMap InventoryQueryDto dto);

    /**
     * 提交审核
     *
     * @param approveDto approveDto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/task/submit")
    Boolean submit(InventoryTaskApproveDto approveDto);

    /**
     * 手动盘点
     *
     * @param dto dto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/task/manualInventory")
    Boolean manualInventory(InventoryManualDto dto);

    /**
     * 资产上报
     *
     * @param dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/task/reportAsset")
    Boolean reportAsset(InventorySurplusDto dto);

    /**
     * 编辑上报资产
     *
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/task/reportAsset")
    Boolean editReportAsset(InventorySurplusDto dto);

    /**
     * 删除上报资产
     *
     * @param id
     * @return 结果
     */
    @DeleteMapping(value = "server/inventory/task/reportAsset/{id}")
    Boolean removeReportAsset(@PathVariable("id") Long id);

    /**
     * 盘点单概览
     *
     * @param id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/view/{id}")
    InventoryView getInventoryView(@PathVariable("id") Long id);

    /**
     * 盘点头部统计
     *
     * @return 结果
     */
    @GetMapping(value = "server/inventory/count")
    InventoryCountDto count();

    /**
     * 批量删除上报资产
     *
     * @param ids 上报资产IDs
     * @return 结果
     */
    @DeleteMapping(value = "server/inventory/task/reportAsset")
    Boolean removeReportAssets(@RequestBody List<Long> ids);

    /**
     * 盘点分配方式详细
     *
     * @param dto 资产查询条件
     * @return 结果
     */
    @PostMapping(value = "server/inventory/dispatchDetail")
    InventoryDispatchDetailDto getDispatchDetail(InventoryAssetRangeWithModeDto dto);

    /**
     * 同步上传盘点任务资产数据
     *
     * @param dto 同步资产数据
     * @return 结果
     */
    @PutMapping(value = "server/inventory/task/syncUploadPdTaskAsset/v1")
    InventorySyncAssetResultDto syncUploadPdTaskAsset(Request.Options options, InventorySyncAssetDto dto);

    @PutMapping(value = "server/inventory/task/syncUploadPdTaskAsset/v2")
    InventorySyncAssetResultDto syncUploadPdTaskAssetV2(Request.Options options, InventorySyncAssetDto dto);

    /**
     * 盘点任务详情
     *
     * @param id 盘点任务id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/task/info/{id}")
    InventoryTaskInfoDto getInfo(@PathVariable("id") Long id);

    /**
     * 盘点单详情
     *
     * @param id 盘点单id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/info/{id}")
    InventoryDto getInventoryInfo(@PathVariable("id") Long id);

    /**
     * 盘点损益处理盘盈资产数据
     *
     * @param dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/surplus")
    PageUtils<InventorySurplusDto> getPlSurplus(InventorySurplusSimpleQueryDto dto);

    /**
     * 同步新增资产+提交审核
     *
     * @param dto 盘盈资产数据
     * @return 结果
     */
    @PostMapping(value = "server/inventory/task/syncAddAndSubmit")
    Boolean syncAddAndSubmit(InventorySyncAddAndSubmitDto dto);

    /**
     * 忽略修改资产
     *
     * @param dto 盘盈资产数据
     * @return 结果
     */
    @PutMapping(value = "server/inventory/asset/ignoreUpdate")
    Boolean ignoreUpdate(IgnoreUpdateDto dto);

    /**
     * 获取子任务详情
     *
     * @param id id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/task/{id}")
    InventoryTaskListDto getTaskListInfoById(@PathVariable("id") Long id);

    /**
     * 修改资产信息
     *
     * @param dto 资产数据
     * @return 结果
     */
    @PutMapping(value = "server/inventory/asset/plUpdateAsset")
    Boolean plUpdateAsset(AssetUpdateDto dto);

    /**
     * 使用盘点人修改信息
     *
     * @param dto 资产数据
     * @return 结果
     */
    @PutMapping(value = "server/inventory/asset/plUpdateAssetByUser")
    Boolean plUpdateAssetByUser(List<PlUpdateAssetByUserDto> dto);

    /**
     * 盘盈资产新增入库
     *
     * @param dto 资产数据
     * @return 资产ID
     */
    @PostMapping(value = "server/inventory/asset/plAddAsset")
    Long plAddAsset(AssetAddDto dto);

    /**
     * 盘点报告
     *
     * @param id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/report/{id}")
    InventoryReportDto getInventoryReport(@PathVariable("id") Long id);

    @GetMapping("server/inventory/task/checkInventory/{userId}")
    Boolean checkInventory(@PathVariable("userId") Long userId);

    @GetMapping("server/inventory/task/view/{id}")
    InventoryTaskView getInventoryTaskView(@PathVariable("id") Long id);

    /**
     * 查询任务盘点人
     *
     * @param taskId
     * @return
     */
    @GetMapping(value = "server/inventory/task/inventoryUsers/{taskId}")
    InventoryTaskUpdateUserInfoDto getInventoryUsers(@PathVariable("taskId") Long taskId);

    /**
     * 重新分配盘点人
     *
     * @param updateUserDto
     * @return
     */
    @PutMapping(value = "server/inventory/task/updateInventoryUsers")
    Boolean updateInventoryUsers(@RequestBody InventoryTaskUpdateUserDto updateUserDto);

    @PutMapping(value = "server/inventory/task/addInventoryUsers")
    Boolean addInventoryUsers(@RequestBody InventoryTaskAddUserDto addUserDto);

    /**
     * 盘点范围创建
     *
     * @param dto
     * @return
     */
    @PostMapping("server/inventory/range/create")
    List<InventoryRangeGroupDto> createRangeGroup(@RequestBody InventoryRangeGroupCreateDto dto);

    @PostMapping("server/inventory/asset/addPhoto")
    Boolean addPhoto(PlPhotoDto dto);

    @PostMapping("server/inventory/asset/replacePhoto")
    Boolean replacePhoto(PlPhotoDto dto);

    @GetMapping("server/inventory/asset/handleLog")
    InventoryHandleLogDto handleLog(@SpringQueryMap InventoryHandleLogQry qry);

    @GetMapping("server/inventory/asset/handleLog/{inventoryId}")
    List<InventoryHandleLogDto> handleLogList(@PathVariable("inventoryId") Long inventoryId);

}
