package com.niimbot.asset.service;

import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.CusRoleFeignClient;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AccountDto;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusRoleDto;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Stream;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;

import static cn.hutool.core.util.StrUtil.isBlank;
import static cn.hutool.core.util.StrUtil.isNotBlank;

/**
 * 验证码发送前置校验验证
 *
 * <AUTHOR>
 * @Date 2021/2/1
 */
public enum SmsCodeConsumerContext {

    /**
     * 【首页-忘记密码重置】
     */
    RESET_PASSWORD("resetPassword", mobile -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(mobile);
        // CusUserDto byMobile = SpringUtil.getBean(CusUserFeignClient.class).getByMobile(mobile);
        if (null == account) {
            throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        }
    }),
    /**
     * 【注册】
     */
    REGISTER("register", mobile -> {
        /*AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(mobile);
        if (null != account) {
            throw new BusinessException(SystemResultCode.USER_REGISTER_WITH_ACCOUNT);
        }*/
        // SpringUtil.getBean(CusRegisterFeignClient.class).checkRegisterMobile(mobile)
    }
    ),

    /**
     * 【注销】
     *  注销账号不需要对手机号做过多的校验
     */
    CANCEL("cancel",mobile ->{}),
    /**
     * 【登录】
     */
    LOGIN("login", mobile -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(mobile);
        if (account == null) {
            throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        }
        // SpringUtil.getBean(SmsUserDetailsServiceImpl.class).checkMobile(mobile);
    }
    ),
    /**
     * 【绑定手机号】
     */
    BIND_MOBILE("bindMobile", mobile -> {
        // 获取sso tokenService
        CusUserDto cusUser = SpringUtil.getBean("tokenService", AbstractTokenService.class)
                .getLoginUser(ServletUtils.getRequest()).getCusUser();
        if (cusUser != null) {
            AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountById(cusUser.getAccountId());
            // CusUserDto byId = SpringUtil.getBean(CusUserFeignClient.class).getById(cusUser.getId());
            if (isNotBlank(account.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_PHONE_BIND);
            }
        }
    }),
    /**
     * 【用户中心-更换手机号、确认原手机号】
     */
    CHANGE_MOBILE_OLD("changeMobileOld", mobile -> {
        // 获取sso tokenService
        CusUserDto cusUser = SpringUtil.getBean("tokenService", AbstractTokenService.class)
                .getLoginUser(ServletUtils.getRequest()).getCusUser();
        if (cusUser != null) {
            AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountById(cusUser.getAccountId());
            // CusUserDto byId = SpringUtil.getBean(CusUserFeignClient.class).getById(cusUser.getId());
            if (!Objects.equals(mobile, account.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_PHONE_CHANGE_OLD);
            }
        }
    }),
    /**
     * 【用户中心-更换手机号、确认新手机号】
     */
    CHANGE_MOBILE_NEW("changeMobileNew", mobile -> {
        AccountDto account = SpringUtil.getBean(AccountCenterFeignClient.class).getAccountByWay2(mobile);
        // CusEmployeeDto employeeDto = SpringUtil.getBean(CusEmployeeFeignClient.class).checkMobile(mobile);
        if (account != null && isNotBlank(account.getMobile())) {
            throw new BusinessException(SystemResultCode.USER_REGISTER_COMPLETE);
        }
    }),

    /**
     * 修改员工手机号 确认旧手机号
     */
    CHANGE_EMP_MOBILE_OLD("changeEmpMobileOld", mobile -> {
        // 获取sso tokenService
        CusUserDto cusUser = SpringUtil.getBean("tokenService", AbstractTokenService.class)
                .getLoginUser(ServletUtils.getRequest()).getCusUser();
        if (cusUser != null) {
            Long empId = cusUser.getId();
            CusEmployeeDto employeeDto = SpringUtil.getBean(CusEmployeeFeignClient.class).getInfo(empId);
            if (!Objects.equals(mobile, employeeDto.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_PHONE_CHANGE_OLD);
            }
        }
    }),
    /**
     * 修改员工手机号 确认新手机号
     */
    CHANGE_EMP_MOBILE_NEW("changeEmpMobileNew", mobile -> {
        CusEmployeeDto dto = SpringUtil.getBean(CusEmployeeFeignClient.class).checkMobile(mobile);
        if (dto != null && isNotBlank(dto.getMobile())) {
            throw new BusinessException(SystemResultCode.EMPLOYEE_FIELD_EXIST, "手机号");
        }
    }),

    /**
     * 超管转让
     */
    SUPERTUBE_TRANSFER("supertubeTransfer", mobile -> {
        // 员工是否存在
        CusEmployeeDto employeeDto = SpringUtil.getBean(CusEmployeeFeignClient.class)
                .checkMobile(mobile);
        if (employeeDto == null || isBlank(employeeDto.getMobile())) {
            throw new BusinessException(SystemResultCode.EMP_NOT_EXISTS);
        }
        List<CusRoleDto> roleDtos = SpringUtil.getBean(CusRoleFeignClient.class).getRoleByUserId(employeeDto.getId());
        boolean match = roleDtos.stream().anyMatch(cusRoleDto -> cusRoleDto.getRoleCode().equals(BaseConstant.ADMIN_ROLE));
        // 操作人不是超管
        if (!match) {
            throw new BusinessException(SystemResultCode.USER_ROLE_IS_ADMIN);
        }
    }),

    /**
     * 激活账号 通过手机号找不到员工时不允许发送验证码
     */
    ACTIVATE_AN_ACCOUNT("activateAnAccount", mobile -> {
        List<CusEmployeeDto> dtos = SpringUtil.getBean(CusEmployeeFeignClient.class).listByMobile(mobile);
        if (CollUtil.isEmpty(dtos)) {
            throw new BusinessException(SystemResultCode.EMP_NOT_EXISTS);
        }
    }),

    /**
     * 无条件的发送短信验证码；
     * 场景：
     * 1.企业注册
     * 2.绑定社交登录，使用手机验证码
     */
    UNCONDITIONAL("unconditional", mobile -> {
    });

    private final String type;
    private final Consumer<String> consumer;

    SmsCodeConsumerContext(String type, Consumer<String> consumer) {
        this.type = type;
        this.consumer = consumer;
    }

    public String getType() {
        return type;
    }

    public Consumer<String> getConsumer() {
        return consumer;
    }

    /**
     * 根据类型校验手机号
     *
     * @param type   类型
     * @param mobile 手机号
     */
    public static void checkMobile(String type, String mobile) {
        SmsCodeConsumerContext[] enums = SmsCodeConsumerContext.values();
        Stream.of(enums)
                .filter(context -> Objects.equals(context.getType(), type))
                .findFirst().ifPresent(context -> context.getConsumer().accept(mobile));
    }
}
