package com.niimbot.asset.service.feign.report;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.report.AssetLogReportDto;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.GroupReportResult;
import com.niimbot.report.HandleAssetLogDto;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.MaterialCategoryReportSearch;
import com.niimbot.report.MaterialRepositoryReportSearch;
import com.niimbot.report.MeansCategoryReportSearch;
import com.niimbot.report.RepairAssetLogDto;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.UseOrgGroupReportSearch;
import com.niimbot.report.WaitHandleAssetLogDto;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PredefinedReportFeignClient {

    @PostMapping("server/report/predefined/means/useOrgFixedHeadReport")
    GroupReportResult meansUseOrgFixedHeadReport(@RequestBody UseOrgGroupReportSearch search);

    @PostMapping("server/report/predefined/means/categoryFixedHeadReport")
    GroupReportResult meansCategoryFixedHeadReport(@RequestBody MeansCategoryReportSearch search);

    @PostMapping("server/report/predefined/material/categoryFixedHeadReport")
    GroupReportResult materialCategoryFixedHeadReport(@RequestBody MaterialCategoryReportSearch search);

    @PostMapping("server/report/predefined/material/repositoryFixedHeadReport")
    GroupReportResult materialRepositoryFixedHeadReport(@RequestBody MaterialRepositoryReportSearch search);

    @PostMapping("server/report/predefined/means/sortField/{type}")
    QueryConditionSortDto assetSortField(@PathVariable("type") String type);

    @PostMapping("server/report/predefined/means/assetLogReport")
    PageUtils<AssetLogReportDto> assetLogReport(@RequestBody AssetLogReportQueryDto queryDto);

    @PostMapping("server/report/predefined/means/waitHandleAssetLog")
    PageUtils<WaitHandleAssetLogDto> waitHandleAssetLog(@RequestBody WaitHandleAssetLogQueryDto queryDto);

    @PostMapping("server/report/predefined/means/handleAssetLog")
    PageUtils<HandleAssetLogDto> handleAssetLog(@RequestBody HandleAssetLogQueryDto queryDto);

    @PostMapping("server/report/predefined/means/waitReturnAssetLog")
    PageUtils<WaitReturnAssetLogDto> waitReturnAssetLog(@RequestBody WaitReturnAssetLogQueryDto queryDto);

    @PostMapping("server/report/predefined/means/repairAssetLog")
    PageUtils<RepairAssetLogDto> repairAssetLog(@RequestBody RepairAssetLogQueryDto queryDto);

    @PostMapping("server/report/predefined/means/repairAssetRecord")
    PageUtils<RepairAssetRecordDto> repairAssetRecord(@RequestBody RepairAssetRecordQueryDto queryDto);
}
