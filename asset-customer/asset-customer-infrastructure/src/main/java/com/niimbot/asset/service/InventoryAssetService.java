package com.niimbot.asset.service;

import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;

import java.util.List;

import cn.hutool.poi.excel.ExcelWriter;

/**
 * <AUTHOR>
 * @since 2020/12/15 16:44
 */
public interface InventoryAssetService {

    /**
     * 构建固定属性和自定义属性
     */
    List<FormFieldCO> buildAttrCache(List<FormFieldCO> formFields, Long assetFormId, Long standardId);

    /**
     * 写入盘点概述
     *
     * @return excel
     */
    ExcelWriter buildInventoryViewExcel(Long id);

}
