package com.niimbot.asset.service.feign.report;

import com.niimbot.report.*;
import com.niimbot.system.QueryConditionDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/27 上午10:10
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CustomReportFeignClient {

    /**
     * 自定义报表配置
     *
     * @param configDto
     * @return
     */
    @PostMapping("server/report/custom/config")
    DimensionDataDto configReport(@RequestBody CustomReportConfigDto configDto);

    /**
     * 编辑资产自定义统计图和统计表配置
     *
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/report/custom/edit")
    DimensionDataDto editReport(@RequestBody CustomReportConfigDto configDto);

    /**
     * 查询资产自定义统计图和统计表数据
     *
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/report/custom/query")
    DimensionDataDto query(@RequestBody CustomReportConfigDto configDto);

    /**
     * 资产自定义明细报表配置
     *
     * @param customReportConfigDto
     * @return
     */
    @PostMapping("server/report/custom/detailReportConfig")
    AssetStatisticsDataDto detailReportConfig(@RequestBody CustomReportConfigDto customReportConfigDto);

    /**
     * 编辑资产自定义明细报表
     *
     * @param customReportConfigDto
     * @return
     */
    @PostMapping("server/report/custom/editDetail")
    AssetStatisticsDataDto editDetailReport(@RequestBody CustomReportConfigDto customReportConfigDto);

    /**
     * 资产自定义明细报表查询
     *
     * @param customReportConfigDto
     * @return
     */
    @PostMapping("server/report/custom/queryDetailReport")
    AssetStatisticsDataDto queryDetailReport(@RequestBody CustomReportConfigDto customReportConfigDto);

    /**
     * 查询报表配置信息
     *
     * @param id
     * @return
     */
    @GetMapping("server/report/custom/queryReportConfig/{id}")
    CustomReportConfigDto queryReportConfig(@PathVariable("id") Long id);

    /**
     * 分析维度筛选条件查询
     *
     * @param reportId
     * @return
     */
    @GetMapping("server/report/custom/dimension/query/field")
    DimensionFieldDto dimensionQueryField(@RequestParam(value = "reportId") Long reportId);

    /**
     * 数据指标字段查询
     *
     * @param reportId
     * @return
     */
    @GetMapping("server/report/custom/norm/query/field")
    List<NormItemDto> normQueryField(@RequestParam(value = "reportId") Long reportId);

    /**
     * 统计范围字段查询
     *
     * @param reportId
     * @return
     */
    @GetMapping("server/report/custom/statisticsCondition/query/field")
    List<QueryConditionDto> statisticsConditionField(@RequestParam(value = "reportId") Long reportId);

    /**
     * 查询企业自定义报表列表
     *
     * @return
     */
    @GetMapping("server/report/custom/queryAll")
    List<ReportConfigItemDto> queryAll();

    /**
     * 删除自定义报表配置
     *
     * @param id
     * @return
     */
    @DeleteMapping("server/report/custom/remove/{id}")
    Boolean dropConfig(@PathVariable("id") Long id);


    /**
     * 统计范围描述
     * @param id
     * @return
     */
    @GetMapping("server/report/custom/statisticsConditionDesc/{id}")
    List<StatisticsConditionDescDto> statisticsConditionDesc(@PathVariable("id") Long id);
}
