package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialDto;
import com.niimbot.material.MaterialQueryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.ImportImages;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.QueryConditionSortDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/7 9:10
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialFeignClient {

    /**
     * 分页查询
     *
     * @param queryDto 查询条件
     * @return 结果
     */
    @PostMapping(value = "server/material/material/page")
    PageUtils<MaterialDto> materialPage(@RequestBody MaterialQueryDto queryDto);

    /**
     * 查询获取耗材信息
     *
     * @param materialId 耗材ID
     * @return 耗材信息
     */
    @GetMapping(value = "server/material/material/{materialId}")
    MaterialDto getInfo(@PathVariable("materialId") Long materialId);

    @GetMapping(value = "server/material/material/getInRecycleBin/{id}")
    MaterialDto getInRecycleBin(@PathVariable("id") Long id);

    @PostMapping(value = "server/material/material/list")
    List<MaterialDto> getInfoList(@RequestParam(value = "ids") List<Long> ids);

    @GetMapping(value = "server/material/material/noPerm/{materialId}")
    MaterialDto getInfoNoPerm(@PathVariable(value = "materialId") long materialId);

    @GetMapping(value = "server/material/material/noPerm/php/{materialId}")
    MaterialDto getInfoNoPermPhp(@PathVariable(value = "materialId") String materialId);

    @PostMapping(value = "server/material/material")
    MaterialDto add(MaterialDto materialDto);

    @PutMapping(value = "server/material/material")
    Boolean edit(MaterialDto materialDto);

    @DeleteMapping(value = "server/material/material")
    List<AuditableOperateResult> remove(@RequestBody List<Long> materialIds, @RequestParam("checkOrder") Boolean checkOrder);

    @PostMapping("server/material/material/removePre")
    Boolean removePre(@RequestBody List<Long> ids);

    @PostMapping("server/material/material/hasOrder")
    Boolean hasOrder(@RequestBody List<Long> materialIds);

    /**
     * 查询耗材总数
     *
     * @return 数量
     */
    @GetMapping(value = "server/material/material/countMaterial")
    Integer countMaterial();

    /**
     * 删除全部
     */
    @DeleteMapping(value = "server/material/material/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    /**
     * 导入资产数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "server/material/material/saveSheetData")
    Boolean saveSheetData(AssetImportDto importDto);

    @PostMapping(value = "server/material/material/saveEditSheetData")
    Boolean saveEditSheetData(AssetImportDto importDto);

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "server/material/material/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    /**
     * 查询导入错误数据
     *
     * @return 错误数据
     */
    @GetMapping(value = "server/material/material/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);

    @GetMapping("server/material/material/sortField")
    QueryConditionSortDto sortField();

    @PostMapping("server/material/material/images/check/{action}")
    List<ImportImages> materialImagesCheck(@RequestBody List<ImportImages> codes, @PathVariable("action") Integer action, @RequestHeader("Authorization") String token);

    @PostMapping("server/material/material/images/import/{action}")
    List<ImportImages> materialImagesModify(@RequestBody List<ImportImages> images, @PathVariable("action") Integer action, @RequestHeader("Authorization") String token);
}
