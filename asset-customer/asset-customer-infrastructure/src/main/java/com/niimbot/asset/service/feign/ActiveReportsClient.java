package com.niimbot.asset.service.feign;

import com.niimbot.system.UserActiveRecordDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 活跃记录feign客户端
 *
 * <AUTHOR>
 * @Date 2021/03/31
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ActiveReportsClient {

    /**
     * 添加活跃记录
     *
     * @param userActiveRecordDto 活跃记录对象
     * @return Boolean
     */
    @PostMapping(value = "server/system/activeRecord")
    Boolean saveActiveRecord(UserActiveRecordDto userActiveRecordDto);

}
