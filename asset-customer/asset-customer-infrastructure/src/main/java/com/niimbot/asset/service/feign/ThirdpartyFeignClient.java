package com.niimbot.asset.service.feign;

import com.niimbot.thirdparty.AssetThirdPartyEmpMapping;
import com.niimbot.thirdparty.AssetThirdPartyOrgMapping;
import com.niimbot.thirdparty.BindThirdPartyResult;
import com.niimbot.thirdparty.DingtalkBindDto;
import com.niimbot.thirdparty.DingtalkReplenish;
import com.niimbot.thirdparty.FeishuBindDto;
import com.niimbot.thirdparty.FeishuJsAuth;
import com.niimbot.thirdparty.FeishuJsTicketDto;
import com.niimbot.thirdparty.ThirdPartyDto;
import com.niimbot.thirdparty.TransMismatchEmp;
import com.niimbot.thirdparty.WeChatBindDto;
import com.niimbot.thirdparty.WechatJsAuth;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import feign.Request;

/**
 * <AUTHOR>
 * @since 2021/11/9 10:01
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ThirdpartyFeignClient {

    @PostMapping(value = "server/thirdparty/bind/dingtalk")
    BindThirdPartyResult bindDingtalk(DingtalkBindDto dingtalk);

    @PostMapping("server/thirdparty/replenish/dingtalk")
    Boolean replenishDingtalk(@RequestBody DingtalkReplenish dto);

    @GetMapping(value = "server/thirdparty/bind/info")
    ThirdPartyDto getBindInfo(@RequestParam(value = "type", required = false) String type);

    @DeleteMapping(value = "server/thirdparty/bind/dingtalk")
    Boolean removeDingtalk();

    @PostMapping(value = "server/thirdparty/bind/wechat")
    BindThirdPartyResult bindWeChat(WeChatBindDto wechat);

    @DeleteMapping(value = "server/thirdparty/bind/wechat")
    Boolean removeWeChat();

    @PostMapping(value = "server/thirdparty/bind/feishu")
    BindThirdPartyResult bingFeishu(FeishuBindDto feishu);

    @DeleteMapping(value = "server/thirdparty/bind/feishu")
    Boolean removeFeishu();

    @GetMapping(value = "server/system/thirdparty/list")
    boolean list();

    @GetMapping("server/thirdparty/org/getMapping/{type}")
    AssetThirdPartyOrgMapping getOrgMapping(Request.Options options, @PathVariable("type") String type);

    @PostMapping("server/thirdparty/org/handleMapping/{type}")
    Boolean handleOrgMapping(Request.Options options, @PathVariable("type") String type, @RequestBody List<AssetThirdPartyOrgMapping.Mapping> mappings);

    @GetMapping("server/thirdparty/emp/getMapping/{type}")
    AssetThirdPartyEmpMapping getEmpMapping(Request.Options options, @PathVariable("type") String type);

    @PostMapping("server/thirdparty/handle/{type}")
    Boolean handle(Request.Options options, @PathVariable("type") String type, @RequestBody List<TransMismatchEmp> transMismatchEmps);

    @GetMapping("server/thirdparty/syncStep/{type}")
    Map<String, Object> process(@PathVariable("type") String type);

    @PostMapping("server/thirdparty/overallHandle")
    Boolean overallHandle(@RequestParam("type") String type);

    @DeleteMapping("server/thirdparty/syncStep/clean")
    Boolean cleanSyncStepCache(@RequestParam("type") String type, @RequestParam(value = "companyId", required = false) Long companyId);

    @GetMapping("server/thirdparty/isBindThirdParty/{companyId}")
    Boolean isBindThirdParty(@PathVariable("companyId") Long companyId);

    @RequestMapping("server/thirdparty/syncApproveRole/manualSyncFromDing/{companyId}")
    boolean manualSyncFromDing(@PathVariable("companyId") Long companyId);

    @PostMapping("/server/thirdparty/user/auth/wechat/getSignature")
    Map<String, String> getWechatJsSignature(@RequestBody WechatJsAuth wechatJsAuth);

    @PostMapping("/server/thirdparty/user/auth/feishu/getSignature")
    FeishuJsTicketDto getFeishuJsSignature(@RequestBody FeishuJsAuth feishuJsAuth);

    @PostMapping(value = "/server/thirdparty/user/auth/authenticate")
    Map<String, Object> authenticate(Map<String, Object> authMap);
}
