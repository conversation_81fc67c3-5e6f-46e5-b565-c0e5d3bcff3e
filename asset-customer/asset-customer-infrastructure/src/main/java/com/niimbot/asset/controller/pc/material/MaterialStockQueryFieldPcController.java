package com.niimbot.asset.controller.pc.material;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionGeneralDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @since 2022/9/5 14:49
 */
@Slf4j
@Validated
@Api(tags = "耗材库存字段管理")
@ResultController
@RequestMapping("api/pc/queryField/material/stock")
@RequiredArgsConstructor
public class MaterialStockQueryFieldPcController {

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @ApiOperation(value = "【PC】筛选项配置-保存")
    @PostMapping("/query/field")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@RequestBody QueryConditionGeneralDto condition) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(), QueryFieldConstant.TYPE_MATERIAL_STOCK_QUERY));
    }

    @ApiOperation(value = "【PC】筛选项配置-查询")
    @GetMapping("/query/field")
    public QueryConditionGeneralDto getQueryField() {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_STOCK_QUERY);
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(QueryConditionGeneralDto.class);
    }

}
