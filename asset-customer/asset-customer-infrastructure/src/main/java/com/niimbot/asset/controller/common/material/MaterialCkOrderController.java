package com.niimbot.asset.controller.common.material;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.MaterialOrderService;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.MaterialCkOrderFeignClient;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.GrantingDto;
import com.niimbot.material.MaterialCkOrderDetailDto;
import com.niimbot.material.MaterialCkOrderDto;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.material.MaterialOrderResponseDto;
import com.niimbot.material.SensitiveMaterialCkOrderDetailDto;
import com.niimbot.material.SensitiveMaterialCkOrderDto;
import com.niimbot.material.SensitiveMaterialCkOrderSubmitDto;
import com.niimbot.system.Auditable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/7/12 18:16
 */
@Slf4j
@Api(tags = "【耗材】单据")
@ResultController
@RequestMapping("api/common/material/order/ck")
@Validated
public class MaterialCkOrderController {

    private final MaterialCkOrderFeignClient materialCkOrderFeignClient;

    private final ActWorkflowFeignClient workflowFeignClient;

    private final AsMaterialUtil materialUtil;

    private final DictConvertUtil dictConvertUtil;

    private final MaterialService materialService;

    private final MaterialOrderService materialOrderService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    public MaterialCkOrderController(MaterialCkOrderFeignClient materialCkOrderFeignClient,
                                     ActWorkflowFeignClient workflowFeignClient,
                                     AsMaterialUtil materialUtil,
                                     DictConvertUtil dictConvertUtil,
                                     MaterialService materialService,
                                     MaterialOrderService materialOrderService) {
        this.materialCkOrderFeignClient = materialCkOrderFeignClient;
        this.workflowFeignClient = workflowFeignClient;
        this.materialUtil = materialUtil;
        this.dictConvertUtil = dictConvertUtil;
        this.materialService = materialService;
        this.materialOrderService = materialOrderService;
    }

    @ApiOperation(value = "【出库单】设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated SensitiveMaterialCkOrderDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_CK);
        return materialCkOrderFeignClient.getWorkflowStepList(dto);
    }


    @ApiOperation(value = "【出库单】出库单详情")
    @GetMapping("/{id}")
    @AutoConvert
    public MaterialOrderResponseDto getById(@PathVariable Long id) {
        MaterialCkOrderDto orderDto = materialCkOrderFeignClient.getById(id);
        if (ObjectUtil.isNotNull(orderDto)) {
            dictConvertUtil.convertToDictionary(orderDto);
            orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_CK);
            // 翻译耗材
            JSONObject orderJson = materialUtil.toJSONObject(orderDto);
            //数据脱敏处理
            if (Objects.nonNull(orderJson)) {
                desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderJson), SensitiveObjectTypeEnum.MATERIAL.getCode());
            }
            if (orderDto.getApproveStatus() == 0) {
                return new MaterialOrderResponseDto()
                        .setOrder(orderJson)
                        .setApproveInfo(new WorkflowApproveInfoDto());
            }
            WorkflowApproveInfoDto approveInfoDto;
            if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_CK, id);
                if (approveInfoDto == null || approveInfoDto.getExecuteList().isEmpty()) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_CK, id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_CK, id);
                if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(orderDto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
            return new MaterialOrderResponseDto()
                    .setOrder(orderJson)
                    .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
        } else {
            throw new BusinessException(MaterialResultCode.ORDER_NOT_EXISTS, "出库单" + id);
        }
    }

    @ApiOperation(value = "【出库单】出库单批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<MaterialOrderResponseDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_CK);
        return requestDto.getOrderIds().stream().map(this::getById).collect(Collectors.toList());
    }

    @ApiOperation(value = "【出库单】发放中数量")
    @GetMapping("/granting/{orderId}/{materialId}")
    public List<GrantingDto> grantingDetail(@PathVariable("orderId") Long orderId,
                                            @PathVariable("materialId") Long materialId) {
        return materialCkOrderFeignClient.grantingDetail(orderId, materialId);
    }

    @ApiOperation(value = "【出库单】已发放数量")
    @GetMapping("/grant/{orderId}/{materialId}")
    public List<GrantingDto> grantDetail(@PathVariable("orderId") Long orderId,
                                         @PathVariable("materialId") Long materialId) {
        return materialCkOrderFeignClient.grantDetail(orderId, materialId);
    }

    @ApiOperation(value = "【出库单】出库单明细详情")
    @GetMapping("/detail/{orderId}/{materialId}")
    public JSONObject getDetail(@PathVariable("orderId") Long orderId,
                                @PathVariable("materialId") Long materialId) {
        MaterialCkOrderDetailDto detailDto = materialCkOrderFeignClient.getDetail(orderId, materialId);
        if (detailDto == null) {
            throw new BusinessException(MaterialResultCode.ORDER_DETAIL_NOT_EXIST);
        }
        JSONObject result = materialUtil.toJSONObject(detailDto);
        //数据脱敏处理
        if (Objects.nonNull(result)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return result;
    }

    @ApiOperation(value = "【出库单】出库单分页")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody @Validated MaterialOrderQueryDto query) {
//        resolveDate(query);
        PageUtils<MaterialCkOrderDto> page = materialCkOrderFeignClient.page(query);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (MaterialCkOrderDto orderDto : page.getList()) {
            orderDto.setOrderType(OrderFormTypeEnum.CK.getCode());
            //数据脱敏处理
//            if (CollUtil.isNotEmpty(orderDto.getMaterials())) {
//                desensitizationDataUtil.handleSensitiveField(orderDto.getMaterials()
//                        .stream()
//                        .map(MaterialCkOrderDetailDto::getMaterialSnapshotData)
//                        .collect(Collectors.toList()), SensitiveObjectTypeEnum.MATERIAL.getCode());
//            }
            list.add(materialUtil.toJSONObject(orderDto));
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【出库单】出库单分页")
    @GetMapping("/page")
    public PageUtils<JSONObject> pageGet(MaterialOrderQueryDto dto) {
        return page(dto);
    }

    @ApiOperation(value = "【出库单】出库单耗材详情")
    @GetMapping("/detail/page")
    public PageUtils<JSONObject> materialPage(@Validated MaterialOrderDetailQueryDto dto) {
        PageUtils<MaterialCkOrderDetailDto> page = materialCkOrderFeignClient.pageDetail(dto);
        List<JSONObject> list = new ArrayList<>();
        for (MaterialCkOrderDetailDto detailDto : page.getList()) {
            JSONObject jsonObject = materialUtil.toJSONObject(detailDto);
            list.add(jsonObject);
        }
        //数据脱敏处理
        if (CollUtil.isNotEmpty(list)) {
            desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【出库单】出库单新增")
    @PostMapping
    @RepeatSubmit
    @AuditLog(Auditable.Action.OR_MRL_CK)
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public AuditableCreateOrderResult create(@RequestBody @Validated SensitiveMaterialCkOrderSubmitDto submitDto) {
        SensitiveMaterialCkOrderDto orderDto = submitDto.getOrderDto();
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_CK);
        materialUtil.verifyAndTranslation(orderDto, AssetConstant.ORDER_TYPE_MATERIAL_CK);
        List<Long> materialIds = orderDto.getMaterials().stream().map(SensitiveMaterialCkOrderDetailDto::getMaterialId).collect(Collectors.toList());
        Map<Long, JSONObject> infoMap = materialService.getInfoMap(materialIds);
        // 处理耗材列表，过滤出库数量为0的耗材
        List<SensitiveMaterialCkOrderDetailDto> collect = orderDto.getMaterials().stream()
                .filter(detailDto -> BigDecimal.ZERO.compareTo(detailDto.getCkNum()) < 0)
                .peek(detailDto -> {
                    JSONObject data = infoMap.get(detailDto.getMaterialId());
                    detailDto.setMaterialSnapshotData(data);
                }).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            throw new BusinessException(MaterialResultCode.CK_NUM_NOT_ZERO);
        }
        // 总种类
        int totalType = 0;
        // 总数量
        BigDecimal totalNum = BigDecimal.ZERO;
        for (SensitiveMaterialCkOrderDetailDto m : collect) {
            totalType += 1;
            totalNum = totalNum.add(m.getCkNum());
        }
        orderDto.setTotalNum(totalNum).setTotalType(totalType);
        orderDto.setMaterials(collect);

        List<JSONObject> sortList = collect.stream().map(d -> infoMap.get(d.getMaterialId())).collect(Collectors.toList());
        orderDto.setSummary(materialUtil.buildSummary(sortList));
        return materialCkOrderFeignClient.create(submitDto);
    }


    @ApiOperation(value = "【导出】导出出库单据卡片")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderCard(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialCkOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrderCard(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_CK);
    }

    @ApiOperation(value = "【导出】导出出库单耗材")
    @PostMapping(value = "/exportOrder/materials")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrder(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialCkOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrder(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_CK);
    }
}
