package com.niimbot.asset.service.feign;

import com.niimbot.system.TaskRecordDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface TaskRecordFeignClient {

    @PostMapping("/server/system/task/record")
    boolean addTaskRecord(@RequestBody TaskRecordDto dto);

}
