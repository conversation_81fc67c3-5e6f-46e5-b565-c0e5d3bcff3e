package com.niimbot.asset.service.feign;

import com.niimbot.sale.AsInvoiceInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发票
 *
 * <AUTHOR>
 * @date 2021/8/7 10:56
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface InvoiceFeignClient {

    /**
     * 新增发票
     *
     * @param invoice
     * @return
     */
    @PostMapping("server/sale/invoice")
    Boolean save(@RequestBody AsInvoiceInfoDto invoice);

    /**
     * 修改发票
     *
     * @param invoice
     * @return
     */
    @PutMapping("server/sale/invoice")
    Boolean update(@RequestBody AsInvoiceInfoDto invoice);

    /**
     * 删除发票
     *
     * @param id
     * @return
     */
    @DeleteMapping("server/sale/invoice/{id}")
    Boolean delete(@PathVariable("id") Long id);

    /**
     * 发票列表
     *
     * @return
     */
    @GetMapping("server/sale/invoice/list")
    List<AsInvoiceInfoDto> queryList();
}
