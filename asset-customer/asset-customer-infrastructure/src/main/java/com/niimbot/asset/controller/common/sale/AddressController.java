package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.AddressFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.sale.AddressDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/28 17:19
 */
@Api(tags = "【服务中心】收货地址")
@ResultController
@RequestMapping("api/common/emailAddress")
@RequiredArgsConstructor
public class AddressController {

    private final AddressFeignClient addressFeignClient;

    @ApiOperation(value = "新增收货地址")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean save(@RequestBody @Validated(value = {Insert.class}) AddressDto addressDto) {
        return addressFeignClient.save(addressDto);
    }

    @ApiOperation(value = "编辑收货地址")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean update(@RequestBody @Validated(value = {Update.class}) AddressDto addressDto) {
        return addressFeignClient.update(addressDto);
    }

    @ApiOperation(value = "删除收货地址")
    @DeleteMapping("/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("id") Long id) {
        return addressFeignClient.delete(id);
    }

    @ApiOperation(value = "收货地址列表")
    @GetMapping("/list")
    public List<AddressDto> queryList() {
        return addressFeignClient.queryList();
    }

    @ApiOperation(value = "设置默认")
    @PutMapping("/default/{id}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean setDefault(@PathVariable("id") Long id) {
        return addressFeignClient.setDefault(id);
    }

}
