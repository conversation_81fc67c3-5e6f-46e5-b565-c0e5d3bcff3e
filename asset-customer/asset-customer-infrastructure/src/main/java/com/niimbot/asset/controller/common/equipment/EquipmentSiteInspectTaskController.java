package com.niimbot.asset.controller.common.equipment;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.equipment.EquipmentSiteInspectTaskFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.equipment.SiteInspectTaskDetailExportDto;
import com.niimbot.equipment.SiteInspectTaskEditExecutorsDto;
import com.niimbot.equipment.SiteInspectTaskInfoDto;
import com.niimbot.equipment.SiteInspectTaskPageDto;
import com.niimbot.equipment.SiteInspectTaskPageExportDto;
import com.niimbot.equipment.SiteInspectTaskQueryDto;
import com.niimbot.equipment.SiteInspectTaskStatisticsDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableOperateResult;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/9 15:31
 */
@Api(tags = "【设备管理】巡检任务")
@Slf4j
@RequiredArgsConstructor
@ResultController
@RequestMapping("api/common/equipment/siteInspect/task")
public class EquipmentSiteInspectTaskController {

    private final EquipmentSiteInspectTaskFeignClient feignClient;
    private final DictConvertUtil dictConvertUtil;

    @ApiOperation(value = "巡检任务——分页")
    @PostMapping("/page")
    @AutoConvert
    public PageUtils<SiteInspectTaskPageDto> page(@RequestBody SiteInspectTaskQueryDto query) {
        return feignClient.page(query);
    }

    @ApiOperation(value = "巡检任务——详情")
    @GetMapping("/info")
    @AutoConvert
    public SiteInspectTaskInfoDto info(@RequestParam("taskId") Long taskId,
                                       @RequestParam(value = "full", required = false, defaultValue = "false") Boolean full) {
        return feignClient.info(taskId, full);
    }

    @ApiOperation(value = "巡检任务——状态统计")
    @GetMapping("/statistics")
    public SiteInspectTaskStatisticsDto taskStatistics() {
        return feignClient.taskStatistics();
    }

    @ApiOperation(value = "巡检任务——结束任务")
    @PostMapping("/close/{taskId}")
    @RepeatSubmit
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean closeTask(@PathVariable("taskId") Long taskId) {
        return feignClient.closeTask(taskId);
    }

    @ApiOperation(value = "巡检任务——作废")
    @PostMapping("/cancel")
    @RepeatSubmit
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean cancelTask(@RequestBody List<Long> taskIds) {
        List<AuditableOperateResult> results = feignClient.cancelTask(taskIds);
        if (CollUtil.isNotEmpty(results)) {
            AuditLogs.sendRecord(() -> {
                Map<String, Object> params = Auditable.Resolver.resolveParams(results);
                return AuditLogRecord.create(Auditable.Action.CANCEL_SITE_INSPECT_TASK, params);
            });
            return true;
        } else {
            return false;
        }
    }

    @ApiOperation(value = "巡检任务——转派")
    @PostMapping("/edit/executors")
    @RepeatSubmit
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean editExecutors(@RequestBody @Validated SiteInspectTaskEditExecutorsDto editExecutorsList) {
        List<AuditableOperateResult> result = feignClient.editExecutors(editExecutorsList);
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.UPT_SITE_INSPECT_TASK_EXECUTORS, result));
        return true;
    }

    @ApiOperation(value = "巡检任务——导出")
    @PostMapping("/export")
    public void export(@RequestBody SiteInspectTaskQueryDto query, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SiteInspectTaskPageExportDto.class);
            // 查询数据
            query.setPageNum(1);
            query.setPageSize(Integer.MAX_VALUE);
            PageUtils<SiteInspectTaskPageDto> taskPage = feignClient.page(query);
            List<SiteInspectTaskPageDto> list = taskPage.getList();
            dictConvertUtil.convertToDictionary(list);
            List<SiteInspectTaskPageExportDto> exportList = list.stream().map(f -> BeanUtil.copyProperties(f, SiteInspectTaskPageExportDto.class)).collect(Collectors.toList());
            String fileName = "巡检任务-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            ExcelUtils.export(response, new ExcelExportDto(headerData, exportList), fileName + ".xlsx");
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_SITE_INSPECT_TASK, Collections.singletonMap(Auditable.Tpl.COUNT, String.valueOf(exportList.size()))));
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

    @ApiOperation(value = "巡检任务——导出详情")
    @GetMapping("/detail/export")
    public void export(@RequestParam("taskId") Long taskId, HttpServletResponse response) {
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(SiteInspectTaskDetailExportDto.class);
            // 查询数据
            List<SiteInspectTaskDetailExportDto> exportList = feignClient.detailExport(taskId);
            dictConvertUtil.convertToDictionary(exportList);
            String name = CollUtil.isNotEmpty(exportList) ? exportList.get(0).getTaskName() : "巡检任务详情";
            String fileName = name + "-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            ExcelUtils.export(response, new ExcelExportDto(headerData, exportList), fileName + ".xlsx");
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.EXP_SITE_INSPECT_TASK_DETAIL, Collections.singletonMap(Auditable.Tpl.CONTENT, name)));
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
