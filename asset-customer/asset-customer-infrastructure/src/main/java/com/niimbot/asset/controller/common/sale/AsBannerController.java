package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.service.feign.BannerFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.BannerPageDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <p>
 * Banner图详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Api(tags = "商城购物")
@ResultController
@RequestMapping("/api/banner")
@RequiredArgsConstructor
public class AsBannerController {

    private final BannerFeignClient feignClient;

    @ApiOperation(value = "banner列表")
    @GetMapping("/page/{localId}")
    public List<BannerPageDto> page(@PathVariable Long localId) {
       return feignClient.getBannerList(localId);
    }


}
