package com.niimbot.asset.controller.common.means;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.PrintFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.PrintDataViewDto;
import com.niimbot.means.UserPrintCheckInfoDto;
import com.niimbot.means.UserPrintLogDto;
import com.niimbot.means.UserPrintTaskConfigDto;
import com.niimbot.means.UserPrintTaskDto;
import com.niimbot.means.UserPrintTaskInfoDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 打印任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Api(tags = "资产打印任务接口")
@ResultController
@RequestMapping("api/common/print/task")
@RequiredArgsConstructor
@Validated
public class UserPrintTaskController {

    private final PrintFeignClient printFeignClient;

    private final AbstractFileUtils fileUtils;

    private static final Integer MAX_PRINT_ASSET_NUM = 500;

    @ApiOperation(value = "新增打印任务")
    @PostMapping(value = "add")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public PrintDataViewDto addTask(@RequestBody @Validated UserPrintTaskDto printTask) {
        return printFeignClient.save(printTask);
    }

    @ApiOperation(value = "开始打印任务")
    @PostMapping(value = "start")
    public PrintDataViewDto startTask(@RequestBody @Validated UserPrintTaskConfigDto configDto) {
        return printFeignClient.startTask(configDto);
    }

    @ApiOperation(value = "暂停打印任务")
    @PostMapping(value = "pause")
    public Boolean pauseTask(@RequestBody @NotEmpty List<@Valid UserPrintLogDto> logs) {
        return printFeignClient.pauseTask(logs);
    }

    @ApiOperation(value = "取消打印任务")
    @GetMapping(value = "cancel")
    public Boolean cancelTask(@RequestParam Long taskId) {
        return printFeignClient.cancelTask(taskId);
    }

    @ApiOperation(value = "继续打印任务")
    @PostMapping(value = "continue")
    public PrintDataViewDto continueTask(@RequestBody @Validated UserPrintTaskConfigDto configDto) {
        return printFeignClient.continueTask(configDto);
    }

    @ApiOperation("再次打印全部")
    @PostMapping("againTask")
    public PrintDataViewDto againTask(@RequestBody UserPrintTaskDto printTask) {
        return printFeignClient.againTask(printTask);
    }

    @ApiOperation("再次打印一个")
    @PostMapping("continueTaskPrintOne")
    public PrintDataViewDto continueTaskPrintOne(@RequestBody UserPrintTaskConfigDto configDto) {
        return printFeignClient.continueTaskPrintOne(configDto);
    }

    @ApiOperation(value = "检查打印机是否匹配任务")
    @GetMapping(value = "continue/check")
    public UserPrintCheckInfoDto continueCheck(@NotNull(message = "打印任务不能为空") Long taskId,
                                               @NotBlank(message = "打印机名称不能为空") String printerName) {
        UserPrintCheckInfoDto res = printFeignClient.continueTaskCheck(taskId, printerName);
        UserPrintTaskInfoDto taskInfo = res.getTaskInfo();
        String tagUrl = taskInfo.getTagUrl();
        taskInfo.setTagUrl(fileUtils.convertToDownloadUrl(tagUrl));
        return res;
    }

    @ApiOperation(value = "修改打印配置")
    @PutMapping(value = "config")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean configTask(@RequestBody @Validated UserPrintTaskConfigDto printTask) {
        return printFeignClient.configTask(printTask);
    }

    @ApiOperation(value = "获取打印任务详情信息")
    @GetMapping(value = "{taskId}")
    public UserPrintTaskInfoDto taskAppInfo(@PathVariable("taskId") Long taskId) {
        return printFeignClient.taskInfo(taskId);
    }

    @ApiOperation("获取打印标签的打印JSON")
    @GetMapping("/printJson/{tagSizeId}")
    public JSONObject getPrintJsonByTagSizeId(@NotNull(message = "标签尺寸ID不能为空") @PathVariable("tagSizeId") Long tagSizeId) {
        return printFeignClient.getPrintJsonByTagSizeId(tagSizeId);
    }

    @ApiOperation("改为资产编码后打印")
    @PostMapping("/changeAssetTagQrCodeValue/{tagId}")
    public Boolean changeAssetTagQrCodeValue(@PathVariable Long tagId) {
        return printFeignClient.changeAssetTagQrCodeValue(tagId);
    }

    @ApiOperation("是否支持RFID标签打印")
    @PostMapping("/isSupportRfid/{type}")
    public Boolean isSupportRfid(@RequestBody UserPrintTaskDto printTask, @ApiParam(name = "type", value = "all：检查是否支持RFID与检查标签的二维码是否是唯一字段，rfid：检查是否支持RFID， tag：检查标签的二维码是否是唯一字段") @PathVariable("type") String type) {
        return printFeignClient.isSupportRfid(printTask, type);
    }

}
