package com.niimbot.asset.service.feign;

import com.niimbot.system.AssetHelpDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/8 下午2:49
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface HomeHelpFeignClient {

    @GetMapping("server/system/helpDoc/query")
    List<AssetHelpDto> queryByTitle(@RequestParam("kw") String kw);
}
