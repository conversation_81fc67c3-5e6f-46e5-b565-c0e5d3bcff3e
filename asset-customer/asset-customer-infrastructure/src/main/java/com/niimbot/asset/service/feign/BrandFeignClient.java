package com.niimbot.asset.service.feign;

import com.niimbot.means.AsBrandDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/8 15:56
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface BrandFeignClient {

    @GetMapping(value = "server/means/brand/search")
    List<AsBrandDto> search(@RequestParam(value = "name", required = false) String name);
}
