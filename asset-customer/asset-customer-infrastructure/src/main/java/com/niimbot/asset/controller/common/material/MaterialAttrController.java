package com.niimbot.asset.controller.common.material;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/9 17:39
 */
@Api(tags = "【耗材】属性管理")
@ResultController
@RequestMapping("api/common/materialAttr")
@Validated
public class MaterialAttrController {

    private final FormFeignClient formFeignClient;

    @Autowired
    public MaterialAttrController(FormFeignClient formFeignClient) {
        this.formFeignClient = formFeignClient;
    }


    @ApiOperation(value = "查询耗材属性")
    @GetMapping(value = "/list")
    public FormVO materialAttr() {
        // 查询设置显示的数据
        return formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
    }

    @ApiOperation(value = "【PC】唯一键属性列表")
    @GetMapping(value = "/field/unique")
    public List<Map<String, Object>> materialFieldUnique() {
        FormVO formVO = materialAttr();
        List<FormFieldCO> formFields = formVO.getFormFields();
        // 过滤唯一键字段
        return formFields.stream().filter(f -> {
            if (ListUtil.of(FormFieldCO.YZC_ASSET_SERIALNO,
                    FormFieldCO.YZC_MATERIAL_SERIALNO,
                    FormFieldCO.YZC_SERIALNO).contains(f.getFieldType())) {
                return true;
            }
            boolean isUnique = f.getFieldProps().containsKey("unique") ? f.getFieldProps().getBoolean("unique") :
                    false;
            return !FormFieldCO.SPLIT_LINE.equals(f.getFieldType()) && !f.isHidden() && !f.requiredProps() && isUnique;
        }).map(f -> ImmutableMap.<String, Object>builder()
                .put("fieldCode", f.getFieldCode())
                .put("fieldType", f.getFieldType())
                .put("fieldName", f.getFieldName())
                .build()
        ).collect(Collectors.toList());
    }

}
