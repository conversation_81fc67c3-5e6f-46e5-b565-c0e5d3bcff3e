package com.niimbot.asset.security.config;

/**
 * <AUTHOR>
 * @date 2021/1/15 11:14
 */
public class AccessConfig {
    public static final String[] ACCESS = {"/actuator/**",
            "/api/common/captchaImage",
            "/api/common/smsCode/**",
            "/api/common/emailCode/**",
            "/api/common/checkSmsCode",
            "/api/common/checkCode",
            "/api/common/industry/tree",
            "/api/common/register",
            "/api/common/register/progress",
            "/api/dingtalk/recommend/register",
            "/api/weixin/recommend/register",
            "/api/common/recommend/**",
            "/api/common/login/third",
            "/api/pc/login",
            "/api/pc/login/sms",
            "/api/pc/login/qrCode",
            "/api/pc/login/qrCode/check",
            "/api/pc/login/rsa/encrypt",
            "/api/pc/login/rsa/decrypt",
            "/api/app/login",
            "/api/app/login/sms",
            "/api/app/login/password",
            "/api/pc/login/social",
            "/api/pc/login/banding",
            "/api/app/login/social",
            "/api/app/login/banding",
            "/api/common/openApi/login",
            "/api/common/openApi/oauth2.0/reverseAuthByCode",
            "/api/common/file/{id}",
            "/api/common/employee/scanInvite/**",
            "/api/common/password",
            "/api/common/password/verifyMobile",
            "/api/common/userCenter/userFeedback",
            "/api/app/version/management/latestVersion/**",
            "/api/common/sale/order/notify",
            "/api/webSocket/**",
            "/api/common/thirdparty/callback/**",
            "/material/**", "/print/**", "/tags/**", "/toolbox/**", "/navigation/**",
            "/api/common/lastPrivacyAgreement/**",
            "/api/common/account/center/dingtalk/auth/**",
            "/api/common/account/center/activateAccountByMobile",
            "/api/common/account/center/activateAccountByThirdParty",
            "/api/common/account/center/activateAccountByThirdPartyBindMobile",
            "/api/common/account/center/activateAccountByThirdPartyH5",
            "/api/common/account/center/loginByThirdParty",
            "/api/common/account/center/bindThirdPartyAndLogin/**",
            "/api/common/account/center/accountExistBindCompany",
            "/api/common/account/center/getCompanyConfig/**",
            "/api/common/stsToken",
            "/api/common/workflow/callback/error/{orderType}",
            "/api/dingtalk/auth/**",
            "/api/dingtalk/dingCallback",
            "/api/dingtalk/dingCallback/**",
            "/api/app/home/<USER>/list/free",
            "/api/ding/dataSource/workflow",
            "/api/ding/connector/**",
            "/api/weixin/callback/**",
            "/api/weixin/auth/**",
            "/api/common/asCountryCode/list",
            "/api/common/companyPasswordSetting/getLimitWordsByMobile",
            "/api/common/workflow/signature/authCode/verify",
            "/api/common/workflow/signature/submit",
            "/api/common/bigScreen/*",
            "/api/common/file/upload/single/*"
    };
}
