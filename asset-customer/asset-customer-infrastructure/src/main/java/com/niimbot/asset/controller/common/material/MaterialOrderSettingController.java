package com.niimbot.asset.controller.common.material;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.MaterialOrderSettingFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialOrderFieldDto;
import com.niimbot.material.MaterialOrderFieldListDto;
import com.niimbot.material.MaterialOrderTypeDto;
import com.niimbot.material.MaterialOrderTypeEnableWorkflowDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

;

/**
 * <AUTHOR>
 * @since 2021/7/7 15:49
 */
@Api(tags = "【耗材】单据配置")
@RequestMapping("api/common/material/orderSetting")
@ResultController
@Validated
public class MaterialOrderSettingController {

    private final MaterialOrderSettingFeignClient orderSettingFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;

    @Autowired
    public MaterialOrderSettingController(MaterialOrderSettingFeignClient orderSettingFeignClient,
                                          ActWorkflowFeignClient workflowFeignClient) {
        this.orderSettingFeignClient = orderSettingFeignClient;
        this.workflowFeignClient = workflowFeignClient;
    }

    @ApiOperation(value = "单据类型列表")
    @GetMapping(value = "/orderType/list")
    public List<MaterialOrderTypeDto> listOrderType() {
        return orderSettingFeignClient.listOrderType();
    }

    @ApiOperation(value = "单据类型是否开启审批流")
    @GetMapping(value = "/orderType/enableWorkflow/{orderType}")
    public Boolean enableWorkflow(@PathVariable("orderType") Integer orderType) {
        return orderSettingFeignClient.enableWorkflow(orderType);
    }

    @ApiOperation(value = "单据类型下拉字典")
    @GetMapping(value = "/orderType/dict")
    public List<Map<String, ?>> orderTypeDict() {
        List<MaterialOrderTypeDto> orderType = orderSettingFeignClient.listOrderType();
        return orderType.stream().map(item ->
                ImmutableMap.of("value", item.getType(), "label", StrUtil.replace(item.getName(), "单", ""))
        ).collect(Collectors.toList());
    }

    @ApiOperation(value = "通过单据类型查询字段列表")
    @GetMapping(value = "/orderField/list/{orderType}")
    @AutoConvert
    public MaterialOrderFieldListDto listOrderField(@PathVariable("orderType") Integer orderType) {
        List<MaterialOrderFieldDto> fields = orderSettingFeignClient.listOrderField(orderType);
        MaterialOrderFieldDto fieldDto = fields.stream().filter(dto -> dto.getUpdateTime() != null)
                .max(Comparator.comparingLong(dto ->
                        dto.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                .orElse(null);
        return new MaterialOrderFieldListDto()
                .setUpdateBy(fieldDto != null ? fieldDto.getUpdateBy() : null)
                .setUpdateTime(fieldDto != null ? fieldDto.getUpdateTime() : null)
                .setFields(fields);
    }

    @ApiOperation(value = "动态表单")
    @GetMapping(value = "/dynamicField/form/{orderType}")
    public List<MaterialOrderFieldDto> listDynamicForm(@PathVariable("orderType") Integer orderType) {
        return orderSettingFeignClient.listDynamicOrderField(orderType)
                .stream()
                .peek(field -> field.setRename(Optional.ofNullable(field.getRename()).orElse(field.getName())))
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "启用单据流程")
    @PutMapping("/enableOrderTypeWorkflow")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean enableOrderTypeWorkflow(@RequestBody MaterialOrderTypeEnableWorkflowDto dto) {
        MaterialOrderTypeDto type = orderSettingFeignClient.getOrderTypeById(dto.getId());
        if (type == null) {
            throw new BusinessException(MeansResultCode.ORDER_TYPE_NOT_EXISTS, String.valueOf(dto.getId()));
        }
        if (dto.getEnableWorkflow() && !workflowFeignClient.hasWorkflow(type.getActivitiKey())) {
//            throw new BusinessException(SystemResultCode.ORDER_TYPE_NO_EXISTS_WORKFLOW);
            return false;
        }
        MaterialOrderTypeDto orderType = BeanUtil.copyProperties(dto, MaterialOrderTypeDto.class);
        return orderSettingFeignClient.updateOrderType(orderType);
    }

    @ApiOperation(value = "批量更新单据字段")
    @PutMapping("/updateOrderField/batch")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean updateOrderFieldBatch(@RequestBody
                                         @NotEmpty(message = "单据字段列表不能为空")
                                                 List<@Valid MaterialOrderFieldDto> dtos) {
        return orderSettingFeignClient.updateOrderFieldBatch(dtos);
    }

    @ApiOperation(value = "更新单据字段")
    @PutMapping("/updateOrderField")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean updateOrderField(@RequestBody MaterialOrderFieldDto dto) {
        return orderSettingFeignClient.updateOrderField(dto);
    }

}
