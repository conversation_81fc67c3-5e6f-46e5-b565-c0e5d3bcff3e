package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.ExcelExportDto;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.AuditLogFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogSearch;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "操作日志")
@ResultController
@RequestMapping("api/common/auditLog")
@RequiredArgsConstructor
public class AuditLogController {

    private final AuditLogFeignClient auditLogFeignClient;

    @ApiOperation("搜索系统操作日志")
    @PostMapping("/search")
    public PageUtils<AuditLogDto> search(@RequestBody AuditLogSearch search) {
        return auditLogFeignClient.search(search);
    }

    @ApiOperation("导出系统操作日志")
    @PostMapping("/export")
    public void export(@RequestBody AuditLogSearch search, HttpServletResponse response) {
        PageUtils<AuditLogDto> page = auditLogFeignClient.search(search);
        try {
            // 查询head
            LinkedHashMap<String, String> headerData = ExcelUtils.buildExcelHead(AuditLogDto.class);
            // 查询数据
            List<AuditLogDto> logs = page.getList();
            // excel 为空就生成一个空文件
            String fileName = "系统操作日志-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (search.getPageNum() > 1) {
                fileName += "-" + search.getPageNum();
            }
            ExcelUtils.export(response, new ExcelExportDto(headerData, logs), fileName + ".xlsx");
        } catch (BusinessException business) {
            throw business;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }
    }

}
