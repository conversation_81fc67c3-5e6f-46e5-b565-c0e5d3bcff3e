package com.niimbot.asset.service.feign;

import com.niimbot.system.IndustryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 公共feign客户端
 *
 * <AUTHOR>
 * @Date 2020/11/13
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface IndustryFeignClient {
    /**
     * 获取行业列表
     *
     * @param dto 查询参数
     * @return 行业列表
     */
    @GetMapping(value = "server/system/industry/list")
    List<IndustryDto> listIndustry();
}
