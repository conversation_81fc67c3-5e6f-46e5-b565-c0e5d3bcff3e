package com.niimbot.asset.controller.app.means;

import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.service.feign.CusMenuFeignClient;
import com.niimbot.asset.service.feign.MaterialOperationFeignClient;
import com.niimbot.asset.service.feign.MaterialOrderSettingFeignClient;
import com.niimbot.asset.service.feign.OrderSettingFeignClient;
import com.niimbot.asset.service.feign.PurchaseOrderSettingFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.material.MaterialOperationDto;
import com.niimbot.material.MaterialOrderTypeDto;
import com.niimbot.means.OrderTypeDto;
import com.niimbot.purchase.PurchaseOrderTypeDto;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.CusMenuDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:26
 */
@Api(tags = "单据配置管理接口")
@RequestMapping("api/app/orderSetting")
@ResultController
@Validated
public class OrderSettingAppController {

    private final Map<Integer, String> orderTypeMenu = new HashMap<>();

    {
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_RECEIVE, "recv");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_RETURN, "return");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_BORROW, "borrow");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_BACK, "revert");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_ALLOCATE, "allot");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_REPAIR_REPORT, "report_repair");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_REPAIR, "repair");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_DISPOSE, "handle");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_CHANGE, "change");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MAINTAIN, "maintenance");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_STORE, "asset-storage");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MATERIAL_RK, "material_rk");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MATERIAL_CK, "material_ck");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MATERIAL_LY, "material_ly");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MATERIAL_TZ, "material_tz");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MATERIAL_DB, "material_db");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MATERIAL_BS, "material_bs");
        orderTypeMenu.put(AssetConstant.ORDER_TYPE_MATERIAL_TK, "material_tk");
        orderTypeMenu.put(10, "purchase-apply");
        orderTypeMenu.put(12, "purchase-order");
    }

    private final CusMenuFeignClient menuFeignClient;
    private final OrderSettingFeignClient orderSettingFeignClient;
    private final MaterialOrderSettingFeignClient materialOrderSettingFeignClient;
    private final PurchaseOrderSettingFeignClient purchaseOrderSettingFeignClient;
    @Autowired
    private MaterialOperationFeignClient materialOperationFeignClient;
    @Autowired
    private AbstractFileUtils fileUtils;

    @Autowired
    public OrderSettingAppController(CusMenuFeignClient menuFeignClient,
                                     OrderSettingFeignClient orderSettingFeignClient,
                                     MaterialOrderSettingFeignClient materialOrderSettingFeignClient,
                                     PurchaseOrderSettingFeignClient purchaseOrderSettingFeignClient) {
        this.menuFeignClient = menuFeignClient;
        this.orderSettingFeignClient = orderSettingFeignClient;
        this.materialOrderSettingFeignClient = materialOrderSettingFeignClient;
        this.purchaseOrderSettingFeignClient = purchaseOrderSettingFeignClient;
    }

    @ApiOperation(value = "单据类型列表")
    @GetMapping(value = "/orderType/list")
    public List<OrderTypeDto> listDynamicOrderType() {
        AppCusMenuDto appCusMenuDto = menuFeignClient.userMenuAppList();
        List<CusMenuDto> menus = appCusMenuDto.getMenus();
        List<String> menuCodes = menus.stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        return orderSettingFeignClient.appListOrderTypeShow().stream()
                .filter(f -> {
                    String menu = orderTypeMenu.get(f.getType());
                    return menuCodes.contains(menu);
                }).collect(Collectors.toList());
    }

    @ApiOperation(value = "耗材单据类型列表")
    @GetMapping(value = "/materialOrderType/list")
    public List<MaterialOrderTypeDto> listDynamicMaterialOrderType() {
        AppCusMenuDto appCusMenuDto = menuFeignClient.userMenuAppList();
        List<CusMenuDto> menus = appCusMenuDto.getMenus();
        List<String> menuCodes = menus.stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());

        List<MaterialOperationDto> materialOperationList = materialOperationFeignClient.allType();
        Map<Integer, MaterialOperationDto> materialOperationDtoMap = materialOperationList
                .stream()
                .collect(Collectors.toMap(MaterialOperationDto::getOrderType, value -> value, (v1, v2) -> v1));
        return materialOrderSettingFeignClient.appListOrderTypeShow().stream()
                .filter(f -> {
                    String menu = orderTypeMenu.get(f.getType());
                    return menuCodes.contains(menu);
                }).map(item -> {
                    if (Objects.nonNull(materialOperationDtoMap.get(item.getType()))) {
                        String icon = materialOperationDtoMap.get(item.getType()).getIcon();
                        item.setIcon(fileUtils.convertToDownloadUrl(icon));
                    }
                    return item;
                }).collect(Collectors.toList());
    }

    @ApiOperation(value = "耗材单据类型列表")
    @GetMapping(value = "/purchaseOrderType/list")
    public List<PurchaseOrderTypeDto> listDynamicPurchaseOrderType() {
        AppCusMenuDto appCusMenuDto = menuFeignClient.userMenuAppList();
        List<CusMenuDto> menus = appCusMenuDto.getMenus();
        List<String> menuCodes = menus.stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        return purchaseOrderSettingFeignClient.appListOrderTypeShow().stream()
                .filter(f -> {
                    String menu = orderTypeMenu.get(f.getType());
                    return menuCodes.contains(menu);
                }).collect(Collectors.toList());
    }
}
