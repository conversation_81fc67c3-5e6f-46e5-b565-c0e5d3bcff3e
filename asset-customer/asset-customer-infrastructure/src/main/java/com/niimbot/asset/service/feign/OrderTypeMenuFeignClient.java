package com.niimbot.asset.service.feign;

import com.niimbot.system.CusMenuDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:22
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface OrderTypeMenuFeignClient {

    /**
     * 根据单据类型获取菜单
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "server/means/orderTypeMenu/{orderType}")
    CusMenuDto getMenuById(@PathVariable("orderType") Short orderType);
}
