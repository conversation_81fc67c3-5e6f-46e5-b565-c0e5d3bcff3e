package com.niimbot.asset.controller.common.inventory;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.service.feign.InventoryApproveFeignClient;
import com.niimbot.inventory.InventoryApproveDto;
import com.niimbot.jf.core.component.annotation.ResultController;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * 盘点审批
 *
 * <AUTHOR>
 * @date 2021/4/14 15:19
 */
@Api(tags = "盘点审批")
@ResultController
@RequestMapping("api/common/inventory/approve")
@RequiredArgsConstructor
public class InventoryApproveController {
    private final InventoryApproveFeignClient inventoryApproveFeignClient;

    @ApiOperation(value = "审批记录列表")
    @GetMapping("/list/{inventoryId}/{taskId}")
    @AutoConvert
    public List<InventoryApproveDto> list(@PathVariable("inventoryId") Long inventoryId,
                                          @PathVariable("taskId") Long taskId) {
        return inventoryApproveFeignClient.list(inventoryId, taskId);
    }
}
