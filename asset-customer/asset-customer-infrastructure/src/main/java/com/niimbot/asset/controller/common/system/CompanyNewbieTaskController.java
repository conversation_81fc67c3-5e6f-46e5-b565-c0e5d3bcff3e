package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.CompanyNewbieTaskFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.CompanyNewbieTaskDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "【企业新手任务】")
@ResultController
@RequestMapping("/api/common/company/newbie/task")
@RequiredArgsConstructor
public class CompanyNewbieTaskController {

    private final CompanyNewbieTaskFeignClient companyNewbieTaskFeignClient;

    @ApiOperation("企业新手任务列表")
    @AutoConvert
    @GetMapping("/list")
    public List<CompanyNewbieTaskDto> tasks() {
        return companyNewbieTaskFeignClient.tasks(LoginUserThreadLocal.getCompanyId());
    }

    @ApiOperation("修改新手任务为已完成")
    @PutMapping("/completed/{id}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean completed(@PathVariable("id") Long id) {
        return companyNewbieTaskFeignClient.completed(id);
    }

    @ApiOperation("修改新手任务为已忽略")
    @PutMapping("/ignored/{id}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean ignored(@PathVariable("id") Long id) {
        return companyNewbieTaskFeignClient.ignored(id);
    }

    @ApiOperation("当前企业新手任务进度")
    @GetMapping("/progressRate")
    public Map<String, Integer> progressRate() {
        return companyNewbieTaskFeignClient.progressRate(LoginUserThreadLocal.getCompanyId());
    }

    @ApiOperation("当前企业新手任务是否已经全部处理")
    @GetMapping("/isAllProcessed")
    public Boolean isAllProcessed() {
        return companyNewbieTaskFeignClient.isAllProcessed(LoginUserThreadLocal.getCompanyId());
    }

}
