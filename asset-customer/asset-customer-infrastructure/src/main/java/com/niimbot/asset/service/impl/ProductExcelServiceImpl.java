package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.ProductExcelService;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.ProductFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.asset.utils.TimeUtil;
import com.niimbot.easydesign.form.dto.clientobject.DateTimeCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.ReImportDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;
import com.niimbot.system.ImportTaskExtInfoDto;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/9/1 9:37
 */
@Slf4j
@Service
public class ProductExcelServiceImpl implements ProductExcelService {

    private static final int MAX_BATCH = 5000;

    private final RedisService redisService;
    private final ImportService importService;
    private final ImportTaskFeignClient importTaskFeignClient;
    private final StandardFeignClient standardFeignClient;
    private final ProductFeignClient productFeignClient;

    // product-产品属性，standard-自定义属性
    private static ThreadLocal<List<FormFieldCO>> formFieldMap = new TransmittableThreadLocal<>();
    private static ThreadLocal<ImportInfo> globalCache = new TransmittableThreadLocal<>();

    @Autowired
    public ProductExcelServiceImpl(RedisService redisService,
                                   ImportService importService,
                                   ImportTaskFeignClient importTaskFeignClient,
                                   StandardFeignClient standardFeignClient,
                                   ProductFeignClient productFeignClient) {
        this.redisService = redisService;
        this.importService = importService;
        this.importTaskFeignClient = importTaskFeignClient;
        this.standardFeignClient = standardFeignClient;
        this.productFeignClient = productFeignClient;
    }

    @Override
    public ExcelWriter buildExcelWriter(Long standardId) {
        // 获取 writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 获取 sheet
        Sheet sheet = writer.getSheet();
        // 获取 styleSet
        StyleSet styleSet = writer.getStyleSet();
        // 设置边框
        styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
        styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);
        Workbook workbook = sheet.getWorkbook();
        DataFormat format = workbook.createDataFormat();
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(format.getFormat("@"));

        // 设置表头的cellStyle
        CellStyle redHeadStyle = writer.createCellStyle();
        redHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        redHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        redHeadStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        redHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        redHeadStyle.setBorderBottom(BorderStyle.THIN);
        redHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        redHeadStyle.setBorderTop(BorderStyle.THIN);
        redHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderLeft(BorderStyle.THIN);
        redHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderRight(BorderStyle.THIN);
        redHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font red = writer.createFont();
        red.setFontHeightInPoints((short) 13);
        red.setColor(IndexedColors.RED.getIndex());
        redHeadStyle.setFont(red);

        CellStyle blackHeadStyle = writer.createCellStyle();
        blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        blackHeadStyle.setBorderBottom(BorderStyle.THIN);
        blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setBorderTop(BorderStyle.THIN);
        blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderLeft(BorderStyle.THIN);
        blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderRight(BorderStyle.THIN);
        blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font black = writer.createFont();
        black.setFontHeightInPoints((short) 13);
        black.setColor(IndexedColors.BLACK.getIndex());
        blackHeadStyle.setFont(black);

        // 写入文件注意事项
        Row attentionRow = writer.getOrCreateRow(0);
        attentionRow.setHeight((short) 2000);
        Cell attentionCell = attentionRow.createCell(0);
        CellStyle attention = writer.createCellStyle();
        attention.setVerticalAlignment(VerticalAlignment.CENTER);
        attention.setWrapText(true);
        attention.setFont(black);
        attentionCell.setCellStyle(attention);
        String text = "注意事项:\r\n" +
                "1、模板中的表头名称不可更改，表头行不能删除；\r\n" +
                "2、产品的编码禁止重复。请确认系统中不存在当前的编码；\r\n" +
                "3、单次最多可导入5000条数据，文件不可超过2MB。";
        XSSFRichTextString xssfRichTextString = new XSSFRichTextString(text);
        attentionCell.setCellValue(xssfRichTextString);
        writer.merge(26);

        // 查询录入标准数据项
        List<FormFieldCO> formFields = getStandardAttr(standardId);
        Row headRow = writer.getOrCreateRow(1);
        int realCol = 0;
        for (FormFieldCO attr : formFields) {
            // ============================== 设置表头 ===================================
            // 写入表头
            Cell cell = headRow.createCell(realCol);
            cell.setCellStyle(attr.requiredProps() ? redHeadStyle : blackHeadStyle);
            cell.setCellValue(attr.getFieldName());

            if ("code".equals(attr.getFieldCode())) {
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6));
                comment.setString(new XSSFRichTextString("产品编码禁止重复。请确认系统中不存在当前编码"));
                cell.setCellComment(comment);
            }
            // 调整每一列宽度
            sheet.autoSizeColumn((short) realCol);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(realCol, sheet.getColumnWidth(realCol) * 17 / 10);
            sheet.setDefaultColumnStyle(realCol, style);

            if (FormFieldCO.SELECT_DROPDOWN.equals(attr.getFieldType())) {
                JSONArray values = attr.getFieldProps().getJSONArray("values");
                String[] selected = values.toArray(new String[]{});
                writer.addSelect(new CellRangeAddressList(2, 5000, realCol, realCol), selected);
            }
            realCol += 1;
        }
        return writer;
    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize,
                                 Long companyId, Long standardId) {
        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setFileName(fileName);
        importInfo.setFileSize(fileSize);
        importInfo.setCompanyId(companyId);
        importInfo.setStandardId(standardId);

        List<FormFieldCO> formFields = getStandardAttr(standardId);
        Map<String, List<FormFieldCO>> productAttrMap = formFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 解析的模板导入
        ExcelReader reader = ExcelUtil.getReader(stream);
        List<List<Object>> read = reader.read(1);
        if (CollUtil.isEmpty(read)) {
            throw new BusinessException(MeansResultCode.PRODUCT_IMPORT_ERROR, "表格未检测到数据");
        }
        // =============================  读取Excel表头并校验 ================================
        List<Object> header = ExcelUtils.clearEmpty(read.get(0));
        int noNullHeaderSize = Convert.toInt(header.stream().filter(it -> StrUtil.isNotBlank(Convert.toStr(it))).count());
        // 表头字段映射
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        // 匹配动态属性和表头数据是否一致，防止动态属性被修改
        if (formFields.size() != noNullHeaderSize) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
        } else {
            for (int i = 0; i < header.size(); i++) {
                String headName = Convert.toStr(header.get(i), "").trim();
                if (StrUtil.isBlank(Convert.toStr(headName))) {
                    continue;
                }
                if (!productAttrMap.containsKey(headName)) {
                    throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
                }
                List<FormFieldCO> formFieldCOList = productAttrMap.get(headName);
                if (CollUtil.isNotEmpty(formFieldCOList)) {
                    FormFieldCO formFieldCO = formFieldCOList.get(0);
                    headerMapping.put(i, formFieldCO);
                    formFieldCOList.remove(0);
                    productAttrMap.put(headName, formFieldCOList);
                }
            }
        }
        importInfo.setHeaderMapping(headerMapping);
        importInfo.setRead(read);
        this.importExcel(importInfo, true);
    }

    private void importExcel(ImportInfo importInfo, boolean async) {
        // 判断是否超过最大上传条数，一次限制5000
        if (importInfo.getRead().size() > MAX_BATCH + 1) {
            throw new BusinessException(MaterialResultCode.IMPORT_MAX_LIMIT);
        }

        // 删除历史导入信息
        if (importInfo.getTaskId() != null) {
            productFeignClient.importErrorDeleteAll(importInfo.getTaskId());
        }
        globalCache.set(importInfo);
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto()
                        .setFileName(importInfo.getFileName())
                        .setImportType(DictConstant.IMPORT_TYPE_PRODUCT)
                        .setFileSize(importInfo.getFileSize())
                        .setCount(importInfo.getRead().size() - 1);
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(importCache);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto()
                    .setFileName(importInfo.getFileName())
                    .setImportType(DictConstant.IMPORT_TYPE_PRODUCT)
                    .setFileSize(importInfo.getFileSize())
                    .setCount(importInfo.getRead().size() - 1);
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(importCache);
        }
    }

    private void startImport(ImportDto importCache) {
        ImportInfo importInfo = globalCache.get();
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (importInfo.getTaskId() != null) {
            importTaskDto = importTaskFeignClient.queryById(importInfo.getTaskId());
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.PRODUCT, importInfo.getCompanyId()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                ImportTaskExtInfoDto extInfoDto = new ImportTaskExtInfoDto();
                extInfoDto.setStandardId(importInfo.getStandardId());
                importTaskDto.setExtInfo(extInfoDto);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
            }

            List<AssetImportDto> tableData = new ArrayList<>();
            // 构建资产JSON数据
            for (int idx = 1; idx < importInfo.getRead().size(); idx++) {
                // 行数据
                List<Object> dataRow = importInfo.getRead().get(idx);
                // 写入固定属性数据
                JSONObject materialData = new JSONObject();
                IntStream.range(0, dataRow.size()).forEach(nameIdx -> {
                    FormFieldCO materialConfig = importInfo.getHeaderMapping().get(nameIdx);
                    if (materialConfig != null) {
                        Object cell = dataRow.get(nameIdx);
                        materialData.put(materialConfig.getFieldCode(), cell);
                    }
                });
                // 创建属性对象
                AssetImportDto importDto = new AssetImportDto();
                importDto.setTaskId(importTaskDto.getId())
                        .setStandardId(importInfo.getStandardId())
                        .setErrorNum(0)
                        .setAssetData(materialData);
                tableData.add(importDto);
            }
            // 执行
            importService.sendProduct(importInfo.getCompanyId());
            this.executeTableData(importTaskDto.getId(), tableData, importInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.PRODUCT, importInfo.getCompanyId()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.PRODUCT, importInfo.getCompanyId()), "finish", true);
            importService.sendProduct(importInfo.getCompanyId());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.PRODUCT, importInfo.getCompanyId());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
            }
            this.clearThreadLocal();
        }
    }

    private void executeTableData(Long taskId, List<AssetImportDto> tableData, ImportInfo importInfo) {
        // 加载缓存，有序集合
        LinkedHashMap<String, FormFieldCO> codeToMaterialAttrMap = new LinkedHashMap<>();
        for (Map.Entry<Integer, FormFieldCO> entry : importInfo.getHeaderMapping().entrySet()) {
            FormFieldCO fieldCO = entry.getValue();
            codeToMaterialAttrMap.putIfAbsent(fieldCO.getFieldCode(), fieldCO);
        }
        // 写入表头数据
        this.saveLuckySheetHead(taskId, importInfo.getHeaderMapping());
        AtomicInteger successNum = new AtomicInteger(0);
        // 循环处理行数据
        IntStream.range(0, tableData.size()).forEach(idx -> {
            // 获取导入数据
            AssetImportDto importDto = tableData.get(idx);
            // 数据
            JSONObject materialData = importDto.getAssetData();
            List<AssetImportDto.FieldData> fieldDataList = new ArrayList<>();
            codeToMaterialAttrMap.forEach((attrCode, fieldCO) -> {
                AssetImportDto.FieldData fieldData = new AssetImportDto.FieldData();
                Object attrVal = materialData.getOrDefault(attrCode, null);
                fieldData.setSource(attrVal);
                if (attrVal instanceof String) {
                    fieldData.setTarget(StrUtil.trim(((String) attrVal)));
                } else {
                    fieldData.setTarget(attrVal);
                }
                fieldData.setFieldName(fieldCO.getFieldName());
                fieldData.setFieldCode(fieldCO.getFieldCode());

                // 处理日期类型
                if (FormFieldCO.DATETIME.equals(fieldCO.getFieldType())) {
                    dateConvert(fieldData, attrVal, fieldCO.getFieldProps().getString("dateFormatType"));
                }
                // 处理多选
                if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(fieldCO.getFieldType())) {
                    if (fieldData.getTarget() != null) {
                        String val = Convert.toStr(attrVal);
                        val = val.replace("，", ",");
                        String[] split = val.split(",");
                        fieldData.setTarget(new ArrayList<>(Arrays.asList(split)));
                    } else {
                        fieldData.setTarget(new ArrayList<>());
                    }
                }
                fieldDataList.add(fieldData);
            });
            importDto.setFieldDataList(fieldDataList);
            importDto.setRowNum(idx + 1 - successNum.get());
            importDto.setFormFieldMap(MapUtil.of("product", formFieldMap.get()));
            Boolean success = productFeignClient.saveSheetData(importDto);
            if (BooleanUtil.isTrue(success)) {
                successNum.getAndIncrement();
            }
            importService.sendProduct(importInfo.getCompanyId());
        });
    }

    private void saveLuckySheetHead(Long taskId, Map<Integer, FormFieldCO> headerMapping) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new LinkedList<>();

        List<Integer> headerList = new ArrayList<>(headerMapping.keySet());
        headerList = headerList.stream().sorted().collect(Collectors.toList());

        AtomicInteger cellIndex = new AtomicInteger(0);
        for (Integer index : headerList) {
            FormFieldCO config = headerMapping.get(index);
            // 记录当前属性
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(config.getFieldName());
            if (config.requiredProps()) {
                modelV.setFc("#ff0000");
            }
            headModelList.add(luckySheetModel);
        }
        importErrorDto.setHeadModelList(headModelList);
        this.productFeignClient.saveSheetHead(importErrorDto);
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        return productFeignClient.importError(taskId);
    }

    @Override
    public ReImportDto reImport(Long taskId) {
        ReImportDto reImportDto = new ReImportDto();
        List<List<LuckySheetModel>> lists = productFeignClient.importError(taskId);
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (CollUtil.isNotEmpty(lists) && ObjectUtil.isNotNull(importTaskDto)) {
            reImportDto.setExcel(lists);
        }
        return reImportDto;
    }

    @Override
    public void importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (importTaskDto == null) {
            throw new BusinessException(MeansResultCode.PRODUCT_IMPORT_ERROR, "导入任务" + taskId + "不存在");
        }
        if (CollUtil.isEmpty(sheetModels)) {
            throw new BusinessException(MeansResultCode.PRODUCT_IMPORT_ERROR, "表格未检测到数据");
        }

        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setTaskId(taskId);
        importInfo.setFileName("产品在线编辑保存");
        importInfo.setFileSize(0L);
        importInfo.setCompanyId(companyId);
        importInfo.setRead(sheetModels);

        ImportTaskExtInfoDto extInfo = importTaskDto.getExtInfo();
        Long standardId = null;
        if (extInfo != null && extInfo.getStandardId() != null) {
            standardId = extInfo.getStandardId();
        }
        importInfo.setStandardId(standardId);

        List<FormFieldCO> formFields = getStandardAttr(standardId);
        Map<String, List<FormFieldCO>> materialAttrMap = formFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 必填项code
        Set<String> requiredCodes = formFields.stream().filter(attr -> BooleanUtil.isTrue(attr.requiredProps()))
                .map(FormFieldCO::getFieldCode).collect(Collectors.toSet());

        // =============================  读取Excel表头并校验 ================================
        List<Object> header = sheetModels.get(0);
        // 表头字段映射
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        // 匹配动态属性和表头数据是否一致，防止动态属性被修改
        for (int i = 0; i < header.size(); i++) {
            String headName = Convert.toStr(header.get(i), "").trim();
            if (StrUtil.isBlank(Convert.toStr(headName))) {
                continue;
            }
            if (!materialAttrMap.containsKey(headName)) {
                throw new BusinessException(MeansResultCode.ASSET_IMPORT_TPL_CHANGE);
            }
            List<FormFieldCO> bizFormAssetConfigs = materialAttrMap.get(headName);
            if (CollUtil.isNotEmpty(bizFormAssetConfigs)) {
                FormFieldCO bizFormAssetConfig = bizFormAssetConfigs.get(0);
                headerMapping.put(i, bizFormAssetConfig);
                requiredCodes.remove(bizFormAssetConfig.getFieldCode());
                bizFormAssetConfigs.remove(0);
                materialAttrMap.put(headName, bizFormAssetConfigs);
            }
        }

        if (requiredCodes.size() > 0) {
            throw new BusinessException(MeansResultCode.PRODUCT_IMPORT_ERROR, "存在必填项字段未设置");
        }

        importInfo.setHeaderMapping(headerMapping);
        // 校验表头数据
        this.importExcel(importInfo, false);
    }

    private List<FormFieldCO> getStandardAttr(Long standardId) {
        // 查询录入标准数据项
        FormVO form = standardFeignClient.form(standardId, true);
        List<FormFieldCO> formFields = form.getFormFields();
        formFields = formFields.stream()
                .filter(attr -> !attr.isHidden())
                .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                        .contains(attr.getFieldType())).collect(Collectors.toList());
        formFieldMap.set(formFields);
        return formFields;
    }

    private void dateConvert(AssetImportDto.FieldData fieldData, Object value, String dateFormatType) {
        String dateStr = StrUtil.trim(Convert.toStr(value));
        if (StrUtil.isEmpty(dateStr)) {
            return;
        }
        /*String regex = DateTimeCO.regexMap.get(dateFormatType);*/
        // 导入的日期格式兼容，自动对齐配置的日期格式
        if (value instanceof Date) {
            try {
                Date date = Convert.toDate(value);
                SimpleDateFormat sdf = new SimpleDateFormat(dateFormatType);
                dateStr = sdf.format(date);
            } catch (Exception e) {
                fieldData.getErrMsg().add("值不是日期类型");
                return;
            }
        }

        /*if (regex == null || !dateStr.matches(regex)) {
            fieldData.getErrMsg().add("日期格式仅支持" + dateFormatType);
            return;
        }*/

        String dateStrFmt = TimeUtil.convertDate(dateStr);
        switch (dateFormatType) {
            case DateTimeCO.yyyyMMdd:
                try {
                    LocalDate yyyyMMddParse = LocalDate.parse(dateStrFmt.endsWith("-") ? dateStrFmt.substring(0, dateStrFmt.length() - 1) : dateStrFmt, DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyMMddLong = yyyyMMddParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyMMddLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM-dd，yyyy/MM/dd，yyyy年MM月dd日");
                }
                return;
            case DateTimeCO.yyyyMM:
                try {
                    LocalDate yyyyMMParse = LocalDate.parse(dateStrFmt + (dateStrFmt.endsWith("-") ? "01" : "-01"), DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyMMLong = yyyyMMParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyMMLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM，yyyy/MM，yyyy年MM月");
                }
                return;
            case DateTimeCO.yyyy:
                try {
                    LocalDate yyyyParse = LocalDate.parse(dateStrFmt + (dateStrFmt.endsWith("-") ? "01-01" : "-01-01"), DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMdd));
                    Long yyyyLong = yyyyParse.atTime(0, 0, 0).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(yyyyLong);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy，yyyy年");
                }
                return;
            case DateTimeCO.yyyyMMddHHmmss:
                try {
                    LocalDateTime parse = LocalDateTime.parse(dateStrFmt, DateTimeFormatter.ofPattern(DateTimeCO.yyyyMMddHHmmss));
                    Long time = parse.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                    fieldData.setSource(dateStr).setTarget(time);
                } catch (Exception e) {
                    log.warn("资产导入日期格式错误，{}", e.getMessage(), e);
                    fieldData.getErrMsg().add("不支持的日期格式，例yyyy-MM-dd HH:mm:ss，yyyy/MM/dd HH:mm:ss，yyyy年MM月dd日 HH时mm分ss秒");
                }
                return;
            default:
                fieldData.getErrMsg().add("不支持的日期格式，例" + dateFormatType);
        }
    }

    @Data
    @Accessors(chain = true)
    private static class ImportInfo {
        private Long taskId;
        private String fileName;
        private Long fileSize;
        private Long companyId;
        private Long standardId;
        private List<List<Object>> read = new ArrayList<>();
        private Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
    }

    /**
     * 清理本地缓存
     */
    private void clearThreadLocal() {
        globalCache.remove();
        formFieldMap.remove();
    }
}
