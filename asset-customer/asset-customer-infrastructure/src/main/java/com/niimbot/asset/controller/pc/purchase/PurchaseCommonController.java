package com.niimbot.asset.controller.pc.purchase;

import com.niimbot.asset.service.feign.PurchaseCommonFeignClient;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2022/10/18 16:42
 */
@Api(tags = "采购单据公共接口")
@ResultController
@RequestMapping("api/pc/purchase/common")
@RequiredArgsConstructor
public class PurchaseCommonController {
    private final PurchaseCommonFeignClient purchaseCommonFeignClient;

    @ApiOperation(value = "采购单据表单查询")
    @GetMapping(value = "/form/{orderType}")
    public FormVO getForm(@PathVariable("orderType") Integer orderType) {
        return purchaseCommonFeignClient.getForm(orderType);
    }
}
