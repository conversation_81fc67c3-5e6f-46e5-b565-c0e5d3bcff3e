package com.niimbot.asset.service.feign;

import com.niimbot.activiti.*;
import com.niimbot.asset.framework.utils.PageUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审批角色feign
 *
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ActApproveRoleFeignClient {

    /**
     * 新增审批角色
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("server/activiti/approveRole")
    Boolean create(ActApproveRoleDto dto);

    /**
     * 修改审批角色
     *
     * @param dto dto
     * @return 结果
     */
    @PutMapping("server/activiti/approveRole")
    Boolean update(ActApproveRoleDto dto);

    /**
     * 删除审批角色
     *
     * @param ids 审批角色ids
     * @return 结果
     */
    @DeleteMapping("server/activiti/approveRole")
    Boolean remove(List<Long> ids);

    /**
     * 审批角色分页
     *
     * @return 分页数据
     */
    @GetMapping(value = "server/activiti/approveRole/page")
    PageUtils<ActApproveRoleDto> page(@SpringQueryMap ActApproveRoleQueryDto dto);

    /**
     * 审批角色列表
     *
     * @param dto dto
     * @return 列表数据
     */
    @GetMapping(value = "server/activiti/approveRole/list")
    List<ActApproveRoleDto> list(@SpringQueryMap ActApproveRoleQueryDto dto);

    /**
     * 新增审批角色成员
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("server/activiti/approveRole/member")
    Boolean createMembers(ActApproveRoleMemberDto dto);

    /**
     * 删除审批角色成员
     *
     * @param dto dto
     * @return 结果
     */
    @DeleteMapping("server/activiti/approveRole/member")
    Boolean removeMembers(ActApproveRoleMemberDto dto);

    /**
     * 审批角色成员分页
     *
     * @param dto dto
     * @return 分页数据
     */
    @GetMapping(value = "server/activiti/approveRole/member/page")
    PageUtils<ActApproveRoleMemberResultDto> pageMember(@SpringQueryMap ActApproveRoleMemberQueryDto dto);

    /**
     * 审批角色成员列表
     *
     * @param dto dto
     * @return 列表数据
     */
    @GetMapping(value = "server/activiti/approveRole/member/list")
    List<ActApproveRoleMemberResultDto> listMember(@SpringQueryMap ActApproveRoleMemberQueryDto dto);

    /**
     * 根据审批角色ids查询所有成员
     *
     * @param roleIds 审批角色ids
     * @return 成员list
     */
    @PostMapping(value = "server/activiti/approveRole/member/listByRoleIds")
    List<ActApproveRoleMemberResultDto> listByRoleIds(List<Long> roleIds);

    @PostMapping("server/activiti/approveRole/member/orgScope")
    Boolean memberOrgScope(@RequestBody ApproveMemberOrgScopeSetting setting);
}
