package com.niimbot.asset.controller.pc.report;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.MaterialReportQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.report.ReportEnum;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionGeneralDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/29 下午2:50
 */
@Slf4j
@Validated
@Api(tags = "耗材报表查询字段管理")
@ResultController
@RequestMapping("api/pc/queryField/report/material/")
@RequiredArgsConstructor
public class MaterialReportQueryFieldController {

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    private final MaterialReportQueryFieldService materialReportQueryFieldService;

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all/{type}")
    public List<QueryConditionDto> reportAllQueryField(@PathVariable("type") String type) {
        return materialReportQueryFieldService.allQueryField(type);
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view/{type}")
    public List<QueryConditionDto> reportQueryView(@PathVariable("type") String type) {
        return materialReportQueryFieldService.queryView(type);
    }

    @ApiOperation(value = "筛选项配置-保存")
    @PostMapping("/query/field/{type}")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@PathVariable("type") String type,
                              @RequestBody QueryConditionGeneralDto condition) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(), type));
    }

    @ApiOperation(value = "筛选项配置-查询")
    @GetMapping("/query/field/{type}")
    public QueryConditionGeneralDto getQueryField(@PathVariable("type") String type) {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(type);
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(QueryConditionGeneralDto.class);
    }
}
