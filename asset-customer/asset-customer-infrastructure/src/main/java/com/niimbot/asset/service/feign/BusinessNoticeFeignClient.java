package com.niimbot.asset.service.feign;

import com.niimbot.system.BusinessNoticeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/3/17 下午4:52
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface BusinessNoticeFeignClient {

    /**
     * 查询业务公告
     * @param companyId
     * @return
     */
    @GetMapping(value = "server/system/businessNotice/detail/{companyId}")
    BusinessNoticeDto queryDetail(@PathVariable("companyId") Long companyId);

    /**
     * 删除业务公告
     * @param noticeId
     * @return
     */
    @DeleteMapping(value = "server/system/businessNotice/{noticeId}")
    Boolean removeNotice(@PathVariable("noticeId") Long noticeId);

    /**
     * 保存业务公告
     * @param noticeDto
     * @return
     */
    @PostMapping(value = "server/system/businessNotice/save")
    Boolean saveOrUpdate(@RequestBody BusinessNoticeDto noticeDto);
}
