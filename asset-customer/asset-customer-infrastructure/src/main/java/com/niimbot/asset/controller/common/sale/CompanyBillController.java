package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.CompanyBillFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.CompanyBillDetailDto;
import com.niimbot.sale.CompanyBillDto;
import com.niimbot.sale.CompanyBillQueryDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/12/31 10:37
 */
@Api(tags = "【服务中心】费用账单")
@ResultController
@RequestMapping("api/common/bill")
@RequiredArgsConstructor
public class CompanyBillController {

    private final CompanyBillFeignClient companyBillFeignClient;

    @ApiOperation(value = "总账单")
    @GetMapping("/page/all")
    public PageUtils<CompanyBillDto> pageAll(CompanyBillQueryDto query) {
        return this.companyBillFeignClient.pageAll(query);
    }

    @ApiOperation(value = "收入账单")
    @GetMapping("/page/income")
    public PageUtils<CompanyBillDetailDto> pageIncome(CompanyBillQueryDto query) {
        return this.companyBillFeignClient.pageIncome(query);
    }

    @ApiOperation(value = "支出账单")
    @GetMapping("/page/expenses")
    public PageUtils<CompanyBillDetailDto> pageExpenses(CompanyBillQueryDto query) {
        return this.companyBillFeignClient.pageExpenses(query);
    }

}
