package com.niimbot.asset.service.feign.equipment;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.CreateEntSntProject;
import com.niimbot.equipment.EntSntProject;
import com.niimbot.equipment.EntSntProjectImportDto;
import com.niimbot.equipment.SearchEntSntProject;
import com.niimbot.luckysheet.LuckyMultiSheetModel;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentSiteInspectProjectFeignClient {

    @PostMapping("/server/equipment/site/inspect/project/create")
    AuditableOperateResult create(@RequestBody CreateEntSntProject create);

    @PostMapping("/server/equipment/site/inspect/project/remove")
    List<AuditableOperateResult> remove(@RequestBody List<Long> projectIds);

    @PostMapping("/server/equipment/site/inspect/project/search")
    PageUtils<EntSntProject> search(@RequestBody SearchEntSntProject search);

    @GetMapping("/server/equipment/site/inspect/project/recommendCode")
    String recommendCode();

    @DeleteMapping(value = "server/equipment/site/inspect/project/importErrorAll/{taskId}")
    Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId);

    @PostMapping(value = "server/equipment/site/inspect/project/saveSheetHead")
    void saveSheetHead(HeadImportErrorDto importErrorDto);

    @PostMapping(value = "server/equipment/site/inspect/project/saveSheetData")
    Boolean saveSheetData(EntSntProjectImportDto importDto);

    @GetMapping(value = "server/equipment/site/inspect/project/importError/{taskId}")
    List<LuckyMultiSheetModel> importError(@PathVariable("taskId") Long taskId);
}
