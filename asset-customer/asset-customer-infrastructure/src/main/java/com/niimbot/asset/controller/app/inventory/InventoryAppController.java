package com.niimbot.asset.controller.app.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.JacksonConverter;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.InventoryAssetFeignClient;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.dynamicform.BizFormAssetConfig;
import com.niimbot.inventory.*;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import feign.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 盘点单后台api
 *
 * <AUTHOR>
 * @date 2021/4/12 11:50
 */
@Slf4j
@Validated
@Api(tags = "APP盘点管理")
@ResultController
@RequestMapping("api/app/inventory")
@RequiredArgsConstructor
public class InventoryAppController {

    private final InventoryFeignClient inventoryFeignClient;
    private final InventoryAssetFeignClient inventoryAssetFeignClient;
    private final DictConvertUtil dictConvertUtil;

    @ApiOperation(value = "APP盘点任务分页列表")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<InventoryTaskListDto> page(InventoryQueryDto dto) {
        return inventoryFeignClient.taskPageApp(dto);
    }

    @ApiOperation(value = "子任务获取盘点任务详情及盘点配置数据")
    @GetMapping("/{id}")
    @AutoConvert
    public InventoryTaskListDto getTaskListInfoById(@PathVariable("id") Long id) {
        return inventoryFeignClient.getTaskListInfoById(id);
    }

    @ApiOperation(value = "APP盘点资产分页列表")
    @GetMapping("/asset/page")
    public PageUtils<InventoryAssetListDto> page(InventorySurplusQueryDto dto) {
        return inventoryAssetFeignClient.appPage(dto);
    }

    @ApiOperation(value = "APP盘点资产分页列表")
    @GetMapping("/asset/scan/ref")
    public Map<String, String> inventoryAssetScanRef(@RequestParam(value = "inventoryId", required = false) Long inventoryId,
                                                     @RequestParam(value = "taskId", required = false)
                                                     @NotNull(message = "任务Id不能为空") Long taskId) {
        return inventoryAssetFeignClient.inventoryAssetScanRef(inventoryId, taskId);
    }

    @ApiOperation(value = "盘点单盘盈资产数据")
    @GetMapping("/detail/surplus")
    public PageUtils<JSONObject> getSurplusById(InventorySurplusQueryDto dto) {
        PageUtils<InventorySurplusDto> inventorySurplusDto = inventoryFeignClient.getSurplusById(dto);
        List<InventorySurplusDto> inventorySurplusDtoList = inventorySurplusDto.getList();

        List<JSONObject> surplusData = Lists.newArrayList();
        try {
            dictConvertUtil.convertToDictionary(inventorySurplusDtoList);
            for (InventorySurplusDto surplusDto : inventorySurplusDtoList) {
                JSONObject assetData = surplusDto.getAssetData();
                String jackson = JacksonConverter.MAPPER.writeValueAsString(surplusDto);
                JSONObject json = JSONObject.parseObject(jackson, Feature.OrderedField);
                json.remove("assetData");
                assetData.putAll(json);
                surplusData.add(assetData);
            }
        } catch (Exception e) {
            log.error("获取盘盈资产数据失败", e);
        }

        PageUtils<JSONObject> surplusPage = new PageUtils<>();
        BeanUtil.copyProperties(inventorySurplusDto, surplusPage);
        surplusPage.setList(surplusData);
        return surplusPage;
    }

    @ApiOperation(value = "APP手动盘点")
    @PutMapping("/manualInventory")
    @RepeatSubmit
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean manualInventory(@RequestBody @Validated InventoryManualDto dto) {
        return inventoryFeignClient.manualInventory(dto);
    }

    @ApiOperation(value = "同步上传盘点任务资产数据")
    @PutMapping("/syncUploadPdTaskAsset")
    @ResultMessage("同步成功")
    @RepeatSubmit
    public Boolean syncUploadPdTaskAsset(@RequestBody @Validated InventorySyncAssetDto dto) {
        if (CollUtil.isEmpty(dto.getSyncAssetData()) && CollUtil.isEmpty(dto.getSyncAddAssetData())) {
            return true;
        }
        inventoryFeignClient.syncUploadPdTaskAsset(
                new Request.Options(30L, TimeUnit.SECONDS, 3L, TimeUnit.MINUTES, true),
                dto);
        return true;
    }

    @ApiOperation(value = "同步上传盘点任务资产数据新版")
    @PutMapping("/syncUploadPdTaskAsset/v2")
    @ResultMessage("同步成功")
    @RepeatSubmit
    public InventorySyncAssetResultDto syncUploadPdTaskAssetV2(@RequestBody @Validated InventorySyncAssetDto dto) {
        if (CollUtil.isEmpty(dto.getSyncAssetData()) && CollUtil.isEmpty(dto.getSyncAddAssetData())) {
            return new InventorySyncAssetResultDto();
        }
        return inventoryFeignClient.syncUploadPdTaskAssetV2(
                new Request.Options(30L, TimeUnit.SECONDS, 3L, TimeUnit.MINUTES, true),
                dto);
    }

    @ApiOperation(value = "提交审核")
    @PutMapping("/submit")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean submit(@RequestBody @Validated InventoryTaskApproveDto approveDto) {
        return inventoryFeignClient.submit(approveDto);
    }

    @ApiOperation(value = "同步新增资产+提交审核")
    @PostMapping("/syncAddAndSubmit")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean syncAddAndSubmit(@RequestBody @Validated InventorySyncAddAndSubmitDto dto) {
        return inventoryFeignClient.syncAddAndSubmit(dto);
    }

    /*@ApiOperation(value = "APP资产上报")
    @PostMapping("/reportAsset")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean reportAsset(@RequestBody @Validated({Insert.class}) InventorySurplusDto dto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject assetData = dto.getAssetData();
        assetUtil.translateAssetJson(assetData, formVO.getFormFields());
        return inventoryFeignClient.reportAsset(dto);
    }

    @ApiOperation(value = "APP编辑上报资产")
    @PutMapping("/reportAsset")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean editReportAsset(@RequestBody @Validated({Update.class}) InventorySurplusDto dto) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        JSONObject assetData = dto.getAssetData();
        assetUtil.translateAssetJson(assetData, formVO.getFormFields());
        return inventoryFeignClient.editReportAsset(dto);
    }

    @ApiOperation(value = "APP批量删除上报资产")
    @DeleteMapping("/reportAsset")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @RepeatSubmit
    public Boolean removeReportAssets(@RequestBody List<Long> ids) {
        return inventoryFeignClient.removeReportAssets(ids);
    }*/

    /**
     * todo app端待确认删除
     */
    @ApiOperation("上报资产-新增/编辑字段列表")
    @Deprecated
    @GetMapping("/attrList")
    public List<BizFormAssetConfig> reportFieldList(@ApiParam(name = "type", value = "类型，默认空")
                                                    @RequestParam(value = "type", required = false) String type) {
        return null;
    }


    // 下次删除
    @Deprecated
    @ApiOperation(value = "APP非当前盘点人已盘资产列表")
    @GetMapping("/asset/otherUser")
    public OtherUserInventoryAssetDto otherUser(InventoryAssetOtherUserDto dto) {
        return inventoryAssetFeignClient.otherUser(dto);
    }

}
