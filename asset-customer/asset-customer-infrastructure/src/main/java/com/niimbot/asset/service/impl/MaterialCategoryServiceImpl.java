package com.niimbot.asset.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.framework.annotation.ExcelField;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.MaterialCategoryService;
import com.niimbot.asset.service.feign.ImportTaskFeignClient;
import com.niimbot.asset.service.feign.MaterialCategoryFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialCategoryDto;
import com.niimbot.material.MaterialCategoryImportDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;
import com.niimbot.system.AuditableImportResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.ImportDto;
import com.niimbot.system.ImportTaskDto;

import org.apache.catalina.manager.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/9/27 18:29
 */
@Slf4j
@Service
public class MaterialCategoryServiceImpl implements MaterialCategoryService {

    private LinkedHashMap<String, String> tableHeader;

    {
        tableHeader = new LinkedHashMap<>();
        tableHeader.putAll(ImmutableMap.<String, String>builder()
                .put("分类编码", "categoryCode")
                .put("分类名称", "categoryName")
                .put("上级分类编码", "pidCode")
                .build());
    }

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    private static ThreadLocal<GlobalCache> globalCache = new TransmittableThreadLocal<>();

    private final RedisService redisService;

    private final MaterialCategoryFeignClient categoryFeignClient;

    private final ImportService importService;

    private final ImportTaskFeignClient importTaskFeignClient;

    @Autowired
    public MaterialCategoryServiceImpl(RedisService redisService,
                                       MaterialCategoryFeignClient categoryFeignClient,
                                       ImportService importService,
                                       ImportTaskFeignClient importTaskFeignClient) {
        this.redisService = redisService;
        this.categoryFeignClient = categoryFeignClient;
        this.importService = importService;
        this.importTaskFeignClient = importTaskFeignClient;
    }

    @Data
    @AllArgsConstructor
    private static class CateExport {
        @ExcelField(header = "分类编码", ordinal = 1)
        private String cateCode;
        @ExcelField(header = "分类名称", ordinal = 2)
        private String cateName;
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ClassPathResource templateSource = new ClassPathResource("/excelTemplate/material_category_template.xlsx");
        try (OutputStream out = response.getOutputStream();
             InputStream inputStream = templateSource.getInputStream()) {
            String fileName = "耗材分类导入模板.xlsx";
            response.setContentType("application/vnd.ms-excel;charset=" + Constants.CHARSET);
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            ExcelWriter writer = reader.getWriter();
            writer.setSheet("分类信息");
            List<MaterialCategoryDto> cateAll = categoryFeignClient.all(null);
            LinkedHashMap<String, String> cateHead = ExcelUtils.buildExcelHead(CateExport.class);
            cateHead.forEach(writer::addHeaderAlias);
            List<CateExport> cateData = cateAll.stream().map(cate ->
                    new CateExport(cate.getCategoryCode(), cate.getCategoryName()))
                    .collect(Collectors.toList());
            writer.setOnlyAlias(true);
            writer.write(cateData);
            writer.autoSizeColumnAll();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.EXPORT_ERROR);
        }

    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId) {
        ExcelReader reader = ExcelUtil.getReader(stream);
        reader.setHeaderAlias(tableHeader);
        // 校验表头
        List<List<Object>> read = reader.read(1, 1);
        if (read.size() > 0) {
            List<Object> header = ExcelUtils.clearEmpty(read.get(0));
            if (header.size() != tableHeader.size()) {
                throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
            }
            header.forEach(it -> {
                if (!tableHeader.containsValue(Convert.toStr(it))) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                }
            });
        } else {
            throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
        }
        // 读取数据并校验
        List<MaterialCategoryImportDto> categoryDtoList = reader.read(1, 1, MaterialCategoryImportDto.class);
        this.importExcel(null, categoryDtoList, fileName, fileSize, companyId, true);
    }

    private void importExcel(Long taskId,
                             List<MaterialCategoryImportDto> read,
                             String fileName,
                             Long fileSize,
                             Long companyId,
                             boolean async) {
        // 判断是否超过最大上传条数，一次限制1000
        if (read.size() > MAX_BATCH) {
            throw new BusinessException(SystemResultCode.IMPORT_MAX_LIMIT, "耗材分类", Convert.toStr(MAX_BATCH));
        }
        // 删除历史导入信息
        if (taskId != null) {
            categoryFeignClient.importErrorDeleteAll(taskId);
        }
        GlobalCache global = new GlobalCache().setCompany(companyId);
        globalCache.set(global);
        if (async) {
            // 异步启动
            new Thread(() -> {
                ImportDto importCache = new ImportDto();
                importCache.setFileName(fileName);
                importCache.setImportType(DictConstant.IMPORT_TYPE_MATERIAL_CATEGORY);
                importCache.setFileSize(fileSize);
                importCache.setCount(read.size());
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(taskId, read, importCache);
            }).start();
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto();
            importCache.setFileName(fileName);
            importCache.setImportType(DictConstant.IMPORT_TYPE_MATERIAL_CATEGORY);
            importCache.setFileSize(fileSize);
            importCache.setCount(read.size());
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(taskId, read, importCache);
        }
    }

    private void startImport(Long taskId, List<MaterialCategoryImportDto> read, ImportDto importCache) {
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (taskId != null) {
            importTaskDto = importTaskFeignClient.queryById(taskId);
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, globalCache.get().getCompany()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
            }

            // 写入表头数据
            this.saveLuckySheetHead(importTaskDto.getId());
            // 记录一行错误信息，生成LuckySheet格式数据
            AtomicInteger successNum = new AtomicInteger(0);
            importService.sendMaterialCategory(globalCache.get().getCompany());
            // 循环处理行数据
            ImportTaskDto finalImportTaskDto = importTaskDto;
            IntStream.range(0, read.size()).forEach(idx -> {
                MaterialCategoryImportDto importDto = read.get(idx);
                importDto.setTaskId(finalImportTaskDto.getId());
                importDto.setErrorNum(0);
                List<LuckySheetModel> luckySheetModelRow = new ArrayList<>();
                // 调用validator
                Set<ConstraintViolation<MaterialCategoryImportDto>> validate = VALIDATOR.validate(importDto);
                Map<String, String> validateData = validate.stream().collect(Collectors.toMap(k -> k.getPropertyPath().toString(), ConstraintViolation::getMessage, (k1, k2) -> k1));

                // 分类编码
                LuckySheetModel assetCategoryCode = validateField(idx + 1 - successNum.get(), 0, importDto.getCategoryCode(), importDto, "categoryCode", validateData);
                luckySheetModelRow.add(assetCategoryCode);

                // 分类名称
                LuckySheetModel assetCategoryName = validateField(idx + 1 - successNum.get(), 1, importDto.getCategoryName(), importDto, "categoryName", validateData);
                luckySheetModelRow.add(assetCategoryName);

                // 上级编码
                LuckySheetModel pidCode = validateField(idx + 1 - successNum.get(), 2, importDto.getPidCode(), importDto, "pidCode", validateData);
                luckySheetModelRow.add(pidCode);

                importDto.setSheetModelList(luckySheetModelRow);

                Boolean success = categoryFeignClient.saveSheetData(importDto);
                if (BooleanUtil.isTrue(success)) {
                    successNum.getAndIncrement();
                }
                importService.sendMaterialCategory(globalCache.get().getCompany());
            });
            AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MRL_CATE, new AuditableImportResult(successNum.get())));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, globalCache.get().getCompany()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, globalCache.get().getCompany()), "finish", true);
            importService.sendMaterialCategory(globalCache.get().getCompany());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.MATERIAL_CATEGORY, globalCache.get().getCompany());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                importTaskFeignClient.update(importTaskDto);
            }
            this.clearThreadLocal();
        }
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        return categoryFeignClient.importError(taskId);
    }

    @Override
    public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        List<Object> header = sheetModels.get(0);
        // 去除表头尾部空列
        int lastIndex = header.size();
        for (int i = header.size() - 1; i >= 0; i--) {
            if (StrUtil.isBlank(Convert.toStr(header.get(i)))) {
                lastIndex = i;
            } else {
                break;
            }
        }
        header = header.subList(0, lastIndex);
        List<MaterialCategoryImportDto> categoryList = new ArrayList<>();
        for (int i = 1; i < sheetModels.size(); i++) {
            List<Object> data = sheetModels.get(i);
            MaterialCategoryImportDto materialCategoryImportDto = new MaterialCategoryImportDto();
            for (int j = 0; j < header.size(); j++) {
                String head = tableHeader.get(Convert.toStr(header.get(j)));
                if (StrUtil.isBlank(head)) {
                    throw new BusinessException(SystemResultCode.IMPORT_HEADER_ERROR);
                } else {
                    if (j < data.size()) {
                        ReflectUtil.setFieldValue(materialCategoryImportDto, head, data.get(j));
                    }
                }
            }
            categoryList.add(materialCategoryImportDto);
        }

        this.importExcel(taskId, categoryList, "耗材分类在线编辑保存", 0L, companyId, false);
        return true;
    }

    @Override
    public Boolean importErrorDelete(Long taskId) {
        return categoryFeignClient.importErrorDeleteAll(taskId);
    }

    private void saveLuckySheetHead(Long taskId) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new ArrayList<>();
        AtomicInteger cellIndex = new AtomicInteger(0);
        List<String> must = ListUtil.of("categoryCode", "categoryName");
        tableHeader.forEach((k, v) -> {
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(k);
            if (must.contains(v)) {
                modelV.setFc("#ff0000");
            }
            headModelList.add(luckySheetModel);
        });
        importErrorDto.setHeadModelList(headModelList);
        this.categoryFeignClient.saveSheetHead(importErrorDto);

    }

    private LuckySheetModel validateField(int r, int c, String data, MaterialCategoryImportDto importDto, String errorCode, Map<String, String> error) {
        LuckySheetModel model = new LuckySheetModel();
        LuckySheetModel.Value modelV = model.getV();
        model.setR(r).setC(c);
        modelV.setV(StrUtil.trim(data));
        if (error.containsKey(errorCode)) {
            LuckySheetModel.Comment comment = new LuckySheetModel.Comment(error.get(errorCode));
            modelV.setPs(comment);
            importDto.setErrorNum(importDto.getErrorNum() + 1);
        }
        return model;
    }

    /**
     * 清理本地缓存
     */
    private void clearThreadLocal() {
        globalCache.remove();
    }

    @Data
    @Accessors(chain = true)
    private static class GlobalCache {
        private Long company;
    }
}
