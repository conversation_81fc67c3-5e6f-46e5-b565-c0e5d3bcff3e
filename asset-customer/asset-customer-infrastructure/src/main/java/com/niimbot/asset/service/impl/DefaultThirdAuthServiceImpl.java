package com.niimbot.asset.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.niimbot.asset.service.ThirdAuthService;
import com.niimbot.asset.service.feign.ThirdpartyFeignClient;
import com.niimbot.event.ThirdAuthEvent;
import com.niimbot.jf.core.exception.category.BusinessException;

import com.niimbot.redisevent.core.SyncResult;
import com.niimbot.redisevent.publisher.RedisEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("defaultThirdAuthService")
public class DefaultThirdAuthServiceImpl implements ThirdAuthService {

    @Resource
    private RedisEventPublisher redisEventPublisher;

    @Resource
    private ThirdpartyFeignClient thirdpartyFeignClient;

    @Resource
    private ObjectMapper objectMapper;
    /**
     * 解析 Redis 消息为 Map
     * 处理可能的双重引号包装问题
     */
    private Map<String, Object> parseMessageToMap(String message) {
        try {
            // 处理可能的双重引号包装问题
            String cleanMessage = message;
            if (message.startsWith("\"") && message.endsWith("\"")) {
                // 移除外层引号并处理转义字符
                cleanMessage = objectMapper.readValue(message, String.class);
            }
            TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};
            return objectMapper.readValue(cleanMessage, typeRef);
        } catch (Exception e) {
            log.error("解析响应消息失败: {}", message, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "解析失败");
            errorResult.put("rawMessage", message);
            errorResult.put("errorMessage", e.getMessage());
            return errorResult;
        }
    }


    @Override
    public Map<String, Object> authenticate(Map<String, Object> authMap) throws BusinessException {
        ThirdAuthEvent event = new ThirdAuthEvent(authMap);
        SyncResult<Map<String, Object>> result = redisEventPublisher.publishSync(
                "sso:default",
                event,
                this::parseMessageToMap,
                results -> results.size() == 1,
                3000L
        );
        if (result.isSuccess()) {
            authMap.put("success", "true");
            authMap.put("account", result.getResults().get(0).get("account"));
            System.out.println("业务完成，结果: " + result.getResults());
        } else {
            System.out.println("业务失败: " + result.getErrorMessage());
            authMap.put("retry", (Integer)authMap.getOrDefault("retry", 0)+1);
            if ((Integer)authMap.get("retry") > 3) {
                authMap.put("success", "false");
                authMap.put("error", "登录失败");
                return authMap;
            }
            return authenticate(authMap);
        }
        return authMap;
    }
}
