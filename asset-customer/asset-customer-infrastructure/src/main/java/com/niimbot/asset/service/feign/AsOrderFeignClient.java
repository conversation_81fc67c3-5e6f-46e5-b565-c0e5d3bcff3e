package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.*;
import com.niimbot.system.QueryConditionSortDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-12-24
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AsOrderFeignClient {

    /**
     * 分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/means/assetOrder/page")
    PageUtils<AsOrderDto> page(AsOrderQueryDto dto);

    /**
     * 设置审批流信息获取
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/means/assetOrder/workflowStepList")
    WorkflowExecuteDto getWorkflowStepList(@RequestBody AsOrderDto dto);

    /**
     * 新增单据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/means/assetOrder")
    AuditableCreateOrderResult insert(AsOrderSubmitDto dto);

    /**
     * 单据详情、含资产数据
     *
     * @param id id
     * @return 结果
     */
    @GetMapping(value = "server/means/assetOrder/{id}")
    AsOrderDto getDetail(@PathVariable("id") Long id);

    /**
     * 单据详情、含资产数据
     *
     * @param id id
     * @return 结果
     */
    @GetMapping(value = "server/means/assetOrder/withoutAsset/{id}")
    AsOrderDto getDetailWithoutAsset(@PathVariable("id") Long id);

    /**
     * 单据资产列表查询
     *
     * @param ids
     * @return 结果
     */
    @PostMapping(value = "server/means/assetOrder/assets")
    List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids);

    /**
     * 分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "server/means/assetOrder/asset/page")
    PageUtils<JSONObject> pageAsset(@SpringQueryMap AsOrderAssetQueryDto dto);

    /**
     * 根据单据id、资产id获取资产详情
     *
     * @param orderId 单据id
     * @param assetId 资产id
     * @return 资产快照详情
     */
    @GetMapping(value = "server/means/assetOrder/asset/{orderId}/{assetId}")
    JSONObject getAssetDetail(
            @PathVariable("orderId") Long orderId, @PathVariable("assetId") Long assetId);

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @param assetId assetId
     * @return 结果
     */
    @GetMapping(value = "server/means/assetOrder/approveOrderByAsset/{assetId}")
    AsOrderInfoDto getApproveOrderByAssetId(@PathVariable("assetId") Long assetId);

    /**
     * 资产单据-用于导出
     *
     * @param orderQueryDto orderQueryDto
     * @return 结果
     */
    @PostMapping(value = "server/means/assetOrder/listForExport")
    List<AsOrderDto> listForExport(AsOrderQueryDto orderQueryDto);

    /**
     * 单据表单查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "server/means/assetOrder/form/{orderType}")
    FormVO getForm(@PathVariable("orderType") Integer orderType);

    @GetMapping(value = "server/means/assetOrder/form/{orderType}")
    FormVO getOrderChangeForm(@PathVariable("orderType") Integer orderType,
                              @RequestParam(value = "singRow", required = false) Boolean singRow);

    /**
     * 变更单单据表单查询
     *
     * @return
     */
    @GetMapping(value = "server/means/assetOrder/changeForm")
    FormVO getChangeForm();

    @GetMapping("server/means/assetOrder/sortField/{orderType}")
    QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType);

}
