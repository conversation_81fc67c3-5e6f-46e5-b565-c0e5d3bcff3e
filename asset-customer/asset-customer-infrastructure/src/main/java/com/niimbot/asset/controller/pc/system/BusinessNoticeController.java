package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.BusinessNoticeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.BusinessNoticeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/3/17 下午4:51
 */
@Api(tags = "业务公告")
@ResultController
@RequestMapping("api/system/businessNotice")
public class BusinessNoticeController {

    @Autowired
    private BusinessNoticeFeignClient businessNoticeFeignClient;

    @ApiOperation(value = "查询业务公告")
    @GetMapping(value = "/detail")
    public BusinessNoticeDto queryDetail() {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        return businessNoticeFeignClient.queryDetail(companyId);
    }

    @ApiOperation(value = "删除业务公告")
    @DeleteMapping(value = "/{noticeId}")
    public Boolean removeNotice(@PathVariable("noticeId") Long noticeId) {
        return businessNoticeFeignClient.removeNotice(noticeId);
    }

    @ApiOperation(value = "保存业务公告")
    @PostMapping(value = "/save")
    public Boolean saveOrUpdate(@RequestBody BusinessNoticeDto noticeDto) {
        return businessNoticeFeignClient.saveOrUpdate(noticeDto);
    }
}
