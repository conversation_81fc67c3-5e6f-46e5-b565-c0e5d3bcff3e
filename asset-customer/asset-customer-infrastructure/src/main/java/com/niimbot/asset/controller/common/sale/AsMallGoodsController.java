package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.MallGoodsFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.MallGoodsPageDto;
import com.niimbot.sale.MallGoodsQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 商品详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Api(tags = "商城购物")
@ResultController
@RequestMapping("/api/mallgoods")
@RequiredArgsConstructor
@Slf4j
public class AsMallGoodsController {

    private final MallGoodsFeignClient mallGoodsFeignClient;

    @ApiOperation(value = "商品列表")
    @GetMapping("/page")
    public PageUtils<MallGoodsPageDto> page(@Validated MallGoodsQueryDto mallGoodsQueryDto) {
        return mallGoodsFeignClient.pageQuery(mallGoodsQueryDto);
    }

    @ApiOperation(value = "商品详情")
    @GetMapping("/detail/{id}")
    public MallGoodsPageDto detail(@PathVariable("id") Long id) {
        return mallGoodsFeignClient.detail(id);
    }
}
