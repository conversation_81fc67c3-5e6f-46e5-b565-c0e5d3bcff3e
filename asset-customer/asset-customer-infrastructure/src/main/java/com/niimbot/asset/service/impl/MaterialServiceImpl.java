package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.MaterialFeignClient;
import com.niimbot.asset.service.feign.MaterialStockFeignClient;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialDto;
import com.niimbot.material.MaterialQueryDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.material.MaterialTotalStockDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR>
 * @since 2021/7/7 10:18
 */
@Service
public class MaterialServiceImpl implements MaterialService {

    private final AsMaterialUtil materialUtil;

    private final MaterialFeignClient materialFeignClient;

    private final DictConvertUtil dictConvertUtil;

    private final MaterialStockFeignClient materialStockFeignClient;

    @Resource
    private FormFeignClient formFeignClient;

    @Autowired
    public MaterialServiceImpl(AsMaterialUtil materialUtil,
                               MaterialFeignClient materialFeignClient,
                               DictConvertUtil dictConvertUtil,
                               MaterialStockFeignClient materialStockFeignClient) {
        this.materialUtil = materialUtil;
        this.materialFeignClient = materialFeignClient;
        this.dictConvertUtil = dictConvertUtil;
        this.materialStockFeignClient = materialStockFeignClient;
    }

    @Override
    public PageUtils<JSONObject> materialPage(MaterialQueryDto queryDto) {
        PageUtils<MaterialDto> pageUtils = materialFeignClient.materialPage(queryDto);
        List<MaterialDto> list = pageUtils.getList();
        dictConvertUtil.convertToDictionary(list);
        // 返回结果集
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);

        List<JSONObject> materialDataList = list.stream()
                .map(MaterialDto::translate).collect(Collectors.toList());
        materialUtil.translateMaterialJsonBatch(materialDataList, material.getFormFields());

        PageUtils<JSONObject> materialPage = new PageUtils<>();
        BeanUtil.copyProperties(pageUtils, materialPage);
        materialPage.setList(materialDataList);
        return materialPage;
    }

    @Override
    public JSONObject getInfo(Long materialId) {
        MaterialDto materialDto = materialFeignClient.getInfo(materialId);
        if (ObjectUtil.isEmpty(materialDto)) {
            return new JSONObject();
        }
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        JSONObject translate = materialDto.translate();
        materialUtil.translateMaterialJson(translate, material.getFormFields());
        return translate;
    }

    @Override
    public JSONObject getInfo(Long repositoryId, Long materialId) {
        MaterialDto materialDto = materialFeignClient.getInfo(materialId);
        if (ObjectUtil.isEmpty(materialDto)) {
            return new JSONObject();
        }
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        JSONObject translate = materialDto.translate();
        materialUtil.translateMaterialJson(translate, material.getFormFields());
        MaterialStockQueryDto stockQueryDto = new MaterialStockQueryDto();
        stockQueryDto.setRepositoryId(repositoryId);
        stockQueryDto.setIncludeMaterialIds(ListUtil.of(materialId));
        MaterialTotalStockDto total = materialStockFeignClient.total(stockQueryDto);
        translate.put("totalQuantity", total.getTotalQuantity());
        translate.put("totalMoney", total.getTotalMoney());
        if (ObjectUtil.isNull(total.getTotalMoney())
                || ObjectUtil.isNull(total.getTotalQuantity())
                || BigDecimal.ZERO.compareTo(total.getTotalMoney()) >= 0
                || BigDecimal.ZERO.compareTo(total.getTotalQuantity()) >= 0) {
            translate.put("averageMoney", BigDecimal.ZERO);
            translate.put("avgPrice", BigDecimal.ZERO);
        } else {
            BigDecimal avgPrice = total.getTotalMoney().divide(total.getTotalQuantity(), 4, RoundingMode.HALF_UP);
            translate.put("avgPrice", avgPrice);
            translate.put("averageMoney", total.getTotalMoney().divide(total.getTotalQuantity(), 4, RoundingMode.HALF_UP));
        }
        translate.put("approveCkNum", total.getApproveCkNum());
        return translate;
    }

    @Override
    public JSONObject getInfoNoPerm(String materialId) {
        MaterialDto materialDto = materialFeignClient.getInfoNoPermPhp(materialId);
        if (ObjectUtil.isEmpty(materialDto)) {
            return new JSONObject();
        }
        FormVO material = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        JSONObject translate = materialDto.translate();
        materialUtil.translateMaterialJson(translate, material.getFormFields());
        return translate;
    }

    @Override
    public Map<Long, JSONObject> getInfoMap(List<Long> ids) {
        // 查询资产
        List<MaterialDto> infoList = materialFeignClient.getInfoList(ids);
        if (ids.size() != infoList.size()) {
            throw new BusinessException(MaterialResultCode.MATERIAL_NOT_EXISTS_OR_DELETE);
        }
        FormVO materialVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
        return infoList.stream().collect(
                Collectors.toMap(
                        MaterialDto::getId,
                        material -> {
                            JSONObject translate = material.translate();
                            materialUtil.translateMaterialJson(translate, materialVO.getFormFields());
                            return translate;
                        },
                        (k1, k2) -> k1));
    }

    @Override
    public MaterialDto add(MaterialDto materialDto) {
        return materialFeignClient.add(materialDto);
    }

    @Override
    public Boolean edit(MaterialDto material) {
        return materialFeignClient.edit(material);
    }
}
