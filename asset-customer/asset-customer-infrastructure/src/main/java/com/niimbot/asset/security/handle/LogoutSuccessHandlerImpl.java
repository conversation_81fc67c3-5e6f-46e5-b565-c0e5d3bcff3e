package com.niimbot.asset.security.handle;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.service.AuditLogService;
import com.niimbot.jf.core.result.Result;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;

import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.extra.spring.SpringUtil;

/**
 * 自定义退出处理类 返回成功
 */
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    private final AbstractTokenService tokenService;

    public LogoutSuccessHandlerImpl(AbstractTokenService tokenService) {
        this.tokenService = tokenService;
    }

    /**
     * 退出处理
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {
        LoginUserDto loginUser = tokenService.getLoginUser(request);
        if (Objects.nonNull(loginUser)) {
            tokenService.delLoginUser(loginUser);
            // 登出日志记录
            CacheResourceUtil cacheResourceUtil = SpringUtil.getBean(CacheResourceUtil.class);
            Map<String, Object> params = new HashMap<>(2);
            params.put(Auditable.Tpl.NAME, cacheResourceUtil.getUserNameAndCode(loginUser.getCusUser().getId()));
            params.put(Auditable.Tpl.TIME, LocalDateTime.now().format(Auditable.FORMATTER));
            SpringUtil.getBean(AuditLogService.class).sendRecord(AuditLogRecord.create(Auditable.Action.LOGOUT, params, loginUser.getCusUser(), loginUser.getTerminal()));
        }
        ServletUtils.renderString(response, JSON.toJSONString(Result.ofSuccess("退出成功")));
    }
}
