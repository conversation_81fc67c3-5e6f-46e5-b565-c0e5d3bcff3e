package com.niimbot.asset.service;

/**
 * <AUTHOR>
 * @since 2021/9/2 10:18
 */
public interface ImportService {

    String EMPLOYEE = "employee";
    String ORG = "org";
    String MATERIAL = "material";
    String MATERIAL_EDIT = "material_edit";
    String ASSET = "asset";
    String ASSET_EDIT = "asset_edit";
    String ASSET_CATEGORY = "assetCategory";
    String MATERIAL_CATEGORY = "materialCategory";
    String MATERIAL_REPOSITORY = "materialRepository";
    String AREA = "area";

    String INSPECT_POINT = "inspectPoint";
    String INSPECT_PROJECT = "inspectProject";
    String PRODUCT = "product";
    String ASSET_FINANCE = "asset_finance";

    void sendAssetMsg(Long companyId);

    void sendAssetEditMsg(Long companyId);

    void sendMaterialMsg(Long companyId);

    void sendMaterialEditMsg(Long companyId);

    void sendEmployeeMsg(Long companyId);

    void sendOrgMsg(Long companyId);

    void sendAssetCategory(Long companyId);

    void sendMaterialCategory(Long companyId);

    void sendMaterialRepository(Long companyId);

    void sendArea(Long companyId);

    void sendProduct(Long companyId);

    void sendAssetFinance(Long companyId);

    void sendInspectPoint(Long companyId);

    void sendInspectProject(Long companyId);

//    List<ImportDto> importTask(Long companyId);

}
