package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.means.*;
import com.niimbot.system.AuditableOperateResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/12/15 16:44
 */
public interface AssetService {

    /**
     * 分页查询资产数据
     *
     * @param queryDto 资产数据
     * @return 资产ID
     */
    PageUtils<JSONObject> assetPcPage(AssetQueryConditionDto queryDto);

    /**
     * App端分页查询资产数据
     *
     * @param queryDto 资产数据
     * @return 资产ID
     */
    PageUtils<AssetAppPageDto> assetAppPage(AssetQueryConditionDto queryDto);

    /**
     * 员工资产列表清单 使用的与管理的
     *
     * @param empId 员工ID
     * @return list
     */
    Map<String, List<JSONObject>> empAssetList(Long empId);

    /**
     * 新增保存资产
     *
     * @param asset 资产数据
     * @return 资产ID
     */
    AssetDto add(AssetDto asset);

    /**
     * 编辑保存资产
     *
     * @param asset 资产数据
     * @return true/false
     */
    Boolean edit(AssetDto asset);

    /**
     * 批量编辑保存资产
     *
     * @param assetBatchDto 资产数据
     * @return true/false
     */
    List<AuditableOperateResult> editBatch(AssetBatchDto assetBatchDto);

    /**
     * 查询资产详情
     *
     * @param assetId 资产编码
     * @return 资产详情集合
     */
    JSONObject getInfo(String assetId);

    /**
     * 查询资产详情
     *
     * @param ids 资产编码
     * @return 资产详情集合
     */
    List<JSONObject> getInfoList(List<Long> ids);

    /**
     * 查询资产详情
     *
     * @param ids 资产编码
     * @return 资产详情集合
     */
    Map<Long, JSONObject> getInfoMap(List<Long> ids);

    /**
     * 复制资产
     *
     * @param copyDto 复制信息
     * @return true/false
     */
    List<AuditableOperateResult> copy(AssetCopyDto copyDto);

    /**
     * 根据资产id获取资产详情-不带权限的
     *
     * @param assetId   资产id
     * @param orderType 单据类型
     * @param hasPerm   是否需要权限
     * @return 详情
     */
    JSONObject getInfoNoPerm(String assetId, Integer orderType, Boolean hasPerm);

    /**
     * 根据资产id或者资产phpId获取资产详情-不带权限，这里做了和phpId关联，上层谨慎调用
     * @param assetId 资产id或者phpId
     * @param orderType 单据类型
     * @param hasPerm 是否需要权限
     * @return 资产详情数据
     */
    JSONObject getInfoNoPermPhp(String assetId, Integer orderType, Boolean hasPerm);

    List<JSONObject> quickSearch(AssetQuickSearchDto quickSearchDto);
}
