package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.material.MaterialRepositoryDto;
import com.niimbot.material.inventory.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/6 17:46
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialInventoryFeignClient {
    String BASE_URL = "/server/material/inventory";

    @PostMapping(BASE_URL + "/page")
    PageUtils<MaterialInventoryPageDto> page(InventoryQueryDto qry);

    @PostMapping(BASE_URL + "/app/page")
    PageUtils<MaterialInventoryAppPageDto> appPage(InventoryQueryDto qry);

    @GetMapping(BASE_URL + "/status/statistics")
    MaterialInventoryStatusStatisticsDto statusStatistics();

    @GetMapping(BASE_URL + "/statistics/{inventoryId}")
    MaterialInventoryStatisticsDto statistics(@PathVariable("inventoryId") Long inventoryId);

    @GetMapping(BASE_URL + "/info/{inventoryId}")
    MaterialInventoryInfoDto info(@PathVariable("inventoryId") Long inventoryId);

    @GetMapping(BASE_URL + "/detail/material/info/{detailId}")
    MaterialInventoryMaterialInfoDto detailMaterialInfo(@PathVariable("detailId") Long detailId);

    @PostMapping(BASE_URL + "/create")
    MaterialInventoryInfoDto create(MaterialInventoryCreateDto createDto);

    @PostMapping(BASE_URL + "/detail/page")
    PageUtils<MaterialInventoryDetailPageDto> detailPage(InventoryDetailQueryDto qry);

    @PostMapping(BASE_URL + "/detail/app/page")
    PageUtils<MaterialInventoryDetailAppPageDto> detailAppPage(InventoryDetailAppQueryDto qry);

    @PostMapping(BASE_URL + "/cancel/{inventoryId}")
    MaterialInventoryInfoDto cancel(@PathVariable("inventoryId") Long inventoryId);

    @PostMapping(BASE_URL + "/delete/{inventoryId}")
    MaterialInventoryInfoDto delete(@PathVariable("inventoryId") Long inventoryId);

    @PostMapping(BASE_URL + "/report")
    MaterialInventoryReportDto report(MaterialInventoryReportDto reportDto);

    @PostMapping(BASE_URL + "/manualInventory")
    Boolean manualInventory(MaterialInventoryManualDto inventoryManualDto);

    @PostMapping(BASE_URL + "/group/repository")
    List<MaterialInventoryGroupDto> repositoryGroup(MaterialInventoryGroupQueryDto queryDto);

    @PostMapping(BASE_URL + "/updateInventoryUsers")
    MaterialInventoryInfoDto updateInventoryUsers(MaterialInventoryUpdateUserDto updateUserDto);

    @PostMapping(BASE_URL + "/addInventoryUsers")
    Boolean addInventoryUsers(MaterialInventoryAddUserDto addUserDto);

    @PostMapping(BASE_URL + "/submit")
    MaterialInventoryInfoDto submit(MaterialInventorySubmitDto submitDto);

    @GetMapping(BASE_URL + "/repository/{inventoryId}")
    List<MaterialRepositoryDto> repositoryList(@PathVariable("inventoryId") Long inventoryId);

    @GetMapping(BASE_URL + "/progressRepo")
    List<Long> progressRepoList();

    @GetMapping(BASE_URL + "/scan/material")
    MaterialScanMaterialDto scanMaterial(@RequestParam(value = "inventoryId") String inventoryId,
                                         @RequestParam("materialId") String materialId,
                                         @RequestParam("repositoryId") String repositoryId);

    @GetMapping(BASE_URL + "/result/report/{inventoryId}")
    MaterialInventoryResultReportDto resultReport(@PathVariable("inventoryId") Long inventoryId);
}
