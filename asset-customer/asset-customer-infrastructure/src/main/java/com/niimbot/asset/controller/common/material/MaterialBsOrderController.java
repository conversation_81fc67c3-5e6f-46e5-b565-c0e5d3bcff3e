package com.niimbot.asset.controller.common.material;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.MaterialOrderService;
import com.niimbot.asset.service.MaterialService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.MaterialBsOrderFeignClient;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialBsOrderDetailDto;
import com.niimbot.material.MaterialBsOrderDto;
import com.niimbot.material.MaterialBsOrderSubmitDto;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.material.MaterialOrderResponseDto;
import com.niimbot.system.Auditable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2022/10/31 15:58
 */
@Api(tags = "【耗材】单据")
@ResultController
@RequestMapping("api/common/material/order/bs")
@Validated
public class MaterialBsOrderController {

    private final MaterialBsOrderFeignClient materialBsOrderFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final AsMaterialUtil materialUtil;
    private final MaterialService materialService;
    private final MaterialOrderService materialOrderService;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @Autowired
    public MaterialBsOrderController(MaterialBsOrderFeignClient materialBsOrderFeignClient,
                                     ActWorkflowFeignClient workflowFeignClient,
                                     DictConvertUtil dictConvertUtil,
                                     AsMaterialUtil materialUtil,
                                     MaterialService materialService,
                                     MaterialOrderService materialOrderService) {
        this.materialBsOrderFeignClient = materialBsOrderFeignClient;
        this.workflowFeignClient = workflowFeignClient;
        this.dictConvertUtil = dictConvertUtil;
        this.materialUtil = materialUtil;
        this.materialService = materialService;
        this.materialOrderService = materialOrderService;
    }


    @ApiOperation(value = "【报损单】设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated MaterialBsOrderDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_BS);
        return materialBsOrderFeignClient.getWorkflowStepList(dto);

    }

    @ApiOperation(value = "【报损单】创建报损单")
    @PostMapping
    @RepeatSubmit
    @AuditLog(Auditable.Action.OR_MRL_BS)
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public AuditableCreateOrderResult create(@RequestBody @Validated MaterialBsOrderSubmitDto submitDto) {
        MaterialBsOrderDto orderDto = submitDto.getOrderDto();
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_BS);
        // 校验单据
        materialUtil.verifyAndTranslation(orderDto, AssetConstant.ORDER_TYPE_MATERIAL_BS);
        // 查询耗材信息
        List<Long> materialIds = orderDto.getMaterials().stream()
                .map(MaterialBsOrderDetailDto::getMaterialId).collect(Collectors.toList());
        Map<Long, JSONObject> infoMap = materialService.getInfoMap(materialIds);
        orderDto.getMaterials().forEach(detailDto -> {
            JSONObject data = infoMap.get(detailDto.getMaterialId());
            detailDto.setMaterialSnapshotData(data);
        });

        // 总种类
        int totalType = 0;
        // 总数量
        BigDecimal totalNum = BigDecimal.ZERO;
        for (MaterialBsOrderDetailDto m : orderDto.getMaterials()) {
            totalType += 1;
            totalNum = totalNum.add(m.getBsNum());
        }

        orderDto.setTotalNum(totalNum).setTotalType(totalType);

        List<JSONObject> sortList = materialIds.stream().map(infoMap::get).collect(Collectors.toList());
        orderDto.setSummary(materialUtil.buildSummary(sortList));
        return materialBsOrderFeignClient.create(submitDto);
    }

    @ApiOperation(value = "【报损单】报损单详情")
    @GetMapping("/{id}")
    @AutoConvert
    public MaterialOrderResponseDto getById(@PathVariable("id") Long id) {
        MaterialBsOrderDto orderDto = materialBsOrderFeignClient.getById(id);
        if (ObjectUtil.isNotNull(orderDto)) {
            dictConvertUtil.convertToDictionary(orderDto);
            orderDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_BS);
            // 翻译耗材
            JSONObject orderJson = materialUtil.toJSONObject(orderDto);
            //数据脱敏处理
            if (Objects.nonNull(orderJson)) {
                desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderJson), SensitiveObjectTypeEnum.MATERIAL.getCode());
            }
            if (orderDto.getApproveStatus() == 0) {
                return new MaterialOrderResponseDto()
                        .setOrder(orderJson)
                        .setApproveInfo(new WorkflowApproveInfoDto());
            }
            WorkflowApproveInfoDto approveInfoDto;
            if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_BS, id);
                if (approveInfoDto == null || approveInfoDto.getExecuteList().isEmpty()) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_BS, id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_MATERIAL_BS, id);
                if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(orderDto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
            return new MaterialOrderResponseDto()
                    .setOrder(orderJson)
                    .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
        } else {
            throw new BusinessException(MaterialResultCode.ORDER_NOT_EXISTS, "报损单" + id);
        }
    }

    @ApiOperation(value = "【报损单】报损单批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<MaterialOrderResponseDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_MATERIAL_BS);
        return requestDto.getOrderIds().stream().map(this::getById).collect(Collectors.toList());
    }

    @ApiOperation(value = "【报损单】报损单耗材快照")
    @GetMapping("/detail/{orderId}/{materialId}")
    public JSONObject getDetail(@PathVariable("orderId") Long orderId,
                                @PathVariable("materialId") Long materialId) {
        MaterialBsOrderDetailDto detailDto = materialBsOrderFeignClient.getDetail(orderId, materialId);
        if (detailDto == null) {
            throw new BusinessException(MaterialResultCode.ORDER_DETAIL_NOT_EXIST);
        }
        JSONObject result = materialUtil.toJSONObject(detailDto);
        //数据脱敏处理
        if (Objects.nonNull(result)) {
            desensitizationDataUtil.handleSensitiveField(Collections.singletonList(result), SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return result;
    }

    @ApiOperation(value = "【报损单】报损单分页")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialBsOrderDto> page = materialBsOrderFeignClient.page(query);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (MaterialBsOrderDto orderDto : page.getList()) {
            orderDto.setOrderType(OrderFormTypeEnum.BS.getCode());
            //数据脱敏处理
//            if (CollUtil.isNotEmpty(orderDto.getMaterials())) {
//                desensitizationDataUtil.handleSensitiveField(orderDto.getMaterials()
//                        .stream()
//                        .map(MaterialBsOrderDetailDto::getMaterialSnapshotData)
//                        .collect(Collectors.toList()), SensitiveObjectTypeEnum.MATERIAL.getCode());
//            }
            list.add(materialUtil.toJSONObject(orderDto));
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【报损单】报损单分页")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<JSONObject> pageGet(MaterialOrderQueryDto dto) {
        return page(dto);
    }

    @ApiOperation(value = "【报损单】报损单耗材详情")
    @GetMapping("/detail/page")
    public PageUtils<JSONObject> pageDetail(@Validated MaterialOrderDetailQueryDto query) {
        PageUtils<MaterialBsOrderDetailDto> page = materialBsOrderFeignClient.pageDetail(query);
        List<JSONObject> list = new ArrayList<>();
        for (MaterialBsOrderDetailDto detailDto : page.getList()) {
            JSONObject jsonObject = materialUtil.toJSONObject(detailDto);
            list.add(jsonObject);
        }
        //数据脱敏处理
        if (CollUtil.isNotEmpty(list)) {
            desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.MATERIAL.getCode());
        }
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "【导出】导出报损单据卡片")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderCard(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialBsOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrderCard(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_BS);
    }

    @ApiOperation(value = "【导出】导出报损单耗材")
    @PostMapping(value = "/exportOrder/materials")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrder(@RequestBody @Validated MaterialOrderQueryDto query) {
        PageUtils<MaterialOrderExportDto> page = materialBsOrderFeignClient.listForExport(query);
        return materialOrderService.exportOrder(page.getList(), query, AssetConstant.ORDER_TYPE_MATERIAL_BS);
    }

}
