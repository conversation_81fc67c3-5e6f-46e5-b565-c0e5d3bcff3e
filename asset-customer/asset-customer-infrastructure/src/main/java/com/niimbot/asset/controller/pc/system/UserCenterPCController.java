package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.annotation.RequestJson;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.asset.service.feign.SensitiveAuthorityFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AccountDto;
import com.niimbot.system.AccountInfoDto;
import com.niimbot.system.CompanyDto;
import com.niimbot.system.CompanySettingDto;
import com.niimbot.system.DataAuthorityDto;
import com.niimbot.system.DataPermissionDto;
import com.niimbot.system.RoleDataAuthorityConfigDto;
import com.niimbot.system.SensitiveConfigDto;
import com.niimbot.system.SensitiveDataPermissionDto;
import com.niimbot.system.SensitivePermissionDto;
import com.niimbot.system.UserCenterPCDto;
import com.niimbot.system.UserPasswordDto;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * App用户中心
 *
 * <AUTHOR>
 * @Date 2020/11/25
 */
@Api(tags = {"PC用户中心接口"})
@ResultController
@RequestMapping("api/pc/userCenter")
@Slf4j
@Validated
public class UserCenterPCController {

    @Resource
    private AccountCenterFeignClient accountCenterFeignClient;

    @Resource
    private CusUserFeignClient userFeignClient;

    @Resource
    private CusEmployeeFeignClient employeeFeignClient;

    @Resource
    private CompanyFeignClient companyFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Autowired
    private SensitiveAuthorityFeignClient sensitiveAuthorityFeignClient;

    /**
     * PC端用户中心数据
     *
     * @return UserCenterPCDto
     */
    @ApiOperation(value = "用户中心")
    @GetMapping("/center")
    public UserCenterPCDto center() {
        return userFeignClient.getUserCenterPCInfo();
    }

    /**
     * 修改姓名
     *
     * @param username username
     * @return 成功与否
     */
    @ApiOperation(value = "修改姓名")
    @PutMapping("/changeName")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeName(@NotBlank(message = "用户名不能为空")
                              @Size(min = 2, max = 20, message = "请输入用户名 / 2-20位，可包含中文，字母")
                              @RequestJson("username") String username) {
        return employeeFeignClient.changeName(username);
    }

    /**
     * 修改密码
     * <p>
     * todo.. will be removed soon
     *
     * @param dto dto
     * @return 成功与否
     */
    @ApiOperation(value = "设置密码/修改密码，【设置密码、忘记密码时oldPassword可不传】")
    @PutMapping("/changePassword")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    @Deprecated
    public Boolean changePassword(@RequestBody @Validated UserPasswordDto dto) {
        String newPassword = SecurityUtils.decryptPassword(dto.getNewPassword());
        String rePassword = SecurityUtils.decryptPassword(dto.getRePassword());
        if (StrUtil.isNotBlank(dto.getOldPassword())) {
            String oldPassword = SecurityUtils.decryptPassword(dto.getOldPassword());
//            ValidationUtils.checkPassword(SystemResultCode.USER_PASSWORD_ERROR, oldPassword, newPassword, rePassword);
            String rawPassword = userFeignClient.checkPassword();
            if (!SecurityUtils.matchesPassword(oldPassword, rawPassword)) {
                throw new BusinessException(SystemResultCode.USER_OLD_PASSWORD_ERROR);
            }
        } else {
//            ValidationUtils.checkPassword(SystemResultCode.USER_PASSWORD_ERROR, newPassword, rePassword);
        }
        if (!StringUtils.equals(newPassword, rePassword)) {
            throw new BusinessException(SystemResultCode.USER_PASSWORD_CONFIRM_ERROR);
        }
        return userFeignClient.changeCurrentUserPassword(newPassword);
    }

    /**
     * 校验手机验证码
     *
     * @param mobile  手机号
     * @param smsCode 验证码
     * @return 验证码是否正确
     */
    @ApiOperation(value = "校验验证码")
    @GetMapping("/checkSmsCode")
    public Boolean checkSmsCode(@NotBlank(message = "请输入手机号")
                                    @Size(max = 11, message = "手机号最多11位") String mobile,
                                @NotBlank(message = "请输入区号") String nationalCode,
                                @Size(min = 4, max = 4, message = "验证码为4位") String smsCode) {
        NationalCodeValidate.checkCNMobile(nationalCode,mobile);
        if (!smsCodeFeignClient.checkSmsCode(mobile, smsCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        return true;
    }

    /**
     * 绑定手机号
     *
     * @param mobile  手机号
     * @param smsCode 验证码
     * @return 验证码是否正确
     */
    @ApiOperation(value = "绑定手机号")
    @GetMapping("/bindMobile")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean bindMobile(@NotBlank(message = "请输入手机号") String mobile,
                              @NotBlank(message = "请输入区号") String nationalCode,
                              @Size(min = 4, max = 4, message = "验证码为4位") String smsCode) {
        checkSmsCode(mobile,nationalCode, smsCode);
        return userFeignClient.bindMobile(mobile);
    }

    /**
     * 修改行业
     *
     * @param industryId 行业id
     * @return 是否成功
     */
    @ApiOperation(value = "修改行业")
    @PutMapping("/industry/{industryId}")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeIndustry(@PathVariable("industryId") Long industryId) {
        return companyFeignClient.changeIndustry(industryId);
    }

    @ApiOperation("开放闲置资产")
    @RepeatSubmit
    @PutMapping("/enableIdleAsset/{enable}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean modifyEnableIdleAsset(@PathVariable("enable") Boolean enable) {
        return companyFeignClient.modifyEnableIdleAsset(enable);
    }

    @ApiOperation("是否已开放闲置资产配置")
    @GetMapping("/getIdleAssetSwitch")
    public Boolean getIdleAssetSwitch() {
        return companyFeignClient.getIdleAssetSwitch(LoginUserThreadLocal.getCompanyId());
    }

    @ApiOperation("企业配置详情")
    @GetMapping("/companySetting/details")
    public CompanySettingDto companySettingDetails() {
        return companyFeignClient.selectCompanySettingById(LoginUserThreadLocal.getCompanyId());
    }

    @ApiOperation(("获取企业默认用户数据权限配置"))
    @GetMapping("/getCompanyDefaultDataAuth")
    public List<DataPermissionDto> getCompanyDefaultDataAuth() {
        return companyFeignClient.getCompanyDefaultDataAuth(LoginUserThreadLocal.getCompanyId());
    }

    @ApiOperation("敏感字段配置")
    @GetMapping("/sensitiveConfig")
    public List<SensitiveConfigDto> sensitiveConfig() {
        return sensitiveAuthorityFeignClient.sensitiveConfig();
    }


    @ApiOperation(("获取角色默认数据权限配置"))
    @GetMapping("/getRoleDataAuth")
    public SensitiveDataPermissionDto getRoleDataAuth(@RequestParam(name = "roleId", required = true) Long roleId) {
        SensitiveDataPermissionDto result = new SensitiveDataPermissionDto();
        //角色数据权限
        List<DataPermissionDto> dataPermissionDtoList = companyFeignClient.getRoleDataAuth(roleId);
        //角色敏感数据权限
        List<SensitivePermissionDto> sensitivePermissionDtoList = sensitiveAuthorityFeignClient.roleAuthority(roleId);
        result.setDataPermission(dataPermissionDtoList);
        result.setSensitivePermission(sensitivePermissionDtoList);
        return result;
    }

    @ApiOperation("配置企业默认数据权限")
    @PutMapping("/configDefaultDataAuth")
    public Boolean configureDefaultDataAuth(@RequestBody List<DataAuthorityDto> dataAuthorityDtos) {
        return companyFeignClient.configureDefaultDataAuth(dataAuthorityDtos, LoginUserThreadLocal.getCompanyId());
    }

    @ApiOperation(("配置角色数据权限"))
    @PostMapping("/configRoleDataAuth")
    public Boolean configRoleDataAuth(@RequestBody @Validated RoleDataAuthorityConfigDto roleDataAuthorityConfigDto) {
        //设置企业id
        roleDataAuthorityConfigDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        return companyFeignClient.configRoleDataAuth(roleDataAuthorityConfigDto);
    }

    /**
     * 修改企业名称
     *
     * @param name name
     * @return 成功与否
     */
    @ApiOperation(value = "修改企业名称")
    @PutMapping("/changeCompanyName")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeCompanyName(@NotBlank(message = "企业名称不能为空")
                                     @Size(min = 2, max = 30, message = "企业名称最多30个字符")
                                     @RequestJson("name") String name) {
        return companyFeignClient.changeCompanyName(name);
    }

    /**
     * 修改用户所在企业logo
     *
     * @param url 图片地址
     * @return true/false
     */
    @ApiOperation(value = "修改企业logo")
    @PostMapping(value = "/changeLogo")
    public Boolean uploadLogo(@RequestJson("url") String url) {
        if (url.length() > 200) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "图片名称超长");
        }
        return companyFeignClient.changeLogo(url);
    }

    /**
     * 修改当前用户头像
     *
     * @param url 图片地址
     * @return true/false
     */
    @ApiOperation(value = "修改头像")
    @PostMapping(value = "/changeImage")
    public Boolean uploadImage(@RequestJson("url") String url) {
        return employeeFeignClient.changeImage(url);
    }

    /**
     * 更换手机号【第二步】
     *
     * @param dto 更换验证码实体
     * @return 成功true、失败false
     */
    @ApiOperation(value = "更换手机号【第二步】")
    @PostMapping("/changeMobile")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeMobile(@RequestBody UserCenterPCDto.ChangeMobileDTO dto) {
        CusUserDto cusUser = SpringUtil.getBean("tokenService", AbstractTokenService.class)
                .getLoginUser(ServletUtils.getRequest()).getCusUser();
        if (cusUser != null) {
            Long accountId = cusUser.getAccountId();
            AccountDto account = accountCenterFeignClient.getAccountById(accountId);
            // CusUserDto byId = userFeignClient.getById(cusUser.getId());
            if (Objects.equals(dto.getNewMobile(), account.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_PHONE_CHANGE_NEW_SAME);
            }
        }
        AccountDto accountByWay2 = accountCenterFeignClient.getAccountByWay2(dto.getNewMobile());
        // CusEmployeeDto employeeDto = employeeFeignClient.checkMobile(dto.getNewMobile());
        if (accountByWay2 != null) {
            throw new BusinessException(SystemResultCode.USER_CHANGE_MOBILE_ERROR);
        }
        NationalCodeValidate.checkCNMobile(dto.getNationalCode(),dto.getNewMobile());
        checkSmsCode(dto.getNewMobile(),dto.getNationalCode(), dto.getSmsCode());
        return userFeignClient.changeMobile(dto.getNewMobile());
    }

    /**
     * 校验旧手机号
     *
     * @param oldMobile 旧手机号
     * @param smsCode   验证码
     * @return 校验手机号是否正确
     */
    @ApiOperation(value = "更换手机号【第一步】、校验旧手机号")
    @GetMapping("/verifyOldMobile")
    public Boolean confirmOld(@NotBlank(message = "请输入手机号")
                                  @Size(max = 11, message = "手机号最多11位") String oldMobile,
                              @NotBlank(message = "请输入区号") String nationalCode,
                              @Size(min = 4, max = 4, message = "验证码为4位")
                              @NotBlank(message = "验证码不能为空") String smsCode) {
        CusUserDto cusUser = SpringUtil.getBean("tokenService", AbstractTokenService.class)
                .getLoginUser(ServletUtils.getRequest()).getCusUser();
        NationalCodeValidate.checkCNMobile(nationalCode,oldMobile);
        if (cusUser != null) {
            Long accountId = cusUser.getAccountId();
            AccountDto account = accountCenterFeignClient.getAccountById(accountId);
            // CusUserDto byId = userFeignClient.getById(cusUser.getId());
            if (!Objects.equals(oldMobile, account.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_PHONE_CHANGE_OLD);
            }
        }
        return checkSmsCode(oldMobile,nationalCode, smsCode);
    }

    /**
     * PC端用户中心-企业信息
     *
     * @return CompanyDto
     */
    @ApiOperation(value = "PC端用户中心-企业信息")
    @GetMapping("/companyInfo")
    public CompanyDto companyInfo() {
        // 当前企业id
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 获取企业信息
        return companyFeignClient.getCompanyInfo(companyId);
    }

    /**
     * PC端用户中心-账号信息
     *
     * @return AccountInfoDto
     */
    @ApiOperation(value = "PC端用户中心-账号信息")
    @GetMapping("/accountInfo")
    public AccountInfoDto accountInfo() {
        return userFeignClient.currentAccountInfo();
    }
}
