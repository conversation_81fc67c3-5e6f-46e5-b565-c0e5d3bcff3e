package com.niimbot.asset.controller.common.activiti;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.activiti.*;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.websocket.WebSocketServer;
import com.niimbot.asset.websocket.msg.SignatureMessage;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.material.inventory.MaterialOrderPreCheckDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/2/23 17:59
 */
@Slf4j
@Api(tags = "审批流管理")
@ResultController
@RequestMapping("api/common/workflow")
@RequiredArgsConstructor
public class ActWorkflowController {

    private final ActWorkflowFeignClient actWorkflowFeignClient;

    @ApiOperation(value = "查询审批流数据列表")
    @AutoConvert
    @GetMapping("/list/{activitiKey}")
    public List<ActWorkflowDto> list(@PathVariable("activitiKey") String activitiKey) {
        List<ActWorkflowDto> list = actWorkflowFeignClient.list(activitiKey);
        list.forEach(f -> f.setAllOrg(CollUtil.isEmpty(f.getSuitOrg())));
        return list;
    }

    @ApiOperation(value = "查询审批流数据详情")
    @GetMapping("/{flowId}")
    public ActWorkflowGraphDto getInfo(@PathVariable("flowId") Long flowId) {
        return actWorkflowFeignClient.getInfo(flowId);
    }

    @ApiOperation(value = "新增审批流")
    @RepeatSubmit
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean addFlow(@RequestBody @Validated(value = {Insert.class}) ActWorkflowDto actWorkflowDto) {
        if (BooleanUtil.isTrue(actWorkflowDto.getAllOrg())) {
            actWorkflowDto.setSuitOrg(ListUtil.empty());
        }
        return actWorkflowFeignClient.addFlow(actWorkflowDto);
    }

    @ApiOperation(value = "编辑审批流")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean editFlow(@RequestBody @Validated(value = {Update.class}) ActWorkflowDto actWorkflowDto) {
        if (BooleanUtil.isTrue(actWorkflowDto.getAllOrg())) {
            actWorkflowDto.setSuitOrg(ListUtil.empty());
        }
        return actWorkflowFeignClient.editFlow(actWorkflowDto);
    }

    @ApiOperation(value = "启用审批流")
    @PutMapping("/enable/{flowId}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean enable(@PathVariable("flowId") Long flowId) {
        ActWorkflowDto workflowDto = new ActWorkflowDto();
        workflowDto.setId(flowId).setStatus(DictConstant.SYS_ENABLE);
        return actWorkflowFeignClient.updateStatus(workflowDto);
    }

    @ApiOperation(value = "禁用审批流")
    @PutMapping("/disable/{flowId}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean disable(@PathVariable("flowId") Long flowId) {
        ActWorkflowDto workflowDto = new ActWorkflowDto();
        workflowDto.setId(flowId).setStatus(DictConstant.SYS_DISABLE);
        return actWorkflowFeignClient.updateStatus(workflowDto);
    }

    @ApiOperation(value = "删除审批流")
    @DeleteMapping("/{flowId}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean delete(@PathVariable("flowId") Long flowId) {
        return actWorkflowFeignClient.delete(flowId);
    }

    @ApiOperation(value = "保存并部署流程")
    @PutMapping("/saveAndDeploy")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean saveAndDeploy(@RequestBody @Validated ActWorkflowGraphDto graphDto) {
        return actWorkflowFeignClient.saveAndDeploy(graphDto);
    }

    @ApiOperation(value = "流程审批通过")
    @PutMapping("/approved")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean approved(@RequestBody @Validated WorkflowApproveDto approveDto) {
        return actWorkflowFeignClient.approved(approveDto);
    }

    @ApiOperation(value = "耗材单据检查")
    @PostMapping("/materialOrder/preCheck")
    public MaterialOrderPreCheckDto materialOrderPreCheck(@RequestBody WorkflowApproveDto approveDto) {
        return actWorkflowFeignClient.materialOrderPreCheck(approveDto);
    }

    @ApiOperation(value = "获取签名AuthCode")
    @GetMapping("/signature/authCode")
    public ActWorkflowSignAuthCodeDto signAuthCode(@RequestParam("orderId") Long orderId) {
        return actWorkflowFeignClient.signAuthCode(orderId);
    }

    @ApiOperation(value = "获取签名AuthCodeLink")
    @GetMapping("/signature/authCode/link")
    public ActWorkflowSignAuthCodeDto signAuthCodeLink(@RequestParam("authCode") String authCode) {
        return actWorkflowFeignClient.signAuthCodeLink(authCode);
    }

    @ApiOperation(value = "校验签名AuthCode[不需要token]")
    @PostMapping("/signature/authCode/verify")
    public ActWorkflowSignAuthCodeDto signAuthCodeVerify(@RequestParam("authCode") String authCode) {
        return actWorkflowFeignClient.signAuthCodeVerify(authCode);
    }

    @ApiOperation(value = "提交签名[不需要token]")
    @PostMapping("/signature/submit")
    public Boolean signatureSubmit(@RequestBody @Validated ActWorkflowSignSubmitDto submitDto) {
        ActWorkflowSignSubmitDto signatureSubmit = actWorkflowFeignClient.signatureSubmit(submitDto);
        try {
            WebSocketServer.sendMessage(Convert.toLong(signatureSubmit.getUserId()), new SignatureMessage(signatureSubmit.getAuthCode(), signatureSubmit.getSignatureLink()));
        } catch (Exception e) {
            log.error("提交签名消息通知异常，data=[{}], {}", JSONObject.toJSONString(signatureSubmit), e.getMessage(), e);
        }
        return true;
    }

    @ApiOperation(value = "流程审批驳回")
    @PutMapping("/rejected")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean rejected(@RequestBody @Validated WorkflowApproveDto approveDto) {
        return actWorkflowFeignClient.rejected(approveDto);
    }

    @ApiOperation(value = "流程撤销")
    @PutMapping("/revoked/{processInstanceId}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean revoked(@PathVariable("processInstanceId") String processInstanceId) {
        return actWorkflowFeignClient.revoked(processInstanceId);
    }

    @ApiOperation(value = "流程转办")
    @PutMapping("/forward")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean forward(@RequestBody WorkflowForwardDto forwardDto) {
        return actWorkflowFeignClient.forward(forwardDto);
    }

    @ApiOperation(value = "运行时流程执行信息")
    @GetMapping("/runWorkflowApproveInfo/{type}/{businessId}")
    public WorkflowApproveInfoDto getRunWorkflowApproveInfo(@PathVariable("type") Short type,
                                                            @PathVariable("businessId") Long businessId) {
        return actWorkflowFeignClient.getRunWorkflowApproveInfo(type, businessId);
    }

    @ApiOperation(value = "历史流程执行信息")
    @GetMapping("/hiWorkflowApproveInfo/{type}/{businessId}")
    public WorkflowApproveInfoDto getHiWorkflowApproveInfo(@PathVariable("type") Short type,
                                                           @PathVariable("businessId") Long businessId) {
        return actWorkflowFeignClient.getHiWorkflowApproveInfo(type, businessId);
    }

    @ApiOperation(value = "我的流程")
    @GetMapping("/my")
    @AutoConvert
    public PageUtils<ActWorkflowRequestViewDto> pageMyRequest(ActWorkflowRequestQueryDto query) {
        PageUtils<ActWorkflowRequestViewDto> page = actWorkflowFeignClient.pageMyRequest(query);
        return page;
    }

    @ApiOperation(value = "首页-我的流程数量")
    @GetMapping("/my/count")
    public WorkflowCountDto countMyWorkflow() {
        return actWorkflowFeignClient.countMyWorkflow();
    }

    @ApiOperation(value = "抄送我的")
    @GetMapping("/copy/my")
    @AutoConvert
    public PageUtils<ActWorkflowCopyViewDto> pageMyCopy(ActWorkflowCopyQueryDto query) {
        PageUtils<ActWorkflowCopyViewDto> page = actWorkflowFeignClient.pageMyCopy(query);
        return page;
    }

    @ApiOperation(value = "抄送已读")
    @PutMapping("/copy/read/{processInstanceId}")
    public Boolean copyRead(@PathVariable("processInstanceId") String processInstanceId) {
        return actWorkflowFeignClient.copyRead(processInstanceId);
    }

    @ApiOperation(value = "抄送全部已读")
    @PutMapping("/copy/read/all")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean copyReadAll() {
        return actWorkflowFeignClient.copyReadAll();
    }

    @ApiOperation(value = "校验员工是否存在")
    @PostMapping("/verifyEmployeeExist")
    public EmployeeExistVerifyDto verifyEmployeeExist(@RequestBody WorkflowApproveDto approveDto) {
        EmployeeExistVerifyDto result = new EmployeeExistVerifyDto();
        String errorMsg = actWorkflowFeignClient.verifyEmployeeExist(approveDto);
        if (StrUtil.isNotBlank(errorMsg)) {
            result.setVerifyResult(Boolean.FALSE);
        } else {
            result.setVerifyResult(Boolean.TRUE);
        }
        result.setErrorMsg(errorMsg);
        return result;
    }
}
