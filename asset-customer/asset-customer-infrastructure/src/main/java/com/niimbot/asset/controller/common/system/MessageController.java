package com.niimbot.asset.controller.common.system;

import com.google.common.collect.ImmutableList;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.AssetService;
import com.niimbot.asset.service.feign.MessageFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetAppPageDto;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.message.HomePageMsg;
import com.niimbot.message.HomePageMsgV2;
import com.niimbot.message.UserMsgStat;
import com.niimbot.system.MessageDetailsPageQuery;
import com.niimbot.system.MessageDto;
import com.niimbot.system.MessageIsNoticeDto;
import com.niimbot.system.MessagePageDto;
import com.niimbot.system.MessageReadDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.ListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

/**
 * 消息模板配置控制器
 *
 * <AUTHOR>
 * @date 2021/4/19 15:25
 */
@Api(tags = "消息管理接口")
@RequestMapping("api/common/message")
@ResultController
@RequiredArgsConstructor
public class MessageController {

    private final AssetService assetService;

    private final MessageFeignClient messageFeignClient;

    @ApiOperation("分页获取消息列表")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<MessageDto> page(MessagePageDto dto) {
        dto.setClientType(ServletUtils.getClientSource().getValue());
        return messageFeignClient.page(dto);
    }

    @ApiOperation("全部已读")
    @PutMapping("/allRead/{businessType}")
    public Boolean allRead(@PathVariable(value = "businessType") Integer businessType) {
        return messageFeignClient.allRead(ServletUtils.getClientSource().getValue(), businessType);
    }

    @ApiOperation("未读消息数量")
    @GetMapping("/unread/count")
    public Integer unreadCount() {
        return messageFeignClient.unreadCount(ServletUtils.getClientSource().getValue());
    }

    @ApiOperation("获取最新一条要弹窗的运营公告")
    @GetMapping("/latestNotice")
    public MessageIsNoticeDto latestNotice() {
        return messageFeignClient.latestNotice(ServletUtils.getClientSource().getValue());
    }

    @ApiOperation(value = "获取消息列表")
    @GetMapping
    public List<MessageDto> getMessage() {
        return messageFeignClient.getMessage();
    }

    @ApiOperation(value = "站内信已读")
    @PutMapping("/read")
    @ResultMessage(value = ResultConstant.OPERATION_SUCCESS)
    public Boolean readMessage(@RequestBody @Validated MessageReadDto dto) {
        return messageFeignClient.readMessage(dto);
    }

    @ApiOperation(value = "获取消息详情")
    @GetMapping(value = "/getMessageDetail")
    public MessageDto getMessageDetail(@ApiParam(name = "messageId", value = "消息Id")
                                       @RequestParam(value = "messageId", required = false) String messageId,
                                       @ApiParam(name = "type", value = "消息类型")
                                       @RequestParam(value = "type", required = false) Integer type,
                                       @RequestParam(value = "businessType", defaultValue = "2") Integer businessType) {
        return messageFeignClient.getMessageDetail(messageId, type, businessType);
    }

    private final List<String> assetMessageCodes = ImmutableList.of(
            MessageConstant.Code.ZCLYDQTX.getCode(),
            MessageConstant.Code.ZCJYDQTX.getCode(),
            MessageConstant.Code.ZCNXDQTX.getCode(),
            MessageConstant.Code.ZCBXTX.getCode(),
            MessageConstant.Code.BYDQTX.getCode(),
            MessageConstant.Code.BYGQTX.getCode()
    );

    @ApiOperation("获取消息体原始参数信息")
    @GetMapping("/messageDataPage")
    public Map<String, Object> messageDataPage(@Validated MessageDetailsPageQuery query) {
        Map<String, Object> result = new HashMap<>(2);
        MessageDto forQuery = messageFeignClient.getMessageBodyForQuery(query.getMsgId());
        // 资产列表
        if (assetMessageCodes.contains(forQuery.getCode())) {
            result.put("type", "asset");
            if (forQuery.getRawMessageData().containsKey(MessageConstant.ExtConfig.ASSET_IDS)) {
                List<Long> assetIds = forQuery.getRawMessageData().getJSONArray(MessageConstant.ExtConfig.ASSET_IDS).toJavaList(Long.class);
                AssetQueryConditionDto dto = new AssetQueryConditionDto();
                dto.setPageSize(query.getPageSize());
                dto.setPageNum(query.getPageNum());
                dto.setAssetIds(assetIds);
                if (AssetConstant.TERMINAL_APP.equalsIgnoreCase(query.getTerminal())) {
                    PageUtils<AssetAppPageDto> assetAppPage = assetService.assetAppPage(dto);
                    result.put("pageResult", assetAppPage);
                }
                if (AssetConstant.TERMINAL_PC.equalsIgnoreCase(query.getTerminal())) {
                    PageUtils<JSONObject> assetPcPage = assetService.assetPcPage(dto);
                    result.put("pageResult", assetPcPage);
                }
            } else {
                result.put("pageResult", ListUtil.empty());
            }
            return result;
        }
        throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "消息类型");
    }

    @AutoConvert
    @ApiOperation("获取预警消息统计")
    @GetMapping("/getUserMsgStat")
    public List<UserMsgStat> getWarningMessage() {
        return messageFeignClient.getUserMessage(LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId());
    }

    @Deprecated
    @ApiOperation("首页最新一条消息")
    @GetMapping("/homePageMsg")
    public HomePageMsg homePageMsg() {
        return messageFeignClient.homePageMsg(LoginUserThreadLocal.getCurrentUserId());
    }

    @ApiOperation("首页最新消息")
    @GetMapping("/homePageMsg/v2")
    public HomePageMsgV2 homePageMsg(@RequestParam(value = "limit", required = false, defaultValue = "5") Integer limit) {
        return messageFeignClient.homePageMsgV2(limit);
    }

    @PostMapping("/manual")
    public Boolean manualScheduleMessage() {
        return messageFeignClient.schedule();
    }
}
