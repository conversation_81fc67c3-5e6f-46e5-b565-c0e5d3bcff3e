package com.niimbot.asset.service.feign.equipment;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.equipment.SiteInspectTaskAbnormalDto;
import com.niimbot.equipment.SiteInspectTaskAbnormalQryDto;
import com.niimbot.equipment.SiteInspectTaskDetailExportDto;
import com.niimbot.equipment.SiteInspectTaskEditExecutorsDto;
import com.niimbot.equipment.SiteInspectTaskHandleOrderDto;
import com.niimbot.equipment.SiteInspectTaskHandleRecordDto;
import com.niimbot.equipment.SiteInspectTaskInfoDto;
import com.niimbot.equipment.SiteInspectTaskPageDto;
import com.niimbot.equipment.SiteInspectTaskProjectDto;
import com.niimbot.equipment.SiteInspectTaskProjectQryDto;
import com.niimbot.equipment.SiteInspectTaskProjectSubmitDto;
import com.niimbot.equipment.SiteInspectTaskQueryDto;
import com.niimbot.equipment.SiteInspectTaskRangeDto;
import com.niimbot.equipment.SiteInspectTaskRangeQryDto;
import com.niimbot.equipment.SiteInspectTaskRangeStatisticsDto;
import com.niimbot.equipment.SiteInspectTaskStatisticsDto;
import com.niimbot.system.AuditableOperateResult;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:32
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentSiteInspectTaskFeignClient {

    // ==================================巡检任务===========================================
    @PostMapping("/server/equipment/siteInspect/task/page")
    PageUtils<SiteInspectTaskPageDto> page(SiteInspectTaskQueryDto query);

    @GetMapping("/server/equipment/siteInspect/task/info")
    SiteInspectTaskInfoDto info(@RequestParam("taskId") Long taskId,
                                @RequestParam(value = "full", required = false, defaultValue = "false") Boolean full);

    @GetMapping("/server/equipment/siteInspect/task/statistics")
    SiteInspectTaskStatisticsDto taskStatistics();

    @PostMapping("/server/equipment/siteInspect/task/close/{taskId}")
    Boolean closeTask(@PathVariable("taskId") Long taskId);

    @PostMapping("/server/equipment/siteInspect/task/cancel")
    List<AuditableOperateResult> cancelTask(List<Long> taskIds);

    @PostMapping("/server/equipment/siteInspect/task/edit/executors")
    List<AuditableOperateResult> editExecutors(SiteInspectTaskEditExecutorsDto editExecutorsList);

    @GetMapping("/server/equipment/siteInspect/task/detail/export")
    List<SiteInspectTaskDetailExportDto> detailExport(@RequestParam("taskId") Long taskId);

    // ==================================巡检路线===========================================

    @GetMapping("/server/equipment/siteInspect/task/range/page")
    PageUtils<SiteInspectTaskRangeDto> taskRangePage(@SpringQueryMap SiteInspectTaskRangeQryDto query);

    @GetMapping("/server/equipment/siteInspect/task/range/info")
    SiteInspectTaskRangeDto taskRangeInfo(@RequestParam("rangeId") Long rangeId);

    @GetMapping("/server/equipment/siteInspect/task/range/scan")
    SiteInspectTaskRangeDto rangeScan(@RequestParam("taskId") Long taskId, @RequestParam("pointId") Long pointId);

    @GetMapping("/server/equipment/siteInspect/task/range/statistics")
    SiteInspectTaskRangeStatisticsDto taskRangeStatistics(@RequestParam("taskId") Long taskId);

    @GetMapping("/server/equipment/siteInspect/task/range/handleRecord")
    SiteInspectTaskHandleRecordDto handleRecordInfo(@RequestParam("rangeId") Long rangeId);

    @GetMapping("/server/equipment/siteInspect/task/range/handle/ignore")
    AuditableOperateResult handleIgnore(@RequestParam("rangeId") Long rangeId);

    @GetMapping("/server/equipment/siteInspect/task/range/handle/check")
    void handleCheck(@RequestParam("rangeId") Long rangeId);

    @PostMapping("/server/equipment/siteInspect/task/range/handle/assetHandle")
    AuditableOperateResult assetHandle(SiteInspectTaskHandleOrderDto handleOrderDto);

    // ==================================巡检项目===========================================

    @GetMapping("/server/equipment/siteInspect/task/project/page")
    PageUtils<SiteInspectTaskProjectDto> projectPage(@SpringQueryMap SiteInspectTaskProjectQryDto query);

    @GetMapping("/server/equipment/siteInspect/task/project/info")
    SiteInspectTaskProjectDto projectInfo(@RequestParam("id") Long id);

    @GetMapping("/server/equipment/siteInspect/task/project/abnormal/page")
    PageUtils<SiteInspectTaskAbnormalDto> projectAbnormalPage(@SpringQueryMap SiteInspectTaskAbnormalQryDto query);

    @PostMapping("/server/equipment/siteInspect/task/project/submit")
    Boolean projectSubmit(SiteInspectTaskProjectSubmitDto submitDto);
}
