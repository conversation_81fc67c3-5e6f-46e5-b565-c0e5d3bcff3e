package com.niimbot.asset.controller.common.activiti;

import cn.hutool.core.util.StrUtil;
import com.niimbot.activiti.*;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.ActApproveRoleFeignClient;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.ThirdpartyFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.CompanySettingDto;
import com.niimbot.thirdparty.ThirdPartyDto;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Objects;

/**
 * 审批角色服务
 *
 * <AUTHOR>
 */
@Api(tags = "【审批流】审批角色管理")
@ResultController
@RequestMapping("api/common/approveRole")
@RequiredArgsConstructor
@Validated
public class ActApproveRoleController {

    private final ActApproveRoleFeignClient approveRoleFeignClient;

    private final CompanyFeignClient companyFeignClient;

    private final ThirdpartyFeignClient thirdpartyFeignClient;

    @ApiOperation(value = "审批角色新增")
    @PostMapping
    @RepeatSubmit
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean create(@RequestBody @Validated(Insert.class) ActApproveRoleDto dto) {
        return approveRoleFeignClient.create(dto);
    }

    @ApiOperation(value = "审批角色编辑")
    @PutMapping
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean update(@RequestBody @Validated(Update.class) ActApproveRoleDto dto) {
        return approveRoleFeignClient.update(dto);
    }

    @ApiOperation(value = "审批角色删除")
    @DeleteMapping
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean remove(@RequestBody @NotEmpty(message = "请选择要删除的审批角色") List<Long> ids) {
        return approveRoleFeignClient.remove(ids);
    }

    @ApiOperation(value = "审批角色分页")
    @GetMapping("/page")
    public PageUtils<ActApproveRoleDto> page(ActApproveRoleQueryDto dto) {
        PageUtils<ActApproveRoleDto> page = approveRoleFeignClient.page(dto);
        type(page.getList());
        return page;
    }

    private void type(List<ActApproveRoleDto> list) {
        // 是否开启了同步
        CompanySettingDto setting = companyFeignClient.selectCompanySettingById(LoginUserThreadLocal.getCompanyId());
        if (!setting.getExpandSwitch().getEnableSyncApproveRole()) {
            return;
        }
        // 钉钉环境
        Edition.ding(() -> list.forEach(v -> {
            if (StrUtil.isNotBlank(v.getExternalId())) {
                v.setType(2);
            }
        }));
        // saas环境
        if (Edition.isLocal() || Edition.isSaas()) {
            ThirdPartyDto bindInfo = thirdpartyFeignClient.getBindInfo(null);
            if (Objects.isNull(bindInfo) || !"DINGTALK".equalsIgnoreCase(bindInfo.getType())) {
                return;
            }
            list.forEach(v -> {
                if (StrUtil.isNotBlank(v.getExternalId())) {
                    v.setType(2);
                }
            });
        }
    }

    @ApiOperation(value = "审批角色列表")
    @GetMapping("/list")
    public List<ActApproveRoleDto> list(ActApproveRoleQueryDto dto) {
        List<ActApproveRoleDto> list = approveRoleFeignClient.list(dto);
        type(list);
        return list;
    }

    @ApiOperation(value = "审批角色成员新增")
    @PostMapping("/member")
    @RepeatSubmit
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean createMembers(@RequestBody @Validated ActApproveRoleMemberDto dto) {
        return approveRoleFeignClient.createMembers(dto);
    }

    @ApiOperation(value = "审批角色成员删除")
    @DeleteMapping("/member")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean removeMembers(@RequestBody @Validated ActApproveRoleMemberDto dto) {
        return approveRoleFeignClient.removeMembers(dto);
    }

    @ApiOperation(value = "审批角色成员分页")
    @GetMapping(value = "/member/page")
    public PageUtils<ActApproveRoleMemberResultDto> pageMember(@Validated ActApproveRoleMemberQueryDto dto) {
        return approveRoleFeignClient.pageMember(dto);
    }

    @ApiOperation(value = "审批角色成员列表")
    @GetMapping(value = "/member/list")
    public List<ActApproveRoleMemberResultDto> listMember(@Validated ActApproveRoleMemberQueryDto dto) {
        return approveRoleFeignClient.listMember(dto);
    }

    @ApiOperation(value = "角色列表获取角色成员列表")
    @PostMapping(value = "/member/listByRoleIds")
    public List<ActApproveRoleMemberResultDto> listByRoleIds(@RequestBody @NotEmpty List<Long> roleIds) {
        return approveRoleFeignClient.listByRoleIds(roleIds);
    }

    @ApiOperation("设置审批角色中人员的管理范围")
    @PostMapping("/member/orgScope")
    public Boolean memberOrgScope(@Validated @RequestBody ApproveMemberOrgScopeSetting setting) {
        return approveRoleFeignClient.memberOrgScope(setting);
    }

}
