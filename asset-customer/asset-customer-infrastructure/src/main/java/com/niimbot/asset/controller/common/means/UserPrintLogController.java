package com.niimbot.asset.controller.common.means;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.controller.BaseController;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.view.ExcelFieldExcelView;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.UserPrintLogFeignClient;
import com.niimbot.asset.utils.AsAssetUtil;
import com.niimbot.asset.utils.AsMaterialUtil;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.PrintAssetLogQueryDto;
import com.niimbot.means.UserPrintLogDto;
import com.niimbot.means.UserPrintLogViewDto;
import com.niimbot.system.FullPrintLogDto;
import com.niimbot.system.PrintLogQuery;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.View;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 打印日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Api(tags = "资产打印日志接口")
@ResultController
@RequestMapping("api/common/print/log")
@RequiredArgsConstructor
@Validated
public class UserPrintLogController extends BaseController {

    private final UserPrintLogFeignClient logFeignClient;

    private final FormFeignClient formFeignClient;

    private final AsAssetUtil assetUtil;

    private final DictConvertUtil dictConvertUtil;

    @ApiOperation(value = "资产日志列表")
    @GetMapping(value = "list")
    @AutoConvert
    public List<UserPrintLogViewDto> assetLogList(PrintAssetLogQueryDto dto) {
        setQueryDate(dto);
        List<UserPrintLogViewDto> res = logFeignClient.assetLogList(dto);
        res.forEach(UserPrintLogViewDto::convert);
        return res;
    }

    @ApiOperation(value = "资产日志分页")
    @GetMapping(value = "page")
    @AutoConvert
    public PageUtils<UserPrintLogViewDto> assetLogPage(PrintAssetLogQueryDto dto) {
        setQueryDate(dto);
        PageUtils<UserPrintLogViewDto> page = logFeignClient.assetLogPage(dto);
        page.getList().forEach(UserPrintLogViewDto::convert);
        return page;
    }

    @Resource
    private AsMaterialUtil asMaterialUtil;

    @ApiOperation("耗材与区域打印日志分页")
    @GetMapping("/full/page")
    @AutoConvert
    public PageUtils<FullPrintLogDto> materialLogPage(@Validated PrintLogQuery query) {
        PageUtils<FullPrintLogDto> page = logFeignClient.fullPage(query);
        page.getList().forEach(FullPrintLogDto::convert);
        if (query.getPrintType() == DictConstant.PRINT_TYPE_MATERIAL || query.getPrintType() == DictConstant.PRINT_TYPE_ASSET) {
            // 返回结果集
            page.getList().parallelStream().forEachOrdered(printDto -> {
                JSONObject data = printDto.getAssetData();
                if (query.getPrintType() == DictConstant.PRINT_TYPE_ASSET) {
                    FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
                    assetUtil.translateAssetJson(data, formVO.getFormFields());
                }
                if (query.getPrintType() == DictConstant.PRINT_TYPE_MATERIAL) {
                    FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_MATERIAL);
                    asMaterialUtil.translateMaterialJson(data, formVO.getFormFields());
                }
                printDto.setAssetData(data);
            });
        }
        return page;
    }

    @ApiOperation(value = "添加打印资产日志【异常暂停，打印如果报异常】")
    @PostMapping
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean addPrintLog(@RequestBody @NotEmpty List<@Valid UserPrintLogDto> logs) {
        return logFeignClient.addPrintLog(logs);
    }

    @ApiOperation(value = "根据打印日志ID查询资产快照数据")
    @GetMapping(value = "{printTaskId}/{assetId}")
    public JSONObject getPrintAssetSnapShot(@PathVariable("printTaskId") Long printTaskId,
                                            @PathVariable("assetId") Long assetId) {
        JSONObject snapShot = logFeignClient.getPrintAssetSnapShot(printTaskId, assetId);
        return snapShot;
    }

    @ApiOperation(value = "打印日志记录下载")
    @GetMapping("down")
    public View down(PrintAssetLogQueryDto dto) {
        List<UserPrintLogViewDto> list = this.assetLogList(dto);
        dictConvertUtil.convertToDictionary(list);
        return new ExcelFieldExcelView(UserPrintLogViewDto.class, "打印日志导出", list);
    }

    private void setQueryDate(PrintAssetLogQueryDto dto) {
        List<String> printTimes = dto.getPrintTimes();
        if (null == printTimes) {
            printTimes = new ArrayList<>(2);
            printTimes.add(dto.getPrintBegin());
            printTimes.add(dto.getPrintEnd());
            dto.setPrintTimes(printTimes);
        }
    }
}
