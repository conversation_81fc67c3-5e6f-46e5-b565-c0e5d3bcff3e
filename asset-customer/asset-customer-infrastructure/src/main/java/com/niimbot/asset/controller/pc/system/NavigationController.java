package com.niimbot.asset.controller.pc.system;

import com.niimbot.asset.service.feign.NavigationFeignClient;
import com.niimbot.asset.utils.AbstractFileUtils;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.AsNavigationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Collections;
import java.util.List;

/**
 * 导航栏控制器
 * <AUTHOR>
 * @date 2020/12/9 下午3:26
 */
@Api(tags = "导航栏管理")
@ResultController
@RequestMapping("api/pc/navigation")
@Validated
public class NavigationController {

    private final NavigationFeignClient navigationFeignClient;

    private final AbstractFileUtils fileUtils;

    @Autowired
    public NavigationController(NavigationFeignClient navigationFeignClient, AbstractFileUtils fileUtils) {
        this.navigationFeignClient = navigationFeignClient;
        this.fileUtils = fileUtils;
    }

    /**
     * 列表数据
     * 查询启用状态 - status = 1
     * 查询禁用状态 - status = 2
     *
     * @return 列表
     */
    @ApiOperation(value = "导航栏")
    @GetMapping(value = "/list")
    public List<AsNavigationDto> list() {
        List<AsNavigationDto> list = navigationFeignClient.list(Collections.singletonMap("status", 1));
        list.forEach(it -> {
            it.setNavIcon(fileUtils.convertToDownloadUrl(it.getNavIcon()));
        });
        return list;
    }
}
