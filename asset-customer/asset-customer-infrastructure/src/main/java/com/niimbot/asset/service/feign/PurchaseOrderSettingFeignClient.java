package com.niimbot.asset.service.feign;

import com.niimbot.purchase.PurchaseOrderTypeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface PurchaseOrderSettingFeignClient {

    @GetMapping(value = "server/purchase/order/setting/orderType/app/list")
    List<PurchaseOrderTypeDto> appListOrderTypeShow();

}
