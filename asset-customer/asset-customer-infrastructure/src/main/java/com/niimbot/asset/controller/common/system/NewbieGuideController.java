package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.service.feign.NewbieGuideFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.NewbieGuideDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "【企业新手指引】")
@ResultController
@RequestMapping("/api/common/newbie/guide")
public class NewbieGuideController {

    @Resource
    private NewbieGuideFeignClient newbieGuideFeignClient;

    @ApiOperation("点击引导页")
    @PutMapping("/click/{id}")
    public Boolean click(@PathVariable("id") Long id) {
        return newbieGuideFeignClient.click(id);
    }

    @ApiOperation("获取全部引导列表")
    @GetMapping("/all")
    public List<NewbieGuideDto> allGuide() {
        return newbieGuideFeignClient.allGuide();
    }

}
