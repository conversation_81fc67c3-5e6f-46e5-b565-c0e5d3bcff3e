package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.service.feign.SaleOrderFeignClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 销售单前端控制器
 *
 * <AUTHOR>
 * @date 2021/8/9 14:23
 */
@Slf4j
@RestController
@RequestMapping("api/common/sale/order/notify")
@RequiredArgsConstructor
public class SaleOrderNotifyController {
    private final SaleOrderFeignClient saleOrderFeignClient;

    /**
     * 支付回调地址
     *
     * @param event
     * @return
     */
    @PostMapping
    public String notify(@RequestBody String event) {
        log.info("received notify {}", event);
        return saleOrderFeignClient.notify(event);
    }

}
