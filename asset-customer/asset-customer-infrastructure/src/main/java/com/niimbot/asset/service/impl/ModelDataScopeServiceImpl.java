package com.niimbot.asset.service.impl;

import com.niimbot.framework.dataperm.core.model.ModelDataScope;
import com.niimbot.framework.dataperm.core.model.ModelDataScopeService;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.collection.ListUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/4/8 16:50
 */
@Service
@RequiredArgsConstructor
public class ModelDataScopeServiceImpl implements ModelDataScopeService {

    @Override
    public List<ModelDataScope> loadDataScope() {
        // ignore
        return ListUtil.empty();
    }
}
