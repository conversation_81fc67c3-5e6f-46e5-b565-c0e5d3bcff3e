package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.service.UserFeedBackService;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.asset.utils.DingTalkUtil;

import org.apache.commons.io.Charsets;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserFeedBackServiceImpl implements UserFeedBackService {

    @Resource
    private DingTalkUtil dingTalkUtil;

    @Resource
    private CusUserFeignClient cusUserFeignClient;

    /**
     * 接收用户反馈转发钉钉群
     *
     * @param request 请求数据
     * @return 分页结果
     */
    @Override
    public Boolean userFeedback(HttpServletRequest request) throws IOException {
        String read = IoUtil.read(request.getInputStream(), Charsets.UTF_8);
        // json字符串转对象
        JSONObject jsonObject = JSON.parseObject(read);
        System.out.println("接收用户反馈信息event = " + jsonObject);
        if (ObjectUtil.isEmpty(jsonObject)) {
            log.info("接收用户反馈信息为空");
            return false;
        }

        // 对异步通知做处理
        String type = jsonObject.getString("type");
        String key = "post";
        if (type.contains("reply")) {
            key = "reply";
        }

        // 回复消息只需要更新帖子的消息
        if ("reply.created".equals(type)) {
            return true;
        }

        String typeName = getTypeName(type);
        Long userId = 0L;
        String content = "";
        String createdAt = "";
        String feedbackDetailUrl = "";
        if (jsonObject.containsKey("payload") && ObjectUtil.isNotEmpty(jsonObject.getJSONObject("payload"))
            && ObjectUtil.isNotEmpty(jsonObject.getJSONObject("payload").getJSONObject(key))) {
            JSONObject postData = jsonObject.getJSONObject("payload").getJSONObject(key);
            content = postData.getString("content");
            createdAt = postData.getString("created_at");
            feedbackDetailUrl = postData.getString("post_url");

            if (ObjectUtil.isEmpty(feedbackDetailUrl)) {
                feedbackDetailUrl = "";
            }

            // 获取userId
            if (postData.containsKey("user") && ObjectUtil.isNotEmpty(postData.getJSONObject("user"))
                    && ObjectUtil.isNotEmpty(postData.getJSONObject("user").getString("openid"))) {
                userId = Convert.toLong(postData.getJSONObject("user").getString("openid"), -1L);
            }

            // 更新成回复的时间和内容
            if ("post.updated".equals(type)) {
                JSONObject repliesAll = postData.getJSONObject("replies_all");
                for (Map.Entry<String, Object> entry : repliesAll.entrySet()) {
                    String k = entry.getKey();
                    Object v = entry.getValue();
                    JSONObject v1 = (JSONObject) v;
                    if (v1.containsKey("self") && ObjectUtil.isNotEmpty(v1.getJSONObject("self"))) {
                        JSONObject self = v1.getJSONObject("self");
                        String time = self.getString("time");
                        if (ObjectUtil.isNotEmpty(time) && "现在".equals(time)) {
                            content = self.getString("content");
                            createdAt = self.getString("created_at");
                        }
                    }
                }
            }
        }
        if (ObjectUtil.isEmpty(userId)) {
            log.error("用户id为空");
            return false;
        }

        // 获取用户的账号和手机号
        CusUserDto cusUserDto = cusUserFeignClient.getById(userId);
        if (ObjectUtil.isEmpty(cusUserDto)) {
            log.error("用户信息不存在");
            return false;
        }
        String account = cusUserDto.getAccount() + "+" + cusUserDto.getMobile();
        // 留言内容
        String dingMsg = "【反馈类型】：" + typeName + "，\n" +
                "【反馈内容】：" + content + "，\n" +
                "【反馈时间】：" + createdAt + "，\n" +
                "【用户账号】：" + account + "，\n" +
                "【反馈详情】：" + feedbackDetailUrl;

        // 发送钉钉消息
        return dingTalkUtil.sendTxt(dingMsg);
    }

    private String getTypeName(String type) {
        String typeCn;
        switch (type) {
            case "post.created":
                // 业务代码
                typeCn = "新的帖子";
                break;
            case "post.updated":
                // 业务代码
                typeCn = "更新帖子";
                break;
            case "reply.created":
                // 业务代码
                typeCn = "新的回复";
                break;
            case "reply.updated":
                // 业务代码
                typeCn = "更新回复";
                break;
            default:
                typeCn = "";
        }
        return typeCn;
    }

}
