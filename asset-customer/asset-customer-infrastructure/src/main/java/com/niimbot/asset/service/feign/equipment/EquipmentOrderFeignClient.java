package com.niimbot.asset.service.feign.equipment;

import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2023/10/11 15:49
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface EquipmentOrderFeignClient {

    @GetMapping("server/equipment/order/sortField/{orderType}")
    QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType);


    @GetMapping(value = "server/equipment/order/form/{orderType}")
    FormVO getForm(@PathVariable("orderType") Integer orderType);

}
