package com.niimbot.asset.service.feign;

import com.niimbot.means.OrderFieldDto;
import com.niimbot.means.OrderTypeDto;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:22
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface OrderSettingFeignClient {

    /**
     * 单据类型详情
     * @param id
     * @return
     */
    @GetMapping(value = "server/means/orderSetting/orderType/getById/{id}")
    OrderTypeDto getOrderTypeById(@PathVariable("id") Long id);

    /**
     * 单据类型查询
     * @return
     */
    @GetMapping(value = "server/means/orderSetting/orderType/list")
    List<OrderTypeDto> listOrderType();

    /**
     * app单据类型查询
     * @return
     */
    @GetMapping(value = "server/means/orderSetting/orderType/app/list")
    List<OrderTypeDto> appListOrderTypeShow();

    /**
     * 单据字段查询
     * @param orderType
     * @return
     */
    @GetMapping(value = "server/means/orderSetting/orderField/list/{orderType}")
    List<OrderFieldDto> listOrderField(@PathVariable("orderType") Integer orderType);

    /**
     * 表单字段/列表字段列表
     * @param orderType
     * @return
     */
    @GetMapping(value = "server/means/orderSetting/dynamicField/list/{orderType}")
    List<OrderFieldDto> listDynamicOrderField(@PathVariable("orderType") Integer orderType);

    /**
     * 表单字段/列表字段列表
     * @return
     */
    @GetMapping(value = "server/means/orderSetting/dynamicField/listAll")
    List<OrderFieldDto> listAllDynamicOrderField();

    /**
     * 更新单据类型
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "server/means/orderSetting/updateOrderType")
    Boolean updateOrderType(OrderTypeDto dto);

    /**
     * 更新单据字段
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "server/means/orderSetting/updateOrderField")
    Boolean updateOrderField(OrderFieldDto dto);

    /**
     * 批量更新单据字段
     * @param dtos
     * @return 结果
     */
    @PutMapping(value = "server/means/orderSetting/updateOrderField/batch")
    Boolean updateOrderFieldBatch(List<OrderFieldDto> dtos);

    /**
     * 排序单据字段
     * @param fieldIds
     * @return 结果
     */
    @PutMapping(value = "server/means/orderSetting/sortFields")
    Boolean sortFields(List<Long> fieldIds);

    /**
     * 此类型是否开启审批流
     *
     * @param orderType 单据类型
     * @return 是否开启审批流
     */
    @GetMapping(value = "server/means/orderSetting/orderType/enableWorkflow/{orderType}")
    Boolean enableWorkflow(@PathVariable("orderType") Integer orderType);
}
