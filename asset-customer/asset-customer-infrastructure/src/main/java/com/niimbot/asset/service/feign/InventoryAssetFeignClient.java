package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.inventory.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/12 11:50
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface InventoryAssetFeignClient {
    /**
     * 分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/page")
    PageUtils<InventoryAssetPcDto> page(InventorySurplusQueryDto dto);

    /**
     * 损益处理盘亏分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/plPage")
    PageUtils<InventoryAssetPcDto> plPage(InventorySurplusQueryDto dto);

    /**
     * 全部资产数据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/listAll")
    List<InventoryAssetListDto> assetListAll(InventorySurplusQueryDto dto);

    /**
     * 盘点资产头部统计
     *
     * @param dto
     * @return 结果
     */
    @GetMapping(value = "server/inventory/asset/count")
    InventoryAssetCountDto assetCount(@SpringQueryMap InventoryAssetCountQueryDto dto);

    /**
     * PC盘点资产盘亏处理
     *
     * @param pDto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/assetHandle")
    InventoryAssetHandleDto assetHandle(@RequestBody InventoryAssetHandleDto pDto);

    @PostMapping(value = "server/inventory/asset/assetChangeRollback")
    void assetChangeRollback(@RequestBody InventoryAssetHandleDto pDto);

    /**
     * 查询资产详情快照
     *
     * @param id 盘点资产表Id
     * @return 资产详情集合
     */
    @GetMapping(value = "server/inventory/asset/detail/{id}")
    InventoryAssetDto getAssetDetail(@PathVariable("id") Long id);

    /**
     * 分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/appPage")
    PageUtils<InventoryAssetListDto> appPage(InventorySurplusQueryDto dto);

    @GetMapping(value = "server/inventory/asset/scan/ref")
    Map<String, String> inventoryAssetScanRef(@RequestParam(value = "inventoryId", required = false) Long inventoryId,
                                              @RequestParam("taskId") Long taskId);

    /**
     * APP非当前盘点人已盘资产列表
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "server/inventory/asset/otherUser")
    OtherUserInventoryAssetDto otherUser(@SpringQueryMap InventoryAssetOtherUserDto dto);

    /**
     * 编辑上报资产
     *
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "server/inventory/asset")
    Boolean editAsset(InventoryAssetEditDto dto);

    /**
     * 已修改资产列表分页单据
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/modifiedPage")
    PageUtils<InventoryAssetPcDto> modifiedPage(InventorySurplusQueryDto dto);

    /**
     * PC损益处理-已拍照资产
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/takePhotoPage")
    PageUtils<InventoryAssetPcDto> takePhotoPage(InventorySurplusQueryDto dto);

    /**
     * 已修改资产列表单据-不分页
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/modifiedPageList")
    List<InventoryAssetPcDto> modifiedPageList(InventorySurplusQueryDto dto);

    /**
     * 盘点资产详情
     *
     * @param id 盘点任务id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/asset/info/{id}")
    InventoryAssetPcDto getInfo(@PathVariable("id") Long id);

    /**
     * 盘点资产详情
     *
     * @param id 盘点任务id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/assetReport/info/{id}")
    InventorySurplusDto getSurplusInfo(@PathVariable("id") Long id);

    /**
     * PC盘点资产盘亏处理-校验资产是否能盘亏处理
     *
     * @param ids 盘点资产ids
     * @return 结果
     */
    @PutMapping(value = "server/inventory/asset/checkAssetLoss")
    Boolean checkAssetLoss(@RequestBody List<Long> ids);

    @PutMapping(value = "server/inventory/asset/checkAssetChange")
    Boolean checkAssetChange(@RequestBody List<Long> ids);

    /**
     * 判断资产id是否在盘点单内
     *
     * @param assetId     盘点资产id
     * @param inventoryId 盘点单id
     * @return 结果
     */
    @GetMapping(value = "server/inventory/asset/checkAssetId")
    Boolean checkAssetId(
            @RequestParam("assetId") Long assetId, @RequestParam("inventoryId") Long inventoryId);

    /**
     * 历史盘点单资产id列表
     *
     * @param dto
     * @return
     */
    @GetMapping("server/inventory/asset/historyInventoryAssetIds")
    List<Long> historyInventoryAssetIds(@SpringQueryMap InventoryHistoryQueryDto dto);


    /**
     * 修改盘点备注
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/updateRemark")
    Boolean updateRemark(@RequestBody InventoryRemarkDto dto);

    /**
     * 修改盘点照片
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping(value = "server/inventory/asset/updatePicture")
    Boolean updatePicture(@RequestBody InventoryPictureDto dto);

}
