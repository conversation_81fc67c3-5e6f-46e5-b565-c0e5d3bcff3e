package com.niimbot.asset.controller.app.system;

import com.niimbot.asset.annotation.LoginRecord;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.SsoExceptionUtils;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.jf.core.result.Result;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * APP登录模块
 *
 * <AUTHOR>
 * @since 2020/11/13 18:37
 */
@Slf4j
@Api(tags = {"手机登录接口"})
@RestController
@RequestMapping("api/app")
public class CustomAppLoginController {

    @Autowired
    private CusLoginService loginService;

    /**
     * 社交登录
     *
     * @param provider 社交平台: QQ ,WEIXIN
     * @param appId    社交平台AppId
     * @param code     社交平台授权码
     * @param pushId   极光推送id
     * @return token
     */
    @LoginRecord
    @PostMapping("/login/social")
    @ApiOperation(value = "社交登录", notes = "社交登录")
    public Result loginBySocial(
            @ApiParam(name = "provider", value = "社交平台: QQ ,WEIXIN", required = true)
            @RequestParam("provider") String provider,
            @ApiParam(name = "appId", value = "社交平台AppId", required = true)
            @RequestParam("appId") String appId,
            @ApiParam(name = "code", value = "社交平台授权码", required = true)
            @RequestParam("code") String code,
            @ApiParam(name = "pushId", value = "极光推送唯一id")
            @RequestParam(value = "pushId", required = false) String pushId) {
        Map<String, Object> map = new HashMap<>();
        try {
            String token = loginService.loginBySocial(provider, appId, code,
                    AssetConstant.TERMINAL_APP, pushId);
            map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
            return Result.ofSuccess(map);
        } catch (BusinessException e) {
            if (SsoExceptionUtils.NOT_REGISTER_CODE != e.getCode()) {
                throw e;
            }
            return Result.ofSuccess(map, HttpStatus.OK.getReasonPhrase(), SsoExceptionUtils.NOT_REGISTER_CODE);
        }
    }
}
