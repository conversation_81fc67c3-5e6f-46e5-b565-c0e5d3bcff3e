package com.niimbot.asset.controller.pc.finance;

import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.service.feign.finance.DepreciationConfigFeignClient;
import com.niimbot.finance.DepreciationConfigDto;
import com.niimbot.finance.DisableDepreciationConfigDto;
import com.niimbot.finance.OrgSettleInfoDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/2/15 下午2:50
 */
@Api(tags = "折旧方案配置")
@ResultController
@RequestMapping("api/finance/depreciation/")
@RequiredArgsConstructor
public class DepreciationConfigController {

    private final DepreciationConfigFeignClient depreciationConfigFeignClient;

    @PostMapping("config")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    @ApiOperation(value = "折旧方案配置")
    public Boolean saveConfig(@RequestBody @Validated(Insert.class) DepreciationConfigDto configDto) {
        return depreciationConfigFeignClient.saveConfig(configDto);
    }

    @ApiOperation(value = "编辑折旧方案配置")
    @PostMapping(value = "edit")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean modifyConfig(@RequestBody @Validated(Update.class) DepreciationConfigDto configDto) {
        return depreciationConfigFeignClient.modifyConfig(configDto);
    }

    @ApiOperation("折旧方案配置详情")
    @GetMapping("{orgId}")
    public DepreciationConfigDto detail(@PathVariable Long orgId) {
        return depreciationConfigFeignClient.detail(orgId);
    }

    @ApiOperation("获取反启用数据")
    @GetMapping("querySettleInfo/{orgId}")
    public OrgSettleInfoDto querySettleInfo(@PathVariable Long orgId) {
        return depreciationConfigFeignClient.querySettleInfo(orgId);
    }

    @ApiOperation("反启用获取验证码")
    @GetMapping("verificationCode")
    public Boolean verificationCode() {
        return depreciationConfigFeignClient.verificationCode();
    }

    @ApiOperation("反启用")
    @PostMapping("removeConfig")
    @ResultMessage(ResultConstant.DISABLE_SUCCESS)
    public Boolean removeConfig(@RequestBody @Validated DisableDepreciationConfigDto configDto) {
        return depreciationConfigFeignClient.removeConfig(configDto);
    }
}
