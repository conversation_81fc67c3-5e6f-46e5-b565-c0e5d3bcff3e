package com.niimbot.asset.service.feign.finance;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.finance.*;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.HeadImportErrorDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 资产入账
 * <AUTHOR>
 * @date 2023/2/15 下午4:37
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface AssetFinanceInfoFeignClient {

    /**
     * 获取资产入账默认值信息
     * @param assetId
     * @return
     */
    @GetMapping(value = "server/finance/bill/defaultValue/{assetId}")
    AssetFinanceDefaultDto assetDefaultValue(@PathVariable(value = "assetId") Long assetId);

    /**
     * 保存资产入账信息
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/config")
    Boolean saveConfig(@RequestBody AssetFinanceInfoConfigDto configDto);

    /**
     * 保存导入资产入账信息
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/saveImportData")
    Boolean saveImportData(@RequestBody AssetFinanceImportDto configDto);

    /**
     * 获取资产入账信息详情
     * @param bizCode
     * @return
     */
    @GetMapping(value = "server/finance/bill/detail/{bizCode}")
    AssetFinanceInfoConfigDto detail(@PathVariable(value = "bizCode") String bizCode);

    /**
     * 编辑资产入账信息
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/edit")
    Boolean editConfig(@RequestBody AssetFinanceInfoConfigDto configDto);

    /**
     * 变更资产入账信息
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/alter")
    Boolean alterConfig(@RequestBody AssetFinanceInfoConfigDto configDto);

    /**
     * 资产变更日志分页列表
     * @param queryDto
     * @return
     */
    @GetMapping(value = "server/finance/bill/pageAlterLog")
    PageUtils<AssetFinanceAlterLogDto> pageAlterLog(@SpringQueryMap AlterLogQueryDto queryDto);

    /**
     * 反入账
     * @param configDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/resetConfig")
    Boolean resetConfig(@RequestBody AssetFinanceInfoConfigDto configDto);

    /**
     * 查询待入账资产
     * @param queryDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/queryAssetWait")
    List<List<Object>> queryAssetWait(@RequestBody AssetFinanceInfoQueryDto queryDto);

    /**
     * 入账列表查询
     * @param queryDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/queryFinanceInfo")
    PageUtils<AssetFinanceInfoDto> queryFinanceInfo(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto);

    /**
     * 资产入账初始列表
     * @param queryDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/queryInitFinanceInfo")
    PageUtils<AssetInitFinanceInfoDto> queryInitFinanceInfo(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto);

    /**
     * 资产财务台账
     * @param queryDto
     * @return
     */
    @PostMapping(value = "server/finance/bill/queryMachineAccount")
    PageUtils<AssetMachineAccountDto> queryMachineAccount(@RequestBody @Validated AssetFinanceInfoQueryDto queryDto);

    /**
     * 保存excel导入表头数据
     * @param importErrorDto
     */
    @PostMapping(value = "server/finance/bill/saveSheetHead")
    void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto);

    /**
     * 查询错误信息
     * @param taskId
     * @return
     */
    @GetMapping(value = "server/finance/bill/importError/{taskId}")
    List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId);
}
