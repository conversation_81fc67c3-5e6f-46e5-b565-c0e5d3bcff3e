package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.SaleOrderFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.sale.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 销售单前端控制器
 *
 * <AUTHOR>
 * @date 2021/8/9 14:23
 */
@Slf4j
@Api(tags = "【服务中心】订单管理")
@ResultController
@RequestMapping("api/common/sale/order")
@RequiredArgsConstructor
public class SaleOrderController {

    private final SaleOrderFeignClient saleOrderFeignClient;

    @ApiOperation(value = "订单记录")
    @PostMapping("/page")
    @AutoConvert
    public PageUtils<SaleOrderDto> page(@RequestBody SaleOrderPageQueryDto query) {
        return saleOrderFeignClient.page(query);
    }

    @ApiOperation(value = "订单详情")
    @GetMapping("/{id}")
    @AutoConvert
    public SaleOrderDetailDto getById(@PathVariable("id") Long id) {
        return saleOrderFeignClient.getById(id);
    }

    @ApiOperation(value = "取消订单")
    @PutMapping("/close/{id}")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public Boolean closeOrder(@PathVariable("id") Long id) {
        return saleOrderFeignClient.closeOrder(id);
    }

    @ApiOperation(value = "重新发起支付")
    @RepeatSubmit
    @PutMapping("/repay")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public SaleOrderPayInfoDto repay(@RequestBody @Validated SaleOrderRepayDto repayDto) {
        return saleOrderFeignClient.repay(repayDto);
    }

    @ApiOperation(value = "查询订单支付状态")
    @GetMapping("/getPayStatus/{id}")
    public SaleOrderPaidDto getPayStatus(
            @ApiParam(name = "id", value = "订单id")
            @PathVariable("id") Long id) {
        return saleOrderFeignClient.getPayStatus(id);
    }

    @ApiOperation(value = "【资源包】创建订购资源包订单")
    @RepeatSubmit
    @PostMapping("/resourcePack")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public SaleOrderPayInfoDto createResourcePackOrder(@RequestBody @Validated SaleOrderResourcePackCreateDto createDto) {
        return saleOrderFeignClient.createResourcePackOrder(createDto);
    }

    @ApiOperation(value = "可开票金额")
    @GetMapping("/invoiceAmount")
    public BigDecimal queryInvoiceAmount() {
        return saleOrderFeignClient.queryInvoiceAmount();
    }
}
