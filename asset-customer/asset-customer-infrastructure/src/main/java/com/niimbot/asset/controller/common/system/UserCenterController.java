package com.niimbot.asset.controller.common.system;

import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.UserFeedBackService;
import com.niimbot.asset.service.feign.AccountCenterFeignClient;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.CusEmployeeFeignClient;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AccountDto;
import com.niimbot.system.SalesOwnerDto;
import com.niimbot.system.UserCenterAPPDto;
import com.niimbot.system.UserCenterPCDto;
import com.niimbot.system.UserPasswordDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.util.Objects;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import cn.hutool.extra.spring.SpringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import static cn.hutool.core.util.StrUtil.isNotBlank;

/**
 * 用户中心
 *
 * <AUTHOR>
 * @Date 2021/6/1
 */
@Slf4j
@Api(tags = {"App用户中心接口"})
@ResultController
@RequestMapping("api/common/userCenter")
public class UserCenterController {
    @Resource
    private CusUserFeignClient userFeignClient;
    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;
    @Resource
    private CusEmployeeFeignClient employeeFeignClient;
    @Resource
    private UserFeedBackService userFeedBackService;
    @Resource
    private AccountCenterFeignClient accountCenterFeignClient;
    @Resource
    private CompanyFeignClient companyFeignClient;

    /**
     * 修改密码
     *
     * @param dto dto
     * @return 成功与否
     */
    @ApiOperation(value = "设置密码/修改密码，【设置密码、忘记密码时oldPassword可不传】")
    @PutMapping("/changePassword")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changePassword(@RequestBody @Validated UserPasswordDto dto) {
        String newPassword = SecurityUtils.decryptPassword(dto.getNewPassword());
        String rePassword = SecurityUtils.decryptPassword(dto.getRePassword());
        String oldPassword = SecurityUtils.decryptPassword(dto.getOldPassword());
        String rawPassword = userFeignClient.checkPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, rawPassword)) {
            throw new BusinessException(SystemResultCode.USER_OLD_PASSWORD_ERROR);
        }
        if (!StringUtils.equals(newPassword, rePassword)) {
            throw new BusinessException(SystemResultCode.USER_PASSWORD_CONFIRM_ERROR);
        }
        return userFeignClient.changeCurrentUserPassword(newPassword);
    }

    /**
     * 更换邮箱【第二步】
     *
     * @param dto 更换验证码实体
     * @return 成功true、失败false
     */
    @ApiOperation(value = "更换邮箱【第二步】")
    @PostMapping("/changeEmail")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean changeEmail(@RequestBody UserCenterPCDto.ChangeEmailDTO dto) {
        if (!smsCodeFeignClient.checkSmsCode(dto.getNewEmail(), dto.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        CusUserDto cusUser = SpringUtil.getBean("tokenService", AbstractTokenService.class)
                .getLoginUser(ServletUtils.getRequest()).getCusUser();
        if (cusUser != null) {
            AccountDto account = accountCenterFeignClient.getAccountById(cusUser.getAccountId());
            // CusEmployeeDto info = employeeFeignClient.getInfo(cusUser.getId());
            if (Objects.equals(dto.getNewEmail(), account.getEmail())) {
                throw new BusinessException(SystemResultCode.USER_EMAIL_CHANGE_NEW_SAME);
            }
        }
        AccountDto accountByWay2 = accountCenterFeignClient.getAccountByWay2(dto.getNewEmail());
        // CusEmployeeDto employeeDto = employeeFeignClient.checkEmail(dto.getNewEmail());
        if (accountByWay2 != null) {
            throw new BusinessException(SystemResultCode.USER_CHANGE_EMAIL_ERROR);
        }
        return employeeFeignClient.changeEmail(dto.getNewEmail());
    }

    /**
     * 校验旧邮箱
     *
     * @param oldEmail 旧邮箱
     * @param smsCode  验证码
     * @return 校验邮箱是否正确
     */
    @ApiOperation(value = "更换邮箱【第一步】、校验旧邮箱")
    @GetMapping("/verifyOldEmail")
    public Boolean confirmOldEmail(@NotBlank(message = "请输入邮箱")
                                   @Pattern(regexp = com.niimbot.validate.ValidationUtils.EMAIL_REG, message = "邮箱格式不正确")
                                   @Size(max = 255, message = "邮箱不得超过255个字符") String oldEmail,
                                   @Size(min = 4, max = 4, message = "验证码为4位")
                                   @NotBlank(message = "验证码不能为空") String smsCode) {
        if (!smsCodeFeignClient.checkSmsCode(oldEmail, smsCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        CusUserDto cusUser = SpringUtil.getBean("tokenService", AbstractTokenService.class)
                .getLoginUser(ServletUtils.getRequest()).getCusUser();
        if (cusUser != null) {
            AccountDto account = accountCenterFeignClient.getAccountById(cusUser.getAccountId());
            // CusEmployeeDto info = employeeFeignClient.getInfo(cusUser.getId());
            if (!Objects.equals(oldEmail, account.getEmail())) {
                throw new BusinessException(SystemResultCode.USER_EMAIL_CHANGE_OLD);
            }
        }
        return true;
    }

    @ApiOperation(value = "绑定邮箱")
    @GetMapping("/bindEmail")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean bindEmail(@NotBlank(message = "请输入邮箱")
                             @Pattern(regexp = com.niimbot.validate.ValidationUtils.EMAIL_REG, message = "邮箱格式不正确")
                             @Size(max = 255, message = "邮箱不得超过255个字符") String email,
                             @Size(min = 4, max = 4, message = "验证码为4位") String smsCode) {
        if (!smsCodeFeignClient.checkSmsCode(email, smsCode)) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        // CusEmployeeDto employeeDto = employeeFeignClient.checkEmail(email);
        AccountDto account = accountCenterFeignClient.getAccountByWay2(email);
        if (account != null) {
            throw new BusinessException(SystemResultCode.USER_CHANGE_EMAIL_ERROR);
        }
        return userFeignClient.bindEmail(email);
    }

    @ApiOperation(value = "设置密码")
    @PutMapping("/setPassword")
    @ResultMessage(ResultConstant.EDIT_SUCCESS)
    public Boolean setPassword(@RequestBody @Validated(UserCenterAPPDto.SetPassword.class) UserCenterAPPDto dto) {
        String newPassword = SecurityUtils.decryptPassword(dto.getNewPassword());
//        ValidationUtils.checkPassword(SystemResultCode.USER_PASSWORD_ERROR, newPassword);
        String rawPassword = userFeignClient.checkPassword();
        if (isNotBlank(rawPassword)) {
            throw new BusinessException(SystemResultCode.USER_PASSWORD_ALREADY_SET);
        }
        return userFeignClient.changeCurrentUserPassword(newPassword);
    }

    @ApiOperation(value = "接收用户反馈转发钉钉群")
    @PostMapping("/userFeedback")
    public Boolean userFeedback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        return userFeedBackService.userFeedback(request);
    }

    @ApiOperation(value = "负责人信息")
    @GetMapping("/salesOwner")
    public SalesOwnerDto getSalesOwner() {
        return companyFeignClient.getSalesOwner();
    }

}
