package com.niimbot.asset.controller.common.sale;

import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.feign.CompanyIncomeExpensesFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.sale.CompanyIncomeExpensesDto;
import com.niimbot.sale.CompanyIncomeExpensesQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @since 2021/12/30 16:01
 */
@Api(tags = "【服务中心】收支明细")
@ResultController
@RequestMapping("api/common/incomeExpenses")
@RequiredArgsConstructor
public class CompanyIncomeExpensesController {

    private final CompanyIncomeExpensesFeignClient companyIncomeExpensesFeignClient;

    @ApiOperation(value = "明细记录")
    @GetMapping("/page")
    @AutoConvert
    public PageUtils<CompanyIncomeExpensesDto> page(CompanyIncomeExpensesQueryDto query) {
        return companyIncomeExpensesFeignClient.page(query);
    }

}
