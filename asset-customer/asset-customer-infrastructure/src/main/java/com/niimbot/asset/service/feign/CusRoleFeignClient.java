package com.niimbot.asset.service.feign;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.CusMenuRoleDto;
import com.niimbot.system.CusRoleAccountDto;
import com.niimbot.system.CusRoleAccountQueryDto;
import com.niimbot.system.CusRoleDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/11/17 17:45
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CusRoleFeignClient {

    /**
     * 查询角色列表list集合
     *
     * @return 角色列表
     */
    @GetMapping(value = "server/system/cusRole/list")
    List<CusRoleDto> list();

    /**
     * 查询角色账户列表list集合
     *
     * @param roleAccountQueryDto 查询
     * @return 角色账户列表
     */
    @GetMapping(value = "server/system/cusRole/roleAccountList")
    List<CusRoleAccountDto> roleAccountList(@SpringQueryMap CusRoleAccountQueryDto roleAccountQueryDto);

    /**
     * 查询角色分页列表
     *
     * @param params 查询参数
     * @return 角色列表
     */
    @GetMapping(value = "server/system/cusRole/page")
    PageUtils<CusRoleDto> page(@RequestParam Map<String, Object> params);

    /**
     * 根据角色Id查询角色详情
     *
     * @param roleId 角色Id
     * @return 角色数据
     */
    @GetMapping(value = "server/system/cusRole/{roleId}")
    CusRoleDto getInfo(@PathVariable("roleId") Long roleId);

    /**
     * 批量删除角色数据
     *
     * @param roleIds 需要删除的角色id
     * @return 结果
     */
    @DeleteMapping(value = "server/system/cusRole")
    Boolean delete(List<Long> roleIds);

    /**
     * 新增保存角色数据
     *
     * @param cusRoleDto 角色数据
     * @return 结果
     */
    @PostMapping(value = "server/system/cusRole")
    Boolean add(CusRoleDto cusRoleDto);

    /**
     * 修改保存角色数据
     *
     * @param cusRoleDto 角色数据
     * @return 结果
     */
    @PutMapping(value = "server/system/cusRole")
    List<String> edit(CusRoleDto cusRoleDto);

    /**
     * 查询角色权限
     *
     * @param roleId 角色Id
     * @return 结果
     */
    @GetMapping(value = "server/system/cusRole/setting/{roleId}")
    CusMenuRoleDto getSetting(@PathVariable("roleId") Long roleId);


    /**
     * 根据用户Id查询组织详情
     *
     * @param userId 用户Id
     * @return 组织数据
     */
    @GetMapping(value = "server/system/cusRole/user/{userId}")
    List<CusRoleDto> getRoleByUserId(@PathVariable("userId") Long userId);

    /**
     * 根据角色Id查询用户
     *
     * @param roleId 用户Id
     * @return 用户信息
     */
    @GetMapping(value = "server/system/cusRole/user")
    List<CusUserDto> listUserByRoleId(@RequestParam(value = "roleId") Long roleId);

    /**
     * 转让超管，修改用户角色对应关系
     */
    @PutMapping("server/system/cusRole/supertubeTransfer")
    Boolean supertubeTransfer(@RequestBody Map<String, Long> data);

    /**
     * 设置为默认角色
     * @param roleId
     * @return
     */
    @PostMapping("server/system/cusRole/configDefault")
    Boolean configDefault(@RequestBody Long roleId);
}
