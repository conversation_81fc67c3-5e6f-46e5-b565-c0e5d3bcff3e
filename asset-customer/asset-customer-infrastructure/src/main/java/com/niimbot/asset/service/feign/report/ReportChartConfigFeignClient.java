package com.niimbot.asset.service.feign.report;

import com.niimbot.report.ReportChartConfigDto;
import com.niimbot.report.ReportChartQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/27 上午10:09
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface ReportChartConfigFeignClient {

    /**
     * 配置报表图表类型
     * @param reportChartConfigDto
     * @return
     */
    @RequestMapping("server/report/chart/config")
    Boolean config(@RequestBody @Validated ReportChartConfigDto reportChartConfigDto);

    /**
     * 修改报表图表类型
     * @param reportChartConfigDto
     * @return
     */
    @RequestMapping("server/report/chart/edit")
    Boolean edit(@RequestBody @Validated ReportChartConfigDto reportChartConfigDto);

    /**
     * 查询所有的图表配置
     * @return
     */
    @GetMapping("server/report/chart/queryAll")
    List<ReportChartConfigDto> queryAll();

    /**
     * 根据条件查询报表支持图表
     * @param queryDto
     * @return
     */
    @GetMapping("server/report/chart/queryByCondition")
    List<ReportChartConfigDto> queryByCondition(@SpringQueryMap ReportChartQueryDto queryDto);
}
