package com.niimbot.asset.controller.pc.inventory;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.InventoryExcelService;
import com.niimbot.asset.service.InventoryResultHandleService;
import com.niimbot.asset.service.feign.InventoryFeignClient;
import com.niimbot.inventory.AssetAddDto;
import com.niimbot.inventory.AssetUpdateDto;
import com.niimbot.inventory.IgnoreUpdateDto;
import com.niimbot.inventory.InventoryHandleLogDto;
import com.niimbot.inventory.InventoryHandleLogQry;
import com.niimbot.inventory.InventorySurplusQueryDto;
import com.niimbot.inventory.InventorySurplusSimpleQueryDto;
import com.niimbot.inventory.PlPhotoDto;
import com.niimbot.inventory.PlUpdateAssetByUserDto;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.means.AssetDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/1 14:46
 */
@Slf4j
@Api(tags = "盘点结果处理")
@ResultController
@RequestMapping("api/pc/inventory/asset")
@RequiredArgsConstructor
public class InventoryResultHandleController {

    private final InventoryFeignClient inventoryFeignClient;
    private final InventoryExcelService inventoryExcelService;
    private final InventoryResultHandleService handleService;

    @ApiOperation(value = "PC损益处理-盘亏资产")
    @PostMapping("/pl/page")
    public PageUtils<JSONObject> plPage(@RequestBody InventorySurplusQueryDto dto) {
        return handleService.plPage(dto);
    }

    @ApiOperation(value = "PC损益处理-盘盈不在册数据")
    @GetMapping("/surplus")
    public PageUtils<JSONObject> getPlSurplus(@Validated InventorySurplusSimpleQueryDto dto) {
        return handleService.getPlSurplus(dto);
    }

    @ApiOperation(value = "PC损益处理-已修改资产")
    @PostMapping("/modified/page")
    public PageUtils<JSONObject> modifiedPage(@RequestBody @Validated InventorySurplusQueryDto dto) {
        return handleService.modifiedPage(dto);
    }

    @ApiOperation(value = "PC损益处理-已拍照资产")
    @PostMapping("/takePhoto/page")
    public PageUtils<JSONObject> takePhotoPage(@RequestBody @Validated InventorySurplusQueryDto dto) {
        return handleService.takePhotoPage(dto);
    }

    @ApiOperation(value = "PC损益处理导出")
    @PostMapping("/pl/export")
    public void assetPlExport(@RequestBody @Validated InventorySurplusQueryDto dto) {
        inventoryExcelService.assetPlExport(dto);
    }

    @ApiOperation(value = "*盘点损益处理-忽略操作")
    @PutMapping("/ignoreUpdate")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean ignoreUpdate(@RequestBody @Validated IgnoreUpdateDto dto) {
        return inventoryFeignClient.ignoreUpdate(dto);
    }

    @ApiOperation(value = "*盘点损益处理-修改资产信息")
    @PutMapping("/plUpdateAsset")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean plUpdateAsset(@RequestBody @Validated AssetUpdateDto dto) {
        return inventoryFeignClient.plUpdateAsset(dto);
    }

    @ApiOperation(value = "*盘点损益处理-使用盘点人修改信息")
    @PutMapping("/plUpdateAssetByUser")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean plUpdateAssetByUser(@RequestBody @Validated List<@Valid PlUpdateAssetByUserDto> dto) {
        return inventoryFeignClient.plUpdateAssetByUser(dto);
    }

    @ApiOperation(value = "*盘点损益处理-盘盈资产新增入库")
    @RepeatSubmit
    @PostMapping("/plAddAsset")
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public AssetDto plAddAsset(@RequestBody @Validated AssetAddDto dto) {
        return new AssetDto().setId(inventoryFeignClient.plAddAsset(dto));
    }

    @ApiOperation(value = "*盘点损益处理-添加资产照片")
    @PostMapping("/addPhoto")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean addPhoto(@RequestBody PlPhotoDto dto) {
        return inventoryFeignClient.addPhoto(dto);
    }

    @ApiOperation(value = "*盘点损益处理-替换已有资产照片")
    @PostMapping("/replacePhoto")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    @RepeatSubmit
    public Boolean replacePhoto(@RequestBody PlPhotoDto dto) {
        return inventoryFeignClient.replacePhoto(dto);
    }

    @ApiOperation(value = "盘点损益处理-处理记录")
    @AutoConvert
    @GetMapping("/handleLog")
    public InventoryHandleLogDto handleLog(InventoryHandleLogQry qry) {
        return inventoryFeignClient.handleLog(qry);
    }

}
