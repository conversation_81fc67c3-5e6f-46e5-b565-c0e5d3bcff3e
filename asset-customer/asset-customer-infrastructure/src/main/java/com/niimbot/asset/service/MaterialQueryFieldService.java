package com.niimbot.asset.service;

import com.niimbot.means.AssetHeadDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.system.QueryHeadConfigDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/6 15:31
 */
public interface MaterialQueryFieldService {

    // --------------耗材筛选项---------------
    List<QueryConditionDto> materialAllQueryField();

    List<QueryConditionDto> materialQueryView();

    // --------------耗材列表设置---------------
    Boolean materialHeadField(QueryHeadConfigDto config);

    QueryHeadConfigDto materialHeadField();

    List<QueryConditionDto> materialAllHeadField();

    List<AssetHeadDto> materialHeadView();

    List<AssetHeadDto> exportHeadField(Long standardId);

    // --------------耗材库存筛选项---------------
    List<QueryConditionDto> stockAllQueryField();

    List<QueryConditionDto> stockQueryView();

    // --------------标准品---------------
    QueryConditionStandardDto standardAllField(Long standardId, boolean needName, boolean filterFile);

    List<QueryConditionDto> materialSearchAllQueryField();

    // --------------耗材盘点---------------
    List<QueryConditionDto> inventoryAllQueryField();

    List<QueryConditionDto> inventoryQueryView();
}

