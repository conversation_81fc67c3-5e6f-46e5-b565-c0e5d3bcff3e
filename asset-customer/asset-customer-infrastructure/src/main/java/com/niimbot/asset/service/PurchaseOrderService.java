package com.niimbot.asset.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.purchase.PurchaseOrderQueryDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/20 15:02
 */
public interface PurchaseOrderService {

    /**
     * 采购单据导出
     *
     * @param orderQueryDto
     * @return 结果
     */
    ExportResponse exportOrderCard(PurchaseOrderQueryDto orderQueryDto);

    /**
     * 采购单据物品列表导出
     *
     * @param dto
     * @param orders
     * @param itemsMap
     * @return 结果
     */
    ExportResponse exportOrderDetail(
            PurchaseOrderQueryDto dto,
            List<JSONObject> orders,
            Map<Long, List<JSONObject>> itemsMap);
}
