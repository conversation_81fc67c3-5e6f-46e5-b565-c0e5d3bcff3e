package com.niimbot.asset.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.niimbot.asset.customer.event.ImportTaskEvent;
import com.niimbot.asset.framework.annotation.ExcelField;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.NoticeConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.ExcelUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.model.AssetExcelTplEnum;
import com.niimbot.asset.service.AbstractAssetService;
import com.niimbot.asset.service.AssetExcelService;
import com.niimbot.asset.service.ImportService;
import com.niimbot.asset.service.feign.*;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.asset.utils.ExcelExportUtil;
import com.niimbot.dynamicform.FormByIdQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.*;
import com.niimbot.system.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2021/3/2 15:43
 */
@Slf4j
@Service
@Primary
public class AssetExcelServiceImpl extends AbstractAssetService implements AssetExcelService {

    private static List<String> needSheets = ListUtil.of(AssetConstant.ED_YZC_ASSET_CATE,
            AssetConstant.ED_YZC_AREA,
            AssetConstant.ED_YZC_EMP,
            AssetConstant.ED_YZC_ORG);

    private static ThreadLocal<ImportInfo> globalCache = new TransmittableThreadLocal<>();

    private final RedisService redisService;
    private final ImportService importService;
    private final ImportTaskFeignClient importTaskFeignClient;
    private final DataAuthorityFeignClient dataAuthorityFeignClient;
    private final StoreRecordFeignClient storeRecordFeignClient;

    @Autowired
    public AssetExcelServiceImpl(RedisService redisService,
                                 ImportService importService,
                                 ImportTaskFeignClient importTaskFeignClient,
                                 DataAuthorityFeignClient dataAuthorityFeignClient,
                                 StoreRecordFeignClient storeRecordFeignClient) {
        this.redisService = redisService;
        this.importService = importService;
        this.dataAuthorityFeignClient = dataAuthorityFeignClient;
        this.importTaskFeignClient = importTaskFeignClient;
        this.storeRecordFeignClient = storeRecordFeignClient;
    }

    /**
     * 导出资产模板
     *
     * @return excel
     */
    @Override
    public ExcelWriter buildExcelWriter(Long standardId) {
        // 获取 writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 获取 sheet
        Sheet sheet = writer.getSheet();
        // 获取 styleSet
        StyleSet styleSet = writer.getStyleSet();
        // 设置边框
        styleSet.setBorder(BorderStyle.NONE, IndexedColors.GREY_25_PERCENT);
        styleSet.setAlign(HorizontalAlignment.LEFT, VerticalAlignment.BOTTOM);
        Workbook workbook = sheet.getWorkbook();
        DataFormat format = workbook.createDataFormat();
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(format.getFormat("@"));

        // 设置表头的cellStyle
        CellStyle redHeadStyle = writer.createCellStyle();
        redHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        redHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        redHeadStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        redHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        redHeadStyle.setBorderBottom(BorderStyle.THIN);
        redHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        redHeadStyle.setBorderTop(BorderStyle.THIN);
        redHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderLeft(BorderStyle.THIN);
        redHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        redHeadStyle.setBorderRight(BorderStyle.THIN);
        redHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font red = writer.createFont();
        red.setFontHeightInPoints((short) 13);
        red.setColor(IndexedColors.RED.getIndex());
        redHeadStyle.setFont(red);

        CellStyle blackHeadStyle = writer.createCellStyle();
        blackHeadStyle.setAlignment(HorizontalAlignment.CENTER);
        blackHeadStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        blackHeadStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        blackHeadStyle.setBorderBottom(BorderStyle.THIN);
        blackHeadStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blackHeadStyle.setBorderTop(BorderStyle.THIN);
        blackHeadStyle.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderLeft(BorderStyle.THIN);
        blackHeadStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        blackHeadStyle.setBorderRight(BorderStyle.THIN);
        blackHeadStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        Font black = writer.createFont();
        black.setFontHeightInPoints((short) 13);
        black.setColor(IndexedColors.BLACK.getIndex());
        blackHeadStyle.setFont(black);

        // 写入文件注意事项
        Row attentionRow = writer.getOrCreateRow(0);
        attentionRow.setHeight((short) 3000);
        Cell attentionCell = attentionRow.createCell(0);
        CellStyle attention = writer.createCellStyle();
        attention.setVerticalAlignment(VerticalAlignment.CENTER);
        attention.setWrapText(true);
        attention.setFont(black);
        attentionCell.setCellStyle(attention);
        String text = "注意事项:\n" +
                "1、红色字体为必填项，黑色字体为选填项\n" +
                "2、输入员工时，同名员工需填写员工工号，如：李白（001）；\n" +
                "3、资产编码填写不可重复，当需要系统自动根据编码规则生成时，资产编码请填写以下中文：系统自动生成；\n" +
                "4、若资产是在用/借用状态，请务必填写使用组织；若填写了使用人，使用组织必填；\n" +
                "5、多选字段，多个字段以中文或英文逗号隔开；\n" +
                "6、单次最多可导入5000条数据，文件不可超过1MB；\n" +
                "7、具体字段填写要求可参考表头单元格批注信息。";
        XSSFRichTextString xssfRichTextString = new XSSFRichTextString(text);
        Font titleRed = writer.createFont();
        titleRed.setFontHeightInPoints((short) 13);
        titleRed.setColor(IndexedColors.RED.getIndex());
        xssfRichTextString.applyFont(0, 99, black);
        xssfRichTextString.applyFont(99, 105, titleRed);
        xssfRichTextString.applyFont(105, text.length() - 1, black);
        attentionCell.setCellValue(xssfRichTextString);
        writer.merge(26);

        // 查询录入标准数据项
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        // 查询录入的标准品扩展数据
        if (ObjectUtil.isNotNull(standardId)) {
            List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(standardExtField);
        }

        formFields = formFields.stream()
                .filter(attr -> !attr.isHidden())
                .filter(attr -> !ListUtil.of(FormFieldCO.IMAGES, FormFieldCO.FILES, FormFieldCO.SPLIT_LINE)
                        .contains(attr.getFieldType())).collect(Collectors.toList());
        Row headRow = writer.getOrCreateRow(1);
        int realCol = 0;
        for (FormFieldCO attr : formFields) {
            // ============================== 设置表头 ===================================
            // 写入表头
            Cell cell = headRow.createCell(realCol);
            cell.setCellStyle(attr.requiredProps() ? redHeadStyle : blackHeadStyle);
            cell.setCellValue(attr.getFieldName());

            String tplComment = AssetExcelTplEnum.getTplComment(attr.getFieldCode());
            if (StrUtil.isNotEmpty(tplComment)) {
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6));
                comment.setString(new XSSFRichTextString(tplComment));
                cell.setCellComment(comment);
            }

            // 调整每一列宽度
            sheet.autoSizeColumn((short) realCol);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(realCol, sheet.getColumnWidth(realCol) * 17 / 10);
            sheet.setDefaultColumnStyle(realCol, style);

            if (FormFieldCO.SELECT_DROPDOWN.equals(attr.getFieldType())) {
                JSONArray values = attr.getFieldProps().getJSONArray("values");
                String[] selected = values.toArray(new String[]{});
                writer.addSelect(new CellRangeAddressList(2, 5000, realCol, realCol), selected);
            }
            realCol += 1;
        }
        // 写入其他sheet
        buildSheet(writer, formFields);
        return writer;
    }

    @Data
    @AllArgsConstructor
    private static class AreaExport {
        @ExcelField(header = "区域编码", ordinal = 1)
        private String areaCode;
        @ExcelField(header = "区域名称", ordinal = 2)
        private String areaName;
    }

    @Data
    @AllArgsConstructor
    private static class CateExport {
        @ExcelField(header = "分类编码", ordinal = 1)
        private String cateCode;
        @ExcelField(header = "分类名称", ordinal = 2)
        private String cateName;
    }

    @Data
    @AllArgsConstructor
    public static class OrgExport {
        @ExcelField(header = "组织编码", ordinal = 1)
        public String orgCode;
        @ExcelField(header = "组织名称", ordinal = 2)
        public String orgName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(2);
            map.put("组织编码", "orgCode");
            map.put("组织名称", "orgName");
            return map;
        }
    }

    @Data
    @AllArgsConstructor
    public static class EmpExport {
        @ExcelField(header = "员工编码", ordinal = 1)
        public String empCode;
        @ExcelField(header = "员工名称", ordinal = 2)
        public String empName;

        public static LinkedHashMap<String, String> headerMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>(2);
            map.put("员工编码", "empCode");
            map.put("员工名称", "empName");
            return map;
        }
    }

    protected void buildSheet(ExcelWriter writer, List<FormFieldCO> formFields) {
        formFields.stream()
                .filter(f -> needSheets.contains(f.getFieldType()))
                .map(FormFieldCO::getFieldType).distinct().forEach(f -> {
                    switch (f) {
                        case AssetConstant.ED_YZC_AREA:
                            writer.setSheet("区域");
                            List<AreaDto> areaAll = areaFeignClient.areaList(new AreaQueryDto());
                            LinkedHashMap<String, String> areaHead = ExcelUtils.buildExcelHead(AreaExport.class);
                            areaHead.forEach(writer::addHeaderAlias);
                            List<AreaExport> areaData = areaAll.stream().map(area ->
                                            new AreaExport(area.getAreaCode(), area.getAreaName()))
                                    .collect(Collectors.toList());
                            writer.setOnlyAlias(true);
                            writer.write(areaData);
                            writer.autoSizeColumnAll();
                            break;
                        case AssetConstant.ED_YZC_ASSET_CATE:
                            writer.setSheet("资产分类");
                            List<CategoryDto> cateAll = categoryFeignClient.list(new CategoryQueryDto());
                            LinkedHashMap<String, String> cateHead = ExcelUtils.buildExcelHead(CateExport.class);
                            cateHead.forEach(writer::addHeaderAlias);
                            List<CateExport> cateData = cateAll.stream().map(cate ->
                                            new CateExport(cate.getCategoryCode(), cate.getCategoryName()))
                                    .collect(Collectors.toList());
                            writer.setOnlyAlias(true);
                            writer.write(cateData);
                            writer.autoSizeColumnAll();
                            break;
                        case AssetConstant.ED_YZC_ORG:
                            writer.setSheet("组织");
                            List<OrgDto> orgAll = orgFeignClient.list(new OrgQueryDto().setFilterPerm(true));
                            LinkedHashMap<String, String> orgHead = ExcelUtils.buildExcelHead(OrgExport.class);
                            orgHead.forEach(writer::addHeaderAlias);
                            List<OrgExport> orgData = orgAll.stream().map(org ->
                                            new OrgExport(org.getOrgCode(), org.getOrgName()))
                                    .collect(Collectors.toList());
                            writer.setOnlyAlias(true);
                            writer.write(orgData);
                            writer.autoSizeColumnAll();
                            break;
                        case AssetConstant.ED_YZC_EMP:
                            Edition.weixin(() -> {
                                writer.setSheet("员工");
                                List<CusEmployeeDto> empAll = employeeFeignClient.actList(new CusEmployeeQueryDto().setFilterPerm(true));
                                LinkedHashMap<String, String> empHead = ExcelUtils.buildExcelHead(EmpExport.class);
                                empHead.forEach(writer::addHeaderAlias);
                                List<EmpExport> empData = empAll.stream().map(v -> new EmpExport(v.getEmpNo(), v.getEmpName())).collect(Collectors.toList());
                                writer.setOnlyAlias(true);
                                writer.write(empData);
                                writer.autoSizeColumnAll();
                            });
                            break;
                        default:
                            break;
                    }
                });
    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize,
                                 Long companyId, Long standardId) {
        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setFileName(fileName);
        importInfo.setFileSize(fileSize);
        importInfo.setCompanyId(companyId);
        importInfo.setStandardId(standardId);

        List<FormFieldCO> formFields = getStandardAttr(standardId);
        Map<String, List<FormFieldCO>> assetAttrMap = formFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 解析的模板导入
        ExcelReader reader = ExcelUtil.getReader(stream);
        List<List<Object>> read = reader.read(1);
        if (CollUtil.isEmpty(read)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
        }
        // =============================  读取Excel表头并校验 ================================
        List<Object> header = ExcelUtils.clearEmpty(read.get(0));
        int noNullHeaderSize = Convert.toInt(header.stream().filter(it -> StrUtil.isNotBlank(Convert.toStr(it))).count());
        // 表头字段映射
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        // 匹配动态属性和表头数据是否一致，防止动态属性被修改
        if (formFields.size() != noNullHeaderSize) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
        } else {
            for (int i = 0; i < header.size(); i++) {
                String headName = Convert.toStr(header.get(i), "").trim();
                if (StrUtil.isBlank(Convert.toStr(headName))) {
                    continue;
                }
                if (!assetAttrMap.containsKey(headName)) {
                    throw new BusinessException(MeansResultCode.ASSET_IMPORT_ATTR_CANT_EDIT);
                }
                List<FormFieldCO> formFieldCOList = assetAttrMap.get(headName);
                if (CollUtil.isNotEmpty(formFieldCOList)) {
                    FormFieldCO formFieldCO = formFieldCOList.get(0);
                    headerMapping.put(i, formFieldCO);
                    formFieldCOList.remove(0);
                    assetAttrMap.put(headName, formFieldCOList);
                }
            }
        }
        importInfo.setHeaderMapping(headerMapping);
        importInfo.setRead(read);
        List<OrgExport> orgExports = new ArrayList<>();
        List<EmpExport> empExports = new ArrayList<>();
        Edition.weixin(() -> {
            List<OrgExport> readOrg = reader.setSheet("组织").setHeaderAlias(OrgExport.headerMap()).read(0, 1, OrgExport.class);
            orgExports.addAll(readOrg);
            readOrg.forEach(v -> {
                OrgExport copy = BeanUtil.copyProperties(v, OrgExport.class);
                copy.setOrgName(copy.getOrgName() + "（" + copy.getOrgCode() + "）");
                orgExports.add(copy);
            });

            List<EmpExport> readEmp = reader.setSheet("员工").setHeaderAlias(EmpExport.headerMap()).read(0, 1, EmpExport.class);
            // name && name（）
            empExports.addAll(readEmp);
            readEmp.forEach(v -> {
                EmpExport copy = BeanUtil.copyProperties(v, EmpExport.class);
                copy.setEmpName(copy.getEmpName() + "（" + copy.getEmpCode() + "）");
                empExports.add(copy);
            });
        });
        this.importExcel(importInfo, true, orgExports, empExports);
    }

    @Override
    public void parseExcelStream(InputStream stream, String fileName, Long fileSize, Long companyId,
                                 Long standardId, List<ImportMappingDto> mapping) {
        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setFileName(fileName);
        importInfo.setFileSize(fileSize);
        importInfo.setCompanyId(companyId);
        importInfo.setStandardId(standardId);

        // 校验code是否存在重复
        List<String> codes = mapping.stream().map(ImportMappingDto::getCode).collect(Collectors.toList());
        if (codes.size() != new HashSet<>(codes).size()) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "映射字段不可重复选择");
        }

        List<FormFieldCO> formFields = getStandardAttr(standardId);

        // 必填项code
        Set<String> requiredCodes = formFields.stream().filter(attr -> BooleanUtil.isTrue(attr.requiredProps()))
                .map(FormFieldCO::getFieldCode).collect(Collectors.toSet());

        // 当前全部字段属性集合
        Map<String, FormFieldCO> assetAttrMap = formFields.stream().collect(Collectors.toMap(FormFieldCO::getFieldCode, k -> k));

        // 解析的模板导入
        ExcelReader reader = ExcelUtil.getReader(stream);
        List<List<Object>> read = reader.read(0);

        if (CollUtil.isEmpty(read)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
        }
        // 表头
        List<Object> header = read.get(0);
        // 表头字段映射
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        // 过滤筛选
        for (ImportMappingDto mappingDto : mapping) {
            String mappingHeaderName = mappingDto.getName();
            String mappingSelectItemCode = mappingDto.getCode();
            Integer column = mappingDto.getColumn();
            String excelHeaderName = Convert.toStr(header.get(column), "");
            if (mappingHeaderName.equals(excelHeaderName)) {
                if (assetAttrMap.containsKey(mappingSelectItemCode)) {
                    headerMapping.put(column, assetAttrMap.get(mappingSelectItemCode));
                    requiredCodes.remove(mappingSelectItemCode);
                } else {
                    throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "您当前匹配的导入模板已变更，请重新导入数据");
                }
            }

        }
        if (requiredCodes.size() > 0) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "存在必填项字段未设置");
        }
        importInfo.setHeaderMapping(headerMapping);
        importInfo.setRead(read);
        this.importExcel(importInfo, true, Collections.emptyList(), Collections.emptyList());
    }

    @Override
    public Boolean importErrorSave(Long taskId, List<List<Object>> sheetModels, Long companyId) {
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (importTaskDto == null) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "导入任务" + taskId + "不存在");
        }
        if (CollUtil.isEmpty(sheetModels)) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "表格未检测到数据");
        }

        // 导入对象
        ImportInfo importInfo = new ImportInfo();
        importInfo.setTaskId(taskId);
        importInfo.setFileName("资产在线编辑保存");
        importInfo.setFileSize(0L);
        importInfo.setCompanyId(companyId);
        importInfo.setRead(sheetModels);

        ImportTaskExtInfoDto extInfo = importTaskDto.getExtInfo();
        Long standardId = null;
        if (extInfo != null && extInfo.getStandardId() != null) {
            standardId = extInfo.getStandardId();
        }
        importInfo.setStandardId(standardId);

        List<FormFieldCO> formFields = getStandardAttr(standardId);
        Map<String, List<FormFieldCO>> assetAttrMap = formFields.stream().collect(Collectors.groupingBy(FormFieldCO::getFieldName));

        // 必填项code
        Set<String> requiredCodes = formFields.stream().filter(attr -> BooleanUtil.isTrue(attr.requiredProps()))
                .map(FormFieldCO::getFieldCode).collect(Collectors.toSet());

        // =============================  读取Excel表头并校验 ================================
        List<Object> header = sheetModels.get(0);
        // 表头字段映射
        Map<Integer, FormFieldCO> headerMapping = new HashMap<>();
        // 匹配动态属性和表头数据是否一致，防止动态属性被修改
        for (int i = 0; i < header.size(); i++) {
            String headName = Convert.toStr(header.get(i), "").trim();
            if (StrUtil.isBlank(Convert.toStr(headName))) {
                continue;
            }
            if (!assetAttrMap.containsKey(headName)) {
                throw new BusinessException(MeansResultCode.ASSET_IMPORT_TPL_CHANGE);
            }
            List<FormFieldCO> bizFormAssetConfigs = assetAttrMap.get(headName);
            if (CollUtil.isNotEmpty(bizFormAssetConfigs)) {
                FormFieldCO bizFormAssetConfig = bizFormAssetConfigs.get(0);
                headerMapping.put(i, bizFormAssetConfig);
                requiredCodes.remove(bizFormAssetConfig.getFieldCode());
                bizFormAssetConfigs.remove(0);
                assetAttrMap.put(headName, bizFormAssetConfigs);
            }
        }

        if (requiredCodes.size() > 0) {
            throw new BusinessException(MeansResultCode.ASSET_IMPORT_ERROR, "存在必填项字段未设置");
        }

        importInfo.setHeaderMapping(headerMapping);
        // 校验表头数据
        List<OrgExport> orgExports = new ArrayList<>();
        List<EmpExport> empExports = new ArrayList<>();
        Edition.weixin(() -> {
            Object orgs = redisService.get("weixin:import:asset:add:org_id_trans:" + importInfo.getTaskId());
            Object emps = redisService.get("weixin:import:asset:add:emp_id_trans:" + importInfo.getTaskId());
            if (Objects.isNull(orgs) || Objects.isNull(emps)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "文件已过期，请重新导入");
            }
            orgExports.addAll(JSONArray.parseArray(orgs.toString(), OrgExport.class));
            empExports.addAll(JSONArray.parseArray(emps.toString(), EmpExport.class));
        });
        this.importExcel(importInfo, false, orgExports, empExports);
        return true;
    }

    /**
     * 导入资产模板
     */
    protected void importExcel(ImportInfo importInfo, Boolean async, List<OrgExport> orgExports, List<EmpExport> empExports) {
        // 判断是否超过最大上传条数，一次限制5000
        if (importInfo.getRead().size() > MAX_BATCH + 1) {
            throw new BusinessException(MeansResultCode.IMPORT_MAX_LIMIT);
        }

        // 删除历史导入信息
        if (importInfo.getTaskId() != null) {
            assetFeignClient.importErrorDeleteAll(importInfo.getTaskId());
        }
        globalCache.set(importInfo);
        dropDownCache.set(new HashMap<>());
        orgRefEmpCache.set(new HashMap<>());
        if (async) {
            // 异步启动
            Thread thread = new Thread(() -> {
                ImportDto importCache = new ImportDto()
                        .setFileName(importInfo.getFileName())
                        .setImportType(DictConstant.IMPORT_TYPE_ASSET)
                        .setFileSize(importInfo.getFileSize())
                        .setCount(importInfo.getRead().size() - 1);
                importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
                startImport(importCache, orgExports, empExports);
            });
            thread.start();
            thread.setUncaughtExceptionHandler((t, e) ->
                    log.error(t.getName() + " ERROR " + e.getMessage(), e)
            );
        } else {
            // 同步启动
            ImportDto importCache = new ImportDto()
                    .setFileName(importInfo.getFileName())
                    .setImportType(DictConstant.IMPORT_TYPE_ASSET)
                    .setFileSize(importInfo.getFileSize())
                    .setCount(importInfo.getRead().size() - 1);
            importCache.setImportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
            startImport(importCache, orgExports, empExports);
        }

    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        return assetFeignClient.importError(taskId);
    }

    @Override
    public ReImportDto reImport(Long taskId) {
        ReImportDto reImportDto = new ReImportDto();
        List<List<LuckySheetModel>> lists = assetFeignClient.importError(taskId);
        ImportTaskDto importTaskDto = importTaskFeignClient.queryById(taskId);
        if (CollUtil.isNotEmpty(lists) && ObjectUtil.isNotNull(importTaskDto)) {
            reImportDto.setExcel(lists);
            if (ObjectUtil.isNotNull(importTaskDto.getExtInfo())) {
                ImportTaskExtInfoDto extInfo = importTaskDto.getExtInfo();
                if (extInfo.getStandardId() != null) {
                    FormVO standardVO = formFeignClient.getByFormId(new FormByIdQry(importTaskDto.getExtInfo().getStandardId(),
                            ListUtil.of(1L, LoginUserThreadLocal.getCompanyId())));
                    if (standardVO != null) {
                        reImportDto.setStandardId(importTaskDto.getExtInfo().getStandardId());
                        reImportDto.setStandardName(standardVO.getFormName());
                    }
                }
            }
        }
        return reImportDto;
    }

    @Override
    public Boolean importErrorDelete(Long taskId) {
        return assetFeignClient.importErrorDeleteAll(taskId);
    }

    @Override
    public List<JSONObject> getExcelData(AssetQueryConditionDto queryDto, List<Long> standardIds) {
        // 查询固定属性
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (CollUtil.isNotEmpty(standardIds)) {
            for (Long standardId : standardIds) {
                List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(formVO.getFormId(), standardId);
                formFields.addAll(standardExtField);
            }
        }
        // 如果有勾选，则直接取勾选数据
        if (CollUtil.isNotEmpty(queryDto.getAssetIds())) {
            AssetQueryConditionDto conditionDto = new AssetQueryConditionDto();
            conditionDto.setAssetIds(queryDto.getAssetIds());
            conditionDto.setPageSize(Integer.MAX_VALUE);
            queryDto = conditionDto;
        }
        PageUtils<AssetDto> assetPage = assetFeignClient.pcPage(queryDto);

        FormFieldCO createTime = new FormFieldCO();
        createTime.setFieldCode(QueryFieldConstant.FIELD_CREATE_TIME);
        createTime.setFieldName("创建时间");
        createTime.setFieldType(FormFieldCO.DATETIME);
        createTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(createTime);

        FormFieldCO updateTime = new FormFieldCO();
        updateTime.setFieldCode(QueryFieldConstant.FIELD_UPDATE_TIME);
        updateTime.setFieldName("更新时间");
        updateTime.setFieldType(FormFieldCO.DATETIME);
        updateTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(updateTime);

        FormFieldCO lastPrintTime = new FormFieldCO();
        lastPrintTime.setFieldCode(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME);
        lastPrintTime.setFieldName("最近打印时间");
        lastPrintTime.setFieldType(FormFieldCO.DATETIME);
        lastPrintTime.setFieldProps(new JSONObject().fluentPut("dateFormatType", "yyyy-MM-dd"));
        formFields.add(lastPrintTime);

        List<JSONObject> assetDataList = assetPage.getList().stream().map(AssetDto::translate).collect(Collectors.toList());
        assetUtil.translateAssetJsonFormatBatch(assetDataList, formFields, true);
        //导出的时候价值字段为整数时拼接00如：(10.00)
        assetDataList.forEach(v -> {
            if (StringUtils.isNotEmpty(v.getString("price"))) {
                if (ExcelExportUtil.isNumeric(v.getString("price"))) {
                    v.put("price", v.getString("price") + ".00");
                }else {
                    v.put("price", v.getString("price") + " ");
                }
            }
        });
        return assetDataList;
    }

    @Override
    public List<List<LuckySheetModel>> resolveExcelStream(InputStream stream) {
        List<List<LuckySheetModel>> result = new ArrayList<>();
        ExcelReader reader = ExcelUtil.getReader(stream);
        List<List<Object>> read = reader.read(0, 30, false);
        for (int r = 0; r < read.size(); r++) {
            List<LuckySheetModel> list = new ArrayList<>();
            List<Object> row = read.get(r);
            for (int c = 0, len = row.size(); c < len; c++) {
                String cell = Convert.toStr(row.get(c));
                LuckySheetModel sheetModel = new LuckySheetModel();
                sheetModel.setR(r).setC(c);
                LuckySheetModel.Value value = new LuckySheetModel.Value();
                value.setV(cell);
                sheetModel.setV(value);
                list.add(sheetModel);
            }
            result.add(list);
        }
        return result;
    }

    /**
     * 导入
     *
     * @param importCache 导入结果集
     */
    private void startImport(ImportDto importCache, List<OrgExport> orgExports, List<EmpExport> empExports) {
        ImportInfo importInfo = globalCache.get();
        // 导入任务
        ImportTaskDto importTaskDto = null;
        if (importInfo.getTaskId() != null) {
            importTaskDto = importTaskFeignClient.queryById(importInfo.getTaskId());
        }
        try {
            // 设置锁
            redisService.hSetAll(RedisConstant.companyImportKey(ImportService.ASSET, importInfo.getCompanyId()),
                    (JSONObject) JSON.toJSON(importCache));
            // 创建导入任务
            if (importTaskDto == null) {
                importTaskDto = new ImportTaskDto(DictConstant.TASK_TYPE_IMPORT, importCache);
                ImportTaskExtInfoDto extInfoDto = new ImportTaskExtInfoDto();
                extInfoDto.setStandardId(importInfo.getStandardId());
                importTaskDto.setExtInfo(extInfoDto);
                Long id = importTaskFeignClient.save(importTaskDto);
                importTaskDto.setId(id);
                Edition.weixin(() -> {
                    redisService.set("weixin:import:asset:add:org_id_trans:" + id, JSONObject.toJSONString(orgExports), 3, TimeUnit.DAYS);
                    redisService.set("weixin:import:asset:add:emp_id_trans:" + id, JSONObject.toJSONString(empExports), 3, TimeUnit.DAYS);
                });
            }

            List<AssetImportDto> tableData = new ArrayList<>();
            // 构建资产JSON数据
            for (int idx = 1; idx < importInfo.getRead().size(); idx++) {
                // 行数据
                List<Object> dataRow = importInfo.getRead().get(idx);
                // 写入固定属性数据
                JSONObject assetData = new JSONObject();
                IntStream.range(0, dataRow.size()).forEach(nameIdx -> {
                    FormFieldCO assetConfig = importInfo.getHeaderMapping().get(nameIdx);
                    if (assetConfig != null) {
                        Object cell = dataRow.get(nameIdx);
                        assetData.put(assetConfig.getFieldCode(), cell);
                    }
                });
                // 创建属性对象
                AssetImportDto importDto = new AssetImportDto();
                importDto.setTaskId(importTaskDto.getId())
                        .setStandardId(importInfo.getStandardId())
                        .setErrorNum(0)
                        .setAssetData(assetData);
                tableData.add(importDto);
            }
            // 执行
            importService.sendAssetMsg(importInfo.getCompanyId());
            this.executeTableData(importTaskDto.getId(), tableData, importInfo, orgExports, empExports);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET, importInfo.getCompanyId()), "notice", true);
            redisService.hSet(RedisConstant.companyImportKey(ImportService.ASSET, importInfo.getCompanyId()), "finish", true);
            importService.sendAssetMsg(importInfo.getCompanyId());
            // 更新任务状态
            if (importTaskDto != null && importTaskDto.getId() != null) {
                String key = RedisConstant.companyImportKey(ImportService.ASSET, importInfo.getCompanyId());
                Map<String, Object> map = Convert.toMap(String.class, Object.class, redisService.hGetAll(key));
                JSONObject json = new JSONObject(map);
                ImportDto importDto = json.toJavaObject(ImportDto.class);
                importTaskDto.setTaskImportStatus(importDto);
                log.info("资产导入完成，Id={}, Finish={}, cache={}", importTaskDto.getId(), importTaskDto.getTaskStatus(), json.toJSONString());
                importTaskFeignClient.update(importTaskDto);
                if (Edition.isWeixin() && importTaskDto.getTaskStatus() == DictConstant.IMPORT_STATUS_SUCC) {
                    redisService.del("weixin:import:asset:add:org_id_trans:" + importTaskDto.getId());
                    redisService.del("weixin:import:asset:add:emp_id_trans:" + importTaskDto.getId());
                }
            }
            this.clearThreadLocal();
        }
    }

    /**
     * 处理校验入库
     *
     * @param tableData 数据
     */
    private void executeTableData(Long taskId, List<AssetImportDto> tableData, ImportInfo importInfo, List<OrgExport> orgExports, List<EmpExport> empExports) {
        // 区域
        DataAuthorityDto areaPerm = dataAuthorityFeignClient.getByUserAndDataCodeAndCode(LoginUserThreadLocal.getCurrentUserId(), "area", "dept");
        // 分类
        DataAuthorityDto catePerm = dataAuthorityFeignClient.getByUserAndDataCodeAndCode(LoginUserThreadLocal.getCurrentUserId(), "asset", "cate");
        // 有序集合
        LinkedHashMap<String, FormFieldCO> codeToAssetAttrMap = new LinkedHashMap<>();
        // 临时缓存
        Map<String, Map<String, List<IdNameCache>>> tmpCache = new HashMap<>();
        for (Map.Entry<Integer, FormFieldCO> entry : importInfo.getHeaderMapping().entrySet()) {
            FormFieldCO fieldCO = entry.getValue();
            // 写入本地缓存
            loadSelectCache(fieldCO.getFieldType(), fieldCO.getFieldName(), tmpCache, fieldCO.getFieldProps());
            codeToAssetAttrMap.putIfAbsent(fieldCO.getFieldCode(), fieldCO);
        }

        Edition.weixin(() -> {
            // 组织
            Map<String, List<OrgExport>> orgGroup = orgExports.stream().collect(Collectors.groupingBy(OrgExport::getOrgCode));
            Map<String, List<IdNameCache>> orgCache = orgFeignClient.listByCodes(new ArrayList<>(orgGroup.keySet()))
                    .stream()
                    .filter(v -> orgGroup.containsKey(v.getOrgCode()))
                    .map(v -> orgGroup.get(v.getOrgCode()).stream().map(t -> new IdNameCache(Convert.toStr(v.getId()), t.getOrgName(), true)).collect(Collectors.toList()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.groupingBy(IdNameCache::getName));
            importInfo.getHeaderMapping().values().stream()
                    .filter(v -> FormFieldCO.YZC_ORG.equals(v.getFieldType()))
                    .collect(Collectors.toList())
                    .forEach(v -> {
                        dropDownCache.get().remove(v.getFieldName());
                        dropDownCache.get().put(v.getFieldName(), orgCache);
                    });

            // 人
            Map<String, List<EmpExport>> empGroup = empExports.stream().collect(Collectors.groupingBy(EmpExport::getEmpCode));
            Map<String, List<IdNameCache>> empCache = employeeFeignClient.listByCodes(new ArrayList<>(empGroup.keySet()))
                    .stream()
                    .filter(v -> empGroup.containsKey(v.getEmpNo()))
                    .map(v -> empGroup.get(v.getEmpNo()).stream().map(t -> new IdNameCache(Convert.toStr(v.getId()), t.getEmpName(), true)).collect(Collectors.toList()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.groupingBy(IdNameCache::getName));

            importInfo.getHeaderMapping().values().stream()
                    .filter(v -> FormFieldCO.YZC_EMP.equals(v.getFieldType()))
                    .collect(Collectors.toList())
                    .forEach(v -> {
                        dropDownCache.get().remove(v.getFieldName());
                        dropDownCache.get().put(v.getFieldName(), empCache);
                    });
        });

        // 下拉Name和ID映射，Map<字段名称, Map<名称, List<映射>>
        Map<String, Map<String, List<IdNameCache>>> fieldNameCache = dropDownCache.get();
        // 写入表头数据
        this.saveLuckySheetHead(taskId, importInfo.getHeaderMapping());
        AtomicInteger successNum = new AtomicInteger(0);
        // 循环处理行数据
        IntStream.range(0, tableData.size()).forEach(idx -> {
            // 获取导入数据
            AssetImportDto importDto = tableData.get(idx);
            // 数据
            JSONObject assetData = importDto.getAssetData();
            List<AssetImportDto.FieldData> fieldDataList = new ArrayList<>();
            codeToAssetAttrMap.forEach((attrCode, fieldCO) -> {
                // 新增数据，直接新增，不参与事务。2022年7月19日
                boolean needSave = BooleanUtil.isTrue(fieldCO.getFieldProps().getBoolean("saveData"));

                AssetImportDto.FieldData fieldData = new AssetImportDto.FieldData();
                Object attrVal = assetData.getOrDefault(attrCode, null);
                if (fieldCO.getFieldType().equals(EXCLUDE_FIELD)) {
                    assetData.remove(attrCode);
                }
                fieldData.setSource(attrVal);
                if (attrVal instanceof String) {
                    fieldData.setTarget(StrUtil.trim(((String) attrVal)));
                } else {
                    fieldData.setTarget(attrVal);
                }
                fieldData.setFieldName(fieldCO.getFieldName());
                fieldData.setFieldCode(fieldCO.getFieldCode());

                // 处理日期类型
                if (FormFieldCO.DATETIME.equals(fieldCO.getFieldType())) {
                    dateConvert(fieldData, attrVal, fieldCO.getFieldProps().getString("dateFormatType"));
                }
                // 处理多选
                if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(fieldCO.getFieldType())) {
                    if (fieldData.getTarget() != null) {
                        String val = Convert.toStr(attrVal);
                        val = val.replace("，", ",");
                        String[] split = val.split(",");
                        fieldData.setTarget(new ArrayList<>(Arrays.asList(split)));
                    } else {
                        fieldData.setTarget(new ArrayList<>());
                    }
                }
                // 翻译业务组件数据
                String attrValStr = StrUtil.trim(Convert.toStr(attrVal));
                if (fieldNameCache.containsKey(fieldCO.getFieldName()) && StrUtil.isNotBlank(attrValStr)) {
                    Map<String, List<IdNameCache>> idNameCacheMap = fieldNameCache.getOrDefault(fieldCO.getFieldName(), new HashMap<>());
                    List<IdNameCache> idNameCaches;
                    if (idNameCacheMap.containsKey(attrValStr)) {
                        // 1.优先全匹配文本
                        idNameCaches = idNameCacheMap.get(attrValStr);
                    } else {
                        // 2.匹配括号内容是否大写字符加数字类型
                        idNameCaches = idNameCacheMap.get(ExcelUtils.matchCodeAndReplace(attrValStr));
                    }
                    if (CollUtil.isEmpty(idNameCaches)) {
                        if (needSave) {
                            // db插入数据
                            switch (fieldCO.getFieldType()) {
                                case FormFieldCO.YZC_AREA:
                                    String areaCode = areaFeignClient.recommendCode();
                                    AreaDto areaDto = new AreaDto();
                                    areaDto.setAreaCode(areaCode)
                                            .setAreaName(attrValStr);
                                    List<OrgDto> orgDtoList = orgFeignClient.areaPermsList();
                                    orgDtoList.stream().filter(f ->
                                                    BooleanUtil.isFalse(f.getDisabled())).findFirst()
                                            .ifPresent(dto -> areaDto.setOrgId(dto.getId()));
                                    if (attrValStr.length() > 50 || attrValStr.isEmpty()) {
                                        fieldData.getErrMsg().add("区域名称请输入1-50位");
                                    } else {
                                        try {
                                            Long areaId = areaFeignClient.insertArea(areaDto);
                                            areaDto.setId(areaId);
                                            idNameCacheMap.put(attrValStr,
                                                    ListUtil.of(new IdNameCache(Convert.toStr(areaDto.getId()), areaDto.getAreaName(),
                                                            areaPerm.getAuthorityType() == 0)));
                                            if (areaPerm.getAuthorityType() == 0) {
                                                fieldData.setTarget(Convert.toStr(areaDto.getId()));
                                            } else {
                                                fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                                            }
                                        } catch (Exception e) {
                                            log.error("区域自动新增异常, {}", e.getMessage(), e);
                                            fieldData.getErrMsg().add("区域自动新增异常，请重试");
                                        }
                                    }
                                    break;
                                case FormFieldCO.YZC_ASSET_CATE:
                                    String cateCode = categoryFeignClient.recommendCode();
                                    CategoryDto categoryDto = new CategoryDto();
                                    categoryDto.setCategoryName(attrValStr)
                                            .setCategoryCode(cateCode)
                                            .setPid(0L);
                                    if (attrValStr.length() > 50 || attrValStr.isEmpty()) {
                                        fieldData.getErrMsg().add("分类名称请输入1-50位");
                                    } else {
                                        try {
                                            CategoryDto cateDto = categoryFeignClient.add(categoryDto);
                                            categoryDto.setId(cateDto.getId());
                                            idNameCacheMap.put(attrValStr,
                                                    ListUtil.of(new IdNameCache(Convert.toStr(categoryDto.getId()),
                                                            categoryDto.getCategoryName(),
                                                            catePerm.getAuthorityType() == 0)));
                                            if (catePerm.getAuthorityType() == 0) {
                                                fieldData.setTarget(Convert.toStr(categoryDto.getId()));
                                            } else {
                                                fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                                            }
                                        } catch (Exception e) {
                                            log.error("资产分类自动新增异常, {}", e.getMessage(), e);
                                            fieldData.getErrMsg().add("资产分类自动新增异常，请重试");
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        } else {
                            fieldData.getErrMsg().add("当前数据不存在，请先添加");
                        }
                    } else if (idNameCaches.size() > 1) {
                        List<IdNameCache> collect = idNameCaches.stream().filter(IdNameCache::getHasPerm).collect(Collectors.toList());
                        // 多条数据都没权限
                        if (CollUtil.isEmpty(collect)) {
                            fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                        } else {
                            switch (fieldCO.getFieldType()) {
                                case FormFieldCO.YZC_EMP:
                                    if (collect.size() > 1) {
                                        fieldData.getErrMsg().add("当前数据有重名，请输入员工姓名和员工工号，示例：李白（001）");
                                    } else {
                                        IdNameCache idName = collect.get(0);
                                        fieldData.setTarget(idName.getId());
                                    }
                                    break;
                                case FormFieldCO.YZC_ORG:
                                    if (collect.size() > 1) {
                                        fieldData.getErrMsg().add("当前数据有重名，请输入组织名称和组织编码，示例：销售部（001）");
                                    } else {
                                        IdNameCache idName = collect.get(0);
                                        fieldData.setTarget(idName.getId());
                                    }
                                    break;
                                case FormFieldCO.YZC_ASSET_CATE:
                                    if (collect.size() > 1) {
                                        fieldData.getErrMsg().add("当前数据有重名，请输入资产分类名称和分类编码，示例：电子产品（001）");
                                    } else {
                                        IdNameCache idName = collect.get(0);
                                        fieldData.setTarget(idName.getId());
                                    }
                                    break;
                                case FormFieldCO.YZC_AREA:
                                    if (collect.size() > 1) {
                                        fieldData.getErrMsg().add("当前数据有重名，请输入区域名称和区域编码，示例：二楼（A01）");
                                    } else {
                                        IdNameCache idName = collect.get(0);
                                        fieldData.setTarget(idName.getId());
                                    }
                                    break;
                                default:
                                    fieldData.getErrMsg().add("当前数据有重名");
                            }
                        }
                    } else {
                        IdNameCache idName = idNameCaches.get(0);
                        if (idName.getHasPerm()) {
                            fieldData.setTarget(idName.getId());
                        } else {
                            fieldData.getErrMsg().add("当前数据你无数据权限，请更换");
                        }
                    }
                }
                fieldDataList.add(fieldData);
            });

            // 处理组织业务组件
            Map<String, AssetImportDto.FieldData> fieldDataMap = fieldDataList.stream().collect(Collectors.toMap(AssetImportDto.FieldData::getFieldCode, k -> k));
            codeToAssetAttrMap.forEach((attrCode, fieldCO) -> {
                if (FormFieldCO.YZC_ORG.equals(fieldCO.getFieldType())) {
                    // 组织数据
                    AssetImportDto.FieldData orgData = fieldDataMap.get(attrCode);
                    String relation = fieldCO.getFieldProps().getString("relation");
                    AssetImportDto.FieldData empData = fieldDataMap.get(relation);

                    if (orgData != null && empData != null && StrUtil.isNotBlank(Convert.toStr(empData.getTarget()))) {
                        Long orgId = Convert.toLong(orgData.getTarget(), -1L);
                        Long empId = Convert.toLong(empData.getTarget(), -1L);
                        Set<Long> empIds = orgRefEmpCache.get().getOrDefault(orgId, new HashSet<>());
                        if (!empIds.contains(empId)) {
                            orgData.getErrMsg().add(empData.getFieldName() + "不在当前组织【" + (ObjectUtil.isEmpty(orgData.getSource()) ? "空" : orgData.getSource()) + "】下，请检查");
                        }
                    }
                }
            });
            importDto.setFieldDataList(fieldDataList);
            importDto.setRowNum(idx + 1 - successNum.get());
            importDto.setFormFieldMap(formFieldMap.get());
            Boolean success = assetFeignClient.saveSheetData(importDto);
            if (BooleanUtil.isTrue(success)) {
                successNum.getAndIncrement();
            }
            importService.sendAssetMsg(importInfo.getCompanyId());
        });
        // 操作日志
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.IMP_MEANS, new AuditableImportResult(successNum.get())));
        // 记录本次任务
        SpringUtil.getBean(TaskRecordFeignClient.class).addTaskRecord(
                new TaskRecordDto().setCompanyId(importInfo.getCompanyId()).setName(importInfo.getFileName())
                        .setType(1).setImportType(DictConstant.IMPORT_TYPE_ASSET)
                        .setTotal(tableData.size()).setSuccessNum(successNum.get())
                        .setFailureNum(tableData.size() - successNum.get())
        );
        // 发送消息
        EventPublishHandler.publish(new ImportTaskEvent(new SendExternalNotice(LoginUserThreadLocal.getCompanyId(), NoticeConstant.ASSET_IMPORT_FAILED, NoticeConstant.DING_TALK)));
        // 资产入库记录
        if (successNum.get() != 0) {
            storeRecordFeignClient.save(StoreRecordFactory.createForMeans(StoreMode.IMPORT, taskId));
        }
    }

    private void saveLuckySheetHead(Long taskId, Map<Integer, FormFieldCO> headerMapping) {
        HeadImportErrorDto importErrorDto = new HeadImportErrorDto().setTaskId(taskId);
        List<LuckySheetModel> headModelList = new LinkedList<>();

        List<Integer> headerList = new ArrayList<>(headerMapping.keySet());
        headerList = headerList.stream().sorted().collect(Collectors.toList());

        AtomicInteger cellIndex = new AtomicInteger(0);
        for (Integer index : headerList) {
            FormFieldCO config = headerMapping.get(index);
            // 记录当前属性
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(0).setC(cellIndex.getAndIncrement());
            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(config.getFieldName());
            if (config.requiredProps()) {
                modelV.setFc("#ff0000");
            }
            headModelList.add(luckySheetModel);
        }
        importErrorDto.setHeadModelList(headModelList);
        this.assetFeignClient.saveSheetHead(importErrorDto);
    }

    /**
     * 清理本地缓存
     */
    private void clearThreadLocal() {
        dropDownCache.remove();
        orgRefEmpCache.remove();
        globalCache.remove();
        formFieldMap.remove();
    }

}
