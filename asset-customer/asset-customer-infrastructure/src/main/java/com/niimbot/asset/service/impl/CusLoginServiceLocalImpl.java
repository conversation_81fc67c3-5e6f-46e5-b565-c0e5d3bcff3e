package com.niimbot.asset.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.niimbot.asset.annotation.LoginRecord;
import com.niimbot.asset.customer.event.LoginSuccessEvent;
import com.niimbot.asset.framework.autoconfig.AssetConfig;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.model.LoginAndBandingBody;
import com.niimbot.asset.model.LoginUser;
import com.niimbot.asset.openapi.dto.AccessTokenDto;
import com.niimbot.asset.openapi.dto.GetAccessTokenDto;
import com.niimbot.asset.security.authentication.QrAuthenticationToken;
import com.niimbot.asset.security.authentication.SmsAuthenticationToken;
import com.niimbot.asset.security.enmu.QrCodeEnum;
import com.niimbot.asset.security.service.SysPermissionService;
import com.niimbot.asset.security.utils.SecurityUtils;
import com.niimbot.asset.service.AbstractDefaultLoginService;
import com.niimbot.asset.service.ThirdAuthService;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.asset.service.CusUserService;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.kalimdor.magneto.model.UserInfoResponse;
import com.niimbot.system.CompanyDto;
import com.niimbot.system.LoginByMobileResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2020/10/27 10:10
 */
@Slf4j
@Service
@Profile({Edition.LOCAL})
public class CusLoginServiceLocalImpl extends AbstractDefaultLoginService implements CusLoginService {

    private final AbstractTokenService tokenService;
    private final AuthenticationManager authenticationManager;
    private final AssetConfig assetConfig;
    @Resource
    private CusUserService userService;
    @Resource
    private CompanyFeignClient companyFeignClient;
    @Resource
    private SysPermissionService sysPermissionService;
    @Value("${asset.sso.ad.enable:false}")
    private Boolean adEnable;
    @Value("${asset.sso.ad.bean:x}")
    private String adBean;
    @Value("${asset.sso.cas.enable:false}")
    private Boolean casEnable;
    @Value("${asset.sso.cas.bean:x}")
    private String casBean;


    @Autowired
    public CusLoginServiceLocalImpl(AbstractTokenService tokenService,
                                    AuthenticationManager authenticationManager,
                                    AssetConfig assetConfig,
                                    CusUserFeignClient userFeignClient,
                                    RedisService redisService) {
        super(redisService, userFeignClient);
        this.tokenService = tokenService;
        this.authenticationManager = authenticationManager;
        this.assetConfig = assetConfig;
    }

    // TODO: 2022/1/24 极光推送ID绑定
    @Override
    @LoginRecord
    public String loginByEmployee(CusUserDto cusUserDto, String terminal) {
        LoginUserDto loginUser = new LoginUserDto();
        loginUser.setCusUser(cusUserDto);
        // 写入设备
        loginUser.setTerminal(terminal);
        super.loginAfterRecord(cusUserDto, terminal);
        // 生成token
        return tokenService.createToken(loginUser);
    }



    @Override
    public Map<String, Object> loginByThird(Map<String,Object> loginBody) {
        ThirdAuthService authService = null;
        String authType = loginBody.get("type").toString();
        // 判断是否开启域认证
        if(adEnable&&"ad".equals(authType)){
            authService = SpringUtil.getBean(adBean);
        }else if(casEnable&&"cas".equals(authType)){
            authService = SpringUtil.getBean(casBean);
        }else{
            authService = SpringUtil.getBean("defaultThirdAuthService");
        }
        if (authService == null || !"true".equals(authService.authenticate(loginBody).get("success"))) {
            throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
        }
        String account = loginBody.get("account").toString();
        try {
//            CusUserDto cusUserDto = userService.checkAccount(account);
            CusUserDto cusUserDto = userService.selectUserByUnionId( account);
            LoginUserDto loginUserDto = new LoginUserDto();
            loginUserDto.setCusUser(cusUserDto);
            loginBody.put("access_token", tokenService.createToken(loginUserDto));
        }catch (Exception e){
            throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
        }
        return loginBody;
    }

    /**
     * 登录验证
     *
     * @param account  用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @param terminal 设备
     * @return 结果
     */
    @Override
    public String loginByPwd(String account, String password, String code, String uuid, String terminal, String pushId) {
        // 判断错误次数，是否需要使用验证码
        String userErrorCount = BaseConstant.LOGIN_USER_ERROR_COUNT + account;
        Long count = Convert.toLong(redisService.get(userErrorCount));
        // 24小时内登录错误次数大于10，锁定账号密码登录
        if (count != null && count >= 10L) {
            throw new BusinessException(SystemResultCode.USER_ACCOUNT_LOCKED);
        }
        // 用户验证
        Authentication authentication;
        try {
            String rawPassword = SecurityUtils.decryptPassword(password);
            // 调用登录
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(account, rawPassword));
        } catch (Exception e) {
            // 密码错误异常抛出处理，需要统计失败次数
            if (e instanceof BadCredentialsException) {
                // 失败次数加一
                Long incr = redisService.incr(userErrorCount, 1);
                // 设置失效时间24小时
                redisService.expire(userErrorCount, 24, TimeUnit.HOURS);
                // 登录次数错误大于3次，需要验证码
                if (incr > 3L) {
                    throw new BusinessException(SystemResultCode.USER_ACCOUNT_CAPTCHA);
                } else {
                    throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
                }
            } else if (e instanceof BusinessException) {
                throw e;
            } else {
                throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
            }
        }
        // 获取登录信息
        LoginUser user = (LoginUser) authentication.getPrincipal();
        LoginUserDto loginUser = BeanUtil.copyProperties(user, LoginUserDto.class);
        // 注册推送
        EventPublishHandler.publish(new LoginSuccessEvent(user.getCusUser()).setJpushId(pushId));
        // 写入设备
        loginUser.setTerminal(terminal);
        // 登录成功删除错误次数记录
        delLoginCount(account);
        log.info("用户 {} 登录成功", loginUser.getCusUser().getAccount());
        super.loginAfterRecord(user.getCusUser(), terminal);
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 手机号登录验证
     *
     * @param mobile   用户名
     * @param sms      验证码
     * @param terminal 设备
     * @return 结果
     */
    @Override
    public LoginByMobileResult loginByMobile(String mobile, String sms, String terminal, String pushId) {
        // 用户验证
        Authentication authentication;
        try {
            // 调用手机验证码登录
            authentication = authenticationManager
                    .authenticate(new SmsAuthenticationToken(mobile, sms));
        } catch (Exception e) {
            if (e.getClass().isAssignableFrom(BusinessException.class)) {
                throw (BusinessException) e;
            } else {
                // 异常抛出处理
                throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
            }
        }
        LoginUser user = (LoginUser) authentication.getPrincipal();
        LoginUserDto loginUser = BeanUtil.copyProperties(user, LoginUserDto.class);
        // 注册推送
        EventPublishHandler.publish(new LoginSuccessEvent(user.getCusUser()).setJpushId(pushId));
        // 写入设备
        loginUser.setTerminal(terminal);
        log.info("用户 {} 登录成功", loginUser.getCusUser().getAccount());
        super.loginAfterRecord(user.getCusUser(), terminal);
        // 生成token
        String token = tokenService.createToken(loginUser);
        return new LoginByMobileResult().setAccess_token(token).setPreActivated(loginUser.getCusUser().isPreActivated()).setCompanyNames(loginUser.getCusUser().getCompanyNames());
    }

    /**
     * 获取登录用户信息
     *
     * @return 登录用户
     */
    @Override
    public LoginUserDto getLoginUser() {
        return tokenService.getLoginUser(ServletUtils.getRequest());
    }

    /**
     * 通过用户名查询用户
     *
     * @return 二维码
     */
    @Override
    public String getQrCode() {
        // 保存验证码信息
        String uuid = UUID.fastUUID().toString();
        String qrCodeKey = BaseConstant.QR_CODE_LOGIN + uuid;
        redisService.hSet(qrCodeKey, BaseConstant.QR_CODE_LOGIN_STATUS, QrCodeEnum.NOT_SCAN.getCode(), assetConfig.getQrCodeExpireTime());
        return uuid;
    }

    /**
     * 获取二维码状态
     *
     * @param uuid 二维码UUID
     * @return 状态
     */
    @Override
    public Map<String, Object> checkQrCode(String uuid) {
        return appCheckQrCode(uuid, status -> {
            // 二维码key
            String qrCodeKey = BaseConstant.QR_CODE_LOGIN + uuid;
            Map<String, Object> map = new HashMap<>();
            map.put(OAuth2AccessToken.ACCESS_TOKEN, StrUtil.EMPTY);
            QrCodeEnum qrStatus = EnumUtil.getEnumAt(QrCodeEnum.class, status);
            // 获取用户uuid登录
            if (qrStatus == QrCodeEnum.CONFIRM) {
                String employeeId = Convert.toStr(redisService.hGet(qrCodeKey, BaseConstant.QR_CODE_LOGIN_MOBILE));
                if (StrUtil.isEmpty(employeeId)) {
                    throw new BusinessException(SystemResultCode.USER_QR_MESSAGE_FAILED);
                }
                String token = loginByQr(employeeId);
                map.put(OAuth2AccessToken.ACCESS_TOKEN, token);
                // 删除登录完成的二维码
                log.info("手机号 {} 扫码登录成功", employeeId);
                redisService.del(qrCodeKey);
            } else if (qrStatus == QrCodeEnum.CANCEL) {
                throw new BusinessException(SystemResultCode.USER_QR_CANCEL);
            }
            return map;
        });
    }

    /**
     * 二维码登录
     *
     * @param employeeId 手机号
     * @return 结果
     */
    private String loginByQr(String employeeId) {
        // 用户验证
        Authentication authentication = null;
        try {
            // 调用登录
            authentication = authenticationManager
                    .authenticate(new QrAuthenticationToken(employeeId));
        } catch (Exception e) {
            if (e.getClass().isAssignableFrom(BusinessException.class)) {
                throw (BusinessException) e;
            } else {
                throw new BusinessException(SystemResultCode.USER_LOGIN_ERROR);
            }
        }
        // 获取登录信息
        LoginUser user = (LoginUser) authentication.getPrincipal();
        LoginUserDto loginUser = BeanUtil.copyProperties(user, LoginUserDto.class);
        loginUser.setTerminal(AssetConstant.TERMINAL_PC);
        super.loginAfterRecord(user.getCusUser(), AssetConstant.TERMINAL_PC);
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * APP扫码
     *
     * @param uuid 二维码UUID
     * @return 状态
     */
    @Override
    public Map<String, Object> scanQrCode(String uuid) {
        return appCheckQrCode(uuid, status -> {
            // 二维码key
            String qrCodeKey = BaseConstant.QR_CODE_LOGIN + uuid;
            QrCodeEnum qrStatus = EnumUtil.getEnumAt(QrCodeEnum.class, status);
            // 二维码未被扫描，写入当前用户Id
            if (qrStatus == QrCodeEnum.NOT_SCAN) {
                redisService.hSet(qrCodeKey, BaseConstant.QR_CODE_LOGIN_STATUS, QrCodeEnum.SCANNED.getCode(), assetConfig.getQrCodeExpireTime());
            } else if (qrStatus == QrCodeEnum.SCANNED) {
                throw new BusinessException(SystemResultCode.USER_QR_SCANNED);
            } else {
                throw new BusinessException(SystemResultCode.USER_QR_INVALID);
            }
            return MapUtil.empty();
        });
    }

    /**
     * 确认登录
     *
     * @param uuid    二维码UUID
     * @param confirm 是否登陆
     * @return 状态
     */
    @Override
    public Map<String, Object> loginByConfirm(String uuid, boolean confirm) {
        return appCheckQrCode(uuid, status -> {
            // 二维码key
            String qrCodeKey = BaseConstant.QR_CODE_LOGIN + uuid;
            QrCodeEnum qrStatus = EnumUtil.getEnumAt(QrCodeEnum.class, status);
            // 二维码已被扫描，写入当前用户Id
            if (qrStatus == QrCodeEnum.SCANNED) {
                Long employeeId = SecurityUtils.getLoginUser().getCusUser().getId();
                redisService.hSet(qrCodeKey, BaseConstant.QR_CODE_LOGIN_MOBILE, String.valueOf(employeeId), assetConfig.getQrCodeExpireTime());
                redisService.hSet(qrCodeKey, BaseConstant.QR_CODE_LOGIN_STATUS,
                        confirm ? QrCodeEnum.CONFIRM.getCode() : QrCodeEnum.CANCEL.getCode(), assetConfig.getQrCodeExpireTime());
            } else {
                throw new BusinessException(SystemResultCode.USER_QR_INVALID);
            }
            return MapUtil.empty();
        });
    }

    @Override
    public UserInfoResponse socialBind(String unionId, String provider, String appId, String code) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

    @Override
    public UserInfoResponse socialUnbind(String unionId, String provider) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

    @Override
    public String loginBySocial(String provider, String appId, String code, String terminal, String pushId) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

    @Override
    public String loginAndBanding(LoginAndBandingBody loginAndBandingBody) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

    @Override
    public AccessTokenDto loginByOpenApi(GetAccessTokenDto getAccessTokenDto) {
        // 获取登录信息
        LoginUser user = loadUserByAppKey(getAccessTokenDto.getAppKey(), getAccessTokenDto.getAppSecret());
        LoginUserDto loginUser = BeanUtil.copyProperties(user, LoginUserDto.class);
        loginUser.setTerminal(AssetConstant.TERMINAL_OPEN_API);
        // 生成token
        String token = tokenService.createToken(loginUser);
        AccessTokenDto tokenDto = new AccessTokenDto();
        tokenDto.setAccessToken(token);
        tokenDto.setAppName(cacheResourceUtil.getUserName(loginUser.getCusUser().getId()));
        tokenDto.setCompanyId(loginUser.getCusUser().getCompanyId().toString());
        return tokenDto;
    }



    private LoginUser loadUserByAppKey(String appKey, String appSecret) {
        // 获取账户信息
        CusUserDto cusUser = userService.selectUserByAppKey(appKey, appSecret);
        // 账户信息不存在
        if (cusUser == null) {
            log.info("登录用户：appKey : {} 不存在.", appKey);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, appKey + "的配置信息不存在");
        } else {
            CompanyDto companyInfo = companyFeignClient.getCompanyInfo(cusUser.getCompanyId());
            if (ManageConstant.COMPANY_STATUS_DISABLE == companyInfo.getStatus()) {
                throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN);
            }
        }
        return new LoginUser(cusUser, sysPermissionService.getMenuPermission(cusUser));
    }

    /**
     * 校验二维码状态
     *
     * @param uuid 二维码UUID
     * @return 结果集
     */
    private Map<String, Object> appCheckQrCode(String uuid, Function<Integer, Map<String, Object>> fun) {
        // 二维码key
        String qrCodeKey = BaseConstant.QR_CODE_LOGIN + uuid;
        // 查询二维码UUID状态
        Integer status = Convert.toInt(redisService.hGet(qrCodeKey, BaseConstant.QR_CODE_LOGIN_STATUS));
        //检查redis是否中还存在二维码内容
        if (status == null) {
            throw new BusinessException(SystemResultCode.USER_QR_INVALID);
        } else {
            return fun.apply(status);
        }
    }
}
