package com.niimbot.asset.security.interceptor;

import com.niimbot.asset.framework.utils.ThreadLocalUtil;

import org.springframework.stereotype.Component;

import java.util.Map;

import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * <AUTHOR>
 * @since 2020/10/21 8:39
 */
@Component
public class FeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        Map<String, String> headerMap = ThreadLocalUtil.get();
        for (String key : headerMap.keySet()) {
            requestTemplate.header(key, headerMap.get(key));
        }
    }
}
