package com.niimbot.asset.controller.app.means;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionGeneralDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/5 14:49
 */
@Slf4j
@Validated
@Api(tags = "资产字段管理")
@ResultController
@RequestMapping("api/app/queryField/asset")
@RequiredArgsConstructor
public class AssetQueryFieldAppController {

    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @ApiOperation(value = "【APP】筛选项配置-保存")
    @PostMapping("/query/field")
    @RepeatSubmit
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@RequestBody List<String> conditions) {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_QUERY);
        if (one != null) {
            JSON conditionJson = one.getConditions();
            QueryConditionGeneralDto generalDto = conditionJson.toJavaObject(QueryConditionGeneralDto.class);
            generalDto.setConditions(conditions);
            one.setConditions(generalDto.toJson());
        } else {
            one = new QueryConditionConfigDto();
            one.setType(QueryFieldConstant.TYPE_ASSET_QUERY);
            QueryConditionGeneralDto generalDto = new QueryConditionGeneralDto();
            generalDto.setConditions(conditions);
            one.setConditions(generalDto.toJson());
        }
        return queryConditionConfigFeignClient.saveOrUpdate(one);
    }

    @ApiOperation(value = "【APP】筛选项配置-查询")
    @GetMapping("/query/field")
    public List<String> getQueryField() {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_ASSET_QUERY);
        JSON conditionJson = one.getConditions();
        QueryConditionGeneralDto generalDto = conditionJson.toJavaObject(QueryConditionGeneralDto.class);
        return generalDto.getConditions();
    }

}
