package com.niimbot.asset.service.feign;

import com.niimbot.material.MaterialOrderFieldDto;
import com.niimbot.material.MaterialOrderTypeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/7 16:02
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface MaterialOrderSettingFeignClient {

    /**
     * 耗材单据类型查询
     *
     * @return 单据列表
     */
    @GetMapping(value = "server/material/orderSetting/orderType/list")
    List<MaterialOrderTypeDto> listOrderType();

    /**
     * 此类型是否开启审批流
     *
     * @param orderType 单据类型
     * @return 是否开启审批流
     */
    @GetMapping(value = "server/material/orderSetting/orderType/enableWorkflow/{orderType}")
    Boolean enableWorkflow(@PathVariable("orderType") Integer orderType);

    /**
     * 单据字段查询
     *
     * @param orderType
     * @return
     */
    @GetMapping(value = "server/material/orderSetting/orderField/list/{orderType}")
    List<MaterialOrderFieldDto> listOrderField(@PathVariable("orderType") Integer orderType);

    /**
     * 耗材表单字段/列表字段列表
     *
     * @param orderType
     * @return
     */
    @GetMapping(value = "server/material/orderSetting/dynamicField/list/{orderType}")
    List<MaterialOrderFieldDto> listDynamicOrderField(@PathVariable("orderType") Integer orderType);

    /**
     * 更新耗材单据类型
     *
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "server/material/orderSetting/updateOrderType")
    Boolean updateOrderType(MaterialOrderTypeDto dto);


    /**
     * 单据类型详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "server/material/orderSetting/orderType/getById/{id}")
    MaterialOrderTypeDto getOrderTypeById(@PathVariable("id") Long id);

    /**
     * 批量更新单据字段
     *
     * @param dtos
     * @return 结果
     */
    @PutMapping(value = "server/material/orderSetting/updateOrderField/batch")
    Boolean updateOrderFieldBatch(List<MaterialOrderFieldDto> dtos);

    /**
     * 批量更新单据字段
     *
     * @param dto
     * @return 结果
     */
    @PutMapping(value = "server/material/orderSetting/updateOrderField")
    Boolean updateOrderField(MaterialOrderFieldDto dto);

    @GetMapping(value = "server/material/orderSetting/orderType/app/list")
    List<MaterialOrderTypeDto> appListOrderTypeShow();
}
