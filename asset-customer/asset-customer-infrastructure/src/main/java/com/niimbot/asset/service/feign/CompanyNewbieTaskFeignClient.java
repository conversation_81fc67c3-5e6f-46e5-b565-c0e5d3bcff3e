package com.niimbot.asset.service.feign;

import com.niimbot.system.CompanyNewbieTaskDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CompanyNewbieTaskFeignClient {

    @GetMapping("server/system/company/newbie/task/list/{companyId}")
    List<CompanyNewbieTaskDto> tasks(@PathVariable("companyId") Long companyId);

    @PutMapping("server/system/company/newbie/task/completed/{id}")
    <PERSON><PERSON><PERSON> completed(@PathVariable("id") Long id);

    @PutMapping("server/system/company/newbie/task/ignored/{id}")
    <PERSON><PERSON><PERSON> ignored(@PathVariable("id") Long id);

    @GetMapping("server/system/company/newbie/task/progressRate/{companyId}")
    Map<String, Integer> progressRate(@PathVariable("companyId") Long companyId);

    @GetMapping("server/system/company/newbie/task/isAllProcessed/{companyId}")
    Boolean isAllProcessed(@PathVariable("companyId") Long companyId);
}
