package com.niimbot.asset.controller.pc.purchase;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.activiti.WorkflowApproveInfoDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.annotation.AuditLog;
import com.niimbot.asset.framework.annotation.AutoConvert;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.DictConvertUtil;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.framework.utils.OrderJsonUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.service.PurchaseOrderService;
import com.niimbot.asset.service.excel.ExportResponse;
import com.niimbot.asset.service.feign.ActWorkflowFeignClient;
import com.niimbot.asset.service.feign.PurchaseCommonFeignClient;
import com.niimbot.asset.service.feign.PurchaseOrderFeignClient;
import com.niimbot.asset.utils.AsOrderUtil;
import com.niimbot.asset.utils.DesensitizationDataUtil;
import com.niimbot.dynamicform.FormPrintDetailRequestDto;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.SensitiveObjectTypeEnum;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.purchase.PurchaseDetailDataConvertDto;
import com.niimbot.purchase.PurchaseDetailPageQueryDto;
import com.niimbot.purchase.PurchaseLinkAmountQueryDto;
import com.niimbot.purchase.PurchaseLinkStoreAmountDto;
import com.niimbot.purchase.PurchaseOrderDetailDto;
import com.niimbot.purchase.PurchaseOrderDto;
import com.niimbot.purchase.PurchaseOrderQueryDto;
import com.niimbot.purchase.PurchaseOrderResponseDto;
import com.niimbot.purchase.PurchaseOrderSubmitDto;
import com.niimbot.system.Auditable;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 采购单前端控制器
 *
 * <AUTHOR>
 * @date 2021/5/24 16:05
 */
@Slf4j
@Api(tags = "采购单管理")
@ResultController
@RequestMapping("api/pc/purchase/order")
@RequiredArgsConstructor
public class PurchaseOrderController {
    private final PurchaseOrderFeignClient purchaseOrderFeignClient;
    private final DictConvertUtil dictConvertUtil;
    private final AsOrderUtil orderUtil;
    private final PurchaseOrderService purchaseOrderService;
    private final PurchaseCommonFeignClient purchaseCommonFeignClient;
    private final ActWorkflowFeignClient workflowFeignClient;
    @Autowired
    private DesensitizationDataUtil desensitizationDataUtil;

    @ApiOperation(value = "设置审批流查询")
    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(@RequestBody @Validated PurchaseOrderDto dto) {
        return purchaseOrderFeignClient.getWorkflowStepList(dto);
    }

    @ApiOperation(value = "创建采购单")
    @PostMapping
    @RepeatSubmit
    @ResultMessage(ResultConstant.SAVE_SUCCESS)
    public Boolean create(@RequestBody @Validated PurchaseOrderSubmitDto dto) {
        return purchaseOrderFeignClient.create(dto);
    }

    @ApiOperation(value = "采购单详情")
    @GetMapping("/{id}")
    @AutoConvert
    public PurchaseOrderResponseDto getById(@PathVariable("id") Long id) {
        PurchaseOrderDto orderDto = purchaseOrderFeignClient.getById(id);
        if (ObjectUtil.isNull(orderDto)) {
            return new PurchaseOrderResponseDto()
                    .setOrder(new JSONObject())
                    .setApproveInfo(new WorkflowApproveInfoDto());
        }
        orderDto.setOrderType(AssetConstant.ORDER_TYPE_PURCHASE_ORDER);
        dictConvertUtil.convertToDictionary(orderDto);
        WorkflowApproveInfoDto approveInfoDto = null;
        if (!DictConstant.NO_APPROVE_PROCESS.equals(orderDto.getApproveStatus())) {
            if (DictConstant.WAIT_APPROVE.equals(orderDto.getApproveStatus())) {
                approveInfoDto = workflowFeignClient.getRunWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_PURCHASE_ORDER, id);
                if (approveInfoDto != null && approveInfoDto.getExecuteList().isEmpty()) {
                    approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_PURCHASE_ORDER, id);
                }
            } else {
                approveInfoDto = workflowFeignClient.getHiWorkflowApproveInfo((short) AssetConstant.ORDER_TYPE_PURCHASE_ORDER, id);
                if (approveInfoDto != null && CollUtil.isNotEmpty(approveInfoDto.getExecuteList())) {
                    approveInfoDto.getExecuteList().stream()
                            .filter(step -> step.getType() == 8)
                            .forEach(step -> {
                                step.setStatus(orderDto.getApproveStatus());
                                dictConvertUtil.convertToDictionary(step);
                            });
                }
            }
        }
        JSONObject orderInfo = orderUtil.toJSONObject(orderDto);
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(Collections.singletonList(orderInfo), SensitiveObjectTypeEnum.PURCHASE.getCode());
        return new PurchaseOrderResponseDto()
                .setOrder(orderInfo)
                .setApproveInfo(ObjectUtil.isNull(approveInfoDto) ? new WorkflowApproveInfoDto() : approveInfoDto);
    }

    @ApiOperation(value = "采购单批量详情")
    @AutoConvert
    @AuditLog(Auditable.Action.DO_PRINT_TPL)
    @PostMapping("/detailBatch")
    public List<PurchaseOrderResponseDto> getDetailByIds(@RequestBody @Validated FormPrintDetailRequestDto requestDto) {
        requestDto.setOrderType(AssetConstant.ORDER_TYPE_PURCHASE_ORDER);
        return requestDto.getOrderIds().stream().map(this::getById).collect(Collectors.toList());
    }

    @ApiOperation(value = "采购单分页")
    @PostMapping("/page")
    public PageUtils<JSONObject> page(@RequestBody PurchaseOrderQueryDto query) {
        PageUtils<PurchaseOrderDto> page = purchaseOrderFeignClient.page(query);
        dictConvertUtil.convertToDictionary(page.getList());
        List<JSONObject> list = new ArrayList<>();
        for (PurchaseOrderDto orderDto : page.getList()) {
            orderDto.setOrderType(12);
            list.add(orderUtil.toJSONObject(orderDto));
        }
        //数据脱敏处理
        desensitizationDataUtil.handleSensitiveField(list, SensitiveObjectTypeEnum.PURCHASE.getCode());
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getCurrPage());
    }

    @ApiOperation(value = "采购单明细分页查询")
    @GetMapping("/detail/page")
    public PageUtils<JSONObject> pageDetailGet(PurchaseDetailPageQueryDto dto) {
        return innerPageDetail(dto);
    }

    @ApiOperation(value = "采购单明细分页查询")
    @PostMapping("/detail/page")
    public PageUtils<JSONObject> pageDetail(@RequestBody PurchaseDetailPageQueryDto dto) {
        return innerPageDetail(dto);
    }

    private PageUtils<JSONObject> innerPageDetail(PurchaseDetailPageQueryDto dto) {
        if (ObjectUtil.isNull(dto.getOrderId())) {
            return new PageUtils<>();
        }
        PageUtils<PurchaseOrderDetailDto> purchaseResult = purchaseOrderFeignClient.pageDetail(dto);
        PageUtils<JSONObject> result = new PageUtils<>();
        BeanUtils.copyProperties(purchaseResult, result);
        //数据脱敏处理
        if (CollUtil.isNotEmpty(purchaseResult.getList())) {
            List<PurchaseOrderDetailDto> list = purchaseResult.getList();
            dictConvertUtil.convertToDictionary(list);
            List<JSONObject> dataList = list.stream().map(JsonUtil::toJsonObject).collect(Collectors.toList());
            desensitizationDataUtil.handleSensitiveField(dataList, SensitiveObjectTypeEnum.PURCHASE.getCode());
            result.setList(dataList);
        } else {
            result.setList(Collections.emptyList());
        }
        return result;
    }

    @ApiOperation(value = "物品数据转换")
    @PostMapping("/detail/convert")
    @AutoConvert
    public List<JSONObject> dataConvert(@RequestBody @Validated PurchaseDetailDataConvertDto dto) {
        return purchaseOrderFeignClient.dataConvert(dto);
    }

    @ApiOperation(value = "采购数量明细查询")
    @PostMapping("/link/apply")
    public List<JSONObject> listApplyAmount(@RequestBody @Validated(PurchaseLinkAmountQueryDto.Apply.class) PurchaseLinkAmountQueryDto dto) {
        return purchaseOrderFeignClient.listApplyAmount(dto).stream().map(amountDto -> JsonUtil.toJsonObject(amountDto, json -> {
            json.fluentPutAll(amountDto.getOrderData());
            json.fluentRemove("orderData");
        })).collect(Collectors.toList());
    }

    @ApiOperation(value = "入库中数量明细")
    @PostMapping("/link/store")
    public List<PurchaseLinkStoreAmountDto> listStoreAmount(@RequestBody @Validated(PurchaseLinkAmountQueryDto.Store.class) PurchaseLinkAmountQueryDto dto) {
        return purchaseOrderFeignClient.listStoreAmount(dto);
    }

    @ApiOperation(value = "【PC】导出采购申请单据")
    @PostMapping(value = "/exportOrder/card")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrder(@RequestBody PurchaseOrderQueryDto query) {
        query.setOrderType(AssetConstant.ORDER_TYPE_PURCHASE_ORDER);
        return purchaseOrderService.exportOrderCard(query);
    }

    @ApiOperation(value = "【PC】导出采购申请单据明细")
    @PostMapping(value = "/exportOrder/assets")
    @ResultMessage(ResultConstant.OPERATION_SUCCESS)
    public ExportResponse exportOrderAssets(@RequestBody @Validated PurchaseOrderQueryDto dto) {
        dto.setOrderType(AssetConstant.ORDER_TYPE_PURCHASE_ORDER);
        PageUtils<PurchaseOrderDto> page = purchaseOrderFeignClient.page(dto);
        List<PurchaseOrderDto> list = page.getList();
        if (CollUtil.isEmpty(list)) {
            BusinessExceptionUtil.throwException("无单据数据, 无法导出");
        }
        list.forEach(o -> o.setOrderType(AssetConstant.ORDER_TYPE_PURCHASE_ORDER));
        dictConvertUtil.convertToDictionary(list);
        FormVO orderForm = purchaseCommonFeignClient.getForm(dto.getOrderType());
        Map<String, String> orderDateFormatType = new HashMap<>();
        Set<String> orderMultiSelectSet = new HashSet<>();
        Map<String, Boolean> orderNumberMap = new HashMap<>();
        for (FormFieldCO it : orderForm.getFormFields()) {
            if (FormFieldCO.DATETIME.equals(it.getFieldType())) {
                orderDateFormatType.put(it.getFieldCode(), it.getFieldProps().getString("dateFormatType"));
            }
            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(it.getFieldType())) {
                orderMultiSelectSet.add(it.getFieldCode());
            }
            if (FormFieldCO.NUMBER_INPUT.equals(it.getFieldType())) {
                orderNumberMap.put(it.getFieldCode(), "2".equals(it.getFieldProps().getString("numberFormatType")));
            }
        }
        orderDateFormatType.put(QueryFieldConstant.FIELD_CREATE_TIME, "yyyy-MM-dd");
        orderDateFormatType.put(QueryFieldConstant.FIELD_UPDATE_TIME, "yyyy-MM-dd");
        List<Long> orderIds = new ArrayList<>();
        List<JSONObject> orders = new ArrayList<>();
        for (PurchaseOrderDto order : list) {
            orderIds.add(order.getId());
            JSONObject orderJson = orderUtil.toJSONObject(order);
            if (Objects.nonNull(order.getCreateTime())) {
                long timeVal = order.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                if (timeVal > 0L) {
                    orderJson.putIfAbsent("createTime", timeVal);
                }
            }
            orderNumberMap.forEach((code, percentage) -> {
                Number number = Convert.toNumber(orderJson.get(code));
                if (ObjectUtil.isNotNull(number)) {
                    if (BooleanUtil.isTrue(percentage)) {
                        orderJson.put(code, number + "%");
                    } else {
                        orderJson.put(code, number);
                    }
                }
            });
            orderDateFormatType.forEach((code, fmt) -> {
                String date = orderJson.getString(code);
                if (StrUtil.isNotEmpty(date)) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Convert.toLong(date, 0L) / 1000, 0, ZoneOffset.of("+8"));
                        orderJson.put(code, dateTime.format(DateTimeFormatter.ofPattern(fmt)));
                    } catch (Exception e) {
                        log.warn("[{}] [{}]转换时间异常", code, date);
                    }
                }
            });
            orderMultiSelectSet.forEach(code -> {
                try {
                    JSONArray jsonArray = orderJson.getJSONArray(code);
                    if (CollUtil.isNotEmpty(jsonArray)) {
                        List<String> strings = jsonArray.toJavaList(String.class);
                        String collect = String.join(",", strings);
                        orderJson.put(code, collect);
                    } else {
                        orderJson.put(code, null);
                    }
                } catch (Exception e) {
                    log.warn("[{}] [{}]转换数组异常", code, orderJson.get(code));
                }
            });
            orders.add(orderJson);
        }

        //敏感数据处理
        desensitizationDataUtil.handleSensitiveField(orders, SensitiveObjectTypeEnum.PURCHASE.getCode());

        OrderJsonUtil.convertJsonKey(AssetConstant.ORDER_FIELD_EXPORT_TYPE_PREFIX, orders);

        List<PurchaseOrderDetailDto> details = purchaseOrderFeignClient.listDetailsByOrderId(orderIds);
        dictConvertUtil.convertToDictionary(details);
        Map<Long, List<PurchaseOrderDetailDto>> detailMap = details.stream().collect(
                Collectors.groupingBy(PurchaseOrderDetailDto::getPurchaseOrderId));
        Map<Long, List<JSONObject>> detailJsonMap = new HashMap<>();
        detailMap.forEach((key, detailDtoList) -> {
            List<JSONObject> detailJsons = new ArrayList<>();
            for (PurchaseOrderDetailDto detail : detailDtoList) {
                JSONObject assetJson = JsonUtil.toJsonObject(detail);
                detailJsons.add(assetJson);
            }
            detailJsonMap.put(key, detailJsons);
        });
        return purchaseOrderService.exportOrderDetail(dto, orders, detailJsonMap);
    }
}
