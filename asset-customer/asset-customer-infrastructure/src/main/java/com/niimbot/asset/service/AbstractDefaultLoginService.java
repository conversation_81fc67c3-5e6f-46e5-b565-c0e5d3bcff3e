package com.niimbot.asset.service;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.service.feign.CusUserFeignClient;
import com.niimbot.asset.support.AuditLogs;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.Auditable;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录后置处理 <AUTHOR>
 *
 * @since 2021/3/26 14:58
 */
public abstract class AbstractDefaultLoginService extends AbstractLoginService {

    private final CusUserFeignClient userFeignClient;

    @Resource
    protected CacheResourceUtil cacheResourceUtil;

    public AbstractDefaultLoginService(RedisService redisService,
                                       CusUserFeignClient userFeignClient) {
        super(redisService);
        this.userFeignClient = userFeignClient;
    }

    /**
     * 登录后处理记录 记录当前登录时间，上次登录时间，登录次数
     */
    protected void loginAfterRecord(CusUserDto cusUserDto, String terminal) {
        userFeignClient.loginAfterRecord(cusUserDto.getId());
        Map<String, Object> tplParams = new HashMap<>(2);
        tplParams.put(Auditable.Tpl.NAME, cacheResourceUtil.getUserName(cusUserDto.getId()));
        tplParams.put(Auditable.Tpl.TIME, LocalDateTime.now().format(Auditable.FORMATTER));
        AuditLogs.sendRecord(AuditLogRecord.create(Auditable.Action.LOGIN, tplParams, cusUserDto, terminal));
    }

}
