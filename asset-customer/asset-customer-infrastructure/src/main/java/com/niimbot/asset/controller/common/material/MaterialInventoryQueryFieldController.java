package com.niimbot.asset.controller.common.material;

import com.alibaba.fastjson.JSON;
import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.MaterialQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionGeneralDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/5 14:03
 */
@Slf4j
@Validated
@Api(tags = "耗材盘点字段管理")
@ResultController
@RequestMapping("api/common/queryField/material/inventory")
@RequiredArgsConstructor
public class MaterialInventoryQueryFieldController {

    private final MaterialQueryFieldService queryFieldService;
    private final AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @ApiOperation(value = "【pc】筛选项配置-保存")
    @RepeatSubmit
    @PostMapping("/query/field")
    @ResultMessage(ResultConstant.ADD_SUCCESS)
    public Boolean queryField(@RequestBody QueryConditionGeneralDto condition) {
        return queryConditionConfigFeignClient.saveOrUpdate(new QueryConditionConfigDto(condition.toJson(), QueryFieldConstant.TYPE_MATERIAL_INVENTORY_QUERY));
    }

    @ApiOperation(value = "【pc】筛选项配置-查询")
    @GetMapping("/query/field")
    public QueryConditionGeneralDto getQueryField() {
        QueryConditionConfigDto one = queryConditionConfigFeignClient.getByType(QueryFieldConstant.TYPE_MATERIAL_INVENTORY_QUERY);
        JSON conditionJson = one.getConditions();
        return conditionJson.toJavaObject(QueryConditionGeneralDto.class);
    }

    @ApiOperation(value = "筛选项配置-所有字段")
    @GetMapping("/query/field/all")
    public List<QueryConditionDto> inventoryAllQueryField() {
        return queryFieldService.inventoryAllQueryField();
    }

    @ApiOperation(value = "筛选项-渲染")
    @GetMapping("/query/field/view")
    public List<QueryConditionDto> inventoryQueryView() {
        return queryFieldService.inventoryQueryView();
    }

}
