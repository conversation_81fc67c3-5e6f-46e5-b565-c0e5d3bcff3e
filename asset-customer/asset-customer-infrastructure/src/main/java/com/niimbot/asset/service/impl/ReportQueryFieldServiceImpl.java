package com.niimbot.asset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.service.ReportQueryFieldService;
import com.niimbot.asset.service.feign.AsQueryConditionConfigFeignClient;
import com.niimbot.asset.service.feign.AssetQueryFieldFeignClient;
import com.niimbot.asset.service.feign.FormFeignClient;
import com.niimbot.asset.service.feign.StandardFeignClient;
import com.niimbot.asset.service.feign.report.PredefinedReportFeignClient;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.AssetHeadDto;
import com.niimbot.report.ReportEnum;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionGeneralDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryConditionStandardDto;
import com.niimbot.system.QueryHeadConfigDto;
import com.niimbot.system.QueryTypeDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/27 11:02
 */
@Slf4j
@Service
public class ReportQueryFieldServiceImpl implements ReportQueryFieldService {

    @Autowired
    private FormFeignClient formFeignClient;

    @Autowired
    private AssetQueryFieldFeignClient assetQueryFieldFeignClient;

    @Autowired
    private StandardFeignClient standardFeignClient;

    @Autowired
    private PredefinedReportFeignClient predefinedReportFeignClient;

    @Autowired
    private AsQueryConditionConfigFeignClient queryConditionConfigFeignClient;

    @Value("${asset.upload.domain}")
    private String domain;

    @Override
    public List<QueryConditionDto> reportAllQueryField(String type) {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_STANDARD));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));

        //额外添加处置日期和到期日期
        JSONObject fieldProps = new JSONObject();
        fieldProps.put("dateFormatType", "yyyy-MM-dd");
        QueryConditionDto expireDate = new QueryConditionDto()
                .setCode("expireDate").setName("到期日期").setFixedField(Boolean.TRUE)
                .setType(FormFieldCO.DATETIME).setFieldProps(fieldProps);
        QueryConditionDto handleDate = new QueryConditionDto()
                .setCode("handleDate").setName("处置日期").setFixedField(Boolean.TRUE)
                .setType(FormFieldCO.DATETIME).setFieldProps(fieldProps);
        queryConditionDtos.add(expireDate);
        queryConditionDtos.add(handleDate);

        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
//            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
            if (FormFieldCO.SPLIT_LINE.equals(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }

        // 补齐 标准品，创建人，创建时间，更新时间
        FormBaseFieldCO empField = formFeignClient.getBaseFieldByType(FormFieldCO.YZC_EMP);
        QueryConditionDto createBy = toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            queryConditionDtos.add(createBy);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    @Override
    public List<QueryConditionDto> reportQueryView(String type) {
        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> reportAllQueryField = reportAllQueryField(type);
        Map<String, QueryConditionDto> reportAllQueryFieldMap = reportAllQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        ReportEnum reportEnum = ReportEnum.getByType(type);
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(reportEnum.getQuery());
        QueryConditionGeneralDto generalDto = configDto.getConditions().toJavaObject(QueryConditionGeneralDto.class);
        generalDto.getConditions().forEach(f -> {
            if (reportAllQueryFieldMap.containsKey(f)) {
                result.add(reportAllQueryFieldMap.get(f));
            }
        });

        // 判断是否有标准品
        if (generalDto.getStandardId() != null && generalDto.getStandardId() > 0L) {
            QueryConditionStandardDto standardDto = standardAllField(FormFeignClient.BIZ_TYPE_ASSET,
                    generalDto.getStandardId(), false, false);
            Map<String, QueryConditionDto> standardAllFieldMap = standardDto.getConditions()
                    .stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
            generalDto.getStandardCondition().forEach(f -> {
                if (standardAllFieldMap.containsKey(f)) {
                    result.add(standardAllFieldMap.get(f));
                }
            });
        }
        return result;
    }

    @Override
    public List<QueryConditionDto> reportAllHeadField(String type) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        return reportAllHeadField(type, formVO);
    }

    @Override
    public List<AssetHeadDto> reportHeadView(String type) {
        FormVO formVO = formFeignClient.getTplByType(FormFeignClient.BIZ_TYPE_ASSET);
        ReportEnum reportEnum = ReportEnum.getByType(type);
        QueryConditionSortDto querySort = predefinedReportFeignClient.assetSortField(reportEnum.getCode());
        List<String> querySortList = querySort.getSortList().stream()
                .map(QueryConditionSortDto.Field::getValue).collect(Collectors.toList());

        List<QueryConditionDto> result = new ArrayList<>();
        List<QueryConditionDto> assetAllHeadField = reportAllHeadField(type, formVO);
        Map<String, QueryConditionDto> assetAllQueryFieldMap = assetAllHeadField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
        // 查询配置项
        QueryConditionConfigDto configDto = queryConditionConfigFeignClient.getByType(reportEnum.getHead());
        QueryHeadConfigDto generalDto = configDto.getConditions().toJavaObject(QueryHeadConfigDto.class);
        List<String> selectConditions = generalDto.getConditions();
        selectConditions.forEach(f -> {
            if (assetAllQueryFieldMap.containsKey(f)) {
                result.add(assetAllQueryFieldMap.get(f));
            }
        });

        Map<String, String> transCodeMap = new HashMap<>();
        transCodeMap.put(QueryFieldConstant.FIELD_CREATE_BY, QueryFieldConstant.FIELD_CREATE_BY + "Text");
        Map<String, JSONObject> fieldPropsMap = new HashMap<>();
        JSONObject dateFormat = new JSONObject();
        dateFormat.put("dateFormatType", "yyyy-MM-dd");
        fieldPropsMap.put(QueryFieldConstant.FIELD_CREATE_TIME, dateFormat);
        fieldPropsMap.put(QueryFieldConstant.FIELD_UPDATE_TIME, dateFormat);
        fieldPropsMap.put(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME, dateFormat);
        for (FormFieldCO formFieldCO : formVO.getFormFields()) {
            fieldPropsMap.put(formFieldCO.getFieldCode(), formFieldCO.getFieldProps());
            if (StrUtil.isNotEmpty(formFieldCO.getTranslationCode())) {
                transCodeMap.put(formFieldCO.getFieldCode(), formFieldCO.getTranslationCode());
            }
        }

        for (QueryConditionDto queryConditionDto : assetAllHeadField) {
            if (!fieldPropsMap.containsKey(queryConditionDto.getCode())) {
                fieldPropsMap.put(queryConditionDto.getCode(), queryConditionDto.getFieldProps());
            }
        }

        // 判断是否有标准品
        if (generalDto.getStandardId() != null && generalDto.getStandardId() > 0L) {
            QueryConditionStandardDto standardDto = standardAllField(FormFeignClient.BIZ_TYPE_ASSET,
                    generalDto.getStandardId(), false, true);
            Map<String, QueryConditionDto> standardAllFieldMap = standardDto.getConditions()
                    .stream().collect(Collectors.toMap(QueryConditionDto::getCode, k -> k));
            generalDto.getStandardCondition().forEach(f -> {
                if (standardAllFieldMap.containsKey(f)) {
                    QueryConditionDto queryCondition = standardAllFieldMap.get(f);
                    result.add(queryCondition);
                    fieldPropsMap.put(queryCondition.getCode(), queryCondition.getFieldProps());
                }
            });
        }

        List<AssetHeadDto> headList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            QueryConditionDto conditionDto = result.get(i);
            AssetHeadDto headDto = new AssetHeadDto();
            headDto.setCode(conditionDto.getCode());
            headDto.setName(conditionDto.getName());
            headDto.setType(conditionDto.getType());
            headDto.setIsLock(false);
            headDto.setFieldProps(fieldPropsMap.get(conditionDto.getCode()));
            headDto.setSort(querySortList.contains(conditionDto.getCode()));
            String tranCode = transCodeMap.get(conditionDto.getCode());
            if (FormFieldCO.NUMBER_INPUT.equals(conditionDto.getType())
                    && "2".equals(conditionDto.getFieldProps().getString("numberFormatType"))) {
                tranCode = "%";
            }
            headDto.setTranslationCode(
                    StrUtil.isNotEmpty(tranCode) ? tranCode : "");
            if (i < generalDto.getLockNum()) {
                headDto.setIsLock(true);
            }
            headList.add(headDto);
        }
        return headList;
    }

    @Override
    public QueryConditionStandardDto standardAllField(String baseType, Long standardId, boolean needName, boolean filterFile) {
        QueryConditionStandardDto standardDto = new QueryConditionStandardDto();
        if (standardId == null || standardId == 0L) {
            return standardDto;
        }
        FormVO assetForm = formFeignClient.getTplByType(baseType);
        if (needName) {
            FormVO form = standardFeignClient.form(standardId, false);
            if (ObjectUtil.isNotNull(form)) {
                standardDto.setStandardName(form.getFormName());
            }
        }
        List<FormFieldCO> standardExtField = standardFeignClient.getStandardExtField(assetForm.getFormId(), standardId);
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        List<String> filterType = new ArrayList<>();
        filterType.add(FormFieldCO.SPLIT_LINE);
        if (filterFile) {
            filterType.add(FormFieldCO.FILES);
        }
        List<QueryConditionDto> collect = standardExtField.stream().filter(f ->
                !filterType.contains(f.getFieldType())
                        && !f.isHidden()
        ).map(f -> {
            QueryConditionDto queryConditionDto = buildQueryCondition(f);
            queryConditionDto.setOperators(operatorMap.get(queryConditionDto.getType()));
            return queryConditionDto;
        }).collect(Collectors.toList());
        standardDto.setConditions(collect);
        return standardDto;
    }

    private List<QueryConditionDto> reportAllHeadField(String type, FormVO formVO) {
        ReportEnum reportEnum = ReportEnum.getByType(type);
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();
        switch (reportEnum) {
            case WAIT_HANDLE_ASSET_LOG:
            case REPAIR_ASSET_LOG:
            case REPAIR_ASSET_RECORD:
                queryConditionDtos.addAll(reportEnum.getCondition(null));
                break;
            case HANDLE_ASSET_LOG:
                queryConditionDtos.addAll(reportEnum.getCondition(formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(8).getBizType())));
                break;
            case WAIT_RETURN_ASSET_LOG:
                queryConditionDtos.addAll(reportEnum.getCondition(formFeignClient.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(3).getBizType())));
                break;
            default:
                break;
        }

        // 补齐 资产状态，创建人，创建时间，更新时间，最近打印时间
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));
        List<FormFieldCO> formFields = formVO.getFormFields();

        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = buildQueryCondition(formFieldCO);
            queryConditionDtos.add(queryConditionDto);
        }
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        queryConditionDtos.add(toQueryConditionDto(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldFeignClient.getOperatorMap();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    private QueryConditionDto buildQueryCondition(FormFieldCO formFieldCO) {
        return new QueryConditionDto()
                .setName(formFieldCO.getFieldName())
                .setCode(formFieldCO.getFieldCode())
                .setType(formFieldCO.getFieldType())
                .setFieldProps(formFieldCO.getFieldProps());
    }

    private QueryConditionDto toQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ASSET_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

}
