package com.niimbot.asset.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.AsEmployeeResignRecordsDto;
import com.niimbot.system.CusEmployeeChangeDto;
import com.niimbot.system.CusEmployeeChangeQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/16 11:15
 */
@FeignClient(url = "${feign.remote}", name = "asset-platform")
public interface CusEmployeeChangeFeignClient {

    /**
     * 查询异动分页列表
     *
     * @param query 条件
     * @return 分页
     */
    @GetMapping(value = "server/system/employee/change/page")
    PageUtils<CusEmployeeChangeDto> page(@SpringQueryMap CusEmployeeChangeQueryDto query);

    /**
     * 查询资产异动分页列表
     *
     * @param query 条件
     * @return 分页
     */
    @GetMapping(value = "server/system/employee/change/asset")
    PageUtils<JSONObject> changeAsset(@SpringQueryMap CusEmployeeChangeQueryDto query);

    /**
     * 查询删除操作记录
     *
     * @param changeId 异动Id
     * @return 分页
     */
    @GetMapping(value = "server/system/employee/change//operation/{changeId}")
    List<AsEmployeeResignRecordsDto> operation(@PathVariable("changeId") Long changeId);

}
