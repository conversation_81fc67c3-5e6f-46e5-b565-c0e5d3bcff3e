package com.niimbot.asset.controller.common.thirdparty;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.service.feign.ThirdpartyCallbackFeignClient;
import com.niimbot.thirdparty.DingCallbackDto;
import com.niimbot.thirdparty.FeishuCallbackDto;
import com.niimbot.thirdparty.WeChatCallbackDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/19 15:04
 */
@Slf4j
@Api(tags = "【三方对接】回调")
@RestController
@RequestMapping("api/common/thirdparty/callback")
@RequiredArgsConstructor
public class CallbackController {

    private final ThirdpartyCallbackFeignClient callbackFeignClient;

    @PostMapping("/dingtalk/{companyId}")
    public Map<String, String> dingCallback(@PathVariable("companyId") Long companyId,
                                            @RequestParam(value = "signature") String signature,
                                            @RequestParam(value = "timestamp") Long timestamp,
                                            @RequestParam(value = "nonce") String nonce,
                                            @RequestBody(required = false) JSONObject body) {
        DingCallbackDto dingCallbackDto = new DingCallbackDto().setCompanyId(companyId)
                .setSignature(signature)
                .setTimestamp(timestamp)
                .setNonce(nonce)
                .setBody(body);
        return callbackFeignClient.dingCallback(dingCallbackDto);
    }

    @GetMapping("/wechat/{companyId}")
    public String wechatCallbackGet(@PathVariable("companyId") Long companyId,
                                    @RequestParam(name = "msg_signature", required = false) String signature,
                                    @RequestParam(name = "timestamp", required = false) String timestamp,
                                    @RequestParam(name = "nonce", required = false) String nonce,
                                    @RequestParam(name = "echostr", required = false) String echostr) {
        return "success";
    }

    @PostMapping(value = "/wechat/{companyId}", produces = "application/xml; charset=UTF-8")
    public String wechatCallbackPost(@PathVariable("companyId") Long companyId,
                                     @RequestBody String requestBody,
                                     @RequestParam("msg_signature") String signature,
                                     @RequestParam("timestamp") String timestamp,
                                     @RequestParam("nonce") String nonce) {
        return "success";
    }

    @GetMapping(value = "/wechat", produces = "application/xml; charset=UTF-8")
    public String wechatCallGetRequest(
            @RequestParam("msg_signature") String signature,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam("echostr") String echostr
    ) {
        WeChatCallbackDto wechatCallbackDto = new WeChatCallbackDto()
                .setSignature(signature)
                .setTimestamp(timestamp)
                .setNonce(nonce)
                .setEchostr(echostr);
        return callbackFeignClient.wechatCallGetRequest(wechatCallbackDto);
    }

    @PostMapping(value = "/wechat", produces = "application/xml; charset=UTF-8")
    public String wechatCallPostRequest(
            @RequestBody String requestBody,
            @RequestParam("msg_signature") String signature,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce
    ) {
        WeChatCallbackDto wechatCallbackDto = new WeChatCallbackDto()
                .setSignature(signature)
                .setTimestamp(timestamp)
                .setNonce(nonce)
                .setRequestBody(requestBody);
        return callbackFeignClient.wechatCallPostRequest(wechatCallbackDto);
    }

    @RequestMapping("/feishu/{companyId}")
    public JSONObject feishuCallback(HttpServletRequest request,
                                     @PathVariable("companyId") Long companyId,
                                     @RequestBody JSONObject body) {
        FeishuCallbackDto feishuCallbackDto = new FeishuCallbackDto();
        feishuCallbackDto.setTimestamp(request.getHeader("X-Lark-Request-Timestamp"))
                .setNonce(request.getHeader("X-Lark-Request-Nonce"))
                .setSignature(request.getHeader("X-Lark-Signature"))
                .setCompanyId(companyId)
                .setBody(body);
        return callbackFeignClient.feishuCallback(feishuCallbackDto);
    }


}
