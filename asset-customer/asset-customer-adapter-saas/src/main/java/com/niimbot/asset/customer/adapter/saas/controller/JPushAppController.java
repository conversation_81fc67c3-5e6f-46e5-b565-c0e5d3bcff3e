package com.niimbot.asset.customer.adapter.saas.controller;

import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.middlend.core.component.message.MessageBuilder;
import com.niimbot.middlend.core.component.message.bean.JPushAddress;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/10 16:00
 */

@Slf4j
@Api(tags = {"手机登录接口"})
@ResultController
@RequestMapping("api/app/jpush")
public class JPushAppController {

    @Resource
    private MessageBuilder messageBuilder;

    @PostMapping("/bind/{pushId}")
    @ApiOperation(value = "绑定极光Push")
    public Boolean bindPushId(@PathVariable(value = "pushId") String pushId) {
        try {
            messageBuilder.registerAddress(new JPushAddress(String.valueOf(LoginUserThreadLocal.getCurrentUserId()), pushId));
            return true;
        } catch (Exception e) {
            log.error("员工{}, pushId {} 绑定失败", LoginUserThreadLocal.getCurrentUserId(), pushId, e);
            return false;
        }
    }

    @PostMapping("/unbind/{pushId}")
    @ApiOperation(value = "解绑极光Push")
    public Boolean unbindPushId(@PathVariable(value = "pushId") String pushId) {
        try {
            messageBuilder.removeAddress(new JPushAddress(String.valueOf(LoginUserThreadLocal.getCurrentUserId()), pushId));
            return true;
        } catch (Exception e) {
            log.error("员工{}, pushId {} 解绑失败", LoginUserThreadLocal.getCurrentUserId(), pushId, e);
            return false;
        }
    }

}
