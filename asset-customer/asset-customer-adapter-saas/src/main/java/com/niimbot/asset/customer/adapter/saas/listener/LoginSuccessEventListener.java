package com.niimbot.asset.customer.adapter.saas.listener;

import com.niimbot.asset.customer.event.LoginSuccessEvent;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.middlend.core.component.message.MessageBuilder;
import com.niimbot.middlend.core.component.message.bean.JPushAddress;

import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class LoginSuccessEventListener implements ApplicationListener<LoginSuccessEvent> {

    @Resource
    private MessageBuilder messageBuilder;

    @Override
    @Async
    public void onApplicationEvent(LoginSuccessEvent event) {
        String jpushId = event.getJpushId();
        CusUserDto source = (CusUserDto) event.getSource();
        if (StrUtil.isNotBlank(jpushId)) {
            try {
                messageBuilder.registerAddress(new JPushAddress(String.valueOf(source.getId()), jpushId));
            } catch (Exception e) {
                log.error("推送服务id绑定失败", e);
            }
        }
    }
}
