package com.niimbot.asset.customer.adapter.saas.controller;

import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.service.feign.CompanyFeignClient;
import com.niimbot.asset.service.feign.ThirdpartyFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.system.CompanySettingDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Api(tags = "【saas版本特殊接口】")
@ResultController
@RequestMapping("/api/saas")
@RequiredArgsConstructor
public class SaasSpecialController {

    private final CompanyFeignClient companyFeignClient;

    private final ThirdpartyFeignClient thirdpartyFeignClient;

    @ApiOperation("开启从第三方同步审批角色")
    @PutMapping("/enableSyncApproveRole/{enable}")
    public Boolean enableSyncApproveRole(@PathVariable("enable") Boolean enable) {
        Boolean flag = companyFeignClient.enableSyncApproveRole(enable);
        if (enable && flag) {
            thirdpartyFeignClient.manualSyncFromDing(LoginUserThreadLocal.getCompanyId());
            return true;
        }
        return false;
    }

    @ApiOperation("开启从第三方同步审批角色")
    @PutMapping("/manualSyncFromDing")
    public Boolean manualSyncFromDing() {
        CompanySettingDto setting = companyFeignClient.selectCompanySettingById(LoginUserThreadLocal.getCompanyId());
        if (Objects.isNull(setting) || Objects.isNull(setting.getExpandSwitch()) || !setting.getExpandSwitch().getEnableSyncApproveRole()) {
            return false;
        }
        return thirdpartyFeignClient.manualSyncFromDing(LoginUserThreadLocal.getCompanyId());
    }

}
