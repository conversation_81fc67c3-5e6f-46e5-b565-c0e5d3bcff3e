package com.niimbot.asset.customer.adapter.saas.controller;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.framework.annotation.RepeatSubmit;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.service.CusLoginService;
import com.niimbot.asset.service.feign.CusRegisterFeignClient;
import com.niimbot.asset.service.feign.SmsCodeFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.RecommendRegisterDto;
import com.niimbot.validate.NationalCodeValidate;

import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/1 13:47
 */
@Slf4j
@Api(tags = "saas邀请注册")
@ResultController
@RequestMapping("/api/common/recommend")
@RequiredArgsConstructor
public class SaasRecommendController {

    @Resource
    private CusRegisterFeignClient registerFeignClient;

    @Resource
    private SmsCodeFeignClient smsCodeFeignClient;

    @Resource
    private RedisService redisService;

    @Resource
    private CusLoginService loginService;

    @ApiOperation(value = "企业注册", httpMethod = "POST")
    @RepeatSubmit
    @PostMapping("/register")
    @ResultMessage("注册成功")
    public Map<String, String> recommendRegister(@RequestBody @Validated(RecommendRegisterDto.SaasChannel.class) RecommendRegisterDto dto) {
        log.info("企业邀请注册");
        NationalCodeValidate.checkCNMobile(dto.getNationalCode(), dto.getMobile());
        if (!smsCodeFeignClient.checkSmsCode(dto.getMobile(), dto.getSmsCode())) {
            throw new BusinessException(SystemResultCode.USER_VERIFY_ERROR);
        }
        //校验区号和手机号一致
        String registerKey = "register_key:" + dto.getMobile();
        if (redisService.hasKey(registerKey)) {
            if (!redisService.get(registerKey).equals(dto.getNationalCode())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "国际区号与手机号不匹配，请检查国际区号！");
            }
        }
        // saas渠道
        dto.setSaasChannel(true);
        registerFeignClient.recommendRegister(dto);
        String token = loginService.loginByMobile(dto.getMobile(), dto.getSmsCode(), AssetConstant.TERMINAL_PC, StrUtil.EMPTY).getAccess_token();
        return ImmutableMap.of(
                OAuth2AccessToken.ACCESS_TOKEN, token
        );
    }

}
