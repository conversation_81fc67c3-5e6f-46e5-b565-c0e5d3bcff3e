package com.niimbot.asset.customer.adapter.saas.mq;

import com.aliyun.openservices.ons.api.SendResult;
import com.niimbot.asset.framework.annotation.MessageProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.AbstractRocketMqProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqPropertiesConfig;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.system.SendExternalNotice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@MessageProducer(topic = MqConstant.ASSET_TOPIC, group = MqConstant.ASSET_EXTERNAL_NOTICE_PRODUCER_GROUP)
public class ExternalNoticeProducer extends AbstractRocketMqProducer {

    @Resource
    private ThreadPoolTaskExecutor assetTaskExecutor;

    public ExternalNoticeProducer(RocketMqPropertiesConfig propertiesConfig) {
        super(propertiesConfig);
    }

    public void sendMessage(SendExternalNotice sendExternalNotice) {
        String event = sendExternalNotice.getEvent();
        Long uid = sendExternalNotice.getUid();
        try {
            SendResult sendResult = this.sendMsg(MqConstant.ASSET_EXTERNAL_NOTICE_SEND, event + "_" + uid + "_" + System.currentTimeMillis(), sendExternalNotice);
            log.info("Send External Notice Success By RockerMq : Message[msgId:{}]", sendResult.getMessageId());
        } catch (Exception e) {
            log.error("Send External Notice Error By RockerMq : Details[event:{} uid:{}]", sendExternalNotice.getEvent(), sendExternalNotice.getUid(), e);
        }
    }

    public void asyncSendMessage(SendExternalNotice sendExternalNotice) {
        assetTaskExecutor.execute(() -> sendMessage(sendExternalNotice));
    }
}
