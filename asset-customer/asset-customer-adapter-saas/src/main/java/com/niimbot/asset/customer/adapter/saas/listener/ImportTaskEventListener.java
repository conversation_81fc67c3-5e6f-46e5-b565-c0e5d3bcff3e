package com.niimbot.asset.customer.adapter.saas.listener;

import com.niimbot.asset.customer.adapter.saas.mq.ExternalNoticeProducer;
import com.niimbot.asset.customer.event.ImportTaskEvent;
import com.niimbot.system.SendExternalNotice;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class ImportTaskEventListener implements ApplicationListener<ImportTaskEvent> {

    @Resource
    private ExternalNoticeProducer externalNoticeProducer;

    @Override
    public void onApplicationEvent(ImportTaskEvent event) {
        SendExternalNotice source = (SendExternalNotice) event.getSource();
        externalNoticeProducer.asyncSendMessage(source);
    }
}
