package com.niimbot.asset.customer.adapter.saas.controller;

import com.niimbot.asset.service.feign.InventoryAssetFeignClient;
import com.niimbot.jf.core.component.annotation.ResultController;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "APP盘点管理")
@ResultController
@RequestMapping("api/app/inventory")
@RequiredArgsConstructor
public class SaasInventoryAppController {

    private final InventoryAssetFeignClient inventoryAssetFeignClient;

    // @ApiOperation(value = "判断资产id是否在盘点单内")
    // @GetMapping(value = "/checkAssetId")
    // public Boolean checkAssetId(
    //         @ApiParam(name = "assetId", value = "资产Id")
    //         @RequestParam("assetId") String assetId,
    //         @ApiParam(name = "inventoryId", value = "盘点单id")
    //         @RequestParam(value = "inventoryId") String inventoryId) {
    //     long assetIdLong;
    //     long inventoryIdLong;
    //     try {
    //         assetIdLong = Long.parseLong(assetId);
    //         inventoryIdLong = Long.parseLong(inventoryId);
    //     } catch (Exception e) {
    //         log.warn("string convert to long error ==> {}", e.getMessage());
    //         return false;
    //     }
    //
    //     return inventoryAssetFeignClient.checkAssetId(assetIdLong, inventoryIdLong);
    // }

}
