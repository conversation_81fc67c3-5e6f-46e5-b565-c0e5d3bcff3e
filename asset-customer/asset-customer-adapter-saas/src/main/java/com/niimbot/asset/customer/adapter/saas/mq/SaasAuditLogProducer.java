package com.niimbot.asset.customer.adapter.saas.mq;

import com.aliyun.openservices.ons.api.SendResult;
import com.niimbot.asset.framework.annotation.MessageProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.AbstractRocketMqProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqPropertiesConfig;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.service.AuditLogService;
import com.niimbot.system.AuditLogRecord;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@MessageProducer(topic = MqConstant.ASSET_TOPIC, group = "GID_asset-common-audit-log_p")
public class SaasAuditLogProducer extends AbstractRocketMqProducer implements AuditLogService {

    public SaasAuditLogProducer(RocketMqPropertiesConfig propertiesConfig) {
        super(propertiesConfig);
    }

    @Override
    public void sendRecord(AuditLogRecord record) {
        try {
            String key = record.getActionCode() + "_" + record.getId();
            SendResult result = this.sendMsg(Edition.SAAS + "_" + TAG, key, record);
            log.info("Send Audit Log Success By RockerMq : Message[msgId:{}]", result.getMessageId());
        } catch (Exception e) {
            log.error("Send Audit Log Error By RockerMq : Details[actionCode:{} operatorId:{}]", record.getActionCode(), record.getOperatorId(), e);
        }
    }
}
