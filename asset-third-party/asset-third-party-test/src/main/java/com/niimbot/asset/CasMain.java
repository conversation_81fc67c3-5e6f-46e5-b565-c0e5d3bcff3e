package com.niimbot.asset;

import java.io.*;
import java.net.*;
import java.util.regex.*;

public class CasMain {
    // 配置参数（可根据实际环境修改）
    private static final String CAS_LOGIN_URL = "http://oaqas.shineway.com:8068"; // 泛微CAS服务地址
    private static final String CAS_SERVICE = "http://zc.shineway.com/"; // 业务service
    private static final String USERNAME = "00010913"; // 测试用户名
    private static final String PASSWORD = "sw+0066"; // 测试密码
    private static final boolean DEBUG = false; // 生产环境请设为false

    public static void main(String[] args) {
        System.out.println("==== 泛微CAS认证流程测试 ====");
        String tgt = getTgc(DEBUG, CAS_LOGIN_URL, CAS_SERVICE, USERNAME, PASSWORD);
        if (tgt == null) {
            System.err.println("[失败] 获取TGT失败，请检查用户名、密码或CAS服务地址");
            return;
        }
        System.out.println("TGT: " + tgt);
        String st = getTicket(DEBUG, CAS_LOGIN_URL, tgt);
        if (st == null) {
            System.err.println("[失败] 获取ST失败，请检查TGT或CAS服务");
            return;
        }
        System.out.println("ST: " + st);
        String user = getUsername(DEBUG, st);
        if (user == null) {
            System.err.println("[失败] 校验ST获取用户信息失败，ST无效或CAS服务异常");
            return;
        }
        System.out.println("认证成功，用户名/工号: " + user);
    }

    // 以下为CasUtils的全部实现
    public static String getTgc(Boolean debug, String casLoginUrl, String casService, String username, String password) {
        if (debug) return "getTgc";
        try {
            URL url = new URL(casLoginUrl + "/sso/v1/tickets");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            String params = "username=" + URLEncoder.encode(username, "UTF-8") + "&password=" + URLEncoder.encode(password, "UTF-8");
            OutputStream os = conn.getOutputStream();
            os.write(params.getBytes());
            os.flush();
            os.close();
            int code = conn.getResponseCode();
            if (code == 201) {
                String location = conn.getHeaderField("Location");
                if (location != null) {
                    int idx = location.lastIndexOf('/');
                    if (idx != -1) {
                        return location.substring(idx + 1);
                    }
                }
            } else if (code == 400) {
                System.err.println("[TGT失败] 用户名或密码错误");
            } else {
                System.err.println("[TGT失败] HTTP响应码: " + code);
            }
        } catch (Exception e) {
            System.err.println("[TGT异常] " + e.getMessage());
        }
        return null;
    }

    public static String getTicket(Boolean debug, String casLoginUrl, String casTgc) {
        if (debug) return "getTicket";
        try {
            String tgt = casTgc.startsWith("TGT-") ? casTgc : "TGT-" + casTgc;
            URL url = new URL(casLoginUrl + "/sso/v1/tickets/" + tgt);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            String params = "service=" + URLEncoder.encode(CAS_SERVICE, "UTF-8") + "&appid=ZC";
            OutputStream os = conn.getOutputStream();
            os.write(params.getBytes());
            os.flush();
            os.close();
            int code = conn.getResponseCode();
            if (code == 200) {
                BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                String st = in.readLine();
                in.close();
                if (st != null && st.startsWith("ST-")) {
                    return st.trim();
                }
            } else if (code == 404) {
                System.err.println("[ST失败] TGT无效或已过期");
            } else {
                System.err.println("[ST失败] HTTP响应码: " + code);
            }
        } catch (Exception e) {
            System.err.println("[ST异常] " + e.getMessage());
        }
        return null;
    }

    public static String getUsername(Boolean debug, String ticket) {
        if (debug) return "18888888888";
        try {
            URL url = new URL(CAS_LOGIN_URL + "/sso/proxyValidate");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            String params = "ticket=" + URLEncoder.encode(ticket, "UTF-8") + "&service=" + URLEncoder.encode(CAS_SERVICE, "UTF-8");
            OutputStream os = conn.getOutputStream();
            os.write(params.getBytes());
            os.flush();
            os.close();
            int code = conn.getResponseCode();
            if (code == 200) {
                BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
                StringBuilder sb = new StringBuilder();
                String line;
                while ((line = in.readLine()) != null) sb.append(line);
                in.close();
                Pattern p = Pattern.compile("<cas:user>(.*?)</cas:user>");
                Matcher m = p.matcher(sb.toString());
                if (m.find()) {
                    return m.group(1);
                }
                // 失败情况
                if (sb.toString().contains("<cas:authenticationFailure")) {
                    System.err.println("[用户失败] ST无效或已过期");
                }
            } else {
                System.err.println("[用户失败] HTTP响应码: " + code);
            }
        } catch (Exception e) {
            System.err.println("[用户异常] " + e.getMessage());
        }
        return null;
    }
}
