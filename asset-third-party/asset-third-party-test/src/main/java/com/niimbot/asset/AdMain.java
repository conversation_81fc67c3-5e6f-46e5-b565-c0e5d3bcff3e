package com.niimbot.asset;


import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.util.Hashtable;

public class AdMain {
    public static void main(String[] args) throws Exception {
        // AD域认证所需参数
        String domain = "shineway.com";       // 域名
        String server = "domain.shineway.com";    // 域控制器地址
        String username = "zc";              // 域账号用户名
        String password = "swzc+2025";        // 域账号密码

        // 尝试认证
        boolean authenticated = authenticate(domain, server, username, password);

        // 输出结果
        System.out.println("===========================================");
        System.out.println("AD域认证结果: " + (authenticated ? "成功" : "失败"));
        System.out.println("===========================================");

        if (authenticated) {
            System.out.println("用户 " + username + " 已通过 " + domain + " 域认证");
        } else {
            System.out.println("认证失败，请检查：");
            System.out.println("1. 用户名/密码是否正确");
            System.out.println("2. 域控制器地址是否正确");
            System.out.println("3. 网络连接是否正常");
            System.out.println("4. 服务器端口是否开放（389或636）");
        }

    }

    public static boolean authenticate(String domain, String server, String username, String password) {
        // 构建用户DN (Distinguished Name)
        String userDN = username + "@" + domain;
        // 另一种格式：CN=User Name,OU=Users,DC=yourdomain,DC=com

        // 构建LDAP URL 如果使用SSL/TLS，使用 ldaps:// 和端口636
        String ldapUrl = "ldap://" + server + ":389";
        // 设置LDAP环境参数
        Hashtable<String, String> env = getStringStringHashtable(password, ldapUrl, userDN);

        try {
            // 尝试连接和认证
            DirContext ctx = new InitialDirContext(env);
            ctx.close();
            return true;
        } catch (NamingException e) {
            System.err.println("认证错误: " + e.getMessage());
            return false;
        }
    }

    private static Hashtable<String, String> getStringStringHashtable(String password, String ldapUrl, String userDN) {
        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl);
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, userDN);
        env.put(Context.SECURITY_CREDENTIALS, password);

        // 可选：设置超时时间（毫秒）
        env.put("com.sun.jndi.ldap.connect.timeout", "5000");
        env.put("com.sun.jndi.ldap.read.timeout", "5000");
        return env;
    }
}