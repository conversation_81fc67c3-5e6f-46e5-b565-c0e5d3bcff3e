package com.niimbot.asset;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CasUtils {
    private final String casLoginUrl;
    private final String casService;
    private final String casAppId;
    private final boolean debug;

    public CasUtils(String casLoginUrl, String casService, String casAppId, boolean debug) {
        this.casLoginUrl = casLoginUrl;
        this.casService = casService;
        this.casAppId = casAppId;
        this.debug = debug;
    }

    public static void main(String[] args) {
        // 正确的测试参数（请根据实际环境调整）
        String casLoginUrl = "http://oaqas.shineway.com:8068";
        String casService = "http://zc.shineway.com/";
        String casAppId = "ZC";
        String username = "00010913";
        String password = "sw+0066";
        boolean debug = false;
        CasUtils casUtils = new CasUtils(casLoginUrl, casService, casAppId, debug);
        try {
            String tgt = casUtils.getTgc(username, password);
            System.out.println("TGT: " + tgt);
            String st = casUtils.getTicket(tgt);
            System.out.println("ST: " + st);
            String user = casUtils.getUsername(st);
            System.out.println("认证成功，用户名/工号: " + user);
        } catch (Exception e) {
            System.err.println("CAS认证流程失败: " + e.getMessage());
        }
    }

    public String getTgc(String username, String password) {
        if (debug) return "getTgc";
        try {
            HttpResponse response = HttpRequest.post(casLoginUrl + "/sso/v1/tickets")
                    .form("username", username)
                    .form("password", password)
                    .execute();
            int code = response.getStatus();
            if (code == 201) {
                String location = response.header("Location");
                if (location != null) {
                    int idx = location.lastIndexOf('/');
                    if (idx != -1) {
                        return location.substring(idx + 1);
                    }
                }
            } else if (code == 400) {
                System.out.println("[TGT失败] 用户名或密码错误");
                throw new RuntimeException("TGT失败: 用户名或密码错误");
            } else {
                System.out.println("[TGT失败] HTTP响应码: " + code);
                throw new RuntimeException("TGT失败: HTTP响应码: " + code);
            }
        } catch (Exception e) {
            System.out.println("[TGT异常] " + e.getMessage());
            throw new RuntimeException("TGT异常: " + e.getMessage(), e);
        }
        System.out.println("[TGT失败] 未知错误");
        throw new RuntimeException("TGT失败: 未知错误");
    }

    public String getTicket(String casTgc) {
        if (debug) return "getTicket";
        try {
            String tgt = casTgc.startsWith("TGT-") ? casTgc : "TGT-" + casTgc;
            String url = casLoginUrl + "/sso/v1/tickets/" + tgt;
            HttpResponse response = HttpRequest.post(url)
                    .form("service", casService)
                    .form("appid", casAppId)
                    .execute();
            int code = response.getStatus();
            if (code == 200) {
                String st = response.body();
                if (st != null && st.startsWith("ST-")) {
                    return st.trim();
                } else {
                    System.out.println("[ST失败] 响应内容无效: " + st);
                    throw new RuntimeException("ST失败: 响应内容无效");
                }
            } else if (code == 404) {
                System.out.println("[ST失败] TGT无效或已过期");
                throw new RuntimeException("ST失败: TGT无效或已过期");
            } else {
                System.out.println("[ST失败] HTTP响应码: " + code);
                throw new RuntimeException("ST失败: HTTP响应码: " + code);
            }
        } catch (Exception e) {
            System.out.println("[ST异常] " + e.getMessage());
            throw new RuntimeException("ST异常: " + e.getMessage(), e);
        }
    }

    public String getUsername(String ticket) {
        if (debug) return "18888888888";
        try {
            HttpResponse response = HttpRequest.post(casLoginUrl + "/sso/proxyValidate")
                    .form("ticket", ticket)
                    .form("service", casService)
                    .execute();
            int code = response.getStatus();
            String body = response.body();
            if (code == 200) {
                Pattern p = Pattern.compile("<cas:user>(.*?)</cas:user>");
                Matcher m = p.matcher(body);
                if (m.find()) {
                    return m.group(1);
                }
                if (body.contains("<cas:authenticationFailure")) {
                    System.out.println("[用户失败] ST无效或已过期, 响应: " + body);
                    throw new RuntimeException("用户失败: ST无效或已过期");
                }
                System.out.println("[用户失败] 未找到用户信息, 响应: " + body);
                throw new RuntimeException("用户失败: 未找到用户信息");
            } else {
                System.out.println("[用户失败] HTTP响应码: " + code);
                throw new RuntimeException("用户失败: HTTP响应码: " + code);
            }
        } catch (Exception e) {
            System.out.println("[用户异常] " + e.getMessage());
            throw new RuntimeException("用户异常: " + e.getMessage(), e);
        }
    }
}
