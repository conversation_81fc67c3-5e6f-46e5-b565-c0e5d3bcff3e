package com.niimbot.asset.thirdparty.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.thirdparty.model.AsThirdParty;
import com.niimbot.thirdparty.BindThirdPartyResult;
import com.niimbot.thirdparty.DingtalkBindDto;
import com.niimbot.thirdparty.DingtalkReplenish;
import com.niimbot.thirdparty.FeishuBindDto;
import com.niimbot.thirdparty.WeChatBindDto;

import me.chanjar.weixin.cp.api.WxCpService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface AsThirdPartyService extends IService<AsThirdParty> {

    Boolean getInitSync(Long companyId);

    BindThirdPartyResult bindDingtalk(DingtalkBindDto dingtalk);

    Boolean replenishDingtalk(DingtalkReplenish dto);

    Boolean removeDingtalk();

    BindThirdPartyResult bindWeChat(WeChatBindDto wechat);

    Boolean removeWeChat(Long companyId, boolean removeCheck);

    BindThirdPartyResult bindFeishu(FeishuBindDto feishu);

    Boolean removeFeishu();

    JSONObject getSecret(Long companyId, String type);

    AsThirdParty getWeChatBind(String corpId);

    Long getWechatCompany(String cropId);

    boolean unbindWechat(String corpId);

    WxCpService getWeChatService(Long companyId);

    boolean getThirdPartyInitSync(Long companyId, String type);

    String getDingToken(Long companyId);

}
