package com.niimbot.asset.thirdparty.support;

import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiRoleGetroleRequest;
import com.dingtalk.api.request.OapiRoleGetrolegroupRequest;
import com.dingtalk.api.request.OapiRoleListRequest;
import com.dingtalk.api.request.OapiRoleSimplelistRequest;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiRoleGetroleResponse;
import com.dingtalk.api.response.OapiRoleGetrolegroupResponse;
import com.dingtalk.api.response.OapiRoleListResponse;
import com.dingtalk.api.response.OapiRoleSimplelistResponse;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.thirdparty.restops.RestOps;
import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.URLUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DingApiSupport {

    @Resource
    private AsThirdPartyService thirdPartyService;

    public String sendInnerLinkMessage(String title, String text, String url, List<String> userIds, JSONObject form, Long companyId) {
        if (CollUtil.isEmpty(userIds)) {
            log.warn("send dingtalk inner message userIds is empty");
            return "non";
        }
        try {
            String ad = "dingtalk://dingtalkclient/page/link?url=" +
                    URLUtil.encodeAll(URLUtil.decode(url)) +
                    "&target=workbench";
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(form.getLong("agentId"));
            req.setUseridList(String.join(",", userIds));
            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = getMsg(title, text, ad);
            req.setMsg(msg);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, thirdPartyService.getDingToken(companyId));
            if (!rsp.isSuccess()) {
                log.error("发送工作通知出错 ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
            }
            return String.valueOf(rsp.getTaskId());
        } catch (Exception e) {
            log.error("DingApiSupport sendInnerLinkMessage error", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "");
        }
    }

    private OapiMessageCorpconversationAsyncsendV2Request.Msg getMsg(String title, String text, String url) {
        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype("link");
        OapiMessageCorpconversationAsyncsendV2Request.Link link = new OapiMessageCorpconversationAsyncsendV2Request.Link();
        link.setPicUrl(url);
        link.setMessageUrl(url);
        link.setText(text);
        link.setTitle(title);
        msg.setLink(link);
        return msg;
    }

    public List<OapiRoleListResponse.OpenRoleGroup> getRoles(Long companyId) {
        String dingToken = thirdPartyService.getDingToken(companyId);
        OapiRoleListRequest req = new OapiRoleListRequest();
        req.setSize(200L);
        Long nextCursor = 0L;
        List<OapiRoleListResponse.OpenRoleGroup> result = new ArrayList<>();
        while (nextCursor >= 0L) {
            req.setOffset(nextCursor);
            nextCursor = handleRolesNextCursor(dingToken, req, result);
        }
        return result;
    }

    private Long handleRolesNextCursor(String token, OapiRoleListRequest req, List<OapiRoleListResponse.OpenRoleGroup> result) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/list");
        OapiRoleListResponse response = RestOps
                .handleOrThrow(() -> client.execute(req, token), () -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取钉钉审批角色列表异常"))
                .getResultOrDefault(OapiRoleListResponse::isSuccess, new OapiRoleListResponse());
        if (Objects.isNull(response.getResult())) {
            return -1L;
        }
        result.addAll(response.getResult().getList());
        if (Objects.nonNull(response.getResult().getHasMore()) && response.getResult().getHasMore()) {
            return req.getOffset() + req.getSize();
        }
        return -1L;
    }

    public List<OapiRoleSimplelistResponse.OpenEmpSimple> getRoleUsers(Long companyId, Long roleId) {
        String dingToken = thirdPartyService.getDingToken(companyId);
        OapiRoleSimplelistRequest req = new OapiRoleSimplelistRequest();
        req.setRoleId(roleId);
        req.setSize(200L);
        req.setOffset(0L);
        List<OapiRoleSimplelistResponse.OpenEmpSimple> result = new ArrayList<>();
        recursionRoleUsers(dingToken, req, result);
        return result;
    }

    private void recursionRoleUsers(String token, OapiRoleSimplelistRequest req, List<OapiRoleSimplelistResponse.OpenEmpSimple> result) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/simplelist");
        OapiRoleSimplelistResponse response = RestOps
                .handleOrThrow(() -> client.execute(req, token), () -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取钉钉角色员工列表异常"))
                .getResultIf(OapiRoleSimplelistResponse::isSuccess);
        if (Objects.isNull(response)) {
            return;
        }
        Boolean hasMore = response.getResult().getHasMore();
        Long nextCursor = response.getResult().getNextCursor();
        result.addAll(response.getResult().getList());
        if (Objects.isNull(hasMore) || !hasMore) {
            return;
        }
        req.setOffset(nextCursor);
        recursionRoleUsers(token, req, result);
    }

    public OapiRoleGetrolegroupResponse.OpenRoleGroup getRolesByGroupId(Long companyId, Long roleGroupId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/getrolegroup");
        OapiRoleGetrolegroupRequest req = new OapiRoleGetrolegroupRequest();
        req.setGroupId(roleGroupId);
        OapiRoleGetrolegroupResponse response = RestOps
                .handleOrThrow(() -> client.execute(req, thirdPartyService.getDingToken(companyId)), () -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取钉钉角色列表异常"))
                .getResultOrDefault(OapiRoleGetrolegroupResponse::isSuccess, new OapiRoleGetrolegroupResponse());
        return response.getRoleGroup();
    }

    public OapiRoleGetroleResponse.OpenRole getRoleById(Long companyId, Long roleId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/getrole");
        OapiRoleGetroleRequest req = new OapiRoleGetroleRequest();
        req.setRoleId(roleId);
        OapiRoleGetroleResponse response = RestOps
                .handleOrThrow(() -> client.execute(req, thirdPartyService.getDingToken(companyId)), () -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "钉钉根据角色ID获取角色详情异常"))
                .getResultOrDefault(OapiRoleGetroleResponse::isSuccess, new OapiRoleGetroleResponse());
        return response.getRole();
    }

    public List<DingRole> getRoles(Long companyId, List<Long> ids) {
        List<DingRole> toDb = new ArrayList<>();
        // 角色或角色组ID集合
        // 先根据角色组ID去查询
        ids.forEach(id -> {
            OapiRoleGetrolegroupResponse.OpenRoleGroup group = this.getRolesByGroupId(companyId, id);
            if (Objects.isNull(group) || CollUtil.isEmpty(group.getRoles())) {
                return;
            }
            List<DingRole> roles = group.getRoles().stream()
                    .map(v -> new DingRole(v.getRoleId(), v.getRoleName()))
                    .collect(Collectors.toList());
            toDb.addAll(roles);
        });
        // 根据角色ID查寻
        ids.forEach(id -> {
            OapiRoleGetroleResponse.OpenRole r = this.getRoleById(companyId, id);
            if (Objects.isNull(r) || Objects.isNull(r.getGroupId()) || r.getGroupId() == -1) {
                return;
            }
            toDb.add(new DingRole(id, r.getName()));
        });
        return toDb.stream().distinct().collect(Collectors.toList());
    }

    @Data
    public static class DingRole {
        private Long id;
        private String name;

        public DingRole(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

}
