package com.niimbot.asset.thirdparty.event;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/11/9 9:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OthersCallbackDto对象", description = "其他回调")
public class OthersCallbackDto {

    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    @ApiModelProperty(value = "数据")
    private EventCO body = new EventCO();

}
