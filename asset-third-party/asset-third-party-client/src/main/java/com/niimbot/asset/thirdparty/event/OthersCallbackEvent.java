package com.niimbot.asset.thirdparty.event;

/**
 * <AUTHOR>
 * @Since 2024-10-14
 */
public interface OthersCallbackEvent {

    // 新增部门
    String DEPT_CREATE = "contact.department.created";

    // 更新部门
    String DEPT_UPDATE = "contact.department.updated";

    // 删除部门
    String DEPT_DELETE = "contact.department.deleted";

    // 新增用户
    String USER_CREATE = "contact.user.created";

    // 更新用户
    String USER_UPDATE = "contact.user.updated";

    // 删除用户
    String USER_DELETE = "contact.user.deleted";

}
