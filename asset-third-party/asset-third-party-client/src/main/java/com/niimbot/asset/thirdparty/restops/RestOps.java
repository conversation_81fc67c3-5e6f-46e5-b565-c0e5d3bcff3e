package com.niimbot.asset.thirdparty.restops;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.lang.reflect.Type;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 第三方请求工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class RestOps<Result> {

    /**
     * 结果集
     */
    protected Result result;

    /**
     * 请求异常
     */
    public static final RuntimeException REQUEST_ERROR = new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请求第三方接口异常");

    /**
     * 微信请求统一异常，联系客服处理：IP受限、权限受限等
     */

    RestOps() {
    }

    RestOps(Result result) {
        this.result = result;
    }

    public Result peek() {
        return result;
    }

    public Optional<Result> getResult() {
        return Optional.ofNullable(this.result);
    }

    public Result getResultIf(Predicate<Result> predicate) {
        return predicate.test(this.result) ? result : null;
    }

    public Result getResultOrDefault(Predicate<Result> predicate, Result defaultResult) {
        return predicate.test(this.result) ? result : defaultResult;
    }

    public RestOps<Result> assertNotEmpty(Supplier<RuntimeException> supplier) {
        if (ObjectUtil.isEmpty(result) && Objects.nonNull(supplier)) {
            throw supplier.get();
        }
        return this;
    }

    public JSONObject toJsonObject() {
        if (this.result instanceof String) {
            return JSONObject.parseObject(String.valueOf(this.result));
        }
        return (JSONObject) JSONObject.toJSON(this.result);
    }

    public Result get() {
        return result;
    }

    public static <Result> RestOps<Result> of(Result result) {
        if (Objects.isNull(result)) {
            log.error("third party response is null");
            throw REQUEST_ERROR;
        }
        return new RestOps<>(result);
    }

    public static <Result> RestOps<Result> handle(RequestSupplier<Result> requestExe) {
        return of(exe(requestExe));
    }

    public static <Result> RestOps<Result> handleOrThrow(RequestSupplier<Result> requestExe, Supplier<RuntimeException> supplier) {
        try {
            return of(requestExe.get());
        } catch (Exception e) {
            log.error("third party request execute error", e);
            if (StrUtil.isNotBlank(e.getMessage())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, e.getMessage());
            }
            throw supplier.get();
        }
    }

    private static <Result> Result exe(RequestSupplier<Result> requestExe) {
        try {
            return requestExe.get();
        } catch (Exception e) {
            log.error("third party request execute error", e);
            if (StrUtil.isNotBlank(e.getMessage())) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, e.getMessage());
            }
            throw REQUEST_ERROR;
        }
    }

    public WorkWeChat<Result> workWeChat() {
        return new WorkWeChat<>(this.result);
    }

    /**
     * 企业微信请求类型
     *
     * @param <Result>
     */
    public static class WorkWeChat<Result> extends RestOps<Result> {

        public WorkWeChat() {
        }

        public WorkWeChat(Result result) {
            super(result);
        }

        public JSONObject readJson() {
            return JSONObject.parseObject(String.valueOf(this.result));
        }

        public <T> T readJson(Class<T> clazz) {
            return WxCpGsonBuilder.create()
                    .fromJson(String.valueOf(this.result), clazz);
        }

        public <T> T readJson(Type typeOfT) {
            return WxCpGsonBuilder.create()
                    .fromJson(String.valueOf(this.result), typeOfT);
        }

    }

}
