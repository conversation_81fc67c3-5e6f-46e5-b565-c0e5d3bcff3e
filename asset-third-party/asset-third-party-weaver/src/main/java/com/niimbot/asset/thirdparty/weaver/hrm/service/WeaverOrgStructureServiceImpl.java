package com.niimbot.asset.thirdparty.weaver.hrm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.AbstractTree;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.weaver.config.WeaverAppConfig;
import com.niimbot.asset.thirdparty.weaver.hrm.HrmDept;
import com.niimbot.asset.thirdparty.weaver.oa.WeaverOAClient;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.service.AbsOrgStructureService;
import com.niimbot.asset.thirdparty.weaver.hrm.HrmSubCompany;
import com.niimbot.asset.thirdparty.weaver.hrm.HrmUserInfo;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 泛微组织架构服务实现类
 * 负责处理泛微OA系统的组织架构数据同步，包括部门和员工信息的获取与转换
 *
 * <AUTHOR>
 * @date 2024/7/9 16:21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeaverOrgStructureServiceImpl extends AbsOrgStructureService {

    private final OrgService orgService;
    private final WeaverOAClient weaverOAClient;
    private final WeaverAppConfig appConfig;

    /**
     * 加载泛微组织架构数据
     * 将泛微的组织架构数据转换为系统内部的AsOrg对象
     *
     * @param companyId 企业ID
     * @return 组织架构列表
     */
    @Override
    public List<AsOrg> loadThirdPartyOrg(Long companyId) {
        // 获取系统中已存在的组织数据
        List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                .eq(AsOrg::getCompanyId, appConfig.getCompanyId()).eq(AsOrg::getSourceType, 1));
        log.info("已存在组织数据数量: {}", orgList.size());
        // 构建外部组织ID到内部组织ID的映射
        Map<String, Long> orgIdMap = orgList.stream()
                .filter(f -> StrUtil.isNotEmpty(f.getExternalOrgId()))
                .collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, AsOrg::getId));
        log.info("成功构建外部组织ID到内部组织ID的映射数量: {}", orgIdMap.size());

        // 获取泛微的组织数据
        List<HrmSubCompany> subCompanyList = weaverOAClient.getHrmsubcompanyWithPage();
        log.info("获取到子公司数据数量: {}", subCompanyList.size());
        List<HrmDept> deptList = weaverOAClient.getHrmdepartmentWithPage();
        log.info("获取到部门数据数量: {}", deptList.size());

        List<AsOrg> result = convertToOrg(subCompanyList, deptList, orgIdMap);
        log.info("转换后的组织总数量: {}", result.size());
        
        return result;
    }

    /**
     * 将泛微的组织数据转换为系统内部的组织对象
     * 包括公司（子公司）和部门的转换
     *
     * @param subCompanyList 泛微子公司列表
     * @param deptList 泛微部门列表
     * @param orgIdMap 外部组织ID到内部组织ID的映射
     * @return 转换后的组织列表
     */
    private List<AsOrg> convertToOrg(List<HrmSubCompany> subCompanyList, List<HrmDept> deptList, Map<String, Long> orgIdMap) {
        log.info("开始转换泛微组织架构数据，子公司数量: {}, 部门数量: {}", 
                subCompanyList.size(), deptList.size());
        
        // 验证根节点唯一性
        long rootCount = subCompanyList.stream()
                .filter(f -> "0".equals(f.getSupsubcomid()))
                .count();
        if (rootCount != 1) {
            log.error("子公司根节点验证失败，找到 {} 个根节点", rootCount);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "子公司根节点为空或不唯一");
        }
        log.info("成功通过根节点唯一性验证");

        // 转换公司（子公司）数据
        log.info("开始转换子公司数据");
        List<AsOrg> companyList = subCompanyList.stream()
                .map(f -> toSystemCompany(orgIdMap.get(f.getId()), f))
                .collect(Collectors.toList());
        log.info("完成子公司数据转换，共转换 {} 个子公司", companyList.size());
        
        // 构建公司ID映射，用于后续部门关联
        log.info("开始构建公司ID映射");
        Map<String, Long> companyRef = companyList.stream()
                .collect(Collectors.toMap(
                        AsOrg::getExternalOrgId, 
                        AbstractTree::getId, 
                        (k1, k2) -> k1)); // 如果有重复键值，保留第一个
        log.info("成功构建公司ID映射，包含 {} 个条目", companyRef.size());
        
        List<AsOrg> orgList = new ArrayList<>(companyList);
        log.info("初始化组织列表，包含 {} 个公司节点", orgList.size());

        // 转换部门数据
        log.info("开始转换部门数据");
        int deptProcessed = 0;
        int deptIgnored = 0;
        
        for (HrmDept hrmDept : deptList) {
            String subCompanyId = hrmDept.getSubcompanyid1();
            
            if (companyRef.containsKey(subCompanyId)) {
                AsOrg org = toSystemOrg(
                        orgIdMap.get(hrmDept.getSubcompanyid1() + "_" + hrmDept.getId()), 
                        hrmDept,
                        companyRef.get(subCompanyId));
                orgList.add(org);
                deptProcessed++;
            } else {
                log.error("未找到对应的子公司，subCompanyId {} 不存在，忽略部门 {}", 
                        subCompanyId, hrmDept);
                deptIgnored++;
            }
        }
        
        log.info("完成部门数据转换，成功处理 {} 个部门，忽略 {} 个无效部门", 
                deptProcessed, deptIgnored);
        log.info("组织结构转换完成，总组织数: {}", orgList.size());
        
        return orgList;
    }

    /**
     * 将泛微子公司转换为系统公司对象
     *
     * @param id 系统内部ID（如果已存在）
     * @param subCompany 泛微子公司数据
     * @return 系统公司对象
     */
    /**
     * 将泛微子公司数据转换为系统公司对象
     *
     * @param id 系统内部ID（如果已存在）
     * @param subCompany 泛微子公司数据
     * @return 转换后的系统公司对象
     */
    private AsOrg toSystemCompany(Long id, HrmSubCompany subCompany) {
        log.info("开始将泛微子公司 [{}] 转换为系统公司对象", subCompany.getId());
        
        AsOrg org = new AsOrg();
        
        // 设置组织ID：若已存在则使用原有ID，否则生成新ID
        Long assignedId = Objects.isNull(id) ? IdUtils.getId() : id;
        org.setId(assignedId);
        log.debug("分配系统内部ID: {}", assignedId);
        
        // 设置企业ID
        org.setCompanyId(appConfig.getCompanyId());
        log.debug("设置企业ID: {}", appConfig.getCompanyId());
        
        // 设置外部组织ID（即泛微系统的ID）
        String externalOrgId = subCompany.getId();
        org.setExternalOrgId(externalOrgId);
        log.debug("设置外部组织ID: {}", externalOrgId);
        
        // 设置上级组织ID（对应泛微的supsubcomid）
        String parentId = subCompany.getSupsubcomid();
        org.setExternalPid(parentId);
        log.debug("设置上级组织ID: {}", parentId);
        
        // 设置排序号，默认100
        int sortNum = Convert.toInt(subCompany.getShoworder(), 100);
        org.setSortNum(sortNum);
        log.debug("设置排序号: {}", sortNum);
        
        // 设置组织名称
        String orgName = subCompany.getSubcompanyname();
        org.setOrgName(orgName);
        log.debug("设置组织名称: {}", orgName);
        
        // 设置公司所有者为自身（公司节点的companyOwner指向自己）
        org.setCompanyOwner(assignedId);
        log.debug("设置公司所有者为自身: {}", assignedId);
        
        // 设置组织编码为泛微ID
        org.setOrgCode(externalOrgId);
        log.debug("设置组织编码: {}", externalOrgId);
        
        // 设置来源类型为1，表示来自泛微系统
        org.setSourceType(1);
        log.debug("设置来源类型: 1 (泛微系统)");
        
        // 设置组织类型为公司
        org.setOrgType(AssetConstant.ORG_TYPE_COMPANY);
        log.debug("设置组织类型: 公司");
        
        log.info("完成子公司 [{}] 到系统公司的转换", subCompany.getId());
        return org;
    }

    /**
     * 将泛微部门转换为系统部门对象
     *
     * @param id 系统内部ID（如果已存在）
     * @param dept 泛微部门数据
     * @param companyOwner 所属公司ID
     * @return 系统部门对象
     */
    private AsOrg toSystemOrg(Long id, HrmDept dept, Long companyOwner) {
        log.info("开始将泛微部门 [{}] 转换为系统部门对象", dept.getId());
        
        AsOrg org = new AsOrg();
        
        // 设置企业ID
        org.setCompanyId(appConfig.getCompanyId());
        log.debug("设置企业ID: {}", appConfig.getCompanyId());
        
        // 构建外部组织ID（格式：子公ID_部门ID）
        String externalOrgId = dept.getSubcompanyid1() + "_" + dept.getId();
        org.setExternalOrgId(externalOrgId);
        log.debug("设置外部组织ID: {}", externalOrgId);
        
        // 处理上级部门ID
        if ("0".equals(dept.getSupdepid())) {
            // 如果是根部门，则上级ID为所属公司ID
            org.setExternalPid(dept.getSubcompanyid1());
            log.debug("检测到根部门，设置上级ID为所属公司ID: {}", dept.getSubcompanyid1());
        } else {
            // 否则上级ID为"子公司ID_部门ID"
            org.setExternalPid(dept.getSubcompanyid1() + "_" + dept.getSupdepid());
            log.debug("设置上级部门ID: {}", org.getExternalPid());
        }
        
        // 设置排序号，默认100
        int sortNum = Convert.toInt(dept.getShoworder(), 100);
        org.setSortNum(sortNum);
        log.debug("设置排序号: {}", sortNum);
        
        // 设置部门名称
        String deptName = dept.getDepartmentname();
        org.setOrgName(deptName);
        log.debug("设置部门名称: {}", deptName);
        
        // 设置所属公司
        org.setCompanyOwner(companyOwner);
        log.debug("设置所属公司: {}", companyOwner);
        
        // 设置系统内部ID
        Long assignedId = Objects.isNull(id) ? IdUtils.getId() : id;
        org.setId(assignedId);
        log.debug("分配系统内部ID: {}", assignedId);
        
        // 设置组织编码为泛微部门ID
        org.setOrgCode(dept.getId());
        log.debug("设置组织编码: {}", dept.getId());
        
        // 设置来源类型为1，表示来自泛微系统
        org.setSourceType(1);
        log.debug("设置来源类型: 1 (泛微系统)");
        
        // 设置组织类型为部门
        org.setOrgType(AssetConstant.ORG_TYPE_DEPT);
        log.debug("设置组织类型: 部门");
        
        log.info("完成部门 [{}] 到系统部门的转换", dept.getId());
        return org;
    }

    /**
     * 加载泛微员工数据
     * 将泛微的员工数据转换为系统内部的ThirdPartyEmp对象
     *
     * @param companyId 企业ID
     * @param thirdPartyOrgIds 组织结构ID列表
     * @return 员工列表
     */
    @Override
    public List<ThirdPartyEmp> loadThirdPartyEmp(Long companyId, List<String> thirdPartyOrgIds) {
        log.info("开始从泛微系统加载员工数据");
        
        // 获取泛微员工列表
        List<HrmUserInfo> hrmUserInfoList = weaverOAClient.getHrmUserInfoWithPage();
        
        if (CollUtil.isEmpty(hrmUserInfoList)) {
            log.warn("未获取到任何泛微员工数据");
            return new ArrayList<>();
        }
        
        log.info("成功获取到 {} 条泛微员工记录，开始转换", hrmUserInfoList.size());
        
        List<ThirdPartyEmp> empList = new ArrayList<>();
        int validCount = 0;
        int invalidCount = 0;
        
        for (HrmUserInfo user : hrmUserInfoList) {
            // 过滤掉无效状态的员工（状态码7表示无效）
            if ("7".equals(user.getStatus())) {
                log.debug("跳过无效员工 [{}], 状态码为7", user.getLastname());
                invalidCount++;
                continue;
            }
            
            log.debug("开始处理员工: {}, ID: {}", user.getLastname(), user.getId());
            
            ThirdPartyEmp emp = new ThirdPartyEmp();
            
            // 设置基本信息
            String name = user.getLastname();
            emp.setName(name);           // 姓名
            log.debug("设置员工姓名: {}", name);

            String workcode = user.getWorkcode();
            emp.setJobNum(workcode);      // 工号
            log.debug("设置工号: {}", workcode);
            
            emp.setPosition(StrUtil.EMPTY); // 职位（暂为空）
            log.debug("设置职位: 空");
            
            String userId = user.getId();
            emp.setThirdPartyId(userId); // 第三方系统唯一标识
            log.debug("设置第三方唯一标识: {}", userId);

            String loginid = user.getLoginid();
            emp.setAccount(loginid);    // 登录账号
            log.debug("设置登录账号: {}", workcode);
            
            String mobile = user.getMobile();
            emp.setMobile(mobile);       // 手机号
            log.debug("设置手机号: {}", mobile);
            
            emp.setAvatar(StrUtil.EMPTY); // 头像（暂为空）
            log.debug("设置头像: 空");
            
            String email = user.getEmail();
            emp.setEmail(email);         // 邮箱
            log.debug("设置邮箱: {}", email);
            
            // 设置部门信息（格式：公司ID_部门ID）
            String deptId = user.getSubcompanyid1() + "_" + user.getDepartmentid();
            emp.setDeptIdList(Collections.singletonList(deptId));
            log.debug("设置部门ID: {}", deptId);
            
            // 设置其他信息
            emp.setCompanyId(companyId);             // 公司ID
            log.debug("设置公司ID: {}", companyId);
            
            String thirdPartyType = getType();
            emp.setThirdPartyType(thirdPartyType);   // 第三方类型（泛微）
            log.debug("设置第三方类型: {}", thirdPartyType);
            
            empList.add(emp);
            validCount++;
            log.debug("完成员工 [{}] 的转换", name);
        }
        
        log.info("员工数据转换完成，共处理 {} 条记录，有效员工数: {}, 无效员工数: {}", 
                hrmUserInfoList.size(), validCount, invalidCount);
        
        return empList;
    }

    /**
     * 获取第三方类型
     * 用于标识数据来源为泛微系统
     *
     * @return 第三方类型标识
     */
    @Override
    public String getType() {
        return appConfig.getPlatform();
    }
}
