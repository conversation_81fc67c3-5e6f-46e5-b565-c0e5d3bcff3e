
package com.niimbot.asset.thirdparty.weaver.webservices;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the cn.com.weaver.services.webservices package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: cn.com.weaver.services.webservices
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestCountResponse }
     * 
     */
    public GetHendledWorkflowRequestCountResponse createGetHendledWorkflowRequestCountResponse() {
        return new GetHendledWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowRequestInfoResponse }
     * 
     */
    public GetCreateWorkflowRequestInfoResponse createGetCreateWorkflowRequestInfoResponse() {
        return new GetCreateWorkflowRequestInfoResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeListResponse }
     * 
     */
    public GetCreateWorkflowTypeListResponse createGetCreateWorkflowTypeListResponse() {
        return new GetCreateWorkflowTypeListResponse();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestList }
     * 
     */
    public GetHendledWorkflowRequestList createGetHendledWorkflowRequestList() {
        return new GetHendledWorkflowRequestList();
    }

    /**
     * Create an instance of {@link ArrayOfString }
     * 
     */
    public ArrayOfString createArrayOfString() {
        return new ArrayOfString();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestCountResponse }
     * 
     */
    public GetCCWorkflowRequestCountResponse createGetCCWorkflowRequestCountResponse() {
        return new GetCCWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetLeaveDaysResponse }
     * 
     */
    public GetLeaveDaysResponse createGetLeaveDaysResponse() {
        return new GetLeaveDaysResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestList4OSResponse }
     * 
     */
    public GetToDoWorkflowRequestList4OSResponse createGetToDoWorkflowRequestList4OSResponse() {
        return new GetToDoWorkflowRequestList4OSResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowRequest4Split }
     * 
     */
    public GetWorkflowRequest4Split createGetWorkflowRequest4Split() {
        return new GetWorkflowRequest4Split();
    }

    /**
     * Create an instance of {@link GetTodoData }
     * 
     */
    public GetTodoData createGetTodoData() {
        return new GetTodoData();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowList }
     * 
     */
    public GetCreateWorkflowList createGetCreateWorkflowList() {
        return new GetCreateWorkflowList();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestCount }
     * 
     */
    public GetProcessedWorkflowRequestCount createGetProcessedWorkflowRequestCount() {
        return new GetProcessedWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowCountResponse }
     * 
     */
    public GetCreateWorkflowCountResponse createGetCreateWorkflowCountResponse() {
        return new GetCreateWorkflowCountResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestCount4OS }
     * 
     */
    public GetToDoWorkflowRequestCount4OS createGetToDoWorkflowRequestCount4OS() {
        return new GetToDoWorkflowRequestCount4OS();
    }

    /**
     * Create an instance of {@link GetToBeReadWorkflowRequestListResponse }
     * 
     */
    public GetToBeReadWorkflowRequestListResponse createGetToBeReadWorkflowRequestListResponse() {
        return new GetToBeReadWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestList }
     * 
     */
    public GetProcessedWorkflowRequestList createGetProcessedWorkflowRequestList() {
        return new GetProcessedWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestCount4OSResponse }
     * 
     */
    public GetMyWorkflowRequestCount4OSResponse createGetMyWorkflowRequestCount4OSResponse() {
        return new GetMyWorkflowRequestCount4OSResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowRequestInfo }
     * 
     */
    public GetCreateWorkflowRequestInfo createGetCreateWorkflowRequestInfo() {
        return new GetCreateWorkflowRequestInfo();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeList }
     * 
     */
    public GetCreateWorkflowTypeList createGetCreateWorkflowTypeList() {
        return new GetCreateWorkflowTypeList();
    }

    /**
     * Create an instance of {@link GetDoingWorkflowRequestCountResponse }
     * 
     */
    public GetDoingWorkflowRequestCountResponse createGetDoingWorkflowRequestCountResponse() {
        return new GetDoingWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestList4OS }
     * 
     */
    public GetProcessedWorkflowRequestList4OS createGetProcessedWorkflowRequestList4OS() {
        return new GetProcessedWorkflowRequestList4OS();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestCountResponse }
     * 
     */
    public GetMyWorkflowRequestCountResponse createGetMyWorkflowRequestCountResponse() {
        return new GetMyWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestList }
     * 
     */
    public GetCCWorkflowRequestList createGetCCWorkflowRequestList() {
        return new GetCCWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetBeRejectWorkflowRequestCount }
     * 
     */
    public GetBeRejectWorkflowRequestCount createGetBeRejectWorkflowRequestCount() {
        return new GetBeRejectWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestList }
     * 
     */
    public GetAllWorkflowRequestList createGetAllWorkflowRequestList() {
        return new GetAllWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestCountResponse }
     * 
     */
    public GetToDoWorkflowRequestCountResponse createGetToDoWorkflowRequestCountResponse() {
        return new GetToDoWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestList }
     * 
     */
    public GetToDoWorkflowRequestList createGetToDoWorkflowRequestList() {
        return new GetToDoWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestCount4OS }
     * 
     */
    public GetCCWorkflowRequestCount4OS createGetCCWorkflowRequestCount4OS() {
        return new GetCCWorkflowRequestCount4OS();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestCount }
     * 
     */
    public GetHendledWorkflowRequestCount createGetHendledWorkflowRequestCount() {
        return new GetHendledWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestCountResponse }
     * 
     */
    public GetAllWorkflowRequestCountResponse createGetAllWorkflowRequestCountResponse() {
        return new GetAllWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetToBeReadWorkflowRequestCount }
     * 
     */
    public GetToBeReadWorkflowRequestCount createGetToBeReadWorkflowRequestCount() {
        return new GetToBeReadWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link ForwardWorkflowRequest }
     * 
     */
    public ForwardWorkflowRequest createForwardWorkflowRequest() {
        return new ForwardWorkflowRequest();
    }

    /**
     * Create an instance of {@link SubmitWorkflowRequestResponse }
     * 
     */
    public SubmitWorkflowRequestResponse createSubmitWorkflowRequestResponse() {
        return new SubmitWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link GetUserIdResponse }
     * 
     */
    public GetUserIdResponse createGetUserIdResponse() {
        return new GetUserIdResponse();
    }

    /**
     * Create an instance of {@link GetToBeReadWorkflowRequestList }
     * 
     */
    public GetToBeReadWorkflowRequestList createGetToBeReadWorkflowRequestList() {
        return new GetToBeReadWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeCountResponse }
     * 
     */
    public GetCreateWorkflowTypeCountResponse createGetCreateWorkflowTypeCountResponse() {
        return new GetCreateWorkflowTypeCountResponse();
    }

    /**
     * Create an instance of {@link GetBeRejectWorkflowRequestList }
     * 
     */
    public GetBeRejectWorkflowRequestList createGetBeRejectWorkflowRequestList() {
        return new GetBeRejectWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestCount }
     * 
     */
    public GetCCWorkflowRequestCount createGetCCWorkflowRequestCount() {
        return new GetCCWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestCount }
     * 
     */
    public GetAllWorkflowRequestCount createGetAllWorkflowRequestCount() {
        return new GetAllWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link ForwardWorkflowRequestResponse }
     * 
     */
    public ForwardWorkflowRequestResponse createForwardWorkflowRequestResponse() {
        return new ForwardWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link GetDoingWorkflowRequestCount }
     * 
     */
    public GetDoingWorkflowRequestCount createGetDoingWorkflowRequestCount() {
        return new GetDoingWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestCount }
     * 
     */
    public GetMyWorkflowRequestCount createGetMyWorkflowRequestCount() {
        return new GetMyWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetBeRejectWorkflowRequestListResponse }
     * 
     */
    public GetBeRejectWorkflowRequestListResponse createGetBeRejectWorkflowRequestListResponse() {
        return new GetBeRejectWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link WriteWorkflowReadFlag }
     * 
     */
    public WriteWorkflowReadFlag createWriteWorkflowReadFlag() {
        return new WriteWorkflowReadFlag();
    }

    /**
     * Create an instance of {@link GivingOpinionsResponse }
     * 
     */
    public GivingOpinionsResponse createGivingOpinionsResponse() {
        return new GivingOpinionsResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowNewFlagResponse }
     * 
     */
    public GetWorkflowNewFlagResponse createGetWorkflowNewFlagResponse() {
        return new GetWorkflowNewFlagResponse();
    }

    /**
     * Create an instance of {@link Forward2WorkflowRequestResponse }
     * 
     */
    public Forward2WorkflowRequestResponse createForward2WorkflowRequestResponse() {
        return new Forward2WorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link DoCreateWorkflowRequestResponse }
     * 
     */
    public DoCreateWorkflowRequestResponse createDoCreateWorkflowRequestResponse() {
        return new DoCreateWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link DeleteRequest }
     * 
     */
    public DeleteRequest createDeleteRequest() {
        return new DeleteRequest();
    }

    /**
     * Create an instance of {@link GetUserId }
     * 
     */
    public GetUserId createGetUserId() {
        return new GetUserId();
    }

    /**
     * Create an instance of {@link GetForwardWorkflowRequestListResponse }
     * 
     */
    public GetForwardWorkflowRequestListResponse createGetForwardWorkflowRequestListResponse() {
        return new GetForwardWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestCount4OSResponse }
     * 
     */
    public GetToDoWorkflowRequestCount4OSResponse createGetToDoWorkflowRequestCount4OSResponse() {
        return new GetToDoWorkflowRequestCount4OSResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowRequest }
     * 
     */
    public GetWorkflowRequest createGetWorkflowRequest() {
        return new GetWorkflowRequest();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestListResponse }
     * 
     */
    public GetHendledWorkflowRequestListResponse createGetHendledWorkflowRequestListResponse() {
        return new GetHendledWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestListResponse }
     * 
     */
    public GetToDoWorkflowRequestListResponse createGetToDoWorkflowRequestListResponse() {
        return new GetToDoWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link SubmitWorkflowRequest }
     * 
     */
    public SubmitWorkflowRequest createSubmitWorkflowRequest() {
        return new SubmitWorkflowRequest();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestCount4OS }
     * 
     */
    public GetMyWorkflowRequestCount4OS createGetMyWorkflowRequestCount4OS() {
        return new GetMyWorkflowRequestCount4OS();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestList4OS }
     * 
     */
    public GetCCWorkflowRequestList4OS createGetCCWorkflowRequestList4OS() {
        return new GetCCWorkflowRequestList4OS();
    }

    /**
     * Create an instance of {@link GetLeaveDays }
     * 
     */
    public GetLeaveDays createGetLeaveDays() {
        return new GetLeaveDays();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestList4Ofs }
     * 
     */
    public GetHendledWorkflowRequestList4Ofs createGetHendledWorkflowRequestList4Ofs() {
        return new GetHendledWorkflowRequestList4Ofs();
    }

    /**
     * Create an instance of {@link DoForceOverResponse }
     * 
     */
    public DoForceOverResponse createDoForceOverResponse() {
        return new DoForceOverResponse();
    }

    /**
     * Create an instance of {@link GetToBeReadWorkflowRequestCountResponse }
     * 
     */
    public GetToBeReadWorkflowRequestCountResponse createGetToBeReadWorkflowRequestCountResponse() {
        return new GetToBeReadWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link DoCreateRequestResponse }
     * 
     */
    public DoCreateRequestResponse createDoCreateRequestResponse() {
        return new DoCreateRequestResponse();
    }

    /**
     * Create an instance of {@link DoCreateRequest }
     * 
     */
    public DoCreateRequest createDoCreateRequest() {
        return new DoCreateRequest();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestListResponse }
     * 
     */
    public GetMyWorkflowRequestListResponse createGetMyWorkflowRequestListResponse() {
        return new GetMyWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestList4OSResponse }
     * 
     */
    public GetMyWorkflowRequestList4OSResponse createGetMyWorkflowRequestList4OSResponse() {
        return new GetMyWorkflowRequestList4OSResponse();
    }

    /**
     * Create an instance of {@link DoCreateWorkflowRequest }
     * 
     */
    public DoCreateWorkflowRequest createDoCreateWorkflowRequest() {
        return new DoCreateWorkflowRequest();
    }

    /**
     * Create an instance of {@link DoForceOver }
     * 
     */
    public DoForceOver createDoForceOver() {
        return new DoForceOver();
    }

    /**
     * Create an instance of {@link GetWorkflowRequest4SplitResponse }
     * 
     */
    public GetWorkflowRequest4SplitResponse createGetWorkflowRequest4SplitResponse() {
        return new GetWorkflowRequest4SplitResponse();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestListResponse }
     * 
     */
    public GetAllWorkflowRequestListResponse createGetAllWorkflowRequestListResponse() {
        return new GetAllWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowRequestResponse }
     * 
     */
    public GetWorkflowRequestResponse createGetWorkflowRequestResponse() {
        return new GetWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowRequestLogsResponse }
     * 
     */
    public GetWorkflowRequestLogsResponse createGetWorkflowRequestLogsResponse() {
        return new GetWorkflowRequestLogsResponse();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestCount4Ofs }
     * 
     */
    public GetHendledWorkflowRequestCount4Ofs createGetHendledWorkflowRequestCount4Ofs() {
        return new GetHendledWorkflowRequestCount4Ofs();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestList4OS }
     * 
     */
    public GetMyWorkflowRequestList4OS createGetMyWorkflowRequestList4OS() {
        return new GetMyWorkflowRequestList4OS();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowListResponse }
     * 
     */
    public GetCreateWorkflowListResponse createGetCreateWorkflowListResponse() {
        return new GetCreateWorkflowListResponse();
    }

    /**
     * Create an instance of {@link GetBeRejectWorkflowRequestCountResponse }
     * 
     */
    public GetBeRejectWorkflowRequestCountResponse createGetBeRejectWorkflowRequestCountResponse() {
        return new GetBeRejectWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetForwardWorkflowRequestCount }
     * 
     */
    public GetForwardWorkflowRequestCount createGetForwardWorkflowRequestCount() {
        return new GetForwardWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestCount }
     * 
     */
    public GetToDoWorkflowRequestCount createGetToDoWorkflowRequestCount() {
        return new GetToDoWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GivingOpinions }
     * 
     */
    public GivingOpinions createGivingOpinions() {
        return new GivingOpinions();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestCount4OS }
     * 
     */
    public GetProcessedWorkflowRequestCount4OS createGetProcessedWorkflowRequestCount4OS() {
        return new GetProcessedWorkflowRequestCount4OS();
    }

    /**
     * Create an instance of {@link Forward2WorkflowRequest }
     * 
     */
    public Forward2WorkflowRequest createForward2WorkflowRequest() {
        return new Forward2WorkflowRequest();
    }

    /**
     * Create an instance of {@link GetTodoDataResponse }
     * 
     */
    public GetTodoDataResponse createGetTodoDataResponse() {
        return new GetTodoDataResponse();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestCount4OfsResponse }
     * 
     */
    public GetHendledWorkflowRequestCount4OfsResponse createGetHendledWorkflowRequestCount4OfsResponse() {
        return new GetHendledWorkflowRequestCount4OfsResponse();
    }

    /**
     * Create an instance of {@link GetForwardWorkflowRequestCountResponse }
     * 
     */
    public GetForwardWorkflowRequestCountResponse createGetForwardWorkflowRequestCountResponse() {
        return new GetForwardWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestListResponse }
     * 
     */
    public GetProcessedWorkflowRequestListResponse createGetProcessedWorkflowRequestListResponse() {
        return new GetProcessedWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestList4OSResponse }
     * 
     */
    public GetCCWorkflowRequestList4OSResponse createGetCCWorkflowRequestList4OSResponse() {
        return new GetCCWorkflowRequestList4OSResponse();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestCount4OSResponse }
     * 
     */
    public GetCCWorkflowRequestCount4OSResponse createGetCCWorkflowRequestCount4OSResponse() {
        return new GetCCWorkflowRequestCount4OSResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestList4OS }
     * 
     */
    public GetToDoWorkflowRequestList4OS createGetToDoWorkflowRequestList4OS() {
        return new GetToDoWorkflowRequestList4OS();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestList4OSResponse }
     * 
     */
    public GetProcessedWorkflowRequestList4OSResponse createGetProcessedWorkflowRequestList4OSResponse() {
        return new GetProcessedWorkflowRequestList4OSResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowCount }
     * 
     */
    public GetCreateWorkflowCount createGetCreateWorkflowCount() {
        return new GetCreateWorkflowCount();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestListResponse }
     * 
     */
    public GetCCWorkflowRequestListResponse createGetCCWorkflowRequestListResponse() {
        return new GetCCWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestCountResponse }
     * 
     */
    public GetProcessedWorkflowRequestCountResponse createGetProcessedWorkflowRequestCountResponse() {
        return new GetProcessedWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestCount4OSResponse }
     * 
     */
    public GetProcessedWorkflowRequestCount4OSResponse createGetProcessedWorkflowRequestCount4OSResponse() {
        return new GetProcessedWorkflowRequestCount4OSResponse();
    }

    /**
     * Create an instance of {@link DeleteRequestResponse }
     * 
     */
    public DeleteRequestResponse createDeleteRequestResponse() {
        return new DeleteRequestResponse();
    }

    /**
     * Create an instance of {@link GetForwardWorkflowRequestList }
     * 
     */
    public GetForwardWorkflowRequestList createGetForwardWorkflowRequestList() {
        return new GetForwardWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestList }
     * 
     */
    public GetMyWorkflowRequestList createGetMyWorkflowRequestList() {
        return new GetMyWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetDoingWorkflowRequestListResponse }
     * 
     */
    public GetDoingWorkflowRequestListResponse createGetDoingWorkflowRequestListResponse() {
        return new GetDoingWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowNewFlag }
     * 
     */
    public GetWorkflowNewFlag createGetWorkflowNewFlag() {
        return new GetWorkflowNewFlag();
    }

    /**
     * Create an instance of {@link WriteWorkflowReadFlagResponse }
     * 
     */
    public WriteWorkflowReadFlagResponse createWriteWorkflowReadFlagResponse() {
        return new WriteWorkflowReadFlagResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeCount }
     * 
     */
    public GetCreateWorkflowTypeCount createGetCreateWorkflowTypeCount() {
        return new GetCreateWorkflowTypeCount();
    }

    /**
     * Create an instance of {@link GetWorkflowRequestLogs }
     * 
     */
    public GetWorkflowRequestLogs createGetWorkflowRequestLogs() {
        return new GetWorkflowRequestLogs();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestList4OfsResponse }
     * 
     */
    public GetHendledWorkflowRequestList4OfsResponse createGetHendledWorkflowRequestList4OfsResponse() {
        return new GetHendledWorkflowRequestList4OfsResponse();
    }

    /**
     * Create an instance of {@link GetDoingWorkflowRequestList }
     * 
     */
    public GetDoingWorkflowRequestList createGetDoingWorkflowRequestList() {
        return new GetDoingWorkflowRequestList();
    }

    /**
     * Create an instance of {@link ArrayOfArrayOfString }
     * 
     */
    public ArrayOfArrayOfString createArrayOfArrayOfString() {
        return new ArrayOfArrayOfString();
    }

}
