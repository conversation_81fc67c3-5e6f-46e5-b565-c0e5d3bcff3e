package com.niimbot.asset.thirdparty.weaver.hrm;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/12 10:24
 */
@Data
public class HrmUserInfo {

    // ""
    private String companystartdate;
    // ""
    private String tempresidentnumber;
    // "2017-08-09"
    private String createdate;
    // ""
    private String language;
    // ""
    private String workstartdate;
    // "1"
    private String subcompanyid1;
    // "路德环境科技股份有限公司"
    private String subcompanyname;
    // "0"
    private String joblevel;
    // "2018-01-12"
    private String startdate;
    // ""
    private String password;
    // "-1"
    private String belongtoid;
    // "LD"
    private String subcompanycode;
    // ""
    private String jobactivitydesc;
    // ""
    private String bememberdate;
    // "2021-01-16 14:55:17"
    private String modified;
    // 人员id "3"
    private String id;
    // 其他电话 "13040958610"
    private String mobilecall;
    // 籍贯 ""
    private String nativeplace;
    // 身份证 ""
    private String certificatenum;
    // 身高 ""
    private String height;
    // 登录名 ""
    private String loginid;
    // "2021-01-16 14:55:17"
    private String created;
    // 学位 ""
    private String degree;
    // 入党日期 ""
    private String bepartydate;
    // 体重 ""
    private String weight;
    // 办公电话 ""
    private String telephone;
    // ""
    private String classification;
    // ""
    private String residentplace;
    // 名称 "cs"
    private String lastname;
    //  健康状况 ""
    private String healthinfo;
    // "2020-10-23"
    private String enddate;
    // "未婚"
    private String maritalstatus;
    // ""
    private String departmentname;
    // ""
    private String folk;
    // 状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效
    private String status;
    // ""
    private String birthday;
    // ""
    private String isadaccount;
    // "0"
    private String accounttype;
    // ""
    private String textfield1;
    // ""
    private String textfield2;
    // jobcall "0"
    private String jobcall;
    // "122"
    private String managerid;
    // "0"
    private String assistantid;
    // ""
    private String departmentcode;
    // ""
    private String belongto;
    // ""
    private String email;
    // 安全级别"0"
    private String seclevel;
    // 政治面貌 ""
    private String policy;
    // 岗位Id "2"
    private String jobtitle;
    // 编号 ""
    private String workcode;
    // 性别 "男"
    private String sex;
    // 部门Id "67"
    private String departmentid;
    // 家庭联系方式 ""
    private String homeaddress;
    // 手机号 ""
    private String mobile;
    // 最后修改日期 "2020-10-23"
    private String lastmoddate;
    // 学历 ""
    private String educationlevel;
    // 工会会员 ""
    private String islabouunion;
    // 办公地点 "2"
    private String locationid;
    // 户口 ""
    private String regresidentplace;
    // 排序 "3.0"
    private String dsporder;

}
