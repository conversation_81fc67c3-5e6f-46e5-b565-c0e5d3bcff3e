package com.niimbot.asset.thirdparty.weaver.oa;

import com.niimbot.asset.thirdparty.weaver.config.WeaverAppConfig;
import com.niimbot.asset.thirdparty.weaver.webservices.WorkflowService;
import com.niimbot.asset.thirdparty.weaver.webservices.WorkflowServicePortType;
import com.niimbot.asset.thirdparty.weaver.webservices.workflow.*;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.thirdparty.weaver.hrm.HrmDept;
import com.niimbot.asset.thirdparty.weaver.hrm.HrmSubCompany;
import com.niimbot.asset.thirdparty.weaver.hrm.HrmUserInfo;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 泛微OA系统客户端，用于对接获取组织架构信息（分部、部门、人员）的接口。
 *
 * <AUTHOR>
 * @date 2024/8/12 9:57
 */
@Slf4j
@Service
public class WeaverOAClient {

    /**
     * 获取分部信息的接口路径。
     * 示例请求：
      curl -s -X POST "http://192.168.6.45:8888/api/hrm/resful/getHrmsubcompanyWithPage" \
           -H "Content-Type: application/x-www-form-urlencoded; charset=utf-8" \
           -d "params={\"pagesize\": 5000,\"curpage\": 1}"
     */
    private static final String GET_HRM_SUBCOMPANY = "/api/hrm/resful/getHrmsubcompanyWithPage";
    /**
     * 获取部门信息的接口路径。
     * 示例请求：
      curl -s -X POST "http://192.168.6.45:8888/api/hrm/resful/getHrmdepartmentWithPage" \
           -H "Content-Type: application/x-www-form-urlencoded; charset=utf-8" \
           -d "params={\"pagesize\": 5000,\"curpage\": 1}"
     */
    private static final String GET_HRM_DEPARTMENT = "/api/hrm/resful/getHrmdepartmentWithPage";
    /**
     * 获取人员信息的接口路径。
     * 示例请求：
      curl -s -X POST "http://192.168.6.45:8888/api/hrm/resful/getHrmUserInfoWithPage" \
           -H "Content-Type: application/x-www-form-urlencoded; charset=utf-8" \
           -d "params={\"pagesize\": 5000,\"curpage\": 1}"
     */
    private static final String GET_HRM_USERINFO = "/api/hrm/resful/getHrmUserInfoWithPage";


    //  消息推送
    /**
     消息标题：xxbt
     消息内容：xxnr
     pc链接：pclj
     移动链接：ydlj
     发送人：fsr
     接收人：jsr

     curl -X POST "http://192.168.6.45:8888/api/workflow/paService/doCreateRequest" \
     -H "Content-Type: application/json" \
     -d '{
     "mainData": {
     "root": [
         {
         "fieldName": "xxbt",
         "fieldValue": "测试消息标题"
         },
         {
         "fieldName": "xxnr",
         "fieldValue": "这是消息内容"
         },
         {
         "fieldName": "pclj",
         "fieldValue": "http://192.168.6.18/#/emp-manage"
         },
         {
         "fieldName": "ydlj",
         "fieldValue": "http://m.example.com"
         },
         {
         "fieldName": "fsr",
         "fieldValue": "350"
         },
         {
         "fieldName": "jsr",
         "fieldValue": "390"
         }
         ]
     },
     "requestName": "消息推送测试",
     "workflowId": "149"
     }'
     * 发送消息
     */


    private final WeaverAppConfig appConfig;

    public WeaverOAClient(WeaverAppConfig appConfig) {
        this.appConfig = appConfig;
        log.info("WeaverOAClient 初始化完成，当前连接的泛微生态平台地址为：{}", appConfig.getHost());
    }

    /**
     * 获取所有分部信息列表。
     *
     * @return 分部信息对象列表
     */
    public List<HrmSubCompany> getHrmsubcompanyWithPage() {
        Map<String, Object> formMap = new HashMap<>();
        String requestParams = "{\"pagesize\": 5000,\"curpage\": 1}";
        formMap.put("params", requestParams);
        log.info("开始请求获取分部信息，参数：{}", requestParams);
        String data = HttpRequest.post(appConfig.getDebug()?appConfig.getMockUrlCompany():appConfig.getHost() + GET_HRM_SUBCOMPANY)
                .contentType("application/x-www-form-urlencoded; charset=utf-8")
                .form(formMap)
                .execute().body();

        log.debug("获取分部信息返回原始数据：{}", data);
        JSONObject dataJSON = JSONObject.parseObject(data);
        String code = dataJSON.getString("code");
        if (!"1".equals(code)) {
            log.error("获取分部列表失败，响应内容：{}", data);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取分部列表异常");
        }

        List<HrmSubCompany> result = dataJSON.getJSONObject("data").getJSONArray("dataList").toJavaList(HrmSubCompany.class);
        log.info("成功获取到 {} 条分部信息", result.size());
        return result;
    }

    /**
     * 获取所有部门信息列表。
     *
     * @return 部门信息对象列表
     */
    public List<HrmDept> getHrmdepartmentWithPage() {
        Map<String, Object> formMap = new HashMap<>();
        String requestParams = "{\"pagesize\": 5000,\"curpage\": 1}";
        formMap.put("params", requestParams);
        log.info("开始请求获取部门信息，参数：{}", requestParams);
        String data = HttpRequest.post(appConfig.getDebug()?appConfig.getMockUrlDept():appConfig.getHost() + GET_HRM_DEPARTMENT)
                .contentType("application/x-www-form-urlencoded; charset=utf-8")
                .form(formMap)
                .execute().body();

        log.debug("获取部门信息返回原始数据：{}", data);
        JSONObject dataJSON = JSONObject.parseObject(data);
        String code = dataJSON.getString("code");
        if (!"1".equals(code)) {
            log.error("获取部门集合失败，响应内容：{}", data);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取部门集合异常");
        }

        List<HrmDept> result = dataJSON.getJSONObject("data").getJSONArray("dataList").toJavaList(HrmDept.class);
        log.info("成功获取到 {} 条部门信息", result.size());
        return result;
    }

    /**
     * 获取所有人员信息列表。
     *
     * @return 人员信息对象列表
     */
    public List<HrmUserInfo> getHrmUserInfoWithPage() {
        Map<String, Object> formMap = new HashMap<>();
        String requestParams = "{\"pagesize\": 5000,\"curpage\": 1}";
        formMap.put("params", requestParams);
        log.info("开始请求获取人员信息，参数：{}", requestParams);
        String data = HttpRequest.post(appConfig.getDebug()?appConfig.getMockUrlPerson():appConfig.getHost() + GET_HRM_USERINFO)
                .contentType("application/x-www-form-urlencoded; charset=utf-8")
                .form(formMap)
                .execute().body();

        log.debug("获取人员信息返回原始数据：{}", data);
        JSONObject dataJSON = JSONObject.parseObject(data);
        String code = dataJSON.getString("code");
        if (!"1".equals(code)) {
            log.error("获取人员信息列表失败，响应内容：{}", data);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取人员信息列表异常");
        }

        List<HrmUserInfo> result = dataJSON.getJSONObject("data").getJSONArray("dataList").toJavaList(HrmUserInfo.class);
        log.info("成功获取到 {} 条人员信息", result.size());
        return result;
    }




    /**
     * 推送消息到泛微OA工作流（通过WebService接口）。
     */
    public String pushMessageToWorkflow(String title, String content, String pcUrl, String mobileUrl, String senderId, String receiverId) {
        try {
            System.out.println("开始调用WebService创建流程请求...");
            ObjectFactory factory = new ObjectFactory();
            WorkflowRequestInfo workflowRequestInfo = new WorkflowRequestInfo();
            workflowRequestInfo.setCanView(true);
            workflowRequestInfo.setCanEdit(true);
            workflowRequestInfo.setRequestName(factory.createWorkflowRequestInfoRequestName(title));
            workflowRequestInfo.setRequestLevel(factory.createWorkflowRequestInfoRequestLevel("0"));
            workflowRequestInfo.setCreatorId(factory.createWorkflowRequestInfoCreatorId(senderId));

            WorkflowBaseInfo workflowBaseInfo = new WorkflowBaseInfo();
            workflowBaseInfo.setWorkflowId(factory.createWorkflowBaseInfoWorkflowId("149"));
            workflowBaseInfo.setWorkflowName(factory.createWorkflowBaseInfoWorkflowName("webservice-create"));
            workflowBaseInfo.setWorkflowTypeName(factory.createWorkflowBaseInfoWorkflowTypeName("webservice-create"));
            workflowRequestInfo.setWorkflowBaseInfo(factory.createWorkflowRequestInfoWorkflowBaseInfo(workflowBaseInfo));

            // 主表
            WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();
            WorkflowRequestTableRecord[] workflowRequestTableRecord = new WorkflowRequestTableRecord[1];
            WorkflowRequestTableField[] WorkflowRequestTableField = new WorkflowRequestTableField[6];

            WorkflowRequestTableField[0] = new WorkflowRequestTableField();
            WorkflowRequestTableField[0].setFieldName(factory.createWorkflowRequestTableFieldFieldName("xxbt"));
            WorkflowRequestTableField[0].setFieldValue(factory.createWorkflowRequestTableFieldFieldValue(title));
            WorkflowRequestTableField[0].setView(true);
            WorkflowRequestTableField[0].setEdit(true);

            WorkflowRequestTableField[1] = new WorkflowRequestTableField();
            WorkflowRequestTableField[1].setFieldName(factory.createWorkflowRequestTableFieldFieldName("xxnr"));
            WorkflowRequestTableField[1].setFieldValue(factory.createWorkflowRequestTableFieldFieldValue(content));
            WorkflowRequestTableField[1].setView(true);
            WorkflowRequestTableField[1].setEdit(true);

            WorkflowRequestTableField[2] = new WorkflowRequestTableField();
            WorkflowRequestTableField[2].setFieldName(factory.createWorkflowRequestTableFieldFieldName("pclj"));
            WorkflowRequestTableField[2].setFieldValue(factory.createWorkflowRequestTableFieldFieldValue(pcUrl));
            WorkflowRequestTableField[2].setView(true);
            WorkflowRequestTableField[2].setEdit(true);

            WorkflowRequestTableField[3] = new WorkflowRequestTableField();
            WorkflowRequestTableField[3].setFieldName(factory.createWorkflowRequestTableFieldFieldName("ydlj"));
            WorkflowRequestTableField[3].setFieldValue(factory.createWorkflowRequestTableFieldFieldValue(mobileUrl));
            WorkflowRequestTableField[3].setView(true);
            WorkflowRequestTableField[3].setEdit(true);

            WorkflowRequestTableField[4] = new WorkflowRequestTableField();
            WorkflowRequestTableField[4].setFieldName(factory.createWorkflowRequestTableFieldFieldName("fsr"));
            WorkflowRequestTableField[4].setFieldValue(factory.createWorkflowRequestTableFieldFieldValue(senderId));
            WorkflowRequestTableField[4].setView(true);
            WorkflowRequestTableField[4].setEdit(true);

            WorkflowRequestTableField[5] = new WorkflowRequestTableField();
            WorkflowRequestTableField[5].setFieldName(factory.createWorkflowRequestTableFieldFieldName("jsr"));
            WorkflowRequestTableField[5].setFieldValue(factory.createWorkflowRequestTableFieldFieldValue(receiverId));
            WorkflowRequestTableField[5].setView(true);
            WorkflowRequestTableField[5].setEdit(true);

            ArrayOfWorkflowRequestTableField arrayOfFields = new ArrayOfWorkflowRequestTableField();
            for (WorkflowRequestTableField field : WorkflowRequestTableField) {
                arrayOfFields.getWorkflowRequestTableField().add(field);
            }
            workflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
            workflowRequestTableRecord[0].setWorkflowRequestTableFields(factory.createWorkflowRequestTableRecordWorkflowRequestTableFields(arrayOfFields));
            ArrayOfWorkflowRequestTableRecord arrayOfRecords = new ArrayOfWorkflowRequestTableRecord();
            arrayOfRecords.getWorkflowRequestTableRecord().add(workflowRequestTableRecord[0]);
            workflowMainTableInfo.setRequestRecords(factory.createWorkflowMainTableInfoRequestRecords(arrayOfRecords));
            workflowRequestInfo.setWorkflowMainTableInfo(factory.createWorkflowRequestInfoWorkflowMainTableInfo(workflowMainTableInfo));

            // 明细表（可选，按需添加）
            // WorkflowDetailTableInfo[] workflowDetailTableInfo = ...
            // workflowRequestInfo.setWorkflowDetailTableInfos(workflowDetailTableInfo);

            // 实际调用WebService
            // 假设已通过wsimport生成了WorkflowService和WorkflowServicePortType类
            // 并且WebService地址、用户ID等已知
            String wsdlUrl = appConfig.getHost()+"/services/WorkflowService?wsdl";
            int userid = Integer.parseInt(senderId); // 替换为实际用户ID

            // 创建服务和端口
            WorkflowService service = new WorkflowService(
                    new URL(wsdlUrl));
            WorkflowServicePortType port = service.getWorkflowServiceHttpPort();

            // 调用接口
            String requestId = port.doCreateWorkflowRequest(workflowRequestInfo, userid);
            System.out.println("requestid: " + requestId);
        } catch (Exception e) {
            System.out.println("推送消息到OA工作流异常");
            e.printStackTrace();
        }
        return null;
    }




}
