
package com.niimbot.asset.thirdparty.weaver.webservices;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

import com.niimbot.asset.thirdparty.weaver.webservices.workflow.ArrayOfWorkflowBaseInfo;
import com.niimbot.asset.thirdparty.weaver.webservices.workflow.ArrayOfWorkflowRequestInfo;
import com.niimbot.asset.thirdparty.weaver.webservices.workflow.ArrayOfWorkflowRequestLog;
import com.niimbot.asset.thirdparty.weaver.webservices.workflow.WorkflowRequestInfo;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "WorkflowServicePortType", targetNamespace = "webservices.services.weaver.com.cn")
@XmlSeeAlso({
    ObjectFactory.class,
    com.niimbot.asset.thirdparty.weaver.webservices.workflow.ObjectFactory.class
})
public interface WorkflowServicePortType {


    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.WorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowRequest")
    @ResponseWrapper(localName = "getWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowRequestResponse")
    public WorkflowRequestInfo getWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getHendledWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getHendledWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestList")
    @ResponseWrapper(localName = "getHendledWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getHendledWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.WorkflowRequestInfo
     */
    @WebMethod(operationName = "getWorkflowRequest4split", action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowRequest4split")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowRequest4split", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowRequest4Split")
    @ResponseWrapper(localName = "getWorkflowRequest4splitResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowRequest4SplitResponse")
    public WorkflowRequestInfo getWorkflowRequest4Split(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.submitWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "submitWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.SubmitWorkflowRequest")
    @ResponseWrapper(localName = "submitWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.SubmitWorkflowRequestResponse")
    public String submitWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        WorkflowRequestInfo in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        String in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getMyWorkflowRequestCount4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getMyWorkflowRequestCount4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestCount4OS")
    @ResponseWrapper(localName = "getMyWorkflowRequestCount4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestCount4OSResponse")
    public int getMyWorkflowRequestCount4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in2);

    /**
     * 
     * @param in0
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getTodoData")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getTodoData", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetTodoData")
    @ResponseWrapper(localName = "getTodoDataResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetTodoDataResponse")
    public String getTodoData(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCCWorkflowRequestList4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCCWorkflowRequestList4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestList4OS")
    @ResponseWrapper(localName = "getCCWorkflowRequestList4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestList4OSResponse")
    public ArrayOfWorkflowRequestInfo getCCWorkflowRequestList4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in5);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getLeaveDays")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getLeaveDays", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetLeaveDays")
    @ResponseWrapper(localName = "getLeaveDaysResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetLeaveDaysResponse")
    public String getLeaveDays(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        String in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getHendledWorkflowRequestList4Ofs")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getHendledWorkflowRequestList4Ofs", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestList4Ofs")
    @ResponseWrapper(localName = "getHendledWorkflowRequestList4OfsResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestList4OfsResponse")
    public ArrayOfWorkflowRequestInfo getHendledWorkflowRequestList4Ofs(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in5);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowBaseInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowList")
    @ResponseWrapper(localName = "getCreateWorkflowListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowListResponse")
    public ArrayOfWorkflowBaseInfo getCreateWorkflowList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        int in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in5);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getProcessedWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getProcessedWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestCount")
    @ResponseWrapper(localName = "getProcessedWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestCountResponse")
    public int getProcessedWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.doCreateRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "doCreateRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DoCreateRequest")
    @ResponseWrapper(localName = "doCreateRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DoCreateRequestResponse")
    public String doCreateRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        WorkflowRequestInfo in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.doCreateWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "doCreateWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DoCreateWorkflowRequest")
    @ResponseWrapper(localName = "doCreateWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DoCreateWorkflowRequestResponse")
    public String doCreateWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        WorkflowRequestInfo in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToDoWorkflowRequestCount4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToDoWorkflowRequestCount4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestCount4OS")
    @ResponseWrapper(localName = "getToDoWorkflowRequestCount4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestCount4OSResponse")
    public int getToDoWorkflowRequestCount4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in2);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.doForceOver")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "doForceOver", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DoForceOver")
    @ResponseWrapper(localName = "doForceOverResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DoForceOverResponse")
    public String doForceOver(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getProcessedWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getProcessedWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestList")
    @ResponseWrapper(localName = "getProcessedWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getProcessedWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.WorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowRequestInfo")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowRequestInfo", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowRequestInfo")
    @ResponseWrapper(localName = "getCreateWorkflowRequestInfoResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowRequestInfoResponse")
    public WorkflowRequestInfo getCreateWorkflowRequestInfo(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowBaseInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowTypeList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowTypeList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowTypeList")
    @ResponseWrapper(localName = "getCreateWorkflowTypeListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowTypeListResponse")
    public ArrayOfWorkflowBaseInfo getCreateWorkflowTypeList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getHendledWorkflowRequestCount4Ofs")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getHendledWorkflowRequestCount4Ofs", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestCount4Ofs")
    @ResponseWrapper(localName = "getHendledWorkflowRequestCount4OfsResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestCount4OfsResponse")
    public int getHendledWorkflowRequestCount4Ofs(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in2);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getMyWorkflowRequestList4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getMyWorkflowRequestList4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestList4OS")
    @ResponseWrapper(localName = "getMyWorkflowRequestList4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestList4OSResponse")
    public ArrayOfWorkflowRequestInfo getMyWorkflowRequestList4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in5);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getProcessedWorkflowRequestList4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getProcessedWorkflowRequestList4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestList4OS")
    @ResponseWrapper(localName = "getProcessedWorkflowRequestList4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestList4OSResponse")
    public ArrayOfWorkflowRequestInfo getProcessedWorkflowRequestList4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in5);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getForwardWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getForwardWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetForwardWorkflowRequestCount")
    @ResponseWrapper(localName = "getForwardWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetForwardWorkflowRequestCountResponse")
    public int getForwardWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToDoWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToDoWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestCount")
    @ResponseWrapper(localName = "getToDoWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestCountResponse")
    public int getToDoWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.givingOpinions")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "givingOpinions", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GivingOpinions")
    @ResponseWrapper(localName = "givingOpinionsResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GivingOpinionsResponse")
    public String givingOpinions(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCCWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCCWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestList")
    @ResponseWrapper(localName = "getCCWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getCCWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getProcessedWorkflowRequestCount4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getProcessedWorkflowRequestCount4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestCount4OS")
    @ResponseWrapper(localName = "getProcessedWorkflowRequestCount4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetProcessedWorkflowRequestCount4OSResponse")
    public int getProcessedWorkflowRequestCount4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in2);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getBeRejectWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getBeRejectWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetBeRejectWorkflowRequestCount")
    @ResponseWrapper(localName = "getBeRejectWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetBeRejectWorkflowRequestCountResponse")
    public int getBeRejectWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.forward2WorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "forward2WorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.Forward2WorkflowRequest")
    @ResponseWrapper(localName = "forward2WorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.Forward2WorkflowRequestResponse")
    public String forward2WorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getAllWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getAllWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetAllWorkflowRequestList")
    @ResponseWrapper(localName = "getAllWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetAllWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getAllWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToDoWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToDoWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestList")
    @ResponseWrapper(localName = "getToDoWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getToDoWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCCWorkflowRequestCount4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCCWorkflowRequestCount4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestCount4OS")
    @ResponseWrapper(localName = "getCCWorkflowRequestCount4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestCount4OSResponse")
    public int getCCWorkflowRequestCount4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in2);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getHendledWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getHendledWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestCount")
    @ResponseWrapper(localName = "getHendledWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetHendledWorkflowRequestCountResponse")
    public int getHendledWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToDoWorkflowRequestList4OS")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToDoWorkflowRequestList4OS", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestList4OS")
    @ResponseWrapper(localName = "getToDoWorkflowRequestList4OSResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToDoWorkflowRequestList4OSResponse")
    public ArrayOfWorkflowRequestInfo getToDoWorkflowRequestList4OS(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in5);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToBeReadWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToBeReadWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToBeReadWorkflowRequestCount")
    @ResponseWrapper(localName = "getToBeReadWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToBeReadWorkflowRequestCountResponse")
    public int getToBeReadWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in2);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowCount")
    @ResponseWrapper(localName = "getCreateWorkflowCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowCountResponse")
    public int getCreateWorkflowCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in2);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.forwardWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "forwardWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.ForwardWorkflowRequest")
    @ResponseWrapper(localName = "forwardWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.ForwardWorkflowRequestResponse")
    public String forwardWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToBeReadWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToBeReadWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToBeReadWorkflowRequestList")
    @ResponseWrapper(localName = "getToBeReadWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetToBeReadWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getToBeReadWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in5);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getBeRejectWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getBeRejectWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetBeRejectWorkflowRequestList")
    @ResponseWrapper(localName = "getBeRejectWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetBeRejectWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getBeRejectWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCCWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCCWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestCount")
    @ResponseWrapper(localName = "getCCWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCCWorkflowRequestCountResponse")
    public int getCCWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getAllWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getAllWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetAllWorkflowRequestCount")
    @ResponseWrapper(localName = "getAllWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetAllWorkflowRequestCountResponse")
    public int getAllWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getDoingWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getDoingWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetDoingWorkflowRequestCount")
    @ResponseWrapper(localName = "getDoingWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetDoingWorkflowRequestCountResponse")
    public int getDoingWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in2);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getForwardWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getForwardWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetForwardWorkflowRequestList")
    @ResponseWrapper(localName = "getForwardWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetForwardWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getForwardWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getMyWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getMyWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestList")
    @ResponseWrapper(localName = "getMyWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getMyWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getMyWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getMyWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestCount")
    @ResponseWrapper(localName = "getMyWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetMyWorkflowRequestCountResponse")
    public int getMyWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns webservices.com.niimbot.thirdparty.weaver.ArrayOfString
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowNewFlag")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowNewFlag", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowNewFlag")
    @ResponseWrapper(localName = "getWorkflowNewFlagResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowNewFlagResponse")
    public ArrayOfString getWorkflowNewFlag(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1);

    /**
     * 
     * @param in0
     * @param in1
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.writeWorkflowReadFlag")
    @RequestWrapper(localName = "writeWorkflowReadFlag", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.WriteWorkflowReadFlag")
    @ResponseWrapper(localName = "writeWorkflowReadFlagResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.WriteWorkflowReadFlagResponse")
    public void writeWorkflowReadFlag(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowTypeCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowTypeCount", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowTypeCount")
    @ResponseWrapper(localName = "getCreateWorkflowTypeCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetCreateWorkflowTypeCountResponse")
    public int getCreateWorkflowTypeCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestLog
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowRequestLogs")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowRequestLogs", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowRequestLogs")
    @ResponseWrapper(localName = "getWorkflowRequestLogsResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetWorkflowRequestLogsResponse")
    public ArrayOfWorkflowRequestLog getWorkflowRequestLogs(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        int in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns boolean
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.deleteRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "deleteRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DeleteRequest")
    @ResponseWrapper(localName = "deleteRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.DeleteRequestResponse")
    public boolean deleteRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getUserid")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getUserId", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetUserId")
    @ResponseWrapper(localName = "getUserIdResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetUserIdResponse")
    public String getUserId(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.niimbot.asset.weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getDoingWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getDoingWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetDoingWorkflowRequestList")
    @ResponseWrapper(localName = "getDoingWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "webservices.com.niimbot.thirdparty.weaver.GetDoingWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getDoingWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        boolean in5);

}
