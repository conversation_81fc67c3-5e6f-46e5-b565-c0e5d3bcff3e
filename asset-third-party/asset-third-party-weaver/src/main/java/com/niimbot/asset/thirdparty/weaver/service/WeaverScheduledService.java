package com.niimbot.asset.thirdparty.weaver.service;

import com.niimbot.asset.thirdparty.service.impl.OrgStructureHandler;
import com.niimbot.asset.thirdparty.weaver.config.WeaverAppConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class WeaverScheduledService {

    private static final String SYNC_LOCK_KEY = "weaver_sync_lock";

    private final RedissonClient redissonClient;
    private final OrgStructureHandler orgStructureHandler;
    private final WeaverAppConfig appConfig;

//    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点自动执行
    public void scheduledSync() {
        RLock lock = redissonClient.getLock(SYNC_LOCK_KEY);
        if (lock.isLocked()) {
            log.warn("Weaver sync is already running");
            return;
        }
        lock.lock();
        try {
            sync();
        } finally {
            lock.unlock();
        }
    }

    public void sync() {
        log.info("============== Start Weaver sync ==============");
        try {
            // 1. Sync all
            log.info("============== Start sync all ==============");
            orgStructureHandler.getService(appConfig.getPlatform()).overallHandle(appConfig.getCompanyId());
            log.info("============== End sync all ==============");
        } catch (Exception e) {
            log.error("Weaver sync failed", e);
            throw e;
        }
    }
} 