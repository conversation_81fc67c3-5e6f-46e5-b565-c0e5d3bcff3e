package com.niimbot.asset.thirdparty.weaver.hrm;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/12 10:24
 */
@Data
public class HrmDept {

    // 部门id
    private String id;

    // 分部id
    private String subcompanyid1;

    // 封存标志；默认查询非封存数据。1:封存。
    private String canceled;

    // 上级部门id
    private String supdepid;

    // 简称
    private String departmentmark;

    // 全称
    private String departmentname;

    // 编码
    private String departmentcode;

    // 创建时间戳
    private String created;

    // 修改时间戳
    private String modified;

    // 自定义数据，具体参考 【组织权限中心-自定义设置-部门字段定义】
    private String showorder;

}
