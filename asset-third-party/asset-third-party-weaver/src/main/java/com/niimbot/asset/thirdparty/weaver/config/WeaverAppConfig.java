package com.niimbot.asset.thirdparty.weaver.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Getter
@Configuration
@PropertySource("classpath:weaver.properties")
public class WeaverAppConfig {

    /**
     * key泛微平台
     */
    @Value("${platform}")
    private String platform;

    /**
     * 公司id
     */
    @Value("${company.id}")
    private Long companyId;

    /**
     * ecology系统地址
     */
    @Value("${ecology.host}")
    private String host;

    /**
     * 是否开启debug
     */
    @Value("${asset.debug:false}")
    private Boolean debug;

    @Value("${mock.url.company}")
    private String mockUrlCompany;

    @Value("${mock.url.dept}")
    private String mockUrlDept;

    @Value("${mock.url.person}")
    private String mockUrlPerson;
}
