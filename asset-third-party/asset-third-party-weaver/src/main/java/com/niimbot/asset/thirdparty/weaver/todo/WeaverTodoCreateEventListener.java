package com.niimbot.asset.thirdparty.weaver.todo;

import cn.hutool.core.convert.Convert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.event.TodoCreateEvent;
import com.niimbot.asset.thirdparty.weaver.oa.WeaverOAClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeaverTodoCreateEventListener implements ApplicationListener<TodoCreateEvent> {

    private static final Map<String, String> DJ_URL = new HashMap<>();

    static {
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_RECEIVE), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_RETURN), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_BORROW), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_BACK), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_ALLOCATE), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_REPAIR_REPORT), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_REPAIR), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_DISPOSE), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_CHANGE), "/#/backlog/asset-order-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_PURCHASE), "/#/backlog/purchase-apply-info?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_PURCHASE_ORDER), "/#/backlog/purchase-order-info?orderType=%s&docId=%s");

        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_MATERIAL_RK), "/#/backlog/material-order-detail?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_MATERIAL_CK), "/#/backlog/material-order-detail?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_MATERIAL_LY), "/#/backlog/material-order-detail?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_MATERIAL_TZ), "/#/backlog/material-order-detail?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_MATERIAL_DB), "/#/backlog/material-order-detail?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_MATERIAL_BS), "/#/backlog/material-order-detail?orderType=%s&docId=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_MATERIAL_TK), "/#/backlog/material-order-detail?orderType=%s&docId=%s");

        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_ASSET_REPORT), "/#/backlog/asset-report-child-task-view?id=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_ASSET_INVENTORY), "/#/backlog/inventory-task-view?id=%s");
        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_ASSET_INVENTORY_EXAMINE), "/#/backlog/inventory-task-view?id=%s");

        DJ_URL.put(Convert.toStr(AssetConstant.ORDER_TYPE_STORE), "/#/backlog/asset-storage-info?orderType=%s&docId=%s");

    }

    @Value("${asset.domain.pc}")
    protected String pcDomain;
    List<Short> cod = Arrays.asList((short) 96, (short) 98, (short) 99);
    @Resource
    private AsThirdPartyEmployeeService thirdPartyEmployeeService;
    @Resource
    private WeaverOAClient weaverOAClient;

    @Override
    public void onApplicationEvent(TodoCreateEvent event) {
        log.info("泛微接收待办事件发布");
//        try {
//            TodoCreateMessageDto source = (TodoCreateMessageDto) event.getSource();
//            // 获取发送人信息
//            AsThirdPartyEmployee sender = thirdPartyEmployeeService.getOne(new LambdaQueryWrapper<AsThirdPartyEmployee>()
//                    .eq(AsThirdPartyEmployee::getEmployeeId, source.getCreateBy()));
//            // 获取接收人信息
//            AsThirdPartyEmployee receiver = thirdPartyEmployeeService.getOne(new LambdaQueryWrapper<AsThirdPartyEmployee>()
//                    .eq(AsThirdPartyEmployee::getEmployeeId, source.getHandleUser()));
//            // 如果发送人或接收人是第三方员工，且其cod在指定范围内，则不处理
//            if (ObjectUtil.isNotNull(receiver)) {
//                log.info("第三方员工信息：{}", receiver);
//                log.info("泛微待办信息：{}", source);
//                // 推送待办到泛微OA
//                weaverOAClient.pushMessageToWorkflow(
//                    source.getTitle(), // 标题
//                    source.getOrderData(), // 内容
//                    pcDomain + String.format(DJ_URL.get(String.valueOf(source.getOrderType())), source.getOrderType(), source.getBusinessId()), // PC端链接
//                    pcDomain + String.format(DJ_URL.get(String.valueOf(source.getOrderType())), source.getOrderType(), source.getBusinessId()), // PC端链接
//                    String.valueOf(sender.getUserId()), // 发送人ID
//                    String.valueOf(receiver.getUserId()) // 接收人ID
//                );
//            }
//        } catch (Exception e) {
//            log.error("发送泛微待办消息失败了", e);
//        }
    }
}
