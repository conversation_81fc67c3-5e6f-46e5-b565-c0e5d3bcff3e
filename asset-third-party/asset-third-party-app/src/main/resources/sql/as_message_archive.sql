-- 消息归档表
CREATE TABLE `as_message_archive` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `event_id` varchar(64) NOT NULL COMMENT '事件ID',
  `third_party_type` varchar(32) NOT NULL COMMENT '第三方系统类型(yanhuang,dingtalk,wechat,feishu)',
  `event_type` varchar(64) NOT NULL COMMENT '事件类型(TODO_CREATE,TODO_COMPLETE,TODO_DELETE,SSO_AUTH,ORG_SYNC_USER,ORG_SYNC_DEPT,ORG_SYNC_ROLE,ASSET_SYNC_CREATE,ASSET_SYNC_UPDATE,ASSET_SYNC_DELETE)',
  `raw_message` text COMMENT '原始消息内容',
  `failure_reason` varchar(1000) COMMENT '失败原因',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '当前重试次数',
  `max_retry_count` int(11) NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `status` varchar(32) NOT NULL DEFAULT 'FAILED' COMMENT '状态(FAILED-失败,RETRYING-重试中,SUCCESS-成功,FINAL_FAILED-最终失败)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_retry_time` datetime DEFAULT NULL COMMENT '最后重试时间',
  `next_retry_time` datetime DEFAULT NULL COMMENT '下次重试时间',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_event_id` (`event_id`),
  KEY `idx_third_party_type` (`third_party_type`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_next_retry_time` (`next_retry_time`),
  KEY `idx_third_party_event` (`third_party_type`, `event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息归档表-通用归档表，支持多种第三方集成类型';