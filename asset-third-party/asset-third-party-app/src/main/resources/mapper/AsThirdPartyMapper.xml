<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.thirdparty.mapper.AsThirdPartyMapper">

    <resultMap id="BaseResultMap" type="com.niimbot.asset.thirdparty.model.AsThirdParty">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="type" property="type"/>
        <result column="form" property="form" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectWechatByCorpId" resultMap="BaseResultMap">
        SELECT *
        FROM as_third_party
        WHERE type = 'WECHAT'
          AND `form` ->> '$.corpId' = #{corpId}
    </select>

    <select id="selectDingtalkByAppKeyAndSecret" resultMap="BaseResultMap">
        SELECT *
        FROM as_third_party
        WHERE type = 'DINGTALK' AND `form` ->> '$.appKey' = #{appKey} AND `form` ->> '$.appSecret' = #{appSecret} limit 1
    </select>

    <select id="selectFeishuByAppIdAndSecret" resultMap="BaseResultMap">
        SELECT *
        FROM as_third_party
        WHERE type = 'FEISHU' AND `form` ->> '$.appId' = #{appId} AND `form` ->> '$.appSecret' = #{appSecret} limit 1
    </select>

    <delete id="deleteWechatByCorpId">
        DELETE
        as_third_party WHERE type = 'WECHAT' AND `form` ->> '$.corpId' =
        #{corpId}
    </delete>

    <select id="selectCompanyIdByCorpId" resultType="java.lang.Long">
        SELECT company_id FROM as_third_party WHERE type = 'WECHAT' AND `form` ->> '$.corpId' = #{corpId}
    </select>

</mapper>
