package com.niimbot.asset.thirdparty.strategy.dingtalk;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.strategy.TypeSign;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:43
 */
public interface DingCallbackStrategy extends TypeSign {

    boolean handleCallback(Long companyId, String accessToken, JSONObject content);

    @Override
    default String getType() {
        return ThirdPartyConstant.DINGTALK;
    }
}
