package com.niimbot.asset.thirdparty.constant;

import com.niimbot.thirdparty.WeChatAgent;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/11/8 16:13
 */
@Data
@ToString(callSuper = true)
@Accessors(chain = true)
public class ThirdPartyAkSk {

    @Data
    @ToString(callSuper = true)
    @Accessors(chain = true)
    public static class DingTalk {

        private String appKey;

        private String appSecret;

        private String aesKey;

        private String token;

        private Long agentId;

        private String corpId;
    }

    @Data
    @ToString(callSuper = true)
    @Accessors(chain = true)
    public static class WeChat {

        private String corpId;

        private String decryptCropId;

        private String secret;

        private String encodingAESKey;

        private String token;

        private WeChatAgent userAuthAgent;

    }

    @Data
    @ToString(callSuper = true)
    @Accessors(chain = true)
    public static class Feishu {

        private String appId;

        private String appSecret;

        private String encryptKey;

        private String verificationToken;

    }

    @Data
    @ToString(callSuper = true)
    @Accessors(chain = true)
    public static class Others {

        private String systemName;

        private String syncUrl;

        private String sign;

    }

}
