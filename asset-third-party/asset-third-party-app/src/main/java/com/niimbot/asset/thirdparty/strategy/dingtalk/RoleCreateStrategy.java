package com.niimbot.asset.thirdparty.strategy.dingtalk;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.activiti.model.ActApproveRole;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.thirdparty.support.DingApiSupport;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.LABEL_CONF_ADD)
public class RoleCreateStrategy implements DingCallbackStrategy {

    private static final Logger log = LoggerFactory.getLogger(RoleCreateStrategy.class);
    @Resource
    private DingApiSupport dingApiSupport;

    @Resource
    private ActApproveRoleService approveRoleService;

    @Resource
    private CompanySettingService companySettingService;

    @Override
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        log.info("RoleCreateStrategy handleCallback");
        CompanySwitch setting = companySettingService.getSwitchSettingWithCache(companyId);
        if (!setting.getEnableSyncApproveRole()) {
            return true;
        }
        if (!content.containsKey("LabelIdList")) {
            return true;
        }
        // 角色或角色组ID集合
        List<Long> labelIdList = content.getJSONArray("LabelIdList").toJavaList(Long.class);
        List<DingApiSupport.DingRole> toDb = dingApiSupport.getRoles(companyId, labelIdList);
        List<String> extIds = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .select(ActApproveRole::getId, ActApproveRole::getExternalId)
                        .eq(ActApproveRole::getCompanyId, companyId)
        ).stream().map(ActApproveRole::getExternalId).collect(Collectors.toList());
        toDb.removeIf(v -> extIds.contains(String.valueOf(v.getId())));
        if (CollUtil.isEmpty(toDb)) {
            return true;
        }
        // 去掉已存在的
        List<String> exist = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .in(ActApproveRole::getExternalId, toDb.stream().map(v -> String.valueOf(v.getId())).collect(Collectors.toList()))
        ).stream().map(ActApproveRole::getExternalId).collect(Collectors.toList());
        toDb.removeIf(v -> exist.contains(String.valueOf(v.getId())));
        if (CollUtil.isEmpty(toDb)) {
            return true;
        }
        List<ActApproveRole> save = toDb.stream().distinct().map(role -> new ActApproveRole().setExternalId(String.valueOf(role.getId())).setName(role.getName())).collect(Collectors.toList());
        approveRoleService.recommendRoleCode(companyId, save);
        return true;
    }
}
