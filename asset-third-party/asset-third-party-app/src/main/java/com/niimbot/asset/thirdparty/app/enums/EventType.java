package com.niimbot.asset.thirdparty.app.enums;

/**
 * 事件类型枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
public enum EventType {
    
    // 待办相关
    TODO_CREATE("TODO_CREATE", "创建待办"),
    TODO_COMPLETE("TODO_COMPLETE", "完成待办"),
    TODO_DELETE("TODO_DELETE", "删除待办"),
    
    // 单点登录相关
    SSO_AUTH("SSO_AUTH", "单点登录认证"),
    
    // 组织同步相关
    ORG_SYNC_USER("ORG_SYNC_USER", "同步用户"),
    ORG_SYNC_DEPT("ORG_SYNC_DEPT", "同步部门"),
    ORG_SYNC_ROLE("ORG_SYNC_ROLE", "同步角色"),
    
    // 资产同步相关
    ASSET_SYNC_CREATE("ASSET_SYNC_CREATE", "创建资产"),
    ASSET_SYNC_UPDATE("ASSET_SYNC_UPDATE", "更新资产"),
    ASSET_SYNC_DELETE("ASSET_SYNC_DELETE", "删除资产");
    
    private final String code;
    private final String name;
    
    EventType(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
}