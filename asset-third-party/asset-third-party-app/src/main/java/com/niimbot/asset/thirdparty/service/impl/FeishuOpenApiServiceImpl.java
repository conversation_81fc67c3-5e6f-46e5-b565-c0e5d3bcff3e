package com.niimbot.asset.thirdparty.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.response.RawResponse;
import com.lark.oapi.core.token.AccessTokenType;
import com.lark.oapi.core.utils.UnmarshalRespUtil;
import com.lark.oapi.service.auth.v3.model.InternalTenantAccessTokenReq;
import com.lark.oapi.service.auth.v3.model.InternalTenantAccessTokenReqBody;
import com.lark.oapi.service.auth.v3.model.InternalTenantAccessTokenResp;
import com.lark.oapi.service.authen.v1.model.CreateOidcAccessTokenReq;
import com.lark.oapi.service.authen.v1.model.CreateOidcAccessTokenReqBody;
import com.lark.oapi.service.authen.v1.model.CreateOidcAccessTokenResp;
import com.lark.oapi.service.authen.v1.model.CreateOidcAccessTokenRespBody;
import com.lark.oapi.service.authen.v1.model.GetUserInfoResp;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.lark.oapi.service.contact.v3.model.BatchDepartmentReq;
import com.lark.oapi.service.contact.v3.model.BatchDepartmentResp;
import com.lark.oapi.service.contact.v3.model.BatchDepartmentRespBody;
import com.lark.oapi.service.contact.v3.model.BatchUserReq;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.contact.v3.model.ChildrenDepartmentReq;
import com.lark.oapi.service.contact.v3.model.ChildrenDepartmentResp;
import com.lark.oapi.service.contact.v3.model.ChildrenDepartmentRespBody;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.FindByDepartmentUserReq;
import com.lark.oapi.service.contact.v3.model.FindByDepartmentUserResp;
import com.lark.oapi.service.contact.v3.model.FindByDepartmentUserRespBody;
import com.lark.oapi.service.contact.v3.model.ListScopeReq;
import com.lark.oapi.service.contact.v3.model.ListScopeResp;
import com.lark.oapi.service.contact.v3.model.ListScopeRespBody;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.thirdparty.model.AsThirdParty;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.model.AuthScopes;
import com.niimbot.asset.thirdparty.model.CuzCreateTenantAccessTokenResp;
import com.niimbot.asset.thirdparty.model.CuzJssdkTicketBody;
import com.niimbot.asset.thirdparty.model.CuzJssdkTicketResp;
import com.niimbot.asset.thirdparty.service.FeishuOpenApiService;
import com.niimbot.asset.thirdparty.utils.UrlUtils;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.FeishuJsAuth;
import com.niimbot.thirdparty.FeishuJsTicketDto;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/9 16:37
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeishuOpenApiServiceImpl implements FeishuOpenApiService {

    private static final String TENANT_ACCESS_TOKEN_KEY = "feishu:tenantAccessToken";
    private static final String SIGNATURE_KEY = "feishu:signature";
    private static final String JSAPI_TICKET_URI = "/open-apis/jssdk/ticket/get";

    private static final String MSG_LINK_KEY = "点击查看详情";
    private static final String MSG_LINK_JUMP_URL = "https://applink.feishu.cn/client/web_app/open?appId={appId}&lk_target_url={url}";
    private final RedisService redisService;

    @Override
    public String getAccessToken(ThirdPartyAkSk.Feishu feishu) {
        String cacheKey = getTenantAccessTokenKey(feishu.getAppId());
        if (redisService.hasKey(cacheKey)) {
            return Convert.toStr(redisService.get(cacheKey));
        } else {
            Client client = Client.newBuilder(feishu.getAppId(), feishu.getAppSecret()).build();
            try {
                // 创建请求对象
                InternalTenantAccessTokenReq req = InternalTenantAccessTokenReq.newBuilder()
                        .internalTenantAccessTokenReqBody(InternalTenantAccessTokenReqBody.newBuilder()
                                .appId(feishu.getAppId())
                                .appSecret(feishu.getAppSecret())
                                .build())
                        .build();

                // 发起请求
                InternalTenantAccessTokenResp resp = client.auth().tenantAccessToken().internal(req);
                if (resp.success()) {
                    CuzCreateTenantAccessTokenResp content = UnmarshalRespUtil.unmarshalResp(resp.getRawResponse(), CuzCreateTenantAccessTokenResp.class);
                    String tenantAccessToken = content.getTenant_access_token();
                    redisService.set(cacheKey, tenantAccessToken, content.getExpire() - 600);
                    return tenantAccessToken;
                } else {
                    log.error("get feishu tenantAccessToken error, [{}], {}", resp.getCode(), resp.getMsg());
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取TenantAccessToken异常");
                }
            } catch (Exception e) {
                log.error("get feishu tenantAccessToken error, {}", e.getMessage(), e);
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取TenantAccessToken异常");
            }
        }
    }

    @Override
    public void getAuthScopes(Client feishuClient, AuthScopes authScopes, String pageToken) {
        // 创建请求对象
        ListScopeReq req = ListScopeReq.newBuilder()
                .pageSize(100)
                .pageToken(pageToken)
                .build();
        // 发起请求
        try {
            ListScopeResp resp = feishuClient.contact().scope().list(req);
            if (resp.success()) {
                ListScopeRespBody data = resp.getData();
                if (ArrayUtil.isNotEmpty(data.getUserIds())) {
                    authScopes.getUserIds().addAll(Lists.newArrayList(data.getUserIds()));
                }
                if (ArrayUtil.isNotEmpty(data.getDepartmentIds())) {
                    authScopes.getDepartmentIds().addAll(Lists.newArrayList(data.getDepartmentIds()));
                }
                if (data.getHasMore()) {
                    getAuthScopes(feishuClient, authScopes, data.getPageToken());
                }
            } else {
                log.error("get feishu auth scope error, [{}], {}", resp.getCode(), resp.getMsg());
                throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
            }
        } catch (Exception e) {
            log.error("get feishu auth scope error, {}", e.getMessage(), e);
            throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
        }
    }

    @Override
    public void getChildrenDepartment(Client feishuClient, String departmentId, List<Department> departmentList, String pageToken) {
        if (departmentList == null) {
            departmentList = new ArrayList<>();
        }
        // 创建请求对象
        ChildrenDepartmentReq req = ChildrenDepartmentReq.newBuilder()
                .departmentId(departmentId)
                .fetchChild(true)
                .pageSize(50)
                .pageToken(pageToken)
                .build();

        try {
            ChildrenDepartmentResp resp = feishuClient.contact().department().children(req);
            if (resp.success()) {
                ChildrenDepartmentRespBody data = resp.getData();
                if (ArrayUtil.isNotEmpty(data.getItems())) {
                    departmentList.addAll(Lists.newArrayList(data.getItems()));
                }
                if (data.getHasMore()) {
                    getChildrenDepartment(feishuClient, departmentId, departmentList, data.getPageToken());
                }
            } else {
                log.error("feishu departmentId = {} get child depart ids response is fail [{}]", departmentId, JSONObject.toJSONString(resp));
            }
        } catch (Exception e) {
            log.error("feishu departmentId = {} get child depart ids response is fail [{}]", departmentId, e.getMessage(), e);
        }
    }

    @Override
    public List<Department> listDepartment(Client feishuClient, List<String> departmentIds) {
        if (CollUtil.isEmpty(departmentIds)) {
            return ListUtil.empty();
        }
        BatchDepartmentReq req = BatchDepartmentReq.newBuilder()
                .departmentIds(departmentIds.toArray(new String[]{}))
                .build();

        try {
            BatchDepartmentResp resp = feishuClient.contact().department().batch(req);
            if (resp.success()) {
                BatchDepartmentRespBody data = resp.getData();
                if (ArrayUtil.isNotEmpty(data.getItems())) {
                    return Lists.newArrayList(data.getItems());
                } else {
                    return ListUtil.empty();
                }
            } else {
                log.error("feishu departmentIds = {} get child depart ids response is fail [{}]", departmentIds, JSONObject.toJSONString(resp));
            }
        } catch (Exception e) {
            log.error("feishu departmentIds = {} get child depart ids response is fail [{}]", departmentIds, e.getMessage(), e);
        }
        return ListUtil.empty();
    }

    public void getUserListByDepartmentId(Client feishuClient, String departmentId, List<User> userList, String pageToken) {
        FindByDepartmentUserReq req = FindByDepartmentUserReq.newBuilder()
                .departmentId(departmentId)
                .pageSize(50)
                .pageToken(pageToken)
                .build();

        // 发起请求
        try {
            FindByDepartmentUserResp resp = feishuClient.contact().user().findByDepartment(req);
            if (resp.success()) {
                FindByDepartmentUserRespBody data = resp.getData();
                if (ArrayUtil.isNotEmpty(data.getItems())) {
                    userList.addAll(Lists.newArrayList(data.getItems()));
                }
                if (data.getHasMore()) {
                    getUserListByDepartmentId(feishuClient, departmentId, userList, data.getPageToken());
                }
            } else {
                log.error("feishu departmentId = {} get user list response is fail [{}]", departmentId, JSONObject.toJSONString(resp));
            }
        } catch (Exception e) {
            log.error("feishu departmentId = {} get user list response is fail [{}]", departmentId, e.getMessage(), e);
        }
    }

    @Override
    public String getUserAccessToken(Client client, String authCode) {
        // 创建请求对象
        CreateOidcAccessTokenReq req = CreateOidcAccessTokenReq.newBuilder()
                .createOidcAccessTokenReqBody(CreateOidcAccessTokenReqBody.newBuilder()
                        .grantType("authorization_code")
                        .code(authCode)
                        .build())
                .build();

        // 发起请求
        try {
            CreateOidcAccessTokenResp resp = client.authen().oidcAccessToken().create(req);
            if (resp.success()) {
                CreateOidcAccessTokenRespBody data = resp.getData();
                return data.getAccessToken();
            } else {
                log.error("feishu authCode = {} response is fail [{}]", authCode, JSONObject.toJSONString(resp));
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取user_access_token失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("feishu authCode = {} response is fail [{}]", authCode, e.getMessage(), e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取user_access_token失败，请稍后重试");
        }
    }

    @Override
    public GetUserInfoRespBody getUserInfoByCode(Client client, String accessToken) {
        // 发起请求
        try {
            GetUserInfoResp resp = client.authen().userInfo().get(RequestOptions.newBuilder()
                    .userAccessToken(accessToken)
                    .build());
            if (resp.success()) {
                return resp.getData();
            } else {
                log.error("feishu accessToken = {} response is fail [{}]", accessToken, JSONObject.toJSONString(resp));
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取飞书用户信息失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("feishu accessToken = {} response is fail [{}]", accessToken, e.getMessage(), e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取飞书用户信息失败，请稍后重试");
        }
    }

    @Override
    public FeishuJsTicketDto createJsapiSignature(FeishuJsAuth feishuJsAuth) {
        AsThirdParty thirdParty = Db.getOne(Wrappers.lambdaQuery(AsThirdParty.class)
                .eq(AsThirdParty::getCompanyId, feishuJsAuth.getCompanyId())
                .eq(AsThirdParty::getType, ThirdPartyConstant.FEISHU));
        ThirdPartyAkSk.Feishu feishu = thirdParty.getForm().toJavaObject(ThirdPartyAkSk.Feishu.class);

        String cacheKey = getSignatureKey(feishu.getAppId());

        String ticket;
        if (redisService.hasKey(cacheKey)) {
            ticket = Convert.toStr(redisService.get(cacheKey));
        } else {
            // 构建client
            Client client = Client.newBuilder(feishu.getAppId(), feishu.getAppSecret()).build();
            try {
                RawResponse post = client.post(JSAPI_TICKET_URI, null, AccessTokenType.Tenant);
                CuzJssdkTicketResp resp = UnmarshalRespUtil.unmarshalResp(post, CuzJssdkTicketResp.class);
                if (resp.success()) {
                    CuzJssdkTicketBody data = resp.getData();
                    redisService.set(cacheKey, data.getTicket(), data.getExpireIn() - 600);
                    ticket = data.getTicket();
                } else {
                    log.error("appId = {} get feishu jsapi ticket error", feishu.getAppId());
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取飞书jsApi ticket失败，请稍后重试");
                }
            } catch (Exception e) {
                log.error("appId = {} get feishu jsapi ticket error, {}", feishu.getAppId(), e.getMessage(), e);
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取飞书jsApi ticket失败，请稍后重试");
            }
        }

        // 生成签名
        try {
            String nonceStr = RandomUtil.randomString(8);
            long timeStamp = LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
            String plain = "jsapi_ticket=" + ticket + "&noncestr=" + nonceStr + "&timestamp=" + timeStamp
                    + "&url=" + UrlUtils.decodeUrl(feishuJsAuth.getUrl());
            FeishuJsTicketDto ticketDto = new FeishuJsTicketDto();
            ticketDto.setAppId(feishu.getAppId());
            ticketDto.setTimeStamp(timeStamp);
            ticketDto.setNonceStr(nonceStr);
            ticketDto.setSignature(DigestUtils.sha1Hex(plain));
            ticketDto.setUrl(feishuJsAuth.getUrl());
            return ticketDto;
        } catch (Exception e) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "生成jsApi ticket失败，请稍后重试");
        }
    }

    @Override
    public Boolean sendInnerLinkMessage(String title, String text, String url, List<String> userIds, JSONObject form, Long companyId) {
        if (CollUtil.isEmpty(userIds)) {
            log.warn("send feishu inner message userIds is empty");
            return false;
        }
        try {
            ThirdPartyAkSk.Feishu feishu = form.toJavaObject(ThirdPartyAkSk.Feishu.class);

            // 飞书需要特殊处理跳转
            if (StrUtil.isNotEmpty(url)) {
                url = StrUtil.format(MSG_LINK_JUMP_URL, ImmutableMap.of("appId", feishu.getAppId(), "url", URLEncoder.encode(url, StandardCharsets.UTF_8.toString())));
                text = text.replace(MSG_LINK_KEY, "[" + MSG_LINK_KEY + "](" + url + ")");
            }
            String content = StrUtil.format(INNER_MESSAGE_TPL, ImmutableMap.of("title", title, "text", text));
            // 构建client
            Client client = Client.newBuilder(feishu.getAppId(), feishu.getAppSecret()).build();

            // 创建请求对象
            CreateMessageReq req = CreateMessageReq.newBuilder()
                    .receiveIdType("open_id")
                    .createMessageReqBody(CreateMessageReqBody.newBuilder()
                            .msgType("post")
                            .content(content)
                            .build())
                    .build();
            for (String userId : userIds) {
                req.getCreateMessageReqBody().setReceiveId(userId);
                // 发起请求
                CreateMessageResp resp = client.im().message().create(req);
                if (!resp.success()) {
                    log.error("appId = {} send message card error, {}, title={}, text={}", feishu.getAppId(), resp.getMsg(), title, text);
                }
            }
        } catch (Exception e) {
            log.error("appId = {} send message card error, {}, title={}, text={}", form, e.getMessage(), title, text, e);
        }
        return true;
    }

    @Override
    public User[] listUser(Client feishuClient, List<String> userIds) {
        String[] userIdsArray = userIds.toArray(new String[]{});
        BatchUserReq req = BatchUserReq.newBuilder()
                .userIds(userIdsArray).build();
        // 发起请求
        try {
            BatchUserResp resp = feishuClient.contact().user().batch(req);
            if (resp.success()) {
                return resp.getData().getItems();
            } else {
                log.error("feishu list user batch response is fail [{}]", JSONObject.toJSONString(resp));
            }
        } catch (Exception e) {
            log.error("feishu list user batch response is fail [{}]", e.getMessage(), e);
        }
        return new User[]{};
    }

    private String getTenantAccessTokenKey(String appId) {
        return TENANT_ACCESS_TOKEN_KEY + ":" + appId;
    }

    private String getSignatureKey(String appId) {
        return SIGNATURE_KEY + ":" + appId;
    }

    /**
     * Demo { "zh_cn": { "title": "{title}", "content": [ [ { "tag": "md", "text":
     * "【精臣云资产】您测试仓库有53个耗材低于安全库存，请及时采购， [点击查看详情](https://markdown.com.cn)。" } ] ] } }
     */
    private static final String INNER_MESSAGE_TPL = "{" +
            "  \"zh_cn\": { " +
            "    \"title\": \"{title}\", " +
            "    \"content\": [ " +
            "      [ " +
            "        { " +
            "          \"tag\": \"md\", " +
            "          \"text\": \"{text}\"" +
            "        } " +
            "      ] " +
            "    ] " +
            "  } " +
            "}";

}
