package com.niimbot.asset.thirdparty.strategy.feishu;

import com.alibaba.fastjson.JSONObject;
import com.lark.oapi.service.contact.v3.model.P2UserDeletedV3;
import com.lark.oapi.service.contact.v3.model.UserEvent;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.FeishuCallbackEvent;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/15 14:14
 */
@Slf4j
@Component
@CallbackStrategy(eventType = FeishuCallbackEvent.USER_DELETE)
public class UserDeleteStrategy extends AbsUserStrategy implements FeishuCallbackStrategy {

    @Override
    public boolean handleCallback(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        P2UserDeletedV3 userDeletedV3 = content.toJavaObject(P2UserDeletedV3.class);
        if (checkToken(feishu.getVerificationToken(), userDeletedV3.getHeader())) {
            UserEvent eventData = userDeletedV3.getEvent().getObject();
            deleteUser(eventData.getOpenId(), companyId, getType());
            return true;
        } else {
            log.error("companyId = {}, check token error", companyId);
            return false;
        }
    }

}
