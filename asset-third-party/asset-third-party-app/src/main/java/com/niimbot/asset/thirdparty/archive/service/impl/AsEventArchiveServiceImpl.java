package com.niimbot.asset.thirdparty.archive.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.thirdparty.archive.dto.AsEventArchiveQueryDto;
import com.niimbot.asset.thirdparty.archive.entity.AsEventArchive;
import com.niimbot.asset.thirdparty.archive.service.AsEventArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息归档服务实现类 - 通用归档服务实现
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
@Service
@Slf4j
public class AsEventArchiveServiceImpl implements AsEventArchiveService {
    
    private final Map<String, AsEventArchive> archiveStore = new ConcurrentHashMap<>();
    
    @Override
    public void saveArchive(AsEventArchive archive) {
        archiveStore.put(archive.getEventId(), archive);
        log.info("保存归档消息: eventId={}, thirdPartyType={}, eventType={}", 
                archive.getEventId(), archive.getThirdPartyType(), archive.getEventType());
    }
    
    @Override
    public AsEventArchive getArchive(String eventId) {
        return archiveStore.get(eventId);
    }
    
    @Override
    public void updateArchive(AsEventArchive archive) {
        archiveStore.put(archive.getEventId(), archive);
        log.debug("更新归档消息: eventId={}, status={}, retryCount={}", 
                 archive.getEventId(), archive.getStatus(), archive.getRetryCount());
    }
    
    @Override
    public void removeArchive(String eventId) {
        AsEventArchive removed = archiveStore.remove(eventId);
        if (removed != null) {
            log.info("移除归档消息: eventId={}, thirdPartyType={}, eventType={}", 
                    eventId, removed.getThirdPartyType(), removed.getEventType());
        }
    }
    
    @Override
    public List<AsEventArchive> getArchivesByThirdPartyType(String thirdPartyType) {
        return archiveStore.values().stream()
                .filter(archive -> thirdPartyType.equals(archive.getThirdPartyType()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AsEventArchive> getArchivesByStatus(String status) {
        return archiveStore.values().stream()
                .filter(archive -> status.equals(archive.getStatus()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AsEventArchive> getRetryableArchives() {
        LocalDateTime now = LocalDateTime.now();
        return archiveStore.values().stream()
                .filter(AsEventArchive::canRetry)
                .filter(archive -> archive.getNextRetryTime() == null || 
                                 archive.getNextRetryTime().isBefore(now))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AsEventArchive> getArchivesByTypeAndEvent(String thirdPartyType, String eventType) {
        return archiveStore.values().stream()
                .filter(archive -> thirdPartyType.equals(archive.getThirdPartyType()) && 
                                 eventType.equals(archive.getEventType()))
                .collect(Collectors.toList());
    }

    @Override
    public IPage<AsEventArchive> page(AsEventArchiveQueryDto queryDto) {
        List<AsEventArchive> filteredList = filterArchives(queryDto);
        
        // 手动分页
        int current = queryDto.getCurrent() != null ? queryDto.getCurrent().intValue() : 1;
        int size = queryDto.getSize() != null ? queryDto.getSize().intValue() : 10;
        int start = (current - 1) * size;
        int end = Math.min(start + size, filteredList.size());
        
        List<AsEventArchive> pageData = start < filteredList.size() ? 
            filteredList.subList(start, end) : Collections.emptyList();
        
        // 创建分页结果
        IPage<AsEventArchive> page = new Page<>(current, size, filteredList.size());
        page.setRecords(pageData);
        return page;
    }

    @Override
    public List<AsEventArchive> list(AsEventArchiveQueryDto queryDto) {
        return filterArchives(queryDto);
    }

    @Override
    public AsEventArchive getById(Long id) {
        return archiveStore.values().stream()
                .filter(archive -> id.equals(archive.getId()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public AsEventArchive getByEventId(String eventId) {
        return archiveStore.get(eventId);
    }

    @Override
    public Boolean retryEvent(Long id) {
        AsEventArchive archive = getById(id);
        if (archive == null || !archive.canRetry()) {
            return false;
        }
        
        // 重置状态为可重试
        archive.setStatus("FAILED");
        archive.setNextRetryTime(LocalDateTime.now());
        updateArchive(archive);
        log.info("手动重试事件: id={}, eventId={}", id, archive.getEventId());
        return true;
    }

    @Override
    public Boolean retryEvents(List<Long> ids) {
        boolean allSuccess = true;
        for (Long id : ids) {
            if (!retryEvent(id)) {
                allSuccess = false;
            }
        }
        return allSuccess;
    }

    @Override
    public Boolean removeById(Long id) {
        AsEventArchive archive = getById(id);
        if (archive != null) {
            removeArchive(archive.getEventId());
            return true;
        }
        return false;
    }

    @Override
    public Boolean removeByIds(List<Long> ids) {
        boolean allSuccess = true;
        for (Long id : ids) {
            if (!removeById(id)) {
                allSuccess = false;
            }
        }
        return allSuccess;
    }

    @Override
    public List<String> getThirdPartyTypes() {
        return archiveStore.values().stream()
                .map(AsEventArchive::getThirdPartyType)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getEventTypes() {
        return archiveStore.values().stream()
                .map(AsEventArchive::getEventType)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getStatusList() {
        return archiveStore.values().stream()
                .map(AsEventArchive::getStatus)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据查询条件过滤归档数据
     */
    private List<AsEventArchive> filterArchives(AsEventArchiveQueryDto queryDto) {
        return archiveStore.values().stream()
                .filter(archive -> {
                    // 事件ID过滤
                    if (queryDto.getEventId() != null && 
                        !queryDto.getEventId().equals(archive.getEventId())) {
                        return false;
                    }
                    
                    // 第三方类型过滤
                    if (queryDto.getThirdPartyType() != null && 
                        !queryDto.getThirdPartyType().equals(archive.getThirdPartyType())) {
                        return false;
                    }
                    
                    // 事件类型过滤
                    if (queryDto.getEventType() != null && 
                        !queryDto.getEventType().equals(archive.getEventType())) {
                        return false;
                    }
                    
                    // 状态过滤
                    if (queryDto.getStatus() != null && 
                        !queryDto.getStatus().equals(archive.getStatus())) {
                        return false;
                    }
                    
                    // 状态列表过滤
                    if (queryDto.getStatusList() != null && !queryDto.getStatusList().isEmpty() &&
                        !queryDto.getStatusList().contains(archive.getStatus())) {
                        return false;
                    }
                    
                    // 失败原因关键词过滤
                    if (queryDto.getFailureReason() != null && 
                        (archive.getFailureReason() == null || 
                         !archive.getFailureReason().contains(queryDto.getFailureReason()))) {
                        return false;
                    }
                    
                    // 重试次数范围过滤
                    if (queryDto.getMinRetryCount() != null && 
                        archive.getRetryCount() < queryDto.getMinRetryCount()) {
                        return false;
                    }
                    if (queryDto.getMaxRetryCount() != null && 
                        archive.getRetryCount() > queryDto.getMaxRetryCount()) {
                        return false;
                    }
                    
                    // 创建时间范围过滤
                    if (queryDto.getCreateTimeStart() != null && 
                        archive.getCreateTime().isBefore(queryDto.getCreateTimeStart())) {
                        return false;
                    }
                    if (queryDto.getCreateTimeEnd() != null && 
                        archive.getCreateTime().isAfter(queryDto.getCreateTimeEnd())) {
                        return false;
                    }
                    
                    // 下次重试时间范围过滤
                    if (queryDto.getNextRetryTimeStart() != null && 
                        (archive.getNextRetryTime() == null || 
                         archive.getNextRetryTime().isBefore(queryDto.getNextRetryTimeStart()))) {
                        return false;
                    }
                    if (queryDto.getNextRetryTimeEnd() != null && 
                        (archive.getNextRetryTime() == null || 
                         archive.getNextRetryTime().isAfter(queryDto.getNextRetryTimeEnd()))) {
                        return false;
                    }
                    
                    return true;
                })
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime())) // 按创建时间倒序
                .collect(Collectors.toList());
    }
}
