package com.niimbot.asset.thirdparty.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.event.EventCO;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.strategy.dingtalk.DingCallbackStrategy;
import com.niimbot.asset.thirdparty.strategy.feishu.FeishuCallbackStrategy;
import com.niimbot.asset.thirdparty.strategy.others.OthersCallbackStrategy;
import com.niimbot.asset.thirdparty.strategy.wechat.WeChatCallbackStrategy;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:46
 */
@Slf4j
@Component
@ComponentScan("com.niimbot.asset.thirdparty.strategy")
public class CallbackContext {

    private final RedissonClient redissonClient;
    private static final String FEISHU_EVENT_LOCK = "feishu:event_lock:";
    private static final String OTHERS_EVENT_LOCK = "others:event_lock:";

    private final Map<String, DingCallbackStrategy> dingStrategyMap = new ConcurrentHashMap<>();
    private final Map<String, WeChatCallbackStrategy> wechatStrategyMap = new ConcurrentHashMap<>();
    private final Map<String, FeishuCallbackStrategy> feishuStrategyMap = new ConcurrentHashMap<>();
    private final Map<String, OthersCallbackStrategy> othersStrategyMap = new ConcurrentHashMap<>();

    public CallbackContext(RedissonClient redissonClient,
                           List<DingCallbackStrategy> dingStrategies,
                           List<WeChatCallbackStrategy> wechatStrategies,
                           List<FeishuCallbackStrategy> feishuStrategies,
                           List<OthersCallbackStrategy> othersStrategies) {
        this.redissonClient = redissonClient;
        for (DingCallbackStrategy strategy : dingStrategies) {
            CallbackStrategy annotation = strategy.getClass().getAnnotation(CallbackStrategy.class);
            if (ObjectUtil.isNotNull(annotation)) {
                String eventType = annotation.eventType();
                if (StrUtil.isNotBlank(eventType)) {
                    dingStrategyMap.put(eventType, strategy);
                }
            }
        }

        for (WeChatCallbackStrategy strategy : wechatStrategies) {
            CallbackStrategy annotation = strategy.getClass().getAnnotation(CallbackStrategy.class);
            if (ObjectUtil.isNotNull(annotation)) {
                String eventType = annotation.eventType();
                if (StrUtil.isNotBlank(eventType)) {
                    wechatStrategyMap.put(eventType, strategy);
                }
            }
        }

        for (FeishuCallbackStrategy strategy : feishuStrategies) {
            CallbackStrategy annotation = strategy.getClass().getAnnotation(CallbackStrategy.class);
            if (ObjectUtil.isNotNull(annotation)) {
                String eventType = annotation.eventType();
                if (StrUtil.isNotBlank(eventType)) {
                    feishuStrategyMap.put(eventType, strategy);
                }
            }
        }

        for (OthersCallbackStrategy strategy : othersStrategies) {
            CallbackStrategy annotation = strategy.getClass().getAnnotation(CallbackStrategy.class);
            if (ObjectUtil.isNotNull(annotation)) {
                String eventType = annotation.eventType();
                if (StrUtil.isNotBlank(eventType)) {
                    othersStrategyMap.put(eventType, strategy);
                }
            }
        }
    }

    public boolean executeDing(Long companyId, String accessToken, JSONObject content) {
        String type = content.getString("EventType");
        if (this.dingStrategyMap.containsKey(type)) {
            if (companyId != null && StrUtil.isNotEmpty(accessToken)) {
                return this.dingStrategyMap.get(type).handleCallback(companyId, accessToken, content);
            } else {
                log.error("companyId or accessToken is null, " + content);
                return false;
            }
        } else {
            return false;
        }
    }

    public boolean executeWeChat(Long companyId, WxCpTpXmlMessage message) {
        String type = message.getChangeType();
        if (this.wechatStrategyMap.containsKey(type)) {
            if (companyId != null) {
                return this.wechatStrategyMap.get(type).handleCallback(companyId, message);
            } else {
                log.error("companyId is null, " + message);
                return false;
            }
        } else {
            return false;
        }
    }

    public boolean executeFeishu(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        JSONObject header = content.getJSONObject("header");
        String type = header.getString("event_type");
        if (this.feishuStrategyMap.containsKey(type)) {
            if (companyId != null && ObjectUtil.isNotNull(feishu)) {
                String eventId = header.getString("event_id");
                RLock lock = redissonClient.getLock(FEISHU_EVENT_LOCK + eventId);
                if (lock.isLocked()) {
                    return false;
                } else {
                    lock.lock();
                    try {
                        return this.feishuStrategyMap.get(type).handleCallback(companyId, feishu, content);
                    } finally {
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                }
            } else {
                log.error("companyId or feishu aksk is null, " + content);
                return false;
            }
        } else {
            return false;
        }
    }

    public boolean executeOthers(Long companyId, EventCO event) {
        if (this.othersStrategyMap.containsKey(event.getEventType())) {
            if (companyId != null) {
                RLock lock = redissonClient.getLock(OTHERS_EVENT_LOCK + event.getEventId());
                if (lock.isLocked()) {
                    return false;
                } else {
                    lock.lock();
                    try {
                        return this.othersStrategyMap.get(event.getEventType())
                                .handleCallback(companyId, event.getContent());
                    } finally {
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                }
            } else {
                log.error("companyId is null, " + event);
                return false;
            }
        } else {
            return false;
        }
    }

}
