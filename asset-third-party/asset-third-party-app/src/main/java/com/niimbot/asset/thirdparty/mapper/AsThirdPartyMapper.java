package com.niimbot.asset.thirdparty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.thirdparty.model.AsThirdParty;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface AsThirdPartyMapper extends BaseMapper<AsThirdParty> {

    AsThirdParty selectWechatByCorpId(@Param("corpId") String corpId);

    AsThirdParty selectDingtalkByAppKeyAndSecret(@Param("appKey") String appKey, @Param("appSecret") String appSecret);

    AsThirdParty selectFeishuByAppIdAndSecret(@Param("appId") String appId, @Param("appSecret") String appSecret);

    void deleteWechatByCorpId(@Param("corpId") String corpId);

    Long selectCompanyIdByCorpId(@Param("corpId") String corpId);
}
