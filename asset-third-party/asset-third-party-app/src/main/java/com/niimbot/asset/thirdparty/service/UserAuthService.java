package com.niimbot.asset.thirdparty.service;

import com.niimbot.asset.thirdparty.strategy.loginauth.UserAuthStrategy;
import com.niimbot.thirdparty.ThirdPartyUser;

/**
 * <AUTHOR>
 */
public interface UserAuthService {

    UserAuthStrategy getUserAuthStrategy(String provider);

    ThirdPartyUser socialAuth(String provider, String authCode, String terminal);

    String webAuth(Long companyId, String type, String authCode);

    String h5Auth(Long companyId, String type, String authCode);
}
