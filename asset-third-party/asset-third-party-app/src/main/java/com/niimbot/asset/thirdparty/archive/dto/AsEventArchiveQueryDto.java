package com.niimbot.asset.thirdparty.archive.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件归档查询DTO
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "事件归档查询参数")
public class AsEventArchiveQueryDto {

    @ApiModelProperty("事件ID")
    private String eventId;

    @ApiModelProperty("第三方系统类型")
    private String thirdPartyType;

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态列表")
    private List<String> statusList;

    @ApiModelProperty("失败原因关键词")
    private String failureReason;

    @ApiModelProperty("重试次数最小值")
    private Integer minRetryCount;

    @ApiModelProperty("重试次数最大值")
    private Integer maxRetryCount;

    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

    @ApiModelProperty("下次重试时间开始")
    private LocalDateTime nextRetryTimeStart;

    @ApiModelProperty("下次重试时间结束")
    private LocalDateTime nextRetryTimeEnd;

    @ApiModelProperty("是否包含原始消息内容")
    private Boolean includeRawMessage = false;
}