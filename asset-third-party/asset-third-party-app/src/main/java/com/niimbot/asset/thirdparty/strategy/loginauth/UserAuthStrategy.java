package com.niimbot.asset.thirdparty.strategy.loginauth;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.properties.ThirdPartyProperties;
import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.strategy.TypeSign;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.ThirdPartyUser;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Locale;

/**
 * 登录授权抽象类
 *
 * <AUTHOR>
 */
public abstract class UserAuthStrategy implements TypeSign, ApplicationContextAware {

    protected ThirdPartyProperties properties = new ThirdPartyProperties();

    protected AsThirdPartyService thirdPartyService;

    /**
     * PC端扫码认证获取用户信息
     *
     * @param authCode 临时授权码
     * @return ThirdPartyUser
     */
    abstract public ThirdPartyUser pcAuth(String authCode);

    /**
     * APP端扫码认证获取用户信息
     *
     * @param authCode 临时授权码
     * @return ThirdPartyUser
     */
    abstract public ThirdPartyUser appAuth(String authCode);

    /**
     * 获取第三方用户信息
     *
     * @param authCode 访问凭证
     * @param terminal 终端类型 app | pc
     * @return ThirdPartyUser
     */
    public ThirdPartyUser socialAuth(String authCode, String terminal) {
        switch (terminal) {
            case AssetConstant.TERMINAL_PC:
                return pcAuth(authCode);
            case AssetConstant.TERMINAL_APP:
                return appAuth(authCode);
            default:
                throw new BusinessException(ThirdPartyResultCode.NONSUPPORT_AUTH_WAY);
        }
    }

    public JSONObject getCompanyConfig(Long companyId) {
        String type = getType();
        return thirdPartyService.getSecret(companyId, type);
    }

    /**
     * 内部应用web认证
     *
     * @param companyId 企业ID
     * @param authCode  授权码
     * @return userid
     */
    public String webAuth(Long companyId, String authCode) {
        return "-1";
    }

    /**
     * 内部应用H5认证
     *
     * @param companyId 企业ID
     * @param authCode  授权码
     * @return userid
     */
    public String h5Auth(Long companyId, String authCode) {
        return "-1";
    }

    /**
     * 获取应用基本信息
     *
     * @return appInfo
     */
    protected ThirdPartyProperties.AppInfo getAppInfo() {
        String type = getType();
        if (type.contains(ThirdPartyConstant.DINGTALK)) {
            type = ThirdPartyConstant.DINGTALK;
        }
        if (type.contains(ThirdPartyConstant.WECHAT)) {
            type = ThirdPartyConstant.WECHAT;
        }
        if (type.toUpperCase(Locale.ROOT).contains("QQ")) {
            type = "QQ";
        }
        if (type.toUpperCase(Locale.ROOT).contains("WEIXIN")) {
            type = "WEIXIN";
        }
        return properties.getInfo(type);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        properties = applicationContext.getBean(ThirdPartyProperties.class);
        thirdPartyService = applicationContext.getBean(AsThirdPartyService.class);
    }
}
