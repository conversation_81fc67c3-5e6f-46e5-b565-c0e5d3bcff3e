package com.niimbot.asset.thirdparty.strategy.dingtalk;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.activiti.model.ActApproveRole;
import com.niimbot.asset.activiti.model.ActApproveRoleMember;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.system.dto.clientobject.ExternalRelation;
import com.niimbot.asset.system.ots.SystemEmployeeOts;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;
import com.niimbot.asset.thirdparty.service.SyncApproveRoleService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.LABEL_USER_CHANGE)
public class UserRoleChangeStrategy implements DingCallbackStrategy {

    @Resource
    private SyncApproveRoleService syncApproveRoleService;

    @Resource
    private CompanySettingService companySettingService;

    @Resource
    private SystemEmployeeOts systemEmployeeService;

    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Resource
    private ActApproveRoleService approveRoleService;

    @Override
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        CompanySwitch setting = companySettingService.getSwitchSettingWithCache(companyId);
        if (!setting.getEnableSyncApproveRole()) {
            return true;
        }
        String action = content.getString("action");
        List<String> userIds = content.getJSONArray("UserIdList").toJavaList(String.class);
        List<Long> roleIds = content.getJSONArray("LabelIdList").toJavaList(Long.class);
        if (CollUtil.isEmpty(userIds) || CollUtil.isEmpty(roleIds)) {
            return true;
        }
        // 用户从角色中移除
        if ("remove".equalsIgnoreCase(action)) {
            List<ExternalRelation> empExternalRelation = systemEmployeeService.getEmpExternalRelation(companyId, userIds);
            List<ActApproveRole> exitsRoles = approveRoleService.list(
                    Wrappers.lambdaQuery(ActApproveRole.class)
                            .eq(ActApproveRole::getCompanyId, companyId)
                            .in(ActApproveRole::getExternalId, roleIds.stream().map(String::valueOf).collect(Collectors.toList()))
            );
            if (CollUtil.isEmpty(empExternalRelation) || CollUtil.isEmpty(exitsRoles)) {
                return true;
            }
            approveRoleMemberService.remove(
                    Wrappers.lambdaUpdate(ActApproveRoleMember.class)
                            .in(ActApproveRoleMember::getApproveRoleId, exitsRoles.stream().map(ActApproveRole::getId).collect(Collectors.toList()))
                            .in(ActApproveRoleMember::getMemberId, empExternalRelation.stream().map(ExternalRelation::getId).collect(Collectors.toList()))

            );
        } else {
            syncApproveRoleService.syncFromDing(companyId, roleIds);
        }
        return true;
    }
}
