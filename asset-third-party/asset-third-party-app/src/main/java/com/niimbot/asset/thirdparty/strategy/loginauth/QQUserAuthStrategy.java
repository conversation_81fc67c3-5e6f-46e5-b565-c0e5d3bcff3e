package com.niimbot.asset.thirdparty.strategy.loginauth;

import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.properties.ThirdPartyProperties;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.ThirdPartyUser;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class QQUserAuthStrategy extends UserAuthStrategy {

    @Value("${asset.domain.pc}")
    private String pcDomain;

    @Resource
    private RestTemplate restTemplate;

    private final HttpHeaders httpHeaders;

    private final ParameterizedTypeReference<Map<String, String>> mapRef = new ParameterizedTypeReference<Map<String, String>>() {
    };

    public QQUserAuthStrategy() {
        this.httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.TEXT_HTML); // 添加头
    }

    @Override
    public String getType() {
        return "QQ";
    }

    private Map<String, String> executeGetRequest(String url, Object... uriVariables) {
        ResponseEntity<Map<String, String>> exchange;
        try {
            exchange = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(httpHeaders), mapRef, uriVariables);
        } catch (RestClientException e) {
            log.error("QQ Request Error", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        if (Objects.isNull(exchange.getBody())) {
            log.error("QQ Response Is Null");
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        Map<String, String> body = exchange.getBody();
        if (body.containsKey(Auth.ERROR)) {
            log.error("QQ User Auth Error {}", body);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        return body;
    }

    /**
     * QQ PC端扫码与APP端接入登录逻辑一致
     *
     * @param accessToken 访问标识符
     * @return thirdPartyUser
     * @see <a href="https://wiki.connect.qq.com/%e5%87%86%e5%a4%87%e5%b7%a5%e4%bd%9c_oauth2-0">...</a>
     */
    private ThirdPartyUser commonUserAuth(String accessToken, String appId) {
        // ThirdPartyProperties.AppInfo appInfo = getAppInfo();
        // 1.获取accessToken
        // Map<String, String> authResult = executeGetRequest(Auth.GET_ACCESS_TOKEN, appId, appSecret, authCode, pcDomain + "/#/login");
        // String refreshToken = authResult.get(Auth.REFRESH_TOKEN);
        // 2.自动续期token
        // Map<String, String> refreshTokenResult = executeGetRequest(Auth.RENEWAL_TOKEN, appId, appSecret, refreshToken);
        // String accessToken = refreshTokenResult.get(Auth.ACCESS_TOKEN);
        // 3.获取用户openId
        Map<String, String> openIdResult = executeGetRequest(Auth.GET_USER_OPEN_ID, accessToken);
        String userOpenId = openIdResult.get(Auth.OPENID);
        // 4.获取用户基本信息
        Map<String, String> userInfoResult = executeGetRequest(Auth.GET_USER_INFO, accessToken, appId, userOpenId);
        // 5.获取用户unionId
        Map<String, String> unionIdResult = executeGetRequest(Auth.GET_USER_UNION_ID, accessToken);
        String unionId = unionIdResult.get(Auth.UNIONID);
        // 6.组装数据返回
        return new ThirdPartyUser().setType(getType())
                .setNickname(userInfoResult.get(Auth.NICKNAME))
                .setAvatarUrl(userInfoResult.get(Auth.FIGUREURL))
                .setOpenId(userOpenId)
                .setUniqueId(unionId);
    }

    @Override
    public ThirdPartyUser pcAuth(String authCode) {
        ThirdPartyProperties.AppInfo appInfo = getAppInfo();
        // 1.获取accessToken
        Map<String, String> authResult = executeGetRequest(Auth.GET_ACCESS_TOKEN, appInfo.getAppKey(), appInfo.getAppSecret(), authCode, pcDomain + "/#/login");
        String refreshToken = authResult.get(Auth.REFRESH_TOKEN);
        // 2.自动续期token
        Map<String, String> refreshTokenResult = executeGetRequest(Auth.RENEWAL_TOKEN, appInfo.getAppKey(), appInfo.getAppSecret(), refreshToken);
        String accessToken = refreshTokenResult.get(Auth.ACCESS_TOKEN);
        return commonUserAuth(accessToken, appInfo.getAppKey());
    }

    @Override
    public ThirdPartyUser appAuth(String accessToken) {
        return commonUserAuth(accessToken, "101965661");
    }

    protected interface Auth {

        String BASE_URL = "https://graph.qq.com";

        String GET_ACCESS_TOKEN = BASE_URL + "/oauth2.0/token?grant_type=authorization_code&client_id={clientId}&client_secret={clientSecret}&code={authCode}&redirect_uri={redirectUri}&fmt=json";

        String RENEWAL_TOKEN = BASE_URL + "/oauth2.0/token?grant_type=refresh_token&client_id={clientId}&client_secret={clientSecret}&refresh_token={refreshToken}&fmt=json";

        String GET_USER_OPEN_ID = BASE_URL + "/oauth2.0/me?access_token={accessToken}&fmt=json";

        String GET_USER_UNION_ID = BASE_URL + "/oauth2.0/me?access_token={accessToken}&unionid=1&fmt=json";

        String GET_USER_INFO = BASE_URL + "/user/get_user_info?access_token={accessToken}&oauth_consumer_key={clientId}&openid={openId}";

        String ACCESS_TOKEN = "access_token";

        String REFRESH_TOKEN = "refresh_token";

        String OPENID = "openid";

        String UNIONID = "unionid";

        String NICKNAME = "nickname";

        String FIGUREURL = "figureurl";

        String ERROR = "error";
    }

}
