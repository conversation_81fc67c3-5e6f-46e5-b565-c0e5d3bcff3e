package com.niimbot.asset.thirdparty.config;

import com.niimbot.asset.framework.properties.ThirdPartyProperties;
import me.chanjar.weixin.common.redis.RedissonWxRedisOps;
import me.chanjar.weixin.common.util.http.apache.DefaultApacheHttpClientBuilder;
import me.chanjar.weixin.cp.config.WxCpTpConfigStorage;
import me.chanjar.weixin.cp.config.impl.WxCpTpRedissonConfigImpl;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpServiceImpl;
import me.chanjar.weixin.cp.util.crypto.WxCpTpCryptUtil;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class WechatConfiguration {

    @Resource
    private ThirdPartyProperties thirdPartyProperties;

    @Resource
    private RedissonClient redissonClient;

    @Bean
    public WxCpTpConfigStorage assetWxCpTpDefaultConfig() {
        ThirdPartyProperties.WeChatSync weChatSync = thirdPartyProperties.getWeChatSync();
        DefaultApacheHttpClientBuilder httpClientBuilder = DefaultApacheHttpClientBuilder.get();
        httpClientBuilder.setMaxTotalConn(100);
        httpClientBuilder.setConnectionRequestTimeout(5000);
        return WxCpTpRedissonConfigImpl.builder()
                .suiteId(weChatSync.getSuiteId())
                .suiteSecret(weChatSync.getSuiteSecret())
                .corpId(weChatSync.getCorpId())
                .keyPrefix("saas-wx-cp-tp")
                .providerSecret(weChatSync.getProviderSecret())
                .token(weChatSync.getToken())
                .aesKey(weChatSync.getAesKey())
                .apacheHttpClientBuilder(httpClientBuilder)
                .wxRedisOps(new RedissonWxRedisOps(redissonClient))
                .build();
    }

    /*@Bean
    public WxCpTpDefaultConfigImpl assetWxCpTpDefaultConfig() throws NoSuchFieldException, IllegalAccessException {
        ThirdPartyProperties.WeChatSync weChatSync = thirdPartyProperties.getWeChatSync();
        WxCpTpDefaultConfigImpl configStorage = new WxCpTpDefaultConfigImpl();
        configStorage.setCorpId(weChatSync.getCorpId());
        configStorage.setToken(weChatSync.getToken());
        configStorage.setSuiteId(weChatSync.getSuiteId());
        configStorage.setSuiteSecret(weChatSync.getSuiteSecret());
        configStorage.setAesKey(weChatSync.getAesKey());
        Field providerSecret = configStorage.getClass().getDeclaredField("providerSecret");
        providerSecret.setAccessible(true);
        providerSecret.set(configStorage, weChatSync.getProviderSecret());
        return configStorage;
    }*/

    @Bean
    public WxCpTpService assetWxCpTpService(WxCpTpConfigStorage assetWxCpTpDefaultConfig) {
        WxCpTpService assetWxCpTpService = new WxCpTpServiceImpl();
        assetWxCpTpService.setWxCpTpConfigStorage(assetWxCpTpDefaultConfig);
        return assetWxCpTpService;
    }

    @Bean
    public WxCpTpCryptUtil assetWxCpTpCrypt(WxCpTpConfigStorage assetWxCpTpDefaultConfig) {
        return new WxCpTpCryptUtil(assetWxCpTpDefaultConfig);
    }

}
