package com.niimbot.asset.thirdparty.domain.abs.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.system.abs.ThirdPartyAbs;
import com.niimbot.asset.system.dto.ThirdPartyGetQry;
import com.niimbot.asset.system.dto.ThirdPartyInnerMessage;
import com.niimbot.asset.system.dto.clientobject.ThirdPartyCO;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.thirdparty.mapstruct.ThirdPartyMapStruct;
import com.niimbot.asset.thirdparty.model.AsThirdParty;
import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.asset.thirdparty.support.DingApiSupport;
import com.niimbot.asset.thirdparty.service.FeishuOpenApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/client/abs/thirdparty/thirdPartyAbs")
@RequiredArgsConstructor
public class ThirdPartyAbsImpl implements ThirdPartyAbs {

    @Value("${asset.domain.forward}")
    private String forward;

    private final ThirdPartyMapStruct mapStruct;

    @Resource
    private AsThirdPartyService thirdPartyService;

    @Resource
    private DingApiSupport dingApiSupport;

    private final FeishuOpenApiService feishuOpenApiService;

    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    @Override

    public ThirdPartyCO getThirdParty(ThirdPartyGetQry qry) {
        List<AsThirdParty> parties = thirdPartyService.list(Wrappers.lambdaQuery(AsThirdParty.class).eq(AsThirdParty::getCompanyId, qry.getId()));
        if (CollUtil.isEmpty(parties)) {
            return null;
        }
        return mapStruct.convertThirdPartyModelToCo(parties.get(0));
    }

    @Override
    public String sendThirdPartyInnerMessage(ThirdPartyInnerMessage body) {
        if (CollUtil.isEmpty(body.getEmpIds())) {
            log.warn("sendThirdPartyInnerMessage empIds is empty");
            return "";
        }
        // 应用表单
        JSONObject form = thirdPartyService.getSecret(body.getCompanyId(), body.getType());
        if (CollUtil.isEmpty(form)) {
            log.warn("sendThirdPartyInnerMessage form is empty");
            return "";
        }
        // user ids
        List<String> userIds = thirdPartyEmployeeService.list(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .select(AsThirdPartyEmployee::getUserId)
                        .eq(AsThirdPartyEmployee::getType, body.getType())
                        .eq(AsThirdPartyEmployee::getCompanyId, body.getCompanyId())
                        .in(AsThirdPartyEmployee::getEmployeeId, body.getEmpIds())
        ).stream().map(AsThirdPartyEmployee::getUserId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(userIds)) {
            log.warn("sendThirdPartyInnerMessage userIds is empty");
            return "";
        }
        StringBuilder url = new StringBuilder();
        url.append(forward)
                .append("?companyId=").append(body.getCompanyId())
                .append("&type=msg")
                .append("&code=").append(body.getCode());
        String msgId = "";

        if (CollUtil.isNotEmpty(body.getParams())) {
            body.getParams().forEach((k, v) -> {
                // 忽略url
                if ("url".equals(k) || MessageConstant.Template.TASK_NAME.equals(k)) {
                    return;
                }
                url.append("&").append(k).append("=").append(URLUtil.encode(v));
            });
        }

        // 钉钉 | 企业微信 | 飞书
        if ("DINGTALK".equals(body.getType())) {
            // 链接消息
            url.append("&platform=DINGTALK");
            appendSpecialParams(body, url);
            msgId = dingApiSupport.sendInnerLinkMessage(body.getTitle(), body.getText(), url.toString(), userIds, form, body.getCompanyId());
        } else if ("WECHAT".equals(body.getType())) {
            // 文本卡片消息
            url.append("&platform=WECHAT");
            appendSpecialParams(body, url);
            WxCpMessage message = WxCpMessage.TEXTCARD()
                    .btnTxt("详情")
                    .toUser(String.join("|", userIds))
                    .agentId(form.getJSONObject("userAuthAgent").getInteger("agentId"))
                    .title(body.getTitle())
                    .description(body.getText())
                    .url(url.toString())
                    .build();
            WxCpMessageService messageService = thirdPartyService.getWeChatService(body.getCompanyId()).getMessageService();
            try {
                WxCpMessageSendResult result = messageService.send(message);
                msgId = result.getMsgId();
            } catch (WxErrorException e) {
                log.error("WxCpMessageService send error", e);
            }
        } else if ("FEISHU".equals(body.getType())) {
            // 链接消息
            url.append("&platform=FEISHU");
            appendSpecialParams(body, url);
            feishuOpenApiService.sendInnerLinkMessage(body.getTitle(), body.getText(), url.toString(), userIds, form, body.getCompanyId());
        }
        return msgId;
    }

    private void appendSpecialParams(ThirdPartyInnerMessage body, StringBuilder url) {
        if (CollUtil.isNotEmpty(body.getParams())) {
            // task_name放在最后
            if (body.getParams().containsKey(MessageConstant.Template.TASK_NAME)) {
                url.append("&").append(MessageConstant.Template.TASK_NAME).append("=")
                        .append(URLUtil.encode(body.getParams().get(MessageConstant.Template.TASK_NAME)));
            }
        }
    }
}
