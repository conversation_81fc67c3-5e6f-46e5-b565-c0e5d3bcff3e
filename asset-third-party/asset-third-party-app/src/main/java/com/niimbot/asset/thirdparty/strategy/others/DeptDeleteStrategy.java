package com.niimbot.asset.thirdparty.strategy.others;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsSyncChange;
import com.niimbot.asset.system.service.CompanyAssetService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.SyncChangeService;
import com.niimbot.asset.thirdparty.event.OrgCO;
import com.niimbot.asset.thirdparty.event.OthersCallbackEvent;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Since 2024-10-14
 */
@Slf4j
@Component(value = "othersDeptDeleteStrategy")
@CallbackStrategy(eventType = OthersCallbackEvent.DEPT_DELETE)
public class DeptDeleteStrategy implements OthersCallbackStrategy {

    private final OrgService orgService;
    private final CompanyAssetService companyAssetService;
    private final SyncChangeService syncChangeService;

    public DeptDeleteStrategy(OrgService orgService,
                              CompanyAssetService companyAssetService,
                              SyncChangeService syncChangeService) {
        this.orgService = orgService;
        this.companyAssetService = companyAssetService;
        this.syncChangeService = syncChangeService;
    }

    @Override
    public boolean handleCallback(Long companyId, JSONObject content) {
        OrgCO orgCO = content.toJavaObject(OrgCO.class);
        if (orgCO != null) {
            AsOrg one = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .and(wrap ->
                            wrap.eq(AsOrg::getExternalOrgId, orgCO.getId())
                                    .or()
                                    .eq(AsOrg::getId, orgCO.getId())));
            if (ObjectUtil.isNotNull(one)) {
                boolean remove = orgService.removeById(one.getId());
                if (remove) {
                    // 使用组织
                    List<Long> useOrgIds = companyAssetService.checkUseOrg(ListUtil.of(one.getId()), companyId);
                    // 管理组织
                    List<Long> orgOwnerIds = companyAssetService.checkOrgOwner(ListUtil.of(one.getId()), companyId);
                    useOrgIds.addAll(orgOwnerIds);
                    if (!useOrgIds.isEmpty()) {
                        // 写入异动记录
                        AsSyncChange syncChange = new AsSyncChange();
                        AsSyncChange dingSyncChange = syncChange.setResId(one.getId()).setCompanyId(companyId).setFromOrg(new ArrayList<>()).setToOrg(new ArrayList<>()).setType(3);
                        syncChangeService.save(dingSyncChange);
                    }
                }
            }
            return true;
        } else {
            log.error("companyId = {}, orgCO is null", companyId);
            return false;
        }
    }
}
