package com.niimbot.asset.thirdparty.strategy.loginauth;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.properties.ThirdPartyProperties;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.ThirdPartyUser;
import com.niimbot.thirdparty.WeChatAgent;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.WxCpUserService;
import me.chanjar.weixin.cp.api.impl.WxCpOAuth2ServiceImpl;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.api.impl.WxCpUserServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.WxTpLoginInfo;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.cp.config.impl.WxCpTpDefaultConfigImpl;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpServiceImpl;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeChatUserAuthStrategy extends UserAuthStrategy {

    private WxCpTpService getWxCpTpService() {
        ThirdPartyProperties.AppInfo appInfo = getAppInfo();
        WxCpTpDefaultConfigImpl wxCpTpDefaultConfig = new WxCpTpDefaultConfigImpl();
        wxCpTpDefaultConfig.setCorpId(appInfo.getAppKey());
        try {
            Field providerSecret = wxCpTpDefaultConfig.getClass().getDeclaredField("providerSecret");
            providerSecret.setAccessible(true);
            providerSecret.set(wxCpTpDefaultConfig, appInfo.getProviderSecret());
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("企业微信第三方应用属性设置错误", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        WxCpTpServiceImpl wxCpTpService = new WxCpTpServiceImpl();
        wxCpTpService.setWxCpTpConfigStorage(wxCpTpDefaultConfig);
        return wxCpTpService;
    }

    private WxCpService getWxCpService() {
        ThirdPartyProperties.AppInfo appInfo = getAppInfo();
        WxCpDefaultConfigImpl wxCpDefaultConfig = new WxCpDefaultConfigImpl();
        wxCpDefaultConfig.setCorpId(appInfo.getAppKey());
        wxCpDefaultConfig.setCorpSecret(appInfo.getAppSecret());
        wxCpDefaultConfig.setAgentId(1);
        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(wxCpDefaultConfig);
        return wxCpService;
    }

    @Override
    public String getType() {
        return ThirdPartyConstant.WECHAT;
    }

    @Override
    public String webAuth(Long companyId, String authCode) {
        JSONObject config = getCompanyConfig(companyId);
        String authAgent = config.getString("userAuthAgent");
        if (StrUtil.isBlank(authAgent)) {
            throw new BusinessException(ThirdPartyResultCode.WECHAT_AGENT_CONFIG_ERROR);
        }
        WeChatAgent weChatAgent = JSONObject.parseObject(authAgent, WeChatAgent.class);

        WxCpDefaultConfigImpl wxCpDefaultConfig = new WxCpDefaultConfigImpl();
        wxCpDefaultConfig.setCorpId(weChatAgent.getCorpId());
        wxCpDefaultConfig.setCorpSecret(weChatAgent.getAgentSecret());
        WxCpService service = new WxCpServiceImpl();
        service.setWxCpConfigStorage(wxCpDefaultConfig);

        WxCpOauth2UserInfo userInfo;
        try {
            userInfo = service.getOauth2Service().getUserInfo(authCode);
        } catch (WxErrorException e) {
            log.error("Get CP WX UserInfo Error", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getUserId()) || userInfo.getUserId().trim().isEmpty()) {
            throw new BusinessException(ThirdPartyResultCode.NOT_TO_JOIN_COMPANY);
        }
        WxCpUser wxCpUser;
        try {
            wxCpUser = service.getUserService().getById(userInfo.getUserId());
        } catch (WxErrorException e) {
            log.error("Get CP WX User By UserId Error", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        return wxCpUser.getUserId();
    }

    @Override
    public String h5Auth(Long companyId, String authCode) {
        throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不支持此种方式");
    }

    @Override
    public ThirdPartyUser pcAuth(String authCode) {
        WxCpTpService service = getWxCpTpService();
        WxTpLoginInfo loginInfo;
        try {
            loginInfo = service.getLoginInfo(authCode);
        } catch (WxErrorException e) {
            log.error("Get CP TP WX LoginInfo Error", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        if (Objects.isNull(loginInfo) || Objects.isNull(loginInfo.getUserInfo()) || Objects.isNull(loginInfo.getUserInfo().getOpenUserId()) || loginInfo.getUserInfo().getOpenUserId().trim().isEmpty()) {
            throw new BusinessException(ThirdPartyResultCode.NOT_TO_JOIN_COMPANY);
        }
        // 用户基本信息
        WxTpLoginInfo.UserInfo userInfo = loginInfo.getUserInfo();
        return new ThirdPartyUser()
                .setType(getType())
                .setUserId(userInfo.getUserId())
                .setUniqueId(userInfo.getOpenUserId())
                .setNickname(userInfo.getName())
                .setAvatarUrl(userInfo.getAvatar());
    }

    // // TODO: 2022/2/11 拿不到openUserId
    @Override
    public ThirdPartyUser appAuth(String authCode) {
        WxCpService wxCpService = getWxCpService();
        WxCpOAuth2Service wxCpOAuth2Service = new WxCpOAuth2ServiceImpl(wxCpService);
        WxCpOauth2UserInfo userInfo = null;
        try {
            userInfo = wxCpOAuth2Service.getUserInfo(authCode);
        } catch (WxErrorException e) {
            log.error("App User WeChat Auth Error", e);
        }
        if (Objects.isNull(userInfo)) {
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        String userId = userInfo.getUserId();
        // 根据用户ID获取用户信息
        WxCpUserService wxCpUserService = new WxCpUserServiceImpl(wxCpService);
        WxCpUser wxCpUser = null;
        try {
            wxCpUser = wxCpUserService.getById(userId);
        } catch (WxErrorException e) {
            log.error("App User Get WeChat Info By UserId Error", e);
        }
        if (Objects.isNull(wxCpUser)) {
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        // TODO: 2022/2/11 第三方应用
        // WxCpTpService wxCpTpService = getWxCpTpService();
        // WxCpTpUserService wxCpTpUserService = new WxCpTpUserServiceImpl(wxCpTpService);
        // try {
        //     wxCpTpUserService.getById(userId, getAppInfo().getAppKey());
        // } catch (WxErrorException e) {
        //     e.printStackTrace();
        // }

        return new ThirdPartyUser()
                .setType(getType())
                .setNickname(wxCpUser.getName())
                .setUserId(wxCpUser.getUserId())
                .setOpenId(wxCpUser.getOpenUserId())
                .setUniqueId(wxCpUser.getOpenUserId())
                .setAvatarUrl(wxCpUser.getAvatar())
                .setMobile(wxCpUser.getMobile())
                .setEmail(wxCpUser.getEmail());
    }

}
