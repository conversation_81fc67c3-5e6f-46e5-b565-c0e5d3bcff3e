package com.niimbot.asset.thirdparty.strategy.loginauth;

import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.properties.ThirdPartyProperties;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.ThirdPartyUser;

import org.springframework.stereotype.Component;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.service.WxOAuth2Service;
import me.chanjar.weixin.open.api.WxOpenConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenInMemoryConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenOAuth2ServiceImpl;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeiXinUserAuthStrategy extends UserAuthStrategy {
    @Override
    public String getType() {
        return "WEIXIN";
    }

    private WxOAuth2Service getWxOAuth2Service(String terminal) {
        ThirdPartyProperties.AppInfo appInfo = getAppInfo();
        String appId = terminal.equalsIgnoreCase(AssetConstant.TERMINAL_APP) ? "wxd341aeea15b6af81" : appInfo.getAppKey();
        String appSecret= terminal.equalsIgnoreCase(AssetConstant.TERMINAL_APP) ? "29f43078f70dbcb6b38d17c44aba41ae" : appInfo.getAppSecret();
        WxOpenConfigStorage wxOpenConfigStorage = new WxOpenInMemoryConfigStorage();
        wxOpenConfigStorage.setComponentAppId(appId);
        wxOpenConfigStorage.setComponentAppSecret(appSecret);
        WxOpenOAuth2ServiceImpl wxOpenOAuth2Service = new WxOpenOAuth2ServiceImpl(appId, appSecret);
        wxOpenOAuth2Service.setWxOpenConfigStorage(wxOpenConfigStorage);
        return wxOpenOAuth2Service;
    }

    /**
     * 微信PC端扫码与APP端接入登录逻辑一致，但使用的APPID不相同。
     *
     * @param authCode 授权码
     * @return thirdPartyUser
     * @see <a href="https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html">...</a>
     */
    private ThirdPartyUser commonUserAuth(String authCode, String terminal) {
        WxOAuth2Service wxOAuth2Service = getWxOAuth2Service(terminal);
        WxOAuth2AccessToken authResult;
        try {
            authResult = wxOAuth2Service.getAccessToken(authCode);
        } catch (WxErrorException e) {
            log.error("WeiXin User Auth Error", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        if (Objects.isNull(authResult)) {
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        // 用户授权的作用域，使用逗号（,）分隔 snsapi_userinfo
        String scope = authResult.getScope();
        // 授权用户唯一标识
        String openId = authResult.getOpenId();
        // 当且仅当该网站应用已获得该用户的userinfo授权时，才会出现该字段。
        String unionId = authResult.getUnionId();
        /*
         * 获取用户个人信息（UnionID机制）
         *
         * 此接口用于获取用户个人信息。开发者可通过OpenID来获取用户基本信息。
         * 特别需要注意的是，如果开发者拥有多个移动应用、网站应用和公众帐号，可通过获取用户基本信息中的unionid来区分用户的唯一性，
         * 因为只要是同一个微信开放平台帐号下的移动应用、网站应用和公众帐号，用户的unionid是唯一的。
         * 换句话说，同一用户，对同一个微信开放平台下的不同应用，unionid是相同的。
         * 请注意，在用户修改微信头像后，旧的微信头像URL将会失效，因此开发者应该自己在获取用户信息后，将头像图片保存下来，避免微信头像URL失效后的异常情况。
         */
        WxOAuth2UserInfo userInfo;
        try {
            userInfo = wxOAuth2Service.getUserInfo(authResult, null);
        } catch (WxErrorException e) {
            log.error("Get WeiXin UserInfo Error", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        if (Objects.isNull(userInfo)) {
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        return new ThirdPartyUser().setType(getType())
                .setNickname(userInfo.getNickname())
                .setOpenId(userInfo.getOpenid())
                .setUniqueId(userInfo.getUnionId())
                .setAvatarUrl(userInfo.getHeadImgUrl());
    }

    @Override
    public ThirdPartyUser pcAuth(String authCode) {
        return commonUserAuth(authCode, AssetConstant.TERMINAL_PC);
    }

    @Override
    public ThirdPartyUser appAuth(String authCode) {
        return commonUserAuth(authCode, AssetConstant.TERMINAL_APP);
    }
}
