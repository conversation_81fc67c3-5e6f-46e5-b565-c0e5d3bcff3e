package com.niimbot.asset.thirdparty.config;

import com.niimbot.asset.thirdparty.service.WeChatCallbackService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/7/3 14:57
 */
@Slf4j
@Component
public class WeChatBlockQueue extends Thread {

    public static final String BLOCK_QUEUE_WECHAT = "blockQueue:thirdParty:wechat";
    private final RBlockingQueue<WxCpTpXmlMessage> wechatBlockingQueue;
    private final WeChatCallbackService wechatCallbackService;

    @Autowired
    public WeChatBlockQueue(RedissonClient redissonClient,
                            WeChatCallbackService wechatCallbackService) {
        this.wechatBlockingQueue = redissonClient.getBlockingQueue(BLOCK_QUEUE_WECHAT, new JsonJacksonCodec());
        this.wechatCallbackService = wechatCallbackService;
        this.start();
    }

    @Override
    public void run() {
        while (true) {
            WxCpTpXmlMessage message = null;
            try {
                log.info("take wechat queue msg, {} waiting", wechatBlockingQueue.size());
                message = wechatBlockingQueue.take();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
//                Thread.currentThread().interrupt();
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex) {
                    // ignore
                }
            }
            if (message != null) {
                try {
                    wechatCallbackService.handle3rdPartyMessage(message);
                } catch (Exception e) {
                    log.error("handle wechat queue msg error, {}", e.getMessage(), e);
                }
            }
        }
    }

    public void put(WxCpTpXmlMessage message) {
        try {
            int size = wechatBlockingQueue.size();
            log.info("send wechat queue msg, {} waiting", size);
            wechatBlockingQueue.put(message);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
//            Thread.currentThread().interrupt();
            try {
                Thread.sleep(5000);
            } catch (InterruptedException ex) {
                // ignore
            }
        }
    }

}
