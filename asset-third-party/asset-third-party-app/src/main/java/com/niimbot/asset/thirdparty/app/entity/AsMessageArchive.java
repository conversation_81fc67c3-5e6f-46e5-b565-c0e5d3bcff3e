package com.niimbot.asset.thirdparty.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 消息归档实体类 - 通用归档实体，支持多种第三方集成类型
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@TableName("as_message_archive")
public class AsMessageArchive {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 事件ID
     */
    @TableField("event_id")
    private String eventId;
    
    /**
     * 第三方系统类型 (yanhuang, dingtalk, wechat等)
     */
    @TableField("third_party_type")
    private String thirdPartyType;
    
    /**
     * 事件类型 (TODO_CREATE, TODO_COMPLETE, TODO_DELETE, SSO_AUTH, ORG_SYNC, ASSET_SYNC等)
     */
    @TableField("event_type")
    private String eventType;
    
    /**
     * 原始消息内容
     */
    @TableField("raw_message")
    private String rawMessage;
    
    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;
    
    /**
     * 当前重试次数
     */
    @TableField("retry_count")
    private Integer retryCount = 0;
    
    /**
     * 最大重试次数
     */
    @TableField("max_retry_count")
    private Integer maxRetryCount = 3;
    
    /**
     * 状态 (FAILED-失败, RETRYING-重试中, SUCCESS-成功, FINAL_FAILED-最终失败)
     */
    @TableField("status")
    private String status;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 最后重试时间
     */
    @TableField("last_retry_time")
    private LocalDateTime lastRetryTime;
    
    /**
     * 下次重试时间
     */
    @TableField("next_retry_time")
    private LocalDateTime nextRetryTime;
    
    /**
     * 扩展字段1
     */
    @TableField("ext1")
    private String ext1;
    
    /**
     * 扩展字段2
     */
    @TableField("ext2")
    private String ext2;
    
    /**
     * 扩展字段3
     */
    @TableField("ext3")
    private String ext3;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    public AsMessageArchive() {
        this.createTime = LocalDateTime.now();
        this.status = "FAILED";
    }
    
    public AsMessageArchive(String eventId, String thirdPartyType, String eventType, String rawMessage) {
        this();
        this.eventId = eventId;
        this.thirdPartyType = thirdPartyType;
        this.eventType = eventType;
        this.rawMessage = rawMessage;
    }
    
    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetryCount && !"SUCCESS".equals(status) && !"FINAL_FAILED".equals(status);
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetry() {
        this.retryCount++;
        this.lastRetryTime = LocalDateTime.now();
        this.nextRetryTime = LocalDateTime.now().plusSeconds(3);
        this.status = "RETRYING";
    }
    
    /**
     * 标记为成功
     */
    public void markSuccess() {
        this.status = "SUCCESS";
    }
    
    /**
     * 标记为最终失败
     */
    public void markFinalFailure() {
        this.status = "FINAL_FAILED";
    }
}