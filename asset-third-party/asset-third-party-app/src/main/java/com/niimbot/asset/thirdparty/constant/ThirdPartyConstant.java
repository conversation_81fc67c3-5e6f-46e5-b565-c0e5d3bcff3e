package com.niimbot.asset.thirdparty.constant;

import cn.hutool.core.collection.ListUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/8 16:13
 */
public class ThirdPartyConstant {
    /* 类型 */
    public static final String DINGTALK = "DINGTALK";
    public static final String WECHAT = "WECHAT";
    public static final String FEISHU = "FEISHU";
    public static final String OTHERS = "OTHERS";

    public static final String GET_ORG_MAPPING = "getOrgMapping";
    public static final String HANDLE_ORG_MAPPING = "handleOrgMapping";
    public static final String GET_EMP_MAPPING = "getEmpMapping";
    public static final String HANDLE = "handle";

    public static final List<String> STEP = ListUtil.of(GET_ORG_MAPPING, HANDLE_ORG_MAPPING, GET_EMP_MAPPING);

    public static String getType(String type) {
        switch (type) {
            case DINGTALK:
                return "钉钉";
            case WECHAT:
                return "企业微信";
            case FEISHU:
                return "飞书";
            default:
                return null;
        }
    }

    /* redis缓存Key */
    private static final String REDIS_AKSK = "aksk";
    private static final String REDIS_ACCESS_TOKEN = "access_token";
    private static final String REDIS_KEY = "third_party";

    public static String getDingtalkKey(Long companyId) {
        return REDIS_KEY + ":" + DINGTALK + ":" + companyId + ":" + REDIS_AKSK;
    }

    public static String getThirdPartyManualSyncKey(Long companyId, String type) {
        return REDIS_KEY + ":" + type + ":" + companyId + ":manual_sync";
    }

    public static String getDingtalkToken(Long companyId) {
        return REDIS_KEY + ":" + DINGTALK + ":" + companyId + ":" + REDIS_ACCESS_TOKEN;
    }

    public static String getWeChatKey(Long companyId) {
        return REDIS_KEY + ":" + WECHAT + ":" + companyId + ":" + REDIS_AKSK;
    }

    public static String getWeChatToken(Long companyId) {
        return REDIS_KEY + ":" + WECHAT + ":" + companyId + ":" + REDIS_ACCESS_TOKEN;
    }

    public static String getFeishuKey(Long companyId) {
        return REDIS_KEY + ":" + FEISHU + ":" + companyId + ":" + REDIS_AKSK;
    }

    public static String getFeishuToken(Long companyId) {
        return REDIS_KEY + ":" + FEISHU + ":" + companyId + ":" + REDIS_ACCESS_TOKEN;
    }

    public static String getSyncStepCacheKey(String type, Long companyId) {
        return REDIS_KEY + ":" + type + ":" + companyId + ":init_sync:";
    }

    public static String getOrgMappingCacheKey(String type, Long companyId) {
        return REDIS_KEY + ":" + type + ":" + companyId + ":" + GET_ORG_MAPPING + ":";
    }

    public static String getEmpMappingCacheKey(String type, Long companyId) {
        return REDIS_KEY + ":" + type + ":" + companyId + ":" + GET_EMP_MAPPING + ":";
    }

}
