package com.niimbot.asset.thirdparty.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.strategy.TypeSign;
import com.niimbot.thirdparty.AssetThirdPartyEmpMapping;
import com.niimbot.thirdparty.AssetThirdPartyOrgMapping;
import com.niimbot.thirdparty.TransMismatchEmp;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/10 10:33
 */
public interface OrgStructureService extends TypeSign {

    /**
     * 写入同步步骤
     *
     * @param companyId 企业ID
     * @param step      步骤
     */
    void writeSyncStep(Long companyId, String step);

    /**
     * 读取同步步骤
     *
     * @param companyId 企业ID
     * @return 步骤
     */
    String readSyncStep(Long companyId);

    /**
     * 清除同步过程中生成的全部缓存
     *
     * @param companyId 企业ID
     */
    void cleanCache(Long companyId);

    /**
     * 云资产组织架构
     *
     * @param companyId 租户ID
     * @return List<AsOrg>
     */
    List<AsOrg> loadAssetOrg(Long companyId);

    /**
     * 第三方组织架构
     *
     * @param companyId 企业ID
     * @return List<AsOrg>
     */
    List<AsOrg> loadThirdPartyOrg(Long companyId);

    /**
     * 获取企业根节点
     *
     * @param companyId 企业ID
     * @return Org
     */
    AsOrg getAssetRootOrg(Long companyId);

    /**
     * 处理云资产和第三方组织映射关系
     *
     * @param companyId 企业ID
     * @return AssetThirdPartyOrgMapping
     */
    AssetThirdPartyOrgMapping getOrgMapping(Long companyId);

    /**
     * 保存处理后的组织映射关系
     *
     * @param mappings 映射关系集合
     * @return true or false
     */
    Boolean handleOrgMapping(List<AssetThirdPartyOrgMapping.Mapping> mappings);

    /**
     * 加载已同步过的员工信息
     *
     * @param companyId 企业ID
     * @return List<AsCusEmployee> remark -> AsThirdPartyEmployeeUserId ; updateBy -> AsThirdPartyEmployeeId
     */
    List<AsCusEmployee> loadAssetEmp(Long companyId);

    /**
     * 第三方员工
     *
     * @param companyId        企业ID
     * @param thirdPartyOrgIds 组织结构
     * @return 员工
     */
    List<ThirdPartyEmp> loadThirdPartyEmp(Long companyId, List<String> thirdPartyOrgIds);

    /**
     * 同步时未匹配的员工集合
     *
     * @param companyId 企业ID
     * @return List<UnmatchedEmp>
     */
    AssetThirdPartyEmpMapping getEmpMapping(Long companyId);

    /**
     * 开始处理同步的数据
     *
     * @param transMismatchEmps 要转移的员工数据
     * @return true or false
     */
    Boolean handle(List<TransMismatchEmp> transMismatchEmps);

    /**
     * 整体处理
     *
     * @param companyId 企业ID
     */
    void overallHandle(Long companyId);

    /**
     * 加载云资产组织字典缓存
     *
     * @param companyId 企业ID
     */
    void loadOrgCache(Long companyId);

    /**
     * 加载云资产员工字段缓存
     *
     * @param companyId 企业ID
     */
    void loadEmpCache(Long companyId);

    JSONObject getSecret(Long companyId, String type);

}
