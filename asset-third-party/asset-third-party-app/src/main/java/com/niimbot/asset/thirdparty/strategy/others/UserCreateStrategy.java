package com.niimbot.asset.thirdparty.strategy.others;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.event.OthersCallbackEvent;
import com.niimbot.asset.thirdparty.event.UserCO;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Since 2024-10-14
 */
@Slf4j
@Component(value = "othersUserCreateStrategy")
@CallbackStrategy(eventType = OthersCallbackEvent.USER_CREATE)
public class UserCreateStrategy extends AbsUserStrategy implements OthersCallbackStrategy {

    @Override
    public boolean handleCallback(Long companyId, JSONObject content) {
        UserCO userCO = content.toJavaObject(UserCO.class);
        if (userCO != null) {
            ThirdPartyEmp emp = new ThirdPartyEmp();
            emp.setThirdPartyId(userCO.getId());
            emp.setName(userCO.getEmpName());
            emp.setJobNum(userCO.getEmpNo());
            emp.setPosition(userCO.getPosition());
            emp.setAvatar(userCO.getAvatar());
            emp.setMobile(userCO.getMobile());
            emp.setEmail(userCO.getEmail());
            emp.setDeptIdList(userCO.getDeptIdList());
            emp.setCompanyId(companyId);
            createEmp(emp.setThirdPartyType(getType()));
            return true;
        } else {
            log.error("companyId = {}, userCO is null", companyId);
            return false;
        }
    }
}
