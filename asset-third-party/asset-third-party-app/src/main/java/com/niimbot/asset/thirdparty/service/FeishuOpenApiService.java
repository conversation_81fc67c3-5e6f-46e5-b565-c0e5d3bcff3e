package com.niimbot.asset.thirdparty.service;

import com.alibaba.fastjson.JSONObject;
import com.lark.oapi.Client;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.User;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.model.AuthScopes;
import com.niimbot.thirdparty.FeishuJsAuth;
import com.niimbot.thirdparty.FeishuJsTicketDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/9 16:37
 */
public interface FeishuOpenApiService {

    String getAccessToken(ThirdPartyAkSk.Feishu feishu);

    void getAuthScopes(Client feishuClient, AuthScopes authScopes, String pageToken);

    void getChildrenDepartment(Client feishuClient, String departmentId, List<Department> departmentList, String pageToken);

    List<Department> listDepartment(Client feishuClient, List<String> departmentIds);

    void getUserListByDepartmentId(Client feishuClient, String departmentId, List<User> userList, String pageToken);

    String getUserAccessToken(Client client, String authCode);

    GetUserInfoRespBody getUserInfoByCode(Client client, String accessToken);

    FeishuJsTicketDto createJsapiSignature(FeishuJsAuth feishuJsAuth);

    Boolean sendInnerLinkMessage(String title, String text, String string, List<String> userIds, JSONObject form, Long companyId);

    User[] listUser(Client feishuClient, List<String> userIds);
}
