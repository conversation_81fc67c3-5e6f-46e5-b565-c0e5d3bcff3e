package com.niimbot.asset.thirdparty.service.impl;

import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.ResponseCode;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiUserGetbyunionidRequest;
import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiUserGetbyunionidResponse;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.taobao.api.ApiException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/23 17:00
 */
@Slf4j
@Service
public class DingOpenApiServiceImpl implements DingOpenApiService {

    private final RedisService redisService;

    private final OrgService orgService;

    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;


    @Autowired
    public DingOpenApiServiceImpl(RedisService redisService,
                                  OrgService orgService,
                                  AsThirdPartyEmployeeService thirdPartyEmployeeService) {
        this.redisService = redisService;
        this.orgService = orgService;
        this.thirdPartyEmployeeService = thirdPartyEmployeeService;
    }


    @Override
    public String getToken(Long companyId, String appKey, String appSecret) {
        // 查询缓存是否存在access_token
        String accessToken = Convert.toStr(redisService.get(ThirdPartyConstant.getDingtalkToken(companyId)));
        if (StrUtil.isBlank(accessToken)) {
            DingTalkClient client = new DefaultDingTalkClient(GET_TOKEN);
            OapiGettokenRequest request = new OapiGettokenRequest();
            request.setAppkey(appKey);
            request.setAppsecret(appSecret);
            request.setHttpMethod(HttpMethod.GET.name());
            try {
                OapiGettokenResponse rsp = client.execute(request);
                if (ResponseCode.SUCCESS == rsp.getErrcode()) {
                    accessToken = rsp.getAccessToken();
                    // 提前5分钟过期
                    long expiresIn = rsp.getExpiresIn() - 300;
                    redisService.set(ThirdPartyConstant.getDingtalkToken(companyId), accessToken, expiresIn, TimeUnit.SECONDS);

                } else {
                    log.error("company {} get dingtalk access token error ==>> [{}] {}", companyId, rsp.getErrcode(), rsp.getErrmsg());
                    throw new BusinessException(ThirdPartyResultCode.DINGTALK_AKSK_ERROR);
                }
            } catch (ApiException e) {
                log.error("company {} get dingtalk access token error ==>> [{}] {}", companyId, e.getErrCode(), e.getErrMsg());
                throw new BusinessException(ThirdPartyResultCode.DINGTALK_AKSK_ERROR);
            }
        }
        return accessToken;
    }


    @Override
    public String getUserIdByUnionId(String unionId, String accessToken) {
        DingTalkClient client = new DefaultDingTalkClient(GET_USER_ID_BY_UNION_ID);
        OapiUserGetbyunionidRequest req = new OapiUserGetbyunionidRequest();
        req.setUnionid(unionId);
        try {
            OapiUserGetbyunionidResponse rsp = client.execute(req, accessToken);
            if (ResponseCode.SUCCESS == rsp.getErrcode()) {
                return rsp.getResult().getUserid();
            }else {
                throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
            }
        } catch (ApiException e) {
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
    }

    @Override
    public AsOrg getDept(Long deptId, Long companyId, String accessToken) {
        DingTalkClient client = new DefaultDingTalkClient(GET_DEPARTMENT);
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(deptId);
        try {
            OapiV2DepartmentGetResponse rsp = client.execute(req, accessToken);
            if (ResponseCode.SUCCESS == rsp.getErrcode()) {
                OapiV2DepartmentGetResponse.DeptGetResponse deptBaseResponse = rsp.getResult();
                AsOrg pOrg = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getExternalOrgId, Convert.toStr(deptBaseResponse.getParentId())));
                // 查询当前节点
                deptBaseResponse.getDeptManagerUseridList();
                AsOrg org = new AsOrg();
                org.setId(IdUtils.getId());
                org.setExternalOrgId(Convert.toStr(deptBaseResponse.getDeptId()));
                org.setOrgName(deptBaseResponse.getName());
                if (ObjectUtil.isNotNull(pOrg)) {
                    org.setLevel(pOrg.getLevel() + 1);
                    org.setPaths(pOrg.getPaths() + pOrg.getId() + ",");
                    org.setPid(pOrg.getId());
                    org.setExternalPid(pOrg.getExternalOrgId());

                    // 查询该部门公司的所属公司
                    List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
                    Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                    org.setCompanyOwner(orgCompanyOwner);
                } else {
                    org.setLevel(0);
                    org.setPaths("0,");
                    org.setPid(0L);
                    org.setExternalPid("0");
                    org.setCompanyOwner(org.getId());
                }
                org.setCompanyId(companyId);
                org.setOrgCode(Convert.toStr(deptBaseResponse.getDeptId()));
                List<String> userIdList = deptBaseResponse.getDeptManagerUseridList();
                if (CollUtil.isNotEmpty(userIdList)) {
                    List<AsThirdPartyEmployee> thirdPartyEmployeeList = thirdPartyEmployeeService.list(Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                            .select(AsThirdPartyEmployee::getEmployeeId)
                            .in(AsThirdPartyEmployee::getUserId, userIdList)
                            .eq(AsThirdPartyEmployee::getType, ThirdPartyConstant.DINGTALK)
                            .eq(AsThirdPartyEmployee::getCompanyId, companyId));
                    org.setDirector(thirdPartyEmployeeList.stream().map(AsThirdPartyEmployee::getEmployeeId).collect(Collectors.toList()));
                }
                return org;
            } else {
                log.warn("company {} get dept {} error ==>> [{}] {}", companyId, deptId, rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
            }
        } catch (ApiException e) {
            log.error("company {} get dept {} error ==>> [{}] {}", companyId, deptId, e.getErrCode(), e.getErrMsg());
            throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
        }
    }

    @Override
    public ThirdPartyEmp getEmp(String userId, Long companyId, String accessToken) {
        DingTalkClient client = new DefaultDingTalkClient(GET_USER_BY_USER_ID);
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        try {
            OapiV2UserGetResponse rsp = client.execute(req, accessToken);
            if (ResponseCode.SUCCESS == rsp.getErrcode()) {
                OapiV2UserGetResponse.UserGetResponse user = rsp.getResult();
                ThirdPartyEmp emp = new ThirdPartyEmp();
                emp.setName(user.getName());
                emp.setJobNum(user.getJobNumber());
                emp.setPosition(user.getTitle());
                emp.setAvatar(user.getAvatar());
                emp.setThirdPartyId(user.getUserid());
                // emp.setAccount(user.getUserid());
                emp.setMobile(user.getMobile());
                emp.setEmail(StrUtil.isNotEmpty(user.getOrgEmail()) ? user.getOrgEmail() : user.getEmail());
                emp.setDeptIdList(user.getDeptIdList().stream().map(Convert::toStr).collect(Collectors.toList()));
                emp.setCompanyId(companyId);
                return emp;
            } else {
                log.warn("company {} get user {} error ==>> [{}] {}", companyId, userId, rsp.getErrcode(), rsp.getErrmsg());
                throw new BusinessException(HttpStatus.BAD_REQUEST.value(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("company {} get user {} error ==>> [{}] {}", companyId, userId, e.getErrCode(), e.getErrMsg());
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), e.getErrMsg());
        }
    }

}
