package com.niimbot.asset.thirdparty.utils;

import com.lark.oapi.core.utils.Decryptor;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.apache.commons.codec.binary.Hex;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2024/7/12 16:27
 */
public class FeishuEncryptor {

    public static void checkSignature(String signature, String timestamp, String nonce, String encryptKey, String body) {
        StringBuilder content = new StringBuilder();
        content.append(timestamp).append(nonce).append(encryptKey).append(body);
        MessageDigest alg;
        try {
            alg = MessageDigest.getInstance("SHA-256");
            String sign = Hex.encodeHexString(alg.digest(content.toString().getBytes()));
            if (!StrUtil.equals(sign, signature)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "签名计算失败");
            }
        } catch (NoSuchAlgorithmException e) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "签名计算失败");
        }
    }

    public static void main(String[] args) {
        String s = "QhAjEIinkNYWEOFLRwYEcHPK4B3cYRBhneOQX2hs+rjvylP1TdljuDZpkISt8OUYt1i47F3AU9H4foSIwgG3rz704ldjFbf9GuQX8R5UbIR1tDO6oFOOkwvucw4HeM2m4P0l2GUwYLAtx+n+ZlZRLL2rT9yxgM55eWtR0+yAbLuSIQFPZfcsoVwa5ZNNgND/";
        System.out.println(decrypt("DJli3cTT901iOyLObE2UNgXBF4PgExJv", s));
    }

    public static String decrypt(String encryptKey, String base64) {
        Decryptor decryptor = new Decryptor(encryptKey);
        return decryptor.decrypt(base64);
    }

}
