package com.niimbot.asset.thirdparty.app.service.impl;

import com.niimbot.asset.thirdparty.app.entity.AsMessageArchive;
import com.niimbot.asset.thirdparty.app.service.AsMessageArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息归档服务实现类 - 通用归档服务实现
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
@Service
@Slf4j
public class AsMessageArchiveServiceImpl implements AsMessageArchiveService {
    
    private final Map<String, AsMessageArchive> archiveStore = new ConcurrentHashMap<>();
    
    @Override
    public void saveArchive(AsMessageArchive archive) {
        archiveStore.put(archive.getEventId(), archive);
        log.info("保存归档消息: eventId={}, thirdPartyType={}, eventType={}", 
                archive.getEventId(), archive.getThirdPartyType(), archive.getEventType());
    }
    
    @Override
    public AsMessageArchive getArchive(String eventId) {
        return archiveStore.get(eventId);
    }
    
    @Override
    public void updateArchive(AsMessageArchive archive) {
        archiveStore.put(archive.getEventId(), archive);
        log.debug("更新归档消息: eventId={}, status={}, retryCount={}", 
                 archive.getEventId(), archive.getStatus(), archive.getRetryCount());
    }
    
    @Override
    public void removeArchive(String eventId) {
        AsMessageArchive removed = archiveStore.remove(eventId);
        if (removed != null) {
            log.info("移除归档消息: eventId={}, thirdPartyType={}, eventType={}", 
                    eventId, removed.getThirdPartyType(), removed.getEventType());
        }
    }
    
    @Override
    public List<AsMessageArchive> getArchivesByThirdPartyType(String thirdPartyType) {
        return archiveStore.values().stream()
                .filter(archive -> thirdPartyType.equals(archive.getThirdPartyType()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AsMessageArchive> getArchivesByStatus(String status) {
        return archiveStore.values().stream()
                .filter(archive -> status.equals(archive.getStatus()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AsMessageArchive> getRetryableArchives() {
        LocalDateTime now = LocalDateTime.now();
        return archiveStore.values().stream()
                .filter(AsMessageArchive::canRetry)
                .filter(archive -> archive.getNextRetryTime() == null || 
                                 archive.getNextRetryTime().isBefore(now))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AsMessageArchive> getArchivesByTypeAndEvent(String thirdPartyType, String eventType) {
        return archiveStore.values().stream()
                .filter(archive -> thirdPartyType.equals(archive.getThirdPartyType()) && 
                                 eventType.equals(archive.getEventType()))
                .collect(Collectors.toList());
    }
}