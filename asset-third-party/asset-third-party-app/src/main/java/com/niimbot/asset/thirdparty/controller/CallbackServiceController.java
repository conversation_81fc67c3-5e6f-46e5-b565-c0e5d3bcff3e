package com.niimbot.asset.thirdparty.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.niimbot.asset.thirdparty.event.OthersCallbackDto;
import com.niimbot.asset.thirdparty.config.DingBlockQueue;
import com.niimbot.asset.thirdparty.config.FeishuBlockQueue;
import com.niimbot.asset.thirdparty.config.OthersBlockQueue;
import com.niimbot.asset.thirdparty.config.WeChatBlockQueue;
import com.niimbot.asset.thirdparty.constant.FeishuCallbackEvent;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;
import com.niimbot.asset.thirdparty.service.OrgStructureService;
import com.niimbot.asset.thirdparty.utils.FeishuEncryptor;
import com.niimbot.thirdparty.DingCallbackDto;
import com.niimbot.thirdparty.FeishuCallbackDto;
import com.niimbot.thirdparty.WeChatCallbackDto;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.util.crypto.WxCpTpCryptUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 工具箱控制器
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Slf4j
@Validated
@RestController
@RequestMapping("server/thirdparty/callback")
public class CallbackServiceController {

    private final OrgStructureService orgStructureService;

    private final DingOpenApiService dingOpenApiService;

    private final WxCpTpService assetWxCpTpService;

    private final WxCpTpCryptUtil assetWxCpTpCrypt;

    @Autowired
    private DingBlockQueue dingBlockQueue;
    @Autowired
    private WeChatBlockQueue weChatBlockQueue;
    @Autowired
    private FeishuBlockQueue feishuBlockQueue;
    @Autowired
    private OthersBlockQueue othersBlockQueue;

    @Autowired
    public CallbackServiceController(
            @Qualifier("dingtalkOrgStructureServiceImpl")
                    OrgStructureService orgStructureService,
            DingOpenApiService dingOpenApiService,
            WxCpTpService assetWxCpTpService,
            WxCpTpCryptUtil assetWxCpTpCrypt) {
        this.orgStructureService = orgStructureService;
        this.dingOpenApiService = dingOpenApiService;
        this.assetWxCpTpService = assetWxCpTpService;
        this.assetWxCpTpCrypt = assetWxCpTpCrypt;
    }

    @PostMapping("/dingtalk")
    public Map<String, String> dingCallback(@RequestBody DingCallbackDto callback) {
        String params = "signature:" + callback.getSignature() + " timestamp:" + callback.getTimestamp() + " nonce:" + callback.getNonce() + " body:" + callback.getBody();
        try {
            log.info("process callback params:" + params);
            // 查询AK/SK
            JSONObject secret = orgStructureService.getSecret(callback.getCompanyId(), ThirdPartyConstant.DINGTALK);
            if (secret == null) {
                log.info("company {} dingtalk does not has aksk", callback.getCompanyId());
                return MapUtil.empty();
            }
            ThirdPartyAkSk.DingTalk dingTalk = secret.toJavaObject(ThirdPartyAkSk.DingTalk.class);
            String accessToken = dingOpenApiService.getToken(callback.getCompanyId(), dingTalk.getAppKey(), dingTalk.getAppSecret());
            DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(dingTalk.getToken(), dingTalk.getAesKey(), dingTalk.getAppKey());
            // 从body中获取回调信息的加密数据进行解密处理
            String encrypt = callback.getBody().getString("encrypt");
            String plainText = dingTalkEncryptor.getDecryptMsg(callback.getSignature(), callback.getTimestamp().toString(), callback.getNonce(), encrypt);
            JSONObject content = JSON.parseObject(plainText);

            dingBlockQueue.put(new DingBlockQueue.DingQueueMsg(callback.getCompanyId(), accessToken, content));
            return dingTalkEncryptor.getEncryptedMap("success", callback.getTimestamp(), callback.getNonce());
        } catch (Exception e) {
            log.error("process callback fail." + params, e);
            return MapUtil.empty();
        }
    }

    @GetMapping("/wechatCallRequest")
    public String wechatCallGetRequest(WeChatCallbackDto callback) {
        boolean signature = assetWxCpTpService.checkSignature(callback.getSignature(), callback.getTimestamp(), callback.getNonce(), callback.getEchostr());
        if (signature) {
            String decrypt = assetWxCpTpCrypt.decrypt(callback.getEchostr());
            log.info(decrypt);
            return decrypt;
        }
        return "success";
    }

    @PostMapping("/wechatCallRequest")
    public String wechatCallPostRequest(@RequestBody WeChatCallbackDto callback) {
        String decryptXml = assetWxCpTpCrypt.decryptXml(callback.getSignature(), callback.getTimestamp(), callback.getNonce(), callback.getRequestBody());
        WxCpTpXmlMessage message = WxCpTpXmlMessage.fromXml(decryptXml);
        log.info("接收到的企业微信回调消息 : {}", JSONObject.toJSONString(message));
        // 处理回调事件
        weChatBlockQueue.put(message);
        return "success";
    }

    @PostMapping("/feishu")
    public JSONObject feishuCallback(@RequestBody FeishuCallbackDto callback) {
        String params = "signature:" + callback.getSignature() + " timestamp:" + callback.getTimestamp() + " nonce:" + callback.getNonce() + " body:" + callback.getBody();
        try {
            log.info("{} process callback params:{}", callback.getCompanyId(), params);
            // 查询AK/SK
            JSONObject secret = orgStructureService.getSecret(callback.getCompanyId(), ThirdPartyConstant.FEISHU);
            if (secret == null) {
                log.info("company {} feishu does not has aksk", callback.getCompanyId());
                return new JSONObject();
            }
            ThirdPartyAkSk.Feishu feishu = secret.toJavaObject(ThirdPartyAkSk.Feishu.class);
            // ====== 如果签名不为空，校验一下 =======
            if (StrUtil.isNotEmpty(callback.getSignature())) {
                FeishuEncryptor.checkSignature(callback.getSignature(), callback.getTimestamp(),
                        callback.getNonce(), feishu.getEncryptKey(), callback.getBody().toJSONString());
            }
            // 从body中获取回调信息的加密数据进行解密处理
            String encrypt = callback.getBody().getString("encrypt");
            // ====== 数据解密 =======
            String plainText = FeishuEncryptor.decrypt(feishu.getEncryptKey(), encrypt);
            JSONObject content;
            try {
                content = JSON.parseObject(plainText);
            } catch (Exception e) {
                log.error("解密失败，EncryptKey = {}，data = {}", feishu.getEncryptKey(), encrypt);
                return new JSONObject();
            }

            // ====== 判断事件是否是验证回调 =======
            if (FeishuCallbackEvent.URL_VERIFICATION.equals(content.getString("type"))) {
                // ====== 校验token =======
                if (!StrUtil.equals(feishu.getVerificationToken(), content.getString("token"))) {
                    log.error("feishu verify token fail, companyId = {}", callback.getCompanyId());
                    return new JSONObject();
                }
                return content;
            }

            // ===== 事件分发 ======
            feishuBlockQueue.put(new FeishuBlockQueue.FeishuQueueMsg(callback.getCompanyId(), feishu, content));
            return new JSONObject();
        } catch (Exception e) {
            log.error("feishu process callback fail." + params, e);
            return new JSONObject();
        }
    }

    @PostMapping("/others")
    public Map<String, String> othersCallback(@RequestBody OthersCallbackDto callback) {
        try {
            log.info("others process callback params:{}", callback);
            othersBlockQueue.put(new OthersBlockQueue
                    .OthersQueueMsg(callback.getCompanyId(), callback.getBody()));
            return MapUtil.of("status", "success");
        } catch (Exception e) {
            log.error("others process callback fail.{}", e.getMessage(), e);
            return MapUtil.empty();
        }
    }

}
