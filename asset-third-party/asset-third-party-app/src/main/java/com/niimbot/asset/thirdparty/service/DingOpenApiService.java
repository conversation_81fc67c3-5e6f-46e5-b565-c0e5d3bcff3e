package com.niimbot.asset.thirdparty.service;

import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;

/**
 * <AUTHOR>
 * @since 2021/8/23 17:00
 */
public interface DingOpenApiService {

    String API_URL = "https://oapi.dingtalk.com";
    String GET_TOKEN = API_URL + "/gettoken";
    String GET_DEPARTMENT = API_URL + "/topapi/v2/department/get";
    String DEPARTMENT_LIST_SUB = API_URL + "/topapi/v2/department/listsub";
    String USER_LIST = API_URL + "/topapi/v2/user/list";
    String GET_USER_BY_USER_ID = API_URL + "/topapi/v2/user/get";
    String GET_BY_MOBILE = API_URL + "/topapi/v2/user/getbymobile";
    String GET_USER_ID_BY_UNION_ID = API_URL + "/topapi/user/getbyunionid";

    String getToken(Long companyId, String appKey, String appSecret);

    String getUserIdByUnionId(String unionId, String accessToken);

    AsOrg getDept(Long deptId, Long companyId, String accessToken);

    ThirdPartyEmp getEmp(String userId, Long companyId, String accessToken);
}
