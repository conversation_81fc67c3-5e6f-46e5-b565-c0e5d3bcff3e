package com.niimbot.asset.thirdparty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.ResponseCode;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiV2UserGetbymobileRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiV2UserGetbymobileResponse;
import com.lark.oapi.Client;
import com.lark.oapi.service.auth.v3.model.InternalTenantAccessTokenReq;
import com.lark.oapi.service.auth.v3.model.InternalTenantAccessTokenReqBody;
import com.lark.oapi.service.auth.v3.model.InternalTenantAccessTokenResp;
import com.lark.oapi.service.contact.v3.model.*;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.MessageAbs;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.thirdparty.model.AsThirdParty;
import com.niimbot.asset.thirdparty.restops.RestOps;
import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.mapper.AsThirdPartyMapper;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.*;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxError;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Slf4j
@Service
public class AsThirdPartyServiceImpl extends ServiceImpl<AsThirdPartyMapper, AsThirdParty> implements AsThirdPartyService {

    private final RedisService redisService;

    private final DingOpenApiService dingOpenApiService;

    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    private final AsCusEmployeeService employeeService;

    @Autowired
    private CusUserService cusUserService;

    private final WxCpTpService assetWxCpTpService;

    private final OrgStructureHandler orgStructureHandler;

    @Resource
    private MessageAbs messageAbs;

    @Autowired
    public AsThirdPartyServiceImpl(RedisService redisService,
                                   DingOpenApiService dingOpenApiService,
                                   AsThirdPartyEmployeeService thirdPartyEmployeeService,
                                   AsCusEmployeeService employeeService,
                                   WxCpTpService assetWxCpTpService,
                                   @Lazy OrgStructureHandler orgStructureHandler) {
        this.redisService = redisService;
        this.dingOpenApiService = dingOpenApiService;
        this.thirdPartyEmployeeService = thirdPartyEmployeeService;
        this.employeeService = employeeService;
        this.assetWxCpTpService = assetWxCpTpService;
        this.orgStructureHandler = orgStructureHandler;
        this.messageAbs = messageAbs;
    }

    @Override
    public Boolean getInitSync(Long companyId) {
        AsThirdParty party = this.getOne(
                Wrappers.lambdaQuery(AsThirdParty.class)
                        .select(AsThirdParty::getInitSync)
                        .eq(AsThirdParty::getCompanyId, companyId), false
        );
        if (Objects.isNull(party)) {
            throw new BusinessException(ThirdPartyResultCode.UNBIND_THIRD_PARTY);
        }
        return party.getInitSync();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BindThirdPartyResult bindDingtalk(DingtalkBindDto dingtalk) {
        AsThirdParty thirdParty = this.getOne(new LambdaQueryWrapper<AsThirdParty>()
                .eq(AsThirdParty::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsThirdParty::getType, ThirdPartyConstant.DINGTALK));
        if (Objects.nonNull(thirdParty)) {
            return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, false, "当前企业已绑定过钉钉，如需更换请解绑后重新绑定");
        }
        thirdParty = BeanUtil.copyProperties(dingtalk, AsThirdParty.class);
        ThirdPartyAkSk.DingTalk aksk = BeanUtil.copyProperties(dingtalk, ThirdPartyAkSk.DingTalk.class);
        if (Objects.nonNull(this.getBaseMapper().selectDingtalkByAppKeyAndSecret(aksk.getAppKey(), aksk.getAppSecret()))) {
            return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, false, "当前钉钉配置已绑定其他企业，请勿重复绑定");
        }
        thirdParty.setForm((JSONObject) JSONObject.toJSON(aksk));
        thirdParty.setCompanyId(LoginUserThreadLocal.getCompanyId());
        thirdParty.setType(ThirdPartyConstant.DINGTALK);

        // 检查AKSK
        DingTalkClient tokenClient = new DefaultDingTalkClient(DingOpenApiService.GET_TOKEN);
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(aksk.getAppKey());
        request.setAppsecret(aksk.getAppSecret());
        request.setHttpMethod(HttpMethod.GET.name());
        String accessToken;
        try {
            OapiGettokenResponse rsp = tokenClient.execute(request);
            if (ResponseCode.SUCCESS == rsp.getErrcode()) {
                accessToken = rsp.getAccessToken();
            } else {
                log.error("get access token error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
                return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, false, rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("get access token error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
            return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, false, e.getErrMsg());
        }

        // 检查超管手机号
        String mobile = cusUserService.getAccountMobileByUserId(LoginUserThreadLocal.getCurrentUserId());
        // 检查三方系统是否存在该用户
        String userId;
        DingTalkClient mobileClient = new DefaultDingTalkClient(DingOpenApiService.GET_BY_MOBILE);
        OapiV2UserGetbymobileRequest req = new OapiV2UserGetbymobileRequest();
        req.setMobile(mobile);
        try {
            OapiV2UserGetbymobileResponse rsp = mobileClient.execute(req, accessToken);
            if (ResponseCode.SUCCESS == rsp.getErrcode()) {
                OapiV2UserGetbymobileResponse.UserGetByMobileResponse result = rsp.getResult();
                userId = result.getUserid();
            } else {
                return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, false, String.format(ThirdPartyResultCode.MOBILE_NOT_EXIST.getMessage(), mobile)).setCode(110007);
            }
        } catch (ApiException e) {
            log.error("get user by mobile {} error ==>> [{}] {}", mobile, e.getErrCode(), e.getErrMsg());
            return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, false, e.getErrMsg());
        }

        Long companyId = LoginUserThreadLocal.getCompanyId();
        this.removeById(companyId);
        if (this.save(thirdParty)) {
            cleanAkskRedis(companyId);
            orgStructureHandler.getService(ThirdPartyConstant.DINGTALK).cleanCache(companyId);
            AsThirdPartyEmployee thirdPartyEmployee = thirdPartyEmployeeService.getByEmployeeId(LoginUserThreadLocal.getCurrentUserId()).orElse(
                    new AsThirdPartyEmployee().setId(IdUtils.getId())
                            .setCompanyId(companyId)
                            .setType(ThirdPartyConstant.DINGTALK)
                            .setUserId(userId).setEmployeeId(LoginUserThreadLocal.getCurrentUserId())
            );
            // 提前保存超管的userId
            thirdPartyEmployeeService.saveOrUpdate(thirdPartyEmployee);
            return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, true);
        } else {
            return new BindThirdPartyResult(ThirdPartyConstant.DINGTALK, false, "保存钉钉配置失败，请稍后再试");
        }
    }

    @Override
    public Boolean replenishDingtalk(DingtalkReplenish dto) {
        AsThirdParty thirdParty = this.getOne(new LambdaQueryWrapper<AsThirdParty>()
                .eq(AsThirdParty::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsThirdParty::getType, ThirdPartyConstant.DINGTALK));
        if (Objects.isNull(thirdParty)) {
            throw new BusinessException(ThirdPartyResultCode.UNBIND_THIRD_PARTY);
        }
        if (CollUtil.isEmpty(thirdParty.getForm())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "绑定的钉钉应用配置为空，请解除绑定后重新绑定");
        }
        thirdParty.getForm().put("agentId", dto.getAgentId());
        thirdParty.getForm().put("corpId", dto.getCorpId());
        this.updateById(thirdParty);
        cleanAkskRedis(LoginUserThreadLocal.getCompanyId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeDingtalk() {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        removeCheck(companyId);
        String dingtalkKey = ThirdPartyConstant.getDingtalkKey(companyId);
        String dingtalkToken = ThirdPartyConstant.getDingtalkToken(companyId);
        redisService.del(ListUtil.of(dingtalkKey, dingtalkToken));
        orgStructureHandler.getService(ThirdPartyConstant.DINGTALK).cleanCache(companyId);
        this.remove(Wrappers.lambdaQuery(AsThirdParty.class).eq(AsThirdParty::getCompanyId, companyId).eq(AsThirdParty::getType, ThirdPartyConstant.DINGTALK));
        thirdPartyEmployeeService.removeByCompanyId(companyId, ThirdPartyConstant.DINGTALK);
        // 删除三方消息配置
        messageAbs.cleanThirdPartyMessageRules(LoginUserThreadLocal.getCompanyId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BindThirdPartyResult bindWeChat(WeChatBindDto wechat) {
        // 转换成加密的corpId
        String providerToken = RestOps.handle(assetWxCpTpService::getWxCpProviderToken).get();
        JSONObject body = new JSONObject();
        body.put("corpid", wechat.getCorpId());
        JSONObject result;
        try {
            String post = assetWxCpTpService.post("https://qyapi.weixin.qq.com/cgi-bin/service/corpid_to_opencorpid?provider_access_token=" + providerToken, body.toString());
            result = JSONObject.parseObject(post);
        } catch (WxErrorException e) {
            log.error("换取密文corpId错误", e);
            return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, false, e.getError().getErrorMsg());
        }
        String openCorpid = result.getString("open_corpid");
        AsThirdParty weChatBind = getWeChatBind(openCorpid);
        if (Objects.isNull(weChatBind)) {
            return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, false, "请先扫码激活企业微信代开发应用");
        }
        if (Objects.nonNull(weChatBind.getCompanyId()) && !Objects.equals(weChatBind.getCompanyId(), LoginUserThreadLocal.getCompanyId())) {
            return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, false, "当前企业已绑定过企业微信，如需更换请解绑后重新绑定");
        }
        weChatBind.setCompanyId(LoginUserThreadLocal.getCompanyId()).setCreateBy(LoginUserThreadLocal.getCurrentUserId());
        ThirdPartyAkSk.WeChat config = weChatBind.getForm().toJavaObject(ThirdPartyAkSk.WeChat.class);
        config.setDecryptCropId(wechat.getCorpId());
        weChatBind.setForm((JSONObject) JSONObject.toJSON(config));
        WxCpDefaultConfigImpl defaultConfig = new WxCpDefaultConfigImpl();
        defaultConfig.setCorpId(config.getCorpId());
        defaultConfig.setCorpSecret(config.getSecret());
        WxCpServiceImpl wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(defaultConfig);
        // 检查AKSK
        try {
            wxCpService.getAccessToken();
        } catch (WxErrorException e) {
            log.error("检查企业微信AKSK错误", e);
            WxError error = e.getError();
            return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, false, error.getErrorMsg());
        }
        // 检查超管手机号
        String mobile = cusUserService.getAccountMobileByUserId(LoginUserThreadLocal.getCurrentUserId());
        String userId;
        try {
            userId = wxCpService.getUserService().getUserId(mobile);
        } catch (WxErrorException e) {
            log.error("获取超管userId错误", e);
            if (e.getError().getErrorMsg().contains("用户不存在")) {
                return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, false, mobile).setCode(110007);
            } else {
                return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, false, e.getError().getErrorMsg());
            }
        }
        // 更新配置
        if (!this.updateById(weChatBind)) {
            return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, false, "更新三方信息失败，请稍后再试");
        }
        cleanAkskRedis(LoginUserThreadLocal.getCompanyId());
        orgStructureHandler.getService(ThirdPartyConstant.WECHAT).cleanCache(LoginUserThreadLocal.getCompanyId());
        AsThirdPartyEmployee thirdPartyEmployee = thirdPartyEmployeeService.getByEmployeeId(LoginUserThreadLocal.getCurrentUserId()).orElse(
                new AsThirdPartyEmployee().setId(IdUtils.getId())
                        .setCompanyId(LoginUserThreadLocal.getCompanyId())
                        .setType(ThirdPartyConstant.WECHAT)
                        .setUserId(userId).setEmployeeId(LoginUserThreadLocal.getCurrentUserId())
        );
        // 提前保存超管的userId
        thirdPartyEmployeeService.saveOrUpdate(thirdPartyEmployee);
        return new BindThirdPartyResult(ThirdPartyConstant.WECHAT, true);
    }

    private void removeCheck(Long companyId) {
        if (BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            return;
        }
        AsCusEmployee administrator = employeeService.getAdministratorByCompanyId(companyId);
        throw new BusinessException(
                SystemResultCode.GENERIC_EXCEPTION,
                "此功能需要使用超管账号操作，请联系超级管理员：" + administrator.getEmpName() + "(" + administrator.getEmpNo() + ")"
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeWeChat(Long companyId, boolean removeCheck) {
        if (removeCheck) {
            removeCheck(companyId);
        }
        if (Objects.isNull(companyId)) {
            companyId = LoginUserThreadLocal.getCompanyId();
        }
        String wechatKey = ThirdPartyConstant.getWeChatKey(companyId);
        String wechatToken = ThirdPartyConstant.getWeChatToken(companyId);
        redisService.del(ListUtil.of(wechatKey, wechatToken));
        orgStructureHandler.getService(ThirdPartyConstant.WECHAT).cleanCache(companyId);
        this.remove(Wrappers.lambdaQuery(AsThirdParty.class).eq(AsThirdParty::getCompanyId, companyId).eq(AsThirdParty::getType, ThirdPartyConstant.WECHAT));
        thirdPartyEmployeeService.removeByCompanyId(companyId, ThirdPartyConstant.WECHAT);
        // 删除三方消息配置
        messageAbs.cleanThirdPartyMessageRules(LoginUserThreadLocal.getCompanyId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BindThirdPartyResult bindFeishu(FeishuBindDto feishu) {
        AsThirdParty thirdParty = this.getOne(new LambdaQueryWrapper<AsThirdParty>()
                .eq(AsThirdParty::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsThirdParty::getType, ThirdPartyConstant.FEISHU));
        if (Objects.nonNull(thirdParty)) {
            return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, "当前企业已绑定过飞书，如需更换请解绑后重新绑定");
        }
        thirdParty = new AsThirdParty();
        ThirdPartyAkSk.Feishu aksk = BeanUtil.copyProperties(feishu, ThirdPartyAkSk.Feishu.class);
        if (Objects.nonNull(this.getBaseMapper().selectFeishuByAppIdAndSecret(aksk.getAppId(), aksk.getAppSecret()))) {
            return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, "当前飞书配置已绑定其他企业，请勿重复绑定");
        }
        thirdParty.setForm((JSONObject) JSONObject.toJSON(aksk));
        thirdParty.setCompanyId(LoginUserThreadLocal.getCompanyId());
        thirdParty.setType(ThirdPartyConstant.FEISHU);

        // 检查AKSK
        Client client = Client.newBuilder(aksk.getAppId(), aksk.getAppSecret()).build();
        // 创建请求对象
        InternalTenantAccessTokenReq req = InternalTenantAccessTokenReq.newBuilder()
                .internalTenantAccessTokenReqBody(InternalTenantAccessTokenReqBody.newBuilder()
                        .appId(aksk.getAppId())
                        .appSecret(aksk.getAppSecret())
                        .build())
                .build();
        // 发起请求
        try {
            InternalTenantAccessTokenResp resp = client.auth().tenantAccessToken().internal(req);
            if (!resp.success()) {
                log.error("get feishu access token error ==>> [{}] {}", resp.getCode(), resp.getMsg());
                return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, resp.getMsg());
            }
        } catch (Exception e) {
            log.error("get feishu access token error ==>> {}", e.getMessage(), e);
            return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, e.getMessage());
        }
        // 检查超管手机号
        String mobile = cusUserService.getAccountMobileByUserId(LoginUserThreadLocal.getCurrentUserId());
        String userId = null;
        // 检查三方系统是否存在该用户
        BatchGetIdUserReq batchGetIdUserReq = BatchGetIdUserReq.newBuilder()
                .batchGetIdUserReqBody(BatchGetIdUserReqBody.newBuilder()
                        .mobiles(new String[]{mobile})
                        .includeResigned(false)
                        .build())
                .build();
        // 发起请求
        try {
            BatchGetIdUserResp resp = client.contact().user().batchGetId(batchGetIdUserReq);
            if (resp.success()) {
                BatchGetIdUserRespBody userRespBody = resp.getData();
                for (UserContactInfo userContactInfo : userRespBody.getUserList()) {
                    if (StrUtil.isNotEmpty(userContactInfo.getUserId())
                            && ObjectUtil.equals(userContactInfo.getMobile(), mobile)) {
                        userId = userContactInfo.getUserId();
                        break;
                    }
                }
            } else {
                return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, resp.getMsg()).setCode(110007);
            }
            if (StrUtil.isEmpty(userId)) {
                return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, String.format(ThirdPartyResultCode.MOBILE_NOT_EXIST.getMessage(), mobile)).setCode(110007);
            }
        } catch (Exception e) {
            log.error("get user by mobile {} error ==>> {}", mobile, e.getMessage(), e);
            return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, e.getMessage());
        }
        Long companyId = LoginUserThreadLocal.getCompanyId();
        this.removeById(companyId);
        if (this.save(thirdParty)) {
            cleanAkskRedis(companyId);
            orgStructureHandler.getService(ThirdPartyConstant.FEISHU).cleanCache(companyId);
            AsThirdPartyEmployee thirdPartyEmployee = thirdPartyEmployeeService.getByEmployeeId(LoginUserThreadLocal.getCurrentUserId()).orElse(
                    new AsThirdPartyEmployee().setId(IdUtils.getId())
                            .setCompanyId(companyId)
                            .setType(ThirdPartyConstant.FEISHU)
                            .setUserId(userId).setEmployeeId(LoginUserThreadLocal.getCurrentUserId())
            );
            // 提前保存超管的userId
            thirdPartyEmployeeService.saveOrUpdate(thirdPartyEmployee);
            return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, true);
        } else {
            return new BindThirdPartyResult(ThirdPartyConstant.FEISHU, false, "保存飞书配置失败，请稍后再试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeFeishu() {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        removeCheck(companyId);
        String feishuKey = ThirdPartyConstant.getFeishuKey(companyId);
        String feishuToken = ThirdPartyConstant.getFeishuToken(companyId);
        redisService.del(ListUtil.of(feishuKey, feishuToken));
        orgStructureHandler.getService(ThirdPartyConstant.FEISHU).cleanCache(companyId);
        this.remove(Wrappers.lambdaQuery(AsThirdParty.class).eq(AsThirdParty::getCompanyId, companyId).eq(AsThirdParty::getType, ThirdPartyConstant.FEISHU));
        thirdPartyEmployeeService.removeByCompanyId(companyId, ThirdPartyConstant.FEISHU);
        // 删除三方消息配置
        messageAbs.cleanThirdPartyMessageRules(LoginUserThreadLocal.getCompanyId());
        return true;
    }

    @Override
    public JSONObject getSecret(Long companyId, String type) {
        JSONObject formData;
        switch (type) {
            case ThirdPartyConstant.DINGTALK:
                formData = orgStructureHandler.getService(type).getSecret(companyId, ThirdPartyConstant.DINGTALK);
                break;
            case ThirdPartyConstant.WECHAT:
                formData = orgStructureHandler.getService(type).getSecret(companyId, ThirdPartyConstant.WECHAT);
                break;
            case ThirdPartyConstant.FEISHU:
                formData = orgStructureHandler.getService(type).getSecret(companyId, ThirdPartyConstant.FEISHU);
                break;
            default:
                formData = null;
        }
        if (Objects.isNull(formData)) {
            throw new BusinessException(ThirdPartyResultCode.UNBIND_THIRD_PARTY);
        }
        return formData;
    }

    @Override
    public AsThirdParty getWeChatBind(String corpId) {
        return this.getBaseMapper().selectWechatByCorpId(corpId);
    }

    @Override
    public Long getWechatCompany(String cropId) {
        return this.getBaseMapper().selectCompanyIdByCorpId(cropId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindWechat(String corpId) {
        Long companyId = this.getBaseMapper().selectCompanyIdByCorpId(corpId);
        if (Objects.isNull(companyId)) {
            return true;
        }
        return removeWeChat(companyId, false);
    }

    @Override
    public WxCpService getWeChatService(Long companyId) {
        JSONObject config = getSecret(companyId, ThirdPartyConstant.WECHAT);
        ThirdPartyAkSk.WeChat weChat = config.toJavaObject(ThirdPartyAkSk.WeChat.class);
        WxCpDefaultConfigImpl defaultConfig = new WxCpDefaultConfigImpl();
        defaultConfig.setCorpId(weChat.getCorpId());
        defaultConfig.setToken(weChat.getToken());
        defaultConfig.setCorpSecret(weChat.getSecret());
        defaultConfig.setAesKey(weChat.getEncodingAESKey());
        WxCpServiceImpl wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(defaultConfig);
        return wxCpService;
    }

    @Override
    public boolean getThirdPartyInitSync(Long companyId, String type) {
        List<AsThirdParty> thirdParties = this.list(
                Wrappers.lambdaQuery(AsThirdParty.class)
                        .eq(AsThirdParty::getCompanyId, companyId)
                        .eq(AsThirdParty::getType, type)
        );
        if (CollUtil.isEmpty(thirdParties)) {
            return false;
        }
        return thirdParties.get(0).getInitSync();
    }

    @Override
    public String getDingToken(Long companyId) {
        ThirdPartyAkSk.DingTalk dingTalk = getSecret(companyId, ThirdPartyConstant.DINGTALK).toJavaObject(ThirdPartyAkSk.DingTalk.class);
        return dingOpenApiService.getToken(companyId, dingTalk.getAppKey(), dingTalk.getAppSecret());
    }

    private void cleanAkskRedis(Long companyId) {
        String wechatKey = ThirdPartyConstant.getWeChatKey(companyId);
        String wechatToken = ThirdPartyConstant.getWeChatToken(companyId);

        String dingtalkKey = ThirdPartyConstant.getDingtalkKey(companyId);
        String dingtalkToken = ThirdPartyConstant.getDingtalkToken(companyId);

        String feishuKey = ThirdPartyConstant.getFeishuKey(companyId);
        String feishuToken = ThirdPartyConstant.getFeishuToken(companyId);

        redisService.del(ListUtil.of(
                wechatKey, wechatToken,
                dingtalkKey, dingtalkToken,
                feishuKey, feishuToken));

        orgStructureHandler.getService(ThirdPartyConstant.WECHAT).cleanCache(companyId);
        orgStructureHandler.getService(ThirdPartyConstant.DINGTALK).cleanCache(companyId);
        orgStructureHandler.getService(ThirdPartyConstant.FEISHU).cleanCache(companyId);
    }

}
