package com.niimbot.asset.thirdparty.controller;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.thirdparty.service.SyncApproveRoleService;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("server/thirdparty/syncApproveRole")
@RequiredArgsConstructor
public class SyncApproveRoleServiceController {

    private final RedissonClient redissonClient;

    private final SyncApproveRoleService syncApproveRoleService;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @RequestMapping("/manualSyncFromDing/{companyId}")
    public boolean manualSyncFromDing(@PathVariable Long companyId) {
        RLock lock = redissonClient.getLock("sync_approve_role_from_ding:" + companyId);
        if (lock.isLocked()) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "角色正在同步中，请耐心等待");
        }
        threadPoolTaskExecutor.execute(() -> {
            try {
                lock.lock();
                syncApproveRoleService.manualSyncFromDing(companyId);
            } catch (Exception e) {
                log.error("审批角色同步失败", e);
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        });
        return true;
    }
}