package com.niimbot.asset.thirdparty.service.impl;

import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.thirdparty.service.OrgStructureService;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class OrgStructureHandler {

    private final Map<String, OrgStructureService> serviceMap;

    public OrgStructureHandler(List<OrgStructureService> orgStructureServices) {
        this.serviceMap = orgStructureServices.stream().collect(Collectors.toMap(OrgStructureService::getType, v -> v));
    }

    public OrgStructureService getService(String type) {
        if (!serviceMap.containsKey(type)) {
            throw new BusinessException(ThirdPartyResultCode.NONSUPPORT_AUTH_WAY);
        }
        return serviceMap.get(type);
    }
}
