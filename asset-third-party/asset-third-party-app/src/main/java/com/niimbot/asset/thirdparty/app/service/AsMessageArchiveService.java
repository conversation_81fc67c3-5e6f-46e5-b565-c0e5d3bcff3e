package com.niimbot.asset.thirdparty.app.service;

import com.niimbot.asset.thirdparty.app.entity.AsMessageArchive;
import java.util.List;

/**
 * 消息归档服务接口 - 通用归档服务，支持多种第三方集成类型
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface AsMessageArchiveService {
    
    /**
     * 保存归档消息
     * @param archive 归档消息
     */
    void saveArchive(AsMessageArchive archive);
    
    /**
     * 根据事件ID获取归档消息
     * @param eventId 事件ID
     * @return 归档消息
     */
    AsMessageArchive getArchive(String eventId);
    
    /**
     * 更新归档消息
     * @param archive 归档消息
     */
    void updateArchive(AsMessageArchive archive);
    
    /**
     * 删除归档消息
     * @param eventId 事件ID
     */
    void removeArchive(String eventId);
    
    /**
     * 根据第三方类型获取归档消息列表
     * @param thirdPartyType 第三方类型
     * @return 归档消息列表
     */
    List<AsMessageArchive> getArchivesByThirdPartyType(String thirdPartyType);
    
    /**
     * 根据状态获取归档消息列表
     * @param status 状态
     * @return 归档消息列表
     */
    List<AsMessageArchive> getArchivesByStatus(String status);
    
    /**
     * 获取可重试的归档消息列表
     * @return 可重试的归档消息列表
     */
    List<AsMessageArchive> getRetryableArchives();
    
    /**
     * 根据第三方类型和事件类型获取归档消息列表
     * @param thirdPartyType 第三方类型
     * @param eventType 事件类型
     * @return 归档消息列表
     */
    List<AsMessageArchive> getArchivesByTypeAndEvent(String thirdPartyType, String eventType);
}