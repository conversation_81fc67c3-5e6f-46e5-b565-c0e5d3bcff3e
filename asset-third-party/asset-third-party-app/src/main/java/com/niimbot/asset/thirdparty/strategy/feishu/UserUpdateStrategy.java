package com.niimbot.asset.thirdparty.strategy.feishu;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lark.oapi.service.contact.v3.model.P2UserUpdatedV3;
import com.lark.oapi.service.contact.v3.model.UserEvent;
import com.niimbot.asset.system.service.AsCountryCodeService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.FeishuCallbackEvent;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15 14:14
 */
@Slf4j
@Component
@CallbackStrategy(eventType = FeishuCallbackEvent.USER_UPDATE)
public class UserUpdateStrategy extends AbsUserStrategy implements FeishuCallbackStrategy {

    private final AsCountryCodeService countryCodeService;

    public UserUpdateStrategy(AsCountryCodeService countryCodeService) {
        this.countryCodeService = countryCodeService;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        P2UserUpdatedV3 userUpdatedV3 = content.toJavaObject(P2UserUpdatedV3.class);
        if (checkToken(feishu.getVerificationToken(), userUpdatedV3.getHeader())) {
            // 删除也会发更新消息
            if (userUpdatedV3.getEvent().getOldObject() != null &&
                    userUpdatedV3.getEvent().getOldObject().getStatus() != null &&
                    BooleanUtil.isTrue(userUpdatedV3.getEvent().getOldObject().getStatus().getIsResigned())) {
                // ignore
                return true;
            }
            UserEvent eventData = userUpdatedV3.getEvent().getObject();
            ThirdPartyEmp emp = new ThirdPartyEmp();
            emp.setName(eventData.getName());
            emp.setJobNum(eventData.getEmployeeNo());
            emp.setPosition(eventData.getJobTitle());
            if (eventData.getAvatar() != null) {
                emp.setAvatar(eventData.getAvatar().getAvatar640());
            }
            emp.setThirdPartyId(eventData.getOpenId());
            if (StrUtil.isNotEmpty(eventData.getMobile())) {
                List<String> countryCodes = countryCodeService.allCountryCode();
                for (String countryCode : countryCodes) {
                    if (eventData.getMobile().startsWith(countryCode)) {
                        emp.setMobile(eventData.getMobile().replace(countryCode, StrUtil.EMPTY));
                        break;
                    }
                }
            }
            emp.setEmail(eventData.getEmail());
            emp.setDeptIdList(Lists.newArrayList(eventData.getDepartmentIds()));
            emp.setCompanyId(companyId);
            updateEmp(emp.setThirdPartyType(getType()));
            return true;
        } else {
            log.error("companyId = {}, check token error", companyId);
            return false;
        }
    }

}
