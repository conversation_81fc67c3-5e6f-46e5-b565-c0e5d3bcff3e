package com.niimbot.asset.thirdparty.strategy.dingtalk;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:45
 */
@Slf4j
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.ORG_DEPT_MODIFY)
public class OrgDeptModifyStrategy implements DingCallbackStrategy {

    private final OrgService orgService;

    private final DingOpenApiService dingOpenApiService;

    @Autowired
    public OrgDeptModifyStrategy(OrgService orgService, DingOpenApiService dingOpenApiService) {
        this.orgService = orgService;
        this.dingOpenApiService = dingOpenApiService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        if (log.isDebugEnabled()) {
            log.debug("org_dept_modify: " + content);
        }
        List<Long> deptIds = content.getJSONArray("DeptId").toJavaList(Long.class);
        for (Long deptId : deptIds) {
            // 查询当前节点
            AsOrg org = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, deptId));
            if (ObjectUtil.isNull(org)) {
                log.warn("update company {} org {} has not exists ", companyId, deptId);
                // 新建组织
                AsOrg createOrg = dingOpenApiService.getDept(deptId, companyId, accessToken);
                orgService.save(createOrg);
            } else {
                // 新建组织
                AsOrg updateOrg = dingOpenApiService.getDept(deptId, companyId, accessToken);
                updateOrg.setId(org.getId());
                // 所有子Path
                String sonPath = updateOrg.getPaths() + updateOrg.getId() + ",";
                List<AsOrg> sonList = orgService.list(new QueryWrapper<AsOrg>().lambda()
                        .eq(AsOrg::getCompanyId, companyId)
                        .likeRight(AsOrg::getPaths, sonPath));
                List<AsOrg> updateSonList = new ArrayList<>();
                for (AsOrg son : sonList) {
                    String p = son.getPaths().replace(sonPath, org.getPaths() + org.getId() + ",");
                    int level = p.split(",").length - 1;
                    AsOrg updateSon = new AsOrg();
                    updateSon.setId(son.getId());
                    updateSon.setLevel(level);
                    updateSon.setPaths(p);
                    // 该组织为部门时并且该子节点也是部门时，需要重写所属公司
                    if (AssetConstant.ORG_TYPE_DEPT.equals(son.getOrgType())
                            && AssetConstant.ORG_TYPE_DEPT.equals(org.getOrgType())) {
                        List<Long> ids = Convert.toList(Long.class, p.split(","));
                        Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                        updateSon.setCompanyOwner(orgCompanyOwner);
                    }
                    updateSonList.add(updateSon);
                }
                // 更新子节点数据
                orgService.updateBatchById(updateSonList);
                // 更新当前数据
                orgService.updateById(updateOrg);
                SpringUtil.getBean(CacheOrgStrategy.class).evictCache(updateOrg.getId());
            }
        }

        return true;
    }

}
