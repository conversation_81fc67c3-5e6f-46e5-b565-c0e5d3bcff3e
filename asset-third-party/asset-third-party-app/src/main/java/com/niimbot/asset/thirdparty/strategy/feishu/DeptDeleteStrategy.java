package com.niimbot.asset.thirdparty.strategy.feishu;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.oapi.service.contact.v3.model.DepartmentEvent;
import com.lark.oapi.service.contact.v3.model.P2DepartmentDeletedV3;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsSyncChange;
import com.niimbot.asset.system.service.CompanyAssetService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.SyncChangeService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.FeishuCallbackEvent;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/15 11:08
 */
@Slf4j
@Component
@CallbackStrategy(eventType = FeishuCallbackEvent.DEPT_DELETE)
public class DeptDeleteStrategy implements FeishuCallbackStrategy {

    private final OrgService orgService;
    private final CompanyAssetService companyAssetService;
    private final SyncChangeService syncChangeService;

    public DeptDeleteStrategy(OrgService orgService,
                              CompanyAssetService companyAssetService,
                              SyncChangeService syncChangeService) {
        this.orgService = orgService;
        this.companyAssetService = companyAssetService;
        this.syncChangeService = syncChangeService;
    }

    @Override
    public boolean handleCallback(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        P2DepartmentDeletedV3 departmentDeletedV3 = content.toJavaObject(P2DepartmentDeletedV3.class);
        if (checkToken(feishu.getVerificationToken(), departmentDeletedV3.getHeader())) {
            DepartmentEvent eventData = departmentDeletedV3.getEvent().getObject();
            AsOrg one = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, eventData.getOpenDepartmentId()));
            if (ObjectUtil.isNotNull(one)) {
                boolean remove = orgService.removeById(one.getId());
                if (remove) {
                    // 使用组织
                    List<Long> useOrgIds = companyAssetService.checkUseOrg(ListUtil.of(one.getId()), companyId);
                    // 管理组织
                    List<Long> orgOwnerIds = companyAssetService.checkOrgOwner(ListUtil.of(one.getId()), companyId);
                    useOrgIds.addAll(orgOwnerIds);
                    if (!useOrgIds.isEmpty()) {
                        // 写入异动记录
                        AsSyncChange syncChange = new AsSyncChange();
                        AsSyncChange dingSyncChange = syncChange.setResId(one.getId()).setCompanyId(companyId).setFromOrg(new ArrayList<>()).setToOrg(new ArrayList<>()).setType(3);
                        syncChangeService.save(dingSyncChange);
                    }
                }
            }
            return true;
        } else {
            log.error("companyId = {}, check token error", companyId);
            return false;
        }
    }

}
