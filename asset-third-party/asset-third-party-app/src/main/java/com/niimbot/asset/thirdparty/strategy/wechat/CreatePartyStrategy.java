package com.niimbot.asset.thirdparty.strategy.wechat;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.WeChatCallbackEvent;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;

/**
 * <AUTHOR>
 * @since 2021/11/19 10:27
 */
@Slf4j
@Component
@CallbackStrategy(eventType = WeChatCallbackEvent.CREATE_PARTY)
public class CreatePartyStrategy implements WeChatCallbackStrategy {

    private final OrgService orgService;

    public CreatePartyStrategy(OrgService orgService) {
        this.orgService = orgService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, WxCpTpXmlMessage xmlMessage) {
        if (log.isDebugEnabled()) {
            // org_dept_create: {"CorpId":"ding8b2d6adfb93a210f35c2f4657eb6378f","EventType":"org_dept_create","DeptId":[*********],"TimeStamp":"1637114921920"}
            log.debug("create_party: " + xmlMessage);
        }
        AsOrg pOrg = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                .eq(AsOrg::getCompanyId, companyId)
                .eq(AsOrg::getExternalOrgId, Convert.toStr(xmlMessage.getParentId())));
        // 查询当前节点
        AsOrg org = new AsOrg();
        org.setId(IdUtils.getId());
        org.setExternalOrgId(Convert.toStr(xmlMessage.getId()));
        org.setOrgName(xmlMessage.getName());
        if (ObjectUtil.isNotNull(pOrg)) {
            org.setLevel(pOrg.getLevel() + 1);
            org.setPaths(pOrg.getPaths() + pOrg.getId() + ",");
            org.setPid(pOrg.getId());
            org.setExternalPid(pOrg.getExternalOrgId());
            // 查询该部门公司的所属公司
            List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
            Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
            org.setCompanyOwner(orgCompanyOwner);
        } else {
            org.setLevel(0);
            org.setPaths("0,");
            org.setPid(0L);
            org.setExternalPid("0");
            org.setCompanyOwner(org.getId());
        }
        org.setCompanyId(companyId);
        org.setOrgCode(Convert.toStr(xmlMessage.getId()));
        orgService.save(org);

        return true;
    }

}
