package com.niimbot.asset.thirdparty.strategy.others;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.event.OthersCallbackEvent;
import com.niimbot.asset.thirdparty.event.UserCO;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Since 2024-10-14
 */
@Slf4j
@Component(value = "othersUserDeleteStrategy")
@CallbackStrategy(eventType = OthersCallbackEvent.USER_DELETE)
public class UserDeleteStrategy extends AbsUserStrategy implements OthersCallbackStrategy {

    @Override
    public boolean handleCallback(Long companyId, JSONObject content) {
        UserCO userCO = content.toJavaObject(UserCO.class);
        if (userCO != null) {
            deleteUser(userCO.getId(), companyId, getType());
            return true;
        } else {
            log.error("companyId = {}, userCO is null", companyId);
            return false;
        }
    }

}
