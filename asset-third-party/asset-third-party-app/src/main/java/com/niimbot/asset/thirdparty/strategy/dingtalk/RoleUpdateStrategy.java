package com.niimbot.asset.thirdparty.strategy.dingtalk;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.activiti.model.ActApproveRole;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.thirdparty.support.DingApiSupport;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.LABEL_CONF_MODIFY)
public class RoleUpdateStrategy implements DingCallbackStrategy {

    @Resource
    private CompanySettingService companySettingService;

    @Resource
    private DingApiSupport dingApiSupport;

    @Resource
    private ActApproveRoleService approveRoleService;

    @Override
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        CompanySwitch setting = companySettingService.getSwitchSettingWithCache(companyId);
        if (!setting.getEnableSyncApproveRole()) {
            return true;
        }
        if (!content.containsKey("LabelIdList")) {
            return true;
        }
        List<Long> extIds = content.getJSONArray("LabelIdList").toJavaList(Long.class);
        List<DingApiSupport.DingRole> roles = dingApiSupport.getRoles(companyId, extIds);
        roles.forEach(v -> approveRoleService.update(
                Wrappers.lambdaUpdate(ActApproveRole.class)
                        .set(ActApproveRole::getName, v.getName())
                        .eq(ActApproveRole::getExternalId, String.valueOf(v.getId()))
        ));
        return true;
    }
}
