package com.niimbot.asset.thirdparty.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.Tuple3;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.asset.thirdparty.model.AsThirdParty;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusEmployeeTransferDto;
import com.niimbot.system.DataTransfer;
import com.niimbot.system.RemoveEmployDto;
import com.niimbot.thirdparty.AssetThirdPartyEmpMapping;
import com.niimbot.thirdparty.AssetThirdPartyOrgMapping;
import com.niimbot.thirdparty.OrgTransferDto;
import com.niimbot.thirdparty.TransMismatchEmp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/10 10:40
 */
@Slf4j
public abstract class AbsOrgStructureService implements OrgStructureService {

    @Autowired
    protected CompanyService companyService;

    @Autowired
    private AsAccountEmployeeService accountEmployeeService;

    @Autowired
    protected OrgService orgService;

    @Autowired
    private CusAccountService accountService;

    @Autowired
    protected AsCusEmployeeService employeeService;

    @Autowired
    private CusUserCompanyService cusUserCompanyService;

    @Autowired
    private AsUserOrgService userOrgService;

    @Autowired
    private CompanyAssetService companyAssetService;

    @Autowired
    private SyncChangeService syncChangeService;

    @Autowired
    private AsCusEmployeeExtService employeeExtService;

    @Autowired
    private AsCusEmployeeSettingService employeeSettingService;

    @Autowired
    private CusUserRoleService userRoleService;

    @Autowired
    private RedisService redisService;

    @Autowired
    protected AsThirdPartyService thirdPartyService;

    @Resource
    protected AsThirdPartyEmployeeService thirdPartyEmployeeService;

    @Autowired
    private UserPreviewDeleteService userPreviewDeleteService;

    @Autowired
    private AsDataPermissionService dataPermissionService;

    @Autowired
    private MessageService messageService;
    @Autowired
    private ModelDataScopeServiceImpl modelDataScopeService;
    @Autowired
    private CusUserService cusUserService;

    public static final String ADD = "add";
    public static final String UPDATE = "update";
    public static final String DELETE = "delete";
    public static final String TRANS = "trans";
    public static final String ASSET = "asset";
    public static final String THIRD = "third";
    public static final String ORG_HANDLE_FINISH = "org_handle_finish";
    public static final long CACHE_EXPIRED = TimeUnit.HOURS.toSeconds(24L);

    @Override
    public void writeSyncStep(Long companyId, String step) {
        String cacheKey = ThirdPartyConstant.getSyncStepCacheKey(getType(), companyId) + "step";
        redisService.set(cacheKey, step, CACHE_EXPIRED);
    }

    @Override
    public String readSyncStep(Long companyId) {
        Object step = redisService.get(ThirdPartyConstant.getSyncStepCacheKey(getType(), companyId) + "step");
        if (Objects.isNull(step)) {
            return "";
        }
        return String.valueOf(step);
    }

    @Override
    public void cleanCache(Long companyId) {
        String type = getType();
        // 1.步骤
        redisService.deleteKeysByScan(ThirdPartyConstant.getSyncStepCacheKey(type, companyId) + "*");

        // 2.getOrgMapping
        redisService.deleteKeysByScan(ThirdPartyConstant.getOrgMappingCacheKey(type, companyId) + "*");
        // 3.handOrgMapping
        // 4.getEmpMapping
        redisService.deleteKeysByScan(ThirdPartyConstant.getEmpMappingCacheKey(type, companyId) + "*");
    }

    @Override
    public List<AsOrg> loadAssetOrg(Long companyId) {
        return orgService.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId)
        );
    }

    @Override
    public AsOrg getAssetRootOrg(Long companyId) {
        // 获取系统组织根节点
        List<AsOrg> systemRootOrg = this.orgService.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getOrgType, 1)
                        .orderByAsc(AsOrg::getLevel)
                        .last("LIMIT 1")
        );
        if (CollUtil.isEmpty(systemRootOrg)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "企业组织根节点不存在");
        }
        return systemRootOrg.get(0);
    }

    @Override
    public AssetThirdPartyOrgMapping getOrgMapping(Long companyId) {
//        JSONObject secret = getSecret(companyId, getType());
//        if (secret == null || secret.isEmpty()) {
//            throw new BusinessException(ThirdPartyResultCode.THIRD_PARTY_NOT_BIND, ThirdPartyConstant.getType(getType()));
//        }
        List<AsOrg> assetOrgs = loadAssetOrg(companyId);
        List<AsOrg> thirdPartyOrgs = loadThirdPartyOrg(companyId);
        String extRootPid = ThirdPartyConstant.FEISHU.equals(getType()) ? "-1" : "0";
        // 初次绑定已完成且本地同步没有根节点
        if (thirdPartyOrgs.stream().noneMatch(v -> v.getOrgType() == 1 && "0".equals(v.getExternalPid()))) {
            AsOrg rootOrg = orgService.getRootOrg(companyId);
            // 三方根节点（飞书和钉钉企微不一样）
            rootOrg.setExternalOrgId(ThirdPartyConstant.FEISHU.equals(getType()) ? "0" : "1");
            // 三方父节点（飞书和钉钉企微不一样）
            rootOrg.setExternalPid(extRootPid);
            thirdPartyOrgs.add(rootOrg);
        }
        AssetThirdPartyOrgMapping mapping = new AssetThirdPartyOrgMapping();
        // 转树
        List<Tree<String>> assetOrgTree = TreeUtil.build(assetOrgs, "0", (org, treeNode) -> {
            treeNode.setId(Convert.toStr(org.getId()));
            treeNode.setParentId(Convert.toStr(org.getPid()));
            treeNode.setName(org.getOrgName());
            treeNode.setWeight(org.getSortNum());
        });
        List<Tree<String>> thirdPartyOrgTree = TreeUtil.build(thirdPartyOrgs, extRootPid, (org, treeNode) -> {
            treeNode.setId(org.getExternalOrgId());
            treeNode.setParentId(org.getExternalPid());
            treeNode.setName(org.getOrgName());
            treeNode.setWeight(org.getSortNum());
        });
        mapping.setAssetOrgTree(assetOrgTree).setThirdPartyOrgTree(thirdPartyOrgTree);
        // 根据规则处理映射
        List<AssetThirdPartyOrgMapping.Mapping> mappings = new ArrayList<>();
        AsOrg thirdPartyRootOrg = thirdPartyOrgs.stream().filter(org -> org.getOrgType() == 1 && extRootPid.equals(org.getExternalPid())).findFirst().orElseThrow(() -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请将根节点设置为可见"));
        Map<String, List<AsOrg>> assetOrgNameMap = assetOrgs.stream().collect(Collectors.groupingBy(AsOrg::getOrgName));
        Map<String, List<AsOrg>> thirdPartyOrgNameMap = thirdPartyOrgs.stream().filter(v -> StrUtil.isNotBlank(v.getOrgName())).collect(Collectors.groupingBy(AsOrg::getOrgName));
        Map<String, AsOrg> thirdPartyOrgIdMap = thirdPartyOrgs.stream().filter(v -> StrUtil.isNotBlank(v.getExternalOrgId())).collect(Collectors.toMap(AsOrg::getExternalOrgId, v -> v));
        assetOrgs.forEach(assetOrg -> {
            // 根节点强关联
            if (assetOrg.getOrgType() == 1 && assetOrg.getPid() == 0) {
                mappings.add(new AssetThirdPartyOrgMapping.Mapping(assetOrg.getId(), thirdPartyRootOrg.getExternalOrgId()));
                return;
            }
            // 已经关联过
            if (thirdPartyOrgIdMap.containsKey(assetOrg.getExternalOrgId())) {
                mappings.add(new AssetThirdPartyOrgMapping.Mapping(assetOrg.getId(), thirdPartyOrgIdMap.get(assetOrg.getExternalOrgId()).getExternalOrgId()));
                return;
            }
            // 名称相同
            List<AsOrg> asset = assetOrgNameMap.get(assetOrg.getOrgName());
            List<AsOrg> third = thirdPartyOrgNameMap.get(assetOrg.getOrgName());
            if (CollUtil.isEmpty(asset) || CollUtil.isEmpty(third)) {
                return;
            }
            // 不重复且只有一个同名则自动映射
            if (asset.size() == 1 && third.size() == 1) {
                mappings.add(new AssetThirdPartyOrgMapping.Mapping(assetOrg.getId(), third.get(0).getExternalOrgId()));
                return;
            }
            // 重复只有同一层级才自动映射
            Integer level = assetOrg.getLevel();
            third.stream()
                    .filter(v -> level.equals(v.getLevel()))
                    .findFirst()
                    .ifPresent(org -> mappings.add(new AssetThirdPartyOrgMapping.Mapping(assetOrg.getId(), org.getExternalOrgId())));
        });
        mapping.setMappings(mappings);
        // 缓存
        writeOrgCache(companyId, ASSET, assetOrgs);
        writeOrgCache(companyId, THIRD, thirdPartyOrgs);
        writeSyncStep(companyId, ThirdPartyConstant.GET_ORG_MAPPING);
        String valueCacheKey = ThirdPartyConstant.getSyncStepCacheKey(getType(), companyId) + ThirdPartyConstant.GET_ORG_MAPPING;
        redisService.set(valueCacheKey + ":" + ASSET, JSONObject.toJSONString(assetOrgTree), CACHE_EXPIRED);
        redisService.set(valueCacheKey + ":" + THIRD, JSONObject.toJSONString(thirdPartyOrgTree), CACHE_EXPIRED);
        redisService.set(valueCacheKey + ":mapping", JSONObject.toJSONString(mappings), CACHE_EXPIRED);
        return mapping;
    }

    @Override
    public Boolean handleOrgMapping(List<AssetThirdPartyOrgMapping.Mapping> mappings) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        if (!ThirdPartyConstant.STEP.contains(readSyncStep(companyId))) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先拉取组织架构信息");
        }
        List<AsOrg> asset = readOrgsCache(companyId, ASSET);
        List<AsOrg> third = readOrgsCache(companyId, THIRD);
        String assetOrgIdsToString = mappings.stream().map(AssetThirdPartyOrgMapping.Mapping::getAssetOrgId).sorted(Long::compare).map(String::valueOf).collect(Collectors.joining(","));
        String existIdsToString = asset.stream().map(AsOrg::getId).sorted(Long::compare).map(String::valueOf).collect(Collectors.joining(","));
        if (!assetOrgIdsToString.equals(existIdsToString) && !thirdPartyService.getThirdPartyInitSync(companyId, getType())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先关联所有数据");
        }
        ConcurrentMap<Long, AsOrg> assetMap = asset.stream().collect(Collectors.toConcurrentMap(AsOrg::getId, v -> v));
        ConcurrentMap<String, AsOrg> thirdMap = third.stream().collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, v -> v));
        // 第三方组织ID没有对应的映射关系，代表是一定要新增的组织
        Set<String> thirdPartySelectIds = mappings.stream().map(AssetThirdPartyOrgMapping.Mapping::getThirdPartyOrgId).collect(Collectors.toSet());
        List<AsOrg> orgsForAdd = third.stream()
                .filter(v -> !thirdPartySelectIds.contains(v.getExternalOrgId()))
                .peek(v -> {
                    v.setId(IdUtils.getId());
                    // 飞书的编码不同步
                    if (!ThirdPartyConstant.FEISHU.equals(getType())) {
                        v.setOrgCode(v.getExternalOrgId());
                    }
                    v.setRemark(ADD);
                }).collect(Collectors.toList());
        // 第三方组织ID有对应的映射关系，代表映射的云资产组织一定存在这个时候只用做更新或合并组织
        ConcurrentMap<String, List<Long>> thirdOrgIdGroup = mappings.stream().collect(Collectors.groupingByConcurrent(AssetThirdPartyOrgMapping.Mapping::getThirdPartyOrgId, Collectors.mapping(AssetThirdPartyOrgMapping.Mapping::getAssetOrgId, Collectors.toList())));
        Set<Long> processed = new HashSet<>();
        List<AsOrg> orgsForUpdate = new ArrayList<>();
        List<AsOrg> transfers = new ArrayList<>();
        mappings.forEach(mapping -> {
            Long assetOrgId = mapping.getAssetOrgId();
            String thirdPartyOrgId = mapping.getThirdPartyOrgId();
            // 数据异常
            if (!assetMap.containsKey(assetOrgId) || !thirdMap.containsKey(thirdPartyOrgId)) {
                return;
            }
            // 已处理过
            if (processed.contains(assetOrgId)) {
                return;
            }
            AsOrg assetOrg = assetMap.get(assetOrgId);
            AsOrg thirdPartyOrg = thirdMap.get(thirdPartyOrgId);
            List<Long> selectAssetOrgIds = thirdOrgIdGroup.get(thirdPartyOrgId);
            // 更新云资产组织信息
            if (selectAssetOrgIds.size() == 1) {
                toAssetOrg(assetOrg, thirdPartyOrg, processed, orgsForUpdate);
                return;
            }
            // 第三方组织ID对应多个云资产组织ID：先找名称相同组织，如果没有名称相同组织就取第一个。
            List<AsOrg> reverseElection = asset.stream().filter(v -> selectAssetOrgIds.contains(v.getId())).collect(Collectors.toList());
            AsOrg merge = reverseElection.stream().filter(v -> thirdPartyOrg.getOrgName().equals(v.getOrgName())).findFirst().orElseGet(() -> reverseElection.get(0));
            toAssetOrg(merge, thirdPartyOrg, processed, orgsForUpdate);
            // 标记其他要合并的
            List<AsOrg> trans = reverseElection.stream()
                    .filter(v -> !v.getId().equals(merge.getId()))
                    .peek(v -> processed.add(v.getId()))
                    .map(org -> {
                        AsOrg asOrg = new AsOrg();
                        asOrg.setId(org.getId());
                        asOrg.setPid(merge.getId());
                        asOrg.setCompanyOwner(org.getCompanyOwner());
                        asOrg.setOrgName(org.getOrgName());
                        asOrg.setPaths(org.getPaths());
                        asOrg.setRemark(TRANS);
                        return asOrg;
                    }).collect(Collectors.toList());
            transfers.addAll(trans);
        });
        orgsForAdd.addAll(orgsForUpdate);
        List<AsOrg> finish = handleOrgTree(orgsForAdd);
        finish.addAll(transfers);
        // 缓存
        writeOrgCache(companyId, ORG_HANDLE_FINISH, finish);
        writeSyncStep(companyId, ThirdPartyConstant.HANDLE_ORG_MAPPING);
        String valueCacheKey = ThirdPartyConstant.getSyncStepCacheKey(getType(), companyId) + ThirdPartyConstant.HANDLE_ORG_MAPPING;
        redisService.set(valueCacheKey, JSONObject.toJSONString(mappings), CACHE_EXPIRED);
        return true;
    }

    private void toAssetOrg(AsOrg assetOrg, AsOrg thirdPartyOrg, Set<Long> processed, List<AsOrg> orgsForUpdate) {
        assetOrg.setOrgName(thirdPartyOrg.getOrgName())
                .setExternalOrgId(thirdPartyOrg.getExternalOrgId())
                .setExternalPid(thirdPartyOrg.getExternalPid())
                .setExternalDirector(thirdPartyOrg.getExternalDirector())
                .setSortNum(thirdPartyOrg.getSortNum())
                .setCompanyOwner(thirdPartyOrg.getCompanyOwner())
                .setRemark(UPDATE);
        // 飞书的编码不同步
        if (!ThirdPartyConstant.FEISHU.equals(getType())) {
            assetOrg.setOrgCode(thirdPartyOrg.getExternalOrgId());
        }
        processed.add(assetOrg.getId());
        orgsForUpdate.add(assetOrg);
    }

    private List<AsOrg> handleOrgTree(List<AsOrg> orgs) {
        String extRootPid = ThirdPartyConstant.FEISHU.equals(getType()) ? "-1" : "0";
        List<Tree<String>> trees = TreeUtil.build(orgs, extRootPid, (org, treeNode) -> {
            treeNode.setId(org.getExternalOrgId());
            treeNode.setParentId(org.getExternalPid());
            treeNode.setWeight(org.getSortNum());
        });
        Map<String, AsOrg> idMap = orgs.stream().collect(Collectors.toMap(AsOrg::getExternalOrgId, v -> v));
        trees.forEach(node -> treeStructure(idMap, node, new Relation(0, "0,", 0L)));
        return new ArrayList<>(idMap.values());
    }

    private void treeStructure(Map<String, AsOrg> idMap, Tree<String> node, Relation relation) {
        String id = node.getId();
        AsOrg oneself = idMap.get(id);
        oneself.setPid(relation.pid);
        oneself.setLevel(relation.level);
        oneself.setPaths(relation.paths);
        List<Tree<String>> children = node.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            Relation sonRelation = new Relation(relation.level + 1, relation.paths + oneself.getId() + ",", oneself.getId());
            children.forEach(son -> treeStructure(idMap, son, sonRelation));
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Relation {

        private Integer level;

        private String paths;

        private Long pid;

    }

    private void writeOrgCache(Long companyId, String key, List<AsOrg> orgs) {
        String cacheKey = ThirdPartyConstant.getOrgMappingCacheKey(getType(), companyId) + key;
        redisService.deleteKeysByScan(cacheKey);
        orgs.forEach(v -> redisService.lPush(cacheKey, v));
        redisService.expire(cacheKey, CACHE_EXPIRED);
    }

    private List<AsOrg> readOrgsCache(Long companyId, String key) {
        String cacheKey = ThirdPartyConstant.getOrgMappingCacheKey(getType(), companyId) + key;
        List<Object> objects = redisService.lRange(cacheKey, 0L, -1L);
        if (CollUtil.isEmpty(objects)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先处理组织同步映射");
        }
        return objects.stream().map(o -> (AsOrg) o).collect(Collectors.toList());
    }

    @Override
    public List<AsCusEmployee> loadAssetEmp(Long companyId) {
        Map<Long, AsThirdPartyEmployee> partyEmployeeMap = thirdPartyEmployeeService.listByCompanyId(companyId, getType()).stream().collect(Collectors.toMap(AsThirdPartyEmployee::getEmployeeId, v -> v));
        List<AsCusEmployee> employees = employeeService.list(Wrappers.lambdaQuery(AsCusEmployee.class).eq(AsCusEmployee::getCompanyId, companyId));
        employees.forEach(emp -> {
            if (!partyEmployeeMap.containsKey(emp.getId())) {
                return;
            }
            AsThirdPartyEmployee thirdPartyEmployee = partyEmployeeMap.get(emp.getId());
            if (StrUtil.isBlank(thirdPartyEmployee.getUserId())) {
                return;
            }
            // userId填充到Remark，id填充到updateBy 做为临时变量
            emp.setRemark(thirdPartyEmployee.getUserId()).setUpdateBy(thirdPartyEmployee.getId());
        });
        return employees;
    }

    @Override
    public AssetThirdPartyEmpMapping getEmpMapping(Long companyId) {
        if (!ThirdPartyConstant.STEP.contains(readSyncStep(companyId))) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先处理组织架构映射关系");
        }
        // 1.拉取已同步员工数据
        List<AsCusEmployee> employees = loadAssetEmp(companyId);
        // 2.拉取第三方员工数据
        List<AsOrg> orgs = readOrgsCache(companyId, ORG_HANDLE_FINISH);
        List<AsOrg> orgRaw = readOrgsCache(companyId, ASSET);
        Map<Long, String> rawOrgNameMap = orgRaw.stream().filter(v -> StrUtil.isNotBlank(v.getOrgName())).collect(Collectors.toConcurrentMap(AsOrg::getId, AsOrg::getOrgName));
        Map<Long, String> rawOrgPathMap = orgRaw.stream().collect(Collectors.toConcurrentMap(AsOrg::getId, AsOrg::getPaths));
        ConcurrentMap<String, AsOrg> orgMap = orgs.stream().filter(v -> !DELETE.equals(v.getRemark())).filter(v -> StrUtil.isNotBlank(v.getExternalOrgId())).collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, v -> v));
        AsOrg rootOrg = orgMap.get("2");
        List<ThirdPartyEmp> thirdPartyEmps = loadThirdPartyEmp(companyId, orgs.stream().filter(v -> !(v.getRemark().equals(DELETE) || v.getRemark().equals(TRANS)) && StrUtil.isNotBlank(v.getExternalOrgId())).map(AsOrg::getExternalOrgId).collect(Collectors.toList()));
        // userId去重
        thirdPartyEmps = thirdPartyEmps.stream().distinct().collect(Collectors.toList());
        // 3.按规则匹配员工
        Map<String, ThirdPartyEmp> userIdMap = new ConcurrentHashMap<>();
        Map<String, ThirdPartyEmp> nameMap = new ConcurrentHashMap<>();
        Map<String, ThirdPartyEmp> mobileMap = new ConcurrentHashMap<>();
        Map<String, ThirdPartyEmp> emailMap = new ConcurrentHashMap<>();
        thirdPartyEmps.forEach(data -> {
            if (StrUtil.isNotBlank(data.getThirdPartyId())) {
                userIdMap.put(data.getThirdPartyId(), data);
            }
            if (StrUtil.isNotBlank(data.getName())) {
                nameMap.put(data.getName(), data);
            }
            if (StrUtil.isNotBlank(data.getMobile())) {
                mobileMap.put(data.getMobile(), data);
            }
            if (StrUtil.isNotBlank(data.getEmail())) {
                emailMap.put(data.getEmail(), data);
            }
        });
        // 记录员工与第三方员工关系，云资产员工组织关系
        Map<String, Long> existUserIdMap = employees.stream().filter(v -> StrUtil.isNotBlank(v.getRemark())).collect(Collectors.toConcurrentMap(AsCusEmployee::getRemark, AsCusEmployee::getUpdateBy));
        // 云资产与第三方中同名的员工
        ConcurrentMap<String, Long> assetEmpNameGroup = employees.stream().collect(Collectors.groupingByConcurrent(AsCusEmployee::getEmpName, Collectors.counting()));
        ConcurrentMap<String, Long> thirdEmpNameGroup = thirdPartyEmps.stream().collect(Collectors.groupingByConcurrent(ThirdPartyEmp::getName, Collectors.counting()));
        List<AssetThirdPartyEmpMapping.Emp> mismatch = new ArrayList<>();
        Map<String, AsCusEmployee> empReady = new ConcurrentHashMap<>();
        List<AsCusEmployee> empDeleteReady = new ArrayList<>();
        Map<Long, AsCusEmployee> toBeDeletedEmps = new HashMap<>(6);
        employees.forEach(emp -> {
            // userId || mobile || email || name
            ThirdPartyEmp thirdPartyEmp = userIdMap.get(emp.getRemark());
            if (Objects.isNull(thirdPartyEmp)) {
                thirdPartyEmp = mobileMap.get(emp.getMobile());
            }
            if (Objects.isNull(thirdPartyEmp)) {
                thirdPartyEmp = emailMap.get(emp.getEmail());
            }
            if (Objects.isNull(thirdPartyEmp)) {
                thirdPartyEmp = nameMap.get(emp.getEmpName());
            }
            // 已匹配特殊
            if (Objects.nonNull(thirdPartyEmp) && empReady.containsKey(thirdPartyEmp.getThirdPartyId())) {
                AsCusEmployee tobe = empReady.get(thirdPartyEmp.getThirdPartyId());
                toBeDeletedEmps.put(tobe.getId(), tobe);
                return;
            }
            // 未匹配上或者员工姓名在云资产或第三方中重复
            String remark = emp.getRemark();
            boolean flag = !userIdMap.containsKey(remark) && (assetEmpNameGroup.get(emp.getEmpName()) > 1 || (thirdEmpNameGroup.containsKey(emp.getEmpName()) && thirdEmpNameGroup.get(emp.getEmpName()) > 1));
            if (Objects.isNull(thirdPartyEmp) || flag) {
                handleMismatchEmp(companyId, emp, mismatch, empDeleteReady, rawOrgNameMap, rawOrgPathMap);
                return;
            }
            empReady.put(thirdPartyEmp.getThirdPartyId(), toAssetEmployee(thirdPartyEmp, emp, companyId).setRemark(UPDATE));
        });
        // 移除待删除的
        if (CollUtil.isNotEmpty(toBeDeletedEmps)) {
            empReady.values().removeIf(v -> toBeDeletedEmps.containsKey(v.getId()));
            toBeDeletedEmps.values().forEach(emp -> handleMismatchEmp(companyId, emp, mismatch, empDeleteReady, rawOrgNameMap, rawOrgPathMap));
        }
        Set<String> userIdForUpdate = empReady.keySet();
        thirdPartyEmps.forEach(data -> {
            if (!userIdForUpdate.contains(data.getThirdPartyId())) {
                empReady.put(data.getThirdPartyId(), toAssetEmployee(data, new AsCusEmployee().setId(IdUtils.getId()), companyId).setRemark(ADD));
            }
        });
        List<AsThirdPartyEmployee> thirdPartyEmpReady = new ArrayList<>();
        List<AsUserOrg> userOrgsReady = new ArrayList<>();
        List<AssetThirdPartyEmpMapping.Emp> all = new ArrayList<>(thirdPartyEmps.size());
        empReady.forEach((k, v) -> {
            thirdPartyEmpReady.add(new AsThirdPartyEmployee().setCompanyId(companyId).setEmployeeId(v.getId()).setUserId(k).setType(getType()).setId(existUserIdMap.get(k)));
            Set<String> orgIds = new HashSet<>(userIdMap.get(k).getDeptIdList());
            List<AsUserOrg> userOrgs = orgIds.stream().filter(orgMap::containsKey).map(extId -> new AsUserOrg(v.getId(), orgMap.get(extId).getId())).collect(Collectors.toList());
            // 挂到根部门
            if (CollUtil.isEmpty(userOrgs)) {
                userOrgs.add(new AsUserOrg(v.getId(), rootOrg.getId()));
            }
            userOrgsReady.addAll(userOrgs);
            AssetThirdPartyEmpMapping.Emp emp = new AssetThirdPartyEmpMapping.Emp(v.getId(), v.getEmpName(), v.getMobile()).setEmpNo(v.getEmpNo());
            List<String> orgNames = orgIds.stream().map(id -> orgMap.containsKey(id) ? orgMap.get(id).getOrgName() : "").collect(Collectors.toList());
            emp.setOrgNames(orgNames);
            all.add(emp);
        });
        // 回写部门主管ID
        orgs.forEach(org -> {
            if (TRANS.equals(org.getRemark()) || DELETE.equals(org.getRemark()) || CollUtil.isEmpty(org.getExternalDirector())) {
                return;
            }
            List<Long> empIds = new ArrayList<>();
            for (String extDir : org.getExternalDirector()) {
                if (empReady.containsKey(extDir)) {
                    Long id = empReady.get(extDir).getId();
                    empIds.add(id);
                }
            }
            org.setDirector(empIds);
        });
        AssetThirdPartyEmpMapping mapping = new AssetThirdPartyEmpMapping(
                all,
                mismatch
        );
        // 缓存
        List<AsCusEmployee> ready = new ArrayList<>(empReady.values());
        ready.addAll(empDeleteReady);
        writeOrgCache(companyId, ORG_HANDLE_FINISH, orgs);
        writeEmpCache(companyId, ready, thirdPartyEmpReady, userOrgsReady);
        writeSyncStep(companyId, ThirdPartyConstant.GET_EMP_MAPPING);
        String valueCacheKey = ThirdPartyConstant.getSyncStepCacheKey(getType(), companyId) + ThirdPartyConstant.GET_EMP_MAPPING;
        redisService.set(valueCacheKey + ":all", JSONObject.toJSONString(mapping.getAll()), CACHE_EXPIRED);
        redisService.set(valueCacheKey + ":mismatch", JSONObject.toJSONString(mapping.getMismatch()), CACHE_EXPIRED);
        // 4.返回未匹配且有管理或使用资产的员工
        return mapping;
    }

    private AsCusEmployee toAssetEmployee(ThirdPartyEmp thirdPartyEmp, AsCusEmployee emp, Long companyId) {
        emp.setCompanyId(companyId)
                .setEmpName(thirdPartyEmp.getName())
                .setEmpNo(thirdPartyEmp.getJobNum())
                .setPosition(thirdPartyEmp.getPosition());
        if (StrUtil.isNotEmpty(thirdPartyEmp.getMobile())) {
            emp.setMobile(thirdPartyEmp.getMobile());
        }
        if (StrUtil.isNotEmpty(thirdPartyEmp.getEmail())) {
            emp.setEmail(thirdPartyEmp.getEmail());
        }
        return emp;
    }

    private void handleMismatchEmp(Long companyId, AsCusEmployee emp, List<AssetThirdPartyEmpMapping.Emp> mismatch, List<AsCusEmployee> empDeleteReady, Map<Long, String> rawOrgNameMap, Map<Long, String> rawOrgPathMap) {
        AssetThirdPartyEmpMapping.Emp miss = new AssetThirdPartyEmpMapping.Emp(emp.getId(), emp.getEmpName(), emp.getMobile());
        miss.setTransUseAsset(userPreviewDeleteService.hasUseAsset(emp.getId(), companyId));
        miss.setTransManageAsset(userPreviewDeleteService.hasManageAsset(emp.getId(), companyId));
        miss.setTransApproval(userPreviewDeleteService.hasApproval(emp.getId(), companyId));
        miss.setEntMatTask(userPreviewDeleteService.hasEntMatTask(emp.getId(), companyId));
        // 无业务数据转移
        if (!miss.isTransUseAsset() && !miss.isTransManageAsset() && !miss.isTransApproval() && !miss.isEntMatTask()) {
            empDeleteReady.add(new AsCusEmployee().setId(emp.getId()).setCompanyId(emp.getCompanyId()).setRemark(DELETE));
            return;
        }
        List<AsUserOrg> userOrgs = userOrgService.list(Wrappers.lambdaQuery(AsUserOrg.class).eq(AsUserOrg::getUserId, emp.getId()));
        List<String> paths = userOrgs.stream()
                .map(id -> (rawOrgPathMap.getOrDefault(id.getOrgId(), ",") + id.getOrgId()))
                .collect(Collectors.toList());
        List<String> path = paths.stream().map(v -> Arrays.stream(v.split(","))
                        .map(Convert::toLong)
                        .sorted(Comparator.reverseOrder())
                        .filter(t -> t != 0L)
                        .map(id -> rawOrgNameMap.getOrDefault(id, ""))
                        .collect(Collectors.joining("/")))
                .collect(Collectors.toList());
        miss.setOrgPathName(path);
        mismatch.add(miss);
    }

    private void writeEmpCache(Long companyId, List<AsCusEmployee> employees, List<AsThirdPartyEmployee> thirdPartyEmployees, List<AsUserOrg> userOrgs) {
        String cacheKey = ThirdPartyConstant.getEmpMappingCacheKey(getType(), companyId);
        String c1 = cacheKey + "emp_handle_finish";
        String c2 = cacheKey + "third_emp_handle_finish";
        String c3 = cacheKey + "emp_org_handle_finish";
        redisService.deleteKeysByScan(cacheKey + "*");
        employees.forEach(v -> redisService.lPush(c1, v));
        thirdPartyEmployees.forEach(v -> redisService.lPush(c2, v));
        userOrgs.forEach(v -> redisService.lPush(c3, v));
        redisService.expire(c1, CACHE_EXPIRED);
        redisService.expire(c2, CACHE_EXPIRED);
        redisService.expire(c3, CACHE_EXPIRED);
    }

    private Tuple3<List<AsCusEmployee>, List<AsThirdPartyEmployee>, List<AsUserOrg>> readEmpCache(Long companyId) {
        String cacheKey = ThirdPartyConstant.getEmpMappingCacheKey(getType(), companyId);
        List<Object> employees = redisService.lRange(cacheKey + "emp_handle_finish", 0L, -1L);
        List<Object> thirdEmployees = redisService.lRange(cacheKey + "third_emp_handle_finish", 0L, -1L);
        List<Object> empOrgs = redisService.lRange(cacheKey + "emp_org_handle_finish", 0L, -1L);
        BusinessException exception = new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先处理员工同步映射");
        if (CollUtil.isEmpty(employees)) {
            log.error("List<AsCusEmployee> Is Empty");
            throw exception;
        }
        if (CollUtil.isEmpty(thirdEmployees)) {
            log.error("List<AsThirdPartyEmployee> Is Empty");
            throw exception;
        }
        if (CollUtil.isEmpty(empOrgs)) {
            log.error("List<AsUserOrg> Is Empty");
            throw exception;
        }
        return new Tuple3<>(
                employees.stream().map(o -> (AsCusEmployee) o).collect(Collectors.toList()),
                thirdEmployees.stream().map(o -> (AsThirdPartyEmployee) o).collect(Collectors.toList()),
                empOrgs.stream().map(o -> (AsUserOrg) o).collect(Collectors.toList())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handle(List<TransMismatchEmp> transMismatchEmps) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        if (!ThirdPartyConstant.STEP.contains(readSyncStep(companyId))) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先处理员工同步映射");
        }
        AtomicBoolean empChange = new AtomicBoolean(false);
        // 1.读缓存
        List<AsOrg> orgs = readOrgsCache(companyId, ORG_HANDLE_FINISH);
        Tuple3<List<AsCusEmployee>, List<AsThirdPartyEmployee>, List<AsUserOrg>> tuple3 = readEmpCache(companyId);
        // 2.处理组织
        List<AsOrg> orgForAdd = new ArrayList<>();
        List<AsOrg> orgForUpdate = new ArrayList<>();
        List<AsOrg> orgForTrans = new ArrayList<>();
        List<AsOrg> orgForDelete = new ArrayList<>();
        orgs.forEach(v -> {
            if (ADD.equals(v.getRemark())) {
                orgForAdd.add(v.setRemark(null));
            }
            if (UPDATE.equals(v.getRemark())) {
                orgForUpdate.add(v.setRemark(null));
            }
            if (TRANS.equals(v.getRemark())) {
                orgForTrans.add(v.setRemark(null));
            }
            if (DELETE.equals(v.getRemark())) {
                orgForDelete.add(v.setRemark(null));
            }
        });
        orgService.saveBatch(orgForAdd);
        orgService.updateBatchById(orgForUpdate);
        // 更新企业名称为组织根节点
        orgForUpdate.stream()
                .filter(v -> v.getPid() == 0 && v.getOrgType() == 1)
                .findFirst()
                .ifPresent(org -> companyService.update(Wrappers.lambdaUpdate(AsCompany.class).set(AsCompany::getName, org.getOrgName()).eq(AsCompany::getId, companyId)));
        // 仓库与区域转移
        orgForTrans.forEach(v -> {
            companyAssetService.updateAreaOrgId(v.getId(), v.getPid());
            companyAssetService.updateRepositoryOrgId(v.getId(), v.getPid());
        });
        // 删除匹配不上的组织转移资产生成异动记录
        Map<Long, Long> orgTransMap = orgForTrans.stream().collect(Collectors.toMap(AsOrg::getId, AsOrg::getPid));
        Set<Long> orgDeleteIds = orgForDelete.stream().map(AsOrg::getId).collect(Collectors.toSet());
        ArrayList<Long> checkOrgIds = new ArrayList<>(orgTransMap.keySet());
        checkOrgIds.addAll(orgDeleteIds);
        Set<Long> removeReadyIds = checkOrgAsset(checkOrgIds, companyId);
        orgService.removeByIds(checkOrgIds);
        loadOrgCache(companyId);
        if (CollUtil.isNotEmpty(removeReadyIds)) {
            List<AsSyncChange> orgChange = removeReadyIds.stream().map(formOrgId -> new AsSyncChange(IdUtils.getId(), companyId, formOrgId, 3, Collections.singletonList(formOrgId), 1).setToOrg(Collections.singletonList(orgTransMap.get(formOrgId)))).collect(Collectors.toList());
            // syncChangeService.saveBatch(orgChange);
            syncChangeService.records(orgChange);
            // 只有需要转移的组织需要
            orgChange.stream()
                    .filter(v -> orgTransMap.containsKey(v.getResId()))
                    .map(change -> new OrgTransferDto(change.getId(), change.getResId(), orgTransMap.get(change.getResId()), orgTransMap.get(change.getResId())))
                    .forEach(syncChangeService::transferOrgDelete);
        }
        // 3.处理员工
        List<AsCusEmployee> employees = tuple3.getFirst();
        List<AsThirdPartyEmployee> thirdPartyEmployees = tuple3.getSecond();
        List<AsUserOrg> userOrgs = tuple3.getThird();
        ConcurrentMap<Long, Set<Long>> newEmpOrgGroup = userOrgs.stream().collect(Collectors.groupingByConcurrent(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toSet())));
        ConcurrentMap<Long, Set<Long>> oldEmpOrgGroup = userOrgService.list(new LambdaQueryWrapper<AsUserOrg>().in(AsUserOrg::getOrgId, orgs.stream().map(AsOrg::getId).collect(Collectors.toSet()))).stream().collect(Collectors.groupingByConcurrent(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toSet())));
        AsCusEmployee administrator = employeeService.getAdministratorByCompanyId(companyId);
        // ConcurrentMap<String, Long> roleNameIdMap = roleService.list(new LambdaQueryWrapper<AsCusRole>().eq(AsCusRole::getCompanyId, companyId).in(AsCusRole::getRoleCode, BaseConstant.ADMIN_ROLE, BaseConstant.ASSET_ADMIN_ROLE, BaseConstant.COMMON_ROLE)).stream().collect(Collectors.toConcurrentMap(AsCusRole::getRoleCode, AsCusRole::getId));
        List<AsCusEmployee> empForAdd = new ArrayList<>();
        List<AsCusEmployee> empForUpdate = new ArrayList<>();
        List<AsCusEmployee> empForDelete = new ArrayList<>();
        employees.forEach(v -> {
            if (ADD.equals(v.getRemark())) {
                empForAdd.add(v.setRemark(null).setUpdateBy(null));
            }
            if (UPDATE.equals(v.getRemark())) {
                empForUpdate.add(v.setRemark(null).setUpdateBy(null));
            }
            if (DELETE.equals(v.getRemark())) {
                empForDelete.add(v);
            }
        });
        // 新增用户及关系表
        List<AsCusEmployeeExt> empExtForAdd = new ArrayList<>(empForAdd.size());
        List<AsCusEmployeeSetting> empSettingForAdd = new ArrayList<>(empForAdd.size());
        List<AsUserCompany> empCompanyForAdd = new ArrayList<>(empForAdd.size());
        // List<AsUserRole> empRoleForAdd = new ArrayList<>(empForAdd.size());
        empForAdd.forEach(v -> {
            empExtForAdd.add(new AsCusEmployeeExt().setId(v.getId()).setAgentId(0));
            empSettingForAdd.add(new AsCusEmployeeSetting().setUserId(v.getId()).setAppToolbox(Collections.emptyList()).setPcToolbox(Collections.emptyList()).setPcHome(Collections.emptyList()).setAssetHead(Collections.emptyList()).setMaterialHead(Collections.emptyList()));
            empCompanyForAdd.add(new AsUserCompany().setUserId(v.getId()).setCompanyId(companyId));
            // empRoleForAdd.add(new AsUserRole().setUserId(v.getId()).setRoleId(roleNameIdMap.get(BaseConstant.COMMON_ROLE)));
            dataPermissionService.initDataPermission(companyId, v.getId(), BaseConstant.COMMON_ROLE);
        });
        // 新增用户初始化数据
        employeeService.batchSaveCondition(empForAdd);
        employeeService.batchUpdateByIdCondition(empForUpdate);
        employeeExtService.saveBatch(empExtForAdd);
        employeeSettingService.saveBatch(empSettingForAdd);
        cusUserCompanyService.saveBatch(empCompanyForAdd);
        // userRoleService.saveBatch(empRoleForAdd);
        // 重建员工与第三方用户的关系表
        thirdPartyEmployeeService.removeByCompanyId(companyId, getType());
        thirdPartyEmployeeService.saveBatch(thirdPartyEmployees);
        // 重建员工组织关系表及异动处理
        userOrgService.remove(Wrappers.lambdaQuery(AsUserOrg.class).in(AsUserOrg::getUserId, employees.stream().map(AsCusEmployee::getId).collect(Collectors.toSet())));
        userOrgService.saveBatch(userOrgs);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(companyId, employees.stream().map(AsCusEmployee::getId).collect(Collectors.toList()));
        loadEmpCache(companyId);
        List<Long> kickoff = new ArrayList<>();
        oldEmpOrgGroup.forEach((k, v) -> {
            // 新增员工不用处理
            if (!newEmpOrgGroup.containsKey(k)) {
                return;
            }
            List<Long> form = new ArrayList<>(v);
            List<Long> to = new ArrayList<>(newEmpOrgGroup.get(k));
            String newOrgIdsToString = to.stream().sorted(Long::compare).map(String::valueOf).collect(Collectors.joining());
            String oldOrgIdsToString = new ArrayList<>(v).stream().sorted(Long::compare).map(String::valueOf).collect(Collectors.joining());
            // 没有变化不处理
            if (newOrgIdsToString.equals(oldOrgIdsToString)) {
                return;
            }
            // 异动的组织包含之前的组织不处理 A -> A || AB -> ABC || A -> ABC
            if (to.size() > 1 && CollUtil.isNotEmpty(CollUtil.intersectionDistinct(to, form))) {
                return;
            }
            // 自动处理 A->B, AB->B, 多个部门合并为一个【ABC->B】
            if (to.size() == 1) {
                // 最后一次未处理的员工部门异动
                AsSyncChange lastUntreatedRecord = syncChangeService.lastUntreatedRecord(companyId, k, 1);
                if (Objects.nonNull(lastUntreatedRecord) && CollUtil.isNotEmpty(lastUntreatedRecord.getFromOrg())) {
                    form.addAll(lastUntreatedRecord.getFromOrg().stream().distinct().filter(id -> !form.contains(id)).collect(Collectors.toList()));
                }
                form.removeAll(to);
                // 原部门
                List<AsOrg> formOrgs = CollUtil.isEmpty(form) ? new ArrayList<>(6) : orgs.stream().filter(org -> form.contains(org.getId())).collect(Collectors.toList());
                // 现部门
                List<AsOrg> toOrgs = CollUtil.isEmpty(to) ? new ArrayList<>(6) : orgs.stream().filter(org -> to.contains(org.getId())).collect(Collectors.toList());
                List<CusEmployeeTransferDto> trans = form.stream().map(id -> new CusEmployeeTransferDto().setFrom(id).setTo(to.get(0))).collect(Collectors.toList());
                employeeService.changeOrgLog(formOrgs, toOrgs, trans, k, companyId);
                // 删除还未处理的员工编辑异动记录
                syncChangeService.removeUntreatedRecord(companyId, k, 1);
                kickoff.add(k);
                return;
            }
            // 异动处理 A->BC, AB->BC, AB-CD
            if (!companyAssetService.checkUsePerson(Collections.singletonList(k), companyId).isEmpty()) {
                AsSyncChange empOrgChange = new AsSyncChange(IdUtils.getId(), companyId, k, 1, form, 1).setToOrg(to);
                // syncChangeService.save(empOrgChange);
                syncChangeService.records(Lists.newArrayList(empOrgChange));
                empChange.set(true);
                kickoff.add(k);
            }
        });
        // 删除员工
        handleDeleteEmp(empForDelete.stream().map(AsCusEmployee::getId).collect(Collectors.toList()), administrator);
        // 未匹配的员工处理
        if (CollUtil.isNotEmpty(transMismatchEmps)) {
            List<AsSyncChange> empDeleteChange = transMismatchEmps.stream().map(transMismatchEmp -> new AsSyncChange().setId(IdUtils.getId()).setResId(transMismatchEmp.getEmpId()).setType(2).setStatus(1).setFromOrg(Collections.emptyList()).setToOrg(Collections.emptyList())).collect(Collectors.toList());
            syncChangeService.records(empDeleteChange);
            // syncChangeService.saveBatch(empDeleteChange);
            empChange.set(true);
            Map<Long, Long> empSyncChangeIdMap = empDeleteChange.stream().collect(Collectors.toMap(AsSyncChange::getResId, AsSyncChange::getId));
            Long userId = LoginUserThreadLocal.getCurrentUserId();
            // 只有组织同步初始化的时候需要主动转移资产
            transMismatchEmps.forEach(v -> {
                if (!v.getActiveTransfer()) {
                    return;
                }
                // 转移管理的资产与使用的资产
                RemoveEmployDto removeEmp = new RemoveEmployDto();
                removeEmp.setEmployeeId(v.getEmpId()).setEmployeeName(v.getEmpName()).setAssetStatusToIdle(v.getAssetStatusToIdle());
                if (Objects.nonNull(v.getUseAssetEmpId())) {
                    DataTransfer useAssetTrans = new DataTransfer()
                            .setOperateEmployeeId(userId)
                            .setReceiveEmployeeId(v.getUseAssetEmpId())
                            .setReceiveEmployeeNo(v.getUseAssetEmpNo())
                            .setReceiveEmployeeName(v.getUseAssetEmpName())
                            .setReceiveOrgId(newEmpOrgGroup.get(v.getUseAssetEmpId()).iterator().next());
                    removeEmp.setUseAsset(useAssetTrans);
                }
                if (Objects.nonNull(v.getManageAssetEmpId())) {
                    DataTransfer manageAssetTrans = new DataTransfer()
                            .setOperateEmployeeId(userId)
                            .setReceiveEmployeeId(v.getManageAssetEmpId())
                            .setReceiveEmployeeNo(v.getManageAssetEmpNo())
                            .setReceiveEmployeeName(v.getManageAssetEmpName())
                            .setReceiveOrgId(newEmpOrgGroup.get(v.getManageAssetEmpId()).iterator().next());
                    removeEmp.setManageAsset(manageAssetTrans);
                }
                if (Objects.nonNull(v.getApprovalEmpId())) {
                    DataTransfer approvalTrans = new DataTransfer()
                            .setOperateEmployeeId(userId)
                            .setReceiveEmployeeId(v.getApprovalEmpId())
                            .setReceiveEmployeeNo(v.getApprovalEmpNo())
                            .setReceiveEmployeeName(v.getApprovalEmpName());
                    removeEmp.setApprovalTask(approvalTrans);
                }
                if (Objects.nonNull(v.getEntMatTaskEmpId())) {
                    DataTransfer entMatTaskTrans = new DataTransfer()
                            .setOperateEmployeeId(userId)
                            .setReceiveEmployeeId(v.getEntMatTaskEmpId())
                            .setReceiveEmployeeNo(v.getEntMatTaskEmpNo())
                            .setReceiveEmployeeName(v.getEntMatTaskEmpName());
                    removeEmp.setEntMatTask(entMatTaskTrans);
                }
                syncChangeService.transferEmpDelete(empSyncChangeIdMap.get(v.getEmpId()), removeEmp);
            });
            // 删除员工数据
            List<Long> deleteEmpIds = transMismatchEmps.stream().map(TransMismatchEmp::getEmpId).collect(Collectors.toList());
            handleDeleteEmp(deleteEmpIds, administrator);
        }
        // 更新状态
        thirdPartyService.update(
                Wrappers.lambdaUpdate(AsThirdParty.class)
                        .set(AsThirdParty::getInitSync, true)
                        .set(AsThirdParty::getUpdateTime, LocalDateTime.now())
                        .eq(AsThirdParty::getCompanyId, companyId)
        );
        // 清除全部步骤缓存
        cleanCache(companyId);
        writeSyncStep(companyId, ThirdPartyConstant.HANDLE);
        if (empChange.get()) {
            messageService.sendInnerMessage(MsgSendCmd.yd(companyId));
        }
        if (CollUtil.isNotEmpty(kickoff)) {
            try {
                SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(
                        kickoff.stream().map(v -> new AsCusUser().setId(v).setCompanyId(companyId)).collect(Collectors.toList())
                );
            } catch (Exception e) {
                log.error("kickoff user error", e);
            }
        }
        return true;
    }

    private void handleDeleteEmp(List<Long> empIds, AsCusEmployee admin) {
        if (CollUtil.isEmpty(empIds)) {
            return;
        }
        if (Objects.nonNull(admin) && empIds.contains(admin.getId())) {
            empIds.remove(admin.getId());
            // 更新超管被删除的标记
            employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getThirdPartyRemove, true).eq(AsCusEmployeeExt::getId, admin.getId()));
        }
        if (CollUtil.isNotEmpty(empIds)) {
            List<AsCusUser> users = employeeService.listByIds(empIds).stream().map(employee -> new AsCusUser().setCompanyId(employee.getCompanyId()).setId(employee.getId())).collect(Collectors.toList());
            employeeService.removeByIds(empIds);
            employeeExtService.removeByIds(empIds);
            employeeSettingService.removeByIds(empIds);
            userRoleService.remove(Wrappers.lambdaQuery(AsUserRole.class).in(AsUserRole::getUserId, empIds));
            List<Long> removeAccountIds = accountEmployeeService.list(Wrappers.lambdaQuery(AsAccountEmployee.class)
                            .select(AsAccountEmployee::getAccountId).eq(AsAccountEmployee::getEmployeeId, empIds))
                    .stream().map(AsAccountEmployee::getAccountId).collect(Collectors.toList());
            accountEmployeeService.batchUnbindEmploy(empIds);
            accountService.kickOffLoginUser(users);
            if (Edition.isLocal() && CollUtil.isNotEmpty(removeAccountIds)) {
                // 本地部署删除账号
                cusUserService.removeByIds(removeAccountIds);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void overallHandle(Long companyId) {
        String syncKey = ThirdPartyConstant.getThirdPartyManualSyncKey(companyId, getType());
        try {
            AssetThirdPartyOrgMapping orgMapping = getOrgMapping(companyId);
            List<AssetThirdPartyOrgMapping.Mapping> mappings = orgMapping.getMappings();
            handleOrgMapping(mappings);
            // 找出要被删除的
            List<AsOrg> orgRaw = readOrgsCache(companyId, ASSET);
            List<AsOrg> orgReady = readOrgsCache(companyId, ORG_HANDLE_FINISH);
            Set<Long> orgIds = mappings.stream().map(AssetThirdPartyOrgMapping.Mapping::getAssetOrgId).collect(Collectors.toSet());
            List<AsOrg> orgForDelete = orgRaw.stream().filter(v -> !orgIds.contains(v.getId())).peek(v -> v.setRemark(DELETE)).collect(Collectors.toList());
            orgReady.addAll(orgForDelete);
            writeOrgCache(companyId, ORG_HANDLE_FINISH, orgReady);
            // 获取员工
            AssetThirdPartyEmpMapping empMapping = getEmpMapping(companyId);
            List<TransMismatchEmp> mismatchEmps = empMapping.getMismatch().stream().map(v -> new TransMismatchEmp().setEmpId(v.getEmpId()).setActiveTransfer(false)).collect(Collectors.toList());
            handle(mismatchEmps);
            // 处理账号
            if (Edition.isLocal()) {
                handleAccount(companyId);
            }
        } catch (BusinessException e) {
            redisService.del(syncKey);
            throw e;
        } catch (Exception e) {
            loadOrgCache(companyId);
            loadEmpCache(companyId);
            log.error(e.getMessage(), e);
            redisService.del(syncKey);
            throw e;
        } finally {
            cleanCache(companyId);
        }
    }

    private void handleAccount(Long companyId) {
        // 查询全部三方员工
        List<AsThirdPartyEmployee> partyEmployees = thirdPartyEmployeeService.listByCompanyId(companyId, getType());

        List<AsCusEmployee> employeeList = employeeService.list(Wrappers.lambdaQuery(AsCusEmployee.class).eq(AsCusEmployee::getCompanyId, companyId));
        Map<Long, AsCusEmployee> employeeMap = employeeList.stream().collect(Collectors.toMap(AsCusEmployee::getId, k -> k, (k1, k2) -> k1));

        // 查询所有员工关联账号信息 Map<员工ID, 账户ID>
        Map<Long, Long> accountEmployeeMap = accountEmployeeService
                .list(Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .select(AsAccountEmployee::getAccountId, AsAccountEmployee::getEmployeeId)
                        .eq(AsAccountEmployee::getCompanyId, companyId)).stream()
                .collect(Collectors.toMap(AsAccountEmployee::getEmployeeId, AsAccountEmployee::getAccountId, (k1, k2) -> k1));

        List<AsCusUser> accountForAdd = new ArrayList<>();
        List<AsAccountEmployee> accountEmployeeForAdd = new ArrayList<>();
        List<AsCusUser> accountForUpt = new ArrayList<>();
        for (AsThirdPartyEmployee partyEmployee : partyEmployees) {
            if (employeeMap.containsKey(partyEmployee.getEmployeeId())) {
                AsCusEmployee employee = employeeMap.get(partyEmployee.getEmployeeId());
                AsCusUser account = new AsCusUser();
                account.setAccount(partyEmployee.getUserId())
                        .setUnionId(partyEmployee.getUserId())
                        .setThirdPartyId(partyEmployee.getUserId())
                        .setCompanyId(companyId)
                        .setId(accountEmployeeMap.getOrDefault(partyEmployee.getEmployeeId(), IdUtils.getId()))
                        .setMobile(employee.getMobile())
                        .setEmail(employee.getEmail())
                        .setPassword(new BCryptPasswordEncoder().encode("jc123456"))
                        .setSource(1);
                if (accountEmployeeMap.containsKey(partyEmployee.getEmployeeId())) {
                    accountForUpt.add(account);
                } else {
                    accountForAdd.add(account);
                    accountEmployeeForAdd.add(new AsAccountEmployee(account.getId(), companyId, employee.getId()));
                }
            }
        }
        // 先新增/更新账号
        if (CollUtil.isNotEmpty(accountForAdd)) {
            cusUserService.saveBatch(accountForAdd);
            accountEmployeeService.saveBatch(accountEmployeeForAdd);
            // 激活员工
            employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class)
                    .set(AsCusEmployeeExt::getAccountStatus, 3)
                    .in(AsCusEmployeeExt::getId, accountEmployeeForAdd.stream()
                            .map(AsAccountEmployee::getEmployeeId).collect(Collectors.toList())));
        }
        if (CollUtil.isNotEmpty(accountForUpt)) {
            cusUserService.updateBatchById(accountForUpt);
        }
    }

    /**
     * 检查组织是否被资产关联
     *
     * @param removeOrgIds 删除的组织
     * @param companyId    公司Id
     * @return 被使用的组织
     */
    private Set<Long> checkOrgAsset(List<Long> removeOrgIds, Long companyId) {
        if (CollUtil.isEmpty(removeOrgIds)) {
            return Collections.emptySet();
        }
        // 使用组织
        List<Long> useOrgIds = companyAssetService.checkUseOrg(removeOrgIds, companyId);
        // 管理组织
        List<Long> orgOwnerIds = companyAssetService.checkOrgOwner(removeOrgIds, companyId);
        useOrgIds.addAll(orgOwnerIds);
        return new HashSet<>(useOrgIds);
    }

    @Override
    public void loadOrgCache(Long companyId) {
        // 全部字典
        List<AsOrg> orgList = this.orgService.list(new LambdaQueryWrapper<AsOrg>()
                .select(AsOrg::getOrgName, AsOrg::getId)
                .eq(AsOrg::getCompanyId, companyId));
        // 全部字典
        Map<String, String> collect = new HashMap<>();
        orgList.forEach(org -> {
            JSONObject jsonObject = new JSONObject().fluentPut("name", org.getOrgName()).fluentPut("code", org.getOrgCode());
            collect.put(Convert.toStr(org.getId()), jsonObject.toJSONString());
        });
        redisService.hSetAll(RedisConstant.orgDictKey() + ":" + companyId, collect);
        redisService.hSetAll(RedisConstant.orgDictKey(), collect);
    }

    @Override
    public void loadEmpCache(Long companyId) {
        List<AsCusEmployee> empList = employeeService.list(new LambdaQueryWrapper<AsCusEmployee>()
                .select(AsCusEmployee::getEmpName, AsCusEmployee::getId)
                .eq(AsCusEmployee::getCompanyId, companyId));
        // 全部字典
        Map<String, String> collect = new HashMap<>();
        // 按公司ID创建字典
        empList.forEach(emp -> {
            JSONObject jsonObject = new JSONObject().fluentPut("name", emp.getEmpName()).fluentPut("code", emp.getEmpNo());
            collect.put(Convert.toStr(emp.getId()), jsonObject.toJSONString());
        });
        redisService.hSetAll(RedisConstant.employeeDictKey() + ":" + companyId, collect);
        redisService.hSetAll(RedisConstant.employeeDictKey(), collect);
    }

    @Override
    public JSONObject getSecret(Long companyId, String type) {
        switch (type) {
            case ThirdPartyConstant.DINGTALK:
                String dingtalkKey = ThirdPartyConstant.getDingtalkKey(companyId);
                Object ding = redisService.get(dingtalkKey);
                if (ding == null) {
                    AsThirdParty one = this.thirdPartyService.getOne(new LambdaQueryWrapper<AsThirdParty>()
                            .eq(AsThirdParty::getCompanyId, companyId)
                            .eq(AsThirdParty::getType, ThirdPartyConstant.DINGTALK));
                    if (one != null) {
                        JSONObject form = one.getForm();
                        redisService.set(dingtalkKey, form, 2, TimeUnit.HOURS);
                        return form;
                    } else {
                        return null;
                    }
                } else {
                    return (JSONObject) ding;
                }
            case ThirdPartyConstant.WECHAT:
                String wechatKey = ThirdPartyConstant.getWeChatKey(companyId);
                Object wechat = redisService.get(wechatKey);
                if (wechat == null) {
                    AsThirdParty one = this.thirdPartyService.getOne(new LambdaQueryWrapper<AsThirdParty>()
                            .eq(AsThirdParty::getCompanyId, companyId)
                            .eq(AsThirdParty::getType, ThirdPartyConstant.WECHAT));
                    if (one != null) {
                        JSONObject form = one.getForm();
                        redisService.set(wechatKey, form, 2, TimeUnit.HOURS);
                        return form;
                    } else {
                        return null;
                    }
                } else {
                    return (JSONObject) wechat;
                }
            case ThirdPartyConstant.FEISHU:
                String feishuKey = ThirdPartyConstant.getFeishuKey(companyId);
                Object feishu = redisService.get(feishuKey);
                if (feishu == null) {
                    AsThirdParty one = this.thirdPartyService.getOne(new LambdaQueryWrapper<AsThirdParty>()
                            .eq(AsThirdParty::getCompanyId, companyId)
                            .eq(AsThirdParty::getType, ThirdPartyConstant.FEISHU));
                    if (one != null) {
                        JSONObject form = one.getForm();
                        redisService.set(feishuKey, form, 2, TimeUnit.HOURS);
                        return form;
                    } else {
                        return null;
                    }
                } else {
                    return (JSONObject) feishu;
                }
            default:
                return null;
        }
    }

}
