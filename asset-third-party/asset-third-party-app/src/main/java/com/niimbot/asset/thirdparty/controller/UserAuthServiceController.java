package com.niimbot.asset.thirdparty.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.service.FeishuOpenApiService;
import com.niimbot.asset.thirdparty.service.UserAuthService;
import com.niimbot.event.ThirdAuthEvent;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.FeishuJsAuth;
import com.niimbot.thirdparty.FeishuJsTicketDto;
import com.niimbot.thirdparty.ThirdPartyUser;
import com.niimbot.thirdparty.ThirdPartyUserAuth;
import com.niimbot.thirdparty.WeChatAgent;
import com.niimbot.thirdparty.WechatJsAuth;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * 接入第三方应用登录能力
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/thirdparty/user/auth")
public class UserAuthServiceController {

    private final UserAuthService userAuthService;

    private final AsThirdPartyService thirdPartyService;

    private final FeishuOpenApiService feishuOpenApiService;

    @PostMapping
    public ThirdPartyUser authenticated(@RequestBody ThirdPartyUserAuth userAuth) {
        ThirdPartyUser thirdPartyUser = userAuthService.socialAuth(userAuth.getProvider(), userAuth.getCode(), userAuth.getTerminal());
        log.info("Third Party User Info {}", thirdPartyUser);
        return thirdPartyUser;
    }

    @GetMapping("/scanGetUserId/{companyId}/{type}/{code}")
    public String innerWebAppAuth(@PathVariable Long companyId, @PathVariable String type, @PathVariable String code) {
        JSONObject form = thirdPartyService.getSecret(companyId, type);
        if (CollUtil.isEmpty(form)) {
            throw new BusinessException(ThirdPartyResultCode.UNBIND_THIRD_PARTY);
        }
        return userAuthService.webAuth(companyId, type, code);
    }

    @GetMapping("/h5AppAuth/{companyId}/{type}/{code}")
    public String h5AppAuth(@PathVariable Long companyId, @PathVariable String type, @PathVariable String code) {
        JSONObject form = thirdPartyService.getSecret(companyId, type);
        if (CollUtil.isEmpty(form)) {
            throw new BusinessException(ThirdPartyResultCode.UNBIND_THIRD_PARTY);
        }
        return userAuthService.h5Auth(companyId, type, code);
    }

    @GetMapping("/getConfig/{companyId}/{type}")
    public Object getConfig(@PathVariable Long companyId, @PathVariable String type) {
        Map<String, String> result = new HashMap<>(4);
        JSONObject secret = thirdPartyService.getSecret(companyId, type);
        if (ThirdPartyConstant.DINGTALK.equalsIgnoreCase(type)) {
            // 钉钉配置
            ThirdPartyAkSk.DingTalk dingTalk = secret.toJavaObject(ThirdPartyAkSk.DingTalk.class);
            result.put("clientId", dingTalk.getAppKey());
            result.put("corpId", dingTalk.getCorpId());
        } else if (ThirdPartyConstant.WECHAT.equalsIgnoreCase(type)) {
            // 企微配置
            String authAgent = secret.getString("userAuthAgent");
            if (StrUtil.isBlank(authAgent)) {
                throw new BusinessException(ThirdPartyResultCode.WECHAT_AGENT_CONFIG_ERROR);
            }
            WeChatAgent weChatAgent = JSONObject.parseObject(authAgent, WeChatAgent.class);
            result.put("agentId", weChatAgent.getAgentId());
            result.put("corpId", weChatAgent.getCorpId());
            result.put("decryptCropId", secret.getString("decryptCropId"));
        } else if (ThirdPartyConstant.FEISHU.equalsIgnoreCase(type)) {
            // 飞书配置
            ThirdPartyAkSk.Feishu feishu = secret.toJavaObject(ThirdPartyAkSk.Feishu.class);
            result.put("appId", feishu.getAppId());
        }
        return result;
    }

    @PostMapping("/wechat/getSignature")
    public Map<String, Object> getSignature(@RequestBody WechatJsAuth wechatJsAuth) {
        try {
            WxJsapiSignature jsapiSignature = thirdPartyService.getWeChatService(wechatJsAuth.getCompanyId()).createJsapiSignature(wechatJsAuth.getUrl());
            return BeanUtil.beanToMap(jsapiSignature);
        } catch (WxErrorException e) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取企业微信JsTicket异常，请稍后再试");
        }
    }


    @PostMapping("/feishu/getSignature")
    public FeishuJsTicketDto getFeishuSignature(@RequestBody FeishuJsAuth feishuJsAuth) {
        return feishuOpenApiService.createJsapiSignature(feishuJsAuth);
    }

    @PostMapping("/authenticate")
    public Map<String, Object> authenticate(@RequestBody Map<String, Object> authMap) {
        ThirdAuthEvent event = new ThirdAuthEvent(authMap);
        EventPublishHandler.publish(event);
        return event.getResult();
    }



}
