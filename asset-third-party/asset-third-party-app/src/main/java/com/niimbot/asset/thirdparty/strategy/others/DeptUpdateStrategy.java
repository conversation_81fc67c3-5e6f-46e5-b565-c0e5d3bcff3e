package com.niimbot.asset.thirdparty.strategy.others;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.event.OrgCO;
import com.niimbot.asset.thirdparty.event.OthersCallbackEvent;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Since 2024-10-14
 */
@Slf4j
@Component(value = "othersDeptUpdateStrategy")
@CallbackStrategy(eventType = OthersCallbackEvent.DEPT_UPDATE)
public class DeptUpdateStrategy implements OthersCallbackStrategy {

    private final OrgService orgService;
    private final DeptCreateStrategy deptCreateStrategy;
    private final CacheOrgStrategy cacheOrgStrategy;

    public DeptUpdateStrategy(OrgService orgService,
                              DeptCreateStrategy deptCreateStrategy,
                              CacheOrgStrategy cacheOrgStrategy) {
        this.orgService = orgService;
        this.deptCreateStrategy = deptCreateStrategy;
        this.cacheOrgStrategy = cacheOrgStrategy;
    }

    @Override
    public boolean handleCallback(Long companyId, JSONObject content) {
        OrgCO orgCO = content.toJavaObject(OrgCO.class);
        if (orgCO != null) {
            AsOrg org = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, orgCO.getId()));
            if (ObjectUtil.isNull(org)) {
                log.warn("update company {} org {} has not exists ", companyId, orgCO.getId());
                // 新建组织
                deptCreateStrategy.handleCallback(companyId, content);
            } else {
                AsOrg updateOrg = BeanUtil.copyProperties(org, AsOrg.class);
                updateOrg.setOrgName(orgCO.getName())
                        .setOrgCode(orgCO.getCode())
                        .setExternalOrgId(orgCO.getId())
                        .setExternalPid(orgCO.getParentId());
                // 所有子Path
                String sonPath = updateOrg.getPaths() + updateOrg.getId() + ",";
                List<AsOrg> sonList = orgService.list(new QueryWrapper<AsOrg>().lambda()
                        .eq(AsOrg::getCompanyId, companyId)
                        .likeRight(AsOrg::getPaths, sonPath));
                List<AsOrg> updateSonList = new ArrayList<>();
                for (AsOrg son : sonList) {
                    String p = son.getPaths().replace(sonPath, org.getPaths() + org.getId() + ",");
                    int level = p.split(",").length - 1;
                    AsOrg updateSon = new AsOrg();
                    updateSon.setId(son.getId());
                    updateSon.setLevel(level);
                    updateSon.setPaths(p);
                    // 该组织为部门时并且该子节点也是部门时，需要重写所属公司
                    if (AssetConstant.ORG_TYPE_DEPT.equals(son.getOrgType())
                            && AssetConstant.ORG_TYPE_DEPT.equals(org.getOrgType())) {
                        List<Long> ids = Convert.toList(Long.class, p.split(","));
                        Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                        updateSon.setCompanyOwner(orgCompanyOwner);
                    }
                    updateSonList.add(updateSon);
                }
                // 更新子节点数据
                orgService.updateBatchById(updateSonList);
                // 更新当前数据
                orgService.updateById(updateOrg);
                cacheOrgStrategy.evictCache(updateOrg.getId());
            }
            return true;
        } else {
            log.error("companyId = {}, orgCO is null", companyId);
            return false;
        }
    }
}
