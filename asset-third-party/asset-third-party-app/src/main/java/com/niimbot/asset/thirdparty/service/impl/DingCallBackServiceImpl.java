package com.niimbot.asset.thirdparty.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.service.DingCallbackService;
import com.niimbot.asset.thirdparty.strategy.CallbackContext;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:40
 */
@Service
public class DingCallBackServiceImpl implements DingCallbackService {

    private final CallbackContext callbackContext;

    public DingCallBackServiceImpl(CallbackContext callbackContext) {
        this.callbackContext = callbackContext;
    }

    @Override
    public boolean handle(Long companyId, String accessToken, JSONObject content) {
        return callbackContext.executeDing(companyId, accessToken, content);
    }

}
