package com.niimbot.asset.thirdparty.model;

import com.lark.oapi.core.response.BaseResponse;
import com.lark.oapi.core.response.EmptyData;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/7/15 18:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CuzCreateTenantAccessTokenResp extends BaseResponse<EmptyData> {

    private String tenant_access_token;

    private Integer expire;

}
