package com.niimbot.asset.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.ResponseCode;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.utils.DeduplicationUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.service.AbsOrgStructureService;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/10 10:45
 */
@Slf4j
@Service
public class DingtalkOrgStructureServiceImpl extends AbsOrgStructureService {

    private final DingOpenApiService dingOpenApiService;

    @Autowired
    public DingtalkOrgStructureServiceImpl(DingOpenApiService dingOpenApiService) {
        this.dingOpenApiService = dingOpenApiService;
    }

    OapiAuthScopesResponse dingAuthScope(Long companyId) {
        String accessToken = getAccessToken(companyId);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/auth/scopes");
        OapiAuthScopesRequest req = new OapiAuthScopesRequest();
        req.setHttpMethod("GET");
        try {
            return client.execute(req, accessToken);
        } catch (ApiException e) {
            log.error("get auth scope error", e);
            throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
        }
    }

    List<Long> getOrgAuthScope(Long companyId) {
        return dingAuthScope(companyId).getAuthOrgScopes().getAuthedDept().stream().distinct().collect(Collectors.toList());
    }

    List<String> getUserAuthScope(Long companyId) {
        return dingAuthScope(companyId).getAuthOrgScopes().getAuthedUser().stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<AsOrg> loadThirdPartyOrg(Long companyId) {
        // 授权范围
        List<Long> scope = getOrgAuthScope(companyId);
        if (CollUtil.isEmpty(scope)) {
            return new ArrayList<>();
        }
        Set<String> remoteOrgIds = Collections.synchronizedSet(new HashSet<>(200));
        List<OapiV2DepartmentGetResponse.DeptGetResponse> remoteOrgs = new ArrayList<>(200);
        getRemoteOrgs(companyId, new HashSet<>(scope), remoteOrgIds, remoteOrgs);
        // 去重
        if (remoteOrgIds.size() != remoteOrgs.size()) {
            remoteOrgs = remoteOrgs.stream()
                    .filter(DeduplicationUtil.distinctByKey(OapiV2DepartmentGetResponse.DeptGetResponse::getDeptId))
                    .collect(Collectors.toList());
        }
        AsOrg rootOrg = orgService.getRootOrg(companyId);
        return remoteOrgs.stream()
                .map(v -> toOrg(rootOrg.getId(), companyId, remoteOrgIds, v))
                .collect(Collectors.toList());
        // 结果集
        // List<AsOrg> remoteList = new ArrayList<>();
        // 递归查询全部组织，1是根节点
        // recursionAllOrg(remoteList, new ArrayList<>(), companyId, accessToken, null);
        // return remoteList;
    }

    private AsOrg toOrg(Long localRootOrgId, Long companyId, Set<String> remoteIds, OapiV2DepartmentGetResponse.DeptGetResponse remote) {
        AsOrg org = new AsOrg();
        org.setCompanyId(companyId);
        org.setExternalOrgId(String.valueOf(remote.getDeptId()));
        // 当前节点是根节点
        if ("1".equals(String.valueOf(remote.getDeptId()))) {
            org.setExternalPid("0");
        }
        // 当前节点不是根节点 且 其父节点不在本地同步的范围内 直接挂靠在根节点下
        else if (!"1".equals(String.valueOf(remote.getDeptId())) && Objects.nonNull(remote.getParentId()) && !remoteIds.contains(String.valueOf(remote.getParentId()))) {
            org.setExternalPid("1");
        }
        // 普通节点
        else {
            org.setExternalPid(Objects.isNull(remote.getParentId()) ? "1" : String.valueOf(remote.getParentId()));
        }
        org.setSortNum(Objects.isNull(remote.getOrder()) ? 100 : Convert.toInt(remote.getOrder(), 100));
        org.setOrgName(remote.getName());
        org.setCompanyOwner(localRootOrgId);
        org.setExternalDirector(remote.getDeptManagerUseridList());
        org.setId(IdUtils.getId());
        org.setOrgType("1".equals(String.valueOf(remote.getDeptId())) ? 1 : 2);
        org.setSourceType(1);
        return org;
    }

    private void getRemoteOrgs(Long companyId, Set<Long> remoteParentIds, Set<String> remoteOrgIds, List<OapiV2DepartmentGetResponse.DeptGetResponse> remoteOrgs) {
        OapiV2DepartmentGetResponse.DeptGetResponse parent = null;
        List<OapiV2DepartmentGetResponse.DeptGetResponse> child;
        for (Long pid : remoteParentIds) {
            // 当前组织
            if (!remoteOrgIds.contains(String.valueOf(pid))) {
                parent = getRemoteOrg(companyId, pid);
            }
            if (Objects.nonNull(parent)) {
                remoteOrgs.add(parent);
                remoteOrgIds.add(String.valueOf(pid));
            }
            // 子节点
            child = getRemoteChildOrgs(companyId, pid);
            try {
                TimeUnit.MILLISECONDS.sleep(50L);
            } catch (InterruptedException e) {
                log.warn("ding get child depart sleep error", e);
                continue;
            }
            if (CollUtil.isEmpty(child)) {
                continue;
            }
            remoteOrgs.addAll(child);
            Set<Long> nextParentIds = new HashSet<>(child.size());
            child.forEach(c -> {
                nextParentIds.add(c.getDeptId());
                remoteOrgIds.add(String.valueOf(c.getDeptId()));
            });
            getRemoteOrgs(companyId, nextParentIds, remoteOrgIds, remoteOrgs);
        }
    }

    private OapiV2DepartmentGetResponse.DeptGetResponse getRemoteOrg(Long companyId, Long id) {
        String accessToken = getAccessToken(companyId);
        DingTalkClient client = new DefaultDingTalkClient(DingOpenApiService.GET_DEPARTMENT);
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(id);
        OapiV2DepartmentGetResponse rsp;
        try {
            log.info("ding get depart request[{}]", JSONObject.toJSONString(req));
            rsp = client.execute(req, accessToken);
        } catch (Exception e) {
            log.error("ding get depart error", e);
            return null;
        }
        if (Objects.isNull(rsp)) {
            log.error("ding get depart response is null");
            return null;
        }
        if (!rsp.isSuccess()) {
            log.error("ding get depart response is fail[{}]", JSONObject.toJSONString(rsp));
            return null;
        }
        log.info("ding get depart response[{}]", JSONObject.toJSONString(rsp));
        return rsp.getResult();
    }

    private List<OapiV2DepartmentGetResponse.DeptGetResponse> getRemoteChildOrgs(Long companyId, Long pid) {
        DingTalkClient client = new DefaultDingTalkClient(DingOpenApiService.DEPARTMENT_LIST_SUB);
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(pid);
        OapiV2DepartmentListsubResponse rsp;
        try {
            String accessToken = getAccessToken(companyId);
            log.info("ding get child depart request[{}]", JSONObject.toJSONString(req));
            rsp = client.execute(req, accessToken);
        } catch (Exception e) {
            log.error("ding get child depart ids error", e);
            return null;
        }
        if (Objects.isNull(rsp)) {
            log.error("ding get child depart ids response is null");
            return null;
        }
        if (!rsp.isSuccess()) {
            log.error("ding get child depart ids response is fail[{}]", JSONObject.toJSONString(rsp));
            return null;
        }
        log.info("ding get child depart response[{}]", JSONObject.toJSONString(rsp));
        return rsp.getResult()
                .stream()
                .filter(v -> v.getDeptId() > 0)
                .map(v -> getRemoteOrg(companyId, v.getDeptId())).collect(Collectors.toList());
    }

    // private void recursionAllOrg(List<AsOrg> remoteList,
    //                              List<AsOrg> pList,
    //                              Long companyId, String accessToken, Long rootOrgId) {
    //     if (CollUtil.isEmpty(remoteList)) {
    //         // 查询 DeptId 为1的根节点
    //         AsOrg rootOrg = getRootOrg(companyId, accessToken);
    //         if (ObjectUtil.isNotNull(rootOrg)) {
    //             pList.add(rootOrg);
    //             remoteList.addAll(pList);
    //             rootOrgId = rootOrg.getId();
    //         }
    //     }
    //     for (AsOrg org : pList) {
    //         List<AsOrg> orgList = listSub(org.getId(), Convert.toLong(org.getExternalOrgId()), companyId, accessToken, org, rootOrgId);
    //         if (CollUtil.isNotEmpty(orgList)) {
    //             remoteList.addAll(orgList);
    //             recursionAllOrg(remoteList, orgList, companyId, accessToken, rootOrgId);
    //         }
    //     }
    // }

    // private AsOrg getRootOrg(Long companyId, String accessToken) {
    //     DingTalkClient client = new DefaultDingTalkClient(DingOpenApiService.GET_DEPARTMENT);
    //     OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
    //     req.setDeptId(1L);
    //     try {
    //         OapiV2DepartmentGetResponse rsp = client.execute(req, accessToken);
    //         if (ResponseCode.SUCCESS == rsp.getErrcode()) {
    //             OapiV2DepartmentGetResponse.DeptGetResponse deptBaseResponse = rsp.getResult();
    //             // 查询当前节点
    //             AsOrg org = new AsOrg();
    //             List<String> deptManagerUserIdList = getDeptManagerUserIdList(deptBaseResponse.getDeptId(), accessToken);
    //             if (CollUtil.isNotEmpty(deptManagerUserIdList)) {
    //                 org.setExternalDirector(deptManagerUserIdList);
    //             }
    //             org.setId(IdUtils.getId());
    //             org.setExternalOrgId("1");
    //             org.setOrgName(deptBaseResponse.getName());
    //             org.setPid(0L);
    //             org.setExternalPid("0");
    //             org.setPaths("0,");
    //             org.setLevel(0);
    //             org.setOrgType(AssetConstant.ORG_TYPE_COMPANY);
    //             org.setCompanyId(companyId);
    //             org.setCompanyOwner(org.getId());
    //             org.setSortNum(1);
    //             AsOrg assetRootOrg = getAssetRootOrg(companyId);
    //             org.setId(assetRootOrg.getId());
    //             org.setCompanyOwner(assetRootOrg.getId());
    //
    //             return org;
    //         } else if (rsp.getErrcode().equals(50004L)) {
    //             log.warn("company {} get dep root error code is 50004 {}", companyId, rsp.getErrmsg());
    //             return null;
    //         } else {
    //             log.warn("company {} get dept root error ==>> [{}] {}", companyId, rsp.getErrcode(), rsp.getErrmsg());
    //             throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
    //         }
    //     } catch (ApiException e) {
    //         log.error("company {} get dept root error ==>> [{}] {}", companyId, e.getErrCode(), e.getErrMsg());
    //         throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
    //     }
    // }

    // private List<String> getDeptManagerUserIdList(Long deptId, String accessToken) {
    //     DingTalkClient client = new DefaultDingTalkClient(DingOpenApiService.GET_DEPARTMENT);
    //     OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
    //     req.setDeptId(deptId);
    //     try {
    //         OapiV2DepartmentGetResponse rsp = client.execute(req, accessToken);
    //         if (ResponseCode.SUCCESS == rsp.getErrcode()) {
    //             OapiV2DepartmentGetResponse.DeptGetResponse deptBaseResponse = rsp.getResult();
    //             return deptBaseResponse.getDeptManagerUseridList();
    //         } else {
    //             log.warn("get dept error ==>> [{}] {}", rsp.getErrcode(), rsp.getErrmsg());
    //             throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
    //         }
    //     } catch (ApiException e) {
    //         log.error("get dept error ==>> [{}] {}", e.getErrCode(), e.getErrMsg());
    //         throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
    //     }
    // }

    // private List<AsOrg> listSub(Long pid, Long externalPid, Long companyId,
    //                             String accessToken, AsOrg pOrg, Long rootOrgId) {
    //     DingTalkClient client = new DefaultDingTalkClient(DingOpenApiService.DEPARTMENT_LIST_SUB);
    //     OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
    //     req.setDeptId(externalPid);
    //     try {
    //         OapiV2DepartmentListsubResponse rsp = client.execute(req, accessToken);
    //         if (ResponseCode.SUCCESS == rsp.getErrcode()) {
    //             List<OapiV2DepartmentListsubResponse.DeptBaseResponse> result = rsp.getResult();
    //             List<AsOrg> orgList = new ArrayList<>();
    //             for (int i = 0, len = result.size(); i < len; i++) {
    //                 OapiV2DepartmentListsubResponse.DeptBaseResponse deptBaseResponse = result.get(i);
    //                 if (deptBaseResponse.getDeptId() > 0) {
    //                     // 查询当前节点
    //                     AsOrg org = new AsOrg();
    //                     List<String> deptManagerUserIdList = getDeptManagerUserIdList(deptBaseResponse.getDeptId(), accessToken);
    //                     if (CollUtil.isNotEmpty(deptManagerUserIdList)) {
    //                         org.setExternalDirector(deptManagerUserIdList);
    //                     }
    //                     org.setId(IdUtils.getId());
    //                     org.setExternalOrgId(Convert.toStr(deptBaseResponse.getDeptId()));
    //                     org.setOrgName(deptBaseResponse.getName());
    //                     org.setSortNum(i);
    //                     org.setPid(pid);
    //                     org.setExternalPid(Convert.toStr(externalPid));
    //                     org.setPaths(pOrg.getPaths() + pid + ",");
    //                     org.setLevel(pOrg.getLevel() + 1);
    //                     org.setCompanyId(companyId);
    //                     org.setCompanyOwner(rootOrgId);
    //                     orgList.add(org);
    //                 }
    //             }
    //             return orgList;
    //         } else {
    //             log.warn("company {} get dept [pid={}] list error ==>> [{}] {}", companyId, pid, rsp.getErrcode(), rsp.getErrmsg());
    //             throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
    //         }
    //     } catch (ApiException e) {
    //         log.error("company {} get dept [pid={}] list error ==>> [{}] {}", companyId, pid, e.getErrCode(), e.getErrMsg());
    //         throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
    //     }
    // }

    @Override
    public List<ThirdPartyEmp> loadThirdPartyEmp(Long companyId, List<String> thirdPartyOrgIds) {
        List<String> userAuthScope = getUserAuthScope(companyId);
        if (CollUtil.isEmpty(userAuthScope) && CollUtil.isEmpty(thirdPartyOrgIds)) {
            return Collections.emptyList();
        }
        String accessToken = getAccessToken(companyId);
        // 查询员工集合
        List<ThirdPartyEmp> userRspList = new CopyOnWriteArrayList<>();
        thirdPartyOrgIds.forEach(id -> userList(userRspList, Convert.toLong(id), companyId, accessToken, 0L));
        // 授权范围内的员工信息
        userRspList.addAll(getRemoteAuthUsers(companyId, userAuthScope, userRspList.stream().map(ThirdPartyEmp::getThirdPartyId).collect(Collectors.toSet())));
        return userRspList;
    }

    private List<ThirdPartyEmp> getRemoteAuthUsers(Long companyId, List<String> userAuthScope, Set<String> remoteUserIds) {
        if (CollUtil.isEmpty(userAuthScope)) {
            return Collections.emptyList();
        }
        userAuthScope.removeIf(remoteUserIds::contains);
        if (CollUtil.isEmpty(userAuthScope)) {
            return Collections.emptyList();
        }
        return userAuthScope.stream()
                .map(id -> {
                    try {
                        String accessToken = getAccessToken(companyId);
                        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
                        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
                        req.setUserid(id);
                        req.setLanguage("zh_CN");
                        OapiV2UserGetResponse response = client.execute(req, accessToken);
                        if (!response.isSuccess()) {
                            log.error("ding get user fail[{}]", JSONObject.toJSONString(response));
                            return null;
                        }
                        OapiV2UserGetResponse.UserGetResponse it = response.getResult();
                        ThirdPartyEmp emp = new ThirdPartyEmp();
                        emp.setName(it.getName());
                        emp.setJobNum(it.getJobNumber());
                        emp.setPosition(it.getTitle());
                        emp.setAvatar(it.getAvatar());
                        emp.setThirdPartyId(it.getUserid());
                        emp.setMobile(it.getMobile());
                        emp.setEmail(StrUtil.isNotEmpty(it.getOrgEmail()) ? it.getOrgEmail() : it.getEmail());
                        emp.setDeptIdList(it.getDeptIdList().stream().map(Convert::toStr).collect(Collectors.toList()));
                        emp.setCompanyId(companyId);
                        return emp;
                    } catch (Exception e) {
                        log.error("ding get user error", e);
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public String getType() {
        return ThirdPartyConstant.DINGTALK;
    }

    /**
     * 获取部门用户详情 https://developers.dingtalk.com/document/app/queries-the-complete-information-of-a-department-user
     *
     * @param listUserResponses 用户List
     * @param orgId             组织Id
     * @param accessToken       企业access_token
     * @param cursor            游标
     */
    private void userList(List<ThirdPartyEmp> listUserResponses, Long orgId, Long companyId, String accessToken, Long cursor) {
        DingTalkClient client = new DefaultDingTalkClient(DingOpenApiService.USER_LIST);
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(orgId);
        req.setOrderField("entry_asc");
        req.setCursor(cursor);
        req.setSize(100L);
        try {
            OapiV2UserListResponse rsp = client.execute(req, accessToken);
            if (ResponseCode.SUCCESS == rsp.getErrcode()) {
                OapiV2UserListResponse.PageResult result = rsp.getResult();
                List<OapiV2UserListResponse.ListUserResponse> userResponses = result.getList();
                List<ThirdPartyEmp> collect = userResponses.stream().map(it -> {
                    ThirdPartyEmp emp = new ThirdPartyEmp();
                    emp.setName(it.getName());
                    emp.setJobNum(it.getJobNumber());
                    emp.setPosition(it.getTitle());
                    emp.setAvatar(it.getAvatar());
                    emp.setThirdPartyId(it.getUserid());
                    emp.setMobile(it.getMobile());
                    emp.setEmail(StrUtil.isNotEmpty(it.getOrgEmail()) ? it.getOrgEmail() : it.getEmail());
                    emp.setDeptIdList(it.getDeptIdList().stream().map(Convert::toStr).collect(Collectors.toList()));
                    emp.setCompanyId(companyId);
                    return emp;
                }).collect(Collectors.toList());
                listUserResponses.addAll(collect);
                // 递归查询全部数据
                if (result.getHasMore()) {
                    userList(listUserResponses, orgId, companyId, accessToken, result.getNextCursor());
                }
            } else {
                log.warn("company {} get user list [orgId={}] list error ==>> [{}] {}", companyId, orgId, rsp.getErrcode(), rsp.getErrmsg());
                // throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
            }
        } catch (ApiException e) {
            log.error("company {} get user list [orgId={}] list error ==>> [{}] {}", companyId, orgId, e.getErrCode(), e.getErrMsg());
            // throw new BusinessException(ThirdPartyResultCode.SYNC_ERROR);
        }
    }

    private String getAccessToken(Long companyId) {
        // 查询AKSK
        JSONObject secret = getSecret(companyId, ThirdPartyConstant.DINGTALK);
        ThirdPartyAkSk.DingTalk dingTalk = secret.toJavaObject(ThirdPartyAkSk.DingTalk.class);
        return dingOpenApiService.getToken(companyId, dingTalk.getAppKey(), dingTalk.getAppSecret());
    }

}
