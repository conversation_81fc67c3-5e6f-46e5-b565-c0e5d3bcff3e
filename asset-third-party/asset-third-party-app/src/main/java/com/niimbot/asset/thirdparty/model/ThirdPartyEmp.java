package com.niimbot.asset.thirdparty.model;

import java.util.List;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/11/10 15:17
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AbstractEmpDto", description = "抽象员工")
public class ThirdPartyEmp {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "工号")
    private String jobNum;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "thirdPartyId 该用户在第三方组织架构中企业下的唯一标识")
    private String thirdPartyId;

    @ApiModelProperty(value = "账户")
    private String account;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "部门ID")
    private List<String> deptIdList;

    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    @ApiModelProperty(value = "是否超管")
    private Boolean admin;

    @ApiModelProperty("第三方类型")
    private String thirdPartyType;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ThirdPartyEmp that = (ThirdPartyEmp) o;
        return Objects.equals(thirdPartyId, that.thirdPartyId) &&
                Objects.equals(thirdPartyType, that.thirdPartyType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(thirdPartyId, thirdPartyType);
    }
}
