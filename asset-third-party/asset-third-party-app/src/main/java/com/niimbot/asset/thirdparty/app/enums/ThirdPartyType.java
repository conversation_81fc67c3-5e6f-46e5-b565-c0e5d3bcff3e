package com.niimbot.asset.thirdparty.app.enums;

/**
 * 第三方系统类型枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
public enum ThirdPartyType {
    
    /**
     * 炎黄BPM系统
     */
    YANHUANG("yanhuang", "炎黄BPM系统"),
    
    /**
     * 钉钉
     */
    DINGTALK("dingtalk", "钉钉"),
    
    /**
     * 企业微信
     */
    WECHAT("wechat", "企业微信"),
    
    /**
     * 飞书
     */
    FEISHU("feishu", "飞书");
    
    private final String code;
    private final String name;
    
    ThirdPartyType(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
}