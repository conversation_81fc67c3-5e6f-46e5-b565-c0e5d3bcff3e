package com.niimbot.asset.thirdparty.strategy.feishu;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lark.oapi.service.contact.v3.model.DepartmentEvent;
import com.lark.oapi.service.contact.v3.model.P2DepartmentCreatedV3;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.FeishuCallbackEvent;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;

import org.springframework.stereotype.Component;

import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/15 11:08
 */
@Slf4j
@Component
@CallbackStrategy(eventType = FeishuCallbackEvent.DEPT_CREATE)
public class DeptCreateStrategy implements FeishuCallbackStrategy {

    private final OrgService orgService;
    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    public DeptCreateStrategy(OrgService orgService,
                              AsThirdPartyEmployeeService thirdPartyEmployeeService) {
        this.orgService = orgService;
        this.thirdPartyEmployeeService = thirdPartyEmployeeService;
    }

    @Override
    public boolean handleCallback(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        P2DepartmentCreatedV3 departmentCreatedV3 = content.toJavaObject(P2DepartmentCreatedV3.class);
        if (checkToken(feishu.getVerificationToken(), departmentCreatedV3.getHeader())) {
            DepartmentEvent eventData = departmentCreatedV3.getEvent().getObject();
            // 查询当前数据，防重
            if (orgService.count(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, eventData.getOpenDepartmentId())) > 0) {
                // ignore
                return true;
            }
            // 查询父节点
            AsOrg pOrg = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, eventData.getParentDepartmentId()));

            // 查询当前节点
            AsOrg org = new AsOrg();
            org.setId(IdUtils.getId());
            org.setExternalOrgId(eventData.getOpenDepartmentId());
            org.setOrgName(eventData.getName());
            if (ObjectUtil.isNotNull(pOrg)) {
                org.setLevel(pOrg.getLevel() + 1);
                org.setPaths(pOrg.getPaths() + pOrg.getId() + ",");
                org.setPid(pOrg.getId());
                org.setExternalPid(pOrg.getExternalOrgId());
                // 查询该部门公司的所属公司
                List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
                Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                org.setCompanyOwner(orgCompanyOwner);
            } else {
                org.setLevel(0);
                org.setPaths("0,");
                org.setPid(0L);
                org.setExternalPid("0");
                org.setCompanyOwner(org.getId());
            }
            org.setCompanyId(companyId);
            // 部门主管
            String leaderUserId = eventData.getLeaderUserId();
            if (StrUtil.isNotEmpty(leaderUserId)) {
                AsThirdPartyEmployee thirdPartyEmployee = thirdPartyEmployeeService.getOne(Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .select(AsThirdPartyEmployee::getEmployeeId)
                        .eq(AsThirdPartyEmployee::getUserId, leaderUserId)
                        .eq(AsThirdPartyEmployee::getType, getType())
                        .eq(AsThirdPartyEmployee::getCompanyId, companyId));
                if (thirdPartyEmployee != null) {
                    org.setDirector(ListUtil.of(thirdPartyEmployee.getEmployeeId()));
                }
            }
            orgService.save(org);
            return true;
        } else {
            log.error("companyId = {}, check token error", companyId);
            return false;
        }
    }

}
