package com.niimbot.asset.thirdparty.strategy.loginauth;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkcontact_1_0.Client;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponse;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.request.OapiV2UserGetuserinfoRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.properties.ThirdPartyProperties;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.ThirdPartyUser;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DingTalkUserAuthStrategy extends UserAuthStrategy {

    @Resource
    private DingOpenApiService dingOpenApiService;

    private static final String PROTOCOL = "https";

    private static final String REGION_ID = "central";

    private static com.aliyun.dingtalkoauth2_1_0.Client authClient() {
        Config config = new Config();
        config.setProtocol(PROTOCOL);
        config.setRegionId(REGION_ID);
        try {
            return new com.aliyun.dingtalkoauth2_1_0.Client(config);
        } catch (Exception e) {
            log.error("Build DingTalk Auth Client Error", e);
            throw new RuntimeException(e);
        }
    }

    private static Client contactClient() {
        Config config = new Config();
        config.setProtocol(PROTOCOL);
        config.setRegionId(REGION_ID);
        try {
            return new Client(config);
        } catch (Exception e) {
            log.error("Build DingTalk Contact Client Error", e);
            throw new RuntimeException(e);
        }
    }

    private static <Request, Response> Response handRequest(RequestFunction<Request, Response> requestFunction, Request request) {
        Response response;
        try {
            response = requestFunction.handle(request);
        } catch (Exception e) {
            log.error("DingTalk Request Error", e);
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        if (Objects.isNull(response)) {
            log.error("DingTalk Response Is Null");
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        return response;
    }

    public String accessToken(String authCode, String appKey, String appSecret) {
        com.aliyun.dingtalkoauth2_1_0.Client client = authClient();
        GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                //应用基础信息-应用信息的AppKey,请务必替换为开发的应用AppKey
                .setClientId(appKey)
                //应用基础信息-应用信息的AppSecret，,请务必替换为开发的应用AppSecret
                .setClientSecret(appSecret)
                .setCode(authCode)
                .setGrantType("authorization_code");
        GetUserTokenResponse getUserTokenResponse = handRequest(client::getUserToken, getUserTokenRequest);
        return getUserTokenResponse.getBody().getAccessToken();
    }

    @Override
    public String webAuth(Long companyId, String authCode) {
        JSONObject config = getCompanyConfig(companyId);
        ThirdPartyAkSk.DingTalk dingTalk = config.toJavaObject(ThirdPartyAkSk.DingTalk.class);
        String accessToken = accessToken(authCode, dingTalk.getAppKey(), dingTalk.getAppSecret());
        ThirdPartyUser thirdPartyUser = commonAuth(accessToken);
        String niimbotAccessToken = dingOpenApiService.getToken(companyId, dingTalk.getAppKey(), dingTalk.getAppSecret());
        return dingOpenApiService.getUserIdByUnionId(thirdPartyUser.getUniqueId(), niimbotAccessToken);
    }

    public com.aliyun.dingtalkoauth2_1_0.Client innerH5AuthClient() {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        try {
            return new com.aliyun.dingtalkoauth2_1_0.Client(config);
        } catch (Exception e) {
            log.error("Build DingTalk innerH5AuthClient Error", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String h5Auth(Long companyId, String authCode) {
        ThirdPartyAkSk.DingTalk dingTalk = getCompanyConfig(companyId).toJavaObject(ThirdPartyAkSk.DingTalk.class);
        com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
                .setAppKey(dingTalk.getAppKey())
                .setAppSecret(dingTalk.getAppSecret());
        GetAccessTokenResponse accessTokenResponse = handRequest(innerH5AuthClient()::getAccessToken, getAccessTokenRequest);
        String accessToken = accessTokenResponse.getBody().getAccessToken();
        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        req.setCode(authCode);
        OapiV2UserGetuserinfoResponse response = handRequest(request -> new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo").execute(request, accessToken), req);
        return response.getResult().getUserid();
    }

    public ThirdPartyUser commonAuth(String accessToken) {
        com.aliyun.dingtalkcontact_1_0.Client client = contactClient();
        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.setXAcsDingtalkAccessToken(accessToken);
        //获取用户个人信息，如需获取当前授权人的信息，unionId参数必须传me
        GetUserResponse getUserResponse = handRequest(request -> client.getUserWithOptions("me", request, new RuntimeOptions()), getUserHeaders);
        GetUserResponseBody body = getUserResponse.getBody();
        return new ThirdPartyUser().setType(getType())
                .setUniqueId(body.getUnionId())
                .setOpenId(body.getOpenId())
                .setNickname(body.getNick())
                .setAvatarUrl(body.getAvatarUrl())
                .setMobile(body.getMobile())
                .setEmail(body.getEmail());
    }

    @Override
    public ThirdPartyUser pcAuth(String authCode) {
        ThirdPartyProperties.AppInfo appInfo = getAppInfo();
        String accessToken = accessToken(authCode, appInfo.getAppKey(), appInfo.getAppSecret());
        return commonAuth(accessToken);
    }

    @Override
    public ThirdPartyUser appAuth(String authCode) {
        ThirdPartyProperties.AppInfo appInfo = getAppInfo();
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
        req.setTmpAuthCode(authCode);
        OapiSnsGetuserinfoBycodeResponse response = null;
        try {
            response = client.execute(req, appInfo.getAppKey(), appInfo.getAppSecret());
        } catch (ApiException e) {
            log.error("APP User DingTalk Auth Error", e);
        }
        if (Objects.isNull(response)) {
            throw new BusinessException(ThirdPartyResultCode.SCAN_QR_ERROR);
        }
        // unionId openId nickname
        OapiSnsGetuserinfoBycodeResponse.UserInfo userInfo = response.getUserInfo();
        return new ThirdPartyUser()
                .setType(getType())
                .setNickname(userInfo.getNick())
                .setUniqueId(userInfo.getUnionid())
                .setOpenId(userInfo.getOpenid());
    }

    @Override
    public String getType() {
        return ThirdPartyConstant.DINGTALK;
    }

}
