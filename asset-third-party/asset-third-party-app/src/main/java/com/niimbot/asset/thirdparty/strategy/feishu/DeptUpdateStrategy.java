package com.niimbot.asset.thirdparty.strategy.feishu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lark.oapi.service.contact.v3.model.DepartmentEvent;
import com.lark.oapi.service.contact.v3.model.P2DepartmentUpdatedV3;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.FeishuCallbackEvent;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/15 11:08
 */
@Slf4j
@Component
@CallbackStrategy(eventType = FeishuCallbackEvent.DEPT_UPDATE)
public class DeptUpdateStrategy implements FeishuCallbackStrategy {

    private final OrgService orgService;
    private final DeptCreateStrategy deptCreateStrategy;
    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    public DeptUpdateStrategy(OrgService orgService, DeptCreateStrategy deptCreateStrategy,
                              AsThirdPartyEmployeeService thirdPartyEmployeeService) {
        this.orgService = orgService;
        this.deptCreateStrategy = deptCreateStrategy;
        this.thirdPartyEmployeeService = thirdPartyEmployeeService;
    }

    @Override
    public boolean handleCallback(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        P2DepartmentUpdatedV3 departmentUpdatedV3 = content.toJavaObject(P2DepartmentUpdatedV3.class);
        if (checkToken(feishu.getVerificationToken(), departmentUpdatedV3.getHeader())) {
            DepartmentEvent eventData = departmentUpdatedV3.getEvent().getObject();
            // 删除也会发更新消息
            if (BooleanUtil.isTrue(eventData.getStatus().getIsDeleted())) {
                // ignore
                return true;
            }
            DepartmentEvent oldObject = departmentUpdatedV3.getEvent().getOldObject();
            AsOrg org = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, eventData.getOpenDepartmentId()));
            if (ObjectUtil.isNull(org)) {
                log.warn("update company {} org {} has not exists ", companyId, eventData.getOpenDepartmentId());
                // 新建组织
                deptCreateStrategy.handleCallback(companyId, feishu, content);
            } else {
                if (StrUtil.isNotEmpty(eventData.getParentDepartmentId())) {
                    AsOrg pOrg = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                            .eq(AsOrg::getCompanyId, companyId)
                            .eq(AsOrg::getExternalOrgId, eventData.getParentDepartmentId()));
                    org.setExternalOrgId(eventData.getOpenDepartmentId());
                    if (ObjectUtil.isNotNull(pOrg)) {
                        org.setLevel(pOrg.getLevel() + 1);
                        org.setPaths(pOrg.getPaths() + pOrg.getId() + ",");
                        org.setPid(pOrg.getId());
                        org.setExternalPid(pOrg.getExternalOrgId());

                        // 查询该部门公司的所属公司
                        List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
                        Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                        org.setCompanyOwner(orgCompanyOwner);
                    } else {
                        org.setLevel(0);
                        org.setPaths("0,");
                        org.setPid(0L);
                        org.setExternalPid("0");
                        org.setCompanyOwner(org.getId());
                    }
                    org.setCompanyId(companyId);
                    // 所有子Path
                    String sonPath = org.getPaths() + org.getId() + ",";
                    List<AsOrg> sonList = orgService.list(new QueryWrapper<AsOrg>().lambda()
                            .eq(AsOrg::getCompanyId, companyId)
                            .likeRight(AsOrg::getPaths, sonPath));
                    List<AsOrg> updateSonList = new ArrayList<>();
                    for (AsOrg son : sonList) {
                        String p = son.getPaths().replace(sonPath, org.getPaths() + org.getId() + ",");
                        int level = p.split(",").length - 1;
                        AsOrg updateSon = new AsOrg();
                        updateSon.setId(son.getId());
                        updateSon.setLevel(level);
                        updateSon.setPaths(p);
                        // 该组织为部门时并且该子节点也是部门时，需要重写所属公司
                        if (AssetConstant.ORG_TYPE_DEPT.equals(son.getOrgType())
                                && AssetConstant.ORG_TYPE_DEPT.equals(org.getOrgType())) {
                            List<Long> ids = Convert.toList(Long.class, p.split(","));
                            Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                            updateSon.setCompanyOwner(orgCompanyOwner);
                        }
                        updateSonList.add(updateSon);
                    }
                    // 更新当前数据
                    orgService.updateById(org);
                    // 更新子节点数据
                    orgService.updateBatchById(updateSonList);
                }

                // 部门名称，仅当该字段发生变更时传递
                if (!ObjectUtil.equals(eventData.getName(), oldObject.getName())) {
                    org.setOrgName(eventData.getName());
                    // 更新当前数据
                    orgService.update(new LambdaUpdateWrapper<AsOrg>().set(AsOrg::getOrgName, eventData.getName())
                            .eq(AsOrg::getCompanyId, companyId)
                            .eq(AsOrg::getId, org.getId()));
                    SpringUtil.getBean(CacheOrgStrategy.class).evictCache(org.getId());
                }
                // 部门主管
                if (!ObjectUtil.equals(eventData.getLeaderUserId(), oldObject.getLeaderUserId())) {
                    if (StrUtil.isEmpty(eventData.getLeaderUserId())) {
                        orgService.update(new LambdaUpdateWrapper<AsOrg>().set(AsOrg::getDirector, JSONArray.toJSONString(new ArrayList<>()))
                                .eq(AsOrg::getCompanyId, companyId)
                                .eq(AsOrg::getId, org.getId()));
                    } else {
                        AsThirdPartyEmployee thirdPartyEmployee = thirdPartyEmployeeService.getOne(Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                                .select(AsThirdPartyEmployee::getEmployeeId)
                                .eq(AsThirdPartyEmployee::getUserId, eventData.getLeaderUserId())
                                .eq(AsThirdPartyEmployee::getType, getType())
                                .eq(AsThirdPartyEmployee::getCompanyId, companyId));
                        if (thirdPartyEmployee != null) {
                            orgService.update(new LambdaUpdateWrapper<AsOrg>()
                                    .set(AsOrg::getDirector, JSONArray.toJSONString(ListUtil.of(thirdPartyEmployee.getEmployeeId())))
                                    .eq(AsOrg::getCompanyId, companyId)
                                    .eq(AsOrg::getId, org.getId()));
                        } else {
                            orgService.update(new LambdaUpdateWrapper<AsOrg>().set(AsOrg::getDirector, JSONArray.toJSONString(new ArrayList<>()))
                                    .eq(AsOrg::getCompanyId, companyId)
                                    .eq(AsOrg::getId, org.getId()));
                        }
                    }
                }
            }
            return true;
        } else {
            log.error("companyId = {}, check token error", companyId);
            return false;
        }
    }

}
