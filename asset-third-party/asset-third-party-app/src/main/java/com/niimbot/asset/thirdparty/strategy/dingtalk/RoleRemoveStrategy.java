package com.niimbot.asset.thirdparty.strategy.dingtalk;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.activiti.model.ActApproveRole;
import com.niimbot.asset.activiti.model.ActApproveRoleMember;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.LABEL_CONF_DEL)
public class RoleRemoveStrategy implements DingCallbackStrategy {

    private static final Logger log = LoggerFactory.getLogger(RoleRemoveStrategy.class);
    @Resource
    private ActApproveRoleService approveRoleService;

    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Resource
    private CompanySettingService companySettingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        log.info("RoleRemoveStrategy handleCallback");
        CompanySwitch setting = companySettingService.getSwitchSettingWithCache(companyId);
        if (!setting.getEnableSyncApproveRole()) {
            return true;
        }

        if (!content.containsKey("LabelIdList")) {
            return true;
        }
        List<Long> ids = content.getJSONArray("LabelIdList").toJavaList(Long.class);
        List<String> extIds = ids.stream().map(String::valueOf).collect(Collectors.toList());
        List<Long> systemRoleIds = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .select(ActApproveRole::getId)
                        .in(ActApproveRole::getExternalId, extIds)
        ).stream().map(ActApproveRole::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(systemRoleIds)) {
            return true;
        }
        approveRoleService.remove(
                Wrappers.lambdaUpdate(ActApproveRole.class)
                        .in(ActApproveRole::getExternalId, ids.stream().map(String::valueOf).collect(Collectors.toList()))
        );
        approveRoleMemberService.remove(
                Wrappers.lambdaUpdate(ActApproveRoleMember.class)
                        .in(ActApproveRoleMember::getApproveRoleId, systemRoleIds)
        );
        return true;
    }
}
