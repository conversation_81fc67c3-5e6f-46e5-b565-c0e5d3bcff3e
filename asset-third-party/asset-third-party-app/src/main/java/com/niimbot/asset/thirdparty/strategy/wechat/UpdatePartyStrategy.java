package com.niimbot.asset.thirdparty.strategy.wechat;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.WeChatCallbackEvent;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;

/**
 * <AUTHOR>
 * @since 2021/11/19 10:36
 */
@Slf4j
@Component
@CallbackStrategy(eventType = WeChatCallbackEvent.UPDATE_PARTY)
public class UpdatePartyStrategy implements WeChatCallbackStrategy {

    private final OrgService orgService;

    private final CreatePartyStrategy createPartyStrategy;

    @Autowired
    public UpdatePartyStrategy(OrgService orgService, CreatePartyStrategy createPartyStrategy) {
        this.orgService = orgService;
        this.createPartyStrategy = createPartyStrategy;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, WxCpTpXmlMessage xmlMessage) {
        if (log.isDebugEnabled()) {
            log.debug("update_party: " + xmlMessage);
        }
        // 只会在ParentId变更时触发，并且部门属性只回调Id/ParentId两个字段
        WxCpDepart partyDetails = partyDetails(companyId, xmlMessage.getId());
        xmlMessage.setName(partyDetails.getName());
        AsOrg org = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                .eq(AsOrg::getCompanyId, companyId)
                .eq(AsOrg::getExternalOrgId, xmlMessage.getId()));
        if (ObjectUtil.isNull(org)) {
            log.warn("update company {} org {} has not exists ", companyId, xmlMessage.getId());
            if (StrUtil.isAllNotEmpty(xmlMessage.getName(), xmlMessage.getParentId())) {
                // 新建组织
                createPartyStrategy.handleCallback(companyId, xmlMessage);
            }
        } else {
            // 父部门id，仅当该字段发生变更时传递
            if (StrUtil.isNotEmpty(xmlMessage.getParentId())) {
                AsOrg pOrg = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getExternalOrgId, Convert.toStr(xmlMessage.getParentId())));
                org.setExternalOrgId(Convert.toStr(xmlMessage.getId()));
                if (ObjectUtil.isNotNull(pOrg)) {
                    org.setLevel(pOrg.getLevel() + 1);
                    org.setPaths(pOrg.getPaths() + pOrg.getId() + ",");
                    org.setPid(pOrg.getId());
                    org.setExternalPid(pOrg.getExternalOrgId());

                    // 查询该部门公司的所属公司
                    List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
                    Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                    org.setCompanyOwner(orgCompanyOwner);
                } else {
                    org.setLevel(0);
                    org.setPaths("0,");
                    org.setPid(0L);
                    org.setExternalPid("0");
                    org.setCompanyOwner(org.getId());
                }
                org.setCompanyId(companyId);
                // 所有子Path
                String sonPath = org.getPaths() + org.getId() + ",";
                List<AsOrg> sonList = orgService.list(new QueryWrapper<AsOrg>().lambda()
                        .eq(AsOrg::getCompanyId, companyId)
                        .likeRight(AsOrg::getPaths, sonPath));
                List<AsOrg> updateSonList = new ArrayList<>();
                for (AsOrg son : sonList) {
                    String p = son.getPaths().replace(sonPath, org.getPaths() + org.getId() + ",");
                    int level = p.split(",").length - 1;
                    AsOrg updateSon = new AsOrg();
                    updateSon.setId(son.getId());
                    updateSon.setLevel(level);
                    updateSon.setPaths(p);
                    // 该组织为部门时并且该子节点也是部门时，需要重写所属公司
                    if (AssetConstant.ORG_TYPE_DEPT.equals(son.getOrgType())
                            && AssetConstant.ORG_TYPE_DEPT.equals(org.getOrgType())) {
                        List<Long> ids = Convert.toList(Long.class, p.split(","));
                        Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                        updateSon.setCompanyOwner(orgCompanyOwner);
                    }
                    updateSonList.add(updateSon);
                }
                // 更新当前数据
                orgService.updateById(org);
                // 更新子节点数据
                orgService.updateBatchById(updateSonList);
            }
            // 部门名称，仅当该字段发生变更时传递
            if (StrUtil.isNotEmpty(xmlMessage.getName())) {
                org.setOrgName(xmlMessage.getName());
                // 更新当前数据
                orgService.update(new LambdaUpdateWrapper<AsOrg>().set(AsOrg::getOrgName, xmlMessage.getName())
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getId, org.getId()));
                SpringUtil.getBean(CacheOrgStrategy.class).evictCache(org.getId());
            }

        }
        return true;
    }
}
