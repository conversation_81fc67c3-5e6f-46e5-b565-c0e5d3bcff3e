package com.niimbot.asset.thirdparty.strategy.others;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.strategy.TypeSign;

/**
 * <AUTHOR>
 * @date 2024/7/11 9:25
 */
public interface OthersCallbackStrategy extends TypeSign {

    boolean handleCallback(Long companyId, JSONObject content);

    @Override
    default String getType() {
        return ThirdPartyConstant.OTHERS;
    }

}
