package com.niimbot.asset.thirdparty.strategy.wechat;

import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.restops.RestOps;
import com.niimbot.asset.thirdparty.strategy.TypeSign;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:43
 */
public interface WeChatCallbackStrategy extends TypeSign {

    boolean handleCallback(Long companyId, WxCpTpXmlMessage message);

    default WxCpDepart partyDetails(Long companyId, String partyId) {
        WxCpService service = SpringUtil.getBean(AsThirdPartyService.class).getWeChatService(companyId);
        return RestOps.handle(() -> service.getDepartmentService().get(Convert.toLong(partyId))).get();
    }

    default WxCpUser userDetails(Long companyId, String userId) {
        WxCpService service = SpringUtil.getBean(AsThirdPartyService.class).getWeChatService(companyId);
        return RestOps.handle(() -> service.getUserService().getById(userId)).get();
    }

    @Override
    default String getType() {
        return ThirdPartyConstant.WECHAT;
    }
}
