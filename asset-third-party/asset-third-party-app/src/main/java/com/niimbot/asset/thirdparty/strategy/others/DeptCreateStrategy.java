package com.niimbot.asset.thirdparty.strategy.others;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.event.OrgCO;
import com.niimbot.asset.thirdparty.event.OthersCallbackEvent;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Since 2024-10-14
 */
@Slf4j
@Component(value = "othersDeptCreateStrategy")
@CallbackStrategy(eventType = OthersCallbackEvent.DEPT_CREATE)
public class DeptCreateStrategy implements OthersCallbackStrategy {

    private final OrgService orgService;

    public DeptCreateStrategy(OrgService orgService) {
        this.orgService = orgService;
    }

    @Override
    public boolean handleCallback(Long companyId, JSONObject content) {
        OrgCO orgCO = content.toJavaObject(OrgCO.class);
        if (orgCO != null) {
            // 查询当前数据，防重
            if (orgService.count(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, orgCO.getId())) > 0) {
                // ignore
                return true;
            }
            // 查询父节点
            AsOrg pOrg = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, orgCO.getParentId()));

            // 查询当前节点
            AsOrg org = new AsOrg();
            org.setId(IdUtils.getId());
            org.setExternalOrgId(orgCO.getId());
            org.setOrgName(orgCO.getName());
            org.setOrgCode(orgCO.getCode());
            if (ObjectUtil.isNotNull(pOrg)) {
                org.setLevel(pOrg.getLevel() + 1);
                org.setPaths(pOrg.getPaths() + pOrg.getId() + ",");
                org.setPid(pOrg.getId());
                org.setExternalPid(pOrg.getExternalOrgId());
                // 查询该部门公司的所属公司
                List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
                Long orgCompanyOwner = orgService.getOrgCompanyOwner(ids);
                org.setCompanyOwner(orgCompanyOwner);
            } else {
                org.setLevel(0);
                org.setPaths("0,");
                org.setPid(0L);
                org.setExternalPid("0");
                org.setCompanyOwner(org.getId());
            }
            org.setCompanyId(companyId);
            orgService.save(org);
            return true;
        } else {
            log.error("companyId = {}, orgCO is null", companyId);
            return false;
        }
    }
}
