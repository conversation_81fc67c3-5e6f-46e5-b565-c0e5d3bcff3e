package com.niimbot.asset.thirdparty.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.thirdparty.model.AsThirdParty;
import com.niimbot.asset.thirdparty.restops.RestOps;
import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.service.WeChatCallbackService;
import com.niimbot.asset.thirdparty.strategy.CallbackContext;
import com.niimbot.thirdparty.WeChatAgent;
import com.niimbot.thirdparty.WeChatBindDto;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpTpAuthInfo;
import me.chanjar.weixin.cp.bean.WxCpTpPermanentCodeInfo;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeChatCallBackServiceImpl implements WeChatCallbackService, InitializingBean {

    private final CallbackContext callbackContext;

    private final AsThirdPartyService thirdPartyService;

    private final WxCpTpService assetWxCpTpService;

    /**
     * 处理认证授权等消息
     */
    private final Map<String, Consumer<WxCpTpXmlMessage>> wechatAuthCallBack = new ConcurrentHashMap<>(6);

    @Override
    public boolean handle3rdPartyMessage(WxCpTpXmlMessage message) {
        log.info("handle3rdPartyMessage WxCpTpXmlMessage[{}]", JSONObject.toJSONString(message));
        // 处理认证等消息
        if (StrUtil.isNotBlank(message.getInfoType()) && wechatAuthCallBack.containsKey(message.getInfoType())) {
            wechatAuthCallBack.get(message.getInfoType()).accept(message);
            return true;
        }
        // 处理通讯录消息
        if (StrUtil.isNotBlank(message.getChangeType())) {
            Long companyId = thirdPartyService.getWechatCompany(message.getToUserName());
            if (Objects.isNull(companyId)) {
                log.info("未绑定企业的消息不处理");
                return true;
            }
            try {
                LoginUserDto userDto = new LoginUserDto();
                userDto.setCusUser(new CusUserDto()
                        .setIsAdmin(true)
                        .setCompanyId(companyId));
                LoginUserThreadLocal.set(userDto);
                callbackContext.executeWeChat(companyId, message);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                LoginUserThreadLocal.remove();
            }
        }
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 刷新套件Token
        wechatAuthCallBack.put("suite_ticket", message -> {
            String suiteTicket = message.getSuiteTicket();
            assetWxCpTpService.setSuiteTicket(suiteTicket);
            log.info("assetWxCpTpService setSuiteTicket [{}] success", suiteTicket);
        });
        // 代应用企业扫码授权
        wechatAuthCallBack.put("create_auth", message -> {
            String authCode = message.getAuthCode();
            WxCpTpPermanentCodeInfo permanentCodeInfo = RestOps.handle(() -> assetWxCpTpService.getPermanentCodeInfo(authCode)).get();
            // 写入应用信息
            String secret = permanentCodeInfo.getPermanentCode();
            WxCpTpPermanentCodeInfo.AuthCorpInfo authCorpInfo = permanentCodeInfo.getAuthCorpInfo();
            String encryptCorpId = authCorpInfo.getCorpId();
            List<WxCpTpPermanentCodeInfo.Agent> agents = permanentCodeInfo.getAuthInfo().getAgents();
            if (CollUtil.isEmpty(agents)) {
                return;
            }
            AsThirdParty weChatBind = thirdPartyService.getWeChatBind(encryptCorpId);
            if (Objects.isNull(weChatBind)) {
                weChatBind = new AsThirdParty();
            }
            WxCpTpPermanentCodeInfo.Agent agent = agents.get(0);
            Integer agentId = agent.getAgentId();
            WeChatBindDto weChatBindDto = new WeChatBindDto().setCorpId(encryptCorpId).setSecret(secret).setUserAuthAgent(
                    new WeChatAgent().setAgentId(String.valueOf(agentId)).setCorpId(encryptCorpId).setAgentSecret(secret)
            );
            weChatBind.setType(ThirdPartyConstant.WECHAT).setForm((JSONObject) JSONObject.toJSON(weChatBindDto));
            thirdPartyService.saveOrUpdate(weChatBind);
        });
        // 代应用企业授权变更
        wechatAuthCallBack.put("change_auth", message -> {
            String authCorpId = message.getAuthCorpId();
            AsThirdParty thirdParty = thirdPartyService.getWeChatBind(authCorpId);
            if (Objects.isNull(thirdParty)) {
                return;
            }
            WxCpTpAuthInfo authInfo = RestOps.handle(() -> assetWxCpTpService.getAuthInfo(authCorpId, thirdParty.getForm().toJavaObject(ThirdPartyAkSk.WeChat.class).getSecret())).get();
            // TODO: 2022/8/29 权限描述
            List<WxCpTpAuthInfo.Agent> agents = authInfo.getAuthInfo().getAgents();
        });
        // 代应用企业取消授权
        wechatAuthCallBack.put("cancel_auth", message -> {
            String authCorpId = message.getAuthCorpId();
            thirdPartyService.unbindWechat(authCorpId);
        });
        // 服务商重置企业代应用的secret
        wechatAuthCallBack.put("reset_permanent_code", message -> wechatAuthCallBack.get("create_auth").accept(message));
    }
}
