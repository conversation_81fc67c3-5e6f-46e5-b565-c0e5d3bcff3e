package com.niimbot.asset.thirdparty.strategy.dingtalk;


import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:45
 */
@Slf4j
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.ORG_DEPT_CREATE)
public class OrgDeptCreateStrategy implements DingCallbackStrategy {

    private final OrgService orgService;

    private final DingOpenApiService dingOpenApiService;

    @Autowired
    public OrgDeptCreateStrategy(OrgService orgService,
                                 DingOpenApiService dingOpenApiService) {
        this.orgService = orgService;
        this.dingOpenApiService = dingOpenApiService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        if (log.isDebugEnabled()) {
            // org_dept_create: {"CorpId":"ding8b2d6adfb93a210f35c2f4657eb6378f","EventType":"org_dept_create","DeptId":[*********],"TimeStamp":"1637114921920"}
            log.debug("org_dept_create: " + content);
        }
        List<Long> deptIds = content.getJSONArray("DeptId").toJavaList(Long.class);
        for (Long deptId : deptIds) {
            AsOrg org = dingOpenApiService.getDept(deptId, companyId, accessToken);
            orgService.save(org);
        }

        return true;
    }

}
