package com.niimbot.asset.thirdparty.strategy.dingtalk;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsSyncChange;
import com.niimbot.asset.system.service.CompanyAssetService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.SyncChangeService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:45
 */
@Slf4j
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.ORG_DEPT_REMOVE)
public class OrgDeptRemoveStrategy implements DingCallbackStrategy {

    @Resource
    private OrgService orgService;

    @Resource
    private CompanyAssetService companyAssetService;

    @Resource
    private SyncChangeService syncChangeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        if (log.isDebugEnabled()) {
            // org_dept_create: {"CorpId":"ding8b2d6adfb93a210f35c2f4657eb6378f","EventType":"org_dept_create","DeptId":[*********],"TimeStamp":"1637114921920"}
            log.debug("org_dept_remove: " + content);
        }
        List<Long> deptIds = content.getJSONArray("DeptId").toJavaList(Long.class);
        for (Long deptId : deptIds) {
            AsOrg one = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                    .eq(AsOrg::getCompanyId, companyId)
                    .eq(AsOrg::getExternalOrgId, deptId));
            if (ObjectUtil.isNotNull(one)) {
                boolean remove = orgService.removeById(one.getId());
                if (remove) {
                    // 使用组织
                    List<Long> useOrgIds = companyAssetService.checkUseOrg(ListUtil.of(one.getId()), companyId);
                    // 管理组织
                    List<Long> orgOwnerIds = companyAssetService.checkOrgOwner(ListUtil.of(one.getId()), companyId);
                    useOrgIds.addAll(orgOwnerIds);
                    if (useOrgIds.size() > 0) {
                        // 写入异动记录
                        AsSyncChange syncChange = new AsSyncChange();
                        AsSyncChange dingSyncChange = syncChange.setResId(one.getId()).setCompanyId(companyId).setFromOrg(new ArrayList<>()).setToOrg(new ArrayList<>()).setType(3);
                        syncChangeService.save(dingSyncChange);
                    }
                }
            }
        }

        return true;
    }

}
