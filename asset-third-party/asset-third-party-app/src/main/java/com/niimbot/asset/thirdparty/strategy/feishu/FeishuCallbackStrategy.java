package com.niimbot.asset.thirdparty.strategy.feishu;

import com.alibaba.fastjson.JSONObject;
import com.lark.oapi.event.model.Header;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.strategy.TypeSign;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2024/7/11 9:25
 */
public interface FeishuCallbackStrategy extends TypeSign {

    boolean handleCallback(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content);

    default boolean checkToken(String token, Header header) {
        return StrUtil.equals(token, header.getToken());
    }

    @Override
    default String getType() {
        return ThirdPartyConstant.FEISHU;
    }

}
