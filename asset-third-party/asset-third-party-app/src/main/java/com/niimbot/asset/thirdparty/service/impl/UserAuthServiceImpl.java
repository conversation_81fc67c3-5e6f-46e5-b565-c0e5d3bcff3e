package com.niimbot.asset.thirdparty.service.impl;

import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.thirdparty.service.UserAuthService;
import com.niimbot.asset.thirdparty.strategy.loginauth.UserAuthStrategy;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.ThirdPartyUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserAuthServiceImpl implements UserAuthService {

    private final Map<String, UserAuthStrategy> userAuthStrategyMap;

    public UserAuthServiceImpl(List<UserAuthStrategy> authStrategies) {
        this.userAuthStrategyMap = authStrategies.stream().collect(Collectors.toMap(UserAuthStrategy::getType, v -> v));
    }

    @Override
    public UserAuthStrategy getUserAuthStrategy(String provider) {
        if (!userAuthStrategyMap.containsKey(provider)) {
            throw new BusinessException(ThirdPartyResultCode.NONSUPPORT_AUTH_WAY);
        }
        return userAuthStrategyMap.get(provider);
    }

    @Override
    public ThirdPartyUser socialAuth(String provider, String authCode, String terminal) {
        UserAuthStrategy userAuthStrategy = getUserAuthStrategy(provider);
        return userAuthStrategy.socialAuth(authCode, terminal);
    }

    @Override
    public String webAuth(Long companyId, String type, String authCode) {
        UserAuthStrategy userAuthStrategy = getUserAuthStrategy(type);
        return userAuthStrategy.webAuth(companyId, authCode);
    }

    @Override
    public String h5Auth(Long companyId, String type, String authCode) {
        UserAuthStrategy userAuthStrategy = getUserAuthStrategy(type);
        return userAuthStrategy.h5Auth(companyId, authCode);
    }
}
