package com.niimbot.asset.thirdparty.strategy.wechat;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsSyncChange;
import com.niimbot.asset.system.service.CompanyAssetService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.SyncChangeService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.WeChatCallbackEvent;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;

/**
 * <AUTHOR>
 * @since 2021/11/19 10:49
 */
@Slf4j
@Component
@CallbackStrategy(eventType = WeChatCallbackEvent.DELETE_PARTY)
public class DeletePartyStrategy implements WeChatCallbackStrategy {

    @Resource
    private OrgService orgService;

    @Resource
    private CompanyAssetService companyAssetService;

    @Resource
    private SyncChangeService syncChangeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, WxCpTpXmlMessage xmlMessage) {
        if (log.isDebugEnabled()) {
            log.debug("delete_user: " + xmlMessage);
        }

        AsOrg one = orgService.getOne(new LambdaQueryWrapper<AsOrg>()
                .eq(AsOrg::getCompanyId, companyId)
                .eq(AsOrg::getExternalOrgId, xmlMessage.getId()));
        if (ObjectUtil.isNotNull(one)) {
            boolean remove = orgService.removeById(one.getId());
            if (remove) {
                // 使用组织
                List<Long> useOrgIds = companyAssetService.checkUseOrg(ListUtil.of(one.getId()), companyId);
                // 管理组织
                List<Long> orgOwnerIds = companyAssetService.checkOrgOwner(ListUtil.of(one.getId()), companyId);
                useOrgIds.addAll(orgOwnerIds);
                if (!useOrgIds.isEmpty()) {
                    // 写入异动记录
                    AsSyncChange syncChange = new AsSyncChange();
                    AsSyncChange dingSyncChange = syncChange.setResId(one.getId()).setCompanyId(companyId).setFromOrg(new ArrayList<>()).setToOrg(new ArrayList<>()).setType(3);
                    syncChangeService.save(dingSyncChange);
                }

            }
        }

        return true;
    }

}
