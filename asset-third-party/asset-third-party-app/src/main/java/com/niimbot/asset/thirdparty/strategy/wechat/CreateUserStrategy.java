package com.niimbot.asset.thirdparty.strategy.wechat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.WeChatCallbackEvent;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;
import com.niimbot.system.OrgDto;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/19 10:52
 */
@Slf4j
@Component
@CallbackStrategy(eventType = WeChatCallbackEvent.CREATE_USER)
public class CreateUserStrategy extends AbsUserStrategy implements WeChatCallbackStrategy {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, WxCpTpXmlMessage xmlMessage) {
        if (log.isDebugEnabled()) {
            log.debug("create_user: " + xmlMessage);
        }
        WxCpUser userDetails = userDetails(companyId, xmlMessage.getUserID());
        ThirdPartyEmp emp = new ThirdPartyEmp();
        emp.setName(userDetails.getName());
        emp.setPosition(userDetails.getPosition());
        emp.setAvatar(userDetails.getAvatar());
        emp.setThirdPartyId(userDetails.getUserId());
        emp.setAccount(userDetails.getUserId());
        emp.setMobile(userDetails.getMobile());
        emp.setEmail(userDetails.getEmail());
        emp.setDeptIdList(Arrays.stream(userDetails.getDepartIds()).map(Convert::toStr).collect(Collectors.toList()));
        emp.setCompanyId(companyId);
        createEmp(emp.setThirdPartyType(getType()));
        // 更新主管信息
        Long[] departIds = userDetails.getDepartIds();
        Integer[] isLeaderInDept = userDetails.getIsLeaderInDept();
        if (NumberUtil.equals(departIds.length, isLeaderInDept.length) && departIds.length > 0) {
            Optional<AsThirdPartyEmployee> exist = thirdPartyEmployeeService.getOne(getType(), emp.getThirdPartyId(), emp.getCompanyId());
            if (!exist.isPresent()) {
                log.error("insert user, company {} account {} has not exist", emp.getCompanyId(), emp.getAccount());
                return false;
            }
            // 查看UnionId是否存在
            AsThirdPartyEmployee thirdPartyEmployee = exist.get();
            Long userId = thirdPartyEmployee.getEmployeeId();

            // 删除组织主管数据
            List<OrgDto> orgDtoList = orgService.listByDirector(userId, companyId);
            List<AsOrg> updateOrgList = new ArrayList<>();
            for (OrgDto orgDto : orgDtoList) {
                List<Long> director = orgDto.getDirector();
                if (CollUtil.isNotEmpty(director)) {
                    director.remove(userId);
                    AsOrg org = new AsOrg();
                    org.setId(orgDto.getId());
                    org.setDirector(director);
                    updateOrgList.add(org);
                }
            }
            orgService.updateBatchById(updateOrgList);

            // 写入主管
            List<AsOrg> allOrgList = orgService.list(new LambdaQueryWrapper<AsOrg>().eq(AsOrg::getCompanyId, companyId));
            Map<String, AsOrg> orgMap = allOrgList.stream().collect(Collectors.toMap(AsOrg::getExternalOrgId, k -> k));
            updateOrgList.clear();

            for (int i = 0; i < departIds.length; i++) {
                // 是主管写入缓存
                if (orgMap.containsKey(Convert.toStr(departIds[i]))) {
                    AsOrg org = orgMap.get(Convert.toStr(departIds[i]));
                    List<Long> director = org.getDirector();
                    if (isLeaderInDept[i] == 1) {
                        if (director == null) {
                            director = new ArrayList<>();
                        }
                        if (!director.contains(userId)) {
                            director.add(userId);
                        }
                        org.setDirector(director);
                        updateOrgList.add(org);
                    }
                }
            }
            orgService.updateBatchById(updateOrgList);
        }
        return true;
    }

}
