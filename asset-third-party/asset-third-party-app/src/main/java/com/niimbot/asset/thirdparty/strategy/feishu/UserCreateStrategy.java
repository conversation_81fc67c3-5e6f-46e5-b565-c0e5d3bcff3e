package com.niimbot.asset.thirdparty.strategy.feishu;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lark.oapi.service.contact.v3.model.P2UserCreatedV3;
import com.lark.oapi.service.contact.v3.model.UserEvent;
import com.niimbot.asset.system.service.AsCountryCodeService;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.FeishuCallbackEvent;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15 14:14
 */
@Slf4j
@Component
@CallbackStrategy(eventType = FeishuCallbackEvent.USER_CREATE)
public class UserCreateStrategy extends AbsUserStrategy implements FeishuCallbackStrategy {

    private final AsCountryCodeService countryCodeService;

    public UserCreateStrategy(AsCountryCodeService countryCodeService) {
        this.countryCodeService = countryCodeService;
    }

    @Override
    public boolean handleCallback(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        P2UserCreatedV3 userCreatedV3 = content.toJavaObject(P2UserCreatedV3.class);
        if (checkToken(feishu.getVerificationToken(), userCreatedV3.getHeader())) {
            UserEvent eventData = userCreatedV3.getEvent().getObject();
            ThirdPartyEmp emp = new ThirdPartyEmp();
            emp.setName(eventData.getName());
            emp.setJobNum(eventData.getEmployeeNo());
            emp.setPosition(eventData.getJobTitle());
            if (eventData.getAvatar() != null) {
                emp.setAvatar(eventData.getAvatar().getAvatar640());
            }
            emp.setThirdPartyId(eventData.getOpenId());
            if (StrUtil.isNotEmpty(eventData.getMobile())) {
                List<String> countryCodes = countryCodeService.allCountryCode();
                for (String countryCode : countryCodes) {
                    if (eventData.getMobile().startsWith(countryCode)) {
                        emp.setMobile(eventData.getMobile().replace(countryCode, StrUtil.EMPTY));
                        break;
                    }
                }
            }
            emp.setEmail(eventData.getEmail());
            emp.setDeptIdList(Lists.newArrayList(eventData.getDepartmentIds()));
            emp.setCompanyId(companyId);
            createEmp(emp.setThirdPartyType(getType()));
            return true;
        } else {
            log.error("companyId = {}, check token error", companyId);
            return false;
        }
    }

}
