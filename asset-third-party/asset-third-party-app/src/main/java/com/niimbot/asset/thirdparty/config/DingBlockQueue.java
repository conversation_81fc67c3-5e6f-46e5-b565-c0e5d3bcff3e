package com.niimbot.asset.thirdparty.config;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.thirdparty.service.DingCallbackService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/7/3 14:29
 */
@Slf4j
@Component
public class DingBlockQueue extends Thread {

    public static final String BLOCK_QUEUE_DING = "blockQueue:thirdParty:dingtalk";

    private final RBlockingQueue<DingQueueMsg> dingBlockingQueue;
    private final DingCallbackService dingCallbackService;

    @Autowired
    public DingBlockQueue(RedissonClient redissonClient,
                          DingCallbackService dingCallbackService) {
        this.dingBlockingQueue = redissonClient.getBlockingQueue(BLOCK_QUEUE_DING, new JsonJacksonCodec());
        this.dingCallbackService = dingCallbackService;
        this.start();
    }

    @Override
    public void run() {
        while (true) {
            DingQueueMsg queueMsg = null;
            try {
                queueMsg = dingBlockingQueue.take();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
//                Thread.currentThread().interrupt();
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex) {
                    // ignore
                }
            }

            if (queueMsg != null) {
                try {
                    // threadLocal写入公司Id
                    LoginUserDto userDto = new LoginUserDto();
                    userDto.setCusUser(new CusUserDto()
                            .setIsAdmin(true)
                            .setCompanyId(queueMsg.getCompanyId()));
                    LoginUserThreadLocal.set(userDto);
                    log.info("third party ding msg [{}]", JSONObject.toJSONString(queueMsg));
                    dingCallbackService.handle(queueMsg.getCompanyId(), queueMsg.getAccessToken(), queueMsg.getContent());
                } catch (Exception e) {
                    log.error("process ding callback fail, {}", e.getMessage(), e);
                } finally {
                    LoginUserThreadLocal.remove();
                }
            }
        }
    }

    public void put(DingQueueMsg queueMsg) {
        try {
            int size = dingBlockingQueue.size();
            log.info("send ding queue msg, {} waiting", size);
            dingBlockingQueue.put(queueMsg);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
//            Thread.currentThread().interrupt();
            try {
                Thread.sleep(5000);
            } catch (InterruptedException ex) {
                // ignore
            }
        }
    }

    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DingQueueMsg {
        private Long companyId;
        private String accessToken;
        private JSONObject content;
    }

}
