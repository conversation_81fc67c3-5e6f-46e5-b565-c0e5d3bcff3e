package com.niimbot.asset.thirdparty.strategy.loginauth;

import com.alibaba.fastjson.JSONObject;
import com.lark.oapi.Client;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.service.FeishuOpenApiService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.ThirdPartyUser;

import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/11 14:20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FeishuUserAuthStrategy extends UserAuthStrategy {

    private final FeishuOpenApiService feishuOpenApiService;

    @Override
    public String webAuth(Long companyId, String authCode) {
        return h5Auth(companyId, authCode);
    }

    @Override
    public String h5Auth(Long companyId, String authCode) {
        JSONObject config = getCompanyConfig(companyId);
        ThirdPartyAkSk.Feishu feishu = config.toJavaObject(ThirdPartyAkSk.Feishu.class);
        Client client = Client.newBuilder(feishu.getAppId(), feishu.getAppSecret()).build();
        String accessToken = feishuOpenApiService.getUserAccessToken(client, authCode);
        GetUserInfoRespBody userInfoByCode = feishuOpenApiService.getUserInfoByCode(client, accessToken);
        return userInfoByCode.getOpenId();
    }

    @Override
    public ThirdPartyUser pcAuth(String authCode) {
        throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不支持此种方式");
    }

    @Override
    public ThirdPartyUser appAuth(String authCode) {
        throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不支持此种方式");
    }

    @Override
    public String getType() {
        return ThirdPartyConstant.FEISHU;
    }

}
