package com.niimbot.asset.thirdparty.strategy.dingtalk;


import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:45
 */
@Slf4j
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.USER_ADD_ORG)
public class UserAddOrgStrategy extends AbsUserStrategy implements DingCallbackStrategy {

    private final DingOpenApiService dingOpenApiService;

    @Autowired
    public UserAddOrgStrategy(DingOpenApiService dingOpenApiService) {
        this.dingOpenApiService = dingOpenApiService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        if (log.isDebugEnabled()) {
            log.debug("user_add_org: " + content);
        }

        List<String> userIds = content.getJSONArray("UserId").toJavaList(String.class);
        for (String userId : userIds) {
            ThirdPartyEmp emp = dingOpenApiService.getEmp(userId, companyId, accessToken);
            createEmp(emp.setThirdPartyType(getType()));
        }
        return true;
    }
}
