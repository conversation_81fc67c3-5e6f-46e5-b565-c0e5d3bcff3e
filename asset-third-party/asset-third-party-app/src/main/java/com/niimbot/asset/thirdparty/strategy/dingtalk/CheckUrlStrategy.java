package com.niimbot.asset.thirdparty.strategy.dingtalk;


import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:45
 */
@Slf4j
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.CHECK_URL)
public class CheckUrlStrategy implements DingCallbackStrategy {

    @Override
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        if (log.isDebugEnabled()) {
            log.debug("{}测试回调URL事件: {}", companyId, content);
        }
        return true;
    }

}
