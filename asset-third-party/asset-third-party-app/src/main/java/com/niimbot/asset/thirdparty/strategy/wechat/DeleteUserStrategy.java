package com.niimbot.asset.thirdparty.strategy.wechat;

import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.WeChatCallbackEvent;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;

/**
 * <AUTHOR>
 * @since 2021/11/19 11:16
 */
@Slf4j
@Component
@CallbackStrategy(eventType = WeChatCallbackEvent.DELETE_USER)
public class DeleteUserStrategy extends AbsUserStrategy implements WeChatCallbackStrategy {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, WxCpTpXmlMessage xmlMessage) {
        if (log.isDebugEnabled()) {
            log.debug("delete_user: " + xmlMessage);
        }
        deleteUser(xmlMessage.getUserID(), companyId, getType());
        return true;
    }

}
