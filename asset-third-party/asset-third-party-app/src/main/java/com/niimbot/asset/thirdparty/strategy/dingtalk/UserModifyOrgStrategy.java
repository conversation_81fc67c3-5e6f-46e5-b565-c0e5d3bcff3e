package com.niimbot.asset.thirdparty.strategy.dingtalk;


import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.annotation.CallbackStrategy;
import com.niimbot.asset.thirdparty.constant.DingtalkCallbackEvent;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.service.DingOpenApiService;
import com.niimbot.asset.thirdparty.strategy.AbsUserStrategy;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/19 17:45
 */
@Slf4j
@Component
@CallbackStrategy(eventType = DingtalkCallbackEvent.USER_MODIFY_ORG)
public class UserModifyOrgStrategy extends AbsUserStrategy implements DingCallbackStrategy {

    private final DingOpenApiService dingOpenApiService;

    public UserModifyOrgStrategy(DingOpenApiService dingOpenApiService) {
        this.dingOpenApiService = dingOpenApiService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCallback(Long companyId, String accessToken, JSONObject content) {
        if (log.isDebugEnabled()) {
            log.debug("user_modify_org: " + content);
        }
        List<String> userIds = content.getJSONArray("UserId").toJavaList(String.class);
        for (String userId : userIds) {
            ThirdPartyEmp emp = dingOpenApiService.getEmp(userId, companyId, accessToken);
            updateEmp(emp.setThirdPartyType(getType()));
        }
        return true;
    }
}
