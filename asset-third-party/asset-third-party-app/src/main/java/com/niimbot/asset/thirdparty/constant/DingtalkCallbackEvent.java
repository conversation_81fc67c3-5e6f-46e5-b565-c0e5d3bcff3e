package com.niimbot.asset.thirdparty.constant;

/**
 * <AUTHOR>
 * @since 2021/8/19 18:10
 */
public interface DingtalkCallbackEvent {

    // 测试回调URL事件
    String CHECK_URL = "check_url";

    // 通讯录用户增加
    String USER_ADD_ORG = "user_add_org";

    // 通讯录用户更改
    String USER_MODIFY_ORG = "user_modify_org";

    // 通讯录用户离职
    String USER_LEAVE_ORG = "user_leave_org";

    // 通讯录企业部门创建
    String ORG_DEPT_CREATE = "org_dept_create";

    // 通讯录企业部门修改
    String ORG_DEPT_MODIFY = "org_dept_modify";

    // 通讯录企业部门删除
    String ORG_DEPT_REMOVE = "org_dept_remove";

    /**
     * 员工角色信息发生变更
     */
    String LABEL_USER_CHANGE = "label_user_change";

    /**
     * 增加角色或者角色组
     */
    String LABEL_CONF_ADD = "label_conf_add";

    /**
     * 删除角色或者角色组
     */
    String LABEL_CONF_DEL = "label_conf_del";

    /**
     * 修改角色或者角色组
     */
    String LABEL_CONF_MODIFY = "label_conf_modify";
}
