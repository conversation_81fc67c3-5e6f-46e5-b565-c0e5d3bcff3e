package com.niimbot.asset.thirdparty.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.service.FeishuCallbackService;
import com.niimbot.asset.thirdparty.strategy.CallbackContext;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/15 10:21
 */
@Service
public class FeishuCallbackServiceImpl implements FeishuCallbackService {

    private final CallbackContext callbackContext;

    public FeishuCallbackServiceImpl(CallbackContext callbackContext) {
        this.callbackContext = callbackContext;
    }

    @Override
    public boolean handle(Long companyId, ThirdPartyAkSk.Feishu feishu, JSONObject content) {
        return callbackContext.executeFeishu(companyId, feishu, content);
    }

}
