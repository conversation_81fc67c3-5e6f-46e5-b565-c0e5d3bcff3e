package com.niimbot.asset.thirdparty.archive.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.thirdparty.archive.dto.AsEventArchiveQueryDto;
import com.niimbot.asset.thirdparty.archive.entity.AsEventArchive;
import com.niimbot.asset.thirdparty.archive.service.AsEventArchiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 第三方事件归档控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Api(tags = "第三方事件归档管理")
@RestController
@RequestMapping("server/thirdparty/archive")
@RequiredArgsConstructor
public class AsEventArchiveController {

    private final AsEventArchiveService messageArchiveService;

    @ApiOperation(value = "事件归档分页列表")
    @PostMapping("/page")
    public IPage<AsEventArchive> page(@RequestBody @Valid AsEventArchiveQueryDto queryDto) {
        return messageArchiveService.page(queryDto);
    }

    @ApiOperation(value = "事件归档列表")
    @PostMapping("/list")
    public List<AsEventArchive> list(@RequestBody @Valid AsEventArchiveQueryDto queryDto) {
        return messageArchiveService.list(queryDto);
    }

    @ApiOperation(value = "根据ID查询事件归档详情")
    @GetMapping("/{id}")
    public AsEventArchive getById(@PathVariable("id") Long id) {
        return messageArchiveService.getById(id);
    }

    @ApiOperation(value = "根据事件ID查询归档信息")
    @GetMapping("/event/{eventId}")
    public AsEventArchive getByEventId(@PathVariable("eventId") String eventId) {
        return messageArchiveService.getByEventId(eventId);
    }

    @ApiOperation(value = "手动重试失败的事件")
    @PostMapping("/retry/{id}")
    public Boolean retryEvent(@PathVariable("id") Long id) {
        return messageArchiveService.retryEvent(id);
    }

    @ApiOperation(value = "批量重试失败的事件")
    @PostMapping("/retry/batch")
    public Boolean retryEvents(@RequestBody List<Long> ids) {
        return messageArchiveService.retryEvents(ids);
    }

    @ApiOperation(value = "删除归档记录")
    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Long id) {
        return messageArchiveService.removeById(id);
    }

    @ApiOperation(value = "批量删除归档记录")
    @DeleteMapping("/batch")
    public Boolean deleteBatch(@RequestBody List<Long> ids) {
        return messageArchiveService.removeByIds(ids);
    }

    @ApiOperation(value = "获取第三方系统类型列表")
    @GetMapping("/thirdPartyTypes")
    public List<String> getThirdPartyTypes() {
        return messageArchiveService.getThirdPartyTypes();
    }

    @ApiOperation(value = "获取事件类型列表")
    @GetMapping("/eventTypes")
    public List<String> getEventTypes() {
        return messageArchiveService.getEventTypes();
    }

    @ApiOperation(value = "获取状态列表")
    @GetMapping("/statusList")
    public List<String> getStatusList() {
        return messageArchiveService.getStatusList();
    }
}
