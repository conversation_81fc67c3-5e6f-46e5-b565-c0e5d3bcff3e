package com.niimbot.asset.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.User;
import com.niimbot.asset.framework.utils.DeduplicationUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.AsCountryCodeService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.model.AuthScopes;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.service.AbsOrgStructureService;
import com.niimbot.asset.thirdparty.service.FeishuOpenApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/9 16:21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeishuOrgStructureServiceImpl extends AbsOrgStructureService {

    private final FeishuOpenApiService feishuOpenApiService;
    private final AsCountryCodeService countryCodeService;

    /**
     * 获取飞书的组织架构
     *
     * @param companyId 企业ID
     * @return
     */
    @Override
    public List<AsOrg> loadThirdPartyOrg(Long companyId) {
        // 授权范围
        List<String> scope = getOrgAuthScope(companyId);
        if (CollUtil.isEmpty(scope)) {
            return new ArrayList<>();
        }
        List<Department> remoteOrgs = getRemoteOrgs(companyId, new HashSet<>(scope));
        Set<String> remoteDepartmentIds = remoteOrgs.stream()
                .map(Department::getOpenDepartmentId).collect(Collectors.toSet());
        if (remoteOrgs.size() != remoteDepartmentIds.size()) {
            remoteOrgs = remoteOrgs.stream()
                    .filter(DeduplicationUtil.distinctByKey(Department::getOpenDepartmentId))
                    .collect(Collectors.toList());
        }
        AsOrg rootOrg = orgService.getRootOrg(companyId);
        return remoteOrgs.stream()
                .map(v -> toOrg(rootOrg.getId(), companyId, remoteDepartmentIds, v))
                .collect(Collectors.toList());
    }

    private List<Department> getRemoteOrgs(Long companyId, Set<String> remoteParentIds) {
        List<Department> remoteOrgs = new ArrayList<>();
        Client feishuClient = feishuClient(companyId);
        // 查询当前组织节点数据
        List<List<String>> remotePidPartition = Lists.partition(new ArrayList<>(remoteParentIds), 50);
        for (List<String> remotePids : remotePidPartition) {
            // 获取所有当前部门数据
            List<Department> currentDept = feishuOpenApiService.listDepartment(feishuClient, remotePids);
            remoteOrgs.addAll(currentDept);
        }

        // 查询当前子组织节点的递归数据
        for (String remoteParentId : remoteParentIds) {
            List<Department> childDept = new ArrayList<>();
            // 获取所有子部门数据
            feishuOpenApiService.getChildrenDepartment(feishuClient, remoteParentId, childDept, null);
            remoteOrgs.addAll(childDept);
        }
        return remoteOrgs;
    }

    private AsOrg toOrg(Long localRootOrgId, Long companyId, Set<String> remoteDepartmentIds, Department remote) {
        AsOrg org = new AsOrg();
        org.setCompanyId(companyId);
        org.setExternalOrgId(remote.getOpenDepartmentId());
        // 当前节点不是根节点 且 其父节点不在本地同步的范围内 直接挂靠在根节点下
        if (remoteDepartmentIds.contains(remote.getParentDepartmentId())) {
            org.setExternalPid(remote.getParentDepartmentId());
        } else {
            org.setExternalPid("0");
        }
        org.setSortNum(Objects.isNull(remote.getOrder()) ? 100 : Convert.toInt(remote.getOrder(), 100));
        org.setOrgName(remote.getName());
        org.setCompanyOwner(localRootOrgId);
        if (StrUtil.isNotEmpty(remote.getLeaderUserId())) {
            org.setExternalDirector(ListUtil.of(remote.getLeaderUserId()));
        }
        org.setId(IdUtils.getId());
        org.setOrgType(2);
        org.setSourceType(1);
        return org;
    }


    /**
     * 获取飞书员工
     * @param companyId        企业ID
     * @param thirdPartyOrgIds 组织结构
     * @return
     */
    @Override
    public List<ThirdPartyEmp> loadThirdPartyEmp(Long companyId, List<String> thirdPartyOrgIds) {
        Client feishuClient = feishuClient(companyId);
        List<String> userAuthScope = getUserAuthScope(companyId);
        if (CollUtil.isEmpty(userAuthScope) && CollUtil.isEmpty(thirdPartyOrgIds)) {
            return Collections.emptyList();
        }
        // 查询部门下的人
        List<ThirdPartyEmp> userRspList = new CopyOnWriteArrayList<>();
        thirdPartyOrgIds.forEach(deptId -> {
            if (!"0".equals(deptId)) {
                List<User> userList = new ArrayList<>();
                feishuOpenApiService.getUserListByDepartmentId(feishuClient, deptId, userList, null);
                for (User user : userList) {
                    userRspList.add(toEmp(user, companyId));
                }
            }
        });
        // 查询有权限的人
        List<List<String>> userList = Lists.partition(userAuthScope, 50);
        for (List<String> userIds : userList) {
            User[] users = feishuOpenApiService.listUser(feishuClient, userIds);
            for (User user : users) {
                userRspList.add(toEmp(user, companyId));
            }
        }
        return userRspList;
    }

    private ThirdPartyEmp toEmp(User user, Long companyId) {
        ThirdPartyEmp emp = new ThirdPartyEmp();
        emp.setName(user.getName());
        emp.setJobNum(user.getEmployeeNo());
        emp.setPosition(user.getJobTitle());
        if (user.getAvatar() != null) {
            emp.setAvatar(user.getAvatar().getAvatar640());
        }
        emp.setThirdPartyId(user.getOpenId());
        if (StrUtil.isNotEmpty(user.getMobile())) {
            List<String> countryCodes = countryCodeService.allCountryCode();
            for (String countryCode : countryCodes) {
                if (user.getMobile().startsWith(countryCode)) {
                    emp.setMobile(user.getMobile().replace(countryCode, StrUtil.EMPTY));
                    break;
                }
            }
        }
        emp.setEmail(user.getEmail());
        if (ArrayUtil.isNotEmpty(user.getDepartmentIds())) {
            emp.setDeptIdList(Lists.newArrayList(user.getDepartmentIds()));
        }
        emp.setCompanyId(companyId);
        return emp;
    }

    @Override
    public String getType() {
        return ThirdPartyConstant.FEISHU;
    }

    // 获取部门权限范围
    private List<String> getOrgAuthScope(Long companyId) {
        AuthScopes authScopes = new AuthScopes();
        feishuOpenApiService.getAuthScopes(feishuClient(companyId), authScopes, null);
        return authScopes.getDepartmentIds();
    }

    // 获取员工权限范围
    private List<String> getUserAuthScope(Long companyId) {
        AuthScopes authScopes = new AuthScopes();
        feishuOpenApiService.getAuthScopes(feishuClient(companyId), authScopes, null);
        return authScopes.getUserIds();
    }

    private Client feishuClient(Long companyId) {
        JSONObject secret = getSecret(companyId, getType());
        ThirdPartyAkSk.Feishu feishu = secret.toJavaObject(ThirdPartyAkSk.Feishu.class);
        return Client.newBuilder(feishu.getAppId(), feishu.getAppSecret()).build();
    }

}
