package com.niimbot.asset.thirdparty.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsConfig;
import com.niimbot.asset.system.service.AsConfigService;
import com.niimbot.asset.thirdparty.model.AsThirdParty;
import com.niimbot.asset.thirdparty.service.AsThirdPartyService;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.service.OrgStructureService;
import com.niimbot.asset.thirdparty.service.impl.OrgStructureHandler;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.thirdparty.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 工具箱控制器
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Slf4j
@Validated
@RestController
@RequestMapping("server/thirdparty")
public class ThirdPartyServiceController {

    private final AsThirdPartyService thirdPartyService;

    private final OrgStructureHandler orgStructureHandler;

    private final RedisService redisService;

    private final ThreadPoolTaskExecutor assetTaskExecutor;

    private final AsConfigService configService;

    private final RestTemplate restTemplate;

    @Value("${asset.domain.interface}")
    public String interfaceDomain;

    @Value("${asset.domain.h5}")
    private String h5Domain;

    @Autowired
    public ThirdPartyServiceController(AsThirdPartyService thirdPartyService,
                                       OrgStructureHandler orgStructureHandler,
                                       RedisService redisService,
                                       ThreadPoolTaskExecutor assetTaskExecutor,
                                       AsConfigService configService,
                                       RestTemplate restTemplate) {
        this.thirdPartyService = thirdPartyService;
        this.orgStructureHandler = orgStructureHandler;
        this.redisService = redisService;
        this.assetTaskExecutor = assetTaskExecutor;
        this.configService = configService;
        this.restTemplate = restTemplate;
    }

    @PostMapping("/bind/dingtalk")
    public BindThirdPartyResult bindDingtalk(@RequestBody DingtalkBindDto dingtalk) {
        return this.thirdPartyService.bindDingtalk(dingtalk);
    }

    @PostMapping("/replenish/dingtalk")
    public Boolean replenishDingtalk(@RequestBody DingtalkReplenish dto) {
        return this.thirdPartyService.replenishDingtalk(dto);
    }

    @GetMapping("/bind/info")
    public ThirdPartyDto getBindInfo(@RequestParam(value = "type", required = false) String platform) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        AsThirdParty one = this.thirdPartyService.getOne(new LambdaQueryWrapper<AsThirdParty>()
                .eq(AsThirdParty::getCompanyId, companyId));
        ThirdPartyDto thirdPartyDto = new ThirdPartyDto();
        if (Objects.nonNull(one)) {
            thirdPartyDto = BeanUtil.copyProperties(one, ThirdPartyDto.class);
        }

        if (StrUtil.isEmpty(platform)) {
            if (Objects.nonNull(one)) {
                platform = one.getType();
            }
        }

        if (StrUtil.isNotEmpty(platform)) {
            String h5DomainVar = h5Domain.endsWith("/") ? h5Domain.substring(0, h5Domain.length() - 1) : h5Domain;
            switch (platform) {
                case ThirdPartyConstant.DINGTALK:
                    thirdPartyDto.setDingCallBack((interfaceDomain.endsWith("/") ? interfaceDomain.substring(0, interfaceDomain.length() - 1) : interfaceDomain) + "/api/common/thirdparty/callback/dingtalk/" + companyId);
                    thirdPartyDto.setH5Index(h5DomainVar + "/h5/pages/login/index?companyId=" + companyId + "&platform=DINGTALK");
                    thirdPartyDto.setPcIndex(h5DomainVar + "/#/login?companyId=" + companyId + "&platform=DINGTALK");
                    break;
                case ThirdPartyConstant.FEISHU:
                    thirdPartyDto.setFeishuCallBack((interfaceDomain.endsWith("/") ? interfaceDomain.substring(0, interfaceDomain.length() - 1) : interfaceDomain) + "/api/common/thirdparty/callback/feishu/" + companyId);
                    thirdPartyDto.setH5Index(h5DomainVar + "/h5/pages/login/index?companyId=" + companyId + "&platform=FEISHU");
                    thirdPartyDto.setPcIndex(h5DomainVar + "/#/login?companyId=" + companyId + "&platform=FEISHU");
                    break;
                case ThirdPartyConstant.WECHAT:
                    AsConfig qrCode = configService.getOne(Wrappers.lambdaQuery(AsConfig.class)
                            .eq(AsConfig::getConfigKey, "wechat_bind_qr_code"));
                    if (qrCode != null) {
                        thirdPartyDto.setWechatBindQRCode(qrCode.getConfigValue());
                    }
                    break;
            }
        }
        return thirdPartyDto;
    }

    @DeleteMapping("/bind/dingtalk")
    public Boolean removeDingtalk() {
        return this.thirdPartyService.removeDingtalk();
    }

    @PostMapping("/bind/wechat")
    public BindThirdPartyResult bindWeChat(@RequestBody WeChatBindDto wechat) {
        return this.thirdPartyService.bindWeChat(wechat);
    }

    @DeleteMapping("/bind/wechat")
    public Boolean removeWeChat() {
        return this.thirdPartyService.removeWeChat(LoginUserThreadLocal.getCompanyId(), true);
    }

    @PostMapping("/bind/feishu")
    public BindThirdPartyResult bindFeishu(@RequestBody FeishuBindDto feishu) {
        return this.thirdPartyService.bindFeishu(feishu);
    }

    @DeleteMapping("/bind/feishu")
    public Boolean removeFeishu() {
        return this.thirdPartyService.removeFeishu();
    }

    @GetMapping("/org/getMapping/{type}")
    public AssetThirdPartyOrgMapping getOrgMapping(@PathVariable("type") String type) {
        if (StrUtil.equals(type, ThirdPartyConstant.OTHERS)) {
            return new AssetThirdPartyOrgMapping();
        }
        return orgStructureHandler.getService(type.toUpperCase(Locale.ROOT)).getOrgMapping(LoginUserThreadLocal.getCompanyId());
    }

    @PostMapping("/org/handleMapping/{type}")
    public Boolean handleOrgMapping(@PathVariable("type") String type, @RequestBody List<AssetThirdPartyOrgMapping.Mapping> mappings) {
        return orgStructureHandler.getService(type.toUpperCase(Locale.ROOT)).handleOrgMapping(mappings);
    }

    @GetMapping("/emp/getMapping/{type}")
    public AssetThirdPartyEmpMapping getEmpMapping(@PathVariable("type") String type) {
        return orgStructureHandler.getService(type.toUpperCase(Locale.ROOT)).getEmpMapping(LoginUserThreadLocal.getCompanyId());
    }

    @PostMapping("/handle/{type}")
    public Boolean handle(@PathVariable("type") String type, @RequestBody List<TransMismatchEmp> transMismatchEmps) {
        OrgStructureService handlerService = orgStructureHandler.getService(type.toUpperCase(Locale.ROOT));
        try {
            return handlerService.handle(transMismatchEmps);
        } catch (Exception e) {
            handlerService.loadOrgCache(LoginUserThreadLocal.getCompanyId());
            handlerService.loadEmpCache(LoginUserThreadLocal.getCompanyId());
            throw e;
        }
    }

    @PostMapping("/overallHandle")
    public Boolean overallHandle(@RequestParam("type") String type) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 如果是其他类型，走特殊方法
        if (ThirdPartyConstant.OTHERS.equals(type)) {
            AsThirdParty thirdParty = thirdPartyService.getOne(Wrappers.lambdaQuery(AsThirdParty.class)
                    .eq(AsThirdParty::getCompanyId, companyId)
                    .eq(AsThirdParty::getType, type));
            if (thirdParty == null) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先绑定第三方信息");
            }
            ThirdPartyAkSk.Others others = thirdParty.getForm().toJavaObject(ThirdPartyAkSk.Others.class);
            String syncUrl = others.getSyncUrl();
            String sign = others.getSign();

            // header添加签名
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            TreeMap<String, String> signatureMap = new TreeMap<>();
            signatureMap.put("companyId", Convert.toStr(companyId));
            signatureMap.put("timestamp", Convert.toStr(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond()));
            signatureMap.put("nonceStr", RandomUtil.randomString(8));
            signatureMap.forEach(headers::add);
            // 签名
            signatureMap.put("sign", sign);
            headers.add("signature", DigestUtils.sha1Hex(URLUtil.buildQuery(signatureMap, StandardCharsets.UTF_8)));
            log.info("手动同步三方系统数据\n[url]:{}\n[header]:{}", syncUrl, headers);
            HttpEntity<String> entity = new HttpEntity<>(StrUtil.EMPTY_JSON, headers);
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            // 设置连接超时时间（单位：毫秒）
            factory.setConnectTimeout(60000);
            // 设置读取超时时间（单位：毫秒）
            factory.setReadTimeout(60000);
            restTemplate.setRequestFactory(factory);
            try {
                String body = restTemplate.exchange(syncUrl, HttpMethod.POST, entity, String.class).getBody();
                JSONObject bodyJson = JSONObject.parseObject(body);
                if (bodyJson.getInteger("code") != 1) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, bodyJson.getString("msg"));
                }
            } catch (HttpClientErrorException var) {
                if (var.getStatusCode() == HttpStatus.NOT_FOUND) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "接口" + syncUrl + " 404 NOT FOUND");
                } else {
                    log.error(var.getMessage(), var);
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "接口" + syncUrl + "异常，" + var.getMessage());
                }
            } catch (Exception e) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, e.getMessage());
            }
        } else {
            if (!thirdPartyService.getThirdPartyInitSync(companyId, type)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先绑定第三方信息并初始化");
            }
            String syncKey = ThirdPartyConstant.getThirdPartyManualSyncKey(companyId, type);
//            if (ObjectUtil.isNotEmpty(redisService.get(syncKey))) {
//                Long expire = redisService.getExpire(syncKey);
//                Long minutes = expire / 60 + 1;
//                throw new BusinessException(ThirdPartyResultCode.SYNC_30_MIN_LIMIT, Convert.toStr(minutes));
//            }
            // 查询
            try {
//                redisService.set(syncKey, companyId, 30, TimeUnit.MINUTES);
                assetTaskExecutor.execute(() -> orgStructureHandler.getService(type).overallHandle(companyId));
            } catch (BusinessException bus) {
                redisService.del(syncKey);
                throw bus;
            } catch (Exception e) {
                redisService.del(syncKey);
                log.error(e.getMessage());
            }
        }
        return true;
    }

    @SuppressWarnings("all")
    @GetMapping("/syncStep/{type}")
    public Map<String, Object> process(@PathVariable("type") String type) {
        Map<String, Object> result = new HashMap<>();
        result.put("currentStep", "");
        String prefix = ThirdPartyConstant.getSyncStepCacheKey(type, LoginUserThreadLocal.getCompanyId());
        // 步骤
        String stepCacheKey = prefix + "step";
        if (redisService.hasKey(stepCacheKey)) {
            Object step = redisService.get(stepCacheKey);
            result.put("currentStep", step);
        }
        // 获取组织映射关系的缓存结果
        Map<String, Object> getOrgMapping = new HashMap<>();
        String getMappingCacheKey = prefix + ThirdPartyConstant.GET_ORG_MAPPING + ":";
        if (redisService.hasKey(getMappingCacheKey + "asset")) {
            List<Tree> assetOrgTree = JSONObject.parseArray(String.valueOf(redisService.get(getMappingCacheKey + "asset")), Tree.class);
            getOrgMapping.put("assetOrgTree", assetOrgTree);
        }
        if (redisService.hasKey(getMappingCacheKey + "third")) {
            List<Tree> thirdOrgTree = JSONObject.parseArray(String.valueOf(redisService.get(getMappingCacheKey + "third")), Tree.class);
            getOrgMapping.put("thirdOrgTree", thirdOrgTree);
        }
        if (redisService.hasKey(getMappingCacheKey + "mapping")) {
            List<AssetThirdPartyOrgMapping.Mapping> mappings = JSONObject.parseArray(String.valueOf(redisService.get(getMappingCacheKey + "mapping")), AssetThirdPartyOrgMapping.Mapping.class);
            getOrgMapping.put("mappings", mappings);
        }
        result.put("getOrgMapping", getOrgMapping);
        // 获取设置组织映射关系的参数
        String handleOrgMappingCacheKey = prefix + ThirdPartyConstant.HANDLE_ORG_MAPPING;
        Map<String, Object> handleOrgMapping = new HashMap<>();
        if (redisService.hasKey(handleOrgMappingCacheKey)) {
            List<AssetThirdPartyOrgMapping.Mapping> mappings = JSONObject.parseArray(String.valueOf(redisService.get(handleOrgMappingCacheKey)), AssetThirdPartyOrgMapping.Mapping.class);
            handleOrgMapping.put("mappings", mappings);
        }
        result.put("handleOrgMapping", handleOrgMapping);
        // 员工映射结果
        String getEmpMappingCacheKey = prefix + ThirdPartyConstant.GET_EMP_MAPPING + ":";
        Map<String, Object> getEmpMapping = new HashMap<>();
        if (redisService.hasKey(getEmpMappingCacheKey + "all")) {
            List<AssetThirdPartyEmpMapping.Emp> all = JSONObject.parseArray(String.valueOf(redisService.get(getEmpMappingCacheKey + "all")), AssetThirdPartyEmpMapping.Emp.class);
            getEmpMapping.put("all", all);
        }
        if (redisService.hasKey(getEmpMappingCacheKey + "mismatch")) {
            List<AssetThirdPartyEmpMapping.Emp> mismatch = JSONObject.parseArray(String.valueOf(redisService.get(getEmpMappingCacheKey + "mismatch")), AssetThirdPartyEmpMapping.Emp.class);
            getEmpMapping.put("mismatch", mismatch);
        }
        result.put("getEmpMapping", getEmpMapping);
        return result;
    }

    @DeleteMapping("/syncStep/clean")
    public Boolean cleanStepCache(@RequestParam("type") String type, @RequestParam(value = "companyId", required = false) Long companyId) {
        if (Objects.isNull(companyId)) {
            companyId = LoginUserThreadLocal.getCompanyId();
        }
        orgStructureHandler.getService(type).cleanCache(companyId);
        return true;
    }

    @GetMapping("/isBindThirdParty/{companyId}")
    public Boolean isBindThirdParty(@PathVariable("companyId") Long companyId) {
        List<AsThirdParty> list = thirdPartyService.list(
                Wrappers.lambdaQuery(AsThirdParty.class)
                        .eq(AsThirdParty::getCompanyId, companyId)
        );
        return CollUtil.isNotEmpty(list);
    }



}
