package com.niimbot.asset.thirdparty.config;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.thirdparty.event.EventCO;
import com.niimbot.asset.thirdparty.service.OthersCallbackService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/15 10:04
 */
@Slf4j
@Component
public class OthersBlockQueue extends Thread {

    public static final String BLOCK_QUEUE_OTHERS = "blockQueue:thirdParty:others";

    private final RBlockingQueue<OthersQueueMsg> othersBlockingQueue;
    private final OthersCallbackService othersCallbackService;

    @Autowired
    public OthersBlockQueue(RedissonClient redissonClient,
                            OthersCallbackService othersCallbackService) {
        this.othersBlockingQueue = redissonClient.getBlockingQueue(BLOCK_QUEUE_OTHERS, new JsonJacksonCodec());
        this.othersCallbackService = othersCallbackService;
        this.start();
    }

    @Override
    public void run() {
        while (true) {
            OthersQueueMsg queueMsg = null;
            try {
                queueMsg = othersBlockingQueue.take();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
//                Thread.currentThread().interrupt();
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex) {
                    // ignore
                }
            }

            if (queueMsg != null) {
                try {
                    // threadLocal写入公司Id
                    LoginUserDto userDto = new LoginUserDto();
                    userDto.setCusUser(new CusUserDto()
                            .setIsAdmin(true)
                            .setCompanyId(queueMsg.getCompanyId()));
                    LoginUserThreadLocal.set(userDto);
                    log.info("third party others msg [{}]", JSONObject.toJSONString(queueMsg));
                    othersCallbackService.handle(queueMsg.getCompanyId(), queueMsg.getContent());
                } catch (Exception e) {
                    log.error("process others callback fail, {}", e.getMessage(), e);
                } finally {
                    LoginUserThreadLocal.remove();
                }
            }
        }
    }

    public void put(OthersQueueMsg queueMsg) {
        try {
            int size = othersBlockingQueue.size();
            log.info("send others queue msg, {} waiting", size);
            othersBlockingQueue.put(queueMsg);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
//            Thread.currentThread().interrupt();
            try {
                Thread.sleep(5000);
            } catch (InterruptedException ex) {
                // ignore
            }
        }
    }

    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OthersQueueMsg {
        private Long companyId;
        private EventCO content;
    }

}
