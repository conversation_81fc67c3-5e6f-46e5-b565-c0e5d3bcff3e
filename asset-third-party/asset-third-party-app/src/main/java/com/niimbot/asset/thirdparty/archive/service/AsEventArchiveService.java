package com.niimbot.asset.thirdparty.archive.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.thirdparty.archive.dto.AsEventArchiveQueryDto;
import com.niimbot.asset.thirdparty.archive.entity.AsEventArchive;
import java.util.List;

/**
 * 事件消息归档服务接口 - 通用归档服务，支持多种第三方集成类型
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface AsEventArchiveService {
    
    /**
     * 保存归档事件消息
     * @param archive 归档事件消息
     */
    void saveArchive(AsEventArchive archive);
    
    /**
     * 根据事件ID获取归档事件消息
     * @param eventId 事件ID
     * @return 归档事件消息
     */
    AsEventArchive getArchive(String eventId);
    
    /**
     * 更新归档事件消息
     * @param archive 归档事件消息
     */
    void updateArchive(AsEventArchive archive);
    
    /**
     * 删除归档事件消息
     * @param eventId 事件ID
     */
    void removeArchive(String eventId);
    
    /**
     * 根据第三方类型获取归档事件消息列表
     * @param thirdPartyType 第三方类型
     * @return 归档事件消息列表
     */
    List<AsEventArchive> getArchivesByThirdPartyType(String thirdPartyType);
    
    /**
     * 根据状态获取归档事件消息列表
     * @param status 状态
     * @return 归档事件消息列表
     */
    List<AsEventArchive> getArchivesByStatus(String status);
    
    /**
     * 获取可重试的归档事件消息列表
     * @return 可重试的归档事件消息列表
     */
    List<AsEventArchive> getRetryableArchives();
    
    /**
     * 根据第三方类型和事件类型获取归档事件消息列表
     * @param thirdPartyType 第三方类型
     * @param eventType 事件类型
     * @return 归档事件消息列表
     */
    List<AsEventArchive> getArchivesByTypeAndEvent(String thirdPartyType, String eventType);

    /**
     * 分页查询归档事件消息
     * @param queryDto 查询条件
     * @return 分页结果
     */
    IPage<AsEventArchive> page(AsEventArchiveQueryDto queryDto);

    /**
     * 列表查询归档事件消息
     * @param queryDto 查询条件
     * @return 归档事件消息列表
     */
    List<AsEventArchive> list(AsEventArchiveQueryDto queryDto);

    /**
     * 根据ID获取归档事件消息
     * @param id 主键ID
     * @return 归档事件消息
     */
    AsEventArchive getById(Long id);

    /**
     * 根据事件ID获取归档事件消息
     * @param eventId 事件ID
     * @return 归档事件消息
     */
    AsEventArchive getByEventId(String eventId);

    /**
     * 手动重试失败的事件
     * @param id 归档记录ID
     * @return 是否成功
     */
    Boolean retryEvent(Long id);

    /**
     * 批量重试失败的事件
     * @param ids 归档记录ID列表
     * @return 是否成功
     */
    Boolean retryEvents(List<Long> ids);

    /**
     * 根据ID删除归档记录
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean removeById(Long id);

    /**
     * 批量删除归档记录
     * @param ids 主键ID列表
     * @return 是否成功
     */
    Boolean removeByIds(List<Long> ids);

    /**
     * 获取第三方系统类型列表
     * @return 第三方系统类型列表
     */
    List<String> getThirdPartyTypes();

    /**
     * 获取事件类型列表
     * @return 事件类型列表
     */
    List<String> getEventTypes();

    /**
     * 获取状态列表
     * @return 状态列表
     */
    List<String> getStatusList();
}
