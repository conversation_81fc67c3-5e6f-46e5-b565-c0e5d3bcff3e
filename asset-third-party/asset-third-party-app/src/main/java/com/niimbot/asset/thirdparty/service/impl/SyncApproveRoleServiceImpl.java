package com.niimbot.asset.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiRoleListResponse;
import com.dingtalk.api.response.OapiRoleSimplelistResponse;
import com.niimbot.asset.activiti.model.ActApproveRole;
import com.niimbot.asset.activiti.model.ActApproveRoleMember;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActApproveRoleService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.dto.clientobject.ExternalRelation;
import com.niimbot.asset.system.ots.SystemEmployeeOts;
import com.niimbot.asset.system.ots.SystemOrgOts;
import com.niimbot.asset.thirdparty.support.DingApiSupport;
import com.niimbot.asset.thirdparty.service.SyncApproveRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SyncApproveRoleServiceImpl implements SyncApproveRoleService {

    @Resource
    private DingApiSupport dingApiSupport;

    @Resource
    private ActApproveRoleService approveRoleService;

    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Resource
    private SystemOrgOts systemOrgService;

    @Resource
    private SystemEmployeeOts systemEmployeeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manualSyncFromDing(Long companyId) {
        // 钉钉侧数据
        List<OapiRoleListResponse.OpenRoleGroup> roles = dingApiSupport.getRoles(companyId);
        List<ActApproveRole> dingRoles = new ArrayList<>();
        roles.forEach(v -> dingRoles.addAll(v.getRoles().stream().map(openRole -> new ActApproveRole().setExternalId(String.valueOf(openRole.getId())).setName(openRole.getName())).collect(Collectors.toList())));
        // 系统侧数据 处理角色
        List<ActApproveRole> systemRoles = approveRoleService.list(Wrappers.lambdaUpdate(ActApproveRole.class).eq(ActApproveRole::getCompanyId, companyId));
        List<String> systemRoleIds = systemRoles.stream().map(ActApproveRole::getExternalId).collect(Collectors.toList());
        Map<String, ActApproveRole> dingRoleMap = dingRoles.stream().collect(Collectors.toMap(ActApproveRole::getExternalId, v -> v));
        // 新增 钉钉侧有系统侧没有的数据
        List<ActApproveRole> forAdd = dingRoles.stream().filter(v -> !systemRoleIds.contains(v.getExternalId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(forAdd)) {
            forAdd.forEach(v -> v.setId(IdUtils.getId()));
            approveRoleService.recommendRoleCode(companyId, forAdd);
        }
        // 更新 钉钉侧有系统侧也有的数据 更新角色名称
        List<ActApproveRole> forUpdate = systemRoles.stream()
                .filter(v -> Objects.nonNull(v.getExternalId()) && dingRoleMap.containsKey(v.getExternalId()))
                .peek(v -> v.setName(dingRoleMap.get(v.getExternalId()).getName()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(forUpdate)) {
            approveRoleService.updateBatchById(forUpdate);
        }
        // 删除 系统侧有钉钉侧没有的数据
        List<Long> forDelete = systemRoles.stream().filter(v -> Objects.nonNull(v.getExternalId()) && !dingRoleMap.containsKey(v.getExternalId())).map(ActApproveRole::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(forDelete)) {
            approveRoleService.removeByIds(forDelete);
            approveRoleMemberService.remove(Wrappers.lambdaUpdate(ActApproveRoleMember.class).in(ActApproveRoleMember::getApproveRoleId, forDelete));
        }
        // 循环处理角色中的人
        forUpdate.addAll(forAdd);
        handleUserRole(companyId, forUpdate);
        return true;
    }

    private void handleUserRole(Long companyId, List<ActApproveRole> roles) {
        Map<String, Long> roleRelationMap = roles.stream().collect(Collectors.toMap(ActApproveRole::getExternalId, ActApproveRole::getId));
        roleRelationMap.forEach((k, v) -> {
            // 钉钉角色下的人
            List<OapiRoleSimplelistResponse.OpenEmpSimple> roleUsers = dingApiSupport.getRoleUsers(companyId, Convert.toLong(k));
            if (CollUtil.isEmpty(roleUsers)) {
                return;
            }
            List<String> dingUserIds = new ArrayList<>();
            List<String> dingUserDepIds = new ArrayList<>();
            roleUsers.forEach(u -> {
                dingUserIds.add(u.getUserid());
                if (CollUtil.isEmpty(u.getManageScopes())) {
                    u.setManageScopes(Collections.emptyList());
                }
                dingUserDepIds.addAll(u.getManageScopes().stream().map(d -> String.valueOf(d.getDeptId())).collect(Collectors.toList()));
            });
            // 组织ID映射关系
            List<ExternalRelation> orgRelations = systemOrgService.getOrgExternalRelation(companyId, dingUserDepIds);
            Map<String, Long> orgRelationMap = orgRelations.stream().collect(Collectors.toMap(ExternalRelation::getExternalId, ExternalRelation::getId));
            // 员工ID映射关系
            List<ExternalRelation> empRelation = systemEmployeeService.getEmpExternalRelation(companyId, dingUserIds);
            Map<String, Long> empRelationMap = empRelation.stream().collect(Collectors.toMap(ExternalRelation::getExternalId, ExternalRelation::getId));
            // 转换成人
            List<ActApproveRoleMember> toDb = roleUsers.stream()
                    .filter(u -> empRelationMap.containsKey(u.getUserid()))
                    .map(a -> {
                        ActApproveRoleMember member = new ActApproveRoleMember();
                        member.setMemberId(empRelationMap.get(a.getUserid()));
                        member.setApproveRoleId(v);
                        member.setOrgScope(a.getManageScopes().stream().filter(d -> orgRelationMap.containsKey(String.valueOf(d.getDeptId()))).map(d -> orgRelationMap.get(String.valueOf(d.getDeptId()))).collect(Collectors.toList()));
                        return member;
                    }).collect(Collectors.toList());
            // 先删除在重建
            approveRoleMemberService.remove(
                    Wrappers.lambdaUpdate(ActApproveRoleMember.class)
                            .eq(ActApproveRoleMember::getApproveRoleId, v)
            );
            approveRoleMemberService.saveBatch(toDb);
        });
    }

    @Override
    public boolean syncFromDing(Long companyId, List<Long> extRoleIds) {
        List<DingApiSupport.DingRole> roles = dingApiSupport.getRoles(companyId, extRoleIds);
        if (CollUtil.isEmpty(roles)) {
            return true;
        }
        Map<Long, String> roleMap = roles.stream().collect(Collectors.toMap(DingApiSupport.DingRole::getId, DingApiSupport.DingRole::getName));
        // 过滤掉在云资产系统中没有的
        List<ActApproveRole> systemRoles = approveRoleService.list(
                Wrappers.lambdaQuery(ActApproveRole.class)
                        .eq(ActApproveRole::getCompanyId, companyId)
                        .in(ActApproveRole::getExternalId, roleMap.keySet().stream().map(String::valueOf).collect(Collectors.toList()))
        );
        handleUserRole(companyId, systemRoles);
        return false;
    }
}
