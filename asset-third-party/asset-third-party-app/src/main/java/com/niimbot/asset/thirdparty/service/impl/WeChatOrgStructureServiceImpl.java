package com.niimbot.asset.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.thirdparty.restops.RestOps;
import com.niimbot.asset.thirdparty.constant.ThirdPartyConstant;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.asset.thirdparty.service.AbsOrgStructureService;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxCpErrorMsgEnum;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpTpAuthInfo;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/10 10:45
 */
@Slf4j
@Service
public class WeChatOrgStructureServiceImpl extends AbsOrgStructureService {

    @Resource
    private WxCpTpService assetWxCpTpService;

    @Override
    public List<AsOrg> loadThirdPartyOrg(Long companyId) {
        // 1.获取全部的部门ID
        WxCpService wxCpService = thirdPartyService.getWeChatService(companyId);
        List<WxCpDepart> allDept = RestOps.handleOrThrow(
                () -> wxCpService.getDepartmentService().simpleList(null),
                () -> new BusinessException(ThirdPartyResultCode.SYNC_ERROR)
        ).get();
        // 2.转树，获取单个部门详情
        List<Tree<String>> treeList = TreeUtil.build(allDept, "0", (map, tree) -> {
            tree.setId(Convert.toStr(map.getId()));
            tree.setParentId(Convert.toStr(map.getParentId()));
            tree.setWeight(map.getOrder());
            WxCpDepart wxCpDepart = RestOps.handle(() -> wxCpService.getDepartmentService().get(map.getId())).get();
            tree.setName(wxCpDepart.getName());
            tree.putExtra("directors", Arrays.asList(wxCpDepart.getDepartmentLeader()));
        });
        AsOrg assetRootOrg = getAssetRootOrg(companyId);
        // 3.树转List，计算path
        List<AsOrg> remoteList = new ArrayList<>();
        LevelPath levelPath = new LevelPath(0, "0,", 0L, null);
        treeList.forEach(tree -> treeToList(tree, remoteList, companyId, levelPath, assetRootOrg));
        return remoteList;
    }

    private static void treeToList(Tree<String> orgNode, List<AsOrg> remoteList, Long companyId, LevelPath levelPath, AsOrg assetRootOrg) {
        List<Tree<String>> children = orgNode.getChildren();
        AsOrg org = new AsOrg();
        org.setExternalOrgId(orgNode.getId());
        org.setOrgName(Convert.toStr(orgNode.getName()));
        org.setSortNum(Convert.toInt(orgNode.getWeight(), Integer.MAX_VALUE));

        // 只是为了遍历树
        org.setId(IdUtils.getId());
        org.setPid(levelPath.getPid());

        org.setExternalPid(orgNode.getParentId());
        org.setPaths(levelPath.getPaths());
        org.setLevel(levelPath.getLevel());
        org.setCompanyId(companyId);
        org.setCompanyOwner(assetRootOrg.getId());
        // 设置根节点部门为企业类型
        if (org.getPid().equals(0L)) {
            org.setOrgType(1);
            org.setId(assetRootOrg.getId());
        }
        // 部门负责人
        if (orgNode.containsKey("directors")) {
            org.setExternalDirector(Convert.toList(String.class, orgNode.get("directors")));
        }
        remoteList.add(org);
        LevelPath nextLevelPath = new LevelPath(levelPath.getLevel() + 1, levelPath.getPaths() + org.getId() + ",", org.getId(), org.getCompanyOwner());
        // 递归计算子节点
        if (CollUtil.isNotEmpty(children)) {
            children.forEach(tree -> treeToList(tree, remoteList, companyId, nextLevelPath, assetRootOrg));
        }
    }

    @Data
    @AllArgsConstructor
    private static class LevelPath {

        private Integer level;

        private String paths;

        private Long pid;

        private Long companyOwner;

    }

    @Override
    public List<ThirdPartyEmp> loadThirdPartyEmp(Long companyId, List<String> thirdPartyOrgIds) {
        // 查询员工集合
        List<ThirdPartyEmp> userRspList = new CopyOnWriteArrayList<>();
        WxCpService wxCpService = thirdPartyService.getWeChatService(companyId);
        thirdPartyOrgIds.forEach(id -> userList(userRspList, id, companyId, wxCpService));
        userRspList.addAll(getAuthScopeUser(companyId, userRspList.stream().map(ThirdPartyEmp::getThirdPartyId).collect(Collectors.toSet())));
        return userRspList;
    }

    WxCpTpAuthInfo getAuthInfo(Long companyId) {
        JSONObject form = getSecret(companyId, getType());
        String corpId = form.getJSONObject("userAuthAgent").getString("corpId");
        String secret = form.getString("secret");
        try {
            log.info("assetWxCpTpService getSuiteTicket[{}]", assetWxCpTpService.getSuiteTicket());
            return assetWxCpTpService.getAuthInfo(corpId, secret);
        } catch (WxErrorException e) {
            log.error("获取企业微信应用授权范围异常", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "获取企业微信应用授权范围异常");
        }
    }

    List<ThirdPartyEmp> getAuthScopeUser(Long companyId, Set<String> userIds) {
        WxCpTpAuthInfo authInfo = getAuthInfo(companyId);
        List<String> authUserIds = authInfo.getAuthInfo().getAgents().stream()
                .filter(v -> Objects.nonNull(v.getPrivilege()) && CollUtil.isNotEmpty(v.getPrivilege().getAllowUsers()))
                .map(v -> v.getPrivilege().getAllowUsers())
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(authUserIds)) {
            return Collections.emptyList();
        }
        authUserIds.removeIf(userIds::contains);
        if (CollUtil.isEmpty(authUserIds)) {
            return Collections.emptyList();
        }
        WxCpService wxCpService = thirdPartyService.getWeChatService(companyId);
        return authUserIds.stream().distinct().map(v -> {
                    try {
                        return wxCpService.getUserService().getById(v);
                    } catch (WxErrorException e) {
                        log.error("获取企业微信用户[{}]异常", v, e);
                        return null;
                    }
                }).filter(Objects::nonNull)
                .map(v -> {
                    ThirdPartyEmp emp = new ThirdPartyEmp();
                    emp.setName(v.getName());
                    emp.setPosition(v.getPosition());
                    emp.setAvatar(v.getAvatar());
                    emp.setThirdPartyId(v.getUserId());
                    // 用户的唯一标识
                    emp.setAccount(v.getUserId());
                    emp.setMobile(v.getMobile());
                    emp.setEmail(v.getEmail());
                    emp.setDeptIdList(Arrays.stream(v.getDepartIds()).map(Convert::toStr).collect(Collectors.toList()));
                    emp.setCompanyId(companyId);
                    return emp;
                }).collect(Collectors.toList());
    }

    @Override
    public String getType() {
        return ThirdPartyConstant.WECHAT;
    }

    private void userList(List<ThirdPartyEmp> userRspList,
                          String externalOrgId,
                          Long companyId,
                          WxCpService wxCpService) {
        // 部门下的员工详情
        List<WxCpUser> wxCpUsers = null;
        try {
            wxCpUsers = wxCpService.getUserService().listByDepartment(Convert.toLong(externalOrgId), false, 0);
        } catch (WxErrorException e) {
            Affirm.isTrue(WxCpErrorMsgEnum.CODE_60011.getCode() == e.getError().getErrorCode(), "请求微信获取部门下的员工异常");
        }

        if (CollUtil.isEmpty(wxCpUsers)) {
            return;
        }
        List<ThirdPartyEmp> emps = new ArrayList<>();
        for (WxCpUser wxCpUser : wxCpUsers) {
            ThirdPartyEmp emp = new ThirdPartyEmp();
            emp.setName(wxCpUser.getName());
            emp.setPosition(wxCpUser.getPosition());
            emp.setAvatar(wxCpUser.getAvatar());
            emp.setThirdPartyId(wxCpUser.getUserId());
            // 用户的唯一标识
            emp.setAccount(wxCpUser.getUserId());
            emp.setMobile(wxCpUser.getMobile());
            emp.setEmail(wxCpUser.getEmail());
            emp.setDeptIdList(Arrays.stream(wxCpUser.getDepartIds()).map(Convert::toStr).collect(Collectors.toList()));
            emp.setCompanyId(companyId);
            emps.add(emp);
        }
        userRspList.addAll(emps);
    }

}
