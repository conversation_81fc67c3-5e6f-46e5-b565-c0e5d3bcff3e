package com.niimbot.asset.thirdparty.service.impl;

import com.niimbot.asset.thirdparty.event.EventCO;
import com.niimbot.asset.thirdparty.service.OthersCallbackService;
import com.niimbot.asset.thirdparty.strategy.CallbackContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Since 2024-10-14
 */
@Service
public class OthersCallbackServiceImpl implements OthersCallbackService {

    private final CallbackContext callbackContext;

    public OthersCallbackServiceImpl(CallbackContext callbackContext) {
        this.callbackContext = callbackContext;
    }

    @Override
    public boolean handle(Long companyId, EventCO content) {
        return callbackContext.executeOthers(companyId, content);
    }

}
