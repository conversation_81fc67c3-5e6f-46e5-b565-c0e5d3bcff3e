package com.niimbot.asset.thirdparty.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheEmpStrategy;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.asset.thirdparty.model.ThirdPartyEmp;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.system.CusEmployeeTransferDto;
import com.niimbot.system.OrgDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/19 10:57
 */
@Slf4j
public abstract class AbsUserStrategy {

    @Resource
    private CusAccountService accountService;

    @Autowired
    private AsCusEmployeeService employeeService;

    @Autowired
    private AsCusEmployeeExtService employeeExtService;

    @Autowired
    private AsCusEmployeeSettingService employeeSettingService;

    @Autowired
    protected AsThirdPartyEmployeeService thirdPartyEmployeeService;

    @Autowired
    private AsAccountEmployeeService accountEmployeeService;

    @Autowired
    private CusUserService cusUserService;

    @Autowired
    private CusRoleService roleService;

    @Autowired
    private CusUserRoleService userRoleService;

    @Autowired
    private AsUserOrgService userOrgService;

    @Autowired
    protected OrgService orgService;

    @Autowired
    private SyncChangeService syncChangeService;

    @Resource
    private MessageService messageService;

    @Autowired
    private CusUserCompanyService cusUserCompanyService;

    @Autowired
    private UserPreviewDeleteService userPreviewDeleteService;

    @Autowired
    private AsDataPermissionService dataPermissionService;

    @Resource
    private ModelDataScopeServiceImpl modelDataScopeService;

    @Autowired
    private CompanyAssetService companyAssetService;

    @Value("${asset.domain.pc}")
    public String pcDomain;

    protected void createEmp(ThirdPartyEmp emp) {
        // 员工是否已经存在
        Optional<AsThirdPartyEmployee> exist = thirdPartyEmployeeService.getOne(emp.getThirdPartyType(), emp.getThirdPartyId(), emp.getCompanyId());
        if (exist.isPresent()) {
            log.warn("add user, company {} account {} has exist", emp.getCompanyId(), emp.getAccount());
            return;
        }

        Long id = IdUtils.getId();
        // ================================员工=======================================
        AsCusEmployee cusEmployee = new AsCusEmployee();
        cusEmployee.setId(id)
                .setCompanyId(emp.getCompanyId())
                .setImage(emp.getAvatar())
                .setEmpName(emp.getName())
                .setMobile(emp.getMobile())
                .setDataScope(DataPermRuleType.TYPE_ALL.getValue().shortValue())
                .setEmail(emp.getEmail())
                .setEmpNo(emp.getJobNum())
                .setPosition(emp.getPosition());
        AsThirdPartyEmployee thirdPartyEmployee = new AsThirdPartyEmployee()
                .setId(IdUtils.getId()).setCompanyId(emp.getCompanyId()).setEmployeeId(id)
                .setUserId(emp.getThirdPartyId()).setType(emp.getThirdPartyType());

        employeeService.save(cusEmployee);
        thirdPartyEmployeeService.save(thirdPartyEmployee);

        // ================================员工组织=======================================
        List<String> deptIdList = emp.getDeptIdList();
        List<AsUserOrg> userOrgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(deptIdList)) {
            List<AsOrg> orgList = orgService.list(new LambdaQueryWrapper<AsOrg>()
                    .select(AsOrg::getId)
                    .in(AsOrg::getExternalOrgId, deptIdList)
                    .eq(AsOrg::getCompanyId, emp.getCompanyId()));
            for (AsOrg org : orgList) {
                userOrgList.add(new AsUserOrg().setUserId(id).setOrgId(org.getId()));
            }
        }
        // 更新用户多组织
        userOrgService.remove(new LambdaQueryWrapper<AsUserOrg>().eq(AsUserOrg::getUserId, id));
        userOrgService.saveBatch(userOrgList);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(emp.getCompanyId(), ListUtil.of(id));

        // ================================员工扩展=======================================
        AsCusEmployeeExt employeeExt = new AsCusEmployeeExt();
        employeeExt.setId(id).setAgentId(0);
        employeeExtService.save(employeeExt);

        // ================================员工设置=======================================
        AsCusEmployeeSetting employeeSetting = new AsCusEmployeeSetting();
        employeeSetting.setUserId(id)
                .setAppToolbox(Lists.newArrayList())
                .setPcToolbox(Lists.newArrayList())
                .setPcHome(Lists.newArrayList())
                .setAssetHead(Lists.newArrayList())
                .setMaterialHead(Lists.newArrayList());
        employeeSettingService.save(employeeSetting);

        // 本地部署自动激活员工
        if (Edition.isLocal()) {
            employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getAccountStatus, 3).eq(AsCusEmployeeExt::getId, cusEmployee.getId()));
            // 创建账号
            AsCusUser account = new AsCusUser();
            account.setAccount(thirdPartyEmployee.getUserId())
                    .setUnionId(thirdPartyEmployee.getUserId())
                    .setThirdPartyId(thirdPartyEmployee.getUserId())
                    .setCompanyId(emp.getCompanyId())
                    .setId(IdUtils.getId())
                    .setMobile(cusEmployee.getMobile())
                    .setEmail(cusEmployee.getEmail())
                    .setPassword(new BCryptPasswordEncoder().encode("jc123456"))
                    .setSource(1);
            // 绑定账号
            AsAccountEmployee accountEmployee = new AsAccountEmployee()
                    .setAccountId(account.getId())
                    .setEmployeeId(cusEmployee.getId())
                    .setCompanyId(emp.getCompanyId());
            cusUserService.save(account);
            accountEmployeeService.save(accountEmployee);
            // 初始化数据权限
            dataPermissionService.initDataPermission(emp.getCompanyId(), cusEmployee.getId(), null);
        }

        /*
        // ================================角色关联=======================================
        AsCusRole role = roleService.getOne(new LambdaQueryWrapper<AsCusRole>()
                .eq(AsCusRole::getCompanyId, emp.getCompanyId()).eq(AsCusRole::getRoleCode, BaseConstant.COMMON_ROLE));
        AsUserRole userRole = new AsUserRole();
        userRole.setUserId(id);
        userRole.setRoleId(role.getId());
        userRoleService.save(userRole);

        // ================================用户公司=======================================
        AsUserCompany asUserCompany = new AsUserCompany().setUserId(id).setCompanyId(emp.getCompanyId());
        cusUserCompanyService.save(asUserCompany);*/

    }

    protected void updateEmp(ThirdPartyEmp emp) {
        // 查看UnionId是否存在
        Optional<AsThirdPartyEmployee> exist = thirdPartyEmployeeService.getOne(emp.getThirdPartyType(), emp.getThirdPartyId(), emp.getCompanyId());
        if (!exist.isPresent()) {
            log.warn("update user, company {} account {} has not exist", emp.getCompanyId(), emp.getAccount());
            return;
        }
        AsThirdPartyEmployee thirdPartyEmployee = exist.get();
        AsCusEmployee employee = employeeService.getById(thirdPartyEmployee.getEmployeeId());
        if (ObjectUtil.isNull(employee)) {
            log.warn("update user, company {} account {} has not exist", emp.getCompanyId(), emp.getAccount());
            return;
        }

        Long id = thirdPartyEmployee.getEmployeeId();
        employee.setId(id)
                .setCompanyId(emp.getCompanyId())
                .setEmpName(emp.getName())
                .setEmpNo(emp.getJobNum())
                .setPosition(emp.getPosition());
        if (StrUtil.isBlank(employee.getMobile())) {
            employee.setMobile(emp.getMobile());
        }
        if (StrUtil.isBlank(employee.getEmail())) {
            employee.setEmail(emp.getEmail());
        }
        employeeService.updateById(employee);

        thirdPartyEmployee.setUserId(emp.getThirdPartyId());
        thirdPartyEmployeeService.updateById(thirdPartyEmployee);

        // ================================员工组织=======================================
        List<String> deptIdList = emp.getDeptIdList();
        List<AsUserOrg> userOrgList = new ArrayList<>();
        List<Long> toOrgIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(deptIdList)) {
            List<AsOrg> orgList = orgService.list(new LambdaQueryWrapper<AsOrg>()
                    .select(AsOrg::getId)
                    .in(AsOrg::getExternalOrgId, deptIdList)
                    .eq(AsOrg::getCompanyId, emp.getCompanyId()));
            // 挂在根节点
            if (CollUtil.isEmpty(orgList)) {
                orgList = Lists.newArrayList(orgService.getRootOrg(thirdPartyEmployee.getCompanyId()));
            }
            for (AsOrg org : orgList) {
                toOrgIds.add(org.getId());
                userOrgList.add(new AsUserOrg().setUserId(id).setOrgId(org.getId()));
            }
        }

        // 查询用户原始组织
        List<AsUserOrg> list = userOrgService.list(new LambdaQueryWrapper<AsUserOrg>().eq(AsUserOrg::getUserId, id));
        List<Long> fromOrgIds = list.stream().map(AsUserOrg::getOrgId).collect(Collectors.toList());

        // 组织不一致记录异动
        if (CollUtil.isNotEmpty(fromOrgIds)) {
            // 跳过 A->AB
            if (!new HashSet<>(toOrgIds).containsAll(fromOrgIds)) {
                // 自动处理 A->B, AB->B, 多个部门合并为一个【ABC->B】
                if ((fromOrgIds.size() == toOrgIds.size() && fromOrgIds.size() == 1)
                        || (new HashSet<>(fromOrgIds).containsAll(toOrgIds) && toOrgIds.size() == 1) || toOrgIds.size() == 1) {
                    // 最后一次未处理的员工部门异动
                    AsSyncChange lastUntreatedRecord = syncChangeService.lastUntreatedRecord(emp.getCompanyId(), id, 1);
                    if (Objects.nonNull(lastUntreatedRecord) && CollUtil.isNotEmpty(lastUntreatedRecord.getFromOrg())) {
                        fromOrgIds.addAll(lastUntreatedRecord.getFromOrg().stream().distinct().filter(v -> !fromOrgIds.contains(v)).collect(Collectors.toList()));
                    }
                    fromOrgIds.removeAll(toOrgIds);
                    // 原始部门
                    List<AsOrg> fromOrgList = CollUtil.isEmpty(fromOrgIds) ? new ArrayList<>() : orgService.listAllByIds(fromOrgIds, emp.getCompanyId());
                    // 现部门
                    List<AsOrg> toOrgList = CollUtil.isEmpty(toOrgIds) ? new ArrayList<>() : orgService.listAllByIds(toOrgIds, emp.getCompanyId());
                    List<CusEmployeeTransferDto> transfers = fromOrgIds.stream().map(it -> {
                        CusEmployeeTransferDto transferDto = new CusEmployeeTransferDto();
                        transferDto.setFrom(it).setTo(toOrgIds.get(0));
                        return transferDto;
                    }).collect(Collectors.toList());
                    employeeService.changeOrgLog(fromOrgList, toOrgList, transfers, id, emp.getCompanyId());
                    // 删除还未处理的员工编辑异动记录
                    syncChangeService.removeUntreatedRecord(thirdPartyEmployee.getCompanyId(), thirdPartyEmployee.getEmployeeId(), 1);
                } else {
                    // 需要异动处理 A->BC, AB->BC, AB-CD
                    if (!companyAssetService.checkUsePerson(Collections.singletonList(id), emp.getCompanyId()).isEmpty()) {
                        AsSyncChange syncChange = new AsSyncChange();
                        AsSyncChange dingSyncChange = syncChange.setResId(id).setCompanyId(emp.getCompanyId()).setFromOrg(fromOrgIds).setToOrg(toOrgIds).setType(1);
                        // syncChangeService.save(dingSyncChange);
                        syncChangeService.records(Lists.newArrayList(dingSyncChange));
                        messageService.sendInnerMessage(MsgSendCmd.yd(emp.getCompanyId()));
                    }
                }
                // 踢出登录
                SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(
                        Lists.newArrayList(new AsCusUser().setId(thirdPartyEmployee.getEmployeeId()).setCompanyId(thirdPartyEmployee.getCompanyId()))
                );
            }
        }

        // 更新用户多组织
        userOrgService.remove(new LambdaQueryWrapper<AsUserOrg>().eq(AsUserOrg::getUserId, id));
        userOrgService.saveBatch(userOrgList);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(emp.getCompanyId(), ListUtil.of(id));
        // ================================数据权限=======================================
        /*// 员工角色
        List<AsUserRole> userRoles = userRoleService.list(
                Wrappers.<AsUserRole>lambdaQuery().eq(AsUserRole::getUserId, employee.getId()));
        if (CollUtil.isEmpty(userRoles)) {
            log.error("用户: {}, 无对应角色, 无法初始化权限", employee.getId());
            return;
        }
        List<Long> roleIds = userRoles.stream().map(AsUserRole::getRoleId).collect(Collectors.toList());
        List<AsCusRole> roles = roleService.listByIds(roleIds);
        String roleCode = findRoleCode(roles);
        dataPermissionService.initDataPermission(employee.getCompanyId(), employee.getId(), roleCode);
        */
        if (StrUtil.isNotEmpty(employee.getEmpName())) {
            SpringUtil.getBean(CacheEmpStrategy.class).evictCache(employee.getId());

        }

    }

    protected void deleteUser(String remoteId, Long companyId, String thirdpartyType) {
        Optional<AsThirdPartyEmployee> exist = thirdPartyEmployeeService.getOne(thirdpartyType, remoteId, companyId);
        Long userId;
        if (exist.isPresent()) {
            AsThirdPartyEmployee thirdPartyEmployee = exist.get();
            userId = thirdPartyEmployee.getEmployeeId();
        } else {
            userId = Convert.toLong(remoteId);
        }

        // 判断是否是本系统超级管理员
        AsCusEmployee currentEmployee = employeeService.getOne(Wrappers.lambdaQuery(AsCusEmployee.class)
                .eq(AsCusEmployee::getId, userId).eq(AsCusEmployee::getCompanyId, companyId));
        if (currentEmployee == null) {
            log.warn("delete user, company {} account {} has not exist", companyId, remoteId);
            return;
        }

        // 存在资产关联 或 设备保养任务
        Set<Long> ids = userPreviewDeleteService.checkUserAsset(ListUtil.of(userId), companyId);
        if (!ids.isEmpty() || userPreviewDeleteService.hasEntMatTask(userId, companyId)) {
            // 写入异动记录
            AsSyncChange syncChange = new AsSyncChange();
            AsSyncChange dingSyncChange = syncChange.setResId(userId).setCompanyId(companyId).setFromOrg(ListUtil.empty()).setToOrg(ListUtil.empty()).setType(2);
            // syncChangeService.save(dingSyncChange);
            syncChangeService.records(Lists.newArrayList(dingSyncChange));
            messageService.sendInnerMessage(MsgSendCmd.yd(companyId));
        }

        AsCusEmployee administrator = employeeService.getAdministratorByCompanyId(companyId);
        boolean isAdmin = administrator != null && administrator.getId().equals(userId);
        if (!isAdmin) {
            // 删除员工数据
            employeeService.removeById(userId);
            if (exist.isPresent()) {
                thirdPartyEmployeeService.removeById(exist.get().getId());
            }
            accountEmployeeService.unbindEmployee(userId);
            // userService.removeById(userId);
            employeeExtService.removeById(userId);
            employeeSettingService.removeById(userId);
            userOrgService.remove(new LambdaQueryWrapper<AsUserOrg>().eq(AsUserOrg::getUserId, userId));
            // 用户的部门有修改，需要删除权限缓存
            modelDataScopeService.cleanDataScopeCache(companyId, ListUtil.of(userId));
            userRoleService.remove(new LambdaQueryWrapper<AsUserRole>().eq(AsUserRole::getUserId, userId));

            // 删除组织主管数据
            List<OrgDto> orgDtoList = orgService.listByDirector(userId, companyId);
            List<AsOrg> updateOrgList = new ArrayList<>();
            for (OrgDto orgDto : orgDtoList) {
                List<Long> director = orgDto.getDirector();
                if (CollUtil.isNotEmpty(director)) {
                    director.remove(userId);
                    AsOrg org = new AsOrg();
                    org.setId(orgDto.getId());
                    org.setDirector(director);
                    updateOrgList.add(org);
                }
            }
            orgService.updateBatchById(updateOrgList);
            // 踢出当前用户
            accountService.kickOffLoginUser(Collections.singletonList(new AsCusUser().setCompanyId(currentEmployee.getCompanyId()).setId(currentEmployee.getId())));

        } else {
            employeeExtService.update(new LambdaUpdateWrapper<AsCusEmployeeExt>()
                    .set(AsCusEmployeeExt::getThirdPartyRemove, true)
                    .eq(AsCusEmployeeExt::getId, userId));
        }
    }

    private String findRoleCode(List<AsCusRole> roles) {
        boolean hasAdmin = false;
        boolean hasAssetAdmin = false;
        for (AsCusRole role : roles) {
            if (BaseConstant.ADMIN_ROLE.equals(role.getRoleCode())) {
                hasAdmin = true;
            }
            if (BaseConstant.ASSET_ADMIN_ROLE.equals(role.getRoleCode())) {
                hasAssetAdmin = true;
            }
        }
        if (hasAdmin) {
            return BaseConstant.ADMIN_ROLE;
        }
        if (hasAssetAdmin) {
            return BaseConstant.ASSET_ADMIN_ROLE;
        }

        return BaseConstant.COMMON_ROLE;
    }

}
