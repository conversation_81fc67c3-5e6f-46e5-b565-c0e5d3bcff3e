package com.niimbot.asset.thirdparty.config;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.thirdparty.constant.ThirdPartyAkSk;
import com.niimbot.asset.thirdparty.service.FeishuCallbackService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/15 10:04
 */
@Slf4j
@Component
public class FeishuBlockQueue extends Thread {

    public static final String BLOCK_QUEUE_FEISHU = "blockQueue:thirdParty:feishu";

    private final RBlockingQueue<FeishuQueueMsg> feishuBlockingQueue;
    private final FeishuCallbackService feishuCallbackService;

    @Autowired
    public FeishuBlockQueue(RedissonClient redissonClient,
                            FeishuCallbackService feishuCallbackService) {
        this.feishuBlockingQueue = redissonClient.getBlockingQueue(BLOCK_QUEUE_FEISHU, new JsonJacksonCodec());
        this.feishuCallbackService = feishuCallbackService;
        this.start();
    }

    @Override
    public void run() {
        while (true) {
            FeishuQueueMsg queueMsg = null;
            try {
                queueMsg = feishuBlockingQueue.take();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
//                Thread.currentThread().interrupt();
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex) {
                    // ignore
                }
            }

            if (queueMsg != null) {
                try {
                    // threadLocal写入公司Id
                    LoginUserDto userDto = new LoginUserDto();
                    userDto.setCusUser(new CusUserDto()
                            .setIsAdmin(true)
                            .setCompanyId(queueMsg.getCompanyId()));
                    LoginUserThreadLocal.set(userDto);
                    log.info("third party feishu msg [{}]", JSONObject.toJSONString(queueMsg));
                    feishuCallbackService.handle(queueMsg.getCompanyId(), queueMsg.getFeishu(), queueMsg.getContent());
                } catch (Exception e) {
                    log.error("process feishu callback fail, {}", e.getMessage(), e);
                } finally {
                    LoginUserThreadLocal.remove();
                }
            }
        }
    }

    public void put(FeishuQueueMsg queueMsg) {
        try {
            int size = feishuBlockingQueue.size();
            log.info("send feishu queue msg, {} waiting", size);
            feishuBlockingQueue.put(queueMsg);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
//            Thread.currentThread().interrupt();
            try {
                Thread.sleep(5000);
            } catch (InterruptedException ex) {
                // ignore
            }
        }
    }

    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeishuQueueMsg {
        private Long companyId;
        private ThirdPartyAkSk.Feishu feishu;
        private JSONObject content;
    }

}
