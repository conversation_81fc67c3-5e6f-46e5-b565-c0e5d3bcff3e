package com.niimbot.asset.material.resolver;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.means.service.StandardService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * MySQL资产查询条件处理器
 *
 * <AUTHOR>
 * @date 2021/12/7 15:42
 */
@Component
public class MySqlMaterialInventoryQueryConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {

    private static final String SQL_SEGMENT_TPL = "%s.%s";
    private static final String JSON_SQL_SEGMENT_TPL = "%s.material_snapshot_data ->> '$.%s'";

    private static final Map<String, QueryFieldConstant.Field> MATERIAL_INVENTORY_EXT_FIELD = new HashMap<>(QueryFieldConstant.MATERIAL_EXT_FIELD);

    @Autowired
    private AsQueryConditionConfigService queryConditionConfigService;
    @Autowired
    private AsFormService formService;
    @Autowired
    private StandardService standardService;

    public MySqlMaterialInventoryQueryConditionResolver() {
        MATERIAL_INVENTORY_EXT_FIELD.put("actualQuantity", new QueryFieldConstant.Field("actualQuantity", "实际库存", FormFieldCO.NUMBER_INPUT, ""));
        MATERIAL_INVENTORY_EXT_FIELD.put("surplusQuantity", new QueryFieldConstant.Field("surplusQuantity", "盘盈数量", FormFieldCO.NUMBER_INPUT, ""));
        MATERIAL_INVENTORY_EXT_FIELD.put("lossQuantity", new QueryFieldConstant.Field("lossQuantity", "盘亏数量", FormFieldCO.NUMBER_INPUT, ""));
        MATERIAL_INVENTORY_EXT_FIELD.put("actualInventoryUser", new QueryFieldConstant.Field("actualInventoryUser", "实际盘点人", FormFieldCO.YZC_EMP, ""));
        MATERIAL_INVENTORY_EXT_FIELD.put("inventoryTerminal", new QueryFieldConstant.Field("inventoryTerminal", "盘点终端", FormFieldCO.SELECT_DROPDOWN, ""));
        MATERIAL_INVENTORY_EXT_FIELD.put("inventoryMode", new QueryFieldConstant.Field("inventoryMode", "盘点方式", FormFieldCO.SELECT_DROPDOWN, ""));
        MATERIAL_INVENTORY_EXT_FIELD.put("inventoryTime", new QueryFieldConstant.Field("inventoryTime", "盘点时间", FormFieldCO.DATETIME, ""));
    }

    public QueryConditionSortDto sortField() {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.TYPE_MATERIAL_HEAD);
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        // 标准品
        Long standardId = queryHeadConfig.getStandardId();
        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (standardId != null && standardId > 0L) {
            List<FormFieldCO> extField = standardService.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(extField);
        }
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));
        // 补齐 创建时间、更新时间
        QueryFieldConstant.Field createTimeField = QueryFieldConstant.MATERIAL_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }

        QueryFieldConstant.Field updateTimeField = QueryFieldConstant.MATERIAL_EXT_FIELD.get(QueryFieldConstant.FIELD_UPDATE_TIME);
        if (ObjectUtil.isNotNull(updateTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(updateTimeField.getName(), updateTimeField.getCode(), updateTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        ImmutableMap.copyOf(MATERIAL_INVENTORY_EXT_FIELD));
                if (StringUtils.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition) {
        if (MATERIAL_INVENTORY_EXT_FIELD.containsKey(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        }
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, code);
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }
}
