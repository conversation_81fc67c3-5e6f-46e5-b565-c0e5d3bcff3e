package com.niimbot.asset.material.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 耗材出库单据详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsMaterialCkOrderDetail对象", description = "耗材出库单据详情表")
@TableName(value = "as_material_ck_order_detail", autoResultMap = true)
public class AsMaterialCkOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "耗材出库单据ID")
    private Long orderId;

    @ApiModelProperty(value = "耗材ID")
    private Long materialId;

    @ApiModelProperty(value = "耗材变动前的数据JSON")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject materialSnapshotData;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal ckNum;

    @ApiModelProperty(value = "出库金额")
    private BigDecimal ckPrice;

    @ApiModelProperty(value = "入库单价")
    private BigDecimal ckUnitPrice;

}
