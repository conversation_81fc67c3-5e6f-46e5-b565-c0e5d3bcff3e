package com.niimbot.asset.material.resolver;

import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.means.service.StandardService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.StringJoiner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * MySQL资产查询条件处理器
 *
 * <AUTHOR>
 * @date 2021/12/7 15:42
 */
@Component
public class MySqlMaterialQueryConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {

    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.material_data ->> '$.%s'";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.material_data ->> '$.%s' as DECIMAL(20,4))";
    public static final String DATE_JSON_SQL_SEGMENT_TPL = "LPAD(%s.material_data ->> '$.%s', 13, 0)";

    @Autowired
    private AsQueryConditionConfigService queryConditionConfigService;
    @Autowired
    private AsFormService formService;
    @Autowired
    private StandardService standardService;

    public QueryConditionSortDto sortField() {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.TYPE_MATERIAL_HEAD);
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        // 标准品
        Long standardId = queryHeadConfig.getStandardId();
        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_MATERIAL);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (standardId != null && standardId > 0L) {
            List<FormFieldCO> extField = standardService.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(extField);
        }
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));
        // 补齐 创建时间、更新时间
        QueryFieldConstant.Field createTimeField = QueryFieldConstant.MATERIAL_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }

        QueryFieldConstant.Field updateTimeField = QueryFieldConstant.MATERIAL_EXT_FIELD.get(QueryFieldConstant.FIELD_UPDATE_TIME);
        if (ObjectUtil.isNotNull(updateTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(updateTimeField.getName(), updateTimeField.getCode(), updateTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition, joiner);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        QueryFieldConstant.MATERIAL_EXT_FIELD);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition, StringJoiner joiner) {
        if (QueryFieldConstant.MATERIAL_EXT_FIELD.containsKey(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            } else if (QueryFieldConstant.MATERIAL_STOCK_FILED_STATUS.equals(condition.getCode())) {
                // 特殊处理库存预警状态
                if (condition.getQuery().equals("1")) {
                    // 正常
                    String tpl = " and ({}.current_quantity >= {}.material_data ->> '$.safetyStock' or {}.material_data ->> '$.safetyStock' is null or {}.material_data ->> '$.safetyStock' = '' or {}.material_data ->> '$.safetyStock' = 'null') ";
                    String format = StrUtil.format(tpl, tableAlias, tableAlias, tableAlias, tableAlias, tableAlias);
                    joiner.add(format);
                    return format;
                } else if (condition.getQuery().equals("2")) {
                    // 预警
                    String tpl = " and {}.current_quantity < {}.material_data ->> '$.safetyStock' ";
                    String format = StrUtil.format(tpl, tableAlias, tableAlias);
                    joiner.add(format);
                    return format;
                } else {
                    return StrUtil.EMPTY;
                }
            } else if (QueryFieldConstant.MATERIAL_STOCK_FILED_STOCK.equals(condition.getCode())) {
                // 特殊处理库存状态
                if (condition.getQuery().equals("1")) {
                    // 无库存
                    String tpl = " and {}.current_quantity > 0 ";
                    String format = StrUtil.format(tpl, tableAlias);
                    joiner.add(format);
                    return format;
                } else if (condition.getQuery().equals("2")) {
                    // 有库存
                    String tpl = " and ({}.current_quantity = 0 or {}.repository_id is NULL) ";
                    String format = StrUtil.format(tpl, tableAlias, tableAlias);
                    joiner.add(format);
                    return format;
                } else {
                    return StrUtil.EMPTY;
                }
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        }
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, code);
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }
}
