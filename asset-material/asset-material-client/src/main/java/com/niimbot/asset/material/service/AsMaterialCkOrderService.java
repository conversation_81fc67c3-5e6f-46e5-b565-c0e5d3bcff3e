package com.niimbot.asset.material.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.material.model.AsMaterialCkOrder;
import com.niimbot.asset.material.model.AsMaterialCkOrderDetail;
import com.niimbot.asset.material.model.AsMaterialLyOrderDetail;
import com.niimbot.material.GrantingDto;
import com.niimbot.material.MaterialCkOrderDto;
import com.niimbot.material.MaterialCkOrderSubmitDto;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;

import java.util.List;

/**
 * <p>
 * 耗材出库单据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-12
 */
public interface AsMaterialCkOrderService extends IService<AsMaterialCkOrder> {

    WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, MaterialCkOrderDto orderDto);

    AuditableCreateOrderResult create(LoginUserDto loginUserDto, MaterialCkOrderSubmitDto submitDto);

    void createOrder(MaterialCkOrderDto orderDto, List<AsMaterialLyOrderDetail> updateLyOrderDetail, boolean enableWorkflow);

    MaterialCkOrderDto getOrderDetail(Long id);

    MaterialCkOrderDto getOrderDetailWithTkInfo(Long orderId);

    MaterialCkOrderDto getOrderDetailWithTkInfo(String orderNo);

    AsMaterialCkOrderDetail getDetail(Long orderId, Long materialId);

    IPage<MaterialCkOrderDto> pageCk(MaterialOrderQueryDto dto);

    IPage<AsMaterialCkOrderDetail> pageDetail(MaterialOrderDetailQueryDto dto);

    Boolean processCallback(WorkflowCallbackDto callbackDto);

    IPage<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto dto);

    List<GrantingDto> grantingDetail(Long orderId, Long materialId);

    List<GrantingDto> grantDetail(Long orderId, Long materialId);

}
