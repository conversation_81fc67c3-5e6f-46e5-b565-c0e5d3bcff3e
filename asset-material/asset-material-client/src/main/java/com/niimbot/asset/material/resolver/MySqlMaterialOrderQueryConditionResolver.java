package com.niimbot.asset.material.resolver;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.MaterialResultCode;
import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.material.service.AsMaterialOrderService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * MySQL资产查询条件处理器
 *
 * <AUTHOR>
 * @date 2021/12/7 15:42
 */
@Component
public class MySqlMaterialOrderQueryConditionResolver
        extends AbsQueryConditionResolver<String, QueryConditionDto> {
    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.order_data ->> '$.%s'";
    public static final String DATE_JSON_SQL_SEGMENT_TPL = "LPAD(%s.order_data ->> '$.%s', 13, 0)";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.order_data ->> '$.%s' as DECIMAL(20,4))";
    public static final String DETAIL_JSON_SQL_SEGMENT_TPL = "%s.material_snapshot_data ->> '$.%s'";

    @Autowired
    private AsMaterialOrderService materialOrderService;

    public String resolveQueryCondition(String tableAlias, Integer orderType, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, orderType, condition);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        QueryFieldConstant.ORDER_COMMON_EXT_FIELD);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, Integer orderType, QueryConditionDto condition) {
        if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(condition.getCode())) {
            if (QueryFieldConstant.FIELD_TK_IN_REPO.equals(condition.getCode()) && !ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_TK)) {
                return getJsonSqlField(tableAlias, condition.getCode());
            }
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        }
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, code);
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }

    public Page<?> buildOrderSort(String tableAlias, Integer orderType, MaterialOrderQueryDto queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            if (ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_RK)) {
                tableAlias = "as_material_rk_order";
            } else if (ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_CK)) {
                tableAlias = "as_material_ck_order";
            } else if (ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_LY)) {
                tableAlias = "as_material_ly_order";
            } else if (ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_TZ)) {
                tableAlias = "as_material_tz_order";
            } else if (ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_DB)) {
                tableAlias = "as_material_db_order";
            } else if (ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_BS)) {
                tableAlias = "as_material_bs_order";
            } else if (ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_TK)) {
                tableAlias = "as_material_tk_order";
            } else {
                throw new BusinessException(MaterialResultCode.ORDER_TYPE_NOT_EXISTS, Convert.toStr(orderType));
            }
        }
        Page<?> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = materialOrderService.sortField(orderType);
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                if (QueryFieldConstant.FIELD_TK_IN_REPO.equals(column) && !ObjectUtil.equal(orderType, AssetConstant.ORDER_TYPE_MATERIAL_TK)) {
                    order.setColumn(String.format(JSON_SQL_SEGMENT_TPL, tableAlias, column));
                } else {
                    order.setColumn(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
                }
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    /**
     * 查询条件处理
     *
     * @param tableAlias 表别名
     * @param q          查询条件
     * @return
     */
    @Deprecated
    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> q) {
        return null;
    }
}
