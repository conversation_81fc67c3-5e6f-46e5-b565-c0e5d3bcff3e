package com.niimbot.asset.material.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.asset.framework.model.AbstractTree;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 耗材分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsMaterialCategory对象", description = "耗材分类表")
@TableName(value = "as_material_category")
public class AsMaterialCategory extends AbstractTree implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自定义分类名称")
    private String categoryName;

    @ApiModelProperty(value = "自定义分类编码")
    private String categoryCode;

    @ApiModelProperty(value = "公司ID（租户ID） 默认0为系统配置分类")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "0-未删除  1：已删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
