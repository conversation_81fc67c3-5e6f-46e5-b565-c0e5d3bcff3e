package com.niimbot.asset.material.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.material.model.AsMaterial;
import com.niimbot.equipment.EntMatPlanSrPrData;
import com.niimbot.equipment.GetSelectedSrPrData;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialDto;
import com.niimbot.material.MaterialQueryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.ImportImages;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryKwSearchDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 耗材表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface AsMaterialService extends IService<AsMaterial> {

    AsMaterial add(AsMaterial material);

    Boolean edit(AsMaterial material);

    Boolean deleteBatchPre(List<Long> ids);

    List<AuditableOperateResult> deleteBatch(List<Long> materialIds, Boolean checkOrder);

    Boolean hasOrder(Long companyId, List<Long> materialIds);

    IPage<MaterialDto> materialPage(MaterialQueryDto queryDto);

    IPage<EntMatPlanSrPrData> pageEntMatPlanMrlData(GetSelectedSrPrData get);

    MaterialDto getInfoNoPerm(Long materialId);

    /**
     * 根据耗材id或耗材phpId获取耗材详情
     *
     * @param materialId
     * @return
     */
    MaterialDto getInfoNoPermPhp(String materialId);

    Integer countCompanyMaterial();

    Boolean saveSheetData(AssetImportDto importDto);

    Boolean saveEditSheetData(AssetImportDto importDto);

    void saveSheetHead(HeadImportErrorDto importErrorDto);

    List<List<LuckySheetModel>> importError(Long taskId);

    QueryConditionSortDto sortField();

    List<ImportImages> importImagesCheck(List<ImportImages> codes, Integer action);

    List<ImportImages> importImages(List<ImportImages> images, Integer action);

    AsMaterial getInRecycleBin(Long id);

    void transformKwFields(List<QueryKwSearchDto> kwFiled,String kw);

    Map<Long, JSONObject> getMaterialSnapshot(List<Long> materialIds);
}
