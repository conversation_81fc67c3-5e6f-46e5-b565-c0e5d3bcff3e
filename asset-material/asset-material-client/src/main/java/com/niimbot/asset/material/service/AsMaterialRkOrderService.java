package com.niimbot.asset.material.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.material.model.AsMaterialRkOrder;
import com.niimbot.asset.material.model.AsMaterialRkOrderDetail;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;
import com.niimbot.material.MaterialRkOrderDto;
import com.niimbot.material.MaterialRkOrderSubmitDto;

/**
 * <p>
 * 耗材入库单据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-12
 */
public interface AsMaterialRkOrderService extends IService<AsMaterialRkOrder> {

    WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, MaterialRkOrderDto orderDto);

    AuditableCreateOrderResult create(LoginUserDto loginUserDto, MaterialRkOrderSubmitDto submitDto);

    void createOrder(MaterialRkOrderDto orderDto, boolean enableWorkflow);

    MaterialRkOrderDto getOrderDetail(Long id);

    AsMaterialRkOrderDetail getDetail(Long orderId, Long materialId);

    IPage<MaterialRkOrderDto> pageRk(MaterialOrderQueryDto dto);

    IPage<AsMaterialRkOrderDetail> pageDetail(MaterialOrderDetailQueryDto dto);

    Boolean processCallback(WorkflowCallbackDto callbackDto);

    IPage<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto dto);

}
