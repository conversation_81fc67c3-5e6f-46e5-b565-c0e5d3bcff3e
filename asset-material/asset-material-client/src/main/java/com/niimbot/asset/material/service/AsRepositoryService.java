package com.niimbot.asset.material.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.service.CommonCodeService;
import com.niimbot.asset.material.model.AsRepository;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialRepositoryImportDto;
import com.niimbot.material.MaterialRepositorySearchDto;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;

import java.util.List;

/**
 * <p>
 * 耗材仓库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface AsRepositoryService extends IService<AsRepository>, CommonCodeService {

    Long add(AsRepository repository);

    Boolean update(AsRepository repository);

    void loadRepoCache(List<AsRepository> repositories);

    IPage<AsRepository> search(MaterialRepositorySearchDto searchDto);

    List<AsRepository> excelContent(MaterialRepositorySearchDto dto);

    List<AuditableOperateResult> deleteList(List<Long> ids);

    List<AsRepository> listPermission(MaterialRepositorySearchDto searchDto);

    List<Long> hasPermRepoIds(List<Long> repoIds);

    List<List<LuckySheetModel>> importError(Long taskId);

    void saveSheetHead(HeadImportErrorDto importErrorDto);

    Boolean saveSheetData(MaterialRepositoryImportDto importDto);


}
