package com.niimbot.asset.material.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.framework.dataperm.annonation.OrderFilterColumn;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.framework.dataperm.annonation.UserFilterColumn;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 耗材出库单据表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsMaterialCkOrder对象", description = "耗材出库单据表")
@TableName(value = "as_material_ck_order", autoResultMap = true)
public class AsMaterialCkOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "耗材出库单据ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @OrderFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_MATERIAL_ORDER, subBizCode = AssetConstant.AUTHORITY_APPROVAL_USER)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "单据审批状态 0-无审批 1-待审批 2-已驳回 3-已同意 4-已撤销")
    private Short approveStatus;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "单据编号：字母前缀CK+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "自定义数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject orderData;

    @ApiModelProperty(value = "总种类")
    private Integer totalType;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    @UserFilterColumn(bizCode = AssetConstant.DATA_PERMISSION_MATERIAL_ORDER, subBizCode = AssetConstant.AUTHORITY_CREATE_USER)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
