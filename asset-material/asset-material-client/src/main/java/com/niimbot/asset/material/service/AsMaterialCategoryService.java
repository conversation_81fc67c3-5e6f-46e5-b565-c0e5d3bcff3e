package com.niimbot.asset.material.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.service.CommonCodeService;
import com.niimbot.asset.material.model.AsMaterialCategory;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.material.MaterialCategoryImportDto;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;

import java.util.List;

/**
 * <p>
 * 耗材分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface AsMaterialCategoryService extends IService<AsMaterialCategory>, CommonCodeService {

    Long add(AsMaterialCategory category);

    Boolean update(AsMaterialCategory category);

    List<AuditableOperateResult> delete(Long id);

    List<AuditableOperateResult> deleteList(List<Long> ids);

    Boolean sort(List<Long> ids);

    void loadCateCache(List<AsMaterialCategory> categories);

    List<List<LuckySheetModel>> importError(Long taskId);

    void saveSheetHead(HeadImportErrorDto importErrorDto);

    Boolean saveSheetData(MaterialCategoryImportDto importDto);

    List<AsMaterialCategory> listByCategoryNamePermission(String kw);
}
