package com.niimbot.asset.material.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.material.model.AsMaterialStock;
import com.niimbot.material.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 耗材库存表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-08
 */
public interface AsMaterialStockService extends IService<AsMaterialStock> {

    IPage<MaterialStockDto> stockPage(MaterialStockQueryDto queryDto);

    IPage<Long> stockPageIds(MaterialStockQueryDto queryDto);

    MaterialTotalStockDto stockTotal(MaterialStockQueryDto queryDto);

    IPage<MaterialOrderStockDto> orderStockPage(MaterialOrderStockQueryDto queryDto);

    /**
     * 调整库存（上游需要加锁）material_stock_lock:companyId
     *
     * @param adjustStock 耗材库存调整
     * @param companyId   公司Id
     * @return true/false
     */
    boolean adjustStock(AdjustStockDto adjustStock, Long companyId);

    List<String> hasStock(List<Long> materialIds);

    /**
     * 单个耗材的库存分布情况
     *
     * @param materialId 耗材ID
     * @return
     */
    IPage<MaterialStockDetailDto> stockDetail(MaterialStockDetailQueryDto queryDto);

    /**
     * 回补
     *
     * @param ckNum
     * @param ckPrice
     * @param materialId
     * @param outRepoId
     */
    void backPriceAndStock(BigDecimal ckNum, BigDecimal ckPrice, Long materialId, Long outRepoId);

    /**
     * 查询盘点库存数据
     *
     * @param inventoryRepo
     * @return
     */
    List<MaterialStockDto> inventoryStock(List<Long> inventoryRepo);
}
