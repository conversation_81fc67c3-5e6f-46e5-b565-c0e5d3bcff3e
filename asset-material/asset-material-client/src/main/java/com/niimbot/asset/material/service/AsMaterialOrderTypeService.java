package com.niimbot.asset.material.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.material.model.AsMaterialOrderType;
import com.niimbot.material.MaterialOrderTypeDto;

import java.util.List;

/**
 * <p>
 * 单据类型配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface AsMaterialOrderTypeService extends IService<AsMaterialOrderType> {

    void initCompanyOrderType(Long companyId);

    Boolean syncCompanyOrderType(Long companyId);

    Boolean enableWorkflow(Integer orderType);

    List<MaterialOrderTypeDto> listOrderType();
}
