package com.niimbot.asset.material.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.system.handle.LongListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 耗材仓库表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsRepository对象", description = "耗材仓库表")
@TableName(value = "as_repository", autoResultMap = true)
public class AsRepository implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "仓库名称")
    private String name;

    @ApiModelProperty(value = "仓库编码")
    private String code;

    @ApiModelProperty(value = "公司ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "所属公司")
    private Long managerOwner;

    @ApiModelProperty(value = "所属公司名称")
    @TableField(exist = false)
    private String managerOwnerText;

    @ApiModelProperty("管理员列表")
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> admins;

    @ApiModelProperty("仓库备注")
    private String remark;

    @ApiModelProperty(value = "0-未删除  1：已删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
