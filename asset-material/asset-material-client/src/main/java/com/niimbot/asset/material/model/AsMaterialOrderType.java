package com.niimbot.asset.material.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单据类型配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsMaterialOrderType对象", description = "单据类型配置表")
@TableName(value = "as_material_order_type", autoResultMap = true)
public class AsMaterialOrderType implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "单据名称")
    private String name;

    @ApiModelProperty(value = "单据类型：31-耗材入库 32-耗材出库 33-耗材领用 ")
    private Short type;

    @ApiModelProperty(value = "流程key")
    private String activitiKey;

    @ApiModelProperty(value = "流程说明")
    private String description;

    private String icon;

    @ApiModelProperty(value = "是否可配置")
    private Boolean enableConfig;

    @ApiModelProperty(value = "app是否启用")
    private Boolean enableApp;

    @ApiModelProperty(value = "启用流程 0-否  1-是")
    private Boolean enableWorkflow;

    @ApiModelProperty(value = "审批流是否可配置")
    private Boolean enableWorkflowConfig;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
