package com.niimbot.asset.material.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.material.model.AsMaterialLyOrder;
import com.niimbot.asset.material.model.AsMaterialLyOrderDetail;
import com.niimbot.material.MaterialLyOrderDetailDto;
import com.niimbot.material.MaterialLyOrderDto;
import com.niimbot.material.MaterialLyOrderSubmitDto;
import com.niimbot.material.MaterialOrderDetailQueryDto;
import com.niimbot.material.MaterialOrderExportDto;
import com.niimbot.material.MaterialOrderQueryDto;

/**
 * <p>
 * 耗材入库单据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-13
 */
public interface AsMaterialLyOrderService extends IService<AsMaterialLyOrder> {

    WorkflowExecuteDto getWorkflowStepList(LoginUserDto loginUserDto, MaterialLyOrderDto orderDto);

    AuditableCreateOrderResult create(LoginUserDto loginUserDto, MaterialLyOrderSubmitDto submitDto);

    Boolean createOrder(MaterialLyOrderDto orderDto, boolean enableWorkflow);

    /**
     * 单据详情
     *
     * @param id
     * @return
     */
    MaterialLyOrderDto getOrderDetail(Long id);

    MaterialLyOrderDto getOrderDetailByNo(String orderNo);

    /**
     * 单据明细详情查询
     *
     * @param orderId
     * @param materialId
     * @return
     */
    AsMaterialLyOrderDetail getDetail(Long orderId, Long materialId);

    IPage<MaterialLyOrderDto> pageLy(MaterialOrderQueryDto query);

    IPage<MaterialLyOrderDetailDto> pageDetail(MaterialOrderDetailQueryDto query);

    Boolean processCallback(WorkflowCallbackDto callbackDto);

    /**
     * 资产单据-用于导出
     *
     * @param dto dto
     * @return 结果
     */
    IPage<MaterialOrderExportDto> listForExport(MaterialOrderQueryDto dto);

}
