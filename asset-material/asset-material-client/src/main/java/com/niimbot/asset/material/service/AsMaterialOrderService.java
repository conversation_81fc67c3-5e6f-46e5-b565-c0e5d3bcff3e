package com.niimbot.asset.material.service;

import com.niimbot.activiti.WorkflowApproveDto;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.material.inventory.MaterialOrderPreCheckDto;
import com.niimbot.system.QueryConditionSortDto;

/**
 * <AUTHOR>
 * @date 2022/10/17 15:52
 */
public interface AsMaterialOrderService {

    FormVO getForm(Integer orderType, Long companyId);

    QueryConditionSortDto sortField(Integer orderType);

    MaterialOrderPreCheckDto materialOrderPreCheck(WorkflowApproveDto approveDto);
}
