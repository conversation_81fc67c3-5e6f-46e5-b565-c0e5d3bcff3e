package com.niimbot.asset.system.adapter.ding.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.TodoAbs;
import com.niimbot.asset.system.adapter.ding.service.DingEmployeeService;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.CusAccountService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.ding.BatchChangeEmpAccountStatus;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/6 11:37
 */
@Service
@RequiredArgsConstructor
public class DingEmployeeServiceImpl implements DingEmployeeService {

    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    private final AsCusEmployeeExtService employeeExtService;

    private final CusAccountService accountService;

    private final CusUserService cusUserService;

    private final AsAccountEmployeeService accountEmployeeService;

    private final TodoAbs todoAbs;

    private final AsCusEmployeeService employeeService;

    @Override
    public List<String> dingUserIdList(List<Long> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return ListUtil.empty();
        }
        List<AsThirdPartyEmployee> thirdPartyEmployees = thirdPartyEmployeeService.list(Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                .eq(AsThirdPartyEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(AsThirdPartyEmployee::getEmployeeId, empIds));
        return thirdPartyEmployees.stream().map(AsThirdPartyEmployee::getUserId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, String> dingUserIdMap(List<Long> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return MapUtil.empty();
        }
        List<AsThirdPartyEmployee> thirdPartyEmployees = thirdPartyEmployeeService.list(Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                .select(AsThirdPartyEmployee::getEmployeeId, AsThirdPartyEmployee::getUserId)
                .eq(AsThirdPartyEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(AsThirdPartyEmployee::getEmployeeId, empIds));
        return thirdPartyEmployees.stream().collect(Collectors.toMap(AsThirdPartyEmployee::getEmployeeId, AsThirdPartyEmployee::getUserId));
    }

    @Override
    public Boolean batchChangeAccountStatus(BatchChangeEmpAccountStatus body) {
        if (body.getAccountStatus() == 1) {
            Map<Long, String> idNameMap = employeeService.listByIds(body.getEmpIds()).stream().collect(Collectors.toMap(AsCusEmployee::getId, AsCusEmployee::getEmpName));
            Map<Long, Long> unHandle = todoAbs.countUserUnHandle(body.getEmpIds());
            StringJoiner joiner = new StringJoiner("，");
            idNameMap.forEach((k, v) -> {
                Long count = unHandle.getOrDefault(k, 0L);
                if (count > 0) {
                    joiner.add(v);
                }
            });
            String names = joiner.toString();
            if (StrUtil.isNotBlank(names)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, names + "有审批未处理，请先处理审批后再操作");
            }
        }
        // 超管不让被禁用
        Long adminId = employeeService.getAdministrator().getId();
        if (body.getEmpIds().contains(adminId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "超管禁止禁用账号");
        }
        if (body.getEmpIds().contains(LoginUserThreadLocal.getCurrentUserId())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前禁用列表包含自己");
        }
        employeeExtService.update(
                Wrappers.lambdaUpdate(AsCusEmployeeExt.class)
                        .set(AsCusEmployeeExt::getAccountStatus, body.getAccountStatus())
                        .in(AsCusEmployeeExt::getId, body.getEmpIds())
        );
        List<AsAccountEmployee> accountEmployees = accountEmployeeService.listByEmployeeId(body.getEmpIds());
        List<AsCusUser> collect = accountEmployees.stream().map(k -> {
            AsCusUser cusUser = new AsCusUser();
            cusUser.setCompanyId(k.getCompanyId());
            cusUser.setId(k.getEmployeeId());
            return cusUser;
        }).collect(Collectors.toList());
        accountService.kickOffLoginUser(collect);
        return true;
    }

}
