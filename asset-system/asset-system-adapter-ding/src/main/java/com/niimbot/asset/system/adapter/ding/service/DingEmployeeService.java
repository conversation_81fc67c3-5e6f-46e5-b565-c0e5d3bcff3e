package com.niimbot.asset.system.adapter.ding.service;

import com.niimbot.ding.BatchChangeEmpAccountStatus;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/6 11:35
 */
public interface DingEmployeeService {

    List<String> dingUserIdList(List<Long> empIds);

    Map<Long, String> dingUserIdMap(List<Long> empIds);

    Boolean batchChangeAccountStatus(BatchChangeEmpAccountStatus body);

}
