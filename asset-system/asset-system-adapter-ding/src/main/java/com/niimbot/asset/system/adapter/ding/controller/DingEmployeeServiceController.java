package com.niimbot.asset.system.adapter.ding.controller;

import com.niimbot.asset.system.adapter.ding.service.DingEmployeeService;
import com.niimbot.ding.BatchChangeEmpAccountStatus;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/6 11:33
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("server/system/employee")
public class DingEmployeeServiceController {

    private final DingEmployeeService dingEmployeeService;

    @PostMapping("/dingUserIdList")
    public List<String> dingUserIdList(@RequestBody List<Long> empIds) {
        return dingEmployeeService.dingUserIdList(empIds);
    }

    @PostMapping("/batch/change/accountStatus")
    public Boolean batchChangeEmpAccountStatus(@RequestBody BatchChangeEmpAccountStatus body) {
        return dingEmployeeService.batchChangeAccountStatus(body);
    }

}
