package com.niimbot.asset.system.adapter.ding.model;

import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * <AUTHOR>
 */
@Data
@Document(indexName = "asset_ding_audit_log")
@FieldNameConstants
public class AuditLogDocument {

    @Id
    @Field(type = FieldType.Long)
    private Long id;

    @Field(type = FieldType.Text)
    private String url;

    @Field(type = FieldType.Object)
    private Object params;

    @Field(type = FieldType.Keyword)
    private String terminalCode;

    @Field(type = FieldType.Keyword)
    private String terminalText;

    @Field(type = FieldType.Long)
    private Long companyId;

    @Field(index = false, type = FieldType.Keyword)
    private String operator;

    @Field(type = FieldType.Long)
    private Long orgId;

    @Field(index = false, type = FieldType.Keyword)
    private String orgName;

    @Field(type = FieldType.Long)
    private Long roleId;

    @Field(index = false, type = FieldType.Keyword)
    private String roleName;

    @Field(type = FieldType.Text)
    private String actionCode;

    @Field(type = FieldType.Text)
    private String actionText;

    @Field(type = FieldType.Text)
    private String content;

    @Field(type = FieldType.Long)
    private Long createBy;

    @Field(type = FieldType.Long)
    private Long createTime;

}
