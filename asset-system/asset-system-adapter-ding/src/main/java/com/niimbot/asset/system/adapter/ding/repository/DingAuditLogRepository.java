package com.niimbot.asset.system.adapter.ding.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.adapter.ding.model.AuditLogDocument;
import com.niimbot.asset.system.adapter.ding.model.AuditLogDocument.Fields;
import com.niimbot.asset.system.model.AsAuditLog;
import com.niimbot.asset.system.repository.AuditLogRepository;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogSearch;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class DingAuditLogRepository implements AuditLogRepository {

    @Resource
    private EsAuditLogRepository esAuditLogRepository;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public boolean existById(Long id) {
        try {
            Optional<AuditLogDocument> optional = esAuditLogRepository.findById(id);
            return optional.isPresent();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void save(AsAuditLog log) {
        AuditLogDocument document = BeanUtil.copyProperties(log, AuditLogDocument.class, Fields.createTime);
        document.setCreateTime(log.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        esAuditLogRepository.save(document);
    }

    @Override
    public PageUtils<AuditLogDto> search(AuditLogSearch search) {
        Page<Object> page = search.buildIPage();
        Pageable pageable = PageRequest.of(Convert.toInt(page.getCurrent()) - 1, Convert.toInt(page.getSize()));
        // 查询条件
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withPageable(pageable);
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        // 租户隔离
        query.must(QueryBuilders.termQuery(Fields.companyId, LoginUserThreadLocal.getCompanyId()));
        // 关键字搜索
        if (StrUtil.isNotBlank(search.getKw())) {
            BoolQueryBuilder kwQuery = QueryBuilders.boolQuery();
            kwQuery.should(QueryBuilders.matchPhraseQuery(Fields.actionText, search.getKw()));
            kwQuery.should(QueryBuilders.matchPhraseQuery(Fields.content, search.getKw()));
            query.must(kwQuery);
        }
        // 操作人搜索
        if (CollUtil.isNotEmpty(search.getCreateBys())) {
            query.must(QueryBuilders.termsQuery(Fields.createBy, search.getCreateBys()));
        }
        // 操作组织搜索
        if (CollUtil.isNotEmpty(search.getOrgIds())) {
            query.must(QueryBuilders.termsQuery(Fields.orgId, search.getOrgIds()));
        }
        // 操作人角色搜索
        if (CollUtil.isNotEmpty(search.getRoleIds())) {
            query.must(QueryBuilders.termsQuery(Fields.roleId, search.getRoleIds()));
        }
        // 时间范围搜索
        if (CollUtil.isNotEmpty(search.getCreateTimes()) && search.getCreateTimes().size() == 2) {
            RangeQueryBuilder timeRange = QueryBuilders.rangeQuery(Fields.createTime);
            timeRange.gte(LocalDateTime.parse(search.getCreateTimes().get(0), formatter).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            timeRange.lte(LocalDateTime.parse(search.getCreateTimes().get(1), formatter).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            query.must(timeRange);
        }
        builder.withQuery(query);
        FieldSortBuilder fieldSort = SortBuilders.fieldSort(Fields.createTime);
        fieldSort.order(SortOrder.DESC);
        if (StrUtil.isNotBlank(search.getSidx()) && search.getSidx().equals(Fields.createTime)) {
            SortOrder order = SortOrder.fromString(search.getOrder());
            fieldSort.order(order);
        }
        builder.withSort(fieldSort);
        NativeSearchQuery build = builder.build();
        SearchHits<AuditLogDocument> searchHits = elasticsearchRestTemplate.search(build, AuditLogDocument.class);
        long totalHits = searchHits.getTotalHits();
        List<AuditLogDto> result = searchHits.getSearchHits().stream().map(v -> {
            AuditLogDocument doc = v.getContent();
            AuditLogDto dto = new AuditLogDto();
            dto.setId(doc.getId()).setCompanyId(doc.getCompanyId()).setOperator(doc.getOperator())
                    .setOrgId(doc.getOrgId()).setOrgName(doc.getOrgName())
                    .setRoleId(doc.getRoleId()).setRoleName(doc.getRoleName())
                    .setActionText(doc.getActionText()).setContent(doc.getContent())
                    .setTerminalCode(doc.getTerminalCode()).setTerminalText(doc.getTerminalText())
                    .setCreateBy(doc.getCreateBy()).setCreateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(doc.getCreateTime()), ZoneId.systemDefault()));
            return dto;
        }).collect(Collectors.toList());
        return new PageUtils<>(result, Convert.toInt(totalHits), Convert.toInt(search.getPageSize()), Convert.toInt(search.getPageNum()));
    }
}
