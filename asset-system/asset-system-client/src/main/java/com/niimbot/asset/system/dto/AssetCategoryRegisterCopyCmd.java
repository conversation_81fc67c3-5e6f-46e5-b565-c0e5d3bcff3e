package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/5/11 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetCategoryRegisterCopyCmd extends CommonCommand {
    private Long companyId;

    private Collection<Long> categories;
}
