package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsRoleDataAuthority;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/22 上午10:57
 */
public interface AsRoleDataAuthorityService extends IService<AsRoleDataAuthority> {

    /**
     * 初始化角色数据权限
     * @param companyId
     * @param roleId
     * @param roleCode
     */
    void initRoleDataAuth(Long companyId, Long roleId, String roleCode);

    /**
     * 查询企业角色配置的数据权限
     * @param companyId
     * @param roleId
     * @return
     */
    List<AsRoleDataAuthority> queryRoleDataAuthByRoleId(Long companyId, Long roleId);
}
