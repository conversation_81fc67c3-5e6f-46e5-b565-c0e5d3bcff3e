package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class OrgCO extends ClientObject {

    @ApiModelProperty(value = "组织编号ID")
    private Long id;

    @ApiModelProperty("对接外部的orgId")
    private String externalOrgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "父级组织")
    private Long pid;

    @ApiModelProperty("外部组织的PID")
    private String externalPid;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "路径paths")
    private String paths;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "所属公司")
    private Long companyOwner;

    @ApiModelProperty(value = "组织类型:1-公司,2-部门")
    private Integer orgType;

    @ApiModelProperty(value = "主管ID")
    private List<Long> director;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "备注")
    private String remark;

}
