package com.niimbot.asset.system.dto;

import com.alibaba.cola.dto.PageQuery;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DataSnapshotGetQry extends PageQuery {

    private Long companyId = LoginUserThreadLocal.getCompanyId();

    private Long sourceId;

    private Integer sourceType;

    private Integer dataType;
}
