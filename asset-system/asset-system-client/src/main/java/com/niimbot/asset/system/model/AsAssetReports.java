package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 新增资产数统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsAssetReports对象", description="新增资产数统计表")
@TableName(value = "as_asset_reports")
public class AsAssetReports implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "新增资产数")
    private Integer newAssetNum;

    @ApiModelProperty(value = "删除资产数")
    private Integer deleteAssetNum;

    @ApiModelProperty(value = "时间 单位：天")
    private LocalDate dayTime;

    @ApiModelProperty(value = "所在一年的第几周")
    private Integer weekTime;


}
