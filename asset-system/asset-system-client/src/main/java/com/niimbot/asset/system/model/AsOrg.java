package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.framework.model.AbstractTree;
import com.niimbot.asset.system.handle.LongListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 组织表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsOrg对象", description = "组织表")
@TableName(value = "as_org", autoResultMap = true)
public class AsOrg extends AbstractTree implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("来源类型：1-系统，2-钉钉自建，3-企微自建")
    private Integer sourceType;

    @ApiModelProperty("对接外部的orgId")
    private String externalOrgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty("外部组织的PID")
    private String externalPid;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "所属公司")
    private Long companyOwner;

    @ApiModelProperty(value = "组织类型:1-公司,2-部门")
    private Integer orgType;

    @ApiModelProperty(value = "主管ID")
    @TableField(typeHandler = LongListTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<Long> director;

    @ApiModelProperty(value = "0-未删除  1：已删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("外部主管ID")
    @TableField(exist = false)
    private List<String> externalDirector;

    public void hierarchy(AsOrg parent) {
        this.setPid(parent.getId());
        this.setLevel(parent.getLevel() + 1);
        this.setPaths(parent.getPaths() + this.getId() + ",");
    }

}
