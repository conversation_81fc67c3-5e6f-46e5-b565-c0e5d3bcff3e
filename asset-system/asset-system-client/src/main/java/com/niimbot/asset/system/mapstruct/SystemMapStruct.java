package com.niimbot.asset.system.mapstruct;

import com.niimbot.asset.system.dto.clientobject.*;
import com.niimbot.asset.system.model.*;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface SystemMapStruct {

    OrgCO convertOrgDoToCo(AsOrg org);

    EmployeeCO convertEmployeeDoTo<PERSON>o(AsCusEmployee employee);

    CompanySettingCO convertCompanySettingDoToCo(AsCompanySetting companySetting);

    List<EmployeeCO> convertEmployeeDoListToCoList(List<AsCusEmployee> employees);

    DictDataCO convertDictDataDoToCo(AsDictData dictData);

    List<DictDataCO> convertDictDataDoListToCoList(List<AsDictData> dictData);

    EmployeeResignRecordCO convertEmployeeResignRecordDoToCo(AsEmployeeResignRecord employeeResignRecord);

}
