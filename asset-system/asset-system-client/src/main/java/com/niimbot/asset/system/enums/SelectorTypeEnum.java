package com.niimbot.asset.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SelectorTypeEnum {
    /**
     * 组织选择器 -> 组织权限
     */
    ORG
            (
                    new Table("as_org", "id", "org_name", "org_code")
            ),
    /**
     * 组织员工选择器 -> 组织权限
     */
    ORG_EMP
            (
                    new Table("as_cus_employee", "id", "emp_name", "emp_no")
            ),
    /**
     * 审批角色选择器 -> 无权限控制
     */
    APPROVE_ROLE
            (
                    new Table("act_approve_role", "id", "name", "code")
            ),
    /**
     * 角色员工选择器 -> 功能权限控制
     */
    ROLE_EMP
            (
                    new Table("as_cus_employee", "id", "emp_name", "emp_no")
            ),
    /**
     * 资产分类选择器 -> 跟随配置
     */
    ASSET_CATE
            (
                    new Table("as_category", "id", "category_name", "category_code")
            ),
    /**
     * 耗材分类选择器 -> 无数据权限
     */
    MATERIAL_CATE
            (
                    new Table("as_material_category", "id", "category_name", "category_code")
            ),
    /**
     * 耗材仓库选择器 -> 跟随配置的
     */
    REPOSITORY
            (
                    new Table("as_repository", "id", "name", "code")
            ),
    /**
     * 供应商选择器 -> 无权限
     */
    SUPPLIER
            (
                    new Table("as_supplier", "id", "name", "''")
            ),
    /**
     * 选择器 -> 跟随配置的
     */
    AREA
            (
                    new Table("as_area", "id", "area_name", "area_code")
            ),
    /**
     * 选公司 -> 部门权限
     */
    COMPANY
            (
                    new Table("as_org", "id", "org_name", "org_code")
            ),
    /**
     * 巡检点位 -> 区域权限
     */
    SNT_POINT
            (
                    new Table("as_equipment_site_inspect_point", "id", "point_name", "point_code")
            );

    private final Table table;

    private static final String TPL = "%s AS id, %s AS `name`, %s AS `code`";

    @Getter
    public static class Table {

        private final String name;

        private final String columns;

        public Table(String name, String... columns) {
            this.name = name;
            this.columns = String.format(TPL, columns[0], columns[1], columns[2]);
        }
    }

    public static class Filter {

    }

}
