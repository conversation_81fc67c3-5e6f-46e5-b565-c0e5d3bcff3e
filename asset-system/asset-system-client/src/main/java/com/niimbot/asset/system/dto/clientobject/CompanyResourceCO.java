package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/8 15:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class CompanyResourceCO extends ClientObject {

    /**
     * 企业资源ID
     */
    private Long id;

    /**
     * 企业资源ID
     */
    private Long companyId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 资源包名称
     */
    private String resourceName;

    /**
     * 资源容量
     */
    private Integer capacity;

    /**
     * 是否体验资源
     */
    private Boolean experience;

    /**
     * 到期时间
     */
    private LocalDateTime expirationTime;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

}
