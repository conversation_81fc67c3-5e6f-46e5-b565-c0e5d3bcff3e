package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/18 10:32
 */
@Data
@TableName(value = "as_open_api_scope", autoResultMap = true)
public class AsOpenApiScope {

    // 主键ID
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    // 权限类型
    private String scopeType;

    // 权限名称
    private String scopeName;

    // 权限分组
    private String scopeGroup;


}
