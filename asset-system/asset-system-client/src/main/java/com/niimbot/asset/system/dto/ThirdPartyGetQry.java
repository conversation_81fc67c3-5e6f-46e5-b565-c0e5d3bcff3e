package com.niimbot.asset.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/6 18:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ThirdPartyGetQry extends CommonCommand {
    /**
     * 企业ID
     */
    private Long id;
}
