package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/11 17:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AdminPrinterCO extends ClientObject {
    /** 
     * 主键ID 
     */
    private Long id;

    /** 
     * 设备名称 
     */
    private String name;

    /** 
     * 设备型号 
     */
    private String model;

    /** 
     * 打印机系列 1-B32系列 2-B50系列 3-佐藤系列 
     */
    private Long type;

    /** 
     * 是否rfid支持 0否 1是 
     */
    private Boolean isRfid;

    /** 
     * 是否启用碳带防伪 0-否  1-是 
     */
    private Boolean enableAnti;

    /** 
     * 是否需认证 0-否  1-是 
     */
    private Boolean enableAuth;

    /** 
     * 认证方式 1、设备序列号 2、企业名称 
     */
    private Short authType;

    /** 
     * 是否适用于pc 0-否  1-是 
     */
    private Boolean isApplyPc;

    /** 
     * 是否适用于app 0-否  1-是 
     */
    private Boolean isApplyApp;
}
