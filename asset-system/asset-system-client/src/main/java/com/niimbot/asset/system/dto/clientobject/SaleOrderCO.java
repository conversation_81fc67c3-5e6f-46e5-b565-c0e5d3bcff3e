package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 版本销售单
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class SaleOrderCO extends ClientObject {

    /**
     * 销售单ID
     */
    private Long id;

    /**
     * 公司ID（租户ID）
     */
    private Long companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 单据编号：字母前缀ON+年月日时分秒+时间戳的微秒数小数点后半部分的前四位
     */
    private String orderNo;

    /**
     * 商品摘要
     */
    private String summary;

    /**
     * 订单来源: 1-web, 2-android, 3-iOS
     */
    private Integer source;

    /**
     * 订单总金额
     */
    private BigDecimal totalMoney;

    /**
     * 订单支付金额
     */
    private BigDecimal payMoney;

    /**
     * 抵扣金额
     */
    private BigDecimal discountMoney = BigDecimal.ZERO;

    /**
     * 支付方式: 1-支付宝, 2-微信, 3-对公转账
     */
    private Integer payType;

    /**
     * 支付过期时间
     */
    private LocalDateTime expiredTime;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 收款账号
     */
    private String receivePaymentNo;

    /**
     * 操作时间(支付/关闭)
     */
    private LocalDateTime operateTime;

    /**
     * 支付状态: 1-待付款, 2-已完成, 3-已关闭
     */
    private Integer status;

    /**
     * 开票状态:0-无需开票，1-待开票，2-开票中，3-已开票
     */
    private Integer invoiceStatus;

    /**
     * 中台订单同步状态
     */
    private Integer middlendSyncStatus;

    /**
     * 用户手机
     */
    private String mobile;

    /**
     * 发票信息
     */
    private InvoiceInfoCO invoiceInfo;

    /**
     * 销售员
     */
    private SellerCO seller;

    /**
     * 用户备注
     */
    private String customerRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @ApiModelProperty(value = "购买资源包数量")
    private Integer quantity;
}
