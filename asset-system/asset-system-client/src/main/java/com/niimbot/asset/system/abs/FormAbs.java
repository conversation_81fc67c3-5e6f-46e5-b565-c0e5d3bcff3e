package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.BizFormAssetInitCmd;
import com.niimbot.asset.system.dto.BizFormMaterialInitCmd;
import com.niimbot.asset.system.dto.FormGetQry;
import com.niimbot.asset.system.dto.FormInitCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022/7/12 19:58
 */
public interface FormAbs {

    String BIZ_TYPE_ASSET = "asset";
    String BIZ_TYPE_MATERIAL = "material";
    String BIZ_TYPE_STANDARD = "standard";

    @GetMapping("getTplByType")
    FormVO getTplByType(@RequestParam("type") String type);

    @GetMapping("getFormById")
    FormVO getFormById(@SpringQueryMap FormGetQry qry);

    /**
     * 初始化企业表单
     *
     * @param cmd
     */
    @PostMapping("initCompanyForm")
    void initCompanyForm(FormInitCmd cmd);

    @PostMapping("initAssetItems")
    void initAssetItems(BizFormAssetInitCmd cmd);

    @PostMapping("initMaterialItems")
    void initMaterialItems(BizFormMaterialInitCmd cmd);

}
