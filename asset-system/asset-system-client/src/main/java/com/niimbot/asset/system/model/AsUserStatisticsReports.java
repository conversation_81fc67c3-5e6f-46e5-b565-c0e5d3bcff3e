package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户活跃状态数量定时统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsUserStatisticsReports对象", description="用户活跃状态数量定时统计表")
@TableName(value = "as_user_statistics_reports")
public class AsUserStatisticsReports implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "新用户活跃数")
    private Integer newUserActiveNum;

    @ApiModelProperty(value = "老用户活跃数")
    private Integer oldUserActiveNum;

    @ApiModelProperty(value = "删除用户数")
    private Integer deleteUserNum;

    @ApiModelProperty(value = "沉睡用户数")
    private Integer sleepUserNum;

    @ApiModelProperty(value = "流失用户数")
    private Integer lostUserNum;

    @ApiModelProperty(value = "时间 单位：天")
    private String dayTime;

    @ApiModelProperty(value = "所在一年的第几周")
    private Integer weekTime;


}
