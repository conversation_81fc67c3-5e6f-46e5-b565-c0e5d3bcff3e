package com.niimbot.asset.system.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 打印模板
 * 来源于云打印应用
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsPrintTemplate", description = "打印模板")
@TableName(value = "as_print_template", autoResultMap = true)
public class AsPrintTemplate implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("模板来源ID")
    private String sourceId;

    @ApiModelProperty("模板来源：1-云打印")
    private Integer sourceType = 1;

    @ApiModelProperty("来源账号")
    private String sourceAccount;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板宽度")
    private Integer width;

    @ApiModelProperty("模板高度")
    private Integer height;

    @ApiModelProperty("预览图地址")
    private String preview;

    @ApiModelProperty("标签信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject tagInfo;

    @ApiModelProperty("模板编辑动作：1-新增 2-更新")
    private Integer action;

    @ApiModelProperty("模板状态：1-待审核 2-已审核")
    private Integer status;

    @ApiModelProperty("打印JSON")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject printJson;

//    @ApiModelProperty("原始信息")
//    @TableField(typeHandler = FastjsonTypeHandler.class)
//    private JSONObject original;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
