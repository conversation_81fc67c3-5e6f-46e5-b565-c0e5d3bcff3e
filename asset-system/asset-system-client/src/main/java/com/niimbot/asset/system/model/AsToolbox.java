package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 工具箱表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsToolbox对象", description = "工具箱表")
public class AsToolbox implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工具箱ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "工具箱名称")
    private String toolboxName;

    @ApiModelProperty(value = "显示名称")
    private String showName;

    @ApiModelProperty(value = "工具箱编码")
    private String toolboxCode;

    @ApiModelProperty(value = "图标")
    private String toolboxIcon;

    @ApiModelProperty(value = "类型 1-app管理端 2-app员工端")
    private Short toolboxType;

    @ApiModelProperty(value = "显示位置 1 -工具箱  2-常用工具")
    private Short position;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "单据类型")
    private Long orderType;

    @ApiModelProperty(value = "业务类型：1-单据、2-资产上报")
    private Integer businessType;

    @ApiModelProperty(value = "app是否启用")
    private Boolean enableApp;

    @ApiModelProperty(value = "pc是否启用")
    private Boolean enablePc;

    @ApiModelProperty(value = "是否常用 0-否 1-是")
    private Integer commonlyUsed;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private Integer isDelete;
}
