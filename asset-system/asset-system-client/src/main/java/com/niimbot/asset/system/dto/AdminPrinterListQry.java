package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/7 16:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AdminPrinterListQry extends CommonCommand {
    /**
     * 是否适用于app 0-否  1-是
     */
    private Boolean isApplyApp;
}
