package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class ThirdPartyCO extends ClientObject {

    /** 
     * 公司ID 
     */
    private Long companyId;

    /** 
     * 组织同步类型（钉钉，企业微信等） 
     */
    private String type;

    /** 
     * 表单数据 
     */
    private JSONObject form;
}
