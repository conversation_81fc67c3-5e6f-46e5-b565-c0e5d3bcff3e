package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCompanyNewbieTask;
import com.niimbot.system.CompanyNewbieTaskDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CompanyNewbieTaskService extends IService<AsCompanyNewbieTask> {

    List<CompanyNewbieTaskDto> listTask(Long companyId);

    void init(Long companyId);

}
