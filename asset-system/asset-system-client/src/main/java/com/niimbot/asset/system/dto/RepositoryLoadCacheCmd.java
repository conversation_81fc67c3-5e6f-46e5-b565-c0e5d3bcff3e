package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.dto.clientobject.RepositoryCO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 11:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class RepositoryLoadCacheCmd extends CommonCommand {
    private List<RepositoryCO> repositories;
}
