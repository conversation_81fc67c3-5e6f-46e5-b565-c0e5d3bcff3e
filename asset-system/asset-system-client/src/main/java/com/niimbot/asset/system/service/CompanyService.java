package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.system.SalesOwnerDto;

import java.util.List;

import cn.hutool.core.date.DateTime;

/**
 * <AUTHOR>
 * @since 2020/10/20 12:35
 */
public interface CompanyService extends IService<AsCompany> {

    /**
     * 修改企业名称
     *
     * @param name name
     * @return 成功与否
     */
    Boolean changeCompanyName(String name);

    Integer countOfExcludedTest(DateTime date);

    AsCompany checkIsExist(Long companyId);

    void cleanCompanyStatusCache(Long companyId);

    void cleanCompanyStatusCache(List<Long> companyIds);

    /**
     * 查询企业规模
     * @param companyId
     * @return
     */
    Integer queryEnterpriseSize(Long companyId);

    /**
     * 查询企业标签信息
     * @param companyId
     * @return
     */
    List<String> queryCompanyTag(Long companyId);

    SalesOwnerDto getSalesOwner();
}
