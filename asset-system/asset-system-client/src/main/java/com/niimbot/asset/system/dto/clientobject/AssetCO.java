package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.alibaba.fastjson.JSONObject;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AssetCO extends ClientObject {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "标准品ID")
    private Long standardId;

    @ApiModelProperty(value = "属性JSON数据")
    private JSONObject assetData;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "业务调整前状态")
    private Integer beforeStatus;

    @ApiModelProperty(value = "本次维修前状态（冗余字段，可关联最后一次维修前状态）")
    private Integer repairBeforeStatus;

    @ApiModelProperty(value = "RFID-TID")
    private String labelTid;

    @ApiModelProperty(value = "rfid_epcid")
    private String labelEpcid;

    /*public String getLabelEpcid() {
        return id + "0";
    }*/

    @ApiModelProperty("最新打印时间")
    private LocalDateTime lastPrintTime;

    @ApiModelProperty(value = "是否删除 0-否  time-是")
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "虚拟键——资产编码")
    private String assetCode;

    @ApiModelProperty(value = "虚拟键——组织ID")
    private String orgOwner;

    @ApiModelProperty(value = "虚拟键——员工ID")
    private String usePerson;

    @ApiModelProperty(value = "虚拟键——资产分类ID")
    private String assetCategory;

    @ApiModelProperty(value = "虚拟键——资产名称")
    private String assetName;

    @ApiModelProperty(value = "虚拟键——区域ID")
    private String storageArea;

    @ApiModelProperty(value = "虚拟键——资产来源")
    private String assetOrigin;

    @ApiModelProperty(value = "虚拟键——所属管理员")
    private String managerOwner;

    @ApiModelProperty(value = "虚拟键——使用组织")
    private String useOrg;

}
