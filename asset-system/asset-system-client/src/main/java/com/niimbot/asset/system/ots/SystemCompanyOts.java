package com.niimbot.asset.system.ots;

import com.niimbot.asset.system.dto.CompanySettingTodoConfigEditCmd;
import com.niimbot.asset.system.dto.CompleteNewbieTaskCmd;
import com.niimbot.asset.system.dto.clientobject.CompanySettingCO;
import com.niimbot.asset.system.model.CompanySwitch;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
public interface SystemCompanyOts {

    @GetMapping("getById/{id}")
    CompanySettingCO getCompanySettingById(@PathVariable("id") Long id);

    @PutMapping("editCompanySettingTodoConfig")
    Boolean editCompanySettingTodoConfig(@RequestBody CompanySettingTodoConfigEditCmd cmd);

    @GetMapping("getSwitchWithCache/{id}")
    CompanySwitch getSwitchSettingWithCache(@PathVariable("id") Long id);

    @GetMapping("getCompanyPaymentStatus")
    Integer getCompanyPaymentStatus(Long companyId);

    @PostMapping("completeNewbieTask")
    void completeNewbieTask(@RequestBody CompleteNewbieTaskCmd cmd);
}
