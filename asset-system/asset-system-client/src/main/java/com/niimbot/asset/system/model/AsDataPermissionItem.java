package com.niimbot.asset.system.model;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据权限项
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@ApiModel(value = "AsDataPermissionItem对象", description = "数据权限项")
public class AsDataPermissionItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final String COMBO_KEY = "k";
    private static final String COMBO_VALUE = "v";

    @ApiModelProperty(value = "权限项名称")
    private String label;

    @ApiModelProperty(value = "权限项名称是否展示")
    private Boolean labelIsShow;

    @ApiModelProperty(value = "是否选中")
    private Boolean select;

    @ApiModelProperty(value = "权限编码")
    private String authorityCode;

    @ApiModelProperty(value = "权限类型: 0-所有数据, 1-本人, 2-本组织(公司), 3-本组织(公司)和子组织(公司), 4-自定义")
    private Integer authorityType;

    @ApiModelProperty(value = "后缀描述")
    private String suffixDesc;

    @ApiModelProperty(value = "自定义权限数据")
    private List<Object> authorityData;

    @ApiModelProperty(value = "下拉数据源")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray combo;

    @ApiModelProperty(value = "权限类型下拉数据源")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray typeCombo;

    public boolean hasCombo() {
        return CollUtil.isNotEmpty(combo);
    }

    public Map<String, String> getComboMap() {
        if (hasCombo()) {
            return combo.toJavaList(JSONObject.class)
                    .stream().collect(Collectors.toMap(
                                    o -> o.getString(COMBO_KEY),
                                    o -> o.getString(COMBO_VALUE)));
        }
        return null;
    }
}
