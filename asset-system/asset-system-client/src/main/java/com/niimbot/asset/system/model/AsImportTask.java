package com.niimbot.asset.system.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.system.ImportTaskExtInfoDto;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导入导出任务
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsImportTask对象", description="导入导出任务")
@TableName(value = "as_import_task", autoResultMap = true)
public class AsImportTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "企业ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "任务类型：1-导入任务，2-导出任务")
    private Integer type;

    @ApiModelProperty(value = "业务类型")
    private Integer importType;

    @ApiModelProperty(value = "任务状态：1-进行中，2-失败，3-正常")
    private Integer taskStatus;

    @ApiModelProperty(value = "导入任务扩展信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private ImportTaskExtInfoDto extInfo;

    @ApiModelProperty(value = "总成功数量")
    private Integer totalSuccessNum;

    @ApiModelProperty(value = "成功数量")
    private Integer successNum;

    @ApiModelProperty(value = "失败数量")
    private Integer failureNum;

    @ApiModelProperty(value = "总数量")
    private Integer total;

    @ApiModelProperty(value = "列表模糊查询字段")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject queryData;

    @ApiModelProperty(value = "导出下载地址")
    private String url;

    @ApiModelProperty(value = "导出失败重新请求地址")
    private String exportUrl;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime finishTime;

}
