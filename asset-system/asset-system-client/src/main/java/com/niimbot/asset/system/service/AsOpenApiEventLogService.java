package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.openapi.dto.OpenApiMessageSendDto;
import com.niimbot.asset.system.model.AsOpenApiEventLog;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/1 16:58
 */
public interface AsOpenApiEventLogService extends IService<AsOpenApiEventLog> {

    void sendBatch(Map<Long, List<OpenApiMessageSendDto>> sendMap);
}
