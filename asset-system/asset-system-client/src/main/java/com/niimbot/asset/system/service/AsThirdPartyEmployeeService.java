package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface AsThirdPartyEmployeeService extends IService<AsThirdPartyEmployee> {

    List<AsThirdPartyEmployee> listByCompanyId(Long companyId, String type);

    Optional<AsThirdPartyEmployee> getByEmployeeId(Long employeeId);

    Optional<AsThirdPartyEmployee> getOne(String type, String userId, Long companyId);

    void removeByCompanyId(Long companyId, String type);

    String getUserId(Long empId);

    Map<String, Long> getExternalMapping(Long companyId, List<String> userIds);

}
