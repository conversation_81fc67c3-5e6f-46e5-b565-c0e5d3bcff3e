package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.system.handle.AsDataPermissionItemListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据权限
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AsDataPermission对象", description = "数据权限")
@TableName(value = "as_data_permission", autoResultMap = true)
public class AsDataPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "数据对象")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String name;

    @ApiModelProperty(value = "数据编码")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String code;

    @ApiModelProperty(value = "数据描述")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String dataDesc;

    /**
     * 加了一个层级：仅可查看自己资产数据
     */
    @ApiModelProperty(value = "权限项")
    @TableField(typeHandler = AsDataPermissionItemListTypeHandler.class, updateStrategy = FieldStrategy.NEVER)
    private List<AsDataPermissionItem> perms;
}
