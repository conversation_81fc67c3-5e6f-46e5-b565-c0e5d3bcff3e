package com.niimbot.asset.system.abs;

import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.system.dto.AssetAreaLoadCacheCmd;
import com.niimbot.asset.system.dto.AssetAreaSaveCmd;
import com.niimbot.asset.system.model.AsDataAuthority;

import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 资产区域
 * 原始接口 {@link com.niimbot.asset.means.service.AreaService}
 * <AUTHOR>
 * @date 2022/5/10 18:12
 */
public interface AssetAreaAbs {

    /**
     * 保存区域
     *
     * @param cmd
     */
    @PostMapping("saveAssetArea")
    void saveAssetArea(AssetAreaSaveCmd cmd);

    /**
     * 加载到缓存
     * areaService.loadAreaCache(ListUtil.of(area))
     * @param cmd
     * @return
     */
    @PostMapping("loadCacheAssetArea")
    void loadCacheAssetArea(AssetAreaLoadCacheCmd cmd);

    @PostMapping("listSimpleWithPerms")
    List<DictDataDto> listSimpleWithPerms(AsDataAuthority authority, Long companyId);

    @PostMapping("hasPermAreaIds")
    List<Long> hasPermAreaIds(List<Long> areaIds);
}
