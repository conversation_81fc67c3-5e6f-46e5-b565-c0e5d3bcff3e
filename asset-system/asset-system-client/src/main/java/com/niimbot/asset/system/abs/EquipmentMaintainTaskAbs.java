package com.niimbot.asset.system.abs;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/16 下午4:58
 */
public interface EquipmentMaintainTaskAbs {

    /**
     * 查询资产是否存在待保养/已逾期/保养中的任务
     * @param assetId
     * @return
     */
    @GetMapping("countMaintainTask/{assetId}")
    Integer countMaintainTask(@PathVariable("assetId") Long assetId);

    /**
     * 删除备件
     * @param assetId
     * @return
     */
    @GetMapping("removeSpareParts/{assetId}")
    Boolean removeSpareParts(@PathVariable("assetId")Long assetId);

    /**
     * 根据耗材删除备件
     * @param materialIds
     * @return
     */
    @GetMapping("removeByMaterialId")
    Boolean removeByMaterialId(@RequestParam("materialIds") List<Long> materialIds);
}
