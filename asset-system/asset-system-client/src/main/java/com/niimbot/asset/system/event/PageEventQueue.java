package com.niimbot.asset.system.event;


import com.niimbot.asset.system.model.AsModuleStatistics;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 事件队列
 * <AUTHOR>
 * @date 2023/4/11 下午2:33
 */
public class PageEventQueue {

    private BlockingQueue<AsModuleStatistics> recordsBasket = new LinkedBlockingQueue<>(10000);

    private static class PageEventHolder {
        private static final PageEventQueue INSTANCE = new PageEventQueue();
    }

    private PageEventQueue() {
    }

    public static PageEventQueue getInstance() {
        return PageEventHolder.INSTANCE;
    }

    public void producePageEvent(AsModuleStatistics moduleStatistics) {
        recordsBasket.add(moduleStatistics);
    }

    public AsModuleStatistics consumePageEvent() {
        return recordsBasket.poll();
    }
}
