package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsFile;
import com.niimbot.system.AsFileDownDto;
import com.niimbot.system.FileParamDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文件上传下载service
 *
 * <AUTHOR>
 * @Date 2020/11/30
 */
public interface AsFileService extends IService<AsFile> {

    /**
     * 批量上传文件
     *
     * @param fileList     上传的文件
     * @param fileParamDto 文件上传相关控制参数
     * @return 结果
     * @throws IOException
     */
    List<AsFile> uploadFile(List<MultipartFile> fileList, FileParamDto fileParamDto) throws IOException;

    /**
     * custom/manage端下载
     *
     * @param id     文件id
     * @param delete 下载后是否删除原文件
     * @return 文件dto
     */
    AsFileDownDto feignFileDownload(Long id, Boolean delete);

    /**
     * 批量下载
     *
     * @param ids 批量下载文件id
     * @return 字节数组
     */
    byte[] feignFileDownloadBatch(List<Long> ids);

    /**
     * 获取文件dto列表
     *
     * @param ids 文件id
     * @return 文件dto列表
     */
    List<AsFileDownDto> listByIds(List<Long> ids);
}
