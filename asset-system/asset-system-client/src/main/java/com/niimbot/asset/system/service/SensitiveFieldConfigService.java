package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.system.SensitiveFieldItemDto;
import com.niimbot.asset.system.model.AsSensitiveFieldConfig;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2023/7/21 下午4:29
 */
public interface SensitiveFieldConfigService extends IService<AsSensitiveFieldConfig> {

    /**
     * 根据数据对象编码获取敏感字段配置
     * @param code
     * @return
     */
    List<SensitiveFieldItemDto> getSensitiveFieldByCode(String code) throws ExecutionException;

    /**
     * 获取所以敏感字段配置
     * @return
     */
    List<AsSensitiveFieldConfig> allSensitiveConfig();
}
