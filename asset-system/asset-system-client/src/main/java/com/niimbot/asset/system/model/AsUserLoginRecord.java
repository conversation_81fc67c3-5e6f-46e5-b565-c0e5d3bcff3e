package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户登录记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsUserLoginRecord对象", description = "用户登录记录表")
@TableName(value = "as_user_login_record")
public class AsUserLoginRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "账号id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "账号id")
    private Long userId;

    @ApiModelProperty("员工ID")
    private Long employeeId;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "登陆时间")
    private LocalDateTime loginTime;

    @ApiModelProperty(value = "来源  1-本系统（pc端） 2-本系统（Android端）3-本系统（iOS端）4-本系统(PAD)")
    private Integer source;

}
