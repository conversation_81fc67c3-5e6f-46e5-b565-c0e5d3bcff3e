package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_store_record", autoResultMap = true)
@ApiModel(value = "AsStoreRecord", description = "入库记录表")
public class AsStoreRecord {

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("企业ID")
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty("入库单号")
    private String storeNo;

    @ApiModelProperty("入库数量")
    private Integer storeQuantity;

    @ApiModelProperty("入库方式")
    private Integer storeMode;

    @ApiModelProperty("入库类型：1-资产，2-耗材")
    private Integer storeType;

    @ApiModelProperty("入库处理人")
    private Long storeHandler;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
