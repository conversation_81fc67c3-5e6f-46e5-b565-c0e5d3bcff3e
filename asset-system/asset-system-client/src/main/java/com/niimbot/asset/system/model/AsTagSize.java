package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标签模板-尺寸
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsTagSize对象", description = "标签模板-尺寸")
@TableName(value = "as_tag_size", autoResultMap = true)
public class AsTagSize implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("打印模板ID")
    private Long tplId;

    @ApiModelProperty(value = "尺寸 长")
    private String sizeLong;

    @ApiModelProperty(value = "宽")
    private String sizeWide;

    @ApiModelProperty(value = "类型 1- 默认类型  2-定制")
    private Integer type;

    @ApiModelProperty(value = "最大属性数量")
    private Integer maxAttrNum;

    @ApiModelProperty(value = "二维码位置类型（打印时需要用到）")
    private Integer qrcodePosition;

    @ApiModelProperty(value = "二维码占用行数")
    private Integer qrcodeRow;

    @ApiModelProperty(value = "适用于app")
    private Boolean applyApp;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "默认材质id")
    private Long defaultMaterialId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "是否删除 0 未删除  1已删除")
    @TableLogic
    private Boolean isDelete;

    /**
     * 获取json文件名称
     *
     * @return 打印json数据模板文件
     */
    // public String getJsonFileName() {
    //     String temp =
    //             this.sizeLong.replaceAll("mm", "") +
    //                     this.sizeWide.replaceAll("mm", "") +
    //                     this.type + this.maxAttrNum + this.qrcodePosition;
    //     // 加上例外--因为JSON文件名冲突了
    //     if (this.getId() == 42L) {
    //         temp = temp + "-1";
    //     }
    //     return temp + ".json";
    // }

    /**
     * 获取长度70mm*50mm
     *
     * @return 打印json数据模板文件
     */
    public String getLongWide() {
        return this.sizeLong.replaceAll("mm", "") + "*" +
                this.sizeWide.replaceAll("mm", "") + "mm";
    }
}
