package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.ImportErrorDeleteCmd;
import com.niimbot.asset.system.dto.ImportErrorListQry;
import com.niimbot.asset.system.dto.ImportErrorSaveCmd;
import com.niimbot.asset.system.dto.ImportErrorSaveOrUpdateCmd;
import com.niimbot.asset.system.dto.clientobject.ImportErrorCO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 导入错误数据
 * 原始接口 {@link com.niimbot.asset.means.service.AsAssetImportErrorService}
 *
 * <AUTHOR>
 * @date 2022/5/6 13:50
 */
public interface ImportErrorAbs {
    /**
     * 导入错误记录列表
     *
     *  List<AsAssetImportError> list = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
     *                 .lambda()
     *                 .eq(AsAssetImportError::getTaskId, taskId)
     *                 .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_ORG));
     *
     * @param qry
     * @return
     */
    @GetMapping("listImportError")
    List<ImportErrorCO> listImportError(@SpringQueryMap ImportErrorListQry qry);

    /**
     * 保存导入错误记录
     *
     * @param cmd
     */
    @PostMapping("saveImportError")
    void saveImportError(ImportErrorSaveCmd cmd);

    /**
     * 保存或更新导入错误记录
     *
     * @param cmd
     */
    @PostMapping("saveOrUpdateImportError")
    void saveOrUpdateImportError(ImportErrorSaveOrUpdateCmd cmd);

    /**
     * 删除导入错误记录
     *
     * @param cmd
     */
    @PostMapping("deleteImportError")
    Boolean deleteImportError(ImportErrorDeleteCmd cmd);

}
