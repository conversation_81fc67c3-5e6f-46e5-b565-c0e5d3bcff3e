package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 员工异动记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsEmployeeChange对象", description = "员工异动记录")
@TableName(value = "as_employee_change")
public class AsEmployeeChange implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "异动Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "异动类型（1-编辑员工，2-删除员工）")
    private Integer type;

    @ApiModelProperty(value = "公司Id")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "员工Id")
    private Long empId;

    @ApiModelProperty(value = "异动说明")
    private String orgExplain;

    @ApiModelProperty(value = "资产异动部门")
    private String assetExplain;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
