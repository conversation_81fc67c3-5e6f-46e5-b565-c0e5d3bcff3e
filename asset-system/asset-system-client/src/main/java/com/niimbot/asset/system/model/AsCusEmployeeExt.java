package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户角色扩展
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsCusEmployeeExt对象", description = "员工信息扩展表")
@TableName(value = "as_cus_employee_ext")
public class AsCusEmployeeExt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("员工状态激活状态: 1-待激活；2-预激活；3-已激活")
    private Integer accountStatus;

    @ApiModelProperty("员工类型：1-系统员工，2-钉钉自建员工")
    private Integer type;

    @ApiModelProperty(value = "所属代理商ID")
    private Integer agentId;

    @ApiModelProperty(value = "代理商平台注册的客户id")
    private Integer customerId;

    @ApiModelProperty(value = "默认打印标签ID")
    private Long defaultTagId;

    @ApiModelProperty(value = "耗材默认打印标签模板id")
    private Long defaultCftagId;

    @ApiModelProperty(value = "耗材默认打印标签模板 二维码内容 1-耗材唯一ID 2-耗材条码号")
    private Integer defaultCftagCode;

    @ApiModelProperty(value = "默认资产模版编号")
    private String defaultTplId;

    @ApiModelProperty(value = "默认材质id")
    private Long defaultMaterialId;

    @ApiModelProperty(value = "默认浓度")
    private Integer defaultConcentration;

    @ApiModelProperty(value = "默认浓度")
    private Long defaultPrinterSeries;

    @ApiModelProperty("默认打印机型")
    private Long defaultPrinterId;

    @ApiModelProperty(value = "pc 刷新时间(用户状态发生变化,同步操作APP)")
    private LocalDateTime pcRefreshAt;

    @ApiModelProperty(value = "APP 刷新时间(用户状态发生变化,同步操作PC)")
    private LocalDateTime appRefreshAt;

    @ApiModelProperty(value = "上一次登陆时间")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty(value = "当前登陆时间")
    private LocalDateTime currentLoginTime;

    @ApiModelProperty(value = "超管被三方组织删除")
    private Boolean thirdPartyRemove;

    @ApiModelProperty(value = "APP 极光推送ID")
    private String jpushId;

}
