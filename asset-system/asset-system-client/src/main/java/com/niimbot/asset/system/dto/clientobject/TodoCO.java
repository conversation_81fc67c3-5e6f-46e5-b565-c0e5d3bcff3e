package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 待办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class TodoCO extends ClientObject {

    /**
     * Id
     */
    private Long id;

    /**
     * 公司ID（租户ID）
     */
    private Long companyId;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 业务类型(对应单据类型)：1：领用，2：退还，3：借用，4：归还，5：调拨，6：报修，
     * 7：维修，8：处置，9：变更, 10: 采购, 11: 保养, 33:耗材领用, 96: 盘点审核,
     * 97: 保养计划, 98: 上报, 99: 盘点
     */
    private Short orderType;

    /**
     * 标题
     */
    private String title;

    /**
     * 单据数据
     */
    private String orderData;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 是否已处理
     */
    private Boolean isHandle;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
