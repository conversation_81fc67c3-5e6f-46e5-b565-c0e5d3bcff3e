package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.niimbot.luckysheet.LuckySheetModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6 14:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class ImportErrorCO extends ClientObject {
    /**
     * 导入Id
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 业务类型，1-资产导入，2-耗材导入
     */
    private Integer importType;

    /**
     * excel数据
     */
    private List<LuckySheetModel> sheetModelList;

    /**
     * 错误数量
     */
    private Integer errorNum;
}
