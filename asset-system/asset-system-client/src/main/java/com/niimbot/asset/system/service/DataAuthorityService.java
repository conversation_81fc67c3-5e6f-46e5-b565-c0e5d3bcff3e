package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsDataAuthority;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/10/21 16:15
 */
public interface DataAuthorityService extends IService<AsDataAuthority> {
    /**
     * 根据用户查询权限列表
     *
     * @param userId 用户di
     * @return 数据权限列表
     */
    List<AsDataAuthority> getListByUserId(Long userId);

    List<AsDataAuthority> listCompanyPerms(Long companyId, Wrapper<AsDataAuthority> queryWrapper);

    /**
     * 删除用户权限
     *
     * @param userIds
     * @return
     */
    default void removeByUserIds(Collection<Long> userIds) {
        remove(Wrappers.<AsDataAuthority>lambdaQuery().in(AsDataAuthority::getUserId, userIds));
    }

    /**
     * 查询具体权限
     *
     * @param userId
     * @param dataCode
     * @param code
     * @return
     */
    default AsDataAuthority getByUserAndDataCodeAndCode(Long userId, String dataCode, String code) {
        return getOne(Wrappers.<AsDataAuthority>lambdaQuery()
                .eq(AsDataAuthority::getUserId, userId)
                .eq(AsDataAuthority::getAuthorityDataCode, dataCode)
                .eq(AsDataAuthority::getAuthorityCode, code));
    }

}
