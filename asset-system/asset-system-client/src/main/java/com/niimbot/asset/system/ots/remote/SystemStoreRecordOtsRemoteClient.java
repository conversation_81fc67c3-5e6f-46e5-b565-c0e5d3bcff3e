package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemStoreRecordOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemStoreRecordServiceImpl")
@FeignClient(name = "asset-system", url = "https://{gateway}/system/SystemStoreRecordOts/")
public interface SystemStoreRecordOtsRemoteClient extends SystemStoreRecordOts {
}
