package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsUserSensitiveAuthority;
import com.niimbot.system.SensitivePermissionDto;
import com.niimbot.system.UserSensitivePermissionDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/21 下午4:30
 */
public interface UserSensitiveAuthorityService extends IService<AsUserSensitiveAuthority> {

    /**
     * 根据用户id获取敏感数据对象权限
     * @param userId
     * @return
     */
    List<SensitivePermissionDto> getByUserId(Long userId);

    /**
     * 获取用户敏感数据权限
     * @param userId
     * @return
     */
    List<UserSensitivePermissionDto> getSensitivePerm(Long userId);

    /**
     * 批量删除用户敏感数据权限
     * @param userIds
     */
    void removeByUserIds(List<Long> userIds, Long companyId);

    /**
     * 保存用户敏感数据权限
     * @param userIds
     * @param sensitivePermissionDto
     * @return
     */
    Boolean savePermission(List<Long> userIds, List<SensitivePermissionDto> sensitivePermissionDto);
}
