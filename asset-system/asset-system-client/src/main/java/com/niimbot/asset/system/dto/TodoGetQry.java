package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/10 13:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class TodoGetQry extends CommonCommand {
    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 是否已处理
     */
    private Boolean isHandle;
}
