package com.niimbot.asset.system.event;

import com.niimbot.asset.system.annotations.EventDesc;


/**
 * 事件id和事件名称定义
 * <AUTHOR>
 * @date 2023/4/11 上午11:15
 */
public class EventType {

    @EventDesc(value = "首页活动广播")
    public static final String ACTIVE_BROADCAST = "ACTIVE_BROADCAST";

    public static class EventAction {

        /**
         * 查看动作
         */
        public static final String VIEW = "VIEW";

        /**
         * 点击动作
         */
        public static final String CLICK = "CLICK";
    }
}
