package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ThirdPartyInnerMessage extends CommonCommand {

    private Long companyId;

    private String type;

    private String title;

    private String text;

    private Map<String, String> params;

    private Set<Long> empIds;

    private String code;

}
