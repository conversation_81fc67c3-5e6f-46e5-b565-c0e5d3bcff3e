package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.system.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/10/30
 */
public interface CusUserService extends IService<AsCusUser> {
    /**
     * 获取最大的账号
     *
     * @return 最大账号信息
     */
    String getMaxAccount();

    /**
     * 根据注册中心uid查询用户
     *
     * @param unionId 注册中心uid
     * @return 用户信息
     */
    CusUserDto selectUserByUnionId(String unionId);

    /**
     * 更新员工信息
     *
     * @param asCusUser asCusUser
     * @return 结果
     */
    boolean update(AsCusUser asCusUser);

    /**
     * 更新员工信息
     *
     * @param asCusUser asCusUser
     * @return 结果
     */
    boolean updateByAccount(AsCusUser asCusUser);

    /**
     * 根据条件
     *
     * @param condition condition
     * @return 账号
     */
    AsCusUser getCusUserByCondition(AsCusUser condition);

    /**
     * 查询角色Id下是否存在用户
     *
     * @param roleId 角色Id
     * @return 结果
     */
    Boolean hasUserByRoleId(Long roleId);

    /**
     * 获取用户中心数据
     *
     * @return 用户中心数据
     */
    UserCenterAPPDto getUserCenterAppInfo();

    /**
     * 用户详情信息
     *
     * @return 用户登录详情
     */
    CusUserDetailDto personDetail();

    /**
     * 获取账号手机号
     *
     * @return 用户id
     */
    String getAccountMobileByUserId(Long userId);

    /**
     * 修改手机号
     *
     * @param mobile 手机号
     * @return 结果
     */
    Boolean modifyMobile(String mobile);

    /**
     * 通过企业id查询子账号数量
     *
     * @param companyId 企业id
     * @return
     */
    int getSonAccountNumByCompanyId(Long companyId);

    /**
     * 通过企业id查询子账号列表
     *
     * @param companyId 企业id
     * @return
     */
    List<SonUserDto> getSonListByCompanyId(Long companyId);

    /**
     * 企业注册V2
     *
     * @param dto dto
     * @return 账号
     * @since 2021/01/04
     */
    AsCusUser registerV2(RegisterDto dto);

    /**
     * 企业邀请注册
     */
    void recommendRegister(RecommendRegisterDto dto);

    /**
     * 注销账号
     *
     * @since 2022/03/10
     */
    boolean cancalUser();


    /**
     * 获取PC用户中心数据
     *
     * @return 用户中心数据
     */
    UserCenterPCDto getUserCenterPcInfo();

    /**
     * 登录后置处理
     *
     * @param userId 用户Id
     */
    void loginAfterRecord(Long userId);


    /**
     * PC端用户中心-账号信息
     *
     * @return AccountInfoDto
     */
    AccountInfoDto currentAccountInfo();

}
