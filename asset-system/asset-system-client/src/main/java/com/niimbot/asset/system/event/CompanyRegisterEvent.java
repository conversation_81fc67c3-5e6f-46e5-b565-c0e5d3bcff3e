package com.niimbot.asset.system.event;

import com.niimbot.asset.framework.support.SystemEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Getter
@Accessors(chain = true)
public class CompanyRegisterEvent extends SystemEvent {

    @Setter
    private String xsCode;

    @Setter
    private String registerMobile;

    public CompanyRegisterEvent(Object source) {
        super(source);
    }
}
