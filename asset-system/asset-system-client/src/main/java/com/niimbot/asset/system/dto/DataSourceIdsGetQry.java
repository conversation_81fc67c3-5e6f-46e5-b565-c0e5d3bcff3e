package com.niimbot.asset.system.dto;

import com.alibaba.cola.dto.Query;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceIdsGetQry extends Query {

    private String kw;

    private Long companyId;

    private Integer sourceType;

    private Integer dataType;

}
