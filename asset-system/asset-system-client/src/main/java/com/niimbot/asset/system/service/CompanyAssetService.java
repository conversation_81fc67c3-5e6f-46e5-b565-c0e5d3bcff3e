package com.niimbot.asset.system.service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CompanyAssetService {

    List<Long> checkUseOrg(List<Long> orgIds, Long companyId);

    List<Long> checkOrgOwner(List<Long> orgIds, Long companyId);

    List<Long> checkUsePerson(List<Long> userIds, Long companyId);

    List<Long> checkManagerOwner(List<Long> userIds, Long companyId);

    void updateAreaOrgId(Long oldId, Long newId);

    void updateRepositoryOrgId(Long oldId, Long newId);

    Set<Long> checkOrgAsset(List<Long> removeOrgIds, Long companyId);

}
