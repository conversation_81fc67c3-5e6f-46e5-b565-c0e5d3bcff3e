package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.AdminPrinterConcentrationGetQry;
import com.niimbot.asset.system.dto.AdminPrinterConcentrationSaveOrUpdateCmd;
import com.niimbot.asset.system.dto.clientobject.AdminPrinterConcentrationCO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 原始接口 {@link com.niimbot.asset.means.service.AdminPrinterConcentrationService}
 * <AUTHOR>
 * @date 2022/5/11 17:57
 */
public interface AdminPrinterConcentrationAbs {
    /**
     * 获取打印设备
     * printerConcentrationService.getOne(
     *                     Wrappers.<AsAdminPrinterConcentration>lambdaQuery()
     *                             .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
     *                             .eq(AsAdminPrinterConcentration::getIsDefault, true)
     *                             .eq(AsAdminPrinterConcentration::getUserId, LoginUserThreadLocal.getCurrentUserId
     *                             ()));
     * @param qry
     * @return
     */
    @GetMapping("getAdminPrinterConcentration")
    AdminPrinterConcentrationCO getAdminPrinterConcentration(@SpringQueryMap AdminPrinterConcentrationGetQry qry);

    /**
     * 保存或更新
     *
     * @param cmd
     */
    @PostMapping("saveOrUpdateAdminPrinterConcentration")
    Boolean saveOrUpdateAdminPrinterConcentration(AdminPrinterConcentrationSaveOrUpdateCmd cmd);


}
