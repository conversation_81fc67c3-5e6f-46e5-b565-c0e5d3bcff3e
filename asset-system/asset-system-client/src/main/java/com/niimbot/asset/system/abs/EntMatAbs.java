package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.EntMatTransferCmd;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 */
public interface EntMatAbs {

    @GetMapping("entMatUserHasTask")
    Boolean entMatUserHasTask(Long userId);

    @PostMapping("entMatTransfer")
    Boolean entMatTransfer(EntMatTransferCmd cmd);

}
