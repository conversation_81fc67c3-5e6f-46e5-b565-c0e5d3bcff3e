package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsUserTag;
import com.niimbot.system.UserTagDto;
import com.niimbot.system.UserTagPrintDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/17 10:22
 */
public interface AsUserTagService extends IService<AsUserTag> {
    /**
     * 通过企业id查询模板数量
     *
     * @param companyId 企业id
     * @return 数量
     */
    int getCountByCompanyId(Long companyId);

    /**
     * 通过标签id查询标签数据
     *
     * @param tagId 标签id
     * @return 数量
     */
    AsUserTag getOneById(Long tagId, Short printType);

    /**
     * 通过标签id查询标签详情
     *
     * @param tagId 标签id
     * @return 标签详情
     */
    UserTagPrintDto getDetail(Long tagId);

    /**
     * 获取最早一条有效的标签
     *
     * @return 标签信息
     */
    Long getFirstTagId(Short printType, Long printerId);

//    /**
//     * 根据尺寸id查询标签信息
//     *
//     * @param type 标签类型
//     * @param size 尺寸类型
//     * @return 标签信息
//     */
//    List<UserTagDto> queryTagBySizeAndType(@Param("type") Integer type,
//                                           @Param("size") Integer size,
//                                           @Param("companyId") Long companyId);

    /**
     * 根据尺寸id查询标签信息
     *
     * @param sizeId 尺寸
     * @return 标签信息
     */
    List<UserTagDto> queryTagBySizeId(Long companyId,
                                      Long sizeId,
                                      Integer tagType,
                                      String kw,
                                      Short printType,
                                      Long printerId);

    List<AsUserTag> companyTagList(Long companyId, Short printType, Long sizeId);
}

