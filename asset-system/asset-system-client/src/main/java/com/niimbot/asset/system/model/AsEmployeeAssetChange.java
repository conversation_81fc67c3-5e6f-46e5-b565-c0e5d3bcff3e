package com.niimbot.asset.system.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 员工资产异动记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsEmployeeAssetChange对象", description = "员工资产异动记录")
@TableName(value = "as_employee_asset_change")
public class AsEmployeeAssetChange implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资产异动Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "组织异动类型 1-使用资产，2-管理资产")
    private Integer changeOrgType;

    @ApiModelProperty(value = "异动Id")
    private Long changeId;

    @ApiModelProperty(value = "资产Id")
    private Long assetId;

    @ApiModelProperty(value = "资产属性快照数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject assetSnapshotData;

    @ApiModelProperty(value = "资产名称")
    private String assetName;

    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    @ApiModelProperty(value = "使用组织")
    private String useOrg;

    @ApiModelProperty(value = "使用人")
    private String usePerson;

    @ApiModelProperty(value = "所属管理组织")
    private String orgOwner;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
