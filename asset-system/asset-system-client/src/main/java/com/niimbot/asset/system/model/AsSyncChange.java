package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.system.handle.LongListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsSyncChange对象", description = "组织员工异动记录")
@TableName(value = "as_sync_change", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class AsSyncChange implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织员工异动ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "公司ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "资源ID（员工，组织）")
    private Long resId;

    @ApiModelProperty(value = "异动类型（1-编辑员工，2-删除员工，3-组织异动）")
    private Integer type;

    @ApiModelProperty(value = "原始部门")
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> fromOrg = new ArrayList<>(4);

    @ApiModelProperty(value = "新部门")
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> toOrg = new ArrayList<>(4);

    @ApiModelProperty(value = "状态（1-待处理，2-已处理）")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public AsSyncChange(Long id, Long companyId, Long resId, Integer type, List<Long> fromOrg, Integer status) {
        this.id = id;
        this.companyId = companyId;
        this.resId = resId;
        this.type = type;
        this.fromOrg = new ArrayList<>(fromOrg);
        this.status = status;
    }
}
