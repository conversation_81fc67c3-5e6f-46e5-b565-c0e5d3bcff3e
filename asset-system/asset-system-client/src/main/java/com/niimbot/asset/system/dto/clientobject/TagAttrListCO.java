package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 属性表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TagAttrListCO对象")
public class TagAttrListCO extends ClientObject {
    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "打印属性")
    private List<TagAttrCO> tagAttrs;
}
