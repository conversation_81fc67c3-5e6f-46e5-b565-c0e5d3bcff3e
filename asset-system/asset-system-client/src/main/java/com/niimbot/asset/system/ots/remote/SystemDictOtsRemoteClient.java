package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemDictOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemDictOtsProcessClient")
@FeignClient(name = "asset-system", url = "{gateway}/system/SystemDictOts/")
public interface SystemDictOtsRemoteClient extends SystemDictOts {
}
