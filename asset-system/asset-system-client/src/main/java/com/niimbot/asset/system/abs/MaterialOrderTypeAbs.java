package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.MaterialOrderTypeInitCmd;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 耗材单据类型
 * 原始接口 {@link com.niimbot.asset.material.service.AsMaterialOrderTypeService}
 * <AUTHOR>
 * @date 2022/5/11 10:25
 */
public interface MaterialOrderTypeAbs {
    /**
     * 初始化
     * materialOrderTypeService.initCompanyOrderType(companyId);
     * @param cmd
     */
    @PostMapping("initCompanyOrderType")
    void initCompanyOrderType(MaterialOrderTypeInitCmd cmd);
}
