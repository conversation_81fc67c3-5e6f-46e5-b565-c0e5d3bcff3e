package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsSelectorRecord;
import com.niimbot.system.AddSelectorRecord;
import com.niimbot.system.GetSelectorRecord;
import com.niimbot.system.SelectorRecord;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AsSelectorRecordService extends IService<AsSelectorRecord> {

    /**
     * 添加选择器记录
     *
     * @param params 参数
     * @return true if success
     */
    Boolean addSelectorRecord(AddSelectorRecord params);

    /**
     * 获取选择器记录
     *
     * @param params 参数
     * @return selector records
     */
    List<SelectorRecord> getSelectorRecords(GetSelectorRecord params);

    /**
     * 清除整理非必要的记录
     */
    void clearSelectorRecords();
}
