package com.niimbot.asset.system.service;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.AuditLogSearch;

/**
 * <AUTHOR>
 */
public interface AsAuditLogService {

    /**
     * 审计日志分页搜索
     *
     * @param search body
     * @return list
     */
    PageUtils<AuditLogDto> search(AuditLogSearch search);

    /**
     * 记录审计日志
     *
     * @param record body
     */
    void record(AuditLogRecord record);

    /**
     * 加载审计日志配置
     */
    void reloadConfig();

}
