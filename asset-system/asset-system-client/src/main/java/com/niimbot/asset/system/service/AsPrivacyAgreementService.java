package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsPrivacyAgreement;

/**
 * <p>
 * 隐私协议管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface AsPrivacyAgreementService extends IService<AsPrivacyAgreement> {

    /**
     * 查询平台最新隐私协议
     *
     * @param platform
     * @return
     */
    AsPrivacyAgreement queryLast(Integer platform);

}
