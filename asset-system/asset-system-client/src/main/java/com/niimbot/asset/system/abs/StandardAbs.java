package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.StandardExtFieldListQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 原始接口 {@link com.niimbot.asset.means.service.StandardService}
 * <AUTHOR>
 * @date 2022/5/11 17:53
 */
public interface StandardAbs {

    /**
     * 查询自定义属性 standardService.getExtensionInfo(userTagSaveDto.getStandardId());
     *
     * @param qry
     * @return
     */
    @GetMapping("getStandardExtField")
    List<FormFieldCO> getStandardExtField(@SpringQueryMap StandardExtFieldListQry qry);
}
