package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.dto.clientobject.ImportErrorCO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/5/6 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class ImportErrorSaveCmd extends CommonCommand {
    /**
     * 错误记录
     */
    @NotNull
    private ImportErrorCO importErrorCO;
}
