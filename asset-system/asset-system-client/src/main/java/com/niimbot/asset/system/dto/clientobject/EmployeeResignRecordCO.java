package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class EmployeeResignRecordCO extends ClientObject {

    @ApiModelProperty("删除异动详情Id")
    private Long id;

    @ApiModelProperty("异动Id")
    private Long changeId;

    @ApiModelProperty("离职人员Id")
    private Long empId;

    @ApiModelProperty("离职人员名称")
    private String empName;

    @ApiModelProperty("接收人")
    private Long forwardUserId;

    @ApiModelProperty("接收组织")
    private Long forwardOrgId;

    @ApiModelProperty("资源类型")
    private String resType;

}
