package com.niimbot.asset.system.service;

/**
 * 验证码
 *
 * <AUTHOR>
 * @date 2021/1/15 15:59
 */
public interface SmsCodeService {
    /**
     * 发送验证码
     *
     * @param iddCode 国际字冠
     * @param mobile  手机号
     */
    void sendSmsCode(String iddCode, String mobile);

    /**
     * 发送邮箱验证码、手机号验证码
     *
     * @param type email/mobile
     * @param addr 邮箱/手机号
     * @return true
     */
    void sendCommonCode(String type, String addr);

    /**
     * 校验验证码
     *
     * @param mobile  手机号
     * @param smsCode 验证码
     * @return 验证码是否合法
     */
    Boolean checkSmsCode(String mobile, String smsCode);

}
