package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenSerialNoCmd extends ClientObject {

    private Long companyId;

    private String type;

    private Integer serialLen;

    private String delimiter;

    private String fixedChar;

    private List<Rule> rules;

    private String fieldCode;

    private List<JSONObject> data;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rule {

        private String code;

        private String desc;

        private Long id;
    }

}
