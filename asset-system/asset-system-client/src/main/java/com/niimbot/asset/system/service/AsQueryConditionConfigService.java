package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsQueryConditionConfig;
import com.niimbot.system.QueryConditionConfigDto;

/**
 * <AUTHOR>
 * @date 2022/9/5 11:24
 */
public interface AsQueryConditionConfigService extends IService<AsQueryConditionConfig> {

    QueryConditionConfigDto getByType(String type);

    QueryConditionConfigDto getByType(String type, String terminal);
}
