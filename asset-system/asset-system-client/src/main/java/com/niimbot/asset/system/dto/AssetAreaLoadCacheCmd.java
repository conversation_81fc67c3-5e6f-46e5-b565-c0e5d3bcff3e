package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.dto.clientobject.AssetAreaCO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 11:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetAreaLoadCacheCmd extends CommonCommand {
    private List<AssetAreaCO> areas;
}
