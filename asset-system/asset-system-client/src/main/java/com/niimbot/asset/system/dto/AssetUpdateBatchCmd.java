package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.dto.clientobject.AssetCO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AssetUpdateBatchCmd extends CommonCommand {

    private List<AssetCO> assetCOS = Collections.emptyList();

}
