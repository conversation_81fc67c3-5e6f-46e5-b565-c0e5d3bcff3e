package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.AssetCategoryLoadCacheCmd;
import com.niimbot.asset.system.dto.AssetCategoryRegisterCopyCmd;
import com.niimbot.asset.system.dto.AssetCategorySaveBatchCmd;
import com.niimbot.asset.system.dto.CommonIndustryAssetCategoryListQry;
import com.niimbot.asset.system.dto.clientobject.AssetCategoryCO;
import com.niimbot.framework.dataperm.object.Tuple2;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;

/**
 * 资产分类
 * 原始接口 {@link com.niimbot.asset.means.service.CategoryService}
 *
 * <AUTHOR>
 * @date 2022/5/10 18:12
 */
public interface AssetCategoryAbs {

    @GetMapping("list/permission")
    List<AssetCategoryCO> listPermission(Long companyId);

    /**
     * 行业资产分类列表
     * categoryService.list(new QueryWrapper<AsCategory>()
     * .lambda().eq(AsCategory::getIndustryId, commonIndustryId).eq(AsCategory::getCompanyId, 0));
     *
     * @param qry
     * @return
     */
    @GetMapping("listCommonIndustryAssetCategory")
    List<AssetCategoryCO> listCommonIndustryAssetCategory(@SpringQueryMap CommonIndustryAssetCategoryListQry qry);

    /**
     * 注册复制分类
     * categoryService.registerCopy(0L, categoryIds);
     * @param cmd
     * @return
     */
    @PostMapping("registerCopyAssetCategory")
    Tuple2<List<AssetCategoryCO>, Map<Long, Long>> registerCopyAssetCategory(AssetCategoryRegisterCopyCmd cmd);

    /**
     * 批量保存
     *
     * @param cmd
     * @return
     */
    @PostMapping("saveBatchAssetCategory")
    Boolean saveBatchAssetCategory(AssetCategorySaveBatchCmd cmd);

    /**
     * 加载到缓存
     * categoryService.loadCateCache(newCategoryList)
     * @param cmd
     * @return
     */
    @PostMapping("loadCacheAssetCategory")
    void loadCacheAssetCategory(AssetCategoryLoadCacheCmd cmd);

    @PostMapping("hasPermCateIds")
    List<Long> hasPermCateIds(List<Long> cateIds);
}
