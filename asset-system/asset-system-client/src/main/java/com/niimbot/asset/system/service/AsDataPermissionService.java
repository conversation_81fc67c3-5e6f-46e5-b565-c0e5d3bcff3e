package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsDataPermission;
import com.niimbot.system.DataPermissionAuthorizeDto;
import com.niimbot.system.RoleDataAuthorityConfigDto;

import java.util.List;

/**
 * <p>
 * 数据权限 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
public interface AsDataPermissionService extends IService<AsDataPermission> {
    /**
     * 查询用户授权信息
     *
     * @param empId
     * @return
     */
    List<AsDataPermission> queryUserDataPermission(Long empId);

    /**
     * 企业默认配置的用户授权信息
     *
     * @param companyId 企业ID
     * @return list
     */
    List<AsDataPermission> queryCompanyDefaultUserDataPermission(Long companyId);

    /**
     * 获取角色数据权限
     * @param roleId
     * @return
     */
    List<AsDataPermission> queryDataAuthByRole(Long roleId);

    /**
     * 角色配置数据权限
     * @param roleDataAuthorityConfigDto
     * @return
     */
    Boolean configRoleDataAuth(RoleDataAuthorityConfigDto roleDataAuthorityConfigDto);

    /**
     * 查询数据权限信息
     *
     * @return
     */
    List<AsDataPermission> queryDataPermission();

    /**
     * 用户授权
     *
     * @param authorizeDto
     * @return
     */
    Boolean authorize(DataPermissionAuthorizeDto authorizeDto);

    /**
     * 用户权限初始化
     *
     * @param companyId
     * @param userId
     * @param roleCode
     */
    void initDataPermission(Long companyId, Long userId, String roleCode);
}
