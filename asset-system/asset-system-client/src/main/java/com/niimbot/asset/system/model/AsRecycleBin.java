package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsRecycleBin", description = "回收站")
@TableName(value = "as_recycle_bin", autoResultMap = true)
public class AsRecycleBin {

    @ApiModelProperty("租户ID")
    private Long companyId;

    @ApiModelProperty("资源ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long resId;

    @ApiModelProperty("资源类型，1-资产，2-耗材")
    private Integer resType;

    @ApiModelProperty("回收原因")
    private String reason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("回收人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("回收时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
