package com.niimbot.asset.system.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_asset_advertise
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class AsAssetAdvertise implements Serializable {

    private static final long serialVersionUID = -1483178872172168797L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 位置1-个人中心 2-pc首页
     */
    private Integer type;

    /**
     * 排序序号
     */
    private Integer sortNum;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}