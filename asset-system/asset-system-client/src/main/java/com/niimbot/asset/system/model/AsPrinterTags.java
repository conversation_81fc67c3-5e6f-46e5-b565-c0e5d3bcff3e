package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_printer_tags", autoResultMap = true)
@ApiModel(value = "AsPrinterTags", description = "打印设备与打印标签关联表")
public class AsPrinterTags implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("打印设备ID")
    private Long printerId;

    @ApiModelProperty("打印标签ID")
    private Long tagId;

}
