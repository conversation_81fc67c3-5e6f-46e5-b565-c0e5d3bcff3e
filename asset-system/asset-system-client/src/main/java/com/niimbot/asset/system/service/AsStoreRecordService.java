package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.model.AsStoreRecord;
import com.niimbot.means.StoreRecordDto;
import com.niimbot.means.StoreRecordSearch;
import com.niimbot.system.StoreRecord;

/**
 * <AUTHOR>
 */
public interface AsStoreRecordService extends IService<AsStoreRecord> {

    PageUtils<StoreRecordDto> search(StoreRecordSearch search);

    void record(StoreRecord saved);

}
