package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.CompanySwitch;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class CompanySettingCO extends ClientObject {

    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    @ApiModelProperty(value = "PC端首页布局")
    private List<Map<String, Object>> expandPcHome;

    @ApiModelProperty(value = "是否测试账号 0-否  1-是")
    private Boolean isTest;

    @ApiModelProperty(value = "是否付费 0-否  1-是")
    private Boolean isPay;

    @ApiModelProperty(value = "自定义开关量")
    private CompanySwitch expandSwitch;

    @ApiModelProperty(value = "待办超时下限(小时)")
    private Integer todoTimeoutLowerLimit;

    @ApiModelProperty(value = "待办超时上限(小时)")
    private Integer todoTimeoutUpperLimit;

    @ApiModelProperty("默认数据权限")
    private List<AsDataAuthority> defaultDataAuthorities;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
