package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * as_company_tag
 * <AUTHOR>
@Data
public class AsCompanyTag {
    /**
     * 主键
     */
    private Long id;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}