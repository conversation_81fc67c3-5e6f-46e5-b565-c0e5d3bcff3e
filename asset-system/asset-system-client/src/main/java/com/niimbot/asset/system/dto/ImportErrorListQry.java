package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/6 17:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class ImportErrorListQry extends CommonCommand {
    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务类型
     */
    private Integer importType;
}
