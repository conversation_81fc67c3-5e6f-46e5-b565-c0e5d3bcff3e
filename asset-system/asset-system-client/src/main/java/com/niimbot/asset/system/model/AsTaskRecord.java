package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsTaskRecord", description = "导入任务记录表")
public class AsTaskRecord implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("企业ID")
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("任务类型：1-导入任务，2-打印任务，3-导出任务")
    private Integer type;

    @ApiModelProperty("业务类型")
    private Integer importType;

    @ApiModelProperty("本次任务成功数量")
    private Integer successNum;

    @ApiModelProperty("本次任务失败数量")
    private Integer failureNum;

    @ApiModelProperty("本次任务总数量")
    private Integer total;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
