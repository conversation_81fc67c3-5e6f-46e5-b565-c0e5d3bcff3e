package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsThirdPartyAccount;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface AsThirdPartyAccountService extends IService<AsThirdPartyAccount> {

    Optional<AsThirdPartyAccount> getThirdPartyAccount(String type, String thirdUniqueId);

    Boolean bindOne(Long accountId, String type, String openId, String thirdUniqueId, String nickname, String avatarUrl);

    void bindOneIfNotExist(Long accountId, String type, String openId, String thirdUniqueId, String nickname, String avatarUrl);

    List<AsThirdPartyAccount> getThirdPartyAccounts(Long accountId);
}
