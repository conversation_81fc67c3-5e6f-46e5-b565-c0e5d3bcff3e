package com.niimbot.asset.system.dto;

import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/7 10:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class InventoryAssetCountGetQry extends CommonCommand {
    /**
     * 盘点人
     */
    private Long inventoryUser;

    /**
     * 盘点单id
     */
    @NotNull
    private Long inventoryId;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务类型 1-盘点任务  2-全员盘点任务
     */
    private Integer taskType;

    /**
     * 默认false  true-盘点任务  false-盘点子任务（可不传）
     */
    private Boolean isMainTask = false;
}
