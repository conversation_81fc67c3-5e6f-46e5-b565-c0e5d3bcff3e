package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName(value = "as_account_employee", autoResultMap = true)
@ApiModel(value = "AsAccountEmployee对象", description = "账号员工关联表")
@NoArgsConstructor
public class AsAccountEmployee implements Serializable {

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("账号ID")
    private Long accountId;

    @ApiModelProperty("企业ID")
    private Long companyId;

    @ApiModelProperty("员工ID")
    private Long employeeId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public AsAccountEmployee(Long accountId, Long companyId, Long employeeId) {
        this.accountId = accountId;
        this.companyId = companyId;
        this.employeeId = employeeId;
    }
}
