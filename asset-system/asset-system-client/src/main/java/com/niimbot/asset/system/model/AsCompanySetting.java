package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.asset.system.handle.DataAuthListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公司自定义设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsCompanySetting对象", description = "公司自定义设置表")
@TableName(value = "as_company_setting", autoResultMap = true)
public class AsCompanySetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司ID")
    @TableId(value = "company_id")
    private Long companyId;

    @ApiModelProperty(value = "PC端首页布局")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<Map<String, Object>> expandPcHome;

    @ApiModelProperty(value = "是否测试账号 0-否  1-是")
    private Boolean isTest;

    @ApiModelProperty(value = "是否付费 0-否  1-是")
    private Boolean isPay;

    @ApiModelProperty(value = "自定义开关量")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private CompanySwitch expandSwitch;

    @ApiModelProperty(value = "待办超时下限(小时)")
    private Integer todoTimeoutLowerLimit;

    @ApiModelProperty(value = "待办超时上限(小时)")
    private Integer todoTimeoutUpperLimit;

    @ApiModelProperty("默认数据权限")
    @TableField(typeHandler = DataAuthListTypeHandler.class)
    private List<AsDataAuthority> defaultDataAuthorities = Collections.emptyList();

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
