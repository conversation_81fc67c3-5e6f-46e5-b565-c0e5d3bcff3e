package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.ThirdPartyGetQry;
import com.niimbot.asset.system.dto.ThirdPartyInnerMessage;
import com.niimbot.asset.system.dto.clientobject.ThirdPartyCO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 三方
 * 原始接口 {@link com.niimbot.asset.thirdparty.service.AsThirdPartyService}
 *
 * <AUTHOR>
 * @date 2022/5/11 10:06
 */
public interface ThirdPartyAbs {
    /**
     * 获取三方信息
     *
     * @param qry
     * @return
     */
    @GetMapping("getThirdParty")
    ThirdPartyCO getThirdParty(@SpringQueryMap ThirdPartyGetQry qry);

    @PostMapping("sendThirdPartyInnerMessage")
    String sendThirdPartyInnerMessage(@RequestBody ThirdPartyInnerMessage msg);
}
