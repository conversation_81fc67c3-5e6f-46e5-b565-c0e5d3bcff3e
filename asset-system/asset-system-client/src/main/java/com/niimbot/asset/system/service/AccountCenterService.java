package com.niimbot.asset.system.service;

import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.system.AccountBasicInfo;
import com.niimbot.system.AccountDto;
import com.niimbot.system.AccountEmployeeDto;
import com.niimbot.system.ActivateAccountResult;
import com.niimbot.system.InviteLink;
import com.niimbot.thirdparty.ThirdPartyUser;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AccountCenterService {

    /**
     * 获取账户对应的企业信息列表
     *
     * @param accountId 账号ID
     * @return 企业列表
     */
    List<AccountEmployeeDto> accountCompanyList(Long accountId);

    AsCusUser getAccountById(Long id);

    /**
     * 根据手机号获取账号信息
     *
     * @param accountNo 登录账号
     * @return account
     */
    AsCusUser getAccountByNo(String accountNo);

    /**
     * 根据手机号获取账号信息
     *
     * @param mobile 手机号
     * @return account
     */
    AsCusUser getAccountByMobile(String mobile);

    /**
     * 根据手机号获取账号信息
     *
     * @param email 邮箱账号
     * @return account
     */
    AsCusUser getAccountByEmail(String email);

    /**
     * 根据账号唯一标识获取账号
     *
     * @param way 唯一标识
     * @return account
     */
    AsCusUser getAccountByWay(String way);

    /**
     * 根据第三方类型与unionId获取系统账户信息
     *
     * @param type    第三方类型
     * @param unionId 第三方用户唯一标识
     * @return account
     */
    AsCusUser getAccountByThirdPartyUserUnionId(String type, String unionId);

    /**
     * 选择账号对应的员工
     *
     * @param account 账号信息
     * @return emp
     */
    AsCusEmployee chooseEmployee(AsCusUser account);

    /**
     * 使用手机号登录时，如果当前手机号没有对应的账号就去创建此账号
     *
     * @param mobile  手机号
     * @param smsCode 本地的短信验证码；当新建账号在中台注册时需要
     * @return cusUserDto
     */
    CusUserDto getLoginInfoByMobile(String mobile, String smsCode);

    /**
     * 使用手机号登录时选择员工
     *
     * @param no 账号
     * @return cusUserDto
     */
    CusUserDto getLoginInfoByNo(String no);

    /**
     * 使用手机号登录时选择员工
     *
     * @param email 邮箱
     * @return cusUserDto
     */
    CusUserDto getLoginInfoByEmail(String email);

    /**
     * 使用唯一标识登录选择员工
     *
     * @param way 账号、手机号、邮箱、精臣中台账户中心ID（unionId）
     * @return cusUserDto
     */
    CusUserDto getLoginInfoByWay(String way);

    CusUserDto getLoginInfoByAppKey(String appKey, String appSecret);

    /**
     * 通过第三方认证获取登录信息（社交登录）
     *
     * @param type     第三方类型
     * @param uniqueId 第三方用户唯一标识
     * @return cusUserDto
     */
    CusUserDto getLoginInfoByThirdParty(String type, String uniqueId);

    /**
     * 通过第三方认证获取登录信息（企业自建应用登录）
     *
     * @param type      类型
     * @param userId    userId
     * @return cusUserDto
     */
    CusUserDto getLoginInfoByThirdPartyInnerApp(String type, String userId);

    /**
     * 获取员工登录信息
     *
     * @param empId 员工ID
     * @return cusUserDto
     */
    CusUserDto getEmpLoginInfo(Long empId);

    /**
     * 获取企业下员工的登录信息
     *
     * @param accountId 账号ID
     * @param companyId 企业ID
     * @return cusUserDto
     */
    CusUserDto getCompanyEmpLoginInfo(Long accountId, Long companyId);

    /**
     * 删除员工账号关联
     *
     * @param empId 员工ID
     */
    Boolean deleteEmployeeAccount(Long empId);

    /**
     * 批量删除员工账号关联
     *
     * @param empIds 员工ID集合
     * @return true or false
     */
    Boolean batchDeleteEmployeeAccount(List<Long> empIds);

    /**
     * 删除企业账号关联
     *
     * @param companyId 企业ID
     * @return true or false
     */
    Boolean deleteCompanyAccount(Long companyId, Long accountId);

    /**
     * 通过手机号激活企业内员工账号
     *
     * @param companyId 企业ID
     * @param mobile    手机号
     * @param smsCode   短信验证码，新增账号时需要使用
     * @return true or false
     */
    ActivateAccountResult activateAccountByMobile(Long companyId, String mobile, String nationalCode, String smsCode);

    /**
     * 第三方扫码激活企业内员工账号
     *
     * @param companyId      企业ID
     * @param thirdType      第三方类型
     * @param thirdPartyUser 第三方用户信息
     */
    ActivateAccountResult activateAccountByThirdParty(Long companyId, String thirdType, ThirdPartyUser thirdPartyUser);

    /**
     * 第三方扫码激活账号时需要新建账号
     *
     * @param companyId 企业ID
     * @param mobile    需要新建的账号手机号信息
     * @param smsCode   短信验证码
     * @param uuid      上一步返回的用户UUID，用于从缓存中取出员工的empId与第三方UID
     * @return true of false
     */
    ActivateAccountResult activateAccountByThirdPartyBindMobile(Long companyId, String mobile, String nationalCode, String smsCode, String uuid);

    /**
     * 第三方扫码时 账号绑定员工 账号绑定第三方
     *
     * @param accountId     账号ID
     * @param thirdType     第三方类型
     * @param openId        第三方用户应用内唯一标识
     * @param thirdUniqueId 第三方用户全局唯一标识
     * @return cusUserDto
     */
    CusUserDto bindAccountForThirdParty(Long accountId, String thirdType, String openId, String thirdUniqueId, String nickname, String avatarUrl);

    /**
     * 第三方扫码时  通过手机号验证 账号不在时创建
     *
     * @param mobile        账号的手机号信息
     * @param smsCode       短信验证码
     * @param thirdType     第三方类型
     * @param thirdUniqueId 第三方用户全局唯一标识
     * @param nickname      昵称
     * @param avatarUrl     头像
     * @return cusUserDto
     */
    CusUserDto bindOrCreateAccountThirdPartyByMobile(String mobile, String smsCode, String thirdType, String openId, String thirdUniqueId, String nickname, String avatarUrl);

    /**
     * 获取账号对应的关联信息
     *
     * @param accountId 账号ID
     * @return accountEmployees
     */
    List<AsAccountEmployee> getAccountEmployees(Long accountId);

    /**
     * 获取账号对应的关联关系
     *
     * @param account 账号
     * @return accountEmployees
     */
    List<AsAccountEmployee> getAccountEmployees(AsCusUser account);

    /**
     * 生成邀请链接
     * 2.5.1.0 : 去掉受邀人ID以及校验逻辑
     *
     * @param companyId  企业ID
     * @param operatorId 邀请人
     * @return result
     */
    InviteLink sendInviteLink(Long companyId, Long operatorId);

    /**
     * 预激活员工账号
     *
     * @param companyId 企业ID
     * @param empId     员工ID
     * @return true or false
     */
    Boolean preActivatedAccount(Long companyId, Long empId);

    /**
     * 员工绑定账号
     *
     * @param companyId  企业ID
     * @param employeeId 员工ID
     * @param accountId  账号ID
     * @return true or false
     */
    Boolean bindEmployee(Long companyId, Long employeeId, Long accountId, Boolean isCheckExist);

    /**
     * 账号绑定手机号
     *
     * @param accountId 账号ID
     * @param mobile    手机号
     * @return true or false
     */
    Boolean bindMobile(Long accountId, String mobile);

    /**
     * 账号绑定邮箱
     *
     * @param accountId 账号ID
     * @param email     邮箱号
     * @return true or false
     */
    Boolean bindEmail(Long accountId, String email);

    /**
     * 账号绑定第三方账号
     *
     * @param thirdPartyUser 第三方用户信息
     * @return true or false
     */
    Boolean bindThirdParty(ThirdPartyUser thirdPartyUser);

    /**
     * 账号解除绑定
     *
     * @param way       绑定方式 1：手机 2：邮箱
     * @param accountId 账号ID
     * @return true or false
     */
    Boolean unbindWay(Integer way, Long accountId);

    /**
     * 账号解除与第三方平台的绑定
     *
     * @param thirdType 第三方平台
     * @param accountId 账号ID
     * @return true or false
     */
    Boolean unbindThirdParty(String thirdType, Long accountId);

    /**
     * 获取账号基本信息
     *
     * @param accountId 账号ID
     * @return AccountBasicInfoFFF
     */
    AccountBasicInfo getAccountBasicInfo(Long accountId, Long employeeId);

    /**
     * 单纯更新密码和昵称
     *
     * @param dto dto
     * @return true or false
     */
    Boolean updateAccountPwdAndNickname(AccountDto dto);

    /**
     * 更新账号最后登录的一次企业ID
     *
     * @param accountId 账号ID
     * @param companyId 企业ID
     */
    void updateLastCompany(Long accountId, Long companyId);

    /**
     * 更新账户昵称
     *
     * @return true or false
     */
    Boolean updateNickname(Long accountId, String nickname);

}
