package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsRoleMenu;

/**
 * 角色菜单关联关系
 *
 * <AUTHOR>
 * @Date 2020/11/23
 */
public interface AsRoleMenuService extends IService<AsRoleMenu> {

    /**
     * 初始化资产角色菜单
     *
     * @param assetAdminRoleId 角色ID
     */
    void initAssetRole(Long assetAdminRoleId);

    /**
     * 初始化普通角色菜单
     *
     * @param commonRoleId 角色ID
     */
    void initCommonRole(Long commonRoleId);
}
