package com.niimbot.asset.system.event;

import com.niimbot.asset.framework.support.SystemEvent;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class EmpInfoChangeEvent extends SystemEvent {

    private Boolean isAdmin;

    private Long companyId;

    private Long empId;

    private Long accountId;

    public EmpInfoChangeEvent setAdmin(Boolean admin) {
        isAdmin = admin;
        return this;
    }

    public EmpInfoChangeEvent setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public EmpInfoChangeEvent setEmpId(Long empId) {
        this.empId = empId;
        return this;
    }

    public EmpInfoChangeEvent setAccountId(Long accountId) {
        this.accountId = accountId;
        return this;
    }

    /**
     * event
     *
     * @param source 是否变更成功
     */
    public EmpInfoChangeEvent(Object source) {
        super(source);
    }
}
