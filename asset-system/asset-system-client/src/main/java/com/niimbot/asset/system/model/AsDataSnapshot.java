package com.niimbot.asset.system.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_data_snapshot", autoResultMap = true)
public class AsDataSnapshot {

    @ApiModelProperty("id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("企业ID")
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty("来源ID")
    private Long sourceId;

    @ApiModelProperty("来源类型")
    private Integer sourceType;

    @ApiModelProperty("快照数据ID")
    private String dataId;

    @ApiModelProperty("快照数据类型")
    private Integer dataType;

    @ApiModelProperty("快照数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject snapshot;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
