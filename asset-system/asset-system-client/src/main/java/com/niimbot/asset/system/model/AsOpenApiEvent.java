package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/18 10:31
 */
@Data
@TableName(value = "as_open_api_event", autoResultMap = true)
public class AsOpenApiEvent {

    // 主键ID
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    // 事件类型
    private String eventType;

    // 事件名称
    private String eventName;

    // 事件分组
    private String eventGroup;

}
