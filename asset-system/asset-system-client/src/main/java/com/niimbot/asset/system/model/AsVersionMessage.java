package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsVersionMessage对象", description = "版本消息")
@TableName(value = "as_version_message", autoResultMap = true)
public class AsVersionMessage {

    @ApiModelProperty("版本管理ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long versionManagementId;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("是否已读")
    private Boolean isRead;

}
