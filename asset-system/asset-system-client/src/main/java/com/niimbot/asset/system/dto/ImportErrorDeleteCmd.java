package com.niimbot.asset.system.dto;

import java.util.Collection;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/6 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class ImportErrorDeleteCmd extends CommonCommand {
    /**
     * id list
     */
    private Collection<Long> ids;
    /**
     * 任务id
     */
    private Long taskId;

    private List<Long> taskIds;
}
