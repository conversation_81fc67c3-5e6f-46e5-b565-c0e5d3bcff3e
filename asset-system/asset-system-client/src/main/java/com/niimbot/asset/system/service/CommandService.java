package com.niimbot.asset.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsAssetReports;

/**
 * 计划任务
 *
 * <AUTHOR>
 * @Date 2021/03/24
 */
public interface CommandService extends IService<AsAssetReports> {

    /**
     * 统计当天资产新增数（每天统计一次）
     *
     * @param date 日期
     * @return true / false
     */
    boolean statisticsAssetAddNumByDay(String date);

    /**
     * 当天用户统计报告（每天统计一次）
     *
     * @param date 日期
     * @return true / false
     */
    boolean statisticsUserNumByDay(String date);

}
