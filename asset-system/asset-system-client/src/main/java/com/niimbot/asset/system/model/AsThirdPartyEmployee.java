package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 相同type下userId可能相同
 *
 * userId是用户在第三方组织某个企业下的唯一标识，userId + type 无法确定唯一的一个员工，因为在第三方中两个不用的用户在不同的企业下可能userId相同。
 *
 * companyId + userId + type 某企业的某用户在某三方组织中唯一
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName(value = "as_third_party_employee", autoResultMap = true)
@ApiModel(value = "AsThirdPartyEmployee对象", description = "员工与第三方平台关联表")
public class AsThirdPartyEmployee implements Serializable {

    @ApiModelProperty("主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("企业ID")
    private Long companyId;

    @ApiModelProperty("员工ID")
    private Long employeeId;

    @ApiModelProperty("第三方类型")
    private String type;

    @ApiModelProperty("第三方用户企业下的唯一ID")
    private String userId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
