package com.niimbot.asset.system.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * as_module_statistics
 * <AUTHOR>
@Data
@TableName(value = "as_module_statistics", autoResultMap = true)
public class AsModuleStatistics implements Serializable {

    private static final long serialVersionUID = -4830469445516470684L;

    /**
     * 主键Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 事件id
     */
    private String eventId;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 事件名称
     */
    private String eventAction;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}