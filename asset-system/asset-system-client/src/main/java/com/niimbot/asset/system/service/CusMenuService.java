package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCusMenu;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.CusMenuDto;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020/10/27 14:31
 */
public interface CusMenuService extends IService<AsCusMenu> {
    /**
     * 配置角色菜单权所用列表
     *
     * @return 菜单列表
     */
    List<CusMenuDto> configRoleMenuPcList();

    /**
     * 配置角色菜单权所用列表
     *
     * @return 菜单列表
     */
    List<CusMenuDto> configRoleMenuAppList();

    /**
     * 根据用户权限查询菜单树显示列表
     *
     * @return 菜单列表
     */
    List<CusMenuDto> userMenuPcList();

    /**
     * 根据用户权限查询菜单树显示列表
     *
     * @return 菜单列表
     */
    AppCusMenuDto userMenuAppList();

    /**
     * 所有菜单
     *
     * @return
     */
    List<CusMenuDto> allAppMenu();

    /**
     * 所有菜单
     *
     * @return
     */
    List<CusMenuDto> allPcMenu();

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(Long userId);
}
