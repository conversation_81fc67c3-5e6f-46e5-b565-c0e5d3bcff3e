package com.niimbot.asset.system.support;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.system.ots.SystemDataSnapshotOts;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.system.DataSnapshotFactory;
import com.niimbot.system.DataSnapshotSave;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataSnapshots implements ApplicationContextAware {

    private static SystemDataSnapshotOts systemDataSnapshotService;

    private static ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private static AssetUtil assetUtil;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        systemDataSnapshotService = applicationContext.getBean(SystemDataSnapshotOts.class);
        threadPoolTaskExecutor = applicationContext.getBean(ThreadPoolTaskExecutor.class);
        assetUtil = applicationContext.getBean(AssetUtil.class);
    }

    public static void saveMeansData(Long sourceId, List<FormFieldCO> formFields, List<JSONObject> objects) {
        threadPoolTaskExecutor.execute(() -> {
            try {
                // 翻译字段集
                List<AssetUtil.FieldTranslation> translations =
                        formFields.stream().map(f -> new AssetUtil.FieldTranslation()
                                        .setFieldCode(f.getFieldCode())
                                        .setFieldType(f.getFieldType())
                                        .setTranslationCode(f.getTranslationCode()))
                                .collect(Collectors.toList());
                // 翻译数据
                objects.forEach(v -> assetUtil.translateAssetJson(v, translations));
                DataSnapshotSave forMeans = DataSnapshotFactory.createForMeans(sourceId, objects);
                systemDataSnapshotService.saveSnapshot(forMeans);
            } catch (Exception e) {
                log.warn("save means store record data snapshots error", e);
            }
        });
    }
}
