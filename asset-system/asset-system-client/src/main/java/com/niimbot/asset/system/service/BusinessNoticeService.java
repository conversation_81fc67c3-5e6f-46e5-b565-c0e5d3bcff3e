package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsBusinessNotice;
import com.niimbot.system.BusinessNoticeDto;

/**
 * <AUTHOR>
 * @date 2023/3/17 下午3:18
 */
public interface BusinessNoticeService extends IService<AsBusinessNotice> {

    /**
     * 保存或修改业务公告
     * @param noticeDto
     * @return
     */
    Boolean saveOrUpdate(BusinessNoticeDto noticeDto);

    /**
     * 查询业务公告详情
     * @param companyId
     * @return
     */
    BusinessNoticeDto queryDetail(Long companyId);

    /**
     * 删除业务公告
     * @param noticeId
     * @return
     */
    Boolean removeNotice(Long noticeId);
}
