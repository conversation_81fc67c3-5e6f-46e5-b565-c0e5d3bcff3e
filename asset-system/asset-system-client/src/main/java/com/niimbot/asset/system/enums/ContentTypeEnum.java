package com.niimbot.asset.system.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/14 下午5:06
 */
public enum ContentTypeEnum {

    TEXT(1, "自定义富文本"),
    LINK(2, "第三方链接"),

    UN_KNOW(-1, "未知"),
    ;

    ContentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ContentTypeEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UN_KNOW;
        }

        for (ContentTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
