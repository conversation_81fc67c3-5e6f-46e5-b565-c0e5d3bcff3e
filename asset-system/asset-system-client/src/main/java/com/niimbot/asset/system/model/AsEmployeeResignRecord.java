package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsEmployeeResignRecords对象", description = "离职员工记录")
public class AsEmployeeResignRecord {

    @ApiModelProperty("删除异动详情Id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("异动Id")
    private Long changeId;

    @ApiModelProperty("离职人员Id")
    private Long empId;

    @ApiModelProperty("离职人员名称")
    private String empName;

    @ApiModelProperty("接收人")
    private Long forwardUserId;

    @ApiModelProperty("接收组织")
    private Long forwardOrgId;

    @ApiModelProperty("资源类型")
    private String resType;
}
