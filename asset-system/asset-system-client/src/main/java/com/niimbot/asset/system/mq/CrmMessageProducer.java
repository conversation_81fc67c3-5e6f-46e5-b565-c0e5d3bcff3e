package com.niimbot.asset.system.mq;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import com.niimbot.asset.framework.annotation.MessageProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.AbstractRocketMqProducer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqPropertiesConfig;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.system.crm.CrmPushMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Crm消息生产者
 *
 * <AUTHOR>
 * @date 2021/9/13 14:29
 */
@Slf4j
@Profile({Edition.SAAS, Edition.DING, Edition.WEIXIN})
@MessageProducer(topic = MqConstant.ASSET_TOPIC, group = MqConstant.ASSET_CRM_PRODUCER_GROUP)
public class CrmMessageProducer extends AbstractRocketMqProducer {

    @Resource
    private Environment environment;

    @Resource
    private CompanyService companyService;

    public CrmMessageProducer(RocketMqPropertiesConfig propertiesConfig) {
        super(propertiesConfig);
    }

    public void sendCrmPushMessage(Long companyId, String xsCode, String registerMobile, String event) {
        List<String> active = Stream.of(environment.getActiveProfiles()).filter(v -> "prod".equals(v) || "dingtalkprod".equals(v) || "weixinprod".equals(v)).collect(Collectors.toList());
        if (active.isEmpty()) {
            log.warn("sendCrmPushMessage fail active is [{}]", JSONObject.toJSONString(active));
            return;
        }
        // 已注销的企业不发送消息
        AsCompany company = companyService.getById(companyId);
        if (Objects.isNull(company) || company.getStatus() == 5) {
            log.info("企业不存在[{}]", companyId);
            return;
        }
        String edition = Edition.getValue();
        // saas_push_123
        String key = edition + "_" + MqConstant.ASSET_CRM_PUSH_TAG + "_" + companyId;
        CrmPushMessage dto = new CrmPushMessage()
                .setEvent(event)
                .setCompanyId(companyId)
                .setXsCode(xsCode)
                .setRegisterMobile(registerMobile);
        SendResult sendResult = sendMsg
                (
                        // saas_push | ding_push | weixin_push
                        edition + "_" + MqConstant.ASSET_CRM_PUSH_TAG,
                        key, dto, 3000L
                );
        if (sendResult != null) {
            log.info("推送Crm消息发送成功 -> topic = [{}], msgId = [{}]", sendResult.getTopic(), sendResult.getMessageId());
        }
    }
}
