package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsCommodityInfo", description = "SKU信息映射表")
public class AsCommodityInfo implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("中台商品名称")
    private String middlendProductName;

    @ApiModelProperty("中台商品SKU")
    private String middlendSku;

    @ApiModelProperty("产品类型 - 1: PHP软件 2: Java软件 3: 硬件 4: 耗材")
    private Integer productType;

    @ApiModelProperty("固资SKU")
    private String assetSku;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
