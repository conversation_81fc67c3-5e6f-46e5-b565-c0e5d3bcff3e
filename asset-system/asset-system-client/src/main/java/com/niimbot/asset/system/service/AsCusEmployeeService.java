package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeOptDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.CusEmployeeTransferDto;
import com.niimbot.system.EmployeeImportDto;
import com.niimbot.system.EmployeeModifyDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.RemoveEmployDto;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 员工管理
 *
 * <AUTHOR>
 * @Date 2020/11/12
 */
public interface AsCusEmployeeService extends IService<AsCusEmployee> {

    /**
     * 写入员工缓存
     *
     * @param empList 员工列表
     */
    void loadEmpCache(List<AsCusEmployee> empList);

    /**
     * 默认员工工号
     */
    String DEFAULT_EMP_NO = "00001";

    /**
     * 获取公司对应的最大员工号
     *
     * @return max empNo
     */
    String recommendEmpNo();

    /**
     * 获取公司对应的最大员工号
     *
     * @return max empNo
     */
    String recommendEmpNo(Long companyId);

    /**
     * 检查手机号是否存在
     *
     * @param mobile 手机号
     * @return 是否存在
     */
    AsCusEmployee checkPhone(String mobile, Long companyId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    AsCusEmployee checkEmail(@RequestParam String email, Long companyId);

    /**
     * 编辑员工
     *
     * @param employee 员工
     * @return 是否成功
     */
    Boolean edit(CusEmployeeOptDto employee);

    /**
     * 批量变更员工组织
     *
     * @param employees 员工集合
     * @return true or false
     */
    Boolean batchModifyOrg(List<CusEmployeeOptDto> employees);

    /**
     * 处理异动记录
     *
     * @param fromOrgList 变更前组织
     * @param toOrgList   变更后组织
     * @param transfers   转移部门
     * @param userId      用户Id
     */
    void changeOrgLog(List<AsOrg> fromOrgList, List<AsOrg> toOrgList, List<CusEmployeeTransferDto> transfers, Long userId, Long companyId);

    /**
     * 编辑员工
     *
     * @param employeeDto 员工
     * @return 是否成功
     */
    Boolean insert(CusEmployeeOptDto employeeDto);


    String insertV2(CusEmployeeOptDto employeeDto);
    /**
     * 编辑员工
     *
     * @param employee 员工
     * @return 是否成功
     */
    Boolean insert(AsCusEmployee employee, Long companyId);

    /**
     * 联表分页查询
     *
     * @param page     分页参数
     * @param queryDto 查询条件
     * @return 分页结果
     */
    IPage<CusEmployeeDto> selectCustomPage(Page<CusEmployeeDto> page, CusEmployeeQueryDto queryDto);

    /**
     * 联表分页查询
     *
     * @param queryDto 数据查询
     * @return 结果
     */
    List<CusEmployeeDto> listCusEmployee(CusEmployeeQueryDto queryDto);

    List<CusEmployeeDto> listHasAccountEmployee(CusEmployeeQueryDto dto);

//    List<Long> getOrgIds(CusEmployeeQueryDto queryDto, boolean isAct);

    /**
     * 发送验证码校验手机号
     *
     * @param mobile
     */
    void checkRegisterMobile(String mobile);

    /**
     * 发送验证码校验邮箱
     *
     * @param email
     */
    void checkRegisterEmail(String email);

    /**
     * 获取多级主管列表
     *
     * @param userId     当前用户id
     * @param level      层级
     * @param handleType 层级
     * @return
     */
    List<List<AsCusEmployee>> getMultiManager(Long orgId, Integer level, Integer handleType);

    /**
     * 寻找流程主管
     *
     * @param userId
     * @param level
     * @param handleType
     * @param isAgent
     * @return
     */
    List<AsCusEmployee> getWorkflowManager(Long orgId, Integer level, Integer handleType, Boolean isAgent);

    /**
     * 获取管理员账号
     *
     * @return
     */
    AsCusEmployee getAdministrator();

    /**
     * 获取资产管理员ids
     *
     * @return
     */
    List<Long> getAssetAdminIds(Long companyId);

    /**
     * 查询企业管理员
     *
     * @param companyId
     * @return
     */
    AsCusEmployee getAdministratorByCompanyId(Long companyId);

    /**
     * 填充员工所在的公司信息
     *
     * @param list users
     */
    void fillUserCompany(List<CusEmployeeDto> list);

    /**
     * 删除员工
     *
     * @param ids
     */
    void removeCusEmployee(@Param("ids") List<Long> ids);

    /**
     * 获取公司对应的最大员工号
     *
     * @return max empNo
     */
    String getRecommendEmpNo(Long companyId);

    /**
     * 二维码邀请员工-注册开通账号
     *
     * @return map
     */
    // Map<String, String> inviteOpenAccount(CusEmpRegisterDto cusEmpRegisterDto);

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    CusEmployeeDto currentUserInfo();

    List<List<LuckySheetModel>> importError(Long taskId);

    void saveSheetHead(HeadImportErrorDto importErrorDto);

    Boolean saveSheetData(EmployeeImportDto importDto);

    CusEmployeeDto getInfo(Long empId);

    void transferEmp(RemoveEmployDto employ);

    List<CusEmployeeDto> selectListByIds(List<Long> ids);

    // 初始化当前企业员工的角色信息和数据权限信息
    void initEmployeeRoleDataScope(Long... empIds);

    Boolean changeMobile(Long empId, String newMobile);

    AsCusEmployee getByIdWithDel(Long id);

    void updateAllCompanyEmpMobile(String mobile, List<Long> empIds);

    void updateAllCompanyEmpEmail(String email, List<Long> empIds);

    AsCusEmployee getDeleted(Long empId);

    /**
     * 内部服务，通过code或者name查询
     *
     * @return
     */
    Long getOne(String empName, String empNo);

    /**
     * 员工工号修改
     *
     * @param employeeModifyDto
     * @return
     */
    Boolean updateEmpNo(EmployeeModifyDto employeeModifyDto);

    /**
     * 校验员工姓名
     *
     * @param employee
     * @return false:存在重名 true:没有重名
     */
    Boolean verifyEmpName(CusEmployeeOptDto employee);

    /**
     * 批量添加员工，当前有工号的时候，不进行同步工号
     *
     * @param employeeList
     * @return
     */
    Boolean batchSaveCondition(List<AsCusEmployee> employeeList);

    /**
     * 批量更新用户信息
     *
     * @param employeeList
     * @return
     */
    Boolean batchUpdateByIdCondition(List<AsCusEmployee> employeeList);


}
