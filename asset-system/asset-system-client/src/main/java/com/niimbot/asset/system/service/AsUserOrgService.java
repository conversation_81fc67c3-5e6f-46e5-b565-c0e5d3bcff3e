package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsUserOrg;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface AsUserOrgService extends IService<AsUserOrg> {

    Map<Long, List<Long>> groupByUser(List<Long> userIds);

    void removeByUser(List<Long> userIds);

    /**
     * 查询用户的组织Id（type=1 所在公司，type=2 所在组织）
     *
     * @param type
     * @return
     */
    List<Long> orgIdsByUserId(Long companyId, Long userId, Integer type);

    /**
     * 查询用户的所属公司或部门
     *
     * @param companyId 租户
     * @param userIds   员工集合（如果为空则查询所有账户）
     * @param type      1-公司 2-部门
     * @return
     */
    List<AsUserOrg> orgIdsByUserIds(Long companyId, List<Long> userIds, Integer type);
}
