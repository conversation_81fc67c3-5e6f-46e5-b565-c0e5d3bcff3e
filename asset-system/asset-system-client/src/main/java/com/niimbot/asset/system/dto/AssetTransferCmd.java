package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.system.CusEmployeeTransferDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetTransferCmd extends CommonCommand {
    /**
     * 异动id
     */
    private Long employeeChangeId;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 企业id
     */
    private Long companyId;

    private List<CusEmployeeTransferDto> transfers;

    private List<AsOrg> orgList;
}
