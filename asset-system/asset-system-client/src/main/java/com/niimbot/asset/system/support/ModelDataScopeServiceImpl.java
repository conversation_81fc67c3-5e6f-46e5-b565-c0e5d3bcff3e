package com.niimbot.asset.system.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.model.UserDataOrgPermBizDto;
import com.niimbot.asset.framework.service.AbstractTokenService;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.model.ModelDataRule;
import com.niimbot.framework.dataperm.core.model.ModelDataScope;
import com.niimbot.framework.dataperm.core.model.ModelDataScopeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/7 16:11
 */
@Service
public class ModelDataScopeServiceImpl implements ModelDataScopeService {

    private final static String REDIS_KEY = "modelDataScope";

    public String getCacheKey(Long companyId, Long userId) {
        return REDIS_KEY + ":" + companyId + ":" + userId;
    }

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private AbstractTokenService tokenService;

    @Autowired
    private RedisService redisService;

    private static DataAuthorityService dataAuthorityService = null;
    private static CompanySettingService companySettingService = null;
    private static AsUserOrgService userOrgService = null;

    @Override
    public List<ModelDataScope> loadDataScope() {
        // 这里需要懒加载，否则注册到mybatis-plus拦截器会失败
        if (dataAuthorityService == null) {
            dataAuthorityService = SpringUtil.getBean(DataAuthorityService.class);
        }
        if (companySettingService == null) {
            companySettingService = SpringUtil.getBean(CompanySettingService.class);
        }
        if (userOrgService == null) {
            userOrgService = SpringUtil.getBean(AsUserOrgService.class);
        }
        // 获取当前用户
        String token = tokenService.getToken(tokenService.getRequest());
        // token不为空，说明一定是web访问，没有用户直接报错
        if (StrUtil.isNotEmpty(token)) {
            LoginUserDto loginUser = tokenService.getLoginUserByToken(token);
            if (loginUser != null) {
                CusUserDto cusUser = loginUser.getCusUser();
                if (cusUser == null) {
                    return new ArrayList<>();
                }
                // 如果是超管，直接返回超管DataScope
                if (BooleanUtil.isTrue(cusUser.getIsAdmin())) {
                    return adminDataScope(cusUser);
                } else {
                    String cacheKey = getCacheKey(cusUser.getCompanyId(), cusUser.getId());
                    // 查询缓存
                    if (redisService.hasKey(cacheKey)) {
                        return Convert.toList(ModelDataScope.class, redisService.get(cacheKey));
                    } else {
                        // 走Db查询
                        List<ModelDataScope> modelDataScopeList = queryDataScope(cusUser);
                        redisService.set(cacheKey, modelDataScopeList, 3, TimeUnit.DAYS);
                        return modelDataScopeList;
                    }
                }
            } else {
                throw new BusinessException(SystemResultCode.TOKEN_EXPIRE);
            }
        }
        return new ArrayList<>();
    }

    public void cleanDataScopeCache(Long companyId, List<Long> userIdList) {
        if (CollUtil.isNotEmpty(userIdList)) {
            // 清理数据权限缓存
            taskExecutor.submit(() -> {
                List<String> collect = userIdList.stream().map(f -> getCacheKey(companyId, f)).collect(Collectors.toList());
                redisService.del(collect);
            });
        }
    }

    private List<ModelDataScope> queryDataScope(CusUserDto cusUser) {
        List<ModelDataScope> modelDataScopeList = new ArrayList<>();
        // 全局dataScope
        ModelDataScope modelDataScope = new ModelDataScope(cusUser.getCompanyId(), cusUser.getId());
        // 查询db数据
        List<AsDataAuthority> authorities = dataAuthorityService.getListByUserId(cusUser.getId());
        // 查询当前用户的所在公司
        List<Long> companyOwnerIds = userOrgService.orgIdsByUserId(cusUser.getCompanyId(), cusUser.getId(), 1);
        // 查询当前用户的所在组织
        List<Long> orgIds = userOrgService.orgIdsByUserId(cusUser.getCompanyId(), cusUser.getId(), 2);
        // 是否开放闲置资产
        CompanySwitch switchSetting = companySettingService.getSwitchSettingWithCache(cusUser.getCompanyId());
        // 权限集合
        List<ModelDataRule> modelDataRuleList = new ArrayList<>();
        // ========== 兼容php迁移数据，没有onlyOneself的属性补齐一个 ==========
        Optional<AsDataAuthority> hasOnlyOneself = authorities.stream().filter(authority ->
                AssetConstant.DATA_PERMISSION_ASSET.equals(authority.getAuthorityDataCode())
                        && AssetConstant.AUTHORITY_ONLY_ONESELF.equals(authority.getAuthorityCode())).findAny();
        if (!hasOnlyOneself.isPresent()) {
            authorities.add(new AsDataAuthority()
                    .setAuthorityDataCode(AssetConstant.DATA_PERMISSION_ASSET)
                    .setAuthorityCode(AssetConstant.AUTHORITY_ONLY_ONESELF)
                    .setAuthorityType(AssetConstant.AUTHORITY_TYPE_ONESELF_AND_OTHERS)
            );
        }
        // 循环生成所需权限数据
        for (AsDataAuthority authority : authorities) {
            // ================================ 通用选择器权限 ==============================================
            // 组织权限
            if (AssetConstant.DATA_PERMISSION_ORG.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_DEPTS.equals(authority.getAuthorityCode())) {
                orgPerms(authority, orgIds, companyOwnerIds).ifPresent(modelDataRuleList::add);
            }
            // 区域权限
            else if (AssetConstant.DATA_PERMISSION_AREA.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_DEPTS.equals(authority.getAuthorityCode())) {
                areaPerms(authority, companyOwnerIds).ifPresent(modelDataRuleList::add);
            }
            // 资产分类权限
            else if (AssetConstant.DATA_PERMISSION_ASSET.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_CATE.equals(authority.getAuthorityCode())) {
                catePerms(authority).ifPresent(modelDataRuleList::add);
            }
            // 耗材仓库权限
            else if (AssetConstant.DATA_PERMISSION_MATERIAL.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_STORE.equals(authority.getAuthorityCode())) {
                storePerms(authority, companyOwnerIds).ifPresent(modelDataRuleList::add);
            }

            // ============= 用户会存在很多不同的配置，需要单独指定 =============
            // 资产数据 仅可查看自己管理和使用的资产数据
            else if (AssetConstant.DATA_PERMISSION_ASSET.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_ONLY_ONESELF.equals(authority.getAuthorityCode())) {
                ModelDataRule rule = new ModelDataRule(DataPermType.USER, DataPermRuleType.TYPE_CUSTOM_LIST);
                rule.setIds(ListUtil.of(cusUser.getId()));
                rule.setBizCode(authority.getAuthorityDataCode());
                rule.setSubBizCode(authority.getAuthorityCode());
                if (AssetConstant.AUTHORITY_TYPE_ONLY_ONESELF == authority.getAuthorityType()) {
                    // 仅查看自己的资产数据 and 关系
                    modelDataRuleList.add(rule);
                    // 开放闲置资产
                    if (BooleanUtil.isTrue(switchSetting.getEnableIdleAsset())) {
                        // or 关系
                        ModelDataScope idleAssetDataScope = getIdleAssetDataScope(cusUser);
                        modelDataScopeList.add(idleAssetDataScope);
                    }
                } else if (AssetConstant.AUTHORITY_TYPE_ONESELF_AND_OTHERS == authority.getAuthorityType()) {
                    AtomicBoolean hasPerm = new AtomicBoolean(false);
                    for (AsDataAuthority selfAuth : authorities) {
                        // 写入资产分类
                        if (AssetConstant.DATA_PERMISSION_ASSET.equals(selfAuth.getAuthorityDataCode())
                                && AssetConstant.AUTHORITY_CATE.equals(selfAuth.getAuthorityCode())) {
                            catePerms(selfAuth).ifPresent(r -> {
                                r.setBizCode(AssetConstant.DATA_PERMISSION_ASSET);
                                r.setSubBizCode(AssetConstant.AUTHORITY_CATE);
                                modelDataRuleList.add(r);
                                hasPerm.set(true);
                            });
                        }
                        // 写入区域分类
                        if (AssetConstant.DATA_PERMISSION_AREA.equals(selfAuth.getAuthorityDataCode())
                                && AssetConstant.AUTHORITY_DEPTS.equals(selfAuth.getAuthorityCode())) {
                            areaPerms(selfAuth, companyOwnerIds).ifPresent(r -> {
                                r.setBizCode(AssetConstant.DATA_PERMISSION_ASSET);
                                r.setSubBizCode(AssetConstant.AUTHORITY_AREAS);
                                modelDataRuleList.add(r);
                                hasPerm.set(true);
                            });

                        }
                        // 写入部门
                        if (AssetConstant.DATA_PERMISSION_ORG.equals(selfAuth.getAuthorityDataCode())
                                && AssetConstant.AUTHORITY_DEPTS.equals(selfAuth.getAuthorityCode())) {
                            orgPerms(selfAuth, orgIds, companyOwnerIds).ifPresent(r -> {
                                r.setBizCode(AssetConstant.DATA_PERMISSION_ASSET);
                                r.setSubBizCode(AssetConstant.ORG_OWNER);
                                modelDataRuleList.add(r);
                                hasPerm.set(true);
                            });
                            // 判断管理组织使用组织，是否添加使用组织
                            Optional<AsDataAuthority> managerOrUseDept = authorities.stream().filter(a -> AssetConstant.DATA_PERMISSION_ASSET.equals(a.getAuthorityDataCode())
                                    && AssetConstant.AUTHORITY_MANAGER_OR_USE_DEPT.equals(a.getAuthorityCode())).findAny();
                            if (managerOrUseDept.isPresent()) {
                                orgPerms(selfAuth, orgIds, companyOwnerIds).ifPresent(r -> {
                                    r.setBizCode(AssetConstant.DATA_PERMISSION_ASSET);
                                    r.setSubBizCode(AssetConstant.USE_ORG);
                                    modelDataRuleList.add(r);
                                    hasPerm.set(true);
                                });
                            }
                        }
                    }

                    if (hasPerm.get()) {
                        // or 关系
                        ModelDataScope selfAssetDataScope = new ModelDataScope(cusUser.getCompanyId(), cusUser.getId());
                        selfAssetDataScope.setRuleList(ListUtil.of(rule));
                        modelDataScopeList.add(selfAssetDataScope);

                        // 开放闲置资产
                        if (BooleanUtil.isTrue(switchSetting.getEnableIdleAsset())) {
                            // or 关系
                            ModelDataScope idleAssetDataScope = getIdleAssetDataScope(cusUser);
                            modelDataScopeList.add(idleAssetDataScope);
                        }
                    }
                }
            }

            // 资产单据创建人
            else if (AssetConstant.DATA_PERMISSION_ASSET_ORDER.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_CREATE_USER.equals(authority.getAuthorityCode())) {
                userPerms(authority, cusUser.getId(), orgIds).ifPresent(modelDataRuleList::add);
                // 特殊处理审批人信息
                Optional<AsDataAuthority> orderCreateBy = authorities.stream().filter(a -> AssetConstant.DATA_PERMISSION_ASSET_ORDER.equals(a.getAuthorityDataCode())
                        && AssetConstant.AUTHORITY_CREATE_USER.equals(a.getAuthorityCode())).findAny();
                // 如果创建人设置为所有，不需要处理创建人是本人的数据
                if (orderCreateBy.isPresent()
                        && orderCreateBy.get().getAuthorityType() != AssetConstant.AUTHORITY_TYPE_ALL
                        && orderCreateBy.get().getAuthorityType() != AssetConstant.AUTHORITY_TYPE_USER_ONLY) {
                    authority.setAuthorityType(AssetConstant.AUTHORITY_TYPE_USER_ONLY);
                    userPerms(authority, cusUser.getId(), orgIds).ifPresent(modelDataRuleList::add);
                }
            }
            // 资产单据审批人
            else if (AssetConstant.DATA_PERMISSION_ASSET_ORDER.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_APPROVAL_USER.equals(authority.getAuthorityCode())) {
                // 这里是兼容盘点没有审批流的情况，审批人需要权限
                userPerms(authority, cusUser.getId(), orgIds).ifPresent(modelDataRuleList::add);
                // 特殊处理审批人信息
                Optional<AsDataAuthority> orderCreateBy = authorities.stream().filter(a -> AssetConstant.DATA_PERMISSION_ASSET_ORDER.equals(a.getAuthorityDataCode())
                        && AssetConstant.AUTHORITY_CREATE_USER.equals(a.getAuthorityCode())).findAny();
                // 如果创建人设置为所有，不需要处理审批人数据
                if (orderCreateBy.isPresent() && orderCreateBy.get().getAuthorityType() != AssetConstant.AUTHORITY_TYPE_ALL) {
                    orderPerms(authority, cusUser.getId(), orgIds)
                            .ifPresent(f -> {
                                ModelDataScope orderApproveScope = new ModelDataScope(cusUser.getCompanyId(), cusUser.getId());
                                orderApproveScope.setRuleList(ListUtil.of(f));
                                modelDataScopeList.add(orderApproveScope);
                            });
                }
            }
            // 耗材单据创建人
            else if (AssetConstant.DATA_PERMISSION_MATERIAL_ORDER.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_CREATE_USER.equals(authority.getAuthorityCode())) {
                userPerms(authority, cusUser.getId(), orgIds).ifPresent(modelDataRuleList::add);
                // 特殊处理审批人信息
                Optional<AsDataAuthority> orderCreateBy = authorities.stream().filter(a -> AssetConstant.DATA_PERMISSION_MATERIAL_ORDER.equals(a.getAuthorityDataCode())
                        && AssetConstant.AUTHORITY_CREATE_USER.equals(a.getAuthorityCode())).findAny();
                // 如果创建人设置为所有，不需要处理审批人数据
                if (orderCreateBy.isPresent()
                        && orderCreateBy.get().getAuthorityType() != AssetConstant.AUTHORITY_TYPE_ALL
                        && orderCreateBy.get().getAuthorityType() != AssetConstant.AUTHORITY_TYPE_USER_ONLY) {
                    authority.setAuthorityType(AssetConstant.AUTHORITY_TYPE_USER_ONLY);
                    userPerms(authority, cusUser.getId(), orgIds).ifPresent(modelDataRuleList::add);
                }
            }
            // 耗材单据审批人
            else if (AssetConstant.DATA_PERMISSION_MATERIAL_ORDER.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_APPROVAL_USER.equals(authority.getAuthorityCode())) {
                // 特殊处理审批人信息
                Optional<AsDataAuthority> orderCreateBy = authorities.stream().filter(a -> AssetConstant.DATA_PERMISSION_MATERIAL_ORDER.equals(a.getAuthorityDataCode())
                        && AssetConstant.AUTHORITY_CREATE_USER.equals(a.getAuthorityCode())).findAny();
                // 如果创建人设置为所有，不需要处理审批人数据
                if (orderCreateBy.isPresent() && orderCreateBy.get().getAuthorityType() != AssetConstant.AUTHORITY_TYPE_ALL) {
                    orderPerms(authority, cusUser.getId(), orgIds)
                            .ifPresent(f -> {
                                ModelDataScope orderApproveScope = new ModelDataScope(cusUser.getCompanyId(), cusUser.getId());
                                orderApproveScope.setRuleList(ListUtil.of(f));
                                modelDataScopeList.add(orderApproveScope);
                            });
                }
            }
            //设备任务
            else if (AssetConstant.DATA_PERMISSION_EQUIPMENT.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_MAINTAIN_USER.equals(authority.getAuthorityCode())) {
                userPerms(authority, cusUser.getId(), orgIds).ifPresent(modelDataRuleList::add);
            }
            //设备计划
            else if (AssetConstant.DATA_PERMISSION_EQUIPMENT.equals(authority.getAuthorityDataCode())
                    && AssetConstant.AUTHORITY_CREATE_USER.equals(authority.getAuthorityCode())) {
                userPerms(authority, cusUser.getId(), orgIds)
                        .ifPresent(modelDataRuleList::add);
            }
        }

        modelDataScope.setRuleList(modelDataRuleList);
        modelDataScopeList.add(modelDataScope);
        return modelDataScopeList;
    }


    private Optional<ModelDataRule> orgPerms(AsDataAuthority authority, List<Long> orgIds, List<Long> companyOwnerIds) {
        ModelDataRule modelDataRule = null;
        // 组织权限
        if (authority.getAuthorityType() == AssetConstant.AUTHORITY_TYPE_DEPT_ONLY) {
            // 所在组织(公司)
            modelDataRule = new ModelDataRule(DataPermType.DEPT, DataPermRuleType.TYPE_CURRENT_ONLY);
            modelDataRule.setIds(orgIds);
        } else if (authority.getAuthorityType() == AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT) {
            // 所在组织和子组织
            modelDataRule = new ModelDataRule(DataPermType.DEPT, DataPermRuleType.TYPE_CURRENT_AND_CHILD);
            modelDataRule.setIds(orgIds);
        } else if (authority.getAuthorityType() == AssetConstant.AUTHORITY_TYPE_COMP_AND_CHILD_DEPT) {
            // 所属公司及其下属组织
            modelDataRule = new ModelDataRule(DataPermType.DEPT, DataPermRuleType.TYPE_CURRENT_AND_CHILD);
            modelDataRule.setIds(companyOwnerIds);
        } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义
            modelDataRule = new ModelDataRule(DataPermType.DEPT, DataPermRuleType.TYPE_CUSTOM_LIST);
            modelDataRule.setIds(authority.longAuthorityData());
        }
        return Optional.ofNullable(modelDataRule);
    }

    private Optional<ModelDataRule> areaPerms(AsDataAuthority authority, List<Long> companyOwnerIds) {
        ModelDataRule modelDataRule = null;
        if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == authority.getAuthorityType()) {
            // 本公司区域
            modelDataRule = new ModelDataRule(DataPermType.AREA, DataPermRuleType.TYPE_CURRENT_ONLY);
            modelDataRule.setIds(companyOwnerIds);
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == authority.getAuthorityType()) {
            // 本公司和下属公司区域
            modelDataRule = new ModelDataRule(DataPermType.AREA, DataPermRuleType.TYPE_CURRENT_AND_CHILD);
            modelDataRule.setIds(companyOwnerIds);
        } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义
            List<UserDataOrgPermBizDto> authorityDataList = authority.userDataOrgPermBizDtoAuthorityData();
            modelDataRule = new ModelDataRule(DataPermType.AREA, DataPermRuleType.TYPE_CUSTOM_LIST);
            modelDataRule.setIds(authorityDataList.stream().flatMap(v -> v.getBiz().stream()).collect(Collectors.toList()));
        }
        return Optional.ofNullable(modelDataRule);
    }

    private Optional<ModelDataRule> catePerms(AsDataAuthority authority) {
        ModelDataRule modelDataRule = null;
        if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义
            modelDataRule = new ModelDataRule(DataPermType.CATE, DataPermRuleType.TYPE_CUSTOM_LIST);
            modelDataRule.setIds(authority.longAuthorityData());

        }
        return Optional.ofNullable(modelDataRule);
    }

    private Optional<ModelDataRule> storePerms(AsDataAuthority authority, List<Long> companyOwnerIds) {
        ModelDataRule modelDataRule = null;
        if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == authority.getAuthorityType()) {
            // 本公司仓库
            modelDataRule = new ModelDataRule(DataPermType.STORE, DataPermRuleType.TYPE_CURRENT_ONLY);
            modelDataRule.setIds(companyOwnerIds);
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == authority.getAuthorityType()) {
            // 本公司和下属公司仓库
            modelDataRule = new ModelDataRule(DataPermType.STORE, DataPermRuleType.TYPE_CURRENT_AND_CHILD);
            modelDataRule.setIds(companyOwnerIds);
        } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义
            List<UserDataOrgPermBizDto> authorityDataList = authority.userDataOrgPermBizDtoAuthorityData();
            modelDataRule = new ModelDataRule(DataPermType.STORE, DataPermRuleType.TYPE_CUSTOM_LIST);
            modelDataRule.setIds(authorityDataList.stream().flatMap(v -> v.getBiz().stream()).collect(Collectors.toList()));
        }
        return Optional.ofNullable(modelDataRule);
    }

    private Optional<ModelDataRule> userPerms(AsDataAuthority authority, Long userId, List<Long> orgIds) {
        ModelDataRule modelDataRule = null;
        if (AssetConstant.AUTHORITY_TYPE_USER_ONLY == authority.getAuthorityType()) {
            // 本人
            modelDataRule = new ModelDataRule(DataPermType.USER, DataPermRuleType.TYPE_CUSTOM_LIST);
            modelDataRule.setIds(ListUtil.of(userId));
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == authority.getAuthorityType()) {
            // 所在组织
            modelDataRule = new ModelDataRule(DataPermType.USER, DataPermRuleType.TYPE_CURRENT_ONLY);
            modelDataRule.setIds(orgIds);
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == authority.getAuthorityType()) {
            // 所在组织和下属组织
            modelDataRule = new ModelDataRule(DataPermType.USER, DataPermRuleType.TYPE_CURRENT_AND_CHILD);
            modelDataRule.setIds(orgIds);
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义
            modelDataRule = new ModelDataRule(DataPermType.USER, DataPermRuleType.TYPE_CURRENT_ONLY);
            modelDataRule.setIds(authority.longAuthorityData());
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_ALL == authority.getAuthorityType()) {
            // 自定义
            modelDataRule = new ModelDataRule(DataPermType.USER, DataPermRuleType.TYPE_ALL);
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        }
        return Optional.ofNullable(modelDataRule);
    }

    private Optional<ModelDataRule> orderPerms(AsDataAuthority authority, Long userId, List<Long> orgIds) {
        ModelDataRule modelDataRule = null;
        if (AssetConstant.AUTHORITY_TYPE_USER_ONLY == authority.getAuthorityType()) {
            // 本人
            modelDataRule = new ModelDataRule(DataPermType.ORDER, DataPermRuleType.TYPE_CUSTOM_LIST);
            modelDataRule.setIds(ListUtil.of(userId));
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == authority.getAuthorityType()) {
            // 所在组织
            modelDataRule = new ModelDataRule(DataPermType.ORDER, DataPermRuleType.TYPE_CURRENT_ONLY);
            modelDataRule.setIds(orgIds);
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == authority.getAuthorityType()) {
            // 所在组织和下属组织
            modelDataRule = new ModelDataRule(DataPermType.ORDER, DataPermRuleType.TYPE_CURRENT_AND_CHILD);
            modelDataRule.setIds(orgIds);
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义
            modelDataRule = new ModelDataRule(DataPermType.ORDER, DataPermRuleType.TYPE_CURRENT_ONLY);
            modelDataRule.setIds(authority.longAuthorityData());
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        } else if (AssetConstant.AUTHORITY_TYPE_ALL == authority.getAuthorityType()) {
            // 自定义
            modelDataRule = new ModelDataRule(DataPermType.ORDER, DataPermRuleType.TYPE_ALL);
            modelDataRule.setBizCode(authority.getAuthorityDataCode());
            modelDataRule.setSubBizCode(authority.getAuthorityCode());
        }
        return Optional.ofNullable(modelDataRule);
    }

    private List<ModelDataScope> adminDataScope(CusUserDto cusUser) {
        ModelDataScope modelDataScope = new ModelDataScope(cusUser.getCompanyId(), cusUser.getId());
        modelDataScope.setRuleList(new ArrayList<>());
        return ListUtil.of(modelDataScope);
    }

    private ModelDataScope getIdleAssetDataScope(CusUserDto cusUser) {
        ModelDataScope idleAsset = new ModelDataScope(cusUser.getCompanyId(), cusUser.getId());
        ModelDataRule rule = new ModelDataRule(DataPermType.USER, DataPermRuleType.TYPE_CUSTOM_LIST);
        rule.setIds(ListUtil.of(Convert.toLong(AssetConstant.ASSET_STATUS_IDLE)));
        rule.setBizCode(AssetConstant.DATA_PERMISSION_ASSET);
        rule.setSubBizCode(AssetConstant.STATUS);
        idleAsset.setRuleList(ListUtil.of(rule));
        return idleAsset;
    }

}
