package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsUserReports对象", description = "用户统计表")
public class AsUserReports implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "账号id（员工Id）")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "认证中心Id")
    private Long companyId;

    @ApiModelProperty(value = "创建时间 单位：天")
    private LocalDateTime dayTime;

    @ApiModelProperty(value = "所在一年的第几周")
    private Integer weekTime;


}
