package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.AdminPrinterGetQry;
import com.niimbot.asset.system.dto.AdminPrinterListQry;
import com.niimbot.asset.system.dto.clientobject.AdminPrinterCO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 原始接口 {@link com.niimbot.asset.means.service.AdminPrinterService}
 * <AUTHOR>
 * @date 2022/5/11 17:55
 */
public interface AdminPrinterAbs {

    /**
     * 获取打印设备
     * printerService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
     *                     .eq(AsAdminPrinter::getModel, printerName))
     * @param qry
     * @return
     */
    @GetMapping("getAdminPrinter")
    AdminPrinterCO getAdminPrinter(@SpringQueryMap AdminPrinterGetQry qry);

    /**
     * 获取打印设备列表
     * printerService.list(Wrappers.<AsAdminPrinter>lambdaQuery()
     *                     .eq(AsAdminPrinter::getIsApplyApp, true));
     * @param qry
     * @return
     */
    @GetMapping("listAdminPrinter")
    List<AdminPrinterCO> listAdminPrinter(@SpringQueryMap AdminPrinterListQry qry);
}
