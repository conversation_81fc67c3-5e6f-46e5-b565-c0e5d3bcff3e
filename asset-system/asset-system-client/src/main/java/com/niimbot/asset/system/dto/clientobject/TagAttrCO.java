package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/7/13 11:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class TagAttrCO extends ClientObject {
    @ApiModelProperty(value = "属性编码")
    private String attrCode;

    @ApiModelProperty(value = "属性显示名称")
    private String attrName;

    @ApiModelProperty(value = "属性名称是否显示")
    private Boolean isShow = true;

    @ApiModelProperty(value = "属性名称是否编辑过")
    private Boolean isEdit;

    @ApiModelProperty(value = "属性名称别名")
    private String alias = "";

    @ApiModelProperty(value = "属性名称原始名称")
    private String originalTitle;

    @ApiModelProperty("属性类型")
    private String attrType;

    @Deprecated
    @ApiModelProperty(value = "[已废弃] 属性分组 1-基本信息，2-使用信息，3-维保信息，4-其他信息")
    private Integer attrGroup;

    @ApiModelProperty(value = "打印分隔符 1-中文冒号（：），2-英文冒号（:），3-中划线（-），4-空格（ ），5-无分隔符")
    private Integer delimiterType = 1;

    @ApiModelProperty(value = "所属公司字段取值 1-所属管理组织公司信息，2-使用组织公司信息")
    private Integer companyOwnerVal = 1;

    @ApiModelProperty(value = "自定义文案取值")
    private String customWords;

    @ApiModelProperty(value = "对齐类型 1-居左 2-居中 3-居右")
    private Integer align = 1;

    @ApiModelProperty(value = "是否自定义")
    private Boolean isCustomize = false;

    @ApiModelProperty("默认换行模式")
    private Integer lineMode = 9;
}
