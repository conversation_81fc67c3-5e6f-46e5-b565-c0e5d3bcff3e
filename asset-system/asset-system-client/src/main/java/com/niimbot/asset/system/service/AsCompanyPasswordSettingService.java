package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCompanyPasswordSetting;
import com.niimbot.system.CompanyPasswordSettingDto;

public interface AsCompanyPasswordSettingService extends IService<AsCompanyPasswordSetting> {

    Boolean saveOrUpdate(CompanyPasswordSettingDto settingDto);

    void checkPwdByRule(String pwd, Long companyId);

    /**
     * @param companyId 公司名称
     * @param isSetting 是否用于设置，（关闭的配置，在设置里面也需要展示，使用的时候用系统默认）
     * @return
     */
    CompanyPasswordSettingDto getSettingDetail(Long companyId, boolean isSetting);

    void markChangePwd(Long companyId, Long accountId);

    String getLimitWords();

    String getLimitWordsByMobile(String mobile);

    Boolean closeSwitch();

}
