package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/6 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetQueryViewInitCmd extends CommonCommand {
    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 员工id
     */
    private Long employeeId;
}
