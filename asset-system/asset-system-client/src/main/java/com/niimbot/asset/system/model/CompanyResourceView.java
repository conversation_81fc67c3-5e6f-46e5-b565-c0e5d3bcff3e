package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * company_resource_view
 *
 * <AUTHOR>
@Data
@TableName(value = "company_resource_view", autoResultMap = true)
public class CompanyResourceView {
    /**
     * 企业Id
     */
    @TableId(value = "company_id")
    private Long companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 剩余容量
     */
    private Long remainder;

    /**
     * 已使用容量
     */
    private Long hasUsed;

    /**
     * 总容量
     */
    private Long capacity;

    /**
     * 到期时间
     */
    private LocalDateTime expirationTime;
}