package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName(value = "as_third_party_account", autoResultMap = true)
@ApiModel(value = "AsThirdPartyAccount对象", description = "账号与第三方平台关联表")
public class AsThirdPartyAccount implements Serializable {

    @ApiModelProperty("主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("云资产系统账号ID")
    private Long accountId;

    @ApiModelProperty("第三方类型")
    private String type;

    @ApiModelProperty("第三方用户针对服务上账号下，不同的应用获取到的同一用户openId不同")
    private String openId;

    @ApiModelProperty("第三方用户针对服务商账号下的用户唯一标识，即同一账号下不同应用下获取到的同一用户ID相同")
    private String uniqueId;

    @ApiModelProperty("第三方用户昵称")
    private String nickname;

    @ApiModelProperty("头像URL")
    public String avatarUrl;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
