package com.niimbot.asset.system.abs;

import com.niimbot.purchase.PurchaseOrderDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 采购单
 * 原始接口 {@link com.niimbot.asset.purchase.service.AsPurchaseOrderService}
 * <AUTHOR>
 * @date 2022/5/10 10:45
 */
public interface PurchaseOrderAbs {

    @GetMapping("/getByNo/{orderNo}")
    PurchaseOrderDto getDetailByOrderNo(@PathVariable("orderNo") String orderNo);

    @GetMapping("/getIdByNo/{orderNo}")
    Long getIdByOrderNo(@PathVariable("orderNo") String orderNo);
}
