package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsUserTag;
import com.niimbot.means.PrintDataSetTagDto;
import com.niimbot.system.*;

import java.util.HashMap;
import java.util.List;

/**
 * 标签管理service
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
public interface TagService extends IService<AsUserTag> {

    /**
     * 通过sizeId
     *
     * @param sizeId 尺寸Id
     * @return 标签信息
     */
    UserTagResDto getBySizeId(Short printType, Long sizeId, Integer tagType, String kw, String printerName);

    /**
     * 获取尺寸列表
     *
     * @param printerIds  打印机ids
     * @param printerName printerName
     * @return 尺寸信息
     */
    SizeResDto getSizeList(Short printType, List<Long> printerIds, String printerName);

    /**
     * 获取app尺寸列表
     *
     * @return 尺寸信息
     */
    List<SizeDto> appSizeList(String printerName);

    /**
     * 设置资产默认标签模板
     *
     * @param printDataSetTagDto 设置默认标签dto
     * @return Boolean
     */
    Boolean setDefaultTag(PrintDataSetTagDto printDataSetTagDto);

    /**
     * 设置耗材默认标签模板
     *
     * @param printDataSetTagDto 设置默认标签dto
     * @return Boolean
     */
    Boolean setDefaultCftag(PrintDataSetTagDto printDataSetTagDto);

    /**
     * 保存用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Boolean
     */
    Boolean saveUserTag(UserTagSaveDto userTagSaveDto);

    /**
     * 获取移动端标签列表
     *
     * @param sizeId  尺寸类型
     * @param tagType 标签类型
     * @param kw      关键字
     * @return 移动端标签列表
     */
    UserTagResDto appTagList(Short printType, Long sizeId, Integer tagType, String kw, String printerName);

    /**
     * 获取PDF模版
     *
     * @return 获取PDF模版
     */
    HashMap<String, List<UserTagDto>> getPdfTag(Short printType, Long printerId);

    /**
     * 修改标签模板名称
     *
     * @param userTagNameDto 修改标签模板名称对象
     * @return Boolean
     */
    Boolean updateTagName(UserTagNameDto userTagNameDto);

    /**
     * 编辑用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Boolean
     */
    Boolean editUserTag(UserTagSaveDto userTagSaveDto);

    /**
     * 删除用户自定义标签模板
     *
     * @param id 用户自定义标签id
     * @return Boolean
     */
    Boolean deleteUserTag(Long id);

    /**
     * 全部去重数据查询列表
     *
     * @param printType 打印类型
     * @param sizeId    尺寸id
     * @return 标签模板信息
     */
    List<UserTagDto> getDistinctBySizeId(Short printType, Long sizeId, Long printerId);

    /**
     * 标签模板详情
     *
     * @param id 模板id
     * @return 标签模板信息
     */
    UserTagDetailDto getDetail(Long id);
}

