package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCusEmployeeExt;

/**
 * 用户端配置
 *
 * <AUTHOR>
 * @since 2020/12/08
 */
public interface AsCusEmployeeExtService extends IService<AsCusEmployeeExt> {

    /**
     * 更新用户最后登录时间
     *
     * @param userId 用户Id
     */
    void updateLastLoginTime(Long userId);

    void add(Long employeeId);
}
