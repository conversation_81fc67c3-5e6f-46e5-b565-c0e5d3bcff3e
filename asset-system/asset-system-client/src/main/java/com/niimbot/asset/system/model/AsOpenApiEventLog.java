package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/3/18 10:31
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_open_api_event_log", autoResultMap = true)
public class AsOpenApiEventLog {

    // 消息ID
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    // 事件类型
    private String eventType;

    // 租户ID
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    // API_ID
    private Long apiId;

    // API_KEY
    private String apiKey;

    // 原始数据包
    private String sourceData;

    // 0-未处理，1-完成，2-失败
    private Integer status;

    // 失败次数
    private Integer failCount;

    // 响应信息
    private String response;

    // 创建时间
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
