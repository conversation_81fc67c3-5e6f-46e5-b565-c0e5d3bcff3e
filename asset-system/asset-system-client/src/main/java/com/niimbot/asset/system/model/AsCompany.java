package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 公司表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsCompany对象", description = "公司表")
@TableName(value = "as_company", autoResultMap = true)
public class AsCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "企业名称")
    private String name;

    @ApiModelProperty(value = "企业地区")
    private String area;

    @ApiModelProperty(value = "企业地址")
    private String address;

    @ApiModelProperty(value = "企业电话")
    private String phone;

    @ApiModelProperty(value = "企业logo地址")
    private String logo;

    @ApiModelProperty(value = "行业名称")
    private String industryName;

    @ApiModelProperty(value = "行业类型")
    private Long industryId;

    @ApiModelProperty("状态: 1-正常, 2-禁用, 3-欠费, 4-流失, 5-已注销")
    private Integer status;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "企业名称修改次数")
    private Integer updateTimes;
}
