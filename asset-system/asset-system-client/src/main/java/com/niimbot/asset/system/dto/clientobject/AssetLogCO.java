package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetLogCO extends ClientObject {
    
    private Long id;

    /** 
     * 公司ID 
     */
    private Long companyId;

    /** 
     * 资产编号 
     */
    private Long assetId;

    /** 
     * 动作类型 
     */
    private Integer actionType;

    /** 
     * 动作名称(新增/编辑/删除/盘点)等 
     */
    private String actionName;

    /** 
     * 具体变化内容 
     */
    private String actionContent;

    /** 
     * 调整字段之前值 
     */
    private JSONObject originalData;

    /** 
     * 调整字段之后值 
     */
    private JSONObject changeData;

    /** 
     * 处理时间 
     */
    private LocalDateTime handleTime;

    /** 
     * 创建时间 
     */
    private LocalDateTime createTime;

    /** 
     * 创建人 
     */
    private Long createBy;

    /** 
     * 关联业务流水号(盘点/处理记录流水号) 
     */
    private String orderNo;

    /** 
     * 关联业务单据id(盘点/处理记录单据id) 
     */
    private Long orderId;
}
