package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.system.dto.openapi.OauthUserInfoRsp;
import com.niimbot.asset.system.model.AsOpenApi;
import com.niimbot.system.OpenApiAuthCodeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/1 16:58
 */
public interface AsOpenApiService extends IService<AsOpenApi> {

    Boolean add(AsOpenApi openApi);

    Boolean edit(AsOpenApi openApi);

    List<AsOpenApi> listByCompanyId(Long companyId);


    AsOpenApi getByAgentId(Long agentId);
    /**
     * 获取临时授权码
     */
    String getAuthCode(OpenApiAuthCodeDto authCodeDto, LoginUserDto loginUser);

    /**
     * 通过临时授权码获取员工信息
     */
    OauthUserInfoRsp getUserInfoByCode(Long companyId, String appKey, String code);
}
