package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.dto.clientobject.AssetAreaCO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/5/11 11:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetAreaSaveCmd extends CommonCommand {
    @NotNull
    private AssetAreaCO assetArea;
}
