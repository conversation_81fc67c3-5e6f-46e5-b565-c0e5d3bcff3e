package com.niimbot.asset.system.abs;

import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 */
public interface MessageAbs {

    @PostMapping("companyMessageRuleDataInit")
    void companyMessageRuleDataInit(Long companyId);

    @PostMapping("sendEmpChangeMessage")
    void sendEmpChangeMessage(Long companyId);

    @PostMapping("cleanThirdPartyMessageRules")
    void cleanThirdPartyMessageRules(Long companyId);

}
