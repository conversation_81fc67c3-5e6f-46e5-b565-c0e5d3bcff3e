package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.AssetQueryViewInitCmd;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 资产查询分组
 * 原始接口 {@link com.niimbot.asset.means.service.AsAssetQueryViewService}
 * <AUTHOR>
 * @date 2022/5/10 16:41
 */
public interface AssetQueryViewAbs {

    /**
     * 初始化员工查询分组
     * assetQueryViewService.initUserView(LoginUserThreadLocal.getCompanyId(), employee.getId());
     * @param cmd
     */
    @PostMapping("initUserView")
    void initUserView(AssetQueryViewInitCmd cmd);
}
