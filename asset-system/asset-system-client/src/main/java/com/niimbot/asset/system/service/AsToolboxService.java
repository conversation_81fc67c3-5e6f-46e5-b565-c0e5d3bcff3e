package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsToolbox;
import com.niimbot.system.ToolboxGroupItemDto;

import java.util.List;

/**
 * 工具箱service
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
public interface AsToolboxService extends IService<AsToolbox> {

    /**
     * 获取app工具箱
     *
     * @return 列表
     */
    List<AsToolbox> getAppToolBox();

    /**
     * 获取pc端工具箱
     *
     * @return 列表
     */
    List<AsToolbox> getPcToolBox();

    /**
     * 获取比较全的菜单
     * @return
     */
    List<AsToolbox> getAllToolbox();

    /**
     * 工具箱列表排序
     *
     * @param list list
     * @return 结果
     */
    Boolean sort(List<AsToolbox> list);

    List<ToolboxGroupItemDto> getWorkbench(Integer businessType);
}
