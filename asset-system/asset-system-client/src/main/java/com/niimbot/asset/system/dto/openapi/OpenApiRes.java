package com.niimbot.asset.system.dto.openapi;


import cn.hutool.core.bean.BeanUtil;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsOpenApi;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OpenApiRes {

    private Long apiId;

    private Long companyId;

    private String appName;

    private String appKey;

    private String appSecret;

    private String callback;

    private String aesKey;

    private String token;

    private String redirectUrl;

    private Long empId;


    public static OpenApiRes build(AsOpenApi asOpenApi ) {
        OpenApiRes infoRsp = new OpenApiRes();
        BeanUtil.copyProperties(asOpenApi, infoRsp);
        return infoRsp;
    }
}
