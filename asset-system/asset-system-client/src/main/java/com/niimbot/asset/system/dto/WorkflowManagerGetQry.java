package com.niimbot.asset.system.dto;

import com.alibaba.cola.dto.Query;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowManagerGetQry extends Query {

    private Long empId;

    private Long orgId;

    private Integer level;

    private Integer handleType;

    private Boolean isAgent;

    public WorkflowManagerGetQry(Long empId, Long orgId, Integer level, Integer handleType) {
        this.empId = empId;
        this.orgId = orgId;
        this.level = level;
        this.handleType = handleType;
    }
}
