package com.niimbot.asset.system.service;

import com.niimbot.kalimdor.hulk.model.TokenResponse;

/**
 * 获取用户unionId接口
 *
 * <AUTHOR>
 * @date 2021/4/2 16:49
 */
public interface UserExtService {
    /**
     * 通过验证码获取unionId
     *
     * @param mobile  手机号
     * @param smsCode 验证码
     * @return
     */
    TokenResponse getUnionIdBySmsCode(String mobile, String smsCode);
    // /**
    //  * 通过密码获取unionId
    //  *
    //  * @param account  账号密码
    //  * @param password 密码
    //  * @return
    //  */
    // TokenResponse getUnionIdByPassword(String account, String password);
    //
    // /**
    //  * 补充手机号/邮箱
    //  * @param unionId
    //  * @param account
    //  * @return
    //  */
    // String accountRenewal(String unionId, String account);
}
