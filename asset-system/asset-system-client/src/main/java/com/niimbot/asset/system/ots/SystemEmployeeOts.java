package com.niimbot.asset.system.ots;

import com.niimbot.asset.system.dto.EmployeeResignRecordGetQry;
import com.niimbot.asset.system.dto.WorkflowManagerGetQry;
import com.niimbot.asset.system.dto.clientobject.EmployeeCO;
import com.niimbot.asset.system.dto.clientobject.EmployeeResignRecordCO;
import com.niimbot.asset.system.dto.clientobject.ExternalRelation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SystemEmployeeOts {

    @GetMapping("getById/{id}")
    EmployeeCO getById(@PathVariable("id") Long id);

    @PostMapping("listByIds")
    List<EmployeeCO> listByIds(@RequestBody List<Long> ids);

    @GetMapping("getWorkFlowMultiManagers")
    List<List<EmployeeCO>> getWorkFlowMultiManagers(@SpringQueryMap WorkflowManagerGetQry qry);

    @GetMapping("getWorkflowManagers")
    List<EmployeeCO> getWorkflowManagers(@SpringQueryMap WorkflowManagerGetQry qry);

    @GetMapping("getCompanyAdministrator")
    EmployeeCO getCompanyAdministrator();

    @GetMapping("getAdmin")
    EmployeeCO getAdmin(@RequestParam Long companyId);

    @GetMapping("hasAccount/{empId}")
    Boolean hasAccount(@PathVariable("empId") Long empId);

    @PutMapping("filterWithAccount/{companyId}")
    Set<Long> filterWithAccount(@PathVariable("companyId") Long companyId, @RequestBody Set<Long> ids);

    @GetMapping("getResignRecordByType")
    EmployeeResignRecordCO getResignRecordByType(@SpringQueryMap EmployeeResignRecordGetQry qry);

    @GetMapping("getByIdWithDel/{id}")
    EmployeeCO getByIdWithDel(@PathVariable("id") Long id);

    @GetMapping("getEmpExternalRelationByCid")
    List<ExternalRelation> getEmpExternalRelation(@RequestParam Long companyId);

    @GetMapping("getEmpExternalRelation")
    List<ExternalRelation> getEmpExternalRelation(@RequestParam Long companyId, @RequestParam List<String> extIds);

    List<String> getEmpExternalIds(@RequestBody List<Long> ids);

    String getEmpExternalId(@PathVariable("id") Long id);
}
