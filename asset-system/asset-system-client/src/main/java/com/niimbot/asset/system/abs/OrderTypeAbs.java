package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.OrderTypeInitCmd;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 单据类型
 * 原始接口 {@link com.niimbot.asset.means.service.AsOrderTypeService}
 * <AUTHOR>
 * @date 2022/5/11 10:20
 */
public interface OrderTypeAbs {
    /**
     * 初始化
     * orderTypeService.initCompanyOrderType(companyId);
     * @param cmd
     */
    @PostMapping("initCompanyOrderType")
    void initCompanyOrderType(OrderTypeInitCmd cmd);
}
