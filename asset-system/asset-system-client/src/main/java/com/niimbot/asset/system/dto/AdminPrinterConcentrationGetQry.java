package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/7 16:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AdminPrinterConcentrationGetQry extends CommonCommand {
    /**
     * 打印机ID
     */
    private Long printerId;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否默认 0-不是默认标签数据  1-默认标签数据
     */
    private Boolean isDefault;
}
