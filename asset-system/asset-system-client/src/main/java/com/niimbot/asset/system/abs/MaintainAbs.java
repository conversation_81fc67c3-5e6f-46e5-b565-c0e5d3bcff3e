package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.TagAttrListQry;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2022/7/13 11:40
 */
public interface MaintainAbs {

    @GetMapping("getAttrList")
    TagAttrListCO getAttrList(@SpringQueryMap TagAttrListQry qry);
}
