package com.niimbot.asset.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.dto.DataSnapshotGetQry;
import com.niimbot.system.DataSnapshotSave;
import com.niimbot.asset.system.dto.DataSourceIdsGetQry;
import com.niimbot.asset.system.model.AsDataSnapshot;
import com.niimbot.means.StoreSnapshotSearch;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DataSnapshotService extends IService<AsDataSnapshot> {

    PageUtils<JSONObject> search(StoreSnapshotSearch search);

    List<Long> getSourceIds(DataSourceIdsGetQry qry);

    List<String> getSnapshotIds(DataSnapshotGetQry qry);

    List<JSONObject> getSnapshots(DataSnapshotGetQry qry);

    Integer saveSnapshots(DataSnapshotSave saved);

}
