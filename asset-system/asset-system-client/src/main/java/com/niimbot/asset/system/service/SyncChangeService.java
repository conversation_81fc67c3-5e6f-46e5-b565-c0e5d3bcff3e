package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsSyncChange;
import com.niimbot.system.RemoveEmployDto;
import com.niimbot.thirdparty.EmpTransferDto;
import com.niimbot.thirdparty.OrgTransferDto;
import com.niimbot.thirdparty.SyncChangeDto;
import com.niimbot.thirdparty.SyncChangeEmpDto;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
public interface SyncChangeService extends IService<AsSyncChange> {

    List<SyncChangeDto> listChange(Integer type, Integer status);

    SyncChangeEmpDto getEmp(Long id);

    AsOrg getOrg(Long id);

    Boolean transferEmpEdit(EmpTransferDto empTransferDto);

    Boolean transferEmpDelete(Long id, RemoveEmployDto employ);

    Boolean transferOrgDelete(OrgTransferDto orgTransferDto);

    void records(List<AsSyncChange> changes);

    AsSyncChange lastUntreatedRecord(Long companyId, Long resId, Integer type);

    void removeUntreatedRecord(Long companyId, Long resId, Integer type);
}
