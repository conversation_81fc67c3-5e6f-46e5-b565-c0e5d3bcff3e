package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemCompanyOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemCompanyOtsProcessClient")
@FeignClient(name = "asset-system", url = "{gateway}/system/SystemCompanyOts/")
public interface SystemCompanyOtsRemoteClient extends SystemCompanyOts {
}
