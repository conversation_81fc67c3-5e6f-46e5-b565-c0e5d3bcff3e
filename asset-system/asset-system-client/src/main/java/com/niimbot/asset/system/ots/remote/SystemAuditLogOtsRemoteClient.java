package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemAuditLogOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemAuditLogOtsProcessClient")
@FeignClient(name = "asset-system", url = "{gateway}/system/SystemAuditLogOts/")
public interface SystemAuditLogOtsRemoteClient extends SystemAuditLogOts {
}
