package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsVersionManagement对象", description = "版本管理")
@TableName(value = "as_version_management", autoResultMap = true)
public class AsVersionManagement implements Serializable {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("版本号第一位")
    private Integer versionNumberFirst;

    @ApiModelProperty("版本号第二位")
    private Integer versionNumberSecond;

    @ApiModelProperty("版本号第三位")
    private Integer versionNumberThird;

    @ApiModelProperty("版本号 v2.0.0")
    @TableField(exist = false)
    private String versionNumber;

    @ApiModelProperty("文件路径")
    private String path;

    @ApiModelProperty("更新描述")
    private String updateDesc;

    @ApiModelProperty("客户端类型 PC 1 APP 2 IOS 3")
    private Integer type;

    @ApiModelProperty("是否主动提示")
    private Boolean isProactivelyRemind;

    @ApiModelProperty("是否强制更新")
    private Boolean isMandatoryUpdate;

    @ApiModelProperty("更新方式描述")
    private String updateMethodDesc;

    @ApiModelProperty("是否已删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public void transformUpdateMethodDesc() {
        StringBuilder builder = new StringBuilder();
        if (Boolean.FALSE.equals(this.isProactivelyRemind) && Boolean.FALSE.equals(this.isMandatoryUpdate)) {
            this.updateMethodDesc = "";
            return;
        }
        if (Boolean.TRUE.equals(this.isMandatoryUpdate) && Boolean.TRUE.equals(this.isProactivelyRemind)) {
            builder.append("强制更新").append(",").append("主动提示更新");
            this.updateMethodDesc = builder.toString();
            return;
        }
        if (Boolean.TRUE.equals(this.isMandatoryUpdate)) {
            builder.append("强制更新");
        }
        if (Boolean.TRUE.equals(this.isProactivelyRemind)) {
            builder.append("主动提示更新");
        }
        this.updateMethodDesc = builder.toString();
    }
}
