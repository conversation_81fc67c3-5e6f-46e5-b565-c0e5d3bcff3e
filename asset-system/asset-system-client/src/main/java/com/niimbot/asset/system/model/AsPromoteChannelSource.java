package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsPromoteChannelSource对象", description = "渠道来源表")
@TableName(value = "as_promote_channel_source", autoResultMap = true)
public class AsPromoteChannelSource implements Serializable {

    @ApiModelProperty("来源ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("渠道ID")
    private Long promoteChannelId;

    @ApiModelProperty("来源名称")
    private String name;

    @ApiModelProperty("外部渠道编码")
    private String outerCode;

    @ApiModelProperty(value = "来源编码")
    private Long code;

    @ApiModelProperty("crmId")
    private Long crmId;

    @ApiModelProperty("crm负责人信息列表")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<CrmUser> crmPersonInCharge;

    @ApiModelProperty("crm协作人信息列表")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<CrmUser> crmCollaborator;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Data
    @Accessors(chain = true)
    public static class CrmUser implements Serializable{
        private String id;
        private String name;
    }
}
