package com.niimbot.asset.system.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;

/**
 * as_active_broadcast
 * <AUTHOR>
@Data
@TableName(value = "as_active_broadcast", autoResultMap = true)
public class AsActiveBroadcast implements Serializable {

    private static final long serialVersionUID = -600195502097991438L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动内容
     */
    private String content;

    /**
     * 活动状态
     */
    private Integer status;

    /**
     * 跳转类型
     */
    private Integer type;

    /**
     * 扩展参数
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject ext;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}