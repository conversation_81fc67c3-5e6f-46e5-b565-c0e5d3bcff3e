package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemOrgOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemOrgOtsProcessClient")
@FeignClient(name = "asset-system", url = "{gateway}/system/SystemOrgOts/")
public interface SystemOrgOtsRemoteClient extends SystemOrgOts {
}
