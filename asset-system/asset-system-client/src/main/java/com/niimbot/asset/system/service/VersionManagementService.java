package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsVersionManagement;
import com.niimbot.system.VersionManagementDto;

/**
 * <AUTHOR>
 */
public interface VersionManagementService extends IService<AsVersionManagement> {

    VersionManagementDto latestVersion(String version, Integer clientType);

    VersionManagementDto latestVersionMessage();

    Boolean versionMessageRead(Long versionManagementId);
}
