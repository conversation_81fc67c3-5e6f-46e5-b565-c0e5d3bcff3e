package com.niimbot.asset.system.ots;

import com.niimbot.asset.system.dto.clientobject.OrgCO;
import com.niimbot.asset.system.dto.clientobject.ExternalRelation;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemOrgOts {

    @GetMapping("getById/{id}")
    OrgCO getById(@PathVariable("id") Long id);

    @GetMapping("getEmpOrgIds")
    List<Long> getEmpOrgIds(@RequestParam Long empId);

    @GetMapping("getEmpOrgs")
    List<OrgCO> getEmpOrgs(@RequestParam Long empId);

    @GetMapping("getOrgExternalRelationByCompanyId")
    List<ExternalRelation> getOrgExternalRelation(@RequestParam Long companyId);

    @GetMapping("getOrgExternalRelationByExtIds")
    List<ExternalRelation> getOrgExternalRelation(@RequestParam Long companyId, @RequestParam List<String> externalIds);

    List<AsOrg> listOrgPermission(String kw);

    List<AsCusEmployee> listEmpPermission(String kw);

    String getExternalId(Long id);
}
