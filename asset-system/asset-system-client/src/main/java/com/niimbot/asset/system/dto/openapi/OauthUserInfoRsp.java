package com.niimbot.asset.system.dto.openapi;

import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/3/4 14:00
 */
@Data
@Accessors(chain = true)
public class OauthUserInfoRsp {

    // 账号Id
    private String accountId;

    // 员工Id
    private String empId;

    // 昵称
    private String nickName;

    // 头像
    private String avatar;

    // 员工姓名
    private String empName;

    // 员工工号
    private String empNo;

    // 员工手机号
    private String mobile;

    // 员工邮箱
    private String email;

    public static OauthUserInfoRsp build(AsCusEmployee employee, AsCusUser account) {
        OauthUserInfoRsp infoRsp = new OauthUserInfoRsp();

        infoRsp.setAccountId(account.getId().toString());
        infoRsp.setNickName(account.getNickname());
        infoRsp.setAvatar(account.getImage());

        infoRsp.setEmpId(employee.getId().toString());
        infoRsp.setEmpName(employee.getEmpName());
        infoRsp.setEmpNo(employee.getEmpNo());
        infoRsp.setMobile(employee.getMobile());
        infoRsp.setEmail(employee.getEmail());

        return infoRsp;
    }

}
