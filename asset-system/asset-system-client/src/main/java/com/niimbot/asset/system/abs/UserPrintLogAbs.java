package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.LastUserPrintLogGetQry;
import com.niimbot.asset.system.dto.PrintErrorAmountGetQry;
import com.niimbot.asset.system.dto.PrintMaterialNameGetQry;
import com.niimbot.asset.system.dto.clientobject.UserPrintLogCO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 资产打印日志
 * 原始接口 {@link com.niimbot.asset.means.service.AsUserPrintLogService}
 *
 * <AUTHOR>
 * @date 2022/5/7 11:15
 */
public interface UserPrintLogAbs {

    /**
     * 获取打印数量
     *
     * @param qry
     * @return
     */
    @GetMapping("getPrintErrorAmount")
    Integer getPrintErrorAmount(@SpringQueryMap PrintErrorAmountGetQry qry);

    /**
     * 获取企业时间段内最后一次打印记录
     * List<AsUserPrintLog> printLogs =
     *                 userPrintLogService.list(
     *                         Wrappers.lambdaQuery(AsUserPrintLog.class)
     *                                 .eq(AsUserPrintLog::getCompanyId, company.getId())
     *                                 .between(AsUserPrintLog::getPrintTime, start, end)
     *                                 .orderByDesc(AsUserPrintLog::getPrintTime)
     *                                 .last("LIMIT 1"));
     *
     * @param qry
     * @return
     */
    @GetMapping("getLastUserPrintLog")
    UserPrintLogCO getLastUserPrintLog(@SpringQueryMap LastUserPrintLogGetQry qry);

    /**
     * 获取材料名称
     *
     * @param qry
     * @return
     */
    @GetMapping("getPrintMaterialName")
    String getPrintMaterialName(@SpringQueryMap PrintMaterialNameGetQry qry);

}
