package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.RepositoryLoadCacheCmd;
import com.niimbot.asset.system.dto.RepositorySaveCmd;

import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 耗材仓库
 * 原始接口 {@link com.niimbot.asset.material.service.AsRepositoryService}
 * <AUTHOR>
 * @date 2022/5/10 17:59
 */
public interface RepositoryAbs {

    /**
     * 保存仓库
     *
     * @param cmd
     */
    @PostMapping("saveRepository")
    void saveRepository(RepositorySaveCmd cmd);

    /**
     * 加载到缓存
     * repositoryService.loadCateCache(ListUtil.of(repository))
     * @param cmd
     * @return
     */
    @PostMapping("loadCacheRepository")
    void loadCacheRepository(RepositoryLoadCacheCmd cmd);

    List<Long> hasPermRepoIds(List<Long> repoIds);
}
