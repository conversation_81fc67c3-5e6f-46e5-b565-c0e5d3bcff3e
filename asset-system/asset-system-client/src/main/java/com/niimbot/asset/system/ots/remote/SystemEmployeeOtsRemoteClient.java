package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemEmployeeOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemEmployeeOtsProcessClient")
@FeignClient(name = "asset-system", url = "{gateway}/system/SystemEmployeeOts/")
public interface SystemEmployeeOtsRemoteClient extends SystemEmployeeOts {
}
