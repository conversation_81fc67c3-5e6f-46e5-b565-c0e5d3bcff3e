package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/13 15:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class FormGetQry extends CommonCommand {
    private Long formId;
    private List<Long> companyIds;
    private Boolean throwException;
}
