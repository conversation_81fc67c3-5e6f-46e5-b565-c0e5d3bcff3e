package com.niimbot.asset.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsEmployeeChange;
import com.niimbot.system.CusEmployeeChangeDto;
import com.niimbot.system.CusEmployeeChangeQueryDto;

/**
 * <p>
 * 员工异动记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface AsEmployeeChangeService extends IService<AsEmployeeChange> {

    IPage<CusEmployeeChangeDto> getPage(CusEmployeeChangeQueryDto query);

    IPage<JSONObject> changeAsset(CusEmployeeChangeQueryDto query);
}
