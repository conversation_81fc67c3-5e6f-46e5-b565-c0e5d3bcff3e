package com.niimbot.asset.system.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.niimbot.asset.system.handle.AsToolboxListTypeHandler;
import com.niimbot.material.MaterialHeadDto;
import com.niimbot.means.AssetHeadDto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户自定义设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsCusUserSetting对象", description = "员工自定义设置表")
@TableName(value = "as_cus_employee_setting", autoResultMap = true)
public class AsCusEmployeeSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户账号Id")
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;

    @ApiModelProperty(value = "APP首页常用工具箱")
    @TableField(typeHandler = AsToolboxListTypeHandler.class)
    private List<AsToolbox> appToolbox = Collections.emptyList();

    @ApiModelProperty(value = "PC首页常用工具箱")
    @TableField(typeHandler = AsToolboxListTypeHandler.class)
    private List<AsToolbox> pcToolbox = Collections.emptyList();

    @ApiModelProperty(value = "PC端首页布局")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> pcHome = Collections.emptyList();

    @ApiModelProperty(value = "用户自定义设置资产表头")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<AssetHeadDto> assetHead = Collections.emptyList();

    @ApiModelProperty(value = "用户自定义设置耗材表头")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<MaterialHeadDto> materialHead = Collections.emptyList();

    @ApiModelProperty(value = "过滤库存为0的耗材")
    private Boolean filterZeroStock = false;

    @ApiModelProperty(value = "过滤没入库的耗材")
    private Boolean filterNullStock = false;

    @ApiModelProperty(value = "用户自定义设置资产表头")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> assetExportField = Collections.emptyList();

    @ApiModelProperty(value = "用户自定义设置资产搜索信息")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> assetSearchField;

    @ApiModelProperty(value = "用户自定义设置耗材表头")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> materialExportField = Collections.emptyList();

    @ApiModelProperty(value = "用户自定义设置耗材搜索信息")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> materialSearchField;

    @ApiModelProperty(value = "单据打印默认模板")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject orderPrintDefaultTpl;

    @ApiModelProperty(value = "是否极简模式")
    private Boolean assetSimplify;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;


}
