package com.niimbot.asset.system.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.system.SensitiveFieldItemDto;
import com.niimbot.asset.system.handle.SensitiveTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_sensitive_field_config
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_sensitive_field_config", autoResultMap = true)
public class AsSensitiveFieldConfig implements Serializable {

    private static final long serialVersionUID = -92387412649673836L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 公司ID（租户ID）0-所有租户
     */
    private Long companyId;

    /**
     * 敏感字段
     */
    @TableField(typeHandler = SensitiveTypeHandler.class)
    private List<SensitiveFieldItemDto> sensitiveField;

    /**
     * 数据描述
     */
    private String dataDesc;

    /**
     * 是否删除 0-否  1-是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}