package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.AssetLogSaveBatchCmd;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 原始接口 {@link com.niimbot.asset.means.service.AsAssetLogService}
 * <AUTHOR>
 * @date 2022/5/11 15:01
 */
public interface AssetLogAbs {
    /**
     * 批量保存
     *
     * @param cmd
     * @return
     */
    @PostMapping("saveBatchAssetLog")
    Boolean saveBatchAssetLog(AssetLogSaveBatchCmd cmd);
}
