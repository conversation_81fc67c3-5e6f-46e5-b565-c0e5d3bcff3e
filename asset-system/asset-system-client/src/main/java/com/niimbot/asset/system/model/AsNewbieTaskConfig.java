package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.system.handle.NewbieTaskButtonListHandler;
import com.niimbot.system.NewbieTaskButton;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_newbie_task_config", autoResultMap = true)
public class AsNewbieTaskConfig implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty("行动按钮组")
    @TableField(typeHandler = NewbieTaskButtonListHandler.class)
    private List<NewbieTaskButton> buttons;

    @ApiModelProperty("业务表名")
    private String tableName;

    @ApiModelProperty("排序字段")
    private Integer sort;

    @ApiModelProperty("软删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
