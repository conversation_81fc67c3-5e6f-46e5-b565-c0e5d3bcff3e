package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsAuditLog", description = "系统审计日志")
@TableName(value = "as_audit_log", autoResultMap = true)
public class AsAuditLog {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String url;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private Object params;

    private String terminalCode;

    private String terminalText;

    @TenantFilterColumn
    private Long companyId;

    private String operator;

    private Long orgId;

    private String orgName;

    private Long roleId;

    private String roleName;

    private String actionCode;

    private String actionText;

    private String content;

    private Long createBy;

    private LocalDateTime createTime;
}
