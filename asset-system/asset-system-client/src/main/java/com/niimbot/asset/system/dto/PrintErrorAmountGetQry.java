package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/5/7 11:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class PrintErrorAmountGetQry extends CommonCommand {
    /**
     * 企业id
     */
    private Long companyId;
    /**
     * 打印类型
     */
    private Integer printType;
    /**
     * 打印状态
     */
    private Integer printStatus;
    /**
     * 开始时间
     */
    private LocalDateTime start;
    /**
     * 结束时间
     */
    private LocalDateTime end;
}
