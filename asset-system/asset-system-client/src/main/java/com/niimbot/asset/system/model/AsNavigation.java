package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 导航栏表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsNavigation对象", description = "导航栏表")
@TableName(value = "as_navigation")
public class AsNavigation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导航栏ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "功能名称")
    private String navName;

    @ApiModelProperty(value = "导航栏跳转链接")
    private String navUrl;

    @ApiModelProperty(value = "类型 1-页面 2-弹窗")
    private Short navType;

    @ApiModelProperty(value = "导航状态（1正常 2禁用）")
    private Short status;

    @ApiModelProperty(value = "显示名称")
    private String showName;

    @ApiModelProperty(value = "位置 1-顶部横栏 2-下拉框")
    private Short position;

    @ApiModelProperty(value = "排序")
    private Integer sortNum;

    @ApiModelProperty(value = "图标")
    private String navIcon;

    @ApiModelProperty(value = "说明")
    private String remark;


}
