package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 服务版本商品
 *
 * <AUTHOR>
 * @date 2021/10/26 11:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class VersionItemCO extends ClientObject {

    /**
     * 版本id(商品id)
     */
    private Long versionId;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本定价(月单价)
     */
    private Integer versionPrice;

    /**
     * 版本活动类型: 1-限时折扣, 2-限时优惠, 3-限时赠送
     */
    private Integer versionActivityType;

    /**
     * 购买时长(单位: 年)
     */
    private Integer buyDuration;

    /**
     * 赠送时长(单位: 月)
     */
    private Integer presentedDuration;
}
