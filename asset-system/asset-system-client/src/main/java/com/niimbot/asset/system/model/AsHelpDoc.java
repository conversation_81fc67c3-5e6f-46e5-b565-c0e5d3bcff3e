package com.niimbot.asset.system.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_help_doc
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class AsHelpDoc implements Serializable {

    private static final long serialVersionUID = 6221337508506583937L;

    /**
     * Id
     */
    private Long id;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 文档外部链接地址
     */
    private String path;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}