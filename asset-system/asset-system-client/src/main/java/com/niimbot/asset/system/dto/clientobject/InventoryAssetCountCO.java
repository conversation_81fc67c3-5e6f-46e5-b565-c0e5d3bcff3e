package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/7 10:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class InventoryAssetCountCO extends ClientObject {
    /**
     * 未盘
     */
    private long noCheckedNum;

    /**
     * 已盘
     */
    private long checkedNum;

    /**
     * 盘盈
     */
    private long checkedAddNum;
}
