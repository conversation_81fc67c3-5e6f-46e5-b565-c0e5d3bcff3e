package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.dto.clientobject.AssetCategoryCO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11 11:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetCategoryLoadCacheCmd extends CommonCommand {
    @NotEmpty
    private List<AssetCategoryCO> categories;
}
