package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsCompanyPasswordSetting对象", description = "企业用户密码配置表")
@TableName(value = "as_company_password_setting")
public class AsCompanyPasswordSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "账号id（员工Id）")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("企业ID")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "企业密码最低位数")
    private Integer passwordLowestDigit;

    @ApiModelProperty(value = "企业密码最高位数")
    private Integer passwordHighDigit;

    @ApiModelProperty(value = "密码限制规则 1:大写字母 2:小写字母 3:数字 4:符号")
    private String passwordLimit;

    @ApiModelProperty(value = "是否强制更新: 1-是, 0-否")
    private Boolean forceUpdate;

    @ApiModelProperty(value = "更新月份")
    private Integer updateMonth;

    @ApiModelProperty(value = "更新天数")
    private Integer updateDay;

    @ApiModelProperty(value = "密码其他限制规则 1:不得包含重复3位数字 2:不得包含3位重复字母 3:不得输入常见弱密码")
    private String otherLimit;

    @ApiModelProperty(value = "企业密码配置开关 1开启 0关闭")
    private Boolean passwordSwitch;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
