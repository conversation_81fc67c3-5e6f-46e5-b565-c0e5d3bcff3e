package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemDataSnapshotOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemDataSnapshotOtsProcessClient")
@FeignClient(name = "asset-system", url = "{gateway}/system/SystemDataSnapshotOts/")
public interface SystemDataSnapshotOtsRemoteClient extends SystemDataSnapshotOts {
}
