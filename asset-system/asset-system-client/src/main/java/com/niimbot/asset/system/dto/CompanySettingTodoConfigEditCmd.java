package com.niimbot.asset.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class CompanySettingTodoConfigEditCmd extends CommonCommand {

    private Long companyId;

    private Integer timeoutLowerLimit;

    private Integer timeoutUpperLimit;

}
