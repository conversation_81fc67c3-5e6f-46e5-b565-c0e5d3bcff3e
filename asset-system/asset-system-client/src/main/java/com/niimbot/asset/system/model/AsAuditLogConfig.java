package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_audit_log_config", autoResultMap = true)
public class AsAuditLogConfig {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String module;

    private String submodule;

    private String actionCode;

    private String actionText;

    private String contentTpl;

}
