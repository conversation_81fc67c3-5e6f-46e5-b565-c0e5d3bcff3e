package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCompanyChannel;
import com.niimbot.asset.system.model.AsPromoteChannel;
import com.niimbot.asset.system.model.AsPromoteChannelSource;
import com.niimbot.system.CompanyChannelInfo;

/**
 * <p>
 * 企业CRM信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface AsCompanyChannelService extends IService<AsCompanyChannel> {

    /**
     * 查询渠道
     *
     * @param code
     * @return
     */
    AsPromoteChannel getPromoteChannelByCode(Long code);

    /**
     * 查询渠道来源
     *
     * @param code
     * @return
     */
    AsPromoteChannelSource getAsPromoteChannelSourceByCode(Long code);

    CompanyChannelInfo getCompanyChannelInfo(Long companyId);
}
