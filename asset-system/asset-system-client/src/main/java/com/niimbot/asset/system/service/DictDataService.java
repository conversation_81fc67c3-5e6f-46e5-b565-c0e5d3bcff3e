package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsDictData;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/9 8:44
 */
public interface DictDataService extends IService<AsDictData> {

    /**
     * 根据分类查询所有的字典
     * @param dictType
     * @return
     */
    List<AsDictData> selectDictDataByType(String dictType);
}
