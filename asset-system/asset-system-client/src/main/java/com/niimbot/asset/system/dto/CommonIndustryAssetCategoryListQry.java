package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/10 18:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class CommonIndustryAssetCategoryListQry extends CommonCommand {
    /**
     * 公共行业id
     */
    private Long commonIndustryId;
    /**
     * 企业id
     */
    private Long companyId;
}
