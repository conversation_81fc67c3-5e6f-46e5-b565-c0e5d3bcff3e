package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.AssetDeleteAmountGetQry;
import com.niimbot.asset.system.dto.AssetExcludeTestAmountGetQry;
import com.niimbot.asset.system.dto.AssetManageTransferCmd;
import com.niimbot.asset.system.dto.AssetTransferCmd;
import com.niimbot.asset.system.dto.AssetUpdateBatchCmd;
import com.niimbot.asset.system.dto.AssetUseTransferCmd;
import com.niimbot.asset.system.dto.TagAttrListQry;
import com.niimbot.asset.system.dto.clientobject.AssetLogCO;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.system.CusEmployeeTransferDto;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 资产
 * 原始接口 {@link com.niimbot.asset.means.service.AssetService}
 *
 * <AUTHOR>
 * @date 2022/5/7 10:56
 */
public interface AssetAbs {

    /**
     * 资产转移
     * assetService.editAssetUseOrg(employeeChange.getId(), userId, companyId, transfers, orgList)
     *
     * @param cmd
     * @return
     */
    @PostMapping("assetTransfer")
    Boolean assetTransfer(AssetTransferCmd cmd);

    /**
     * 资产转移
     * assetService.assetManageTransfer(employeeId, employ.getManageAsset(), changeId);
     *
     * @param cmd
     * @return
     */
    @PostMapping("assetManageTransfer")
    List<AssetLogCO> assetManageTransfer(AssetManageTransferCmd cmd);

    /**
     * 资产转移
     * assetService.assetUseTransfer(employeeId, employ.getUseAsset(), changeId);
     *
     * @param cmd
     * @return
     */
    @PostMapping("assetUseTransfer")
    List<AssetLogCO> assetUseTransfer(AssetUseTransferCmd cmd);

    /**
     * 获取资产数量(排除测试企业)
     * assetService.countAssetExcludeTestCompanyByTime(beginTime, endTime)
     *
     * @param qry
     * @return
     */
    @GetMapping("getAssetExcludeTestAmount")
    Integer getAssetExcludeTestAmount(@SpringQueryMap AssetExcludeTestAmountGetQry qry);

    /**
     * 获取删除资产数量
     * assetService.countDeleteNum(beginTime, endTime);
     *
     * @param qry
     * @return
     */
    @GetMapping("getAssetDeleteAmount")
    Integer getAssetDeleteAmount(@SpringQueryMap AssetDeleteAmountGetQry qry);

    @GetMapping("getAttrList")
    TagAttrListCO getAttrList(@SpringQueryMap TagAttrListQry qry);

    Boolean editAssetUseOrg(Long changeId, Long userId, List<CusEmployeeTransferDto> transfers, List<AsOrg> orgList);

    Boolean updateBatchAsset(AssetUpdateBatchCmd cmd);
}
