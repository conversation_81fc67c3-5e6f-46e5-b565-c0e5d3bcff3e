package com.niimbot.asset.system.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_toolbox_statistics
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class AsToolboxStatistics implements Serializable {

    private static final long serialVersionUID = 3592538286940990303L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 公司ID（租户ID）
     */
    private Long companyId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 单据类型
     */
    private Long orderType;

    /**
     * 统计
     */
    private Long count;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}