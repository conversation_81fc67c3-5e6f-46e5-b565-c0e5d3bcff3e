package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 文件上传记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsFile对象", description = "文件上传记录表")
@TableName(value = "as_file")
public class AsFile implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "原始文件名")
    private String originalName;

    @ApiModelProperty(value = "存储文件名")
    private String persistName;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件存储路径")
    private String filePath;

    @ApiModelProperty(value = "文件相对路径")
    private String relativePath;

    @ApiModelProperty(value = "业务操作类型")
    private String optType;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
//    @UserFilterColumn
    private Long createBy;

    @ApiModelProperty(value = "公司ID（租户ID） 默认0为系统配置分类")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;
}
