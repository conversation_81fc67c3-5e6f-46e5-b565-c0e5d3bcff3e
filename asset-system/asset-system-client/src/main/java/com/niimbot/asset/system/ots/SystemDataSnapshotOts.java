package com.niimbot.asset.system.ots;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.system.dto.DataSnapshotGetQry;
import com.niimbot.system.DataSnapshotSave;
import com.niimbot.asset.system.dto.DataSourceIdsGetQry;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemDataSnapshotOts {

    @PostMapping("/saveSnapshot")
    void saveSnapshot(@RequestBody DataSnapshotSave cmd);

    @PostMapping("/getSourceIds")
    List<Long> getSourceIds(@RequestBody DataSourceIdsGetQry qry);

    @PostMapping("/getSnapshotIds")
    List<String> getSnapshotIds(@RequestBody DataSnapshotGetQry qry);

    @PostMapping("/getSnapshots")
    List<JSONObject> getSnapshots(@RequestBody DataSnapshotGetQry qry);

}
