package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/27 15:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "as_recommend_record", autoResultMap = true)
public class AsRecommendRecord {

    // 推荐id
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    // 推荐人公司ID
    private Long recommendCompanyId;

    // 推荐人员工ID
    private Long recommendEmpId;

    // 推荐状态（1-已注册，2-已签单，3-已失效）
    private Integer status;

    // 被推荐人企业ID
    private Long registerCompanyId;

    // 被推荐人企业名称
    private String registerCompanyName;

    // 手机区号
    private String registerNationalCode;

    // 被推荐人手机号
    private String registerMobile;

    // 注册时间
    private LocalDateTime registerTime;

    private Long saleOrderId;

    // 订单金额
    private BigDecimal tradeMoney;

    // 下单时间
    private LocalDateTime tradeTime;

    // 发放状态（1-待发放，2-已发放）
    private Integer awardStatus;

}
