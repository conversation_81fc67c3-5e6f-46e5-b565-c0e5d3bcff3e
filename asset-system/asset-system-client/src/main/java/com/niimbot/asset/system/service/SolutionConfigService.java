package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.model.AsSolutionConfig;
import com.niimbot.system.SolutionConfigDto;
import com.niimbot.system.SolutionDetailDto;
import com.niimbot.system.SolutionQueryDto;

/**
 * <AUTHOR>
 * @date 2023/3/16 上午10:41
 */
public interface SolutionConfigService extends IService<AsSolutionConfig> {

    /**
     * 分页查询
     * @param queryDto
     * @return
     */
    PageUtils<SolutionConfigDto> pageQuery(SolutionQueryDto queryDto);

    /**
     * 解决方案配置详情
     * @param configId
     * @return
     */
    SolutionDetailDto queryDetail(Long configId);
}
