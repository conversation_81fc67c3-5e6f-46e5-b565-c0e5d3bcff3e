package com.niimbot.asset.system.service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/12/6 15:22
 */
public interface UserPreviewDeleteService {

    Set<Long> checkUserAsset(List<Long> removeUserIds, Long companyId);

    boolean hasUseAsset(Long removeUserId, Long companyId);

    boolean hasManageAsset(Long removeUserId, Long companyId);

    boolean hasApproval(Long removeUserId, Long companyId);

    boolean hasEntMatTask(Long removeUserId, Long companyId);

    boolean notAllowRemove(Long removeUserId, Long companyId);

}
