package com.niimbot.asset.system.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * as_industry_case_config
 * <AUTHOR>
@Data
@TableName(value = "as_industry_case_config", autoResultMap = true)
public class AsIndustryCaseConfig implements Serializable {

    private static final long serialVersionUID = 912413317561671426L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 案例标题
     */
    private String title;

    /**
     * 关键字
     */
    private String kw;

    /**
     * 封面图片
     */
    private String images;

    /**
     * 内容类型
     */
    private Integer contentType;

    /**
     * 案例详情
     */
    private String content;

    /**
     * 浏览量
     */
    private Long views;

    /**
     * 是否删除 0-否  1-是
     */
    private Integer isDelete;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}