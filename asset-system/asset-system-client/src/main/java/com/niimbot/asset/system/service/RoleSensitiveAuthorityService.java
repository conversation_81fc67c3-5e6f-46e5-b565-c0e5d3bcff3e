package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsRoleSensitiveAuthority;
import com.niimbot.system.SensitivePermissionDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/21 下午4:31
 */
public interface RoleSensitiveAuthorityService extends IService<AsRoleSensitiveAuthority> {

    /**
     * 根据角色获取敏感数据对象权限
     * @param roleId
     * @return
     */
    List<SensitivePermissionDto> getByRoleId(Long roleId);

    /**
     * 批量删除用户敏感数据权限
     * @param roleIds
     */
    void removeByUserIds(List<Long> roleIds);

    /**
     * 保存角色敏感数据权限
     * @param roleId
     * @param sensitivePermissionDto
     * @return
     */
    Boolean savePermission(Long roleId, List<SensitivePermissionDto> sensitivePermissionDto);
}
