package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/10 18:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetAreaCO extends ClientObject {
    /**
     * 区域ID
     */
    private Long id;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 区域CODE
     */
    private String areaCode;

    /**
     * 租户ID
     */
    private Long companyId;

    /**
     * 组织id(公司)
     */
    private Long orgId;

    /**
     * 节点
     */
    private Long pid;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 路径paths
     */
    private String paths;

    /**
     * 排序序号
     */
    private Integer sortNum;

    /**
     * 备注
     */
    private String remark;
}
