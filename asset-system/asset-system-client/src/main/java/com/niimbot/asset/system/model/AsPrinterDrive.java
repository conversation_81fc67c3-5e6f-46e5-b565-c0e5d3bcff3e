package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_printer_drive", autoResultMap = true)
@ApiModel(value = "AsPrinterDrive", description = "打印机驱动")
public class AsPrinterDrive implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("打印机系列ID")
    private Long printerSeriesId;

    @ApiModelProperty("打印驱动版本号")
    private Long version;

    @ApiModelProperty("打印驱动更新方式")
    private Integer mode;

    @ApiModelProperty("打印驱动包地址")
    private String driveUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public static Long vToLong(List<Long> version) {
        if (version.size() > 4) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前仅支持4位版本号");
        }
        return (version.get(0) << 24) + (version.get(1) << 16) + (version.get(2) << 8) + version.get(3);
    }

    public static String vToString(Long version) {
        // 直接右移24位
        return (version >>> 24) +
                "." +
                ((version & 0x00FFFFFF) >>> 16) +
                "." +
                ((version & 0x0000FFFF) >>> 8) +
                "." +
                ((version & 0x000000FF))
                ;
    }
}
