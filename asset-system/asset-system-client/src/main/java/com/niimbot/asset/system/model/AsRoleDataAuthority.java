package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * as_role_data_authority
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_role_data_authority", autoResultMap = true)
public class AsRoleDataAuthority {

    /**
     * 权限ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 角色编码
     */
    private Long roleId;

    /**
     * 数据编码
     */
    private String authorityDataCode;

    /**
     * 权限类型
     */
    private String authorityCode;

    /**
     * 权限类型
     */
    private Integer authorityType;

    /**
     * 权限数据
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<Object> authorityData;

    /**
     * 权限分组
     */
    private Integer authorityGroup;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}