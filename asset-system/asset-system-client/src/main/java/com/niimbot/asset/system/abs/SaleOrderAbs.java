package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.SaleOrderGetQry;
import com.niimbot.asset.system.dto.clientobject.SaleOrderCO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 销售单
 * 原始接口
 * {@link com.niimbot.asset.sale.service.AsSaleOrderService}
 * {@link com.niimbot.asset.sale.service.AsSaleOrderItemService}
 *
 * <AUTHOR>
 * @date 2022/5/6 17:15
 */
public interface SaleOrderAbs {

    /**
     * 销售单详情查询
     *
     * @param qry
     * @return
     */
    @GetMapping("getSaleOrder")
    SaleOrderCO getSaleOrder(@SpringQueryMap SaleOrderGetQry qry);

}
