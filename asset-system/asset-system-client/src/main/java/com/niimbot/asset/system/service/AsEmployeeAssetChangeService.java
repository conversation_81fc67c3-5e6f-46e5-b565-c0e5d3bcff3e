package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsEmployeeAssetChange;
import com.niimbot.system.CusEmployeeChangeQueryDto;

/**
 * <p>
 * 员工资产异动记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface AsEmployeeAssetChangeService extends IService<AsEmployeeAssetChange> {

    IPage<AsEmployeeAssetChange> selectPage(CusEmployeeChangeQueryDto query);

}
