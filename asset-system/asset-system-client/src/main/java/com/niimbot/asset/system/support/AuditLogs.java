package com.niimbot.asset.system.support;

import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.asset.system.ots.SystemAuditLogOts;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Component
public class AuditLogs implements ApplicationContextAware {

    private static SystemAuditLogOts systemAuditLogService;

    private static final ThreadPoolExecutor EXECUTOR = AssetThreadPoolExecutorManager.newThreadPool("audit-log", 5, 10, 10000);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        systemAuditLogService = applicationContext.getBean(SystemAuditLogOts.class);
    }

    public static void record(AuditLogRecord record) {
        EXECUTOR.execute(() -> systemAuditLogService.record(record));
    }

}
