package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 发票信息
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class InvoiceInfoCO extends ClientObject {

    /**
     * 发票信息id
     */
    private Long id;

    /**
     * 发票类型：1-个人；2-普通；3：增值税
     */
    private Integer type;

    /**
     * 发票抬头
     */
    private String title;

    /**
     * 税号
     */
    private String taxNum;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String phone;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 银行账号
     */
    private String bankAccount;
}
