package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.system.handle.UserTagListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.system.TagAttrDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/17 10:04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AsUserTag对象", description = "用户标签模板表")
@TableName(value = "as_user_tag", autoResultMap = true)
public class AsUserTag implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "模板类型 1-系统默认模板  2-用户自定义模板")
    private Integer tagType;

    @ApiModelProperty(value = "打印类型 1-资产  2-耗材")
    private Short printType;

    @ApiModelProperty(value = "公司ID（租户ID）")
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "尺寸")
    private Long sizeId;

    @ApiModelProperty(value = "尺寸父id")
    private Long sizePid;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "模板图片链接")
    private String tagUrl;

    @ApiModelProperty("模板背景图片链接")
    private String backgroundImage;

    @ApiModelProperty(value = "标准品Id")
    private Long standardId;

    @ApiModelProperty(value = "二维码的值 资产id-assetId 资产编码-assetCode 等")
    private String codeVal;

    @ApiModelProperty(value = "二维码是否为自定义文案 0-否  1-是")
    private Boolean isCustomWords;

    @ApiModelProperty("适用于RFID")
    private Boolean applyRfid;

    @ApiModelProperty(value = "标签属性配置-JSON数据样式")
    @TableField(typeHandler = UserTagListTypeHandler.class)
    private List<TagAttrDto> attrData;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;
}
