package com.niimbot.asset.system.model;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 钉钉消息规则扩展配置
 *
 * <AUTHOR>
 * @date 2022/1/14 14:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsDingtalkMessageRuleConfig", description = "钉钉消息规则扩展配置")
public class AsDingtalkMessageRuleConfig {
    private String url;
    private String sign;
}
