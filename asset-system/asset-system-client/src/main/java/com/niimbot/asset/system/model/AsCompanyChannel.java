package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业CRM信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsCompanyChannel对象", description="企业CRM信息")
public class AsCompanyChannel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业Id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "渠道id")
    private Long channelCode;

    @ApiModelProperty(value = "来源id")
    private Long sourceCode;

    @ApiModelProperty(value = "线索同步状态(0-待同步, 1-已同步, 2-同步失败)")
    private Integer crmClueSyncStatus;

    @ApiModelProperty(value = "线索同步时间")
    private LocalDateTime crmClueSyncTime;

    @ApiModelProperty(value = "线索id")
    private String crmClueId;

    @ApiModelProperty(value = "客户同步状态(0-待同步, 1-已同步, 2-同步失败)")
    private Integer crmCusSyncStatus;

    @ApiModelProperty(value = "客户同步时间")
    private LocalDateTime crmCusSyncTime;

    @ApiModelProperty(value = "客户id")
    private String crmCusId;

    @ApiModelProperty(value = "业务数据同步时间")
    private LocalDateTime crmBusSyncTime;

    @ApiModelProperty(value = "业务数据id")
    private String crmBusId;
}
