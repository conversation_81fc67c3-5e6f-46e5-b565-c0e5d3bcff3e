package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCusMenuApp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CusMenuAppService extends IService<AsCusMenuApp> {
    Boolean addMenu(AsCusMenuApp menu);

    Boolean updateMenu(AsCusMenuApp menu);

    Boolean deleteList(List<Long> ids);

    Boolean sort(List<Long> ids);
}
