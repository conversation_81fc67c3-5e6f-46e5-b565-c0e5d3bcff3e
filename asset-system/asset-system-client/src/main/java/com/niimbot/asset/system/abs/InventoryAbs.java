package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.InventoryAssetCountGetQry;
import com.niimbot.asset.system.dto.InventoryGetQry;
import com.niimbot.asset.system.dto.clientobject.InventoryAssetCountCO;
import com.niimbot.asset.system.dto.clientobject.InventoryCO;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 盘点单
 * 原始接口
 * {@link com.niimbot.asset.inventory.service.AsInventoryService}
 * {@link com.niimbot.asset.inventory.service.AsInventoryAssetService}
 *
 * <AUTHOR>
 * @date 2022/5/7 09:45
 */
public interface InventoryAbs {
    /**
     * 盘点单查询
     *
     * @param qry
     * @return
     */
    @GetMapping("getInventory")
    InventoryCO getInventory(@SpringQueryMap InventoryGetQry qry);

    /**
     * 盘点单资产数量
     *
     * @param qry
     * @return
     */
    @GetMapping("getInventoryAssetCount")
    InventoryAssetCountCO getInventoryAssetCount(@SpringQueryMap InventoryAssetCountGetQry qry);

    /**
     * 获取非完成终止的盘点单
     * @param assetId
     * @return
     */
    Integer getProcessInventory(Long assetId);

}
