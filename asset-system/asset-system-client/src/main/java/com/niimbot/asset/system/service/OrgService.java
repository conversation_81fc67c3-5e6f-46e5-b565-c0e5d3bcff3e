package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.framework.service.CommonCodeService;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.EditRootOrg;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgExportDto;
import com.niimbot.system.OrgImportDto;
import com.niimbot.system.OrgQueryDto;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrgService extends IService<AsOrg>, CommonCodeService {

    /**
     * 写入部门缓存
     *
     * @param orgList 组织列表
     */
    void loadOrgCache(List<AsOrg> orgList);

    /**
     * 查询企业组织【员工数量】
     *
     * @param queryDto 组织查询
     * @return 组织
     */
//    List<OrgDto> countEmpList(OrgQueryDto queryDto);

    /**
     * 添加组织
     *
     * @param org 组织
     * @return 是否成功
     */
    Boolean add(AsOrg org);

    String addV2(AsOrg org);

    /**
     * 修改组织结构
     *
     * @param org 组织
     * @return 是否成功
     */
    Boolean edit(AsOrg org);

    Boolean editRootOrg(EditRootOrg org);

    /**
     * 根据parentId获取所有子孙id集合
     *
     * @param permsIds 具有权限的组织id
     * @param orgList  所有组织
     * @param parentId 父id
     * @param result   结果
     */
//    void getChildIdsByParentId(List<Long> permsIds, List<AsOrg> orgList, Long parentId, List<Long> result);

    /**
     * 分页查询组织
     *
     * @return 组织列表
     */
    IPage<OrgDto> orgPage(OrgQueryDto queryDto);

    /**
     * 查询组织列表
     *
     * @return 组织列表
     */
    List<OrgDto> orgList(OrgQueryDto queryDto);

    /**
     * 传入指定的组织Id，过滤有权限的数据
     *
     * @return
     */
    List<Long> hasPermOrgIds(List<Long> orgIds, Integer orgType);

    List<OrgExportDto> getExcelData(OrgQueryDto queryDto);

    List<List<LuckySheetModel>> importError(Long taskId);

    void saveSheetHead(HeadImportErrorDto importErrorDto);

    Boolean saveSheetData(OrgImportDto importDto);

    /**
     * 删除
     *
     * @param ids Id
     * @return 是否成功
     */
    Boolean remove(@RequestBody List<Long> ids);

    Boolean sort(List<Long> orgIds);

    List<OrgDto> listByDirector(Long userId, Long companyId);

    List<OrgDto> listByDirectors(List<Long> userIds, Long companyId);

    /**
     * 查询包括已删除的数据
     *
     * @param orgIds 组织ID
     * @return
     */
    List<AsOrg> listAllByIds(List<Long> orgIds);

    /**
     * 查询包括已删除的数据
     *
     * @param orgIds 组织ID
     * @return
     */
    List<AsOrg> listAllByIds(List<Long> orgIds, Long companyId);

//    List<OrgDto> countPermsEmpList(OrgQueryDto queryDto);

    List<OrgDto> areaPermsList();

    List<OrgDto> storePermsList();

    Long getOrgCompanyOwner(@Param("ids") List<Long> ids);

    AsOrg getRootOrg(Long companyId);

    List<AsOrg> getAll(Long companyId);

    Map<String, Long> getExternalMapping(Long companyId, List<String> externalIds);

    String getExternalId(Long id);

    Map<Long, List<AsOrg>> listOrgByEmpIds(List<Long> empIds);

    // 通过权限查询用户可见的部门简单集合（仅包含id，name）
    List<DictDataDto> listSimpleWithPerms(AsDataAuthority authority, Long companyId);

    /**
     * 内部服务，通过code或者name查询
     *
     * @return
     */
    Long getOne(String orgName, String orgCode);

    String getMaxCodeByCompanyId(Long companyId);


}