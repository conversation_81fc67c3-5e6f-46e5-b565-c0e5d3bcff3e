package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.model.AsIndustryCaseConfig;
import com.niimbot.system.IndustryCaseConfigDto;
import com.niimbot.system.IndustryCaseDetailDto;
import com.niimbot.system.IndustryCaseQueryDto;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午5:01
 */
public interface IndustryCaseConfigService extends IService<AsIndustryCaseConfig> {

    /**
     * 分页查询
     * @param queryDto
     * @return
     */
    PageUtils<IndustryCaseConfigDto> pageQuery(IndustryCaseQueryDto queryDto);

    /**
     * 解决方案配置详情
     * @param configId
     * @return
     */
    IndustryCaseDetailDto queryDetail(Long configId);
}
