package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SerialNumberOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SerialNumberOtsProcessClient")
@FeignClient(name = "asset-system", url = "https://{gateway}/system/SerialNumberOts/")
public interface SerialNumberOtsRemoteClient extends SerialNumberOts {
}
