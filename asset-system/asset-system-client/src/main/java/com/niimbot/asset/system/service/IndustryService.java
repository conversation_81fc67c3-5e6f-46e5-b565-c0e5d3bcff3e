package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsIndustry;

import java.util.List;

/**
 * 行业信息接口
 *
 * <AUTHOR>
 * @Date 2020/11/2
 */
public interface IndustryService extends IService<AsIndustry> {

    Boolean add(AsIndustry industry);

    Boolean edit(AsIndustry industry);

    Boolean delete(List<Long> industryIds);

    Boolean sort(List<Long> industryIds);
}
