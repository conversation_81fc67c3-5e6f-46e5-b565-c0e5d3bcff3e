package com.niimbot.asset.system.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/7 11:54
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class PrintMaterialNameGetQry extends CommonCommand {
    /**
     * 打印标签ID
     */
    private Long tagId;

    /**
     * 材质ID
     */
    private Long tagMaterialId;
}
