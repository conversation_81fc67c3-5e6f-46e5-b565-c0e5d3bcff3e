package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/5/7 11:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class UserPrintLogCO extends ClientObject {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 打印任务ID
     */
    private Long printTaskId;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 打印机完整序列号
     */
    private String printerSerialNo;

    /**
     * 打印标签ID
     */
    private Long tagId;

    /**
     * 标签类型 0-非RFID  1-RFID
     */
    private Boolean tagType;

    /**
     * 材质ID
     */
    private Long tagMaterialId;

    /**
     * 打印浓度
     */
    private Integer printConcentration;

    /**
     * 耗材code
     */
    private String consumerCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 资产ID
     */
    private Long assetId;

    /**
     * 打印终端 1-pc端  2-Android端  3-iOS端
     */
    private Short printSource;

    /**
     * RFID-TID 抗金属签发卡
     */
    private String labelTid;

    /**
     * 打印类型 1-资产  2-耗材
     */
    private Short printType;

    /**
     * 打印结果 1-成功 2-失败
     */
    private Short printStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 打印时间
     */
    private LocalDateTime printTime;

    /**
     * 虚拟键，资产名称
     */
    private String assetName;

    /**
     * 虚拟键，资产编码
     */
    private String assetCode;

    /**
     * 虚拟键，资产分类
     */
    private String assetCategory;

    /**
     * 虚拟键，使用人
     */
    private String usePerson;

    /**
     * 虚拟键，存放区域
     */
    private String storageArea;

    /**
     * 虚拟键，所属组织
     */
    private String orgOwner;

    /**
     * 虚拟键，使用组织
     */
    private String useOrg;

    /**
     * 打印机硬件版本
     */
    private String hardwareVersion;

    /**
     * SDK版本
     */
    private String sdkVersion;

    /**
     * 固件版本号
     */
    private String firmwareVersion;

    /**
     * 终端品牌
     */
    private String terminalBrand;

    /**
     * 终端型号
     */
    private String terminalType;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 资产快照数据
     */
    private JSONObject assetData;
}
