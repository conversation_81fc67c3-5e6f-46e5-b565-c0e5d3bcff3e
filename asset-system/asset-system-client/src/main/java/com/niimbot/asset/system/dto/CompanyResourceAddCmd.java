package com.niimbot.asset.system.dto;

import com.niimbot.asset.system.dto.clientobject.CompanyResourceCO;

import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/8 15:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class CompanyResourceAddCmd extends CommonCommand {
    @NotNull
    private CompanyResourceCO companyResource;
}
