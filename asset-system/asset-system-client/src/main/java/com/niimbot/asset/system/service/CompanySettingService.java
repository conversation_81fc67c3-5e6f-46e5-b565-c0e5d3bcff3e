package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.CompanySwitch;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/12/8 17:00
 */
public interface CompanySettingService extends IService<AsCompanySetting> {

    String SETTING_CACHE_KEY = "company_setting:";
    String SWITCH_CACHE_KEY = ":switch";

    default String switchCacheKey(Long id) {
        return SETTING_CACHE_KEY + id + SWITCH_CACHE_KEY;
    }

    String getBizCodeForGenSerialNo(Long id, String type, Long companyId);

    Long getTreePrimaryNodeId(Long id, String type);

    String getMaxCode(String prefix, String escapePrefix, Long companyId,
                      String type, Integer serialLen, String fieldCode);

    CompanySwitch getSwitchSettingWithCache(Long id);

    Boolean enableIdleAsset(Long companyId, Boolean enabled);

    Boolean configDefaultDataAuth(Long companyId, List<AsDataAuthority> dataAuthorities);

    Boolean enableSyncRole(Long companyId, Boolean enabled);
}
