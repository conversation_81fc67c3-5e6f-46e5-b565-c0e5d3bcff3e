package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/10 18:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AssetCategoryCO extends ClientObject {

    /**
     * ID
     */
    private Long id;

    /**
     * 自定义分类名称
     */
    private String categoryName;

    /**
     * 自定义分类编码
     */
    private String categoryCode;

    /**
     * 行业ID
     */
    private Long industryId;

    /**
     * 公司ID（租户ID） 默认0为系统配置分类
     */
    private Long companyId;

    /**
     * 运营后台分类ID
     */
    private Long sourceId;

    /**
     * 父级分类
     */
    private Long pid;

    /**
     * 路径
     */
    private String paths;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 排序序号
     */
    private Integer sortNum;

    /**
     * 创建者
     */
    private Long createBy;
}
