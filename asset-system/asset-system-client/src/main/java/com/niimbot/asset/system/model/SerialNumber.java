package com.niimbot.asset.system.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SerialNumber implements Serializable {

    private String type;

    private String value;

    @JsonIgnore
    private Long companyId;

    public SerialNumber(String type, String value) {
        this.type = type;
        this.value = value;
    }
}
