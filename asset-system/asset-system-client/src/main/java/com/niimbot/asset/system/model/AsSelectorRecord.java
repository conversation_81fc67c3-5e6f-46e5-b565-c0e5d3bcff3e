package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.asset.system.handle.RecordCounterListTypeHandler;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import com.niimbot.system.RecordCounter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "as_selector_record", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class AsSelectorRecord {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TenantFilterColumn
    private Long companyId;

    private Long empId;

    private String type;

    @TableField(typeHandler = RecordCounterListTypeHandler.class)
    private List<RecordCounter> data = Collections.emptyList();

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
