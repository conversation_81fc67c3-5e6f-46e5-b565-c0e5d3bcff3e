package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusUser;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface AsAccountEmployeeService extends IService<AsAccountEmployee> {

    Optional<AsAccountEmployee> getEmployeeAccount(Long employeeId);

    Optional<AsCusUser> getEmployAccount(Long employeeId);

    boolean unbindEmployee(Long employeeId);

    boolean batchUnbindEmploy(List<Long> employeeIds);

    boolean unbindCompany(Long companyId, Long accountId);

    List<AsAccountEmployee> listByEmployeeId(List<Long> employeeIds);

    List<Long> filterHasAccountEmp(List<Long> empIds);

    List<Long> hasAccountEmpId(Long companyId);
}
