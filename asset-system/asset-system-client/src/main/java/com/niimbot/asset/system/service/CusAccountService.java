package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.system.CusAccountBaseDto;
import com.niimbot.system.CusAccountDto;
import com.niimbot.system.CusAccountEnableBatchDto;
import com.niimbot.system.CusAccountEnableDto;
import com.niimbot.system.CusAccountPageDto;
import com.niimbot.system.CusAccountPageQueryDto;
import com.niimbot.system.CusAccountRoleBatchDto;

import java.util.List;

/**
 * 账号管理service
 *
 * <AUTHOR>
 * @Date 2020/11/18
 */
public interface CusAccountService {


    /**
     * 账号列表查询
     *
     * @return 列表数据
     */
    List<CusAccountDto> selectAccountList(Long companyId);

    List<Long> selectAccountEmpIds(Long companyId);

    /**
     * 账号列表查询
     *
     * @param kw 关键字
     * @return 列表数据
     */
    List<CusAccountBaseDto> selectListKw(String kw);

    /**
     * 账号列表查询
     *
     * @param page     分页对象
     * @param queryDto 账号条件
     * @return 分页数据
     */
    IPage<CusAccountPageDto> selectPage(Page<CusAccountDto> page, CusAccountPageQueryDto queryDto);

    Integer getAccountAmount(Long roleId);

    /**
     * 修改账号 - 当前修改密码和角色 since 2020-11-18
     *
     * @param account 账号信息
     * @return true or false
     */
    Boolean edit(CusAccountDto account);

    /**
     * 修改角色 since 2021-01-19
     *
     * @param account 账号信息
     * @return 角色信息是否与之前一样
     */
    Boolean editRole(CusAccountDto account);

    /**
     * 禁用或启用账号
     *
     * @param account 账号信息
     * @return 结果
     */
    Boolean enableOrDisable(CusAccountEnableDto account, String domain);

    /**
     * 批量启用禁用
     *
     * @param account
     * @param domain
     * @return
     */
    Boolean enableOrDisableBatch(CusAccountEnableBatchDto account, String domain);

    /**
     * 踢出用户
     *
     * @param userList 用户集合
     * @return true/false
     */
    Boolean kickOffLoginUser(List<AsCusUser> userList);

    /**
     * 批量添加角色
     *
     * @param account
     * @return
     */
    Boolean addRoleBatch(CusAccountRoleBatchDto account);

    /**
     * 批量移除角色
     *
     * @param account
     * @return
     */
    Boolean removeRoleBatch(CusAccountRoleBatchDto account);
}
