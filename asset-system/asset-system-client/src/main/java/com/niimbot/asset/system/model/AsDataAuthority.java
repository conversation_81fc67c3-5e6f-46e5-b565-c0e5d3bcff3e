package com.niimbot.asset.system.model;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.niimbot.asset.framework.model.UserDataOrgPermBizDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据权限
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "AsDataAuthority对象", description = "数据权限")
@TableName(value = "as_data_authority", autoResultMap = true)
public class AsDataAuthority implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "权限ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "权限数据编码")
    private String authorityDataCode;

    @ApiModelProperty(value = "权限编码")
    private String authorityCode;

    @ApiModelProperty(value = "权限类型: 0-所有数据, 1-本人, 2-本组织, 3-本组织(公司)和子组织(公司), 4-自定义")
    private Integer authorityType;

    @ApiModelProperty(value = "自定义权限数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<Object> authorityData;

    @ApiModelProperty(value = "权限分组")
    private Integer authorityGroup;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public List<UserDataOrgPermBizDto> userDataOrgPermBizDtoAuthorityData() {
        List<Object> data = this.authorityData;
        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream()
                .map(BeanUtil::beanToMap)
                .map(o -> BeanUtil.mapToBean(o, UserDataOrgPermBizDto.class, true, CopyOptions.create()))
                .collect(Collectors.toList());
    }

    public List<Long> longAuthorityData() {
        List<Object> data = this.authorityData;
        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream()
                .map(o -> Long.parseLong(o.toString()))
                .collect(Collectors.toList());
    }

/*    public List<Long> getPermsData() {
        List<Object> data = this.authorityData;
        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }
        Object firstData = data.get(0);
        if (NumberUtil.isNumber(firstData.toString())) {
            return data.stream()
                    .map(o -> Long.parseLong(o.toString()))
                    .collect(Collectors.toList());
        }
        List<UserDataOrgPermBizDto> orgBizs = data.stream()
                .map(BeanUtil::beanToMap)
                .map(o -> BeanUtil.mapToBean(o, UserDataOrgPermBizDto.class, true, CopyOptions.create()))
                .collect(Collectors.toList());
        List<Long> result = new ArrayList<>();
        orgBizs.forEach(o -> {
            result.addAll(o.getBiz());
        });
        return result;
    }*/

    @JsonIgnore
    public String getPermsKey() {
        return this.authorityDataCode + ":" + this.authorityCode + ":" + this.authorityType;
    }

    @JsonIgnore
    public String getPermsCacheKey() {
        return this.authorityDataCode + ":" + this.authorityCode;
    }

}
