package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.framework.dataperm.annonation.TenantFilterColumn;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * as_equipment_manage_config
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_app_activate", autoResultMap = true)
public class AsAppActivate implements Serializable {

    private static final long serialVersionUID = 6783858344948987013L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公司ID（租户ID）
     */
    @TableField(fill = FieldFill.INSERT)
    @TenantFilterColumn
    private Long companyId;

    /**
     * 类型
     */
    private String type;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}