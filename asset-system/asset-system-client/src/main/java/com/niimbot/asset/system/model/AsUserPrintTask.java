package com.niimbot.asset.system.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 打印任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsUserPrintTask对象", description = "打印任务表")
@TableName(value = "as_user_print_task", autoResultMap = true)
public class AsUserPrintTask implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "任务类型 1-标签打印 2-抗金属标签发卡 3-抗金属标签发卡&打印标签")
    private Short taskType;

    @ApiModelProperty(value = "打印类型 1-资产  2-耗材")
    private Short printType;

    @ApiModelProperty(value = "打印终端 1-pc端  2-Android端  3-iOS端 4-PDA")
    private Short printSource;

    @ApiModelProperty(value = "租户ID")
    private Long companyId;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "资产数量")
    private Integer assetNum;

    @ApiModelProperty(value = "资产id列表")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<Long> assets;

    @ApiModelProperty(value = "打印资产快照数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> assetsSnapshot;

    @ApiModelProperty(value = "模糊查询字段【资产名称、资产编码】")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject queryData;

    @ApiModelProperty(value = "已打印资产数量")
    private Integer printedAssetNum;

    @ApiModelProperty(value = "任务状态-1、排队待打印 2、打印中 3、暂停 4、异常暂停 5、已完成")
    private Short taskStatus;

    @ApiModelProperty(value = "设备名称")
    private String printerName;

    @ApiModelProperty(value = "打印机完整序列号")
    private String printerSerialNo;

    @ApiModelProperty(value = "打印标签ID")
    private Long tagId;

    @ApiModelProperty(value = "标签类型 0-非RFID  1-RFID")
    private Boolean tagType;

    @ApiModelProperty(value = "材质ID")
    private Long tagMaterialId;

    @ApiModelProperty(value = "打印浓度")
    private Integer printConcentration;

    @ApiModelProperty(value = "打印机硬件版本")
    private String hardwareVersion;

    @ApiModelProperty(value = "SDK版本")
    private String sdkVersion;

    @ApiModelProperty(value = "固件版本号")
    private String firmwareVersion;

    @ApiModelProperty(value = "终端品牌")
    private String terminalBrand;

    @ApiModelProperty(value = "终端型号")
    private String terminalType;

    @ApiModelProperty(value = "操作系统版本")
    private String osVersion;

    @ApiModelProperty(value = "app版本")
    private String appVersion;

    @ApiModelProperty(value = "打印时间")
    private LocalDateTime printTime;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "设备名称")
    @TableField(exist = false)
    private String taskName;

    @ApiModelProperty(value = "任务异常暂停的失败原因")
    @TableField(exist = false)
    private String failReason;

    @ApiModelProperty(value = "虚拟键，资产名称")
    private String assetName;

    @ApiModelProperty(value = "虚拟键，资产编码")
    private String assetCode;
}
