package com.niimbot.asset.system.ots.remote;

import com.niimbot.asset.system.ots.SystemRecycleBinOts;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.system.ots.process.SystemRecycleBinOtsProcessClient")
@FeignClient(name = "asset-system", url = "{gateway}/system/SystemRecycleBinOts/")
public interface SystemRecycleBinOtsRemoteClient extends SystemRecycleBinOts {
}
