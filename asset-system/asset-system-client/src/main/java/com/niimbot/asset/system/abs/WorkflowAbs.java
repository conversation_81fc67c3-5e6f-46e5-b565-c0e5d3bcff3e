package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.WorkflowTransferCmd;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 审批流
 * 原始接口 {@link com.niimbot.asset.activiti.service.ActWorkflowService}
 *
 * <AUTHOR>
 * @date 2022/5/11 10:31
 */
public interface WorkflowAbs {

    /**
     * 审批流转移
     * WorkflowTransfer workflowTransfer = new WorkflowTransfer()
     * .setRemovedUserId(employ.getEmployeeId())
     * .setWorkflowTransfer(dataTransfer);
     * workflowService.workflowTransfer(workflowTransfer);
     *
     * @param cmd
     * @return
     */
    @PostMapping("workflowTransfer")
    Boolean workflowTransfer(WorkflowTransferCmd cmd);

    @PostMapping("initApproveRoles")
    void initApproveRoles(Long companyId);

    @GetMapping("checkWorkflow")
    Boolean checkWorkflow(Long userId);
}
