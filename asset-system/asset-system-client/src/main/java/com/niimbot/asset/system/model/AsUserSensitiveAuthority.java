package com.niimbot.asset.system.model;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.system.SensitiveFieldItemDto;
import com.niimbot.asset.system.handle.SensitiveTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_user_sensitive_authority
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_user_sensitive_authority", autoResultMap = true)
public class AsUserSensitiveAuthority {

    /**
     * 权限ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公司ID（租户ID）
     */
    private Long companyId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 数据对象编码
     */
    private String configCode;

    /**
     * 权限数据
     */
    @TableField(typeHandler = SensitiveTypeHandler.class)
    private List<SensitiveFieldItemDto> authorityData;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}