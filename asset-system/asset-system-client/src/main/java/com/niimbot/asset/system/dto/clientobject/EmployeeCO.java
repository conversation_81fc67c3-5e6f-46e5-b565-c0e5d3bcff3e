package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class EmployeeCO extends ClientObject {

    @ApiModelProperty(value = "员工id")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String empName;

    @ApiModelProperty(value = "工号")
    private String empNo;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "头像")
    private String image;

    @ApiModelProperty(value = "职位信息")
    private String position;

    @ApiModelProperty(value = "数据范围（0、查看全部，1、仅查看当前用户，2、仅查看当前部门，3、所在部门及子部门，4、自定义部门列表）")
    private Short dataScope;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "在职状态:1-在职, 2-离职")
    private Integer status;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @ApiModelProperty(value = "备注")
    private String remark;

}
