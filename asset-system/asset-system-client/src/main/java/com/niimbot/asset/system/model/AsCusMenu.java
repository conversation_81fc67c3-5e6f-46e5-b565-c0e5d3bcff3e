package com.niimbot.asset.system.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AsCusMenu对象", description = "客户菜单")
@TableName(value = "as_cus_menu")
public class AsCusMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "菜单编码")
    private String menuCode;

    @ApiModelProperty(value = "父菜单ID")
    private Long pid;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "路径paths")
    private String paths;

    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;

    @ApiModelProperty(value = "菜单地址")
    private String feRoute;

    @ApiModelProperty(value = "菜单类型（M-目录 C-菜单 F-按钮 A-APP）")
    private String menuType;

    @ApiModelProperty(value = "菜单class样式")
    private String menuClass;

    @ApiModelProperty(value = "样式icon图标")
    private String menuIcon;

    @ApiModelProperty(value = "菜单状态（1正常 2禁用）")
    private Short status;

    @ApiModelProperty(value = "权限标识")
    private String perms;

    @ApiModelProperty(value = "是否配置项目 0 -否  1-是")
    private Boolean isConfig;

    @ApiModelProperty(value = "是否展示子节点菜单 0 -否  1-是")
    private Boolean isShowChildren;

    @ApiModelProperty(value = "是否可编辑")
    private Boolean canEdit;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
