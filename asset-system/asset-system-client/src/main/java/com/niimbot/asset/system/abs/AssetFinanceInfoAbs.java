package com.niimbot.asset.system.abs;

import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.finance.FinanceStatisticsQueryDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/2/27 下午4:46
 */
public interface AssetFinanceInfoAbs {

    /**
     * 获取资产入账信息
     * @param assetId
     * @return
     */
    @GetMapping("queryAssetFinanceInfo")
    AssetFinanceInfoDto queryAssetFinanceInfo(@PathVariable("assetId") Long assetId);

    /**
     * 分摊部门为当前orgId的资产入账条数
     * @param queryDto
     * @return
     */
    @PostMapping("countAssetFinanceByOrg")
    Integer countAssetFinanceByOrg(@RequestBody FinanceStatisticsQueryDto queryDto);

    /**
     * 启用了的折旧功能的组织
     * @param queryDto
     * @return
     */
    @PostMapping("countDepreciationConfigByOrg")
    Integer countDepreciationConfigByOrg(@RequestBody FinanceStatisticsQueryDto queryDto);
}
