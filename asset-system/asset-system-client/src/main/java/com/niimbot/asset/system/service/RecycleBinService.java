package com.niimbot.asset.system.service;

import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RecycleBinService {

    Integer RES_TYPE_IS_ASSET = 1;

    Integer RES_TYPE_IS_MATERIAL = 2;

    Map<Integer, String> RES_TYPE_TABLE_NAME = ImmutableMap.of(RES_TYPE_IS_ASSET, "as_asset", RES_TYPE_IS_MATERIAL, "as_material");

    Map<Integer, String> RES_TYPE_COLUMN_NAME = ImmutableMap.of(RES_TYPE_IS_ASSET, "asset_data", RES_TYPE_IS_MATERIAL, "material_data");

    /**
     * 是否支持
     *
     * @param resType 类型
     */
    default void isSupport(Integer resType) {
        if (!RES_TYPE_TABLE_NAME.containsKey(resType)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前回收站不支持此类型业务");
        }
    }

    /**
     * 资源回收
     *
     * @param resRecycle body
     * @return true if success
     */
    Boolean recycle(ResRecycle resRecycle);

    /**
     * 资源还原
     *
     * @param resRestore body
     * @return true if success
     */
    Boolean restore(ResRestore resRestore);

    /**
     * 资源释放-彻底删除
     *
     * @param resRelease body
     * @return true if success
     */
    Boolean release(ResRelease resRelease);

    /**
     * 获取资产回收站信息详情
     *
     * @param get body
     * @return body
     */
    RecycleBin details(GetRecycleBins get);

    /**
     * 获取回收站内容
     *
     * @param getRecycleBins body
     * @return list of recycle bins
     */
    RecycleBin get(GetRecycleBins getRecycleBins);

    /**
     * 获取回收站数据ID集合
     *
     * @param getRecycleBins body
     * @return ids
     */
    List<Long> getRecycleIds(GetRecycleBins getRecycleBins);
}
