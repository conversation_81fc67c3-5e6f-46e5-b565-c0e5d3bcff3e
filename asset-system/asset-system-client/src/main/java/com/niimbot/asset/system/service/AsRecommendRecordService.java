package com.niimbot.asset.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsRecommendRecord;
import com.niimbot.system.RecommendRecordDto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/27 15:07
 */
public interface AsRecommendRecordService extends IService<AsRecommendRecord> {

    void tradeRecord(Long saleOrderId, Long companyId, BigDecimal totalMoney, LocalDateTime operateTime);

    List<RecommendRecordDto> list(Long recommendEmpId, Integer status);

    AsCusEmployee verifyMobile(Long recommendEmpId, String mobile);
}
