package com.niimbot.asset.system.dto;

import com.niimbot.system.DataTransfer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/6 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class WorkflowTransferCmd extends CommonCommand {
    /**
     * 员工id
     */
    private Long removeEmployeeId;

    /**
     * 企业id
     */
    private Long companyId;

    private DataTransfer workflowTransfer;
}
