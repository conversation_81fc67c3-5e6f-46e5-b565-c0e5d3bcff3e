package com.niimbot.asset.system.abs;

import com.niimbot.asset.system.dto.TodoGetQry;
import com.niimbot.asset.system.dto.clientobject.TodoCO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 待办
 * 原始接口 {@link com.niimbot.asset.todo.service.AsTodoService}
 *
 * <AUTHOR>
 * @date 2022/5/10 11:45
 */
public interface TodoAbs {

    /**
     * 待办查询
     *
     * @param qry
     * @return
     */
    @GetMapping("getUserTodo")
    TodoCO getUserTodo(@SpringQueryMap TodoGetQry qry);

    @GetMapping("count")
    Map<Long, Long> countUserUnHandle(@RequestBody List<Long> userIds);
}
