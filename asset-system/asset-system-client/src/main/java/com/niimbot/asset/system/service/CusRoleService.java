package com.niimbot.asset.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.system.CusMenuRoleDto;
import com.niimbot.system.CusRoleAccountDto;
import com.niimbot.system.CusRoleAccountQueryDto;
import com.niimbot.system.CusRoleDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/10/27 14:32
 */
public interface CusRoleService extends IService<AsCusRole> {

    /**
     * 添加角色和菜单权限
     *
     * @param dto 角色
     * @return 是否成功
     */
    Boolean add(CusRoleDto dto);

    /**
     * 编辑角色和菜单权限
     *
     * @param dto 角色
     * @return 修改权限列表
     */
    List<String> edit(CusRoleDto dto);

    /**
     * 删除角色
     *
     * @param roleIds 角色Id
     * @return 是否成功
     */
    Boolean remove(List<Long> roleIds);

    /**
     * 查询公司角色信息
     *
     * @param companyId 公司id
     * @return 角色信息列表
     */
    List<AsCusRole> listByCompanyId(Long companyId);

    /**
     * 查询公司角色权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    CusMenuRoleDto getSetting(Long roleId);

    /**
     * 查询获取角色信息
     *
     * @param userId 用户ID
     * @return CusRoleDto信息
     */
    List<CusRoleDto> getRoleByEmployeeId(Long userId);

    /**
     * 根据角色Id查询用户
     *
     * @param roleId 用户Id
     * @return 用户信息
     */
    List<AsCusUser> listUserByRoleId(Long roleId);

    /**
     * 查询角色账户列表list集合
     *
     * @return 角色账户列表
     */
    List<CusRoleAccountDto> roleAccountList(CusRoleAccountQueryDto roleAccountQueryDto);

    /**
     * 通过id查询角色信息
     *
     * @param roleId 角色id
     * @return 角色信息
     */
    CusRoleDto getInfo(Long roleId);

    Boolean supertubeTransfer(Map<String, Long> data);

    /**
     * 配置为默认角色
     * @param roleId
     * @return
     */
    Boolean configDefault(Long roleId);

    /**
     * 查询企业默认角色
     * @param companyId
     * @return
     */
    AsCusRole queryDefaultRole(Long companyId);
}
