package com.niimbot.asset.system.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/11 18:04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AdminPrinterConcentrationCO extends ClientObject {
    /** 
     * 主键ID 
     */
    private Long id;

    /** 
     * 打印机ID 
     */
    private Long printerId;

    /** 
     * 材质id 
     */
    private Long materialId;

    /** 
     * 默认浓度 
     */
    private Integer defaultConcentration;

    /** 
     * 最小浓度 
     */
    private Integer minConcentration;

    /** 
     * 最大浓度 
     */
    private Integer maxConcentration;

    /** 
     * 资产默认打印标签ID 
     */
    private Long defaultTagId;

    /** 
     * 耗材默认打印标签模板ID 
     */
    private Long defaultCftagId;

    /** 
     * 用户ID 
     */
    private Long userId;

    /** 
     * 是否默认 0-不是默认标签数据  1-默认标签数据 
     */
    private Boolean isDefault;
}
