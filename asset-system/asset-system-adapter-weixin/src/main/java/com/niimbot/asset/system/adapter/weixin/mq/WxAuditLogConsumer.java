package com.niimbot.asset.system.adapter.weixin.mq;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.niimbot.asset.framework.annotation.MessageConsumer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqConsumerListener;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.asset.system.service.AsAuditLogService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AuditLogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@MessageConsumer(topic = MqConstant.ASSET_TOPIC, group = "GID_asset-weixin-audit-log_c", tags = "weixin_send_audit_log")
public class WxAuditLogConsumer implements RocketMqConsumerListener<AuditLogRecord> {

    private final AsAuditLogService auditLogService;

    @Override
    public Action consume(AuditLogRecord record, ConsumeContext consumeContext) {
        try {
            auditLogService.record(record);
        } catch (BusinessException e) {
            log.warn("保存审计日志业务异常", e);
        } catch (Exception e) {
            log.warn("保存审计日志业务异常", e);
            return Action.ReconsumeLater;
        }
        return Action.CommitMessage;
    }
}
