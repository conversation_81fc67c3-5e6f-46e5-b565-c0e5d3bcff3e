package com.niimbot.asset.system.adapter.weixin.controller;

import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/weixin/message")
public class WeixinMessageServiceController {

    private final WxOpenApiService openApiService;

    @PostMapping("/test")
    public boolean sendMessage() {

        return true;
    }

}
