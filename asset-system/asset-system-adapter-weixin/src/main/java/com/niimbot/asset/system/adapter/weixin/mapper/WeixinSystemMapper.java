package com.niimbot.asset.system.adapter.weixin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WeixinSystemMapper {

    // 企业微信兼容，员工搜索
    IPage<CusEmployeeDto> selectCustomPage(@Param("page") Page<CusEmployeeDto> page,
                                           @Param("em") CusEmployeeQueryDto queryDto,
                                           @Param("orgIds") List<Long> orgIds,
                                           @Param("companyId") Long companyId,
                                           @Param("unionIds") List<String> unionIds);

    Integer selectCompanyBaseAccountSum(@Param("companyId") Long companyId);

}
