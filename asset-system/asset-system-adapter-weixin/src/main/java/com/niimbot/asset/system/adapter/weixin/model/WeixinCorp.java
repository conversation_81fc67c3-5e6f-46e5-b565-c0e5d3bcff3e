package com.niimbot.asset.system.adapter.weixin.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/7/4 15:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "weixin_corp", autoResultMap = true)
public class WeixinCorp {

    @ApiModelProperty(value = "授权方企业微信id")
    @TableId(value = "corp_id", type = IdType.ASSIGN_ID)
    private String corpId;

    @ApiModelProperty("应用状态")
    private Integer appStatus;

    @ApiModelProperty("授权码摘要")
    private String licenseDigest;

    @ApiModelProperty(value = "公司Id")
    private Long companyId;

    @ApiModelProperty(value = "授权方企业名称")
    private String corpName;

    @ApiModelProperty(value = "agentId")
    private Integer agentId;

    @ApiModelProperty(value = "授权方企业类型，认证号：verifie注册号：unverified")
    private String corpType;

    @ApiModelProperty(value = "授权方企业方形头像")
    private String corpSquareLogoUrl;

    @ApiModelProperty(value = "授权方企业用户规模")
    private String corpUserMax;

    @ApiModelProperty(value = "企业类型，1. 企业; 2. 政府以及事业单位; 3. 其他组织,团队号")
    private String subjectType;

    @ApiModelProperty(value = "企业规模。当企业未设置该属性时，值为空")
    private String corpScale;

    @ApiModelProperty(value = "企业所属行业。当企业未设置该属性时，值为空")
    private String corpIndustry;

    @ApiModelProperty(value = "企业所属子行业。当企业未设置该属性时，值为空")
    private String corpSubIndustry;

    @ApiModelProperty(value = "企业永久授权码")
    private String permanentCode;

    @ApiModelProperty("原始内容")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject content;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "0-未删除  1：已删除")
    @TableLogic
    private Boolean isDelete;
}
