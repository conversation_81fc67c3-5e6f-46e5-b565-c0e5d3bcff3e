package com.niimbot.asset.system.adapter.weixin.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/7/4 13:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "weixin_callback", autoResultMap = true)
public class WeixinCallback {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private String suiteId;

    private String infoType;

    private String timeStamp;

    private String authCorpId;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject content;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
