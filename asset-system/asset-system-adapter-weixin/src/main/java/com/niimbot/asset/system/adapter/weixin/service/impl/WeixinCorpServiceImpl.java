package com.niimbot.asset.system.adapter.weixin.service.impl;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.DateUtils;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.abs.AssetAreaAbs;
import com.niimbot.asset.system.abs.AssetCategoryAbs;
import com.niimbot.asset.system.abs.AssetQueryViewAbs;
import com.niimbot.asset.system.abs.CompanyResourceAbs;
import com.niimbot.asset.system.abs.FormAbs;
import com.niimbot.asset.system.abs.MaterialOrderTypeAbs;
import com.niimbot.asset.system.abs.MessageAbs;
import com.niimbot.asset.system.abs.OrderTypeAbs;
import com.niimbot.asset.system.abs.PurchaseOrderTypeAbs;
import com.niimbot.asset.system.abs.RepositoryAbs;
import com.niimbot.asset.system.abs.WorkflowAbs;
import com.niimbot.asset.system.adapter.weixin.mapper.WeixinCorpMapper;
import com.niimbot.asset.system.adapter.weixin.model.WeixinCorp;
import com.niimbot.asset.system.adapter.weixin.service.WeixinContactsService;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCorpService;
import com.niimbot.asset.system.dto.AssetAreaSaveCmd;
import com.niimbot.asset.system.dto.AssetCategorySaveBatchCmd;
import com.niimbot.asset.system.dto.AssetQueryViewInitCmd;
import com.niimbot.asset.system.dto.BizFormAssetInitCmd;
import com.niimbot.asset.system.dto.BizFormMaterialInitCmd;
import com.niimbot.asset.system.dto.CompanyResourceAddCmd;
import com.niimbot.asset.system.dto.FormInitCmd;
import com.niimbot.asset.system.dto.MaterialOrderTypeInitCmd;
import com.niimbot.asset.system.dto.OrderTypeInitCmd;
import com.niimbot.asset.system.dto.PurchaseOrderTypeInitCmd;
import com.niimbot.asset.system.dto.RepositorySaveCmd;
import com.niimbot.asset.system.dto.clientobject.AssetAreaCO;
import com.niimbot.asset.system.dto.clientobject.AssetCategoryCO;
import com.niimbot.asset.system.dto.clientobject.CompanyResourceCO;
import com.niimbot.asset.system.dto.clientobject.RepositoryCO;
import com.niimbot.asset.system.event.CompanyRegisterEvent;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCompanyChannel;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsCusEmployeeSetting;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsIndustry;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsPromoteChannel;
import com.niimbot.asset.system.model.AsPromoteChannelSource;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.model.AsUserCompany;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.model.AsUserReports;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCompanyChannelService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeSettingService;
import com.niimbot.asset.system.service.AsDataPermissionService;
import com.niimbot.asset.system.service.AsRoleDataAuthorityService;
import com.niimbot.asset.system.service.AsRoleMenuService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.CompanyNewbieTaskService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.CusAccountService;
import com.niimbot.asset.system.service.CusRoleService;
import com.niimbot.asset.system.service.CusUserCompanyService;
import com.niimbot.asset.system.service.CusUserRoleService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.IndustryService;
import com.niimbot.asset.system.service.UserReportsService;
import com.niimbot.asset.weixin.base.constant.WxConstant;
import com.niimbot.asset.weixin.base.error.WxResultCode;
import com.niimbot.asset.weixin.base.support.CorpCaches;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpTpPermanentCodeInfo;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeixinCorpServiceImpl extends ServiceImpl<WeixinCorpMapper, WeixinCorp> implements WeixinCorpService {
    @Resource(name = "assetTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    private final AsCusEmployeeService employeeService;
    private final AsCusEmployeeExtService employeeExtService;
    private final AsCusEmployeeSettingService employeeSettingService;
    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    private final CusRoleService roleService;
    private final CusUserRoleService userRoleService;
    private final AsUserOrgService userOrgService;
    private final CusUserService userService;
    private final AsAccountEmployeeService accountEmployeeService;
    private final CusUserCompanyService cusUserCompanyService;
    private final AsDataPermissionService dataPermissionService;
    private final RedissonClient redissonClient;
    private final CompanyService companyService;

    private String getType() {
        return "WEIXIN";
    }

    @Override
    public String getCorpId(Long companyId) {
        // String cacheKey = String.format(WX_CORP_ID_CACHE, companyId);
        // if (redisService.hasKey(cacheKey)) {
        //     return Convert.toStr(redisService.get(cacheKey));
        // }
        WeixinCorp corp = this.getOne(Wrappers.lambdaQuery(WeixinCorp.class).eq(WeixinCorp::getCompanyId, companyId));
        if (Objects.isNull(corp)) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "企业未注册");
        }
        // redisService.set(cacheKey, corp.getCorpId());
        return corp.getCorpId();
    }

    @Override
    public Long getCompanyId(String corpId) {
        // String cacheKey = String.format(WX_COMPANY_ID_CACHE, corpId);
        // if (redisService.hasKey(cacheKey)) {
        //     return Convert.toLong(redisService.get(cacheKey));
        // }
        WeixinCorp corp = this.getOne(Wrappers.lambdaQuery(WeixinCorp.class).eq(WeixinCorp::getCorpId, corpId));
        if (Objects.isNull(corp)) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "企业未注册");
        }
        // redisService.set(cacheKey, corp.getCompanyId());
        return corp.getCompanyId();
    }

    @Override
    public WeixinCorp getCorp(String corpId, Long companyId) {
        if (companyId == null && corpId == null) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "企业未注册");
        }
        WeixinCorp corp = this.getOne(Wrappers.lambdaQuery(WeixinCorp.class)
                .eq(corpId != null, WeixinCorp::getCorpId, corpId)
                .eq(companyId != null, WeixinCorp::getCompanyId, companyId));
        if (Objects.isNull(corp)) {
            throw new BusinessException(WxResultCode.WX_COMMON_ERROR, "企业未注册");
        }
        return corp;
    }

    @Override
    public WeixinCorp preCreateCorp(String corpId) {
        RLock lock = redissonClient.getLock(WxConstant.getWxCorpLockKey(corpId));
        lock.lock();
        WeixinCorp weixinCorp;
        try {
            weixinCorp = getById(corpId);
            if (weixinCorp == null) {
                weixinCorp = new WeixinCorp();
                weixinCorp.setCorpId(corpId);
                weixinCorp.setCompanyId(IdUtils.getId());
                save(weixinCorp);
            }
        } finally {
            lock.unlock();
        }
        return weixinCorp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeixinCorp createOrUpdateCorp(WxCpTpPermanentCodeInfo permanentCodeInfo) {
        WxCpTpPermanentCodeInfo.AuthCorpInfo authCorpInfo = permanentCodeInfo.getAuthCorpInfo();
        WxCpTpPermanentCodeInfo.Agent agent = permanentCodeInfo.getAuthInfo().getAgents().get(0);
        WeixinCorp weixinCorp = preCreateCorp(authCorpInfo.getCorpId());
        weixinCorp.setPermanentCode(permanentCodeInfo.getPermanentCode());
        weixinCorp.setCorpName(authCorpInfo.getCorpName())
                .setAgentId(agent.getAgentId())
                .setCorpType(authCorpInfo.getCorpType())
                .setCorpSquareLogoUrl(authCorpInfo.getCorpSquareLogoUrl())
                .setCorpUserMax(authCorpInfo.getCorpUserMax())
                .setSubjectType(Convert.toStr(authCorpInfo.getSubjectType()))
                .setCorpScale(authCorpInfo.getCorpScale())
                .setCorpIndustry(authCorpInfo.getCorpIndustry())
                .setCorpSubIndustry(authCorpInfo.getCorpSubIndustry())
                .setContent((JSONObject) JSONObject.toJSON(permanentCodeInfo));
        updateById(weixinCorp);
        return weixinCorp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void register(WeixinCorp weixinCorp, WxCpTpPermanentCodeInfo.AuthUserInfo authUserInfo) {
        // ========================= 1、创建公司 =====================================
        initCompany(weixinCorp);
        log.debug("init company success {}", weixinCorp.getCompanyId());

        initCompanyChannel(weixinCorp.getCompanyId(), 1851L, 3981L);

        // ========================= 2、公司组织表 ====================================
        Long orgId = initOrg(weixinCorp);
        log.debug("init org success {}", orgId);

        // ========================= 3、公司角色表、管理员角色信息、公司用户表 =============
        initCompanyRole(weixinCorp.getCompanyId());
        log.debug("init company role success");

        // ========================= 4、用户账号表、员工表、用户扩展表、用户首页setting相关 =============
        // 开通的管理员，设置为系统超级管理员
        if (authUserInfo != null) {
            Long empId = saveAdmin(authUserInfo, weixinCorp.getCompanyId(), orgId);
            // 用户资产查询视图
            initAssetQueryView(weixinCorp.getCompanyId(), empId);
            log.debug("init user and employee success");
        } else {
            log.error("can not find init user and employee");
        }

        // ========================= 5、合同表 、角色菜单信息 ============================
        initCompanyContract(weixinCorp);
        log.debug("init company contract success");

        // ========================= 6、分类表 =========================================
        initCategory(weixinCorp.getCompanyId());
        log.debug("init company category success");

        // ========================= 6、区域表 =========================================
        initCompanyArea(weixinCorp.getCompanyId(), orgId);
        log.debug("init company area success");

        // ========================= 仓库表 =========================================
        initRepository(weixinCorp.getCompanyId(), orgId);

        // ========================= 7、用户统计表 =========================================
        initUserReports(weixinCorp.getCompanyId());
        log.debug("init user report success");

        // ========================= 8、资产配置 =========================================
        SpringUtil.getBean(FormAbs.class).initAssetItems(new BizFormAssetInitCmd()
                .setCompanyId(weixinCorp.getCompanyId()));
        log.debug("init assetAttr success");

        // ========================= 9、耗材配置 =========================================
        SpringUtil.getBean(FormAbs.class).initMaterialItems(new BizFormMaterialInitCmd()
                .setCompanyId(weixinCorp.getCompanyId()));
        log.debug("init materialAssetAttr success");

        // ========================= 10、单据配置 =========================================
        SpringUtil.getBean(OrderTypeAbs.class).initCompanyOrderType(new OrderTypeInitCmd()
                .setCompanyId(weixinCorp.getCompanyId()));
        log.debug("init orderType and orderField success");

        // 表单初始化
        SpringUtil.getBean(FormAbs.class).initCompanyForm(new FormInitCmd().setCompanyId(weixinCorp.getCompanyId()));

        // ========================= 11、耗材单据配置 =========================================
        SpringUtil.getBean(MaterialOrderTypeAbs.class).initCompanyOrderType(
                new MaterialOrderTypeInitCmd().setCompanyId(weixinCorp.getCompanyId()));
        log.debug("init material orderType and orderField success");

        // 采购单据配置
        SpringUtil.getBean(PurchaseOrderTypeAbs.class).initCompanyOrderType(
                new PurchaseOrderTypeInitCmd().setCompanyId(weixinCorp.getCompanyId()));

        // 15、审批角色初始化
        SpringUtil.getBean(WorkflowAbs.class).initApproveRoles(weixinCorp.getCompanyId());

        // ========================= 14、消息配置 =========================================
        SpringUtil.getBean(MessageAbs.class).companyMessageRuleDataInit(weixinCorp.getCompanyId());
        log.debug("init message rule success");

        // 新手任务录入
        SpringUtil.getBean(CompanyNewbieTaskService.class).init(weixinCorp.getCompanyId());

        // 同步组织架构
        executor.execute(() -> {
            try {
                TimeUnit.SECONDS.sleep(5L);
            } catch (InterruptedException e) {
                log.error("注册时异步同步组织架构线程休眠异常", e);
            }
            try {
                SpringUtil.getBean(WeixinContactsService.class).loadSave(weixinCorp.getCompanyId());
            } catch (Exception e) {
                log.error("注册时异步同步组织架构异常", e);
            }
        });

        // 注册成功时间
        EventPublishHandler.publish(new CompanyRegisterEvent(weixinCorp.getCompanyId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAuth(String authCorpId) {
        WeixinCorp weixinCorp = getOne(Wrappers.lambdaQuery(WeixinCorp.class)
                .eq(WeixinCorp::getCorpId, authCorpId));
        if (weixinCorp != null) {
            String yyyyMMddHHmmss = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            update(Wrappers.lambdaUpdate(WeixinCorp.class)
                    .set(WeixinCorp::getCorpId, "DEL_" + authCorpId + "_" + yyyyMMddHHmmss)
                    .eq(WeixinCorp::getCorpId, authCorpId));
            companyService.update(Wrappers.lambdaUpdate(AsCompany.class)
                    .set(AsCompany::getStatus, ManageConstant.COMPANY_STATUS_DISABLE)
                    .eq(AsCompany::getId, weixinCorp.getCompanyId()));
            CorpCaches.clean(weixinCorp.getCompanyId(), weixinCorp.getCorpId());
            List<AsCusUser> kickOffUser = accountEmployeeService.list(Wrappers.lambdaQuery(AsAccountEmployee.class).eq(AsAccountEmployee::getCompanyId, weixinCorp.getCompanyId()))
                    .stream().map(v -> new AsCusUser().setId(v.getEmployeeId()).setCompanyId(v.getCompanyId()))
                    .collect(Collectors.toList());
            SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(kickOffUser);
        }
    }

    private Long saveAdmin(WxCpTpPermanentCodeInfo.AuthUserInfo authUserInfo, Long companyId, Long orgId) {
        Long empId = IdUtils.getId();
        // ================================员工=======================================
        AsCusEmployee cusEmployee = new AsCusEmployee();
        cusEmployee.setId(empId)
                .setCompanyId(companyId)
                .setImage(authUserInfo.getAvatar())
                .setEmpName(WxConstant.getContactEmpName(authUserInfo.getUserId()))
                .setEmpNo(AsCusEmployeeService.DEFAULT_EMP_NO)
                .setDataScope(DataPermRuleType.TYPE_ALL.getValue().shortValue())
                .setRemark("weixinAdmin");

        AsThirdPartyEmployee thirdPartyEmployee = new AsThirdPartyEmployee()
                .setId(IdUtils.getId()).setCompanyId(companyId).setEmployeeId(empId).setUserId(authUserInfo.getUserId())
                .setType(getType());
        employeeService.save(cusEmployee);
        thirdPartyEmployeeService.save(thirdPartyEmployee);

        // ================================员工组织=======================================
        userOrgService.save(new AsUserOrg().setOrgId(orgId).setUserId(empId));

        // ================================账户=======================================
        // 判断是否有账户
        AsCusUser one = userService.getOne(new LambdaQueryWrapper<AsCusUser>().eq(AsCusUser::getUnionId, authUserInfo.getOpenUserid()));
        if (ObjectUtil.isNull(one)) {
            one = new AsCusUser();
            one.setId(IdUtils.getId())
                    .setAccount(authUserInfo.getUserId())
                    .setUnionId(authUserInfo.getOpenUserid())
                    .setNickname(WxConstant.getContactEmpName(authUserInfo.getUserId()))
                    .setImage(authUserInfo.getAvatar())
                    .setCompanyId(companyId)
                    .setStatus((short) 1)
                    .setSource(1);
            userService.save(one);
        }

        AsAccountEmployee accountEmployee = new AsAccountEmployee();
        accountEmployee.setCompanyId(companyId).setAccountId(one.getId()).setEmployeeId(empId);
        accountEmployeeService.save(accountEmployee);

        // ================================账户扩展=======================================
        AsCusEmployeeExt employeeExt = new AsCusEmployeeExt();
        employeeExt.setId(empId)
                .setAgentId(0)
                .setAccountStatus(3);
        employeeExtService.save(employeeExt);

        // ================================账户设置=======================================
        AsCusEmployeeSetting employeeSetting = new AsCusEmployeeSetting();
        employeeSetting.setUserId(empId)
                .setAppToolbox(Lists.newArrayList())
                .setPcToolbox(Lists.newArrayList())
                .setPcHome(Lists.newArrayList())
                .setAssetHead(Lists.newArrayList())
                .setMaterialHead(Lists.newArrayList());
        employeeSettingService.save(employeeSetting);

        // ================================角色关联=======================================
        AsCusRole role = roleService.getOne(new LambdaQueryWrapper<AsCusRole>()
                .eq(AsCusRole::getCompanyId, companyId).eq(AsCusRole::getRoleCode, BaseConstant.ADMIN_ROLE));
        AsUserRole userRole = new AsUserRole();
        userRole.setUserId(empId);
        userRole.setRoleId(role.getId());
        userRoleService.save(userRole);

        // ================================用户公司=======================================
        AsUserCompany asUserCompany = new AsUserCompany().setUserId(empId).setCompanyId(companyId);
        cusUserCompanyService.save(asUserCompany);

        // ================================数据权限=======================================
        dataPermissionService.initDataPermission(cusEmployee.getCompanyId(), cusEmployee.getId(), BaseConstant.ADMIN_ROLE);

        return empId;

    }

    private void initCompany(WeixinCorp weixinCorp) {
        IndustryService industryService = SpringUtil.getBean(IndustryService.class);

        // 查询行业信息
        List<AsIndustry> industryList = industryService.list();
        Long industryId = -1L;
        String industryName = "";
        if (CollUtil.isNotEmpty(industryList)) {
            industryId = industryList.get(0).getId();
            industryName = industryList.get(0).getIndustryName();
        }

        // 创建公司数据
        AsCompany company = new AsCompany()
                .setId(weixinCorp.getCompanyId())
                .setName(weixinCorp.getCorpName())
                .setLogo(weixinCorp.getCorpSquareLogoUrl())
                .setIndustryId(industryId)
                .setIndustryName(industryName);
        companyService.save(company);

        // 赠送企业资源包
        CompanyResourceCO resourceCO = new CompanyResourceCO();
        resourceCO.setCompanyId(weixinCorp.getCompanyId())
                .setSkuCode("Trial")
                .setResourceName("体验版")
                .setCapacity(100)
                .setExperience(true)
                .setEffectiveTime(LocalDateTime.now())
                .setExpirationTime(LocalDateTime.now().plusYears(10).withHour(23).withMinute(59).withSecond(59).withNano(0));
        SpringUtil.getBean(CompanyResourceAbs.class).saveResource(new CompanyResourceAddCmd().setCompanyResource(resourceCO));
    }

    private Long initOrg(WeixinCorp weixinCorp) {
        // 查询当前节点
        AsOrg org = new AsOrg();
        org.setOrgCode("A001");
        org.setId(IdUtils.getId());
        org.setExternalOrgId("1");
        org.setOrgName(weixinCorp.getCorpName());
        org.setPid(0L);
        org.setExternalPid("0");
        org.setPaths("0,");
        org.setLevel(0);
        org.setOrgType(AssetConstant.ORG_TYPE_COMPANY);
        org.setCompanyId(weixinCorp.getCompanyId());
        org.setCompanyOwner(org.getId());
        Db.save(org);
        return org.getId();
    }

    private void initCompanyRole(Long companyId) {
        Long adminRoleId = IdUtils.getId();
        Long assetAdminRoleId = IdUtils.getId();
        Long commonRoleId = IdUtils.getId();
        // 公司角色信息
        List<AsCusRole> roles = Lists.newArrayList(
                new AsCusRole()
                        .setId(adminRoleId)
                        .setRoleName("超级管理员").setRoleCode(BaseConstant.ADMIN_ROLE)
                        .setCanDelete(false)
                        .setCompanyId(companyId),
                new AsCusRole()
                        .setId(assetAdminRoleId)
                        .setRoleName("资产管理员").setRoleCode(BaseConstant.ASSET_ADMIN_ROLE)
                        .setCanDelete(false)
                        .setCompanyId(companyId),
                new AsCusRole()
                        .setId(commonRoleId)
                        .setRoleName("普通员工").setRoleCode(BaseConstant.COMMON_ROLE)
                        .setCanDelete(false)
                        .setIsDefault(Boolean.TRUE)
                        .setCompanyId(companyId)
        );
        SpringUtil.getBean(CusRoleService.class).saveBatch(roles);

        // 添加资产管理员初始菜单
        SpringUtil.getBean(AsRoleMenuService.class).initAssetRole(assetAdminRoleId);
        // 初始化资产管理员角色数据权限
        SpringUtil.getBean(AsRoleDataAuthorityService.class).initRoleDataAuth(companyId, assetAdminRoleId, BaseConstant.ASSET_ADMIN_ROLE);
        // 添加员工初始菜单
        SpringUtil.getBean(AsRoleMenuService.class).initCommonRole(commonRoleId);
        // 初始化普通员工角色数据权限
        SpringUtil.getBean(AsRoleDataAuthorityService.class).initRoleDataAuth(companyId, commonRoleId, BaseConstant.COMMON_ROLE);
    }

    private void initCompanyContract(WeixinCorp weixinCorp) {
        // 公司setting
        AsCompanySetting asCompanySetting = new AsCompanySetting().setCompanyId(weixinCorp.getCompanyId())
                .setExpandSwitch(new CompanySwitch());
        SpringUtil.getBean(CompanySettingService.class).save(asCompanySetting);
    }

    private void initCategory(Long companyId) {
        AssetCategoryCO cate = new AssetCategoryCO();
        cate.setCompanyId(companyId)
                .setIndustryId(1L)
                .setCategoryName("默认分类")
                .setCategoryCode("A01")
                .setSourceId(0L)
                .setPid(0L);
        cate.setPaths("0,");
        cate.setLevel(0);
        AssetCategorySaveBatchCmd saveBatchCmd = new AssetCategorySaveBatchCmd().setCategories(ListUtil.of(cate));
        SpringUtil.getBean(AssetCategoryAbs.class).saveBatchAssetCategory(saveBatchCmd);
    }

    private void initCompanyArea(Long companyId, Long rootOrgId) {
        AssetAreaCO area = new AssetAreaCO().setId(IdUtils.getId()).setCompanyId(companyId).setAreaCode("01").setAreaName("默认区域")
                .setOrgId(rootOrgId).setPid(0L).setLevel(0).setPaths("0,");
        SpringUtil.getBean(AssetAreaAbs.class).saveAssetArea(new AssetAreaSaveCmd().setAssetArea(area));
    }

    private void initRepository(Long companyId, Long orgId) {
        RepositoryCO repository = new RepositoryCO();
        repository.setName("默认仓库")
                .setCode("A01")
                .setCompanyId(companyId)
                .setManagerOwner(orgId);
        SpringUtil.getBean(RepositoryAbs.class).saveRepository(new RepositorySaveCmd().setRepository(repository));
    }

    private void initUserReports(Long companyId) {
        AsUserReports userReports = new AsUserReports().setCompanyId(companyId).setDayTime(LocalDateTime.now())
                .setWeekTime(DateUtils.getWeeksNum(DateUtils.now()));
        SpringUtil.getBean(UserReportsService.class).save(userReports);
    }

    private void initAssetQueryView(Long companyId, Long userId) {
        SpringUtil.getBean(AssetQueryViewAbs.class).initUserView(new AssetQueryViewInitCmd().setCompanyId(companyId)
                .setEmployeeId(userId));
    }

    private void initCompanyChannel(Long companyId, Long ch, Long ag) {
        AsCompanyChannel channel = new AsCompanyChannel();
        channel.setId(companyId);
        AsPromoteChannel promoteChannel = SpringUtil.getBean(AsCompanyChannelService.class)
                .getPromoteChannelByCode(ch);
        AsPromoteChannelSource source = SpringUtil.getBean(AsCompanyChannelService.class)
                .getAsPromoteChannelSourceByCode(ag);
        if ((promoteChannel == null || source == null) || !source.getPromoteChannelId().equals(promoteChannel.getId())) {
            channel.setChannelCode(0L);
            channel.setSourceCode(0L);
        } else {
            channel.setChannelCode(ch);
            channel.setSourceCode(ag);
        }
        SpringUtil.getBean(AsCompanyChannelService.class).save(channel);
    }

}
