package com.niimbot.asset.system.adapter.weixin.controller;

import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.adapter.weixin.service.WeixinContactsService;
import com.niimbot.asset.weixin.base.dto.UpdateEmployeeNo;
import com.niimbot.asset.weixin.base.dto.UpdateOrgNo;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/weixin/contacts")
@RequiredArgsConstructor
public class WeixinContactsServiceController {

    private final WeixinContactsService contactsService;

    @Resource(name = "assetTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    @PostMapping("/manual/sync")
    public Boolean manualSync() {
        // RLock lock = redissonClient.getLock(String.format(WxConstant.SYNC_LOCK, LoginUserThreadLocal.getCompanyId()));
        // if (lock.isLocked()) {
        //     throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "正在同步中，请勿重复操作！");
        // }
        executor.execute(() -> contactsService.loadSave(LoginUserThreadLocal.getCompanyId()));
        return true;
    }

    @PostMapping("/emp/updateNo")
    public Boolean updateEmpNo(@RequestBody UpdateEmployeeNo updateEmployeeNo) {
        return contactsService.updateEmployeeNo(updateEmployeeNo);
    }

    @PostMapping("/org/updateNo")
    public Boolean updateOrgNo(@RequestBody UpdateOrgNo updateOrgNo) {
        return contactsService.updateOrgNo(updateOrgNo);
    }
}
