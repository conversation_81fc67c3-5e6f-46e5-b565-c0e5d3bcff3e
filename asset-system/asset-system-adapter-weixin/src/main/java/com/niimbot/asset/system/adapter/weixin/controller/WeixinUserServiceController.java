package com.niimbot.asset.system.adapter.weixin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.system.adapter.weixin.model.WeixinCorp;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCorpService;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.CusRoleService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusRoleDto;

import cn.hutool.core.collection.CollUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/5 10:50
 */
@Slf4j
@RestController
@RequestMapping("server/weixin/user")
@RequiredArgsConstructor
public class WeixinUserServiceController {

    private final WeixinCorpService weixinCorpService;
    private final CusUserService accountUserService;
    private final AsAccountEmployeeService accountEmployeeService;
    private final AsCusEmployeeService employeeService;
    private final AsCusEmployeeExtService employeeExtService;
    private final CusRoleService employeeRoleService;

    @GetMapping("/getByOpenId")
    public CusUserDto selectUserByOpenId(@RequestParam("openId") String openId,
                                         @RequestParam("corpId") String corpId) {
        WeixinCorp weixinCorp = weixinCorpService.getOne(Wrappers.lambdaQuery(WeixinCorp.class)
                .eq(WeixinCorp::getCorpId, corpId));
        if (ObjectUtil.isNotNull(weixinCorp)) {
            Long companyId = weixinCorp.getCompanyId();
            AsCusUser account = accountUserService.getOne(
                    Wrappers.lambdaQuery(AsCusUser.class)
                            .and(wrapper -> wrapper.eq(AsCusUser::getUnionId, openId)
                                    .or()
                                    .eq(AsCusUser::getAccount, openId)));
            if (Objects.isNull(account)) {
                return null;
            }

            // 账号是否关联了员工
            AsAccountEmployee accountEmployee = accountEmployeeService.getOne(
                    Wrappers.lambdaQuery(AsAccountEmployee.class)
                            .eq(AsAccountEmployee::getAccountId, account.getId())
                            .eq(AsAccountEmployee::getCompanyId, companyId));

            if (ObjectUtil.isNull(accountEmployee)) {
                return null;
            }

            AsCusEmployee employee = employeeService.getById(accountEmployee.getEmployeeId());

            // 账号未激活
            AsCusEmployeeExt ext = employeeExtService.getById(accountEmployee.getEmployeeId());
            if (ext.getAccountStatus() == 1) {
                throw new BusinessException(SystemResultCode.EMP_INACTIVE_ACCOUNT);
            }
            return buildUserInfo(account, employee);
        } else {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
    }

    private CusUserDto buildUserInfo(AsCusUser account, AsCusEmployee employee) {
        if (Objects.isNull(account) || Objects.isNull(employee)) {
            return null;
        }
        CusUserDto dto = new CusUserDto()
                // 账户信息
                .setAccountId(account.getId()).setAccount(account.getAccount()).setUnionId(account.getUnionId()).setPassword(account.getPassword())
                .setEmail(account.getEmail()).setStatus(account.getStatus())
                .setAgreementStatus(account.getAgreementStatus()).setGuideStatus(account.getGuideStatus())
                // 员工信息
                .setId(employee.getId()).setCompanyId(employee.getCompanyId()).setMobile(employee.getMobile()).setDataScope(employee.getDataScope());
        AsCusEmployeeExt employeeExt = employeeExtService.getById(employee.getId());
        if (Objects.nonNull(employeeExt)) {
            // 员工扩展信息
            dto.setDefaultTagId(employeeExt.getDefaultTagId())
                    .setDefaultCftagId(employeeExt.getDefaultCftagId())
                    .setDefaultCftagCode(employeeExt.getDefaultCftagCode())
                    .setDefaultTplId(employeeExt.getDefaultTplId());
        }
        List<CusRoleDto> roles = employeeRoleService.getRoleByEmployeeId(employee.getId());
        if (!CollUtil.isEmpty(roles)) {
            // 员工角色超管标识
            List<String> roleNames = roles.stream().map(CusRoleDto::getRoleCode).collect(Collectors.toList());
            dto.setIsAdmin(roleNames.contains(BaseConstant.ADMIN_ROLE));
        }
        return dto;
    }

}
