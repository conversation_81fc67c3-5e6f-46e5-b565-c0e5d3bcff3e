package com.niimbot.asset.system.adapter.weixin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.adapter.weixin.mapper.WeixinCallbackMapper;
import com.niimbot.asset.system.adapter.weixin.model.WeixinCallback;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCallbackService;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:02
 */
@Service
public class WeixinCallbackServiceImpl extends ServiceImpl<WeixinCallbackMapper, WeixinCallback> implements WeixinCallbackService {

}
