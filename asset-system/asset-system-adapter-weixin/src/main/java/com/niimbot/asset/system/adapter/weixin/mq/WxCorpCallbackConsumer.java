package com.niimbot.asset.system.adapter.weixin.mq;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.annotation.MessageConsumer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqConsumerListener;
import com.niimbot.asset.framework.constant.ManageConstant;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.asset.system.adapter.weixin.model.WeixinCorp;
import com.niimbot.asset.system.adapter.weixin.service.WeixinContactsService;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCorpService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.weixin.base.constant.WxMqConstant;
import com.niimbot.asset.weixin.base.restops.RestOps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpTpPermanentCodeInfo;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.constant.WxCpTpConsts;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/10 16:39
 */
@Slf4j
@RequiredArgsConstructor
@MessageConsumer(topic = MqConstant.ASSET_TOPIC,
        group = WxMqConstant.ASSET_WEIXIN_CORP_CONSUMER_GROUP,
        tags = WxMqConstant.ASSET_WEIXIN_CORP_TAG)
public class WxCorpCallbackConsumer implements RocketMqConsumerListener<WxCpTpXmlMessage> {

    private final WxCpTpService wxCpTpService;
    private final WeixinCorpService weixinCorpService;
    private final RedissonClient redissonClient;
    private final WeixinContactsService contactsService;

    @Override
    public Action consume(WxCpTpXmlMessage message, ConsumeContext consumeContext) {
        try {
            switch (message.getInfoType()) {
                case WxCpTpConsts.InfoType.CREATE_AUTH:
                    createAuth(message);
                    break;
                case WxCpTpConsts.InfoType.CANCEL_AUTH:
                    cancelAuth(message);
                    break;
                case WxCpTpConsts.InfoType.CHANGE_AUTH:
                    changeAuth(message);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("WxCorpCallbackConsumer error, {}, [{}]", e.getMessage(),
                    JSONObject.toJSONString(message), e);
            return Action.ReconsumeLater;
        }
        return Action.CommitMessage;
    }

    private void createAuth(WxCpTpXmlMessage message) {
        WxCpTpPermanentCodeInfo permanentCodeInfo = RestOps.handleOrThrow(() -> wxCpTpService.getPermanentCodeInfo(message.getAuthCode()));
        log.info("PermanentCodeInfo data -> {}", JSONObject.toJSONString(permanentCodeInfo));
        // 幂等处理
        if (weixinCorpService.count(Wrappers.lambdaQuery(WeixinCorp.class)
                .eq(WeixinCorp::getPermanentCode, permanentCodeInfo.getPermanentCode())) > 0) {
            log.warn("weixinCorp {} has register", permanentCodeInfo.getAuthCorpInfo().getCorpId());
            return;
        }
        // 成员授权转组织授权
        String authCorpId = permanentCodeInfo.getAuthCorpInfo().getCorpId();
        WeixinCorp exist = weixinCorpService.getOne(
                Wrappers.lambdaQuery(WeixinCorp.class)
                        .likeRight(WeixinCorp::getCorpId, "DEL_" + authCorpId), false
        );
        if (Objects.nonNull(exist)) {
            weixinCorpService.remove(
                    Wrappers.lambdaUpdate(WeixinCorp.class)
                            .eq(WeixinCorp::getCorpId, exist.getCorpId())
            );
            WxCpTpPermanentCodeInfo.Agent agent = permanentCodeInfo.getAuthInfo().getAgents().get(0);
            exist.setCorpId(authCorpId)
                    .setAgentId(agent.getAgentId())
                    .setPermanentCode(permanentCodeInfo.getPermanentCode())
                    .setContent((JSONObject) JSONObject.toJSON(permanentCodeInfo))
                    .setCreateTime(LocalDateTime.now());
            weixinCorpService.save(exist);
            SpringUtil.getBean(CompanyService.class).update(Wrappers.lambdaUpdate(AsCompany.class)
                    .set(AsCompany::getStatus, ManageConstant.COMPANY_STATUS_ENABLE)
                    .eq(AsCompany::getId, exist.getCompanyId()));
            try {
                // 强制刷新一次token
                wxCpTpService.getCorpToken(exist.getCorpId(), exist.getPermanentCode(), true);
            } catch (WxErrorException e) {
                log.warn("weixinCorp getCorpToken error");
            }
            log.warn("weixinCorp {} has register for del", permanentCodeInfo.getAuthCorpInfo().getCorpId());
            return;
        }
        WeixinCorp weixinCorp = weixinCorpService.createOrUpdateCorp(permanentCodeInfo);
        RLock rLock = redissonClient.getLock("weixin-register:" + weixinCorp.getCorpId());
        rLock.lock();
        try {
            weixinCorpService.register(weixinCorp, permanentCodeInfo.getAuthUserInfo());
        } catch (Exception e) {
            log.error("register company {} error, {}", weixinCorp.getCorpId(), e.getMessage(), e);
        } finally {
            rLock.unlock();
        }
    }

    private void cancelAuth(WxCpTpXmlMessage message) {
        weixinCorpService.cancelAuth(message.getAuthCorpId());
    }

    private void changeAuth(WxCpTpXmlMessage message) {
        // 可见范围变更
        try {
            String authCorpId = message.getAuthCorpId();
            Long companyId = weixinCorpService.getCompanyId(authCorpId);
            contactsService.loadSave(companyId);
        } catch (Exception e) {
            log.error("可见范围变更事件处理异常", e);
        }
    }
}
