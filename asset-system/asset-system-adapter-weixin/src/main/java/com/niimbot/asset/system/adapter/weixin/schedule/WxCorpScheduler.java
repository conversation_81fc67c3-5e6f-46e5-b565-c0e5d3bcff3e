package com.niimbot.asset.system.adapter.weixin.schedule;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.niimbot.asset.system.adapter.weixin.mapper.WeixinSystemMapper;
import com.niimbot.asset.system.adapter.weixin.model.WeixinCorp;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCorpService;
import com.niimbot.asset.weixin.base.support.CorpCaches;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpTpAuthInfo;
import me.chanjar.weixin.cp.bean.license.WxCpTpLicenseCorpAccount;
import me.chanjar.weixin.cp.bean.license.account.WxCpTpLicenseCorpAccountListResp;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WxCorpScheduler {

    @Resource
    private WeixinSystemMapper weixinSystemMapper;

    @Resource
    private WeixinCorpService corpService;

    @Resource
    private ThreadPoolTaskExecutor executor;

    @Resource(name = "wxCpTpService")
    private WxCpTpService wxCpTpService;

    @Scheduled(cron = "0 0 2 * * ?")
    // @Scheduled(cron = "0 0/1 * * * ?")
    public void status() {
        List<WeixinCorp> corps = corpService.list(
                Wrappers.lambdaQuery(WeixinCorp.class)
                        .notLike(WeixinCorp::getCorpId, "DEL_")
        );
        if (CollUtil.isEmpty(corps)) {
            return;
        }
        List<List<WeixinCorp>> partition = Lists.partition(corps, 500);

        partition.forEach(part -> executor.execute(() -> {
            part.forEach(v -> {
                try {
                    WxCpTpAuthInfo authInfo = wxCpTpService.getAuthInfo(v.getCorpId(), CorpCaches.getPermanentCode(v.getCorpId()));
                    if (Objects.isNull(authInfo) || Objects.isNull(authInfo.getEditionInfo()) || CollUtil.isEmpty(authInfo.getEditionInfo().getAgents())) {
                        log.warn("Schedule WxCpTpAuthInfo is null corpid[{}]", v.getCorpId());
                        return;
                    }
                    Optional<WxCpTpAuthInfo.Agent> optional = authInfo.getEditionInfo().getAgents()
                            .stream().filter(a -> a.getAgentId().equals(v.getAgentId()))
                            .findFirst();
                    if (!optional.isPresent()) {
                        return;
                    }
                    // 应用状态
                    WxCpTpAuthInfo.Agent agent = optional.get();
                    v.setAppStatus(agent.getAppStatus());
                    // 授权码摘要
                    List<WxCpTpLicenseCorpAccount> accounts = new ArrayList<>(500);
                    getLicenseAccount(v, null, accounts);
                    long activeCount = accounts.stream()
                            .filter(c -> Objects.nonNull(c.getType()) && c.getType() == 1 && Objects.nonNull(c.getActiveTime()) && Objects.nonNull(c.getExpireTime()) && c.getExpireTime() < System.currentTimeMillis())
                            .count();
                    Integer nums = weixinSystemMapper.selectCompanyBaseAccountSum(v.getCompanyId());
                    v.setLicenseDigest(activeCount + "/" + (Objects.nonNull(nums) ? nums : 0));
                } catch (Exception e) {
                    log.warn("Schedule WxCpTpAuthInfo error", e);
                }
            });
            corpService.updateBatchById(part);
        }));
    }

    private void getLicenseAccount(WeixinCorp corp, String cursor, List<WxCpTpLicenseCorpAccount> accounts) throws WxErrorException {
        WxCpTpLicenseCorpAccountListResp resp = wxCpTpService.getWxCpTpLicenseService().getCorpAccountList(corp.getCorpId(), 1000, cursor);
        List<WxCpTpLicenseCorpAccount> orderList = resp.getOrderList();
        if (CollUtil.isNotEmpty(orderList)) {
            accounts.addAll(orderList);
        }
        Integer hasMore = resp.getHasMore();
        String nextCursor = resp.getNextCursor();
        if (hasMore == 0) {
            return;
        }
        getLicenseAccount(corp, nextCursor, accounts);
    }

}
