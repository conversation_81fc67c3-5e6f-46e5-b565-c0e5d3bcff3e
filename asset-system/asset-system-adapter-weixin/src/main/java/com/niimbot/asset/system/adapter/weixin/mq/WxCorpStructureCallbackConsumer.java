package com.niimbot.asset.system.adapter.weixin.mq;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.niimbot.asset.framework.annotation.MessageConsumer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqConsumerListener;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.adapter.weixin.service.WeixinContactsService;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCorpService;
import com.niimbot.asset.weixin.base.constant.WxConstant;
import com.niimbot.asset.weixin.base.constant.WxMqConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.constant.WxCpTpConsts;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

import static me.chanjar.weixin.cp.constant.WxCpConsts.ContactChangeType.*;

/**
 * <AUTHOR>
 * @date 2023/7/10 16:39
 */
@Slf4j
@RequiredArgsConstructor
@MessageConsumer(topic = MqConstant.ASSET_TOPIC,
        group = WxMqConstant.ASSET_WEIXIN_CORP_STRUCTURE_CONSUMER_GROUP,
        tags = WxMqConstant.ASSET_WEIXIN_CORP_STRUCTURE_TAG)
public class WxCorpStructureCallbackConsumer implements RocketMqConsumerListener<WxCpTpXmlMessage>, InitializingBean {

    private final WeixinCorpService corpService;

    private final WeixinContactsService contactsService;

    private final RedissonClient redissonClient;

    private final BiConsumer<Long, WxCpTpXmlMessage> defaultEventHandler = (id, message) -> log.warn("unmatch event [{}]", message);

    private final Map<String, BiConsumer<Long, WxCpTpXmlMessage>> eventMap = new ConcurrentHashMap<>(6);

    @Override
    public Action consume(WxCpTpXmlMessage message, ConsumeContext consumeContext) {
        Long companyId = corpService.getCompanyId(message.getAuthCorpId());
        if (!WxCpTpConsts.InfoType.CHANGE_CONTACT.equals(message.getInfoType())) {
            return Action.CommitMessage;
        }
        String lockKey = String.format(WxConstant.SYNC_LOCK, companyId);
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.isLocked()) {
            log.warn("load save processing reconsume later");
            return Action.ReconsumeLater;
        }
        RLock eventLock = null;
        try {
            // 员工事件
            if (CREATE_USER.equals(message.getChangeType()) || UPDATE_USER.equals(message.getChangeType()) || DELETE_USER.equals(message.getChangeType())) {
                eventLock = redissonClient.getLock(Edition.WEIXIN + ":user_event:" + companyId + ":" + message.getUserID());
            }
            // 组织事件
            if (CREATE_PARTY.equals(message.getChangeType()) || UPDATE_PARTY.equals(message.getChangeType()) || DELETE_PARTY.equals(message.getChangeType())) {
                eventLock = redissonClient.getLock(Edition.WEIXIN + ":org_event:" + companyId + ":" + message.getId());
            }
            // 处理中
            if (Objects.nonNull(eventLock) && eventLock.isLocked()) {
                log.info("weixin event is process [{}]", JSONObject.toJSONString(message));
                return Action.CommitMessage;
            }
            if (Objects.nonNull(eventLock)) {
                eventLock.lock();
            }
            eventMap.getOrDefault(message.getChangeType(), defaultEventHandler).accept(companyId, message);
        } catch (Exception e) {
            log.error("WxCorpStructureCallbackConsumer error, {}, [{}]", e.getMessage(), JSONObject.toJSONString(message), e);
            return Action.ReconsumeLater;
        } finally {
            if (Objects.nonNull(eventLock) && eventLock.isLocked()) {
                eventLock.unlock();
            }
        }
        return Action.CommitMessage;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        eventMap.put(CREATE_USER, contactsService::createUserEvent);
        eventMap.put(UPDATE_USER, contactsService::updateUserEvent);
        eventMap.put(DELETE_USER, contactsService::deleteUserEvent);
        eventMap.put(CREATE_PARTY, contactsService::createDepartEvent);
        eventMap.put(UPDATE_PARTY, contactsService::updateDepartEvent);
        eventMap.put(DELETE_PARTY, contactsService::deleteDepartEvent);
    }
}
