package com.niimbot.asset.system.adapter.weixin.service;

import com.niimbot.asset.weixin.base.dto.UpdateEmployeeNo;
import com.niimbot.asset.weixin.base.dto.UpdateOrgNo;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;

/**
 * <AUTHOR>
 */
public interface WeixinContactsService {

    String FOR_ADD = "for_add";

    String FOR_UPT = "for_upt";

    String FOR_DEL = "for_del";

    /**
     * 同步通讯录
     *
     * @param companyId 企业ID
     */
    void loadSave(Long companyId);

    /**
     * 更新员工编号
     *
     * @param updateEmployeeNo updateEmployeeNo
     * @return true if success
     */
    Boolean updateEmployeeNo(UpdateEmployeeNo updateEmployeeNo);

    /**
     * 更新组织编码
     *
     * @param updateOrgNo updateOrgNo
     * @return true if success
     */
    Boolean updateOrgNo(UpdateOrgNo updateOrgNo);

    /**
     * 新增员工事件
     *
     * @param companyId companyId
     * @param message   message
     */
    void createUserEvent(Long companyId, WxCpTpXmlMessage message);

    /**
     * 更新员工事件
     *
     * @param companyId companyId
     * @param message   message
     */
    void updateUserEvent(Long companyId, WxCpTpXmlMessage message);

    /**
     * 删除员工事件
     *
     * @param companyId companyId
     * @param message   message
     */
    void deleteUserEvent(Long companyId, WxCpTpXmlMessage message);

    /**
     * 新增部门事件
     *
     * @param companyId companyId
     * @param message   message
     */
    void createDepartEvent(Long companyId, WxCpTpXmlMessage message);

    /**
     * 更新部门事件
     *
     * @param companyId companyId
     * @param message   message
     */
    void updateDepartEvent(Long companyId, WxCpTpXmlMessage message);

    /**
     * 删除部门事件
     *
     * @param companyId companyId
     * @param message   message
     */
    void deleteDepartEvent(Long companyId, WxCpTpXmlMessage message);

}
