package com.niimbot.asset.system.adapter.weixin.controller;

import com.niimbot.asset.system.adapter.weixin.model.WeixinCorp;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCorpService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/12 15:35
 */
@RestController
@RequestMapping("/server/weixin/corp")
@RequiredArgsConstructor
public class WeixinCorpServiceController {

    private final WeixinCorpService weixinCorpService;

    @GetMapping()
    public WeixinCorp getCorp(@RequestParam(value = "corpId", required = false) String corpId,
                              @RequestParam(value = "companyId", required = false) Long companyId) {
        return weixinCorpService.getCorp(corpId, companyId);
    }

}
