package com.niimbot.asset.system.adapter.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.system.adapter.weixin.model.WeixinCorp;

import me.chanjar.weixin.cp.bean.WxCpTpPermanentCodeInfo;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:01
 */
public interface WeixinCorpService extends IService<WeixinCorp> {

    String getCorpId(Long companyId);

    Long getCompanyId(String corpId);

    WeixinCorp getCorp(String corpId, Long companyId);

    WeixinCorp preCreateCorp(String corpId);

    WeixinCorp createOrUpdateCorp(WxCpTpPermanentCodeInfo permanentCodeInfo);

    void register(WeixinCorp weixinCorp, WxCpTpPermanentCodeInfo.AuthUserInfo authUserInfo);

    void cancelAuth(String authCorpId);
}
