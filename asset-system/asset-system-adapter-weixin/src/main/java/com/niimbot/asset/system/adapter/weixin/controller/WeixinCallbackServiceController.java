package com.niimbot.asset.system.adapter.weixin.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.adapter.weixin.model.WeixinCallback;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCallbackService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;

/**
 * <AUTHOR>
 * @date 2023/7/4 14:03
 */
@Slf4j
@RestController
@RequestMapping("server/weixin/callback")
@RequiredArgsConstructor
public class WeixinCallbackServiceController {

    private final WeixinCallbackService weixinCallbackService;

    @PostMapping
    public Long save(@RequestBody WxCpTpXmlMessage message) {
        WeixinCallback callback = new WeixinCallback();
        callback.setId(IdUtils.getId());
        callback.setAuthCorpId(message.getAuthCorpId());
        callback.setContent((JSONObject) JSONObject.toJSON(message));
        callback.setInfoType(message.getInfoType());
        callback.setSuiteId(message.getSuiteId());
        callback.setTimeStamp(message.getTimeStamp());
        weixinCallbackService.save(callback);
        return callback.getId();
    }

}
