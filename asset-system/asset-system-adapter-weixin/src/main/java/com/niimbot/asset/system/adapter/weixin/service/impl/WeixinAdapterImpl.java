package com.niimbot.asset.system.adapter.weixin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.service.handler.WeixinAdapter;
import com.niimbot.asset.weixin.base.constant.WxConstant;
import com.niimbot.asset.weixin.base.dto.CorpInfo;
import com.niimbot.asset.weixin.base.restops.RestOps;
import com.niimbot.asset.weixin.base.support.CorpCaches;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.cp.bean.WxCpTpContactSearch;
import me.chanjar.weixin.cp.bean.WxCpTpContactSearchResp;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/24 11:12
 */
@Service
@RequiredArgsConstructor
public class WeixinAdapterImpl implements WeixinAdapter {

    private final WxCpTpService wxCpTpService;

    /**
     * 通讯录员工搜索
     *
     * @param queryWord
     * @return
     */
    @Override
    public List<String> empSearch(String queryWord) {
        if (StrUtil.isEmpty(queryWord)) {
            return ListUtil.empty();
        }
        WxCpTpContactSearchResp.QueryResult queryResult = contactSearch(queryWord, 1);
        WxCpTpContactSearchResp.QueryResult.User user = queryResult.getUser();
        if (user != null && CollUtil.isNotEmpty(user.getOpenUserId())) {
            return queryResult.getUser().getOpenUserId()
                    .stream().map(WxConstant::getContactEmpName)
                    .collect(Collectors.toList());
        } else {
            return ListUtil.empty();
        }
    }

    @Override
    public List<String> empSearch(String queryWord, Boolean fullMatch) {
        if (StrUtil.isEmpty(queryWord)) {
            return ListUtil.empty();
        }

        WxCpTpContactSearchResp.QueryResult queryResult = null;
        if (fullMatch) {
            queryResult = fullMatchSearch(queryWord, 1);
        } else {
            queryResult = contactSearch(queryWord, 1);
        }
        WxCpTpContactSearchResp.QueryResult.User user = queryResult.getUser();
        if (user != null && CollUtil.isNotEmpty(user.getOpenUserId())) {
            return queryResult.getUser().getUserid();
        } else {
            return ListUtil.empty();
        }
    }

    /**
     * 通讯录组织搜索
     *
     * @param queryWord
     * @return
     */
    @Override
    public List<String> orgSearch(String queryWord) {
        if (StrUtil.isEmpty(queryWord)) {
            return ListUtil.empty();
        }
        WxCpTpContactSearchResp.QueryResult queryResult = contactSearch(queryWord, 2);
        WxCpTpContactSearchResp.QueryResult.Party party = queryResult.getParty();
        if (party != null && CollUtil.isNotEmpty(party.getDepartmentId())) {
            return queryResult.getParty().getDepartmentId()
                    .stream().map(org -> WxConstant.getContactOrgName(Convert.toStr(org)))
                    .collect(Collectors.toList());
        } else {
            return ListUtil.empty();
        }
    }

    @Override
    public List<String> orgNameMatch(String orgName, Long companyId) {
        CorpInfo corp = CorpCaches.getCorp(companyId);
        WxCpTpContactSearch wxCpTpContactSearch = new WxCpTpContactSearch();
        wxCpTpContactSearch.setType(2)
                .setAuthCorpId(corp.getCorpId())
                .setQueryWord(orgName)
                .setFullMatchField(1)
                .setLimit(200);
        WxCpTpContactSearchResp searchResp = RestOps.handleOrThrow(() -> wxCpTpService.getWxCpTpContactService().contactSearch(wxCpTpContactSearch));
        if (Objects.isNull(searchResp) || Objects.isNull(searchResp.getQueryResult())) {
            return Collections.emptyList();
        }
        if (Objects.isNull(searchResp.getQueryResult().getParty())) {
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(searchResp.getQueryResult().getParty().getDepartmentId())) {
            return Collections.emptyList();
        }
        return searchResp.getQueryResult().getParty().getDepartmentId().stream().map(String::valueOf).distinct().collect(Collectors.toList());
    }

    private WxCpTpContactSearchResp.QueryResult contactSearch(String queryWord, Integer type) {
        CorpInfo corp = CorpCaches.getCorp(LoginUserThreadLocal.getCompanyId());
        WxCpTpContactSearch wxCpTpContactSearch = new WxCpTpContactSearch();
        wxCpTpContactSearch.setType(type)
                .setAuthCorpId(corp.getCorpId())
                .setQueryWord(queryWord)
                .setLimit(200);
        WxCpTpContactSearchResp searchResp = RestOps.handleOrThrow(() -> wxCpTpService.getWxCpTpContactService().contactSearch(wxCpTpContactSearch));
        return searchResp.getQueryResult();
    }

    private WxCpTpContactSearchResp.QueryResult fullMatchSearch(String queryWord, Integer type) {
        CorpInfo corp = CorpCaches.getCorp(LoginUserThreadLocal.getCompanyId());
        WxCpTpContactSearch wxCpTpContactSearch = new WxCpTpContactSearch();
        wxCpTpContactSearch.setType(type)
                .setAuthCorpId(corp.getCorpId())
                .setQueryWord(queryWord)
                .setFullMatchField(1)
                .setLimit(200);
        WxCpTpContactSearchResp searchResp = RestOps.handleOrThrow(() -> wxCpTpService.getWxCpTpContactService().contactSearch(wxCpTpContactSearch));
        return searchResp.getQueryResult();
    }

}
