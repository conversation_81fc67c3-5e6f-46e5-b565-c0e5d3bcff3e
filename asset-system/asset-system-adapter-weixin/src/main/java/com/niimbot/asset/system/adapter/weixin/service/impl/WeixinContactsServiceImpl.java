package com.niimbot.asset.system.adapter.weixin.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Affirm;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.SerialNumberUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheEmpStrategy;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.system.abs.MessageAbs;
import com.niimbot.asset.system.adapter.weixin.service.WeixinContactsService;
import com.niimbot.asset.system.adapter.weixin.service.WeixinCorpService;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsCusEmployeeSetting;
import com.niimbot.asset.system.model.AsCusMenu;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsRoleMenu;
import com.niimbot.asset.system.model.AsSyncChange;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeSettingService;
import com.niimbot.asset.system.service.AsDataPermissionService;
import com.niimbot.asset.system.service.AsRoleMenuService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.CompanyAssetService;
import com.niimbot.asset.system.service.CusAccountService;
import com.niimbot.asset.system.service.CusMenuService;
import com.niimbot.asset.system.service.CusRoleService;
import com.niimbot.asset.system.service.CusUserRoleService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.SyncChangeService;
import com.niimbot.asset.system.service.UserPreviewDeleteService;
import com.niimbot.asset.system.service.UserSensitiveAuthorityService;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.asset.weixin.base.constant.Jumps;
import com.niimbot.asset.weixin.base.constant.WxConstant;
import com.niimbot.asset.weixin.base.dto.UpdateEmployeeNo;
import com.niimbot.asset.weixin.base.dto.UpdateOrgNo;
import com.niimbot.asset.weixin.base.restops.RestOps;
import com.niimbot.asset.weixin.base.service.WxOpenApiService;
import com.niimbot.asset.weixin.base.service.impl.CuzWxCpTpDepartmentServiceImpl;
import com.niimbot.asset.weixin.base.service.impl.CuzWxCpTpUserServiceImpl;
import com.niimbot.asset.weixin.base.support.CorpCaches;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusEmployeeTransferDto;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxCpErrorMsgEnum;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpTpAuthInfo;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.templatecard.HorizontalContent;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeixinContactsServiceImpl implements WeixinContactsService {

    private final WxCpTpService wxCpTpService;

    private final WeixinCorpService weixinCorpService;

    private final OrgService orgService;

    private final AsCusEmployeeService employeeService;

    private final CusUserService accountService;

    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    private final AsDataPermissionService dataPermissionService;

    private final AsCusEmployeeExtService employeeExtService;

    private final AsCusEmployeeSettingService employeeSettingService;

    private final CusUserRoleService empRoleService;

    private final AsUserOrgService empOrgService;

    private final SyncChangeService syncChangeService;

    private final CompanyAssetService companyAssetService;

    private final UserPreviewDeleteService userPreviewDeleteService;

    private final AsAccountEmployeeService accountEmployeeService;

    private final CusMenuService menuService;

    private final CusRoleService roleService;

    private final AsRoleMenuService roleMenuService;

    private final WxOpenApiService openApiService;

    private final RedissonClient redissonClient;

    private final MessageAbs messageAbs;

    private final ModelDataScopeServiceImpl modelDataScopeService;

    private final DataAuthorityService dataAuthorityService;

    private final UserSensitiveAuthorityService userSensitiveAuthorityService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadSave(Long companyId) {
        String lockKey = String.format(WxConstant.SYNC_LOCK, companyId);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.isLocked()) {
                log.warn("weixin contacts load save is locked id[{}]", companyId);
                return;
            }
            lock.lock();
            // 1.获取企业corpId
            String corpId = weixinCorpService.getCorpId(companyId);
            // 2.获取全部组织信息
            List<AsOrg> wxOrgs = getAllOrgFromWeixin(companyId, corpId);
            Map<String, Long> wxOrgIdMap = wxOrgs.stream().filter(v -> !FOR_DEL.equals(v.getRemark())).collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, AsOrg::getId));
            List<Long> orgForDel = wxOrgs.stream().filter(v -> FOR_DEL.equals(v.getRemark())).map(AsOrg::getId).collect(Collectors.toList());
            // 3.企业微信所有用户
            InnerUser innerUser = getAllEmpFromWeixin(companyId, new ArrayList<>(wxOrgIdMap.keySet()), corpId);
            List<AsCusUser> accounts = innerUser.getAccounts();
            List<AsCusEmployee> employees = innerUser.getEmployees();
            List<UserMapping> userMappings = innerUser.getUserMap();
            ConcurrentMap<String, Long> userIdMap = userMappings.stream().collect(Collectors.toConcurrentMap(UserMapping::getUserId, UserMapping::getId));
            // 4.处理数据
            handleOrg(companyId, wxOrgs, userIdMap);
            handleEmployee(companyId, employees, userMappings, wxOrgIdMap);
            handleOrgRemove(companyId, orgForDel);
            handleAccount(companyId, accounts, userMappings.stream().collect(Collectors.toConcurrentMap(UserMapping::getOpenUserId, UserMapping::getId)));
        } finally {
            loadCompanyDictCache(companyId);
            lock.unlock();
        }
    }

    private void loadCompanyDictCache(Long companyId) {
        try {
            orgService.loadOrgCache(
                    orgService.list(new LambdaQueryWrapper<AsOrg>()
                            .select(AsOrg::getOrgName, AsOrg::getId)
                            .eq(AsOrg::getCompanyId, companyId))
            );
            employeeService.loadEmpCache(
                    employeeService.list(new LambdaQueryWrapper<AsCusEmployee>()
                            .select(AsCusEmployee::getEmpName, AsCusEmployee::getId)
                            .eq(AsCusEmployee::getCompanyId, companyId))
            );
        } catch (Exception e) {
            log.error("loadCompanyDictCache error", e);
        }
    }

    @Override
    public Boolean updateEmployeeNo(UpdateEmployeeNo updateEmployeeNo) {
        // 查询当前数据
        AsCusEmployee emp = employeeService.getById(updateEmployeeNo.getId());
        if (emp == null) {
            return false;
        }
        if (StrUtil.isNotBlank(updateEmployeeNo.getEmpNo())) {
            if (employeeService.count(new QueryWrapper<AsCusEmployee>().lambda()
                    .eq(AsCusEmployee::getEmpNo, updateEmployeeNo.getEmpNo())
                    .ne(AsCusEmployee::getId, updateEmployeeNo.getId())) > 0) {
                throw new BusinessException(SystemResultCode.PARAM_EXISTS, "员工工号", updateEmployeeNo.getEmpNo());
            }
        }
        employeeService.update(Wrappers.lambdaUpdate(AsCusEmployee.class)
                .set(AsCusEmployee::getEmpNo, StrUtil.isNotBlank(updateEmployeeNo.getEmpNo()) ? updateEmployeeNo.getEmpNo() : "")
                .set(StrUtil.isNotBlank(updateEmployeeNo.getRemark()), AsCusEmployee::getRemark, updateEmployeeNo.getRemark())
                .eq(AsCusEmployee::getId, updateEmployeeNo.getId()));
        SpringUtil.getBean(CacheEmpStrategy.class).evictCache(updateEmployeeNo.getId());
        return true;
    }

    @Override
    public Boolean updateOrgNo(UpdateOrgNo updateOrgNo) {
        // 查询当前数据
        AsOrg org = orgService.getById(updateOrgNo.getId());
        if (org == null) {
            return false;
        }
        if (StrUtil.isNotBlank(updateOrgNo.getOrgCode())) {
            if (orgService.count(new QueryWrapper<AsOrg>().lambda()
                    .eq(AsOrg::getOrgCode, updateOrgNo.getOrgCode())
                    .ne(AsOrg::getId, updateOrgNo.getId())) > 0) {
                throw new BusinessException(SystemResultCode.PARAM_EXISTS, "组织编码", updateOrgNo.getOrgCode());
            }
        }
        orgService.update(Wrappers.lambdaUpdate(AsOrg.class)
                .set(AsOrg::getOrgCode, StrUtil.isNotBlank(updateOrgNo.getOrgCode()) ? updateOrgNo.getOrgCode() : "")
                .eq(AsOrg::getId, updateOrgNo.getId()));
        SpringUtil.getBean(CacheOrgStrategy.class).evictCache(updateOrgNo.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUserEvent(Long companyId, WxCpTpXmlMessage message) {
        Optional<AsThirdPartyEmployee> optional = thirdPartyEmployeeService.getOne(WxConstant.WX, message.getUserID(), companyId);
        if (optional.isPresent()) {
            log.warn("createUserEvent user exist [{}]", message.getUserID());
            return;
        }
        WxCpUser wxCpUser = RestOps.handleOrThrow(() -> wxCpTpService.getWxCpTpUserService().getById(message.getUserID(), message.getAuthCorpId()));
        AsCusEmployee employee = toSystemEmp(null, companyId, wxCpUser);
        AsCusUser account = toSystemAccount(null, companyId, wxCpUser);
        List<String> depIds = Arrays.stream(wxCpUser.getDepartIds()).map(String::valueOf).collect(Collectors.toList());
        handleEmployeeCreate(
                companyId,
                Collections.singletonList(employee),
                Collections.singletonMap(employee.getId(), wxCpUser.getUserId()),
                Collections.singletonMap(employee.getId(), depIds),
                orgService.getExternalMapping(companyId, depIds)
        );
        handleAccount(companyId,
                Collections.singletonList(account),
                Collections.singletonMap(wxCpUser.getOpenUserId(), employee.getId())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserEvent(Long companyId, WxCpTpXmlMessage message) {
        Optional<AsThirdPartyEmployee> optional = thirdPartyEmployeeService.getOne(WxConstant.WX, message.getUserID(), companyId);
        if (!optional.isPresent()) {
            log.warn("updateUserEvent user not exist [{}]", message.getUserID());
            return;
        }
        AsCusUser account = accountService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getCompanyId, companyId).eq(AsCusUser::getAccount, message.getUserID()));
        if (Objects.isNull(account)) {
            log.warn("updateUserEvent account not exist [{}]", message.getUserID());
            return;
        }
        Long employeeId = optional.get().getEmployeeId();
        WxCpUser wxCpUser = RestOps.handleOrThrow(() -> wxCpTpService.getWxCpTpUserService().getById(message.getUserID(), message.getAuthCorpId()));
        AsCusEmployee employee = toSystemEmp(employeeId, companyId, wxCpUser);
        List<String> depIds = Arrays.stream(wxCpUser.getDepartIds()).map(String::valueOf).collect(Collectors.toList());
        handleEmployeeUpdate(
                companyId,
                Collections.singletonList(employee),
                Collections.singletonMap(employeeId, depIds),
                orgService.getExternalMapping(companyId, depIds)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserEvent(Long companyId, WxCpTpXmlMessage message) {
        Optional<AsThirdPartyEmployee> optional = thirdPartyEmployeeService.getOne(WxConstant.WX, message.getUserID(), companyId);
        if (!optional.isPresent()) {
            log.warn("deleteUserEvent user not exist [{}]", message.getUserID());
            return;
        }
        handleEmployeeRemove(companyId, Collections.singletonList(optional.get().getEmployeeId()));
        AsCusUser account = accountService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getCompanyId, companyId).eq(AsCusUser::getAccount, message.getUserID()));
        if (Objects.nonNull(account)) {
            account.setRemark(FOR_DEL);
            handleAccount(companyId, Collections.singletonList(account), Collections.emptyMap());
        }
    }

    @Override
    public void createDepartEvent(Long companyId, WxCpTpXmlMessage message) {
        AsOrg exist = orgService.getOne(Wrappers.lambdaQuery(AsOrg.class).select(AsOrg::getId).eq(AsOrg::getCompanyId, companyId).eq(AsOrg::getExternalOrgId, message.getId()));
        if (Objects.nonNull(exist)) {
            log.warn("createDepartEvent org exist [{}]", message.getId());
            return;
        }
        WxCpDepart wxCpDepart = RestOps.handleOrThrow(() -> ((CuzWxCpTpDepartmentServiceImpl) wxCpTpService.getWxCpTpDepartmentService()).get(Convert.toLong(message.getId()), message.getAuthCorpId()));
        AsOrg rootOrg = orgService.getRootOrg(companyId);
        AsOrg org = toSystemOrg(null, rootOrg.getId(), companyId, wxCpDepart);
        AsOrg parent = rootOrg;
        if (Objects.nonNull(wxCpDepart.getParentId())) {
            AsOrg one = orgService.getOne(Wrappers.lambdaQuery(AsOrg.class).eq(AsOrg::getCompanyId, companyId).eq(AsOrg::getExternalOrgId, String.valueOf(wxCpDepart.getParentId())));
            if (Objects.nonNull(one)) {
                parent = one;
            }
        }
        org.hierarchy(parent);
        handleOrg(
                companyId,
                Collections.singletonList(org),
                ArrayUtil.isEmpty(wxCpDepart.getDepartmentLeader()) ? Collections.emptyMap() : thirdPartyEmployeeService.getExternalMapping(companyId, Arrays.asList(wxCpDepart.getDepartmentLeader()))
        );
    }

    @Override
    public void updateDepartEvent(Long companyId, WxCpTpXmlMessage message) {
        if (WxConstant.WX_ROOT_DEP_ID.equals(message.getId())) {
            log.warn("updateDepartEvent is root org [{}]", message.getId());
            return;
        }
        AsOrg exist = orgService.getOne(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getExternalOrgId, message.getId())
        );
        if (Objects.isNull(exist)) {
            log.warn("updateDepartEvent org not exist [{}]", message.getId());
            return;
        }
        WxCpDepart wxCpDepart = RestOps.handleOrThrow(() -> ((CuzWxCpTpDepartmentServiceImpl) wxCpTpService.getWxCpTpDepartmentService()).get(Convert.toLong(message.getId()), message.getAuthCorpId()));
        AsOrg parent = orgService.getOne(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getExternalOrgId, wxCpDepart.getParentId().toString())
        );
        AsOrg rootOrg = orgService.getRootOrg(companyId);
        if (Objects.isNull(parent)) {
            parent = rootOrg;
        }
        AsOrg org = toSystemOrg(exist.getId(), rootOrg.getId(), companyId, wxCpDepart);
        org.hierarchy(parent);
        handleOrg(
                companyId,
                Collections.singletonList(org),
                ArrayUtil.isEmpty(wxCpDepart.getDepartmentLeader()) ? Collections.emptyMap() : thirdPartyEmployeeService.getExternalMapping(companyId, Arrays.asList(wxCpDepart.getDepartmentLeader()))
        );
    }

    @Override
    public void deleteDepartEvent(Long companyId, WxCpTpXmlMessage message) {
        if (WxConstant.WX_ROOT_DEP_ID.equals(message.getId())) {
            log.warn("deleteDepartEvent is root org [{}]", message.getId());
            return;
        }
        AsOrg exist = orgService.getOne(Wrappers.lambdaQuery(AsOrg.class).select(AsOrg::getId).eq(AsOrg::getCompanyId, companyId).eq(AsOrg::getExternalOrgId, message.getId()));
        if (Objects.isNull(exist)) {
            log.warn("deleteDepartEvent org not exist [{}]", message.getId());
            return;
        }
        AsOrg rootOrg = orgService.getRootOrg(companyId);
        if (Objects.equals(exist.getId(), rootOrg.getId())) {
            log.warn("deleteDepartEvent is root org");
            return;
        }
        handleOrgRemove(companyId, Collections.singletonList(exist.getId()));
    }

    private List<AsOrg> getAllOrgFromWeixin(Long companyId, String corpId) {
        List<WxCpDepart> departs = RestOps.handleOrThrow(() -> ((CuzWxCpTpDepartmentServiceImpl) wxCpTpService.getWxCpTpDepartmentService()).simpleList(null, corpId));
        if (CollUtil.isEmpty(departs)) {
            log.warn("企业微信通讯部门可见范围为空");
            return Collections.emptyList();
        }
        List<AsOrg> systemOrgs = orgService.getAll(companyId);
        ConcurrentMap<String, Long> idMap = systemOrgs.stream().collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, AsOrg::getId));
        AsOrg systemRootOrg = orgService.getRootOrg(companyId);
        departs.parallelStream().forEach(v -> {
            WxCpDepart details = RestOps.handleOrThrow(() -> ((CuzWxCpTpDepartmentServiceImpl) wxCpTpService.getWxCpTpDepartmentService()).get(v.getId(), corpId));
            v.setName(details.getName());
            v.setEnName(details.getEnName());
            v.setDepartmentLeader(details.getDepartmentLeader());
        });
        // 本次同步的部门ID集合
        Set<String> ids = departs.stream().map(v -> v.getId().toString()).collect(Collectors.toSet());
        // 企业微信当前所有的部门转换成云资产系统部门
        List<AsOrg> orgs = departs.stream()
                .map(v -> toSystemOrg(idMap.get(String.valueOf(v.getId())), systemRootOrg.getId(), companyId, v))
                .peek(v -> {
                    // 当前节点不是根节点 且 其父节点不在本地同步的范围内 直接挂靠在根节点下
                    if (!WxConstant.WX_ROOT_DEP_ID.equals(v.getExternalOrgId()) && !ids.contains(v.getExternalPid())) {
                        v.setExternalPid(WxConstant.WX_ROOT_DEP_ID);
                    }
                })
                .collect(Collectors.toList());
        // 处理层级
        if (!ids.contains(WxConstant.WX_ROOT_DEP_ID)) {
            orgs.add(systemRootOrg.setRemark(FOR_UPT));
        }
        orgs = handleOrgTree(orgs);
        // 云资产系统中存在而企业微信中不存在的部门(排除顶级节点)
        List<AsOrg> forDel = systemOrgs.stream()
                .filter(v -> !ids.contains(v.getExternalOrgId()) && !WxConstant.WX_ROOT_DEP_ID.equals(v.getExternalOrgId()))
                .peek(v -> v.setRemark(FOR_DEL)).collect(Collectors.toList());
        orgs.addAll(forDel);
        return orgs;
    }

    public InnerUser getAllEmpFromWeixin(Long companyId, List<String> externalOrgIds, String corpId) {
        if (CollUtil.isEmpty(externalOrgIds)) {
            return InnerUser.empty();
        }
        // 遍历获取部门下全部的员工(部门无权限时跳过)
        List<WxCpUser> wxCpUsers = Collections.synchronizedList(new ArrayList<>(externalOrgIds.size() * 10));
        externalOrgIds.parallelStream().forEach(id -> {
            try {
                List<WxCpUser> part = ((CuzWxCpTpUserServiceImpl) wxCpTpService.getWxCpTpUserService()).listSimpleByDepartment(Convert.toLong(id), false, null, corpId);
                wxCpUsers.addAll(part);
            } catch (WxErrorException e) {
                Affirm.isTrue(WxCpErrorMsgEnum.CODE_60011.getCode() == e.getError().getErrorCode(), "请求微信获取部门下的员工异常");
            }
        });
        // 分组去重
        ConcurrentMap<String, List<WxCpUser>> group = wxCpUsers.stream().collect(Collectors.groupingByConcurrent(WxCpUser::getUserId));
        wxCpUsers.clear();
        group.forEach((k, v) -> wxCpUsers.add(v.get(0).setDepartIds(v.stream().filter(u -> Objects.nonNull(u.getDepartIds())).map(u -> Arrays.asList(u.getDepartIds())).flatMap(Collection::stream).distinct().toArray(Long[]::new))));
        // 合并可见范围内的用户
        mergeAuthScopeUser(corpId, group.keySet(), wxCpUsers);
        group.clear();
        List<AsThirdPartyEmployee> partyEmployees = thirdPartyEmployeeService.listByCompanyId(companyId, "WEIXIN");
        Map<String, Long> empIdMap = partyEmployees.stream().collect(Collectors.toConcurrentMap(AsThirdPartyEmployee::getUserId, AsThirdPartyEmployee::getEmployeeId));
        List<AsCusUser> systemAccounts = accountService.list(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getCompanyId, companyId));
        Map<String, Long> accountIdMap = systemAccounts.stream().collect(Collectors.toConcurrentMap(AsCusUser::getUnionId, AsCusUser::getId));
        List<AsCusUser> accounts = new ArrayList<>(partyEmployees.size());
        List<AsCusEmployee> employees = new ArrayList<>(partyEmployees.size());
        List<UserMapping> userMappings = new ArrayList<>(partyEmployees.size());
        wxCpUsers.forEach(v -> {
            AsCusUser account = toSystemAccount(accountIdMap.get(v.getOpenUserId()), companyId, v);
            accounts.add(account);
            AsCusEmployee employee = toSystemEmp(empIdMap.get(v.getUserId()), companyId, v);
            employees.add(employee);
            UserMapping userMapping = new UserMapping()
                    .setId(employee.getId())
                    .setAccountId(account.getId())
                    .setUserId(v.getUserId())
                    .setOpenUserId(v.getOpenUserId())
                    .setOrgIds((Objects.isNull(v.getDepartIds()) || v.getDepartIds().length == 0) ? Collections.emptyList() : Arrays.stream(v.getDepartIds()).map(String::valueOf).collect(Collectors.toList()));
            userMappings.add(userMapping);
        });
        Map<String, String> idMap = wxCpUsers.stream().collect(Collectors.toConcurrentMap(WxCpUser::getUserId, WxCpUser::getOpenUserId));
        List<AsCusEmployee> empForDel = partyEmployees.stream()
                .filter(v -> !idMap.containsKey(v.getUserId()))
                .map(v -> new AsCusEmployee().setId(v.getEmployeeId()).setCompanyId(companyId).setRemark(FOR_DEL))
                .collect(Collectors.toList());
        employees.addAll(empForDel);
        List<AsCusUser> accountForDel = systemAccounts.stream()
                .filter(v -> !idMap.containsValue(v.getUnionId()))
                .peek(v -> v.setRemark(FOR_DEL))
                .collect(Collectors.toList());
        accounts.addAll(accountForDel);
        return new InnerUser(accounts, employees, userMappings);
    }

    private void mergeAuthScopeUser(String corpId, Set<String> userIds, List<WxCpUser> wxCpUsers) {
        // 授权范围内的用户
        WxCpTpAuthInfo auth = RestOps.handleOrThrow(() -> wxCpTpService.getAuthInfo(corpId, CorpCaches.getPermanentCode(corpId)));
        if (Objects.isNull(auth) || Objects.isNull(auth.getAuthInfo()) || CollUtil.isEmpty(auth.getAuthInfo().getAgents())) {
            return;
        }
        List<String> authUserIds = auth.getAuthInfo().getAgents().stream()
                .filter(v -> Objects.nonNull(v.getPrivilege()) && CollUtil.isNotEmpty(v.getPrivilege().getAllowUsers()))
                .map(v -> v.getPrivilege().getAllowUsers())
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        authUserIds.removeIf(userIds::contains);
        if (CollUtil.isEmpty(authUserIds)) {
            return;
        }
        authUserIds.forEach(id -> {
            try {
                wxCpUsers.add(wxCpTpService.getWxCpTpUserService().getById(id, corpId));
            } catch (WxErrorException e) {
                log.warn("请求企业微信获取用户[{}]信息异常", id, e);
            }
        });

    }

    private void handleOrg(Long companyId, List<AsOrg> orgs, Map<String, Long> externalUserIdMap) {
        if (CollUtil.isEmpty(orgs)) {
            return;
        }
        // 回写主管
        orgs.stream().filter(v -> CollUtil.isNotEmpty(v.getExternalDirector())).forEach(v -> {
            List<Long> leaderIds = v.getExternalDirector().stream().filter(externalUserIdMap::containsKey).map(externalUserIdMap::get).distinct().collect(Collectors.toList());
            v.setDirector(leaderIds);
        });
        // 新增或更新
        Map<String, List<AsOrg>> group = orgs.stream().collect(Collectors.groupingBy(AsOrg::getRemark));
        List<AsOrg> orgForAdd = group.getOrDefault(FOR_ADD, new ArrayList<>()).stream().peek(v -> v.setRemark(null)).collect(Collectors.toList());
        List<AsOrg> orgForUpt = group.getOrDefault(FOR_UPT, new ArrayList<>()).stream().peek(v -> v.setRemark(null)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(orgForAdd)) {
            String maxCode = orgService.getMaxCodeByCompanyId(companyId);
            for (int i = 0; i < orgForAdd.size(); i++) {
                AsOrg org = orgForAdd.get(i);
                if (i == 0) {
                    org.setOrgCode(SerialNumberUtils.getMaxCodeByCode(maxCode, orgService));
                } else {
                    org.setOrgCode(SerialNumberUtils.getMaxCodeByCode(orgForAdd.get(i - 1).getOrgCode(), orgService));
                }
            }
            orgService.saveBatch(orgForAdd);
        }
        if (CollUtil.isNotEmpty(orgForUpt)) {
            orgService.updateBatchById(orgForUpt);
        }
    }

    private void handleOrgRemove(Long companyId, List<Long> orgForDel) {
        if (CollUtil.isEmpty(orgForDel)) {
            return;
        }
        orgService.removeByIds(orgForDel);
        // 组织异动
        Set<Long> ids = companyAssetService.checkOrgAsset(orgForDel, companyId);
        if (CollUtil.isNotEmpty(ids)) {
            List<AsSyncChange> orgChange = ids.stream()
                    .map(formOrgId -> new AsSyncChange(IdUtils.getId(), companyId, formOrgId, 3, Collections.singletonList(formOrgId), 1).setToOrg(Collections.emptyList()))
                    .collect(Collectors.toList());
            syncChangeService.records(orgChange);
        }
    }

    private void handleEmployee(Long companyId, List<AsCusEmployee> employees, List<UserMapping> userMappings, Map<String, Long> orgIdMap) {
        if (CollUtil.isEmpty(employees)) {
            return;
        }
        Map<Long, String> idMap = userMappings.stream().collect(Collectors.toConcurrentMap(UserMapping::getId, UserMapping::getUserId));
        Map<Long, List<String>> userIdOrgIdMap = userMappings.stream().collect(Collectors.toConcurrentMap(UserMapping::getId, UserMapping::getOrgIds));
        Map<String, List<AsCusEmployee>> group = employees.stream().collect(Collectors.groupingBy(AsCusEmployee::getRemark));
        handleEmployeeCreate(companyId, group.get(FOR_ADD), idMap, userIdOrgIdMap, orgIdMap);
        handleEmployeeUpdate(companyId, group.get(FOR_UPT), userIdOrgIdMap, orgIdMap);
        handleEmployeeRemove(companyId, group.getOrDefault(FOR_DEL, new ArrayList<>()).stream().map(AsCusEmployee::getId).collect(Collectors.toList()));
    }

    private void handleEmployeeCreate(Long companyId, List<AsCusEmployee> empForAdd, Map<Long, String> idMap, Map<Long, List<String>> userIdOrgIdMap, Map<String, Long> orgIdMap) {
        if (CollUtil.isEmpty(empForAdd)) {
            return;
        }
        int capacity = empForAdd.size();
        List<AsCusEmployeeExt> extForAdd = new ArrayList<>(capacity);
        List<AsCusEmployeeSetting> settingForAdd = new ArrayList<>(capacity);
        List<AsThirdPartyEmployee> partyForAdd = new ArrayList<>(capacity);
        String maxEmpNo = employeeService.getRecommendEmpNo(companyId);
        for (int i = 0; i < empForAdd.size(); i++) {
            AsCusEmployee employee = empForAdd.get(i);
            employee.setRemark(null);
            if (i == 0) {
                employee.setEmpNo(maxEmpNo);
            } else {
                employee.setEmpNo(
                        StringUtils.getMaxIncreFromList(Collections.singletonList(empForAdd.get(i - 1).getEmpNo()), AsCusEmployeeService.DEFAULT_EMP_NO)
                );
            }
            extForAdd.add(new AsCusEmployeeExt().setId(employee.getId()).setType(1).setAccountStatus(3));
            settingForAdd.add(new AsCusEmployeeSetting().setUserId(employee.getId()));
            if (idMap.containsKey(employee.getId())) {
                partyForAdd.add(new AsThirdPartyEmployee().setId(IdUtils.getId()).setEmployeeId(employee.getId()).setCompanyId(companyId).setType("WEIXIN").setUserId(idMap.get(employee.getId())));
            }
        }
        employeeService.saveBatch(empForAdd);
        empForAdd.parallelStream().forEach(v -> dataPermissionService.initDataPermission(companyId, v.getId(), BaseConstant.COMMON_ROLE));
        employeeExtService.saveBatch(extForAdd);
        employeeSettingService.saveBatch(settingForAdd);
        List<AsUserOrg> userOrgs = toUserOrgs(empForAdd, userIdOrgIdMap, orgIdMap);
        empOrgService.saveBatch(userOrgs);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(companyId, userOrgs.stream().map(AsUserOrg::getUserId).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(partyForAdd)) {
            thirdPartyEmployeeService.saveBatch(partyForAdd);
        }
    }

    private void handleEmployeeUpdate(Long companyId, List<AsCusEmployee> empForUpt, Map<Long, List<String>> userIdOrgIdMap, Map<String, Long> orgIdMap) {
        if (CollUtil.isEmpty(empForUpt)) {
            return;
        }
        empForUpt.parallelStream().forEach(v -> v.setRemark(null));
        // 员工异动
        List<Long> ids = empForUpt.stream().map(AsCusEmployee::getId).collect(Collectors.toList());
        Map<Long, List<Long>> oldGroup = empOrgService.groupByUser(ids);
        List<AsUserOrg> userOrgs = toUserOrgs(empForUpt, userIdOrgIdMap, orgIdMap);
        Map<Long, List<Long>> newGroup = userOrgs.stream().collect(Collectors.groupingByConcurrent(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toList())));
        handleEmployeeChange(companyId, oldGroup, newGroup);
        employeeService.updateBatchById(empForUpt);
        empOrgService.removeByUser(ids);
        empOrgService.saveBatch(userOrgs);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(companyId, userOrgs.stream().map(AsUserOrg::getUserId).collect(Collectors.toList()));
    }

    private void handleEmployeeRemove(Long companyId, List<Long> empForDel) {
        if (CollUtil.isEmpty(empForDel)) {
            return;
        }
        AsCusEmployee admin = employeeService.getAdministratorByCompanyId(companyId);
        empForDel.remove(admin.getId());
        if (CollUtil.isEmpty(empForDel)) {
            return;
        }
        // 员工异动
        List<Long> ids = empForDel.stream().filter(v -> userPreviewDeleteService.notAllowRemove(v, companyId)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ids)) {
            List<AsSyncChange> empDeleteChange = ids.stream().map(id -> new AsSyncChange().setCompanyId(companyId).setId(IdUtils.getId()).setResId(id).setType(2).setStatus(1).setFromOrg(Collections.emptyList()).setToOrg(Collections.emptyList())).collect(Collectors.toList());
            syncChangeService.records(empDeleteChange);
            sendEmployeeRemoveMsg(companyId, empDeleteChange);
            messageAbs.sendEmpChangeMessage(companyId);
        }
        employeeService.removeByIds(empForDel);
        employeeExtService.removeByIds(empForDel);
        employeeSettingService.removeByIds(empForDel);
        thirdPartyEmployeeService.remove(Wrappers.lambdaUpdate(AsThirdPartyEmployee.class).in(AsThirdPartyEmployee::getEmployeeId, empForDel));
        empRoleService.remove(Wrappers.lambdaUpdate(AsUserRole.class).in(AsUserRole::getUserId, empForDel));
        empOrgService.removeByUser(empForDel);
        dataAuthorityService.removeByUserIds(empForDel);
        userSensitiveAuthorityService.removeByUserIds(empForDel, companyId);
        accountEmployeeService.batchUnbindEmploy(empForDel);
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(companyId, ids);
    }

    private List<String> getReceiverIds(Long companyId) {
        AsCusMenu menu = menuService.getOne(
                Wrappers.lambdaQuery(AsCusMenu.class)
                        .eq(AsCusMenu::getMenuCode, "emp-change")
        );
        // 企业角色ID集合
        List<Long> roleIds = roleService.list(
                Wrappers.lambdaQuery(AsCusRole.class)
                        .select(AsCusRole::getId, AsCusRole::getCompanyId)
                        .eq(AsCusRole::getStatus, 1)
                        .eq(AsCusRole::getCompanyId, companyId)
        ).stream().map(AsCusRole::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        // 获取企业能查看员工异动功能权限的人列表
        roleIds = roleMenuService.list(
                Wrappers.lambdaQuery(AsRoleMenu.class)
                        .select(AsRoleMenu::getRoleId)
                        .eq(AsRoleMenu::getMenuId, menu.getId())
                        .eq(AsRoleMenu::getType, "pc")
                        .in(AsRoleMenu::getRoleId, roleIds)
        ).stream().map(AsRoleMenu::getRoleId).distinct().collect(Collectors.toList());
        Set<Long> ids = new HashSet<>(32);
        ids.add(employeeService.getAdministratorByCompanyId(companyId).getId());
        if (CollUtil.isNotEmpty(roleIds)) {
            Set<Long> set = empRoleService.list(
                    Wrappers.lambdaQuery(AsUserRole.class)
                            .select(AsUserRole::getUserId)
                            .in(AsUserRole::getRoleId, roleIds)
            ).stream().map(AsUserRole::getUserId).collect(Collectors.toSet());
            ids.addAll(set);
        }
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return thirdPartyEmployeeService.list(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .in(AsThirdPartyEmployee::getEmployeeId, ids)
        ).stream().map(AsThirdPartyEmployee::getUserId).distinct().collect(Collectors.toList());
    }

    /**
     * 异动消息
     * XX离职资产消息提醒
     * 员工名称：XX
     * 所在部门：XX
     * 离职时间：2023.05.23
     * 管理资产：XX件
     * 在用资产：XX件
     *
     * @param companyId       企业ID
     * @param empDeleteChange 删除的员工ID集合
     */
    private void sendEmployeeRemoveMsg(Long companyId, List<AsSyncChange> empDeleteChange) {
        try {
            List<String> receiverIds = getReceiverIds(companyId);
            List<Long> ids = empDeleteChange.stream().map(AsSyncChange::getResId).collect(Collectors.toList());
            Map<Long, List<Long>> group = empOrgService.groupByUser(ids);
            empDeleteChange.forEach(change -> {
                List<HorizontalContent> contents = Lists.newArrayList(
                        HorizontalContent.builder().keyname("员工名称：").value(WxConstant.getContactEmpName(thirdPartyEmployeeService.getUserId(change.getResId()))).build(),
                        HorizontalContent.builder().keyname("所在部门：").value(group.get(change.getResId()).stream().map(oid -> WxConstant.getContactOrgName(orgService.getExternalId(oid))).collect(Collectors.joining("、"))).build(),
                        HorizontalContent.builder().keyname("离职时间：").value(LocalDateTime.now().format(DatePattern.NORM_DATE_FORMATTER)).build(),
                        HorizontalContent.builder().keyname("管理资产：").value(companyAssetService.checkManagerOwner(Collections.singletonList(change.getResId()), companyId).size() + "件").build(),
                        HorizontalContent.builder().keyname("在用资产：").value(companyAssetService.checkUsePerson(Collections.singletonList(change.getResId()), companyId).size() + "件").build()
                );
                WxCpMessage message = WxCpMessage.TEMPLATECARD()
                        .toUser(String.join("|", receiverIds))
                        .mainTitleTitle("员工异动").mainTitleDesc(WxConstant.getContactEmpName(thirdPartyEmployeeService.getUserId(change.getResId())) + "离职资产消息提醒")
                        .horizontalContents(contents)
                        .build();
                // openApiService.sendTplCardTextNoticeSpecialMsg(companyId, message, Jumps.EMP_CHANGE, Lists.newArrayList(2, change.getId()));
                openApiService.sendTplCardTextNoticeMessage(companyId, message, Jumps.EMP_CHANGE, Lists.newArrayList(2, change.getId()));
            });
        } catch (Exception e) {
            log.warn("员工异动微信消息发送失败", e);
        }
    }

    /**
     * 异动消息
     * XX变更组织，资产处理通知
     * 员工名称：XX
     * 旧部门：XX
     * 新部门：XX
     * 变更时间：2023.05.23
     * 使用资产：XX件
     *
     * @param companyId 企业ID
     * @param changes   变更
     */
    private void sendEmployeeChangeMsg(Long companyId, List<AsSyncChange> changes) {
        try {
            List<String> receiverIds = getReceiverIds(companyId);
            changes.forEach(v -> {
                String userName = WxConstant.getContactEmpName(thirdPartyEmployeeService.getUserId(v.getResId()));
                List<HorizontalContent> contents = Lists.newArrayList(
                        HorizontalContent.builder().keyname("员工名称：").value(userName).build(),
                        HorizontalContent.builder().keyname("旧部门：").value(v.getFromOrg().stream().map(id -> WxConstant.getContactOrgName(orgService.getExternalId(id))).collect(Collectors.joining("、"))).build(),
                        HorizontalContent.builder().keyname("新部门：").value(v.getToOrg().stream().map(id -> WxConstant.getContactOrgName(orgService.getExternalId(id))).collect(Collectors.joining("、"))).build(),
                        HorizontalContent.builder().keyname("变更时间：").value(LocalDateTime.now().format(DatePattern.NORM_DATE_FORMATTER)).build(),
                        HorizontalContent.builder().keyname("使用资产：").value(companyAssetService.checkUsePerson(Collections.singletonList(v.getResId()), companyId).size() + "件").build()
                );
                WxCpMessage message = WxCpMessage.TEMPLATECARD()
                        .toUser(String.join("|", receiverIds))
                        .mainTitleTitle("员工异动").mainTitleDesc(userName + "部门变更资产消息提醒")
                        .horizontalContents(contents)
                        .build();
                // openApiService.sendTplCardTextNoticeSpecialMsg(companyId, message, Jumps.EMP_CHANGE, Lists.newArrayList(v.getType(), v.getId()));
                openApiService.sendTplCardTextNoticeMessage(companyId, message, Jumps.EMP_CHANGE, Lists.newArrayList(v.getType(), v.getId()));
            });
        } catch (Exception e) {
            log.warn("员工异动微信消息发送失败", e);
        }
    }

    private void handleEmployeeChange(Long companyId, Map<Long, List<Long>> oldGroup, Map<Long, List<Long>> newGroup) {
        List<AsOrg> orgs = orgService.list(Wrappers.lambdaQuery(AsOrg.class).eq(AsOrg::getCompanyId, companyId));
        List<AsSyncChange> changes = new ArrayList<>(oldGroup.size());
        List<Long> kickoff = new ArrayList<>();
        oldGroup.forEach((k, v) -> {
            // 新增员工不用处理
            if (!newGroup.containsKey(k)) {
                return;
            }
            List<Long> form = new ArrayList<>(v);
            List<Long> to = new ArrayList<>(newGroup.get(k));
            String newOrgIdsToString = to.stream().sorted(Long::compare).map(String::valueOf).collect(Collectors.joining());
            String oldOrgIdsToString = new ArrayList<>(v).stream().sorted(Long::compare).map(String::valueOf).collect(Collectors.joining());
            // 没有变化不处理
            if (newOrgIdsToString.equals(oldOrgIdsToString)) {
                return;
            }
            // 异动的组织包含之前的组织不处理 A -> A || AB -> ABC || A -> ABC
            if (to.size() > 1 && new HashSet<>(to).containsAll(form)) {
                return;
            }
            // 自动处理 A->B, AB->B, 多个部门合并为一个【ABC->B】
            if (to.size() == 1) {
                // 最后一次未处理的员工部门异动
                AsSyncChange lastUntreatedRecord = syncChangeService.lastUntreatedRecord(companyId, k, 1);
                if (Objects.nonNull(lastUntreatedRecord) && CollUtil.isNotEmpty(lastUntreatedRecord.getFromOrg())) {
                    form.addAll(lastUntreatedRecord.getFromOrg().stream().distinct().filter(id -> !form.contains(id)).collect(Collectors.toList()));
                }
                form.removeAll(to);
                // 原部门
                List<AsOrg> formOrgs = CollUtil.isEmpty(form) ? new ArrayList<>(6) : orgs.stream().filter(org -> form.contains(org.getId())).collect(Collectors.toList());
                // 现部门
                List<AsOrg> toOrgs = CollUtil.isEmpty(to) ? new ArrayList<>(6) : orgs.stream().filter(org -> to.contains(org.getId())).collect(Collectors.toList());
                List<CusEmployeeTransferDto> trans = form.stream().map(id -> new CusEmployeeTransferDto().setFrom(id).setTo(to.get(0))).collect(Collectors.toList());
                employeeService.changeOrgLog(formOrgs, toOrgs, trans, k, companyId);
                // 删除还未处理的员工编辑异动记录
                syncChangeService.removeUntreatedRecord(companyId, k, 1);
                kickoff.add(k);
                return;
            }
            // 异动处理 A->BC, AB->BC, AB-CD
            // 如果没有使用的资产，也不记录异动信息，也不发消息
            if (userPreviewDeleteService.hasUseAsset(k, companyId)) {
                AsSyncChange empOrgChange = new AsSyncChange(IdUtils.getId(), companyId, k, 1, form, 1).setToOrg(to);
                changes.add(empOrgChange);
                kickoff.add(k);
            }
        });
        if (CollUtil.isNotEmpty(changes)) {
            syncChangeService.records(changes);
            sendEmployeeChangeMsg(companyId, changes);
            messageAbs.sendEmpChangeMessage(companyId);
        }
        if (CollUtil.isNotEmpty(kickoff)) {
            SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(
                    kickoff.stream().map(v -> new AsCusUser().setId(v).setCompanyId(companyId)).collect(Collectors.toList())
            );
        }
    }

    private void handleAccount(Long companyId, List<AsCusUser> accounts, Map<String, Long> externalUserIdMap) {
        if (CollUtil.isEmpty(accounts)) {
            return;
        }
        Map<String, List<AsCusUser>> group = accounts.stream().collect(Collectors.groupingBy(AsCusUser::getRemark));
        List<AsCusUser> accountForAdd = group.getOrDefault(FOR_ADD, new ArrayList<>()).stream().peek(v -> v.setRemark(null)).collect(Collectors.toList());
        List<AsCusUser> accountForUpt = group.getOrDefault(FOR_UPT, new ArrayList<>()).stream().peek(v -> v.setRemark(null)).collect(Collectors.toList());
        List<AsCusUser> accountForDel = group.getOrDefault(FOR_DEL, new ArrayList<>());
        if (CollUtil.isNotEmpty(accountForAdd)) {
            List<AsAccountEmployee> forAdd = accountForAdd.stream().filter(v -> externalUserIdMap.containsKey(v.getUnionId())).map(v -> new AsAccountEmployee(v.getId(), companyId, externalUserIdMap.get(v.getUnionId()))).collect(Collectors.toList());
            accountService.saveBatch(accountForAdd);
            accountEmployeeService.saveBatch(forAdd);
        }
        if (CollUtil.isNotEmpty(accountForUpt)) {
            accountService.updateBatchById(accountForUpt);
        }
        if (CollUtil.isNotEmpty(accountForDel)) {
            AsCusEmployee admin = employeeService.getAdministratorByCompanyId(companyId);
            AsThirdPartyEmployee party = thirdPartyEmployeeService.getOne(Wrappers.lambdaQuery(AsThirdPartyEmployee.class).eq(AsThirdPartyEmployee::getCompanyId, companyId).eq(AsThirdPartyEmployee::getEmployeeId, admin.getId()));
            if (Objects.nonNull(party)) {
                accountForDel.removeIf(v -> v.getThirdPartyId().equals(party.getUserId()));
            }
            if (CollUtil.isEmpty(accountForDel)) {
                return;
            }
            Map<Long, String> idMap = accountForDel.stream().collect(Collectors.toMap(AsCusUser::getId, AsCusUser::getUnionId));
            List<AsCusUser> kickOffUser = accountEmployeeService.list(Wrappers.lambdaQuery(AsAccountEmployee.class).eq(AsAccountEmployee::getCompanyId, companyId).in(AsAccountEmployee::getAccountId, idMap.keySet()))
                    .stream().map(v -> new AsCusUser().setId(v.getEmployeeId()).setCompanyId(v.getCompanyId()))
                    .collect(Collectors.toList());
            accountService.removeByIds(idMap.keySet());
            accountEmployeeService.remove(Wrappers.lambdaUpdate(AsAccountEmployee.class).eq(AsAccountEmployee::getCompanyId, companyId).in(AsAccountEmployee::getAccountId, idMap.keySet()));
            // 踢出登录
            SpringUtil.getBean(CusAccountService.class).kickOffLoginUser(kickOffUser);
        }
    }

    private List<AsOrg> handleOrgTree(List<AsOrg> orgs) {
        List<Tree<String>> trees = TreeUtil.build(orgs, "0", (org, treeNode) -> {
            treeNode.setId(org.getExternalOrgId());
            treeNode.setParentId(org.getExternalPid());
            treeNode.setWeight(org.getSortNum());
        });
        Map<String, AsOrg> idMap = orgs.stream().collect(Collectors.toMap(AsOrg::getExternalOrgId, v -> v));
        trees.forEach(node -> treeStructure(idMap, node, new Relation(0, "0,", 0L)));
        return new ArrayList<>(idMap.values());
    }

    private void treeStructure(Map<String, AsOrg> idMap, Tree<String> node, Relation relation) {
        String id = node.getId();
        AsOrg oneself = idMap.get(id);
        oneself.setPid(relation.pid);
        oneself.setLevel(relation.level);
        oneself.setPaths(relation.paths);
        if (oneself.getPid().equals(0L) && (oneself.getLevel().equals(0) || oneself.getLevel().equals(1))) {
            oneself.setOrgType(1);
        } else {
            oneself.setOrgType(2);
        }
        List<Tree<String>> children = node.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            Relation sonRelation = new Relation(relation.level + 1, relation.paths + oneself.getId() + ",", oneself.getId());
            children.forEach(son -> treeStructure(idMap, son, sonRelation));
        }
    }

    private AsOrg toSystemOrg(Long id, Long systemRootId, Long companyId, WxCpDepart wxCpDepart) {
        AsOrg org = new AsOrg();
        org.setCompanyId(companyId);
        org.setExternalOrgId(String.valueOf(wxCpDepart.getId()));
        org.setExternalPid(String.valueOf(wxCpDepart.getParentId()));
        org.setSortNum(Convert.toInt(wxCpDepart.getOrder()));
        org.setOrgName(WxConstant.getContactOrgName(String.valueOf(wxCpDepart.getId())));
        org.setCompanyOwner(systemRootId);
        org.setExternalDirector(Arrays.asList(wxCpDepart.getDepartmentLeader()));
        org.setId(Objects.isNull(id) ? IdUtils.getId() : id);
        org.setRemark(Objects.isNull(id) ? FOR_ADD : FOR_UPT);
        // 给个默认类型
        org.setOrgType(2);
        return org;
    }

    private AsCusEmployee toSystemEmp(Long id, Long companyId, WxCpUser wxCpUser) {
        AsCusEmployee emp = new AsCusEmployee();
        emp.setCompanyId(companyId);
        emp.setEmpName(WxConstant.getContactEmpName(wxCpUser.getUserId()));
        emp.setEmail(wxCpUser.getEmail()).setMobile(wxCpUser.getMobile()).setPosition(wxCpUser.getPosition());
        emp.setId(Objects.isNull(id) ? IdUtils.getId() : id);
        emp.setRemark(Objects.isNull(id) ? FOR_ADD : FOR_UPT);
        emp.setDataScope(DataPermRuleType.TYPE_ALL.getValue().shortValue());
        return emp;
    }

    private AsCusUser toSystemAccount(Long id, Long companyId, WxCpUser wxCpUser) {
        AsCusUser account = new AsCusUser();
        account.setAccount(wxCpUser.getUserId());
        account.setUnionId(wxCpUser.getOpenUserId());
        account.setThirdPartyId(wxCpUser.getUserId());
        account.setCompanyId(companyId);
        account.setId(Objects.isNull(id) ? IdUtils.getId() : id);
        account.setRemark(Objects.isNull(id) ? FOR_ADD : FOR_UPT);
        return account;
    }

    private List<AsUserOrg> toUserOrgs(List<AsCusEmployee> employees, Map<Long, List<String>> userIdOrgIdMap, Map<String, Long> orgIdMap) {
        Long rootOrgId = orgService.getRootOrg(employees.get(0).getCompanyId()).getId();
        return employees.stream()
                .filter(v -> userIdOrgIdMap.containsKey(v.getId()))
                .map(v -> {
                    List<String> depIds = userIdOrgIdMap.get(v.getId());
                    depIds.removeIf(id -> !orgIdMap.containsKey(id));
                    // 说明同步过来的用户部门为空或者部门与本次同步的任意一个部门匹配，默认挂到根组织上
                    return CollUtil.isEmpty(depIds) ? Collections.singletonList(new AsUserOrg(v.getId(), rootOrgId)) : depIds.stream().map(eid -> new AsUserOrg(v.getId(), orgIdMap.get(eid))).collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Relation {

        private Integer level;

        private String paths;

        private Long pid;

    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    static class UserMapping {
        private Long id;
        private Long accountId;
        private String userId;
        private String openUserId;
        private List<String> orgIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class InnerUser {
        List<AsCusUser> accounts;
        List<AsCusEmployee> employees;
        List<UserMapping> userMap;

        static InnerUser empty() {
            InnerUser innerUser = new InnerUser();
            innerUser.setAccounts(Collections.emptyList());
            innerUser.setEmployees(Collections.emptyList());
            innerUser.setUserMap(Collections.emptyList());
            return innerUser;
        }

    }
}
