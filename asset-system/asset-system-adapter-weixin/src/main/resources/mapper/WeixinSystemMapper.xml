<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.adapter.weixin.mapper.WeixinSystemMapper">

  <resultMap id="CusEmployeeDto3" type="com.niimbot.system.CusEmployeeDto">
    <id column="id" property="id"/>
    <result column="company_id" property="companyId"/>
    <result column="account" property="account"/>
    <result column="emp_name" property="empName"/>
    <result column="emp_no" property="empNo"/>
    <result column="mobile" property="mobile"/>
    <result column="national_code" property="nationalCode"/>
    <result column="email" property="email"/>
    <result column="position" property="position"/>
    <result column="status" property="status"/>
    <result column="status_text" property="statusText"/>
    <result column="account_status" property="accountStatus"/>
    <result column="account_id" property="accountId"/>
    <result column="account_activation_status" property="accountActivationStatus"/>
    <result column="emp_type" property="empType"/>
    <result column="emp_type_text" property="empTypeText"/>
  </resultMap>

  <select id="selectCustomPage" resultMap="CusEmployeeDto3">
    SELECT
    as_cus_employee.id,
    as_cus_employee.emp_name,
    as_cus_employee.emp_no,
    as_cus_employee.mobile,
    IFNULL(as_cus_employee.national_code, '+86') as national_code,
    as_cus_employee.email,
    as_cus_employee.position,
    as_cus_employee.company_id,
    as_cus_employee.status,
    t3.label as status_text,
    t4.account_id,
    t11.account_status,
    case
    when t11.account_status=1 then '待激活'
    when t11.account_status=2 then '预激活'
    when (t11.account_status = 3 and (t12.id is null or t12.expire_time &gt; now())) then '已激活'
    when (t11.account_status = 3 and (t12.id is not null and t12.expire_time &lt; now())) then '已过期'
    else '未知'
    end as account_activation_status,
    t10.account,
    t11.type as emp_type,
    case t11.type
    when 1 then '系统员工'
    when 2 then '自建员工'
    when 3 then '自建员工'
    else '未知'
    end as emp_type_text
    from
    as_cus_employee
    left join as_dict_data t3 on t3.value = as_cus_employee.status
    and t3.dict_type = 'work_status' and t3.status = 1
    left join as_account_employee t4 on as_cus_employee.id = t4.employee_id
    left join as_cus_user t10 on t4.account_id = t10.id
    left join as_cus_employee_ext t11 on as_cus_employee.id = t11.id
    left join weixin_active_code t12 on as_cus_employee.id = t12.emp_id
    <where>
      as_cus_employee.company_id = #{companyId}
      <if test="orgIds!=null and orgIds.size>0">
        AND as_cus_employee.id in (SELECT t7.user_id FROM as_user_org t7 JOIN as_org t8 on
        t7.org_id = t8.id AND
        t8.id in
        <foreach collection="orgIds" item="orgId" index="index" open="(" close=")"
                 separator=",">
          #{orgId}
        </foreach>
        )
      </if>
      <choose>
        <when test="em.isDelete!=null and em.isDelete==1">
          and as_cus_employee.is_delete = 1
        </when>
        <otherwise>
          and as_cus_employee.is_delete = 0
        </otherwise>
      </choose>
      <if test="em.id!=null">
        and as_cus_employee.id= #{em.id}
      </if>
      <if test="em.empName!=null and em.empName!=''">
        and as_cus_employee.emp_name like concat('%',#{em.empName},'%')
      </if>
      <if test="em.empNo!=null and em.empNo!=''">
        and as_cus_employee.emp_no= #{em.empNo}
      </if>
      <if test="em.mobile!=null and em.mobile!=''">
        and as_cus_employee.mobile like concat('%',#{em.mobile},'%')
      </if>
      <if test="em.position!=null and em.position!=''">
        and as_cus_employee.position like concat('%',#{em.position},'%')
      </if>
      <if test="em.status!=null">
        and as_cus_employee.status = #{em.status}
      </if>
      <if test="em.mobileType!=null and em.mobileType!='' and em.mobileType =='1'.toString()">
        and (as_cus_employee.mobile is null or as_cus_employee.mobile = "")
      </if>
      <if test="em.mobileType!=null and em.mobileType!='' and em.mobileType=='2'.toString()">
        and as_cus_employee.mobile != ""
      </if>
      <if test="em.accountType != null and em.accountType != '' and em.accountType != 4 and em.accountType != 5">
        and t11.account_status = #{em.accountType}
      </if>
      <if test="em.accountType != null and em.accountType == 4">
        and t11.type = 2
      </if>
      <if test="em.accountType != null and em.accountType == 5">
        and t12.id is not null and t12.expire_time &lt; now()
      </if>
      <if test="em.kw!=null and em.kw!=''">
          and (
          (as_cus_employee.emp_name like concat('%',#{em.kw},'%'))
          or
          (as_cus_employee.emp_no like concat('%',#{em.kw},'%'))
          or
          (as_cus_employee.mobile like concat('%',#{em.kw},'%'))
          or
          (as_cus_employee.email like concat('%',#{em.kw},'%'))
          <if test="unionIds != null and unionIds.size() > 0">
              or as_cus_employee.emp_name in
              <foreach collection="unionIds" item="id" index="index" open="(" close=")"
                       separator=",">
                  #{id}
              </foreach>
          </if>
          )
      </if>
        <!-- 钉钉小程序端离职员工转移审批时不能选择没有账号的员工 -->
        <if test="em.isNotShowNoAccountEmp != null and em.isNotShowNoAccountEmp == true">
            and t11.account_status = 3
        </if>
    </where>
    order by as_cus_employee.${em.sidx} ${em.order}
    <if test="em.sidx == 'create_time'">
      , as_cus_employee.id ${em.order}
    </if>
  </select>

  <select id="selectCompanyBaseAccountSum" resultType="java.lang.Integer">
    SELECT SUM(base_count) AS account_nums FROM weixin_license_order WHERE company_id = #{companyId} AND order_status = 1
  </select>

</mapper>