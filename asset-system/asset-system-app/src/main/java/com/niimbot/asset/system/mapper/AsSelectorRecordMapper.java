package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsSelectorRecord;
import com.niimbot.system.SelectorRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AsSelectorRecordMapper extends BaseMapper<AsSelectorRecord> {

    List<SelectorRecord> selectIdsFromTable(@Param("columns") String columns, @Param("table") String table, @Param("ids") List<Long> ids);

}
