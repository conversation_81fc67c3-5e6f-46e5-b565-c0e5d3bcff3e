package com.niimbot.asset.system.controller;


import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.ImportErrorAbs;
import com.niimbot.asset.system.dto.ImportErrorDeleteCmd;
import com.niimbot.asset.system.model.AsImportTask;
import com.niimbot.asset.system.service.AsImportTaskService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.ImportTaskPageQueryDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 导入导出任务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@Slf4j
@RestController
@RequestMapping("server/system/import")
@RequiredArgsConstructor
public class AsImportTaskServiceController {
    private final AsImportTaskService importTaskService;
    private final ImportErrorAbs importErrorAbs;

    @PostMapping
    public Long save(@RequestBody AsImportTask task) {
        // 只限制导入
        long count = importTaskService.count(
                Wrappers.<AsImportTask>lambdaQuery()
                        .eq(AsImportTask::getType, DictConstant.TASK_TYPE_IMPORT)
                        .eq(AsImportTask::getImportType, task.getImportType())
                        .eq(AsImportTask::getTaskStatus, DictConstant.IMPORT_STATUS_DOING));

        if (Edition.isLocal()) {
            if (!task.getType().equals(DictConstant.TASK_TYPE_EXPORT)) {
                if (count > 0) {
                    throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
                }
            }
        } else {
            if (count > 0) {
                log.info("存在进行中的导入任务, {}", JSONObject.toJSONString(task));
                throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
            }
        }

        if (importTaskService.save(task)) {
            return task.getId();
        }
        return null;
    }

    @PutMapping
    public Boolean update(@RequestBody AsImportTask task) {
        return importTaskService.updateById(task);
    }

    @DeleteMapping("/{id}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(@PathVariable("id") Long id) {
        AsImportTask task = importTaskService.getById(id);
        if (task == null) {
            return true;
        }
        if (DictConstant.IMPORT_STATUS_DOING == task.getTaskStatus()) {
            BusinessExceptionUtil.throwException("正在进行的任务无法删除");
        }
        return importTaskService.removeById(id) &&
                importErrorAbs.deleteImportError(new ImportErrorDeleteCmd().setTaskId(id));
    }

    @DeleteMapping("/all/{type}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteAll(@PathVariable("type") Integer type) {
        long count = importTaskService.count(
                Wrappers.<AsImportTask>lambdaQuery()
                        .eq(AsImportTask::getTaskStatus, DictConstant.IMPORT_STATUS_DOING)
                        .eq(AsImportTask::getType, type)
                        .eq(AsImportTask::getCreateBy, LoginUserThreadLocal.getCurrentUserId()));
        if (count > 0) {
            throw new BusinessException(SystemResultCode.IMPORT_FILE_PROGRESS);
        }
        List<AsImportTask> tasks = importTaskService.list(Wrappers.<AsImportTask>lambdaQuery()
                .eq(AsImportTask::getType, type)
                .eq(AsImportTask::getCreateBy, LoginUserThreadLocal.getCurrentUserId()));
        List<Long> ids = tasks.stream().map(AsImportTask::getId).collect(Collectors.toList());

        if (type.equals(DictConstant.TASK_TYPE_IMPORT)) {
            return importTaskService.removeByIds(ids) &&
                    importErrorAbs.deleteImportError(new ImportErrorDeleteCmd().setTaskIds(ids));
        }

        return importTaskService.removeByIds(ids);
    }

    @GetMapping("/page")
    public IPage<AsImportTask> page(ImportTaskPageQueryDto query) {
        Integer type = query.getType();
        LambdaQueryWrapper<AsImportTask> queryWrapper = Wrappers.<AsImportTask>lambdaQuery().eq(AsImportTask::getType, type);
        queryWrapper.eq(AsImportTask::getCreateBy, LoginUserThreadLocal.getCurrentUserId());
        /*if (type.equals(DictConstant.TASK_TYPE_EXPORT)) {
            LocalDateTime pre30Date = LocalDateTimeUtil.offset(LocalDateTimeUtil.now(), -30, ChronoUnit.DAYS);
            queryWrapper.eq(AsImportTask::getCreateBy, LoginUserThreadLocal.getCurrentUserId())
                    .ge(AsImportTask::getCreateTime, pre30Date);
        }*/
        queryWrapper = queryWrapper.orderByDesc(AsImportTask::getCreateTime);
        return importTaskService.page(query.buildIPage(), queryWrapper);
    }

    @GetMapping("/{id}")
    public AsImportTask queryById(@PathVariable("id") Long id) {
        return importTaskService.getById(id);
    }

    @GetMapping("/amount/{type}")
    public Integer amount(@PathVariable("type") Integer type) {
        LambdaQueryWrapper<AsImportTask> queryWrapper = Wrappers.<AsImportTask>lambdaQuery()
                .eq(AsImportTask::getType, type)
                .in(AsImportTask::getTaskStatus, Arrays.asList(DictConstant.IMPORT_STATUS_DOING,
                        DictConstant.IMPORT_STATUS_FAIL));
        queryWrapper.eq(AsImportTask::getCreateBy, LoginUserThreadLocal.getCurrentUserId());
        /*if (type.equals(DictConstant.TASK_TYPE_EXPORT)) {
            LocalDateTime pre30Date = LocalDateTimeUtil.offset(LocalDateTimeUtil.now(), -30, ChronoUnit.DAYS);
            queryWrapper.eq(AsImportTask::getCreateBy, LoginUserThreadLocal.getCurrentUserId())
                    .ge(AsImportTask::getCreateTime, pre30Date);
        }*/
        return Convert.toInt(importTaskService.count(queryWrapper));
    }

    @GetMapping("/count/{type}/{orderType}")
    public Integer count(@PathVariable("type") Integer type, @PathVariable("orderType") Integer orderType) {
        return Convert.toInt(importTaskService.count(
                Wrappers.<AsImportTask>lambdaQuery()
                        .eq(AsImportTask::getType, type)
                        .eq(AsImportTask::getImportType, orderType)
                        .eq(AsImportTask::getCreateBy, LoginUserThreadLocal.getCurrentUserId())));
    }

}
