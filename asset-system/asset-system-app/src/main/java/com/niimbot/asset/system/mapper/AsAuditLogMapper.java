package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.system.model.AsAuditLog;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogSearch;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface AsAuditLogMapper extends BaseMapper<AsAuditLog> {

    Page<AuditLogDto> search(Page<AuditLogSearch> page, @Param("em") AuditLogSearch search);

}
