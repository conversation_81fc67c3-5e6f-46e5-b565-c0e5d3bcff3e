package com.niimbot.asset.system.service.handler;

import com.niimbot.asset.framework.constant.CompanyPaymentStatusEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.CompanyResourceView;
import com.niimbot.asset.system.service.AsConfigService;
import com.niimbot.asset.system.service.CompanyResourceViewService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CompanyPaymentStatusDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 主要处理已过期和已流失的企业付费状态，其他的状态在别的PaymentStatusHandler处理
 *
 * <AUTHOR>
 * @date 2022/12/8 上午11:40
 */
@Component
public class InvalidPaymentStatusHandler implements PaymentStatusHandler {

    //在状态处理链中的优先级，由于有短路存在，优先级设置比较重要
    private final static Integer HANDLER_PRIORITY = 1;

    @Autowired
    private AsConfigService configService;

    @Autowired
    private CompanyResourceViewService companyResourceViewService;

    @Override
    public Integer handlePaymentStatus(Long companyId) {
        CompanyResourceView companyResource = companyResourceViewService.selectCompanyResource(companyId);
        //没有购买资源包或没有过期时间的(企业注册就会送体验版资源包N条，企业初始数据都没有购买资源包，也就没有过期时间)，这里无法判断付费状态，留给后续的PaymentStatusHandler处理
        if (Objects.isNull(companyResource) || Objects.isNull(companyResource.getExpirationTime())) {
            return null;
        }

        //判断是否过期，没有过期就交给下一个PaymentStatusHandler处理
        if (LocalDateTime.now().isBefore(companyResource.getExpirationTime())) {
            return null;
        }

        //企业资源包已过期
        Integer result = CompanyPaymentStatusEnum.INVALID.getStatusCode();

        //查询企业付费状态配置，没有配置直接返回已过期
        CompanyPaymentStatusDto paymentStatusConfig = configService.queryCompanyPaymentStatus();
        if (Objects.isNull(paymentStatusConfig.getInvalidDayMin())) {
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "企业付费状态配置错误，失效日期未配置!");
        }

        //失效日期超过配置值，返回已流失状态
        long betweenDays = Duration.between(LocalDateTime.now(), companyResource.getExpirationTime()).abs().toDays();
        if (betweenDays >= paymentStatusConfig.getInvalidDayMin()) {
            result = CompanyPaymentStatusEnum.LOST.getStatusCode();
        }
        return result;
    }

    @Override
    public Integer priority() {
        return HANDLER_PRIORITY;
    }
}
