package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.RecycleBinService;
import com.niimbot.system.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/system/recycleBin")
public class RecycleBinServiceController {

    private final RecycleBinService recycleBinService;

    @PostMapping("/recycle")
    public Boolean recycle(@RequestBody ResRecycle resRecycle) {
        return recycleBinService.recycle(resRecycle);
    }

    @PostMapping("/restore")
    public Boolean restore(@RequestBody ResRestore resRestore) {
        return recycleBinService.restore(resRestore);
    }

    @PostMapping("/release")
    public Boolean release(@RequestBody ResRelease resRelease) {
        return recycleBinService.release(resRelease);
    }

    @GetMapping("/details")
    public RecycleBin details(GetRecycleBins get) {
        return recycleBinService.details(get);
    }

    @GetMapping("/getByResId")
    public RecycleBin get(GetRecycleBins getRecycleBins) {
        return recycleBinService.get(getRecycleBins);
    }

    @GetMapping("/ids")
    public List<Long> getRecycleIds(GetRecycleBins getRecycleBins) {
        return recycleBinService.getRecycleIds(getRecycleBins);
    }

}
