package com.niimbot.asset.system.service.impl;

import com.google.common.collect.ImmutableMap;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsCusRoleMapper;
import com.niimbot.asset.system.mapper.AsDataPermissionMapper;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.AsDataPermission;
import com.niimbot.asset.system.model.AsDataPermissionItem;
import com.niimbot.asset.system.model.AsRoleDataAuthority;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.service.AsDataPermissionService;
import com.niimbot.asset.system.service.AsRoleDataAuthorityService;
import com.niimbot.asset.system.service.CusUserRoleService;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.asset.system.service.RoleSensitiveAuthorityService;
import com.niimbot.asset.system.service.UserSensitiveAuthorityService;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.DataPermissionAuthorizeDto;
import com.niimbot.system.DataPermissionAuthorizeItemDto;
import com.niimbot.system.RoleDataAuthorityConfigDto;
import com.niimbot.system.SensitivePermissionDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据权限 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsDataPermissionServiceImpl extends ServiceImpl<AsDataPermissionMapper, AsDataPermission> implements AsDataPermissionService {
    private final DataAuthorityService dataAuthorityService;
    @Autowired
    private AsRoleDataAuthorityService roleDataAuthorityService;
    private final AsCusRoleMapper roleMapper;
    private final CusUserRoleService userRoleService;
    private final RoleSensitiveAuthorityService roleSensitiveAuthorityService;
    private final UserSensitiveAuthorityService userSensitiveAuthorityService;

    @Resource
    private ModelDataScopeServiceImpl modelDataScopeService;

    public static final Map<String, Integer> ROLE_PERMS_MAPPER = ImmutableMap.<String, Integer>builder()
            // 所属组织
            .put(AssetConstant.DATA_PERMISSION_ORG + ":" + AssetConstant.AUTHORITY_DEPTS + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_ORG + ":" + AssetConstant.AUTHORITY_DEPTS + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_ORG + ":" + AssetConstant.AUTHORITY_DEPTS + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_ONLY)
            // 存放区域
            .put(AssetConstant.DATA_PERMISSION_AREA + ":" + AssetConstant.AUTHORITY_DEPTS + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_AREA + ":" + AssetConstant.AUTHORITY_DEPTS + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_AREA + ":" + AssetConstant.AUTHORITY_DEPTS + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_ONLY)
            // 资产数据 - 管理组织或使用组织
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_MANAGER_OR_USE_DEPT + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_MANAGER_OR_USE_DEPT + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_MANAGER_OR_USE_DEPT + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_ONLY)
            // 资产数据 - 资产分类
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_CATE + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_CATE + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_CATE + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            // 资产数据 - 存放区域
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_AREAS + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_AREAS + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_AREAS + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_ONLY)
            // 资产数据 - 仅可查看自己的数据 - 所有角色默认不勾选
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_ONLY_ONESELF + ":" + BaseConstant.ADMIN_ROLE, 21)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_ONLY_ONESELF + ":" + BaseConstant.ASSET_ADMIN_ROLE, 21)
            .put(AssetConstant.DATA_PERMISSION_ASSET + ":" + AssetConstant.AUTHORITY_ONLY_ONESELF + ":" + BaseConstant.COMMON_ROLE, 21)
            // 资产单据 - 创建人
            .put(AssetConstant.DATA_PERMISSION_ASSET_ORDER + ":" + AssetConstant.AUTHORITY_CREATE_USER + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_ASSET_ORDER + ":" + AssetConstant.AUTHORITY_CREATE_USER + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_ASSET_ORDER + ":" + AssetConstant.AUTHORITY_CREATE_USER + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_USER_ONLY)
            // 资产单据 - 审批人
            .put(AssetConstant.DATA_PERMISSION_ASSET_ORDER + ":" + AssetConstant.AUTHORITY_APPROVAL_USER + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_ASSET_ORDER + ":" + AssetConstant.AUTHORITY_APPROVAL_USER + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_ASSET_ORDER + ":" + AssetConstant.AUTHORITY_APPROVAL_USER + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_USER_ONLY)
            // 耗材/仓库
            .put(AssetConstant.DATA_PERMISSION_MATERIAL + ":" + AssetConstant.AUTHORITY_STORE + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_MATERIAL + ":" + AssetConstant.AUTHORITY_STORE + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_MATERIAL + ":" + AssetConstant.AUTHORITY_STORE + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_ONLY)
            // 耗材单据 - 创建人
            .put(AssetConstant.DATA_PERMISSION_MATERIAL_ORDER + ":" + AssetConstant.AUTHORITY_CREATE_USER + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_MATERIAL_ORDER + ":" + AssetConstant.AUTHORITY_CREATE_USER + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_MATERIAL_ORDER + ":" + AssetConstant.AUTHORITY_CREATE_USER + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_USER_ONLY)
            // 耗材单据 - 审批人
            .put(AssetConstant.DATA_PERMISSION_MATERIAL_ORDER + ":" + AssetConstant.AUTHORITY_APPROVAL_USER + ":" + BaseConstant.ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_ALL)
            .put(AssetConstant.DATA_PERMISSION_MATERIAL_ORDER + ":" + AssetConstant.AUTHORITY_APPROVAL_USER + ":" + BaseConstant.ASSET_ADMIN_ROLE, AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            .put(AssetConstant.DATA_PERMISSION_MATERIAL_ORDER + ":" + AssetConstant.AUTHORITY_APPROVAL_USER + ":" + BaseConstant.COMMON_ROLE, AssetConstant.AUTHORITY_TYPE_USER_ONLY)
            .build();

    @Override
    public List<AsDataPermission> queryUserDataPermission(Long empId) {
        List<AsDataPermission> permissions = list(Wrappers.<AsDataPermission>lambdaQuery()
                .orderByAsc(AsDataPermission::getId));
        List<AsDataAuthority> authorities = dataAuthorityService.getListByUserId(empId);
        handle(permissions, authorities);
        return permissions;
    }

    private void handle(List<AsDataPermission> permissions, List<AsDataAuthority> authorities) {
        Map<String, List<AsDataAuthority>> authoritiesMap = authorities.stream().collect(
                Collectors.groupingBy(AsDataAuthority::getAuthorityDataCode));
        for (AsDataPermission permission : permissions) {
            Map<String, AsDataAuthority> authMap = authoritiesMap.getOrDefault(permission.getCode(), new ArrayList<>(0))
                    .stream().collect(Collectors.toMap(AsDataAuthority::getAuthorityCode, o -> o));
            for (AsDataPermissionItem permissionItem : permission.getPerms()) {
                AsDataAuthority dataAuthority = authMap.get(permissionItem.getAuthorityCode());
                // 处理有多种选择的权限向
                if (permissionItem.hasCombo()) {
                    Map<String, String> comboMap = permissionItem.getComboMap();
                    // 确定对应的权限项
                    for (String code : comboMap.keySet()) {
                        if (Objects.nonNull(dataAuthority)) {
                            break;
                        }
                        dataAuthority = authMap.get(code);
                    }
                    if (Objects.nonNull(dataAuthority)) {
                        permissionItem.setAuthorityCode(dataAuthority.getAuthorityCode());
                        permissionItem.setLabel(comboMap.get(dataAuthority.getAuthorityCode()));
                    }
                }
                if (Objects.nonNull(dataAuthority)) {
                    permissionItem.setSelect(Boolean.TRUE);
                    permissionItem.setAuthorityType(dataAuthority.getAuthorityType());
                    permissionItem.setAuthorityData(dataAuthority.getAuthorityData());
                }
            }
        }
    }

    @Override
    public List<AsDataPermission> queryCompanyDefaultUserDataPermission(Long companyId) {
        List<AsDataPermission> permissions = list(Wrappers.<AsDataPermission>lambdaQuery()
                .orderByAsc(AsDataPermission::getId));
        AsCompanySetting setting = Db.getById(companyId, AsCompanySetting.class);
        List<AsDataAuthority> defaultDataAuthorities = setting.getDefaultDataAuthorities();
        handle(permissions, defaultDataAuthorities);
        return permissions;
    }

    @Override
    public List<AsDataPermission> queryDataAuthByRole(Long roleId) {
        List<AsDataPermission> permissions = list(Wrappers.<AsDataPermission>lambdaQuery()
                .orderByAsc(AsDataPermission::getId));
        List<AsRoleDataAuthority> roleDataAuthorityList = roleDataAuthorityService.queryRoleDataAuthByRoleId(LoginUserThreadLocal.getCompanyId(), roleId);
        if (CollUtil.isEmpty(roleDataAuthorityList)) {
            return permissions;
        } else {
            handleRoleDataAuth(permissions, roleDataAuthorityList);
        }
        return permissions;
    }

    private void handleRoleDataAuth(List<AsDataPermission> permissions, List<AsRoleDataAuthority> authorities) {
        Map<String, List<AsRoleDataAuthority>> authoritiesMap = authorities.stream().collect(
                Collectors.groupingBy(AsRoleDataAuthority::getAuthorityDataCode));
        for (AsDataPermission permission : permissions) {
            Map<String, AsRoleDataAuthority> authMap = authoritiesMap.getOrDefault(permission.getCode(), new ArrayList<>(0))
                    .stream().collect(Collectors.toMap(AsRoleDataAuthority::getAuthorityCode, o -> o));
            for (AsDataPermissionItem permissionItem : permission.getPerms()) {
                AsRoleDataAuthority dataAuthority = authMap.get(permissionItem.getAuthorityCode());
                // 处理有多种选择的权限向
                if (permissionItem.hasCombo()) {
                    Map<String, String> comboMap = permissionItem.getComboMap();
                    // 确定对应的权限项
                    for (String code : comboMap.keySet()) {
                        if (Objects.nonNull(dataAuthority)) {
                            break;
                        }
                        dataAuthority = authMap.get(code);
                    }
                    if (Objects.nonNull(dataAuthority)) {
                        permissionItem.setAuthorityCode(dataAuthority.getAuthorityCode());
                        permissionItem.setLabel(comboMap.get(dataAuthority.getAuthorityCode()));
                    }
                }
                if (Objects.nonNull(dataAuthority)) {
                    permissionItem.setSelect(Boolean.TRUE);
                    permissionItem.setAuthorityType(dataAuthority.getAuthorityType());
                    permissionItem.setAuthorityData(dataAuthority.getAuthorityData());
                }
            }
        }
    }

    @Override
    public Boolean configRoleDataAuth(RoleDataAuthorityConfigDto roleDataAuthorityConfigDto) {
        AsCusRole role = roleMapper.selectByRoleId(roleDataAuthorityConfigDto.getRoleId(), roleDataAuthorityConfigDto.getCompanyId());
        if (Objects.isNull(role)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "角色信息不存在");
        }
        //超管不允许修改数据权限信息
        if (BaseConstant.ADMIN_ROLE.equalsIgnoreCase(role.getRoleCode())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "超管不允许修改数据权限信息");
        }

        //先删除已有的数据
        roleDataAuthorityService.remove(Wrappers.lambdaQuery(AsRoleDataAuthority.class)
                .eq(AsRoleDataAuthority::getCompanyId, roleDataAuthorityConfigDto.getCompanyId())
                .eq(AsRoleDataAuthority::getRoleId, roleDataAuthorityConfigDto.getRoleId()));

        //删除已有的敏感字段数据权限
        roleSensitiveAuthorityService.removeByUserIds(Collections.singletonList(roleDataAuthorityConfigDto.getRoleId()));

        //数据权限列表转换
        List<AsRoleDataAuthority> roleDataAuthorityList = roleDataAuthorityConfigDto.getDataAuthorityDtoList().stream().map(item -> {
            AsRoleDataAuthority roleDataAuthority = new AsRoleDataAuthority();
            BeanUtils.copyProperties(item, roleDataAuthority);
            roleDataAuthority.setCompanyId(roleDataAuthorityConfigDto.getCompanyId());
            roleDataAuthority.setRoleId(roleDataAuthorityConfigDto.getRoleId());
            return roleDataAuthority;
        }).collect(Collectors.toList());
        roleDataAuthorityService.saveBatch(roleDataAuthorityList);

        //保存角色敏感数据权限
        return roleSensitiveAuthorityService.savePermission(roleDataAuthorityConfigDto.getRoleId(), roleDataAuthorityConfigDto.getSensitivePermissionDtoList());
    }

    @Override
    public List<AsDataPermission> queryDataPermission() {
        return list(Wrappers.<AsDataPermission>lambdaQuery()
                .orderByAsc(AsDataPermission::getId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean authorize(DataPermissionAuthorizeDto authorizeDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        //校验数据权限
        verifyDataAuthParam(authorizeDto);
        // 删除用户权限
        dataAuthorityService.removeByUserIds(authorizeDto.getEmpIds());
        //删除用户敏感字段数据权限
        userSensitiveAuthorityService.removeByUserIds(authorizeDto.getEmpIds(), companyId);
        // 待更新用户权限
        List<AsDataAuthority> resolve = new ArrayList<>();
        // 待更新的敏感数据权限
        List<SensitivePermissionDto> sensitivePermission;
        if (authorizeDto.getPermissionType() == 1) {
            sensitivePermission = authorizeDto.getSensitivePermission();
        } else {
            sensitivePermission = roleSensitiveAuthorityService.getByRoleId(authorizeDto.getMirrorRoleId());
        }

        for (Long empId : authorizeDto.getEmpIds()) {
            //使用自定义的数据权限
            if (authorizeDto.getPermissionType() == 1) {
                for (DataPermissionAuthorizeItemDto permission : authorizeDto.getPerms()) {
                    AsDataAuthority dataAuthority = BeanUtil.copyProperties(permission, AsDataAuthority.class);
                    dataAuthority.setUserId(empId);
                    if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == dataAuthority.getAuthorityType()
                            && CollUtil.isEmpty(dataAuthority.getAuthorityData())) {
                        BusinessExceptionUtil.throwException("自定义权限数据不能为空");
                    }
                    resolve.add(dataAuthority);
                }

            } else {
                //复用角色数据权限
                List<AsRoleDataAuthority> roleDataAuthorityList = roleDataAuthorityService.queryRoleDataAuthByRoleId(LoginUserThreadLocal.getCompanyId(), authorizeDto.getMirrorRoleId());
                for (AsRoleDataAuthority item : roleDataAuthorityList) {
                    AsDataAuthority dataAuthority = new AsDataAuthority().setUserId(empId)
                            .setAuthorityDataCode(item.getAuthorityDataCode())
                            .setAuthorityCode(item.getAuthorityCode())
                            .setAuthorityType(item.getAuthorityType())
                            .setAuthorityData(item.getAuthorityData())
                            .setAuthorityGroup(item.getAuthorityGroup());
                    resolve.add(dataAuthority);
                }
            }
        }

        try {
            //保存用户敏感字段数据权限
            userSensitiveAuthorityService.savePermission(authorizeDto.getEmpIds(), sensitivePermission);
            // 写入数据库
            dataAuthorityService.saveBatch(resolve);
            // 删除缓存
            modelDataScopeService.cleanDataScopeCache(companyId, authorizeDto.getEmpIds());
//            dataPermissionCacheService.removeCache(LoginUserThreadLocal.getCompanyId(), resolve.stream().map(AsDataAuthority::getUserId).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("数据授权失败", e);
            BusinessExceptionUtil.throwException("数据授权失败");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDataPermission(Long companyId, Long userId, String roleCode) {
        // 入口太多，这里特殊判断LoginUserThreadLocal是否为空
        LoginUserDto loginUserDto = LoginUserThreadLocal.get();
        if (loginUserDto == null) {
            // threadLocal写入公司Id
            LoginUserDto userDto = new LoginUserDto();
            userDto.setCusUser(new CusUserDto()
                    .setId(userId)
                    .setIsAdmin(false)
                    .setCompanyId(companyId));
            LoginUserThreadLocal.set(userDto);
            try {
                initDataPermissionExec(companyId, userId, roleCode);
            } finally {
                LoginUserThreadLocal.remove();
            }
        } else {
            initDataPermissionExec(companyId, userId, roleCode);
        }
    }

    private void initDataPermissionExec(Long companyId, Long userId, String roleCode) {
        //查询角色信息
        AsCusRole cusRole = queryRole(companyId, roleCode);

        //这里是为了解决问题(新增员工时被设置成了普通员工角色，而非企业默认角色，新增员工的口子太多了，改起来困难，轻喷)
        if (!BaseConstant.ADMIN_ROLE.equalsIgnoreCase(roleCode)) {
            //删除用户以前角色关系
            userRoleService.remove(Wrappers.lambdaQuery(AsUserRole.class).eq(AsUserRole::getUserId, userId));
            //查询企业默认角色
            cusRole = queryRole(companyId, "");
            //员工设置为默认角色
            userRoleService.save(new AsUserRole().setUserId(userId).setRoleId(cusRole.getId()));
        }

        //删除用户权限
        dataAuthorityService.removeByUserIds(Collections.singletonList(userId));

        //初始化数据权限，获取角色数据权限进行赋值
        List<AsRoleDataAuthority> roleDataAuthorityList = roleDataAuthorityService.queryRoleDataAuthByRoleId(companyId, cusRole.getId());
        if (CollUtil.isNotEmpty(roleDataAuthorityList)) {
            List<AsDataAuthority> dataAuthorityList = roleDataAuthorityList.stream().map(item -> {
                AsDataAuthority asDataAuthority = new AsDataAuthority();
                asDataAuthority.setUserId(userId).setAuthorityData(item.getAuthorityData()).setAuthorityCode(item.getAuthorityCode())
                        .setAuthorityDataCode(item.getAuthorityDataCode()).setAuthorityType(item.getAuthorityType())
                        .setAuthorityGroup(item.getAuthorityGroup());
                return asDataAuthority;
            }).collect(Collectors.toList());
            dataAuthorityService.saveBatch(dataAuthorityList);
        } else {
            List<AsDataPermission> permissions = queryDataPermission();
            List<AsDataAuthority> authorities = new ArrayList<>();
            for (AsDataPermission permission : permissions) {
                for (AsDataPermissionItem item : permission.getPerms()) {
                    String permKey = permission.getCode() + ":" + item.getAuthorityCode() + ":" + roleCode;
                    AsDataAuthority authority = new AsDataAuthority();
                    authority.setUserId(userId);
                    authority.setAuthorityDataCode(permission.getCode());
                    authority.setAuthorityCode(item.getAuthorityCode());
                    authority.setAuthorityType(ROLE_PERMS_MAPPER.get(permKey));
                    authority.setAuthorityData(item.getAuthorityData());
                    authority.setAuthorityGroup(1);
                    authorities.add(authority);
                }
            }
            dataAuthorityService.saveBatch(authorities);
        }

        // 数据权限
        List<SensitivePermissionDto> sensitivePermission = roleSensitiveAuthorityService.getByRoleId(cusRole.getId());
        List<Long> userIds = ListUtil.of(userId);
        //保存用户敏感字段数据权限
        userSensitiveAuthorityService.savePermission(userIds, sensitivePermission);
        // 删除缓存
        modelDataScopeService.cleanDataScopeCache(companyId, userIds);
    }

    /**
     * 校验数据权限
     * @param authorizeDto
     */
    private void verifyDataAuthParam(DataPermissionAuthorizeDto authorizeDto) {
        //自定义权限集的时候，需要校验权限集是否为空
        if (authorizeDto.getPermissionType() == 1 && CollUtil.isEmpty(authorizeDto.getPerms())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "自定义权限集不允许为空");
        }

        //复用角色权限集
        if (authorizeDto.getPermissionType() == 2) {
            AsCusRole role = roleMapper.selectByRoleId(authorizeDto.getMirrorRoleId(), LoginUserThreadLocal.getCompanyId());
            if (Objects.isNull(role)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "复用角色不存在");
            }
        }
    }

    private AsCusRole queryRole(Long companyId, String roleCode) {
        //查询企业默认角色
        AsCusRole queryDefaultParam = new AsCusRole().setCompanyId(companyId).setIsDefault(Boolean.TRUE);
        List<AsCusRole> defaultRoleList = roleMapper.selectByCondition(queryDefaultParam);

        AsCusRole defaultRole = null;
        //如果企业默认角色不存在，兜底采用普通员工的角色
        if (CollUtil.isEmpty(defaultRoleList)) {
            AsCusRole roleCodeParam = new AsCusRole().setCompanyId(companyId).setRoleCode(BaseConstant.COMMON_ROLE);
            defaultRole = roleMapper.selectByCondition(roleCodeParam).get(0);
        } else {
            defaultRole = defaultRoleList.get(0);
        }

        //如果角色编码为空返回默认角色，存量数据用户新增的角色都是没有角色编码的
        if (StrUtil.isBlank(roleCode)) {
            return defaultRole;
        }

        //根据角色编码获取角色信息
        AsCusRole roleCodeListParam = new AsCusRole().setCompanyId(companyId).setRoleCode(roleCode);
        List<AsCusRole> roleList = roleMapper.selectByCondition(roleCodeListParam);
        if (CollUtil.isEmpty(roleList)) {
            return defaultRole;
        } else {
            return roleList.get(0);
        }
    }
}
