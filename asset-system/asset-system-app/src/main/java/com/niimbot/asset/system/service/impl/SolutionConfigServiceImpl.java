package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.enums.ContentTypeEnum;
import com.niimbot.asset.system.mapper.AsSolutionConfigMapper;
import com.niimbot.asset.system.model.AsSolutionConfig;
import com.niimbot.asset.system.service.SolutionConfigService;
import com.niimbot.system.SolutionConfigDto;
import com.niimbot.system.SolutionDetailDto;
import com.niimbot.system.SolutionQueryDto;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/16 上午10:48
 */
@Slf4j
@Service
public class SolutionConfigServiceImpl extends ServiceImpl<AsSolutionConfigMapper, AsSolutionConfig> implements SolutionConfigService {

    @Override
    public PageUtils<SolutionConfigDto> pageQuery(SolutionQueryDto queryDto) {
        IPage<AsSolutionConfig> pageResult = this.getBaseMapper().pageQuery(queryDto.buildIPage(), queryDto);
        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageUtils<>(null, 0, (int) queryDto.getPageSize(), (int) queryDto.getPageNum());
        }

        List<SolutionConfigDto> dataList = new ArrayList<>();
        for (AsSolutionConfig item : pageResult.getRecords()) {
            SolutionConfigDto data = new SolutionConfigDto();
            BeanUtils.copyProperties(item, data);
            if (StrUtil.isNotBlank(item.getImages())) {
                data.setImages(Arrays.asList(item.getImages().split(",")));
            }
            //第三方链接需要返回给前端，富文本内容太多，不进行返回
            if (!ContentTypeEnum.LINK.getCode().equals(item.getContentType())) {
                data.setContent(null);
            }

            data.setContentTypeDesc(ContentTypeEnum.getByCode(item.getContentType()).getDesc());
            dataList.add(data);
        }
        return new PageUtils<>(dataList, (int) pageResult.getTotal(), (int) pageResult.getSize(), (int) pageResult.getCurrent());
    }

    @Override
    public SolutionDetailDto queryDetail(Long configId) {
        if (Objects.isNull(configId)) {
            return null;
        }
        
        AsSolutionConfig solutionConfig = this.getById(configId);
        if (Objects.isNull(solutionConfig)) {
            return null;
        }

        //浏览次数增加
        this.getBaseMapper().increaseView(configId);

        SolutionDetailDto result = new SolutionDetailDto();
        SolutionConfigDto currentConfig = new SolutionConfigDto();
        BeanUtils.copyProperties(solutionConfig, currentConfig);
        if (StrUtil.isNotBlank(solutionConfig.getImages())) {
            currentConfig.setImages(Arrays.asList(solutionConfig.getImages().split(",")));
        }
        result.setCurrentConfig(currentConfig);
        result.setPreviousConfig(this.getBaseMapper().selectPrevious(configId));
        result.setNextConfig(this.getBaseMapper().selectNext(configId));
        return result;
    }


}
