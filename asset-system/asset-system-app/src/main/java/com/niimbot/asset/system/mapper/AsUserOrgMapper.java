package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsUserOrg;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface AsUserOrgMapper extends BaseMapper<AsUserOrg> {

    List<AsUserOrg> orgEmpList(@Param("companyId") Long companyId,
                               @Param("orgIds") List<Long> orgIds,
                               @Param("isOnlyShowEmpWithAccount") boolean isOnlyShowEmpWithAccount);

    List<Long> orgIdsByUserId(@Param("companyId") Long companyId,
                              @Param("userId") Long userId,
                              @Param("type") Integer type);

    List<AsUserOrg> orgIdsByUserIds(@Param("companyId") Long companyId,
                                    @Param("userIds") List<Long> userIds,
                                    @Param("type") Integer type);
}
