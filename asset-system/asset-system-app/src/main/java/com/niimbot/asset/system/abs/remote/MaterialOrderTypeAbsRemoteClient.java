package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.MaterialOrderTypeAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:27
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.material.domain.abs.impl.MaterialOrderTypeAbsImpl")
@FeignClient(name = "asset-material", url = "https://{gateway}/client/abs/material/materialOrderFieldAbs/")
public interface MaterialOrderTypeAbsRemoteClient extends MaterialOrderTypeAbs {
}
