package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.utils.DateUtils;
import com.niimbot.asset.system.abs.AssetAbs;
import com.niimbot.asset.system.dto.AssetDeleteAmountGetQry;
import com.niimbot.asset.system.dto.AssetExcludeTestAmountGetQry;
import com.niimbot.asset.system.mapper.AsAssetReportsMapper;
import com.niimbot.asset.system.mapper.AsUserActiveReportsMapper;
import com.niimbot.asset.system.mapper.AsUserStatisticsReportsMapper;
import com.niimbot.asset.system.model.AsAssetReports;
import com.niimbot.asset.system.model.AsUserStatisticsReports;
import com.niimbot.asset.system.service.CommandService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.system.statistics.StatisticsNumDayDto;

import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import static cn.hutool.core.collection.CollUtil.isNotEmpty;

@Service
@Slf4j
public class CommandServiceImpl extends ServiceImpl<AsAssetReportsMapper, AsAssetReports> implements CommandService {

    @Resource
    private AssetAbs assetAbs;

    @Resource
    private AsAssetReportsMapper assetReportsMapper;

    @Resource
    private AsUserActiveReportsMapper userActiveReportsMapper;

    @Resource
    private AsUserStatisticsReportsMapper userStatisticsReportsMapper;

    @Resource
    private CompanyService companyService;

    /**
     * 统计当天资产新增数（每天统计一次）
     *
     * @param date 日期
     * @return true / false
     */
    @Override
    public boolean statisticsAssetAddNumByDay(String date) {
        // 当前时间
        DateTime currDate = DateUtils.date();

        DateTime dateTime = StrUtil.isNotBlank(date) ? DateUtils.parseDate(date) : currDate;
        // 开始时间
        String beginTime = DateUtils.format(DateUtils.beginOfDay(dateTime), DatePattern.NORM_DATETIME_PATTERN);
        // 结束时间
        String endTime = DateUtils.format(DateUtils.endOfDay(dateTime), DatePattern.NORM_DATETIME_PATTERN);

        // 获取搜索条件之内的资产数量 排除测试企业
        int assetNum = assetAbs.getAssetExcludeTestAmount(new AssetExcludeTestAmountGetQry()
                .setStartTime(beginTime).setEndTime(endTime));

        // 统计当天删除资产数 排除测试企业
        Integer deleteAssetNum = assetAbs.getAssetDeleteAmount(new AssetDeleteAmountGetQry()
                .setStartTime(beginTime).setEndTime(endTime));

        // 实例化资产统计
        AsAssetReports asAssetReports = new AsAssetReports();
        asAssetReports.setDayTime(LocalDate.now()).setNewAssetNum(assetNum)
                .setDeleteAssetNum(deleteAssetNum)
                .setWeekTime(DateUtils.getWeeksNum(DateUtils.now()));

        // 查询是否已存在
        QueryWrapper<AsAssetReports> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("day_time", LocalDate.now());
        List<AsAssetReports> list = assetReportsMapper.selectList(queryWrapper);
        if (isNotEmpty(list)) {
            log.error("当前日期资产新增数量已存在");
            assetReportsMapper.update(asAssetReports, queryWrapper);
            return true;
        }

        int insert = assetReportsMapper.insert(asAssetReports);
        if (insert != 1) {
            log.error("添加资产新增数量失败");
            return false;
        }

        return true;
    }

    /**
     * 当天用户统计报告（每天统计一次）
     *
     * @param date 日期
     * @return true / false
     */
    @Override
    public boolean statisticsUserNumByDay(String date) {
        // 当前时间
        DateTime currDate = DateUtils.date();
        // 默认当前时间
        DateTime dateTime = StrUtil.isNotBlank(date) ? DateUtils.parseDate(date) : currDate;
        // 当前年
        int year = DateUtil.year(dateTime);
        // 获取前一天
        DateTime yesterdayDateTime = DateUtils.offsetDay(dateTime, -1);

        // 超过100天没有登录过系统的企业视为沉睡用户；
        String pre100Date = DateUtils.format(DateUtils.offsetDay(yesterdayDateTime, -100), DatePattern.NORM_DATE_PATTERN);
        // 超过356天没有登录过系统的企业视为流失用户；
        String pre365Date = DateUtils.format(DateUtils.offsetDay(yesterdayDateTime, -365), DatePattern.NORM_DATE_PATTERN);

        //获取用户每天的新增活跃数
        List<StatisticsNumDayDto> newUserActiveNumDto = userActiveReportsMapper.newUserActiveNumByDay(year);
        List<StatisticsNumDayDto> oldUserActiveNumDto = userActiveReportsMapper.oldUserActiveNumByDay(year);

        Map<String, Integer> newUserActiveNumMap = newUserActiveNumDto.stream().collect(Collectors.toMap(StatisticsNumDayDto::getDay, StatisticsNumDayDto::getNum));
        Map<String, Integer> oldUserActiveNumMap = oldUserActiveNumDto.stream().collect(Collectors.toMap(StatisticsNumDayDto::getDay, StatisticsNumDayDto::getNum));

        //获取流失用户和沉睡用户
        Integer active100Num = userActiveReportsMapper.selectCountByDay(pre100Date);

        Integer active365Num = userActiveReportsMapper.selectCountByDay(pre365Date);

        // 获取日期之前的用户总数
        int companyTotal = companyService.countOfExcludedTest(dateTime);

        // 流失用户数
        int lostUserNum = companyTotal - active365Num;
        lostUserNum = Math.max(lostUserNum, 0);
        // 沉睡用户数
        int sleepUserNum = active365Num - active100Num;
        sleepUserNum = Math.max(sleepUserNum, 0);

        // 当前天-字符串
        String dayTimeStr = DateUtils.format(dateTime, DatePattern.NORM_DATE_PATTERN);
        // 获取周
        Integer week = DateUtils.getWeeksNum(dayTimeStr);

        Integer newUserActiveNum = newUserActiveNumMap.getOrDefault(dayTimeStr, 0);
        Integer oldUserActiveNum = oldUserActiveNumMap.getOrDefault(dayTimeStr, 0);
        // 实例化资产统计
        AsUserStatisticsReports userStatisticsReports = new AsUserStatisticsReports();
        userStatisticsReports.setDayTime(dayTimeStr).setWeekTime(week)
                        .setNewUserActiveNum(newUserActiveNum).setOldUserActiveNum(oldUserActiveNum)
                        .setDeleteUserNum(0).setSleepUserNum(sleepUserNum).setLostUserNum(lostUserNum);

        // 查询是否已经存在
        QueryWrapper<AsUserStatisticsReports> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("day_time", dayTimeStr);
        long count = userStatisticsReportsMapper.selectCount(queryWrapper);
        if(count > 0){
            log.error("当前日期用户统计数据已存在");
            userStatisticsReportsMapper.update(userStatisticsReports, queryWrapper);
            return true;
        }

        int insert = userStatisticsReportsMapper.insert(userStatisticsReports);
        if (insert != 1) {
            log.error("添加用户统计数据失败");
            return false;
        }

        return true;
    }

}
