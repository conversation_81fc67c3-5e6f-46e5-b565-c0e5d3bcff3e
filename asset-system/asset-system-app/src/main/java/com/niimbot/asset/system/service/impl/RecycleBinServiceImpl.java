package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsRecycleBinMapper;
import com.niimbot.asset.system.model.AsRecycleBin;
import com.niimbot.asset.system.service.RecycleBinService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RecycleBinServiceImpl implements RecycleBinService {

    private final AsRecycleBinMapper recycleBinMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recycle(ResRecycle resRecycle) {
        isSupport(resRecycle.getResType());
        List<Long> ids = recycleBinMapper.selectResIdsFrom(RES_TYPE_TABLE_NAME.get(resRecycle.getResType()), resRecycle.getResIds());
        resRecycle.getResIds().removeIf(v -> !ids.contains(v));
        if (CollUtil.isEmpty(resRecycle.getResIds())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "业务数据没有前置删除掉");
        }
        List<AsRecycleBin> recycleBins = resRecycle.getResIds().stream().map(id -> new AsRecycleBin().setResType(resRecycle.getResType()).setResId(id)
                .setReason(resRecycle.getReason()).setCompanyId(LoginUserThreadLocal.getCompanyId())).collect(Collectors.toList());
        recycleBins.forEach(recycleBinMapper::insert);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean restore(ResRestore resRestore) {
        List<AsRecycleBin> bins = recycleBinMapper.selectByResIdsOrElseThrow(resRestore.getCompanyId(), resRestore.getResIds(), resRestore.getResType(), false);
        if (CollUtil.isEmpty(bins)) {
            return false;
        }
        // 恢复
        bins.stream().collect(Collectors.groupingBy(AsRecycleBin::getResType, Collectors.mapping(AsRecycleBin::getResId, Collectors.toList()))).forEach((k, v) -> recycleBinMapper.updateBatchTableIsDelete(RES_TYPE_TABLE_NAME.get(k), v, 0));
        // 删除
        recycleBinMapper.deleteByResIds(resRestore.getCompanyId(), resRestore.getResIds(), resRestore.getResType());
        return true;
    }

    @Override
    public Boolean release(ResRelease resRelease) {
        return recycleBinMapper.deleteByResIds(resRelease.getCompanyId(), resRelease.getResIds(), resRelease.getResType());
    }

    @Override
    public RecycleBin details(GetRecycleBins get) {
        AsRecycleBin recycleBin = recycleBinMapper.selectOne(
                Wrappers.lambdaQuery(AsRecycleBin.class)
                        .eq(AsRecycleBin::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsRecycleBin::getResType, get.getResType())
                        .eq(AsRecycleBin::getResId, get.getResId())
        );
        if (Objects.isNull(recycleBin)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "回收站不存在此业务数据");
        }
        return BeanUtil.copyProperties(recycleBin, RecycleBin.class);
    }

    @Override
    public RecycleBin get(GetRecycleBins getRecycleBins) {
        isSupport(getRecycleBins.getResType());
        List<AsRecycleBin> bins = recycleBinMapper.selectByResIdsOrElseThrow(getRecycleBins.getCompanyId(), Collections.singletonList(getRecycleBins.getResId()), getRecycleBins.getResType(), true);
        RecycleBin.ResData resData = recycleBinMapper.selectResData
                (
                        RES_TYPE_TABLE_NAME.get(getRecycleBins.getResType()),
                        RES_TYPE_COLUMN_NAME.get(getRecycleBins.getResType()),
                        getRecycleBins.getResId()
                );
        if (Objects.isNull(resData)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "回收站不存在此业务数据");
        }
        AsRecycleBin bin = bins.get(0);
        return new RecycleBin()
                .setResId(bin.getResId()).setCompanyId(bin.getCompanyId()).setResType(bin.getResType())
                .setReason(bin.getReason()).setCreateBy(bin.getCreateBy()).setCreateTime(bin.getCreateTime())
                .setData(resData);
    }

    @Override
    public List<Long> getRecycleIds(GetRecycleBins getRecycleBins) {
        isSupport(getRecycleBins.getResType());
        List<Long> ids = recycleBinMapper.selectResIds(getRecycleBins);
        return CollUtil.isEmpty(ids) ? Collections.emptyList() : ids;
    }
}
