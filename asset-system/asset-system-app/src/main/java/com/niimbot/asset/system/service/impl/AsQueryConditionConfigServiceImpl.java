package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsQueryConditionConfigMapper;
import com.niimbot.asset.system.model.AsQueryConditionConfig;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.QueryConditionConfigDto;

import org.springframework.stereotype.Service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @since 2022/9/5 11:24
 */
@Service
public class AsQueryConditionConfigServiceImpl extends ServiceImpl<AsQueryConditionConfigMapper, AsQueryConditionConfig> implements AsQueryConditionConfigService {

    @Override
    public QueryConditionConfigDto getByType(String type) {
        return getByType(type, null);
    }

    @Override
    public QueryConditionConfigDto getByType(String type, String terminal) {
        AsQueryConditionConfig queryCondition = getOne(Wrappers.lambdaQuery(AsQueryConditionConfig.class)
                .eq(AsQueryConditionConfig::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .eq(AsQueryConditionConfig::getTerminal, StrUtil.isBlank(terminal) ? StrUtil.EMPTY : terminal)
                .eq(AsQueryConditionConfig::getType, type));
        if (ObjectUtil.isNull(queryCondition)) {
            queryCondition = getOne(Wrappers.lambdaQuery(AsQueryConditionConfig.class)
                    .eq(AsQueryConditionConfig::getUserId, 0L)
                    .eq(AsQueryConditionConfig::getType, type));
            if (ObjectUtil.isNull(queryCondition)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "类型" + type + "未初始化");
            }
        }
        QueryConditionConfigDto configDto = new QueryConditionConfigDto();
        configDto.setSysConfig(queryCondition.getUserId() == 0L);
        configDto.setConditions(queryCondition.getConditions());
        configDto.setType(queryCondition.getType());
        configDto.setTerminal(terminal);
        return configDto;
    }
}
