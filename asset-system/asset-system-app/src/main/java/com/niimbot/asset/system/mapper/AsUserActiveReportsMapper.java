package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsUserActiveReports;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.statistics.StatisticsNumDayDto;
import com.niimbot.system.statistics.StatisticsNumWeekDto;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 活跃用户统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsUserActiveReportsMapper.class)
public interface AsUserActiveReportsMapper extends BaseMapper<AsUserActiveReports> {
    /**
     * 按周数统计新用户中活跃用户数（新用户--本周内注册的用户）
     *
     * @param year     搜索年
     * @return 新用户中活跃用户数
     */
    List<StatisticsNumWeekDto> newUserActiveNum(Integer year);

    /**
     * 按周数统计老用户中活跃用户数
     *
     * @param year     搜索年
     * @return 老用户中活跃用户数
     */
    List<StatisticsNumWeekDto> oldUserActiveNum(Integer year);

    /**
     * 按周数统计用户中活跃用户数
     *
     * @param year     搜索年
     * @return 老用户中活跃用户数
     */
    List<StatisticsNumWeekDto> userActiveNum(Integer year);

    /**
     * 按天数统计新用户中活跃用户数
     *
     * @param year     搜索年
     * @return 新用户中活跃用户数
     */
    List<StatisticsNumDayDto> newUserActiveNumByDay(Integer year);

    /**
     * 按天数统计老用户中活跃用户数
     *
     * @param year     搜索年
     * @return 老用户中活跃用户数
     */
    List<StatisticsNumDayDto> oldUserActiveNumByDay(Integer year);

    Integer selectCountByDay(String year);
}
