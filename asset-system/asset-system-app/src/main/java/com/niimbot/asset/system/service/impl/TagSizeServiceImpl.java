package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsTagSizeMapper;
import com.niimbot.asset.system.model.AsTagSize;
import com.niimbot.asset.system.service.TagSizeService;
import org.springframework.stereotype.Service;

/**
 * 运行后台账号service
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Service
public class TagSizeServiceImpl extends ServiceImpl<AsTagSizeMapper, AsTagSize> implements TagSizeService {
}
