package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsDictType;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 字典类型表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-06
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsDictTypeMapper.class)
public interface AsDictTypeMapper extends BaseMapper<AsDictType> {

}
