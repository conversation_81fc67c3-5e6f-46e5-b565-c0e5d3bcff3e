package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsEmployeeChangeMapper;
import com.niimbot.asset.system.model.AsEmployeeAssetChange;
import com.niimbot.asset.system.model.AsEmployeeChange;
import com.niimbot.asset.system.service.AsEmployeeAssetChangeService;
import com.niimbot.asset.system.service.AsEmployeeChangeService;
import com.niimbot.asset.system.service.handler.WeixinAdapter;
import com.niimbot.system.CusEmployeeChangeDto;
import com.niimbot.system.CusEmployeeChangeQueryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 员工异动记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Service
public class AsEmployeeChangeServiceImpl extends ServiceImpl<AsEmployeeChangeMapper, AsEmployeeChange> implements AsEmployeeChangeService {

    private final AsEmployeeAssetChangeService employeeAssetChangeService;

    @Autowired
    public AsEmployeeChangeServiceImpl(AsEmployeeAssetChangeService employeeAssetChangeService) {
        this.employeeAssetChangeService = employeeAssetChangeService;
    }

    @Override
    public IPage<CusEmployeeChangeDto> getPage(CusEmployeeChangeQueryDto query) {
        // 企业微信兼容，员工搜索
        if (StrUtil.isNotEmpty(query.getKw()) && Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).empSearch(query.getKw());
            return this.getBaseMapper().getPage(query.buildIPage(), query, LoginUserThreadLocal.getCompanyId(), unionIds);
        }
        return this.getBaseMapper().getPage(query.buildIPage(), query, LoginUserThreadLocal.getCompanyId(), Collections.emptyList());
    }

    @Override
    public IPage<JSONObject> changeAsset(CusEmployeeChangeQueryDto query) {
        IPage<AsEmployeeAssetChange> page = employeeAssetChangeService.selectPage(query);
        List<AsEmployeeAssetChange> records = page.getRecords();
        List<JSONObject> jsonObjectList = records.stream().map(change -> {
            JSONObject assetSnapshotData = change.getAssetSnapshotData();
            assetSnapshotData.put("id", change.getAssetId());
            return assetSnapshotData;
        }).collect(Collectors.toList());
        IPage<JSONObject> jsonObjectPage = new Page<>();
        BeanUtil.copyProperties(page, jsonObjectPage);
        jsonObjectPage.setRecords(jsonObjectList);
        return jsonObjectPage;
    }

}
