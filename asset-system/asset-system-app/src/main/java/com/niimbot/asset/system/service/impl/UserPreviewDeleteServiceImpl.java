package com.niimbot.asset.system.service.impl;

import com.niimbot.asset.system.abs.EntMatAbs;
import com.niimbot.asset.system.abs.InventoryTaskAbs;
import com.niimbot.asset.system.abs.WorkflowAbs;
import com.niimbot.asset.system.mapper.CompanyAssetMapper;
import com.niimbot.asset.system.service.UserPreviewDeleteService;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @since 2021/12/6 15:22
 */
@Service
public class UserPreviewDeleteServiceImpl implements UserPreviewDeleteService {

    @Resource
    private InventoryTaskAbs inventoryTaskAbs;

    @Resource
    private WorkflowAbs workflowAbs;

    @Resource
    private EntMatAbs entMatAbs;

    @Resource
    private CompanyAssetMapper companyAssetMapper;

    @Override
    public Set<Long> checkUserAsset(List<Long> removeUserIds, Long companyId) {
        // 使用人
        List<Long> usePersonIds = companyAssetMapper.checkUsePerson(removeUserIds, companyId);
        // 所属管理员
        List<Long> managerOwnerIds = companyAssetMapper.checkManagerOwner(removeUserIds, companyId);
        usePersonIds.addAll(managerOwnerIds);
        HashSet<Long> removeUserSet = new HashSet<>(usePersonIds);
        for (Long removeUserId : removeUserIds) {
            // 盘点
            if (!removeUserSet.contains(removeUserId)
                    && inventoryTaskAbs.checkInventory(removeUserId)) {
                removeUserSet.add(removeUserId);
            }
            // 审批流
            if (!removeUserSet.contains(removeUserId)
                    && workflowAbs.checkWorkflow(removeUserId)) {
                removeUserSet.add(removeUserId);
            }
            // 设备保养任务
            if (!removeUserSet.contains(removeUserId) && entMatAbs.entMatUserHasTask(removeUserId)) {
                removeUserSet.add(removeUserId);
            }
        }
        return removeUserSet;
    }

    @Override
    public boolean hasUseAsset(Long removeUserId, Long companyId) {
        List<Long> result = companyAssetMapper.checkUsePerson(Collections.singletonList(removeUserId), companyId);
        return CollUtil.isNotEmpty(result);
    }

    @Override
    public boolean hasManageAsset(Long removeUserId, Long companyId) {
        List<Long> managerOwnerIds = companyAssetMapper.checkManagerOwner(Collections.singletonList(removeUserId), companyId);
        return CollUtil.isNotEmpty(managerOwnerIds);
    }

    @Override
    public boolean hasApproval(Long removeUserId, Long companyId) {
        return workflowAbs.checkWorkflow(removeUserId);
    }

    @Override
    public boolean hasEntMatTask(Long removeUserId, Long companyId) {
        return entMatAbs.entMatUserHasTask(removeUserId);
    }

    @Override
    public boolean notAllowRemove(Long removeUserId, Long companyId) {
        return hasUseAsset(removeUserId, companyId) || hasManageAsset(removeUserId, companyId) || hasApproval(removeUserId, companyId) || hasEntMatTask(removeUserId, companyId);
    }
}
