package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsOpenApiScopeMapper;
import com.niimbot.asset.system.model.AsOpenApiScope;
import com.niimbot.asset.system.service.AsOpenApiScopeService;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/18 11:09
 */
@Service
@RequiredArgsConstructor
public class AsOpenApiScopeServiceImpl extends ServiceImpl<AsOpenApiScopeMapper, AsOpenApiScope> implements AsOpenApiScopeService {
}
