package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.SalesOwnerDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import cn.hutool.core.date.DateTime;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-14
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsCompanyMapper.class)
public interface AsCompanyMapper extends BaseMapper<AsCompany> {

    Integer selectCountOfExcludeTest(DateTime date);

    Integer selectEnterpriseSize(@Param("companyId") Long companyId);

    List<String> selectCompanyTag(@Param("companyId") Long companyId);

    @Select("SELECT " +
            " so.company_id, " +
            " so.agent_name, " +
            " so.pre_sales, " +
            " pre.emp_name AS pre_sales_text, " +
            " pre.udesk_id as pre_udesk_id, " +
            " so.post_sales, " +
            " post.emp_name AS post_sales_text, " +
            " post.udesk_id as post_udesk_id " +
            " FROM " +
            " as_company_sales_owner so " +
            " left join as_admin_user pre on so.pre_sales = pre.id " +
            " left join as_admin_user post on so.post_sales = post.id " +
            " WHERE " +
            " so.company_id = #{companyId}")
    SalesOwnerDto getSalesOwner(@Param("companyId") Long companyId);
}
