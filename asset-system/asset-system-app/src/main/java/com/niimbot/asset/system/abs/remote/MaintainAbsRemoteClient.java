package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.MaintainAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/7/13 11:55
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.maintenance.domain.abs.impl.MaintainAbsImpl")
@FeignClient(name = "asset-maintenance", url = "https://{gateway}/client/abs/maintenance/maintainAbs/")
public interface MaintainAbsRemoteClient extends MaintainAbs {
}
