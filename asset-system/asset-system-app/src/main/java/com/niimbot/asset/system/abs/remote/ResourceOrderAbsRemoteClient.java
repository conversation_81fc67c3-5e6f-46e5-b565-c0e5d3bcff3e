package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.ResourceOrderAbs;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2024/5/23 14:34
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.sale.domain.abs.impl.ResourceOrderAbsImpl")
@FeignClient(name = "asset-sale", url = "https://{gateway}///client/abs/resource/resourceOrderAbs")
public interface ResourceOrderAbsRemoteClient extends ResourceOrderAbs {
}
