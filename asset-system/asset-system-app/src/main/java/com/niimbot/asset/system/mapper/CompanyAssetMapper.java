package com.niimbot.asset.system.mapper;

import com.niimbot.means.AssetDto;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/8 11:03
 */
@Repository
public interface CompanyAssetMapper {

    List<AssetDto> selectUseOrgAsset(@Param("orgId") Long orgId, @Param("companyId") Long companyId);

    List<AssetDto> selectOrgOwnerAsset(@Param("orgId") Long orgId, @Param("companyId") Long companyId);

    List<Long> checkUseOrg(@Param("orgIds") List<Long> orgIds, @Param("companyId") Long companyId);

    List<Long> checkOrgOwner(@Param("orgIds") List<Long> orgIds, @Param("companyId") Long companyId);

    List<Long> checkUsePerson(@Param("userIds") List<Long> userIds, @Param("companyId") Long companyId);

    List<Long> checkManagerOwner(@Param("userIds") List<Long> userIds, @Param("companyId") Long companyId);

    void updateAreaOrgId(@Param("oldId") Long oldId, @Param("newId") Long newId);

    void updateRepositoryOrgId(@Param("oldId") Long oldId, @Param("newId") Long newId);
}
