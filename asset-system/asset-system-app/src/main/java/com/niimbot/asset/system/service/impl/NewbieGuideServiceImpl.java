package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.system.mapper.AsNewbieGuideMapper;
import com.niimbot.asset.system.model.AsNewbieGuide;
import com.niimbot.asset.system.service.NewbieGuideService;
import com.niimbot.system.NewbieGuideDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NewbieGuideServiceImpl extends ServiceImpl<AsNewbieGuideMapper, AsNewbieGuide> implements NewbieGuideService {

    @Resource
    private RedisService redisService;

    @Override
    public Boolean click(Long id) {
        redisService.hIncr(BaseConstant.NEWBIE_GUIDE, String.valueOf(id), 1L);
        return true;
    }

    @Override
    public List<NewbieGuideDto> allGuide() {
        List<AsNewbieGuide> guides = this.list();
        return guides.stream().map(asNewbieGuide -> BeanUtil.copyProperties(asNewbieGuide, NewbieGuideDto.class)).collect(Collectors.toList());
    }
}
