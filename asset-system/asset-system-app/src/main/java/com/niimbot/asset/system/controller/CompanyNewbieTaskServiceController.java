package com.niimbot.asset.system.controller;

import com.google.common.collect.ImmutableMap;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.NewbieTaskConstant;
import com.niimbot.asset.system.model.AsCompanyNewbieTask;
import com.niimbot.asset.system.service.CompanyNewbieTaskService;
import com.niimbot.system.CompanyNewbieTaskDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("server/system/company/newbie/task")
@RequiredArgsConstructor
public class CompanyNewbieTaskServiceController {

    private final CompanyNewbieTaskService companyNewbieTaskService;

    @GetMapping("/list/{companyId}")
    public List<CompanyNewbieTaskDto> tasks(@PathVariable Long companyId) {
        return companyNewbieTaskService.listTask(companyId);
    }

    @PutMapping("/completed/{id}")
    public Boolean completed(@PathVariable Long id) {
        return companyNewbieTaskService.update(
                Wrappers.lambdaUpdate(AsCompanyNewbieTask.class)
                        .set(AsCompanyNewbieTask::getStatus, NewbieTaskConstant.TASK_STATUS_COMPLETED)
                        .eq(AsCompanyNewbieTask::getId, id)
        );
    }

    @PutMapping("/ignored/{id}")
    public Boolean ignored(@PathVariable Long id) {
        return companyNewbieTaskService.update(
                Wrappers.lambdaUpdate(AsCompanyNewbieTask.class)
                        .set(AsCompanyNewbieTask::getStatus, NewbieTaskConstant.TASK_STATUS_IGNORED)
                        .eq(AsCompanyNewbieTask::getId, id)
        );
    }

    @GetMapping("/progressRate/{companyId}")
    public Map<String, Integer> progressRate(@PathVariable Long companyId) {
        int total = Convert.toInt(companyNewbieTaskService.count(
                Wrappers.lambdaQuery(AsCompanyNewbieTask.class)
                        .eq(AsCompanyNewbieTask::getCompanyId, companyId)
        ));
        int processed = Convert.toInt(companyNewbieTaskService.count(
                Wrappers.lambdaQuery(AsCompanyNewbieTask.class)
                        .eq(AsCompanyNewbieTask::getCompanyId, companyId)
                        .ne(AsCompanyNewbieTask::getStatus, NewbieTaskConstant.TASK_STATUS_INIT)
        ));
        return ImmutableMap.of("total", total, "processed", processed);
    }

    @GetMapping("/isAllProcessed/{companyId}")
    public Boolean isAllProcessed(@PathVariable Long companyId) {
        long count = companyNewbieTaskService.count(
                Wrappers.lambdaQuery(AsCompanyNewbieTask.class)
                        .eq(AsCompanyNewbieTask::getCompanyId, companyId)
                        .eq(AsCompanyNewbieTask::getStatus, NewbieTaskConstant.TASK_STATUS_INIT)
        );
        return count == 0;
    }
}
