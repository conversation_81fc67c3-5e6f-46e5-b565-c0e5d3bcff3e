package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsRoleMenu;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 角色-菜单关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-26
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsRoleMenuMapper.class)
public interface AsRoleMenuMapper extends BaseMapper<AsRoleMenu> {

}
