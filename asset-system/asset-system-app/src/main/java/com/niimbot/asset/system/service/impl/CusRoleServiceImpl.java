package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.event.EmpInfoChangeEvent;
import com.niimbot.asset.system.mapper.AsCusRoleMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsCusMenuInit;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.AsRoleMenu;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeSettingService;
import com.niimbot.asset.system.service.AsCusMenuInitService;
import com.niimbot.asset.system.service.AsDataPermissionService;
import com.niimbot.asset.system.service.AsRoleDataAuthorityService;
import com.niimbot.asset.system.service.AsRoleMenuService;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.CusMenuService;
import com.niimbot.asset.system.service.CusRoleService;
import com.niimbot.asset.system.service.CusUserRoleService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.asset.system.service.UserSensitiveAuthorityService;
import com.niimbot.asset.system.service.handler.WeixinAdapter;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusMenuDto;
import com.niimbot.system.CusMenuRoleDto;
import com.niimbot.system.CusRoleAccountDto;
import com.niimbot.system.CusRoleAccountQueryDto;
import com.niimbot.system.CusRoleDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 * @since 2020/10/27 14:33
 */
@Service
public class CusRoleServiceImpl extends ServiceImpl<AsCusRoleMapper, AsCusRole> implements CusRoleService {

    @Autowired
    private CusUserService cusUserService;

    @Autowired
    private AsRoleMenuService roleMenuService;

    @Autowired
    private CusMenuService cusMenuService;

    @Autowired
    private CusRoleService cusRoleService;

    @Resource
    private CusUserRoleService userRoleService;

    @Autowired
    private AsCusEmployeeService employeeService;

    @Autowired
    private AsUserOrgService userOrgService;

    @Autowired
    private AsCusEmployeeExtService cusEmployeeExtService;

    @Autowired
    private AsCusEmployeeSettingService cusUserSettingService;

    @Autowired
    private DataAuthorityService dataAuthorityService;

    @Autowired
    private AsAccountEmployeeService accountEmployeeService;

    @Autowired
    private AsDataPermissionService dataPermissionService;
    @Autowired
    private AsCusMenuInitService cusMenuInitService;
    @Autowired
    private AsRoleDataAuthorityService roleDataAuthorityService;

    @Autowired
    private UserSensitiveAuthorityService userSensitiveAuthorityService;
    @Autowired
    private ModelDataScopeServiceImpl modelDataScopeService;

    /**
     * 添加角色和菜单权限
     *
     * @param dto 角色
     * @return 最大账号信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusRoleDto dto) {
        Long roleId = IdUtils.getId();
        dto.setId(roleId);
        AsCusRole cusRole = BeanUtil.copyProperties(dto, AsCusRole.class);
        // 校验name和code是否存在重复
        if (this.count(new QueryWrapper<AsCusRole>().lambda()
                .eq(AsCusRole::getRoleName, cusRole.getRoleName())
                .or().eq(StrUtil.isNotBlank(cusRole.getRoleCode()), AsCusRole::getRoleCode, cusRole.getRoleCode())) > 0) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "角色名称", cusRole.getRoleName());
        }
        // 添加角色
        if (!this.save(cusRole)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        roleSetting(dto, Boolean.FALSE);
        //初始化角色数据权限，默认给普通员工的数据权限
        roleDataAuthorityService.initRoleDataAuth(LoginUserThreadLocal.getCompanyId(), roleId, BaseConstant.COMMON_ROLE);
        return true;
    }

    private List<String> roleSetting(CusRoleDto dto, Boolean isEdit) {
        // 先删除全部
        roleMenuService.remove(new QueryWrapper<AsRoleMenu>().lambda().eq(AsRoleMenu::getRoleId, dto.getId()));

        List<AsRoleMenu> roleMenuList = new ArrayList<>();
        //PC菜单角色
        if (CollUtil.isNotEmpty(dto.getPcMenuIds())) {
            List<AsRoleMenu> pcRoleMenu = dto.getPcMenuIds().stream().map(menuId -> new AsRoleMenu()
                            .setRoleId(dto.getId())
                            .setMenuId(menuId)
                            .setType(AssetConstant.TERMINAL_PC))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pcRoleMenu)) {
                roleMenuList.addAll(pcRoleMenu);
            }
        }

        //App菜单角色
        if (CollUtil.isNotEmpty(dto.getAppMenuIds())) {
            List<AsRoleMenu> appRoleMenu = dto.getAppMenuIds().stream().map(menuId -> new AsRoleMenu()
                            .setRoleId(dto.getId())
                            .setMenuId(menuId)
                            .setType(AssetConstant.TERMINAL_APP))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(appRoleMenu)) {
                roleMenuList.addAll(appRoleMenu);
            }
        }

        //新增角色的时候，需要初始默认普通员工的菜单权限
        if (!isEdit) {
            AsCusRole defaultRole = queryDefaultRole(LoginUserThreadLocal.getCompanyId());
            if (Objects.isNull(defaultRole)) {
                //默认员工为空，取系统配置的默认菜单权限
                List<AsCusMenuInit> menuInitList = cusMenuInitService.list(Wrappers.lambdaQuery(AsCusMenuInit.class)
                        .eq(AsCusMenuInit::getRoleCode, BaseConstant.COMMON_ROLE));
                if (CollUtil.isNotEmpty(menuInitList)) {
                    List<AsRoleMenu> pcRoleMenuList = menuInitList.stream()
                            .map(item -> new AsRoleMenu().setRoleId(dto.getId()).setMenuId(item.getMenuId()).setType(item.getType()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(pcRoleMenuList)) {
                        roleMenuList.addAll(pcRoleMenuList);
                    }
                }
            } else {
                //取默认角色的菜单权限
                List<AsRoleMenu> defaultRoleMenuList = roleMenuService.list(Wrappers.lambdaQuery(AsRoleMenu.class).eq(AsRoleMenu::getRoleId, defaultRole.getId()));
                if (CollUtil.isNotEmpty(defaultRoleMenuList)) {
                    roleMenuList.addAll(defaultRoleMenuList.stream()
                            .map(item -> new AsRoleMenu().setRoleId(dto.getId()).setMenuId(item.getMenuId()).setType(item.getType()))
                            .collect(Collectors.toList()));
                }
            }
        }

        this.roleMenuService.saveBatch(roleMenuList);

        //角色修改了，通知外层用户更新token
        List<String> changeRole = new ArrayList<>();
        changeRole.add(AssetConstant.TERMINAL_PC);
        changeRole.add(AssetConstant.TERMINAL_APP);
        return changeRole;
    }

    /**
     * 编辑角色和菜单权限
     *
     * @param dto 角色
     * @return 最大账号信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> edit(CusRoleDto dto) {
        AsCusRole cusRole = BeanUtil.copyProperties(dto, AsCusRole.class);
        // 检查超级管理员、超级管理员不能编辑
        AsCusRole role = this.getOne(new QueryWrapper<AsCusRole>().lambda().eq(AsCusRole::getId, cusRole.getId()));
        if (ObjectUtil.isNull(role)) {
            throw new BusinessException(SystemResultCode.ROLE_NOT_EXISTS);
        }
        if (StrUtil.equals(role.getRoleCode(), BaseConstant.ADMIN_ROLE)) {
            throw new BusinessException(SystemResultCode.CANT_EDIT_ADMIN);
        }

        // 校验name和code是否存在重复
        if (this.count(new QueryWrapper<AsCusRole>().lambda()
                .ne(AsCusRole::getId, cusRole.getId())
                .eq(AsCusRole::getRoleName, cusRole.getRoleName())) > 0) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "角色名称", cusRole.getRoleName());
        }
        // 更新角色
        if (!this.updateById(cusRole)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return roleSetting(dto, Boolean.TRUE);
    }

    /**
     * 删除角色
     *
     * @param roleIds@return 最大账号信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<Long> roleIds) {
        for (Long roleId : roleIds) {
            // 检查超级管理员
            checkAdminCommon(roleId);
            // 检查存在员工
            checkRefUser(roleId);
        }

        //查询是否删除了默认角色
        long defaultRoleCount = this.count(Wrappers.lambdaQuery(AsCusRole.class)
                .eq(AsCusRole::getIsDefault, Boolean.TRUE)
                .eq(AsCusRole::getIsDelete, Boolean.FALSE)
                .eq(AsCusRole::getStatus, (short)1).in(AsCusRole::getId, roleIds));
        // 判断是否全部删除成功
        if (this.getBaseMapper().deleteBatchIds(roleIds) != roleIds.size()) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }

        //默认角色被删除，需要设置普通员工为默认角色
        if (defaultRoleCount > 0) {
            this.update(Wrappers.lambdaUpdate(AsCusRole.class)
                    .eq(AsCusRole::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsCusRole::getRoleCode, BaseConstant.COMMON_ROLE).set(AsCusRole::getIsDefault, Boolean.TRUE));
        }
        return true;
    }

    @Override
    public List<AsCusRole> listByCompanyId(Long companyId) {
        return getBaseMapper().listByCompanyId(companyId);
    }

    /**
     * 查询公司角色权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public CusMenuRoleDto getSetting(Long roleId) {
        // 查看是否超级管理员
        AsCusRole role = cusRoleService.getById(roleId);
        if (BaseConstant.ADMIN_ROLE.equals(role.getRoleCode())) {
            List<Long> pcMenus = cusMenuService.allPcMenu().stream().map(CusMenuDto::getId)
                    .collect(Collectors.toList());
            List<Long> appMenus = cusMenuService.allAppMenu().stream().map(CusMenuDto::getId)
                    .collect(Collectors.toList());
            return new CusMenuRoleDto()
                    .setPc(pcMenus)
                    .setApp(appMenus);
        } else {
            List<AsRoleMenu> list = roleMenuService.list(new QueryWrapper<AsRoleMenu>().lambda()
                    .eq(AsRoleMenu::getRoleId, roleId));
            List<Long> pcMenus = new ArrayList<>();
            List<Long> appMenus = new ArrayList<>();
            list.forEach(roleMenu -> {
                if (AssetConstant.TERMINAL_PC.equals(roleMenu.getType())) {
                    pcMenus.add(roleMenu.getMenuId());
                } else if (AssetConstant.TERMINAL_APP.equals(roleMenu.getType())) {
                    appMenus.add(roleMenu.getMenuId());
                }
            });
            return new CusMenuRoleDto()
                    .setPc(pcMenus)
                    .setApp(appMenus);
        }
    }

    /**
     * 查询获取角色信息
     *
     * @param userId 用户ID
     * @return CusRoleDto信息
     */
    @Override
    public List<CusRoleDto> getRoleByEmployeeId(Long userId) {
        return this.getBaseMapper().getRoleByEmployeeId(userId);
    }

    /**
     * 根据角色Id查询用户
     *
     * @param roleId 用户Id
     * @return 用户信息
     */
    @Override
    public List<AsCusUser> listUserByRoleId(Long roleId) {
        return getBaseMapper().listUserByRoleId(roleId, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public List<CusRoleAccountDto> roleAccountList(CusRoleAccountQueryDto roleAccountQueryDto) {
        // 企业微信兼容，员工搜索
        if (StrUtil.isNotEmpty(roleAccountQueryDto.getEmpName()) && Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).empSearch(roleAccountQueryDto.getEmpName());
            return this.getBaseMapper().roleAccountList(roleAccountQueryDto, unionIds);
        } else {
            return this.getBaseMapper().roleAccountList(roleAccountQueryDto, null);
        }
    }

    @Override
    public CusRoleDto getInfo(Long roleId) {
        AsCusRole cusRole = cusRoleService.getById(roleId);
        CusMenuRoleDto setting = cusRoleService.getSetting(roleId);
        return BeanUtil.copyProperties(cusRole, CusRoleDto.class)
                .setPcMenuIds(setting.getPc())
                .setAppMenuIds(setting.getApp());
    }

    /**
     * 判断是否超级管理员、员工
     */
    private void checkAdminCommon(Long roleId) {
        AsCusRole role = this.getOne(new QueryWrapper<AsCusRole>().lambda().eq(AsCusRole::getId, roleId));
        if (roleId != null && role != null && !role.getCanDelete()) {
            throw new BusinessException(SystemResultCode.CANT_DELETE_DEFAULT_ROLE);
        }
    }

    /**
     * 判断是否存在员工
     */
    private void checkRefUser(Long roleId) {
        // 判断该角色是否存在员工
        if (cusUserService.hasUserByRoleId(roleId)) {
            throw new BusinessException(SystemResultCode.ROLE_EXISTS_USER);
        }
    }

    /**
     * 超转转让
     *
     * @param data
     * @return true or false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean supertubeTransfer(Map<String, Long> data) {
        Long oldAdmin = data.get("oldAdmin");
        Long newAdmin = data.get("newAdmin");
        AsCusEmployee old = employeeService.getById(oldAdmin);
        AsCusEmployee newEmp = employeeService.getById(newAdmin);
        // 新超管没有账号时不让转让
        Optional<AsAccountEmployee> optional = accountEmployeeService.getEmployeeAccount(newAdmin);
        if (!optional.isPresent()) {
            throw new BusinessException(SystemResultCode.EMP_INACTIVE_ACCOUNT);
        }
        AsCusEmployeeExt cusUserExt = this.cusEmployeeExtService.getById(oldAdmin);

        AsCusRole adminRole = this.getOne(Wrappers.<AsCusRole>lambdaQuery().eq(AsCusRole::getRoleCode, BaseConstant.ADMIN_ROLE));
        AsCusRole commonRole = this.getOne(Wrappers.<AsCusRole>lambdaUpdate().eq(AsCusRole::getRoleCode, BaseConstant.COMMON_ROLE));
        // 原超管修改为普通角色 先删除 在新增为普通角色
        userRoleService.remove(Wrappers.<AsUserRole>lambdaUpdate().eq(AsUserRole::getUserId, oldAdmin));
        employeeService.update(Wrappers.lambdaUpdate(AsCusEmployee.class).set(AsCusEmployee::getPosition, "").eq(AsCusEmployee::getId, oldAdmin));
        userRoleService.save(new AsUserRole().setUserId(oldAdmin).setRoleId(commonRole.getId()));
        employeeService.update(Wrappers.lambdaUpdate(AsCusEmployee.class).set(AsCusEmployee::getDataScope, 1).eq(AsCusEmployee::getId, oldAdmin));

        // 设置新超管 删除原来的所有角色 在新增为超管
        userRoleService.remove(Wrappers.lambdaUpdate(AsUserRole.class).eq(AsUserRole::getUserId, newAdmin));
        userRoleService.save(new AsUserRole().setUserId(newAdmin).setRoleId(adminRole.getId()));
        employeeService.update(Wrappers.lambdaUpdate(AsCusEmployee.class).set(AsCusEmployee::getPosition, "超级管理员").eq(AsCusEmployee::getId, newAdmin));
        employeeService.update(Wrappers.lambdaUpdate(AsCusEmployee.class).set(AsCusEmployee::getDataScope, 0).eq(AsCusEmployee::getId, newAdmin));
        dataAuthorityService.remove(Wrappers.lambdaUpdate(AsDataAuthority.class).eq(AsDataAuthority::getUserId, newAdmin));
        dataPermissionService.initDataPermission(old.getCompanyId(), old.getId(), BaseConstant.COMMON_ROLE);
        dataPermissionService.initDataPermission(newEmp.getCompanyId(), newEmp.getId(), BaseConstant.ADMIN_ROLE);
        // 删除敏感数据
        userSensitiveAuthorityService.removeByUserIds(ListUtil.of(newEmp.getId()), LoginUserThreadLocal.getCompanyId());

        // 判断是否超管被删除
        if (cusUserExt != null && BooleanUtil.isTrue(cusUserExt.getThirdPartyRemove())) {
            // 删除员工数据
            employeeService.removeById(oldAdmin);
            cusUserService.removeById(oldAdmin);
            cusEmployeeExtService.removeById(oldAdmin);
            cusUserSettingService.removeById(oldAdmin);
            userOrgService.remove(new LambdaQueryWrapper<AsUserOrg>().eq(AsUserOrg::getUserId, oldAdmin));
            // 用户的部门有修改，需要删除权限缓存
            modelDataScopeService.cleanDataScopeCache(old.getCompanyId(), ListUtil.of(oldAdmin));
            userRoleService.remove(new LambdaQueryWrapper<AsUserRole>().eq(AsUserRole::getUserId, oldAdmin));
            // 解绑账号
            accountEmployeeService.unbindEmployee(oldAdmin);
        }
        EventPublishHandler.publish(new EmpInfoChangeEvent(true).setAdmin(true).setCompanyId(LoginUserThreadLocal.getCompanyId()));
        return true;
    }

    @Override
    public Boolean configDefault(Long roleId) {
        //校验角色是否存在
        AsCusRole cusRole = this.getOne(Wrappers.lambdaQuery(AsCusRole.class)
                .eq(AsCusRole::getId, roleId)
                .eq(AsCusRole::getIsDelete, Boolean.FALSE).eq(AsCusRole::getStatus, (short)1));
        if (Objects.isNull(cusRole)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前角色不存在");
        }

        //超管不允许设置为默认角色
        if (BaseConstant.ADMIN_ROLE.equalsIgnoreCase(cusRole.getRoleCode())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "超管不允许设置为默认角色");
        }

        //更新为默认角色
        List<AsCusRole> modifyParam = new ArrayList<>();
        AsCusRole defaultRole = this.getOne(Wrappers.lambdaQuery(AsCusRole.class)
                .eq(AsCusRole::getCompanyId, LoginUserThreadLocal.getCompanyId()).eq(AsCusRole::getIsDefault, Boolean.TRUE)
                .eq(AsCusRole::getIsDelete, Boolean.FALSE).eq(AsCusRole::getStatus, (short)1));
        if (Objects.nonNull(defaultRole)) {
            modifyParam.add(new AsCusRole().setId(defaultRole.getId()).setIsDefault(Boolean.FALSE));
        }
        modifyParam.add(new AsCusRole().setId(roleId).setIsDefault(Boolean.TRUE));
        return this.saveOrUpdateBatch(modifyParam);
    }

    @Override
    public AsCusRole queryDefaultRole(Long companyId) {
        //查询企业默认角色
        List<AsCusRole> defaultRoleList = this.list(Wrappers.lambdaQuery(AsCusRole.class)
                .eq(AsCusRole::getCompanyId, companyId)
                .eq(AsCusRole::getIsDefault, 1)
                .eq(AsCusRole::getStatus, 1)
                .eq(AsCusRole::getIsDelete, 0));

        if (CollUtil.isEmpty(defaultRoleList)) {
            return this.getOne(Wrappers.lambdaQuery(AsCusRole.class)
                    .eq(AsCusRole::getCompanyId, companyId)
                    .eq(AsCusRole::getRoleCode, BaseConstant.COMMON_ROLE));
        } else {
            return defaultRoleList.get(0);
        }
    }
}
