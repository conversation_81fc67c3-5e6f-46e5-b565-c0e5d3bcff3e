package com.niimbot.asset.system.service.impl;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.DateUtils;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.system.abs.AssetAreaAbs;
import com.niimbot.asset.system.abs.AssetCategoryAbs;
import com.niimbot.asset.system.abs.AssetQueryViewAbs;
import com.niimbot.asset.system.abs.CompanyResourceAbs;
import com.niimbot.asset.system.abs.FormAbs;
import com.niimbot.asset.system.abs.MaterialOrderTypeAbs;
import com.niimbot.asset.system.abs.MessageAbs;
import com.niimbot.asset.system.abs.OrderTypeAbs;
import com.niimbot.asset.system.abs.PurchaseOrderTypeAbs;
import com.niimbot.asset.system.abs.RepositoryAbs;
import com.niimbot.asset.system.abs.WorkflowAbs;
import com.niimbot.asset.system.dto.AssetAreaLoadCacheCmd;
import com.niimbot.asset.system.dto.AssetAreaSaveCmd;
import com.niimbot.asset.system.dto.AssetCategoryLoadCacheCmd;
import com.niimbot.asset.system.dto.AssetCategoryRegisterCopyCmd;
import com.niimbot.asset.system.dto.AssetCategorySaveBatchCmd;
import com.niimbot.asset.system.dto.AssetQueryViewInitCmd;
import com.niimbot.asset.system.dto.BizFormAssetInitCmd;
import com.niimbot.asset.system.dto.BizFormMaterialInitCmd;
import com.niimbot.asset.system.dto.CommonIndustryAssetCategoryListQry;
import com.niimbot.asset.system.dto.CompanyResourceAddCmd;
import com.niimbot.asset.system.dto.FormInitCmd;
import com.niimbot.asset.system.dto.MaterialOrderTypeInitCmd;
import com.niimbot.asset.system.dto.OrderTypeInitCmd;
import com.niimbot.asset.system.dto.PurchaseOrderTypeInitCmd;
import com.niimbot.asset.system.dto.RepositoryLoadCacheCmd;
import com.niimbot.asset.system.dto.RepositorySaveCmd;
import com.niimbot.asset.system.dto.clientobject.AssetAreaCO;
import com.niimbot.asset.system.dto.clientobject.AssetCategoryCO;
import com.niimbot.asset.system.dto.clientobject.CompanyResourceCO;
import com.niimbot.asset.system.dto.clientobject.RepositoryCO;
import com.niimbot.asset.system.event.CompanyRegisterEvent;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCompanyChannel;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsCusEmployeeSetting;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsIndustry;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsPromoteChannel;
import com.niimbot.asset.system.model.AsPromoteChannelSource;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.model.AsUserReports;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.AccountCenterService;
import com.niimbot.asset.system.service.AsCompanyChannelService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeSettingService;
import com.niimbot.asset.system.service.AsDataPermissionService;
import com.niimbot.asset.system.service.AsRoleDataAuthorityService;
import com.niimbot.asset.system.service.AsRoleMenuService;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.CompanyNewbieTaskService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.CusRoleService;
import com.niimbot.asset.system.service.CusUserRoleService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.UserReportsService;
import com.niimbot.asset.system.util.AccountNumberUtils;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.framework.dataperm.object.Tuple2;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.RegisterDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021-01-04
 */
@Service
@Slf4j
public class RegisterDataInitService {

    @Autowired
    private OrderTypeAbs orderTypeAbs;
    @Autowired
    private MaterialOrderTypeAbs materialOrderTypeAbs;
    @Autowired
    private PurchaseOrderTypeAbs purchaseOrderTypeAbs;
    @Autowired
    private FormAbs formAbs;
    @Autowired
    private MessageAbs messageAbs;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private AccountCenterService accountCenterService;
    @Autowired
    private CompanyNewbieTaskService companyNewbieTaskService;
    @Autowired
    private AssetQueryViewAbs assetQueryViewAbs;
    @Autowired
    private AssetCategoryAbs assetCategoryAbs;
    @Autowired
    private AssetAreaAbs assetAreaAbs;
    @Autowired
    private RepositoryAbs repositoryAbs;

    @Autowired
    private CompanyResourceAbs companyResourceAbs;

    @Resource
    private WorkflowAbs workflowAbs;
    @Resource
    private RedisService redisService;

    public static final String PROGRESS_KEY = "register:progress:";
    private static final Map<String, String> ORG_SUB = ImmutableMap.<String, String>builder()
            .put("A02", "总经办")
            .put("A03", "人力资源部")
            .put("A04", "行政部")
            .put("A05", "财务部").build();

    public AsCusUser init(RegisterDto dto) {
        String unionKey = dto.getMobile() + "_" + dto.getSmsCode();
        // 公司表、公司相关setting
        redisService.lPush(PROGRESS_KEY + unionKey, "初始化公司配置", 600);
        Tuple2<Long, Long> companyIdCommonId = initCompany(dto.getCompanyName(), dto.getMobile(), dto.getIndustryId());
        Long companyId = companyIdCommonId.getFirst();


        AsCusUser cusUser = new AsCusUser();
        cusUser.setCompanyId(companyId);

        // 写入渠道
        initCompanyChannel(companyId, dto.getCh(), dto.getAg());

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化组织数据", 600);
        //公司组织表 顶层数据结构 、数据权限as_data_authority
        Long rootOrgId = initOrg(dto.getCompanyName(), companyId);

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化员工账号数据", 600);
        // 员工表
        Long employeeId = initEmployee(companyId, rootOrgId, dto.getMobile(),dto.getNationalCode());
        // 员工默认属性扩展表 as_cus_employee_ext
        initEmployeeExt(employeeId);
        // 员工首页属性设置表 as_cus_employee_setting
        initEmployeeSetting(employeeId);
        // 员工账号表
        Tuple2<String, Long> tuple2 = initAccount(dto.getUnionId(), dto.getMobile(), companyId, dto.getSource());
        Long accountId = tuple2.getSecond();
        cusUser.setId(accountId);
        cusUser.setAccount(tuple2.getFirst());
        // 员工与账号绑定
        accountCenterService.bindEmployee(companyId, employeeId, accountId, false);

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化角色数据", 600);
        // 公司角色表、管理员角色信息、公司用户表
        initCompanyRole(companyId, employeeId);
        // 合同表 、角色菜单信息
        initCompanyContract(companyId);

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化基础业务数据数据", 600);
        // 公司行业分类
        initCategory(companyIdCommonId.getSecond(), companyId, employeeId);
        // 区域表
        initCompanyArea(companyId, rootOrgId);
        // 仓库表
        initRepository(companyId, rootOrgId);
        // 用户统计表
        initUserReports(companyId);

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化资产表单", 600);
        // 用户资产查询视图
        initAssetQueryView(companyId, employeeId);
        // 资产配置
        formAbs.initAssetItems(new BizFormAssetInitCmd().setCompanyId(companyId));

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化耗材表单", 600);
        // 耗材配置
        formAbs.initMaterialItems(new BizFormMaterialInitCmd().setCompanyId(companyId));

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化单据表单", 600);
        // 单据配置
        orderTypeAbs.initCompanyOrderType(new OrderTypeInitCmd().setCompanyId(companyId));
        // 表单初始化
        formAbs.initCompanyForm(new FormInitCmd().setCompanyId(companyId));
        // 耗材单据配置
        materialOrderTypeAbs.initCompanyOrderType(new MaterialOrderTypeInitCmd().setCompanyId(companyId));
        // 采购单据配置
        purchaseOrderTypeAbs.initCompanyOrderType(new PurchaseOrderTypeInitCmd().setCompanyId(companyId));
        // 审批角色
        workflowAbs.initApproveRoles(companyId);

        redisService.lPush(PROGRESS_KEY + unionKey, "初始化消息规则", 600);
        // 消息规则配置
        messageAbs.companyMessageRuleDataInit(companyId);
        // 新手任务录入
        companyNewbieTaskService.init(companyId);
        EventPublishHandler.publish(new CompanyRegisterEvent(companyId).setXsCode(dto.getXs()));

        redisService.lPush(PROGRESS_KEY + unionKey, "注册完成", 600);
        return cusUser;
    }

    private void initAssetQueryView(Long companyId, Long userId) {
        assetQueryViewAbs.initUserView(new AssetQueryViewInitCmd().setCompanyId(companyId)
                .setEmployeeId(userId));
    }

    private void initUserReports(Long companyId) {
        AsUserReports userReports = new AsUserReports().setCompanyId(companyId).setDayTime(LocalDateTime.now())
                .setWeekTime(DateUtils.getWeeksNum(DateUtils.now()));
        SpringUtil.getBean(UserReportsService.class).save(userReports);
    }

    private void initCompanyContract(Long companyId) {
        // 公司setting
        AsCompanySetting asCompanySetting = new AsCompanySetting().setCompanyId(companyId)
                .setExpandSwitch(new CompanySwitch());
        SpringUtil.getBean(CompanySettingService.class).save(asCompanySetting);
    }

    private void initCompanyArea(Long companyId, Long rootOrgId) {
        AssetAreaCO area = new AssetAreaCO().setId(IdUtils.getId()).setCompanyId(companyId).setAreaCode("01").setAreaName("默认区域")
                .setOrgId(rootOrgId).setPid(0L).setLevel(0).setPaths("0,");
        assetAreaAbs.saveAssetArea(new AssetAreaSaveCmd().setAssetArea(area));
        // 写入redis
        taskExecutor.execute(() -> assetAreaAbs.loadCacheAssetArea(
                new AssetAreaLoadCacheCmd().setAreas(ListUtil.of(area))));
    }

    private void initCompanyRole(Long companyId, Long employeeId) {
        // 公司用户关联信息
        Long assetAdminRoleId = IdUtils.getId();
        Long commonRoleId = IdUtils.getId();
        // 公司角色信息
        List<AsCusRole> roles = Lists.newArrayList(
                new AsCusRole()
                        .setRoleName("超级管理员").setRoleCode(BaseConstant.ADMIN_ROLE)
                        .setCanDelete(false)
                        .setCompanyId(companyId),
                new AsCusRole()
                        .setId(assetAdminRoleId)
                        .setRoleName("资产管理员").setRoleCode(BaseConstant.ASSET_ADMIN_ROLE)
                        .setCanDelete(false)
                        .setCompanyId(companyId),
                new AsCusRole()
                        .setId(commonRoleId)
                        .setRoleName("普通员工").setRoleCode(BaseConstant.COMMON_ROLE)
                        .setCanDelete(false)
                        .setIsDefault(Boolean.TRUE)
                        .setCompanyId(companyId)
        );
        SpringUtil.getBean(CusRoleService.class).saveBatch(roles);

        // 用户管理员角色信息
        AsUserRole adminUserRole = new AsUserRole().setUserId(employeeId);
        roles.stream().filter(asCusRole -> StringUtils.equals(BaseConstant.ADMIN_ROLE, asCusRole.getRoleCode()))
                .findFirst().ifPresent(adminRole -> adminUserRole.setRoleId(adminRole.getId()));
        SpringUtil.getBean(CusUserRoleService.class).save(adminUserRole);
        // 初始化数据权限
        SpringUtil.getBean(AsDataPermissionService.class).initDataPermission(companyId, employeeId, BaseConstant.ADMIN_ROLE);
        // 添加资产管理员初始菜单
        SpringUtil.getBean(AsRoleMenuService.class).initAssetRole(assetAdminRoleId);
        // 初始化资产管理员角色数据权限
        SpringUtil.getBean(AsRoleDataAuthorityService.class).initRoleDataAuth(companyId, assetAdminRoleId, BaseConstant.ASSET_ADMIN_ROLE);
        // 添加员工初始菜单
        SpringUtil.getBean(AsRoleMenuService.class).initCommonRole(commonRoleId);
        // 初始化普通员工角色数据权限
        SpringUtil.getBean(AsRoleDataAuthorityService.class).initRoleDataAuth(companyId, commonRoleId, BaseConstant.COMMON_ROLE);
    }

    private Long initCategory(Long commonIndustryId, Long companyId, Long employeeId) {
        // 复制企业分类
        List<AssetCategoryCO> categories = assetCategoryAbs.listCommonIndustryAssetCategory(
                new CommonIndustryAssetCategoryListQry().setCommonIndustryId(commonIndustryId)
                        .setCompanyId(0L));
        if (CollUtil.isEmpty(categories)) {
            log.error("【注册】行业未配置分类");
            return null;
        }
        List<Long> categoryIds = categories.stream().map(AssetCategoryCO::getId).collect(Collectors.toList());
        Tuple2<List<AssetCategoryCO>, Map<Long, Long>> categoryTuple = assetCategoryAbs.registerCopyAssetCategory(
                new AssetCategoryRegisterCopyCmd().setCompanyId(0L).setCategories(categoryIds));
        List<AssetCategoryCO> newCategoryList = categoryTuple.getFirst();
        newCategoryList.parallelStream().forEach(cate -> cate.setCompanyId(companyId).setCreateBy(employeeId));
        if (CollUtil.isNotEmpty(newCategoryList)) {
            CollUtil.sortByProperty(newCategoryList, "categoryCode");
            AssetCategorySaveBatchCmd saveBatchCmd = new AssetCategorySaveBatchCmd().setCategories(newCategoryList);
            assetCategoryAbs.saveBatchAssetCategory(saveBatchCmd);
        }
        // 写入redis
        AssetCategoryLoadCacheCmd loadCacheCmd = new AssetCategoryLoadCacheCmd().setCategories(newCategoryList);
        taskExecutor.execute(() -> assetCategoryAbs.loadCacheAssetCategory(loadCacheCmd));
        return CollUtil.isNotEmpty(newCategoryList) ? newCategoryList.get(0).getId() : null;
    }

    private Long initEmployee(Long companyId, Long rootOrgId, String mobile,String nationalCode) {
        String empNo = SpringUtil.getBean(AsCusEmployeeService.class)
                .recommendEmpNo(companyId);
        Long employeeId = IdUtils.getId();
        AsCusEmployee admin = new AsCusEmployee()
                .setId(employeeId)
                .setEmpNo(empNo)
                .setEmpName(mobile)
                .setNationalCode(nationalCode)
                .setCompanyId(companyId)
                .setMobile(mobile).setPosition("超级管理员")
                .setDataScope(DataPermRuleType.TYPE_ALL.getValue().shortValue());
        AsCusEmployeeService employeeService = SpringUtil.getBean(AsCusEmployeeService.class);
        employeeService.save(admin);
        //写入组织员工关联关系
        AsUserOrgService userOrgService = SpringUtil.getBean(AsUserOrgService.class);
        userOrgService.save(new AsUserOrg().setUserId(employeeId).setOrgId(rootOrgId));
        // 写入redis
        taskExecutor.execute(() -> employeeService.loadEmpCache(ListUtil.of(admin)));
        return employeeId;
    }

    private Long initOrg(String companyName, Long companyId) {
        // 根节点id
        Long rootOrgId = IdUtils.getId();
        AsOrg rootOrg = new AsOrg();
        rootOrg.setOrgName(companyName);
        rootOrg.setId(rootOrgId);
        rootOrg.setOrgCode("A01");
        rootOrg.setCompanyId(companyId);
        rootOrg.setCompanyOwner(rootOrgId);
        rootOrg.setPid(0L);
        rootOrg.setLevel(0);
        rootOrg.setPaths("0,");
        rootOrg.setOrgType(1);
        OrgService orgService = SpringUtil.getBean(OrgService.class);
        orgService.save(rootOrg);

        // 子节点
        List<AsOrg> subOrg = Lists.newArrayListWithCapacity(4);
        ORG_SUB.forEach((k, v) -> {
            AsOrg org = new AsOrg().setOrgName(v);
            org.setOrgCode(k);
            org.setCompanyId(companyId);
            org.setCompanyOwner(rootOrgId);
            org.setPid(rootOrgId);
            org.setLevel(1);
            org.setPaths("0," + rootOrgId + ",");
            org.setOrgType(2);
            subOrg.add(org);
        });
        orgService.saveBatch(subOrg);
        // 异步写入缓存
        subOrg.add(rootOrg);
        taskExecutor.execute(() -> orgService.loadOrgCache(subOrg));
        return rootOrgId;
    }

    private void initRepository(Long companyId, Long orgId) {
        RepositoryCO repository = new RepositoryCO();
        repository.setName("默认仓库")
                .setCode("A01")
                .setCompanyId(companyId)
                .setManagerOwner(orgId);
        repositoryAbs.saveRepository(new RepositorySaveCmd().setRepository(repository));
        taskExecutor.execute(() -> repositoryAbs.loadCacheRepository(
                new RepositoryLoadCacheCmd().setRepositories(ListUtil.of(repository))));
    }

    private void initEmployeeExt(Long employeeId) {
        // 用户扩展表
        AsCusEmployeeExt asCusEmployeeExt = new AsCusEmployeeExt().setId(employeeId)
                .setCurrentLoginTime(LocalDateTime.now()).setLastLoginTime(LocalDateTime.now());
        SpringUtil.getBean(AsCusEmployeeExtService.class).save(asCusEmployeeExt);
    }

    private void initEmployeeSetting(Long employeeId) {
        // 用户首页setting相关
        AsCusEmployeeSetting cusEmployeeSetting = new AsCusEmployeeSetting()
                .setUserId(employeeId)
                .setAppToolbox(Lists.newArrayList())
                .setPcToolbox(Lists.newArrayList())
                .setPcHome(Lists.newArrayList())
                .setAssetHead(Lists.newArrayList())
                .setMaterialHead(Lists.newArrayList());
        SpringUtil.getBean(AsCusEmployeeSettingService.class).save(cusEmployeeSetting);
    }

    private Tuple2<String, Long> initAccount(String unionId, String mobile, Long companyId, Integer source) {
        log.info("init account params : [unionId:{},mobile:{},companyId:{},source:{}]", unionId, mobile, companyId, source);
        // 手机号已注册 返回当前账号与新生成的员工做绑定
        AsCusUser account = accountCenterService.getAccountByMobile(mobile);
        if (Objects.nonNull(account)) {
            log.info("账号已被注册");
            return new Tuple2<>(account.getAccount(), account.getId());
        }
        // 手机号未注册
        String maxAccount = AccountNumberUtils.getMaxAccount();
        AsCusUser asCusUser = new AsCusUser()
                .setMobile(mobile)
                .setUnionId(unionId)
                .setAccount(maxAccount)
                .setCompanyId(companyId)
                .setNickname(mobile);
        if (null == source) {
            source = ServletUtils.getClientSource().getValue();
        }
        asCusUser.setSource(source);
        SpringUtil.getBean(CusUserService.class).save(asCusUser);
        log.info("新建账号 : mobile[{}]", asCusUser.getMobile());
        Long userId = asCusUser.getId();
        return new Tuple2<>(maxAccount, userId);
    }

    private Tuple2<Long, Long> initCompany(String companyName, String mobile, Long industryId) {
        // 公司信息
        AsIndustry industry = Db.getById(industryId, AsIndustry.class);
        Preconditions.checkNotNull(industry, "所选行业不存在、请检查");
        List<AsIndustry> parent = Db.list(Wrappers.lambdaQuery(AsIndustry.class).eq(AsIndustry::getPid, industryId));
        if (CollUtil.isNotEmpty(parent)) {
            throw new BusinessException(SystemResultCode.USER_REGISTER_INDUSTRY_NOT_LEGAL);
        }
        final Long id = IdUtils.getId();
        final AsCompany asCompany = new AsCompany()
                .setId(id)
                .setName(companyName)
                .setPhone(mobile)
                .setIndustryId(industry.getId())
                .setIndustryName(industry.getIndustryName());
        Db.save(asCompany);

        // 赠送企业资源包
        CompanyResourceCO resourceCO = new CompanyResourceCO();
        resourceCO.setCompanyId(asCompany.getId())
                .setSkuCode("Trial")
                .setResourceName("体验版")
                .setCapacity(10)
                .setExperience(true)
                .setEffectiveTime(LocalDateTime.now())
                .setExpirationTime(LocalDateTime.now().plusYears(10).withHour(23).withMinute(59).withSecond(59).withNano(0));
        companyResourceAbs.saveResource(new CompanyResourceAddCmd().setCompanyResource(resourceCO));
        return new Tuple2<>(asCompany.getId(), industry.getId());
    }

    private void initCompanyChannel(Long companyId, Long ch, Long ag) {
        AsCompanyChannel channel = new AsCompanyChannel();
        channel.setId(companyId);
        AsCompanyChannelService channelService = SpringUtil.getBean(AsCompanyChannelService.class);
        AsPromoteChannel promoteChannel = channelService.getPromoteChannelByCode(ch);
        AsPromoteChannelSource source = channelService.getAsPromoteChannelSourceByCode(ag);
        if ((promoteChannel == null || source == null) || !source.getPromoteChannelId().equals(promoteChannel.getId())) {
            channel.setChannelCode(0L);
            channel.setSourceCode(0L);
        } else {
            channel.setChannelCode(ch);
            channel.setSourceCode(ag);
        }
        channelService.save(channel);
    }
}
