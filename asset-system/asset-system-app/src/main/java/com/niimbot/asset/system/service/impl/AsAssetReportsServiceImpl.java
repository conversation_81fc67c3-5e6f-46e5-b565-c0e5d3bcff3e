package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsAssetReportsMapper;
import com.niimbot.asset.system.model.AsAssetReports;
import com.niimbot.asset.system.service.AsAssetReportsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新增资产数统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Service
public class AsAssetReportsServiceImpl extends ServiceImpl<AsAssetReportsMapper, AsAssetReports> implements AsAssetReportsService {

}
