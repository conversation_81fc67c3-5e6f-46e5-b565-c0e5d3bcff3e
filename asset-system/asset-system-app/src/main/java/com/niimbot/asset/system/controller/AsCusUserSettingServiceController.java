package com.niimbot.asset.system.controller;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsAssetAdvertise;
import com.niimbot.asset.system.model.AsCusEmployeeSetting;
import com.niimbot.asset.system.model.AsToolbox;
import com.niimbot.asset.system.model.AsToolboxStatistics;
import com.niimbot.asset.system.service.AsCusEmployeeSettingService;
import com.niimbot.asset.system.service.AsToolboxService;
import com.niimbot.asset.system.service.AssetAdvertiseService;
import com.niimbot.asset.system.service.CusMenuService;
import com.niimbot.asset.system.service.ToolboxStatisticsService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.AssetAdvertiseDto;
import com.niimbot.system.CusMenuDto;
import com.niimbot.system.ToolboxGroupDto;
import com.niimbot.system.ToolboxGroupItemDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户首页配置控制器
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@RestController
@RequestMapping("server/system/userSetting")
@Slf4j
@Validated
public class AsCusUserSettingServiceController {

    @Autowired
    private AsCusEmployeeSettingService userSettingService;
    @Autowired
    private AsToolboxService toolboxService;
    @Autowired
    private CusMenuService cusMenuService;
    @Autowired
    private ToolboxStatisticsService toolboxStatisticsService;
    @Autowired
    private AssetAdvertiseService assetAdvertiseService;

    @PostMapping
    public Boolean saveOrUpdate(@RequestBody AsCusEmployeeSetting setting) {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        AsCusEmployeeSetting byId = userSettingService.getById(userId);
        setting.setUserId(userId);
        if (byId == null && !userSettingService.save(setting)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }

        //解决线上bug，修改一个会把其他字段置为null
        if (CollUtil.isNotEmpty(setting.getAppToolbox())) {
            byId.setAppToolbox(setting.getAppToolbox());
        }
        if (CollUtil.isNotEmpty(setting.getPcToolbox())) {
            byId.setPcToolbox(setting.getPcToolbox());
        }
        if (CollUtil.isNotEmpty(setting.getAssetSearchField())) {
            byId.setAssetSearchField(setting.getAssetSearchField());
        }
        if (CollUtil.isNotEmpty(setting.getMaterialSearchField())) {
            byId.setMaterialSearchField(setting.getMaterialSearchField());
        }
        if (!userSettingService.updateById(byId)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @Deprecated
    @GetMapping("/app/getUserToolBox")
    public List<AsToolbox> getUserAppToolBox() {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        AsCusEmployeeSetting setting = userSettingService.getById(userId);
        List<AsToolbox> systemToolBox = toolboxService.getAppToolBox();

        //获取当前用户权限
        AppCusMenuDto cusMenuDto = cusMenuService.userMenuAppList();
        List<String> menuCodeList = new ArrayList<>();
        if (Objects.nonNull(cusMenuDto) && CollUtil.isNotEmpty(cusMenuDto.getMenus())) {
            menuCodeList = cusMenuDto.getMenus().stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        }

        //没有自定义首页工具箱的时候
        if (setting == null || CollUtil.isEmpty(setting.getAppToolbox())) {
            //兼容是否常用，和以前的保持一致，现在是commonlyUsed字段，以前是position字段
            if (CollUtil.isNotEmpty(systemToolBox)) {
                for (AsToolbox item : systemToolBox) {
                    if (Objects.nonNull(item.getCommonlyUsed())) {
                        item.setPosition((short) (item.getCommonlyUsed() + 1));
                    }
                }

                //权限过滤
                List<String> finalMenuCodeList = menuCodeList;
                if (CollUtil.isNotEmpty(finalMenuCodeList)) {
                    systemToolBox = systemToolBox.stream().filter(item -> finalMenuCodeList.contains(item.getToolboxCode())).collect(Collectors.toList());
                }
            }
            return systemToolBox;
        }
        Map<Long, AsToolbox> sysToolBoxMap = systemToolBox.stream().collect(
                Collectors.toMap(AsToolbox::getId, box -> box));
        List<AsToolbox> toolboxes = setting.getAppToolbox();
        // 合并系统和当前的返回
        // 1、修改运营后台更改后的属性
        toolboxes.forEach(userToolBox -> {
            AsToolbox toolbox = sysToolBoxMap.get(userToolBox.getId());
            if (toolbox != null) {
                userToolBox.setToolboxName(toolbox.getToolboxName())
                        .setOrderType(toolbox.getOrderType())
                        .setShowName(toolbox.getShowName())
                        .setToolboxCode(toolbox.getToolboxCode())
                        .setToolboxIcon(toolbox.getToolboxIcon());
            }
        });
        // 2、获取appToolbox里面没有，系统有的工具
        List<Long> idList = toolboxes.stream().map(AsToolbox::getId).collect(Collectors.toList());
        List<AsToolbox> collect = systemToolBox.stream().filter(toolbox -> !idList.contains(toolbox.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            //兼容是否常用，和以前的保持一致，现在是commonlyUsed字段，以前是position字段
            for (AsToolbox item : collect) {
                if (Objects.nonNull(item.getCommonlyUsed())) {
                    item.setPosition((short) 1);
                }
            }
            toolboxes.addAll(collect);
            // 回写数据
            setting.setAppToolbox(toolboxes);
        }

        //根据权限过滤，过滤当前用户没有权限的菜单
        List<AsToolbox> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(menuCodeList)) {
            List<String> finalMenuCodeList1 = menuCodeList;
            result = toolboxes.stream().filter(item -> finalMenuCodeList1.contains(item.getToolboxCode())).collect(Collectors.toList());
        }
        return result;
    }

    @GetMapping("/app/getUserToolBox/v2")
    public List<ToolboxGroupDto> getUserAppToolBoxV2() {
        List<ToolboxGroupDto> result = new ArrayList<>();
        List<ToolboxGroupItemDto> systemToolBox = toolboxService.getWorkbench(2);

        Long userId = LoginUserThreadLocal.getCurrentUserId();
        AsCusEmployeeSetting setting = userSettingService.getById(userId);

        //获取当前用户权限
        AppCusMenuDto cusMenuDto = cusMenuService.userMenuAppList();
        List<String> menuCodeList = new ArrayList<>();
        if (Objects.nonNull(cusMenuDto) && CollUtil.isNotEmpty(cusMenuDto.getMenus())) {
            menuCodeList = cusMenuDto.getMenus().stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        }

        //兼容是否常用，和以前的保持一致，现在是commonlyUsed字段，以前是position字段
        for (ToolboxGroupItemDto item : systemToolBox) {
            if (Objects.nonNull(item.getPosition())) {
                item.setPosition(item.getPosition() + 1);
            }
        }

        //权限过滤
        if (CollUtil.isNotEmpty(menuCodeList)) {
            List<String> finalMenuCodeList = menuCodeList;
            systemToolBox = systemToolBox.stream().filter(item ->
                    finalMenuCodeList.contains(item.getToolboxCode())).collect(Collectors.toList());
        }

        //没有自定义首页工具箱的时候
        if (setting != null && CollUtil.isNotEmpty(setting.getAppToolbox())) {
            List<AsToolbox> toolboxes = setting.getAppToolbox();
            List<Long> idList = toolboxes.stream().map(AsToolbox::getId).collect(Collectors.toList());
            systemToolBox.forEach(f -> {
                f.setPosition(1);
                if (idList.contains(f.getId())) {
                    f.setPosition(2);
                }
            });
        }
        Map<Long, List<ToolboxGroupItemDto>> toolboxGroupMap = systemToolBox.stream()
                .filter(f -> f.getGroupId() != null)
                .collect(Collectors.groupingBy(ToolboxGroupItemDto::getGroupId));
        for (Map.Entry<Long, List<ToolboxGroupItemDto>> item : toolboxGroupMap.entrySet()) {
            ToolboxGroupDto toolboxGroupDto = new ToolboxGroupDto();
            toolboxGroupDto.setGroupName(item.getValue().get(0).getGroupName());
            toolboxGroupDto.setSortNum(item.getValue().get(0).getGroupSortNum());
            toolboxGroupDto.setToolboxGroupItemDtoList(item.getValue().stream().sorted(Comparator.comparing(ToolboxGroupItemDto::getSortNum)).collect(Collectors.toList()));
            result.add(toolboxGroupDto);
        }
        return result.stream().sorted(Comparator.comparing(ToolboxGroupDto::getSortNum).reversed()).collect(Collectors.toList());
    }

    @GetMapping("/app/commonlyUsed")
    public List<ToolboxGroupItemDto> commonlyUsed() {
        List<ToolboxGroupItemDto> systemToolBox = toolboxService.getWorkbench(2);

        Long userId = LoginUserThreadLocal.getCurrentUserId();
        AsCusEmployeeSetting setting = userSettingService.getById(userId);

        //获取当前用户权限
        AppCusMenuDto cusMenuDto = cusMenuService.userMenuAppList();
        List<String> menuCodeList = new ArrayList<>();
        if (Objects.nonNull(cusMenuDto) && CollUtil.isNotEmpty(cusMenuDto.getMenus())) {
            menuCodeList = cusMenuDto.getMenus().stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        }

        //兼容是否常用，和以前的保持一致，现在是commonlyUsed字段，以前是position字段
        for (ToolboxGroupItemDto item : systemToolBox) {
            if (Objects.nonNull(item.getPosition())) {
                item.setPosition(item.getPosition() + 1);
            }
        }

        //权限过滤
        if (CollUtil.isNotEmpty(menuCodeList)) {
            List<String> finalMenuCodeList = menuCodeList;
            systemToolBox = systemToolBox.stream().filter(item ->
                    finalMenuCodeList.contains(item.getToolboxCode())).collect(Collectors.toList());
        }

        //没有自定义首页工具箱的时候
        if (setting != null && CollUtil.isNotEmpty(setting.getAppToolbox())) {
            List<AsToolbox> toolboxes = setting.getAppToolbox();
            Map<Long, Integer> idMap = toolboxes.stream().collect(Collectors.toMap(AsToolbox::getId, AsToolbox::getSortNum));
            systemToolBox.forEach(f -> {
                f.setPosition(1);
                if (idMap.containsKey(f.getId())) {
                    f.setPosition(2);
                    f.setSortNum(idMap.get(f.getId()));
                }
            });
        }
        return systemToolBox.stream().filter(f -> f.getPosition() == 2)
                .sorted(Comparator.comparing(ToolboxGroupItemDto::getSortNum))
                .collect(Collectors.toList());

    }

    @GetMapping("/pc/getUserToolBox")
    public List<AsToolbox> getUserPcToolBox() {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        AsCusEmployeeSetting setting = userSettingService.getById(userId);
        List<AsToolbox> systemToolBox = toolboxService.getPcToolBox();

        //获取当前用户没有权限的菜单
        List<CusMenuDto> cusMenuDtoList = cusMenuService.userMenuPcList();
        List<String> menuCodeList = new ArrayList<>();
        if (CollUtil.isNotEmpty(cusMenuDtoList)) {
            menuCodeList = cusMenuDtoList.stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
        }

        if (setting == null || CollUtil.isEmpty(setting.getPcToolbox())) {
            //兼容是否常用，和以前的保持一致，现在是commonlyUsed字段，以前是position字段
            if (CollUtil.isNotEmpty(systemToolBox)) {
                for (AsToolbox item : systemToolBox) {
                    if (Objects.nonNull(item.getCommonlyUsed())) {
                        item.setPosition((short) (item.getCommonlyUsed() + 1));
                    }
                }

                //权限过滤
                if (CollUtil.isNotEmpty(menuCodeList)) {
                    List<String> finalMenuCodeList = menuCodeList;
                    systemToolBox = systemToolBox.stream().filter(item -> finalMenuCodeList.contains(item.getToolboxCode())).collect(Collectors.toList());
                }
            }
            return systemToolBox;
        }
        Map<Long, AsToolbox> sysToolBoxMap = systemToolBox.stream().collect(
                Collectors.toMap(AsToolbox::getId, box -> box));
        List<AsToolbox> toolboxes = setting.getPcToolbox();
        // 合并系统和当前的返回
        // 1、修改运营后台更改后的属性
        toolboxes.forEach(userToolBox -> {
            AsToolbox toolbox = sysToolBoxMap.get(userToolBox.getId());
            if (toolbox != null) {
                userToolBox.setToolboxName(toolbox.getToolboxName())
                        .setOrderType(toolbox.getOrderType())
                        .setShowName(toolbox.getShowName())
                        .setToolboxCode(toolbox.getToolboxCode())
                        .setToolboxIcon(toolbox.getToolboxIcon());
            }
        });
        // 2、获取appToolbox里面没有，系统有的工具
        List<Long> idList = toolboxes.stream().map(AsToolbox::getId).collect(Collectors.toList());
        List<AsToolbox> collect = systemToolBox.stream().filter(toolbox -> !idList.contains(toolbox.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            //兼容是否常用，和以前的保持一致，现在是commonlyUsed字段，以前是position字段
            for (AsToolbox item : collect) {
                if (Objects.nonNull(item.getCommonlyUsed())) {
                    item.setPosition((short) 1);
                }
            }
            toolboxes.addAll(collect);
            // 回写数据
            setting.setPcToolbox(toolboxes);
        }

        List<AsToolbox> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(cusMenuDtoList)) {
            List<String> finalMenuCodeList1 = menuCodeList;
            result = toolboxes.stream().filter(item -> finalMenuCodeList1.contains(item.getToolboxCode())).collect(Collectors.toList());
        }
        return result;
    }

    @GetMapping("/app/getWorkbench")
    public List<ToolboxGroupDto> getWorkbench() {
        List<ToolboxGroupDto> result = new ArrayList<>();
        List<ToolboxGroupItemDto> toolboxGroupItemDtoList = toolboxService.getWorkbench(3);
        if (CollUtil.isEmpty(toolboxGroupItemDtoList)) {
            return result;
        }

        //根据权限过滤，过滤当前用户没有权限的菜单
        AppCusMenuDto appCusMenuDto = cusMenuService.userMenuAppList();
        if (Objects.nonNull(appCusMenuDto) && CollUtil.isNotEmpty(appCusMenuDto.getMenus())) {
            List<String> menuCodeList = appCusMenuDto.getMenus().stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());
            toolboxGroupItemDtoList = toolboxGroupItemDtoList.stream().filter(item -> menuCodeList.contains(item.getToolboxCode())).collect(Collectors.toList());
        }

        //兼容以前app端是否常用的值
        for (ToolboxGroupItemDto item : toolboxGroupItemDtoList) {
            item.setPosition(item.getPosition() + 1);
        }

        Map<Long, List<ToolboxGroupItemDto>> toolboxGroupMap = toolboxGroupItemDtoList.stream().collect(Collectors.groupingBy(ToolboxGroupItemDto::getGroupId));
        for (Map.Entry<Long, List<ToolboxGroupItemDto>> item : toolboxGroupMap.entrySet()) {
            ToolboxGroupDto toolboxGroupDto = new ToolboxGroupDto();
            toolboxGroupDto.setGroupName(item.getValue().get(0).getGroupName());
            toolboxGroupDto.setSortNum(item.getValue().get(0).getGroupSortNum());
            toolboxGroupDto.setToolboxGroupItemDtoList(item.getValue().stream().sorted(Comparator.comparing(ToolboxGroupItemDto::getSortNum)).collect(Collectors.toList()));
            result.add(toolboxGroupDto);
        }
        return result.stream().sorted(Comparator.comparing(ToolboxGroupDto::getSortNum).reversed()).collect(Collectors.toList());
    }

    @PutMapping("/updatePcHome")
    public Boolean updatePcHome(@RequestBody AsCusEmployeeSetting setting) {
        AsCusEmployeeSetting byId = userSettingService.getById(LoginUserThreadLocal.getCurrentUserId());
        if (byId == null) {
            throw new BusinessException(SystemResultCode.CUS_USER_SETTING_NOT_EXISTS);
        }
        byId.setPcHome(setting.getPcHome());
        if (!userSettingService.updateById(byId)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @GetMapping("/getPcHome")
    public List<Map<String, Object>> getPcHome() {
        AsCusEmployeeSetting setting = userSettingService.getById(LoginUserThreadLocal.getCurrentUserId());
        if (setting != null && CollUtil.isNotEmpty(setting.getPcHome())) {
            return setting.getPcHome();
        }
        return Collections.emptyList();
    }

    @PutMapping("/simplify")
    public Boolean updateSimplify(@RequestBody AsCusEmployeeSetting setting) {
        setting.setUserId(LoginUserThreadLocal.getCurrentUserId());
        userSettingService.updateById(setting);
        return true;
    }

    @GetMapping
    public AsCusEmployeeSetting listConfig() {
        return userSettingService.getById(LoginUserThreadLocal.getCurrentUserId());
    }

    /**
     * 菜单统计
     * @param statistics
     * @return
     */
    @PostMapping("/toolbox/statistics")
    public Boolean toolboxStatistics(@RequestBody AsToolboxStatistics statistics) {
        return toolboxStatisticsService.toolboxStatistics(statistics);
    }

    /**
     * 最近使用菜单
     * @return
     */
    @GetMapping("/toolbox/recentlyUsed")
    public List<AsToolbox> recentlyUsed() {
        //获取当前用户权限
        AppCusMenuDto cusMenuDto = cusMenuService.userMenuAppList();
        if (Objects.isNull(cusMenuDto) || CollUtil.isEmpty(cusMenuDto.getMenus())) {
            return Collections.emptyList();
        }
        //有权限的菜单code
        List<String> menuCodeList = cusMenuDto.getMenus().stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());

        //获取工作台菜单，这里是比较全的
        List<AsToolbox> systemToolBox = toolboxService.getAllToolbox();
       /* List<AsToolbox> appToolboxList = toolboxService.getAppToolBox();
        if (CollUtil.isNotEmpty(appToolboxList)) {
            systemToolBox.addAll(appToolboxList);
        }*/
        Map<Long, AsToolbox> sysToolBoxMap = systemToolBox.stream().collect(
                Collectors.toMap(AsToolbox::getOrderType, box -> box, (v1, v2) -> v2));

        List<AsToolbox> toolboxResult = new ArrayList<>();

        //获取当前用户最近使用菜单
        List<AsToolboxStatistics> toolboxStatisticsList = toolboxStatisticsService.list(Wrappers.lambdaQuery(AsToolboxStatistics.class)
                .eq(AsToolboxStatistics::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsToolboxStatistics::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .orderByDesc(AsToolboxStatistics::getUpdateTime));

        //当前用户最近使用菜单为空的时候，取常用菜单，常用菜单为空的时候，取兜底配置菜单数据
        if (CollUtil.isEmpty(toolboxStatisticsList)) {
            //获取用户菜单配置
            AsCusEmployeeSetting setting = userSettingService.getById(LoginUserThreadLocal.getCurrentUserId());
            if (Objects.isNull(setting) || CollUtil.isEmpty(setting.getAppToolbox())) {
                //系统默认兜底配置
                List<AsToolboxStatistics> defaultToolboxList = toolboxStatisticsService.list(Wrappers.lambdaQuery(AsToolboxStatistics.class)
                        .eq(AsToolboxStatistics::getCompanyId, 0)
                        .eq(AsToolboxStatistics::getUserId, 0));
                toolboxResult = buildToolboxList(defaultToolboxList, sysToolBoxMap);
            } else {
                //常用菜单
                for (AsToolbox appToolbox : setting.getAppToolbox()) {
                    AsToolbox sysToolbox = sysToolBoxMap.get(appToolbox.getOrderType());
                    if (Objects.isNull(sysToolbox)) {
                        continue;
                    }

                    appToolbox.setToolboxName(sysToolbox.getToolboxName())
                            .setOrderType(sysToolbox.getOrderType())
                            .setShowName(sysToolbox.getShowName())
                            .setToolboxCode(sysToolbox.getToolboxCode())
                            .setToolboxIcon(sysToolbox.getToolboxIcon());
                    toolboxResult.add(appToolbox);
                }
            }
        } else {
            //最近使用菜单
            toolboxResult = buildToolboxList(toolboxStatisticsList, sysToolBoxMap);
        }
        if (CollUtil.isEmpty(toolboxResult)) {
            return toolboxResult;
        }

        //菜单权限过滤
        toolboxResult = toolboxResult.stream().filter(item -> menuCodeList.contains(item.getToolboxCode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(toolboxResult)) {
            return toolboxResult;
        }

        //只返回前10个菜单
        return Lists.partition(toolboxResult, 10).get(0);
    }

    private List<AsToolbox> buildToolboxList(List<AsToolboxStatistics> defaultToolboxList, Map<Long, AsToolbox> sysToolBoxMap) {
        List<AsToolbox> result = new ArrayList<>();
        if (CollUtil.isEmpty(defaultToolboxList)) {
            return result;
        }
        for (AsToolboxStatistics defaultToolbox : defaultToolboxList) {
            AsToolbox sysToolbox = sysToolBoxMap.get(defaultToolbox.getOrderType());
            if (Objects.isNull(sysToolbox)) {
                continue;
            }
            AsToolbox toolbox = new AsToolbox()
                    .setToolboxName(sysToolbox.getToolboxName())
                    .setShowName(sysToolbox.getShowName())
                    .setOrderType(sysToolbox.getOrderType())
                    .setToolboxCode(sysToolbox.getToolboxCode())
                    .setToolboxIcon(sysToolbox.getToolboxIcon());
            result.add(toolbox);
        }
        return result;
    }

    @GetMapping("/advertise")
    public List<AssetAdvertiseDto> advertise(@RequestParam("type") Integer type) {
        List<AsAssetAdvertise> advertiseList = assetAdvertiseService.list(Wrappers.lambdaQuery(AsAssetAdvertise.class)
                .eq(AsAssetAdvertise::getType, type).orderByAsc(AsAssetAdvertise::getSortNum));
        if (CollUtil.isEmpty(advertiseList)) {
            return Collections.emptyList();
        }

        return advertiseList.stream()
                .filter(item -> StrUtil.isNotBlank(item.getUrl()))
                .map(advertise -> JSONObject.parseArray(advertise.getUrl(), AssetAdvertiseDto.class))
                .flatMap(Collection::stream).collect(Collectors.toList());
    }
}
