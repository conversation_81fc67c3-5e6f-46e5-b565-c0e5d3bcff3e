package com.niimbot.asset.system.service.handler;

import com.niimbot.asset.framework.constant.CompanyPaymentStatusEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.abs.ResourceOrderAbs;
import com.niimbot.asset.system.model.CompanyResourceView;
import com.niimbot.asset.system.service.AsConfigService;
import com.niimbot.asset.system.service.CompanyResourceViewService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.sale.ResourceSaleOrderDto;
import com.niimbot.system.CompanyPaymentStatusDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/12/8 上午11:38
 */
@Slf4j
@Component
public class UsingPaymentStatusHandler implements PaymentStatusHandler {

    private final static Integer HANDLER_PRIORITY = 2;

    @Autowired
    private ResourceOrderAbs resourceOrderAbs;
    @Autowired
    private AsConfigService configService;
    @Autowired
    private CompanyResourceViewService companyResourceViewService;

    @Override
    public Integer handlePaymentStatus(Long companyId) {
        Integer result = null;
        //查询企业购买的资源包，有可能企业只有赠送的体验版资源包，但是没有自己购买资源包
        List<ResourceSaleOrderDto> resourceSaleOrderDtoList = resourceOrderAbs.queryResourceOrder(companyId);

        //企业没有购买资源包，就是新入驻或待转化，交由后续PaymentStatusHandler进行处理
        if (CollUtil.isEmpty(resourceSaleOrderDtoList)) {
            return result;
        }

        //获取企业付费状态配置
        CompanyPaymentStatusDto paymentStatusConfig = configService.queryCompanyPaymentStatus();
        if (Objects.isNull(paymentStatusConfig.getBuyNumMin())
                || Objects.isNull(paymentStatusConfig.getRemainingDayMin())
                || Objects.isNull(paymentStatusConfig.getRemainingDayMax()) || Objects.isNull(paymentStatusConfig.getRemainingCapacityRateMax())) {
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "企业付费状态配置错误");
        }

        //下面是已购买，购买一次或多次
        CompanyResourceView companyResource = companyResourceViewService.selectCompanyResource(companyId);
        if (Objects.isNull(companyResource) || Objects.isNull(companyResource.getExpirationTime())) {
            log.error("usingPaymentStatusHandler handlePaymentStatus error! company resource not exist! companyId=[{}]", companyId);
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "企业资源包信息不存在");
        }

        //待续费状态判断:剩余有效期在配置范围段内
        long remainingDays = Duration.between(companyResource.getExpirationTime(), LocalDateTime.now()).abs().toDays();
        if (remainingDays >= paymentStatusConfig.getRemainingDayMin() && remainingDays <= paymentStatusConfig.getRemainingDayMax()) {
            return CompanyPaymentStatusEnum.WAIT_RENEW.getStatusCode();
        }

        //待扩容判断
        if (companyResource.getRemainder() > 0 && companyResource.getCapacity() != 0) {
            long remainingRate = companyResource.getRemainder() * 100 / companyResource.getCapacity();
            if (remainingRate <= paymentStatusConfig.getRemainingCapacityRateMax()) {
                return CompanyPaymentStatusEnum.WAIT_EXPEND.getStatusCode();
            }
        }

        //已复购
        if (resourceSaleOrderDtoList.size() >= paymentStatusConfig.getBuyNumMin()) {
            return CompanyPaymentStatusEnum.REPURCHASED.getStatusCode();
        }

        //对于已购买资源包的都定义为已付费
        return CompanyPaymentStatusEnum.PAID.getStatusCode();
    }

    @Override
    public Integer priority() {
        return HANDLER_PRIORITY;
    }
}
