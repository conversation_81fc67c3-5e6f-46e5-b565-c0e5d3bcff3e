package com.niimbot.asset.system.constant;

public interface CompanyPasswordConstant {

    String COMPANY_PASSWORD_SETTING = "company_password_setting_key";
    String COMPANY_PASSWORD_DEFAULT_SETTING = "company_password_default_setting_key";
    String COMPANY_WEAK_PASSWORD = "company_weak_password_key";
    String COMPANY_UPDATE_PWD_ACCOUNT = "company_update_pwd_account";

    static String companyPasswordSettingKey(Long companyId) {
        return COMPANY_PASSWORD_SETTING + ":" + companyId;
    }

    static String companyUpdatePwdAccountKey(Long companyId) {
        return COMPANY_UPDATE_PWD_ACCOUNT + ":" + companyId;
    }

}
