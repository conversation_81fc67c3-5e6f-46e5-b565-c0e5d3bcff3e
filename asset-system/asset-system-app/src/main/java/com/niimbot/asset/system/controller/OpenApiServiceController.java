package com.niimbot.asset.system.controller;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.niimbot.asset.system.dto.openapi.OauthUserInfoRsp;
import com.niimbot.asset.system.dto.openapi.OpenApiRes;
import com.niimbot.asset.system.model.AsOpenApi;
import com.niimbot.asset.system.service.AsOpenApiService;
import com.niimbot.system.OpenApiAuthCodeDto;

import com.niimbot.system.OpenApiGetUserInfoDto;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/1 17:24
 */
@RestController
@RequestMapping("server/system/openApi")
@RequiredArgsConstructor
public class OpenApiServiceController {

    private final AsOpenApiService openApiService;

    @PostMapping
    public Boolean add(@RequestBody AsOpenApi openApi) {
        return openApiService.add(openApi);
    }

    @PutMapping
    public Boolean edit(@RequestBody AsOpenApi openApi) {
        return openApiService.edit(openApi);
    }

    @DeleteMapping("/{id}")
    public Boolean remove(@PathVariable("id") Long id) {
        return openApiService.removeById(id);
    }

    @PostMapping("/getAuthCode")
    public String getAuthCode(@RequestBody OpenApiAuthCodeDto authCodeDto) {
        return openApiService.getAuthCode(authCodeDto,  authCodeDto.getLoginUser());
    }

    @GetMapping("/getUserInfoByCode")
    public OauthUserInfoRsp getUserInfoByCode(@RequestParam("companyId") Long companyId,
                                              @RequestParam("appKey") String appKey,
                                              @RequestParam("code") String code) {
        return openApiService.getUserInfoByCode(companyId, appKey, code);
    }

    @PostMapping("/getUserInfoByReverseCode")
    public OauthUserInfoRsp getUserInfoByReverseCode(@RequestBody OpenApiGetUserInfoDto getUserInfoDto) {
        return openApiService.getUserInfoByCode(getUserInfoDto.getCompanyId(), getUserInfoDto.getAppKey(), getUserInfoDto.getCode());
    }

    @GetMapping("/getOpenApiByAgentId")
    public OpenApiRes getOpenApiByAgentId(@RequestParam("agentId") Long agentId) {
        return OpenApiRes.build(openApiService.getByAgentId(agentId));
    }



}
