package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.FormAbs;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 17:56
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.dynamicform.abs.impl.FormAbsImpl")
@FeignClient(name = "asset-dynamicform", url = "https://{gateway}/client/abs/dynamicform/formAbs/")
public interface FormAbsRemoteClient extends FormAbs {
}
