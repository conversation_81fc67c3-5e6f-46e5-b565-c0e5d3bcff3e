package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCountryCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsCountryCodeMapper extends BaseMapper<AsCountryCode> {

    boolean updateStatusByIds(@Param("chooseStatus") Integer chooseStatus, @Param("countryIds") List<Long> countryIds);
}
