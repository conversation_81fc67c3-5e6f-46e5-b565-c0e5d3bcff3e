package com.niimbot.asset.system.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsCusAccountMapper;
import com.niimbot.asset.system.mapper.AsCusRoleMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.model.ChangeCusUserPassword;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.ChangeCusUserPasswordService;
import com.niimbot.asset.system.service.CusAccountService;
import com.niimbot.asset.system.service.CusUserRoleService;
import com.niimbot.asset.system.service.handler.WeixinAdapter;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.system.CusAccountBaseDto;
import com.niimbot.system.CusAccountDto;
import com.niimbot.system.CusAccountEnableBatchDto;
import com.niimbot.system.CusAccountEnableDto;
import com.niimbot.system.CusAccountPageDto;
import com.niimbot.system.CusAccountPageQueryDto;
import com.niimbot.system.CusAccountRoleBatchDto;
import com.niimbot.system.CusRoleDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2020/11/18
 */
@Service
@Slf4j
public class CusAccountServiceImpl implements CusAccountService {

    @Resource
    private AsCusAccountMapper mapper;

    @Resource
    private AsCusEmployeeService employeeService;

    @Resource
    private CusUserRoleService cusUserRoleService;

    @Resource
    private ChangeCusUserPasswordService changeCusUserPasswordService;

    @Resource
    private RedisService redisService;

    @Resource
    private AsCusRoleMapper cusRoleMapper;

    @Resource
    private DataScopeStrategyManager dataScopeStrategyManager;

    @Override
    public List<CusAccountDto> selectAccountList(Long companyId) {
        return mapper.selectAccountList(companyId);
    }

    @Override
    public List<Long> selectAccountEmpIds(Long companyId) {
        return mapper.selectAccountEmpIds(companyId);
    }

    @Override
    public List<CusAccountBaseDto> selectListKw(String kw) {
        // 筛选已激活账号的员工ID
        List<AsAccountEmployee> list = Db.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .select(AsAccountEmployee::getEmployeeId)
                        .eq(AsAccountEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
        );
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Set<Long> empIds = list.stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toSet());
        List<AsCusEmployee> employees = employeeService.list(
                Wrappers.<AsCusEmployee>lambdaQuery()
                        .in(AsCusEmployee::getId, empIds)
                        .and(queryWrapper -> queryWrapper.like(AsCusEmployee::getEmpNo, kw)
                                .or()
                                .like(AsCusEmployee::getEmpName, kw)
                                .or()
                                .like(AsCusEmployee::getMobile, kw)
                                .or()
                                .like(AsCusEmployee::getEmail, kw))
        );
        Edition.weixin(() -> {
            employees.clear();
            List<String> userId = SpringUtil.getBean(WeixinAdapter.class).empSearch(kw);
            if (CollUtil.isNotEmpty(userId)) {
                List<Long> likeIds = SpringUtil.getBean(AsThirdPartyEmployeeService.class).list(
                        Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                                .eq(AsThirdPartyEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                                .in(AsThirdPartyEmployee::getUserId, userId)
                ).stream().map(AsThirdPartyEmployee::getEmployeeId).filter(empIds::contains).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(userId)) {
                    employees.addAll(employeeService.listByIds(likeIds));
                }
            }
        });
        if (CollUtil.isEmpty(employees)) {
            return Collections.emptyList();
        }
        List<Long> ids = employees.stream().map(AsCusEmployee::getId).collect(Collectors.toList());
        Map<Long, List<AsUserOrg>> userOrgMap = Db.list(
                        Wrappers.<AsUserOrg>lambdaQuery().in(AsUserOrg::getUserId, ids))
                .stream()
                .collect(Collectors.groupingBy(AsUserOrg::getUserId));
        return employees.stream()
                .map(employee -> {
                    List<Long> userOrgs = userOrgMap.getOrDefault(employee.getId(), new ArrayList<>())
                            .stream().map(AsUserOrg::getOrgId).collect(Collectors.toList());
                    return new CusAccountBaseDto()
                            .setAccountId(employee.getId())
                            .setEmpName(employee.getEmpName())
                            .setEmpNo(employee.getEmpNo())
                            .setMobile(employee.getMobile())
                            .setEmail(employee.getEmail())
                            .setOrgIds(userOrgs);
                })
                .collect(Collectors.toList());
    }

    @Override
    public IPage<CusAccountPageDto> selectPage(Page<CusAccountDto> page, CusAccountPageQueryDto queryDto) {
        // 添加权限
        String deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        IPage<CusAccountPageDto> cusAccountPageDtoIPage;
        if (StrUtil.isNotEmpty(queryDto.getKw()) && Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).empSearch(queryDto.getKw());
            cusAccountPageDtoIPage = mapper.selectAccountPage(page, queryDto, deptSql, LoginUserThreadLocal.getCompanyId(), unionIds);
        } else {
            cusAccountPageDtoIPage = mapper.selectAccountPage(page, queryDto, deptSql, LoginUserThreadLocal.getCompanyId(), null);
        }

        AsCusEmployee administrator = employeeService.getAdministrator();

        if (CollUtil.isNotEmpty(cusAccountPageDtoIPage.getRecords())) {
            List<Long> empIds = cusAccountPageDtoIPage.getRecords().stream().map(CusAccountPageDto::getEmpId)
                    .collect(Collectors.toList());
            // 处理组织
            List<AsUserOrg> userOrgList = Db.list(Wrappers.lambdaQuery(AsUserOrg.class).in(AsUserOrg::getUserId, empIds));
            Map<Long, List<AsUserOrg>> userOrgMap = new HashMap<>();
            List<Long> userOrgIds = new ArrayList<>();
            for (AsUserOrg asUserOrg : userOrgList) {
                userOrgMap.computeIfAbsent(asUserOrg.getUserId(), k -> new ArrayList<>()).add(asUserOrg);
                userOrgIds.add(asUserOrg.getOrgId());
            }
            Map<Long, String> orgNameCache = new HashMap<>();
            if (CollUtil.isNotEmpty(userOrgIds)) {
                orgNameCache = Db.list(Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getOrgName).in(AsOrg::getId, userOrgIds))
                        .stream().collect(Collectors.toMap(AsOrg::getId, AsOrg::getOrgName));
            }

            // 处理角色
            Map<Long, List<AsUserRole>> userRoleMap = cusUserRoleService.list(Wrappers.<AsUserRole>lambdaQuery().in(AsUserRole::getUserId, empIds))
                    .stream().collect(Collectors.groupingBy(AsUserRole::getUserId));

            Map<Long, String> finalOrgNameCache = orgNameCache;
            cusAccountPageDtoIPage.getRecords().forEach(ac -> {
                ac.setIsAdmin(ac.getEmpId().equals(administrator.getId()));
                List<AsUserOrg> userOrgs = userOrgMap.getOrDefault(ac.getEmpId(), ListUtil.empty());
                ac.setOrgIds(new ArrayList<>());
                ac.setOrgNames(new ArrayList<>());
                userOrgs.forEach(uo -> {
                    ac.getOrgIds().add(uo.getOrgId());
                    ac.getOrgNames().add(finalOrgNameCache.get(uo.getOrgId()));
                });
                List<AsUserRole> userRoles = userRoleMap.getOrDefault(ac.getEmpId(), ListUtil.empty());
                ac.setRoleList(userRoles.stream().map(f -> new CusRoleDto().setId(f.getRoleId())).collect(Collectors.toList()));
            });
        }
        return cusAccountPageDtoIPage;
    }

    @Override
    public Integer getAccountAmount(Long roleId) {
        // 添加权限
        String deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        return mapper.getAccountAmount(LoginUserThreadLocal.getCompanyId(), roleId, deptSql);
    }

    /**
     * 修改密码和角色
     *
     * @param account 账号信息
     * @return 修改成功否
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(CusAccountDto account) {
        // 修改密码
        String password = account.getDecryptPassword();
        if (StrUtil.isNotBlank(password)) {
            changeCusUserPasswordService.changeCusUserPassword(
                    new ChangeCusUserPassword()
                            .setId(account.getAccountId())
                            .setPassword(password));
        }
        return true;
    }

    @Override
    public Boolean editRole(CusAccountDto account) {
        Long userId = account.getEmpId();
        List<CusRoleDto> roleList = account.getRoleList();
        if (CollUtil.isNotEmpty(roleList)) {
            // 数据库角色list
            List<Long> list = cusUserRoleService
                    .list(Wrappers.<AsUserRole>lambdaQuery().eq(AsUserRole::getUserId, userId)).stream()
                    .map(AsUserRole::getRoleId).collect(Collectors.toList());
            saveUserRole(roleList, userId);
            if (list.size() != roleList.size()) {
                return true;
            }
            List<Long> toSaveList = roleList.stream().map(CusRoleDto::getId).collect(Collectors.toList());
            return !new HashSet<>(toSaveList).containsAll(list) || !new HashSet<>(list).containsAll(toSaveList);
        }
        return false;
    }

    private void saveUserRole(List<CusRoleDto> roleList, Long userId) {
        if (CollUtil.isNotEmpty(roleList)) {
            cusUserRoleService.remove(new QueryWrapper<AsUserRole>().lambda()
                    .eq(AsUserRole::getUserId, userId));
            List<AsUserRole> collect = roleList.stream().map(cusRoleDto -> new AsUserRole()
                    .setUserId(userId).setRoleId(cusRoleDto.getId())).collect(Collectors.toList());
            cusUserRoleService.saveBatch(collect);
        }
    }

    @Override
    public Boolean enableOrDisable(CusAccountEnableDto account, String domain) {
        /*AsCusUser byId = Db.getById(account.getAccountId(), AsCusUser.class);
        AsCusUser cusUser = new AsCusUser()
                .setId(account.getAccountId())
                .setStatus(account.getAccountStatus().shortValue());

        if (account.getAccountStatus() == AssetConstant.ACCOUNT_ENABLE) {
            if (StrUtil.isBlank(byId.getMobile())) {
                throw new BusinessException(SystemResultCode.USER_MOBILE_IS_NULL);
            }
        }*/
//        return Db.updateById(cusUser);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableOrDisableBatch(CusAccountEnableBatchDto account, String domain) {
        /*for (Long userId : account.getAccountIds()) {
            // 数据库角色list
            List<Long> list = cusUserRoleService
                    .list(Wrappers.<AsUserRole>lambdaQuery().eq(AsUserRole::getUserId, userId)).stream()
                    .map(AsUserRole::getRoleId).collect(Collectors.toList());
            if (cusRoleMapper.containsAdmin(list)) {
                BusinessExceptionUtil.throwException("无法启用/禁用超级管理员");
            }

            CusAccountEnableDto enableDto = new CusAccountEnableDto()
                    .setAccountId(userId)
                    .setAccountStatus(account.getAccountStatus());
            this.enableOrDisable(enableDto, domain);
        }*/
        return true;
    }

    @Override
    public Boolean kickOffLoginUser(List<AsCusUser> userList) {
        Stream<AsCusUser> stream;
        if (userList.size() > 500) {
            stream = userList.parallelStream();
        } else {
            stream = userList.stream();
        }
        // 查询出所有的用户redis key
        List<String> userKey = Collections.synchronizedList(new ArrayList<>());
        stream.forEach(k -> {
            Set<Object> set = redisService.sMembers(BaseConstant.LOGIN_TOKEN_KEY_INDEX + k.getCompanyId() + ":" + k.getId());
            userKey.addAll(set.stream().map(Convert::toStr).collect(Collectors.toList()));
        });

        // 切分
        List<List<String>> partition = Lists.partition(userKey, 200);
        for (List<String> userKeyPartition : partition) {
            redisService.del(userKeyPartition);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addRoleBatch(CusAccountRoleBatchDto account) {
        Long companyId = LoginUserThreadLocal.getCompanyId();

        // 查询超管
        AsCusRole adminRole = cusRoleMapper.getAdministratorByCompanyId(companyId);
        if (account.getRoleList().contains(adminRole.getId())) {
            BusinessExceptionUtil.throwException("无法对超级管理员权限进行此操作");
        }
        // 数据库角色list
        Map<Long, Set<Long>> userRoleMap = cusUserRoleService
                .list(Wrappers.lambdaQuery(AsUserRole.class).in(AsUserRole::getUserId, account.getEmpIds())).stream()
                .collect(Collectors.groupingBy(AsUserRole::getUserId, Collectors.mapping(AsUserRole::getRoleId, Collectors.toSet())));

        List<AsUserRole> saveList = new ArrayList<>();
        List<AsCusUser> kickOffUsers = new ArrayList<>();
        userRoleMap.forEach((userId, roleIds) -> {
            if (roleIds.contains(adminRole.getId())) {
                BusinessExceptionUtil.throwException("无法对超级管理员做权限变更");
            }
            // 有新增的补进去
            for (Long addRoleId : account.getRoleList()) {
                if (!roleIds.contains(addRoleId)) {
                    saveList.add(new AsUserRole().setUserId(userId).setRoleId(addRoleId));
                    kickOffUsers.add(new AsCusUser().setCompanyId(companyId).setId(userId));
                }
            }
        });
        cusUserRoleService.saveBatch(saveList);
        // 更新缓存
        kickOffLoginUser(kickOffUsers);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeRoleBatch(CusAccountRoleBatchDto account) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 查询超管
        AsCusRole adminRole = cusRoleMapper.getAdministratorByCompanyId(companyId);
        if (account.getRoleList().contains(adminRole.getId())) {
            BusinessExceptionUtil.throwException("无法对超级管理员权限进行此操作");
        }
        // 数据库角色list
        Map<Long, Set<Long>> userRoleMap = cusUserRoleService
                .list(Wrappers.lambdaQuery(AsUserRole.class).in(AsUserRole::getUserId, account.getEmpIds())).stream()
                .collect(Collectors.groupingBy(AsUserRole::getUserId, Collectors.mapping(AsUserRole::getRoleId, Collectors.toSet())));

        List<AsUserRole> removeList = new ArrayList<>();
        userRoleMap.forEach((userId, roleIds) -> {
            long remainderSize = roleIds.stream()
                    .filter(f -> !account.getRoleList().contains(f))
                    .count();
            if (remainderSize == 0) {
                AsCusEmployee employee = employeeService.getById(userId);
                BusinessExceptionUtil.throwException("每个账号至少要有一个权限组[" + employee.getEmpName() + "]");
            }
            roleIds.forEach(f -> {
                for (Long removeRoleId : account.getRoleList()) {
                    removeList.add(new AsUserRole().setRoleId(removeRoleId).setUserId(userId));
                }
            });
        });
        cusUserRoleService.removeBatch(removeList);
        return true;
    }

}
