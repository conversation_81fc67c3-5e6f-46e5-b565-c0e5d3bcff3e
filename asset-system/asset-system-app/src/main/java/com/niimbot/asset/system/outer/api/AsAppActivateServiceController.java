package com.niimbot.asset.system.outer.api;

import com.niimbot.asset.system.service.AsAppActivateService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Since 2024-09-05
 */
@RestController
@RequestMapping("server/system/appActivate")
public class AsAppActivateServiceController {

    private final AsAppActivateService appActivateService;

    public AsAppActivateServiceController(AsAppActivateService appActivateService) {
        this.appActivateService = appActivateService;
    }

    @GetMapping("configStatus")
    public List<String> configStatus() {
        return appActivateService.configStatus();

    }

}
