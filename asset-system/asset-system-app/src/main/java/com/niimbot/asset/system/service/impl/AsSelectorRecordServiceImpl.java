package com.niimbot.asset.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.abs.AssetAreaAbs;
import com.niimbot.asset.system.abs.AssetCategoryAbs;
import com.niimbot.asset.system.abs.EquipmentSiteInspectPointAbs;
import com.niimbot.asset.system.abs.RepositoryAbs;
import com.niimbot.asset.system.enums.SelectorTypeEnum;
import com.niimbot.asset.system.mapper.AsSelectorRecordMapper;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsSelectorRecord;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.service.*;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.system.AddSelectorRecord;
import com.niimbot.system.GetSelectorRecord;
import com.niimbot.system.RecordCounter;
import com.niimbot.system.SelectorRecord;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AsSelectorRecordServiceImpl extends ServiceImpl<AsSelectorRecordMapper, AsSelectorRecord> implements AsSelectorRecordService, ApplicationListener<ApplicationStartedEvent> {

    @Resource(name = "assetRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private AsCusEmployeeService employeeService;
    @Resource
    private OrgService orgService;
    @Resource
    private AssetAreaAbs assetAreaAbs;
    @Resource
    private AssetCategoryAbs assetCategoryAbs;
    @Resource
    private RepositoryAbs repositoryAbs;
    @Resource
    private AsAccountEmployeeService accountEmployeeService;
    @Resource
    private RedisDistributeLock redisDistributeLock;

    @Resource
    private EquipmentSiteInspectPointAbs equipmentSiteInspectPointAbs;

    private final Integer THRESHOLD = 1000;

    private final String CACHE_KEY_TPL = "selector_record:%d:%d:%s";

    private final Map<SelectorTypeEnum, Consumer<List<Long>>> permFilterMap = new ConcurrentHashMap<>(16);

    private final Map<SelectorTypeEnum, Consumer<List<SelectorRecord>>> postProcessMap = new ConcurrentHashMap<>(2);

    @Override
    public Boolean addSelectorRecord(AddSelectorRecord params) {
        SelectorTypeEnum.valueOf(params.getType().toUpperCase(Locale.ROOT));
        String cacheKey = String.format(CACHE_KEY_TPL, params.getCompanyId(), params.getEmpId(), params.getType());
        // 不超过阈值
        if (params.getIds().size() <= THRESHOLD) {
            increment(cacheKey, params.getIds());
            return true;
        }
        // 超过阈值时只记录已存在的
        BoundZSetOperations<String, Object> zSetOps = redisTemplate.boundZSetOps(cacheKey);
        Set<ZSetOperations.TypedTuple<Object>> range = zSetOps.rangeWithScores(0, -1);
        if (CollUtil.isEmpty(range)) {
            increment(cacheKey, Lists.partition(params.getIds(), THRESHOLD).get(0));
            return true;
        }
        // 剔除次数最少的
        Map<Long, Integer> rangeMap = new HashMap<>(range.size());
        range.parallelStream().forEach(v -> rangeMap.put(Convert.toLong(v.getValue()), Convert.toInt(v.getScore())));
        List<RecordCounter> data = params.getIds()
                .stream()
                .map(id -> new RecordCounter(id, rangeMap.getOrDefault(id, 0)))
                .sorted((o1, o2) -> o2.getVal() - o1.getVal())
                .collect(Collectors.toList());
        params.setIds(null);
        List<Long> ids = data.stream().limit(THRESHOLD).map(RecordCounter::getId).collect(Collectors.toList());
        increment(cacheKey, ids);
        return true;
    }

    @SuppressWarnings("all")
    private void increment(String key, List<Long> ids) {
        redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                BoundZSetOperations<String, Object> zSetOps = (BoundZSetOperations<String, Object>) operations.boundZSetOps((K) key);
                ids.forEach(v -> zSetOps.incrementScore(v, 1d));
                return null;
            }
        });
    }

    @Override
    public List<SelectorRecord> getSelectorRecords(GetSelectorRecord params) {
        SelectorTypeEnum anEnum = SelectorTypeEnum.valueOf(params.getType().toUpperCase(Locale.ROOT));
        SelectorTypeEnum.Table table = anEnum.getTable();
        String key = String.format(CACHE_KEY_TPL, params.getCompanyId(), params.getEmpId(), params.getType());
        // 查所有-最多1000条
        Set<Object> range = redisTemplate.boundZSetOps(key).reverseRange(0, -1);
        if (CollUtil.isEmpty(range)) {
            return Collections.emptyList();
        }
        List<Long> ids = range.stream().filter(Objects::nonNull).map(Convert::toLong).collect(Collectors.toList());
        // 非超管权限过滤
        if (params.getIsPerm() && !Objects.equals(employeeService.getAdministratorByCompanyId(params.getCompanyId()).getId(), params.getEmpId())) {
            permFilterMap.get(anEnum).accept(ids);
        }
        // 无账号员工过滤
        if (params.getIsAccount()) {
            ids = accountEmployeeService.filterHasAccountEmp(ids);
        }

        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        /*
         *  理论上删除的数据ID在权限列表里应该已同步更新过了，所以在上面的权限过滤中已删除的数据已经被过滤了
         *  防止已删除(已经查数据库了顺带获取code与name)
         *  先取部分数据去查询如果结果大于等于10条就直接返回了
         */
        List<SelectorRecord> records = this.getBaseMapper().selectIdsFromTable(table.getColumns(), table.getName(), Lists.partition(ids, 30).get(0));
        long num = 10L;
        if (Objects.isNull(records) || records.size() < num) {
            records = this.getBaseMapper().selectIdsFromTable(table.getColumns(), table.getName(), ids);
        }
        // 名称翻译
        List<SelectorRecord> result = records.stream().limit(num).collect(Collectors.toList());
        // 后置结果补充
        if (postProcessMap.containsKey(anEnum)) {
            postProcessMap.get(anEnum).accept(result);
        }
        return result;
    }

    @Override
    @Scheduled(cron = "0 30 2 * * ?")
    // @Scheduled(cron = "0/1 * * * * ?")
    public void clearSelectorRecords() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }

        redisDistributeLock.lock("cleanSelectorRecord", 3, TimeUnit.MINUTES, (a) -> {
            // 获取RedisKey
            Set<String> keys = SpringUtil.getBean(RedisService.class).scan(CACHE_KEY_TPL.split(":")[0] + "*");
            if (CollUtil.isEmpty(keys)) {
                return;
            }
            // 已有记录为空时直接保存入库
            List<AsSelectorRecord> recordsInDb = this.list(
                    Wrappers.lambdaQuery(AsSelectorRecord.class)
                            .last("LIMIT 1")
            );
            if (CollUtil.isEmpty(recordsInDb)) {
                List<AsSelectorRecord> recordsInCache = getRecordsInCache(keys);
                this.saveBatch(recordsInCache);
                return;
            }
            // 按人分组遍历 selector_record:1480810229972209664:1480810230072872960: -> [org,org_emp]
            Map<String, List<String>> group = keys.stream()
                    .map(s -> s.split(":"))
                    .collect(Collectors.groupingBy(v -> v[0] + ":" + v[1] + ":" + v[2] + ":", Collectors.mapping(v -> v[3], Collectors.toList())));
            List<AsSelectorRecord> update = new ArrayList<>(THRESHOLD * 3);
            group.forEach((k, v) -> {
                String[] str = k.split(":");
                // 获取单个人的记录
                Map<String, AsSelectorRecord> dbMap = this.list(
                        Wrappers.lambdaQuery(AsSelectorRecord.class)
                                .eq(AsSelectorRecord::getCompanyId, Convert.toLong(str[1]))
                                .eq(AsSelectorRecord::getEmpId, Convert.toLong(str[2]))
                                .in(AsSelectorRecord::getType, v)
                ).stream().collect(Collectors.toMap(AsSelectorRecord::getType, r -> r));
                // 获取单个人的记录
                Map<String, AsSelectorRecord> cacheMap = getRecordsInCache(v.stream().map(s -> k + s).collect(Collectors.toSet()))
                        .stream().collect(Collectors.toMap(AsSelectorRecord::getType, r -> r));
                if (CollUtil.isEmpty(dbMap)) {
                    update.addAll(cacheMap.values());
                    return;
                }
                dbMap.forEach((t, r) -> {
                    // 异常情况不处理
                    if (!cacheMap.containsKey(t)) {
                        return;
                    }
                    // 未达到7天不处理
                    if (r.getUpdateTime().plusDays(7).isAfter(LocalDateTime.now())) {
                        update.add(r.setUpdateTime(LocalDateTime.now()).setData(cacheMap.get(t).getData()));
                        return;
                    }
                    // 缩减缓存数量
                    AsSelectorRecord cache = cacheMap.get(t);
                    List<RecordCounter> cacheCounters = cache.getData();
                    if (cacheCounters.size() < 500) {
                        update.add(r.setUpdateTime(LocalDateTime.now()).setData(cacheCounters));
                        return;
                    }
                    redisTemplate.boundZSetOps(k + t).removeRange(501, -1);
                    update.add(r.setUpdateTime(LocalDateTime.now()).setData(Lists.partition(cacheCounters, 500).get(0)));
                });
                if (CollUtil.isNotEmpty(update) && update.size() >= 3000) {
                    this.updateBatchById(update);
                    update.clear();
                }
            });
            if (CollUtil.isNotEmpty(update)) {
                this.updateBatchById(update);
            }
        });
    }

    private void filterDeleted(String type, List<RecordCounter> counters) {
        SelectorTypeEnum anEnum = SelectorTypeEnum.valueOf(type.toUpperCase(Locale.ROOT));
        SelectorTypeEnum.Table table = anEnum.getTable();
        List<SelectorRecord> fromTable = this.getBaseMapper().selectIdsFromTable(table.getColumns(), table.getName(), counters.stream().map(RecordCounter::getId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(fromTable)) {
            counters.clear();
        }
        List<Long> ids = fromTable.stream().map(SelectorRecord::getId).collect(Collectors.toList());
        counters.removeIf(v -> !ids.contains(v.getId()));
    }

    private List<AsSelectorRecord> getRecordsInCache(Set<String> keys) {
        List<AsSelectorRecord> sync = new ArrayList<>(THRESHOLD * 3);
        keys.forEach(key -> {
            BoundZSetOperations<String, Object> ops = redisTemplate.boundZSetOps(key);
            Set<ZSetOperations.TypedTuple<Object>> scores = ops.reverseRangeWithScores(0, -1);
            if (CollUtil.isEmpty(scores)) {
                return;
            }
            List<RecordCounter> data = scores.stream()
                    .map(v -> new RecordCounter(Convert.toLong(v.getValue()), Convert.toInt(v.getScore())))
                    .collect(Collectors.toList());
            String[] v = key.split(":");
            Long companyId = Convert.toLong(v[1]);
            Long empId = Convert.toLong(v[2]);
            String type = v[3];
            filterDeleted(type, data);
            sync.add(new AsSelectorRecord(IdUtils.getId(), companyId, empId, type, data, LocalDateTime.now()));
        });
        return sync;
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        ConfigurableApplicationContext context = event.getApplicationContext();
        buildPermissionMap(context);
        buildSpecialHandlerMap(context);
    }

    private void buildPermissionMap(ConfigurableApplicationContext context) {
        AsUserOrgService userOrgService = context.getBean(AsUserOrgService.class);
        // 选组织
        permFilterMap.put(SelectorTypeEnum.ORG, ids -> {
            List<Long> hasPermOrgIds = orgService.hasPermOrgIds(ids, null);
            ids.removeIf(id -> !hasPermOrgIds.contains(id));
        });
        // 根据组织选人
        permFilterMap.put(SelectorTypeEnum.ORG_EMP, ids -> {
            List<Long> orgIds = orgService.hasPermOrgIds(ListUtil.empty(), null);
            if (CollUtil.isEmpty(orgIds)) {
                ids.clear();
                return;
            }
            Map<Long, Set<Long>> userIdOrgIdMap = userOrgService.list(
                    Wrappers.lambdaQuery(AsUserOrg.class)
                            .in(AsUserOrg::getUserId, ids)
            ).stream().collect(Collectors.groupingBy(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toSet())));
            ids.removeIf(id -> {
                Set<Long> orgIdSet = userIdOrgIdMap.getOrDefault(id, new HashSet<>());
                if (CollUtil.isEmpty(orgIdSet)) {
                    return true;
                }
                return Collections.disjoint(orgIds, orgIdSet);
            });
        });
        // 选审批角色
        permFilterMap.put(SelectorTypeEnum.APPROVE_ROLE, ids -> {
        });
        // 根据系统角色选人
        permFilterMap.put(SelectorTypeEnum.ROLE_EMP, ids -> {
        });
        // 选资产分类
        permFilterMap.put(SelectorTypeEnum.ASSET_CATE, ids -> {
            List<Long> hasPermCateIds = assetCategoryAbs.hasPermCateIds(ids);
            ids.removeIf(id -> !hasPermCateIds.contains(id));
        });
        // 选耗材分类
        permFilterMap.put(SelectorTypeEnum.MATERIAL_CATE, ids -> {
        });
        // 选耗材仓库
        permFilterMap.put(SelectorTypeEnum.REPOSITORY, ids -> {
            List<Long> hasPermRepoIds = repositoryAbs.hasPermRepoIds(ids);
            ids.removeIf(id -> !hasPermRepoIds.contains(id));
        });
        // 选供应商
        permFilterMap.put(SelectorTypeEnum.SUPPLIER, ids -> {
        });
        // 选资产区域
        permFilterMap.put(SelectorTypeEnum.AREA, ids -> {
            List<Long> hasPermAreaIds = assetAreaAbs.hasPermAreaIds(ids);
            ids.removeIf(id -> !hasPermAreaIds.contains(id));
        });
        // 选公司
        permFilterMap.put(SelectorTypeEnum.COMPANY, ids -> {
            orgService.hasPermOrgIds(ids, AssetConstant.ORG_TYPE_COMPANY);
        });
        // 选点位
        permFilterMap.put(SelectorTypeEnum.SNT_POINT, ids -> {
        });
    }

    private void buildSpecialHandlerMap(ConfigurableApplicationContext context) {
        AsUserOrgService userOrgService = context.getBean(AsUserOrgService.class);
        postProcessMap.put(SelectorTypeEnum.ORG_EMP, selectorRecords -> {
            List<Long> empIds = selectorRecords.stream().map(SelectorRecord::getId).collect(Collectors.toList());
            if (CollUtil.isEmpty(empIds)) {
                return;
            }
            Map<Long, List<Long>> group = userOrgService.list(
                    Wrappers.lambdaQuery(AsUserOrg.class)
                            .in(AsUserOrg::getUserId, empIds)
            ).stream().collect(Collectors.groupingBy(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toList())));
            selectorRecords.forEach(v -> {
                List<Long> orgIds = group.getOrDefault(v.getId(), Collections.emptyList());
                // 员工所在的组织
                v.getExtra().put("orgIds", orgIds);
                // 员工所在的公司
                if (CollUtil.isNotEmpty(orgIds)) {
                    OrgService orgService = context.getBean(OrgService.class);
                    List<Long> companyIds = orgService.list(
                            Wrappers.lambdaQuery(AsOrg.class)
                                    .select(AsOrg::getId, AsOrg::getCompanyOwner, AsOrg::getOrgType)
                                    .in(AsOrg::getId, orgIds)
                    ).stream().map(k -> {
                        if (k.getOrgType() == 1) {
                            return k.getId();
                        }
                        return k.getCompanyOwner();
                    }).collect(Collectors.toList());
                    v.getExtra().put("companyIds", companyIds);
                } else {
                    v.getExtra().put("companyIds", Collections.emptyList());
                }
            });
        });
        postProcessMap.put(SelectorTypeEnum.SNT_POINT, selectorRecords -> selectorRecords.forEach(v -> {
            AsEquipmentSiteInspectPointDto point = equipmentSiteInspectPointAbs.getById(v.getId());
            v.getExtra().put("specificLocation", point.getSpecificLocation());
            v.getExtra().put("storageAreaText", point.getStorageAreaText());
        }));
    }

}
