package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AsAccountEmployeeMapper extends BaseMapper<AsAccountEmployee> {

    List<Long> filterHasAccountEmpIds(@Param("ids") List<Long> ids);

    List<Long> hasAccountEmpId(@Param("companyId") Long companyId);
}
