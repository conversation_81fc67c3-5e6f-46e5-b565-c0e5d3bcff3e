package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.annotation.MapEntity;
import com.niimbot.asset.framework.annotation.MapPage;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsDictData;
import com.niimbot.asset.system.service.DictDataService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/11/9 8:53
 */
@RestController
@RequestMapping("server/system/dictData")
public class DictDataServiceController {

    @Autowired
    private DictDataService dictDataService;

    @GetMapping(value = "/page")
    public IPage<AsDictData> page(@MapEntity AsDictData dictData, @MapPage Page<AsDictData> page) {
        return dictDataService.page(page, new QueryWrapper<>(dictData));
    }

    @GetMapping(value = "/list")
    public List<AsDictData> list(@MapEntity AsDictData dictData) {
        return dictDataService.list(new QueryWrapper<>(dictData));
    }

    @GetMapping("/{dictCode}")
    public AsDictData getInfo(@PathVariable("dictCode") Long dictCode) {
        return dictDataService.getById(dictCode);
    }

    @GetMapping("/type/{dictType}")
    public List<AsDictData> dictType(@PathVariable("dictType") String dictType) {
        return dictDataService.selectDictDataByType(dictType);
    }

    @PostMapping
    public Boolean add(@RequestBody AsDictData dictData) {
        if (!dictDataService.save(dictData)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }

    @PutMapping
    public Boolean edit(@RequestBody AsDictData dictData) {
        if (!dictDataService.updateById(dictData)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @DeleteMapping
    public Boolean remove(@RequestBody List<Long> dictCodes) {
        if (dictDataService.getBaseMapper().deleteBatchIds(dictCodes) != dictCodes.size()) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }
        return true;
    }

}
