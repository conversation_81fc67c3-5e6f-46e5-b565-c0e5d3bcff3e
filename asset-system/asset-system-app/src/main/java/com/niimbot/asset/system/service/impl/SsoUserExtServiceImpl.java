package com.niimbot.asset.system.service.impl;

import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.SsoExceptionUtils;
import com.niimbot.asset.system.service.UserExtService;
import com.niimbot.kalimdor.hulk.exceptions.ClientException;
import com.niimbot.kalimdor.hulk.model.TokenResponse;
import com.niimbot.kalimdor.odin.OdinKamClient;
import com.niimbot.kalimdor.odin.model.VerifyCodeAuthenticationRequest;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 获取用户unionId接口实现
 *
 * <AUTHOR>
 * @date 2021/4/2 16:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Profile({Edition.SAAS, Edition.DING, Edition.WEIXIN})
public class SsoUserExtServiceImpl implements UserExtService {
    private final OdinKamClient odinKamClient;
    @Override
    public TokenResponse getUnionIdBySmsCode(String mobile, String smsCode) {
        try {
            return odinKamClient.verifyCodeAuthority(
                    new VerifyCodeAuthenticationRequest(mobile, smsCode));
        } catch (ClientException e) {
            throw SsoExceptionUtils.resolveCallClientException(e);
        }
    }

}
