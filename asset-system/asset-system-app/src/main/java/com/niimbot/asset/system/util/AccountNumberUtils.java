package com.niimbot.asset.system.util;

import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.system.service.CusUserService;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.env.Environment;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * 业务相关工具类
 *
 * <AUTHOR>
 * @Date 2020/11/18
 */
public final class AccountNumberUtils {

    /**
     * 获取最大登录账号
     *
     * @return GZ********递增
     */
    public static String getMaxAccount() {
        CusUserService cusUserService = SpringUtil.getBean(CusUserService.class);
        String prefix = SpringUtil.getBean(Environment.class).getProperty(BaseConstant.USER_ACCOUNT_PREFIX);
        if (StrUtil.isBlank(prefix)) {
            prefix = "GZ";
        }
        String maxAccount = cusUserService.getMaxAccount();
        maxAccount = maxAccount == null ? prefix + "********" :
                String.format("%s%d", prefix, NumberUtils.createLong(maxAccount.substring(2)) + 1L);
        return maxAccount;
    }
}
