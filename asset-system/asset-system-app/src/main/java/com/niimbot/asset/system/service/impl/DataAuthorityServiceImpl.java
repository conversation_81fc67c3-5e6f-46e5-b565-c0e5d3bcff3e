package com.niimbot.asset.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsDataAuthorityMapper;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.service.DataAuthorityService;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/10/21 16:16
 */
@Service
public class DataAuthorityServiceImpl extends ServiceImpl<AsDataAuthorityMapper, AsDataAuthority> implements DataAuthorityService {

    @Override
    public List<AsDataAuthority> getListByUserId(Long userId) {
        return getBaseMapper().getListByUserId(userId);
    }

    @Override
    public List<AsDataAuthority> listCompanyPerms(Long companyId, Wrapper<AsDataAuthority> queryWrapper) {
        return getBaseMapper().listCompanyPerms(companyId, queryWrapper);
    }

}
