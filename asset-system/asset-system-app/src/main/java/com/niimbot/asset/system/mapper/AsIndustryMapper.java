package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsIndustry;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.statistics.StatisticsIndustryDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/2
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsIndustryMapper.class)
public interface AsIndustryMapper extends BaseMapper<AsIndustry> {
    /**
     * 按行业分类统计用户数
     *
     * @return 用户数量统计数据
     */
    List<StatisticsIndustryDto> selectUserNumByIndustry();

    /**
     * 按行业分类统计沉睡用户数和流失用户数
     *
     * @return 用户数量统计数据
     */
    List<StatisticsIndustryDto> selectLostUserNumByIndustry(@Param("preDate") String preDate);

    /**
     * 按行业分类统计有效用户数（上周活跃，并且资产数量大于500）
     *
     * @return 用户数量统计数据
     */
    List<StatisticsIndustryDto> selectEffectiveUserNumByIndustry(
            @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 按行业分类统计周活跃用户数（上周活跃）
     *
     * @return 用户数量统计数据
     */
    List<StatisticsIndustryDto> selectWeekActiveUserNumByIndustry(
            @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    Integer industryRefCompany(Long industryId);
}
