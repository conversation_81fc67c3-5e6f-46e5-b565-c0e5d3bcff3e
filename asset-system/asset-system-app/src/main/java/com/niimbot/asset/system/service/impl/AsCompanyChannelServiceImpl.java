package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsCompanyChannelMapper;
import com.niimbot.asset.system.mapper.AsPromoteChannelMapper;
import com.niimbot.asset.system.mapper.AsPromoteChannelSourceMapper;
import com.niimbot.asset.system.model.AsCompanyChannel;
import com.niimbot.asset.system.model.AsPromoteChannel;
import com.niimbot.asset.system.model.AsPromoteChannelSource;
import com.niimbot.asset.system.service.AsCompanyChannelService;
import com.niimbot.system.CompanyChannelInfo;

import org.springframework.stereotype.Service;

import java.util.Objects;

import javax.annotation.Resource;

/**
 * <p>
 * 企业CRM信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class AsCompanyChannelServiceImpl extends ServiceImpl<AsCompanyChannelMapper, AsCompanyChannel>
        implements AsCompanyChannelService {
    @Resource
    private AsPromoteChannelMapper promoteChannelMapper;
    @Resource
    private AsPromoteChannelSourceMapper promoteChannelSourceMapper;

    @Override
    public AsPromoteChannel getPromoteChannelByCode(Long code) {
        return promoteChannelMapper.selectOne(
                Wrappers.<AsPromoteChannel>lambdaQuery()
                        .eq(AsPromoteChannel::getChannelCode, code));
    }

    @Override
    public AsPromoteChannelSource getAsPromoteChannelSourceByCode(Long code) {
        return promoteChannelSourceMapper.selectOne(
                Wrappers.<AsPromoteChannelSource>lambdaQuery()
                        .eq(AsPromoteChannelSource::getCode, code));
    }

    @Override
    public CompanyChannelInfo getCompanyChannelInfo(Long companyId) {
        AsCompanyChannel companyChannel = this.getById(companyId);
        if (Objects.isNull(companyChannel)) {
            return null;
        }
        AsPromoteChannel promoteChannel = promoteChannelMapper.selectOne(
                Wrappers.lambdaQuery(AsPromoteChannel.class)
                        .eq(AsPromoteChannel::getChannelCode, companyChannel.getChannelCode())
        );
        AsPromoteChannelSource source = promoteChannelSourceMapper.selectOne(
                Wrappers.lambdaQuery(AsPromoteChannelSource.class)
                        .eq(AsPromoteChannelSource::getCode, companyChannel.getSourceCode())
        );
        CompanyChannelInfo info = new CompanyChannelInfo().setChannelCode(companyChannel.getChannelCode()).setSourceCode(companyChannel.getSourceCode());
        if (Objects.nonNull(promoteChannel)) {
            info.setChannelName(promoteChannel.getChannelName());
        }
        if (Objects.nonNull(source)) {
            info.setSourceName(source.getName());
        }
        return info;
    }
}
