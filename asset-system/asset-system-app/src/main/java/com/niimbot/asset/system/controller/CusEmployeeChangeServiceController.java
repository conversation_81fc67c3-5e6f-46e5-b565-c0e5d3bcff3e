package com.niimbot.asset.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.system.model.AsEmployeeResignRecord;
import com.niimbot.asset.system.service.AsEmployeeChangeService;
import com.niimbot.asset.system.service.AsEmployeeResignRecordService;
import com.niimbot.system.CusEmployeeChangeDto;
import com.niimbot.system.CusEmployeeChangeQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/16 11:50
 */
@RestController
@RequestMapping("server/system/employee/change")
public class CusEmployeeChangeServiceController {

    private final AsEmployeeChangeService employeeChangeService;

    private final AsEmployeeResignRecordService recordService;

    @Autowired
    public CusEmployeeChangeServiceController(AsEmployeeChangeService employeeChangeService,
                                              AsEmployeeResignRecordService recordService) {
        this.employeeChangeService = employeeChangeService;
        this.recordService = recordService;
    }

    @GetMapping("/page")
    public IPage<CusEmployeeChangeDto> page(CusEmployeeChangeQueryDto query) {
        return employeeChangeService.getPage(query);
    }

    @GetMapping("/asset")
    public IPage<JSONObject> changeAsset(CusEmployeeChangeQueryDto query) {
        return employeeChangeService.changeAsset(query);
    }

    @GetMapping("/operation/{changeId}")
    public List<AsEmployeeResignRecord> operation(@PathVariable("changeId") Long changeId) {
        return recordService.list(new LambdaQueryWrapper<AsEmployeeResignRecord>().eq(AsEmployeeResignRecord::getChangeId, changeId));
    }

}
