package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsPrinterDriveMapper;
import com.niimbot.asset.system.model.AsPrinterDrive;
import com.niimbot.asset.system.service.PrinterDriveService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class PrinterDriveServiceImpl extends ServiceImpl<AsPrinterDriveMapper, AsPrinterDrive> implements PrinterDriveService {
}
