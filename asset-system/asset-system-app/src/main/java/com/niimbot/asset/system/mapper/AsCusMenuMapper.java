package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCusMenu;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.CusMenuDto;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 客户菜单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsCusMenuMapper.class)
public interface AsCusMenuMapper extends BaseMapper<AsCusMenu> {
    /**
     * 配置角色菜单所用列表
     *
     * @return
     */
    List<CusMenuDto> configRoleMenuPcList();

    /**
     * 配置角色模块配置菜单所用列表
     *
     * @return
     */
    List<CusMenuDto> configRoleModulePcList();

    /**
     * 模块配置菜单
     *
     * @return
     */
    List<CusMenuDto> modulePcList();

    /**
     * 根据用户查询系统菜单列表
     *
     * @param userId  用户ID
     * @param pcMenus 版本菜单
     * @return 菜单列表
     */
    List<CusMenuDto> userRoleMenuPcList(@Param("userId") Long userId, @Param("pcMenus") List<Long> pcMenus);

    /**
     * 配置角色菜单所用列表
     *
     * @return
     */
    List<CusMenuDto> configRoleMenuAppList();

    /**
     * 基础菜单
     *
     * @return
     */
    List<CusMenuDto> baseAppList();

    /**
     * 模块配置菜单
     *
     * @return
     */
    List<CusMenuDto> moduleAppList();

    /**
     * 配置角色模块配置菜单所用列表
     *
     * @return
     */
    List<CusMenuDto> configRoleModuleAppList();

    /**
     * 根据用户查询系统菜单列表
     *
     * @param userId   用户ID
     * @param appMenus 版本菜单
     * @return 菜单列表
     */
    List<CusMenuDto> userRoleMenuAppList(@Param("userId") Long userId, @Param("appMenus") List<Long> appMenus);


    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectMenuPermsByUserId(Long userId);
}
