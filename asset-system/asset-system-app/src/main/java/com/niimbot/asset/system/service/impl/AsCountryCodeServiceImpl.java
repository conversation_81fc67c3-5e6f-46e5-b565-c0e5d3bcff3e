package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.mapper.AsCountryCodeMapper;
import com.niimbot.asset.system.model.AsCountryCode;
import com.niimbot.asset.system.service.AsCountryCodeService;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;

@Service
public class AsCountryCodeServiceImpl extends ServiceImpl<AsCountryCodeMapper, AsCountryCode> implements AsCountryCodeService, ApplicationListener<ApplicationReadyEvent> {

    @Resource
    private RedisService redisService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;


    @Override
    public List<String> allCountryCode() {
        if (redisService.hasKey(RedisConstant.countryCodeKey())) {
            return Convert.toList(String.class, redisService.sMembers(RedisConstant.countryCodeKey()));
        } else {
            List<AsCountryCode> list = list();
            Object[] array = list.stream().map(AsCountryCode::getCountryCode).toArray();
            redisService.sAdd(RedisConstant.countryCodeKey(), array);
            return CollUtil.toList(array).stream().map(Convert::toStr).collect(Collectors.toList());
        }
    }

    @Override
    public boolean updateStatusByIds(Integer chooseStatus, List<Long> countryIds) {
        return this.getBaseMapper().updateStatusByIds(chooseStatus, countryIds);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return;
        }

        taskExecutor.execute(() -> {
            redisService.del(RedisConstant.countryCodeKey());
            List<AsCountryCode> list = list();
            Object[] array = list.stream().map(AsCountryCode::getCountryCode).toArray();
            redisService.sAdd(RedisConstant.countryCodeKey(), array);
        });
    }
}
