package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsQueryConditionConfig;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.system.QueryConditionConfigDto;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/9/5 11:27
 */
@RestController
@RequestMapping("server/system/queryCondition/config")
@Slf4j
@Validated
public class AsQueryConditionConfigServiceController {

    private final AsQueryConditionConfigService queryConditionConfigService;

    public AsQueryConditionConfigServiceController(AsQueryConditionConfigService queryConditionConfigService) {
        this.queryConditionConfigService = queryConditionConfigService;
    }

    @GetMapping("/{type}")
    public QueryConditionConfigDto getByType(@PathVariable("type") String type) {
        return queryConditionConfigService.getByType(type);
    }


    @GetMapping("/terminal/{type}")
    public QueryConditionConfigDto getByType(@PathVariable("type") String type,
                                             @RequestParam("terminal") String terminal) {
        return queryConditionConfigService.getByType(type, terminal);
    }

    @PostMapping
    public Boolean saveOrUpdate(@RequestBody AsQueryConditionConfig queryCondition) {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        AsQueryConditionConfig one = queryConditionConfigService.getOne(Wrappers.lambdaQuery(AsQueryConditionConfig.class)
                .eq(AsQueryConditionConfig::getUserId, userId)
                .eq(AsQueryConditionConfig::getTerminal, queryCondition.getTerminal())
                .eq(AsQueryConditionConfig::getType, queryCondition.getType()));
        if (one == null) {
            queryCondition.setUserId(userId);
            return queryConditionConfigService.save(queryCondition);
        } else {
            return queryConditionConfigService.update(Wrappers.lambdaUpdate(AsQueryConditionConfig.class)
                    .set(AsQueryConditionConfig::getConditions, queryCondition.getConditions().toJSONString())
                    .eq(AsQueryConditionConfig::getUserId, userId)
                    .eq(AsQueryConditionConfig::getTerminal, StrUtil.isBlank(queryCondition.getTerminal()) ? StrUtil.EMPTY : queryCondition.getTerminal())
                    .eq(AsQueryConditionConfig::getType, queryCondition.getType()));
        }
    }
}
