package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.AssetAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/7 10:58
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.AssetAbsImpl")
@FeignClient(name = "asset-means", url = "https://{gateway}/client/abs/means/assetAbs/")
public interface AssetAbsRemoteClient extends AssetAbs {
}
