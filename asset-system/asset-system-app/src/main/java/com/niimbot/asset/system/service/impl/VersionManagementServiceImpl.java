package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsVersionManagementMapper;
import com.niimbot.asset.system.mapper.AsVersionMessageMapper;
import com.niimbot.asset.system.model.AsVersionManagement;
import com.niimbot.asset.system.model.AsVersionMessage;
import com.niimbot.asset.system.service.VersionManagementService;
import com.niimbot.system.VersionManagementDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VersionManagementServiceImpl extends ServiceImpl<AsVersionManagementMapper, AsVersionManagement> implements VersionManagementService {

    private final AsVersionMessageMapper versionMessageMapper;

    @Override
    public VersionManagementDto latestVersion(String version, Integer clientType) {
        String[] versionSplit = version.split("\\.");
        if (versionSplit.length != 3) {
            log.error("version {} is error", version);
            return null;
        }
        // 获取当前最大版本号
        List<VersionManagementDto> maxVersionExt = this.getBaseMapper().getMaxVersionExt(clientType,
                Convert.toInt(versionSplit[0], 999),
                Convert.toInt(versionSplit[1], 999),
                Convert.toInt(versionSplit[2], 999));
        if (CollUtil.isEmpty(maxVersionExt)) {
            return null;
        }

        // 返回最新版本
        VersionManagementDto max = maxVersionExt.get(0);
        if (max.getIsMandatoryUpdate()) {
            return max;
        }

        // 当前版本与最新版本间存在强制更新的版本时本次更新也要改成强制更新
        Optional<VersionManagementDto> any = maxVersionExt.stream().filter(VersionManagementDto::getIsMandatoryUpdate).findAny();
        if (any.isPresent()) {
            max.setIsMandatoryUpdate(true);
        }
        return max;
    }

    @Override
    public VersionManagementDto latestVersionMessage() {
        List<VersionManagementDto> maxVersionExt = this.getBaseMapper().getMaxVersionExt(1, null, null, null);
        if (maxVersionExt == null || maxVersionExt.isEmpty()) {
            return null;
        }
        VersionManagementDto max = maxVersionExt.get(0);
        AsVersionMessage versionMessage = versionMessageMapper.selectOne(
                Wrappers.<AsVersionMessage>lambdaQuery()
                        .eq(AsVersionMessage::getVersionManagementId, max.getId())
                        .eq(AsVersionMessage::getUserId, LoginUserThreadLocal.getCurrentUserId())
                        .eq(AsVersionMessage::getIsRead, false)
        );
        if (versionMessage == null) {
            return null;
        }
        max.setIsRead(versionMessage.getIsRead());
        return max;
    }

    @Override
    public Boolean versionMessageRead(Long versionManagementId) {
        versionMessageMapper.update(
                null,
                Wrappers.<AsVersionMessage>lambdaUpdate()
                        .set(AsVersionMessage::getIsRead, true)
                        .eq(AsVersionMessage::getVersionManagementId, versionManagementId)
                        .eq(AsVersionMessage::getUserId, LoginUserThreadLocal.getCurrentUserId())
        );
        return true;
    }
}
