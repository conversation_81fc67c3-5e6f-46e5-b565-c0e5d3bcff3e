package com.niimbot.asset.system.service.impl;

import cn.hutool.core.util.BooleanUtil;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.SsoExceptionUtils;
import com.niimbot.asset.system.service.SmsCodeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.kalimdor.hulk.exceptions.ClientException;
import com.niimbot.kalimdor.quicksilver.QuickSilverKamClient;
import com.niimbot.kalimdor.quicksilver.model.SingleEmailMessageRequest;
import com.niimbot.kalimdor.quicksilver.model.SinglePhoneMessageRequest;
import com.niimbot.kalimdor.quicksilver.model.VerifyCodeCheckRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/1/15 16:01
 */
@Service
@Slf4j
@Profile({Edition.SAAS, Edition.DING, Edition.WEIXIN})
public class SsoSmsCodeServiceImpl implements SmsCodeService {
    private final QuickSilverKamClient quickSilverKamClient;
    private final RedisService redisService;

    public SsoSmsCodeServiceImpl(QuickSilverKamClient quickSilverKamClient,
                                 RedisService redisService) {
        this.quickSilverKamClient = quickSilverKamClient;
        this.redisService = redisService;
    }

    @Override
    public void sendSmsCode(String iddCode, String mobile) {
        try {
            SinglePhoneMessageRequest singlePhoneMessageRequest = new SinglePhoneMessageRequest(iddCode, mobile);
            singlePhoneMessageRequest.setMessageCode("GZ128794");
            quickSilverKamClient.sendPhoneVerifyCode(singlePhoneMessageRequest);
        } catch (ClientException e) {
            throw SsoExceptionUtils.resolveCallClientException(e);
        } catch (Exception e) {
            log.error("send sms code error, {}", e.getMessage(), e);
        }
    }

    @Override
    public void sendCommonCode(String type, String addr) {
        if (CodeTypeEnum.MOBILE.name().equalsIgnoreCase(type)) {
            try {
                SinglePhoneMessageRequest singlePhoneMessageRequest = new SinglePhoneMessageRequest("86", addr);
                singlePhoneMessageRequest.setMessageCode("GZ128794");
                quickSilverKamClient.sendPhoneVerifyCode(singlePhoneMessageRequest);
            } catch (ClientException e) {
                throw SsoExceptionUtils.resolveCallClientException(e);
            }
        } else if (CodeTypeEnum.EMAIL.name().equalsIgnoreCase(type)) {
            try {
                SingleEmailMessageRequest emailMessageRequest = new SingleEmailMessageRequest();
                emailMessageRequest.setMessageCode("GZ128794");
                emailMessageRequest.setEmail(addr);
                quickSilverKamClient.sendEmailVerifyCode(emailMessageRequest);
            } catch (ClientException e) {
                throw SsoExceptionUtils.resolveCallClientException(e);
            }
        }
    }

    @Override
    public Boolean checkSmsCode(String mobile, String smsCode) {
        // 限流防刷
        String key = "SmsCode:" + mobile;
        Long incr = redisService.incr(key, 1);
        if (incr != null && incr > 6) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请求过多，请稍后再试");
        }
        redisService.expire(key, 3, TimeUnit.MINUTES);
        try {
            Boolean check = quickSilverKamClient.messageCheck(new VerifyCodeCheckRequest(mobile, smsCode));
            if (BooleanUtil.isTrue(check)) {
                redisService.del(key);
            }
            return check;
        } catch (ClientException e) {
            throw SsoExceptionUtils.resolveCallClientException(e);
        }
    }

    @AllArgsConstructor
    @Getter
    public enum CodeTypeEnum {
        MOBILE,
        EMAIL
    }
}
