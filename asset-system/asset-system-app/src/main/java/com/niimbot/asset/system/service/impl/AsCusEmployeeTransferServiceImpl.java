package com.niimbot.asset.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.AssetAbs;
import com.niimbot.asset.system.abs.AssetLogAbs;
import com.niimbot.asset.system.abs.EntMatAbs;
import com.niimbot.asset.system.abs.WorkflowAbs;
import com.niimbot.asset.system.dto.*;
import com.niimbot.asset.system.dto.clientobject.AssetLogCO;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsEmployeeChange;
import com.niimbot.asset.system.model.AsEmployeeResignRecord;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeTransferService;
import com.niimbot.asset.system.service.AsEmployeeChangeService;
import com.niimbot.asset.system.service.AsEmployeeResignRecordService;
import com.niimbot.system.DataTransfer;
import com.niimbot.system.RemoveEmployDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/9/18 11:13
 */
@Service
public class AsCusEmployeeTransferServiceImpl implements AsCusEmployeeTransferService {

    @Autowired
    private AssetAbs assetAbs;

    @Autowired
    private WorkflowAbs workflowAbs;
    @Resource
    private EntMatAbs entMatAbs;

    private final AsEmployeeResignRecordService recordService;

    private final AsEmployeeChangeService employeeChangeService;

    private final AssetLogAbs assetLogAbs;

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Resource
    private AsCusEmployeeService employeeService;

    @Autowired
    public AsCusEmployeeTransferServiceImpl(AsEmployeeResignRecordService recordService,
                                            AsEmployeeChangeService employeeChangeService,
                                            AssetLogAbs assetLogAbs) {
        this.recordService = recordService;
        this.employeeChangeService = employeeChangeService;
        this.assetLogAbs = assetLogAbs;
    }

    /**
     * 员工删除资产转移
     *
     * @param employ 转移信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferEmp(RemoveEmployDto employ) {
        Long changeId = IdUtils.getId();
        // 资产转移
        // 将使用资产与管理资产置为闲置
        List<String> assetExplainList = assetDataTransfer(employ, changeId);
        // 盘点转移
//        inventoryDataTransfer(employ, changeId);
        // 审批流转移
        workflowTransfer(employ, changeId);
        // 设备保养任务转移
        entMatTransfer(employ, changeId);
        // 写入异动记录
        AsEmployeeChange employeeChange = new AsEmployeeChange();
        employeeChange.setId(changeId)
                .setType(DictConstant.CHANGE_TYPE_DELETE)
                .setEmpId(employ.getEmployeeId())
                .setOrgExplain("删除员工");
        if (CollUtil.isNotEmpty(assetExplainList)) {
            employeeChange.setAssetExplain(String.join("；", assetExplainList));
        } else {
            employeeChange.setAssetExplain("无");
        }
        employeeChangeService.save(employeeChange);
    }

    private AsEmployeeResignRecord buildRecord(RemoveEmployDto employ, String resType, Long changeId) {
        AsEmployeeResignRecord resignRecord = new AsEmployeeResignRecord();
        resignRecord.setChangeId(changeId)
                .setEmpId(employ.getEmployeeId())
                .setEmpName(employ.getEmployeeName())
                .setResType(resType);
        switch (resType) {
            case DictConstant.TRANS_EMP_MANAGE_ASSET:
                DataTransfer assetUnderManagement = employ.getManageAsset();
                if (Objects.nonNull(assetUnderManagement)) {
                    resignRecord.setForwardOrgId(assetUnderManagement.getReceiveOrgId())
                            .setForwardUserId(assetUnderManagement.getReceiveEmployeeId());
                }
                return resignRecord;
            case DictConstant.TRANS_EMP_USE_ASSET:
                DataTransfer assetUser = employ.getUseAsset();
                if (Objects.nonNull(assetUser)) {
                    resignRecord.setForwardOrgId(assetUser.getReceiveOrgId())
                            .setForwardUserId(assetUser.getReceiveEmployeeId());
                }
                return resignRecord;
            case DictConstant.TRANS_EMP_APPROVAL_TASK:
                DataTransfer approvalTask = employ.getApprovalTask();
                resignRecord.setForwardUserId(approvalTask.getReceiveEmployeeId());
                return resignRecord;
            case DictConstant.TRANS_EMP_ENT_MAT_TASK:
                DataTransfer entMatTask = employ.getEntMatTask();
                resignRecord.setForwardUserId(entMatTask.getReceiveEmployeeId());
                return resignRecord;
            default:
                return null;
        }
    }

    private List<String> assetDataTransfer(RemoveEmployDto employ, Long changeId) {
        Long employeeId = employ.getEmployeeId();
        List<String> assetExplainList = new ArrayList<>();
        List<AssetLogCO> assetLogs = new ArrayList<>();
        // 管理资产
        if (employ.getManageAsset() != null) {
            List<AssetLogCO> assetLogList = assetAbs.assetManageTransfer(new AssetManageTransferCmd()
                    .setRemoveEmployeeId(employeeId)
                    .setEmployeeChangeId(changeId)
                    .setAssetUnderManagement(employ.getManageAsset())
                    .setCompanyId(LoginUserThreadLocal.getCompanyId()));
            assetLogs.addAll(assetLogList);
            AsEmployeeResignRecord record = buildRecord(employ, DictConstant.TRANS_EMP_MANAGE_ASSET, changeId);
            recordService.save(record);
            String userName = cacheResourceUtil.getUserNameAndCode(employ.getManageAsset().getReceiveEmployeeId());
            assetExplainList.add("管理资产转移到" + (StrUtil.isNotEmpty(userName) ? userName : "--"));
        }
        // 使用资产
        if (employ.getUseAsset() != null || BooleanUtil.isTrue(employ.getAssetStatusToIdle())) {
            // 如果转闲置，把使用清理
            if (BooleanUtil.isTrue(employ.getAssetStatusToIdle())) {
                employ.setUseAsset(new DataTransfer());
            }
            List<AssetLogCO> assetLogList = assetAbs.assetUseTransfer(new AssetUseTransferCmd()
                    .setRemoveEmployeeId(employeeId)
                    .setEmployeeChangeId(changeId)
                    .setUseAsset(employ.getUseAsset())
                    .setCompanyId(LoginUserThreadLocal.getCompanyId())
                    .setToIdle(employ.getAssetStatusToIdle()));
            assetLogs.addAll(assetLogList);
            AsEmployeeResignRecord record = buildRecord(employ, DictConstant.TRANS_EMP_USE_ASSET, changeId);
            recordService.save(record);
            if (employ.getUseAsset() != null) {
                Long orgId = employ.getUseAsset().getReceiveOrgId();
                String orgNameAndCode = cacheResourceUtil.getOrgNameAndCode(orgId);
                if (StrUtil.isNotEmpty(orgNameAndCode)) {
                    String userName = cacheResourceUtil.getUserNameAndCode(employ.getUseAsset().getReceiveEmployeeId());
                    assetExplainList.add("使用资产转移到" + orgNameAndCode + (StrUtil.isNotEmpty(userName) ? "的" + userName : "的--"));
                }
            } else if (BooleanUtil.isTrue(employ.getAssetStatusToIdle())) {
                assetExplainList.add("使用资产转为闲置");
            }
        }
        // 资产上报任务转移
//        if (employ.getAssetReportTask() != null) {
//            assetReportTaskAbs.assetReportTaskDataTransfer(new AssetReportTaskDataTransferCmd()
//                    .setRemoveEmployeeId(employeeId)
//                    .setAssetUnderManagement(employ.getAssetReportTask())
//                    .setCompanyId(LoginUserThreadLocal.getCompanyId()));
//            AsEmployeeResignRecord record = buildRecord(employ, DictConstant.TRANS_EMP_REPORT_TASK, changeId);
//            recordService.save(record);
//        }

        Map<Long, AssetLogCO> assetLogMap = new HashMap<>();
        for (AssetLogCO assetLog : assetLogs) {
            Long assetId = assetLog.getAssetId();
            AssetLogCO asAssetLog = assetLogMap.get(assetId);
            if (asAssetLog == null) {
                assetLog.setActionType(AssetConstant.OPT_ORG_CHANGE)
                        .setHandleTime(LocalDateTime.now())
                        .setActionName("员工异动");
                assetLogMap.put(assetId, assetLog);
            } else {
                asAssetLog.setActionContent(asAssetLog.getActionContent() + "；" + assetLog.getActionContent());
            }
        }
        Collection<AssetLogCO> logs = assetLogMap.values();
        if (CollUtil.isNotEmpty(logs)) {
            assetLogAbs.saveBatchAssetLog(new AssetLogSaveBatchCmd().setLogs(logs));
        }
        // 写入资产变更记录
        return assetExplainList;
    }

    private void entMatTransfer(RemoveEmployDto employ, Long changeId) {
        if (Objects.nonNull(employ.getEntMatTask())) {
            EntMatTransferCmd cmd = new EntMatTransferCmd();
            cmd.setEntMatTask(employ.getEntMatTask());
            cmd.setRemoveEmployeeId(employ.getEmployeeId());
            cmd.setCompanyId(LoginUserThreadLocal.getCompanyId());
            entMatAbs.entMatTransfer(cmd);
            AsEmployeeResignRecord buildRecord = buildRecord(employ, DictConstant.TRANS_EMP_ENT_MAT_TASK, changeId);
            recordService.save(buildRecord);
        }
    }

    private void workflowTransfer(RemoveEmployDto employ, Long changeId) {
        if (employ.getApprovalTask() != null) {
            AsCusEmployee receiveEmp = employeeService.getById(employ.getApprovalTask().getReceiveEmployeeId());
            DataTransfer dataTransfer =
                    employ.getApprovalTask().setReceiveEmployeeNo(receiveEmp.getEmpNo()).setReceiveEmployeeName(receiveEmp.getEmpName());
            workflowAbs.workflowTransfer(new WorkflowTransferCmd()
                    .setRemoveEmployeeId(employ.getEmployeeId())
                    .setWorkflowTransfer(dataTransfer)
                    .setCompanyId(LoginUserThreadLocal.getCompanyId()));
            AsEmployeeResignRecord record = buildRecord(employ, DictConstant.TRANS_EMP_APPROVAL_TASK, changeId);
            recordService.save(record);
        }
    }
}
