package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCompanyNewbieTask;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.system.CompanyNewbieTaskDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@EnableDataPerm
public interface AsCompanyNewbieTaskMapper extends BaseMapper<AsCompanyNewbieTask> {

    List<CompanyNewbieTaskDto> listTask(@Param("companyId") Long companyId);

}
