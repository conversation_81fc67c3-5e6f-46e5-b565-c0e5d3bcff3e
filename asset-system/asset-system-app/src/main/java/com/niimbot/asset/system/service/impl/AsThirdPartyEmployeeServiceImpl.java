package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.utils.DeduplicationUtil;
import com.niimbot.asset.system.mapper.AsThirdPartyEmployeeMapper;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 */
@Service
public class AsThirdPartyEmployeeServiceImpl extends ServiceImpl<AsThirdPartyEmployeeMapper, AsThirdPartyEmployee> implements AsThirdPartyEmployeeService {

    @Autowired
    private AsCusEmployeeService employeeService;

    @Override
    public List<AsThirdPartyEmployee> listByCompanyId(Long companyId, String type) {
        return this.list(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getCompanyId, companyId)
                        .eq(AsThirdPartyEmployee::getType, type)
        );
    }

    @Override
    public Optional<AsThirdPartyEmployee> getByEmployeeId(Long employeeId) {
        AsThirdPartyEmployee thirdPartyEmployee = this.getOne(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getEmployeeId, employeeId)
        );
        return Optional.ofNullable(thirdPartyEmployee);
    }

    @Override
    public Optional<AsThirdPartyEmployee> getOne(String type, String userId, Long companyId) {
        AsThirdPartyEmployee thirdPartyEmployee = this.getOne(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getCompanyId, companyId)
                        .eq(AsThirdPartyEmployee::getUserId, userId)
                        .eq(AsThirdPartyEmployee::getType, type)
        );
        return Optional.ofNullable(thirdPartyEmployee);
    }

    @Override
    public void removeByCompanyId(Long companyId, String type) {
        this.remove(
                Wrappers.lambdaUpdate(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getCompanyId, companyId)
                        .eq(AsThirdPartyEmployee::getType, type)
        );
    }

    @Override
    public String getUserId(Long empId) {
        List<AsThirdPartyEmployee> employee = this.list(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .select(AsThirdPartyEmployee::getUserId)
                        .eq(AsThirdPartyEmployee::getEmployeeId, empId)
        );
        if (CollUtil.isEmpty(employee)) {
            return "";
        }
        return employee.get(0).getUserId();
    }

    @Override
    public Map<String, Long> getExternalMapping(Long companyId, List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        return this.list(
                        Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                                .eq(AsThirdPartyEmployee::getCompanyId, companyId)
                                .in(AsThirdPartyEmployee::getUserId, userIds)
                ).stream()
                .filter(DeduplicationUtil.distinctByKey(AsThirdPartyEmployee::getUserId))
                .collect(Collectors.toConcurrentMap(AsThirdPartyEmployee::getUserId, AsThirdPartyEmployee::getEmployeeId));
    }

}
