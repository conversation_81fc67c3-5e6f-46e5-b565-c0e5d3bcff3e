package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.niimbot.asset.system.mapper.AsCusEmployeeSettingMapper;
import com.niimbot.asset.system.model.AsCusEmployeeSetting;
import com.niimbot.asset.system.service.AsCusEmployeeSettingService;

import org.springframework.stereotype.Service;

/**
 * 用户端配置
 *
 * <AUTHOR>
 */
@Service
public class AsCusEmployeeSettingServiceImpl extends ServiceImpl<AsCusEmployeeSettingMapper, AsCusEmployeeSetting>
        implements AsCusEmployeeSettingService {

    @Override
    public void init(Long employeeId) {
        AsCusEmployeeSetting employeeSetting = new AsCusEmployeeSetting()
                .setUserId(employeeId).setAppToolbox(Lists.newArrayList())
                .setPcHome(Lists.newArrayList())
                .setAssetHead(Lists.newArrayList())
                .setMaterialHead(Lists.newArrayList());
        this.save(employeeSetting);
    }

}
