package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.AssetQueryViewAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/10 16:42
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.AssetQueryViewAbsImpl")
@FeignClient(name = "asset-means", url = "http://localhost:8000/")
public interface AssetQueryViewAbsRemoteClient extends AssetQueryViewAbs {
}
