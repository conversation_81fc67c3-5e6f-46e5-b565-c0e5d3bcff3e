package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.system.mapper.AsRoleMenuMapper;
import com.niimbot.asset.system.model.AsCusMenuInit;
import com.niimbot.asset.system.model.AsRoleMenu;
import com.niimbot.asset.system.service.AsCusMenuInitService;
import com.niimbot.asset.system.service.AsRoleMenuService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/11/23
 */
@Service
public class AsRoleMenuServiceImpl extends ServiceImpl<AsRoleMenuMapper, AsRoleMenu> implements AsRoleMenuService {

    private final AsCusMenuInitService menuInitService;

    @Autowired
    public AsRoleMenuServiceImpl(AsCusMenuInitService menuInitService) {
        this.menuInitService = menuInitService;
    }

    /**
     * 初始化资产角色菜单
     *
     * @param assetAdminRoleId 角色ID
     */
    @Override
    public void initAssetRole(Long assetAdminRoleId) {
        List<AsCusMenuInit> menuInits = menuInitService.list(new QueryWrapper<AsCusMenuInit>().lambda()
                .eq(AsCusMenuInit::getRoleCode, BaseConstant.ASSET_ADMIN_ROLE));
        List<AsRoleMenu> collect = menuInits.stream().map(m ->
                new AsRoleMenu().setRoleId(assetAdminRoleId).setMenuId(m.getMenuId()).setType(m.getType())
        ).collect(Collectors.toList());
        this.saveBatch(collect);
    }

    /**
     * 初始化普通角色菜单
     *
     * @param commonRoleId 角色ID
     */
    @Override
    public void initCommonRole(Long commonRoleId) {
        List<AsCusMenuInit> menuInits = menuInitService.list(new QueryWrapper<AsCusMenuInit>().lambda()
                .eq(AsCusMenuInit::getRoleCode, BaseConstant.COMMON_ROLE));
        List<AsRoleMenu> collect = menuInits.stream().map(m ->
                new AsRoleMenu().setRoleId(commonRoleId).setMenuId(m.getMenuId()).setType(m.getType())
        ).collect(Collectors.toList());
        this.saveBatch(collect);
    }
}
