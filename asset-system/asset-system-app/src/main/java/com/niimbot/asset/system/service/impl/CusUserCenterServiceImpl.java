package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsDictData;
import com.niimbot.asset.system.model.AsThirdPartyAccount;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.DictDataService;
import com.niimbot.asset.system.service.UserCenterService;
import com.niimbot.system.UserCenterAPPDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 本地用户中心接口实现
 *
 * <AUTHOR>
 * @date 2021/4/1 14:59
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CusUserCenterServiceImpl implements UserCenterService {

    @Autowired
    private CusUserService userService;
    private final DictDataService dictDataService;
    @Autowired
    private AsThirdPartyAccountServiceImpl thirdPartyAccountService;

    // 新账号体系  QQ与微信社交信息继续在中台获取 钉钉与企业微信社交信息则在本系统获取
    @Override
    public UserCenterAPPDto getUserCenterAppInfo(String unionId) {
        UserCenterAPPDto info = userService.getUserCenterAppInfo();
        info.setSocials(getSocials(unionId));
        return info;
    }

    @Override
    public List<UserCenterAPPDto.Social> getSocials(String unionId) {
        List<AsDictData> socialPlatform = dictDataService.selectDictDataByType("social_platform");
        List<UserCenterAPPDto.Social> socials = socialPlatform.stream().map(dict -> new UserCenterAPPDto.Social()
                .setProvider(dict.getValue())
                .setProviderName(dict.getLabel())
                .setIsBound(false)).collect(Collectors.toList());
        // 存在中台的社交账号绑定信息
        // if (StringUtils.hasText(unionId)) {
        //     try {
        //         UserInfoResponse ssoUser = magnetoKamClient.getAnyUserInfo(new AnyUserInfoRequest(unionId));
        //         Map<String, SocialInfo> socialInfoMap = ssoUser.getSocialInfos().stream().collect(
        //                 Collectors.toMap(SocialInfo::getProvider, socialInfo -> socialInfo, (k1, k2) -> k1));
        //         socials.forEach(social -> {
        //             if (socialInfoMap.containsKey(social.getProvider())) {
        //                 BeanUtil.copyProperties(socialInfoMap.get(social.getProvider()), social);
        //                 social.setIsBound(true);
        //             }
        //         });
        //     } catch (ClientException e) {
        //         // throw SsoExceptionUtils.resolveCallClientException(e);
        //         log.error("从中台获取绑定的社交信息异常", e);
        //     }
        // }
        // 存在系统的社交账号绑定信息
        List<AsThirdPartyAccount> accounts = thirdPartyAccountService.list(
                Wrappers.lambdaQuery(AsThirdPartyAccount.class)
                        .eq(AsThirdPartyAccount::getAccountId, LoginUserThreadLocal.getAccountId())
        );
        if (!CollUtil.isEmpty(accounts)) {
            Map<String, AsThirdPartyAccount> collect = accounts.stream().collect(Collectors.toMap(AsThirdPartyAccount::getType, v -> v));
            socials.forEach(data -> {
                if (collect.containsKey(data.getProvider())) {
                    AsThirdPartyAccount partyAccount = collect.get(data.getProvider());
                    data.setIsBound(true);
                    data.setNickname(partyAccount.getNickname()).setProfileUrl(partyAccount.getAvatarUrl());
                }
            });
        }
        return socials;
    }
}
