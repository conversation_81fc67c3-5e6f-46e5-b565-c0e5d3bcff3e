package com.niimbot.asset.system.schedule;

import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.system.model.AsNewbieGuide;
import com.niimbot.asset.system.service.NewbieGuideService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;

/**
 * <AUTHOR>
 */
@Component
public class NewbieGuideScheduler {

    @Resource
    private RedisService redisService;

    @Resource
    private NewbieGuideService newbieGuideService;

    @Autowired
    private RedisDistributeLock redisDistributeLock;

    /**
     * 同步缓存中的引导页点击次数到数据库
     */
    @Scheduled(cron = "0 20 1 * * ?")
    public void redisNewGuideClickToDatabase() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        redisDistributeLock.lock("newGuideClickDataSync", 3, TimeUnit.MINUTES, (a) -> {
            Map<Object, Object> map = redisService.hGetAll(BaseConstant.NEWBIE_GUIDE);
            if (CollUtil.isEmpty(map)) {
                return;
            }
            List<AsNewbieGuide> newbieGuides = new ArrayList<>(map.size());
            map.forEach((k, v) -> {
                Long guideId = Convert.toLong(k, 0L);
                Long click = Convert.toLong(v, 0L);
                if (guideId != 0L && click != 0L) {
                    newbieGuides.add(new AsNewbieGuide().setId(guideId).setClicks(click));
                }
            });
            newbieGuideService.updateBatchById(newbieGuides);
        });
    }

}
