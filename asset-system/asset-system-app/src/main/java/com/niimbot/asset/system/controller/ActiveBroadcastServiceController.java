package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.event.EventType;
import com.niimbot.asset.system.event.PageEventQueue;
import com.niimbot.asset.system.model.AsActiveBroadcast;
import com.niimbot.asset.system.model.AsModuleStatistics;
import com.niimbot.asset.system.service.ActiveBroadcastService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/4/11 下午3:23
 */
@Slf4j
@RestController
@RequestMapping("/server/system/broadcast/")
@RequiredArgsConstructor
public class ActiveBroadcastServiceController {

    @Autowired
    private ActiveBroadcastService activeBroadcastService;

    /**
     * 活动广播列表
     * @return
     */
    @GetMapping("list")
    public List<AsActiveBroadcast> list() {
        return activeBroadcastService.list(Wrappers.lambdaQuery(AsActiveBroadcast.class)
                .eq(AsActiveBroadcast::getStatus, 1).orderByDesc(AsActiveBroadcast::getCreateTime));
    }

    /**
     * 活动广播点击事件统计
     * @return
     */
    @PostMapping("broadcastAction")
    public Boolean broadcastAction(@RequestBody Long broadcastId) {
        AsActiveBroadcast activeBroadcast = activeBroadcastService.getById(broadcastId);
        if (Objects.isNull(activeBroadcast)) {
            return Boolean.TRUE;
        } else {
            AsModuleStatistics moduleStatistics = new AsModuleStatistics();
            moduleStatistics.setCompanyId(LoginUserThreadLocal.getCompanyId());
            moduleStatistics.setUserId(LoginUserThreadLocal.getCurrentUserId());
            moduleStatistics.setEventId(EventType.ACTIVE_BROADCAST);
            moduleStatistics.setEventName("首页活动广播");
            moduleStatistics.setBizName(activeBroadcast.getName());
            moduleStatistics.setEventAction(EventType.EventAction.CLICK);
            moduleStatistics.setCreateTime(LocalDateTime.now());

            //生成事件
            PageEventQueue.getInstance().producePageEvent(moduleStatistics);
            return Boolean.TRUE;
        }
    }
}
