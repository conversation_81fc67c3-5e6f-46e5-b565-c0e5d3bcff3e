package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.FileUtils;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.system.mapper.AsFileMapper;
import com.niimbot.asset.system.model.AsFile;
import com.niimbot.asset.system.service.AsFileService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AsFileDownDto;
import com.niimbot.system.FileParamDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件附件service
 *
 * <AUTHOR>
 * @Date 2020/11/30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AsFileServiceImpl extends ServiceImpl<AsFileMapper, AsFile> implements AsFileService {

    private final FileUploadConfig config;

    @Override
    public List<AsFile> uploadFile(List<MultipartFile> fileList, FileParamDto fileParamDto)
            throws IOException {
        FileUtils.uploadCheck(fileList, fileParamDto.getFileName());
        boolean isTemp = fileParamDto.getIsTemp();
        String module = fileParamDto.getModule();
        String business = fileParamDto.getBusiness();
        String moduleUser = (StrUtil.isNotBlank(module) ? (File.separator + module) : "")
                + (StrUtil.isNotBlank(business) ? (File.separator + business) : "");
        String filePath = isTemp ? config.getTempPath() : config.getPath() + moduleUser;
        List<AsFile> list = Lists.newArrayList();

        for (MultipartFile file : fileList) {
            Long idKey = IdUtils.getId();
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            if (StrUtil.isBlank(extension)) {
                extension = FilenameUtils.getExtension(fileParamDto.getFileName());
            }
            String persistFileName = FileUtils.persistFileName(file, extension);
            File absoluteFile = FileUtils.getAbsoluteFile(filePath, persistFileName);
            FileUtil.writeFromStream(file.getInputStream(), absoluteFile);
            filePath = absoluteFile.getPath();
            AsFile asFile = new AsFile().setId(idKey)
                    .setOriginalName(file.getOriginalFilename())
                    .setPersistName(persistFileName).setFileType(extension)
                    .setFileSize(file.getSize()).setFilePath(filePath)
                    .setOptType(fileParamDto.getOptType());
            list.add(asFile);
        }
        // 存库
        if (!isTemp) {
            saveBatch(list);
        }
        return list;
    }

    @Override
    public AsFileDownDto feignFileDownload(Long id, Boolean delete) {
        AsFile byId = this.getById(id);
        if (null == byId) {
            throw new BusinessException(SystemResultCode.FILE_DOWNLOAD_NOT_FOUND);
        }
        AsFileDownDto downDto = BeanUtil.copyProperties(byId, AsFileDownDto.class);
        try (InputStream inputStream = new FileInputStream(byId.getFilePath())) {
            byte[] read = IoUtil.readBytes(inputStream);
            downDto.setContent(read);
            // 文件如果删除、逻辑删除
            if (ObjectUtil.isNotNull(delete) && delete.booleanValue()) {
                this.removeById(id);
            }
        } catch (FileNotFoundException e) {
            throw new BusinessException(SystemResultCode.FILE_DOWNLOAD_NOT_FOUND);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return downDto;
    }

    @Override
    public byte[] feignFileDownloadBatch(List<Long> ids) {
        if (CollUtil.isNotEmpty(ids) && ObjectUtil.isNotNull(config.getMaxDownLoadCountSize()) &&
                ids.size() > config.getMaxDownLoadCountSize()) {
            throw new BusinessException(SystemResultCode.FILE_DOWNLOAD_MULTI_EXCEED);
        }
        List<AsFile> files = super.listByIds(ids);
        try {
            File tempZipFile = zipFiles(files, config.getTempPath(),
                    FileUtils.getFileNameWithNow("zip", "yyyyMMdd_SSS"));
            byte[] bytes = IoUtil.readBytes(new FileInputStream(tempZipFile));
            FileUtils.deleteFile(tempZipFile.getPath());
            return bytes;
        } catch (IOException e) {
            throw new BusinessException(SystemResultCode.FILE_DOWNLOAD_NOT_FOUND);
        }
    }

    @Override
    public List<AsFileDownDto> listByIds(List<Long> ids) {
        List<AsFile> files = super.listByIds(ids);
        return files.stream().map(asFile -> {
            AsFileDownDto fileDownDto = BeanUtil.copyProperties(asFile, AsFileDownDto.class);
            try {
                InputStream sourceInput = new FileInputStream(fileDownDto.getFilePath());
                byte[] bytes = IoUtil.readBytes(sourceInput);
                fileDownDto.setContent(bytes);
            } catch (FileNotFoundException e) {
                fileDownDto.setContent(new byte[]{});
                log.error("file :{} not found", fileDownDto.getFilePath());
            }
            return fileDownDto;
        }).collect(Collectors.toList());
    }


    /**
     * 批量文件生成zip文件
     *
     * @param asFileList 文件列表
     * @param zipPath    临时文件目录
     * @param zipName    临时文件名称
     * @return 临时文件
     * @throws IOException io异常
     */
    private File zipFiles(List<AsFile> asFileList, String zipPath, String zipName) throws IOException {
        // 如果被压缩文件中有重复，会重命名
        File zipPathFile = new File(zipPath);
        // 文件夹不存在则创建
        if (!zipPathFile.exists()) {
            zipPathFile.mkdirs();
        }
        File zipFile = new File(zipPath + File.separator + zipName);
        if (!zipFile.exists() && !zipFile.createNewFile()) {
            throw new BusinessException(HttpStatus.OK.value(), "压缩文件不存在");
        }
        BufferedInputStream bis = null;
        ZipEntry zipEntry;
        try ( // 存放的目标文件
              ZipOutputStream zos =
                      new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(zipFile.getPath())))) {
            for (AsFile file : asFileList) {
                // 创建ZIP实体，并添加进压缩包,指定压缩文件中的文件名
                zipEntry = new ZipEntry(file.getOriginalName());
                zos.putNextEntry(zipEntry);
                // 读取待压缩的文件并写进压缩包里
                bis = new BufferedInputStream(new FileInputStream(file.getFilePath()), 1024 * 10);
                byte[] buf = new byte[1024 * 10];
                int read;
                while ((read = (bis.read(buf, 0, 1024 * 10))) != -1) {
                    zos.write(buf, 0, read);
                }
                bis.close();
            }
        } catch (Exception e) {
            log.error("create zip file error:{}", e.getMessage());
        } finally {
            // 关闭流
            if (bis != null) {
                bis.close();
            }
        }
        return zipFile;
    }
}
