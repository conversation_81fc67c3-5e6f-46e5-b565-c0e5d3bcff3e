package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.mapper.AsToolboxMapper;
import com.niimbot.asset.system.model.AsToolbox;
import com.niimbot.asset.system.service.AsToolboxService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.ToolboxGroupItemDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * 工具箱service
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Service
@RequiredArgsConstructor
public class AsToolboxServiceImpl extends ServiceImpl<AsToolboxMapper, AsToolbox> implements AsToolboxService {

//    private final CusUserRoleService userRoleService;
//    private final AsRoleMenuService roleMenuService;
//    private final CusMenuService cusMenuService;

    @Override
    public List<AsToolbox> getAppToolBox() {
        return this.getBaseMapper().selectToolbox(2);
    }

    @Override
    public List<AsToolbox> getPcToolBox() {
        return this.getBaseMapper().selectToolbox(1);
    }

    @Override
    public List<AsToolbox> getAllToolbox() {
        return this.getBaseMapper().selectToolbox(3);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sort(List<AsToolbox> list) {
        int sort = getBaseMapper().sort(list);
        if (sort != list.size()) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @Override
    public List<ToolboxGroupItemDto> getWorkbench(Integer businessType) {
        return this.getBaseMapper().selectWorkbench(businessType);
    }
}