package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsActiveBroadcastMapper;
import com.niimbot.asset.system.model.AsActiveBroadcast;
import com.niimbot.asset.system.service.ActiveBroadcastService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/4/11 下午2:20
 */
@Slf4j
@Service
public class ActiveBroadcastServiceImpl extends ServiceImpl<AsActiveBroadcastMapper, AsActiveBroadcast> implements ActiveBroadcastService {
}
