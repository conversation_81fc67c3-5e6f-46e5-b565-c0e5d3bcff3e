package com.niimbot.asset.system.service.handler;

import java.util.List;

/**
 * 微信接口，实现在WeixinAdapter里面
 *
 * <AUTHOR>
 * @date 2023/7/24 11:11
 */
public interface WeixinAdapter {

    /**
     * 通讯录员工搜索
     *
     * @param queryWord
     * @return
     */
    List<String> empSearch(String queryWord);

    /**
     * 查询员工
     * @param queryWord
     * @param fullMatch 是否精确匹配
     * @return
     */
    List<String> empSearch(String queryWord, Boolean fullMatch);

    /**
     * 通讯录组织搜索
     *
     * @param
     * @return
     */
    List<String> orgSearch(String queryWord);

    List<String> orgNameMatch(String orgName, Long companyId);

}
