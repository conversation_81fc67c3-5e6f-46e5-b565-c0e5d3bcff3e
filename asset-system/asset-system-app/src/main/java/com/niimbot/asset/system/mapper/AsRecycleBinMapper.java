package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsRecycleBin;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.GetRecycleBins;
import com.niimbot.system.RecycleBin;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 */
public interface AsRecycleBinMapper extends BaseMapper<AsRecycleBin> {

    void updateTableIsDelete(@Param("tableName") String tableName, @Param("resId") Long resId, @Param("isDelete") Integer isDelete);

    void updateBatchTableIsDelete(@Param("tableName") String tableName, @Param("resIds") List<Long> resIds, @Param("isDelete") Integer isDelete);

    List<Long> selectResIds(@Param("em") GetRecycleBins get);

    Boolean selectResIsDelete(@Param("tableName") String tableName, @Param("resId") Long resId);

    List<Long> selectResIdsFrom(@Param("tableName") String tableName, @Param("resIds") List<Long> resIds);

    RecycleBin.ResData selectResData(@Param("tableName") String tableName, @Param("columnName") String columnName, @Param("resId") Long resId);

    default List<AsRecycleBin> selectByResIdsOrElseThrow(Long companyId, List<Long> ids, Integer resType, Boolean isThrow) {
        List<AsRecycleBin> list = this.selectList(
                Wrappers.lambdaQuery(AsRecycleBin.class)
                        .eq(AsRecycleBin::getCompanyId, companyId)
                        .eq(AsRecycleBin::getResType, resType)
                        .in(AsRecycleBin::getResId, ids)
        );
        if (CollUtil.isEmpty(list) && isThrow) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前数据集未回收过");
        }
        return list;
    }

    default Boolean deleteByResIds(Long companyId, List<Long> ids, Integer resType) {
        return this.delete(
                Wrappers.lambdaUpdate(AsRecycleBin.class)
                        .eq(AsRecycleBin::getCompanyId, companyId)
                        .eq(AsRecycleBin::getResType, resType)
                        .in(AsRecycleBin::getResId, ids)
        ) > 0;
    }

}
