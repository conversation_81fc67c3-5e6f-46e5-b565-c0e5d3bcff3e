package com.niimbot.asset.system.schedule;

import cn.hutool.extra.spring.SpringUtil;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.system.service.CommandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class HomeStatScheduler {

    @Resource
    private CommandService commandService;
    @Autowired
    private RedisDistributeLock redisDistributeLock;

    @Scheduled(cron = "0 30 0 * * ?")
    public void schedule() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return ;
        }

        redisDistributeLock.lock("homeStat", 3, TimeUnit.MINUTES, (a) -> {
            commandService.statisticsUserNumByDay(null);
            commandService.statisticsAssetAddNumByDay(null);
        });
    }

}
