package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.enums.ContentTypeEnum;
import com.niimbot.asset.system.mapper.AsIndustryCaseConfigMapper;
import com.niimbot.asset.system.model.AsIndustryCaseConfig;
import com.niimbot.asset.system.service.IndustryCaseConfigService;
import com.niimbot.system.IndustryCaseConfigDto;
import com.niimbot.system.IndustryCaseDetailDto;
import com.niimbot.system.IndustryCaseQueryDto;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午5:24
 */
@Slf4j
@Service
public class IndustryCaseConfigServiceImpl extends ServiceImpl<AsIndustryCaseConfigMapper, AsIndustryCaseConfig> implements IndustryCaseConfigService {

    @Override
    public PageUtils<IndustryCaseConfigDto> pageQuery(IndustryCaseQueryDto queryDto) {
        IPage<AsIndustryCaseConfig> pageResult = this.getBaseMapper().pageQuery(queryDto.buildIPage(), queryDto);
        if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageUtils<>(null, 0, (int) queryDto.getPageSize(), (int) queryDto.getPageNum());
        }

        List<IndustryCaseConfigDto> dataList = new ArrayList<>();
        for (AsIndustryCaseConfig item : pageResult.getRecords()) {
            IndustryCaseConfigDto data = new IndustryCaseConfigDto();
            BeanUtils.copyProperties(item, data);
            if (StrUtil.isNotBlank(item.getImages())) {
                data.setImages(Arrays.asList(item.getImages().split(",")));
            }
            //第三方链接需要返回给前端，富文本内容太多，不进行返回
            if (!ContentTypeEnum.LINK.getCode().equals(item.getContentType())) {
                data.setContent(null);
            }

            data.setContentTypeDesc(ContentTypeEnum.getByCode(item.getContentType()).getDesc());
            dataList.add(data);
        }
        return new PageUtils<>(dataList, (int) pageResult.getTotal(), (int) pageResult.getSize(), (int) pageResult.getCurrent());
    }

    @Override
    public IndustryCaseDetailDto queryDetail(Long configId) {
        if (Objects.isNull(configId)) {
            return null;
        }

        AsIndustryCaseConfig solutionConfig = this.getById(configId);
        if (Objects.isNull(solutionConfig)) {
            return null;
        }

        //浏览次数++
        this.getBaseMapper().increaseView(configId);

        IndustryCaseDetailDto result = new IndustryCaseDetailDto();
        IndustryCaseConfigDto currentConfig = new IndustryCaseConfigDto();
        BeanUtils.copyProperties(solutionConfig, currentConfig);
        if (StrUtil.isNotBlank(solutionConfig.getImages())) {
            currentConfig.setImages(Arrays.asList(solutionConfig.getImages().split(",")));
        }
        result.setCurrentConfig(currentConfig);
        result.setPreviousConfig(this.getBaseMapper().selectPrevious(configId));
        result.setNextConfig(this.getBaseMapper().selectNext(configId));
        return result;
    }
}
