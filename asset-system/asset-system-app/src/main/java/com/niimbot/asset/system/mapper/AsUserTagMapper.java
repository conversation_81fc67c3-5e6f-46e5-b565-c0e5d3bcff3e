package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsUserTag;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.UserTagDto;
import com.niimbot.system.UserTagPrintDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/17 10:16
 */
@EnableDataPerm(excludeMethodName = {"getCountByCompanyId", "getOneById", "queryTagBySizeId",
        "getFirstTagId", "getDetail", "checkTagName", "getDistinctBySizeId", "getFirstUserTagId", "selectAllCompanyTags"})
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsUserTagMapper.class)
public interface AsUserTagMapper extends BaseMapper<AsUserTag> {
    /**
     * 通过企业id查询模板数量
     *
     * @param companyId 企业id
     * @return 数量
     */
    @Select("select count(id) from as_user_tag where company_id = #{companyId} and tag_type = 2")
    int getCountByCompanyId(@Param("companyId") Long companyId);

    /**
     * 根据尺寸id查询标签信息
     *
     * @param sizeId 尺寸
     * @return 标签信息
     */
    List<UserTagDto> queryTagBySizeId(@Param("companyId") Long companyId,
                                      @Param("sizeId") Long sizeId,
                                      @Param("tagType") Integer tagType,
                                      @Param("kw") String kw,
                                      @Param("printType") Short printType,
                                      @Param("printerId") Long printerId);

    /**
     * 获取最早一条有效的标签
     *
     * @return 标签信息
     */
    @Select("SELECT t2.id FROM as_printer_tags t1 left join as_user_tag t2 on t1.tag_id = t2.id WHERE t1.printer_id = #{printerId} and t2.company_id = 0 and t2.tag_type = 1 and t2.print_type = #{printType} and t2.is_delete = 0 ORDER BY t2.id ASC LIMIT 1")
    Long getFirstTagId(@Param("printType") Short printType, @Param("printerId") Long printerId);

    /**
     * 获取最早一条用户自定义的有效的标签
     *
     * @return 标签信息
     */
    @Select("select t2.id from as_printer_tags t1 left join as_user_tag t2 on t1.tag_id = t2.id where t1.printer_id = #{printerId} and t2.company_id = #{companyId} and t2.print_type = #{printType} and t2.is_delete = 0 order by t2.id asc limit 1")
    Long getFirstUserTagId(@Param("companyId") Long companyId, @Param("printType") Short printType, @Param("printerId") Long printerId);

    /**
     * 通过标签id查询标签数据
     *
     * @param tagId     标签id
     * @param printType 打印类型
     * @return 数量
     */
    AsUserTag getOneById(@Param("tagId") Long tagId, @Param("printType") Short printType);

    /**
     * 通过标签id查询标签详情
     *
     * @param tagId 标签id
     * @return 标签详情
     */
    UserTagPrintDto getDetail(@Param("tagId") Long tagId);

    /**
     * 根据尺寸id查询标签信息
     *
     * @param type 标签类型
     * @param size 尺寸类型
     * @return 标签信息
     */
//    List<UserTagDto> queryTagBySizeAndType(@Param("type") Integer type,
//                                           @Param("size") Integer size,
//                                           @Param("companyId") Long companyId);

    /**
     * 获取标签数据
     *
     * @param tagName   标签名称
     * @param tagId     标签id
     * @param companyId 公司id
     * @return 标签信息
     */
    List<AsUserTag> checkTagName(@Param("tagName") String tagName, @Param("tagId") Long tagId, @Param("companyId") Long companyId);

    /**
     * 通过sizeId获取去去重的标签模板
     *
     * @param printType 打印类型
     * @param sizeId    尺寸Id
     * @return 标签信息
     */
    List<UserTagDto> getDistinctBySizeId(@Param("printType") Short printType, @Param("sizeId") Long sizeId, @Param("printerId") Long printerId);

    List<AsUserTag> selectAllCompanyTags(@Param("companyId") Long companyId, @Param("printType") Short printType, @Param("sizeId") Long sizeId);

}
