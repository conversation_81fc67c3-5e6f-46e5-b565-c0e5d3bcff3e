package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.*;
import com.niimbot.asset.system.dto.*;
import com.niimbot.asset.system.dto.clientobject.AdminPrinterCO;
import com.niimbot.asset.system.dto.clientobject.AdminPrinterConcentrationCO;
import com.niimbot.asset.system.dto.clientobject.TagAttrCO;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import com.niimbot.asset.system.mapper.AsPrinterTagsMapper;
import com.niimbot.asset.system.mapper.AsTagSizeMapper;
import com.niimbot.asset.system.mapper.AsUserTagMapper;
import com.niimbot.asset.system.model.AsPrinterTags;
import com.niimbot.asset.system.model.AsTagSize;
import com.niimbot.asset.system.model.AsUserTag;
import com.niimbot.asset.system.service.AsUserTagService;
import com.niimbot.asset.system.service.TagService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.PrintDataSetTagDto;
import com.niimbot.system.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @Date 2020/12/11
 */
@Service
@Slf4j
public class TagServiceImpl extends ServiceImpl<AsUserTagMapper, AsUserTag> implements TagService {

    @Resource
    private AsTagSizeMapper tagSizeMapper;

    @Resource
    private AsUserTagService userTagService;

    @Resource
    private AssetAbs assetAbs;

    @Resource
    private MaintainAbs maintainAbs;

    @Resource
    private StandardAbs standardAbs;

    @Resource
    private FormAbs formAbs;

    @Resource
    private AdminPrinterAbs adminPrinterAbs;

    @Resource
    private AdminPrinterConcentrationAbs adminPrinterConcentrationAbs;

    @Resource
    private AsPrinterTagsMapper printerTagsMapper;

    /**
     * 通过sizeId
     *
     * @param sizeId 尺寸Id
     * @return 标签信息
     */
    @Override
    public UserTagResDto getBySizeId(Short printType, Long sizeId, Integer tagType, String kw, String printerName) {
        // 校验该标签尺寸是否存在
        AsTagSize asTagSize = tagSizeMapper.selectById(sizeId);
        if (ObjectUtil.isEmpty(asTagSize)) {
            sizeId = 1L;
        }

        // if (StrUtil.isNotBlank(printerName)) {
        // 获取设备id
        AdminPrinterCO adminPrinter = adminPrinterAbs.getAdminPrinter(
                new AdminPrinterGetQry().setModel(printerName));
        if (null == adminPrinter) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }
        Long id = adminPrinter.getId();

        // 获取最早的一条有效的模板ID
        Long defaultTagId = userTagService.getFirstTagId(printType, id);

        if (Objects.isNull(defaultTagId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印配置中打印机型不支持该标签");
        }

        // 获取用户当前机型下对应的默认模板
        AdminPrinterConcentrationCO adminPrinterConcentration = adminPrinterConcentrationAbs.getAdminPrinterConcentration(
                new AdminPrinterConcentrationGetQry()
                        .setPrinterId(id)
                        .setUserId(LoginUserThreadLocal.getCurrentUserId())
                        .setIsDefault(true));

        if (adminPrinterConcentration != null) {
            if (DictConstant.PRINT_TYPE_ASSET == printType) {
                UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultTagId());
                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                    defaultTagId = tagPrintDto.getId();
                }
            } else if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
                UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultCftagId());
                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                    defaultTagId = tagPrintDto.getId();
                }
            }
        }
        // }

        // 获取标签列表
        // 过滤这个机型支持的打印标签
        List<UserTagDto> list = userTagService.queryTagBySizeId(LoginUserThreadLocal.getCompanyId(), sizeId, tagType, kw, printType, id);

        if (CollUtil.isNotEmpty(list)) {
            for (UserTagDto userTagDto : list) {
                //加上是否默认选中 字段
                userTagDto.setSelected((userTagDto.getId().equals(defaultTagId)) ? 1 : 0);
            }
        }
        // 2022-10-25 增加 7634 尺寸不支持佐藤系列
        if ("佐藤CL4NX".equals(printerName)) {
            list.removeIf(v -> v.getId().equals(48L));
        }

        // 封装返回数据
        UserTagResDto userTagResDto = new UserTagResDto();
        userTagResDto.setGroupData(list);
        userTagResDto.setTagSelected(defaultTagId);
        return userTagResDto;
    }

    /**
     * 获取尺寸列表
     *
     * @return 尺寸信息
     */
    @Override
    public SizeResDto getSizeList(Short printType, List<Long> printerIds, String printerName) {
        if (printerIds == null) {
            printerIds = Lists.newArrayList();
        }
        if (StrUtil.isNotBlank(printerName)) {
            AdminPrinterCO one = adminPrinterAbs.getAdminPrinter(
                    new AdminPrinterGetQry().setModel(printerName));
            if (one != null) {
                printerIds.add(one.getId());
            }
        }
        if (CollUtil.isEmpty(printerIds)) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_NOT_EXIST);
        }
        // 获取标签尺寸数据
        List<AsTagSize> tagSizeData = tagSizeMapper.selectSizeByPrinterIds(printerIds);

        // 当前公司id
        Long companyId = LoginUserThreadLocal.getCompanyId();

        // 根据printType缩小tagSize范围
        tagSizeData = tagSizeData.stream().filter(asTagSize -> {
            List<AsUserTag> tags = userTagService.companyTagList(companyId, printType, asTagSize.getId());
            return CollUtil.isNotEmpty(tags);
        }).collect(toList());

        if (CollUtil.isEmpty(tagSizeData)) {
            throw new BusinessException(SystemResultCode.TAG_SIZE_NOT_EXISTS);
        }

        List<SizeDto> list = Lists.newArrayList();
        List<String> tagSizeList = Lists.newArrayList();
        for (AsTagSize tagSize : tagSizeData) {
            // 尺寸对象
            SizeDto sizeDto = new SizeDto();
            // 复制属性
            BeanUtil.copyProperties(tagSize, sizeDto);

            String tagSizeName = tagSize.getSizeWide() + "*" + tagSize.getSizeLong();
            if (!tagSizeList.contains(tagSizeName)) {
                tagSizeList.add(tagSizeName);

                // 添加到尺寸列表中
                sizeDto.setTagSize(tagSizeName);
                list.add(sizeDto);
            }
        }

        AsUserTag asUserTag = null;
        // 获取默认标签模板id
        if (StrUtil.isNotBlank(printerName)) {
            // 获取设备id
            AdminPrinterCO adminPrinter = adminPrinterAbs.getAdminPrinter(
                    new AdminPrinterGetQry().setModel(printerName));
            Long printerId = adminPrinter.getId();

            // 获取用户当前机型下对应的默认模板
            AdminPrinterConcentrationCO adminPrinterConcentration = adminPrinterConcentrationAbs.getAdminPrinterConcentration(
                    new AdminPrinterConcentrationGetQry()
                            .setPrinterId(printerId)
                            .setUserId(LoginUserThreadLocal.getCurrentUserId())
                            .setIsDefault(true));
            Long defaultTagId = null;
            if (adminPrinterConcentration != null) {
                if (DictConstant.PRINT_TYPE_ASSET == printType) {
                    UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultTagId());
                    if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                        defaultTagId = tagPrintDto.getId();
                    }
                } else if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
                    UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultCftagId());
                    if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                        defaultTagId = tagPrintDto.getId();
                    }
                }

                if (ObjectUtil.isNotNull(defaultTagId)) {
                    asUserTag = userTagService.getOneById(defaultTagId, printType);
                }
            }
        }
        // 获取用户自定义的标签数
        int userTagCount = userTagService.getCountByCompanyId(companyId);

        // 封装返回数据
        SizeResDto sizeResDto = new SizeResDto();
        sizeResDto.setGroupData(list);
        sizeResDto.setCustomNum(userTagCount);
        sizeResDto.setSizeSelected(asUserTag != null ? asUserTag.getSizePid() : 1L);
        return sizeResDto;
    }

    /**
     * 获取app尺寸列表
     *
     * @return 尺寸信息
     */
    @Override
    public List<SizeDto> appSizeList(String printerName) {
        List<Long> printerIds = Lists.newArrayList();
        if (StrUtil.isNotBlank(printerName)) {
            AdminPrinterCO printer = adminPrinterAbs.getAdminPrinter(
                    new AdminPrinterGetQry().setModel(printerName));
            if (printer == null) {
                throw new BusinessException(SystemResultCode.PRINT_PRINTER_NOT_EXIST);
            }
            if (!printer.getIsApplyApp()) {
                throw new BusinessException(SystemResultCode.PRINTER_NO_APPLY_APP);
            }

            printerIds.add(printer.getId());
        } else {
            // 获取适用于app的打印机
            List<AdminPrinterCO> printerList = adminPrinterAbs.listAdminPrinter(
                    new AdminPrinterListQry().setIsApplyApp(true));
            printerIds = printerList.stream().map(AdminPrinterCO::getId).collect(toList());
        }

        // 获取标签尺寸数据
        List<AsTagSize> tagSizeData = tagSizeMapper.selectSizeByPrinterIds(printerIds);
        if (CollUtil.isEmpty(tagSizeData)) {
            throw new BusinessException(SystemResultCode.TAG_SIZE_NOT_EXISTS);
        }

        List<SizeDto> list = Lists.newArrayList();
        List<String> tagSizeList = Lists.newArrayList();
        for (AsTagSize tagSize : tagSizeData) {
            if (!tagSize.getApplyApp()) {
                continue;
            }
            // 尺寸对象
            SizeDto sizeDto = new SizeDto();
            // 复制属性
            BeanUtil.copyProperties(tagSize, sizeDto);
            sizeDto.setTagSize(tagSize.getSizeWide() + "×" + tagSize.getSizeLong());
            if (!tagSizeList.contains(sizeDto.getTagSize())) {
                tagSizeList.add(sizeDto.getTagSize());

                // 添加到尺寸列表中
                sizeDto.setTagSize(sizeDto.getTagSize());
                list.add(sizeDto);
            }
        }

        return list;
    }

//    /**
//     * 设置默认标签模板
//     *
//     * @param tagId 标签Id
//     * @return Boolean
//     */
//    @Override
//    public Boolean setDefaultTag(Long tagId) {
//        // 查询标签模板
//        AsUserTag asUserTag = userTagService.getOneById(tagId, DictConstant.PRINT_TYPE_ASSET);
//        if (ObjectUtil.isNull(asUserTag)) {
//            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
//        }
//
//        // 修改用户默认标签模板
//        AsCusUserExt asCusUserExt = new AsCusUserExt();
//        asCusUserExt.setId(LoginUserThreadLocal.getCurrentUserId());
//        asCusUserExt.setDefaultTagId(tagId);
//        if (!cusUserExtService.saveOrUpdate(asCusUserExt)) {
//            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
//        }
//        return true;
//    }

    /**
     * 设置默认标签模板
     *
     * @param printDataSetTagDto 设置默认标签dto
     * @return Boolean
     */
    @Override
    public Boolean setDefaultTag(PrintDataSetTagDto printDataSetTagDto) {
        // 查询标签模板
        Long tagId = printDataSetTagDto.getTagId();
        AsUserTag asUserTag = userTagService.getOneById(tagId, DictConstant.PRINT_TYPE_ASSET);
        if (ObjectUtil.isNull(asUserTag)) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }

        // 获取设备id
        AdminPrinterCO adminPrinter = adminPrinterAbs.getAdminPrinter(
                new AdminPrinterGetQry().setModel(printDataSetTagDto.getPrinterName()));
        if (ObjectUtil.isNull(adminPrinter)) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }
        Long printerId = adminPrinter.getId();

        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 设备-材质-浓度关联表查询
        AdminPrinterConcentrationCO printerConcentration = adminPrinterConcentrationAbs.getAdminPrinterConcentration(
                new AdminPrinterConcentrationGetQry()
                        .setPrinterId(printerId)
                        .setUserId(currentUserId)
                        .setIsDefault(true));
        AdminPrinterConcentrationCO printerConcentrationNew = Optional.ofNullable(printerConcentration)
                .orElse(new AdminPrinterConcentrationCO().setUserId(currentUserId).setPrinterId(printerId).setIsDefault(true));

        // 设置默认标签
        printerConcentrationNew.setDefaultTagId(tagId);
        if (!adminPrinterConcentrationAbs.saveOrUpdateAdminPrinterConcentration(
                new AdminPrinterConcentrationSaveOrUpdateCmd()
                        .setAdminPrinterConcentration(printerConcentrationNew))) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        return true;
    }

//    @Override
//    public Boolean setDefaultCftag(Long tagId) {
//        // 查询标签模板
//        AsUserTag asUserTag = userTagService.getOneById(tagId, DictConstant.PRINT_TYPE_MATERIAL);
//        if (ObjectUtil.isNull(asUserTag)) {
//            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
//        }
//
//
//        // 修改用户默认标签模板
//        AsCusUserExt asCusUserExt = new AsCusUserExt();
//        asCusUserExt.setId(LoginUserThreadLocal.getCurrentUserId());
//        asCusUserExt.setDefaultCftagId(tagId);
//        if (!cusUserExtService.saveOrUpdate(asCusUserExt)) {
//            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
//        }
//        return true;
//    }

    @Override
    public Boolean setDefaultCftag(PrintDataSetTagDto printDataSetTagDto) {
        // 查询标签模板
        Long tagId = printDataSetTagDto.getTagId();
        AsUserTag asUserTag = userTagService.getOneById(tagId, DictConstant.PRINT_TYPE_MATERIAL);
        if (ObjectUtil.isNull(asUserTag)) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }

        // 获取设备id
        AdminPrinterCO adminPrinter = adminPrinterAbs.getAdminPrinter(
                new AdminPrinterGetQry().setModel(printDataSetTagDto.getPrinterName()));
        if (ObjectUtil.isNull(adminPrinter)) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }
        Long printerId = adminPrinter.getId();

        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 设备-材质-浓度关联表查询
        AdminPrinterConcentrationCO printerConcentration = adminPrinterConcentrationAbs.getAdminPrinterConcentration(
                new AdminPrinterConcentrationGetQry()
                        .setPrinterId(printerId)
                        .setUserId(currentUserId)
                        .setIsDefault(true));
        AdminPrinterConcentrationCO printerConcentrationNew = Optional.ofNullable(printerConcentration)
                .orElse(new AdminPrinterConcentrationCO().setUserId(currentUserId).setPrinterId(printerId).setIsDefault(true));

        // 设置默认标签
        printerConcentrationNew.setDefaultCftagId(tagId);
        if (!adminPrinterConcentrationAbs.saveOrUpdateAdminPrinterConcentration(
                new AdminPrinterConcentrationSaveOrUpdateCmd()
                        .setAdminPrinterConcentration(printerConcentrationNew))) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        return true;
    }

    /**
     * 保存用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveUserTag(UserTagSaveDto userTagSaveDto) {
        // 校验该标签尺寸是否存在
        AsTagSize asTagSize = tagSizeMapper.selectById(userTagSaveDto.getSizeId());
        if (ObjectUtil.isEmpty(asTagSize)) {
            throw new BusinessException(SystemResultCode.TAG_SIZE_NOT_EXISTS);
        }

        //判断属性数量是否大于最大属性数量
        int size = userTagSaveDto.getAttrData().size();
        // 获取标签的max_attr_num
        Integer maxAttrNum = asTagSize.getMaxAttrNum();
        if (size > maxAttrNum) {
            throw new BusinessException(SystemResultCode.TAG_ATTR_MAX_LIMIT, String.valueOf(maxAttrNum));
        }

        // 获取所有的标签属性数据
        TagAttrListCO tagAttrListCO;
        switch (userTagSaveDto.getPrintType()) {
            case DictConstant.PRINT_TYPE_ASSET:
                tagAttrListCO = assetAbs.getAttrList(new TagAttrListQry());
                break;
            case DictConstant.PRINT_TYPE_MATERIAL:
                tagAttrListCO = maintainAbs.getAttrList(new TagAttrListQry());
                break;
            default:
                throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        List<String> attrCollect = tagAttrListCO.getTagAttrs().stream().map(TagAttrCO::getAttrCode).collect(Collectors.toList());

        // 扩展属性
        List<FormFieldCO> standardExtField = standardAbs.getStandardExtField(
                new StandardExtFieldListQry(tagAttrListCO.getFormId(), userTagSaveDto.getStandardId()));
        standardExtField.forEach(it -> attrCollect.add(it.getFieldCode()));

        // 标签属性
        List<TagAttrDto> attrData = userTagSaveDto.getAttrData();
        // 标签属性判断
        if (CollUtil.isNotEmpty(attrData)) {
            for (TagAttrDto attr : attrData) {
                if (!attrCollect.contains(attr.getAttrCode())) {
                    throw new BusinessException(SystemResultCode.TAG_ATTR_NOT_EXISTS, attr.getAttrName());
                }

                if (StrUtil.isNotBlank(attr.getAlias()) &&
                        StrUtil.length(attr.getAlias()) > AssetConstant.TAG_ATTR_LENGTH_LIMIT) {
                    throw new BusinessException(SystemResultCode.TAG_ATTR_LENGTH_LIMIT, attr.getAlias(), String.valueOf(AssetConstant.TAG_ATTR_LENGTH_LIMIT));
                }
            }
        }

        // 新增用户标签数据
        AsUserTag asUserTag = new AsUserTag();
        BeanUtil.copyProperties(userTagSaveDto, asUserTag);
        asUserTag.setTagType(AssetConstant.TAG_TYPE_CUSTOM);
        asUserTag.setId(IdUtils.getId());
        asUserTag.setBackgroundImage(userTagSaveDto.getTagBackgroundImage());
        if (!this.save(asUserTag)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }

        List<AsPrinterTags> tags = printerTagsMapper.selectList(
                Wrappers.lambdaQuery(AsPrinterTags.class)
                        .eq(AsPrinterTags::getTagId, userTagSaveDto.getSourceTagId())
        );
        // 打印设备与标签
        if (CollUtil.isNotEmpty(tags)) {
            List<AsPrinterTags> list = tags.stream().map(v -> new AsPrinterTags().setPrinterId(v.getPrinterId()).setTagId(asUserTag.getId())).collect(toList());
            list.forEach(printerTagsMapper::insert);
        } else {
            printerTagsMapper.insert(new AsPrinterTags().setPrinterId(userTagSaveDto.getPrinterId()).setTagId(asUserTag.getId()));
        }

        return true;
    }

    /**
     * 获取移动端标签列表
     *
     * @param sizeId  尺寸类型
     * @param tagType 标签类型
     * @param kw      关键字
     * @return 移动端标签列表
     */
    @Override
    public UserTagResDto appTagList(Short printType, Long sizeId, Integer tagType, String kw, String printerName) {
        // 获取设备id
        AdminPrinterCO adminPrinter = adminPrinterAbs.getAdminPrinter(
                new AdminPrinterGetQry().setModel(printerName));
        if (null == adminPrinter) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }
        Long id = adminPrinter.getId();

        Long defaultTagId = userTagService.getFirstTagId(printType, id);

        if (Objects.isNull(defaultTagId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印配置中打印机型不支持该标签");
        }

        // 获取用户当前机型下对应的默认模板
        AdminPrinterConcentrationCO adminPrinterConcentration = adminPrinterConcentrationAbs.getAdminPrinterConcentration(
                new AdminPrinterConcentrationGetQry()
                        .setPrinterId(id)
                        .setUserId(LoginUserThreadLocal.getCurrentUserId())
                        .setIsDefault(true));

        if (adminPrinterConcentration != null) {
            if (DictConstant.PRINT_TYPE_ASSET == printType) {
                UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultTagId());
                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                    defaultTagId = tagPrintDto.getId();
                }
            } else if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
                UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultCftagId());
                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                    defaultTagId = tagPrintDto.getId();
                }
            }
        }

        // 获取标签详情数据
        UserTagPrintDto tagDetail = userTagService.getDetail(defaultTagId);
        if (ObjectUtil.isEmpty(tagDetail)) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }

        // 获取当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();
        List<UserTagDto> userTags = userTagService.queryTagBySizeId(companyId, sizeId, tagType, kw, printType, id);

        // 封装返回数据
        UserTagResDto userTagResDto = new UserTagResDto();
        userTagResDto.setGroupData(userTags);
        userTagResDto.setTagSelected(defaultTagId);
        return userTagResDto;
    }

    /**
     * 获取PDF模版
     *
     * @return 获取PDF模版
     */
    @Override
    public HashMap<String, List<UserTagDto>> getPdfTag(Short printType, Long printerId) {
        // 获取标签模板数据
        List<UserTagDto> tagList = userTagService.queryTagBySizeId(LoginUserThreadLocal.getCompanyId(), null, null, null, printType, printerId);

        // Pdf标签只有 50*30 取出标签尺寸列表
        List<Long> pdfSize = Arrays.asList(1L, 4L);
        List<UserTagDto> userTagList = Lists.newArrayList();
        // 初始化组数据
        HashMap<String, List<UserTagDto>> groupData = new HashMap<>();
        String key = "";
        if (CollUtil.isNotEmpty(tagList)) {
            for (UserTagDto userTagDto : tagList) {
                //判断是否在允许的 标签尺寸列表中
                if (pdfSize.contains(userTagDto.getSizeId())) {
                    userTagList.add(userTagDto);
                    key = userTagDto.getSizeLong() + "*" + userTagDto.getSizeWide();
                }
            }
            groupData.put(key, userTagList);
        }

        return groupData;
    }

    /**
     * 修改标签模板名称
     *
     * @param userTagNameDto 修改标签模板名称对象
     * @return Boolean
     */
    @Override
    public Boolean updateTagName(UserTagNameDto userTagNameDto) {
        // 标签id
        Long tagId = userTagNameDto.getTagId();
        // 标签名称
        String tagName = userTagNameDto.getTagName();
        // 查询标签模板
        AsUserTag asUserTag = userTagService.getOneById(tagId, userTagNameDto.getPrintType());
        if (ObjectUtil.isNull(asUserTag)) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }

        //  云模板不允许修改名称
        if (asUserTag.getTagType() == 1) {
            throw new BusinessException(SystemResultCode.SYSTEM_TAG_NO_EDIT);
        }

//        // 当前企业
//        Long companyId = LoginUserThreadLocal.getCompanyId();

//        // 判断模板名称是否重名  # 标签模板名称可以重复 李勇 新需求
//        List<AsUserTag> userTagList = userTagMapper.checkTagName(tagName, tagId, companyId);
//        if (CollUtil.isNotEmpty(userTagList)) {
//            throw new BusinessException(SystemResultCode.TAG_NAME_USED);
//        }

        // 修改标签模板名称
        AsUserTag userTag = new AsUserTag();
        userTag.setId(tagId).setTagName(tagName);
        if (!userTagService.updateById(userTag)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        return true;
    }

    /**
     * 编辑用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Booleanø
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editUserTag(UserTagSaveDto userTagSaveDto) {
        // 校验标签模板是否存在
        AsUserTag byId = this.getById(userTagSaveDto.getId());
        if (ObjectUtil.isEmpty(byId)) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }

        // 校验该标签尺寸是否存在
        AsTagSize asTagSize = tagSizeMapper.selectById(userTagSaveDto.getSizeId());
        if (ObjectUtil.isEmpty(asTagSize)) {
            throw new BusinessException(SystemResultCode.TAG_SIZE_NOT_EXISTS);
        }

        //判断属性数量是否大于最大属性数量
        int size = userTagSaveDto.getAttrData().size();
        // 获取标签的max_attr_num
        Integer maxAttrNum = asTagSize.getMaxAttrNum();
        if (size > maxAttrNum) {
            throw new BusinessException(SystemResultCode.TAG_ATTR_MAX_LIMIT, String.valueOf(maxAttrNum));
        }

        // 获取所有的标签属性数据
        TagAttrListCO tagAttrListCO;
        switch (userTagSaveDto.getPrintType()) {
            case DictConstant.PRINT_TYPE_ASSET:
                tagAttrListCO = assetAbs.getAttrList(new TagAttrListQry());
                break;
            case DictConstant.PRINT_TYPE_MATERIAL:
                tagAttrListCO = maintainAbs.getAttrList(new TagAttrListQry());
                break;
            default:
                throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        List<String> attrCollect = tagAttrListCO.getTagAttrs().stream().map(TagAttrCO::getAttrCode).collect(Collectors.toList());

        // 扩展属性
        List<FormFieldCO> standardExtField = standardAbs.getStandardExtField(
                new StandardExtFieldListQry(tagAttrListCO.getFormId(), userTagSaveDto.getStandardId()));
        standardExtField.forEach(it -> attrCollect.add(it.getFieldCode()));

        // 标签属性
        List<TagAttrDto> attrData = userTagSaveDto.getAttrData();
        // 标签属性判断
        if (CollUtil.isNotEmpty(attrData)) {
            for (TagAttrDto attr : attrData) {
                if (!attrCollect.contains(attr.getAttrCode())) {
                    throw new BusinessException(SystemResultCode.TAG_ATTR_NOT_EXISTS, attr.getAttrName());
                }

                if (StrUtil.isNotBlank(attr.getAlias()) &&
                        StrUtil.length(attr.getAlias()) > AssetConstant.TAG_ATTR_LENGTH_LIMIT) {
                    throw new BusinessException(SystemResultCode.TAG_ATTR_LENGTH_LIMIT, attr.getAlias(), String.valueOf(AssetConstant.TAG_ATTR_LENGTH_LIMIT));
                }
            }
        }

        // 编辑用户标签数据
        AsUserTag asUserTag = new AsUserTag();
        BeanUtil.copyProperties(userTagSaveDto, asUserTag);
        asUserTag.setTagType(AssetConstant.TAG_TYPE_CUSTOM).setId(userTagSaveDto.getId());
        if (StrUtil.isNotBlank(userTagSaveDto.getTagBackgroundImage())) {
            asUserTag.setBackgroundImage(userTagSaveDto.getTagBackgroundImage());
        }
        if (!this.updateById(asUserTag)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 打印设备与标签
        printerTagsMapper.delete(
                Wrappers.lambdaUpdate(AsPrinterTags.class)
                        .eq(AsPrinterTags::getTagId, asUserTag.getId())
        );
        List<AsPrinterTags> tags = printerTagsMapper.selectList(
                Wrappers.lambdaQuery(AsPrinterTags.class)
                        .eq(AsPrinterTags::getTagId, userTagSaveDto.getSourceTagId())
        );
        if (CollUtil.isNotEmpty(tags)) {
            List<AsPrinterTags> list = tags.stream().map(v -> new AsPrinterTags().setPrinterId(v.getPrinterId()).setTagId(asUserTag.getId())).collect(toList());
            list.forEach(printerTagsMapper::insert);
        } else {
            printerTagsMapper.insert(new AsPrinterTags().setTagId(asUserTag.getId()).setPrinterId(userTagSaveDto.getPrinterId()));
        }
        return true;
    }

    /**
     * 删除用户自定义标签模板
     *
     * @param id 用户自定义标签id
     * @return Boolean
     */
    @Override
    public Boolean deleteUserTag(Long id) {
        // 校验标签模板是否存在
        AsUserTag byId = this.getById(id);
        if (ObjectUtil.isEmpty(byId)) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }

        //  云模板不允许操作删除
        if (byId.getTagType() == 1) {
            throw new BusinessException(SystemResultCode.SYSTEM_TAG_NO_ACTION);
        }

        boolean flag = this.removeById(id);
        if (flag){
            printerTagsMapper.delete(
                    Wrappers.lambdaUpdate(AsPrinterTags.class)
                            .eq(AsPrinterTags::getTagId, id)
            );
        }
        return flag;
    }

    /**
     * 通过sizeId
     *
     * @param sizeId 尺寸Id
     * @return 标签信息
     */
    @Override
    public List<UserTagDto> getDistinctBySizeId(Short printType, Long sizeId, Long printerId) {
        // 校验该标签尺寸是否存在
        AsTagSize asTagSize = tagSizeMapper.selectById(sizeId);
        if (ObjectUtil.isEmpty(asTagSize)) {
            sizeId = 1L;
        }

        // 获取标签列表
        return this.baseMapper.getDistinctBySizeId(printType, sizeId, printerId);
    }

    /**
     * 标签模板详情
     *
     * @param id 模板id
     * @return 标签模板信息
     */
    @Override
    public UserTagDetailDto getDetail(Long id) {
        UserTagPrintDto detail = this.baseMapper.getDetail(id);
        if (detail.getStandardId() != null) {
            FormVO formVO = formAbs.getFormById(
                    new FormGetQry().setFormId(detail.getStandardId())
                            .setCompanyIds(ListUtil.of(1L, LoginUserThreadLocal.getCompanyId()))
                            .setThrowException(false));
            if (ObjectUtil.isNotNull(formVO)) {
                detail.setStandardName(formVO.getFormName());
            } else {
                detail.setStandardName(StrUtil.EMPTY);
            }
        } else {
            detail.setStandardName(StrUtil.EMPTY);
        }
        UserTagDetailDto userTagDto = new UserTagDetailDto();
        BeanUtil.copyProperties(detail, userTagDto);
        return userTagDto;
    }

}
