package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.AdminPrinterConcentrationAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 17:58
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.AdminPrinterConcentrationAbsImpl")
@FeignClient(name = "asset-means", url = "https://{gateway}/client/abs/means/AdminPrinterConcentrationAbs/")
public interface AdminPrinterConcentrationAbsRemoteClient extends AdminPrinterConcentrationAbs {

}
