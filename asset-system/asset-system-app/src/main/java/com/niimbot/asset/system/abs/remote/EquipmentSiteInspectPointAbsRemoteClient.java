package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.EquipmentSiteInspectPointAbs;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;


@ConditionalOnMissingClass(value = "com.niimbot.asset.equipment.abs.impl.EquipmentSiteInspectPointAbsImpl")
@FeignClient(name = "asset-equipment", url = "https://{gateway}/client/abs/equipment/equipmentPointAbs/")
public interface EquipmentSiteInspectPointAbsRemoteClient extends EquipmentSiteInspectPointAbs {
}
