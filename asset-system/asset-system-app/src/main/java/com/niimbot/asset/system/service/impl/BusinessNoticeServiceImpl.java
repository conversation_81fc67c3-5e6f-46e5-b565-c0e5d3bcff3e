package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsBusinessNoticeMapper;
import com.niimbot.asset.system.model.AsBusinessNotice;
import com.niimbot.asset.system.service.BusinessNoticeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.BusinessNoticeDto;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/17 下午3:19
 */
@Slf4j
@Service
public class BusinessNoticeServiceImpl extends ServiceImpl<AsBusinessNoticeMapper, AsBusinessNotice> implements BusinessNoticeService {

    @Override
    public Boolean saveOrUpdate(BusinessNoticeDto noticeDto) {
        AsBusinessNotice businessNotice = new AsBusinessNotice();
        BeanUtils.copyProperties(noticeDto, businessNotice);
        businessNotice.setCompanyId(LoginUserThreadLocal.getCompanyId());

        if (StrUtil.isBlank(noticeDto.getBizCode())) {
            businessNotice.setBizCode(IdUtil.simpleUUID());
        }
        return this.saveOrUpdate(businessNotice);
    }

    @Override
    public BusinessNoticeDto queryDetail(Long companyId) {
        if (Objects.isNull(companyId)) {
            return null;
        }

        AsBusinessNotice businessNotice = this.getOne(Wrappers.lambdaQuery(AsBusinessNotice.class).eq(AsBusinessNotice::getCompanyId, companyId)
                .eq(AsBusinessNotice::getIsDelete, 0));
        if (Objects.isNull(businessNotice)) {
            return null;
        }
        BusinessNoticeDto result = new BusinessNoticeDto();
        BeanUtils.copyProperties(businessNotice, result);
        return result;
    }

    @Override
    public Boolean removeNotice(Long noticeId) {
        if (Objects.isNull(noticeId)) {
            return null;
        }

        AsBusinessNotice businessNotice = this.getOne(Wrappers.lambdaQuery(AsBusinessNotice.class).eq(AsBusinessNotice::getId, noticeId)
                .eq(AsBusinessNotice::getIsDelete, 0));
        if (Objects.isNull(businessNotice)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "业务公告不存在");
        }

        return update(Wrappers.lambdaUpdate(AsBusinessNotice.class)
                .set(AsBusinessNotice::getIsDelete, 1)
                .eq(AsBusinessNotice::getId, noticeId)
                .eq(AsBusinessNotice::getIsDelete, 0));
    }
}
