//package com.niimbot.asset.system.service.impl;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.convert.Convert;
//import cn.hutool.core.date.DatePattern;
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.util.ObjectUtil;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.niimbot.asset.framework.core.enums.MeansResultCode;
//import com.niimbot.asset.framework.utils.DateUtils;
//import com.niimbot.asset.framework.utils.StringUtils;
//import com.niimbot.asset.system.mapper.*;
//import com.niimbot.asset.means.service.AsUserPrintLogService;
//import com.niimbot.asset.means.service.AssetService;
//import com.niimbot.asset.system.abs.AssetAbs;
//import com.niimbot.asset.system.abs.UserPrintLogAbs;
//import com.niimbot.asset.system.model.*;
//import com.niimbot.asset.system.service.CompanyService;
//import com.niimbot.asset.system.service.HomeStatisticsService;
//import com.niimbot.jf.core.exception.category.BusinessException;
//import com.niimbot.system.statistics.*;
//import org.springframework.stereotype.Service;
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
//
//@Service
//public class HomeStatisticsServiceImpl implements HomeStatisticsService {
//
//    @Resource
//    private UserPrintLogAbs userPrintLogAbs;
//
//    @Resource
//    private AssetAbs assetAbs;
//
//    @Resource
//    private CompanyService companyService;
//
//    @Resource
//    private AsUserReportsMapper userReportsMapper;
//
//    @Resource
//    private AsUserActiveReportsMapper userActiveReportsMapper;
//
//    @Resource
//    private AsUserStatisticsReportsMapper userStatisticsReportsMapper;
//
//    @Resource
//    private AsAssetReportsMapper assetReportsMapper;
//
//    @Resource
//    private AsIndustryMapper industryMapper;
//
//    /* 统计类型 */
//    private static final String STATISTICS_MEMBER = "statisticsMember";
//    private static final String STATISTICS_LOST_MEMBER = "statisticsLostMember";
//    private static final String STATISTICS_MEMBER_ASSET = "statisticsMemberAsset";
//    private static final String STATISTICS_ACTIVE_MEMBER = "statisticsActiveMember";
//    private static final String STATISTICS_MEMBER_BY_INDUSTRY = "statisticsMemberByIndustry";
//    private static final String STATISTICS_EFFECTIVE_MEMBER_BY_INDUSTRY = "statisticsEffectiveMemberByIndustry";
//    private static final String STATISTICS_MEMBER_BY_ASSET = "statisticsMemberByAsset";
//    private static final String STATISTICS_EQUIPMENT = "statisticsEquipment";
//
//    @Override
//    public HomeStatisticsDto homeStatistics(String type, Integer year, Integer reportType) {
//        // 自动感应格式
//        Date date = DateUtil.parse(DateUtil.now());
//        // 当前年
//        int currentYear = DateUtil.year(date);
//        if (0 == year) {
//            year = currentYear;
//        }
//        // 目前只支持查看2018至%s年数据
//        if (year > currentYear) {
//            throw new BusinessException(MeansResultCode.ASSET_INCR_SELECT_LIMIT, String.valueOf(year));
//        }
//        // 统计方式 1-周 2-日
//        List<Integer> reportTypeArr = Arrays.asList(1, 2);
//        if (!reportTypeArr.contains(reportType)) {
//            reportType = 1;
//        }
//
//        String[] typeArr = type.split("\\|");
//        if (StringUtils.isEmpty(type) || StringUtils.isEmpty(typeArr[0])) {
//            //如果你没有明确指定要统计什么，那返回所有可能的统计数据给你
//            typeArr = new String[]{
//                    STATISTICS_MEMBER, STATISTICS_LOST_MEMBER, STATISTICS_MEMBER_ASSET, STATISTICS_ACTIVE_MEMBER,
//                    STATISTICS_MEMBER_BY_INDUSTRY, STATISTICS_EFFECTIVE_MEMBER_BY_INDUSTRY,
//                    STATISTICS_MEMBER_BY_ASSET, STATISTICS_EQUIPMENT};
//        }
//
//        // 初始化统计对象
//        HomeStatisticsDto homeStatisticsDto = new HomeStatisticsDto();
//
//        for (String typeStr : typeArr) {
//            switch (typeStr) {
//                case STATISTICS_MEMBER:
//                    HomeStatisticsDto.StatisticsWeekDto statisticsMember = this.statisticsMemberByTime(year, currentYear);
//                    homeStatisticsDto.setStatisticsMember(statisticsMember);
//                    break;
//                case STATISTICS_LOST_MEMBER:
//                    HomeStatisticsDto.StatisticsWeekDto statisticsLostMember = this.statisticsLostMemberByTime(year, currentYear);
//                    homeStatisticsDto.setStatisticsLostMember(statisticsLostMember);
//                    break;
//                case STATISTICS_MEMBER_ASSET:
//                    HomeStatisticsDto.StatisticsWeekDto statisticsMemberAsset = this.statisticsAssetByTime(year, currentYear);
//                    homeStatisticsDto.setStatisticsMemberAsset(statisticsMemberAsset);
//                    break;
//                case STATISTICS_ACTIVE_MEMBER:
//                    HomeStatisticsDto.StatisticsWeekDto statisticsActiveMember = this.statisticsActiveMemberByTime(year, currentYear, reportType);
//                    homeStatisticsDto.setStatisticsActiveMember(statisticsActiveMember);
//                    break;
//                case STATISTICS_MEMBER_BY_INDUSTRY:
//                    Map<String, List<StatisticsIndustryDto>> statisticsMemberByIndustry = this.statisticsAllUserByIndustry();
//                    homeStatisticsDto.setStatisticsMemberByIndustry(statisticsMemberByIndustry);
//                    break;
//                case STATISTICS_EFFECTIVE_MEMBER_BY_INDUSTRY:
//                    HomeStatisticsDto.StatisticsEffectiveUserDto statisticsEffectiveUserByIndustry = this.statisticsEffectiveUserByIndustry();
//                    homeStatisticsDto.setStatisticsEffectiveMemberByIndustry(statisticsEffectiveUserByIndustry);
//                    break;
//                case STATISTICS_MEMBER_BY_ASSET:
//                    StatisticsUserByAssetDto statisticsMemberByAssetNum = this.statisticsMemberByAssetNum();
//                    homeStatisticsDto.setStatisticsMemberByAsset(statisticsMemberByAssetNum);
//                    break;
//                case STATISTICS_EQUIPMENT:
//                    homeStatisticsDto.setStatisticsEquipment(userPrintLogService.userByCount());
//                    break;
//                default:
//                    break;
//            }
//        }
//
//        return homeStatisticsDto;
//    }
//
//    /**
//     * 按时间统计会员数量
//     *
//     * @param year        搜索年份
//     * @param currentYear 当前年份
//     * @return 会员数量统计数据
//     */
//    private HomeStatisticsDto.StatisticsWeekDto statisticsMemberByTime(Integer year, Integer currentYear) {
//        //获取搜索年份之前的用户总数
//        QueryWrapper<AsUserReports> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lt("day_time", year + "-01-01");
//        Integer oldCount = userReportsMapper.selectCount(queryWrapper);
//
//        //获取用户每周的新增数
//        List<StatisticsNumWeekDto> reportsData = userReportsMapper.userAddByWeek(year);
//
//        //获取一年的总周数
//        List<Map<String, String>> weeksOfYear = DateUtils.getWeeksOfYear(year);
//        //获取当前时间是一年中的第几周
//        int currentWeek = DateUtils.thisWeekOfYear();
//        // 搜索年的总周数
//        int weeks = currentYear.equals(year) ? currentWeek : weeksOfYear.size();
//
//        Map<Integer, Integer> userAddMap = reportsData.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//        // 补全每周数量
//        HashMap<Integer, Integer> userAddAllMap = new HashMap<>();
//        HashMap<Integer, Integer> userTotalMap = new HashMap<>();
//        Integer temp = 0;
//        for (int i = 1; i <= weeks; i++) {
//            userAddAllMap.put(i, userAddMap.getOrDefault(i, 0));
//            userTotalMap.put(i, temp + oldCount);
//            temp += userAddMap.getOrDefault(i, 0);
//        }
//
//        /* 获取每周的注销的企业数 */
//        // 获取用户总数
//        int companyTotal = companyService.count();
//
//        //获取注销用户统计报告数据
//        List<StatisticsNumWeekDto> userDeleteReports = userStatisticsReportsMapper.userDeleteByWeek(year);
//
//        Map<Integer, Integer> userDeleteMap = userDeleteReports.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//        Integer tmp = 0;
//        // 每周的用户删除数
//        HashMap<Integer, Integer> userDeleteTotalMap = new HashMap<>();
//        for (int i = 1; i <= weeks; i++) {
//            tmp += userDeleteMap.getOrDefault(i, 0);
//            userDeleteTotalMap.put(i, tmp);
//        }
//        /* 获取每周的注销的企业数 */
//
//        // 初始化数组
//        List<String> xAxisList = Lists.newArrayList();
//        List<String> weekRangeList = Lists.newArrayList();
//        List<Integer> userNumList = Lists.newArrayList();
//        List<Integer> oldUserList = Lists.newArrayList();
//        List<Integer> userTotalList = Lists.newArrayList();
//        List<Integer> deleteUserNumList = Lists.newArrayList();
//        HashMap<String, List<?>> seriesMap = new HashMap<>();
//        HomeStatisticsDto.StatisticsWeekDto statisticsWeekDto = new HomeStatisticsDto.StatisticsWeekDto();
//        for (int i = 1; i <= weeks; i++) {
//            xAxisList.add("第" + i + "周");
//            weekRangeList.add(weeksOfYear.get(i - 1).get("week_start").replaceAll("-", ".") + " - " +
//                    weeksOfYear.get(i - 1).get("week_end").replaceAll("-", "."));
//
//            Integer userAddAll = userAddAllMap.getOrDefault(i, 0);
//            Integer userTotal = userTotalMap.getOrDefault(i, 0);
//            userNumList.add(userAddAll);
//            oldUserList.add(userTotal);
//            userTotalList.add(userAddAll + userTotal);
//
//            Integer deleteUserNum;
//            if (weeks == i && currentYear.equals(year)) {
//                deleteUserNum = userAddAll + userTotal - companyTotal;
//                deleteUserNum = deleteUserNum < 0 ? 0 : deleteUserNum;
//            } else {
//                deleteUserNum = userDeleteTotalMap.getOrDefault(i, 0);
//            }
//            deleteUserNumList.add(deleteUserNum);
//        }
//        seriesMap.put("weekRange", weekRangeList);
//        seriesMap.put("userNum", userNumList);
//        seriesMap.put("oldUserNum", oldUserList);
//        seriesMap.put("userTotalNum", userTotalList);
//        seriesMap.put("deleteUserNum", deleteUserNumList);
//
//        statisticsWeekDto.setXAxis(xAxisList);
//        statisticsWeekDto.setSeries(seriesMap);
//
//        return statisticsWeekDto;
//    }
//
//    /**
//     * 按时间统计资产数量
//     *
//     * @param year        搜索年份
//     * @param currentYear 当前年份
//     * @return 资产数量统计数据
//     */
//    private HomeStatisticsDto.StatisticsWeekDto statisticsAssetByTime(Integer year, Integer currentYear) {
//        // 获取搜索年份之前的资产总数
//        QueryWrapper<AsAssetReports> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lt("day_time", year + "-01-01");
//        Integer oldCount = assetReportsMapper.selectCount(queryWrapper);
//
//        // 获取资产每周的新增数
//        List<StatisticsNumWeekDto> reportsData = assetReportsMapper.assetAddByWeek(year);
//        // 获取资产每周的删除数
//        List<StatisticsNumWeekDto> reportsDeleteData = assetReportsMapper.assetDeleteByWeek(year);
//
//        //获取搜索年份之前的用户总数
//        QueryWrapper<AsUserReports> userQueryWrapper = new QueryWrapper<>();
//        userQueryWrapper.lt("day_time", year + "-01-01");
//        Integer userOldCount = userReportsMapper.selectCount(userQueryWrapper);
//
//        //获取用户每周的新增数
//        List<StatisticsNumWeekDto> userReportsData = userReportsMapper.userAddByWeek(year);
//
//        //获取一年的总周数
//        List<Map<String, String>> weeksOfYear = DateUtils.getWeeksOfYear(year);
//        //获取当前时间是一年中的第几周
//        int currentWeek = DateUtils.thisWeekOfYear();
//        // 搜索年的总周数
//        int weeks = currentYear.equals(year) ? currentWeek : weeksOfYear.size();
//
//        Map<Integer, Integer> assetAddMap = reportsData.stream().collect(Collectors.toMap(
//                StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//        Map<Integer, Integer> assetDeleteMap = reportsDeleteData.stream().collect(Collectors.toMap(
//                StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//        Map<Integer, Integer> userAddMap = userReportsData.stream().collect(Collectors.toMap(
//                StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//        // 获取资产每周的总数
//        HashMap<Integer, Integer> assetAddAllMap = new HashMap<>();
//        HashMap<Integer, Integer> assetTotalMap = new HashMap<>();
//        Integer temp = 0;
//        // 获取用户每周的总数
//        HashMap<Integer, Integer> userTotalMap = new HashMap<>();
//        Integer tmp = 0;
//        for (int i = 1; i <= weeks; i++) {
//            assetAddAllMap.put(i, assetAddMap.getOrDefault(i, 0));
//            temp += assetAddMap.getOrDefault(i, 0);
//            assetTotalMap.put(i, temp + oldCount);
//
//            // 每天的用户总数
//            tmp += userAddMap.getOrDefault(i, 0);
//            userTotalMap.put(i, tmp + userOldCount);
//        }
//
//
//        /* 获取每周的资产总数（除去删除部分的资产） */
////        // 获取资产总数
////        int assetNum = assetService.count();
////
////        //取出实际资产每周的新增数
////        List<StatisticsNumWeekDto> assetWeekData = assetService.weekReport(year);
////        Map<Integer, Integer> assetWeekMap = assetWeekData.stream().collect(Collectors.toMap(
////                StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
////
////        HashMap<Integer, Integer> assetWeekTotalMap = new HashMap<>();
////        for (int i = weeks; i > 0; i--) {
////            if (i == weeks) {
////                assetWeekTotalMap.put(i, assetNum);
////            } else {
////                Integer assetWeekNum = assetWeekMap.getOrDefault(i, 0);
////                assetWeekTotalMap.put(i, Math.max(assetWeekTotalMap.get(i + 1) - assetWeekNum, 0));
////            }
////        }
//        /* 获取每周的资产总数（除去删除部分的资产） */
//
//        // 获取用户总数
//        int companyTotal = companyService.count();
//
//        // 初始化数组
//        List<String> xAxisList = Lists.newArrayList();
//        List<String> weekRangeList = Lists.newArrayList();
//        List<Integer> assetNumList = Lists.newArrayList();
//        List<Integer> assetTotalList = Lists.newArrayList();
////        List<Integer> actualAssetTotalList = Lists.newArrayList();
//        List<Integer> assetDeleteList = Lists.newArrayList();
//        List<String> avgUserAssetList = Lists.newArrayList();
//        HashMap<String, List<?>> seriesMap = new HashMap<>();
//        HomeStatisticsDto.StatisticsWeekDto statisticsWeekDto = new HomeStatisticsDto.StatisticsWeekDto();
//        for (int i = 1; i <= weeks; i++) {
//            xAxisList.add("第" + i + "周");
//            weekRangeList.add(weeksOfYear.get(i - 1).get("week_start").replaceAll("-", ".") + " - " +
//                    weeksOfYear.get(i - 1).get("week_end").replaceAll("-", "."));
//
//            Integer assetAddAll = assetAddAllMap.getOrDefault(i, 0);
//            Integer assetTotal = assetTotalMap.getOrDefault(i, 0);
//            Integer assetDelete = assetDeleteMap.getOrDefault(i, 0);
////            Integer assetWeekTotal = assetWeekTotalMap.getOrDefault(i, 0);
//            assetNumList.add(assetAddAll);
//            assetTotalList.add(assetTotal);
//            assetDeleteList.add(assetDelete);
////            actualAssetTotalList.add(assetWeekTotal);
//
////            // 删除的资产数
////            int assetDeleteNum = assetTotal - assetWeekTotal;
////            assetDeleteNum = Math.max(assetDeleteNum, 0);
////            assetDeleteList.add(assetDeleteNum);
//
//            // 当天的总资产数
//            int assetCurrentTotal = assetTotal - assetDelete;
//            assetCurrentTotal = Math.max(assetCurrentTotal, 0);
//
//            // 计算平均资产数
//            Integer userTotal = (i == weeks) ? companyTotal : userTotalMap.getOrDefault(i, 0);
//            BigDecimal avgUserAsset = divide(assetCurrentTotal, userTotal, 2);
//            String avgUserAssetStr = "0.00".equals(avgUserAsset.toPlainString()) ? "0" : avgUserAsset.toPlainString();
//            avgUserAssetList.add(avgUserAssetStr);
//        }
//        seriesMap.put("weekRange", weekRangeList);
//        seriesMap.put("assetNum", assetNumList);
//        seriesMap.put("assetTotalNum", assetTotalList);
////        seriesMap.put("actualAssetTotal", actualAssetTotalList);
//        seriesMap.put("assetDelete", assetDeleteList);
//        seriesMap.put("avgUserAsset", avgUserAssetList);
//
//        statisticsWeekDto.setXAxis(xAxisList);
//        statisticsWeekDto.setSeries(seriesMap);
//
//        return statisticsWeekDto;
//    }
//
//    /**
//     * 按时间统计流失用户和沉睡用户数量
//     *
//     * @param year        搜索年份
//     * @param currentYear 当前年份
//     * @return 流失用户和沉睡用户数量统计数据
//     */
//    private HomeStatisticsDto.StatisticsWeekDto statisticsLostMemberByTime(Integer year, Integer currentYear) {
//        //获取搜索年份之前的用户总数
//        QueryWrapper<AsUserReports> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lt("day_time", year + "-01-01");
//        Integer oldCount = userReportsMapper.selectCount(queryWrapper);
//
//        //获取用户每天的新增数
//        List<StatisticsNumWeekDto> reportsData = userReportsMapper.userAddByWeek(year);
//
//        //获取用户统计报告数据
//        QueryWrapper<AsUserStatisticsReports> statisticsQueryWrapper = new QueryWrapper<>();
//        queryWrapper.likeRight("day_time", year);
//        List<AsUserStatisticsReports> userStatisticsReports = userStatisticsReportsMapper.selectList(statisticsQueryWrapper);
//
//        //初始化每天的日期
//        DateTime currDate = DateUtils.date();
//
//        Map<Integer, Integer> userAddMap = reportsData.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//        //获取一年的总周数
//        List<Map<String, String>> weeksOfYear = DateUtils.getWeeksOfYear(year);
//        //获取当前时间是一年中的第几周
//        int currentWeek = DateUtils.thisWeekOfYear();
//        // 搜索年的总周数
//        int weeks = currentYear.equals(year) ? currentWeek : weeksOfYear.size();
//
//        // 补全每周数量
//        HashMap<Integer, Integer> userTotalMap = new HashMap<>();
//        Integer temp = 0;
//        for (int i = 1; i <= weeks; i++) {
//            temp += userAddMap.getOrDefault(i, 0);
//            userTotalMap.put(i, temp + oldCount);
//        }
//
//        Map<String, Integer> sleepUserNumMap = userStatisticsReports.stream().collect(Collectors.toMap(AsUserStatisticsReports::getDayTime, AsUserStatisticsReports::getSleepUserNum));
//        Map<String, Integer> lostUserNumMap = userStatisticsReports.stream().collect(Collectors.toMap(AsUserStatisticsReports::getDayTime, AsUserStatisticsReports::getLostUserNum));
//
//        // 取出每周的最后一天
//        List<String> weekEndList = Lists.newArrayList();
//        weeksOfYear.forEach(dto -> {
//            weekEndList.add(dto.getOrDefault("week_end", ""));
//        });
//
//        // 初始化数组
//        List<String> xAxisList = Lists.newArrayList();
//        List<String> weekRangeList = Lists.newArrayList();
//        List<Integer> lostUserNumList = Lists.newArrayList();
//        List<Integer> sleepUserNumList = Lists.newArrayList();
//        List<String> lostUserRateList = Lists.newArrayList();
//        List<String> sleepUserRateList = Lists.newArrayList();
//        HashMap<String, List<?>> seriesMap = new HashMap<>();
//        for (int i = 1; i <= weeks; i++) {
//            xAxisList.add("第" + i + "周");
//            weekRangeList.add(weeksOfYear.get(i - 1).get("week_start").replaceAll("-", ".") + " - " +
//                    weeksOfYear.get(i - 1).get("week_end").replaceAll("-", "."));
//
//            String k = weekEndList.get(i - 1);
//            if (currentWeek == i && currentYear.equals(year) && k.compareTo(currDate.toString()) >= 0) {
//                k = DateUtils.format(DateUtils.offsetDay(currDate, -1), DatePattern.NORM_DATE_PATTERN);
//            }
//            k += " 00:00:00";
//
//            Integer lostUserNum = lostUserNumMap.getOrDefault(k, 0);
//            Integer sleepUserNum = sleepUserNumMap.getOrDefault(k, 0);
//            // 老用户数
//            Integer userTotal = userTotalMap.getOrDefault(i, 0);
//            lostUserNumList.add(lostUserNum);
//            sleepUserNumList.add(sleepUserNum);
//
//            // 计算流失用户百分比
//            BigDecimal lostUserRate = divide(lostUserNum, userTotal);
//            String lostUserRateStr = "0.0000".equals(lostUserRate.toPlainString()) ? "0" : lostUserRate.toPlainString();
//            lostUserRateList.add(lostUserRateStr);
//
//            // 计算沉睡用户百分比
//            BigDecimal sleepUserRate = divide(sleepUserNum, userTotal);
//            String sleepUserRateStr = "0.0000".equals(sleepUserRate.toPlainString()) ? "0" : sleepUserRate.toPlainString();
//            sleepUserRateList.add(sleepUserRateStr);
//        }
//
//        seriesMap.put("weekRange", weekRangeList);
//        seriesMap.put("lostUserNum", lostUserNumList);
//        seriesMap.put("sleepUserNum", sleepUserNumList);
//        seriesMap.put("lostUserRate", lostUserRateList);
//        seriesMap.put("sleepUserRate", sleepUserRateList);
//
//        HomeStatisticsDto.StatisticsWeekDto statisticsWeekDto = new HomeStatisticsDto.StatisticsWeekDto();
//        statisticsWeekDto.setXAxis(xAxisList);
//        statisticsWeekDto.setSeries(seriesMap);
//
//        return statisticsWeekDto;
//    }
//
//    /**
//     * 按时间统计活跃用户数量
//     *
//     * @param year        搜索年份
//     * @param currentYear 当前年份
//     * @param reportType  统计方式 1-周/ 2-日
//     * @return 活跃用户数量统计数据
//     */
//
//    private HomeStatisticsDto.StatisticsWeekDto statisticsActiveMemberByTime(Integer year, Integer currentYear, Integer reportType) {
//        //获取搜索年份之前的用户总数
//        QueryWrapper<AsUserReports> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lt("day_time", year + "-01-01");
//        Integer oldCount = userReportsMapper.selectCount(queryWrapper);
//
//        //获取用户每周的新增数
//        List<StatisticsNumWeekDto> reportsData = userReportsMapper.userAddByWeek(year);
//
//        //获取一年的总周数
//        List<Map<String, String>> weeksOfYear = DateUtils.getWeeksOfYear(year);
//        //获取当前时间是一年中的第几周
//        int currentWeek = DateUtils.thisWeekOfYear();
//        // 搜索年的总周数
//        int weeks = currentYear.equals(year) ? currentWeek : weeksOfYear.size();
//
//        // 最后返回的统计对象
//        HomeStatisticsDto.StatisticsWeekDto statisticsWeekDto = new HomeStatisticsDto.StatisticsWeekDto();
//        if (1 == reportType) {
//            //周活跃用户
//
//            //获取用户每周的新增活跃数
//            List<StatisticsNumWeekDto> newUserActiveNumDto = userActiveReportsMapper.newUserActiveNum(year);
////            List<StatisticsNumWeekDto> oldUserActiveNumDto = userActiveReportsMapper.oldUserActiveNum(year);
//            List<StatisticsNumWeekDto> userActiveNumDto = userActiveReportsMapper.userActiveNum(year);
//            Map<Integer, Integer> newUserActiveNumMap = newUserActiveNumDto.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
////            Map<Integer, Integer> oldUserActiveNumMap = oldUserActiveNumDto.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//            Map<Integer, Integer> userActiveNumDtoMap = userActiveNumDto.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//            Map<Integer, Integer> userAddMap = reportsData.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//            // 补全每周数量
//            HashMap<Integer, Integer> userAddAllMap = new HashMap<>();
//            HashMap<Integer, Integer> userTotalMap = new HashMap<>();
//            Integer temp = 0;
//            for (int i = 1; i <= weeks; i++) {
//                userAddAllMap.put(i, userAddMap.getOrDefault(i, 0));
//                userTotalMap.put(i, temp + oldCount);
//                temp += userAddMap.getOrDefault(i, 0);
//            }
//
//            // 初始化数组
//            List<String> xAxisList = Lists.newArrayList();
//            List<String> weekRangeList = Lists.newArrayList();
//            List<Integer> newUserActiveNumList = Lists.newArrayList();
//            List<Integer> oldUserActiveNumList = Lists.newArrayList();
//            List<Integer> totalUserActiveNumList = Lists.newArrayList();
//            List<String> newUserActiveRateList = Lists.newArrayList();
//            List<String> oldUserActiveRateList = Lists.newArrayList();
//            List<String> totalUserActiveRateList = Lists.newArrayList();
//            HashMap<String, List<?>> seriesMap = new HashMap<>();
//
//            for (int i = 1; i <= weeks; i++) {
//                xAxisList.add("第" + i + "周");
//                weekRangeList.add(weeksOfYear.get(i - 1).get("week_start").replaceAll("-", ".") + " - " +
//                        weeksOfYear.get(i - 1).get("week_end").replaceAll("-", "."));
//
//                Integer newUserActiveNum = newUserActiveNumMap.getOrDefault(i, 0);
////                Integer oldUserActiveNum = oldUserActiveNumMap.getOrDefault(i, 0);
//                Integer userActiveNum = userActiveNumDtoMap.getOrDefault(i, 0);
//                newUserActiveNumList.add(newUserActiveNum);
////                oldUserActiveNumList.add(oldUserActiveNum);
//                oldUserActiveNumList.add(userActiveNum - newUserActiveNum);
//                totalUserActiveNumList.add(userActiveNum);
//
//                Integer userAddAllNum = userAddAllMap.getOrDefault(i, 0);
//                Integer userTotalNum = userTotalMap.getOrDefault(i, 0);
//                // 计算新用户百分比
//                BigDecimal newRate = divide(newUserActiveNum, userAddAllNum);
//                String newUserActiveRateStr = "0.0000".equals(newRate.toPlainString()) ? "0" : newRate.toPlainString();
//                newUserActiveRateList.add(newUserActiveRateStr);
//
//                // 计算老用户百分比
//                BigDecimal oldRate = divide(userActiveNum - newUserActiveNum, userTotalNum);
//                String oldUserActiveRateStr = "0.0000".equals(oldRate.toPlainString()) ? "0" : oldRate.toPlainString();
//                oldUserActiveRateList.add(oldUserActiveRateStr);
//
//                // 计算总用户百分比
//                BigDecimal totalRate = divide(userActiveNum, userAddAllNum + userTotalNum);
//                String totalRateStr = "0.0000".equals(totalRate.toPlainString()) ? "0" : totalRate.toPlainString();
//                totalUserActiveRateList.add(totalRateStr);
//            }
//
//            seriesMap.put("weekRange", weekRangeList);
//            seriesMap.put("newUserActiveNum", newUserActiveNumList);
//            seriesMap.put("oldUserActiveNum", oldUserActiveNumList);
//            seriesMap.put("totalUserActiveNum", totalUserActiveNumList);
//            seriesMap.put("newUserActiveRate", newUserActiveRateList);
//            seriesMap.put("oldUserActiveRate", oldUserActiveRateList);
//            seriesMap.put("totalUserActiveRate", totalUserActiveRateList);
//
//            statisticsWeekDto.setXAxis(xAxisList);
//            statisticsWeekDto.setSeries(seriesMap);
//        } else if (2 == reportType) {
//            //日活跃用户
//
//            //获取用户每天的新增活跃数
//            List<StatisticsNumDayDto> newUserActiveNumDto = userActiveReportsMapper.newUserActiveNumByDay(year);
//            List<StatisticsNumDayDto> oldUserActiveNumDto = userActiveReportsMapper.oldUserActiveNumByDay(year);
//
//            //初始化每天的日期
//            Date endDate = currentYear.equals(year) ? DateUtils.date() : DateUtils.parse(year + "-12-31");
//            List<String> dates = DateUtils.findDates(DateUtils.parse(year + "-01-01"), endDate);
//            int dayNum = dates.size();
//
//            Map<String, Integer> newUserActiveNumMap = newUserActiveNumDto.stream().collect(Collectors.toMap(StatisticsNumDayDto::getDay, StatisticsNumDayDto::getNum));
//            Map<String, Integer> oldUserActiveNumMap = oldUserActiveNumDto.stream().collect(Collectors.toMap(StatisticsNumDayDto::getDay, StatisticsNumDayDto::getNum));
//
//            Map<Integer, Integer> userAddMap = reportsData.stream().collect(Collectors.toMap(StatisticsNumWeekDto::getWeekTime, StatisticsNumWeekDto::getNum));
//
//            // 补全每周数量
//            HashMap<Integer, Integer> userAddAllMap = new HashMap<>();
//            HashMap<Integer, Integer> userTotalMap = new HashMap<>();
//            Integer temp = 0;
//            for (int i = 0; i < dayNum; i++) {
//                userAddAllMap.put(i, userAddMap.getOrDefault(i, 0));
//                userTotalMap.put(i, temp + oldCount);
//                temp += userAddMap.getOrDefault(i, 0);
//            }
//
//            // 初始化数组
//            List<Integer> xAxisList = Lists.newArrayList();
//            List<String> xAxisMonList = Lists.newArrayList();
//            List<String> dayDateList = Lists.newArrayList();
//            List<Integer> newUserActiveNumList = Lists.newArrayList();
//            List<Integer> oldUserActiveNumList = Lists.newArrayList();
//            List<Integer> totalUserActiveNumList = Lists.newArrayList();
//            List<String> newUserActiveRateList = Lists.newArrayList();
//            List<String> oldUserActiveRateList = Lists.newArrayList();
//            List<String> totalUserActiveRateList = Lists.newArrayList();
//            HashMap<String, List<?>> seriesMap = new HashMap<>();
//
//            for (int i = 0; i < dayNum; i++) {
//                String date = dates.get(i);
//                String dateStr = dates.get(i).replaceAll("-", ".");
//                dayDateList.add(dateStr);
//                xAxisList.add(DateUtil.dayOfMonth(DateUtils.parse(dateStr)));
//
//                Integer newUserActiveNum = newUserActiveNumMap.getOrDefault(date, 0);
//                Integer oldUserActiveNum = oldUserActiveNumMap.getOrDefault(date, 0);
//                newUserActiveNumList.add(newUserActiveNum);
//                oldUserActiveNumList.add(oldUserActiveNum);
//                totalUserActiveNumList.add(newUserActiveNum + oldUserActiveNum);
//
//                // 获得指定日期是所在年份的第几周
//                int curWeek = DateUtils.weekOfYear(DateUtils.parse(dateStr));
//
//                Integer userAddAllNum = userAddAllMap.getOrDefault(curWeek, 0);
//                Integer userTotalNum = userTotalMap.getOrDefault(curWeek, 0);
//                // 计算新用户百分比
//                BigDecimal newRate = divide(newUserActiveNum, userAddAllNum);
//                String newUserActiveRateStr = "0.0000".equals(newRate.toPlainString()) ? "0" : newRate.toPlainString();
//                newUserActiveRateList.add(newUserActiveRateStr);
//
//                // 计算老用户百分比
//                BigDecimal oldRate = divide(oldUserActiveNum, userTotalNum);
//                String oldUserActiveRateStr = "0.0000".equals(oldRate.toPlainString()) ? "0" : oldRate.toPlainString();
//                oldUserActiveRateList.add(oldUserActiveRateStr);
//
//                // 计算总用户百分比
//                BigDecimal totalRate = divide(newUserActiveNum + oldUserActiveNum, userAddAllNum + userTotalNum);
//                String totalRateStr = "0.0000".equals(totalRate.toPlainString()) ? "0" : totalRate.toPlainString();
//                totalUserActiveRateList.add(totalRateStr);
//            }
//
//            seriesMap.put("dayDate", dayDateList);
//            seriesMap.put("newUserActiveNum", newUserActiveNumList);
//            seriesMap.put("oldUserActiveNum", oldUserActiveNumList);
//            seriesMap.put("totalUserActiveNum", totalUserActiveNumList);
//            seriesMap.put("newUserActiveRate", newUserActiveRateList);
//            seriesMap.put("oldUserActiveRate", oldUserActiveRateList);
//            seriesMap.put("totalUserActiveRate", totalUserActiveRateList);
//
//            // 添加x周月份
//            // 获得指定日期是所在年份的第几月
//            int month = DateUtil.month(endDate) + 1;
//            for (int i = 1; i <= month; i++) {
//                xAxisMonList.add(i + "月");
//            }
//
//            statisticsWeekDto.setXAxis(xAxisList);
//            statisticsWeekDto.setXAxisMon(xAxisMonList);
//            statisticsWeekDto.setSeries(seriesMap);
//
//        }
//        return statisticsWeekDto;
//    }
//
//    /**
//     * 按行业分类统计用户数
//     *
//     * @return 用户数量统计数据
//     */
//    private Map<String, List<StatisticsIndustryDto>> statisticsAllUserByIndustry() {
//        List<StatisticsIndustryDto> statisticsUserIndustry = this.statisticsUserByIndustry();
//        List<StatisticsIndustryDto> statisticsLostUserIndustry = this.statisticsLostUserByIndustry();
//        List<StatisticsIndustryDto> statisticsSleepUserIndustry = this.statisticsSleepUserByIndustry();
//        List<StatisticsIndustryDto> statisticsWeekActiveUserIndustry = this.statisticsWeekActiveUserByIndustry();
//
//        HashMap<String, List<StatisticsIndustryDto>> map = new HashMap<>();
//        map.put("userIndustry", statisticsUserIndustry);
//        map.put("lostUserIndustry", statisticsLostUserIndustry);
//        map.put("sleepUserIndustry", statisticsSleepUserIndustry);
//        map.put("weekActiveUserIndustry", statisticsWeekActiveUserIndustry);
//
//        return map;
//    }
//
//    /**
//     * 按行业分类统计用户数
//     *
//     * @return 用户数量统计数据
//     */
//    private List<StatisticsIndustryDto> statisticsUserByIndustry() {
//        // 按行业分组取企业数量
//        List<StatisticsIndustryDto> statisticsIndustryDto = industryMapper.selectUserNumByIndustry();
//
//        // 组装成行业对企业数量的map
//        Map<Long, String> companyNumMap = getCompanyNumMap(statisticsIndustryDto);
//
//        // 初始化企业数量
//        BigDecimal companyTotal = new BigDecimal("0");
//        for (StatisticsIndustryDto dto : statisticsIndustryDto) {
//            companyTotal = companyTotal.add(new BigDecimal(dto.getCompanyNum()));
//        }
//
//        // 组装行业占比数据
//        return getIndustryRate(companyNumMap, companyTotal);
//    }
//
//    /**
//     * 按行业分类统计流失用户数
//     *
//     * @return 用户数量统计数据
//     */
//    private List<StatisticsIndustryDto> statisticsLostUserByIndustry() {
//        Calendar calendar = Calendar.getInstance();
//        // 180天前的时间
//        calendar.add(Calendar.DAY_OF_MONTH, -180);
//        String datePre180 = DateUtils.format(calendar.getTime(), DatePattern.NORM_DATE_PATTERN);
//
//        // 按行业分组取180天前活跃的企业数量
//        List<StatisticsIndustryDto> statisticsIndustryDto = industryMapper.selectLostUserNumByIndustry(datePre180);
//
//        // 组装成行业对企业数量的map
//        Map<Long, String> companyNumMap = getCompanyNumMap(statisticsIndustryDto);
//
//        // 初始化企业数量
//        BigDecimal companyTotal = new BigDecimal("0");
//        for (StatisticsIndustryDto dto : statisticsIndustryDto) {
//            companyTotal = companyTotal.add(new BigDecimal(dto.getCompanyNum()));
//        }
//
//        // 组装行业占比数据
//        return getIndustryRate(companyNumMap, companyTotal);
//    }
//
//    /**
//     * 按行业分类统计沉睡用户数
//     *
//     * @return 用户数量统计数据
//     */
//    private List<StatisticsIndustryDto> statisticsSleepUserByIndustry() {
//        Calendar calendar = Calendar.getInstance();
//        // 30天前的时间
//        calendar.add(Calendar.DAY_OF_MONTH, -30);
//        String datePre30 = DateUtils.format(calendar.getTime(), DatePattern.NORM_DATE_PATTERN);
//        // 180天前的时间
//        calendar.add(Calendar.DAY_OF_MONTH, -150);
//        String datePre180 = DateUtils.format(calendar.getTime(), DatePattern.NORM_DATE_PATTERN);
//
//        // 按行业分组取30天前活跃的企业数量
//        List<StatisticsIndustryDto> statisticsIndustryDto = industryMapper.selectLostUserNumByIndustry(datePre30);
//        // 按行业分组取180天前活跃的企业数量
//        List<StatisticsIndustryDto> statisticsIndustryPre180 = industryMapper.selectLostUserNumByIndustry(datePre180);
//
//        // 组装成行业对企业数量的map
//        Map<Long, String> companyNumMapPre180 = getCompanyNumMap(statisticsIndustryPre180);
//
//        // 初始化沉睡用户map
//        HashMap<Long, String> companyNumMap = new HashMap<>();
//        // 初始化用户总数
//        BigDecimal companyTotal = new BigDecimal("0");
//        for (StatisticsIndustryDto dto : statisticsIndustryDto) {
//            String companyNumPre180 = companyNumMapPre180.getOrDefault(dto.getId(), "0");
//            BigDecimal companyNum = new BigDecimal(dto.getCompanyNum());
//            BigDecimal companyDecimalPre180 = new BigDecimal(companyNumPre180);
//            // 相减获取沉睡用户
//            BigDecimal subtract = companyNum.subtract(companyDecimalPre180);
//            String sleepNum = subtract.intValue() < 0 ? "0" : subtract.toPlainString();
//            companyNumMap.put(dto.getId(), sleepNum);
//            companyTotal = companyTotal.add(new BigDecimal(sleepNum));
//        }
//
//        // 组装行业占比数据
//        return getIndustryRate(companyNumMap, companyTotal);
//    }
//
//    /**
//     * 按行业分类统计有效用户数
//     *
//     * @return 用户数量统计数据
//     */
//    private HomeStatisticsDto.StatisticsEffectiveUserDto statisticsEffectiveUserByIndustry() {
//        // 获取上周的开始时间和结束时间
//        DateTime lastWeek = DateUtils.lastWeek();
//        DateTime beginDate = DateUtils.beginOfWeek(lastWeek);
//        DateTime endDate = DateUtils.endOfWeek(lastWeek);
//        // 按行业分组取企业数量
//        List<StatisticsIndustryDto> statisticsIndustryDto = industryMapper.selectEffectiveUserNumByIndustry(beginDate.toString(), endDate.toString());
//
//        // 组装成行业对企业数量的map
//        Map<Long, String> companyNumMap = getCompanyNumMap(statisticsIndustryDto);
//
//        // 初始化企业数量
//        BigDecimal companyTotal = new BigDecimal("0");
//        for (StatisticsIndustryDto dto : statisticsIndustryDto) {
//            companyTotal = companyTotal.add(new BigDecimal(dto.getCompanyNum()));
//        }
//
//        // 组装行业占比数据
//        List<StatisticsIndustryDto> rateList = getIndustryRate(companyNumMap, companyTotal);
//
//        // 加上有效用户数量
//        HomeStatisticsDto.StatisticsEffectiveUserDto statisticsEffectiveUserDto = new HomeStatisticsDto.StatisticsEffectiveUserDto();
//        statisticsEffectiveUserDto.setEffectiveUserNum(companyTotal.toPlainString()).setStatisticsEffectiveUserRate(rateList);
//        return statisticsEffectiveUserDto;
//    }
//
//    /**
//     * 按行业分类统计周活跃用户数
//     *
//     * @return 用户数量统计数据
//     */
//    private List<StatisticsIndustryDto> statisticsWeekActiveUserByIndustry() {
//        // 获取上周的开始时间和结束时间
//        DateTime lastWeek = DateUtils.lastWeek();
//        DateTime beginDate = DateUtils.beginOfWeek(lastWeek);
//        DateTime endDate = DateUtils.endOfWeek(lastWeek);
//        // 按行业分组取企业数量
//        List<StatisticsIndustryDto> statisticsIndustryDto = industryMapper.selectWeekActiveUserNumByIndustry(beginDate.toString(), endDate.toString());
//
//        // 组装成行业对企业数量的map
//        Map<Long, String> companyNumMap = getCompanyNumMap(statisticsIndustryDto);
//
//        // 初始化企业数量
//        BigDecimal companyTotal = new BigDecimal("0");
//        for (StatisticsIndustryDto dto : statisticsIndustryDto) {
//            companyTotal = companyTotal.add(new BigDecimal(dto.getCompanyNum()));
//        }
//
//        // 组装行业占比数据
//        return getIndustryRate(companyNumMap, companyTotal);
//    }
//
//    /**
//     * 按不同的资产数量范围统计会员数量
//     *
//     * @return 用户数量统计数据
//     */
//    private StatisticsUserByAssetDto statisticsMemberByAssetNum() {
//        List<StatisticsUserByAssetDto.AssetNumByCompany> assetNumByCompany = assetService.selectAssetNumGroupByCompany();
//        if (ObjectUtil.isEmpty(assetNumByCompany)) {
//            return null;
//        }
//
//        // 初始化资产数量
//        long userNum500 = 0L, gte_1 = 0L, gte_100 = 0L, gte_500 = 0L,
//                gte_1000 = 0L, gte_3000 = 0L, gte_10000 = 0L, gte_50000 = 0L, gte_100000 = 0L;
//
//        //换成区间形式
//        for (StatisticsUserByAssetDto.AssetNumByCompany val : assetNumByCompany) {
//            Long assetNum = val.getAssetNum();
//            if (assetNum > 100000L) {
//                ++gte_100000;
//            } else if (assetNum >= 50001) {
//                ++gte_50000;
//            } else if (assetNum >= 10001) {
//                ++gte_10000;
//            } else if (assetNum >= 3001) {
//                ++gte_3000;
//            } else if (assetNum >= 1001) {
//                ++gte_1000;
//            } else if (assetNum >= 501) {
//                ++gte_500;
//            } else if (assetNum >= 101) {
//                ++gte_100;
//                if (500 == assetNum) {
//                    ++userNum500;
//                }
//            } else if (assetNum >= 1) {
//                ++gte_1;
//            }
//        }
//
//        // 获取公司总数
//        int companyNum = companyService.count();
//
//        //有资产公司数
//        int haveAssetCompanyNum = assetNumByCompany.size();
//        //资产数为0的公司数
//        long gte_0 = companyNum - haveAssetCompanyNum;
//
//
//        // 资产数量范围数据
//        List<Long> memNumByAssetNum = Lists.newArrayList(gte_0, gte_1, gte_100, gte_500,
//                gte_1000, gte_3000, gte_10000, gte_50000, gte_100000);
//
//        //资产数量 区间范围
//        List<String> assetRange = Lists.newArrayList("0", "1-100", "101-500", "501-1000",
//                "1001-3000", "3001-10000", "10001-50000", "50001-100000", "100000以上");
//        // 初始化数组
//        List<StatisticsUserByAssetDto.UserRateByAsset> statisticsUserList = Lists.newArrayList();
//        int size = assetRange.size();
//        for (int i = 0; i < size; i++) {
//            // 资产数量 区间范围
//            String str = assetRange.get(i);
//            // 公司数量
//            Long companyNumByAssetNum = memNumByAssetNum.get(i);
//            // 计算百分比
//            BigDecimal rate = divide(companyNumByAssetNum, companyNum);
//            String rateStr = "0.0000".equals(rate.toPlainString()) ? "0" : rate.toPlainString();
//
//            StatisticsUserByAssetDto.UserRateByAsset userRateByAsset = new StatisticsUserByAssetDto.UserRateByAsset();
//            userRateByAsset.setRange(str).setUserNum(companyNumByAssetNum).setRate(rateStr);
//
//            // 添加到list
//            statisticsUserList.add(userRateByAsset);
//        }
//
//        // 500及以上资产的用户数及占比
//        HashMap<String, String> headData = new HashMap<>();
//        // 500及以上资产的用户数
//        Long userNumGte500 = haveAssetCompanyNum - gte_1 - gte_100 + userNum500;
//        headData.put("user_num_500", userNumGte500.toString());
//        // 计算百分比
//        BigDecimal rate500 = divide(userNumGte500, companyNum);
//        String rate500Str = "0.0000".equals(rate500.toPlainString()) ? "0" : rate500.toPlainString();
//        headData.put("rate", rate500Str);
//
//        StatisticsUserByAssetDto statisticsUserByAssetDto = new StatisticsUserByAssetDto();
//        statisticsUserByAssetDto.setHeadData(headData).setList(statisticsUserList);
//        return statisticsUserByAssetDto;
//    }
//
//    /**
//     * @param dividend 被除数
//     * @param divisor  除数
//     * @param digit  保留位数
//     * @return 结果
//     */
//    private BigDecimal divide(Object dividend, Object divisor, Integer digit) {
//        BigDecimal calc = new BigDecimal("0");
//        try {
//            BigDecimal dividendDecimal = new BigDecimal(Convert.toStr(dividend));
//            BigDecimal divisorDecimal = new BigDecimal(Convert.toStr(divisor));
//            if (0 == divisorDecimal.intValue()) {
//                return calc;
//            } else {
//                return dividendDecimal
//                        .divide(divisorDecimal, digit, RoundingMode.HALF_UP);
//            }
//        } catch (Exception e) {
//            return calc;
//        }
//    }
//
//    /**
//     * @param dividend 被除数
//     * @param divisor  除数
//     * @return 结果
//     */
//    private BigDecimal divide(Object dividend, Object divisor) {
//        return divide(dividend, divisor, 4);
//    }
//
//    /**
//     * 组装成行业对企业数量的map
//     *
//     * @return map
//     */
//    private Map<Long, String> getCompanyNumMap(List<StatisticsIndustryDto> statisticsIndustryDto) {
//        return statisticsIndustryDto.stream().collect(Collectors.toMap(StatisticsIndustryDto::getId, StatisticsIndustryDto::getCompanyNum));
//    }
//
//    /**
//     * 组装行业占比数据
//     *
//     * @return 行业占比数据
//     */
//    private List<StatisticsIndustryDto> getIndustryRate(Map<Long, String> companyNumMap, BigDecimal companyTotal) {
//        // 获取行业数据
////        QueryWrapper<AsIndustry> queryWrapper = new QueryWrapper<>();
////        List<AsIndustry> industries = industryMapper.selectList(queryWrapper.select("id", "industry_name"));
//        List<AsIndustry> industries = industryMapper.selectAllData();
//
//        List<StatisticsIndustryDto> statisticsIndustryList = Lists.newArrayList();
//        for (AsIndustry industry : industries) {
//            // 企业数量
//            String companyNum = companyNumMap.get(industry.getId());
//            companyNum = StringUtils.isNotEmpty(companyNum) ? companyNum : "0";
//            // 计算百分比
//            BigDecimal rate = divide(companyNum, companyTotal);
//            String rateStr = "0.0000".equals(rate.toPlainString()) ? "0" : rate.toPlainString();
//
//            // 行业统计对象
//            StatisticsIndustryDto industryDto = new StatisticsIndustryDto();
//            BeanUtil.copyProperties(industry, industryDto);
//            industryDto.setCompanyNum(companyNum)
//                    .setPercent(rateStr);
//
//            // 添加到list中
//            statisticsIndustryList.add(industryDto);
//        }
//
//        // 最后按照企业数量降序
//        return statisticsIndustryList.stream()
//                .sorted(Comparator.comparing(StatisticsIndustryDto::getCompanyNum).reversed()
//                        .thenComparing(StatisticsIndustryDto::getId))
//                .collect(Collectors.toList());
//    }
//
//}
