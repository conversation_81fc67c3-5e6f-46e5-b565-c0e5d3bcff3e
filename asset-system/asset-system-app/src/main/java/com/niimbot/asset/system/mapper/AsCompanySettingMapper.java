package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCompanySetting;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 公司自定义设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
public interface AsCompanySettingMapper extends BaseMapper<AsCompanySetting> {

    String selectCodeForGenSerialNo(@Param("id") Long id,
                                    @Param("type") String type,
                                    @Param("companyId") Long companyId);

    String getAssetMaxCode(@Param("prefix") String prefix,
                           @Param("companyId") Long companyId,
                           @Param("serialLen") Integer serialLen,
                           @Param("codeLen") Integer codeLen,
                           @Param("fieldCode") String fieldCode);

    String getMaterialMaxCode(@Param("prefix") String prefix,
                              @Param("companyId") Long companyId,
                              @Param("serialLen") Integer serialLen,
                              @Param("codeLen") Integer codeLen,
                              @Param("fieldCode") String fieldCode);

    String getProductMaxCode(@Param("prefix") String prefix,
                             @Param("companyId") Long companyId,
                             @Param("serialLen") Integer serialLen,
                             @Param("codeLen") Integer codeLen,
                             @Param("fieldCode") String fieldCode);

    String selectTreeNodePaths(@Param("id") Long id, @Param("type") String type);


}
