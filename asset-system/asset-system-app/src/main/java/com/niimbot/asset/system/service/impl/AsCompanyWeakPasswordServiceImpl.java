package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.system.constant.CompanyPasswordConstant;
import com.niimbot.asset.system.mapper.AsCompanyWeakPasswordMapper;
import com.niimbot.asset.system.model.AsCompanyWeakPassword;
import com.niimbot.asset.system.service.AsCompanyWeakPasswordService;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AsCompanyWeakPasswordServiceImpl extends ServiceImpl<AsCompanyWeakPasswordMapper, AsCompanyWeakPassword> implements AsCompanyWeakPasswordService, ApplicationListener<ApplicationReadyEvent> {

    @Resource
    private RedisService redisService;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        taskExecutor.execute(this::loadWeakPassword);
    }

    @Override
    public List<String> weakPwdList() {
        if (redisService.hasKey(CompanyPasswordConstant.COMPANY_WEAK_PASSWORD)) {
            return Convert.toList(String.class, redisService.get(CompanyPasswordConstant.COMPANY_WEAK_PASSWORD));
        } else {
            return loadWeakPassword();
        }
    }

    private List<String> loadWeakPassword() {
        // 弱密码写入缓存
        List<AsCompanyWeakPassword> list = this.list();
        List<String> collect = list.stream().map(AsCompanyWeakPassword::getWeakPassword).collect(Collectors.toList());
        redisService.set(CompanyPasswordConstant.COMPANY_WEAK_PASSWORD, collect);
        return collect;
    }
}
