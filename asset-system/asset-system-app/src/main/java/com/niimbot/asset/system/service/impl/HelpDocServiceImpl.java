package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsHelpDocMapper;
import com.niimbot.asset.system.model.AsHelpDoc;
import com.niimbot.asset.system.service.HelpDocService;
import com.niimbot.system.AssetHelpDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/9/8 上午11:45
 */
@Slf4j
@Service
public class HelpDocServiceImpl extends ServiceImpl<AsHelpDocMapper, AsHelpDoc> implements HelpDocService {

    private static final ScheduledExecutorService scheduleExecutor = Executors.newScheduledThreadPool(1);

    //帮助文档配置缓存
    private List<AsHelpDoc> helpDocCache = null;

    @PostConstruct
    public void init() {
        allHelpDoc();
        scheduleExecutor.scheduleAtFixedRate(this::allHelpDoc, 3, 6, TimeUnit.HOURS);
    }

    private void allHelpDoc() {
        helpDocCache = this.list();
    }

    @Override
    public List<AssetHelpDto> queryByTitle(String kw) {
        if (StrUtil.isBlank(kw)) {
            return helpDocCache.stream().map(item -> {
                AssetHelpDto helpDto = new AssetHelpDto();
                helpDto.setId(item.getId());
                helpDto.setTitle(item.getTitle());
                helpDto.setPath(item.getPath());
                helpDto.setType(2);
                return helpDto;
            }).collect(Collectors.toList());
        }

        return helpDocCache.stream().filter(item -> item.getTitle().contains(kw)).map(item -> {
            AssetHelpDto helpDto = new AssetHelpDto();
            helpDto.setId(item.getId());
            helpDto.setTitle(item.getTitle());
            helpDto.setPath(item.getPath());
            helpDto.setType(2);
            return helpDto;
        }).collect(Collectors.toList());
    }
}
