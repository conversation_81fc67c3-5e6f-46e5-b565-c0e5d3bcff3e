package com.niimbot.asset.system.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.system.model.AsCountryCode;
import com.niimbot.asset.system.service.AsCountryCodeService;
import com.niimbot.system.CountryCodeQueryDto;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("server/system/asCountryCode")
@Slf4j
public class AsCountryCodeServiceController {

    @Resource
    private AsCountryCodeService countryCodeService;

    @PostMapping(value = "/list")
    public List<AsCountryCode> page(@RequestBody CountryCodeQueryDto dto) {
        return countryCodeService.list(new LambdaQueryWrapper<AsCountryCode>()
                .eq(Objects.nonNull(dto.getChooseStatus()),AsCountryCode::getChooseStatus,dto.getChooseStatus()));
    }

    @PostMapping(value = "/save")
    public boolean save(@RequestBody CountryCodeQueryDto dto) {
        return countryCodeService.updateStatusByIds(dto.getChooseStatus(),dto.getCountryIds());
    }

    @PostMapping(value = "/queryCode")
    public Integer queryCode(@RequestParam("nationalCode") String nationalCode) {
        return Convert.toInt(countryCodeService.count(new LambdaQueryWrapper<AsCountryCode>()
                .eq(AsCountryCode::getChooseStatus,1)
                .eq(AsCountryCode::getCountryCode, nationalCode)));
    }
}
