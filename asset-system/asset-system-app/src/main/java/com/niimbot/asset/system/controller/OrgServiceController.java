package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.SerialNumberUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.ImportErrorAbs;
import com.niimbot.asset.system.dto.ImportErrorDeleteCmd;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.EditRootOrg;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgExportDto;
import com.niimbot.system.OrgImportDto;
import com.niimbot.system.OrgQueryDto;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 20201111
 * @description 企业组织结构
 */
@Slf4j
@RestController
@RequestMapping("server/system/org")
@RequiredArgsConstructor
public class OrgServiceController {
    private final OrgService orgService;
    private final ImportErrorAbs importErrorAbs;

    /**
     * 添加组织
     *
     * @param org 组织
     * @return 是否成功
     */
    @PostMapping
    public Boolean add(@RequestBody AsOrg org) {
        return this.orgService.add(org);
    }

    @PostMapping("v2")
    public String addV2(@RequestBody AsOrg org) {
        return this.orgService.addV2(org);
    }

    /**
     * 修改组织结构
     *
     * @param org 组织
     * @return 是否成功
     */
    @PutMapping
    public Boolean edit(@RequestBody AsOrg org) {
        return this.orgService.edit(org);

    }

    @PutMapping("/editRootOrg")
    public Boolean editRootOrg(@RequestBody EditRootOrg org) {
        return this.orgService.editRootOrg(org);
    }

    /**
     * 查询获取组织结构
     *
     * @param orgId 组织Id
     * @return AsArea信息
     */
    @GetMapping(value = "/{orgId}")
    public AsOrg getInfo(@PathVariable(value = "orgId") Long orgId) {
        return orgService.getById(orgId);
    }

    /**
     * 删除组织结构
     *
     * @param orgIds 组织结构Id
     * @return 是否成功
     */
    @DeleteMapping
    public Boolean remove(@RequestBody List<Long> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        // 不允许删除最后一条记录
        long count = orgService.count(new QueryWrapper<>());
        if (orgIds.size() >= count) {
            throw new BusinessException(SystemResultCode.LAST_RECORD_DELETE_FAIL);
        }
        Edition.weixin(() -> {
            long empCount = Db.count(
                    Wrappers.lambdaQuery(AsUserOrg.class)
                            .in(AsUserOrg::getOrgId, orgIds)
            );
            if (empCount > 0) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "请先将当前组织或子组织关联员工变更至其他部门，再进行删除");
            }
        });
        return orgService.remove(orgIds);
    }

    /**
     * 分页查询组织列表
     *
     * @param queryDto 组织对象
     * @return pageUtils对象
     */
    @GetMapping(value = "/page")
    public IPage<OrgDto> page(OrgQueryDto queryDto) {
        return orgService.orgPage(queryDto);
    }

    /**
     * 查询组织列表list集合
     *
     * @param queryDto 参数实体
     * @return 组织列表
     */
    @GetMapping(value = "/list")
    public List<OrgDto> list(OrgQueryDto queryDto) {
        // 模糊查询组织名称
        return orgService.orgList(queryDto);
    }

    /**
     * 查询组织列表list集合
     *
     * @param queryDto 参数实体
     * @return 组织列表
     */
    /*@PostMapping(value = "/countEmp/list")
    public List<OrgDto> countEmpList(@RequestParam(value = "countEmp", required = false) Boolean countEmp,
                                     @RequestBody OrgQueryDto queryDto) {
        return orgService.countEmpList(queryDto);
    }*/

    /**
     * 查询组织列表list集合[带权限]
     *
     * @param queryDto 参数实体
     * @return 组织列表
     */
   /* @PostMapping(value = "/countEmp/list/permission")
    public List<OrgDto> countPermsEmpList(@RequestParam(value = "countEmp", required = false) Boolean countEmp,
                                          @RequestBody OrgQueryDto queryDto) {
        return orgService.countPermsEmpList(queryDto);
    }*/

    /**
     * 查询存放区域组织列表list集合[带权限]
     *
     * @return 组织列表
     */
    @GetMapping(value = "/area/list/permission")
    public List<OrgDto> areaPermsList() {
        return orgService.areaPermsList();
    }

    /**
     * 查询耗材仓库组织列表list集合[带权限]
     *
     * @return 组织列表
     */
    @GetMapping(value = "/store/list/permission")
    public List<OrgDto> storePermsList() {
        return orgService.storePermsList();
    }

    /**
     * 获取推荐区域编码
     *
     * @return 编码
     */
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return SerialNumberUtils.getMaxCode(orgService);
    }

    /**
     * 查询获取组织结构
     */
    @GetMapping(value = "/rootOrg")
    public AsOrg getRootOrg() {
        return orgService.getOne(Wrappers.lambdaQuery(AsOrg.class)
                .eq(AsOrg::getPid, 0L)
                .eq(AsOrg::getOrgType, AssetConstant.ORG_TYPE_COMPANY), false);
    }

    /**
     * 根据组织id集合查询组织
     *
     * @param orgIds
     * @return 组织列表
     */
    @PostMapping(value = "/listByIds")
    public List<AsOrg> listByIds(@RequestBody List<Long> orgIds) {
        return orgService.list(Wrappers.<AsOrg>lambdaQuery()
                .in(AsOrg::getId, orgIds));
    }

    @PostMapping(value = "/export")
    public List<OrgExportDto> getExcelData(@RequestBody OrgQueryDto queryDto) {
        return orgService.getExcelData(queryDto);
    }

    /**
     * 错误记录
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return orgService.importError(taskId);
    }

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        orgService.saveSheetHead(importErrorDto);
    }

    /**
     * 导入耗材数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody OrgImportDto importDto) {
        return orgService.saveSheetData(importDto);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return importErrorAbs.deleteImportError(new ImportErrorDeleteCmd().setTaskId(taskId));
    }

    @PutMapping("/sort")
    public Boolean sort(@RequestBody List<Long> orgIds) {
        return orgService.sort(orgIds);
    }

    @PostMapping("/listByCodes")
    public List<AsOrg> listByCodes(@RequestBody List<String> codes) {
        return orgService.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .in(AsOrg::getOrgCode, codes)
        );
    }

    /**
     * 内部服务，通过code或者name查询
     *
     * @return
     */
    @GetMapping("getOne")
    public Long getOne(@RequestParam(value = "orgName", required = false) String orgName,
                       @RequestParam(value = "orgCode", required = false) String orgCode) {
        return orgService.getOne(orgName, orgCode);
    }

    @GetMapping("/openApi/list")
    public IPage<AsOrg> openApiList(@RequestParam("pageNum") int pageNum,
                                    @RequestParam("pageSize") int pageSize) {
        return orgService.page(new Page<>(pageNum, pageSize),
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, LoginUserThreadLocal.getCompanyId()));
    }

}
