package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.service.AsUserOrgService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/23 11:57
 */
@RestController
@RequestMapping("server/system/userOrg")
@RequiredArgsConstructor
public class UserOrgServiceController {
    private final AsUserOrgService userOrgService;

    @GetMapping(value = "/{userId}")
    public List<AsUserOrg> getByUserId(@PathVariable("userId") Long userId) {
        return userOrgService.list(Wrappers.<AsUserOrg>lambdaQuery().eq(AsUserOrg::getUserId, userId));
    }

    @PostMapping(value = "/getByOrgs")
    public List<AsUserOrg> getByOrgs(@RequestBody List<Long> orgIds) {
        return userOrgService.list(Wrappers.<AsUserOrg>lambdaQuery().in(AsUserOrg::getOrgId, orgIds));
    }
}
