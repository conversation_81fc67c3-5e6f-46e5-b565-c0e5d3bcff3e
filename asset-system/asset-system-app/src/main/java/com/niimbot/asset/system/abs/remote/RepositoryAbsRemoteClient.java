package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.RepositoryAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/10 18:01
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.material.domain.abs.impl.RepositoryAbsImpl")
@FeignClient(name = "asset-material", url = "https://{gateway}/client/abs/material/repositoryAbs/")
public interface RepositoryAbsRemoteClient extends RepositoryAbs {
}
