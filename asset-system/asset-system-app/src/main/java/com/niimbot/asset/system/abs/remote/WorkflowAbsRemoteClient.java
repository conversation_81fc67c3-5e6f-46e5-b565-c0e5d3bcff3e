package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.WorkflowAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:31
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.activiti.domain.abs.impl.WorkflowAbsImpl")
@FeignClient(name = "asset-activiti", url = "https://{gateway}/client/abs/activiti/workflowAbs/")
public interface WorkflowAbsRemoteClient extends WorkflowAbs {
}
