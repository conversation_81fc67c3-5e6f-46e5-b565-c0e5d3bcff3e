package com.niimbot.asset.system.lisenter;

import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.event.CompanyRegisterEvent;
import com.niimbot.asset.system.mq.CrmMessageProducer;
import com.niimbot.system.crm.CrmPushEvent;

import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Profile({Edition.SAAS, Edition.DING, Edition.WEIXIN})
public class CompanyRegisterEventListener implements ApplicationListener<CompanyRegisterEvent> {

    @Resource
    private CrmMessageProducer crmMessageProducer;

    @Override
    public void onApplicationEvent(CompanyRegisterEvent event) {
        Long companyId = (Long) event.getSource();
        // CRM发送推送线索
        try {
            crmMessageProducer.sendCrmPushMessage(companyId, event.getXsCode(), event.getRegisterMobile(), CrmPushEvent.REGISTER);
        } catch (Exception e) {
            log.warn("发送CRM消息失败", e);
        }
    }
}
