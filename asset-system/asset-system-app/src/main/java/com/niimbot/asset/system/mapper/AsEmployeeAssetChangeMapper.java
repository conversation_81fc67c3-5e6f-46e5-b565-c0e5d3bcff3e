package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.system.model.AsEmployeeAssetChange;
import com.niimbot.system.CusEmployeeChangeQueryDto;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 员工资产异动记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface AsEmployeeAssetChangeMapper extends BaseMapper<AsEmployeeAssetChange> {

    IPage<AsEmployeeAssetChange> page(@Param("page") Page<Object> buildIPage, @Param("ew") CusEmployeeChangeQueryDto query);
}
