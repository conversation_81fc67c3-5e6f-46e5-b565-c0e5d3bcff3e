package com.niimbot.asset.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.AccountCenterService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.*;
import com.niimbot.thirdparty.ThirdPartyUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/system/server/account/center")
@RequiredArgsConstructor
public class AccountCenterServiceController {

    private final RedissonClient redissonClient;

    private final AccountCenterService accountCenterService;

    @GetMapping("/by/id/{id}")
    public AccountDto getAccountById(@PathVariable Long id) {
        AsCusUser account = accountCenterService.getAccountById(id);
        if (Objects.isNull(account)) {
            return null;
        }
        return BeanUtil.copyProperties(account, AccountDto.class);
    }

    @GetMapping("/by/mobile/{mobile}")
    public AccountDto getAccountByMobile(@PathVariable String mobile) {
        AsCusUser account = accountCenterService.getAccountByMobile(mobile);
        return BeanUtil.copyProperties(account, AccountDto.class);
    }

    @GetMapping("/by/no/{no}")
    public AccountDto getAccountByNo(@PathVariable String no) {
        AsCusUser account = accountCenterService.getAccountByNo(no);
        return BeanUtil.copyProperties(account, AccountDto.class);
    }

    @GetMapping("/by/email/{email}")
    public AccountDto getAccountByEmail(@PathVariable String email) {
        AsCusUser account = accountCenterService.getAccountByEmail(email);
        if (Objects.isNull(account)) {
            return null;
        }
        return BeanUtil.copyProperties(account, AccountDto.class);
    }

    @GetMapping("/by/way/{way}")
    public AccountDto getAccountByWay(@PathVariable String way) {
        AsCusUser account = accountCenterService.getAccountByWay(way);
        return BeanUtil.copyProperties(account, AccountDto.class);
    }

    @GetMapping("/by/way2/{way}")
    public AccountDto getAccountByWay2(@PathVariable String way) {
        AsCusUser account;
        try {
            account = accountCenterService.getAccountByWay(way);
            if (Objects.isNull(account)) return null;
        } catch (Exception e) {
            log.warn("获取账号信息错误[{}]", way, e);
            return null;
        }
        return BeanUtil.copyProperties(account, AccountDto.class);
    }

    @GetMapping("/byThirdPartyUserUnionId/{type}/{unionId}")
    public AccountDto getAccountByThirdPartyUserUnionId(@PathVariable String type, @PathVariable String unionId) {
        AsCusUser account = accountCenterService.getAccountByThirdPartyUserUnionId(type, unionId);
        return BeanUtil.copyProperties(account, AccountDto.class);
    }

    public <T> T mayCreateAccountLock(String mobile, Supplier<T> supplier) {
        // 全局锁
        RLock lock = redissonClient.getLock(RedisConstant.createAccountLockKey(mobile));
        if (lock.isLocked()) {
            throw new BusinessException(SystemResultCode.PARAM_REPEAT_SUBMIT);
        }
        lock.lock();
        try {
            return supplier.get();
        } finally {
            lock.unlock();
        }
    }

    @GetMapping("/getLoginInfoByMobile/{mobile}/{smsCode}")
    public CusUserDto getLoginInfoByMobile(@PathVariable String mobile, @PathVariable String smsCode) {
        log.info("手机验证码登录 : {}", mobile);
        return mayCreateAccountLock(mobile, () -> accountCenterService.getLoginInfoByMobile(mobile, smsCode));
    }

    @GetMapping("/getLoginInfoByNo/{no}")
    public CusUserDto getLoginInfoByNo(@PathVariable String no) {
        return accountCenterService.getLoginInfoByNo(no);
    }

    @GetMapping("/getLoginInfoByEmail/{email}")
    public CusUserDto getLoginInfoByEmail(@PathVariable String email) {
        return accountCenterService.getLoginInfoByEmail(email);
    }

    @GetMapping("/getLoginInfoByWay/{way}")
    public CusUserDto getLoginInfoByWay(@PathVariable String way) {
        return accountCenterService.getLoginInfoByWay(way);
    }

    @GetMapping("/getLoginInfoByAppKey/{appKey}/{appSecret}")
    public CusUserDto getLoginInfoByAppKey(@PathVariable("appKey") String appKey,
                                           @PathVariable("appSecret") String appSecret) {
        return accountCenterService.getLoginInfoByAppKey(appKey, appSecret);
    }

    @GetMapping("/getLoginInfoByThirdParty/{type}/{uniqueId}")
    public CusUserDto getLoginInfoByThirdParty(@PathVariable String type, @PathVariable String uniqueId) {
        return accountCenterService.getLoginInfoByThirdParty(type, uniqueId);
    }

    @GetMapping("/getLoginInfoByThirdPartyInnerApp/{type}/{userId}")
    public CusUserDto getLoginInfoByThirdPartyInnerApp(@PathVariable String type, @PathVariable String userId) {
        return accountCenterService.getLoginInfoByThirdPartyInnerApp(type, userId);
    }

    @GetMapping("/getEmpLoginInfo/{empId}")
    public CusUserDto getEmpLoginInfo(@PathVariable Long empId) {
        return accountCenterService.getEmpLoginInfo(empId);
    }

    @GetMapping("/getCompanyEmpLoginInfo/{accountId}/{companyId}")
    public CusUserDto getCompanyEmpLoginInfo(@PathVariable Long accountId, @PathVariable Long companyId) {
        return accountCenterService.getCompanyEmpLoginInfo(accountId, companyId);
    }

    @DeleteMapping("/deleteEmployeeAccount/{empId}")
    public Boolean deleteEmployeeAccount(@PathVariable Long empId) {
        return accountCenterService.deleteEmployeeAccount(empId);
    }

    @DeleteMapping("/deleteCompanyAccount/{companyId}/{accountId}")
    public Boolean deleteCompanyAccount(@PathVariable Long companyId, @PathVariable Long accountId) {
        return accountCenterService.deleteCompanyAccount(companyId, accountId);
    }

    @DeleteMapping("/batchDeleteEmployeeAccount")
    public Boolean batchDeleteEmployeeAccount(@RequestBody List<Long> empIds) {
        return accountCenterService.batchDeleteEmployeeAccount(empIds);
    }

    @PostMapping("/activateAccountByMobile")
    public ActivateAccountResult activateAccountByMobile(@RequestBody ActivateAccount activateAccount) {
        log.info("手机验证码激活账号 : {}", activateAccount.getMobile());
        return mayCreateAccountLock(activateAccount.getMobile(), () -> accountCenterService.activateAccountByMobile(activateAccount.getCompanyId(), activateAccount.getMobile(), activateAccount.getNationalCode(), activateAccount.getSmsCode()));
    }

    @PostMapping("/activateAccountByThirdParty")
    public ActivateAccountResult activateAccountByThirdParty(@RequestBody ActivateAccount activateAccount) {
        return accountCenterService.activateAccountByThirdParty(
                activateAccount.getCompanyId(),
                activateAccount.getThirdType(),
                activateAccount.getThirdPartyUser()
        );
    }

    @PostMapping("/activateAccountByThirdPartyBindMobile")
    public ActivateAccountResult activateAccountByThirdPartyBindMobile(@RequestBody ActivateAccount activateAccount) {
        log.info("扫码认证后激活账号 : {}", activateAccount.getMobile());
        return mayCreateAccountLock(activateAccount.getMobile(), () -> accountCenterService.activateAccountByThirdPartyBindMobile(
                activateAccount.getCompanyId(),
                activateAccount.getMobile(),
                activateAccount.getNationalCode(),
                activateAccount.getSmsCode(),
                activateAccount.getUuid()
        ));
    }

    @PostMapping("/bindAccountForThirdParty")
    public CusUserDto bindAccountForThirdParty(@RequestBody ThirdPartyUser thirdPartyUser) {
        return accountCenterService.bindAccountForThirdParty(
                thirdPartyUser.getAccountId(),
                thirdPartyUser.getType(),
                thirdPartyUser.getOpenId(),
                thirdPartyUser.getUniqueId(),
                thirdPartyUser.getNickname(),
                thirdPartyUser.getAvatarUrl()
        );
    }

    @PostMapping("/bindOrCreateAccountThirdPartyByMobile/{mobile}/{smsCode}")
    public CusUserDto bindOrCreateAccountThirdPartyByMobile(@PathVariable String mobile, @PathVariable String smsCode, @RequestBody ThirdPartyUser thirdPartyUser) {
        log.info("扫码认证后创建或激活账号 : {}", mobile);
        return mayCreateAccountLock(mobile, () -> accountCenterService.bindOrCreateAccountThirdPartyByMobile(
                mobile,
                smsCode,
                thirdPartyUser.getType(),
                thirdPartyUser.getOpenId(),
                thirdPartyUser.getUniqueId(),
                thirdPartyUser.getNickname(),
                thirdPartyUser.getAvatarUrl()
        ));
    }

    @PostMapping("/bindMobile")
    public Boolean bindMobile(@RequestBody AccountBind accountBind) {
        return accountCenterService.bindMobile(accountBind.getAccountId(), accountBind.getMobile());
    }

    @PostMapping("/bindEmail")
    public Boolean bindEmail(@RequestBody AccountBind accountBind) {
        return accountCenterService.bindEmail(accountBind.getAccountId(), accountBind.getEmail());
    }

    @PostMapping("/bindThirdParty")
    public Boolean bindThirdParty(@RequestBody ThirdPartyUser thirdPartyUser) {
        return accountCenterService.bindThirdParty(thirdPartyUser);
    }

    @PutMapping("/unbind/{way}/{accountId}")
    public Boolean unbindWay(@PathVariable Integer way, @PathVariable Long accountId) {
        return accountCenterService.unbindWay(way, accountId);
    }

    @PutMapping("/unbindThirdParty/{type}/{accountId}")
    public Boolean unbindThirdParty(@PathVariable String type, @PathVariable Long accountId) {
        return accountCenterService.unbindThirdParty(type, accountId);
    }

    @GetMapping("/companyList/{accountId}")
    public List<AccountEmployeeDto> companyList(@PathVariable Long accountId) {
        return accountCenterService.accountCompanyList(accountId);
    }

    @GetMapping("/basicInfo/{accountId}/{employeeId}")
    public AccountBasicInfo getAccountBasicInfo(@PathVariable("accountId") Long accountId, @PathVariable("employeeId") Long employeeId) {
        return accountCenterService.getAccountBasicInfo(accountId, employeeId);
    }

    @PutMapping("/updatePwdAndNickname")
    public Boolean updatePwdAndNickname(@RequestBody AccountDto dto) {
        return accountCenterService.updateAccountPwdAndNickname(dto);
    }

    @PutMapping("/updateLastCompany/{accountId}/{companyId}")
    public void updateLastCompany(@PathVariable Long accountId, @PathVariable Long companyId) {
        accountCenterService.updateLastCompany(accountId, companyId);
    }

    @PostMapping("/sendInviteLink/{companyId}/{operatorId}")
    public InviteLink sendInviteLink(@PathVariable Long companyId, @PathVariable Long operatorId) {
        return accountCenterService.sendInviteLink(companyId, operatorId);
    }

    @PutMapping("/preActivatedAccount/{companyId}/{empId}")
    public Boolean preActivatedAccount(@PathVariable Long companyId, @PathVariable Long empId) {
        return accountCenterService.preActivatedAccount(companyId, empId);
    }

    @PutMapping("/updateNickname")
    public Boolean updateNickname(@RequestBody AccountDto dto) {
        return accountCenterService.updateNickname(dto.getId(), dto.getNickname());
    }
}
