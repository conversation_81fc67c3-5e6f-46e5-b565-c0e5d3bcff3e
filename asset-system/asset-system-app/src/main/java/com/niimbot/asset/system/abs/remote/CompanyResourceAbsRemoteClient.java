package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.CompanyResourceAbs;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/7 16:11
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.sale.domain.abs.impl.CompanyResourceAbsImpl")
@FeignClient(name = "asset-sale", url = "https://{gateway}/client/abs/sale/companyResourceAbs/")
public interface CompanyResourceAbsRemoteClient extends CompanyResourceAbs {
}
