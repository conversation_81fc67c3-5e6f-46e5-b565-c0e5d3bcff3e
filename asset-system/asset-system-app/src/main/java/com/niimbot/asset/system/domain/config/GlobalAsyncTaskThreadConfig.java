package com.niimbot.asset.system.domain.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务线程池定义，注意提交的任务执行耗时不要太长，防止任务堵塞，定时任务得不到执行，太长另建线程池
 * <AUTHOR>
 * @date 2023/1/9 下午3:40
 */
@Configuration
@EnableAsync
@Slf4j
public class GlobalAsyncTaskThreadConfig {

    @Bean
    public Executor asyncThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程池大小
        executor.setCorePoolSize(5);
        //最大线程数
        executor.setMaxPoolSize(10);
        //队列容量
        executor.setQueueCapacity(10);
        //活跃时间
        executor.setKeepAliveSeconds(60);
        //线程名字前缀
        executor.setThreadNamePrefix("GlobalAsyncThreadPool-");
        //线程池的关闭，等待所有任务清空并完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //销毁等待时间，保证线程全部完成60s后一定销毁
        executor.setAwaitTerminationSeconds(60);
        //设置拒绝策略
        //CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行，转回主线程
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        log.info("GlobalAsyncTaskThreadConfig asyncThreadPool init success");
        return executor;
    }

    @Bean
    public Executor asyncScheduledThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程池大小
        executor.setCorePoolSize(10);
        //最大线程数
        executor.setMaxPoolSize(20);
        //队列容量
        executor.setQueueCapacity(1000);
        //活跃时间
        executor.setKeepAliveSeconds(60);
        //线程名字前缀
        executor.setThreadNamePrefix("GlobalAsyncScheduledThreadPool-");
        //线程池的关闭，等待所有任务清空并完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //销毁等待时间，保证线程全部完成60s后一定销毁
        executor.setAwaitTerminationSeconds(60);
        //设置拒绝策略
        //CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行，转回主线程
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        log.info("GlobalAsyncTaskThreadConfig asyncScheduledThreadPool init success");
        return executor;
    }

}
