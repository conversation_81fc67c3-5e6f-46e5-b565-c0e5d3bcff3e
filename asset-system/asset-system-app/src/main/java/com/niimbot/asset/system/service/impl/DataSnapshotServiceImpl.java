package com.niimbot.asset.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.dto.DataSnapshotGetQry;
import com.niimbot.asset.system.dto.DataSourceIdsGetQry;
import com.niimbot.asset.system.mapper.AsDataSnapshotMapper;
import com.niimbot.asset.system.model.AsDataSnapshot;
import com.niimbot.asset.system.service.DataSnapshotService;
import com.niimbot.means.StoreSnapshotSearch;
import com.niimbot.system.DataSnapshotSave;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DataSnapshotServiceImpl extends ServiceImpl<AsDataSnapshotMapper, AsDataSnapshot> implements DataSnapshotService {

    @Override
    public PageUtils<JSONObject> search(StoreSnapshotSearch search) {
        DataSnapshotGetQry qry = new DataSnapshotGetQry().setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setSourceType(search.getSourceType()).setSourceId(search.getSourceId());
        LambdaQueryWrapper<AsDataSnapshot> wrapper = queryWrapper(qry);
        wrapper.select(AsDataSnapshot::getSnapshot, AsDataSnapshot::getCreateTime);
        Page<AsDataSnapshot> result = this.page(search.buildIPage(), wrapper);
        List<JSONObject> json = result.getRecords().stream().map(v -> {
            JSONObject snapshot = v.getSnapshot();
            snapshot.put("storeTime", v.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            return snapshot;
        }).collect(Collectors.toList());
        return new PageUtils<>(
                json, Convert.toInt(result.getTotal()), Convert.toInt(search.getPageSize()), Convert.toInt(search.getPageNum())
        );
    }

    @Override
    public List<Long> getSourceIds(DataSourceIdsGetQry qry) {
        return this.getBaseMapper().selectSourceIds(qry);
    }

    private LambdaQueryWrapper<AsDataSnapshot> queryWrapper(DataSnapshotGetQry qry) {
        return Wrappers.lambdaQuery(AsDataSnapshot.class)
                .eq(AsDataSnapshot::getCompanyId, qry.getCompanyId())
                .eq(AsDataSnapshot::getSourceId, qry.getSourceId())
                .eq(Objects.nonNull(qry.getSourceType()), AsDataSnapshot::getSourceType, qry.getSourceType())
                .eq(Objects.nonNull(qry.getDataType()), AsDataSnapshot::getDataType, qry.getDataType());
    }

    @Override
    public List<String> getSnapshotIds(DataSnapshotGetQry qry) {
        LambdaQueryWrapper<AsDataSnapshot> wrapper = queryWrapper(qry);
        wrapper.select(AsDataSnapshot::getDataId);
        return this.list(wrapper).stream().map(AsDataSnapshot::getDataId).collect(Collectors.toList());
    }

    @Override
    public List<JSONObject> getSnapshots(DataSnapshotGetQry qry) {
        LambdaQueryWrapper<AsDataSnapshot> wrapper = queryWrapper(qry);
        wrapper.select(AsDataSnapshot::getSnapshot);
        return this.list(wrapper).stream().map(AsDataSnapshot::getSnapshot).collect(Collectors.toList());
    }

    private List<AsDataSnapshot> toDataSnapshot(DataSnapshotSave saved) {
        if (Objects.isNull(saved.getSourceId()) || Objects.isNull(saved.getSourceType()) || CollUtil.isEmpty(saved.getSnapshots())) {
            return Collections.emptyList();
        }
        return saved.getSnapshots().stream().map(v -> new AsDataSnapshot()
                .setId(IdUtils.getId()).setCompanyId(saved.getCompanyId())
                .setSourceId(saved.getSourceId()).setSourceType(saved.getSourceType())
                .setDataId(v.getDataId()).setDataType(v.getDataType()).setSnapshot(v.getDataSnapshot())
                .setCreateTime(LocalDateTime.now())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveSnapshots(DataSnapshotSave saved) {
        List<AsDataSnapshot> snapshots = toDataSnapshot(saved);
        if (CollUtil.isNotEmpty(snapshots)) {
            this.saveBatch(snapshots);
        }
        return Convert.toInt(this.count(
                Wrappers.lambdaQuery(AsDataSnapshot.class)
                        .eq(AsDataSnapshot::getCompanyId, saved.getCompanyId())
                        .eq(AsDataSnapshot::getSourceId, saved.getSourceId())
        ));
    }

}
