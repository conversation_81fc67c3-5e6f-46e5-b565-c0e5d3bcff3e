package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.dto.DataSourceIdsGetQry;
import com.niimbot.asset.system.model.AsDataSnapshot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AsDataSnapshotMapper extends BaseMapper<AsDataSnapshot> {

    List<Long> selectSourceIds(@Param("em") DataSourceIdsGetQry qry);

}
