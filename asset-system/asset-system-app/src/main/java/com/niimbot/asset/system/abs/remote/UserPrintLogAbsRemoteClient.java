package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.UserPrintLogAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/7 11:17
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.UserPrintLogAbsImpl")
@FeignClient(name = "asset-means", url = "https://{gateway}/client/abs/means/userPrintLogAbs/")
public interface UserPrintLogAbsRemoteClient extends UserPrintLogAbs {
}
