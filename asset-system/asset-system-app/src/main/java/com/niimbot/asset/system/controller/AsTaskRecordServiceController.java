package com.niimbot.asset.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.niimbot.asset.system.model.AsTaskRecord;
import com.niimbot.asset.system.service.AsTaskRecordService;
import com.niimbot.system.TaskRecordDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/system/task/record")
@RequiredArgsConstructor
public class AsTaskRecordServiceController {

    private final AsTaskRecordService taskRecordService;

    @PostMapping
    public boolean addTaskRecord(@RequestBody TaskRecordDto dto) {
        AsTaskRecord record = BeanUtil.copyProperties(dto, AsTaskRecord.class);
        return taskRecordService.save(record);
    }

}
