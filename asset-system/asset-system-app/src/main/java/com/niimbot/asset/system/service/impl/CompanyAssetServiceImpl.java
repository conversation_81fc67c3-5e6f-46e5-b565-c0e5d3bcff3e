package com.niimbot.asset.system.service.impl;

import com.niimbot.asset.system.mapper.CompanyAssetMapper;
import com.niimbot.asset.system.service.CompanyAssetService;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CompanyAssetServiceImpl implements CompanyAssetService {

    private final CompanyAssetMapper mapper;

    @Override
    public List<Long> checkUseOrg(List<Long> orgIds, Long companyId) {
        return mapper.checkUseOrg(orgIds, companyId);
    }

    @Override
    public List<Long> checkOrgOwner(List<Long> orgIds, Long companyId) {
        return mapper.checkOrgOwner(orgIds, companyId);
    }

    @Override
    public List<Long> checkUsePerson(List<Long> userIds, Long companyId) {
        return mapper.checkUsePerson(userIds, companyId);
    }

    @Override
    public List<Long> checkManagerOwner(List<Long> userIds, Long companyId) {
        return mapper.checkManagerOwner(userIds, companyId);
    }

    @Override
    public void updateAreaOrgId(Long oldId, Long newId) {
        mapper.updateAreaOrgId(oldId, newId);
    }

    @Override
    public void updateRepositoryOrgId(Long oldId, Long newId) {
        mapper.updateRepositoryOrgId(oldId, newId);
    }

    @Override
    public Set<Long> checkOrgAsset(List<Long> removeOrgIds, Long companyId) {
        if (CollUtil.isEmpty(removeOrgIds)) {
            return Collections.emptySet();
        }
        // 使用组织
        List<Long> useOrgIds = this.checkUseOrg(removeOrgIds, companyId);
        // 管理组织
        List<Long> orgOwnerIds = this.checkOrgOwner(removeOrgIds, companyId);
        useOrgIds.addAll(orgOwnerIds);
        return new HashSet<>(useOrgIds);
    }
}
