package com.niimbot.asset.system.ots.process;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.dto.clientobject.ExternalRelation;
import com.niimbot.asset.system.dto.clientobject.OrgCO;
import com.niimbot.asset.system.mapstruct.SystemMapStruct;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.ots.SystemOrgOts;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.handler.WeixinAdapter;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemOrgOts/")
@RequiredArgsConstructor
public class SystemOrgOtsProcessClient implements SystemOrgOts {

    private final com.niimbot.asset.system.service.OrgService orgService;

    private final AsUserOrgService empOrgService;

    private final SystemMapStruct systemMapStruct;

    @Resource
    private AsCusEmployeeService asCusEmployeeService;

    @Override
    public OrgCO getById(Long id) {
        AsOrg org = orgService.getById(id);
        return systemMapStruct.convertOrgDoToCo(org);
    }

    @Override
    public List<Long> getEmpOrgIds(Long empId) {
        List<AsUserOrg> userOrgs = empOrgService.list(
                Wrappers.lambdaQuery(AsUserOrg.class)
                        .select(AsUserOrg::getOrgId)
                        .eq(AsUserOrg::getUserId, empId)
        );
        return CollUtil.isEmpty(userOrgs) ? Collections.emptyList() : userOrgs.stream().map(AsUserOrg::getOrgId).collect(Collectors.toList());
    }

    @Override
    public List<OrgCO> getEmpOrgs(Long empId) {
        List<Long> orgIds = getEmpOrgIds(empId);
        if (CollUtil.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        return orgService.listByIds(orgIds).stream().map(systemMapStruct::convertOrgDoToCo).collect(Collectors.toList());
    }

    @Override
    public List<ExternalRelation> getOrgExternalRelation(Long companyId) {
        return orgService.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getExternalOrgId)
                        .eq(AsOrg::getCompanyId, companyId)
        ).stream().map(v -> {
            ExternalRelation relation = new ExternalRelation();
            relation.setId(v.getId());
            relation.setExternalId(v.getExternalOrgId());
            return relation;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ExternalRelation> getOrgExternalRelation(Long companyId, List<String> externalIds) {
        if (CollUtil.isEmpty(externalIds)) {
            return Collections.emptyList();
        }
        return orgService.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getExternalOrgId)
                        .eq(AsOrg::getCompanyId, companyId)
                        .in(AsOrg::getExternalOrgId, externalIds)
        ).stream().map(v -> new ExternalRelation(v.getId(), v.getExternalOrgId())).collect(Collectors.toList());
    }

    @Override
    public List<AsOrg> listOrgPermission(String kw) {
        if (Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).orgSearch(kw);
            return orgService.list(
                    Wrappers.lambdaQuery(AsOrg.class)
                            .like(AsOrg::getOrgName, kw).or().like(AsOrg::getOrgCode, kw)
                            .or(CollUtil.isNotEmpty(unionIds), wrapper -> wrapper.in(AsOrg::getOrgName, unionIds)));
        } else {
            return orgService.list(
                    Wrappers.lambdaQuery(AsOrg.class)
                            .like(AsOrg::getOrgName, kw)
                            .or()
                            .like(AsOrg::getOrgCode, kw));
        }
    }

    @Override
    public List<AsCusEmployee> listEmpPermission(String kw) {
        if (Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).empSearch(kw);
            return asCusEmployeeService.list(Wrappers.lambdaQuery(AsCusEmployee.class)
                    .like(AsCusEmployee::getEmpName, kw).or().like(AsCusEmployee::getEmpNo, kw)
                    .or(CollUtil.isNotEmpty(unionIds), wrapper -> wrapper.in(AsCusEmployee::getEmpName, unionIds)));
        } else {
            return asCusEmployeeService.list(Wrappers.lambdaQuery(AsCusEmployee.class)
                    .like(AsCusEmployee::getEmpName, kw).or().like(AsCusEmployee::getEmpNo, kw));
        }
    }

    @Override
    public String getExternalId(Long id) {
        return orgService.getExternalId(id);
    }
}
