package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsOpenApiEventMapper;
import com.niimbot.asset.system.model.AsOpenApiEvent;
import com.niimbot.asset.system.service.AsOpenApiEventService;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/18 11:09
 */
@Service
@RequiredArgsConstructor
public class AsOpenApiEventServiceImpl extends ServiceImpl<AsOpenApiEventMapper, AsOpenApiEvent> implements AsOpenApiEventService {
}
