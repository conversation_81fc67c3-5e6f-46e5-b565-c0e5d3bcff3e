package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.AsRecommendRecordService;
import com.niimbot.system.RecommendRecordDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/2 11:09
 */
@Slf4j
@RestController
@RequestMapping("server/system/recommend")
@RequiredArgsConstructor
public class RecommendRecordServiceController {

    private final AsRecommendRecordService recommendRecordService;

    @GetMapping("/register/list")
    public List<RecommendRecordDto> registerList(@RequestParam("recommendEmpId") Long recommendEmpId) {
        return recommendRecordService.list(recommendEmpId, 1);
    }

    @GetMapping("/pay/list")
    public List<RecommendRecordDto> payList(@RequestParam("recommendEmpId") Long recommendEmpId) {
        return recommendRecordService.list(recommendEmpId, 2);
    }

    @PostMapping(value = "/verifyMobile")
    public Boolean verifyMobile(@RequestParam("recommendEmpId") Long recommendEmpId,
                                @RequestParam("mobile") String mobile) {
        recommendRecordService.verifyMobile(recommendEmpId, mobile);
        return true;
    }
}
