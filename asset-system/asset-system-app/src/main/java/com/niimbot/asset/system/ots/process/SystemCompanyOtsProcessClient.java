package com.niimbot.asset.system.ots.process;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.system.dto.CompanySettingTodoConfigEditCmd;
import com.niimbot.asset.system.dto.CompleteNewbieTaskCmd;
import com.niimbot.asset.system.dto.clientobject.CompanySettingCO;
import com.niimbot.asset.system.mapstruct.SystemMapStruct;
import com.niimbot.asset.system.model.AsCompanyNewbieTask;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsNewbieTaskConfig;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.ots.SystemCompanyOts;
import com.niimbot.asset.system.service.CompanyNewbieTaskService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.NewbieTaskConfigService;
import com.niimbot.asset.system.service.handler.CompanyPaymentStatusHandler;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

import javax.annotation.Resource;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemCompanyOts/")
@RequiredArgsConstructor
public class SystemCompanyOtsProcessClient implements SystemCompanyOts {

    private final SystemMapStruct systemMapStruct;

    @Resource
    private CompanySettingService companySettingService;

    private final CompanyPaymentStatusHandler companyPaymentStatusHandler;

    private final CompanyNewbieTaskService companyNewbieTaskService;

    private final NewbieTaskConfigService newbieTaskConfigService;

    @Override
    public CompanySettingCO getCompanySettingById(Long id) {
        AsCompanySetting companySetting = companySettingService.getById(id);
        return systemMapStruct.convertCompanySettingDoToCo(companySetting);
    }

    @Override
    public Boolean editCompanySettingTodoConfig(CompanySettingTodoConfigEditCmd cmd) {
        return companySettingService.update(
                Wrappers.lambdaUpdate(AsCompanySetting.class)
                        .set(AsCompanySetting::getTodoTimeoutLowerLimit, cmd.getTimeoutLowerLimit())
                        .set(AsCompanySetting::getTodoTimeoutUpperLimit, cmd.getTimeoutUpperLimit())
                        .eq(AsCompanySetting::getCompanyId, cmd.getCompanyId())
        );
    }

    @Override
    public CompanySwitch getSwitchSettingWithCache(Long id) {
        return companySettingService.getSwitchSettingWithCache(id);
    }

    @Override
    public Integer getCompanyPaymentStatus(Long companyId) {
        return companyPaymentStatusHandler.getCompanyPaymentStatus(companyId);
    }

    @Override
    public void completeNewbieTask(CompleteNewbieTaskCmd cmd) {

        AsNewbieTaskConfig config = newbieTaskConfigService.getOne(
                Wrappers.lambdaQuery(AsNewbieTaskConfig.class)
                        .eq(AsNewbieTaskConfig::getTableName, cmd.getKey()),
                false
        );
        if (Objects.isNull(config)) {
            return;
        }

        AsCompanyNewbieTask newbieTask = companyNewbieTaskService.getOne(
                Wrappers.lambdaQuery(AsCompanyNewbieTask.class)
                        .eq(AsCompanyNewbieTask::getCompanyId, cmd.getCompanyId())
                        .eq(AsCompanyNewbieTask::getTaskId, config.getId())
                        .eq(AsCompanyNewbieTask::getStatus, 0)
        );
        if (Objects.nonNull(newbieTask)) {
            newbieTask.setStatus(1);
            companyNewbieTaskService.updateById(newbieTask);
        }
    }
}
