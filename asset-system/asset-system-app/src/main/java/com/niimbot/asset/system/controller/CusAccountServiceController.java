package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.CusAccountService;
import com.niimbot.asset.system.util.AccountNumberUtils;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CusAccountBaseDto;
import com.niimbot.system.CusAccountDto;
import com.niimbot.system.CusAccountEnableBatchDto;
import com.niimbot.system.CusAccountEnableDto;
import com.niimbot.system.CusAccountPageDto;
import com.niimbot.system.CusAccountPageQueryDto;
import com.niimbot.system.CusAccountRoleBatchDto;
import com.niimbot.system.CusEmployeeDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 账号管理控制器
 *
 * <AUTHOR>
 * @Date 2020/11/18
 */
@RestController
@RequestMapping("server/system/account")
@Slf4j
public class CusAccountServiceController {

    @Resource
    private CusAccountService accountService;

    @Resource
    private CompanySettingService companySettingService;

    @Resource
    private AsCusEmployeeService cusEmployeeService;

    @Resource
    private AsAccountEmployeeService accountEmployeeService;

    @PutMapping
    public Boolean edit(@RequestBody CusAccountDto account) {
        checkModify(account);
        return accountService.edit(account);
    }

    @PutMapping("/role")
    public Boolean editRole(@RequestBody CusAccountDto account) {
        checkModify(account);
        return accountService.editRole(account);
    }

    @PutMapping("/addRoleBatch")
    public Boolean addRoleBatch(@RequestBody CusAccountRoleBatchDto account) {
        return accountService.addRoleBatch(account);
    }

    @PutMapping("/removeRoleBatch")
    public Boolean removeRoleBatch(@RequestBody CusAccountRoleBatchDto account) {
        return accountService.removeRoleBatch(account);
    }

    /**
     * companyId
     *
     * @param queryDto queryDto
     * @return pageUtils
     */
    @GetMapping(value = "/page")
    public IPage<CusAccountPageDto> page(CusAccountPageQueryDto queryDto) {
        return accountService.selectPage(queryDto.buildIPage(), queryDto);
    }

    @GetMapping(value = "/amount/{roleId}")
    public Integer getAccountAmount(@PathVariable("roleId") Long roleId) {
        return accountService.getAccountAmount(roleId);
    }

    /**
     * 账号列表
     * @return list
     */
    @ApiOperation(value = "企业员工账号总数")
    @GetMapping(value = "/accountTotal/{companyId}")
    public Integer selectList(@PathVariable("companyId") Long companyId) {
        return accountService.selectAccountList(companyId).size();
    }

    /**
     * 查询企业超管
     * @param companyId
     * @return
     */

    @ApiOperation(value = "查询企业超管")
    @GetMapping(value = "/getAdministratorByCompanyId/{companyId}")
    public CusEmployeeDto getAdministratorByCompanyId(@PathVariable("companyId") Long companyId){
        return BeanUtil.copyProperties(cusEmployeeService.getAdministratorByCompanyId(companyId),CusEmployeeDto.class);
    }


    /**
     * 账号列表
     *
     * @param kw 关键字
     * @return list
     */
    @ApiOperation(value = "账号列表查询")
    @GetMapping(value = "/list/{kw}")
    public List<CusAccountBaseDto> selectListKw(@PathVariable("kw") String kw) {
        return accountService.selectListKw(kw);
    }

    /**
     * 禁用或启用账号
     *
     * @param account 1:启用 2:禁用
     * @return 成功与否
     */
    @PutMapping("/status")
    public Boolean enableOrDisable(@RequestBody CusAccountEnableDto account, String domain) {
        return accountService.enableOrDisable(account, domain);
    }

    /**
     * 禁用或启用账号
     *
     * @param account 1:启用 2:禁用
     * @return 成功与否
     */
    @PutMapping("/status/batch")
    public Boolean enableOrDisableBatch(@RequestBody CusAccountEnableBatchDto account, String domain) {
        return accountService.enableOrDisableBatch(account, domain);
    }

    @GetMapping("/getAccountNo")
    public String getAccountNo() {
        return AccountNumberUtils.getMaxAccount();
    }

    @PostMapping("/switchSetting")
    public Boolean switchAccountCompanySetting(@RequestBody CompanySwitch accountSwitch) {
        return companySettingService.updateById(
                new AsCompanySetting()
                        .setCompanyId(LoginUserThreadLocal.getCompanyId())
                        .setExpandSwitch(accountSwitch));
    }

    //根据员工id查询员工账号id
    @GetMapping("/getEmployAccount/{employeeId}")
    public Long getEmployAccount(@PathVariable("employeeId") Long employeeId) {
        Optional<AsCusUser> cusUseradmin = accountEmployeeService.getEmployAccount(employeeId);
        if (cusUseradmin.isPresent()) {
            log.info("******************" + cusUseradmin.get().getId().toString());
            log.info("执行数据");
            return cusUseradmin.get().getId();
        } else {
            log.info("执行测试");
            return 0L;
        }

    }

    private void checkModify(CusAccountDto account) {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        if (ObjectUtil.equal(userId, account.getEmpId())/* || ObjectUtil.equal(userId, account.getAccountId())*/) {
            throw new BusinessException(SystemResultCode.EMP_ACCOUNT_CANT_SET_SELF);
        }
    }
}
