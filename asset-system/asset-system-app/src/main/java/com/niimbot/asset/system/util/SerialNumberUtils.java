package com.niimbot.asset.system.util;

import com.google.common.collect.Lists;

import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.service.CommonCodeService;
import com.niimbot.asset.system.service.CusUserService;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.env.Environment;

import java.util.List;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * 业务相关工具类
 *
 * <AUTHOR>
 * @Date 2020/11/18
 */
public final class SerialNumberUtils {

    private static final List<String> CODE_PREFIX = Lists.newArrayList("A", "B", "C", "D", "E", "F", "G",
            "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z");

    /**
     * 获取最大登录账号
     *
     * @return GZ********递增
     */
    public static String getMaxAccount() {
        CusUserService cusUserService = SpringUtil.getBean(CusUserService.class);
        String prefix = SpringUtil.getBean(Environment.class).getProperty(BaseConstant.USER_ACCOUNT_PREFIX);
        if (StrUtil.isBlank(prefix)) {
            prefix = "GZ";
        }
        String maxAccount = cusUserService.getMaxAccount();
        maxAccount = maxAccount == null ? prefix + "********" :
                String.format("%s%d", prefix, NumberUtils.createLong(maxAccount.substring(2)) + 1L);
        return maxAccount;
    }

    /**
     * 获取最大编码
     *
     * @return A01-A99-Z99递增
     */
    public static String getMaxCode(CommonCodeService service) {
        String maxCode = service.getMaxCode();
        if (StrUtil.isBlank(maxCode)) {
            return "A01";
        }
        String prefix = maxCode.substring(0, 1);
        String numStr = maxCode.substring(1);
        int number = NumberUtil.parseInt(numStr);
        if (number == 99) {
            // prefix == Z 会报错
            return CODE_PREFIX.get(CODE_PREFIX.indexOf(prefix) + 1) + "01";
        }
        return prefix + StrUtil.padPre(String.valueOf(++number), 2, "0");
    }
}
