package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsUserStatisticsReports;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 用户活跃状态数量定时统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsUserStatisticsReportsMapper.class)
public interface AsUserStatisticsReportsMapper extends BaseMapper<AsUserStatisticsReports> {

}
