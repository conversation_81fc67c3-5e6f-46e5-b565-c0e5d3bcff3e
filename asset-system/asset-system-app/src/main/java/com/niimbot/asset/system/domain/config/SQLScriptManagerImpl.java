package com.niimbot.asset.system.domain.config;

import cn.hutool.core.util.StrUtil;
import com.niimbot.framework.dataperm.core.model.InitSQL;
import com.niimbot.framework.dataperm.core.model.OrderInitSQL;
import com.niimbot.framework.dataperm.core.model.SQLScriptManager;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/7 17:01
 */
@Service
public class SQLScriptManagerImpl implements SQLScriptManager {
    @Override
    public InitSQL areaScript() {
        return new InitSQL() {
            @Override
            public String currentOnly() {
                return " SELECT area.id FROM as_area area " +
                        " JOIN as_org o ON area.company_id = o.company_id AND area.org_id = o.id AND o.org_type = 1 AND o.is_delete = 0 " +
                        " WHERE area.is_delete = 0 " +
                        " AND o.id in ({ids}) ";
            }

            @Override
            public String currentAndChild() {
                return " SELECT area.id FROM as_area area " +
                        " JOIN as_org o ON area.company_id = o.company_id AND area.org_id = o.id AND o.org_type = 1 AND o.is_delete = 0 " +
                        " JOIN as_org oc ON area.company_id = oc.company_id AND oc.is_delete = 0 AND oc.org_type = 1 AND (oc.company_owner = o.id OR FIND_IN_SET(oc.company_owner, o.paths)) " +
                        " WHERE area.is_delete = 0 " +
                        " AND oc.id in ({ids}) ";
            }
        };
    }

    @Override
    public InitSQL cateScript() {
        return new InitSQL() {
            @Override
            public String currentOnly() {
                return StrUtil.EMPTY;
            }

            @Override
            public String currentAndChild() {
                return StrUtil.EMPTY;
            }
        };
    }

    @Override
    public InitSQL deptScript() {
        return new InitSQL() {
            @Override
            public String currentOnly() {
                return " SELECT o.id FROM as_org o " +
                        " WHERE o.is_delete = 0 " +
                        " AND o.id in ({ids}) ";
            }

            @Override
            public String currentAndChild() {
                return " SELECT oc.id FROM as_org o " +
                        " JOIN as_org oc on o.company_id = oc.company_id and (o.id = oc.id or FIND_IN_SET(o.id, oc.paths)) and oc.is_delete = 0 " +
                        " WHERE o.is_delete = 0 " +
                        " AND o.id in ({ids}) ";
            }
        };
    }

    @Override
    public InitSQL storeScript() {
        return new InitSQL() {
            @Override
            public String currentOnly() {
                return " SELECT repo.id FROM as_repository repo " +
                        " JOIN as_org o ON repo.company_id = o.company_id AND repo.manager_owner = o.id AND o.org_type = 1 AND o.is_delete = 0 " +
                        " WHERE repo.is_delete = 0 " +
                        " AND o.id in ({ids}) ";
            }

            @Override
            public String currentAndChild() {
                return " SELECT repo.id FROM as_repository repo " +
                        " JOIN as_org o ON repo.company_id = o.company_id AND repo.manager_owner = o.id AND o.org_type = 1 AND o.is_delete = 0 " +
                        " JOIN as_org oc ON repo.company_id = oc.company_id AND oc.is_delete = 0 AND oc.org_type = 1 AND (oc.company_owner = o.id OR FIND_IN_SET(oc.company_owner, o.paths)) " +
                        " WHERE repo.is_delete = 0 " +
                        " AND oc.id in ({ids}) ";
            }
        };
    }

    @Override
    public InitSQL userScript() {
        return new InitSQL() {
            @Override
            public String currentOnly() {
                return " SELECT e.id FROM as_org o " +
                        " JOIN as_user_org uo on o.id = uo.org_id " +
                        " JOIN as_cus_employee e on uo.user_id = e.id and o.company_id = e.company_id " +
                        " WHERE o.is_delete = 0 and e.is_delete = 0 AND o.id in ({ids}) ";
            }

            @Override
            public String currentAndChild() {
                return " SELECT e.id FROM as_org o " +
                        " JOIN as_org oc on o.company_id = oc.company_id and (o.id = oc.id or FIND_IN_SET(o.id, oc.paths)) and oc.is_delete = 0 " +
                        " JOIN as_user_org uo on oc.id = uo.org_id " +
                        " JOIN as_cus_employee e on uo.user_id = e.id and o.company_id = e.company_id " +
                        " WHERE o.is_delete = 0 and e.is_delete = 0 AND o.id in ({ids}) ";
            }
        };
    }

    @Override
    public OrderInitSQL orderScript() {
        return new OrderInitSQL() {

            @Override
            public String all() {
                return " SELECT bus.business_id FROM ( " +
                        " SELECT PROC_INST_ID_ FROM ACT_RU_TASK rt WHERE rt.TENANT_ID_ = {tenantId} " +
                        " UNION ALL " +
                        " SELECT PROC_INST_ID_ FROM ACT_HI_TASKINST ht WHERE ht.TENANT_ID_ = {tenantId} " +
                        " ) t JOIN act_workflow_business bus ON t.PROC_INST_ID_ = bus.proc_inst_id ";
            }

            @Override
            public String selfOnly() {
                return " SELECT bus.business_id FROM ( " +
                        " SELECT PROC_INST_ID_ FROM ACT_RU_TASK rt WHERE rt.TENANT_ID_ = {tenantId} AND rt.ASSIGNEE_ IN ({ids}) " +
                        " UNION ALL " +
                        " SELECT PROC_INST_ID_ FROM ACT_HI_TASKINST ht WHERE ht.TENANT_ID_ = {tenantId} AND ht.ASSIGNEE_ IN ({ids}) " +
                        " ) t JOIN act_workflow_business bus ON t.PROC_INST_ID_ = bus.proc_inst_id ";
            }

            @Override
            public String currentOnly() {
                return " SELECT bus.business_id FROM ( " +
                        " SELECT PROC_INST_ID_ FROM ACT_RU_TASK rt, as_user_org uo WHERE rt.TENANT_ID_ = {tenantId} AND rt.ASSIGNEE_ = uo.user_id AND uo.org_id IN ({ids}) " +
                        " UNION ALL " +
                        " SELECT PROC_INST_ID_ FROM ACT_HI_TASKINST ht, as_user_org uo WHERE ht.TENANT_ID_ = {tenantId} AND ht.ASSIGNEE_ = uo.user_id AND uo.org_id IN ({ids}) " +
                        " ) t JOIN act_workflow_business bus ON t.PROC_INST_ID_ = bus.proc_inst_id ";
            }

            @Override
            public String currentAndChild() {
                return " SELECT bus.business_id FROM ( " +
                        " SELECT PROC_INST_ID_ FROM ACT_RU_TASK rt, as_user_org uo, as_org o, as_org oc " +
                        " WHERE rt.TENANT_ID_ = {tenantId} AND rt.ASSIGNEE_ = uo.user_id AND o.is_delete = 0 AND oc.is_delete = 0 AND rt.TENANT_ID_ = o.company_id AND rt.TENANT_ID_ = oc.company_id AND uo.org_id = oc.id " +
                        " AND ( o.id = oc.id or FIND_IN_SET(o.id, oc.paths)) and o.id IN ({ids}) " +
                        " UNION ALL " +
                        " SELECT PROC_INST_ID_ FROM ACT_HI_TASKINST ht, as_user_org uo, as_org o, as_org oc " +
                        " WHERE ht.TENANT_ID_ = {tenantId} AND ht.ASSIGNEE_ = uo.user_id AND o.is_delete = 0 AND oc.is_delete = 0 AND ht.TENANT_ID_ = o.company_id AND ht.TENANT_ID_ = oc.company_id AND uo.org_id = oc.id " +
                        " AND ( o.id = oc.id or FIND_IN_SET(o.id, oc.paths)) and o.id IN ({ids}) " +
                        " ) t JOIN act_workflow_business bus ON t.PROC_INST_ID_ = bus.proc_inst_id ";
            }
        };
    }
}
