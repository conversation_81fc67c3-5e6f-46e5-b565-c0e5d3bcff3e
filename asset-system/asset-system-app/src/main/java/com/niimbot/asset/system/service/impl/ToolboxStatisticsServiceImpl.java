package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsToolboxStatisticsMapper;
import com.niimbot.asset.system.model.AsToolboxStatistics;
import com.niimbot.asset.system.service.ToolboxStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/13 下午2:58
 */
@Slf4j
@Service
public class ToolboxStatisticsServiceImpl extends ServiceImpl<AsToolboxStatisticsMapper, AsToolboxStatistics> implements ToolboxStatisticsService {

    @Override
    public Boolean toolboxStatistics(AsToolboxStatistics toolboxStatistics) {
        if (Objects.isNull(toolboxStatistics.getOrderType())) {
            return Boolean.FALSE;
        }

        AsToolboxStatistics toolboxCount = this.getOne(Wrappers.lambdaQuery(AsToolboxStatistics.class)
                .eq(AsToolboxStatistics::getUserId, toolboxStatistics.getUserId())
                .eq(AsToolboxStatistics::getOrderType, toolboxStatistics.getOrderType()));
        if (Objects.isNull(toolboxCount)) {
            toolboxStatistics.setCount(1L);
            toolboxStatistics.setCreateTime(LocalDateTime.now());
            toolboxStatistics.setUpdateTime(LocalDateTime.now());
            return this.save(toolboxStatistics);
        } else {
            AsToolboxStatistics modifyParam = new AsToolboxStatistics().setId(toolboxCount.getId()).setUpdateTime(LocalDateTime.now());
            this.getBaseMapper().increaseToolbox(modifyParam);
        }
        return Boolean.TRUE;
    }
}
