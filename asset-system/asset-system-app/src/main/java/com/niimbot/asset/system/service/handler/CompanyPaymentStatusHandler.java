package com.niimbot.asset.system.service.handler;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/8 下午1:32
 */
@Component
public class CompanyPaymentStatusHandler {

    @Autowired
    private List<PaymentStatusHandler> statusHandlerList;

    @PostConstruct
    public void init() {
        //给处理器排序，以达到最快短路快速获取企业付费状态信息
        statusHandlerList = statusHandlerList.stream()
                .sorted(Comparator.comparing(PaymentStatusHandler::priority))
                .collect(Collectors.toList());
    }

    /**
     * 获取企业付费状态
     * @param companyId
     * @return
     */
    public Integer getCompanyPaymentStatus(Long companyId) {
        if (Objects.isNull(companyId)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "获取企业付费状态失败，企业信息为空");
        }

        Integer result = null;
        for (PaymentStatusHandler item : statusHandlerList) {
            result = item.handlePaymentStatus(companyId);
            if (Objects.nonNull(result)) {
                return result;
            }
        }
        throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "获取企业付费状态异常");
    }
}
