package com.niimbot.asset.system.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.mapper.AsAuditLogMapper;
import com.niimbot.asset.system.model.AsAuditLog;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogSearch;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
@Repository
@Profile(Edition.LOCAL)
public class LocalAuditLogRepository implements AuditLogRepository {

    @Resource
    private AsAuditLogMapper auditLogMapper;

    @Override
    public boolean existById(Long id) {
        AsAuditLog auditLog = auditLogMapper.selectOne(
                Wrappers.lambdaQuery(AsAuditLog.class)
                        .select(AsAuditLog::getId)
                        .eq(AsAuditLog::getId, id)
        );
        return Objects.nonNull(auditLog);
    }

    @Override
    public void save(AsAuditLog log) {
        auditLogMapper.insert(log);
    }

    @Override
    public PageUtils<AuditLogDto> search(AuditLogSearch search) {
        search.setSidx(StrUtil.toUnderlineCase(search.getSidx()));
        Page<AuditLogDto> page = auditLogMapper.search(search.buildIPage(), search);
        return new PageUtils<>(page);
    }
}
