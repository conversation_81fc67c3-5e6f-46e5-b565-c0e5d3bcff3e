package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCompanyPasswordSetting;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Select;

@EnableDataPerm(excludeMethodName = {"getDefaultSetting"})
public interface AsCompanyPasswordSettingMapper extends BaseMapper<AsCompanyPasswordSetting> {

    @Select("select id, company_id, password_lowest_digit, password_high_digit, password_limit, force_update, update_month, update_day, other_limit, true as password_switch from as_company_password_setting where company_id = 0")
    AsCompanyPasswordSetting getDefaultSetting();

}
