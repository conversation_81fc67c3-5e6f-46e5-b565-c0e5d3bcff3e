package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.niimbot.system.SensitiveFieldItemDto;
import com.niimbot.asset.system.mapper.AsSensitiveFieldConfigMapper;
import com.niimbot.asset.system.model.AsSensitiveFieldConfig;
import com.niimbot.asset.system.service.SensitiveFieldConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/7/21 下午4:32
 */
@Slf4j
@Service
public class SensitiveFieldConfigServiceImpl extends ServiceImpl<AsSensitiveFieldConfigMapper, AsSensitiveFieldConfig> implements SensitiveFieldConfigService {

    private List<AsSensitiveFieldConfig> allConfigCache;

    @PostConstruct
    public void init() {
        allConfigCache = this.list(Wrappers.lambdaQuery(AsSensitiveFieldConfig.class)
                .eq(AsSensitiveFieldConfig::getCompanyId, 0)
                .eq(AsSensitiveFieldConfig::getIsDelete, Boolean.FALSE));
    }

    private LoadingCache<String, List<SensitiveFieldItemDto>> sensitiveFieldCache = CacheBuilder.newBuilder()
            .maximumSize(10).refreshAfterWrite(3, TimeUnit.HOURS).build(new CacheLoader<String, List<SensitiveFieldItemDto>>() {
                @Override
                public List<SensitiveFieldItemDto> load(String key) throws Exception {
                    return getFieldByCode(key);
                }
            });

    /**
     * 获取敏感字段配置
     * @param code
     * @return
     */
    private List<SensitiveFieldItemDto> getFieldByCode(String code) {
        AsSensitiveFieldConfig sensitiveFieldConfig = this.getOne(Wrappers.lambdaQuery(AsSensitiveFieldConfig.class)
                .eq(AsSensitiveFieldConfig::getCompanyId, 0)
                .eq(AsSensitiveFieldConfig::getCode, code)
                .eq(AsSensitiveFieldConfig::getIsDelete, Boolean.FALSE));
        if (Objects.isNull(sensitiveFieldConfig)) {
            return Collections.emptyList();
        }

        return sensitiveFieldConfig.getSensitiveField();
    }

    @Override
    public List<SensitiveFieldItemDto> getSensitiveFieldByCode(String code) throws ExecutionException {
        return sensitiveFieldCache.get(code);
    }

    @Override
    public List<AsSensitiveFieldConfig> allSensitiveConfig() {
        return allConfigCache;
    }
}
