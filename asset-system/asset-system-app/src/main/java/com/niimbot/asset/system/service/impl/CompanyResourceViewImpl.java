package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.CompanyResourceViewMapper;
import com.niimbot.asset.system.model.CompanyResourceView;
import com.niimbot.asset.system.service.CompanyResourceViewService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/12/8 下午2:29
 */
@Service
public class CompanyResourceViewImpl extends ServiceImpl<CompanyResourceViewMapper, CompanyResourceView> implements CompanyResourceViewService {

    @Override
    public CompanyResourceView selectCompanyResource(Long companyId) {
        return this.getBaseMapper().selectCompanyResource(companyId);
    }
}
