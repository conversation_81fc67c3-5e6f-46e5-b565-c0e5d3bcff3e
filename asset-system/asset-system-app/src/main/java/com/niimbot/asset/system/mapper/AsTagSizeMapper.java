package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsTagSize;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 标签模板-尺寸 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsTagSizeMapper.class)
public interface AsTagSizeMapper extends BaseMapper<AsTagSize> {

    List<AsTagSize> selectSizeByPrinterIds(@Param("printerIds") List<Long> printerIds);
}
