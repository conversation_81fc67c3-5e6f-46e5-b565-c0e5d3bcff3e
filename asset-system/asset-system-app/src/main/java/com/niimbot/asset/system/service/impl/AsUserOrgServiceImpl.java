package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsUserOrgMapper;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.service.AsUserOrgService;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class AsUserOrgServiceImpl extends ServiceImpl<AsUserOrgMapper, AsUserOrg> implements AsUserOrgService {

    @Override
    public Map<Long, List<Long>> groupByUser(List<Long> userIds) {
        return this.list(Wrappers.lambdaQuery(AsUserOrg.class).in(AsUserOrg::getUserId, userIds))
                .stream()
                .collect(Collectors.groupingByConcurrent(AsUserOrg::getUserId, Collectors.mapping(AsUserOrg::getOrgId, Collectors.toList())));
    }

    @Override
    public void removeByUser(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        this.remove(
                Wrappers.lambdaUpdate(AsUserOrg.class)
                        .in(AsUserOrg::getUserId, userIds)
        );
    }

    @Override
    public List<Long> orgIdsByUserId(Long companyId, Long userId, Integer type) {
        return baseMapper.orgIdsByUserId(companyId, userId, type);
    }

    @Override
    public List<AsUserOrg> orgIdsByUserIds(Long companyId, List<Long> userIds, Integer type) {
        return baseMapper.orgIdsByUserIds(companyId, userIds, type);
    }
}
