package com.niimbot.asset.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.AssetAreaAbs;
import com.niimbot.asset.system.abs.AssetCategoryAbs;
import com.niimbot.asset.system.dto.clientobject.AssetCategoryCO;
import com.niimbot.asset.system.mapper.AsCusEmployeeMapper;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.service.AsDataPermissionDataService;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.system.DataPermFilterDto;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/26 20:19
 */
@Service
@RequiredArgsConstructor
public class AsDataPermissionDataServiceImpl implements AsDataPermissionDataService {

    private final DataAuthorityService dataAuthorityService;
    private final OrgService orgService;
    private final AssetAreaAbs assetAreaAbs;
    private final AssetCategoryAbs assetCategoryAbs;
    private final AsCusEmployeeMapper employeeMapper;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    public List<JSONObject> filterNoPermData(DataPermFilterDto dataPermFilterDto) {
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        // 加载当前账户权限可见数据
        Map<String, List<DictDataDto>> permsMap = null;
        try {
            permsMap = getPermsMap(cusUser.getCompanyId(), cusUser.getId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 字段code，字典id，字典name
        Map<String, Map<String, String>> filterCodePermsMap = new HashMap<>();
        List<FormFieldCO> filterCode = dataPermFilterDto.getFilterCode();
        for (FormFieldCO formFieldCO : filterCode) {
            if (permsMap.containsKey(formFieldCO.getFieldType())) {
                List<DictDataDto> dictDataList = permsMap.get(formFieldCO.getFieldType());
                filterCodePermsMap.put(formFieldCO.getFieldCode(), dictDataList.stream()
                        .collect(Collectors.toMap(DictDataDto::getValue, DictDataDto::getLabel, (k1, k2) -> k1)));
            }
        }
        // 过滤无效数据
        for (JSONObject jsonObject : dataPermFilterDto.getDataList()) {
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String code = entry.getKey();
                Object value = entry.getValue();
                if (filterCodePermsMap.containsKey(code)) {
                    Map<String, String> permCache = filterCodePermsMap.get(code);
                    if (permCache.containsKey(Convert.toStr(value))) {
                        // 直接写入最新名称
                        if (jsonObject.containsKey(code + "Text")) {
                            jsonObject.put(code + "Text", permCache.get(Convert.toStr(value)));
                        }
                    } else {
                        // 没权限调整为空
                        entry.setValue(StrUtil.EMPTY);
                        if (jsonObject.containsKey(code + "Text")) {
                            jsonObject.put(code + "Text", StrUtil.EMPTY);
                        }
                    }
                }
            }
        }
        return dataPermFilterDto.getDataList();
    }

    private Map<String, List<DictDataDto>> getPermsMap(Long companyId, Long userId) throws ExecutionException, InterruptedException, TimeoutException {
        Map<String, List<DictDataDto>> result = new HashMap<>();
        List<AsDataAuthority> listByUserId = dataAuthorityService.getListByUserId(userId);
        // 部门有权限的数据
        Future<List<DictDataDto>> orgFuture = null;
        // 区域有权限的数据
        Future<List<DictDataDto>> areaFuture = null;
        boolean isOrgAllPerms = false;
        for (AsDataAuthority authority : listByUserId) {
            // 组织权限
            if (AssetConstant.DATA_PERMISSION_ORG.equals(authority.getAuthorityDataCode())) {
                if (AssetConstant.AUTHORITY_TYPE_ALL == authority.getAuthorityType()) {
                    isOrgAllPerms = true;
                }
                orgFuture = taskExecutor.submit(() -> orgService.listSimpleWithPerms(authority, companyId));
            } else if (AssetConstant.DATA_PERMISSION_AREA.equals(authority.getAuthorityDataCode())) {
                areaFuture = taskExecutor.submit(() -> assetAreaAbs.listSimpleWithPerms(authority, companyId));
            }
        }
        // 分类数据
        Future<List<DictDataDto>> cateFuture = taskExecutor.submit(() -> {
            List<AssetCategoryCO> assetCategoryCOS = assetCategoryAbs.listPermission(companyId);
            return assetCategoryCOS.stream().map(c -> new DictDataDto().setLabel(c.getCategoryName())
                            .setValue(c.getId().toString()))
                    .collect(Collectors.toList());
        });

        List<DictDataDto> orgList = new ArrayList<>();
        if (orgFuture != null) {
            orgList = orgFuture.get(10, TimeUnit.SECONDS);
        }
        List<DictDataDto> areaList = new ArrayList<>();
        if (areaFuture != null) {
            areaList = areaFuture.get(10, TimeUnit.SECONDS);
        }
        List<DictDataDto> cateList = cateFuture.get(10, TimeUnit.SECONDS);
        // 员工数据（通过组织查询）
        List<DictDataDto> empList = new ArrayList<>();
        if (isOrgAllPerms) {
            empList = Db.list(Wrappers.lambdaQuery(AsCusEmployee.class).select(AsCusEmployee::getId, AsCusEmployee::getEmpName))
                    .stream().map(o -> new DictDataDto().setValue(o.getId().toString()).setLabel(o.getEmpName()))
                    .collect(Collectors.toList());
        } else if (CollUtil.isNotEmpty(orgList)) {
            List<Long> orgIds = orgList.stream().map(d -> Convert.toLong(d.getValue(), -1L))
                    .collect(Collectors.toList());
            List<AsCusEmployee> employeeList = employeeMapper.getByOrgList(orgIds);
            empList = employeeList.stream().map(c -> new DictDataDto().setLabel(c.getEmpName())
                            .setValue(c.getId().toString()))
                    .collect(Collectors.toList());
        }
        result.put(FormFieldCO.YZC_EMP, empList);
        result.put(FormFieldCO.YZC_ASSET_CATE, cateList);
        result.put(FormFieldCO.YZC_ORG, orgList);
        result.put(FormFieldCO.YZC_AREA, areaList);
        return result;
    }

}
