package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.mapper.AsIndustryMapper;
import com.niimbot.asset.system.model.AsIndustry;
import com.niimbot.asset.system.service.IndustryService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;

/**
 * 行业service
 *
 * <AUTHOR>
 * @Date 2020/11/2
 */
@Service
public class IndustryServiceImpl extends ServiceImpl<AsIndustryMapper, AsIndustry> implements IndustryService {

    @Override
    public Boolean add(AsIndustry industry) {
        // 判断是否一个行业也没有
        long count = this.count(Wrappers.emptyWrapper());
        if (count == 0) {
            industry.setPid(0L).setLevel(0).setPaths("0,");
        } else {
            // 查询父节点
            AsIndustry parent = this.getOne(new QueryWrapper<AsIndustry>().lambda().eq(AsIndustry::getId, industry.getPid()));
            // 没有父节点
            if (ObjectUtil.isNull(parent)) {
                industry.setPid(0L).setLevel(0).setPaths("0,");
            } else {
                int level = parent.getLevel() + 1;
                if (level > 1) {
                    throw new BusinessException(SystemResultCode.INDUSTRY_LEVEL_OVER_MAX);
                }
                industry.setLevel(level).setPaths(parent.getPaths() + parent.getId() + ",");
            }
            // 检查名称
            AsIndustry one = this.getOne(new QueryWrapper<AsIndustry>().lambda()
                    .eq(AsIndustry::getIndustryName, industry.getIndustryName()));
            if (ObjectUtil.isNotNull(one)) {
                throw new BusinessException(SystemResultCode.PARAM_EXISTS, "行业名称", industry.getIndustryName());
            }
        }
        if (!this.save(industry)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(AsIndustry industry) {
        if (industry.getId().equals(industry.getPid())) {
            throw new BusinessException(SystemResultCode.INDUSTRY_PID_ERROR);
        }
        // 查询当前节点
        AsIndustry asIndustry = this.getById(industry.getId());
        if (ObjectUtil.isNull(asIndustry)) {
            throw new BusinessException(SystemResultCode.INDUSTRY_NOT_EXISTS);
        }
        // 检查名称
        AsIndustry one = this.getOne(new QueryWrapper<AsIndustry>().lambda()
                .eq(AsIndustry::getIndustryName, industry.getIndustryName())
                .ne(AsIndustry::getId, industry.getId()));
        if (ObjectUtil.isNotNull(one)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "行业名称", industry.getIndustryName());
        }
        // 所有子Path
        String sonPath = asIndustry.getPaths() + asIndustry.getId() + ",";
        // 查询父节点
        AsIndustry parent = this.getOne(new QueryWrapper<AsIndustry>().lambda().eq(AsIndustry::getId, industry.getPid()));
        // 查询父节点paths
        String paths = "0,";
        if (ObjectUtil.isNotNull(parent)) {
            paths = parent.getPaths() + parent.getId() + ",";
        }
        int level = paths.split(",").length - 1;
        if (level > 1) {
            throw new BusinessException(SystemResultCode.INDUSTRY_LEVEL_OVER_MAX);
        }
        industry.setLevel(level).setPaths(paths);

        List<AsIndustry> sonList = this.list(new QueryWrapper<AsIndustry>().lambda().likeRight(AsIndustry::getPaths, sonPath));
        List<AsIndustry> updateSonList = new ArrayList<>();
        for (AsIndustry son : sonList) {
            String p = son.getPaths().replace(sonPath, paths + industry.getId() + ",");
            level = p.split(",").length - 1;
            if (level > 1) {
                throw new BusinessException(SystemResultCode.INDUSTRY_LEVEL_OVER_MAX);
            }
            AsIndustry updateSon = new AsIndustry();
            updateSon.setId(son.getId()).setLevel(level).setPaths(p);
            updateSonList.add(updateSon);
        }
        this.updateBatchById(updateSonList);
        if (!this.updateById(industry)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @Override
    public Boolean delete(List<Long> industryIds) {
        for (Long industryId : industryIds) {
            // 当前行业以及子行业有关联企业用户时，则不允许删除；提示：当前行业或子行业关联有企业用户，不可删除
            Integer count = this.getBaseMapper().industryRefCompany(industryId);
            if (count > 0) {
                throw new BusinessException(SystemResultCode.INDUSTRY_DELETE_ROOT);
            }
            // 删除行业和子行业
            this.remove(new QueryWrapper<AsIndustry>().lambda().eq(AsIndustry::getId, industryId)
                    .or().like(AsIndustry::getPaths, ("," + industryId + ",")));
        }
        return true;
    }

    @Override
    public Boolean sort(List<Long> industryIds) {
        int count = this.list(new QueryWrapper<AsIndustry>().lambda()
                .select(AsIndustry::getPid)
                .in(AsIndustry::getId, industryIds)
                .groupBy(AsIndustry::getPid)).size();
        if (count > 1) {
            throw new BusinessException(SystemResultCode.INDUSTRY_SORT_ERROR);
        }
        AtomicInteger idx = new AtomicInteger(0);
        List<AsIndustry> collect = industryIds.stream().map(it -> new AsIndustry()
                .setId(it).setSortNum(idx.getAndIncrement())).collect(Collectors.toList());
        this.updateBatchById(collect);
        return true;
    }
}
