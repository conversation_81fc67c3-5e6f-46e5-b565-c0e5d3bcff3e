package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.AsSelectorRecordService;
import com.niimbot.system.AddSelectorRecord;
import com.niimbot.system.GetSelectorRecord;
import com.niimbot.system.SelectorRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/selectorRecord")
public class SelectorRecordServiceController {

    private final AsSelectorRecordService selectorRecordService;

    @PostMapping("/add")
    public Boolean record(@RequestBody AddSelectorRecord record) {
        return selectorRecordService.addSelectorRecord(record);
    }

    @GetMapping("/get")
    public List<SelectorRecord> records(GetSelectorRecord record) {
        return selectorRecordService.getSelectorRecords(record);
    }

}
