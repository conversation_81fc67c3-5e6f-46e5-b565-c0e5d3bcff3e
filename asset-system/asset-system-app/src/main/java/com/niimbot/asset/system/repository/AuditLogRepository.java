package com.niimbot.asset.system.repository;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.model.AsAuditLog;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogSearch;

/**
 * <AUTHOR>
 */
public interface AuditLogRepository {

    /**
     * 是否已保存过
     *
     * @param id id
     * @return true if exists
     */
    boolean existById(Long id);

    /**
     * 保存记录
     *
     * @param log 记录
     */
    void save(AsAuditLog log);

    /**
     * 分页搜索
     *
     * @param search body
     * @return list
     */
    PageUtils<AuditLogDto> search(AuditLogSearch search);
}
