package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.model.AsSensitiveFieldConfig;
import com.niimbot.asset.system.service.RoleSensitiveAuthorityService;
import com.niimbot.asset.system.service.SensitiveFieldConfigService;
import com.niimbot.asset.system.service.UserSensitiveAuthorityService;
import com.niimbot.system.SensitivePermissionDto;
import com.niimbot.system.UserSensitivePermissionDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24 上午10:07
 */
@RestController
@RequestMapping("server/system/sensitive")
@RequiredArgsConstructor
public class SensitiveAuthorityServiceController {

    @Autowired
    private RoleSensitiveAuthorityService roleSensitiveAuthorityService;
    @Autowired
    private UserSensitiveAuthorityService userSensitiveAuthorityService;
    @Autowired
    private SensitiveFieldConfigService sensitiveFieldConfigService;

    @GetMapping("/config")
    public List<AsSensitiveFieldConfig> config() {
        return sensitiveFieldConfigService.allSensitiveConfig();
    }

    @GetMapping("/roleAuthority")
    public List<SensitivePermissionDto> roleAuthority(@RequestParam("roleId") Long roleId) {
        return roleSensitiveAuthorityService.getByRoleId(roleId);
    }

    @GetMapping("/userAuthority")
    public List<SensitivePermissionDto> userAuthority(@RequestParam("userId") Long userId) {
        return userSensitiveAuthorityService.getByUserId(userId);
    }

    @GetMapping("/userSensitivePermission")
    public List<UserSensitivePermissionDto> userSensitivePermission(@RequestParam("userId") Long userId) {
        return userSensitiveAuthorityService.getSensitivePerm(userId);
    }

}
