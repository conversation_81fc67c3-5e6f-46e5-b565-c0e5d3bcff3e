package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.CusMenuService;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.CusMenuDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/10/27 14:42
 */
@RestController
@RequestMapping("server/system/cusMenu")
public class CusMenuServiceController {
    @Autowired
    private CusMenuService cusMenuService;

    @GetMapping(value = "/configRoleMenu/pc")
    public List<CusMenuDto> configRoleMenuPcList() {
        return cusMenuService.configRoleMenuPcList();
    }

    @GetMapping(value = "/configRoleMenu/app")
    public List<CusMenuDto> contractMenuAppList() {
        return cusMenuService.configRoleMenuAppList();
    }

    @GetMapping(value = "/allPcMenu")
    public List<CusMenuDto> allPcMenu() {
        // 返回集合
        return cusMenuService.allPcMenu();
    }

    @GetMapping(value = "/allAppMenu")
    public List<CusMenuDto> allAppMenu() {
        // 返回集合
        return cusMenuService.allAppMenu();
    }

    @GetMapping(value = "/userMenu/pc")
    public List<CusMenuDto> userMenuPcList() {
        // 返回集合
        return cusMenuService.userMenuPcList();
    }


    @GetMapping(value = "/userMenu/app")
    public AppCusMenuDto userMenuAppList() {
        // 返回集合
        return cusMenuService.userMenuAppList();
    }

    @GetMapping("/getMenuPermsByUserId/{userId}")
    public Set<String> selectMenuPermsByUserId(@PathVariable("userId") Long userId) {
        return cusMenuService.selectMenuPermsByUserId(userId);
    }
}
