package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsEmployeeAssetChangeMapper;
import com.niimbot.asset.system.model.AsEmployeeAssetChange;
import com.niimbot.asset.system.service.AsEmployeeAssetChangeService;
import com.niimbot.system.CusEmployeeChangeQueryDto;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 员工资产异动记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Service
public class AsEmployeeAssetChangeServiceImpl extends ServiceImpl<AsEmployeeAssetChangeMapper, AsEmployeeAssetChange> implements AsEmployeeAssetChangeService {

    @Override
    public IPage<AsEmployeeAssetChange> selectPage(CusEmployeeChangeQueryDto query) {
        return this.getBaseMapper().page(query.buildIPage(), query);
    }
}
