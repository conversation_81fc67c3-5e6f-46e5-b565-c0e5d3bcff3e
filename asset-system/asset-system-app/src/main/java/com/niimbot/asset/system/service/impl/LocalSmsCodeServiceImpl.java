package com.niimbot.asset.system.service.impl;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.service.SmsCodeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * 手机验证码service
 *
 * <AUTHOR>
 * @Date 2020/11/2
 */
@Service
@Profile({Edition.LOCAL})
public class LocalSmsCodeServiceImpl implements SmsCodeService {

    @Override
    public void sendSmsCode(String iddCode, String mobile) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

    @Override
    public void sendCommonCode(String type, String addr) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

    @Override
    public Boolean checkSmsCode(String mobile, String smsCode) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

}
