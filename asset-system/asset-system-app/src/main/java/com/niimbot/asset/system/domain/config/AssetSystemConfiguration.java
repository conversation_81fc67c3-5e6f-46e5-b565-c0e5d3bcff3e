package com.niimbot.asset.system.domain.config;

import com.niimbot.asset.system.adapter.SystemCommonAdapter;
import com.niimbot.asset.system.mq.ExternalNoticeProducer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class AssetSystemConfiguration {

    @Bean
    @ConditionalOnMissingBean(value = ExternalNoticeProducer.class)
    public ExternalNoticeProducer defaultExternalNoticeProducer() {
        return new ExternalNoticeProducer() {
        };
    }

    @Bean
    @ConditionalOnMissingBean(value = SystemCommonAdapter.class)
    public SystemCommonAdapter defaultSystemCommonAdapter() {
        return new SystemCommonAdapter() {
        };
    }

}
