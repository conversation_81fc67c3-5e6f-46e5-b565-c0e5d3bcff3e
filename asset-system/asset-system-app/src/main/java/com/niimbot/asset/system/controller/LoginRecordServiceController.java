package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsUserLoginRecord;
import com.niimbot.asset.system.service.AsUserLoginRecordService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * 登录记录管理控制器
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@RestController
@RequestMapping("server/system/loginRecord")
@Slf4j
public class LoginRecordServiceController {

    @Resource
    private AsUserLoginRecordService userLoginRecordService;

    /**
     * 添加登录记录
     *
     * @param userLoginRecord 登录记录对象
     * @return Boolean
     */
    @PostMapping
    public boolean saveLoginRecord(@RequestBody AsUserLoginRecord userLoginRecord) {
        return userLoginRecordService.save(userLoginRecord);
    }

    @GetMapping("/isFirstLogin")
    public Boolean isFirstLogin() {
        long count = userLoginRecordService.count(
                Wrappers.lambdaQuery(AsUserLoginRecord.class)
                        .eq(AsUserLoginRecord::getEmployeeId, LoginUserThreadLocal.getCusUser().getId())
        );
        return count <= 1;
    }
}
