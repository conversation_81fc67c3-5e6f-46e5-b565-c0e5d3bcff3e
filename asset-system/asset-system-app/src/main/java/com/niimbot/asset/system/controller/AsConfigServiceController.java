package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.system.model.AsConfig;
import com.niimbot.asset.system.service.AsConfigService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/10 14:02
 */
@RestController
@RequestMapping("server/system/config")
public class AsConfigServiceController {

    private final AsConfigService configService;

    @Autowired
    public AsConfigServiceController(AsConfigService configService) {
        this.configService = configService;
    }

    @GetMapping("/{type}")
    public List<AsConfig> config(@PathVariable("type") Integer type) {
        return configService.list(new LambdaQueryWrapper<AsConfig>().eq(AsConfig::getType, type));
    }

}
