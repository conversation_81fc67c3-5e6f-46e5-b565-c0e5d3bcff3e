package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsRoleSensitiveAuthorityMapper;
import com.niimbot.asset.system.model.AsRoleSensitiveAuthority;
import com.niimbot.asset.system.service.RoleSensitiveAuthorityService;
import com.niimbot.asset.system.service.SensitiveFieldConfigService;
import com.niimbot.system.SensitiveFieldItemDto;
import com.niimbot.system.SensitivePermissionDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/21 下午4:40
 */
@Slf4j
@Service
public class RoleSensitiveAuthorityServiceImpl extends ServiceImpl<AsRoleSensitiveAuthorityMapper, AsRoleSensitiveAuthority> implements RoleSensitiveAuthorityService {

    @Autowired
    private SensitiveFieldConfigService sensitiveFieldConfigService;

    @Override
    public List<SensitivePermissionDto> getByRoleId(Long roleId) {
        if (Objects.isNull(roleId)) {
            return Collections.emptyList();
        }

        //角色敏感数据权限
        List<AsRoleSensitiveAuthority> roleSensitiveAuthorityList = this.list(Wrappers.lambdaQuery(AsRoleSensitiveAuthority.class)
                .eq(AsRoleSensitiveAuthority::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsRoleSensitiveAuthority::getRoleId, roleId));
        if (CollUtil.isNotEmpty(roleSensitiveAuthorityList)) {
            return roleSensitiveAuthorityList.stream().map(item -> {
                SensitivePermissionDto data = new SensitivePermissionDto().setCode(item.getConfigCode());
                data.setFieldCode(item.getAuthorityData().stream().map(SensitiveFieldItemDto::getCode).collect(Collectors.toList()));
                return data;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public void removeByUserIds(List<Long> roleIds) {
        this.remove(Wrappers.lambdaQuery(AsRoleSensitiveAuthority.class)
                .eq(AsRoleSensitiveAuthority::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(AsRoleSensitiveAuthority::getRoleId, roleIds));
    }

    @Override
    public Boolean savePermission(Long roleId, List<SensitivePermissionDto> sensitivePermissionDto) {
        if (CollUtil.isEmpty(sensitivePermissionDto)) {
            return Boolean.TRUE;
        }

        for (SensitivePermissionDto item : sensitivePermissionDto) {
            if (StrUtil.isBlank(item.getCode()) || CollUtil.isEmpty(item.getFieldCode())) {
                continue;
            }

            try {
                //获取敏感字段配置
                List<SensitiveFieldItemDto> sensitiveFieldItemDtoList = sensitiveFieldConfigService.getSensitiveFieldByCode(item.getCode());
                if (CollUtil.isEmpty(sensitiveFieldItemDtoList)) {
                    continue;
                }

                //按敏感字段编码分组
                Map<String, SensitiveFieldItemDto> sensitiveFieldItemDtoMap = sensitiveFieldItemDtoList.stream().collect(Collectors.toMap(SensitiveFieldItemDto::getCode, value -> value, (v1, v2) -> v1));

                //敏感字段
                List<SensitiveFieldItemDto> sensitiveFieldItemDtos = new ArrayList<>();
                for (String fieldCode : item.getFieldCode()) {
                    //敏感字段配置中没有的字段，直接跳过
                    if (Objects.isNull(sensitiveFieldItemDtoMap.get(fieldCode))) {
                        continue;
                    }

                    //设置字段编码-名称-类型
                    SensitiveFieldItemDto sensitiveFieldItemDto = new SensitiveFieldItemDto()
                            .setCode(fieldCode)
                            .setName(sensitiveFieldItemDtoMap.get(fieldCode).getName())
                            .setType(sensitiveFieldItemDtoMap.get(fieldCode).getType());
                    sensitiveFieldItemDtos.add(sensitiveFieldItemDto);
                }

                //敏感字段不为空，保存到数据库
                if (CollUtil.isNotEmpty(sensitiveFieldItemDtos)) {
                    AsRoleSensitiveAuthority userSensitiveAuthority = new AsRoleSensitiveAuthority()
                            .setCompanyId(LoginUserThreadLocal.getCompanyId())
                            .setRoleId(roleId)
                            .setConfigCode(item.getCode())
                            .setAuthorityData(sensitiveFieldItemDtos);
                    this.save(userSensitiveAuthority);
                }
            } catch (ExecutionException e) {
                log.error("roleSensitiveAuthorityService savePermission fieldCache exception ", e);
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }
}
