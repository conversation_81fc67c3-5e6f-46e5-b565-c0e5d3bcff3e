package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.ThirdPartyAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:07
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.thirdparty.domain.abs.impl.ThirdPartyAbsImpl")
@FeignClient(name = "asset-thirdparty", url = "https://{gateway}/client/abs/thirdparty/thirdPartyAbs/")
public interface ThirdPartyAbsRemoteClient extends ThirdPartyAbs {
}
