package com.niimbot.asset.system.controller;


import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.service.AsCompanyPasswordSettingService;
import com.niimbot.system.CompanyPasswordSettingDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("server/system/companyPasswordSetting")
@Slf4j
public class CompanyPasswordSettingServiceController {

    @Resource
    private AsCompanyPasswordSettingService settingService;


    @PostMapping
    public Boolean saveOrUpdate(@RequestBody CompanyPasswordSettingDto settingDto) {
        return settingService.saveOrUpdate(settingDto);
    }

    @GetMapping("/getSettingDetail")
    public CompanyPasswordSettingDto getSettingDetail() {
        return settingService.getSettingDetail(LoginUserThreadLocal.getCompanyId(), true);
    }

    @PostMapping("/closeSwitch")
    public Boolean closeSwitch(){
        return settingService.closeSwitch();
    }

    @GetMapping("/getLimitWords")
    public String getLimitWords() {
        return settingService.getLimitWords();
    }

    @GetMapping("/getLimitWordsByMobile")
    public String getLimitWordsByMobile(@RequestParam(value = "mobile") String mobile) {
        return settingService.getLimitWordsByMobile(mobile);
    }
}
