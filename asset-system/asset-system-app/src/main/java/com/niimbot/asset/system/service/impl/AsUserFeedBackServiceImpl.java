package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsUserFeedbackMapper;
import com.niimbot.asset.system.model.AsUserFeedback;
import com.niimbot.asset.system.service.AsUserFeedBackService;

import org.springframework.stereotype.Service;

/**
 * 用户反馈
 *
 * <AUTHOR>
 * @Date 2020/11/2
 */
@Service
public class AsUserFeedBackServiceImpl extends ServiceImpl<AsUserFeedbackMapper, AsUserFeedback> implements AsUserFeedBackService {
}
