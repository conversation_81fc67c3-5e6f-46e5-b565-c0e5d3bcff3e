package com.niimbot.asset.system.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.system.mapper.AsAccountEmployeeMapper;
import com.niimbot.asset.system.mapper.AsCusEmployeeMapper;
import com.niimbot.asset.system.mapper.AsCusUserMapper;
import com.niimbot.asset.system.mapper.AsRecommendRecordMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsRecommendRecord;
import com.niimbot.asset.system.service.AsRecommendRecordService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.RecommendRecordDto;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/27 15:08
 */
@Service
@RequiredArgsConstructor
public class AsRecommendRecordServiceImpl extends ServiceImpl<AsRecommendRecordMapper, AsRecommendRecord> implements AsRecommendRecordService {

    private final AsCusEmployeeMapper employeeMapper;
    private final AsAccountEmployeeMapper accountEmployeeMapper;
    private final AsCusUserMapper cusUserMapper;

    @Override
    public void tradeRecord(Long saleOrderId, Long companyId, BigDecimal totalMoney, LocalDateTime operateTime) {
        // 记录老带新数据
        AsRecommendRecord recommendRecord = getOne(Wrappers.lambdaQuery(AsRecommendRecord.class)
                .eq(AsRecommendRecord::getRegisterCompanyId, companyId)
                .eq(AsRecommendRecord::getStatus, 1)
                .isNull(AsRecommendRecord::getSaleOrderId));
        if (recommendRecord != null) {
            recommendRecord.setStatus(2)
                    .setAwardStatus(1)
                    .setSaleOrderId(saleOrderId)
                    .setTradeTime(operateTime)
                    .setTradeMoney(totalMoney);
            updateById(recommendRecord);
        }
    }

    @Override
    public List<RecommendRecordDto> list(Long recommendEmpId, Integer status) {
        List<AsRecommendRecord> list = list(Wrappers.lambdaQuery(AsRecommendRecord.class)
                .eq(AsRecommendRecord::getRecommendEmpId, recommendEmpId)
                .eq(AsRecommendRecord::getStatus, status)
                .orderByDesc(AsRecommendRecord::getRegisterTime));
        return list.stream().map(rr -> {
            RecommendRecordDto recordDto = new RecommendRecordDto();
            recordDto.setId(rr.getId());
            recordDto.setRegisterCompanyName(rr.getRegisterCompanyName());
            recordDto.setRegisterMobile(StrUtil.isBlank(rr.getRegisterMobile()) ? "" : StrUtil.hide(rr.getRegisterMobile(), 3, rr.getRegisterMobile().length() - 4));
            recordDto.setRegisterTime(rr.getRegisterTime());
            return recordDto;
        }).collect(Collectors.toList());
    }

    @Override
    public AsCusEmployee verifyMobile(Long recommendEmpId, String mobile) {
        AsCusEmployee employee = employeeMapper.selectById(recommendEmpId);
        BusinessExceptionUtil.checkNotNull(employee, "推荐人不存在");

        List<AsAccountEmployee> accountEmployeeList = accountEmployeeMapper.selectList(Wrappers.lambdaQuery(AsAccountEmployee.class)
                .eq(AsAccountEmployee::getCompanyId, employee.getCompanyId())
                .eq(AsAccountEmployee::getEmployeeId, employee.getId()));
        if (CollUtil.isEmpty(accountEmployeeList)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "推荐人不存在");
        }

        AsCusUser cusUser = cusUserMapper.selectById(accountEmployeeList.get(0).getAccountId());
        BusinessExceptionUtil.checkNotNull(cusUser, "推荐人不存在");

        if (mobile.equals(cusUser.getMobile())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "该手机号与推荐人手机号重复");
        }
        return employee;
    }

}
