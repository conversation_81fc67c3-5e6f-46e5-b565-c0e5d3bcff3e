package com.niimbot.asset.system.abs.remote;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2023/2/27 下午4:51
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.finance.abs.impl.AssetFinanceInfoAbsImpl")
@FeignClient(name = "asset-finance", url = "https://{gateway}/client/abs/finance/assetFinanceInfoAbs/")
public interface AssetFinanceInfoAbsRemoteClient {
}
