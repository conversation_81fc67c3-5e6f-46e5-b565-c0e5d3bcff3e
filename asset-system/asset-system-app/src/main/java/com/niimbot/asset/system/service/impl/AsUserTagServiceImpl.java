package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsUserTagMapper;
import com.niimbot.asset.system.model.AsUserTag;
import com.niimbot.asset.system.service.AsUserTagService;
import com.niimbot.system.UserTagDto;
import com.niimbot.system.UserTagPrintDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/17 10:30
 */
@Slf4j
@Service
public class AsUserTagServiceImpl extends ServiceImpl<AsUserTagMapper, AsUserTag> implements AsUserTagService {
    @Override
    public int getCountByCompanyId(Long companyId) {
        return this.baseMapper.getCountByCompanyId(companyId);
    }

    @Override
    public AsUserTag getOneById(Long tagId, Short printType) {
        return this.baseMapper.getOneById(tagId, printType);
    }

    @Override
    public UserTagPrintDto getDetail(Long tagId) {
        return this.baseMapper.getDetail(tagId);
    }

    @Override
    public Long getFirstTagId(Short printType, Long printerId) {
        // 首先获取用户模板的第一个，不存在，则获取系统模板的第一个
        Long firstTagId = this.baseMapper.getFirstUserTagId(LoginUserThreadLocal.getCompanyId(), printType, printerId);
        if (firstTagId == null) {
            firstTagId = this.baseMapper.getFirstTagId(printType, printerId);
        }

        return firstTagId;
    }

//    @Override
//    public List<UserTagDto> queryTagBySizeAndType(Integer type, Integer size, Long companyId) {
//        return this.baseMapper.queryTagBySizeAndType(type, size, companyId);
//    }

    @Override
    public List<UserTagDto> queryTagBySizeId(Long companyId, Long sizeId, Integer tagType, String kw, Short printType, Long printerId) {
        return this.baseMapper.queryTagBySizeId(companyId, sizeId, tagType, kw, printType, printerId);
    }

    @Override
    public List<AsUserTag> companyTagList(Long companyId, Short printType, Long sizeId) {
        return this.baseMapper.selectAllCompanyTags(companyId, printType, sizeId);
    }
}
