package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.system.model.AsIndustryCaseConfig;
import com.niimbot.asset.system.model.AsSolutionConfig;
import com.niimbot.system.IndustryCaseQueryDto;
import com.niimbot.system.IndustryCaseConfigDto;
import com.niimbot.system.SolutionQueryDto;
import org.apache.ibatis.annotations.Param;

public interface AsIndustryCaseConfigMapper extends BaseMapper<AsIndustryCaseConfig> {

    /**
     * 分页查询
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AsIndustryCaseConfig> pageQuery(IPage<Object> page, @Param("page") IndustryCaseQueryDto queryDto);
    
    /**
     * 查询上一篇
     * @param configId
     * @return
     */
    IndustryCaseConfigDto selectPrevious(@Param("configId") Long configId);

    /**
     * 查询下一篇
     * @param configId
     * @return
     */
    IndustryCaseConfigDto selectNext(@Param("configId") Long configId);

    /**
     * 浏览次数++
     * @param id
     * @return
     */
    int increaseView(Long id);
}