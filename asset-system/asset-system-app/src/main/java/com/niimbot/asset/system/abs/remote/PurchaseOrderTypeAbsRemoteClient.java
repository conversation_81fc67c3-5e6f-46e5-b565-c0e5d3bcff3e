package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.PurchaseOrderTypeAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:27
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.purchase.domain.abs.impl.PurchaseOrderTypeAbsImpl")
@FeignClient(name = "asset-purchase", url = "https://{gateway}/client/abs/purchase/purchaseOrderTypeAbs/")
public interface PurchaseOrderTypeAbsRemoteClient extends PurchaseOrderTypeAbs {
}
