package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.dto.DataSourceIdsGetQry;
import com.niimbot.asset.system.mapper.AsStoreRecordMapper;
import com.niimbot.asset.system.model.AsStoreRecord;
import com.niimbot.asset.system.service.AsStoreRecordService;
import com.niimbot.asset.system.service.DataSnapshotService;
import com.niimbot.means.StoreRecordDto;
import com.niimbot.means.StoreRecordSearch;
import com.niimbot.system.DataSnapshotFactory;
import com.niimbot.system.StoreRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AsStoreRecordServiceImpl extends ServiceImpl<AsStoreRecordMapper, AsStoreRecord> implements AsStoreRecordService {

    private final DataSnapshotService dataSnapshotService;

    @Override
    public PageUtils<StoreRecordDto> search(StoreRecordSearch search) {
        search.clear();
        Long companyId = LoginUserThreadLocal.getCompanyId();
        LambdaQueryWrapper<AsStoreRecord> wrapper = Wrappers.lambdaQuery(AsStoreRecord.class).eq(AsStoreRecord::getCompanyId, companyId);
        if (CollUtil.isNotEmpty(search.getIds())) {
            wrapper.in(AsStoreRecord::getId, search.getIds());
        }
        if (Objects.nonNull(search.getStoreMode()) && search.getStoreMode() != 0) {
            wrapper.eq(AsStoreRecord::getStoreMode, search.getStoreMode());
        }
        if (Objects.nonNull(search.getStoreHandler())) {
            wrapper.eq(AsStoreRecord::getStoreHandler, search.getStoreHandler());
        }
        if (StrUtil.isNotBlank(search.getStoreNo())) {
            wrapper.like(AsStoreRecord::getStoreNo, search.getStoreNo());
        }
        if (StrUtil.isNotBlank(search.getKw())) {
            DataSourceIdsGetQry qry = new DataSourceIdsGetQry(search.getKw(), companyId, 1, 1);
            List<Long> sourceIds = dataSnapshotService.getSourceIds(qry);
            if (CollUtil.isNotEmpty(sourceIds)) {
                wrapper.in(AsStoreRecord::getId, sourceIds);
            } else {
                wrapper.eq(AsStoreRecord::getId, -1);
            }
        }
        if (CollUtil.isNotEmpty(search.getCreateTime())) {
            LocalDateTime start = DateUtil.parseLocalDateTime(search.getCreateTime().get(0) + " 00:00:00");
            wrapper.gt(AsStoreRecord::getCreateTime, start);
            if (search.getCreateTime().size() == 2) {
                LocalDateTime end = DateUtil.parseLocalDateTime(search.getCreateTime().get(1) + " 23:59:59");
                wrapper.lt(AsStoreRecord::getCreateTime, end);
            }
        }
        wrapper.orderByDesc(AsStoreRecord::getCreateTime);
        IPage<StoreRecordDto> page = this.page(search.buildIPage(), wrapper).convert(v -> BeanUtil.copyProperties(v, StoreRecordDto.class));
        return new PageUtils<>(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void record(StoreRecord saved) {
        if (Objects.isNull(saved.getCompanyId())) {
            saved.setCompanyId(LoginUserThreadLocal.getCompanyId());
        }
        if (Objects.isNull(saved.getStoreHandler())) {
            saved.setStoreHandler(LoginUserThreadLocal.getCurrentUserId());
        }
        if (Objects.isNull(saved.getStoreTime())) {
            saved.setStoreTime(LocalDateTime.now());
        }
        // 是否已经保存过
        AsStoreRecord record = this.getById(saved.getId());
        if (Objects.isNull(record)) {
            record = new AsStoreRecord()
                    .setId(saved.getId())
                    .setCompanyId(saved.getCompanyId())
                    .setStoreMode(saved.getStoreMode())
                    .setStoreType(saved.getStoreType())
                    .setStoreHandler(saved.getStoreHandler())
                    .setStoreNo(StringUtils.getOrderNo("SR" + saved.getSourceType() + saved.getStoreMode()));
        }
        if (StrUtil.isNotBlank(saved.getStoreNo())) {
            record.setStoreNo(saved.getStoreNo());
        }
        // 直接保存快照
        Integer count = dataSnapshotService.saveSnapshots(DataSnapshotFactory.createForStoreRecord(saved));
        record.setStoreQuantity(count);
        this.saveOrUpdate(record);

    }
}
