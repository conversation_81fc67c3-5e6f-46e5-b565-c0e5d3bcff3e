package com.niimbot.asset.system.ots.process;

import cn.hutool.core.collection.CollUtil;
import com.niimbot.asset.system.ots.SystemRecycleBinOts;
import com.niimbot.asset.system.service.RecycleBinService;
import com.niimbot.system.GetRecycleBins;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemRecycleBinOts/")
@RequiredArgsConstructor
public class SystemRecycleBinOtsProcessClient implements SystemRecycleBinOts {

    private final RecycleBinService recycleBinService;

    @Override
    public List<Long> getResIdsForQuery(GetRecycleBins get) {
        if (get.getFormRecycle() == 0) {
            return Collections.emptyList();
        }
        if (get.getFormRecycle() == 1) {
            List<Long> recycleIds = recycleBinService.getRecycleIds(get);
            return CollUtil.isEmpty(recycleIds) ? Collections.singletonList(-1L) : recycleIds;
        }
        return Collections.emptyList();
    }
}
