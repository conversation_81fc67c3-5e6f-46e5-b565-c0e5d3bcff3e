package com.niimbot.asset.system.service.handler;

import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.CompanyPaymentStatusEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.service.AsConfigService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CompanyPaymentStatusDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

/**
 * 处理没有资源包情况下的企业状态：新入驻和待转化
 *
 * <AUTHOR>
 * @date 2022/12/8 上午11:36
 */
@Slf4j
@Component
public class InitPaymentStatusHandler implements PaymentStatusHandler {

    private final static Integer HANDLER_PRIORITY = 3;


    @Autowired
    private AsConfigService asConfigService;

    @Override
    public Integer handlePaymentStatus(Long companyId) {
        AsCompany company = Db.getById(companyId, AsCompany.class);
        if (Objects.isNull(company)) {
            log.error("initPaymentStatusHandler handlePaymentStatus error! company not exist! companyId=[{}]", companyId);
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "企业信息不存在");
        }

        //默认新入驻状态
        Integer result = CompanyPaymentStatusEnum.REGISTERED.getStatusCode();

        //获取企业付费状态配置
        CompanyPaymentStatusDto paymentStatusConfig = asConfigService.queryCompanyPaymentStatus();
        if (Objects.isNull(paymentStatusConfig.getRegisterDayMin())) {
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "企业付费状态配置错误，注册天数未配置!");
        }

        //注册超过配置天数，企业付费状态为待转化
        long betweenDays = Duration.between(LocalDateTime.now(), company.getCreateTime()).abs().toDays();
        if (betweenDays >= paymentStatusConfig.getRegisterDayMin()) {
            result = CompanyPaymentStatusEnum.WAIT_TRANSFORM.getStatusCode();
        }
        return result;
    }

    @Override
    public Integer priority() {
        return HANDLER_PRIORITY;
    }
}
