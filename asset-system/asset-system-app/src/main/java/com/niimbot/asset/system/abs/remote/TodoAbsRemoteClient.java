package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.TodoAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/10 11:46
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.todo.domain.abs.impl.TodoAbsImpl")
@FeignClient(name = "asset-todo", url = "https://{gateway}/client/abs/todo/todoAbs/")
public interface TodoAbsRemoteClient extends TodoAbs {
}
