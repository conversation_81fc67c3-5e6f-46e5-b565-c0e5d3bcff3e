package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.mapper.AsCusEmployeeExtMapper;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2020-12-18
 */
@Service
public class AsCusEmployeeExtServiceImpl extends ServiceImpl<AsCusEmployeeExtMapper, AsCusEmployeeExt>
        implements AsCusEmployeeExtService {

    @Override
    public void updateLastLoginTime(Long userId) {
        this.getBaseMapper().updateLastLoginTime(userId);
    }

    @Override
    public void add(Long employeeId) {
        AsCusEmployeeExt asCusEmployeeExt = new AsCusEmployeeExt().setId(employeeId)
                .setCurrentLoginTime(LocalDateTime.now()).setLastLoginTime(LocalDateTime.now());
        this.save(asCusEmployeeExt);
    }

}
