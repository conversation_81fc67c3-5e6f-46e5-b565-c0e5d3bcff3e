package com.niimbot.asset.system.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsCusUserRoleMapper;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.service.CusUserRoleService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @Date 2020/10/30
 */
@Service
public class CusUserRoleServiceImpl extends ServiceImpl<AsCusUserRoleMapper, AsUserRole> implements CusUserRoleService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeBatch(List<AsUserRole> userRoleList) {
        if (CollUtil.isEmpty(userRoleList)) {
            return;
        }
        List<List<AsUserRole>> partition = Lists.partition(userRoleList, 500);
        for (List<AsUserRole> userRoles : partition) {
            this.getBaseMapper().removeBatch(userRoles);
        }

    }
}
