package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.system.mapper.AsRoleDataAuthorityMapper;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsRoleDataAuthority;
import com.niimbot.asset.system.service.AsRoleDataAuthorityService;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/5/22 上午11:00
 */
@Slf4j
@Service
public class AsRoleDataAuthorityServiceImpl extends ServiceImpl<AsRoleDataAuthorityMapper, AsRoleDataAuthority> implements AsRoleDataAuthorityService {

    @Override
    public void initRoleDataAuth(Long companyId, Long roleId, String roleCode) {
        //查询运营后台配置的，系统全局默认的角色数据权限
        List<AsRoleDataAuthority> roleDataAuthorityList = this.getBaseMapper().selectByRoleCode(0L, roleCode);
        if (CollUtil.isEmpty(roleDataAuthorityList)) {
            //普通员工角色数据权限做最后的兜底处理
            roleDataAuthorityList = this.getBaseMapper().selectByRoleCode(0L, BaseConstant.COMMON_ROLE);
        }

        //再次判断下，如果数据权限还是为空的话，就直接返回
        if (CollUtil.isEmpty(roleDataAuthorityList)) {
            log.error("roleDataAuthorityService initRoleDataAuth role data auth empty! companyId");
            return ;
        }

        List<AsRoleDataAuthority> baseRoleDataAuthList = roleDataAuthorityList.stream().map(item -> {
            AsRoleDataAuthority roleDataAuthority = new AsRoleDataAuthority();
            roleDataAuthority.setCompanyId(companyId).setRoleId(roleId)
                    .setAuthorityData(item.getAuthorityData()).setAuthorityCode(item.getAuthorityCode())
                    .setAuthorityDataCode(item.getAuthorityDataCode()).setAuthorityType(item.getAuthorityType())
                    .setAuthorityGroup(item.getAuthorityGroup());
            return roleDataAuthority;
        }).collect(Collectors.toList());

        //保存角色数据权限
        this.saveBatch(baseRoleDataAuthList);
    }

    @Override
    public List<AsRoleDataAuthority> queryRoleDataAuthByRoleId(Long companyId, Long roleId) {
        //查询指定角色的数据权限
        List<AsRoleDataAuthority> roleDataAuthorityList = this.list(Wrappers.lambdaQuery(AsRoleDataAuthority.class)
                .eq(AsRoleDataAuthority::getCompanyId, companyId)
                .eq(AsRoleDataAuthority::getRoleId, roleId));
        if (CollUtil.isEmpty(roleDataAuthorityList)) {
            //查询企业默认角色数据权限
            roleDataAuthorityList = this.getBaseMapper().selectDefaultDataAuth(companyId);
        }

        //如果企业没有默认数据权限，选择企业普通员工的数据权限
        if (CollUtil.isEmpty(roleDataAuthorityList)) {
            roleDataAuthorityList = this.getBaseMapper().selectByRoleCode(companyId, BaseConstant.COMMON_ROLE);
        }

        //如果企业普通员工数据权限为空，选择系统配置的普通员工的数据权限
        if (CollUtil.isEmpty(roleDataAuthorityList)) {
            roleDataAuthorityList = this.getBaseMapper().selectByRoleCode(0L, BaseConstant.COMMON_ROLE);
        }
        AsCusRole role = Db.getOne(
                Wrappers.lambdaQuery(AsCusRole.class)
                        .select(AsCusRole::getId, AsCusRole::getRoleCode)
                        .eq(AsCusRole::getCompanyId, companyId)
                        .eq(AsCusRole::getId, roleId)
        );
        // 如果是超管就给一套超管得数据权限配置
        if (Objects.nonNull(role) && BaseConstant.ADMIN_ROLE.equalsIgnoreCase(role.getRoleCode())) {
            roleDataAuthorityList.forEach(v -> {
                if (!v.getAuthorityCode().equalsIgnoreCase(AssetConstant.AUTHORITY_ONLY_ONESELF)) {
                    v.setAuthorityType(AssetConstant.AUTHORITY_TYPE_ALL);
                    v.setAuthorityData(Collections.emptyList());
                }
            });
        }
        return roleDataAuthorityList;
    }
}
