package com.niimbot.asset.system.aspect;

import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.annotations.EventDesc;
import com.niimbot.asset.system.annotations.PageEvent;
import com.niimbot.asset.system.event.PageEventQueue;
import com.niimbot.asset.system.model.AsModuleStatistics;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * PageEvent注解切面
 * <AUTHOR>
 * @date 2023/4/11 下午2:43
 */
@Slf4j
@Aspect
@Component
public class PageEventAspect {

    /**
     * 事件id和事件名称映射缓存
     */
    private Map<String, String> eventNameMap = new HashMap<>();

    @PostConstruct
    public void init() {
        Field[] fields = PageEvent.class.getDeclaredFields();
        for (Field item : fields) {
            EventDesc eventDesc = fields.getClass().getAnnotation(EventDesc.class);
            if (Objects.nonNull(eventDesc)){
                eventNameMap.put(item.getName(), eventDesc.value());
            }
        }
    }

    @Before("@annotation(com.niimbot.asset.system.annotations.PageEvent)")
    public void before(JoinPoint joinPoint){
        try {
            //获取事件注解
            PageEvent pageEvent = getDeclaredAnnotation(joinPoint, PageEvent.class);
            if (Objects.isNull(pageEvent)) {
                return ;
            }

            AsModuleStatistics moduleStatistics = new AsModuleStatistics();
            moduleStatistics.setCompanyId(LoginUserThreadLocal.getCompanyId());
            moduleStatistics.setUserId(LoginUserThreadLocal.getCurrentUserId());
            moduleStatistics.setEventId(pageEvent.eventId());
            moduleStatistics.setEventName(eventNameMap.get(pageEvent.eventId()));
            moduleStatistics.setEventAction(pageEvent.action());
            moduleStatistics.setCreateTime(LocalDateTime.now());

            //生成事件
            PageEventQueue.getInstance().producePageEvent(moduleStatistics);
        } catch (Exception e) {
            log.error("pageEventAspect before error! exception ", e);
        }
    }

    /**
     * 获取方法PageEvent注解
     * @param pjp
     * @param annotationClass
     * @return
     * @param <T>
     */
    private <T extends Annotation> T getDeclaredAnnotation(JoinPoint pjp, Class<T> annotationClass) {
        try {
            if (pjp.getSignature() instanceof MethodSignature) {
                MethodSignature ms = (MethodSignature) pjp.getSignature();
                T annotation = ms.getMethod().getAnnotation(annotationClass);
                if (null != annotation) {
                    return annotation;
                }
            }
        } catch (Exception e) {
            log.error("pageEventAspect getDeclaredAnnotation error! exception ", e);
        }
        return null;
    }
}
