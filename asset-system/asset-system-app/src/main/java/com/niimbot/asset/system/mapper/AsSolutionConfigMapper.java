package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.system.model.AsSolutionConfig;
import com.niimbot.system.SolutionConfigDto;
import com.niimbot.system.SolutionQueryDto;
import org.apache.ibatis.annotations.Param;

public interface AsSolutionConfigMapper extends BaseMapper<AsSolutionConfig> {

    /**
     * 分页查询
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AsSolutionConfig> pageQuery(IPage<Object> page, @Param("page") SolutionQueryDto queryDto);

    /**
     * 查询上一篇
     * @param configId
     * @return
     */
    SolutionConfigDto selectPrevious(@Param("configId") Long configId);

    /**
     * 查询下一篇
     * @param configId
     * @return
     */
    SolutionConfigDto selectNext(@Param("configId") Long configId);

    /**
     * 浏览次数++
     * @param id
     * @return
     */
    int increaseView(Long id);
}