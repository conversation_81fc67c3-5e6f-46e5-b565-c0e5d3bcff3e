package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.VersionManagementService;
import com.niimbot.system.VersionManagementDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/server/system/version/management")
public class VersionManagementServiceController {

    private final VersionManagementService versionManagementService;

    @GetMapping("/latestVersion/{version}/{clientType}")
    public VersionManagementDto latestVersion(@PathVariable("version") String version, @PathVariable("clientType") Integer clientType) {
        return versionManagementService.latestVersion(version, clientType);
    }

    @GetMapping("/pc/latestVersionMessage")
    public VersionManagementDto latestVersionMessage() {
        return versionManagementService.latestVersionMessage();
    }

    @PutMapping("/pc/versionMessageRead/{id}")
    public Boolean versionMessageRead(@PathVariable Long id) {
        return versionManagementService.versionMessageRead(id);
    }
}
