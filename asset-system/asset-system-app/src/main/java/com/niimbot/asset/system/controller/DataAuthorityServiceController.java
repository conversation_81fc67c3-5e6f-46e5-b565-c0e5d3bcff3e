package com.niimbot.asset.system.controller;


import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.service.AsDataPermissionDataService;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.system.DataPermFilterDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/10/21 16:18
 */
@RestController
@RequestMapping("server/system/dataAuthority")
public class DataAuthorityServiceController {

    private final DataAuthorityService dataAuthorityService;
    private final AsDataPermissionDataService dataPermissionDataService;

    @Autowired
    public DataAuthorityServiceController(DataAuthorityService dataAuthorityService,
                                          AsDataPermissionDataService dataPermissionDataService) {
        this.dataAuthorityService = dataAuthorityService;
        this.dataPermissionDataService = dataPermissionDataService;
    }

    @GetMapping("/{userId}")
    public List<AsDataAuthority> getInfo(@PathVariable("userId") Long userId) {
        return dataAuthorityService.getListByUserId(userId);
    }

    @GetMapping("/getByUserAndDataCodeAndCode")
    public AsDataAuthority getByUserAndDataCodeAndCode(@RequestParam("userId") Long userId,
                                                       @RequestParam("dataCode") String dataCode,
                                                       @RequestParam("code") String code) {
        return dataAuthorityService.getByUserAndDataCodeAndCode(userId, dataCode, code);
    }

    @PostMapping("/filterNoPermData")
    public List<JSONObject> filterNoPermData(@RequestBody DataPermFilterDto dataPermFilterDto) {
        return dataPermissionDataService.filterNoPermData(dataPermFilterDto);
    }

}
