package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.CusRoleService;
import com.niimbot.system.CusMenuRoleDto;
import com.niimbot.system.CusRoleAccountDto;
import com.niimbot.system.CusRoleAccountQueryDto;
import com.niimbot.system.CusRoleDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/10/27 14:42
 */
@RestController
@RequestMapping("server/system/cusRole")
public class CusRoleServiceController {

    private final CusRoleService cusRoleService;

    @Autowired
    public CusRoleServiceController(CusRoleService cusRoleService) {
        this.cusRoleService = cusRoleService;
    }

    @GetMapping(value = "/list")
    public List<AsCusRole> list() {
        return cusRoleService.list();
    }

    @GetMapping(value = "/roleAccountList")
    public List<CusRoleAccountDto> roleAccountList(CusRoleAccountQueryDto roleAccountQueryDto) {
        return cusRoleService.roleAccountList(roleAccountQueryDto);
    }

    @GetMapping("/{roleId}")
    public CusRoleDto getInfo(@PathVariable("roleId") Long roleId) {
        return cusRoleService.getInfo(roleId);
    }

    @PostMapping
    public Boolean add(@RequestBody CusRoleDto dto) {
        return cusRoleService.add(dto);
    }

    @GetMapping("/setting/{roleId}")
    public CusMenuRoleDto getSetting(@PathVariable("roleId") Long roleId) {
        return cusRoleService.getSetting(roleId);
    }

    @PutMapping
    public List<String> edit(@RequestBody CusRoleDto dto) {
        return cusRoleService.edit(dto);
    }

    @DeleteMapping
    public Boolean remove(@RequestBody List<Long> roleIds) {
        return cusRoleService.remove(roleIds);
    }

    /**
     * 查询获取角色
     *
     * @param userId 用户ID
     * @return RoleDto信息
     */
    @GetMapping(value = "/user/{userId}")
    public List<CusRoleDto> getRoleByUserId(@PathVariable("userId") Long userId) {
        return cusRoleService.getRoleByEmployeeId(userId);
    }

    /**
     * 根据角色Id查询用户
     *
     * @param roleId 用户Id
     * @return 用户信息
     */
    @GetMapping(value = "/user")
    public List<AsCusUser> listUserByRoleId(@RequestParam("roleId") Long roleId) {
        return cusRoleService.listUserByRoleId(roleId);
    }

    @PutMapping("/supertubeTransfer")
    public Boolean supertubeTransfer(@RequestBody Map<String, Long> data){
        return cusRoleService.supertubeTransfer(data);
    }

    @PostMapping("/configDefault")
    public Boolean configDefault(@RequestBody Long roleId) {
        return cusRoleService.configDefault(roleId);
    }
}
