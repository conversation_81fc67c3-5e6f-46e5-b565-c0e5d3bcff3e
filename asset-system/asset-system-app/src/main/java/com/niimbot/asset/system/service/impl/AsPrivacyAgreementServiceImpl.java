package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsPrivacyAgreementMapper;
import com.niimbot.asset.system.model.AsPrivacyAgreement;
import com.niimbot.asset.system.service.AsPrivacyAgreementService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 隐私协议管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Service
public class AsPrivacyAgreementServiceImpl extends ServiceImpl<AsPrivacyAgreementMapper, AsPrivacyAgreement> implements AsPrivacyAgreementService {

    @Override
    public AsPrivacyAgreement queryLast(Integer platform) {
        return this.getBaseMapper().queryLast(platform);
    }
}
