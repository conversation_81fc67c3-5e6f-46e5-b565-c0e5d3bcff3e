package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsCompanyMapper;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.SalesOwnerDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 * @since 2020/10/20 12:36
 */
@Service
public class CompanyServiceImpl extends ServiceImpl<AsCompanyMapper, AsCompany> implements CompanyService {

    @Resource
    private OrgService orgService;

    @Resource
    private RedisService redisService;

    /**
     * 修改企业名称
     *
     * @param name name
     * @return 成功与否
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeCompanyName(String name) {
        // 当前企业id
        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 修改企业名称信息，只有超管才可以修改；其他人点击toast：请联系超管修改；
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            throw new BusinessException(SystemResultCode.COMPANY_NAME_ONLY_ADMIN);
        }

        // 判断是否能修改企业名称
        AsCompany byId = this.getById(companyId);
        if (null == byId) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }

        if (Edition.isSaas()) {
            if (byId.getUpdateTimes() > 1) {
                throw new BusinessException(SystemResultCode.UPDATE_CHANCE_IS_USE_UP);
            }
        }

        // 校验企业名称
        List<AsCompany> existCompany = this.list(Wrappers.<AsCompany>lambdaQuery()
                .eq(AsCompany::getName, name).ne(AsCompany::getId, companyId));
        if (CollUtil.isNotEmpty(existCompany)) {
            throw new BusinessException(SystemResultCode.COMPANY_NAME_REPEAT);
        }

        // 更新as_company
        boolean companyUpdate = this.update(Wrappers.<AsCompany>lambdaUpdate()
                .set(AsCompany::getName, name)
                .set(AsCompany::getUpdateTimes, byId.getUpdateTimes() + 1)
                .eq(AsCompany::getId, LoginUserThreadLocal.getCompanyId()));
        if (!companyUpdate) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 更新as_org
        boolean orgUpdate = orgService.update(Wrappers.<AsOrg>lambdaUpdate()
                .set(AsOrg::getOrgName, name)
                .eq(AsOrg::getPid, 0)
                .eq(AsOrg::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        if (!orgUpdate) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        //查询组织
        AsOrg org = orgService.getOne(Wrappers.<AsOrg>lambdaQuery()
                .eq(AsOrg::getPid, 0)
                .eq(AsOrg::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        SpringUtil.getBean(CacheOrgStrategy.class).evictCache(org.getId());
        return true;
    }

    @Override
    public Integer countOfExcludedTest(DateTime date) {
        return this.getBaseMapper().selectCountOfExcludeTest(date);
    }

    @Override
    public AsCompany checkIsExist(Long companyId) {
        return Optional.ofNullable(this.getById(companyId)).orElseThrow(() -> new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST));
    }

    @Override
    public void cleanCompanyStatusCache(Long companyId) {
        redisService.del(RedisConstant.getCompanyStatusKey(companyId));
    }

    @Override
    public void cleanCompanyStatusCache(List<Long> companyIds) {
        List<String> keys = companyIds.stream().map(RedisConstant::getCompanyStatusKey).collect(Collectors.toList());
        redisService.del(keys);
    }

    @Override
    public Integer queryEnterpriseSize(Long companyId) {
        return this.getBaseMapper().selectEnterpriseSize(companyId);
    }

    @Override
    public List<String> queryCompanyTag(Long companyId) {
        return this.getBaseMapper().selectCompanyTag(companyId);
    }

    @Override
    public SalesOwnerDto getSalesOwner() {
        return getBaseMapper().getSalesOwner(LoginUserThreadLocal.getCompanyId());
    }
}
