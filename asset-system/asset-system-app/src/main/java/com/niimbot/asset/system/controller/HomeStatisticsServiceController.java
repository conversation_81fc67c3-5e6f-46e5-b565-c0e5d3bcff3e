package com.niimbot.asset.system.controller;//package com.niimbot.asset.controller;
//
//import com.niimbot.asset.system.service.HomeStatisticsService;
//import com.niimbot.system.statistics.HomeStatisticsDto;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * 首页统计数据
// *
// * <AUTHOR>
// * @Date 2021/03/24
// */
//@RestController
//@RequestMapping("server/system/statistics")
//@Slf4j
//public class HomeStatisticsServiceController {
//
//    @Resource
//    private HomeStatisticsService homeStatisticsService;
//
//    /**
//     * 首页-各类统计数据
//     *
//     * @return 各类统计数据对象
//     */
//    @GetMapping
//    public HomeStatisticsDto homeStatistics(@RequestParam("type") String type,
//                                            @RequestParam("year") Integer year,
//                                            @RequestParam("reportType") Integer reportType) {
//        return homeStatisticsService.homeStatistics(type, year, reportType);
//    }
//
//
//}
