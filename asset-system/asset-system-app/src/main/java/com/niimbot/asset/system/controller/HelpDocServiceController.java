package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.HelpDocService;
import com.niimbot.system.AssetHelpDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/8 下午2:32
 */
@RestController
@RequestMapping("server/system/helpDoc")
public class HelpDocServiceController {

    @Autowired
    private HelpDocService helpDocService;

    /**
     * 根据标题关键字搜索帮助文档
     * @param kw
     * @return
     */
    @GetMapping("/query")
    public List<AssetHelpDto> queryDoc(@RequestParam("kw") String kw) {
        return helpDocService.queryByTitle(kw);
    }
}
