package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.mapper.AsThirdPartyAccountMapper;
import com.niimbot.asset.system.model.AsThirdPartyAccount;
import com.niimbot.asset.system.service.AsThirdPartyAccountService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AsThirdPartyAccountServiceImpl extends ServiceImpl<AsThirdPartyAccountMapper, AsThirdPartyAccount> implements AsThirdPartyAccountService {

    @Override
    public Optional<AsThirdPartyAccount> getThirdPartyAccount(String type, String thirdUniqueId) {
        return Optional.ofNullable(this.getOne(
                Wrappers.lambdaQuery(AsThirdPartyAccount.class)
                        .eq(AsThirdPartyAccount::getType, type)
                        .eq(AsThirdPartyAccount::getUniqueId, thirdUniqueId)
        ));
    }

    private Optional<AsThirdPartyAccount> getOne(String type, String thirdUniqueId) {
        AsThirdPartyAccount thirdPartyAccount = this.getOne(
                Wrappers.lambdaQuery(AsThirdPartyAccount.class)
                        .eq(AsThirdPartyAccount::getType, type)
                        .eq(AsThirdPartyAccount::getUniqueId, thirdUniqueId)
        );
        return Optional.ofNullable(thirdPartyAccount);
    }

    @Override
    public Boolean bindOne(Long accountId, String type, String openId, String thirdUniqueId, String nickname, String avatarUrl) {
        AsThirdPartyAccount thirdPartyAccount = new AsThirdPartyAccount().setAccountId(accountId).setType(type).setOpenId(openId).setUniqueId(thirdUniqueId).setNickname(nickname).setAvatarUrl(avatarUrl);
        return this.save(thirdPartyAccount);
    }

    @Override
    public void bindOneIfNotExist(Long accountId, String type, String openId, String thirdUniqueId, String nickname, String avatarUrl) {
        // 同一账号的第三方绑定类型只能有一个
        AsThirdPartyAccount partyAccount = this.getOne(Wrappers.lambdaQuery(AsThirdPartyAccount.class).eq(AsThirdPartyAccount::getAccountId, accountId).eq(AsThirdPartyAccount::getType, type));
        if (Objects.nonNull(partyAccount)) {
            throw new BusinessException(SystemResultCode.THIRD_PARTY_ACCOUNT_EXIST);
        }
        // 此用户是否已经绑定过
        Optional<AsThirdPartyAccount> thirdPartyAccountOptional = this.getOne(type, thirdUniqueId);
        if (!thirdPartyAccountOptional.isPresent()) {
            this.bindOne(accountId, type, openId, thirdUniqueId, nickname, avatarUrl);
        }
    }

    @Override
    public List<AsThirdPartyAccount> getThirdPartyAccounts(Long accountId) {
        return this.list(
                Wrappers.lambdaQuery(AsThirdPartyAccount.class)
                        .eq(AsThirdPartyAccount::getAccountId, accountId)
        );
    }
}
