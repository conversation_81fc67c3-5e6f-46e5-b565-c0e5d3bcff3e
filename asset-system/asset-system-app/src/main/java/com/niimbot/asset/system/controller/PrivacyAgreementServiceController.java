package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.model.AsPrivacyAgreement;
import com.niimbot.asset.system.service.AsPrivacyAgreementService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 隐私协议管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@RestController
@RequestMapping("server/system/privacyAgreement")
@RequiredArgsConstructor
public class PrivacyAgreementServiceController {
    private final AsPrivacyAgreementService privacyAgreementService;

    @GetMapping(value = "/last/{platform}")
    public AsPrivacyAgreement last(@PathVariable("platform") Integer platform) {
        return privacyAgreementService.queryLast(platform);
    }
}
