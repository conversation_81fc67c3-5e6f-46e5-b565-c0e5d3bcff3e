package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.ImportErrorAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 导入错误数据实现
 * <AUTHOR>
 * @date 2022/5/6 14:19
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.ImportErrorAbsImpl")
@FeignClient(name = "asset-means", url = "https://{gateway}/client/abs/means/importErrorAbs/")
public interface ImportErrorAbsRemoteClient extends ImportErrorAbs {
}
