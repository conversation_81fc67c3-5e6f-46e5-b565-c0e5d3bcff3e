package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.AssetLogAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 15:02
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.AssetLogAbsImpl")
@FeignClient(name = "asset-means", url = "http://localhost:8000/")
public interface AssetLogAbsRemoteClient extends AssetLogAbs {
}
