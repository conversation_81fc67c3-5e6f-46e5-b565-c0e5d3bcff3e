package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.*;
import com.niimbot.asset.framework.utils.cacheStrategy.BaseCacheStrategy;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheEmpStrategy;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.AssetAbs;
import com.niimbot.asset.system.abs.AssetQueryViewAbs;
import com.niimbot.asset.system.abs.ImportErrorAbs;
import com.niimbot.asset.system.dto.*;
import com.niimbot.asset.system.dto.clientobject.ImportErrorCO;
import com.niimbot.asset.system.event.EmpInfoChangeEvent;
import com.niimbot.asset.system.mapper.AsCusEmployeeMapper;
import com.niimbot.asset.system.mapper.AsCusRoleMapper;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.asset.system.service.handler.WeixinAdapter;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.asset.system.util.AccountNumberUtils;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.*;
import com.niimbot.validate.NationalCodeValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 员工管理
 *
 * <AUTHOR> @Date 2020/11/12
 */
@Slf4j
@Service
public class AsCusEmployeeServiceImpl extends ServiceImpl<AsCusEmployeeMapper, AsCusEmployee>
        implements AsCusEmployeeService, ApplicationListener<ApplicationReadyEvent> {

    @Resource
    private AsCusRoleMapper cusRoleMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AssetAbs assetAbs;

    @Autowired
    private OrgService orgService;

    @Autowired
    private CusRoleService cusRoleService;

    @Autowired
    private CusUserRoleService cusUserRoleService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private AsCusEmployeeSettingService employeeSettingService;

    @Resource
    private AsCusEmployeeExtService employeeExtService;

    @Resource
    private CusUserCompanyService cusUserCompanyService;

    @Resource
    private CusRoleService roleService;

    @Resource
    private ImportErrorAbs importErrorAbs;

    @Resource
    private AsUserOrgService userOrgService;

    @Resource
    private AsCusEmployeeTransferService transferService;
    @Resource
    private AssetQueryViewAbs assetQueryViewAbs;
    @Resource
    private AsDataPermissionService dataPermissionService;
    @Resource
    private AsAccountEmployeeService accountEmployeeService;

    @Resource
    private DataScopeStrategyManager dataScopeStrategyManager;

    @Autowired
    private ModelDataScopeServiceImpl modelDataScopeService;

    @Override
    public void onApplicationEvent(
            @SuppressWarnings("NullableProblems") ApplicationReadyEvent applicationReadyEvent) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return;
        }

        taskExecutor.execute(
                () -> {
                    List<AsCusEmployee> empList = this.getBaseMapper().allEmployee();
                    this.loadEmpCache(empList);
                });
    }

    /**
     * 写入员工缓存
     *
     * @param empList 员工列表
     */
    @Override
    public void loadEmpCache(List<AsCusEmployee> empList) {
        Map<String, String> collect = new ConcurrentHashMap<>();
        empList.parallelStream().forEach(it -> {
            JSONObject jsonObject = new JSONObject()
                    .fluentPut(BaseCacheStrategy.KEY_NAME, it.getEmpName())
                    .fluentPut(BaseCacheStrategy.KEY_CODE, it.getEmpNo());
            collect.put(Convert.toStr(it.getId()), jsonObject.toJSONString());
        });
        redisService.hSetAll(RedisConstant.employeeDictKey(), collect);
        log.info("init employee cache finish");
    }

    @Override
    public String recommendEmpNo() {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        if (companyId == null) {
            companyId = 0L;
        }
        return StringUtils.getMaxIncreFromList(getBaseMapper().getEmpNoList(companyId), DEFAULT_EMP_NO);
    }

    @Override
    public String recommendEmpNo(Long companyId) {
        return StringUtils.getMaxIncreFromList(getBaseMapper().getEmpNoList(companyId), DEFAULT_EMP_NO);
    }

    @Override
    public AsCusEmployee checkPhone(String mobile, Long companyId) {
        return getBaseMapper().checkPhone(mobile, companyId);
    }

    @Override
    public AsCusEmployee checkEmail(String email, Long companyId) {
        return getBaseMapper().checkEmail(email, companyId);
    }

    private void checkExistUnique(Long id, Long companyId, String mobile, String email) {
        if (StrUtil.isNotBlank(mobile)) {
            AsCusEmployee mobileIsExist =
                    this.getOne(
                            Wrappers.lambdaQuery(AsCusEmployee.class)
                                    .eq(AsCusEmployee::getMobile, mobile)
                                    .eq(AsCusEmployee::getCompanyId, companyId)
                                    .ne(AsCusEmployee::getId, id));
            if (Objects.nonNull(mobileIsExist)) {
                throw new BusinessException(SystemResultCode.EMPLOYEE_FIELD_EXIST, "手机号");
            }
        }
        if (StrUtil.isNotBlank(email)) {
            AsCusEmployee emailIsExist =
                    this.getOne(
                            Wrappers.lambdaQuery(AsCusEmployee.class)
                                    .eq(AsCusEmployee::getEmail, email)
                                    .eq(AsCusEmployee::getCompanyId, companyId)
                                    .ne(AsCusEmployee::getId, id));
            if (Objects.nonNull(emailIsExist)) {
                throw new BusinessException(SystemResultCode.EMPLOYEE_FIELD_EXIST, "邮箱号");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(CusEmployeeOptDto employee) {
        List<AsAccountEmployee> list =
                accountEmployeeService.list(
                        Wrappers.lambdaQuery(AsAccountEmployee.class)
                                .eq(AsAccountEmployee::getEmployeeId, employee.getId()));
        if (!CollUtil.isEmpty(list)) {
            if (Edition.isLocal()) {
                if (StrUtil.isNotBlank(employee.getMobile())) {
                    ValidationUtils.checkMobile(SystemResultCode.USER_PHONE_ERROR, employee.getMobile());
                }
            } else {
                if (StrUtil.isBlank(employee.getMobile())) {
                    throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
                }
                //只校验+86的手机号
                NationalCodeValidate.checkCNMobile(employee.getNationalCode(), employee.getMobile());
            }
        }
        if (StringUtils.isEmpty(employee.getEmpNo())) {
            employee.setEmpNo(StrUtil.EMPTY);
        } else {
            AsCusEmployee asCusEmployee = getBaseMapper().checkEmpNo(employee.getEmpNo(), employee.getId());
            if (null != asCusEmployee) {
                throw new BusinessException(SystemResultCode.EMP_NO_EXISTS);
            }
        }
        checkExistUnique(
                employee.getId(),
                LoginUserThreadLocal.getCompanyId(),
                employee.getMobile(),
                employee.getEmail());
        AsCusEmployee forUpdate = BeanUtil.copyProperties(employee, AsCusEmployee.class);
        boolean update = updateById(forUpdate);
        if (update) {
            batchModifyOrg(Collections.singletonList(employee));
            if (Edition.isLocal()) {
                // 同时修改账号手机号
                AsCusUser cusUser = new AsCusUser();
                cusUser.setId(employee.getId()).setMobile(employee.getMobile())
                        .setEmail(employee.getEmail());
                Db.updateById(cusUser);
            }
        }
        AsCusEmployee administrator = this.getAdministrator();
        SpringUtil.getBean(CacheEmpStrategy.class).evictCache(employee.getId());
        EventPublishHandler.publish(new EmpInfoChangeEvent(update).setCompanyId(LoginUserThreadLocal.getCompanyId()).setAdmin(administrator.getId().equals(forUpdate.getId())));
        return true;
    }

    @Override
    public Boolean batchModifyOrg(List<CusEmployeeOptDto> employees) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        for (CusEmployeeOptDto employee : employees) {
            // 场景 A->B, A->AB, A->BC, AB->B, AB->BC, AB-CD
            List<CusEmployeeTransferDto> transfers = employee.getTransfers();
            // 异动说明
            List<AsUserOrg> userOrgList =
                    userOrgService.list(
                            new LambdaQueryWrapper<AsUserOrg>()
                                    .eq(AsUserOrg::getUserId, employee.getId()));
            List<Long> orgIds =
                    userOrgList.stream().map(AsUserOrg::getOrgId).collect(Collectors.toList());
            // 原始部门
            List<AsOrg> fromOrgList =
                    CollUtil.isEmpty(userOrgList)
                            ? new ArrayList<>()
                            : orgService.listAllByIds(orgIds);
            // 现部门
            List<AsOrg> toOrgList =
                    CollUtil.isEmpty(employee.getOrgId())
                            ? new ArrayList<>()
                            : orgService.listAllByIds(employee.getOrgId());
            // 判断部门是否有修改
            if (!(new HashSet<>(orgIds).containsAll(employee.getOrgId())
                    && new HashSet<>(employee.getOrgId()).containsAll(orgIds))) {
                // 修改部门
                userOrgService.remove(
                        new LambdaQueryWrapper<AsUserOrg>()
                                .eq(AsUserOrg::getUserId, employee.getId()));
                List<AsUserOrg> userOrgs =
                        employee.getOrgId().stream()
                                .map(it -> new AsUserOrg().setOrgId(it).setUserId(employee.getId()))
                                .collect(Collectors.toList());
                // 用户的部门有修改，需要删除权限缓存
                modelDataScopeService.cleanDataScopeCache(companyId, ListUtil.of(employee.getId()));
                userOrgService.saveBatch(userOrgs);
                changeOrgLog(
                        fromOrgList,
                        toOrgList,
                        transfers,
                        employee.getId(),
                        LoginUserThreadLocal.getCompanyId());
            }
        }
        return true;
    }

    @Override
    public void changeOrgLog(
            List<AsOrg> fromOrgList,
            List<AsOrg> toOrgList,
            List<CusEmployeeTransferDto> transfers,
            Long userId,
            Long companyId) {
        // 写入异动信息
        AsEmployeeChange employeeChange = new AsEmployeeChange();
        employeeChange.setId(IdUtils.getId());
        employeeChange.setCompanyId(companyId);
        employeeChange.setType(DictConstant.CHANGE_TYPE_EDIT).setEmpId(userId);

        // 部门转移记录
        StringBuffer orgExplain = new StringBuffer();
        orgExplain
                .append("部门由“")
                .append(
                        fromOrgList.stream()
                                .map(
                                        o ->
                                                o.getOrgName()
                                                        + (StrUtil.isNotEmpty(o.getOrgCode())
                                                        ? "（" + o.getOrgCode() + "）"
                                                        : ""))
                                .collect(Collectors.joining("，")));
        orgExplain
                .append("”变成“")
                .append(
                        toOrgList.stream()
                                .map(
                                        o ->
                                                o.getOrgName()
                                                        + (StrUtil.isNotEmpty(o.getOrgCode())
                                                        ? "（" + o.getOrgCode() + "）"
                                                        : ""))
                                .collect(Collectors.joining("，")));
        orgExplain.append("”");
        employeeChange.setOrgExplain(orgExplain.toString());

        // 资产转移记录
        if (CollUtil.isNotEmpty(transfers)) {
            Set<Long> orgIdSet = new HashSet<>();
            transfers.forEach(
                    it -> {
                        orgIdSet.add(it.getFrom());
                        orgIdSet.add(it.getTo());
                    });
            List<AsOrg> orgList = orgService.listAllByIds(new ArrayList<>(orgIdSet), companyId);
            Map<Long, String> orgMap =
                    orgList.stream()
                            .collect(
                                    Collectors.toMap(
                                            AsOrg::getId,
                                            o ->
                                                    o.getOrgName()
                                                            + (StrUtil.isNotEmpty(o.getOrgCode())
                                                            ? "（" + o.getOrgCode() + "）"
                                                            : ""),
                                            (k1, k2) -> k1));
            List<String> assetExplain = new ArrayList<>();
            transfers.forEach(
                    it -> {
                        StringBuffer buffer = new StringBuffer();
                        buffer.append(orgMap.get(it.getFrom()))
                                .append("资产转移到")
                                .append(orgMap.get(it.getTo()));
                        assetExplain.add(buffer.toString());
                    });
            employeeChange.setAssetExplain(String.join(",", assetExplain));
        } else {
            employeeChange.setAssetExplain("无");
        }
        // 转移资产
        List<AsOrg> orgList = new ArrayList<>();
        orgList.addAll(fromOrgList);
        orgList.addAll(toOrgList);
        assetAbs.assetTransfer(new AssetTransferCmd()
                .setEmployeeChangeId(employeeChange.getId())
                .setEmployeeId(userId)
                .setCompanyId(companyId)
                .setTransfers(transfers)
                .setOrgList(orgList));
        // 转移记录
        Db.save(employeeChange);
    }

    private String save(CusEmployeeOptDto employeeDto){
        AsCusEmployee employee = BeanUtil.copyProperties(employeeDto, AsCusEmployee.class);
        Long companyId = LoginUserThreadLocal.getCompanyId();
        if (Edition.isLocal()) {
            // 创建账号
            String rawPassword = "jc123456";
            String account = AccountNumberUtils.getMaxAccount();
            AsCusUser cusUser = new AsCusUser()
                    .setAccount(account)
                    .setCompanyId(companyId)
                    .setMobile(employee.getMobile())
                    .setEmail(employee.getEmail())
                    .setSource(1);
            cusUser.setPassword(new BCryptPasswordEncoder().encode(rawPassword));
            // 保存员工
            this.insert(employee, LoginUserThreadLocal.getCompanyId());
            cusUser.setId(employee.getId());
            Db.save(cusUser);
            // 绑定账号
            AsAccountEmployee accountEmployee = new AsAccountEmployee()
                    .setAccountId(cusUser.getId())
                    .setEmployeeId(employee.getId())
                    .setCompanyId(companyId);
            accountEmployeeService.save(accountEmployee);
            // 初始化数据权限
            dataPermissionService.initDataPermission(companyId, employee.getId(), BaseConstant.COMMON_ROLE);
        } else {
            // 保存员工
            this.insert(employee, LoginUserThreadLocal.getCompanyId());
        }

        // 初始化用户查询视图
        assetQueryViewAbs.initUserView(new AssetQueryViewInitCmd()
                .setCompanyId(LoginUserThreadLocal.getCompanyId()).setEmployeeId(employee.getId()));

        // 保存员工组织关系
        List<Long> orgId = employeeDto.getOrgId();
        List<AsUserOrg> userOrgList =
                orgId.stream()
                        .map(it -> new AsUserOrg().setOrgId(it).setUserId(employee.getId()))
                        .collect(Collectors.toList());
        userOrgService.saveBatch(userOrgList);

        // 初始化 as_cus_employee_setting 配置表
        employeeSettingService.init(employee.getId());

        // 初始化 as_cus_employee_ext 扩展表
        employeeExtService.add(employee.getId());
        // 钉钉自建员工标记
        Edition.ding(() -> employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getType, 2).set(AsCusEmployeeExt::getAccountStatus, 1).eq(AsCusEmployeeExt::getId, employee.getId())));
        // 微信自建员工标记
        Edition.weixin(() -> employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getType, 3).set(AsCusEmployeeExt::getAccountStatus, 1).eq(AsCusEmployeeExt::getId, employee.getId())));
        // 本地部署自动激活员工
        Edition.local(() -> employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getAccountStatus, 3).eq(AsCusEmployeeExt::getId, employee.getId())));
        // 保存用户公司关系
        cusUserCompanyService.add(companyId, employee.getId());
        return employee.getId().toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(CusEmployeeOptDto employeeDto) {
        this.save(employeeDto);
        return true;
    }

    @Override
    public String insertV2(CusEmployeeOptDto employeeDto) {
        return this.save(employeeDto);
    }

    @Override
    public Boolean insert(AsCusEmployee employee, Long companyId) {
        String no = employee.getEmpNo();
        if (StrUtil.isEmpty(no)) {
            employee.setEmpNo(StrUtil.EMPTY);
        } else {
            AsCusEmployee asCusEmployee =
                    getOne(
                            Wrappers.<AsCusEmployee>lambdaQuery()
                                    .eq(AsCusEmployee::getEmpNo, employee.getEmpNo()));
            if (null != asCusEmployee) {
                throw new BusinessException(SystemResultCode.EMP_NO_EXISTS);
            }
        }
        Long id = IdUtils.getId();
        employee.setId(id);
        // 手机号+区号 + 邮箱 企业内唯一
        if (StrUtil.isNotBlank(employee.getMobile())) {
            if (Objects.nonNull(checkPhone(employee.getMobile(), companyId))) {
                throw new BusinessException(SystemResultCode.EMPLOYEE_FIELD_EXIST, "手机号");
            }
        }
        if (StrUtil.isNotBlank(employee.getEmail())) {
            if (Objects.nonNull(checkEmail(employee.getEmail(), companyId))) {
                throw new BusinessException(SystemResultCode.EMPLOYEE_FIELD_EXIST, "邮箱号");
            }
        }
        save(employee);
        return true;
    }

    @Override
    public IPage<CusEmployeeDto> selectCustomPage(Page<CusEmployeeDto> page, CusEmployeeQueryDto queryDto) {
        // 部门权限
        String deptSql = null;
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
            deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        }
        // 10w人场景特殊优化，判断是否根节点，如果是根节点，没必要查询所有人
        AsOrg rootOrg = orgService.getRootOrg(LoginUserThreadLocal.getCompanyId());
        boolean isQueryRootOrg = queryDto.getOrgId() != null && queryDto.getOrgId().equals(rootOrg.getId());
        IPage<CusEmployeeDto> employeeDtoIPage;
        // 企业微信兼容，员工搜索
        if (StrUtil.isNotEmpty(queryDto.getKw()) && Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).empSearch(queryDto.getKw());
            employeeDtoIPage = getBaseMapper().selectCustomPage(page, queryDto, deptSql, unionIds, isQueryRootOrg);
        } else {
            employeeDtoIPage = getBaseMapper().selectCustomPage(page, queryDto, deptSql, null, isQueryRootOrg);
        }

        // 回填主管信息
        List<Long> empIds = employeeDtoIPage.getRecords().stream().map(CusEmployeeDto::getId).collect(Collectors.toList());
        Set<Long> orgDirections = new HashSet<>();
        if (CollUtil.isNotEmpty(empIds)) {
            List<Long> orgIds = new ArrayList<>();
            if (queryDto.getOrgId() == null) {
                List<AsUserOrg> userOrgList = userOrgService.list(Wrappers.lambdaQuery(AsUserOrg.class).in(AsUserOrg::getUserId, empIds));
                orgIds = userOrgList.stream().map(AsUserOrg::getOrgId).collect(Collectors.toList());
            } else {
                orgIds = ListUtil.of(queryDto.getOrgId());
            }
            if (CollUtil.isNotEmpty(orgIds)) {
                List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getDirector)
                        .in(AsOrg::getId, orgIds));
                for (AsOrg asOrg : orgList) {
                    if (asOrg.getDirector() != null) {
                        orgDirections.addAll(asOrg.getDirector());
                    }
                }
            }
        }

        employeeDtoIPage.getRecords().forEach(f -> f.setIsDirector(orgDirections.contains(f.getId())));
        userOrg(employeeDtoIPage.getRecords());
        return employeeDtoIPage;
    }

    @Override
    public List<CusEmployeeDto> listCusEmployee(CusEmployeeQueryDto queryDto) {
        List<CusEmployeeDto> list;
        // 部门权限
        String deptSql = null;
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
            deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        }
        // 10w人场景特殊优化，判断是否根节点，如果是根节点，没必要查询所有人
        AsOrg rootOrg = orgService.getRootOrg(LoginUserThreadLocal.getCompanyId());
        boolean isQueryRootOrg = queryDto.getOrgId() != null && queryDto.getOrgId().equals(rootOrg.getId());
        if (queryDto.getIsOnlyShowTop()) {
            if (StrUtil.isNotEmpty(queryDto.getKw()) && Edition.isWeixin()) {
                List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).empSearch(queryDto.getKw());
                list = getBaseMapper().selectCustomPage(queryDto.buildIPage(), queryDto, deptSql, unionIds, isQueryRootOrg).getRecords();
            } else {
                list = getBaseMapper().selectCustomPage(queryDto.buildIPage(), queryDto, deptSql, null, isQueryRootOrg).getRecords();
            }
        } else {
            if (StrUtil.isNotEmpty(queryDto.getKw()) && Edition.isWeixin()) {
                List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).empSearch(queryDto.getKw());
                list = getBaseMapper().selectCustomPage(queryDto, deptSql, unionIds, isQueryRootOrg);
            } else {
                list = getBaseMapper().selectCustomPage(queryDto, deptSql, null, isQueryRootOrg);
            }
        }
        userOrg(list);
        fillUserCompany(list);
        return list;
    }

    @Override
    public List<CusEmployeeDto> listHasAccountEmployee(CusEmployeeQueryDto dto) {
        return this.getBaseMapper().selectHasAccountEmployee(dto.buildIPage(), LoginUserThreadLocal.getCompanyId(), dto).getRecords();
        // List<AsAccountEmployee> accountEmployees =
        //         accountEmployeeService.listByCompanyId(LoginUserThreadLocal.getCompanyId());
        // if (CollUtil.isEmpty(accountEmployees)) {
        //     return Collections.emptyList();
        // }
        // Set<Long> empIds =
        //         accountEmployees.stream()
        //                 .map(AsAccountEmployee::getEmployeeId)
        //                 .collect(Collectors.toSet());
        // return this.page(
        //                 dto.buildIPage(),
        //                 Wrappers.lambdaQuery(AsCusEmployee.class)
        //                         .select(AsCusEmployee::getId, AsCusEmployee::getEmpName, AsCusEmployee::getEmpNo)
        //                         .in(AsCusEmployee::getId, empIds)
        //                         .and(StrUtil.isNotBlank(dto.getKw()), v -> v.like(AsCusEmployee::getEmpName, dto.getKw()).or().like(AsCusEmployee::getEmpNo, dto.getKw()))
        //         ).getRecords().stream()
        //         .map(v -> new CusEmployeeDto().setId(v.getId()).setEmpName(v.getEmpName()).setEmpNo(v.getEmpNo()))
        //         .collect(Collectors.toList());
    }

   /* @Override
    public List<Long> getOrgIds(CusEmployeeQueryDto queryDto, boolean isAct) {
        Boolean isAdmin = LoginUserThreadLocal.getCusUser().getIsAdmin();
        String key = AssetConstant.DATA_PERMISSION_ORG + ":" + AssetConstant.AUTHORITY_DEPTS;
        UserDataPermsDto dataPermsCache = dataPermissionCacheService.getUserDataPermsCache(
                        LoginUserThreadLocal.getCompanyId(),
                        LoginUserThreadLocal.getCurrentUserId());
        List<Long> permsIds = dataPermsCache.getAuthorityData(key);
        List<Long> orgIds = Lists.newArrayList();
        if (ObjectUtil.isNotNull(queryDto.getOrgId())) {
            switch (edition) {
                case "local":
                    if (permsIds.contains(queryDto.getOrgId()) || isAdmin) {
                        orgIds.add(queryDto.getOrgId());
                    }
                    break;
                default:
                    if (permsIds.contains(queryDto.getOrgId())) {
                        orgIds.add(queryDto.getOrgId());
                    }
            }
            if (permsIds.contains(queryDto.getOrgId()) || isAdmin) {
                orgIds.add(queryDto.getOrgId());
            }
            if (!isAct) {
                List<AsOrg> orgList = orgService.list();
                orgList.parallelStream()
                        .filter(asOrg -> ObjectUtil.equal(asOrg.getId(), queryDto.getOrgId()))
                        .findFirst()
                        .ifPresent(
                                asOrg -> {
                                    // if (ObjectUtil.equal(asOrg.getPid(), 0L)) {
                                    orgService.getChildIdsByParentId(
                                            permsIds, orgList, queryDto.getOrgId(), orgIds);
                                    // }
                                });
            }
        }
        if (Objects.isNull(queryDto.getOrgId()) && queryDto.getIsOnlyShowHasPermEmp() && !LoginUserThreadLocal.getCusUser().getIsAdmin()) {
            return permsIds;
        }
        return orgIds;
    }*/

    @Override
    public void checkRegisterMobile(String mobile) {
        Map<String, String> map = getBaseMapper().checkRegisterMobile(mobile);
        if (CollUtil.isNotEmpty(map)) {
            if (ObjectUtil.isNotNull(map.get("empId"))) {
                if (StrUtil.isNotBlank(map.get("account"))) {
                    throw new BusinessException(SystemResultCode.USER_REGISTER_WITH_ACCOUNT);
                }
                if (StrUtil.isNotBlank(map.get("mobile"))) {
                    throw new BusinessException(SystemResultCode.USER_REGISTER_WITHOUT_ACCOUNT);
                }
            }
        }
    }

    @Override
    public void checkRegisterEmail(String email) {
        Map<String, String> map = getBaseMapper().checkRegisterEmail(email);
        if (CollUtil.isNotEmpty(map)) {
            if (ObjectUtil.isNotNull(map.get("empId"))) {
                if (StrUtil.isNotBlank(map.get("account"))) {
                    throw new BusinessException(SystemResultCode.USER_REGISTER_EMAIL_WITH_ACCOUNT);
                }
                if (StrUtil.isNotBlank(map.get("email"))) {
                    throw new BusinessException(
                            SystemResultCode.USER_REGISTER_EMAIL_WITHOUT_ACCOUNT);
                }
            }
        }
    }

    @Override
    public List<List<AsCusEmployee>> getMultiManager(Long orgId, Integer level, Integer handleType) {
        List<List<AsCusEmployee>> managerList = new ArrayList<>();
        AsCusEmployee autoPassEmp;
        if (handleType == 1) {
            autoPassEmp = new AsCusEmployee().setId(-1L).setEmpName("自动通过");
        } else {
            autoPassEmp = getAdministrator();
        }
        getMultiManager(orgId, level, autoPassEmp, 0, managerList);
        return managerList;
    }

    private void getMultiManager(
            Long orgId,
            Integer level,
            AsCusEmployee autoPassEmp,
            int currLevel,
            List<List<AsCusEmployee>> managerList) {
        if (currLevel >= level || orgId == 0) {
            return;
        }
        AsOrg asOrg = orgService.getById(orgId);
        List<AsCusEmployee> managerEmp =
                CollUtil.isNotEmpty(asOrg.getDirector())
                        ? this.listByIds(asOrg.getDirector())
                        : null;

        if (CollUtil.isEmpty(managerEmp)) {
            managerList.add(Collections.singletonList(autoPassEmp));
        } else {
            for (AsCusEmployee employee : managerEmp) {
                Optional<AsAccountEmployee> optional =
                        accountEmployeeService.getEmployeeAccount(employee.getId());
                if (!optional.isPresent()) {
                    managerList.add(Collections.singletonList(autoPassEmp));
                } else {
                    managerList.add(Collections.singletonList(employee));
                }
            }
        }
        getMultiManager(asOrg.getPid(), level, autoPassEmp, ++currLevel, managerList);
    }

    @Override
    public List<AsCusEmployee> getWorkflowManager(Long orgId, Integer level, Integer handleType, Boolean isAgent) {
        AsCusEmployee autoPassEmp;
        if (handleType == 1) {
            autoPassEmp = new AsCusEmployee().setId(-1L).setEmpName("自动通过");
        } else {
            autoPassEmp = getAdministrator();
        }
        return getWorkflowManager(orgId, level, autoPassEmp, isAgent, 0);
    }

    private List<AsCusEmployee> getWorkflowManager(
            Long orgId, Integer level, AsCusEmployee autoPassEmp, Boolean isAgent, int currLevel) {
        // 查询组织
        AsOrg asOrg = orgService.getById(orgId);

        if (asOrg == null) {
            return Collections.singletonList(autoPassEmp);
        }

        if (level - currLevel == 1) {
            List<AsCusEmployee> managerEmp =
                    CollUtil.isNotEmpty(asOrg.getDirector())
                            ? this.listByIds(asOrg.getDirector())
                            : ListUtil.empty();
            if (CollUtil.isEmpty(managerEmp)) {
                if (isAgent) {
                    return getSupperWorkflowManager(asOrg.getPid(), autoPassEmp);
                } else {
                    return Collections.singletonList(autoPassEmp);
                }
            } else {
                Map<Long, AsCusEmployee> employeeMap = managerEmp.stream().map(emp -> {
                    Optional<AsAccountEmployee> optional =
                            accountEmployeeService.getEmployeeAccount(emp.getId());
                    if (!optional.isPresent()) {
                        return autoPassEmp;
                    } else {
                        return emp;
                    }
                }).collect(Collectors.toMap(AsCusEmployee::getId, k -> k, (k1, k2) -> k1));
                managerEmp = new ArrayList<>(employeeMap.values());
            }
            return managerEmp;
        }
        return getWorkflowManager(asOrg.getPid(), level, autoPassEmp, isAgent, ++currLevel);
    }

    private List<AsCusEmployee> getSupperWorkflowManager(Long orgId, AsCusEmployee autoPassEmp) {
        AsOrg pOrg = orgService.getById(orgId);
        if (pOrg == null) {
            return Collections.singletonList(autoPassEmp);
        }

        List<AsCusEmployee> pManagerEmp =
                CollUtil.isNotEmpty(pOrg.getDirector()) ? this.listByIds(pOrg.getDirector()) : null;
        if (CollUtil.isEmpty(pManagerEmp)) {
            return Collections.singletonList(autoPassEmp);
        }

        return pManagerEmp;
    }

    @Override
    public AsCusEmployee getAdministrator() {
        AsCusRole cusRole =
                cusRoleService.getOne(
                        Wrappers.<AsCusRole>lambdaQuery()
                                .eq(AsCusRole::getCompanyId, LoginUserThreadLocal.getCompanyId())
                                .eq(AsCusRole::getRoleCode, BaseConstant.ADMIN_ROLE));
        AsUserRole userRole =
                cusUserRoleService.getOne(
                        new LambdaQueryWrapper<AsUserRole>()
                                .eq(AsUserRole::getRoleId, cusRole.getId()),
                        false);
        AsCusEmployee one = this.getById(userRole.getUserId());
        BusinessExceptionUtil.checkNotNull(one, "找不到超管");
        return one;
    }

    @Override
    public List<Long> getAssetAdminIds(Long companyId) {
        AsCusRole cusRole = cusRoleService.getOne(Wrappers.lambdaQuery(AsCusRole.class)
                .eq(AsCusRole::getRoleCode, BaseConstant.ASSET_ADMIN_ROLE)
                .eq(AsCusRole::getCompanyId, companyId));
        List<AsUserRole> userRoles =
                cusUserRoleService.list(
                        Wrappers.lambdaQuery(AsUserRole.class)
                                .eq(AsUserRole::getRoleId, cusRole.getId()));
        return userRoles.stream().map(AsUserRole::getUserId).collect(Collectors.toList());
    }

    @Override
    public AsCusEmployee getAdministratorByCompanyId(Long companyId) {
        AsCusRole cusRole = cusRoleMapper.getAdministratorByCompanyId(companyId);
        AsUserRole userRole =
                cusUserRoleService.getOne(
                        new LambdaQueryWrapper<AsUserRole>()
                                .eq(AsUserRole::getRoleId, cusRole.getId()),
                        false);
        if (userRole == null) {
            log.error("company {} administrator not exists", companyId);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前企业超管不存在");
        }
        return this.getById(userRole.getUserId());
    }

    private void userOrg(List<CusEmployeeDto> list) {
        List<Long> empIds = list.stream().map(CusEmployeeDto::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(empIds)) {
            List<CusUserOrgDto> org = this.getBaseMapper().selectUserOrgByUserIds(empIds);
            if (CollUtil.isNotEmpty(org)) {
                ConcurrentMap<Long, List<CusUserOrgDto>> group = org.stream().collect(Collectors.groupingByConcurrent(CusUserOrgDto::getEmpId));
                list.forEach(v -> v.setOrgList(group.getOrDefault(v.getId(), new ArrayList<>())));
            }
        }
    }

    @Override
    public void fillUserCompany(List<CusEmployeeDto> list) {
        List<Long> orgIds = list.stream()
                .map(CusEmployeeDto::getOrgList)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .map(CusUserOrgDto::getOrgId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(orgIds)) {
            return;
        }
        LambdaQueryWrapper<AsOrg> queryWrapper = Wrappers.lambdaQuery(AsOrg.class)
                .select(AsOrg::getId, AsOrg::getOrgType, AsOrg::getOrgName, AsOrg::getCompanyOwner);
        // 当前组织信息
        queryWrapper.in(AsOrg::getId, orgIds);
        List<AsOrg> orgs = orgService.list(queryWrapper);
        // 组织类型不是公司时寻找所属公司
        List<Long> companyOwnerIds = orgs.stream().filter(v -> v.getOrgType().equals(2)).map(AsOrg::getCompanyOwner).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(companyOwnerIds)) {
            queryWrapper.clear();
            queryWrapper.select(AsOrg::getId, AsOrg::getOrgType, AsOrg::getOrgName, AsOrg::getCompanyOwner)
                    .in(AsOrg::getId, companyOwnerIds);
            orgs.addAll(orgService.list(queryWrapper));
        }
        // 去重
        orgs = orgs.stream().filter(DeduplicationUtil.distinctByKey(AsOrg::getId)).collect(Collectors.toList());
        ConcurrentMap<Long, AsOrg> orgMap = orgs.stream().collect(Collectors.toConcurrentMap(AsOrg::getId, v -> v));
        for (CusEmployeeDto dto : list) {
            if (CollUtil.isEmpty(dto.getOrgList())) {
                return;
            }
            List<CusUserOrgDto> copmanyList = dto.getOrgList().stream()
                    .filter(k -> orgMap.containsKey(k.getOrgId()))
                    .map(k -> orgMap.get(k.getOrgId()))
                    .filter(k -> orgMap.containsKey(k.getId()) && orgMap.containsKey(k.getCompanyOwner()))
                    .map(k -> {
                        // 节点自己就是公司类型
                        if (k.getOrgType() == 1) {
                            return orgMap.get(k.getId());
                        }
                        // 节点自己不是公司类型
                        return orgMap.get(k.getCompanyOwner());
                    })
                    .map(k -> new CusUserOrgDto().setEmpId(dto.getId()).setOrgId(k.getId()).setOrgName(k.getOrgName()))
                    .collect(Collectors.toList());
            dto.setCompanyList(copmanyList);
        }
    }

    @Override
    public void removeCusEmployee(List<Long> ids) {
        getBaseMapper().removeCusEmployee(ids);
    }

    @Override
    public String getRecommendEmpNo(Long companyId) {
        if (companyId == null) {
            companyId = 0L;
        }
        return StringUtils.getMaxIncreFromList(getBaseMapper().getEmpNoList(companyId), DEFAULT_EMP_NO);
    }

    @Override
    public CusEmployeeDto currentUserInfo() {
        return getBaseMapper().currentUserInfo(LoginUserThreadLocal.getCurrentUserId());
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<ImportErrorCO> errorCOS =
                this.importErrorAbs.listImportError(
                        new ImportErrorListQry()
                                .setTaskId(taskId)
                                .setImportType(DictConstant.IMPORT_TYPE_EMPLOYEE));
        return errorCOS.stream().map(ImportErrorCO::getSheetModelList).collect(Collectors.toList());
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        ImportErrorCO importError =
                new ImportErrorCO()
                        .setTaskId(importErrorDto.getTaskId())
                        .setErrorNum(0)
                        .setImportType(DictConstant.IMPORT_TYPE_EMPLOYEE)
                        .setSheetModelList(importErrorDto.getHeadModelList());
        this.importErrorAbs.saveImportError(new ImportErrorSaveCmd().setImportErrorCO(importError));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSheetData(EmployeeImportDto importDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();

        CusEmployeeExcelDto employeeDto = importDto.getEmployeeDto();
        AsCusEmployee employee = BeanUtil.copyProperties(employeeDto, AsCusEmployee.class);

        // 保存员工
        if (StringUtils.isEmpty(employee.getEmpNo())) {
            String empNo = recommendEmpNo(companyId);
            employee.setEmpNo(empNo);
        } else {
            AsCusEmployee asCusEmployee =
                    getOne(
                            Wrappers.<AsCusEmployee>lambdaQuery()
                                    .eq(AsCusEmployee::getEmpNo, employee.getEmpNo()));
            if (null != asCusEmployee) {
                LuckySheetModel emp = importDto.getSheetModelList().get(4);
                if (ObjectUtil.isNull(emp.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("员工工号已存在");
                    emp.getV().setPs(comment);
                }
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }

        if (importDto.getErrorNum() > 0) {
            ImportErrorSaveOrUpdateCmd cmd =
                    new ImportErrorSaveOrUpdateCmd()
                            .setImportErrorCO(copyToAsEmployeeImportError(importDto));
            importErrorAbs.saveOrUpdateImportError(cmd);
            redisService.hIncr(
                    RedisConstant.companyImportKey("employee", LoginUserThreadLocal.getCompanyId()),
                    "error",
                    1L);
            return false;
        }

        Long id = IdUtils.getId();
        employee.setId(id);
        save(employee);

        if (Edition.isLocal()) {
            // 创建账号
            String rawPassword = "jc123456";
            String account = AccountNumberUtils.getMaxAccount();
            AsCusUser cusUser = new AsCusUser()
                    .setAccount(account)
                    .setCompanyId(companyId)
                    .setMobile(employee.getMobile())
                    .setEmail(employee.getEmail())
                    .setSource(1);
            cusUser.setPassword(new BCryptPasswordEncoder().encode(rawPassword));
            cusUser.setId(employee.getId());
            Db.save(cusUser);
            // 绑定账号
            AsAccountEmployee accountEmployee = new AsAccountEmployee()
                    .setAccountId(cusUser.getId())
                    .setEmployeeId(employee.getId())
                    .setCompanyId(companyId);
            accountEmployeeService.save(accountEmployee);
            // 初始化数据权限
            dataPermissionService.initDataPermission(companyId, employee.getId(), BaseConstant.COMMON_ROLE);
        }

        // 初始化用户查询视图
        assetQueryViewAbs.initUserView(new AssetQueryViewInitCmd()
                .setCompanyId(LoginUserThreadLocal.getCompanyId()).setEmployeeId(employee.getId()));
        // 保存组织员工
        List<Long> orgIds = employeeDto.getOrgIds();
        List<AsUserOrg> userOrgList =
                orgIds.stream()
                        .map(o -> new AsUserOrg().setOrgId(o).setUserId(id))
                        .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(userOrgList)) {
            userOrgService.saveBatch(userOrgList);
        }

        // 初始化 as_cus_employee_setting配置表
        employeeSettingService.init(employee.getId());

        // 用户扩展表
        employeeExtService.add(employee.getId());
        // 钉钉自建员工标记
        Edition.ding(() -> employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getType, 2).set(AsCusEmployeeExt::getAccountStatus, 1).eq(AsCusEmployeeExt::getId, employee.getId())));
        Edition.weixin(() -> employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getType, 2).set(AsCusEmployeeExt::getAccountStatus, 1).eq(AsCusEmployeeExt::getId, employee.getId())));
        // 本地部署自动激活员工
        Edition.local(() -> employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getAccountStatus, 3).eq(AsCusEmployeeExt::getId, employee.getId())));
        // 保存用户公司关系
        AsUserCompany asUserCompany =
                new AsUserCompany().setUserId(employee.getId()).setCompanyId(companyId);
        cusUserCompanyService.save(asUserCompany);
        // 保存用户角色关系
        List<AsCusRole> asCusRoles = roleService.listByCompanyId(companyId);
        Optional<AsCusRole> commonRole =
                asCusRoles.stream()
                        .filter(
                                role ->
                                        StringUtils.equals(
                                                role.getRoleCode(), BaseConstant.COMMON_ROLE))
                        .findAny();
        if (!Edition.isLocal()) {
            commonRole.ifPresent(
                    role ->
                            cusUserRoleService.saveBatch(
                                    Collections.singletonList(
                                            new AsUserRole()
                                                    .setUserId(employee.getId())
                                                    .setRoleId(role.getId()))));
        }
        redisService.hIncr(
                RedisConstant.companyImportKey("employee", LoginUserThreadLocal.getCompanyId()),
                "success",
                1L);
        return true;
    }

    @Override
    public CusEmployeeDto getInfo(Long empId) {
        return this.getBaseMapper().getInfo(empId);
    }

    @Override
    public void transferEmp(RemoveEmployDto employ) {
        transferService.transferEmp(employ);
    }

    @Override
    public List<CusEmployeeDto> selectListByIds(List<Long> ids) {
        return this.getBaseMapper().selectAllByIds(ids);
    }

    @Override
    public void initEmployeeRoleDataScope(Long... empIds) {
        if (Objects.isNull(empIds) || empIds.length == 0) {
            return;
        }
        Long companyId = LoginUserThreadLocal.getCompanyId();
        Stream.of(empIds)
                .forEach(
                        empId -> {
                            // 清除角色
                            cusUserRoleService.remove(
                                    Wrappers.lambdaUpdate(AsUserRole.class)
                                            .eq(AsUserRole::getUserId, empId));
                            // 初始化基本角色
                            AsCusRole role =
                                    roleService.getOne(
                                            Wrappers.lambdaQuery(AsCusRole.class)
                                                    .eq(AsCusRole::getCompanyId, companyId)
                                                    .eq(
                                                            AsCusRole::getRoleCode,
                                                            BaseConstant.COMMON_ROLE));
                            if (Objects.nonNull(role)) {
                                cusUserRoleService.save(
                                        new AsUserRole().setRoleId(role.getId()).setUserId(empId));
                            }
                            // 重新初始化数据权限为普通普通
                            dataPermissionService.initDataPermission(
                                    companyId, empId, BaseConstant.COMMON_ROLE);
                        });
    }

    private ImportErrorCO copyToAsEmployeeImportError(EmployeeImportDto importDto) {
        ImportErrorCO importError = new ImportErrorCO();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_EMPLOYEE);
        List<LuckySheetModel> sheetModelList = importDto.getSheetModelList();
        importError.setSheetModelList(new ArrayList<>(sheetModelList));
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }

    @Override
    public Boolean changeMobile(Long empId, String newMobile) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        AsCusEmployee employee = this.getById(empId);
        if (Objects.isNull(employee)) {
            throw new BusinessException(SystemResultCode.EMP_NOT_EXISTS);
        }
        List<AsCusEmployee> list =
                this.list(
                        Wrappers.lambdaQuery(AsCusEmployee.class)
                                .eq(AsCusEmployee::getMobile, newMobile)
                                .eq(AsCusEmployee::getCompanyId, companyId));
        if (!CollUtil.isEmpty(list)) {
            throw new BusinessException(SystemResultCode.EMPLOYEE_FIELD_EXIST, "新手机号");
        }
        return this.update(
                Wrappers.lambdaUpdate(AsCusEmployee.class)
                        .set(AsCusEmployee::getMobile, newMobile)
                        .eq(AsCusEmployee::getId, empId));
    }

    @Override
    public AsCusEmployee getByIdWithDel(Long id) {
        return this.getBaseMapper().getByIdWithDel(id);
    }

    @Override
    public void updateAllCompanyEmpMobile(String mobile, List<Long> empIds) {
        this.getBaseMapper().updateAllCompanyEmpMobile(mobile, empIds);
    }

    @Override
    public void updateAllCompanyEmpEmail(String email, List<Long> empIds) {
        this.getBaseMapper().updateAllCompanyEmpEmail(email, empIds);
    }

    @Override
    public AsCusEmployee getDeleted(Long empId) {
        return this.getBaseMapper().selectByIsDeleted(empId);
    }

    @Override
    public Long getOne(String empName, String empNo) {
        LoginUserDto userDto = LoginUserThreadLocal.get();
        if (userDto == null
                || userDto.getCusUser() == null
                || userDto.getCusUser().getCompanyId() == null) {
            return null;
        }
        Long companyId = userDto.getCusUser().getCompanyId();
        if (StrUtil.isNotEmpty(empName)) {
            String cacheKey = RedisConstant.employeeDictKey() + ":" + companyId + ":name:" + empName;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsCusEmployee one = getOne(Wrappers.lambdaQuery(AsCusEmployee.class)
                        .eq(AsCusEmployee::getCompanyId, companyId)
                        .eq(AsCusEmployee::getEmpName, empName), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        } else if (StrUtil.isNotEmpty(empNo)) {
            String cacheKey = RedisConstant.employeeDictKey() + ":" + companyId + ":code:" + empNo;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsCusEmployee one = getOne(Wrappers.lambdaQuery(AsCusEmployee.class)
                        .eq(AsCusEmployee::getCompanyId, companyId)
                        .eq(AsCusEmployee::getEmpNo, empNo), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEmpNo(EmployeeModifyDto employeeModifyDto) {
        // 查询当前数据
        AsCusEmployee emp = this.getById(employeeModifyDto.getId());
        if (emp == null) {
            return false;
        }
        if (StrUtil.isNotBlank(employeeModifyDto.getEmpNo())) {
            if (this.count(new QueryWrapper<AsCusEmployee>().lambda()
                    .eq(AsCusEmployee::getEmpNo, employeeModifyDto.getEmpNo())
                    .eq(AsCusEmployee::getIsDelete, Boolean.FALSE)
                    .ne(AsCusEmployee::getId, employeeModifyDto.getId())) > 0) {
                throw new BusinessException(SystemResultCode.PARAM_EXISTS, "员工工号", employeeModifyDto.getEmpNo());
            }
        }

        //修改员工工号，并且记录员工工号来源为手工录入
        this.update(Wrappers.lambdaUpdate(AsCusEmployee.class)
                .set(AsCusEmployee::getEmpNo, StrUtil.isNotBlank(employeeModifyDto.getEmpNo()) ? employeeModifyDto.getEmpNo() : "")
                .set(AsCusEmployee::getEmpNoSource, 1)
                .set(StrUtil.isNotBlank(employeeModifyDto.getRemark()), AsCusEmployee::getRemark, employeeModifyDto.getRemark())
                .eq(AsCusEmployee::getId, employeeModifyDto.getId())
                .eq(AsCusEmployee::getIsDelete, Boolean.FALSE));
        SpringUtil.getBean(CacheEmpStrategy.class).evictCache(employeeModifyDto.getId());
        return true;
    }

    @Override
    public Boolean verifyEmpName(CusEmployeeOptDto employee) {
        //企微判断用户重名和其他环境还不一样，企微员工有2套，自建员工和企微系统员工两部分
        if (Edition.isWeixin()) {
            List<String> userIdList = SpringUtil.getBean(WeixinAdapter.class).empSearch(employee.getEmpName(), Boolean.TRUE);
            //如果是编辑用户，需要剔除自己
            if (Objects.nonNull(employee.getId()) && !CollUtil.isEmpty(userIdList)) {
                userIdList.remove(String.valueOf(employee.getId()));
            }

            //如果有其他用户重名，返回false
            if (!CollUtil.isEmpty(userIdList)) {
                return Boolean.FALSE;
            }
        }
        LambdaQueryWrapper<AsCusEmployee> queryWrapper = Wrappers.lambdaQuery(AsCusEmployee.class)
                .eq(AsCusEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsCusEmployee::getEmpName, employee.getEmpName())
                .eq(AsCusEmployee::getIsDelete, Boolean.FALSE);
        //如果是编辑，校验重名的时候排除自己
        if (Objects.nonNull(employee.getId())) {
            queryWrapper.ne(AsCusEmployee::getId, employee.getId());
        }

        return this.count(queryWrapper) > 0 ? Boolean.FALSE : Boolean.TRUE;
    }

    @Override
    public Boolean batchSaveCondition(List<AsCusEmployee> employeeList) {
        if (CollUtil.isEmpty(employeeList)) {
            return Boolean.FALSE;
        }

        LambdaQueryWrapper<AsCusEmployee> queryWrapper = Wrappers.lambdaQuery(AsCusEmployee.class)
                .eq(AsCusEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsCusEmployee::getIsDelete, Boolean.FALSE);
        for (AsCusEmployee item : employeeList) {
            if (StrUtil.isBlank(item.getEmpNo())) {
                continue;
            }

            queryWrapper.eq(AsCusEmployee::getEmpNo, item.getEmpNo());
            if (this.count(queryWrapper) > 0) {
                //如果当前工号被其他用户占用，不同步工号入系统，保证系统里工号唯一性
                item.setEmpNo(null);
            }
            //设置工号为第三方系统同步
            item.setEmpNoSource(2);
        }
        return this.saveBatch(employeeList);
    }

    @Override
    public Boolean batchUpdateByIdCondition(List<AsCusEmployee> employeeList) {
        if (CollUtil.isEmpty(employeeList)) {
            return Boolean.FALSE;
        }

        for (AsCusEmployee item : employeeList) {
            if (Objects.isNull(item.getId()) || StrUtil.isBlank(item.getEmpNo())) {
                continue;
            }

            //查询当前用户是否已手工录入工号
            LambdaQueryWrapper<AsCusEmployee> queryWrapper = Wrappers.lambdaQuery(AsCusEmployee.class)
                    .eq(AsCusEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsCusEmployee::getId, item.getId())
                    .eq(AsCusEmployee::getIsDelete, Boolean.FALSE);
            AsCusEmployee data = this.getOne(queryWrapper);
            if (Objects.isNull(data)) {
                continue;
            }

            //如果在系统中已经手工录入工号，就不进行同步工号
            if (StrUtil.isNotBlank(data.getEmpNo())
                    && Objects.nonNull(data.getEmpNoSource())
                    && data.getEmpNoSource() == 1) {
                item.setEmpNo(null);
                continue;
            }

            //查询当前工号是否已在系统中使用，如果工号已被其他用户使用，则不进行工号同步
            LambdaQueryWrapper<AsCusEmployee> countWrapper = Wrappers.lambdaQuery(AsCusEmployee.class)
                    .eq(AsCusEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsCusEmployee::getEmpNo, item.getEmpNo())
                    .eq(AsCusEmployee::getIsDelete, Boolean.FALSE)
                    .ne(AsCusEmployee::getId, item.getId());
            if (this.count(countWrapper) > 0) {
                item.setEmpNo(null);
            }

            //这个时候会更新工号，需要设置工号来源为第三方系统同步
            item.setEmpNoSource(2);
        }
        return this.updateBatchById(employeeList);
    }
}
