package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsAccountEmployeeMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.CusUserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 */
@Service
public class AsAccountEmployeeServiceImpl extends ServiceImpl<AsAccountEmployeeMapper, AsAccountEmployee> implements AsAccountEmployeeService {

    @Autowired
    private CusUserService accountService;

    @Override
    public Optional<AsAccountEmployee> getEmployeeAccount(Long employeeId) {
        AsAccountEmployee employee = this.getOne(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getEmployeeId, employeeId)
        );
        return Optional.ofNullable(employee);
    }

    @Override
    public Optional<AsCusUser> getEmployAccount(Long employeeId) {
        Optional<AsAccountEmployee> accountEmployeeOptional = getEmployeeAccount(employeeId);
        return accountEmployeeOptional.map(asAccountEmployee -> accountService.getById(asAccountEmployee.getAccountId()));
    }

    @Override
    public boolean unbindEmployee(Long employeeId) {
        return this.remove(
                Wrappers.lambdaUpdate(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getEmployeeId, employeeId)
        );
    }

    @Override
    public boolean batchUnbindEmploy(List<Long> employeeIds) {
        return this.remove(
                Wrappers.lambdaUpdate(AsAccountEmployee.class)
                        .in(AsAccountEmployee::getEmployeeId, employeeIds)
        );
    }

    @Override
    public boolean unbindCompany(Long companyId, Long accountId) {
        return this.remove(
                Wrappers.lambdaUpdate(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getCompanyId, companyId)
                        .eq(AsAccountEmployee::getAccountId, accountId)
        );
    }

    @Override
    public List<AsAccountEmployee> listByEmployeeId(List<Long> employeeIds) {
        if (CollUtil.isEmpty(employeeIds)) {
            return Collections.emptyList();
        }
        return list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .in(AsAccountEmployee::getEmployeeId, employeeIds)
        );
    }
    @Override
    public List<Long> filterHasAccountEmp(List<Long> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        return this.getBaseMapper().filterHasAccountEmpIds(empIds);
    }

    @Override
    public List<Long> hasAccountEmpId(Long companyId) {
        return getBaseMapper().hasAccountEmpId(companyId);
    }
}
