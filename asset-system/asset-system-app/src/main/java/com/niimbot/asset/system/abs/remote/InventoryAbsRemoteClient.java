package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.InventoryAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/7 09:52
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.inventory.domain.abs.impl.InventoryAbsImpl")
@FeignClient(name = "asset-inventory", url = "https://{gateway}/client/abs/inventory/inventoryAbs/")
public interface InventoryAbsRemoteClient extends InventoryAbs {
}
