package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsToolbox;
import com.niimbot.asset.system.service.AsToolboxService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AsToolboxQueryDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import javax.validation.constraints.NotEmpty;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 工具箱控制器
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@RestController
@RequestMapping("server/system/toolbox")
@Slf4j
@Validated
@RequiredArgsConstructor
public class AsToolboxServiceController {

    private final AsToolboxService toolboxService;

    @PostMapping
    public Boolean add(@RequestBody AsToolbox toolbox) {
        if (!toolboxService.save(toolbox)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }

    @PutMapping("/setFrequency")
    public Boolean setFrequency(@RequestBody AsToolbox toolbox) {
        Short toolboxType = toolbox.getToolboxType();
        Short position = toolbox.getPosition();
        toolboxService.list().stream()
                .filter(tool -> tool.getToolboxType().intValue() == toolboxType.intValue()
                        && position.equals(tool.getPosition()))
                .map(AsToolbox::getSortNum).max(Integer::compareTo).ifPresent(max -> toolbox.setSortNum(++max));
        return edit(toolbox);
    }

    @PutMapping
    public Boolean edit(@RequestBody AsToolbox toolbox) {
        LambdaUpdateWrapper<AsToolbox> wrapper = Wrappers.<AsToolbox>lambdaUpdate()
                .eq(AsToolbox::getId, toolbox.getId())
                .set(StrUtil.isNotBlank(toolbox.getShowName()), AsToolbox::getShowName, toolbox.getShowName())
                .set(ObjectUtil.isNotNull(toolbox.getPosition()), AsToolbox::getPosition, toolbox.getPosition())
                .set(ObjectUtil.isNotNull(toolbox.getEnableApp()), AsToolbox::getEnableApp, toolbox.getEnableApp())
                .set(ObjectUtil.isNotNull(toolbox.getEnablePc()), AsToolbox::getEnablePc, toolbox.getEnablePc())
                .set(StrUtil.isNotBlank(toolbox.getToolboxIcon()), AsToolbox::getToolboxIcon, toolbox.getToolboxIcon());
        if (!toolboxService.update(wrapper)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 批量设置排序
     *
     * @param list list
     * @return 结果
     */
    @PutMapping("/sort")
    public Boolean sort(@RequestBody @NotEmpty List<AsToolbox> list) {
        // 前台未排序
        if (list.stream().anyMatch(toolbox -> ObjectUtil.isNull(toolbox.getSortNum()))) {
            AtomicInteger index = new AtomicInteger(1);
            list.stream().forEach(toolbox -> toolbox.setSortNum(index.getAndDecrement()));
        }
        return toolboxService.sort(list);
    }

    /**
     * 批量修改
     *
     * @param list list
     * @return 结果
     */
    @PutMapping("/batch")
    public Boolean editBatch(@RequestBody @NotEmpty List<AsToolbox> list) {
        if (!toolboxService.updateBatchById(list)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @GetMapping(value = "/{id}")
    public AsToolbox getById(@PathVariable Long id) {
        return toolboxService.getById(id);
    }

    @DeleteMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(@RequestBody List<Long> ids) {
        if (toolboxService.getBaseMapper().deleteBatchIds(ids) != ids.size()) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }
        return true;
    }

    @GetMapping(value = "/page")
    public IPage<AsToolbox> page(AsToolboxQueryDto queryDto) {
        LambdaQueryWrapper<AsToolbox> wrapper = Wrappers.<AsToolbox>lambdaQuery()
                .eq(ObjectUtil.isNotNull(queryDto.getToolboxType()), AsToolbox::getToolboxType, queryDto.getToolboxType())
                .last("order by sort_num asc");
        return toolboxService.page(queryDto.buildIPage(), wrapper);
    }

    @GetMapping(value = "/list")
    public List<AsToolbox> list(AsToolboxQueryDto queryDto) {
        LambdaQueryWrapper<AsToolbox> wrapper = Wrappers.<AsToolbox>lambdaQuery()
                .eq(ObjectUtil.isNotNull(queryDto.getToolboxType()), AsToolbox::getToolboxType, queryDto.getToolboxType())
                .last("order by sort_num asc");
        return toolboxService.list(wrapper);
    }
}
