package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.AdminPrinterAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 17:56
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.AdminPrinterAbsImpl")
@FeignClient(name = "asset-means", url = "http://localhost:8000/")
public interface AdminPrinterAbsRemoteClient extends AdminPrinterAbs {
}
