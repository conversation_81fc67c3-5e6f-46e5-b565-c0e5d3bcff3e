package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsPrivacyAgreement;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 隐私协议管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface AsPrivacyAgreementMapper extends BaseMapper<AsPrivacyAgreement> {

    /**
     * 查询平台最新隐私协议
     *
     * @param platform
     * @return
     */
    @Select("select * from as_privacy_agreement where platform = #{platform} order by create_time desc limit 1")
    AsPrivacyAgreement queryLast(@Param("platform") Integer platform);
}
