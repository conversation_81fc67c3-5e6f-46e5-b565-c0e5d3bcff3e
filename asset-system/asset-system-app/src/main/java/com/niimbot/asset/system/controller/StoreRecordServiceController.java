package com.niimbot.asset.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.dto.DataSnapshotGetQry;
import com.niimbot.asset.system.service.AsStoreRecordService;
import com.niimbot.asset.system.service.DataSnapshotService;
import com.niimbot.means.StoreRecordDto;
import com.niimbot.means.StoreRecordSearch;
import com.niimbot.means.StoreSnapshotSearch;
import com.niimbot.system.StoreRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("server/system/store")
@RequiredArgsConstructor
public class StoreRecordServiceController {

    private final AsStoreRecordService storeRecordService;

    private final DataSnapshotService dataSnapshotService;

    @PostMapping("/record/save")
    public boolean recordSave(@RequestBody StoreRecord save) {
        storeRecordService.record(save);
        return true;
    }

    @PostMapping("/record/search")
    public PageUtils<StoreRecordDto> recordSearch(@RequestBody StoreRecordSearch search) {
        return storeRecordService.search(search);
    }

    @PostMapping("/snapshot/search")
    public PageUtils<JSONObject> snapshotSearch(@RequestBody StoreSnapshotSearch search) {
        return dataSnapshotService.search(search);
    }

    @GetMapping("/snapshot/list/{id}")
    public List<JSONObject> snapshotList(@PathVariable Long id) {
        DataSnapshotGetQry qry = new DataSnapshotGetQry();
        qry.setCompanyId(LoginUserThreadLocal.getCompanyId());
        qry.setSourceId(id).setSourceType(1).setDataType(1);
        return dataSnapshotService.getSnapshots(qry);
    }

}
