package com.niimbot.asset.system.ots.process;

import com.niimbot.asset.system.ots.SystemStoreRecordOts;
import com.niimbot.asset.system.service.AsStoreRecordService;
import com.niimbot.system.StoreRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemStoreRecordOts/")
@RequiredArgsConstructor
public class SystemStoreRecordOtsProcessClient implements SystemStoreRecordOts {

    private final AsStoreRecordService storeRecordService;

    @Override
    public void record(StoreRecord saved) {
        storeRecordService.record(saved);
    }
}
