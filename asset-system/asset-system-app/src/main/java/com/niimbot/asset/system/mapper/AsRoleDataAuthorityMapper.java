package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsRoleDataAuthority;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsRoleDataAuthorityMapper extends BaseMapper<AsRoleDataAuthority> {

    /**
     * 根据角色编码查询角色数据权限
     * @param companyId
     * @param roleCode
     * @return
     */
    List<AsRoleDataAuthority> selectByRoleCode(@Param("companyId") Long companyId, @Param("roleCode") String roleCode);

    /**
     * 查询企业默认角色数据权限
     * @param companyId
     * @return
     */
    List<AsRoleDataAuthority> selectDefaultDataAuth(@Param("companyId") Long companyId);
}