package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.CusUserOrgDto;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 员工表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@EnableDataPerm(excludeMethodName = {"selectById", "getEmpNoList", "checkPhone", "checkEmail", "checkRegisterMobile", "getNumByCompanyId",
        "manageRemoveByIds", "removeCusEmployee", "allEmployee", "selectUserOrg", "selectAllByIds", "selectEmployeeRoles",
        "updateAllCompanyEmpMobile", "updateAllCompanyEmpEmail", "selectEmployeeRoles", "selectUserOrgByUserIds", "selectByIsDeleted", "selectHasAccountEmployee"})
public interface AsCusEmployeeMapper extends BaseMapper<AsCusEmployee> {

    /**
     * 字典初始化，查询全部员工
     *
     * @return 员工集合
     */
    @Select("select id, company_id, emp_name, emp_no from as_cus_employee")
    List<AsCusEmployee> allEmployee();

    /**
     * 获取公司对应的最大员工号
     *
     * @param companyId 公司id
     * @return max empNo
     */
    List<String> getEmpNoList(Long companyId);

    /**
     * 检查手机号是否存在
     *
     * @param mobile 手机号
     * @return 是否存在
     */
    AsCusEmployee checkPhone(@Param("mobile") String mobile, @Param("companyId") Long companyId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    AsCusEmployee checkEmail(@Param("email") String email, @Param("companyId") Long companyId);

    /**
     * 检查员工账号唯一性
     *
     * @param empNo empNo
     * @param empId empId
     * @return 是否存在
     */
    AsCusEmployee checkEmpNo(@Param("empNo") String empNo, @Param("empId") Long empId);

    /**
     * 联表分页查询
     *
     * @param page      分页参数
     * @param queryDto  查询条件
     * @param deptSql    员工id集合
     * @return 分页结果
     */
    // 企业微信兼容，员工搜索
    IPage<CusEmployeeDto> selectCustomPage(@Param("page") Page<CusEmployeeDto> page,
                                           @Param("em") CusEmployeeQueryDto queryDto,
                                           @Param("deptSql") String deptSql,
                                           @Param("unionIds") List<String> unionIds,
                                           @Param("isQueryRootOrg") boolean isQueryRootOrg);

    /**
     * 联表分页查询
     *
     * @param queryDto  查询条件
     * @param orgIds    组织id集合
     * @param companyId 公司id
     * @return 分页结果
     */
    List<CusEmployeeDto> selectCustomPage(@Param("em") CusEmployeeQueryDto queryDto,
                                          @Param("deptSql") String deptSql,
                                          @Param("unionIds") List<String> unionIds,
                                          @Param("isQueryRootOrg") boolean isQueryRootOrg);

    /**
     * 校验注册手机号
     *
     * @param mobile mobile
     * @return
     */
    Map<String, String> checkRegisterMobile(@Param("mobile") String mobile);

    /**
     * 校验注册邮箱
     *
     * @param email
     * @return
     */
    Map<String, String> checkRegisterEmail(@Param("email") String email);

    /**
     * 【运营后台根据ids逻辑删除员工】
     *
     * @param ids ids
     * @return 更新的记录数
     */
    int manageRemoveByIds(@Param("ids") List<Long> ids);

    /**
     * 删除员工
     *
     * @param ids
     * @return
     */
    @Delete("<script> delete from as_cus_employee where id in " +
            "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach></script>")
    void removeCusEmployee(@Param("ids") List<Long> ids);

    /**
     * 组织员工关联查询
     *
     * @param orgIds 组织Id
     * @return 组织员工集合
     */
    // 企业微信兼容，员工搜索
//    List<CusEmployeeDto> orgEmpList(@Param("orgIds") List<Long> orgIds, @Param("kw") String kw, @Param("unionIds") List<String> unionIds);

    CusEmployeeDto currentUserInfo(Long currentUserId);

    CusEmployeeDto getInfo(@Param("userId") Long userId);

//    List<CusUserOrgDto> selectUserOrg(Long id);

    List<CusUserOrgDto> selectUserOrgByUserIds(@Param("ids") List<Long> ids);

    List<CusEmployeeDto> selectAllByIds(@Param("ids") List<Long> ids);

    AsCusEmployee getByIdWithDel(@Param("id") Long id);

    void updateAllCompanyEmpMobile(@Param("mobile") String mobile, @Param("empIds") List<Long> empIds);

    void updateAllCompanyEmpEmail(@Param("email") String email, @Param("empIds") List<Long> empIds);

    AsCusEmployee selectByIsDeleted(@Param("id") Long id);

    List<AsCusEmployee> getByOrgList(@Param("orgIds") List<Long> orgIds);

    IPage<CusEmployeeDto> selectHasAccountEmployee(@Param("page") IPage<CusEmployeeQueryDto> page, @Param("companyId") Long companyId, @Param("em") CusEmployeeQueryDto em);
}
