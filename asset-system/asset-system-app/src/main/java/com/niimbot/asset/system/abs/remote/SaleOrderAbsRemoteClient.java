package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.SaleOrderAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 销售单实现
 *
 * <AUTHOR>
 * @date 2022/5/6 17:20
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.sale.domain.abs.impl.SaleOrderAbsImpl")
@FeignClient(name = "asset-sale", url = "https://{gateway}/client/abs/sale/saleOrderAbs/")
public interface SaleOrderAbsRemoteClient extends SaleOrderAbs {
}
