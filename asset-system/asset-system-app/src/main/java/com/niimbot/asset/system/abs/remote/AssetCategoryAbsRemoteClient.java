package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.AssetCategoryAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/10 18:15
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.AssetCategoryAbsImpl")
@FeignClient(name = "asset-means", url = "https://{gateway}/client/abs/means/assetCategoryAbs/")
public interface AssetCategoryAbsRemoteClient extends AssetCategoryAbs {
}
