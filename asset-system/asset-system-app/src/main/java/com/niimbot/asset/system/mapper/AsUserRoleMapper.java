package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;

import org.apache.ibatis.annotations.*;

/**
 * <p>
 * 用户-角色关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-26
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsUserRoleMapper.class)
public interface AsUserRoleMapper extends BaseMapper<AsUserRole> {

    @Select("SELECT id, role_name FROM as_cus_role WHERE id = ( SELECT role_id FROM as_user_role WHERE user_id = #{userId} LIMIT 1 ) AND is_delete = 0")
    AsCusRole selectUserFirstRoleName(@Param("userId") Long userId);

}
