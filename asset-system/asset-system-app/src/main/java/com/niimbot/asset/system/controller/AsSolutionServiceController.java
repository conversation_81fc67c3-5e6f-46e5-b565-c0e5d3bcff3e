package com.niimbot.asset.system.controller;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.service.SolutionConfigService;
import com.niimbot.system.SolutionConfigDto;
import com.niimbot.system.SolutionDetailDto;
import com.niimbot.system.SolutionQueryDto;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午4:23
 */
@RestController
@RequestMapping("server/system/solution/")
@RequiredArgsConstructor
public class AsSolutionServiceController {

    private final SolutionConfigService solutionService;

    @ApiOperation(value = "查询解决方案配置")
    @GetMapping(value = "query")
    public PageUtils<SolutionConfigDto> pageQuery(SolutionQueryDto request) {
        return solutionService.pageQuery(request);
    }

    @ApiOperation(value = "查询解决方案配置")
    @GetMapping(value = "detail/{configId}")
    public SolutionDetailDto detail(@PathVariable("configId") Long configId) {
        return solutionService.queryDetail(configId);
    }
}
