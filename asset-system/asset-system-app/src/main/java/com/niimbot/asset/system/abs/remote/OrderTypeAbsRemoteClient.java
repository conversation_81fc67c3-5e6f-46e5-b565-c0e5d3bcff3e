package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.OrderTypeAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 10:22
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.OrderTypeAbsImpl")
@FeignClient(name = "asset-means", url = "https://{gateway}/client/abs/means/orderTypeAbs/")
public interface OrderTypeAbsRemoteClient extends OrderTypeAbs {
}
