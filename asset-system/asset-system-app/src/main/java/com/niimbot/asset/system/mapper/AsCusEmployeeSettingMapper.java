package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCusEmployeeSetting;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 用户自定义设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsCusEmployeeSettingMapper.class)
public interface AsCusEmployeeSettingMapper extends BaseMapper<AsCusEmployeeSetting> {

}
