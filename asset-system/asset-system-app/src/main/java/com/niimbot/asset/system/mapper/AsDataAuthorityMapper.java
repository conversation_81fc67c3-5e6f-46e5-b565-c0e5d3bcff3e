package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.niimbot.asset.system.model.AsDataAuthority;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据权限 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
public interface AsDataAuthorityMapper extends BaseMapper<AsDataAuthority> {

    /**
     * 根据用户查询权限列表
     *
     * @param userId 用户di
     * @return 数据权限列表
     */
    List<AsDataAuthority> getListByUserId(@Param("userId") Long userId);

    List<AsDataAuthority> listCompanyPerms(@Param("companyId") Long companyId,
                                           @Param(Constants.WRAPPER) Wrapper<AsDataAuthority> queryWrapper);
}
