package com.niimbot.asset.system.controller;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.service.AsAuditLogService;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.AuditLogSearch;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/auditLog")
@RequiredArgsConstructor
public class AsAuditLogServiceController {

    private final AsAuditLogService auditLogService;

    @PostMapping("/search")
    public PageUtils<AuditLogDto> search(@RequestBody AuditLogSearch search) {
        return auditLogService.search(search);
    }

    @PostMapping("/record")
    public boolean record(@RequestBody AuditLogRecord record) {
        auditLogService.record(record);
        return true;
    }

}
