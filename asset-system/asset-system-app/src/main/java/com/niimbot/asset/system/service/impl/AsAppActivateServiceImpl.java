package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsAppActivateMapper;
import com.niimbot.asset.system.model.AsAppActivate;
import com.niimbot.asset.system.service.AsAppActivateService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Since 2024-09-05
 */
@Service
public class AsAppActivateServiceImpl extends ServiceImpl<AsAppActivateMapper, AsAppActivate> implements AsAppActivateService {

    @Override
    public List<String> configStatus() {
        return list(Wrappers.lambdaQuery(AsAppActivate.class)
                .select(AsAppActivate::getType))
                .stream().map(AsAppActivate::getType).collect(Collectors.toList());
    }

}
