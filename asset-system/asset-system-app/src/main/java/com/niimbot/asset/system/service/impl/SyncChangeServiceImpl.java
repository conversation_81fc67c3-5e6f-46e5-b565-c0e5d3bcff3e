package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.ThirdPartyResultCode;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.AssetAbs;
import com.niimbot.asset.system.abs.AssetLogAbs;
import com.niimbot.asset.system.dto.AssetLogSaveBatchCmd;
import com.niimbot.asset.system.dto.AssetUpdateBatchCmd;
import com.niimbot.asset.system.dto.clientobject.AssetCO;
import com.niimbot.asset.system.dto.clientobject.AssetLogCO;
import com.niimbot.asset.system.mapper.AsSyncChangeMapper;
import com.niimbot.asset.system.mapper.CompanyAssetMapper;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetDto;
import com.niimbot.system.CusEmployeeTransferDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.RemoveEmployDto;
import com.niimbot.thirdparty.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Service
public class SyncChangeServiceImpl extends ServiceImpl<AsSyncChangeMapper, AsSyncChange> implements SyncChangeService {

    @Resource
    private OrgService orgService;

    @Resource
    private AsEmployeeChangeService employeeChangeService;

    @Resource
    private AsEmployeeAssetChangeService employeeAssetChangeService;

    @Resource
    private AsCusEmployeeTransferService transferService;

    @Resource
    private AssetAbs assetAbs;

    @Resource
    private CompanyAssetMapper companyAssetMapper;

    @Resource
    private AssetLogAbs assetLogAbs;

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Override
    public List<SyncChangeDto> listChange(Integer type, Integer status) {
        return this.getBaseMapper().list(type, status);
    }

    @Override
    public SyncChangeEmpDto getEmp(Long id) {
        AsSyncChange change = this.getById(id);
        if (Objects.isNull(change)) {
            throw new BusinessException(ThirdPartyResultCode.SYNC_CHANGE_NOT_FOUND);
        }
        List<Long> fromOrg = change.getFromOrg();
        List<Long> toOrg = change.getToOrg();
        List<OrgDto> toOrgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(toOrg)) {
            if (change.getType() == 1) {
                fromOrg.removeIf(toOrg::contains);
            }
            List<AsOrg> list = orgService.listAllByIds(toOrg);
            toOrgList = list.stream().map(it -> BeanUtil.copyProperties(it, OrgDto.class)).collect(Collectors.toList());
        }
        List<OrgDto> fromOrgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(fromOrg)) {
            List<AsOrg> list = orgService.listAllByIds(fromOrg);
            fromOrgList = list.stream().map(it -> BeanUtil.copyProperties(it, OrgDto.class)).collect(Collectors.toList());
        }

        AsCusEmployee emp = this.getBaseMapper().getEmp(change.getResId(), LoginUserThreadLocal.getCompanyId());
        SyncChangeEmpDto changeEmpDto = new SyncChangeEmpDto();
        return changeEmpDto.setEmpId(emp.getId())
                .setEmpName(emp.getEmpName()).setEmpNo(emp.getEmpNo())
                .setFromOrg(fromOrgList).setToOrg(toOrgList)
                .setIsHandlerDone(change.getStatus() == 2);
    }

    @Override
    public AsOrg getOrg(Long id) {
        return this.getBaseMapper().getOrg(id, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferEmpEdit(EmpTransferDto empTransferDto) {
        AsSyncChange syncChange = this.getOne(new LambdaQueryWrapper<AsSyncChange>()
                .eq(AsSyncChange::getId, empTransferDto.getId())
                .eq(AsSyncChange::getStatus, 1));
        if (ObjectUtil.isNull(syncChange)) {
            throw new BusinessException(ThirdPartyResultCode.SYNC_CHANGE_NOT_FOUND);
        }
        List<TransferDto> transfers = empTransferDto.getTransfer();
        // 写入异动信息
        AsEmployeeChange employeeChange = new AsEmployeeChange();
        employeeChange.setId(IdUtils.getId());
        employeeChange.setType(DictConstant.CHANGE_TYPE_EDIT).setEmpId(syncChange.getResId());

        List<AsOrg> allOrglist = orgService.list();
        Map<Long, String> orgMap = allOrglist.stream().collect(Collectors.toMap(AsOrg::getId, AsOrg::getOrgName, (k1, k2) -> k1));

        // 组织变更
        List<Long> fromOrg = syncChange.getFromOrg();
        List<Long> toOrg = syncChange.getToOrg();
        StringBuilder orgExplain = new StringBuilder();
        orgExplain.append("部门由“").append(fromOrg.stream().map(v -> orgMap.getOrDefault(v, cacheResourceUtil.getOrgName(v) + "（已删除）")).collect(Collectors.joining("，")));
        orgExplain.append("”变成“").append(toOrg.stream().map(v -> orgMap.getOrDefault(v, cacheResourceUtil.getOrgName(v) + "（已删除）")).collect(Collectors.joining("，")));
        orgExplain.append("”");
        employeeChange.setOrgExplain(orgExplain.toString());

        // 资产变更
        List<String> assetExplain = new ArrayList<>();
        for (TransferDto transfer : transfers) {
            StringBuilder buffer = new StringBuilder();
            String formName = orgMap.get(transfer.getFrom());
            String toName = orgMap.get(transfer.getTo());
            if (StrUtil.isBlank(formName) || StrUtil.isBlank(toName)) {
                transfer.setAllowTrans(false);
                buffer.append(orgMap.getOrDefault(transfer.getFrom(), cacheResourceUtil.getOrgName(transfer.getFrom()) + "（已删除）"))
                        .append("资产转移到")
                        .append(orgMap.getOrDefault(transfer.getTo(), cacheResourceUtil.getOrgName(transfer.getTo()) + "（已删除）"));
            } else {
                buffer.append(formName).append("资产转移到").append(toName);
                transfer.setAllowTrans(true);
            }
            assetExplain.add(buffer.toString());
        }
        if (CollUtil.isNotEmpty(assetExplain)) {
            employeeChange.setAssetExplain(String.join(",", assetExplain));
        } else {
            employeeChange.setAssetExplain("无");
        }
        // 转移记录
        employeeChangeService.save(employeeChange);
        this.update(new LambdaUpdateWrapper<AsSyncChange>().set(AsSyncChange::getStatus, 2)
                .eq(AsSyncChange::getId, syncChange.getId()));
        // 转移资产
        List<CusEmployeeTransferDto> collect = transfers.stream()
                .filter(TransferDto::getAllowTrans)
                .map(it -> BeanUtil.copyProperties(it, CusEmployeeTransferDto.class))
                .collect(Collectors.toList());
        // 转移资产
        if (CollUtil.isNotEmpty(collect)) {
            List<AsOrg> orglist = orgService.list();
            assetAbs.editAssetUseOrg(employeeChange.getId(), syncChange.getResId(), collect, orglist);
        }
        return true;
    }

    @Override
    public Boolean transferEmpDelete(Long id, RemoveEmployDto employ) {
        AsSyncChange syncChange = this.getOne(new LambdaQueryWrapper<AsSyncChange>()
                .eq(AsSyncChange::getId, id)
                .eq(AsSyncChange::getStatus, 1));
        if (ObjectUtil.isNull(syncChange)) {
            throw new BusinessException(ThirdPartyResultCode.SYNC_CHANGE_NOT_FOUND);
        }
        transferService.transferEmp(BeanUtil.copyProperties(employ, RemoveEmployDto.class));
        this.update(new LambdaUpdateWrapper<AsSyncChange>().set(AsSyncChange::getStatus, 2)
                .eq(AsSyncChange::getId, id));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferOrgDelete(OrgTransferDto orgTransferDto) {
        AsSyncChange syncChange = this.getOne(new LambdaQueryWrapper<AsSyncChange>()
                .eq(AsSyncChange::getId, orgTransferDto.getId())
                .eq(AsSyncChange::getStatus, 1));
        if (ObjectUtil.isNull(syncChange)) {
            throw new BusinessException(ThirdPartyResultCode.SYNC_CHANGE_NOT_FOUND);
        }

        Long changeId = IdUtils.getId();
        Long orgId = orgTransferDto.getOrgId();

        List<String> assetExplainList = new ArrayList<>();

        List<AssetLogCO> assetLogs = new ArrayList<>();

        // 转移使用组织
        if (orgTransferDto.getUseOrg() != null) {
            Long to = orgTransferDto.getUseOrg();
            List<AssetLogCO> assetLogList = assetUseOrgTransfer(changeId, orgId, to);
            assetLogs.addAll(assetLogList);
            String orgName = cacheResourceUtil.getOrgName(to);
            if (StrUtil.isNotEmpty(orgName)) {
                assetExplainList.add("使用资产转移到" + orgName);
            }
        }

        // 转移所属管理组织
        if (orgTransferDto.getOrgOwner() != null) {
            Long to = orgTransferDto.getOrgOwner();
            List<AssetLogCO> assetLogList = assetOrgOwnerTransfer(changeId, orgId, to);
            assetLogs.addAll(assetLogList);
            String orgName = cacheResourceUtil.getOrgName(to);
            if (StrUtil.isNotEmpty(orgName)) {
                assetExplainList.add("管理资产转移到" + orgName);
            }
        }

        Map<Long, AssetLogCO> assetLogMap = new HashMap<>();
        for (AssetLogCO assetLog : assetLogs) {
            Long assetId = assetLog.getAssetId();
            AssetLogCO asAssetLog = assetLogMap.get(assetId);
            if (asAssetLog == null) {
                assetLog.setActionType(AssetConstant.OPT_ORG_CHANGE)
                        .setHandleTime(LocalDateTime.now())
                        .setActionName("员工异动");
                assetLogMap.put(assetId, assetLog);
            } else {
                asAssetLog.setActionContent(asAssetLog.getActionContent() + "；" + assetLog.getActionContent());
            }
        }
        Collection<AssetLogCO> logs = assetLogMap.values();
        assetLogAbs.saveBatchAssetLog(new AssetLogSaveBatchCmd().setLogs(logs));

        // 写入异动记录
        AsEmployeeChange employeeChange = new AsEmployeeChange();
        employeeChange.setId(changeId)
                .setType(3)
                .setEmpId(orgId)
                .setOrgExplain("删除组织");
        if (CollUtil.isNotEmpty(assetExplainList)) {
            employeeChange.setAssetExplain(String.join("；", assetExplainList));
        } else {
            employeeChange.setAssetExplain("无");
        }
        employeeChangeService.save(employeeChange);
        // 写入资产变更记录

        this.update(new LambdaUpdateWrapper<AsSyncChange>().set(AsSyncChange::getStatus, 2)
                .eq(AsSyncChange::getId, syncChange.getId()));
        return true;
    }

    private List<AssetLogCO> assetUseOrgTransfer(Long changeId, Long from, Long to) {
        List<AssetDto> useOrg = companyAssetMapper.selectUseOrgAsset(from, LoginUserThreadLocal.getCompanyId());
        List<AssetCO> useOrgAsset = new ArrayList<>();
        List<AsEmployeeAssetChange> assetChanges = new ArrayList<>();
        List<AssetLogCO> assetLogList = new ArrayList<>();
        for (AssetDto assetDto : useOrg) {
            // 更新资产
            AssetCO asAsset = new AssetCO().setId(assetDto.getId()).setAssetData(assetDto.getAssetData());
            JSONObject data = asAsset.getAssetData();

            // 记录原始所属组织
            String fromOrgName = cacheResourceUtil.getOrgName(from);
            String toOrgName = cacheResourceUtil.getOrgName(to);
            String content = "使用组织由" + fromOrgName + "变成" + toOrgName;
            AssetLogCO assetLog = new AssetLogCO().setAssetId(assetDto.getId()).setActionContent(content);
            assetLogList.add(assetLog);

            // 更新使用组织
            data.put("useOrg", to != null ? Convert.toStr(to) : StrUtil.EMPTY);
            useOrgAsset.add(asAsset);

            // 写入记录
            AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
            assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(changeId).setChangeOrgType(1);
            assetChanges.add(assetChange);
        }

        // 更新
        if (!useOrgAsset.isEmpty()) {
            this.assetAbs.updateBatchAsset(new AssetUpdateBatchCmd().setAssetCOS(useOrgAsset));
            this.employeeAssetChangeService.saveBatch(assetChanges);
        }
        return assetLogList;
    }

    private List<AssetLogCO> assetOrgOwnerTransfer(Long changeId, Long from, Long to) {
        List<AssetDto> orgOwner = companyAssetMapper.selectOrgOwnerAsset(from, LoginUserThreadLocal.getCompanyId());
        List<AssetCO> orgOwnerAsset = new ArrayList<>();
        List<AsEmployeeAssetChange> assetChanges = new ArrayList<>();
        List<AssetLogCO> assetLogList = new ArrayList<>();
        for (AssetDto assetDto : orgOwner) {
            // 更新资产
            AssetCO asAsset = new AssetCO().setId(assetDto.getId()).setAssetData(assetDto.getAssetData());
            JSONObject data = asAsset.getAssetData();

            // 记录原始所属组织
            String fromOrgName = cacheResourceUtil.getOrgName(from);
            String toOrgName = cacheResourceUtil.getOrgName(to);
            String content = "所属管理组织由" + fromOrgName + "变成" + toOrgName;
            AssetLogCO assetLog = new AssetLogCO().setAssetId(assetDto.getId()).setActionContent(content);
            assetLogList.add(assetLog);

            // 更新使用组织
            data.put("orgOwner", to != null ? Convert.toStr(to) : StrUtil.EMPTY);
            orgOwnerAsset.add(asAsset);

            // 写入记录
            AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
            assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(changeId).setChangeOrgType(2);
            assetChanges.add(assetChange);
        }

        // 更新
        if (!orgOwnerAsset.isEmpty()) {
            this.assetAbs.updateBatchAsset(new AssetUpdateBatchCmd().setAssetCOS(orgOwnerAsset));
            this.employeeAssetChangeService.saveBatch(assetChanges);
        }
        return assetLogList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void records(List<AsSyncChange> changes) {
        if (CollUtil.isEmpty(changes)) {
            return;
        }
        changes.forEach(next -> {
            // 1.删除员工
            if (next.getType() == 2) {
                next.setId(IdUtils.getId());
                this.remove(
                        Wrappers.lambdaQuery(AsSyncChange.class)
                                .eq(AsSyncChange::getResId, next.getResId())
                                .eq(AsSyncChange::getType, 1)
                                .eq(AsSyncChange::getStatus, 1)
                );
                return;
            }
            // 2.编辑员工 更新记录
            if (next.getType() == 1) {
                AsSyncChange one = lastUntreatedRecord(next.getCompanyId(), next.getResId(), 1);
                if (Objects.nonNull(one)) {
                    // 合并历史部门
                    Set<Long> form = new HashSet<>(6);
                    if (CollUtil.isNotEmpty(next.getFromOrg())) {
                        form.addAll(next.getFromOrg());
                    }
                    if (CollUtil.isNotEmpty(one.getFromOrg())) {
                        form.addAll(one.getFromOrg());
                    }
                    next.setId(one.getId()).setFromOrg(new ArrayList<>(form));
                } else {
                    next.setId(IdUtils.getId());
                }
                return;
            }
            next.setId(IdUtils.getId());
        });
        this.saveOrUpdateBatch(changes);
    }

    @Override
    public AsSyncChange lastUntreatedRecord(Long companyId, Long resId, Integer type) {
        return this.getOne(
                Wrappers.lambdaQuery(AsSyncChange.class)
                        .eq(AsSyncChange::getResId, resId)
                        .eq(AsSyncChange::getType, type)
                        .eq(AsSyncChange::getStatus, 1)
                        .orderByDesc(AsSyncChange::getId)
                        .last("LIMIT 1")
        );
    }

    @Override
    public void removeUntreatedRecord(Long companyId, Long resId, Integer type) {
        this.remove(
                Wrappers.lambdaQuery(AsSyncChange.class)
                        .eq(AsSyncChange::getCompanyId, companyId)
                        .eq(AsSyncChange::getResId, resId)
                        .eq(AsSyncChange::getType, type)
                        .eq(AsSyncChange::getStatus, 1)
        );
    }
}
