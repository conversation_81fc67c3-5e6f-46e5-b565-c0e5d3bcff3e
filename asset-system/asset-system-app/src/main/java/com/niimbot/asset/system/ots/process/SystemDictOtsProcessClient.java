package com.niimbot.asset.system.ots.process;

import com.niimbot.asset.system.dto.clientobject.DictDataCO;
import com.niimbot.asset.system.mapstruct.SystemMapStruct;
import com.niimbot.asset.system.model.AsDictData;
import com.niimbot.asset.system.ots.SystemDictOts;
import com.niimbot.asset.system.service.DictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemDictOts/")
@RequiredArgsConstructor
public class SystemDictOtsProcessClient implements SystemDictOts {

    private final SystemMapStruct systemMapStruct;

    private final DictDataService dictDataService;

    @Override
    public List<DictDataCO> listByType(String type) {
        List<AsDictData> dictData = dictDataService.selectDictDataByType(type);
        return systemMapStruct.convertDictDataDoListToCoList(dictData);
    }
}
