package com.niimbot.asset.system.service.impl;

import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.service.UserExtService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.kalimdor.hulk.model.TokenResponse;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * 获取用户unionId接口实现
 *
 * <AUTHOR>
 * @date 2021/4/2 16:52
 */
@Slf4j
@Service
@Profile({Edition.LOCAL})
public class LocalSsoUserExtServiceImpl implements UserExtService {
    @Override
    public TokenResponse getUnionIdBySmsCode(String mobile, String smsCode) {
        throw new BusinessException(SystemResultCode.LOCAL_SERVICE_UNSUPPORTED_OPERATION);
    }

}
