package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.MessageAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.message.abs.MessageAbsImpl")
@FeignClient(name = "asset-message", url = "https://{gateway}/client/abs/message/abs/")
public interface MessageAbsRemoteClient extends MessageAbs {

}
