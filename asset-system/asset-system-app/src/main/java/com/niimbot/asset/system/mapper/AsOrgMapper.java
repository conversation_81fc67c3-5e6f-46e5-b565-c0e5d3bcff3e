package com.niimbot.asset.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.system.OrgDto;
import com.niimbot.system.OrgExportDto;
import com.niimbot.system.OrgQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 组织表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
@EnableDataPerm(excludeMethodName = {"getOrgList", "allOrg"})
public interface AsOrgMapper extends BaseMapper<AsOrg> {

    /**
     * 字典初始化，查询全部组织
     *
     * @return 组织集合
     */
    @Select("select id, company_id, org_name, org_code from as_org")
    List<AsOrg> allOrg();

    /**
     * 查询最大组织编码
     *
     * @return 最大组织编码
     */
    @Select("select max(org_code) from as_org WHERE org_code REGEXP '^[A-Z]{1}[0-9]{2,3}$'")
    String getMaxOrgCode();

    /**
     * 查询最大组织编码
     *
     * @return 最大组织编码
     */
    @Select("select max(org_code) from as_org WHERE company_id = #{companyId} and org_code REGEXP '^[A-Z]{1}[0-9]{2,3}$'")
    String getMaxOrgCodeByCompanyId(@Param("companyId") Long companyId);

    /**
     * 通过code查询最大区域编码
     *
     * @return 最大区域编码
     */
    @Select("select max(org_code) from as_org WHERE org_code REGEXP '^[A-Z]{1}[0-9]{2,3}$' and org_code like concat(#{code}, '%')")
    String getMaxOrgCodeByCode(@Param("code") String code);

    /**
     * 查询组织
     *
     * @return 组织列表
     */
    // 企业微信兼容，部门搜索
    IPage<OrgDto> listOrg(IPage<Object> page,
                          @Param("ew") OrgQueryDto queryDto,
                          @Param("deptSql") String deptSql,
                          @Param("orgUnionIds") List<String> orgUnionIds);

    /**
     * 查询组织
     *
     * @return 组织列表
     */
    // 企业微信兼容，部门搜索
    List<OrgDto> listOrg(@Param("ew") OrgQueryDto queryDto,
                         @Param("deptSql") String deptSql,
                         @Param("orgUnionIds") List<String> orgUnionIds);

    List<OrgExportDto> getExcelData(@Param("ew") OrgQueryDto orgQueryDto,
                                    @Param("deptSql") String deptSql);

    Integer orgRefAsset(@Param("orgId") Long orgId);

    Integer useOrgRefAsset(@Param("orgId") Long orgId);

    List<OrgDto> listByDirector(@Param("userId") Long userId, @Param("companyId") Long companyId);

    List<OrgDto> listByDirectors(@Param("userIds") List<Long> userIds, @Param("companyId") Long companyId);

    List<AsOrg> listAllByIds(@Param("orgIds") List<Long> orgIds, @Param("companyId") Long companyId);

    @Select("SELECT count(*) FROM as_area a WHERE a.is_delete = 0 and a.company_id = #{companyId} AND a.org_id = #{orgId} limit 1")
    Integer orgRefArea(@Param("orgId") Long orgId, @Param("companyId") Long companyId);

    @Select("SELECT count(*) FROM as_repository a WHERE a.is_delete = 0 AND a.company_id = #{companyId} and a.manager_owner = #{orgId} limit 1")
    Integer orgRefRepository(@Param("orgId") Long orgId, @Param("companyId") Long companyId);

    Long getOrgCompanyOwner(@Param("ids") List<Long> ids);

    // 通过权限查询用户可见的部门简单集合（仅包含id，name）
    List<DictDataDto> listSimpleWithPerms(@Param("currentUserId") Long currentUserId,
                                          @Param("authorityType") Integer authorityType,
                                          @Param("companyId") Long companyId);

    /**
     * 查询公司列表
     *
     * @param ids          公司Id
     * @param includeChild 是否包含子集
     * @param type         类型-固定业务【area, store】
     * @return
     */
    List<OrgDto> companyList(@Param("ids") List<Long> ids,
                             @Param("includeChild") boolean includeChild,
                             @Param("type") String type);

    List<Long> hasPermOrgIds(@Param("orgIds") List<Long> orgIds,
                             @Param("orgType") Integer orgType,
                             @Param("deptSql") String deptSql);
}
