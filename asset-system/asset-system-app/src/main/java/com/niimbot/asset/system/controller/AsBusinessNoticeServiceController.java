package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.BusinessNoticeService;
import com.niimbot.system.BusinessNoticeDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/3/17 下午4:42
 */
@RestController
@RequestMapping("server/system/businessNotice")
public class AsBusinessNoticeServiceController {

    @Autowired
    private BusinessNoticeService noticeService;

    @ApiOperation(value = "查询业务公告")
    @GetMapping(value = "/detail/{companyId}")
    public BusinessNoticeDto queryDetail(@PathVariable("companyId") Long companyId) {
        return noticeService.queryDetail(companyId);
    }

    @ApiOperation(value = "删除业务公告")
    @DeleteMapping(value = "/{noticeId}")
    public Boolean removeNotice(@PathVariable("noticeId") Long noticeId) {
        return noticeService.removeNotice(noticeId);
    }

    @ApiOperation(value = "保存业务公告")
    @PostMapping(value = "/save")
    public Boolean saveOrUpdate(@RequestBody BusinessNoticeDto noticeDto) {
        return noticeService.saveOrUpdate(noticeDto);
    }
}
