package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.CusUserDetailDto;
import com.niimbot.system.SonUserDto;
import com.niimbot.system.UserCenterAPPDto;
import com.niimbot.system.UserCenterPCDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 客户账号 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
// @EnableDataPerm(excludeMethodName = {"getUserCenterAppInfo","getMaxAccount", "queryUserByAccount", "queryUserByUnionId", "getCusUserByCondition", "getUserCenterInfo",
//         "getSonAccountNumByCompanyId", "getSonListByCompanyId", "getUserIds", "removeCusUser", "removeCusUserById", "removeCusUserExt",
//         "removeCusUserSetting", "removeCusUserCompany", "removeCusUserFeedback", "removeCusUserRegconfig", "removeCusUserRole", "removeCusUserTag",
//         "removeCusUserTerminalRecord", "getRoleIds", "removeCusRole", "removeCusRoleMenu","updateUnionIdByAccountNo","changeUserPassword","userInfo", "searchAllByMenuCode", "personDetail"})
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsCusUserMapper.class)
public interface AsCusUserMapper extends BaseMapper<AsCusUser> {

    /**
     * 获取用户最大账号信息
     *
     * @return 最大账号只
     */
    @Select("select max(account) from as_cus_user where account like 'GZ%'")
    String getMaxAccount();

    /**
     * 根据注册中心uid查询用户
     *
     * @param unionId 注册中心uid
     * @return 用户信息
     */
    CusUserDto queryUserByUnionId(@Param("unionId") String unionId);


    /**
     * 根据条件查询用户
     *
     * @param condition 条件
     * @return AsCusUser用户
     */
    AsCusUser getCusUserByCondition(AsCusUser condition);

    /**
     * 查询角色Id下的用户数量 【需要权限】
     *
     * @param roleId 角色Id
     * @return 账户数量
     */
    int countUserByRoleId(@Param("roleId") Long roleId);

    /**
     * APP获取用户中心数据
     *
     * @param id 用户id
     * @return 用户中心数据
     */
    UserCenterAPPDto getUserCenterAppInfo(@Param("id") Long id);

    /**
     * 通过企业id查询子账号数量
     *
     * @param companyId
     * @return
     */
    int getSonAccountNumByCompanyId(@Param("companyId") Long companyId);

    /**
     * 通过企业id查询子账号列表
     *
     * @param companyId
     * @return
     */
    List<SonUserDto> getSonListByCompanyId(@Param("companyId") Long companyId);

    /**
     * 用户详情信息
     *
     * @param id 用户Id
     * @return 用户登录详情
     */
    CusUserDetailDto personDetail(@Param("userId") Long id);

    /**
     * pc用户中心
     *
     * @param id id
     * @return 结果
     */
    UserCenterPCDto getUserCenterPcInfo(@Param("id") Long id);

    /**
     * [注册]根据accountNo更新unionId
     *
     * @param asCusUser asCusUser
     * @return 成功与否
     */
    boolean updateUnionIdByAccountNo(AsCusUser asCusUser);

    /**
     * 【忘记密码】 更新用户密码
     *
     * @param userId 用户id
     * @param password 用户密码
     * @return true/false
     */
    @Update("UPDATE as_cus_user SET password = #{password} WHERE id = #{userId} AND is_delete = 0")
    boolean changeUserPassword(@Param("userId") Long userId, @Param("password") String password);

    /**
     * 二维码邀请员工注册-用户详情信息
     *
     * @return 用户id
     */
    CusUserDetailDto userInfo(@Param("userId") Long id);

    /**
     * 获取企业超管信息
     *
     * @param id id
     * @return 结果
     */
    UserCenterPCDto getAdminUserInfo(@Param("id") Long id);

}
