package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.system.model.AsOpsNotice;
import com.niimbot.asset.system.service.AsOpsNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/system/opsNotice")
@RequiredArgsConstructor
public class AsOpsNoticeServiceController {

    private final AsOpsNoticeService opsNoticeService;

    @GetMapping("/available")
    public AsOpsNotice available() {
        List<AsOpsNotice> notices = opsNoticeService.getBaseMapper().selectList(
                Wrappers.<AsOpsNotice>lambdaQuery().eq(AsOpsNotice::getIsEnabled, true).orderByDesc(AsOpsNotice::getCreateTime)
        );
        return notices == null || notices.isEmpty() ? null : notices.get(0);
    }

    @GetMapping("/list")
    public List<AsOpsNotice> list() {
        return opsNoticeService.list();
    }
}
