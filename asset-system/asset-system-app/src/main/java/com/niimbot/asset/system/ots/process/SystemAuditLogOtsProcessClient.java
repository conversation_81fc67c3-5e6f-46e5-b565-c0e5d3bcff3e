package com.niimbot.asset.system.ots.process;

import com.niimbot.asset.system.ots.SystemAuditLogOts;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.asset.system.service.AsAuditLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemAuditLogOts/")
@RequiredArgsConstructor
public class SystemAuditLogOtsProcessClient implements SystemAuditLogOts {

    private final AsAuditLogService auditLogService;

    @Override
    public void record(AuditLogRecord record) {
        auditLogService.record(record);
    }
}
