package com.niimbot.asset.system.ots.process;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.dto.EmployeeResignRecordGetQry;
import com.niimbot.asset.system.dto.WorkflowManagerGetQry;
import com.niimbot.asset.system.dto.clientobject.EmployeeCO;
import com.niimbot.asset.system.dto.clientobject.EmployeeResignRecordCO;
import com.niimbot.asset.system.dto.clientobject.ExternalRelation;
import com.niimbot.asset.system.mapstruct.SystemMapStruct;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsEmployeeResignRecord;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.ots.SystemEmployeeOts;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsEmployeeResignRecordService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemEmployeeOts/")
@RequiredArgsConstructor
public class SystemEmployeeOtsProcessClient implements SystemEmployeeOts {

    private final SystemMapStruct systemMapStruct;

    private AsCusEmployeeService employeeService;

    private AsAccountEmployeeService accountEmployeeService;

    private AsEmployeeResignRecordService employeeResignRecordService;

    private AsCusEmployeeExtService employeeExtService;

    private AsThirdPartyEmployeeService thirdPartyEmployeeService;

    //TODO: 2022/6/8 其他模块暂未对 interface 模块解耦，会产生bean循环依赖问题
    @Lazy
    @Autowired
    public void setEmployeeService(AsCusEmployeeService employeeService) {
        this.employeeService = employeeService;
    }

    @Lazy
    @Autowired
    public void setAccountEmployeeService(AsAccountEmployeeService accountEmployeeService) {
        this.accountEmployeeService = accountEmployeeService;
    }

    @Lazy
    @Autowired
    public void setEmployeeResignRecordService(AsEmployeeResignRecordService employeeResignRecordService) {
        this.employeeResignRecordService = employeeResignRecordService;
    }

    @Lazy
    @Autowired
    public void setEmployeeExtService(AsCusEmployeeExtService employeeExtService) {
        this.employeeExtService = employeeExtService;
    }

    @Lazy
    @Autowired
    public void setThirdPartyEmployeeService(AsThirdPartyEmployeeService thirdPartyEmployeeService) {
        this.thirdPartyEmployeeService = thirdPartyEmployeeService;
    }

    @Override
    public EmployeeCO getById(Long id) {
        AsCusEmployee employee = employeeService.getById(id);
        return systemMapStruct.convertEmployeeDoToCo(employee);
    }

    @Override
    public EmployeeCO getByIdWithDel(Long id) {
        AsCusEmployee employee = employeeService.getByIdWithDel(id);
        return systemMapStruct.convertEmployeeDoToCo(employee);
    }

    @Override
    public List<EmployeeCO> listByIds(List<Long> ids) {
        List<AsCusEmployee> employees = employeeService.listByIds(ids);
        return systemMapStruct.convertEmployeeDoListToCoList(employees);
    }

    @Override
    public List<List<EmployeeCO>> getWorkFlowMultiManagers(WorkflowManagerGetQry qry) {
        List<List<AsCusEmployee>> multiManager = employeeService.getMultiManager(qry.getOrgId(), qry.getLevel(), qry.getHandleType());
        if (CollUtil.isEmpty(multiManager)) {
            return Collections.emptyList();
        }
        return multiManager.stream().map(systemMapStruct::convertEmployeeDoListToCoList).collect(Collectors.toList());
    }

    @Override
    public List<EmployeeCO> getWorkflowManagers(WorkflowManagerGetQry qry) {
        List<AsCusEmployee> workflowManager = employeeService.getWorkflowManager(qry.getOrgId(), qry.getLevel(), qry.getHandleType(), qry.getIsAgent());
        return systemMapStruct.convertEmployeeDoListToCoList(workflowManager);
    }

    @Override
    public EmployeeCO getCompanyAdministrator() {
        AsCusEmployee administrator = employeeService.getAdministrator();
        return systemMapStruct.convertEmployeeDoToCo(administrator);
    }

    @Override
    public EmployeeCO getAdmin(Long companyId) {
        AsCusEmployee admin = employeeService.getAdministratorByCompanyId(companyId);
        if (Objects.isNull(admin)) {
            return null;
        }
        return systemMapStruct.convertEmployeeDoToCo(admin);
    }

    @Override
    public Boolean hasAccount(Long empId) {
        Optional<AsAccountEmployee> employeeAccount = accountEmployeeService.getEmployeeAccount(empId);
        // 钉钉环境
        if (Edition.isDing() || Edition.isWeixin()) {
            if (!employeeAccount.isPresent()) {
                return false;
            }
            AsCusEmployeeExt ext = employeeExtService.getById(employeeAccount.get().getEmployeeId());
            if (Objects.isNull(ext)) {
                return false;
            }
            // 账号状态未激活时当无账号逻辑处理
            return ext.getAccountStatus() == 3;
        }
        return employeeAccount.isPresent();
    }

    @Override
    public Set<Long> filterWithAccount(Long companyId, Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptySet();
        }
        List<AsAccountEmployee> accounts = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .select(AsAccountEmployee::getAccountId, AsAccountEmployee::getEmployeeId)
                        .eq(AsAccountEmployee::getCompanyId, companyId)
                        .in(AsAccountEmployee::getEmployeeId, ids)
        );
        if (CollUtil.isEmpty(accounts)) {
            return Collections.emptySet();
        }
        Set<Long> empIds = accounts.stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toSet());
        List<AsCusEmployeeExt> ext = employeeExtService.list(
                Wrappers.lambdaQuery(AsCusEmployeeExt.class)
                        .select(AsCusEmployeeExt::getId)
                        .in(AsCusEmployeeExt::getId, empIds)
                        .eq(AsCusEmployeeExt::getAccountStatus, 3)
        );
        if (CollUtil.isEmpty(ext)) {
            return Collections.emptySet();
        }
        return ext.stream().map(AsCusEmployeeExt::getId).collect(Collectors.toSet());
    }

    @Override
    public EmployeeResignRecordCO getResignRecordByType(EmployeeResignRecordGetQry qry) {
        EmployeeResignRecordCO co = qry.getQry();
        AsEmployeeResignRecord resignRecords = employeeResignRecordService.getOne(
                new LambdaQueryWrapper<AsEmployeeResignRecord>()
                        .eq(AsEmployeeResignRecord::getResType, co.getResType())
                        .eq(AsEmployeeResignRecord::getEmpId, co.getEmpId())
        );
        return systemMapStruct.convertEmployeeResignRecordDoToCo(resignRecords);
    }

    @Override
    public List<ExternalRelation> getEmpExternalRelation(Long companyId) {
        AsThirdPartyEmployeeService service = SpringUtil.getBean(AsThirdPartyEmployeeService.class);
        return service.list(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getCompanyId, companyId)
        ).stream().map(v -> new ExternalRelation(v.getEmployeeId(), v.getUserId())).collect(Collectors.toList());
    }

    @Override
    public List<ExternalRelation> getEmpExternalRelation(Long companyId, List<String> extIds) {
        AsThirdPartyEmployeeService service = SpringUtil.getBean(AsThirdPartyEmployeeService.class);
        return service.list(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getCompanyId, companyId)
                        .in(AsThirdPartyEmployee::getUserId, extIds)
        ).stream().map(v -> new ExternalRelation(v.getEmployeeId(), v.getUserId())).collect(Collectors.toList());
    }

    @Override
    public List<String> getEmpExternalIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return thirdPartyEmployeeService.list(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .in(AsThirdPartyEmployee::getEmployeeId, ids)
        ).stream().map(AsThirdPartyEmployee::getUserId).distinct().collect(Collectors.toList());
    }

    @Override
    public String getEmpExternalId(Long id) {
        if (Objects.isNull(id)) {
            return "";
        }
        AsThirdPartyEmployee partyEmployee = thirdPartyEmployeeService.getOne(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getEmployeeId, id)
        );
        if (Objects.isNull(partyEmployee) || StrUtil.isBlank(partyEmployee.getUserId())) {
            return "";
        }
        return partyEmployee.getUserId();
    }
}
