package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.TreeUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.BaseCacheStrategy;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheOrgStrategy;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.AssetFinanceInfoAbs;
import com.niimbot.asset.system.abs.ImportErrorAbs;
import com.niimbot.asset.system.dto.ImportErrorListQry;
import com.niimbot.asset.system.dto.ImportErrorSaveCmd;
import com.niimbot.asset.system.dto.ImportErrorSaveOrUpdateCmd;
import com.niimbot.asset.system.dto.clientobject.ImportErrorCO;
import com.niimbot.asset.system.mapper.AsOrgMapper;
import com.niimbot.asset.system.mapper.AsUserOrgMapper;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.service.handler.WeixinAdapter;
import com.niimbot.finance.FinanceStatisticsQueryDto;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.model.ModelDataRule;
import com.niimbot.framework.dataperm.core.model.ModelDataScope;
import com.niimbot.framework.dataperm.core.model.ModelDataScopeService;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrgServiceImpl extends ServiceImpl<AsOrgMapper, AsOrg> implements OrgService, ApplicationListener<ApplicationReadyEvent> {

    // 组织最大层级数
    private static final int ORG_MAX_LEVEL = 10;

    @Resource
    private RedisService redisService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private ImportErrorAbs importErrorAbs;

    @Resource
    private AsUserOrgMapper userOrgMapper;

    @Autowired
    private AssetFinanceInfoAbs assetFinanceInfoAbs;

    @Autowired
    private DataScopeStrategyManager dataScopeStrategyManager;

    @Autowired
    private ModelDataScopeService modelDataScopeService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return ;
        }

        taskExecutor.execute(() -> {
            List<AsOrg> asOrgs = this.getBaseMapper().allOrg();
            loadOrgCache(asOrgs);
        });
    }

    @Override
    public void loadOrgCache(List<AsOrg> orgList) {
        Map<String, String> collect = new ConcurrentHashMap<>();
        orgList.parallelStream().forEach(it -> {
            JSONObject jsonObject = new JSONObject()
                    .fluentPut(BaseCacheStrategy.KEY_NAME, it.getOrgName())
                    .fluentPut(BaseCacheStrategy.KEY_CODE, it.getOrgCode());
            collect.put(Convert.toStr(it.getId()), jsonObject.toJSONString());
        });
        redisService.hSetAll(RedisConstant.orgDictKey(), collect);
        log.info("init org cache finish");
    }

    /**
     * 获取最大组织编码
     *
     * @return 结果
     */
    @Override
    public String getMaxCode() {
        return this.getBaseMapper().getMaxOrgCode();
    }

    /**
     * 通过code获取最大编码
     *
     * @return 结果
     */
    @Override
    public String getMaxCode(String code) {
        return this.getBaseMapper().getMaxOrgCodeByCode(code);
    }


    @Override
    public String getMaxCodeByCompanyId(Long companyId) {
        return this.getBaseMapper().getMaxOrgCodeByCompanyId(companyId);
    }

    /**
     * 查询企业组织【员工数量】
     *
     * @return 组织
     */
    @SuppressWarnings("unchecked")
   /* @Override
    public List<OrgDto> countEmpList(OrgQueryDto queryDto) {
        return fillEmpCount(this.orgList(queryDto), queryDto);
    }*/

    /**
     * 添加组织
     *
     * @param org 组织
     * @return 是否成功
     */
    @Override
    public Boolean add(AsOrg org) {
        if (org.getPid() == 0) {
            throw new BusinessException(SystemResultCode.CANT_ADD_ROOT);
        } else {
            // 查询父节点
            AsOrg parentOrg = this.getOne(new QueryWrapper<AsOrg>().lambda().eq(AsOrg::getId, org.getPid()));
            if (ObjectUtil.isNull(parentOrg)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "父节点不存在");
            }
            // 部门下不可添加公司
            if (AssetConstant.ORG_TYPE_DEPT.equals(parentOrg.getOrgType())
                    && AssetConstant.ORG_TYPE_COMPANY.equals(org.getOrgType())) {
                throw new BusinessException(SystemResultCode.ORG_ADD_ERROR);
            }

            // level 加一
            int level = parentOrg.getLevel() + 1;
            if (level >= ORG_MAX_LEVEL) {
                throw new BusinessException(SystemResultCode.LEVEL_OVER_MAX, Convert.toStr(ORG_MAX_LEVEL));
            }
            org.setLevel(level);
            // 添加paths路径
            org.setPaths(parentOrg.getPaths() + parentOrg.getId() + ",");
        }
        // 入库前的唯一性校验
        checkRepeat(org, false);
        // 查询该部门公司的所属公司
        List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
        Long orgCompanyOwner = this.baseMapper.getOrgCompanyOwner(ids);
        org.setCompanyOwner(orgCompanyOwner);

        Long id = IdUtils.getId();
        org.setId(id);
        if (AssetConstant.ORG_TYPE_COMPANY.equals(org.getOrgType())) {
            org.setCompanyOwner(id);
        }
        // 钉钉自建与企微自建
        Edition.ding(() -> org.setSourceType(2));
        Edition.weixin(() -> org.setSourceType(3));
        if (!this.save(org)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }

    @Override
    public String addV2(AsOrg org) {
        Boolean add = this.add(org);
        return add?org.getId().toString():null;
    }

    /**
     * 实体唯一性校验
     *
     * @param org    实体
     * @param isEdit 是否是修改操作
     */
    private void checkRepeat(AsOrg org, boolean isEdit) {
        LambdaQueryWrapper<AsOrg> wrapper = new LambdaQueryWrapper<>();
        // 更新时可修改自身属性，但不能与其他记录相同。这个先排开当前记录
        if (isEdit) {
            wrapper.ne(AsOrg::getId, org.getId());
        }
        LambdaQueryWrapper<AsOrg> clone = wrapper.clone();
        // categoryCode 唯一性校验
        wrapper.eq(AsOrg::getOrgCode, org.getOrgCode());
        AsOrg categoryCodeRepeat = this.getOne(wrapper);
        if (ObjectUtil.isNotNull(categoryCodeRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "组织编码", org.getOrgCode());
        }
        // categoryName 唯一性校验
        clone.eq(AsOrg::getPid, org.getPid()).eq(AsOrg::getOrgName, org.getOrgName());
        AsOrg categoryNameRepeat = this.getOne(clone, false);
        if (ObjectUtil.isNotNull(categoryNameRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "组织名称", org.getOrgName());
        }
        // 企微版本名称重复校验
        Edition.weixin(() -> {
            List<String> matchIds = SpringUtil.getBean(WeixinAdapter.class).orgNameMatch(org.getOrgName(), LoginUserThreadLocal.getCompanyId());
            // 为空
            if (CollUtil.isEmpty(matchIds)) {
                return;
            }
            // 编辑且数据唯一
            if (isEdit && matchIds.size() == 1) {
                return;
            }
            // 重复
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "组织名称", org.getOrgName());
        });
    }

    /**
     * 修改组织结构
     *
     * @param org 组织
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(AsOrg org) {
        //查询当前修改节点是否存在
        AsOrg currentOrg = this.getOne(new QueryWrapper<AsOrg>().lambda().eq(AsOrg::getId, org.getId()), false);
        BusinessExceptionUtil.checkNotNull(currentOrg, "组织不存在");

        //查询新的父级节点，如果父级节点不存在，则修改失败
        AsOrg parentOrg;
        if (Objects.equals(org.getPid(), 0L)) {
            //兼容父级节点是顶级根节点，在数据库里面是没有根节点数据，模拟构造一条根节点数据
            parentOrg = new AsOrg();
            parentOrg.setId(0L);
            parentOrg.setLevel(-1);
            parentOrg.setPaths("");
            parentOrg.setOrgType(AssetConstant.ORG_TYPE_COMPANY);
        } else {
            parentOrg = this.getOne(new QueryWrapper<AsOrg>().lambda().eq(AsOrg::getId, org.getPid()), false);
        }
        BusinessExceptionUtil.checkNotNull(parentOrg, "父节点不存在");

        // 部门下不可挂公司
        if (AssetConstant.ORG_TYPE_DEPT.equals(parentOrg.getOrgType())
                && AssetConstant.ORG_TYPE_COMPANY.equals(currentOrg.getOrgType())) {
            throw new BusinessException(SystemResultCode.ORG_ADD_ERROR);
        }

        //是否移动分类层级，true：改变分类层级 false：没有改变分类层级(仅改变编码和名称等信息)
        boolean moveLevel = !currentOrg.getPid().equals(parentOrg.getId());

        // 验证数据唯一性
        checkRepeat(org, true);

        if (moveLevel) {
            //查询当前资产分类及其子资产分类
            String sonPath = currentOrg.getPaths() + currentOrg.getId() + ",";
            List<AsOrg> currentSubOrg = this.list(new QueryWrapper<AsOrg>().lambda().likeRight(AsOrg::getPaths, sonPath));

            //资产分类层级不能超过7层
            TreeUtils.checkLevelOverMax(parentOrg, currentSubOrg, ORG_MAX_LEVEL);

            //父级节点不能是当前修改节点及其子节点
            TreeUtils.checkParent(parentOrg.getId(), currentOrg, currentSubOrg);

            AsOrg modify = new AsOrg();
            modify.setId(currentOrg.getId());
            modify.setPid(org.getPid());
            modify.setOrgCode(org.getOrgCode());
            modify.setOrgName(org.getOrgName());
            modify.setLevel(parentOrg.getLevel() + 1);
            modify.setPaths(parentOrg.getPaths() + parentOrg.getId() + ",");
            modify.setDirector(org.getDirector());

            //递归修改paths和level等信息
            TreeUtils.assemblePaths(modify, currentSubOrg);
            currentSubOrg.add(modify);

            // 修改所属公司
            currentSubOrg.forEach(f -> {
                List<Long> ids = Convert.toList(Long.class, f.getPaths().split(","));
                Long orgCompanyOwner = this.baseMapper.getOrgCompanyOwner(ids);
                f.setCompanyOwner(orgCompanyOwner);
            });

            //批量修改paths信息
            updateBatchById(currentSubOrg, currentSubOrg.size());
        } else {
            AsOrg update = new AsOrg();
            update.setId(org.getId());
            update.setOrgName(org.getOrgName());
            update.setOrgCode(org.getOrgCode());
            update.setDirector(org.getDirector());
            //没有修改层级，直接修改当前节点的编码和名称即可
            updateById(update);
        }
        if (StrUtil.isNotEmpty(org.getOrgName())) {
            SpringUtil.getBean(CacheOrgStrategy.class).evictCache(org.getId());
        }
        return true;
    }

    @Override
    public Boolean editRootOrg(EditRootOrg org) {
        AsOrg root = this.getById(org.getId());
        if (Objects.isNull(root) || root.getPid() != 0L) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        List<AsOrg> orgs = this.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getOrgCode, org.getCode())
                        .ne(AsOrg::getId, org.getId())
        );
        if (!CollUtil.isEmpty(orgs)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        if (Edition.isLocal()) {
            this.update(
                    Wrappers.lambdaUpdate(AsOrg.class)
                            .set(AsOrg::getOrgCode, org.getCode())
                            .set(StrUtil.isNotEmpty(org.getOrgName()), AsOrg::getOrgName, org.getOrgName())
                            .eq(AsOrg::getId, org.getId())
            );

            if (StrUtil.isNotEmpty(org.getOrgName())) {
                SpringUtil.getBean(CacheOrgStrategy.class).evictCache(org.getId());
            }
        } else {
            this.update(
                    Wrappers.lambdaUpdate(AsOrg.class)
                            .set(AsOrg::getOrgCode, org.getCode())
                            .eq(AsOrg::getId, org.getId()));
            SpringUtil.getBean(CacheOrgStrategy.class).evictCache(org.getId());
        }
        return true;
    }

    /*@Override
    public void getChildIdsByParentId(List<Long> permsIds, List<AsOrg> orgList, Long parentId, List<Long> result) {
        for (AsOrg asOrg : orgList) {
            if (ObjectUtil.equal(parentId, asOrg.getPid())) {
                if (permsIds.contains(asOrg.getId())) {
                    result.add(asOrg.getId());
                }
                getChildIdsByParentId(permsIds, orgList, asOrg.getId(), result);
            }
        }
    }*/

    /**
     * 分页查询组织
     *
     * @param queryDto
     * @return 组织列表
     */
    @Override
    public IPage<OrgDto> orgPage(OrgQueryDto queryDto) {
        if (queryDto.getCompanyId() == null) {
            queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        }

        // 是否过滤权限(默认带权限)
        String deptSql = null;
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
            deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        }
        // 结果集
        IPage<OrgDto> orgList;
        // 企业微信兼容，部门搜索
        if (StrUtil.isNotEmpty(queryDto.getKw()) && Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).orgSearch(queryDto.getKw());
            orgList = this.baseMapper.listOrg(queryDto.buildIPage(), queryDto, deptSql, unionIds);
        } else {
            orgList = this.baseMapper.listOrg(queryDto.buildIPage(), queryDto, deptSql, null);
        }

        return orgList;
    }

    /**
     * 查询组织列表
     *
     * @param queryDto
     * @return 组织列表
     */
    @Override
    public List<OrgDto> orgList(OrgQueryDto queryDto) {
        if (queryDto.getCompanyId() == null) {
            queryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        }

        // 是否过滤权限(默认带权限)
        String deptSql = null;
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
            deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        }
        // 结果集
        List<OrgDto> orgList;
        // 企业微信兼容，部门搜索
        if (StrUtil.isNotEmpty(queryDto.getKw()) && Edition.isWeixin()) {
            List<String> unionIds = SpringUtil.getBean(WeixinAdapter.class).orgSearch(queryDto.getKw());
            orgList = this.baseMapper.listOrg(queryDto, deptSql, unionIds);
        } else {
            orgList = this.baseMapper.listOrg(queryDto, deptSql, null);
        }

        // 是否构造树，只有开启过滤权限，才需要补齐无权限数据
        if (BooleanUtil.isTrue(queryDto.getBuildTree())) {
            // 如果是部门，需要补公司数据
            if (Objects.equals(queryDto.getOrgType(), AssetConstant.ORG_TYPE_DEPT)) {
                fillTreeNoPermNode(orgList);
            } else if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
                // 只有开启过滤权限
                fillTreeNoPermNode(orgList);
            }
        }

        // 是否显示人数
        if (BooleanUtil.isTrue(queryDto.getIsShowEmpCount())) {
            fillEmpCount(orgList, queryDto);
        }
        return orgList;
    }

    @Override
    public List<Long> hasPermOrgIds(List<Long> orgIds, Integer orgType) {
        String deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        return getBaseMapper().hasPermOrgIds(orgIds, orgType, deptSql);
    }

    private void fillTreeNoPermNode(List<OrgDto> orgList) {
        // 当前组织Id
        Set<Long> orgIds = orgList.stream().map(OrgDto::getId).collect(Collectors.toSet());
        Set<Long> pidSet = new HashSet<>();
        // 如果父节点Id不在当前组织集合中，则需要查询补充
        for (OrgDto orgDto : orgList) {
            String paths = orgDto.getPaths();
            List<Long> pIds = Arrays.stream(paths.split(",")).map(Long::parseLong).collect(Collectors.toList());
            for (Long id : pIds) {
                if (id > 0 && !orgIds.contains(id)) {
                    pidSet.add(id);
                }
            }
        }
        if (!pidSet.isEmpty()) {
            OrgQueryDto queryDto = new OrgQueryDto().setIncludeIds(new ArrayList<>(pidSet));
            List<OrgDto> orgDtos = this.baseMapper.listOrg(queryDto, null, null);
            orgDtos.forEach(f -> f.setDisabled(true));
            orgList.addAll(orgDtos);
        }
    }

    @Override
    public List<OrgExportDto> getExcelData(OrgQueryDto orgQueryDto) {
        String deptSql = dataScopeStrategyManager.simplePermsSql(DataPermType.DEPT);
        return this.getBaseMapper().getExcelData(orgQueryDto, deptSql);
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<ImportErrorCO> list = importErrorAbs.listImportError(new ImportErrorListQry().setTaskId(taskId)
                .setImportType(DictConstant.IMPORT_TYPE_ORG));
        return list.stream().map(ImportErrorCO::getSheetModelList)
                .collect(Collectors.toList());
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        ImportErrorCO importError =
                new ImportErrorCO()
                        .setTaskId(importErrorDto.getTaskId())
                        .setErrorNum(0)
                        .setImportType(DictConstant.IMPORT_TYPE_ORG)
                        .setSheetModelList(importErrorDto.getHeadModelList());
        this.importErrorAbs.saveImportError(new ImportErrorSaveCmd().setImportErrorCO(importError));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSheetData(OrgImportDto importDto) {
        // 转换实体
        AsOrg org = BeanUtil.copyProperties(importDto, AsOrg.class);
        // 通过pidCode查询id
        AsOrg parentOrg = this.getOne(new QueryWrapper<AsOrg>().lambda().eq(AsOrg::getOrgCode, importDto.getPidCode()), false);
        // 单个企业组织编码不能重复
        if (this.count(new QueryWrapper<AsOrg>().lambda()
                .eq(AsOrg::getOrgCode, org.getOrgCode())) > 0) {
            LuckySheetModel codee = importDto.getSheetModelList().get(0);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("组织编码已存在");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }
        if (ObjectUtil.isNull(parentOrg)) {
            LuckySheetModel pidCode = importDto.getSheetModelList().get(3);
            if (ObjectUtil.isNull(pidCode.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("上级组织编码不存在");
                pidCode.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        } else {
            // 部门下不可添加公司
            if (AssetConstant.ORG_TYPE_DEPT.equals(parentOrg.getOrgType())
                    && AssetConstant.ORG_TYPE_COMPANY.equals(org.getOrgType())) {
                LuckySheetModel codee = importDto.getSheetModelList().get(2);
                if (ObjectUtil.isNull(codee.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("部门下不可添加公司");
                    codee.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
            org.setPid(parentOrg.getId());
            // level 加一
            int level = parentOrg.getLevel() + 1;
            if (level >= ORG_MAX_LEVEL) {
                LuckySheetModel pidCode = importDto.getSheetModelList().get(3);
                if (ObjectUtil.isNull(pidCode.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("层级不得超过" + ORG_MAX_LEVEL + "层");
                    pidCode.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            } else {
                org.setLevel(level);
                // 添加paths路径
                org.setPaths(parentOrg.getPaths() + parentOrg.getId() + ",");
            }
            // 查询改父节点下是否code重复
            if (this.count(new QueryWrapper<AsOrg>().lambda()
                    .eq(AsOrg::getPid, org.getPid())
                    .eq(AsOrg::getOrgName, org.getOrgName())) > 0) {
                LuckySheetModel orgName = importDto.getSheetModelList().get(1);
                if (ObjectUtil.isNull(orgName.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("组织名称已存在");
                    orgName.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
            // 钉钉与企微环境自建部门不允许挂在非自建部门下
            if ((Edition.isWeixin() || Edition.isDing()) && parentOrg.getSourceType() == 1 && parentOrg.getPid() != 0L) {
                LuckySheetModel parentOrgName = importDto.getSheetModelList().get(2);
                if (ObjectUtil.isNull(parentOrgName.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("上级组织只允许自建类型");
                    parentOrgName.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            }
        }
        if (importDto.getErrorNum() == 0) {
            // 查询该部门公司的所属公司
            List<Long> ids = Convert.toList(Long.class, org.getPaths().split(","));
            Long orgCompanyOwner = this.baseMapper.getOrgCompanyOwner(ids);
            org.setCompanyOwner(orgCompanyOwner);
            Long id = IdUtils.getId();
            org.setId(id);
            // 钉钉自建与企微自建
            Edition.ding(() -> org.setSourceType(2));
            Edition.weixin(() -> org.setSourceType(3));
            this.save(org);
            redisService.hIncr(RedisConstant.companyImportKey("org", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("org", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            return true;
        } else {
            ImportErrorSaveOrUpdateCmd cmd =
                    new ImportErrorSaveOrUpdateCmd()
                            .setImportErrorCO(copyToAsOrgImportError(importDto));
            importErrorAbs.saveOrUpdateImportError(cmd);
            redisService.hIncr(RedisConstant.companyImportKey("org", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }

    /**
     * 删除
     *
     * @param ids Id
     * @return 是否成功
     */
    @Override
    public Boolean remove(List<Long> ids) {
        List<AsUserOrg> userOrgList = userOrgMapper.orgEmpList(LoginUserThreadLocal.getCompanyId(), ids, false);
        Set<Long> hasUserOrgSet = userOrgList.stream().map(AsUserOrg::getOrgId).collect(Collectors.toSet());

        Long companyId = LoginUserThreadLocal.getCompanyId();
        // 目前原型设计只能逐条删除，因此先循环判断
        for (Long orgId : ids) {
            // 判断关联资产
            Integer count = this.getBaseMapper().orgRefAsset(orgId);
            Integer useOrgCount = this.getBaseMapper().useOrgRefAsset(orgId);
            Integer areaCount = this.getBaseMapper().orgRefArea(orgId, companyId);
            Integer repositoryCount = this.getBaseMapper().orgRefRepository(orgId, companyId);
            if (count > 0 || useOrgCount > 0) {
                throw new BusinessException(SystemResultCode.ORG_ASSET_EXISTS);
            }
            // 查看是否有员工
            if (hasUserOrgSet.contains(orgId)) {
                throw new BusinessException(SystemResultCode.ORG_USED, "员工");
            }
            if (areaCount > 0) {
                throw new BusinessException(SystemResultCode.ORG_USED, "区域");
            }
            if (repositoryCount > 0) {
                throw new BusinessException(SystemResultCode.ORG_USED, "仓库");
            }

            //统计组织是否开启了财务折旧功能 以及 是否有分摊部门是当前组织的，有以上情况不允许删除当前组织
            List<AsOrg> orgList = this.list(Wrappers.lambdaQuery(AsOrg.class)
                    .select(AsOrg::getId).eq(AsOrg::getId, orgId)
                    .or().like(AsOrg::getPaths, ("," + orgId + ",")));
            if (!CollUtil.isEmpty(orgList)) {
                List<Long> orgIdList = orgList.stream().map(AsOrg::getId).collect(Collectors.toList());
                FinanceStatisticsQueryDto statisticsQueryDto = new FinanceStatisticsQueryDto();
                statisticsQueryDto.setOrgIds(orgIdList);
                Integer depreciationCount = assetFinanceInfoAbs.countDepreciationConfigByOrg(statisticsQueryDto);
                if (Objects.nonNull(depreciationCount) && depreciationCount > 0) {
                    throw new BusinessException(SystemResultCode.ORG_FINANCE);
                }

                Integer assetFinanceCount = assetFinanceInfoAbs.countAssetFinanceByOrg(statisticsQueryDto);
                if (Objects.nonNull(assetFinanceCount) && assetFinanceCount > 0) {
                    throw new BusinessException(SystemResultCode.ORG_FINANCE);
                }
            }

            // 删除组织和子节点
            this.remove(new QueryWrapper<AsOrg>().lambda().eq(AsOrg::getId, orgId)
                    .or().like(AsOrg::getPaths, ("," + orgId + ",")));
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sort(List<Long> orgIds) {
        int count = this.list(new QueryWrapper<AsOrg>().lambda()
                .select(AsOrg::getPid)
                .in(AsOrg::getId, orgIds)
                .groupBy(AsOrg::getPid)).size();
        if (count > 1) {
            throw new BusinessException(SystemResultCode.ORG_SORT_ERROR);
        }
        AtomicInteger idx = new AtomicInteger(0);
        List<AsOrg> collect = orgIds.stream().map(it -> {
            AsOrg org = new AsOrg();
            org.setId(it);
            org.setSortNum(idx.getAndIncrement());
            return org;
        }).collect(Collectors.toList());
        this.updateBatchById(collect);
        return true;
    }

    @Override
    public List<OrgDto> listByDirector(Long userId, Long companyId) {
        return this.getBaseMapper().listByDirector(userId, companyId);
    }

    @Override
    public List<OrgDto> listByDirectors(List<Long> userIds, Long companyId) {
        return this.getBaseMapper().listByDirectors(userIds, companyId);
    }

    @Override
    public List<AsOrg> listAllByIds(List<Long> orgIds) {
        return listAllByIds(orgIds, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public List<AsOrg> listAllByIds(List<Long> orgIds, Long companyId) {
        if (CollUtil.isNotEmpty(orgIds)) {
            return this.getBaseMapper().listAllByIds(orgIds, companyId);
        } else {
            return new ArrayList<>();
        }
    }

    private ImportErrorCO copyToAsOrgImportError(OrgImportDto importDto) {
        ImportErrorCO importError = new ImportErrorCO();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_ORG);
        List<LuckySheetModel> sheetModelList = importDto.getSheetModelList();
        importError.setSheetModelList(new ArrayList<>(sheetModelList));
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }
   /* @Override
    public List<OrgDto> countPermsEmpList(OrgQueryDto queryDto) {
        // 模糊查询组织名称
        List<OrgDto> result = filterPerms(this.orgList(queryDto), AssetConstant.DATA_PERMISSION_ORG, AssetConstant.AUTHORITY_DEPTS, Arrays.asList(AssetConstant.ORG_TYPE_COMPANY, AssetConstant.ORG_TYPE_DEPT));
        return fillEmpCount(result, queryDto);
    }*/

    private List<OrgDto> fillEmpCount(List<OrgDto> result, OrgQueryDto queryDto) {
        if (queryDto.getIsShowEmpCount() && CollUtil.isNotEmpty(result)) {
            Long companyId = LoginUserThreadLocal.getCompanyId();
            // 新版本计算
            List<Long> orgIds = result.stream().map(OrgDto::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(orgIds)) {
                if (BooleanUtil.isTrue(queryDto.getIsOnlyShowCurrentNodeEmpCount())) {
                    // 仅显示当前节点的数量-要去掉其子节点的员工数量
                    List<AsUserOrg> userOrgList = userOrgMapper.orgEmpList(companyId, orgIds, BooleanUtil.isTrue(queryDto.getIsOnlyShowEmpWithAccount()));
                    Map<Long, Long> orgUserCount = userOrgList.stream().collect(Collectors.groupingBy(AsUserOrg::getOrgId, Collectors.counting()));
                    result.forEach(f -> f.setEmpNumber(Convert.toInt(orgUserCount.getOrDefault(f.getId(), 0L))));
                } else {
                    // 显示当前节点及子节点的员工数量
                    List<AsOrg> orgAllList = list(Wrappers.lambdaQuery(AsOrg.class)
                            .select(AsOrg::getId, AsOrg::getPaths)
                            .orderByAsc(AsOrg::getLevel));
                    List<AsUserOrg> userOrgList = userOrgMapper.orgEmpList(companyId, null, BooleanUtil.isTrue(queryDto.getIsOnlyShowEmpWithAccount()));
                    Map<Long, Set<Long>> orgUserSetMap = userOrgList.stream().collect(Collectors
                            .groupingBy(AsUserOrg::getOrgId, Collectors.mapping(AsUserOrg::getUserId, Collectors.toSet())));
                    Map<Long, Set<Long>> accumulateMap = new HashMap<>();
                    for (AsOrg asOrg : orgAllList) {
                        if (orgUserSetMap.containsKey(asOrg.getId())) {
                            Set<Long> userSet = orgUserSetMap.get(asOrg.getId());
                            // 写入父节点集合
                            String[] split = asOrg.getPaths().split(",");
                            Set<String> accumulateIdSet = Arrays.stream(split).collect(Collectors.toSet());
                            // 统计当前节点
                            accumulateIdSet.add(Convert.toStr(asOrg.getId()));
                            // 写入accumulateMap累加计算
                            for (String s : accumulateIdSet) {
                                Long pOrgId = Convert.toLong(s, 0L);
                                if (pOrgId > 0L) {
                                    Set<Long> accumulate = accumulateMap.getOrDefault(pOrgId, new HashSet<>());
                                    accumulate.addAll(userSet);
                                    accumulateMap.put(pOrgId, accumulate);
                                }
                            }
                        }
                    }
                    result.forEach(f -> {
                        f.setEmpNumber(Convert.toInt(accumulateMap.getOrDefault(f.getId(), new HashSet<>()).size()));
                        f.setEmpCount(f.getEmpNumber());
                    });
                }
            }
        }
        return result;
    }

    @Override
    public List<OrgDto> areaPermsList() {
        // 获取权限
        List<ModelDataScope> modelDataScopeList = modelDataScopeService.loadDataScope();
        ModelDataRule rule = null;
        for (ModelDataScope modelDataScope : modelDataScopeList) {
            for (ModelDataRule modelDataRule : modelDataScope.getRuleList()) {
                if (DataPermType.AREA.equals(modelDataRule.getDataType())) {
                    rule = modelDataRule;
                    break;
                }
            }
        }

        if (rule != null) {
            if (DataPermRuleType.TYPE_CURRENT_ONLY.equals(rule.getRuleType())) {
                // 仅查看本公司数据
                List<OrgDto> orgList = getBaseMapper().companyList(rule.getIds(), false, null);
                fillTreeNoPermNode(orgList);
                return orgList;
            } else if (DataPermRuleType.TYPE_CURRENT_AND_CHILD.equals(rule.getRuleType())) {
                // 查看本公司及子集数据
                List<OrgDto> orgList = getBaseMapper().companyList(rule.getIds(), true, null);
                fillTreeNoPermNode(orgList);
                return orgList;
            } else if (DataPermRuleType.TYPE_CUSTOM_LIST.equals(rule.getRuleType())) {
                List<OrgDto> orgList = getBaseMapper().companyList(rule.getIds(), false, DataPermType.AREA.getValue());
                fillTreeNoPermNode(orgList);
                return orgList;
            }
        }
        return orgList(new OrgQueryDto().setOrgType(1).setFilterPerm(false));
    }

    @Override
    public List<OrgDto> storePermsList() {
        // 获取权限
        List<ModelDataScope> modelDataScopeList = modelDataScopeService.loadDataScope();
        ModelDataRule rule = null;
        for (ModelDataScope modelDataScope : modelDataScopeList) {
            for (ModelDataRule modelDataRule : modelDataScope.getRuleList()) {
                if (DataPermType.STORE.equals(modelDataRule.getDataType())) {
                    rule = modelDataRule;
                }
            }
        }

        if (rule != null) {
            if (DataPermRuleType.TYPE_CURRENT_ONLY.equals(rule.getRuleType())) {
                // 仅查看本公司数据
                List<OrgDto> orgList = getBaseMapper().companyList(rule.getIds(), false, null);
                fillTreeNoPermNode(orgList);
                return orgList;
            } else if (DataPermRuleType.TYPE_CURRENT_AND_CHILD.equals(rule.getRuleType())) {
                // 查看本公司及子集数据
                List<OrgDto> orgList = getBaseMapper().companyList(rule.getIds(), true, null);
                fillTreeNoPermNode(orgList);
                return orgList;
            } else if (DataPermRuleType.TYPE_CUSTOM_LIST.equals(rule.getRuleType())) {
                List<OrgDto> orgList = getBaseMapper().companyList(rule.getIds(), false, DataPermType.STORE.getValue());
                fillTreeNoPermNode(orgList);
                return orgList;
            }
        }
        return orgList(new OrgQueryDto().setOrgType(1).setFilterPerm(false));
    }

    @Override
    public Long getOrgCompanyOwner(List<Long> ids) {
        return this.getBaseMapper().getOrgCompanyOwner(ids);
    }

    @Override
    public AsOrg getRootOrg(Long companyId) {
        // 获取系统组织根节点
        List<AsOrg> systemRootOrg = this.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getOrgType, 1)
                        .orderByAsc(AsOrg::getLevel)
                        .last("LIMIT 1")
        );
        if (CollUtil.isEmpty(systemRootOrg)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "企业组织根节点不存在");
        }
        return systemRootOrg.get(0);
    }

    @Override
    public List<AsOrg> getAll(Long companyId) {
        return this.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getExternalOrgId)
                        .eq(AsOrg::getCompanyId, companyId)
                        .eq(AsOrg::getSourceType, 1)
        );
    }

    @Override
    public Map<String, Long> getExternalMapping(Long companyId, List<String> externalIds) {
        if (CollUtil.isEmpty(externalIds)) {
            return Collections.emptyMap();
        }
        return this.list(
                Wrappers.lambdaQuery(AsOrg.class)
                        .in(AsOrg::getExternalOrgId, externalIds)
                        .eq(AsOrg::getCompanyId, companyId)
        ).stream().collect(Collectors.toConcurrentMap(AsOrg::getExternalOrgId, AsOrg::getId));
    }

    @Override
    public String getExternalId(Long id) {
        if (Objects.isNull(id)) {
            return "";
        }
        AsOrg byId = this.getById(id);
        if (Objects.isNull(byId) || StrUtil.isBlank(byId.getExternalOrgId())) {
            return "";
        }
        return byId.getExternalOrgId();
    }

    @Override
    public Map<Long, List<AsOrg>> listOrgByEmpIds(List<Long> empIds) {
        Map<Long, List<AsOrg>> result = new HashMap<>();
        if (CollUtil.isNotEmpty(empIds)) {
            List<AsUserOrg> userOrgList = userOrgMapper.selectList(
                    Wrappers.lambdaQuery(AsUserOrg.class).in(AsUserOrg::getUserId, empIds));

            Set<Long> orgIds = userOrgList.stream().map(AsUserOrg::getOrgId).collect(Collectors.toSet());
            if (orgIds.size() > 0) {
                List<AsOrg> orgList = list(Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getOrgName, AsOrg::getOrgCode)
                        .in(AsOrg::getId, orgIds));
                Map<Long, AsOrg> orgMap = orgList.stream().collect(Collectors.toMap(AsOrg::getId, k -> k));
                result = userOrgList.stream().collect(Collectors.groupingBy(AsUserOrg::getUserId,
                        Collectors.mapping(k -> orgMap.get(k.getOrgId()), Collectors.toList())));
            }
        }
        return result;
    }

    @Override
    public List<DictDataDto> listSimpleWithPerms(AsDataAuthority authority, Long companyId) {
        List<DictDataDto> data = new ArrayList<>();
        if (AssetConstant.AUTHORITY_TYPE_ALL == authority.getAuthorityType()) {
            // 所有权限，标记全部数据
            data = list(Wrappers.lambdaQuery(AsOrg.class).select(AsOrg::getId, AsOrg::getOrgName).eq(AsOrg::getCompanyId, companyId))
                    .stream().map(o -> new DictDataDto().setValue(o.getId().toString()).setLabel(o.getOrgName()))
                    .collect(Collectors.toList());
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == authority.getAuthorityType()
                || AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == authority.getAuthorityType()
                || AssetConstant.AUTHORITY_TYPE_COMP_AND_CHILD_DEPT == authority.getAuthorityType()) {
            data = getBaseMapper().listSimpleWithPerms(authority.getUserId(), authority.getAuthorityType(), companyId);
        } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义数据类型
            if (CollUtil.isNotEmpty(authority.getAuthorityData())) {
                List<AsOrg> orgList = list(Wrappers.lambdaQuery(AsOrg.class)
                        .select(AsOrg::getId, AsOrg::getOrgName)
                        .eq(AsOrg::getCompanyId, companyId)
                        .in(AsOrg::getId, authority.longAuthorityData()));
                data = orgList.stream().map(o -> new DictDataDto().setLabel(o.getOrgName()).setValue(o.getId().toString()))
                        .collect(Collectors.toList());
            }
        }
        return data;
    }

    @Override
    public Long getOne(String orgName, String orgCode) {
        LoginUserDto userDto = LoginUserThreadLocal.get();
        if (userDto == null
                || userDto.getCusUser() == null
                || userDto.getCusUser().getCompanyId() == null) {
            return null;
        }
        Long companyId = userDto.getCusUser().getCompanyId();
        if (StrUtil.isNotEmpty(orgName)) {
            String cacheKey = RedisConstant.orgDictKey() + ":" + companyId + ":name:" + orgName;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsOrg one = getOne(Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId).eq(AsOrg::getOrgName, orgName), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        } else if (StrUtil.isNotEmpty(orgCode)) {
            String cacheKey = RedisConstant.orgDictKey() + ":" + companyId + ":code:" + orgCode;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsOrg one = getOne(Wrappers.lambdaQuery(AsOrg.class)
                        .eq(AsOrg::getCompanyId, companyId).eq(AsOrg::getOrgCode, orgCode), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        }
        return null;
    }

    /**
     * 数据权限过滤
     *
     * @param orgDtos
     * @param authorityDataCode
     * @param authorityCode
     * @param orderTypes
     * @return
     */
    /*private List<OrgDto> filterPerms(List<OrgDto> orgDtos, String authorityDataCode, String authorityCode, List<Integer> orderTypes) {
        if (LoginUserThreadLocal.getCusUser().getIsAdmin()) {
            return orgDtos.stream().filter(o -> orderTypes.contains(o.getOrgType()))
                    .map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList());
        }
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        AsDataAuthority dataAuthority = dataAuthorityService.getByUserAndDataCodeAndCode(userId,
                authorityDataCode, authorityCode);
        if (dataAuthority == null) {
            BusinessExceptionUtil.throwException("您没有此数据权限, 请联系管理员");
        }
        UserDataPermsDto dataPermsDto = dataPermissionCacheService.getUserDataPermsCache(LoginUserThreadLocal.getCompanyId(), userId);
        List<Long> perms = dataPermsDto.getConvertAuthorityData(dataAuthority.getPermsCacheKey(), Long.class);
        if (!AssetConstant.DATA_PERMISSION_ORG.equals(dataAuthority.getAuthorityDataCode())) {
            perms = dataPermsDto.getConvertAuthorityData(dataAuthority.getPermsCacheKey(), UserDataOrgPermBizDto.class)
                    .stream().map(UserDataOrgPermBizDto::getOrg).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(perms)) {
            throw new BusinessException(SystemResultCode.PERMISSION_DATA_ACCESS_PERMISSION);
        }
        List<OrgDto> resolve = new ArrayList<>();
        if (CollUtil.isNotEmpty(orgDtos)) {
            Map<Long, OrgDto> data = orgDtos.stream().collect(Collectors.toMap(OrgDto::getId, o -> o));

            if (AssetConstant.AUTHORITY_TYPE_ALL == dataAuthority.getAuthorityType()) {
                return orgDtos.stream().filter(o -> orderTypes.contains(o.getOrgType()))
                        .map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList());
            } else {
                List<Long> finalPerms = perms;
                List<OrgDto> myOrgs = orgDtos.stream().filter(o -> finalPerms.contains(o.getId())).collect(Collectors.toList());
                for (OrgDto orgDto : myOrgs) {
                    orgDto.setDisabled(Boolean.FALSE);
                    resolve.add(orgDto);
                    findParent(data, orgDto.getPid(), resolve);
                }
            }
        }
        return resolve.stream().filter(DeduplicationUtil.distinctByKey(OrgDto::getId))
                .sorted(Comparator.comparing(OrgDto::getSortNum).thenComparing(OrgDto::getCreateTime))
                .collect(Collectors.toList());
    }

    private void findParent(Map<Long, OrgDto> data, Long currentId, List<OrgDto> resolve) {
        OrgDto orgDto = data.get(currentId);
        if (orgDto == null) {
            return;
        }
        resolve.add(orgDto);
        findParent(data, orgDto.getPid(), resolve);
    }

    private void findChildren(List<OrgDto> data, Long currentPid, List<OrgDto> selfAndChildren) {
        for (OrgDto orgDto : data) {
            if (orgDto.getPid().equals(currentPid)) {
                selfAndChildren.add(orgDto);
                findChildren(data, orgDto.getId(), selfAndChildren);
            }
        }
    }
    private void findParentOrgCompany(Map<Long, OrgDto> data, Long currentId, List<OrgDto> resolve) {
        OrgDto orgDto = data.get(currentId);
        if (orgDto == null) {
            return;
        }
        if (AssetConstant.ORG_TYPE_COMPANY.equals(orgDto.getOrgType())) {
            orgDto.setDisabled(Boolean.FALSE);
            resolve.add(orgDto);
            return;
        }
        findParentOrgCompany(data, orgDto.getPid(), resolve);
    }
    */
}
