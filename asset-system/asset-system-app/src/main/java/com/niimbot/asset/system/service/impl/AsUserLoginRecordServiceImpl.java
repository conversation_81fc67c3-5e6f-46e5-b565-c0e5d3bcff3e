package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsUserLoginRecordMapper;
import com.niimbot.asset.system.model.AsUserLoginRecord;
import com.niimbot.asset.system.service.AsUserLoginRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户登录记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Service
public class AsUserLoginRecordServiceImpl extends ServiceImpl<AsUserLoginRecordMapper, AsUserLoginRecord> implements AsUserLoginRecordService {

}
