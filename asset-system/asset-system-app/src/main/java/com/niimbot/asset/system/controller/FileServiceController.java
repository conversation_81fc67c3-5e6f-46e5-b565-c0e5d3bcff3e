package com.niimbot.asset.system.controller;

import cn.hutool.core.util.ObjectUtil;
import com.niimbot.asset.framework.autoconfig.FileUploadConfig;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.FileUtils;
import com.niimbot.asset.system.model.AsFile;
import com.niimbot.asset.system.service.AsFileService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AsFileDownDto;
import com.niimbot.system.FileParamDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传下周服务器
 *
 * <AUTHOR>
 * @Date 2020/11/30
 */
@RestController
@RequestMapping("server/system/file")
@Slf4j
public class FileServiceController {

    private final AsFileService fileService;

    @Autowired
    public FileServiceController(AsFileService fileService) {
        this.fileService = fileService;
    }

    /**
     * 文件上传
     * ------
     * module用于区分文件目录
     * 例如入参为image, 则在rootPath后拼上模块路径，为空则再rootPath下创建上传路径
     * 可传: e.g image,means,category
     *
     * @param files        文件列表
     * @param fileParamDto 文件上传相关控制参数
     * @return 返回上传信息
     */
    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    public List<AsFile> upload(@RequestParam(FileUploadConfig.FRONT_MULTI_PARAM_NAME) MultipartFile[] files,
                               FileParamDto fileParamDto)
            throws IOException {
        return fileService.uploadFile(Arrays.asList(files), fileParamDto);
    }

    /**
     * feign通用下载请求
     *
     * @param id     文件id
     * @param delete 是否删除
     */
    @GetMapping("/download/feign")
    public AsFileDownDto feignFileDownload(Long id, Boolean delete) {
        return fileService.feignFileDownload(id, delete);
    }

    /**
     * 批量下载
     *
     * @param ids 文件id list
     * @return zip 字符流
     */
    @PostMapping("/download/feign/batch")
    public byte[] feignFileDownloadBatch(@RequestBody List<Long> ids) {
        return fileService.feignFileDownloadBatch(ids);
    }

    @GetMapping("/{id}")
    public AsFile getById(@PathVariable Long id) {
        return fileService.getById(id);
    }

    @PostMapping("/list")
    public List<AsFileDownDto> getList(@RequestBody List<Long> ids) {
        return fileService.listByIds(ids);
    }

    /**
     * 【本地下载-已废弃】本地通用下载请求
     *
     * @param id     文件id
     * @param delete 是否删除
     */
    @GetMapping("/download/local")
    @Deprecated
    public void localFileDownload(Long id, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        AsFile byId = fileService.getById(id);
        if (null == byId) {
            log.error("file id:{} not found", id);
            throw new BusinessException(SystemResultCode.FILE_DOWNLOAD_NOT_FOUND);
        }
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        try {
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, byId.getOriginalName()));
            FileUtils.writeBytes(byId.getFilePath(), response.getOutputStream());
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException(SystemResultCode.FILE_NAME_ENCODED_FAIL);
        } catch (IOException exception) {
            log.error("file:{} not found", byId.getFilePath());
            throw new BusinessException(SystemResultCode.FILE_DOWNLOAD_NOT_FOUND);
        }
        if (ObjectUtil.isNotNull(delete) && delete) {
            if (fileService.removeById(id)) {
                log.warn("file delete fail, id:{} not found", id);
            }
            FileUtils.deleteFile(byId.getFilePath());
        }
    }
}
