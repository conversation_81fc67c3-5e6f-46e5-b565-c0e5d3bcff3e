package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsImportTaskMapper;
import com.niimbot.asset.system.model.AsImportTask;
import com.niimbot.asset.system.service.AsImportTaskService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 导入导出任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@Service
public class AsImportTaskServiceImpl extends ServiceImpl<AsImportTaskMapper, AsImportTask> implements AsImportTaskService {

}
