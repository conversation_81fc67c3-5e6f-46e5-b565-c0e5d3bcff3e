package com.niimbot.asset.system.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.dto.openapi.OauthUserInfoRsp;
import com.niimbot.asset.system.mapper.AsOpenApiMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsOpenApi;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsOpenApiService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.OpenApiAuthCodeDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/9/1 16:58
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsOpenApiServiceImpl extends ServiceImpl<AsOpenApiMapper, AsOpenApi> implements AsOpenApiService {

    @Resource
    private AsCusEmployeeService employeeService;
    private final AsAccountEmployeeService accountEmployeeService;
    private final CusUserService cusUserService;

    private final RedisService redisService;

    private static final String OPEN_API_OAUTH = "open_api_oauth";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(AsOpenApi openApi) {
        openApi.setApiId(null);
        checkAppNameRepeat(openApi);
        Edition.saas(() ->
                openApi.setAppKey("yzc" + RandomUtil.randomString(20))
        );
        Edition.ding(() ->
                openApi.setAppKey("ding" + RandomUtil.randomString(20))
        );
        Edition.weixin(() ->
                openApi.setAppKey("wx" + RandomUtil.randomString(20))
        );
        Edition.local(() ->
                openApi.setAppKey("yzc" + RandomUtil.randomString(20))
        );

        openApi.setAppSecret(RandomUtil.randomString(32));
        openApi.setEmpId(IdUtils.getId());
        save(openApi);
        // 写入登录的虚拟账号和虚拟员工
        AsCusEmployee employee = new AsCusEmployee();
        employee.setId(openApi.getEmpId())
                .setEmpName(openApi.getAppName())
                .setIsDelete(true);
        employeeService.save(employee);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(AsOpenApi openApi) {
        openApi.setAppKey(null).setAppSecret(null);
        checkAppNameRepeat(openApi);
        return updateById(openApi);
    }

    @Override
    public List<AsOpenApi> listByCompanyId(Long companyId) {
        return list(Wrappers.lambdaQuery(AsOpenApi.class)
                .eq(AsOpenApi::getCompanyId, companyId));
    }

    public AsOpenApi getByAgentId(Long agentId) {
        return getOne(Wrappers.lambdaQuery(AsOpenApi.class)
                .eq(AsOpenApi::getApiId, agentId));
    }


    @Override
    public String getAuthCode(OpenApiAuthCodeDto authCodeDto,LoginUserDto loginUser) {
        LoginUserDto loginUserDto = loginUser;
        if (loginUserDto == null){
            loginUserDto = LoginUserThreadLocal.get();
        }
        if (loginUserDto == null) {
            throw new BusinessException(SystemResultCode.USER_NOT_LOGGED_IN);
        }
        AsOpenApi openApi = getOne(Wrappers.lambdaQuery(AsOpenApi.class)
                .eq(AsOpenApi::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                .eq(AsOpenApi::getAppKey, authCodeDto.getClientId()));
        if (openApi == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "clientId不存在");
        }

        if (StrUtil.isEmpty(openApi.getRedirectUrl())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "redirectUrl未配置");
        }

        // 重定向地址
        String redirectUrl = new String(URLDecoder.decode(authCodeDto.getRedirectUrl().getBytes()));
        if (!StrUtil.equals(openApi.getRedirectUrl(), redirectUrl)) {
            log.info("clientId=[{}], param url = [{}], db url = [{}]",
                    authCodeDto.getClientId(), redirectUrl, openApi.getRedirectUrl());
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "redirectUrl与配置不一致");
        }

        // 生成code
        String code = UUID.randomUUID().toString(true);
        // redis添加用户和code绑定关系，5分钟有效，重新获取会覆盖老的
        Long empId = loginUserDto.getCusUser().getId();
        AsCusEmployee employee = employeeService.getById(empId);
        if (employee == null) {
            log.error("临时授权码生成错误，找不到对应员工{}", empId);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "找不到对应登录账号");
        }
        redisService.set(OPEN_API_OAUTH + ":" + openApi.getAppKey() + ":" + empId, code, 5, TimeUnit.MINUTES);
        redisService.set(OPEN_API_OAUTH + ":" + openApi.getAppKey() + ":" + code, empId, 5, TimeUnit.MINUTES);
        if (loginUser!=null)
            return code;
        return redirectUrl + "?code=" + code + "&state=" + StrUtil.emptyToDefault(authCodeDto.getState(), StrUtil.EMPTY);
    }

    @Override
    public OauthUserInfoRsp getUserInfoByCode(Long companyId, String appKey, String code) {
        log.info("companyId=[{}], appKey = [{}], code = [{}]", companyId, appKey, code);
        // 查询code对应的emp
        Long empId = null;
        String emp = Convert.toStr(redisService.get(OPEN_API_OAUTH + ":" + appKey + ":" + code), StrUtil.EMPTY);
        if (StrUtil.isNotEmpty(emp)) {
            String refCode = Convert.toStr(redisService.get(OPEN_API_OAUTH + ":" + appKey + ":" + emp), StrUtil.EMPTY);
            if (StrUtil.equals(refCode, code)) {
                empId = Convert.toLong(emp);
            }
        }
        if (empId != null) {
            try {
                // 查询员工
                AsCusEmployee employee = employeeService.getById(empId);
                if (employee == null) {
                    log.error("【openApi employee {} not exists】", emp);
                } else {
                    // 查询账户关系
                    AsAccountEmployee accountEmployee = accountEmployeeService.getOne(Wrappers.lambdaQuery(AsAccountEmployee.class)
                            .eq(AsAccountEmployee::getCompanyId, companyId)
                            .eq(AsAccountEmployee::getEmployeeId, empId));
                    if (accountEmployee == null) {
                        log.error("【openApi accountEmployee {} not exists】", emp);
                    } else {
                        // 查询账户
                        AsCusUser account = cusUserService.getById(accountEmployee.getAccountId());
                        if (account == null) {
                            log.error("【openApi account {} not exists】", accountEmployee.getAccountId());
                        } else {
                            // 构建响应
                            OauthUserInfoRsp rsp = OauthUserInfoRsp.build(employee, account);
                            redisService.del(OPEN_API_OAUTH + ":" + appKey + ":" + code);
                            redisService.del(OPEN_API_OAUTH + ":" + appKey + ":" + empId);
                            return rsp;
                        }
                    }
                }
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "临时授权码【" + code + "】解析异常");
            } catch (Exception e) {
                log.error("临时授权码, {}", e.getMessage(), e);
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "临时授权码【" + code + "】解析异常");
            }
        } else {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "临时授权码【" + code + "】已失效或不存在");
        }
    }

    private void checkAppNameRepeat(AsOpenApi openApi) {
        long count = this.count(Wrappers.lambdaQuery(AsOpenApi.class)
                .eq(AsOpenApi::getAppName, openApi.getAppName())
                .ne(openApi.getApiId() != null, AsOpenApi::getApiId, openApi.getApiId()));
        if (count > 0) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "应用名称", openApi.getAppName());
        }
    }

}
