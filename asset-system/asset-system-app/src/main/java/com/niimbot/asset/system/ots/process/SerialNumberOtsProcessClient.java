package com.niimbot.asset.system.ots.process;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.dto.clientobject.GenSerialNoCmd;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.ots.SerialNumberOts;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.function.Function;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/system/SerialNumberOts/")
public class SerialNumberOtsProcessClient implements SerialNumberOts, InitializingBean {

    @Resource
    private OrgService orgService;

    @Resource
    private CompanySettingService companySettingService;

    @Override
    public List<JSONObject> fillSerial(GenSerialNoCmd cmd) {
        Long companyId = ObjectUtil.isEmpty(cmd.getCompanyId()) ? LoginUserThreadLocal.getCompanyId() : cmd.getCompanyId();
//        Set<String> prefixSet = new HashSet<>();
//        RLock lock = redissonClient.getLock(FILL_SERIAL_LOCK + ":" + companyId + ":" + cmd.getType() + ":" + cmd.getFieldCode());
        try {
            for (JSONObject datum : cmd.getData()) {
                String type = cmd.getType();
                // 转换规则ID
                for (GenSerialNoCmd.Rule rule : cmd.getRules()) {
                    if (BIZS.contains(rule.getCode()) && BIZ_KEY_SUPPLIERS.containsKey(type)) {
                        String key = BIZ_KEY_SUPPLIERS.get(type).apply(rule.getCode());
                        rule.setId(datum.containsKey(key) ? datum.getLong(key) : null);
                    }
                }
                String delimiter = StrUtil.isNotEmpty(cmd.getDelimiter()) ? ('\\' + cmd.getDelimiter()) : cmd.getDelimiter();
                StringJoiner escapeJoiner = new StringJoiner(delimiter);
                StringJoiner joiner = new StringJoiner(cmd.getDelimiter());
                // 有序处理
                List<GenSerialNoCmd.Rule> rules = cmd.getRules();
                rules.forEach(rule -> {
                    if (FIXED_CHAR.equals(rule.getCode())) {
                        escapeJoiner.add(cmd.getFixedChar());
                        joiner.add(cmd.getFixedChar());
                        return;
                    }
                    if (TIMES.contains(rule.getCode()) && TIME_SUPPLIERS.containsKey(rule.getCode())) {
                        String segment = TIME_SUPPLIERS.get(rule.getCode()).apply(null);
                        escapeJoiner.add(segment);
                        joiner.add(segment);
                        return;
                    }
                    if (BIZS.contains(rule.getCode()) && BIZ_SUPPLIERS.containsKey(rule.getCode())) {
                        String segment = BIZ_SUPPLIERS.get(rule.getCode()).apply(rule, companyId);
                        escapeJoiner.add(segment);
                        joiner.add(segment);
                        return;
                    }
                    log.error("不合法的流水号生成规则[" + rule.getCode() + "]");
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "不合法的流水号生成规则[" + rule.getCode() + "]");
                });
                String escapePrefix = escapeJoiner.toString() + delimiter;
                String prefix = joiner.toString() + cmd.getDelimiter();
//                prefixSet.add(prefix);
                // 生成编码和流水号
                String code = companySettingService.getMaxCode(prefix, escapePrefix, companyId, cmd.getType(), cmd.getSerialLen(), cmd.getFieldCode());
                datum.put(cmd.getFieldCode(), code);
            }
        } catch (BusinessException ex) {
            log.error("generate serial number error, {}", ex.getMessage(), ex);
            throw ex;
        } catch (Exception e) {
            log.error("generate serial number error, {}", e.getMessage(), e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "流水号生成异常");
        } /*finally {
            // 生成结束清理缓存
            if (CollUtil.isNotEmpty(prefixSet)) {
                prefixSet.forEach(f -> companySettingService.cleanCache(f, companyId, cmd.getType(), cmd.getSerialLen(), cmd.getFieldCode()));
            }
        }*/
        return cmd.getData();
    }

    @Override
    public void afterPropertiesSet() {
        TIMES.add(YY);
        TIMES.add(YYYY);
        TIMES.add(YY_MM);
        TIMES.add(YY_MM_DD);
        TIMES.add(YY_MM_DD_HH);
        TIMES.add(YY_MM_DD_HH_MM);
        TIMES.add(YY_MM_DD_HH_MM_SS);
        BIZS.add(COMPANY_OWNER);
        BIZS.add(ORG_OWNER);
        BIZS.add(AREA);
        BIZS.add(ASSET_CATE);
        BIZS.add(MATERIAL_CATE);
        BIZS.add(PRIMARY_AREA);
        BIZS.add(PRIMARY_ASSET_CATE);
        BIZS.add(BUY_TIME_YY);
        BIZS.add(BUY_TIME_YYYY);
        BIZS.add(BUY_TIME_YY_MM);
        BIZS.add(BUY_TIME_YY_MM_DD);
        BIZS.add(PRIMARY_MATERIAL_CATE);
        this.load();
    }

    private static final String YY = "yy";
    private static final String YY_MM = "yyMM";
    private static final String YY_MM_DD = "yyMMdd";
    private static final String YY_MM_DD_HH = "yyMMddHH";
    private static final String YY_MM_DD_HH_MM = "yyMMddHHmm";
    private static final String YY_MM_DD_HH_MM_SS = "yyMMddHHmmss";
    private static final String COMPANY_OWNER = "companyOwner";
    private static final String ORG_OWNER = "orgOwner";
    private static final String AREA = "area";
    private static final String ASSET_CATE = "assetCate";
    private static final String MATERIAL_CATE = "materialCate";
    private static final String FIXED_CHAR = "fixedChar";
    private static final String PRIMARY = "primary";
    private static final String PRIMARY_AREA = "primaryArea";
    private static final String PRIMARY_ASSET_CATE = "primaryAssetCate";
    private static final String YYYY = "yyyy";
    private static final String BUY_TIME = "buyTime";
    private static final String BUY_TIME_YY = "buyTime-yy";
    private static final String BUY_TIME_YYYY = "buyTime-yyyy";
    private static final String BUY_TIME_YY_MM = "buyTime-yyMM";
    private static final String BUY_TIME_YY_MM_DD = "buyTime-yyMMdd";
    private static final String PRIMARY_MATERIAL_CATE = "primaryMaterialCate";

    private static final Set<String> TIMES = new HashSet<>(8);
    private static final Set<String> BIZS = new HashSet<>(8);

    private static final Map<String, Function<Long, String>> TIME_SUPPLIERS = new ConcurrentHashMap<>(8);

    private static final Map<String, BiFunction<GenSerialNoCmd.Rule, Long, String>> BIZ_SUPPLIERS = new ConcurrentHashMap<>(8);

    private static final Map<String, Function<String, String>> BIZ_KEY_SUPPLIERS = new ConcurrentHashMap<>(8);

    /**
     * 固定字符 + 连接符 + 流水号
     */
    void load() {
        // code mapping
        BIZ_KEY_SUPPLIERS.put(FormFieldCO.YZC_ASSET_SERIALNO, code -> {
            if (COMPANY_OWNER.equals(code)) {
                return "orgOwner";
            }
            if (ORG_OWNER.equals(code)) {
                return "orgOwner";
            }
            if (AREA.equals(code) || PRIMARY_AREA.equals(code)) {
                return "storageArea";
            }
            if (ASSET_CATE.equals(code) || PRIMARY_ASSET_CATE.equals(code)) {
                return "assetCategory";
            }
            if (code.contains(BUY_TIME)) {
                return "buyTime";
            }
            return StrUtil.EMPTY;
        });
        BIZ_KEY_SUPPLIERS.put(FormFieldCO.YZC_MATERIAL_SERIALNO, code -> {
            if (MATERIAL_CATE.equals(code) || PRIMARY_MATERIAL_CATE.equals(code)) {
                return "materialCategory";
            }
            return StrUtil.EMPTY;
        });

        // timestamp format 时间戳格式化为空时默认取当前时间
        Arrays.asList(YY, YYYY, YY_MM, YY_MM_DD, YY_MM_DD_HH, YY_MM_DD_HH_MM, YY_MM_DD_HH_MM_SS).forEach(v -> TIME_SUPPLIERS.put(v, timestamp -> {
            LocalDateTime time = Objects.isNull(timestamp) ? LocalDateTime.now() : LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            return time.format(DateTimeFormatter.ofPattern(v));
        }));

        // biz
        BIZ_SUPPLIERS.put(COMPANY_OWNER, (rule, companyId) -> {
            AsOrg org = orgService.getById(rule.getId());
            if (ObjectUtil.isNotEmpty(org)) {
                if (org.getOrgType().equals(1)) {
                    return org.getOrgCode();
                } else {
                    AsOrg companyOwner = orgService.getById(org.getCompanyOwner());
                    if (ObjectUtil.isNotEmpty(companyOwner)) {
                        return companyOwner.getOrgCode();
                    }
                }
            }
            return StrUtil.EMPTY;
        });
        // get biz code
        Arrays.asList(ORG_OWNER, AREA, ASSET_CATE, MATERIAL_CATE).forEach(v -> BIZ_SUPPLIERS.put(v, (rule, companyId) -> companySettingService.getBizCodeForGenSerialNo(rule.getId(), v, companyId)));
        // get biz primary code
        Arrays.asList(PRIMARY_AREA, PRIMARY_ASSET_CATE, PRIMARY_MATERIAL_CATE).forEach(v -> BIZ_SUPPLIERS.put(v, (rule, companyId) -> {
            String type = v.replace(PRIMARY, "");
            Long treePrimaryNodeId = companySettingService.getTreePrimaryNodeId(rule.getId(), type);
            return companySettingService.getBizCodeForGenSerialNo(treePrimaryNodeId, type, companyId);
        }));
        // asset timestamp format
        Arrays.asList(BUY_TIME_YY, BUY_TIME_YYYY, BUY_TIME_YY_MM, BUY_TIME_YY_MM_DD).forEach(v -> BIZ_SUPPLIERS.put(v, (rule, companyId) -> {
            String key = v.replace(BUY_TIME + "-", "");
            return TIME_SUPPLIERS.get(key).apply(rule.getId());
        }));
    }

}
