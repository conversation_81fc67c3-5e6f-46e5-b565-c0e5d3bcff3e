package com.niimbot.asset.system.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsUserSensitiveAuthorityMapper;
import com.niimbot.asset.system.model.AsUserSensitiveAuthority;
import com.niimbot.asset.system.service.SensitiveFieldConfigService;
import com.niimbot.asset.system.service.UserSensitiveAuthorityService;
import com.niimbot.system.SensitiveFieldItemDto;
import com.niimbot.system.SensitivePermissionDto;
import com.niimbot.system.UserSensitivePermissionDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/21 下午4:39
 */
@Slf4j
@Service
public class UserSensitiveAuthorityServiceImpl extends ServiceImpl<AsUserSensitiveAuthorityMapper, AsUserSensitiveAuthority> implements UserSensitiveAuthorityService {

    @Autowired
    private SensitiveFieldConfigService sensitiveFieldConfigService;
    @Autowired
    private RedisService redisService;

    @Override
    public List<SensitivePermissionDto> getByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }

        //先从缓存里面获取
        List<AsUserSensitiveAuthority> userSensitiveAuthorityList = null;
        if (Objects.nonNull(redisService.get(userSensitiveKey(LoginUserThreadLocal.getCompanyId(), userId)))) {
            userSensitiveAuthorityList = (List<AsUserSensitiveAuthority>) redisService.get(userSensitiveKey(LoginUserThreadLocal.getCompanyId(), userId));
        } else {
            //缓存没有从数据库获取
            userSensitiveAuthorityList = this.list(Wrappers.lambdaQuery(AsUserSensitiveAuthority.class)
                    .eq(AsUserSensitiveAuthority::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsUserSensitiveAuthority::getUserId, userId));
            //设置下redis缓存
            if (CollUtil.isNotEmpty(userSensitiveAuthorityList)) {
                redisService.set(userSensitiveKey(LoginUserThreadLocal.getCompanyId(), userId), userSensitiveAuthorityList, 3, TimeUnit.HOURS);
            } else {
                //设置下redis缓存
                redisService.set(userSensitiveKey(LoginUserThreadLocal.getCompanyId(), userId), Collections.emptyList(), 3, TimeUnit.HOURS);
            }
        }
        if (CollUtil.isNotEmpty(userSensitiveAuthorityList)) {
            return userSensitiveAuthorityList.stream().map(item -> {
                SensitivePermissionDto data = new SensitivePermissionDto().setCode(item.getConfigCode());
                data.setFieldCode(item.getAuthorityData().stream().map(SensitiveFieldItemDto::getCode).collect(Collectors.toList()));
                return data;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<UserSensitivePermissionDto> getSensitivePerm(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }

        //先从缓存里面获取
        List<AsUserSensitiveAuthority> userSensitiveAuthorityList;
        String key = userSensitiveKey(LoginUserThreadLocal.getCompanyId(), userId);
        if (redisService.hasKey(key)) {
            userSensitiveAuthorityList = (List<AsUserSensitiveAuthority>) redisService.get(key);
        } else {
            //缓存没有从数据库获取
            userSensitiveAuthorityList = this.list(Wrappers.lambdaQuery(AsUserSensitiveAuthority.class)
                    .eq(AsUserSensitiveAuthority::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsUserSensitiveAuthority::getUserId, userId));
            //设置下redis缓存
            if (CollUtil.isNotEmpty(userSensitiveAuthorityList)) {
                redisService.set(key, userSensitiveAuthorityList, 3, TimeUnit.HOURS);
            } else {
                //设置下redis缓存
                redisService.set(key, Collections.emptyList(), 3, TimeUnit.HOURS);
            }
        }
        if (CollUtil.isNotEmpty(userSensitiveAuthorityList)) {
            return userSensitiveAuthorityList.stream().map(item -> {
                UserSensitivePermissionDto sensitivePermissionDto = new UserSensitivePermissionDto();
                BeanUtils.copyProperties(item, sensitivePermissionDto);
                return sensitivePermissionDto;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public void removeByUserIds(List<Long> userIds, Long companyId) {
        this.remove(Wrappers.lambdaQuery(AsUserSensitiveAuthority.class)
                .eq(AsUserSensitiveAuthority::getCompanyId, companyId)
                .in(AsUserSensitiveAuthority::getUserId, userIds));
        // 删除redis缓存数据
        List<String> userKeyList = userIds.stream().map(userId -> userSensitiveKey(companyId, userId)).collect(Collectors.toList());
        redisService.del(userKeyList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePermission(List<Long> userIds, List<SensitivePermissionDto> sensitivePermissionDto) {
        if (CollUtil.isEmpty(userIds)) {
            return true;
        }
        Long companyId = LoginUserThreadLocal.getCompanyId();
//        List<String> userKeyList = userIds.stream().map(userId -> userSensitiveKey(companyId, userId)).collect(Collectors.toList());
//        if (CollUtil.isEmpty(sensitivePermissionDto)) {
//            如果敏感数据权限为空，需要清空redis缓存数据
//            redisService.del(userKeyList);
//        }

        List<AsUserSensitiveAuthority> userSensitiveAuthorityList = new ArrayList<>();
        for (SensitivePermissionDto item : sensitivePermissionDto) {
            if (StrUtil.isBlank(item.getCode()) || CollUtil.isEmpty(item.getFieldCode())) {
                continue;
            }

            try {
                //获取敏感字段配置
                List<SensitiveFieldItemDto> sensitiveFieldItemDtoList = sensitiveFieldConfigService.getSensitiveFieldByCode(item.getCode());
                if (CollUtil.isEmpty(sensitiveFieldItemDtoList)) {
                    continue;
                }

                //按敏感字段编码分组
                Map<String, SensitiveFieldItemDto> sensitiveFieldItemDtoMap = sensitiveFieldItemDtoList.stream().collect(Collectors.toMap(SensitiveFieldItemDto::getCode, value -> value, (v1, v2) -> v1));

                //敏感字段
                List<SensitiveFieldItemDto> sensitiveFieldItemDtos = new ArrayList<>();
                for (String fieldCode : item.getFieldCode()) {
                    //敏感字段配置中没有的字段，直接跳过
                    if (Objects.isNull(sensitiveFieldItemDtoMap.get(fieldCode))) {
                        continue;
                    }

                    //设置字段编码-名称-类型
                    SensitiveFieldItemDto sensitiveFieldItemDto = new SensitiveFieldItemDto()
                            .setCode(fieldCode)
                            .setName(sensitiveFieldItemDtoMap.get(fieldCode).getName())
                            .setType(sensitiveFieldItemDtoMap.get(fieldCode).getType());
                    sensitiveFieldItemDtos.add(sensitiveFieldItemDto);
                }

                //敏感字段不为空，保存到数据库
                if (CollUtil.isNotEmpty(sensitiveFieldItemDtos)) {
                    AsUserSensitiveAuthority userSensitiveAuthority = new AsUserSensitiveAuthority()
                            .setConfigCode(item.getCode())
                            .setAuthorityData(sensitiveFieldItemDtos);
                    userSensitiveAuthorityList.add(userSensitiveAuthority);
                }
            } catch (ExecutionException e) {
                log.error("userSensitiveAuthorityService savePermission fieldCache exception ", e);
                return Boolean.FALSE;
            }
        }

        //敏感数据权限写入缓存
        if (CollUtil.isNotEmpty(userSensitiveAuthorityList)) {
            // userIds
            List<List<Long>> partition = Lists.partition(userIds, 200);
            for (List<Long> userIdList : partition) {
                List<AsUserSensitiveAuthority> saveList = new ArrayList<>();
                for (Long userId : userIdList) {
                    saveList.addAll(userSensitiveAuthorityList.stream().map(f -> {
                        AsUserSensitiveAuthority authority = BeanUtil.copyProperties(f, AsUserSensitiveAuthority.class);
                        authority.setId(IdUtils.getId())
                                .setCompanyId(companyId)
                                .setUserId(userId);
                        return authority;
                    }).collect(Collectors.toList()));
                }
                saveBatch(saveList);
            }
        }
//        else {
            //如果敏感数据权限为空，需要清空redis缓存数据
//            redisService.del(userKeyList);
//        }
        return Boolean.TRUE;
    }

    /**
     * 用户敏感数据权限key
     * @param companyId
     * @param userId
     * @return
     */
    private String userSensitiveKey(Long companyId, Long userId) {
        return String.join(":", "user_sensitive_perm", String.valueOf(companyId), String.valueOf(userId));
    }
}
