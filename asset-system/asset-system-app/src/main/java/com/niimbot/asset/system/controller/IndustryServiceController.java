package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.niimbot.asset.system.model.AsIndustry;
import com.niimbot.asset.system.service.IndustryService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2020/11/2
 */
@RestController
@RequestMapping("server/system/industry")
public class IndustryServiceController {

    private final IndustryService industryService;

    public IndustryServiceController(IndustryService industryService) {
        this.industryService = industryService;
    }

    @PostMapping
    public Boolean industryAdd(@RequestBody AsIndustry industry) {
        return this.industryService.add(industry);
    }

    @PutMapping
    public Boolean edit(@RequestBody AsIndustry industry) {
        return this.industryService.edit(industry);
    }

    @PutMapping("/sort")
    public Boolean sort(@RequestBody List<Long> industryIds) {
        return this.industryService.sort(industryIds);
    }

    @GetMapping(value = "/{industryId}")
    public AsIndustry industryGetInfo(@PathVariable(value = "industryId") Long industryId) {
        return industryService.getById(industryId);
    }

    @GetMapping(value = "/list")
    public List<AsIndustry> list() {
        return industryService.list(new QueryWrapper<AsIndustry>().lambda()
                .orderByAsc(AsIndustry::getSortNum)
                .orderByAsc(AsIndustry::getCreateTime));
    }

    @DeleteMapping
    public Boolean remove(@RequestBody List<Long> industryIds) {
        return industryService.delete(industryIds);
    }
}
