package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsToolbox;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.ToolboxGroupItemDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 工具箱表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsToolboxMapper.class)
public interface AsToolboxMapper extends BaseMapper<AsToolbox> {
    /**
     * 更新排序
     *
     * @param list 列表
     * @return 更新数量
     */
    int sort(List<AsToolbox> list);

    /**
     * 查询功能菜单
     * @param businessType
     * @return
     */
    List<AsToolbox> selectToolbox(@Param("businessType") Integer businessType);

    /**
     * 查询工作台
     * @return
     */
    List<ToolboxGroupItemDto> selectWorkbench(@Param("businessType") Integer businessType);
}
