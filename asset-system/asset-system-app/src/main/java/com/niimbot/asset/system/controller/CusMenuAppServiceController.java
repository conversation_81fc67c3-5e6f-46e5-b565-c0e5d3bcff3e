package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.model.AsCusMenuApp;
import com.niimbot.asset.system.service.CusMenuAppService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/10/27 14:42
 */
@RestController
@RequestMapping("server/system/cusMenu")
public class CusMenuAppServiceController {

    private final CusMenuAppService cusMenuAppService;

    public CusMenuAppServiceController(CusMenuAppService cusMenuAppService) {
        this.cusMenuAppService = cusMenuAppService;
    }

    @PostMapping("/app")
    public Boolean add(@RequestBody AsCusMenuApp menuDto) {
        return cusMenuAppService.addMenu(menuDto);
    }

    @PutMapping("/app")
    public Boolean edit(@RequestBody AsCusMenuApp menuDto) {
        return cusMenuAppService.updateMenu(menuDto);
    }

    @DeleteMapping("/app")
    public Boolean remove(@RequestBody List<Long> menuIds) {
        return cusMenuAppService.deleteList(menuIds);
    }

    @PostMapping(value = "/sort/app")
    public Boolean sortBeApp(@RequestBody List<Long> ids) {
        return cusMenuAppService.sort(ids);
    }

    @GetMapping("/app/{id}")
    public AsCusMenuApp pcMenuDetail(@PathVariable("id") Long id) {
        return cusMenuAppService.getById(id);
    }
}
