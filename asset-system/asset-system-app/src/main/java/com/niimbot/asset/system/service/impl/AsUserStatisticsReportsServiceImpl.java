package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsUserStatisticsReportsMapper;
import com.niimbot.asset.system.model.AsUserStatisticsReports;
import com.niimbot.asset.system.service.AsUserStatisticsReportsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户活跃状态数量定时统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Service
public class AsUserStatisticsReportsServiceImpl extends ServiceImpl<AsUserStatisticsReportsMapper, AsUserStatisticsReports> implements AsUserStatisticsReportsService {

}
