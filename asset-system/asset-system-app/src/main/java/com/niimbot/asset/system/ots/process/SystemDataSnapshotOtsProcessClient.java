package com.niimbot.asset.system.ots.process;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.system.dto.DataSnapshotGetQry;
import com.niimbot.asset.system.dto.DataSourceIdsGetQry;
import com.niimbot.asset.system.ots.SystemDataSnapshotOts;
import com.niimbot.asset.system.service.DataSnapshotService;
import com.niimbot.system.DataSnapshotSave;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/SystemDataSnapshotOts/")
@RequiredArgsConstructor
public class SystemDataSnapshotOtsProcessClient implements SystemDataSnapshotOts {

    private final DataSnapshotService dataSnapshotService;

    @Override
    public void saveSnapshot(DataSnapshotSave cmd) {
        dataSnapshotService.saveSnapshots(cmd);
    }

    @Override
    public List<Long> getSourceIds(DataSourceIdsGetQry qry) {
        return dataSnapshotService.getSourceIds(qry);
    }

    @Override
    public List<String> getSnapshotIds(DataSnapshotGetQry qry) {
        return dataSnapshotService.getSnapshotIds(qry);
    }

    @Override
    public List<JSONObject> getSnapshots(DataSnapshotGetQry qry) {
        return dataSnapshotService.getSnapshots(qry);
    }
}
