package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.NewbieGuideService;
import com.niimbot.system.NewbieGuideDto;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/server/system/newbie/guide")
public class NewbieGuideServiceController {

    @Resource
    private NewbieGuideService newbieGuideService;

    @PutMapping("/click/{id}")
    public Boolean click(@PathVariable("id") Long id) {
        return newbieGuideService.click(id);
    }

    @GetMapping("/all")
    public List<NewbieGuideDto> allGuide() {
        return newbieGuideService.allGuide();
    }

}
