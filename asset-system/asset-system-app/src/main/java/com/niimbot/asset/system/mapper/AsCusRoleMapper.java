package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.CusRoleAccountDto;
import com.niimbot.system.CusRoleAccountQueryDto;
import com.niimbot.system.CusRoleDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 客户角色 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@EnableDataPerm(excludeMethodName = {"listByCompanyId", "getRoleByEmployeeId", "listUserByRoleId", "getAdministratorByCompanyId"})
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsCusRoleMapper.class)
public interface AsCusRoleMapper extends BaseMapper<AsCusRole> {

    @Select("SELECT * FROM as_cus_role WHERE role_code = 'admin' AND company_id = #{companyId}")
    AsCusRole getAdministratorByCompanyId(@Param("companyId") Long companyId);

    /**
     * 查询公司角色信息
     *
     * @param companyId 公司id
     * @return 角色信息列表
     */
    @Select("select id,role_name,role_code,status,company_id from as_cus_role where company_id = #{companyId} " +
            "and is_delete = 0 and status = 1")
    List<AsCusRole> listByCompanyId(Long companyId);

    /**
     * 查询获取角色结构
     *
     * @param employeeId 用户ID
     * @return CusRoleDto信息
     */
    List<CusRoleDto> getRoleByEmployeeId(@Param("employeeId") Long employeeId);

    /**
     * 根据角色Id查询用户
     *
     * @param roleId    用户Id
     * @param companyId 公司id
     * @return 用户信息
     */
    List<AsCusUser> listUserByRoleId(@Param("roleId") Long roleId, @Param("companyId") Long companyId);

    /**
     * 查询角色账户列表list集合
     *
     * @return 角色账户列表
     */
    // 企业微信兼容，员工搜索
    List<CusRoleAccountDto> roleAccountList(@Param("ew") CusRoleAccountQueryDto roleAccountQueryDto, @Param("unionIds") List<String> unionIds);

    default AsCusRole selectByRoleId(@Param("roleId") Long roleId, @Param("companyId") Long companyId) {
        return selectOne(Wrappers.lambdaQuery(AsCusRole.class)
                .eq(AsCusRole::getId, roleId)
                .eq(AsCusRole::getCompanyId, companyId)
                .eq(AsCusRole::getStatus, 1));
    }

    /**
     * 条件查询角色信息
     * @param role
     * @return
     */
    List<AsCusRole> selectByCondition(@Param("param") AsCusRole role);
}
