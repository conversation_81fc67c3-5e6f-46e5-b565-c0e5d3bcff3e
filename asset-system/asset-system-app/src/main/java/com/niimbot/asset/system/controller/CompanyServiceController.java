package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.AsIndustry;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.IndustryService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CompanyDto;
import com.niimbot.system.CompanySettingDto;
import com.niimbot.system.SalesOwnerDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/10/20 12:31
 */
@RestController
@RequestMapping("server/system/company")
@Slf4j
public class CompanyServiceController {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IndustryService industryService;

    @Autowired
    private CompanySettingService settingService;

    @GetMapping("/setting/{companyId}")
    public CompanySettingDto companySettingDetails(@PathVariable("companyId") Long id) {
        AsCompanySetting setting = settingService.getById(id);
        return BeanUtil.copyProperties(setting, CompanySettingDto.class);
    }

    /**
     * 新增会员
     *
     * @param company
     * @return
     */
    @PostMapping
    public Boolean add(@RequestBody AsCompany company) {
        if (!companyService.save(company)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }

    /**
     * 更新会员行业信息
     *
     * @param industryId
     * @return
     */
    @PutMapping("/industry/{industryId}")
    public Boolean changeIndustry(@PathVariable Long industryId) {
        // 根据行业id查询行业名称
        AsIndustry byId = industryService.getById(industryId);
        return companyService.update(Wrappers.<AsCompany>lambdaUpdate()
                .set(AsCompany::getIndustryId, industryId)
                .set(AsCompany::getIndustryName, byId.getIndustryName())
                .eq(AsCompany::getId, LoginUserThreadLocal.getCompanyId()));
    }

    /**
     * 更新会员logo
     *
     * @param logo logo
     */
    @GetMapping(value = "/changeLogo")
    public Boolean changeLogo(@RequestParam String logo) {
        return companyService.update(Wrappers.<AsCompany>lambdaUpdate().set(AsCompany::getLogo, logo)
                .eq(AsCompany::getId, LoginUserThreadLocal.getCompanyId()));
    }

    /**
     * 根据id查询会员信息
     *
     * @param companyId
     * @return
     */
    @GetMapping("/{companyId}")
    public CompanyDto getCompanyById(@PathVariable("companyId") Long companyId) {
        AsCompany company = companyService.getById(companyId);
        CompanyDto companyDto = BeanUtil.copyProperties(company, CompanyDto.class);
        AsCompanySetting setting = settingService.getById(companyId);
        companyDto.setIsPay(setting.getIsPay());
        return companyDto;
    }

    @PutMapping("/enableIdleAsset/{enable}")
    public Boolean modifyEnableIdleAsset(@PathVariable("enable") Boolean enable) {
        return settingService.enableIdleAsset(LoginUserThreadLocal.getCompanyId(), enable);
    }

    @PutMapping("/enableSyncApproveRole/{enable}")
    public Boolean enableSyncApproveRole(@PathVariable("enable") Boolean enable) {
        return settingService.enableSyncRole(LoginUserThreadLocal.getCompanyId(), enable);
    }

    @GetMapping("/getIdleAssetSwitch/{companyId}")
    public Boolean getIdleAssetSwitch(@PathVariable("companyId") Long companyId) {
        AsCompanySetting setting = settingService.getById(companyId);
        if (Objects.isNull(setting)) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        return Objects.nonNull(setting.getExpandSwitch()) ? setting.getExpandSwitch().getEnableIdleAsset() : false;
    }

    @PutMapping("/configDefaultDataAuth/{companyId}")
    public Boolean configureDefaultDataAuth(@RequestBody List<AsDataAuthority> dataAuthorities, @PathVariable("companyId") Long companyId) {
        return settingService.configDefaultDataAuth(companyId, dataAuthorities);
    }

    /**
     * 修改企业名称
     *
     * @param name name
     * @return 成功与否
     */
    @PutMapping(value = "/changeCompanyName")
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeCompanyName(@RequestParam String name) {
        return companyService.changeCompanyName(name);
    }

    @GetMapping(value = "/getSalesOwner")
    public SalesOwnerDto getSalesOwner() {
        return companyService.getSalesOwner();
    }
}
