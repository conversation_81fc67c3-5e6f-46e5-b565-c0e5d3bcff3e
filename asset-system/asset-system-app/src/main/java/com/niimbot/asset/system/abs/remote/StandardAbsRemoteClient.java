package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.StandardAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 17:54
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.StandardAbsImpl")
@FeignClient(name = "asset-means", url = "https://{gateway}/client/abs/means/standardAbs/")
public interface StandardAbsRemoteClient extends StandardAbs {
}
