package com.niimbot.asset.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.openapi.dto.OpenApiMessageSendDto;
import com.niimbot.asset.system.mapper.AsOpenApiEventLogMapper;
import com.niimbot.asset.system.model.AsOpenApi;
import com.niimbot.asset.system.model.AsOpenApiEventLog;
import com.niimbot.asset.system.service.AsOpenApiEventLogService;
import com.niimbot.asset.system.service.AsOpenApiService;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/18 11:09
 */
@Service
@RequiredArgsConstructor
public class AsOpenApiEventLogServiceImpl extends ServiceImpl<AsOpenApiEventLogMapper, AsOpenApiEventLog> implements AsOpenApiEventLogService {

    private final AsOpenApiService openApiService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    public void sendBatch(Map<Long, List<OpenApiMessageSendDto>> sendMap) {
        taskExecutor.execute(() -> {
            Map<String, List<AsOpenApi>> cmpOpenApiMap = new HashMap<>();
            List<AsOpenApiEventLog> eventLogList = new ArrayList<>();
            sendMap.forEach((cmpId, messageSendList) -> {
                List<AsOpenApi> openApiList;
                String cmpIdStr = Convert.toStr(cmpId);
                if (cmpOpenApiMap.containsKey(cmpIdStr)) {
                    openApiList = cmpOpenApiMap.get(cmpIdStr);
                } else {
                    openApiList = openApiService.listByCompanyId(cmpId);
                    cmpOpenApiMap.put(cmpIdStr, openApiList);
                }

                for (OpenApiMessageSendDto sendDto : messageSendList) {
                    for (AsOpenApi openApi : openApiList) {
                        // 必须要包含回调函数，token和签名
                        if (StrUtil.isAllNotEmpty(openApi.getCallback(), openApi.getToken(), openApi.getAesKey())) {
                            AsOpenApiEventLog eventLog = new AsOpenApiEventLog();
                            eventLog.setEventType("message")
                                    .setCompanyId(cmpId)
                                    .setApiId(openApi.getApiId())
                                    .setApiKey(openApi.getAppKey())
                                    .setSourceData(JSONObject.toJSONString(sendDto))
                                    .setStatus(0)
                                    .setFailCount(0);
                            eventLogList.add(eventLog);
                        }
                    }
                }
            });
            // 这里不开启事务
            saveBatch(eventLogList);
        });
    }

}
