package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsNavigation;
import com.niimbot.asset.system.service.AsNavigationService;
import com.niimbot.jf.core.exception.category.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 导航栏控制器
 *
 * <AUTHOR>
 * @date 2020/12/9 上午11:52
 */
@RestController
@RequestMapping("server/system/navigation")
@Slf4j
@Validated
public class AsNavigationController {
    private final AsNavigationService asNavigationService;

    @Autowired
    public AsNavigationController(AsNavigationService asNavigationService) {
        this.asNavigationService = asNavigationService;
    }

    /**
     * 导航编辑
     * @param navigation
     * @return
     */
    @PutMapping
    public Boolean edit(@RequestBody AsNavigation navigation) {
        if (!asNavigationService.updateById(navigation)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 批量编辑
     * @param list
     * @return
     */
    @PutMapping("/batch")
    public Boolean editBatch(@RequestBody @NotEmpty List<AsNavigation> list) {
        AtomicInteger index = new AtomicInteger(1);
        list.forEach(navigation -> navigation.setSortNum(index.getAndIncrement()));
        if (!asNavigationService.updateBatchById(list)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 是否展示开关
     * @param list
     * @return
     */
    @PutMapping("/enable")
    public Boolean editEnable(@RequestBody @NotEmpty List<AsNavigation> list) {
        if (!asNavigationService.updateBatchById(list)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 导航列表
     * @param navigation
     * @return
     */
    @GetMapping(value = "/list")
    public List<AsNavigation> list(AsNavigation navigation) {
        return asNavigationService.list(
                Wrappers.lambdaQuery(navigation)
                        .orderByAsc(AsNavigation::getSortNum));
    }
}
