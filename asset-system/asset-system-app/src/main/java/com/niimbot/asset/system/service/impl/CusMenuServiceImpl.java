package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.mapper.AsCusMenuMapper;
import com.niimbot.asset.system.model.AsCusMenu;
import com.niimbot.asset.system.service.CusMenuService;
import com.niimbot.system.AppCusMenuDto;
import com.niimbot.system.CusMenuDto;

import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/27 14:33
 */
@Service
@RequiredArgsConstructor
public class CusMenuServiceImpl extends ServiceImpl<AsCusMenuMapper, AsCusMenu> implements CusMenuService {

    @Override
    public List<CusMenuDto> configRoleMenuPcList() {
        return this.getBaseMapper().configRoleModulePcList()
                .stream()
                .sorted(Comparator.comparing(CusMenuDto::getOrderNum))
                .collect(Collectors.toList());
    }

    @Override
    public List<CusMenuDto> configRoleMenuAppList() {
        return this.getBaseMapper().configRoleModuleAppList()
                .stream()
                .sorted(Comparator.comparing(CusMenuDto::getOrderNum))
                .collect(Collectors.toList());
    }

    @Override
    public List<CusMenuDto> userMenuPcList() {
        CusUserDto user = LoginUserThreadLocal.getCusUser();
        if (user == null) {
            log.warn("user is null");
            return ListUtil.empty();
        }
        List<CusMenuDto> pcMenus = allPcMenu();
        // 超级管理员直接返回全部数据
        if (BooleanUtil.isTrue(user.getIsAdmin())) {
            return pcMenus;
        } else {
            // 其他角色过滤筛选
            List<Long> pcMenusIds = pcMenus.stream().map(CusMenuDto::getId).collect(Collectors.toList());
            return this.baseMapper.userRoleMenuPcList(user.getId(), pcMenusIds);
        }
    }

    @Override
    public AppCusMenuDto userMenuAppList() {
        CusUserDto user = LoginUserThreadLocal.getCusUser();
        if (user == null) {
            log.warn("user is null");
            return new AppCusMenuDto();
        }
        List<CusMenuDto> appMenus = allAppMenu();
        List<String> codes = appMenus.stream().map(CusMenuDto::getMenuCode).collect(Collectors.toList());

        // 超级管理员直接返回全部数据
        if (BooleanUtil.isTrue(user.getIsAdmin())) {
            return new AppCusMenuDto()
                    .setAllMenus(codes)
                    .setMenus(appMenus);
        } else {
            // 其他角色过滤筛选
            List<Long> appMenusIds = appMenus.stream().map(CusMenuDto::getId).collect(Collectors.toList());
            return new AppCusMenuDto()
                    .setAllMenus(codes)
                    .setMenus(this.baseMapper.userRoleMenuAppList(user.getId(), appMenusIds));
        }
    }

    @Override
    public List<CusMenuDto> allPcMenu() {
        List<CusMenuDto> modulePcList = this.getBaseMapper().modulePcList();
        LinkedHashSet<CusMenuDto> all = new LinkedHashSet<>(modulePcList);
        return all.stream()
                .sorted(Comparator.comparing(CusMenuDto::getOrderNum))
                .collect(Collectors.toList());
    }

    @Override
    public List<CusMenuDto> allAppMenu() {
        List<CusMenuDto> moduleAppList = this.getBaseMapper().moduleAppList();
        LinkedHashSet<CusMenuDto> all = new LinkedHashSet<>(moduleAppList);
        return all.stream()
                .sorted(Comparator.comparing(CusMenuDto::getOrderNum))
                .collect(Collectors.toList());
    }

    @Override
    public Set<String> selectMenuPermsByUserId(Long userId) {
        List<String> perms = this.getBaseMapper().selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        perms.forEach(perm -> {
            if (StrUtil.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        });
        return permsSet;
    }
}
