package com.niimbot.asset.system.controller;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsSyncChange;
import com.niimbot.asset.system.service.SyncChangeService;
import com.niimbot.system.RemoveEmployDto;
import com.niimbot.thirdparty.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/19 17:39
 */
@RestController
@RequestMapping("server/thirdparty/syncChange")
public class SyncChangeServiceController {

    private final SyncChangeService syncChangeService;

    @Autowired
    public SyncChangeServiceController(SyncChangeService syncChangeService) {
        this.syncChangeService = syncChangeService;
    }

    @GetMapping("/list")
    public List<SyncChangeDto> list(@RequestParam("type") Integer type, @RequestParam("status") Integer status) {
        return syncChangeService.listChange(type, status);
    }

    @GetMapping("/emp/{id}")
    public SyncChangeEmpDto getEmp(@PathVariable("id") Long id) {
        return syncChangeService.getEmp(id);
    }

    @GetMapping("/org/{id}")
    public AsOrg getOrg(@PathVariable("id") Long id) {
        return syncChangeService.getOrg(id);
    }

    @PostMapping("/emp/transfer/edit")
    public Boolean transferEmpEdit(@RequestBody EmpTransferDto empTransferDto) {
        return syncChangeService.transferEmpEdit(empTransferDto);
    }

    @PostMapping("/emp/transfer/remove/{id}")
    public Boolean transferEmpDelete(@PathVariable("id") Long id, @RequestBody RemoveEmployDto employ) {
        return syncChangeService.transferEmpDelete(id, employ);
    }

    @PostMapping("/org/transfer/remove")
    public Boolean transferOrgDelete(@RequestBody OrgTransferDto orgTransferDto) {
        return syncChangeService.transferOrgDelete(orgTransferDto);
    }

    @GetMapping("/count")
    public SyncChangeCountDto count() {
        if (LoginUserThreadLocal.getCusUser().getIsAdmin()) {
            List<AsSyncChange> syncChanges = syncChangeService.list(
                    new LambdaQueryWrapper<AsSyncChange>().eq(AsSyncChange::getStatus, 1));
            int emp = 0;
            int org = 0;
            int minTime = 0;
            for (AsSyncChange syncChange : syncChanges) {
                Integer type = syncChange.getType();
                if (ListUtil.of(1, 2).contains(type)) {
                    emp++;
                } else if (type == 3) {
                    org++;
                }
            }
            return new SyncChangeCountDto().setEmp(emp).setOrg(org).setTime(minTime);
        } else {
            return new SyncChangeCountDto().setEmp(0).setOrg(0).setTime(0);
        }
    }

}
