package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.system.model.AsEmployeeChange;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.system.CusEmployeeChangeDto;
import com.niimbot.system.CusEmployeeChangeQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 员工异动记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@EnableDataPerm(excludeMethodName = {"getPage"})
public interface AsEmployeeChangeMapper extends BaseMapper<AsEmployeeChange> {

    // 企业微信兼容，员工搜索
    IPage<CusEmployeeChangeDto> getPage(@Param("page") Page<Object> buildIPage,
                                        @Param("ew") CusEmployeeChangeQueryDto query,
                                        @Param("companyId") Long companyId,
                                        @Param("unionIds") List<String> unionIds);
}
