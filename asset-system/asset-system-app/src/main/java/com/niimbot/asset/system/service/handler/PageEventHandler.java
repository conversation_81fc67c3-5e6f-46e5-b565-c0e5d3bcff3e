package com.niimbot.asset.system.service.handler;

import com.niimbot.asset.framework.utils.thread.AssetThreadPoolExecutorManager;
import com.niimbot.asset.system.event.PageEventQueue;
import com.niimbot.asset.system.model.AsModuleStatistics;
import com.niimbot.asset.system.service.ModuleStatisticsService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

/**
 * 消费处理事件
 *
 * <AUTHOR>
 * @date 2023/4/11 下午2:58
 */
@Slf4j
@Component
public class PageEventHandler implements CommandLineRunner {

    @Autowired
    private ModuleStatisticsService moduleStatisticsService;

    private Integer eventCount = 0;

    private int batchSize = 100;

    private List<AsModuleStatistics> pageEventList = new ArrayList<>(batchSize);

    private ThreadPoolExecutor executor;

    @PostConstruct
    public void init() {
        executor = AssetThreadPoolExecutorManager.newThreadPool("PAGE_EVENT", 1, 1, 10000);
    }

    @Override
    public void run(String... args) throws Exception {
        executor.submit(() -> {
            while(true) {
                try {
                    //阻塞队列获取事件
                    AsModuleStatistics moduleStatistics = PageEventQueue.getInstance().consumePageEvent();
                    if (Objects.isNull(moduleStatistics)) {
                        //处理历史的事件，保存落库
                        if (eventCount != 0) {
                            moduleStatisticsService.saveBatch(pageEventList);
                            reset();
                        }

                        //当前线程休息，阻塞队列没有后续事件需要进行处理
                        Thread.sleep(1000L);
                    } else {
                        //事件添加到队列
                        pageEventList.add(moduleStatistics);
                        eventCount++;

                        //事件攒批进行批量保存处理
                        if (eventCount == batchSize) {
                            moduleStatisticsService.saveBatch(pageEventList);
                            reset();
                        }
                    }
                } catch (Exception e) {
                    log.error("pageEventHandler run error! exception ", e);
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e1) {
                        log.error("pageEventHandler sleep exception");
                        Thread.currentThread().interrupt();
                    }
                    //忽略异常，继续消费
                    reset();
                }
            }
        });
    }

    /**
     * 重置消费
     */
    private void reset() {
        this.pageEventList.clear();
        this.eventCount = 0;
    }
}
