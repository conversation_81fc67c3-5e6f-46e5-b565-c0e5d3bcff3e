package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheEmpStrategy;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.ImportErrorAbs;
import com.niimbot.asset.system.dto.ImportErrorDeleteCmd;
import com.niimbot.asset.system.event.EmpInfoChangeEvent;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.model.AsThirdPartyEmployee;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.model.AsUserRole;
import com.niimbot.asset.system.service.AccountCenterService;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsCusEmployeeSettingService;
import com.niimbot.asset.system.service.AsThirdPartyEmployeeService;
import com.niimbot.asset.system.service.AsUserOrgService;
import com.niimbot.asset.system.service.CusUserRoleService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.system.CusEmployeeDto;
import com.niimbot.system.CusEmployeeOptDto;
import com.niimbot.system.CusEmployeeQueryDto;
import com.niimbot.system.EmployeeImportDto;
import com.niimbot.system.EmployeeModifyDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.OrgDto;
import com.niimbot.system.RemoveEmployDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 员工管理控制器
 *
 * <AUTHOR>
 * @Date 2020/11/12
 */
@RestController
@RequestMapping("server/system/employee")
@Slf4j
public class CusEmployeeServiceController {

    @Resource
    private AsCusEmployeeService employeeService;

    @Resource
    private OrgService orgService;

    @Resource
    private AsUserOrgService userOrgService;

    @Resource
    private ImportErrorAbs importErrorAbs;

    @Resource
    private AccountCenterService accountCenterService;

    @Resource
    private CusUserRoleService userRoleService;

    @Resource
    private AsCusEmployeeExtService employeeExtService;

    @Resource
    private AsThirdPartyEmployeeService thirdPartyEmployeeService;

    @Resource
    private AsCusEmployeeSettingService employeeSettingService;

    @Resource
    private CusUserService cusUserService;

    @Resource
    private ModelDataScopeServiceImpl modelDataScopeService;

    @PostMapping
    public Boolean save(@RequestBody CusEmployeeOptDto employeeDto) {
        return employeeService.insert(employeeDto);
    }

    @PostMapping("v2")
    public String saveV2(@RequestBody CusEmployeeOptDto employeeDto) {
        return employeeService.insertV2(employeeDto);
    }

    @PostMapping("/saveWithCompany")
    public Boolean saveWithCompany(@RequestBody AsCusEmployee employee) {
        return employeeService.insert(employee, employee.getCompanyId());
    }

    @PutMapping
    public Boolean edit(@RequestBody CusEmployeeOptDto employee) {
        // 如果当前员工的账号已激活，则手机号为必填，如果当前员工的账号未激活，则手机号为选填；
        return employeeService.edit(employee);
    }

    @PutMapping("/batchModifyOrg")
    public Boolean batchModifyOrg(@RequestBody List<CusEmployeeOptDto> employeeDtos) {
        return employeeService.batchModifyOrg(employeeDtos);
    }

    @GetMapping("/adminInfo")
    public AsCusEmployee getAdminInfo() {
        return employeeService.getAdministrator();
    }

    @GetMapping("/list/by/mobile/{mobile}")
    public List<AsCusEmployee> listByMobile(@PathVariable String mobile) {
        return employeeService.list(
                Wrappers.lambdaUpdate(AsCusEmployee.class).eq(AsCusEmployee::getMobile, mobile)
        );
    }

    @PutMapping("/transferEmp")
    public void transferEmp(@RequestBody RemoveEmployDto employ) {
        employeeService.transferEmp(employ);
    }

    @DeleteMapping
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(@RequestBody RemoveEmployDto employ) {
        AsCusEmployee exist = employeeService.getById(employ.getEmployeeId());
        if (exist == null) {
            return true;
        }
        employ.setEmployeeName(exist.getEmpName());
        // 过滤掉超管
        AsCusEmployee administrator = employeeService.getAdministrator();
        Long employeeId = employ.getEmployeeId();
        if (employeeId.equals(administrator.getId())) {
            throw new BusinessException(SystemResultCode.CANT_EDIT_ADMIN);
        }

        // 转移资产数据
        employeeService.transferEmp(employ);

        // 删除员工
        // 删除员工 删除员工扩展属性 删除员工首页布局
        employeeService.removeById(employeeId);
        if (Edition.isLocal()) {
            // 删除账号
            cusUserService.removeById(employeeId);
        }
        employeeExtService.removeById(employeeId);
        employeeSettingService.removeById(employeeId);

        // 删除组织员工
        userOrgService.remove(new LambdaQueryWrapper<AsUserOrg>().eq(AsUserOrg::getUserId, employeeId));
        // 用户的部门有修改，需要删除权限缓存
        modelDataScopeService.cleanDataScopeCache(LoginUserThreadLocal.getCompanyId(), ListUtil.of(employ.getEmployeeId()));
        // 删除角色员工
        userRoleService.remove(
                Wrappers.lambdaUpdate(AsUserRole.class)
                        .eq(AsUserRole::getUserId, employeeId)
        );

        // 解除账号绑定关系
        accountCenterService.deleteEmployeeAccount(employeeId);

        // 删除员工与第三方信息
        thirdPartyEmployeeService.remove(
                Wrappers.lambdaUpdate(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getEmployeeId, employeeId)
        );

        // 删除组织主管数据
        List<OrgDto> orgDtoList = orgService.listByDirector(employeeId, LoginUserThreadLocal.getCompanyId());
        List<AsOrg> updateOrgList = new ArrayList<>();
        for (OrgDto orgDto : orgDtoList) {
            List<Long> director = orgDto.getDirector();
            if (CollUtil.isNotEmpty(director)) {
                director.remove(employeeId);
                AsOrg org = new AsOrg();
                org.setId(orgDto.getId());
                org.setDirector(director);
                updateOrgList.add(org);
            }
        }
        orgService.updateBatchById(updateOrgList);

        return true;
    }

   /* @GetMapping(value = "/list")
    public List<CusEmployeeDto> list(CusEmployeeQueryDto dto) {
        return employeeService.listCusEmployee(dto);
    }*/

    @PostMapping("/listHasAccount")
    public List<CusEmployeeDto> listHasAccount(@RequestBody CusEmployeeQueryDto dto) {
        return employeeService.listHasAccountEmployee(dto);
    }

    @GetMapping(value = "act/list")
    public List<CusEmployeeDto> actList(CusEmployeeQueryDto dto) {
        return employeeService.listCusEmployee(dto);
    }

 /*   @GetMapping(value = "/orgEmpList")
    public List<CusEmployeeDto> orgEmpList(@RequestParam(value = "orgIds", required = false) List<Long> orgIds,
                                           @RequestParam(value = "kw", required = false) String kw) {
        // 查询所有
        return employeeService.orgEmpList(orgIds, kw);
    }*/

    @GetMapping(value = "/getDirectorByOrgId")
    public List<AsCusEmployee> getDirectorByOrgId(@RequestParam("orgId") Long orgId) {
        // 查询主管
        AsOrg org = orgService.getById(orgId);
        if (ObjectUtil.isNull(org)) {
            return null;
        }
        return employeeService.listByIds(org.getDirector());
    }

    /**
     * companyId 和 orgId必传
     *
     * @param dto 分页查询参数
     * @return pageUtils
     */
    @GetMapping(value = "/page")
    public IPage<CusEmployeeDto> page(CusEmployeeQueryDto dto) {
        return employeeService.selectCustomPage(dto.buildIPage(), dto);
    }

    /**
     * 查询员工
     *
     * @param empId 员工Id
     * @return 员工信息
     */
    @GetMapping(value = "/{empId}")
    public CusEmployeeDto getInfo(@PathVariable("empId") Long empId) {
        return employeeService.getInfo(empId);
    }

    @GetMapping("/recommendEmpNo")
    public String recommendEmpNo() {
        return employeeService.recommendEmpNo();
    }

    @GetMapping("/checkPhone")
    public AsCusEmployee checkPhone(@RequestParam String mobile) {
        return employeeService.checkPhone(mobile, LoginUserThreadLocal.getCompanyId());
    }

    @GetMapping("/checkEmail")
    public AsCusEmployee checkEmail(@RequestParam String email) {
        return employeeService.checkEmail(email, LoginUserThreadLocal.getCompanyId());
    }

    @PutMapping("/changeName")
    public Boolean changeName(String username) {
        boolean update = employeeService.update(new UpdateWrapper<AsCusEmployee>().lambda()
                .set(AsCusEmployee::getEmpName, username)
                .eq(AsCusEmployee::getId, LoginUserThreadLocal.getCurrentUserId())
        );
        if (update && StrUtil.isNotEmpty(username)) {
            SpringUtil.getBean(CacheEmpStrategy.class).evictCache(LoginUserThreadLocal.getCurrentUserId());
        }
        EventPublishHandler.publish(new EmpInfoChangeEvent(update).setAdmin(LoginUserThreadLocal.getCusUser().getIsAdmin()).setCompanyId(LoginUserThreadLocal.getCompanyId()));
        return update;
    }

    @Resource
    private CusUserService accountUserService;

    @GetMapping(value = "/changeImage")
    public Boolean changeImage(@RequestParam String image) {
        return accountUserService.update(
                Wrappers.lambdaUpdate(AsCusUser.class).set(AsCusUser::getImage, image)
                        .eq(AsCusUser::getId, LoginUserThreadLocal.getAccountId())
        );
        // return employeeService.update(Wrappers.<AsCusEmployee>lambdaUpdate().set(AsCusEmployee::getImage, image)
        //         .eq(AsCusEmployee::getId, LoginUserThreadLocal.getCurrentUserId()));
    }

    /**
     * 根据员工id集合查询员工
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/listByIds")
    public List<CusEmployeeDto> listByIds(@RequestBody List<Long> ids) {
        return employeeService.selectListByIds(ids);
    }

    /**
     * 【注册】注册校验手机号
     *
     * @param mobile 手机号
     */
    @GetMapping("/checkRegisterMobile")
    public void checkRegisterMobile(@RequestParam String mobile) {
        employeeService.checkRegisterMobile(mobile);
    }

    /**
     * 【注册】注册校验邮箱
     *
     * @param email 邮箱
     */
    @GetMapping("/checkRegisterEmail")
    public void checkRegisterEmail(@RequestParam String email) {
        employeeService.checkRegisterEmail(email);
    }

    /**
     * 获取员工推荐工号
     *
     * @return 推荐工号
     */
    @GetMapping("/getRecommendEmpNo/{companyId}")
    public String getRecommendEmpNo(@PathVariable("companyId") Long companyId) {
        return employeeService.getRecommendEmpNo(companyId);
    }

    /**
     * 二维码邀请员工-注册开通账号
     *
     * @return map
     */
    // @PostMapping("/scanInvite/openAccount")
    // public Map<String, String> inviteOpenAccount(@RequestBody CusEmpRegisterDto cusEmpRegisterDto) {
    //     return employeeService.inviteOpenAccount(cusEmpRegisterDto);
    // }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping(value = "currentUserInfo")
    public CusEmployeeDto currentUserInfo() {
        return employeeService.currentUserInfo();
    }

    @Resource
    private CusUserService userService;

    @Resource
    private AsAccountEmployeeService accountEmployeeService;

    @PutMapping("/changeEmail/{email}")
    public Boolean changeMobile(@PathVariable("email") String email) {
        // AsCusEmployee byId = employeeService.getById(LoginUserThreadLocal.getCurrentUserId());
        AsCusUser account = userService.getById(LoginUserThreadLocal.getAccountId());
        if (StringUtils.equals(email, account.getEmail())) {
            // same mobile ignore.
            return true;
        }
        // 当前企业下员工是否有此邮箱
        long count = employeeService.count(
                Wrappers.lambdaQuery(AsCusEmployee.class)
                        .eq(AsCusEmployee::getEmail, email)
        );
        if (count >= 1) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "邮箱号与企业下员工邮箱重复，请检查后重新修改");
        }
        boolean update = userService.update(
                Wrappers.lambdaUpdate(AsCusUser.class)
                        .set(AsCusUser::getEmail, email)
                        .eq(AsCusUser::getId, account.getId())
        );
        // 同步修改账号关联的所有员工邮箱
        List<AsAccountEmployee> accountEmployees = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getAccountId, account.getId())
        );
        List<Long> empIds = accountEmployees.stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toList());
        if (update && CollUtil.isNotEmpty(accountEmployees)) {
            employeeService.updateAllCompanyEmpEmail(email, empIds);
        }
        EventPublishHandler.publish(new EmpInfoChangeEvent(true).setAdmin(LoginUserThreadLocal.getCusUser().getIsAdmin()).setCompanyId(LoginUserThreadLocal.getCompanyId()));
        return update;
    }

    /**
     * 错误记录
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return employeeService.importError(taskId);
    }

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        employeeService.saveSheetHead(importErrorDto);
    }

    /**
     * 导入耗材数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody EmployeeImportDto importDto) {
        return employeeService.saveSheetData(importDto);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return importErrorAbs.deleteImportError(new ImportErrorDeleteCmd().setTaskId(taskId));
    }

    @PutMapping("/changeEmpMobile/{newMobile}")
    public Boolean changeEmpMobile(@PathVariable String newMobile) {
        Long empId = LoginUserThreadLocal.getCusUser().getId();
        Boolean flag = employeeService.changeMobile(empId, newMobile);
        AsCusEmployee administrator = employeeService.getAdministrator();
        EventPublishHandler.publish(new EmpInfoChangeEvent(flag).setAdmin(administrator.getId().equals(empId)).setCompanyId(LoginUserThreadLocal.getCompanyId()));
        return flag;
    }

    @PostMapping("/listByCodes")
    public List<AsCusEmployee> listByCodes(@RequestBody List<String> codes) {
        return employeeService.list(
                Wrappers.lambdaQuery(AsCusEmployee.class)
                        .eq(AsCusEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .in(AsCusEmployee::getEmpNo, codes)
        );
    }

    /**
     * 内部服务，通过code或者name查询
     *
     * @return
     */
    @GetMapping("getOne")
    public Long getOne(@RequestParam(value = "empName", required = false) String empName,
                       @RequestParam(value = "empNo", required = false) String empNo) {
        return employeeService.getOne(empName, empNo);
    }

    /**
     * 修改员工工号信息
     * @param employeeModifyDto
     * @return
     */
    @PostMapping("/modifyEmployeeNo")
    public Boolean modifyEmployeeNo(@RequestBody EmployeeModifyDto employeeModifyDto) {
        return employeeService.updateEmpNo(employeeModifyDto);
    }

    /**
     * 校验员工是否重名
     * @param employee
     * @return
     */
    @PostMapping("/verifyEmpName")
    public Boolean verifyEmpName(@RequestBody CusEmployeeOptDto employee) {
        return employeeService.verifyEmpName(employee);
    }

}
