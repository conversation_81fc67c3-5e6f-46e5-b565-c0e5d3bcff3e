package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.system.CusAccountDto;
import com.niimbot.system.CusAccountPageDto;
import com.niimbot.system.CusAccountPageQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 员工表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
public interface AsCusAccountMapper {

    // 企业微信兼容，员工搜索
    IPage<CusAccountPageDto> selectAccountPage(Page<CusAccountDto> page,
                                               @Param("ew") CusAccountPageQueryDto query,
                                               @Param("deptSql") String deptSql,
                                               @Param("companyId") Long companyId,
                                               @Param("unionIds") List<String> unionIds);

    List<CusAccountDto> selectAccountList(@Param("companyId") Long companyId);

    List<Long> selectAccountEmpIds(@Param("companyId") Long companyId);


    /**
     * 通过角色id查询账号数量
     *
     * @param companyId
     * @param roleId
     * @return
     */
    Integer getAccountAmount(@Param("companyId") Long companyId,
                             @Param("roleId") Long roleId,
                             @Param("deptSql") String deptSql);

}
