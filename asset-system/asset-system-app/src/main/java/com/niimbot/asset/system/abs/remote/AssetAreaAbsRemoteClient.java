package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.AssetAreaAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/10 18:14
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.means.domain.abs.impl.AssetAreaAbsImpl")
@FeignClient(name = "asset-means", url = "http://localhost:8000/")
public interface AssetAreaAbsRemoteClient extends AssetAreaAbs {
}
