package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsVersionManagement;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.VersionManagementDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsVersionManagementMapper.class)
public interface AsVersionManagementMapper extends BaseMapper<AsVersionManagement> {

    List<VersionManagementDto> getMaxVersionExt(@Param("type") Integer type,
                                                @Param("first") Integer first,
                                                @Param("second") Integer second,
                                                @Param("third") Integer third);
}
