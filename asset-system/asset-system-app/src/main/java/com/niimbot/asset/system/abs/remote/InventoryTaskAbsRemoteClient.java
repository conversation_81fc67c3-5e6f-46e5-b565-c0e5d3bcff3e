package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.InventoryTaskAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/11 14:59
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.inventory.domain.abs.impl.InventoryTaskAbsImpl")
@FeignClient(name = "asset-inventory", url = "https://{gateway}/client/abs/inventory/inventoryTaskAbs/")
public interface InventoryTaskAbsRemoteClient extends InventoryTaskAbs {
}
