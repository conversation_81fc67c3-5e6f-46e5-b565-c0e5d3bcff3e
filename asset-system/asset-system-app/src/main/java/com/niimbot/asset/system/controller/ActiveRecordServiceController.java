package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.system.model.AsUserActiveReports;
import com.niimbot.asset.system.service.AsUserActiveReportsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.hutool.core.collection.CollUtil.isNotEmpty;

/**
 * 活跃记录管理控制器
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@RestController
@RequestMapping("server/system/activeRecord")
@Slf4j
public class ActiveRecordServiceController {

    @Resource
    private AsUserActiveReportsService userActiveReportsService;

    /**
     * 添加活跃记录
     *
     * @param userActiveReports 活跃记录对象
     * @return Boolean
     */
    @PostMapping
    public boolean saveActiveRecord(@RequestBody AsUserActiveReports userActiveReports) {
        List<AsUserActiveReports> list = userActiveReportsService.list(Wrappers.<AsUserActiveReports>lambdaQuery()
                .eq(AsUserActiveReports::getUserId, userActiveReports.getUserId())
                .eq(AsUserActiveReports::getDayTime, userActiveReports.getDayTime()));
        if (isNotEmpty(list)) {
            return false;
        }
        return userActiveReportsService.save(userActiveReports);
    }

}
