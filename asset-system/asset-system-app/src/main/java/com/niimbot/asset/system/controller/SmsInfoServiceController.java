package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.system.model.AsSmsInfo;
import com.niimbot.asset.system.service.AsSmsInfoService;
import com.niimbot.system.SmsInfoQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 短信管理控制器
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@RestController
@RequestMapping("server/system/smsInfo")
@Slf4j
public class SmsInfoServiceController {

    @Resource
    private AsSmsInfoService smsInfoService;

    @GetMapping(value = "/page")
    public IPage<AsSmsInfo> page(SmsInfoQueryDto dto) {
        return smsInfoService.page(dto.buildIPage(), new QueryWrapper<AsSmsInfo>().lambda()
                .like(StringUtils.isNotEmpty(dto.getAddress()), AsSmsInfo::getAddress, dto.getAddress())
                .orderByDesc(AsSmsInfo::getCreateTime));
    }

}
