package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.PurchaseOrderAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/5/10 10:46
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.purchase.domain.abs.impl.PurchaseOrderAbsImpl")
@FeignClient(name = "asset-purchase", url = "https://{gateway}/client/abs/purchase/purchaseOrderAbs/")
public interface PurchaseOrderAbsRemoteClient extends PurchaseOrderAbs {
}
