package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsNewbieTaskConfigMapper;
import com.niimbot.asset.system.model.AsNewbieTaskConfig;
import com.niimbot.asset.system.service.NewbieTaskConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NewbieTaskConfigServiceImpl extends ServiceImpl<AsNewbieTaskConfigMapper, AsNewbieTaskConfig> implements NewbieTaskConfigService {
}
