package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.mapper.AsCusMenuAppMapper;
import com.niimbot.asset.system.model.AsCusMenuApp;
import com.niimbot.asset.system.service.CusMenuAppService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
@Service
public class CusMenuAppServiceImpl extends ServiceImpl<AsCusMenuAppMapper, AsCusMenuApp> implements CusMenuAppService {

    @Override
    public Boolean addMenu(AsCusMenuApp menu) {
        // 检查是否包含PID，不包含PID或者PID不存在都默认设置PID。
        LambdaQueryWrapper<AsCusMenuApp> wrapper = new QueryWrapper<AsCusMenuApp>().lambda().eq(AsCusMenuApp::getId, menu.getPid());
        AsCusMenuApp parent = this.getBaseMapper().selectOne(wrapper);
        if (parent == null) {
            menu.setPid(0L).setPaths("0,").setLevel(0);
        } else {
            if (parent.getLevel() + 1 > 7) {
                throw new BusinessException(SystemResultCode.LEVEL_OVER_MAX, String.valueOf(7));
            }
            menu.setPid(parent.getId()).setPaths(parent.getPaths() + parent.getId() + ",").setLevel(parent.getLevel() + 1);
        }
        // name与code唯一性校验
        checkRepeat(menu, false);
        // 存入数据库
        if (this.getBaseMapper().insert(menu) < 0) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }


    /**
     * code全表唯一 route全表唯一 同一层级下name不能重复，更新时不能与其他记录相同
     *
     * @param menu 菜单
     */
    private void checkRepeat(AsCusMenuApp menu, boolean isUpdate) {
        LambdaQueryWrapper<AsCusMenuApp> nameWrapper = new QueryWrapper<AsCusMenuApp>().lambda();
        nameWrapper.select(AsCusMenuApp::getId);
        if (isUpdate) {
            nameWrapper.ne(AsCusMenuApp::getId, menu.getId());
        }
        LambdaQueryWrapper<AsCusMenuApp> codeWrapper = nameWrapper.clone();
        LambdaQueryWrapper<AsCusMenuApp> routeWrapper = nameWrapper.clone();
        if (StrUtil.isBlank(menu.getMenuName())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        // name repeat
        nameWrapper.eq(AsCusMenuApp::getPid, menu.getPid()).eq(AsCusMenuApp::getMenuName, menu.getMenuName());
        AsCusMenuApp nameRepeat = this.getBaseMapper().selectOne(nameWrapper);
        if (ObjectUtil.isNotNull(nameRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "菜单名称", menu.getMenuName());
        }
        // code repeat
        if (StrUtil.isBlank(menu.getMenuCode())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        codeWrapper.eq(AsCusMenuApp::getMenuCode, menu.getMenuCode());
        AsCusMenuApp codeRepeat = this.getBaseMapper().selectOne(codeWrapper);
        if (ObjectUtil.isNotNull(codeRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "菜单编码", menu.getMenuCode());
        }
        // route repeat
        if ((!"B".equals(menu.getMenuType()) && !"C".equals(menu.getMenuType())) && StrUtil.isBlank(menu.getFeRoute())) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        if (StrUtil.isNotBlank(menu.getFeRoute())) {
            return;
        }
        routeWrapper.eq(AsCusMenuApp::getFeRoute, menu.getFeRoute());
        AsCusMenuApp routeRepeat = this.getBaseMapper().selectOne(routeWrapper);
        if (ObjectUtil.isNotNull(routeRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "菜单地址", menu.getFeRoute());
        }
    }

    @Override
    public Boolean updateMenu(AsCusMenuApp menu) {
        // 存在性校验
        AsCusMenuApp exist = this.getBaseMapper().selectById(menu.getId());
        if (ObjectUtil.isNull(exist)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        LambdaUpdateWrapper<AsCusMenuApp> wrapper = new UpdateWrapper<AsCusMenuApp>().lambda();
        wrapper.eq(AsCusMenuApp::getId, menu.getId());
        // 属性重复性校验
        checkRepeat(menu, true);
        wrapper.set(AsCusMenuApp::getMenuName, menu.getMenuName()).set(AsCusMenuApp::getMenuCode, menu.getMenuCode()).set(AsCusMenuApp::getFeRoute, menu.getFeRoute());
        // PID
        AsCusMenuApp parent = this.getBaseMapper().selectOne(new QueryWrapper<AsCusMenuApp>().lambda().eq(AsCusMenuApp::getId, menu.getPid()));
        if (parent == null || menu.getPid().equals(exist.getId())) {
            menu.setPid(exist.getPid()).setPaths(exist.getPaths()).setLevel(exist.getLevel());
        } else {
            if (parent.getLevel() + 1 > 7) {
                throw new BusinessException(SystemResultCode.LEVEL_OVER_MAX, String.valueOf(7));
            }
            menu.setPid(parent.getId()).setPaths(parent.getPaths() + parent.getId() + ",").setLevel(parent.getLevel() + 1);
        }
        wrapper.set(AsCusMenuApp::getPid, menu.getPid()).set(AsCusMenuApp::getPaths, menu.getPaths()).set(AsCusMenuApp::getLevel, menu.getLevel());
        // 无特殊规则属性的非空校验
        wrapper.set(ObjectUtil.isNotNull(menu.getOrderNum()), AsCusMenuApp::getOrderNum, menu.getOrderNum())
                .set(StrUtil.isNotBlank(menu.getMenuType()), AsCusMenuApp::getMenuType, menu.getMenuType())
                .set(StrUtil.isNotBlank(menu.getMenuClass()), AsCusMenuApp::getMenuClass, menu.getMenuClass())
                .set(StrUtil.isNotBlank(menu.getMenuIcon()), AsCusMenuApp::getMenuIcon, menu.getMenuIcon())
                .set(ObjectUtil.isNotNull(menu.getIsShowChildren()), AsCusMenuApp::getIsShowChildren, menu.getIsShowChildren())
                .set(ObjectUtil.isNotNull(menu.getIsConfig()), AsCusMenuApp::getIsConfig, menu.getIsConfig())
                .set(ObjectUtil.isNotNull(menu.getCanEdit()), AsCusMenuApp::getCanEdit, menu.getCanEdit());
        // 更新
        if (!this.update(wrapper)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @Override
    public Boolean deleteList(List<Long> ids) {
        ids.forEach(id -> {
            LambdaQueryWrapper<AsCusMenuApp> wrapper = new QueryWrapper<AsCusMenuApp>()
                    .lambda()
                    .eq(AsCusMenuApp::getId, id)
                    .or()
                    .like(AsCusMenuApp::getPaths, ("," + id + ","));
            this.remove(wrapper);
        });
        return true;
    }

    @Override
    public Boolean sort(List<Long> ids) {
        // 父节点相同才能进行排序
        LambdaQueryWrapper<AsCusMenuApp> wrapper = new QueryWrapper<AsCusMenuApp>().lambda().select(AsCusMenuApp::getPid).in(AsCusMenuApp::getId, ids).groupBy(AsCusMenuApp::getPid);
        List<AsCusMenuApp> list = this.getBaseMapper().selectList(wrapper);
        if (list.size() > 1) {
            throw new BusinessException(SystemResultCode.MENU_SORT_ERROR);
        }
        // 重新升序编号
        AtomicInteger index = new AtomicInteger(0);
        List<AsCusMenuApp> cusMenus = ids.stream().map(aLong -> new AsCusMenuApp().setId(aLong).setOrderNum(index.getAndIncrement())).collect(Collectors.toList());
        this.updateBatchById(cusMenus);
        return true;
    }
}
