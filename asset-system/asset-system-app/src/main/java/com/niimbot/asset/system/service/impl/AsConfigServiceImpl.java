package com.niimbot.asset.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.mapper.AsConfigMapper;
import com.niimbot.asset.system.model.AsConfig;
import com.niimbot.asset.system.service.AsConfigService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CompanyPaymentStatusDto;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Service
public class AsConfigServiceImpl extends ServiceImpl<AsConfigMapper, AsConfig> implements AsConfigService {

    @Override
    public CompanyPaymentStatusDto queryCompanyPaymentStatus() {
        AsConfig config = this.getBaseMapper().selectOne(Wrappers.lambdaQuery(AsConfig.class)
                .eq(AsConfig::getConfigKey, "company_payment_status"));
        if (Objects.isNull(config)) {
            throw new BusinessException(SystemResultCode.INTERNAL_SERVER_ERROR, "企业付费状态配置不存在");
        }

        return JSONObject.parseObject(config.getConfigValue(), CompanyPaymentStatusDto.class);
    }

}
