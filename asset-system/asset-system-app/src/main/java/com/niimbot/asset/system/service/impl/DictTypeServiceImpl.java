package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.system.mapper.AsDictTypeMapper;
import com.niimbot.asset.system.model.AsDictData;
import com.niimbot.asset.system.model.AsDictType;
import com.niimbot.asset.system.service.DictDataService;
import com.niimbot.asset.system.service.DictTypeService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/11/9 8:45
 */
@Service
public class DictTypeServiceImpl extends ServiceImpl<AsDictTypeMapper, AsDictType> implements DictTypeService, ApplicationListener<ApplicationReadyEvent> {

    private final DictDataService dictDataService;

    private final RedisService redisService;

    private final ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    public DictTypeServiceImpl(DictDataService dictDataService, RedisService redisService,
                               ThreadPoolTaskExecutor taskExecutor) {
        this.dictDataService = dictDataService;
        this.redisService = redisService;
        this.taskExecutor = taskExecutor;
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return ;
        }

        taskExecutor.execute(() -> {
            List<AsDictType> dictTypeList = this.list();
            List<AsDictData> dictDataList = dictDataService.list(Wrappers.lambdaQuery(AsDictData.class)
                    .eq(AsDictData::getStatus, DictConstant.SYS_ENABLE));
            Map<String, List<AsDictData>> dictDataMap = dictDataList.stream().collect(Collectors.groupingBy(AsDictData::getDictType));
            for (AsDictType dictType : dictTypeList) {
                List<AsDictData> dictDatas = dictDataMap.getOrDefault(dictType.getDictType(), new ArrayList<>());
                redisService.set(BaseConstant.SYS_DICT_KEY + dictType.getDictType(), dictDatas);
            }
        });
    }
}
