package com.niimbot.asset.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.mapper.AsAuditLogConfigMapper;
import com.niimbot.asset.system.mapper.AsUserOrgMapper;
import com.niimbot.asset.system.mapper.AsUserRoleMapper;
import com.niimbot.asset.system.model.AsAuditLog;
import com.niimbot.asset.system.model.AsAuditLogConfig;
import com.niimbot.asset.system.model.AsCusRole;
import com.niimbot.asset.system.model.AsUserOrg;
import com.niimbot.asset.system.repository.AuditLogRepository;
import com.niimbot.asset.system.service.AsAuditLogService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AuditLogDto;
import com.niimbot.system.AuditLogRecord;
import com.niimbot.system.AuditLogSearch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsAuditLogServiceImpl implements AsAuditLogService, ApplicationListener<ApplicationReadyEvent> {

    private final AuditLogRepository auditLogRepository;

    private final AsAuditLogConfigMapper auditLogConfigMapper;

    private final CacheResourceUtil cacheResourceUtil;

    private final AsUserOrgMapper userOrgMapper;

    private final AsUserRoleMapper userRoleMapper;

    private final TemplateEngine templateEngine = TemplateUtil.createEngine(new TemplateConfig());

    private final Map<String, AsAuditLogConfig> auditLogConfigMap = new ConcurrentHashMap<>(32);

    private final Map<String, String> terminalMap = new ConcurrentHashMap<>(2);

    @Override
    public PageUtils<AuditLogDto> search(AuditLogSearch search) {
        search.handleTime();
        search.handleOrder();
        return auditLogRepository.search(search);
    }

    @Override
    public void record(AuditLogRecord record) {
        if (auditLogRepository.existById(record.getId())) {
            log.warn("audit log action is exist id[{}]", record.getId());
            return;
        }
        String actionCode = record.getActionCode();
        if (!auditLogConfigMap.containsKey(actionCode)) {
            log.warn("audit log action code not in config [{}]", actionCode);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "操作日志类型不在配置中");
        }
        List<AsUserOrg> userOrgs = userOrgMapper.selectList(
                Wrappers.lambdaQuery(AsUserOrg.class)
                        .select(AsUserOrg::getOrgId)
                        .eq(AsUserOrg::getUserId, record.getOperatorId())
                        .last("LIMIT 1")
        );
        if (CollUtil.isEmpty(userOrgs)) {
            log.warn("audit log operator org is empty [{}]", record.getOperatorId());
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "操作人没有对应的组织");
        }
        Long orgId = userOrgs.get(0).getOrgId();
        AsCusRole userFirstRole = userRoleMapper.selectUserFirstRoleName(record.getOperatorId());
        if (Objects.isNull(userFirstRole)) {
            log.warn("audit log operator role is empty [{}]", record.getOperatorId());
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "操作人没有对应的组织");
        }
        AsAuditLogConfig config = auditLogConfigMap.get(actionCode);
        String content = config.getContentTpl();
        // value 全为空
        if (CollUtil.isNotEmpty(record.getTplParams()) && record.getTplParams().values().stream().noneMatch(ObjectUtil::isNotEmpty)) {
            log.warn("audit log template params values is empty [{}]", record.getTplParams());
            return;
        }
        // 模板值为空代表是静态文案不需要填充
        if (CollUtil.isNotEmpty(record.getTplParams())) {
            content = templateEngine.getTemplate(content).render(record.getTplParams());
        }
        // 如果填充后包含此占位符代表模板参数不对，默认填充成动作类型
        if (content.contains("${")) {
            content = config.getActionText();
        }
        String userNameAndCode = cacheResourceUtil.getUserNameAndCode(record.getOperatorId());
        String orgNameAndCode = cacheResourceUtil.getOrgNameAndCode(orgId);
        AsAuditLog auditLog = new AsAuditLog()
                .setId(record.getId()).setCompanyId(record.getCompanyId())
                .setUrl(record.getRequestUrl()).setParams(record.getRequestParams())
                .setActionText(config.getActionText()).setActionCode(config.getActionCode()).setContent(content)
                .setOperator(userNameAndCode).setCreateBy(record.getOperatorId())
                .setOrgId(orgId).setOrgName(orgNameAndCode)
                .setRoleId(userFirstRole.getId()).setRoleName(userFirstRole.getRoleName())
                .setCreateTime(record.getOperationTime());
        auditLog.setTerminalCode(record.getTerminal());
        if (StrUtil.isNotBlank(record.getTerminal())) {
            auditLog.setTerminalText(terminalMap.getOrDefault(record.getTerminal(), ""));
        }
        auditLogRepository.save(auditLog);
    }

    @Override
    public void reloadConfig() {
        List<AsAuditLogConfig> configs = auditLogConfigMapper.selectList(Wrappers.emptyWrapper());
        configs.forEach(v -> auditLogConfigMap.put(v.getActionCode(), v));
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        ConfigurableApplicationContext ignore = event.getApplicationContext();
        reloadConfig();
        terminalMap.put("pc", "pc");
        terminalMap.put("app", "app");
        terminalMap.put("dingtalk", "钉钉小程序");
        terminalMap.put("wechat", "企业微信小程序");
    }
}
