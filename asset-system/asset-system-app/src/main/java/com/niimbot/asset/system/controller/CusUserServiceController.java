package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.annotation.MapEntity;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.utils.ServletUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsCompanyChannel;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsUserFeedback;
import com.niimbot.asset.system.model.ChangeCusUserPassword;
import com.niimbot.asset.system.service.AccountCenterService;
import com.niimbot.asset.system.service.AsCompanyChannelService;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.asset.system.service.AsUserFeedBackService;
import com.niimbot.asset.system.service.ChangeCusUserPasswordService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.service.UserCenterService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AccountInfoDto;
import com.niimbot.system.CusUserDetailDto;
import com.niimbot.system.RecommendRegisterDto;
import com.niimbot.system.RegisterDto;
import com.niimbot.system.ResetPasswordDto;
import com.niimbot.system.SonUserDto;
import com.niimbot.system.UserCenterAPPDto;
import com.niimbot.system.UserCenterPCDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Validator;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @Date 2020/10/30
 */
@RestController
@RequestMapping("server/system/cusUser")
@Slf4j
public class CusUserServiceController {

    @Resource
    private CusUserService userService;

    @Resource
    private AccountCenterService accountCenterService;

    @Resource
    private AsUserFeedBackService feedBackService;

    @Resource
    private ChangeCusUserPasswordService changeCusUserPasswordService;

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private AsCusEmployeeService employeeService;

    @Resource
    private AsCompanyChannelService companyChannelService;

    @PutMapping
    public Boolean edit(@RequestBody AsCusUser asCusUser) {
        return userService.update(asCusUser);
    }

    @DeleteMapping
    public Boolean remove(@RequestBody List<Long> userIds) {
        return userService.removeByIds(userIds);
    }

    @GetMapping(value = "/list")
    public List<AsCusUser> list(@MapEntity AsCusUser cusUser) {
        return userService.list(new QueryWrapper<>(cusUser));
    }

    @PutMapping("/updateByAccount")
    public Boolean updateByAccount(@RequestBody AsCusUser asCusUser) {
        return userService.updateByAccount(asCusUser);
    }

    @GetMapping("/getByUnionId")
    public CusUserDto getUserByUnionId(@RequestParam String unionId) {
        return userService.selectUserByUnionId(unionId);
    }

    @GetMapping("/{userId}")
    public CusUserDto getInfo(@PathVariable("userId") Long userId) {
        return accountCenterService.getEmpLoginInfo(userId);
    }

    @PostMapping("/getByIds")
    public List<AsCusUser> getByIds(@RequestBody List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return ListUtil.empty();
        }
        return userService.list(Wrappers.<AsCusUser>lambdaQuery().in(AsCusUser::getId, userIds));
    }

    @GetMapping("/getByMobile")
    public AsCusUser getCusUserByMobile(@RequestParam String mobile) {
        return userService.getCusUserByCondition(new AsCusUser().setMobile(mobile));
    }

    @GetMapping("/getByEmail")
    public AsCusUser getCusUserByEmail(@RequestParam String email) {
        AsCusEmployee employee = employeeService.getOne(
                Wrappers.<AsCusEmployee>lambdaQuery()
                        .eq(AsCusEmployee::getEmail, email));
        if (employee == null) {
            return null;
        }
        return userService.getById(employee.getId());
    }

    /**
     * 登录 忘记密码、验证码重置密码
     *
     * @param dto
     * @return 重置成功
     */
    @PutMapping("/reSetPassword")
    public Boolean reSetPassword(@RequestBody ResetPasswordDto dto) {
        AsCusUser userExist = null;

        if (userExist == null && Validator.isEmail(dto.getMobile())) {
            userExist = userService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getEmail, dto.getMobile()));
        }else {
            userExist = userService.getCusUserByCondition(new AsCusUser().setMobile(dto.getMobile()));
        }

        if (userExist == null) {
            // 用户未注册
            throw new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST);
        }
        return changeCusUserPasswordService.changeCusUserPassword(new ChangeCusUserPassword().setId(userExist.getId())
                .setPassword(dto.getPassword()));
    }

    @GetMapping("/app/getUserCenterInfo")
    public UserCenterAPPDto getUserCenterAppInfo() {
        UserCenterAPPDto userCenterAppInfo = userCenterService.getUserCenterAppInfo(
                LoginUserThreadLocal.getCusUser().getUnionId());
        userCenterAppInfo.setCompanyId(LoginUserThreadLocal.getCompanyId());
        AsCompanyChannel companyChannel = companyChannelService.getById(LoginUserThreadLocal.getCompanyId());
        if (Objects.nonNull(companyChannel)) {
            userCenterAppInfo.setCrmClueSyncStatus(companyChannel.getCrmClueSyncStatus());
            userCenterAppInfo.setCrmCusSyncStatus(companyChannel.getCrmCusSyncStatus());
        }
        return userCenterAppInfo;
    }

    @GetMapping("/personDetail")
    public CusUserDetailDto personDetail() {
        return userService.personDetail();
    }

    @GetMapping("/checkPassword")
    public String checkPassword() {
        return userService.getById(LoginUserThreadLocal.getAccountId()).getPassword();
    }

    /**
     * APP、PC个人中心修改密码
     *
     * @param newPassword 信息密码
     * @return 成功与否
     */
    @GetMapping("/changeAppCenterPassword")
    public Boolean changeAppCenterPassword(@RequestParam String newPassword) {
        return changeCusUserPasswordService.changeCusUserPassword(new ChangeCusUserPassword()
                .setId(LoginUserThreadLocal.getAccountId())
                .setPassword(newPassword));
    }

    @GetMapping("/bindMobile")
    @Transactional(rollbackFor = Exception.class)
    public Boolean bindMobile(@RequestParam String mobile) {
        AsCusUser byId = userService.getById(LoginUserThreadLocal.getAccountId());
        if (StringUtils.isNotEmpty(byId.getMobile())) {
            throw new BusinessException(SystemResultCode.USER_PHONE_BIND);
        }
        return userService.modifyMobile(mobile);
    }

    @GetMapping("/bindEmail")
    @Transactional(rollbackFor = Exception.class)
    public Boolean bindEmail(@RequestParam String email) {
        AsCusUser byId = userService.getById(LoginUserThreadLocal.getAccountId());
        // AsCusEmployee employee = employeeService.getById(byId.getId());
        if (StringUtils.isNotEmpty(byId.getEmail())) {
            throw new BusinessException(SystemResultCode.USER_EMAIL_BIND);
        }
        byId.setEmail(email);
        return userService.updateById(byId);
        // return employeeService.updateById(employee);
    }

    @GetMapping("/changeMobile")
    public Boolean changeMobile(@RequestParam String mobile) {
        AsCusUser byId = userService.getById(LoginUserThreadLocal.getAccountId());
        if (StringUtils.equals(mobile, byId.getMobile())) {
            // same mobile ignore.
            return true;
        }
        return userService.modifyMobile(mobile);
    }

    @PostMapping("/feedback")
    public Boolean feedback(@RequestBody UserCenterAPPDto dto) {
        AsUserFeedback asUserFeedback = new AsUserFeedback()
                .setFeedbackContent(dto.getFeedBack())
                .setSource(ServletUtils.getClientSource().getValue().shortValue())
                .setUserId(LoginUserThreadLocal.getCurrentUserId());
        return feedBackService.save(asUserFeedback);
    }

    @GetMapping("/companySonAccountNum/{companyId}")
    public Integer getSonAccountNumByCompanyId(@PathVariable("companyId") Long companyId) {
        return userService.getSonAccountNumByCompanyId(companyId);
    }

    @GetMapping("/companySonList/{companyId}")
    public List<SonUserDto> getSonListByCompanyId(@PathVariable("companyId") Long companyId) {
        return userService.getSonListByCompanyId(companyId);
    }

    /**
     * 企业注册V2
     *
     * @param dto dto
     * @return 账号信息
     * @since 2021/01/04
     */
    @PostMapping
    public AsCusUser registerV2(@RequestBody RegisterDto dto) {
        log.info("企业注册");
        return userService.registerV2(dto);
    }

    @PostMapping("/recommendRegister")
    public void recommendRegister(@RequestBody RecommendRegisterDto dto) {
        log.info("企业邀请注册");
        userService.recommendRegister(dto);
    }

    /**
     * 注销账号
     *
     * @return 账号信息
     * @since 2022/03/10
     */
    @PostMapping("/canceluser")
    public boolean cancalUser() {
        return userService.cancalUser();
    }

    /**
     * PC用户中心
     */
    @GetMapping("/pc/getUserCenterInfo")
    public UserCenterPCDto getUserCenterPCInfo() {
        return userService.getUserCenterPcInfo();
    }

    /**
     * 二维码邀请员工注册-用户详情信息
     *
     * @return 用户id
     */
    // @GetMapping("/userInfo/{userId}")
    // public CusUserDetailDto userInfo(@PathVariable("userId") Long userId) {
    //     return userService.userInfo(userId);
    // }
    @PostMapping("loginAfterRecord")
    public void loginAfterRecord(@RequestBody Long userId) {
        userService.loginAfterRecord(userId);
    }


    /**
     * PC端用户中心-账号信息
     *
     * @return AccountInfoDto
     */
    @GetMapping("/accountInfo")
    public AccountInfoDto currentAccountInfo() {
        return userService.currentAccountInfo();
    }
}
