package com.niimbot.asset.system.service.impl;

import com.google.common.collect.ImmutableMap;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.filter.SQLFilter;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.system.mapper.AsCompanySettingMapper;
import com.niimbot.asset.system.model.AsCompanySetting;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.model.CompanySwitch;
import com.niimbot.asset.system.service.CompanySettingService;
import com.niimbot.asset.system.service.CusAccountService;
import com.niimbot.asset.system.support.ModelDataScopeServiceImpl;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/12/8 17:01
 */
@Slf4j
@Service
public class CompanySettingServiceImpl extends ServiceImpl<AsCompanySettingMapper, AsCompanySetting> implements CompanySettingService {

    private static final String COMPANY_BIZ_SERIAL_NUMBER_KEY = "company_biz_serial_number:";

    @Autowired
    private RedisService redisService;
    @Autowired
    private RedisDistributeLock redisDistributeLock;
    @Autowired
    private ModelDataScopeServiceImpl modelDataScopeService;
    @Autowired
    private CusAccountService accountService;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    public String getBizCodeForGenSerialNo(Long id, String type, Long companyId) {
        String serialNo = this.getBaseMapper().selectCodeForGenSerialNo(id, type, companyId);
        if (StrUtil.isBlank(serialNo)) {
            return StrUtil.EMPTY;
        }
        return serialNo;
    }

    @Override
    public Long getTreePrimaryNodeId(Long id, String type) {
        String paths = this.getBaseMapper().selectTreeNodePaths(id, type);
        if (StrUtil.isBlank(paths)) {
            return id;
        }
        List<Long> ids = Arrays.stream(paths.split(",")).map(Long::parseLong).collect(Collectors.toList());
        ids.removeIf(v -> v == 0);
        if (CollUtil.isEmpty(ids)) {
            return id;
        }
        return ids.get(0);
    }

    @Override
    public String getMaxCode(String prefix, String escapePrefix, Long companyId,
                             String type, Integer serialLen, String fieldCode) {
        return redisDistributeLock.lock("companySettingMaxCode", 10L, 60L, () -> {
            // 获取缓存，如果存在，则加一取值
            Integer max = Convert.toInt(redisService.hGet(getRedisKey(prefix, companyId, type, serialLen, fieldCode), "serial"), -1);
            Integer incr = Convert.toInt(redisService.hGet(getRedisKey(prefix, companyId, type, serialLen, fieldCode), "incr"), 0);
            if (ListUtil.of(FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_MATERIAL_SERIALNO, FormFieldCO.YZC_SERIALNO).contains(type)) {
                if (max < 0) {
                    max = Convert.toInt(getMaxCodeDB(prefix, escapePrefix, companyId, type, serialLen, fieldCode), 0);
                }
                boolean loop = true;
            while (loop) {
                // 到最大位数了
                if (Convert.toStr(max + 1).length() > serialLen + incr) {
                    max = Convert.toInt(getMaxCodeDB(prefix, escapePrefix, companyId, type, serialLen + incr + 1, fieldCode), 0);
                    incr++;
                } else {
                    loop = false;
                }
            }
            max = max + 1;
            Map<String, Object> map = ImmutableMap.of("serial", max, "incr", incr);
                redisService.hSetAll(getRedisKey(prefix, companyId, type, serialLen, fieldCode), map, 1500);
            return prefix + StrUtil.padPre(Convert.toStr(max), serialLen + incr, "0");
        } else {
            log.error("流水号类型" + type + "不存在");
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "流水号类型" + type + "不存在");
        }});
    }

    @Override
    public CompanySwitch getSwitchSettingWithCache(Long id) {
        String switchCacheKey = this.switchCacheKey(id);
        if (redisService.hasKey(switchCacheKey)) {
            return (CompanySwitch) redisService.get(switchCacheKey);
        }
        AsCompanySetting setting = Optional.ofNullable(this.getById(id)).orElseThrow(() -> new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST));
        CompanySwitch companySwitch = Optional.ofNullable(setting.getExpandSwitch()).orElse(new CompanySwitch());
        redisService.set(switchCacheKey, companySwitch, 1, TimeUnit.DAYS);
        return companySwitch;
    }

    @Override
    public Boolean enableIdleAsset(Long companyId, Boolean enabled) {
        redisService.del(this.switchCacheKey(companyId));
        AsCompanySetting setting = Optional.ofNullable(this.getById(companyId)).orElseThrow(() -> new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST));
        // 不相同更新
        if (!ObjectUtil.equals(setting.getExpandSwitch().getEnableIdleAsset(), enabled)) {
            setting.getExpandSwitch().setEnableIdleAsset(enabled);
            this.updateById(setting);
            taskExecutor.execute(() -> {
                // 失效权限缓存
                List<Long> hasAccountEmpIds = accountService.selectAccountEmpIds(companyId);
                modelDataScopeService.cleanDataScopeCache(companyId, hasAccountEmpIds);
            });
        }
        return true;
    }

    @Override
    public Boolean configDefaultDataAuth(Long companyId, List<AsDataAuthority> dataAuthorities) {
        AsCompanySetting setting = this.getById(companyId);
        setting.setDefaultDataAuthorities(dataAuthorities);
        return this.updateById(setting);
    }

    @Override
    public Boolean enableSyncRole(Long companyId, Boolean enabled) {
        AsCompanySetting setting = this.getById(companyId);
        if (Objects.isNull(setting)) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        setting.getExpandSwitch().setEnableSyncApproveRole(enabled);
        return this.updateById(setting);
    }

    private String getMaxCodeDB(String prefix, String escapePrefix, Long companyId, String type, Integer serialLen, String fieldCode) {
        SQLFilter.sqlInject(fieldCode);
        if (FormFieldCO.YZC_ASSET_SERIALNO.equals(type)) {
            return this.getBaseMapper().getAssetMaxCode("\"" + escapePrefix + "%\"", companyId, serialLen, StrUtil.length(prefix) + serialLen, fieldCode);
        } else if (FormFieldCO.YZC_MATERIAL_SERIALNO.equals(type)) {
            return this.getBaseMapper().getMaterialMaxCode("\"" + escapePrefix + "%\"", companyId, serialLen, StrUtil.length(prefix) + serialLen, fieldCode);
        } else if (FormFieldCO.YZC_SERIALNO.equals(type)) {
            return this.getBaseMapper().getProductMaxCode("\"" + escapePrefix + "%\"", companyId, serialLen, StrUtil.length(prefix) + serialLen, fieldCode);
        }
        return StrUtil.EMPTY;
    }

    private String getRedisKey(String prefix, Long companyId, String type, Integer serialLen, String fieldCode) {
        return StrUtil.concat(true, COMPANY_BIZ_SERIAL_NUMBER_KEY,
                Convert.toStr(companyId), ":",
                prefix, ":",
                type, ":",
                fieldCode, ":",
                Convert.toStr(serialLen));
    }

}
