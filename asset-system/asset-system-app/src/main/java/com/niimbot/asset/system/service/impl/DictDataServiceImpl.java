package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.system.mapper.AsDictDataMapper;
import com.niimbot.asset.system.model.AsDictData;
import com.niimbot.asset.system.service.DictDataService;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/9 8:45
 */
@Service
public class DictDataServiceImpl extends ServiceImpl<AsDictDataMapper, AsDictData> implements DictDataService {

    @Override
    public List<AsDictData> selectDictDataByType(String dictType) {
        QueryWrapper<AsDictData> query = new QueryWrapper<>();
        query.eq("dict_type", dictType)
                .eq("status", DictConstant.SYS_ENABLE)
                .orderByAsc("dict_sort");
        return this.list(query);
    }
}
