package com.niimbot.asset.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.annotation.MapEntity;
import com.niimbot.asset.framework.annotation.MapPage;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.system.model.AsDictType;
import com.niimbot.asset.system.service.DictTypeService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/11/9 8:54
 */
@RestController
@RequestMapping("server/system/dictType")
public class DictTypeServiceController {

    @Autowired
    private DictTypeService dictTypeService;

    @GetMapping(value = "/page")
    public IPage<AsDictType> page(@MapEntity AsDictType dictType, @MapPage Page<AsDictType> page) {
        return dictTypeService.page(page, new QueryWrapper<>(dictType));
    }

    @GetMapping(value = "/list")
    public List<AsDictType> list(@MapEntity AsDictType dictType) {
        return dictTypeService.list(new QueryWrapper<>(dictType));
    }

    @GetMapping("/{dictId}")
    public AsDictType getInfo(@PathVariable("dictId") Long dictId) {
        return dictTypeService.getById(dictId);
    }

    @PostMapping
    public Boolean add(@RequestBody AsDictType dictType) {
        if (!dictTypeService.save(dictType)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return true;
    }

    @PutMapping
    public Boolean edit(@RequestBody AsDictType dictType) {
        if (!dictTypeService.updateById(dictType)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    @DeleteMapping
    public Boolean remove(@RequestBody List<Long> dictTypes) {
        if (dictTypeService.getBaseMapper().deleteBatchIds(dictTypes) != dictTypes.size()) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }
        return true;
    }

}
