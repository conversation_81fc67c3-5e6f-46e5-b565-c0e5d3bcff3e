package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.constant.CompanyPasswordConstant;
import com.niimbot.asset.system.mapper.AsCusUserMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.ChangeCusUserPassword;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCompanyPasswordSettingService;
import com.niimbot.asset.system.service.ChangeCusUserPasswordService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/1/26 11:26
 */
@Service
@RequiredArgsConstructor
public class ChangeCusUserPasswordServiceImpl implements ChangeCusUserPasswordService {

    private final AsCusUserMapper asCusUserMapper;
    private final AsCompanyPasswordSettingService passwordSettingService;
    private final RedisService redisService;
    private final AsAccountEmployeeService accountEmployeeService;

    @Override
    public boolean changeCusUserPassword(ChangeCusUserPassword password) {
        // 清理
        LoginUserDto userDto = LoginUserThreadLocal.get();
        Long companyId;
        if (userDto != null) {
            companyId = userDto.getCusUser().getCompanyId();
        } else {
            AsCusUser cusUser = asCusUserMapper.selectById(password.getId());
            if (cusUser == null) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "修改密码的账号不存在");
            }
            companyId = cusUser.getCompanyId();
            if (companyId == null) {
                AsAccountEmployee accountEmployee = accountEmployeeService.getOne(Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getAccountId, cusUser.getId()), false);
                if (accountEmployee == null) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "修改密码的账号不存在");
                } else {
                    companyId = accountEmployee.getCompanyId();
                }
            }
        }

        if (companyId == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "未找到绑定的企业");
        }
        // 企业密码规则校验
        passwordSettingService.checkPwdByRule(password.getPassword(), companyId);
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        asCusUserMapper.changeUserPassword(password.getId(),
                passwordEncoder.encode(password.getPassword()));

        redisService.sRemove(CompanyPasswordConstant.companyUpdatePwdAccountKey(companyId), Convert.toStr(password.getId()));
        return true;
    }
}
