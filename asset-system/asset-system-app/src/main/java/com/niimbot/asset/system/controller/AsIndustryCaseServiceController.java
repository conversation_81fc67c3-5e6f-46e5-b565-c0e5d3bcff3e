package com.niimbot.asset.system.controller;

import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.system.service.IndustryCaseConfigService;
import com.niimbot.asset.system.service.SolutionConfigService;
import com.niimbot.system.*;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/3/16 下午5:43
 */
@RestController
@RequestMapping("server/system/industryCase/")
@RequiredArgsConstructor
public class AsIndustryCaseServiceController {

    private final IndustryCaseConfigService industryCaseConfigService;

    @ApiOperation(value = "查询行业案例配置")
    @GetMapping(value = "query")
    public PageUtils<IndustryCaseConfigDto> pageQuery(IndustryCaseQueryDto request) {
        return industryCaseConfigService.pageQuery(request);
    }

    @ApiOperation(value = "查询行业案例配置")
    @GetMapping(value = "detail/{configId}")
    public IndustryCaseDetailDto detail(@PathVariable("configId") Long configId) {
        return industryCaseConfigService.queryDetail(configId);
    }
}
