package com.niimbot.asset.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.AdminPrinterConcentrationAbs;
import com.niimbot.asset.system.dto.AdminPrinterConcentrationGetQry;
import com.niimbot.asset.system.dto.clientobject.AdminPrinterConcentrationCO;
import com.niimbot.asset.system.model.AsUserPrintTag;
import com.niimbot.asset.system.service.AsUserPrintTagService;
import com.niimbot.asset.system.service.TagService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.PrintDataSetTagDto;
import com.niimbot.system.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 标签管理控制器
 *
 * <AUTHOR>
 * @Date 2020/12/11
 */
@RestController
@RequestMapping("server/system/tag")
@Slf4j
public class TagServiceController {

    @Resource
    private TagService tagService;

    @Resource
    private AsUserPrintTagService userPrintTagService;

    /**
     * 通过sizeId
     *
     * @param printType 打印类型
     * @param sizeId 尺寸Id
     * @return 账号信息
     */
    @GetMapping(value = "/{printType}/{sizeId}")
    public UserTagResDto getBySizeId(@PathVariable("printType") Short printType,
                                     @PathVariable("sizeId") Long sizeId,
                                     @RequestParam(value = "tagType", required = false) Integer tagType,
                                     @RequestParam(value = "kw", required = false) String kw,
                                     @RequestParam(value = "printerName", required = false) String printerName) {
        return tagService.getBySizeId(printType, sizeId, tagType, kw, printerName);
    }

    @Resource
    private AdminPrinterConcentrationAbs adminPrinterConcentrationAbs;

    @GetMapping(value = "/abs/qry")
    public AdminPrinterConcentrationCO getAbsQry(){
        AdminPrinterConcentrationGetQry qry = new AdminPrinterConcentrationGetQry();
        qry.setPrinterId(2L);
        return  adminPrinterConcentrationAbs.getAdminPrinterConcentration(qry);
    }

    /**
     * 获取尺寸列表
     *
     * @return 尺寸信息
     */
    @GetMapping(value = "/sizeList/{printType}")
    public SizeResDto getSizeList(@PathVariable("printType") Short printType,
                                  @RequestParam(value = "printerIds", required = false) List<Long> printerIds,
                                  @RequestParam(value = "printerName", required = false) String printerName) {
        return tagService.getSizeList(printType, printerIds, printerName);
    }

    /**
     * 获取app尺寸列表
     *
     * @return 尺寸信息
     */
    @GetMapping(value = "/appSizeList")
    public List<SizeDto> appSizeList(@RequestParam(value = "printerName", required = false) String printerName) {
        return tagService.appSizeList(printerName);
    }

    /**
     * 设置资产默认标签模板
     *
     * @param printDataSetTagDto 设置默认标签dto
     * @return Boolean
     */
    @PutMapping(value = "/setDefaultTag")
    public Boolean setDefaultTag(@RequestBody PrintDataSetTagDto printDataSetTagDto) {
        return tagService.setDefaultTag(printDataSetTagDto);
    }

    /**
     * 设置耗材默认标签模板
     *
     * @param printDataSetTagDto 设置默认标签dto
     * @return Boolean
     */
    @PutMapping(value = "/setDefaultCftag")
    public Boolean setDefaultCftag(@RequestBody PrintDataSetTagDto printDataSetTagDto) {
        return tagService.setDefaultCftag(printDataSetTagDto);
    }

    /**
     * 保存用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Boolean
     */
    @PostMapping
    public Boolean saveUserTag(@RequestBody UserTagSaveDto userTagSaveDto) {
        return tagService.saveUserTag(userTagSaveDto);
    }

    /**
     * 编辑用户自定义标签模板
     *
     * @param userTagSaveDto 用户自定义标签对象
     * @return Boolean
     */
    @PutMapping
    public Boolean editUserTag(@RequestBody UserTagSaveDto userTagSaveDto) {
        return tagService.editUserTag(userTagSaveDto);
    }

    /**
     * 删除用户自定义标签模板
     *
     * @param id 用户自定义标签id
     * @return Boolean
     */
    @DeleteMapping(value = "/{id}")
    public Boolean deleteUserTag(@PathVariable("id") Long id) {
        return tagService.deleteUserTag(id);
    }

    /**
     * 获取移动端标签列表
     *
     * @param printType 打印类型
     * @param sizeId 尺寸类型
     * @param tagType 标签类型
     * @param kw 关键字
     * @return 移动端标签列表
     */
    @GetMapping(value = "/appTagList/{printType}")
    public UserTagResDto appTagList(@PathVariable("printType") Short printType,
                                    @RequestParam(value = "sizeId") Long sizeId,
                                    @RequestParam(value = "tagType", required = false) Integer tagType,
                                    @RequestParam(value = "kw", required = false) String kw,
                                    @RequestParam(value = "printerName", required = true) String printerName) {
        return tagService.appTagList(printType, sizeId, tagType, kw, printerName);
    }

    /**
     * 获取PDF模版
     *
     * @return 获取PDF模版
     */
    @GetMapping(value = "/getPdfTag/{printType}/{printerId}")
    public HashMap<String, List<UserTagDto>> getPdfTag(@PathVariable("printType") Short printType, @PathVariable("printerId") Long printerId) {
        return tagService.getPdfTag(printType, printerId);
    }


    /**
     * 修改标签模板名称
     *
     * @param userTagNameDto 修改标签模板名称对象
     * @return Boolean
     */
    @PutMapping(value = "/updateTagName")
    public Boolean updateTagName(@RequestBody UserTagNameDto userTagNameDto) {
        return tagService.updateTagName(userTagNameDto);
    }

    /**
     * 新增标签模板
     *
     * @param userPrintTagDto 标签模板对象
     * @return Boolean
     */
    @PostMapping(value = "/add")
    public Boolean add(@RequestBody UserPrintTagDto userPrintTagDto) {
        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();

        AsUserPrintTag asUserPrintTag = new AsUserPrintTag();
        BeanUtil.copyProperties(userPrintTagDto, asUserPrintTag);
        asUserPrintTag.setCompanyId(companyId);
        if (!userPrintTagService.save(asUserPrintTag)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }

        return true;
    }

    /**
     * 编辑标签模板
     *
     * @param userPrintTagDto 标签模板对象
     * @return Boolean
     */
    @PutMapping(value = "/edit")
    public Boolean edit(@RequestBody UserPrintTagDto userPrintTagDto) {
        Long printTagId = userPrintTagDto.getId();
        AsUserPrintTag byId = userPrintTagService.getById(printTagId);
        if (null == byId) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }

        // 当前企业
        Long companyId = LoginUserThreadLocal.getCompanyId();

        AsUserPrintTag asUserPrintTag = new AsUserPrintTag();
        BeanUtil.copyProperties(userPrintTagDto, asUserPrintTag);
        asUserPrintTag.setCompanyId(companyId);
        if (!userPrintTagService.saveOrUpdate(asUserPrintTag)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }

        return true;
    }

    /**
     * 标签模板列表
     *
     * @return 标签模板
     */
    @GetMapping(value = "/list")
    public List<AsUserPrintTag> tagList() {
        return userPrintTagService.list();
    }

    /**
     * 获取单个标签模板
     *
     * @return UserPrintTagDto
     */
    @GetMapping(value = "/one/{id}")
    public AsUserPrintTag getById(@PathVariable Long id) {
        return userPrintTagService.getById(id);
    }


    /**
     * 全部去重数据查询列表
     *
     * @param sizeId 尺寸id
     * @return 标签模板信息
     */
    @GetMapping(value = "/distinct/{printType}/{sizeId}/{printerId}")
    public List<UserTagDto> getDistinctBySizeId(@PathVariable("printType") Short printType,
                                                @PathVariable("sizeId") Long sizeId,
                                                @PathVariable("printerId") Long printerId) {
        return tagService.getDistinctBySizeId(printType, sizeId, printerId);
    }

    /**
     * 标签模板详情
     *
     * @param id 模板id
     * @return 标签模板信息
     */
    @GetMapping(value = "/detail/{id}")
    public UserTagDetailDto getDetail(@PathVariable("id") Long id) {
        return tagService.getDetail(id);
    }
}
