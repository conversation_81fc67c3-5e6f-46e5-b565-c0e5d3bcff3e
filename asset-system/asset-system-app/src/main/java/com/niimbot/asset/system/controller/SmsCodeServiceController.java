package com.niimbot.asset.system.controller;

import com.niimbot.asset.system.service.SmsCodeService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信验证码控制器
 *
 * <AUTHOR>
 * @date 2021/4/28 13:45
 */
@RestController
@RequestMapping("server/system/sms")
public class SmsCodeServiceController {
    private final SmsCodeService smsCodeService;

    public SmsCodeServiceController(SmsCodeService smsCodeService) {
        this.smsCodeService = smsCodeService;
    }

    @GetMapping("/send/{iddCode}/{mobile}")
    public void sendSmsCode(@PathVariable("iddCode") String iddCode, @PathVariable("mobile") String mobile) {
        smsCodeService.sendSmsCode(iddCode, mobile);
    }

    @GetMapping("/code/{type}/{addr}")
    public void sendCommonCode(@PathVariable("type") String type, @PathVariable("addr") String addr) {
        smsCodeService.sendCommonCode(type, addr);
    }

    @GetMapping("/check/{mobile}/{code}")
    public boolean checkSmsCode(@PathVariable("mobile") String mobile, @PathVariable("code") String code) {
        return smsCodeService.checkSmsCode(mobile, code);
    }

}
