package com.niimbot.asset.system.controller;


import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsDataPermission;
import com.niimbot.asset.system.service.AsDataPermissionService;
import com.niimbot.system.DataPermissionAuthorizeDto;
import com.niimbot.system.RoleDataAuthorityConfigDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 数据权限 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@RestController
@RequestMapping("server/system/permission")
@RequiredArgsConstructor
public class AsDataPermissionServiceController {
    private final AsDataPermissionService dataPermissionService;

    private final RedisService redisService;

    @GetMapping("/user/{empId}")
    public List<AsDataPermission> queryUserDataPermission(@PathVariable("empId") Long empId) {
        return dataPermissionService.queryUserDataPermission(empId);
    }

    @GetMapping("/companyDefault/{companyId}")
    public List<AsDataPermission> queryCompanyDefaultUserDataPermission(@PathVariable("companyId") Long companyId) {
        return dataPermissionService.queryCompanyDefaultUserDataPermission(companyId);
    }

    @GetMapping("/getRoleDataAuth")
    public List<AsDataPermission> getRoleDataAuth(@RequestParam(name = "roleId", required = true) Long roleId) {
        return dataPermissionService.queryDataAuthByRole(roleId);
    }

    @PostMapping("/configRoleDataAuth")
    public Boolean configRoleDataAuth(@RequestBody RoleDataAuthorityConfigDto roleDataAuthorityConfigDto) {
        return dataPermissionService.configRoleDataAuth(roleDataAuthorityConfigDto);
    }

    @GetMapping
    public List<AsDataPermission> queryDataPermission() {
        return dataPermissionService.queryDataPermission();
    }

    @PostMapping("/user/authorize")
    public Boolean authorize(@RequestBody DataPermissionAuthorizeDto authorizeDto) {
        return dataPermissionService.authorize(authorizeDto);
    }

    @GetMapping("/user/initDataPermission")
    public void initDataPermission() {
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        Long companyId = cusUser.getCompanyId();
        Long userId = cusUser.getId();
        Boolean isAdmin = cusUser.getIsAdmin();
        if (isAdmin && !redisService.hasKey(RedisConstant.modelDataScope(companyId, userId))) {
            dataPermissionService.initDataPermission(companyId, userId, BaseConstant.ADMIN_ROLE);
        }
    }

}
