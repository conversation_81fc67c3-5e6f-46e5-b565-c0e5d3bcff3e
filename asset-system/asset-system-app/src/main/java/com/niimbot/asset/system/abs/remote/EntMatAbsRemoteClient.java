package com.niimbot.asset.system.abs.remote;

import com.niimbot.asset.system.abs.EntMatAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.equipment.abs.impl.EntMatAbsImpl")
@FeignClient(name = "asset-equipment", url = "https://{gateway}/client/abs/equipment/equipmentAbs/")
public interface EntMatAbsRemoteClient extends EntMatAbs {
}
