package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.NewbieTaskConstant;
import com.niimbot.asset.system.mapper.AsCompanyNewbieTaskMapper;
import com.niimbot.asset.system.model.AsCompanyNewbieTask;
import com.niimbot.asset.system.model.AsNewbieTaskConfig;
import com.niimbot.asset.system.service.CompanyNewbieTaskService;
import com.niimbot.asset.system.service.NewbieTaskConfigService;
import com.niimbot.system.CompanyNewbieTaskDto;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CompanyNewbieTaskServiceImpl extends ServiceImpl<AsCompanyNewbieTaskMapper, AsCompanyNewbieTask> implements CompanyNewbieTaskService {

    @Resource
    private NewbieTaskConfigService newbieTaskConfigService;

    @Override
    public List<CompanyNewbieTaskDto> listTask(Long companyId) {
        List<CompanyNewbieTaskDto> companyNewbieTaskDtos = this.getBaseMapper().listTask(companyId);
        List<CompanyNewbieTaskDto> sorted = new ArrayList<>(companyNewbieTaskDtos.size());
        // 排序 待完成>已忽略>已完成，其中待完成状态的任务，按照后台排序值从大到小排序；
        Map<Integer, List<CompanyNewbieTaskDto>> collect = companyNewbieTaskDtos.stream().collect(Collectors.groupingBy(CompanyNewbieTaskDto::getStatus));
        Optional.ofNullable(collect.get(NewbieTaskConstant.TASK_STATUS_INIT)).ifPresent(sorted::addAll);
        Optional.ofNullable(collect.get(NewbieTaskConstant.TASK_STATUS_IGNORED)).ifPresent(sorted::addAll);
        Optional.ofNullable(collect.get(NewbieTaskConstant.TASK_STATUS_COMPLETED)).ifPresent(taskDtos -> sorted.addAll(taskDtos.stream().sorted(Comparator.comparingInt(CompanyNewbieTaskDto::getSort)).collect(Collectors.toList())));
        return sorted;
    }

    @Override
    public void init(Long companyId) {
        List<AsNewbieTaskConfig> configs = newbieTaskConfigService.list();
        if (CollUtil.isEmpty(configs)) {
            return;
        }
        List<AsCompanyNewbieTask> collect = configs.stream().map(config -> new AsCompanyNewbieTask().setCompanyId(companyId).setTaskId(config.getId()).setStatus(NewbieTaskConstant.TASK_STATUS_INIT)).collect(Collectors.toList());
        this.saveBatch(collect);
    }
}
