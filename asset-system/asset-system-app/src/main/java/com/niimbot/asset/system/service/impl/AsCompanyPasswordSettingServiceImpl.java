package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.support.RedisDistributeLock;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.constant.CompanyPasswordConstant;
import com.niimbot.asset.system.mapper.AsCompanyPasswordSettingMapper;
import com.niimbot.asset.system.model.AsAccountEmployee;
import com.niimbot.asset.system.model.AsCompanyPasswordSetting;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.service.AsAccountEmployeeService;
import com.niimbot.asset.system.service.AsCompanyPasswordSettingService;
import com.niimbot.asset.system.service.AsCompanyWeakPasswordService;
import com.niimbot.asset.system.service.CusUserService;
import com.niimbot.asset.system.util.NumberUtils;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.CompanyPasswordSettingDto;
import com.niimbot.system.enums.PasswordLimitEnum;
import com.niimbot.system.enums.PasswordOtherLimitEnum;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AsCompanyPasswordSettingServiceImpl extends ServiceImpl<AsCompanyPasswordSettingMapper, AsCompanyPasswordSetting> implements AsCompanyPasswordSettingService {

    @Resource
    private CusUserService userService;
    @Resource
    private RedisService redisService;
    @Resource
    private AsCompanyWeakPasswordService weakPasswordService;
    @Resource
    private AsAccountEmployeeService accountEmployeeService;
    @Resource
    private RedisDistributeLock redisDistributeLock;

    /**
     * 每天凌晨检索企业强更配置，将所有需要配置的员工写入Redis
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void schedule() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }

        redisDistributeLock.lock("companyPassword", 5, TimeUnit.MINUTES, (a) -> {
            LocalDateTime now = LocalDateTime.now();
            int day = now.getDayOfMonth();
            List<AsCompanyPasswordSetting> companyPasswordSettingList = list(Wrappers.lambdaQuery(AsCompanyPasswordSetting.class)
                    .select(AsCompanyPasswordSetting::getCompanyId,
                            AsCompanyPasswordSetting::getPasswordSwitch,
                            AsCompanyPasswordSetting::getUpdateMonth,
                            AsCompanyPasswordSetting::getUpdateDay,
                            AsCompanyPasswordSetting::getUpdateTime)
                    .eq(AsCompanyPasswordSetting::getPasswordSwitch, true)
                    .eq(AsCompanyPasswordSetting::getForceUpdate, true)
                    .eq(AsCompanyPasswordSetting::getUpdateDay, day)
                    .ne(AsCompanyPasswordSetting::getCompanyId, 0));

            for (AsCompanyPasswordSetting setting : companyPasswordSettingList) {
                // 判断是否间隔月份
                Integer updateMonth = setting.getUpdateMonth();
                LocalDateTime updateTime = setting.getUpdateTime();
                if (updateTime == null || updateMonth == null) {
                    continue;
                }
                // 比较月份
                LocalDate start = LocalDate.of(updateTime.getYear(), updateTime.getMonth(), 1);
                LocalDate end = LocalDate.of(now.getYear(), now.getMonth(), 1);
                Period between = Period.between(start, end);
                int monthDiff = between.getYears() * 12 + between.getMonths();

                // 整除说明整月需要更新
                if (monthDiff > 0 && monthDiff % updateMonth == 0) {
                    List<AsAccountEmployee> accountEmployeeList = accountEmployeeService.list(Wrappers.lambdaQuery(AsAccountEmployee.class)
                            .eq(AsAccountEmployee::getCompanyId, setting.getCompanyId()));
                    redisService.sAdd(CompanyPasswordConstant.companyUpdatePwdAccountKey(setting.getCompanyId()), accountEmployeeList
                            .stream().map(f -> Convert.toStr(f.getAccountId())).toArray());
                }
            }
        });
    }

    @Override
    public Boolean saveOrUpdate(CompanyPasswordSettingDto settingDto) {
        AsCompanyPasswordSetting setting = this.getOne(Wrappers.lambdaQuery(AsCompanyPasswordSetting.class));
        if (setting == null) {
            setting = new AsCompanyPasswordSetting();
        }
        List<String> pwdList = settingDto.getPasswordLimit();
        List<String> otherList = settingDto.getOtherLimit();
        String passwordLimit = null;
        String otherLimit = null;
        if (CollectionUtil.isNotEmpty(pwdList)) {
            passwordLimit = String.join(",", pwdList);
        }
        if (CollectionUtil.isNotEmpty(otherList)) {
            otherLimit = String.join(",", otherList);
        }
        setting.setPasswordSwitch(true);
        setting.setPasswordLowestDigit(settingDto.getPasswordLowestDigit());
        setting.setPasswordHighDigit(settingDto.getPasswordHighDigit());
        setting.setForceUpdate(settingDto.getForceUpdate());
        if (settingDto.getForceUpdate()) {
            if (!NumberUtils.rangeInDefined(settingDto.getUpdateDay(), 1, 31) || !NumberUtils.rangeInDefined(settingDto.getUpdateMonth(), 1, 12)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "更新天数或更新月份不合法");
            }
        setting.setUpdateMonth(settingDto.getUpdateMonth());
        setting.setUpdateDay(settingDto.getUpdateDay());
        }
        setting.setPasswordLimit(passwordLimit);
        setting.setOtherLimit(otherLimit);
        saveOrUpdate(setting);
        redisService.del(CompanyPasswordConstant.companyPasswordSettingKey(LoginUserThreadLocal.getCompanyId()));

        // 所有用户都需要修改密码
        List<AsAccountEmployee> accountEmployeeList = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        redisService.sAdd(CompanyPasswordConstant.companyUpdatePwdAccountKey(setting.getCompanyId()), accountEmployeeList
                .stream().map(f -> Convert.toStr(f.getAccountId())).toArray());
        return true;
    }


    @Override
    public void checkPwdByRule(String pwd, Long companyId) {
        if (StrUtil.isEmpty(pwd)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "密码不能为空");
        }
        // 查询配置
        CompanyPasswordSettingDto passwordSetting = getSettingDetail(companyId, false);
        if (passwordSetting != null) {
            // 没有开启则读取系统默认配置
            if (BooleanUtil.isFalse(passwordSetting.getPasswordSwitch())) {
                passwordSetting = getDefaultSettingDetail();
            }
            // 判断最低位数
            Integer passwordLowestDigit = passwordSetting.getPasswordLowestDigit();
            if (pwd.length() < passwordLowestDigit) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "密码最低位要求" + passwordLowestDigit + "位");
            }

            Integer passwordHighDigit = passwordSetting.getPasswordHighDigit();
            if (pwd.length() > passwordSetting.getPasswordHighDigit()) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "密码最高位要求" + passwordHighDigit + "位");
            }
            // 判断大小写数字字符配置
            List<String> passwordLimit = passwordSetting.getPasswordLimit();
            PasswordLimitEnum.check(pwd, passwordLimit);
            // 判断其他规则限制
            List<String> otherLimit = passwordSetting.getOtherLimit();
            List<String> weakPwdList = weakPasswordService.weakPwdList();
            PasswordOtherLimitEnum.check(pwd, otherLimit, weakPwdList);
        }
    }

    @Override
    public CompanyPasswordSettingDto getSettingDetail(Long companyId, boolean isSetting) {
        Object setting = redisService.get(CompanyPasswordConstant.companyPasswordSettingKey(companyId));
        CompanyPasswordSettingDto settingDto;
        if (ObjectUtil.isNull(setting)) {
            AsCompanyPasswordSetting one = getOne(Wrappers.lambdaQuery(AsCompanyPasswordSetting.class)
                    .eq(AsCompanyPasswordSetting::getCompanyId, companyId));
            // 没有密码配置
            if (one == null) {
                if (BooleanUtil.isTrue(isSetting)) {
                    CompanyPasswordSettingDto companyPasswordSettingDto = new CompanyPasswordSettingDto();
                    companyPasswordSettingDto.setPasswordLowestDigit(10)
                            .setPasswordHighDigit(20)
                            .setForceUpdate(true)
                            .setUpdateDay(1)
                            .setUpdateMonth(1)
                            .setPasswordLimit(Stream.of(PasswordLimitEnum.values()).map(PasswordLimitEnum::getCode).collect(Collectors.toList()))
                            .setOtherLimit(Stream.of(PasswordOtherLimitEnum.values()).map(PasswordOtherLimitEnum::getCode).collect(Collectors.toList()));
                    return companyPasswordSettingDto;
                } else {
                    return getDefaultSettingDetail();
                }
            } else {
                settingDto = toDto(one);
                redisService.set(CompanyPasswordConstant.companyPasswordSettingKey(companyId), settingDto);
            }
        } else {
            settingDto = (CompanyPasswordSettingDto) setting;
        }
        // 判断是否需要读取系统配置
        if (BooleanUtil.isFalse(isSetting) && BooleanUtil.isFalse(settingDto.getPasswordSwitch())) {
            return getDefaultSettingDetail();
        }
        return settingDto;
    }

    private CompanyPasswordSettingDto getDefaultSettingDetail() {
        Object setting = redisService.get(CompanyPasswordConstant.COMPANY_PASSWORD_DEFAULT_SETTING);
        if (ObjectUtil.isNull(setting)) {
            AsCompanyPasswordSetting defaultSetting = getBaseMapper().getDefaultSetting();
            if (defaultSetting != null) {
                CompanyPasswordSettingDto settingDto = toDto(defaultSetting);
                redisService.set(CompanyPasswordConstant.COMPANY_PASSWORD_DEFAULT_SETTING, settingDto);
                return settingDto;
            } else {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "缺失系统默认密码配置");
            }
        }
        return (CompanyPasswordSettingDto) setting;
    }

    private CompanyPasswordSettingDto toDto(AsCompanyPasswordSetting setting) {
        CompanyPasswordSettingDto settingDto = BeanUtil.copyProperties(setting, CompanyPasswordSettingDto.class);
        if (StrUtil.isNotBlank(setting.getPasswordLimit())) {
            settingDto.setPasswordLimit(ListUtil.toList(setting.getPasswordLimit().split(",")));
        }
        if (StrUtil.isNotBlank(setting.getOtherLimit())) {
            settingDto.setOtherLimit(ListUtil.toList(setting.getOtherLimit().split(",")));
        }
        return settingDto;
    }

    @Override
    public void markChangePwd(Long companyId, Long accountId) {
        CompanyPasswordSettingDto settingDetail = getSettingDetail(companyId, true);
        if (BooleanUtil.isTrue(settingDetail.getPasswordSwitch())) {
            redisService.sAdd(CompanyPasswordConstant.companyUpdatePwdAccountKey(companyId), Convert.toStr(accountId));
        }
    }

    @Override
    public String getLimitWords() {
        CompanyPasswordSettingDto settingDetail = getSettingDetail(LoginUserThreadLocal.getCompanyId(), false);
        return buildLimitWords(settingDetail);
    }

    @Override
    public String getLimitWordsByMobile(String mobile) {
        if (mobile == null) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "手机号不可为空");
        } else {
            AsCusUser cusUser = userService.getOne(Wrappers.lambdaQuery(AsCusUser.class)
                    .eq(AsCusUser::getMobile, mobile), false);
            if (cusUser == null) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "手机号" + mobile + "系统中不存在");
            }
            CompanyPasswordSettingDto settingDetail = getSettingDetail(cusUser.getCompanyId(), false);
            return buildLimitWords(settingDetail);
        }
    }

    private String buildLimitWords(CompanyPasswordSettingDto setting) {
        StringBuilder limitWords = new StringBuilder();
        if (CollUtil.isNotEmpty(setting.getPasswordLimit())) {
            limitWords.append("1." + "请输入" + setting.getPasswordLowestDigit() + "以上"+setting.getPasswordHighDigit()+"以下包含");
            for (String s : setting.getPasswordLimit()) {
                String desc = PasswordLimitEnum.match(s).getDesc();
                limitWords.append(desc+"、");
            }
            limitWords.append("在内的密码。\n");
            limitWords.deleteCharAt(limitWords.lastIndexOf("、"));
        }
        if (CollUtil.isNotEmpty(setting.getOtherLimit())) {
            limitWords.append("2.");
            for (String s : setting.getOtherLimit()) {
                PasswordOtherLimitEnum match = PasswordOtherLimitEnum.match(s);
                if (match != null) {
                    limitWords.append(match.getDesc()).append("、");
                }
            }
            limitWords.deleteCharAt(limitWords.lastIndexOf("、")).append("。");
        }
        return limitWords.toString();
    }

    @Override
    public Boolean closeSwitch() {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        update(Wrappers.<AsCompanyPasswordSetting>lambdaUpdate()
                .set(AsCompanyPasswordSetting::getPasswordSwitch, false)
                .eq(AsCompanyPasswordSetting::getCompanyId, companyId));
        redisService.del(CompanyPasswordConstant.companyPasswordSettingKey(companyId));
        redisService.del(CompanyPasswordConstant.companyUpdatePwdAccountKey(companyId));
        return true;
    }
}
