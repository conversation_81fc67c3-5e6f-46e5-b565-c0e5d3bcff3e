package com.niimbot.asset.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.system.model.AsCusUser;
import com.niimbot.asset.system.model.AsUserCompany;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <AUTHOR>
 * @Date 2020/11/2
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsCusUserCompanyMapper.class)
public interface AsCusUserCompanyMapper extends BaseMapper<AsUserCompany> {
    /**
     * 查找公司管理员
     *
     * @param companyId companyId
     * @return 管理员信息
     */
    AsCusUser getCompanyManager(Long companyId);
}
