package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.autoconfig.AssetConfig;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.abs.AssetQueryViewAbs;
import com.niimbot.asset.system.abs.ThirdPartyAbs;
import com.niimbot.asset.system.dto.AssetQueryViewInitCmd;
import com.niimbot.asset.system.dto.ThirdPartyGetQry;
import com.niimbot.asset.system.dto.clientobject.ThirdPartyCO;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.asset.system.util.AccountNumberUtils;
import com.niimbot.framework.dataperm.constant.DataPermRuleType;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.kalimdor.hulk.model.TokenResponse;
import com.niimbot.system.*;
import com.niimbot.thirdparty.ThirdPartyUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountCenterServiceImpl implements AccountCenterService {

    private final RedisService redisService;

    private final CompanyService companyService;

    private final CusUserService accountUserService;

    private final AsCusEmployeeService employeeService;

    private final AsCusEmployeeExtService employeeExtService;

    private final AsAccountEmployeeService accountEmployeeService;

    private final CusRoleService employeeRoleService;

    private final ThirdPartyAbs thirdPartyAbs;

    private final AssetQueryViewAbs assetQueryViewAbs;

    private final AsThirdPartyEmployeeService thirdPartyEmployeeService;

    private final AsThirdPartyAccountService thirdPartyAccountService;

    private final AsDataPermissionService dataPermissionService;

    private final CusRoleService roleService;

    private final AsOpenApiService openApiService;

    private final ChangeCusUserPasswordService changeCusUserPasswordService;

    @Override
    public List<AccountEmployeeDto> accountCompanyList(Long accountId) {
        List<AsAccountEmployee> accountEmployees = getAccountEmployees(accountId);
        if (CollUtil.isEmpty(accountEmployees)) {
            return Collections.emptyList();
        }
        List<AccountEmployeeDto> result = new ArrayList<>(6);
        for (AsAccountEmployee index : accountEmployees) {
            AsCompany company = companyService.getById(index.getCompanyId());
            // 禁用与注销状态的企业不展示
            if (Objects.isNull(company) || company.getStatus().equals(2) || company.getStatus().equals(5)) {
                continue;
            }
            AsCusEmployee employee = employeeService.getById(index.getEmployeeId());
            if (Objects.isNull(employee) || !employee.getStatus().equals(1)) {
                continue;
            }
            result.add(new AccountEmployeeDto().setAccountId(accountId).setCompanyId(company.getId()).setCompanyName(company.getName()).setEmployeeId(employee.getId()));
        }
        return result;
    }

    @Override
    public AsCusUser getAccountById(Long id) {
        return accountUserService.getById(id);
    }

    @Override
    public AsCusUser getAccountByMobile(String mobile) {
        return accountUserService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getMobile, mobile));
    }

    @Override
    public AsCusUser getAccountByNo(String accountNo) {
        return Optional.ofNullable(
                accountUserService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getAccount, accountNo))
        ).orElseThrow(() -> new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST));
    }

    @Override
    public AsCusUser getAccountByEmail(String email) {
        // return Optional.ofNullable(
        return accountUserService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getEmail, email));
        // ).orElseThrow(() -> new BusinessException(SystemResultCode.USER_PHONE_NOT_EXIST));
    }

    @Override
    public AsCusUser getAccountByWay(String way) {
        return Optional.ofNullable(
                accountUserService.getOne(
                        Wrappers.lambdaQuery(AsCusUser.class)
                                .eq(AsCusUser::getAccount, way)
                                .or()
                                .eq(AsCusUser::getMobile, way)
                                .or()
                                .eq(AsCusUser::getEmail, way)
                )
        ).orElseThrow(() -> new BusinessException(SystemResultCode.ACCOUNT_NOT_EXIST, way));
    }

    @Override
    public AsCusUser getAccountByThirdPartyUserUnionId(String type, String unionId) {
        AsThirdPartyAccount thirdPartyAccount = thirdPartyAccountService.getThirdPartyAccount(type, unionId).orElseThrow(() -> new BusinessException(SystemResultCode.UNBOUND_SOCIAL));
        return Optional.ofNullable(accountUserService.getById(thirdPartyAccount.getAccountId())).orElseThrow(() -> new BusinessException(SystemResultCode.ACCOUNT_NOT_EXIST, "第三方授权"));
    }

    @Override
    public AsCusEmployee chooseEmployee(AsCusUser account) {
        // 账号是否关联了员工
        List<AsAccountEmployee> accountEmployees = getAccountEmployees(account);
        if (CollUtil.isEmpty(accountEmployees)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
        }
        // 防止员工被清掉了但是关联关系没有被清掉
        List<AsCusEmployee> employees = employeeService.list(
                Wrappers.lambdaQuery(AsCusEmployee.class)
                        .in(AsCusEmployee::getId, accountEmployees.stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toSet()))
        );
        if (CollUtil.isEmpty(employees)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
        }
        // 已绑定账号的员工数量大于查询出来员工数量
        // accountEmployeeIds 1476457989316677632 1496743451478331392
        // existEmployeeIds 1476457989316677632
        Set<Long> existEmployeeIds = employees.stream().map(AsCusEmployee::getId).collect(Collectors.toSet());
        if (accountEmployees.size() > employees.size()) {
            // Set<Long> accountEmployeeIds = accountEmployees.stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toSet());
            accountEmployees.removeIf(accountEmployee -> !existEmployeeIds.contains(accountEmployee.getEmployeeId()));
            // boolean removeAll = accountEmployeeIds.removeAll(existEmployeeIds);
            // if (removeAll) {
            //     accountEmployeeService.remove(Wrappers.lambdaUpdate(AsAccountEmployee.class).in(AsAccountEmployee::getEmployeeId, accountEmployeeIds));
            // }
        }
        if (CollUtil.isEmpty(accountEmployees)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
        }
        // 剔除企业状态为禁用的员工
        List<Long> companies = companyService.list(
                Wrappers.lambdaQuery(AsCompany.class)
                        .select(AsCompany::getId)
                        .in(AsCompany::getStatus, 1, 3, 4)
                        .in(AsCompany::getId, employees.stream().map(AsCusEmployee::getCompanyId).collect(Collectors.toSet()))
        ).stream().map(AsCompany::getId).collect(Collectors.toList());
        // 没有一个启用的企业
        if (CollUtil.isEmpty(companies)) {
            throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN);
        }
        // 过滤
        if (accountEmployees.size() > companies.size()) {
            accountEmployees.removeIf(employee -> !companies.contains(employee.getCompanyId()));
        }
        if (CollUtil.isEmpty(accountEmployees)) {
            throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN);
        }
        // 账号只关联了一个企业员工
        Long onlyEmployeeId = accountEmployees.get(0).getEmployeeId();
        if (accountEmployees.size() == 1) {
            return checkAndGetEmployee(onlyEmployeeId, account.getId());
        }
        // 账号上次登录的企业员工
        // Map<Long, Long> companyMap = accountEmployees.stream().collect(Collectors.toMap(AsAccountEmployee::getCompanyId, AsAccountEmployee::getEmployeeId));
        Map<Long, Long> companyMap = new HashMap<>();
        accountEmployees.forEach(ae -> companyMap.put(ae.getCompanyId(), ae.getEmployeeId()));
        if (companyMap.containsKey(account.getCompanyId())) {
            Long employeeId = companyMap.get(account.getCompanyId());
            return checkAndGetEmployee(employeeId, account.getId());
        }
        // 账号最近关联的企业员工
        return checkAndGetEmployee(onlyEmployeeId, account.getId());
    }

    private CusUserDto buildUserInfo(AsCusUser account, AsCusEmployee employee) {
        if (Objects.isNull(account) || Objects.isNull(employee)) {
            return null;
        }
        CusUserDto dto = new CusUserDto()
                // 账户信息
                .setAccountId(account.getId()).setAccount(account.getAccount()).setUnionId(account.getUnionId()).setPassword(account.getPassword())
                .setEmail(account.getEmail()).setStatus(account.getStatus())
                .setAgreementStatus(account.getAgreementStatus()).setGuideStatus(account.getGuideStatus())
                // 员工信息
                .setId(employee.getId()).setCompanyId(employee.getCompanyId()).setMobile(employee.getMobile()).setDataScope(employee.getDataScope());
        AsCusEmployeeExt employeeExt = employeeExtService.getById(employee.getId());
        if (Objects.nonNull(employeeExt)) {
            // 员工扩展信息
            dto.setDefaultTagId(employeeExt.getDefaultTagId())
                    .setDefaultCftagId(employeeExt.getDefaultCftagId())
                    .setDefaultCftagCode(employeeExt.getDefaultCftagCode())
                    .setDefaultTplId(employeeExt.getDefaultTplId());
        }
        List<CusRoleDto> roles = employeeRoleService.getRoleByEmployeeId(employee.getId());
        if (!CollUtil.isEmpty(roles)) {
            // 员工角色超管标识
            List<String> roleNames = roles.stream().map(CusRoleDto::getRoleCode).collect(Collectors.toList());
            dto.setIsAdmin(roleNames.contains(BaseConstant.ADMIN_ROLE));
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = BusinessException.class)
    public CusUserDto getLoginInfoByMobile(String mobile, String smsCode) {
        AsCusUser account = getAccountByMobile(mobile);
        // 账号不存在时去创建
        if (Objects.isNull(account)) {
            log.info("通过手机号验证码登录时创建账号 : [mobile:{},smsCode:{}]", mobile, smsCode);
            account = createAccount(mobile, smsCode, null, null);
        }
        AsCusEmployee employeeOfLogin = null;
        try {
            employeeOfLogin = chooseEmployee(account);
        } catch (Exception e) {
            // 账号无关联的员工
        }
        // 2.5.1.0 对预激活员工的处理 手机号改为员工的
        Map<Long, AsCusEmployee> employees = employeeService.list(
                Wrappers.lambdaQuery(AsCusEmployee.class)
                        .eq(AsCusEmployee::getMobile, mobile)
        ).stream().collect(Collectors.toMap(AsCusEmployee::getId, employee -> employee));
        if (CollUtil.isEmpty(employees)) {
            // 无预激活的员工且账号无关联的员工
            if (Objects.isNull(employeeOfLogin)) {
                throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
            }
            return buildUserInfo(account, employeeOfLogin);
        }
        List<AsCusEmployeeExt> employeeExts = employeeExtService.listByIds(employees.keySet());
        List<AsCusEmployeeExt> isToBeList = employeeExts.stream().filter(asCusEmployeeExt -> asCusEmployeeExt.getAccountStatus().equals(2)).collect(Collectors.toList());
        if (CollUtil.isEmpty(isToBeList)) {
            // 无预激活的员工且账号无关联的员工
            if (Objects.isNull(employeeOfLogin)) {
                throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
            }
            return buildUserInfo(account, employeeOfLogin);
        }
        // 如果有预激活的员工就去激活然后选择任意一个企业登录
        List<String> companyNames = new ArrayList<>();
        AsCusEmployee preEmployee = null;
        for (AsCusEmployeeExt data : isToBeList) {
            AsCusEmployee employee = employees.get(data.getId());
            AsCompany company = companyService.getById(employee.getCompanyId());
            if (!(Objects.nonNull(company) && company.getStatus() != 2 && company.getStatus() != 5)) {
                continue;
            }
            if (!accountIsBind(employee.getCompanyId(), account.getId(), false)) {
                bindEmployee(employee.getCompanyId(), employee.getId(), account.getId(), false);
                // 初始化数据权限
                dataPermissionService.initDataPermission(employee.getCompanyId(), employee.getId(), BaseConstant.COMMON_ROLE);
                assetQueryViewAbs.initUserView(new AssetQueryViewInitCmd().setCompanyId(employee.getCompanyId()).setEmployeeId(employee.getId()));
                companyNames.add(company.getName());
                // 要登录的员工
                preEmployee = employee;
            }
        }
        if (CollUtil.isEmpty(companyNames)) {
            throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN);
        }
        CusUserDto userDto = buildUserInfo(account, preEmployee);
        if (Objects.isNull(userDto)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP);
        }
        return userDto.setPreActivated(true).setCompanyNames(companyNames);
    }

    @Override
    public CusUserDto getLoginInfoByNo(String no) {
        AsCusUser account = getAccountByNo(no);
        AsCusEmployee employee = chooseEmployee(account);
        return buildUserInfo(account, employee);
    }

    @Override
    public CusUserDto getLoginInfoByEmail(String email) {
        AsCusUser account = getAccountByEmail(email);
        AsCusEmployee employee = chooseEmployee(account);
        return buildUserInfo(account, employee);
    }

    @Override
    public CusUserDto getLoginInfoByWay(String way) {
        AsCusUser account = accountUserService.getOne(
                Wrappers.lambdaQuery(AsCusUser.class)
                        .eq(AsCusUser::getAccount, way)
                        .or()
                        .eq(AsCusUser::getMobile, way)
                        .or()
                        .eq(AsCusUser::getEmail, way)
                        .or()
                        .eq(AsCusUser::getUnionId, way)
        );
        if (Objects.isNull(account)) {
            // 通过员工表的工号查询和账号的关联关系
            // 获取员工服务实例
            AsCusEmployee employee = employeeService.getOne(
                    Wrappers.lambdaQuery(AsCusEmployee.class)
                            .eq(AsCusEmployee::getEmpNo, way)
            );
            if (employee == null) {
                return null;
            }
            Optional<AsAccountEmployee> accountEmployeeOpt = accountEmployeeService.getEmployeeAccount(employee.getId());
            if (!accountEmployeeOpt.isPresent()) {
                return null;
            }
            account = accountUserService.getById(accountEmployeeOpt.get().getAccountId());
            if (Objects.isNull(account)) {
                return null;
            }
        }
        AsCusEmployee employee = chooseEmployee(account);
        return buildUserInfo(account, employee);
    }

    @Override
    public CusUserDto getLoginInfoByAppKey(String appKey, String appSecret) {
        AsOpenApi openApi = openApiService.getOne(Wrappers.lambdaQuery(AsOpenApi.class)
                .eq(AsOpenApi::getAppKey, appKey)
                .eq(AsOpenApi::getAppSecret, appSecret));
        if (Objects.isNull(openApi)) {
            return null;
        }
        CusUserDto dto = new CusUserDto();
        dto.setAccountId(openApi.getEmpId())
                .setId(openApi.getEmpId())
                .setCompanyId(openApi.getCompanyId())
                .setDataScope(Convert.toShort(DataPermRuleType.TYPE_ALL.getValue()))
                .setIsAdmin(true);
        return dto;
    }

    @Override
    public CusUserDto getLoginInfoByThirdParty(String type, String uniqueId) {
        Optional<AsThirdPartyAccount> thirdPartyAccountOptional = thirdPartyAccountService.getThirdPartyAccount(type, uniqueId);
        if (!thirdPartyAccountOptional.isPresent()) {
            throw new BusinessException(SystemResultCode.UNBOUND_SOCIAL);
        }
        AsCusUser account = accountUserService.getById(thirdPartyAccountOptional.get().getAccountId());
        AsCusEmployee employee = chooseEmployee(account);
        return buildUserInfo(account, employee);
    }

    @Override
    public CusUserDto getLoginInfoByThirdPartyInnerApp(String type, String userId) {
        // 对应员工
        AsThirdPartyEmployee thirdPartyEmployee = thirdPartyEmployeeService.getOne(
                Wrappers.lambdaQuery(AsThirdPartyEmployee.class)
                        .eq(AsThirdPartyEmployee::getType, type)
                        .eq(AsThirdPartyEmployee::getUserId, userId)
                        .last("LIMIT 1"),
                false
        );
        if (Objects.isNull(thirdPartyEmployee)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "您的认证账号没有加入企业员工信息中，请先联系管理员添加员工信息");
        }
        // 对应员工
        AsCusEmployee employee = employeeService.getById(thirdPartyEmployee.getEmployeeId());
        if (Objects.isNull(employee)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "您的认证账号没有加入企业员工信息中，请先联系管理员添加员工信息");
        }
        // 是否有账号
        Optional<AsAccountEmployee> accountEmployeeOptional = accountEmployeeService.getEmployeeAccount(employee.getId());
        if (!accountEmployeeOptional.isPresent()) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "您的认证账号未激活，请先联系管理员激活账号");
        }
        AsCusUser account = accountUserService.getById(accountEmployeeOptional.get().getAccountId());
        return buildUserInfo(account, employee);
    }

    @Override
    public CusUserDto getEmpLoginInfo(Long empId) {
        AsCusEmployee employee = employeeService.getById(empId);
        if (Objects.isNull(employee)) {
            return null;
        }
        Optional<AsCusUser> account = accountEmployeeService.getEmployAccount(employee.getId());
        return account.map(cusUser -> buildUserInfo(cusUser, employee)).orElse(null);
    }

    @Override
    public CusUserDto getCompanyEmpLoginInfo(Long accountId, Long companyId) {
        // 企业是否被禁用或注销状态
        checkCompany(companyId);
        AsAccountEmployee accountEmployee = accountEmployeeService.getOne(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getAccountId, accountId)
                        .eq(AsAccountEmployee::getCompanyId, companyId)
        );
        if (Objects.isNull(accountEmployee)) {
            return null;
        }
        AsCusUser account = accountUserService.getById(accountId);
        AsCusEmployee employee = employeeService.getById(accountEmployee.getEmployeeId());
        return buildUserInfo(account, employee);
    }

    @Override
    public Boolean deleteEmployeeAccount(Long empId) {
        return accountEmployeeService.unbindEmployee(empId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteEmployeeAccount(List<Long> empIds) {
        boolean flag = accountEmployeeService.batchUnbindEmploy(empIds);
        if (flag) {
            if (Edition.isLocal()) {
                employeeService.initEmployeeRoleDataScope(empIds.toArray(new Long[0]));
                employeeService.removeCusEmployee(empIds);
                employeeExtService.removeByIds(empIds);
                accountUserService.removeByIds(empIds);
            } else {
                employeeExtService.update(Wrappers.lambdaUpdate(AsCusEmployeeExt.class).set(AsCusEmployeeExt::getAccountStatus, 1).in(AsCusEmployeeExt::getId, empIds));
                employeeService.initEmployeeRoleDataScope(empIds.toArray(new Long[0]));
            }
        }
        return true;
    }

    @Override
    public Boolean deleteCompanyAccount(Long companyId, Long accountId) {
        return accountEmployeeService.unbindCompany(companyId, accountId);
    }

    private AsCusEmployee checkAndGetEmployee(Long employeeId, Long accountId) {
        // AsCusEmployee employee = Optional.ofNullable(employeeService.getById(employeeId)).orElseThrow(() -> new BusinessException(SystemResultCode.ACCOUNT_NOT_ASSOCIATION_EMP));
        // 检查员工
        AsCusEmployee employee = employeeService.getById(employeeId);
        if (Objects.isNull(employee) || employee.getStatus() != 1) {
            accountEmployeeService.unbindEmployee(employeeId);
            throw new BusinessException(SystemResultCode.EMP_NOT_EXISTS);
        }
        // 更新账号中最后一次登录的企业ID
        updateLastCompany(accountId, employee.getCompanyId());
        return employee;
    }

    private AsCusUser createAccount(String mobile, String smsCode, Long companyId, String nickname) {
        // 事务内的锁，如果后续操作执行时间较长。可能还是会重复创建账号，锁提前至整个方法调用时 全局锁
        // RLock lock = redissonClient.getLock(RedisConstant.createAccountLockKey(mobile));
        // if (lock.isLocked()) {
        //     log.info("正在为手机号[{}]创建系统账号", mobile);
        //     throw new BusinessException(SystemResultCode.PARAM_REPEAT_SUBMIT);
        // }
        // lock.lock();
        // try {
        AsCusUser one = accountUserService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getMobile, mobile));
        if (Objects.nonNull(one)) {
            return one;
        }
        String maxAccount = AccountNumberUtils.getMaxAccount();
        AsCusUser account = new AsCusUser()
                .setId(IdUtils.getId())
                .setNickname(nickname)
                .setMobile(mobile)
                .setAccount(maxAccount)
                .setSource(1);
        if (Objects.nonNull(companyId)) {
            account.setCompanyId(companyId);
        }
        if (StringUtils.isEmpty(nickname)) {
            account.setNickname(mobile);
        }
        // 去中台注册一下
        if (BaseConstant.EDITION_SAAS.equals(SpringUtil.getBean(AssetConfig.class).getEdition())) {
            // 中台用户的UID 存入账号信息中
            try {
                TokenResponse response = SpringUtil.getBean(UserExtService.class).getUnionIdBySmsCode(mobile, smsCode);
                if (Objects.nonNull(response) && StringUtils.hasText(response.getUid())) {
                    account.setUnionId(response.getUid());
                }
            } catch (Exception e) {
                // ignore
                log.error("创建账号[{}]时去中台注册异常", mobile, e);
            }
        }
        accountUserService.save(account);
        return account;
        // }
        // finally {
        //     lock.unlock();
        // }
    }

    private void checkCompany(Long companyId) {
        AsCompany company = companyService.getById(companyId);
        if (Objects.isNull(company)) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        if (company.getStatus().equals(2) || company.getStatus().equals(5)) {
            throw new BusinessException(SystemResultCode.COMPANY_FORBIDDEN);
        }
    }

    @Override
    public ActivateAccountResult activateAccountByMobile(Long companyId, String mobile, String nationalCode, String smsCode) {
        checkCompany(companyId);
        //校验区号和手机号一致
        String registerKey = "register_key:" + mobile;
        if (redisService.hasKey(registerKey)) {
            if (!redisService.get(registerKey).equals(nationalCode)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "国际区号与手机号不匹配，请检查国际区号！");
            }
        }
        // 手机号+企业ID确定唯一员工
        AsCusEmployee employee = employeeService.getOne(
                Wrappers.lambdaQuery(AsCusEmployee.class)
                        .eq(AsCusEmployee::getCompanyId, companyId)
                        .eq(AsCusEmployee::getMobile, mobile)
        );
        if (Objects.isNull(employee)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "您的手机号没有加入企业员工信息中，请先联系管理员添加员工信息");
        }
        ActivateAccountResult result = new ActivateAccountResult();
        result.setIsNewAccount(false);
        Optional<AsAccountEmployee> employeeAccount = accountEmployeeService.getEmployeeAccount(employee.getId());
        // 员工已经激活过账号，返回访问凭证
        if (employeeAccount.isPresent()) {
            AsAccountEmployee accountEmployee = employeeAccount.get();
            AsCusUser accountExist = accountUserService.getById(accountEmployee.getAccountId());
            result.setUserDto(buildUserInfo(accountExist, employee));
            return result;
        }
        // 当前手机号是否有已存在的账号
        AsCusUser account = accountUserService.getOne(
                Wrappers.lambdaQuery(AsCusUser.class)
                        .eq(AsCusUser::getMobile, mobile)
        );
        // 新增账号 初始化账号相关信息
        if (Objects.isNull(account)) {
            log.info("通过手机号激活账号时创建账号 : [mobile:{}]", mobile);
            account = createAccount(employee.getMobile(), smsCode, companyId, employee.getEmpName());
            result.setIsNewAccount(true);
        }
        // 绑定账号
        accountIsBind(employee.getCompanyId(), account.getId(), true);
        if (!bindEmployee(employee.getCompanyId(), employee.getId(), account.getId(), true)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "激活账号失败，请稍后再试");
        }
        CusUserDto cusUserDto = buildUserInfo(account, employee);
        result.setUserDto(cusUserDto);
        updateLastCompany(account.getId(), employee.getCompanyId());
        //更新最新区号到员工表
        employeeService.update(new UpdateWrapper<AsCusEmployee>().lambda()
                .set(AsCusEmployee::getNationalCode, nationalCode)
                .eq(AsCusEmployee::getCompanyId, companyId)
                .eq(AsCusEmployee::getMobile, mobile)
        );

        //查询企业默认角色
        AsCusRole cusRole = roleService.queryDefaultRole(companyId);
        if (Objects.isNull(cusRole)) {
            // 初始化数据权限
            dataPermissionService.initDataPermission(companyId, employee.getId(), BaseConstant.COMMON_ROLE);
        } else {
            // 初始化数据权限
            dataPermissionService.initDataPermission(companyId, employee.getId(), cusRole.getRoleCode());
        }
        assetQueryViewAbs.initUserView(new AssetQueryViewInitCmd().setCompanyId(companyId)
                .setEmployeeId(employee.getId()));
        return result;
    }

    private String getActivateAccountByThirdPartyKey(Long companyId, String uuid) {
        return "activate_account_by_third_party:" + companyId + ":" + uuid;
    }

    private ActivateAccountResult allowLogin(AsCusUser account, AsCusEmployee employee) {
        ActivateAccountResult result = new ActivateAccountResult();
        result.setNeedBindAccount(false);
        // 账号有效返回登录凭证
        CusUserDto cusUserDto = buildUserInfo(account, employee);
        if (cusUserDto != null) {
            result.setUserDto(cusUserDto);
            // 更新此账号最近登录的企业
            updateLastCompany(account.getId(), employee.getCompanyId());
        }
        return result;
    }

    private ActivateAccountResult notAllowLogin(ThirdPartyUser thirdPartyUser, Long companyId) {
        ActivateAccountResult result = new ActivateAccountResult();
        String uuid = UUID.fastUUID().toString();
        result.setNeedBindAccount(true);
        redisService.set(getActivateAccountByThirdPartyKey(companyId, uuid), thirdPartyUser, TimeUnit.MINUTES.toSeconds(5));
        result.setUuid(uuid);
        return result;
    }

    @Override
    public ActivateAccountResult activateAccountByThirdParty(Long companyId, String thirdType, ThirdPartyUser thirdPartyUser) {
        checkCompany(companyId);
        // 获取员工
        Optional<AsThirdPartyEmployee> thirdPartyEmployeeOptional = thirdPartyEmployeeService.getOne(thirdType, thirdPartyUser.getUserId(), companyId);
        if (!thirdPartyEmployeeOptional.isPresent()) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "您的认证账号没有加入企业员工信息中，请先联系管理员添加员工信息");
        }
        AsCusEmployee employee = employeeService.getById(thirdPartyEmployeeOptional.get().getEmployeeId());
        if (Objects.isNull(employee)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "您的认证账号没有加入企业员工信息中，请先联系管理员添加员工信息");
        }
        thirdPartyUser.setEmployeeId(employee.getId()).setType(thirdType);
        Optional<AsAccountEmployee> employeeAccount = accountEmployeeService.getEmployeeAccount(employee.getId());
        // 员工已和账号有关联即已激活过账号
        if (employeeAccount.isPresent()) {
            AsCusUser account = accountUserService.getById(employeeAccount.get().getAccountId());
            // 校验此账号是否有效，如果无效就删除关联
            if (Objects.isNull(account)) {
                accountEmployeeService.removeById(employeeAccount.get().getId());
                throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "激活账号失败，请稍后再试");
            }
            return allowLogin(account, employee);
        }
        // 员工没有手机号，前端跳转去根据手机号绑定账号
        String mobile = employee.getMobile();
        if (!StringUtils.hasText(mobile)) {
            return notAllowLogin(thirdPartyUser, companyId);
        }
        // 员工没有绑定过账号即没有激活账号，查看员工对应的手机号是否有账号。有就绑定该账号并登录
        AsCusUser account = accountUserService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getMobile, mobile));
        if (Objects.nonNull(account)) {
            if (accountIsBind(companyId, account.getId(), false)) {
                return notAllowLogin(thirdPartyUser, companyId);
            }
            bindEmployee(companyId, employee.getId(), account.getId(), false);
            AsCusRole cusRole = roleService.queryDefaultRole(companyId);
            if (Objects.isNull(cusRole)) {
                // 初始化数据权限
                dataPermissionService.initDataPermission(companyId, employee.getId(), BaseConstant.COMMON_ROLE);
            } else {
                // 初始化数据权限
                dataPermissionService.initDataPermission(companyId, employee.getId(), cusRole.getRoleCode());
            }
            assetQueryViewAbs.initUserView(new AssetQueryViewInitCmd().setCompanyId(companyId)
                    .setEmployeeId(employee.getId()));
            return allowLogin(account, employee);
        }
        return notAllowLogin(thirdPartyUser, companyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = BusinessException.class)
    public ActivateAccountResult activateAccountByThirdPartyBindMobile(Long companyId, String mobile, String nationalCode, String smsCode, String uuid) {
        String key = getActivateAccountByThirdPartyKey(companyId, uuid);
        if (!redisService.hasKey(key)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "认证已超时，请重新认证激活账号");
        }
        //校验区号和手机号一致
        String registerKey = "register_key:" + mobile;
        if (redisService.hasKey(registerKey)) {
            if (!redisService.get(registerKey).equals(nationalCode)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "国际区号与手机号不匹配，请检查国际区号！");
            }
        }
        ThirdPartyUser thirdPartyUser = (ThirdPartyUser) redisService.get(key);
        AsCusEmployee employee = employeeService.getById(thirdPartyUser.getEmployeeId());
        if (Objects.isNull(employee)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "您的认证账号没有加入企业员工信息中，请先联系管理员添加员工信息");
        }
        ActivateAccountResult result = new ActivateAccountResult().setIsNewAccount(false);
        // 检查手机号是否有账号，没有账号时新建账号
        AsCusUser account = accountUserService.getOne(
                Wrappers.lambdaQuery(AsCusUser.class)
                        .eq(AsCusUser::getMobile, mobile)
        );
        if (Objects.isNull(account)) {
            log.info("第三方认证后通过手机号激活账号时创建账号 : mobile:{}", mobile);
            account = createAccount(mobile, smsCode, companyId, employee.getEmpName());
            result.setIsNewAccount(true);
        }
        // 绑定账号与第三方 企业微信目前只拿得到userId 钉钉传过来的unionId是使用用户自建应用的配置获取的，所以与我们自建应用拿到的unionID不一致。这里暂时绑定第三方与账号的关系
        // thirdPartyAccountService.bindOneIfNotExist(account.getId(), thirdPartyUser.getType(), thirdPartyUser.getOpenId(), thirdPartyUser.getUniqueId(), thirdPartyUser.getNickname(), thirdPartyUser.getAvatarUrl());
        // 绑定账号与员工
        Optional<AsAccountEmployee> accountEmployeeOptional = accountEmployeeService.getEmployeeAccount(thirdPartyUser.getEmployeeId());
        if (!accountEmployeeOptional.isPresent()) {
            accountIsBind(companyId, account.getId(), true);
            bindEmployee(companyId, employee.getId(), account.getId(), false);
            // 初始化数据权限
            dataPermissionService.initDataPermission(companyId, employee.getId(), BaseConstant.COMMON_ROLE);
            assetQueryViewAbs.initUserView(new AssetQueryViewInitCmd().setCompanyId(companyId)
                    .setEmployeeId(employee.getId()));
        }
        try {
            redisService.del(key);
        } catch (Exception e) {
            // ignore
        }
        //更新最新区号到员工表
        employeeService.update(new UpdateWrapper<AsCusEmployee>().lambda()
                .set(AsCusEmployee::getNationalCode, nationalCode)
                .eq(AsCusEmployee::getCompanyId, companyId)
                .eq(AsCusEmployee::getMobile, mobile)
        );
        CusUserDto cusUserDto = buildUserInfo(account, employee);
        if (cusUserDto != null) {
            updateLastCompany(account.getId(), employee.getCompanyId());
        }
        result.setUserDto(cusUserDto);
        return result;
    }

    @Override
    public CusUserDto bindAccountForThirdParty(Long accountId, String thirdType, String openId, String thirdUniqueId, String nickname, String avatarUrl) {
        // 账号合法
        AsCusUser account = accountUserService.getById(accountId);
        if (Objects.isNull(account)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_EXIST);
        }
        // 绑定账号
        thirdPartyAccountService.bindOneIfNotExist(account.getId(), thirdType, openId, thirdUniqueId, nickname, avatarUrl);
        AsCusEmployee employee = chooseEmployee(account);
        return buildUserInfo(account, employee);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = BusinessException.class)
    public CusUserDto bindOrCreateAccountThirdPartyByMobile(String mobile, String smsCode, String thirdType, String openId, String thirdUniqueId, String nickname, String avatarUrl) {
        // 账号
        AsCusUser account = accountUserService.getOne(Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getMobile, mobile));
        if (Objects.isNull(account)) {
            log.info("第三方认证后绑定或创建账号 : mobile:{}", mobile);
            account = createAccount(mobile, smsCode, null, nickname);
        }
        // 绑定账号
        thirdPartyAccountService.bindOneIfNotExist(account.getId(), thirdType, openId, thirdUniqueId, nickname, avatarUrl);
        AsCusEmployee employee = chooseEmployee(account);
        return buildUserInfo(account, employee);
    }

    @Override
    public List<AsAccountEmployee> getAccountEmployees(Long accountId) {
        AsCusUser account = accountUserService.getById(accountId);
        if (Objects.isNull(account)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_EXIST, String.valueOf(accountId));
        }
        return getAccountEmployees(account);
    }

    @Override
    public List<AsAccountEmployee> getAccountEmployees(AsCusUser account) {
        return accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getAccountId, account.getId())
                        .orderByDesc(AsAccountEmployee::getCreateTime)
        );
    }

    @Value("${asset.domain.pc}")
    private String pcDomain;

    private InviteLink sendInviteLink(AsCompany company, AsCusUser operator) {
        // AsCusEmployee inviteesEmp = employeeService.checkIsExist(invitees);
        // 是否已激活了账号
        // Optional<AsAccountEmployee> employeeAccount = accountEmployeeService.getEmployeeAccount(inviteesEmp.getId());
        // if (employeeAccount.isPresent()) {
        //     throw new BusinessException(SystemResultCode.EMP_HAS_BEEN_ACTIVATED);
        // }
        // if (!StringUtils.hasText(inviteesEmp.getMobile())) {
        //     throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "需激活账号的员工手机号不可为空，请先填写手机号");
        // }
        String way = "manual";
        ThirdPartyCO thirdParty = thirdPartyAbs.getThirdParty(new ThirdPartyGetQry().setId(company.getId()));
        // 企业微信不走扫码激活逻辑
        if (Objects.nonNull(thirdParty)) {
            way = thirdParty.getType();
        }
        if (Objects.nonNull(thirdParty) && "WECHAT".equals(thirdParty.getType())) {
            way = "manual";
        }
        return new InviteLink().setPcDomain(pcDomain)
                .setCompanyId(company.getId())
                .setCompanyName(URLUtil.encode(company.getName(), StandardCharsets.UTF_8))
                .setNickname(URLUtil.encode(operator.getNickname(), StandardCharsets.UTF_8))
                .setImage(operator.getImage())
                .setWay(way);
    }

    @Override
    public InviteLink sendInviteLink(Long companyId, Long operatorId) {
        AsCompany company = companyService.checkIsExist(companyId);
        // AsCusEmployee operator = employeeService.checkIsExist(operatorId);
        // 获取账号昵称与图像
        AsCusUser account = accountUserService.getById(LoginUserThreadLocal.getAccountId());
        return sendInviteLink(company, account);
    }

    @Override
    public Boolean preActivatedAccount(Long companyId, Long empId) {
        AsCompany company = companyService.checkIsExist(companyId);
        ThirdPartyCO thirdParty = thirdPartyAbs.getThirdParty(new ThirdPartyGetQry().setId(company.getId()));
        if (Objects.nonNull(thirdParty)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "当前企业已绑定第三方组织架构，不支持预激活操作");
        }
        AsCusEmployee employee = employeeService.getById(empId);
        if (Objects.isNull(employee) || StrUtil.isBlank(employee.getMobile())) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "需激活账号的员工手机号不可为空，请先填写手机号");
        }
        AsCusEmployeeExt employeeExt = employeeExtService.getById(empId);
        if (Objects.isNull(employeeExt)) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "员工扩展信息不存在");
        }
        if (employeeExt.getAccountStatus() == 3) {
            throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "账号已激活，请勿重复激活");
        }
        // 预激活状态
        employeeExtService.update(
                Wrappers.lambdaUpdate(AsCusEmployeeExt.class)
                        .set(AsCusEmployeeExt::getAccountStatus, 2)
                        .eq(AsCusEmployeeExt::getId, empId)
        );
        return true;
    }

    private Boolean accountIsBind(Long companyId, Long accountId, Boolean isThrow) {
        // 账号存在但已被该企业下的其他员工绑定则不允许绑定
        List<AsAccountEmployee> exist = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getCompanyId, companyId)
                        .eq(AsAccountEmployee::getAccountId, accountId)
        );
        if (!CollUtil.isEmpty(exist)) {
            if (isThrow) {
                throw new BusinessException(SystemResultCode.ACTIVATE_ACCOUNT_ERROR, "此账号以绑定过当前企业其他员工，请更换手机号重新绑定");
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    @Override
    public Boolean bindEmployee(Long companyId, Long employeeId, Long accountId, Boolean isCheckExist) {
        // 同一账号最多只能关联五家企业
        Set<Long> companyNum = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getAccountId, accountId)
        ).stream().map(AsAccountEmployee::getCompanyId).collect(Collectors.toSet());
        if (companyNum.size() >= 100) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前账号关联企业过多，不支持关联新企业");
        }
        if (!isCheckExist) {
            employeeExtService.update(
                    Wrappers.lambdaUpdate(AsCusEmployeeExt.class)
                            .set(AsCusEmployeeExt::getAccountStatus, 3)
                            .eq(AsCusEmployeeExt::getId, employeeId)
            );
            AsAccountEmployee accountEmployee = new AsAccountEmployee().setAccountId(accountId).setEmployeeId(employeeId).setCompanyId(companyId);
            // 标记是否需要修改密码
            if (Edition.isSaas()) {
                SpringUtil.getBean(AsCompanyPasswordSettingService.class).markChangePwd(companyId, accountId);
            }
            return accountEmployeeService.save(accountEmployee);
        }
        AsCusUser account = accountUserService.getById(accountId);
        if (Objects.isNull(account)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_EXIST, String.valueOf(accountId));
        }
        AsCompany company = companyService.getById(companyId);
        if (Objects.isNull(company)) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        AsCusEmployee employee = employeeService.getById(employeeId);
        if (Objects.isNull(employee)) {
            throw new BusinessException(SystemResultCode.EMP_NOT_EXISTS);
        }
        employeeExtService.update(
                Wrappers.lambdaUpdate(AsCusEmployeeExt.class)
                        .set(AsCusEmployeeExt::getAccountStatus, 3)
                        .eq(AsCusEmployeeExt::getId, employeeId)
        );
        AsAccountEmployee accountEmployee = new AsAccountEmployee().setAccountId(accountId).setEmployeeId(employeeId).setCompanyId(companyId);
        accountEmployeeService.save(accountEmployee);
        // 标记是否需要修改密码
        if (Edition.isSaas()) {
            SpringUtil.getBean(AsCompanyPasswordSettingService.class).markChangePwd(companyId, accountId);
        }
        return true;
    }

    private AsCusUser checkAccount(Long accountId) {
        AsCusUser account = accountUserService.getById(accountId);
        if (Objects.isNull(account)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_EXIST, String.valueOf(accountId));
        }
        return account;
    }

    @Override
    public Boolean bindMobile(Long accountId, String mobile) {
        AsCusUser account = accountUserService.getOne(
                Wrappers.lambdaQuery(AsCusUser.class)
                        .eq(AsCusUser::getMobile, mobile)
                        .ne(AsCusUser::getId, accountId)
        );
        if (Objects.nonNull(account)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_IS_BINDING, "手机号");
        }
        return accountUserService.update(
                Wrappers.lambdaUpdate(AsCusUser.class)
                        .set(AsCusUser::getMobile, mobile)
                        .eq(AsCusUser::getId, accountId)
        );
    }

    @Override
    public Boolean bindEmail(Long accountId, String email) {
        AsCusUser account = accountUserService.getOne(
                Wrappers.lambdaQuery(AsCusUser.class)
                        .eq(AsCusUser::getEmail, email)
                        .ne(AsCusUser::getId, accountId)
        );
        if (Objects.nonNull(account)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_IS_BINDING, "邮箱");
        }
        return accountUserService.update(
                Wrappers.lambdaUpdate(AsCusUser.class)
                        .set(AsCusUser::getEmail, email)
                        .eq(AsCusUser::getId, accountId)
        );
    }

    @Override
    public Boolean bindThirdParty(ThirdPartyUser thirdPartyUser) {
        AsCusUser account = checkAccount(thirdPartyUser.getAccountId());
        AsThirdPartyAccount thirdPartyAccount = thirdPartyAccountService.getOne(
                Wrappers.lambdaQuery(AsThirdPartyAccount.class)
                        .eq(AsThirdPartyAccount::getType, thirdPartyUser.getType())
                        .eq(AsThirdPartyAccount::getUniqueId, thirdPartyUser.getUniqueId())
                        .ne(AsThirdPartyAccount::getAccountId, thirdPartyUser.getAccountId())
        );
        if (Objects.nonNull(thirdPartyAccount)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_IS_BINDING, "此第三方平台");
        }
        AsThirdPartyAccount exist = thirdPartyAccountService.getOne(
                Wrappers.lambdaQuery(AsThirdPartyAccount.class)
                        .eq(AsThirdPartyAccount::getAccountId, account.getId())
                        .eq(AsThirdPartyAccount::getType, thirdPartyUser.getType())
        );
        // 新增
        if (Objects.isNull(exist)) {
            return thirdPartyAccountService.save(
                    new AsThirdPartyAccount()
                            .setAccountId(account.getId())
                            .setType(thirdPartyUser.getType())
                            .setOpenId(thirdPartyUser.getOpenId())
                            .setUniqueId(thirdPartyUser.getUniqueId())
                            .setNickname(thirdPartyUser.getNickname())
                            .setAvatarUrl(thirdPartyUser.getAvatarUrl())
            );
        }
        // 更新
        return thirdPartyAccountService.update(
                Wrappers.lambdaUpdate(AsThirdPartyAccount.class)
                        .set(StringUtils.hasText(thirdPartyUser.getOpenId()), AsThirdPartyAccount::getOpenId, thirdPartyUser.getOpenId())
                        .set(AsThirdPartyAccount::getUniqueId, thirdPartyUser.getUniqueId())
                        .set(StringUtils.hasText(thirdPartyUser.getNickname()), AsThirdPartyAccount::getNickname, thirdPartyUser.getNickname())
                        .set(StringUtils.hasText(thirdPartyUser.getAvatarUrl()), AsThirdPartyAccount::getAvatarUrl, thirdPartyUser.getAvatarUrl())
                        .eq(AsThirdPartyAccount::getAccountId, account.getId())
                        .eq(AsThirdPartyAccount::getType, thirdPartyUser.getType())
        );
    }

    @Override
    public Boolean unbindWay(Integer way, Long accountId) {
        if (way == 1) {
            return accountUserService.update(
                    Wrappers.lambdaUpdate(AsCusUser.class)
                            .set(AsCusUser::getMobile, "")
                            .eq(AsCusUser::getId, accountId)
            );
        }
        if (way == 2) {
            return accountUserService.update(
                    Wrappers.lambdaUpdate(AsCusUser.class)
                            .set(AsCusUser::getEmail, "")
                            .eq(AsCusUser::getId, accountId)
            );
        }
        return false;
    }

    @Override
    public Boolean unbindThirdParty(String thirdType, Long accountId) {
        return thirdPartyAccountService.remove(
                Wrappers.lambdaUpdate(AsThirdPartyAccount.class)
                        .eq(AsThirdPartyAccount::getType, thirdType)
                        .eq(AsThirdPartyAccount::getAccountId, accountId)
        );
    }

    @Override
    public AccountBasicInfo getAccountBasicInfo(Long accountId, Long employeeId) {
        AsCusUser account = accountUserService.getById(accountId);
        if (Objects.isNull(account)) {
            throw new BusinessException(SystemResultCode.ACCOUNT_NOT_EXIST, String.valueOf(accountId));
        }
        AccountBasicInfo basicInfo = new AccountBasicInfo().setId(account.getId()).setImage(account.getImage()).setNickname(account.getNickname());
        basicInfo.setMobile(account.getMobile()).setEmail(account.getEmail()).setAccount(account.getAccount());
        if (StringUtils.hasText(account.getPassword())) {
            basicInfo.setIsPassword(true);
        }
        List<AsThirdPartyAccount> thirdPartyAccounts = thirdPartyAccountService.getThirdPartyAccounts(accountId);
        if (!CollUtil.isEmpty(thirdPartyAccounts)) {
            List<AccountThirdPartyInfo> collect = thirdPartyAccounts.stream().map(asThirdPartyAccount -> BeanUtil.copyProperties(asThirdPartyAccount, AccountThirdPartyInfo.class)).collect(Collectors.toList());
            basicInfo.setThirdPartyInfos(collect);
        }
        // 指定员工
        AsCusEmployee employee = employeeService.getById(employeeId);
        List<CusRoleDto> roleDtos = employeeRoleService.getRoleByEmployeeId(employeeId);
        if (Objects.nonNull(employee) && !CollUtil.isEmpty(roleDtos)) {
            List<String> roles = roleDtos.stream().map(CusRoleDto::getRoleName).collect(Collectors.toList());
            basicInfo.setRoles(roles).setEmpId(employee.getId()).setEmpName(employee.getEmpName()).setEmpNo(employee.getEmpNo());
        }
        return basicInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAccountPwdAndNickname(AccountDto dto) {
        ChangeCusUserPassword userPassword = new ChangeCusUserPassword();
        userPassword.setId(dto.getId());
        userPassword.setPassword(dto.getPassword());
        changeCusUserPasswordService.changeCusUserPassword(userPassword);
        if (StringUtils.hasText(dto.getNickname())) {
            accountUserService.update(
                    Wrappers.lambdaUpdate(AsCusUser.class)
                            .set(AsCusUser::getNickname, dto.getNickname())
                            .eq(AsCusUser::getId, dto.getId())
            );
        }
        return true;
    }

    @Override
    public void updateLastCompany(Long accountId, Long companyId) {
        accountUserService.update(
                Wrappers.lambdaUpdate(AsCusUser.class)
                        .set(AsCusUser::getCompanyId, companyId)
                        .eq(AsCusUser::getId, accountId)
        );
    }

    @Override
    public Boolean updateNickname(Long accountId, String nickname) {
        return accountUserService.update(
                Wrappers.lambdaUpdate(AsCusUser.class)
                        .set(AsCusUser::getNickname, nickname)
                        .eq(AsCusUser::getId, accountId)
        );
    }
}
