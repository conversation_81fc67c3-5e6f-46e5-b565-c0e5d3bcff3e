package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsCusUserCompanyMapper;
import com.niimbot.asset.system.model.AsUserCompany;
import com.niimbot.asset.system.service.CusUserCompanyService;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2020/10/30
 */
@Service
public class CusUserCompanyServiceImpl extends ServiceImpl<AsCusUserCompanyMapper, AsUserCompany> implements CusUserCompanyService {

    @Override
    public void add(Long companyId, Long employeeId) {
        AsUserCompany asUserCompany = new AsUserCompany().setUserId(employeeId).setCompanyId(companyId);
        this.save(asUserCompany);
    }
}
