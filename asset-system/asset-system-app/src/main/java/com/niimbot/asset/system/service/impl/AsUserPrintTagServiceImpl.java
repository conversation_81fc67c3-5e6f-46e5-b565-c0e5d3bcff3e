package com.niimbot.asset.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.system.mapper.AsUserPrintTagMapper;
import com.niimbot.asset.system.model.AsUserPrintTag;
import com.niimbot.asset.system.service.AsUserPrintTagService;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 打印模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
@Service
public class AsUserPrintTagServiceImpl extends ServiceImpl<AsUserPrintTagMapper, AsUserPrintTag> implements AsUserPrintTagService {

}
