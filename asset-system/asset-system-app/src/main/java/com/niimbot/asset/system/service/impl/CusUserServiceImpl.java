package com.niimbot.asset.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.niimbot.asset.framework.autoconfig.AssetConfig;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.event.EmpInfoChangeEvent;
import com.niimbot.asset.system.mapper.AsCompanyMapper;
import com.niimbot.asset.system.mapper.AsCusUserMapper;
import com.niimbot.asset.system.model.*;
import com.niimbot.asset.system.service.*;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.kalimdor.hulk.model.TokenResponse;
import com.niimbot.system.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/10/30
 */
@Service
@Slf4j
public class CusUserServiceImpl extends ServiceImpl<AsCusUserMapper, AsCusUser> implements CusUserService {

    @Resource
    private AsCompanyMapper companyMapper;

    @Resource
    private CusUserRoleService userRoleService;

    @Resource
    private CusRoleService cusRoleService;

    @Resource
    private AsCusEmployeeService employeeService;

    @Resource
    private AsCusEmployeeExtService cusEmployeeExtService;

    @Resource
    private UserExtService userExtService;

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private CusAccountService accountService;

    @Resource
    private AsCompanyChannelService companyChannelService;

    @Resource
    private AsAccountEmployeeService accountEmployeeService;

    @Resource
    private AsRecommendRecordService recommendRecordService;

    @Resource
    @Lazy
    private AccountCenterService accountCenterService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public String getMaxAccount() {
        return getBaseMapper().getMaxAccount();
    }

    @Override
    public CusUserDto selectUserByUnionId(String unionId) {
        return this.getBaseMapper().queryUserByUnionId(unionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(AsCusUser asCusUser) {
        QueryWrapper<AsCusUser> wrapper = new QueryWrapper<>();
        wrapper.eq("id", asCusUser.getId());
        return super.update(asCusUser, wrapper);
    }

    @Override
    public boolean updateByAccount(AsCusUser asCusUser) {
        return getBaseMapper().updateUnionIdByAccountNo(asCusUser);
    }

    @Override
    public AsCusUser getCusUserByCondition(AsCusUser condition) {
        return this.getBaseMapper().getCusUserByCondition(condition);
    }

    /**
     * 查询角色Id下是否存在用户
     *
     * @param roleId 角色Id
     * @return 结果
     */
    @Override
    public Boolean hasUserByRoleId(Long roleId) {
        return this.baseMapper.countUserByRoleId(roleId) > 0;
    }

    @Override
    public UserCenterAPPDto getUserCenterAppInfo() {
        UserCenterAPPDto info = getBaseMapper().getUserCenterAppInfo(LoginUserThreadLocal.getCusUser().getId());
        info.setIsAdmin(BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin()));
        // 角色list
        List<CusRoleDto> roleDtoList = getSortedUserRoleList();
        String roleNames = roleDtoList.stream()
                .map(CusRoleDto::getRoleName).collect(Collectors.joining(","));
        info.setRoleName(roleNames);
        info.setRoleList(roleDtoList);
        // 设置是否已经设置密码
        if (StrUtil.isBlank(info.getOldPassword())) {
            info.setIsPasswordSet(false);
        }
        info.setOldPassword("");
        return info;
    }

    private List<CusRoleDto> getSortedUserRoleList() {
        // 获取用户角色名称
        List<AsUserRole> list = userRoleService.list(new QueryWrapper<AsUserRole>().lambda().eq(AsUserRole::getUserId,
                LoginUserThreadLocal.getCurrentUserId()));
        LinkedList<CusRoleDto> sortList = Lists.newLinkedList();
        if (CollUtil.isNotEmpty(list)) {
            List<Long> roleIds = list.stream().map(AsUserRole::getRoleId).collect(Collectors.toList());
            List<AsCusRole> cusRoles = cusRoleService.listByIds(roleIds);
            // 正常状态角色
            List<AsCusRole> normalList = cusRoles.parallelStream()
                    .filter(asCusRole -> asCusRole.getStatus().intValue() == 1).collect(Collectors.toList());

            List<CusRoleDto> roleDtoList = Convert.toList(CusRoleDto.class, normalList);
            for (CusRoleDto cusRoleDto : roleDtoList) {
                if (!StringUtils.equals(cusRoleDto.getRoleCode(), BaseConstant.ADMIN_ROLE) &&
                        !StringUtils.equals(cusRoleDto.getRoleCode(), BaseConstant.ASSET_ADMIN_ROLE)
                        && !StringUtils.equals(cusRoleDto.getRoleCode(), BaseConstant.COMMON_ROLE)
                ) {
                    sortList.add(cusRoleDto);
                }
            }
            roleDtoList.parallelStream()
                    .filter(cusRoleDto -> StringUtils.equals(cusRoleDto.getRoleCode(), BaseConstant.ASSET_ADMIN_ROLE))
                    .findFirst().ifPresent(cusRoleDto -> sortList.addFirst(cusRoleDto));
            roleDtoList.parallelStream()
                    .filter(cusRoleDto -> StringUtils.equals(cusRoleDto.getRoleCode(), BaseConstant.ADMIN_ROLE))
                    .findFirst().ifPresent(cusRoleDto -> sortList.addFirst(cusRoleDto));
            roleDtoList.parallelStream()
                    .filter(cusRoleDto -> StringUtils.equals(cusRoleDto.getRoleCode(), BaseConstant.COMMON_ROLE))
                    .findFirst().ifPresent(cusRoleDto -> sortList.addLast(cusRoleDto));
        }
        return sortList;
    }

    /**
     * 用户详情信息
     *
     * @return 用户登录详情
     */
    @Override
    public CusUserDetailDto personDetail() {
        CusUserDetailDto userDetailDto = this.getBaseMapper().personDetail(LoginUserThreadLocal.getCusUser().getId());
        if (Objects.isNull(userDetailDto)) {
            throw new BusinessException(SystemResultCode.USER_NOT_EXIST);
        }
        AsCompanyChannel companyChannel = companyChannelService.getById(LoginUserThreadLocal.getCompanyId());
        if (Objects.nonNull(companyChannel)) {
            userDetailDto.setCrmClueSyncStatus(companyChannel.getCrmClueSyncStatus());
            userDetailDto.setCrmCusSyncStatus(companyChannel.getCrmCusSyncStatus());
        }

        // 三个月前的时间
        LocalDateTime pre3Date = LocalDateTimeUtil.offset(LocalDateTimeUtil.now(), -3, ChronoUnit.MONTHS);
        // 是否显示按钮【返回旧版】
        boolean isReturnOld = DictConstant.COMPANY_SOURCE_PHP_UPGRADE.equals(userDetailDto.getCompanySource())
                && userDetailDto.getCreateTime().compareTo(pre3Date) > 0;
        userDetailDto.setIsReturnOld(isReturnOld);
        CusEmployeeDto dto = new CusEmployeeDto();
        dto.setId(Convert.toLong(userDetailDto.getEmpId()))
                .setOrgList(userDetailDto.getOrgList())
                .setCompanyList(new ArrayList<>(1));
        employeeService.fillUserCompany(Collections.singletonList(dto));
        if (CollUtil.isNotEmpty(dto.getCompanyList())) {
            userDetailDto.setCompanyList(dto.getCompanyList());
        }
        return userDetailDto;
    }

    @Override
    public String getAccountMobileByUserId(Long userId) {
        AsAccountEmployee accountEmployee = accountEmployeeService.getOne(Wrappers.lambdaQuery(AsAccountEmployee.class)
                .eq(AsAccountEmployee::getEmployeeId, userId)
                .eq(AsAccountEmployee::getCompanyId, LoginUserThreadLocal.getCompanyId()), false);
        if (accountEmployee != null) {
            AsCusUser cusUser = getOne(Wrappers.lambdaQuery(AsCusUser.class)
                    .select(AsCusUser::getMobile)
                    .eq(AsCusUser::getId, accountEmployee.getAccountId()), false);
            return cusUser.getMobile();
        }
        return StrUtil.EMPTY;
    }

    /**
     * 二维码邀请员工注册-用户详情信息
     *
     * @return 用户id
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyMobile(String mobile) {
        AsCusUser exist = this.getOne(
                Wrappers.lambdaQuery(AsCusUser.class).eq(AsCusUser::getMobile, mobile)
        );
        if (exist != null) {
            throw new BusinessException(SystemResultCode.USER_CHANGE_MOBILE_ERROR);
        }
        long count = employeeService.count(
                Wrappers.lambdaQuery(AsCusEmployee.class)
                        .eq(AsCusEmployee::getMobile, mobile)
        );
        if (count >= 1) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "手机号与企业下员工手机号重复，请检查后重新修改");
        }
        Wrapper<AsCusUser> wrapper = new UpdateWrapper<AsCusUser>().lambda()
                .set(AsCusUser::getMobile, mobile)
                .eq(AsCusUser::getId, LoginUserThreadLocal.getAccountId());
        // 同步修改账号关联的所有员工手机号
        List<AsAccountEmployee> accountEmployees = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getAccountId, LoginUserThreadLocal.getAccountId())
        );
        if (CollUtil.isNotEmpty(accountEmployees)) {
            employeeService.updateAllCompanyEmpMobile(mobile, accountEmployees.stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toList()));
        }
        boolean update = update(wrapper);
        EventPublishHandler.publish(new EmpInfoChangeEvent(update).setAdmin(BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())).setCompanyId(LoginUserThreadLocal.getCompanyId()));
        return update;
    }

    @Override
    public int getSonAccountNumByCompanyId(Long companyId) {
        return this.baseMapper.getSonAccountNumByCompanyId(companyId);
    }

    @Override
    public List<SonUserDto> getSonListByCompanyId(Long companyId) {
        return this.baseMapper.getSonListByCompanyId(companyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AsCusUser registerV2(RegisterDto dto) {
        // 全局锁
        RLock lock = redissonClient.getLock(RedisConstant.companyRegisterLockKey(dto.getMobile()));
        if (lock.isLocked()) {
            log.info("手机号为[{}]的用户正在注册企业", dto.getMobile());
            throw new BusinessException(SystemResultCode.PARAM_REPEAT_SUBMIT);
        }
        lock.lock();
        try {
            // 企业名称重复不允许注册
            List<AsCompany> existCompany = companyMapper.selectList(
                    Wrappers.<AsCompany>lambdaQuery()
                            .eq(AsCompany::getName, dto.getCompanyName()));
            if (CollUtil.isNotEmpty(existCompany)) {
                throw new BusinessException(SystemResultCode.USER_REGISTER_UN_COMPLETE);
            }
            if (BaseConstant.EDITION_SAAS.equals(SpringUtil.getBean(AssetConfig.class).getEdition())) {
                // 中台用户的UID 存入账号信息中
                try {
                    TokenResponse response = userExtService.getUnionIdBySmsCode(dto.getMobile(), dto.getSmsCode());
                    dto.setUnionId(response.getUid());
                } catch (Exception e) {
                    // ignore
                }
            }
            // 初始企业数据
            return SpringUtil.getBean(RegisterDataInitService.class).init(dto);
        } finally {
            lock.unlock();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recommendRegister(RecommendRegisterDto dto) {
        // 全局锁
        RLock lock = redissonClient.getLock(RedisConstant.companyRegisterLockKey(dto.getMobile()));
        if (lock.isLocked()) {
            log.info("手机号为[{}]的用户正在注册企业", dto.getMobile());
            throw new BusinessException(SystemResultCode.PARAM_REPEAT_SUBMIT);
        }
        lock.lock();
        try {
            // 企业名称重复不允许注册
            if (companyMapper.selectCount(
                    Wrappers.<AsCompany>lambdaQuery()
                            .eq(AsCompany::getName, dto.getCompanyName())) > 0) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "该企业名已注册");
            }

            if (recommendRecordService.count(Wrappers.lambdaQuery(AsRecommendRecord.class)
                    .eq(AsRecommendRecord::getRegisterCompanyName, dto.getCompanyName())) > 0) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "该企业名已存在，请勿重复提交");
            }

            // 获取推荐人信息
            AsCusEmployee employee = recommendRecordService.verifyMobile(dto.getRecommendEmpId(), dto.getMobile());

            // 添加注册关联关系
            AsRecommendRecord recommendRecord = new AsRecommendRecord();
            recommendRecord.setRecommendCompanyId(employee.getCompanyId())
                    .setRecommendEmpId(employee.getId())
                    .setRegisterCompanyName(dto.getCompanyName())
                    .setRegisterNationalCode(dto.getNationalCode())
                    .setRegisterTime(LocalDateTime.now())
                    .setRegisterMobile(dto.getMobile());

            // SAAS渠道需要直接注册企业
            if (BooleanUtil.isTrue(dto.getSaasChannel())) {
                RegisterDto registerDto = new RegisterDto();
                registerDto.setCompanyName(dto.getCompanyName())
                        .setIndustryId(dto.getIndustryId())
                        .setMobile(dto.getMobile())
                        .setNationalCode(dto.getNationalCode())
                        .setSmsCode(dto.getSmsCode());
                if (BaseConstant.EDITION_SAAS.equals(SpringUtil.getBean(AssetConfig.class).getEdition())) {
                    // 中台用户的UID 存入账号信息中
                    try {
                        TokenResponse response = userExtService.getUnionIdBySmsCode(dto.getMobile(), dto.getSmsCode());
                        registerDto.setUnionId(response.getUid());
                    } catch (Exception e) {
                        // ignore
                    }
                }
                // 老客户转介绍 7983
                registerDto.setCh(7983L);
                // 老客户转介绍 3308
                registerDto.setAg(3308L);
                AsCusUser registerUser = SpringUtil.getBean(RegisterDataInitService.class).init(registerDto);

                // 1-已注册
                recommendRecord.setStatus(1)
                        .setRegisterCompanyId(registerUser.getCompanyId());
            }
            recommendRecordService.save(recommendRecord);
        } finally {
            lock.unlock();
        }
    }

    //注销账号
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancalUser() {

        Long accountId = LoginUserThreadLocal.getAccountId();
        //获取账号关联的所有企业
        List<AccountEmployeeDto> companyList = accountCenterService.accountCompanyList(accountId);

        if (CollectionUtil.isNotEmpty(companyList)) {

            for (AccountEmployeeDto accountEmployeeDto : companyList) {

                Long companyId = accountEmployeeDto.getCompanyId();
                //查询当前企业的管理员员工
                AsCusEmployee cusEmployee = employeeService.getAdministratorByCompanyId(companyId);
                //根据管理员员工找到账号信息
                Optional<AsCusUser> cusUseradmin = accountEmployeeService.getEmployAccount(cusEmployee.getId());
                Long accountIdAdmin = 0L;
                if (cusUseradmin.isPresent()) {
                    accountIdAdmin = cusUseradmin.get().getId();
                }
                //登录者在当前企业是否是超管
                if (accountIdAdmin.equals(accountId)) {
                    //优先查询企业员工数
                    int accountTotal = accountService.selectAccountList(companyId).size();
                    //注销企业只能是当前企业只有一个账号的情况下
                    if (accountTotal <= 1) {
                        AsCompany company = companyMapper.selectById(companyId);
                        company.setName("JCZX" + company.getName());
                        company.setStatus(5);
                        companyMapper.update(company, Wrappers.<AsCompany>lambdaUpdate().eq(AsCompany::getId, companyId));
                    }
                }
            }
            //不管企业是否欠费或者有没有其他账号存在都需要执行账号注销
            this.cancel();
        }
        //账号没有关联任何企业
        else {
            this.cancel();
        }
        return true;
    }

    private void cancel() {
        AsCusUser cusUser = this.baseMapper.selectById(LoginUserThreadLocal.getCusUser().getAccountId());
        cusUser.setMobile("ZX" + cusUser.getMobile());
        cusUser.setEmail(cusUser.getEmail() == null ? null : cusUser.getEmail().replace("@", "#"));
        cusUser.setBusinessStatus(10);
        cusUser.setIsDelete(true);
        this.baseMapper.update(cusUser, Wrappers.<AsCusUser>lambdaUpdate().eq(AsCusUser::getId, cusUser.getId()));

        List<AsAccountEmployee> employees = accountEmployeeService.list(
                Wrappers.lambdaQuery(AsAccountEmployee.class)
                        .eq(AsAccountEmployee::getAccountId, LoginUserThreadLocal.getAccountId())
        );
        if (CollUtil.isNotEmpty(employees)) {
            List<Long> empId = employees.stream().map(AsAccountEmployee::getEmployeeId).collect(Collectors.toList());
            // 调整账号为待激活状态
            cusEmployeeExtService.update(
                    Wrappers.lambdaUpdate(AsCusEmployeeExt.class)
                            .set(AsCusEmployeeExt::getAccountStatus, 1)
                            .in(AsCusEmployeeExt::getId, empId)
            );
            //账号与员工企业解绑
            accountEmployeeService.remove(
                    Wrappers.<AsAccountEmployee>lambdaUpdate()
                            .eq(AsAccountEmployee::getAccountId, cusUser.getId())
            );
        }
        // 企业绑定的第三方组织信息解绑

    }

    @Override
    public UserCenterPCDto getUserCenterPcInfo() {
        UserCenterPCDto dto = getBaseMapper().getUserCenterPcInfo(LoginUserThreadLocal.getCusUser().getId());
        dto.setIsAdmin(BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin()));

        // 获取超管信息
        UserCenterPCDto adminUserInfo = getBaseMapper().getAdminUserInfo(LoginUserThreadLocal.getCompanyId());
        dto.setAdminUserName(adminUserInfo.getAdminUserName());
        dto.setAdminUserMobile(adminUserInfo.getAdminUserMobile());

        List<CusRoleDto> sortedUserRoleList = getSortedUserRoleList();
        dto.setRoleList(sortedUserRoleList);
        // 设置社交账号
        dto.setSocials(userCenterService.getSocials(LoginUserThreadLocal.getCusUser().getUnionId()));
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loginAfterRecord(Long userId) {
        // 记录用户上一次登陆时间
        cusEmployeeExtService.updateLastLoginTime(userId);
        // 记录用户当前登陆时间
        cusEmployeeExtService.update(new UpdateWrapper<AsCusEmployeeExt>().lambda()
                .set(AsCusEmployeeExt::getCurrentLoginTime, new Date())
                .eq(AsCusEmployeeExt::getId, userId));
        // 添加redis登录记录
    }

    /**
     * PC端用户中心-账号信息
     *
     * @return AccountInfoDto
     */
    @Override
    public AccountInfoDto currentAccountInfo() {
        // 用户信息
        UserCenterPCDto dto = getBaseMapper().getUserCenterPcInfo(LoginUserThreadLocal.getCusUser().getId());

        AccountInfoDto accountInfoDto = new AccountInfoDto();
        BeanUtil.copyProperties(dto, accountInfoDto);
        accountInfoDto.setIsAdmin(BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin()));

        // 角色信息
        List<CusRoleDto> sortedUserRoleList = getSortedUserRoleList();
        accountInfoDto.setRoleList(sortedUserRoleList);
        // 设置社交账号
        accountInfoDto.setSocials(userCenterService.getSocials(LoginUserThreadLocal.getCusUser().getUnionId()));
        return accountInfoDto;
    }

}
