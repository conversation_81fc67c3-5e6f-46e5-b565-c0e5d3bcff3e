package com.niimbot.asset.system.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.system.model.AsThirdPartyAccount;
import com.niimbot.asset.system.service.AsThirdPartyAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 第三方账号关联表
 *
 * <AUTHOR>
 * @Date 2022/03/09
 */
@RestController
@RequestMapping("server/system/thirdparty")
@Slf4j
public class ThirdPartyAccountServiceController {

   @Resource
   private AsThirdPartyAccountService thirdPartyAccountService;

   //查询是否有关联账号
    @GetMapping(value = "/list")
    public Boolean list() {

       List<AsThirdPartyAccount> list=   thirdPartyAccountService.getThirdPartyAccounts(LoginUserThreadLocal.getAccountId());

        if(CollectionUtil.isNotEmpty(list)){
            return true;
        }
        return false;
    }

}
