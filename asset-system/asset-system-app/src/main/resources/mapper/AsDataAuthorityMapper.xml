<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsDataAuthorityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsDataAuthority">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="authority_data_code" property="authorityDataCode"/>
        <result column="authority_code" property="authorityCode"/>
        <result column="authority_type" property="authorityType"/>
        <result column="authority_data" property="authorityData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="authority_group" property="authorityGroup"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="getListByUserId" resultMap="BaseResultMap">
        SELECT
            id,
            user_id,
            authority_data_code,
            authority_code,
            authority_type,
            authority_data,
            authority_group,
            create_by,
            create_time,
            update_by,
            update_time
        FROM
            as_data_authority
        WHERE
            user_id = #{userId}
	</select>

    <select id="listCompanyPerms" resultMap="BaseResultMap" fetchSize="5000">
        SELECT
            user_id,
            authority_data_code,
            authority_code,
            authority_type,
            authority_data
        FROM
            as_data_authority
        <choose>
            <when test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
                ${ew.customSqlSegment} and
            </when>
            <otherwise>
                where
            </otherwise>
        </choose>
          user_id in (SELECT
              t1.employee_id
          FROM
              as_account_employee t1 JOIN as_cus_employee_ext t2 ON t1.employee_id = t2.id
          WHERE
              t1.company_id = #{companyId}
            and t2.account_status = 3)
    </select>
</mapper>
