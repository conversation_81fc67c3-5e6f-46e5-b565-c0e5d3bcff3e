<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsToolboxMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsToolbox">
        <id column="id" property="id"/>
        <result column="toolbox_name" property="toolboxName"/>
        <result column="show_name" property="showName"/>
        <result column="toolbox_code" property="toolboxCode"/>
        <result column="toolbox_icon" property="toolboxIcon"/>
        <result column="toolbox_type" property="toolboxType"/>
        <result column="position" property="position"/>
        <result column="sort_num" property="sortNum"/>
        <result column="order_type" property="orderType"/>
        <result column="business_type" property="businessType"/>
        <result column="enable_app" property="enableApp"/>
        <result column="enable_pc" property="enablePc"/>
        <result column="commonly_used" property="commonlyUsed"/>
    </resultMap>

    <resultMap id="WorkbenchResultMap" type="com.niimbot.system.ToolboxGroupItemDto">
        <id column="id" property="id"/>
        <id column="toolbox_id" property="toolboxId"/>
        <result column="toolbox_name" property="toolboxName"/>
        <result column="show_name" property="showName"/>
        <result column="toolbox_code" property="toolboxCode"/>
        <result column="toolbox_icon" property="toolboxIcon"/>
        <result column="sort_num" property="sortNum"/>
        <result column="toolbox_group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="group_sort_num" property="groupSortNum"/>
        <result column="order_type" property="orderType"/>
        <result column="commonly_used" property="position"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, toolbox_name, show_name, toolbox_code, toolbox_icon, toolbox_type, `position`, sort_num, order_type, business_type, enable_app, enable_pc
    </sql>

    <update id="sort" parameterType="list">
        update as_toolbox
         set sort_num = case
            <foreach collection="list" item="item" index="index">
                WHEN id = #{item.id} THEN #{item.sortNum}
            </foreach>
         end
         where id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <select id="selectToolbox" resultMap="BaseResultMap">
        select a.id, a.toolbox_name as show_name, a.commonly_used, b.toolbox_code, b.order_type, b.toolbox_icon, b.toolbox_name
        from as_toolbox_business as a left join as_toolbox as b on a.toolbox_id = b.id
        where a.is_delete = 0 and a.type = #{businessType}
        order by a.commonly_used desc, a.sort_num asc
    </select>

    <select id="selectWorkbench" resultMap="WorkbenchResultMap">
        select a.id, a.toolbox_id, a.toolbox_name as show_name, c.toolbox_code, c.toolbox_name, c.order_type,
               c.toolbox_icon, a.sort_num, a.toolbox_group_id,
               b.group_name, b.sort_num as group_sort_num, a.commonly_used
        from as_toolbox_business as a left join as_toolbox_group as b on a.toolbox_group_id = b.id left join as_toolbox as c on a.toolbox_id = c.id
        where a.is_delete = 0 and a.type = #{businessType}
    </select>
</mapper>
