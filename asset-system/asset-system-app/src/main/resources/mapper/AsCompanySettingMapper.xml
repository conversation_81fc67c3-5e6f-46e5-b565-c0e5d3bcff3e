<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCompanySettingMapper">

    <select id="selectCodeForGenSerialNo" resultType="string">
        <if test='"orgOwner".equalsIgnoreCase(type)'>
            SELECT org_code FROM as_org WHERE id = #{id} and company_id = #{companyId}
        </if>
        <if test='"area".equalsIgnoreCase(type)'>
            SELECT area_code FROM as_area WHERE id = #{id} and company_id = #{companyId}
        </if>
        <if test='"assetCate".equalsIgnoreCase(type)'>
            SELECT category_code FROM as_category WHERE id = #{id} and company_id = #{companyId}
        </if>
        <if test='"materialCate".equalsIgnoreCase(type)'>
            SELECT category_code FROM as_material_category WHERE id = #{id} and company_id =
            #{companyId}
        </if>
    </select>

    <select id="getAssetMaxCode" resultType="java.lang.String">
        SELECT max(t.asset_code) FROM (
            select
                <choose>
                    <when test="fieldCode != null and fieldCode == 'assetCode'">
                        SUBSTR(asset_code, LENGTH(asset_code) + 1 - #{serialLen} , LENGTH(asset_code)) as asset_code
                    </when>
                    <otherwise>
                        SUBSTR(asset_data ->> '$.${fieldCode}', LENGTH(asset_data ->> '$.${fieldCode}') + 1 - #{serialLen} , LENGTH(asset_data ->> '$.${fieldCode}')) as asset_code
                    </otherwise>
                </choose>
            from as_asset
            where company_id = #{companyId}
            <choose>
                <when test="fieldCode != null and fieldCode == 'assetCode'">
                    and asset_code like ${prefix} and LENGTH(asset_code) = #{codeLen}
                </when>
                <otherwise>
                    and asset_data ->> '$.${fieldCode}' like ${prefix} and LENGTH(asset_data ->> '$.${fieldCode}') = #{codeLen}
                </otherwise>
            </choose>
        ) t where t.asset_code REGEXP '[^0-9]' = 0
    </select>

    <select id="getMaterialMaxCode" resultType="java.lang.String">
        SELECT max(t.material_code) FROM (
            select
                <choose>
                    <when test="fieldCode != null and fieldCode == 'materialCode'">
                        SUBSTR(material_code, LENGTH(material_code) + 1 - #{serialLen} , LENGTH(material_code)) as material_code
                    </when>
                    <otherwise>
                        SUBSTR(material_data ->> '$.${fieldCode}', LENGTH(material_data ->> '$.${fieldCode}') + 1 - #{serialLen} , LENGTH(material_data ->> '$.${fieldCode}')) as material_code
                    </otherwise>
                </choose>
            from as_material
            where company_id = #{companyId}
            <choose>
                <when test="fieldCode != null and fieldCode == 'materialCode'">
                    and material_code like ${prefix} and LENGTH(material_code) = #{codeLen}
                </when>
                <otherwise>
                    and material_data ->> '$.${fieldCode}' like ${prefix} and LENGTH(material_data ->> '$.${fieldCode}') = #{codeLen}
                </otherwise>
            </choose>
        ) t where t.material_code REGEXP '[^0-9]' = 0
    </select>

    <select id="getProductMaxCode" resultType="java.lang.String">
        SELECT max(t.code) FROM (
            select SUBSTR(extension ->> '$.${fieldCode}', LENGTH(extension ->> '$.${fieldCode}') + 1 - #{serialLen} , LENGTH(extension ->> '$.${fieldCode}')) as code
            from as_product
            where company_id = #{companyId} and extension ->> '$.${fieldCode}' like ${prefix} and LENGTH(extension ->> '$.${fieldCode}') = #{codeLen}
        ) t where t.code REGEXP '[^0-9]' = 0
    </select>

    <select id="selectTreeNodePaths" resultType="java.lang.String">
        <if test='"area".equalsIgnoreCase(type)'>
            SELECT paths FROM as_area WHERE id = #{id}
        </if>
        <if test='"assetCate".equalsIgnoreCase(type)'>
            SELECT paths FROM as_category WHERE id = #{id}
        </if>
        <if test='"materialCate".equalsIgnoreCase(type)'>
            SELECT paths FROM as_material_category WHERE id = #{id}
        </if>
    </select>

</mapper>
