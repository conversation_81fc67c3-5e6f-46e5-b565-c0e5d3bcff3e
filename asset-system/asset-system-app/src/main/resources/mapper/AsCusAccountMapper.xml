<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCusAccountMapper">

    <resultMap id="customResultMap" type="com.niimbot.system.CusAccountDto">
        <id column="account_id" property="accountId"/>
        <result column="empId" property="empId"/>
        <result column="account" property="account"/>
        <result column="mobile" property="mobile"/>
        <result column="email" property="email"/>
        <result column="data_scope" property="dataScope"/>
        <result column="empStatus" property="empStatus"/>
        <result column="emp_name" property="empName"/>
        <result column="emp_no" property="empNo"/>
        <result column="emp_mobile" property="empMobile"/>
        <result column="accountStatus" property="accountStatus"/>
        <result column="accountStatusText" property="accountStatusText"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_password_set" property="isPasswordSet"/>
        <result column="nickname" property="nickname"/>
        <collection property="roleList" ofType="com.niimbot.system.CusRoleDto" select="getRoleByEmployeeId" column="empId" javaType="java.util.ArrayList"/>
<!--        <collection property="authorityList" ofType="com.niimbot.system.DataAuthorityDto" select="getDataAuthorityList" column="{userId=empId}" javaType="java.util.ArrayList"/>-->
    </resultMap>

    <select id="getRoleByEmployeeId" resultType="com.niimbot.system.CusRoleDto">
        SELECT
            ar.id,
            ar.role_name,
            ar.role_code,
            ar.status
        FROM
            as_cus_role ar
                LEFT JOIN as_user_role ur ON ar.id = ur.role_id
                LEFT JOIN as_cus_employee au ON au.id = ur.user_id
        WHERE
            au.id = #{empId}
          AND au.is_delete = 0
          AND ar.is_delete = 0
    </select>

    <select id="selectAccountPage" resultType="com.niimbot.system.CusAccountPageDto">
        SELECT t3.id AS empId,
        t2.id AS account_id,
        t2.account,
        t2.mobile,
        t2.email,
        t2.nickname,
        t3.emp_name,
        t3.emp_no,
        t3.mobile AS emp_mobile
        FROM as_account_employee t1
        JOIN as_cus_user t2 ON t1.account_id = t2.id
        JOIN as_cus_employee t3 ON t1.employee_id = t3.id
        <if test="ew.roleId!=null">
            join as_user_role ur on t3.id = ur.user_id
        </if>
        <if test="ew.orgIds != null and ew.orgIds.size() > 0">
            join as_user_org uo on t3.id = uo.user_id
        </if>
        WHERE t2.is_delete = 0
        AND t2.`status` = 1
        AND t3.is_delete = 0
        AND t1.company_id = t3.company_id
        AND t3.company_id = #{companyId}
        <!-- 员工权限 -->
        <if test="deptSql!=null and deptSql!=''">
            and t3.id in (select user_id from as_user_org where org_id in ${deptSql})
        </if>
        <if test="ew.kw != null and ew.kw != '' and ew.kw != 'null'">
            AND
            (
            t3.emp_name LIKE CONCAT('%',#{ew.kw},'%')
            OR
            t3.emp_no LIKE CONCAT('%',#{ew.kw},'%')
            OR
            t2.mobile LIKE CONCAT('%',#{ew.kw},'%')
            OR
            t2.email LIKE CONCAT('%',#{ew.kw},'%')
            OR
            t2.account LIKE CONCAT('%',#{ew.kw},'%')
            <if test="unionIds != null and unionIds.size() > 0">
                or t3.emp_name in
                <foreach collection="unionIds" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="ew.roleId!=null">
            and ur.role_id = #{ew.roleId}
        </if>
        <if test="ew.orgIds != null and ew.orgIds.size() > 0">
            and uo.org_id in
            <foreach collection="ew.orgIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test="ew.sidx == 'create_time'">
            ORDER BY t2.create_time ${ew.order}, t3.id ${ew.order}
        </if>
        <if test="ew.sidx == 'emp_no'">
            ORDER BY t3.emp_no ${ew.order}, t3.id ${ew.order}
        </if>
    </select>

    <select id="selectAccountList" resultMap="customResultMap">
        SELECT t3.id AS empId,
        t3.emp_name,
        t3.emp_no,
        t3.mobile AS emp_mobile,
        t2.id AS account_id,
        t2.mobile,
        t2.email,
        t2.account,
        t2.nickname,
        t3.data_scope
        FROM as_account_employee t1
            JOIN as_cus_user t2 ON t1.account_id = t2.id
            JOIN as_cus_employee t3 ON t1.employee_id = t3.id
        WHERE t2.is_delete = 0
        AND t2.`status` = 1
        AND t3.is_delete = 0
        AND t1.company_id = #{companyId}
        AND t3.company_id = #{companyId}
    </select>

    <select id="selectAccountEmpIds" resultType="java.lang.Long">
        SELECT t3.id
        FROM as_account_employee t1
             JOIN as_cus_user t2 ON t1.account_id = t2.id
             JOIN as_cus_employee t3 ON t1.employee_id = t3.id
        WHERE t2.is_delete = 0
          AND t2.`status` = 1
          AND t3.is_delete = 0
          AND t1.company_id = #{companyId}
          AND t3.company_id = #{companyId}
    </select>

    <select id="getAccountAmount" resultType="integer">
        SELECT
        IFNULL( COUNT( DISTINCT(cu.id)), 0 )
        FROM
        as_cus_user cu
        JOIN as_account_employee ae ON cu.id = ae.account_id
        JOIN as_cus_employee e ON ae.employee_id = e.id AND ae.company_id = e.company_id
        JOIN as_user_role ur ON ur.user_id = e.id
        WHERE
        e.company_id = #{companyId}
        AND cu.status = 1
        AND e.is_delete = 0
        AND cu.is_delete = 0
        AND ur.role_id = #{roleId}
        <!-- 员工权限 -->
        <if test="deptSql!=null and deptSql!=''">
            and e.id in (select user_id from as_user_org where org_id in ${deptSql})
        </if>
    </select>

</mapper>
