<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCusEmployeeMapper">

    <resultMap id="CusEmployeeDto3" type="com.niimbot.system.CusEmployeeDto">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="account" property="account"/>
        <result column="emp_name" property="empName"/>
        <result column="emp_no" property="empNo"/>
        <result column="mobile" property="mobile"/>
        <result column="national_code" property="nationalCode"/>
        <result column="email" property="email"/>
        <result column="position" property="position"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="account_status" property="accountStatus"/>
        <result column="account_id" property="accountId"/>
        <result column="account_activation_status" property="accountActivationStatus"/>
        <result column="emp_type" property="empType"/>
        <result column="emp_type_text" property="empTypeText"/>
    </resultMap>

    <!-- <select id="selectEmployeeRoles" resultType="string">
        SELECT role_name
        FROM as_cus_role
        WHERE id IN (SELECT role_id FROM as_user_role WHERE user_id = #{id})
    </select>

    <select id="selectUserOrg" resultType="com.niimbot.system.CusUserOrgDto">
        SELECT
        t2.id as org_id, t2.org_name
        FROM as_user_org t1 LEFT JOIN as_org t2 ON t1.org_id = t2.id
        WHERE t1.user_id = #{id} and t2.is_delete = 0
    </select>-->

    <select id="selectUserOrgByUserIds" resultType="com.niimbot.system.CusUserOrgDto">
        SELECT
            t2.id as org_id, t2.org_name, t1.user_id as emp_id
        FROM as_user_org t1 JOIN as_org t2 ON t1.org_id = t2.id
        WHERE t1.user_id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")"
                 separator=",">
            #{id}
        </foreach>
          and t2.is_delete = 0
    </select>

    <select id="getEmpNoList" resultType="java.lang.String">
        select emp_no
        from as_cus_employee
        where company_id = #{companyId}
        order by emp_no desc
    </select>

    <select id="checkPhone" resultType="com.niimbot.asset.system.model.AsCusEmployee">
        select id, mobile
        from as_cus_employee
        where mobile = #{mobile} and is_delete = 0 and company_id = #{companyId}
    </select>

    <select id="checkEmail" resultType="com.niimbot.asset.system.model.AsCusEmployee">
        select id, email
        from as_cus_employee
        where email = #{email} and is_delete = 0 and company_id = #{companyId}
    </select>


    <select id="checkEmpNo" resultType="com.niimbot.asset.system.model.AsCusEmployee">
        select id, emp_no, mobile, company_id, emp_name
        from as_cus_employee
        where emp_no = #{empNo}
          and id != #{empId}
          and is_delete = 0
    </select>

    <select id="selectCustomPage" resultMap="CusEmployeeDto3">
        SELECT
        as_cus_employee.id,
        as_cus_employee.emp_name,
        as_cus_employee.emp_no,
        as_cus_employee.mobile,
        IFNULL(as_cus_employee.national_code, '+86') as national_code,
        as_cus_employee.email,
        as_cus_employee.position,
        as_cus_employee.company_id,
        as_cus_employee.status,
        as_cus_employee.remark,
        t4.account_id,
        t11.account_status,
        CASE
        as_cus_employee.`status`
        WHEN 1 THEN
        '在职'
        WHEN 2 THEN
        '离职' ELSE '未知'
        END AS status_text,
        case t11.account_status
        when 1 then '待激活'
        when 2 then '预激活'
        when 3 then '已激活'
        else '未知'
        end as account_activation_status,
        t10.account,
        t11.type as emp_type,
        case t11.type
        when 1 then '系统员工'
        when 2 then '自建员工'
        when 3 then '自建员工'
        else '未知'
        end as emp_type_text
        from
        as_cus_employee
        left join as_account_employee t4 on as_cus_employee.id = t4.employee_id
        left join as_cus_user t10 on t4.account_id = t10.id
        left join as_cus_employee_ext t11 on as_cus_employee.id = t11.id
        <where>
            <!-- 员工权限 -->
            <if test="deptSql!=null and deptSql!=''">
                and as_cus_employee.id in (select user_id from as_user_org where org_id in ${deptSql})
            </if>
            <if test="em.orgId!=null">
                <choose>
                    <!-- 是否仅展示当前节点下的员工，有且仅有传入了orgId才生效 -->
                    <when test="em.isOnlyShowCurrentNodeEmp!=null and !em.isOnlyShowCurrentNodeEmp">
                        <if test="!isQueryRootOrg">
                            and as_cus_employee.id in (
                                SELECT e.id FROM as_org o
                                JOIN as_org oc on o.company_id = oc.company_id and (o.id = oc.id or
                                FIND_IN_SET(o.id, oc.paths)) and oc.is_delete = 0
                                join as_user_org uo on oc.id = uo.org_id
                                join as_cus_employee e on uo.user_id = e.id and o.company_id = e.company_id
                                WHERE o.is_delete = 0 and e.is_delete = 0 AND o.id = #{em.orgId}
                            )
                        </if>
                    </when>
                    <otherwise>
                        and as_cus_employee.id in (
                        SELECT e.id FROM as_org o
                        join as_user_org uo on o.id = uo.org_id
                        join as_cus_employee e on uo.user_id = e.id and o.company_id = e.company_id
                        WHERE o.is_delete = 0 and e.is_delete = 0 AND o.id = #{em.orgId}
                        )
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="em.isDelete!=null and em.isDelete==1">
                    and as_cus_employee.is_delete = 1
                </when>
                <otherwise>
                    and as_cus_employee.is_delete = 0
                </otherwise>
            </choose>
            <if test="em.id!=null">
                and as_cus_employee.id= #{em.id}
            </if>
            <if test="em.empName!=null and em.empName!=''">
                and as_cus_employee.emp_name like concat('%',#{em.empName},'%')
            </if>
            <if test="em.empNo!=null and em.empNo!=''">
                and as_cus_employee.emp_no= #{em.empNo}
            </if>
            <if test="em.mobile!=null and em.mobile!=''">
                and as_cus_employee.mobile like concat('%',#{em.mobile},'%')
            </if>
            <if test="em.position!=null and em.position!=''">
                and as_cus_employee.position like concat('%',#{em.position},'%')
            </if>
            <if test="em.status!=null">
                and as_cus_employee.status = #{em.status}
            </if>
            <if test="em.mobileType!=null and em.mobileType!='' and em.mobileType =='1'.toString()">
                and (as_cus_employee.mobile is null or as_cus_employee.mobile = "")
            </if>
            <if test="em.mobileType!=null and em.mobileType!='' and em.mobileType=='2'.toString()">
                and as_cus_employee.mobile != ""
            </if>
            <if test="em.accountType != null and em.accountType != '' and em.accountType != 4">
                and t11.account_status = #{em.accountType}
            </if>
            <if test="em.accountType != null and em.accountType == 4">
                and t11.type = 2
            </if>
            <if test="em.kw!=null and em.kw!=''">
                and (
                (as_cus_employee.emp_name like concat('%',#{em.kw},'%'))
                or
                (as_cus_employee.emp_no like concat('%',#{em.kw},'%'))
                or
                (as_cus_employee.mobile like concat('%',#{em.kw},'%'))
                or
                (as_cus_employee.email like concat('%',#{em.kw},'%'))
                <if test="unionIds != null and unionIds.size() > 0">
                    or as_cus_employee.emp_name in
                    <foreach collection="unionIds" item="id" index="index" open="(" close=")"
                             separator=",">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <!-- 钉钉小程序端离职员工转移审批时不能选择没有账号的员工 -->
            <if test="em.isNotShowNoAccountEmp != null and em.isNotShowNoAccountEmp == true">
                and t11.account_status = 3
            </if>
        </where>
        order by as_cus_employee.${em.sidx} ${em.order}
        <if test="em.sidx == 'create_time'">
            , as_cus_employee.id ${em.order}
        </if>
    </select>

    <select id="checkRegisterMobile" resultType="java.util.Map">
        SELECT ace.id as empId,
               ace.mobile,
               acu.id as accountId,
               acu.account
        from as_cus_employee ace
                     left join as_cus_user acu on acu.id = ace.id and acu.is_delete = 0
        where ace.is_delete = 0 and ace.mobile = #{mobile} limit 1
    </select>

    <select id="checkRegisterEmail" resultType="java.util.Map">
        SELECT ace.id as empId,
               ace.email,
               acu.id as accountId,
               acu.account
        from as_cus_employee ace
                     left join as_cus_user acu on acu.id = ace.id and acu.is_delete = 0
        where ace.is_delete = 0 and ace.email = #{email} limit 1
    </select>

    <update id="manageRemoveByIds">
        update as_cus_employee
        set is_delete = 1
        where is_delete = 0
        and id in
        <foreach collection="ids" index="index" item="id"
                 separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <!--<select id="orgEmpList" resultMap="CusEmployeeDto3">
        SELECT
        emp.id,
        emp.emp_name,
        emp.emp_no,
        t2.account_id,
        t3.account_status
        FROM
        as_cus_employee emp LEFT JOIN as_account_employee t2 ON emp.id = t2.employee_id
        LEFT JOIN as_cus_employee_ext t3 ON emp.id = t3.id
        WHERE
        emp.is_delete = 0
        <if test="kw!=null and kw!=''">
            AND (emp.emp_name like concat('%',#{kw},'%')
            <if test="unionIds != null and unionIds.size() > 0">
                or emp.emp_name in
                <foreach collection="unionIds" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="orgIds!=null and orgIds.size>0">
            AND emp.id in (SELECT t7.user_id FROM as_user_org t7 JOIN as_org t8 on t7.org_id = t8.id
            AND
            t8.id in
            <foreach collection="orgIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>-->

    <!-- 通用查询映射结果 -->
    <resultMap id="CusEmployeeDto" type="com.niimbot.system.CusEmployeeDto">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="account" property="account"/>
        <result column="emp_name" property="empName"/>
        <result column="emp_no" property="empNo"/>
        <result column="mobile" property="mobile"/>
        <result column="national_code" property="nationalCode"/>
        <result column="email" property="email"/>
        <result column="position" property="position"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="account_status" property="accountStatus"/>
        <result column="account_id" property="accountId"/>
        <result column="account_activation_status" property="accountActivationStatus"/>
        <result column="emp_type" property="empType"/>
        <result column="emp_type_text" property="empTypeText"/>
        <collection property="orgList" ofType="com.niimbot.system.CusUserOrgDto">
            <result column="org_id" property="orgId" />
            <result column="org_name" property="orgName" />
        </collection>
        <!--<collection property="orgList" ofType="com.niimbot.system.CusUserOrgDto" column="{id=id}"
                    select="selectUserOrg" javaType="java.util.ArrayList"/>-->
    </resultMap>

    <select id="currentUserInfo" resultMap="CusEmployeeDto">

        SELECT
            ce.id,
            ce.emp_name,
            ce.emp_no,
            ce.mobile,
            IFNULL(ce.national_code, '+86') as national_code,
            ce.position,
            ce.company_id,
            ce.status,
            ce.is_director
        from
            as_cus_employee ce
                join as_user_org uo on uo.user_id = ce.id
                join as_org o on uo.org_id = o.id
        where ce.id = #{id} and ce.is_delete = 0
    </select>

    <resultMap id="CusEmployeeDto2" type="com.niimbot.system.CusEmployeeDto">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="account" property="account"/>
        <result column="emp_name" property="empName"/>
        <result column="emp_no" property="empNo"/>
        <result column="mobile" property="mobile"/>
        <result column="national_code" property="nationalCode"/>
        <result column="email" property="email"/>
        <result column="position" property="position"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="account_status" property="accountStatus"/>
        <result column="account_id" property="accountId"/>
        <result column="account_activation_status" property="accountActivationStatus"/>
        <collection property="orgList" ofType="com.niimbot.system.CusUserOrgDto">
            <result column="org_id" property="orgId" />
            <result column="org_name" property="orgName" />
        </collection>
        <!--<collection property="orgList" ofType="com.niimbot.system.CusUserOrgDto" column="{id=id}"
                    select="selectUserOrg" javaType="java.util.ArrayList"/>
        <collection property="roles" column="id" ofType="string" javaType="java.util.ArrayList"
                    select="selectEmployeeRoles"/>-->
    </resultMap>

    <select id="getInfo" resultMap="CusEmployeeDto2">
        SELECT
            ce.id,
            ce.emp_name,
            ce.emp_no,
            ce.mobile,
            IFNULL(ce.national_code, '+86') as national_code,
            ce.email,
            ce.position,
            ce.STATUS,
            case t2.account_status
                when 1 then '待激活'
                when 2 then '预激活'
                when 3 then '已激活'
                else '未知'
                end as account_activation_status,
            o.org_name,
            o.id as org_id
        FROM
            as_cus_employee ce
                join as_cus_employee_ext t2 on ce.id = t2.id
                join as_user_org uo on uo.user_id = ce.id
                join as_org o on uo.org_id = o.id
        WHERE ce.is_delete = 0
          and o.is_delete = 0
          and ce.company_id = o.company_id
          and ce.id = #{userId}
    </select>

    <select id="selectAllByIds" resultMap="CusEmployeeDto2">
        SELECT
        ce.id,
        ce.emp_name,
        ce.emp_no,
        ce.mobile,
        IFNULL(ce.national_code, '+86') as national_code,
        ce.email,
        ce.position,
        ce.STATUS,
        case t2.account_status
        when 1 then '待激活'
        when 2 then '预激活'
        when 3 then '已激活'
        else '未知'
        end as account_activation_status,
        o.org_name,
        o.id as org_id
        FROM
        as_cus_employee ce
        join as_cus_employee_ext t2 on ce.id = t2.id
        join as_user_org uo on uo.user_id = ce.id
        join as_org o on uo.org_id = o.id
        WHERE ce.is_delete = 0
        and o.is_delete = 0
        and ce.company_id = o.company_id
        and ce.id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getByIdWithDel" resultType="com.niimbot.asset.system.model.AsCusEmployee">
        select id, emp_name, emp_no, mobile, email, image, position, data_scope, company_id, `status`, remark
        from as_cus_employee
        where id = #{id}
    </select>

    <update id="updateAllCompanyEmpMobile">
        UPDATE as_cus_employee SET mobile = #{mobile} WHERE id IN
        <foreach collection="empIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateAllCompanyEmpEmail">
        UPDATE as_cus_employee SET email = #{email} WHERE id IN
        <foreach collection="empIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectByIsDeleted" resultType="com.niimbot.asset.system.model.AsCusEmployee">
        SELECT * FROM as_cus_employee WHERE id = #{id}
    </select>

    <select id="getByOrgList" resultType="com.niimbot.asset.system.model.AsCusEmployee">
    SELECT
        ce.id,
        ce.emp_name,
        ce.emp_no,
        ce.mobile,
        ce.email,
        ce.image,
        ce.position,
        ce.STATUS
        FROM
        as_cus_employee ce
        JOIN as_user_org uo ON ce.id = uo.user_id
        JOIN as_org o ON uo.org_id = o.id
        WHERE
        ce.is_delete = 0
        AND o.is_delete = 0
        AND ce.company_id = o.company_id
        AND o.id IN
        <foreach collection="orgIds" item="orgId" index="index" open="(" close=")" separator=",">
            #{orgId}
        </foreach>
        group by ce.id,
        ce.emp_name,
        ce.emp_no,
        ce.mobile,
        ce.email,
        ce.image,
        ce.position,
        ce.STATUS
    </select>

    <select id="selectHasAccountEmployee" resultType="com.niimbot.system.CusEmployeeDto">
        select t2.id, t2.emp_name, t2.emp_no
        from as_account_employee t1
                 left join as_cus_employee t2 on t1.employee_id = t2.id and t1.company_id = t2.company_id
        where t1.company_id = #{companyId} and t2.is_delete = 0
        <if test="em.kw != null and em.kw != ''">
            and ( t2.emp_name like concat('%', #{em.kw}, '%') or t2.emp_no like concat('%', #{em.kw}, '%') )
        </if>
        order by t2.id desc
    </select>
</mapper>
