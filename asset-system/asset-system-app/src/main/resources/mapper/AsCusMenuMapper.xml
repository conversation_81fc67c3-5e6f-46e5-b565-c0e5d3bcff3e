<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCusMenuMapper">
    <cache-ref namespace="com.niimbot.asset.system.mapper.AsCusMenuMapper"/>

    <select id="configRoleMenuPcList" resultType="com.niimbot.system.CusMenuDto">
        select
        a.id,
        a.menu_name,
        a.menu_code,
        a.pid,
        a.level,
        a.paths,
        a.order_num,
        a.fe_route,
        a.menu_type,
        a.menu_class,
        a.menu_icon,
        a.status,
        a.perms,
        a.is_config,
        a.is_show_children,
        a.can_edit,
        a.is_cache
        from as_cus_menu a
        where a.can_edit = 1 and a.is_delete = 0 and a.is_public = 1
        order by a.order_num
    </select>

    <select id="configRoleModulePcList" resultType="com.niimbot.system.CusMenuDto">
        select
        a.id,
        a.menu_name,
        a.menu_code,
        a.pid,
        a.level,
        a.paths,
        a.order_num,
        a.fe_route,
        a.menu_type,
        a.menu_class,
        a.menu_icon,
        a.status,
        a.perms,
        a.is_config,
        a.is_show_children,
        a.can_edit,
        a.is_public,
        a.is_cache
        from as_cus_menu a
        where a.can_edit = 1 and a.is_delete = 0 and a.status = 1
<!--        and exists (-->
<!--        select 1 from as_module_menu b where a.id = b.menu_id and b.type = 1 and b.module_id in-->
<!--        <foreach collection="moduleIds" item="id" index="index" open="(" close=")" separator=",">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--        )-->
        order by a.order_num
    </select>

    <select id="modulePcList" resultType="com.niimbot.system.CusMenuDto">
        select
        a.id,
        a.menu_name,
        a.menu_code,
        a.pid,
        a.level,
        a.paths,
        a.order_num,
        a.fe_route,
        a.menu_type,
        a.menu_class,
        a.menu_icon,
        a.status,
        a.perms,
        a.is_config,
        a.is_show_children,
        a.can_edit,
        a.is_cache
        from as_cus_menu a
        where a.is_delete = 0 and a.status = 1
        order by a.order_num
    </select>

    <select id="userRoleMenuPcList" resultType="com.niimbot.system.CusMenuDto">
        select DISTINCT m.id, m.menu_name, m.menu_code, m.pid, m.level
        , m.paths, m.fe_route, m.menu_type, m.menu_class, m.menu_icon
        , m.status, m.perms, m.is_config, m.is_show_children, m.can_edit
        , m.order_num, m.is_cache from
        (select m.id, m.menu_name, m.menu_code, m.pid, m.level
        , m.paths, m.fe_route, m.menu_type, m.menu_class, m.menu_icon
        , m.status, m.perms, m.is_config, m.is_show_children, m.can_edit
        , m.order_num, m.is_cache from
        as_cus_menu m
        join as_role_menu rm on (m.id = rm.menu_id and type = 'pc')
        join as_user_role ur on (ur.role_id = rm.role_id )
        where
        m.is_delete = 0
        and m.status = 1
        and ur.user_id = #{userId}
        AND rm.menu_id IN
        <choose>
            <when test="pcMenus!=null and pcMenus.size() > 0">
                <foreach collection="pcMenus" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                ("")
            </otherwise>
        </choose>
        union all
        select m.id, m.menu_name, m.menu_code, m.pid, m.level
        , m.paths, m.fe_route, m.menu_type, m.menu_class, m.menu_icon
        , m.status, m.perms, m.is_config, m.is_show_children, m.can_edit
        , m.order_num, m.is_cache from as_cus_menu m where (m.can_edit = 0 or m.is_public = 1) and
        m.is_delete =
        0 and m.status = 1) m
        ORDER BY m.order_num
    </select>

    <select id="configRoleMenuAppList" resultType="com.niimbot.system.CusMenuDto">
        select
        a.id,
        a.menu_name,
        a.menu_code,
        a.pid,
        a.level,
        a.paths,
        a.order_num,
        a.fe_route,
        a.menu_type,
        a.menu_class,
        a.menu_icon,
        a.status,
        a.perms,
        a.is_config,
        a.is_show_children,
        a.can_edit
        from as_cus_menu_app a
        where a.can_edit = 1 and a.is_delete = 0 and a.is_public = 1
        order by a.order_num
    </select>

    <select id="baseAppList" resultType="com.niimbot.system.CusMenuDto">
        select
        a.id,
        a.menu_name,
        a.menu_code,
        a.pid,
        a.level,
        a.paths,
        a.order_num,
        a.fe_route,
        a.menu_type,
        a.menu_class,
        a.menu_icon,
        a.status,
        a.perms,
        a.is_config,
        a.is_show_children,
        a.can_edit
        from as_cus_menu_app a
        where a.is_delete = 0 and a.is_public = 1
        order by a.order_num
    </select>

    <select id="configRoleModuleAppList" resultType="com.niimbot.system.CusMenuDto">
        select
        a.id,
        a.menu_name,
        a.menu_code,
        a.pid,
        a.level,
        a.paths,
        a.order_num,
        a.fe_route,
        a.menu_type,
        a.menu_class,
        a.menu_icon,
        a.status,
        a.perms,
        a.is_config,
        a.is_show_children,
        a.can_edit,
        a.is_public
        from as_cus_menu_app a
        where a.can_edit = 1 and a.is_delete = 0 and a.status = 1
<!--        and exists (-->
<!--        select 1 from as_module_menu b where a.id = b.menu_id and b.type = 2 and b.module_id in-->
<!--        <foreach collection="moduleIds" item="id" index="index" open="(" close=")" separator=",">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--        )-->
        order by a.order_num
    </select>

    <select id="moduleAppList" resultType="com.niimbot.system.CusMenuDto">
        select
        a.id,
        a.menu_name,
        a.menu_code,
        a.pid,
        a.level,
        a.paths,
        a.order_num,
        a.fe_route,
        a.menu_type,
        a.menu_class,
        a.menu_icon,
        a.status,
        a.perms,
        a.is_config,
        a.is_show_children,
        a.can_edit
        from as_cus_menu_app a
        where a.is_delete = 0 and a.status = 1
        order by a.order_num
    </select>

    <select id="userRoleMenuAppList" resultType="com.niimbot.system.CusMenuDto">
        select DISTINCT m.id, m.menu_name, m.menu_code, m.pid, m.level
        , m.paths, m.fe_route, m.menu_type, m.menu_class, m.menu_icon
        , m.status, m.perms, m.is_config, m.is_show_children, m.can_edit
        , m.order_num from
        (select m.id, m.menu_name, m.menu_code, m.pid, m.level
        , m.paths, m.fe_route, m.menu_type, m.menu_class, m.menu_icon
        , m.status, m.perms, m.is_config, m.is_show_children, m.can_edit
        , m.order_num from
        as_cus_menu_app m
        join as_role_menu rm on (m.id = rm.menu_id and type = 'app')
        join as_user_role ur on (ur.role_id = rm.role_id )
        where
        m.is_delete = 0
        and m.status = 1
        and ur.user_id = #{userId}
        AND rm.menu_id IN
        <choose>
            <when test="appMenus!=null and appMenus.size() > 0">
                <foreach collection="appMenus" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                ("")
            </otherwise>
        </choose>
        union all
        select m.id, m.menu_name, m.menu_code, m.pid, m.level
        , m.paths, m.fe_route, m.menu_type, m.menu_class, m.menu_icon
        , m.status, m.perms, m.is_config, m.is_show_children, m.can_edit
        , m.order_num from as_cus_menu_app m where (m.can_edit = 0 or m.is_public = 1) and
        m.is_delete =
        0 and m.status = 1) m
        ORDER BY m.order_num
    </select>

    <select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
		SELECT DISTINCT
            m.perms
        FROM
            as_cus_menu m
            LEFT JOIN as_role_menu rm ON m.id = rm.menu_id
            LEFT JOIN as_user_role ur ON rm.role_id = ur.role_id
            LEFT JOIN as_cus_role r ON r.id = ur.role_id
        WHERE
            m.status = 1
            AND r.status = 1
            AND ur.user_id = #{userId}
	</select>
</mapper>
