<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsSelectorRecordMapper">

    <select id="selectIdsFromTable" resultType="com.niimbot.system.SelectorRecord">
        SELECT ${columns} FROM ${table} WHERE is_delete = 0 AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY FIELD (id
        <foreach collection="ids" item="id" open="," separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
