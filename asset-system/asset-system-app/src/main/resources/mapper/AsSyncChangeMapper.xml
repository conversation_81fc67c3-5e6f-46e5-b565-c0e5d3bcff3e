<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsSyncChangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="syncChange" type="com.niimbot.thirdparty.SyncChangeDto">
        <id column="id" property="id"/>
        <result column="res_id" property="resId"/>
        <result column="res_name" property="resName"/>
        <result column="type" property="type"/>
        <result column="from_org" property="fromOrg"
                typeHandler="com.niimbot.asset.system.handle.LongListTypeHandler"/>
        <result column="to_org" property="toOrg"
                typeHandler="com.niimbot.asset.system.handle.LongListTypeHandler"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="list" resultMap="syncChange">
        SELECT
        id,
        res_id,
        case
        when type in (1,2) then (select emp_name from as_cus_employee e where e.id = c.res_id)
        when type = 3 then (select org_name from as_org o where o.id = c.res_id)
        else '' end as res_name,
        type,
        from_org,
        to_org,
        STATUS,
        create_time,
        update_by,
        update_time
        FROM
        as_sync_change c
        <where>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by create_time desc;
    </select>
</mapper>
