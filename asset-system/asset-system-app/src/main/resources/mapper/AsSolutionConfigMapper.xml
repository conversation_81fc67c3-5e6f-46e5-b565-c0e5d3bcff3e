<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsSolutionConfigMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsSolutionConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="kw" jdbcType="VARCHAR" property="kw" />
    <result column="images" jdbcType="VARCHAR" property="images" />
    <result column="content_type" jdbcType="INTEGER" property="contentType" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="views" jdbcType="BIGINT" property="views" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_code, title, kw, images, content_type, content, views, is_delete, create_by, 
    create_time, update_by, update_time
  </sql>

  <sql id="Exclude_Content_Column">
    id, biz_code, title, kw, images, content_type, views, is_delete, create_by,
    create_time, update_by, update_time
  </sql>

  <select id="pageQuery" parameterType="com.niimbot.system.SolutionQueryDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from as_solution_config
    <where>
      is_delete = 0
      <if test="page.title != null and page.title != ''">
        and title LIKE concat('%',#{page.title},'%')
      </if>
    </where>
    order by create_time desc
  </select>

  <select id="selectPrevious" parameterType="java.lang.Long" resultType="com.niimbot.system.SolutionConfigDto">
    select id, title, create_time as createTime
    from as_solution_config
    where id <![CDATA[ > ]]> #{configId} and is_delete = 0
    order by create_time asc
    limit 1
  </select>

  <select id="selectNext" parameterType="java.lang.Long" resultType="com.niimbot.system.SolutionConfigDto">
    select id, title, create_time as createTime
    from as_solution_config
    where id <![CDATA[ < ]]> #{configId} and is_delete = 0
    order by create_time desc
    limit 1
  </select>

  <update id="increaseView">
    update as_solution_config set views = views + 1 where id = #{id}
  </update>
</mapper>