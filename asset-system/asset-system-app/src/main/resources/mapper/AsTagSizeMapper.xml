<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsTagSizeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsTagSize">
        <id column="id" property="id" />
        <result column="size_long" property="sizeLong" />
        <result column="size_wide" property="sizeWide" />
        <result column="type" property="type" />
        <result column="max_attr_num" property="maxAttrNum" />
        <result column="qrcode_position" property="qrcodePosition" />
        <result column="qrcode_row" property="qrcodeRow" />
        <result column="apply_app" property="applyApp" />
        <result column="sort_num" property="sortNum" />
        <result column="default_material_id" property="defaultMaterialId" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, size_long, size_wide, type, max_attr_num, qrcode_position, qrcode_row, apply_app, sort_num, default_material_id, create_time, create_by, update_time, update_by, is_delete
    </sql>
    <select id="selectSizeByPrinterIds" resultType="com.niimbot.asset.system.model.AsTagSize">
        SELECT
            *
        FROM
            as_tag_size
        WHERE
            id IN ( SELECT DISTINCT size_id FROM as_admin_printer_size
            <if test="printerIds != null and printerIds.size > 0">
                where printer_id in
                <foreach item="item" index="index" collection="printerIds" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            )
        order by size_wide asc, size_long asc, sort_num asc, id asc
    </select>

</mapper>
