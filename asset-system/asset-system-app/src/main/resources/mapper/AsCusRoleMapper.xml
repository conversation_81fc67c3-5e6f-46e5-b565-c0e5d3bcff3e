<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCusRoleMapper">

    <cache-ref namespace="com.niimbot.asset.system.mapper.AsCusRoleMapper"/>

    <select id="getRoleByEmployeeId" resultType="com.niimbot.system.CusRoleDto">
        SELECT
            ar.id,
            ar.role_name,
            ar.role_code,
            ar.status
        FROM
            as_cus_role ar
            LEFT JOIN as_user_role ur ON ar.id = ur.role_id
            LEFT JOIN as_cus_employee au ON au.id = ur.user_id
        WHERE
            au.id = #{employeeId}
            AND au.is_delete = 0
            AND ar.is_delete = 0
    </select>

    <select id="listUserByRoleId" resultType="com.niimbot.asset.system.model.AsCusUser">
        SELECT id,mobile,company_id
        from as_cus_employee where id in (
                select distinct user_id  from as_user_role where role_id = #{roleId}
        ) and company_id = #{companyId} and is_delete = 0
    </select>

    <resultMap id="roleAccountMap" type="com.niimbot.system.CusRoleAccountDto">
        <id column="id" property="id"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <collection property="empList" ofType="com.niimbot.system.CusEmployeeDto">
            <result column="emp_id" property="id"/>
            <result column="account" property="account"/>
            <result column="emp_name" property="empName"/>
            <result column="emp_no" property="empNo"/>
        </collection>
    </resultMap>

    <select id="roleAccountList" resultMap="roleAccountMap">
        SELECT
        t1.id,
        t1.role_name,
        t1.role_code,
        t3.employee_id AS emp_id,
        t5.account,
        t4.emp_name,
        t4.emp_no
        FROM
        as_cus_role t1
        JOIN as_user_role t2 ON t1.id = t2.role_id
        JOIN as_account_employee t3 ON t2.user_id = t3.employee_id
        JOIN as_cus_employee t4 ON t3.employee_id = t4.id AND t4.is_delete = 0 AND t4.`status` = 1
        JOIN as_cus_user t5 ON t3.account_id = t5.id AND t5.is_delete = 0 AND t5.account IS NOT NULL
        JOIN as_cus_employee_ext t6 ON t3.employee_id = t6.id AND t6.account_status = 3
        <if test="ew.empName!=null and ew.empName!=''">
            AND (t4.emp_name like concat('%',#{ew.empName},'%')
            <if test="unionIds != null and unionIds.size() > 0">
                or t4.emp_name in
                <foreach collection="unionIds" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="ew.roleId!=null">
            AND t1.id = #{ew.roleId}
        </if>
    </select>

    <select id="selectByCondition" resultType="com.niimbot.asset.system.model.AsCusRole">
        select *
        from as_cus_role
        <where>
            <if test="param.companyId != null">
                and company_id = #{param.companyId}
            </if>
            <if test="param.isDefault != null">
                and is_default = #{param.isDefault}
            </if>
            <if test="param.roleCode != null and param.roleCode != ''">
                and role_code = #{param.roleCode}
            </if>
            and `status` = 1 and is_delete = 0
        </where>
    </select>

</mapper>
