<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsRecycleBinMapper">

    <update id="updateTableIsDelete">
        UPDATE ${tableName}
        SET is_delete = #{isDelete}
        WHERE id = #{resId}
    </update>

    <update id="updateBatchTableIsDelete">
        UPDATE ${tableName} SET is_delete = #{isDelete} WHERE id IN
        <foreach collection="resIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectResIsDelete" resultType="java.lang.Boolean">
        SELECT is_delete
        FROM ${tableName}
        WHERE id = #{resId}
    </select>

    <select id="selectResIds" resultType="java.lang.Long">
        SELECT res_id
        FROM as_recycle_bin
        WHERE company_id = #{em.companyId}
          AND res_type = #{em.resType}
    </select>

    <select id="selectResIdsFrom" resultType="java.lang.Long">
        SELECT id FROM ${tableName} WHERE is_delete = 1 AND id IN
        <foreach collection="resIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <resultMap id="ResDataResultMap" type="com.niimbot.system.RecycleBin$ResData">
        <id column="id" property="id"/>
        <result column="standard_id" property="standardId"/>
        <result column="product_id" property="productId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="body" property="body" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="selectResData" resultMap="ResDataResultMap">
        SELECT id, standard_id, product_id, create_by, create_time, ${columnName} AS `body` FROM ${tableName} WHERE id = #{resId} AND is_delete = 1
    </select>

</mapper>
