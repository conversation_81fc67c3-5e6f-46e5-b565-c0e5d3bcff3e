<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCountryCodeMapper">

    <update id="updateStatusByIds">
        update as_country_code set choose_status = #{chooseStatus}
        where id in
        <foreach collection="countryIds" index="index" item="id"
                 separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>


</mapper>
