<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsToolboxStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsToolboxStatistics">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="order_type" jdbcType="BIGINT" property="orderType" />
    <result column="count" jdbcType="BIGINT" property="count" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, user_id, order_type, `count`, create_time, update_time
  </sql>

  <update id="increaseToolbox">
    update as_toolbox_statistics set count = count + 1, update_time = #{param.updateTime} where id = #{param.id}
  </update>
</mapper>