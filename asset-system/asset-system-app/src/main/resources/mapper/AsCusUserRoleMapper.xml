<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCusUserRoleMapper">

    <cache-ref namespace="com.niimbot.asset.system.mapper.AsCusUserRoleMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="AsUserRoleMap" type="com.niimbot.asset.system.model.AsUserRole">
        <result column="role_id" property="roleId"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <delete id="removeBatch">
        DELETE from as_user_role where (user_id, role_id)
        in
        <foreach collection="list" item="ew" index="index" open="(" close=")" separator=",">
            (#{ew.userId}, #{ew.roleId})
        </foreach>
    </delete>
</mapper>
