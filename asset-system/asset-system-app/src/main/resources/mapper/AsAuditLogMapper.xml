<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsAuditLogMapper">

    <resultMap id="DtoMap" type="com.niimbot.system.AuditLogDto">
        <id column="id" property="id"/>
        <result column="operator" property="operator"/>
        <result column="org_name" property="orgName"/>
        <result column="role_name" property="roleName"/>
        <result column="action_text" property="actionText"/>
        <result column="content" property="content"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="terminal_code" property="terminalCode"/>
        <result column="terminal_text" property="terminalText"/>
    </resultMap>

    <select id="search" resultType="com.niimbot.system.AuditLogDto">
        SELECT id, operator, org_name, role_name, action_text, content, create_by, create_time, terminal_code, terminal_text FROM as_audit_log
        <where>
            <if test="em.createBys != null and em.createBys.size() != 0">
                AND create_by IN
                <foreach collection="em.createBys" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="em.orgIds != null and em.orgIds.size() != 0">
                AND org_id IN
                <foreach collection="em.orgIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="em.roleIds != null and em.roleIds.size() != 0">
                AND role_id IN
                <foreach collection="em.roleIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="em.kw != null and em.kw != ''">
                AND ( action_text LIKE CONCAT('%', #{em.kw}, '%') OR content LIKE CONCAT('%', #{em.kw}, '%') )
            </if>
            <if test="em.createTimes != null">
                AND ( create_time &gt; #{em.createTimes[0]} AND create_time &lt; #{em.createTimes[1]} )
            </if>
        </where>
        ORDER BY ${em.sidx} ${em.order}
    </select>
</mapper>
