<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsIndustryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsIndustry">
        <id column="id" property="id" />
        <result column="industry_name" property="industryName" />
        <result column="pid" property="pid" />
        <result column="level" property="level" />
        <result column="paths" property="paths" />
        <result column="sort_num" property="sortNum" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="selectUserNumByIndustry" resultType="com.niimbot.system.statistics.StatisticsIndustryDto">
        SELECT
            ind.id,
            ind.industry_name,
            COUNT( DISTINCT c.`id` ) AS company_num
        FROM
            `as_industry` AS ind
            LEFT JOIN `as_company` AS c ON c.industry_id = ind.id
        WHERE
            c.is_delete = 0
        GROUP BY
            ind.id
    </select>
    <select id="selectLostUserNumByIndustry" resultType="com.niimbot.system.statistics.StatisticsIndustryDto">
        SELECT
            ind.id,
            ind.industry_name,
            COUNT( DISTINCT c.`id` ) AS company_num
        FROM
            `as_industry` AS ind
            LEFT JOIN `as_company` AS c ON c.industry_id = ind.id
        WHERE
            c.is_delete = 0
            AND c.id NOT IN ( SELECT DISTINCT company_id FROM as_user_active_reports WHERE day_time >= #{preDate} )
        GROUP BY
            ind.id
    </select>
    <select id="selectEffectiveUserNumByIndustry"
            resultType="com.niimbot.system.statistics.StatisticsIndustryDto">
        SELECT
            ind.id,
            ind.industry_name,
            COUNT( DISTINCT c.`id` ) AS company_num
        FROM
            `as_industry` AS ind
            LEFT JOIN `as_company` AS c ON c.industry_id = ind.id
        WHERE
            c.is_delete = 0
            AND c.id IN ( SELECT company_id FROM as_asset WHERE is_delete = 0 GROUP BY company_id HAVING count( id ) >= 500 )
            AND c.id IN ( SELECT DISTINCT company_id FROM as_user_active_reports WHERE day_time &gt;= #{beginDate}
            AND day_time &lt;= #{endDate})
        GROUP BY
            ind.id
    </select>
    <select id="selectWeekActiveUserNumByIndustry"
            resultType="com.niimbot.system.statistics.StatisticsIndustryDto">
        SELECT
            ind.id,
            ind.industry_name,
            COUNT( DISTINCT c.`id` ) AS company_num
        FROM
            `as_industry` AS ind
            LEFT JOIN `as_company` AS c ON c.industry_id = ind.id
        WHERE
            c.is_delete = 0
            AND c.id IN ( SELECT DISTINCT company_id FROM as_user_active_reports WHERE day_time &gt;= #{beginDate}
            AND day_time &lt;= #{endDate})
        GROUP BY
            ind.id
    </select>

    <select id="industryRefCompany" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            as_industry a
            LEFT JOIN as_company b ON a.id = b.industry_id
        WHERE
            b.is_delete = 0
            AND a.is_delete = 0
            AND (a.id = #{industryId} or a.paths like concat('%,',#{industryId},',%'))
    </select>

</mapper>
