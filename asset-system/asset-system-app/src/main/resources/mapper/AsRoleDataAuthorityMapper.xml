<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsRoleDataAuthorityMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsRoleDataAuthority">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="authority_data_code" jdbcType="VARCHAR" property="authorityDataCode" />
    <result column="authority_code" jdbcType="VARCHAR" property="authorityCode" />
    <result column="authority_type" jdbcType="SMALLINT" property="authorityType" />
    <result column="authority_data" property="authorityData"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="authority_group" jdbcType="INTEGER" property="authorityGroup" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, role_id, authority_data_code, authority_code, authority_type, authority_data, 
    authority_group, create_by, create_time, update_by, update_time
  </sql>

  <select id="selectByRoleCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from as_role_data_authority
    where company_id = #{companyId} and role_id in
        (select id
         from as_cus_role
         where company_id = #{companyId}
           and role_code = #{roleCode}
           and status = 1
           and is_delete = 0)
  </select>

  <select id="selectDefaultDataAuth" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from as_role_data_authority
    where company_id = #{companyId} and role_id in
    (select id
    from as_cus_role
    where company_id = #{companyId}
    and is_default = 1
    and status = 1
    and is_delete = 0)
  </select>
</mapper>