<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCompanyMapper">

    <cache-ref namespace="com.niimbot.asset.system.mapper.AsCompanyMapper"/>

    <select id="selectCountOfExcludeTest" resultType="int">
        SELECT
            COUNT( t1.id ) AS num
        FROM
            as_company t1
            LEFT JOIN as_company_setting t2 ON t1.id = t2.company_id
        WHERE
          t1.is_delete = 0
          AND t2.is_delete = 0
          AND t2.is_test = 0
          AND t1.create_time &lt; #{date}
    </select>

    <select id="selectEnterpriseSize" resultType="java.lang.Integer">
        select count(distinct id)
        from as_cus_employee
        where is_delete = 0 and company_id = #{companyId}
    </select>

    <select id="selectCompanyTag" resultType="java.lang.String">
        select tag_name
        from as_company_tag
        where is_delete = 0 and company_id = #{companyId}
    </select>
</mapper>
