<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsEmployeeChangeMapper">

    <resultMap id="cusEmployeeChangeDto" type="com.niimbot.system.CusEmployeeChangeDto">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="emp_id" property="empId"/>
        <result column="emp_name" property="empName"/>
        <result column="emp_no" property="empNo"/>
        <result column="org_explain" property="orgExplain"/>
        <result column="asset_explain" property="assetExplain"/>
    </resultMap>

    <select id="getPage" resultMap="cusEmployeeChangeDto">
        select id, type, create_by, create_time, emp_id, emp_name, emp_no, org_explain,
        asset_explain
        from (
        SELECT
        c.id,
        c.type,
        c.create_by,
        c.create_time,
        c.emp_id,
        case
        when type in (1,2) then (select emp_name from as_cus_employee e where e.id = c.emp_id)
        when type = 3 then (select org_name from as_org o where o.id = c.emp_id)
        else '' end as emp_name,
        case
        when type in (1,2) then (select emp_no from as_cus_employee e where e.id = c.emp_id)
        else '' end as emp_no,
        case
        when type in (1,2) then (select mobile from as_cus_employee e where e.id = c.emp_id)
        else '' end as emp_mobile,
        c.org_explain,
        c.asset_explain
        FROM
        as_employee_change c
        <where>
            c.company_id = #{companyId}
            <if test="ew.changeOrgType!=null">
                and c.type = #{ew.changeOrgType}
            </if>
            <if test="ew.changeOrgType==null">
                and c.type in (1, 2)
            </if>
        </where>
        ) t
        <where>
            <if test="ew.kw!=null and ew.kw!=''">
                and ( ( (t.emp_name like concat('%',#{ew.kw},'%')) or (t.emp_no like concat('%',#{ew.kw},'%')) or (t.emp_mobile like #{ew.kw}) )
                <if test="unionIds != null and unionIds.size() > 0">
                    or t.emp_name in
                    <foreach collection="unionIds" item="id" index="index" open="(" close=")"
                             separator=",">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
        </where>
        order by t.create_time desc
    </select>


</mapper>
