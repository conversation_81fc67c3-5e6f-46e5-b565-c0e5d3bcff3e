<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCompanyNewbieTaskMapper">

    <resultMap id="details" type="com.niimbot.system.CompanyNewbieTaskDto">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="task_id" property="taskId"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="buttons" property="buttons"
                typeHandler="com.niimbot.asset.system.handle.NewbieTaskButtonListHandler"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
    </resultMap>

    <select id="listTask" resultMap="details">
        SELECT t1.id,
               t1.company_Id,
               t1.task_id,
               t1.`status`,
               t2.`name`,
               t2.`description`,
               t2.buttons,
               t2.`sort`
        FROM as_company_newbie_task t1
                 LEFT JOIN as_newbie_task_config t2 ON t1.task_id = t2.id
        WHERE t1.company_Id = #{companyId}
          AND t2.is_delete = 0
        ORDER BY t2.`sort` DESC
    </select>

</mapper>
