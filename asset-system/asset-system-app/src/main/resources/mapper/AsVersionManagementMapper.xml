<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsVersionManagementMapper">
    <select id="getMaxVersionExt" resultType="com.niimbot.system.VersionManagementDto">
        SELECT
            t.*,
            CONCAT( t.version_number_first, '.', t.version_number_second, '.', t.version_number_third ) AS versionNumber
        FROM
            as_version_management t
        WHERE
            t.type = #{type}
          and t.is_delete = 0
            <if test="first != null and second != null and third != null">
              and cast(CONCAT(
                    LPAD(t.version_number_first, 3, '0'),
                    LPAD(t.version_number_second, 3, '0'),
                    LPAD(t.version_number_third, 3, '0')) as SIGNED) &gt;
                  cast(CONCAT(LPAD(#{first}, 3, '0'),
                          LPAD(#{second}, 3, '0'),
                          LPAD(#{third}, 3, '0')) as SIGNED)
            </if>
        order by cast(CONCAT(
                LPAD(t.version_number_first, 3, '0'),
                LPAD(t.version_number_second, 3, '0'),
                LPAD(t.version_number_third, 3, '0')) as SIGNED) desc
    </select>

</mapper>
