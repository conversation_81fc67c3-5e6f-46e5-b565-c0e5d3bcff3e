<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsUserTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="UserTagMap" type="com.niimbot.system.UserTagDto">
        <id column="id" property="id" />
        <result column="tag_name" property="tagName" />
        <result column="size_pid" property="sizePid" />
        <result column="size_id" property="sizeId" />
        <result column="tag_url" property="tagUrl" />
        <result column="tag_type" property="tagType" />
        <result column="attr_data" property="attrData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="size_long" property="sizeLong" />
        <result column="size_wide" property="sizeWide" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tag_type, company_id, tag_name, size_id, size_pid, company_name, tag_url, attr_data, create_by, create_time, update_by, update_time, remark
    </sql>

    <select id="queryTagBySizeId" resultMap="UserTagMap">
        SELECT
        ut.id,
        ut.tag_name,
        ut.size_pid,
        ut.size_id,
        ut.tag_url,
        ut.tag_type,
        ut.attr_data,
        s.size_long,
        s.size_wide,
        s.type,
        s.max_attr_num,
        s.qrcode_position,
        s.qrcode_row
        FROM
        as_printer_tags pt
        LEFT JOIN as_user_tag ut ON pt.tag_id = ut.id
        LEFT JOIN as_tag_size s ON s.id = ut.size_id
        WHERE pt.printer_id = #{printerId} AND ut.company_id in (0, #{companyId}) and ut.is_delete = 0 and ut.print_type = #{printType}
        <if test="sizeId!=null and sizeId!=''">
            and ut.size_pid = #{sizeId}
        </if>
        <if test="tagType!=null and tagType!=''">
            and ut.tag_type = #{tagType}
        </if>
        <if test="kw!=null and kw!=''">
            and ut.tag_name like concat('%',#{kw},'%')
        </if>
        order by ut.create_time desc, ut.id asc
    </select>

    <resultMap id="UserTagPrintMap" type="com.niimbot.system.UserTagPrintDto">
        <id column="id" property="id"/>
        <result column="tag_name" property="tagName"/>
        <result column="standard_id" property="standardId"/>
        <result column="size_id" property="sizeId"/>
        <result column="size_pid" property="sizePid"/>
        <result column="tag_url" property="tagUrl"/>
        <result column="tag_type" property="tagType"/>
        <result column="attr_data" property="attrData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="code_val" property="codeVal"/>
        <result column="is_custom_words" property="isCustomWords"/>
        <result column="size_long" property="sizeLong"/>
        <result column="size_wide" property="sizeWide"/>
        <result column="type" property="type" />
        <result column="max_attr_num" property="maxAttrNum" />
        <result column="qrcode_position" property="qrcodePosition" />
        <result column="qrcode_row" property="qrcodeRow" />
        <result column="default_material_id" property="defaultMaterialId" />
        <result column="background_image" property="tagBackgroundImage"/>
    </resultMap>
    <select id="getDetail" resultMap="UserTagPrintMap">
        SELECT
            ut.id,
            ut.tag_name,
            ut.size_id,
            ut.size_pid,
            ut.tag_url,
            ut.tag_type,
            ut.attr_data,
            ut.code_val,
            ut.is_custom_words,
            s.size_long,
            s.size_wide,
            s.type,
            s.max_attr_num,
            s.qrcode_position,
            s.qrcode_row,
            s.default_material_id,
            ut.standard_id,
            ut.background_image
        FROM
            as_user_tag ut
                LEFT JOIN as_tag_size s ON s.id = ut.size_id
        WHERE ut.id = #{tagId} and ut.is_delete = 0
    </select>
    <!--    <select id="queryTagBySizeAndType" resultType="com.niimbot.system.UserTagDto">-->
    <!--        SELECT-->
    <!--            ut.id,-->
    <!--            ut.tag_type,-->
    <!--            ut.tag_name,-->
    <!--            ut.size_id,-->
    <!--            ut.size_pid,-->
    <!--            ut.tag_url,-->
    <!--            s.size_long,-->
    <!--            s.size_wide,-->
    <!--            s.type-->
    <!--        FROM-->
    <!--            as_user_tag ut-->
    <!--            LEFT JOIN as_tag_size s ON s.id = ut.size_id-->
    <!--        WHERE ut.company_id in (0, #{companyId})-->
    <!--        <choose>-->
    <!--            <when test="size == 1">-->
    <!--                and s.size_wide = "30" and s.size_long = "50"-->
    <!--            </when>-->
    <!--            <when test="size == 2">-->
    <!--                and s.size_wide = "50" and s.size_long = "70"-->
    <!--            </when>-->
    <!--            <when test="size == 6">-->
    <!--                and s.size_wide = "70" and s.size_long = "50"-->
    <!--            </when>-->
    <!--            <when test="size == 8">-->
    <!--                and s.size_wide = "34" and s.size_long = "76"-->
    <!--            </when>-->
    <!--        </choose>-->
    <!--    </select>-->



    <resultMap id="asUserTagMap" type="com.niimbot.asset.system.model.AsUserTag">
        <id column="id" property="id" />
        <result column="tag_type" property="tagType" />
        <result column="print_type" property="printType" />
        <result column="company_id" property="companyId" />
        <result column="standard_id" property="standardId"/>
        <result column="tag_name" property="tagName" />
        <result column="size_id" property="sizeId" />
        <result column="size_pid" property="sizePid" />
        <result column="company_name" property="companyName" />
        <result column="tag_url" property="tagUrl" />
        <result column="code_val" property="codeVal" />
        <result column="is_custom_words" property="isCustomWords" />
        <result column="attr_data" property="attrData"
                typeHandler="com.niimbot.asset.system.handle.UserTagListTypeHandler"/>
        <result column="apply_rfid" property="applyRfid"/>
    </resultMap>


    <select id="getOneById" resultMap="asUserTagMap">
        select id, tag_type, print_type, company_id, tag_name, size_id, size_pid, company_name,
               tag_url, standard_id, code_val, is_custom_words, attr_data, create_by, create_time, update_by, update_time,
               remark,apply_rfid from as_user_tag where id = #{tagId} and print_type = #{printType}
    </select>
    <select id="checkTagName" resultType="com.niimbot.asset.system.model.AsUserTag">
        SELECT
        id,
        tag_type,
        company_id,
        tag_name,
        size_id,
        size_pid,
        company_name
        FROM
        as_user_tag
        WHERE
        tag_name = #{tagName}
        <if test="tagId != 0">
            AND id != #{tagId}
        </if>
        AND company_id IN ( 0, #{companyId} )
        AND is_delete = 0
    </select>

    <select id="getDistinctBySizeId" resultMap="UserTagPrintMap">
        SELECT
            ANY_VALUE ( ut.id ) AS id,
            ANY_VALUE ( ut.tag_name ) AS tag_name,
            ANY_VALUE ( ut.size_pid ) AS size_pid,
            ut.size_id,
            ANY_VALUE ( ut.tag_url ) AS tag_url,
            ANY_VALUE ( ut.tag_type ) AS tag_type,
            ANY_VALUE ( ut.attr_data ) AS attr_data,
            ANY_VALUE ( s.size_long ) AS size_long,
            ANY_VALUE ( s.size_wide ) AS size_wide,
            ANY_VALUE ( s.type ) AS type,
            ANY_VALUE ( s.max_attr_num ) AS max_attr_num,
            ANY_VALUE ( s.qrcode_position ) AS qrcode_position,
            ANY_VALUE ( s.qrcode_row ) AS qrcode_row,
            ANY_VALUE ( ut.background_image ) AS background_image
        FROM
            as_printer_tags t1
            LEFT JOIN as_user_tag ut ON t1.tag_id = ut.id
            LEFT JOIN as_tag_size s ON s.id = ut.size_id
        WHERE
            t1.printer_id = #{printerId}
          AND ut.tag_type = 1
          AND ut.print_type = #{printType}
          AND ut.size_pid = #{sizeId}
        GROUP BY
            ut.size_id
        ORDER BY
            id ASC
    </select>

    <select id="selectAllCompanyTags" resultType="com.niimbot.asset.system.model.AsUserTag">
        SELECT
            id
        FROM
            as_user_tag
        WHERE
            ( company_id = 0 OR company_id = #{companyId} )
          AND is_delete = 0
          AND print_type = #{printType}
          AND size_pid = #{sizeId}
    </select>

</mapper>
