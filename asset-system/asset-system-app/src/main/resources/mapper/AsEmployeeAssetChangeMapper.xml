<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsEmployeeAssetChangeMapper">

    <resultMap id="asEmployeeAssetChange"
               type="com.niimbot.asset.system.model.AsEmployeeAssetChange">
        <id column="id" property="id"/>
        <result column="change_org_type" property="changeOrgType"/>
        <result column="change_id" property="changeId"/>
        <result column="asset_id" property="assetId"/>
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="asset_name" property="assetName"/>
        <result column="asset_code" property="assetCode"/>
        <result column="use_org" property="useOrg"/>
        <result column="use_person" property="usePerson"/>
        <result column="org_owner" property="orgOwner"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="page" resultMap="asEmployeeAssetChange">
        select id,
            change_org_type,
            change_id,
            asset_id,
            asset_snapshot_data,
            asset_name,
            asset_code,
            use_org,
            use_person,
            org_owner,
            create_by,
            create_time
        from as_employee_asset_change
        where
            change_org_type = #{ew.changeOrgType}
            and change_id = #{ew.changeId}
    </select>
</mapper>
