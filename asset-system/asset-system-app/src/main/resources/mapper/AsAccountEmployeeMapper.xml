<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsAccountEmployeeMapper">

  <select id="filterHasAccountEmpIds" resultType="java.lang.Long">
    SELECT
      employee_id
    FROM
      as_account_employee t1
        JOIN as_cus_employee_ext t2 ON t1.employee_id = t2.id
    WHERE
      t2.account_status = 3
      AND t1.employee_id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    ORDER BY FIELD (employee_id
    <foreach collection="ids" item="id" open="," separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="hasAccountEmpId" resultType="java.lang.Long">
      SELECT
          employee_id
      FROM
          as_account_employee t1 JOIN as_cus_employee_ext t2 ON t1.employee_id = t2.id
      WHERE
          t1.company_id = #{companyId}
          and t2.account_status = 3
    </select>
</mapper>