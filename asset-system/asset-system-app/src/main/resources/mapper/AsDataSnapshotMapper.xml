<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsDataSnapshotMapper">

    <select id="selectSourceIds" resultType="java.lang.Long">
        SELECT source_id FROM as_data_snapshot
        WHERE company_id = #{em.companyId}
        AND source_type = #{em.sourceType}
        AND data_type = #{em.dataType}
        <if test="em.kw != null and em.kw != ''">
            <if test="em.dataType == 1">
                AND ( snapshot ->> '$.assetName' LIKE CONCAT('%', #{em.kw}, '%') OR snapshot ->> '$.assetCode' LIKE CONCAT('%', #{em.kw}, '%') )
            </if>
        </if>
    </select>

</mapper>
