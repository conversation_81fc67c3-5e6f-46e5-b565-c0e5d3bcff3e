<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsUserActiveReportsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.system.model.AsUserActiveReports">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="company_id" property="companyId" />
        <result column="day_time" property="dayTime" />
        <result column="week_time" property="weekTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, company_id, day_time, week_time
    </sql>
    <select id="newUserActiveNum" resultType="com.niimbot.system.statistics.StatisticsNumWeekDto">
        SELECT
            count( DISTINCT u.company_id ) AS num,
            u.week_time
        FROM
            `as_user_active_reports` AS u
            LEFT JOIN `as_user_reports` AS ur ON u.company_id = ur.company_id
            AND u.week_time = ur.week_time
        WHERE
            u.day_time LIKE concat(#{year},'%')
            AND ur.day_time LIKE concat(#{year},'%')
        GROUP BY
            u.week_time
    </select>
    <select id="oldUserActiveNum" resultType="com.niimbot.system.statistics.StatisticsNumWeekDto">
        SELECT
            count( DISTINCT u.company_id ) AS num,
            u.week_time
        FROM
            `as_user_active_reports` AS u
            LEFT JOIN `as_user_reports` AS ur ON u.company_id = ur.company_id
        WHERE
            u.day_time LIKE concat(#{year},'%')
            AND ( u.week_time != ur.week_time OR ur.day_time &lt; #{year} )
        GROUP BY
            u.week_time
    </select>
    <select id="newUserActiveNumByDay" resultType="com.niimbot.system.statistics.StatisticsNumDayDto">
        SELECT
            count( DISTINCT u.company_id ) AS num,
            DATE_FORMAT(u.day_time, '%Y-%m-%d')  AS day
        FROM
            `as_user_active_reports` AS u
            LEFT JOIN `as_user_reports` AS ur ON u.company_id = ur.company_id
            LEFT JOIN `as_company_setting` cs ON u.company_id = cs.company_id
        WHERE
            u.day_time LIKE concat(#{year},'%')
            AND ur.day_time LIKE concat(#{year},'%')
            AND u.week_time = ur.week_time
            AND cs.is_delete = 0
            AND cs.is_test = 0
        GROUP BY
            day
    </select>
    <select id="oldUserActiveNumByDay" resultType="com.niimbot.system.statistics.StatisticsNumDayDto">
        SELECT
            count( DISTINCT u.company_id ) AS num,
            DATE_FORMAT(u.day_time, '%Y-%m-%d')  AS day
        FROM
            `as_user_active_reports` AS u
            LEFT JOIN `as_user_reports` AS ur ON u.company_id = ur.company_id
            LEFT JOIN `as_company_setting` cs ON u.company_id = cs.company_id
        WHERE
            u.day_time LIKE concat(#{year},'%')
            AND ( u.week_time != ur.week_time OR ur.day_time &lt; concat(#{year},'') )
            AND cs.is_delete = 0
            AND cs.is_test = 0
        GROUP BY
            day
    </select>
    <select id="userActiveNum" resultType="com.niimbot.system.statistics.StatisticsNumWeekDto">
        SELECT
            count( DISTINCT company_id ) AS num,
            week_time
        FROM
            `as_user_active_reports`
        WHERE
            day_time LIKE concat(#{year},'%')
        GROUP BY
            week_time
    </select>

    <select id="selectCountByDay" resultType="int">
        SELECT
            COUNT( DISTINCT t1.company_id ) AS num
        FROM
            as_user_active_reports t1
                LEFT JOIN as_company_setting t2 ON t1.company_id = t2.company_id
        WHERE
            t1.day_time &gt; #{year}
          AND t2.is_delete = 0
          AND t2.is_test = 0
    </select>
</mapper>
