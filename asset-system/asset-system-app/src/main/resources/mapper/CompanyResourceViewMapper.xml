<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.CompanyResourceViewMapper">

    <select id="selectCompanyResource" resultType="com.niimbot.asset.system.model.CompanyResourceView" parameterType="java.lang.Long">
        select t.id as company_id,
               t.name as company_name,
               (ifnull( t.capacity, 0 ) - ifnull( t.asset_num, 0 )) AS remainder,
               ifnull( t.asset_num, 0 ) AS has_used,
               ifnull( t.capacity, 0 ) AS capacity,
               t.expiration_time
        from (
                 select 	c.id, c.name,
                           (select count(1) from as_asset  where is_delete = 0 and company_id = c.id) as asset_num,
                           (SELECT sum( capacity ) FROM as_company_resource WHERE expiration_time > sysdate() AND effective_time <![CDATA[ <= ]]> sysdate() AND company_id = c.id) as capacity,
                           (SELECT max( expiration_time ) FROM as_company_resource WHERE experience = 0 AND expiration_time IS NOT NULL AND company_id = c.id) as expiration_time
                 from as_company c WHERE
                     c.id = #{companyId}
             ) t
    </select>
</mapper>