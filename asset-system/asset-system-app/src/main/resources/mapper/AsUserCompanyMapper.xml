<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCusUserCompanyMapper">

    <cache-ref namespace="com.niimbot.asset.system.mapper.AsCusUserCompanyMapper"/>

    <select id="getCompanyManager" resultType="com.niimbot.asset.system.model.AsCusUser">
        select t1.phone,t1.name
        from as_user_company t
                JOIN as_cus_user t1 on t.user_id = t1.id and t1.is_delete = 0 and t.company_id = #{companyId}
                JOIN as_user_role t2 on t2.user_id = t1.id
                JOIN as_admin_role t3 on t3.id = t2.role_id and t3.role_code = 'admin' and t3.is_delete = 0
    </select>

</mapper>
