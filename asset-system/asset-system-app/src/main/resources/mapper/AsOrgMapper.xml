<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsOrgMapper">

    <!-- dto result map    -->
    <resultMap id="OrgDto" type="com.niimbot.system.OrgDto">
        <id column="id" property="id"/>
        <result column="source_type" property="sourceType"/>
        <result column="org_name" property="orgName"/>
        <result column="org_code" property="orgCode"/>
        <result column="pid" property="pid"/>
        <result column="pid_type" property="pidType"/>
        <result column="level" property="level"/>
        <result column="paths" property="paths"/>
        <result column="sort_num" property="sortNum"/>
        <result column="company_owner" property="companyOwner"/>
        <result column="director" property="director"
                typeHandler="com.niimbot.asset.system.handle.LongListTypeHandler"/>
        <result column="org_type" property="orgType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="listOrg" resultMap="OrgDto">
        SELECT
        o.id,
        o.source_type,
        o.org_name,
        o.org_code,
        o.pid,
        o1.org_type AS pid_type,
        o.LEVEL,
        o.paths,
        o.sort_num,
        o.company_owner,
        o.org_type,
        o.director,
        o.create_by,
        o.create_time,
        o.update_by,
        o.update_time,
        o.remark
        FROM
        as_org o
        LEFT JOIN as_org o1 ON o.company_id = o1.company_id and o.pid = o1.id
        where
        o.is_delete = 0
        <!-- 部门权限 -->
        <if test="deptSql!=null and deptSql!=''">
            and o.id in ${deptSql}
        </if>
        <if test="ew.pid!=null">
            and o.pid = #{ew.pid}
        </if>
        <if test="ew.kw!=null and ew.kw!=''">
            and (
            o.org_name like concat('%',#{ew.kw},'%')
            or o.org_code like concat('%',#{ew.kw},'%')
            <if test="orgUnionIds != null and orgUnionIds.size() > 0">
                or o.org_name in
                <foreach collection="orgUnionIds" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="ew.orgType != null">
            and o.org_type = #{ew.orgType}
        </if>
        <if test="ew.companyId!=null">
            and o.company_id = #{ew.companyId}
        </if>
        <if test="ew.orgId != null">
            and o.company_owner = #{ew.orgId}
        </if>
        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and o.id in
            <foreach collection="ew.includeIds" index="index" item="id" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
        </if>
        order by o.sort_num asc, o.id asc
    </select>

    <select id="getExcelData" resultType="com.niimbot.system.OrgExportDto">
        select o.id, o.pid, o.level, o.sort_num, o.org_code, o.org_name,
        o.org_type as org_type_id,o.create_time,
        d.label as org_type,
        o1.org_code as pid_code,
        o1.org_name as pid_name
        from as_org o
        left join as_org o1 on o1.id = o.pid and o1.company_id = o.company_id
        left join as_dict_data d on d.dict_type = 'org_type' and d.value = o.org_type
        where o.is_delete = 0
        <!-- 部门权限 -->
        <if test="deptSql!=null and deptSql!=''">
            and o.id in ${deptSql}
        </if>
        order by o.level, o.sort_num
    </select>
    <select id="orgRefAsset" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            as_org a
            LEFT JOIN as_asset b ON CONVERT(a.id, CHAR) = b.org_owner and a.company_id = b.company_id
        WHERE
            b.is_delete = 0
            AND a.is_delete = 0
            AND (a.id = #{orgId} or a.paths like concat('%,',#{orgId},',%'))
    </select>
    <select id="useOrgRefAsset" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            as_org a
            LEFT JOIN as_asset b ON CONVERT(a.id, CHAR) = b.use_org and a.company_id = b.company_id
        WHERE
            b.is_delete = 0
            AND a.is_delete = 0
            AND (a.id = #{orgId} or a.paths like concat('%,',#{orgId},',%'))
    </select>

    <resultMap id="AsOrg" type="com.niimbot.asset.system.model.AsOrg">
        <id column="id" property="id"/>
        <result column="org_name" property="orgName"/>
        <result column="org_code" property="orgCode"/>
        <result column="pid" property="pid"/>
        <result column="level" property="level"/>
        <result column="paths" property="paths"/>
        <result column="sort_num" property="sortNum"/>
        <result column="company_owner" property="companyOwner"/>
        <result column="director" property="director"
                typeHandler="com.niimbot.asset.system.handle.LongListTypeHandler"/>
        <result column="org_type" property="orgType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="listByDirector" resultMap="OrgDto">
        select id, org_name, org_code, director from as_org
        where is_delete = 0
          and company_id = #{companyId}
          and json_contains(director, JSON_ARRAY(#{userId}))
    </select>

    <select id="listAllByIds" resultMap="AsOrg">
        select id, org_name, org_code, director from as_org
        where id in
        <foreach collection="orgIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="companyId!=null">
            and company_id = #{companyId}
        </if>
    </select>

    <select id="getOrgCompanyOwner" resultType="java.lang.Long">
        SELECT
            id
        FROM
            as_org
        WHERE
            org_type = 1
            and id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
            ORDER BY
                FIELD(id,
                <foreach collection="ids" item="id" index="index" open="" close="" separator=",">
                    #{id}
                </foreach>
            ) DESC LIMIT 1
    </select>

    <select id="companyList" resultMap="OrgDto">
        SELECT
        <if test="includeChild">
            DISTINCT
        </if>
            o.id,
            o.pid,
            o.LEVEL,
            o.sort_num,
            o.org_code,
            o.org_name,
            o.org_type,
            o.paths,
            o.create_time
        FROM
            as_org o
            <if test="includeChild">
                JOIN as_org oc ON o.company_id = oc.company_id AND oc.is_delete = 0 AND oc.org_type = 1 AND (oc.id = o.id OR FIND_IN_SET( oc.id, o.paths ))
            </if>
        WHERE
            o.is_delete = 0
        AND o.org_type = 1
        <choose>
            <when test="type!='' and type=='area'">
                AND o.id IN (select DISTINCT org_id from as_area a where a.is_delete = 0 and a.company_id = o.company_id and a.id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                )
            </when>
            <when test="type!='' and type=='store'">
                AND o.id IN (select DISTINCT manager_owner from as_repository a where a.is_delete = 0 and a.company_id = o.company_id and a.id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                )
            </when>
            <otherwise>
                <choose>
                    <when test="includeChild">
                        AND oc.id IN
                    </when>
                    <otherwise>
                        AND o.id IN
                    </otherwise>
                </choose>
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
    </select>

    <select id="listSimpleWithPerms" resultType="com.niimbot.asset.framework.model.DictDataDto">
        SELECT
        <choose>
            <when test="authorityType != null
            and ((authorityType == @com.niimbot.asset.framework.constant.AssetConstant@AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT)
            or (authorityType == @com.niimbot.asset.framework.constant.AssetConstant@AUTHORITY_TYPE_COMP_AND_CHILD_DEPT))">
                oc.id as value,
                oc.org_name as label
            </when>
            <otherwise>
                o.id as value,
                o.org_name as label
            </otherwise>
        </choose>
        FROM
            as_org o
            JOIN as_user_org uo ON o.id = uo.org_id
            JOIN as_cus_employee e ON uo.user_id = e.id
        <choose>
            <when test="authorityType != null and authorityType == @com.niimbot.asset.framework.constant.AssetConstant@AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT">
                join as_org oc on oc.is_delete = 0 and o.company_id = oc.company_id and (o.id = oc.id or FIND_IN_SET(o.id, oc.paths))
            </when>
            <when test="authorityType != null and authorityType == @com.niimbot.asset.framework.constant.AssetConstant@AUTHORITY_TYPE_COMP_AND_CHILD_DEPT">
                join as_org oc on oc.is_delete = 0 and o.company_id = oc.company_id and (o.company_owner = oc.id or FIND_IN_SET(o.company_owner, oc.paths))
            </when>
        </choose>
        where o.is_delete = 0 and e.is_delete = 0
          and o.company_id = e.company_id
          and o.company_id = #{companyId}
          and e.id = #{currentUserId}
    </select>

    <select id="hasPermOrgIds" resultType="java.lang.Long">
        select id from as_org
        where is_delete = 0
        <if test="orgType != null">
            and org_type = #{orgType}
        </if>
        <if test="orgIds != null and orgIds.size() > 0">
            and id in
            <foreach collection="orgIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 部门权限 -->
        <if test="deptSql!=null and deptSql!=''">
            and id in ${deptSql}
        </if>
    </select>

    <select id="listByDirectors" resultMap="OrgDto">
        select id, org_name, org_code, director from as_org
        where is_delete = 0
          and company_id = #{companyId}
          <if test="userIds != null and userIds.size() > 0">
              and
                  <foreach collection="userIds" item="id" open="(" separator="or" close=")">
                      json_contains(director, JSON_ARRAY(#{id}))
                  </foreach>
          </if>
    </select>

</mapper>
