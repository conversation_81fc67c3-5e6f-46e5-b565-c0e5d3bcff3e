<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsUserOrgMapper">
    
    <select id="orgEmpList" resultType="com.niimbot.asset.system.model.AsUserOrg">
        SELECT
            uo.org_id AS org_id,
            e.id AS user_id
        FROM
            as_org o
        JOIN as_user_org uo ON o.id = uo.org_id
        JOIN as_cus_employee e ON uo.user_id = e.id
        WHERE
            o.is_delete = 0
        AND o.company_id = e.company_id
        AND o.company_id = #{companyId}
        and e.is_delete = 0
        AND e.STATUS = 1
        <if test="isOnlyShowEmpWithAccount != null and isOnlyShowEmpWithAccount == true">
            and e.id in ( SELECT employee_id FROM as_account_employee ae where e.company_id = ae.company_id)
        </if>
        <if test="orgIds != null and orgIds.size() > 0">
            and uo.org_id in
            <foreach collection="orgIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by uo.org_id
    </select>

    <select id="orgIdsByUserId" resultType="java.lang.Long">
        SELECT
            o.id
        FROM as_cus_employee e
         JOIN as_user_org uo ON e.id = uo.user_id
        <choose>
            <when test="type!=null and type == '1'.toString()">
                JOIN as_org o1 ON uo.org_id = o1.id AND o1.is_delete = 0 AND e.company_id = o1.company_id
                JOIN as_org o ON o1.company_owner = o.id AND o.is_delete = 0 AND e.company_id = o.company_id
            </when>
            <otherwise>
                JOIN as_org o ON uo.org_id = o.id AND o.is_delete = 0 AND e.company_id = o.company_id
            </otherwise>
        </choose>
        WHERE e.is_delete = 0
          and e.company_id = #{companyId}
          and e.id = #{userId}
    </select>

    <select id="orgIdsByUserIds" resultType="com.niimbot.asset.system.model.AsUserOrg">
        SELECT
            e.id as user_id, o.id as org_id
        FROM as_cus_employee e
        JOIN as_user_org uo ON e.id = uo.user_id
        <choose>
            <when test="type!=null and type == '1'.toString()">
                JOIN as_org o1 ON uo.org_id = o1.id AND o1.is_delete = 0 AND e.company_id = o1.company_id
                JOIN as_org o ON o1.company_owner = o.id AND o.is_delete = 0 AND e.company_id = o.company_id
            </when>
            <otherwise>
                JOIN as_org o ON uo.org_id = o.id AND o.is_delete = 0 AND e.company_id = o.company_id
            </otherwise>
        </choose>
        WHERE e.is_delete = 0
        and e.company_id = #{companyId}
        <choose>
            <when test="userIds!=null and userIds.size() > 0">
                and e.id in
                <foreach collection="userIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                and e.id in (SELECT
                t1.employee_id
                FROM
                as_account_employee t1 JOIN as_cus_employee_ext t2 ON t1.employee_id = t2.id
                WHERE
                t1.company_id = #{companyId} and t2.account_status = 3)
            </otherwise>
        </choose>
    </select>

</mapper>