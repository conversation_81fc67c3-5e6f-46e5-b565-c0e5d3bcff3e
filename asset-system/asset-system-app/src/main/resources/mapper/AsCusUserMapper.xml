<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.system.mapper.AsCusUserMapper">

    <cache-ref namespace="com.niimbot.asset.system.mapper.AsCusUserMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="AsCusUserMap" type="com.niimbot.asset.system.model.AsCusUser">
        <result column="id" property="id"/>
        <result column="union_id" property="unionId"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="mobile" property="mobile"/>
        <result column="company_id" property="companyId"/>
        <result column="status" property="status"/>
        <result column="source" property="source"/>
        <result column="business_status" property="businessStatus"/>
        <result column="agreement_status" property="agreementStatus"/>
        <result column="guide_status" property="guideStatus"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, union_id, account, password, mobile, company_id, status, source, business_status, agreement_status, guide_status, is_delete, create_by, create_time, update_by, update_time, remark
    </sql>

    <update id="updateUnionIdByAccountNo">
        update as_cus_user set union_id = #{unionId} where account = #{account}
    </update>

    <select id="queryUserByUnionId" parameterType="string"
            resultType="com.niimbot.asset.framework.model.CusUserDto">
        SELECT
            au.id,
            au.account,
            au.union_id,
            au.password,
            au.mobile,
            au.company_id,
            au.status,
            au.data_scope,
            au.agreement_status,
            au.guide_status,
            ace.org_id
        FROM
            as_cus_user au LEFT JOIN as_cus_employee ace ON au.id = ace.id
        WHERE au.union_id = #{unionId} and au.is_delete = 0
    </select>

    <select id="getCusUserByCondition" resultType="com.niimbot.asset.system.model.AsCusUser">
        select
        <include refid="Base_Column_List"/>
        from as_cus_user where is_delete = 0
        <if test="id!=null">
            and id = #{id}
        </if>
        <if test="mobile!=null and mobile!=''">
            and mobile = #{mobile}
        </if>
    </select>

    <select id="countUserByRoleId" resultType="int">
        SELECT count(*) FROM
            as_cus_role cr
        JOIN as_user_role ur ON cr.id = ur.role_id
        JOIN as_cus_employee cu ON ur.user_id = cu.id
        WHERE cr.is_delete = 0 AND cu.is_delete = 0
            AND ur.role_id = #{roleId}
    </select>


    <resultMap id="userCenterAPPDto" type="com.niimbot.system.UserCenterAPPDto">
        <result column="user_id" property="userId"/>
        <result column="emp_id" property="empId"/>
        <result column="company_id" property="companyId"/>
        <result column="old_password" property="oldPassword"/>
        <result column="account" property="account"/>
        <result column="mobile" property="mobile"/>
        <result column="username" property="username"/>
        <result column="email" property="email"/>
        <result column="company_name" property="companyName"/>
        <result column="company_status" property="companyStatus"/>
        <result column="industry" property="industry"/>
        <result column="logo" property="logo"/>
        <result column="is_pay" property="isPay"/>
        <result column="bind_third_party" property="bindThirdParty"/>
        <result column="image" property="image"/>
        <result column="account_mobile" property="accountMobile"/>
        <result column="account_email" property="accountEmail"/>
        <result column="position" property="position"/>
        <result column="nickname" property="nickname"/>
        <result column="emp_no" property="empNo"/>
        <result column="nationalCode" property="nationalCode"/>
        <collection property="orgList" ofType="com.niimbot.system.CusUserOrgDto">
            <id column="org_id" property="orgId"/>
            <result column="org_name" property="orgName"/>
        </collection>
    </resultMap>

    <select id="getUserCenterAppInfo" resultMap="userCenterAPPDto">
        SELECT
            t3.id AS user_id,
            t2.id AS emp_id,
            t3.`password` AS old_password,
            t3.account,
            t3.image,
            t3.mobile AS account_mobile,
            t3.email AS account_email,
            t3.nickname,
            t2.mobile,
            IFNULL(t2.national_code, '+86') AS nationalCode,
            t2.emp_name AS username,
            t2.email,
            t2.position,
            t2.emp_no,
            t6.`name` AS company_name,
            t6.id AS company_id,
            t6.logo as logo,
            t7.industry_name AS industry,
            (select label from as_dict_data d where d.dict_type = 'company_status' and d.value = t6.status) as company_status,
            (select count(*) > 0 from as_sale_order as b join as_sale_order_item as c on b.id = c.sale_order_id
            where b.company_id = t1.company_id and b.status = 2 and c.type = 5) as is_pay,
            t5.id AS org_id,
            t5.org_name,
            ( SELECT type FROM as_third_party WHERE company_id = t1.company_id LIMIT 1 ) AS bind_third_party
        FROM
            as_account_employee t1
            LEFT JOIN as_cus_employee t2 ON t1.employee_id = t2.id
            LEFT JOIN as_cus_user t3 ON t1.account_id = t3.id
            LEFT JOIN as_user_org t4 ON t1.employee_id = t4.user_id
            LEFT JOIN as_org t5 ON t4.org_id = t5.id
            LEFT JOIN as_company t6 ON t1.company_id = t6.id
            LEFT JOIN as_industry t7 ON t7.id = t6.industry_id
        WHERE
            t1.employee_id = #{id}
    </select>

    <select id="getSonAccountNumByCompanyId" resultType="integer">
        SELECT
            count(id)
        FROM
            as_cus_user
        WHERE
            is_delete = 0
            and company_id = #{companyId}
            and id != (
                select distinct a.user_id from as_user_role a
                left join as_cus_role b on a.role_id = b.id
                where b.company_id = #{companyId} and b.role_code = 'admin'
                );
    </select>

    <select id="getSonListByCompanyId" resultType="com.niimbot.system.SonUserDto">
        SELECT
            t.id AS userId,
            t.account,
            t.mobile,
            t2.emp_name AS username,
            t.create_time as createTime,
            t.update_time as updateTime
        FROM
            as_cus_user t
            LEFT JOIN as_cus_employee t2 ON t2.id = t.id
        WHERE
            t.is_delete = 0
            and t.company_id = #{companyId}
            and t.id != (
                select distinct a.user_id from as_user_role a
                left join as_cus_role b on a.role_id = b.id
                where b.company_id = #{companyId} and b.role_code = 'admin'
                );
    </select>

    <resultMap id="cusUserDetailDto" type="com.niimbot.system.CusUserDetailDto">
        <result column="emp_id" property="empId"/>
        <result column="emp_name" property="empName"/>
        <result column="company_id" property="companyId"/>
        <result column="company_name" property="companyName"/>
        <result column="company_logo" property="companyLogo"/>
        <result column="company_status" property="companyStatus"/>
        <result column="company_source" property="companySource"/>
        <result column="create_time" property="createTime"/>
        <result column="account_id" property="accountId"/>
        <result column="image" property="image"/>
        <result column="agreement_status" property="agreementStatus"/>
        <result column="guide_status" property="guideStatus"/>
        <result column="photo_num_amount" property="photoNumAmount"/>
        <result column="appendix_num_amount" property="appendixNumAmount"/>
        <result column="status" property="status"/>
        <result column="is_admin" property="isAdmin"/>
        <result column="asset_simplify" property="assetSimplify"/>
        <result column="mobile" property="mobile"/>
        <result column="email" property="email"/>
        <result column="is_pay" property="isPay"/>
        <result column="nationalCode" property="nationalCode"/>
        <result column="bind_third_party" property="bindThirdParty"/>
        <result column="third_party_init_sync" property="thirdPartyInitSync"/>
        <result column="admin_has_remove" property="adminHasRemove"/>
        <collection property="orgList" ofType="com.niimbot.system.CusUserOrgDto">
            <id column="org_id" property="orgId"/>
            <result column="org_name" property="orgName"/>
        </collection>
    </resultMap>

    <select id="personDetail" resultMap="cusUserDetailDto">
        SELECT
            t2.id AS emp_id,
            t2.emp_no,
            t2.emp_name,
            t3.image,
            t2.mobile,
            IFNULL(t2.national_code,'+86') as nationalCode,
            t2.email,
            t4.id AS company_id,
            t4.`name` AS company_name,
            t4.logo AS company_logo,
            t4.status as company_status,
            t4.source as company_source,
            t4.create_time as create_time,
            t3.id AS account_id,
            t3.agreement_status,
            t3.guide_status,
            t6.asset_simplify,
            t8.id AS org_id,
            t8.org_name,
            ( SELECT COUNT(1) FROM as_user_role t9, as_cus_role t10 WHERE t9.role_id = t10.id AND t9.user_id = t2.id AND t10.role_code = 'admin' ) AS is_admin,
            ( SELECT type FROM as_third_party t11 WHERE t11.company_id = t2.company_id LIMIT 1 ) AS bind_third_party,
            ( SELECT init_sync FROM as_third_party t12 WHERE t12.company_id = t2.company_id LIMIT 1 ) AS third_party_init_sync,
	        t5.third_party_remove AS admin_has_remove,
             (select count(*) > 0 from as_sale_order as b join as_sale_order_item as c on b.id = c.sale_order_id
            where b.company_id = t1.company_id and b.status = 2 and c.type = 5) as is_pay
        FROM
            as_account_employee t1
            INNER JOIN as_cus_employee t2 ON t1.employee_id = t2.id
            INNER JOIN as_cus_user t3 ON t1.account_id = t3.id
            INNER JOIN as_company t4 ON t1.company_id = t4.id
            LEFT JOIN as_cus_employee_ext t5 ON t1.employee_id = t5.id
            LEFT JOIN as_cus_employee_setting t6 ON t1.employee_id = t6.user_id
            LEFT JOIN as_user_org t7 ON t1.employee_id = t7.user_id
            LEFT JOIN as_org t8 ON t7.org_id = t8.id
        WHERE t1.employee_id = #{userId}
    </select>

    <select id="getUserCenterPcInfo" resultType="com.niimbot.system.UserCenterPCDto">
        SELECT
            t3.`password` AS old_password,
            case when LENGTH(ifnull(t3.`password`,''))>0 then 1 else 0 end as is_password_set,
            t3.image,
            t3.nickname,
            t3.account,
            t3.mobile AS account_mobile,
            t3.email AS account_email,
            t2.emp_name AS username,
            t2.mobile,
            t2.email,
            t2.emp_no,
            t2.position,
            t4.`name` AS company_name,
            t4.logo,
            t4.update_times,
            t5.industry_name,
             (select count(*) > 0 from as_sale_order as b join as_sale_order_item as c on b.id = c.sale_order_id
            where b.company_id = t1.company_id and b.status = 2 and c.type = 5) as is_pay
        FROM
            as_account_employee t1
                INNER JOIN as_cus_employee t2 ON t1.employee_id = t2.id
                INNER JOIN as_cus_user t3 ON t1.account_id = t3.id
                INNER JOIN as_company t4 ON t1.company_id = t4.id
                LEFT JOIN as_industry t5 ON t4.industry_id = t5.id
        WHERE
            t1.employee_id = #{id}
            AND t2.is_delete = 0
            AND t2.`status` = 1
            AND t3.is_delete = 0
            AND t3.`status` = 1
    </select>

    <select id="userInfo" resultType="com.niimbot.system.CusUserDetailDto">
        SELECT
            ce.id as emp_id,
            ce.emp_name,
            com.id as company_id,
            com.name as company_name,
            cu.status as status,
            ce.status as emp_status
        FROM
            as_cus_user cu
            JOIN as_cus_employee ce ON cu.id = ce.id
            JOIN as_company com ON cu.company_id = com.id
        WHERE
            cu.id = #{userId} and cu.is_delete = 0 and ce.is_delete = 0
    </select>

    <select id="getAdminUserInfo" resultType="com.niimbot.system.UserCenterPCDto">
        SELECT
            ce.emp_name as admin_user_name,
            ce.mobile as admin_user_mobile
        FROM
            as_cus_employee ce
            LEFT JOIN as_user_role ur ON ur.user_id = ce.id
            LEFT JOIN as_cus_role cr ON cr.id = ur.role_id
        WHERE
            cr.role_code = 'admin'
            AND cr.company_id = #{id}
            AND cr.is_delete = 0
            AND ce.is_delete = 0
    </select>

</mapper>
