package com.niimbot.asset.message.adapter.saas.strategies.notice;

import com.niimbot.asset.framework.constant.NoticeConstant;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.message.model.AsNoticeSetting;
import com.niimbot.asset.message.service.NoticeEventStrategy;
import com.niimbot.asset.message.service.NoticeTypeStrategy;
import com.niimbot.asset.system.adapter.SystemCommonAdapter;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.system.RawExternalNotice;
import com.niimbot.system.crm.CrmErrorInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CrmPushFailedEventNotice extends NoticeEventStrategy {

    // INSERT INTO `fixed_asset`.`as_notice_setting` (`id`, `type`, `event`, `is_order_remind`, `threshold`, `remind_interval`, `url`, `sign`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (1506161653794869248, 1, 'crmPushFailed', 1, NULL, NULL, 'https://oapi.dingtalk.com/robot/send?access_token=778498f84108f186b800c4c5b6926c170277736b2b2f726d62a5d3267a214310', 'SECb8b7cfe16eb18a0a50fdf704f7f5ac914361d9aef7b96dc8e4bb06932941834f', 1, '2022-03-22 14:49:59', 1, '2022-03-22 14:50:07');

    private final CacheResourceUtil cacheResourceUtil;

    private final SystemCommonAdapter systemCommonAdapter;

    @Override
    public String getNoticeEvent() {
        return NoticeConstant.CRM_PUSH_FAILED;
    }

    @Override
    public RawExternalNotice buildRawNotice(Long uid, AsCompany company, AsNoticeSetting noticeSetting) {
        RawExternalNotice rawExternalNotice = new RawExternalNotice();
        rawExternalNotice.setCompanyId(company.getId()).setCompanyName(company.getName());
        CrmErrorInfo crmSyncError = systemCommonAdapter.getSaasCrmErrorInfo(uid);
        String result = crmSyncError.getErrorInfo();
        LocalDateTime time = crmSyncError.getCreateTime();
        String type = cacheResourceUtil.getDictLabel("crm_sync_error_type", String.valueOf(crmSyncError.getType()));
        RawExternalNotice.CrmPushFailed crmPushFailed = new RawExternalNotice.CrmPushFailed().setType(type).setTime(time).setResult(result);
        rawExternalNotice.setCrmPushFailed(crmPushFailed);
        return rawExternalNotice;
    }

    @Override
    public Object buildNoticeContent(AsNoticeSetting noticeSetting, RawExternalNotice raw, NoticeTypeStrategy noticeTypeStrategy) {
        return noticeTypeStrategy.buildCrmPushFailed(raw);
    }

}
