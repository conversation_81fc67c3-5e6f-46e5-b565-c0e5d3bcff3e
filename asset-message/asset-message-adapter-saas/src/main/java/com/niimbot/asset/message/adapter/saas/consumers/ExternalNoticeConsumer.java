package com.niimbot.asset.message.adapter.saas.consumers;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.niimbot.asset.framework.annotation.MessageConsumer;
import com.niimbot.asset.framework.autoconfig.rocketmq.RocketMqConsumerListener;
import com.niimbot.asset.framework.constant.MqConstant;
import com.niimbot.asset.message.service.ExternalNoticeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.SendExternalNotice;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@MessageConsumer(topic = MqConstant.ASSET_TOPIC, group = MqConstant.ASSET_EXTERNAL_NOTICE_CONSUMER_GROUP, tags = MqConstant.ASSET_EXTERNAL_NOTICE_SEND)
@RequiredArgsConstructor
public class ExternalNoticeConsumer implements RocketMqConsumerListener<SendExternalNotice> {

    @Resource
    private ExternalNoticeService externalNoticeService;

    @Override
    public Action consume(SendExternalNotice messageBody, ConsumeContext consumeContext) {
        // 部分消息等一会；如：下单提醒，防止事务还未提交
        if (Objects.nonNull(messageBody.getDelay())) {
            try {
                TimeUnit.MILLISECONDS.sleep(messageBody.getDelay());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                // ignore
            }
        }
        try {
            externalNoticeService.sendNotice(messageBody);
        } catch (IllegalArgumentException e) {
            // 参数不合法
            log.info("传递的参数不和法", e);
        } catch (BusinessException e) {
            // 业务异常
            log.info("不满足发送外部消息条件", e);
        } catch (Exception e) {
            // 未知异常
            log.error("ExternalNoticeConsumer Handle Error", e);
            return Action.ReconsumeLater;
        }
        return Action.CommitMessage;
    }
}
