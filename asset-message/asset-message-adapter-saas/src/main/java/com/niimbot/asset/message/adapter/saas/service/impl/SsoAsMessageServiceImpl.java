package com.niimbot.asset.message.adapter.saas.service.impl;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.BaseConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.SsoExceptionUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.message.model.AsMessage;
import com.niimbot.asset.message.model.AsMessageTemplate;
import com.niimbot.asset.message.service.impl.AsMessageServiceImpl;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.kalimdor.hulk.exceptions.ClientException;
import com.niimbot.kalimdor.quicksilver.QuickSilverKamClient;
import com.niimbot.kalimdor.quicksilver.model.BatchEmailMessageRequest;
import com.niimbot.kalimdor.quicksilver.model.BatchPhoneMessageRequest;
import com.niimbot.kalimdor.quicksilver.model.MessageSendResponse;
import com.niimbot.middlend.core.component.message.MessageSender;
import com.niimbot.middlend.core.component.message.bean.Address;
import com.niimbot.middlend.core.component.message.bean.JPushAddress;
import com.niimbot.middlend.core.component.message.bean.MessageRequest;
import com.niimbot.middlend.core.component.message.bean.Response;
import com.niimbot.middlend.core.component.message.exception.IllegalMessageException;
import com.niimbot.system.MessageSendDto;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息记录表
 *
 * <AUTHOR>
 * @date 2021/4/20 14:32
 */
@Slf4j
@Service
@Profile({Edition.SAAS})
public class SsoAsMessageServiceImpl extends AsMessageServiceImpl {
    @Autowired
    private QuickSilverKamClient quickSilverKamClient;
    // todo 推送暂时用本地实现
    @Autowired
    private MessageSender messageSender;
    @Autowired
    private RedisService redisService;

    @Autowired
    private AsCusEmployeeService cusEmployeeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSendEmailMessage(MessageSendDto messageSendDto, Set<String> addresses) {
        // 查找模板
        AsMessageTemplate messageTemplate = getTemplate(messageSendDto.getCode(), AssetConstant.EMAIL);
        if (StrUtil.isBlank(messageTemplate.getContent())) {
            throw new BusinessException(SystemResultCode.MESSAGE_TEMPLATE_CONTENT_EMPTY);
        }

        if (CollectionUtils.size(messageSendDto.getMapParam()) != messageTemplate.getParamLength()) {
            throw new BusinessException(SystemResultCode.MESSAGE_PARAM_LENGTH_NOT_MATCH);
        }
        messageSendDto.setSubject(messageTemplate.getName());
        messageSendDto.setContent(JSON.toJSONString(messageSendDto.getMapParam()));
        // this.saveMessage(messageSendDto, messageTemplate, addresses);
        AsMessage message = buildMessage(messageSendDto, messageTemplate);
        log.info("线程[{}] 保存邮件消息 : {}", Thread.currentThread().getName(), message);
        this.save(message);
        // 构建消息
//        AsMessage message = new AsMessage()
//                .setId(IdUtils.getId())
//                .setCode(messageSendDto.getCode())
//                .setNickname(messageSendDto.getNickname())
//                .setBusinessType(messageSendDto.getBusinessType())
//                .setSubject(messageSendDto.getSubject())
//                .setContent(JSON.toJSONString(messageSendDto.getMapParam()))
//                .setType(AssetConstant.EMAIL);
//        if (messageSendDto.getExpired() != null) {
//            message.setExpiredTime(
//                    LocalDateTime.now().plusDays(messageSendDto.getExpired())
//                            .withHour(23).withMinute(59).withSecond(59).withNano(0));
//        }
//        // 保存消息
//        this.save(message);
        try {
            BatchEmailMessageRequest request = new BatchEmailMessageRequest();
            request.setAddresses(addresses);
            request.setNickname(messageSendDto.getNickname());
            request.setSubject(messageSendDto.getSubject());
            request.setParamLength(messageTemplate.getParamLength());
            request.setTemplateContent(messageTemplate.getContent());
            request.setParams(messageSendDto.getMapParam());
            MessageSendResponse messageSendResponse = quickSilverKamClient.batchEmailMessage(request);
        } catch (ClientException e) {
            log.error("消息发送失败", e);
            // throw SsoExceptionUtils.resolveCallClientException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSendSmsMessage(MessageSendDto messageSendDto, Set<String> phoneNumbers) {
        // 查找模板
        AsMessageTemplate messageTemplate = getTemplate(messageSendDto.getCode(), AssetConstant.SMS);
        if (StrUtil.isBlank(messageTemplate.getExtCode())) {
            throw new BusinessException(SystemResultCode.SMS_MESSAGE_TEMPLATE_NOT_EXT_CODE);
        }

        if (CollectionUtils.size(messageSendDto.getArrayParam()) != messageTemplate.getParamLength()) {
            throw new BusinessException(SystemResultCode.MESSAGE_PARAM_LENGTH_NOT_MATCH);
        }
        messageSendDto.setSubject(messageTemplate.getName());
        messageSendDto.setContent(JSON.toJSONString(messageSendDto.getArrayParam()));
        // this.saveMessage(messageSendDto, messageTemplate, phoneNumbers);
        AsMessage message = buildMessage(messageSendDto, messageTemplate);
        log.info("线程[{}] 保存短信消息 : {}", Thread.currentThread().getName(), message);
        this.save(message);
        try {
            //区号改为动态
            List<AsCusEmployee> list = cusEmployeeService.list(Wrappers.<AsCusEmployee>lambdaQuery().select(AsCusEmployee::getMobile, AsCusEmployee::getNationalCode).in(AsCusEmployee::getMobile, phoneNumbers));
            log.info("SsoAsMessageServiceImpl 获取employee:[{}]",list);
            for (AsCusEmployee employee : list) {
                //将手机号转成set
                Set<String> mobiles = new HashSet<>();
                Collections.addAll(mobiles, employee.getMobile());
                BatchPhoneMessageRequest request = new BatchPhoneMessageRequest();
                request.setIddCode(StringUtils.isEmpty(employee.getNationalCode())?"86":employee.getNationalCode().replace("+",""));
                request.setPhoneNumbers(mobiles);
                request.setParams(messageSendDto.getArrayParam());
                request.setMessageCode(messageTemplate.getExtCode());
                // 开发环境
//            request.setPretend(true);
                MessageSendResponse messageSendResponse = quickSilverKamClient.batchPhoneMessage(request);
            }

        } catch (ClientException e) {
            log.error("消息发送失败", e);
            // throw SsoExceptionUtils.resolveCallClientException(e);
        }
    }

    public static void main(String[] args) {
        Set<String> set = new HashSet<>();
        Collections.addAll(set,"***********");
        System.out.println(set);
    }

    @Override
    protected void doSendPushMessage(MessageSendDto messageSendDto, Set<String> addresses) {
        // 查找模板
        AsMessageTemplate messageTemplate = getTemplate(messageSendDto.getCode(), AssetConstant.APP_PUSH);
        if (StrUtil.isBlank(messageTemplate.getContent())) {
            log.error("msgId = {}, 消息模板未配置模板内容", messageSendDto.getMsgId());
            throw new BusinessException(SystemResultCode.MESSAGE_TEMPLATE_CONTENT_EMPTY);
        }

        if (CollectionUtils.size(messageSendDto.getAppMapParam()) != messageTemplate.getParamLength()) {
            log.error("msgId = {}, 消息参数个数不匹配, [{}] ", messageSendDto.getMsgId(), messageSendDto.getAppMapParam());
            throw new BusinessException(SystemResultCode.MESSAGE_PARAM_LENGTH_NOT_MATCH);
        }
        messageSendDto.setSubject(messageTemplate.getName());
        messageSendDto.setContent(JSON.toJSONString(messageSendDto.getAppMapParam()));
        // 构建消息
        Map<String, String> mapParam = new HashMap<>(messageSendDto.getAppMapParam());
        if (Objects.nonNull(messageSendDto.getAppExtMapParam()) && !messageSendDto.getAppExtMapParam().isEmpty()) {
            messageSendDto.getAppMapParam().putAll(messageSendDto.getAppExtMapParam());
        }
        // 保存消息
        Long messageId = this.saveMessage(messageSendDto, messageTemplate, addresses);
        // AsMessage message = buildMessage(messageSendDto, messageTemplate);
        // log.info("线程[{}] 保存APP消息 : {}", Thread.currentThread().getName(), message);
        // this.save(message);
        try {
            Set<Address> addressSet = new HashSet<>();
            addresses.forEach(str -> {
                addressSet.add(new JPushAddress(str));
            });
            MessageRequest request = new MessageRequest();
            request.setSignName(messageSendDto.getNickname());
            request.setParamLength(messageTemplate.getParamLength());
            request.setTemplateContent(deleteHTML(messageTemplate.getContent()));
            request.setTemplateName(messageSendDto.getSubject());
            request.setMapParam(mapParam);
            request.setAddresses(addressSet);
            // 补充额外字段
            Map<String, String> ext = new HashMap<>();
            ext.put("message_code", messageSendDto.getCode());
            ext.put("message_id", String.valueOf(messageId));
            if (Objects.nonNull(messageSendDto.getAppExtMapParam())) {
                ext.putAll(messageSendDto.getAppExtMapParam());
            }
            request.setExt(ext);
            Response response = messageSender.sendMessage(request);
            if (Response.FAIL.equals(response.getStatus())) {
                log.error("发送邮件失败，response[{}]", response);
                throw new BusinessException(SystemResultCode.MESSAGE_SEND_FAIL);
            }
        } catch (IllegalMessageException e) {
            log.error("消息发送失败", e);
            // throw new BusinessException(SystemResultCode.MESSAGE_SEND_FAIL);
        }
    }

    @Override
    public void sendEmailCode(Set<String> emailAddressList) {
        if (CollUtil.isNotEmpty(emailAddressList)) {
            return;
        }
        try {
            BatchEmailMessageRequest request = new BatchEmailMessageRequest();
            request.setAddresses(emailAddressList);
            request.setNickname("【NIIMBOT】精臣");
            request.setSubject("【NIIMBOT】修改密码验证码");
            request.setParamLength(1);
            request.setTemplateContent(EMAIL_MESSAGE_TEMPLATE);
            String code = StringUtils.randomVerifyCode(4);
            request.setParams(ImmutableMap.of("code", code));
            quickSilverKamClient.batchEmailMessage(request);
            // redis 存入验证码
            emailAddressList.forEach(str ->
                    redisService.set(BaseConstant.EMAIL_CODE_KEY + str, code, 5, TimeUnit.MINUTES)
            );
        } catch (ClientException e) {
            log.error("邮件验证码消息发送失败", e);
            throw SsoExceptionUtils.resolveCallClientException(e);
        } catch (Exception e) {
            log.error("邮件验证码消息发送失败", e);
            throw new BusinessException(SystemResultCode.MESSAGE_SEND_FAIL);
        }
    }
}
