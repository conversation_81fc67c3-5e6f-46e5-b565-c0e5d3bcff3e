<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.report.mapper.AsCustomReportMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.report.model.AsCustomReport">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_name" jdbcType="VARCHAR" property="reportName" />
    <result column="dimension" property="dimension" typeHandler="com.niimbot.asset.report.handle.DimensionItemTypeHandler" />
    <result column="norm" property="norm" typeHandler="com.niimbot.asset.report.handle.NormItemTypeHandler" />
    <result column="summary" property="summary" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="statistic_condition" property="statisticCondition" typeHandler="com.niimbot.asset.report.handle.StatisticsConditionTypeHandler" />
    <result column="sort_condition" property="sortCondition" typeHandler="com.niimbot.asset.report.handle.SortTypeHandler" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, report_name, dimension, norm, summary, statistic_condition, sort_condition, `type`, 
    company_id, is_delete, create_by, create_time, update_by, update_time
  </sql>
</mapper>