<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.report.mapper.MaterialPredefinedReportMapper">

    <sql id="materialRkManifestSql">
        (
        select
        rd.id,
        FROM_UNIXTIME( r.order_data ->> '$.storageTime' / 1000, '%Y-%m-%d %H:%i:%s') as rk_time,
        IF(r.order_data ->> '$.inRepo' = 'null', null, r.order_data ->> '$.inRepo') as rk_repo,
        IF(r.order_data ->> '$.inRepoText' = 'null', null, r.order_data ->> '$.inRepoText') as rk_repo_text,
        IF(r.order_data ->> '$.rkType' = 'null', null, r.order_data ->> '$.rkType') as rk_type,
        rd.material_id,
        IF(rd.material_snapshot_data ->> '$.materialName' = 'null', null, rd.material_snapshot_data ->> '$.materialName') as material_name,
        IF(rd.material_snapshot_data ->> '$.materialCode' = 'null', null, rd.material_snapshot_data ->> '$.materialCode') as material_code,
        IF(rd.material_snapshot_data ->> '$.materialCategory' = 'null', null, rd.material_snapshot_data ->> '$.materialCategory') as material_category,
        IF(rd.material_snapshot_data ->> '$.materialCategoryText' = 'null', null, rd.material_snapshot_data ->> '$.materialCategoryText') as material_category_text,
        IF(rd.material_snapshot_data ->> '$.barCode' = 'null', null, rd.material_snapshot_data ->> '$.barCode') as bar_code,
        IF(rd.material_snapshot_data ->> '$.model' = 'null', null, rd.material_snapshot_data ->> '$.model') as model,
        IF(rd.material_snapshot_data ->> '$.brand' = 'null', null, rd.material_snapshot_data ->> '$.brand') as brand,
        IF(rd.material_snapshot_data ->> '$.unit' = 'null', null, rd.material_snapshot_data ->> '$.unit') as unit,
        rd.rk_num,
        rd.rk_price,
        r.order_no,
        IF(r.order_data ->> '$.remark' = 'null', null, r.order_data ->> '$.remark') as remark
        from as_material_rk_order r
        join as_material_rk_order_detail rd on r.id = rd.order_id
        where r.company_id = #{companyId} and r.approve_status IN ( 0, 3 )
        union all
        SELECT
        tkd.id,
        FROM_UNIXTIME( tk.order_data ->> '$.tkDate' / 1000, '%Y-%m-%d %H:%i:%s') as rk_time,
        tk.in_repo as rk_repo,
        tk.in_repo_text as rk_repo_text,
        '退库单入库' as rk_type,
        tkd.material_id,
        IF(tkd.material_snapshot_data ->> '$.materialName' = 'null', null, tkd.material_snapshot_data ->> '$.materialName') as material_name,
        IF(tkd.material_snapshot_data ->> '$.materialCode' = 'null', null, tkd.material_snapshot_data ->> '$.materialCode') as material_code,
        IF(tkd.material_snapshot_data ->> '$.materialCategory' = 'null', null, tkd.material_snapshot_data ->> '$.materialCategory') as material_category,
        IF(tkd.material_snapshot_data ->> '$.materialCategoryText' = 'null', null, tkd.material_snapshot_data ->> '$.materialCategoryText') as material_category_text,
        IF(tkd.material_snapshot_data ->> '$.barCode' = 'null', null, tkd.material_snapshot_data ->> '$.barCode') as bar_code,
        IF(tkd.material_snapshot_data ->> '$.model' = 'null', null, tkd.material_snapshot_data ->> '$.model') as model,
        IF(tkd.material_snapshot_data ->> '$.brand' = 'null', null, tkd.material_snapshot_data ->> '$.brand') as brand,
        IF(tkd.material_snapshot_data ->> '$.unit' = 'null', null, tkd.material_snapshot_data ->> '$.unit') as unit,
        tkd.tk_num as rk_num,
        tkd.tk_price as rk_price,
        tk.order_no,
        IF(tk.order_data ->> '$.remark' = 'null', null, tk.order_data ->> '$.remark') as remark
        FROM
        as_material_tk_order tk
        JOIN as_material_tk_order_detail tkd ON tk.id = tkd.order_id
        JOIN as_material_ck_order ck ON tk.company_id = ck.company_id AND ck.order_no = tk.order_data ->> '$.ckOrderNo'
        JOIN as_material_ck_order_detail ckd ON ck.id = ckd.order_id AND tkd.material_id = ckd.material_id
        WHERE
        tk.company_id = #{companyId} and tk.approve_status IN ( 0, 3 )
        union all
        SELECT
        l.id,
        l.create_time as rk_time,
        l.repository_id as rk_repo,
        r.name as rk_repo_text,
        '盘盈入库' as rk_type,
        d.material_id,
        d.material_snapshot_data ->> '$.materialName' as material_name,
        d.material_snapshot_data ->> '$.materialCode' as material_code,
        d.material_snapshot_data ->> '$.materialCategory' as material_category,
        d.material_snapshot_data ->> '$.materialCategoryText' as material_category_text,
        d.material_snapshot_data ->> '$.barCode' as bar_code,
        d.material_snapshot_data ->> '$.model' as model,
        d.material_snapshot_data ->> '$.brand' as brand,
        d.material_snapshot_data ->> '$.unit' as unit,
        l.change_num as rk_num,
        l.change_money as rk_price,
        l.order_no,
        '' as remark
        FROM
        as_material_stock_log l
        join as_material_stock st on l.stock_id = st.id
        JOIN as_material_inventory_detail d ON l.company_id = d.company_id
        AND l.order_id = d.inventory_id
        AND l.repository_id = d.repository_id
        AND st.material_id = d.material_id
        left join as_repository r on l.repository_id= r.id
        WHERE
        l.action_type = 81 and l.company_id = #{companyId}
        ) t
        <where>
            <if test="ew.includeIds != null and ew.includeIds.size > 0">
                and id in
                <foreach collection="ew.includeIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="ew.rkTime != null and ew.rkTime.size == 2">
                <if test="ew.rkTime[0] != null and ew.rkTime[0] != '' and ew.rkTime[0] != 'null'">
                    and rk_time &gt;= CONCAT(#{ew.rkTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.rkTime[1]!=null and ew.rkTime[1] != '' and ew.rkTime[1] != 'null'">
                    and rk_time &lt;= CONCAT(#{ew.rkTime[1]}, ' 23:59:59')
                </if>
            </if>

            <if test="ew.rkRepo != null and ew.rkRepo.size > 0">
                and CAST(rk_repo as DECIMAL(19)) in
                <foreach collection="ew.rkRepo" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeSql != null and storeSql!=''">
                and CAST(rk_repo as DECIMAL(19)) in ${storeSql}
            </if>
            <if test="ew.rkType != null and ew.rkType.size > 0">
                and rk_type in
                <foreach collection="ew.rkType" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="ew.materialName!=null and ew.materialName!=''">
                and material_name like concat('%',#{ew.materialName},'%')
            </if>

            <if test="ew.materialCode!=null and ew.materialCode!=''">
                and material_code like concat('%',#{ew.materialCode},'%')
            </if>

            <if test="ew.materialCategory != null and ew.materialCategory.size > 0">
                and material_category in
                <foreach collection="ew.materialCategory" item="item" index="index" open="(" separator="," close=")">
                    CONCAT(#{item} , '')
                </foreach>
            </if>

            <if test="ew.barCode!=null and ew.barCode!=''">
                and bar_code like concat('%',#{ew.barCode},'%')
            </if>

            <if test="ew.model!=null and ew.model!=''">
                and model like concat('%',#{ew.model},'%')
            </if>

            <if test="ew.brand!=null and ew.brand!=''">
                and brand like concat('%',#{ew.brand},'%')
            </if>

            <if test="ew.unit!=null and ew.unit!=''">
                and unit like concat('%',#{ew.unit},'%')
            </if>

            <if test="ew.rkNum != null and ew.rkNum.size == 2">
                <if test="ew.rkNum[0] != null">
                    and rk_num &gt;= #{ew.rkNum[0]}
                </if>
                <if test="ew.rkNum[1]!=null">
                    and rk_num &lt;= #{ew.rkNum[1]}
                </if>
            </if>

            <if test="ew.rkPrice != null and ew.rkPrice.size == 2">
                <if test="ew.rkPrice[0] != null">
                    and rk_price &gt;= #{ew.rkPrice[0]}
                </if>
                <if test="ew.rkPrice[1]!=null">
                    and rk_price &lt;= #{ew.rkPrice[1]}
                </if>
            </if>

            <if test="ew.orderNo!=null and ew.orderNo!=''">
                and order_no like concat('%',#{ew.orderNo},'%')
            </if>

            <if test="ew.remark!=null and ew.remark!=''">
                and remark like concat('%',#{ew.remark},'%')
            </if>
        </where>
    </sql>

    <select id="materialRkManifest" resultType="com.niimbot.report.MaterialRkManifestDto">
        select * from
        <include refid="materialRkManifestSql" />
        order by rk_time desc
    </select>

    <select id="materialRkManifestTotal" resultType="com.niimbot.report.MaterialManifestTotalDto">
        select sum(t.rk_num) as total_num,
        sum(t.rk_price) as total_money from
        <include refid="materialRkManifestSql" />
    </select>

    <sql id="materialCkManifestSql">
        (
        SELECT
        cd.id,
        FROM_UNIXTIME( c.order_data ->> '$.outRepoTime' / 1000, '%Y-%m-%d %H:%i:%s') as ck_time,
        IF(c.order_data ->> '$.outRepo' = 'null', null, c.order_data ->> '$.outRepo') as ck_repo,
        IF(c.order_data ->> '$.outRepoText' = 'null', null, c.order_data ->> '$.outRepoText') as ck_repo_text,
        IF(c.order_data ->> '$.ckType' = 'null', null, c.order_data ->> '$.ckType') as ck_type,
        IF(c.order_data ->> '$.receiveOrg' = 'null', null, c.order_data ->> '$.receiveOrg') as receive_org,
        IF(c.order_data ->> '$.receiveOrgText' = 'null', null, c.order_data ->> '$.receiveOrgText') as receive_org_text,
        IF(c.order_data ->> '$.receiveUser' = 'null', null, c.order_data ->> '$.receiveUser') as receive_user,
        IF(c.order_data ->> '$.receiveUserText' = 'null', null, c.order_data ->> '$.receiveUserText') as receive_user_text,
        cd.material_id,
        IF(cd.material_snapshot_data ->> '$.materialName' = 'null', null, cd.material_snapshot_data ->> '$.materialName') as material_name,
        IF(cd.material_snapshot_data ->> '$.materialCode' = 'null', null, cd.material_snapshot_data ->> '$.materialCode') as material_code,
        IF(cd.material_snapshot_data ->> '$.materialCategory' = 'null', null, cd.material_snapshot_data ->> '$.materialCategory') as material_category,
        IF(cd.material_snapshot_data ->> '$.materialCategoryText' = 'null', null, cd.material_snapshot_data ->> '$.materialCategoryText') as material_category_text,
        IF(cd.material_snapshot_data ->> '$.barCode' = 'null', null, cd.material_snapshot_data ->> '$.barCode') as bar_code,
        IF(cd.material_snapshot_data ->> '$.model' = 'null', null, cd.material_snapshot_data ->> '$.model') as model,
        IF(cd.material_snapshot_data ->> '$.brand' = 'null', null, cd.material_snapshot_data ->> '$.brand') as brand,
        IF(cd.material_snapshot_data ->> '$.unit' = 'null', null, cd.material_snapshot_data ->> '$.unit') as unit,
        cd.ck_num,
        cd.ck_price,
        c.order_no,
        IF(c.order_data ->> '$.remark' = 'null', null, c.order_data ->> '$.remark') as remark
        from as_material_ck_order c
        join as_material_ck_order_detail cd on c.id = cd.order_id
        where c.company_id = #{companyId} and c.approve_status IN ( 0, 3 )
        union all
        select
        od.id,
        FROM_UNIXTIME( o.order_data ->> '$.bsDate' / 1000, '%Y-%m-%d %H:%i:%s') as ck_time,
        o.order_data ->> '$.outRepo' as ck_repo,
        o.order_data ->> '$.outRepoText' as ck_repo_text,
        '报损出库' as ck_type,
        '' as receive_org,
        '' as receive_org_text,
        '' as receive_user,
        '' as receive_user_text,
        od.material_id,
        od.material_snapshot_data ->> '$.materialName' as material_name,
        od.material_snapshot_data ->> '$.materialCode' as material_code,
        od.material_snapshot_data ->> '$.materialCategory' as material_category,
        od.material_snapshot_data ->> '$.materialCategoryText' as material_category_text,
        od.material_snapshot_data ->> '$.barCode' as bar_code,
        od.material_snapshot_data ->> '$.model' as model,
        od.material_snapshot_data ->> '$.brand' as brand,
        od.material_snapshot_data ->> '$.unit' as unit,
        od.bs_num as ck_num,
        od.bs_price as ck_price,
        o.order_no,
        o.order_data ->> '$.remark' as remark
        from as_material_bs_order o
        join as_material_bs_order_detail od on o.id = od.order_id
        where o.order_data ->> '$.subtractStock' = '是' and o.company_id = #{companyId} and o.approve_status IN ( 0, 3 )
        union all
        select
        r.id,
        FROM_UNIXTIME( mt.order_data ->> '$.maintainEndDate' / 1000, '%Y-%m-%d %H:%i:%s') as ck_time,
        mt.order_data ->> '$.outRepo' as ck_repo,
        mt.order_data ->> '$.outRepoText' as ck_repo_text,
        '设备保养出库' as ck_type,
        '' as receive_org,
        '' as receive_org_text,
        '' as receive_user,
        '' as receive_user_text,
        r.material_id,
        r.material_snapshot_data ->> '$.materialName' as material_name,
        r.material_snapshot_data ->> '$.materialCode' as material_code,
        r.material_snapshot_data ->> '$.materialCategory' as material_category,
        r.material_snapshot_data ->> '$.materialCategoryText' as material_category_text,
        r.material_snapshot_data ->> '$.barCode' as bar_code,
        r.material_snapshot_data ->> '$.model' as model,
        r.material_snapshot_data ->> '$.brand' as brand,
        r.material_snapshot_data ->> '$.unit' as unit,
        r.ck_num,
        r.ck_price,
        mt.task_no as order_no,
        mt.order_data ->> '$.remark' as remark
        from as_equipment_maintain_task mt join as_equipment_replacement r
        where
        mt.company_id = r.company_id
        and r.maintain_task_id = mt.id
        and mt.company_id = #{companyId}
        and mt.task_status = 4
        union all
        SELECT
        l.id,
        l.create_time as ck_time,
        l.repository_id as ck_repo,
        r.name as ck_repo_text,
        '盘亏出库' as ck_type,
        '' as receive_org,
        '' as receive_org_text,
        '' as receive_user,
        '' as receive_user_text,
        d.material_id,
        d.material_snapshot_data ->> '$.materialName' as material_name,
        d.material_snapshot_data ->> '$.materialCode' as material_code,
        d.material_snapshot_data ->> '$.materialCategory' as material_category,
        d.material_snapshot_data ->> '$.materialCategoryText' as material_category_text,
        d.material_snapshot_data ->> '$.barCode' as bar_code,
        d.material_snapshot_data ->> '$.model' as model,
        d.material_snapshot_data ->> '$.brand' as brand,
        d.material_snapshot_data ->> '$.unit' as unit,
        ABS(l.change_num) as ck_num,
        ABS(l.change_money) as ck_price,
        l.order_no,
        '' as remark
        FROM
        as_material_stock_log l
        join as_material_stock st on l.stock_id = st.id
        JOIN as_material_inventory_detail d ON l.company_id = d.company_id
        AND l.order_id = d.inventory_id
        AND l.repository_id = d.repository_id
        AND st.material_id = d.material_id
        left join as_repository r on l.repository_id= r.id
        WHERE
        l.action_type = 82 and l.company_id = #{companyId}
        ) t
        <where>
            <if test="ew.includeIds != null and ew.includeIds.size > 0">
                and id in
                <foreach collection="ew.includeIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="ew.ckTime != null and ew.ckTime.size == 2">
                <if test="ew.ckTime[0] != null and ew.ckTime[0] != '' and ew.ckTime[0] != 'null'">
                    and ck_time &gt;= CONCAT(#{ew.ckTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.ckTime[1]!=null and ew.ckTime[1] != '' and ew.ckTime[1] != 'null'">
                    and ck_time &lt;= CONCAT(#{ew.ckTime[1]}, ' 23:59:59')
                </if>
            </if>

            <if test="ew.ckRepo != null and ew.ckRepo.size > 0">
                and CAST(ck_repo as DECIMAL(19)) in
                <foreach collection="ew.ckRepo" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeSql != null and storeSql!=''">
                and CAST(ck_repo as DECIMAL(19)) in ${storeSql}
            </if>
            <if test="ew.ckType != null and ew.ckType.size > 0">
                and ck_type in
                <foreach collection="ew.ckType" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="ew.receiveOrg != null and ew.receiveOrg.size > 0">
                and receive_org in
                <foreach collection="ew.receiveOrg" item="item" index="index" open="(" separator="," close=")">
                    CONCAT(#{item} , '')
                </foreach>
            </if>

            <if test="ew.receiveUser != null and ew.receiveUser.size > 0">
                and receive_user in
                <foreach collection="ew.receiveUser" item="item" index="index" open="(" separator="," close=")">
                    CONCAT(#{item} , '')
                </foreach>
            </if>

            <if test="ew.materialName!=null and ew.materialName!=''">
                and material_name like concat('%',#{ew.materialName},'%')
            </if>

            <if test="ew.materialCode!=null and ew.materialCode!=''">
                and material_code like concat('%',#{ew.materialCode},'%')
            </if>

            <if test="ew.materialCategory != null and ew.materialCategory.size > 0">
                and material_category in
                <foreach collection="ew.materialCategory" item="item" index="index" open="(" separator="," close=")">
                    CONCAT(#{item} , '')
                </foreach>
            </if>

            <if test="ew.barCode!=null and ew.barCode!=''">
                and bar_code like concat('%',#{ew.barCode},'%')
            </if>

            <if test="ew.model!=null and ew.model!=''">
                and model like concat('%',#{ew.model},'%')
            </if>

            <if test="ew.brand!=null and ew.brand!=''">
                and brand like concat('%',#{ew.brand},'%')
            </if>

            <if test="ew.unit!=null and ew.unit!=''">
                and unit like concat('%',#{ew.unit},'%')
            </if>

            <if test="ew.ckNum != null and ew.ckNum.size == 2">
                <if test="ew.ckNum[0] != null">
                    and ck_num &gt;= #{ew.ckNum[0]}
                </if>
                <if test="ew.ckNum[1]!=null">
                    and ck_num &lt;= #{ew.ckNum[1]}
                </if>
            </if>

            <if test="ew.ckUnitPrice != null and ew.ckUnitPrice.size == 2">
                <if test="ew.ckUnitPrice[0] != null">
                    and ck_unit_price &gt;= #{ew.ckUnitPrice[0]}
                </if>
                <if test="ew.ckUnitPrice[1]!=null">
                    and ck_unit_price &lt;= #{ew.ckUnitPrice[1]}
                </if>
            </if>

            <if test="ew.ckPrice != null and ew.ckPrice.size == 2">
                <if test="ew.ckPrice[0] != null">
                    and ck_price &gt;= #{ew.ckPrice[0]}
                </if>
                <if test="ew.ckPrice[1]!=null">
                    and ck_price &lt;= #{ew.ckPrice[1]}
                </if>
            </if>

            <if test="ew.orderNo!=null and ew.orderNo!=''">
                and order_no like concat('%',#{ew.orderNo},'%')
            </if>

            <if test="ew.remark!=null and ew.remark!=''">
                and remark like concat('%',#{ew.remark},'%')
            </if>
        </where>
    </sql>

    <select id="materialCkManifest" resultType="com.niimbot.report.MaterialCkManifestDto">
        select * from
        <include refid="materialCkManifestSql" />
        order by ck_time desc
    </select>

    <select id="materialCkManifestTotal" resultType="com.niimbot.report.MaterialManifestTotalDto">
        select sum(t.ck_num) as total_num,
        sum(t.ck_price) as total_money from
        <include refid="materialCkManifestSql" />
    </select>

    <select id="materialRkStatistics" resultType="com.niimbot.report.MaterialRkStatisticsDto">
        select rk_time,
        material_id,
        material_name,
        material_code,
        sum(rk_num) as rk_num,
        sum(rk_price) as rk_price
        from (
        select
        FROM_UNIXTIME( r.order_data ->> '$.storageTime' / 1000, '%Y-%m-%d') as rk_time,
        r.order_data ->> '$.inRepo' as rk_repo,
        r.order_data ->> '$.rkType' as rk_type,
        rd.material_id,
        rd.material_snapshot_data ->> '$.materialName' as material_name,
        rd.material_snapshot_data ->> '$.materialCode' as material_code,
        rd.rk_num,
        rd.rk_price,
        r.order_data ->> '$.remark' as remark
        from as_material_rk_order r
        join as_material_rk_order_detail rd on r.id = rd.order_id
        where r.company_id = #{companyId} and r.approve_status IN ( 0, 3 )
        union all
        SELECT
        FROM_UNIXTIME( tk.order_data ->> '$.tkDate' / 1000, '%Y-%m-%d') as rk_time,
        tk.in_repo as rk_repo,
        '退库单入库' as rk_type,
        tkd.material_id,
        tkd.material_snapshot_data ->> '$.materialName' as material_name,
        tkd.material_snapshot_data ->> '$.materialCode' as material_code,
        tkd.tk_num as rk_num,
        tkd.tk_price as rk_price,
        tk.order_data ->> '$.remark' as remark
        FROM
        as_material_tk_order tk
        JOIN as_material_tk_order_detail tkd ON tk.id = tkd.order_id
        JOIN as_material_ck_order ck ON tk.company_id = ck.company_id AND ck.order_no = tk.order_data ->> '$.ckOrderNo'
        JOIN as_material_ck_order_detail ckd ON ck.id = ckd.order_id AND tkd.material_id = ckd.material_id
        WHERE
        tk.company_id = #{companyId} and tk.approve_status IN ( 0, 3 )
        union all
        SELECT
        DATE_FORMAT(l.create_time,'%Y-%m-%d') as rk_time,
        l.repository_id as rk_repo,
        '盘盈入库' as rk_type,
        d.material_id,
        d.material_snapshot_data ->> '$.materialName' as material_name,
        d.material_snapshot_data ->> '$.materialCode' as material_code,
        l.change_num as rk_num,
        l.change_money as rk_price,
        '' as remark
        FROM
        as_material_stock_log l
        join as_material_stock st on l.stock_id = st.id
        JOIN as_material_inventory_detail d ON l.company_id = d.company_id
        AND l.order_id = d.inventory_id
        AND l.repository_id = d.repository_id
        AND st.material_id = d.material_id
        WHERE
        l.action_type = 81 and l.company_id = #{companyId}
        )	t
        <where>
            <if test="ew.rkTime != null and ew.rkTime.size == 2">
                <if test="ew.rkTime[0] != null and ew.rkTime[0] != '' and ew.rkTime[0] != 'null'">
                    and rk_time &gt;= #{ew.rkTime[0]}
                </if>
                <if test="ew.rkTime[1]!=null and ew.rkTime[1] != '' and ew.rkTime[1] != 'null'">
                    and rk_time &lt;= #{ew.rkTime[1]}
                </if>
            </if>
            <if test="ew.rkRepo != null and ew.rkRepo.size > 0">
                and CAST(rk_repo AS DECIMAL(19)) in
                <foreach collection="ew.rkRepo" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeSql != null and storeSql!=''">
                and CAST(rk_repo AS DECIMAL(19)) in ${storeSql}
            </if>
            <if test="ew.rkType != null and ew.rkType.size > 0">
                and rk_type in
                <foreach collection="ew.rkType" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ew.materialName!=null and ew.materialName!=''">
                and material_name like concat('%',#{ew.materialName},'%')
            </if>

            <if test="ew.materialCode!=null and ew.materialCode!=''">
                and material_code like concat('%',#{ew.materialCode},'%')
            </if>
            <if test="ew.rkNum != null and ew.rkNum.size == 2">
                <if test="ew.rkNum[0] != null">
                    and rk_num &gt;= #{ew.rkNum[0]}
                </if>
                <if test="ew.rkNum[1]!=null">
                    and rk_num &lt;= #{ew.rkNum[1]}
                </if>
            </if>

            <if test="ew.rkPrice != null and ew.rkPrice.size == 2">
                <if test="ew.rkPrice[0] != null">
                    and rk_price &gt;= #{ew.rkPrice[0]}
                </if>
                <if test="ew.rkPrice[1]!=null">
                    and rk_price &lt;= #{ew.rkPrice[1]}
                </if>
            </if>

            <if test="ew.remark!=null and ew.remark!=''">
                and remark like concat('%',#{ew.remark},'%')
            </if>
        </where>
        GROUP BY rk_time, material_id, material_code, material_name
        order by rk_time, material_code
    </select>

    <select id="materialCkStatistics" resultType="com.niimbot.report.MaterialCkStatisticsDto">
        select ck_time,
        material_id,
        material_code,
        material_name,
        sum(ck_num) as ck_num,
        sum(ck_price) as ck_price
        from (
        SELECT
        FROM_UNIXTIME( c.order_data ->> '$.outRepoTime' / 1000, '%Y-%m-%d') as ck_time,
        c.order_data ->> '$.outRepo' as ck_repo,
        c.order_data ->> '$.ckType' as ck_type,
        c.order_data ->> '$.outRepoTime' as receive_org,
        c.order_data ->> '$.receiveUser' as receive_user,
        cd.material_id,
        cd.material_snapshot_data ->> '$.materialName' as material_name,
        cd.material_snapshot_data ->> '$.materialCode' as material_code,
        cd.ck_num,
        cd.ck_price,
        c.order_data ->> '$.remark' as remark
        from as_material_ck_order c
        join as_material_ck_order_detail cd on c.id = cd.order_id
        where c.company_id = #{companyId} and c.approve_status IN ( 0, 1, 3 )
        union all
        select
        FROM_UNIXTIME( o.order_data ->> '$.bsDate' / 1000, '%Y-%m-%d') as ck_time,
        o.order_data ->> '$.outRepo' as ck_repo,
        '报损出库' as ck_type,
        '' as receive_org,
        '' as receive_user,
        od.material_id,
        od.material_snapshot_data ->> '$.materialName' as material_name,
        od.material_snapshot_data ->> '$.materialCode' as material_code,
        od.bs_num as ck_num,
        od.bs_price as ck_price,
        o.order_data ->> '$.remark' as remark
        from as_material_bs_order o
        join as_material_bs_order_detail od on o.id = od.order_id
        where o.order_data ->> '$.subtractStock' = '是' and o.company_id = #{companyId} and o.approve_status IN ( 0, 1, 3 )
        union all
        select
        FROM_UNIXTIME( mt.order_data ->> '$.maintainEndDate' / 1000, '%Y-%m-%d') as ck_time,
        mt.order_data ->> '$.outRepo' as ck_repo,
        '设备保养出库' as ck_type,
        '' as receive_org,
        '' as receive_user,
        r.material_id,
        r.material_snapshot_data ->> '$.materialName' as material_name,
        r.material_snapshot_data ->> '$.materialCode' as material_code,
        r.ck_num,
        r.ck_price,
        mt.order_data ->> '$.remark' as remark
        from as_equipment_maintain_task mt join as_equipment_replacement r
        where mt.company_id = r.company_id and r.maintain_task_id = mt.id
        and mt.company_id = #{companyId} and mt.task_status = 4
        union all
        SELECT
        DATE_FORMAT(l.create_time,'%Y-%m-%d') as ck_time,
        l.repository_id as ck_repo,
        '盘亏出库' as ck_type,
        '' as receive_org,
        '' as receive_user,
        d.material_id,
        d.material_snapshot_data ->> '$.materialName' as material_name,
        d.material_snapshot_data ->> '$.materialCode' as material_code,
        ABS(l.change_num) as ck_num,
        ABS(l.change_money) as ck_price,
        '' as remark
        FROM
        as_material_stock_log l
        join as_material_stock st on l.stock_id = st.id
        JOIN as_material_inventory_detail d ON l.company_id = d.company_id
        AND l.order_id = d.inventory_id
        AND l.repository_id = d.repository_id
        AND st.material_id = d.material_id
        left join as_repository r on l.repository_id= r.id
        WHERE
        l.action_type = 82 and l.company_id = #{companyId}
        ) t
        <where>
            <if test="ew.ckTime != null and ew.ckTime.size == 2">
                <if test="ew.ckTime[0] != null and ew.ckTime[0] != '' and ew.ckTime[0] != 'null'">
                    and ck_time &gt;= #{ew.ckTime[0]}
                </if>
                <if test="ew.ckTime[1]!=null and ew.ckTime[1] != '' and ew.ckTime[1] != 'null'">
                    and ck_time &lt;= #{ew.ckTime[1]}
                </if>
            </if>
            <if test="ew.ckRepo != null and ew.ckRepo.size > 0">
                and CAST(ck_repo as DECIMAL(19)) in
                <foreach collection="ew.ckRepo" item="item" index="index" open="(" separator="," close=")">
                    CONCAT(#{item} , '')
                </foreach>
            </if>
            <if test="storeSql != null and storeSql!=''">
                and CAST(ck_repo as DECIMAL(19)) in ${storeSql}
            </if>
            <if test="ew.ckType != null and ew.ckType.size > 0">
                and ck_type in
                <foreach collection="ew.ckType" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="ew.receiveOrg != null and ew.receiveOrg.size > 0">
                and receive_org in
                <foreach collection="ew.receiveOrg" item="item" index="index" open="(" separator="," close=")">
                    CONCAT(#{item} , '')
                </foreach>
            </if>

            <if test="ew.receiveUser != null and ew.receiveUser.size > 0">
                and receive_user in
                <foreach collection="ew.receiveUser" item="item" index="index" open="(" separator="," close=")">
                    CONCAT(#{item} , '')
                </foreach>
            </if>

            <if test="ew.materialName!=null and ew.materialName!=''">
                and material_name like concat('%',#{ew.materialName},'%')
            </if>

            <if test="ew.materialCode!=null and ew.materialCode!=''">
                and material_code like concat('%',#{ew.materialCode},'%')
            </if>
            <if test="ew.ckNum != null and ew.ckNum.size == 2">
                <if test="ew.ckNum[0] != null">
                    and ck_num &gt;= #{ew.ckNum[0]}
                </if>
                <if test="ew.ckNum[1]!=null">
                    and ck_num &lt;= #{ew.ckNum[1]}
                </if>
            </if>
            <if test="ew.ckPrice != null and ew.ckPrice.size == 2">
                <if test="ew.ckPrice[0] != null">
                    and ck_price &gt;= #{ew.ckPrice[0]}
                </if>
                <if test="ew.ckPrice[1]!=null">
                    and ck_price &lt;= #{ew.ckPrice[1]}
                </if>
            </if>
            <if test="ew.remark!=null and ew.remark!=''">
                and remark like concat('%',#{ew.remark},'%')
            </if>
        </where>
        GROUP BY ck_time, material_id, material_code, material_name
        order by ck_time, material_code
    </select>

</mapper>