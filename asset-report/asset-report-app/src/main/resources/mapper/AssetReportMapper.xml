<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.report.mapper.AssetReportMapper">

  <select id="assetCustomReport" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
      select ${param.fieldStr}
      from (
      select asset.*,
      <if test="param.norm != null and param.norm.size > 0">
          <foreach collection="param.norm" item="key" index="index">
              <choose>
                  <when test="key!=null and key==@com.niimbot.asset.report.constants.ReportAssetConstant@REPAIR_MONEY">
                      (select IFNULL(sum(rod.repair_money), 0) from as_repair_order ro join
                      as_repair_order_detail rod on ro.id = rod.repair_order_id where
                      asset.company_id = ro.company_id and asset.id = rod.id
                      AND ro.approve_status IN ( 0, 3 )
                      AND rod.repair_status = '维修完成'
                      ) as repair_money,
                  </when>
                  <when test="key!=null and key==@com.niimbot.asset.report.constants.ReportAssetConstant@REPAIR_NUM">
                      (select count(*) from as_repair_order ro join as_repair_order_detail rod on
                      ro.id = rod.repair_order_id where asset.company_id = ro.company_id and
                      asset.id = rod.id
                      AND ro.approve_status IN ( 0, 3 )
                      AND rod.repair_status = '维修完成'
                      ) as repair_num,
                  </when>
                  <when test="key!=null and key==@com.niimbot.asset.report.constants.ReportAssetConstant@REPAIR_REPORT_NUM">
                      (select count(*) from as_repair_report_order ro join
                      as_repair_report_order_detail rod on ro.id = rod.repair_report_order_id where
                      asset.company_id = ro.company_id and asset.id = rod.id
                      AND ro.approve_status IN ( 0, 3 )) as repair_report_num,
                  </when>
              </choose>
          </foreach>
      </if>
      field.expire_date,
      field.handle_date
      from as_asset as asset left join as_asset_report_field as field on asset.id = field.asset_id
      where asset.company_id = #{param.companyId} and asset.is_delete = 0
      ) as a
      <where>
          ${param.conditionStr}
          <if test="param.kw!=null and param.kw!=''">
              and (
              (a.asset_code like concat('%',#{param.kw},'%'))
              or
              (a.asset_name like concat('%',#{param.kw},'%'))
        )
      </if>
    <if test="param.detailId != null and param.detailId.size > 0">
        and a.id in
        <foreach collection="param.detailId" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    </where>
    <if test="param.groupStr != null and param.groupStr != ''">
      group by ${param.groupStr}
    </if>
    <if test="param.orderStr != null and param.orderStr != ''">
      order by ${param.orderStr}
    </if>
    <if test="param.limitStr != null and param.limitStr != ''">
      limit ${param.limitStr}
    </if>
  </select>

  <select id="selectAllStatus" resultType="com.niimbot.means.AssetStatusDto">
    select id, name
    from as_asset_status
    where is_delete = 0
  </select>

  <select id="stockCustomReport" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select ${param.fieldStr}
    from (
    select a.*, a.id as detail_id, IFNULL(c.current_quantity, 0) AS current_quantity,
           IFNULL(c.total_money, 0) as total_money, IFNULL(c.avg_price, 0) as avg_price
    from as_material as a left join
        (select material_id,
                sum(IFNULL(total_money, 0)) as total_money,
                sum(IFNULL(current_quantity, 0)) as current_quantity,
                sum(IFNULL(total_money, 0)) / if(sum(IFNULL(current_quantity, 0)) = 0, 1, sum(IFNULL(current_quantity, 0))) as avg_price
         from as_material_stock
         <where>
             company_id = #{param.companyId}
             <if test="param.repositoryCondition != null and param.repositoryCondition != ''">
                ${param.repositoryCondition}
             </if>
         </where>
         group by material_id) as c on a.id = c.material_id
    where a.company_id = #{param.companyId} and a.is_delete = 0
    ) as m
    <where>
      <if test="param.conditionStr != null and param.conditionStr != ''
      and param.conditionStr != 'null' and param.conditionStr != ' null'">
        ${param.conditionStr}
      </if>
      <if test="param.permSql != null and param.permSql != ''">
          and ${param.permSql}
      </if>
      <if test="param.kw!=null and param.kw!=''">
        and (
        m.material_data ->> '$.materialCode' like concat('%',#{param.kw},'%')
        or m.material_data ->> '$.materialName' like concat('%',#{param.kw},'%')
        )
      </if>
    <if test="param.detailId != null and param.detailId.size > 0">
        and m.detail_id in
        <foreach collection="param.detailId" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    </where>
    <if test="param.groupStr != null and param.groupStr != ''">
      group by ${param.groupStr}
    </if>
    <if test="param.orderStr != null and param.orderStr != ''">
      order by ${param.orderStr}
    </if>
    <if test="param.limitStr != null and param.limitStr != ''">
      limit ${param.limitStr}
    </if>
  </select>

  <select id="stockRepositoryCustomReport" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select ${param.fieldStr}
    from (
    select a.*, b.id as detail_id, b.repository_id, IFNULL(c.current_quantity, 0) AS current_quantity,
    IFNULL(c.total_money, 0) as total_money, IFNULL(c.avg_price, 0) as avg_price
    from as_material as a left join as_material_stock as b on a.id = b.material_id left join
    (select material_id, repository_id,
    sum(IFNULL(total_money, 0)) as total_money,
    sum(IFNULL(current_quantity, 0)) as current_quantity,
    sum(IFNULL(total_money, 0)) / if(sum(IFNULL(current_quantity, 0)) = 0, 1, sum(IFNULL(current_quantity, 0))) as avg_price
    from as_material_stock
    <where>
      company_id = #{param.companyId}
      <if test="param.repositoryCondition != null and param.repositoryCondition != ''">
        ${param.repositoryCondition}
      </if>
    </where>
     group by material_id, repository_id) as c on (b.material_id = c.material_id and b.repository_id = c.repository_id)
    where a.company_id = #{param.companyId} and a.is_delete = 0
    ) as m
    <where>
      <if test="param.conditionStr != null and param.conditionStr != ''
      and param.conditionStr != 'null' and param.conditionStr != ' null'">
        ${param.conditionStr}
      </if>
      <if test="param.permSql != null and param.permSql != ''">
          and ${param.permSql}
      </if>
      <if test="param.kw!=null and param.kw!=''">
        and (
        m.material_data ->> '$.materialCode' like concat('%',#{param.kw},'%')
        or m.material_data ->> '$.materialName' like concat('%',#{param.kw},'%')
        )
      </if>
      <if test="param.detailId != null and param.detailId.size > 0">
        and m.detail_id in
        <foreach collection="param.detailId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    <if test="param.groupStr != null and param.groupStr != ''">
      group by ${param.groupStr}
    </if>
    <if test="param.orderStr != null and param.orderStr != ''">
      order by ${param.orderStr}
    </if>
    <if test="param.limitStr != null and param.limitStr != ''">
      limit ${param.limitStr}
    </if>
  </select>

  <select id="selectMaterialOtherField" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select m.material_code as materialCode, ${param.fieldStr}
    from as_material as m
    where m.is_delete = 0 and m.company_id = #{param.companyId}
    <if test="param.materialCode != null and param.materialCode.size() > 0">
      and m.material_code in
      <foreach collection="param.materialCode" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="storageCustomReport" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select ${param.fieldStr}
    from (
    select a.*, b.id as detail_id, b.material_id, b.material_snapshot_data,
           IFNULL(b.rk_price, 0) as rk_price,
           IFNULL(b.rk_unit_price, 0) as rk_unit_price,
           IFNULL(b.rk_num, 0) as rk_num
    from as_material_rk_order as a left join as_material_rk_order_detail as b on a.id = b.order_id
    where a.company_id = #{param.companyId} and a.approve_status in (0,3)
    ) as r
    <where>
      <if test="param.conditionStr != null and param.conditionStr != ''
      and param.conditionStr != 'null' and param.conditionStr != ' null'">
        ${param.conditionStr}
      </if>
      <if test="param.permSql != null and param.permSql != ''">
          and ${param.permSql}
      </if>
      <if test="param.kw!=null and param.kw!=''">
        and (
        r.material_snapshot_data ->> '$.materialCode' like concat('%', #{param.kw}, '%')
        or r.material_snapshot_data ->> '$.materialName' like concat('%', #{param.kw}, '%')
        )
      </if>
      <if test="param.detailId != null and param.detailId.size > 0">
        and r.detail_id in
        <foreach collection="param.detailId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    <if test="param.groupStr != null and param.groupStr != ''">
      group by ${param.groupStr}
    </if>
    <if test="param.orderStr != null and param.orderStr != ''">
      order by ${param.orderStr}
    </if>
    <if test="param.limitStr != null and param.limitStr != ''">
      limit ${param.limitStr}
    </if>
  </select>

  <select id="selectMaterialOtherFieldStorage" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select r.material_code as materialCode, ${param.fieldStr}
    from as_material_rk_order_detail as r left join as_material_rk_order as o on r.order_id = o.id
    where o.company_id = #{param.companyId}
    <if test="param.materialCode != null and param.materialCode.size() > 0">
      and r.material_code in
      <foreach collection="param.materialCode" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="outCustomReport" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select ${param.fieldStr}
    from (
    select a.*, b.id as detail_id, b.material_id, b.material_snapshot_data,
           IFNULL(b.ck_price, 0) as ck_price,
           IFNULL(b.ck_unit_price, 0) as ck_unit_price,
           IFNULL(b.ck_num, 0) as ck_num
    from as_material_ck_order as a left join as_material_ck_order_detail as b on a.id = b.order_id
    where a.company_id = #{param.companyId} and a.approve_status in (0,3)
    ) as ck_order
    <where>
      <if test="param.conditionStr != null and param.conditionStr != ''
      and param.conditionStr != 'null' and param.conditionStr != ' null'">
        ${param.conditionStr}
      </if>
      <if test="param.permSql != null and param.permSql != ''">
          and ${param.permSql}
      </if>
      <if test="param.kw!=null and param.kw!=''">
        and (
        ck_order.material_snapshot_data ->> '$.materialCode' like concat('%', #{param.kw}, '%')
        or ck_order.material_snapshot_data ->> '$.materialName' like concat('%', #{param.kw}, '%')
        )
      </if>
      <if test="param.detailId != null and param.detailId.size > 0">
        and ck_order.detail_id in
        <foreach collection="param.detailId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    <if test="param.groupStr != null and param.groupStr != ''">
      group by ${param.groupStr}
    </if>
    <if test="param.orderStr != null and param.orderStr != ''">
      order by ${param.orderStr}
    </if>
    <if test="param.limitStr != null and param.limitStr != ''">
      limit ${param.limitStr}
    </if>
  </select>

  <select id="selectMaterialOtherFieldOut" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select ck_order.material_code as materialCode, ${param.fieldStr}
    from as_material_ck_order_detail as ck_order left join as_material_ck_order as o on ck_order.order_id = o.id
    where o.company_id = #{param.companyId}
    <if test="param.materialCode != null and param.materialCode.size() > 0">
      and ck_order.material_code in
      <foreach collection="param.materialCode" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="receiptCustomReport" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select ${param.fieldStr}
    from (
    select a.id, a.detail_id, a.material_id, a.material_snapshot_data,
    a.ly_num, a.grant_num, a.granting_num, (a.ly_num - a.grant_num - a.granting_num) as wait_grant_num,
    b.company_id, b.summary, b.approve_status, b.order_no, b.order_data, b.total_type, b.total_num, b.create_by,
    b.create_time, b.grant_status
    from (SELECT
    ly.id,
    lyd.id as detail_id,
    lyd.material_id,
    lyd.material_snapshot_data,
    lyd.ly_num,
    lyd.grant_num,
    sum(IFNULL(ckd.ck_num, 0)) AS granting_num
    FROM
    as_material_ly_order_detail lyd
    JOIN as_material_ly_order ly ON lyd.order_id = ly.id
    LEFT JOIN as_material_ck_order ck
    ON (ly.company_id = ck.company_id and ck.ly_order_no = ly.order_no AND ck.approve_status = 1)
    LEFT JOIN as_material_ck_order_detail ckd
    ON (ck.id = ckd.order_id AND lyd.material_id = ckd.material_id)
    WHERE
    ly.company_Id = #{param.companyId} and ly.approve_status in (0,3)
    GROUP BY lyd.id,
    lyd.material_id,
    ly.id,
    lyd.material_snapshot_data,
    lyd.ly_num,
    lyd.grant_num
    ) as a left join (SELECT
    l.id,
    l.company_id,
    l.summary,
    l.approve_status,
    l.order_no,
    l.order_data,
    l.total_type,
    l.total_num,
    l.create_by,
    l.create_time,
    case
    when l.approve_status in (1, 2, 4, 5) then 0
    when l.approve_status in (0, 3) and count( ck.id ) = 0 then 1
    when l.approve_status in (0, 3) and count( ck.id ) > 0 and sum(IFNULL( d.grant_num, 0 )) = 0
    then 2
    when l.approve_status in (0, 3) and count( ck.id ) > 0 and sum(IFNULL( d.grant_num, 0 )) =
    sum(IFNULL( d.ly_num, 0 )) then 4
    else 3
    end as grant_status
    FROM
    as_material_ly_order l
    left join as_material_ly_order_detail d on l.id = d.order_id
    left join as_material_ck_order ck on (l.company_id = ck.company_id and ck.ly_order_no = l.order_no)
    where
    l.company_id = #{param.companyId} and l.approve_status in (0,3)
    GROUP BY
    l.id,
    l.company_id,
    l.summary,
    l.approve_status,
    l.order_no,
    l.order_data,
    l.total_type,
    l.total_num,
    l.create_by,
    l.create_time) as b on a.id = b.id) as l
    <where>
      <if test="param.conditionStr != null and param.conditionStr != ''
      and param.conditionStr != 'null' and param.conditionStr != ' null'">
        ${param.conditionStr}
      </if>
      <if test="param.permSql != null and param.permSql != ''">
          and ${param.permSql}
      </if>
      <if test="param.kw !=null and param.kw != ''">
        and (
        l.material_snapshot_data ->> '$.materialCode' like concat('%', #{param.kw}, '%')
        or l.material_snapshot_data ->> '$.materialName' like concat('%', #{param.kw}, '%')
        )
      </if>
      <if test="param.detailId != null and param.detailId.size > 0">
        and l.detail_id in
        <foreach collection="param.detailId" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    <if test="param.groupStr != null and param.groupStr != ''">
      group by ${param.groupStr}
    </if>
    <if test="param.orderStr != null and param.orderStr != ''">
      order by ${param.orderStr}
    </if>
    <if test="param.limitStr != null and param.limitStr != ''">
      limit ${param.limitStr}
    </if>
  </select>

  <select id="selectMaterialOtherFieldReceipt" parameterType="com.alibaba.fastjson.JSONObject"
          resultType="com.alibaba.fastjson.JSONObject">
    select l.material_code as materialCode, ${param.fieldStr}
    from as_material_ly_order_detail as l left join as_material_ly_order as o on l.order_id = o.id
    where o.company_id = #{param.companyId}
    <if test="param.materialCode != null and param.materialCode.size() > 0">
      and l.material_code in
      <foreach collection="param.materialCode" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

    <select id="countTkNumByLyOrder" resultType="java.util.Map">
        SELECT
        ck.ly_order_no AS lyOrderNo,
        tkd.material_id AS materialId,
        sum(IFNULL( tkd.tk_num, 0 )) AS tkNum
        FROM
        as_material_tk_order tk
        JOIN as_material_tk_order_detail tkd ON tk.id = tkd.order_id
        JOIN as_material_ck_order ck ON tk.company_id = ck.company_id AND ck.order_no = tk.order_data ->> '$.ckOrderNo'
        JOIN as_material_ck_order_detail ckd ON ck.id = ckd.order_id AND tkd.material_id = ckd.material_id
        WHERE
        tk.company_id = #{companyId}
        AND tk.approve_status IN ( 0, 3 )
        AND ck.ly_order_no in
        <foreach collection="lyOrderNos" item="orderNo" index="index" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
        GROUP BY
        ck.ly_order_no,
        tkd.material_id
    </select>

</mapper>