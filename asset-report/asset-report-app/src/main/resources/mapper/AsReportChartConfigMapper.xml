<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.report.mapper.AsReportChartConfigMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.report.model.AsReportChartConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="dimension_num" property="dimensionNum" typeHandler="com.niimbot.asset.report.handle.SupportNumTypeHandler" />
    <result column="norm_num"  property="normNum" typeHandler="com.niimbot.asset.report.handle.SupportNumTypeHandler" />
    <result column="default_norm_num" property="defaultNormNum" typeHandler="com.niimbot.asset.report.handle.SupportNumTypeHandler" />
    <result column="support_sort" jdbcType="BOOLEAN" property="supportSort" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, dimension_num, norm_num, default_norm_num, support_sort, company_id, 
    is_delete, create_by, create_time, update_by, update_time
  </sql>
</mapper>