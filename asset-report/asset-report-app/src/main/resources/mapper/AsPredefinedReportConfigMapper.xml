<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.report.mapper.AsPredefinedReportConfigMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.report.model.AsPredefinedReportConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_name" jdbcType="VARCHAR" property="reportName" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, report_name, biz_type, `type`, is_delete, create_time, update_time
  </sql>
</mapper>