<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.report.mapper.PredefinedReportMapper">

    <resultMap id="AssetLogReportDto" type="com.niimbot.report.AssetLogReportDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
    </resultMap>

    <resultMap id="WaitHandleAssetLogDto" type="com.niimbot.report.WaitHandleAssetLogDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
        <result column="due_time" property="dueTime"/>
    </resultMap>

    <resultMap id="HandleAssetLogDto" type="com.niimbot.report.HandleAssetLogDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
        <result column="dispose_date" property="disposeDate"/>
        <result column="dispose_type" property="disposeType"/>
        <result column="dispose_user" property="disposeUser"/>
        <result column="dispose_money" property="disposeMoney"/>
        <result column="order_no" property="orderNo"/>
    </resultMap>

    <resultMap id="WaitReturnAssetLogDto" type="com.niimbot.report.WaitReturnAssetLogDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
        <result column="overdue_status" property="overdueStatus"/>
        <result column="borrow_date" property="borrowDate"/>
        <result column="estimate_back_date" property="estimateBackDate"/>
        <result column="borrow_org" property="borrowOrg"/>
        <result column="borrow_user" property="borrowUser"/>
        <result column="order_no" property="orderNo"/>
    </resultMap>

    <resultMap id="RepairAssetLogDto" type="com.niimbot.report.RepairAssetLogDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
        <result column="repair_count" property="repairCount"/>
        <result column="repair_cost" property="repairCost"/>
        <result column="repair_report_count" property="repairReportCount"/>
        <result column="repair_time" property="repairTime"/>
    </resultMap>

    <resultMap id="RepairAssetRecordDto" type="com.niimbot.report.RepairAssetRecordDto">
        <id column="id" property="id"/>
        <result column="detail_id" property="detailId"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
        <result column="do_repair_user" property="doRepairUser"/>
        <result column="repair_date" property="repairDate"/>
        <result column="repair_start_date" property="repairStartDate"/>
        <result column="repair_end_date" property="repairEndDate"/>
        <result column="repair_time" property="repairTime"/>
        <result column="repair_cost" property="repairCost"/>
    </resultMap>

    <resultMap id="groupReportResultMap" type="com.niimbot.report.GroupReportItem">
        <result column="opening_quantity" property="openingQuantity"/>
        <result column="opening_amount" property="openingAmount"/>
        <result column="increased_quantity" property="increasedQuantity"/>
        <result column="increased_amount" property="increasedAmount"/>
        <result column="reduced_quantity" property="reducedQuantity"/>
        <result column="reduced_amount" property="reducedAmount"/>
        <result column="ending_quantity" property="endingQuantity"/>
        <result column="ending_amount" property="endingAmount"/>
    </resultMap>

    <resultMap id="meansUseOrgReportResultMap" type="com.niimbot.report.MeansUseOrgReportItem" extends="groupReportResultMap">
        <id column="use_org" property="useOrgId"/>
    </resultMap>

    <resultMap id="meansCategoryReportResultMap" type="com.niimbot.report.MeansCategoryReportItem" extends="groupReportResultMap">
        <id column="asset_category" property="categoryId"/>
    </resultMap>

    <sql id="meansGroupSql">
        COUNT(IF(IF(a.asset_data ->> '$.buyTime' IS NULL OR a.asset_data ->> '$.buyTime' = '' OR a.asset_data ->> '$.buyTime' = 'null', UNIX_TIMESTAMP(a.create_time) * 1000, a.asset_data ->> '$.buyTime') &lt; #{em.timestamps[0]}, a.id, NULL)) AS opening_quantity,
        SUM(IF(IF(a.asset_data ->> '$.buyTime' IS NULL OR a.asset_data ->> '$.buyTime' = '' OR a.asset_data ->> '$.buyTime' = 'null', UNIX_TIMESTAMP(a.create_time) * 1000, a.asset_data ->> '$.buyTime') &lt; #{em.timestamps[0]}, CONVERT(a.asset_data ->> '$.price', DECIMAL(18, 4)), 0)) AS opening_amount,
        COUNT(IF(IF(a.asset_data ->> '$.buyTime' IS NULL OR a.asset_data ->> '$.buyTime' = '' OR a.asset_data ->> '$.buyTime' = 'null', UNIX_TIMESTAMP(a.create_time) * 1000, a.asset_data ->> '$.buyTime') BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]} , a.id, NULL)) AS increased_quantity,
        SUM(IF(IF(a.asset_data ->> '$.buyTime' IS NULL OR a.asset_data ->> '$.buyTime' = '' OR a.asset_data ->> '$.buyTime' = 'null', UNIX_TIMESTAMP(a.create_time) * 1000, a.asset_data ->> '$.buyTime') BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]}, CONVERT(a.asset_data ->> '$.price', DECIMAL(18, 4)), 0)) AS increased_amount,
        COUNT(IF(a.`status` = 4 AND b.dispose_date BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]} , a.id, NULL)) AS reduced_quantity,
        SUM(IF(a.`status` = 4 AND b.dispose_date BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]}, CONVERT(a.asset_data ->> '$.price', DECIMAL(18, 4)), 0)) AS reduced_amount,
        COUNT(IF(IF(a.asset_data ->> '$.buyTime' IS NULL OR a.asset_data ->> '$.buyTime' = '' OR a.asset_data ->> '$.buyTime' = 'null', UNIX_TIMESTAMP(a.create_time) * 1000, a.asset_data ->> '$.buyTime') &gt; #{em.timestamps[1]}, a.id, NULL)) AS ending_quantity,
        SUM(IF(IF(a.asset_data ->> '$.buyTime' IS NULL OR a.asset_data ->> '$.buyTime' = '' OR a.asset_data ->> '$.buyTime' = 'null', UNIX_TIMESTAMP(a.create_time) * 1000, a.asset_data ->> '$.buyTime') &gt; #{em.timestamps[1]}, CONVERT(a.asset_data ->> '$.price', DECIMAL(18, 4)), 0)) AS ending_amount
        FROM
        as_asset AS a
        LEFT JOIN
        (
        SELECT
        t1.asset_id,
        t2.order_data ->> '$.disposeDate' AS dispose_date
        FROM
        as_order_detail t1 INNER JOIN as_order t2 ON t1.order_id = t2.id
        WHERE
        t2.company_id = #{em.companyId}
        AND t2.order_type = 8
        AND t2.approve_status = 3
        ) b ON a.id = b.asset_id
        WHERE
        a.company_id = #{em.companyId}
        AND a.is_delete = 0
        <if test="perms != null and perms != ''">
            ${perms}
        </if>
    </sql>

    <select id="selectMeansUseOrgGroupReport" parameterType="com.niimbot.report.GroupReportSearch" resultMap="meansUseOrgReportResultMap">
        SELECT
        a.use_org,
        <include refid="meansGroupSql"></include>
        <if test="em.useOrgIds != null and em.useOrgIds.size() > 0">
            AND a.use_org IN
            <foreach collection="em.useOrgIds" item="id" open="(" separator="," close=")">
                CONVERT(#{id},CHAR)
            </foreach>
        </if>
        AND CONVERT(a.use_org,DECIMAL(19)) IN ( SELECT id FROM as_org WHERE company_id = #{em.companyId} AND is_delete = 0 )
        GROUP BY a.use_org
    </select>
    
    <select id="selectMeansCategoryGroupReport" resultMap="meansCategoryReportResultMap">
        SELECT
            t10.asset_category,
            COUNT(IF(t10.time &lt; #{em.timestamps[0]}, t10.id, NULL)) AS opening_quantity,
            SUM(IF(t10.time &lt; #{em.timestamps[0]}, CONVERT(t10.price, DECIMAL(18, 4)), 0)) AS opening_amount,
            COUNT(IF(t10.time BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]} , t10.id, NULL)) AS increased_quantity,
            SUM(IF(t10.time BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]}, CONVERT(t10.price, DECIMAL(18, 4)), 0)) AS increased_amount,
            COUNT(IF(t10.`status` = 4 AND t10.dispose_date BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]}, t10.id, NULL)) AS reduced_quantity,
            SUM(IF(t10.`status` = 4 AND t10.dispose_date BETWEEN #{em.timestamps[0]} AND #{em.timestamps[1]}, CONVERT(t10.price, DECIMAL(18, 4)), 0)) AS reduced_amount,
            COUNT(IF(t10.time &lt;= #{em.timestamps[1]}, t10.id, NULL)) AS ending_quantity,
            SUM(IF(t10.time &lt;= #{em.timestamps[1]}, CONVERT(t10.price, DECIMAL(18, 4)), 0)) AS ending_amount
        FROM
        (
            SELECT
                t1.id,
                t1.`status`,
                t1.asset_category,
                IF
                (
                    t1.asset_data ->> '$.buyTime' IS NULL
                    OR t1.asset_data ->> '$.buyTime' = ''
                    OR t1.asset_data ->> '$.buyTime' = 'null',
                    UNIX_TIMESTAMP( t1.create_time ) * 1000,
                    t1.asset_data ->> '$.buyTime'
                ) AS time,
                IF (
                    t1.asset_data ->> '$.price' IS NULL
                    OR t1.asset_data ->> '$.price' = ''
                    OR t1.asset_data ->> '$.price' = 'null',
                    0,
                    t1.asset_data ->> '$.price'
                ) AS price,
                t5.dispose_date
            FROM
                as_asset t1
            LEFT JOIN as_category t2 ON CONVERT(t1.asset_category, DECIMAL(19)) = t2.id
            INNER JOIN (
                SELECT
                    t3.asset_id,
                    t4.order_data ->> '$.disposeDate' AS dispose_date
                FROM
                    as_order_detail t3
                INNER JOIN as_order t4 ON t3.order_id = t4.id
                WHERE
                    t4.company_id = #{em.companyId}
                    AND t4.order_type = 8
                    AND t4.approve_status IN ( 0, 3 )
            ) t5 ON t1.id = t5.asset_id
            WHERE
                t1.company_id = #{em.companyId}
                AND t1.is_delete = 0
                AND t2.company_id = #{em.companyId}
                AND t2.is_delete = 0
                <if test="em.categoryIds != null and em.categoryIds.size() > 0">
                    AND t1.asset_category IN
                    <foreach collection="em.categoryIds" item="id" open="(" separator="," close=")">
                        CAST(#{id} AS CHAR)
                    </foreach>
                </if>
                <if test="perms != null and perms != ''">
                    ${perms}
                </if>

        UNION ALL

            SELECT
                t1.id,
                t1.`status`,
                t1.asset_category,
                IF
                (
                    t1.asset_data ->> '$.buyTime' IS NULL
                    OR t1.asset_data ->> '$.buyTime' = ''
                    OR t1.asset_data ->> '$.buyTime' = 'null',
                    UNIX_TIMESTAMP( t1.create_time ) * 1000,
                    t1.asset_data ->> '$.buyTime'
                ) AS time,
                IF (
                    t1.asset_data ->> '$.price' IS NULL
                    OR t1.asset_data ->> '$.price' = ''
                    OR t1.asset_data ->> '$.price' = 'null',
                    0,
                    t1.asset_data ->> '$.price'
                ) AS price,
                0 AS dispose_date
            FROM
                as_asset t1
            LEFT JOIN as_category t2 ON CONVERT(t1.asset_category, DECIMAL(19)) = t2.id
            WHERE
                t1.company_id = #{em.companyId}
                AND t1.is_delete = 0
                AND t2.company_id = #{em.companyId}
                AND t2.is_delete = 0
                AND t1.id NOT IN (
                    SELECT
                    t3.asset_id
                    FROM
                    as_order_detail t3
                    INNER JOIN as_order t4 ON t3.order_id = t4.id
                    WHERE
                    t4.company_id = #{em.companyId}
                    AND t4.order_type = 8
                    AND t4.approve_status IN ( 0, 3 )
                )
                <if test="em.categoryIds != null and em.categoryIds.size() > 0">
                    AND t1.asset_category IN
                    <foreach collection="em.categoryIds" item="id" open="(" separator="," close=")">
                        CAST(#{id} AS CHAR)
                    </foreach>
                </if>
                <if test="perms != null and perms != ''">
                    ${perms}
                </if>
        ) AS t10 GROUP BY t10.asset_category
    </select>

    <resultMap id="materialCategoryReportResultMap" type="com.niimbot.report.MaterialCategoryReportItem" extends="groupReportResultMap">
        <id column="material_category" property="categoryId"/>
        <result column="opening_unit_price" property="openingUnitPrice"/>
        <result column="ending_unit_price" property="endingUnitPrice"/>
    </resultMap>

    <select id="selectMaterialCategoryGroupOpeningAndEndingReport" resultMap="materialCategoryReportResultMap">
        SELECT
            t2.material_category,
            SUM(t4.remainder) AS opening_quantity,
            SUM(t4.total_money) AS opening_amount,
            SUM(t5.remainder) AS ending_quantity,
            SUM(t5.total_money) AS ending_amount
        FROM
        as_material t2 RIGHT JOIN
        (
            SELECT
                t1.id AS stock_id,
                t1.repository_id,
                t1.material_id,
                ( SELECT MAX(id) FROM as_material_stock_log WHERE stock_id = t1.id AND company_id = #{em.companyId} AND handle_time &lt;= #{em.times[0]} ) AS start_log_id,
                ( SELECT MAX(id) FROM as_material_stock_log WHERE stock_id = t1.id AND company_id = #{em.companyId} AND handle_time &lt;= #{em.times[1]} ) AS end_log_id
            FROM as_material_stock t1 WHERE t1.company_id = #{em.companyId}
        ) t3 ON t2.id = t3.material_id
        LEFT JOIN as_material_stock_log t4 ON t3.start_log_id = t4.id
        LEFT JOIN as_material_stock_log t5 ON t3.end_log_id = t5.id
        LEFT JOIN as_material_category t6 ON CONVERT(t2.material_category,DECIMAL(19)) = t6.id
        WHERE t2.company_id = #{em.companyId} AND t2.is_delete = 0 AND t6.company_id = #{em.companyId} AND t6.is_delete = 0
          <if test="em.repositoryIds != null and em.repositoryIds.size() > 0">
              AND t3.repository_id IN
              <foreach collection="em.repositoryIds" item="id" open="(" separator="," close=")">
                  #{id}
              </foreach>
          </if>
            <!-- 权限条件 -->
            <if test="storeSql != null and storeSql != ''">
                and t3.repository_id in ${storeSql}
            </if>
          <if test="em.categoryIds != null and em.categoryIds.size() > 0">
              AND t2.material_category IN
              <foreach collection="em.categoryIds" item="id" open="(" separator="," close=")">
                  #{id}
              </foreach>
          </if>
        GROUP BY t2.material_category
    </select>

    <select id="selectMaterialCategoryGroupProcessReport" resultMap="materialCategoryReportResultMap">
        SELECT
        t2.material_category AS material_category,
        SUM(
        CASE
        WHEN t3.change_num &gt; 0 AND t3.action_type = 31 THEN t12.rk_num
        WHEN t3.change_num &gt; 0 AND t3.action_type = 35 THEN t14.db_num
        WHEN t3.change_num &gt; 0 AND t3.action_type = 37 THEN t16.tk_num
        WHEN t3.change_num &gt; 0 AND t3.action_type = 81 THEN ABS(t3.change_num)
        WHEN t3.action_type = 34 AND (t15.tz_stock_num &gt; t15.tz_pre_stock_num) THEN
        (t15.tz_stock_num-t15.tz_pre_stock_num)
        ELSE 0
        END
        ) AS increased_quantity,
        SUM(
        CASE
        WHEN t3.change_num &gt; 0 AND t3.action_type = 31 THEN t12.rk_price
        WHEN t3.change_num &gt; 0 AND t3.action_type = 35 THEN t14.db_price
        WHEN t3.change_num &gt; 0 AND t3.action_type = 37 THEN t16.tk_price
        WHEN t3.change_num &gt; 0 AND t3.action_type = 81 THEN ABS(t3.change_money)
        WHEN t3.action_type = 34 AND (t15.tz_stock_num &gt; t15.tz_pre_stock_num) THEN
        GREATEST(t15.tz_stock_price-t15.tz_pre_stock_price, 0)
        ELSE 0
        END
        ) AS increased_amount,
        SUM(
        CASE
        WHEN t3.change_num &lt; 0 AND t3.action_type = 32 THEN t13.ck_num
        WHEN t3.change_num &lt; 0 AND t3.action_type = 35 THEN t14.db_num
        WHEN t3.change_num &lt; 0 AND t3.action_type = 36 THEN t17.bs_num
        WHEN t3.change_num &lt; 0 AND t3.action_type = 72 THEN t18.ck_num
        WHEN t3.change_num &lt; 0 AND t3.action_type = 82 THEN ABS(t3.change_num)
        WHEN t3.action_type = 34 AND (t15.tz_stock_num &lt; t15.tz_pre_stock_num) THEN
        (t15.tz_pre_stock_num-t15.tz_stock_num)
        ELSE 0
        END
        ) AS reduced_quantity,
        SUM(
            CASE
            WHEN t3.change_num &lt; 0 AND t3.action_type = 32 THEN t13.ck_price
            WHEN t3.change_num &lt; 0 AND t3.action_type = 35 THEN t14.db_price
            WHEN t3.change_num &lt; 0 AND t3.action_type = 36 THEN t17.bs_price
            WHEN t3.change_num &lt; 0 AND t3.action_type = 72 THEN t18.ck_price
            WHEN t3.action_type = 34 AND (t15.tz_stock_num &lt; t15.tz_pre_stock_num) THEN ABS(LEAST(t15.tz_stock_price-t15.tz_pre_stock_price, 0))
        WHEN t3.change_num &lt; 0 AND t3.action_type = 82 THEN ABS(t3.change_money)
        ELSE 0
        END
        ) AS reduced_amount
        FROM
        as_material t2
        LEFT JOIN as_material_stock t1 ON t2.id = t1.material_id
        LEFT JOIN as_material_stock_log t3 ON t1.id = t3.stock_id
        LEFT JOIN as_material_category t4 ON CONVERT(t2.material_category,DECIMAL(19)) = t4.id
        LEFT JOIN as_material_rk_order_detail t12 ON t3.order_id = t12.order_id AND t2.id =
        t12.material_id
        LEFT JOIN as_material_ck_order_detail t13 ON t3.order_id = t13.order_id AND t2.id =
        t13.material_id
        LEFT JOIN as_material_db_order_detail t14 ON t3.order_id = t14.order_id AND t2.id =
        t14.material_id
        LEFT JOIN as_material_tz_order_detail t15 ON t3.order_id = t15.order_id AND t2.id =
        t15.material_id
        LEFT JOIN as_material_tk_order_detail t16 ON t3.order_id = t16.order_id AND t2.id =
        t16.material_id
        LEFT JOIN as_material_bs_order_detail t17 ON t3.order_id = t17.order_id AND t2.id =
        t17.material_id
        LEFT JOIN as_equipment_replacement t18 ON t3.order_id = t18.maintain_task_id AND t2.id =
        t18.material_id
        WHERE
        t2.company_id = #{em.companyId} AND t2.is_delete = 0
        AND t1.company_id = #{em.companyId} AND t3.company_id = #{em.companyId}
        AND t4.company_id = #{em.companyId} AND t4.is_delete = 0
        AND ( t3.handle_time BETWEEN #{em.times[0]} AND #{em.times[1]} )
        <if test="em.repositoryIds != null and em.repositoryIds.size() > 0">
            AND t1.repository_id IN
            <foreach collection="em.repositoryIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <!-- 权限条件 -->
        <if test="storeSql != null and storeSql != ''">
            and t1.repository_id in ${storeSql}
        </if>
        <if test="em.categoryIds != null and em.categoryIds.size() > 0">
            AND t2.material_category IN
            <foreach collection="em.categoryIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY t2.material_category
    </select>

    <resultMap id="materialRepositoryReportResultMap" type="com.niimbot.report.MaterialRepositoryReportItem" extends="groupReportResultMap">
        <result column="stock_id" property="id"/>
        <result column="repository_id" property="repositoryId"/>
        <result column="material_id" property="materialId"/>
        <result column="opening_unit_price" property="openingUnitPrice"/>
        <result column="ending_unit_price" property="endingUnitPrice"/>
        <result column="material_data" property="materialData" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="selectMaterialRepositoryGroupReport" resultMap="materialRepositoryReportResultMap">
        SELECT
        t3.repository_id,
        t3.material_id,
        t3.stock_id,
        t2.material_data,
        t4.remainder AS opening_quantity,
        t4.total_money AS opening_amount,
        ( CONVERT(t4.total_money/t4.remainder, DECIMAL(19, 4)) ) AS opening_unit_price,
        t5.remainder AS ending_quantity,
        t5.total_money AS ending_amount,
        ( CONVERT(t5.total_money/t5.remainder, DECIMAL(19, 4)) ) AS ending_unit_price,
        t9.increased_quantity, t9.increased_amount, t9.reduced_quantity, t9.reduced_amount
        FROM
        (
            SELECT
                t1.repository_id,
                t1.material_id,
                t1.id AS stock_id,
                ( SELECT MAX(id) FROM as_material_stock_log WHERE stock_id = t1.id AND company_id = #{em.companyId} AND handle_time &lt;= #{em.times[0]} ) AS start_log_id,
                ( SELECT MAX(id) FROM as_material_stock_log WHERE stock_id = t1.id AND company_id = #{em.companyId} AND handle_time &lt;= #{em.times[1]} ) AS end_log_id
            FROM
                as_material_stock t1
            WHERE
                t1.company_id = #{em.companyId}
            <if test="em.repositoryIds != null and em.repositoryIds.size() > 0">
                AND t1.repository_id IN
                <foreach collection="em.repositoryIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <!-- 权限条件 -->
            <if test="storeSql != null and storeSql != ''">
                and t1.repository_id in ${storeSql}
            </if>
            <if test="em.includeIds != null and em.includeIds.size > 0">
                and t1.id in
                <foreach collection="em.includeIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        ) AS t3
        LEFT JOIN
        (
        SELECT
        t6.stock_id,
        SUM(
        CASE
        WHEN t6.change_num &gt; 0 AND t6.action_type = 31 THEN t12.rk_num
        WHEN t6.change_num &gt; 0 AND t6.action_type = 35 THEN t14.db_num
        WHEN t6.change_num &gt; 0 AND t6.action_type = 37 THEN t16.tk_num
        WHEN t6.change_num &gt; 0 AND t6.action_type = 81 THEN ABS(t6.change_num)
        WHEN t6.action_type = 34 AND (t15.tz_stock_num &gt; t15.tz_pre_stock_num) THEN
        (t15.tz_stock_num-t15.tz_pre_stock_num)
        ELSE 0
        END
        ) AS increased_quantity,
        SUM(
        CASE
        WHEN t6.change_num &gt; 0 AND t6.action_type = 31 THEN t12.rk_price
        WHEN t6.change_num &gt; 0 AND t6.action_type = 35 THEN t14.db_price
        WHEN t6.change_num &gt; 0 AND t6.action_type = 37 THEN t16.tk_price
        WHEN t6.change_num &gt; 0 AND t6.action_type = 81 THEN ABS(t6.change_money)
        WHEN t6.action_type = 34 AND (t15.tz_stock_num &gt; t15.tz_pre_stock_num) THEN
        GREATEST(t15.tz_stock_price-t15.tz_pre_stock_price, 0)
        ELSE 0
        END
        ) AS increased_amount,
        SUM(
        CASE
        WHEN t6.change_num &lt; 0 AND t6.action_type = 32 THEN t13.ck_num
        WHEN t6.change_num &lt; 0 AND t6.action_type = 35 THEN t14.db_num
        WHEN t6.change_num &lt; 0 AND t6.action_type = 36 THEN t17.bs_num
        WHEN t6.change_num &lt; 0 AND t6.action_type = 72 THEN t18.ck_num
        WHEN t6.change_num &lt; 0 AND t6.action_type = 82 THEN ABS(t6.change_num)
        WHEN t6.action_type = 34 AND (t15.tz_stock_num &lt; t15.tz_pre_stock_num) THEN
        (t15.tz_pre_stock_num-t15.tz_stock_num)
        ELSE 0
            END
        ) AS reduced_quantity,
        SUM(
        CASE
        WHEN t6.change_num &lt; 0 AND t6.action_type = 32 THEN t13.ck_price
        WHEN t6.change_num &lt; 0 AND t6.action_type = 35 THEN t14.db_price
        WHEN t6.change_num &lt; 0 AND t6.action_type = 36 THEN t17.bs_price
        WHEN t6.change_num &lt; 0 AND t6.action_type = 72 THEN t18.ck_price
        WHEN t6.change_num &lt; 0 AND t6.action_type = 82 THEN ABS(t6.change_money)
        WHEN t6.action_type = 34 AND (t15.tz_stock_num &lt; t15.tz_pre_stock_num) THEN
        ABS(LEAST(t15.tz_stock_price-t15.tz_pre_stock_price, 0))
        ELSE 0
        END
        ) AS reduced_amount
        FROM as_material_stock_log t6
        LEFT JOIN as_material_stock t11 ON t6.stock_id = t11.id
        LEFT JOIN as_material_rk_order_detail t12 ON t6.order_id = t12.order_id AND t11.material_id
        = t12.material_id
        LEFT JOIN as_material_ck_order_detail t13 ON t6.order_id = t13.order_id AND t11.material_id
        = t13.material_id
        LEFT JOIN as_material_db_order_detail t14 ON t6.order_id = t14.order_id AND t11.material_id
        = t14.material_id
        LEFT JOIN as_material_tz_order_detail t15 ON t6.order_id = t15.order_id AND t11.material_id
        = t15.material_id
        LEFT JOIN as_material_tk_order_detail t16 ON t6.order_id = t16.order_id AND t11.material_id
        = t16.material_id
        LEFT JOIN as_material_bs_order_detail t17 ON t6.order_id = t17.order_id AND t11.material_id
        = t17.material_id
        LEFT JOIN as_equipment_replacement t18 ON t6.order_id = t18.maintain_task_id AND t11.material_id
        = t18.material_id
        WHERE t6.company_id = #{em.companyId} AND ( t6.handle_time BETWEEN #{em.times[0]} AND
        #{em.times[1]} ) AND t11.company_id = #{em.companyId}
            <if test="em.repositoryIds != null and em.repositoryIds.size() > 0">
                AND t6.repository_id IN
                <foreach collection="em.repositoryIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <!-- 权限条件 -->
            <if test="storeSql != null and storeSql != ''">
                and t6.repository_id in ${storeSql}
            </if>
            GROUP BY t6.stock_id
        ) t9 ON t3.stock_id = t9.stock_id
        LEFT JOIN as_material t2 ON t3.material_id = t2.id
        LEFT JOIN as_material_stock_log t4 ON t3.start_log_id = t4.id
        LEFT JOIN as_material_stock_log t5 ON t3.end_log_id = t5.id
        LEFT JOIN as_repository t10 ON t3.repository_id = t10.id
        WHERE t2.company_id = #{em.companyId} AND t2.is_delete = 0
        AND t10.company_id = #{em.companyId} AND t10.is_delete = 0
        AND (t4.remainder != 0 OR t9.increased_quantity != 0 OR t9.reduced_quantity != 0 OR t5.remainder != 0)
        <if test="em.kw != null and em.kw != ''">
            AND ( t2.material_data ->> '$.materialName' LIKE CONCAT('%', #{em.kw}, '%') OR t2.material_data ->> '$.materialCode' LIKE CONCAT('%', #{em.kw}, '%') )
        </if>
        <if test="condition != null and condition != ''">
            ${condition}
        </if>
        order by t3.stock_id desc
    </select>

    <select id="assetLogReport" resultMap="AssetLogReportDto">
        select
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time
        from (
        SELECT
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time,
        log.handle_time
        FROM
        as_asset a JOIN as_asset_log log ON a.id = log.asset_id
        WHERE
        a.is_delete = 0 and a.company_id = #{companyId}
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <!-- 关键字（编码/名称）-->
        <if test="ew.kw!=null and ew.kw!=''">
            and (
            (a.asset_code like concat('%',#{ew.kw},'%'))
            or
            (a.asset_name like concat('%',#{ew.kw},'%'))
            )
        </if>

        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and a.id in
            <foreach collection="ew.includeIds" index="index" item="assetId" open="(" separator="," close=")">
                #{assetId}
            </foreach>
        </if>

        <if test="ew.handleTime!=null and ew.handleTime.size==2">
            <!-- 处理开始时间 -->
            <if test="ew.handleTime[0]!=null and ew.handleTime[0]!='' and ew.handleTime[0]!='null'">
                and log.create_time &gt;= CONCAT(#{ew.handleTime[0]}, ' 00:00:00')
            </if>
            <!-- 处理结束时间 -->
            <if test="ew.handleTime[1]!=null and ew.handleTime[1]!='' and ew.handleTime[1]!='null'">
                and log.create_time &lt;= CONCAT(#{ew.handleTime[1]}, ' 23:59:59')
            </if>
        </if>

        <!-- 处理类型 -->
        <if test="ew.handleType != null and ew.handleType.size() > 0">
            and log.action_type in
            <foreach collection="ew.handleType" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        ) a
        group by a.id, a.standard_id, a.asset_data, a.status
        , a.create_by, a.create_time, a.update_by, a.update_time, a.last_print_time
    </select>

    <select id="waitHandleAssetLog" resultMap="WaitHandleAssetLogDto">
        SELECT * from (
        SELECT
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time,
        DATE_ADD(
        IF
        (
        (a.`asset_data` ->> '$.buyTime' != '') and
        (a.`asset_data` ->> '$.buyTime' IS NOT NULL) and
        (a.`asset_data` ->> '$.buyTime' != 'null'),
        FROM_UNIXTIME( a.`asset_data` ->> '$.buyTime' / 1000, '%Y-%m-%d %H:%i:%s' ),
        a.`create_time`
        ),
        INTERVAL (a.`asset_data` ->> '$.useTimeLimit') MONTH
        ) AS due_time
        FROM
        as_asset a
        WHERE
        a.is_delete = 0
        AND a.company_id = #{companyId}
        AND a.status != 4
        AND a.asset_data ->> '$.useTimeLimit' != ''
        AND a.asset_data ->> '$.useTimeLimit' IS NOT NULL
        AND a.asset_data ->> '$.useTimeLimit' != 'null'

        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and a.id in
            <foreach collection="ew.includeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) a
        <where>
            <if test="ew.dueTime!=null and ew.dueTime.size==2">
                <!-- 处理开始时间 -->
                <if test="ew.dueTime[0]!=null and ew.dueTime[0]!='' and ew.dueTime[0]!='null'">
                    and a.due_time &gt;= CONCAT(#{ew.dueTime[0]}, ' 00:00:00')
                </if>
                <!-- 处理结束时间 -->
                <if test="ew.dueTime[1]!=null and ew.dueTime[1]!='' and ew.dueTime[1]!='null'">
                    and a.due_time &lt;= CONCAT(#{ew.dueTime[1]}, ' 23:59:59')
                </if>
            </if>
        </where>
    </select>

    <select id="handleAssetLog" resultMap="HandleAssetLogDto">
        select * from (
        SELECT
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time,
        IF(o.order_data ->> '$.disposeDate' = 'null', null, o.order_data ->> '$.disposeDate') as dispose_date,
        IF(o.order_data ->> '$.disposeType' = 'null', null, o.order_data ->> '$.disposeType') as dispose_type,
        IF(o.order_data ->> '$.disposeUserText' = 'null', null, o.order_data ->> '$.disposeUserText') as dispose_user,
        IF(o.order_data ->> '$.disposeMoney' = 'null', null, o.order_data ->> '$.disposeMoney') as dispose_money,
        o.order_no
        FROM
        as_asset a
        JOIN as_order_detail d ON a.id = d.asset_id
        JOIN as_order o ON o.id = d.order_id and o.company_id = a.company_id and o.order_type = '8'
        WHERE
        a.company_id = #{companyId}
        and a.is_delete = 0
        and a.status = 4
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and a.id in
            <foreach collection="ew.includeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 处置日期 -->
        <if test="ew.disposeDate!=null and ew.disposeDate.size==2">
            <!-- 处置日期开始时间 -->
            <if test="ew.disposeDate[0]!=null and ew.disposeDate[0]!='' and ew.disposeDate[0]!='null'">
                and o.order_data ->> '$.disposeDate' != ''
                and FROM_UNIXTIME( o.order_data ->> '$.disposeDate' / 1000, '%Y-%m-%d %H:%i:%s' )
                &gt;=
                CONCAT(#{ew.disposeDate[0]}, ' 00:00:00')
            </if>
            <!-- 处置日期结束时间 -->
            <if test="ew.disposeDate[1]!=null and ew.disposeDate[1]!='' and ew.disposeDate[1]!='null'">
                and FROM_UNIXTIME( o.order_data ->> '$.disposeDate' / 1000, '%Y-%m-%d %H:%i:%s' )
                &lt;=
                CONCAT(#{ew.disposeDate[1]}, ' 23:59:59')
            </if>
        </if>
        <!-- 处置操作人 -->
        <if test="ew.disposeUser!=null and ew.disposeUser.size>0">
            and o.order_data ->> '$.disposeUser' in
            <foreach collection="ew.disposeUser" item="disposeUserId" open="(" separator=","
                     close=")">
                #{disposeUserId}
            </foreach>
        </if>
        <!-- 处置类型 -->
        <if test="ew.disposeType!=null and ew.disposeType.size>0">
            and o.order_data ->> '$.disposeType' in
            <foreach collection="ew.disposeType" item="disposeType" open="(" separator=","
                     close=")">
                #{disposeType}
            </foreach>
        </if>
        <!-- 处置金额 -->
        <if test="ew.disposeMoney!=null and ew.disposeMoney.size==2">
            <!-- 处置金额 -->
            <if test="ew.disposeMoney[0]!=null and ew.disposeMoney[0]!=''">
                and o.order_data ->> '$.disposeMoney' &gt;= #{ew.disposeMoney[0]}
            </if>
            <!-- 处置金额 -->
            <if test="ew.disposeMoney[1]!=null and ew.disposeMoney[1]!=''">
                and o.order_data ->> '$.disposeMoney' &lt;= #{ew.disposeMoney[1]}
            </if>
        </if>
        <!-- 处置单据 -->
        <if test="ew.orderNo != null and ew.orderNo != ''">
            and o.order_no like concat('%',#{ew.orderNo},'%')
        </if>
        ) a
    </select>

    <select id="waitReturnAssetLog" resultMap="WaitReturnAssetLogDto">
        select * from (
        select
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time,
        IF(o.order_data ->> '$.borrowDate' = 'null', null, o.order_data ->> '$.borrowDate') as borrow_date,
        IF(o.order_data ->> '$.estimateBackDate' = 'null', null, o.order_data ->> '$.estimateBackDate') as estimate_back_date,
        IF(o.order_data ->> '$.borrowOrgText' = 'null', null, o.order_data ->> '$.borrowOrgText') as borrow_org,
        IF(o.order_data ->> '$.borrowUserText' = 'null', null, o.order_data ->> '$.borrowUserText') as borrow_user,
        CASE
        WHEN (o.order_data ->> '$.estimateBackDate' = '') or (o.order_data ->> '$.estimateBackDate'
        = 'null') or (o.order_data ->> '$.estimateBackDate' is null)
        THEN '未逾期'
        WHEN o.order_data ->> '$.estimateBackDate' &lt; (UNIX_TIMESTAMP(NOW()) * 1000)
        THEN '逾期'
        ELSE
        '未逾期'
        END as overdue_status,
        o.order_no from (
        SELECT
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time,
        max( o.id ) AS order_id
        FROM
        as_asset a
        JOIN as_order_detail d ON a.id = d.asset_id
        JOIN as_order o ON o.id = d.order_id and o.company_id = a.company_id AND o.order_type = '3' and o.approve_status in (0, 3)
        WHERE
        a.company_id = #{companyId}
        AND a.is_delete = 0
        AND a.STATUS = 3
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and a.id in
            <foreach collection="ew.includeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time ) a join as_order o on a.order_id = o.id
        <!-- 借用组织 -->
        <if test="ew.borrowOrg!=null and ew.borrowOrg.size>0">
            and o.order_data ->> '$.borrowOrg' in
            <foreach collection="ew.borrowOrg" item="borrowOrgId" open="(" separator="," close=")">
                #{borrowOrgId}
            </foreach>
        </if>
        <!-- 借用日期 -->
        <if test="ew.borrowDate!=null and ew.borrowDate.size==2">
            <!-- 借用日期开始时间 -->
            <if test="ew.borrowDate[0]!=null and ew.borrowDate[0]!='' and ew.borrowDate[0]!='null'">
                and o.order_data ->> '$.borrowDate' != ''
                and FROM_UNIXTIME( o.order_data ->> '$.borrowDate' / 1000, '%Y-%m-%d %H:%i:%s' )
                &gt;=
                CONCAT(#{ew.borrowDate[0]}, ' 00:00:00')
            </if>
            <!-- 借用日期结束时间 -->
            <if test="ew.borrowDate[1]!=null and ew.borrowDate[1]!='' and ew.borrowDate[1]!='null'">
                and FROM_UNIXTIME( o.order_data ->> '$.borrowDate' / 1000, '%Y-%m-%d %H:%i:%s' )
                &lt;=
                CONCAT(#{ew.borrowDate[1]}, ' 23:59:59')
            </if>
        </if>
        <!-- 预计归还日期 -->
        <if test="ew.estimateBackDate!=null and ew.estimateBackDate.size==2">
            <!-- 预计归还日期开始时间 -->
            <if test="ew.estimateBackDate[0]!=null and ew.estimateBackDate[0]!='' and ew.estimateBackDate[0]!='null'">
                and o.order_data ->> '$.estimateBackDate' != ''
                and FROM_UNIXTIME( o.order_data ->> '$.estimateBackDate' / 1000, '%Y-%m-%d %H:%i:%s'
                ) &gt;=
                CONCAT(#{ew.estimateBackDate[0]}, ' 00:00:00')
            </if>
            <!-- 预计归还日期结束时间 -->
            <if test="ew.estimateBackDate[1]!=null and ew.estimateBackDate[1]!='' and ew.estimateBackDate[1]!='null'">
                and FROM_UNIXTIME( o.order_data ->> '$.estimateBackDate' / 1000, '%Y-%m-%d %H:%i:%s'
                ) &lt;=
                CONCAT(#{ew.estimateBackDate[1]}, ' 23:59:59')
            </if>
        </if>
        <!-- 借用人 -->
        <if test="ew.borrowUser!=null and ew.borrowUser.size>0">
            and o.order_data ->> '$.borrowUser' in
            <foreach collection="ew.borrowUser" item="borrowUserId" open="(" separator=","
                     close=")">
                #{borrowUserId}
            </foreach>
        </if>
        <!-- 处置单据 -->
        <if test="ew.orderNo != null and ew.orderNo != ''">
            and o.order_no like concat('%',#{ew.orderNo},'%')
        </if>) a
        <where>
            <if test="ew.overdueStatus!=null and ew.overdueStatus!=''">
                and a.overdue_status = #{ew.overdueStatus}
            </if>
        </where>
    </select>

    <select id="repairAssetLog" resultMap="RepairAssetLogDto">
        select * from (
        SELECT
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time,
        (select count(*) from as_repair_report_order_detail rrd join as_repair_report_order rr
        on rr.id = rrd.repair_report_order_id
        where rr.approve_status in (0, 3) and rr.company_id = a.company_id and a.id = rrd.id) as
        repair_report_count,
        count(o.order_no) as repair_count,
        sum(d.repair_money) as repair_cost,
        sum(IF(d.finish_time is not null and o.repair_finish_date is not null,
        GREATEST(TIMESTAMPDIFF(HOUR , FROM_UNIXTIME(o.repair_finish_date / 1000 , '%Y-%m-%d %H:%i:%s'),
        d.finish_time), 0)
        , 0)) as repair_time
        FROM
        as_asset a
        JOIN as_repair_order_detail d ON a.id = d.id and d.repair_status = '维修完成'
        JOIN as_repair_order o ON o.id = d.repair_order_id AND o.company_id = a.company_id and
        o.approve_status in (0, 3)
        WHERE
        a.company_id = #{companyId}
        and a.is_delete = 0
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and a.id in
            <foreach collection="ew.includeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 维修日期 -->
        <if test="ew.repairFinishDate!=null and ew.repairFinishDate.size==2">
            <!-- 维修日期开始时间 -->
            <if test="ew.repairFinishDate[0]!=null and ew.repairFinishDate[0]!='' and ew.repairFinishDate[0]!='null'">
                and o.order_data ->> '$.repairFinishDate' != ''
                and FROM_UNIXTIME( o.order_data ->> '$.repairFinishDate' / 1000, '%Y-%m-%d %H:%i:%s'
                ) &gt;=
                CONCAT(#{ew.repairFinishDate[0]}, ' 00:00:00')
            </if>
            <!-- 维修日期结束时间 -->
            <if test="ew.repairFinishDate[1]!=null and ew.repairFinishDate[1]!='' and ew.repairFinishDate[1]!='null'">
                and FROM_UNIXTIME( o.order_data ->> '$.repairFinishDate' / 1000, '%Y-%m-%d %H:%i:%s'
                ) &lt;=
                CONCAT(#{ew.repairFinishDate[1]}, ' 23:59:59')
            </if>
        </if>
        GROUP BY
        a.id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time) a
        <where>
            <!-- 维修次数 -->
            <if test="ew.repairCount!=null and ew.repairCount.size==2">
                <!-- 维修次数 -->
                <if test="ew.repairCount[0]!=null and ew.repairCount[0]!=''">
                    and a.repair_count &gt;= #{ew.repairCount[0]}
                </if>
                <!-- 维修次数 -->
                <if test="ew.repairCount[1]!=null and ew.repairCount[1]!=''">
                    and a.repair_count &lt;= #{ew.repairCount[1]}
                </if>
            </if>
            <!-- 维修花费 -->
            <if test="ew.repairCost!=null and ew.repairCost.size==2">
                <!-- 维修花费 -->
                <if test="ew.repairCost[0]!=null and ew.repairCost[0]!=''">
                    and a.repair_cost &gt;= #{ew.repairCost[0]}
                </if>
                <!-- 维修花费 -->
                <if test="ew.repairCost[1]!=null and ew.repairCost[1]!=''">
                    and a.repair_cost &lt;= #{ew.repairCost[1]}
                </if>
            </if>
        </where>
    </select>

    <select id="repairAssetRecord" resultMap="RepairAssetRecordDto">
        SELECT
        a.id,
        concat(a.id, '_', o.id) as detail_id,
        a.company_id,
        a.standard_id,
        a.asset_data,
        a.STATUS,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.last_print_time,
        IF(o.order_data ->> '$.doRepairUser' = 'null', null, o.order_data ->> '$.doRepairUser') as do_repair_user,
        FROM_UNIXTIME( o.repair_finish_date / 1000, '%Y-%m-%d %H:%i:%s') as repair_start_date,
        d.finish_time as repair_end_date,
        d.repair_money AS repair_cost,
        IF(d.finish_time IS NOT NULL AND o.repair_finish_date IS NOT NULL,
        GREATEST(TIMESTAMPDIFF( HOUR, FROM_UNIXTIME( o.repair_finish_date / 1000, '%Y-%m-%d %H:%i:%s' ),
        d.finish_time ), 0),0) AS repair_time
        FROM
        as_asset a
        JOIN as_repair_order_detail d ON a.id = d.id
        AND d.repair_status = '维修完成'
        JOIN as_repair_order o ON o.id = d.repair_order_id
        AND o.company_id = a.company_id
        AND o.approve_status IN ( 0, 3 )
        WHERE
        a.company_id = #{companyId}
        AND a.is_delete = 0
        <!-- 关键字（编码/名称）-->
        <if test="ew.kw!=null and ew.kw!=''">
            and (
            (a.asset_code like concat('%',#{ew.kw},'%'))
            or
            (a.asset_name like concat('%',#{ew.kw},'%'))
            )
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and concat(a.id, '_', o.id) in
            <foreach collection="ew.includeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <!-- 维修花费 -->
        <if test="ew.repairCost!=null and ew.repairCost.size==2">
            <!-- 维修花费 -->
            <if test="ew.repairCost[0]!=null and ew.repairCost[0]!=''">
                and d.repair_money &gt;= #{ew.repairCost[0]}
            </if>
            <!-- 维修花费 -->
            <if test="ew.repairCost[1]!=null and ew.repairCost[1]!=''">
                and d.repair_money &lt;= #{ew.repairCost[1]}
            </if>
        </if>
        <!-- 维修时长 -->
        <if test="ew.repairTime!=null and ew.repairTime.size==2">
            <!-- 维修花费 -->
            <if test="ew.repairTime[0]!=null and ew.repairTime[0]!=''">
                and (IF(d.finish_time IS NOT NULL AND o.repair_finish_date IS NOT NULL,
                (TIMESTAMPDIFF( HOUR, FROM_UNIXTIME( o.repair_finish_date / 1000, '%Y-%m-%d %H:%i:%s'
                ), d.finish_time )),0)) &gt;= #{ew.repairTime[0]}
            </if>
            <!-- 维修花费 -->
            <if test="ew.repairTime[1]!=null and ew.repairTime[1]!=''">
                and (IF(d.finish_time IS NOT NULL AND o.repair_finish_date IS NOT NULL,
                (TIMESTAMPDIFF( HOUR, FROM_UNIXTIME( o.repair_finish_date / 1000, '%Y-%m-%d %H:%i:%s'
                ), d.finish_time )),0)) &lt;= #{ew.repairTime[1]}
            </if>
        </if>
        <!-- 维修开始时间 -->
        <if test="ew.repairStartDate!= null and ew.repairStartDate.size()==2">
            <if test="ew.repairStartDate[0]!=null and ew.repairStartDate[0]!='' and ew.repairStartDate[0]!='null'">
                and FROM_UNIXTIME( o.repair_finish_date / 1000, '%Y-%m-%d %H:%i:%s') &gt;=
                concat(#{ew.repairStartDate[0]}, ' 00:00:00')
            </if>
            <if test="ew.repairStartDate[1]!=null and ew.repairStartDate[1]!='' and ew.repairStartDate[1]!='null'">
                and FROM_UNIXTIME( o.repair_finish_date / 1000, '%Y-%m-%d %H:%i:%s') &lt;=
                concat(#{ew.repairStartDate[1]}, ' 23:59:59')
            </if>
        </if>

        <!-- 维修完成时间 -->
        <if test="ew.repairEndDate!= null and ew.repairEndDate.size()==2">
            <if test="ew.repairEndDate[0]!=null and ew.repairEndDate[0]!='' and ew.repairEndDate[0]!='null'">
                and d.finish_time &gt;= concat(#{ew.repairEndDate[0]}, ' 00:00:00')
            </if>
            <if test="ew.repairEndDate[1]!=null and ew.repairEndDate[1]!='' and ew.repairEndDate[1]!='null'">
                and d.finish_time &lt;= concat(#{ew.repairEndDate[1]}, ' 23:59:59')
            </if>
        </if>
    </select>

    <select id="repairReportAssetRecord" resultMap="RepairAssetRecordDto">
        SELECT
        od.id,
        FROM_UNIXTIME( o.repair_date / 1000, '%Y-%m-%d %H:%i:%s' ) AS repair_date
        FROM
        as_repair_report_order o
        JOIN as_repair_report_order_detail od ON o.id = od.repair_report_order_id
        WHERE
        o.company_id = #{companyId}
        AND o.approve_status IN ( 0, 3 )
        AND od.id IN
        <foreach collection="assetIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by od.id asc, o.repair_date desc
    </select>

</mapper>