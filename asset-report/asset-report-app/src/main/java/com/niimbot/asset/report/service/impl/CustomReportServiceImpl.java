package com.niimbot.asset.report.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.asset.report.constants.ReportConstant;
import com.niimbot.asset.report.enums.CustomReportBizTypeEnum;
import com.niimbot.asset.report.enums.DimensionDisplayTypeEnum;
import com.niimbot.asset.report.enums.ReportChartTypeEnum;
import com.niimbot.asset.report.enums.ReportTerminalEnum;
import com.niimbot.asset.report.mapper.AsCustomReportMapper;
import com.niimbot.asset.report.model.AsCustomReport;
import com.niimbot.asset.report.model.AsPredefinedReportConfig;
import com.niimbot.asset.report.service.AssetCustomReportService;
import com.niimbot.asset.report.service.CustomReportService;
import com.niimbot.asset.report.service.DynamicFieldService;
import com.niimbot.asset.report.service.OutCustomReportService;
import com.niimbot.asset.report.service.PredefinedReportConfigService;
import com.niimbot.asset.report.service.ReceiptCustomReportService;
import com.niimbot.asset.report.service.StatisticsConditionService;
import com.niimbot.asset.report.service.StockCustomReportService;
import com.niimbot.asset.report.service.StorageCustomReportService;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;
import com.niimbot.report.DimensionFieldDto;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.report.DynamicFieldQueryDto;
import com.niimbot.report.NormItemDto;
import com.niimbot.report.ReportConfigItemDto;
import com.niimbot.report.StatisticsConditionDescDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/8 上午11:12
 */
@Slf4j
@Service
public class CustomReportServiceImpl extends ServiceImpl<AsCustomReportMapper, AsCustomReport> implements CustomReportService {

    @Autowired
    private DynamicFieldService dynamicFieldService;
    @Autowired
    private AssetCustomReportService assetCustomReportService;
    @Autowired
    private StockCustomReportService stockCustomReportService;
    @Autowired
    private StorageCustomReportService storageReportService;
    @Autowired
    private OutCustomReportService outReportService;
    @Autowired
    private ReceiptCustomReportService receiptReportService;
    @Autowired
    private PredefinedReportConfigService predefinedReportConfigService;
    @Autowired
    private StatisticsConditionService statisticsConditionService;
    @Autowired
    private AsFormService formService;

    @Override
    public DimensionDataDto configReport(CustomReportConfigDto customReportConfigDto) {
        //校验报表业务类型是否正确
        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
        if (CustomReportBizTypeEnum.UN_KNOW.equals(bizTypeEnum)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
        }

        AsCustomReport customReport = new AsCustomReport();
        BeanUtils.copyProperties(customReportConfigDto, customReport);
        customReport.setCompanyId(LoginUserThreadLocal.getCompanyId());
        customReport.setReportTerminal(ReportTerminalEnum.PC.getCode());
        //校验维度指标重复
        verifyDimensionNormConflict(customReportConfigDto);
        //保存自定义报表
        this.save(customReport);
        return new DimensionDataDto().setReportId(customReport.getId());
//        switch (bizTypeEnum) {
//            case ASSET:
//                return assetCustomReportService.reportData(customReportConfigDto);
//            case MATERIAL_STOCK:
//                return stockCustomReportService.reportData(customReportConfigDto);
//            case MATERIAL_STORAGE:
//                return storageReportService.reportData(customReportConfigDto);
//            case MATERIAL_OUT:
//                return outReportService.reportData(customReportConfigDto);
//            case MATERIAL_RECEIPT:
//                return receiptReportService.reportData(customReportConfigDto);
//            default:
//                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
//        }
    }

    @Override
    public DimensionDataDto editReport(CustomReportConfigDto customReportConfigDto) {
        //校验报表是否存在
        verifyExistReport(customReportConfigDto.getId());

        //校验报表业务类型是否正确
        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
        if (CustomReportBizTypeEnum.UN_KNOW.equals(bizTypeEnum)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
        }

        AsCustomReport modifyParam = new AsCustomReport();
        BeanUtils.copyProperties(customReportConfigDto, modifyParam);
        //校验维度指标重复
        verifyDimensionNormConflict(customReportConfigDto);
        this.updateById(modifyParam);
        return new DimensionDataDto().setReportId(customReportConfigDto.getId());

//        //返回报表数据
//        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
//        switch (bizTypeEnum) {
//            case ASSET:
//                return assetCustomReportService.reportData(customReportConfigDto);
//            case MATERIAL_STOCK:
//                return stockCustomReportService.reportData(customReportConfigDto);
//            case MATERIAL_STORAGE:
//                return storageReportService.reportData(customReportConfigDto);
//            case MATERIAL_OUT:
//                return outReportService.reportData(customReportConfigDto);
//            case MATERIAL_RECEIPT:
//                return receiptReportService.reportData(customReportConfigDto);
//            default:
//                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
//        }
    }

    @Override
    public DimensionDataDto queryReport(CustomReportConfigDto configDto) {
        //校验报表是否存在
        AsCustomReport customReport = verifyExistReport(configDto.getId());

        //返回报表数据
        CustomReportConfigDto customReportConfigDto = new CustomReportConfigDto();
        BeanUtils.copyProperties(customReport, customReportConfigDto);
        //耗材编码和名称是传输到这个字段进行筛选
        customReportConfigDto.setKw(configDto.getKw());
        //设置更多筛选条件，前端传过来的，有可能为空
        customReportConfigDto.setDimensionQueryCondition(configDto.getDimensionQueryCondition());
        //返回报表数据
        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
        switch (bizTypeEnum) {
            case ASSET:
                return assetCustomReportService.reportData(customReportConfigDto);
            case MATERIAL_STOCK:
                return stockCustomReportService.reportData(customReportConfigDto);
            case MATERIAL_STORAGE:
                return storageReportService.reportData(customReportConfigDto);
            case MATERIAL_OUT:
                return outReportService.reportData(customReportConfigDto);
            case MATERIAL_RECEIPT:
                return receiptReportService.reportData(customReportConfigDto);
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
        }
    }

    @Override
    public CustomReportConfigDto queryReportConfig(Long id) {
        AsCustomReport customReport = verifyExistReport(id);
        //返回报表数据
        CustomReportConfigDto customReportConfigDto = new CustomReportConfigDto();
        BeanUtils.copyProperties(customReport, customReportConfigDto);
        return customReportConfigDto;
    }

    @Override
    public DimensionFieldDto dimensionConditionField(Long reportId) {
        //校验报表是否存在
        AsCustomReport customReport = verifyExistReport(reportId);
        DynamicFieldQueryDto dynamicFieldQueryDto = new DynamicFieldQueryDto().setBizType(customReport.getBizType()).setFieldType(ReportConstant.DIMENSION);
        List<QueryConditionDto> dimensionQueryField = dynamicFieldService.queryField(dynamicFieldQueryDto);

        DimensionFieldDto result = new DimensionFieldDto();

        //耗材档案需要特殊处理，需要把耗材档案相关字段都添加进来
        if (!CustomReportBizTypeEnum.ASSET.getCode().equals(customReport.getBizType())) {
            dimensionQueryField.addAll(dynamicFieldService.materialField());
            result.setMaterialField(customReport.getDimension().stream()
                    .filter(dimensionItemDto -> DimensionDisplayTypeEnum.MATERIAL.getCode().equals(dimensionItemDto.getDisplayType()))
                    .map(DimensionItemDto::getCode).collect(Collectors.toList()));
        }

        Map<String, QueryConditionDto> dimensionMap = dimensionQueryField.stream().collect(Collectors.toMap(QueryConditionDto::getCode, value -> value, (v1, v2) -> v2));
        List<QueryConditionDto> allField = new ArrayList<>();
        for (DimensionItemDto item : customReport.getDimension()) {
            if (Objects.nonNull(dimensionMap.get(item.getCode()))) {
                //如果是耗材仓库，需要进行特殊处理，要设置fieldProps，前端列表需要调用仓库接口，否则会报错
                if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                    QueryConditionDto conditionDto = dimensionMap.get(item.getCode());
                    FormBaseFieldCO baseFieldCO = formService.getBaseFieldByType(FormFieldCO.YZC_REPOSITORY);
                    conditionDto.setFieldProps(baseFieldCO.getFieldProps());
                    allField.add(conditionDto);
                } else {
                    allField.add(dimensionMap.get(item.getCode()));
                }
            }
        }

        result.setAllField(allField);
        return result;
    }

    @Override
    public List<NormItemDto> normQueryField(Long reportId) {
        //校验报表是否存在
        AsCustomReport customReport = verifyExistReport(reportId);
        List<NormItemDto> result = new ArrayList<>();
        //明细报表返回汇总字段
        if (ReportChartTypeEnum.DETAIL_TABLE.getCode().equals(customReport.getType())) {
            if (CollUtil.isNotEmpty(customReport.getSummary())) {
                result = customReport.getSummary();
            }
        } else {
            result = customReport.getNorm();
        }
        return result;
    }

    @Override
    public List<QueryConditionDto> statisticsConditionField(Long reportId) {
        //校验报表是否存在
        AsCustomReport customReport = verifyExistReport(reportId);
        return customReport.getStatisticCondition();
    }

    @Override
    public List<StatisticsConditionDescDto> statisticsConditionDesc(Long reportId) {
        //校验报表是否存在
        AsCustomReport customReport = verifyExistReport(reportId);
        return statisticsConditionService.resolveDesc(customReport.getStatisticCondition());
    }

    @Override
    public List<ReportConfigItemDto> queryAll() {
        List<ReportConfigItemDto> result = new ArrayList<>();
        //自定义报表
        List<AsCustomReport> customReportList = this.list(Wrappers.lambdaQuery(AsCustomReport.class)
                .select(AsCustomReport::getId, AsCustomReport::getReportName,
                        AsCustomReport::getBizType, AsCustomReport::getType, AsCustomReport::getSortCondition,
                        AsCustomReport::getReportTerminal, AsCustomReport::getCreateTime)
                .eq(AsCustomReport::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsCustomReport::getReportTerminal, ReportTerminalEnum.PC.getCode())
                .eq(AsCustomReport::getIsDelete, 0)
                .orderByDesc(AsCustomReport::getCreateTime));
        if (CollUtil.isNotEmpty(customReportList)) {
            result.addAll(customReportList.stream().map(item -> {
                ReportConfigItemDto reportConfigItemDto = new ReportConfigItemDto();
                BeanUtils.copyProperties(item, reportConfigItemDto);
                return reportConfigItemDto;
            }).collect(Collectors.toList()));
        }

        //系统报表：运营后台自定义报表 + 预定义报表
        List<ReportConfigItemDto> systemReportList = new ArrayList<>();
        //运营后台自定义报表
        List<AsCustomReport> manageCustomList = this.list(Wrappers.lambdaQuery(AsCustomReport.class)
                .select(AsCustomReport::getId, AsCustomReport::getReportName,
                        AsCustomReport::getBizType, AsCustomReport::getType, AsCustomReport::getSortCondition,
                        AsCustomReport::getReportTerminal, AsCustomReport::getCreateTime)
                .eq(AsCustomReport::getReportTerminal, ReportTerminalEnum.MANAGE.getCode())
                .eq(AsCustomReport::getIsDelete, 0));
        if (CollUtil.isNotEmpty(manageCustomList)) {
            systemReportList.addAll(manageCustomList.stream().map(item -> {
                ReportConfigItemDto reportConfigItemDto = new ReportConfigItemDto();
                BeanUtils.copyProperties(item, reportConfigItemDto);
                return reportConfigItemDto;
            }).collect(Collectors.toList()));
        }

        //预定义报表
        List<AsPredefinedReportConfig> predefinedReportList = predefinedReportConfigService.list(Wrappers.lambdaQuery(AsPredefinedReportConfig.class)
                .select(AsPredefinedReportConfig::getId, AsPredefinedReportConfig::getReportName,
                        AsPredefinedReportConfig::getBizType, AsPredefinedReportConfig::getType,
                        AsPredefinedReportConfig::getCreateTime)
                .eq(AsPredefinedReportConfig::getIsDelete, 0));
        if (CollUtil.isNotEmpty(predefinedReportList)) {
            systemReportList.addAll(predefinedReportList.stream().map(item -> {
                ReportConfigItemDto reportConfigItemDto = new ReportConfigItemDto();
                BeanUtils.copyProperties(item, reportConfigItemDto);
                reportConfigItemDto.setReportTerminal(ReportTerminalEnum.MANAGE.getCode());
                return reportConfigItemDto;
            }).collect(Collectors.toList()));
        }
        result.addAll(systemReportList.stream().sorted(Comparator.comparing(ReportConfigItemDto::getCreateTime).reversed()).collect(Collectors.toList()));
        return result;
    }

    @Override
    public Boolean removeCustomReportConfig(Long id) {
        //校验报表是否存在
        verifyExistReport(id);
        return this.update(Wrappers.lambdaUpdate(AsCustomReport.class)
                .eq(AsCustomReport::getId, id)
                .set(AsCustomReport::getIsDelete, 1));
    }

    @Override
    public AssetStatisticsDataDto detailReportConfig(CustomReportConfigDto customReportConfigDto) {
        //校验报表业务类型是否正确
        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
        if (CustomReportBizTypeEnum.UN_KNOW.equals(bizTypeEnum)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
        }

        AsCustomReport customReport = new AsCustomReport();
        BeanUtils.copyProperties(customReportConfigDto, customReport);
        customReport.setType(ReportChartTypeEnum.DETAIL_TABLE.getCode());
        customReport.setCompanyId(LoginUserThreadLocal.getCompanyId());
        customReport.setReportTerminal(ReportTerminalEnum.PC.getCode());
        //保存自定义明细报表
        this.save(customReport);
        return new AssetStatisticsDataDto().setReportId(customReport.getId());
//        //返回报表数据
//        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
//        switch (bizTypeEnum) {
//            case ASSET:
//                return assetCustomReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_STOCK:
//                return stockCustomReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_STORAGE:
//                return storageReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_OUT:
//                return outReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_RECEIPT:
//                return receiptReportService.statisticsReportData(customReportConfigDto);
//            default:
//                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
//        }
    }

    @Override
    public AssetStatisticsDataDto editDetailReport(CustomReportConfigDto customReportConfigDto) {
        //校验报表是否存在
        verifyExistReport(customReportConfigDto.getId());

        //校验报表业务类型是否正确
        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
        if (CustomReportBizTypeEnum.UN_KNOW.equals(bizTypeEnum)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
        }

        AsCustomReport modifyParam = new AsCustomReport();
        BeanUtils.copyProperties(customReportConfigDto, modifyParam);
        this.updateById(modifyParam);
        return new AssetStatisticsDataDto().setReportId(modifyParam.getId());

        //返回报表数据
//        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(customReportConfigDto.getBizType());
//        switch (bizTypeEnum) {
//            case ASSET:
//                return assetCustomReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_STOCK:
//                return stockCustomReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_STORAGE:
//                return storageReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_OUT:
//                return outReportService.statisticsReportData(customReportConfigDto);
//            case MATERIAL_RECEIPT:
//                return receiptReportService.statisticsReportData(customReportConfigDto);
//            default:
//                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
//        }
    }

    @Override
    public AssetStatisticsDataDto queryDetailReport(CustomReportConfigDto customReportConfigDto) {
        //校验报表是否存在
        AsCustomReport customReport = verifyExistReport(customReportConfigDto.getId());

        //返回报表数据
        CustomReportConfigDto reportConfigDto = new CustomReportConfigDto();
        BeanUtils.copyProperties(customReport, reportConfigDto);
        //前端传过来的，更多筛选条件，需要额外去进行处理
        reportConfigDto.setDimensionQueryCondition(customReportConfigDto.getDimensionQueryCondition());
        reportConfigDto.setKw(customReportConfigDto.getKw());
        reportConfigDto.setCurrent(customReportConfigDto.getCurrent());
        reportConfigDto.setSize(customReportConfigDto.getSize());
        reportConfigDto.setDetailId(customReportConfigDto.getDetailId());
        //返回报表数据
        CustomReportBizTypeEnum bizTypeEnum = CustomReportBizTypeEnum.getByCode(reportConfigDto.getBizType());
        switch (bizTypeEnum) {
            case ASSET:
                return assetCustomReportService.statisticsReportData(reportConfigDto);
            case MATERIAL_STOCK:
                return stockCustomReportService.statisticsReportData(reportConfigDto);
            case MATERIAL_STORAGE:
                return storageReportService.statisticsReportData(reportConfigDto);
            case MATERIAL_OUT:
                return outReportService.statisticsReportData(reportConfigDto);
            case MATERIAL_RECEIPT:
                return receiptReportService.statisticsReportData(reportConfigDto);
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "报表业务类型错误");
        }
    }

    /**
     * 校验维度和指标相同
     * @param customReportConfigDto
     */
    private void verifyDimensionNormConflict(CustomReportConfigDto customReportConfigDto) {
        List<String> dimensionCodeList = customReportConfigDto.getDimension().stream().map(DimensionItemDto::getCode).collect(Collectors.toList());
        List<String> normCodeList = customReportConfigDto.getNorm().stream().map(NormItemDto::getCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(CollUtil.intersection(dimensionCodeList, normCodeList))) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "维度和指标有重复字段");
        }
    }

    /**
     * 校验报表信息是否存在
     * @param id
     * @return
     */
    private AsCustomReport verifyExistReport(Long id) {
        AsCustomReport customReport = this.getBaseMapper()
                .selectOne(Wrappers.lambdaQuery(AsCustomReport.class)
                        .eq(AsCustomReport::getId, id)
                        .eq(AsCustomReport::getIsDelete, 0));
        if (Objects.isNull(customReport)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "自定义报表不存在");
        }
        return customReport;
    }
}
