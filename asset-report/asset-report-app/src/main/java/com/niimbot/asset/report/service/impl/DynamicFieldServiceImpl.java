package com.niimbot.asset.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.service.AsAssetQueryFieldService;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.asset.report.constants.ReportConstant;
import com.niimbot.asset.report.enums.CustomReportBizTypeEnum;
import com.niimbot.asset.report.service.DynamicFieldService;
import com.niimbot.easydesign.form.dto.clientobject.FormBaseFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.report.DynamicFieldQueryDto;
import com.niimbot.system.QueryConditionDto;
import com.niimbot.system.QueryTypeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/7 下午2:21
 */
@Slf4j
@Service
public class DynamicFieldServiceImpl implements DynamicFieldService {

    private final String domain;

    public DynamicFieldServiceImpl(Environment environment) {
        if (!StrUtil.equals(environment.getProperty("asset.edition"), Edition.LOCAL)) {
            // 非本地部署，需要写入接口
            this.domain = environment.getProperty("asset.domain.interface");
        } else {
            // 本地部署，前端自动拼接路由
            this.domain = StrUtil.EMPTY;
        }
    }

    @Autowired
    private AsFormService formService;

    @Autowired
    private AsAssetQueryFieldService assetQueryFieldService;

    @Override
    public List<QueryConditionDto> queryField(DynamicFieldQueryDto queryDto) {
        //资产分析维度
        if (CustomReportBizTypeEnum.ASSET.getCode().equals(queryDto.getBizType()) && ReportConstant.DIMENSION.equalsIgnoreCase(queryDto.getFieldType())) {
            return assetDimensionField();
        } else if (CustomReportBizTypeEnum.ASSET.getCode().equals(queryDto.getBizType()) && ReportConstant.NORM.equalsIgnoreCase(queryDto.getFieldType())) {
            //资产数据指标
            return assetNormField();
        } else if (CustomReportBizTypeEnum.ASSET.getCode().equals(queryDto.getBizType()) && ReportConstant.STATISTICS.equalsIgnoreCase(queryDto.getFieldType())) {
            //资产统计范围
            return assetStatisticsCondition();
        } else if (CustomReportBizTypeEnum.ASSET.getCode().equals(queryDto.getBizType()) && ReportConstant.TABLE_HEADER.equalsIgnoreCase(queryDto.getFieldType())) {
            //资产明细表表头字段
            return assetHeaderField();
        } else if (CustomReportBizTypeEnum.ASSET.getCode().equals(queryDto.getBizType()) && ReportConstant.SUMMARY.equalsIgnoreCase(queryDto.getFieldType())) {
            //资产数据指标
            return assetNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STOCK.getCode().equals(queryDto.getBizType()) && ReportConstant.DIMENSION.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材库存维度
            return stockDimensionField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STOCK.getCode().equals(queryDto.getBizType()) && ReportConstant.NORM.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材库存数据指标
            return stockNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STOCK.getCode().equals(queryDto.getBizType()) && ReportConstant.STATISTICS.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材统计范围
            return stockStatisticsCondition();
        } else if (CustomReportBizTypeEnum.MATERIAL_STOCK.getCode().equals(queryDto.getBizType()) && ReportConstant.TABLE_HEADER.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材库存表头
            return stockHeadField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STOCK.getCode().equals(queryDto.getBizType()) && ReportConstant.SUMMARY.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材汇总字段
            return stockNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STORAGE.getCode().equals(queryDto.getBizType()) && ReportConstant.DIMENSION.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材入库维度
            return storageDimensionField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STORAGE.getCode().equals(queryDto.getBizType()) && ReportConstant.NORM.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材入库指标
            return storageNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STORAGE.getCode().equals(queryDto.getBizType()) && ReportConstant.STATISTICS.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材入库统计范围
            return storageStatisticsCondition();
        } else if (CustomReportBizTypeEnum.MATERIAL_STORAGE.getCode().equals(queryDto.getBizType()) && ReportConstant.TABLE_HEADER.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材入库表头
            return storageHeadField();
        } else if (CustomReportBizTypeEnum.MATERIAL_STORAGE.getCode().equals(queryDto.getBizType()) && ReportConstant.SUMMARY.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材汇总字段
            return storageNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_OUT.getCode().equals(queryDto.getBizType()) && ReportConstant.DIMENSION.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材出库维度
            return outDimensionField();
        } else if (CustomReportBizTypeEnum.MATERIAL_OUT.getCode().equals(queryDto.getBizType()) && ReportConstant.NORM.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材出库指标
            return outNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_OUT.getCode().equals(queryDto.getBizType()) && ReportConstant.STATISTICS.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材出库统计范围
            return outStatisticsCondition();
        } else if (CustomReportBizTypeEnum.MATERIAL_OUT.getCode().equals(queryDto.getBizType()) && ReportConstant.TABLE_HEADER.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材出库表头
            return outHeadField();
        } else if (CustomReportBizTypeEnum.MATERIAL_OUT.getCode().equals(queryDto.getBizType()) && ReportConstant.SUMMARY.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材汇总字段
            return outNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_RECEIPT.getCode().equals(queryDto.getBizType()) && ReportConstant.DIMENSION.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材出库维度
            return receiptDimensionField();
        } else if (CustomReportBizTypeEnum.MATERIAL_RECEIPT.getCode().equals(queryDto.getBizType()) && ReportConstant.NORM.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材出库指标
            return receiptNormField();
        } else if (CustomReportBizTypeEnum.MATERIAL_RECEIPT.getCode().equals(queryDto.getBizType()) && ReportConstant.STATISTICS.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材出库统计范围
            return receiptStatisticsCondition();
        } else if (CustomReportBizTypeEnum.MATERIAL_RECEIPT.getCode().equals(queryDto.getBizType()) && ReportConstant.TABLE_HEADER.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材领用表头
            return receiptHeadField();
        } else if (CustomReportBizTypeEnum.MATERIAL_RECEIPT.getCode().equals(queryDto.getBizType()) && ReportConstant.SUMMARY.equalsIgnoreCase(queryDto.getFieldType())) {
            //耗材汇总字段
            return receiptNormField();
        }
        return null;
    }

    @Override
    public List<QueryConditionDto> materialField() {
        List<QueryConditionDto> materialCondition = materialAllField();
        //耗材档案需要去除耗材分类和耗材图片
        return materialCondition.stream().filter(item -> !("materialCategory".equalsIgnoreCase(item.getCode())
                || FormFieldCO.IMAGES.equalsIgnoreCase(item.getType()))).collect(Collectors.toList());
    }

    @Override
    public List<String> materialFieldCode() {
        List<QueryConditionDto> materialAllField = materialAllField();
        if (CollUtil.isNotEmpty(materialAllField)) {
            return materialAllField.stream().map(QueryConditionDto::getCode).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> materialOrderFieldCode(Integer orderType) {
        List<QueryConditionDto> materialOrderFieldList = materialOrderAllHeadField(orderType);
        return materialOrderFieldList.stream().map(QueryConditionDto::getCode).collect(Collectors.toList());
    }

    @Override
    public Boolean isMaterialOrderFieldCode(String code, Integer orderType) {
        List<String> materialOrderFieldList = materialFieldCode();
        return materialOrderFieldList.contains(code);
    }

    @Override
    public Map<String, QueryConditionDto> assetFixedDimension() {
        Map<String, QueryConditionDto> result = new LinkedHashMap<>();
        //资产状态
        result.put(QueryFieldConstant.ASSET_FIELD_STATUS, assetToQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));
        //标准品
        result.put(QueryFieldConstant.ASSET_FILED_STANDARD, assetToQueryConditionDto(QueryFieldConstant.ASSET_FILED_STANDARD));
        //创建时间
        result.put(QueryFieldConstant.FIELD_CREATE_TIME, assetToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        //创建人
        result.put(QueryFieldConstant.FIELD_CREATE_BY, assetToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY));
        result.putAll(ReportAssetConstant.ASSET_COMMON_DIMENSION);
        return result;
    }

    @Override
    public Map<String, QueryConditionDto> assetFixedNorm() {
        //资产闲置率，数字类型，百分号
//        JSONObject fieldProps = new JSONObject();
//        fieldProps.put("numberFormatType", 2);
//        result.put(ReportAssetConstant.IDLE_RATE, new QueryConditionDto().setCode(ReportAssetConstant.IDLE_RATE).setName("资产闲置率").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(fieldProps));
        return new LinkedHashMap<>(ReportAssetConstant.ASSET_COMMON_NORM);
    }

    /**
     * 资产分析维度动态表单字段
     * @return
     */
    private List<QueryConditionDto> assetDimensionField() {
        //资产维度固定字段
        List<QueryConditionDto> result = new ArrayList<>(assetFixedDimension().values());

        FormVO assetForm = formService.assetTpl();
        if (Objects.isNull(assetForm) || CollUtil.isEmpty(assetForm.getFormFields())) {
            return result;
        }

        //下拉单选、下拉多选、日期、数字、组织单选、员工单选、区域单选、供应商单选、资产分类单选
        List<String> dimensionList = ListUtil.of(FormFieldCO.SELECT_DROPDOWN, FormFieldCO.MULTI_SELECT_DROPDOWN, FormFieldCO.DATETIME,
                FormFieldCO.NUMBER_INPUT, FormFieldCO.YZC_ORG, FormFieldCO.YZC_EMP, FormFieldCO.YZC_AREA,
                FormFieldCO.YZC_SUPPLIER, FormFieldCO.YZC_ASSET_CATE);
        for (FormFieldCO item : assetForm.getFormFields()) {
            if (dimensionList.contains(item.getFieldType())) {
                result.add(new QueryConditionDto()
                        .setName(item.getFieldName())
                        .setCode(item.getFieldCode())
                        .setType(item.getFieldType())
                        .setFieldProps(item.getFieldProps()));
            }
        }

        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    /**
     * 资产分析维度字段
     * @return
     */
    private List<QueryConditionDto> assetNormField() {
        List<QueryConditionDto> result = new ArrayList<>(assetFixedNorm().values());
        FormVO assetForm = formService.assetTpl();
        if (Objects.isNull(assetForm) || CollUtil.isEmpty(assetForm.getFormFields())) {
            return result;
        }

        for (FormFieldCO item : assetForm.getFormFields()) {
            if (FormFieldCO.NUMBER_INPUT.equalsIgnoreCase(item.getFieldType())) {
                result.add(new QueryConditionDto()
                        .setName(item.getFieldName())
                        .setCode(item.getFieldCode())
                        .setType(item.getFieldType())
                        .setFieldProps(item.getFieldProps()));
            }
        }
        return result;
    }

    /**
     * 资产统计范围
     * @return
     */
    private List<QueryConditionDto> assetStatisticsCondition() {
        List<QueryConditionDto> result = new ArrayList<>();

        //补齐资产状态，创建人，创建时间，更新时间，最近打印时间
        result.add(assetToQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));
        result.add(assetToQueryConditionDto(QueryFieldConstant.ASSET_FILED_STANDARD));

        FormVO formVO = formService.assetTpl();
        FormBaseFieldCO empField = formService.getBaseFieldByType(FormFieldCO.YZC_EMP);

        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            result.add(queryConditionDto);
        }
        QueryConditionDto createBy = assetToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            createBy.setFieldProps(empField.getFieldProps());
            result.add(createBy);
        }
        result.add(assetToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        result.add(assetToQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        result.add(assetToQueryConditionDto(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    /**
     * 资产明细表，表头
     * @return
     */
    private List<QueryConditionDto> assetHeaderField() {
        List<QueryConditionDto> result = new ArrayList<>();

        //补齐资产状态，创建人，创建时间，更新时间，最近打印时间
        result.add(assetToQueryConditionDto(QueryFieldConstant.ASSET_FIELD_STATUS));
        result.add(assetToQueryConditionDto(QueryFieldConstant.ASSET_FILED_STANDARD));
        QueryConditionDto createBy = assetToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            FormBaseFieldCO empField = formService.getBaseFieldByType(FormFieldCO.YZC_EMP);
            createBy.setFieldProps(empField.getFieldProps());
            result.add(createBy);
        }
        result.add(assetToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        result.addAll(ReportAssetConstant.ASSET_COMMON_DIMENSION.values());

        FormVO formVO = formService.assetTpl();
        for (FormFieldCO formFieldCO : formVO.getFormFields()) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES).contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            result.add(queryConditionDto);
        }
        return result;
    }



    /**
     * 根据字段编码转换成动态表头对象
     * @param code
     * @return
     */
    private QueryConditionDto assetToQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ASSET_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    private List<QueryConditionDto> stockDimensionField() {
        //资产维度固定字段
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_STOCK_DIMENSION.values());

        return materialDimensionField(result);
    }

    private List<QueryConditionDto> materialDimensionField(List<QueryConditionDto> result) {
        FormVO assetForm = formService.materialTpl();
        if (Objects.isNull(assetForm) || CollUtil.isEmpty(assetForm.getFormFields())) {
            return result;
        }

        //下拉单选、下拉多选、日期、数字、组织单选、员工单选、区域单选、供应商单选、资产分类单选
        List<String> dimensionList = ListUtil.of(FormFieldCO.SELECT_DROPDOWN, FormFieldCO.MULTI_SELECT_DROPDOWN, FormFieldCO.DATETIME,
                FormFieldCO.NUMBER_INPUT, FormFieldCO.YZC_ORG, FormFieldCO.YZC_EMP, FormFieldCO.YZC_AREA,
                FormFieldCO.YZC_SUPPLIER, FormFieldCO.YZC_MATERIAL_CATE);
        for (FormFieldCO item : assetForm.getFormFields()) {
            if (dimensionList.contains(item.getFieldType())) {
                result.add(new QueryConditionDto()
                        .setName(item.getFieldName())
                        .setCode(item.getFieldCode())
                        .setType(item.getFieldType())
                        .setFieldProps(item.getFieldProps()));
            }
        }

        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private List<QueryConditionDto> stockNormField() {
        return new ArrayList<>(ReportAssetConstant.MATERIAL_STOCK_NORM.values());
    }

    private List<QueryConditionDto> stockStatisticsCondition() {
        List<QueryConditionDto> queryConditionDtos = new ArrayList<>();
        queryConditionDtos.add(stockToQueryConditionDto(QueryFieldConstant.MATERIAL_FILED_STANDARD));

        FormVO formVO = formService.materialTpl();
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_MATERIAL_SERIALNO, FormFieldCO.YZC_MATERIAL_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            queryConditionDtos.add(queryConditionDto);
        }

        // 补齐 标准品，创建人，创建时间
        QueryConditionDto createBy = stockToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            FormBaseFieldCO empField = formService.getBaseFieldByType(FormFieldCO.YZC_EMP);
            createBy.setFieldProps(empField.getFieldProps());
            queryConditionDtos.add(createBy);
        }
        queryConditionDtos.add(stockToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        queryConditionDtos.add(stockToQueryConditionDto(QueryFieldConstant.FIELD_UPDATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        queryConditionDtos.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return queryConditionDtos;
    }

    private List<QueryConditionDto> stockHeadField() {
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_STOCK_HEAD.values());
        //耗材表单中的字段
        result.addAll(materialAllField());
        return result;
    }

    private List<QueryConditionDto> materialAllField() {
        List<QueryConditionDto> result = new ArrayList<>();

        FormVO formVO = formService.materialTpl();
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            //去除附件和分割线
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            result.add(queryConditionDto);
        }

        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private QueryConditionDto stockToQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.MATERIAL_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    private List<QueryConditionDto> storageDimensionField() {
        //资产维度固定字段
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_STORAGE_DIMENSION.values());

        //添加耗材表单中的耗材分类
        materialCategoryField(result);

        FormVO formVO = formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(OrderFormTypeEnum.RK.getCode()).getBizType(), LoginUserThreadLocal.getCompanyId());
        if (Objects.isNull(formVO) || CollUtil.isEmpty(formVO.getFormFields())) {
            return result;
        }

        //下拉单选、下拉多选、日期、数字、组织单选、员工单选、区域单选、供应商单选、资产分类单选
        List<String> dimensionList = ListUtil.of(FormFieldCO.SELECT_DROPDOWN, FormFieldCO.MULTI_SELECT_DROPDOWN, FormFieldCO.DATETIME,
                FormFieldCO.NUMBER_INPUT, FormFieldCO.YZC_ORG, FormFieldCO.YZC_EMP, FormFieldCO.YZC_REPOSITORY,
                FormFieldCO.YZC_SUPPLIER, FormFieldCO.YZC_MATERIAL_CATE);
        for (FormFieldCO item : formVO.getFormFields()) {
            if (dimensionList.contains(item.getFieldType())) {
                result.add(new QueryConditionDto()
                        .setName(item.getFieldName())
                        .setCode(item.getFieldCode())
                        .setType(item.getFieldType())
                        .setFieldProps(item.getFieldProps()));
            }
        }

        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private void materialCategoryField(List<QueryConditionDto> result) {
        FormVO materialForm = formService.materialTpl();
        if (Objects.isNull(materialForm) || CollUtil.isEmpty(materialForm.getFormFields())) {
            return ;
        }

        Optional<FormFieldCO> materialCategoryOptional = materialForm.getFormFields().stream()
                .filter(item -> ReportAssetConstant.MATERIAL_CATEGORY.equalsIgnoreCase(item.getFieldCode())).findAny();
        materialCategoryOptional.ifPresent(formFieldCO -> result.add(new QueryConditionDto()
                .setName(formFieldCO.getFieldName())
                .setCode(formFieldCO.getFieldCode())
                .setType(formFieldCO.getFieldType())
                .setFieldProps(formFieldCO.getFieldProps())));
    }

    private List<QueryConditionDto> storageNormField() {
        return new ArrayList<>(ReportAssetConstant.MATERIAL_STORAGE_NORM.values());
    }

    private List<QueryConditionDto> storageStatisticsCondition() {
        List<QueryConditionDto> result = new ArrayList<>();

        FormVO formVO = formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(OrderFormTypeEnum.RK.getCode()).getBizType(), LoginUserThreadLocal.getCompanyId());
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            result.add(queryConditionDto);
        }
        QueryConditionDto createBy = storageToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            FormBaseFieldCO empField = formService.getBaseFieldByType(FormFieldCO.YZC_EMP);
            createBy.setFieldProps(empField.getFieldProps());
            result.add(createBy);
        }
        result.add(storageToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private QueryConditionDto storageToQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    private List<QueryConditionDto> storageHeadField() {
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_STORAGE_HEAD.values());
        //耗材入库表单动态字段
        List<QueryConditionDto> materialStorageAllField = materialOrderAllHeadField(OrderFormTypeEnum.RK.getCode());
        result.addAll(materialStorageAllField);
        //耗材入库表单字段列表
        List<String> materialStorageFieldCodeList = materialStorageAllField.stream().map(QueryConditionDto::getCode).collect(Collectors.toList());

        //耗材所有动态字段
        List<QueryConditionDto> materialField = materialAllField();
        if (CollUtil.isNotEmpty(materialField)) {
            for (QueryConditionDto item : materialField) {
                //如果耗材入库表单动态字段中有，就跳过
                if (materialStorageFieldCodeList.contains(item.getCode())) {
                    continue;
                } else {
                    result.add(item);
                }
            }
        }
        return result;
    }

    private List<QueryConditionDto> outDimensionField() {
        //资产维度固定字段
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_OUT_DIMENSION.values());

        //添加耗材表单中的耗材分类
        materialCategoryField(result);

        FormVO formVO = formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(OrderFormTypeEnum.CK.getCode()).getBizType(), LoginUserThreadLocal.getCompanyId());
        if (Objects.isNull(formVO) || CollUtil.isEmpty(formVO.getFormFields())) {
            return result;
        }

        //下拉单选、下拉多选、日期、数字、组织单选、员工单选、区域单选、供应商单选、资产分类单选
        List<String> dimensionList = ListUtil.of(FormFieldCO.SELECT_DROPDOWN, FormFieldCO.MULTI_SELECT_DROPDOWN, FormFieldCO.DATETIME,
                FormFieldCO.NUMBER_INPUT, FormFieldCO.YZC_ORG, FormFieldCO.YZC_EMP, FormFieldCO.YZC_REPOSITORY,
                FormFieldCO.YZC_SUPPLIER, FormFieldCO.YZC_MATERIAL_CATE);
        for (FormFieldCO item : formVO.getFormFields()) {
            if (dimensionList.contains(item.getFieldType())) {
                result.add(new QueryConditionDto()
                        .setName(item.getFieldName())
                        .setCode(item.getFieldCode())
                        .setType(item.getFieldType())
                        .setFieldProps(item.getFieldProps()));
            }
        }

        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private List<QueryConditionDto> outNormField() {
        return new ArrayList<>(ReportAssetConstant.MATERIAL_OUT_NORM.values());
    }

    private List<QueryConditionDto> outStatisticsCondition() {
        List<QueryConditionDto> result = new ArrayList<>();

        FormVO formVO = formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(OrderFormTypeEnum.CK.getCode()).getBizType(), LoginUserThreadLocal.getCompanyId());
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            result.add(queryConditionDto);
        }
        QueryConditionDto createBy = outToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            FormBaseFieldCO empField = formService.getBaseFieldByType(FormFieldCO.YZC_EMP);
            createBy.setFieldProps(empField.getFieldProps());
            result.add(createBy);
        }
        result.add(outToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private QueryConditionDto outToQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }

    private List<QueryConditionDto> outHeadField() {
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_OUT_HEAD.values());
        //耗材出库表单动态字段
        List<QueryConditionDto> materialOutAllField = materialOrderAllHeadField(OrderFormTypeEnum.CK.getCode());
        result.addAll(materialOutAllField);
        //耗材出库表单字段列表
        List<String> materialOutFieldCodeList = materialOutAllField.stream().map(QueryConditionDto::getCode).collect(Collectors.toList());

        //耗材所有动态字段
        List<QueryConditionDto> materialField = materialAllField();
        if (CollUtil.isNotEmpty(materialField)) {
            for (QueryConditionDto item : materialField) {
                //如果耗材出库表单动态字段中有，就跳过
                if (materialOutFieldCodeList.contains(item.getCode())) {
                    continue;
                } else {
                    result.add(item);
                }
            }
        }
        return result;
    }

    private List<QueryConditionDto> receiptDimensionField() {
        //资产维度固定字段
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_RECEIPT_DIMENSION.values());

        //添加耗材表单中的耗材分类
        materialCategoryField(result);

        FormVO formVO = formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(OrderFormTypeEnum.LY.getCode()).getBizType(), LoginUserThreadLocal.getCompanyId());
        if (Objects.isNull(formVO) || CollUtil.isEmpty(formVO.getFormFields())) {
            return result;
        }

        //下拉单选、下拉多选、日期、数字、组织单选、员工单选、区域单选、供应商单选、资产分类单选
        List<String> dimensionList = ListUtil.of(FormFieldCO.SELECT_DROPDOWN, FormFieldCO.MULTI_SELECT_DROPDOWN, FormFieldCO.DATETIME,
                FormFieldCO.NUMBER_INPUT, FormFieldCO.YZC_ORG, FormFieldCO.YZC_EMP, FormFieldCO.YZC_REPOSITORY,
                FormFieldCO.YZC_SUPPLIER, FormFieldCO.YZC_MATERIAL_CATE);
        for (FormFieldCO item : formVO.getFormFields()) {
            if (dimensionList.contains(item.getFieldType())) {
                result.add(new QueryConditionDto()
                        .setName(item.getFieldName())
                        .setCode(item.getFieldCode())
                        .setType(item.getFieldType())
                        .setFieldProps(item.getFieldProps()));
            }
        }

        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private List<QueryConditionDto> receiptNormField() {
        return new ArrayList<>(ReportAssetConstant.MATERIAL_RECEIPT_NORM.values());
    }

    private List<QueryConditionDto> receiptStatisticsCondition() {
        List<QueryConditionDto> result = new ArrayList<>();

        FormVO formVO = formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(OrderFormTypeEnum.LY.getCode()).getBizType(), LoginUserThreadLocal.getCompanyId());
        List<FormFieldCO> formFields = formVO.getFormFields();
        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_ASSET_NAME)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            result.add(queryConditionDto);
        }
        QueryConditionDto createBy = outToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY);
        if (createBy != null) {
            FormBaseFieldCO empField = formService.getBaseFieldByType(FormFieldCO.YZC_EMP);
            createBy.setFieldProps(empField.getFieldProps());
            result.add(createBy);
        }
        result.add(outToQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private List<QueryConditionDto> receiptHeadField() {
        List<QueryConditionDto> result = new ArrayList<>(ReportAssetConstant.MATERIAL_RECEIPT_HEAD.values());
        //耗材领用表单动态字段
        List<QueryConditionDto> materialReceiptAllField = materialOrderAllHeadField(OrderFormTypeEnum.LY.getCode());
        result.addAll(materialReceiptAllField);
        //耗材领用表单字段列表
        List<String> materialReceiptFieldCodeList = materialReceiptAllField.stream().map(QueryConditionDto::getCode).collect(Collectors.toList());

        //耗材所有动态字段
        List<QueryConditionDto> materialField = materialAllField();
        if (CollUtil.isNotEmpty(materialField)) {
            for (QueryConditionDto item : materialField) {
                //如果耗材领用表单动态字段中有，就跳过
                if (materialReceiptFieldCodeList.contains(item.getCode())) {
                    continue;
                } else {
                    result.add(item);
                }
            }
        }
        return result;
    }

    /**
     * 耗材单据表单动态字段
     * @param orderType
     * @return
     */
    private List<QueryConditionDto> materialOrderAllHeadField(Integer orderType) {
        List<QueryConditionDto> result = new ArrayList<>();

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());

        result.add(toQueryConditionDto(QueryFieldConstant.FIELD_APPROVE_STATUS));
        if (AssetConstant.ORDER_TYPE_MATERIAL_LY == orderType) {
            result.add(toQueryConditionDto(QueryFieldConstant.FIELD_GRANT_STATUS));
        } else if (AssetConstant.ORDER_TYPE_MATERIAL_TK == orderType) {
            result.add(toQueryConditionDto(QueryFieldConstant.FIELD_TK_IN_REPO));
        }
        List<FormFieldCO> formFields = formVO.getFormFields();

        for (FormFieldCO formFieldCO : formFields) {
            if (ListUtil.of(FormFieldCO.SPLIT_LINE, FormFieldCO.FILES)
                    .contains(formFieldCO.getFieldType())) {
                continue;
            }
            if (formFieldCO.isHidden()) {
                continue;
            }
            QueryConditionDto queryConditionDto = new QueryConditionDto()
                    .setName(formFieldCO.getFieldName())
                    .setCode(formFieldCO.getFieldCode())
                    .setType(formFieldCO.getFieldType())
                    .setFieldProps(formFieldCO.getFieldProps());
            result.add(queryConditionDto);
        }
        result.add(toQueryConditionDto(QueryFieldConstant.FIELD_ORDER_NO));
        result.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_BY));
        result.add(toQueryConditionDto(QueryFieldConstant.FIELD_CREATE_TIME));
        Map<String, List<QueryTypeDto>> operatorMap = assetQueryFieldService.getOperator();
        result.forEach(f -> f.setOperators(operatorMap.get(f.getType())));
        return result;
    }

    private QueryConditionDto toQueryConditionDto(String code) {
        QueryFieldConstant.Field field = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(code);
        if (field != null) {
            QueryConditionDto conditionDto = new QueryConditionDto();
            conditionDto.setFixedField(Boolean.TRUE);
            conditionDto.setCode(field.getCode());
            conditionDto.setName(field.getName());
            conditionDto.setType(field.getType());
            conditionDto.setFieldProps(new JSONObject());
            if (FormFieldCO.DATETIME.equals(field.getType())) {
                conditionDto.getFieldProps().put("dateFormatType", "yyyy-MM-dd");
            }
            if (StrUtil.isNotBlank(field.getUrl())) {
                String url = String.format(field.getUrl(), domain);
                JSONObject urlJson = new JSONObject();
                urlJson.put("url", url);
                conditionDto.getFieldProps().put("extProps", urlJson);
            }
            return conditionDto;
        } else {
            return null;
        }
    }
}
