package com.niimbot.asset.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.report.model.AsCustomReport;
import com.niimbot.report.*;
import com.niimbot.system.QueryConditionDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/8 上午11:11
 */
public interface CustomReportService extends IService<AsCustomReport> {

    /**
     * 自定义报表
     * @param customReportConfigDto
     * @return
     */
    DimensionDataDto configReport(CustomReportConfigDto customReportConfigDto);

    /**
     * 编辑自定义报表
     * @param customReportConfigDto
     * @return
     */
    DimensionDataDto editReport(CustomReportConfigDto customReportConfigDto);

    /**
     * 查询自定义报表
     * @param configDto
     * @return
     */
    DimensionDataDto queryReport(CustomReportConfigDto configDto);

    /**
     * 资产统计明细报表配置
     * @param customReportConfigDto
     * @return
     */
    AssetStatisticsDataDto detailReportConfig(CustomReportConfigDto customReportConfigDto);

    /**
     * 编辑资产明细报表配置
     * @param customReportConfigDto
     * @return
     */
    AssetStatisticsDataDto editDetailReport(CustomReportConfigDto customReportConfigDto);

    /**
     * 查询资产明细报表
     * @param customReportConfigDto
     * @return
     */
    AssetStatisticsDataDto queryDetailReport(CustomReportConfigDto customReportConfigDto);

    /**
     * 获取报表配置详情
     * @param id
     * @return
     */
    CustomReportConfigDto queryReportConfig(Long id);

    /**
     * 统计图和统计表维度筛选条件查询
     * @param reportId
     * @return
     */
    DimensionFieldDto dimensionConditionField(Long reportId);

    /**
     * 数据指标字段查询
     * @param reportId
     * @return
     */
    List<NormItemDto> normQueryField(Long reportId);

    /**
     * 统计范围字段查询
     * @param reportId
     * @return
     */
    List<QueryConditionDto> statisticsConditionField(Long reportId);

    /**
     * 查询统计范围描述信息
     * @param reportId
     * @return
     */
    List<StatisticsConditionDescDto> statisticsConditionDesc(Long reportId);

    /**
     * 查询企业所有的自定义报表
     * @return
     */
    List<ReportConfigItemDto> queryAll();

    /**
     * 删除自定义报表配置
     * @param id
     * @return
     */
    Boolean removeCustomReportConfig(Long id);
}
