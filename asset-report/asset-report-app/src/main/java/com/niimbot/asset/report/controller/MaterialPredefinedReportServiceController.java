package com.niimbot.asset.report.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.report.service.MaterialPredefinedReportService;
import com.niimbot.report.MaterialCkManifestDto;
import com.niimbot.report.MaterialCkManifestQueryDto;
import com.niimbot.report.MaterialCkStatisticsDto;
import com.niimbot.report.MaterialCkStatisticsQueryDto;
import com.niimbot.report.MaterialManifestTotalDto;
import com.niimbot.report.MaterialRkManifestDto;
import com.niimbot.report.MaterialRkManifestQueryDto;
import com.niimbot.report.MaterialRkStatisticsDto;
import com.niimbot.report.MaterialRkStatisticsQueryDto;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/23 11:30
 */
@RestController
@RequestMapping("server/report/predefined/material")
@RequiredArgsConstructor
public class MaterialPredefinedReportServiceController {

    private final MaterialPredefinedReportService predefinedReportService;

    @PostMapping("/rk/manifest")
    public IPage<MaterialRkManifestDto> materialRkManifest(@RequestBody MaterialRkManifestQueryDto queryDto) {
        return predefinedReportService.materialRkManifest(queryDto);
    }

    @PostMapping("/rk/manifest/total")
    public MaterialManifestTotalDto materialRkManifestTotal(@RequestBody MaterialRkManifestQueryDto queryDto) {
        return predefinedReportService.materialRkManifestTotal(queryDto);
    }

    @PostMapping("/ck/manifest")
    public IPage<MaterialCkManifestDto> materialCkManifest(@RequestBody MaterialCkManifestQueryDto queryDto) {
        return predefinedReportService.materialCkManifest(queryDto);
    }

    @PostMapping("/ck/manifest/total")
    public MaterialManifestTotalDto materialCkManifestTotal(@RequestBody MaterialCkManifestQueryDto queryDto) {
        return predefinedReportService.materialCkManifestTotal(queryDto);
    }

    @PostMapping("/rk/statistics")
    public List<MaterialRkStatisticsDto> materialRkStatistics(@RequestBody MaterialRkStatisticsQueryDto queryDto) {
        return predefinedReportService.materialRkStatistics(queryDto);
    }

    @PostMapping("/ck/statistics")
    public List<MaterialCkStatisticsDto> materialCkStatistics(@RequestBody MaterialCkStatisticsQueryDto queryDto) {
        return predefinedReportService.materialCkStatistics(queryDto);
    }


}
