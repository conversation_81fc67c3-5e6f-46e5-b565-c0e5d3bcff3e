package com.niimbot.asset.report.service;

import com.niimbot.report.DimensionTreeItemDto;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/12 上午9:52
 */
public interface TreeDimensionService {

    /**
     * 查询组织分组，根据不同的树状类型
     * @param companyId
     * @param type
     * @return
     */
    Map<Long, DimensionTreeItemDto> queryOrgGroup(Long companyId, Integer type);

    /**
     * 查询分类分组，根据不同的树状类型
     * @param companyId
     * @param type
     * @return
     */
    Map<Long, DimensionTreeItemDto> queryCategoryGroup(Long companyId, Integer type);


    /**
     * 查询存放区域分组
     * @param companyId
     * @param type
     * @return
     */
    Map<Long, DimensionTreeItemDto> queryAreaGroup(Long companyId, Integer type);

    /**
     * 查询耗材分类分组，根据不同的树状类型
     * @param companyId
     * @param type
     * @return
     */
    Map<Long, DimensionTreeItemDto> queryMaterialCategoryGroup(Long companyId, Integer type);
}
