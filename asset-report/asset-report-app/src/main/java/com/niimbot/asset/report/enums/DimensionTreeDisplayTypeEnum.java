package com.niimbot.asset.report.enums;

import lombok.Getter;

/**
 * 维度树状展示类型枚举
 *
 * <AUTHOR>
 * @date 2023/6/12 上午9:57
 */
@Getter
public enum DimensionTreeDisplayTypeEnum {

    ALL_CONTAIN_CHILD(1, "所有且包含子级"),
    ALL(2, "所有且不包含子级"),
    ONLY_FIRST_LEVEL(3, "仅展示一级部门且包含子级"),
    ONLY_COMPANY(4, "仅展示企业"),
    ;

    DimensionTreeDisplayTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public static DimensionTreeDisplayTypeEnum getByCode(Integer code) {
        for (DimensionTreeDisplayTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return DimensionTreeDisplayTypeEnum.ALL;
    }
}
