package com.niimbot.asset.report.component;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.system.QueryConditionDto;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/7/11 下午1:36
 */
@Component
public class MaterialReportQueryConditionResolver extends AbsQueryConditionResolver<String, QueryConditionDto> {

    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.material_data ->> '$.%s'";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.material_data ->> '$.%s' as DECIMAL(20,4))";

    private ImmutableMap<String, QueryFieldConstant.Field> MATERIAL_STOCK_FIXED_QUERY = new ImmutableMap.Builder<String, QueryFieldConstant.Field>()
            .put(ReportAssetConstant.REPOSITORY_ID, new QueryFieldConstant.Field(ReportAssetConstant.REPOSITORY_ID, "耗材仓库", FormFieldCO.YZC_REPOSITORY, ""))
            .put(ReportAssetConstant.STOCK_NUM, new QueryFieldConstant.Field(ReportAssetConstant.STOCK_NUM, "库存数量", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.AVG_PRICE, new QueryFieldConstant.Field(ReportAssetConstant.AVG_PRICE, "加权平均价值", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.STOCK_PRICE, new QueryFieldConstant.Field(ReportAssetConstant.STOCK_PRICE, "库存金额", FormFieldCO.NUMBER_INPUT, ""))
            .putAll(QueryFieldConstant.MATERIAL_EXT_FIELD)
            .build();

    /**
     * 解析仓库条件
     * @param condition
     * @return
     */
    public String resolveRepositoryIdQueryCondition(QueryConditionDto condition) {
        StringJoiner joiner = new StringJoiner(" ");
        if (Objects.nonNull(condition)) {
            String sqlField = StrUtil.toUnderlineCase(condition.getCode());
            String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                    condition.getCode(), condition.getType(),
                    MATERIAL_STOCK_FIXED_QUERY);
            if (StrUtil.isNotBlank(conditionStr)) {
                joiner.add(conditionStr);
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition, joiner);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        MATERIAL_STOCK_FIXED_QUERY);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition, StringJoiner joiner) {
        if (QueryFieldConstant.MATERIAL_EXT_FIELD.containsKey(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            } else if (QueryFieldConstant.MATERIAL_STOCK_FILED_STATUS.equals(condition.getCode())) {
                // 特殊处理库存预警状态
                if (condition.getQuery().equals("1")) {
                    // 正常
                    String tpl = " and ({}.current_quantity >= {}.material_data ->> '$.safetyStock' or {}.material_data ->> '$.safetyStock' is null or {}.material_data ->> '$.safetyStock' = '') ";
                    String format = StrUtil.format(tpl, tableAlias, tableAlias, tableAlias, tableAlias);
                    joiner.add(format);
                    return format;
                } else if (condition.getQuery().equals("2")) {
                    // 预警
                    String tpl = " and {}.current_quantity < {}.material_data ->> '$.safetyStock' ";
                    String format = StrUtil.format(tpl, tableAlias, tableAlias);
                    joiner.add(format);
                    return format;
                } else {
                    return StrUtil.EMPTY;
                }
            } else if (QueryFieldConstant.MATERIAL_STOCK_FILED_STOCK.equals(condition.getCode())) {
                // 特殊处理库存状态
                if (condition.getQuery().equals("1")) {
                    // 无库存
                    String tpl = " and {}.current_quantity > 0 ";
                    String format = StrUtil.format(tpl, tableAlias);
                    joiner.add(format);
                    return format;
                } else if (condition.getQuery().equals("2")) {
                    // 有库存
                    String tpl = " and ({}.current_quantity = 0 or {}.repository_id is NULL) ";
                    String format = StrUtil.format(tpl, tableAlias, tableAlias);
                    joiner.add(format);
                    return format;
                } else {
                    return StrUtil.EMPTY;
                }
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        } else if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(condition.getCode())
                || ReportAssetConstant.AVG_PRICE.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        } else if (ReportAssetConstant.STOCK_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "current_quantity");
        } else if (ReportAssetConstant.STOCK_PRICE.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "total_money");
        }
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, code);
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }
}
