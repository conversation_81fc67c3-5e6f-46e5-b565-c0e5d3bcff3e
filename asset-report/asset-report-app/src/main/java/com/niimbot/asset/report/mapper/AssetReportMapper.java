package com.niimbot.asset.report.mapper;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.means.AssetStatusDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/11 下午2:55
 */
public interface AssetReportMapper {

    /**
     * 资产自定义报表动态查询sql
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> assetCustomReport(@Param("param") JSONObject queryFieldDto);

    /**
     * 查询所有的资产状态
     * @return
     */
    List<AssetStatusDto> selectAllStatus();

    /**
     * 库存
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> stockCustomReport(@Param("param") JSONObject queryFieldDto);

    /**
     * 库存
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> stockRepositoryCustomReport(@Param("param") JSONObject queryFieldDto);

    /**
     * 查询耗材其他字段
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> selectMaterialOtherField(@Param("param") JSONObject queryFieldDto);

    /**
     * 入库
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> storageCustomReport(@Param("param") JSONObject queryFieldDto);

    /**
     * 查询耗材其他字段，从快照中查询
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> selectMaterialOtherFieldStorage(@Param("param") JSONObject queryFieldDto);

    /**
     * 入库
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> outCustomReport(@Param("param") JSONObject queryFieldDto);

    /**
     * 查询耗材其他字段，从快照中查询
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> selectMaterialOtherFieldOut(@Param("param") JSONObject queryFieldDto);

    /**
     * 入库
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> receiptCustomReport(@Param("param") JSONObject queryFieldDto);

    /**
     * 查询耗材其他字段，从快照中查询
     * @param queryFieldDto
     * @return
     */
    List<JSONObject> selectMaterialOtherFieldReceipt(@Param("param") JSONObject queryFieldDto);

    /**
     * 关联领用单的耗材退库数量
     *
     * @param lyOrderNos
     * @param companyId
     * @return
     */
    List<Map<String, Object>> countTkNumByLyOrder(@Param("lyOrderNos") List<String> lyOrderNos,
                                                  @Param("companyId") Long companyId);
}
