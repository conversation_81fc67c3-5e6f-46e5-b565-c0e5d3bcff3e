package com.niimbot.asset.report.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.material.model.AsMaterialCategory;
import com.niimbot.asset.material.model.AsRepository;
import com.niimbot.asset.material.resolver.MySqlMaterialQueryConditionResolver;
import com.niimbot.asset.means.resolver.MySqlAssetQueryConditionResolver;
import com.niimbot.asset.means.service.StandardService;
import com.niimbot.asset.report.enums.DimensionTreeDisplayTypeEnum;
import com.niimbot.asset.report.mapper.PredefinedReportMapper;
import com.niimbot.asset.report.service.PredefinedReportService;
import com.niimbot.asset.report.service.TreeDimensionService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.page.SortQuery;
import com.niimbot.report.AssetLogReportDto;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.DimensionTreeItemDto;
import com.niimbot.report.GroupReportItem;
import com.niimbot.report.GroupReportResult;
import com.niimbot.report.HandleAssetLogDto;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.MaterialCategoryReportItem;
import com.niimbot.report.MaterialCategoryReportSearch;
import com.niimbot.report.MaterialRepositoryReportItem;
import com.niimbot.report.MaterialRepositoryReportSearch;
import com.niimbot.report.MeansCategoryReportItem;
import com.niimbot.report.MeansCategoryReportSearch;
import com.niimbot.report.MeansUseOrgReportItem;
import com.niimbot.report.RepairAssetLogDto;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.ReportEnum;
import com.niimbot.report.UseOrgGroupReportSearch;
import com.niimbot.report.WaitHandleAssetLogDto;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PredefinedReportServiceImpl implements PredefinedReportService {

    private final PredefinedReportMapper predefinedReportMapper;
    private final MySqlAssetQueryConditionResolver assetQueryConditionResolver;
    private final TreeDimensionService treeDimensionService;
    private final AsQueryConditionConfigService queryConditionConfigService;
    private final AsFormService formService;
    private final StandardService standardService;
    private final MySqlMaterialQueryConditionResolver materialQueryConditionResolver;
    @Autowired
    private DataScopeStrategyManager dataScopeStrategyManager;

    @Override
    public QueryConditionSortDto assetSortField(String type) {
        ReportEnum reportEnum = ReportEnum.getByType(type);
        QueryConditionSortDto querySort = new QueryConditionSortDto();
        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(reportEnum.getHead());
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        // 标准品
        Long standardId = queryHeadConfig.getStandardId();
        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (standardId != null && standardId > 0L) {
            List<FormFieldCO> extField = standardService.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(extField);
        }
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

        switch (reportEnum) {
            case ASSET_LOG:
                querySort.getSortList().add(0,
                        new QueryConditionSortDto.Field("处理时间", "handleTime", null));
                break;
            case WAIT_HANDLE_ASSET_LOG:
                querySort.getSortList().add(0,
                        new QueryConditionSortDto.Field("到期时间", "dueTime", null));
                break;
            case HANDLE_ASSET_LOG:
                querySort.getSortList().add(0,
                        new QueryConditionSortDto.Field("处置日期", "disposeDate", null));
                break;
            case WAIT_RETURN_ASSET_LOG:
                querySort.getSortList().add(0,
                        new QueryConditionSortDto.Field("借用日期", "borrowDate", null));
                querySort.getSortList().add(1,
                        new QueryConditionSortDto.Field("预计归还日期", "estimateBackDate", null));
                break;
            case REPAIR_ASSET_LOG:
                querySort.getSortList().add(0,
                        new QueryConditionSortDto.Field("维修次数", "repairCount", null));
                querySort.getSortList().add(1,
                        new QueryConditionSortDto.Field("维修花费", "repairCost", null));
                break;
            default:
                break;
        }

        // 补齐 创建时间、更新时间、打印时间
        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ASSET_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }

        QueryFieldConstant.Field updateTimeField = QueryFieldConstant.ASSET_EXT_FIELD.get(QueryFieldConstant.FIELD_UPDATE_TIME);
        if (ObjectUtil.isNotNull(updateTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(updateTimeField.getName(), updateTimeField.getCode(), updateTimeField.getType()));
        }

        QueryFieldConstant.Field lastPrintTimeField = QueryFieldConstant.ASSET_EXT_FIELD.get(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME);
        if (ObjectUtil.isNotNull(lastPrintTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(lastPrintTimeField.getName(), lastPrintTimeField.getCode(), lastPrintTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    public GroupReportResult meansUseOrgFixedHeadReport(UseOrgGroupReportSearch search) {
        DimensionTreeDisplayTypeEnum enumerate = DimensionTreeDisplayTypeEnum.getByCode(search.getType());
        Map<Long, DimensionTreeItemDto> orgGroup = treeDimensionService.queryOrgGroup(LoginUserThreadLocal.getCompanyId(), search.getType());
        List<MeansUseOrgReportItem> items = predefinedReportMapper.selectMeansUseOrgGroupReport(search, assetQueryConditionResolver.getPermsSql("a"));
        return groupFixedHeadReport(enumerate, orgGroup, items, MeansUseOrgReportItem::new);
    }

    @Override
    public GroupReportResult meansCategoryFixedHeadReport(MeansCategoryReportSearch search) {
        DimensionTreeDisplayTypeEnum enumerate = DimensionTreeDisplayTypeEnum.getByCode(search.getType());
        Map<Long, DimensionTreeItemDto> categoryGroup = treeDimensionService.queryCategoryGroup(LoginUserThreadLocal.getCompanyId(), search.getType());
        List<MeansCategoryReportItem> items = predefinedReportMapper.selectMeansCategoryGroupReport(search, assetQueryConditionResolver.getPermsSql("t1"));
        return groupFixedHeadReport(enumerate, categoryGroup, items, MeansCategoryReportItem::new);
    }

    @Override
    public GroupReportResult materialCategoryFixedHeadReport(MaterialCategoryReportSearch search) {
        DimensionTreeDisplayTypeEnum enumerate = DimensionTreeDisplayTypeEnum.getByCode(search.getType());
        Map<Long, DimensionTreeItemDto> materialCategoryGroup = treeDimensionService.queryMaterialCategoryGroup(LoginUserThreadLocal.getCompanyId(), search.getType());

        String storeSql = dataScopeStrategyManager.simplePermsSql(DataPermType.STORE);
        List<MaterialCategoryReportItem> reportItems = predefinedReportMapper.selectMaterialCategoryGroupOpeningAndEndingReport(search, storeSql);
        List<MaterialCategoryReportItem> processReport = predefinedReportMapper.selectMaterialCategoryGroupProcessReport(search, storeSql);
        Map<Long, MaterialCategoryReportItem> processMap = processReport.stream().collect(Collectors.toConcurrentMap(MaterialCategoryReportItem::getCategoryId, v -> v));
        reportItems.parallelStream().forEach(v -> v.merge(processMap.get(v.getCategoryId())));
        return groupFixedHeadReport(enumerate, materialCategoryGroup, reportItems, MaterialCategoryReportItem::new);
    }

    @Override
    public GroupReportResult materialRepositoryFixedHeadReport(MaterialRepositoryReportSearch search) {
        String condition = materialQueryConditionResolver.resolveQueryCondition("t2", search.getConditions());
        ConcurrentMap<Long, String> repositoryNameMap = Db.list(Wrappers.lambdaQuery(AsRepository.class)
                        .select(AsRepository::getId, AsRepository::getName).eq(AsRepository::getCompanyId, search.getCompanyId()))
                .stream().collect(Collectors.toConcurrentMap(AsRepository::getId, AsRepository::getName));
        ConcurrentMap<Long, String> categoryNameMap = Db
                .list(Wrappers.lambdaQuery(AsMaterialCategory.class).select(AsMaterialCategory::getId, AsMaterialCategory::getCategoryName).eq(AsMaterialCategory::getCompanyId, search.getCompanyId()))
                .stream().collect(Collectors.toConcurrentMap(AsMaterialCategory::getId, AsMaterialCategory::getCategoryName));
        // 分页
        String storeSql = dataScopeStrategyManager.simplePermsSql(DataPermType.STORE);
        IPage<MaterialRepositoryReportItem> items = predefinedReportMapper.selectMaterialRepositoryGroupReport(search.buildIPage(), search, condition, storeSql);
        // 合计
        MaterialRepositoryReportItem summary = predefinedReportMapper.selectMaterialRepositoryGroupReport(search, condition, storeSql).stream().reduce(new MaterialRepositoryReportItem(), (v1, v2) -> (MaterialRepositoryReportItem) v1.reduce(v2));
        items.getRecords().forEach(v -> {
            v.setRepositoryName(repositoryNameMap.get(v.getRepositoryId()));
            v.getMaterialData().put("materialCategoryText", categoryNameMap.getOrDefault(v.getMaterialData().getLong("materialCategory"), ""));
        });
        return new GroupReportResult().setItems(new PageUtils<>(items)).setSummary(summary);
    }

    private GroupReportResult groupFixedHeadReport(DimensionTreeDisplayTypeEnum enumerate, Map<Long, DimensionTreeItemDto> groupTree, List<? extends GroupReportItem> reportItems, Supplier<GroupReportItem> supplier) {
        GroupReportResult result = new GroupReportResult();
        reportItems.removeIf(GroupReportItem::filter);
        GroupReportItem summary = supplier.get();
        summary.replenish(-1L, "合计");
        reportItems.forEach(v -> {
            if (groupTree.containsKey(v.groupId())) {
                v.replenish(v.groupId(), groupTree.get(v.groupId()).getName());
            }
            summary.reduce(v);
        });
        result.setSummary(summary);
        // 不包含下级节点数据
        if (enumerate.equals(DimensionTreeDisplayTypeEnum.ALL)) {
            result.setItems(reportItems);
            return result;
        }
        // 包含下级节点数据
        ConcurrentMap<Long, GroupReportItem> itemGroup = reportItems.stream().filter(v -> Objects.nonNull(v.groupId())).collect(Collectors.toConcurrentMap(GroupReportItem::groupId, v -> v));
        List<GroupReportItem> items = new ArrayList<>(reportItems.size());
        groupTree.forEach((k, v) -> {
            if (!itemGroup.containsKey(k)) {
                return;
            }
            GroupReportItem item = v.getChild().stream()
                    .filter(itemGroup::containsKey)
                    .map(itemGroup::get)
                    .reduce(supplier.get(), GroupReportItem::reduce);
            item.replenish(itemGroup.get(k).groupId(), v.getName());
            items.add(item);
        });
        result.setItems(items);
        return result;
    }

    @Override
    public IPage<AssetLogReportDto> assetLogReport(AssetLogReportQueryDto queryDto) {
        String tableAlias = "a";
        String conditions = assetQueryConditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        Page<Object> page = buildAssetSort(ReportEnum.ASSET_LOG, tableAlias, queryDto);
        return predefinedReportMapper.assetLogReport(page, queryDto, LoginUserThreadLocal.getCompanyId(), conditions);
    }

    @Override
    public IPage<WaitHandleAssetLogDto> waitHandleAssetLog(WaitHandleAssetLogQueryDto queryDto) {
        String tableAlias = "a";
        String conditions = assetQueryConditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        Page<Object> page = buildAssetSort(ReportEnum.WAIT_HANDLE_ASSET_LOG, tableAlias, queryDto);
        page.setOptimizeCountSql(false);
        return predefinedReportMapper.waitHandleAssetLog(page, queryDto, LoginUserThreadLocal.getCompanyId(), conditions);
    }

    @Override
    public IPage<HandleAssetLogDto> handleAssetLog(HandleAssetLogQueryDto queryDto) {
        String tableAlias = "a";
        String conditions = assetQueryConditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        Page<Object> page = buildAssetSort(ReportEnum.HANDLE_ASSET_LOG, tableAlias, queryDto);
        return predefinedReportMapper.handleAssetLog(page, queryDto, LoginUserThreadLocal.getCompanyId(), conditions);
    }

    @Override
    public IPage<WaitReturnAssetLogDto> waitReturnAssetLog(WaitReturnAssetLogQueryDto queryDto) {
        String tableAlias = "a";
        String conditions = assetQueryConditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        Page<Object> page = buildAssetSort(ReportEnum.WAIT_RETURN_ASSET_LOG, tableAlias, queryDto);
        return predefinedReportMapper.waitReturnAssetLog(page, queryDto, LoginUserThreadLocal.getCompanyId(), conditions);
    }

    @Override
    public IPage<RepairAssetLogDto> repairAssetLog(RepairAssetLogQueryDto queryDto) {
        String tableAlias = "a";
        String conditions = assetQueryConditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        Page<Object> page = buildAssetSort(ReportEnum.REPAIR_ASSET_LOG, tableAlias, queryDto);
        return predefinedReportMapper.repairAssetLog(page, queryDto, LoginUserThreadLocal.getCompanyId(), conditions);
    }

    @Override
    public IPage<RepairAssetRecordDto> repairAssetRecord(RepairAssetRecordQueryDto queryDto) {
        String tableAlias = "a";
        String conditions = assetQueryConditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        Page<Object> page = buildAssetSort(ReportEnum.REPAIR_ASSET_RECORD, tableAlias, queryDto);
        IPage<RepairAssetRecordDto> repairAssetRecordPage = predefinedReportMapper.repairAssetRecord(page, queryDto, LoginUserThreadLocal.getCompanyId(), conditions);
        // 反查报修单的报修时间
        List<RepairAssetRecordDto> records = repairAssetRecordPage.getRecords();
        Set<Long> assetIds = records.stream().map(RepairAssetRecordDto::getId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(assetIds)) {
            List<RepairAssetRecordDto> repairReportAssetRecord = predefinedReportMapper.repairReportAssetRecord(assetIds, LoginUserThreadLocal.getCompanyId());
            Map<Long, List<LocalDateTime>> repairDateMap = repairReportAssetRecord.stream()
                    .filter(f -> ObjectUtil.isNotNull(f.getRepairDate()))
                    .collect(Collectors.groupingBy(RepairAssetRecordDto::getId,
                            Collectors.mapping(RepairAssetRecordDto::getRepairDate, Collectors.toList())));
            // 距离维修开始时间最近的，为报修时间
            records.forEach(r -> {
                if (r.getRepairStartDate() != null && repairDateMap.containsKey(r.getId())) {
                    List<LocalDateTime> timeList = repairDateMap.get(r.getId());
                    for (LocalDateTime dateTime : timeList) {
                        LocalDateTime d = null;
                        if (dateTime.compareTo(r.getRepairStartDate()) <= 0) {
                            d = dateTime;
                        }
                        if (d != null) {
                            r.setRepairDate(d);
                            break;
                        }
                    }
                }
            });
        }
        return repairAssetRecordPage;
    }

    private Page<Object> buildAssetSort(ReportEnum reportEnum, String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_asset";
        }
        Page<Object> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = assetSortField(reportEnum.getCode());
        Map<String, String> codeAndType = new HashMap<>();
        for (QueryConditionSortDto.Field field : querySort.getSortList()) {
            codeAndType.put(field.getValue(), field.getType());
        }
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ASSET_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.asset_data", tableAlias), column));
                } else {
                    if (type == null) {
                        if (ReportEnum.ASSET_LOG.equals(reportEnum) && "handleTime".equals(column)) {
                            order.setColumn("max(handle_time)");
                        } else {
                            order.setColumn(String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
                        }
                    } else if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlAssetQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlAssetQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlAssetQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

}
