package com.niimbot.asset.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.resolver.AssetReportQueryConditionResolver;
import com.niimbot.asset.means.resolver.MySqlAssetQueryConditionResolver;
import com.niimbot.asset.report.component.AssetReportUtil;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.asset.report.constants.ReportConstant;
import com.niimbot.asset.report.mapper.AssetReportMapper;
import com.niimbot.asset.report.service.AssetCustomReportService;
import com.niimbot.asset.report.service.DimensionGroupService;
import com.niimbot.asset.report.service.DynamicFieldService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;
import com.niimbot.report.DimensionDataItemDto;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.report.NormItemDto;
import com.niimbot.report.ReportQueryFieldDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @date 2023/6/30 上午10:34
 */
@Slf4j
@Service
public class AssetCustomReportServiceImpl implements AssetCustomReportService {

    @Autowired
    private DynamicFieldService dynamicFieldService;
    @Autowired
    private DimensionGroupService dimensionGroupService;
    @Autowired
    private AssetReportUtil assetReportUtil;
    @Autowired
    private AssetReportQueryConditionResolver assetQueryConditionResolver;
    @Autowired
    private AssetReportMapper assetReportMapper;

    @Override
    public DimensionDataDto reportData(CustomReportConfigDto customReportConfigDto) {
        //处理维度和指标数据
        DimensionDataDto result = new DimensionDataDto();
        result.setReportId(customReportConfigDto.getId());
        result.setReportName(customReportConfigDto.getReportName());
        result.setNormName(customReportConfigDto.getNorm().stream().map(NormItemDto::getName).collect(Collectors.toList()));

        //生成查询SQL
        ReportQueryFieldDto queryFieldDto = generateQuerySqlParam(customReportConfigDto);
        String queryParamStr = JSONObject.toJSONString(queryFieldDto);
        JSONObject queryParam = JSONObject.parseObject(queryParamStr);
        log.info("assetCustomReportService reportData companyId=[{}] queryParam=[{}]", LoginUserThreadLocal.getCompanyId(), queryParamStr);

        //执行SQL查询，查询数据
        queryParam.put("norm", customReportConfigDto.getNorm().stream().map(NormItemDto::getCode).collect(Collectors.toList()));
        List<JSONObject> assetDataList = assetReportMapper.assetCustomReport(queryParam);

        //如果数据为空，直接返回给前端，不进行后续维度数据分组处理
        if (CollUtil.isEmpty(assetDataList)) {
            result.setDimensionData(Collections.emptyList());
            return result;
        }

        //维度分组数据展示处理
        List<DimensionDataItemDto> data = dimensionGroupService.processDimensionGroup(customReportConfigDto.getDimension(), assetDataList);
        assetReportUtil.translateAssetJsonView(assetDataList, customReportConfigDto.getDimension());
        result.setDimensionData(data);
        return result;
    }

    @Override
    public AssetStatisticsDataDto statisticsReportData(CustomReportConfigDto customReportConfigDto) {
        //生成分页查询sql
        ReportQueryFieldDto queryFieldDto = generateStatisticsQuerySql(customReportConfigDto);
        //塞一个id字段查询，作为详情接口参数使用
        queryFieldDto.setFieldStr(String.join(",", "a.id as assetId", queryFieldDto.getFieldStr()));
        String queryParamStr = JSONObject.toJSONString(queryFieldDto);
        log.info("assetCustomReportService statisticsReportData companyId=[{}] queryParam=[{}]", LoginUserThreadLocal.getCompanyId(), queryParamStr);
        List<JSONObject> statisticsDataList = assetReportMapper.assetCustomReport(JSONObject.parseObject(queryParamStr));

        //生成汇总查询sql
        ReportQueryFieldDto summary = generateSummaryQuerySql(customReportConfigDto);
        String summaryStr = JSONObject.toJSONString(summary);
        log.info("assetCustomReportService statisticsReportData summary companyId=[{}] summaryQueryParam=[{}]", LoginUserThreadLocal.getCompanyId(), summaryStr);
        JSONObject queryParam = JSONObject.parseObject(summaryStr);
        queryParam.put("norm", customReportConfigDto.getNorm().stream().map(NormItemDto::getCode).collect(Collectors.toList()));
        List<JSONObject> summaryList = assetReportMapper.assetCustomReport(queryParam);

        //生成结果数据
        AssetStatisticsDataDto result = new AssetStatisticsDataDto();
        result.setDimension(customReportConfigDto.getDimension());
        //id -> name转换
        assetReportUtil.translateAssetJsonView(statisticsDataList, customReportConfigDto.getDimension());
        result.setList(statisticsDataList);
        result.setCurrent(customReportConfigDto.getCurrent());
        result.setSize(customReportConfigDto.getSize());
        result.setTotalCount(summaryList.get(0).getInteger(ReportConstant.TOTAL));
        result.setTotalPage((int)Math.ceil((double)result.getTotalCount() / (double)result.getSize()));

        //处理汇总字段名称和值
        Map<String, String> summaryData = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(customReportConfigDto.getSummary())) {
            for (NormItemDto item : customReportConfigDto.getSummary()) {
                summaryData.put(item.getName(), summaryList.get(0).getString(item.getCode()));
            }
        }
        result.setSummary(summaryData);
        return result;
    }

    /**
     * 生成资产查询参数对象
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateQuerySqlParam(CustomReportConfigDto configDto) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());

        //生成指标 select 查询字段
        String norm = resolveNorm(configDto.getNorm());
        //生成groupBy中字段到select查询字段中
        String dimension = resolveGroupByAsField(configDto);
        //select字段：groupBy中的字段 + 指标字段
        result.setFieldStr(String.join(", ", dimension, norm));
        //生成where查询条件
        String conditionStr = String.join(" ", "a.company_id =", String.valueOf(LoginUserThreadLocal.getCompanyId()));
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            String statisticsCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getStatisticCondition());
            if (StrUtil.isNotBlank(statisticsCondition)) {
                conditionStr = String.join(" ", conditionStr, statisticsCondition);
            }
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            String dimensionQueryCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getDimensionQueryCondition());
            if (StrUtil.isNotBlank(dimensionQueryCondition)) {
                conditionStr = String.join(" ", conditionStr, dimensionQueryCondition);
            }
        }
        //如果都为空，需要调用下，里面有处理解析数据权限
        if (CollUtil.isEmpty(configDto.getStatisticCondition()) && CollUtil.isEmpty(configDto.getDimensionQueryCondition())) {
            String dimensionQueryCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getDimensionQueryCondition());
            if (StrUtil.isNotBlank(dimensionQueryCondition)) {
                conditionStr = String.join(" ", conditionStr, dimensionQueryCondition);
            }
        }
        result.setConditionStr(conditionStr);
        //生成groupBy查询分组条件
        result.setGroupStr(resolveGroupBy(configDto));

        //kw关键字搜索
        result.setKw(configDto.getKw());

        //如果有排序，生成order by语句
        String orderStr = resolveSortString(configDto);
        if (StrUtil.isNotBlank(orderStr)) {
            result.setOrderStr(orderStr);
        }
        return result;
    }

    /**
     * 生成明细表查询字段
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateStatisticsQuerySql(CustomReportConfigDto configDto) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        result.setFieldStr(resolveGroupByAsField(configDto));
        //设置勾选明细id
        result.setDetailId(configDto.getDetailId());
        //生成where查询条件
        String conditionStr = String.join(" ", "a.company_id =", String.valueOf(LoginUserThreadLocal.getCompanyId()));
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            String statisticsCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getStatisticCondition());
            if (StrUtil.isNotBlank(statisticsCondition)) {
                conditionStr = String.join(" ", conditionStr, statisticsCondition);
            }
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            String dimensionQueryCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getDimensionQueryCondition());
            if (StrUtil.isNotBlank(dimensionQueryCondition)) {
                conditionStr = String.join(" ", conditionStr, dimensionQueryCondition);
            }
        }
        //如果都为空，需要调用下，里面有处理解析数据权限
        if (CollUtil.isEmpty(configDto.getStatisticCondition()) && CollUtil.isEmpty(configDto.getDimensionQueryCondition())) {
            String dimensionQueryCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getDimensionQueryCondition());
            if (StrUtil.isNotBlank(dimensionQueryCondition)) {
                conditionStr = String.join(" ", conditionStr, dimensionQueryCondition);
            }
        }
        result.setConditionStr(conditionStr);

        //设置关键字搜索
        result.setKw(configDto.getKw());

        //如果有排序，生成order by语句
        String orderStr = resolveSortString(configDto);
        if (StrUtil.isNotBlank(orderStr)) {
            result.setOrderStr(orderStr);
        }

        //生成分页limit sql
        long start = (configDto.getCurrent() - 1) * configDto.getSize();
        String limitStr = String.join(", ", String.valueOf(start), String.valueOf(configDto.getSize()));
        result.setLimitStr(limitStr);
        return result;
    }

    /**
     * 生成明细表汇总sql
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateSummaryQuerySql(CustomReportConfigDto configDto) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        //设置勾选明细id
        result.setDetailId(configDto.getDetailId());
        String summarySql = "count(a.id) as total";
        if (CollUtil.isNotEmpty(configDto.getSummary())) {
            result.setFieldStr(String.join(",", summarySql, resolveNorm(configDto.getSummary())));
        } else {
            result.setFieldStr(summarySql);
        }
        //生成where查询条件
        String conditionStr = String.join(" ", "a.company_id =", String.valueOf(LoginUserThreadLocal.getCompanyId()));
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            String statisticsCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getStatisticCondition());
            if (StrUtil.isNotBlank(statisticsCondition)) {
                conditionStr = String.join(" ", conditionStr, statisticsCondition);
            }
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            String dimensionQueryCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getDimensionQueryCondition());
            if (StrUtil.isNotBlank(dimensionQueryCondition)) {
                conditionStr = String.join(" ", conditionStr, dimensionQueryCondition);
            }
        }
        //如果都为空，需要调用下，里面有处理解析数据权限
        if (CollUtil.isEmpty(configDto.getStatisticCondition()) && CollUtil.isEmpty(configDto.getDimensionQueryCondition())) {
            String dimensionQueryCondition = assetQueryConditionResolver.resolveQueryCondition("a", configDto.getDimensionQueryCondition());
            if (StrUtil.isNotBlank(dimensionQueryCondition)) {
                conditionStr = String.join(" ", conditionStr, dimensionQueryCondition);
            }
        }
        result.setConditionStr(conditionStr);

        //设置关键字搜索
        result.setKw(configDto.getKw());
        return result;
    }

    /**
     * 解析资产数据指标查询
     * @param norm
     * @return
     */
    private String resolveNorm(List<NormItemDto> norm) {
        Set<String> fixedNorm = dynamicFieldService.assetFixedNorm().keySet();
        StringJoiner result = new StringJoiner("");
        for (NormItemDto item : norm) {
            if (ReportAssetConstant.ASSET_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.COUNT_TPL, "a.id")).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.PRICE.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.SUM_TPL, String.format(MySqlAssetQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "a", item.getCode()))).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.REPAIR_NUM.equalsIgnoreCase(item.getCode()) ||
                    ReportAssetConstant.REPAIR_REPORT_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format("IFNULL(sum(%s), 0)", String.join(".", "a", StrUtil.toUnderlineCase(item.getCode())))).add(" as ").add(item.getCode());
            } else {
                if (fixedNorm.contains(item.getCode())) {
                    result.add(", ").add(String.format(ReportConstant.SUM_TPL, String.join(".", "a", StrUtil.toUnderlineCase(item.getCode())))).add(" as ").add(item.getCode());
                } else {
                    result.add(", ").add(String.format(ReportConstant.SUM_TPL, String.format(MySqlAssetQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "a", item.getCode()))).add(" as ").add(item.getCode());
                }
            }
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析维度groupBy语句
     * @param configDto
     * @return
     */
    private String resolveGroupBy(CustomReportConfigDto configDto) {
        Set<String> fixedDimension = dynamicFieldService.assetFixedDimension().keySet();
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            String fieldStr = null;
            //处置日期和到期日期都是在额外的一张表中维护
            if (item.getCode().equalsIgnoreCase(ReportAssetConstant.EXPIRE_DATE)) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "a", "expire_date");
            } else if (item.getCode().equalsIgnoreCase(ReportAssetConstant.HANDLE_DATE)) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "a", "handle_date");
            } else {
                if (fixedDimension.contains(item.getCode())) {
                    fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "a", StrUtil.toUnderlineCase(item.getCode()));
                } else {
                    fieldStr = String.format(MySqlAssetQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "a", item.getCode());
                }
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                //过期时间和处置日期存的timestamp，不是时间戳，转换形式有差别
                if (item.getCode().equalsIgnoreCase(ReportAssetConstant.EXPIRE_DATE)
                        || item.getCode().equalsIgnoreCase(ReportAssetConstant.HANDLE_DATE)
                        || item.getCode().equalsIgnoreCase(ReportAssetConstant.CREATE_TIME)) {
                    fieldStr = timeFormatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                } else {
                    fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                }
            }
            result.add(", ").add(fieldStr);
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析维度groupBy语句，将groupBy中的字段添加到select中
     * @param configDto
     * @return
     */
    private String resolveGroupByAsField(CustomReportConfigDto configDto) {
        Set<String> fixedDimension = dynamicFieldService.assetFixedDimension().keySet();
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            String fieldStr = null;
            //处置日期和到期日期都是在额外的一张表中维护
            if (item.getCode().equalsIgnoreCase(ReportAssetConstant.EXPIRE_DATE)) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "a", "expire_date");
            } else if (item.getCode().equalsIgnoreCase(ReportAssetConstant.HANDLE_DATE)) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "a", "handle_date");
            } else {
                if (fixedDimension.contains(item.getCode())) {
                    //驼峰转下划线
                    fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "a", StrUtil.toUnderlineCase(item.getCode()));
                } else {
                    fieldStr = String.format(MySqlAssetQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "a", item.getCode());
                }
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                //过期时间和处置日期存的timestamp，不是时间戳，转换形式有差别
                if (item.getCode().equalsIgnoreCase(ReportAssetConstant.EXPIRE_DATE)
                        || item.getCode().equalsIgnoreCase(ReportAssetConstant.HANDLE_DATE)
                        || item.getCode().equalsIgnoreCase(ReportAssetConstant.CREATE_TIME)) {
                    fieldStr = timeFormatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                } else {
                    fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                }
            }
            result.add(", ").add(fieldStr).add(" as ").add(item.getCode());
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 处理日期类型group条件
     * @param fieldStr
     * @param groupCondition
     * @return
     */
    private String timeFormatDateGroup(String fieldStr, Integer groupCondition) {
        if (groupCondition == 1) {
            //1-按年分组
            return String.format(MySqlAssetQueryConditionResolver.TIME_STAMP_DATE_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_YEAR_TPL);
        } else if (groupCondition == 2) {
            //2-按月分组
            return String.format(MySqlAssetQueryConditionResolver.TIME_STAMP_DATE_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_MONTH_TPL);
        } else {
            //3-按天分组
            return String.format(MySqlAssetQueryConditionResolver.TIME_STAMP_DATE_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_DAY_TPL);
        }
    }

    /**
     * 处理日期类型group条件,时间戳
     * @param fieldStr
     * @param groupCondition
     * @return
     */
    private String formatDateGroup(String fieldStr, Integer groupCondition) {
        if (groupCondition == 1) {
            //1-按年分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_YEAR_TPL);
        } else if (groupCondition == 2) {
            //2-按月分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_MONTH_TPL);
        } else {
            //3-按天分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_DAY_TPL);
        }
    }

    /**
     * 处理排序条件字符串
     * @param configDto
     * @return
     */
    private String resolveSortString(CustomReportConfigDto configDto) {
        if (Objects.isNull(configDto.getSortCondition()) || StrUtil.isBlank(configDto.getSortCondition().getSidx())) {
            return null;
        }

        //排序字段在维度里面有，表示select已经查询出来了，直接用这个字段排序就可以了
        Optional<DimensionItemDto> dimensionItemOptional = configDto.getDimension()
                .stream()
                .filter(item -> item.getCode().equalsIgnoreCase(configDto.getSortCondition().getSidx()))
                .findAny();
        if (dimensionItemOptional.isPresent()) {
            return String.join(" ", configDto.getSortCondition().getSidx(), configDto.getSortCondition().getOrder());
        } else {
            Set<String> dimensionCodeSet = dynamicFieldService.assetFixedDimension().keySet();
            String fieldStr = null;
            if (dimensionCodeSet.contains(configDto.getSortCondition().getSidx())) {
                //驼峰转下划线
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "a", StrUtil.toUnderlineCase(configDto.getSortCondition().getSidx()));
            } else {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "a", configDto.getSortCondition().getSidx());
            }
            return String.join(" ", fieldStr, configDto.getSortCondition().getOrder());
        }
    }
}
