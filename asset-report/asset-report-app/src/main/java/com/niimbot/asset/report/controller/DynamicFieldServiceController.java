package com.niimbot.asset.report.controller;

import com.niimbot.asset.report.service.DynamicFieldService;
import com.niimbot.report.DynamicFieldQueryDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023/6/7 下午2:15
 */
@Api(tags = "动态字段")
@RestController
@RequestMapping("server/report/dynamic/field/")
public class DynamicFieldServiceController {

    @Autowired
    private DynamicFieldService dynamicFieldService;

    @ApiOperation(value = "动态表单字段查询")
    @GetMapping("query")
    public List<QueryConditionDto> query(@Validated DynamicFieldQueryDto queryDto) {
        return dynamicFieldService.queryField(queryDto);
    }

    @ApiOperation(value = "耗材档案字段")
    @GetMapping("material")
    public List<QueryConditionDto> material() {
        return dynamicFieldService.materialField();
    }
}
