package com.niimbot.asset.report.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/6/26 上午10:05
 */
@Getter
public enum ReportTerminalEnum {
    MANAGE(1, "运营后台", "系统报表"),
    PC(2, "用户", "自定义报表"),
    ;

    ReportTerminalEnum(Integer code, String desc, String outDesc) {
        this.code = code;
        this.desc = desc;
        this.outDesc = outDesc;
    }

    private Integer code;

    private String desc;

    private String outDesc;
}
