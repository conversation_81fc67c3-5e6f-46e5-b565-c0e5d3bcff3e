package com.niimbot.asset.report.service;

import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;

/**
 * <AUTHOR>
 * @date 2023/6/15 下午2:31
 */
public interface StockCustomReportService {

    /**
     * 库存报表数据
     * @param customReportConfigDto
     * @return
     */
    DimensionDataDto reportData(CustomReportConfigDto customReportConfigDto);

    /**
     * 库存明细报表数据
     * @param customReportConfigDto
     * @return
     */
    AssetStatisticsDataDto statisticsReportData(CustomReportConfigDto customReportConfigDto);
}
