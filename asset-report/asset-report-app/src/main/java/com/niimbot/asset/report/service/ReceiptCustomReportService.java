package com.niimbot.asset.report.service;

import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;

/**
 * <AUTHOR>
 * @date 2023/6/19 下午3:39
 */
public interface ReceiptCustomReportService {

    /**
     * 统计图数据
     * @param customReportConfigDto
     * @return
     */
    DimensionDataDto reportData(CustomReportConfigDto customReportConfigDto);

    /**
     * 明细报表
     * @param customReportConfigDto
     * @return
     */
    AssetStatisticsDataDto statisticsReportData(CustomReportConfigDto customReportConfigDto);
}
