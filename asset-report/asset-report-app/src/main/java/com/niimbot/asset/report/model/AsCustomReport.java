package com.niimbot.asset.report.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.report.NormItemDto;
import com.niimbot.report.SortDto;
import com.niimbot.system.QueryConditionDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * as_custom_report
 * <AUTHOR>
@Data
@Accessors
@TableName(value = "as_custom_report", autoResultMap = true)
public class AsCustomReport implements Serializable {

    private static final long serialVersionUID = 883163003183511828L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 报表名称
     */
    private String reportName;

    /**
     * 维度
     */
    @TableField(typeHandler = com.niimbot.asset.report.handle.DimensionItemTypeHandler.class)
    private List<DimensionItemDto> dimension;

    /**
     * 指标
     */
    @TableField(typeHandler = com.niimbot.asset.report.handle.NormItemTypeHandler.class)
    private List<NormItemDto> norm;

    /**
     * 汇总
     */
    @TableField(typeHandler = com.niimbot.asset.report.handle.NormItemTypeHandler.class)
    private List<NormItemDto> summary;

    /**
     * 数据统计范围
     */
    @TableField(typeHandler = com.niimbot.asset.report.handle.StatisticsConditionTypeHandler.class)
    private List<QueryConditionDto> statisticCondition;

    /**
     * 数据排序
     */
    @TableField(typeHandler = com.niimbot.asset.report.handle.SortTypeHandler.class)
    private SortDto sortCondition;

    /**
     * 业务类型：1-资产, 2-耗材
     */
    private Integer bizType;

    /**
     * 报表类型:1-基础折线图, 2-双轴折线图, 3-基础柱状图, 8-明细表
     */
    private Integer type;

    /**
     * 报表终端类型：1-manage 2-pc
     */
    private Integer reportTerminal;

    /**
     * 公司ID（租户ID） 默认0为系统配置分类
     */
    private Long companyId;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    private Boolean isDelete;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}