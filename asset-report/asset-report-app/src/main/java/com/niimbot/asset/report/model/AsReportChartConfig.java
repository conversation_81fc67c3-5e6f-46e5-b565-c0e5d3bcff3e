package com.niimbot.asset.report.model;

import com.baomidou.mybatisplus.annotation.*;
import com.niimbot.report.SupportNumDto;
import com.niimbot.asset.report.handle.SupportNumTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * as_report_chart_config
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName(value = "as_report_chart_config", autoResultMap = true)
public class AsReportChartConfig implements Serializable {

    private static final long serialVersionUID = 7560709826104466552L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 图表类型
     */
    private Integer type;

    /**
     * 支持维度数量
     */
    @TableField(typeHandler = SupportNumTypeHandler.class)
    private SupportNumDto dimensionNum;

    /**
     * 支持指标数量
     */
    @TableField(typeHandler = SupportNumTypeHandler.class)
    private SupportNumDto normNum;

    /**
     * 默认展示指标数量
     */
    @TableField(typeHandler = SupportNumTypeHandler.class)
    private SupportNumDto defaultNormNum;

    /**
     * 是否支持排序: 0-不支持 1-支持
     */
    private Boolean supportSort;

    /**
     * 公司ID（租户ID） 默认0为系统配置分类
     */
    private Long companyId;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    private Boolean isDelete;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(update = "now()", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}