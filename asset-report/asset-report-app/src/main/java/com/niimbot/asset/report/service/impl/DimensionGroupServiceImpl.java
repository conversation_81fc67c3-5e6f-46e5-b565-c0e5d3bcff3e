package com.niimbot.asset.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.report.component.AssetReportUtil;
import com.niimbot.asset.report.constants.ReportConstant;
import com.niimbot.asset.report.enums.DimensionDisplayTypeEnum;
import com.niimbot.asset.report.mapper.AssetReportMapper;
import com.niimbot.asset.report.service.DimensionGroupService;
import com.niimbot.asset.report.service.TreeDimensionService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetStatusDto;
import com.niimbot.report.DimensionDataItemDto;
import com.niimbot.report.DimensionGroupNumDto;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.report.DimensionTreeItemDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/27 上午9:43
 */
@Slf4j
@Service
public class DimensionGroupServiceImpl implements DimensionGroupService {


    @Autowired
    private TreeDimensionService treeDimensionService;
    @Autowired
    private AssetReportMapper assetReportMapper;
    @Autowired
    private AssetReportUtil assetReportUtil;

    @Override
    public List<DimensionDataItemDto> processDimensionGroup(List<DimensionItemDto> dimensionItemDtoList, List<JSONObject> rowDataList) {
        Map<String, Map<Long, DimensionTreeItemDto>> dimensionCache = new HashMap<>();
        return processAllDimension(dimensionItemDtoList, rowDataList, dimensionCache, 0);
    }

    /**
     * 递归处理数据展示
     * @param dimensionItemDtoList
     * @param rowDataList
     * @param i
     * @return
     */
    private List<DimensionDataItemDto> processAllDimension(List<DimensionItemDto> dimensionItemDtoList,
                                                           List<JSONObject> rowDataList,
                                                           Map<String, Map<Long, DimensionTreeItemDto>> dimensionCache,
                                                           int i) {
        if (dimensionItemDtoList.size() <= i) {
            return Collections.emptyList();
        }

        //处理单个维度数据分组
        List<DimensionDataItemDto> tempResult = processDimension(dimensionItemDtoList.get(i), rowDataList, dimensionCache);
        i++;
        for (DimensionDataItemDto item : tempResult) {
            if (CollUtil.isEmpty(item.getNormData())) {
                continue;
            }

            //处理下一个维度指标数据展示
            List<DimensionDataItemDto> itemResult = processAllDimension(dimensionItemDtoList, item.getNormData(), dimensionCache, i);
            if (CollUtil.isNotEmpty(itemResult)) {
                item.setChildDimension(itemResult);
            }
        }
        return tempResult;
    }

    /**
     * 处理单个维度展示
     * @param dimensionItemDto
     * @param rowDataList
     * @return
     */
    private List<DimensionDataItemDto> processDimension(DimensionItemDto dimensionItemDto,
                                                        List<JSONObject> rowDataList,
                                                        Map<String, Map<Long, DimensionTreeItemDto>> dimensionCache) {
        DimensionDisplayTypeEnum displayTypeEnum = DimensionDisplayTypeEnum.getByCode(dimensionItemDto.getDisplayType());
        switch (displayTypeEnum) {
            case TREE:
                return processTreeDisplayType(dimensionItemDto, rowDataList, dimensionCache);
            case DATE:
                return processDateDisplay(dimensionItemDto, rowDataList);
            case NUMBER:
                return processNumberDisplay(dimensionItemDto, rowDataList);
            case PERCENT:
                return processPercentDisplay(dimensionItemDto, rowDataList);
            case OTHER:
                return processOtherDisplay(dimensionItemDto, rowDataList);
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "维度数据展示类型错误");
        }
    }

    /**
     * 处理树形展示的
     * @param dimensionItemDto
     * @param rowDataList
     * @return
     */
    private List<DimensionDataItemDto> processTreeDisplayType(DimensionItemDto dimensionItemDto,
                                                              List<JSONObject> rowDataList,
                                                              Map<String, Map<Long, DimensionTreeItemDto>> dimensionCache) {
        Map<String, DimensionDataItemDto> result = new HashMap<>();
        //如果是组织类型的
        Map<Long, DimensionTreeItemDto>  treeGroupMap = null;
        String cacheKey = dimensionItemDto.getType() + "_" + Convert.toStr(LoginUserThreadLocal.getCompanyId()) + "_" + Convert.toStr(dimensionItemDto.getGroupCondition());
        if (dimensionCache.containsKey(cacheKey)) {
            treeGroupMap = dimensionCache.get(cacheKey);
        } else {
            if (dimensionItemDto.getType().equalsIgnoreCase(FormFieldCO.YZC_ORG)) {
                //查询资产树形组织
                treeGroupMap = treeDimensionService.queryOrgGroup(LoginUserThreadLocal.getCompanyId(), (Integer) dimensionItemDto.getGroupCondition());
            } else if (dimensionItemDto.getType().equalsIgnoreCase(FormFieldCO.YZC_AREA)) {
                //查询区域树形组织
                treeGroupMap = treeDimensionService.queryAreaGroup(LoginUserThreadLocal.getCompanyId(), (Integer) dimensionItemDto.getGroupCondition());
            } else if (dimensionItemDto.getType().equalsIgnoreCase(FormFieldCO.YZC_ASSET_CATE)) {
                //查询分类树形组织
                treeGroupMap = treeDimensionService.queryCategoryGroup(LoginUserThreadLocal.getCompanyId(), (Integer) dimensionItemDto.getGroupCondition());
            } else if (dimensionItemDto.getType().equalsIgnoreCase(FormFieldCO.YZC_MATERIAL_CATE)) {
                //查询耗材分类树形组织
                treeGroupMap = treeDimensionService.queryMaterialCategoryGroup(LoginUserThreadLocal.getCompanyId(), (Integer) dimensionItemDto.getGroupCondition());
            }
            dimensionCache.put(cacheKey, treeGroupMap);
        }

        if (CollUtil.isEmpty(treeGroupMap)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "维度数据不存在");
        }

        for (JSONObject item : rowDataList) {
            Long dimensionValue = item.getLong(dimensionItemDto.getCode());
            if (Objects.isNull(dimensionValue)) {
                DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto().setName(ReportConstant.EMPTY_DESC);
                if (result.containsKey(ReportConstant.DEFAULT_ZERO)) {
                    result.get(ReportConstant.DEFAULT_ZERO).getNormData().add(item);
                } else {
                    List<JSONObject> normData = new ArrayList<>();
                    normData.add(item);
                    dimensionDataItemDto.setNormData(normData);
                    result.put(ReportConstant.DEFAULT_ZERO, dimensionDataItemDto);
                }
            } else {
                for (Long key : treeGroupMap.keySet()) {
                    String keyStr = String.valueOf(key);
                    if (treeGroupMap.get(key).getChild().contains(dimensionValue)) {
                        //维度值名称，组织名称或分类名称
                        DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto()
                                .setName(treeGroupMap.get(key).getName());
                        if (result.containsKey(keyStr)) {
                            result.get(keyStr).getNormData().add(item);
                        } else {
                            List<JSONObject> normData = new ArrayList<>();
                            normData.add(item);
                            dimensionDataItemDto.setNormData(normData);
                            result.put(keyStr, dimensionDataItemDto);
                        }
                    }
                }
            }
        }
        return new ArrayList<>(result.values());
    }

    /**
     * 处理日期类型的展示，日期在生成group by语句的时候，就已经提前处理了，所以这里做分组的时候，不需要处理
     * @param dimensionItemDto
     * @param rowDataList
     * @return
     */
    private List<DimensionDataItemDto> processDateDisplay(DimensionItemDto dimensionItemDto, List<JSONObject> rowDataList) {
        Map<String, DimensionDataItemDto> result = new LinkedHashMap<>();
        for (JSONObject item : rowDataList) {
            String dimensionValue = item.getString(dimensionItemDto.getCode());
            //特殊处理下时间为空，以及时间为0的情况
            if (StrUtil.isBlank(dimensionValue) || dimensionValue.startsWith("1970")) {
                DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto().setName(ReportConstant.EMPTY_DESC);
                if (result.containsKey(ReportConstant.DEFAULT_ZERO)) {
                    result.get(ReportConstant.DEFAULT_ZERO).getNormData().add(item);
                } else {
                    List<JSONObject> normData = new ArrayList<>();
                    normData.add(item);
                    dimensionDataItemDto.setNormData(normData);
                    result.put(ReportConstant.DEFAULT_ZERO, dimensionDataItemDto);
                }
            } else {
                DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto()
                        .setName(dimensionValue);
                if (result.containsKey(item.getString(dimensionItemDto.getCode()))) {
                    result.get(dimensionValue).getNormData().add(item);
                } else {
                    List<JSONObject> normData = new ArrayList<>();
                    normData.add(item);
                    dimensionDataItemDto.setNormData(normData);
                    result.put(dimensionValue, dimensionDataItemDto);
                }
            }
        }
        return new ArrayList<>(result.values());
    }

    /**
     * 处理数值类型的
     * @param dimensionItemDto
     * @param rowDataList
     * @return
     */
    private List<DimensionDataItemDto> processNumberDisplay(DimensionItemDto dimensionItemDto, List<JSONObject> rowDataList) {
        Map<Long, DimensionDataItemDto> result = new TreeMap<>();
        List<DimensionGroupNumDto> dimensionGroupNumDtoList = JSONObject.parseArray(JSONObject.toJSONString(dimensionItemDto.getGroupCondition()),
                DimensionGroupNumDto.class);
        Map<Long, String> dimensionGroupNameMap = getDimensionGroupNameMap(dimensionGroupNumDtoList);
        for (JSONObject item : rowDataList) {
            Double dimensionValue = item.getDouble(dimensionItemDto.getCode());
            if (Objects.isNull(dimensionValue)) {
                dimensionValue = 0.0;
            }

            Double finalDimensionValue = dimensionValue;
            Optional<DimensionGroupNumDto> groupOptional = dimensionGroupNumDtoList.stream()
                    .filter(groupCondition -> finalDimensionValue >= groupCondition.getMinValue() && finalDimensionValue < groupCondition.getMaxValue())
                    .findAny();
            //不在这个分组就不放到维度里面来
            if (!groupOptional.isPresent()) {
                continue;
            }
            DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto()
                    .setName(dimensionGroupNameMap.get(groupOptional.get().getMinValue()));
            if (result.containsKey(groupOptional.get().getMinValue())) {
                result.get(groupOptional.get().getMinValue()).getNormData().add(item);
            } else {
                List<JSONObject> normData = new ArrayList<>();
                normData.add(item);
                dimensionDataItemDto.setNormData(normData);
                result.put(groupOptional.get().getMinValue(), dimensionDataItemDto);
            }
        }
        return new ArrayList<>(result.values());
    }

    Map<Long, String> getDimensionGroupNameMap(List<DimensionGroupNumDto> dimensionGroupNumDtoList) {
        Map<Long, String> result = new HashMap<>();
        if (CollUtil.isEmpty(dimensionGroupNumDtoList)) {
            return result;
        }

        List<DimensionGroupNumDto> sortDimensionGroupList = dimensionGroupNumDtoList
                .stream().sorted(Comparator.comparing(DimensionGroupNumDto::getMinValue))
                .collect(Collectors.toList());
        for (int i = 0; i < sortDimensionGroupList.size(); i++) {
            if (i==0) {
                result.put(sortDimensionGroupList.get(i).getMinValue(),
                        String.join(" ", "小于", sortDimensionGroupList.get(i).getMaxValue().toString()));
            } else if (i == sortDimensionGroupList.size() - 1) {
                result.put(sortDimensionGroupList.get(i).getMinValue(),
                        String.join(" ", "大于等于", sortDimensionGroupList.get(i).getMinValue().toString()));
            } else {
                result.put(sortDimensionGroupList.get(i).getMinValue(),
                        String.join(" - ", "大于等于" + sortDimensionGroupList.get(i).getMinValue().toString(), "小于" + sortDimensionGroupList.get(i).getMaxValue().toString()));
            }
        }
        return result;
    }

    /**
     * 处理百分比类型的
     * @param dimensionItemDto
     * @param rowDataList
     * @return
     */
    private List<DimensionDataItemDto> processPercentDisplay(DimensionItemDto dimensionItemDto, List<JSONObject> rowDataList) {
        return processNumberDisplay(dimensionItemDto, rowDataList);
    }

    /**
     * 处理其他展示类型
     * @param dimensionItemDto
     * @param rowDataList
     * @return
     */
    private List<DimensionDataItemDto> processOtherDisplay(DimensionItemDto dimensionItemDto, List<JSONObject> rowDataList) {
        Map<String, DimensionDataItemDto> result = new HashMap<>();
        if (QueryFieldConstant.ASSET_FIELD_STATUS.equalsIgnoreCase(dimensionItemDto.getCode())) {
            List<AssetStatusDto> allAssetStatus = assetReportMapper.selectAllStatus();
            Map<Integer, String> assetStatusMap = allAssetStatus.stream().collect(Collectors.toMap(AssetStatusDto::getId, AssetStatusDto::getName));
            for (JSONObject item : rowDataList) {
                Integer status = item.getInteger(dimensionItemDto.getCode());
                String statusStr = String.valueOf(status);

                //状态为空的处理
                DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto();
                if (Objects.isNull(assetStatusMap.get(status))) {
                    dimensionDataItemDto.setName(ReportConstant.EMPTY_DESC);
                } else {
                    dimensionDataItemDto.setName(assetStatusMap.get(status));
                }

                if (result.containsKey(statusStr)) {
                    result.get(statusStr).getNormData().add(item);
                } else {
                    List<JSONObject> normData = new ArrayList<>();
                    normData.add(item);
                    dimensionDataItemDto.setNormData(normData);
                    result.put(statusStr, dimensionDataItemDto);
                }
            }
        } else {
            for (JSONObject item : rowDataList) {
                String dimensionValue = item.getString(dimensionItemDto.getCode());
                if (StrUtil.isBlank(dimensionValue)) {
                    DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto().setName(ReportConstant.EMPTY_DESC);
                    if (result.containsKey(ReportConstant.DEFAULT_ZERO)) {
                        result.get(ReportConstant.DEFAULT_ZERO).getNormData().add(item);
                    } else {
                        List<JSONObject> normData = new ArrayList<>();
                        normData.add(item);
                        dimensionDataItemDto.setNormData(normData);
                        result.put(ReportConstant.DEFAULT_ZERO, dimensionDataItemDto);
                    }
                } else {
                    DimensionDataItemDto dimensionDataItemDto = new DimensionDataItemDto()
                            .setName(assetReportUtil.translateSpecificField(dimensionValue, dimensionItemDto));
                    if (result.containsKey(item.getString(dimensionItemDto.getCode()))) {
                        result.get(dimensionValue).getNormData().add(item);
                    } else {
                        List<JSONObject> normData = new ArrayList<>();
                        normData.add(item);
                        dimensionDataItemDto.setNormData(normData);
                        result.put(dimensionValue, dimensionDataItemDto);
                    }
                }
            }
        }
        return new ArrayList<>(result.values());
    }
}
