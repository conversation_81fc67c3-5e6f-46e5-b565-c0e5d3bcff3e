package com.niimbot.asset.report.service;

import com.niimbot.report.DynamicFieldQueryDto;
import com.niimbot.system.QueryConditionDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/7 下午2:09
 */
public interface DynamicFieldService {

    /**
     * 查询报表动态字段
     * @param queryDto
     * @return
     */
    List<QueryConditionDto> queryField(DynamicFieldQueryDto queryDto);

    /**
     * 耗材档案字段
     * @return
     */
    List<QueryConditionDto> materialField();

    /**
     * 资产固定维度
     * @return
     */
    Map<String, QueryConditionDto> assetFixedDimension();

    /**
     * 资产固定指标
     * @return
     */
    Map<String, QueryConditionDto> assetFixedNorm();

    /**
     * 返回所有耗材字段
     * @return
     */
    List<String> materialFieldCode();

    /**
     * 耗材单据表单动态字段
     * @param orderType
     * @return
     */
    List<String> materialOrderFieldCode(Integer orderType);

    /**
     * 是否属于耗材单据表单动态字段
     * @param code
     * @param orderType
     * @return
     */
    Boolean isMaterialOrderFieldCode(String code, Integer orderType);
}
