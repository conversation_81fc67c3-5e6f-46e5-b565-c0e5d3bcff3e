package com.niimbot.asset.report.enums;

import lombok.Getter;

/**
 * 维度分组类型枚举
 * <AUTHOR>
 * @date 2023/6/12 上午10:17
 */
@Getter
public enum DimensionDisplayTypeEnum {

    UN_KNOW(-1, "未知"),

    TREE(1, "树形"),
    DATE(2, "日期"),
    NUMBER(3, "数字"),
    PERCENT(4, "百分比"),
    OTHER(5, "其他"),
    MATERIAL(6, "耗材档案"),
    ;

    DimensionDisplayTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public static DimensionDisplayTypeEnum getByCode(Integer code) {
        for (DimensionDisplayTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return DimensionDisplayTypeEnum.UN_KNOW;
    }
}
