package com.niimbot.asset.report.component;

import com.google.common.collect.ImmutableMap;

import com.niimbot.asset.framework.query.AbsQueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.system.QueryConditionDto;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.StringJoiner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/7/12 上午10:28
 */
@Component
public class MaterialReportOrderQueryConditionResolver extends AbsQueryConditionResolver<String, QueryConditionDto> {
    public static final String SQL_SEGMENT_TPL = "%s.%s";
    public static final String JSON_SQL_SEGMENT_TPL = "%s.order_data ->> '$.%s'";
    public static final String NUM_JSON_SQL_SEGMENT_TPL = "cast(%s.order_data ->> '$.%s' as DECIMAL(20,4))";
    public static final String DATE_JSON_SQL_SEGMENT_TPL = "LPAD(%s.order_data ->> '$.%s', 13, 0)";
    public static final String DETAIL_JSON_SQL_SEGMENT_TPL = "%s.material_snapshot_data ->> '$.%s'";

    private ImmutableMap<String, QueryFieldConstant.Field> MATERIAL_STORAGE_FIXED_QUERY = new ImmutableMap.Builder<String, QueryFieldConstant.Field>()
            .put(ReportAssetConstant.STORAGE_NUM, new QueryFieldConstant.Field(ReportAssetConstant.STORAGE_NUM, "入库数量", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.STORAGE_PRICE, new QueryFieldConstant.Field(ReportAssetConstant.STORAGE_PRICE, "入库金额", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.OUT_NUM, new QueryFieldConstant.Field(ReportAssetConstant.OUT_NUM, "出库数量", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.OUT_PRICE, new QueryFieldConstant.Field(ReportAssetConstant.OUT_PRICE, "出库金额", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.RECEIPT_NUM, new QueryFieldConstant.Field(ReportAssetConstant.RECEIPT_NUM, "申请数量", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.GRANT_NUM, new QueryFieldConstant.Field(ReportAssetConstant.GRANT_NUM, "已发放数量", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.GRANTING_NUM, new QueryFieldConstant.Field(ReportAssetConstant.GRANTING_NUM, "发放中数量", FormFieldCO.NUMBER_INPUT, ""))
            .put(ReportAssetConstant.WAIT_GRANT_NUM, new QueryFieldConstant.Field(ReportAssetConstant.WAIT_GRANT_NUM, "待发放数量", FormFieldCO.NUMBER_INPUT, ""))
            .putAll(QueryFieldConstant.ORDER_COMMON_EXT_FIELD)
            .build();

    @Override
    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        MATERIAL_STORAGE_FIXED_QUERY);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    public String resolveQueryCondition(String tableAlias, List<QueryConditionDto> conditions, List<String> materialOrderAllField) {
        StringJoiner joiner = new StringJoiner(" ");
        // 自定义查询条件解析
        if (CollUtil.isNotEmpty(conditions)) {
            for (QueryConditionDto condition : conditions) {
                String sqlField = getSqlField(tableAlias, condition, materialOrderAllField);
                String conditionStr = super.doResolveQueryCondition(sqlField, condition.getQuery(), condition.getQueryData(),
                        condition.getCode(), condition.getType(),
                        MATERIAL_STORAGE_FIXED_QUERY);
                if (StrUtil.isNotBlank(conditionStr)) {
                    joiner.add(conditionStr);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : null;
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition) {
        if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(condition.getCode())
                && !QueryFieldConstant.FIELD_TK_IN_REPO.equalsIgnoreCase(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        } else if (ReportAssetConstant.STORAGE_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "rk_num");
        } else if (ReportAssetConstant.STORAGE_PRICE.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "rk_price");
        } else if (ReportAssetConstant.OUT_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "ck_num");
        } else if (ReportAssetConstant.OUT_PRICE.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "ck_price");
        } else if (ReportAssetConstant.RECEIPT_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "ly_num");
        } else if (ReportAssetConstant.GRANT_NUM.equalsIgnoreCase(condition.getCode())
                || ReportAssetConstant.GRANTING_NUM.equalsIgnoreCase(condition.getCode())
                || ReportAssetConstant.WAIT_GRANT_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        }
        return getJsonSqlField(tableAlias, condition.getCode());
    }

    private String getSqlField(String tableAlias, QueryConditionDto condition, List<String> materialOrderAllField) {
        //固定字段的时候，不需要走orderData json字段筛选查询
        if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(condition.getCode())
                && !QueryFieldConstant.FIELD_TK_IN_REPO.equalsIgnoreCase(condition.getCode())) {
            if (FormFieldCO.DATETIME.equals(condition.getType())) {
                return unixTimestampField(String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode())));
            }
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        } else if (ReportAssetConstant.STORAGE_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "rk_num");
        } else if (ReportAssetConstant.STORAGE_PRICE.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "rk_price");
        } else if (ReportAssetConstant.OUT_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "ck_num");
        } else if (ReportAssetConstant.OUT_PRICE.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "ck_price");
        } else if (ReportAssetConstant.RECEIPT_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, "ly_num");
        } else if (ReportAssetConstant.GRANT_NUM.equalsIgnoreCase(condition.getCode())
                || ReportAssetConstant.GRANTING_NUM.equalsIgnoreCase(condition.getCode())
                || ReportAssetConstant.WAIT_GRANT_NUM.equalsIgnoreCase(condition.getCode())) {
            return String.format(SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(condition.getCode()));
        } else if (materialOrderAllField.contains(condition.getCode())) {
            return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, condition.getCode());
        } else {
            return String.format(DETAIL_JSON_SQL_SEGMENT_TPL, tableAlias, condition.getCode());
        }
    }

    private String getJsonSqlField(String tableAlias, String code) {
        return String.format(JSON_SQL_SEGMENT_TPL, tableAlias, code);
    }

    private String unixTimestampField(String name) {
        return String.format("unix_timestamp(%s) * 1000", name);
    }

}
