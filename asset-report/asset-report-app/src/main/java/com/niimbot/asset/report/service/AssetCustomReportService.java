package com.niimbot.asset.report.service;

import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;

/**
 * 资产自定义报表
 * <AUTHOR>
 * @date 2023/6/30 上午10:34
 */
public interface AssetCustomReportService {

    /**
     * 库存报表数据
     * @param customReportConfigDto
     * @return
     */
    DimensionDataDto reportData(CustomReportConfigDto customReportConfigDto);

    /**
     * 库存明细报表数据
     * @param customReportConfigDto
     * @return
     */
    AssetStatisticsDataDto statisticsReportData(CustomReportConfigDto customReportConfigDto);
}
