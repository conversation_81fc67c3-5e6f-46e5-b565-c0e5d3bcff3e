package com.niimbot.asset.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.QueryConditionType;
import com.niimbot.asset.report.component.AssetReportUtil;
import com.niimbot.asset.report.service.StatisticsConditionService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.report.StatisticsConditionDescDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/4 下午3:29
 */
@Slf4j
@Service
public class StatisticsConditionServiceImpl implements StatisticsConditionService {

    @Autowired
    private AssetReportUtil assetReportUtil;

    @Override
    public List<StatisticsConditionDescDto> resolveDesc(List<QueryConditionDto> statisticsCondition) {
        List<StatisticsConditionDescDto> result = new ArrayList<>();
        if (CollUtil.isEmpty(statisticsCondition)) {
            return result;
        }

        for (QueryConditionDto item : statisticsCondition) {
            QueryConditionType queryTypeEnum = QueryConditionType.getEnum(item.getQuery());
            if (Objects.isNull(queryTypeEnum)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "统计范围查询类型错误");
            }

            result.add(resolveSpecificCondition(item, queryTypeEnum));
        }
        return result;
    }

    private StatisticsConditionDescDto resolveSpecificCondition(QueryConditionDto conditionDto, QueryConditionType queryTypeEnum) {
        StatisticsConditionDescDto result = new StatisticsConditionDescDto();
        BeanUtils.copyProperties(conditionDto, result);
        result.setQueryText(queryTypeEnum.getDesc());
        if (Objects.isNull(conditionDto.getQueryData())) {
            return result;
        }

        if (AssetConstant.ED_DATETIME.equalsIgnoreCase(conditionDto.getType())) {
            if (QueryConditionType.LAST_DAYS.equals(queryTypeEnum)) {
                result.setQueryDataText(String.join(" ", "最近", Convert.toStr(conditionDto.getQueryData()), "天"));
            } else {
                if (conditionDto.getQueryData() instanceof List) {
                    List<String> timeCondition = JSONObject.parseArray(JSONObject.toJSONString(conditionDto.getQueryData()), String.class);
                    result.setQueryDataText(String.join(" - ", timeCondition));
                } else {
                    result.setQueryDataText(Convert.toStr(conditionDto.getQueryData()));
                }
            }
        } else {
            if (conditionDto.getQueryData() instanceof List) {
                List<?> queryData = (List<?>) conditionDto.getQueryData();
                StringJoiner queryDataText = new StringJoiner(" ,");
                if (queryTypeEnum.equals(QueryConditionType.BETWEEN)) {
                    queryDataText = new StringJoiner(" - ");
                }
                for (Object item : queryData) {
                    queryDataText.add(assetReportUtil.translateStatisticsCondition(Convert.toStr(item), conditionDto));
                }
                result.setQueryDataText(queryDataText.toString());
            } else {
                result.setQueryDataText(assetReportUtil.translateStatisticsCondition(Convert.toStr(conditionDto.getQueryData()), conditionDto));
            }
        }
        return result;
    }
}
