package com.niimbot.asset.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.report.AssetLogReportDto;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.HandleAssetLogDto;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.MaterialCategoryReportItem;
import com.niimbot.report.MaterialCategoryReportSearch;
import com.niimbot.report.MaterialRepositoryReportItem;
import com.niimbot.report.MaterialRepositoryReportSearch;
import com.niimbot.report.MeansCategoryReportItem;
import com.niimbot.report.MeansCategoryReportSearch;
import com.niimbot.report.MeansUseOrgReportItem;
import com.niimbot.report.RepairAssetLogDto;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.UseOrgGroupReportSearch;
import com.niimbot.report.WaitHandleAssetLogDto;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface PredefinedReportMapper {

    List<MeansUseOrgReportItem> selectMeansUseOrgGroupReport(@Param("em") UseOrgGroupReportSearch search, @Param("perms") String perms);

    List<MeansCategoryReportItem> selectMeansCategoryGroupReport(@Param("em") MeansCategoryReportSearch search, @Param("perms") String perms);

    List<MaterialCategoryReportItem> selectMaterialCategoryGroupOpeningAndEndingReport(@Param("em") MaterialCategoryReportSearch search,
                                                                                       @Param("storeSql") String storeSql);

    List<MaterialCategoryReportItem> selectMaterialCategoryGroupProcessReport(@Param("em") MaterialCategoryReportSearch search,
                                                                              @Param("storeSql") String storeSql);

    IPage<MaterialRepositoryReportItem> selectMaterialRepositoryGroupReport
            (
                    IPage<MaterialRepositoryReportSearch> page,
                    @Param("em") MaterialRepositoryReportSearch search,
                    @Param("condition") String condition,
                    @Param("storeSql") String storeSql
            );

    List<MaterialRepositoryReportItem> selectMaterialRepositoryGroupReport
            (
                    @Param("em") MaterialRepositoryReportSearch search,
                    @Param("condition") String condition,
                    @Param("storeSql") String storeSql
            );

    IPage<AssetLogReportDto> assetLogReport(Page<Object> page,
                                            @Param("ew") AssetLogReportQueryDto queryDto,
                                            @Param("companyId") Long companyId,
                                            @Param("conditions") String conditions);

    IPage<WaitHandleAssetLogDto> waitHandleAssetLog(Page<Object> page,
                                                    @Param("ew") WaitHandleAssetLogQueryDto queryDto,
                                                    @Param("companyId") Long companyId,
                                                    @Param("conditions") String conditions);

    IPage<HandleAssetLogDto> handleAssetLog(Page<Object> page,
                                            @Param("ew") HandleAssetLogQueryDto queryDto,
                                            @Param("companyId") Long companyId,
                                            @Param("conditions") String conditions);

    IPage<WaitReturnAssetLogDto> waitReturnAssetLog(Page<Object> page,
                                                    @Param("ew") WaitReturnAssetLogQueryDto queryDto,
                                                    @Param("companyId") Long companyId,
                                                    @Param("conditions") String conditions);

    IPage<RepairAssetLogDto> repairAssetLog(Page<Object> page,
                                            @Param("ew") RepairAssetLogQueryDto queryDto,
                                            @Param("companyId") Long companyId,
                                            @Param("conditions") String conditions);

    IPage<RepairAssetRecordDto> repairAssetRecord(Page<Object> page,
                                                  @Param("ew") RepairAssetRecordQueryDto queryDto,
                                                  @Param("companyId") Long companyId,
                                                  @Param("conditions") String conditions);

    List<RepairAssetRecordDto> repairReportAssetRecord(@Param("assetIds") Set<Long> assetIds, @Param("companyId") Long companyId);
}