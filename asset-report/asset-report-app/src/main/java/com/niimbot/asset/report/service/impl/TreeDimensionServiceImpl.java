package com.niimbot.asset.report.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.material.model.AsMaterialCategory;
import com.niimbot.asset.material.service.AsMaterialCategoryService;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.asset.report.enums.DimensionTreeDisplayTypeEnum;
import com.niimbot.asset.report.service.TreeDimensionService;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.report.DimensionTreeItemDto;

import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/12 上午10:57
 */
@Slf4j
@Service
public class TreeDimensionServiceImpl implements TreeDimensionService {

    @Autowired
    private OrgService orgService;
    @Autowired
    private CategoryService categoryService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private AsMaterialCategoryService materialCategoryService;

    @Override
    public Map<Long, DimensionTreeItemDto> queryOrgGroup(Long companyId, Integer type) {
        //查询当前企业所有有效组织
        List<AsOrg> orgList = orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                .select(AsOrg::getId, AsOrg::getOrgName, AsOrg::getPid, AsOrg::getPaths, AsOrg::getOrgType)
                .eq(AsOrg::getCompanyId, companyId).eq(AsOrg::getIsDelete, 0));

        //组织名称进行分组，后续会用上名称
        Map<Long, String> nameGroup = orgList.stream().collect(Collectors.toMap(AsOrg::getId, AsOrg::getOrgName, (v1, v2) -> v2));

        //获取树状类型
        DimensionTreeDisplayTypeEnum treeType = DimensionTreeDisplayTypeEnum.getByCode(type);
        switch (treeType) {
            case ALL_CONTAIN_CHILD:
                return allOrgAndChild(orgList, nameGroup);
            case ALL:
                return orgList.stream().collect(Collectors.toMap(AsOrg::getId,
                        value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())),
                        (v1, v2) -> v1));
            case ONLY_FIRST_LEVEL:
                return orgOnlyFirstLevel(orgList, nameGroup);
            case ONLY_COMPANY:
                return onlyCompany(orgList, nameGroup);
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "组织维度分组类型错误");
        }
    }

    /**
     * 存放所有组织，包含根节点(顶级企业)
     * @param orgList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> allOrgAndChild(List<AsOrg> orgList, Map<Long, String> nameGroup) {
        Map<Long, DimensionTreeItemDto> result = new HashMap<>();

        for (AsOrg item : orgList) {

            //根据path获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());

            for (Long parentOrgId : parentOrgIdList) {
                //path中为0的不放入，过滤掉
                if (parentOrgId == 0) {
                    continue;
                }

                //父级一个个放入到Map中，就是把自己添加到所有的父节点中
                putItem(parentOrgId, item.getId(), nameGroup.get(parentOrgId), result);
            }

            //把自己放入到Map中
            putItem(item.getId(), item.getId(), item.getOrgName(), result);
        }
        return result;
    }

    /**
     * 仅一级部门和企业，不包含根节点(顶级企业)
     * @param orgList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> orgOnlyFirstLevel(List<AsOrg> orgList, Map<Long, String> nameGroup) {
        //获取根节点企业
        Optional<AsOrg> companyOptional = orgList.stream().filter(item -> item.getPid().equals(0L)).findAny();

        //找出所有的一级部门和企业
        Map<Long, DimensionTreeItemDto> result = orgList.stream()
                .filter(item -> item.getPid().equals(companyOptional.get().getId()))
                .collect(Collectors.toMap(AsOrg::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        for (AsOrg item : orgList) {
            //过滤根节点
            if (item.getPid().equals(0L)) {
                continue;
            }

            //获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
            for (Long parentOrgId : parentOrgIdList) {
                //属于一级部门的子节点，就添加进来
                if (result.containsKey(parentOrgId)) {
                    result.get(parentOrgId).getChild().add(item.getId());
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 仅公司，包含根节点(顶级企业)
     * @param orgList
     * @param nameGroup
     * @return
     */
    private Map<Long, DimensionTreeItemDto> onlyCompany(List<AsOrg> orgList, Map<Long, String> nameGroup) {
        //找出所有的企业
        Map<Long, DimensionTreeItemDto> result = orgList.stream()
                .filter(item -> item.getOrgType() == 1)
                .collect(Collectors.toMap(AsOrg::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        for (AsOrg item : orgList) {
            //获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
            for (Long parentOrgId : parentOrgIdList) {
                if (result.containsKey(parentOrgId)) {
                    result.get(parentOrgId).getChild().add(item.getId());
                }
            }
        }
        return result;
    }

    private void putItem(Long key, Long value, String name, Map<Long, DimensionTreeItemDto> result) {
        if (result.containsKey(key)) {
            result.get(key).getChild().add(value);
        } else {
            DimensionTreeItemDto dimensionTreeItemDto = new DimensionTreeItemDto().setName(name).setChild(SetUtils.hashSet(value));
            result.put(key, dimensionTreeItemDto);
        }
    }

    @Override
    public Map<Long, DimensionTreeItemDto> queryCategoryGroup(Long companyId, Integer type) {
        //获取树状类型
        DimensionTreeDisplayTypeEnum treeType = DimensionTreeDisplayTypeEnum.getByCode(type);

        //查询分类信息
        List<AsCategory> categoryList = categoryService.list(Wrappers.lambdaQuery(AsCategory.class)
                .select(AsCategory::getId, AsCategory::getCategoryName, AsCategory::getPid, AsCategory::getPaths)
                .eq(AsCategory::getCompanyId, companyId).eq(AsCategory::getIsDelete, 0));

        //组织名称进行分组，后续会用上名称
        Map<Long, String> nameGroup = categoryList.stream().collect(Collectors.toMap(AsCategory::getId, AsCategory::getCategoryName, (v1, v2) -> v2));
        switch (treeType) {
            case ALL_CONTAIN_CHILD:
                return allCategoryAndChild(categoryList, nameGroup);
            case ALL:
                return categoryList.stream().collect(Collectors.toMap(AsCategory::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
            case ONLY_FIRST_LEVEL:
                return categoryOnlyFirstLevel(categoryList, nameGroup);
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "组织维度分组类型错误");
        }
    }

    /**
     * 查询所有分类
     * @param categoryList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> allCategoryAndChild(List<AsCategory> categoryList, Map<Long, String> nameGroup) {
        Map<Long, DimensionTreeItemDto> result = new HashMap<>();
        for (AsCategory item : categoryList) {

            //根据path获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());

            for (Long parentOrgId : parentOrgIdList) {
                //path中为0的不放入，过滤掉
                if (parentOrgId == 0) {
                    continue;
                }

                //父级一个个放入到Map中，就是把自己添加到所有的父节点中
                putItem(parentOrgId, item.getId(), nameGroup.get(parentOrgId), result);
            }

            //把自己放入到Map中
            putItem(item.getId(), item.getId(), nameGroup.get(item.getId()), result);
        }
        return result;
    }

    /**
     * 仅一级分类
     * @param categoryList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> categoryOnlyFirstLevel(List<AsCategory> categoryList, Map<Long, String> nameGroup) {
        //找出所有的一级分类
        Map<Long, DimensionTreeItemDto> result = categoryList.stream()
                .filter(item -> item.getPid().equals(0L))
                .collect(Collectors.toMap(AsCategory::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        for (AsCategory item : categoryList) {
            //过滤根节点
            if (item.getPid().equals(0L)) {
                continue;
            }

            //获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
            for (Long parentOrgId : parentOrgIdList) {
                //属于一级分类的子节点，就添加进来
                if (result.containsKey(parentOrgId)) {
                    result.get(parentOrgId).getChild().add(item.getId());
                    break;
                }
            }
        }
        return result;
    }

    @Override
    public Map<Long, DimensionTreeItemDto> queryAreaGroup(Long companyId, Integer type) {
        //获取树状类型
        DimensionTreeDisplayTypeEnum treeType = DimensionTreeDisplayTypeEnum.getByCode(type);

        //查询分类信息
        List<AsArea> areaList = areaService.list(Wrappers.lambdaQuery(AsArea.class)
                .select(AsArea::getId, AsArea::getAreaName, AsArea::getPid, AsArea::getPaths)
                .eq(AsArea::getCompanyId, companyId).eq(AsArea::getIsDelete, 0));

        //组织名称进行分组，后续会用上名称
        Map<Long, String> nameGroup = areaList.stream().collect(Collectors.toMap(AsArea::getId, AsArea::getAreaName, (v1, v2) -> v2));
        switch (treeType) {
            case ALL_CONTAIN_CHILD:
                return allAreaAndChild(areaList, nameGroup);
            case ALL:
                return areaList.stream().collect(Collectors.toMap(AsArea::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
            case ONLY_FIRST_LEVEL:
                return areaOnlyFirstLevel(areaList, nameGroup);
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "组织维度分组类型错误");
        }
    }

    @Override
    public Map<Long, DimensionTreeItemDto> queryMaterialCategoryGroup(Long companyId, Integer type) {
        //获取树状类型
        DimensionTreeDisplayTypeEnum treeType = DimensionTreeDisplayTypeEnum.getByCode(type);

        //查询分类信息
        List<AsMaterialCategory>  categoryList = materialCategoryService.list(Wrappers.lambdaQuery(AsMaterialCategory.class)
                .select(AsMaterialCategory::getId, AsMaterialCategory::getCategoryName, AsMaterialCategory::getPid, AsMaterialCategory::getPaths)
                .eq(AsMaterialCategory::getCompanyId, companyId).eq(AsMaterialCategory::getIsDelete, 0));

        //组织名称进行分组，后续会用上名称
        Map<Long, String> nameGroup = categoryList.stream().collect(Collectors.toMap(AsMaterialCategory::getId, AsMaterialCategory::getCategoryName, (v1, v2) -> v2));
        switch (treeType) {
            case ALL_CONTAIN_CHILD:
                return allMaterialCategoryAndChild(categoryList, nameGroup);
            case ALL:
                return categoryList.stream().collect(Collectors.toMap(AsMaterialCategory::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
            case ONLY_FIRST_LEVEL:
                return materialCategoryOnlyFirstLevel(categoryList, nameGroup);
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "组织维度分组类型错误");
        }
    }

    /**
     * 查询所有分类
     * @param categoryList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> allMaterialCategoryAndChild(List<AsMaterialCategory> categoryList, Map<Long, String> nameGroup) {
        Map<Long, DimensionTreeItemDto> result = new HashMap<>();
        for (AsMaterialCategory item : categoryList) {

            //根据path获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());

            for (Long parentOrgId : parentOrgIdList) {
                //path中为0的不放入，过滤掉
                if (parentOrgId == 0) {
                    continue;
                }

                //父级一个个放入到Map中，就是把自己添加到所有的父节点中
                putItem(parentOrgId, item.getId(), nameGroup.get(parentOrgId), result);
            }

            //把自己放入到Map中
            putItem(item.getId(), item.getId(), nameGroup.get(item.getId()), result);
        }
        return result;
    }

    /**
     * 仅一级分类
     * @param categoryList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> materialCategoryOnlyFirstLevel(List<AsMaterialCategory> categoryList, Map<Long, String> nameGroup) {
        //找出所有的一级分类
        Map<Long, DimensionTreeItemDto> result = categoryList.stream()
                .filter(item -> item.getPid().equals(0L))
                .collect(Collectors.toMap(AsMaterialCategory::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        for (AsMaterialCategory item : categoryList) {
            //过滤根节点
            if (item.getPid().equals(0L)) {
                continue;
            }

            //获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
            for (Long parentOrgId : parentOrgIdList) {
                //属于一级分类的子节点，就添加进来
                if (result.containsKey(parentOrgId)) {
                    result.get(parentOrgId).getChild().add(item.getId());
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 查询所有分组
     * @param areaList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> allAreaAndChild(List<AsArea> areaList, Map<Long, String> nameGroup) {
        Map<Long, DimensionTreeItemDto> result = new HashMap<>();
        for (AsArea item : areaList) {

            //根据path获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());

            for (Long parentOrgId : parentOrgIdList) {
                //path中为0的不放入，过滤掉
                if (parentOrgId == 0) {
                    continue;
                }

                //父级一个个放入到Map中，就是把自己添加到所有的父节点中
                putItem(parentOrgId, item.getId(), nameGroup.get(parentOrgId), result);
            }

            //把自己放入到Map中
            putItem(item.getId(), item.getId(), nameGroup.get(item.getId()), result);
        }
        return result;
    }

    /**
     * 仅一级区域
     * @param areaList
     * @return
     */
    private Map<Long, DimensionTreeItemDto> areaOnlyFirstLevel(List<AsArea> areaList, Map<Long, String> nameGroup) {
        //找出所有的一级分类
        Map<Long, DimensionTreeItemDto> result = areaList.stream()
                .filter(item -> item.getPid().equals(0L))
                .collect(Collectors.toMap(AsArea::getId, value -> new DimensionTreeItemDto().setName(nameGroup.get(value.getId())).setChild(SetUtils.hashSet(value.getId())), (v1, v2) -> v1));
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        for (AsArea item : areaList) {
            //过滤根节点
            if (item.getPid().equals(0L)) {
                continue;
            }

            //获取所有父层级
            List<Long> parentOrgIdList = Arrays.stream(item.getPaths().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
            for (Long parentOrgId : parentOrgIdList) {
                //属于一级分类的子节点，就添加进来
                if (result.containsKey(parentOrgId)) {
                    result.get(parentOrgId).getChild().add(item.getId());
                    break;
                }
            }
        }
        return result;
    }
}
