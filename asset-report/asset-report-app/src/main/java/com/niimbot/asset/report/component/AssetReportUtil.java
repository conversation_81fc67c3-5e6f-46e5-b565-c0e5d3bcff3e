package com.niimbot.asset.report.component;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.asset.report.constants.ReportConstant;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/14 下午2:09
 */
@Slf4j
@Component
public class AssetReportUtil {

    private final static String ASSET_STATUS = "status";
    private final static String ASSET_STATUS_TEXT = "statusText";
    private final static String APPROVE_STATUS = "approveStatus";
    private final static String APPROVE_STATUS_TEXT = "approveStatusText";
    private final static String GRANT_STATUS = "grantStatus";
    private final static String GRANT_STATUS_TEXT = "grantStatusText";

    //耗材单据审批状态
    private ImmutableMap<Integer, String> approveStatusMap = ImmutableMap.<Integer, String>builder()
            .put(0, "已完成")
            .put(1, "审批中")
            .put(2, "已驳回")
            .put(3, "已同意")
            .put(4, "已撤回")
            .build();

    //耗材领用发放状态
    private ImmutableMap<Integer, String> grantStatusMap = ImmutableMap.<Integer, String>builder()
            .put(0, "无")
            .put(1, "待发放")
            .put(2, "发放中")
            .put(3, "部分发放")
            .put(4, "发放完成")
            .build();

    @Autowired
    private CacheResourceUtil cacheResourceUtil;

    @Autowired
    private AssetUtil assetUtil;

    @Autowired
    private AsFormService formService;

    /**
     * 处理资产明细报表展示
     * @param statisticsDataList
     * @param dimension
     */
    public void translateAssetJsonView(List<JSONObject> statisticsDataList, List<DimensionItemDto> dimension) {
        if (CollUtil.isEmpty(statisticsDataList)) {
            return ;
        }

        List<String> dynamicFieldType = ListUtil.of(AssetConstant.ED_YZC_ORG, AssetConstant.ED_YZC_EMP,
                AssetConstant.ED_YZC_AREA, AssetConstant.ED_YZC_ASSET_CATE, AssetConstant.ED_YZC_SUPPLIER,
                AssetConstant.ED_YZC_MATERIAL_CATE, AssetConstant.ED_YZC_REPOSITORY);

        //字段code和类型分组
        Map<String, String> fieldTypeMap = dimension
                .stream()
                .collect(Collectors.toMap(DimensionItemDto::getCode, DimensionItemDto::getType, (v1, v2) -> v2));

        Map<String, Map<Long, String>> cache = new HashMap<>();

        for (JSONObject item : statisticsDataList) {
            JSONObject data = JsonUtil.toJSONObject(item);
            for (DimensionItemDto dimensionItemDto : dimension) {
                try {
                    //状态转换
                    if (ASSET_STATUS.equalsIgnoreCase(dimensionItemDto.getCode())) {
                        //这里特殊处理下，给前端展示用
                        Long assetStatus = item.getLong(ASSET_STATUS);
                        String assetStatusText = StrUtil.EMPTY;
                        Map<Long, String> statusCache = cache.getOrDefault(ASSET_STATUS, new HashMap<>());
                        if (assetStatus != null) {
                            assetStatusText = statusCache.getOrDefault(assetStatus, cacheResourceUtil.getAssetStatusName(item.getLong(ASSET_STATUS)));
                            statusCache.put(assetStatus, assetStatusText);
                        }
                        cache.putIfAbsent(ASSET_STATUS, statusCache);
                        item.put(ASSET_STATUS, item.getLong(ASSET_STATUS));
                        item.put(ASSET_STATUS_TEXT, assetStatusText);
                    } else if (APPROVE_STATUS.equalsIgnoreCase(dimensionItemDto.getCode())) {
                        //这里特殊处理下，给前端展示用
                        item.put(APPROVE_STATUS, item.getInteger(APPROVE_STATUS));
                        item.put(APPROVE_STATUS_TEXT, approveStatusMap.get(item.getInteger(APPROVE_STATUS)));
                    } else if (GRANT_STATUS.equalsIgnoreCase(dimensionItemDto.getCode())) {
                        //这里特殊处理下，给前端展示用
                        item.put(GRANT_STATUS_TEXT, approveStatusMap.get(item.getInteger(GRANT_STATUS)));
                    } else if (AssetConstant.ED_DATETIME.equalsIgnoreCase(dimensionItemDto.getType())) {
                        //特殊处理时间为空的
                        if (StrUtil.isNotBlank(item.getString(dimensionItemDto.getCode()))
                                && item.getString(dimensionItemDto.getCode()).startsWith("1970")) {
                            item.put(dimensionItemDto.getCode(), "");
                        }
                    } else if (QueryFieldConstant.FIELD_CREATE_BY.equalsIgnoreCase(dimensionItemDto.getCode())) {
                        //创建人转换
                        Long userId = item.getLong(QueryFieldConstant.FIELD_CREATE_BY);
                        String userText = StrUtil.EMPTY;
                        Map<Long, String> userCache = cache.getOrDefault(AssetConstant.ED_YZC_EMP, new HashMap<>());
                        if (userId != null) {
                            userText = userCache.getOrDefault(userId, cacheResourceUtil.getUserNameAndCode(item.getLong(QueryFieldConstant.FIELD_CREATE_BY)));
                            userCache.put(userId, userText);
                        }
                        cache.putIfAbsent(AssetConstant.ED_YZC_EMP, userCache);
                        item.put(QueryFieldConstant.FIELD_CREATE_BY, userText);
                    } else if (QueryFieldConstant.ASSET_FILED_STANDARD.equalsIgnoreCase(dimensionItemDto.getCode())) {
                        //标准品转换
                        if (Objects.nonNull(item.getLong(QueryFieldConstant.ASSET_FILED_STANDARD))) {
                            List<FormVO> formVOList = formService.getByFormIds(Collections.singletonList(data.getLong(QueryFieldConstant.ASSET_FILED_STANDARD)));
                            if (CollUtil.isNotEmpty(formVOList)) {
                                item.put(QueryFieldConstant.ASSET_FILED_STANDARD, formVOList.get(0).getFormName());
                            } else {
                                item.put(QueryFieldConstant.ASSET_FILED_STANDARD, "");
                            }
                        } else {
                            item.put(QueryFieldConstant.ASSET_FILED_STANDARD, "");
                        }
                    } else if (AssetConstant.ED_MULTI_SELECT_DROPDOWN.equalsIgnoreCase(dimensionItemDto.getType())) {
                        //下拉多选，需要转成数组
                        item.put(dimensionItemDto.getCode(), JSONObject.parseArray(item.getString(dimensionItemDto.getCode()), String.class));
                    } else if (AssetConstant.ED_IMAGES.equalsIgnoreCase(dimensionItemDto.getType())) {
                        //处理图片，需要转成字符数组
                        item.put(dimensionItemDto.getCode(), JSONObject.parseArray(item.getString(dimensionItemDto.getCode()), String.class));
                    } else if (dynamicFieldType.contains(dimensionItemDto.getType())){
                        //组织等数据转换
                        String type = fieldTypeMap.get(dimensionItemDto.getCode());
                        //这里特殊处理下，给前端展示用
                        Long dataLong = data.getLong(dimensionItemDto.getCode());
                        String dataText = StrUtil.EMPTY;
                        Map<Long, String> typeCache = cache.getOrDefault(type, new HashMap<>());
                        if (dataLong != null) {
                            dataText = typeCache.getOrDefault(dataLong, assetUtil.transIdToName(type, dataLong));
                            typeCache.put(dataLong, dataText);
                        }
                        cache.putIfAbsent(type, typeCache);
                        item.put(dimensionItemDto.getCode(), dataText);
                    } else {
                        //处理查询的时候，json字段null值变成null字符串的问题
                        if (StrUtil.isBlank(item.getString(dimensionItemDto.getCode()))
                                || "null".equalsIgnoreCase(item.getString(dimensionItemDto.getCode()))) {
                            item.put(dimensionItemDto.getCode(), "");
                        }
                    }
                } catch (Exception e) {
                    log.error("assetReportUtil translateAssetJsonView error! code=[{}] data=[{}] exception ", 
                            dimensionItemDto.getCode(), JSONObject.toJSONString(data), e);
                    //问题数据补个空
                    item.put(dimensionItemDto.getCode(), "");
                }
            }
        }
    }

    /**
     * 解析具体字段值，id -> name
     * @param fieldValue
     * @param dimension
     * @return
     */
    public String translateSpecificField(String fieldValue, DimensionItemDto dimension) {
        if (StrUtil.isBlank(fieldValue)) {
            return ReportConstant.EMPTY_DESC;
        }

        //资产来源不做转换，这里是以打补丁的形式进行处理，所以后续碰到了继续添加
        if (ReportAssetConstant.ASSET_ORIGIN.equalsIgnoreCase(dimension.getCode())
                || ReportAssetConstant.MATERIAL_CODE.equalsIgnoreCase(dimension.getCode())) {
            return fieldValue;
        }

        Long fieldLongValue = 0L;
        try {
            fieldLongValue = Long.parseLong(fieldValue);
        } catch (Exception e) {
            return fieldValue;
        }

        List<String> dynamicFieldType = ListUtil.of(AssetConstant.ED_YZC_ORG, AssetConstant.ED_YZC_EMP,
                AssetConstant.ED_YZC_AREA, AssetConstant.ED_YZC_ASSET_CATE, AssetConstant.ED_YZC_SUPPLIER,
                AssetConstant.ED_YZC_MATERIAL_CATE, AssetConstant.ED_YZC_REPOSITORY);

        //状态转换
        if (ASSET_STATUS.equalsIgnoreCase(dimension.getCode())) {
            return cacheResourceUtil.getAssetStatusName(fieldLongValue);
        } else if (QueryFieldConstant.FIELD_CREATE_BY.equalsIgnoreCase(dimension.getCode())) {
            //创建人转换
            return cacheResourceUtil.getUserNameAndCode(fieldLongValue);
        } else if (QueryFieldConstant.ASSET_FILED_STANDARD.equalsIgnoreCase(dimension.getCode())) {
            //标准品转换
            List<FormVO> formVOList = formService.getByFormIds(Collections.singletonList(fieldLongValue));
            if (CollUtil.isNotEmpty(formVOList)) {
                return formVOList.get(0).getFormName();
            } else {
                return ReportConstant.EMPTY_DESC;
            }
        } else if (dynamicFieldType.contains(dimension.getType())){
            //组织等数据转换
            return assetUtil.transIdToName(dimension.getType(), fieldLongValue);
        }
        return ReportConstant.EMPTY_DESC;
    }

    /**
     * 转换统计范围字段
     * @param fieldValue
     * @param conditionDto
     * @return
     */
    public String translateStatisticsCondition(String fieldValue, QueryConditionDto conditionDto) {
        List<String> dynamicFieldType = ListUtil.of(AssetConstant.ED_YZC_ORG, AssetConstant.ED_YZC_EMP,
                AssetConstant.ED_YZC_AREA, AssetConstant.ED_YZC_ASSET_CATE, AssetConstant.ED_YZC_SUPPLIER,
                AssetConstant.ED_YZC_MATERIAL_CATE, AssetConstant.ED_YZC_REPOSITORY);

        //状态转换
        if (ASSET_STATUS.equalsIgnoreCase(conditionDto.getCode())) {
            return cacheResourceUtil.getAssetStatusName(Long.parseLong(fieldValue));
        } else if (QueryFieldConstant.FIELD_CREATE_BY.equalsIgnoreCase(conditionDto.getCode())) {
            //创建人转换
            return cacheResourceUtil.getUserNameAndCode(Long.parseLong(fieldValue));
        } else if (dynamicFieldType.contains(conditionDto.getType())){
            //组织等数据转换
            return assetUtil.transIdToName(conditionDto.getType(), Long.parseLong(fieldValue));
        } else {
            return fieldValue;
        }
    }
}
