package com.niimbot.asset.report.service;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.report.DimensionDataItemDto;
import com.niimbot.report.DimensionItemDto;

import java.util.List;

/**
 * 维度分组处理类
 * <AUTHOR>
 * @date 2023/6/27 上午9:43
 */
public interface DimensionGroupService {

    /**
     * 处理维度分组
     * @param dimensionItemDtoList
     * @param rowDataList
     * @return
     */
    List<DimensionDataItemDto> processDimensionGroup(List<DimensionItemDto> dimensionItemDtoList, List<JSONObject> rowDataList);
}
