package com.niimbot.asset.report.component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.niimbot.asset.framework.utils.JacksonConverter;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/18 下午10:52
 */
@Slf4j
public class JsonUtil {

    public static JSONObject toJSONObject(JSONObject data) {
        JSONObject object = new JSONObject();
        if (data == null) {
            return object;
        }
        try {
            String json = JacksonConverter.MAPPER.writeValueAsString(data);
            object = JSONObject.parseObject(json, Feature.OrderedField);
        } catch (JsonProcessingException e) {
            log.error("单据数据转换JSONObject失败", e);
        }
        return object;
    }
}
