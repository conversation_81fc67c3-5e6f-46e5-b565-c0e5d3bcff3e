package com.niimbot.asset.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.resolver.MySqlAssetQueryConditionResolver;
import com.niimbot.asset.report.component.AssetReportUtil;
import com.niimbot.asset.report.component.MaterialReportQueryConditionResolver;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.asset.report.constants.ReportConstant;
import com.niimbot.asset.report.enums.DimensionDisplayTypeEnum;
import com.niimbot.asset.report.mapper.AssetReportMapper;
import com.niimbot.asset.report.service.DimensionGroupService;
import com.niimbot.asset.report.service.StockCustomReportService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;
import com.niimbot.report.DimensionDataItemDto;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.report.NormItemDto;
import com.niimbot.report.ReportQueryFieldDto;
import com.niimbot.system.QueryConditionDto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.TreeSet;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/15 下午2:31
 */
@Slf4j
@Service
public class StockCustomReportServiceImpl implements StockCustomReportService {

    @Autowired
    private DimensionGroupService dimensionGroupService;
    @Autowired
    private AssetReportMapper assetReportMapper;
    @Autowired
    private AssetReportUtil assetReportUtil;
    @Autowired
    private MaterialReportQueryConditionResolver materialQueryConditionResolver;
    @Autowired
    private DataScopeStrategyManager dataScopeStrategyManager;

    @Override
    public DimensionDataDto reportData(CustomReportConfigDto customReportConfigDto) {
        //返回结果
        DimensionDataDto result = new DimensionDataDto();
        result.setReportId(customReportConfigDto.getId());
        result.setReportName(customReportConfigDto.getReportName());
        result.setNormName(customReportConfigDto.getNorm().stream().map(NormItemDto::getName).collect(Collectors.toList()));

        //判断维度是否存在仓库
        Optional<DimensionItemDto> dimensionItemDtoOptional = customReportConfigDto.getDimension()
                .stream().filter(item -> ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())).findAny();

        //生成查询SQL
        ReportQueryFieldDto queryFieldDto = generateQuerySqlParam(customReportConfigDto, dimensionItemDtoOptional.isPresent());
        String queryParamStr = JSONObject.toJSONString(queryFieldDto);
        JSONObject queryParam = JSONObject.parseObject(queryParamStr);
        log.info("stockCustomReportService reportData companyId=[{}] queryParam=[{}]", LoginUserThreadLocal.getCompanyId(), queryParamStr);

        //执行SQL查询
        List<JSONObject> stockDataList = null;
        if (dimensionItemDtoOptional.isPresent()) {
            stockDataList = assetReportMapper.stockRepositoryCustomReport(queryParam);
        } else {
            stockDataList = assetReportMapper.stockCustomReport(queryParam);
        }

        if (CollUtil.isEmpty(stockDataList)) {
            result.setDimensionData(Collections.emptyList());
            return result;
        }

        //设置其他属性
        assembleOtherField(stockDataList, customReportConfigDto);

        //耗材档案的不参与维度分组展示
        List<DimensionItemDto> dimensionItemDtoList = customReportConfigDto.getDimension().stream()
                .filter(item -> !DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType()))
                .collect(Collectors.toList());

        //维度分组数据处理
        List<DimensionDataItemDto> data = dimensionGroupService.processDimensionGroup(dimensionItemDtoList, stockDataList);

        //去重后的维度
        List<DimensionItemDto> distinctDimensionList = customReportConfigDto.getDimension().stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DimensionItemDto::getCode))), ArrayList::new));

        //id转name
        assetReportUtil.translateAssetJsonView(stockDataList, distinctDimensionList);
        result.setDimensionData(data);
        return result;
    }

    @Override
    public AssetStatisticsDataDto statisticsReportData(CustomReportConfigDto customReportConfigDto) {
        //判断维度是否存在仓库
        Optional<DimensionItemDto> dimensionItemDtoOptional = customReportConfigDto.getDimension()
                .stream().filter(item -> ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())).findAny();

        //生成分页查询sql
        ReportQueryFieldDto queryFieldDto = generateStatisticsQuerySql(customReportConfigDto, dimensionItemDtoOptional.isPresent());
        //塞一个id字段查询，作为详情接口参数使用
        queryFieldDto.setFieldStr(String.join(",", "m.id as materialId", "m.detail_id as detailId", queryFieldDto.getFieldStr()));

        String queryParamStr = JSONObject.toJSONString(queryFieldDto);
        log.info("stockCustomReportService statisticsReportData companyId=[{}] queryParam=[{}]", LoginUserThreadLocal.getCompanyId(), queryParamStr);

        List<JSONObject> statisticsDataList = null;
        if (dimensionItemDtoOptional.isPresent()) {
            statisticsDataList = assetReportMapper.stockRepositoryCustomReport(JSONObject.parseObject(queryParamStr));
        } else {
            statisticsDataList = assetReportMapper.stockCustomReport(JSONObject.parseObject(queryParamStr));
        }

        //生成汇总查询sql
        ReportQueryFieldDto summary = generateSummaryQuerySql(customReportConfigDto, dimensionItemDtoOptional.isPresent());

        String summaryQueryParamStr = JSONObject.toJSONString(summary);
        log.info("stockCustomReportService statisticsReportData summary companyId=[{}] summaryQueryParam=[{}]", LoginUserThreadLocal.getCompanyId(), summaryQueryParamStr);

        List<JSONObject> summaryList = null;
        if (dimensionItemDtoOptional.isPresent()) {
            summaryList = assetReportMapper.stockRepositoryCustomReport(JSONObject.parseObject(summaryQueryParamStr));
        } else {
            summaryList = assetReportMapper.stockCustomReport(JSONObject.parseObject(summaryQueryParamStr));
        }

        //生成结果数据
        AssetStatisticsDataDto result = new AssetStatisticsDataDto();
        result.setDimension(customReportConfigDto.getDimension());
        //id -> name转换
        assetReportUtil.translateAssetJsonView(statisticsDataList, customReportConfigDto.getDimension());
        result.setList(statisticsDataList);
        result.setCurrent(customReportConfigDto.getCurrent());
        result.setSize(customReportConfigDto.getSize());
        result.setTotalCount(summaryList.get(0).getInteger(ReportConstant.TOTAL));
        result.setTotalPage((int)Math.ceil((double)result.getTotalCount() / (double)result.getSize()));

        //处理汇总字段名称和值
        Map<String, String> summaryData = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(customReportConfigDto.getSummary())) {
            for (NormItemDto item : customReportConfigDto.getSummary()) {
                summaryData.put(item.getName(), summaryList.get(0).getString(item.getCode()));
            }
        }
        result.setSummary(summaryData);
        return result;
    }

    /**
     * 生成资产查询参数对象
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateQuerySqlParam(CustomReportConfigDto configDto, Boolean containRepositoryId) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();

        //生成指标 select 查询字段
        String norm = resolveNorm(configDto.getNorm(), containRepositoryId);
        //生成groupBy中字段到select查询字段中
        String dimension = resolveGroupByAsField(configDto, Boolean.FALSE);
        //select字段：groupBy中的字段 + 指标字段
        result.setFieldStr(String.join(", ", dimension, norm));
        //生成where查询条件
        String conditionStr = "";
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        String repositoryCondition = "";
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            //仓库筛选条件
            List<QueryConditionDto> repositoryConditionList = new ArrayList<>();
            //其他筛选条件
            List<QueryConditionDto> otherCondition = new ArrayList<>();
            for (QueryConditionDto item : configDto.getStatisticCondition()) {
                if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                    repositoryConditionList.add(item);
                } else {
                    otherCondition.add(item);
                }
            }
            //解析仓库筛选条件
            if (CollUtil.isNotEmpty(repositoryConditionList)) {
                repositoryCondition = String.join(" ", repositoryCondition,
                        materialQueryConditionResolver.resolveRepositoryIdQueryCondition(repositoryConditionList.get(0)));
            }
            //如果是维度或表头含有仓库的话，有仓库筛选条件的话还是要在外面加下，进行过滤，维度或表头没有仓库的话，仓库筛选条件放在里面
            if (containRepositoryId) {
                conditionStr = String.join(" ", conditionStr,
                        materialQueryConditionResolver.resolveQueryCondition("m", configDto.getStatisticCondition()));
            } else {
                //解析其他筛选条件
                if (CollUtil.isNotEmpty(otherCondition)) {
                    conditionStr = String.join(" ", conditionStr,
                            materialQueryConditionResolver.resolveQueryCondition("m", otherCondition));
                }
            }
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            //仓库筛选条件
            List<QueryConditionDto> repositoryConditionList = new ArrayList<>();
            //其他筛选条件
            List<QueryConditionDto> otherCondition = new ArrayList<>();
            for (QueryConditionDto item : configDto.getDimensionQueryCondition()) {
                if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                    repositoryConditionList.add(item);
                } else {
                    otherCondition.add(item);
                }
            }
            //解析仓库筛选条件
            if (CollUtil.isNotEmpty(repositoryConditionList)) {
                repositoryCondition = String.join(" ", repositoryCondition,
                        materialQueryConditionResolver.resolveRepositoryIdQueryCondition(repositoryConditionList.get(0)));
            }
            //如果是维度或表头含有仓库的话，有仓库筛选条件的话还是要在外面加下，进行过滤，维度或表头没有仓库的话，仓库筛选条件放在里面
            if (containRepositoryId) {
                conditionStr = String.join(" ", conditionStr,
                        materialQueryConditionResolver.resolveQueryCondition("m", configDto.getDimensionQueryCondition()));
            } else {
                //解析其他筛选条件
                if (CollUtil.isNotEmpty(otherCondition)) {
                    conditionStr = String.join(" ", conditionStr,
                            materialQueryConditionResolver.resolveQueryCondition("m", otherCondition));
                }
            }
        }
        result.setConditionStr(conditionStr);

        //kw关键字搜索
        result.setKw(configDto.getKw());

        //解析有仓库权限，不带表的别名
        String permRepository = getRepositorySql(null, "");
        if (StrUtil.isNotBlank(permRepository)) {
            result.setRepositoryCondition(String.join(" ", repositoryCondition, permRepository));
        } else {
            result.setRepositoryCondition(repositoryCondition);
        }

        //如果维度或表头含有仓库的话，数据权限解析仓库条件还是需要在外面加上，维度或表头没有仓库字段的话，上面已经带有仓库条件
        if (containRepositoryId) {
            String permSql = getRepositorySql(null, "m");
            result.setPermSql(permSql);
        }

        //生成groupBy查询分组条件
        result.setGroupStr(resolveGroupBy(configDto));
        //如果有排序，生成order by语句
        String orderStr = resolveSortString(configDto);
        if (StrUtil.isNotBlank(orderStr)) {
            result.setOrderStr(orderStr);
        }
        return result;
    }

    public String getRepositorySql(Long repositoryId, String tableAlias) {
        String alias = StrUtil.isBlank(tableAlias) ? "" : tableAlias + ".";
        StringJoiner joiner = new StringJoiner("");
        if (repositoryId != null) {
            joiner.add(" and ").add(alias + "repository_id = ").add(repositoryId.toString());
        }
        String storeSql = dataScopeStrategyManager.simplePermsSql(DataPermType.STORE);
        if (StrUtil.isNotEmpty(storeSql)) {
            joiner.add(" and ").add(alias + "repository_id in ").add(storeSql);
        }
        return joiner.toString();
    }

    /**
     * 解析汇总字段处理
     * @param norm
     * @return
     */
    private String resolveSummary(List<NormItemDto> norm) {
        StringJoiner result = new StringJoiner("");
        for (NormItemDto item : norm) {
            if (ReportAssetConstant.MATERIAL_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.COUNT_TPL, "m.id")).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.STOCK_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.SUM_TPL, "m.current_quantity")).add(" as ").add(item.getCode());
            } else {
                result.add(", ").add(String.format(ReportConstant.SUM_TPL, "m.total_money")).add(" as ").add(item.getCode());
            }
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析资产数据指标查询
     * @param norm
     * @return
     */
    private String resolveNorm(List<NormItemDto> norm, Boolean containRepositoryId) {
        StringJoiner result = new StringJoiner("");
        for (NormItemDto item : norm) {
            if (ReportAssetConstant.MATERIAL_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.COUNT_TPL, "m.id")).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.STOCK_NUM.equalsIgnoreCase(item.getCode())) {
                if (containRepositoryId) {
                    result.add(", ").add(String.format(ReportConstant.SUM_TPL, "m.current_quantity")).add(" as ").add(item.getCode());
                } else {
                    result.add(", ").add(String.format(ReportConstant.MAX_TPL, "m.current_quantity")).add(" as ").add(item.getCode());
                }
            } else {
                if (containRepositoryId) {
                    result.add(", ").add(String.format(ReportConstant.SUM_TPL, "m.total_money")).add(" as ").add(item.getCode());
                } else {
                    result.add(", ").add(String.format(ReportConstant.MAX_TPL, "m.total_money")).add(" as ").add(item.getCode());
                }
            }
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析维度groupBy语句，将groupBy中的字段添加到select中
     * @param configDto
     * @param containField 对于displayType=6是否包含到查询字段中
     * @return
     */
    private String resolveDetailGroupByAsField(CustomReportConfigDto configDto, Boolean containField) {
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            //耗材档案
            if (!containField && DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())) {
                continue;
            }
            String fieldStr = null;
            if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(item.getCode()));
            } else if (ReportAssetConstant.STOCK_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "current_quantity");
            } else if (ReportAssetConstant.AVG_PRICE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(item.getCode()));
            } else if (ReportAssetConstant.STOCK_PRICE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "total_money");
            } else {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.MATERIAL_JSON_SQL_SEGMENT_TPL, "m", item.getCode());
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
            }
            result.add(", ").add(fieldStr).add(" as ").add(item.getCode());
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析维度groupBy语句，将groupBy中的字段添加到select中
     * @param configDto
     * @param containField 对于displayType=6是否包含到查询字段中
     * @return
     */
    private String resolveGroupByAsField(CustomReportConfigDto configDto, Boolean containField) {
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            //耗材档案
            if (!containField && DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())) {
                continue;
            }
            String fieldStr = null;
            if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(item.getCode()));
            } else if (ReportAssetConstant.STOCK_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "current_quantity");
            } else if (ReportAssetConstant.AVG_PRICE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(item.getCode()));
            } else if (ReportAssetConstant.STOCK_PRICE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "total_money");
            } else {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.MATERIAL_JSON_SQL_SEGMENT_TPL, "m", item.getCode());
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
            }
            result.add(", ").add(fieldStr).add(" as ").add(item.getCode());
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析维度groupBy语句
     * @param configDto
     * @return
     */
    private String resolveGroupBy(CustomReportConfigDto configDto) {
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            //耗材档案
            if (item.getDisplayType() == 6) {
                continue;
            }
            String fieldStr = null;
            if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(item.getCode()));
            } else if (ReportAssetConstant.STOCK_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "current_quantity");
            } else if (ReportAssetConstant.AVG_PRICE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(item.getCode()));
            } else if (ReportAssetConstant.STOCK_PRICE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "total_money");
            } else {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.MATERIAL_JSON_SQL_SEGMENT_TPL, "m", item.getCode());
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
            }
            result.add(", ").add(fieldStr);
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 处理日期类型group条件
     * @param fieldStr
     * @param groupCondition
     * @return
     */
    private String formatDateGroup(String fieldStr, Integer groupCondition) {
        if (groupCondition == 1) {
            //1-按年分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_YEAR_TPL);
        } else if (groupCondition == 2) {
            //2-按月分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_MONTH_TPL);
        } else {
            //3-按天分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_DAY_TPL);
        }
    }

    /**
     * 设置耗材档案其他字段属性
     * @param stockDataList
     * @param configDto
     */
    private void assembleOtherField(List<JSONObject> stockDataList, CustomReportConfigDto configDto) {
        Map<String, JSONObject> materialFieldMap = new HashMap<>();

        //获取耗材编码
        List<String> materialCodeList = stockDataList.stream().map(item -> item.getString(ReportAssetConstant.MATERIAL_CODE)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(materialCodeList)) {
            return ;
        }

        //获取耗材档案额外字段
        List<DimensionItemDto> dimensionItemDtos = configDto.getDimension().stream()
                .filter(item -> DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType()))
                .collect(Collectors.toList());

        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : dimensionItemDtos) {
            //耗材档案
            String fieldStr = String.format(MySqlAssetQueryConditionResolver.MATERIAL_JSON_SQL_SEGMENT_TPL, "m", item.getCode());
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                //默认按年月日展示
                fieldStr = formatDateGroup(fieldStr, 3);
            }
            result.add(", ").add(fieldStr).add(" as ").add(item.getCode());
        }
        String otherFieldQueryStr = result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
        if (StrUtil.isBlank(otherFieldQueryStr)) {
            return ;
        }

        JSONObject queryParam = new JSONObject();
        queryParam.put("fieldStr", otherFieldQueryStr);
        queryParam.put("materialCode", materialCodeList);
        queryParam.put("companyId", LoginUserThreadLocal.getCompanyId());
        List<JSONObject> otherFieldList = assetReportMapper.selectMaterialOtherField(queryParam);
        if (CollUtil.isEmpty(otherFieldList)) {
            return ;
        }

        //耗材分组
        materialFieldMap = otherFieldList.stream()
                .collect(Collectors.toMap(item -> item.getString(ReportAssetConstant.MATERIAL_CODE), value -> value, (v1, v2) -> v1));
        for (JSONObject item : stockDataList) {
            if (Objects.nonNull(materialFieldMap.get(item.getString(ReportAssetConstant.MATERIAL_CODE)))) {
                item.putAll(materialFieldMap.get(item.getString(ReportAssetConstant.MATERIAL_CODE)));
            }
        }
    }

    /**
     * 生成明细表查询字段
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateStatisticsQuerySql(CustomReportConfigDto configDto, Boolean containRepositoryId) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();

        result.setFieldStr(resolveDetailGroupByAsField(configDto, Boolean.TRUE));
        result.setDetailId(configDto.getDetailId());

        //生成where查询条件
        String conditionStr = "";
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        String repositoryCondition = "";
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            //仓库筛选条件
            List<QueryConditionDto> repositoryConditionList = new ArrayList<>();
            //其他筛选条件
            List<QueryConditionDto> otherCondition = new ArrayList<>();
            for (QueryConditionDto item : configDto.getStatisticCondition()) {
                if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                    repositoryConditionList.add(item);
                } else {
                    otherCondition.add(item);
                }
            }
            //解析仓库筛选条件
            if (CollUtil.isNotEmpty(repositoryConditionList)) {
                repositoryCondition = String.join(" ", repositoryCondition,
                        materialQueryConditionResolver.resolveRepositoryIdQueryCondition(repositoryConditionList.get(0)));
            }
            //如果是维度或表头含有仓库的话，有仓库筛选条件的话还是要在外面加下，进行过滤，维度或表头没有仓库的话，仓库筛选条件放在里面
            if (containRepositoryId) {
                conditionStr = String.join(" ", conditionStr,
                        materialQueryConditionResolver.resolveQueryCondition("m", configDto.getStatisticCondition()));
            } else {
                //解析其他筛选条件
                if (CollUtil.isNotEmpty(otherCondition)) {
                    conditionStr = String.join(" ", conditionStr,
                            materialQueryConditionResolver.resolveQueryCondition("m", otherCondition));
                }
            }
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            //仓库筛选条件
            List<QueryConditionDto> repositoryConditionList = new ArrayList<>();
            //其他筛选条件
            List<QueryConditionDto> otherCondition = new ArrayList<>();
            for (QueryConditionDto item : configDto.getDimensionQueryCondition()) {
                if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                    repositoryConditionList.add(item);
                } else {
                    otherCondition.add(item);
                }
            }
            //解析仓库筛选条件
            if (CollUtil.isNotEmpty(repositoryConditionList)) {
                repositoryCondition = String.join(" ", repositoryCondition,
                        materialQueryConditionResolver.resolveRepositoryIdQueryCondition(repositoryConditionList.get(0)));
            }
            //如果是维度或表头含有仓库的话，有仓库筛选条件的话还是要在外面加下，进行过滤，维度或表头没有仓库的话，仓库筛选条件放在里面
            if (containRepositoryId) {
                conditionStr = String.join(" ", conditionStr,
                        materialQueryConditionResolver.resolveQueryCondition("m", configDto.getDimensionQueryCondition()));
            } else {
                //解析其他筛选条件
                if (CollUtil.isNotEmpty(otherCondition)) {
                    conditionStr = String.join(" ", conditionStr,
                            materialQueryConditionResolver.resolveQueryCondition("m", otherCondition));
                }
            }
        }
        result.setConditionStr(conditionStr);

        //解析有仓库权限，不带表的别名
        String permRepository = getRepositorySql(null, "");
        if (StrUtil.isNotBlank(permRepository)) {
            result.setRepositoryCondition(String.join(" ", repositoryCondition, permRepository));
        } else {
            result.setRepositoryCondition(repositoryCondition);
        }

        //如果维度或表头含有仓库的话，数据权限解析仓库条件还是需要在外面加上，维度或表头没有仓库字段的话，上面已经带有仓库条件
        if (containRepositoryId) {
            String permSql = getRepositorySql(null, "m");
            result.setPermSql(permSql);
        }

        //设置关键字搜索
        result.setKw(configDto.getKw());

        //如果有排序，生成order by语句
        String orderStr = resolveSortString(configDto);
        if (StrUtil.isNotBlank(orderStr)) {
            result.setOrderStr(orderStr);
        }

        //生成分页limit sql
        long start = (configDto.getCurrent() - 1) * configDto.getSize();
        String limitStr = String.join(", ", String.valueOf(start), String.valueOf(configDto.getSize()));
        result.setLimitStr(limitStr);
        return result;
    }

    /**
     * 生成明细表汇总sql
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateSummaryQuerySql(CustomReportConfigDto configDto, Boolean containRepositoryId) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();

        //设置勾选明细id
        result.setDetailId(configDto.getDetailId());

        String summarySql = "count(m.id) as total";
        if (CollUtil.isNotEmpty(configDto.getSummary())) {
            result.setFieldStr(String.join(",", summarySql, resolveSummary(configDto.getSummary())));
        } else {
            result.setFieldStr(summarySql);
        }
        //生成where查询条件
        String conditionStr = "";
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        String repositoryCondition = "";
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            //仓库筛选条件
            List<QueryConditionDto> repositoryConditionList = new ArrayList<>();
            //其他筛选条件
            List<QueryConditionDto> otherCondition = new ArrayList<>();
            for (QueryConditionDto item : configDto.getStatisticCondition()) {
                if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                    repositoryConditionList.add(item);
                } else {
                    otherCondition.add(item);
                }
            }
            //解析仓库筛选条件
            if (CollUtil.isNotEmpty(repositoryConditionList)) {
                repositoryCondition = String.join(" ", repositoryCondition,
                        materialQueryConditionResolver.resolveRepositoryIdQueryCondition(repositoryConditionList.get(0)));
            }
            //如果是维度或表头含有仓库的话，有仓库筛选条件的话还是要在外面加下，进行过滤，维度或表头没有仓库的话，仓库筛选条件放在里面
            if (containRepositoryId) {
                conditionStr = String.join(" ", conditionStr,
                        materialQueryConditionResolver.resolveQueryCondition("m", configDto.getStatisticCondition()));
            } else {
                //解析其他筛选条件
                if (CollUtil.isNotEmpty(otherCondition)) {
                    conditionStr = String.join(" ", conditionStr,
                            materialQueryConditionResolver.resolveQueryCondition("m", otherCondition));
                }
            }
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            //仓库筛选条件
            List<QueryConditionDto> repositoryConditionList = new ArrayList<>();
            //其他筛选条件
            List<QueryConditionDto> otherCondition = new ArrayList<>();
            for (QueryConditionDto item : configDto.getDimensionQueryCondition()) {
                if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(item.getCode())) {
                    repositoryConditionList.add(item);
                } else {
                    otherCondition.add(item);
                }
            }
            //解析仓库筛选条件
            if (CollUtil.isNotEmpty(repositoryConditionList)) {
                repositoryCondition = String.join(" ", repositoryCondition,
                        materialQueryConditionResolver.resolveRepositoryIdQueryCondition(repositoryConditionList.get(0)));
            }
            //如果是维度或表头含有仓库的话，有仓库筛选条件的话还是要在外面加下，进行过滤，维度或表头没有仓库的话，仓库筛选条件放在里面
            if (containRepositoryId) {
                conditionStr = String.join(" ", conditionStr,
                        materialQueryConditionResolver.resolveQueryCondition("m", configDto.getDimensionQueryCondition()));
            } else {
                //解析其他筛选条件
                if (CollUtil.isNotEmpty(otherCondition)) {
                    conditionStr = String.join(" ", conditionStr,
                            materialQueryConditionResolver.resolveQueryCondition("m", otherCondition));
                }
            }
        }
        result.setConditionStr(conditionStr);

        //解析有仓库权限，不带表的别名
        String permRepository = getRepositorySql(null, "");
        if (StrUtil.isNotBlank(permRepository)) {
            result.setRepositoryCondition(String.join(" ", repositoryCondition, permRepository));
        } else {
            result.setRepositoryCondition(repositoryCondition);
        }

        //如果维度或表头含有仓库的话，数据权限解析仓库条件还是需要在外面加上，维度或表头没有仓库字段的话，上面已经带有仓库条件
        if (containRepositoryId) {
            String permSql = getRepositorySql(null, "m");
            result.setPermSql(permSql);
        }

        //设置关键字搜索
        result.setKw(configDto.getKw());
        return result;
    }

    /**
     * 处理排序条件字符串
     * @param configDto
     * @return
     */
    private String resolveSortString(CustomReportConfigDto configDto) {
        if (Objects.isNull(configDto.getSortCondition()) || StrUtil.isBlank(configDto.getSortCondition().getSidx())) {
            return null;
        }

        //排序字段在维度里面有，表示select已经查询出来了，直接用这个字段排序就可以了
        Optional<DimensionItemDto> dimensionItemOptional = configDto.getDimension()
                .stream()
                .filter(item -> !DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())
                        && item.getCode().equalsIgnoreCase(configDto.getSortCondition().getSidx()))
                .findAny();
        if (dimensionItemOptional.isPresent()) {
            return String.join(" ", configDto.getSortCondition().getSidx(), configDto.getSortCondition().getOrder());
        } else {
            String fieldStr = null;
            if (ReportAssetConstant.REPOSITORY_ID.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(configDto.getSortCondition().getSidx()));
            } else if (ReportAssetConstant.STOCK_NUM.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "current_quantity");
            } else if (ReportAssetConstant.AVG_PRICE.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", StrUtil.toUnderlineCase(configDto.getSortCondition().getSidx()));
            } else if (ReportAssetConstant.STOCK_PRICE.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, "m", "total_money");
            } else {
                fieldStr = String.format(MySqlAssetQueryConditionResolver.MATERIAL_JSON_SQL_SEGMENT_TPL, "m", configDto.getSortCondition().getSidx());
            }
            return String.join(" ", fieldStr, configDto.getSortCondition().getOrder());
        }
    }
}
