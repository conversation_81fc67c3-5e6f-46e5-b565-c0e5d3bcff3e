package com.niimbot.asset.report.controller;

import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.report.service.CustomReportService;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;
import com.niimbot.report.DimensionFieldDto;
import com.niimbot.report.NormItemDto;
import com.niimbot.report.ReportConfigItemDto;
import com.niimbot.report.StatisticsConditionDescDto;
import com.niimbot.system.QueryConditionDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023/6/8 上午11:14
 */
@Api(tags = "自定义报表")
@RestController
@RequestMapping("server/report/custom/")
public class CustomReportServiceController {

    @Autowired
    private CustomReportService customReportService;

    /**
     * 资产自定义统计图和统计表配置
     * @param configDto
     * @return
     */
    @ApiOperation(value = "自定义报表配置")
    @PostMapping("config")
    public DimensionDataDto configReport(@RequestBody CustomReportConfigDto configDto) {
        return customReportService.configReport(configDto);
    }

    /**
     * 编辑资产自定义统计图和统计表配置
     * @param configDto
     * @return
     */
    @ApiOperation(value = "编辑自定义报表")
    @PostMapping("edit")
    public DimensionDataDto editReport(@RequestBody CustomReportConfigDto configDto) {
        return customReportService.editReport(configDto);
    }

    /**
     * 查询资产自定义统计图和统计表数据
     * @param configDto
     * @return
     */
    @ApiOperation(value = "自定义报表查询")
    @PostMapping("query")
    public DimensionDataDto query(@RequestBody CustomReportConfigDto configDto) {
        return customReportService.queryReport(configDto);
    }

    @ApiOperation(value = "资产自定义明细报表配置")
    @PostMapping("detailReportConfig")
    public AssetStatisticsDataDto detailReportConfig(@RequestBody CustomReportConfigDto customReportConfigDto) {
        return customReportService.detailReportConfig(customReportConfigDto);
    }

    @ApiOperation(value = "编辑资产自定义明细报表")
    @PostMapping("editDetail")
    public AssetStatisticsDataDto editDetailReport(@RequestBody CustomReportConfigDto customReportConfigDto) {
        return customReportService.editDetailReport(customReportConfigDto);
    }

    @ApiOperation(value = "资产自定义明细报表查询")
    @PostMapping("queryDetailReport")
    public AssetStatisticsDataDto queryDetailReport(@RequestBody CustomReportConfigDto customReportConfigDto) {
        return customReportService.queryDetailReport(customReportConfigDto);
    }

    @ApiOperation(value = "查询报表配置信息")
    @GetMapping("queryReportConfig/{id}")
    public CustomReportConfigDto queryReportConfig(@PathVariable("id") Long id) {
        return customReportService.queryReportConfig(id);
    }

    @ApiOperation(value = "分析维度字段查询")
    @GetMapping("dimension/query/field")
    public DimensionFieldDto dimensionQueryField(Long reportId) {
        return customReportService.dimensionConditionField(reportId);
    }

    @ApiOperation(value = "数据指标字段查询")
    @GetMapping("norm/query/field")
    public List<NormItemDto> normQueryField(Long reportId) {
        return customReportService.normQueryField(reportId);
    }

    @ApiOperation(value = "统计范围字段查询")
    @GetMapping("statisticsCondition/query/field")
    public List<QueryConditionDto> statisticsConditionField(Long reportId) {
        return customReportService.statisticsConditionField(reportId);
    }

    @ApiOperation(value = "查询企业自定义报表列表")
    @GetMapping("queryAll")
    public List<ReportConfigItemDto> queryAll() {
        return customReportService.queryAll();
    }

    @ApiOperation(value = "删除自定义报表配置")
    @DeleteMapping("remove/{id}")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    public Boolean dropConfig(@PathVariable Long id) {
        return customReportService.removeCustomReportConfig(id);
    }

    @ApiOperation(value = "统计范围描述")
    @GetMapping("statisticsConditionDesc/{id}")
    public List<StatisticsConditionDescDto> queryStatisticsConditionDesc(@PathVariable Long id) {
        return customReportService.statisticsConditionDesc(id);
    }
}
