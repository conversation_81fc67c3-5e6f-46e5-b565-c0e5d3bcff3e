package com.niimbot.asset.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.report.AssetLogReportDto;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.GroupReportResult;
import com.niimbot.report.HandleAssetLogDto;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.MaterialCategoryReportSearch;
import com.niimbot.report.MaterialRepositoryReportSearch;
import com.niimbot.report.MeansCategoryReportSearch;
import com.niimbot.report.RepairAssetLogDto;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.UseOrgGroupReportSearch;
import com.niimbot.report.WaitHandleAssetLogDto;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;
import com.niimbot.system.QueryConditionSortDto;

/**
 * 预定义报表
 *
 * <AUTHOR>
 */
public interface PredefinedReportService {

    QueryConditionSortDto assetSortField(String type);

    /**
     * 按使用部门分组的资产增减表
     *
     * @param search 搜索条件
     * @return means org report
     */
    GroupReportResult meansUseOrgFixedHeadReport(UseOrgGroupReportSearch search);

    /**
     * 按资产分类分组的资产增减表
     *
     * @param search 搜索条件
     * @return means org report
     */
    GroupReportResult meansCategoryFixedHeadReport(MeansCategoryReportSearch search);

    /**
     * 按照耗材仓库分组的耗材增减表
     *
     * @param search 搜索条件
     * @return material category report
     */
    GroupReportResult materialCategoryFixedHeadReport(MaterialCategoryReportSearch search);

    /**
     * 按照耗材仓库分组的耗材增减表
     *
     * @param search 搜索条件
     * @return material repository report
     */
    GroupReportResult materialRepositoryFixedHeadReport(MaterialRepositoryReportSearch search);

    IPage<AssetLogReportDto> assetLogReport(AssetLogReportQueryDto queryDto);

    IPage<WaitHandleAssetLogDto> waitHandleAssetLog(WaitHandleAssetLogQueryDto queryDto);

    IPage<HandleAssetLogDto> handleAssetLog(HandleAssetLogQueryDto queryDto);

    IPage<WaitReturnAssetLogDto> waitReturnAssetLog(WaitReturnAssetLogQueryDto queryDto);

    IPage<RepairAssetLogDto> repairAssetLog(RepairAssetLogQueryDto queryDto);

    IPage<RepairAssetRecordDto> repairAssetRecord(RepairAssetRecordQueryDto queryDto);
}
