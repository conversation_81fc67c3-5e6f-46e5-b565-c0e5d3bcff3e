package com.niimbot.asset.report.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/6/16 上午10:31
 */
@Getter
public enum CustomReportBizTypeEnum {

    ASSET(1, "资产"),
    MATERIAL_STOCK(2, "耗材库存"),
    MATERIAL_STORAGE(3, "耗材入库"),
    MATERIAL_OUT(4, "耗材出库"),
    MATERIAL_RECEIPT(5, "耗材领用单"),

    UN_KNOW(-1, "未知"),
    ;

    CustomReportBizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public static CustomReportBizTypeEnum getByCode(Integer code) {
        for (CustomReportBizTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
