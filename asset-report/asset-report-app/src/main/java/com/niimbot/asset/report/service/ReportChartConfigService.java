package com.niimbot.asset.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.report.ReportChartConfigDto;
import com.niimbot.report.ReportChartQueryDto;
import com.niimbot.asset.report.model.AsReportChartConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/6 下午2:04
 */
public interface ReportChartConfigService extends IService<AsReportChartConfig> {

    /**
     * 配置报表图表类型
     * @param reportChartConfigDto
     * @return
     */
    Boolean config(ReportChartConfigDto reportChartConfigDto);

    /**
     * 编辑报表图表类型
     * @param reportChartConfigDto
     * @return
     */
    Boolean edit(ReportChartConfigDto reportChartConfigDto);

    /**
     * 查询所有配置表信息
     * @return
     */
    List<ReportChartConfigDto> queryAll();

    /**
     * 根据条件查询支持的图表类型
     * @param queryDto
     * @return
     */
    List<ReportChartConfigDto> queryByCondition(ReportChartQueryDto queryDto);
}
