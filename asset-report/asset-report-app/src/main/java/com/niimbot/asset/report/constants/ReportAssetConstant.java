package com.niimbot.asset.report.constants;

import com.google.common.collect.ImmutableMap;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.system.QueryConditionDto;

/**
 * <AUTHOR>
 * @date 2023/6/9 下午2:43
 */
public interface ReportAssetConstant {

    String ENTRY_DATE = "entryDate";
    String EXPIRE_DATE = "expireDate";
    String HANDLE_DATE = "handleDate";
    String IDLE_DAY = "idleDay";
    String IDLE_RATE = "idleRate";
    String OVERDUE_DAY = "overdueDay";

    String CREATE_TIME = "createTime";

    String ASSET_NUM = "assetNum";

    String REPAIR_MONEY = "repairMoney";
    String REPAIR_NUM = "repairNum";
    String REPAIR_REPORT_NUM = "repairReportNum";

    String ASSET_ORIGIN = "assetOrigin";

    String ASSET_PHOTO = "assetPhoto";

    String PRICE = "price";

    String STOCK_NUM = "stockNum";

    String MATERIAL_NUM = "materialNum";

    String MATERIAL_CATEGORY = "materialCategory";

    String STOCK_PRICE = "stockPrice";

    String STOCK_RECORD = "stockRecord";

    String MATERIAL_CODE = "materialCode";

    String MATERIAL_NAME = "materialName";

    String REPOSITORY_ID = "repositoryId";

    String AVG_PRICE = "avgPrice";

    String STORAGE_NUM = "storageNum";

    String STORAGE_PRICE = "storagePrice";

    String STORAGE_UNIT_PRICE = "storageUnitPrice";

    String OUT_NUM = "outNum";

    String OUT_PRICE = "outPrice";

    String OUT_UNIT_PRICE = "outUnitPrice";

    String RECEIPT_NUM = "receiptNum";

    String GRANT_NUM = "grantNum";

    String GRANTING_NUM = "grantingNum";

    String WAIT_GRANT_NUM = "waitGrantNum";

    ImmutableMap<String, QueryConditionDto> ASSET_COMMON_DIMENSION = new ImmutableMap.Builder<String, QueryConditionDto>()
//            .put(ENTRY_DATE, new QueryConditionDto().setCode(ENTRY_DATE).setName("资产创建日期").setType(FormFieldCO.DATETIME).setFieldProps(new JSONObject()))
            .put(EXPIRE_DATE, new QueryConditionDto().setCode(EXPIRE_DATE).setName("到期日期").setType(FormFieldCO.DATETIME).setFieldProps(new JSONObject()))
            .put(HANDLE_DATE, new QueryConditionDto().setCode(HANDLE_DATE).setName("处置日期").setType(FormFieldCO.DATETIME).setFieldProps(new JSONObject()))
//            .put(IDLE_DAY, new QueryConditionDto().setCode(IDLE_DAY).setName("资产闲置天数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
//            .put(OVERDUE_DAY, new QueryConditionDto().setCode(OVERDUE_DAY).setName("资产归还逾期天数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    ImmutableMap<String, QueryConditionDto> ASSET_COMMON_NORM = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(ASSET_NUM, new QueryConditionDto().setCode(ASSET_NUM).setName("资产数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(REPAIR_MONEY, new QueryConditionDto().setCode(REPAIR_MONEY).setName("维修费用").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(REPAIR_NUM, new QueryConditionDto().setCode(REPAIR_NUM).setName("维修次数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(REPAIR_REPORT_NUM, new QueryConditionDto().setCode(REPAIR_REPORT_NUM).setName("报修次数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
//            .put(PRICE, new QueryConditionDto().setCode(PRICE).setName("资产金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
//            .put(IDLE_DAY, new QueryConditionDto().setCode(IDLE_DAY).setName("资产闲置天数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
//            .put(OVERDUE_DAY, new QueryConditionDto().setCode(OVERDUE_DAY).setName("资产归还逾期天数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材库存维度
    ImmutableMap<String, QueryConditionDto> MATERIAL_STOCK_DIMENSION = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_CODE, new QueryConditionDto().setCode(MATERIAL_CODE).setName("耗材档案").setType(FormFieldCO.YZC_MATERIAL_SERIALNO).setFieldProps(new JSONObject()))
            .put(REPOSITORY_ID, new QueryConditionDto().setCode(REPOSITORY_ID).setName("耗材仓库").setType(FormFieldCO.YZC_REPOSITORY).setFieldProps(new JSONObject()))
            .put(STOCK_NUM, new QueryConditionDto().setCode(STOCK_NUM).setName("库存数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(AVG_PRICE, new QueryConditionDto().setCode(AVG_PRICE).setName("加权平均价值").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STOCK_PRICE, new QueryConditionDto().setCode(STOCK_PRICE).setName("库存金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材库存指标
    ImmutableMap<String, QueryConditionDto> MATERIAL_STOCK_NORM = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_NUM, new QueryConditionDto().setCode(MATERIAL_NUM).setName("耗材种数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STOCK_NUM, new QueryConditionDto().setCode(STOCK_NUM).setName("库存数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STOCK_PRICE, new QueryConditionDto().setCode(STOCK_PRICE).setName("库存金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材库存表头
    ImmutableMap<String, QueryConditionDto> MATERIAL_STOCK_HEAD = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(REPOSITORY_ID, new QueryConditionDto().setCode(REPOSITORY_ID).setName("耗材仓库").setType(FormFieldCO.YZC_REPOSITORY).setFieldProps(new JSONObject()))
            .put(STOCK_NUM, new QueryConditionDto().setCode(STOCK_NUM).setName("库存数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(AVG_PRICE, new QueryConditionDto().setCode(AVG_PRICE).setName("加权平均价值").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STOCK_PRICE, new QueryConditionDto().setCode(STOCK_PRICE).setName("库存金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材入库维度
    ImmutableMap<String, QueryConditionDto> MATERIAL_STORAGE_DIMENSION = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_CODE, new QueryConditionDto().setCode(MATERIAL_CODE).setName("耗材档案").setType(FormFieldCO.YZC_MATERIAL_SERIALNO).setFieldProps(new JSONObject()))
            .build();

    //耗材入库指标
    ImmutableMap<String, QueryConditionDto> MATERIAL_STORAGE_NORM = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_NUM, new QueryConditionDto().setCode(MATERIAL_NUM).setName("耗材种数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STORAGE_NUM, new QueryConditionDto().setCode(STORAGE_NUM).setName("入库数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STORAGE_PRICE, new QueryConditionDto().setCode(STORAGE_PRICE).setName("入库金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材入库表头
    ImmutableMap<String, QueryConditionDto> MATERIAL_STORAGE_HEAD = new ImmutableMap.Builder<String, QueryConditionDto>()
//            .put(MATERIAL_NUM, new QueryConditionDto().setCode(MATERIAL_NUM).setName("耗材种数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STORAGE_NUM, new QueryConditionDto().setCode(STORAGE_NUM).setName("入库数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STORAGE_PRICE, new QueryConditionDto().setCode(STORAGE_PRICE).setName("入库金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(STORAGE_UNIT_PRICE, new QueryConditionDto().setCode(STORAGE_UNIT_PRICE).setName("入库单价").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材出库维度
    ImmutableMap<String, QueryConditionDto> MATERIAL_OUT_DIMENSION = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_CODE, new QueryConditionDto().setCode(MATERIAL_CODE).setName("耗材档案").setType(FormFieldCO.YZC_MATERIAL_SERIALNO).setFieldProps(new JSONObject()))
            .build();

    //耗材出库指标
    ImmutableMap<String, QueryConditionDto> MATERIAL_OUT_NORM = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_NUM, new QueryConditionDto().setCode(MATERIAL_NUM).setName("耗材种数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(OUT_NUM, new QueryConditionDto().setCode(OUT_NUM).setName("出库数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(OUT_PRICE, new QueryConditionDto().setCode(OUT_PRICE).setName("出库金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材出库表头
    ImmutableMap<String, QueryConditionDto> MATERIAL_OUT_HEAD = new ImmutableMap.Builder<String, QueryConditionDto>()
//            .put(MATERIAL_NUM, new QueryConditionDto().setCode(MATERIAL_NUM).setName("耗材种数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(OUT_NUM, new QueryConditionDto().setCode(OUT_NUM).setName("出库数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(OUT_PRICE, new QueryConditionDto().setCode(OUT_PRICE).setName("出库金额").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(OUT_UNIT_PRICE, new QueryConditionDto().setCode(OUT_UNIT_PRICE).setName("出库单价").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    //耗材领用维度
    ImmutableMap<String, QueryConditionDto> MATERIAL_RECEIPT_DIMENSION = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_CODE, new QueryConditionDto().setCode(MATERIAL_CODE).setName("耗材档案").setType(FormFieldCO.YZC_MATERIAL_SERIALNO).setFieldProps(new JSONObject()))
            .build();

    //耗材领用指标
    ImmutableMap<String, QueryConditionDto> MATERIAL_RECEIPT_NORM = new ImmutableMap.Builder<String, QueryConditionDto>()
            .put(MATERIAL_NUM, new QueryConditionDto().setCode(MATERIAL_NUM).setName("耗材种数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(RECEIPT_NUM, new QueryConditionDto().setCode(RECEIPT_NUM).setName("申请数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(GRANT_NUM, new QueryConditionDto().setCode(GRANT_NUM).setName("已发放数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(GRANTING_NUM, new QueryConditionDto().setCode(GRANTING_NUM).setName("发放中数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(WAIT_GRANT_NUM, new QueryConditionDto().setCode(WAIT_GRANT_NUM).setName("待发放数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();

    ImmutableMap<String, QueryConditionDto> MATERIAL_RECEIPT_HEAD = new ImmutableMap.Builder<String, QueryConditionDto>()
//            .put(MATERIAL_NUM, new QueryConditionDto().setCode(MATERIAL_NUM).setName("耗材种数").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(RECEIPT_NUM, new QueryConditionDto().setCode(RECEIPT_NUM).setName("申请数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(GRANT_NUM, new QueryConditionDto().setCode(GRANT_NUM).setName("已发放数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(GRANTING_NUM, new QueryConditionDto().setCode(GRANTING_NUM).setName("发放中数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .put(WAIT_GRANT_NUM, new QueryConditionDto().setCode(WAIT_GRANT_NUM).setName("待发放数量").setType(FormFieldCO.NUMBER_INPUT).setFieldProps(new JSONObject()))
            .build();
}
