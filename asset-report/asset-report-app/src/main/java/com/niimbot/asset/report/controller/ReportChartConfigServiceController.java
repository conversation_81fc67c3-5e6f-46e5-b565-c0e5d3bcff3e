package com.niimbot.asset.report.controller;

import com.niimbot.asset.report.service.ReportChartConfigService;
import com.niimbot.report.ReportChartConfigDto;
import com.niimbot.report.ReportChartQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023/6/6 下午2:06
 */
@Api(tags = "报表图表类型配置")
@RestController
@RequestMapping("server/report/chart/")
public class ReportChartConfigServiceController {

    @Autowired
    private ReportChartConfigService reportChartConfigService;

    @ApiOperation("配置报表图表类型")
    @RequestMapping("config")
    public Boolean config(@RequestBody @Validated ReportChartConfigDto reportChartConfigDto) {
        return reportChartConfigService.config(reportChartConfigDto);
    }

    @ApiOperation("修改报表图表类型")
    @RequestMapping("edit")
    public Boolean edit(@RequestBody @Validated ReportChartConfigDto reportChartConfigDto) {
        return reportChartConfigService.edit(reportChartConfigDto);
    }

    @ApiOperation("查询所有的图表配置")
    @GetMapping("queryAll")
    public List<ReportChartConfigDto> queryAll() {
        return reportChartConfigService.queryAll();
    }

    @ApiOperation("根据条件查询报表支持图表")
    @GetMapping("queryByCondition")
    public List<ReportChartConfigDto> queryByCondition(ReportChartQueryDto queryDto) {
        return reportChartConfigService.queryByCondition(queryDto);
    }
}
