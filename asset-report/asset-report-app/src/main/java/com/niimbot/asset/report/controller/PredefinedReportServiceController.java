package com.niimbot.asset.report.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.report.service.PredefinedReportService;
import com.niimbot.report.AssetLogReportDto;
import com.niimbot.report.AssetLogReportQueryDto;
import com.niimbot.report.GroupReportResult;
import com.niimbot.report.HandleAssetLogDto;
import com.niimbot.report.HandleAssetLogQueryDto;
import com.niimbot.report.MaterialCategoryReportSearch;
import com.niimbot.report.MaterialRepositoryReportSearch;
import com.niimbot.report.MeansCategoryReportSearch;
import com.niimbot.report.RepairAssetLogDto;
import com.niimbot.report.RepairAssetLogQueryDto;
import com.niimbot.report.RepairAssetRecordDto;
import com.niimbot.report.RepairAssetRecordQueryDto;
import com.niimbot.report.UseOrgGroupReportSearch;
import com.niimbot.report.WaitHandleAssetLogDto;
import com.niimbot.report.WaitHandleAssetLogQueryDto;
import com.niimbot.report.WaitReturnAssetLogDto;
import com.niimbot.report.WaitReturnAssetLogQueryDto;
import com.niimbot.system.QueryConditionSortDto;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("server/report/predefined")
@RequiredArgsConstructor
public class PredefinedReportServiceController {

    private final PredefinedReportService predefinedReportService;

    @PostMapping("/means/sortField/{type}")
    public QueryConditionSortDto assetSortField(@PathVariable("type") String type) {
        return predefinedReportService.assetSortField(type);
    }

    @PostMapping("/means/useOrgFixedHeadReport")
    public GroupReportResult meansUseOrgFixedHeadReport(@RequestBody UseOrgGroupReportSearch search) {
        search.handleTime();
        return predefinedReportService.meansUseOrgFixedHeadReport(search);
    }

    @PostMapping("/means/categoryFixedHeadReport")
    public GroupReportResult meansCategoryFixedHeadReport(@RequestBody MeansCategoryReportSearch search) {
        search.handleTime();
        return predefinedReportService.meansCategoryFixedHeadReport(search);
    }

    @PostMapping("/material/categoryFixedHeadReport")
    public GroupReportResult materialCategoryFixedHeadReport(@RequestBody MaterialCategoryReportSearch search) {
        search.handleTime();
        return predefinedReportService.materialCategoryFixedHeadReport(search);
    }

    @PostMapping("/material/repositoryFixedHeadReport")
    public GroupReportResult materialRepositoryFixedHeadReport(@RequestBody MaterialRepositoryReportSearch search) {
        search.handleTime();
        return predefinedReportService.materialRepositoryFixedHeadReport(search);
    }

    @PostMapping(value = "/means/assetLogReport")
    public IPage<AssetLogReportDto> assetLogReport(@RequestBody AssetLogReportQueryDto queryDto) {
        return predefinedReportService.assetLogReport(queryDto);
    }

    @PostMapping(value = "/means/waitHandleAssetLog")
    public IPage<WaitHandleAssetLogDto> waitHandleAssetLog(@RequestBody WaitHandleAssetLogQueryDto queryDto) {
        return predefinedReportService.waitHandleAssetLog(queryDto);
    }

    @PostMapping(value = "/means/handleAssetLog")
    public IPage<HandleAssetLogDto> handleAssetLog(@RequestBody HandleAssetLogQueryDto queryDto) {
        return predefinedReportService.handleAssetLog(queryDto);
    }

    @PostMapping(value = "/means/waitReturnAssetLog")
    public IPage<WaitReturnAssetLogDto> waitReturnAssetLog(@RequestBody WaitReturnAssetLogQueryDto queryDto) {
        return predefinedReportService.waitReturnAssetLog(queryDto);
    }

    @PostMapping(value = "/means/repairAssetLog")
    public IPage<RepairAssetLogDto> repairAssetLog(@RequestBody RepairAssetLogQueryDto queryDto) {
        return predefinedReportService.repairAssetLog(queryDto);
    }

    @PostMapping(value = "/means/repairAssetRecord")
    public IPage<RepairAssetRecordDto> repairAssetRecord(@RequestBody RepairAssetRecordQueryDto queryDto) {
        return predefinedReportService.repairAssetRecord(queryDto);
    }

}
