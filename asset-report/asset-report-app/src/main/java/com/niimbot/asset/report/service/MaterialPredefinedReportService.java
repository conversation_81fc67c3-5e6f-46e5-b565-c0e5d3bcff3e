package com.niimbot.asset.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.report.MaterialCkManifestDto;
import com.niimbot.report.MaterialCkManifestQueryDto;
import com.niimbot.report.MaterialCkStatisticsDto;
import com.niimbot.report.MaterialCkStatisticsQueryDto;
import com.niimbot.report.MaterialManifestTotalDto;
import com.niimbot.report.MaterialRkManifestDto;
import com.niimbot.report.MaterialRkManifestQueryDto;
import com.niimbot.report.MaterialRkStatisticsDto;
import com.niimbot.report.MaterialRkStatisticsQueryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/23 11:31
 */
public interface MaterialPredefinedReportService {
    IPage<MaterialRkManifestDto> materialRkManifest(MaterialRkManifestQueryDto queryDto);

    MaterialManifestTotalDto materialRkManifestTotal(MaterialRkManifestQueryDto queryDto);

    IPage<MaterialCkManifestDto> materialCkManifest(MaterialCkManifestQueryDto queryDto);

    MaterialManifestTotalDto materialCkManifestTotal(MaterialCkManifestQueryDto queryDto);

    List<MaterialRkStatisticsDto> materialRkStatistics(MaterialRkStatisticsQueryDto queryDto);

    List<MaterialCkStatisticsDto> materialCkStatistics(MaterialCkStatisticsQueryDto queryDto);
}
