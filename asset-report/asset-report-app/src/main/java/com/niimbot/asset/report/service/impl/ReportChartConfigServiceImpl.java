package com.niimbot.asset.report.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.report.mapper.AsReportChartConfigMapper;
import com.niimbot.asset.report.model.AsReportChartConfig;
import com.niimbot.asset.report.service.ReportChartConfigService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.report.ReportChartConfigDto;
import com.niimbot.report.ReportChartQueryDto;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/6 下午2:05
 */
@Slf4j
@Service
public class ReportChartConfigServiceImpl extends ServiceImpl<AsReportChartConfigMapper, AsReportChartConfig> implements ReportChartConfigService {


    @Override
    public Boolean config(ReportChartConfigDto reportChartConfigDto) {
        AsReportChartConfig asReportChartConfig = new AsReportChartConfig();
        BeanUtils.copyProperties(reportChartConfigDto, asReportChartConfig);
        return this.save(asReportChartConfig);
    }

    @Override
    public Boolean edit(ReportChartConfigDto reportChartConfigDto) {
        AsReportChartConfig reportChartConfig = this.getById(reportChartConfigDto.getId());
        if (Objects.isNull(reportChartConfig)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "报表指标配置表不存在");
        }

        AsReportChartConfig modifyParam = new AsReportChartConfig();
        BeanUtils.copyProperties(reportChartConfigDto, modifyParam);
        return this.updateById(modifyParam);
    }

    @Override
    public List<ReportChartConfigDto> queryAll() {
        List<AsReportChartConfig> reportChartConfigList = this.list(Wrappers.lambdaQuery(AsReportChartConfig.class)
                .eq(AsReportChartConfig::getIsDelete, Boolean.FALSE));
        if (CollUtil.isNotEmpty(reportChartConfigList)) {
            return reportChartConfigList.stream().map(item -> {
                ReportChartConfigDto reportChartConfigDto = new ReportChartConfigDto();
                BeanUtils.copyProperties(item, reportChartConfigDto);
                return reportChartConfigDto;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<ReportChartConfigDto> queryByCondition(ReportChartQueryDto queryDto) {
        List<AsReportChartConfig> reportChartConfigList = this.list(Wrappers.lambdaQuery(AsReportChartConfig.class)
                .eq(AsReportChartConfig::getIsDelete, Boolean.FALSE));
        if (CollUtil.isEmpty(reportChartConfigList)) {
            return null;
        }

        List<ReportChartConfigDto> reportChartConfigDtoList = reportChartConfigList.stream().map(item -> {
            ReportChartConfigDto reportChartConfigDto = new ReportChartConfigDto();
            BeanUtils.copyProperties(item, reportChartConfigDto);
            return reportChartConfigDto;
        }).collect(Collectors.toList());

        //维度和指标都为空或都为0的情况下，返回所有可用图表类型
        if ((Objects.isNull(queryDto.getDimensionNum()) || queryDto.getDimensionNum() == 0)
                && (Objects.isNull(queryDto.getNormNum()) || queryDto.getNormNum() == 0)) {
            return reportChartConfigDtoList;
        }

        //维度不为空，按维度进行过滤
        if (Objects.nonNull(queryDto.getDimensionNum()) && queryDto.getDimensionNum() != 0) {
            reportChartConfigDtoList = reportChartConfigDtoList.stream()
                    .filter(item -> item.getDimensionNum().getMinNum() <= queryDto.getDimensionNum() && queryDto.getDimensionNum() <= item.getDimensionNum().getMaxNum())
                    .collect(Collectors.toList());
        }

        //指标不为空，按指标进行过滤
        if (Objects.nonNull(queryDto.getNormNum()) && queryDto.getNormNum() != 0) {
            reportChartConfigDtoList = reportChartConfigDtoList.stream()
                    .filter(item -> queryDto.getNormNum() >= item.getNormNum().getMinNum() && queryDto.getNormNum() <= item.getNormNum().getMaxNum())
                    .collect(Collectors.toList());
        }
        return reportChartConfigDtoList;
    }
}
