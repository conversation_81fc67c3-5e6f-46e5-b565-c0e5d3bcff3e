package com.niimbot.asset.report.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/6/6 下午2:10
 * 报表图表类型枚举
 */
@Getter
public enum ReportChartTypeEnum {
    BASIC_LINE_CHART(1, "基础折线图"),
    DUAL_AXIS_LINE_CHART(2, "双轴折线图"),
    BASIC_HISTOGRAM(3, "基础柱状图"),
    STACKED_COLUMN_CHART(4, "基础柱状图"),
    BASIC_BAR_CHART(5, "基础条形图"),
    STACKED_BAR_CHART(6, "堆叠条形图"),
    STATISTICS_TABLE(7, "数据统计表"),
    DETAIL_TABLE(8, "明细表"),
    ;

    ReportChartTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;
}
