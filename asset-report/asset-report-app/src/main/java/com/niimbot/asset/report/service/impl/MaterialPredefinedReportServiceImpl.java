package com.niimbot.asset.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.report.mapper.MaterialPredefinedReportMapper;
import com.niimbot.asset.report.service.MaterialPredefinedReportService;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.report.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/23 11:32
 */
@Service
public class MaterialPredefinedReportServiceImpl implements MaterialPredefinedReportService {

    @Autowired
    private MaterialPredefinedReportMapper predefinedReportMapper;
    @Autowired
    private DataScopeStrategyManager dataScopeManager;

    @Override
    public IPage<MaterialRkManifestDto> materialRkManifest(MaterialRkManifestQueryDto queryDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        List<Long> ids = queryDto.getIncludeIds();
        if (CollUtil.isNotEmpty(ids)) {
            queryDto = new MaterialRkManifestQueryDto();
            queryDto.setIncludeIds(ids);
        }
        // 手工仓库权限
        String storeSql = dataScopeManager.simplePermsSql(DataPermType.STORE);
        return predefinedReportMapper.materialRkManifest(queryDto.buildIPage(), queryDto, companyId, storeSql);
    }

    @Override
    public MaterialManifestTotalDto materialRkManifestTotal(MaterialRkManifestQueryDto queryDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        List<Long> ids = queryDto.getIncludeIds();
        if (CollUtil.isNotEmpty(ids)) {
            queryDto = new MaterialRkManifestQueryDto();
            queryDto.setIncludeIds(ids);
        }
        String storeSql = dataScopeManager.simplePermsSql(DataPermType.STORE);
        MaterialManifestTotalDto materialManifestTotalDto = predefinedReportMapper.materialRkManifestTotal(queryDto, companyId, storeSql);
        if (materialManifestTotalDto == null) {
            materialManifestTotalDto = new MaterialManifestTotalDto();
            materialManifestTotalDto.setTotalMoney(BigDecimal.ZERO);
            materialManifestTotalDto.setTotalNum(BigDecimal.ZERO);
        } else {
            materialManifestTotalDto.setTotalMoney(materialManifestTotalDto.getTotalMoney().setScale(4, RoundingMode.HALF_UP));
        }
        return materialManifestTotalDto;
    }

    @Override
    public IPage<MaterialCkManifestDto> materialCkManifest(MaterialCkManifestQueryDto queryDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        List<Long> ids = queryDto.getIncludeIds();
        if (CollUtil.isNotEmpty(ids)) {
            queryDto = new MaterialCkManifestQueryDto();
            queryDto.setIncludeIds(ids);
        }
        String storeSql = dataScopeManager.simplePermsSql(DataPermType.STORE);
        return predefinedReportMapper.materialCkManifest(queryDto.buildIPage(), queryDto, companyId, storeSql);
    }

    @Override
    public MaterialManifestTotalDto materialCkManifestTotal(MaterialCkManifestQueryDto queryDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        List<Long> ids = queryDto.getIncludeIds();
        if (CollUtil.isNotEmpty(ids)) {
            queryDto = new MaterialCkManifestQueryDto();
            queryDto.setIncludeIds(ids);
        }
        String storeSql = dataScopeManager.simplePermsSql(DataPermType.STORE);
        MaterialManifestTotalDto materialManifestTotalDto = predefinedReportMapper.materialCkManifestTotal(queryDto, companyId, storeSql);
        if (materialManifestTotalDto == null) {
            materialManifestTotalDto = new MaterialManifestTotalDto();
            materialManifestTotalDto.setTotalMoney(BigDecimal.ZERO);
            materialManifestTotalDto.setTotalNum(BigDecimal.ZERO);
        } else {
            materialManifestTotalDto.setTotalMoney(materialManifestTotalDto.getTotalMoney().setScale(4, RoundingMode.HALF_UP));
        }
        return materialManifestTotalDto;
    }

    @Override
    public List<MaterialRkStatisticsDto> materialRkStatistics(MaterialRkStatisticsQueryDto queryDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String storeSql = dataScopeManager.simplePermsSql(DataPermType.STORE);
        List<MaterialRkStatisticsDto> statistics = predefinedReportMapper.materialRkStatistics(queryDto, companyId, storeSql);
        if (!statistics.isEmpty()) {
            // 合计
            BigDecimal rkNum = BigDecimal.ZERO;
            BigDecimal rkPrice = BigDecimal.ZERO;
            for (MaterialRkStatisticsDto statistic : statistics) {
                if (statistic.getRkNum() != null) {
                    rkNum = rkNum.add(statistic.getRkNum());
                }
                if (statistic.getRkPrice() != null) {
                    rkPrice = rkPrice.add(statistic.getRkPrice());
                }
            }
            MaterialRkStatisticsDto statisticsDto = new MaterialRkStatisticsDto();
            statisticsDto.setRkTime("总计");
            statisticsDto.setRkNum(rkNum);
            statisticsDto.setRkPrice(rkPrice);
            statistics.add(statisticsDto);
        }
        return statistics;
    }

    @Override
    public List<MaterialCkStatisticsDto> materialCkStatistics(MaterialCkStatisticsQueryDto queryDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        String storeSql = dataScopeManager.simplePermsSql(DataPermType.STORE);
        List<MaterialCkStatisticsDto> statistics = predefinedReportMapper.materialCkStatistics(queryDto, companyId, storeSql);
        if (!statistics.isEmpty()) {
            // 合计
            BigDecimal ckNum = BigDecimal.ZERO;
            BigDecimal ckPrice = BigDecimal.ZERO;
            for (MaterialCkStatisticsDto statistic : statistics) {
                if (statistic.getCkNum() != null) {
                    ckNum = ckNum.add(statistic.getCkNum());
                }
                if (statistic.getCkPrice() != null) {
                    ckPrice = ckPrice.add(statistic.getCkPrice());
                }
            }
            MaterialCkStatisticsDto statisticsDto = new MaterialCkStatisticsDto();
            statisticsDto.setCkTime("总计");
            statisticsDto.setCkNum(ckNum);
            statisticsDto.setCkPrice(ckPrice);
            statistics.add(statisticsDto);
        }
        return statistics;
    }
}
