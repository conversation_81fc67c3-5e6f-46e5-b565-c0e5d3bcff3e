package com.niimbot.asset.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.report.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/23 11:34
 */
public interface MaterialPredefinedReportMapper {

    IPage<MaterialRkManifestDto> materialRkManifest(Page<Object> objectPage,
                                                    @Param("ew") MaterialRkManifestQueryDto queryDto,
                                                    @Param("companyId") Long companyId,
                                                    @Param("storeSql") String storeSql);

    MaterialManifestTotalDto materialRkManifestTotal(@Param("ew") MaterialRkManifestQueryDto queryDto,
                                                     @Param("companyId") Long companyId,
                                                     @Param("storeSql") String storeSql);

    IPage<MaterialCkManifestDto> materialCkManifest(Page<Object> objectPage,
                                                    @Param("ew") MaterialCkManifestQueryDto queryDto,
                                                    @Param("companyId") Long companyId,
                                                    @Param("storeSql") String storeSql);

    MaterialManifestTotalDto materialCkManifestTotal(@Param("ew") MaterialCkManifestQueryDto queryDto,
                                                     @Param("companyId") Long companyId,
                                                     @Param("storeSql") String storeSql);

    List<MaterialRkStatisticsDto> materialRkStatistics(@Param("ew") MaterialRkStatisticsQueryDto queryDto,
                                                       @Param("companyId") Long companyId,
                                                       @Param("storeSql") String storeSql);

    List<MaterialCkStatisticsDto> materialCkStatistics(@Param("ew") MaterialCkStatisticsQueryDto queryDto,
                                                       @Param("companyId") Long companyId,
                                                       @Param("storeSql") String storeSql);
}
