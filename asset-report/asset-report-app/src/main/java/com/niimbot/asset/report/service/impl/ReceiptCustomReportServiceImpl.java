package com.niimbot.asset.report.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.material.resolver.MySqlMaterialOrderQueryConditionResolver;
import com.niimbot.asset.means.resolver.MySqlAssetQueryConditionResolver;
import com.niimbot.asset.report.component.AssetReportUtil;
import com.niimbot.asset.report.component.MaterialReportOrderQueryConditionResolver;
import com.niimbot.asset.report.constants.ReportAssetConstant;
import com.niimbot.asset.report.constants.ReportConstant;
import com.niimbot.asset.report.enums.DimensionDisplayTypeEnum;
import com.niimbot.asset.report.mapper.AssetReportMapper;
import com.niimbot.asset.report.service.DimensionGroupService;
import com.niimbot.asset.report.service.DynamicFieldService;
import com.niimbot.asset.report.service.ReceiptCustomReportService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.report.AssetStatisticsDataDto;
import com.niimbot.report.CustomReportConfigDto;
import com.niimbot.report.DimensionDataDto;
import com.niimbot.report.DimensionDataItemDto;
import com.niimbot.report.DimensionItemDto;
import com.niimbot.report.NormItemDto;
import com.niimbot.report.ReportQueryFieldDto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.TreeSet;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/19 下午3:40
 */
@Slf4j
@Service
public class ReceiptCustomReportServiceImpl implements ReceiptCustomReportService {
    @Autowired
    private DimensionGroupService dimensionGroupService;
    @Autowired
    private AssetReportMapper assetReportMapper;
    @Autowired
    private AssetReportUtil assetReportUtil;
    @Autowired
    private MaterialReportOrderQueryConditionResolver materialReportOrderQueryConditionResolver;
    @Autowired
    private DataScopeStrategyManager dataScopeStrategyManager;
    @Autowired
    private DynamicFieldService dynamicFieldService;

    @Override
    public DimensionDataDto reportData(CustomReportConfigDto customReportConfigDto) {
        //处理维度和指标数据
        DimensionDataDto result = new DimensionDataDto();
        result.setReportId(customReportConfigDto.getId());
        result.setReportName(customReportConfigDto.getReportName());
        result.setNormName(customReportConfigDto.getNorm().stream().map(NormItemDto::getName).collect(Collectors.toList()));

        //生成查询SQL
        ReportQueryFieldDto queryFieldDto = generateQuerySqlParam(customReportConfigDto);
        String queryParamStr = JSONObject.toJSONString(queryFieldDto);
        log.info("receiptCustomReportService reportData companyId=[{}] queryParam=[{}]", LoginUserThreadLocal.getCompanyId(), queryParamStr);
        JSONObject queryParam = JSONObject.parseObject(queryParamStr);

        //执行SQL查询
        List<JSONObject> stockDataList = assetReportMapper.receiptCustomReport(queryParam);
        if (CollUtil.isEmpty(stockDataList)) {
            result.setDimensionData(Collections.emptyList());
            return result;
        }

        //设置其他属性
        assembleOtherField(stockDataList, customReportConfigDto);

        //耗材档案的不参与维度分组展示
        List<DimensionItemDto> dimensionItemDtoList = customReportConfigDto.getDimension().stream()
                .filter(item -> !DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType()))
                .collect(Collectors.toList());

        //处理数据展示
        List<DimensionDataItemDto> data = dimensionGroupService.processDimensionGroup(dimensionItemDtoList, stockDataList);

        //去重后的维度
        List<DimensionItemDto> distinctDimensionList = customReportConfigDto.getDimension().stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DimensionItemDto::getCode))), ArrayList::new));

        //id转name
        assetReportUtil.translateAssetJsonView(stockDataList, distinctDimensionList);
        result.setDimensionData(data);
        return result;
    }

    @Override
    public AssetStatisticsDataDto statisticsReportData(CustomReportConfigDto customReportConfigDto) {
        //耗材领用动态表单字段
        List<String> materialReceiptField = dynamicFieldService.materialOrderFieldCode(AssetConstant.ORDER_TYPE_MATERIAL_LY);

        //生成分页查询sql
        ReportQueryFieldDto queryFieldDto = generateStatisticsQuerySql(customReportConfigDto, materialReceiptField);
        //塞一个id字段查询，作为详情接口参数使用
        queryFieldDto.setFieldStr(String.join(",", "l.material_id as materialId", "l.detail_id as detailId", queryFieldDto.getFieldStr()));

        String queryParamStr = JSONObject.toJSONString(queryFieldDto);
        log.info("receiptCustomReportService statisticsReportData companyId=[{}] queryParam=[{}]", LoginUserThreadLocal.getCompanyId(), queryParamStr);
        List<JSONObject> statisticsDataList = assetReportMapper.receiptCustomReport(JSONObject.parseObject(queryParamStr));

        // 补齐退库数量
        Map<String, Integer> tkMaterialCountMap = new HashMap<>();
        List<String> lyOrderNos = statisticsDataList.stream().map(f -> f.getString("orderNo")).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(lyOrderNos)) {
            List<Map<String, Object>> tkMaterialCount = assetReportMapper.countTkNumByLyOrder(lyOrderNos, LoginUserThreadLocal.getCompanyId());
            tkMaterialCountMap = tkMaterialCount.stream().collect(Collectors.toMap(f -> Convert.toStr(f.get("lyOrderNo")) + "_" + Convert.toStr(f.get("materialId")),
                    f -> Convert.toInt(f.get("tkNum")), (k1, k2) -> k1));
        }

        for (JSONObject jsonObject : statisticsDataList) {
            String key = Convert.toStr(jsonObject.get("orderNo")) + "_" + Convert.toStr(jsonObject.get("materialId"));
            Integer tkNum = tkMaterialCountMap.getOrDefault(key, 0);
            jsonObject.put("tkNum", tkNum);
        }

        //生成汇总查询sql
        ReportQueryFieldDto summary = generateSummaryQuerySql(customReportConfigDto, materialReceiptField);
        String summaryQueryParamStr = JSONObject.toJSONString(summary);
        log.info("receiptCustomReportService statisticsReportData summary companyId=[{}] summaryQueryParam=[{}]", LoginUserThreadLocal.getCompanyId(), summaryQueryParamStr);
        List<JSONObject> summaryList = assetReportMapper.receiptCustomReport(JSONObject.parseObject(summaryQueryParamStr));

        //生成结果数据
        AssetStatisticsDataDto result = new AssetStatisticsDataDto();
        result.setDimension(customReportConfigDto.getDimension());
        //id -> name转换
        assetReportUtil.translateAssetJsonView(statisticsDataList, customReportConfigDto.getDimension());
        result.setList(statisticsDataList);
        result.setCurrent(customReportConfigDto.getCurrent());
        result.setSize(customReportConfigDto.getSize());
        result.setTotalCount(summaryList.get(0).getInteger(ReportConstant.TOTAL));
        result.setTotalPage((int)Math.ceil((double)result.getTotalCount() / (double)result.getSize()));

        //处理汇总字段名称和值
        Map<String, String> summaryData = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(customReportConfigDto.getSummary())) {
            for (NormItemDto item : customReportConfigDto.getSummary()) {
                summaryData.put(item.getName(), summaryList.get(0).getString(item.getCode()));
            }
        }
        result.setSummary(summaryData);
        return result;
    }

    /**
     * 生成资产查询参数对象
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateQuerySqlParam(CustomReportConfigDto configDto) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();

        //耗材领用动态表单字段
        List<String> materialReceiptField = dynamicFieldService.materialOrderFieldCode(AssetConstant.ORDER_TYPE_MATERIAL_LY);

        //生成指标 select 查询字段
        String norm = resolveNorm(configDto.getNorm());
        //生成groupBy中字段到select查询字段中
        String dimension = resolveGroupByAsField(configDto, Boolean.FALSE);
        //select字段：groupBy中的字段 + 指标字段
        result.setFieldStr(String.join(", ", dimension, norm));
        //生成where查询条件
        String conditionStr = "";
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            conditionStr = String.join(" ",conditionStr,
                    materialReportOrderQueryConditionResolver.resolveQueryCondition("l", configDto.getStatisticCondition(), materialReceiptField));
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            conditionStr = String.join(" ", conditionStr,
                    materialReportOrderQueryConditionResolver.resolveQueryCondition("l", configDto.getDimensionQueryCondition(), materialReceiptField));
        }
        result.setConditionStr(conditionStr);
        //kw关键字搜索
        result.setKw(configDto.getKw());
        //设置数据权限
        String permsSql = dataScopeStrategyManager.generalWhereSql("com.niimbot.asset.material.mapper.AsMaterialLyOrderMapper", "l");
        result.setPermSql(permsSql);
        //生成groupBy查询分组条件
        result.setGroupStr(resolveGroupBy(configDto));
        //如果有排序，生成order by语句
        String orderStr = resolveSortString(configDto);
        if (StrUtil.isNotBlank(orderStr)) {
            result.setOrderStr(orderStr);
        }
        return result;
    }

    /**
     * 解析资产数据指标查询
     * @param norm
     * @return
     */
    private String resolveNorm(List<NormItemDto> norm) {
        StringJoiner result = new StringJoiner("");
        for (NormItemDto item : norm) {
            if (ReportAssetConstant.MATERIAL_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.COUNT_TPL, "l.material_id")).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.RECEIPT_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.SUM_TPL, "l.ly_num")).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.GRANT_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.SUM_TPL, "l.grant_num")).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.GRANTING_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.SUM_TPL, "l.granting_num")).add(" as ").add(item.getCode());
            } else if (ReportAssetConstant.WAIT_GRANT_NUM.equalsIgnoreCase(item.getCode())) {
                result.add(", ").add(String.format(ReportConstant.SUM_TPL, "l.wait_grant_num")).add(" as ").add(item.getCode());
            }
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析明细表groupBy语句，将groupBy中的字段添加到select中
     * @param configDto
     * @return
     */
    private String resolveDetailGroupByAsField(CustomReportConfigDto configDto, Boolean containField, List<String> materialReceiptField) {
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            //耗材档案
            if (!containField && DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())) {
                continue;
            }
            String fieldStr = null;
            if (ReportAssetConstant.MATERIAL_CODE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            } else if (ReportAssetConstant.MATERIAL_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = "count(distinct l.material_id)";
            } else if (ReportAssetConstant.RECEIPT_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "ly_num");
            } else if (ReportAssetConstant.GRANT_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "grant_num");
            } else if (ReportAssetConstant.GRANTING_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "granting_num");
            } else if (ReportAssetConstant.WAIT_GRANT_NUM.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "wait_grant_num");
            } else if (QueryFieldConstant.FIELD_ORDER_NO.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_CREATE_BY.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_CREATE_TIME.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_APPROVE_STATUS.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_GRANT_STATUS.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", StrUtil.toUnderlineCase(item.getCode()));
            } else if (materialReceiptField.contains(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            } else {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                if (AssetConstant.ASSET_FIELD_CREATE_TIME.equalsIgnoreCase(item.getCode())) {
                    fieldStr = timeFormatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                } else {
                    fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                }
            }
            result.add(", ").add(fieldStr).add(" as ").add(item.getCode());
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 解析维度groupBy语句，将groupBy中的字段添加到select中
     * @param configDto
     * @return
     */
    private String resolveGroupByAsField(CustomReportConfigDto configDto, Boolean containField) {
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            //耗材档案
            if (!containField && DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())) {
                continue;
            }
            String fieldStr = null;
            if (ReportAssetConstant.MATERIAL_CODE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            } else if (ReportAssetConstant.MATERIAL_CATEGORY.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            } else if (QueryFieldConstant.FIELD_ORDER_NO.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_CREATE_BY.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_CREATE_TIME.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_APPROVE_STATUS.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_GRANT_STATUS.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", StrUtil.toUnderlineCase(item.getCode()));
            } else {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                if (AssetConstant.ASSET_FIELD_CREATE_TIME.equalsIgnoreCase(item.getCode())) {
                    fieldStr = timeFormatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                } else {
                    fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                }
            }
            result.add(", ").add(fieldStr).add(" as ").add(item.getCode());
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 处理日期类型group条件
     * @param fieldStr
     * @param groupCondition
     * @return
     */
    private String formatDateGroup(String fieldStr, Integer groupCondition) {
        if (groupCondition == 1) {
            //1-按年分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_YEAR_TPL);
        } else if (groupCondition == 2) {
            //2-按月分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_MONTH_TPL);
        } else {
            //3-按天分组
            return String.format(MySqlAssetQueryConditionResolver.UNIT_TIME_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_DAY_TPL);
        }
    }

    /**
     * 处理日期类型group条件
     * @param fieldStr
     * @param groupCondition
     * @return
     */
    private String timeFormatDateGroup(String fieldStr, Integer groupCondition) {
        if (groupCondition == 1) {
            //1-按年分组
            return String.format(MySqlAssetQueryConditionResolver.TIME_STAMP_DATE_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_YEAR_TPL);
        } else if (groupCondition == 2) {
            //2-按月分组
            return String.format(MySqlAssetQueryConditionResolver.TIME_STAMP_DATE_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_MONTH_TPL);
        } else {
            //3-按天分组
            return String.format(MySqlAssetQueryConditionResolver.TIME_STAMP_DATE_FORMAT_TPL, fieldStr, MySqlAssetQueryConditionResolver.DATE_FORMAT_DAY_TPL);
        }
    }

    /**
     * 解析维度groupBy语句
     * @param configDto
     * @return
     */
    private String resolveGroupBy(CustomReportConfigDto configDto) {
        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            //耗材档案
            if (DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())) {
                continue;
            }
            String fieldStr = null;
            if (ReportAssetConstant.MATERIAL_CODE.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            } else if (QueryFieldConstant.FIELD_ORDER_NO.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_CREATE_BY.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_CREATE_TIME.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_APPROVE_STATUS.equalsIgnoreCase(item.getCode())
                    || QueryFieldConstant.FIELD_GRANT_STATUS.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", StrUtil.toUnderlineCase(item.getCode()));
            } else if (ReportAssetConstant.MATERIAL_CATEGORY.equalsIgnoreCase(item.getCode())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            } else {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "l", item.getCode());
            }

            //日期类型的，进行日期格式转换
            if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                if (AssetConstant.ASSET_FIELD_CREATE_TIME.equalsIgnoreCase(item.getCode())) {
                    fieldStr = timeFormatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                } else {
                    fieldStr = formatDateGroup(fieldStr, (Integer) item.getGroupCondition());
                }
            }
            result.add(", ").add(fieldStr);
        }
        return result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
    }

    /**
     * 设置耗材档案其他字段属性
     * @param stockDataList
     * @param configDto
     */
    private void assembleOtherField(List<JSONObject> stockDataList, CustomReportConfigDto configDto) {
        Map<String, JSONObject> materialFieldMap = new HashMap<>();

        //获取耗材编码
        List<String> materialCodeList = stockDataList.stream().map(item -> item.getString(ReportAssetConstant.MATERIAL_CODE)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(materialCodeList)) {
            return ;
        }

        StringJoiner result = new StringJoiner("");
        for (DimensionItemDto item : configDto.getDimension()) {
            //耗材档案
            if (DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())) {
                String fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", item.getCode());
                if (item.getType().equalsIgnoreCase(FormFieldCO.DATETIME)) {
                    //默认按年月日展示
                    fieldStr = formatDateGroup(fieldStr, 3);
                }
                result.add(", ").add(fieldStr).add(" as ").add(item.getCode());
            }
            //日期类型的，进行日期格式转换
        }
        String otherFieldQueryStr = result.length() > 0 ? StrUtil.sub(result.toString(), 1, result.toString().length()) : "";
        if (StrUtil.isBlank(otherFieldQueryStr)) {
            return ;
        }

        JSONObject queryParam = new JSONObject();
        queryParam.put("fieldStr", otherFieldQueryStr);
        queryParam.put(ReportAssetConstant.MATERIAL_CODE, materialCodeList);
        queryParam.put("companyId", LoginUserThreadLocal.getCompanyId());
        List<JSONObject> otherFieldList = assetReportMapper.selectMaterialOtherFieldReceipt(queryParam);
        if (CollUtil.isEmpty(otherFieldList)) {
            return ;
        }

        //耗材分组
        materialFieldMap = otherFieldList.stream()
                .collect(Collectors.toMap(item -> item.getString(ReportAssetConstant.MATERIAL_CODE), value -> value, (v1, v2) -> v1));
        for (JSONObject item : stockDataList) {
            if (Objects.nonNull(materialFieldMap.get(item.getString(ReportAssetConstant.MATERIAL_CODE)))) {
                item.putAll(materialFieldMap.get(item.getString(ReportAssetConstant.MATERIAL_CODE)));
            }
        }
    }

    /**
     * 生成明细表查询字段
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateStatisticsQuerySql(CustomReportConfigDto configDto, List<String> materialReceiptField) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();
        //设置勾选明细id
        result.setDetailId(configDto.getDetailId());
        result.setFieldStr(resolveDetailGroupByAsField(configDto, Boolean.TRUE, materialReceiptField));
        //生成where查询条件
        String conditionStr = "";
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            conditionStr = String.join(" ", conditionStr,
                    materialReportOrderQueryConditionResolver.resolveQueryCondition("l", configDto.getStatisticCondition(), materialReceiptField));
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            conditionStr = String.join(" ", conditionStr,
                    materialReportOrderQueryConditionResolver.resolveQueryCondition("l", configDto.getDimensionQueryCondition(), materialReceiptField));
        }
        result.setConditionStr(conditionStr);

        //设置数据权限
        String permsSql = dataScopeStrategyManager.generalWhereSql("com.niimbot.asset.material.mapper.AsMaterialLyOrderMapper", "l");
        result.setPermSql(permsSql);

        //设置关键字搜索
        result.setKw(configDto.getKw());

        //如果有排序，生成order by语句
        String orderStr = resolveSortString(configDto);
        if (StrUtil.isNotBlank(orderStr)) {
            result.setOrderStr(orderStr);
        }

        //生成分页limit sql
        long start = (configDto.getCurrent() - 1) * configDto.getSize();
        String limitStr = String.join(", ", String.valueOf(start), String.valueOf(configDto.getSize()));
        result.setLimitStr(limitStr);
        return result;
    }

    /**
     * 生成明细表汇总sql
     * @param configDto
     * @return
     */
    private ReportQueryFieldDto generateSummaryQuerySql(CustomReportConfigDto configDto, List<String> materialReceiptField) {
        ReportQueryFieldDto result = new ReportQueryFieldDto();
        //设置勾选明细id
        result.setDetailId(configDto.getDetailId());
        String summarySql = "count(l.material_id) as total";
        if (CollUtil.isNotEmpty(configDto.getSummary())) {
            result.setFieldStr(String.join(",", summarySql, resolveNorm(configDto.getSummary())));
        } else {
            result.setFieldStr(summarySql);
        }
        //生成where查询条件
        String conditionStr = "";
        result.setCompanyId(LoginUserThreadLocal.getCompanyId());
        if (CollUtil.isNotEmpty(configDto.getStatisticCondition())) {
            conditionStr = String.join(" ",conditionStr,
                    materialReportOrderQueryConditionResolver.resolveQueryCondition("l", configDto.getStatisticCondition(), materialReceiptField));
        }
        //处理维度筛选条件，生成where条件查询
        if (CollUtil.isNotEmpty(configDto.getDimensionQueryCondition())) {
            conditionStr = String.join(" ", conditionStr,
                    materialReportOrderQueryConditionResolver.resolveQueryCondition("l", configDto.getDimensionQueryCondition(), materialReceiptField));
        }
        result.setConditionStr(conditionStr);

        //设置数据权限
        String permsSql = dataScopeStrategyManager.generalWhereSql("com.niimbot.asset.material.mapper.AsMaterialLyOrderMapper", "l");
        result.setPermSql(permsSql);

        //设置关键字搜索
        result.setKw(configDto.getKw());
        return result;
    }

    /**
     * 处理排序条件字符串
     * @param configDto
     * @return
     */
    private String resolveSortString(CustomReportConfigDto configDto) {
        if (Objects.isNull(configDto.getSortCondition()) || StrUtil.isBlank(configDto.getSortCondition().getSidx())) {
            return null;
        }

        //排序字段在维度里面有，表示select已经查询出来了，直接用这个字段排序就可以了
        Optional<DimensionItemDto> dimensionItemOptional = configDto.getDimension()
                .stream()
                .filter(item -> !DimensionDisplayTypeEnum.MATERIAL.getCode().equals(item.getDisplayType())
                        && item.getCode().equalsIgnoreCase(configDto.getSortCondition().getSidx()))
                .findAny();
        if (dimensionItemOptional.isPresent()) {
            return String.join(" ", configDto.getSortCondition().getSidx(), configDto.getSortCondition().getOrder());
        } else {
            String fieldStr = null;
            if (ReportAssetConstant.MATERIAL_CODE.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", configDto.getSortCondition().getSidx());
            } else if (ReportAssetConstant.RECEIPT_NUM.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "ly_num");
            } else if (ReportAssetConstant.GRANT_NUM.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "grant_num");
            } else if (ReportAssetConstant.GRANTING_NUM.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "granting_num");
            } else if (ReportAssetConstant.WAIT_GRANT_NUM.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", "wait_grant_num");
            } else if (QueryFieldConstant.FIELD_ORDER_NO.equalsIgnoreCase(configDto.getSortCondition().getSidx())
                    || QueryFieldConstant.FIELD_CREATE_BY.equalsIgnoreCase(configDto.getSortCondition().getSidx())
                    || QueryFieldConstant.FIELD_CREATE_TIME.equalsIgnoreCase(configDto.getSortCondition().getSidx())
                    || QueryFieldConstant.FIELD_APPROVE_STATUS.equalsIgnoreCase(configDto.getSortCondition().getSidx())
                    || QueryFieldConstant.FIELD_GRANT_STATUS.equalsIgnoreCase(configDto.getSortCondition().getSidx())) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.SQL_SEGMENT_TPL, "l", StrUtil.toUnderlineCase(configDto.getSortCondition().getSidx()));
            } else if (dynamicFieldService.isMaterialOrderFieldCode(configDto.getSortCondition().getSidx(), AssetConstant.ORDER_TYPE_MATERIAL_LY)) {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, "l", configDto.getSortCondition().getSidx());
            } else {
                fieldStr = String.format(MySqlMaterialOrderQueryConditionResolver.DETAIL_JSON_SQL_SEGMENT_TPL, "l", configDto.getSortCondition().getSidx());
            }
            return String.join(" ", fieldStr, configDto.getSortCondition().getOrder());
        }
    }
}
