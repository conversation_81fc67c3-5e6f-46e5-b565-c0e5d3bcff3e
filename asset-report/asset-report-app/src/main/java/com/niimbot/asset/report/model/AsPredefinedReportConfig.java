package com.niimbot.asset.report.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * as_predefined_report
 * <AUTHOR>
@Data
@Accessors
@TableName(value = "as_predefined_report_config", autoResultMap = true)
public class AsPredefinedReportConfig implements Serializable {

    private static final long serialVersionUID = -5106318768157406800L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 预定义报表名称
     */
    private String reportName;

    /**
     * 业务类型:11-履历, 12-资产增减
     */
    private Integer bizType;

    /**
     * 报表类型:9-预定义报表
     */
    private Integer type;

    /**
     * 是否删除: 0未删除 1 已删除
     */
    private Boolean isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}