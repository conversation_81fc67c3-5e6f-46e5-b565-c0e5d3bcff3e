{"totalPage": 1, "isCable": 0, "margin": [0, 0, 0, 2], "cableDirection": -1, "currentPage": 1, "cableLength": 0, "elements": [{"id": "f97754207e68fa34998756b957717e8b", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 39.875}, {"id": "96aed166da1bebb54b3339aa5e49f213", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 33}, {"id": "9889dbb39d7d82d71f5569e4bd19356e", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 8, "textAlign": "left", "width": 65.425, "height": 0.3, "x": -30.25, "y": 34.65}, {"id": "7e5c4825a474eea2b47943edea1b4fce", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 8, "textAlign": "left", "width": 65.425, "height": 0.3, "x": 15.25, "y": 34.65}, {"id": "9f0c67e8a6008955aae2afc0a800f747", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 46.75}, {"id": "6e0953e1dbedb694956d3bf9934dbc45", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 53.625}, {"id": "2af77d2bbc806cdfc1efb081f95b8242", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 67.25}, {"id": "f91d886c5bb3502f1d2df7578697c644", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 60.5}, {"id": "ada7d831771190a6d948c470ae31c9ee", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 19.25}, {"id": "aa6ef16b047728de561b242083c78f76", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 26.125}, {"id": "eadf5d361882cf8ea3dc18dc2f13df10", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 2.375, "y": 2}, {"id": "0103420fc27ef64c86d6905988650f2a", "type": "qrcode", "value": "123456789", "correctLevel": 0, "codeType": 31, "width": 14.375, "height": 14.375, "rotate": 0, "zIndex": 10, "textAlign": "left", "x": 17.75, "y": 3.5}, {"id": "442a2c7f168c027828b522b004ed0213", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 42.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "x": 3.5, "y": 20.25}, {"id": "a88b7f4b4a3eacb7d8ea8dac52d402d8", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 42.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "x": 3.5, "y": 27.25}, {"id": "8be25b6282fe41d3cb8a682bc16983ca", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 42.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "x": 3.5, "y": 34.25}, {"id": "de1e26316e379eb594c2bec4976a90bd", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 42.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "x": 3.5, "y": 41}, {"id": "21527cfe833c667692cf3bc07b3fd5e6", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 42.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "x": 3.5, "y": 47.75}, {"id": "3248f1cb7070ffc7903b66ad5fe54200", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 42.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "x": 3.5, "y": 54.75}, {"id": "4893611b9d5d801c8fbc2354ae76ea25", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 42.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "x": 3.5, "y": 61.75}], "name": "新建标签", "rotate": 0, "id": "1597309348000", "consumableType": 1, "height": 70, "width": 50, "paperType": 1, "usedFonts": {"Arail": "Arail.ttf", "ZT002": "ZT002.ttf", "fontDefault": "ZT002.ttf"}, "unit": "mm"}