{"totalPage": 1, "isCable": 0, "margin": [0, 0, 0, 2], "cableDirection": -1, "currentPage": 1, "cableLength": 0, "elements": [{"uid": "w7ceca8a7c34c14f0d71a9a01d97b06bf", "id": "w7ceca8a7c34c14f0d71a9a01d97b06bf", "x": -7.903893402125449, "y": 29.504655519852083, "locked": false, "zIndex": 1, "type": "text", "name": "text", "fontSize": 2.6, "value": "", "fontFamily": "ZT002", "lineMode": 6, "letterSpacing": 0, "lineSpacing": 0, "fontStyle": [], "textAlignHorizonral": 0, "zoomable": "w, e", "textAlignVertical": 1, "isTitle": false, "rotatePoint": 2, "fontCode": "ZT002", "useTitle": false, "height": 3.3022861981371716, "width": 54.38187976291278, "rotate": 90, "itemType": "widget", "printMultiple": 11.81, "aspectRatio": 0, "fontColor": [0, 0, 0], "isLock": 0, "wordSpacing": 0, "centralPoint": 1}, {"uid": "w1821e4b812e367098ad3740aba4fcf2a", "id": "w1821e4b812e367098ad3740aba4fcf2a", "x": -15.216679176892596, "y": 30.209058568116266, "locked": false, "zIndex": 1, "type": "line", "name": "horizontal", "lineType": 1, "dashwidth": [0.75, 0.75], "zoomable": "w, e", "rotatePoint": 5, "useTitle": false, "height": 0.2, "width": 59.5, "rotate": 90, "itemType": "widget", "fontStyle": [], "printMultiple": 11.81, "isLock": 0, "lineWidth": 0.2}, {"uid": "w5525326c8aed69fa5da10eb97b472ffe", "id": "w5525326c8aed69fa5da10eb97b472ffe", "x": -6, "y": 21, "locked": false, "zIndex": 1, "type": "text", "name": "text", "fontSize": 2.6, "value": "", "fontFamily": "ZT002", "lineMode": 6, "letterSpacing": 0, "lineSpacing": 0, "fontStyle": [], "textAlignHorizonral": 0, "zoomable": "w, e", "textAlignVertical": 1, "isTitle": false, "rotatePoint": 2, "fontCode": "ZT002", "useTitle": false, "height": 3.3022861981371716, "width": 37.99999999999999, "rotate": 90, "itemType": "widget", "printMultiple": 11.81, "aspectRatio": 0, "fontColor": [0, 0, 0], "isLock": 0, "wordSpacing": 0, "centralPoint": 1}, {"uid": "w42e492f5ec9f7f7ad8cab2dff01fd76d", "id": "w42e492f5ec9f7f7ad8cab2dff01fd76d", "x": -14, "y": 21, "locked": false, "zIndex": 1, "type": "text", "name": "text", "fontSize": 2.6, "value": "", "fontFamily": "ZT002", "lineMode": 6, "letterSpacing": 0, "lineSpacing": 0, "fontStyle": [], "textAlignHorizonral": 0, "zoomable": "w, e", "textAlignVertical": 1, "isTitle": false, "rotatePoint": 2, "fontCode": "ZT002", "useTitle": false, "height": 3.3022861981371716, "width": 38, "rotate": 90, "itemType": "widget", "printMultiple": 11.81, "aspectRatio": 0, "fontColor": [0, 0, 0], "isLock": 0, "wordSpacing": 0, "centralPoint": 1}, {"uid": "w4323a05e683c6cb73b65f65a9747e120", "id": "w4323a05e683c6cb73b65f65a9747e120", "x": -10, "y": 21, "locked": false, "zIndex": 1, "type": "text", "name": "text", "fontSize": 2.6, "value": "", "fontFamily": "ZT002", "lineMode": 6, "letterSpacing": 0, "lineSpacing": 0, "fontStyle": [], "textAlignHorizonral": 0, "zoomable": "w, e", "textAlignVertical": 1, "isTitle": false, "rotatePoint": 2, "fontCode": "ZT002", "useTitle": false, "height": 3.3022861981371716, "width": 38, "rotate": 90, "itemType": "widget", "printMultiple": 11.81, "aspectRatio": 0, "fontColor": [0, 0, 0], "isLock": 0, "wordSpacing": 0, "centralPoint": 1}, {"uid": "w0d56a06a0746220fae25a4bbe334c274", "id": "w0d56a06a0746220fae25a4bbe334c274", "x": 1.4411531685773462, "y": 45.792547137125574, "locked": false, "zIndex": 1, "type": "qrcode", "name": "qrcode", "codeType": 31, "correctLevel": 0, "value": "", "zoomable": "nw, ne, se, sw", "aspectRatio": 1, "rotatePoint": 5, "useTitle": false, "height": 12.023708721422523, "width": 12, "rotate": 90, "itemType": "widget", "fontStyle": [], "printMultiple": 11.81, "isLock": 0}], "name": "新建标签", "rotate": 0, "id": "1597309348000", "consumableType": 1, "height": 60, "width": 20, "paperType": 1, "usedFonts": {"Arail": "Arail.ttf", "ZT002": "ZT002.ttf", "fontDefault": "ZT002.ttf"}, "unit": "mm"}