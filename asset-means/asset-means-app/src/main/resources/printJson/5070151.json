{"totalPage": 1, "isCable": 0, "margin": [0, 0, 0, 2], "cableDirection": -1, "currentPage": 1, "cableLength": 0, "elements": [{"id": "7fbb01ea177d9afc785c5910b499e4e8", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 13, "textAlign": "left", "width": 64.75, "height": 0.3, "x": 2.55, "y": 2}, {"id": "17dcfe4bd0fedb0e443390cad46fc2a0", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 13, "textAlign": "left", "width": 64.75, "height": 0.3, "x": 2.55, "y": 47.25}, {"id": "d4c663d165545a71ca6c2a61e47eb9fc", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 14, "textAlign": "left", "width": 45.5, "height": 0.3, "x": -20.125, "y": 24.65}, {"id": "ed12e4cd4a7e4037d0d8d88eb8836128", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 14, "textAlign": "left", "width": 45.5, "height": 0.3, "x": 44.5, "y": 24.65}, {"id": "aa514a78ade5a6ab087726d4604c9c4e", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 13, "textAlign": "left", "width": 64.5, "height": 0.3, "x": 2.625, "y": 11.125}, {"id": "40c0e190342688728171e52d9f6eda84", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 13, "textAlign": "left", "width": 64.5, "height": 0.3, "x": 2.625, "y": 20}, {"id": "35f09abea43d8eb928b14f398df72a51", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 13, "textAlign": "left", "width": 64.5, "height": 0.3, "x": 2.625, "y": 29.125}, {"id": "c76638e80a4b616e03a7c6fcc959a2ad", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 13, "textAlign": "left", "width": 46, "height": 0.3, "x": 2.625, "y": 38.25}, {"id": "dc6727321abea5569c26285811eb0403", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 15, "textAlign": "left", "width": 18.125, "height": 0.3, "x": 39.625, "y": 38.25}, {"id": "aadeab07e103b2f2662cf8a2029b6412", "type": "qrcode", "value": "123456789", "correctLevel": 0, "codeType": 31, "width": 15, "height": 15, "rotate": 0, "zIndex": 16, "textAlign": "left", "x": 50.625, "y": 30.75}, {"id": "21760efdc2d2472f835bdde33ae16eb3", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 17, "textAlign": "left", "width": 64, "height": 6.8, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 3.75}, {"id": "75cc83ad087580a7b90ca7405275a50b", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 17, "textAlign": "left", "width": 64, "height": 6.8, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 12.75}, {"id": "3e46e8ae970df53f7474c5408fd80e5f", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 17, "textAlign": "left", "width": 64, "height": 6.8, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 21.75}, {"id": "b467bba4336d3f74f41aebd175779a54", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 17, "textAlign": "left", "width": 44, "height": 6.8, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 30.75}, {"id": "22fc847342ab0fee7ea4c02c94ea1e41", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 17, "textAlign": "left", "width": 44, "height": 6.8, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 39.75}], "name": "新建标签", "rotate": 270, "id": "1597309348000", "consumableType": 1, "height": 50, "width": 70, "paperType": 1, "usedFonts": {"Arail": "Arail.ttf", "ZT002": "ZT002.ttf", "fontDefault": "ZT002.ttf"}, "unit": "mm"}