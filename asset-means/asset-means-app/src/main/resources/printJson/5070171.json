{"totalPage": 1, "isCable": 0, "margin": [0, 0, 0, 2], "cableDirection": -1, "currentPage": 1, "cableLength": 0, "elements": [{"id": "f97754207e68fa34998756b957717e8b", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.625, "height": 0.3, "x": 2.375, "y": 29.875}, {"id": "96aed166da1bebb54b3339aa5e49f213", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.625, "height": 0.3, "x": 2.375, "y": 20.625}, {"id": "9889dbb39d7d82d71f5569e4bd19356e", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 8, "textAlign": "left", "width": 65.25, "height": 0.3, "x": -30.25, "y": 34.5}, {"id": "7e5c4825a474eea2b47943edea1b4fce", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 8, "textAlign": "left", "width": 65.25, "height": 0.3, "x": 15.25, "y": 34.5}, {"id": "9f0c67e8a6008955aae2afc0a800f747", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.625, "height": 0.3, "x": 2.25, "y": 39.375}, {"id": "6e0953e1dbedb694956d3bf9934dbc45", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.625, "height": 0.3, "x": 2.25, "y": 48.625}, {"id": "2af77d2bbc806cdfc1efb081f95b8242", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.625, "height": 0.3, "x": 2.375, "y": 67.25}, {"id": "f91d886c5bb3502f1d2df7578697c644", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 27.375, "height": 0.3, "x": 2.375, "y": 58}, {"id": "aa6ef16b047728de561b242083c78f76", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.375, "height": 0.3, "x": 2.375, "y": 11.375}, {"id": "eadf5d361882cf8ea3dc18dc2f13df10", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 8, "textAlign": "left", "width": 45.625, "height": 0.3, "x": 2.375, "y": 2}, {"id": "6c095d565a53b7b923a91b527dc8d243", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 11, "textAlign": "left", "width": 18.75, "height": 0.3, "x": 20.25, "y": 58}, {"id": "d92a9035411913ac3f796c702294771e", "type": "qrcode", "value": "123456789", "correctLevel": 0, "codeType": 31, "width": 15, "height": 15, "rotate": 0, "zIndex": 12, "textAlign": "left", "x": 31.25, "y": 50.625}, {"id": "442a2c7f168c027828b522b004ed0213", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 44, "height": 7, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.15, "y": 3.75}, {"id": "a88b7f4b4a3eacb7d8ea8dac52d402d8", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 44, "height": 7, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.15, "y": 13}, {"id": "8be25b6282fe41d3cb8a682bc16983ca", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "paraSpacing": 0, "value": "", "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 44, "height": 7, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.15, "y": 22.25}, {"id": "de1e26316e379eb594c2bec4976a90bd", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 44, "height": 7, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.15, "y": 31.5}, {"id": "21527cfe833c667692cf3bc07b3fd5e6", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 44, "height": 7, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.15, "y": 40.75}, {"id": "3248f1cb7070ffc7903b66ad5fe54200", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 25, "height": 8, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.15, "y": 50}, {"id": "4893611b9d5d801c8fbc2354ae76ea25", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 9, "textAlign": "left", "width": 25, "height": 8, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.15, "y": 59.25}], "name": "新建标签", "rotate": 0, "id": "1597309348000", "consumableType": 1, "height": 70, "width": 50, "paperType": 1, "usedFonts": {"Arail": "Arail.ttf", "ZT002": "ZT002.ttf", "fontDefault": "ZT002.ttf"}, "unit": "mm"}