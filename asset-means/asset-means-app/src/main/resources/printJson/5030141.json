{"totalPage": 1, "isCable": 0, "margin": [0, 0, 0, 2], "cableDirection": -1, "currentPage": 1, "cableLength": 0, "elements": [{"id": "8986e3554aeb616c9d63f50b4e700089", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 46, "height": 0.3, "x": 2.375, "y": 1.5}, {"id": "e56a16f2736cee6f75929b49c2a6b8fa", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 2, "textAlign": "left", "width": 26, "height": 0.3, "x": -10.5, "y": 14.625}, {"id": "381a427f35b28c2f50254b639c1d68b3", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 46, "height": 0.3, "x": 2.375, "y": 27.75}, {"id": "ecde1f53d6e3d53d3edc55ae9407edc4", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 46, "height": 0.3, "x": 2.375, "y": 14.625}, {"id": "e1f274ca82f0d229ac0d8668b400a06c", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 32.875, "height": 0.3, "x": 2.375, "y": 21.125}, {"id": "65e570fb9bd56a07b476795fb77f66cd", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 46, "height": 0.3, "x": 2.375, "y": 8}, {"id": "2600d10b429f7e191ce7ff77c70ecdbc", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 3, "textAlign": "left", "width": 13.375, "height": 0.3, "x": 28.625, "y": 21.125}, {"id": "cf819f887222d97ca1513e55b2fb6b94", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 2, "textAlign": "left", "width": 26, "height": 0.3, "x": 35.25, "y": 14.625}, {"id": "d635cc3077420c0549ff91d0d8b6e697", "type": "qrcode", "value": "123456789aaasfsdfsdfdfwe1123", "correctLevel": 0, "codeType": 31, "width": 11.125, "height": 11.125, "rotate": 0, "zIndex": 4, "textAlign": "left", "x": 36.25, "y": 15.75}, {"id": "ba5806c16df36fffda3f421c924f809f", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "lineSpacing": 0, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 45.375, "height": 5.5, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.2, "wordSpacing": 0, "x": 2.875, "y": 2.375}, {"id": "beab12ef46eca8cd37fa44cd7a97cd13", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "lineSpacing": 0, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 45.375, "height": 5.5, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.2, "wordSpacing": 0, "x": 2.875, "y": 8.75}, {"id": "7873e06d3a5efe7cacd59e13b5ad2136", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "lineSpacing": 0, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 32.125, "height": 5.5, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.2, "wordSpacing": 0, "x": 2.875, "y": 15.25}, {"id": "f06f26c7ab71138105e36c3f1f17bf71", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "lineSpacing": 0, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 32.25, "height": 5.5, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.2, "wordSpacing": 0, "x": 2.875, "y": 21.75}], "name": "新建标签", "rotate": 0, "id": "1597309348000", "consumableType": 1, "height": 30, "width": 50, "paperType": 1, "usedFonts": {"Arail": "Arail.ttf", "ZT002": "ZT002.ttf", "fontDefault": "ZT002.ttf"}, "unit": "mm"}