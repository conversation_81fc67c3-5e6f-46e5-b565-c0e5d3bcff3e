{"totalPage": 1, "isCable": 0, "margin": [0, 0, 0, 2], "cableDirection": -1, "currentPage": 1, "cableLength": 0, "elements": [{"id": "8e011f614e40daa0f5796b37706c42dc", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 18, "textAlign": "left", "width": 70.625, "height": 0.3, "x": 2.625, "y": 2.625}, {"id": "1ce071929b533d562dd301ff52329dab", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 19, "textAlign": "left", "width": 70.625, "height": 0.3, "x": 2.625, "y": 30.875}, {"id": "172debbb083349357ec88c5cfee0d548", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 20, "textAlign": "left", "width": 28.5, "height": 0.3, "x": -11.625, "y": 16.75}, {"id": "656a9a57ab9aea278bb0d71e62ce52b9", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 20, "textAlign": "left", "width": 28.5, "height": 0.3, "x": 58.875, "y": 16.75}, {"id": "6fff1dd832ca5ab4a8363befab31970e", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 18, "textAlign": "left", "width": 70.625, "height": 0.3, "x": 2.625, "y": 9.75}, {"id": "b5536dcbdd9c9f416682ae4067db374c", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 18, "textAlign": "left", "width": 49, "height": 0.3, "x": 2.625, "y": 16.75}, {"id": "67a840c0ff0ca0c333f5a6b421e55bf3", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 18, "textAlign": "left", "width": 49, "height": 0.3, "x": 2.75, "y": 23.875}, {"id": "8da232aba8f53f4c0163384668aebea1", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 21, "textAlign": "left", "width": 21, "height": 0.3, "x": 41.125, "y": 20.5}, {"id": "258174ac379ec71a90d32b0f2609a8d0", "type": "qrcode", "value": "123456789", "correctLevel": 0, "codeType": 31, "width": 17.5, "height": 17.5, "rotate": 0, "zIndex": 22, "textAlign": "left", "x": 53.625, "y": 11.75}, {"id": "c402739f60b91b8eb24729f8dbef5a04", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 23, "textAlign": "left", "width": 70, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.6, "wordSpacing": 0, "lineSpacing": 0, "x": 3.5, "y": 3.75}, {"id": "98ca035b3394888fe30f8eef82aa51a6", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 23, "textAlign": "left", "width": 47.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "lineSpacing": 0, "x": 3.5, "y": 10.75}, {"id": "ac7edb8bf741b3ec0741b1dba11920a3", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 23, "textAlign": "left", "width": 47.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "lineSpacing": 0, "x": 3.5, "y": 17.75}, {"id": "43a066536147b53430cca3aa72be370e", "type": "text", "boldType": 0, "fontFamily": "ZT002", "fontWeight": "regular", "fontSize": 3.2, "color": "rgb(16, 16, 16)", "fontStyle": [], "textDecoration": "none", "letterSpacing": 0, "lineHeight": 3.2, "value": "", "paraSpacing": 0, "lineMode": 6, "rotate": 0, "zIndex": 23, "textAlign": "left", "width": 47.75, "height": 6, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.4, "wordSpacing": 0, "lineSpacing": 0, "x": 3.5, "y": 24.75}], "name": "新建标签", "rotate": 0, "id": "1597309348000", "consumableType": 1, "height": 34, "width": 76, "paperType": 1, "usedFonts": {"Arail": "Arail.ttf", "ZT002": "ZT002.ttf", "fontDefault": "ZT002.ttf"}, "unit": "mm"}