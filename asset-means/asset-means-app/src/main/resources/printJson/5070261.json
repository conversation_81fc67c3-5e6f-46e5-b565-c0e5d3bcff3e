{"totalPage": 1, "isCable": 0, "margin": [0, 0, 0, 2], "cableDirection": -1, "currentPage": 1, "cableLength": 0, "elements": [{"id": "d8b8fc41e0e81bb43856c034716fdcb1", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 45.375, "height": 0.3, "x": 2.375, "y": 14.125}, {"id": "fab6b8247338455ac2b951ca1c27f50f", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 24.875, "height": 0.3, "x": 2.375, "y": 58.125}, {"id": "1af2bd623b48e1dd27cc5c9c160f5554", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 2, "textAlign": "left", "width": 54, "height": 0.3, "x": -24.5, "y": 41}, {"id": "98ee249c731d211d779eb30ffd6a4d48", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 90, "zIndex": 2, "textAlign": "left", "width": 54, "height": 0.3, "x": 20.625, "y": 41}, {"id": "1c04c14b8dd607be662115dcd4eb2860", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 270, "zIndex": 3, "textAlign": "left", "width": 19.125, "height": 0.3, "x": 17.75, "y": 58.5}, {"id": "4965fa7465e3a035607a4215182a8859", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 45.375, "height": 0.3, "x": 2.375, "y": 22.875}, {"id": "53a8fdcbb5604f9464a33bf51b69815e", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 45.375, "height": 0.3, "x": 2.375, "y": 31.625}, {"id": "8fd1ecee2647880d875bd91f94ba9c8f", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 45.375, "height": 0.3, "x": 2.375, "y": 40.375}, {"id": "568848efa2727ea8e4ecbbf45a66d426", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 45.375, "height": 0.3, "x": 2.375, "y": 48.875}, {"id": "a19fd5a273692fee00b34a4aabcd854d", "type": "line", "lineWidth": 0.3, "lineType": 1, "dashwidth": [0.75, 0.75], "rotate": 0, "zIndex": 1, "textAlign": "left", "width": 45.375, "height": 0.3, "x": 2.375, "y": 67.875}, {"id": "db3ce06f2ff23d5d6e449cf1c1aed559", "type": "qrcode", "value": "123456789", "codeType": 31, "correctLevel": 0, "width": 17.375, "height": 17.375, "rotate": 0, "zIndex": 4, "textAlign": "left", "x": 28.875, "y": 50}, {"id": "2bd1c6f525f44f2a691d1e31874170d1", "type": "text", "value": "", "paraSpacing": 0, "fontFamily": "ZT002", "lineHeight": 3.2, "fontSize": 3.2, "letterSpacing": 0, "textDecoration": "none", "fontWeight": "regular", "fontStyle": [], "boldType": 0, "isTitle": false, "lineSpacing": 0, "lineMode": 6, "fontCode": "ZT001", "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 43.75, "height": 6.685, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 15.5}, {"id": "f67a73f284e1abaedc334bbcf6007db6", "type": "text", "value": "", "paraSpacing": 0, "fontFamily": "ZT002", "lineHeight": 3.2, "fontSize": 3.2, "letterSpacing": 0, "textDecoration": "none", "fontWeight": "regular", "fontStyle": [], "boldType": 0, "isTitle": false, "lineSpacing": 0, "lineMode": 6, "fontCode": "ZT001", "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 43.625, "height": 6.685, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 24.25}, {"id": "0be9f1ace5c8359b189f851dc86263aa", "type": "text", "value": "", "paraSpacing": 0, "fontFamily": "ZT002", "lineHeight": 3.2, "fontSize": 3.2, "letterSpacing": 0, "textDecoration": "none", "fontWeight": "regular", "fontStyle": [], "boldType": 0, "isTitle": false, "lineSpacing": 0, "lineMode": 6, "fontCode": "ZT001", "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 43.625, "height": 6.685, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 33.25}, {"id": "7c05d54767286b2f65a44dba8d3eedcf", "type": "text", "value": "", "paraSpacing": 0, "fontFamily": "ZT002", "lineHeight": 3.2, "fontSize": 3.2, "letterSpacing": 0, "textDecoration": "none", "fontWeight": "regular", "fontStyle": [], "boldType": 0, "isTitle": false, "lineSpacing": 0, "lineMode": 6, "fontCode": "ZT001", "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 43.625, "height": 6.685, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 41.75}, {"id": "eb5ffc029b0a69a185a48372955c0fb4", "type": "text", "value": "", "paraSpacing": 0, "fontFamily": "ZT002", "lineHeight": 3.2, "fontSize": 3.2, "letterSpacing": 0, "textDecoration": "none", "fontWeight": "regular", "fontStyle": [], "boldType": 0, "isTitle": false, "lineSpacing": 0, "lineMode": 6, "fontCode": "ZT001", "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 23.5, "height": 6.685, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 50.5}, {"id": "f365d32dd7512c2265d840f0b3320bca", "type": "text", "value": "", "paraSpacing": 0, "fontFamily": "ZT002", "lineHeight": 3.2, "fontSize": 3.2, "letterSpacing": 0, "textDecoration": "none", "fontWeight": "regular", "fontStyle": [], "boldType": 0, "isTitle": false, "lineSpacing": 0, "lineMode": 6, "fontCode": "ZT001", "rotate": 0, "zIndex": 5, "textAlign": "left", "width": 23.5, "height": 6.685, "textAlignHorizonral": 0, "textAlignVertical": 1, "minFontSize": 2.8, "wordSpacing": 0, "x": 3.5, "y": 60.25}], "name": "新建标签", "rotate": 0, "id": "1597309348000", "consumableType": 1, "height": 70, "width": 50, "paperType": 1, "usedFonts": {"Arail": "Arail.ttf", "ZT002": "ZT002.ttf", "fontDefault": "ZT002.ttf"}, "unit": "mm"}