<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsProduct">
        <id column="id" property="id"/>
        <result column="standard_id" property="standardId"/>
        <result column="company_id" property="companyId"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="brand" property="brand"/>
        <result column="model" property="model"/>
        <result column="unit" property="unit"/>
        <result column="price" property="price"/>
        <result column="images" property="images"
                typeHandler="com.niimbot.asset.means.handle.StringListTypeHandler"/>
        <result column="extension" property="extension"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,standard_id,company_id,name,brand,model,unit,price,code,images,extension,is_delete,create_by,create_time,update_by,update_time
    </sql>

    <resultMap id="AsProductInfoDto" type="com.niimbot.means.AsProductInfoDto">
        <id column="id" property="id"/>
        <result column="standard_id" property="standardId"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="brand" property="brand"/>
        <result column="model" property="model"/>
        <result column="unit" property="unit"/>
        <result column="price" property="price"/>
        <result column="images" property="images"
                typeHandler="com.niimbot.asset.means.handle.StringListTypeHandler"/>
        <result column="extension" property="extension"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="customPage" resultMap="AsProductInfoDto">
        SELECT
        p.id,
        p.standard_id,
        p.NAME,
        p.CODE,
        p.brand,
        p.model,
        p.unit,
        p.price,
        p.images,
        p.extension
        FROM
        as_product p
        <if test="empId != null">
            LEFT JOIN as_product_habits ph on p.id = ph.product_id and ph.emp_id = #{empId}
        </if>
        WHERE
        p.is_delete = 0
        and p.company_id in
        <foreach collection="companyIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="ew.kw!=null and ew.kw!=''">
            and (p.name like concat('%', #{ew.kw}, '%')
            or p.code like concat('%', #{ew.kw}, '%'))
        </if>
        <if test="ew.name!=null and ew.name!=''">
            and p.name like concat('%', #{ew.name}, '%')
        </if>
        <if test="ew.kwAll!=null and ew.kwAll!=''">
            and (p.name like concat('%', #{ew.kwAll}, '%')
            or p.code like concat('%', #{ew.kwAll}, '%')
            or p.brand like concat('%', #{ew.kwAll}, '%')
            or p.model like concat('%', #{ew.kwAll}, '%'))
        </if>
        <if test="ew.brand!=null and ew.brand!=''">
            and p.brand like concat('%', #{ew.brand}, '%')
        </if>
        <if test="ew.model!=null and ew.model!=''">
            and p.model like concat('%', #{ew.model}, '%')
        </if>
        <if test="ew.prices!=null and ew.prices.size==2">
            <if test="ew.prices[0]!=null and ew.prices[0]!=''">
                and p.price &gt;= #{ew.prices[0]}
            </if>
            <if test="ew.prices[1]!=null and ew.prices[1]!=''">
                and p.price &lt;= #{ew.prices[1]}
            </if>
        </if>
        <if test="ew.excludeProductIds != null and ew.excludeProductIds.size() > 0">
            and p.id not in
            <foreach collection="ew.excludeProductIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test="standardIds != null and standardIds.size() > 0">
            and p.standard_id in
            <foreach collection="standardIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY
        p.company_id desc,
        <if test="empId != null">
            ph.create_time desc,
        </if>
        p.create_time desc
    </select>

    <select id="customList" resultMap="AsProductInfoDto">
        SELECT
        p.id,
        p.standard_id,
        p.NAME,
        p.CODE,
        p.brand,
        p.model,
        p.unit,
        p.price,
        p.images,
        p.extension
        FROM
        as_product p
        WHERE
        p.is_delete = 0
        and p.company_id in
        <foreach collection="companyIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="name!=null and name!=''">
            and p.NAME like concat('%', #{name}, '%')
        </if>
        order by p.company_id desc, p.create_time desc
    </select>

    <select id="customInfo" resultMap="AsProductInfoDto">
        SELECT
            p.id,
            p.standard_id,
            p.NAME,
            p.CODE,
            p.brand,
            p.model,
            p.unit,
            p.price,
            p.images,
            p.extension
        FROM
            as_product p
        WHERE
            p.is_delete = 0
        and p.company_id in (0, #{companyId})
        and p.id = #{id}
    </select>

    <select id="customInfos" resultMap="AsProductInfoDto">
        SELECT
            p.id,
            p.standard_id,
            p.NAME,
            p.CODE,
            p.brand,
            p.model,
            p.unit,
            p.price,
            p.images,
            p.extension
        FROM
            as_product p
        WHERE
            p.is_delete = 0
          and p.company_id in (0, #{companyId})
          and p.id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="listNoDataPerm" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from as_product ${ew.customSqlSegment}
    </select>

</mapper>
