<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsUserPrintTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsUserPrintTask">
        <id column="id" property="id"/>
        <result column="task_type" property="taskType"/>
        <result column="print_type" property="printType"/>
        <result column="print_source" property="printSource"/>
        <result column="company_id" property="companyId"/>
        <result column="user_id" property="userId"/>
        <result column="asset_num" property="assetNum"/>
        <result column="assets" property="assets"  typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="assets_snapshot" property="assetsSnapshot"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="query_data" property="queryData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="printed_asset_num" property="printedAssetNum"/>
        <result column="task_status" property="taskStatus"/>
        <result column="printer_name" property="printerName"/>
        <result column="printer_serial_no" property="printerSerialNo"/>
        <result column="tag_id" property="tagId"/>
        <result column="tag_type" property="tagType"/>
        <result column="tag_material_id" property="tagMaterialId"/>
        <result column="print_concentration" property="printConcentration"/>
        <result column="hardware_version" property="hardwareVersion"/>
        <result column="sdk_version" property="sdkVersion"/>
        <result column="firmware_version" property="firmwareVersion"/>
        <result column="terminal_brand" property="terminalBrand"/>
        <result column="terminal_type" property="terminalType"/>
        <result column="os_version" property="osVersion"/>
        <result column="app_version" property="appVersion"/>
        <result column="print_time" property="printTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_type, print_type, print_source, company_id, user_id, asset_num, assets, assets_snapshot,query_data,
        printed_asset_num,task_status, printer_name, printer_serial_no, tag_id, tag_type, tag_material_id, print_concentration,
         hardware_version, sdk_version, firmware_version, terminal_brand, terminal_type, os_version, app_version,
         print_time, create_by, create_time, is_delete
    </sql>

    <select id="getOneByMapper" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from as_user_print_task where id = #{id}
    </select>

</mapper>
