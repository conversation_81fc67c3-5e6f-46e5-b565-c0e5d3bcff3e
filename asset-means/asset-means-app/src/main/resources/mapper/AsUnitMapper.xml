<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsUnitMapper">

    <select id="search" resultType="com.niimbot.asset.means.model.AsUnit">
        SELECT
        id,
        NAME,
        alias
        FROM
        as_unit
        WHERE
        company_id in (0, #{companyId})
        and is_delete = 0
        <if test="name!=null and name!=''">
            AND name LIKE concat('%',#{name},'%')
        </if>
        order by create_time desc
    </select>

</mapper>
