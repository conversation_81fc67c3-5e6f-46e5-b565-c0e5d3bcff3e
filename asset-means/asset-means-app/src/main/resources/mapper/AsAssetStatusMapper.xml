<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAssetStatusMapper">

    <cache-ref namespace="com.niimbot.asset.means.mapper.AsAssetStatusMapper"/>

    <select id="getAssetStatusByOpt" resultType="com.niimbot.means.AssetStatusDto">
        SELECT
            aas.id,
            aas.code,
            aas.name
        FROM
            as_asset_status aas
            JOIN as_asset_status_operation aaso ON aas.id = aaso.status_id
            JOIN as_asset_operation aao ON aaso.operation_id = aao.id
        WHERE
            aao.id = #{optId}
        GROUP BY
            aas.id,
            aas.code,
            aas.name
    </select>

    <select id="allStatus" resultType="com.niimbot.means.AssetStatusDto">
        SELECT
            aas.id,
            aas.code,
            aas.name
        FROM
            as_asset_status aas
    </select>

    <select id="getAssetStatusByOrderType" resultType="java.lang.Integer">
        SELECT
            st.id
        FROM
            as_asset_status st
            JOIN as_asset_status_operation so ON st.id = so.status_id
            JOIN as_asset_operation op ON so.operation_id = op.id
        WHERE
            op.order_type = #{orderType}
    </select>

</mapper>
