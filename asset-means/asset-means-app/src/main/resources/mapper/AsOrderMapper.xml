<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsOrder">
        <id column="id" property="id"/>
        <result column="order_type" property="orderType"/>
        <result column="company_id" property="companyId"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="order_no" property="orderNo"/>
        <result column="asset_num" property="assetNum"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="page" resultMap="BaseResultMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.order_type,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.asset_num,
        a.order_data,
        a.create_by,
        a.create_time
        from as_order a
        <if test="query.kw != null and query.kw != ''">
            join as_order_detail b on a.id = b.order_id
        </if>
        <where>
            <if test="query.orderType != null">
                and a.order_type = #{query.orderType}
            </if>
            <if test="query.approveStatus != null">
                and a.approve_status = #{query.approveStatus}
            </if>
            <if test="query.kw != null and query.kw != ''">
                and (
                a.order_no like concat('%', #{query.kw}, '%')
                or a.remark like concat('%', #{query.kw}, '%')
                or b.asset_name like concat('%', #{query.kw}, '%')
                or b.asset_code like concat('%', #{query.kw}, '%')
                )
            </if>
            <!-- 勾选的单据id -->
            <if test="query.ids!=null and query.ids.size() > 0">
                and a.id in
                <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
        </where>
    </select>

    <resultMap id="OrderDtoMap" type="com.niimbot.means.AsOrderDto">
        <id column="id" property="id"/>
        <result column="order_type" property="orderType"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="approve_status_text" property="approveStatusText"/>
        <result column="order_no" property="orderNo"/>
        <result column="asset_num" property="assetNum"/>
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_text" property="createByText"/>
        <result column="create_time" property="createTime"/>
        <collection property="assets"
                    ofType="com.niimbot.means.AsOrderAssetDto"
                    column="{id=id}"
                    select="selectAssetSnapshot"
                    javaType="java.util.ArrayList">
        </collection>
    </resultMap>

    <resultMap id="AsOrderAssetDto" type="com.niimbot.means.AsOrderAssetDto">
        <id column="asset_id" property="id" />
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="selectAssetSnapshot" resultMap="AsOrderAssetDto">
        select asset_id, asset_snapshot_data from as_order_detail where order_id = #{id}
    </select>
    <select id="listForExport" resultMap="OrderDtoMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.order_type,
        a.approve_status,
        ( SELECT dd.label FROM as_dict_data dd WHERE dd.value = a.approve_status
        and dd.dict_type = 'approve_status' and dd.status = 1 ) AS approve_status_text,
        a.order_no,
        a.asset_num,
        a.order_data,
        a.create_by,
        ( SELECT CASE IFNULL(emp.emp_no, '')
        WHEN '' THEN
        emp.emp_name
        ELSE
        CONCAT(emp.emp_name,"（", emp.emp_no ,"）")
        END as create_by_text
        FROM as_cus_employee emp WHERE emp.id = a.create_by) AS create_by_text,
        a.create_time
        from as_order a
        <if test="query.kw != null and query.kw != ''">
            join as_order_detail b on a.id = b.order_id
        </if>
        <where>
            <if test="query.orderType != null">
                and a.order_type = #{query.orderType}
            </if>
            <if test="query.approveStatus != null">
                and a.approve_status = #{query.approveStatus}
            </if>
            <if test="query.kw != null and query.kw != ''">
                and (
                a.order_no like concat('%', #{query.kw}, '%')
                or a.remark like concat('%', #{query.kw}, '%')
                or b.asset_name like concat('%', #{query.kw}, '%')
                or b.asset_code like concat('%', #{query.kw}, '%')
                )
            </if>
            <!-- 动态条件 -->
            <if test="conditions != null and conditions != ''">
                ${conditions}
            </if>
            <!-- 勾选的单据id -->
            <if test="query.ids!=null and query.ids.size() > 0">
                and a.id in
                <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        <if test="orderBySql != null and orderBySql != ''">
            ${orderBySql}
        </if>
    </select>

    <select id="getOrderByAssetId" resultType="com.niimbot.means.AsOrderInfoDto">
        SELECT
            a.id,
            a.order_type,
            a.order_no
        FROM
            as_order a JOIN as_order_detail AS b ON a.id = b.order_id
        WHERE
            a.approve_status = 1
          AND b.asset_id = #{assetId}
        limit 1
    </select>

    <select id="getOrderByAssetIdNoPerm" resultType="com.niimbot.means.AsOrderInfoDto">
        SELECT
            a.id,
            a.order_type,
            a.order_no
        FROM
            as_order a JOIN as_order_detail AS b ON a.id = b.order_id
        WHERE
            a.approve_status = 1
          AND a.company_id = #{companyId}
          AND b.asset_id = #{assetId}
        limit 1
    </select>

</mapper>
