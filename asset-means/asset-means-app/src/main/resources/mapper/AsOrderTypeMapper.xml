<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsOrderTypeMapper">

    <select id="listOrderType" resultType="com.niimbot.means.OrderTypeDto">
        SELECT
            o.id,
            o.NAME,
            o.type,
            o.activiti_key,
            o.description,
            o.new_asset_status,
            o.icon,
            o.enable_config,
            o.enable_app,
            o.enable_workflow,
            o.enable_workflow_config,
            o.update_time,
            IF
            ( count( w.id ) > 0, 1, 2 ) AS workflow_status
        FROM
            as_order_type o
            LEFT JOIN act_workflow w ON o.company_id = w.company_id
            AND o.activiti_key = w.activiti_key
            AND w.is_delete = 0
            AND w.STATUS = 1
        WHERE
            o.company_id = #{companyId}
        GROUP BY
            o.id,
            o.NAME,
            o.type,
            o.activiti_key,
            o.description,
            o.new_asset_status,
            o.icon,
            o.enable_config,
            o.enable_app,
            o.enable_workflow,
            o.enable_workflow_config,
            o.update_time
        order by workflow_status asc, o.id asc
    </select>

</mapper>