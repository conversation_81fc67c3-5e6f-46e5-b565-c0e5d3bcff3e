<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsStoreOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsStoreOrder">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="approve_status" property="approveStatus" />
        <result column="summary" property="summary" />
        <result column="order_no" property="orderNo" />
        <result column="order_data" property="orderData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="total_quantity" property="totalQuantity" />
        <result column="total_money" property="totalMoney" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="page" resultMap="BaseResultMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time
        from as_store_order a
        <if test="query.kw != null and query.kw != ''">
            join as_store_order_asset_detail b on a.id = b.store_order_id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <if test="query.kw != null and query.kw != ''">
            distinct
        </if>
        a.id,
        a.company_id,
        a.approve_status,
        a.summary,
        a.order_no,
        a.order_data,
        a.total_quantity,
        a.total_money,
        a.create_by,
        a.create_time
        from as_store_order a
        <if test="query.kw != null and query.kw != ''">
            join as_store_order_asset_detail b on a.id = b.store_order_id
        </if>
        <where>
            <include refid="queryCondition"/>
        </where>
        <if test="orderBySql != null and orderBySql != ''">
            ${orderBySql}
        </if>
    </select>

    <sql id="queryCondition">
        <if test="query.approveStatus != null">
            and a.approve_status = #{query.approveStatus}
        </if>
        <if test="query.kw != null and query.kw != ''">
            and (
            a.order_no like concat('%', #{query.kw}, '%')
            or a.summary like concat('%', #{query.kw}, '%')
            or b.asset_snapshot_data ->> '$.assetCode' like concat('%', #{query.kw}, '%')
            )
        </if>
        <!-- 勾选的单据id -->
        <if test="query.ids!=null and query.ids.size() > 0">
            and a.id in
            <foreach collection="query.ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>
    </sql>

</mapper>
