<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsPrintDataSnapshotMapper">

    <resultMap id="taskDetails" type="com.niimbot.means.UserPrintTaskGroupStatusResult">
        <result property="status" column="status"/>
        <result property="data" column="data"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="isTranslation" column="is_translation"/>
        <result property="dataId" column="data_id"/>
    </resultMap>

    <select id="taskDetailsPage" resultMap="taskDetails">
        SELECT
        `snapshot` AS `data`,
        CASE
        is_print
        WHEN 1 THEN
        '已打印' ELSE '未打印'
        END AS `status`,
        is_translation,
        data_id
        FROM as_print_data_snapshot
        WHERE task_id = #{em.taskId} AND type = #{em.printType}
        <if test="em.status != null">
            <if test="em.status == 1">
                AND is_print = 0
            </if>
            <if test="em.status == 2">
                AND is_print = 1
            </if>
        </if>
        <if test="em.kw != null and em.kw != ''">
            <if test="em.printType == 1">
                AND ( snapshot ->> '$.assetCode' LIKE CONCAT('%', #{em.kw}, '%') OR snapshot ->> '$.assetName'
                LIKE CONCAT('%', #{em.kw}, '%') )
            </if>
            <if test="em.printType == 2">
                AND ( snapshot ->> '$.materialCode' LIKE CONCAT('%', #{em.kw}, '%') OR snapshot ->>
                '$.materialName' LIKE CONCAT('%', #{em.kw}, '%') )
            </if>
            <if test="em.printType == 3">
                AND ( snapshot ->> '$.areaCode' LIKE CONCAT('%', #{em.kw}, '%') OR snapshot ->> '$.areaName' LIKE
                CONCAT('%', #{em.kw}, '%') )
            </if>
        </if>
        ORDER BY sort ASC
    </select>

</mapper>
