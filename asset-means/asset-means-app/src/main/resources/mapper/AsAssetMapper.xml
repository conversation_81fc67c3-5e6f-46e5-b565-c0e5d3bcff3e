<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAssetMapper">

    <resultMap id="AssetAppPageMap" type="com.niimbot.means.AssetAppPageDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="asset_code" property="assetCode"/>
        <result column="use_person" property="usePerson"/>
        <result column="use_person_text" property="usePersonText"/>
        <result column="org_owner" property="orgOwner"/>
        <result column="org_owner_text" property="orgOwnerText"/>
        <result column="asset_category" property="assetCategory"/>
        <result column="asset_category_text" property="assetCategoryText"/>
        <result column="asset_name" property="assetName"/>
        <result column="storage_area" property="storageArea"/>
        <result column="storage_area_text" property="storageAreaText"/>
        <result column="use_org" property="useOrg"/>
        <result column="use_org_text" property="useOrgText"/>
        <result column="asset_photo" property="assetPhoto"
                typeHandler="com.niimbot.handle.ListTypeHandler"/>
        <result column="last_print_time" property="lastPrintTime"/>
        <result column="label_epcid" property="labelEpcid"/>
    </resultMap>

    <resultMap id="AssetRelationAppMap" type="com.niimbot.means.AssetRelationAppDto">
        <id column="id" property="id"/>
        <id column="relation_type" property="relationType" />
        <id column="sub_asset_count" property="subAssetCount" />
        <id column="relation_time" property="relationTime" />
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="asset_code" property="assetCode"/>
        <result column="use_person" property="usePerson"/>
        <result column="use_person_text" property="usePersonText"/>
        <result column="org_owner" property="orgOwner"/>
        <result column="org_owner_text" property="orgOwnerText"/>
        <result column="asset_category" property="assetCategory"/>
        <result column="asset_category_text" property="assetCategoryText"/>
        <result column="asset_name" property="assetName"/>
        <result column="storage_area" property="storageArea"/>
        <result column="storage_area_text" property="storageAreaText"/>
        <result column="use_org" property="useOrg"/>
        <result column="use_org_text" property="useOrgText"/>
        <result column="asset_photo" property="assetPhoto"
                typeHandler="com.niimbot.handle.ListTypeHandler"/>
        <result column="last_print_time" property="lastPrintTime"/>
    </resultMap>

    <resultMap id="AsAssetMap" type="com.niimbot.means.AssetDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="before_status" property="beforeStatus"/>
        <result column="repair_before_status" property="repairBeforeStatus"/>
        <result column="label_tid" property="labelTid"/>
        <result column="label_epcid" property="labelEpcid"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
    </resultMap>

    <resultMap id="AssetRelationMap" type="com.niimbot.means.AssetRelationDto">
        <id column="relation_asset_id" property="id"/>
        <id column="relation_type" property="relationType" />
        <id column="main_asset_id" property="mainAssetId" />
        <id column="sub_asset_count" property="subAssetCount" />
        <id column="relation_time" property="relationTime" />
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="before_status" property="beforeStatus"/>
        <result column="repair_before_status" property="repairBeforeStatus"/>
        <result column="label_tid" property="labelTid"/>
        <result column="label_epcid" property="labelEpcid"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_print_time" property="lastPrintTime"/>
    </resultMap>

    <resultMap id="AsAsset" type="com.niimbot.asset.means.model.AsAsset">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="standard_id" property="standardId"/>
        <result column="asset_data" property="assetData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="before_status" property="beforeStatus"/>
        <result column="repair_before_status" property="repairBeforeStatus"/>
        <result column="label_tid" property="labelTid"/>
        <result column="label_epcid" property="labelEpcid"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="asset_code" property="assetCode"/>
        <result column="last_print_time" property="lastPrintTime"/>
    </resultMap>

    <select id="assetStatusReport" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        ifnull( SUM( CASE WHEN STATUS = 1 THEN 1 ELSE 0 END ), 0 ) AS idle_num,
        ifnull( SUM( CASE WHEN STATUS = 2 THEN 1 ELSE 0 END ), 0 ) AS using_num,
        ifnull( SUM( CASE WHEN STATUS = 3 THEN 1 ELSE 0 END ), 0 ) AS borrow_num,
        ifnull( SUM( CASE WHEN STATUS = 4 THEN 1 ELSE 0 END ), 0 ) AS handle_num,
        ifnull( SUM( CASE WHEN STATUS = 5 THEN 1 ELSE 0 END ), 0 ) AS service_num,
        ifnull( SUM( CASE WHEN STATUS = 6 THEN 1 ELSE 0 END ), 0 ) AS check_num,
        ifnull( SUM( CASE WHEN STATUS = 7 THEN 1 ELSE 0 END ), 0 ) AS waitService_num,
        ifnull( SUM( CASE WHEN STATUS = 1 THEN asset_data ->> '$.price' ELSE 0 END ), 0 ) AS
        idle_price,
        ifnull( SUM( CASE WHEN STATUS = 2 THEN asset_data ->> '$.price' ELSE 0 END ), 0 ) AS
        using_price,
        ifnull( SUM( CASE WHEN STATUS = 3 THEN asset_data ->> '$.price' ELSE 0 END ), 0 ) AS
        borrow_price,
        ifnull( SUM( CASE WHEN STATUS = 4 THEN asset_data ->> '$.price' ELSE 0 END ), 0 ) AS
        handle_price,
        ifnull( SUM( CASE WHEN STATUS = 5 THEN asset_data ->> '$.price' ELSE 0 END ), 0 ) AS
        service_price,
        ifnull( SUM( CASE WHEN STATUS = 6 THEN asset_data ->> '$.price' ELSE 0 END ), 0 ) AS
        check_price,
        ifnull( SUM( CASE WHEN STATUS = 7 THEN asset_data ->> '$.price' ELSE 0 END ), 0 ) AS
        waitService_price
        FROM
        as_asset a
        WHERE
        a.is_delete = 0
        LIMIT 1
    </select>

    <select id="monthsReport" resultType="java.util.HashMap">
        SELECT
        DATE_FORMAT(a.create_time, '%Y%m') AS months,
        count( a.id ) AS asset_num
        FROM
        as_asset a
        WHERE
        a.is_delete = 0
        AND a.create_time &gt;= #{beginTime}
        AND a.create_time &lt;= #{endTime}
        GROUP BY
        months
    </select>

    <select id="monthsWorthReport" resultType="java.util.HashMap">
        SELECT
        DATE_FORMAT(a.create_time, '%Y%m') AS months,
        sum( cast(a.asset_data ->> '$.price' AS DECIMAL (20,4)) ) AS price_sum
        FROM
        as_asset a
        WHERE
        a.is_delete = 0
        AND a.create_time &gt;= #{beginTime}
        AND a.create_time &lt;= #{endTime}
        GROUP BY
        months
    </select>

    <select id="getCategoryAssetReport" resultType="java.util.HashMap">
        SELECT
        c.id,
        c.pid,
        c.category_name AS name,
        c.level,
        c.paths,
        count( a.id ) AS asset_num,
        ifnull( sum( cast(a.asset_data ->> '$.price' AS DECIMAL ( 20, 4 ) ) ), 0 ) AS worth_sum,
        a.asset_category
        FROM
        as_asset a RIGHT JOIN as_category c ON CAST(a.asset_category as DECIMAL (19)) = c.id
        AND a.company_id = c.company_id AND a.is_delete = 0
        WHERE
        c.is_delete = 0
        GROUP BY
        c.id
        ORDER BY
        c.sort_num ASC,
        c.id ASC
    </select>

    <select id="getUseOrgAssetGroupReport" resultType="java.util.HashMap">
        SELECT
        c.id,
        c.pid,
        c.org_name AS name,
        c.level,
        c.paths,
        count( a.id ) AS asset_num,
        ifnull( sum( cast(a.asset_data ->> '$.price' AS DECIMAL ( 20, 4 ) ) ), 0 ) AS worth_sum,
        a.use_org
        FROM
        as_asset a RIGHT JOIN as_org c ON CAST(a.use_org as DECIMAL(19)) = c.id AND a.company_id = c.company_id AND
        a.is_delete = 0
        WHERE
        c.is_delete = 0
        GROUP BY
        c.id
        ORDER BY
        c.level asc
    </select>

    <select id="selectAssetNumGroup" resultType="com.niimbot.inventory.InventoryDispatchGroupAssetDto">
        SELECT
        *
        FROM
        (
        SELECT
        IFNULL(${groupByStr}, "") as ${groupByStr},
        count( id ) AS assetNum
        FROM
        as_asset
        WHERE
        is_delete = 0 and company_id = {companyId}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        GROUP BY
        IFNULL(${groupByStr}, "")
        ) AS as_asset
        ORDER BY
        assetNum DESC
    </select>

    <select id="listByIdsNoPerm" resultMap="AsAsset">
        SELECT
        id, asset_data, status, before_status, repair_before_status, label_tid,
        label_epcid, is_delete, create_by, create_time, update_by, update_time
        FROM
        as_asset
        where
        is_delete = 0 and company_id = #{companyId}
        <if test="assetIdList != null and assetIdList.size() > 0">
            and id in
            <foreach collection="assetIdList" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getByCodeNoPerm" resultMap="AsAsset">
        SELECT
            id, asset_data, status, before_status, repair_before_status, label_tid,
            label_epcid, is_delete, create_by, create_time, update_by, update_time
        FROM
            as_asset
        where
            is_delete = 0 and company_id = #{companyId} and asset_code = #{code}
        limit 1
    </select>

    <select id="selectUseAsset" resultMap="AsAssetMap">
        SELECT t.id,t.status, t.asset_data, t.standard_id, t.create_time
        FROM as_asset t
        WHERE t.is_delete = 0 and
        t.use_person = CONCAT(#{userId}
        , '')
        <if test="companyId != null">
            and t.company_id = #{companyId}
        </if>
    </select>

    <select id="selectOwnerAsset" resultMap="AsAssetMap">
        SELECT t.id, t.status, t.asset_data, t.standard_id, t.create_time
        FROM as_asset t
        WHERE t.is_delete = 0 and
        t.manager_owner = CONCAT(#{userId}
        , '')
        <if test="companyId != null">
            and t.company_id = #{companyId}
        </if>
    </select>

    <select id="selectUseOrg" resultMap="AsAssetMap">
        select id, asset_data, standard_id
        from as_asset
        where is_delete = 0
        and use_org = CONCAT(#{from}, '')
        and use_person = CONCAT(#{userId}, '')
        <if test="companyId != null">
            and company_id = #{companyId}
        </if>
    </select>

    <select id="getInfoNoPermByCodePhp" resultMap="AsAsset">
        SELECT
        a.id, a.asset_data, a.status, a.before_status, a.repair_before_status,
        a.label_tid,
        a.label_epcid, a.is_delete, a.create_by, a.create_time, a.update_by, a.update_time
        FROM
        as_asset as a left join as_asset_php_ref as r on a.id = r.java_id and r.type = 1
        where a.is_delete = 0 and a.company_id = #{companyId}
        <!-- 关键字（编码/名称）-->
        <if test="assetCode!=null and assetCode!=''">
            and (a.label_tid = #{assetCode}
            or a.id = #{assetCode}
            or r.php_id = #{assetCode}
            <foreach collection="uniqueCodes" item="code" index="index" open="or"
                     separator="or">
                a.asset_data ->> '$.${code}' = #{assetCode}
            </foreach>
            )
        </if>
        limit 1
    </select>

    <update id="updateUseOrg">
        update as_asset
        set asset_data = JSON_SET(asset_data, '$.useOrg', CONCAT(#{to}, ''))
        where is_delete = 0
        <!-- 企业id-->
        <if test="companyId != null">
            and company_id = #{companyId}
        </if>
        and use_org = CONCAT(#{from}, '')
        and use_person = CONCAT(#{userId}, '')
    </update>

    <sql id="customCondition">
        <if test="ew.kw!=null and ew.kw!=''">
            AND
            (
            <choose>
                <when test="ew.kwFiled==null or ew.kwFiled.size()==0">
                        (a.asset_code like concat('%',#{ew.kw},'%'))
                        or
                        (a.asset_name like concat('%',#{ew.kw},'%'))
                </when>
                <otherwise>
                    <foreach collection="ew.kwFiled" item="filed" index="index" open="(" close=")"
                             separator="or">
                        <choose>
                            <when test="filed.type.code==@<EMAIL>">
                                a.${filed.code} like concat('%',#{ew.kw},'%')
                            </when>
                            <when test="filed.type.code==@<EMAIL> or filed.type.code==@<EMAIL> or filed.type.code==@<EMAIL> or filed.type.code==@<EMAIL>">
                                    <choose>
                                        <when test="filed.code=='create_by'">
                                            a.create_by
                                        </when>
                                        <otherwise>
                                            a.asset_data ->> '$.${filed.code}'
                                        </otherwise>
                                    </choose>
                                    in
                                    <foreach collection="filed.ids" item="id" index="index" open="(" close=")"
                                             separator=",">
                                        #{id}
                                    </foreach>
                            </when>
                            <otherwise>
                                a.asset_data ->> '$.${filed.code}' like concat('%',#{ew.kw},'%')
                            </otherwise>
                        </choose>
                    </foreach>
                </otherwise>
            </choose>

            )
        </if>

        <!-- 动态条件 -->
        <if test="conditions != null and conditions != ''">
            ${conditions}
        </if>

        <!-- 排除资产 -->
        <if test="ew.excludeAssetIds != null and ew.excludeAssetIds.size() > 0">
            and a.id not in
            <foreach collection="ew.excludeAssetIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 排除资产状态 -->
        <if test="ew.excludeAssetStatus != null and ew.excludeAssetStatus.size() > 0">
            and a.STATUS not in
            <foreach collection="ew.excludeAssetStatus" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 【打印】包含资产 -->
        <if test="ew.includeAssetIds != null and ew.includeAssetIds.size() > 0">
            and a.id in
            <foreach collection="ew.includeAssetIds" item="id" index="index" open="(" close=")"
                     separator=",">
                #{id}
            </foreach>
        </if>

        <!-- 保养计划 -->
        <if test="ew.orderType!=null and ew.orderType =='97'.toString()">
            and a.id not in ( select asset_id from as_maintain_plan where is_delete = 0)
        </if>

        <!-- 保养单 -->
        <if test="ew.orderType!=null and ew.orderType =='11'.toString()">
            and a.id in ( select asset_id from as_maintain_plan where status = 1 and is_delete =
            0)
        </if>
        <!-- 是否已打印 -->
        <if test="ew.isPrint != null">
            <if test="ew.isPrint == true">
                and a.last_print_time IS NOT NULL
            </if>
            <if test="ew.isPrint == false">
                and a.last_print_time IS NULL
            </if>
        </if>
        <if test="ew.assetIds != null and ew.assetIds.size() > 0">
            and a.id in
            <foreach collection="ew.assetIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="relationPage" resultMap="AssetRelationMap" fetchSize="1000">
        select d.relation_asset_id, d.sub_asset_count, d.relation_time, a.standard_id, a.asset_data, a.status,
        a.before_status, a.repair_before_status, a.label_tid, a.label_epcid, a.is_delete, a.create_by, a.create_time, a.update_by,
        a.update_time,a.last_print_time
        from  as_asset as a join (
        select c.asset_id as relation_asset_id, count(c.sub_asset_id) as sub_asset_count, min(c.relation_time) as
        relation_time
        from (
        select b.asset_id, b.sub_asset_id, b.create_time as relation_time
        from (select asset_id, sub_asset_id, create_time from as_asset_relation where company_id = #{ew.companyId}) as b
        inner join as_asset as a on b.sub_asset_id = a.id
        where a.is_delete = #{ew.formRecycle}
        <if test="statusCondition != null and statusCondition != ''">
            ${statusCondition}
        </if>
        ) as c
        group by c.asset_id
        ) as d on d.relation_asset_id = a.id
        <include refid="customCondition"/>
    </select>

    <select id="subAssetPage" resultMap="AssetRelationMap" fetchSize="200">
        select a.id as relation_asset_id, b.main_asset_id, b.relation_type, b.relation_time as relation_time, a.standard_id, a.asset_data, a.status, a.before_status, a.repair_before_status,
        a.label_tid, a.label_epcid, a.is_delete, a.create_by, a.create_time, a.update_by, a.update_time, a.last_print_time
        from as_asset as a  join (select asset_id as main_asset_id, sub_asset_id as asset_id, relation_type, create_time as relation_time
        from as_asset_relation
        where company_id = #{ew.companyId} and asset_id = #{ew.mainAssetId}) as b on b.asset_id = a.id
        WHERE
        a.is_delete = #{ew.formRecycle}
        <include refid="customCondition"/>
    </select>

    <select id="selectAllByUsePersonOrManagerOwner" resultMap="AsAssetMap">
        SELECT
            id, standard_id, asset_data, status, before_status, repair_before_status, label_tid,
            label_epcid, is_delete, create_by, create_time, update_by, update_time,last_print_time
        FROM
            as_asset a
        WHERE
            a.is_delete = 0 AND (use_person = CONVERT(#{empId},CHAR) OR manager_owner = CONVERT(#{empId},CHAR))
    </select>

    <select id="customPage" resultMap="AsAssetMap" fetchSize="10000">
        SELECT
        a.id, a.standard_id, a.asset_data,
        status,
        <if test="ew.showRepaired != null and ew.showRepaired == true">
            IF(a.`status` = 7, 1, 0) as status_sort,
        </if>
        a.before_status, a.repair_before_status, a.asset_name, a.label_tid,
        a.label_epcid, a.is_delete, a.create_by, a.create_time, a.update_by, a.update_time,
        a.company_id, a.last_print_time
        FROM
        as_asset a
        <if test="ew.storeRecordId != null">
            JOIN as_data_snapshot s ON a.company_id = s.company_id AND a.id = s.data_id and s.source_id =
            #{ew.storeRecordId}
        </if>
        WHERE
        a.is_delete = #{ew.formRecycle}
        <include refid="customCondition"/>
        <!-- 是否优先展示待维修资产 -->
        <if test="ew.showRepaired != null and ew.showRepaired == true">
            order by status_sort desc
        </if>
    </select>

    <select id="customPageCount" resultType="java.lang.Long">
        select count(a.id) from as_asset a
        WHERE
        a.is_delete = 0
        <include refid="customCondition"/>
    </select>

    <select id="selectPrintAssetIds" resultType="java.lang.Long">
        select a.id
        FROM as_asset a
        WHERE a.is_delete = 0 and a.company_id = #{companyId}
        <include refid="customCondition"/>
    </select>

    <select id="pageApp" resultMap="AssetAppPageMap" fetchSize="1000">
        SELECT
        a.id,
        a.asset_code,
        a.company_id,
        a.asset_delimiter,
        a.asset_name,
        a.is_delete,
        a.STATUS,
        st.NAME AS status_text,
        a.asset_category,
        ct.category_name AS asset_category_text,
        a.use_person,
        emp.emp_name AS use_person_text,
        a.use_org,
        (select org.org_name from as_org org where org.id = CONVERT(a.use_org, DECIMAL(19))) AS use_org_text,
        a.org_owner,
        (select org.org_name from as_org org where org.id = CONVERT(a.org_owner, DECIMAL(19))) AS org_owner_text,
        a.storage_area,
        area.area_name AS storage_area_text,
        asset_data ->> '$.assetPhoto' as asset_photo,
        a.create_time,
        a.update_time,
        a.manager_owner,
        a.asset_data,
        a.create_by,
        a.last_print_time,
        a.label_epcid
        FROM
        as_asset a
        LEFT JOIN as_category ct ON CONVERT(a.asset_category,DECIMAL(19)) = ct.id
        LEFT JOIN as_asset_status st ON a.STATUS = st.id
        LEFT JOIN as_cus_employee emp ON CONVERT(a.use_person,DECIMAL(19)) = emp.id
        LEFT JOIN as_area area ON CONVERT(a.storage_area,DECIMAL(19)) = area.id
        WHERE
        a.is_delete = #{ew.formRecycle}
        <include refid="customCondition"/>
    </select>

    <select id="mainAssetRelationApp" resultMap="AssetRelationAppMap" fetchSize="1000">
        select d.id, d.sub_asset_count, d.relation_time
        from as_asset as a join (
            select c.asset_id as id, count(c.sub_asset_id) as sub_asset_count, min(c.relation_time) as relation_time
            from (
            select b.asset_id, b.sub_asset_id, b.create_time as relation_time
            from (select asset_id, sub_asset_id, create_time from as_asset_relation where company_id = #{ew.companyId}) as b
            inner join as_asset as a on b.sub_asset_id = a.id
            where a.is_delete = #{ew.formRecycle}
                <if test="statusCondition != null and statusCondition != ''">
                    ${statusCondition}
                </if>
            ) as c
            group by c.asset_id
        ) as d on d.id = a.id
        <include refid="customCondition"/>
    </select>

    <select id="assetRelationApp" resultMap="AssetRelationAppMap">
        SELECT
        a.id,
        a.asset_code,
        a.company_id,
        a.asset_delimiter,
        a.asset_name,
        a.is_delete,
        a.STATUS,
        st.NAME AS status_text,
        a.asset_category,
        ct.category_name AS asset_category_text,
        a.use_person,
        emp.emp_name AS use_person_text,
        a.use_org,
        (select org.org_name from as_org org where org.id = a.use_org) AS use_org_text,
        a.org_owner,
        (select org.org_name from as_org org where org.id = a.org_owner) AS org_owner_text,
        a.storage_area,
        area.area_name AS storage_area_text,
        asset_data ->> '$.assetPhoto' as asset_photo,
        a.create_time,
        a.update_time,
        a.manager_owner,
        a.asset_data,
        a.create_by,
        a.last_print_time
        FROM (
            select id, asset_code, company_id, asset_delimiter, asset_name, is_delete, STATUS, asset_category,
                use_person, use_org, org_owner, storage_area, asset_data, create_time, update_time, manager_owner,
                create_by, last_print_time
            from as_asset a
            <where>
                <if test="ew.assetIds != null and ew.assetIds.size() > 0">
                    and id in
                    <foreach collection="ew.assetIds" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            </where>
        ) as a
        LEFT JOIN as_category ct ON a.asset_category = ct.id
        LEFT JOIN as_asset_status st ON a.STATUS = st.id
        LEFT JOIN as_cus_employee emp ON CONVERT(a.use_person,DECIMAL(19)) = emp.id
        LEFT JOIN as_area area ON CONVERT(a.storage_area,DECIMAL(19)) = area.id
    </select>

    <select id="subAssetApp" resultMap="AssetRelationAppMap" fetchSize="1000">
        SELECT
        a.id,
        relation.relation_type,
        relation.relation_time,
        a.asset_code,
        a.company_id,
        a.asset_delimiter,
        a.asset_name,
        a.is_delete,
        a.STATUS,
        st.NAME AS status_text,
        a.asset_category,
        ct.category_name AS asset_category_text,
        a.use_person,
        emp.emp_name AS use_person_text,
        a.use_org,
        (select org.org_name from as_org org where org.id = a.use_org) AS use_org_text,
        a.org_owner,
        (select org.org_name from as_org org where org.id = a.org_owner) AS org_owner_text,
        a.storage_area,
        area.area_name AS storage_area_text,
        asset_data ->> '$.assetPhoto' as asset_photo,
        a.create_time,
        a.update_time,
        a.manager_owner,
        a.asset_data,
        a.create_by,
        a.last_print_time
        FROM
        as_asset a inner join
        (select sub_asset_id as asset_id, relation_type, create_time as relation_time
        from as_asset_relation
        where company_id = #{ew.companyId} and asset_id = #{ew.mainAssetId}) as relation on relation.asset_id = a.id
        LEFT JOIN as_category ct ON a.asset_category = ct.id
        LEFT JOIN as_asset_status st ON a.STATUS = st.id
        LEFT JOIN as_cus_employee emp ON CONVERT(a.use_person,DECIMAL(19)) = emp.id
        LEFT JOIN as_area area ON CONVERT(a.storage_area,DECIMAL(19)) = area.id
        WHERE
        a.is_delete = #{ew.formRecycle}
        <include refid="customCondition"/>
    </select>

    <select id="selectBySubAssetIdApp" resultMap="AssetRelationAppMap">
        SELECT
        a.id,
        relation.relation_type,
        relation.relation_time,
        a.asset_code,
        a.company_id,
        a.asset_delimiter,
        a.asset_name,
        a.is_delete,
        a.STATUS,
        st.NAME AS status_text,
        a.asset_category,
        ct.category_name AS asset_category_text,
        a.use_person,
        emp.emp_name AS use_person_text,
        a.use_org,
        (select org.org_name from as_org org where org.id = a.use_org) AS use_org_text,
        a.org_owner,
        (select org.org_name from as_org org where org.id = a.org_owner) AS org_owner_text,
        a.storage_area,
        area.area_name AS storage_area_text,
        asset_data ->> '$.assetPhoto' as asset_photo,
        a.create_time,
        a.update_time,
        a.manager_owner,
        a.asset_data,
        a.create_by,
        a.last_print_time
        FROM
            (select asset_id, relation_type, create_time as relation_time
            from as_asset_relation
            where company_id = #{companyId} and asset_id = #{assetId} and sub_asset_id = #{subAssetId}) as relation inner join as_asset a on relation.asset_id = a.id
            LEFT JOIN as_category ct ON a.asset_category = ct.id
            LEFT JOIN as_asset_status st ON a.STATUS = st.id
            LEFT JOIN as_cus_employee emp ON CONVERT(a.use_person,DECIMAL(19)) = emp.id
            LEFT JOIN as_area area ON CONVERT(a.storage_area,DECIMAL(19)) = area.id
    </select>

    <select id="amountMoney" resultType="java.lang.String">
        SELECT
        ifnull(sum(a.asset_data ->> '$.price'), 0)
        FROM
        as_asset a
        WHERE
        a.is_delete = 0
        <include refid="customCondition"/>
    </select>

    <select id="selectCountByAreaId" resultType="com.niimbot.means.AssetValue">
        SELECT
        a.id,
        a.`status`,
        a.asset_name AS `name`,
        a.asset_code AS `code`,
        a.use_person,
        a.asset_data ->> '$.assetPhoto' AS assetPhoto
        FROM
        as_asset AS a
        WHERE
        a.is_delete = 0
        <if test="areaIds != null and areaIds.size() > 0">
            AND a.storage_area IN
            <foreach collection="areaIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY a.id DESC
    </select>

    <select id="selectAllForOrderMessage" resultType="com.niimbot.means.AssetForMessage">
        SELECT
        t3.id AS asset_id,
        IF(t3.asset_data ->> '$.usePerson' = 'null', null, t3.asset_data ->> '$.usePerson') AS use_person,
        IF(t3.asset_data ->> '$.managerOwner' = 'null', null, t3.asset_data ->> '$.managerOwner') AS manager_owner,
        <if test="type == 'ZCLYDQTX'">
            DATEDIFF(FROM_UNIXTIME(t2.order_data ->> '$.estimateReturnDate' / 1000 , '%Y-%m-%d'), CURDATE()) AS `day`
        </if>
        <if test="type == 'ZCJYDQTX'">
            DATEDIFF(FROM_UNIXTIME(t2.order_data ->> '$.estimateBackDate' / 1000 , '%Y-%m-%d'), CURDATE()) AS `day`
        </if>
        FROM
        as_order_detail t1
        JOIN as_order t2 ON t1.order_id = t2.id
        JOIN as_asset t3 ON t1.asset_id = t3.id
        WHERE
        t2.company_id = #{companyId}
        <!-- 领用 -->
        <if test="type == 'ZCLYDQTX'">
            AND t2.order_type = 1 AND
            <!-- 预计退还日期 -->
            <foreach collection="days" item="i" open="( " close=" )" separator="OR">
                DATEDIFF(FROM_UNIXTIME(t2.order_data ->> '$.estimateReturnDate' / 1000 , '%Y-%m-%d'), CURDATE()) = #{i}
            </foreach>
        </if>
        <!-- 借用 -->
        <if test="type == 'ZCJYDQTX'">
            AND t2.order_type = 3 AND
            <!-- 预计归还日期 -->
            <foreach collection="days" item="i" open="( " close=" )" separator="OR">
                DATEDIFF(FROM_UNIXTIME(t2.order_data ->> '$.estimateBackDate' / 1000 , '%Y-%m-%d'), CURDATE()) = #{i}
            </foreach>
        </if>
        AND t3.is_delete = 0
        AND t2.approve_status IN (0, 3)
    </select>

    <select id="selectAllForAssetUseTimeLimitMessage" resultType="com.niimbot.means.AssetForMessage">
        SELECT * FROM
        (
        SELECT
        id AS asset_id,
        asset_data ->> '$.usePerson' AS use_person,
        asset_data ->> '$.managerOwner' AS manager_owner,
        CASE
        WHEN ( ISNULL( asset_data ->> '$.buyTime' ) = 1 OR LENGTH( TRIM( asset_data ->> '$.buyTime' ) ) = 0 ) THEN
        DATEDIFF(DATE_ADD( create_time, INTERVAL asset_data ->> '$.useTimeLimit' MONTH ),CURDATE())
        ELSE DATEDIFF(DATE_ADD( FROM_UNIXTIME( ( asset_data ->> '$.buyTime' ) / 1000, '%Y-%m-%d'), INTERVAL asset_data
        ->> '$.useTimeLimit' MONTH ),CURDATE())
        END AS `day`
        FROM
        as_asset
        WHERE
        company_id = #{companyId}
        AND is_delete = 0
        AND `status` != 4
        AND ISNULL( asset_data ->> '$.useTimeLimit' ) = 0
        AND LENGTH(TRIM( asset_data ->> '$.useTimeLimit' )) > 0
        ) AS t2 WHERE t2.`day` IN
        <foreach collection="days" item="day" open="(" close=")" separator=",">
            #{day}
        </foreach>
    </select>

    <select id="getInRecycleBin" resultMap="AsAsset">
        SELECT * FROM as_asset WHERE id = #{id} AND is_delete = 1
    </select>

    <resultMap id="EntMatPlanEntData" type="com.niimbot.equipment.EntMatPlanEntData" extends="AsAssetMap">
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <select id="pageEntMatPlanDataRangeTypeIsMas" resultMap="EntMatPlanEntData">
        SELECT
        a.id, a.standard_id, a.asset_data, a.status, a.before_status, a.repair_before_status, a.label_tid,
        a.label_epcid, a.is_delete, a.create_by, a.create_time, a.update_by, a.update_time,
        a.company_id, a.last_print_time
        FROM
        as_asset a join as_equipment_maintain_plan_data b on b.data_id = a.id
        where b.plan_id = #{ew.planId} and b.data_type = 1
        <include refid="customCondition"/>
        order by b.create_time desc
    </select>

    <select id="pageEntMatPlanDataRangeTypeIsCate" resultMap="EntMatPlanEntData">
        SELECT
        a.id, a.standard_id, a.asset_data, a.status, a.before_status, a.repair_before_status, a.label_tid,
        a.label_epcid, a.is_delete, a.create_by, a.create_time, a.update_by, a.update_time,
        a.company_id, a.last_print_time
        FROM
        as_asset a
        left join as_category b on CONVERT(a.asset_category, DECIMAL(19)) = b.id
        left join as_equipment_maintain_plan_data c on b.id = c.data_id
        where c.plan_id = #{ew.planId} and c.data_type = 3 and a.company_id = #{ew.companyId} and b.company_id = #{ew.companyId}
        <include refid="customCondition"/>
        order by c.create_time desc
    </select>

    <select id="listInventoryDispatchAssets" resultMap="AsAssetMap">
        SELECT a.id, a.status,
        JSON_OBJECT(
        'assetCategory', a.asset_data ->> '$.assetCategory',
        'storageArea', a.asset_data ->> '$.storageArea',
        'orgOwner', a.asset_data ->> '$.orgOwner',
        'useOrg', a.asset_data ->> '$.useOrg',
        'managerOwner', a.asset_data ->> '$.managerOwner',
        'usePerson', a.asset_data ->> '$.usePerson') as asset_data,
        a.create_by
        FROM as_asset a
        WHERE a.is_delete = 0
        <include refid="customCondition"/>
    </select>

    <select id="selectCountByAreaGroupByStatus" resultType="com.niimbot.means.AreaMeansStatusGroup">
        select
        `storage_area` AS area_id,
        `status` AS means_status,
        count( id ) AS `means_count`
        from as_asset
        where company_id = #{companyId} and is_delete = 0
        <if test="areaIds != null and areaIds.size() > 0">
            and storage_area in
            <foreach collection="areaIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY status, storage_area
    </select>

    <select id="quickSearch" resultMap="AsAsset">
        SELECT
        id, standard_id, asset_data, company_id, create_by, create_time, update_by, update_time, status, label_epcid
        FROM
        as_asset
        WHERE
        company_id = #{companyId}
        AND is_delete = 0
        <if test="query.epcIds != null and query.epcIds.size() > 0">
            AND label_epcid in
            <foreach collection="query.epcIds" item="epcId" open="(" separator="," close=")">
                #{epcId}
            </foreach>
        </if>
        <if test="query.uniqueIds != null and query.uniqueIds.size() > 0">
            and (
            asset_code in
            <foreach collection="query.uniqueIds" item="uniqueId" open="(" separator="," close=")">
                #{uniqueId}
            </foreach>
            or
            id in
            <foreach collection="query.uniqueIds" item="uniqueId" open="(" separator="," close=")">
                #{uniqueId}
            </foreach>
            )
        </if>
    </select>

</mapper>
