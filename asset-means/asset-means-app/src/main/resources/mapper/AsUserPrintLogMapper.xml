<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsUserPrintLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsUserPrintLog">
        <id column="id" property="id"/>
        <result column="print_task_id" property="printTaskId"/>
        <result column="printer_name" property="printerName"/>
        <result column="printer_serial_no" property="printerSerialNo"/>
        <result column="tag_id" property="tagId"/>
        <result column="tag_type" property="tagType"/>
        <result column="tag_material_id" property="tagMaterialId"/>
        <result column="print_concentration" property="printConcentration"/>
        <result column="consumer_code" property="consumerCode"/>
        <result column="user_id" property="userId"/>
        <result column="company_id" property="companyId"/>
        <result column="asset_id" property="assetId"/>
        <result column="asset_data" property="assetData" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="label_tid" property="labelTid"/>
        <result column="print_type" property="printType"/>
        <result column="print_source" property="printSource"/>
        <result column="print_status" property="printStatus"/>
        <result column="fail_reason" property="failReason"/>
        <result column="print_time" property="printTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, print_task_id, printer_name, printer_serial_no, tag_id, tag_type, tag_material_id, print_concentration, consumer_code, user_id, company_id, asset_id, label_tid, print_type, print_status, fail_reason, print_time, create_by, create_time, is_delete
    </sql>


    <select id="assetLog" resultType="com.niimbot.means.UserPrintLogViewDto">
        SELECT
        al.print_time,
        CASE WHEN al.print_status = 1 THEN '成功' ELSE '失败' END AS print_status,
        al.user_id,
        CASE WHEN al.print_source = 1 THEN 'PC' WHEN al.print_source = 2 THEN 'Android'
        when al.print_source then 'iOS' ELSE 'PDA' END AS print_source,
        al.printer_name,
        aa.asset_name,
        aa.asset_code,
        aa.asset_category,
        aa.storage_area,
        aa.org_owner,
        aa.use_org,
        aa.use_person
        FROM
        as_user_print_log al JOIN as_asset aa ON al.asset_id = aa.id AND al.is_delete = 0
        AND al.company_id = #{companyId} AND al.user_id = #{userId}
        <where>
            <if test="em.printBegin!=null">
                and al.print_time <![CDATA[ >= ]]> CONCAT(#{em.printBegin}, ' 00:00:00')
            </if>
            <if test="em.printEnd!=null">
                and al.print_time <![CDATA[ <= ]]> CONCAT(#{em.printEnd}, ' 23:59:59')
            </if>
            <if test="em.printStatus!=null and em.printStatus!=0">
                and al.print_status = #{em.printStatus}
            </if>
            <if test="em.assetName!=null and em.assetName!=''">
                and aa.asset_name like concat('%',#{em.assetName},'%')
            </if>
            <if test="em.assetCode!=null and em.assetCode!=''">
                and aa.asset_code like concat('%',#{em.assetCode},'%')
            </if>
            <if test="em.printTaskId!=null">
                and al.print_task_id = #{em.printTaskId}
            </if>
        </where>
        order by al.create_time
    </select>

    <select id="fullPrintLogPage" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        as_user_print_log t1
        WHERE
        t1.company_id = #{companyId}
        AND t1.print_task_id = #{em.printTaskId}
        AND t1.print_type = #{em.printType}
        AND t1.is_delete = 0
        <if test="em.kw != null and em.kw != '' and em.kw != 'null'">
            <if test="em.printType == 1">
                AND
                (
                t1.asset_data ->> '$.assetCode' LIKE CONCAT( '%', #{em.kw}, '%' )
                OR
                t1.asset_data ->> '$.assetName' LIKE CONCAT( '%', #{em.kw}, '%' )
                )
            </if>
            <if test="em.printType == 2">
                AND
                (
                t1.asset_data ->> '$.materialCode' LIKE CONCAT( '%', #{em.kw}, '%' )
                OR
                t1.asset_data ->> '$.materialName' LIKE CONCAT( '%', #{em.kw}, '%' )
                OR
                t1.asset_data ->> '$.barCode' LIKE CONCAT( '%', #{em.kw}, '%' )
                )
            </if>
            <if test="em.printType == 3">
                AND
                (
                t1.asset_data ->> '$.areaName' LIKE CONCAT( '%', #{em.kw}, '%' )
                OR
                t1.asset_data ->> '$.areaCode' LIKE CONCAT( '%', #{em.kw}, '%' )
                )
            </if>
        </if>
        <if test="em.printStatus != null">
            AND t1.print_status = #{em.printStatus}
        </if>
        <if test="em.sidx != null and em.sidx == '' and em.sidx != 'null' and em.order != null and em.order != '' and em.order != 'null'">
            ORDER BY ${em.sidx} ${em.order}
        </if>
        <if test="em.sidx == null or em.sidx == '' or em.sidx == 'null'">
            ORDER BY t1.print_time DESC
        </if>
    </select>

    <select id="userByCount" resultType="com.niimbot.system.statistics.StatisticsEquipmentDto">
        SELECT
            printer_name as name,
            count(DISTINCT company_id) as company_num,
            count(DISTINCT user_id) as user_num,
            count(id) as print_num
        FROM
            as_user_print_log
        GROUP BY
            printer_name
    </select>

    <select id="selectPrintLogMaterialName" resultType="string">
        SELECT CONCAT(t2.size_long,'*',t2.size_wide) AS size FROM as_user_tag t1 LEFT JOIN as_tag_size t2 ON t1.size_id = t2.id WHERE t1.id = #{tagId};
    </select>

</mapper>
