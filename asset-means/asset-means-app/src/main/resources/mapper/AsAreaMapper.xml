<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAreaMapper">

    <select id="listArea" resultType="com.niimbot.means.AreaDto">
        SELECT
        id,
        area_name,
        area_code,
        pid,
        LEVEL,
        paths,
        org_id,
        sort_num,
        create_by,
        create_time,
        update_by,
        update_time
        FROM
        as_area area
        WHERE
        is_delete = 0
        <!-- 区域权限 -->
        <if test="areaSql!=null and areaSql!=''">
            and area.id in ${areaSql}
        </if>
        <if test="em.orgId != null">
            and org_id = #{em.orgId}
        </if>
        <if test="em.pid != null">
            and pid = #{em.pid}
        </if>
        <if test="em.kw != null and em.kw != ''">
            and (area.area_name like concat('%',#{em.kw},'%')
            or
            area.area_code like concat('%',#{em.kw},'%'))
        </if>
        <if test="em.includeIds != null and em.includeIds.size > 0">
            and area.id in
            <foreach collection="em.includeIds" index="index" item="id" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY sort_num asc, create_time asc
    </select>

    <resultMap id="printAreaPageMap" type="com.niimbot.system.AreaAssetDto">
        <id column="id" property="id"/>
        <result column="area_code" property="code"/>
        <result column="area_name" property="name"/>
        <result column="area_info" property="areaInfo"/>
        <result column="pid" property="pid"/>
        <result column="paths" property="paths"/>
        <result column="parent_info" property="parentInfo"/>
        <result column="asset_count" property="assetCount"/>
        <result column="idle_asset_count" property="idleAssetCount"/>
        <result column="check_asset_count" property="checkAssetCount"/>
        <result column="use_asset_count" property="useAssetCount"/>
        <result column="borrow_asset_count" property="borrowAssetCount"/>
        <result column="wait_service_asset_count" property="waitServiceAssetCount"/>
        <result column="service_asset_count" property="serviceAssetCount"/>
    </resultMap>



    <select id="printAreaPage" resultMap="printAreaPageMap">
        SELECT
        t1.id,
        t1.area_code,
        t1.area_name,
        CONCAT( t1.area_name, '（', t1.area_code, '）' ) AS area_info,
        t1.pid,
        t1.paths,
        IFNULL(( SELECT CONCAT( area_name, '（', area_code, '）' ) FROM as_area WHERE id = t1.pid ),
        '顶级区域' ) AS parent_info
        FROM
        as_area t1
        WHERE
        t1.company_id = #{companyId} and t1.is_delete = 0
        -- 仅统计本级区域下的资产
        <if test="em.id != null">
            AND ( t1.id = #{em.id} OR t1.paths LIKE CONCAT( '%', #{em.id}, '%' ) )
        </if>
        <if test="em.includeAreaIds != null and em.includeAreaIds.size() > 0">
            AND t1.id IN
            <foreach collection="em.includeAreaIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="em.kw != null and em.kw != '' and em.kw != 'null'">
            AND ( t1.area_code LIKE CONCAT( '%', #{em.kw}, '%' ) OR t1.area_name LIKE CONCAT( '%', #{em.kw}, '%' ) )
        </if>
        <!-- 区域权限 -->
        <if test="areaSql!=null and areaSql!=''">
            AND t1.id IN ${areaSql}
        </if>
        ORDER BY
        t1.`level` ASC,
        t1.create_time ASC
    </select>

    <!--<select id="listByIds" resultType="com.niimbot.asset.means.model.AsArea">
        SELECT
        id,
        area_name,
        area_code,
        pid,
        LEVEL,
        paths,
        sort_num,
        create_by,
        create_time,
        update_by,
        update_time
        FROM
        as_area area
        WHERE
        is_delete = 0
        and company_id = #{companyId}
        <if test="ids != null and ids.size > 0">
            and id in
            <foreach item="item" index="index" collection="ids" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>-->

    <select id="areaRefAsset" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            as_area a
                LEFT JOIN as_asset b ON CONVERT(a.id, CHAR) b.storage_area and a.company_id = b.company_id
        WHERE
            b.is_delete = 0
          AND a.is_delete = 0
          AND (a.id = #{areaId} or a.paths like concat('%,',#{areaId},',%'))
          AND b.status != 4
        limit 1
    </select>

    <select id="listByOrg" resultType="com.niimbot.means.AreaDto">
        SELECT
        id,
        area_name,
        area_code,
        pid,
        LEVEL,
        paths,
        sort_num,
        create_by,
        create_time,
        update_by,
        update_time,
        org_id
        FROM
        as_area area
        WHERE
        is_delete = 0 and org_id in
        <foreach item="item" index="index" collection="orgIds" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
        ORDER BY sort_num asc, create_time asc
    </select>

    <select id="listSimpleWithPerms" resultType="com.niimbot.asset.framework.model.DictDataDto">
        SELECT
            a.id AS value,
            a.area_name AS label
        FROM
            as_area a
        <choose>
            <when test="authorityType != null and authorityType == @com.niimbot.asset.framework.constant.AssetConstant@AUTHORITY_TYPE_DEPT_ONLY">
                JOIN as_org o ON a.org_id = o.company_owner
            </when>
            <when test="authorityType != null and authorityType == @com.niimbot.asset.framework.constant.AssetConstant@AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT">
                join as_org oc on a.org_id = oc.id
                join as_org o on oc.is_delete = 0 and o.company_id = oc.company_id and oc.org_type = 1 and (o.company_owner = oc.id or FIND_IN_SET(o.company_owner, oc.paths))
            </when>
        </choose>
            JOIN as_user_org uo ON o.id = uo.org_id
            JOIN as_cus_employee e ON uo.user_id = e.id and o.company_id = e.company_id
        WHERE
            o.is_delete = 0
          AND e.is_delete = 0
          AND a.is_delete = 0
          AND a.company_id = e.company_id
          AND a.company_id = #{companyId}
          AND e.id = #{currentUserId}
    </select>

    <select id="hasPermAreaIds" resultType="java.lang.Long">
        select id from as_area
        where is_delete = 0
        <if test="areaIds != null and areaIds.size() > 0">
            and id in
            <foreach collection="areaIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 区域权限 -->
        <if test="areaSql!=null and areaSql!=''">
            AND id IN ${areaSql}
        </if>
    </select>
</mapper>
