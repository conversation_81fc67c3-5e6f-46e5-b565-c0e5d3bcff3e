<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAssetRelationMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsAssetRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="asset_id" jdbcType="BIGINT" property="assetId" />
    <result column="sub_asset_id" jdbcType="BIGINT" property="subAssetId" />
    <result column="relation_type" jdbcType="INTEGER" property="relationType" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, asset_id, sub_asset_id, relation_type, create_by, create_time, update_by, update_time
  </sql>

  <select id="selectMainAssetId" resultType="java.lang.Long">
    select distinct asset_id
    from as_asset_relation
    where company_id = #{companyId}
  </select>

  <select id="selectSubAssetId" resultType="java.lang.Long">
    select sub_asset_id
    from as_asset_relation
    where company_id = #{companyId}
  </select>

  <select id="selectAllAssetId" resultType="java.lang.Long">
    select distinct asset_id
    from as_asset_relation
    where company_id = #{companyId}
    union
    select sub_asset_id
    from as_asset_relation
    where company_id = #{companyId}
  </select>
</mapper>