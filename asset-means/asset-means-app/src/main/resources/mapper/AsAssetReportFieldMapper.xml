<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAssetReportFieldMapper">
  <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsAssetReportField">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="asset_id" jdbcType="BIGINT" property="assetId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="expire_date" jdbcType="TIMESTAMP" property="expireDate" />
    <result column="handle_date" jdbcType="TIMESTAMP" property="handleDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, asset_id, company_id, expire_date, handle_date, create_time, update_time
  </sql>
</mapper>