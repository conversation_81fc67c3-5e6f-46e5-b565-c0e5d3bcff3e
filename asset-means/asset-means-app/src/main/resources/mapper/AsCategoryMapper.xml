<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsCategoryMapper">


    <select id="listByCompanyId" resultType="com.niimbot.asset.means.model.AsCategory">
        select
        id, category_name, category_code, company_id, source_id, pid, paths, level, sort_num,
        is_delete, create_by, create_time, update_by, update_time, industry_id
        from as_category where is_delete = 0 and company_id = #{companyId}
        <if test="categories != null and categories.size > 0">
            and id in
            <foreach item="item" index="index" collection="categories" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="categoryRefAsset" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            as_category a
        JOIN as_asset b ON a.id = CAST(b.asset_category as DECIMAL(19)) and a.company_id = b.company_id
        WHERE
            b.is_delete = 0
            AND a.is_delete = 0
            AND (a.id = #{categoryId} or a.paths like concat('%,',#{categoryId},',%'))
            AND b.status != 4
        limit 1
    </select>

    <select id="listCate" resultType="com.niimbot.means.CategoryDto">
        SELECT
            id,
            category_name,
            category_code,
            company_id,
            source_id,
            pid,
            paths,
            LEVEL,
            sort_num,
            is_delete,
            create_by,
            create_time,
            update_by,
            update_time,
            industry_id
        FROM
            as_category
        WHERE
            is_delete = 0
        <!-- 分类权限 -->
        <if test="cateSql!=null and cateSql!=''">
            and id in ${cateSql}
        </if>
        <if test="ew.companyId!=null">

            and company_id = #{ew.companyId}
        </if>
        <if test="ew.kw != null and ew.kw != ''">
            and (category_name like concat('%',#{ew.kw},'%')
            or
            category_code like concat('%',#{ew.kw},'%'))
        </if>
        <if test="ew.includeIds != null and ew.includeIds.size > 0">
            and id in
            <foreach collection="ew.includeIds" index="index" item="id" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY sort_num asc, create_time asc
    </select>

    <select id="hasPermCateIds" resultType="java.lang.Long">
        select id from as_category
        where is_delete = 0
        <if test="cateIds != null and cateIds.size() > 0">
            and id in
            <foreach collection="cateIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <!-- 分类权限 -->
        <if test="cateSql!=null and cateSql!=''">
            and id in ${cateSql}
        </if>
    </select>

</mapper>
