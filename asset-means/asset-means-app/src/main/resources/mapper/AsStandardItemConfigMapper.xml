<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsStandardItemConfigMapper">

    <select id="formAttribute" resultType="com.niimbot.means.StandardItemConfigDto">
        SELECT
            f.item_name,
            f.item_code,
            f.item_type,
            s.select_sort,
            s.asset_mapping,
            s.material_mapping
        FROM
            as_standard_item_config s
            JOIN as_biz_form_item f ON ( s.form_id = f.form_id AND f.item_code = s.form_item_code )
        WHERE
            s.standard_id = #{standardId}
        ORDER BY
            f.sort
    </select>

</mapper>
