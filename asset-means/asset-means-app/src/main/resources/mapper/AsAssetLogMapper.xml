<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAssetLogMapper">

    <!-- custom查询映射结果 -->
    <resultMap id="customResultMap" type="com.niimbot.means.AsAssetLogDto">
        <id column="id" property="id"/>
        <result column="asset_id" property="assetId"/>
        <result column="action_type" property="actionType"/>
        <result column="action_name" property="actionName"/>
        <result column="action_content" property="actionContent"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_id" property="orderId"/>
        <result column="handle_time" property="handleTime"/>
    </resultMap>


    <select id="customPage" resultMap="customResultMap">
        select
        t.id,
        t.company_id,
        t.asset_id,
        t.action_type,
        t.action_name,
        t.action_content,
        t.original_data,
        t.change_data,
        t.create_time,
        t.create_by,
        t.order_no,
        t.order_id,
        t.handle_time
        from
        as_asset_log t
        <where>
            <if test="ew.assetId!=null">
                and t.asset_id = #{ew.assetId}
            </if>
            <if test="ew.actionName!=null and ew.actionName!=''">
                and t.action_name = #{ew.actionName}
            </if>
            <if test="ew.handleTime!=null and ew.handleTime.size==2">
                <if test="ew.handleTime[0]!=null and ew.handleTime[0]!='' and ew.handleTime[0]!='null'">
                    and t.create_time &gt;= CONCAT(#{ew.handleTime[0]}, ' 00:00:00')
                </if>
                <if test="ew.handleTime[1]!=null and ew.handleTime[1]!='' and ew.handleTime[1]!='null'">
                    and t.create_time &lt;= CONCAT(#{ew.handleTime[1]}, ' 23:59:59')
                </if>
            </if>
            <!-- 资产ids -->
            <if test="ew.assetIds != null and ew.assetIds.size() > 0">
                and t.asset_id in
                <foreach collection="ew.assetIds" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
            <!-- 处理类型ids -->
            <if test="ew.handleType != null and ew.handleType.size() > 0">
                and t.action_type in
                <foreach collection="ew.handleType" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by t.create_time desc
    </select>


</mapper>
