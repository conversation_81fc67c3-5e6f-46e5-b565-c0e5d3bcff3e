<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAssetQueryViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsAssetQueryView">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="name" property="name" />
        <result column="conditions" property="conditions"
                typeHandler="com.niimbot.asset.means.handle.AsQueryConditionListTypeHandler"/>
        <result column="is_show" property="isShow" />
        <result column="is_can_edit" property="isCanEdit" />
        <result column="is_can_del" property="isCanDel" />
        <result column="sort_num" property="sortNum" />
        <result column="is_delete" property="isDelete" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, name, conditions, is_show, is_can_edit, is_can_del, sort_num, is_delete, create_by, create_time, update_by, update_time
    </sql>

    <select id="querySystemList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        as_asset_query_view
        where company_id = 0 and is_delete = 0
        order by sort_num
    </select>

</mapper>
