<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsStandardMapper">

    <select id="cusPage" resultType="com.niimbot.means.StandardListDto">
        SELECT
        st.id,
        st.standard_name,
        ( SELECT count(*) FROM as_product p WHERE p.standard_id = st.id AND p.is_delete = 0 ) AS
        product_num,
        e.emp_name as update_by_text,
        st.update_time
        FROM
        as_standard st
        left join as_cus_employee e on (st.update_by = e.id and e.company_id = st.company_id)
        WHERE
        st.is_delete = 0
        and st.company_id in
        <foreach collection="companyIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="ew.name!=null and ew.name!=''">
            and st.standard_name LIKE concat('%',#{ew.name},'%')
        </if>
        <if test="ew.sidx == null or ew.sidx == ''">
            ORDER BY st.create_time DESC
        </if>
    </select>

    <select id="cusList" resultType="com.niimbot.means.StandardListDto">
        SELECT
        st.id,
        st.standard_name,
        CASE st.company_id
        WHEN 0 THEN 2 ELSE 1 END as type
        FROM
        as_standard st
        WHERE
        st.is_delete = 0
        AND st.company_id in (0, #{companyId})
        AND st.id NOT IN (
        select s1.id from as_standard s1 join as_standard s2
        on (s1.is_delete = 0
        and s2.is_delete = 0
        and s1.company_id != s2.company_id
        and s1.standard_name = s2.standard_name
        and s2.company_id in (0, #{companyId}))
        where s1.company_id = 0
        )
        <if test="name!=null and name!=''">
            and st.standard_name LIKE concat('%',#{name},'%')
        </if>
        order by st.create_time desc
    </select>

    <select id="getByIdNoPerm" resultType="com.niimbot.asset.means.model.AsStandard">
        select id, standard_name, company_id, form_id, is_delete, create_by, create_time, update_by, update_time
        from as_standard
        where is_delete = 0
        and company_id in (0, #{companyId})
        and id = #{standardId}
    </select>

    <select id="listNoPerm" resultType="com.niimbot.asset.means.model.AsStandard">
        select id, standard_name, company_id, form_id, is_delete, create_by, create_time, update_by, update_time
         from as_standard ${ew.customSqlSegment}
    </select>

    <select id="hasRef" resultType="java.lang.Integer">
        select sum(t.num) from (
            select count(standard_id) num from as_user_tag
             where is_delete = 0 and company_id = #{companyId} and standard_id = #{id}
            union all
            select count(standard_id) num from as_asset
            where is_delete = 0 and company_id = #{companyId} and standard_id = #{id}
            union all
            select count(standard_id) num from as_material
            where is_delete = 0 and company_id = #{companyId} and standard_id = #{id}
            union all
            select count(standard_id) num from as_product
            where is_delete = 0 and company_id = #{companyId} and standard_id = #{id}
            ) t
    </select>
</mapper>
