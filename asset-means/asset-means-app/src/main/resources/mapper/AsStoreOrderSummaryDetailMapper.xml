<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsStoreOrderSummaryDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsStoreOrderSummaryDetail">
        <id column="id" property="id" />
        <result column="store_order_id" property="storeOrderId" />
        <result column="product_id" property="productId" />
        <result column="standard_id" property="standardId" />
        <result column="store_price" property="storePrice" />
        <result column="quantity" property="quantity" />
        <result column="money" property="money" />
        <result column="asset_snapshot_data" property="assetSnapshotData"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, store_order_id, product_id, standard_id, store_price, quantity, money, asset_snapshot_data
    </sql>

</mapper>
