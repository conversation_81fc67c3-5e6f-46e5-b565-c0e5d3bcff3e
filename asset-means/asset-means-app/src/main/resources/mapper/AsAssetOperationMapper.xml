<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsAssetOperationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.means.AssetOperationDto">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="order_type" property="orderType"/>
        <collection property="assetStatusList" ofType="com.niimbot.means.AssetStatusDto">
            <id column="sid" property="id"/>
            <result column="scode" property="code"/>
            <result column="sname" property="name"/>
        </collection>
    </resultMap>

    <select id="getAssetOperationByStatus" resultType="com.niimbot.means.AssetOperationDto">
        SELECT
        o.id, o.code, o.name, o.order_type
        FROM
        as_asset_operation AS o
        JOIN as_asset_status_operation AS so ON o.id = so.operation_id
        JOIN as_asset_status AS s ON s.id = so.status_id
        WHERE
        s.id IN
        <foreach collection="list" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
        GROUP BY
        o.id
    </select>

    <select id="allAssetOpt" resultMap="BaseResultMap">
        SELECT
            aao.id,
            aao.CODE,
            aao.NAME,
            aao.order_type,
            aas.id AS sid,
            aas.CODE AS scode,
            aas.NAME AS sname
        FROM
            as_asset_operation aao
            JOIN as_asset_status_operation aaso ON aao.id = aaso.operation_id
            JOIN as_asset_status aas ON aas.id = aaso.status_id
        WHERE
            aao.CODE != 'asset_add'
        ORDER BY aao.id
    </select>

    <select id="assetOptByOrderType" resultMap="BaseResultMap">
        SELECT
                aao.id,
                aao.CODE,
                aao.NAME,
                aao.order_type,
                aas.id AS sid,
                aas.CODE AS scode,
                aas.NAME AS sname
        FROM
                as_asset_operation aao
                        JOIN as_asset_status_operation aaso ON aao.id = aaso.operation_id
                        JOIN as_asset_status aas ON aas.id = aaso.status_id
        WHERE
               aao.order_type = #{orderType}
    </select>
</mapper>
