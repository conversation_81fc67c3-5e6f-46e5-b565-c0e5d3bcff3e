<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.mapper.AsOrderTypeMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.niimbot.asset.means.model.AsOrderTypeMenu">
        <id column="order_type" property="orderType" />
        <result column="menu_code" property="menuCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        order_type, menu_code
    </sql>
    <select id="getMenuById" resultType="com.niimbot.asset.system.model.AsCusMenu">
        SELECT
            m.id,
            m.menu_name,
            m.menu_code,
            m.fe_route,
            m.menu_type
        FROM
            as_order_type_menu AS tm
            LEFT JOIN as_cus_menu AS m ON m.menu_code = tm.menu_code
        WHERE
            tm.order_type = #{orderType}
    </select>

</mapper>
