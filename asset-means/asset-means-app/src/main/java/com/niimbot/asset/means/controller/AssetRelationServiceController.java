package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.means.service.AssetRelationService;
import com.niimbot.means.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 关联资产
 * <AUTHOR>
 * @date 2023/8/8 上午10:53
 */
@RestController
@RequestMapping("/server/system/assetRelation/")
public class AssetRelationServiceController {

    @Autowired
    private AssetRelationService assetRelationService;


    /**
     * 查询已存在的主资产或子资产
     * @param type 1-主资产 2-子资产 3-主和子资产
     * @return
     */
    @GetMapping("getExistAsset")
    public List<Long> getExistAsset(@RequestParam(value = "type", required = true) Integer type) {
        return assetRelationService.getExistAsset(type);
    }

    /**
     * 配置资产关联关系
     * @param configDto
     * @return
     */
    @PostMapping("config")
    public Boolean config(@RequestBody AssetRelationConfigDto configDto) {
        return assetRelationService.config(configDto);
    }

    /**
     * 批量修改资产关联关系
     * @param relationDto
     * @return
     */
    @PostMapping("alterRelationType")
    public Boolean alterRelationType(@RequestBody AlterAssetRelationDto relationDto) {
        return assetRelationService.alterRelation(relationDto);
    }

    /**
     * 取消组合关系
     * @param relationDto
     * @return
     */
    @PostMapping("cancelRelation")
    public Boolean cancelRelation(@RequestBody CancelAssetRelationDto relationDto) {
        return assetRelationService.cancelRelation(relationDto);
    }

    /**
     * 解除关联关系
     * @param relationDto
     * @return
     */
    @PostMapping("remove")
    public Boolean removeRelation(@RequestBody CancelAssetRelationDto relationDto) {
        return assetRelationService.removeRelation(relationDto);
    }

    /**
     * 资产关联关系分页查询
     * @param queryDto
     * @return
     */
    @PostMapping("page")
    public IPage<AssetRelationDto> page(@RequestBody AssetQueryConditionDto queryDto) {
        return assetRelationService.page(queryDto);
    }

    /**
     * 资产关联关系分页查询
     * @param queryDto
     * @return
     */
    @PostMapping("subAssetPage")
    public IPage<AssetRelationDto> subAsset(@RequestBody AssetRelationQueryConditionDto queryDto) {
        return assetRelationService.subAsset(queryDto);
    }

    /**
     * 资产关联关系分页查询-app
     * @param queryDto
     * @return
     */
    @PostMapping("appPage")
    public IPage<AssetRelationAppDto> appPage(@RequestBody AssetQueryConditionDto queryDto) {
        return assetRelationService.appPage(queryDto);
    }

    /**
     * 资产关联关系分页查询-app
     * @param queryDto
     * @return
     */
    @PostMapping("subAssetAppPage")
    public IPage<AssetRelationAppDto> subAssetAppPage(@RequestBody AssetRelationQueryConditionDto queryDto) {
        return assetRelationService.subAssetAppPage(queryDto);
    }

    /**
     * 根据子资产查询主资产信息-app接口
     * @param subAssetId
     * @return
     */
    @GetMapping("getBySubAssetIdApp/{subAssetId}")
    public AssetRelationAppDto getBySubAssetId(@PathVariable("subAssetId") Long subAssetId) {
        return assetRelationService.getBySubAssetId(subAssetId);
    }

    @GetMapping("existSubAsset")
    public List<Long> existSubAsset(@RequestParam("assetIdList") List<Long> assetIdList) {
        return assetRelationService.existSubAsset(assetIdList);
    }

    @GetMapping("getMainAssetId")
    public Long getMainAssetId(@RequestParam("assetId") Long assetId) {
        return assetRelationService.getMainAssetId(assetId);
    }
}
