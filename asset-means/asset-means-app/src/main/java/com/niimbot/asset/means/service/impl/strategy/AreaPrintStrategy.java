package com.niimbot.asset.means.service.impl.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAreaMapper;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsUserPrintTaskService;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.PrintDataDto;
import com.niimbot.means.PrintDataViewDto;
import com.niimbot.means.PrintPdfDto;
import com.niimbot.means.PrintQueryDataDto;
import com.niimbot.system.AreaAssetDto;
import com.niimbot.system.PrintAreaPageQueryDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AreaPrintStrategy implements PrintStrategy {

    private final AreaService areaService;

    private final AsUserPrintTaskService printTaskService;

    private final DataScopeStrategyManager dataScopeStrategyManager;

    private final AsAreaMapper areaMapper;

    @Override
    public Short printType() {
        return DictConstant.PRINT_TYPE_AREA;
    }

    @Override
    public void resolvePrintData(AsUserPrintTask printTask, PrintQueryDataDto queryDto) {
        PrintAreaPageQueryDto printAreaPageQueryDto = (PrintAreaPageQueryDto) queryDto;
        printAreaPageQueryDto.setPageSize(50000);
        Page<AreaAssetDto> page = areaService.printAreaPage(printAreaPageQueryDto);
        List<Long> areaIds = page.getRecords().stream().map(AreaAssetDto::getId).collect(toList());
        if (CollUtil.isEmpty(areaIds)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "区域ID");
        }
        printTask.setAssets(areaIds);
        printTask.setQueryData((JSONObject) JSONObject.toJSON(queryDto));
        // 为了设置任务名称
        List<Long> idsForTaskName = areaIds.size() >= 3 ? areaIds.subList(0, 3) : areaIds;
        printTask.setAssetsSnapshot(
                areaService.list(
                        Wrappers.lambdaQuery(AsArea.class)
                                .select(AsArea::getAreaName)
                                .in(AsArea::getId, idsForTaskName)
                ).stream().map(asArea -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("areaName", asArea.getAreaName());
                    return jsonObject;
                }).collect(toList())
        );
    }

    @Override
    public void translate(List<JSONObject> dataList, String companyName, Long standardId) {
        // nothing
    }

    @Override
    public void dealPrintSnapshot(List<AsPrintDataSnapshot> snapshots) {
        AsUserPrintTask printTask = printTaskService.getById(snapshots.get(0).getTaskId());
        PrintAreaPageQueryDto dto = printTask.getQueryData().toJavaObject(PrintAreaPageQueryDto.class);
        dto.setIncludeAreaIds(snapshots.stream().map(AsPrintDataSnapshot::getDataId).collect(Collectors.toSet()));
        dto.setPageNum(1L);
        dto.setPageSize(snapshots.size());
        ConcurrentMap<Long, AreaAssetDto> map = areaService.printAreaPage(dto).getRecords().stream().collect(Collectors.toConcurrentMap(AreaAssetDto::getId, v -> v));
        snapshots.forEach(snapshot -> {
            if (Objects.isNull(snapshot.getSnapshot())) {
                AreaAssetDto areaAssetDto = map.get(snapshot.getDataId());
                JSONObject object = new JSONObject();
                object.put("id", String.valueOf(areaAssetDto.getId()));
                object.put("areaName", areaAssetDto.getName());
                object.put("areaCode", areaAssetDto.getCode());
                object.put("qrCode", "area:" + areaAssetDto.getId());
                snapshot.setSnapshot(object);
            }
        });
    }

    @Override
    public void dealResultPrintTask(PrintPdfDto dto, PrintDataDto printDataDto, JSONObject templateJson,
                                    List<PrintDataViewDto.DataDto> resultList) {
        for (JSONObject area : dto.getDataJson()) {
            if (ObjectUtil.isNotEmpty(templateJson)) {
                String jsonString = templateJson.toJSONString();
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                List<JSONObject> elements = jsonObject.getObject("elements", new TypeReference<List<JSONObject>>() {
                });

                // 二维码填充
                List<JSONObject> resElementJson = elements.stream()
                        .filter(el -> !"text".equals(el.getString("type")))
                        .peek(el -> {
                            if ("qrcode".equals(el.getString("type"))) {
                                el.put("value", area.getString("id"));
                            }
                        }).collect(toList());
                List<JSONObject> textList = elements.stream()
                        .filter(el -> "text".equals(el.getString("type")))
                        .peek(jo -> {
                            String value = jo.getString("value");
                            if (value.contains("区域编码：XXXXXX")) {
                                jo.put("value", value.replaceAll("XXXXXX", area.getString("code")));
                            }
                            if (value.contains("区域名称：XXXXXX")) {
                                jo.put("value", value.replaceAll("XXXXXX", area.getString("name")));
                            }
                        })
                        .collect(toList());

                resElementJson.addAll(textList);
                jsonObject.put("elements", resElementJson);

                PrintDataViewDto.AssetDataDto dataDto = new PrintDataViewDto.AssetDataDto();
                dataDto.setLabelEpcid(StrUtil.EMPTY);
                dataDto.setId(area.getLong("id"));
                dataDto.setJsonObject(JSON.toJSONString(jsonObject));
                resultList.add(dataDto);
            }

        }
    }

    @Override
    public List<JSONObject> getPrintData(PrintPdfDto dto, PrintDataDto printDataDto) {
        PrintAreaPageQueryDto queryDto = new PrintAreaPageQueryDto();
        queryDto.setId(dto.getAreaId());
        queryDto.setIncludeAreaIds(new HashSet<>(dto.getIds()));
        queryDto.setPageNum(dto.getPageNum());
        queryDto.setPageSize(dto.getPageSize());
        String areaSql = dataScopeStrategyManager.simplePermsSql(DataPermType.AREA);
        // 获取满足条件的区域
        Page<AreaAssetDto> areaPage = areaMapper.printAreaPage(queryDto.buildIPage(), queryDto, areaSql, LoginUserThreadLocal.getCompanyId());
        List<AreaAssetDto> areaDtos = areaPage.getRecords();
        return areaDtos.stream().map(area -> (JSONObject) JSONObject.toJSON(area)).collect(toList());
    }
}
