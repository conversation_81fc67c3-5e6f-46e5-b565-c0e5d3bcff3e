package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsAssetMapper;
import com.niimbot.asset.means.mapper.AsAssetReportFieldMapper;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetReportField;
import com.niimbot.asset.means.service.AssetReportFieldService;
import com.niimbot.finance.AssetAttributeAlterMessageDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/27 下午3:26
 */
@Slf4j
@Service
public class AssetReportFieldServiceImpl extends ServiceImpl<AsAssetReportFieldMapper, AsAssetReportField> implements AssetReportFieldService {

    @Autowired
    private AsAssetMapper assetMapper;

    @Override
    public Boolean processHandleTime(AssetAttributeAlterMessageDto messageBody) {
        AsAsset asset = assetMapper.selectById(messageBody.getAssetId());
        if (Objects.isNull(asset)) {
            return Boolean.FALSE;
        }

        AsAssetReportField asAssetReportField = new AsAssetReportField()
                .setId(asset.getId())
                .setAssetId(asset.getId())
                .setCompanyId(asset.getCompanyId())
                .setHandleDate(messageBody.getHandlerTime())
                .setUpdateTime(LocalDateTime.now())
                ;
        return this.saveOrUpdate(asAssetReportField);
    }

    @Override
    public Boolean processExpireTime(List<AsAsset> assetList) {
        if (CollUtil.isEmpty(assetList)) {
            return Boolean.FALSE;
        }

        List<AsAssetReportField> assetReportFieldList = new ArrayList<>();
        for (AsAsset asset : assetList) {
            if (StrUtil.isBlank(asset.getAssetData().getString("useTimeLimit")) || "null".equalsIgnoreCase(asset.getAssetData().getString("useTimeLimit"))) {
                continue;
            }

            //使用期限
            Long useTimeLimit = asset.getAssetData().getLong("useTimeLimit");
            LocalDateTime startTime = null;

            if (StrUtil.isBlank(asset.getAssetData().getString("buyTime"))
                    || "null".equalsIgnoreCase(asset.getAssetData().getString("buyTime"))
                    || asset.getAssetData().getLong("buyTime") == 0L) {
                startTime = asset.getCreateTime();
            } else {
                Long buyTime = asset.getAssetData().getLong("buyTime");
                startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(buyTime), ZoneId.systemDefault());
            }

            LocalDateTime expireTime = startTime.plusMonths(useTimeLimit);

            AsAssetReportField asAssetReportField = new AsAssetReportField()
                    .setId(asset.getId())
                    .setAssetId(asset.getId())
                    .setCompanyId(asset.getCompanyId())
                    .setExpireDate(expireTime)
                    .setUpdateTime(LocalDateTime.now())
                    ;
            assetReportFieldList.add(asAssetReportField);
        }

        if (!CollUtil.isEmpty(assetReportFieldList)) {
            return this.saveOrUpdateBatch(assetReportFieldList);
        }
        return Boolean.TRUE;
    }
}
