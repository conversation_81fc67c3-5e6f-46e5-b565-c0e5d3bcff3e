package com.niimbot.asset.means.domain.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.means.mapstruct.AssetCategoryMapStruct;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.asset.system.abs.AssetCategoryAbs;
import com.niimbot.asset.system.dto.AssetCategoryLoadCacheCmd;
import com.niimbot.asset.system.dto.AssetCategoryRegisterCopyCmd;
import com.niimbot.asset.system.dto.AssetCategorySaveBatchCmd;
import com.niimbot.asset.system.dto.CommonIndustryAssetCategoryListQry;
import com.niimbot.asset.system.dto.clientobject.AssetCategoryCO;
import com.niimbot.framework.dataperm.object.Tuple2;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryQueryDto;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/13 16:48
 */
@RestController
@RequestMapping("/client/abs/means/assetCategoryAbs/")
@RequiredArgsConstructor
public class AssetCategoryAbsImpl implements AssetCategoryAbs {
    private final CategoryService categoryService;
    private final AssetCategoryMapStruct assetCategoryMapStruct;

    @Override
    public List<AssetCategoryCO> listPermission(Long companyId) {
        List<CategoryDto> categories = categoryService.cateList(new CategoryQueryDto().setFilterPerm(true).setCompanyId(companyId));
        return categories.stream().map(assetCategoryMapStruct::toDataObject).collect(Collectors.toList());
    }

    @Override
    public List<AssetCategoryCO> listCommonIndustryAssetCategory(
            CommonIndustryAssetCategoryListQry qry) {
        return categoryService
                .list(
                        Wrappers.<AsCategory>lambdaQuery()
                                .eq(AsCategory::getIndustryId, qry.getCommonIndustryId())
                                .eq(AsCategory::getCompanyId, qry.getCompanyId()))
                .stream()
                .map(assetCategoryMapStruct::toDataObject)
                .collect(Collectors.toList());
    }

    @Override
    public Tuple2<List<AssetCategoryCO>, Map<Long, Long>> registerCopyAssetCategory(
            AssetCategoryRegisterCopyCmd cmd) {
        Tuple2<List<AsCategory>, Map<Long, Long>> tuple2 =
                categoryService.registerCopy(
                        cmd.getCompanyId(), CollUtil.newArrayList(cmd.getCategories()));

        return new Tuple2(
                tuple2.getFirst().stream()
                        .map(assetCategoryMapStruct::toDataObject)
                        .collect(Collectors.toList()),
                tuple2.getSecond());
    }

    @Override
    public Boolean saveBatchAssetCategory(AssetCategorySaveBatchCmd cmd) {
        return categoryService.saveBatch(
                cmd.getCategories().stream()
                        .map(assetCategoryMapStruct::toEntity)
                        .collect(Collectors.toList()));
    }

    @Override
    public void loadCacheAssetCategory(AssetCategoryLoadCacheCmd cmd) {
        categoryService.loadCateCache(
                cmd.getCategories().stream()
                        .map(assetCategoryMapStruct::toEntity)
                        .collect(Collectors.toList()));
    }

    @Override
    public List<Long> hasPermCateIds(List<Long> cateIds) {
        return categoryService.hasPermCateIds(cateIds);
    }
}
