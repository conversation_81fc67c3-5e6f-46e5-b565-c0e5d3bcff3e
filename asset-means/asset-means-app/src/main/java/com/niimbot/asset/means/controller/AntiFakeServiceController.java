package com.niimbot.asset.means.controller;

import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.means.service.AntiFakeService;
import com.niimbot.system.AntiFakeCheckDto;
import com.niimbot.system.AntiFakeResDto;
import com.niimbot.system.MachineDetailDto;
import com.niimbot.system.MachineDetailResDto;
import com.niimbot.system.PrintInfoDto;

import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * 碳带防伪控制器
 *
 * <AUTHOR>
 * @Date 2021/03/11
 */
@RestController
@RequestMapping("server/system/antiFake")
@Slf4j
@Profile({Edition.SAAS, Edition.DING, Edition.WEIXIN})
public class AntiFakeServiceController {

    @Resource
    private AntiFakeService antiFakeService;

    /**
     * 碳带防伪判断
     *
     * @param antiFakeCheckDto 碳带防伪判断请求对象
     * @return Boolean
     */
    @PostMapping(value = "/check")
    public AntiFakeResDto checkAntiFake(@RequestBody AntiFakeCheckDto antiFakeCheckDto) {
        try {
            return antiFakeService.checkAntiFake(antiFakeCheckDto);
        } catch (Exception e) {
            log.error("碳带防伪接口异常", e);
            throw e;
        }
    }

    /**
     * 判断是否进行固件升级
     *
     * @param machineDetailDto 固件升级请求对象
     * @return Boolean
     */
    @PostMapping(value = "/machineCascadeDetail")
    public MachineDetailResDto machineCascadeDetail(@RequestBody MachineDetailDto machineDetailDto) {
        return antiFakeService.machineCascadeDetail(machineDetailDto);
    }

    /**
     * 将打印信息上传云打印服务
     *
     * @param printInfoDto 打印信息对象
     * @return Boolean
     */
    @PostMapping(value = "/uploadPrintInfo")
    public Boolean uploadPrintInfo(@RequestBody PrintInfoDto printInfoDto) {
        return antiFakeService.uploadPrintInfo(printInfoDto);
    }

}
