package com.niimbot.asset.means.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsStandardItemConfig;
import com.niimbot.means.StandardItemConfigDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 标准品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-27
 */
public interface AsStandardItemConfigMapper extends BaseMapper<AsStandardItemConfig> {

    List<StandardItemConfigDto> formAttribute(@Param("standardId") Long standardId);
}
