package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsAssetLogMapper;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.means.AsAssetLogDto;
import com.niimbot.means.AsAssetLogQueryDto;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-12-25
 */
@Service
public class AsAssetLogServiceImpl extends ServiceImpl<AsAssetLogMapper, AsAssetLog> implements AsAssetLogService {
    @Override
    public IPage<AsAssetLogDto> customPage(AsAssetLogQueryDto dto) {
        return this.getBaseMapper().customPage(dto.buildIPage(), dto);
    }

    @Override
    public List<AsAssetLogDto> customList(AsAssetLogQueryDto dto) {
        return this.getBaseMapper().customPage(dto);
    }
}
