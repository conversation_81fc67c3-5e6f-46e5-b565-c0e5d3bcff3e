package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAdminPrinterConcentrationMapper;
import com.niimbot.asset.means.model.AsAdminPrinterConcentration;
import com.niimbot.asset.means.service.AdminPrinterConcentrationService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备-材质-浓度关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Service
public class AdminPrinterConcentrationServiceImpl extends ServiceImpl<AsAdminPrinterConcentrationMapper, AsAdminPrinterConcentration> implements AdminPrinterConcentrationService {

    @Override
    public Long getFirstMaterialId(Long printerId) {
        // 首先获取用户模板的第一个，不存在，则获取系统模板的第一个
        Long firstMaterialId = this.baseMapper.getFirstUserMaterialId(LoginUserThreadLocal.getCurrentUserId(), printerId);

        // 判断设置是否支持此材质
        AsAdminPrinterConcentration printerConcentration = this.getOne(
                Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getMaterialId, firstMaterialId)
                        .eq(AsAdminPrinterConcentration::getUserId, 0L)
        );
        if (null == firstMaterialId || 0L == firstMaterialId || null == printerConcentration) {
            firstMaterialId = this.baseMapper.getFirstMaterialId(printerId);
        }

        return firstMaterialId;
    }

    @Override
    public AsAdminPrinterConcentration getConcentrationData(Long printerId, Long materialId) {
        return null;
    }

    @Override
    public Integer getDefaultConcentration(Long printerId, Long materialId) {
        // 首先获取用户上次使用此机型此材质对应的浓度，不存在，则获取此机型此材质对应的浓度
        Integer concentration = this.baseMapper.getDefaultUserConcentration(LoginUserThreadLocal.getCurrentUserId(), printerId, materialId);
        if (null == concentration || 0 == concentration) {
            concentration = this.baseMapper.getDefaultConcentration(printerId, materialId);
        }

        return concentration;
    }
}
