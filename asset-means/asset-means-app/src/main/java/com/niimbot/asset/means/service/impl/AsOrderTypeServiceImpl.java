package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsOrderTypeMapper;
import com.niimbot.asset.means.model.AsOrderType;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.OrderTypeDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2020/12/22 15:23
 */
@Service
public class AsOrderTypeServiceImpl extends ServiceImpl<AsOrderTypeMapper, AsOrderType>
        implements AsOrderTypeService {

    @Autowired
    private ActWorkflowService workflowService;

    @Override
    public void initCompanyOrderType(Long companyId) {
        List<AsOrderType> orderTypes = this.list(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, 0L));
        if (orderTypes.isEmpty()) {
            return;
        }
        List<AsOrderType> companyOrderTypes = orderTypes.stream()
                .peek(type ->
                        type.setId(null).setCompanyId(companyId))
                .collect(Collectors.toList());
        this.saveBatch(companyOrderTypes);
    }

    @Override
    public boolean syncCompanyOrderType(Long companyId) {
        List<AsOrderType> orderTypes = this.list(
                Wrappers.<AsOrderType>lambdaQuery()
                        .in(AsOrderType::getCompanyId, Arrays.asList(companyId, 0L)));
        List<Integer> exists = orderTypes.stream()
                .filter(type -> type.getCompanyId().equals(companyId))
                .map(AsOrderType::getType)
                .collect(Collectors.toList());
        List<AsOrderType> systemOrderTypes = orderTypes.stream()
                .filter(field -> field.getCompanyId().equals(0L))
                .filter(type -> {
                    if (CollUtil.isNotEmpty(exists)) {
                        return !exists.contains(type.getType());
                    }
                    return true;
                })
                .collect(Collectors.toList());
        List<AsOrderType> companyOrderTypes = systemOrderTypes.stream()
                .peek(type ->
                        type.setId(null).setCompanyId(companyId))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(companyOrderTypes)) {
            return true;
        }
        return this.saveBatch(companyOrderTypes);
    }

    @Override
    public Boolean enableWorkflow(Integer orderType) {
        AsOrderType one = getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsOrderType::getType, orderType));
        if (one == null) {
            throw new BusinessException(MeansResultCode.ORDER_TYPE_NOT_EXISTS, String.valueOf(orderType));
        }
        return workflowService.enableWorkflow(one.getActivitiKey());

    }

    @Override
    public List<OrderTypeDto> listOrderType() {
        return getBaseMapper().listOrderType(LoginUserThreadLocal.getCompanyId());
    }
}
