package com.niimbot.asset.means.controller;

import com.niimbot.asset.means.model.AsQueryType;
import com.niimbot.asset.means.service.AsAssetQueryFieldService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;

/**
 * 资产查询字段 前端控制器
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@RestController
@RequestMapping("server/means/queryField")
@RequiredArgsConstructor
public class AsAssetQueryFieldServiceController {

    private final AsAssetQueryFieldService assetQueryFieldService;

    @GetMapping(value = "/getOperators/{type}")
    public List<AsQueryType> getOperators(@PathVariable("type") String type) {
        return assetQueryFieldService.getOperators(type);
    }

    @GetMapping(value = "/getOperatorMap")
    public Map<String, List<AsQueryType>> getOperatorMap() {
        return assetQueryFieldService.getOperatorMap();
    }
}
