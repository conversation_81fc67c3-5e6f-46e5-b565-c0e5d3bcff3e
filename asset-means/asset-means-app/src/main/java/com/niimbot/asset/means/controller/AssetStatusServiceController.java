package com.niimbot.asset.means.controller;

import com.niimbot.asset.means.model.AsAssetStatus;
import com.niimbot.asset.means.service.AsAssetStatusService;
import com.niimbot.means.AssetStatusDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/12/29 12:25
 */

@RestController
@RequestMapping("server/means/assetStatus")
public class AssetStatusServiceController {

    private final AsAssetStatusService assetStatusService;

    @Autowired
    public AssetStatusServiceController(AsAssetStatusService assetStatusService) {
        this.assetStatusService = assetStatusService;
    }

    /**
     * 查询获取资产状态信息
     *
     * @param id 资产状态ID
     * @return AsAssetStatus 信息
     */
    @GetMapping(value = "/{assetStatusId}")
    public AsAssetStatus getInfo(@PathVariable(value = "assetStatusId") Integer id) {
        return assetStatusService.getById(id);
    }

    /**
     * 查询全部资产状态
     *
     * @return 状态集合
     */
    @GetMapping("/list")
    public List<AssetStatusDto> allStatus() {
        return assetStatusService.allStatus();
    }

    /**
     * 通过单据类型查询资产状态
     *
     * @param orderType 单据类型
     * @return 资产状态
     */
    @GetMapping(value = "/getAssetStatusByOrderType")
    public List<Integer> getAssetStatusByOrderType(@RequestParam("orderType") Integer orderType) {
        return assetStatusService.getAssetStatusByOrderType(orderType);
    }

}
