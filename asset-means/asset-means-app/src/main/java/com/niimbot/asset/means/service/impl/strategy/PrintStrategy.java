package com.niimbot.asset.means.service.impl.strategy;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.means.PrintDataDto;
import com.niimbot.means.PrintDataViewDto;
import com.niimbot.means.PrintPdfDto;
import com.niimbot.means.PrintQueryDataDto;

import java.util.List;

/**
 * 打印策略接口
 *
 * <AUTHOR>
 * @date 2021/11/25 14:36
 */
public interface PrintStrategy {
    String ORG_OWNER = "orgOwner";
    String USE_ORG = "useOrg";
    String ASSET_TAG = "assetTag";
    String COMPANY_NAME = "companyName";
    String COMPANY_OWNER = "companyOwner";
    String USE_ORG_COMPANY_OWNER = "useOrgCompanyOwner";
    String ASSET_ID = "assetId";

    Short printType();

    void resolvePrintData(AsUserPrintTask printTask,
                          PrintQueryDataDto queryDto);

    void translate(List<JSONObject> dataList, String companyName, Long standardId);

    void dealPrintSnapshot(List<AsPrintDataSnapshot> snapshots);

    void dealResultPrintTask(PrintPdfDto dto, PrintDataDto printDataDto, JSONObject templateJson,
                             List<PrintDataViewDto.DataDto> resultList);

    List<JSONObject> getPrintData(PrintPdfDto dto, PrintDataDto printDataDto);

    default String getDelimiterStr(Integer delimiterType) {
        String str = "";
        switch (delimiterType) {
            case DictConstant.PRINT_DELIMITER_ENGLISH_COLON:
                str = " : ";
                break;
            case DictConstant.PRINT_DELIMITER_CHINESE_COLON:
                str = "：";
                break;
            case DictConstant.PRINT_DELIMITER_LINE_THROUGH:
                str = " - ";
                break;
            case DictConstant.PRINT_DELIMITER_SPACE:
                str = " ";
                break;
            case DictConstant.PRINT_DELIMITER_NO_SEPARATOR:
                str = "";
                break;
            default:
                break;
        }
        return str;
    }

}
