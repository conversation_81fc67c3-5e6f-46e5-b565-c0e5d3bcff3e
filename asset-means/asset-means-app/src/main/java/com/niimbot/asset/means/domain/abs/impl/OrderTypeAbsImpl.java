package com.niimbot.asset.means.domain.abs.impl;

import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.asset.system.abs.OrderTypeAbs;
import com.niimbot.asset.system.dto.OrderTypeInitCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/client/abs/means/orderTypeAbs")
@RequiredArgsConstructor
public class OrderTypeAbsImpl implements OrderTypeAbs {

    private final AsOrderTypeService orderTypeService;

    @Override
    public void initCompanyOrderType(OrderTypeInitCmd cmd) {
        orderTypeService.initCompanyOrderType(cmd.getCompanyId());
    }
}
