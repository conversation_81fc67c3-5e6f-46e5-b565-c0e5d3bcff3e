package com.niimbot.asset.means.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.niimbot.AbstractOrderDto;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.query.OrderToStringConverter;
import com.niimbot.asset.framework.query.QueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAssetOperationMapper;
import com.niimbot.asset.means.mapper.AsOrderMapper;
import com.niimbot.asset.means.model.*;
import com.niimbot.asset.means.service.*;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormPropsCO;
import com.niimbot.easydesign.form.dto.formprops.EditFieldRules;
import com.niimbot.easydesign.form.dto.sdk.FormValidatorCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import com.niimbot.page.SortQuery;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @since 2020-12-24
 */
@Service
@RequiredArgsConstructor
public class AsOrderServiceImpl extends ServiceImpl<AsOrderMapper, AsOrder> implements AsOrderService {

    private final AsOrderDetailService detailService;
    private final AsAssetLogService logService;
    private final AssetService assetService;
    private final AsAssetOperationMapper assetOperationMapper;
    @Resource
    private AsOrderTypeService orderTypeService;
    private final AsFormService formService;
    private final AssetUtil assetUtil;

    private final CacheResourceUtil cacheResourceUtil;
    private final RedisService redisService;

    private static final String SEPARATOR = "；";
    private static final String VALUE_FORMAT = "\"{0}\"";

    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;
    @Resource
    private MySqlOrderQueryConditionResolver conditionResolver;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(AsOrderDto dto, Boolean enableWorkflow) {

        int orderType = dto.getOrderType();
        OrderTypeEnum typeEnum = OrderTypeEnum.getByType(orderType);

        // 获取当前资产列表原始数据
        List<Long> assetIds = dto.getAssets().stream().map(AsOrderAssetDto::getId).collect(toList());
        List<AsAsset> list = assetService.list(Wrappers.<AsAsset>lambdaQuery().in(AsAsset::getId, assetIds));

        // 校验资产数据
        AssetOperationDto operationDto = assetOperationMapper.assetOptByOrderType(orderType);
        List<AssetStatusDto> assetStatusList = Lists.newArrayList();
        if (null != operationDto) {
            assetStatusList = operationDto.getAssetStatusList();
        }
        typeEnum.checkAsset(list, dto.getOrderData(), assetStatusList);

        AsOrder asOrder = BeanUtil.copyProperties(dto, AsOrder.class);
        if (ObjectUtil.isNull(dto.getAssetNum())) {
            asOrder.setAssetNum(dto.getAssets().size());
        }
        // 此处为了兼容盘点单的损益处理，需要提前传单号
        if (StrUtil.isEmpty(asOrder.getOrderNo())) {
            asOrder.setOrderNo(StringUtils.getOrderNo(OrderTypeEnum.getOrderNoPrefix(orderType)));
        }

        if (BooleanUtil.isTrue(enableWorkflow)) {
            asOrder.setApproveStatus(DictConstant.WAIT_APPROVE);
        }
        //只允许单个资产变更清空资产属性
        if (dto.getAssetModifySwitch() != null && dto.getAssetModifySwitch()) {
            asOrder.setOrderData(dto.getOrderData());
        }
        save(asOrder);
        dto.setOrderNo(asOrder.getOrderNo());

        List<AsOrderDetail> orderDetailList = Lists.newArrayList();
        for (AsOrderAssetDto asset : dto.getAssets()) {
            // 单据详情
            AsOrderDetail orderDetail = new AsOrderDetail().setOrderId(asOrder.getId());
            orderDetail.setAssetId(asset.getId());
            orderDetail.setAssetSnapshotData(asset.getAssetSnapshotData());
            orderDetailList.add(orderDetail);
        }
        if (BooleanUtil.isTrue(enableWorkflow)) {
            // 修改资产状态为审批中
            assetService.updateBatchStatus(assetIds, asset -> {
                asset.setBeforeStatus(asset.getStatus());
                asset.setStatus(AssetConstant.ASSET_STATUS_CHECK);
            });
            //单个资产变更审批时记录入口
            if (dto.getAssetModifySwitch() != null && dto.getAssetModifySwitch()) {
                //单个资产变审批时如果清除必填项需给出拦截提示
                approveNotice(asOrder, list, dto.getAssets(), null, dto.getAssetModifySwitch());
                String orderKey = "orderKey:" + dto.getAssets().get(0).getId();
                redisService.set(orderKey, dto.getAssetModifySwitch());
            }
        } else {
            assetService.updateBatchStatus(assetIds, asset -> {
                asset.setBeforeStatus(asset.getStatus());
            });
            list.forEach(asset -> {
                asset.setBeforeStatus(asset.getStatus());
            });
            dealAssetData(asOrder, list, dto.getAssets(), null, dto.getAssetModifySwitch());
        }
        detailService.saveBatch(orderDetailList);
        return asOrder.getId();
    }

    @Override
    public void dealAssetData(AsOrder asOrder, List<AsAsset> assetNowList, List<AsOrderAssetDto> assets, Long userId, Boolean assetModifySwitch) {
        OrderTypeEnum typeEnum = OrderTypeEnum.getByType(asOrder.getOrderType());
        AsOrderType asOrderType = orderTypeService.getById(asOrder.getOrderType());
        if (null == assets) {
            assets = detailService.getDetailsByOrderId(asOrder.getId());
        }
        if (null == assetNowList) {
            List<Long> assetIds = assets.stream().map(AsOrderAssetDto::getId).collect(toList());
            assetNowList = assetService.list(Wrappers.<AsAsset>lambdaQuery().in(AsAsset::getId, assetIds));
        }
        List<AsAssetLog> assetLogList = Lists.newArrayList();
        List<AsAsset> assetList = Lists.newArrayList();

        FormVO assetFormVO = null;

        if (OrderFormTypeEnum.CHANGE.getCode() == asOrder.getOrderType()) {
            assetFormVO = getAssetChangeForm(asOrder.getCompanyId(), assetModifySwitch);
        }

        // 当前仅处理WX和WW ActionContent 单独处理
//        String nonAssetFieldContent = dealNonAssetFieldActionContent(typeEnum, asOrder);
        for (AsOrderAssetDto asset : assets) {
            // 获取资产原始数据
            AsAsset byId = assetNowList.stream()
                    .filter(asAsset -> asAsset.getId().longValue() == asset.getId().longValue()).findFirst()
                    .orElseGet(AsAsset::new);

            // 待更新资产
            AsAsset renewAsset = new AsAsset().setId(asset.getId()).setBeforeStatus(byId.getBeforeStatus())
                    .setStandardId(byId.getStandardId())
                    .setStatus(byId.getStatus());

            // 资产日志
            AsAssetLog asAssetLog = new AsAssetLog().setAssetId(asset.getId())
                    .setCompanyId(asOrder.getCompanyId())
                    .setActionName(typeEnum.getName()).setActionType(asOrder.getOrderType())
                    .setOriginalData(asset.getAssetSnapshotData())
                    .setActionContent("")
                    .setOrderNo(asOrder.getOrderNo())
                    .setOrderId(asOrder.getId())
                    .setHandleTime(LocalDateTime.now());
            asAssetLog.setHandleTime(LocalDateTime.now());
            if (userId != null) {
                asAssetLog.setCreateBy(userId);
            }

            // 变更调用的单独的逻辑
            if (OrderFormTypeEnum.CHANGE.getCode() == asOrder.getOrderType()) {
                if (assetFormVO != null) {
                    changeOrderUpdateAssetData(assetFormVO.getFormFields(), asOrder.getOrderData(), byId.getAssetData(), renewAsset, asAssetLog, assetModifySwitch);
                }
            }
            // 其他单据调用原来的逻辑
            else {
                typeEnum.dealAssetData(asOrder.getOrderData(), byId.getAssetData(), renewAsset, asAssetLog, asOrderType, cacheResourceUtil);
            }

            assetLogList.add(asAssetLog);
            // 更新资产
            assetList.add(renewAsset);
        }
        assetService.updateBatchById(assetList);
        logService.saveBatch(assetLogList);
    }

    @Override
    public AsOrderDto getDetail(Long id) {
        AsOrder asOrder = getById(id);
        if (asOrder != null) {
            AsOrderDto asOrderDto = BeanUtil.copyProperties(asOrder, AsOrderDto.class);
            asOrderDto.setAssets(detailService.getDetailsByOrderId(id));
            return asOrderDto;
        } else {
            return null;
        }
    }

    @Override
    public List<AsOrderAssetDto> getAssetsByOrderId(Collection<Long> ids) {
        return detailService.getDetailsByOrderId(ids);
    }

    @Override
    public IPage<JSONObject> assetPage(AsOrderAssetQueryDto dto) {
        LambdaQueryWrapper<AsOrderDetail> lambdaQuery = Wrappers.<AsOrderDetail>lambdaQuery()
                .eq(AsOrderDetail::getOrderId, dto.getOrderId());
        Page<AsOrderDetail> page = detailService.page(dto.buildIPage(), lambdaQuery);
        List<JSONObject> collect =
                page.getRecords().stream().map(AsOrderDetail::getAssetSnapshotData).collect(toList());
        Page<JSONObject> result = BeanUtil.copyProperties(page, Page.class);
        result.setRecords(collect);
        return result;
    }

    @Override
    public IPage<AsOrder> page(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsOrder> page = buildOrderSort(tableAlias, query);
        return this.getBaseMapper().page(page, query, conditions);
    }

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @param assetId assetId
     * @return 结果
     */
    @Override
    public AsOrderInfoDto getApproveOrderByAssetId(Long assetId) {
        // 查询通用单据
        AsOrderInfoDto order = getBaseMapper().getOrderByAssetId(assetId);
        if (order != null) {
            order.setPerm(true);
            return order;
        }
        // 超管本身查询就是全部,所以如果非超管, 再多查询一次无权限的
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            order = getBaseMapper().getOrderByAssetIdNoPerm(assetId, LoginUserThreadLocal.getCompanyId());
            if (order != null) {
                order.setPerm(false);
                return order;
            }
        }
        return null;

        /*StringJoiner sql = new StringJoiner(" UNION ALL");
        List<Long> createUserIds =
                orderPermsConvertService.getPermsCreateUserIds(AssetConstant.DATA_PERMISSION_ASSET_ORDER);
        List<Long> approvalUserIds =
                orderPermsConvertService.getPermsApprovalUserIds(AssetConstant.DATA_PERMISSION_ASSET_ORDER);
        // 通用单据
        String orderSql = "(select a.id, a.order_type, a.order_no from as_order_detail as b " +
                "join as_order a on a.id = b.order_id " +
                "where a.approve_status = 1 and b.asset_id = " + assetId;

        if (hasPerm) {
            String orderCreatePermsSql = orderPermsConvertService.resolveCreateUser("a", createUserIds);
            String orderApprovalPermsSql = orderPermsConvertService.resolveApprovalUser("a",
                    ListUtil.of(OrderFormTypeEnum.RECEIVE.getCode(), OrderFormTypeEnum.RETURN.getCode(),
                            OrderFormTypeEnum.BORROW.getCode(), OrderFormTypeEnum.BACK.getCode(),
                            OrderFormTypeEnum.ALLOCATE.getCode(), OrderFormTypeEnum.DISPOSE.getCode(),
                            OrderFormTypeEnum.CHANGE.getCode()),
                    approvalUserIds);
            orderSql += (" and (" + orderCreatePermsSql + " or " + orderApprovalPermsSql + ")");
        }
        orderSql += " limit 1)";
        sql.add(orderSql);

        // 报修
        String reportOrderSql = "(select a.id, 6 as order_type, a.order_no from as_repair_report_order_detail as b " +
                "join as_repair_report_order a on a.id = b.repair_report_order_id " +
                "where a.approve_status = 1 and b.id = " + assetId;

        if (hasPerm) {
            String reportOrderCreatePermsSql = orderPermsConvertService.resolveCreateUser("a", createUserIds);
            String reportOrderApprovalPermsSql = orderPermsConvertService.resolveApprovalUser("a",
                    OrderFormTypeEnum.REPAIR_REPORT.getCode(),
                    approvalUserIds);
            reportOrderSql += (" and (" + reportOrderCreatePermsSql + " or " + reportOrderApprovalPermsSql + ")");
        }
        reportOrderSql += "  limit 1)";
        sql.add(reportOrderSql);
        // 维修 --> 资产处于【维修中】时，点击资产状态展示如下弹窗，点击关联单据号则可跳转至对应的维修单中；（注意有数据权限控制）
        String repairOrderSql = "(select a.id, 7 as order_type, a.order_no from as_repair_order_detail as b " +
                "join as_repair_order a on a.id = b.repair_order_id " +
                "JOIN as_asset ast on ast.company_id = a.company_id and ast.id = b.id " +
                "where (a.approve_status = 1 or ast.status = 5) and b.id = " + assetId;

        if (hasPerm) {
            String repairOrderCreatePermsSql = orderPermsConvertService.resolveCreateUser("a", createUserIds);
            String repairOrderApprovalPermsSql = orderPermsConvertService.resolveApprovalUser("a",
                    OrderFormTypeEnum.REPAIR.getCode(),
                    approvalUserIds);
            repairOrderSql += (" and (" + repairOrderCreatePermsSql + " or " + repairOrderApprovalPermsSql + ")");
        }
        repairOrderSql += " limit 1)";
        sql.add(repairOrderSql);
        return this.getBaseMapper().getOrderByAssetId(sql.toString());*/
    }

    @Override
    public List<AsOrderDto> listForExport(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsOrder> page = buildOrderSort(tableAlias, query);
        String orderByStr = OrderToStringConverter.orderByToString(page.getOrders());
        return this.getBaseMapper().listForExport(query, conditions, orderByStr);
    }

    @Override
    public FormVO getForm(Integer orderType, Boolean singRow, Long companyId) {
        FormVO orderFormVO = formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType(), companyId);
        if (OrderFormTypeEnum.CHANGE.getCode() == orderType) {
            FormVO assetChangeForm = getAssetChangeForm(companyId, singRow);
            orderFormVO.getFormFields().addAll(assetChangeForm.getFormFields());
        }
        return orderFormVO;
    }

    @Override
    public FormVO getChangeForm(Long companyId) {
        return formService.getTpl(OrderFormTypeEnum.CHANGE.getBizType(), companyId);
    }

    @Override
    public FormVO getAssetChangeForm(Long companyId, Boolean assetModifySwitch) {
        FormVO assetFormVO = formService.getTpl(AsFormService.BIZ_TYPE_ASSET, companyId);
        List<FormFieldCO> changeFields = new ArrayList<>();
        List<FormFieldCO> assetFormFields = assetFormVO.getFormFields();
        JSONObject formProps = assetFormVO.getFormProps();
        List<String> changeCodes = new ArrayList<>();
        if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
            changeCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
                    .toJavaList(EditFieldRules.class)
                    .stream()
                    .filter(editFieldRules -> editFieldRules.getValue() == 2)
                    .map(EditFieldRules::getFieldCode)
                    .collect(toList());
        }
        for (FormFieldCO formFieldCO : assetFormFields) {
            if (AssetConstant.ED_SPLIT_LINE.equals(formFieldCO.getFieldType())) {
                continue;
            }
            if (changeCodes.contains(formFieldCO.getFieldCode())) {
                //兼容单个变更单回显必填
                if (assetModifySwitch == null || !assetModifySwitch) {
                    formFieldCO.setRequiredProps(false);
                }
                changeFields.add(formFieldCO);
            }
        }
        assetFormVO.setFormFields(changeFields);
        return assetFormVO;
    }

    @Override
    public void verify(AbstractOrderDto orderDto) {
        JSONObject submitOrderData = orderDto.getOrderData();
        FormVO orderFormVO = formService.assetOrderTpl(
                OrderFormTypeEnum.getOrderFormTypeEnum(orderDto.getOrderType()).getBizType());
        FormVO extensionForm = null;

        if (OrderFormTypeEnum.CHANGE.getCode() == orderDto.getOrderType()) {
            extensionForm = getAssetChangeForm(LoginUserThreadLocal.getCompanyId(), false);
        }
        JSONObject extensionData = new JSONObject();
        if (extensionForm != null) {
            // 校验资产信息是否有变更值
            boolean noneChange = extensionForm.getFormFields().stream()
                    .map(FormFieldCO::getFieldCode)
                    .allMatch(code -> {
                        String value = submitOrderData.getString(code);
                        return StrUtil.isBlank(value) || "[]".equals(value);
                    });
            if ((Objects.isNull(orderDto.getAssetModifySwitch()) || !orderDto.getAssetModifySwitch()) && noneChange) {
                throw new BusinessException(SystemResultCode.ORDER_DATA_BG_NO_CHANGE);
            }
            extensionForm.getFormFields().forEach(f -> {
                extensionData.put(f.getFieldCode(), submitOrderData.get(f.getFieldCode()));
            });
            orderFormVO.getFormFields().addAll(extensionForm.getFormFields());
        }
        if (OrderFormTypeEnum.MAINTAIN.getCode() == orderDto.getOrderType()) {
            FormFieldCO fieldCO = new FormFieldCO();
            fieldCO.setFieldCode("maintainContent");
            fieldCO.setFieldProps(new JSONObject());
            orderFormVO.getFormFields().add(fieldCO);
        }
        // 清理属性
        FormFieldConvert.clearInvalidField(submitOrderData, orderFormVO.getFormFields());
        if (OrderFormTypeEnum.STORE.getCode() == orderDto.getOrderType()) {
            String rkType = submitOrderData.getString(AsStoreOrderService.RK_TYPE);
            if (AsStoreOrderService.RK_TYPE_STRAIGHT.equals(rkType)) {
                FormVO form = formService.getByFormId(orderFormVO.getFormId(), LoginUserThreadLocal.getCompanyId(), true);
                List<FormFieldCO> formFieldCOS = form.getFormFields().stream()
                        .filter(f -> !f.isHidden())
                        .filter(f -> !ListUtil.of(FormFieldCO.YZC_ASSOCIATION_TABLE, FormFieldCO.SPLIT_LINE).contains(f.getFieldType()))
                        .collect(Collectors.toList());
                formService.fieldValidator(submitOrderData, formFieldCOS);
            } else {
                FormValidatorCmd orderFormValidatorCmd = new FormValidatorCmd(submitOrderData, orderFormVO.getFormId(),
                        Convert.toStr(LoginUserThreadLocal.getCompanyId()));
                formService.validatorForm(orderFormValidatorCmd);
            }
        } else {
            FormValidatorCmd orderFormValidatorCmd = new FormValidatorCmd(submitOrderData, orderFormVO.getFormId(),
                    Convert.toStr(LoginUserThreadLocal.getCompanyId()));
            formService.validatorForm(orderFormValidatorCmd);

        }

        if (extensionForm != null) {
            List<FormFieldCO> collect = extensionForm.getFormFields().stream().filter(f -> {
                String str = submitOrderData.getString(f.getFieldCode());
                return StrUtil.isNotEmpty(str) && !"[]".equals(str);
            }).collect(toList());
            if (CollUtil.isNotEmpty(collect)) {
                formService.fieldValidator(extensionData, collect);
            }
        }
    }

    @Override
    public void translation(AbstractOrderDto orderDto) {
        FormVO formVO = getForm(orderDto.getOrderType(), false, LoginUserThreadLocal.getCompanyId());
        List<DefaultTranslateUtil.FieldTranslation> translations =
                formVO.getFormFields().stream()
                        .map(
                                f ->
                                        new DefaultTranslateUtil.FieldTranslation()
                                                .setFieldCode(f.getFieldCode())
                                                .setFieldType(f.getFieldType())
                                                .setTranslationCode(f.getTranslationCode()))
                        .collect(Collectors.toList());
        assetUtil.translateAssetJson(orderDto.getOrderData(), translations);
    }

    @Override
    public QueryConditionSortDto sortField(Integer orderType) {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.ASSET_ORDER_TYPE_HEAD.get(orderType));
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.getOrderFormTypeEnum(orderType).getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    private String buildApprovedRemark(JSONObject before, JSONObject after, List<FormFieldCO> formFields) {
        List<String> text = new ArrayList<>();
        Map<String, FormFieldCO> formFieldMap = formFields.stream()
                .collect(Collectors.toMap(FormFieldCO::getFieldCode, k -> k));
        after.forEach((k, v) -> {
            Object beforeObject = before.get(k);
            String beforeValue;
            String afterValue;
            FormFieldCO formField = formFieldMap.get(k);
            // 图片数字下拉多选特殊处理
            if (ListUtil.of(FormFieldCO.FILES, FormFieldCO.IMAGES, FormFieldCO.MULTI_SELECT_DROPDOWN)
                    .contains(formField.getFieldType())) {
                if (beforeObject instanceof List) {
                    beforeValue = Convert.toStr(before.getJSONArray(k), "[]");
                } else {
                    beforeValue = "[]";
                }
                if (v instanceof List) {
                    afterValue = Convert.toStr(after.getJSONArray(k), "[]");
                } else {
                    afterValue = "[]";
                }
            } else {
                beforeValue = Convert.toStr(before.getString(k), StrUtil.EMPTY);
                afterValue = Convert.toStr(after.getString(k), StrUtil.EMPTY);
            }
            if (!StrUtil.equals(afterValue, beforeValue)) {
                text.add(formField.getFieldName());
            }
        });
        if (CollUtil.isNotEmpty(text)) {
            return "并修改了" + String.join("，", text);
        } else {
            return StrUtil.EMPTY;
        }
    }

    public static String getFormatValue(String oriVal) {
        return MessageFormat.format(VALUE_FORMAT, oriVal);
    }

    private void changeOrderUpdateAssetData(List<FormFieldCO> changeFormFields, JSONObject orderData, JSONObject assetData,
                                            AsAsset toRenew, AsAssetLog assetLog, Boolean assetModifySwitch) {

        StringBuilder sb = new StringBuilder();
        for (FormFieldCO field : changeFormFields) {
            Object orderVal = orderData.get(field.getFieldCode());
            Object assetVal = assetData.get(field.getFieldCode());
            //pc端兼容单个资产变更，可以清空资产属性
            if (ObjectUtil.isEmpty(orderVal)) {
                if (assetModifySwitch == null || !assetModifySwitch) {
                    continue;
                }
            }
            if (!StrUtil.equals(Convert.toStr(orderVal, StrUtil.EMPTY), Convert.toStr(assetVal, StrUtil.EMPTY))) {
                //清空的字段是必填项时，弹框提醒
                if (field.getFieldProps().getBoolean("required") && ObjectUtil.isEmpty(orderVal)) {
                    throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "您清空的字段【" + field.getFieldProps().getString("originFieldName") + "】是必填项");
                }
                // 变更资产数据
                assetData.put(field.getFieldCode(), orderVal);

                // 资产履历
                String orderValStr = "";
                String assetValStr = "空";
                // 组织
                if (AssetConstant.ED_YZC_ORG.equals(field.getFieldType())) {
                    orderValStr = cacheResourceUtil.getOrgName(Convert.toLong(orderVal));
                    if (StrUtil.isNotBlank(Convert.toStr(assetVal))) {
                        assetValStr = cacheResourceUtil.getOrgName(Convert.toLong(assetVal));
                    }
                } else if (AssetConstant.ED_YZC_EMP.equals(field.getFieldType())) {
                    orderValStr = cacheResourceUtil.getUserName(Convert.toLong(orderVal));
                    if (StrUtil.isNotBlank(Convert.toStr(assetVal))) {
                        assetValStr = cacheResourceUtil.getUserName(Convert.toLong(assetVal));
                    }
                } else if (AssetConstant.ED_YZC_AREA.equals(field.getFieldType())) {
                    orderValStr = cacheResourceUtil.getAreaName(Convert.toLong(orderVal));
                    if (StrUtil.isNotBlank(Convert.toStr(assetVal))) {
                        assetValStr = cacheResourceUtil.getAreaName(Convert.toLong(assetVal));
                    }
                } else if (AssetConstant.ED_YZC_ASSET_CATE.equals(field.getFieldType())) {
                    orderValStr = cacheResourceUtil.getCategoryName(Convert.toLong(orderVal));
                    if (StrUtil.isNotBlank(Convert.toStr(assetVal))) {
                        assetValStr = cacheResourceUtil.getCategoryName(Convert.toLong(assetVal));
                    }
                } else if (AssetConstant.ED_DATETIME.equals(field.getFieldType())) {
                    Long orderTmpVal = Convert.toLong(orderVal);
                    Long assetTmpVal = null;
                    if (StrUtil.isNotBlank(Convert.toStr(assetVal))) {
                        assetTmpVal = Convert.toLong(assetVal);
                    }
                    if (ObjectUtil.isNotNull(orderTmpVal)) {
                        LocalDateTime orderDateTime = LocalDateTime.ofEpochSecond(orderTmpVal / 1000, 0, ZoneOffset.of("+8"));
                        orderValStr = orderDateTime.format(DateTimeFormatter.ofPattern(field.getFieldProps().getString("dateFormatType")));
                    }

                    if (ObjectUtil.isNotNull(assetTmpVal)) {
                        LocalDateTime assetDateTime = LocalDateTime.ofEpochSecond(assetTmpVal / 1000, 0, ZoneOffset.of("+8"));
                        assetValStr = assetDateTime.format(DateTimeFormatter.ofPattern(field.getFieldProps().getString("dateFormatType")));
                    }
                } else {
                    orderValStr = Convert.toStr(orderVal);
                    assetValStr = StrUtil.emptyToDefault(Convert.toStr(assetVal), assetValStr);
                }
                if (AssetConstant.ED_IMAGES.equals(field.getFieldType())
                        || AssetConstant.ED_FILES.equals(field.getFieldType())) {
                    sb.append(field.getFieldName()).append(getFormatValue("已修改")).append(SEPARATOR);
                } else {
                    sb.append(field.getFieldName()).append("由").append(getFormatValue(assetValStr))
                            .append("变成").append(getFormatValue(orderValStr)).append(SEPARATOR);
                }

            }
        }
        toRenew.setAssetData(assetData);
        // 变更前是闲置
        if (Objects.equals(toRenew.getBeforeStatus(), AssetConstant.ASSET_STATUS_HANDLE)) {
            toRenew.setStatus(AssetConstant.ASSET_STATUS_HANDLE);
        }
        // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
        else if (StrUtil.isNotEmpty(assetData.getString(USE_ORG)) ||
                StrUtil.isNotEmpty(assetData.getString(USE_PERSON))) {
            if (AssetConstant.ASSET_STATUS_IDLE.equals(toRenew.getStatus()) ||
                    (AssetConstant.ASSET_STATUS_CHECK.equals(toRenew.getStatus()))) {
                toRenew.setStatus(AssetConstant.ASSET_STATUS_USING);
            } else {
                toRenew.setStatus(toRenew.getBeforeStatus());
            }
        } else {
            if (AssetConstant.ASSET_STATUS_USING.equals(toRenew.getStatus()) ||
                    AssetConstant.ASSET_STATUS_BORROW.equals(toRenew.getStatus()) ||
                    AssetConstant.ASSET_STATUS_CHECK.equals(toRenew.getStatus())) {
                toRenew.setStatus(AssetConstant.ASSET_STATUS_IDLE);
            } else {
                toRenew.setStatus(toRenew.getBeforeStatus());
            }
        }

        assetLog.setChangeData(assetData).setActionContent(StrUtil.removeSuffix(sb.toString(), SEPARATOR));

        // 重置状态
        toRenew.setBeforeStatus(0);
    }

    /**
     * 通用排序处理
     *
     * @param tableAlias
     * @param queryDto
     * @return
     */
    private Page<AsOrder> buildOrderSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_order";
        }
        Page<AsOrder> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = sortField(((AsOrderQueryDto) queryDto).getOrderType());
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                order.setColumn(StrUtil.format(QueryConditionResolver.COLUMN_SQL_TPL, ImmutableMap.of("tName", tableAlias, "tColumn", StrUtil.toUnderlineCase(column))));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlOrderQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlOrderQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                        order.setColumn(MySqlOrderQueryConditionResolver.getJsonSqlColumn(tableAlias, column));
                    } else {
                        order.setColumn(MySqlOrderQueryConditionResolver.getJsonSqlColumn(tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    /**
     * 单个单据走审批时，给出必填提醒
     */
    public void approveNotice(AsOrder asOrder, List<AsAsset> assetNowList, List<AsOrderAssetDto> assets, Long userId, Boolean assetModifySwitch) {
        OrderTypeEnum typeEnum = OrderTypeEnum.getByType(asOrder.getOrderType());
        if (null == assets) {
            assets = detailService.getDetailsByOrderId(asOrder.getId());
        }
        if (null == assetNowList) {
            List<Long> assetIds = assets.stream().map(AsOrderAssetDto::getId).collect(toList());
            assetNowList = assetService.list(Wrappers.<AsAsset>lambdaQuery().in(AsAsset::getId, assetIds));
        }
        FormVO assetFormVO = null;

        if (OrderFormTypeEnum.CHANGE.getCode() == asOrder.getOrderType()) {
            assetFormVO = getAssetChangeForm(asOrder.getCompanyId(), assetModifySwitch);
        }

        for (AsOrderAssetDto asset : assets) {
            // 获取资产原始数据
            AsAsset byId = assetNowList.stream()
                    .filter(asAsset -> asAsset.getId().longValue() == asset.getId().longValue()).findFirst()
                    .orElseGet(AsAsset::new);

            // 待更新资产
            AsAsset renewAsset = new AsAsset().setId(asset.getId()).setBeforeStatus(byId.getBeforeStatus())
                    .setStandardId(byId.getStandardId())
                    .setStatus(byId.getStatus());

            // 资产日志
            AsAssetLog asAssetLog = new AsAssetLog().setAssetId(asset.getId())
                    .setCompanyId(asOrder.getCompanyId())
                    .setActionName(typeEnum.getName()).setActionType(asOrder.getOrderType())
                    .setOriginalData(asset.getAssetSnapshotData())
                    .setActionContent("")
                    .setOrderNo(asOrder.getOrderNo())
                    .setOrderId(asOrder.getId())
                    .setHandleTime(LocalDateTime.now());
            asAssetLog.setHandleTime(LocalDateTime.now());
            if (userId != null) {
                asAssetLog.setCreateBy(userId);
            }
            // 变更调用的单独的逻辑
            if (OrderFormTypeEnum.CHANGE.getCode() == asOrder.getOrderType()) {
                if (assetFormVO != null) {
                    changeOrderUpdateAssetData(assetFormVO.getFormFields(), asOrder.getOrderData(), byId.getAssetData(), renewAsset, asAssetLog, assetModifySwitch);
                }
            }
        }
    }
}
