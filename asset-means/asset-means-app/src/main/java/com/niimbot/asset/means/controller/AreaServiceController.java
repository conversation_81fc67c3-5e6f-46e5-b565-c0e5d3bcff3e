package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.SerialNumberUtils;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaImportDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.system.AreaAssetDto;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.PrintAreaPageQueryDto;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 20201026
 * @description 资产地域
 */
@RestController
@RequestMapping("server/mean/area")
@RequiredArgsConstructor
public class AreaServiceController {
    private final AreaService areaService;
    private final AsAssetImportErrorService assetImportErrorService;

    /**
     * 添加资产区域
     *
     * @param area
     * @return true/false
     */
    @PostMapping
    public Long areaAdd(@RequestBody AsArea area) {
        return areaService.add(area);
    }

    /**
     * 修改资产区域
     *
     * @param area
     * @return true/false
     */
    @PutMapping
    public boolean edit(@RequestBody AsArea area) {
        return areaService.edit(area);
    }

    /**
     * 查询获取资产区域
     *
     * @param id
     * @return AsArea信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public AsArea areaGetInfo(@PathVariable(value = "id") Long id) {
        return areaService.getById(id);
    }

    /**
     * 删除资产区域
     *
     * @param ids
     * @return
     */
    @DeleteMapping
    public List<AuditableOperateResult> remove(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        // List<AsArea> areas = areaService.listByIds(ids);
        List<AsArea> areas = areaService.list(
                Wrappers.lambdaQuery(AsArea.class)
                        .select(AsArea::getId, AsArea::getAreaCode, AsArea::getAreaName)
                        .in(AsArea::getId, ids)
        );
        areaService.remove(ids);
        return areas.stream().map(v -> new AuditableOperateResult(v.getAreaCode(), v.getAreaName())).collect(Collectors.toList());
    }

    /**
     * 分页查询区域列表
     *
     * @param queryDto 查询参数
     * @return pageUtils对象
     */
    @GetMapping(value = "/page")
    public IPage<AreaDto> page(AreaQueryDto queryDto) {
        return areaService.pageArea(queryDto);
    }

    @GetMapping("/root/node/list/{companyId}")
    public List<AreaDto> rootNodeList(@PathVariable Long companyId) {
        Set<Long> set = areaService.listArea(new AreaQueryDto().setFilterPerm(true)).stream().map(AreaDto::getId).collect(Collectors.toSet());
        List<AsArea> areas = areaService.list(
                Wrappers.lambdaQuery(AsArea.class)
                        .eq(AsArea::getOrgId, companyId)
                        .eq(AsArea::getPid, 0L)
                        .in(AsArea::getId, set)
                        .orderByAsc(AsArea::getCreateTime)
        );
        if (CollUtil.isEmpty(areas)) {
            return Collections.emptyList();
        }
        return areas.stream().map(asArea -> BeanUtil.copyProperties(asArea, AreaDto.class)).collect(Collectors.toList());
    }

    @GetMapping("/printAreaPage")
    public PageUtils<AreaAssetDto> printAreaPage(PrintAreaPageQueryDto dto) {
        Page<AreaAssetDto> page = areaService.printAreaPage(dto);
        return new PageUtils<>(page);
    }

    @GetMapping("/printAreaIds")
    public List<Long> printAreaIds(PrintAreaPageQueryDto dto) {
        dto.setPageSize(500);
        Page<AreaAssetDto> page = areaService.printAreaPage(dto);
        if (Objects.isNull(page) || CollUtil.isEmpty(page.getRecords())) {
            return Collections.emptyList();
        }
        return page.getRecords().stream().map(AreaAssetDto::getId).collect(Collectors.toList());
    }

    /**
     * 查询区域列表list集合 * @param area 参数实体
     *
     * @return 地区列表
     */
    @GetMapping(value = "/list")
    public List<AreaDto> list(AreaQueryDto queryDto) {
        return areaService.listArea(queryDto);
    }

    /**
     * 获取推荐区域编码
     *
     * @return 编码
     */
    @GetMapping("/recommendCode")
    public String recommendCode() {
        return SerialNumberUtils.getMaxCode(areaService);
    }

    /**
     * 根据区域id集合查询区域
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/listByIds")
    public List<AsArea> listByIds(@RequestBody List<Long> ids) {
        return areaService.list(Wrappers.<AsArea>lambdaQuery()
                .in(AsArea::getId, ids));
    }

    /**
     * 排序
     *
     * @param areaIds 查询参数
     * @return 区域
     */
    @PutMapping("/sort")
    public Boolean sort(@RequestBody List<Long> areaIds) {
        return areaService.sort(areaIds);
    }

    /**
     * 错误记录
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return areaService.importError(taskId);
    }

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        areaService.saveSheetHead(importErrorDto);
    }

    /**
     * 导入耗材数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody AreaImportDto importDto) {
        return areaService.saveSheetData(importDto);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return assetImportErrorService.deleteByTaskId(taskId);
    }

    /**
     * 内部服务，通过code或者name查询
     *
     * @return
     */
    @GetMapping("getOne")
    public Long getOne(@RequestParam(value = "name", required = false) String name,
                       @RequestParam(value = "code", required = false) String code) {
        return areaService.getOne(name, code);
    }

}
