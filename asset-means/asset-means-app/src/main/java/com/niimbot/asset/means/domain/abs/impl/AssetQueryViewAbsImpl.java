package com.niimbot.asset.means.domain.abs.impl;

import com.niimbot.asset.means.service.AsAssetQueryViewService;
import com.niimbot.asset.system.abs.AssetQueryViewAbs;
import com.niimbot.asset.system.dto.AssetQueryViewInitCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/16 2:42 下午
 */
@RestController
@RequestMapping("/client/abs/means/AssetQueryViewAbs/")
@RequiredArgsConstructor
public class AssetQueryViewAbsImpl implements AssetQueryViewAbs {

    private final AsAssetQueryViewService assetQueryViewService;

    @Override
    public void initUserView(AssetQueryViewInitCmd cmd) {
        assetQueryViewService.initUserView(cmd.getCompanyId(), cmd.getEmployeeId());
    }
}
