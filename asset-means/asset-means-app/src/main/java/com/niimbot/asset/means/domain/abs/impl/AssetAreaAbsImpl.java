package com.niimbot.asset.means.domain.abs.impl;

import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.means.mapstruct.MeansSystemMapStruct;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.system.abs.AssetAreaAbs;
import com.niimbot.asset.system.dto.AssetAreaLoadCacheCmd;
import com.niimbot.asset.system.dto.AssetAreaSaveCmd;
import com.niimbot.asset.system.model.AsDataAuthority;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/16 1:55 下午
 */
@RestController
@RequestMapping("/client/abs/means/AssetAreaAbs/")
@RequiredArgsConstructor
public class AssetAreaAbsImpl implements AssetAreaAbs {

    private final MeansSystemMapStruct systemMapStruct;

    @Autowired
    private AreaService areaService;

    @Override
    public void saveAssetArea(AssetAreaSaveCmd cmd) {
        areaService.save(systemMapStruct.convertAsAreaCoToModel(cmd.getAssetArea()));
    }

    @Override
    public void loadCacheAssetArea(AssetAreaLoadCacheCmd cmd) {
        areaService.loadAreaCache(cmd.getAreas()
                .stream()
                .map(systemMapStruct::convertAsAreaCoToModel)
                .collect(Collectors.toList()));
    }

    @Override
    public List<DictDataDto> listSimpleWithPerms(AsDataAuthority authority, Long companyId) {
        return areaService.listSimpleWithPerms(authority, companyId);
    }

    @Override
    public List<Long> hasPermAreaIds(List<Long> areaIds) {
        return areaService.hasPermAreaIds(areaIds);
    }

}
