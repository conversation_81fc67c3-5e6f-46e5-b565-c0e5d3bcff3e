package com.niimbot.asset.means.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 资产分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-26
 */
@EnableDataPerm(excludeMethodName = {"listByCompanyId", "getMaxCategoryCode", "allCategory"})
public interface AsCategoryMapper extends BaseMapper<AsCategory> {

    /**
     * 字典初始化，查询全部分类
     *
     * @return 分类集合
     */
    @Select("select id, company_id, industry_id, category_name from as_category")
    List<AsCategory> allCategory();

    List<AsCategory> listByCompanyId(@Param("companyId") Long companyId, @Param("categories") List<Long> categories);

    /**
     * 查询最大分类编码
     *
     * @param companyId 公司Id
     * @return 最大分类编码
     */
    @Select("select max(category_code) from as_category WHERE category_code REGEXP '^[A-Z]{1}[0-9]{2,3}$' AND company_id = #{companyId}")
    String getMaxCategoryCode(@Param("companyId") Long companyId);

    /**
     * 通过code查询最大区域编码
     *
     * @return 最大区域编码
     */
    @Select("select max(category_code) from as_category WHERE category_code REGEXP '^[A-Z]{1}[0-9]{2,3}$' AND company_id = #{companyId} and category_code like concat(#{code}, '%')")
    String getMaxCategoryCodeByCode(@Param("companyId") Long companyId,@Param("code") String code);

    Integer categoryRefAsset(@Param("categoryId") Long categoryId);

    List<CategoryDto> listCate(@Param("ew") CategoryQueryDto queryDto,
                               @Param("cateSql") String cateSql);

    List<Long> hasPermCateIds(@Param("cateIds") List<Long> cateIds,
                              @Param("cateSql") String cateSql);
}
