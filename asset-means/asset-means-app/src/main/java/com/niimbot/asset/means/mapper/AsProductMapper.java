package com.niimbot.asset.means.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.means.model.AsProduct;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.AsProductInfoDto;
import com.niimbot.means.AsProductQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-12
 */
@EnableDataPerm(excludeMethodName = {"customPage", "customList", "customInfo", "customInfos", "listNoDataPerm", "getShowItems", "getFormItems"})
public interface AsProductMapper extends BaseMapper<AsProduct> {

    AsProductInfoDto customInfo(@Param("id") Long id, @Param("companyId") Long companyId);

    List<AsProductInfoDto> customInfos(@Param("ids") List<Long> ids, @Param("companyId") Long companyId);

    IPage<AsProductInfoDto> customPage(@Param("page") Page<Object> buildIPage,
                                       @Param("ew") AsProductQueryDto queryDto,
                                       @Param("standardIds") List<Long> standardIds,
                                       @Param("companyIds") List<Long> companyIds,
                                       @Param("empId") Long empId);

    List<AsProductInfoDto> customList(@Param("name") String name, @Param("companyIds") List<Long> companyIds);

    /**
     * 无数据权限列表查询
     *
     * @param wrapper
     * @return
     */
    List<AsProduct> listNoDataPerm(@Param(Constants.WRAPPER) Wrapper<AsProduct> wrapper);

}
