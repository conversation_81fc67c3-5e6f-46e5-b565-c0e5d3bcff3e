package com.niimbot.asset.means.controller;


import com.niimbot.asset.means.model.AsAssetQueryView;
import com.niimbot.asset.means.service.AsAssetQueryViewService;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 资产查询视图 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@RestController
@RequestMapping("server/means/queryView")
@RequiredArgsConstructor
public class AsAssetQueryViewServiceController {
    private final AsAssetQueryViewService assetQueryViewService;

    @PostMapping
    public Boolean save(@RequestBody AsAssetQueryView queryView) {
        return assetQueryViewService.saveWithCache(queryView);
    }

    @PutMapping
    public Boolean update(@RequestBody AsAssetQueryView queryView) {
        return assetQueryViewService.updateWithCache(queryView);
    }

    @PutMapping("/{id}/{isShow}")
    public Boolean isShow(@PathVariable("id") Long id, @PathVariable("isShow") Boolean isShow) {
        return assetQueryViewService.isShowWithCache(id, isShow);
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Long id) {
        return assetQueryViewService.deleteWithCache(id);
    }

    @GetMapping("/{id}")
    public AsAssetQueryView getById(@PathVariable("id") Long id) {
        return assetQueryViewService.getByIdWithCache(id);
    }

    @PutMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public Boolean sort(@RequestBody List<Long> ids) {
        return assetQueryViewService.sortWithCache(ids);
    }

    @GetMapping(value = "/list/config")
    public List<AsAssetQueryView> configList() {
        return assetQueryViewService.queryConfigViews();
    }

    @GetMapping(value = "/list/tab")
    public List<AsAssetQueryView> tabList() {
        return assetQueryViewService.queryTabViews();
    }
}
