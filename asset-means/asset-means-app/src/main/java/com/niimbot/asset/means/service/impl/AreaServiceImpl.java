package com.niimbot.asset.means.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.model.UserDataOrgPermBizDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.DeduplicationUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.TreeUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheAreaStrategy;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAreaMapper;
import com.niimbot.asset.means.mapper.AsAssetMapper;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.system.abs.EquipmentSiteInspectPointAbs;
import com.niimbot.asset.system.model.AsDataAuthority;
import com.niimbot.asset.system.service.DataAuthorityService;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaImportDto;
import com.niimbot.means.AreaMeansStatusGroup;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.system.AreaAssetDto;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.PrintAreaPageQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AreaServiceImpl extends ServiceImpl<AsAreaMapper, AsArea> implements AreaService, ApplicationListener<ApplicationReadyEvent> {

    private final RedisService redisService;

    private final ThreadPoolTaskExecutor taskExecutor;

    private final AsAssetImportErrorService assetImportErrorService;

    private final DataAuthorityService dataAuthorityService;

    @Resource
    private EquipmentSiteInspectPointAbs equipmentSiteInspectPointAbs;

    @Resource
    private DataScopeStrategyManager dataScopeStrategyManager;

    @Resource
    private AsAssetMapper asAssetMapper;

    @Autowired
    @Lazy
    public AreaServiceImpl(RedisService redisService,
                           ThreadPoolTaskExecutor taskExecutor,
                           AsAssetImportErrorService assetImportErrorService,
                           DataAuthorityService dataAuthorityService) {
        this.redisService = redisService;
        this.taskExecutor = taskExecutor;
        this.assetImportErrorService = assetImportErrorService;
        this.dataAuthorityService = dataAuthorityService;
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return;
        }

        taskExecutor.execute(() -> {
            List<AsArea> areaList = this.getBaseMapper().allArea();
            this.loadAreaCache(areaList);
        });
    }

    /**
     * 写入区域缓存
     *
     * @param areaList 区域列表
     */
    @Override
    public void loadAreaCache(List<AsArea> areaList) {
        Map<String, String> collect = new ConcurrentHashMap<>();
        areaList.parallelStream().forEach(it -> {
            collect.put(Convert.toStr(it.getId()), it.getAreaName());
        });
        redisService.hSetAll(RedisConstant.materialCategoryDictKey(), collect);
        log.info("init area cache finish");
    }

    /**
     * 获取区域最大编码
     *
     * @return 结果
     */
    @Override
    public String getMaxCode() {
        return this.getBaseMapper().getMaxAreaCode();
    }

    /**
     * 通过code获取区域最大编码
     *
     * @return 结果
     */
    @Override
    public String getMaxCode(String code) {
        return this.getBaseMapper().getMaxAreaCodeByCode(code);
    }

    /**
     * 查询区域信息
     *
     * @param queryDto 查询条件
     * @return 资产列表
     */
    @Override
    public List<AreaDto> listArea(AreaQueryDto queryDto) {
        // 是否过滤权限(默认带权限)
        String areaSql = null;
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
            areaSql = dataScopeStrategyManager.simplePermsSql(DataPermType.AREA);
        }
        List<AreaDto> areaList = this.getBaseMapper().listArea(queryDto, areaSql);
        // 是否构造树，只有开启过滤权限，才需要补齐无权限数据
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm()) && BooleanUtil.isTrue(queryDto.getBuildTree())) {
            fillTreeNoPermNode(areaList);
        }
        return areaList;
    }

    @Override
    public List<Long> hasPermAreaIds(List<Long> areaIds) {
        String areaSql = dataScopeStrategyManager.simplePermsSql(DataPermType.AREA);
        return getBaseMapper().hasPermAreaIds(areaIds, areaSql);
    }

    private void fillTreeNoPermNode(List<AreaDto> areaList) {
        // 当前组织Id
        Set<Long> areaIds = areaList.stream().map(AreaDto::getId).collect(Collectors.toSet());
        Set<Long> pidSet = new HashSet<>();
        // 如果父节点Id不在当前组织集合中，则需要查询补充
        for (AreaDto areaDto : areaList) {
            String paths = areaDto.getPaths();
            List<Long> pIds = Arrays.stream(paths.split(",")).map(Long::parseLong).collect(Collectors.toList());
            for (Long id : pIds) {
                if (id > 0 && !areaIds.contains(id)) {
                    pidSet.add(id);
                }
            }
        }
        if (!pidSet.isEmpty()) {
            AreaQueryDto queryDto = new AreaQueryDto().setIncludeIds(new ArrayList<>(pidSet));
            List<AreaDto> areaDtos = this.baseMapper.listArea(queryDto, null);
            areaDtos.forEach(f -> f.setDisabled(true));
            areaList.addAll(areaDtos);
        }
    }

    /**
     * 查询区域信息
     *
     * @param queryDto 查询条件
     * @return 区域列表
     */
    @Override
    public IPage<AreaDto> pageArea(AreaQueryDto queryDto) {
        // 是否过滤权限(默认带权限)
        String areaSql = null;
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
            areaSql = dataScopeStrategyManager.simplePermsSql(DataPermType.AREA);
        }
        return this.getBaseMapper().listArea(queryDto.buildIPage(), queryDto, areaSql);
    }

    @Override
    public Page<AreaAssetDto> printAreaPage(PrintAreaPageQueryDto dto) {
        String areaSql = dataScopeStrategyManager.simplePermsSql(DataPermType.AREA);
        // 获取满足条件的区域
        Page<AreaAssetDto> page = this.getBaseMapper().printAreaPage(dto.buildIPage(), dto, areaSql, LoginUserThreadLocal.getCompanyId());
        // 统计资产数
        ConcurrentMap<Long, ConcurrentMap<Integer, Long>> group = asAssetMapper.selectCountByAreaGroupByStatus(LoginUserThreadLocal.getCompanyId(), page.getRecords().stream().map(v -> String.valueOf(v.getId())).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingByConcurrent(AreaMeansStatusGroup::getAreaId, Collectors.toConcurrentMap(AreaMeansStatusGroup::getMeansStatus, AreaMeansStatusGroup::getMeansCount)));
        page.getRecords().forEach(v -> {
            if (!group.containsKey(v.getId())) {
                return;
            }
            ConcurrentMap<Integer, Long> countMap = group.get(v.getId());

            v.setAssetCount(countMap.values().stream().reduce(0L, Long::sum))
                    .setIdleAssetCount(countMap.getOrDefault(AssetConstant.ASSET_STATUS_IDLE, 0L))
                    .setCheckAssetCount(countMap.getOrDefault(AssetConstant.ASSET_STATUS_CHECK, 0L))
                    .setUseAssetCount(countMap.getOrDefault(AssetConstant.ASSET_STATUS_USING, 0L))
                    .setBorrowAssetCount(countMap.getOrDefault(AssetConstant.ASSET_STATUS_BORROW, 0L))
                    .setWaitServiceAssetCount(countMap.getOrDefault(AssetConstant.ASSET_STATUS_WAIT_SERVICE, 0L))
                    .setServiceAssetCount(countMap.getOrDefault(AssetConstant.ASSET_STATUS_SERVICE, 0L));
        });

        // 计算子集
        if (dto.getRange() == 2) {
            List<AreaAssetDto> raw = new ArrayList<>(page.getRecords());
            page.getRecords().forEach(v -> {
                Long id = v.getId();
                for (AreaAssetDto data : raw) {
                    if (data.getPaths().contains(String.valueOf(id))) {
                        v.setAssetCount(v.getAssetCount() + data.getAssetCount());
                        v.setIdleAssetCount(v.getIdleAssetCount() + data.getIdleAssetCount());
                        v.setCheckAssetCount(v.getCheckAssetCount() + data.getCheckAssetCount());
                        v.setUseAssetCount(v.getUseAssetCount() + data.getUseAssetCount());
                        v.setBorrowAssetCount(v.getBorrowAssetCount() + data.getBorrowAssetCount());
                        v.setWaitServiceAssetCount(v.getWaitServiceAssetCount() + data.getWaitServiceAssetCount());
                        v.setServiceAssetCount(v.getServiceAssetCount() + data.getServiceAssetCount());
                    }
                }
            });
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sort(List<Long> areaIds) {
        int count = this.list(new QueryWrapper<AsArea>().lambda()
                .select(AsArea::getPid)
                .in(AsArea::getId, areaIds)
                .groupBy(AsArea::getPid)).size();
        if (count > 1) {
            throw new BusinessException(SystemResultCode.AREA_SORT_ERROR);
        }
        AtomicInteger idx = new AtomicInteger(0);
        List<AsArea> collect = areaIds.stream().map(it -> {
            AsArea area = new AsArea();
            area.setId(it);
            area.setSortNum(idx.getAndIncrement());
            return area;
        }).collect(Collectors.toList());
        this.updateBatchById(collect);
        return true;
    }

    /**
     * 删除区域
     *
     * @param ids 区域Id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<Long> ids) {
        // 目前原型设计只能逐条删除，因此先循环判断
        for (Long areaId : ids) {
            //校验区域下是否有巡检点位
            Integer processPoint = equipmentSiteInspectPointAbs.getPointCount(areaId);
            if (processPoint > 0) {
                throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-区域下存在巡检点位");
            }
            // 当前区域及子区域
            Set<Long> part = this.list(
                    Wrappers.lambdaQuery(AsArea.class)
                            .select(AsArea::getId)
                            .eq(AsArea::getId, areaId)
                            .or()
                            .like(AsArea::getPaths, ("," + areaId + ","))
            ).stream().map(AsArea::getId).collect(Collectors.toSet());
            if (CollUtil.isEmpty(part)) {
                continue;
            }
            AsAsset one = asAssetMapper.selectOne(
                    Wrappers.lambdaQuery(AsAsset.class)
                            .select(AsAsset::getId)
                            .in(AsAsset::getStorageArea, part.stream().map(String::valueOf).collect(Collectors.toSet()))
                            .last("LIMIT 1")
            );
            // Integer count = this.getBaseMapper().areaRefAsset(areaId);
            // if (count > 0) {
            //     throw new BusinessException(SystemResultCode.AREA_ASSET_EXISTS);
            // }
            if (Objects.nonNull(one)) {
                throw new BusinessException(SystemResultCode.AREA_ASSET_EXISTS);
            }
            // 删除行业和子区域
            // this.remove(new QueryWrapper<AsArea>().lambda().eq(AsArea::getId, areaId)
            //         .or().like(AsArea::getPaths, ("," + areaId + ",")));
            this.removeBatchByIds(part);
        }
        return true;
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<AsAssetImportError> list = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, taskId)
                .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_AREA));
        return list.stream().map(AsAssetImportError::getExcelJson)
                .collect(Collectors.toList());
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importErrorDto.getTaskId());
        importError.setErrorNum(0);
        importError.setImportType(DictConstant.IMPORT_TYPE_AREA);
        importError.setExcelJson(importErrorDto.getHeadModelList());
        this.assetImportErrorService.save(importError);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSheetData(AreaImportDto importDto) {
        // 转换实体
        AsArea area = BeanUtil.copyProperties(importDto, AsArea.class);
        area.setPid(importDto.getPid());
        area.setAreaDesc(importDto.getAreaDesc());
        // 单个企业区域编码不能重复
        if (this.count(new QueryWrapper<AsArea>().lambda()
                .eq(AsArea::getAreaCode, area.getAreaCode())) > 0) {
            LuckySheetModel codee = importDto.getSheetModelList().get(1);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("区域编码已存在");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }
        if (StrUtil.isNotBlank(importDto.getPidCode())) {
            // 通过pidCode查询id
            AsArea parentArea = this.getOne(
                    new QueryWrapper<AsArea>().lambda()
                            .eq(AsArea::getOrgId, importDto.getOrgId())
                            .eq(AsArea::getAreaCode, importDto.getPidCode()), false);
            if (ObjectUtil.isNull(parentArea)) {
                LuckySheetModel pidCode = importDto.getSheetModelList().get(4);
                if (ObjectUtil.isNull(pidCode.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("上级区域编码不存在");
                    pidCode.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
            } else {
                // level 加一
                int level = parentArea.getLevel() + 1;
                if (level >= 7) {
                    LuckySheetModel pidCode = importDto.getSheetModelList().get(4);
                    if (ObjectUtil.isNull(pidCode.getV().getPs())) {
                        LuckySheetModel.Comment comment = new LuckySheetModel.Comment("层级不得超过7层");
                        pidCode.getV().setPs(comment);
                        importDto.setErrorNum(importDto.getErrorNum() + 1);
                    }
                } else {
                    area.setPid(parentArea.getId());
                    area.setLevel(level);
                    // 添加paths路径
                    area.setPaths(parentArea.getPaths() + parentArea.getId() + ",");
                }
            }
        } else {
            area.setPid(0L);
            area.setLevel(0);
            area.setPaths("0,");
        }

        // 查询改父节点下是否code重复
        if (this.count(new QueryWrapper<AsArea>().lambda()
                .eq(AsArea::getPid, area.getPid())
                .eq(AsArea::getOrgId, area.getOrgId())
                .eq(AsArea::getAreaName, area.getAreaName())) > 0) {
            LuckySheetModel areaName = importDto.getSheetModelList().get(2);
            if (ObjectUtil.isNull(areaName.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("区域名称已存在");
                areaName.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }
        if (importDto.getErrorNum() == 0) {
            Long id = IdUtils.getId();
            area.setId(id);
            this.save(area);
            redisService.hIncr(RedisConstant.companyImportKey("area", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("area", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            return true;
        } else {
            AsAssetImportError importError = copyToAsAreaImportError(importDto);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("area", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }

    @Override
    public List<AsArea> listByAreaNamePermission(AreaQueryDto queryDto) {
        return list(new QueryWrapper<AsArea>()
                .lambda()
                .and(StrUtil.isNotBlank(queryDto.getKw()), wrapper ->
                        wrapper.like(AsArea::getAreaName, queryDto.getKw())));
    }

    @Override
    public List<AreaDto> listByOrg(List<Long> orgIds) {
        return this.getBaseMapper().listByOrg(orgIds);
    }

    @Override
    public Long add(AsArea area) {
        // 设置level paths
        AsArea parent = getOne(new QueryWrapper<AsArea>().lambda().eq(AsArea::getId, area.getPid()));
        if (ObjectUtil.isNotNull(parent)) {
            int level = parent.getLevel() + 1;
            if (level >= 7) {
                throw new BusinessException(SystemResultCode.LEVEL_OVER_MAX, "7");
            }
            area.setLevel(level);
            // 添加paths路径
            area.setPaths(parent.getPaths() + parent.getId() + ",");
        } else {
            area.setPid(0L);
            area.setLevel(0);
            area.setPaths("0,");
        }
        checkRepeat(area, false);
        Long id = IdUtils.getId();
        area.setId(id);
        if (!save(area)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return area.getId();
    }

    @Override
    public boolean edit(AsArea area) {
        //查询当前修改节点是否存在
        AsArea currentArea = this.getOne(new QueryWrapper<AsArea>().lambda().eq(AsArea::getId, area.getId()), false);
        BusinessExceptionUtil.checkNotNull(currentArea, "区域不存在");

        //查询新的父级节点，如果父级节点不存在，则修改失败
        AsArea parentArea;
        if (Objects.equals(area.getPid(), 0L)) {
            //兼容父级节点是顶级根节点，在数据库里面是没有根节点数据，模拟构造一条根节点数据
            parentArea = new AsArea();
            parentArea.setId(0L);
            parentArea.setLevel(-1);
            parentArea.setPaths("");
        } else {
            parentArea = this.getOne(new QueryWrapper<AsArea>().lambda().eq(AsArea::getId, area.getPid()), false);
        }
        BusinessExceptionUtil.checkNotNull(parentArea, "父节点不存在");

        //是否移动分类层级，true：改变分类层级 false：没有改变分类层级(仅改变编码和名称等信息)
        boolean moveLevel = !currentArea.getPid().equals(parentArea.getId());

        // 验证数据唯一性
        checkRepeat(area, true);

        //有移动资产分类层级，需要校验层级高度
        if (moveLevel) {
            //查询当前资产区域及其子资产区域
            String sonPath = currentArea.getPaths() + currentArea.getId() + ",";
            List<AsArea> currentSubArea = this.list(new QueryWrapper<AsArea>().lambda().likeRight(AsArea::getPaths, sonPath));

            //资产分类层级不能超过7层
            TreeUtils.checkLevelOverMax(parentArea, currentSubArea, 7);

            //父级节点不能是当前修改节点及其子节点
            TreeUtils.checkParent(parentArea.getId(), currentArea, currentSubArea);

            //修改了资产分类层级需要修改paths等信息
            AsArea modify = new AsArea();
            modify.setId(currentArea.getId());
            modify.setPid(area.getPid());
            modify.setAreaCode(area.getAreaCode());
            modify.setAreaName(area.getAreaName());
            modify.setAreaImages(area.getAreaImages());
            modify.setAdmins(area.getAdmins());
            modify.setAreaDesc(area.getAreaDesc());
            modify.setLevel(parentArea.getLevel() + 1);
            modify.setPaths(parentArea.getPaths() + parentArea.getId() + ",");

            //递归修改paths和level等信息
            TreeUtils.assemblePaths(modify, currentSubArea);
            currentSubArea.add(modify);

            //批量修改paths信息
            updateBatchById(currentSubArea, currentSubArea.size());
        } else {
            //没有修改层级，直接修改当前节点的编码和名称即可
            AsArea asArea = new AsArea();
            asArea.setId(area.getId());
            asArea.setAreaName(area.getAreaName());
            asArea.setAreaCode(area.getAreaCode());
            asArea.setAreaDesc(area.getAreaDesc());
            asArea.setAreaImages(area.getAreaImages());
            asArea.setAdmins(area.getAdmins());
            this.updateById(asArea);
        }
        // 发送消息记录缓存
        if (StrUtil.isNotEmpty(area.getAreaName())) {
            SpringUtil.getBean(CacheAreaStrategy.class).evictCache(area.getId());
        }
        return true;
    }

    @Override
    public List<DictDataDto> listSimpleWithPerms(AsDataAuthority authority, Long companyId) {
        List<DictDataDto> data = new ArrayList<>();
        if (AssetConstant.AUTHORITY_TYPE_ALL == authority.getAuthorityType()) {
            // 所有权限，标记全部数据
            data = list(Wrappers.lambdaQuery(AsArea.class).select(AsArea::getId, AsArea::getAreaName).eq(AsArea::getCompanyId, companyId))
                    .stream().map(o -> new DictDataDto().setValue(o.getId().toString()).setLabel(o.getAreaName()))
                    .collect(Collectors.toList());
        } else if (AssetConstant.AUTHORITY_TYPE_DEPT_ONLY == authority.getAuthorityType()
                || AssetConstant.AUTHORITY_TYPE_DEPT_AND_CHILD_DEPT == authority.getAuthorityType()) {
            data = getBaseMapper().listSimpleWithPerms(authority.getUserId(), authority.getAuthorityType(), companyId);
        } else if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == authority.getAuthorityType()) {
            // 自定义数据类型
            if (CollUtil.isNotEmpty(authority.getAuthorityData())) {
                List<UserDataOrgPermBizDto> authorityDataList = authority.userDataOrgPermBizDtoAuthorityData();
                List<Long> areaIds = new ArrayList<>();
                for (UserDataOrgPermBizDto userDataOrgPermBizDto : authorityDataList) {
                    areaIds.addAll(userDataOrgPermBizDto.getBiz());
                }
                List<AsArea> areaList = list(Wrappers.lambdaQuery(AsArea.class)
                        .select(AsArea::getId, AsArea::getAreaName)
                        .eq(AsArea::getCompanyId, companyId)
                        .in(AsArea::getId, areaIds));
                data = areaList.stream().map(o -> new DictDataDto().setLabel(o.getAreaName()).setValue(o.getId().toString()))
                        .collect(Collectors.toList());
            }
        }
        return data;
    }

    @Override
    public Long getOne(String name, String code) {
        LoginUserDto userDto = LoginUserThreadLocal.get();
        if (userDto == null
                || userDto.getCusUser() == null
                || userDto.getCusUser().getCompanyId() == null) {
            return null;
        }
        Long companyId = userDto.getCusUser().getCompanyId();
        if (StrUtil.isNotEmpty(name)) {
            String cacheKey = RedisConstant.areaDictKey() + ":" + companyId + ":name:" + name;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsArea one = getOne(Wrappers.lambdaQuery(AsArea.class)
                        .eq(AsArea::getCompanyId, companyId)
                        .eq(AsArea::getAreaName, name), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        } else if (StrUtil.isNotEmpty(code)) {
            String cacheKey = RedisConstant.areaDictKey() + ":" + companyId + ":code:" + code;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsArea one = getOne(Wrappers.lambdaQuery(AsArea.class)
                        .eq(AsArea::getCompanyId, companyId)
                        .eq(AsArea::getAreaCode, code), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        }
        return null;
    }

    /**
     * 实体唯一性校验
     *
     * @param area   实体
     * @param isEdit 是否是修改操作
     */
    private void checkRepeat(AsArea area, boolean isEdit) {
        LambdaQueryWrapper<AsArea> wrapper = new LambdaQueryWrapper<>();
        // 更新时可修改自身属性，但不能与其他记录相同。这个先排开当前记录
        if (isEdit) {
            wrapper.ne(AsArea::getId, area.getId());
        }
        LambdaQueryWrapper<AsArea> clone = wrapper.clone();
        // categoryCode 唯一性校验
        wrapper.eq(AsArea::getAreaCode, area.getAreaCode());
        AsArea codeRepeat = this.getOne(wrapper);
        if (ObjectUtil.isNotNull(codeRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "区域编码", area.getAreaCode());
        }
        // categoryName 唯一性校验
        clone.eq(AsArea::getPid, area.getPid())
                .eq(AsArea::getOrgId, area.getOrgId())
                .eq(AsArea::getAreaName, area.getAreaName());
        AsArea nameRepeat = this.getOne(clone, false);
        if (ObjectUtil.isNotNull(nameRepeat)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "区域名称", area.getAreaName());
        }
    }

    /**
     * 数据权限过滤
     *
     * @param areaDtos
     * @param orgId
     * @return
     */
    private List<AreaDto> filterPerms(List<AreaDto> areaDtos, Long orgId) {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        List<AreaDto> resolve = new ArrayList<>();
        if (CollUtil.isNotEmpty(areaDtos)) {
            if (BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
                return areaDtos.stream().map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList());
            }

            AsDataAuthority dataAuthority = dataAuthorityService.getByUserAndDataCodeAndCode(userId,
                    AssetConstant.DATA_PERMISSION_AREA, AssetConstant.AUTHORITY_DEPTS);

            BusinessExceptionUtil.checkNotNull(dataAuthority, "您没有此数据权限, 请联系管理员");

            Map<Long, AreaDto> data = areaDtos.stream().collect(Collectors.toMap(AreaDto::getId, o -> o));

            if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == dataAuthority.getAuthorityType()) {
                if (CollUtil.isNotEmpty(dataAuthority.getAuthorityData())) {
                    List<UserDataOrgPermBizDto> orgBizs = dataAuthority.userDataOrgPermBizDtoAuthorityData();
                    Map<Long, UserDataOrgPermBizDto> orgBizMap = orgBizs.stream().collect(
                            Collectors.toMap(UserDataOrgPermBizDto::getOrg, o -> o));
                    if (orgId != null) {
                        UserDataOrgPermBizDto orgBiz = orgBizMap.get(orgId);
                        BusinessExceptionUtil.checkNotNull(orgBiz, "您没有此数据权限, 请联系管理员");
                        List<Long> myAreaIds = orgBiz.getBiz();
                        List<AreaDto> myAreas = areaDtos.stream().filter(o -> myAreaIds.contains(o.getId())).collect(Collectors.toList());
                        for (AreaDto areaDto : myAreas) {
                            areaDto.setDisabled(Boolean.FALSE);
                            resolve.add(areaDto);
                            findParent(data, areaDto.getPid(), resolve);
                        }
                    } else {
                        Set<Long> myAreaIds = new HashSet<>();
                        for (UserDataOrgPermBizDto value : orgBizMap.values()) {
                            myAreaIds.addAll(value.getBiz());
                        }
                        return areaDtos.stream().peek(f -> f.setDisabled(!myAreaIds.contains(f.getId()))).collect(Collectors.toList());
                    }
                }
            } else {
                return areaDtos.stream().map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList());
            }
        }
        return resolve.stream().filter(DeduplicationUtil.distinctByKey(AreaDto::getId))
                .sorted(Comparator.comparing(AreaDto::getSortNum, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(AreaDto::getCreateTime, Comparator.nullsLast(LocalDateTime::compareTo)))
                .collect(Collectors.toList());
    }

    private List<AreaDto> filterPerms(List<AreaDto> areaDtos) {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        List<AreaDto> resolve = new ArrayList<>();
        if (CollUtil.isNotEmpty(areaDtos)) {
            if (BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
                return areaDtos.stream().map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList());
            }

            AsDataAuthority dataAuthority = dataAuthorityService.getByUserAndDataCodeAndCode(userId,
                    AssetConstant.DATA_PERMISSION_AREA, AssetConstant.AUTHORITY_DEPTS);

            BusinessExceptionUtil.checkNotNull(dataAuthority, "您没有此数据权限, 请联系管理员");

            if (AssetConstant.AUTHORITY_TYPE_CUSTOMIZE == dataAuthority.getAuthorityType()) {
                if (CollUtil.isNotEmpty(dataAuthority.getAuthorityData())) {
                    List<UserDataOrgPermBizDto> orgBizs = dataAuthority.userDataOrgPermBizDtoAuthorityData();
                    List<Long> myAreaIds = new ArrayList<>();
                    // 遍历出所有自定义的区域id
                    for (UserDataOrgPermBizDto orgBiz : orgBizs) {
                        myAreaIds.addAll(orgBiz.getBiz());
                    }
                    // 从所拥有权限的org中找出所有自定义的区域
                    List<AreaDto> myAreas = areaDtos.stream().filter(o -> myAreaIds.contains(o.getId()))
                            .collect(Collectors.toList());
                    resolve.addAll(myAreas.stream().map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList()));
                }
            } else {
                return areaDtos.stream().map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList());
            }
        }
        return resolve.stream().filter(DeduplicationUtil.distinctByKey(AreaDto::getId))
                .sorted(Comparator.comparing(AreaDto::getSortNum, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(AreaDto::getCreateTime, Comparator.nullsLast(LocalDateTime::compareTo)))
                .collect(Collectors.toList());
    }

    private void findParent(Map<Long, AreaDto> data, Long currentId, List<AreaDto> resolve) {
        AreaDto areaDto = data.get(currentId);
        if (areaDto == null) {
            return;
        }
        resolve.add(areaDto);
        findParent(data, areaDto.getPid(), resolve);
    }

    private AsAssetImportError copyToAsAreaImportError(AreaImportDto importDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_AREA);
        List<LuckySheetModel> sheetModelList = importDto.getSheetModelList();
        importError.setExcelJson(new ArrayList<>(sheetModelList));
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }

}
