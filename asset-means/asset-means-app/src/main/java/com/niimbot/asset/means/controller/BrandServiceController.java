package com.niimbot.asset.means.controller;


import com.niimbot.asset.means.model.AsBrand;
import com.niimbot.asset.means.service.AsBrandService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年5月11日11:03:46
 */
@RestController
@RequestMapping("server/means/brand")
public class BrandServiceController {

    private final AsBrandService brandService;

    @Autowired
    public BrandServiceController(AsBrandService brandService) {
        this.brandService = brandService;
    }

    @GetMapping("/search")
    public List<AsBrand> search(@RequestParam(value = "name", required = false) String name) {
        return this.brandService.search(name);
    }

}
