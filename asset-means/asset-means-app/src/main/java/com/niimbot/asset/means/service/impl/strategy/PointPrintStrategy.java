package com.niimbot.asset.means.service.impl.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.abs.PointAbs;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsUserPrintTaskService;
import com.niimbot.equipment.AsEquipmentSiteInspectPointDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import com.niimbot.system.PrintPointPageQueryDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PointPrintStrategy implements PrintStrategy {

    private final PointAbs pointAbs;

    private final AreaService areaService;

    private final AsUserPrintTaskService printTaskService;

    @Override
    public Short printType() {
        return DictConstant.PRINT_TYPE_POINT;
    }

    @Override
    public void resolvePrintData(AsUserPrintTask printTask, PrintQueryDataDto queryDto) {
        PrintPointPageQueryDto printPointPageQueryDto = (PrintPointPageQueryDto) queryDto;
        printPointPageQueryDto.setPageSize(50000);
        printPointPageQueryDto.setCompanyId(LoginUserThreadLocal.getCompanyId());
        List<AreaDto> permission = areaService.listArea(new AreaQueryDto().setFilterPerm(true));
        if (CollUtil.isNotEmpty(permission)) {
            printPointPageQueryDto.setPremAreaIds(permission.stream().map(AreaDto::getId).collect(Collectors.toSet()));
        }
        PageUtils<AsEquipmentSiteInspectPointDto> page = pointAbs.printPage(printPointPageQueryDto);

        List<Long> pointIds = page.getList().stream().map(AsEquipmentSiteInspectPointDto::getId).collect(toList());
        if (CollUtil.isEmpty(pointIds)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_INVALID, "点位ID");
        }
        printTask.setAssets(pointIds);
        printTask.setQueryData((JSONObject) JSONObject.toJSON(queryDto));
        // 为了设置任务名称
        List<AsEquipmentSiteInspectPointDto> idsForTaskName = page.getList().size() >= 3 ? page.getList().subList(0, 3) : page.getList();
        printTask.setAssetsSnapshot(
                idsForTaskName.stream().map(v -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("pointName", v.getPointName());
                    return jsonObject;
                }).collect(toList())
        );
    }

    @Override
    public void translate(List<JSONObject> dataList, String companyName, Long standardId) {
        // nothing
    }

    @Override
    public void dealPrintSnapshot(List<AsPrintDataSnapshot> snapshots) {
        AsUserPrintTask printTask = printTaskService.getById(snapshots.get(0).getTaskId());
        PrintPointPageQueryDto dto = printTask.getQueryData().toJavaObject(PrintPointPageQueryDto.class);
        dto.setIncludePointIds(snapshots.stream().map(AsPrintDataSnapshot::getDataId).collect(Collectors.toSet()));
        dto.setPageNum(1L);
        dto.setPageSize(snapshots.size());
        ConcurrentMap<Long, AsEquipmentSiteInspectPointDto> map = pointAbs.printPage(dto).getList().stream().collect(Collectors.toConcurrentMap(AsEquipmentSiteInspectPointDto::getId, v -> v));
        snapshots.forEach(snapshot -> {
            if (Objects.isNull(snapshot.getSnapshot())) {
                AsEquipmentSiteInspectPointDto point = map.get(snapshot.getDataId());
                JSONObject object = new JSONObject();
                object.put("id", String.valueOf(point.getId()));
                object.put("pointName", point.getPointName());
                object.put("pointCode", point.getPointCode());
                object.put("qrCode", "point:" + point.getId());
                snapshot.setSnapshot(object);
            }
        });
    }

    @Override
    public void dealResultPrintTask(PrintPdfDto dto,
                                    PrintDataDto printDataDto,
                                    JSONObject templateJson,
                                    List<PrintDataViewDto.DataDto> resultList) {
        // nothing
    }

    @Override
    public List<JSONObject> getPrintData(PrintPdfDto dto, PrintDataDto printDataDto) {
        return ListUtil.empty();
    }
}
