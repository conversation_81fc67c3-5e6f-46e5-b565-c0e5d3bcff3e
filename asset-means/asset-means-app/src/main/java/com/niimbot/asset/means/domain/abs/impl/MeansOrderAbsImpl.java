package com.niimbot.asset.means.domain.abs.impl;

import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.todo.abs.MeansOrderAbs;
import com.niimbot.asset.todo.dto.MeansOrderFieldGetQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/client/abs/means/meansOrderAbs/")
@RequiredArgsConstructor
public class MeansOrderAbsImpl implements MeansOrderAbs {

    private final AsFormService formService;


    public List<FormFieldCO> listOrderFiled(MeansOrderFieldGetQry qry) {
        FormVO formVO =
                formService.getTpl(OrderFormTypeEnum.getOrderFormTypeEnum(qry.getOrderType()).getBizType(),
                        qry.getCompanyId());
        return formVO.getFormFields();
    }
}
