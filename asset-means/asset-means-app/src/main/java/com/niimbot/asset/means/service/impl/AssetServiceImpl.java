package com.niimbot.asset.means.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.EquipmentConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.filter.SQLFilter;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.EventPublishHandler;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.event.AddMeansEvent;
import com.niimbot.asset.means.mapper.AsAssetMapper;
import com.niimbot.asset.means.mapper.AsAssetOperationMapper;
import com.niimbot.asset.means.mapper.AsAssetStatusMapper;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.model.AsAssetOperation;
import com.niimbot.asset.means.resolver.MySqlAssetQueryConditionResolver;
import com.niimbot.asset.means.service.*;
import com.niimbot.asset.system.abs.AssetFinanceInfoAbs;
import com.niimbot.asset.system.abs.EquipmentMaintainTaskAbs;
import com.niimbot.asset.system.abs.InventoryAbs;
import com.niimbot.asset.system.dto.clientobject.GenSerialNoCmd;
import com.niimbot.asset.system.dto.clientobject.TagAttrCO;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import com.niimbot.asset.system.model.AsEmployeeAssetChange;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.ots.SerialNumberOts;
import com.niimbot.asset.system.ots.SystemOrgOts;
import com.niimbot.asset.system.ots.SystemRecycleBinOts;
import com.niimbot.asset.system.service.AsEmployeeAssetChangeService;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.asset.system.support.DataSnapshots;
import com.niimbot.dynamicform.ValidatorModel;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormPropsCO;
import com.niimbot.easydesign.form.dto.formprops.EditFieldRules;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.equipment.EntMatPlanEntData;
import com.niimbot.equipment.GetSelectedEntData;
import com.niimbot.finance.AssetFinanceInfoDto;
import com.niimbot.inventory.InventoryDispatchGroupAssetDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.*;
import com.niimbot.page.SortQuery;
import com.niimbot.system.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 资产表 Service 业务逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Slf4j
@Service
public class AssetServiceImpl extends ServiceImpl<AsAssetMapper, AsAsset> implements AssetService {

    private static final String STANDARD_ID = "standardId";
    private static final String USE_PERSON = "usePerson";
    private static final String USE_ORG = "useOrg";
    private static final String ERROR_COLOR = "#000000";
    private static final String ORIGIN_VOUCHER = "originVoucher";

    @Resource
    private AsAssetOperationMapper assetOperationMapper;

    @Resource
    private AsAssetStatusMapper assetStatusMapper;

    @Resource
    private AsAssetLogService assetLogService;

    @Resource
    private StandardService standardService;

    @Resource
    private AsAssetImportErrorService assetImportErrorService;

    @Resource
    private RedisService redisService;

    @Resource
    private AsEmployeeAssetChangeService employeeAssetChangeService;

    @Resource
    private CacheResourceUtil cacheResourceUtil;

    @Resource
    private MySqlAssetQueryConditionResolver conditionResolver;

    @Resource
    private AsFormService formService;

    @Resource
    private SerialNumberOts serialNumberService;

    @Resource
    private AssetUtil assetUtil;

    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;

    @Autowired
    private AssetFinanceInfoAbs assetFinanceInfoAbs;

    @Resource
    private SystemOrgOts systemOrgService;
    @Autowired
    private AssetReportFieldService assetReportFieldService;

    @Resource
    private AreaService areaService;

    @Resource
    private CategoryService categoryService;
    @Autowired
    private AssetRelationService assetRelationService;
    @Autowired
    private EquipmentMaintainTaskAbs equipmentMaintainTaskAbs;
    @Autowired
    private InventoryAbs inventoryAbs;
    @Autowired
    private SystemRecycleBinOts systemRecycleBinService;


    /**
     * 分页查询
     *
     * @param queryDto 查询信息
     */
    @Override
    public IPage<AssetAppPageDto> pageApp(AssetQueryConditionDto queryDto) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        transformKwFields(queryDto);
        Page<AsAsset> page = buildAssetSort(tableAlias, queryDto);
        if (CollUtil.isNotEmpty(queryDto.getIncludeAssetIds()) || CollUtil.isNotEmpty(queryDto.getAssetIds())) {
            page.setSize(Integer.MAX_VALUE);
        }
        queryDto.getIncludeAssetIds().addAll(systemRecycleBinService.getResIdsForQuery(new GetRecycleBins().setCompanyId(LoginUserThreadLocal.getCompanyId()).setResType(1).setFormRecycle(queryDto.getFormRecycle())));
        return this.getBaseMapper().pageApp(page, queryDto, conditions);
    }

    @Override
    public IPage<AssetRelationAppDto> relationPageApp(AssetQueryConditionDto queryDto) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        //数据权限条件
        List<QueryConditionDto> assetStatusConditionList = null;
        if (CollUtil.isNotEmpty(queryDto.getConditions())) {
            assetStatusConditionList = queryDto.getConditions().stream().filter(item -> AssetConstant.STATUS.equalsIgnoreCase(item.getCode())).collect(toList());
        }
        String statusCondition = conditionResolver.resolveQueryCondition(tableAlias, assetStatusConditionList);
        Page<AsAsset> page = queryDto.buildIPageOrigin();
        //默认按照关联时间进行倒排
        defaultRelationTimeOrder(page, "d", queryDto.getSidx());
        IPage<AssetRelationAppDto> mainAssetPage = this.getBaseMapper().mainAssetRelationApp(page, queryDto, conditions, statusCondition);
        if (Objects.isNull(mainAssetPage) || CollUtil.isEmpty(mainAssetPage.getRecords())) {
            return mainAssetPage;
        }

        Map<Long, AssetRelationAppDto> mainAssetMap = mainAssetPage.getRecords().stream().collect(Collectors.toMap(AssetRelationAppDto::getId, value -> value, (v1, v2) -> v2));
        queryDto.setAssetIds(ListUtil.toList(mainAssetMap.keySet()));

        Map<Long, AssetRelationAppDto> assetMap = null;
        List<AssetRelationAppDto> relationAppDtoList = this.getBaseMapper().assetRelationApp(queryDto);
        if (CollUtil.isNotEmpty(relationAppDtoList)) {
            assetMap = relationAppDtoList.stream().collect(Collectors.toMap(AssetRelationAppDto::getId, value -> value, (v1, v2) -> v2));
        }
        List<AssetRelationAppDto> dataList = new ArrayList<>();
        for (AssetRelationAppDto item : mainAssetPage.getRecords()) {
            AssetRelationAppDto data = new AssetRelationAppDto();
            if (Objects.nonNull(assetMap) && Objects.nonNull(assetMap.get(item.getId()))) {
                BeanUtils.copyProperties(assetMap.get(item.getId()), data);
            }
            data.setRelationTime(item.getRelationTime());
            data.setSubAssetCount(item.getSubAssetCount());
            dataList.add(data);
        }
        mainAssetPage.setRecords(dataList);
        return mainAssetPage;
    }

    @Override
    public IPage<AssetRelationAppDto> subAssetPageApp(AssetRelationQueryConditionDto queryConditionDto) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryConditionDto.getConditions());
        Page<AssetRelationDto> page = queryConditionDto.buildIPageOrigin();
        //默认按照关联时间进行倒排
        defaultRelationTimeOrder(page, "relation", queryConditionDto.getSidx());
        return this.getBaseMapper().subAssetApp(page, queryConditionDto, conditions);
    }

    @Override
    public AssetRelationAppDto queryMainAssetInfoApp(Long assetId, Long subAssetId, Long companyId) {
        return this.getBaseMapper().selectBySubAssetIdApp(assetId, subAssetId, companyId);
    }

    /**
     * 默认按组合资产关联时间进行倒排
     *
     * @param page
     * @param tableAlias
     * @param sidx
     */
    private void defaultRelationTimeOrder(Page page, String tableAlias, String sidx) {
        if (StrUtil.isBlank(sidx)) {
            List<OrderItem> orders = new ArrayList<>();
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(String.join(".", tableAlias, "relation_time"));
            orderItem.setAsc(Boolean.FALSE);
            orders.add(orderItem);
            if (CollUtil.isNotEmpty(page.getOrders())) {
                orders.addAll(page.getOrders());
            }
            page.setOrders(orders);
        }
    }

    /**
     * 员工资产列表清单
     *
     * @param empId 员工ID
     * @return
     */
    @Override
    public List<AssetDto> empAssetList(Long empId) {
        return this.getBaseMapper().selectAllByUsePersonOrManagerOwner(empId);
    }

    /**
     * 通用排序处理
     *
     * @param tableAlias
     * @param queryDto
     * @return
     */
    private Page<AsAsset> buildAssetSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_asset";
        }
        Page<AsAsset> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = sortField();
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ASSET_EXT_FIELD.containsKey(column)) {
                order.setColumn(String.format(MySqlAssetQueryConditionResolver.SQL_SEGMENT_TPL, tableAlias, StrUtil.toUnderlineCase(column)));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.asset_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlAssetQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlAssetQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlAssetQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    @Override
    public IPage<AssetDto> customPage(AssetQueryConditionDto queryDto) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        transformKwFields(queryDto);
        Page<AsAsset> page = buildAssetSort(tableAlias, queryDto);
        if (CollUtil.isNotEmpty(queryDto.getIncludeAssetIds()) || CollUtil.isNotEmpty(queryDto.getAssetIds())) {
            page.setSize(Integer.MAX_VALUE);
        }
        queryDto.getIncludeAssetIds().addAll(systemRecycleBinService.getResIdsForQuery(new GetRecycleBins().setCompanyId(LoginUserThreadLocal.getCompanyId()).setResType(1).setFormRecycle(queryDto.getFormRecycle())));
        if (queryDto.getShowRepaired() != null) {
            String showRepairedKey = "showRepaired:" + LoginUserThreadLocal.getCurrentUserId();
            if (redisService.hasKey(showRepairedKey)) {
                queryDto.setShowRepaired((Boolean) redisService.get(showRepairedKey));
            } else {
                redisService.set(showRepairedKey, queryDto.getShowRepaired());
            }
        }
        return this.getBaseMapper().customPage(page, queryDto, conditions);
    }

    @Override
    public IPage<EntMatPlanEntData> pageEntMatPlanMeansData(GetSelectedEntData get) {
        String tableAlias = "a";
        get.setCompanyId(LoginUserThreadLocal.getCompanyId());
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, get.getConditions());
        transformKwFields(get);
        Page<AsAsset> page = buildAssetSort(tableAlias, get);
        if (EquipmentConstant.RANGE_TYPE_IS_MAS == get.getRangeType()) {
            return this.getBaseMapper().pageEntMatPlanDataRangeTypeIsMas(page, get, conditions);
        }
        if (EquipmentConstant.RANGE_TYPE_IS_CATE == get.getRangeType()) {
            return this.getBaseMapper().pageEntMatPlanDataRangeTypeIsCate(page, get, conditions);
        }
        return new Page<>();
    }

    @Override
    public IPage<AssetRelationDto> relationPage(AssetQueryConditionDto queryDto) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        //数据权限条件
        List<QueryConditionDto> assetStatusConditionList = null;
        if (CollUtil.isNotEmpty(queryDto.getConditions())) {
            assetStatusConditionList = queryDto.getConditions().stream().filter(item -> AssetConstant.STATUS.equalsIgnoreCase(item.getCode())).collect(toList());
        }
        String statusCondition = conditionResolver.resolveQueryCondition(tableAlias, assetStatusConditionList);
        transformKwFields(queryDto);
        Page<AsAsset> page = buildAssetSort("a", queryDto);
        //默认按照关联时间进行倒排
        defaultRelationTimeOrder(page, "d", queryDto.getSidx());
        return this.getBaseMapper().relationPage(page, queryDto, conditions, statusCondition);
    }

    @Override
    public IPage<AssetRelationDto> subAssetPage(AssetRelationQueryConditionDto queryConditionDto) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryConditionDto.getConditions());
        Page<AssetRelationDto> page = queryConditionDto.buildIPage();
        //默认按照关联时间进行倒排
        defaultRelationTimeOrder(page, "b", queryConditionDto.getSidx());
        return this.getBaseMapper().subAssetPage(page, queryConditionDto, conditions);
    }

    /**
     * todo 转换关联类模糊搜索
     */
    private void transformKwFields(AssetQueryConditionDto queryDto) {
        Long start = System.currentTimeMillis();
        log.info("开始时间：" + DateUtil.now());
        if (CollUtil.isNotEmpty(queryDto.getKwFiled()) && StrUtil.isNotBlank(queryDto.getKw())) {
            queryDto.getKwFiled().forEach(t -> {
                SQLFilter.sqlInject(t.getCode());
                switch (t.getType().getCode()) {
                    case AssetConstant.ED_YZC_ORG: {
                        /**
                         * todo 查询资产组织模糊搜索IDs
                         */
                        t.fullIds(t, systemOrgService.listOrgPermission(queryDto.getKw()).stream().map(z -> String.valueOf(z.getId())).collect(toList()));
                        log.info(AssetConstant.ED_YZC_ORG);
                        break;
                    }
                    case AssetConstant.ED_YZC_EMP: {
                        /**
                         * todo 查询资产员工模糊搜索IDs
                         */
                        t.fullIds(t, systemOrgService.listEmpPermission(queryDto.getKw()).stream().map(z -> String.valueOf(z.getId())).collect(toList()));
                        log.info(AssetConstant.ED_YZC_EMP);
                        break;
                    }
                    case AssetConstant.ED_YZC_AREA: {
                        /**
                         * todo 查询资产区域模糊搜索IDs
                         */
                        t.fullIds(t, areaService.listByAreaNamePermission(new AreaQueryDto().setKw(queryDto.getKw())).stream().map(z -> String.valueOf(z.getId())).collect(toList()));
                        log.info(AssetConstant.ED_YZC_AREA);
                        break;
                    }
                    case AssetConstant.ED_YZC_ASSET_CATE: {
                        /**
                         * todo 查询资产分类模糊搜索IDs
                         */
                        t.fullIds(t, categoryService.listByCategoryNamePermission(queryDto.getKw()).stream().map(z -> String.valueOf(z.getId())).collect(toList()));
                        log.info(AssetConstant.ED_YZC_ASSET_CATE);
                        break;
                    }
                    default:
                        break;
                }
            });
        }
        Long end = System.currentTimeMillis();
        log.info("结束时间：" + DateUtil.now());
        log.info("耗时：" + (end - start));
    }

    @Override
    public long customPageCount(AssetQueryConditionDto queryDto) {
        String conditions = conditionResolver.resolveQueryCondition("a", queryDto.getConditions());
        transformKwFields(queryDto);
        return this.getBaseMapper().customPageCount(queryDto, conditions);
    }

    @Override
    public IPage<Long> getPrintAssetIds(AssetQueryConditionDto queryDto, long companyId) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, queryDto.getConditions());
        transformKwFields(queryDto);
        Page<AsAsset> page = buildAssetSort(tableAlias, queryDto);
        return this.getBaseMapper().selectPrintAssetIds(page, queryDto, conditions, companyId);
    }

    @Override
    public List<AssetDto> listPc(AssetQueryConditionDto queryDto) {
        String conditions = conditionResolver.resolveQueryCondition("a", queryDto.getConditions());
        transformKwFields(queryDto);
        IPage<AssetDto> assetDtoIPage = this.getBaseMapper().customPage(queryDto.buildIPage(), queryDto, conditions);
        return assetDtoIPage.getRecords();
    }

    /**
     * 插入一条记录（选择字段，策略插入） 处理EpcId
     *
     * @param entity 实体对象
     */
    @Override
    public boolean save(AsAsset entity) {
        if (entity.getId() == null) {
            entity.setId(IdUtils.getId());
        }
        entity.setLabelEpcid(entity.getId() + "0");
        boolean save = super.save(entity);
        if (BooleanUtil.isTrue(save)) {
            Long companyId = LoginUserThreadLocal.getCompanyId();
            if (companyId != null) {
                Long assetCount = redisService.hIncr("asset_count", Convert.toStr(companyId), 1L);
                getBaseMapper().updateAssetStatistical(companyId, assetCount);
            }
        }
        return save;
    }

    /**
     * 批量插入 处理EpcId
     *
     * @param entityList ignore
     * @param batchSize  ignore
     * @return ignore
     */
    @Override
    public boolean saveBatch(Collection<AsAsset> entityList, int batchSize) {
        entityList.forEach(ast -> {
            if (ast.getId() == null) {
                ast.setId(IdUtils.getId());
            }
            ast.setLabelEpcid(ast.getId() + "0");
        });
        boolean save = super.saveBatch(entityList, batchSize);
        if (BooleanUtil.isTrue(save)) {
            Long companyId = LoginUserThreadLocal.getCompanyId();
            if (companyId != null) {
                Long assetCount = redisService.hIncr("asset_count", Convert.toStr(companyId), Convert.toLong(entityList.size()));
                getBaseMapper().updateAssetStatistical(companyId, assetCount);
            }
        }
        return save;
    }

    /**
     * 根据 ID 删除
     *
     * @param id 主键ID
     */
    @Override
    public boolean removeById(Serializable id) {
        boolean remove = super.removeById(id);
        if (BooleanUtil.isTrue(remove)) {
            Long companyId = LoginUserThreadLocal.getCompanyId();
            if (companyId != null) {
                Long assetCount = redisService.hDecr("asset_count", Convert.toStr(companyId), 1L);
                getBaseMapper().updateAssetStatistical(companyId, assetCount);
            }
        }
        return remove;
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<?> idList) {
        boolean remove = super.removeByIds(idList);
        if (BooleanUtil.isTrue(remove)) {
            Long companyId = LoginUserThreadLocal.getCompanyId();
            if (companyId != null) {
                Long assetCount = redisService.hDecr("asset_count", Convert.toStr(companyId), Convert.toLong(idList.size()));
                getBaseMapper().updateAssetStatistical(companyId, assetCount);
            }
        }
        return remove;
    }

    @Override
    public void preAddHandle(List<AsAsset> assetList, Long standardId) {
        // 清理额外属性
        FormVO formVO = formService.assetTpl();
        List<FormFieldCO> fieldCOS = formVO.getFormFields();
        List<FormFieldCO> standardExtField = new ArrayList<>();
        if (standardId != null) {
            standardExtField = standardService.getStandardExtField(formVO.getFormId(), standardId);
        }
        fieldCOS.addAll(standardExtField);

        List<JSONObject> assetDataList = new ArrayList<>();
        assetList.forEach(a -> {
            // 清理脏数据
            JSONObject assetData = a.getAssetData();
            FormFieldConvert.clearInvalidField(assetData, fieldCOS);
            assetDataList.add(assetData);
            // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
            if (StrUtil.isNotBlank(a.getAssetData().getString(USE_ORG)) ||
                    StrUtil.isNotBlank(a.getAssetData().getString(USE_PERSON))) {
                a.setStatus(AssetConstant.ASSET_STATUS_USING);
            }
        });

        // 添加业务流水号
        setSerialNo(formVO, assetDataList);
        formService.formValidator(assetDataList, formVO, AsFormService.BIZ_TYPE_ASSET);
        // 校验扩展属性
        if (CollUtil.isNotEmpty(standardExtField)) {
            // 只校验传入的表单属性
            formService.fieldValidator(assetDataList, standardExtField);
        }
        assetList.parallelStream().forEach(a -> FormFieldConvert.formatJsonField(a.getAssetData(), fieldCOS));
    }

    @Override
    public void preAddHandleForStore(List<AsAsset> assetList, Long standardId, boolean isValid) {
        // 清理额外属性
        FormVO formVO = formService.assetTpl();
        List<FormFieldCO> fieldCOS = formVO.getFormFields();
        List<FormFieldCO> standardExtField = new ArrayList<>();
        if (standardId != null) {
            standardExtField = standardService.getStandardExtField(formVO.getFormId(), standardId);
        }
        fieldCOS.addAll(standardExtField);

        List<JSONObject> assetDataList = new ArrayList<>();
        assetList.forEach(a -> {
            // 清理脏数据
            JSONObject assetData = a.getAssetData();
            FormFieldConvert.clearInvalidField(assetData, fieldCOS);
            assetDataList.add(assetData);
            // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
            if (StrUtil.isNotBlank(a.getAssetData().getString(USE_ORG)) ||
                    StrUtil.isNotBlank(a.getAssetData().getString(USE_PERSON))) {
                a.setStatus(AssetConstant.ASSET_STATUS_USING);
            }
        });

        // 添加业务流水号
        setSerialNo(formVO, assetDataList);

        if (isValid) {
            formService.fieldFormMultiValidator(assetDataList, formVO.getFormId());
            // 校验扩展属性
            if (CollUtil.isNotEmpty(standardExtField)) {
                // 只校验传入的表单属性
                formService.fieldValidator(assetDataList, standardExtField);
            }
        }
        assetList.parallelStream().forEach(a -> FormFieldConvert.formatJsonField(a.getAssetData(), fieldCOS));
    }

    /**
     * @param formVO
     * @param assetDataList
     * @return 是否包含系统自动生成
     */
    private boolean setSerialNo(FormVO formVO, List<JSONObject> assetDataList) {
        AtomicReference<Boolean> bool = new AtomicReference<>(false);
        formVO.getFormFields().stream()
                .filter(field ->
                        field.getFieldType().equals(FormFieldCO.YZC_ASSET_SERIALNO))
                .findFirst()
                .ifPresent(field -> {
                    List<JSONObject> filterAssetData = assetDataList.stream()
                            .filter(a -> "系统自动生成".equals(a.getString(field.getFieldCode()))).collect(toList());
                    if (filterAssetData.size() > 0) {
                        bool.set(true);
                        GenSerialNoCmd genSerialNoCmd = field.getFieldProps().getJSONObject("defaultRule").toJavaObject(GenSerialNoCmd.class);
                        genSerialNoCmd.setFieldCode(field.getFieldCode());
                        genSerialNoCmd.setData(filterAssetData);
                        genSerialNoCmd.setType(field.getFieldType());
                        serialNumberService.fillSerial(genSerialNoCmd);
                    }
                });
        return bool.get();
    }

    public void preEditHandle(AsAsset asset, boolean multi) {
        // 清理额外属性
        FormVO formVO = formService.assetTpl();
        JSONObject formProps = formVO.getFormProps();
        List<String> editCodes = new ArrayList<>();
        if (formProps != null && formProps.containsKey(FormPropsCO.EDIT_FIELD_RULES)) {
            editCodes = formProps.getJSONArray(FormPropsCO.EDIT_FIELD_RULES)
                    .toJavaList(EditFieldRules.class)
                    .stream()
                    .filter(editFieldRules -> editFieldRules.getValue() == 1)
                    .map(EditFieldRules::getFieldCode)
                    .collect(toList());
        }

        // 可编辑的属性
        List<FormFieldCO> assetEditField = new ArrayList<>();
        for (FormFieldCO f : formVO.getFormFields()) {
            if (editCodes.contains(f.getFieldCode()) && !f.isHidden()) {
                assetEditField.add(f);
            }
        }

        // 标准品
        List<FormFieldCO> standardExtField = standardService.getStandardExtField(formVO.getFormId(), asset.getStandardId());
        editCodes.addAll(standardExtField.stream().filter(f -> !f.isHidden()).map(FormFieldCO::getFieldCode).collect(toList()));

        // 清理数据
        List<String> finalEditCodes = editCodes;
        asset.getAssetData().keySet().removeIf(key -> !finalEditCodes.contains(key));

        // 格式化json
        List<FormFieldCO> formFieldCOList = new ArrayList<>(formVO.getFormFields());
        formFieldCOList.addAll(standardExtField);
        if (CollUtil.isNotEmpty(assetEditField)) {
            // 表单校验
            if (multi) {
                formService.fieldValueValidator(asset.getAssetData(), formVO.getFormId());
            } else {
                // 只校验传入的表单属性
                formService.fieldValidatorWithId(new ValidatorModel(asset.getId(), asset.getAssetData()),
                        assetEditField, AsFormService.BIZ_TYPE_ASSET);
            }
        }
        if (CollUtil.isNotEmpty(standardExtField)) {
            // 只校验传入的表单属性
            formService.fieldValidator(asset.getAssetData(), standardExtField);
        }
        FormFieldConvert.formatJsonField(asset.getAssetData(), formFieldCOList);
    }

    /**
     * 新增
     *
     * @param asset 资产信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(AsAsset asset, Integer storeType, Long batchId) {
        Long id = IdUtils.getId();
        asset.setId(id);
        this.save(asset);
        EventPublishHandler.publish(new AddMeansEvent(storeType, batchId, Lists.newArrayList(asset)));

        //记录过期时间
        assetReportFieldService.processExpireTime(Collections.singletonList(asset));
        return id;
    }

    @Override
    public void addLog(Long assetId, JSONObject assetData, AssetAddLogDto assetAddLog) {
        // 写入资产履历
        AsAssetLog asAssetLog = new AsAssetLog().setAssetId(assetId)
                .setHandleTime(LocalDateTime.now())
                .setActionName("入库")
                .setActionContent(assetAddLog.getActionContent())
                .setOrderNo(assetAddLog.getOrderNo())
                .setOrderId(assetAddLog.getOrderId());
        if (assetAddLog.getActionType() != null) {
            asAssetLog.setActionType(assetAddLog.getActionType());
        } else {
            asAssetLog.setActionType(AssetConstant.OPT_ADD);
        }
        if (StrUtil.isNotBlank(assetData.getString(ORIGIN_VOUCHER))) {
            asAssetLog.setOrderNo(assetData.getString(ORIGIN_VOUCHER));
        }
        if (!assetLogService.save(asAssetLog)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(List<AsAsset> list, AssetAddLogDto assetAddLog, String batchNo) {
        list.forEach(v -> v.setId(IdUtils.getId()));
        this.saveBatch(list);
        list.forEach(v -> addLog(v.getId(), v.getAssetData(), assetAddLog));

        //记录过期时间
        assetReportFieldService.processExpireTime(list);
        EventPublishHandler.publish(new AddMeansEvent(StoreMode.ORDER, batchNo, list));
        return true;
    }

    /**
     * 编辑
     *
     * @param asset 资产信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(AsAsset asset) {
        // 判断是否库里存在
        AsAsset originalAsset = this.getById(asset.getId());
        if (ObjectUtil.isNull(originalAsset)) {
            throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
        }
        asset.setStandardId(asset.getAssetData().getLong(STANDARD_ID));
        // 校验是否可编辑
        if (!canEdit(ListUtil.of(asset.getId()))) {
            throw new BusinessException(MeansResultCode.ASSET_CANT_EDIT);
        }

        // 编辑处理
        preEditHandle(asset, false);

        // 原始数据
        JSONObject assetData = originalAsset.getAssetData();
        JSONObject beforeAssetData = BeanUtil.copyProperties(assetData, JSONObject.class);
        assetData.putAll(asset.getAssetData());

        // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
        if (StrUtil.isNotEmpty(assetData.getString(USE_ORG)) ||
                StrUtil.isNotEmpty(assetData.getString(USE_PERSON))) {
            if (AssetConstant.ASSET_STATUS_IDLE.equals(originalAsset.getStatus())) {
                originalAsset.setStatus(AssetConstant.ASSET_STATUS_USING);
            }
        } else {
            if (AssetConstant.ASSET_STATUS_USING.equals(originalAsset.getStatus()) ||
                    AssetConstant.ASSET_STATUS_BORROW.equals(originalAsset.getStatus())) {
                originalAsset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
            }
        }

        originalAsset.setStandardId(asset.getStandardId());
        // 更新数据
        if (!this.updateById(originalAsset)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        //记录过期时间
        assetReportFieldService.processExpireTime(Collections.singletonList(originalAsset));

        // 记录履历
        FormVO formVO = formService.assetTpl();
        List<FormFieldCO> extField = standardService.getStandardExtField(formVO.getFormId(), asset.getStandardId());
        extField.addAll(formVO.getFormFields());
        List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formVO.getFormFields());
        assetUtil.translateAssetJsonView(beforeAssetData, translations);
        assetUtil.translateAssetJsonView(assetData, translations);
        String text = buildAssetLog(beforeAssetData, assetData, extField);

        // 写入资产履历
        AsAssetLog asAssetLog = new AsAssetLog()
                .setAssetId(asset.getId())
                .setActionType(AssetConstant.OPT_EDIT)
                .setActionName("编辑")
                .setHandleTime(LocalDateTimeUtil.now())
                .setActionContent(text)
                .setOriginalData(beforeAssetData)
                .setChangeData(assetData);
        assetLogService.save(asAssetLog);
        return true;
    }

    private static final String SEPARATOR = "；";

    @Override
    public String buildAssetLog(JSONObject before, JSONObject after, List<FormFieldCO> formFields) {
        StringBuffer text = new StringBuffer();
        for (FormFieldCO formField : formFields) {
            String fieldCode = formField.getFieldCode();
            String fieldName = formField.getFieldName();
            String fieldType = formField.getFieldType();
            // 过滤分割线和隐藏的属性
            if (formField.isHidden() || FormFieldCO.SPLIT_LINE.equals(fieldType)) {
                continue;
            }
            // 原始值
            Object beforeObject = before.get(fieldCode);
            String beforeValue;
            if (ListUtil.of(FormFieldCO.FILES, FormFieldCO.IMAGES, FormFieldCO.MULTI_SELECT_DROPDOWN)
                    .contains(formField.getFieldType())) {
                if (beforeObject instanceof List) {
                    beforeValue = Convert.toStr(before.getJSONArray(fieldCode), "空");
                } else {
                    beforeValue = "[]";
                }
            } else {
                beforeValue = StrUtil.isNotBlank(before.getString(fieldCode)) ? before.getString(fieldCode) : "空";
            }

            // 新值
            Object afterObject = after.get(fieldCode);
            String afterValue;
            if (ListUtil.of(FormFieldCO.FILES, FormFieldCO.IMAGES, FormFieldCO.MULTI_SELECT_DROPDOWN)
                    .contains(formField.getFieldType())) {
                if (afterObject instanceof List) {
                    afterValue = Convert.toStr(after.getJSONArray(fieldCode), "空");
                } else {
                    afterValue = "[]";
                }
            } else {
                afterValue = StrUtil.isNotBlank(after.getString(fieldCode)) ? after.getString(fieldCode) : "空";
            }

            if (!StrUtil.equals(afterValue, beforeValue)) {
                switch (fieldType) {
                    case FormFieldCO.IMAGES:
                    case FormFieldCO.FILES:
                        text.append(fieldName).append("被修改").append(SEPARATOR);
                        break;
                    case FormFieldCO.MULTI_SELECT_DROPDOWN:
                        text.append(fieldName).append("由“");
                        if ("空".equals(beforeValue) || "[]".equals(beforeValue)) {
                            text.append("空");
                        } else {
                            JSONArray jsonArray = before.getJSONArray(fieldCode);
                            text.append(String.join(",", jsonArray.toJavaList(String.class)));
                        }
                        text.append("”变成“");
                        if ("空".equals(afterValue) || "[]".equals(afterValue)) {
                            text.append("空");
                        } else {
                            JSONArray jsonArray = after.getJSONArray(fieldCode);
                            text.append(String.join(",", jsonArray.toJavaList(String.class)));
                        }
                        text.append("”")
                                .append(SEPARATOR);
                        break;
                    case FormFieldCO.DATETIME:
                        text.append(fieldName).append("由“");
                        if ("空".equals(beforeValue)) {
                            text.append(beforeValue);
                        } else {
                            String formatDate = beforeValue;
                            if (formField.getFieldProps().containsKey("dateFormatType")) {
                                Long date = Convert.toLong(beforeValue, 0L);
                                if (date > 0L) {
                                    try {
                                        String format = formField.getFieldProps().getString("dateFormatType");
                                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(date / 1000, 0, ZoneOffset.of("+8"));
                                        formatDate = dateTime.format(DateTimeFormatter.ofPattern(format));
                                    } catch (Exception e) {
                                        log.error("convert date {} error", formatDate);
                                    }
                                }
                            }
                            text.append(formatDate);
                        }
                        text.append("”变成“");
                        if ("空".equals(afterValue)) {
                            text.append(afterValue);
                        } else {
                            String formatDate = afterValue;
                            if (formField.getFieldProps().containsKey("dateFormatType")) {
                                Long date = Convert.toLong(afterValue, 0L);
                                if (date > 0L) {
                                    try {
                                        String format = formField.getFieldProps().getString("dateFormatType");
                                        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(date / 1000, 0, ZoneOffset.of("+8"));
                                        formatDate = dateTime.format(DateTimeFormatter.ofPattern(format));
                                    } catch (Exception e) {
                                        log.error("convert date {} error", formatDate);
                                    }
                                }
                            }
                            text.append(formatDate);
                        }
                        text.append("”")
                                .append(SEPARATOR);
                        break;
                    default:
                        text.append(fieldName)
                                .append("由“").append(beforeValue)
                                .append("”变成“").append(afterValue).append("”")
                                .append(SEPARATOR);
                        break;
                }

            }

        }
        if (text.length() > 0 && text.lastIndexOf(SEPARATOR) != -1) {
            return text.substring(0, text.lastIndexOf(SEPARATOR));
        } else {
            return text.toString();
        }
    }

    /**
     * 批量编辑
     *
     * @param assetBatchDto 资产信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuditableOperateResult> editBatch(AssetBatchDto assetBatchDto) {
        // 校验是否可编辑
        if (!canEdit(assetBatchDto.getIds())) {
            throw new BusinessException(MeansResultCode.ASSET_CANT_EDIT);
        }

        Long standardId = assetBatchDto.getAssetData().getLong(STANDARD_ID);
        AsAsset asset = new AsAsset();
        asset.setStandardId(standardId)
                .setAssetData(assetBatchDto.getAssetData());
        // 编辑处理
        preEditHandle(asset, true);
        // 过滤value是空的数据，为空不更新
        JSONObject convertJson = new JSONObject();
        asset.getAssetData().forEach((k, v) -> {
            if (!(StrUtil.isBlank(Convert.toStr(v))
                    || "[]".equals(Convert.toStr(v)))) {
                convertJson.put(k, v);
            }
        });
        // 清理额外属性
        FormVO formVO = formService.assetTpl();
        for (FormFieldCO f : formVO.getFormFields()) {
            Boolean unique = f.getFieldProps().getBoolean("unique");
            if (!f.isHidden() && BooleanUtil.isTrue(unique)) {
                if (convertJson.containsKey(f.getFieldCode())) {
                    throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, "属性" + f.getFieldName() + "存在唯一校验");
                }
            }
        }
        // 先保存更新数据
        List<AsAsset> assetList = this.listByIds(assetBatchDto.getIds());
        Map<Long, JSONObject> beforeAssetDataMap = new HashMap<>();
        for (AsAsset asAsset : assetList) {
            JSONObject assetData = asAsset.getAssetData();
            JSONObject beforeAssetData = BeanUtil.copyProperties(assetData, JSONObject.class);
            beforeAssetDataMap.put(asAsset.getId(), beforeAssetData);
            assetData.putAll(convertJson);
            asAsset.setStandardId(standardId)
                    .setAssetData(asAsset.getAssetData());
            // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
            if (StrUtil.isNotEmpty(assetData.getString(USE_ORG)) ||
                    StrUtil.isNotEmpty(assetData.getString(USE_PERSON))) {
                if (AssetConstant.ASSET_STATUS_IDLE.equals(asAsset.getStatus())) {
                    asAsset.setStatus(AssetConstant.ASSET_STATUS_USING);
                }
            } else {
                if (AssetConstant.ASSET_STATUS_USING.equals(asAsset.getStatus()) ||
                        AssetConstant.ASSET_STATUS_BORROW.equals(asAsset.getStatus())) {
                    asAsset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
                }
            }
        }
        this.updateBatchById(assetList);

        //记录过期时间
        assetReportFieldService.processExpireTime(assetList);

        // 计算履历并保持
        List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formVO.getFormFields());
        List<AsAssetLog> logList = new ArrayList<>();
        for (AsAsset asAsset : assetList) {
            JSONObject beforeAssetData = beforeAssetDataMap.get(asAsset.getId());
            assetUtil.translateAssetJsonView(beforeAssetData, translations);
            assetUtil.translateAssetJsonView(asAsset.getAssetData(), translations);
            String text = buildAssetLog(beforeAssetData, asAsset.getAssetData(), formVO.getFormFields());
            AsAssetLog asAssetLog = new AsAssetLog()
                    .setAssetId(asAsset.getId())
                    .setActionType(AssetConstant.OPT_EDIT)
                    .setActionName("批量编辑")
                    .setHandleTime(LocalDateTimeUtil.now())
                    .setActionContent(text)
                    .setOriginalData(beforeAssetData)
                    .setChangeData(asAsset.getAssetData());
            logList.add(asAssetLog);
        }
        assetLogService.saveBatch(logList);
        return assetList.stream().map(v -> new AuditableOperateResult(v.getAssetCode(), v.getAssetName())).collect(toList());
    }

    /**
     * 复制资产
     *
     * @param copyDto 实体
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuditableOperateResult> copy(AssetCopyDto copyDto) {
        AsAsset asset = this.getOne(new QueryWrapper<AsAsset>().lambda()
                .eq(AsAsset::getId, copyDto.getId()));
        if (isEmpty(asset)) {
            throw new BusinessException(MeansResultCode.ASSET_NOT_EXISTS);
        }
        List<Long> result = new ArrayList<>();
        List<AsAsset> copyAsset = new ArrayList<>();
        List<AsAssetLog> assetLogList = new ArrayList<>();

        // 处理字段
        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        Long standardId = asset.getStandardId();
        List<FormFieldCO> standardExtField = standardService.getStandardExtField(formVO.getFormId(), standardId);
        formFields.addAll(standardExtField);

        List<FormFieldCO> copyField = new ArrayList<>();
        String assetCode = "assetCode";
        for (FormFieldCO formField : formFields) {
            if (FormFieldCO.YZC_ASSET_SERIALNO.equals(formField.getFieldType())) {
                assetCode = formField.getFieldCode();
            }
            if (!formField.isHidden()) {
                if (formField.requiredProps()) {
                    if (BooleanUtil.isTrue(formField.getFieldProps().getBoolean("unique"))) {
                        throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, "属性" + formField.getFieldName() + "存在唯一校验");
                    }
                    copyField.add(formField);
                } else if (copyDto.getFieldList().contains(formField.getFieldCode())) {
                    if (BooleanUtil.isTrue(formField.getFieldProps().getBoolean("unique"))) {
                        throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, "属性" + formField.getFieldName() + "存在唯一校验");
                    }
                    copyField.add(formField);
                }
            }
        }

        JSONObject assetData = asset.getAssetData();
        FormFieldConvert.clearInvalidField(assetData, copyField);

        int assetStatus = AssetConstant.ASSET_STATUS_IDLE;
        if ((assetData.containsKey(USE_ORG) && StrUtil.isNotBlank(assetData.getString(USE_ORG)))
                || (assetData.containsKey(USE_PERSON) && StrUtil.isNotBlank(assetData.getString(USE_PERSON)))) {
            assetStatus = AssetConstant.ASSET_STATUS_USING;
        }

        for (int i = 0; i < copyDto.getCopyNum(); i++) {
            Long id = IdUtils.getId();
            result.add(id);

            AsAsset cpy = new AsAsset();
            // 重置资产状态
            cpy.setId(id);
            cpy.setStatus(assetStatus);
            cpy.setAssetData(BeanUtil.copyProperties(assetData, JSONObject.class));
            cpy.setStandardId(standardId);
            copyAsset.add(cpy);
            // 写入资产履历
            AsAssetLog asAssetLog = new AsAssetLog().setAssetId(id)
                    .setActionType(AssetConstant.OPT_ADD)
                    .setHandleTime(LocalDateTime.now())
                    .setActionName("入库")
                    .setActionContent("批量复制");
            assetLogList.add(asAssetLog);
        }

        // 添加业务流水号
        List<JSONObject> collect = copyAsset.stream().map(AsAsset::getAssetData).collect(toList());
        String finalAssetCode = assetCode;
        collect.forEach(f -> f.put(finalAssetCode, "系统自动生成"));
        setSerialNo(formVO, collect);
        if (formService.uniqueSerialNo(collect, formVO, AsFormService.BIZ_TYPE_ASSET)) {
            boolean loop = true;
            int maxTime = 50;
            while (loop && maxTime > 0) {
                collect.forEach(f -> f.put(finalAssetCode, "系统自动生成"));
                setSerialNo(formVO, collect);
                if (!formService.uniqueSerialNo(collect, formVO, AsFormService.BIZ_TYPE_ASSET)) {
                    loop = false;
                } else {
                    maxTime--;
                }
            }
            if (BooleanUtil.isTrue(loop)) {
                throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, "编码自动生成异常，请重试");
            }
        }
        this.saveBatch(copyAsset);

        //记录过期时间
        assetReportFieldService.processExpireTime(copyAsset);

        assetLogService.saveBatch(assetLogList);
        EventPublishHandler.publish(new AddMeansEvent(StoreMode.COPY, IdUtils.getId(), Lists.newArrayList(copyAsset)));
        return copyAsset.stream().map(v -> new AuditableOperateResult(v.getId(), v.getAssetData().getString("assetCode"), v.getAssetData().getString("assetName"))).collect(toList());
    }

    /**
     * 根据资产状态获取对应的操作
     *
     * @param ids 资产ids
     * @return 对应的操作list
     */
    @Override
    public List<AssetOperationDto> getAssetOptByAssetId(List<Long> ids) {
        // 根据资产ids获取对应的状态id
        List<AsAsset> asAssets = this.listByIds(ids);
        if (CollUtil.isEmpty(asAssets)) {
            return ListUtil.empty();
        }
        long orgOwnerEmptyCount = asAssets.stream().filter(asset -> ObjectUtil.isEmpty(asset.getOrgOwner())).count();
        // 状态id去重
        List<Integer> statusIds = asAssets.stream().map(AsAsset::getStatus).distinct().collect(Collectors.toList());
        // 查询资产操作
        Collection<AssetOperationDto> filterOperation = null;
        // 遍历获取单个操作取交集操作
        for (Integer statusId : statusIds) {
            if (filterOperation == null) {
                filterOperation = assetOperationMapper.getAssetOperationByStatus(ListUtil.toList(statusId));
            } else {
                // 取交集
                filterOperation = CollectionUtil.intersection(filterOperation, assetOperationMapper.getAssetOperationByStatus(ListUtil.toList(statusId)));
            }
        }
        if (filterOperation == null) {
            filterOperation = new ArrayList<>();
        }
        List<String> filter = filterOperation.stream().map(AssetOperationDto::getCode).collect(Collectors.toList());
        // 查询全部操作
        List<AsAssetOperation> allOperation = assetOperationMapper.selectList(new QueryWrapper<AsAssetOperation>()
                .lambda().ne(AsAssetOperation::getCode, "asset_add"));
        return allOperation.stream().map(opt -> {
            AssetOperationDto assetOpt = BeanUtil.copyProperties(opt, AssetOperationDto.class);
            if (!filter.contains(assetOpt.getCode())) {
                assetOpt.setIsDisable(true);
            }
            if (AssetConstant.ASSET_OPERATION_ALLOT.equals(opt.getCode()) && orgOwnerEmptyCount > 0) {
                assetOpt.setIsDisable(true);
            }
            return assetOpt;
        }).collect(Collectors.toList());
    }

    /**
     * 查询全部操作
     *
     * @return 结果
     */
    @Override
    public List<AssetOperationDto> allAssetOpt() {
        // 查询全部状态
        List<AssetStatusDto> allStatus = assetStatusMapper.allStatus();
        allStatus.forEach(st -> st.setIsDisable(true));
        // 查询全部操作
        List<AssetOperationDto> assetOperationList = assetOperationMapper.allAssetOpt();
        // 批量写入全部状态信息
        assetOperationList.forEach(opt -> {
            AssetStatusDto[] arrays = new AssetStatusDto[allStatus.size()];
            List<AssetStatusDto> statusList = opt.getAssetStatusList();
            Map<String, AssetStatusDto> map = statusList.stream().collect(
                    Collectors.toMap(AssetStatusDto::getCode, filed -> filed, (k1, k2) -> k1));
            for (int i = 0, len = allStatus.size(); i < len; i++) {
                if (map.containsKey(allStatus.get(i).getCode())) {
                    arrays[i] = map.get(allStatus.get(i).getCode());
                } else {
                    arrays[i] = BeanUtil.copyProperties(allStatus.get(i), AssetStatusDto.class);
                }
            }
            opt.setAssetStatusList(ListUtil.toList(arrays));
        });
        // 添加全部资产
        allStatus.forEach(st -> st.setIsDisable(false));
        assetOperationList.add(0, new AssetOperationDto().setId(0)
                .setName("全部资产").setCode("all")
                .setAssetStatusList(allStatus));
        return assetOperationList;
    }

    /**
     * 金额统计
     *
     * @param queryDto 查询条件
     * @return 结果
     */
    @Override
    public String amountMoney(AssetQueryConditionDto queryDto) {
        String conditions = conditionResolver.resolveQueryCondition("a", queryDto.getConditions());
        transformKwFields(queryDto);
        return this.getBaseMapper().amountMoney(queryDto, conditions);
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importErrorDto.getTaskId());
        importError.setErrorNum(0);
        importError.setImportType(DictConstant.IMPORT_TYPE_ASSET);
        importError.setExcelJson(importErrorDto.getHeadModelList());
        this.assetImportErrorService.save(importError);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSheetData(AssetImportDto importDto) {
        List<AssetImportDto.FieldData> fieldDataList = importDto.getFieldDataList();
        JSONObject assetData = new JSONObject();
        List<LuckySheetModel> luckySheetModels = new ArrayList<>();
        Map<String, AssetImportDto.FieldData> fieldDataMap = new HashMap<>();
        for (AssetImportDto.FieldData fieldData : fieldDataList) {
            if (fieldData.getTarget() != null
                    && importDto.getAssetData().containsKey(fieldData.getFieldCode())) {
                assetData.put(fieldData.getFieldCode(), fieldData.getTarget());
            }
            fieldDataMap.put(fieldData.getFieldCode(), fieldData);
        }

        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_ASSET);
        String assetCode = "assetCode";
        for (FormFieldCO formField : formVO.getFormFields()) {
            if (FormFieldCO.YZC_ASSET_SERIALNO.equals(formField.getFieldType())) {
                assetCode = formField.getFieldCode();
            }
        }

        // 表单校验
        List<FormFieldCO> assetField = importDto.getFormFieldMap().get("asset");
        List<FormFieldCO> standardField = importDto.getFormFieldMap().get("standard");

        // 格式化json
        List<FormFieldCO> formFieldCOList = new ArrayList<>(assetField);
        formFieldCOList.addAll(standardField);
        formService.importValidator(assetData, assetField, standardField, fieldDataMap, AsFormService.BIZ_TYPE_ASSET);

        int errorNum = 0;
        for (int i = 0; i < fieldDataList.size(); i++) {
            AssetImportDto.FieldData fieldData = fieldDataList.get(i);
            // LuckySheet表格数据
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(importDto.getRowNum()).setC(i);

            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(Convert.toStr(fieldData.getSource()));
            if (fieldData.getErrMsg().size() > 0) {
                List<String> collect = fieldData.getErrMsg().stream().distinct().collect(toList());
                String errMsg = String.join("，", collect);
                // 错误单元格标记红色
                modelV.setFc(ERROR_COLOR);
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment(errMsg);
                modelV.setPs(comment);
                errorNum++;
            }
            luckySheetModels.add(luckySheetModel);
        }

        importDto.setErrorNum(errorNum);

        if (importDto.getErrorNum() == 0) {
            List<JSONObject> collect = ListUtil.of(assetData);
            String finalAssetCode = assetCode;
            boolean hasAutoGen = setSerialNo(formVO, collect);
            if (hasAutoGen
                    && formService.uniqueSerialNo(collect, formVO, AsFormService.BIZ_TYPE_ASSET)) {
                boolean loop = true;
                int maxTime = 50;
                while (loop && maxTime > 0) {
                    collect.forEach(f -> f.put(finalAssetCode, "系统自动生成"));
                    setSerialNo(formVO, collect);
                    if (!formService.uniqueSerialNo(collect, formVO, AsFormService.BIZ_TYPE_ASSET)) {
                        loop = false;
                    } else {
                        maxTime--;
                    }
                }
                if (BooleanUtil.isTrue(loop)) {
                    Map<Integer, LuckySheetModel> luckySheetMap = luckySheetModels.stream()
                            .collect(Collectors.toMap(LuckySheetModel::getC, k -> k));
                    for (int i = 0; i < fieldDataList.size(); i++) {
                        AssetImportDto.FieldData fieldData = fieldDataList.get(i);
                        if (assetCode.equals(fieldData.getFieldCode())) {
                            LuckySheetModel model = luckySheetMap.get(i);
                            LuckySheetModel.Value modelV = model.getV();
                            // 错误单元格标记红色
                            modelV.setFc(ERROR_COLOR);
                            LuckySheetModel.Comment comment = new LuckySheetModel.Comment("编码自动生成异常，请重试");
                            modelV.setPs(comment);
                            errorNum++;
                            break;
                        }
                    }
                    importDto.setErrorNum(errorNum);
                }
            }
        }
        // 导入成功
        if (importDto.getErrorNum() == 0) {
            FormFieldConvert.formatJsonField(assetData, formFieldCOList);
            AsAsset asset = new AsAsset();
            Long id = IdUtils.getId();
            asset.setId(id);
            asset.setAssetData(assetData);
            asset.setStandardId(importDto.getStandardId());
            asset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
            // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
            if ((assetData.containsKey(USE_ORG) && StrUtil.isNotBlank(assetData.getString(USE_ORG)))
                    || (assetData.containsKey(USE_PERSON) && StrUtil.isNotBlank(assetData.getString(USE_PERSON)))) {
                asset.setStatus(AssetConstant.ASSET_STATUS_USING);
            }
            this.save(asset);

            //记录过期时间
            assetReportFieldService.processExpireTime(Collections.singletonList(asset));

            // 写入履历
            AsAssetLog asAssetLog = new AsAssetLog().setAssetId(id)
                    .setActionType(AssetConstant.OPT_ADD)
                    .setHandleTime(LocalDateTime.now())
                    .setActionName("入库")
                    .setActionContent("批量导入");
            assetLogService.save(asAssetLog);
            redisService.hIncr(RedisConstant.companyImportKey("asset", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("asset", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            DataSnapshots.saveMeansData(importDto.getTaskId(), formFieldCOList, Collections.singletonList(asset.translate()));
            return true;
        } else { // 导入失败
            AsAssetImportError importError = copyToAsAssetImportError(importDto, luckySheetModels);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("asset", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }

    private boolean canEdit(List<Long> assetIds) {
        // 校验是否可编辑
        List<AssetOperationDto> assetOpt = getAssetOptByAssetId(assetIds);
        Optional<AssetOperationDto> first = assetOpt.stream().filter(it -> AssetConstant.OPT_EDIT.equals(Convert.toInt(it.getOrderType())))
                .findFirst();
        if (first.isPresent()) {
            AssetOperationDto assetOperationDto = first.get();
            return !BooleanUtil.isTrue(assetOperationDto.getIsDisable());
        } else {
            return false;
        }
    }

    @Override
    public Boolean saveEditSheetData(AssetImportDto importDto) {
        String assetSerialNoCode = "assetCode";
        // 判断是否库里存在
        AsAsset originalAsset = null;
        if (StrUtil.isNotEmpty(importDto.getAssetData().getString(assetSerialNoCode))) {
            originalAsset = this.getInfoByCode(importDto.getAssetData().getString(assetSerialNoCode), LoginUserThreadLocal.getCompanyId());
        }

        List<AssetImportDto.FieldData> fieldDataList = importDto.getFieldDataList();
        JSONObject updateAssetData = new JSONObject();
        List<LuckySheetModel> luckySheetModels = new ArrayList<>();
        Map<String, AssetImportDto.FieldData> fieldDataMap = new HashMap<>();
        List<String> removeKey = new ArrayList<>();
        for (AssetImportDto.FieldData fieldData : fieldDataList) {
            String target = Convert.toStr(fieldData.getTarget());
            if (StrUtil.isNotEmpty(target) && !"[]".equals(target)) {
                if ("删除".equals(fieldData.getTarget())) {
                    if (!assetSerialNoCode.equals(fieldData.getFieldCode())) {
                        removeKey.add(fieldData.getFieldCode());
                    }
                } else {
                    if (importDto.getAssetData().containsKey(fieldData.getFieldCode())) {
                        updateAssetData.put(fieldData.getFieldCode(), fieldData.getTarget());
                    }
                }
            }
            if (assetSerialNoCode.equals(fieldData.getFieldCode())) {
                if (ObjectUtil.isNull(originalAsset)) {
                    fieldData.setErrMsg(ListUtil.of("资产编码不存在"));
                } else {
                    boolean canEdit = canEdit(ListUtil.of(originalAsset.getId()));
                    if (!canEdit) {
                        fieldData.setErrMsg(ListUtil.of("当前资产不可编辑"));
                    }
                }
            }

            fieldDataMap.put(fieldData.getFieldCode(), fieldData);
        }

        // 处理使用组织
        // 填写了使用人没有填写使用组织时，使用组织也需要跟随变更
        if (StrUtil.isNotBlank(updateAssetData.getString(USE_PERSON))) {
            List<Long> orgIds = systemOrgService.getEmpOrgIds(updateAssetData.getLong(USE_PERSON));
            if (StrUtil.isBlank(updateAssetData.getString(USE_ORG))) {
                updateAssetData.put(USE_ORG, orgIds.get(0));
            } else {
                if (!orgIds.contains(updateAssetData.getLong(USE_ORG))) {
                    fieldDataList.forEach(v -> {
                        if (v.getFieldCode().equals(USE_ORG)) {
                            v.setErrMsg(ListUtil.of("当前使用组织与使用人对应的组织不匹配，请检查"));
                        }
                    });
                }
            }
        }

        // 表单校验
        FormVO formVO = formService.assetTpl();
        List<FormFieldCO> assetField = importDto.getFormFieldMap().get("asset");
        if (ObjectUtil.isNotNull(originalAsset)) {
            formService.importEditValidator(updateAssetData, assetField, fieldDataMap, formVO.getFormId(), originalAsset.getId(), AsFormService.BIZ_TYPE_ASSET);
            // 校验删除字段必填项
            if (CollUtil.isNotEmpty(removeKey)) {
                JSONObject json = new JSONObject();
                List<FormFieldCO> removeField = assetField.stream()
                        .filter(f -> removeKey.contains(f.getFieldCode()))
                        .peek(f -> {
                            if (FormFieldCO.MULTI_SELECT_DROPDOWN.equals(f.getFieldType())
                                    || FormFieldCO.IMAGES.equals(f.getFieldType())
                                    || FormFieldCO.FILES.equals(f.getFieldType())) {
                                json.put(f.getFieldCode(), ListUtil.empty());
                            }
                        }).collect(toList());
                formService.importValidator(json, removeField,
                        ListUtil.empty(), fieldDataMap, AsFormService.BIZ_TYPE_ASSET);
                // 重写错误内容
                removeKey.forEach(r -> {
                    if (fieldDataMap.containsKey(r)) {
                        AssetImportDto.FieldData fieldData = fieldDataMap.get(r);
                        if (CollUtil.isNotEmpty(fieldData.getErrMsg())) {
                            fieldData.setErrMsg(ListUtil.of("必填项不可删除"));
                        }
                    }
                });
            }
        }
        int errorNum = 0;
        for (int i = 0; i < fieldDataList.size(); i++) {
            AssetImportDto.FieldData fieldData = fieldDataList.get(i);
            // LuckySheet表格数据
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(importDto.getRowNum()).setC(i);

            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(Convert.toStr(fieldData.getSource()));
            if (fieldData.getErrMsg().size() > 0) {
                List<String> collect = fieldData.getErrMsg().stream().distinct().collect(toList());
                String errMsg = String.join("，", collect);
                // 错误单元格标记红色
                modelV.setFc(ERROR_COLOR);
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment(errMsg);
                modelV.setPs(comment);
                errorNum++;
            }
            luckySheetModels.add(luckySheetModel);
        }
        importDto.setErrorNum(errorNum);
        if (importDto.getErrorNum() == 0) {
            FormFieldConvert.formatJsonField(updateAssetData, assetField);
            // 原始数据
            JSONObject assetData = originalAsset.getAssetData();
            JSONObject beforeAssetData = BeanUtil.copyProperties(assetData, JSONObject.class);
            assetData.putAll(updateAssetData);
            removeKey.forEach(assetData::remove);
            // 校验是否有使用组织或者使用人，存在写入资产状态 ———— 在用
            if (StrUtil.isNotEmpty(assetData.getString(USE_ORG)) ||
                    StrUtil.isNotEmpty(assetData.getString(USE_PERSON))) {
                if (AssetConstant.ASSET_STATUS_IDLE.equals(originalAsset.getStatus())) {
                    originalAsset.setStatus(AssetConstant.ASSET_STATUS_USING);
                }
            } else {
                if (AssetConstant.ASSET_STATUS_USING.equals(originalAsset.getStatus()) ||
                        AssetConstant.ASSET_STATUS_BORROW.equals(originalAsset.getStatus())) {
                    originalAsset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
                }
            }
            // 更新数据
            if (!this.updateById(originalAsset)) {
                throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
            }

            //记录过期时间
            assetReportFieldService.processExpireTime(Collections.singletonList(originalAsset));

            // 记录履历
            List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formVO.getFormFields());
            assetUtil.translateAssetJsonView(beforeAssetData, translations);
            assetUtil.translateAssetJsonView(assetData, translations);
            String text = buildAssetLog(beforeAssetData, assetData, formVO.getFormFields());

            // 写入资产履历
            AsAssetLog asAssetLog = new AsAssetLog()
                    .setAssetId(originalAsset.getId())
                    .setActionType(AssetConstant.OPT_EDIT)
                    .setActionName("批量导入编辑")
                    .setHandleTime(LocalDateTimeUtil.now())
                    .setActionContent(text)
                    .setOriginalData(beforeAssetData)
                    .setChangeData(assetData);
            assetLogService.save(asAssetLog);
            redisService.hIncr(RedisConstant.companyImportKey("asset_edit", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("asset_edit", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            return true;
        } else { // 导入失败
            AsAssetImportError importError = copyToAsAssetImportError(importDto, luckySheetModels);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("asset_edit", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }

    private AsAssetImportError copyToAsAssetImportError(AssetImportDto importDto, List<LuckySheetModel> rows) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_ASSET);
        importError.setExcelJson(rows);
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }

    /**
     * 删除资产信息
     *
     * @param assetId 资产ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditableOperateResult removeByAssetId(Long assetId) {
        AsAsset byId = this.getById(assetId);
        if (ObjectUtil.isNull(byId)) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }
        //状态判断不允许删除，即进行中的不允许删除
        Integer status = byId.getStatus();
        List<AssetOperationDto> assetOperationByStatus = assetOperationMapper.getAssetOperationByStatus(ListUtil.toList(status));
        List<Integer> statusList = assetOperationByStatus.stream().map(a -> Convert.toInt(a.getOrderType())).collect(Collectors.toList());
        if (!statusList.contains(AssetConstant.OPT_DELETE)) {
            throw new BusinessException(SystemResultCode.ASSET_NOT_ALLOW_DELETE);
        }

        //校验资产是否有入账信息，有入账信息，不允许删除
        AssetFinanceInfoDto financeInfoDto = assetFinanceInfoAbs.queryAssetFinanceInfo(assetId);
        if (Objects.nonNull(financeInfoDto)) {
            throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-资产存在入账信息");
        }
        //校验是否有进行中或已驳回的盘点任务
        Integer processInventory = inventoryAbs.getProcessInventory(assetId);
        if (processInventory > 0) {
            throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-资产存在未完成的盘点单");
        }
        //设备保养任务
        Integer maintainTaskCount = equipmentMaintainTaskAbs.countMaintainTask(assetId);
        if (Objects.nonNull(maintainTaskCount) && maintainTaskCount > 0) {
            throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-请先结束设备保养任务后再操作");
        }

        //删除资产关联关系
        assetRelationService.removeRelation(assetId);
        //删除关联备件
        equipmentMaintainTaskAbs.removeSpareParts(assetId);

        if (!this.removeById(assetId)) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }
        return new AuditableOperateResult(byId.getAssetCode(), byId.getAssetName());
    }

    /**
     * 批量删除资产信息
     *
     * @param assetIds 资产IDs
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuditableOperateResult> deleteBatch(List<Long> assetIds) {
        // 查询待删除的资产信息
        List<AsAsset> assets = this.listByIds(assetIds);
        if (CollUtil.isEmpty(assets)) {
            return ListUtil.empty();
        }
        List<Integer> allAssetStatus = assets.stream()
                .map(AsAsset::getStatus)
                .distinct().collect(Collectors.toList());
        //状态判断不允许删除，即进行中的不允许删除
        List<AssetOperationDto> assetOperationByStatus = assetOperationMapper.getAssetOperationByStatus(allAssetStatus);
        List<Integer> statusList = assetOperationByStatus.stream().map(a -> Convert.toInt(a.getOrderType())).collect(Collectors.toList());
        if (!statusList.contains(AssetConstant.OPT_DELETE)) {
            throw new BusinessException(SystemResultCode.ASSET_NOT_ALLOW_DELETE);
        }

        //校验资产是否有入账信息，有入账信息，不允许删除
        assetIds.forEach(item -> {
            AssetFinanceInfoDto financeInfoDto = assetFinanceInfoAbs.queryAssetFinanceInfo(item);
            if (Objects.nonNull(financeInfoDto)) {
                throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-资产存在入账信息");
            }

            //设备保养任务
            Integer maintainTaskCount = equipmentMaintainTaskAbs.countMaintainTask(item);
            if (Objects.nonNull(maintainTaskCount) && maintainTaskCount > 0) {
                throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-请先结束设备保养任务后再操作");
            }
        });

        //校验是否有进行中或已驳回的盘点任务
        assetIds.forEach(item -> {
            Integer processInventory = inventoryAbs.getProcessInventory(item);
            if (processInventory > 0) {
                throw new BusinessException(SystemResultCode.REMOVE_ERROR, "-资产存在未完成的盘点单");
            }
        });
        //删除资产关联关系
        assetIds.forEach(item -> {
            //删除关联资产
            assetRelationService.removeRelation(item);
            //删除备件
            equipmentMaintainTaskAbs.removeSpareParts(item);
        });

        if (!this.removeByIds(assetIds)) {
            throw new BusinessException(SystemResultCode.OPT_DELETE_FAIL);
        }
        return assets.stream().map(v -> new AuditableOperateResult(v.getAssetCode(), v.getAssetName())).collect(toList());
    }

    /**
     * 分组统计资产数量
     *
     * @return 资产数量
     */
    @Override
    public List<InventoryDispatchGroupAssetDto> selectAssetNumGroup(String groupByStr, List<Long> assetIds) {
        return this.getBaseMapper().selectAssetNumGroup(groupByStr, assetIds, LoginUserThreadLocal.getCompanyId());
    }

    /**
     * 统计当天删除资产数
     *
     * @param
     * @return 删除资产数
     */
    @Override
    public Integer countDeleteNum(String beginTime, String endTime) {
        return this.getBaseMapper().countDeleteNum(beginTime, endTime);
    }

    /**
     * 查询资产数据（不带权限)
     *
     * @param
     * @return 删除资产数
     */
    @Override
    public List<AsAsset> listByIdsNoPerm(List<Long> assetIdList) {
        return this.getBaseMapper().listByIdsNoPerm(assetIdList, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<AsAssetImportError> list = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, taskId)
                .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_ASSET));
        return list.stream().map(AsAssetImportError::getExcelJson)
                .collect(Collectors.toList());
    }

    @Override
    public AsAsset getInfoPerm(Long id) {
        List<AsAsset> assets = listByIds(ListUtil.of(id));
        return CollUtil.isNotEmpty(assets) ? assets.get(0) : null;
    }

    @Override
    public AsAsset getInRecycleBin(Long id) {
        return this.getBaseMapper().getInRecycleBin(id);
    }

    /**
     * 查询获取资产信息
     *
     * @param id 资产ID
     * @return AsAsset信息
     */
    @Override
    public AsAsset getInfoNoPerm(Long id) {
        return getById(id);
    }

    @Override
    public AsAsset getInfoNoPermByCodePhp(String assetCode) {
        FormVO formVO = formService.assetTpl();
        List<String> uniqueCodes = formVO.getFormFields().stream()
                .filter(f -> {
                    if (f.getFieldType().equals(FormFieldCO.YZC_ASSET_SERIALNO)) {
                        return true;
                    }
                    return f.getFieldProps().containsKey("unique") ? f.getFieldProps().getBoolean("unique") : false;
                }).map(FormFieldCO::getFieldCode).collect(toList());
        return this.getBaseMapper().getInfoNoPermByCodePhp(assetCode, uniqueCodes, LoginUserThreadLocal.getCompanyId());
    }

    @Override
    public AsAsset getInfoByCode(String assetCode, Long companyId) {
        return this.getBaseMapper().getByCodeNoPerm(assetCode, companyId);
    }

    @Override
    public List<AsAsset> quickSearch(AssetQuickSearchDto quickSearchDto) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        return this.getBaseMapper().quickSearch(quickSearchDto, companyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchStatus(List<Long> ids, Consumer<AsAsset> consumer) {
        List<AsAsset> assets = this.listByIds(ids);
        assets.forEach(consumer);
        return this.updateBatchById(assets);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editAssetUseOrg(Long changeId, Long userId, List<CusEmployeeTransferDto> transfers, List<AsOrg> orgList) {
        return editAssetUseOrg(changeId, userId, LoginUserThreadLocal.getCompanyId(), transfers, orgList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editAssetUseOrg(Long changeId, Long userId, Long companyId, List<CusEmployeeTransferDto> transfers, List<AsOrg> orgList) {
        if (CollUtil.isEmpty(transfers)) {
            return true;
        }
        // 查询资产使用人是该员工的所有资产列表
        List<AssetDto> asAssets = getBaseMapper().selectUseAsset(userId, companyId);
        if (CollUtil.isEmpty(asAssets)) {
            return true;
        }

        Map<Long, AsOrg> orgMap = orgList.stream().collect(Collectors.toMap(AsOrg::getId, k -> k, (k1, k2) -> k1));
        transfers.forEach(it -> {
            // 查询变更记录
            List<AssetDto> assetList = this.getBaseMapper().selectUseOrg(userId, it.getFrom(), companyId);
            // 写入资产变更记录
            List<AsEmployeeAssetChange> changeList = new ArrayList<>();
            // 写入资产履历记录
            List<AsAssetLog> assetLogList = new ArrayList<>();
            for (AssetDto assetDto : assetList) {
                AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
                assetChange.setChangeId(changeId)
                        .setAssetId(assetDto.getId())
                        .setAssetSnapshotData(assetDto.getAssetData())
                        .setChangeOrgType(1);
                changeList.add(assetChange);

                AsOrg fromOrg = orgMap.get(it.getFrom());
                String fromOrgNameAndCode;
                if (fromOrg == null) {
                    fromOrgNameAndCode = cacheResourceUtil.getOrgNameAndCode(it.getFrom());
                } else {
                    fromOrgNameAndCode = fromOrg.getOrgName() + (StrUtil.isNotEmpty(fromOrg.getOrgCode()) ? "（" + fromOrg.getOrgCode() + "）" : "");
                }

                AsOrg toOrg = orgMap.get(it.getTo());
                String toOrgNameAndCode = "";
                if (toOrg == null) {
                    toOrgNameAndCode = cacheResourceUtil.getOrgNameAndCode(it.getTo());
                } else {
                    toOrgNameAndCode = toOrg.getOrgName() + (StrUtil.isNotEmpty(toOrg.getOrgCode()) ? "（" + toOrg.getOrgCode() + "）" : "");
                }
                String content = "使用组织：" + fromOrgNameAndCode + "变成" + toOrgNameAndCode;

                AsAssetLog asAssetLog = new AsAssetLog().setAssetId(assetDto.getId())
                        .setActionType(AssetConstant.OPT_ORG_CHANGE)
                        .setHandleTime(LocalDateTime.now())
                        .setActionName("员工异动")
                        .setCompanyId(companyId)
                        .setActionContent(content);
                assetLogList.add(asAssetLog);
            }
            assetLogService.saveBatch(assetLogList);
            employeeAssetChangeService.saveBatch(changeList);
            // 变更资产
            this.getBaseMapper().updateUseOrg(userId, it.getFrom(), it.getTo(), companyId);
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AsAssetLog> assetManageTransfer(Long removeUserId, DataTransfer assetUnderManagement, Long changeId) {
        // 管理的资产
        List<AssetDto> owner = getBaseMapper().selectOwnerAsset(removeUserId, LoginUserThreadLocal.getCompanyId());
        List<AsAsset> ownerAsset = new ArrayList<>();
        List<AsEmployeeAssetChange> assetChanges = new ArrayList<>();
        List<AsAssetLog> assetLogList = new ArrayList<>();
        for (AssetDto assetDto : owner) {
            // 更新资产
            AsAsset asAsset = new AsAsset().setId(assetDto.getId()).setAssetData(assetDto.getAssetData()).setStandardId(assetDto.getStandardId()).setCreateTime(assetDto.getCreateTime());
            JSONObject data = asAsset.getAssetData();
            data.put("status", assetDto.getStatus());

            // 记录原始管理组织
            Long managerOwner = data.getLong("managerOwner");
            String managerOwnerName = cacheResourceUtil.getUserNameAndCode(managerOwner);
            String targetManagerOwnerName = cacheResourceUtil.getUserNameAndCode(assetUnderManagement.getReceiveEmployeeId());
            String content = "所属管理员由" + managerOwnerName + "变成" + targetManagerOwnerName;
            AsAssetLog assetLog = new AsAssetLog().setAssetId(assetDto.getId()).setActionContent(content);
            assetLogList.add(assetLog);

            // 更新管理组织
            data.put("managerOwner", assetUnderManagement.getReceiveEmployeeId() != null ? Convert.toStr(assetUnderManagement.getReceiveEmployeeId()) : StrUtil.EMPTY);
            ownerAsset.add(asAsset);

            // 写入记录
            AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
            assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(changeId).setChangeOrgType(2);
            assetChanges.add(assetChange);
        }

        // 更新
        if (!ownerAsset.isEmpty()) {
            this.updateBatchById(ownerAsset);

            //记录过期时间
            assetReportFieldService.processExpireTime(ownerAsset);

            this.employeeAssetChangeService.saveBatch(assetChanges);
        }
        return assetLogList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AsAssetLog> assetUseTransfer(Long removeUserId, Boolean toIdle, DataTransfer assetUser, Long changeId) {
        // 使用的资产
        List<AssetDto> use = getBaseMapper().selectUseAsset(removeUserId, LoginUserThreadLocal.getCompanyId());
        List<AsAsset> useAssets = new ArrayList<>();
        List<AsEmployeeAssetChange> assetChanges = new ArrayList<>();
        List<AsAssetLog> assetLogList = new ArrayList<>();
        List<Integer> status = Arrays.asList(AssetConstant.ASSET_STATUS_USING, AssetConstant.ASSET_STATUS_BORROW);
        for (AssetDto assetDto : use) {
            // 更新资产
            AsAsset asAsset = new AsAsset().setId(assetDto.getId()).setAssetData(assetDto.getAssetData()).setStandardId(assetDto.getStandardId()).setCreateTime(assetDto.getCreateTime());
            JSONObject data = asAsset.getAssetData();
            data.put("status", assetDto.getStatus());
            // 资产置为闲置
            if (Objects.nonNull(toIdle) && toIdle && status.contains(assetDto.getStatus())) {
                asAsset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
                // 记录使用组织，使用人
                String content = "转移资产时将使用的资产置为闲置";
                AsAssetLog assetLog = new AsAssetLog().setAssetId(assetDto.getId()).setActionContent(content);
                assetLogList.add(assetLog);

                data.put(USE_ORG, StrUtil.EMPTY);
                data.put(USE_PERSON, StrUtil.EMPTY);

                useAssets.add(asAsset);

                // 写入记录
                AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
                assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(changeId).setChangeOrgType(1);
                assetChanges.add(assetChange);
            } else {
                // 记录使用组织，使用人
                Long useOrg = data.getLong(USE_ORG);
                Long usePerson = data.getLong(USE_PERSON);
                String orgName = cacheResourceUtil.getOrgName(useOrg);
                if (StrUtil.isBlank(orgName)) {
                    orgName = "空";
                }
                String personName = cacheResourceUtil.getUserNameAndCode(usePerson);
                String targetOrgName = cacheResourceUtil.getOrgName(assetUser.getReceiveOrgId());
                String targetPersonName = cacheResourceUtil.getUserNameAndCode(assetUser.getReceiveEmployeeId());
                String content = "使用组织由" + orgName + "变成" + targetOrgName + "；使用人由" + personName + "变成" + targetPersonName;
                AsAssetLog assetLog = new AsAssetLog().setAssetId(assetDto.getId()).setActionContent(content);
                assetLogList.add(assetLog);

                data.put("useOrg", assetUser.getReceiveOrgId() != null ? Convert.toStr(assetUser.getReceiveOrgId()) : StrUtil.EMPTY);
                data.put("usePerson", assetUser.getReceiveEmployeeId() != null ? Convert.toStr(assetUser.getReceiveEmployeeId()) : StrUtil.EMPTY);
                useAssets.add(asAsset);

                // 写入记录
                AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
                assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(changeId).setChangeOrgType(1);
                assetChanges.add(assetChange);
            }
        }

        if (!useAssets.isEmpty()) {
            this.updateBatchById(useAssets);

            //记录过期时间
            assetReportFieldService.processExpireTime(useAssets);

            this.employeeAssetChangeService.saveBatch(assetChanges);
        }
        return assetLogList;
    }

    /**
     * 检查是否有管理的资产
     *
     * @return true/false
     */
    @Override
    public Boolean checkManageAsset(Long userId) {
        return Objects.nonNull(this.getBaseMapper().checkManageAsset(String.valueOf(userId), LoginUserThreadLocal.getCompanyId()));
    }

    /**
     * 检查是否有使用的资产
     *
     * @return true/false
     */
    @Override
    public Boolean checkUseAsset(Long userId) {
        return Objects.nonNull(this.getBaseMapper().checkUseAsset(String.valueOf(userId), LoginUserThreadLocal.getCompanyId()));
    }

    @Override
    public int countAssetExcludeTestCompanyByTime(String startTime, String endTime) {
        return this.getBaseMapper().countAssetByTimeExcludeTestCompany(startTime, endTime);
    }

    @Override
    public List<AssetValue> listByArea(List<Long> areaIds) {
        // 资产权限控制
        return this.getBaseMapper().selectCountByAreaId(areaIds.stream().map(String::valueOf).collect(toList()));
    }

    @Override
    public PageUtils<AssetValue> pageByArea(SearchAppAssetAreaDto dto) {
        IPage<AssetValue> page = this.getBaseMapper().selectCountByAreaId(dto.buildIPage(), dto.getIds().stream().map(String::valueOf).collect(toList()));
        return new PageUtils<>(page);
    }

    @Override
    public List<AssetForMessage> listAssetOrderForMessage(Long companyId, String type, List<Integer> days) {
        return this.getBaseMapper().selectAllForOrderMessage(companyId, type, days);
    }

    @Override
    public List<AssetForMessage> listAssetUseTimeLimitMessage(Long companyId, List<Integer> days) {
        return this.getBaseMapper().selectAllForAssetUseTimeLimitMessage(companyId, days);
    }

    @Override
    public TagAttrListCO getAttrList(String kw) {
        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        List<TagAttrCO> list = Lists.newArrayList();

        // 企业信息
        list.add(new TagAttrCO().setAttrName("企业信息").setAlias("企业信息").setAttrType(AssetConstant.ED_SPLIT_LINE));
        list.add(new TagAttrCO().setAttrCode("companyName").setAttrName("企业名称").setAlias("企业名称"));
        list.add(new TagAttrCO().setAttrCode("customWords").setAttrName("自定义文案").setAlias("自定义文案").setIsCustomize(true).setIsShow(false));

        list.add(new TagAttrCO().setAttrName("资产基础字段").setAlias("资产基础字段").setAttrType(AssetConstant.ED_SPLIT_LINE));
        list.add(new TagAttrCO().setAttrCode("assetId").setAttrName("资产ID").setAlias("资产ID"));
        list.add(new TagAttrCO().setAttrCode("companyOwner").setAttrName("所属公司").setAlias("所属公司"));

        List<String> notShowAttrList = ImmutableList.of(AssetConstant.ED_SPLIT_LINE, AssetConstant.ED_FILES, AssetConstant.ED_IMAGES);

        for (FormFieldCO fieldCO : formFields) {
            if (notShowAttrList.contains(fieldCO.getFieldType())) {
                continue;
            }
            // 属性对象
            TagAttrCO tagAttrCO = new TagAttrCO();
            tagAttrCO.setAttrCode(fieldCO.getFieldCode());
            tagAttrCO.setAttrName(fieldCO.getFieldName());
            tagAttrCO.setAttrType(fieldCO.getFieldType());
            tagAttrCO.setAlias(fieldCO.getFieldName());
            list.add(tagAttrCO);
        }

        if (StrUtil.isNotBlank(kw)) {
            list = list.stream().filter(it -> it.getAlias().contains(kw)).collect(Collectors.toList());
        }

        return new TagAttrListCO().setFormId(formVO.getFormId()).setTagAttrs(list);
    }

    @Override
    public QueryConditionSortDto sortField() {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.TYPE_ASSET_HEAD);
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        // 标准品
        Long standardId = queryHeadConfig.getStandardId();
        FormVO formVO = formService.getTplByType(AsFormService.BIZ_TYPE_ASSET);
        List<FormFieldCO> formFields = formVO.getFormFields();
        if (standardId != null && standardId > 0L) {
            List<FormFieldCO> extField = standardService.getStandardExtField(formVO.getFormId(), standardId);
            formFields.addAll(extField);
        }
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));
        // 补齐 创建时间、更新时间、打印时间
        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ASSET_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }

        QueryFieldConstant.Field updateTimeField = QueryFieldConstant.ASSET_EXT_FIELD.get(QueryFieldConstant.FIELD_UPDATE_TIME);
        if (ObjectUtil.isNotNull(updateTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(updateTimeField.getName(), updateTimeField.getCode(), updateTimeField.getType()));
        }

        QueryFieldConstant.Field lastPrintTimeField = QueryFieldConstant.ASSET_EXT_FIELD.get(QueryFieldConstant.ASSET_FILED_LAST_PRINT_TIME);
        if (ObjectUtil.isNotNull(lastPrintTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(lastPrintTimeField.getName(), lastPrintTimeField.getCode(), lastPrintTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    public List<AsAssetLog> assetStatusToIdle(Long empId) {
        // 使用的资产
        List<AssetDto> use = getBaseMapper().selectUseAsset(empId, LoginUserThreadLocal.getCompanyId());
        List<AsAsset> useAssets = new ArrayList<>();
        List<AsEmployeeAssetChange> assetChanges = new ArrayList<>();
        List<AsAssetLog> assetLogList = new ArrayList<>();
        List<Integer> status = Arrays.asList(AssetConstant.ASSET_STATUS_USING, AssetConstant.ASSET_STATUS_BORROW);
        for (AssetDto assetDto : use) {
            // 资产状态不是在用或者借用就不处理
            if (!status.contains(assetDto.getStatus())) {
                continue;
            }
            // 更新资产
            AsAsset asAsset = new AsAsset().setId(assetDto.getId()).setAssetData(assetDto.getAssetData()).setStandardId(assetDto.getStandardId());
            asAsset.setStatus(AssetConstant.ASSET_STATUS_IDLE);
            JSONObject data = asAsset.getAssetData();

            // 记录使用组织，使用人
            String content = "转移资产时将使用的资产置为闲置";
            AsAssetLog assetLog = new AsAssetLog().setAssetId(assetDto.getId()).setActionContent(content);
            assetLogList.add(assetLog);

            data.put(USE_ORG, "");
            data.put(USE_PERSON, "");

            useAssets.add(asAsset);

            // 写入记录
            AsEmployeeAssetChange assetChange = new AsEmployeeAssetChange();
            assetChange.setAssetSnapshotData(data).setAssetId(asAsset.getId()).setChangeId(IdUtils.getId()).setChangeOrgType(1);
            assetChanges.add(assetChange);
        }

        if (!useAssets.isEmpty()) {
            this.updateBatchById(useAssets);
            this.employeeAssetChangeService.saveBatch(assetChanges);
        }
        return assetLogList;
    }

    @Override
    public List<ImportImages> importImagesCheck(List<ImportImages> codes, Integer action) {
        Set<String> distinct = codes.stream().map(ImportImages::getCode).collect(Collectors.toSet());
        // 不带权限检索出基本信息
        List<AsAsset> noPrem = this.list(
                Wrappers.lambdaQuery(AsAsset.class)
                        .select(AsAsset::getId, AsAsset::getAssetData)
                        .in(AsAsset::getAssetCode, distinct)
        );
        Map<String, Integer> photoCount = noPrem.stream().collect(Collectors.toMap(asset -> asset.getAssetData().getString("assetCode"), asset -> action == 2 ? 0 : ((JSONArray) asset.getAssetData().getOrDefault("assetPhoto", new JSONArray())).size()));
        // 不存在的资产
        Collection<String> noExist = CollUtil.disjunction(distinct, photoCount.keySet());
        List<ImportImages> result = codes.stream().filter(v -> noExist.contains(v.getCode())).peek(v -> v.setError("当前资产图片没有对应资产信息，请核实资产编码信息是否正确")).collect(Collectors.toList());
        if (CollUtil.isEmpty(noPrem)) {
            return result;
        }
        codes.removeIf(v -> noExist.contains(v.getCode()));
        // 带权限检索
        AssetQueryConditionDto conditionDto = new AssetQueryConditionDto()
                .setIncludeAssetIds(noPrem.stream().map(AsAsset::getId).collect(toList()));
        conditionDto.setPageSize(1000000L);
        List<AssetDto> prem = listPc(conditionDto);
        Set<String> premCodes = prem.stream().map(v -> v.getAssetData().getString("assetCode")).collect(Collectors.toSet());
        // 无权限的
        Collection<String> denied = CollUtil.disjunction(distinct, premCodes);
        result.addAll(codes.stream().filter(v -> denied.contains(v.getCode())).peek(v -> v.setError("您没有当前资产图片编辑权限")).collect(toList()));
        codes.removeIf(v -> denied.contains(v.getCode()));
        // 图片张数超过10张
        Integer max = formService.getFormFieldPropertyValue(AsFormService.BIZ_TYPE_ASSET, AssetConstant.ED_IMAGES, "assetPhoto", "max", Integer.class);
        result.addAll(
                codes.stream().filter(v -> {
                    Integer count = photoCount.get(v.getCode());
                    // 新增
                    if (count >= max) {
                        return true;
                    }
                    // 更新
                    photoCount.put(v.getCode(), count + 1);
                    return false;
                }).peek(v -> v.setError("当前资产图片超出" + max + "张，不可上传")).collect(toList())
        );
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportImages> importImages(List<ImportImages> images, Integer action) {
        // 再次检查
        List<ImportImages> result = importImagesCheck(images, action);
        if (CollUtil.isNotEmpty(result)) {
            List<String> ids = result.stream().map(ImportImages::getUid).collect(toList());
            images.removeIf(v -> ids.contains(v.getUid()));
        }
        if (CollUtil.isEmpty(images)) {
            return result;
        }
        Map<String, List<String>> codeImages = images.stream().collect(Collectors.groupingBy(ImportImages::getCode, Collectors.mapping(ImportImages::getUrl, toList())));
        List<AsAsset> assets = this.list(Wrappers.lambdaQuery(AsAsset.class).in(AsAsset::getAssetCode, codeImages.keySet()));
        Map<String, AsAsset> codeMap = assets.stream().collect(Collectors.toMap(AsAsset::getAssetCode, v -> v));
        codeImages.forEach((k, v) -> {
            AsAsset asset = codeMap.get(k);
            List<String> photo = new ArrayList<>();
            if (asset.getAssetData().containsKey("assetPhoto")) {
                photo = asset.getAssetData().getJSONArray("assetPhoto").toJavaList(String.class);
            }
            if (action == 1) {
                photo.addAll(v);
            }
            if (action == 2) {
                photo.clear();
                photo.addAll(v);
            }
            asset.getAssetData().put("assetPhoto", photo);
            codeMap.put(k, asset);
        });
        this.updateBatchById(codeMap.values());
        return result;
    }

    @Override
    public Map<Long, JSONObject> getAssetSnapshot(List<AsAsset> assets) {
        if (CollUtil.isEmpty(assets)) {
            return MapUtil.empty();
        }
        FormVO assetVO = formService.assetTpl();
        Map<Long, JSONObject> collect = assets.stream().map(m -> BeanUtil.copyProperties(m, AssetDto.class)
        ).collect(Collectors.toMap(AssetDto::getId, AssetDto::translate, (k1, k2) -> k1));
        assetUtil.translateAssetJsonBatch(new ArrayList<>(collect.values()), FormFieldConvert.convertField(assetVO.getFormFields()));
        return collect;
    }

    @Override
    public List<AssetDto> listInventoryDispatchAssets(AssetQueryConditionDto queryConditionDto) {
        String conditions = conditionResolver.resolveQueryCondition("a", queryConditionDto.getConditions());
        transformKwFields(queryConditionDto);
        return this.getBaseMapper().listInventoryDispatchAssets(queryConditionDto, conditions);
    }

}
