package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAssetImportErrorMapper;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.service.AsAssetImportErrorService;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/1/22 14:48
 */
@Service
public class AsAssetImportErrorServiceImpl extends ServiceImpl<AsAssetImportErrorMapper, AsAssetImportError> implements AsAssetImportErrorService {

    @Override
    public Boolean importErrorDeleteAll(Integer importType) {
        return this.getBaseMapper().delete(new QueryWrapper<AsAssetImportError>().lambda()
                .eq(AsAssetImportError::getImportType, importType)
                .eq(AsAssetImportError::getCompanyId, LoginUserThreadLocal.getCompanyId())) > 0;
    }

}
