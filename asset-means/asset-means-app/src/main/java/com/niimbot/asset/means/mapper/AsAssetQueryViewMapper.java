package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsAssetQueryView;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 资产查询视图 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@EnableDataPerm(excludeMethodName = {"querySystemList"})
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsAssetQueryViewMapper.class)
public interface AsAssetQueryViewMapper extends BaseMapper<AsAssetQueryView> {
    /**
     * 查询系统默认列表
     *
     * @return
     */
    List<AsAssetQueryView> querySystemList();
}
