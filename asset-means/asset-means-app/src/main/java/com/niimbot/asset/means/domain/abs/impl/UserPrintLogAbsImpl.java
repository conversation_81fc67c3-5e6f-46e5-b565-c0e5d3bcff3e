package com.niimbot.asset.means.domain.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.means.mapstruct.MeansSystemMapStruct;
import com.niimbot.asset.means.model.AsUserPrintLog;
import com.niimbot.asset.means.service.AsUserPrintLogService;
import com.niimbot.asset.system.abs.UserPrintLogAbs;
import com.niimbot.asset.system.dto.LastUserPrintLogGetQry;
import com.niimbot.asset.system.dto.PrintErrorAmountGetQry;
import com.niimbot.asset.system.dto.PrintMaterialNameGetQry;
import com.niimbot.asset.system.dto.clientobject.UserPrintLogCO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/13 16:23
 */
@RestController
@RequestMapping("/client/abs/means/userPrintLogAbs/")
@RequiredArgsConstructor
public class UserPrintLogAbsImpl implements UserPrintLogAbs {

    private final MeansSystemMapStruct mapStruct;

    private final AsUserPrintLogService userPrintLogService;

    @Override
    public Integer getPrintErrorAmount(PrintErrorAmountGetQry qry) {
        if (Objects.isNull(qry.getPrintStatus())) {
            return userPrintLogService.printErrorCount(qry.getCompanyId(), qry.getPrintType(), qry.getStart(), qry.getEnd());
        }
        return userPrintLogService.printErrorCount(qry.getCompanyId(), qry.getPrintType(), qry.getPrintStatus(), qry.getStart(), qry.getEnd());
    }

    @Override
    public UserPrintLogCO getLastUserPrintLog(LastUserPrintLogGetQry qry) {
        List<AsUserPrintLog> printLogs =
                userPrintLogService.list(
                        Wrappers.lambdaQuery(AsUserPrintLog.class)
                                .eq(AsUserPrintLog::getCompanyId, qry.getCompanyId())
                                .between(AsUserPrintLog::getPrintTime, qry.getStart(), qry.getEnd())
                                .orderByDesc(AsUserPrintLog::getPrintTime)
                                .last("LIMIT 1"));
        return CollUtil.isEmpty(printLogs) ? null : mapStruct.convertUserPrintLogModelToCo(printLogs.get(0));
    }

    @Override
    public String getPrintMaterialName(PrintMaterialNameGetQry qry) {
        return userPrintLogService.getPrintMaterialName(qry.getTagId(), qry.getTagMaterialId());
    }

}
