package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsPrinterMaterialConcentrationMapper;
import com.niimbot.asset.means.model.AsPrinterMaterialConcentration;
import com.niimbot.asset.means.service.AsPrinterMaterialConcentrationService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备-材质-浓度关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Service
public class AsPrinterMaterialConcentrationServiceImpl extends ServiceImpl<AsPrinterMaterialConcentrationMapper, AsPrinterMaterialConcentration>
        implements AsPrinterMaterialConcentrationService {

}
