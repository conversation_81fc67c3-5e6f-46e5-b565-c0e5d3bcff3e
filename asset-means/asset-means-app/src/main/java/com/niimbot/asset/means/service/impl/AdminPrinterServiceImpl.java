package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAdminPrinterMapper;
import com.niimbot.asset.means.model.AsAdminPrinter;
import com.niimbot.asset.means.service.AdminPrinterSeriesService;
import com.niimbot.asset.means.service.AdminPrinterService;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.jf.core.exception.category.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 运行后台账号service
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Service
public class AdminPrinterServiceImpl extends ServiceImpl<AsAdminPrinterMapper, AsAdminPrinter> implements AdminPrinterService {

    @Resource
    private AdminPrinterSeriesService adminPrinterSeriesService;

    @Resource
    private AsCusEmployeeExtService cusEmployeeExtService;

    /**
     * 设置用户默认打印机系列
     *
     * @param id id
     * @return Boolean
     */
    @Override
    public Boolean setDefaultSeries(Long id) {
        // 查询打印机系列
        // AsAdminPrinterSeries byId = adminPrinterSeriesService.getById(id);
        // if (ObjectUtil.isNull(byId)) {
        //     throw new BusinessException(SystemResultCode.PRINT_SERIES_NOT_EXIST);
        // }
        AsAdminPrinter printer = this.getById(id);
        if (Objects.isNull(printer)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印机不存在");
        }
        // 修改用户默认标签模板
        AsCusEmployeeExt asCusEmployeeExt = new AsCusEmployeeExt();
        asCusEmployeeExt.setId(LoginUserThreadLocal.getCurrentUserId());
        // asCusEmployeeExt.setDefaultPrinterSeries(id);
        // 2023 02 13 改为用户默认打印机型
        asCusEmployeeExt.setDefaultPrinterId(id);
        if (!cusEmployeeExtService.saveOrUpdate(asCusEmployeeExt)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }
}
