package com.niimbot.asset.means.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsAssetRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AsAssetRelationMapper extends BaseMapper<AsAssetRelation> {

    /**
     * 查询已存在主资产id
     * @param companyId
     * @return
     */
    List<Long> selectMainAssetId(@Param("companyId") Long companyId);

    /**
     * 查询已存在子资产id
     * @param companyId
     * @return
     */
    List<Long> selectSubAssetId(@Param("companyId") Long companyId);

    /**
     * 查询所有资产id
     * @param companyId
     * @return
     */
    List<Long> selectAllAssetId(@Param("companyId") Long companyId);
}