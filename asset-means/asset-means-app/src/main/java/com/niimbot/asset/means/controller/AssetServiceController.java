package com.niimbot.asset.means.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.PageUtils;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AreaService;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import com.niimbot.dynamicform.BuildAssetLogDto;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.*;
import com.niimbot.system.AuditableOperateResult;
import com.niimbot.system.HeadImportErrorDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.StoreMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 资产控制器
 *
 * <AUTHOR>
 * @since 2020/12/9 17:39
 */
@RestController
@RequestMapping("server/means/asset")
public class AssetServiceController {

    private final AreaService areaService;

    private final AssetService assetService;

    private final AsAssetImportErrorService assetImportErrorService;


    @Autowired
    public AssetServiceController(AreaService areaService, AssetService assetService, AsAssetImportErrorService assetImportErrorService) {
        this.areaService = areaService;
        this.assetService = assetService;
        this.assetImportErrorService = assetImportErrorService;
    }

    /**
     * 新增资产
     *
     * @param asset 资产
     * @return true/false
     */
    @PostMapping
    public AsAsset add(@RequestBody AsAsset asset) {
        Long standardId = asset.getAssetData().getLong("standardId");
        asset.setStandardId(standardId);
        assetService.preAddHandle(ListUtil.of(asset), standardId);
        assetService.add(asset, StoreMode.MANUAL, IdUtils.getId());
        assetService.addLog(asset.getId(), asset.getAssetData(), new AssetAddLogDto().setActionContent("新增入库"));
        return asset;
    }

    @PostMapping("/buildAssetLog")
    public String buildAssetLog(@RequestBody BuildAssetLogDto buildAssetLogDto) {
        return assetService.buildAssetLog(buildAssetLogDto.getBefore(), buildAssetLogDto.getAfter(), buildAssetLogDto.getFormField());
    }

    /**
     * 修改资产
     *
     * @param asset 资产编辑
     * @return true/false
     */
    @PutMapping
    public Boolean edit(@RequestBody AsAsset asset) {
        return this.assetService.edit(asset);
    }

    /**
     * 批量编辑资产
     *
     * @param assetBatchDto 资产编辑
     * @return true/false
     */
    @PutMapping("/batch")
    public List<AuditableOperateResult> editBatch(@RequestBody AssetBatchDto assetBatchDto) {
        return this.assetService.editBatch(assetBatchDto);
    }

    /**
     * 查询获取资产信息
     *
     * @param id 资产ID
     * @return AsAsset信息
     */
    @GetMapping(value = "/{assetId}")
    public AsAsset getInfo(@PathVariable(value = "assetId") Long id) {
        return assetService.getInfoPerm(id);
    }

    @GetMapping(value = "/code/{assetCode}")
    public AsAsset getInfoByCode(@PathVariable(value = "assetCode") String assetCode) {
        return assetService.getOne(Wrappers.lambdaQuery(AsAsset.class)
                .eq(AsAsset::getAssetCode, assetCode), false);
    }

    @GetMapping(value = "/byId/{assetId}")
    public AsAsset getById(@PathVariable(value = "assetId") Long id) {
        return assetService.getInfoNoPerm(id);
    }

    @GetMapping("/inRecycleBin/{id}")
    public AsAsset getInRecycleBin(@PathVariable("id") Long id) {
        return assetService.getInRecycleBin(id);
    }

    /**
     * 根据资产id或资产phpId查询获取资产信息
     *
     * @param assetCode 资产id或资产phpId
     * @return AsAsset信息
     */
    @GetMapping(value = "/noPermCodePhp")
    public AsAsset getInfoNoPermByCodePhp(@RequestParam(value = "assetCode") String assetCode) {
        return assetService.getInfoNoPermByCodePhp(assetCode);
    }

    /**
     * 查询获取资产信息
     *
     * @param ids 资产ID
     * @return AsAsset信息
     */
    @PostMapping(value = "/list")
    public List<AsAsset> getInfoList(@RequestParam(value = "ids") List<Long> ids) {
        return assetService.list(new QueryWrapper<AsAsset>().lambda().in(AsAsset::getId, ids));
    }

    /**
     * 删除资产信息
     *
     * @param assetId 资产ID
     * @return 结果
     */
    @DeleteMapping(value = "/{assetId}")
    public AuditableOperateResult removeById(@PathVariable(value = "assetId") Long assetId) {
        return assetService.removeByAssetId(assetId);
    }

    /**
     * 批量删除资产信息
     *
     * @param assetIds 资产IDs
     * @return 结果
     */
    @DeleteMapping
    public List<AuditableOperateResult> remove(@RequestBody List<Long> assetIds) {
        return assetService.deleteBatch(assetIds);
    }

    @GetMapping(value = "/sortField")
    public QueryConditionSortDto sortField() {
        return assetService.sortField();
    }

    @PostMapping(value = "/page/pc")
    public IPage<AssetDto> pagePc(@RequestBody AssetQueryConditionDto queryDto) {
        return assetService.customPage(queryDto);
    }

    @PostMapping(value = "/page/app")
    public IPage<AssetAppPageDto> pageApp(@RequestBody AssetQueryConditionDto queryDto) {
        return assetService.pageApp(queryDto);
    }

    @GetMapping("/emp/{empId}")
    public List<AssetDto> empAssetList(@PathVariable("empId") Long empId) {
        return assetService.empAssetList(empId);
    }

    /**
     * 资产复制
     *
     * @return pageUtils对象
     */
    @PostMapping(value = "/copy")
    public List<AuditableOperateResult> copy(@RequestBody AssetCopyDto copyDto) {
        return assetService.copy(copyDto);
    }

    /**
     * 查看指定员工下的资产情况
     *
     * @param genericId 通用Id，可以是员工id、区域id，组织id，分类id等
     * @return 是否有资产
     */
    @GetMapping(value = "/checkAsset/{type}/{genericId}")
    public Boolean checkAsset(@PathVariable("type") String type,
                              @PathVariable("genericId") String genericId, AssetQueryDto queryDto) {
        QueryWrapper<AsAsset> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(AsAsset::getId);
        switch (type) {
            case AssetConstant.CHECK_ASSET_EMP:
                wrapper.lambda()
                        .and(query -> query.eq(AsAsset::getUsePerson, genericId)
                                .or().eq(AsAsset::getManagerOwner, genericId))
                        .and(query -> query.ne(AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE));
                break;
            case AssetConstant.CHECK_ASSET_ORG:
                wrapper.lambda().and(query -> query.eq(AsAsset::getOrgOwner, genericId)
                                .or().eq(AsAsset::getUseOrg, genericId))
                        .and(query -> query.ne(AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE));
                break;
            case AssetConstant.CHECK_ASSET_AREA:
                wrapper.lambda().eq(AsAsset::getStorageArea, genericId)
                        .ne(AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE);
                break;
            case AssetConstant.CHECK_ASSET_CATEGORY:
                wrapper.lambda().eq(AsAsset::getAssetCategory, genericId)
                        .ne(AsAsset::getStatus, AssetConstant.ASSET_STATUS_HANDLE);
                break;
            default:
                break;
        }
        if (CollUtil.isNotEmpty(queryDto.getStatusList())) {
            wrapper.lambda().in(AsAsset::getStatus, queryDto.getStatusList());
        }
        AsAsset asset = assetService.getOne(
                wrapper.lambda().last("LIMIT 1")
        );
        return Objects.nonNull(asset);
        // return CollUtil.isNotEmpty(assetService.list(wrapper));
    }

    /**
     * 根据资产状态获取对应的操作
     *
     * @param ids 资产ids
     * @return 对应的操作list
     */
    @PostMapping(value = "/getAssetOptByAssetId")
    public List<AssetOperationDto> getAssetOptByAssetId(@RequestBody List<Long> ids) {
        return assetService.getAssetOptByAssetId(ids);
    }

    /**
     * 根据资产状态获取对应的操作
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/allAssetOpt")
    public List<AssetOperationDto> allAssetOpt() {
        return assetService.allAssetOpt();
    }

    /**
     * 根据资产状态获取对应的操作
     *
     * @return 对应的操作list
     */
    @PostMapping(value = "/amountMoney")
    public String amountMoney(@RequestBody AssetQueryConditionDto queryDto) {
        return assetService.amountMoney(queryDto);
    }

    /**
     * 导入资产数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody AssetImportDto importDto) {
        return assetService.saveSheetData(importDto);
    }

    /**
     * 导入编辑资产数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveEditSheetData")
    public Boolean saveEditSheetData(@RequestBody AssetImportDto importDto) {
        return assetService.saveEditSheetData(importDto);
    }

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        assetService.saveSheetHead(importErrorDto);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return assetImportErrorService.deleteByTaskId(taskId);
    }

    /**
     * 根据资产状态获取对应的操作
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return assetService.importError(taskId);
    }

    @GetMapping("/checkManageAsset/{userId}")
    public Boolean checkManageAsset(@PathVariable("userId") Long userId) {
        return assetService.checkManageAsset(userId);
    }

    @GetMapping("/checkUseAsset/{userId}")
    public Boolean checkUseAsset(@PathVariable("userId") Long userId) {
        return assetService.checkUseAsset(userId);
    }

    @GetMapping("/list/byArea/{areaId}/{range}")
    public AppAssetAreaDto listByArea(@PathVariable Long areaId, @PathVariable(value = "range", required = false) Integer range) {
        if (Objects.isNull(range)) {
            range = 1;
        }
        // 区域权限控制
        List<AreaDto> dtos = areaService.listArea(new AreaQueryDto().setFilterPerm(true).setIncludeIds(ListUtil.of(areaId)));
        if (CollUtil.isEmpty(dtos)) {
            throw new BusinessException(SystemResultCode.NO_AREA_DATA_PERMISSION);
        }
        AsArea area = areaService.getById(areaId);
        if (Objects.isNull(area)) {
            throw new BusinessException(SystemResultCode.AREA_NOT_EXIST);
        }
        AppAssetAreaDto dto = new AppAssetAreaDto();
        dto.setAreaId(area.getId()).setAreaCode(area.getAreaCode()).setAreaName(area.getAreaName());
        List<Long> areaIds = new ArrayList<>(8);
        // 仅统计当前节点
        if (range == 1) {
            areaIds.add(area.getId());
        }
        // 统计当前节点与子集
        else if (range == 2) {
            List<Long> subIds = dtos.stream()
                    .filter(v -> v.getPaths().contains(String.valueOf(areaId)))
                    .map(AreaDto::getId)
                    .collect(Collectors.toList());
            areaIds.add(areaId);
            areaIds.addAll(subIds);
        }
        List<AssetValue> assetValues = assetService.listByArea(areaIds);
        if (CollUtil.isEmpty(assetValues)) {
            dto.setAssetCount(0).setIdleAssetCount(0).setApprovalAssetCount(0);
            return dto;
        }
        int idleAssetCount = 0, approvalAssetCount = 0;
        for (AssetValue assetValue : assetValues) {
            Integer status = assetValue.getStatus();
            if (status == 1) {
                idleAssetCount++;
            }
            if (status == 6) {
                approvalAssetCount++;
            }
        }
        dto.setAssetCount(assetValues.size()).setIdleAssetCount(idleAssetCount).setApprovalAssetCount(approvalAssetCount).setAssets(assetValues);
        return dto;
    }

    @PostMapping("/stata/byArea")
    public AppAssetAreaStatDto statByArea(@RequestBody SearchAppAssetAreaDto dto) {
        AppAssetAreaDto appAssetAreaDto = listByArea(dto.getAreadIdToLong(), dto.getRange());
        return BeanUtil.copyProperties(appAssetAreaDto, AppAssetAreaStatDto.class, "assets");
    }

    @PostMapping("/page/byArea")
    public PageUtils<AssetValue> pageByArea(@RequestBody SearchAppAssetAreaDto dto) {
        // 区域权限控制
        List<AreaDto> dtos = areaService.listArea(new AreaQueryDto().setFilterPerm(true).setIncludeIds(ListUtil.of(dto.getAreadIdToLong())));
        if (CollUtil.isEmpty(dtos)) {
            throw new BusinessException(SystemResultCode.NO_AREA_DATA_PERMISSION);
        }
        AsArea area = areaService.getById(dto.getAreadIdToLong());
        if (Objects.isNull(area)) {
            throw new BusinessException(SystemResultCode.AREA_NOT_EXIST);
        }
        List<Long> areaIds = new ArrayList<>(8);
        // 仅统计当前节点
        if (dto.getRange() == 1) {
            areaIds.add(area.getId());
        }
        // 统计当前节点与子集
        else if (dto.getRange() == 2) {
            List<Long> subIds = dtos.stream()
                    .filter(v -> v.getPaths().contains(String.valueOf(dto.getAreadIdToLong())))
                    .map(AreaDto::getId)
                    .collect(Collectors.toList());
            areaIds.add(dto.getAreadIdToLong());
            areaIds.addAll(subIds);
        }
        dto.setIds(areaIds);
        return assetService.pageByArea(dto);
    }

    @GetMapping(value = "/tag/attrList")
    public TagAttrListCO getTagAttrList(@RequestParam(value = "kw", required = false) String kw) {
        return assetService.getAttrList(kw);
    }

    @PostMapping("/images/check/{action}")
    public List<ImportImages> importImagesCheck(@RequestBody List<ImportImages> assetCodes, @PathVariable("action") Integer action) {
        if (CollUtil.isEmpty(assetCodes)) {
            return Collections.emptyList();
        }
        return assetService.importImagesCheck(assetCodes, action);
    }

    @PostMapping("/images/import/{action}")
    public List<ImportImages> importImages(@RequestBody List<ImportImages> images, @PathVariable("action") Integer action) {
        if (CollUtil.isEmpty(images)) {
            return Collections.emptyList();
        }
        return assetService.importImages(images, action);
    }

    @PostMapping("/quickSearch")
    public List<AsAsset> quickSearch(@RequestBody AssetQuickSearchDto quickSearchDto) {
        return assetService.quickSearch(quickSearchDto);
    }

}
