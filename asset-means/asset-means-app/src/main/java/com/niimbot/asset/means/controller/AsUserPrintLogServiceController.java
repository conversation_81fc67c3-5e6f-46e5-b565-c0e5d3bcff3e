package com.niimbot.asset.means.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.constant.NoticeConstant;
import com.niimbot.asset.framework.core.controller.BaseController;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsUserPrintLog;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.means.service.AsPrintDataSnapshotService;
import com.niimbot.asset.means.service.AsUserPrintLogService;
import com.niimbot.asset.means.service.AsUserPrintTaskService;
import com.niimbot.asset.system.mq.ExternalNoticeProducer;
import com.niimbot.framework.dataperm.object.Tuple2;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.PrintAssetLogQueryDto;
import com.niimbot.means.PrintTaskSnapshot;
import com.niimbot.system.PrintLogQuery;
import com.niimbot.system.SendExternalNotice;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 打印日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@RestController
@RequestMapping("/server/system/print/log")
@RequiredArgsConstructor
public class AsUserPrintLogServiceController extends BaseController {

    private final AsUserPrintLogService printLogService;

    private final AsUserPrintTaskService taskService;

    private final ExternalNoticeProducer externalNoticeProducer;

    private final AsPrintDataSnapshotService printDataSnapshotService;

    @GetMapping(value = "list")
    public List<AsUserPrintLog> assetLogList(PrintAssetLogQueryDto dto) {
        return printLogService.list(getQueryWrapper(dto));
    }

    @GetMapping(value = "page")
    public IPage<AsUserPrintLog> assetLogPage(PrintAssetLogQueryDto dto) {
        Long printTaskId = dto.getPrintTaskId();
        // 打印记录列表
        if (Objects.isNull(printTaskId)) {
            return printLogService.page(dto.buildIPage(), getQueryWrapper(dto));
        }
        // 打印任务 所有资产列表
        AsUserPrintTask byId = taskService.getOneByMapper(printTaskId);
        Page<AsUserPrintLog> page = new Page<AsUserPrintLog>().setSize(dto.getPageSize()).setCurrent(dto.getPageNum());
        if (byId == null) {
            page.setTotal(0).setRecords(new ArrayList<>()).setPages(0);
            return page;
        }
        // 当个打印任务的资产需要全部展示
        List<AsUserPrintLog> list = printLogService.list(Wrappers
                .<AsUserPrintLog>lambdaQuery()
                .eq(AsUserPrintLog::getPrintType, dto.getPrintType())
                .eq(AsUserPrintLog::getPrintTaskId, printTaskId));
        // 对所有打印结果按资产id 按打印时间取最新一个
        Map<Long, Optional<AsUserPrintLog>> collect = list.stream().collect(Collectors.groupingBy(AsUserPrintLog::getAssetId,
                Collectors.maxBy(Comparator.comparing(AsUserPrintLog::getPrintTime))));
        List<AsUserPrintLog> res = new ArrayList<>(byId.getAssetNum());
        // 原始资产id 数据
        PrintTaskSnapshot taskSnapshot = printDataSnapshotService.getTaskSnapshot(byId.getId());
        collect.forEach((k, v) -> v.ifPresent((val) -> res.add(val)));
        List<AsUserPrintLog> notPrintAsset = taskSnapshot.getDataIds().stream()
                .filter(id -> !collect.containsKey(id))
                .map(id -> new AsUserPrintLog().setAssetId(id).setPrintTaskId(printTaskId).setPrintStatus((short) 0))
                .collect(Collectors.toList());
        res.addAll(notPrintAsset);
        // 排序
        if (StrUtil.isBlank(dto.getSidx())) {
            // 无排序字段，默认按打印时间倒序
            res.sort((log1, log2) -> log1.compare(log2, "printTime", "desc"));
        } else {
            // 默认只支持 printTime、assetCode字段排序
            res.sort((log1, log2) -> log1.compare(log2, dto.getSidx(), dto.getOrder()));
        }
        int pageSize = Convert.toInt(dto.getPageSize());
        int pageNum = Convert.toInt(dto.getPageNum());

        long total = res.size() % pageSize == 0 ? res.size() / pageSize : res.size() / pageSize + 1;
        page.setTotal(res.size());
        page.setPages(total);
        if (total == 1) {
            page.setRecords(res);
        } else if (pageNum == total) {
            page.setRecords(res.subList((pageNum - 1) * pageSize, res.size()));
        } else {
            page.setRecords(res.subList((pageNum - 1) * pageSize, pageNum * pageSize));
        }
        return page;
    }

    @GetMapping("/full/page")
    public IPage<AsUserPrintLog> fullPage(PrintLogQuery query) {
        return printLogService.fullPrintLogPage(query);
    }

    @PostMapping
    public Boolean addPrintLog(@RequestBody List<AsUserPrintLog> logs) {
        boolean flag = printLogService.addPrintLog(logs);
        if (flag) {
            externalNoticeProducer.asyncSendMessage(new SendExternalNotice(LoginUserThreadLocal.getCompanyId(), NoticeConstant.PRINT_ERROR, NoticeConstant.DING_TALK));
        }
        return flag;
    }

    @ApiOperation(value = "根据打印日志ID查询资产快照数据")
    @GetMapping(value = "/{printTaskId}/{assetId}")
    public JSONObject getPrintAssetSnapShot(@PathVariable("printTaskId") Long printTaskId,
                                            @PathVariable("assetId") Long assetId) {
        AsUserPrintTask printTask = taskService.getOneByMapper(printTaskId);
        if (printTask == null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_LOG_ASSET_DATA_NOT_EXIST);
        }
        JSONObject assetData = printDataSnapshotService.getTaskTranslateSnapshot(printTaskId, assetId);
        assetData.put("assetTpl", assetData.get("assetTplOrigin"));
        assetData.put("buyTime", assetData.get("buyTimeOrigin"));
        return assetData;
    }

    private LambdaQueryWrapper<AsUserPrintLog> getQueryWrapper(PrintAssetLogQueryDto dto) {
        Tuple2<Date, Date> searchDate = taskService.getSearchDate(dto.getPrintTimes());
        LambdaQueryWrapper<AsUserPrintLog> queryWrapper = Wrappers.<AsUserPrintLog>lambdaQuery()
                .eq(AsUserPrintLog::getPrintType, dto.getPrintType())
                .eq(AsUserPrintLog::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(Objects.nonNull(dto.getPrintStatus()), AsUserPrintLog::getPrintStatus, dto.getPrintStatus())
                .le(Objects.nonNull(searchDate.getSecond()), AsUserPrintLog::getPrintTime, searchDate.getSecond())
                .ge(Objects.nonNull(searchDate.getFirst()), AsUserPrintLog::getPrintTime, searchDate.getFirst())
                .eq(ObjectUtil.isNotNull(dto.getPrintTaskId()), AsUserPrintLog::getPrintTaskId, dto.getPrintTaskId())
                .and(StrUtil.isNotBlank(dto.getKw()), wrapper -> wrapper
                        .like(AsUserPrintLog::getAssetName, dto.getKw())
                        .or().like(AsUserPrintLog::getAssetCode, dto.getKw()));
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            queryWrapper.eq(AsUserPrintLog::getUserId, LoginUserThreadLocal.getCurrentUserId());
        }
        if (StrUtil.isNotBlank(dto.getSidx())) {
            String order = Optional.ofNullable(dto.getOrder()).orElse(" asc ");
            String column = StrUtil.toUnderlineCase(dto.getSidx());
            queryWrapper.last(" order by " + column + " " + order);
        } else {
            queryWrapper.orderByDesc(AsUserPrintLog::getPrintTime);
        }
        return queryWrapper;
    }
}
