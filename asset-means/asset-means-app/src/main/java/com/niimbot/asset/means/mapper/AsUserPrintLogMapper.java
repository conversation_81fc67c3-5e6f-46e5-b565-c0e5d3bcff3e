package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.means.model.AsUserPrintLog;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.means.PrintAssetLogQueryDto;
import com.niimbot.means.UserPrintLogViewDto;
import com.niimbot.system.PrintLogQuery;
import com.niimbot.system.statistics.StatisticsEquipmentDto;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 资产打印日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsUserPrintLogMapper.class)
public interface AsUserPrintLogMapper extends BaseMapper<AsUserPrintLog> {

    List<UserPrintLogViewDto> assetLog(@Param("em") PrintAssetLogQueryDto dto,
                                       @Param("companyId") Long companyId, @Param("userId") Long userId);

    IPage<UserPrintLogViewDto> assetLog(@Param("page") Page<UserPrintLogViewDto> page,
                                        @Param("em") PrintAssetLogQueryDto dto, @Param("companyId") Long companyId,
                                        @Param("userId") Long userId);

    IPage<AsUserPrintLog> fullPrintLogPage(Page<PrintLogQuery> page, @Param("companyId") Long companyId, @Param("em") PrintLogQuery query);

    List<StatisticsEquipmentDto> userByCount();

    String selectPrintLogMaterialName(Long tagId);

    @Select("SELECT\n" +
            "\tCOUNT( DISTINCT print_task_id ) \n" +
            "FROM\n" +
            "\tas_user_print_log \n" +
            "WHERE\n" +
            "\tis_delete = 0 \n" +
            "\tAND ( company_id = #{companyId} AND print_type = #{printType} AND print_time BETWEEN #{begin} AND #{end} )")
    Integer printAssetCount(@Param("companyId") Long companyId, @Param("printType") Integer printType, @Param("begin") LocalDateTime begin, @Param("end") LocalDateTime end);

    @Select("SELECT\n" +
            "\tCOUNT( DISTINCT print_task_id ) \n" +
            "FROM\n" +
            "\tas_user_print_log \n" +
            "WHERE\n" +
            "\tis_delete = 0 \n" +
            "\tAND ( company_id = #{companyId} AND print_type = #{printType} AND print_status = #{printStatus} AND print_time BETWEEN #{begin} AND #{end} )")
    Integer printErrorCount(@Param("companyId") Long companyId, @Param("printType") Integer printType, @Param("printStatus") Integer printStatus, @Param("begin") LocalDateTime begin, @Param("end") LocalDateTime end);
}
