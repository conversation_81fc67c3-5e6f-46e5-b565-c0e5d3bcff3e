package com.niimbot.asset.means.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.activiti.WorkflowExecuteStepDto;
import com.niimbot.activiti.WorkflowStartDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.model.ActWorkflowBusiness;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActWorkflowBusinessService;
import com.niimbot.asset.activiti.service.ActWorkflowCallbackErrorService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.WorkflowUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.*;
import com.niimbot.asset.means.service.AsOrderDetailService;
import com.niimbot.asset.means.service.AsOrderService;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import com.niimbot.system.QueryConditionSortDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.convert.Convert.toInt;
import static cn.hutool.core.convert.Convert.toLong;
import static java.util.stream.Collectors.toList;

/**
 * 单据控制器
 *
 * <AUTHOR>
 * @since 2020-12-24
 */
@Slf4j
@RestController
@RequestMapping("server/means/assetOrder")
@RequiredArgsConstructor
public class AsOrderServiceController {
    private final AsOrderService orderService;
    private final AsOrderDetailService detailService;
    private final AsOrderTypeService orderTypeService;
    private final ActWorkflowService workflowService;
    private final ActWorkflowBusinessService workflowBusinessService;
    private final AsCusEmployeeService cusEmployeeService;
    private final AssetService assetService;
    @Resource
    private RedisService redisService;
    @Resource
    private ActWorkflowCallbackErrorService callbackErrorService;
    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @PostMapping("/page")
    public IPage<AsOrder> page(@RequestBody AsOrderQueryDto dto) {
        return orderService.page(dto);
    }

    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(
            LoginUserDto loginUserDto, @RequestBody AsOrderDto dto) {
        WorkflowExecuteDto result = new WorkflowExecuteDto();
        String uuid = UUID.fastUUID().toString();
        result.setConditionId(uuid);
        loginUserDto.getCusUser().setOrgId(dto.getOrgId());
        // 查询单据类型
        AsOrderType orderType =
                orderTypeService.getOne(
                        Wrappers.<AsOrderType>lambdaQuery()
                                .eq(AsOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                                .eq(AsOrderType::getType, dto.getOrderType()));

        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), dto.getOrgId(), false);
        if (workflow != null) {
            JSONObject orderData = dto.getOrderData();
            List<String> categoryList = new ArrayList<>();
            List<String> areaList = new ArrayList<>();
            List<String> orgOwnerList = new ArrayList<>();
            BigDecimal totalMoney = BigDecimal.ZERO;

            List<Long> assetIds = dto.getAssets().stream().map(AsOrderAssetDto::getId).collect(toList());
            List<AsAsset> asAssets = assetService.listByIds(assetIds);
            Set<Long> managerOwnerSet = new HashSet<>();
            for (AsAsset asAsset : asAssets) {
                JSONObject assetData = asAsset.getAssetData();
                String assetCategory = assetData.getString("assetCategory");
                if (StrUtil.isNotEmpty(assetCategory)) {
                    categoryList.add(assetCategory);
                }
                String storageArea = assetData.getString("storageArea");
                if (StrUtil.isNotEmpty(storageArea)) {
                    areaList.add(storageArea);
                }
                String orgOwner = assetData.getString("orgOwner");
                if (StrUtil.isNotEmpty(orgOwner)) {
                    orgOwnerList.add(orgOwner);
                }
                String price = assetData.getString("price");
                if (StrUtil.isNotEmpty(price)) {
                    BigDecimal priceNum = new BigDecimal(price);
                    totalMoney = totalMoney.add(priceNum);
                }
                if (managerOwnerSet.size() < 20) {
                    Long manageOwner = assetData.getLong("managerOwner");
                    if (manageOwner != null) {
                        managerOwnerSet.add(manageOwner);
                    }
                }
            }
            JSONObject condition = new JSONObject();
            if (CollUtil.isNotEmpty(categoryList)) {
                condition.put(QueryFieldConstant.ASSET_CATEGORY, categoryList);
            }
            if (CollUtil.isNotEmpty(areaList)) {
                condition.put(QueryFieldConstant.ASSET_AREA, areaList);
            }
            if (CollUtil.isNotEmpty(orgOwnerList)) {
                condition.put(QueryFieldConstant.ASSET_ORG_OWNER, orgOwnerList);
            }
            condition.put(QueryFieldConstant.ASSET_TOTAL_MONEY, totalMoney);
            condition.put(QueryFieldConstant.SUBMITTER_ORG, CollUtil.union(ListUtil.of(LoginUserThreadLocal.getCurrentUserId(), dto.getOrgId()), approveRoleMemberService.getEmpRoleIds(LoginUserThreadLocal.getCurrentUserId())));
            orderData.putAll(condition);
            orderData.put(QueryFieldConstant.ASSET_MANAGER_OWNER, new ArrayList<>(managerOwnerSet));

            String redisKey = RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.hSetAll(redisKey, condition);
            redisService.expire(redisKey, 2, TimeUnit.HOURS);
            result.setWorkflowExecuteStepDto(workflowService.preStartWorkflow(loginUserDto, workflow, orderData, null));
            String stepKey = RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.set(stepKey, result.getWorkflowExecuteStepDto());
            redisService.expire(stepKey, 2, TimeUnit.HOURS);
        }
        return result;
    }

    /**
     * 单据提交
     *
     * @param dto
     * @return
     */
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AuditableCreateOrderResult insert(LoginUserDto loginUserDto, @RequestBody AsOrderSubmitDto dto) {
        orderService.verify(dto.getOrderDto());
        orderService.translation(dto.getOrderDto());
        loginUserDto.getCusUser().setOrgId(dto.getOrderDto().getOrgId());
        AsOrderDto orderDto = dto.getOrderDto();
        // 查询单据类型
        AsOrderType orderType =
                orderTypeService.getOne(
                        Wrappers.<AsOrderType>lambdaQuery()
                                .eq(
                                        AsOrderType::getCompanyId,
                                        loginUserDto.getCusUser().getCompanyId())
                                .eq(AsOrderType::getType, orderDto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), loginUserDto.getCusUser().getOrgId(), false);

        Long businessId = orderService.insert(orderDto, workflow != null);
        if (workflow != null) {
            if (dto.getExecuteStepDtoList().isEmpty()) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_EXECUTE_STEP_IS_EMPTY);
            }
            // 写入审批人
            List<WorkflowExecuteStepDto> executeStepList = Convert.toList(WorkflowExecuteStepDto.class, redisService.get(
                    RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId())));
            Map<Long, List<WorkflowExecuteStepDto.WorkflowApprover>> submitExecuteStepList = dto.getExecuteStepDtoList().stream()
                    .collect(Collectors.toMap(WorkflowExecuteStepDto::getStepId, WorkflowExecuteStepDto::getApproverList, (k1, k2) -> k1));
            for (WorkflowExecuteStepDto stepDto : executeStepList) {
                if (submitExecuteStepList.containsKey(stepDto.getStepId())) {
                    stepDto.setApproverList(submitExecuteStepList.get(stepDto.getStepId()));
                }
            }
            AsCusEmployee employee = cusEmployeeService.getById(loginUserDto.getCusUser().getId());
            String userName =
                    employee == null
                            ? loginUserDto.getCusUser().getAccount()
                            : employee.getEmpName();

            JSONObject orderData = orderDto.getOrderData();
            Map<Object, Object> map = redisService.hGetAll(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            if (map != null) {
                Map<String, Object> conditionMap = Convert.toMap(String.class, Object.class, map);
                orderData.putAll(conditionMap);
            }
            WorkflowStartDto workflowStartDto = new WorkflowStartDto()
                    .setUserDto(loginUserDto.getCusUser())
                    .setTitle(getTitle(orderType.getType(), userName))
                    .setActivitiKey(orderType.getActivitiKey())
                    .setWorkflowId(workflow.getId())
                    .setVersion(workflow.getVersion())
                    .setCallbackUrl(workflow.getCallbackUrl())
                    .setBusinessType(orderType.getType().shortValue())
                    .setBusinessId(businessId)
                    .setFormData(orderData)
                    .setExecuteStepDtoList(executeStepList);
            workflowService.startWorkflow(workflowStartDto);
            redisService.del(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            redisService.del(RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
        }
        return new AuditableCreateOrderResult(businessId, orderDto.getOrderNo());
    }

    private String getTitle(Integer orderType, String userName) {
        String templateStr = "%s提交的资产%s申请";
        return String.format(templateStr, userName, OrderTypeEnum.getByType(orderType).getName());
    }

    /**
     * 单据详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AsOrderDto getById(@PathVariable Long id) {
        return orderService.getDetail(id);
    }

    @PostMapping("/assets")
    public List<AsOrderAssetDto> getAssetsByOrderId(@RequestBody Collection<Long> ids) {
        return orderService.getAssetsByOrderId(ids);
    }

    @GetMapping("/withoutAsset/{id}")
    public AsOrderDto getByIdWithoutAsset(@PathVariable Long id) {
        return BeanUtil.copyProperties(getById(id), AsOrderDto.class);
    }

    /**
     * 资产列表分页
     *
     * @param dto
     * @return
     */
    @GetMapping("/asset/page")
    public IPage<JSONObject> assetPage(AsOrderAssetQueryDto dto) {
        return orderService.assetPage(dto);
    }

    /**
     * 单据id、资产id获取资产快照详情
     *
     * @param orderId 单据id
     * @param assetId 资产id
     * @return 资产快照详情
     */
    @GetMapping("/asset/{orderId}/{assetId}")
    public JSONObject getAssetDetail(
            @PathVariable("orderId") Long orderId, @PathVariable("assetId") Long assetId) {
        return detailService.getAssetDetail(orderId, assetId);
    }

    @PostMapping("/processCallback")
    @Transactional(rollbackFor = Exception.class)
    public Boolean processCallback(@RequestBody WorkflowCallbackDto callbackDto) {
        AsOrder order = orderService.getById(Long.parseLong(callbackDto.getBusinessId()));
        if (order == null) {
            return false;
        }
        callbackErrorService.remove(callbackDto);
        if (WorkflowUtil.finishWorkflowCallbackAllowed().contains(order.getApproveStatus())) {
            return true;
        }
        short status = Short.parseShort(callbackDto.getApproveStatus());
        if (!WorkflowUtil.finishWorkflowCallbackAllowed().contains(status)) {
            return false;
        }
        // threadLocal写入公司Id
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(new CusUserDto()
                .setId(order.getCreateBy())
                .setIsAdmin(true)
                .setCompanyId(order.getCompanyId()));
        LoginUserThreadLocal.set(userDto);
        // 状态处理
        order.setApproveStatus(status);
        Optional<ActWorkflowBusiness> workflowBusiness = Optional.ofNullable(workflowBusinessService.getById(callbackDto.getProcessInstanceId()));
        workflowBusiness.ifPresent(v -> v.setApproveStatus(status));
        try {
            if (DictConstant.APPROVED == status) {
                List<AsOrderDetail> list = detailService.list(Wrappers.<AsOrderDetail>lambdaQuery()
                        .eq(AsOrderDetail::getOrderId, order.getId()));
                //单个资产变更时允许清空资产属性
                String orderKey = "orderKey:" + list.get(0).getAssetId();
                Boolean assetModifySwitch = false;
                if (redisService.hasKey(orderKey)){
                    assetModifySwitch = (Boolean) redisService.get(orderKey);
                }
                // 审批通过的业务逻辑
                orderService.dealAssetData(order, null, null, Long.parseLong(callbackDto.getStartUserId()),assetModifySwitch);
                if (assetModifySwitch){
                    redisService.del(orderKey);
                }
            } else {
                // 驳回、撤销  回滚资产状态
                List<AsOrderDetail> detailList = detailService.list(new QueryWrapper<AsOrderDetail>().lambda().eq(AsOrderDetail::getOrderId, order.getId()));
                List<AsAsset> renewList = detailList.stream().map(v -> new AsAsset()
                        .setId(toLong(v.getAssetSnapshotData().get("id")))
                        .setStatus(toInt(v.getAssetSnapshotData().get("status")))).collect(toList());
                assetService.updateBatchById(renewList, 500);
            }
            workflowBusiness.ifPresent(workflowBusinessService::updateById);
            orderService.updateById(order);
        } finally {
            LoginUserThreadLocal.remove();
        }
        return true;
    }

    /**
     * 根据资产id获取当前审批中的单据id
     *
     * @param assetId assetId
     * @return 结果
     */
    @GetMapping("/approveOrderByAsset/{assetId}")
    public AsOrderInfoDto getApproveOrderByAssetId(@PathVariable Long assetId) {
        return orderService.getApproveOrderByAssetId(assetId);
    }

    /**
     * 资产单据-用于导出
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/listForExport")
    public List<AsOrderDto> listForExport(@RequestBody OrderOtherQueryDto dto) {
        return orderService.listForExport(dto);
    }

    /**
     * 单据表单查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "/form/{orderType}")
    public FormVO getForm(@PathVariable("orderType") Integer orderType,
                          @RequestParam(value = "singRow", required = false) Boolean singRow) {
        return orderService.getForm(orderType, singRow, LoginUserThreadLocal.getCompanyId());
    }

    /**
     * 变更单表单查询
     *
     * @return
     */
    @GetMapping(value = "/changeForm")
    public FormVO getChangeForm() {
        return orderService.getChangeForm(LoginUserThreadLocal.getCompanyId());
    }

    /**
     * 单据排序字段查询
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "/sortField/{orderType}")
    public QueryConditionSortDto sortField(@PathVariable("orderType") Integer orderType) {
        return orderService.sortField(orderType);
    }

}
