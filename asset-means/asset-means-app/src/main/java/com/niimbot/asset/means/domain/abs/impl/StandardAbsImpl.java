package com.niimbot.asset.means.domain.abs.impl;

import com.niimbot.asset.means.service.StandardService;
import com.niimbot.asset.system.abs.StandardAbs;
import com.niimbot.asset.system.dto.StandardExtFieldListQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/13 14:53
 */
@RestController
@RequestMapping("/client/abs/means/standardAbs/")
@RequiredArgsConstructor
public class StandardAbsImpl implements StandardAbs {
    private final StandardService standardService;

    @Override
    public List<FormFieldCO> getStandardExtField(StandardExtFieldListQry qry) {
        return standardService.getStandardExtField(qry.getMappingFormId(), qry.getStandardId());
    }
}
