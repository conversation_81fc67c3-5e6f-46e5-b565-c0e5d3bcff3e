package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.constant.ResultConstant;
import com.niimbot.asset.means.service.AdminAuthEquipmentService;
import com.niimbot.jf.core.component.annotation.ResultMessage;
import com.niimbot.system.AdminAuthEquipmentCompanyInsertDto;
import com.niimbot.system.AdminAuthEquipmentCompanyUpdateDto;
import com.niimbot.system.AdminAuthEquipmentDetailListDto;
import com.niimbot.system.AdminAuthEquipmentDto;
import com.niimbot.system.AdminAuthEquipmentQueryDto;
import com.niimbot.system.AdminAuthEquipmentSerialInsertDto;
import com.niimbot.system.AdminAuthEquipmentSerialUpdateDto;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/17 17:17
 */
@RestController
@RequestMapping("server/system/authEquipment")
@RequiredArgsConstructor
public class AdminAuthEquipmentServiceController {
    private final AdminAuthEquipmentService adminAuthEquipmentService;


    /**
     * 查询所有设备
     *
     * @return 所有数据
     */
    @GetMapping("list")
    public List<AdminAuthEquipmentDto> selectEquipmentList() {
        return this.adminAuthEquipmentService.selectEquipmentList();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public AdminAuthEquipmentDto getByEquipmentId(@PathVariable Long id) {
        return this.adminAuthEquipmentService.getByEquipmentId(id);
    }

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @PostMapping
    public Boolean insertEquipment(@RequestBody AdminAuthEquipmentDto dto) {
        return this.adminAuthEquipmentService.insertEquipment(dto);
    }

    /**
     * 修改数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PutMapping
    public Boolean updateEquipment(@RequestBody AdminAuthEquipmentDto dto) {
        return this.adminAuthEquipmentService.updateEquipment(dto);
    }

    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping("{id}")
    public Boolean deleteEquipment(@PathVariable Long id) {
        return this.adminAuthEquipmentService.deleteEquipment(id);
    }

    /**
     * 查询设备详情列表
     *
     * @param equipmentId 设备id
     * @return 所有数据
     */
    @GetMapping("/detail/list")
    public List<AdminAuthEquipmentDetailListDto> selectDetailList(@RequestParam Long equipmentId) {
        return this.adminAuthEquipmentService.selectDetailList(equipmentId);
    }

    /**
     * 查询设备详情分页
     *
     * @param dto page对象
     * @return 所有数据
     */
    @GetMapping(value = "/detail/page")
    public IPage<AdminAuthEquipmentDetailListDto> selectDetailPage(AdminAuthEquipmentQueryDto dto) {
        return adminAuthEquipmentService.selectDetailPage(dto);
    }

    /**
     * 新增企业
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PostMapping("/company")
    public Boolean insertCompany(@RequestBody AdminAuthEquipmentCompanyInsertDto dto) {
        return this.adminAuthEquipmentService.insertCompany(dto);
    }

    /**
     * 修改公司信息
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PutMapping("/company")
    public Boolean updateCompany(@RequestBody AdminAuthEquipmentCompanyUpdateDto dto) {
        return this.adminAuthEquipmentService.updateCompany(dto);
    }


    /**
     * 新增序列号
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PostMapping("/serial")
    public Boolean insertSerial(@RequestBody AdminAuthEquipmentSerialInsertDto dto) {
        return this.adminAuthEquipmentService.insertSerial(dto);
    }

    /**
     * 修改序列号数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PutMapping("/serial")
    public Boolean updateSerial(@RequestBody AdminAuthEquipmentSerialUpdateDto dto) {
        return this.adminAuthEquipmentService.updateSerial(dto);
    }

    /**
     * 删除公司或序列号
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping("/detail/{id}")
    public Boolean deleteSerial(@PathVariable Long id) {
        return this.adminAuthEquipmentService.deleteSerial(id);
    }


    /**
     * 公司、序列号详情接口
     *
     * @param id 主键结合
     * @return 详情信息
     */
    @ApiOperation(value = "公司、序列号详情接口")
    @ResultMessage(ResultConstant.DELETE_SUCCESS)
    @GetMapping("/detail/{id}")
    public AdminAuthEquipmentDetailListDto detailInfo(@PathVariable Long id) {
        return this.adminAuthEquipmentService.detailInfo(id);
    }
}
