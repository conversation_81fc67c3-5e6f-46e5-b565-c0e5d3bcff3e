package com.niimbot.asset.means.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.means.model.AsOrder;
import com.niimbot.asset.means.service.AsOrderService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.TodoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 通用单据待办数据处理
 * <AUTHOR>
 * @date 2023/9/6 上午10:04
 */
@Slf4j
@Service
public class TodoAssetCommonOrderServiceImpl implements TodoOrderService {

    @Autowired
    private AsOrderService orderService;

    //通用单据类型
    private static final List<Integer> commonOrderTypeList = ListUtil.of(AssetConstant.ORDER_TYPE_RECEIVE,
            AssetConstant.ORDER_TYPE_RETURN, AssetConstant.ORDER_TYPE_BORROW, AssetConstant.ORDER_TYPE_BACK,
            AssetConstant.ORDER_TYPE_ALLOCATE, AssetConstant.ORDER_TYPE_DISPOSE, AssetConstant.ORDER_TYPE_CHANGE);

    @Override
    public Boolean supportOrderType(Integer orderType) {
        return commonOrderTypeList.contains(orderType);
    }

    @Override
    public JSONObject getOrderJson(Long businessId) {
        AsOrder order = orderService.getById(businessId);
        if (Objects.isNull(order)) {
            return null;
        }

        JSONObject result = JsonUtil.toJsonObject(order, o -> {
            JSONObject orderData = o.getJSONObject("orderData");
            o.fluentPutAll(orderData);
            o.fluentRemove("orderData");
        });
        return result;
    }

    @Override
    public AsTodo getOrderTodo(AsTodo todo) {
        AsOrder order = orderService.getById(todo.getBusinessId());
        if (Objects.isNull(order)) {
            return null;
        }

        //如果orderData为空，优先设置下
        if (Objects.isNull(order.getOrderData())) {
            order.setOrderData(new JSONObject());
        }

        order.getOrderData().put("summary", order.getSummary());
        AsTodo result = new AsTodo()
                .setSummary(order.getSummary())
                .setCreateBy(order.getCreateBy())
                .setOrderData(JSONObject.toJSONString(order.getOrderData()));
        return result;
    }
}
