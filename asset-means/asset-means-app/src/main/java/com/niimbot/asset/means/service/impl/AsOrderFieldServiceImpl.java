package com.niimbot.asset.means.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsOrderFieldMapper;
import com.niimbot.asset.means.model.AsOrderField;
import com.niimbot.asset.means.service.AsOrderFieldService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/22 15:23
 */
@Service
public class AsOrderFieldServiceImpl extends ServiceImpl<AsOrderFieldMapper, AsOrderField>
        implements AsOrderFieldService {

    @Override
    public void initCompanyOrderField(Long companyId) {
        List<AsOrderField> fields = this.list(
                Wrappers.<AsOrderField>lambdaQuery()
                        .eq(AsOrderField::getCompanyId, 0L));
        if (fields.isEmpty()) {
            return;
        }
        List<AsOrderField> companyFields = fields.stream()
                .peek(field ->
                        field.setId(null).setCompanyId(companyId))
                .collect(Collectors.toList());
        this.saveBatch(companyFields);
    }

    @Override
    public boolean syncCompanyOrderField(Long companyId) {
        List<AsOrderField> fields = this.list(
                Wrappers.<AsOrderField>lambdaQuery()
                        .in(AsOrderField::getCompanyId, Arrays.asList(companyId, 0L)));
        Map<Integer, List<AsOrderField>> existMap = fields.stream()
                .filter(field -> field.getCompanyId().equals(companyId))
                .collect(Collectors.groupingBy(AsOrderField::getOrderType));
        List<AsOrderField> systemOrderFields = fields.stream()
                .filter(field -> field.getCompanyId().equals(0L))
                .filter(field -> {
                    if (CollUtil.isNotEmpty(existMap)
                            && CollUtil.isNotEmpty(existMap.get(field.getOrderType()))) {
                        List<String> exists = existMap.get(field.getOrderType())
                                .stream().map(AsOrderField::getCode).collect(Collectors.toList());
                        return !exists.contains(field.getCode());
                    }
                    return true;
                })
                .collect(Collectors.toList());
        List<AsOrderField> companyOrderFields = systemOrderFields.stream()
                .peek(field ->
                        field.setId(null).setCompanyId(companyId))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(companyOrderFields)) {
            return true;
        }
        return this.saveBatch(companyOrderFields);
    }
}
