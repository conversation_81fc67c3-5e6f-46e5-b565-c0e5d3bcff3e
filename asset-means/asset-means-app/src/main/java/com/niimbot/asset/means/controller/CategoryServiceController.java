package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.utils.SerialNumberUtils;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryImportDto;
import com.niimbot.means.CategoryQueryDto;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资产分类 <AUTHOR>
 *
 * @since 2020/11/26 11:19
 */
@RestController
@RequestMapping("server/means/category")
public class CategoryServiceController {

    private final CategoryService categoryService;

    private final AsAssetImportErrorService assetImportErrorService;

    @Autowired
    public CategoryServiceController(CategoryService categoryService,
                                     AsAssetImportErrorService assetImportErrorService) {
        this.categoryService = categoryService;
        this.assetImportErrorService = assetImportErrorService;
    }

    @GetMapping(value = "/list")
    public List<CategoryDto> list(CategoryQueryDto queryDto) {
        // TODO 测试后在调整是否需要查询PATH
        return categoryService.cateList(queryDto);
    }

    @GetMapping(value = "/{categoryId}")
    public AsCategory getInfo(@PathVariable(value = "categoryId") Long categoryId) {
        return categoryService.getById(categoryId);
    }

    @PostMapping
    public AsCategory add(@RequestBody AsCategory category) {
        return categoryService.add(category);
    }

    @PutMapping
    public AsCategory edit(@RequestBody AsCategory category) {
        return categoryService.edit(category);
    }

    @PutMapping("/sort")
    public Boolean sort(@RequestBody List<Long> categoryIds) {
        return categoryService.sort(categoryIds);
    }

    @DeleteMapping
    public List<AsCategory> remove(@RequestBody List<Long> categoryIds) {
        return categoryService.remove(categoryIds);
    }

    @PostMapping(value = "/listByIds")
    public List<AsCategory> listByIds(@RequestBody List<Long> ids) {
        return categoryService.list(Wrappers.<AsCategory>lambdaQuery()
                .in(AsCategory::getId, ids));
    }

    @GetMapping("/recommendCode")
    public String recommendCode() {
        return SerialNumberUtils.getMaxCode(categoryService);
    }

    /**
     * 错误记录
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return categoryService.importError(taskId);
    }

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        categoryService.saveSheetHead(importErrorDto);
    }

    /**
     * 导入耗材数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody CategoryImportDto importDto) {
        return categoryService.saveSheetData(importDto);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return assetImportErrorService.deleteByTaskId(taskId);
    }

    /**
     * 内部服务，通过code或者name查询
     *
     * @return
     */
    @GetMapping("getOne")
    public Long getOne(@RequestParam(value = "name", required = false) String name,
                       @RequestParam(value = "code", required = false) String code) {
        return categoryService.getOne(name, code);
    }

}
