package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsAssetStatus;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.means.AssetStatusDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 资产状态表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-24
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsAssetStatusMapper.class)
public interface AsAssetStatusMapper extends BaseMapper<AsAssetStatus> {

    /**
     * 通过资产操作查询查询资产状态
     *
     * @param optId 操作Id
     * @return 资产状态列表
     */
    List<AssetStatusDto> getAssetStatusByOpt(Long optId);


    /**
     * 查询全部状态
     *
     * @return 全部状态
     */
    List<AssetStatusDto> allStatus();

    /**
     * 通过单据类型查询资产状态
     *
     * @param orderType 单据类型
     * @return 资产状态
     */
    List<Integer> getAssetStatusByOrderType(Integer orderType);
}
