package com.niimbot.asset.means.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsUserPrintTaskMapper;
import com.niimbot.asset.means.model.*;
import com.niimbot.asset.means.service.*;
import com.niimbot.asset.means.service.impl.strategy.PrintStrategy;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.model.AsPrintTemplate;
import com.niimbot.asset.system.model.AsTagSize;
import com.niimbot.asset.system.model.AsUserTag;
import com.niimbot.asset.system.service.AsUserTagService;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.asset.system.service.PrintTemplateService;
import com.niimbot.asset.system.service.TagSizeService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.enums.PrintTaskStatusEnum;
import com.niimbot.framework.dataperm.object.Tuple2;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.PrintMaterialQueryDto;
import com.niimbot.means.*;
import com.niimbot.system.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.Charsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 打印任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Slf4j
@Service
public class AsUserPrintTaskServiceImpl extends ServiceImpl<AsUserPrintTaskMapper, AsUserPrintTask> implements AsUserPrintTaskService {
    @Resource
    private AdminPrinterService adminPrinterService;
    @Resource
    private AsUserTagService userTagService;
    @Resource
    private AsTagMaterialService tagMaterialService;
    @Resource
    private TagSizeService tagSizeService;
    @Resource
    private AdminAuthEquipmentService adminAuthEquipmentService;
    @Resource
    private AsAdminPrinterSizeService printerSizeService;
    @Resource
    private CompanyService companyService;
    @Resource
    private AsUserPrintLogService userPrintLogService;
    @Resource
    private AdminPrinterConcentrationService printerConcentrationService;
    @Resource
    private AsPrintDataSnapshotService printDataSnapshotService;
    @Resource
    private PrintTemplateService printTemplateService;
    @Resource
    private AsFormService formService;
    @Resource
    private AssetService assetService;
    private Map<Short, PrintStrategy> printStrategyMap;

    @Autowired
    public void setPrintStrategyMap(List<PrintStrategy> printStrategies) {
        this.printStrategyMap = printStrategies.stream().collect(Collectors.toConcurrentMap(PrintStrategy::printType, v -> v));
    }

    private static final String PRINT_JSON_PATH = "printJson";

    @Override
    public List<AsUserPrintTask> listTaskPc(PrintTaskQueryDto dto) {
        if (CollUtil.isNotEmpty(dto.getQueryList())) {
            dto.getQueryList().forEach(queryDto -> checkLastPrintTask(queryDto.getLastTaskId(), queryDto.getLastAssetId()));
        }
        LambdaQueryWrapper<AsUserPrintTask> queryWrapper = getQueryWrapper(dto);
        queryWrapper
                .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .eq(AsUserPrintTask::getIsClear, false)
                .orderByDesc(AsUserPrintTask::getCreateTime);
        List<AsUserPrintTask> list = this.list(queryWrapper);
        configTaskNameAndFailReason(list);
        return list;
    }

    @Override
    public IPage<AsUserPrintTask> pageTaskPc(PrintTaskQueryDto dto) {
        if (CollUtil.isNotEmpty(dto.getQueryList())) {
            dto.getQueryList().forEach(queryDto -> checkLastPrintTask(queryDto.getLastTaskId(),
                    queryDto.getLastAssetId()));
        }
        LambdaQueryWrapper<AsUserPrintTask> queryWrapper = getQueryWrapper(dto);
        // if (!LoginUserThreadLocal.getCusUser().getIsAdmin()) {
        queryWrapper.eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId());
        // }
        if (StrUtil.isNotBlank(dto.getSidx())) {
            String order = Optional.ofNullable(dto.getOrder()).orElse(" asc ");
            String column = StrUtil.toUnderlineCase(dto.getSidx());
            queryWrapper.last(" order by " + column + " " + order);
        } else {
            queryWrapper.orderByDesc(AsUserPrintTask::getCreateTime);
        }
        Page<AsUserPrintTask> page = this.page(dto.buildIPage(), queryWrapper);
        configTaskNameAndFailReason(page.getRecords());

        return page;
    }

    @Override
    public List<AsUserPrintTask> listTaskApp(Short printType, Short taskStatus, List<LastPrintTaskQueryDto> queryList) {
        if (CollUtil.isNotEmpty(queryList)) {
            queryList.forEach(dto -> checkLastPrintTask(dto.getLastTaskId(), dto.getLastAssetId()));
        }
        LambdaQueryWrapper<AsUserPrintTask> wrapper = Wrappers
                .<AsUserPrintTask>lambdaQuery()
                .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .eq(AsUserPrintTask::getTaskType, 1)
                // 只展示未清空的
                .eq(AsUserPrintTask::getIsClear, false)
                .orderByDesc(AsUserPrintTask::getCreateTime);
        // 所有类型
        if (printType == 0) {
            wrapper.in(AsUserPrintTask::getPrintType, Arrays.asList(DictConstant.PRINT_TYPE_ASSET, DictConstant.PRINT_TYPE_MATERIAL));
        } else {
            wrapper.eq(AsUserPrintTask::getPrintType, printType);
        }
        if (taskStatus == 1) {
            // 只展示已完成的
            wrapper.eq(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.DONE.getStatus());
        } else {
            // 不展示已完成与已取消
            wrapper.notIn(AsUserPrintTask::getTaskStatus, Collections.singletonList(PrintTaskStatusEnum.DONE.getStatus()));
        }
        List<AsUserPrintTask> list = this.list(wrapper);
        configTaskNameAndFailReason(list);
        return list;
    }

    @Override
    public Integer listTaskAppCount(Short printType) {
        return Convert.toInt(this.count(Wrappers
                .<AsUserPrintTask>lambdaQuery()
                .eq(AsUserPrintTask::getPrintType, printType)
                .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId())
                .eq(AsUserPrintTask::getIsClear, false)
                .ne(AsUserPrintTask::getTaskStatus,
                        PrintTaskStatusEnum.DONE.getStatus())
                .eq(AsUserPrintTask::getTaskType, 1)));
    }

    @Override
    public List<JSONObject> listOfDataWithStatusPausedInTaskDetails(Long taskId) {
        // 任务打印失败的快照信息
        List<AsUserPrintLog> list = userPrintLogService.list(
                Wrappers.lambdaQuery(AsUserPrintLog.class)
                        .eq(AsUserPrintLog::getPrintTaskId, taskId)
                        .eq(AsUserPrintLog::getPrintStatus, 2)
        );
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Set<Long> dataIds = list.stream().map(AsUserPrintLog::getAssetId).collect(Collectors.toSet());
        List<AsPrintDataSnapshot> part1 = printDataSnapshotService.list(
                Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                        .eq(AsPrintDataSnapshot::getTaskId, taskId)
                        .in(AsPrintDataSnapshot::getDataId, dataIds)
                        .orderByAsc(AsPrintDataSnapshot::getSort)
        );
        // 快照前一张
        Set<Integer> sorts = part1.stream().map(snapshot -> snapshot.getSort() - 1).collect(Collectors.toSet());
        List<AsPrintDataSnapshot> part2 = printDataSnapshotService.list(
                Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                        .eq(AsPrintDataSnapshot::getTaskId, taskId)
                        .in(AsPrintDataSnapshot::getSort, sorts)
                        .notIn(AsPrintDataSnapshot::getDataId, dataIds)
                        .orderByAsc(AsPrintDataSnapshot::getSort)
        );
        // 合并排序后返回
        part1.addAll(part2);
        return part1.stream().sorted(Comparator.comparing(AsPrintDataSnapshot::getSort)).map(AsPrintDataSnapshot::getSnapshot).collect(toList());
    }

    @Override
    public IPage<UserPrintTaskGroupStatusResult> taskDetailsPage(UserPrintTaskGroupByStatusPageQuery query) {
        return printDataSnapshotService.taskDetailsPage(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrintDataViewDto startTask(UserPrintTaskConfigDto configDto) {
        Long taskId = configDto.getId();
        AsUserPrintTask byId = this.getById(taskId);
        if (byId == null) {
            throw new BusinessException(SystemResultCode.PRINT_TASK_NOT_EXIST);
        }
        // 只有第一页才变更状态
        if (configDto.getStartIndex() == 1) {
            // 判断是否有任务在打印
            checkIfAnyProgressingTask(byId.getPrintType(), byId.getPrintSource().intValue());
            byId.setTaskStatus(PrintTaskStatusEnum.PRINTING.getStatus().shortValue())
                    .setPrintTime(LocalDateTime.now());
            this.updateById(byId);
            updateUserExt(byId);
        }
        // 分段获取打印数据快照
        List<AsPrintDataSnapshot> taskSnaps = printDataSnapshotService.getTaskSnaps(configDto.getStartIndex(), configDto.getEndIndex(), byId.getId());
        return getResult(byId, taskSnaps);
    }

    public PrintDataViewDto getResult(AsUserPrintTask userPrintTask, List<AsPrintDataSnapshot> records) {
        Tuple2<AsUserTag, AsTagSize> tuple2 = checkTaskTag(userPrintTask.getTagId(), userPrintTask.getPrintType());
        PrintLabelDto label = getLabel(userPrintTask, tuple2.getFirst(), tuple2.getSecond());
        // 查询当前打印数据
        printStrategyMap.get(userPrintTask.getPrintType()).dealPrintSnapshot(records);
        Map<Long, AsPrintDataSnapshot> map = records.stream().collect(Collectors.toConcurrentMap(AsPrintDataSnapshot::getDataId, v -> v));
        List<JSONObject> needTrans = map.values().stream().filter(snapshot -> !snapshot.getIsTranslation()).map(AsPrintDataSnapshot::getSnapshot).collect(toList());
        if (CollUtil.isNotEmpty(needTrans)) {
            printStrategyMap.get(userPrintTask.getPrintType()).translate(needTrans, label.getCompanyName(), tuple2.getFirst().getStandardId());
            needTrans.forEach(data -> {
                long id = data.getLong("id");
                map.get(id).setSnapshot(data).setIsTranslation(true);
            });
            // 回填翻译后的数据
            printDataSnapshotService.updateBatchById(map.values());
        }
        // 取消服务端生成打印JSON数据的流程由客户端实现组装
        return new PrintDataViewDto()
                .setPrintDataList(records.stream().map(AsPrintDataSnapshot::getSnapshot).collect(toList()))
                .setTaskId(userPrintTask.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrintDataViewDto againTask(UserPrintTaskDto printTask) {
        Long lastTaskId = printTask.getId();
        AsUserPrintTask lastPrintTask = this.getById(lastTaskId).setId(null).setPrintTime(null)
                .setCreateTime(null).setCreateBy(null).setTaskStatus(PrintTaskStatusEnum.WAITING.getStatus().shortValue())
                .setAssetNum(null).setPrintedAssetNum(null).setPrintSource(printTask.getPrintSource());
        if (Objects.isNull(lastPrintTask)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "任务不存在");
        }
        if (Objects.isNull(lastPrintTask.getQueryData())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "任务重新条件不存在");
        }
        // 查询条件
        PrintQueryDataDto query;
        switch (lastPrintTask.getPrintType()) {
            case DictConstant.PRINT_TYPE_ASSET:
                query = lastPrintTask.getQueryData().toJavaObject(PrintAssetQueryDto.class);
                break;
            case DictConstant.PRINT_TYPE_MATERIAL:
                query = lastPrintTask.getQueryData().toJavaObject(PrintMaterialQueryDto.class);
                break;
            case DictConstant.PRINT_TYPE_AREA:
                query = lastPrintTask.getQueryData().toJavaObject(PrintAreaQueryDto.class);
                break;
            case DictConstant.PRINT_TYPE_POINT:
                query = lastPrintTask.getQueryData().toJavaObject(PrintPointQueryDto.class);
                break;
            default:
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印任务类型不存在");
        }
        // 添加新任务
        return addTask(lastPrintTask, query);
    }

    @Override
    public PrintDataViewDto continueTaskPrintOne(UserPrintTaskConfigDto configDto) {
        // copy continueTask method
        AsUserPrintTask userPrintTask = BeanUtil.copyProperties(configDto, AsUserPrintTask.class);
        AsUserPrintTask byId = continuePrintTaskCheck(userPrintTask, configDto.isCheckTaskStatus());
        Tuple2<AsUserTag, AsTagSize> tuple2 = checkTaskTag(byId.getTagId(), byId.getPrintType());
        PrintLabelDto label = getLabel(byId, tuple2.getFirst(), tuple2.getSecond());
        JSONObject snapshot = printDataSnapshotService.getTaskSnapshot(byId.getId(), configDto.getDataId());
        if (Objects.isNull(snapshot)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前打印任务中没有此数据");
        }
        // JSONObject templateJson = getPrintTemplateJson(tuple2.getSecond().getJsonFileName());
        JSONObject templateJson = getPrintJson(tuple2.getSecond().getId());
        return new PrintDataViewDto()
                .setLabel(label)
                .setTemplateJson(templateJson)
                .setTaskSize(1L)
                .setPrintDataList(Collections.singletonList(snapshot))
                .setAttrData(tuple2.getFirst().getAttrData())
                .setTaskId(byId.getId());

    }

    private AsUserPrintTask continuePrintTaskCheck(AsUserPrintTask userPrintTask, boolean checkTaskStatus) {
        Long taskId = userPrintTask.getId();
        configTask(userPrintTask);
        AsUserPrintTask byId = getById(taskId);
        // 判断是否有任务在打印
        checkIfAnyProgressingTask(byId.getPrintType(), byId.getPrintSource().intValue());
        PrintTaskStatusEnum printTaskStatusEnum = PrintTaskStatusEnum.get(byId.getTaskStatus().intValue());
        // 任务异常暂停后，pc 改状态为 排队待打印
        if (checkTaskStatus) {
            if (printTaskStatusEnum == PrintTaskStatusEnum.WAITING || printTaskStatusEnum == PrintTaskStatusEnum.PRINTING
                    || printTaskStatusEnum == PrintTaskStatusEnum.DONE) {
                throw new BusinessException(SystemResultCode.PRINT_TASK_CONTINUE_ERROR);
            }
        }
        // PC 排队打印
        if (ObjectUtil.equal(1, byId.getPrintSource())) {
            List<AsUserPrintTask> list = this.list(Wrappers
                    .<AsUserPrintTask>lambdaQuery()
                    .eq(AsUserPrintTask::getPrintType, byId.getPrintType())
                    .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId())
                    .eq(AsUserPrintTask::getPrintSource, 1)
                    .eq(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PRINTING.getStatus()));
            if (CollUtil.isNotEmpty(list)) {
                throw new BusinessException(SystemResultCode.PRINT_TASK_PRINTING_PC_CONTINUE_ERROR);
            }
            this.update(
                    Wrappers.lambdaUpdate(AsUserPrintTask.class)
                            .set(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.WAITING.getStatus())
                            .eq(AsUserPrintTask::getId, byId.getId())
            );
        }
        return byId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrintDataViewDto continueTask(UserPrintTaskConfigDto configDto) {
        AsUserPrintTask userPrintTask = BeanUtil.copyProperties(configDto, AsUserPrintTask.class);
        AsUserPrintTask byId = continuePrintTaskCheck(userPrintTask, configDto.isCheckTaskStatus());
        userPrintTask.setPrintType(byId.getPrintType());
        AsAdminPrinter printer = checkPrinterName(configDto.getPrinterName());
        updateUserExt(userPrintTask);
        Integer notPrintId = null;
        // 暂停状态的从下一张开始打印
        if (PrintTaskStatusEnum.PAUSE.getStatus().shortValue() == byId.getTaskStatus() || PrintTaskStatusEnum.WAITING.getStatus().shortValue() == byId.getTaskStatus()) {
            List<AsPrintDataSnapshot> snapshots = printDataSnapshotService.list(
                    Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                            .select(AsPrintDataSnapshot::getSort)
                            .eq(AsPrintDataSnapshot::getTaskId, byId.getId())
                            .eq(AsPrintDataSnapshot::getIsPrint, false)
                            .orderByAsc(AsPrintDataSnapshot::getSort)
                            .last("LIMIT 1")
            );
            if (CollUtil.isNotEmpty(snapshots)) {
                notPrintId = snapshots.get(0).getSort();
            }
        }
        // 异常状态的从当前张开始打印
        if (PrintTaskStatusEnum.EXCEPTION_PAUSE.getStatus().shortValue() == byId.getTaskStatus()) {
            List<AsUserPrintLog> logs = userPrintLogService.list(
                    Wrappers.lambdaQuery(AsUserPrintLog.class)
                            .select(AsUserPrintLog::getAssetId)
                            .eq(AsUserPrintLog::getPrintTaskId, byId.getId())
                            .eq(AsUserPrintLog::getPrintStatus, 2)
                            .orderByDesc(AsUserPrintLog::getPrintTime)
                            .last("LIMIT 1")
            );
            if (CollUtil.isNotEmpty(logs)) {
                Long dataId = logs.get(0).getAssetId();
                notPrintId = printDataSnapshotService.getOne(
                        Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                                .eq(AsPrintDataSnapshot::getTaskId, byId.getId())
                                .eq(AsPrintDataSnapshot::getDataId, dataId)
                ).getSort();
            } else {
                // 异常暂停时由于特殊原因，没有回传打印日志。则从第一张开始打印
                notPrintId = 1;
            }
        }
        if (Objects.isNull(notPrintId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前任务没有待打印的数据");
        }
        Tuple2<AsUserTag, AsTagSize> tuple2 = checkTaskTag(userPrintTask.getTagId(), userPrintTask.getPrintType());
        PrintLabelDto label = getLabel(userPrintTask, tuple2.getFirst(), tuple2.getSecond());
        // JSONObject templateJson = getPrintTemplateJson(tuple2.getSecond().getJsonFileName());
        JSONObject templateJson = getPrintJson(tuple2.getSecond().getId());
        // 不是第一张才变更状态未打印中，因为第一张调用start接口时会改变状态
        if (notPrintId > 1) {
            this.update(Wrappers.lambdaUpdate(AsUserPrintTask.class).set(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PRINTING.getStatus().shortValue()).eq(AsUserPrintTask::getId, byId.getId()));
        }
        return new PrintDataViewDto()
                .setTaskId(userPrintTask.getId())
                .setAttrData(tuple2.getFirst().getAttrData())
                .setLabel(label)
                .setTemplateJson(templateJson)
                .setTaskSize(byId.getAssetNum())
                .setNotPrintId(notPrintId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pauseTask(List<AsUserPrintLog> logs, boolean fromLog) {
        Map<Long, List<AsUserPrintLog>> group = logs.stream()
                .collect(Collectors.groupingBy(AsUserPrintLog::getPrintTaskId));
        // 日志可能属于不同task
        group.forEach((taskId, val) -> {
            AsUserPrintTask byId = checkTask(taskId);
            // PrintTaskStatusEnum printTaskStatusEnum = PrintTaskStatusEnum.get(byId.getTaskStatus().intValue());
            // if (printTaskStatusEnum != PrintTaskStatusEnum.PRINTING) {
            //     // throw new BusinessException(SystemResultCode.PRINT_TASK_OPT_ERROR, PrintTaskStatusEnum.PRINTING.getDesc());
            //     log.info("打印任务[{}]状态不是进行中", taskId);
            //     return;
            // }
            Short printSource = val.get(0).getPrintSource();
            if (!Objects.equals(printSource, byId.getPrintSource())) {
                throw new BusinessException(SystemResultCode.PRINT_TASK_PRINTING_PAUSE_ERROR, PrintTaskStatusEnum.PRINTING.getDesc());
            }
            // 已打印成功的数据ID集合
            List<Long> successIds = userPrintLogService.printLogCount(taskId, 1);
            int currentPrintCount = new HashSet<>(successIds).size();
            // 全部的
            PrintTaskSnapshot snapshot = printDataSnapshotService.getTaskSnapshot(taskId);
            List<Long> all = snapshot.getDataIds();

            // 过滤未保存的
            List<AsUserPrintLog> collect = val.stream()
                    .filter(log -> {
                        // 打印异常的数据不管它之前是否打印成功过本次日志都需要保存
                        if (log.getPrintStatus() == 2) {
                            return true;
                        } else {
                            return !successIds.contains(log.getAssetId()) && ObjectUtil.isNotNull(log.getAssetId()) && all.contains(log.getAssetId());
                        }
                    })
                    .collect(toList());
            collect.forEach(printLog -> printLog.fillProps(byId, snapshot.getSnapshots()));
            if (CollUtil.isNotEmpty(collect)) {
                userPrintLogService.saveBatch(collect);
                userPrintLogService.updateBizPrintTime(collect, byId.getPrintType());
                ConcurrentMap<Long, JSONObject> map = collect.stream().filter(v -> Objects.nonNull(v.getAssetId()) && Objects.nonNull(v.getAssetData())).collect(Collectors.toConcurrentMap(AsUserPrintLog::getAssetId, AsUserPrintLog::getAssetData));
                // 更新已打印的记录
                List<AsPrintDataSnapshot> snapshots = printDataSnapshotService.list(
                        Wrappers.lambdaUpdate(AsPrintDataSnapshot.class)
                                .eq(AsPrintDataSnapshot::getTaskId, taskId)
                                .in(AsPrintDataSnapshot::getDataId, map.keySet())
                                .orderByAsc(AsPrintDataSnapshot::getSort)
                );
                snapshots.forEach(snapshot1 -> {
                    if (map.containsKey(snapshot1.getDataId())) {
                        snapshot1.setSnapshot(map.get(snapshot1.getDataId())).setIsPrint(true);
                    }
                });
                printDataSnapshotService.updateBatchById(snapshots);
                // 只统计打印成功的日志
                currentPrintCount = currentPrintCount + collect.stream().filter(v -> v.getPrintStatus() == 1).map(AsUserPrintLog::getAssetId).collect(Collectors.toSet()).size();
            }
            // 已打印总数
            boolean printException = val.stream().anyMatch(log -> ObjectUtil.isNotNull(log.getAssetId()) && log.getPrintStatus().intValue() == 2);
            int taskStatus = printException ? PrintTaskStatusEnum.EXCEPTION_PAUSE.getStatus() : PrintTaskStatusEnum.PAUSE.getStatus();
            this.update(Wrappers.<AsUserPrintTask>lambdaUpdate()
                    // 打印过就算已打印不管成功或失败
                    .set(AsUserPrintTask::getPrintedAssetNum, currentPrintCount)
                    .set(AsUserPrintTask::getTaskStatus, taskStatus)
                    .eq(AsUserPrintTask::getId, taskId)
            );
        });
        return true;
    }

    @Override
    public Boolean cancelTask(Long taskId) {
        AsUserPrintTask byId = checkTask(taskId);
        PrintTaskStatusEnum printTaskStatusEnum = PrintTaskStatusEnum.get(byId.getTaskStatus().intValue());
        if (printTaskStatusEnum == PrintTaskStatusEnum.PAUSE ||
                printTaskStatusEnum == PrintTaskStatusEnum.EXCEPTION_PAUSE ||
                printTaskStatusEnum == PrintTaskStatusEnum.WAITING) {
            return this.update(Wrappers.<AsUserPrintTask>lambdaUpdate()
                    .set(AsUserPrintTask::getIsClear, true)
                    .eq(AsUserPrintTask::getId, taskId));

        }
        throw new BusinessException(SystemResultCode.PRINT_TASK_OPT_ERROR, PrintTaskStatusEnum.PAUSE.getDesc()
                + "、" + PrintTaskStatusEnum.EXCEPTION_PAUSE.getDesc() + "、" + PrintTaskStatusEnum.WAITING.getDesc());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrintDataViewDto addTask(AsUserPrintTask printTask, PrintQueryDataDto dto) {
        AsAdminPrinter printer = checkPrinterName(printTask.getPrinterName());
        Tuple2<AsUserTag, AsTagSize> tuple2 = checkTaskTag(printTask.getTagId(), printTask.getPrintType());
        AsUserTag userTag = tuple2.getFirst();
        AsTagSize tagSize = tuple2.getSecond();
        AsAdminPrinterSize printerSize = printerSizeService.getOne(Wrappers.<AsAdminPrinterSize>lambdaQuery()
                .eq(AsAdminPrinterSize::getPrinterId, printer.getId())
                .eq(AsAdminPrinterSize::getSizeId, userTag.getSizeId()));
        if (printerSize == null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_TAT_SIZEINVALID);
        }
        AsTagMaterial tagMaterial = tagMaterialService.getById(printTask.getTagMaterialId());
        if (tagMaterial == null) {
            throw new BusinessException(SystemResultCode.TAG_MATERIAL_NOT_EXISTS);
        }
        if (tagMaterial.getIsRfid() && !printer.getIsRfid()) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_NOT_SUPPORT_RFID_MATERIAL);
        }
        if (tagMaterial.getIsRfid()) {
            printTask.setTagType(true);
        }
        // String printJsonName = tagSize.getJsonFileName();
        printTask.setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setUserId(LoginUserThreadLocal.getCurrentUserId());
        PrintLabelDto printLabelDto = getLabel(printTask, userTag, tagSize);
        PrintDataDto printDataDto = new PrintDataDto().setPrintTaskId(printTask.getId())
                .setPrintLabelDto(printLabelDto)/*.setPrintJsonName(printJsonName)*/
                .setTagSizeId(tagSize.getId())
                .setPrintSource(printTask.getPrintSource())
                .setStandardId(userTag.getStandardId())
                .setAttrData(userTag.getAttrData());
        return dealSnapshotData(printTask, printDataDto, dto);
    }

    @Override
    public UserPrintCheckInfoDto continueTaskCheck(Long taskId, String printerName) {
        UserPrintTaskInfoDto infoDto = tasKInfo(taskId);
        // 当前只比较打印机是否与任务匹配 比较打印机type
        UserPrintCheckInfoDto res = new UserPrintCheckInfoDto().setTaskInfo(infoDto);
        AsAdminPrinter printer = checkPrinterName(printerName);
        AsAdminPrinter printerTask = checkPrinterName(infoDto.getPrinterName());
        // 根据打印设备匹配
        if (!Objects.equals(printer.getId(), printerTask.getId())) {
            res.setIsMatch(false);
            String message = SystemResultCode.PRINT_TASK_CONTINUE_CHECK_ERROR.getMessage();
            String msg = String.format(message, infoDto.getPrinterName(),
                    infoDto.getSizeLong() + "x" + infoDto.getSizeWide() + "mm");
            res.setMessage(msg);
        } else {
            res.setIsMatch(true);
            res.setMessage("OK");
        }
        return res;
    }

    @Override
    public UserPrintTaskInfoDto tasKInfo(Long taskId) {
        AsUserPrintTask byId = checkTask(taskId);
        UserTagPrintDto tagDetail = userTagService.getDetail(byId.getTagId());
        if (tagDetail == null) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }
        AsTagMaterial tagMaterial = tagMaterialService.getById(byId.getTagMaterialId());
        UserPrintTaskInfoDto infoDto = BeanUtil.copyProperties(byId, UserPrintTaskInfoDto.class);
        return infoDto.setSizeLong(tagDetail.getSizeLong()).setSizeId(tagDetail.getSizeId())
                .setSizeWide(tagDetail.getSizeWide()).setSdkType(tagMaterial.getSdkType())
                .setTagName(tagDetail.getTagName()).setTagUrl(tagDetail.getTagUrl()).setTagMaterialIsRfId(tagMaterial.getIsRfid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean configAppContinueTask(UserPrintTaskConfigDto printTask) {
        checkPrinterName(printTask.getPrinterName());
        updateUserExt(BeanUtil.copyProperties(printTask, AsUserPrintTask.class));
        return true;
    }

    @Override
    public Boolean configTask(AsUserPrintTask printTask) {
        checkTask(printTask.getId());
        // 不允许修改打印类型
        printTask.setPrintType(null);
        // 如果有不为NULL的值就更新数据
        Map<String, Object> beanToMap = BeanUtil.beanToMap(printTask);
        beanToMap.remove("id");
        beanToMap.values().stream().filter(ObjectUtil::isNotNull).findFirst().ifPresent(o -> updateById(printTask));
        return true;
    }

    @Override
    public Boolean checkAuthEquipment(AntiFakeCheckDto antiFakeCheckDto) {
        // 判断设备是否开启认证
        AsAdminPrinter adminPrinter = adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery().eq(AsAdminPrinter::getModel,
                antiFakeCheckDto.getPrinterName()));
        if (null == adminPrinter) {
            return true;
        }
        if (!adminPrinter.getEnableAuth()) {
            return true;
        }

        List<AsAdminAuthEquipment> list = Lists.newArrayList();
        if (adminPrinter.getAuthType() == 1) {
            list = adminAuthEquipmentService.list(Wrappers
                    .<AsAdminAuthEquipment>lambdaQuery().eq(AsAdminAuthEquipment::getEquipmentName, antiFakeCheckDto.getPrinterName())
                    .eq(AsAdminAuthEquipment::getAuthorizationType, 1)
                    .eq(AsAdminAuthEquipment::getSerialNumber, antiFakeCheckDto.getSerialNumber())
            );
        } else if (adminPrinter.getAuthType() == 2) {
            list = adminAuthEquipmentService.list(Wrappers
                    .<AsAdminAuthEquipment>lambdaQuery().eq(AsAdminAuthEquipment::getEquipmentName, antiFakeCheckDto.getPrinterName())
                    .eq(AsAdminAuthEquipment::getAuthorizationType, 2)
                    .eq(AsAdminAuthEquipment::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        }
        return CollUtil.isNotEmpty(list);
    }

    /**
     * 更新 默认标签、 默认材质，默认打印浓度
     */
    private void updateUserExt(AsUserPrintTask userPrintTask) {
        String printerName = userPrintTask.getPrinterName();
        if (StrUtil.isBlank(printerName)) {
            return;
        }

        // 获取设备id
        AsAdminPrinter adminPrinter = adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getModel, userPrintTask.getPrinterName()));
        if (ObjectUtil.isNull(adminPrinter)) {
            return;
        }
        Long printerId = adminPrinter.getId();

        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 设备-材质-浓度关联表查询
        AsAdminPrinterConcentration printerConcentration = printerConcentrationService.getOne(
                Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getMaterialId, userPrintTask.getTagMaterialId())
                        .eq(AsAdminPrinterConcentration::getUserId, currentUserId)
                        .eq(AsAdminPrinterConcentration::getIsDefault, false)
        );
        AsAdminPrinterConcentration printerConcentrationNew = Optional.ofNullable(printerConcentration)
                .orElse(new AsAdminPrinterConcentration().setUserId(currentUserId).setPrinterId(printerId)
                        .setMaterialId(userPrintTask.getTagMaterialId()).setIsDefault(false));

        // 设置默认材质和默认浓度
        if (ObjectUtil.isNotEmpty(userPrintTask.getPrintConcentration())) {
            printerConcentrationNew.setDefaultConcentration(userPrintTask.getPrintConcentration());
        }

        printerConcentrationService.saveOrUpdate(printerConcentrationNew);

        // 查询默认材质浓度数据
        AsAdminPrinterConcentration defaultPrinterConcentration = printerConcentrationService.getOne(
                Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getIsDefault, true)
                        .eq(AsAdminPrinterConcentration::getUserId, currentUserId)
                        .orderByDesc(AsAdminPrinterConcentration::getUpdateTime), false);
        if (ObjectUtil.isNull(defaultPrinterConcentration)) {
            printerConcentrationNew.setIsDefault(true);
            defaultPrinterConcentration = printerConcentrationNew;
        }

        // 设置默认标签
        if (DictConstant.PRINT_TYPE_ASSET == userPrintTask.getPrintType()) {
            defaultPrinterConcentration.setDefaultTagId(userPrintTask.getTagId());
        } else if (DictConstant.PRINT_TYPE_MATERIAL == userPrintTask.getPrintType()) {
            defaultPrinterConcentration.setDefaultCftagId(userPrintTask.getTagId());
        }

        printerConcentrationService.saveOrUpdate(defaultPrinterConcentration);

    }

    /**
     * 检查任务状态和标签、标签尺寸
     *
     * @param tagId     标签id
     * @param printType 打印类型
     * @return 用户标签和标签尺寸
     */
    private Tuple2<AsUserTag, AsTagSize> checkTaskTag(Long tagId, Short printType) {
        AsUserTag userTag = userTagService.getOneById(tagId, printType);
        if (userTag == null) {
            throw new BusinessException(SystemResultCode.TAG_NOT_EXISTS);
        }
        AsTagSize tagSize = tagSizeService.getById(userTag.getSizeId());
        if (tagSize == null) {
            throw new BusinessException(SystemResultCode.TAG_SIZE_NOT_EXISTS);
        }
        return new Tuple2<>(userTag, tagSize);
    }

    /**
     * 配置任务名称，默认取首条资产名称
     */
    private void configTaskNameAndFailReason(List<AsUserPrintTask> list) {
        list.parallelStream().forEach(task -> {
            if (!(PrintTaskStatusEnum.EXCEPTION_PAUSE.getStatus() == task.getTaskStatus().intValue())) {
                return;
            }
            List<AsUserPrintLog> printLogs = userPrintLogService.list(
                    Wrappers.lambdaQuery(AsUserPrintLog.class)
                            .select(AsUserPrintLog::getPrintTaskId, AsUserPrintLog::getFailReason)
                            .eq(AsUserPrintLog::getPrintTaskId, task.getId())
                            .eq(AsUserPrintLog::getPrintStatus, 2)
                            .isNotNull(AsUserPrintLog::getFailReason)
                            .orderByDesc(AsUserPrintLog::getPrintTime)
                            .last("LIMIT 1")
            );
            if (CollUtil.isEmpty(printLogs)) {
                return;
            }
            task.setFailReason(printLogs.get(0).getFailReason());
        });
    }

    /**
     * 检查打印机名称
     *
     * @param printerName 打印机名称
     * @return 打印机
     */
    private AsAdminPrinter checkPrinterName(String printerName) {
        // 校验打印机
        AsAdminPrinter printer = adminPrinterService
                .getOne(Wrappers.<AsAdminPrinter>lambdaQuery().eq(AsAdminPrinter::getName, printerName));
        if (printer == null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_INVALID);
        }
        return printer;
    }

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务id
     * @return 任务
     */
    private AsUserPrintTask checkTask(Long taskId) {
        AsUserPrintTask byId = this.getById(taskId);
        if (byId == null) {
            throw new BusinessException(SystemResultCode.PRINT_TASK_NOT_EXIST);
        }
        return byId;
    }

    /**
     * 获取打印数据label
     *
     * @param userTag 标签
     * @param tagSize 标签尺寸
     * @return 打印label数据
     */
    private PrintLabelDto getLabel(AsUserPrintTask printTask, AsUserTag userTag, AsTagSize tagSize) {
        PrintLabelDto printLabelDto = BeanUtil.copyProperties(userTag, PrintLabelDto.class);
        String companyName = userTag.getCompanyName();
        if (StrUtil.isBlank(companyName)) {
            AsCompany company = companyService.getById(LoginUserThreadLocal.getCompanyId());
            companyName = company.getName();
        }
        printLabelDto.setCompanyName(companyName)
                .setTagMaterialId(printTask.getTagMaterialId())
                .setPrintConcentration(printTask.getPrintConcentration())
                .setSizeWide(tagSize.getSizeWide() + "mm")
                .setSizeLong(tagSize.getSizeLong() + "mm").setQrcodePosition(tagSize.getQrcodePosition())
                .setHeight(tagSize.getSizeLong()).setWidth(tagSize.getSizeWide())
                .setCodeVal(userTag.getCodeVal())
                .setIsCustomWords(userTag.getIsCustomWords())
                // 判断是否支持打印RFID，5 是材质表 as_tag_material RFID记录的主键。
                .setApplyRfid((ObjectUtil.isNotNull(printTask) && ObjectUtil.isNotNull(printTask.getTagMaterialId())) ? 5 == printTask.getTagMaterialId() : userTag.getApplyRfid());
        return printLabelDto;
    }

    /**
     * 更改上一次打印任务异常情况-打印任务状态
     *
     * @param lastTaskId  上一次打印任务
     * @param lastAssetId 上一次打印最后一次资产
     */
    private void checkLastPrintTask(Long lastTaskId, Long lastAssetId) {
        if (Objects.nonNull(lastTaskId) && Objects.nonNull(lastAssetId)) {
            AsUserPrintTask byId = this.getById(lastTaskId);
            // 异常打印中的更新打印状态为异常
            if (byId != null && byId.getTaskStatus().intValue() == PrintTaskStatusEnum.PRINTING.getStatus()) {
                this.update(Wrappers
                        .<AsUserPrintTask>lambdaUpdate()
                        .set(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.EXCEPTION_PAUSE.getStatus())
                        .eq(AsUserPrintTask::getId, lastTaskId));
            }
        }
    }

    /**
     * 根据打印端判断是否有任务正在执行
     *
     * @param printSource 打印端
     */
    public void checkIfAnyProgressingTask(Short printType, Integer printSource) {
        // 仅处理pc端
        if (printSource == 1) {
            List<AsUserPrintTask> list = this.list(Wrappers
                    .<AsUserPrintTask>lambdaQuery()
                    .eq(AsUserPrintTask::getPrintType, printType)
                    .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId())
                    .eq(ObjectUtil.isNotNull(printSource), AsUserPrintTask::getPrintSource, printSource)
                    .eq(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PRINTING.getStatus()));
            if (CollUtil.isNotEmpty(list)) {
                throw new BusinessException(SystemResultCode.PRINT_TASK_PRINTING_EXIST);
            }
        } else {
            // ios android pda 如果有正在进行的，设置为异常暂停
            LambdaUpdateWrapper<AsUserPrintTask> eq = Wrappers.<AsUserPrintTask>lambdaUpdate()
                    .set(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.EXCEPTION_PAUSE.getStatus())
                    .eq(AsUserPrintTask::getPrintType, printType)
                    .eq(AsUserPrintTask::getPrintSource, printSource)
                    .eq(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PRINTING.getStatus())
                    .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId());
            this.update(eq);
        }
    }

    private PrintDataViewDto dealSnapshotData(AsUserPrintTask printTask, PrintDataDto printDataDto, PrintQueryDataDto queryDto) {
        PrintDataViewDto dataViewDto = new PrintDataViewDto().setTaskId(printTask.getId());
        // JSONObject templateJson = getPrintTemplateJson(printDataDto.getPrintJsonName());
        JSONObject templateJson = getPrintJson(printDataDto.getTagSizeId());
        long start = System.currentTimeMillis();
        printStrategyMap.get(printTask.getPrintType()).resolvePrintData(printTask, queryDto);
        log.info("处理打印数据共耗时：" + (System.currentTimeMillis() - start));
        Long taskId = IdUtils.getId();
        List<Long> dataIds = printTask.getAssets();
        if (CollUtil.isEmpty(dataIds)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前任务没有待打印的数据");
        }
        // 覆盖
        printTask.setAssetNum(dataIds.size());
        // 设置任务名称
        printTask.setTaskName(getPrintTaskName(printTask.getAssetsSnapshot(), printTask.getPrintType()));
        // 置空快照信息
        printTask.setId(taskId).setAssetsSnapshot(Collections.emptyList()).setAssets(Collections.emptyList());
        this.save(printTask);
        // 单独保存快照信息
        long startTime = System.currentTimeMillis();
        // 只保存数据ID集合
        printDataSnapshotService.convertIdSave(taskId, (int) printTask.getPrintType(), dataIds);
        log.info("保存快照信息耗时：" + (System.currentTimeMillis() - startTime));
        updateUserExt(printTask);
        dataViewDto.setTaskId(printTask.getId());
        dataViewDto.setLabel(printDataDto.getPrintLabelDto());
        dataViewDto.setTemplateJson(templateJson);
        dataViewDto.setAttrData(printDataDto.getAttrData());
        dataViewDto.setTaskSize(dataIds.size());
        return dataViewDto;
    }

    private String getPrintTaskName(List<JSONObject> snapshot, short printType) {
        StringJoiner joiner = new StringJoiner("、");
        if (CollUtil.isNotEmpty(snapshot)) {
            for (int i = 0; i < snapshot.size(); i++) {
                if (i == 3) {
                    break;
                }
                JSONObject jsonObject = snapshot.get(i);
                if (DictConstant.PRINT_TYPE_ASSET == printType) {
                    Optional.ofNullable(jsonObject.getString("assetName")).ifPresent(joiner::add);
                }
                if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
                    Optional.ofNullable(jsonObject.getString("materialName")).ifPresent(joiner::add);
                }
                if (DictConstant.PRINT_TYPE_AREA == printType) {
                    Optional.ofNullable(jsonObject.getString("areaName")).ifPresent(joiner::add);
                }
                if (DictConstant.PRINT_TYPE_POINT == printType) {
                    Optional.ofNullable(jsonObject.getString("pointName")).ifPresent(joiner::add);
                }
            }
        }
        return joiner.length() > 0 ? joiner.toString() : "";
    }

    private JSONObject getPrintJson(Long tagSizeId) {
        AsTagSize tagSize = tagSizeService.getOne(
                Wrappers.lambdaQuery(AsTagSize.class)
                        .select(AsTagSize::getTplId)
                        .eq(AsTagSize::getId, tagSizeId)
        );
        AsPrintTemplate template = printTemplateService.getOne(
                Wrappers.lambdaQuery(AsPrintTemplate.class)
                        .select(AsPrintTemplate::getPrintJson)
                        .eq(AsPrintTemplate::getId, tagSize.getTplId())
        );
        if (Objects.isNull(template)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "尺寸对应的打印模板不存在");
        }
        return template.getPrintJson();
    }

    private JSONObject getPrintTemplateJson(String jsonName) {
        InputStream resourceAsStream = Thread.currentThread()
                .getContextClassLoader()
                .getResourceAsStream(PRINT_JSON_PATH + "/" + jsonName);
        if (resourceAsStream == null) {
            throw new BusinessException(SystemResultCode.PRINT_DATA_JSON_NOT_FOUND);
        }
        String read = IoUtil.read(resourceAsStream, Charsets.UTF_8);
        return JSON.parseObject(read);
    }

    @Override
    @SuppressWarnings("all")
    public Tuple2<Date, Date> getSearchDate(List<String> printTimes) {
        Date beginDate = null;
        Date endDate = null;
        if (CollUtil.isNotEmpty(printTimes)) {
            try {
                String beginStr = printTimes.get(0) + " 00:00:00";
                beginDate = DateUtil.parse(beginStr, "yyyy-MM-dd HH:mm:ss").toJdkDate();
            } catch (Exception e) {
                // ignore
            }
            try {
                String endStr = printTimes.get(1) + " 23:59:59";
                endDate = DateUtil.parse(endStr, "yyyy-MM-dd HH:mm:ss").toJdkDate();
            } catch (Exception e) {
                // ignore
            }
        }
        return new Tuple2<>(beginDate, endDate);
    }

    @Override
    public Boolean changeAllTaskStatus(Short printType, Integer type) {
        // 进行中的任务数
        Set<Long> ids = this.list(
                Wrappers.lambdaQuery(AsUserPrintTask.class)
                        .select(AsUserPrintTask::getId)
                        .eq(AsUserPrintTask::getPrintType, printType)
                        .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId())
                        .in(AsUserPrintTask::getPrintSource, 1, 2, 3)
                        .eq(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PRINTING.getStatus())
        ).stream().map(AsUserPrintTask::getId).collect(Collectors.toSet());
        LambdaUpdateWrapper<AsUserPrintTask> updateWrapper = Wrappers.lambdaUpdate(AsUserPrintTask.class)
                .set(AsUserPrintTask::getIsClear, true)
                .eq(AsUserPrintTask::getPrintType, printType)
                .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId());
        switch (type) {
            // 清空所有客户端未完成的任务
            case 0:
                updateWrapper
                        .ne(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.DONE.getStatus())
                        .in(AsUserPrintTask::getPrintSource, 1, 2, 3);
                if (CollUtil.isNotEmpty(ids)) {
                    updateWrapper.notIn(AsUserPrintTask::getId, ids);
                }
                break;
            // 暂停PC端所有任务
            case 1:
                return this.update(Wrappers.<AsUserPrintTask>lambdaUpdate()
                        .set(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PAUSE.getStatus())
                        .eq(AsUserPrintTask::getPrintType, printType)
                        .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                        .eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId())
                        .in(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PRINTING.getStatus(), PrintTaskStatusEnum.WAITING.getStatus())
                        .eq(AsUserPrintTask::getPrintSource, 1)
                );
            // 清空所有客户端已完成的任务
            case 2:
                updateWrapper
                        .eq(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.DONE.getStatus())
                        .in(AsUserPrintTask::getPrintSource, 1, 2, 3);
                if (CollUtil.isNotEmpty(ids)) {
                    updateWrapper.notIn(AsUserPrintTask::getId, ids);
                }
                break;
            default:
                return true;
        }
        return this.update(updateWrapper);
    }

    @Override
    public List<AsUserPrintTask> down(PrintTaskQueryDto dto) {
        LambdaQueryWrapper<AsUserPrintTask> queryWrapper = getQueryWrapper(dto);
        if (!BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            queryWrapper.eq(AsUserPrintTask::getUserId, LoginUserThreadLocal.getCurrentUserId());
        }
        queryWrapper.orderByDesc(AsUserPrintTask::getPrintTime)
                .orderByDesc(AsUserPrintTask::getCreateTime);
        List<AsUserPrintTask> list = this.list(queryWrapper);
        configTaskNameAndFailReason(list);
        return list;
    }

    @Override
    public AsUserPrintTask getOneByMapper(Long id) {
        return this.getBaseMapper().getOneByMapper(id);
    }

    @Override
    public Boolean checkAuthEquipmentThirdParty(ThirdPartyCheckDto thirdPartyCheckDto) {
        // 判断设备是否开启认证
        AsAdminPrinter adminPrinter = adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery().eq(AsAdminPrinter::getModel,
                thirdPartyCheckDto.getPrinterName()));
        if (null == adminPrinter) {
            return true;
        }
        if (!adminPrinter.getEnableAuth()) {
            return true;
        }

        List<AsAdminAuthEquipment> list = adminAuthEquipmentService.list(Wrappers
                .<AsAdminAuthEquipment>lambdaQuery().eq(AsAdminAuthEquipment::getEquipmentName,
                        thirdPartyCheckDto.getPrinterName())
        );
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        return list.stream().anyMatch(equipment -> StringUtils.equalsAnyIgnoreCase(thirdPartyCheckDto.getCode(), Optional
                        .ofNullable(equipment.getSerialNumber()).orElse(""),
                Optional.ofNullable(equipment.getCompanyId()).map(Convert::toStr).orElse("")));
    }

    @Override
    public JSONObject getPrintJsonByTagSizeId(Long tagSizeId) {
        return getPrintJson(tagSizeId);
    }

    @Override
    public Boolean isSupportRfid(AsUserPrintTask printTask, String type) {
        if (!printTask.getPrintType().equals(DictConstant.PRINT_TYPE_ASSET)) {
            return true;
        }
        AsAdminPrinter printer = checkPrinterName(printTask.getPrinterName());
        // 资产打印时当前打印机支持RFID而材质不支持时弹窗提示
        if ("all".equals(type) || "rfid".equals(type)) {
            AsTagMaterial tagMaterial = tagMaterialService.getById(printTask.getTagMaterialId());
            if (Objects.isNull(tagMaterial)) {
                throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "当前材质不存在");
            }
            if (printTask.getPrintType().equals(DictConstant.PRINT_TYPE_ASSET) && printer.getIsRfid() && !tagMaterial.getIsRfid()) {
                throw new BusinessException(SystemResultCode.PRINTER_SUPPORT_RFID);
            }
        }
        if ("all".equals(type) || "tag".equals(type)) {
            // 校验唯一性
            if (Objects.nonNull(printTask.getTagId())) {
                AsUserTag userTag = checkTaskTag(printTask.getTagId(), printTask.getPrintType()).getFirst();
                // 如果为空代表是ID值
                if (StrUtil.isBlank(userTag.getCodeVal()) || "assetId".equals(userTag.getCodeVal())) {
                    return true;
                }
                if (userTag.getIsCustomWords()) {
                    throw new BusinessException(SystemResultCode.CURRENT_PRINT_QR_CODE, "自定义文案");
                }
                FormVO tpl = formService.assetTpl();
                Optional<FormFieldCO> optional = tpl.getFormFields().stream()
                        .filter(v -> v.getFieldCode().equalsIgnoreCase(userTag.getCodeVal()))
                        .findFirst();
                if (!optional.isPresent()) {
                    throw new BusinessException(SystemResultCode.CURRENT_PRINT_QR_CODE, userTag.getCodeVal());
                }
                FormFieldCO field = optional.get();
                // 如果是这三种类型那么这个字段就是唯一的
                if (ListUtil.of(FormFieldCO.YZC_ASSET_SERIALNO, FormFieldCO.YZC_MATERIAL_SERIALNO).contains(field.getFieldType())) {
                    return true;
                }
                // 如果不是上述三种类型就判断它是否有唯一属性标识 或 是自定义文案
                if (!field.getFieldProps().containsKey("unique") || BooleanUtil.isFalse(field.getFieldProps().getBoolean("unique"))) {
                    throw new BusinessException(SystemResultCode.CURRENT_PRINT_QR_CODE, field.getFieldName());
                }
            }
        }
        return true;
    }

    @Override
    public Boolean changeAssetTagQrCodeValue(Long tagId) {
        return userTagService.update(
                Wrappers.lambdaUpdate(AsUserTag.class)
                        .set(AsUserTag::getCodeVal, "assetCode")
                        .set(AsUserTag::getIsCustomWords, false)
                        .eq(AsUserTag::getId, tagId)
        );
    }

    /**
     * 获取任务列表查询对象
     *
     * @param dto dto
     * @return 查询对象
     */
    private LambdaQueryWrapper<AsUserPrintTask> getQueryWrapper(PrintTaskQueryDto dto) {
        Tuple2<Date, Date> searchDate = getSearchDate(dto.getPrintTimes());
        return Wrappers
                .<AsUserPrintTask>lambdaQuery()
                .eq(Objects.nonNull(dto.getPrintType()), AsUserPrintTask::getPrintType, dto.getPrintType())
                .eq(AsUserPrintTask::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(CollUtil.isNotEmpty(dto.getTaskStatusList()), AsUserPrintTask::getTaskStatus, dto.getTaskStatusList())
                .ne(Objects.equals(true, dto.getIsGoing()), AsUserPrintTask::getTaskStatus,
                        PrintTaskStatusEnum.DONE.getStatus())
                .le(Objects.nonNull(searchDate.getSecond()), AsUserPrintTask::getPrintTime, searchDate.getSecond())
                .ge(Objects.nonNull(searchDate.getFirst()), AsUserPrintTask::getPrintTime, searchDate.getFirst());
        // .and(StrUtil.isNotBlank(dto.getKw()), wrapper -> wrapper
        //         .like(AsUserPrintTask::getTaskName, dto.getKw()));
    }

    /**
     * 清除状态为打印中的异常数据
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void abnormalPrintTaskDataClean() {
        //预发环境定时任务不启动，因为预发环境和线上共用数据库，定时任务会产生影响
        if (Edition.isUat()) {
            return;
        }

        this.update(
                Wrappers.lambdaUpdate(AsUserPrintTask.class)
                        .set(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.EXCEPTION_PAUSE.getStatus().shortValue())
                        .eq(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.PRINTING.getStatus().shortValue())
                        .and(asUserPrintTaskLambdaUpdateWrapper -> asUserPrintTaskLambdaUpdateWrapper.last("TIMESTAMPDIFF(DAY,print_time,CURRENT_TIMESTAMP) >= 1"))
        );
    }


    @Override
    public List<PrintDataViewDto.DataDto> getPrintJson(PrintPdfDto dto) {
        Long tagId = dto.getTagId();

        Tuple2<AsUserTag, AsTagSize> tuple2 = checkTaskTag(tagId, dto.getPrintType());
        PrintLabelDto printLabelDto = getLabel(new AsUserPrintTask(), tuple2.getFirst(), tuple2.getSecond());
        AsUserTag userTag = tuple2.getFirst();

        AsTagSize tagSize = tuple2.getSecond();
        Long tplId = tagSize.getTplId();
        if (Objects.isNull(tplId)) {
            throw new BusinessException(SystemResultCode.REOCRD_RESULT_CODE, "打印模板id不存在");
        }
        AsPrintTemplate printTemplate = printTemplateService.getById(tplId);
        if (Objects.isNull(printTemplate)) {
            throw new BusinessException(SystemResultCode.REOCRD_RESULT_CODE, "打印模板id不存在");
        }

        PrintDataDto printDataDto = new PrintDataDto().setPrintTaskId(null)
                .setPrintLabelDto(printLabelDto)
                .setPrintJsonName("")
                .setPrintSource((short) 1)
                .setStandardId(userTag.getStandardId())
                .setAttrData(userTag.getAttrData());

        List<PrintDataViewDto.DataDto> resultList = Lists.newCopyOnWriteArrayList();
        printStrategyMap.get(dto.getPrintType()).dealResultPrintTask(dto, printDataDto, printTemplate.getPrintJson(), resultList);

        return resultList;
    }

    @Override
    public List<JSONObject> getPrintData(PrintPdfDto dto) {
        Long tagId = dto.getTagId();

        Tuple2<AsUserTag, AsTagSize> tuple2 = checkTaskTag(tagId, dto.getPrintType());
        PrintLabelDto printLabelDto = getLabel(new AsUserPrintTask(), tuple2.getFirst(), tuple2.getSecond());
        AsUserTag userTag = tuple2.getFirst();

        AsTagSize tagSize = tuple2.getSecond();
        Long tplId = tagSize.getTplId();
        if (Objects.isNull(tplId)) {
            throw new BusinessException(SystemResultCode.REOCRD_RESULT_CODE, "打印模板id不存在");
        }
        AsPrintTemplate printTemplate = printTemplateService.getById(tplId);
        if (Objects.isNull(printTemplate)) {
            throw new BusinessException(SystemResultCode.REOCRD_RESULT_CODE, "打印模板id不存在");
        }

        PrintDataDto printDataDto = new PrintDataDto().setPrintTaskId(null)
                .setPrintLabelDto(printLabelDto)
                .setPrintJsonName("")
                .setPrintSource((short) 1)
                .setStandardId(userTag.getStandardId())
                .setAttrData(userTag.getAttrData());
        return printStrategyMap.get(dto.getPrintType()).getPrintData(dto, printDataDto);
    }

}
