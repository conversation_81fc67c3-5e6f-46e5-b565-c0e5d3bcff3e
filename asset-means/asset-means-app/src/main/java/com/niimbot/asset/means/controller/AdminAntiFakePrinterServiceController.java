package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.means.service.AdminAntiFakePrinterService;
import com.niimbot.system.AdminAntiFakePrinterDto;
import com.niimbot.system.AdminAntiFakeSerialInsertDto;
import com.niimbot.system.AdminAntiFakeSerialListDto;
import com.niimbot.system.AdminAntiFakeSerialQueryDto;
import com.niimbot.system.AdminAntiFakeSerialUpdateDto;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * 防伪控制器
 *
 * <AUTHOR>
 * @date 2020/12/17 17:17
 */
@RestController
@RequestMapping("server/system/antiPrinter")
@RequiredArgsConstructor
public class AdminAntiFakePrinterServiceController {
    private final AdminAntiFakePrinterService antiFakePrinterService;

    /**
     * 查询所有打印机数据
     *
     * @return 所有数据
     */
    @GetMapping("list")
    public List<AdminAntiFakePrinterDto> selectPrinterList() {
        return this.antiFakePrinterService.selectPrinterList();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public AdminAntiFakePrinterDto getByPrinterId(@PathVariable Long id) {
        return this.antiFakePrinterService.getByPrinterId(id);
    }

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @PostMapping
    public Boolean insertPrinter(@RequestBody AdminAntiFakePrinterDto dto) {
        return this.antiFakePrinterService.insertPrinter(dto);
    }

    /**
     * 修改数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PutMapping
    public Boolean updatePrinter(@RequestBody AdminAntiFakePrinterDto dto) {
        return this.antiFakePrinterService.updatePrinter(dto);
    }

    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping("{id}")
    public Boolean deletePrinter(@PathVariable Long id) {
        return this.antiFakePrinterService.deletePrinter(id);
    }

    /**
     * 查询所有打印机序列号列表
     *
     * @param printerId 打印机id
     * @return 所有数据
     */
    @GetMapping("/serial/list")
    public List<AdminAntiFakeSerialListDto> selectPrinterSerialList(@RequestParam Long printerId) {
        return this.antiFakePrinterService.selectPrinterSerialList(printerId);
    }

    /**
     * 查询所有打印机序列号列表
     *
     * @param dto page对象
     * @return 所有数据
     */
    @GetMapping(value = "/serial/page")
    public IPage<AdminAntiFakeSerialListDto> selectPrinterSerialPage(AdminAntiFakeSerialQueryDto dto) {
        return antiFakePrinterService.selectPrinterSerialPage(dto);
    }

    /**
     * 新增序列号数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PostMapping("/serial")
    public Boolean insertSerialNumber(@RequestBody AdminAntiFakeSerialInsertDto dto) {
        return this.antiFakePrinterService.insertSerialNumber(dto);
    }

    /**
     * 修改序列号数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @PutMapping("/serial")
    public Boolean updateSerialNumber(@RequestBody AdminAntiFakeSerialUpdateDto dto) {
        return this.antiFakePrinterService.updateSerialNumber(dto);
    }

    /**
     * 删除序列号
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping("/serial/{id}")
    public Boolean deleteSerial(@PathVariable Long id) {
       return this.antiFakePrinterService.deleteSerial(id);
    }
}
