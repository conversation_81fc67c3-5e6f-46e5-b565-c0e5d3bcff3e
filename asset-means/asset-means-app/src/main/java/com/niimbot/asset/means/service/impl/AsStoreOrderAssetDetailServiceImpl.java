package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsStoreOrderAssetDetailMapper;
import com.niimbot.asset.means.model.AsStoreOrderAssetDetail;
import com.niimbot.asset.means.service.AsStoreOrderAssetDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 资产入库单资产明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Service
public class AsStoreOrderAssetDetailServiceImpl extends ServiceImpl<AsStoreOrderAssetDetailMapper, AsStoreOrderAssetDetail> implements AsStoreOrderAssetDetailService {

}
