package com.niimbot.asset.means.controller;

import com.niimbot.asset.means.service.AssetReportService;
import com.niimbot.means.AssetIncrReportDto;
import com.niimbot.means.AssetOrgReportDto;
import com.niimbot.report.AssetReportDto;
import com.niimbot.report.AssetStatusReportDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("server/means/reports")
public class AssetReportServiceController {

    @Resource
    private AssetReportService assetReportService;

    /**
     * 首页-资产状态统计
     *
     * @return 资产状态统计对象
     */
    @GetMapping(value = "/assetStatusReport")
    public AssetReportDto<AssetStatusReportDto> assetStatusReport() {
        return assetReportService.assetStatusReport();
    }

    /**
     * 首页-资产增量统计
     *
     * @param year 年
     * @return 资产增量统计对象
     */
    @GetMapping(value = "/assetIncrReport/{year}")
    public AssetIncrReportDto assetIncrReport(@PathVariable("year") Long year) {
        return assetReportService.assetIncrReport(year);
    }

    /**
     * 首页-资产使用组织统计
     *
     * @param pid 父id
     * @return 资产使用组织统计对象
     */
    @GetMapping(value = "/assetReportByUseOrg")
    public List<AssetOrgReportDto> assetReportByUseOrg(
            @RequestParam(value = "orgId") Long rootId,
            @RequestParam(value = "pid", required = false) Long pid) {
        return assetReportService.assetReportByUseOrg(rootId, pid);
    }

    /**
     * 首页-资产分类统计
     *
     * @param pid 父id
     * @return 资产分类统计对象
     */
    @GetMapping(value = "/assetReportByCategory")
    public List<AssetOrgReportDto> assetReportByCategory(
            @RequestParam(value = "pid", required = false) Long pid) {
        return assetReportService.assetReportByCategory(pid);
    }

}
