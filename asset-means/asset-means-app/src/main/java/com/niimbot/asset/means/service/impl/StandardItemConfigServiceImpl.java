package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsStandardItemConfigMapper;
import com.niimbot.asset.means.model.AsStandardItemConfig;
import com.niimbot.asset.means.service.StandardItemConfigService;
import com.niimbot.means.StandardItemConfigDto;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/27 15:12
 */
@Service
public class StandardItemConfigServiceImpl extends ServiceImpl<AsStandardItemConfigMapper, AsStandardItemConfig> implements StandardItemConfigService {

    @Override
    public List<StandardItemConfigDto> formAttribute(Long standardId) {
        return this.getBaseMapper().formAttribute(standardId);
    }
}
