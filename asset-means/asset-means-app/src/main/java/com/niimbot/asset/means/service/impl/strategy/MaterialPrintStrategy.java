package com.niimbot.asset.means.service.impl.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.means.abs.MeansMaterialStockAbs;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.system.abs.FormAbs;
import com.niimbot.asset.system.abs.StandardAbs;
import com.niimbot.asset.system.dto.StandardExtFieldListQry;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialDto;
import com.niimbot.material.MaterialStockDto;
import com.niimbot.material.MaterialStockQueryDto;
import com.niimbot.means.PrintDataDto;
import com.niimbot.means.PrintDataViewDto;
import com.niimbot.means.PrintPdfDto;
import com.niimbot.means.PrintQueryDataDto;
import com.niimbot.system.TagAttrDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static cn.hutool.core.util.StrUtil.*;
import static java.util.stream.Collectors.toList;

/**
 * 耗材打印策略
 *
 * <AUTHOR>
 * @date 2021/11/25 14:38
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MaterialPrintStrategy implements PrintStrategy {
    private final MeansMaterialStockAbs meansMaterialStockAbs;
    private final FormAbs formAbs;
    private final StandardAbs standardAbs;
    private final AssetUtil assetUtil;

    @Override
    public Short printType() {
        return DictConstant.PRINT_TYPE_MATERIAL;
    }

    @Override
    public void resolvePrintData(AsUserPrintTask printTask,
                                 PrintQueryDataDto queryDto) {
        // 获取耗材数据
        MaterialStockQueryDto stockQueryDto = (MaterialStockQueryDto) queryDto;
        stockQueryDto.setPageSize(50000);
        IPage<Long> pageIds = this.meansMaterialStockAbs.stockPageIds(stockQueryDto);
        List<Long> materialIds = pageIds.getRecords();
        if (CollUtil.isEmpty(materialIds)) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_MATERIAL_NOT_EXIST);
        }
        printTask.setAssets(materialIds);
        printTask.setQueryData((JSONObject) JSONObject.toJSON(stockQueryDto));
        // 为了设置任务名称
        List<Long> idsForTaskName = materialIds.size() >= 3 ? materialIds.subList(0, 3) : materialIds;
        printTask.setAssetsSnapshot(
                meansMaterialStockAbs.listByIds(idsForTaskName).stream().map(MaterialDto::getMaterialData).collect(toList())
        );
    }

    @Override
    public void translate(List<JSONObject> dataList, String companyName, Long standardId) {
        List<FormFieldCO> formFields = getFormFields(standardId);
        List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formFields);
        assetUtil.translatePrintAssetJson(dataList, translations, false);
        dataList.forEach(material ->
                material.putIfAbsent(COMPANY_NAME, companyName)
        );
    }

    @Override
    public void dealPrintSnapshot(List<AsPrintDataSnapshot> snapshots) {
        MaterialStockQueryDto dto = new MaterialStockQueryDto()
                .setIncludeMaterialIds(snapshots.stream().map(AsPrintDataSnapshot::getDataId).collect(toList()));
        dto.setPageNum(1L);
        dto.setPageSize(snapshots.size());
        ConcurrentMap<Long, MaterialStockDto> map = meansMaterialStockAbs.stockPage(dto).getRecords().stream().collect(Collectors.toConcurrentMap(MaterialStockDto::getMaterialId, v -> v));
        snapshots.parallelStream().forEach(snapshot -> {
            if (Objects.isNull(snapshot.getSnapshot())) {
                MaterialStockDto materialStockDto = map.get(snapshot.getDataId());
                materialStockDto.translate();
                snapshot.setSnapshot(materialStockDto.getMaterialData());
            }
        });
    }

    @Override
    public void dealResultPrintTask(PrintPdfDto dto,
                                    PrintDataDto printDataDto,
                                    JSONObject templateJson,
                                    List<PrintDataViewDto.DataDto> resultList) {
        for (JSONObject material : dto.getDataJson()) {
            JSONObject jsonObject = templateJson.clone();
            List<JSONObject> elements = jsonObject.getObject("elements", new TypeReference<List<JSONObject>>() {
            });
            List<String> valList = Lists.newArrayList();
            List<TagAttrDto> tagAttrList = Lists.newArrayList();
            for (TagAttrDto attrDatum : printDataDto.getAttrData()) {
                String attrCode = attrDatum.getAttrCode();
                String attrValue = blankToDefault(material.getString(attrCode), "");
                // 自定义属性 重名
                if (isBlank(attrValue) && attrCode.contains(",")) {
                    String[] list = attrCode.split(",");
                    for (String customIdCode : list) {
                        String string = material.getString(customIdCode);
                        if (StringUtils.isNotBlank(string)) {
                            attrValue = string;
                            break;
                        }
                    }
                }
                String val;
                String delimiterStr = getDelimiterStr(attrDatum.getDelimiterType());
                if (attrDatum.getIsShow()) {
                    String alias = isNotBlank(attrDatum.getAlias()) ?
                            attrDatum.getAlias() : attrDatum.getAttrName();
                    val = alias + delimiterStr + attrValue;
                } else {
                    val = attrValue;
                }

                if (StringUtils.isNotBlank(attrDatum.getCustomWords())) {
                    val = attrDatum.getCustomWords();
                }
                tagAttrList.add(attrDatum);
                valList.add(val);
            }

            String codeValue = String.valueOf(material.getLong("materialId"));
            if (StringUtils.isNotBlank(printDataDto.getPrintLabelDto().getCodeVal())) {
                String attrCode = printDataDto.getPrintLabelDto().getCodeVal();
                if (printDataDto.getPrintLabelDto().getIsCustomWords()) {
                    codeValue = attrCode;
                } else {
                    codeValue = blankToDefault(material.getString(attrCode), "");
                    // 自定义属性 重名
                    if (isBlank(codeValue) && attrCode.contains(",")) {
                        String[] list = attrCode.split(",");
                        for (String customIdCode : list) {
                            String string = material.getString(customIdCode);
                            if (StringUtils.isNotBlank(string)) {
                                codeValue = string;
                                break;
                            }
                        }
                    }
                }
            }
            // 非text Node
            String finalCodeValue = codeValue;
            List<JSONObject> resElementJson = elements.stream()
                    .filter(el -> !"text".equals(el.getString("type")))
                    .peek(el -> {
                        if ("qrcode".equals(el.getString("type"))) {
                            el.put("value", finalCodeValue);
                        }
                    }).collect(toList());
            List<JSONObject> textList = elements.stream()
                    .filter(el -> "text".equals(el.getString("type"))).collect(toList());
            for (int i = 0; i < textList.size(); i++) {
                JSONObject el = textList.get(i);
                String valToUse;
                try {
                    valToUse = valList.get(i);
                } catch (IndexOutOfBoundsException e) {
                    log.error("打印----> 打印标签属性数据与json模板数据不一致，请检查");
                    valToUse = "";
                }
                el.put("value", valToUse);

                // 写入lineMode
                TagAttrDto tagAttr;
                try {
                    tagAttr = tagAttrList.get(i);
                    el.put("lineMode", tagAttr.getLineMode());
                } catch (IndexOutOfBoundsException e) {
                    log.error("打印----> 打印标签属性数据与json模板数据不一致，请检查");
                }
            }
            resElementJson.addAll(textList);
            jsonObject.put("elements", resElementJson);
            PrintDataViewDto.DataDto dataDto = new PrintDataViewDto.MaterialDataDto();
            dataDto.setLabelEpcid(material.getString("labelEpcid"));
            dataDto.setId(material.getLong("materialId")).setJsonObject(JSON.toJSONString(jsonObject));
            resultList.add(dataDto);
        }
    }

    @Override
    public List<JSONObject> getPrintData(PrintPdfDto dto, PrintDataDto printDataDto) {
        // 耗材查询
        MaterialStockQueryDto queryDto = BeanUtil.copyProperties(dto, MaterialStockQueryDto.class);
        queryDto.setMaterialIds(dto.getIds());
        IPage<MaterialStockDto> materialStockDtoIPage = meansMaterialStockAbs.stockPage(queryDto);
        List<JSONObject> dataList = materialStockDtoIPage.getRecords().stream().map(MaterialStockDto::translate).collect(toList());
        translate(dataList, printDataDto.getPrintLabelDto().getCompanyName(), printDataDto.getStandardId());
        return dataList;
    }

    private List<FormFieldCO> getFormFields(Long standardId) {
        List<FormFieldCO> fields = new ArrayList<>();
        FormVO formVO = formAbs.getTplByType(AsFormService.BIZ_TYPE_MATERIAL);
        fields.addAll(formVO.getFormFields());
        Long formId = formAbs.getTplByType(FormAbs.BIZ_TYPE_MATERIAL).getFormId();
        List<FormFieldCO> standardExtFields = standardAbs.getStandardExtField(
                new StandardExtFieldListQry(formId, standardId));
        fields.addAll(standardExtFields);
        return fields;
    }
}
