package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.means.mapper.AsAdminAuthEquipmentMapper;
import com.niimbot.asset.means.model.AsAdminAuthEquipment;
import com.niimbot.asset.means.service.AdminAuthEquipmentService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AdminAuthEquipmentCompanyInsertDto;
import com.niimbot.system.AdminAuthEquipmentCompanyUpdateDto;
import com.niimbot.system.AdminAuthEquipmentDetailListDto;
import com.niimbot.system.AdminAuthEquipmentDto;
import com.niimbot.system.AdminAuthEquipmentQueryDto;
import com.niimbot.system.AdminAuthEquipmentSerialInsertDto;
import com.niimbot.system.AdminAuthEquipmentSerialUpdateDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 设备授权
 *
 * <AUTHOR>
 * @date 2020/12/17 17:14
 */
@Service
public class AdminAuthEquipmentServiceImpl extends ServiceImpl<AsAdminAuthEquipmentMapper, AsAdminAuthEquipment>
        implements AdminAuthEquipmentService {

    @Resource
    private CompanyService companyService;

    @Override
    public List<AdminAuthEquipmentDto> selectEquipmentList() {
        return this.getBaseMapper().selectEquipmentList();
    }

    @Override
    public AdminAuthEquipmentDto getByEquipmentId(Long id) {
        return this.getBaseMapper().getByEquipmentId(id);
    }

    @Override
    public Boolean insertEquipment(AdminAuthEquipmentDto dto) {
        String equipmentName = dto.getEquipmentName();
        AsAdminAuthEquipment one = getOne(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getEquipmentName, equipmentName)
                .last("limit 1"));
        if (null != one) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_NAME_EXIST);
        }
        AsAdminAuthEquipment equipment = BeanUtil.copyProperties(dto, AsAdminAuthEquipment.class);
        equipment.setEquipmentId(IdUtils.getId());
        return save(equipment);
    }

    @Override
    public Boolean updateEquipment(AdminAuthEquipmentDto dto) {
        String equipmentName = dto.getEquipmentName();
        AsAdminAuthEquipment one = getOne(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getEquipmentName, equipmentName)
                .ne(AsAdminAuthEquipment::getEquipmentId, dto.getId())
                .last("limit 1"));
        if (null != one) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_NAME_EXIST);
        }
        return this.update(Wrappers.<AsAdminAuthEquipment>lambdaUpdate()
                .set(AsAdminAuthEquipment::getEquipmentName, dto.getEquipmentName())
                .set(StrUtil.isNotBlank(dto.getEquipmentDescribe()), AsAdminAuthEquipment::getEquipmentDescribe, dto.getEquipmentDescribe())
                .set(AsAdminAuthEquipment::getAuthorizationType, dto.getAuthorizationType())
                .eq(AsAdminAuthEquipment::getEquipmentId, dto.getId())
        );
    }

    @Override
    public Boolean deleteEquipment(Long id) {
        this.getBaseMapper()
                .delete(Wrappers.<AsAdminAuthEquipment>lambdaQuery().eq(AsAdminAuthEquipment::getEquipmentId, id));
        return true;
    }

    @Override
    public List<AdminAuthEquipmentDetailListDto> selectDetailList(Long equipmentId) {
        AdminAuthEquipmentDto equipmentDto = getByEquipmentId(equipmentId);
        if (null == equipmentDto) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_NOT_EXIST);
        }
        // 设备序列号
        AdminAuthEquipmentQueryDto dto = new AdminAuthEquipmentQueryDto().setEquipmentId(equipmentId);
        if (equipmentDto.getAuthorizationType().intValue() == 1) {
            return this.getBaseMapper().selectDetailSerialPage(dto);
        }
        return this.getBaseMapper().selectDetailCompanyPage(dto);
    }

    @Override
    public IPage<AdminAuthEquipmentDetailListDto> selectDetailPage(AdminAuthEquipmentQueryDto dto) {
        AdminAuthEquipmentDto equipmentDto = getByEquipmentId(dto.getEquipmentId());
        if (null == equipmentDto) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_NOT_EXIST);
        }
        // 设备序列号
        if (equipmentDto.getAuthorizationType().intValue() == 1) {
            return this.getBaseMapper().selectDetailSerialPage(dto.buildIPage(), dto);
        }
        return this.getBaseMapper().selectDetailCompanyPage(dto.buildIPage(), dto);
    }

    @Override
    public Boolean insertCompany(AdminAuthEquipmentCompanyInsertDto dto) {
        List<AsAdminAuthEquipment> list = list(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getEquipmentId, dto.getEquipmentId())
                .eq(AsAdminAuthEquipment::getAuthorizationType, 2));
        if (CollUtil.isEmpty(list)) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_NOT_EXIST);
        }
        AsCompany company = companyService.getById(dto.getCompanyId());
        if (company == null) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        boolean exist = list.stream().anyMatch(eq -> eq.existCompany(dto));
        if (exist) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_COMPANY_EXIST);
        }
        Optional<AsAdminAuthEquipment> first = list.stream().filter(eq -> ObjectUtil.isNull(eq.getCompanyId()))
                .findFirst();
        if (first.isPresent()) {
            AsAdminAuthEquipment cur = first.get();
            return this.update(Wrappers.<AsAdminAuthEquipment>lambdaUpdate()
                    .set(AsAdminAuthEquipment::getCompanyId, dto.getCompanyId())
                    .eq(AsAdminAuthEquipment::getId, cur.getId()));
        }
        AsAdminAuthEquipment newEq = list.get(0);
        newEq.setId(null).setCompanyId(dto.getCompanyId());
        return this.save(newEq);
    }

    @Override
    public Boolean updateCompany(AdminAuthEquipmentCompanyUpdateDto dto) {
        AsAdminAuthEquipment authComp = getOne(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getId, dto.getId()));
        if (authComp == null) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_AUTHINFO_NOT_EXIST);
        }
        AsCompany company = companyService.getById(dto.getCompanyId());
        if (company == null) {
            throw new BusinessException(SystemResultCode.USER_COMPANY_NOT_EXIST);
        }
        AsAdminAuthEquipment one = getOne((Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .ne(AsAdminAuthEquipment::getId, dto.getId())
                .eq(AsAdminAuthEquipment::getEquipmentId, authComp.getEquipmentId())
                .eq(AsAdminAuthEquipment::getCompanyId, dto.getCompanyId())));
        if (one != null) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_COMPANY_EXIST);
        }
        return this.update(Wrappers.<AsAdminAuthEquipment>lambdaUpdate()
                .set(AsAdminAuthEquipment::getCompanyId, dto.getCompanyId())
                .eq(AsAdminAuthEquipment::getId, dto.getId()));
    }

    @Override
    public Boolean insertSerial(AdminAuthEquipmentSerialInsertDto dto) {
        List<AsAdminAuthEquipment> serial = list(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getEquipmentId, dto.getEquipmentId())
                .eq(AsAdminAuthEquipment::getAuthorizationType, 1));
        if (CollUtil.isEmpty(serial)) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_NOT_EXIST);
        }
        boolean serialExist = serial.stream().anyMatch(eq -> StringUtils.equals(dto.getSerialNumber(), eq.getSerialNumber()));
        if (serialExist) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_AUTH_CODE_EXIST);
        }
        Optional<AsAdminAuthEquipment> first = serial.stream().filter(eq -> StrUtil.isBlank(eq.getSerialNumber()))
                .findFirst();
        if (first.isPresent()) {
            AsAdminAuthEquipment cur = first.get();
            return this.update(Wrappers.<AsAdminAuthEquipment>lambdaUpdate()
                    .set(AsAdminAuthEquipment::getSerialNumber, dto.getSerialNumber())
                    .eq(AsAdminAuthEquipment::getId, cur.getId()));
        }
        AsAdminAuthEquipment newEq = serial.get(0);
        newEq.setId(null).setSerialNumber(dto.getSerialNumber());
        return this.save(newEq);
    }

    @Override
    public Boolean updateSerial(AdminAuthEquipmentSerialUpdateDto dto) {
        AsAdminAuthEquipment authSerial = getOne(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getId, dto.getId()));
        if (authSerial == null) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_AUTHINFO_NOT_EXIST);
        }
        AsAdminAuthEquipment one = getOne((Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .ne(AsAdminAuthEquipment::getId, dto.getId())
                .eq(AsAdminAuthEquipment::getEquipmentId, authSerial.getEquipmentId())
                .eq(AsAdminAuthEquipment::getSerialNumber, dto.getSerialNumber())));
        if (one != null) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_AUTH_CODE_EXIST);
        }
        return this.update(Wrappers.<AsAdminAuthEquipment>lambdaUpdate()
                .set(AsAdminAuthEquipment::getSerialNumber, dto.getSerialNumber())
                .eq(AsAdminAuthEquipment::getId, dto.getId()));
    }

    @Override
    public Boolean deleteSerial(Long id) {
        AsAdminAuthEquipment equipment = this.getOne(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getId, id));
        if (equipment == null) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_AUTHINFO_NOT_EXIST);
        }
        // 当前设备列表
        List<AsAdminAuthEquipment> list = this.list(Wrappers.<AsAdminAuthEquipment>lambdaQuery()
                .eq(AsAdminAuthEquipment::getEquipmentId, equipment.getEquipmentId()));
        if (list.size() > 1) {
            return this.removeById(id);
        }
        return this.update(Wrappers.<AsAdminAuthEquipment>lambdaUpdate()
                .set(AsAdminAuthEquipment::getCompanyId, null)
                .set(AsAdminAuthEquipment::getSerialNumber, null)
                .set(AsAdminAuthEquipment::getId, IdUtils.getId())
                .eq(AsAdminAuthEquipment::getId, id)
        );
    }

    @Override
    public AdminAuthEquipmentDetailListDto detailInfo(Long id) {
        AsAdminAuthEquipment byId = this.getById(id);
        String code;
        if (byId.getAuthorizationType().intValue() == 1) {
            code = byId.getSerialNumber();
        } else {
            code = Convert.toStr(byId.getCompanyId(), "");
        }
        return new AdminAuthEquipmentDetailListDto().setId(id).setCode(code);
    }
}
