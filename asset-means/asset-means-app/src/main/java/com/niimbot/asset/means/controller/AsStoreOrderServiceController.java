package com.niimbot.asset.means.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.AuditableCreateOrderResult;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.activiti.WorkflowExecuteDto;
import com.niimbot.activiti.WorkflowExecuteStepDto;
import com.niimbot.activiti.WorkflowStartDto;
import com.niimbot.asset.activiti.model.ActWorkflow;
import com.niimbot.asset.activiti.service.ActApproveRoleMemberService;
import com.niimbot.asset.activiti.service.ActWorkflowService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.ActivitiResultCode;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsOrderType;
import com.niimbot.asset.means.model.AsStoreOrder;
import com.niimbot.asset.means.model.AsStoreOrderAssetDetail;
import com.niimbot.asset.means.model.AsStoreOrderSummaryDetail;
import com.niimbot.asset.means.service.AsOrderService;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.asset.means.service.AsStoreOrderAssetDetailService;
import com.niimbot.asset.means.service.AsStoreOrderService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.model.AsCusEmployee;
import com.niimbot.asset.system.service.AsCusEmployeeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AsOrderQueryDto;
import com.niimbot.means.AsStoreOrderDto;
import com.niimbot.means.AsStoreOrderSubmitDto;
import com.niimbot.means.AsStoreOrderSummaryDetailDto;
import com.niimbot.means.AssetAddLogDto;
import com.niimbot.means.StoreDetailPageQueryDto;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/10/27 10:17
 */
@Slf4j
@RestController
@RequestMapping("server/means/storeOrder")
@RequiredArgsConstructor
public class AsStoreOrderServiceController {
    private final AsStoreOrderService storeOrderService;
    private final AsOrderService orderService;
    private final AssetService assetService;
    private final AsOrderTypeService orderTypeService;
    private final ActWorkflowService workflowService;
    private final AsCusEmployeeService cusEmployeeService;
    private final AsStoreOrderAssetDetailService storeOrderAssetDetailService;
    @Resource
    private ActApproveRoleMemberService approveRoleMemberService;

    @Resource
    private RedisService redisService;

    @Resource
    private MessageService messageService;

    @PostMapping("/workflowStepList")
    public WorkflowExecuteDto getWorkflowStepList(
            LoginUserDto loginUserDto, @RequestBody AsStoreOrderDto dto) {
        WorkflowExecuteDto result = new WorkflowExecuteDto();
        String uuid = UUID.fastUUID().toString();
        result.setConditionId(uuid);
        loginUserDto.getCusUser().setOrgId(dto.getOrgId());
        // 查询单据类型
        AsOrderType orderType =
                orderTypeService.getOne(
                        Wrappers.<AsOrderType>lambdaQuery()
                                .eq(AsOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                                .eq(AsOrderType::getType, dto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), dto.getOrgId(), false);
        if (workflow != null) {
            JSONObject orderData = dto.getOrderData();
            List<String> categoryList = new ArrayList<>();
            List<String> areaList = new ArrayList<>();
            List<String> orgOwnerList = new ArrayList<>();
            BigDecimal totalMoney = BigDecimal.ZERO;

            List<AsStoreOrderSummaryDetailDto> summaryDetail = dto.getSummaryDetail();
            Set<Long> managerOwnerSet = new HashSet<>();
            for (AsStoreOrderSummaryDetailDto detailDto : summaryDetail) {
                BigDecimal price = detailDto.getStorePrice() == null ? BigDecimal.ZERO : detailDto.getStorePrice();
                BigDecimal multiply = price.multiply(new BigDecimal(detailDto.getQuantity()));
                totalMoney = totalMoney.add(multiply);

                JSONObject assetData = detailDto.getAssetSnapshotData();
                String assetCategory = assetData.getString("assetCategory");
                if (StrUtil.isNotEmpty(assetCategory)) {
                    categoryList.add(assetCategory);
                }
                String storageArea = assetData.getString("storageArea");
                if (StrUtil.isNotEmpty(storageArea)) {
                    areaList.add(storageArea);
                }
                String orgOwner = assetData.getString("orgOwner");
                if (StrUtil.isNotEmpty(orgOwner)) {
                    orgOwnerList.add(orgOwner);
                }
                if (managerOwnerSet.size() < 20) {
                    Long manageOwner = assetData.getLong("managerOwner");
                    if (manageOwner != null) {
                        managerOwnerSet.add(manageOwner);
                    }
                }
            }
            JSONObject condition = new JSONObject();
            if (CollUtil.isNotEmpty(categoryList)) {
                condition.put(QueryFieldConstant.ASSET_CATEGORY, categoryList);
            }
            if (CollUtil.isNotEmpty(areaList)) {
                condition.put(QueryFieldConstant.ASSET_AREA, areaList);
            }
            if (CollUtil.isNotEmpty(orgOwnerList)) {
                condition.put(QueryFieldConstant.ASSET_ORG_OWNER, orgOwnerList);
            }
            condition.put(QueryFieldConstant.PURCHASE_TOTAL_MONEY, totalMoney);
            condition.put(QueryFieldConstant.SUBMITTER_ORG, CollUtil.union(ListUtil.of(LoginUserThreadLocal.getCurrentUserId(), dto.getOrgId()), approveRoleMemberService.getEmpRoleIds(LoginUserThreadLocal.getCurrentUserId())));
            orderData.putAll(condition);
            orderData.put(QueryFieldConstant.ASSET_MANAGER_OWNER, new ArrayList<>(managerOwnerSet));

            String redisKey = RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.hSetAll(redisKey, condition);
            redisService.expire(redisKey, 2, TimeUnit.HOURS);
            result.setWorkflowExecuteStepDto(workflowService.preStartWorkflow(loginUserDto, workflow, orderData, null));
            String stepKey = RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), uuid);
            redisService.set(stepKey, result.getWorkflowExecuteStepDto());
            redisService.expire(stepKey, 2, TimeUnit.HOURS);
        }
        return result;
    }

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AuditableCreateOrderResult create(LoginUserDto loginUserDto, @RequestBody AsStoreOrderSubmitDto dto) {
        AsStoreOrderDto storeOrderDto = dto.getOrderDto();
        orderService.verify(storeOrderDto);
        orderService.translation(storeOrderDto);
        loginUserDto.getCusUser().setOrgId(storeOrderDto.getOrgId());
        // 查询单据类型
        AsOrderType orderType =
                orderTypeService.getOne(
                        Wrappers.<AsOrderType>lambdaQuery()
                                .eq(AsOrderType::getCompanyId, loginUserDto.getCusUser().getCompanyId())
                                .eq(AsOrderType::getType, storeOrderDto.getOrderType()));
        ActWorkflow workflow = workflowService.getWorkflow(orderType.getActivitiKey(), storeOrderDto.getOrgId(), false);
        // 保存入库单
        storeOrderService.create(storeOrderDto, workflow != null);
        if (workflow != null) {
            if (dto.getConditionId() == null) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_COMMON_ERROR, "流程执行条件Id不能为空");
            }
            if (dto.getExecuteStepDtoList().isEmpty()) {
                throw new BusinessException(ActivitiResultCode.WORKFLOW_EXECUTE_STEP_IS_EMPTY);
            }
            // 写入审批人
            List<WorkflowExecuteStepDto> executeStepList = Convert.toList(WorkflowExecuteStepDto.class, redisService.get(
                    RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId())));
            Map<Long, List<WorkflowExecuteStepDto.WorkflowApprover>> submitExecuteStepList = dto.getExecuteStepDtoList().stream()
                    .collect(Collectors.toMap(WorkflowExecuteStepDto::getStepId, WorkflowExecuteStepDto::getApproverList, (k1, k2) -> k1));
            for (WorkflowExecuteStepDto stepDto : executeStepList) {
                if (submitExecuteStepList.containsKey(stepDto.getStepId())) {
                    stepDto.setApproverList(submitExecuteStepList.get(stepDto.getStepId()));
                }
            }
            AsCusEmployee employee = cusEmployeeService.getById(loginUserDto.getCusUser().getId());
            String userName = employee == null ? loginUserDto.getCusUser().getAccount() : employee.getEmpName();

            JSONObject orderData = storeOrderDto.getOrderData();
            Map<Object, Object> map = redisService.hGetAll(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            if (map != null) {
                Map<String, Object> conditionMap = Convert.toMap(String.class, Object.class, map);
                orderData.putAll(conditionMap);
            }
            workflowService.startWorkflow(
                    new WorkflowStartDto()
                            .setUserDto(loginUserDto.getCusUser())
                            .setTitle(getTitle(userName))
                            .setActivitiKey(orderType.getActivitiKey())
                            .setBusinessType(orderType.getType().shortValue())
                            .setWorkflowId(workflow.getId())
                            .setVersion(workflow.getVersion())
                            .setCallbackUrl(workflow.getCallbackUrl())
                            .setBusinessId(storeOrderDto.getId())
                            .setFormData(orderData)
                            .setExecuteStepDtoList(executeStepList));
            redisService.del(RedisConstant.activitiCondition(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
            redisService.del(RedisConstant.activitiStep(LoginUserThreadLocal.getCompanyId(), dto.getConditionId()));
        } else {
            // 预处理资产
            List<AsAsset> handledAssets = new ArrayList<>();
            List<AsAsset> assetList =
                    storeOrderDto.getAssetDetail().stream()
                            .map(it ->
                                    new AsAsset()
                                            .setStandardId(it.getStandardId())
                                            .setAssetData(it.getAssetSnapshotData()))
                            .collect(Collectors.toList());
            List<AsAsset> assetListWithSid = assetList.stream()
                    .filter(o -> ObjectUtil.isNotNull(o.getStandardId()))
                    .collect(Collectors.toList());
            List<AsAsset> assetListWithoutSid = assetList.stream()
                    .filter(o -> ObjectUtil.isNull(o.getStandardId()))
                    .collect(Collectors.toList());
            Map<Long, List<AsAsset>> assetWithSidMap = assetListWithSid.stream().collect(
                    Collectors.groupingBy(AsAsset::getStandardId));
            assetWithSidMap.forEach((standardId, assets) -> {
                assetService.preAddHandleForStore(assets, standardId, true);
            });
            assetService.preAddHandleForStore(assetListWithoutSid, null, true);
            if (CollUtil.isNotEmpty(assetListWithSid)) {
                handledAssets.addAll(assetListWithSid);
            }
            if (CollUtil.isNotEmpty(assetListWithoutSid)) {
                handledAssets.addAll(assetListWithoutSid);
            }

            // 处理资产编码
            List<AsStoreOrderAssetDetail> assetDetail = handledAssets.parallelStream()
                    .map(asset -> {
                        AsStoreOrderAssetDetail detailDto = new AsStoreOrderAssetDetail();
                        detailDto.setStandardId(asset.getStandardId());
                        detailDto.setAssetSnapshotData(asset.getAssetData());
                        detailDto.setStoreOrderId(storeOrderDto.getId());
                        return detailDto;
                    })
                    .collect(Collectors.toList());
            storeOrderAssetDetailService.saveBatch(assetDetail);
            // 资产入库
//        taskExecutor.submit(() -> {
            AssetAddLogDto addLogDto = new AssetAddLogDto();
            addLogDto.setActionContent(storeOrderDto.getOrderData().getString(AsStoreOrderDto.RK_TYPE))
//                    2023年7月13日18:43:40 这里actionType改为记录入库类型，非单据类型
//                    2024年1月16日 10点13分 改回单据类型
                    .setActionType(AssetConstant.ORDER_TYPE_STORE)
//                    .setActionType(AssetConstant.OPT_ADD)
                    .setOrderId(storeOrderDto.getId())
                    .setOrderNo(storeOrderDto.getOrderNo());
            assetService.add(assetList, addLogDto, storeOrderDto.getOrderNo());
//        });
            // 入库单无审批时
            messageService.sendInnerMessage(MsgSendCmd.cgwc(LoginUserThreadLocal.getCompanyId(), storeOrderDto.getId(), String.valueOf(OrderFormTypeEnum.STORE.getCode())));
        }
        return new AuditableCreateOrderResult(storeOrderDto.getId(), storeOrderDto.getOrderNo());
    }

    private String getTitle(String userName) {
        return String.format("%s提交的入库单", userName);
    }

    @PostMapping("/processCallback")
    public Boolean processCallback(@RequestBody WorkflowCallbackDto callbackDto) {
        return storeOrderService.processCallback(callbackDto);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/{id}")
    public AsStoreOrderDto getById(@PathVariable("id") Long id) {
        return storeOrderService.getDetailById(id);
    }

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "/page")
    public IPage<AsStoreOrder> page(@RequestBody AsOrderQueryDto query) {
        return storeOrderService.page(query);
    }

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "/list")
    public List<AsStoreOrder> list(@RequestBody AsOrderQueryDto query) {
        return storeOrderService.list(query);
    }

    /**
     * 汇总明细查询
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/listSummaryDetailsByOrderId")
    public List<AsStoreOrderSummaryDetail> listSummaryDetailsByOrderId(@RequestBody Collection<Long> ids) {
        return storeOrderService.listSummaryDetailsByOrderId(ids);
    }

    /**
     * 汇总明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "/pageSummaryDetail")
    public IPage<AsStoreOrderSummaryDetail> pageSummaryDetail(@SpringQueryMap StoreDetailPageQueryDto dto) {
        return storeOrderService.pageSummaryDetail(dto);
    }

    /**
     * 资产明细分页
     *
     * @param dto dto
     * @return 结果
     */
    @GetMapping(value = "/pageAssetDetail")
    public IPage<AsStoreOrderAssetDetail> pageAssetDetail(@SpringQueryMap StoreDetailPageQueryDto dto) {
        return storeOrderService.pageAssetDetail(dto);
    }
}
