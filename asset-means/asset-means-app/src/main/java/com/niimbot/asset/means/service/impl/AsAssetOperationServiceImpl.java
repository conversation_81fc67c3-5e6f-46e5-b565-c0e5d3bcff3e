package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsAssetOperationMapper;
import com.niimbot.asset.means.model.AsAssetOperation;
import com.niimbot.asset.means.service.AsAssetOperationService;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/3/12 16:13
 */
@Service
public class AsAssetOperationServiceImpl extends ServiceImpl<AsAssetOperationMapper, AsAssetOperation> implements AsAssetOperationService {
}
