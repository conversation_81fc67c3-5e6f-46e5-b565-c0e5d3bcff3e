package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsUserPrintTask;

/**
 * <p>
 * 打印任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
public interface AsUserPrintTaskMapper extends BaseMapper<AsUserPrintTask> {

    /**
     * 根据id查询打印任务 - 删除或未删除的
     *
     * @param id 任务id
     * @return 实体
     */
    AsUserPrintTask getOneByMapper(Long id);
}
