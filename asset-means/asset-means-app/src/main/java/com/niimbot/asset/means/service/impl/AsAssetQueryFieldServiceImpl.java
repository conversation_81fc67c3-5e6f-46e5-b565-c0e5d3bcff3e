package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.means.mapper.AsAssetQueryFieldMapper;
import com.niimbot.asset.means.model.AsAssetQueryField;
import com.niimbot.asset.means.model.AsQueryType;
import com.niimbot.asset.means.service.AsAssetQueryFieldService;
import com.niimbot.system.QueryTypeDto;

import org.springframework.beans.BeanUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 资产查询字段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@Service
@RequiredArgsConstructor
public class AsAssetQueryFieldServiceImpl extends ServiceImpl<AsAssetQueryFieldMapper, AsAssetQueryField> implements AsAssetQueryFieldService, ApplicationListener<ApplicationReadyEvent> {

    private final RedisService redisService;
    private final ThreadPoolTaskExecutor taskExecutor;

    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return ;
        }

        taskExecutor.execute(() -> {
            List<AsAssetQueryField> queryFields = list();
            Map<String, List<AsQueryType>> collect = queryFields.stream()
                    .collect(Collectors.toMap(AsAssetQueryField::getType, AsAssetQueryField::getOperators, (k1, k2) -> k1));
            redisService.hSetAll(RedisConstant.assetQueryFieldDictKey(), collect);
        });
    }

    @Override
    public Map<String, List<AsQueryType>> getOperatorMap() {
        Map<Object, Object> map = redisService.hGetAll(RedisConstant.assetQueryFieldDictKey());
        Map<String, List<AsQueryType>> result = new HashMap<>();
        map.forEach((k, v) -> result.put(Convert.toStr(k), Convert.toList(AsQueryType.class, v)));
        return result;
    }

    @Override
    public List<AsQueryType> getOperators(String type) {
        Object o = redisService.hGet(RedisConstant.assetQueryFieldDictKey(), type);
        BusinessExceptionUtil.checkNotNull(o, "字段没有可选操作符");
        return Convert.toList(AsQueryType.class, o);
    }

    @Override
    public Map<String, List<QueryTypeDto>> getOperator() {
        List<AsAssetQueryField> queryFields = this.list(Wrappers.<AsAssetQueryField>lambdaQuery().eq(AsAssetQueryField::getIsDelete, 0));
        return queryFields.stream().collect(Collectors.toMap(AsAssetQueryField::getType,
                item -> item.getOperators().stream().map(queryType -> {
                    QueryTypeDto queryTypeDto = new QueryTypeDto();
                    BeanUtils.copyProperties(queryType, queryTypeDto);
                    return queryTypeDto;
                }).collect(Collectors.toList())));
    }
}
