package com.niimbot.asset.means.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.utils.JsonUtil;
import com.niimbot.asset.means.model.AsStoreOrder;
import com.niimbot.asset.means.service.AsStoreOrderService;
import com.niimbot.asset.todo.model.AsTodo;
import com.niimbot.asset.todo.service.TodoOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/6 下午1:56
 */
@Slf4j
@Service
public class TodoAssetStoreOrderServiceImpl implements TodoOrderService {

    private static final List<Integer> supportTypeList = ListUtil.of(AssetConstant.ORDER_TYPE_STORE);

    @Autowired
    private AsStoreOrderService storeOrderService;

    @Override
    public Boolean supportOrderType(Integer orderType) {
        return supportTypeList.contains(orderType);
    }

    @Override
    public JSONObject getOrderJson(Long businessId) {
        AsStoreOrder storeOrder = storeOrderService.getById(businessId);
        if (Objects.isNull(storeOrder)) {
            return null;
        }

        JSONObject result = JsonUtil.toJsonObject(storeOrder, o -> {
            JSONObject orderData = o.getJSONObject("orderData");
            o.fluentPutAll(orderData);
            o.fluentRemove("orderData");
        });
        return result;
    }

    @Override
    public AsTodo getOrderTodo(AsTodo todo) {
        AsStoreOrder order = storeOrderService.getById(todo.getBusinessId());
        if (Objects.isNull(order)) {
            return null;
        }

        //如果orderData为空，优先设置下
        if (Objects.isNull(order.getOrderData())) {
            order.setOrderData(new JSONObject());
        }

        order.getOrderData().put("summary", order.getSummary());
        AsTodo result = new AsTodo()
                .setSummary(order.getSummary())
                .setCreateBy(order.getCreateBy())
                .setOrderData(JSONObject.toJSONString(order.getOrderData()));
        return result;
    }
}
