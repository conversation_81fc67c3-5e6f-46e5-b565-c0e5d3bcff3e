package com.niimbot.asset.means.domain.client.form;

import com.niimbot.asset.means.abs.PointAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/7/8 18:00
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.equipment.abs.impl.PointAbsImpl")
@FeignClient(name = "asset-means", url = "http://localhost:8000/")
public interface PointAbsClient extends PointAbs {
}
