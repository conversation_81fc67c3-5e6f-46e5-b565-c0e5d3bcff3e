package com.niimbot.asset.means.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsProductHabitsMapper;
import com.niimbot.asset.means.model.AsProductHabits;
import com.niimbot.asset.means.service.AsProductHabitsService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品使用习惯表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Service
public class AsProductHabitsServiceImpl extends ServiceImpl<AsProductHabitsMapper, AsProductHabits> implements AsProductHabitsService {

}
