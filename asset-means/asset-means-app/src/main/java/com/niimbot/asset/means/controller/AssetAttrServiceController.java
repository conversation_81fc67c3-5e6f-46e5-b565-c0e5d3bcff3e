package com.niimbot.asset.means.controller;

import com.niimbot.dynamicform.BizFormAssetConfig;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/12/9 14:08
 */
@RestController
@RequestMapping("server/system/assetAttr")
public class AssetAttrServiceController {

    /**
     * 查询报表属性下拉
     *
     * @return 资产属性列表
     */
    @GetMapping(value = "/reports/attr")
    public List<BizFormAssetConfig> reportAttrList() {
//        return bizFormAssetService.reportAttrList();
        // todo 报表
        return null;
    }

    /**
     * 通过编码查询名称
     *
     * @param code 编码
     * @return 名称
     */
    @GetMapping(value = "/name/{code}")
    public String codeToName(@PathVariable("code") String code) {
//        return bizFormAssetService.codeToName(code);
        // todo 报表
        return null;
    }

}
