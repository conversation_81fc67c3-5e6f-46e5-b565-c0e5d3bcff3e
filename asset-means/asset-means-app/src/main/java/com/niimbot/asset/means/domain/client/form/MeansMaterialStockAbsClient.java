package com.niimbot.asset.means.domain.client.form;

import com.niimbot.asset.means.abs.MeansMaterialStockAbs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@ConditionalOnMissingClass(value = "com.niimbot.asset.material.domain.abs.impl.MeansMaterialStockAbsImpl")
@FeignClient(name = "asset-material", url = "http://localhost:8000/")
public interface MeansMaterialStockAbsClient extends MeansMaterialStockAbs {
}
