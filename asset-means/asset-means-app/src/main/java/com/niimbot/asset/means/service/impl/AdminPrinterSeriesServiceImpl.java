package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsAdminPrinterSeriesMapper;
import com.niimbot.asset.means.service.AdminPrinterSeriesService;
import com.niimbot.asset.system.model.AsAdminPrinterSeries;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备系列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Service
public class AdminPrinterSeriesServiceImpl extends ServiceImpl<AsAdminPrinterSeriesMapper, AsAdminPrinterSeries> implements AdminPrinterSeriesService {
}
