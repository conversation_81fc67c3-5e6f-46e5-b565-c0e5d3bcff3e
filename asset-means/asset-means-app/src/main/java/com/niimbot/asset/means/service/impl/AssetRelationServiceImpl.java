package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.enums.AssetRelationTypeEnum;
import com.niimbot.asset.means.mapper.AsAssetRelationMapper;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.model.AsAssetRelation;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.means.service.AssetRelationService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.ots.SystemRecycleBinOts;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AlterAssetRelationDto;
import com.niimbot.means.AssetQueryConditionDto;
import com.niimbot.means.AssetRelationAppDto;
import com.niimbot.means.AssetRelationConfigDto;
import com.niimbot.means.AssetRelationDto;
import com.niimbot.means.AssetRelationItemDto;
import com.niimbot.means.AssetRelationQueryConditionDto;
import com.niimbot.means.CancelAssetRelationDto;
import com.niimbot.system.GetRecycleBins;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/8/8 上午10:50
 */
@Slf4j
@Service
public class AssetRelationServiceImpl extends ServiceImpl<AsAssetRelationMapper, AsAssetRelation> implements AssetRelationService {

    private static final String ASSET_LOG_TPL = "%s(%s)";

    private static final String CONFIG_RELATION_LOG_TPL = "主资产 %s 与子资产 %s 的关联关系由 空 变成 %s";

    private static final String REMOVE_RELATION_LOG_TPL = "主资产 %s 与子资产 %s 解除关联";

    private static final String ALTER_RELATION_LOG_TPL = "主资产 %s 与子资产 %s 的关联关系由 %s 变更为 %s";

    @Autowired
    private AssetService assetService;
    @Autowired
    private SystemRecycleBinOts systemRecycleBinService;
    @Autowired
    private AsAssetLogService assetLogService;

    @Override
    public List<Long> getExistAsset(Integer type) {
        if (1 == type) {
            return this.getBaseMapper().selectMainAssetId(LoginUserThreadLocal.getCompanyId());
        } else if (2 == type) {
            return this.getBaseMapper().selectSubAssetId(LoginUserThreadLocal.getCompanyId());
        } else {
            return this.getBaseMapper().selectAllAssetId(LoginUserThreadLocal.getCompanyId());
        }
    }

    @Override
    public Boolean config(AssetRelationConfigDto configDto) {
        //查询当前企业下所有主资产id
        List<Long> mainAssetIdList = this.getBaseMapper().selectMainAssetId(LoginUserThreadLocal.getCompanyId());

        //查询当前企业下所有子资产id
        List<Long> subAssetIdList = this.getBaseMapper().selectSubAssetId(LoginUserThreadLocal.getCompanyId());

        //校验当前主资产是否是已经存在子资产
        if (subAssetIdList.contains(configDto.getAssetId())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "主资产已是子资产");
        }

        //配置子资产id列表
        List<Long> subAssetIdConfigList = configDto.getSubAsset().stream().map(AssetRelationItemDto::getSubAssetId).collect(Collectors.toList());

        //校验主资产自己是作为自己的子资产
        if (subAssetIdConfigList.contains(configDto.getAssetId())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "主资产不允许关联自己");
        }

        //校验子资产是否作为主资产，或是否已作为主资产
        if (!Collections.disjoint(mainAssetIdList, subAssetIdConfigList)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "子资产已是主资产");
        }

        //校验子资产是否已作为子资产
        if (!Collections.disjoint(subAssetIdList, subAssetIdConfigList)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "子资产已存在关联关系");
        }

        //校验当前主资产是否有子资产，并且子资产不能超过200
        long subAssetCount = this.count(Wrappers.lambdaQuery(AsAssetRelation.class).eq(AsAssetRelation::getAssetId, configDto.getAssetId())
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId()));
        if (subAssetCount + configDto.getSubAsset().size() > 200) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "子资产不允许超过200，当前主资产已存在" + subAssetCount + "个子资产");
        }

        //资产关联关系
        List<AsAssetRelation> assetRelationList = configDto.getSubAsset().stream().map(item -> {
            AsAssetRelation data = new AsAssetRelation();
            data.setCompanyId(LoginUserThreadLocal.getCompanyId())
                    .setAssetId(configDto.getAssetId())
                    .setSubAssetId(item.getSubAssetId())
                    .setRelationType(item.getRelationType());
            return data;
        }).collect(Collectors.toList());
        this.saveBatch(assetRelationList);

        //保存资产系统操作日志
        saveConfigAssetRelationLog(configDto, subAssetIdConfigList);
        return Boolean.TRUE;
    }

    private void saveConfigAssetRelationLog(AssetRelationConfigDto configDto, List<Long> subAssetIdConfigList) {
        List<Long> assetIds = new ArrayList<>();
        assetIds.add(configDto.getAssetId());
        if (CollUtil.isNotEmpty(subAssetIdConfigList)) {
            assetIds.addAll(subAssetIdConfigList);
        }

        List<AsAssetLog> assetLogList = new ArrayList<>();

        //查询资产信息
        Map<Long, AsAsset> assetMap = queryAssetMap(assetIds);

        //主资产描述
        String mainAssetDesc = String.format(ASSET_LOG_TPL, assetMap.get(configDto.getAssetId()).getAssetName(), assetMap.get(configDto.getAssetId()).getAssetCode());
        AsAssetLog mainAssetLog = new AsAssetLog().setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setAssetId(configDto.getAssetId()).setActionType(AssetConstant.OPT_ASSET_RELATION).setActionName("新增组合").setHandleTime(LocalDateTime.now());
        assetLogList.add(mainAssetLog);

        StringBuilder mainAssetContent = new StringBuilder();
        for (AssetRelationItemDto relationItem : configDto.getSubAsset()) {
            String subAssetDesc = String.format(ASSET_LOG_TPL, assetMap.get(relationItem.getSubAssetId()).getAssetName(), assetMap.get(relationItem.getSubAssetId()).getAssetCode());

            String subAssetContent = String.format(CONFIG_RELATION_LOG_TPL, mainAssetDesc, subAssetDesc, AssetRelationTypeEnum.getByCode(relationItem.getRelationType()).getDesc());
            AsAssetLog subAssetLog = new AsAssetLog().setCompanyId(LoginUserThreadLocal.getCompanyId()).setHandleTime(LocalDateTime.now())
                    .setAssetId(relationItem.getSubAssetId()).setActionType(AssetConstant.OPT_ASSET_RELATION).setActionName("新增组合").setActionContent(subAssetContent);
            assetLogList.add(subAssetLog);

            mainAssetContent.append(subAssetContent).append(",");
        }
        mainAssetLog.setActionContent(mainAssetContent.toString());
        assetLogService.saveBatch(assetLogList);
    }

    /**
     * 获取资产信息分组
     * @param assetIds
     * @return
     */
    private Map<Long, AsAsset> queryAssetMap(List<Long> assetIds) {
        if (CollUtil.isEmpty(assetIds)) {
            return MapUtil.empty();
        }

        List<AsAsset> assetList = assetService.list(Wrappers.lambdaQuery(AsAsset.class)
                .in(AsAsset::getId, assetIds)
                .select(AsAsset::getId, AsAsset::getAssetCode, AsAsset::getAssetName));
        if (CollUtil.isEmpty(assetList)) {
            return MapUtil.empty();
        }

        return assetList.stream().collect(Collectors.toMap(AsAsset::getId, value -> value, (v1, v2) -> v2));
    }

    @Override
    public Boolean alterRelation(AlterAssetRelationDto assetRelationDto) {
        //查询资产关联关系
        List<AsAssetRelation> relationList = this.list(Wrappers.lambdaQuery(AsAssetRelation.class)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsAssetRelation::getAssetId, assetRelationDto.getAssetId())
                .in(AsAssetRelation::getSubAssetId, assetRelationDto.getSubAssetList()));
        if (CollUtil.isEmpty(relationList)) {
            return Boolean.TRUE;
        }

        this.update(Wrappers.lambdaUpdate(AsAssetRelation.class)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsAssetRelation::getAssetId, assetRelationDto.getAssetId())
                .in(AsAssetRelation::getSubAssetId, assetRelationDto.getSubAssetList())
                .set(AsAssetRelation::getRelationType, assetRelationDto.getRelationType()));

        //保存关联资产操作日志
        saveAlterRelationLog(assetRelationDto, relationList);
        return Boolean.TRUE;
    }

    private void saveAlterRelationLog(AlterAssetRelationDto assetRelationDto, List<AsAssetRelation> relationList) {
        List<Long> assetIds = new ArrayList<>();
        assetIds.add(assetRelationDto.getAssetId());
        if (CollUtil.isNotEmpty(assetRelationDto.getSubAssetList())) {
            assetIds.addAll(assetRelationDto.getSubAssetList());
        }

        List<AsAssetLog> assetLogList = new ArrayList<>();

        //查询资产信息
        Map<Long, AsAsset> assetMap = queryAssetMap(assetIds);

        //主资产描述
        String mainAssetDesc = String.format(ASSET_LOG_TPL, assetMap.get(assetRelationDto.getAssetId()).getAssetName(), assetMap.get(assetRelationDto.getAssetId()).getAssetCode());
        AsAssetLog mainAssetLog = new AsAssetLog().setCompanyId(LoginUserThreadLocal.getCompanyId()).setHandleTime(LocalDateTime.now())
                .setAssetId(assetRelationDto.getAssetId()).setActionType(AssetConstant.OPT_ASSET_RELATION).setActionName("修改关联关系");
        assetLogList.add(mainAssetLog);

        StringBuilder mainAssetContent = new StringBuilder();
        for (AsAssetRelation relationItem : relationList) {
            String subAssetDesc = String.format(ASSET_LOG_TPL, assetMap.get(relationItem.getSubAssetId()).getAssetName(), assetMap.get(relationItem.getSubAssetId()).getAssetCode());

            String subAssetContent = String.format(ALTER_RELATION_LOG_TPL, mainAssetDesc, subAssetDesc,
                    AssetRelationTypeEnum.getByCode(relationItem.getRelationType()).getDesc(),
                    AssetRelationTypeEnum.getByCode(assetRelationDto.getRelationType()).getDesc());
            AsAssetLog subAssetLog = new AsAssetLog().setCompanyId(LoginUserThreadLocal.getCompanyId()).setHandleTime(LocalDateTime.now())
                    .setAssetId(relationItem.getSubAssetId()).setActionType(AssetConstant.OPT_ASSET_RELATION).setActionName("修改关联关系").setActionContent(subAssetContent);
            assetLogList.add(subAssetLog);

            mainAssetContent.append(subAssetContent).append(",");
        }
        mainAssetLog.setActionContent(mainAssetContent.toString());
        assetLogService.saveBatch(assetLogList);
    }

    @Override
    public Boolean cancelRelation(CancelAssetRelationDto relationDto) {
        if (CollUtil.isEmpty(relationDto.getAssetIdList())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "主资产信息不能为空");
        }

        //删除关联关系
        return this.remove(Wrappers.lambdaQuery(AsAssetRelation.class)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(AsAssetRelation::getAssetId, relationDto.getAssetIdList()));
    }

    @Override
    public Boolean removeRelation(CancelAssetRelationDto relationDto) {
        if (CollUtil.isEmpty(relationDto.getAssetIdList())) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "主资产信息不能为空");
        }

        LambdaQueryWrapper<AsAssetRelation> wrapper = Wrappers.lambdaQuery(AsAssetRelation.class)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId()).eq(AsAssetRelation::getAssetId, relationDto.getAssetIdList().get(0));
        if (CollUtil.isNotEmpty(relationDto.getSubAssetIdList())) {
            wrapper.in(AsAssetRelation::getSubAssetId, relationDto.getSubAssetIdList());
        }
        this.remove(wrapper);
        //保存资产操作日志
        saveRemoveLog(relationDto.getAssetIdList().get(0), relationDto.getSubAssetIdList());
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelation(Long assetId) {
        if (Objects.isNull(assetId)) {
            return Boolean.FALSE;
        }

        List<AsAssetRelation> assetRelationList = this.list(Wrappers.lambdaQuery(AsAssetRelation.class)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .and(wrapper -> wrapper.eq(AsAssetRelation::getAssetId, assetId).or().eq(AsAssetRelation::getSubAssetId, assetId)));
        if (CollUtil.isEmpty(assetRelationList)) {
            return Boolean.TRUE;
        }

        //删除资产关联关系
        this.remove(Wrappers.lambdaQuery(AsAssetRelation.class)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .and(wrapper -> wrapper.eq(AsAssetRelation::getAssetId, assetId).or().eq(AsAssetRelation::getSubAssetId, assetId)));

        //保存资产操作日志
        saveRemoveLog(assetRelationList.get(0).getAssetId(), assetRelationList.stream().map(AsAssetRelation::getSubAssetId).collect(Collectors.toList()));
        return Boolean.TRUE;
    }

    /**
     * 新增资产解除关联操作日志
     * @param mainAssetId
     * @param subAssetIdList
     */
    private void saveRemoveLog(Long mainAssetId, List<Long> subAssetIdList) {
        List<Long> assetIds = new ArrayList<>();
        assetIds.add(mainAssetId);
        if (CollUtil.isNotEmpty(subAssetIdList)) {
            assetIds.addAll(subAssetIdList);
        }

        List<AsAssetLog> assetLogList = new ArrayList<>();

        //查询资产信息
        Map<Long, AsAsset> assetMap = queryAssetMap(assetIds);

        //主资产描述
        String mainAssetDesc = String.format(ASSET_LOG_TPL, assetMap.get(mainAssetId).getAssetName(), assetMap.get(mainAssetId).getAssetCode());
        AsAssetLog mainAssetLog = new AsAssetLog().setCompanyId(LoginUserThreadLocal.getCompanyId())
                .setAssetId(mainAssetId).setActionType(AssetConstant.OPT_ASSET_RELATION).setActionName("解除关联").setHandleTime(LocalDateTime.now());
        assetLogList.add(mainAssetLog);

        StringBuilder mainAssetContent = new StringBuilder();
        for (Long subAssetId : subAssetIdList) {
            String subAssetDesc = String.format(ASSET_LOG_TPL, assetMap.get(subAssetId).getAssetName(), assetMap.get(subAssetId).getAssetCode());
            mainAssetContent.append(String.format(subAssetDesc)).append(",");

            String subAssetContent = String.format(REMOVE_RELATION_LOG_TPL, mainAssetDesc, subAssetDesc);
            AsAssetLog subAssetLog = new AsAssetLog().setCompanyId(LoginUserThreadLocal.getCompanyId()).setHandleTime(LocalDateTime.now())
                    .setAssetId(subAssetId).setActionType(AssetConstant.OPT_ASSET_RELATION).setActionName("解除关联").setActionContent(subAssetContent);
            assetLogList.add(subAssetLog);
        }
        mainAssetLog.setActionContent(String.format(REMOVE_RELATION_LOG_TPL, mainAssetDesc, mainAssetContent));
        assetLogService.saveBatch(assetLogList);
    }

    @Override
    public IPage<AssetRelationDto> page(AssetQueryConditionDto queryDto) {
        //需要查询回收站里面的数据
        queryDto.getIncludeAssetIds().addAll(systemRecycleBinService.getResIdsForQuery(new GetRecycleBins().setCompanyId(LoginUserThreadLocal.getCompanyId()).setResType(1).setFormRecycle(queryDto.getFormRecycle())));
        IPage<AssetRelationDto> pageResult = assetService.relationPage(queryDto);
        if (Objects.nonNull(pageResult) && CollUtil.isNotEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(item -> {
                item.setRelationType(0);
                item.setRelationTypeText("主");
            });
        }
        return pageResult;
    }

    @Override
    public IPage<AssetRelationDto> subAsset(AssetRelationQueryConditionDto queryConditionDto) {
        //需要查询回收站里面的数据
        queryConditionDto.getIncludeAssetIds().addAll(systemRecycleBinService.getResIdsForQuery(new GetRecycleBins().setCompanyId(LoginUserThreadLocal.getCompanyId()).setResType(1).setFormRecycle(queryConditionDto.getFormRecycle())));
        //查询子资产的时候，防止前端带上了其他筛选条件，这里将条件置空下
        queryConditionDto.setKwFiled(null);
        IPage<AssetRelationDto> pageResult = assetService.subAssetPage(queryConditionDto);
        //设置关联关系
        if (Objects.nonNull(pageResult) && CollUtil.isNotEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(item -> {
                item.setRelationTypeText(AssetRelationTypeEnum.getByCode(item.getRelationType()).getDesc());
                item.setSubAssetCount(0);
            });
        }
        return pageResult;
    }

    @Override
    public IPage<AssetRelationAppDto> appPage(AssetQueryConditionDto queryDto) {
        //需要查询回收站里面的数据
        queryDto.getIncludeAssetIds().addAll(systemRecycleBinService.getResIdsForQuery(new GetRecycleBins().setCompanyId(LoginUserThreadLocal.getCompanyId()).setResType(1).setFormRecycle(queryDto.getFormRecycle())));
        IPage<AssetRelationAppDto> pageResult = assetService.relationPageApp(queryDto);
        if (Objects.nonNull(pageResult) && CollUtil.isNotEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(item -> {
                item.setRelationType(0);
                item.setRelationTypeText("主");
            });
        }
        return pageResult;
    }

    @Override
    public IPage<AssetRelationAppDto> subAssetAppPage(AssetRelationQueryConditionDto queryConditionDto) {
        //需要查询回收站里面的数据
        queryConditionDto.getIncludeAssetIds().addAll(systemRecycleBinService.getResIdsForQuery(new GetRecycleBins().setCompanyId(LoginUserThreadLocal.getCompanyId()).setResType(1).setFormRecycle(queryConditionDto.getFormRecycle())));
        IPage<AssetRelationAppDto> pageResult = assetService.subAssetPageApp(queryConditionDto);
        //设置关联关系
        if (Objects.nonNull(pageResult) && CollUtil.isNotEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(item -> {
                item.setRelationTypeText(AssetRelationTypeEnum.getByCode(item.getRelationType()).getDesc());
                item.setSubAssetCount(0);
            });
        }
        return pageResult;
    }

    @Override
    public AssetRelationAppDto getBySubAssetId(Long subAssetId) {
        Long companyId = LoginUserThreadLocal.getCompanyId();
        AsAssetRelation relation = this.getOne(Wrappers.lambdaQuery(AsAssetRelation.class)
                .eq(AsAssetRelation::getSubAssetId, subAssetId)
                .eq(AsAssetRelation::getCompanyId, companyId));
        if (Objects.isNull(relation)) {
            return null;
        }

        //查询主资产信息
        AssetRelationAppDto result = assetService.queryMainAssetInfoApp(relation.getAssetId(), relation.getSubAssetId(), companyId);
        if (Objects.nonNull(result)) {
            result.setRelationType(0);
            result.setRelationTypeText("主");
            result.setSubAssetCount(0);
        }
        return result;
    }

    @Override
    public List<Long> existSubAsset(List<Long> assetIdList) {
        if (CollUtil.isEmpty(assetIdList)) {
            return Collections.emptyList();
        }
        List<AsAssetRelation> assetRelationList = this.list(Wrappers.lambdaQuery(AsAssetRelation.class)
                .select(AsAssetRelation::getSubAssetId)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .in(AsAssetRelation::getSubAssetId, assetIdList));
        if (CollUtil.isEmpty(assetRelationList)) {
            return Collections.emptyList();
        } else {
            return assetRelationList.stream().map(AsAssetRelation::getSubAssetId).collect(Collectors.toList());
        }
    }

    @Override
    public Long getMainAssetId(Long assetId) {
        if (Objects.isNull(assetId)) {
            return null;
        }

        AsAssetRelation assetRelation = this.getOne(Wrappers.lambdaQuery(AsAssetRelation.class)
                .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                .eq(AsAssetRelation::getSubAssetId, assetId));
        //如果是子资产，直接返回主资产id
        if (Objects.nonNull(assetRelation)) {
            return assetRelation.getAssetId();
        } else {
            long subAssetCount = this.count(Wrappers.lambdaQuery(AsAssetRelation.class)
                    .eq(AsAssetRelation::getCompanyId, LoginUserThreadLocal.getCompanyId())
                    .eq(AsAssetRelation::getAssetId, assetId));
            if (subAssetCount > 0) {
                return assetId;
            }
        }
        return null;
    }
}
