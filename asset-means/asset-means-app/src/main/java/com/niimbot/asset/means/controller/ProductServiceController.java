package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsProductHabits;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.means.service.AsProductHabitsService;
import com.niimbot.asset.means.service.AsProductService;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AsProductDto;
import com.niimbot.means.AsProductInfoDto;
import com.niimbot.means.AsProductQueryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.ImportImages;
import com.niimbot.means.ProductListByIdQueryDto;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2021/5/12 14:07
 */
@RestController
@RequestMapping("server/means/product")
public class ProductServiceController {

    private final AsProductService productService;
    private final AsProductHabitsService habitsService;
    private final AsAssetImportErrorService assetImportErrorService;

    @Autowired
    public ProductServiceController(AsProductService productService,
                                    AsProductHabitsService habitsService,
                                    AsAssetImportErrorService assetImportErrorService) {
        this.productService = productService;
        this.habitsService = habitsService;
        this.assetImportErrorService = assetImportErrorService;
    }

    @PostMapping("/page")
    public IPage<LinkedHashMap<String, Object>> page(@RequestBody AsProductQueryDto queryDto) {
        return productService.customPage(queryDto);
    }

    @GetMapping("/list")
    public List<AsProductInfoDto> list(@RequestParam(value = "name", required = false) String name) {
        if (StrUtil.isBlank(name)) {
            return ListUtil.empty();
        }
        return productService.customList(name);
    }

    @GetMapping("/{id}")
    public AsProductInfoDto info(@PathVariable("id") Long id) {
        return productService.customInfo(id);
    }

    @PostMapping("/listById")
    public List<AsProductInfoDto> customInfos(@RequestBody ProductListByIdQueryDto queryDto) {
        return productService.customInfos(queryDto);
    }

    @PostMapping(value = "/select/{productId}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean select(@PathVariable("productId") Long productId) {
        Long userId = LoginUserThreadLocal.getCurrentUserId();
        habitsService.remove(new LambdaQueryWrapper<AsProductHabits>()
                .eq(AsProductHabits::getProductId, productId).eq(AsProductHabits::getEmpId, userId));
        AsProductHabits habits = new AsProductHabits();
        habits.setEmpId(userId);
        habits.setProductId(productId);
        return habitsService.save(habits);
    }

    @PostMapping
    public AsProductInfoDto add(@RequestBody AsProductDto productDto) {
        return this.productService.add(productDto);
    }

    @PutMapping
    public Boolean edit(@RequestBody AsProductDto productDto) {
        return this.productService.edit(productDto);
    }

    @DeleteMapping
    public Boolean delete(@RequestBody List<Long> ids) {
        return this.productService.delete(ids);
    }

    @DeleteMapping(value = "/importErrorAll/{taskId}")
    public Boolean importErrorDeleteAll(@PathVariable("taskId") Long taskId) {
        return assetImportErrorService.deleteByTaskId(taskId);
    }

    /**
     * 导入耗材数据
     *
     * @param importDto 导入数据
     */
    @PostMapping(value = "/saveSheetData")
    public Boolean saveSheetData(@RequestBody AssetImportDto importDto) {
        return productService.saveSheetData(importDto);
    }

    /**
     * 导入表头数据
     *
     * @param importErrorDto 导入表头
     */
    @PostMapping(value = "/saveSheetHead")
    public void saveSheetHead(@RequestBody HeadImportErrorDto importErrorDto) {
        productService.saveSheetHead(importErrorDto);
    }

    /**
     * 错误记录
     *
     * @return 对应的操作list
     */
    @GetMapping(value = "/importError/{taskId}")
    public List<List<LuckySheetModel>> importError(@PathVariable("taskId") Long taskId) {
        return productService.importError(taskId);
    }

    @PostMapping("/images/check/{action}")
    public List<ImportImages> importImagesCheck(@RequestBody List<ImportImages> codes, @PathVariable("action") Integer action) {
        if (CollUtil.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return productService.importImagesCheck(codes, action);
    }

    @PostMapping("/images/import/{action}")
    public List<ImportImages> importImages(@RequestBody List<ImportImages> images, @PathVariable("action") Integer action) {
        if (CollUtil.isEmpty(images)) {
            return Collections.emptyList();
        }
        return productService.importImages(images, action);
    }
}
