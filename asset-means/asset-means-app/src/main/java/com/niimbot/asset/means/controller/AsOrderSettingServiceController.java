package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsOrderField;
import com.niimbot.asset.means.model.AsOrderType;
import com.niimbot.asset.means.service.AsOrderFieldService;
import com.niimbot.asset.means.service.AsOrderTypeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.OrderTypeDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2020/12/22 15:25
 */
@RestController
@RequestMapping("server/means/orderSetting")
public class AsOrderSettingServiceController {
    private final AsOrderTypeService asOrderTypeService;
    private final AsOrderFieldService asOrderFieldService;

    @Autowired
    public AsOrderSettingServiceController(AsOrderTypeService asOrderTypeService,
                                           AsOrderFieldService asOrderFieldService) {
        this.asOrderTypeService = asOrderTypeService;
        this.asOrderFieldService = asOrderFieldService;
    }

    /**
     * 单据类型详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/orderType/getById/{id}")
    public AsOrderType getOrderTypeById(@PathVariable("id") Long id) {
        return asOrderTypeService.getOne(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, getCompanyId())
                        .eq(AsOrderType::getId, id));
    }

    /**
     * 单据类型列表
     *
     * @return
     */
    @GetMapping(value = "/orderType/list")
    public List<OrderTypeDto> listOrderType() {
        return asOrderTypeService.listOrderType();
    }

    /**
     * app客户单据类型列表查询
     *
     * @return
     */
    @GetMapping(value = "/orderType/app/list")
    public List<AsOrderType> appListOrderTypeShow() {
        return asOrderTypeService.list(
                Wrappers.<AsOrderType>lambdaQuery()
                        .eq(AsOrderType::getCompanyId, getCompanyId())
                        .eq(AsOrderType::getEnableApp, true));
    }

    @GetMapping("/orderType/enableWorkflow/{orderType}")
    public Boolean enableWorkflow(@PathVariable("orderType") Integer orderType) {
        return asOrderTypeService.enableWorkflow(orderType);
    }

    /**
     * 单据字段列表
     *
     * @param orderType
     * @return
     */
    @GetMapping(value = "/orderField/list/{orderType}")
    public List<AsOrderField> listOrderField(@PathVariable("orderType") Integer orderType) {
        return asOrderFieldService.list(
                Wrappers.<AsOrderField>lambdaQuery()
                        .eq(AsOrderField::getCompanyId, getCompanyId())
                        .eq(AsOrderField::getOrderType, orderType)
                        .orderByAsc(AsOrderField::getOrderType, AsOrderField::getSortNum));
    }

    /**
     * 根据单据类型查询动态表单字段列表
     *
     * @return
     */
    @GetMapping(value = "/dynamicField/list/{orderType}")
    public List<AsOrderField> listDynamicOrderField(@PathVariable("orderType") Integer orderType) {
        return asOrderFieldService.list(
                Wrappers.<AsOrderField>lambdaQuery()
                        .eq(AsOrderField::getCompanyId, getCompanyId())
                        .eq(AsOrderField::getOrderType, orderType)
                        .eq(AsOrderField::getIsShow, true)
                        .orderByAsc(AsOrderField::getOrderType, AsOrderField::getSortNum));
    }

    /**
     * 动态表单字段列表查询
     *
     * @return
     */
    @GetMapping(value = "/dynamicField/listAll")
    public List<AsOrderField> listAllDynamicOrderField() {
        return asOrderFieldService.list(
                Wrappers.<AsOrderField>lambdaQuery()
                        .eq(AsOrderField::getCompanyId, getCompanyId())
                        .orderByAsc(AsOrderField::getOrderType, AsOrderField::getSortNum));
    }

    /**
     * 更新单据类型
     *
     * @param orderType
     * @return
     */
    @PutMapping("/updateOrderType")
    public Boolean updateOrderType(@RequestBody AsOrderType orderType) {
        AsOrderType raw = getOrderTypeById(orderType.getId());
        if (raw == null) {
            throw new BusinessException(MeansResultCode.ORDER_TYPE_NOT_EXISTS, orderType.getName());
        }
        if (!asOrderTypeService.updateById(orderType)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 更新单据字段
     *
     * @param orderField
     * @return
     */
    @PutMapping("/updateOrderField")
    public Boolean updateOrderField(@RequestBody AsOrderField orderField) {
        AsOrderField field = checkFieldCompanyId(orderField);
        checkFieldName(getCompanyId(), orderField);
        if (!field.getIsShowCanEdit()) {
            orderField.setIsShow(null);
        }
        if (!field.getIsRequiredCanEdit()) {
            orderField.setIsRequired(null);
        } else {
            if (Boolean.TRUE.equals(orderField.getIsRequired())) {
                orderField.setIsShow(true);
            }
        }
        if (!asOrderFieldService.updateById(orderField)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 批量更新单据字段
     *
     * @param fields
     * @return
     */
    @PutMapping("/updateOrderField/batch")
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderFieldBatch(@RequestBody List<AsOrderField> fields) {
        AtomicInteger index = new AtomicInteger(1);
        Map<String, Long> groupByAlias = fields.stream().filter(field -> StrUtil.isNotBlank(field.getRename()))
                .collect(Collectors.groupingBy(AsOrderField::getRename, Collectors.counting()));
        groupByAlias.keySet().forEach(key -> {
            if (groupByAlias.get(key) > 1) {
                throw new BusinessException(SystemResultCode.PARAM_EXISTS, "字段别名", key);
            }
        });
        fields.forEach(field -> {
            AsOrderField fieldById = checkFieldCompanyId(field);
            checkFieldName(getCompanyId(), field);
            if (!fieldById.getIsShowCanEdit()) {
                field.setIsShow(null);
            }
            if (!fieldById.getIsRequiredCanEdit()) {
                field.setIsRequired(null);
            } else {
                if (Boolean.TRUE.equals(field.getIsRequired())) {
                    field.setIsShow(true);
                }
            }
            field.setSortNum(index.getAndIncrement());
        });
        if (!asOrderFieldService.updateBatchById(fields)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 排序更新单据字段
     *
     * @param fieldIds
     * @return
     */
    @PutMapping("/sortFields")
    @Transactional(rollbackFor = Exception.class)
    public Boolean sortFields(@RequestBody List<Long> fieldIds) {
        AtomicInteger index = new AtomicInteger(1);
        List<AsOrderField> fields = new ArrayList<>();
        for (Long fieldId : fieldIds) {
            if (getOrderFieldById(fieldId) == null) {
                throw new BusinessException(MeansResultCode.ORDER_FIELD_NOT_EXISTS, String.valueOf(fieldId));
            }
            fields.add(new AsOrderField().setId(fieldId).setSortNum(index.getAndIncrement()));
        }
        if (!asOrderFieldService.updateBatchById(fields)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        return true;
    }

    /**
     * 同步新增单据配置
     *
     * @param companyId
     * @return
     */
    @PutMapping("/sync/{companyId}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncCompany(@PathVariable("companyId") Long companyId) {
        return asOrderTypeService.syncCompanyOrderType(companyId);
    }

    private Long getCompanyId() {
        if (LoginUserThreadLocal.getCusUser() == null) {
            return 0L;
        }
        return LoginUserThreadLocal.getCompanyId();
    }

    private AsOrderField checkFieldCompanyId(AsOrderField field) {
        AsOrderField fieldById = getOrderFieldById(field.getId());
        if (fieldById == null) {
            throw new BusinessException(MeansResultCode.ORDER_FIELD_NOT_EXISTS, field.getName());
        }
        return fieldById;
    }

    private void checkFieldName(long companyId, AsOrderField field) {
        if (StrUtil.isNotBlank(field.getRename())
                && asOrderFieldService.count(
                Wrappers.<AsOrderField>lambdaQuery()
                        .eq(AsOrderField::getCompanyId, companyId)
                        .eq(AsOrderField::getRename, field.getRename())
                        .eq(AsOrderField::getOrderType, field.getOrderType())
                        .ne(AsOrderField::getId, field.getId())) > 0) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "字段别名", field.getRename());
        }
    }

    private AsOrderField getOrderFieldById(Long id) {
        return asOrderFieldService.getOne(
                Wrappers.<AsOrderField>lambdaQuery()
                        .eq(AsOrderField::getCompanyId, getCompanyId())
                        .eq(AsOrderField::getId, id));
    }
}
