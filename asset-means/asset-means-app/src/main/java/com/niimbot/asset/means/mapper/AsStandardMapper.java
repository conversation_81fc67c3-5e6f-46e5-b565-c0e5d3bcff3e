package com.niimbot.asset.means.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.means.model.AsStandard;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.StandardListDto;
import com.niimbot.means.StandardQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 标准品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-27
 */
@EnableDataPerm(excludeMethodName = {"cusPage", "cusList", "getByIdNoPerm", "listNoPerm", "hasRef"})
public interface AsStandardMapper extends BaseMapper<AsStandard> {

    IPage<StandardListDto> cusPage(Page<Object> buildIPage, @Param("ew") StandardQueryDto queryDto, @Param("companyIds") List<Long> companyIds);

    List<StandardListDto> cusList(@Param("name") String name, @Param("companyId") Long companyId);

    AsStandard getByIdNoPerm(@Param("standardId") Long standardId, @Param("companyId") Long companyId);

    List<AsStandard> listNoPerm(@Param(Constants.WRAPPER) Wrapper<AsStandard> wrapper);

    int hasRef(@Param("id") Long id, @Param("companyId") Long companyId);
}
