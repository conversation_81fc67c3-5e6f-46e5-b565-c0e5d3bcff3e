package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsOrderTypeMenuMapper;
import com.niimbot.asset.means.model.AsOrderTypeMenu;
import com.niimbot.asset.means.service.AsOrderTypeMenuService;
import com.niimbot.asset.system.model.AsCusMenu;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 单据类型-菜单关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Service
@RequiredArgsConstructor
public class AsOrderTypeMenuServiceImpl extends ServiceImpl<AsOrderTypeMenuMapper, AsOrderTypeMenu> implements AsOrderTypeMenuService {

    /**
     * 根据单据类型获取菜单
     * @param orderType 单据类型
     * @return
     */
    @Override
    public AsCusMenu getMenuById(Short orderType) {
        return this.baseMapper.getMenuById(orderType);
    }
}
