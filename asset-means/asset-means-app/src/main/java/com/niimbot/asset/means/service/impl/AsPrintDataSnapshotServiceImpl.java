package com.niimbot.asset.means.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsPrintDataSnapshotMapper;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.asset.means.service.AsPrintDataSnapshotService;
import com.niimbot.asset.means.service.impl.strategy.PrintStrategy;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.PrintTaskSnapshot;
import com.niimbot.means.UserPrintTaskGroupByStatusPageQuery;
import com.niimbot.means.UserPrintTaskGroupStatusResult;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 */
@Service
public class AsPrintDataSnapshotServiceImpl extends ServiceImpl<AsPrintDataSnapshotMapper, AsPrintDataSnapshot> implements AsPrintDataSnapshotService {

    private final CompanyService companyService;

    private final Map<Short, PrintStrategy> printStrategyMap;

    public AsPrintDataSnapshotServiceImpl(CompanyService companyService, List<PrintStrategy> printStrategies) {
        this.companyService = companyService;
        this.printStrategyMap = printStrategies.stream().collect(Collectors.toConcurrentMap(PrintStrategy::printType, v -> v));
    }

    @Override
    public void convertIdSave(Long taskId, Integer type, List<Long> ids) {
        AtomicInteger index = new AtomicInteger(1);
        List<AsPrintDataSnapshot> snapshots = ids.stream()
                .map(id -> new AsPrintDataSnapshot(taskId, type, index.getAndIncrement(), id))
                .collect(Collectors.toList());
        this.saveBatch(snapshots);
    }

    @Override
    public List<AsPrintDataSnapshot> getAll(Long taskId) {
        return this.list(
                Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                        .eq(AsPrintDataSnapshot::getTaskId, taskId)
                        .orderByAsc(AsPrintDataSnapshot::getSort)
        );
    }

    @Override
    public List<AsPrintDataSnapshot> getTaskSnaps(Integer startIndex, Integer endIndex, Long taskId) {
        return this.list(
                Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                        .eq(AsPrintDataSnapshot::getTaskId, taskId)
                        .between(AsPrintDataSnapshot::getSort, startIndex, endIndex)
                        .orderByAsc(AsPrintDataSnapshot::getSort)
        );
    }

    @Override
    public JSONObject getTaskSnapshot(Long taskId, Long dataId) {
        List<AsPrintDataSnapshot> snapshots = this.list(
                Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                        .select(AsPrintDataSnapshot::getSnapshot)
                        .eq(AsPrintDataSnapshot::getDataId, dataId)
                        .eq(AsPrintDataSnapshot::getTaskId, taskId)
                        .orderByAsc(AsPrintDataSnapshot::getSort)
        );
        if (CollUtil.isEmpty(snapshots)) {
            return null;
        }
        return snapshots.get(0).getSnapshot();
    }

    @Override
    public JSONObject getTaskTranslateSnapshot(Long taskId, Long dataId) {
        AsPrintDataSnapshot snapshot = this.getOne(
                Wrappers.lambdaQuery(AsPrintDataSnapshot.class)
                        .eq(AsPrintDataSnapshot::getTaskId, taskId)
                        .eq(AsPrintDataSnapshot::getDataId, dataId)
        );
        if (Objects.isNull(snapshot)) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_LOG_ASSET_DATA_NOT_EXIST);
        }
        if (snapshot.getIsTranslation() && Objects.nonNull(snapshot.getSnapshot())) {
            return snapshot.getSnapshot();
        }
        PrintStrategy printStrategy = printStrategyMap.get(snapshot.getType().shortValue());
        printStrategy.dealPrintSnapshot(Collections.singletonList(snapshot));
        JSONObject jsonObject = snapshot.getSnapshot();
        if (!jsonObject.containsKey("standardId")) {
            return jsonObject;
        }
        String companyName = companyService.getById(LoginUserThreadLocal.getCompanyId()).getName();
        printStrategy.translate(Collections.singletonList(jsonObject), companyName, jsonObject.getLong("standardId"));
        return jsonObject;
    }

    @Override
    public PrintTaskSnapshot getTaskSnapshot(Long taskId) {
        // 全部的包含未生成快照的数据
        List<AsPrintDataSnapshot> printDataSnapshots = getAll(taskId);
        // 全部的待打印数据ID集合
        List<Long> dataIds = printDataSnapshots.stream().map(AsPrintDataSnapshot::getDataId).collect(Collectors.toList());
        PrintTaskSnapshot snapshot = new PrintTaskSnapshot().setTaskId(taskId);
        snapshot.setDataIds(dataIds);
        // 已有的快照
        List<JSONObject> objects = new ArrayList<>();
        // 已有的快照数据ID与快照的映射
        Map<Long, JSONObject> map = new ConcurrentHashMap<>();
        printDataSnapshots.forEach(v -> {
            if (Objects.nonNull(v.getSnapshot())) {
                objects.add(v.getSnapshot());
                map.put(v.getDataId(), v.getSnapshot());
            }
        });
        snapshot.setSnapshots(objects);
        snapshot.setSnapshotMap(map);
        return snapshot;
    }

    @Override
    public IPage<UserPrintTaskGroupStatusResult> taskDetailsPage(UserPrintTaskGroupByStatusPageQuery query) {

        IPage<UserPrintTaskGroupStatusResult> page = this.getBaseMapper().taskDetailsPage(query.buildIPage(), query);
        List<AsPrintDataSnapshot> snapshots = page.getRecords()
                .stream()
                .filter(v -> Objects.isNull(v.getData()))
                .map(userPrintTaskGroupStatusResult -> new AsPrintDataSnapshot().setDataId(userPrintTaskGroupStatusResult.getDataId()).setTaskId(query.getTaskId()))
                .collect(Collectors.toList());
        // 处理没有快照的情况
        if (CollUtil.isNotEmpty(snapshots)) {
            String companyName = companyService.getById(LoginUserThreadLocal.getCompanyId()).getName();
            PrintStrategy printStrategy = printStrategyMap.get(query.getPrintType().shortValue());
            printStrategy.dealPrintSnapshot(snapshots);
            Map<Long, JSONObject> temp = new ConcurrentHashMap<>(snapshots.size());
            snapshots.forEach(v -> {
                JSONObject snapshot = v.getSnapshot();
                if (!snapshot.containsKey("standardId")) {
                    return;
                }
                printStrategy.translate(Collections.singletonList(snapshot), companyName, snapshot.getLong("standardId"));
                temp.put(v.getDataId(), snapshot);
            });
            page.getRecords().forEach(v -> {
                if (temp.containsKey(v.getDataId())) {
                    v.setData(temp.get(v.getDataId()));
                }
            });
        }
        return page;
    }
}
