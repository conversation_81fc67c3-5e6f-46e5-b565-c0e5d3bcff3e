package com.niimbot.asset.means.service.impl.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.cacheStrategy.DefaultTranslateUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.abs.FormAbs;
import com.niimbot.asset.system.abs.StandardAbs;
import com.niimbot.asset.system.dto.StandardExtFieldListQry;
import com.niimbot.asset.system.model.AsDictData;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.DictDataService;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import com.niimbot.system.TagAttrDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * 资产打印策略
 *
 * <AUTHOR>
 * @date 2021/11/25 14:37
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AssetPrintStrategy implements PrintStrategy {
    private final AssetService assetService;
    private final DictDataService dictDataService;
    private final OrgService orgService;
    private final FormAbs formAbs;
    private final StandardAbs standardAbs;
    private final AssetUtil assetUtil;
    private final CacheResourceUtil cacheResourceUtil;
    private final ThreadPoolTaskExecutor assetThreadPool;

    @Override
    public Short printType() {
        return DictConstant.PRINT_TYPE_ASSET;
    }

    @Override
    public void resolvePrintData(AsUserPrintTask printTask,
                                 PrintQueryDataDto queryDto) {
        AssetQueryConditionDto assetQueryDto = (AssetQueryConditionDto) queryDto;
        Map<Integer, List<Long>> map = new ConcurrentHashMap<>();
        long count = assetService.customPageCount(assetQueryDto);
        if (count == 0L) {
            return;
        }
        List<Long> results = new ArrayList<>((int) count);
        int size;
        if (count % 1000 == 0) {
            size = (int) count / 1000;
        } else {
            size = ((int) count / 1000) + 1;
        }
        CountDownLatch latch = new CountDownLatch(size);
        Long companyId = LoginUserThreadLocal.getCompanyId();
        for (int i = 1; i <= size; i++) {
            AssetQueryConditionDto copy = BeanUtil.copyProperties(assetQueryDto, AssetQueryConditionDto.class);
            copy.setPageNum(i);
            copy.setPageSize(1000L);
            int finalI = i;
            assetThreadPool.execute(() -> {
                // 只查询ID
                map.put(finalI, assetService.getPrintAssetIds(copy, companyId).getRecords());
                latch.countDown();
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("线程异常", e);
            Thread.currentThread().interrupt();
        }
        map.keySet().stream().sorted().forEach(i -> results.addAll(map.get(i)));
        if (CollUtil.isEmpty(results)) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_ASSET_NOT_EXIST);
        }
        printTask.setAssets(results);
        printTask.setQueryData((JSONObject) JSONObject.toJSON(assetQueryDto));
        // 为了设置任务名称
        List<Long> idsForTaskName = results.size() >= 3 ? results.subList(0, 3) : results;
        printTask.setAssetsSnapshot(
                assetService.list(
                        Wrappers.lambdaQuery(AsAsset.class)
                                .select(AsAsset::getAssetData)
                                .in(AsAsset::getId, idsForTaskName)
                ).stream().map(AsAsset::getAssetData).collect(toList())
        );
    }

    @Override
    public void translate(List<JSONObject> dataList, String companyName, Long standardId) {
        //资产标签
        List<AsDictData> tagDictData = this.dictDataService.selectDictDataByType("asset_tag");
        Map<Long, String> orgIdCompanyOwnerNameMap = getOrgCacheByDataList(dataList, ORG_OWNER, USE_ORG);
        List<FormFieldCO> formFields = getFormFields(standardId);
        List<DefaultTranslateUtil.FieldTranslation> translations = FormFieldConvert.convertField(formFields);
        // 判断微信环境
        boolean weixinEdition = Edition.isWeixin();
        List<String> orgEmpCodes = translations.stream().filter(f ->
                        FormFieldCO.YZC_EMP.equals(f.getFieldType()) || FormFieldCO.YZC_ORG.equals(f.getFieldType()))
                .map(DefaultTranslateUtil.FieldTranslation::getFieldCode).collect(toList());
        dataList.forEach(asset -> {
            asset.put(ASSET_ID, asset.getString("id"));
            if (weixinEdition) {
                // 企业微信特殊处理
                asset.put(COMPANY_OWNER, "--");
            } else {
                Long orgOwnerId = asset.getLong(ORG_OWNER);
                asset.put(COMPANY_OWNER, orgIdCompanyOwnerNameMap.getOrDefault(orgOwnerId, StrUtil.EMPTY));
            }

            // 使用组织的所属公司
            if (weixinEdition) {
                // 企业微信特殊处理
                asset.put(USE_ORG_COMPANY_OWNER, "--");
            } else {
                Long useOrgId = asset.getLong(USE_ORG);
                asset.putIfAbsent(USE_ORG_COMPANY_OWNER, orgIdCompanyOwnerNameMap.getOrDefault(useOrgId, StrUtil.EMPTY));
            }

            JSONArray jsonArray = asset.getJSONArray(ASSET_TAG);
            if (ObjectUtil.isNotNull(jsonArray)) {
                List<String> collect = jsonArray.stream().map(Convert::toStr).collect(toList());
                String val = tagDictData.stream().filter(dict -> collect.contains(dict.getValue()))
                        .map(AsDictData::getLabel).collect(Collectors.joining(","));
                asset.put(ASSET_TAG, val);
            }
            asset.putIfAbsent(COMPANY_NAME, companyName);
        });
        assetUtil.translatePrintAssetJson(dataList, translations, true);
        // 企业微信特殊处理，将组织员工改成 --
        if (weixinEdition) {
            dataList.forEach(asset -> {
                for (Map.Entry<String, Object> entry : asset.entrySet()) {
                    if (orgEmpCodes.contains(entry.getKey())) {
                        String value = Convert.toStr(entry.getValue(), StrUtil.EMPTY);
                        if (value.startsWith("qyweixin_emp:") || value.startsWith("qyweixin_org:") || value.startsWith("$userName=") || value.startsWith("$departmentName=")) {
                            entry.setValue("--");
                        }
                    }
                }
            });
        }
    }

    @Override
    public void dealPrintSnapshot(List<AsPrintDataSnapshot> snapshots) {
        AssetQueryConditionDto dto = new AssetQueryConditionDto()
                .setIncludeAssetIds(snapshots.stream().map(AsPrintDataSnapshot::getDataId).collect(toList()));
        dto.setPageNum(1L);
        dto.setPageSize(snapshots.size());
        ConcurrentMap<Long, AssetDto> map = assetService.customPage(dto).getRecords().stream().collect(Collectors.toConcurrentMap(AssetDto::getId, v -> v));
        snapshots.parallelStream().forEach(snapshot -> {
            if (Objects.isNull(snapshot.getSnapshot())) {
                AssetDto assetDto = map.get(snapshot.getDataId());
                assetDto.translate();
                snapshot.setSnapshot(assetDto.getAssetData());
            }
        });
    }

    @Override
    public void dealResultPrintTask(PrintPdfDto dto,
                                    PrintDataDto printDataDto,
                                    JSONObject templateJson,
                                    List<PrintDataViewDto.DataDto> resultList) {
        for (JSONObject asset : dto.getDataJson()) {
            JSONObject jsonObject = new JSONObject();
            asset.put(ASSET_ID, asset.getString("id"));
            if (ObjectUtil.isNotEmpty(templateJson)) {
                jsonObject = templateJson.clone();
                List<JSONObject> elements = jsonObject.getObject("elements", new TypeReference<List<JSONObject>>() {
                });
                List<String> valList = Lists.newArrayList();
                List<TagAttrDto> tagAttrList = Lists.newArrayList();
                for (TagAttrDto attrDatum : printDataDto.getAttrData()) {
                    String attrCode = attrDatum.getAttrCode();
                    if (2 == attrDatum.getCompanyOwnerVal() && COMPANY_OWNER.equals(attrCode)) {
                        attrCode = USE_ORG + StrUtil.upperFirst(attrCode);
                    }
                    String attrValue = StrUtil.blankToDefault(asset.getString(attrCode), "");
                    // 自定义属性 重名
                    if (StrUtil.isBlank(attrValue) && attrCode.contains(",")) {
                        String[] list = attrCode.split(",");
                        for (String customIdCode : list) {
                            String string = asset.getString(customIdCode);
                            if (StrUtil.isNotBlank(string)) {
                                attrValue = string;
                                break;
                            }
                        }
                    }

                    // 自定义文案取自定义信息
                    if (StrUtil.isNotBlank(attrDatum.getCustomWords())) {
                        attrValue = attrDatum.getCustomWords();
                    }

                    String val;
                    String delimiterStr = getDelimiterStr(attrDatum.getDelimiterType());
                    if (attrDatum.getIsShow()) {
                        String alias = StrUtil.isNotBlank(attrDatum.getAlias()) ?
                                attrDatum.getAlias() : attrDatum.getAttrName();
                        val = alias + delimiterStr + attrValue;
                    } else {
                        val = attrValue;
                    }
                    tagAttrList.add(attrDatum);
                    valList.add(val);
                }

                String codeValue = String.valueOf(asset.getLong("id"));
                if (StrUtil.isNotBlank(printDataDto.getPrintLabelDto().getCodeVal())) {
                    String attrCode = printDataDto.getPrintLabelDto().getCodeVal();
                    if (printDataDto.getPrintLabelDto().getIsCustomWords()) {
                        codeValue = attrCode;
                    } else {
                        codeValue = StrUtil.blankToDefault(asset.getString(attrCode), "");
                        // 自定义属性 重名
                        if (StrUtil.isBlank(codeValue) && attrCode.contains(",")) {
                            String[] list = attrCode.split(",");
                            for (String customIdCode : list) {
                                String string = asset.getString(customIdCode);
                                if (StrUtil.isNotBlank(string)) {
                                    codeValue = string;
                                    break;
                                }
                            }
                        }
                    }
                }
                // 非text Node
                String finalCodeValue = codeValue;
                List<JSONObject> resElementJson = elements.stream()
                        .filter(el -> !"text".equals(el.getString("type")))
                        .peek(el -> {
                            if ("qrcode".equals(el.getString("type"))) {
                                el.put("value", finalCodeValue);
                            }
                        }).collect(toList());
                List<JSONObject> textList = elements.stream()
                        .filter(el -> "text".equals(el.getString("type"))).collect(toList());
                for (int i = 0; i < textList.size(); i++) {
                    JSONObject el = textList.get(i);
                    String valToUse;
                    try {
                        valToUse = valList.get(i);
                    } catch (IndexOutOfBoundsException e) {
                        log.error("打印----> 打印标签属性数据与json模板数据不一致，请检查");
                        valToUse = "";
                    }
                    el.put("value", valToUse);

                    // 写入lineMode
                    TagAttrDto tagAttr;
                    try {
                        tagAttr = tagAttrList.get(i);
                        el.put("lineMode", tagAttr.getLineMode());
                    } catch (IndexOutOfBoundsException e) {
                        log.error("打印----> 打印标签属性数据与json模板数据不一致，请检查");
                    }
                }
                resElementJson.addAll(textList);
                jsonObject.put("elements", resElementJson);
            }

            PrintDataViewDto.DataDto dataDto = new PrintDataViewDto.AssetDataDto();
            dataDto.setLabelEpcid(asset.getString("labelEpcid"));
            dataDto.setId(asset.getLong("id"));
            dataDto.setJsonObject(JSON.toJSONString(jsonObject));
            resultList.add(dataDto);
        }
    }

    @Override
    public List<JSONObject> getPrintData(PrintPdfDto dto, PrintDataDto printDataDto) {
        // 资产查询
        AssetQueryConditionDto queryDto = BeanUtil.copyProperties(dto, AssetQueryConditionDto.class);
        queryDto.setAssetIds(dto.getIds());
        IPage<AssetDto> assetDtoIPage = assetService.customPage(queryDto);
        List<JSONObject> dataList = assetDtoIPage.getRecords().stream().map(AssetDto::translate).collect(toList());
        translate(dataList, printDataDto.getPrintLabelDto().getCompanyName(), printDataDto.getStandardId());
        return dataList;
    }

    private List<FormFieldCO> getFormFields(Long standardId) {
        FormVO formVO = formAbs.getTplByType(AsFormService.BIZ_TYPE_ASSET);
        List<FormFieldCO> fields = new ArrayList<>(formVO.getFormFields());
        List<FormFieldCO> standardExtFields = standardAbs.getStandardExtField(
                new StandardExtFieldListQry(formVO.getFormId(), standardId));
        fields.addAll(standardExtFields);
        return fields;
    }

    private Map<Long, String> getOrgCacheByDataList(List<JSONObject> assetList, String... codes) {
        Map<Long, String> orgIdCompanyOwnerNameMap = new HashMap<>();

        List<Long> orgIds = new ArrayList<>();
        Stream.of(codes).forEach(code -> {
            List<Long> collect = assetList.stream()
                    .map(json -> Convert.toLong(json.get(code)))
                    .filter(Objects::nonNull)
                    .distinct().collect(toList());
            orgIds.addAll(collect);
        });

        List<AsOrg> asOrgList = CollUtil.isEmpty(orgIds) ? ListUtil.empty() : this.orgService.list(Wrappers.lambdaQuery(AsOrg.class)
                .select(AsOrg::getId, AsOrg::getCompanyOwner, AsOrg::getOrgName, AsOrg::getOrgCode, AsOrg::getOrgType)
                .in(AsOrg::getId, orgIds));

        Map<Long, String> orgTempCache = new HashMap<>();

        for (AsOrg org : asOrgList) {
            // 如果部门类型是公司, 取自己
            if (AssetConstant.ORG_TYPE_COMPANY.equals(org.getOrgType())) {
                orgIdCompanyOwnerNameMap.put(org.getId(), org.getOrgName());
            }
            // 如果部门类型是部门, 取所属公司
            else {
                Long companyOwnerId = org.getCompanyOwner();
                if (orgTempCache.containsKey(companyOwnerId)) {
                    orgIdCompanyOwnerNameMap.put(org.getId(), orgTempCache.get(companyOwnerId));
                } else {
                    String orgName = cacheResourceUtil.getOrgName(companyOwnerId);
                    orgIdCompanyOwnerNameMap.put(org.getId(), orgName);
                    orgTempCache.put(companyOwnerId, orgName);
                }
            }
        }

        return orgIdCompanyOwnerNameMap;
    }
}
