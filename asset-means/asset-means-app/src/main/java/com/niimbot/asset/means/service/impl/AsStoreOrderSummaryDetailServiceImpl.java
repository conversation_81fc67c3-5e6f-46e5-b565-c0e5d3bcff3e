package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsStoreOrderSummaryDetailMapper;
import com.niimbot.asset.means.model.AsStoreOrderSummaryDetail;
import com.niimbot.asset.means.service.AsStoreOrderSummaryDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 资产入库单汇总明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Service
public class AsStoreOrderSummaryDetailServiceImpl extends ServiceImpl<AsStoreOrderSummaryDetailMapper, AsStoreOrderSummaryDetail> implements AsStoreOrderSummaryDetailService {

}
