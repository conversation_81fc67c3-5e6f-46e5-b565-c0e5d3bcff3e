package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.framework.model.DictDataDto;
import com.niimbot.asset.means.model.AsArea;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.AreaDto;
import com.niimbot.means.AreaQueryDto;
import com.niimbot.system.AreaAssetDto;
import com.niimbot.system.PrintAreaPageQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 资产区域表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-26
 */
@EnableDataPerm(excludeMethodName = {"removeCompanyArea", "listByIds", "allArea", "orgAreaUnfoldList", "printAreaPage"})
public interface AsAreaMapper extends BaseMapper<AsArea> {

    /**
     * 字典初始化，查询全部区域
     *
     * @return 区域集合
     */
    @Select("select id, company_id, area_name from as_area")
    List<AsArea> allArea();

    /**
     * 查询区域信息
     *
     * @param queryDto 查询条件
     * @return 资产列表
     */
    List<AreaDto> listArea(@Param("em") AreaQueryDto queryDto,
                           @Param("areaSql") String areaSql);

    /**
     * 查询区域信息
     *
     * @param page     分页
     * @param queryDto 查询条件
     * @return 资产列表
     */
    IPage<AreaDto> listArea(IPage<Object> page,
                            @Param("em") AreaQueryDto queryDto,
                            @Param("areaSql") String areaSql);

    Page<AreaAssetDto> printAreaPage(IPage<PrintAreaPageQueryDto> page,
                                     @Param("em") PrintAreaPageQueryDto dto,
                                     @Param("areaSql") String areaSql,
                                     @Param("companyId") Long companyId);

    /**
     * 查询最大区域编码
     *
     * @return 最大区域编码
     */
    @Select("select max(area_code) from as_area WHERE area_code REGEXP '^[A-Z]{1}[0-9]{2,3}$'")
    String getMaxAreaCode();

    /**
     * 通过code查询最大区域编码
     *
     * @return 最大区域编码
     */
    @Select("select max(area_code) from as_area WHERE area_code REGEXP '^[A-Z]{1}[0-9]{2,3}$' and area_code like concat(#{code}, '%')")
    String getMaxAreaCodeByCode(@Param("code") String code);

    /**
     * 通过ids查找
     *
     * @param companyId 公司id
     * @param ids       ids
     * @return 结果
     */
    /*List<AsArea> listByIds(@Param("companyId") Long companyId, @Param("ids") List<Long> ids);*/

    Integer areaRefAsset(@Param("areaId") Long areaId);

    List<AreaDto> listByOrg(@Param("orgIds") List<Long> orgIds);

    // 通过权限查询用户可见的部门简单集合（仅包含id，name）
    List<DictDataDto> listSimpleWithPerms(@Param("currentUserId") Long currentUserId,
                                          @Param("authorityType") Integer authorityType,
                                          @Param("companyId") Long companyId);

    List<Long> hasPermAreaIds(@Param("areaIds") List<Long> areaIds,
                              @Param("areaSql") String areaSql);
}
