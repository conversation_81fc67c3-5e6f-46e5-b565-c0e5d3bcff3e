package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsUnitMapper;
import com.niimbot.asset.means.model.AsUnit;
import com.niimbot.asset.means.service.AsUnitService;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;

/**
 * <p>
 * 计量单位 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service
public class AsUnitServiceImpl extends ServiceImpl<AsUnitMapper, AsUnit> implements AsUnitService {

    @Override
    public List<AsUnit> search(String name) {
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        if (ObjectUtil.isNull(cusUser)) {
            return this.getBaseMapper().search(name, 0L);
        } else {
            return this.getBaseMapper().search(name, cusUser.getCompanyId());
        }

    }

    @Override
    public int customCount(String unitName, Long companyId) {
        return this.getBaseMapper().customCount(unitName, companyId);
    }
}
