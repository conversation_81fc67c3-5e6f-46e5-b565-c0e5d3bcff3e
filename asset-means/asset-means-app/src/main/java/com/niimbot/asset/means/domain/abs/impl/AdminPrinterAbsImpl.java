package com.niimbot.asset.means.domain.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.means.mapstruct.MeansSystemMapStruct;
import com.niimbot.asset.means.model.AsAdminPrinter;
import com.niimbot.asset.means.service.AdminPrinterService;
import com.niimbot.asset.system.abs.AdminPrinterAbs;
import com.niimbot.asset.system.dto.AdminPrinterGetQry;
import com.niimbot.asset.system.dto.AdminPrinterListQry;
import com.niimbot.asset.system.dto.clientobject.AdminPrinterCO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/16 3:00 下午
 */
@RestController
@RequestMapping("/client/abs/means/AdminPrinterAbs/")
@RequiredArgsConstructor
public class AdminPrinterAbsImpl implements AdminPrinterAbs {

    private final MeansSystemMapStruct systemMapStruct;

    private final AdminPrinterService adminPrinterService;

    @Override
    public AdminPrinterCO getAdminPrinter(AdminPrinterGetQry qry) {
        return systemMapStruct.convertAsAdminPrinterModelToCo(adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getModel, qry.getModel())));
    }

    @Override
    public List<AdminPrinterCO> listAdminPrinter(AdminPrinterListQry qry) {
        return adminPrinterService.list(Wrappers.<AsAdminPrinter>lambdaQuery()
                        .eq(AsAdminPrinter::getIsApplyApp, true))
                .stream()
                .map(systemMapStruct::convertAsAdminPrinterModelToCo)
                .collect(Collectors.toList());
    }
}
