package com.niimbot.asset.means.domain.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.activiti.abs.ActivitiMeansOrderTypeAbs;
import com.niimbot.asset.activiti.dto.OrderTypeGetQry;
import com.niimbot.asset.activiti.dto.clientobjct.MeansOrderTypeCO;
import com.niimbot.asset.means.mapstruct.MeansActivitiMapStruct;
import com.niimbot.asset.means.model.AsOrderType;
import com.niimbot.asset.means.service.AsOrderTypeService;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/client/abs/means/activitiMeansOrderTypeAbs/")
@RequiredArgsConstructor
public class ActivitiMeansOrderTypeAbsImpl implements ActivitiMeansOrderTypeAbs {

    private final AsOrderTypeService orderTypeService;

    private final MeansActivitiMapStruct meansActivitiMapStruct;

    @Override
    public MeansOrderTypeCO getOneForApprovalMessage(OrderTypeGetQry qry) {
        AsOrderType orderType = orderTypeService.getOne(
                Wrappers.lambdaQuery(AsOrderType.class)
                        .select(AsOrderType::getName, AsOrderType::getType)
                        .eq(AsOrderType::getCompanyId, qry.getCompanyId())
                        .eq(AsOrderType::getType, qry.getOrderType())
        );
        return meansActivitiMapStruct.convertMeansOrderTypeDoToCo(orderType);
    }

}
