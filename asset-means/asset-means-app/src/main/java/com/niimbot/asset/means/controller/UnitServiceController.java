package com.niimbot.asset.means.controller;


import com.niimbot.asset.means.model.AsUnit;
import com.niimbot.asset.means.service.AsUnitService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年5月11日11:03:46
 */
@RestController
@RequestMapping("server/means/unit")
public class UnitServiceController {

    private final AsUnitService unitService;

    @Autowired
    public UnitServiceController(AsUnitService unitService) {
        this.unitService = unitService;
    }


    @GetMapping("/search")
    public List<AsUnit> search(@RequestParam(value = "name", required = false) String name) {
        return this.unitService.search(name);
    }

}
