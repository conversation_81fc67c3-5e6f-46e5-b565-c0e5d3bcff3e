package com.niimbot.asset.means.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsStandardMapper;
import com.niimbot.asset.means.mapstruct.StandardMapStruct;
import com.niimbot.asset.means.model.AsProduct;
import com.niimbot.asset.means.model.AsStandard;
import com.niimbot.asset.means.service.AsProductService;
import com.niimbot.asset.means.service.StandardService;
import com.niimbot.easydesign.core.dto.page.PageResult;
import com.niimbot.easydesign.form.dto.FormTplAddCmd;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormPropsCO;
import com.niimbot.easydesign.form.dto.formprops.DataMapping;
import com.niimbot.easydesign.form.dto.sdk.FormSdkVO;
import com.niimbot.easydesign.form.dto.sdk.SdkFormAddCmd;
import com.niimbot.easydesign.form.dto.sdk.SdkFormDeleteCmd;
import com.niimbot.easydesign.form.dto.sdk.SdkFormEditCmd;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.StandardListDto;
import com.niimbot.means.StandardQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @since 2020/11/27 15:12
 */
@Service
public class StandardServiceImpl extends ServiceImpl<AsStandardMapper, AsStandard> implements StandardService {

    @Autowired
    private AsProductService productService;

    private final AsFormService asFormService;

    private final StandardMapStruct standardMapStruct;

    private final CacheResourceUtil cacheResourceUtil;

    @Autowired
    public StandardServiceImpl(AsFormService asFormService,
                               StandardMapStruct standardMapStruct,
                               CacheResourceUtil cacheResourceUtil) {
        this.asFormService = asFormService;
        this.standardMapStruct = standardMapStruct;
        this.cacheResourceUtil = cacheResourceUtil;
    }

    @Override
    public IPage<StandardListDto> cusPage(StandardQueryDto queryDto) {
        List<String> companyIds = ListUtil.of("1", Convert.toStr(LoginUserThreadLocal.getCompanyId()));
        if (queryDto.getType() != null) {
            if (queryDto.getType() == 1) {
                companyIds = ListUtil.of(Convert.toStr(LoginUserThreadLocal.getCompanyId()));
            } else if (queryDto.getType() == 2) {
                companyIds = ListUtil.of("1");
            }
        }
        PageResult<FormSdkVO> result = asFormService.standardPage(companyIds,
                queryDto.getName(),
                Convert.toInt(queryDto.getPageNum()),
                Convert.toInt(queryDto.getPageSize()));
        List<FormSdkVO> list = result.getList();
        Page<StandardListDto> page = new Page<>(result.getCurrPage(), result.getPageSize(), result.getTotalCount());
        List<StandardListDto> records = list.stream().map(f -> {
            StandardListDto standardListDto = standardMapStruct.toStandard(f);
            standardListDto.setProductNum(Convert.toInt(productService.count(Wrappers.lambdaQuery(AsProduct.class).eq(AsProduct::getStandardId, f.getId()))));
            return standardListDto;
        }).collect(toList());
        page.setRecords(records);
        return page;
    }

    @Override
    public List<StandardListDto> cusList(String name) {
        PageResult<FormSdkVO> result = asFormService.standardPage(
                ListUtil.of("1", Convert.toStr(LoginUserThreadLocal.getCompanyId())),
                name, 1, Integer.MAX_VALUE);
        return standardMapStruct.toStandard(result.getList());
    }

    @Override
    public FormVO form(Long standardId, Boolean throwEx) {
        // 没有标准品，说明是新增标准品，查询标准数据返回
        if (standardId == null) {
            return asFormService.standardTpl();
        } else {
            return asFormService.getByFormId(standardId, ListUtil.of(1L, LoginUserThreadLocal.getCompanyId()), throwEx);
        }
    }

    /**
     * 新增标准品
     *
     * @param standard 标准品
     * @return true/false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(FormTplAddCmd standard) {
        SdkFormAddCmd sdkFormAddCmd = new SdkFormAddCmd(
                standard.getFormName(),
                asFormService.getBizCode(AsFormService.BIZ_TYPE_STANDARD),
                Convert.toStr(LoginUserThreadLocal.getCompanyId()),
                standard.getFormFields().stream().peek(f -> f.setId(null)).collect(toList()),
                standard.getFormProps()
        );
        sdkFormAddCmd.setOperatorId(LoginUserThreadLocal.getCurrentUserId());
        sdkFormAddCmd.setOperatorName(cacheResourceUtil.getUserName(sdkFormAddCmd.getOperatorId()));
        return asFormService.addForm(sdkFormAddCmd);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(FormTplAddCmd standard) {
        SdkFormEditCmd sdkFormEditCmd = new SdkFormEditCmd(
                standard.getFormId(),
                standard.getFormName(),
                Convert.toStr(LoginUserThreadLocal.getCompanyId()),
                standard.getFormFields(),
                standard.getFormProps()
        );
        sdkFormEditCmd.setOperatorId(LoginUserThreadLocal.getCurrentUserId());
        sdkFormEditCmd.setOperatorName(cacheResourceUtil.getUserName(sdkFormEditCmd.getOperatorId()));
        return asFormService.editForm(sdkFormEditCmd);
    }

    /**
     * 删除标准品
     *
     * @param id 标准品ids
     * @return true/false
     */
    @Override
    public Boolean delete(Long id) {
        // 当前数据存在关联数据，不可删除
        int count = getBaseMapper().hasRef(id, LoginUserThreadLocal.getCompanyId());
        if (count > 0) {
            throw new BusinessException(MeansResultCode.STANDARD_ERROR, "当前数据存在关联数据，不可删除");
        }
        SdkFormDeleteCmd cmd = new SdkFormDeleteCmd(id, Convert.toStr(LoginUserThreadLocal.getCompanyId()));
        return asFormService.removeForm(cmd);
    }


    @Override
    public List<AsStandard> listNoPerm(Wrapper<AsStandard> wrapper) {
        return this.getBaseMapper().listNoPerm(wrapper);
    }

    @Override
    public StandardListDto copySys(Long id) {
        // 查询用户表单
        FormVO formVO = asFormService.getByFormId(id, LoginUserThreadLocal.getCompanyId(), false);
        if (formVO != null) {
            return new StandardListDto().setId(formVO.getFormId());
        } else {
            // 查询系统表单
            formVO = asFormService.getByFormId(id, 1L, false);
            if (formVO == null) {
                throw new BusinessException(MeansResultCode.SYSTEM_PRODUCT_NOT_EXISTS);
            } else {
                Long formId = formVO.getFormId();
                try {
                    Long newFormId = asFormService.copyForm(formId, 1L, LoginUserThreadLocal.getCompanyId());
                    return new StandardListDto().setId(newFormId);
                } catch (BusinessException e) {
                    if (e.getMessage().equals("模板名称已存在")) {
                        throw new BusinessException(MeansResultCode.DYNAMIC_FORM_ERROR, "我的标准品存在同名标准品，请选择我的标准品");
                    } else {
                        throw e;
                    }
                }
            }
        }
    }

    @Override
    public List<FormFieldCO> getStandardExtField(Long mappingFormId, Long standardId) {
        String formIdStr = Convert.toStr(mappingFormId);
        List<FormFieldCO> formFieldCOS = new ArrayList<>();
        if (ObjectUtil.isNotNull(standardId)) {
            // 查询用户表单
            FormVO standardVO = asFormService.getByFormId(standardId, ListUtil.of(1L, LoginUserThreadLocal.getCompanyId()), false);
            if (ObjectUtil.isNotNull(standardVO)) {
                // 全部标准品属性
                List<FormFieldCO> fields = standardVO.getFormFields();
                // 开始过滤标准品属性
                JSONObject formProps = standardVO.getFormProps();
                List<String> mappingSourceCodes = new ArrayList<>();
                if (formProps != null && formProps.containsKey(FormPropsCO.DATA_MAPPING)) {
                    JSONObject dataMapping = formProps.getJSONObject(FormPropsCO.DATA_MAPPING);
                    if (dataMapping.containsKey(formIdStr)) {
                        List<DataMapping.Mapping> mappings = dataMapping.getJSONArray(formIdStr).toJavaList(DataMapping.Mapping.class);
                        mappingSourceCodes = mappings.stream()
                                .map(DataMapping.Mapping::getSourceCode).collect(toList());
                    }
                }
                List<String> finalMappingSourceCodes = mappingSourceCodes;
                formFieldCOS = fields.stream()
                        // 过滤不可删除的
                        .filter(f -> BooleanUtil.isTrue(f.getFieldCfg().getCanDelete()))
                        // 过滤有映射关系的
                        .filter(f -> !finalMappingSourceCodes.contains(f.getFieldCode()))
                        .collect(toList());
            }
        }
        return formFieldCOS;
    }

}
