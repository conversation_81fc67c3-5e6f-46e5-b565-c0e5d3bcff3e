package com.niimbot.asset.means.controller;

import com.niimbot.asset.means.model.AsTagMaterial;
import com.niimbot.asset.means.service.PrintTaskService;
import com.niimbot.means.PrintDataSetDto;
import com.niimbot.system.TagMaterialDto;
import com.niimbot.system.UserTagPrintDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签打印管理控制器
 *
 * <AUTHOR>
 * @Date 2020/12/31
 */
@RestController
@RequestMapping("server/system/print")
@Slf4j
public class PrintServiceController {

    @Resource
    private PrintTaskService printService;

    /**
     * 设置默认材质
     *
     * @param printDataSetDto 设置打印数据dto
     * @return Boolean
     */
    @PutMapping(value = "/setDefaultMaterial")
    public TagMaterialDto setDefaultMaterial(@RequestBody PrintDataSetDto printDataSetDto) {
        return printService.setDefaultMaterial(printDataSetDto);
    }

    /**
     * 获取标签材质列表
     *
     * @return AsTagMaterial
     */
    @GetMapping(value = "/getTagMaterial")
    public List<AsTagMaterial> getTagMaterial(@RequestParam(value = "printerName", required = false) String printerName) {
        return printService.getTagMaterial(printerName);
    }

    /**
     * 获取用户的打印模板
     *
     * @return UserTagPrintDto
     */
    @GetMapping(value = "/getPrintTpl/{printType}")
    public UserTagPrintDto getPrintTpl(@PathVariable("printType") Short printType,
                                       @RequestParam(value = "printerName", required = false) String printerName){
        return printService.getPrintTpl(printType, printerName);
    }

}
