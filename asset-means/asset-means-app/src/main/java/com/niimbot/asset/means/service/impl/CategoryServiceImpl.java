package com.niimbot.asset.means.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.utils.DeduplicationUtil;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.TreeUtils;
import com.niimbot.asset.framework.utils.cacheStrategy.CacheCateStrategy;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsCategoryMapper;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.asset.system.model.AsCompany;
import com.niimbot.asset.system.service.CompanyService;
import com.niimbot.framework.dataperm.constant.DataPermType;
import com.niimbot.framework.dataperm.core.strategy.DataScopeStrategyManager;
import com.niimbot.framework.dataperm.object.Tuple2;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.CategoryDto;
import com.niimbot.means.CategoryImportDto;
import com.niimbot.means.CategoryQueryDto;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/11/26 11:13
 */
@Slf4j
@Service
public class CategoryServiceImpl extends ServiceImpl<AsCategoryMapper, AsCategory> implements CategoryService, ApplicationListener<ApplicationReadyEvent> {

    private final RedisService redisService;

    private final CompanyService companyService;

    private final ThreadPoolTaskExecutor taskExecutor;

    private final AsAssetImportErrorService assetImportErrorService;

    @Autowired
    private DataScopeStrategyManager dataScopeStrategyManager;

    @Resource
    private AssetService assetService;

    @Autowired
    public CategoryServiceImpl(RedisService redisService,
                               CompanyService companyService,
                               ThreadPoolTaskExecutor taskExecutor,
                               AsAssetImportErrorService assetImportErrorService) {
        this.redisService = redisService;
        this.companyService = companyService;
        this.taskExecutor = taskExecutor;
        this.assetImportErrorService = assetImportErrorService;
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return;
        }

        taskExecutor.execute(() -> {
            List<AsCategory> cateList = this.getBaseMapper().allCategory();
            this.loadCateCache(cateList);
        });
    }

    /**
     * 写入分类缓存
     *
     * @param cateList 分类列表
     */
    @Override
    public void loadCateCache(List<AsCategory> cateList) {
        Map<String, String> collect = new ConcurrentHashMap<>();
        cateList.parallelStream().forEach(it -> {
            collect.put(Convert.toStr(it.getId()), it.getCategoryName());
        });
        redisService.hSetAll(RedisConstant.categoryDictKey(), collect);
        log.info("init category cache finish");
    }

    @Override
    public List<CategoryDto> cateList(CategoryQueryDto queryDto) {
        // 是否过滤权限(默认带权限)
        String cateSql = null;
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm())) {
            cateSql = dataScopeStrategyManager.simplePermsSql(DataPermType.CATE);
        }
        List<CategoryDto> cateList = this.getBaseMapper().listCate(queryDto, cateSql);
        // 是否构造树，只有开启过滤权限，才需要补齐无权限数据
        if (!BooleanUtil.isFalse(queryDto.getFilterPerm()) && BooleanUtil.isTrue(queryDto.getBuildTree())) {
            fillTreeNoPermNode(cateList);
        }
        return cateList;
    }

    @Override
    public List<Long> hasPermCateIds(List<Long> cateIds) {
        String cateSql = dataScopeStrategyManager.simplePermsSql(DataPermType.CATE);
        return getBaseMapper().hasPermCateIds(cateIds, cateSql);
    }

    private void fillTreeNoPermNode(List<CategoryDto> cateList) {
        // 当前组织Id
        Set<Long> areaIds = cateList.stream().map(CategoryDto::getId).collect(Collectors.toSet());
        Set<Long> pidSet = new HashSet<>();
        // 如果父节点Id不在当前组织集合中，则需要查询补充
        for (CategoryDto category : cateList) {
            String paths = category.getPaths();
            List<Long> pIds = Arrays.stream(paths.split(",")).map(Long::parseLong).collect(Collectors.toList());
            for (Long id : pIds) {
                if (id > 0 && !areaIds.contains(id)) {
                    pidSet.add(id);
                }
            }
        }
        if (!pidSet.isEmpty()) {
            CategoryQueryDto queryDto = new CategoryQueryDto().setIncludeIds(new ArrayList<>(pidSet));
            List<CategoryDto> categories = this.baseMapper.listCate(queryDto, null);
            categories.forEach(f -> f.setDisabled(true));
            cateList.addAll(categories);
        }
    }


    /**
     * 添加资产分类
     *
     * @param category 资产分类
     * @return 是否成功
     */
    @Override
    public AsCategory add(AsCategory category) {
        AsCompany company = companyService.getOne(new QueryWrapper<AsCompany>()
                .lambda().eq(AsCompany::getId, LoginUserThreadLocal.getCompanyId()));
        if (ObjectUtil.isNull(company)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        // 查询父节点，写入level和path
        AsCategory parent = this.getOne(new QueryWrapper<AsCategory>().lambda()
                .eq(AsCategory::getId, category.getPid()));
        // 没有父节点
        if (ObjectUtil.isNull(parent)) {
            category.setPid(0L);
            category.setLevel(0);
            category.setPaths("0,");
        } else {
            // 设置 level 和 path
            int level = parent.getLevel() + 1;
            if (level >= 7) {
                throw new BusinessException(SystemResultCode.LEVEL_OVER_MAX, "7");
            }
            category.setLevel(level);
            category.setPaths(parent.getPaths() + parent.getId() + ",");
        }
        // 查询修改公司下是否code重复
        checkRepeat(category, false);
        category.setIndustryId(company.getIndustryId());
        Long id = IdUtils.getId();
        category.setId(id);
        if (!this.save(category)) {
            throw new BusinessException(SystemResultCode.OPT_SAVE_FAIL);
        }
        return category;
    }

    /**
     * 编辑资产分类
     *
     * @param category 资产分类
     * @return 是否成功
     */
    @Override
    public AsCategory edit(AsCategory category) {
        //查询当前修改节点是否存在
        AsCategory currentCategory = this.getOne(new QueryWrapper<AsCategory>().lambda().eq(AsCategory::getId, category.getId()), false);
        BusinessExceptionUtil.checkNotNull(currentCategory, "资产分类不存在");

        //查询新的父级节点，如果父级节点不存在，则修改失败
        AsCategory parentCategory;
        if (Objects.equals(category.getPid(), 0L)) {
            //兼容父级节点是顶级根节点，在数据库里面是没有根节点数据，模拟构造一条根节点数据
            parentCategory = new AsCategory();
            parentCategory.setId(0L);
            parentCategory.setLevel(-1); // 后续这里会+1为0
            parentCategory.setPaths("");
        } else {
            parentCategory = this.getOne(new QueryWrapper<AsCategory>().lambda().eq(AsCategory::getId, category.getPid()), false);
        }
        BusinessExceptionUtil.checkNotNull(parentCategory, "父节点不存在");
        //是否移动资产分类层级，true：改变资产分类层级 false：没有改变资产分类层级(仅改变编码和名称等信息)
        boolean moveLevel = !currentCategory.getPid().equals(parentCategory.getId());

        //校验资产分类编码和资产分类名称
        checkRepeat(category, true);

        //有移动资产分类层级，需要校验层级高度
        if (moveLevel) {
            //查询当前资产分类及其子资产分类
            String sonPath = currentCategory.getPaths() + currentCategory.getId() + ",";
            List<AsCategory> currentSubCategory = this.list(new QueryWrapper<AsCategory>().lambda().likeRight(AsCategory::getPaths, sonPath));

            //资产分类层级不能超过7层
            TreeUtils.checkLevelOverMax(parentCategory, currentSubCategory, 7);

            //父级节点不能是当前修改节点及其子节点
            TreeUtils.checkParent(parentCategory.getId(), currentCategory, currentSubCategory);

            //修改了资产分类层级需要修改paths等信息
            AsCategory modifyCategory = new AsCategory();
            modifyCategory.setId(currentCategory.getId());
            modifyCategory.setPid(category.getPid());
            modifyCategory.setCategoryCode(category.getCategoryCode());
            modifyCategory.setCategoryName(category.getCategoryName());
            modifyCategory.setLevel(parentCategory.getLevel() + 1);
            modifyCategory.setPaths(parentCategory.getPaths() + parentCategory.getId() + ",");

            //递归修改paths和level等信息
            TreeUtils.assemblePaths(modifyCategory, currentSubCategory);
            currentSubCategory.add(modifyCategory);

            //批量修改paths信息
            updateBatchById(currentSubCategory, currentSubCategory.size());
        } else {
            //没有修改层级，直接修改当前节点的编码和名称即可
            this.update(new UpdateWrapper<AsCategory>().lambda()
                    .set(AsCategory::getCategoryCode, category.getCategoryCode())
                    .set(AsCategory::getCategoryName, category.getCategoryName())
                    .eq(AsCategory::getId, category.getId()));
        }

        //修改名称需要更新redis缓存，发送redis队列更新资产分类缓存
        if (StrUtil.isNotEmpty(category.getCategoryName())) {
            SpringUtil.getBean(CacheCateStrategy.class).evictCache(category.getId());
        }
        return getById(category.getId()).setCategoryName(currentCategory.getCategoryName());
    }

    /**
     * 删除资产分类
     *
     * @param categoryIds 资产分类Id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AsCategory> remove(List<Long> categoryIds) {
        List<AsCategory> categoryList = new ArrayList<>();
        // 目前原型设计只能逐条删除，因此先循环判断
        for (Long categoryId : categoryIds) {
            // 当前分类及子分类
            List<AsCategory> part = this.list(
                    new QueryWrapper<AsCategory>().lambda()
                            .select(AsCategory::getId, AsCategory::getCategoryCode, AsCategory::getCategoryName)
                            .eq(AsCategory::getId, categoryId)
                            .or()
                            .like(AsCategory::getPaths, ("," + categoryId + ","))
            );
            if (CollUtil.isEmpty(part)) {
                continue;
            }
            Set<Long> ids = part.stream().map(AsCategory::getId).collect(Collectors.toSet());
            AsAsset asset = assetService.getOne(
                    Wrappers.lambdaQuery(AsAsset.class)
                            .select(AsAsset::getId)
                            .eq(AsAsset::getCompanyId, LoginUserThreadLocal.getCompanyId())
                            .in(AsAsset::getAssetCategory, ids.stream().map(String::valueOf).collect(Collectors.toSet()))
                            .last("LIMIT 1")
            );
            // 是否关联资产
            // Integer count = this.getBaseMapper().categoryRefAsset(categoryId);
            if (Objects.nonNull(asset)) {
                throw new BusinessException(SystemResultCode.CATEGORY_EXISTS);
            }
            categoryList.addAll(part);
            this.removeBatchByIds(ids);
            // this.remove(new QueryWrapper<AsCategory>().lambda().eq(AsCategory::getId, categoryId).or().like(AsCategory::getPaths, ("," + categoryId + ",")));
        }
        return categoryList;
    }

    @Override
    public Tuple2<List<AsCategory>, Map<Long, Long>> registerCopy(Long companyId, List<Long> categories) {
        // 注册防止分类数不完整、递归查询列出所有父节点
        List<AsCategory> allCateList = getBaseMapper().listByCompanyId(companyId, null);
        List<Long> computeList = Lists.newArrayList();
        categories.forEach(categoryId -> recursiveCategories(allCateList, computeList, categoryId));

        List<CategoryDto> dtoList = new ArrayList<>();
        for (AsCategory asCategory : allCateList) {
            if (computeList.contains(asCategory.getId())) {
                CategoryDto categoryDto = BeanUtil.copyProperties(asCategory, CategoryDto.class);
                dtoList.add(categoryDto);
            }
        }

        // 获取父节点 collect 顶层tree 数据结构
        List<CategoryDto> parentList = dtoList.stream().filter(node -> node.getPid() == 0).collect(Collectors.toList());
        Map<Long, Long> idRelationMap = MapUtil.newHashMap();
        List<CategoryDto> changedList = parentList.parallelStream().map(parent -> {
            parent.setChildren(getChildByPid(parent.getId(), dtoList));
            return parent;
        }).map(parent -> {
            setIdRelation(parent, idRelationMap);
            return parent;
        }).map(dto -> {
            dto.setPaths("0,");
            setChildPaths(dto);
            return dto;
        }).collect(Collectors.toList());

        // 平铺树结构
        List<CategoryDto> flat = Lists.newArrayList();
        changedList.stream().forEach(node -> {
            flat.add(node);
            flatTree(node, flat);
        });
        List<AsCategory> result = flat.stream().map(dto -> {
            AsCategory category = BeanUtil.copyProperties(dto, AsCategory.class);
            category.setSourceId(idRelationMap.get(category.getId()));
            return category;
        }).collect(Collectors.toList());
        return new Tuple2<>(result, idRelationMap);
    }

    private void recursiveCategories(List<AsCategory> allCateList, List<Long> computeList, Long currentCategoryId) {
        if (!computeList.contains(currentCategoryId)) {
            computeList.add(currentCategoryId);
        }
        allCateList.stream()
                .filter(asCategory -> ObjectUtil.equal(asCategory.getId(), currentCategoryId))
                .findFirst().ifPresent(asCategory -> {
                    Long pid = asCategory.getPid();
                    if (pid == 0L) {
                        computeList.add(asCategory.getId());
                    } else {
                        computeList.add(pid);
                        recursiveCategories(allCateList, computeList, pid);
                    }
                });
    }

    @Override
    public void getIdsByParentId(List<AsCategory> categoryList, Long parentId, List<Long> result) {
        for (AsCategory category : categoryList) {
            if (ObjectUtil.equal(category.getId(), parentId)) {
                result.add(parentId);
            }
            if (ObjectUtil.equal(category.getPid(), parentId)) {
                result.add(category.getId());
                getIdsByParentId(categoryList, category.getId(), result);
            }
        }
    }

    @Override
    public List<AsCategory> listByIds(List<Long> ids) {
        return this.getBaseMapper().listByCompanyId(LoginUserThreadLocal.getCompanyId(), ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sort(List<Long> categoryIds) {
        int count = this.list(new QueryWrapper<AsCategory>().lambda()
                .select(AsCategory::getPid)
                .in(AsCategory::getId, categoryIds)
                .groupBy(AsCategory::getPid)).size();
        if (count > 1) {
            throw new BusinessException(SystemResultCode.CATEGORY_SORT_ERROR);
        }
        AtomicInteger idx = new AtomicInteger(0);
        List<AsCategory> collect = categoryIds.stream().map(it -> {
            AsCategory category = new AsCategory();
            category.setId(it);
            category.setSortNum(idx.getAndIncrement());
            return category;
        }).collect(Collectors.toList());
        this.updateBatchById(collect);
        return true;
    }

    /**
     * 平铺树结构
     *
     * @param node
     * @param list
     */
    private void flatTree(CategoryDto node, List<CategoryDto> list) {
        List<CategoryDto> children = node.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            list.addAll(children);
            children.forEach(child -> flatTree(child, list));
        }
    }

    /**
     * 设置path
     *
     * @param node
     */
    private void setChildPaths(CategoryDto node) {
        List<CategoryDto> children = node.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            children.forEach(child -> {
                child.setPaths(node.getPaths() + child.getPid() + ",");
                setChildPaths(child);
            });
        }
    }

    /**
     * 设置节点 id
     *
     * @param node
     */
    private void setIdRelation(CategoryDto node, Map<Long, Long> idRelationMap) {
        long newId = IdUtils.getId();
        idRelationMap.put(newId, node.getId());
        idRelationMap.put(node.getId(), newId);
        node.setId(newId);
        List<CategoryDto> children = node.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            children.forEach(child -> {
                child.setPid(newId);
                setIdRelation(child, idRelationMap);
            });
        }
    }

    /**
     * 设置node子节点
     *
     * @param parentId 父节点
     * @param origin   元素list
     * @return childrenList
     */
    private List<CategoryDto> getChildByPid(Long parentId, List<CategoryDto> origin) {
        List<CategoryDto> collect = origin.stream()
                .filter(node -> node.getPid().longValue() == parentId.longValue()).collect(Collectors.toList());
        collect.forEach(node -> node.setChildren(getChildByPid(node.getId(), origin)));
        return collect;
    }

    /**
     * 资产分类编码和名称校验：编码全公司唯一，名称兄弟节点不能相同
     *
     * @param category 当前修改资产分类
     * @param isEdit   是否编辑
     */
    private void checkRepeat(AsCategory category, Boolean isEdit) {
        LambdaQueryWrapper<AsCategory> wrapper = new LambdaQueryWrapper<>();

        //是否编辑，编辑校验时需要剔除当前修改节点
        if (isEdit) {
            wrapper.ne(AsCategory::getId, category.getId());
        }
        LambdaQueryWrapper<AsCategory> clone = wrapper.clone();
        //校验编码：公司内全局唯一，排除自身
        wrapper.eq(AsCategory::getCategoryCode, category.getCategoryCode());
        AsCategory checkCode = this.getOne(wrapper, false);
        if (ObjectUtil.isNotNull(checkCode)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "分类编码", category.getCategoryCode());
        }

        //校验名称：当前节点下唯一，同一层级兄弟节点不能相同
        clone.eq(AsCategory::getPid, category.getPid())
                .eq(AsCategory::getCategoryName, category.getCategoryName());
        AsCategory checkName = this.getOne(clone, false);
        if (ObjectUtil.isNotNull(checkName)) {
            throw new BusinessException(SystemResultCode.PARAM_EXISTS, "分类名称", category.getCategoryName());
        }
    }

    /**
     * 获取最大编码
     *
     * @return 结果
     */
    @Override
    public String getMaxCode() {
        return this.getBaseMapper().getMaxCategoryCode(LoginUserThreadLocal.getCompanyId());
    }

    /**
     * 通过code获取最大编码
     *
     * @return 结果
     */
    @Override
    public String getMaxCode(String code) {
        return this.getBaseMapper().getMaxCategoryCodeByCode(LoginUserThreadLocal.getCompanyId(), code);
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<AsAssetImportError> list = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, taskId)
                .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_ASSET_CATEGORY));
        return list.stream().map(AsAssetImportError::getExcelJson)
                .collect(Collectors.toList());
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importErrorDto.getTaskId());
        importError.setErrorNum(0);
        importError.setImportType(DictConstant.IMPORT_TYPE_ASSET_CATEGORY);
        importError.setExcelJson(importErrorDto.getHeadModelList());
        this.assetImportErrorService.save(importError);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSheetData(CategoryImportDto importDto) {
        // 转换实体
        AsCategory category = BeanUtil.copyProperties(importDto, AsCategory.class);
        // 单个企业资产分类编码不能重复
        if (this.count(new QueryWrapper<AsCategory>().lambda()
                .eq(AsCategory::getCategoryCode, category.getCategoryCode())) > 0) {
            LuckySheetModel codee = importDto.getSheetModelList().get(0);
            if (ObjectUtil.isNull(codee.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("资产分类编码已存在");
                codee.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }
        if (StrUtil.isNotBlank(importDto.getPidCode())) {
            // 通过pidCode查询id
            AsCategory parentCategory = this.getOne(new QueryWrapper<AsCategory>().lambda().eq(AsCategory::getCategoryCode, importDto.getPidCode()), false);
            if (ObjectUtil.isNull(parentCategory)) {
                LuckySheetModel pidCode = importDto.getSheetModelList().get(2);
                if (ObjectUtil.isNull(pidCode.getV().getPs())) {
                    LuckySheetModel.Comment comment = new LuckySheetModel.Comment("上级资产分类编码不存在");
                    pidCode.getV().setPs(comment);
                    importDto.setErrorNum(importDto.getErrorNum() + 1);
                }
                category.setPid(-1L);
            } else {
                // level 加一
                int level = parentCategory.getLevel() + 1;
                if (level >= 7) {
                    LuckySheetModel pidCode = importDto.getSheetModelList().get(2);
                    if (ObjectUtil.isNull(pidCode.getV().getPs())) {
                        LuckySheetModel.Comment comment = new LuckySheetModel.Comment("层级不得超过7层");
                        pidCode.getV().setPs(comment);
                        importDto.setErrorNum(importDto.getErrorNum() + 1);
                    }
                } else {
                    category.setPid(parentCategory.getId());
                    category.setLevel(level);
                    // 添加paths路径
                    category.setPaths(parentCategory.getPaths() + parentCategory.getId() + ",");
                }
            }
        } else {
            category.setPid(0L);
            category.setLevel(0);
            category.setPaths("0,");
        }

        // 查询改父节点下是否code重复
        if (this.count(new QueryWrapper<AsCategory>().lambda()
                .eq(AsCategory::getPid, category.getPid())
                .eq(AsCategory::getCategoryName, category.getCategoryName())) > 0) {
            LuckySheetModel categoryName = importDto.getSheetModelList().get(1);
            if (ObjectUtil.isNull(categoryName.getV().getPs())) {
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("资产分类名称已存在");
                categoryName.getV().setPs(comment);
                importDto.setErrorNum(importDto.getErrorNum() + 1);
            }
        }
        if (importDto.getErrorNum() == 0) {
            Long id = IdUtils.getId();
            category.setId(id);
            AsCompany company = companyService.getOne(new QueryWrapper<AsCompany>()
                    .lambda().eq(AsCompany::getId, LoginUserThreadLocal.getCompanyId()));
            category.setIndustryId(company.getIndustryId());
            this.save(category);
            redisService.hIncr(RedisConstant.companyImportKey("assetCategory", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("assetCategory", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            return true;
        } else {
            AsAssetImportError importError = copyToAsCategoryImportError(importDto);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("assetCategory", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }

    @Override
    public List<AsCategory> listByCategoryNamePermission(String kw) {
        return list(new QueryWrapper<AsCategory>()
                .lambda()
                .and(StrUtil.isNotBlank(kw), wrapper ->
                        wrapper.like(AsCategory::getCategoryName, kw))
                .orderByAsc(AsCategory::getSortNum)
                .orderByAsc(AsCategory::getCreateTime));
    }

    @Override
    public Long getOne(String name, String code) {
        LoginUserDto userDto = LoginUserThreadLocal.get();
        if (userDto == null
                || userDto.getCusUser() == null
                || userDto.getCusUser().getCompanyId() == null) {
            return null;
        }
        Long companyId = userDto.getCusUser().getCompanyId();
        if (StrUtil.isNotEmpty(name)) {
            String cacheKey = RedisConstant.categoryDictKey() + ":" + companyId + ":name:" + name;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsCategory one = getOne(Wrappers.lambdaQuery(AsCategory.class)
                        .eq(AsCategory::getCompanyId, companyId)
                        .eq(AsCategory::getCategoryName, name), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        } else if (StrUtil.isNotEmpty(code)) {
            String cacheKey = RedisConstant.categoryDictKey() + ":" + companyId + ":code:" + code;
            if (redisService.hasKey(cacheKey)) {
                return Convert.toLong(redisService.get(cacheKey));
            } else {
                AsCategory one = getOne(Wrappers.lambdaQuery(AsCategory.class)
                        .eq(AsCategory::getCompanyId, companyId)
                        .eq(AsCategory::getCategoryCode, code), false);
                if (one == null) {
                    // redisService.set(cacheKey, null, 5, TimeUnit.SECONDS);
                    return null;
                } else {
                    // redisService.set(cacheKey, one.getId(), 2, TimeUnit.HOURS);
                    return one.getId();
                }
            }
        }
        return null;
    }

    private List<CategoryDto> filterPerms(List<CategoryDto> categoryDtos, List<Long> permsIds) {
        if (BooleanUtil.isTrue(LoginUserThreadLocal.getCusUser().getIsAdmin())) {
            return categoryDtos.stream().map(o -> o.setDisabled(Boolean.FALSE)).collect(Collectors.toList());
        }
        List<CategoryDto> resolve = new ArrayList<>();
        Map<Long, CategoryDto> data = categoryDtos.stream().collect(Collectors.toMap(CategoryDto::getId, o -> o));
        List<CategoryDto> myCates = categoryDtos.stream().filter(o -> permsIds.contains(o.getId())).collect(Collectors.toList());
        for (CategoryDto categoryDto : myCates) {
            categoryDto.setDisabled(Boolean.FALSE);
            resolve.add(categoryDto);
            findParent(data, categoryDto.getPid(), resolve);
        }
        return resolve.stream().filter(DeduplicationUtil.distinctByKey(CategoryDto::getId))
                .sorted(Comparator.comparing(CategoryDto::getSortNum, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(CategoryDto::getCreateTime, Comparator.nullsLast(LocalDateTime::compareTo)))
                .collect(Collectors.toList());
    }

    private void findParent(Map<Long, CategoryDto> data, Long currentId, List<CategoryDto> resolve) {
        CategoryDto categoryDto = data.get(currentId);
        if (categoryDto == null) {
            return;
        }
        resolve.add(categoryDto);
        findParent(data, categoryDto.getPid(), resolve);
    }

    private AsAssetImportError copyToAsCategoryImportError(CategoryImportDto importDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_ASSET_CATEGORY);
        List<LuckySheetModel> sheetModelList = importDto.getSheetModelList();
        importError.setExcelJson(new ArrayList<>(sheetModelList));
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }
}
