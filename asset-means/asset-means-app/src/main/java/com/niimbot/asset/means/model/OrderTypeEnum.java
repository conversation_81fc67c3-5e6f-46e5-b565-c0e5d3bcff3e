package com.niimbot.asset.means.model;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.google.common.collect.ImmutableMap;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.CacheResourceUtil;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.means.service.AsOrderService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetStatusDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.util.StrUtil.*;

/**
 * 单据类型枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2020-12-24
 */
@Slf4j
public enum OrderTypeEnum implements IEnum<Integer> {

    LY(1, "领用", ImmutableMap.<String, String>builder()
            .put(AsOrderService.USE_PERSON, "receiveUser")
            .put(AsOrderService.USE_ORG, "receiveOrg")
            .put(AsOrderService.STORAGE_AREA, "receiveArea")
            .put(AsOrderService.STORAGE_ADDRESS, "receiveAddress")
            .build()),
    TH(2, "退还", ImmutableMap.<String, String>builder()
            .put(AsOrderService.USE_ORG, "returnOrg")
            .put(AsOrderService.STORAGE_AREA, "returnArea")
            .put(AsOrderService.STORAGE_ADDRESS, "returnAddress")
            .put(AsOrderService.MANAGER_OWNER, AsOrderService.AFTER_MANAGER_OWNER)
            .build()),
    JY(3, "借用", ImmutableMap.<String, String>builder()
            .put(AsOrderService.USE_PERSON, "borrowUser")
            .put(AsOrderService.USE_ORG, "borrowOrg")
            .put(AsOrderService.STORAGE_AREA, "borrowArea")
            .put(AsOrderService.STORAGE_ADDRESS, "borrowAddress")
            .build()),
    GH(4, "归还", ImmutableMap.<String, String>builder()
            .put(AsOrderService.USE_ORG, "backOrg")
            .put(AsOrderService.STORAGE_AREA, "backArea")
            .put(AsOrderService.STORAGE_ADDRESS, "backAddress")
            .put(AsOrderService.MANAGER_OWNER, AsOrderService.AFTER_MANAGER_OWNER)
            .build()),
    DB(5, "调拨", ImmutableMap.<String, String>builder()
            .put(AsOrderService.USE_PERSON, "allocateAfterUser")
            .put(AsOrderService.USE_ORG, "allocateAfterOrg")
            .put(AsOrderService.MANAGER_OWNER, "allocateInManager")
            .put(AsOrderService.ORG_OWNER, "allocateInOrg")
            .put(AsOrderService.STORAGE_AREA, "allocateArea")
            .put(AsOrderService.STORAGE_ADDRESS, "allocateAddress")
            .build()) {
        @Override
        public void checkAsset(List<AsAsset> list, JSONObject orderData, List<AssetStatusDto> assetStatusList) {
            super.checkAsset(list, orderData, assetStatusList);
            // 调出组织
            Long allocateOutOrg = orderData.getLong("allocateOutOrg");
            if (allocateOutOrg != null) {
                boolean allMatch = list.stream().allMatch(asAsset -> {
                    String useOrg = asAsset.getOrgOwner();
                    if (NumberUtil.isNumber(useOrg)) {
                        return NumberUtil.parseLong(useOrg) == allocateOutOrg.longValue();
                    }
                    return false;
                });
                if (!allMatch) {
                    throw new BusinessException(SystemResultCode.ORDER_NEW_ASSET_TYPE_DB_ERROR);
                }
            }
        }

        @Override
        public void setAssetStatus(Long orgId, Long personId, AsAsset toRenew, AsOrderType asOrderType) {
            if (ObjectUtil.isNotNull(orgId) || ObjectUtil.isNotNull(personId)) {
                toRenew.setStatus(2);
                // 选了使用组织，没选使用人
                if (ObjectUtil.isNotNull(orgId) && ObjectUtil.isNull(personId)) {
                    toRenew.getAssetData().put(AsOrderService.USE_PERSON, "");
                }
                return;
            }
            // 闲置状态
            if (null != asOrderType && null != asOrderType.getNewAssetStatus()) {
                toRenew.setStatus(asOrderType.getNewAssetStatus());
            }
            // 调拨为设置使用组织和使用人
            toRenew.getAssetData().put(AsOrderService.USE_PERSON, "");
            toRenew.getAssetData().put(AsOrderService.USE_ORG, "");
        }
    },
//    WX(6, "维修", ImmutableMap.<String, String>builder().build()),
//    WW(7, "维修完成", ImmutableMap.<String, String>builder()
//            .put(AsOrderService.USE_PERSON, "repairAfterUser")
//            .put(AsOrderService.USE_ORG, "repairAfterOrg")
//            .put(AsOrderService.STORAGE_AREA, "repairAfterArea")
//            .put(AsOrderService.STORAGE_ADDRESS, "repairAfterAddress")
//            .build()) {
//        @Override
//        public void setAssetStatus(Long orgId, Long personId, AsAsset toRenew, AsOrderType asOrderType) {
//            if (ObjectUtil.isNotNull(orgId) || ObjectUtil.isNotNull(personId)) {
//                toRenew.setStatus(2L);
//            } else {
//                // 都为空、恢复为资产维修前的状态
//                AsOrderMapper orderMapper = SpringUtil.getBean(AsOrderMapper.class);
//                long beforeStatusStr = orderMapper.selectAssetStatusBeforeRepair(toRenew.getId());
//                toRenew.setStatus(Convert.toLong(beforeStatusStr));
//            }
//        }
//    },
    /**
     * STORAGE_AREA、STORAGE_ADDRESS 配置已去掉
     */
    CZ(8, "处置", ImmutableMap.<String, String>builder()
            .put(AsOrderService.STORAGE_AREA, "disposeArea")
            .put(AsOrderService.STORAGE_ADDRESS, "disposeAddress")
            .build()),
    BG(9, "变更", ImmutableMap.<String, String>builder()
            .put(AsOrderService.USE_PERSON, AsOrderService.USE_PERSON)
            .put(AsOrderService.USE_ORG, AsOrderService.USE_ORG)
            .put(AsOrderService.STORAGE_AREA, AsOrderService.STORAGE_AREA)
            .put(AsOrderService.STORAGE_ADDRESS, AsOrderService.STORAGE_ADDRESS)
            .put(AsOrderService.ORG_OWNER, AsOrderService.ORG_OWNER)
            .put(AsOrderService.MANAGER_OWNER, AsOrderService.MANAGER_OWNER)
            .put(AsOrderService.USE_TIME_LIMIT, AsOrderService.USE_TIME_LIMIT)
            .build()) {
        @Override
        public void setAssetStatus(Long orgId, Long personId, AsAsset toRenew, AsOrderType asOrderType) {
            if (ObjectUtil.isNotNull(orgId) || ObjectUtil.isNotNull(personId)) {
                toRenew.setStatus(2);
            } else {
                toRenew.setStatus(toRenew.getBeforeStatus());
            }
        }
    };

    private final int value;
    private final String name;
    private final Map<String, String> assetMap;

    private static final String SEPARATOR = "；";
    private static final String VALUE_FORMAT = "\"{0}\"";

    OrderTypeEnum(int value, String name, Map<String, String> assetMap) {
        this.value = value;
        this.name = name;
        this.assetMap = assetMap;
    }

    /**
     * 比较数据并返回不同的数据和修改后的数据
     *
     * @param customerData    客户修改提交数据
     * @param originAssetData 资产原始数据
     * @param toRenew         资产状态更新数据
     * @param assetLog        资产日志数据
     * @param asOrderType     orderType所对应的新资产状态
     */
    public final void dealAssetData(JSONObject customerData, JSONObject originAssetData,
                                    AsAsset toRenew, AsAssetLog assetLog, AsOrderType asOrderType,
                                    CacheResourceUtil cacheResourceUtil) {
        JSONObject changeData = new JSONObject(originAssetData);

        StringBuilder sb = new StringBuilder();

        // 使用组织
        setChangeOrg(sb, customerData, changeData, cacheResourceUtil);
        // 使用人
        setChangeUser(sb, customerData, changeData, cacheResourceUtil);
        // 使用区域
        setChangeArea(sb, customerData, changeData, cacheResourceUtil);
        // 存放位置
        setChangeAddress(sb, customerData, changeData);
        // 使用期限
        setUseTimeLimit(sb, customerData, changeData);

        // 资产分类
//        setChangeCategory(sb, customerData, changeData, orderMapper);

        // 所属管理组织
        setOrgOwner(sb, customerData, changeData, cacheResourceUtil);

        // 所属管理员
        setManagerOwner(sb, customerData, changeData, cacheResourceUtil);
        toRenew.setAssetData(changeData);

        Long frontOrg = customerData.getObject(assetMap.get(AsOrderService.USE_ORG), Long.class);
        Long frontUser = customerData.getObject(assetMap.get(AsOrderService.USE_PERSON), Long.class);
        // 设置新资产状态
        setAssetStatus(frontOrg, frontUser, toRenew, asOrderType);
        assetLog.setChangeData(changeData).setActionContent(removeSuffix(sb.toString(), SEPARATOR));

        // 重置状态
        toRenew.setBeforeStatus(0);
    }

    private void setManagerOwner(StringBuilder sb, JSONObject customerData, JSONObject changeData, CacheResourceUtil cacheResourceUtil) {
        Long frontManagerOwner = customerData.getObject(assetMap.get(AsOrderService.MANAGER_OWNER), Long.class);
        Long currentManagerOwner = changeData.getObject(AsOrderService.MANAGER_OWNER, Long.class);
        if (ObjectUtil.isNotNull(frontManagerOwner)) {
            if (!ObjectUtil.equal(frontManagerOwner, currentManagerOwner)) {
                changeData.put(AsOrderService.MANAGER_OWNER, Convert.toStr(frontManagerOwner, StrUtil.EMPTY));
            }
            if (ObjectUtil.isNotNull(currentManagerOwner)) {
                if (!ObjectUtil.equal(frontManagerOwner, currentManagerOwner)) {
                    sb.append("所属管理员由").append(getFormatValue(cacheResourceUtil.getUserName(currentManagerOwner)))
                            .append("变成").append(getFormatValue(cacheResourceUtil.getUserName(frontManagerOwner))).append(SEPARATOR);
                }
            } else {
                sb.append("所属管理员由").append(getFormatValue("空"))
                        .append("变成").append(getFormatValue(cacheResourceUtil.getUserName(frontManagerOwner))).append(SEPARATOR);
            }
        }
    }

    private void setOrgOwner(StringBuilder sb, JSONObject customerData, JSONObject changeData, CacheResourceUtil cacheResourceUtil) {
        Long frontOrgOwner = customerData.getObject(assetMap.get(AsOrderService.ORG_OWNER), Long.class);
        Long currentOrgOwner = changeData.getObject(AsOrderService.ORG_OWNER, Long.class);
        if (ObjectUtil.isNotNull(frontOrgOwner)) {
            if (!ObjectUtil.equal(frontOrgOwner, currentOrgOwner)) {
                changeData.put(AsOrderService.ORG_OWNER, Convert.toStr(frontOrgOwner, StrUtil.EMPTY));
            }
            if (ObjectUtil.isNotNull(currentOrgOwner)) {
                if (!ObjectUtil.equal(frontOrgOwner, currentOrgOwner)) {
                    sb.append("所属管理组织由").append(getFormatValue(cacheResourceUtil.getOrgName(currentOrgOwner)))
                            .append("变成").append(getFormatValue(cacheResourceUtil.getOrgName(frontOrgOwner))).append(SEPARATOR);
                }
            } else {
                sb.append("所属管理组织由").append(getFormatValue("空"))
                        .append("变成").append(getFormatValue(cacheResourceUtil.getOrgName(frontOrgOwner))).append(SEPARATOR);
            }
        }
    }

    /*private void setChangeCategory(StringBuilder sb, JSONObject customerData, JSONObject changeData, AsOrderMapper orderMapper) {
        Long frontAssetCategory = customerData.getObject(assetMap.get(AsOrderService.ASSET_CATEGORY), Long.class);
        Long currentAssetCategory = changeData.getObject(AsOrderService.ASSET_CATEGORY, Long.class);
        if (ObjectUtil.isNotNull(frontAssetCategory)) {
            if (!ObjectUtil.equal(frontAssetCategory, currentAssetCategory)) {
                changeData.put(AsOrderService.ASSET_CATEGORY, frontAssetCategory);
            }
            CategoryDto front = orderMapper.selectCateByCateId(frontAssetCategory);
            if (ObjectUtil.isNotNull(currentAssetCategory)) {
                if (!ObjectUtil.equal(frontAssetCategory, currentAssetCategory)) {
                    CategoryDto current = orderMapper.selectCateByCateId(currentAssetCategory);
                    sb.append("资产分类由")
                            .append(getFormatValue(current.getCategoryName()))
                            .append("变成").append(getFormatValue(front.getCategoryName())).append(SEPARATOR);
                }
            } else {
                sb.append("资产分类由").append(getFormatValue("空"))
                        .append("变成").append(getFormatValue(front.getCategoryName())).append(SEPARATOR);
            }
        }
    }*/

    private void setUseTimeLimit(StringBuilder sb, JSONObject customerData, JSONObject changeData) {
        String frontUseTimeLimit = customerData.getObject(assetMap.get(AsOrderService.USE_TIME_LIMIT), String.class);
        String currentUseTimeLimit = changeData.getObject(AsOrderService.USE_TIME_LIMIT, String.class);
        if (isNotBlank(frontUseTimeLimit)) {
            if (!ObjectUtil.equal(frontUseTimeLimit, currentUseTimeLimit)) {
                changeData.put(AsOrderService.USE_TIME_LIMIT, Convert.toStr(frontUseTimeLimit, StrUtil.EMPTY));
            }
            if (isBlank(currentUseTimeLimit)) {
                sb.append("使用期限").append(getFormatValue("空"))
                        .append("变成").append(getFormatValue(frontUseTimeLimit)).append(SEPARATOR);
            } else {
                if (!StringUtils.equals(frontUseTimeLimit, currentUseTimeLimit)) {
                    sb.append("使用期限").append(getFormatValue(currentUseTimeLimit))
                            .append("变成").append(getFormatValue(frontUseTimeLimit)).append(SEPARATOR);
                }
            }
        }
    }

    private void setChangeAddress(StringBuilder sb, JSONObject customerData, JSONObject changeData) {
        String frontAddress = customerData.getObject(assetMap.get(AsOrderService.STORAGE_ADDRESS), String.class);
        String currentAddress = changeData.getObject(AsOrderService.STORAGE_ADDRESS, String.class);
//        if (isNotBlank(frontAddress) && !changeData.containsKey(AsOrderService.STORAGE_ADDRESS)) {
//            throw new BusinessException(SystemResultCode.ASSET_FIELD_NOT_SETTING, "存放位置");
//        }
        if (isNotBlank(frontAddress)) {
            if (!ObjectUtil.equal(frontAddress, currentAddress)) {
                changeData.put(AsOrderService.STORAGE_ADDRESS, Convert.toStr(frontAddress, StrUtil.EMPTY));
            }
            if (isBlank(currentAddress)) {
                sb.append("存放位置由").append(getFormatValue("空"))
                        .append("变成").append(getFormatValue(frontAddress)).append(SEPARATOR);
            } else {
                if (!StringUtils.equals(frontAddress, currentAddress)) {
                    sb.append("存放位置由").append(getFormatValue(currentAddress))
                            .append("变成").append(getFormatValue(frontAddress)).append(SEPARATOR);
                }
            }
        }
    }

    private void setChangeOrg(StringBuilder sb, JSONObject customerData, JSONObject changeData, CacheResourceUtil cacheResourceUtil) {
        // 使用组织
        Long frontOrg = customerData.getObject(assetMap.get(AsOrderService.USE_ORG), Long.class);
        Long currentOrg = changeData.getObject(AsOrderService.USE_ORG, Long.class);
        if (this == TH || this == GH || this == CZ) {
            changeData.put(AsOrderService.USE_ORG, "");
            if (ObjectUtil.isNotNull(currentOrg)) {
                sb.append("使用组织由")
                        .append(getFormatValue(cacheResourceUtil.getOrgName(currentOrg)))
                        .append("变成").append(getFormatValue("空")).append(SEPARATOR);
            }
            // bug ID1029833
            if (this == TH || this == GH) {
                currentOrg = changeData.getObject(AsOrderService.ORG_OWNER, Long.class);
                if (ObjectUtil.isNotNull(currentOrg)) {
                    if (!ObjectUtil.equal(frontOrg, currentOrg)) {
                        if (!ObjectUtil.isNull(frontOrg)) {
                            changeData.put(AsOrderService.ORG_OWNER, Convert.toStr(frontOrg, StrUtil.EMPTY));
                            sb.append("所属管理组织由").append(getFormatValue(cacheResourceUtil.getOrgName(currentOrg)))
                                    .append("变成").append(getFormatValue(cacheResourceUtil.getOrgName(frontOrg))).append(SEPARATOR);
                        }
                    }
                } else {
                    if (ObjectUtil.isNotNull(frontOrg)) {
                        changeData.put(AsOrderService.ORG_OWNER, Convert.toStr(frontOrg, StrUtil.EMPTY));
                        sb.append("所属管理组织由").append(getFormatValue("空"))
                                .append("变成").append(getFormatValue(cacheResourceUtil.getOrgName(frontOrg))).append(SEPARATOR);
                    }
                }
            }
            return;
        }
        if (ObjectUtil.isNull(frontOrg)) {
            return;
        }
        if (!ObjectUtil.equal(frontOrg, currentOrg)) {
            changeData.put(AsOrderService.USE_ORG, Convert.toStr(frontOrg, StrUtil.EMPTY));
        }
        if (ObjectUtil.isNotNull(currentOrg)) {
            if (!ObjectUtil.equal(frontOrg, currentOrg)) {
                sb.append("使用组织由").append(getFormatValue(cacheResourceUtil.getOrgName(currentOrg)))
                        .append("变成").append(getFormatValue(cacheResourceUtil.getOrgName(frontOrg))).append(SEPARATOR);
            }
        } else {
            sb.append("使用组织由").append(getFormatValue("空"))
                    .append("变成").append(getFormatValue(cacheResourceUtil.getOrgName(frontOrg))).append(SEPARATOR);
        }
    }

    private void setChangeUser(StringBuilder sb, JSONObject customerData, JSONObject changeData, CacheResourceUtil cacheResourceUtil) {
        Long frontUser = customerData.getObject(assetMap.get(AsOrderService.USE_PERSON), Long.class);
        Long currentUser = changeData.getObject(AsOrderService.USE_PERSON, Long.class);
        if (this == TH || this == GH || this == CZ) {
            changeData.put(AsOrderService.USE_PERSON, "");
            if (ObjectUtil.isNotNull(currentUser)) {
                sb.append("使用人由").append(getFormatValue(cacheResourceUtil.getUserName(currentUser))).append("变成")
                        .append(getFormatValue("空"))
                        .append(SEPARATOR);
            }
            return;
        }
        if (ObjectUtil.isNotNull(frontUser)) {
            if (!ObjectUtil.equal(frontUser, currentUser)) {
                changeData.put(AsOrderService.USE_PERSON, Convert.toStr(frontUser, StrUtil.EMPTY));
            }
            if (ObjectUtil.isNotNull(currentUser)) {
                if (!ObjectUtil.equal(frontUser, currentUser)) {
                    sb.append("使用人由").append(getFormatValue(cacheResourceUtil.getUserName(currentUser)))
                            .append("变成").append(getFormatValue(cacheResourceUtil.getUserName(frontUser))).append(SEPARATOR);
                }
            } else {
                sb.append("使用人由").append(getFormatValue("空"))
                        .append("变成").append(getFormatValue(cacheResourceUtil.getUserName(frontUser))).append(SEPARATOR);
            }
        }
    }

    private void setChangeArea(StringBuilder sb, JSONObject customerData, JSONObject changeData, CacheResourceUtil cacheResourceUtil) {
        Long frontArea = customerData.getObject(assetMap.get(AsOrderService.STORAGE_AREA), Long.class);
        Long currentArea = changeData.getObject(AsOrderService.STORAGE_AREA, Long.class);
        if (ObjectUtil.isNotNull(frontArea)) {
            if (!ObjectUtil.equal(frontArea, currentArea)) {
                changeData.put(AsOrderService.STORAGE_AREA, Convert.toStr(frontArea, StrUtil.EMPTY));
            }
            if (ObjectUtil.isNotNull(currentArea)) {
                if (!ObjectUtil.equal(frontArea, currentArea)) {
                    sb.append("使用区域由").append(getFormatValue(cacheResourceUtil.getAreaName(currentArea)))
                            .append("变成").append(getFormatValue(cacheResourceUtil.getAreaName(frontArea))).append(SEPARATOR);
                }
            } else {
                sb.append("使用区域由").append(getFormatValue("空"))
                        .append("变成").append(getFormatValue(cacheResourceUtil.getAreaName(frontArea))).append(SEPARATOR);
            }
        }
    }

    public void setAssetStatus(Long orgId, Long personId, AsAsset toRenew, AsOrderType asOrderType) {
        if (null != asOrderType && null != asOrderType.getNewAssetStatus()) {
            toRenew.setStatus(asOrderType.getNewAssetStatus());
        }
    }

    /**
     * 校验提交所选资产 资产状态是否符合要求
     *
     * @param list            资产原始数据、包含状态
     * @param orderData       提交单据的自定义数据
     * @param assetStatusList 对应单据资产所对应资产状态列表
     */
    public void checkAsset(List<AsAsset> list, JSONObject orderData, List<AssetStatusDto> assetStatusList) {
        List<Integer> idList = assetStatusList.stream().map(AssetStatusDto::getId).collect(Collectors.toList());
        boolean allMatch = list.stream().allMatch(asAsset -> idList.contains(asAsset.getStatus()));
        if (!allMatch) {
            String statusName = assetStatusList.stream().map(AssetStatusDto::getName)
                    .collect(Collectors.joining(","));
            throw new BusinessException(SystemResultCode.ORDER_NEW_ASSET_TYPE_ERROR, getName(), statusName);
        }
    }

    public String getName() {
        return name;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public Map<String, String> getAssetMap() {
        return assetMap;
    }

    public static OrderTypeEnum getByType(int orderType) {
        OrderTypeEnum[] enums = OrderTypeEnum.values();
        return Stream.of(enums).filter(orderTypeEnum -> orderType == orderTypeEnum.value)
                .findFirst().orElseThrow(() ->
                        new BusinessException(HttpStatus.OK.value(), "单据不存在"));
    }

    public static String getOrderNoPrefix(int orderType) {
        return getByType(orderType).name();
    }

    public static String getFormatValue(String oriVal) {
        return MessageFormat.format(VALUE_FORMAT, oriVal);
    }

}
