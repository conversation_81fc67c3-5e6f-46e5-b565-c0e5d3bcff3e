package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.means.model.AsTagMaterial;
import com.niimbot.asset.means.service.AsTagMaterialService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.TagMaterialQueryDto;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * 材质控制器
 *
 * <AUTHOR>
 * @Date 2021/3/8
 */
@RestController
@RequestMapping("server/system/tagMaterial")
@RequiredArgsConstructor
public class AdminTagMaterialServiceController {
    private final AsTagMaterialService tagMaterialService;

    /**
     * 查询所有材质数据
     *
     * @return 所有数据
     */
    @GetMapping("list")
    public List<AsTagMaterial> list() {
        return this.tagMaterialService.list(Wrappers.<AsTagMaterial>lambdaQuery().orderByAsc(AsTagMaterial::getSortNum));
    }

    /**
     * 查询所有材质分页
     *
     * @param dto 查询参数
     * @return 所有数据
     */
    @GetMapping("page")
    public IPage<AsTagMaterial> page(TagMaterialQueryDto dto) {
        return this.tagMaterialService.page(dto.buildIPage(), Wrappers
                .<AsTagMaterial>lambdaQuery().orderByAsc(AsTagMaterial::getSortNum));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public AsTagMaterial getById(@PathVariable Long id) {
        return this.tagMaterialService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param tagMaterial 实体对象
     * @return 新增结果
     */
    @PostMapping
    public Boolean insert(@RequestBody AsTagMaterial tagMaterial) {
        AsTagMaterial one = this.tagMaterialService.getOne(Wrappers.<AsTagMaterial>lambdaQuery()
                .eq(AsTagMaterial::getName, tagMaterial.getName()));
        if (null != one) {
            throw new BusinessException(SystemResultCode.PRINT_TAG_MATERIAL_NAME_EXIST);
        }
        return this.tagMaterialService.save(tagMaterial);
    }

    /**
     * 修改数据
     *
     * @param tagMaterial 实体对象
     * @return 修改结果
     */
    @PutMapping
    public Boolean update(@RequestBody AsTagMaterial tagMaterial) {
        AsTagMaterial one = this.tagMaterialService.getOne(Wrappers.<AsTagMaterial>lambdaQuery()
                .eq(AsTagMaterial::getName, tagMaterial.getName())
                .ne(AsTagMaterial::getId, tagMaterial.getId()));
        if (null != one) {
            throw new BusinessException(SystemResultCode.PRINT_TAG_MATERIAL_NAME_EXIST);
        }
        return this.tagMaterialService.updateById(tagMaterial);
    }

    /**
     * 删除材质
     *
     * @param id id
     * @return 结果
     */
    @DeleteMapping("{id}")
    public Boolean removeById(@PathVariable("id") Long id) {
        return this.tagMaterialService.removeById(id);
    }
}
