package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsAssetOperation;
import com.niimbot.means.AssetOperationDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资产动作表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-24
 */
public interface AsAssetOperationMapper extends BaseMapper<AsAssetOperation> {

    /**
     * 根据资产状态获取对应的操作
     *
     * @param statusIds 状态id
     * @return 对应的操作list
     */
    List<AssetOperationDto> getAssetOperationByStatus(List<Integer> statusIds);

    /**
     * 查询全部资产操作
     *
     * @return 对应的操作list
     */
    List<AssetOperationDto> allAssetOpt();

    /**
     * 单据类型查询
     *
     * @param orderType orderType
     * @return AssetOperationDto
     */
    AssetOperationDto assetOptByOrderType(@Param("orderType") Integer orderType);
}
