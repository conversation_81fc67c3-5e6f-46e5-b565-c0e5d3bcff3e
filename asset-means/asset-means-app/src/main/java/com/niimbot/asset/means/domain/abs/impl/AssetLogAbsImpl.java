package com.niimbot.asset.means.domain.abs.impl;

import com.niimbot.asset.means.mapstruct.MeansSystemMapStruct;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.asset.system.abs.AssetLogAbs;
import com.niimbot.asset.system.dto.AssetLogSaveBatchCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/16 2:31 下午
 */
@RestController
@RequestMapping("/client/abs/means/AssetLogAbs/")
@RequiredArgsConstructor
public class AssetLogAbsImpl implements AssetLogAbs {

    private final MeansSystemMapStruct systemMapStruct;

    private final AsAssetLogService asAssetLogService;

    @Override
    public Boolean saveBatchAssetLog(AssetLogSaveBatchCmd cmd) {
        return asAssetLogService.saveBatch(cmd.getLogs().stream().map(systemMapStruct::convertAsAssetLogCoToModel).collect(Collectors.toList()));
    }
}
