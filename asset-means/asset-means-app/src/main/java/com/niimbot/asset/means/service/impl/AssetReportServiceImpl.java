package com.niimbot.asset.means.service.impl;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.means.mapper.AsAssetMapper;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsAssetStatus;
import com.niimbot.asset.means.model.AsCategory;
import com.niimbot.asset.means.service.AsAssetStatusService;
import com.niimbot.asset.means.service.AssetReportService;
import com.niimbot.asset.means.service.CategoryService;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.asset.system.service.OrgService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.AssetIncrReportDto;
import com.niimbot.means.AssetOrgReportDto;
import com.niimbot.report.AssetReportDto;
import com.niimbot.report.AssetStatusReportDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 资产统计 Service 业务逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Slf4j
@Service
public class AssetReportServiceImpl extends ServiceImpl<AsAssetMapper, AsAsset> implements AssetReportService {

    private final AsAssetStatusService assetStatusService;

    private final CategoryService categoryService;

    private final OrgService orgService;

    @Autowired
    public AssetReportServiceImpl(AsAssetStatusService assetStatusService,
                                  CategoryService categoryService,
                                  OrgService orgService) {
        this.assetStatusService = assetStatusService;
        this.categoryService = categoryService;
        this.orgService = orgService;
    }

    /**
     * 首页-资产状态统计
     *
     * @return 资产状态统计对象
     */
    @Override
    public AssetReportDto<AssetStatusReportDto> assetStatusReport() {
        // 结果对象
        AssetReportDto<AssetStatusReportDto> statusReport = new AssetReportDto<>();

        // 首先获取公司的状态类型
        List<AsAssetStatus> assetStatuses = assetStatusService.list(new QueryWrapper<AsAssetStatus>()
                .lambda().eq(AsAssetStatus::getCompanyId, 0));

        JSONObject statusData = this.getBaseMapper().assetStatusReport();

        BigDecimal assetTotal = new BigDecimal("0");
        BigDecimal assetTotalWorth = new BigDecimal("0.0000");

        for (AsAssetStatus st : assetStatuses) {
            assetTotal = assetTotal.add(new BigDecimal(statusData.getString(st.getCode() + "_num")));
            assetTotalWorth = assetTotalWorth.add(new BigDecimal(statusData.getString(st.getCode() + "_price")));
        }

        statusReport.setAssetTotal(assetTotal.toPlainString());
        statusReport.setAssetTotalWorth(assetTotalWorth.setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

        // 统计当前资产状态
        for (AsAssetStatus st : assetStatuses) {
            AssetStatusReportDto assetStatusReportDto = new AssetStatusReportDto();
            assetStatusReportDto.setCn(st.getName() + "资产");
            assetStatusReportDto.setKey(st.getCode());
            assetStatusReportDto.setStatus(st.getId());
            // 设置数量和金额
            assetStatusReportDto.setNum(String.valueOf(statusData.get(st.getCode() + "_num")));
            assetStatusReportDto.setWorth(statusData.getBigDecimal(st.getCode() + "_price").setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            // 计算百分比
            BigDecimal numPr = divide(assetStatusReportDto.getNum(), assetTotal, 5);
            BigDecimal worthPr = divide(assetStatusReportDto.getWorth(), assetTotalWorth, 5);
            assetStatusReportDto.setPr(numPr.toPlainString());
            assetStatusReportDto.setWorthPr(worthPr.toPlainString());
            // 设置颜色
            assetStatusReportDto.setColor(st.getColor());
            // 添加到列表中
            statusReport.getList().add(assetStatusReportDto);
        }
        // 对百分比总和不为1特殊处理
        List<AssetStatusReportDto> list = statusReport.getList();
        configPr(list);
        configWorthPr(list);
        // 恢复排序- 默认按status 状态值排序
        CollUtil.sort(list, Comparator.comparingLong(AssetStatusReportDto::getStatus));
        return statusReport;
    }

    private void configPr(List<AssetStatusReportDto> list) {
        int sum = 0;
        for (AssetStatusReportDto reportDto : list) {
            String pr = reportDto.getPr();
            int l = new BigDecimal(pr).multiply(new BigDecimal("10000")).intValue();
            sum += l;
        }
        int differ = 10000 - sum;
        if (differ > 0 && sum != 0) {
            CollUtil.sort(list, (first, second) -> {
                int firstLast = new BigDecimal(first.getPr()).multiply(new BigDecimal("100000")).intValue() % 10;
                int secondLast = new BigDecimal(second.getPr()).multiply(new BigDecimal("100000")).intValue() % 10;
                return secondLast - firstLast;
            });
            for (int i = 0; i < list.size(); i++) {
                AssetStatusReportDto dto = list.get(i);
                if (differ > 0) {
                    int l = new BigDecimal(dto.getPr()).multiply(new BigDecimal("10000")).intValue();
                    String pr = divide(l + 1, 10000, 4).setScale(4).stripTrailingZeros().toPlainString();
                    dto.setPr(pr);
                    differ--;
                } else {
                    dto.setPr(new BigDecimal(dto.getPr()).setScale(4, BigDecimal.ROUND_FLOOR).stripTrailingZeros().toPlainString());
                }
            }
        } else {
            list.forEach(dto -> {
                int l = new BigDecimal(dto.getPr()).multiply(new BigDecimal("10000")).intValue();
                String pr = divide(l, 10000, 4).setScale(4).stripTrailingZeros().toPlainString();
                dto.setPr(pr);
            });
        }
    }

    private void configWorthPr(List<AssetStatusReportDto> list) {
        int sum = 0;
        for (AssetStatusReportDto reportDto : list) {
            int l = new BigDecimal(reportDto.getWorthPr()).multiply(new BigDecimal("10000")).intValue();
            sum += l;
        }
        int differ = 10000 - sum;
        if (differ > 0 && sum != 0) {
            CollUtil.sort(list, (first, second) -> {
                int firstLast = new BigDecimal(first.getWorthPr()).multiply(new BigDecimal("100000")).intValue() % 10;
                int secondLast = new BigDecimal(second.getWorthPr()).multiply(new BigDecimal("100000")).intValue() % 10;
                return secondLast - firstLast;
            });
            for (int i = 0; i < list.size(); i++) {
                AssetStatusReportDto dto = list.get(i);
                if (differ > 0) {
                    int l = new BigDecimal(dto.getWorthPr()).multiply(new BigDecimal("10000")).intValue();
                    String pr = divide(l + 1, 10000, 4).setScale(4).stripTrailingZeros().toPlainString();
                    dto.setWorthPr(pr);
                    differ--;
                } else {
                    dto.setWorthPr(new BigDecimal(dto.getWorthPr()).setScale(4, BigDecimal.ROUND_FLOOR).stripTrailingZeros().toPlainString());
                }
            }
        } else {
            list.stream().forEach(dto -> {
                int l = new BigDecimal(dto.getWorthPr()).multiply(new BigDecimal("10000")).intValue();
                String worthPr = divide(l, 10000, 4).setScale(4).stripTrailingZeros().toPlainString();
                dto.setWorthPr(worthPr);
            });
        }
    }

    /**
     * 首页-资产增量统计
     *
     * @param year 年
     * @return 资产增量统计对象
     */
    @Override
    public AssetIncrReportDto assetIncrReport(Long year) {
        // 获取当前时间
        String now = DateUtil.now();
        // 自动感应格式
        Date date = DateUtil.parse(now);
        // 当前年
        int currentYear = DateUtil.year(date);

        // 目前只支持查看2018至%s年数据
        if (year > currentYear) {
            throw new BusinessException(MeansResultCode.ASSET_INCR_SELECT_LIMIT, String.valueOf(year));
        }

        // 根据查询的年判断当前要查询的数据
        String month = "12";
        String day = "31";
        if (currentYear == year) {
            // 获取指定日期成员（通过DateTime同样可以完成）
            month = String.valueOf(DateUtil.month(date) + 1);
            day = String.valueOf(DateUtil.dayOfMonth(date));
        }

        // 获取指定年的一月一号
        String beginTime = year + "-01-01 00:00:00";
        // 获取结束时间
        String endTime = year + "-" + month + "-" + day + " 23:59:59";
        List<HashMap<String, Object>> assetMonthsReport = this.getBaseMapper().monthsReport(beginTime, endTime);

        // 资产数量数据
        HashMap<String, Object> assetMonthsReportData = new HashMap<>();
        if (CollUtil.isNotEmpty(assetMonthsReport)) {
            for (HashMap<String, Object> v : assetMonthsReport) {
                assetMonthsReportData.put((String) v.get("months"), v.get("asset_num"));
            }
        }

        List<HashMap<String, Object>> assetMonthsWorthReport = this.getBaseMapper().monthsWorthReport(beginTime, endTime);

        // 资产价值数据
        HashMap<String, Object> assetMonthsWorthReportData = new HashMap<>();
        if (CollUtil.isNotEmpty(assetMonthsWorthReport)) {
            for (HashMap<String, Object> v : assetMonthsWorthReport) {
                assetMonthsWorthReportData.put((String) v.get("months"), v.get("price_sum"));
            }
        }

        // 初始化资产数量数组
        ArrayList<Long> numArr = Lists.newArrayList();
        // 初始化资产价值数组
        ArrayList<String> worthArr = Lists.newArrayList();
        for (int i = 1; i <= Integer.parseInt(month); i++) {
            String key = year + StrUtil.padPre(String.valueOf(i), 2, '0'); // 本月的key
            numArr.add((Long) assetMonthsReportData.getOrDefault(key, 0L));
            worthArr.add(NumberUtil.roundStr(
                    Convert.toStr(assetMonthsWorthReportData.getOrDefault(key, "0.00"), "0.00"), 4)
            );
        }


        // 实例化资产增量对象
        AssetIncrReportDto assetIncrReportDto = new AssetIncrReportDto();
        assetIncrReportDto.setMonthReport(numArr);
        assetIncrReportDto.setMonthWorthReport(worthArr);

        return assetIncrReportDto;
    }

    /**
     * 首页-资产分类统计
     *
     * @param pid 父id
     * @return 资产分类统计对象
     */
    @Override
    public List<AssetOrgReportDto> assetReportByCategory(Long pid) {
        if (ObjectUtil.isEmpty(pid)) {
            pid = 0L;
        }

        // 资产所属分类统计数据
        List<HashMap<Object, Object>> categoryAssetReport = this.getBaseMapper().getCategoryAssetReport();

        //查询所有的资产分类数据
        List<AsCategory> categoryData = categoryService.list();
        // 补全资产分类父id
        ArrayList<AssetOrgReportDto> assetOrgReportList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(categoryData)) {
            for (AsCategory category : categoryData) {
                AssetOrgReportDto assetOrgReportDto = new AssetOrgReportDto();
                assetOrgReportDto.setIsHasChild(true)
                        .setName(category.getCategoryName())
                        .setAssetNum("0")
                        .setAssetNumPr("0")
                        .setAssetTotal("0").setWorthPr("0")
                        .setWorthSum("0").setWorthTotal("0");
                BeanUtil.copyProperties(category, assetOrgReportDto);
                assetOrgReportList.add(assetOrgReportDto);
            }
        }

        // 组装分类字典数据
        Map<Long, Dict> cache = new HashMap<>();
        categoryAssetReport.forEach(org -> cache.put((Long) org.get("id"), Dict.create().set("paths", org.get("paths"))
                .set("asset_num", org.get("asset_num")).set("worth_sum", org.get("worth_sum"))));

        // 补全没有资产的父级分类
        for (AssetOrgReportDto assetOrgReportDto : assetOrgReportList) {
            if (!cache.containsKey(assetOrgReportDto.getId())) {
                cache.put(assetOrgReportDto.getId(), Dict.create().set("paths", assetOrgReportDto.getPaths())
                        .set("asset_num", "0").set("worth_sum", "0"));
            }

            assetOrgReportDto.setAssetNum(String.valueOf(cache.get(assetOrgReportDto.getId()).getInt("asset_num")))
                    .setWorthSum(String.valueOf(cache.get(assetOrgReportDto.getId()).getBigDecimal("worth_sum")));
        }

        // 累计计算本级和下级数量
        assetOrgReportList.forEach(org -> {
            Dict dict = cache.get(org.getId());
            if (ObjectUtil.isNotNull(dict)) {
                String[] paths = dict.getStr("paths").split(",");
                Integer assetNum = dict.getInt("asset_num");
                BigDecimal worthSum = dict.getBigDecimal("worth_sum");
                // 遍历 path
                for (String path : paths) {
                    if (Convert.toLong(path) != 0) {
                        Dict orgDict = cache.get(Convert.toLong(path));
                        if (ObjectUtil.isNotNull(orgDict)) {
                            orgDict.set("asset_num", assetNum + orgDict.getInt("asset_num"));
                            orgDict.set("worth_sum", worthSum.add(orgDict.getBigDecimal("worth_sum")));
                            cache.put(Convert.toLong(path), orgDict);
                        }
                    }
                }
            }
        });

        // 初始化资产数量统计和资产价值统计
        BigDecimal assetCountNum = new BigDecimal("0");
        BigDecimal assetCountWorth = new BigDecimal("0.00");

        // 初始化资产所属分类统计数组
        for (HashMap<Object, Object> v : categoryAssetReport) {
            assetCountNum = assetCountNum.add(new BigDecimal(String.valueOf(v.get("asset_num"))));
            assetCountWorth = assetCountWorth.add(new BigDecimal(String.valueOf(v.get("worth_sum"))));
        }

        for (AssetOrgReportDto val : assetOrgReportList) {
            // 补全分类对应的资产数量和价值
            val.setAssetTotal(String.valueOf(cache.get(val.getId()).getInt("asset_num")))
                    .setWorthTotal(String.valueOf(cache.get(val.getId()).getBigDecimal("worth_sum")));

            // 统计百分比
            // 设置数量占比
            String assetNumPr = "0";
            if (!NumberUtil.equals(new BigDecimal(val.getAssetTotal()), BigDecimal.valueOf(0)) &&
                    !NumberUtil.equals(assetCountNum, BigDecimal.valueOf(0))) {
                assetNumPr = NumberUtil.roundStr(String.valueOf(
                        NumberUtil.div(new BigDecimal(val.getAssetTotal()), assetCountNum)), 4);
            }
            val.setAssetNumPr(assetNumPr);

            // 设置金额占比
            String worthPr = "0";
            if (!NumberUtil.equals(new BigDecimal(val.getWorthTotal()), BigDecimal.valueOf(0)) &&
                    !NumberUtil.equals(assetCountWorth, BigDecimal.valueOf(0))) {
                worthPr = NumberUtil.roundStr(String.valueOf(
                        NumberUtil.div(new BigDecimal(val.getWorthTotal()), assetCountWorth)), 4);
            }
            val.setWorthPr(worthPr);
        }

        // 返回最后数据
        ArrayList<AssetOrgReportDto> assetOrgReportReturnData = Lists.newArrayList();
        // 收集所有的pid
        List<Long> pidArr = assetOrgReportList.stream().map(AssetOrgReportDto::getPid).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(assetOrgReportList)) {
            for (AssetOrgReportDto val : assetOrgReportList) {
                // 如果资产分类下资产数量为0 不展示
                if ("0".equals(val.getAssetTotal())) {
                    continue;
                }

                if (val.getPid().equals(pid) || val.getId().equals(pid)) {
                    //默认可以点击
                    val.setIsHasChild(true);
                    // 名称修改为直属组织
                    if (val.getId().equals(pid)) {
//                        val.setName("直属分类");
                        //不能点击
                        val.setIsHasChild(false);

                        // total 改成当前数量 不包括下级数据
                        val.setAssetTotal(val.getAssetNum());
                        val.setWorthTotal(val.getWorthSum());

                        // 设置数量占比
                        String assetNumPr = "0";
                        if (!NumberUtil.equals(new BigDecimal(val.getAssetTotal()), BigDecimal.valueOf(0)) &&
                                !NumberUtil.equals(assetCountNum, BigDecimal.valueOf(0))) {
                            assetNumPr = NumberUtil.roundStr(String.valueOf(
                                    NumberUtil.div(new BigDecimal(val.getAssetTotal()), assetCountNum)), 4);
                        }
                        val.setAssetNumPr(assetNumPr);

                        // 设置金额占比
                        String worthPr = "0";
                        if (!NumberUtil.equals(new BigDecimal(val.getWorthTotal()), BigDecimal.valueOf(0)) &&
                                !NumberUtil.equals(assetCountWorth, BigDecimal.valueOf(0))) {
                            worthPr = NumberUtil.roundStr(String.valueOf(
                                    NumberUtil.div(new BigDecimal(val.getWorthTotal()), assetCountWorth)), 4);
                        }
                        val.setWorthPr(worthPr);
                    }

                    // 该节点不作为父级 或者当前节点数量等于当前节点及子节点的总数量
                    if (!pidArr.contains(val.getId()) || val.getAssetNum().equals(val.getAssetTotal())) {
                        //不能点击
                        val.setIsHasChild(false);
                    }

                    assetOrgReportReturnData.add(val);
                }
            }
        }

        return assetOrgReportReturnData;
    }

    /**
     * 首页-资产使用组织统计
     *
     * @param pid 父id
     * @return 资产使用组织统计对象
     */
    @Override
    public List<AssetOrgReportDto> assetReportByUseOrg(Long rootId, Long pid) {
        if (ObjectUtil.isEmpty(pid)) {
            pid = rootId;
        }

        // 资产所属组织统计数据
        List<HashMap<Object, Object>> orgAssetGroupReport = this.getBaseMapper().getUseOrgAssetGroupReport();

        //查询所有的资产分类数据
        List<AsOrg> orgData = orgService.list();
        // 补全资产分类父id
        ArrayList<AssetOrgReportDto> assetOrgReportList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(orgData)) {
            for (AsOrg org : orgData) {
                AssetOrgReportDto assetOrgReportDto = new AssetOrgReportDto();
                assetOrgReportDto.setIsHasChild(true)
                        .setName(org.getOrgName())
                        .setAssetNum("0")
                        .setAssetNumPr("0")
                        .setAssetTotal("0").setWorthPr("0")
                        .setWorthSum("0").setWorthTotal("0");
                BeanUtil.copyProperties(org, assetOrgReportDto);
                assetOrgReportList.add(assetOrgReportDto);
            }
        }

        // 组装组织字典数据
        Map<Long, Dict> cache = new HashMap<>();
        orgAssetGroupReport.forEach(org -> cache.put((Long) org.get("id"), Dict.create().set("paths", org.get("paths"))
                .set("asset_num", org.get("asset_num")).set("worth_sum", org.get("worth_sum"))));

        // 补全没有资产的父级分类
        for (AssetOrgReportDto assetOrgReportDto : assetOrgReportList) {
            if (!cache.containsKey(assetOrgReportDto.getId())) {
                cache.put(assetOrgReportDto.getId(), Dict.create().set("paths", assetOrgReportDto.getPaths())
                        .set("asset_num", "0").set("worth_sum", "0"));
            }

            assetOrgReportDto.setAssetNum(String.valueOf(cache.get(assetOrgReportDto.getId()).getInt("asset_num")))
                    .setWorthSum(String.valueOf(cache.get(assetOrgReportDto.getId()).getBigDecimal("worth_sum")));
        }

        // 累计计算本级和下级数量
        orgAssetGroupReport.forEach(org -> {
            Dict dict = cache.get(org.get("id"));
            if (ObjectUtil.isNotNull(dict)) {
                String[] paths = dict.getStr("paths").split(",");
                Integer assetNum = dict.getInt("asset_num");
                BigDecimal worthSum = dict.getBigDecimal("worth_sum");
                // 遍历 path
                for (String path : paths) {
                    if (Convert.toLong(path) != 0) {
                        Dict orgDict = cache.get(Convert.toLong(path));
                        if (ObjectUtil.isNotNull(orgDict)) {
                            orgDict.set("asset_num", assetNum + orgDict.getInt("asset_num"));
                            orgDict.set("worth_sum", worthSum.add(orgDict.getBigDecimal("worth_sum")));
                            cache.put(Convert.toLong(path), orgDict);
                        }
                    }
                }
            }
        });

        // 初始化资产数量统计和资产价值统计
        BigDecimal assetCountNum = new BigDecimal("0");
        BigDecimal assetCountWorth = new BigDecimal("0.00");

        // 初始化资产所属分类统计数组
        for (HashMap<Object, Object> v : orgAssetGroupReport) {
            assetCountNum = assetCountNum.add(new BigDecimal(String.valueOf(v.get("asset_num"))));
            assetCountWorth = assetCountWorth.add(new BigDecimal(String.valueOf(v.get("worth_sum"))));
        }

        // 补全分类对应的资产数量和价值
        for (AssetOrgReportDto val : assetOrgReportList) {
            val.setAssetTotal(String.valueOf(cache.get(val.getId()).getInt("asset_num")))
                    .setWorthTotal(String.valueOf(cache.get(val.getId()).getBigDecimal("worth_sum")));

            // 统计百分比
            // 设置数量占比
            String assetNumPr = "0";
            if (!NumberUtil.equals(new BigDecimal(val.getAssetTotal()), BigDecimal.valueOf(0)) &&
                    !NumberUtil.equals(assetCountNum, BigDecimal.valueOf(0))) {
                assetNumPr = NumberUtil.roundStr(String.valueOf(
                        NumberUtil.div(new BigDecimal(val.getAssetTotal()), assetCountNum)), 4);
            }
            val.setAssetNumPr(assetNumPr);

            // 设置金额占比
            String worthPr = "0";
            if (!NumberUtil.equals(new BigDecimal(val.getWorthTotal()), BigDecimal.valueOf(0)) &&
                    !NumberUtil.equals(assetCountWorth, BigDecimal.valueOf(0))) {
                worthPr = NumberUtil.roundStr(String.valueOf(
                        NumberUtil.div(new BigDecimal(val.getWorthTotal()), assetCountWorth)), 4);
            }
            val.setWorthPr(worthPr);
        }

        // 返回最后数据
        ArrayList<AssetOrgReportDto> assetOrgReportReturnData = Lists.newArrayList();
        // 收集所有的pid
        List<Long> pidArr = assetOrgReportList.stream().map(AssetOrgReportDto::getPid).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(assetOrgReportList)) {
            for (AssetOrgReportDto val : assetOrgReportList) {
                // 如果组织下资产数量为0 不展示
                if ("0".equals(val.getAssetTotal())) {
                    continue;
                }

                if ((val.getPid().equals(pid) || val.getId().equals(pid))) {
                    //默认可以点击
                    val.setIsHasChild(true);
                    // 名称修改为直属组织
                    if (val.getId().equals(pid)) {
//                        val.setName("直属组织");
                        //不能点击
                        val.setIsHasChild(false);
                        // total 改成当前数量 不包括下级数据
                        val.setAssetTotal(val.getAssetNum());
                        val.setWorthTotal(val.getWorthSum());

                        // 设置数量占比
                        String assetNumPr = "0";
                        if (!NumberUtil.equals(new BigDecimal(val.getAssetTotal()), BigDecimal.valueOf(0)) &&
                                !NumberUtil.equals(assetCountNum, BigDecimal.valueOf(0))) {
                            assetNumPr = NumberUtil.roundStr(String.valueOf(
                                    NumberUtil.div(new BigDecimal(val.getAssetTotal()), assetCountNum)), 4);
                        }
                        val.setAssetNumPr(assetNumPr);

                        // 设置金额占比
                        String worthPr = "0";
                        if (!NumberUtil.equals(new BigDecimal(val.getWorthTotal()), BigDecimal.valueOf(0)) &&
                                !NumberUtil.equals(assetCountWorth, BigDecimal.valueOf(0))) {
                            worthPr = NumberUtil.roundStr(String.valueOf(
                                    NumberUtil.div(new BigDecimal(val.getWorthTotal()), assetCountWorth)), 4);
                        }
                        val.setWorthPr(worthPr);
                    }

                    // 该节点不作为父级 或者当前节点数量等于当前节点及子节点的总数量
                    if (!pidArr.contains(val.getId()) || val.getAssetNum().equals(val.getAssetTotal())) {
                        //不能点击
                        val.setIsHasChild(false);
                    }

                    assetOrgReportReturnData.add(val);
                }
            }
        }

        return assetOrgReportReturnData;
    }

    /**
     * @param dividend 被除数
     * @param divisor  除数
     * @param scale    精度  -- 默认2
     * @return 结果
     */
    private BigDecimal divide(Object dividend, Object divisor, Integer scale) {
        BigDecimal calc = new BigDecimal("0");
        try {
            BigDecimal dividendDecimal = new BigDecimal(Convert.toStr(dividend));
            BigDecimal divisorDecimal = new BigDecimal(Convert.toStr(divisor));
            if (0 == divisorDecimal.intValue()) {
                return calc;
            } else {
                final int toUserScale = scale == null ? 2 : scale.intValue();
                return dividendDecimal
                        .divide(divisorDecimal, toUserScale, RoundingMode.HALF_UP);
            }
        } catch (Exception e) {
            return calc;
        }

    }

}
