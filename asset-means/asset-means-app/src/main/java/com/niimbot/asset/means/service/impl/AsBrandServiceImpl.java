package com.niimbot.asset.means.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsBrandMapper;
import com.niimbot.asset.means.model.AsBrand;
import com.niimbot.asset.means.service.AsBrandService;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;

/**
 * <p>
 * 品牌管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service
public class AsBrandServiceImpl extends ServiceImpl<AsBrandMapper, AsBrand> implements AsBrandService {

    @Override
    public List<AsBrand> search(String name) {
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        if (ObjectUtil.isNull(cusUser)) {
            return this.getBaseMapper().search(name, 0L);
        } else {
            return this.getBaseMapper().search(name, cusUser.getCompanyId());
        }

    }

    @Override
    public int customCount(String brandName, Long companyId) {
        return this.getBaseMapper().customCount(brandName, companyId);
    }
}
