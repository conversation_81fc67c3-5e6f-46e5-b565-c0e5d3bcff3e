package com.niimbot.asset.means.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsAdminPrinterSizeMapper;
import com.niimbot.asset.means.model.AsAdminPrinterSize;
import com.niimbot.asset.means.service.AsAdminPrinterSizeService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@Service
public class AsAdminPrinterSizeServiceImpl extends ServiceImpl<AsAdminPrinterSizeMapper, AsAdminPrinterSize>
        implements AsAdminPrinterSizeService {

}
