package com.niimbot.asset.means.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.means.model.AsUserPrintLog;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.means.service.AsUserPrintTaskService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.*;
import com.niimbot.system.AntiFakeCheckDto;
import com.niimbot.system.ThirdPartyCheckDto;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 打印任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@RestController
@RequestMapping("/server/system/print/task")
@RequiredArgsConstructor
public class AsUserPrintTaskServiceController {

    private final AsUserPrintTaskService printTaskService;

    @PostMapping(value = "listTaskApp/{printType}/{taskStatus}")
    public List<AsUserPrintTask> listTaskApp(@PathVariable("printType") Short printType, @PathVariable("taskStatus") Short taskStatus, @RequestBody(required = false) List<LastPrintTaskQueryDto> queryList) {
        return printTaskService.listTaskApp(printType, taskStatus, queryList);
    }

    @GetMapping("/app/printTaskCount/{printType}")
    public Integer countAppTask(@PathVariable("printType") Short printType) {
        return printTaskService.listTaskAppCount(printType);
    }

    // @GetMapping("/app/group/status")
    // public IPage<JSONObject> appGroupStatus(UserPrintTaskGroupByStatusPageQuery query) {
    //     return printTaskService.appGroupStatus(query);
    // }

    @GetMapping("/app/listOfDataWithStatusPausedInTaskDetails")
    public List<JSONObject> listOfDataWithStatusPausedInTaskDetails(@RequestParam("taskId") Long taskId) {
        return printTaskService.listOfDataWithStatusPausedInTaskDetails(taskId);
    }

    @GetMapping("/details/page")
    public IPage<UserPrintTaskGroupStatusResult> detailsPage(UserPrintTaskGroupByStatusPageQuery query) {
        return printTaskService.taskDetailsPage(query);
    }

    @PostMapping(value = "listTaskPc")
    public List<AsUserPrintTask> listTaskPc(@RequestBody PrintTaskQueryDto dto) {
        return printTaskService.listTaskPc(dto);
    }

    @PostMapping(value = "pageTaskPc")
    public IPage<AsUserPrintTask> pageTaskPc(@RequestBody PrintTaskQueryDto dto) {
        return printTaskService.pageTaskPc(dto);
    }

    @PostMapping("/againTask")
    public PrintDataViewDto againTask(@RequestBody UserPrintTaskDto printTask) {
        return printTaskService.againTask(printTask);
    }

    @PostMapping("/continueTaskPrintOne")
    public PrintDataViewDto continueTaskPrintOne(@RequestBody UserPrintTaskConfigDto configDto) {
        return printTaskService.continueTaskPrintOne(configDto);
    }

    @PostMapping(value = "start")
    public PrintDataViewDto startTask(@RequestBody UserPrintTaskConfigDto configDto) {
        return printTaskService.startTask(configDto);
    }

    @GetMapping(value = "continue/check")
    public UserPrintCheckInfoDto continueCheck(@RequestParam Long taskId, @RequestParam String printerName) {
        return printTaskService.continueTaskCheck(taskId, printerName);
    }

    @PostMapping(value = "continue")
    public PrintDataViewDto continueTask(@RequestBody UserPrintTaskConfigDto configDto) {
        return printTaskService.continueTask(configDto);
    }

    @PostMapping(value = "pause")
    public Boolean pauseTask(@RequestBody List<AsUserPrintLog> logs) {
        return printTaskService.pauseTask(logs, false);
    }

    @GetMapping(value = "cancel")
    public Boolean cancelTask(@RequestParam Long taskId) {
        return printTaskService.cancelTask(taskId);
    }

    @PostMapping(value = "add")
    public PrintDataViewDto addTask(@RequestBody UserPrintTaskDto printTask) {
        PrintQueryDataDto queryDataDto = printTask.getQueryDto();
        if (DictConstant.PRINT_TYPE_ASSET == printTask.getPrintType()) {
            queryDataDto = printTask.getQueryDto();
        } else if (DictConstant.PRINT_TYPE_MATERIAL == printTask.getPrintType()) {
            queryDataDto = printTask.getMaterialQueryDto();
        } else if (DictConstant.PRINT_TYPE_AREA == printTask.getPrintType()) {
            queryDataDto = printTask.getAreaQueryDto();
        } else if (DictConstant.PRINT_TYPE_POINT == printTask.getPrintType()) {
            queryDataDto = printTask.getPointQueryDto();
        }
        return printTaskService.addTask(
                BeanUtil.copyProperties(printTask, AsUserPrintTask.class),
                // printTask.getAssets(),
                queryDataDto);
    }

    @PutMapping(value = "config")
    public Boolean configTask(@RequestBody AsUserPrintTask printTask) {
        return printTaskService.configTask(printTask);
    }

    @PostMapping(value = "checkAuthEquipment")
    public Boolean checkAuthEquipment(@RequestBody AntiFakeCheckDto antiFakeCheckDto) {
        return printTaskService.checkAuthEquipment(antiFakeCheckDto);
    }

    @PostMapping(value = "checkAuthEquipmentThirdParty")
    public Boolean checkAuthEquipmentThirdParty(@RequestBody ThirdPartyCheckDto thirdPartyCheckDto) {
        return printTaskService.checkAuthEquipmentThirdParty(thirdPartyCheckDto);
    }

    @GetMapping(value = "{taskId}")
    public UserPrintTaskInfoDto tasKInfo(@PathVariable("taskId") Long taskId) {
        return printTaskService.tasKInfo(taskId);
    }

    @PostMapping(value = "configAppContinueTask")
    public Boolean configAppContinueTask(@RequestBody UserPrintTaskConfigDto printTask) {
        return printTaskService.configAppContinueTask(printTask);
    }

    @GetMapping(value = "changeTaskStatus")
    public Boolean changeTaskStatus(Long taskId, Integer taskStatus) {
        AsUserPrintTask byId = printTaskService.getById(taskId);
        if (byId == null) {
            throw new BusinessException(SystemResultCode.PRINT_TASK_NOT_EXIST);
        }
        return printTaskService.update(Wrappers.<AsUserPrintTask>lambdaUpdate()
                .set(AsUserPrintTask::getTaskStatus, taskStatus)
                .eq(AsUserPrintTask::getId, taskId));
    }

    @GetMapping(value = "status/all/{printType}")
    public Boolean changeAllTaskStatus(@PathVariable("printType") Short printType, @RequestParam("type") Integer type) {
        return printTaskService.changeAllTaskStatus(printType, type);
    }

    @PutMapping(value = "status/all")
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeAllTaskStatus(@RequestParam("type") Integer type) {
        List<Short> list = Arrays.asList(DictConstant.PRINT_TYPE_ASSET, DictConstant.PRINT_TYPE_MATERIAL, DictConstant.PRINT_TYPE_AREA, DictConstant.PRINT_TYPE_POINT);
        list.forEach(printType -> printTaskService.changeAllTaskStatus(printType, type));
        return true;
    }

    @GetMapping(value = "cancelTaskWithoutCheck")
    public Boolean cancelTaskWithoutCheck(@RequestParam("taskId") Long taskId) {
        return printTaskService.update(Wrappers.<AsUserPrintTask>lambdaUpdate()
                .set(AsUserPrintTask::getIsClear, true)
                .eq(AsUserPrintTask::getId, taskId));
    }

    @PostMapping(value = "down")
    public List<AsUserPrintTask> down(@RequestBody PrintTaskQueryDto dto) {
        return printTaskService.down(dto);
    }

    @GetMapping("/printJson/{tagSizeId}")
    public JSONObject getPrintJsonByTagSizeId(@PathVariable("tagSizeId") Long tagSizeId) {
        return printTaskService.getPrintJsonByTagSizeId(tagSizeId);
    }

    @PostMapping("/changeAssetTagQrCodeValue/{tagId}")
    public Boolean changeAssetTagQrCodeValue(@PathVariable Long tagId) {
        return printTaskService.changeAssetTagQrCodeValue(tagId);
    }

    @PostMapping("/isSupportRfid/{type}")
    public Boolean isSupportRfid(@RequestBody AsUserPrintTask printTask, @PathVariable("type") String type) {
        return printTaskService.isSupportRfid(printTask, type);
    }

    @PostMapping(value = "/getPrintJson")
    public List<PrintDataViewDto.DataDto> getPrintJson(@RequestBody PrintPdfDto dto) {
        return printTaskService.getPrintJson(dto);
    }

    @PostMapping(value = "/getPrintData")
    public List<JSONObject> getPrintData(@RequestBody PrintPdfDto dto) {
        return printTaskService.getPrintData(dto);
    }

}
