package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.means.model.AsOrder;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.AsOrderDto;
import com.niimbot.means.AsOrderInfoDto;
import com.niimbot.means.AsOrderQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@EnableDataPerm(excludeMethodName = {"getOrderByAssetIdNoPerm", "tempGetById", "selectAssetSnapshot"})
public interface AsOrderMapper extends BaseMapper<AsOrder> {

    /**
     * 分页查询
     * [自动权限]
     * @param page
     * @param query
     * @param conditions
     * @return
     */
    IPage<AsOrder> page(IPage<AsOrder> page,
                        @Param("query") AsOrderQueryDto query,
                        @Param("conditions") String conditions);

    /**
     * 根据资产id获取当前审批中的单据id
     *
     */
    AsOrderInfoDto getOrderByAssetId(@Param("assetId") Long assetId);

    AsOrderInfoDto getOrderByAssetIdNoPerm(@Param("assetId") Long assetId,
                                           @Param("companyId") Long companyId);
    /**
     * 列表查询--导出使用
     * @param query
     * @return
     */
    List<AsOrderDto> listForExport(@Param("query") AsOrderQueryDto query,
                                    @Param("conditions") String conditions,
                                    @Param("orderBySql") String orderBySql);

}