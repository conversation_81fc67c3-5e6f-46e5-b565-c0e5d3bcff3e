package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.means.AsAssetLogDto;
import com.niimbot.means.AsAssetLogQueryDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 资产-履历表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-25
 */
@EnableDataPerm
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsAssetLogMapper.class)
public interface AsAssetLogMapper extends BaseMapper<AsAssetLog> {
    /**
     * 分页查询
     *
     * @param page      分页参数
     * @param dto       查询参数
     * @param companyId 租户id
     * @return 分页数据
     */
    IPage<AsAssetLogDto> customPage(@Param("page") Page<AsAssetLogDto> page, @Param("ew") AsAssetLogQueryDto dto);

    /**
     * 列表查询
     *
     * @param dto       查询参数
     * @param companyId 租户id
     * @return 列表数据
     */
    List<AsAssetLogDto> customPage(@Param("ew") AsAssetLogQueryDto dto);
}
