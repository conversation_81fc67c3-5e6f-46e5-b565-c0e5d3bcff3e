package com.niimbot.asset.means.domain.listener;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.utils.AssetUtil;
import com.niimbot.asset.means.event.AddMeansEvent;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.system.ots.SystemStoreRecordOts;
import com.niimbot.system.DataSnapshot;
import com.niimbot.system.DataSnapshotFactory;
import com.niimbot.system.StoreRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MeansEventListener implements ApplicationListener<AddMeansEvent> {

    private final AsFormService formService;

    private final AssetUtil assetUtil;

    private final SystemStoreRecordOts systemStoreRecordService;

    @Async(value = "assetTaskExecutor")
    @Override
    @SuppressWarnings("all")
    public void onApplicationEvent(AddMeansEvent event) {
        // 资产数据
        List<AsAsset> assets = (List<AsAsset>) event.getSource();
        // 入库方式
        Integer storeMode = event.getStoreMode();
        // 批次ID
        Long batchId = event.getBatchId();
        // 批次编号
        String batchNo = event.getBatchNo();
        // 企业ID
        Long companyId = assets.get(0).getCompanyId();
        Long userId = assets.get(0).getCreateBy();
        // 翻译
        List<JSONObject> jsons = assets.stream().peek(v -> {
            if (Objects.isNull(v.getStatus())) {
                v.setStatus(1);
            }
        }).map(AsAsset::translate).collect(Collectors.toList());
        List<AssetUtil.FieldTranslation> translations = formService.getTpl(AsFormService.BIZ_TYPE_ASSET, companyId)
                .getFormFields()
                .stream().map(f -> new AssetUtil.FieldTranslation()
                        .setFieldCode(f.getFieldCode())
                        .setFieldType(f.getFieldType())
                        .setTranslationCode(f.getTranslationCode()))
                .collect(Collectors.toList());
        assetUtil.translateAssetJsonBatch(jsons, translations);
        // 快照信息
        List<DataSnapshot> dataSnapshots = DataSnapshotFactory.create(1, jsons);
        // 资产入库记录
        StoreRecord means = new StoreRecord()
                .setCompanyId(companyId)
                .setStoreHandler(userId)
                .setStoreMode(storeMode)
                .setSourceType(1)
                .setStoreType(1)
                .setId(batchId)
                .setStoreNo(batchNo);
        means.setSnapshots(dataSnapshots);
        systemStoreRecordService.record(means);
    }
}
