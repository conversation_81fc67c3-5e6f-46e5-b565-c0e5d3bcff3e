package com.niimbot.asset.means.domain.abs.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.means.mapstruct.MeansSystemMapStruct;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.system.abs.ImportErrorAbs;
import com.niimbot.asset.system.dto.ImportErrorDeleteCmd;
import com.niimbot.asset.system.dto.ImportErrorListQry;
import com.niimbot.asset.system.dto.ImportErrorSaveCmd;
import com.niimbot.asset.system.dto.ImportErrorSaveOrUpdateCmd;
import com.niimbot.asset.system.dto.clientobject.ImportErrorCO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/client/abs/means/importErrorAbs/")
@RequiredArgsConstructor
public class ImportErrorAbsImpl implements ImportErrorAbs {

    private final MeansSystemMapStruct systemMapStruct;

    private final AsAssetImportErrorService assetImportErrorService;

    @Override
    public List<ImportErrorCO> listImportError(ImportErrorListQry qry) {
        List<AsAssetImportError> errors = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, qry.getTaskId())
                .eq(AsAssetImportError::getImportType, qry.getImportType()));
        return systemMapStruct.convertAssetImportErrorListModelToCo(errors);
    }

    @Override
    public void saveImportError(ImportErrorSaveCmd cmd) {
        AsAssetImportError assetImportError = systemMapStruct.convertImportErrorCoToModel(cmd.getImportErrorCO());
        assetImportErrorService.save(assetImportError);
    }

    @Override
    public void saveOrUpdateImportError(ImportErrorSaveOrUpdateCmd cmd) {
        AsAssetImportError importError = systemMapStruct.convertImportErrorCoToModel(cmd.getImportErrorCO());
        assetImportErrorService.saveOrUpdate(importError);
    }

    @Override
    public Boolean deleteImportError(ImportErrorDeleteCmd cmd) {
        if (cmd.getTaskId() != null) {
            return assetImportErrorService.remove(Wrappers.<AsAssetImportError>lambdaQuery()
                    .eq(AsAssetImportError::getTaskId, cmd.getTaskId()));
        }
        if (CollUtil.isNotEmpty(cmd.getIds())) {
            return assetImportErrorService.removeByIds(cmd.getIds());
        }
        if (CollUtil.isNotEmpty(cmd.getTaskIds())) {
            return assetImportErrorService.remove(Wrappers.<AsAssetImportError>lambdaQuery()
                    .in(AsAssetImportError::getTaskId, cmd.getTaskIds()));
        }
        return false;
    }

}
