package com.niimbot.asset.means.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.mapper.AsOrderDetailMapper;
import com.niimbot.asset.means.model.AsOrderDetail;
import com.niimbot.asset.means.service.AsOrderDetailService;
import com.niimbot.means.AsOrderAssetDto;

import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020-12-24
 */
@Service
public class AsOrderDetailServiceImpl extends ServiceImpl<AsOrderDetailMapper, AsOrderDetail>
        implements AsOrderDetailService {

    @Override
    public List<AsOrderAssetDto> getDetailsByOrderId(Long id) {
        List<AsOrderDetail> list = list(new QueryWrapper<AsOrderDetail>()
                .lambda().eq(AsOrderDetail::getOrderId, id));
        return list.stream().map(asOrderDetail -> {
            AsOrderAssetDto asOrderAssetDto = new AsOrderAssetDto().setId(asOrderDetail.getAssetId())
                    .setOrderId(asOrderDetail.getOrderId());
            asOrderAssetDto.setAssetSnapshotData(asOrderDetail.getAssetSnapshotData());
            return asOrderAssetDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AsOrderAssetDto> getDetailsByOrderId(Collection<Long> ids) {
        List<AsOrderDetail> list = list(new QueryWrapper<AsOrderDetail>()
                .lambda().in(AsOrderDetail::getOrderId, ids));
        return list.stream().map(asOrderDetail -> {
            AsOrderAssetDto asOrderAssetDto = new AsOrderAssetDto().setId(asOrderDetail.getAssetId())
                    .setOrderId(asOrderDetail.getOrderId());
            asOrderAssetDto.setAssetSnapshotData(asOrderDetail.getAssetSnapshotData());
            return asOrderAssetDto;
        }).collect(Collectors.toList());
    }

    @Override
    public JSONObject getAssetDetail(Long orderId, Long assetId) {
        AsOrderDetail one = getOne(Wrappers.<AsOrderDetail>lambdaQuery()
                .eq(AsOrderDetail::getOrderId, orderId).eq(AsOrderDetail::getAssetId, assetId));
        if (null == one) {
            return new JSONObject();
        }
        return one.getAssetSnapshotData();
    }
}
