package com.niimbot.asset.means.domain.abs.impl;

import com.niimbot.asset.means.mapstruct.AssetLogMapStruct;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.system.abs.AssetAbs;
import com.niimbot.asset.system.dto.AssetDeleteAmountGetQry;
import com.niimbot.asset.system.dto.AssetExcludeTestAmountGetQry;
import com.niimbot.asset.system.dto.AssetManageTransferCmd;
import com.niimbot.asset.system.dto.AssetTransferCmd;
import com.niimbot.asset.system.dto.AssetUpdateBatchCmd;
import com.niimbot.asset.system.dto.AssetUseTransferCmd;
import com.niimbot.asset.system.dto.TagAttrListQry;
import com.niimbot.asset.system.dto.clientobject.AssetLogCO;
import com.niimbot.asset.system.dto.clientobject.TagAttrListCO;
import com.niimbot.asset.system.model.AsOrg;
import com.niimbot.system.CusEmployeeTransferDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/13 15:19
 */
@RestController
@RequestMapping("/client/abs/means/assetAbs/")
@RequiredArgsConstructor
public class AssetAbsImpl implements AssetAbs {
    @Autowired
    private AssetService assetService;
    private final AssetLogMapStruct assetLogMapStruct;


    @Override
    public Boolean assetTransfer(AssetTransferCmd cmd) {
        return assetService.editAssetUseOrg(
                cmd.getEmployeeChangeId(),
                cmd.getEmployeeId(),
                cmd.getCompanyId(),
                cmd.getTransfers(),
                cmd.getOrgList());
    }

    @Override
    public List<AssetLogCO> assetManageTransfer(AssetManageTransferCmd cmd) {
        return assetService
                .assetManageTransfer(
                        cmd.getRemoveEmployeeId(),
                        cmd.getAssetUnderManagement(),
                        cmd.getEmployeeChangeId())
                .stream()
                .map(assetLogMapStruct::toDataObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<AssetLogCO> assetUseTransfer(AssetUseTransferCmd cmd) {
        return assetService
                .assetUseTransfer(
                        cmd.getRemoveEmployeeId(), cmd.getToIdle(), cmd.getUseAsset(), cmd.getEmployeeChangeId())
                .stream()
                .map(assetLogMapStruct::toDataObject)
                .collect(Collectors.toList());
    }

    @Override
    public Integer getAssetExcludeTestAmount(AssetExcludeTestAmountGetQry qry) {
        return assetService.countAssetExcludeTestCompanyByTime(
                qry.getStartTime(), qry.getEndTime());
    }

    @Override
    public Integer getAssetDeleteAmount(AssetDeleteAmountGetQry qry) {
        return assetService.countDeleteNum(qry.getStartTime(), qry.getEndTime());
    }

    @Override
    public TagAttrListCO getAttrList(TagAttrListQry qry) {
        return assetService.getAttrList(qry.getKw());
    }

    @Override
    public Boolean editAssetUseOrg(Long changeId, Long userId, List<CusEmployeeTransferDto> transfers, List<AsOrg> orgList) {
        return assetService.editAssetUseOrg(changeId, userId, transfers, orgList);
    }

    @Override
    public Boolean updateBatchAsset(AssetUpdateBatchCmd cmd) {
        if (CollUtil.isEmpty(cmd.getAssetCOS())) {
            return false;
        }
        List<AsAsset> assets = cmd.getAssetCOS().stream().map(v -> BeanUtil.copyProperties(v, AsAsset.class)).collect(Collectors.toList());
        return assetService.updateBatchById(assets);
    }
}
