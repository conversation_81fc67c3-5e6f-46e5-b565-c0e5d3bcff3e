package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.support.Edition;
import com.niimbot.asset.means.mapper.AsAssetStatusMapper;
import com.niimbot.asset.means.model.AsAssetStatus;
import com.niimbot.asset.means.service.AsAssetStatusService;
import com.niimbot.means.AssetStatusDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.convert.Convert;

/**
 * <AUTHOR>
 * @since 2020/12/29 12:24
 */
@Service
public class AsAssetStatusServiceImpl extends ServiceImpl<AsAssetStatusMapper, AsAssetStatus> implements AsAssetStatusService, ApplicationListener<ApplicationReadyEvent> {

    private final RedisService redisService;

    private final ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    public AsAssetStatusServiceImpl(RedisService redisService,
                                    ThreadPoolTaskExecutor taskExecutor) {
        this.redisService = redisService;
        this.taskExecutor = taskExecutor;
    }

    @Override
    public List<AssetStatusDto> allStatus() {
        return this.getBaseMapper().allStatus();
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        //预发环境定时任务不启动，因为预发环境和线上共用redis，不同时刷redis缓存，saas在预发环境刷redis缓存
        if (Edition.isSaas() && Edition.isProd()) {
            return ;
        }

        taskExecutor.execute(() -> {
            List<AssetStatusDto> assetStatuses = allStatus();
            Map<String, String> collect = assetStatuses.stream()
                    .collect(Collectors.toMap(it -> Convert.toStr(it.getId()), AssetStatusDto::getName, (k1, k2) -> k1));
            redisService.hSetAll(RedisConstant.assetStatusDictKey(), collect);
        });
    }

    @Override
    public List<Integer> getAssetStatusByOrderType(Integer orderType) {
        return this.getBaseMapper().getAssetStatusByOrderType(orderType);
    }

}
