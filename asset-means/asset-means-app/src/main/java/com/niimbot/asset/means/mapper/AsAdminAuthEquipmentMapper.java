package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.means.model.AsAdminAuthEquipment;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.AdminAuthEquipmentDetailListDto;
import com.niimbot.system.AdminAuthEquipmentDto;
import com.niimbot.system.AdminAuthEquipmentQueryDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/17 17:05
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsAdminAuthEquipmentMapper.class)
public interface AsAdminAuthEquipmentMapper extends BaseMapper<AsAdminAuthEquipment> {

    /**
     * 设备列表
     *
     * @return 结果
     */
    List<AdminAuthEquipmentDto> selectEquipmentList();

    /**
     * 设备详情
     *
     * @param id id
     * @return 结果
     */
    AdminAuthEquipmentDto getByEquipmentId(Long id);

    /**
     * 序列号详情列表
     *
     * @param dto 设备id
     * @return 结果
     */
    List<AdminAuthEquipmentDetailListDto> selectDetailSerialPage(@Param("ew") AdminAuthEquipmentQueryDto dto);

    /**
     * 公司详情列表
     *
     * @param dto 设备id
     * @return 结果
     */
    List<AdminAuthEquipmentDetailListDto> selectDetailCompanyPage(@Param("ew") AdminAuthEquipmentQueryDto dto);

    /**
     * 序列号详情分页
     *
     * @param page 分页参数
     * @param dto  参数
     * @return 结果
     */
    IPage<AdminAuthEquipmentDetailListDto> selectDetailSerialPage(@Param("page") Page<AdminAuthEquipmentQueryDto> page,
                                                                  @Param("ew") AdminAuthEquipmentQueryDto dto);

    /**
     * 公司详情分页
     *
     * @param page        分页参数
     * @param dto 设备id
     * @return 结果
     */
    IPage<AdminAuthEquipmentDetailListDto> selectDetailCompanyPage(@Param("page") Page<AdminAuthEquipmentQueryDto> page,
                                                                   @Param("ew") AdminAuthEquipmentQueryDto dto);
}
