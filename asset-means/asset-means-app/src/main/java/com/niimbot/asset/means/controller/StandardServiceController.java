package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.service.StandardService;
import com.niimbot.asset.system.dto.CompleteNewbieTaskCmd;
import com.niimbot.asset.system.ots.SystemCompanyOts;
import com.niimbot.easydesign.form.dto.FormTplAddCmd;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.StandardListDto;
import com.niimbot.means.StandardQueryDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 标准品管理
 *
 * <AUTHOR>
 * @since 2021/1/8 14:16
 */
@RestController
@RequestMapping("server/means/standard")
public class StandardServiceController {

    private final StandardService standardService;

    private final SystemCompanyOts systemCompanyService;

    @Autowired
    public StandardServiceController(StandardService standardService, SystemCompanyOts systemCompanyService) {
        this.standardService = standardService;
        this.systemCompanyService = systemCompanyService;
    }

    @GetMapping(value = "/page")
    public IPage<StandardListDto> page(StandardQueryDto queryDto) {
        return standardService.cusPage(queryDto);
    }

    @GetMapping(value = "/list")
    public List<StandardListDto> list(@RequestParam("name") String name) {
        if (StrUtil.isBlank(name)) {
            return ListUtil.empty();
        } else {
            return standardService.cusList(name);
        }
    }

    @GetMapping(value = "/form")
    public FormVO form(@RequestParam(value = "standardId", required = false) Long standardId,
                       @RequestParam(value = "throwEx", required = false) Boolean throwEx) {
        return standardService.form(standardId, throwEx);
    }

    @PostMapping
    public Boolean add(@RequestBody FormTplAddCmd standard) {
        Boolean result = standardService.add(standard);
        if (result) {
            CompleteNewbieTaskCmd cmd = new CompleteNewbieTaskCmd();
            cmd.setCompanyId(LoginUserThreadLocal.getCompanyId());
            cmd.setKey("as_standard");
            systemCompanyService.completeNewbieTask(cmd);
        }
        return result;
    }

    @PutMapping
    public Boolean edit(@RequestBody FormTplAddCmd standard) {
        return standardService.edit(standard);
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Long id) {
        return standardService.delete(id);
    }

    @PostMapping("/copySys/{id}")
    public StandardListDto copySys(@PathVariable("id") Long id) {
        return standardService.copySys(id);
    }

    @GetMapping("/extension")
    public List<FormFieldCO> getStandardExtField(@RequestParam("mappingFormId") Long mappingFormId,
                                                 @RequestParam("standardId") Long standardId) {
        return standardService.getStandardExtField(mappingFormId, standardId);
    }
}
