package com.niimbot.asset.means.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.dynamicform.utils.FormFieldConvert;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.MeansResultCode;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsProductMapper;
import com.niimbot.asset.means.model.AsAssetImportError;
import com.niimbot.asset.means.model.AsProduct;
import com.niimbot.asset.means.service.AsAssetImportErrorService;
import com.niimbot.asset.means.service.AsProductService;
import com.niimbot.asset.system.dto.clientobject.GenSerialNoCmd;
import com.niimbot.asset.system.ots.SerialNumberOts;
import com.niimbot.dynamicform.ValidatorModel;
import com.niimbot.easydesign.core.dto.page.PageResult;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.clientobject.FormPropsCO;
import com.niimbot.easydesign.form.dto.formprops.DataSummary;
import com.niimbot.easydesign.form.dto.sdk.FormSdkVO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.luckysheet.LuckySheetModel;
import com.niimbot.means.AsProductDto;
import com.niimbot.means.AsProductInfoDto;
import com.niimbot.means.AsProductQueryDto;
import com.niimbot.means.AssetImportDto;
import com.niimbot.means.ImportImages;
import com.niimbot.means.ProductListByIdQueryDto;
import com.niimbot.system.HeadImportErrorDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 产品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-12
 */
@Slf4j
@Service
public class AsProductServiceImpl extends ServiceImpl<AsProductMapper, AsProduct> implements AsProductService {

    private static final String ERROR_COLOR = "#000000";

    @Autowired
    private RedisService redisService;

    @Autowired
    private AsFormService formService;

    @Autowired
    private AsAssetImportErrorService assetImportErrorService;

    @Autowired
    private SerialNumberOts serialNumberService;

    @Override
    public IPage<LinkedHashMap<String, Object>> customPage(AsProductQueryDto queryDto) {
        List<Long> companyIds = ListUtil.of(0L, LoginUserThreadLocal.getCompanyId());
        if (queryDto.getType() != null) {
            if (queryDto.getType() == 1) {
                companyIds = ListUtil.of(LoginUserThreadLocal.getCompanyId());
            } else if (queryDto.getType() == 2) {
                companyIds = ListUtil.of(0L);
            }
        }
        Long empId = null;
        if (BooleanUtil.isTrue(queryDto.getHabits())) {
            empId = LoginUserThreadLocal.getCurrentUserId();
        }

        // 名称反查
        IPage<AsProductInfoDto> infoDtoIPage = new Page<>();
        if (StrUtil.isNotBlank(queryDto.getStandardName())) {
            PageResult<FormSdkVO> standardPage = formService.standardPage(companyIds.stream().map(Convert::toStr).collect(Collectors.toList()),
                    queryDto.getStandardName(), 1, Integer.MAX_VALUE);
            List<FormSdkVO> standardList = standardPage.getList();
            List<Long> standardIds = standardList.stream().map(FormSdkVO::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(standardIds)) {
                infoDtoIPage = this.getBaseMapper().customPage(queryDto.buildIPage(), queryDto, standardIds, companyIds, empId);
            }
        } else {
            infoDtoIPage = this.getBaseMapper().customPage(queryDto.buildIPage(), queryDto, null, companyIds, empId);
        }

        List<AsProductInfoDto> records = infoDtoIPage.getRecords();
        // 批量查询表单结构
        Set<Long> formIds = records.stream().map(AsProductInfoDto::getStandardId).collect(Collectors.toSet());
        List<FormVO> formVOList = formService.getByFormIds(new ArrayList<>(formIds));
        Map<Long, FormVO> formMap = new HashMap<>();
        for (FormVO f : formVOList) {
            formMap.put(f.getFormId(), f);
        }
        List<LinkedHashMap<String, Object>> result = new ArrayList<>();
        // 转换查询结果
        for (AsProductInfoDto record : records) {
            LinkedHashMap<String, Object> map = new LinkedHashMap<>(8);
            map.put("id", record.getId());
            map.put("standardId", record.getStandardId());
            JSONObject jsonObject = record.translateInfo();
            jsonObject.put("images", record.getImages());
            if (!formMap.containsKey(record.getStandardId())) {
                result.add(map);
                continue;
            }
            FormVO formVO = formMap.get(record.getStandardId());
            List<FormFieldCO> formFields = formVO.getFormFields();
            Map<String, FormFieldCO> formFieldMap = formFields.stream()
                    .collect(Collectors.toMap(FormFieldCO::getFieldCode, k -> k));
            JSONObject formProps = formVO.getFormProps();
            if (formProps != null && formProps.containsKey(FormPropsCO.DATA_SUMMARY)) {
                List<DataSummary> dataSummaries = formProps.getJSONArray(FormPropsCO.DATA_SUMMARY).toJavaList(DataSummary.class);
                for (DataSummary dataSummary : dataSummaries) {
                    String fieldCode = dataSummary.getFieldCode();
                    if ("images".equals(fieldCode)) {
                        Object images = jsonObject.get(fieldCode);
                        map.put(fieldCode, ObjectUtil.isNull(images) ? ListUtil.empty() : images);
                    } else {
                        if (dataSummary.getFieldType().equals(FormFieldCO.DATETIME)) {
                            FormFieldCO formFieldCO = formFieldMap.get(dataSummary.getFieldCode());
                            if (ObjectUtil.isNotNull(formFieldCO) && formFieldCO.getFieldProps().containsKey("dateFormatType")) {
                                Long date = jsonObject.getLong(fieldCode);
                                if (date != null && date > 0L) {
                                    String format = formFieldCO.getFieldProps().getString("dateFormatType");
                                    LocalDateTime dateTime = LocalDateTime.ofEpochSecond(date / 1000, 0, ZoneOffset.of("+8"));
                                    String formatDate = dateTime.format(DateTimeFormatter.ofPattern(format));
                                    map.put(dataSummary.getFieldName(), formatDate);
                                }
                            }
                        }
                        if (!map.containsKey(dataSummary.getFieldName())) {
                            map.put(dataSummary.getFieldName(), jsonObject.get(fieldCode));
                        }
                    }
                }
            }
            result.add(map);
        }
        IPage<LinkedHashMap<String, Object>> resultPage = new Page<>(
                infoDtoIPage.getCurrent(), infoDtoIPage.getSize(), infoDtoIPage.getTotal()
        );
        resultPage.setRecords(result);
        return resultPage;
    }

    @Override
    public List<AsProductInfoDto> customList(String name) {
        List<Long> companyIds = ListUtil.of(0L, LoginUserThreadLocal.getCompanyId());
        return this.getBaseMapper().customList(name, companyIds);
    }

    @Override
    public AsProductInfoDto customInfo(Long id) {
        AsProductInfoDto asProductInfoDto = this.getBaseMapper().customInfo(id, LoginUserThreadLocal.getCompanyId());
        if (asProductInfoDto != null) {
            FormVO formVO = formService.getByFormId(asProductInfoDto.getStandardId(), ListUtil.of(1L, LoginUserThreadLocal.getCompanyId()), false);
            if (ObjectUtil.isNotNull(formVO)) {
                asProductInfoDto.setStandardName(formVO.getFormName());
            } else {
                asProductInfoDto.setStandardName(StrUtil.EMPTY);
            }
        }
        return asProductInfoDto;
    }

    @Override
    public List<AsProductInfoDto> customInfos(ProductListByIdQueryDto queryDto) {
        List<AsProductInfoDto> infos = this.getBaseMapper().customInfos(queryDto.getProductIds(), LoginUserThreadLocal.getCompanyId());
        if (CollUtil.isNotEmpty(infos)) {
            for (AsProductInfoDto asProductInfoDto : infos) {
                FormVO formVO = formService.getByFormId(asProductInfoDto.getStandardId(), ListUtil.of(1L, LoginUserThreadLocal.getCompanyId()), false);
                if (ObjectUtil.isNotNull(formVO)) {
                    asProductInfoDto.setStandardName(formVO.getFormName());
                } else {
                    asProductInfoDto.setStandardName(StrUtil.EMPTY);
                }
            }
        }
        return infos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AsProductInfoDto add(AsProductDto productDto) {
        boolean isSys = false;
        // 查询用户表单
        FormVO formVO = formService.getByFormId(productDto.getStandardId(), LoginUserThreadLocal.getCompanyId(), false);
        // 用户表单不存在，查询系统表单
        if (ObjectUtil.isNull(formVO)) {
            formVO = formService.getByFormId(productDto.getStandardId(), 1L, false);
            isSys = true;
        }
        if (ObjectUtil.isNull(formVO)) {
            throw new BusinessException(MeansResultCode.STANDARD_NOT_EXISTS);
        }
        // 先校验动态表单，本质是复制，不用多查询formVO
        JSONObject data = productDto.getData();
        // 添加业务流水号
        setSerialNo(formVO, data);

        formService.formValidator(ListUtil.of(data),
                isSys ? 1L : LoginUserThreadLocal.getCompanyId(),
                formVO, AsFormService.BIZ_TYPE_STANDARD);

        // 如果是系统表单，复制一份
        if (isSys) {
            Long formId = formVO.getFormId();
            Long newFormId = formService.copyForm(formId, 1L, LoginUserThreadLocal.getCompanyId());
            productDto.setStandardId(newFormId);
        }

        FormFieldConvert.clearInvalidField(productDto.getData(), formVO.getFormFields());

        // 构造保存对象
        AsProduct product = data.toJavaObject(AsProduct.class);

        product.setId(IdUtils.getId())
                .setStandardId(productDto.getStandardId())
                .setExtension(productDto.getData());
        this.save(product);
        return new AsProductInfoDto().setId(product.getId());
    }

    /**
     * @param formVO
     * @param data
     * @return 是否包含系统自动生成
     */
    private boolean setSerialNo(FormVO formVO, JSONObject data) {
        AtomicReference<Boolean> bool = new AtomicReference<>(false);
        formVO.getFormFields().stream()
                .filter(field ->
                        field.getFieldType().equals(FormFieldCO.YZC_SERIALNO))
                .findFirst()
                .ifPresent(field -> {
                    if ("系统自动生成".equals(data.getString(field.getFieldCode()))) {
                        bool.set(true);
                        GenSerialNoCmd genSerialNoCmd = field.getFieldProps()
                                .getJSONObject("defaultRule").toJavaObject(GenSerialNoCmd.class);
                        genSerialNoCmd.setFieldCode(field.getFieldCode());
                        genSerialNoCmd.setData(ListUtil.of(data));
                        genSerialNoCmd.setType(field.getFieldType());
                        serialNumberService.fillSerial(genSerialNoCmd);
                    }
                });
        return bool.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(AsProductDto productDto) {
        AsProduct one = this.getById(productDto.getId());
        // 不允许修改运营后台数据
        if (ObjectUtil.isNull(one)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        boolean isSys = false;
        // 查询用户表单
        FormVO formVO = formService.getByFormId(productDto.getStandardId(), LoginUserThreadLocal.getCompanyId(), false);
        // 用户表单不存在，查询系统表单
        if (ObjectUtil.isNull(formVO)) {
            formVO = formService.getByFormId(productDto.getStandardId(), 1L, false);
            isSys = true;
        }
        if (ObjectUtil.isNull(formVO)) {
            throw new BusinessException(MeansResultCode.STANDARD_NOT_EXISTS);
        }
        // 先校验动态表单，本质是复制，不用多查询formVO
        // 添加业务流水号
        setSerialNo(formVO, productDto.getData());
        ValidatorModel validatorModel = new ValidatorModel(productDto.getId(), productDto.getData());
        formService.fieldValidatorWithId(validatorModel, formVO.getFormFields(), AsFormService.BIZ_TYPE_STANDARD);

        // 如果是系统表单，复制一份
        if (isSys) {
            Long formId = formVO.getFormId();
            Long newFormId = formService.copyForm(formId, 1L, LoginUserThreadLocal.getCompanyId());
            productDto.setStandardId(newFormId);
        }

        FormFieldConvert.clearInvalidField(productDto.getData(), formVO.getFormFields());

        // 构造保存对象
        AsProduct product = productDto.getData().toJavaObject(AsProduct.class);
        product.setId(productDto.getId())
                .setStandardId(productDto.getStandardId())
                .setExtension(productDto.getData());
        this.updateById(product);
        return true;
    }

    @Override
    public List<AsProduct> listByProductIds(Long companyId, List<Long> productIds) {
        return this.getBaseMapper().listNoDataPerm(
                Wrappers.<AsProduct>lambdaQuery()
                        .eq(AsProduct::getIsDelete, false)
                        .in(AsProduct::getId, productIds)
                        .in(AsProduct::getCompanyId, Arrays.asList(companyId, 0)));
    }

    @Override
    public Boolean delete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException(SystemResultCode.PARAM_IS_BLANK);
        }
        return this.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSheetData(AssetImportDto importDto) {
        List<AssetImportDto.FieldData> fieldDataList = importDto.getFieldDataList();
        JSONObject productData = new JSONObject();
        List<LuckySheetModel> luckySheetModels = new ArrayList<>();
        Map<String, AssetImportDto.FieldData> fieldDataMap = new HashMap<>();
        for (AssetImportDto.FieldData fieldData : fieldDataList) {
            if (fieldData.getTarget() != null) {
                productData.put(fieldData.getFieldCode(), fieldData.getTarget());
            }
            fieldDataMap.put(fieldData.getFieldCode(), fieldData);
        }
        boolean isSys = false;
        // 查询用户表单
        FormVO formVO = formService.getByFormId(importDto.getStandardId(), LoginUserThreadLocal.getCompanyId(), false);
        // 用户表单不存在，查询系统表单
        if (ObjectUtil.isNull(formVO)) {
            formVO = formService.getByFormId(importDto.getStandardId(), 1L, false);
            isSys = true;
        }
        if (ObjectUtil.isNull(formVO)) {
            for (AssetImportDto.FieldData fieldData : fieldDataList) {
                fieldData.setErrMsg(ListUtil.of("标准品不存在"));
            }
        } else {
            formService.importProductValidator(productData, formVO, isSys ? 1L : LoginUserThreadLocal.getCompanyId(), fieldDataMap);
        }

        int errorNum = 0;
        for (int i = 0; i < fieldDataList.size(); i++) {
            AssetImportDto.FieldData fieldData = fieldDataList.get(i);
            // LuckySheet表格数据
            LuckySheetModel luckySheetModel = new LuckySheetModel();
            luckySheetModel.setR(importDto.getRowNum()).setC(i);

            LuckySheetModel.Value modelV = luckySheetModel.getV();
            modelV.setV(Convert.toStr(fieldData.getSource()));
            if (fieldData.getErrMsg().size() > 0) {
                List<String> collect = fieldData.getErrMsg().stream().distinct().collect(toList());
                String errMsg = String.join("，", collect);
                // 错误单元格标记红色
                modelV.setFc(ERROR_COLOR);
                LuckySheetModel.Comment comment = new LuckySheetModel.Comment(errMsg);
                modelV.setPs(comment);
                errorNum++;
            }
            luckySheetModels.add(luckySheetModel);
        }

        importDto.setErrorNum(errorNum);

        if (importDto.getErrorNum() == 0) {
            boolean hasAutoGen = setSerialNo(formVO, productData);
            if (hasAutoGen
                    && formService.uniqueSerialNo(ListUtil.of(productData), formVO, AsFormService.BIZ_TYPE_STANDARD)) {
                FormFieldCO fieldCO = formVO.getFormFields().stream()
                        .filter(field ->
                                field.getFieldType().equals(FormFieldCO.YZC_SERIALNO))
                        .findFirst().orElse(null);
                if (fieldCO != null) {
                    boolean loop = true;
                    int maxTime = 50;
                    while (loop && maxTime > 0) {
                        productData.put(fieldCO.getFieldCode(), "系统自动生成");
                        setSerialNo(formVO, productData);
                        if (!formService.uniqueSerialNo(ListUtil.of(productData), formVO, AsFormService.BIZ_TYPE_ASSET)) {
                            loop = false;
                        } else {
                            maxTime--;
                        }
                    }
                    if (BooleanUtil.isTrue(loop)) {
                        Map<Integer, LuckySheetModel> luckySheetMap = luckySheetModels.stream()
                                .collect(Collectors.toMap(LuckySheetModel::getC, k -> k));
                        for (int i = 0; i < fieldDataList.size(); i++) {
                            AssetImportDto.FieldData fieldData = fieldDataList.get(i);
                            if (fieldCO.getFieldCode().equals(fieldData.getFieldCode())) {
                                LuckySheetModel model = luckySheetMap.get(i);
                                LuckySheetModel.Value modelV = model.getV();
                                // 错误单元格标记红色
                                modelV.setFc(ERROR_COLOR);
                                LuckySheetModel.Comment comment = new LuckySheetModel.Comment("编码自动生成异常，请重试");
                                modelV.setPs(comment);
                                errorNum++;
                                break;
                            }
                        }
                        importDto.setErrorNum(errorNum);
                    }
                }
            }
        }

        // 导入成功
        if (importDto.getErrorNum() == 0) {
            AsProduct product = productData.toJavaObject(AsProduct.class);
            // 如果是系统表单，复制一份
            if (isSys) {
                Long formId = formVO.getFormId();
                Long newFormId = formService.copyForm(formId, 1L, LoginUserThreadLocal.getCompanyId());
                product.setStandardId(newFormId);
            } else {
                product.setStandardId(importDto.getStandardId());
            }
            product.setExtension(productData);
            this.save(product);
            redisService.hIncr(RedisConstant.companyImportKey("product", LoginUserThreadLocal.getCompanyId()), "success", 1L);
            redisService.hIncr(RedisConstant.companyImportKey("product", LoginUserThreadLocal.getCompanyId()), "totalSuccess", 1L);
            return true;
        } else { // 导入失败
            AsAssetImportError importError = copyToAsAssetImportError(importDto, luckySheetModels);
            assetImportErrorService.saveOrUpdate(importError);
            redisService.hIncr(RedisConstant.companyImportKey("product", LoginUserThreadLocal.getCompanyId()), "error", 1L);
            return false;
        }
    }

    @Override
    public void saveSheetHead(HeadImportErrorDto importErrorDto) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importErrorDto.getTaskId());
        importError.setErrorNum(0);
        importError.setImportType(DictConstant.IMPORT_TYPE_PRODUCT);
        importError.setExcelJson(importErrorDto.getHeadModelList());
        this.assetImportErrorService.save(importError);
    }

    @Override
    public List<List<LuckySheetModel>> importError(Long taskId) {
        List<AsAssetImportError> list = this.assetImportErrorService.list(new QueryWrapper<AsAssetImportError>()
                .lambda()
                .eq(AsAssetImportError::getTaskId, taskId)
                .eq(AsAssetImportError::getImportType, DictConstant.IMPORT_TYPE_PRODUCT));
        return list.stream().map(AsAssetImportError::getExcelJson)
                .collect(Collectors.toList());
    }

    private AsAssetImportError copyToAsAssetImportError(AssetImportDto importDto, List<LuckySheetModel> rows) {
        AsAssetImportError importError = new AsAssetImportError();
        importError.setTaskId(importDto.getTaskId());
        importError.setErrorNum(importDto.getErrorNum());
        importError.setImportType(DictConstant.IMPORT_TYPE_PRODUCT);
        importError.setExcelJson(rows);
        if (ObjectUtil.isNotNull(importDto.getId())) {
            importError.setId(importDto.getId());
        }
        return importError;
    }

    @Override
    public List<ImportImages> importImagesCheck(List<ImportImages> codes, Integer action) {
        Set<String> distinct = codes.stream().map(ImportImages::getCode).collect(Collectors.toSet());
        // 不带权限检索出基本信息
        List<AsProduct> noPrem = this.list(
                Wrappers.lambdaQuery(AsProduct.class)
                        .select(AsProduct::getId, AsProduct::getStandardId, AsProduct::getImages, AsProduct::getCode)
                        .in(AsProduct::getCode, distinct)
        );
        Map<String, Integer> photoMap = noPrem.stream().collect(Collectors.toMap(AsProduct::getCode, v -> action == 2 ? 0 : (CollUtil.isEmpty(v.getImages()) ? 0 : v.getImages().size())));
        Map<String, Integer> codeMax = noPrem.stream().collect(Collectors.toMap(AsProduct::getCode, asProduct -> formService.getByFormId(asProduct.getStandardId(), ListUtil.of(LoginUserThreadLocal.getCompanyId()), true).getFormFields().stream()
                .filter(v -> AssetConstant.ED_IMAGES.equals(v.getFieldType()) && "images".equals(v.getFieldCode()))
                .findFirst()
                .orElseThrow(() -> new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "表单字段" + "images" + "不存在"))
                .getFieldProps()
                .getObject("max", Integer.class)));
        // 不存在的产品
        Collection<String> noExist = CollUtil.disjunction(distinct, photoMap.keySet());
        List<ImportImages> result = codes.stream().filter(v -> noExist.contains(v.getCode())).peek(v -> v.setError("当前资产图片没有对应产品信息，请核实产品编码信息是否正确")).collect(toList());
        if (CollUtil.isEmpty(noPrem)) {
            return result;
        }
        codes.removeIf(v -> noExist.contains(v.getCode()));
        // 图片张数超过10张
        result.addAll(
                codes.stream().filter(v -> {
                    Integer count = photoMap.get(v.getCode());
                    Integer max = codeMax.get(v.getCode());
                    // 新增
                    if (count >= max) {
                        return true;
                    }
                    // 更新
                    photoMap.put(v.getCode(), count + 1);
                    return false;
                }).peek(v -> v.setError("当前产品图片超出" + codeMax.get(v.getCode()) + "张，不可上传")).collect(toList())
        );
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportImages> importImages(List<ImportImages> images, Integer action) {
        // 再次检查
        List<ImportImages> result = importImagesCheck(images, action);
        if (CollUtil.isNotEmpty(result)) {
            List<String> ids = result.stream().map(ImportImages::getUid).collect(toList());
            images = images.stream().filter(v -> ids.contains(v.getUid())).collect(toList());
        }
        if (CollUtil.isEmpty(images)) {
            return result;
        }
        Map<String, List<String>> codeImages = images.stream().collect(Collectors.groupingBy(ImportImages::getCode, Collectors.mapping(ImportImages::getUrl, toList())));
        List<AsProduct> assets = this.list(Wrappers.lambdaQuery(AsProduct.class).in(AsProduct::getCode, codeImages.keySet()));
        Map<String, AsProduct> codeMap = assets.stream().collect(Collectors.toMap(AsProduct::getCode, v -> v));
        codeImages.forEach((k, v) -> {
            AsProduct product = codeMap.get(k);
            List<String> photo = product.getImages();
            if (CollUtil.isEmpty(photo)) {
                photo = new ArrayList<>();
            }
            if (action == 1) {
                photo.addAll(v);
            }
            if (action == 2) {
                photo.clear();
                photo.addAll(v);
            }
            product.setImages(photo);
        });
        this.updateBatchById(codeMap.values());
        return result;
    }
}
