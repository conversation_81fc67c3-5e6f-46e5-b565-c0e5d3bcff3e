package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.means.model.AsStoreOrder;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.means.AsOrderQueryDto;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 资产入库表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@EnableDataPerm(excludeMethodName = {"tempGetById"})
public interface AsStoreOrderMapper extends BaseMapper<AsStoreOrder> {
    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @return
     */
    IPage<AsStoreOrder> page(IPage<AsStoreOrder> page,
                              @Param("query") AsOrderQueryDto query,
                              @Param("conditions") String conditions);
    /**
     * 列表查询
     *
     * @param query
     * @param conditions
     * @param orderBySql
     * @return
     */
    List<AsStoreOrder> list(@Param("query") AsOrderQueryDto query,
                            @Param("conditions") String conditions,
                            @Param("orderBySql") String orderBySql);

    @Select("select * from as_store_order where id = #{id}")
    AsStoreOrder tempGetById(Long id);
}
