package com.niimbot.asset.means.message;

import com.google.common.collect.Lists;

import com.niimbot.asset.framework.constant.MessageConstant;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.message.dto.clientobject.Body;
import com.niimbot.asset.message.dto.clientobject.MessageRuleCO;
import com.niimbot.asset.message.dto.clientobject.Params;
import com.niimbot.asset.message.handler.PeriodCompanyMessageHandler;
import com.niimbot.means.AssetForMessage;

import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.URLUtil;

/**
 * 资产业务消息
 *
 * <AUTHOR>
 */
public abstract class MeansMessageHandler extends PeriodCompanyMessageHandler {

    @Resource
    private AssetService assetService;

    @Override
    protected boolean include() {
        return true;
    }

    @Override
    protected void bodies(MessageRuleCO rule, Params params, List<Body> bodies) {
        // 规则对应业务数据
        List<AssetForMessage> data = new ArrayList<>();
        if (MessageConstant.Code.ZCLYDQTX.getCode().equals(rule.getCode()) || MessageConstant.Code.ZCJYDQTX.getCode().equals(rule.getCode())) {
            data.addAll(assetService.listAssetOrderForMessage(rule.getCompanyId(), rule.getCode(), rule.getReminderTime()));
        }
        if (MessageConstant.Code.ZCNXDQTX.getCode().equals(rule.getCode())) {
            data.addAll(assetService.listAssetUseTimeLimitMessage(rule.getCompanyId(), rule.getReminderTime()));
        }
        if (CollUtil.isEmpty(data)) {
            return;
        }
        // 按照day分组
        Map<Integer, List<AssetForMessage>> groupByDay = data.stream().collect(Collectors.groupingBy(AssetForMessage::getDay));
        groupByDay.forEach((day, assetForMessages) -> {
            Set<Long> userIds = new HashSet<>(200);
            if (rule.includeReceiverType(MessageConstant.ReceiverType.ASSET_USER)) {
                userIds.addAll(assetForMessages.stream().map(AssetForMessage::getUsePerson).collect(Collectors.toSet()));
            }
            if (rule.includeReceiverType(MessageConstant.ReceiverType.ASSET_OWNER)) {
                userIds.addAll(assetForMessages.stream().map(AssetForMessage::getManagerOwner).collect(Collectors.toSet()));
            }
            if (rule.includeCuzReceiverType()) {
                userIds.addAll(rule.resolveCuzReceiver());
            }
            if (CollUtil.isEmpty(userIds)) {
                return;
            }
            Set<Long> assetIds = assetForMessages.stream().map(AssetForMessage::getAssetId).collect(Collectors.toSet());
            Map<String, String> map = new HashMap<>(3);
            map.put(MessageConstant.Template.AMOUNT, String.valueOf(assetIds.size()));
            map.put(MessageConstant.Template.DAY, String.valueOf(Math.abs(day)));
            String[] arrayParams = new String[]{String.valueOf(assetIds.size()), String.valueOf(Math.abs(day))};
            Map<String, Object> commonExtMap = new HashMap<>(1);
            commonExtMap.put(MessageConstant.ExtConfig.ASSET_IDS, assetIds);
            Body body = Body.builder()
                    .userIds(userIds)
                    .mapParam(map)
                    .appMapParam(Convert.toMap(String.class, String.class, map))
                    .arrayParam(arrayParams)
                    .commonExtMap(commonExtMap)
                    .urlParam(Lists.newArrayList(rule.getCode()))
                    .build();
            // 资产借用到期跳转的地址不一样
            if (MessageConstant.Code.ZCJYDQTX.getCode().equals(rule.getCode())) {
                body.setUrlParam(null);
                // estimateBackDate=2023-09-05,2023-09-05
                String begin = LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER);
                String end = LocalDate.now().plusDays(day).format(DatePattern.NORM_DATE_FORMATTER);
                String url = pcDomain + "/#/asset-report?bizType=16&overdueStatus=" + URLUtil.encode("未逾期", StandardCharsets.UTF_8) + "&estimateBackDate=" + begin + "," + end;
                body.getMapParam().put(MessageConstant.Template.URL, url);
                body.getCommonExtMap().put("overdueStatus", "未逾期");
                body.getCommonExtMap().put("estimateBackDate", begin + "," + end);
            }
            bodies.add(body);
        });
    }

    @Component
    public static class ZcJy extends MeansMessageHandler {
        @Override
        public String code() {
            return MessageConstant.Code.ZCJYDQTX.getCode();
        }
    }

    @Component
    public static class ZcLy extends MeansMessageHandler {
        @Override
        public String code() {
            return MessageConstant.Code.ZCLYDQTX.getCode();
        }
    }

    @Component
    public static class ZcNx extends MeansMessageHandler {
        @Override
        public String code() {
            return MessageConstant.Code.ZCNXDQTX.getCode();
        }
    }

}
