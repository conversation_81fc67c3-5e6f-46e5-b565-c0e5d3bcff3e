package com.niimbot.asset.means.controller;

import com.niimbot.asset.means.model.AsAssetOperation;
import com.niimbot.asset.means.service.AsAssetOperationService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/12 16:19
 */
@RestController
@RequestMapping("server/means/assetOperation")
public class AsAssetOperationServiceController {

    private final AsAssetOperationService assetOperationService;

    @Autowired
    public AsAssetOperationServiceController(AsAssetOperationService assetOperationService) {
        this.assetOperationService = assetOperationService;
    }

    /**
     * 查询资产操作集合列表
     *
     * @return 组织列表
     */
    @GetMapping(value = "/list")
    public List<AsAssetOperation> list() {
        return assetOperationService.list();
    }
}
