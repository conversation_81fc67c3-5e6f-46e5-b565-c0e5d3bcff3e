package com.niimbot.asset.means.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.niimbot.activiti.WorkflowCallbackDto;
import com.niimbot.asset.activiti.model.ActWorkflowBusiness;
import com.niimbot.asset.activiti.service.ActWorkflowBusinessService;
import com.niimbot.asset.activiti.service.ActWorkflowCallbackErrorService;
import com.niimbot.asset.dynamicform.service.AsFormService;
import com.niimbot.asset.framework.constant.AssetConstant;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.constant.OrderFormTypeEnum;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.model.LoginUserDto;
import com.niimbot.asset.framework.query.OrderToStringConverter;
import com.niimbot.asset.framework.query.QueryConditionResolver;
import com.niimbot.asset.framework.query.QueryFieldConstant;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.utils.WorkflowUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsStoreOrderMapper;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsStoreOrder;
import com.niimbot.asset.means.model.AsStoreOrderAssetDetail;
import com.niimbot.asset.means.model.AsStoreOrderSummaryDetail;
import com.niimbot.asset.means.service.AsStoreOrderAssetDetailService;
import com.niimbot.asset.means.service.AsStoreOrderService;
import com.niimbot.asset.means.service.AsStoreOrderSummaryDetailService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.asset.message.dto.cmd.MsgSendCmd;
import com.niimbot.asset.message.inner.service.MessageService;
import com.niimbot.asset.system.abs.PurchaseOrderAbs;
import com.niimbot.asset.system.service.AsQueryConditionConfigService;
import com.niimbot.easydesign.form.dto.clientobject.FormFieldCO;
import com.niimbot.easydesign.form.dto.viewobject.FormVO;
import com.niimbot.means.*;
import com.niimbot.page.SortQuery;
import com.niimbot.purchase.PurchaseOrderDto;
import com.niimbot.system.QueryConditionConfigDto;
import com.niimbot.system.QueryConditionSortDto;
import com.niimbot.system.QueryHeadConfigDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产入库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Service
@RequiredArgsConstructor
public class AsStoreOrderServiceImpl extends ServiceImpl<AsStoreOrderMapper, AsStoreOrder> implements AsStoreOrderService {
    private final AsStoreOrderSummaryDetailService storeOrderSummaryDetailService;
    private final MySqlOrderQueryConditionResolver conditionResolver;
    private final AsFormService formService;
    private final AsStoreOrderAssetDetailService storeOrderAssetDetailService;
    private final ActWorkflowBusinessService workflowBusinessService;
    private final AssetService assetService;
    @Resource
    private PurchaseOrderAbs purchaseOrderAbs;
    @Resource
    private ActWorkflowCallbackErrorService callbackErrorService;
    @Resource
    private AsQueryConditionConfigService queryConditionConfigService;
    @Resource
    private MessageService messageService;

    @Override
    public Boolean create(AsStoreOrderDto dto, Boolean enableWorkflow) {
        // 设置id与编号
        dto.setId(IdUtils.getId());
        dto.setOrderNo(StringUtils.getOrderNo("ARK"));
        AsStoreOrder storeOrder = BeanUtil.copyProperties(dto, AsStoreOrder.class);
        if (enableWorkflow) {
            storeOrder.setApproveStatus(DictConstant.WAIT_APPROVE);
        }
        List<AsStoreOrderSummaryDetail> summaryDetails = dto.getSummaryDetail().stream()
                .map(detailDto -> BeanUtil.copyProperties(detailDto, AsStoreOrderSummaryDetail.class)
                        .setStoreOrderId(dto.getId()))
                .collect(Collectors.toList());
        return this.save(storeOrder) && storeOrderSummaryDetailService.saveBatch(summaryDetails);
    }

    @Override
    public AsStoreOrderDto getDetailById(Long id) {
        AsStoreOrder storeOrder = getById(id);
        if (storeOrder == null) {
            return null;
        }

        String purchaseOrderNo = storeOrder.getOrderData().getString("purchaseOrderNo");
        if (RK_TYPE_LINK_PURCHASE.equals(storeOrder.getOrderData().getString(RK_TYPE))
                && StrUtil.isNotEmpty(purchaseOrderNo)) {
            Long orderId = purchaseOrderAbs.getIdByOrderNo(purchaseOrderNo);
            storeOrder.getOrderData().put("purchaseOrderId", orderId);
        }

        AsStoreOrderDto storeOrderDto = BeanUtil.copyProperties(storeOrder, AsStoreOrderDto.class);
        storeOrderDto.setSummaryDetail(
                storeOrderSummaryDetailService.list(
                                Wrappers.<AsStoreOrderSummaryDetail>lambdaQuery()
                                        .eq(AsStoreOrderSummaryDetail::getStoreOrderId, id))
                        .stream().map(detail -> BeanUtil.copyProperties(detail, AsStoreOrderSummaryDetailDto.class))
                        .collect(Collectors.toList()));

        return storeOrderDto;
    }

    @Override
    public IPage<AsStoreOrder> page(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsStoreOrder> page = buildOrderSort(tableAlias, query);
        return this.getBaseMapper().page(page, query, conditions);
    }

    @Override
    public List<AsStoreOrder> list(AsOrderQueryDto query) {
        String tableAlias = "a";
        String conditions = conditionResolver.resolveQueryCondition(tableAlias, query.getConditions());
        Page<AsStoreOrder> page = buildOrderSort(tableAlias, query);
        String orderByStr = OrderToStringConverter.orderByToString(page.getOrders());
        return this.getBaseMapper().list(query, conditions, orderByStr);
    }

    @Override
    public List<AsStoreOrderSummaryDetail> listSummaryDetailsByOrderId(Collection<Long> ids) {
        return storeOrderSummaryDetailService.list(
                Wrappers.<AsStoreOrderSummaryDetail>lambdaQuery()
                        .in(AsStoreOrderSummaryDetail::getStoreOrderId, ids));
    }

    @Override
    public IPage<AsStoreOrderSummaryDetail> pageSummaryDetail(StoreDetailPageQueryDto dto) {
        return storeOrderSummaryDetailService.page(dto.buildIPage(),
                Wrappers.<AsStoreOrderSummaryDetail>lambdaQuery()
                        .eq(AsStoreOrderSummaryDetail::getStoreOrderId, dto.getOrderId())
                        .orderByAsc(AsStoreOrderSummaryDetail::getId));
    }

    @Override
    public IPage<AsStoreOrderAssetDetail> pageAssetDetail(StoreDetailPageQueryDto dto) {
        return storeOrderAssetDetailService.page(dto.buildIPage(),
                Wrappers.<AsStoreOrderAssetDetail>lambdaQuery()
                        .eq(AsStoreOrderAssetDetail::getStoreOrderId, dto.getOrderId())
                        .orderByAsc(AsStoreOrderAssetDetail::getId));
    }

    @Override
    public QueryConditionSortDto sortField() {
        QueryConditionSortDto querySort = new QueryConditionSortDto();

        QueryConditionConfigDto queryConditionConfig = queryConditionConfigService.getByType(QueryFieldConstant.ASSET_ORDER_TYPE_HEAD.get(OrderFormTypeEnum.STORE.getCode()));
        QueryHeadConfigDto queryHeadConfig = queryConditionConfig.getConditions().toJavaObject(QueryHeadConfigDto.class);
        querySort.setSidx(queryHeadConfig.getSort());
        querySort.setOrder(queryHeadConfig.getSortType());

        QueryFieldConstant.Field approveStatusField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
        if (ObjectUtil.isNotNull(approveStatusField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(approveStatusField.getName(), approveStatusField.getCode(), approveStatusField.getType()));
        }

//        QueryFieldConstant.Field summaryField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_APPROVE_STATUS);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(QueryFieldConstant.FIELD_SUMMARY_NAME.get(orderType), summaryField.getCode(), summaryField.getType()));

        FormVO formVO = formService.getTplByType(OrderFormTypeEnum.STORE.getBizType());
        List<FormFieldCO> formFields = formVO.getFormFields();
        formFields.stream()
                .filter(f -> QueryFieldConstant.BIZ_FIELD_CAN_ORDER_TYPE.contains(f.getFieldType()))
                .forEach(f -> querySort.getSortList().add(new QueryConditionSortDto.Field(f.getFieldName(), f.getFieldCode(), f.getFieldType())));

//        QueryFieldConstant.Field createByField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_BY);
//        querySort.getSortList().add(
//                new QueryConditionSortDto.Field(createByField.getName(), createByField.getCode(), createByField.getType()));

        QueryFieldConstant.Field createTimeField = QueryFieldConstant.ORDER_COMMON_EXT_FIELD.get(QueryFieldConstant.FIELD_CREATE_TIME);
        if (ObjectUtil.isNotNull(createTimeField)) {
            querySort.getSortList().add(
                    new QueryConditionSortDto.Field(createTimeField.getName(), createTimeField.getCode(), createTimeField.getType()));
        }
        for (QueryConditionSortDto.Field f : querySort.getSortList()) {
            if (f.getValue().equals(queryHeadConfig.getSort())) {
                querySort.setLabel(f.getLabel());
                break;
            }
        }
        return querySort;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processCallback(WorkflowCallbackDto callbackDto) {
        AsStoreOrder storeOrder = this.getById(Long.parseLong(callbackDto.getBusinessId()));
        if (storeOrder == null) {
            return false;
        }
        callbackErrorService.remove(callbackDto);
        if (WorkflowUtil.finishWorkflowCallbackAllowed().contains(storeOrder.getApproveStatus())) {
            return true;
        }
        short status = Short.parseShort(callbackDto.getApproveStatus());
        if (!WorkflowUtil.finishWorkflowCallbackAllowed().contains(status)) {
            return false;
        }
        // threadLocal写入公司Id
        LoginUserDto userDto = new LoginUserDto();
        userDto.setCusUser(new CusUserDto()
                .setId(storeOrder.getCreateBy())
                .setIsAdmin(true)
                .setCompanyId(storeOrder.getCompanyId()));
        LoginUserThreadLocal.set(userDto);
        try {
            Optional<ActWorkflowBusiness> workflowBusiness = Optional.ofNullable(workflowBusinessService.getById(callbackDto.getProcessInstanceId()));
            workflowBusiness.ifPresent(v -> v.setApproveStatus(status));
            storeOrder.setApproveStatus(status);
            workflowBusiness.ifPresent(workflowBusinessService::updateById);
            this.updateById(storeOrder);

            if (DictConstant.APPROVED == status) {
                String rkType = storeOrder.getOrderData().getString(RK_TYPE);
                List<AsStoreOrderSummaryDetail> summaryDetailList = storeOrderSummaryDetailService.list(Wrappers.lambdaQuery(AsStoreOrderSummaryDetail.class)
                        .eq(AsStoreOrderSummaryDetail::getStoreOrderId, storeOrder.getId()));
                List<AsAsset> handledAssets = new ArrayList<>();
                List<AsStoreOrderAssetDetail> assets = new ArrayList<>();
                AtomicInteger idx = new AtomicInteger(0);
                summaryDetailList.forEach(summaryDetail -> {
                    for (int i = 0; i < summaryDetail.getQuantity(); i++) {
                        AsStoreOrderAssetDetail assetDetail = new AsStoreOrderAssetDetail();
                        assetDetail.setId(Convert.toLong(idx.incrementAndGet()));
                        assetDetail.setStoreOrderId(storeOrder.getId());
                        assetDetail.setAssetSnapshotData(BeanUtil.copyProperties(summaryDetail.getAssetSnapshotData(), JSONObject.class));
                        assetDetail.setStandardId(summaryDetail.getStandardId());
                        assetDetail.getAssetSnapshotData().put("assetCode", "系统自动生成");
                        if (RK_TYPE_LINK_PURCHASE.equals(rkType)) {
//                                assetDetail.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_ASSET_ORIGIN, DictConstant.ASSET_ORIGIN_BUY_TEXT);
                            assetDetail.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_ORIGIN_VOUCHER, storeOrder.getOrderNo());
                            assetDetail.getAssetSnapshotData().put(AssetConstant.PURCHASE_ASSET_FIELD_BUY_TIME, storeOrder.getOrderData().getOrDefault(PurchaseOrderDto.PURCHASE_DATE, ""));
                        }
                        assets.add(assetDetail);
                        AsAsset asset = new AsAsset();
                        asset.setId(assetDetail.getId());
                        asset.setStandardId(summaryDetail.getStandardId());
                        JSONObject jsonObject = BeanUtil.copyProperties(assetDetail.getAssetSnapshotData(), JSONObject.class);
                        asset.setAssetData(jsonObject);
                        handledAssets.add(asset);
                    }
                });


                List<AsAsset> assetListWithSid = new ArrayList<>();
                List<AsAsset> assetListWithoutSid = new ArrayList<>();
                for (AsAsset handledAsset : handledAssets) {
                    if (ObjectUtil.isNotNull(handledAsset.getStandardId())) {
                        assetListWithSid.add(handledAsset);
                    } else {
                        assetListWithoutSid.add(handledAsset);
                    }
                }
                Map<Long, List<AsAsset>> assetWithSidMap = assetListWithSid.stream().collect(
                        Collectors.groupingBy(AsAsset::getStandardId));
                assetWithSidMap.forEach((standardId, ast) ->
                        assetService.preAddHandleForStore(ast, standardId, false));
                assetService.preAddHandleForStore(assetListWithoutSid, null, false);
                List<AsAsset> newAssets = new ArrayList<>();
                if (CollUtil.isNotEmpty(assetListWithSid)) {
                    newAssets.addAll(assetListWithSid);
                }
                if (CollUtil.isNotEmpty(assetListWithoutSid)) {
                    newAssets.addAll(assetListWithoutSid);
                }
                // 回写资产编码，并且清除ID
                Map<Long, String> assetCodeMap = new HashMap<>();
                for (AsAsset f : newAssets) {
                    assetCodeMap.put(f.getId(), f.getAssetData().getString("assetCode"));
                    f.setId(null);
                }
                assets.forEach(f -> {
                    JSONObject assetData = f.getAssetSnapshotData();
                    assetData.put("assetCode", assetCodeMap.get(f.getId()));
                    f.setId(null);
                });
                storeOrderAssetDetailService.saveBatch(assets);
                // 资产入库
                AssetAddLogDto addLogDto = new AssetAddLogDto();
                addLogDto.setActionContent(storeOrder.getOrderData().getString(AsStoreOrderDto.RK_TYPE))
//                    2023年7月13日18:43:40 这里actionType改为记录入库类型，非单据类型
//                    2024年1月16日 10点13分 改回单据类型
                        .setActionType(AssetConstant.ORDER_TYPE_STORE)
//                        .setActionType(AssetConstant.OPT_ADD)
                        .setOrderId(storeOrder.getId())
                        .setOrderNo(storeOrder.getOrderNo());
                assetService.add(newAssets, addLogDto, storeOrder.getOrderNo());
                // 入库单审批
                messageService.sendInnerMessage(MsgSendCmd.cgwc(storeOrder.getCompanyId(), storeOrder.getId(), String.valueOf(OrderFormTypeEnum.STORE.getCode())));
            }

        } finally {
            LoginUserThreadLocal.remove();
        }
        return true;
    }

    private Page<AsStoreOrder> buildOrderSort(String tableAlias, SortQuery queryDto) {
        if (StrUtil.isEmpty(tableAlias)) {
            tableAlias = "as_store_order";
        }
        Page<AsStoreOrder> page = queryDto.buildIPageOrigin();
        List<OrderItem> orders = page.getOrders();
        // 是否有排序字段
        QueryConditionSortDto querySort = sortField();
        Map<String, String> codeAndType = querySort.getSortList().stream().collect(
                Collectors.toMap(QueryConditionSortDto.Field::getValue, QueryConditionSortDto.Field::getType));
        if (CollUtil.isEmpty(orders) && !QueryFieldConstant.FIELD_CREATE_TIME.equals(querySort.getSidx())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(querySort.getSidx());
            orderItem.setAsc("asc".equals(querySort.getOrder()));
            orders.add(orderItem);
        }
        List<OrderItem> removeOrderItems = new ArrayList<>();
        for (OrderItem order : orders) {
            String column = order.getColumn();
            // 判断是json里面数据，还是外面的数据
            if (QueryFieldConstant.ORDER_COMMON_EXT_FIELD.containsKey(column)) {
                order.setColumn(StrUtil.format(QueryConditionResolver.COLUMN_SQL_TPL, ImmutableMap.of("tName", tableAlias, "tColumn", StrUtil.toUnderlineCase(column))));
            } else if (codeAndType.containsKey(column)) {
                String type = codeAndType.get(column);
                String sql = QueryFieldConstant.BIZ_FIELD_TYPE_ORDER.get(type);
                if (StrUtil.isNotEmpty(sql)) {
                    order.setColumn(StrUtil.format(sql, tableAlias, String.format("%s.order_data", tableAlias), column));
                } else {
                    if (type.equals(AssetConstant.ED_NUMBER_INPUT)) {
                        order.setColumn(String.format(MySqlOrderQueryConditionResolver.NUM_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else if (type.equals(AssetConstant.ED_DATETIME)) {
                        order.setColumn(String.format(MySqlOrderQueryConditionResolver.DATE_JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    } else {
                        order.setColumn(String.format(MySqlOrderQueryConditionResolver.JSON_SQL_SEGMENT_TPL, tableAlias, column));
                    }
                }
            } else {
                removeOrderItems.add(order);
            }
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(tableAlias + ".id");
        orderItem.setAsc("asc".equals(querySort.getOrder()));
        orders.add(orderItem);
        if (CollUtil.isNotEmpty(removeOrderItems)) {
            for (OrderItem removeOrderItem : removeOrderItems) {
                orders.remove(removeOrderItem);
            }
        }
        return page;
    }

    @Override
    public AsStoreOrder tempGetById(Long orderId) {
        return this.getBaseMapper().tempGetById(orderId);
    }
}
