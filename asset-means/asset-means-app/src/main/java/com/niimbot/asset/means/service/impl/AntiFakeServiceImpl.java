package com.niimbot.asset.means.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.model.CusUserDto;
import com.niimbot.asset.framework.utils.NumberUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAdminAntiFakePrinterMapper;
import com.niimbot.asset.means.model.AsAdminAntiFakePrinter;
import com.niimbot.asset.means.model.AsAdminPrinter;
import com.niimbot.asset.means.service.AdminPrinterService;
import com.niimbot.asset.means.service.AntiFakeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AntiFakeCheckDto;
import com.niimbot.system.AntiFakeResDto;
import com.niimbot.system.AntiFakeUuidDto;
import com.niimbot.system.MachineDetailDto;
import com.niimbot.system.MachineDetailResDto;
import com.niimbot.system.PrintInfoDto;
import com.niimbot.system.PrintInfoModelDto;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2020/12/11
 */
@Service
@Slf4j
public class AntiFakeServiceImpl extends ServiceImpl<AsAdminAntiFakePrinterMapper, AsAdminAntiFakePrinter> implements AntiFakeService {

    /**
     * 云打印请求地址
     */
    @Value("${print.print-url:empty}")
    private String printUrl;

    /**
     * 云打印上传打印信息请求地址
     */
    @Value("${print.bpa-url:empty}")
    private String bpaUrl;

    @Resource
    private AdminPrinterService adminPrinterService;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 碳带防伪判断
     *
     * @param antiFakeCheckDto 碳带防伪判断请求对象
     * @return Boolean
     */
    @Override
    public AntiFakeResDto checkAntiFake(AntiFakeCheckDto antiFakeCheckDto) {
        AntiFakeResDto antiFakeResDto = new AntiFakeResDto();
        antiFakeResDto.setUuidData(new AntiFakeUuidDto());

        // 云打印是否有该UUID
        String url = printUrl + "/api/rfid/getRfid";
        LinkedMultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("serialNumber", antiFakeCheckDto.getSerialNumber());
        HttpEntity<LinkedMultiValueMap<String, Object>> httpEntity = new HttpEntity<>(paramMap);

        // 请求获取结果
        ResponseEntity<String> exchange;
        try {
            exchange = restTemplate.postForEntity(url, httpEntity, String.class);
        } catch (Exception e) {
            log.error("云打印接口调用异常", e);
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "碳带防伪检查异常，请稍后再试");
        }
        //得到返回的值
        String resultRemote = exchange.getBody();
        // json字符串转对象
        JSONObject jsonObject = JSON.parseObject(resultRemote);
        if (ObjectUtil.isEmpty(jsonObject) || ObjectUtil.isEmpty(jsonObject.get("code"))
                || !"1".equals(jsonObject.get("code") + "")) {
//            antiFakeResDto.setIsCanPrint(true);
            return antiFakeResDto;
        }
        JSONArray data = jsonObject.getJSONArray("data");
        antiFakeResDto.setRawData(data);

        // 当前设备是否进行碳带防伪判断
        if (!this.isAntiFake(antiFakeCheckDto)) {
            antiFakeResDto.setIsCanPrint(true);
            antiFakeResDto.setEnableAnti(antiFakeCheckDto.getEnableAnti()).setEnableTagAnti(antiFakeCheckDto.getEnableTagAnti());
            return antiFakeResDto;
        }
        antiFakeResDto.setEnableAnti(antiFakeCheckDto.getEnableAnti()).setEnableTagAnti(antiFakeCheckDto.getEnableTagAnti());
        antiFakeResDto.setIsAntiFake(true);

        if (ObjectUtil.isEmpty(data) || ObjectUtil.isNull(data.get(0))) {
            return antiFakeResDto;
        }

        // json对象转成AntiFakeUuidDto对象
        AntiFakeUuidDto antiFakeUuidDto = JSONUtil.toBean(JSON.toJSONString(data.get(0)), AntiFakeUuidDto.class);

        if (((ObjectUtil.isNotEmpty(antiFakeUuidDto.getUsedType()) && 2 == antiFakeUuidDto.getUsedType())
                || ObjectUtil.isEmpty(antiFakeUuidDto.getUsedType())) && antiFakeUuidDto.getRfidStatus()) {
            antiFakeResDto.setIsCanPrint(true);
        }

        antiFakeResDto.setUuidExist(true);

        // 补上materialUsed 和usedType （云打印可能不返回）
        AntiFakeUuidDto antiFakeUuid = new AntiFakeUuidDto();
        // 复制属性
        BeanUtil.copyProperties(antiFakeUuidDto, antiFakeUuid);

        antiFakeResDto.setUuidData(antiFakeUuid);
        return antiFakeResDto;
    }

    /**
     * 判断是否进行固件升级
     *
     * @param machineDetailDto 固件升级请求对象
     * @return Boolean
     */
    @Override
    public MachineDetailResDto machineCascadeDetail(MachineDetailDto machineDetailDto) {
        MachineDetailResDto machineDetailResDto = new MachineDetailResDto();

        // 云打印是否有该UUID
        String url = printUrl + "/api/firmware/machineCascadeDetail";
        LinkedMultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("machineType", machineDetailDto.getMachineType());
        paramMap.add("hardVersion", machineDetailDto.getHardVersion());
        paramMap.add("firmVersion", machineDetailDto.getFirmVersion());
        paramMap.add("machineName", machineDetailDto.getMachineName());
        HttpEntity<LinkedMultiValueMap<String, Object>> httpEntity = new HttpEntity<>(paramMap);

        RestTemplate restTemplate = new RestTemplate();
        // 请求获取结果
        ResponseEntity<String> exchange = restTemplate.postForEntity(url, httpEntity, String.class);
        String resultRemote = exchange.getBody();

        // json字符串转对象
        JSONObject jsonObject = JSON.parseObject(resultRemote);
        if (ObjectUtil.isEmpty(jsonObject) || ObjectUtil.isEmpty(jsonObject.get("code"))
                || !"1".equals(jsonObject.get("code") + "")) {
            return machineDetailResDto;
        }
        // json对象转成AntiFakeUuidDto对象
        machineDetailResDto = JSONUtil.toBean(JSON.toJSONString(jsonObject.get("data")), MachineDetailResDto.class);

        if (ObjectUtil.isNotEmpty(machineDetailResDto.getUrl())) {
            String printName = StrUtil.subPre(machineDetailDto.getMachineType(), 3);
            machineDetailResDto.setPrintName(printName);
        }

        return machineDetailResDto;
    }

    /**
     * 将打印信息上传云打印服务
     *
     * @param printInfoDto 打印信息对象
     * @return Boolean
     */
    @Override
    public Boolean uploadPrintInfo(PrintInfoDto printInfoDto) {
        List<PrintInfoModelDto> models = printInfoDto.getModels();
        if (ObjectUtil.isEmpty(models)) {
            log.error("打印信息为空");
            return false;
        }
        // 获取用户uid
        CusUserDto cusUser = LoginUserThreadLocal.getCusUser();
        if (ObjectUtil.isEmpty(cusUser)) {
            log.error("用户信息不存在");
            return false;
        }
        String uid = cusUser.getAccount();

        // 获取当前时间
        String now = DateUtil.now();
        // 当前时间戳
        long currentTime = System.currentTimeMillis();
        // 指定长度的数字串
        String randNum = NumberUtils.generateCode(2);

        // 打印信息
        for (long i = 0L; i < models.size(); i++) {
            String uniqueValue = (currentTime + i) + randNum;

            PrintInfoModelDto printInfoModelDto = models.get((int) i);
            printInfoModelDto.setUniqueValue(uniqueValue);
            printInfoModelDto.setUserId(cusUser.getId());
            printInfoModelDto.setPhone(uid);

            printInfoModelDto.setRfidSerialNumber(
                    ObjectUtil.isNotEmpty(printInfoModelDto.getRfidSerialNumber()) ? "-1|" + printInfoModelDto.getRfidSerialNumber() : "-1|0"
            );
            printInfoModelDto.setNumber(
                    ObjectUtil.isNotEmpty(printInfoModelDto.getNumber()) ? "-1|" + printInfoModelDto.getNumber() : ""
            );
            printInfoModelDto.setRfidPrintNumber(
                    ObjectUtil.isNotEmpty(printInfoModelDto.getRfidPrintNumber()) ? "-1|" + printInfoModelDto.getRfidPrintNumber() : ""
            );
            printInfoModelDto.setSuccessTimes(
                    ObjectUtil.isNotEmpty(printInfoModelDto.getSuccessTimes()) ? "-1|" + printInfoModelDto.getSuccessTimes() : ""
            );
            printInfoModelDto.setAllTimes(
                    ObjectUtil.isNotEmpty(printInfoModelDto.getAllTimes()) ? "-1|" + printInfoModelDto.getAllTimes() : ""
            );
            printInfoModelDto.setAddTime(
                    ObjectUtil.isNotEmpty(printInfoModelDto.getAddTime()) ? printInfoModelDto.getAddTime() : now
            );
        }

        Map<String, Object> printInfoMap = BeanUtil.beanToMap(printInfoDto);
        // 拼装头部信息
        StringBuilder headerData = new StringBuilder();
        printInfoMap.forEach((key, value) -> {
            if ("models".equals(key)) {
                return;
            }
            headerData.append(StringUtils.concat(true, key, "/", String.valueOf(value), " "));
        });

        String finalHeaderData = StringUtils.trim(headerData.toString());
        if (ObjectUtil.isEmpty(headerData)) {
            log.error("设备信息缺失");
            return false;
        }
        // json字符串转对象
        JSONObject jsonObject = JSON.parseObject(uploadCloudPrint(finalHeaderData, models));
        if (ObjectUtil.isEmpty(jsonObject) || ObjectUtil.isEmpty(jsonObject.get("code"))
                || !"1".equals(jsonObject.get("code") + "")) {
            log.error("请求云打印服务器失败");
            return false;
        }
        return true;
    }

    private String uploadCloudPrint(String userAgentHeader, List<PrintInfoModelDto> models) {
        // 上传打印信息
        String url = bpaUrl + "/printed/record/report";
        // 设置请求头
        HttpHeaders header = new HttpHeaders();
        // 需求需要传参为json格式
        header.set("niimbot-user-agent", userAgentHeader);
        header.setContentType(MediaType.APPLICATION_JSON);
        header.setCacheControl(CacheControl.noCache());
        header.setPragma("no-cache");

        LinkedMultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
        paramMap.addAll("models", models);
        HttpEntity<LinkedMultiValueMap<String, Object>> httpEntity = new HttpEntity<>(paramMap, header);

        try {
            ResponseEntity<String> exchange = restTemplate.postForEntity(url, httpEntity, String.class);
            return exchange.getBody();
        } catch (Exception e) {
            log.error("请求云打印上传日志异常", e);
            throw new BusinessException(SystemResultCode.PRINT_UPLOAD_CLOUD_LOG_ERROR);
        }
    }

    /**
     * [isAntiFake] 当前设备是否进行碳带防伪判断
     * - - - - - - - - - - - - - - - - - - - - - - - - - -
     *
     * @param antiFakeCheckDto 碳带防伪判断请求对象
     * @return
     */
    private boolean isAntiFake(AntiFakeCheckDto antiFakeCheckDto) {
        try {
            // 分割打印机名称
            String[] splitArr = StrUtil.split(antiFakeCheckDto.getPrinterName(), "-");
            if (ObjectUtil.isEmpty(splitArr) || splitArr.length < 2) {
                return false;
            }

            // 1、先查询是否是全型号开启
//            QueryWrapper<AsAdminAntiFakePrinter> queryWrapper = new QueryWrapper<>();
//            List<AsAdminAntiFakePrinter> antiFakePrinters = adminAntiFakePrinterMapper.selectList(
//                    queryWrapper.eq("printer_name", splitArr[0]).eq("is_delete", 0)
//            );
//            if (ObjectUtil.isEmpty(antiFakePrinters)) {
//                return true;
//            }

//            List<Boolean> isPassArr = antiFakePrinters.stream().map(printer -> printer.getIsPass() == 1)
//                    .distinct().collect(Collectors.toList());
//            if (1 == isPassArr.size() && isPassArr.get(0)) {
//                return false;
//            }
//
//            // 2、判断该型号下序列号是否存在
//            List<String> serialNumberArr = antiFakePrinters.stream().map(AsAdminAntiFakePrinter::getSerialNumber).distinct().collect(Collectors.toList());
//            if (serialNumberArr.contains(splitArr[1])) {
//                return false;
//            }

            // 新需求改换成 -- 碳带防伪只与设备绑定，与序列号无关
            AsAdminPrinter adminPrinter = adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                    .eq(AsAdminPrinter::getModel, splitArr[0]));
            antiFakeCheckDto.setEnableAnti(adminPrinter.getEnableAnti());
            antiFakeCheckDto.setEnableTagAnti(adminPrinter.getEnableTagAnti());
            return ObjectUtil.isNotEmpty(adminPrinter) && adminPrinter.getEnableAnti();
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }
}
