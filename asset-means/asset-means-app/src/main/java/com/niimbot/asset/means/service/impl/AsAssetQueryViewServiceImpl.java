package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.RedisConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.service.RedisService;
import com.niimbot.asset.framework.utils.BusinessExceptionUtil;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsAssetQueryViewMapper;
import com.niimbot.asset.means.model.AsAssetQueryView;
import com.niimbot.asset.means.service.AsAssetQueryViewService;
import com.niimbot.jf.core.exception.category.BusinessException;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 资产查询视图 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */
@Service
@RequiredArgsConstructor
public class AsAssetQueryViewServiceImpl extends ServiceImpl<AsAssetQueryViewMapper, AsAssetQueryView>
        implements AsAssetQueryViewService {
    private final RedisService redisService;

    @Override
    public boolean saveWithCache(AsAssetQueryView queryView) {
        long count = this.count(
                Wrappers.<AsAssetQueryView>lambdaQuery()
                        .eq(AsAssetQueryView::getCreateBy, LoginUserThreadLocal.getCurrentUserId()));
        if (count >= 30) {
            BusinessExceptionUtil.throwException("最多可新增至30个分组");
        }
        checkName(queryView);
        // 保存数据库
        this.save(queryView);
        // 删除缓存
        redisService.del(RedisConstant.assetQueryView(LoginUserThreadLocal.getCurrentUserId()));
        return true;
    }

    @Override
    public boolean updateWithCache(AsAssetQueryView queryView) {
        AsAssetQueryView view = this.getById(queryView.getId());
        if (!view.getIsCanEdit()) {
            BusinessExceptionUtil.throwException("该分组不能被编辑");
        }
        checkName(queryView);
        // 保存数据库
        this.updateById(queryView);
        // 删除缓存
        redisService.del(RedisConstant.assetQueryView(LoginUserThreadLocal.getCurrentUserId()));
        return true;
    }

    @Override
    public boolean isShowWithCache(Long id, Boolean isShow) {
        AsAssetQueryView view = this.getById(id);
        BusinessExceptionUtil.checkNotNull(view, "该分组信息不存在");
        AsAssetQueryView queryView = new AsAssetQueryView().setId(id).setIsShow(isShow);
        // 保存数据库
        this.updateById(queryView);
        // 删除缓存
        redisService.del(RedisConstant.assetQueryView(LoginUserThreadLocal.getCurrentUserId()));
        return true;
    }

    @Override
    public boolean deleteWithCache(Long id) {
        AsAssetQueryView view = this.getById(id);
        if (!view.getIsCanDel()) {
            BusinessExceptionUtil.throwException("该分组不能被删除");
        }
        this.removeById(id);
        // 删除缓存
        redisService.del(RedisConstant.assetQueryView(LoginUserThreadLocal.getCurrentUserId()));
        return true;
    }

    @Override
    public AsAssetQueryView getByIdWithCache(Long id) {
        AsAssetQueryView view = (AsAssetQueryView) redisService.hGet(
                RedisConstant.assetQueryView(LoginUserThreadLocal.getCurrentUserId()),
                String.valueOf(id));
        if (view == null) {
            view = this.getById(id);
        }
        return view;
    }

    @Override
    public boolean sortWithCache(List<Long> ids) {
        AtomicInteger index = new AtomicInteger(1);
        List<AsAssetQueryView> views = new ArrayList<>();
        for (Long id : ids) {
            views.add(new AsAssetQueryView().setId(id).setSortNum(index.getAndIncrement()));
        }
        if (!this.updateBatchById(views)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }
        // 删除缓存
        redisService.del(RedisConstant.assetQueryView(LoginUserThreadLocal.getCurrentUserId()));
        return true;
    }

    @Override
    public List<AsAssetQueryView> querySystemList() {
        return this.getBaseMapper().querySystemList();
    }

    @Override
    public List<AsAssetQueryView> queryConfigViews() {
        return list(Wrappers.<AsAssetQueryView>lambdaQuery()
                .eq(AsAssetQueryView::getCreateBy, LoginUserThreadLocal.getCurrentUserId())
                .orderByAsc(AsAssetQueryView::getSortNum));
    }

    @Override
    public List<AsAssetQueryView> queryTabViews() {
        // 查询缓存
        Map<Object, Object> objectMap = redisService.hGetAll(RedisConstant.assetQueryView(LoginUserThreadLocal.getCurrentUserId()));
        if (CollUtil.isNotEmpty(objectMap)) {
            return objectMap.values().stream()
                    .map(o -> BeanUtil.copyProperties(o, AsAssetQueryView.class))
                    .sorted(Comparator.comparing(AsAssetQueryView::getSortNum))
                    .collect(Collectors.toList());
        }

        // 查db
        List<AsAssetQueryView> queryViews = list(Wrappers.<AsAssetQueryView>lambdaQuery()
                .eq(AsAssetQueryView::getCreateBy, LoginUserThreadLocal.getCurrentUserId())
                .orderByAsc(AsAssetQueryView::getSortNum));

        // db存在, 写缓存
        if (CollUtil.isNotEmpty(queryViews)) {
            addCache(LoginUserThreadLocal.getCurrentUserId(), queryViews);
        }
        // db也没数据, 初始化数据, 并返回
        else {
            queryViews = this.initUserView(LoginUserThreadLocal.getCompanyId(), LoginUserThreadLocal.getCurrentUserId());
        }

        return queryViews;
    }

    @Override
    public List<AsAssetQueryView> initUserView(Long companyId, Long userId) {
        // 查询用户数据
        List<AsAssetQueryView> queryViews = this.list(
                Wrappers.<AsAssetQueryView>lambdaQuery()
                        .eq(AsAssetQueryView::getCreateBy, userId)
                        .orderByAsc(AsAssetQueryView::getSortNum));
        // 如果用户数据不存在, 则从系统数据初始化一份
        if (CollUtil.isEmpty(queryViews)) {
            queryViews = querySystemList();
            queryViews.forEach(view -> {
                view.setId(null);
                view.setCompanyId(companyId);
                view.setCreateBy(userId);
                view.setUpdateBy(userId);
            });
            // 保存db
            this.saveBatch(queryViews);
            // 过滤出需要展示的
            queryViews = queryViews.stream()
                    .filter(AsAssetQueryView::getIsShow)
                    .sorted(Comparator.comparing(AsAssetQueryView::getSortNum))
                    .collect(Collectors.toList());
        }
        // 写缓存
        addCache(userId, queryViews);
        return queryViews;
    }

    private void addCache(Long userId, List<AsAssetQueryView> queryViews) {
        // 添加缓存
        queryViews.forEach(view -> {
            redisService.hSet(RedisConstant.assetQueryView(userId), String.valueOf(view.getId()), view);
        });
        redisService.expire(RedisConstant.assetQueryView(userId), 7, TimeUnit.DAYS);
    }

    private void checkName(AsAssetQueryView queryView) {
        String name = queryView.getName();
        if (StrUtil.isNotBlank(name)) {
            LambdaQueryWrapper<AsAssetQueryView> wrapper = Wrappers.<AsAssetQueryView>lambdaQuery()
                    .eq(AsAssetQueryView::getCreateBy, LoginUserThreadLocal.getCurrentUserId())
                    .eq(AsAssetQueryView::getName, name)
                    .ne(ObjectUtil.isNotNull(queryView.getId()), AsAssetQueryView::getId, queryView.getId());
            List<AsAssetQueryView> list = this.list(wrapper);
            if (CollUtil.isNotEmpty(list)) {
                BusinessExceptionUtil.throwException("分组名称已存在，请重新输入");
            }
        }
    }
}
