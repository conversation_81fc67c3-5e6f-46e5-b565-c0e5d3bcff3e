package com.niimbot.asset.means.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.abs.MeansMaterialStockAbs;
import com.niimbot.asset.means.mapper.AsUserPrintLogMapper;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.asset.means.model.AsTagMaterial;
import com.niimbot.asset.means.model.AsUserPrintLog;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.means.service.AsPrintDataSnapshotService;
import com.niimbot.asset.means.service.AsTagMaterialService;
import com.niimbot.asset.means.service.AsUserPrintLogService;
import com.niimbot.asset.means.service.AsUserPrintTaskService;
import com.niimbot.asset.means.service.AssetService;
import com.niimbot.enums.PrintTaskStatusEnum;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.material.MaterialDto;
import com.niimbot.means.PrintAssetLogQueryDto;
import com.niimbot.means.PrintTaskSnapshot;
import com.niimbot.means.UserPrintLogViewDto;
import com.niimbot.system.PrintLogQuery;
import com.niimbot.system.statistics.StatisticsEquipmentDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 资产打印日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Service
public class AsUserPrintLogServiceImpl extends ServiceImpl<AsUserPrintLogMapper, AsUserPrintLog>
        implements AsUserPrintLogService {

    @Resource
    private AsUserPrintTaskService printTaskService;

    @Resource
    private AsPrintDataSnapshotService printDataSnapshotService;

    @Resource
    private AssetService assetService;

    @Resource
    private MeansMaterialStockAbs meansMaterialAbs;

    @Override
    public List<UserPrintLogViewDto> assetLogList(PrintAssetLogQueryDto dto) {
        return this.getBaseMapper().assetLog(dto, LoginUserThreadLocal.getCompanyId(),
                LoginUserThreadLocal.getCurrentUserId());
    }

    @Override
    public IPage<UserPrintLogViewDto> assetLogPage(PrintAssetLogQueryDto dto) {
        return this.getBaseMapper().assetLog(dto.buildIPage(), dto, LoginUserThreadLocal.getCompanyId(),
                LoginUserThreadLocal.getCurrentUserId());
    }

    @Override
    public IPage<AsUserPrintLog> fullPrintLogPage(PrintLogQuery query) {
        return this.getBaseMapper().fullPrintLogPage(query.buildIPage(), LoginUserThreadLocal.getCompanyId(), query);
    }

    /**
     * 记录日志方法、仅处理成功的日志、有异常日志会调用暂停接口
     *
     * @param logs logs
     * @return true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addPrintLog(@RequestBody List<AsUserPrintLog> logs) {
        Map<Long, List<AsUserPrintLog>> group = logs.stream()
                .filter(log -> ObjectUtil.isNotNull(log.getAssetId()) && ObjectUtil.isNotNull(log.getPrintTaskId()))
                .collect(Collectors.groupingBy(AsUserPrintLog::getPrintTaskId));
        // 任务ID分组
        group.forEach((taskId, val) -> {
            AsUserPrintTask byId = printTaskService.getById(taskId);
            if (byId == null) {
                throw new BusinessException(SystemResultCode.PRINT_TASK_NOT_EXIST);
            }
            // 打印异常
            boolean printException = val.stream().anyMatch(log -> log.getPrintStatus().intValue() == 2);
            if (printException) {
                printTaskService.pauseTask(val, true);
                return;
            }
            // 打印成功
            List<Long> successIds = printLogCount(taskId, 1);
            int currentPrintCount = new HashSet<>(successIds).size();
            // 全部的
            PrintTaskSnapshot snapshot = printDataSnapshotService.getTaskSnapshot(taskId);
            List<Long> all = snapshot.getDataIds();
            // 过滤未保存的
            List<AsUserPrintLog> collect = val.stream()
                    .filter(log -> ObjectUtil.isNotNull(log.getAssetId()) && !successIds.contains(log.getAssetId()) && all.contains(log.getAssetId()))
                    .collect(toList());
            collect.forEach(printLog -> printLog.fillProps(byId, snapshot.getSnapshots()));
            if (CollUtil.isNotEmpty(collect)) {
                this.saveBatch(collect);
                this.updateBizPrintTime(collect, byId.getPrintType());
                ConcurrentMap<Long, JSONObject> map = collect.stream().collect(Collectors.toConcurrentMap(AsUserPrintLog::getAssetId, AsUserPrintLog::getAssetData));
                // 更新已打印的记录
                List<AsPrintDataSnapshot> snapshots = printDataSnapshotService.list(
                        Wrappers.lambdaUpdate(AsPrintDataSnapshot.class)
                                .eq(AsPrintDataSnapshot::getTaskId, taskId)
                                .in(AsPrintDataSnapshot::getDataId, map.keySet())
                                .orderByAsc(AsPrintDataSnapshot::getSort)
                );
                snapshots.forEach(snapshot1 -> {
                    if (map.containsKey(snapshot1.getDataId())) {
                        snapshot1.setSnapshot(map.get(snapshot1.getDataId())).setIsPrint(true);
                    }
                });
                printDataSnapshotService.updateBatchById(snapshots);
                currentPrintCount = currentPrintCount + collect.stream().map(AsUserPrintLog::getAssetId).collect(Collectors.toSet()).size();
            }
            LambdaUpdateWrapper<AsUserPrintTask> eq = Wrappers.<AsUserPrintTask>lambdaUpdate()
                    .set(AsUserPrintTask::getPrintedAssetNum, currentPrintCount)
                    .eq(AsUserPrintTask::getId, taskId);
            if (currentPrintCount == byId.getAssetNum()) {
                eq.set(AsUserPrintTask::getTaskStatus, PrintTaskStatusEnum.DONE.getStatus());
            }
            printTaskService.update(eq);
        });
        return true;
    }

    /**
     * 用户设备打印信息次数统计
     *
     * @return List<StatisticsEquipmentDto>
     */
    @Override
    public List<StatisticsEquipmentDto> userByCount() {
        return this.getBaseMapper().userByCount();
    }

    @Override
    public List<Long> printLogCount(Long taskId, Integer printStatus) {
        LambdaQueryWrapper<AsUserPrintLog> eq = Wrappers.<AsUserPrintLog>lambdaQuery()
                .eq(AsUserPrintLog::getPrintTaskId, taskId);
        if (Objects.nonNull(printStatus)) {
            eq.eq(AsUserPrintLog::getPrintStatus, 1);
        }
        return list(eq).stream().map(AsUserPrintLog::getAssetId).distinct()
                .collect(toList());
    }

    @Resource
    private AsTagMaterialService tagMaterialService;

    @Override
    public String getPrintMaterialName(Long tagId, Long tagMaterialId) {
        String sizeName = this.getBaseMapper().selectPrintLogMaterialName(tagId);
        AsTagMaterial material = tagMaterialService.getById(tagMaterialId);
        if (Objects.nonNull(material) && StrUtil.isNotBlank(material.getName())) {
            return sizeName + " " + material.getName();
        }
        return sizeName;
    }

    @Override
    public Integer printErrorCount(Long companyId, Integer printType, LocalDateTime begin, LocalDateTime end) {
        return getBaseMapper().printAssetCount(companyId, printType, begin, end);
    }

    @Override
    public Integer printErrorCount(Long companyId, Integer printType, Integer printStatus, LocalDateTime begin, LocalDateTime end) {
        return getBaseMapper().printErrorCount(companyId, printType, printStatus, begin, end);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBizPrintTime(List<AsUserPrintLog> logs, short printType) {
        // 更新资产的最近打印时间
        if (printType == DictConstant.PRINT_TYPE_ASSET) {
            logs.parallelStream()
                    .forEach(v -> {
                        if (v.getPrintType() != 1) {
                            return;
                        }
                        assetService.update(
                                Wrappers.lambdaUpdate(AsAsset.class)
                                        .set(AsAsset::getLastPrintTime, v.getPrintTime())
                                        .eq(AsAsset::getId, v.getAssetId())
                        );
                    });
        }
        // 更新耗材打印时间
        if (printType == DictConstant.PRINT_TYPE_MATERIAL) {
            List<MaterialDto> dtos = logs.stream()
                    .filter(log -> log.getPrintType() == 2)
                    .map(log -> new MaterialDto().setId(log.getAssetId()).setLastPrintTime(log.getPrintTime()))
                    .collect(toList());
            meansMaterialAbs.updatePrintTime(dtos);
        }
    }
}
