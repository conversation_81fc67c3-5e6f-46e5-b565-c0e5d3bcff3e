package com.niimbot.asset.means.domain.abs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapstruct.MeansSystemMapStruct;
import com.niimbot.asset.means.model.AsAdminPrinterConcentration;
import com.niimbot.asset.means.service.AdminPrinterConcentrationService;
import com.niimbot.asset.system.abs.AdminPrinterConcentrationAbs;
import com.niimbot.asset.system.dto.AdminPrinterConcentrationGetQry;
import com.niimbot.asset.system.dto.AdminPrinterConcentrationSaveOrUpdateCmd;
import com.niimbot.asset.system.dto.clientobject.AdminPrinterConcentrationCO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/13 10:03 上午
 */
@RestController
@RequestMapping("/client/abs/means/AdminPrinterConcentrationAbs/")
@RequiredArgsConstructor
public class AdminPrinterConcentrationAbsImpl implements AdminPrinterConcentrationAbs {

    private final MeansSystemMapStruct systemMapStruct;

    private final AdminPrinterConcentrationService adminPrinterConcentrationService;


    @Override
    public AdminPrinterConcentrationCO getAdminPrinterConcentration(AdminPrinterConcentrationGetQry qry) {
        return systemMapStruct.conventAsAdminPrinterConcentrationModelToCo(adminPrinterConcentrationService.getOne(
                Wrappers.<AsAdminPrinterConcentration>lambdaQuery()
                        .eq(AsAdminPrinterConcentration::getPrinterId, qry.getPrinterId())
                        .eq(AsAdminPrinterConcentration::getIsDefault, true)
                        .eq(AsAdminPrinterConcentration::getUserId, LoginUserThreadLocal.getCurrentUserId())));
    }

    @Override
    public Boolean saveOrUpdateAdminPrinterConcentration(AdminPrinterConcentrationSaveOrUpdateCmd cmd) {
        return adminPrinterConcentrationService.saveOrUpdate(systemMapStruct.conventAsAdminPrinterConcentrationCoToModel(cmd.getAdminPrinterConcentration()));
    }

}
