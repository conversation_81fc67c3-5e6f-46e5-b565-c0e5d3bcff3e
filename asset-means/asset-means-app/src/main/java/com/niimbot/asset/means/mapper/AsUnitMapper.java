package com.niimbot.asset.means.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsUnit;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 计量单位 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@EnableDataPerm(excludeMethodName = {"search", "customCount"})
public interface AsUnitMapper extends BaseMapper<AsUnit> {

    List<AsUnit> search(@Param("name") String name, @Param("companyId") Long companyId);

    @Select("select count(*) from as_unit where company_id in (0, #{companyId}) and is_delete = 0 and name = #{unitName}")
    int customCount(@Param("unitName") String unitName, @Param("companyId") Long companyId);
}
