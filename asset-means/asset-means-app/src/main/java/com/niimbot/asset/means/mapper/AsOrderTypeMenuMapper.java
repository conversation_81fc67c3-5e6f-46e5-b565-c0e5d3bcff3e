package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsOrderTypeMenu;
import com.niimbot.asset.system.model.AsCusMenu;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 单据类型-菜单关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsOrderTypeMenuMapper.class)
public interface AsOrderTypeMenuMapper extends BaseMapper<AsOrderTypeMenu> {

    /**
     * 根据单据类型获取菜单
     * @param orderType 单据类型
     * @return
     */
    AsCusMenu getMenuById(Short orderType);
}
