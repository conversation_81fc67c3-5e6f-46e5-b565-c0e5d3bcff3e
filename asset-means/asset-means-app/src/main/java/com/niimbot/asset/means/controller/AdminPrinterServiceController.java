package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.model.AsAdminPrinter;
import com.niimbot.asset.means.model.AsAdminPrinterSize;
import com.niimbot.asset.means.service.AdminPrinterSeriesService;
import com.niimbot.asset.means.service.AdminPrinterService;
import com.niimbot.asset.means.service.AsAdminPrinterSizeService;
import com.niimbot.asset.system.model.AsAdminPrinterSeries;
import com.niimbot.asset.system.model.AsCusEmployeeExt;
import com.niimbot.asset.system.model.AsPrinterDrive;
import com.niimbot.asset.system.model.AsTagSize;
import com.niimbot.asset.system.model.AsUserTag;
import com.niimbot.asset.system.service.AsCusEmployeeExtService;
import com.niimbot.asset.system.service.AsUserTagService;
import com.niimbot.asset.system.service.PrinterDriveService;
import com.niimbot.asset.system.service.TagSizeService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.MaxDriveVersion;
import com.niimbot.system.PrinterDriveDto;
import com.niimbot.system.PrinterInnerModeInfo;
import com.niimbot.system.PrinterTagSizeQueryDto;
import com.niimbot.system.TagSizeDto;

import org.apache.commons.compress.utils.Lists;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

import static java.util.stream.Collectors.toList;

/**
 * 打印机控制器
 *
 * <AUTHOR>
 * @date 2020/12/17 17:17
 */
@RestController
@RequestMapping("server/system/printer")
@RequiredArgsConstructor
public class AdminPrinterServiceController {
    private final AdminPrinterService adminPrinterService;
    private final TagSizeService tagSizeService;
    private final AsUserTagService userTagService;
    private final AsAdminPrinterSizeService printerSizeService;
    private final AdminPrinterSeriesService adminPrinterSeriesService;
    private final AsCusEmployeeExtService cusEmployeeExtService;
    private final PrinterDriveService printerDriveService;

    /**
     * 查询所有打印机数据
     *
     * @return 所有数据
     */
    @GetMapping("list")
    public List<Map<String, Object>> list() {
        List<AsAdminPrinter> dtoList = this.adminPrinterService.list();
        Map<Long, List<AsAdminPrinter>> collect = dtoList.stream()
                .collect(Collectors.groupingBy(AsAdminPrinter::getType));

        // 获取设备系列信息
        List<AsAdminPrinterSeries> seriesList = adminPrinterSeriesService.list();
        Map<Long, String> seriesMap = seriesList.stream().collect(Collectors.toMap(AsAdminPrinterSeries::getId, AsAdminPrinterSeries::getPictureUrl));
        Map<Long, String> seriesNameMap = seriesList.stream().collect(Collectors.toMap(AsAdminPrinterSeries::getId, AsAdminPrinterSeries::getName));

        // 获取用户默认打印机系列
        AsCusEmployeeExt cusUserExt = cusEmployeeExtService.getById(LoginUserThreadLocal.getCurrentUserId());
        List<Map<String, Object>> res = Lists.newArrayList();
        collect.forEach((k, v) -> {
            String seriesName = seriesNameMap.getOrDefault(k, "");
            if (StrUtil.isNotBlank(seriesName)) {
                Set<Long> ids = v.stream().map(AsAdminPrinter::getId).collect(Collectors.toSet());
                Dict set = Dict.create().set("printerType", k).set("serialName", seriesName)
                        .set("pictureUrl", seriesMap.getOrDefault(k, "")).set("selected", false).set("list", v);
                if (ids.contains(cusUserExt.getDefaultPrinterId())) {
                    set.set("selectedPrinterId", cusUserExt.getDefaultPrinterId());
                } else {
                    set.set("selectedPrinterId", null);
                }
                res.add(set);
            }
        });

        CollUtil.sort(res, (k1, k2) -> {
            Long printerType1 = Convert.toLong(k1.get("printerType"), -1L);
            Long printerType2 = Convert.toLong(k2.get("printerType"), -1L);
            return printerType1.compareTo(printerType2);
        });
        return res;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public AsAdminPrinter getById(@PathVariable Long id) {
        return this.adminPrinterService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param adminPrinter 实体对象
     * @return 新增结果
     */
    @PostMapping
    public Boolean insert(@RequestBody AsAdminPrinter adminPrinter) {
        AsAdminPrinter one = this.adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getName, adminPrinter.getName()));
        if (one != null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_NAME_EXIST);
        }
        return this.adminPrinterService.save(adminPrinter);
    }

    /**
     * 修改数据
     *
     * @param adminPrinter 实体对象
     * @return 修改结果
     */
    @PutMapping
    public Boolean update(@RequestBody AsAdminPrinter adminPrinter) {
        AsAdminPrinter one = this.adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getName, adminPrinter.getName())
                .ne(AsAdminPrinter::getId, adminPrinter.getId()));
        if (one != null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_NAME_EXIST);
        }
        return this.adminPrinterService.updateById(adminPrinter);
    }

    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping("{id}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(@PathVariable Long id) {
        printerSizeService.remove(Wrappers.<AsAdminPrinterSize>lambdaQuery().eq(AsAdminPrinterSize::getPrinterId, id));
        return this.adminPrinterService.removeById(id);
    }

    /**
     * 标签尺寸列表
     *
     * @param printerId 打印机id
     * @return 所有数据
     */
    @GetMapping("/tag/list")
    public List<AsTagSize> tagSizeList(Long printerId) {
        List<Long> collect = printerSizeService.list(Wrappers.<AsAdminPrinterSize>lambdaQuery()
                        .eq(AsAdminPrinterSize::getPrinterId, printerId)).stream().map(AsAdminPrinterSize::getSizeId)
                .collect(toList());
        if (CollUtil.isEmpty(collect)) {
            return Collections.EMPTY_LIST;
        }
        return this.tagSizeService.list(Wrappers.<AsTagSize>lambdaQuery().in(AsTagSize::getId, collect));
    }

    /**
     * 标签尺寸分页
     *
     * @param dto page对象
     * @return 所有数据
     */
    @GetMapping(value = "/tag/page")
    public IPage<AsTagSize> tagSizePage(PrinterTagSizeQueryDto dto) {
        List<Long> collect = printerSizeService.list(Wrappers.<AsAdminPrinterSize>lambdaQuery()
                        .eq(AsAdminPrinterSize::getPrinterId, dto.getPrinterId())).stream().map(AsAdminPrinterSize::getSizeId)
                .collect(toList());
        if (CollUtil.isEmpty(collect)) {
            return dto.buildIPage();
        }

        return this.tagSizeService.page(dto.buildIPage(), Wrappers.<AsTagSize>lambdaQuery()
                .in(AsTagSize::getId, collect));
    }

    /**
     * 新增标签尺寸
     *
     * @param tagSize 实体对象
     * @return 新增结果
     */
    @PostMapping("/tag")
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertTagSize(@RequestBody TagSizeDto tagSize) {
        AsAdminPrinter printer = this.adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getId, tagSize.getPrinterId()));
        if (printer == null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_NOT_EXIST);
        }
        AsTagSize one = tagSizeService.getOne(Wrappers.<AsTagSize>lambdaQuery()
                .eq(AsTagSize::getSizeLong, tagSize.getSizeLong()).eq(AsTagSize::getSizeWide, tagSize.getSizeWide()));
        if (one != null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_TAG_SIZE_EXIST);
        }
        // 保存标签尺寸
        AsTagSize toSave = BeanUtil.copyProperties(tagSize, AsTagSize.class);
        this.tagSizeService.save(toSave);
        // 保存打印机和尺寸关系
        return printerSizeService.save(new AsAdminPrinterSize().setPrinterId(tagSize.getPrinterId())
                .setSizeId(toSave.getId()));
    }

    /**
     * 修改标签尺寸
     *
     * @param tagSize 实体对象
     * @return 修改结果
     */
    @PutMapping("/tag")
    public Boolean updateTagSize(@RequestBody AsTagSize tagSize) {
        AsTagSize one = tagSizeService.getOne(Wrappers.<AsTagSize>lambdaQuery()
                .ne(AsTagSize::getId, tagSize.getId())
                .eq(AsTagSize::getSizeLong, tagSize.getSizeLong()).eq(AsTagSize::getSizeWide, tagSize.getSizeWide()));
        if (one != null) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_TAG_SIZE_EXIST);
        }
        if (!this.tagSizeService.updateById(tagSize)) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_TAG_SIZE_NOT_EXIST);
        }
        return true;
    }

    /**
     * 删除标签尺寸
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping("/tag/{id}")
    public Boolean deleteSerial(@PathVariable Long id) {
        List<AsUserTag> list = userTagService.list(Wrappers.<AsUserTag>lambdaQuery().eq(AsUserTag::getSizeId, id));
        if (CollUtil.isNotEmpty(list)) {
            throw new BusinessException(SystemResultCode.PRINT_PRINTER_TAG_SIZE_USED);
        }
        return this.tagSizeService.removeById(id);
    }

    /**
     * 设置用户默认打印机系列
     *
     * @param id id
     * @return Boolean
     */
    @PutMapping("/setDefaultSeries/{id}")
    public Boolean setDefaultSeries(@PathVariable("id") Long id) {
        return adminPrinterService.setDefaultSeries(id);
    }

    /**
     * 获取设备详情
     *
     * @param model model
     * @return AdminPrinterDto
     */
    @GetMapping("/getPrinterDetail")
    public AsAdminPrinter getPrinterDetail(@RequestParam("model") String model) {
        return adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery().eq(AsAdminPrinter::getModel, model));
    }

    @GetMapping("/getMaxDriveVersion")
    public MaxDriveVersion getMaxDriveVersion() {
        List<PrinterDriveDto> dtos = new ArrayList<>();
        Map<Long, String> idNameMap = adminPrinterSeriesService.list(
                Wrappers.lambdaQuery(AsAdminPrinterSeries.class)
                        .select(AsAdminPrinterSeries::getId, AsAdminPrinterSeries::getName)
        ).stream().collect(Collectors.toMap(AsAdminPrinterSeries::getId, AsAdminPrinterSeries::getName));
        idNameMap.forEach((k, v) -> {
            AsPrinterDrive drive = printerDriveService.getOne(
                    Wrappers.lambdaQuery(AsPrinterDrive.class)
                            .eq(AsPrinterDrive::getPrinterSeriesId, k)
                            .orderByDesc(AsPrinterDrive::getVersion)
                            .last("LIMIT 1")
            );
            if (Objects.nonNull(drive)) {
                PrinterDriveDto dto = new PrinterDriveDto().setId(drive.getId()).setPrinterSeriesId(k).setPrinterSeriesName(v)
                        .setVersionText(AsPrinterDrive.vToString(drive.getVersion())).setDriveUrl(drive.getDriveUrl()).setMode(drive.getMode()).setRemark(drive.getRemark());
                dtos.add(dto);
            }
        });
        MaxDriveVersion maxDriveVersion = new MaxDriveVersion().setLast(dtos);
        // 需要强制更新的版本号
        List<PrinterDriveDto> current = dtos.stream().map(v -> {
            AsPrinterDrive drive = printerDriveService.getOne(
                    Wrappers.lambdaQuery(AsPrinterDrive.class)
                            .eq(AsPrinterDrive::getPrinterSeriesId, v.getPrinterSeriesId())
                            .eq(AsPrinterDrive::getMode, 2)
                            .orderByDesc(AsPrinterDrive::getVersion)
                            .last("LIMIT 1")
            );
            if (Objects.isNull(drive)) {
                return v;
            }
            return new PrinterDriveDto()
                    .setId(drive.getId())
                    .setPrinterSeriesId(drive.getPrinterSeriesId()).setPrinterSeriesName(v.getPrinterSeriesName())
                    .setVersionText(AsPrinterDrive.vToString(drive.getVersion()))
                    .setDriveUrl(drive.getDriveUrl()).setMode(drive.getMode())
                    .setRemark(drive.getRemark());
        }).collect(toList());
        maxDriveVersion.setCurrent(current);
        return maxDriveVersion;
    }

    @GetMapping("/getInnerMode")
    public List<PrinterInnerModeInfo> getInnerMode() {
        List<AsAdminPrinter> printers = adminPrinterService.list();
        return printers.stream().map(p -> {
            PrinterInnerModeInfo info = new PrinterInnerModeInfo().setId(p.getId()).setName(p.getName());
            String innerModel = p.getInnerModel();
            if (StrUtil.isBlank(innerModel)) {
                return info;
            }
            String[] split = innerModel.split(",");
            if (split.length == 1 && HexUtil.isHexNumber(split[0])) {
                // ((1转10进制) * 256)
                info.setMode(Long.decode(split[0]) * 256);
            }
            if (split.length == 2 && HexUtil.isHexNumber(split[0]) && HexUtil.isHexNumber(split[1])) {
                //  ((1转10进制) * 256) + (2转10进制)
                info.setMode((Long.decode(split[0]) * 256) + (Long.decode(split[1])));
            }
            return info;
        }).collect(toList());
    }
}
