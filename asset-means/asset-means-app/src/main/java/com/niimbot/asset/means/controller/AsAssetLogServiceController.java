package com.niimbot.asset.means.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.means.model.AsAssetLog;
import com.niimbot.asset.means.service.AsAssetLogService;
import com.niimbot.means.AsAssetLogDto;
import com.niimbot.means.AsAssetLogQueryDto;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * 资产日志控制器
 *
 * <AUTHOR>
 * @since 2020-12-25
 */
@RestController
@RequestMapping("server/means/assetLog")
@RequiredArgsConstructor
public class AsAssetLogServiceController {

    private final AsAssetLogService logService;

    @GetMapping("/page")
    public IPage<AsAssetLogDto> page(AsAssetLogQueryDto dto) {
        return logService.customPage(dto);
    }

    @PostMapping("/list")
    public List<AsAssetLogDto> list(@RequestBody AsAssetLogQueryDto dto) {
        return logService.customList(dto);
    }

    @PostMapping
    public Boolean insert(@RequestBody AsAssetLog assetLog) {
        return logService.save(assetLog);
    }
}
