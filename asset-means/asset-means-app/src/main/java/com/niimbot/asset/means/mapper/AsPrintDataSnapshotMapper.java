package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.niimbot.asset.means.model.AsPrintDataSnapshot;
import com.niimbot.means.UserPrintTaskGroupByStatusPageQuery;
import com.niimbot.means.UserPrintTaskGroupStatusResult;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface AsPrintDataSnapshotMapper extends BaseMapper<AsPrintDataSnapshot> {

    IPage<UserPrintTaskGroupStatusResult> taskDetailsPage(IPage<UserPrintTaskGroupByStatusPageQuery> page, @Param("em") UserPrintTaskGroupByStatusPageQuery query);

}
