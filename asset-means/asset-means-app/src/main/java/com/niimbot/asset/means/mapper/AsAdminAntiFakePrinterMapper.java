package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.means.model.AsAdminAntiFakePrinter;
import com.niimbot.jf.mybatisplus.autoconfigure.cache.MybatisRedisCache;
import com.niimbot.system.AdminAntiFakePrinterDto;
import com.niimbot.system.AdminAntiFakeSerialListDto;
import com.niimbot.system.AdminAntiFakeSerialQueryDto;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.CacheNamespaceRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;

/**
 * <p>
 * 运营后台-防伪配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-05
 */
@CacheNamespace(implementation = MybatisRedisCache.class, properties = {@Property(name = "flushInterval", value = "3600000")})
@CacheNamespaceRef(AsAdminAntiFakePrinterMapper.class)
public interface AsAdminAntiFakePrinterMapper extends BaseMapper<AsAdminAntiFakePrinter> {

    /**
     * 查询所有数据
     *
     * @return 所有数据
     */
    List<AdminAntiFakePrinterDto> selectPrinterList();

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    AdminAntiFakePrinterDto getByPrinterId(@Param("id") Long id);

    /**
     * 查询所有打印机序列号列表
     *
     * @param printerId 打印机id
     * @return 所有数据
     */
    List<AdminAntiFakeSerialListDto> selectPrinterSerial(@Param("printerId") Long printerId);

    /**
     * 查询所有打印机序列号列表
     *
     * @param page      page对象
     * @param dto 打印机id
     * @return 所有数据
     */
    IPage<AdminAntiFakeSerialListDto> selectPrinterSerial(@Param("page") Page<AdminAntiFakeSerialQueryDto> page, @Param("ew") AdminAntiFakeSerialQueryDto dto);
}
