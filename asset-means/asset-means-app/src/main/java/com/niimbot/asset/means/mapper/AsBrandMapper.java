package com.niimbot.asset.means.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsBrand;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 品牌管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@EnableDataPerm(excludeMethodName = {"search", "customCount"})
public interface AsBrandMapper extends BaseMapper<AsBrand> {

    List<AsBrand> search(@Param("name") String name, @Param("companyId") Long companyId);

    @Select("select count(*) from as_brand where company_id in (0, #{companyId}) and is_delete = 0 and name = #{brandName}")
    int customCount(@Param("brandName") String brandName, @Param("companyId") Long companyId);
}
