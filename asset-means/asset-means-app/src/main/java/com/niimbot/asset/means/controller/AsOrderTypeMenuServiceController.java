package com.niimbot.asset.means.controller;


import com.niimbot.asset.means.service.AsOrderTypeMenuService;
import com.niimbot.asset.system.model.AsCusMenu;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 单据类型-菜单关联表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@RestController
@RequestMapping("server/means/orderTypeMenu")
@RequiredArgsConstructor
public class AsOrderTypeMenuServiceController {

    private final AsOrderTypeMenuService orderTypeMenuService;

    /**
     * 根据单据类型获取菜单
     * @param orderType 单据类型
     * @return
     */
    @GetMapping(value = "/{orderType}")
    public AsCusMenu getMenuById(@PathVariable("orderType") Short orderType) {
        return orderTypeMenuService.getMenuById(orderType);
    }

}
