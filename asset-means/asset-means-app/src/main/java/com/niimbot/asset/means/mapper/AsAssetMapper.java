package com.niimbot.asset.means.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.niimbot.asset.means.model.AsAsset;
import com.niimbot.equipment.EntMatPlanEntData;
import com.niimbot.equipment.GetSelectedEntData;
import com.niimbot.framework.dataperm.annonation.EnableDataPerm;
import com.niimbot.inventory.InventoryDispatchGroupAssetDto;
import com.niimbot.means.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 资产表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@EnableDataPerm(excludeMethodName = {"selectAssetNumGroup", "listByIdsNoPerm", "getByIdNoPerm", "getByCodeNoPerm",
        "checkManageAsset", "checkUseAsset", "countAssetByTimeExcludeTestCompany", "selectAllForOrderMessage",
        "selectAllForAssetUseTimeLimitMessage", "selectBySubAssetIdApp", "getInfoNoPermByCodePhp", "quickSearch"})
public interface AsAssetMapper extends BaseMapper<AsAsset> {

    /**
     * APP分页查询
     *
     * @param queryDto   查询信息
     * @param page       分页参数
     * @param conditions 查询条件
     * @return 资产分页
     */
    IPage<AssetAppPageDto> pageApp(Page<AsAsset> page, @Param("ew") AssetQueryConditionDto queryDto,
                                   @Param("conditions") String conditions);

    /**
     * APP分页查询
     * @param queryDto   查询信息
     * @return 资产分页
     */
    List<AssetRelationAppDto> assetRelationApp(@Param("ew") AssetQueryConditionDto queryDto);

    /**
     * APP分页查询
     *
     * @param queryDto   查询信息
     * @param page       分页参数
     * @param conditions 查询条件
     * @return 资产分页
     */
    IPage<AssetRelationAppDto> mainAssetRelationApp(Page<AsAsset> page, @Param("ew") AssetQueryConditionDto queryDto,
                                                    @Param("conditions") String conditions, @Param("statusCondition") String statusCondition);

    /**
     * 子资产分页-app接口
     * @param page
     * @param queryDto
     * @param conditions
     * @return
     */
    IPage<AssetRelationAppDto> subAssetApp(Page<AssetRelationDto> page, @Param("ew") AssetRelationQueryConditionDto queryDto,
                                                @Param("conditions") String conditions);

    /**
     * 查询主资产数据信息
     * @param assetId
     * @param subAssetId
     * @param companyId
     * @return
     */
    AssetRelationAppDto selectBySubAssetIdApp(@Param("assetId") Long assetId, @Param("subAssetId") Long subAssetId, @Param("companyId") Long companyId);

    /**
     * 资产关联关系分页查询
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AssetRelationDto> relationPage(Page<AsAsset> page, @Param("ew") AssetQueryConditionDto queryDto, @Param("conditions") String conditions, @Param("statusCondition") String statusCondition);

    /**
     * 子资产分页查询
     * @param page
     * @param queryDto
     * @return
     */
    IPage<AssetRelationDto> subAssetPage(Page<AssetRelationDto> page, @Param("ew") AssetRelationQueryConditionDto queryDto, @Param("conditions") String conditions);

    List<AssetDto> selectAllByUsePersonOrManagerOwner(@Param("empId") Long empId);

    /**
     * 分页查询
     *
     * @param page
     * @param queryDto
     * @param conditions
     * @return
     */
    IPage<AssetDto> customPage(Page<AsAsset> page, @Param("ew") AssetQueryConditionDto queryDto,
                               @Param("conditions") String conditions);

    IPage<EntMatPlanEntData> pageEntMatPlanDataRangeTypeIsMas(Page<AsAsset> page, @Param("ew") GetSelectedEntData ew, @Param("conditions") String conditions);

    IPage<EntMatPlanEntData> pageEntMatPlanDataRangeTypeIsCate(Page<AsAsset> page, @Param("ew") GetSelectedEntData ew, @Param("conditions") String conditions);

    Long customPageCount(@Param("ew") AssetQueryConditionDto queryDto,
                         @Param("conditions") String conditions);

    IPage<Long> selectPrintAssetIds(Page<AsAsset> page,
                                    @Param("ew") AssetQueryConditionDto queryDto,
                                    @Param("conditions") String conditions,
                                    @Param("companyId") long companyId);

    /**
     * 分页查询
     *
     * @param queryDto
     * @param conditions
     * @return
     */
    List<AssetDto> customPage(@Param("ew") AssetQueryConditionDto queryDto, @Param("conditions") String conditions);

    /**
     * 首页-资产状态统计
     * [自动权限]
     */
    JSONObject assetStatusReport();

    /**
     * 首页-资产增量统计数量
     * [自动权限]
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 资产增量统计数量对象
     */
    List<HashMap<String, Object>> monthsReport(@Param("beginTime") String beginTime,
                                               @Param("endTime") String endTime);

    /**
     * 首页-资产增量统计价值
     * [自动权限]
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 资产增量统计价值
     */
    List<HashMap<String, Object>> monthsWorthReport(@Param("beginTime") String beginTime,
                                                    @Param("endTime") String endTime);

    /**
     * 首页-资产分类统计
     * [自动权限]
     * @return 资产分类统计对象
     */
    List<HashMap<Object, Object>> getCategoryAssetReport();

    /**
     * 首页-资产使用组织统计
     * [自动权限]
     * @return 资产使用组织统计对象
     */
    List<HashMap<Object, Object>> getUseOrgAssetGroupReport();

    /**
     * 金额统计
     * [自动权限]
     * @param queryDto 查询条件
     * @return 结果
     */
    String amountMoney(@Param("ew") AssetQueryConditionDto queryDto, @Param("conditions") String conditions);

    /**
     * 分组统计资产数量
     * [租户权限]
     * @return 资产数量
     */
    List<InventoryDispatchGroupAssetDto> selectAssetNumGroup(@Param("groupByStr") String groupByStr,
                                                             @Param("ids") List<Long> assetIds,
                                                             @Param("companyId") Long companyId);

    /**
     * 统计当天删除资产数
     * [全部公司权限]
     * @param
     * @return 删除资产数
     */
    @Select("select count(t1.id) from as_asset t1 LEFT JOIN as_company_setting t2 ON t1.company_id = t2.company_id where t1.is_delete = 1 AND t2.is_delete = 0 AND t2.is_test = 0 and t1.update_time >= #{beginTime} and t1.update_time <= #{endTime}")
    Integer countDeleteNum(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 查询资产数据（不带权限)
     * [租户权限]
     * @param
     * @return 删除资产数
     */
    List<AsAsset> listByIdsNoPerm(@Param("assetIdList") List<Long> assetIdList, @Param("companyId") Long companyId);

    AsAsset getInRecycleBin(@Param("id") Long id);

    /**
     * 查询获取资产信息 [租户权限]
     *
     * @param code 资产编码
     * @return AsAsset信息
     */
    AsAsset getByCodeNoPerm(@Param("code") String code, @Param("companyId") Long companyId);

    /**
     * [租户权限]
     */
    List<AssetDto> selectUseAsset(@Param("userId") Long userId, @Param("companyId") Long companyId);

    /**
     * [租户权限]
     */
    List<AssetDto> selectOwnerAsset(@Param("userId") Long userId, @Param("companyId") Long companyId);

    /**
     * [租户权限]
     */
    List<AssetDto> selectUseOrg(@Param("userId") Long userId, @Param("from") Long from, @Param("companyId") Long companyId);

    /**
     * [租户权限]
     */
    Boolean updateUseOrg(@Param("userId") Long userId, @Param("from") Long from, @Param("to") Long to, @Param("companyId") Long companyId);

    /**
     * [租户权限]
     */
    @Select("select id from as_asset where manager_owner = #{userId} and is_delete = 0 and company_id = #{companyId} limit 1")
    Long checkManageAsset(@Param("userId") String currentUserId, @Param("companyId") Long companyId);

    /**
     * [全部权限]1
     */
    @Select("SELECT COUNT(t1.id) AS num FROM as_asset t1 LEFT JOIN as_company_setting t2 ON t1.company_id = t2.company_id WHERE t1.is_delete = 0 AND t2.is_delete = 0 AND t2.is_test = 0 AND t1.create_time >= #{startTime} AND t1.create_time <= #{endTime}")
    Integer countAssetByTimeExcludeTestCompany(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * [租户权限]
     */
    @Select("select id from as_asset where use_person = #{userId} and is_delete = 0 and company_id = #{companyId} limit 1")
    Long checkUseAsset(@Param("userId") String currentUserId, @Param("companyId") Long companyId);

    /**
     * 根据资产id或资产phpId获取资产信息，做了和phpId关联
     * [租户权限]
     */
    AsAsset getInfoNoPermByCodePhp(@Param("assetCode") String assetCode,
                                   @Param("uniqueCodes") List<String> uniqueCodes,
                                   @Param("companyId") Long companyId);

    List<AssetValue> selectCountByAreaId(@Param("areaIds") List<String> areaIds);

    IPage<AssetValue> selectCountByAreaId(Page<SearchAppAssetAreaDto> page, @Param("areaIds") List<String> areaIds);

    /**
     * [租户权限]
     */
    List<AssetForMessage> selectAllForOrderMessage(@Param("companyId") Long companyId, @Param("type") String type, @Param("days") List<Integer> days);

    /**
     * [租户权限]
     */
    List<AssetForMessage> selectAllForAssetUseTimeLimitMessage(@Param("companyId") Long companyId, @Param("days") List<Integer> days);

    @Insert("REPLACE INTO AS_STATISTICAL VALUES (#{companyId}, 'asset_count', #{assetCount})")
    void updateAssetStatistical(@Param("companyId") Long companyId, @Param("assetCount") Long assetCount);

    List<AssetDto> listInventoryDispatchAssets(@Param("ew") AssetQueryConditionDto queryDto,
                                               @Param("conditions") String conditions);

    List<AreaMeansStatusGroup> selectCountByAreaGroupByStatus(@Param("companyId") Long companyId, @Param("areaIds") List<String> areaIds);

    List<AsAsset> quickSearch(@Param("query") AssetQuickSearchDto query,
                              @Param("companyId") Long companyId);
}
