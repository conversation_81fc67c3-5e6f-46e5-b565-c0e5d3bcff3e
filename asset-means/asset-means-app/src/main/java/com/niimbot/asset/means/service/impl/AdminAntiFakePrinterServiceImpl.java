package com.niimbot.asset.means.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.utils.IdUtils;
import com.niimbot.asset.framework.utils.StringUtils;
import com.niimbot.asset.means.mapper.AsAdminAntiFakePrinterMapper;
import com.niimbot.asset.means.model.AsAdminAntiFakePrinter;
import com.niimbot.asset.means.service.AdminAntiFakePrinterService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.system.AdminAntiFakePrinterDto;
import com.niimbot.system.AdminAntiFakeSerialInsertDto;
import com.niimbot.system.AdminAntiFakeSerialListDto;
import com.niimbot.system.AdminAntiFakeSerialQueryDto;
import com.niimbot.system.AdminAntiFakeSerialUpdateDto;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2020/12/17 17:14
 */
@Service
public class AdminAntiFakePrinterServiceImpl extends ServiceImpl<AsAdminAntiFakePrinterMapper, AsAdminAntiFakePrinter>
        implements AdminAntiFakePrinterService {

    /**
     * 查询所有数据
     *
     * @return 所有数据
     */
    @Override
    public List<AdminAntiFakePrinterDto> selectPrinterList() {
        return this.getBaseMapper().selectPrinterList();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Override
    public AdminAntiFakePrinterDto getByPrinterId(Long id) {
        return this.getBaseMapper().getByPrinterId(id);
    }

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @Override
    public Boolean insertPrinter(AdminAntiFakePrinterDto dto) {
        String printerName = dto.getPrinterName();
        AsAdminAntiFakePrinter one = getOne(Wrappers.<AsAdminAntiFakePrinter>lambdaQuery()
                .eq(AsAdminAntiFakePrinter::getPrinterName, printerName)
                .last("limit 1"));
        if (null != one) {
            throw new BusinessException(SystemResultCode.PRINT_ANTI_PRINTER_NAME_EXIST);
        }
        AsAdminAntiFakePrinter insert = BeanUtil.copyProperties(dto, AsAdminAntiFakePrinter.class);
        insert.setPrinterId(IdUtils.getId());
        return save(insert);
    }

    /**
     * 修改数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @Override
    public Boolean updatePrinter(AdminAntiFakePrinterDto dto) {
        AsAdminAntiFakePrinter one = getOne(Wrappers.<AsAdminAntiFakePrinter>lambdaQuery()
                .eq(AsAdminAntiFakePrinter::getPrinterName, dto.getPrinterName())
                .ne(AsAdminAntiFakePrinter::getPrinterId, dto.getId())
                .last("limit 1"));
        if (null != one) {
            throw new BusinessException(SystemResultCode.PRINT_ANTI_PRINTER_NAME_EXIST);
        }
        return this.update(Wrappers.<AsAdminAntiFakePrinter>lambdaUpdate()
                .set(StrUtil.isNotBlank(dto.getPrinterName()), AsAdminAntiFakePrinter::getPrinterName, dto.getPrinterName())
                .set(StrUtil.isNotBlank(dto.getRemark()), AsAdminAntiFakePrinter::getRemark, dto.getRemark())
                .set(ObjectUtil.isNotNull(dto.getIsPass()), AsAdminAntiFakePrinter::getIsPass, dto.getIsPass())
                .eq(AsAdminAntiFakePrinter::getPrinterId, dto.getId())
        );
    }

    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @Override
    public Boolean deletePrinter(Long id) {
        this.getBaseMapper()
                .delete(Wrappers.<AsAdminAntiFakePrinter>lambdaQuery().eq(AsAdminAntiFakePrinter::getPrinterId, id));
        return true;
    }

    /**
     * 查询所有打印机序列号列表
     *
     * @param printerId 打印机id
     * @return 所有数据
     */
    @Override
    public List<AdminAntiFakeSerialListDto> selectPrinterSerialList(Long printerId) {
        return this.getBaseMapper().selectPrinterSerial(printerId);
    }

    /**
     * 查询所有打印机序列号列表
     *
     * @param dto page对象
     * @return 所有数据
     */
    @Override
    public IPage<AdminAntiFakeSerialListDto> selectPrinterSerialPage(AdminAntiFakeSerialQueryDto dto) {
        return this.getBaseMapper().selectPrinterSerial(dto.buildIPage(), dto);
    }

    /**
     * 新增序列号数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @Override
    public Boolean insertSerialNumber(AdminAntiFakeSerialInsertDto dto) {
        List<AsAdminAntiFakePrinter> serial = list(Wrappers.<AsAdminAntiFakePrinter>lambdaQuery()
                .eq(AsAdminAntiFakePrinter::getPrinterId, dto.getPrinterId()));
        if (CollUtil.isEmpty(serial)) {
            throw new BusinessException(SystemResultCode.PRINT_ANTI_PRINTER_NOT_EXIST);
        }
        boolean serialExist = serial.stream().anyMatch(eq -> StringUtils.equals(dto.getSerialNumber(), eq.getSerialNumber()));
        if (serialExist) {
            throw new BusinessException(SystemResultCode.PRINT_ANTI_PRINTER_SERIAL_NO_EXIST);
        }
        Optional<AsAdminAntiFakePrinter> first = serial.stream().filter(eq -> StrUtil.isBlank(eq.getSerialNumber()))
                .findFirst();
        if (first.isPresent()) {
            AsAdminAntiFakePrinter cur = first.get();
            return this.update(Wrappers.<AsAdminAntiFakePrinter>lambdaUpdate()
                    .set(AsAdminAntiFakePrinter::getSerialNumber, dto.getSerialNumber())
                    .eq(AsAdminAntiFakePrinter::getId, cur.getId()));
        }
        AsAdminAntiFakePrinter newEq = serial.get(0);
        newEq.setId(null).setSerialNumber(dto.getSerialNumber());
        return this.save(newEq);
    }

    @Override
    public Boolean updateSerialNumber(AdminAntiFakeSerialUpdateDto dto) {
        AsAdminAntiFakePrinter serial = getOne(Wrappers.<AsAdminAntiFakePrinter>lambdaQuery()
                .eq(AsAdminAntiFakePrinter::getId, dto.getId()));
        if (serial == null) {
            throw new BusinessException(SystemResultCode.PRINT_ANTI_PRINTER_SERIAL_NO_NOT_EXIST);
        }
        AsAdminAntiFakePrinter one = getOne((Wrappers.<AsAdminAntiFakePrinter>lambdaQuery()
                .ne(AsAdminAntiFakePrinter::getId, dto.getId())
                .eq(AsAdminAntiFakePrinter::getPrinterId, serial.getPrinterId())
                .eq(AsAdminAntiFakePrinter::getSerialNumber, dto.getSerialNumber())));
        if (one != null) {
            throw new BusinessException(SystemResultCode.PRINT_ANTI_PRINTER_SERIAL_NO_EXIST);
        }
        return this.update(Wrappers.<AsAdminAntiFakePrinter>lambdaUpdate()
                .set(AsAdminAntiFakePrinter::getSerialNumber, dto.getSerialNumber())
                .eq(AsAdminAntiFakePrinter::getId, dto.getId()));
    }

    @Override
    public Boolean deleteSerial(Long id) {
        AsAdminAntiFakePrinter one = this.getOne(Wrappers.<AsAdminAntiFakePrinter>lambdaQuery()
                .eq(AsAdminAntiFakePrinter::getId, id));
        if (one == null) {
            throw new BusinessException(SystemResultCode.PRINT_AUTH_EQUIPMENT_AUTHINFO_NOT_EXIST);
        }
        List<AsAdminAntiFakePrinter> list = list(Wrappers.<AsAdminAntiFakePrinter>lambdaQuery()
                .eq(AsAdminAntiFakePrinter::getPrinterId, one.getPrinterId()));
        if (list.size() > 1) {
            return this.removeById(id);
        }
        return this.update(Wrappers.<AsAdminAntiFakePrinter>lambdaUpdate()
                .set(AsAdminAntiFakePrinter::getSerialNumber, null)
                .set(AsAdminAntiFakePrinter::getId, IdUtils.getId())
                .eq(AsAdminAntiFakePrinter::getId, id)
        );
    }
}
