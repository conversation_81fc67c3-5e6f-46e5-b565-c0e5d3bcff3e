package com.niimbot.asset.means.service.impl;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.framework.constant.DictConstant;
import com.niimbot.asset.framework.core.enums.SystemResultCode;
import com.niimbot.asset.framework.web.LoginUserThreadLocal;
import com.niimbot.asset.means.mapper.AsUserPrintTaskMapper;
import com.niimbot.asset.means.model.AsAdminPrinter;
import com.niimbot.asset.means.model.AsAdminPrinterConcentration;
import com.niimbot.asset.means.model.AsTagMaterial;
import com.niimbot.asset.means.model.AsUserPrintTask;
import com.niimbot.asset.means.service.AdminPrinterConcentrationService;
import com.niimbot.asset.means.service.AdminPrinterService;
import com.niimbot.asset.means.service.AsTagMaterialService;
import com.niimbot.asset.means.service.PrintTaskService;
import com.niimbot.asset.system.service.AsUserTagService;
import com.niimbot.asset.system.service.TagService;
import com.niimbot.jf.core.exception.category.BusinessException;
import com.niimbot.means.PrintDataSetDto;
import com.niimbot.system.SizeDto;
import com.niimbot.system.TagMaterialDto;
import com.niimbot.system.UserTagPrintDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2020/12/31
 */
@Service
@Slf4j
public class PrintServiceImpl extends ServiceImpl<AsUserPrintTaskMapper, AsUserPrintTask> implements PrintTaskService {

    @Resource
    private AsTagMaterialService tagMaterialService;

    @Resource
    private AsUserTagService userTagService;

    @Resource
    private TagService tagService;

    @Resource
    private AdminPrinterService adminPrinterService;

    @Resource
    private AdminPrinterConcentrationService printerConcentrationService;

    /**
     * 标签类型
     */
    private static final Integer SYSTEM_TAG = 1;
    private static final Integer CUSTOM_TAG = 2;
    private static final Integer CUSTOMIZED_TAG = 3;

    /**
     * 尺寸类型
     * 1--默认
     * 2--定制
     */
    private static final Integer DEFAULT_SIZE = 1;
    private static final Integer CUSTOMIZED_SIZE = 2;

//    /**
//     * 设置默认材质
//     *
//     * @param materialId 材质Id
//     * @return Boolean
//     */
//    @Override
//    public AsTagMaterial setDefaultMaterial(Long materialId) {
//        // 查询标签材质
//        AsTagMaterial tagMaterial = tagMaterialService.getById(materialId);
//        if (ObjectUtil.isNull(tagMaterial)) {
//            throw new BusinessException(SystemResultCode.TAG_MATERIAL_NOT_EXISTS);
//        }
//
//        // 当前用户
//        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
//        // 用户扩展表查询
//        AsCusUserExt asCusUserExt = Optional.ofNullable(cusUserExtService.getById(currentUserId))
//                .orElse(new AsCusUserExt().setId(currentUserId));
//
//        // 设置默认材质和默认浓度
//        asCusUserExt.setDefaultMaterialId(materialId);
//        if (!cusUserExtService.saveOrUpdate(asCusUserExt)) {
//            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
//        }
//        return tagMaterial;
//    }

    /**
     * 设置默认材质
     *
     * @param printDataSetDto 设置打印数据dto
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TagMaterialDto setDefaultMaterial(PrintDataSetDto printDataSetDto) {
        // 获取设备id
        AsAdminPrinter adminPrinter = adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getModel, printDataSetDto.getPrinterName()));
        if (ObjectUtil.isNull(adminPrinter)) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }
        Long printerId = adminPrinter.getId();

        // 查询标签材质
        Long materialId = printDataSetDto.getMaterialId();
        AsTagMaterial tagMaterial = tagMaterialService.getById(materialId);
        if (ObjectUtil.isNull(tagMaterial)) {
            throw new BusinessException(SystemResultCode.TAG_MATERIAL_NOT_EXISTS);
        }

        // 查询标签材质是否支持此机型
        AsAdminPrinterConcentration concentrationData = printerConcentrationService.getOne(
                Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getMaterialId, materialId)
                        .eq(AsAdminPrinterConcentration::getUserId, 0L)
        );
        if (ObjectUtil.isNull(concentrationData)) {
            throw new BusinessException(SystemResultCode.PRINTER_CONCENTRATION_NOT_EXIST);
        }

        // 当前用户
        Long currentUserId = LoginUserThreadLocal.getCurrentUserId();
        // 设备-材质-浓度关联表查询
        AsAdminPrinterConcentration printerConcentration = printerConcentrationService.getOne(
                Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getUserId, currentUserId)
                        .eq(AsAdminPrinterConcentration::getIsDefault, true)
        );
        AsAdminPrinterConcentration printerConcentrationNew = Optional.ofNullable(printerConcentration)
                .orElse(new AsAdminPrinterConcentration().setUserId(currentUserId).setPrinterId(printerId).setIsDefault(true));

        // 设置默认材质
        printerConcentrationNew.setMaterialId(materialId);
        if (!printerConcentrationService.saveOrUpdate(printerConcentrationNew)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 设备-材质-浓度关联表查询
        AsAdminPrinterConcentration printerConcentrationNotDefault = printerConcentrationService.getOne(
                Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getMaterialId, materialId)
                        .eq(AsAdminPrinterConcentration::getUserId, currentUserId)
                        .eq(AsAdminPrinterConcentration::getIsDefault, false)
        );
        AsAdminPrinterConcentration printerConcentrationNotDefaultNew = Optional.ofNullable(printerConcentrationNotDefault)
                .orElse(new AsAdminPrinterConcentration().setUserId(currentUserId).setPrinterId(printerId)
                        .setMaterialId(materialId).setIsDefault(false));
        if (!printerConcentrationService.saveOrUpdate(printerConcentrationNotDefaultNew)) {
            throw new BusinessException(SystemResultCode.OPT_EDIT_FAIL);
        }

        // 设置默认浓度
        Integer defaultConcentration = printerConcentrationService.getDefaultConcentration(printerId, materialId);

        TagMaterialDto tagMaterialDto = new TagMaterialDto();
        BeanUtil.copyProperties(tagMaterial, tagMaterialDto);
        tagMaterialDto.setDefaultConcentration(defaultConcentration)
                .setMinConcentration(concentrationData.getMinConcentration())
                .setMaxConcentration(concentrationData.getMaxConcentration());

        return tagMaterialDto;
    }

    /**
     * 获取标签材质列表
     *
     * @return AsTagMaterial
     */
    @Override
    public List<AsTagMaterial> getTagMaterial(String printerName) {
        if (StrUtil.isBlank(printerName)) {
            return tagMaterialService.list(Wrappers.<AsTagMaterial>lambdaQuery().orderByAsc(AsTagMaterial::getSortNum));
        }

        // 获取设备id
        AsAdminPrinter adminPrinter = adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getModel, printerName));
        if (null == adminPrinter) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }
        Long printerId = adminPrinter.getId();

        // 先查询对应的材质id
        List<AsAdminPrinterConcentration> printerConcentrationList = printerConcentrationService.list(Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                .select(AsAdminPrinterConcentration::getMaterialId)
                .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                .eq(AsAdminPrinterConcentration::getUserId, 0L)
        );
        List<Long> materialIds = printerConcentrationList.stream().distinct().map(AsAdminPrinterConcentration::getMaterialId).collect(Collectors.toList());

        if (CollUtil.isEmpty(materialIds)) {
            return Lists.newArrayList();
        }

        // 可选材质列表数据
        return tagMaterialService.list(Wrappers.<AsTagMaterial>lambdaQuery()
                .in(AsTagMaterial::getId, materialIds)
                .orderByAsc(AsTagMaterial::getSortNum)
                .orderByAsc(AsTagMaterial::getCreateTime));
    }

//    /**
//     * 获取用户的打印模板
//     *
//     * @return UserTagPrintDto
//     */
//    @Override
//    public UserTagPrintDto getPrintTpl(Short printType, Integer printerType) {
//        // 获取用户默认标签模板
//        AsCusUserExt asCusUserExt = cusUserExtService.getById(LoginUserThreadLocal.getCurrentUserId());
//        Long defaultTagId = userTagService.getFirstTagId(printType);
//
//        if (asCusUserExt != null && !asCusUserExt.getDefaultTagId().equals(0L)) {
//            if (DictConstant.PRINT_TYPE_ASSET == printType) {
//                UserTagPrintDto tagPrintDto = userTagService.getDetail(asCusUserExt.getDefaultTagId());
//                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
//                    defaultTagId = tagPrintDto.getId();
//                }
//            } else if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
//                UserTagPrintDto tagPrintDto = userTagService.getDetail(asCusUserExt.getDefaultCftagId());
//                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
//                    defaultTagId = tagPrintDto.getId();
//                }
//            }
//        }
//
//        // 获取标签详情数据
//        UserTagPrintDto tagDetail = userTagService.getDetail(defaultTagId);
//
//        //默认类型为 默认
//        Integer defaultType = SYSTEM_TAG;
//        if (SYSTEM_TAG.equals(tagDetail.getTagType())) {
//            if (CUSTOMIZED_SIZE.equals(tagDetail.getType())) {
//                //类型为 定制
//                defaultType = CUSTOMIZED_TAG;
//            }
//        } else if (CUSTOM_TAG.equals(tagDetail.getTagType())) {
//            //类型为 自定义
//            defaultType = CUSTOM_TAG;
//        }
//        tagDetail.setDefaultType(defaultType);
//        tagDetail.setDefaultSize(tagDetail.getSizeLong() + " x " + tagDetail.getSizeWide());
//
//        //目前app只有这三种尺寸，不能等同于size_id，返回的是app头部默认选中的是哪一项
//        // 获取app标签尺寸数据
//        List<SizeDto> sizeList = tagService.appSizeList("");
//        if (CollUtil.isEmpty(sizeList)) {
//            throw new BusinessException(SystemResultCode.TAG_SIZE_NOT_EXISTS);
//        }
//
//        HashMap<String, Long> sizeMap = new HashMap<>();
//        sizeList.forEach(val -> {
//            // 收集成map
//            sizeMap.put(val.getSizeLong() + " x " + val.getSizeWide(), val.getId());
//        });
//
//        // 默认尺寸id
//        tagDetail.setDefaultSizeId(sizeMap.getOrDefault(tagDetail.getDefaultSize(), 1L));
//
//        // 默认材质id
//        Long defaultMaterialId = (asCusUserExt != null && !asCusUserExt.getDefaultMaterialId().equals(0L)) ? asCusUserExt.getDefaultMaterialId() : tagDetail.getDefaultMaterialId();
//        tagDetail.setDefaultMaterialId(defaultMaterialId);
//
//        // 获取材质名称
//        AsTagMaterial tagMaterial = tagMaterialService.getById(defaultMaterialId);
//        tagDetail.setDefaultSdkType(tagMaterial.getSdkType());
//        tagDetail.setDefaultMaterialName(tagMaterial.getName());
//
//        // 判断是否是从切换材质过来的 默认查B32、B32R
//        AsPrinterMaterialConcentration tagMaterialConcentration = printerMaterialConcentrationService
//                .getOne(Wrappers.<AsPrinterMaterialConcentration>lambdaQuery()
//                        .eq(AsPrinterMaterialConcentration::getPrinterType, printerType)
//                        .eq(AsPrinterMaterialConcentration::getMaterialId, defaultMaterialId)
//                );
//
//        Integer defaultMaterialConcentration = (asCusUserExt != null && asCusUserExt.getDefaultConcentration() != null) ? asCusUserExt.getDefaultConcentration() : (
//                (tagMaterialConcentration != null && tagMaterialConcentration.getConcentration() != null) ? tagMaterialConcentration.getConcentration() : tagMaterial.getConcentration()
//                );
////        Integer defaultMaterialConcentration =
////                Optional.ofNullable(asCusUserExt).map(AsCusUserExt::getDefaultConcentration)
////                        .orElseGet(() -> Optional.ofNullable(tagMaterialConcentration)
////                                .map(AsPrinterMaterialConcentration::getConcentration).orElse(0));
//        // 设置默认浓度
//        tagDetail.setDefaultConcentration(defaultMaterialConcentration);
//
//        return tagDetail;
//    }

    /**
     * 获取用户的打印模板
     *
     * @return UserTagPrintDto
     */
    @Override
    public UserTagPrintDto getPrintTpl(Short printType, String printerName) {
        if (StrUtil.isBlank(printerName)) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }

        // 获取设备id
        AsAdminPrinter adminPrinter = adminPrinterService.getOne(Wrappers.<AsAdminPrinter>lambdaQuery()
                .eq(AsAdminPrinter::getModel, printerName));
        if (null == adminPrinter) {
            throw new BusinessException(SystemResultCode.EQUIPMENT_NOT_EXISTS);
        }
        Long printerId = adminPrinter.getId();

        // 获取用户当前机型下对应的默认模板
        AsAdminPrinterConcentration adminPrinterConcentration = printerConcentrationService.getOne(
                Wrappers.<AsAdminPrinterConcentration>lambdaQuery()
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getIsDefault, true)
                        .eq(AsAdminPrinterConcentration::getUserId, LoginUserThreadLocal.getCurrentUserId())
                        .orderByDesc(AsAdminPrinterConcentration::getUpdateTime), false);

        // 获取系统默认标签id
        Long defaultTagId = userTagService.getFirstTagId(printType, printerId);

        if (Objects.isNull(defaultTagId)) {
            throw new BusinessException(SystemResultCode.GENERIC_EXCEPTION, "打印配置中打印机型不支持该标签");
        }

        if (adminPrinterConcentration != null) {
            if (DictConstant.PRINT_TYPE_ASSET == printType) {
                UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultTagId());
                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                    defaultTagId = tagPrintDto.getId();
                }
            } else if (DictConstant.PRINT_TYPE_MATERIAL == printType) {
                UserTagPrintDto tagPrintDto = userTagService.getDetail(adminPrinterConcentration.getDefaultCftagId());
                if (ObjectUtil.isNotEmpty(tagPrintDto)) {
                    defaultTagId = tagPrintDto.getId();
                }
            }
        }

        // 获取标签详情数据
        UserTagPrintDto tagDetail = userTagService.getDetail(defaultTagId);

        //默认类型为 默认
        Integer defaultType = SYSTEM_TAG;
        if (SYSTEM_TAG.equals(tagDetail.getTagType())) {
            if (CUSTOMIZED_SIZE.equals(tagDetail.getType())) {
                //类型为 定制
                defaultType = CUSTOMIZED_TAG;
            }
        } else if (CUSTOM_TAG.equals(tagDetail.getTagType())) {
            //类型为 自定义
            defaultType = CUSTOM_TAG;
        }
        tagDetail.setDefaultType(defaultType);
        tagDetail.setDefaultSize(tagDetail.getSizeWide() + " x " + tagDetail.getSizeLong());

        //目前app只有这三种尺寸，不能等同于size_id，返回的是app头部默认选中的是哪一项
        // 获取app标签尺寸数据
        List<SizeDto> sizeList = tagService.appSizeList(printerName);
        if (CollUtil.isEmpty(sizeList)) {
            throw new BusinessException(SystemResultCode.TAG_SIZE_NOT_EXISTS);
        }

        HashMap<String, Long> sizeMap = new HashMap<>();
        sizeList.forEach(val -> {
            // 收集成map
            sizeMap.put(val.getSizeLong() + " x " + val.getSizeWide(), val.getId());
        });

        // 默认尺寸id
        tagDetail.setDefaultSizeId(sizeMap.getOrDefault(tagDetail.getDefaultSize(), 1L));

        // 获取默认材质id
        Long defaultMaterialId = printerConcentrationService.getFirstMaterialId(printerId);
        if (null == defaultMaterialId) {
            throw new BusinessException(SystemResultCode.PRINTER_CONCENTRATION_NOT_EXIST);
        }
        tagDetail.setDefaultMaterialId(defaultMaterialId);

        // 获取材质名称
        AsTagMaterial tagMaterial = tagMaterialService.getById(defaultMaterialId);
        tagDetail.setDefaultSdkType(tagMaterial.getSdkType());
        tagDetail.setDefaultMaterialName(tagMaterial.getName());
        tagDetail.setDefaultMaterialIsRfid(tagMaterial.getIsRfid());

        // 设置默认浓度
        Integer defaultConcentration = printerConcentrationService.getDefaultConcentration(printerId, defaultMaterialId);
        tagDetail.setDefaultConcentration(defaultConcentration);

        // 设置浓度范围
        AsAdminPrinterConcentration concentrationData = printerConcentrationService.getOne(
                Wrappers.lambdaQuery(AsAdminPrinterConcentration.class)
                        .select(AsAdminPrinterConcentration::getMinConcentration, AsAdminPrinterConcentration::getMaxConcentration)
                        .eq(AsAdminPrinterConcentration::getPrinterId, printerId)
                        .eq(AsAdminPrinterConcentration::getMaterialId, defaultMaterialId)
                        .eq(AsAdminPrinterConcentration::getUserId, 0L), false);

        if (null == concentrationData) {
            throw new BusinessException(SystemResultCode.PRINTER_CONCENTRATION_NOT_EXIST);
        }
        tagDetail.setMinConcentration(concentrationData.getMinConcentration());
        tagDetail.setMaxConcentration(concentrationData.getMaxConcentration());

        return tagDetail;
    }

}
