package com.niimbot.asset.means.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niimbot.asset.means.model.AsAdminPrinterConcentration;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 设备-材质-浓度关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface AsAdminPrinterConcentrationMapper extends BaseMapper<AsAdminPrinterConcentration> {

    @Select("SELECT material_id FROM as_admin_printer_concentration WHERE printer_id = #{printerId} AND user_id = 0 AND is_delete = 0 ORDER BY create_time ASC LIMIT 1")
    Long getFirstMaterialId(@Param("printerId") Long printerId);

    @Select("SELECT material_id FROM as_admin_printer_concentration WHERE printer_id = #{printerId} AND user_id = #{userId} AND is_default = 0 AND is_delete = 0 ORDER BY update_time DESC, id DESC LIMIT 1")
    Long getFirstUserMaterialId(@Param("userId") Long userId, @Param("printerId") Long printerId);

    @Select("SELECT default_concentration FROM as_admin_printer_concentration WHERE printer_id = #{printerId} AND material_id = #{materialId} and user_id = 0 AND is_delete = 0 ORDER BY create_time ASC LIMIT 1")
    Integer getDefaultConcentration(@Param("printerId") Long printerId, @Param("materialId") Long materialId);

    @Select("SELECT default_concentration FROM as_admin_printer_concentration WHERE printer_id = #{printerId} AND material_id = #{materialId} and user_id = #{userId} AND is_default = 0 AND is_delete = 0 ORDER BY update_time DESC, id DESC LIMIT 1")
    Integer getDefaultUserConcentration(@Param("userId") Long userId,@Param("printerId") Long printerId, @Param("materialId") Long materialId);
}
