<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niimbot.asset.means.adapter.local.mapper.BigScreenMapper">

    <select id="queryAssetCategory" resultType="map">
        select
          category_name as ele, count(1) as num
        from (
            select
                a.id, b.category_name
            from (select id, asset_category from as_asset where is_delete ='0') a
                left join (select id, category_name from as_category where is_delete ='0') b on a.asset_category = b.id
            where b.id is not null
        ) c
        group by ele
        order by num desc
    </select>

    <select id="queryAssetStatus" resultType="map">
        select
            name as ele, count(1) as num
        from (
            select
                a.id, b.name
            from (select id, status from as_asset where is_delete ='0') a
                left join (select * from as_asset_status where is_delete ='0') b on a.status = b.id
            where b.id is not null
        ) c
        group by ele
        order by num desc
    </select>

    <select id="queryStorageArea" resultType="map">
        select
            area_name as ele, count(1) as num
        from (
            select
                a.id, b.area_name
            from (select id, storage_area from as_asset where is_delete ='0') a
                left join (select id, area_name from as_area where is_delete ='0') b on a.storage_area = b.id
        ) c
        group by ele
        order by num desc
    </select>

    <select id="queryOrgOwner" resultType="map">
        select org_name as ele, count(1) as num from (
            select
                a.id, b.org_name
            from (select id, org_owner from as_asset where is_delete ='0') a
                left join (select id, org_name from as_org where is_delete ='0') b on a.org_owner = b.id
            where b.id is not null
        ) c
        group by ele
        order by num desc
    </select>

    <select id="queryUseOrg" resultType="map">
        select org_name as ele, count(1) as num from (
            select
                a.id, b.org_name
            from (select id, use_org from as_asset where is_delete ='0') a
                left join (select id, org_name from as_org where is_delete ='0') b on a.use_org = b.id
            where b.id is not null
        ) c
        group by ele
        order by num desc
    </select>

    <select id="queryBigScreenDataOneLeftBottom2" resultType="com.niimbot.local.bigscreen.ScreenRepairReportDto">
       select
          a.asset_name as assetName, c.org_name as orgName, b.action_name as actionName, date_format(b.handle_time, '%Y-%m-%d') as handleTime
         from
             (select * from as_asset where is_delete='0') a
             left join as_asset_log b on a.id=b.asset_id
             left join (select * from as_org where is_delete ='0') c on a.org_owner=c.id
        where
            b.id is not null
        order by b.handle_time desc
        limit 100
    </select>

    <select id="queryAssetNumberAndPriceGroupYear" resultType="map">
        select
          year, count(1) as num, round(sum(price), 2) as price
        from (
            select
                date_format(create_time, '%Y') as year,
                if(asset_data ->> '$.price' is null or asset_data ->> '$.price'='null', 0, asset_data ->> '$.price') as price
            from as_asset
            where is_delete ='0'
        ) a
        <where>
            <if test="yearList!=null and yearList.size()>0">
                year in
                <foreach collection="yearList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by year
        order by year desc
    </select>

    <select id="queryAssetMoneyGroupByStatus" resultType="map">
        select
          name, count(1) as value
        from(
            select
                a.id, b.name
            from
                (select * from as_asset where is_delete='0') a
                left join (select * from as_asset_status where is_delete='0') b on a.status = b.id
        ) c
        group by name
        order by value desc
    </select>

    <select id="queryAssetNumGroupByStatus" resultType="map">
        select
            name, round(sum(price)) as value
        from(
            select
                if(asset_data ->> '$.price' is null or asset_data ->> '$.price'='null', 0, asset_data ->> '$.price') as price,
                b.name
            from
                (select * from as_asset where is_delete ='0') a
                left join (select * from as_asset_status where is_delete='0') b on a.status = b.id
        ) c
        group by name
        order by value desc
    </select>

    <select id="queryAssetNumGroupByStorageArea" resultType="com.niimbot.local.bigscreen.BigScreenAssetDto">
        select area_name as ele, count(1) as num from(
            select
                b.area_name
            from (select * from as_asset where is_delete='0') a
                left join (select * from as_area where is_delete ='0') b on a.storage_area=b.id
        )z
        group by area_name
        order by num desc
        limit 100
    </select>

    <select id="queryAssetNumGroupByOrgOwner" resultType="com.niimbot.local.bigscreen.BigScreenAssetDto">
        select org_name as ele, count(1) as num from(
            select
                b.org_name
            from (select * from as_asset where is_delete='0') a
                left join (select * from as_org where is_delete ='0') b on a.org_owner=b.id
        )z
        group by org_name
        order by num desc
        limit 100
    </select>

    <select id="queryAssetNumGroupByAssetCategory" resultType="com.niimbot.local.bigscreen.BigScreenAssetDto">
       select category_name as ele, count(1) as num from(
            select
                b.category_name
            from (select * from as_asset where is_delete='0') a
                left join (select * from as_category where is_delete ='0') b on a.asset_category=b.id
        )z
        group by category_name
        order by num desc
        limit 100
    </select>

    <select id="queryScreenRepairReport" resultType="com.niimbot.local.bigscreen.ScreenRepairReportDto">
        select
            from_unixtime(a.repair_date/1000, '%Y-%m-%d') as repairDate, b.asset_snapshot_data ->>'$.storageAreaText' as storageAreaText
            , b.asset_snapshot_data ->>'$.orgOwnerText' as orgOwnerText, b.asset_snapshot_data ->>'$.assetName' as assetName
        from
            as_repair_report_order a
            left join as_repair_report_order_detail b on a.id=b.repair_report_order_id
        order by a.create_time desc
        limit 100
    </select>

    <select id="queryMaterialQuantityAndMoney" resultType="map">
        select
            sum(ifnull(b.current_quantity, 0)) as currentQuantity,
            sum(ifnull(b.total_money, 0)) as totalMoney
        from
            (select * from as_material where is_delete = 0) a
            left join as_material_stock b on a.company_id=b.company_id and a.id=b.material_id
    </select>

    <select id="queryMaterialCkNum" resultType="map">
        select
            ifnull(sum(ck_num), 0) as ckNum
        from
            as_material_ck_order_detail a
            LEFT JOIN (select * from as_material_ck_order where approve_status in(0, 3)) b on a.order_id =b.id
    </select>

    <select id="queryMaterialRkNum" resultType="map">
        select
            ifnull(sum(a.rk_num), 0) as rkNum
        from
            as_material_rk_order_detail a
            left join (select * from as_material_rk_order where approve_status IN ( 0, 3 )) b on a.order_id =b.id
    </select>

    <select id="queryQuantityGroupByRepo" resultType="com.niimbot.local.bigscreen.BigScreenAssetDto">
        select
	        c.name as ele, sum(ifnull(b.current_quantity, 0)) as num
        from
            (select * from as_repository where is_delete='0') c
            left join as_material_stock b on b.company_id=c.company_id and b.repository_id =c.id
            left join (select * from as_material where is_delete = 0)a on a.company_id=b.company_id and a.id=b.material_id
        group by c.name
        order by num desc
        limit 100
    </select>

    <select id="queryQuantityGroupByMaterialCategory" resultType="com.niimbot.local.bigscreen.BigScreenAssetDto">
        select
            c.category_name as ele, sum(ifnull(b.current_quantity, 0)) as num
        from
            (select * from as_material_category where is_delete=0) c
            left join as_material_stock b on b.company_id=c.company_id
            left join (select * from as_material where is_delete = 0) a on a.material_category =c.id
        group by c.category_name
        order by num desc
        limit 100
    </select>




</mapper>
