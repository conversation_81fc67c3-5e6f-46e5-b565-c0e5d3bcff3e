package com.niimbot.asset.means.adapter.local.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niimbot.asset.means.model.AsAdminAntiFakePrinter;
import com.niimbot.system.AntiFakeCheckDto;
import com.niimbot.system.AntiFakeResDto;
import com.niimbot.system.MachineDetailDto;
import com.niimbot.system.MachineDetailResDto;
import com.niimbot.system.PrintInfoDto;

/**
 * 碳带防伪service
 *
 * <AUTHOR>
 * @Date 2021/03/11
 */
public interface LocalAntiFakeService extends IService<AsAdminAntiFakePrinter> {

    /**
     * 判断碳带防伪
     *
     * @param antiFakeCheckDto 判断碳带防伪对象
     * @return AntiFakeResDto
     */
    AntiFakeResDto checkAntiFake(AntiFakeCheckDto antiFakeCheckDto);

    /**
     * 判断是否进行固件升级
     *
     * @param machineDetailDto 固件升级请求对象
     * @return Boolean
     */
    MachineDetailResDto machineCascadeDetail(MachineDetailDto machineDetailDto);

    /**
     * 将打印信息上传云打印服务
     *
     * @param printInfoDto 打印信息对象
     * @return Boolean
     */
    Boolean uploadPrintInfo(PrintInfoDto printInfoDto);
}
