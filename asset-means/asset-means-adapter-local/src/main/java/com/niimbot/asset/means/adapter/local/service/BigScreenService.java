package com.niimbot.asset.means.adapter.local.service;


import com.niimbot.local.bigscreen.ScreenRepairReportDto;

import java.util.List;
import java.util.Map;

public interface BigScreenService {

    List<Map<String, Object>> queryBigScreenDataOneLeftTop(String assetKind);

    Map<String, Object> queryAssetNumberAndPriceGroupYear();

    Map<String, Object> queryBigScreenDataTwoLeftTop();

    Map<String, Object> queryTwoAssetDistriStatis();

    List<ScreenRepairReportDto> queryBigScreenLeftBottom();

    List<ScreenRepairReportDto> queryBigScreenDataOneLeftBottom2();

    Map<String, Object> queryBigScreenDataTwoRight();

}
