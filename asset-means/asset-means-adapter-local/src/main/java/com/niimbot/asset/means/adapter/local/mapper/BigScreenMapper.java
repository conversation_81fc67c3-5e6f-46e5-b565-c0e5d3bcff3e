package com.niimbot.asset.means.adapter.local.mapper;


import com.niimbot.local.bigscreen.BigScreenAssetDto;
import com.niimbot.local.bigscreen.ScreenRepairReportDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface BigScreenMapper {

    List<Map<String, Object>> queryAssetCategory();

    List<Map<String, Object>> queryAssetStatus();

    List<Map<String, Object>> queryStorageArea();

    List<Map<String, Object>> queryOrgOwner();

    List<Map<String, Object>> queryUseOrg();

    List<ScreenRepairReportDto> queryBigScreenDataOneLeftBottom2();

    List<Map<String, Object>> queryAssetNumberAndPriceGroupYear(@Param("yearList") List<String> yearList);

    /**
     * 不同状态资产的金额分布统计
     *
     * @return
     */
    List<Map<String, Object>> queryAssetMoneyGroupByStatus();

    /**
     * 不同状态资产的数量分布统计
     *
     * @return
     */
    List<Map<String, Object>> queryAssetNumGroupByStatus();

    List<BigScreenAssetDto> queryAssetNumGroupByStorageArea();

    List<BigScreenAssetDto> queryAssetNumGroupByOrgOwner();

    List<BigScreenAssetDto> queryAssetNumGroupByAssetCategory();

    List<ScreenRepairReportDto> queryScreenRepairReport();

    Map<String, Object> queryMaterialQuantityAndMoney();

    Map<String, Object> queryMaterialCkNum();

    Map<String, Object> queryMaterialRkNum();

    List<BigScreenAssetDto> queryQuantityGroupByRepo();

    List<BigScreenAssetDto> queryQuantityGroupByMaterialCategory();

}
