package com.niimbot.asset.means.adapter.local.controller;


import com.niimbot.asset.means.adapter.local.service.BigScreenService;
import com.niimbot.local.bigscreen.ScreenRepairReportDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("server/means/bigScreen")
public class BigScreenCommonServiceController {

    @Autowired
    private BigScreenService bigScreenService;

    @PostMapping(value = "/queryBigScreenDataOneLeftTop")
    public Map<String, Object> queryBigScreenDataOneLeftTop(@RequestParam("assetKind") String assetKind) {
        List<Map<String, Object>> mapList = bigScreenService.queryBigScreenDataOneLeftTop(assetKind);
        String[] xAxisArray = new String[(mapList != null && mapList.size() > 0) ? mapList.size() : 0];
        Long[] yAxisArray = new Long[(mapList != null && mapList.size() > 0) ? mapList.size() : 0];
        if (mapList != null && mapList.size() > 0) {
            for (int i = 0; i < mapList.size(); i++) {
                xAxisArray[i] = (String) (mapList.get(i).get("ele"));
                yAxisArray[i] = (Long) (mapList.get(i).get("num"));
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("xAxisArray", xAxisArray);
        resultMap.put("yAxisArray", yAxisArray);
        return resultMap;
    }

    @PostMapping("/queryBigScreenDataOneLeftBottom1")
    public List<ScreenRepairReportDto> queryBigScreenDataOneLeftBottom1() {
        return bigScreenService.queryBigScreenLeftBottom();
    }

    @PostMapping(value = "/queryBigScreenDataOneLeftBottom2")
    public List<ScreenRepairReportDto> queryBigScreenDataOneLeftBottom2() {
        return bigScreenService.queryBigScreenDataOneLeftBottom2();
    }


    @PostMapping(value = "/queryBigScreenDataOneRight")
    public Map<String, Object> queryBigScreenDataOneRight() {
        return bigScreenService.queryAssetNumberAndPriceGroupYear();
    }

    /**
     * 获取大屏二的左上部分数据 不同状态资产的数量/金额分布统计：数量、金额 资产增长情况
     *
     * @return
     */
    @PostMapping(value = "/queryBigScreenDataTwoLeftTop")
    public Map<String, Object> queryBigScreenDataTwoLeftTop() {
        return bigScreenService.queryBigScreenDataTwoLeftTop();
    }

    /**
     * 获取大屏二的左下部分数据 1、资产分布统计：区域、部门、分类
     *
     * @return
     */
    @PostMapping(value = "/queryBigScreenDataTwoBottom")
    public Map<String, Object> queryBigScreenDataTwoBottom() {
        return bigScreenService.queryTwoAssetDistriStatis();
    }


    /**
     * 获取大屏二的右部分数据的接口 1、耗材库存统计（耗材、金额、出库数量、入库数量） 2、不同仓库的库存 3、不同耗材分类的库存
     *
     * @return
     */
    @PostMapping(value = "/queryBigScreenDataTwoRight")
    public Map<String, Object> queryBigScreenDataTwoRight() {
        return bigScreenService.queryBigScreenDataTwoRight();
    }

}
