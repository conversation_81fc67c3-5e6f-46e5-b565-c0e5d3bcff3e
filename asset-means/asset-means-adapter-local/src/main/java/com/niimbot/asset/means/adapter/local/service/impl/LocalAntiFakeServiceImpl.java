package com.niimbot.asset.means.adapter.local.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niimbot.asset.means.adapter.local.service.LocalAntiFakeService;
import com.niimbot.asset.means.mapper.AsAdminAntiFakePrinterMapper;
import com.niimbot.asset.means.model.AsAdminAntiFakePrinter;
import com.niimbot.system.AntiFakeCheckDto;
import com.niimbot.system.AntiFakeResDto;
import com.niimbot.system.AntiFakeUuidDto;
import com.niimbot.system.MachineDetailDto;
import com.niimbot.system.MachineDetailResDto;
import com.niimbot.system.PrintInfoDto;

import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2020/12/11
 */
@Service
@Slf4j
public class LocalAntiFakeServiceImpl extends ServiceImpl<AsAdminAntiFakePrinterMapper, AsAdminAntiFakePrinter> implements LocalAntiFakeService {

    /**
     * 碳带防伪判断
     *
     * @param antiFakeCheckDto 碳带防伪判断请求对象
     * @return Boolean
     */
    @Override
    public AntiFakeResDto checkAntiFake(AntiFakeCheckDto antiFakeCheckDto) {
        AntiFakeResDto antiFakeResDto = new AntiFakeResDto();
        antiFakeResDto.setUuidData(new AntiFakeUuidDto());
        antiFakeResDto.setIsCanPrint(true);
        return antiFakeResDto;
    }

    /**
     * 判断是否进行固件升级
     *
     * @param machineDetailDto 固件升级请求对象
     * @return Boolean
     */
    @Override
    public MachineDetailResDto machineCascadeDetail(MachineDetailDto machineDetailDto) {
        return new MachineDetailResDto();
    }

    /**
     * 将打印信息上传云打印服务
     *
     * @param printInfoDto 打印信息对象
     * @return Boolean
     */
    @Override
    public Boolean uploadPrintInfo(PrintInfoDto printInfoDto) {
        return true;
    }

}
