package com.niimbot.asset.means.adapter.local.service.impl;

import com.niimbot.asset.means.adapter.local.mapper.BigScreenMapper;
import com.niimbot.asset.means.adapter.local.service.BigScreenService;
import com.niimbot.local.bigscreen.AssetKindEnum;
import com.niimbot.local.bigscreen.BigScreenAssetDto;
import com.niimbot.local.bigscreen.ScreenRepairReportDto;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BigScreenServiceImpl implements BigScreenService {

    @Resource
    private BigScreenMapper bigScreenMapper;

    @Override
    public List<Map<String, Object>> queryBigScreenDataOneLeftTop(String assetKind) {
        List<Map<String, Object>> resultMap = new ArrayList<>();
        if (AssetKindEnum.ASSET_CATEGORY.getCode().equals(assetKind)) {
            resultMap = bigScreenMapper.queryAssetCategory();
        } else if (AssetKindEnum.ASSET_STATUS.getCode().equals(assetKind)) {
            resultMap = bigScreenMapper.queryAssetStatus();
        } else if (AssetKindEnum.STORAGE_AREA.getCode().equals(assetKind)) {
            resultMap = bigScreenMapper.queryStorageArea();
        } else if (AssetKindEnum.ORG_OWNER.getCode().equals(assetKind)) {
            resultMap = bigScreenMapper.queryOrgOwner();
        } else if (AssetKindEnum.USE_ORG.getCode().equals(assetKind)) {
            resultMap = bigScreenMapper.queryUseOrg();
        }

        return resultMap;
    }


    public Map<String, Object> moneyAndNumGroupByYear(Integer recentYearNum) {
        // 查询最近10年的资产数
        List<String> yearList = new ArrayList<>();
        int currYear = Calendar.getInstance().get(Calendar.YEAR);
        int tempYear = currYear;
        for (int i = 0; i < recentYearNum; i++) {
            yearList.add(String.valueOf(tempYear));
            tempYear--;
        }
        // 组装横、纵坐标
        List<Map<String, Object>> mapList = bigScreenMapper.queryAssetNumberAndPriceGroupYear(yearList);

        Map<String, Object> resultMap = new HashMap<>();
        String[] xAxisArray = new String[recentYearNum];
        Long[] yAxisArrayNum = new Long[recentYearNum];
        Double[] yAxisArrayMoney = new Double[recentYearNum];
        if (mapList != null && mapList.size() > 0) {
            for (int i = 0; i < recentYearNum; i++) {
                String addYearStr = String.valueOf(currYear - i);
                xAxisArray[i] = addYearStr;
                Map<String, Object> loopMap = searchSpecifiedData(mapList, addYearStr);
                yAxisArrayNum[i] = loopMap != null ? (Long) (loopMap.get("num")) : 0L;
                yAxisArrayMoney[i] = loopMap != null ? (Double) (loopMap.get("price")) : 0D;
            }
        }
        Map<String, Object> numMap = new HashMap<>();
        numMap.put("xAxisArray", xAxisArray);
        numMap.put("yAxisArray", yAxisArrayNum);
        resultMap.put("dataNum", numMap);

        Map<String, Object> moneyMap = new HashMap<>();
        moneyMap.put("xAxisArray", xAxisArray);
        moneyMap.put("yAxisArray", yAxisArrayMoney);
        resultMap.put("dataMoney", moneyMap);

        return resultMap;
    }

    private Map<String, Object> searchSpecifiedData(List<Map<String, Object>> mapList, String tempYear) {
        if (mapList != null && mapList.size() > 0) {
            for (Map<String, Object> tempMap : mapList) {
                if (tempMap.get("year").equals(tempYear)) {
                    return tempMap;
                }
            }
        }
        return null;
    }


    @Override
    public Map<String, Object> queryAssetNumberAndPriceGroupYear() {
        return moneyAndNumGroupByYear(10);
    }


    @Override
    public Map<String, Object> queryBigScreenDataTwoLeftTop() {
        List<Map<String, Object>> assetMoneyList = bigScreenMapper.queryAssetMoneyGroupByStatus();
        List<Map<String, Object>> assetNumList = bigScreenMapper.queryAssetNumGroupByStatus();
        Map<String, Object> resultMap = new HashMap<>();
        // 1、不同状态资产的金额/数量分布图的数据
        Map<String, Object> moneyAndNumMap = new HashMap<>();
        moneyAndNumMap.put("assetMoneyList", assetMoneyList);
        moneyAndNumMap.put("assetNumList", assetNumList);
        resultMap.put("moneyAndNumData", moneyAndNumMap);

        ///2、资产增长情况
        int recentYears = 3;
        List<String> yearList = new ArrayList<>();
        int currYear = Calendar.getInstance().get(Calendar.YEAR);
        int tempYear = currYear;
        for (int i = 0; i < recentYears; i++) {
            yearList.add(String.valueOf(tempYear));
            tempYear--;
        }
        // 组装横、纵坐标
        Map<String, Object> assetIncreaseMap = new HashMap<>();
        List<Map<String, Object>> mapList = bigScreenMapper.queryAssetNumberAndPriceGroupYear(yearList);
        // 年份、数量、金额
        String[] yearArray = new String[recentYears];
        Long[] numArray = new Long[recentYears];
        Double[] priceArray = new Double[recentYears];
        for (int i = 0; i < recentYears; i++) {
            String addYearStr = String.valueOf(currYear - i);
            yearArray[i] = addYearStr;
            Map<String, Object> loopMap = searchSpecifiedData(mapList, addYearStr);
            if (loopMap != null && addYearStr.equals(loopMap.get("year").toString())) {
                numArray[i] = (Long) (loopMap.get("num"));
                priceArray[i] = (Double) (loopMap.get("price"));
            } else {
                numArray[i] = 0L;
                priceArray[i] = 0D;
            }
        }
        assetIncreaseMap.put("yearArray", yearArray);
        assetIncreaseMap.put("numArray", numArray);
        assetIncreaseMap.put("priceArray", priceArray);
        resultMap.put("assetIncreaseData", assetIncreaseMap);

        return resultMap;
    }

    /**
     * 查询【大屏二】的【资产分布统计】 包括：区域、部门、分类
     *
     * @return
     */
    @Override
    public Map<String, Object> queryTwoAssetDistriStatis() {
        List<BigScreenAssetDto> storageAreaList = bigScreenMapper.queryAssetNumGroupByStorageArea();
        List<BigScreenAssetDto> orgOwnerList = bigScreenMapper.queryAssetNumGroupByOrgOwner();
        List<BigScreenAssetDto> assetCategoryList = bigScreenMapper.queryAssetNumGroupByAssetCategory();

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.putAll(convertListToXYAxis(storageAreaList, "storageAreaList"));
        resultMap.putAll(convertListToXYAxis(orgOwnerList, "orgOwnerList"));
        resultMap.putAll(convertListToXYAxis(assetCategoryList, "assetCategoryList"));
        return resultMap;
    }


    private Map<String, Object> convertListToXYAxis(List<BigScreenAssetDto> sourceList, String mapName) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> tempMap = new HashMap<>();
        if (sourceList != null && sourceList.size() > 0) {
            String[] xAxisArray = new String[sourceList.size()];
            Long[] yAxisArray = new Long[sourceList.size()];
            for (int i = 0; i < sourceList.size(); i++) {
                xAxisArray[i] = sourceList.get(i).getEle();
                yAxisArray[i] = sourceList.get(i).getNum();
            }
            tempMap.put("xAxisArray", xAxisArray);
            tempMap.put("yAxisArray", yAxisArray);
        }
        resultMap.put(mapName, tempMap);
        return resultMap;
    }

    @Override
    public List<ScreenRepairReportDto> queryBigScreenLeftBottom() {
        return bigScreenMapper.queryScreenRepairReport();
    }

    @Override
    public List<ScreenRepairReportDto> queryBigScreenDataOneLeftBottom2() {
        return bigScreenMapper.queryBigScreenDataOneLeftBottom2();
    }

    /**
     * 查询【大屏二】的右部分 1、耗材库存统计（耗材、金额、出库数量、入库数量） 2、不同仓库的库存
     *
     * @return
     */
    @Override
    public Map<String, Object> queryBigScreenDataTwoRight() {
        // 耗材库统计：耗材、金额
        Map<String, Object> materialQuantityAndMoneyMap = bigScreenMapper.queryMaterialQuantityAndMoney();
        Map<String, Object> ckMap = bigScreenMapper.queryMaterialCkNum();
        Map<String, Object> rkMap = bigScreenMapper.queryMaterialRkNum();
        // 返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.putAll(materialQuantityAndMoneyMap);
        resultMap.putAll(ckMap);
        resultMap.putAll(rkMap);

        // 不同仓库的库存
        List<BigScreenAssetDto> repoStockList = bigScreenMapper.queryQuantityGroupByRepo();
        resultMap.putAll(convertListToXYAxis(repoStockList, "repoStockList"));

        // 不同耗材分类的库存
        List<BigScreenAssetDto> materCateList = bigScreenMapper.queryQuantityGroupByMaterialCategory();
        resultMap.putAll(convertListToXYAxis(materCateList, "materCateList"));

        return resultMap;
    }

}
