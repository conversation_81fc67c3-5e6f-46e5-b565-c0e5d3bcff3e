package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 销售订单支付结果信息
 *
 * <AUTHOR>
 * @date 2021/8/13 17:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SaleOrderPaidDto", description = "销售订单支付结果信息")
public class SaleOrderPaidDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "支付是否成功")
    private Boolean isSuccess;

    @ApiModelProperty(value = "订单id")
    private Long id;

    @ApiModelProperty(value = "单据编号：字母前缀ON+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "商品摘要")
    private String summary;
}
