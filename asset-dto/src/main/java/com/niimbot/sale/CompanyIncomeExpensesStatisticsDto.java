package com.niimbot.sale;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 企业收支明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CompanyIncomeExpensesStatisticsDto对象", description = "企业收支统计")
public class CompanyIncomeExpensesStatisticsDto implements Serializable {

    @ApiModelProperty(value = "公司Id")
    private Long companyId;

    @ApiModelProperty(value = "购入")
    private BigDecimal buy = BigDecimal.ZERO;

    @ApiModelProperty(value = "调账（转入）")
    private BigDecimal adjustIn = BigDecimal.ZERO;

    @ApiModelProperty(value = "资产使用费")
    private BigDecimal use = BigDecimal.ZERO;

    @ApiModelProperty(value = "调账（转出）")
    private BigDecimal adjustOut = BigDecimal.ZERO;

}
