package com.niimbot.sale;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/6 上午9:50
 */
@Data
@Accessors(chain = true)
public class DiscountConditionItem implements Serializable {

    private static final long serialVersionUID = -5629917187751127162L;

    /**
     * 购买年数
     */
    private Integer purchaseYears;

    /**
     * 赠送时长
     */
    private Integer bonusMonth;

}
