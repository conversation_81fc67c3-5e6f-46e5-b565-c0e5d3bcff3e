package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="InvoiceProjectDto对象", description="开票项目")
public class InvoiceProjectDto implements Serializable {
    private static final long serialVersionUID = 6028187163735651701L;

    @ApiModelProperty(value = "开票项目")
    private String name;
}
