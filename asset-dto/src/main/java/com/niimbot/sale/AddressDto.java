package com.niimbot.sale;

import com.niimbot.validate.group.Insert;
import com.niimbot.validate.group.Update;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/12/28 17:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AddressDto对象", description = "收货地址")
public class AddressDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = {Update.class})
    private Long id;

    @ApiModelProperty(value = "收件人名称")
    @NotBlank(message = "收件人名称请输入1-50个字", groups = {Insert.class, Update.class})
    @Size(min = 1, max = 50, message = "收件人名称请输入1-50个字", groups = {Insert.class, Update.class})
    private String name;

    @ApiModelProperty(value = "联系方式")
    @NotBlank(message = "联系方式请输入1-20个字", groups = {Insert.class, Update.class})
    @Size(min = 1, max = 20, message = "联系方式请输入1-20个字", groups = {Insert.class, Update.class})
    private String phone;

    @ApiModelProperty(value = "收件地址")
    @NotBlank(message = "收件地址请输入1-200个字", groups = {Insert.class, Update.class})
    @Size(min = 1, max = 200, message = "收件地址请输入1-200个字", groups = {Insert.class, Update.class})
    private String address;

    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;


}
