package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 企业收支明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CompanyIncomeExpensesDto对象", description = "企业收支明细")
public class CompanyIncomeExpensesDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "收支类型：1-收入，2-支出")
    private Integer type;

    @DictConvert(value = "income_expenses_type", refField = "type")
    private String typeText;

    @ApiModelProperty(value = "业务类型：1-购入，2-调账（转入），3-资产使用费，4-调账（转出）")
    private Integer bizType;

    @DictConvert(value = "income_expenses_biz_type", refField = "bizType")
    private String bizTypeText;

    @ApiModelProperty(value = "业务描述")
    private String bizDesc;

    @ApiModelProperty(value = "交易时间")
    private LocalDateTime tradeTime;

    @ApiModelProperty(value = "交易金额（收入/支出）")
    private BigDecimal tradeAmount;

    @ApiModelProperty(value = "账户余额")
    private BigDecimal balance = BigDecimal.ZERO;

    @ApiModelProperty(value = "欠费金额")
    private BigDecimal arrears = BigDecimal.ZERO;

    @ApiModelProperty(value = "可用余额")
    private BigDecimal availableBalance;

    public BigDecimal getAvailableBalance() {
        return this.balance.subtract(arrears);
    }

    @ApiModelProperty(value = "关联订单id")
    private Long saleOrderId;

    @ApiModelProperty(value = "关联订单编号")
    private String saleOrderNo;

}
