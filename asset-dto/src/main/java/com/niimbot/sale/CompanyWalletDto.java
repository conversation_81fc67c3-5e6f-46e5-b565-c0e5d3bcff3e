package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/12/29 10:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CompanyWalletDto对象", description = "企业钱包")
public class CompanyWalletDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "企业状态")
    private Integer status;

    @DictConvert(value = "company_status", refField = "status")
    private String statusText;

    @ApiModelProperty(value = "余额")
    private BigDecimal balance = BigDecimal.ZERO;

    @ApiModelProperty(value = "欠费")
    private BigDecimal arrears = BigDecimal.ZERO;

}
