package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/3/22 10:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AddressInfoDto对象", description = "邮寄地址信息")
public class AddressInfoDto {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收件地址id")
    private Long id;

    @ApiModelProperty(value = "收件人名称")
    private String name;

    @ApiModelProperty(value = "收件电话")
    private String phone;

    @ApiModelProperty(value = "收件地址")
    private String address;

    @ApiModelProperty("收件人邮箱")
    private String email;
}
