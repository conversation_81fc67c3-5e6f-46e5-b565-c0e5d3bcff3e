package com.niimbot.sale;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售单商品条目
 *
 * <AUTHOR>
 * @date 2021/9/23 14:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@ApiModel(value = "SaleOrderItemDto", description = "销售单商品条目")
public class SaleOrderItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "明细id")
    private Long id;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品规格")
    private String model;

    @ApiModelProperty(value = "sku编码")
    private String sku;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ApiModelProperty(value = "原始单价")
    private BigDecimal originalMoney;

    @ApiModelProperty(value = "优惠信息")
    private String discountInfo;

    @ApiModelProperty(value = "销售单价")
    private BigDecimal saleMoney;

    @ApiModelProperty(value = "原始总价")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "商品类型 1-软件商品 2-硬件商品")
    private Integer goodsType;

    @ApiModelProperty(value = "商品类型: 1-服务版本, 2-资产加油包")
    private Integer type;
}
