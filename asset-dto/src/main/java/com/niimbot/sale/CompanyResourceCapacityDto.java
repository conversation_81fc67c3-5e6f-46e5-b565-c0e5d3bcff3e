package com.niimbot.sale;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/12/29 10:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CompanyResourceCapacityDto对象", description = "企业资源容量")
public class CompanyResourceCapacityDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "剩余容量")
    private Integer remainder;

    @ApiModelProperty(value = "已用容量")
    private Integer hasUsed;

    @ApiModelProperty(value = "总容量")
    private Integer capacity;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expirationTime;

}
