package com.niimbot.sale;

import com.niimbot.page.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售订单分页查询对象
 *
 * <AUTHOR>
 * @date 2021/8/2 17:33
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SaleOrderPageQueryDto", description = "销售订单分页查询对象")
public class SaleOrderPageQueryDto extends Pageable implements Serializable {

    @ApiModelProperty(value = "模糊搜索[订单号，订单摘要]")
    private String kw;

    @ApiModelProperty(value = "下单时间范围查询 [yyyy-MM-dd]")
    private List<String> createTime;

    @ApiModelProperty(value = "支付时间范围查询 [yyyy-MM-dd]")
    private List<String> operateDate;

    @ApiModelProperty(value = "支付方式: 1-支付宝, 2-微信, 3-对公转账")
    private Integer payType;

    @ApiModelProperty(value = "支付状态: 1-待付款, 2-已完成, 3-已关闭")
    private Integer status;

    @ApiModelProperty(value = "开票状态:0-无需开票，1-待开票，2-开票中，3-已开票")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "订单类型:1-服务版本, 2-资产加油包，5-资源包")
    private Integer orderType;

    @ApiModelProperty(value = "企业信息id")
    private Long companyId;

    @ApiModelProperty(value = "购买资源包数量")
    private Integer quantity;

    private List<BigDecimal> payMoney;
    private List<String> expirationTime;
    private List<Integer> buyTime;

    @ApiModelProperty("0-新购应用，1-扩容应用人数，2-续期应用时间，3-变更版本，4-内购硬件")
    private Integer productType;
}