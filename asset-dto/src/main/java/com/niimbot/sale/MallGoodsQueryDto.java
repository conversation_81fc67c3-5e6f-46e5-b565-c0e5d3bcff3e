package com.niimbot.sale;

import com.niimbot.page.Pageable;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MallGoodsQueryDto对象", description="MallGoodsQueryDto商品列表")
public class MallGoodsQueryDto extends Pageable implements Serializable {
    private static final long serialVersionUID = 1L;

}
