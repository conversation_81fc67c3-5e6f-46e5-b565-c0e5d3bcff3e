package com.niimbot.sale;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AsMallGoods对象", description="商品详情")
public class MallGoodsPageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    private Long id;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品副标题")
    private String subtitle;

    @ApiModelProperty(value = "商品图地址")
    private List<JSONObject> goodsUrl;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "SKU信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> sku;

    @ApiModelProperty(value = "商品特点")
    private List<String> goodsTrait;

    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

}
