package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;
import com.niimbot.asset.framework.annotation.IdToNameConvert;
import com.niimbot.asset.framework.constant.RefFieldType;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/3/21 18:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InvoiceRecordDetailDto", description = "开票记录")
public class InvoiceRecordDetailDto extends InvoiceRecordDto implements Serializable {
    private static final long serialVersionUID = -1315822421482730714L;

    @ApiModelProperty(value = "销售单")
    @DictConvert
    private List<SaleOrderDto> saleOrders;

    @ApiModelProperty(value = "发票抬头信息")
    private AsInvoiceInfoDto invoiceInfo;

    @ApiModelProperty(value = "收件地址信息")
    private AddressInfoDto addressInfo;

    @ApiModelProperty(value = "发票项目")
    private String invoiceProject;

    @ApiModelProperty(value = "发票介质: 1-纸质发票")
    private Integer invoiceMedia;

    @DictConvert(value = "invoice_media", refField = "invoiceMedia")
    private String invoiceMediaText;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "物流单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司")
    private Integer expressCompany;

    @DictConvert(value = "express_company", refField = "expressCompany")
    private String expressCompanyText;

    @ApiModelProperty(value = "发票图片地址")
    private String picture;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @IdToNameConvert(refField = "createBy", type = RefFieldType.USER)
    private String createUserText;

    private String createByText;

    public String getCreateByText() {
        return getCreateUserText();
    }

    @ApiModelProperty(value = "创建者手机号")
    private String createPhone;
}
