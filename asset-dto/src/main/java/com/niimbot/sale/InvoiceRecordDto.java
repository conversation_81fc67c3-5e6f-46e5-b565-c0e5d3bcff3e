package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/3/21 18:56
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InvoiceRecordDto", description = "开票记录")
public class InvoiceRecordDto implements Serializable {
    private static final long serialVersionUID = -1315822421482730714L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "开票状态：1-申请中，2-开票失败，3-已完成")
    private Integer status;

    @DictConvert(value = "invoice_status", refField = "status")
    private String statusText;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceMoney;

    @ApiModelProperty(value = "发票类型(虚拟列)：1-个人；2-普通；3：增值税")
    private Integer invoiceType;

    @DictConvert(value = "invoice_type", refField = "invoiceType")
    private String invoiceTypeText;

    @ApiModelProperty(value = "发票抬头(虚拟列)")
    private String invoiceTitle;

    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
