package com.niimbot.sale;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * Banner图详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AsBanner对象", description="Banner图详情")
public class BannerPageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    private Long id;

    @ApiModelProperty(value = "banner图跳转地址")
    private String jumpUrl;

    @ApiModelProperty(value = "banner名称")
    private String bannerName;

    @ApiModelProperty(value = "banner图地址")
    private String bannerUrl;

    @ApiModelProperty(value = "banner打开方式")
    private String bannerOpenWay;


}
