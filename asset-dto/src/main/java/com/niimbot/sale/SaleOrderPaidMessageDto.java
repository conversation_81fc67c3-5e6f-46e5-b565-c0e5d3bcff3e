package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 销售单支付消息
 *
 * <AUTHOR>
 * @date 2021/8/16 15:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SaleOrderPaidMessageDto对象", description="销售单支付消息")
public class SaleOrderPaidMessageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "支付状态")
    private String status;

    @ApiModelProperty(value = "单据编号")
    private String orderNo;

    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

    @ApiModelProperty(value = "支付方式")
    private Integer payType;
}
