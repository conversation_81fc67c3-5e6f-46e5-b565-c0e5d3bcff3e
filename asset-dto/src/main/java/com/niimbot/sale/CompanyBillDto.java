package com.niimbot.sale;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/12/30 16:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CompanyBillDto对象", description = "企业账单信息")
public class CompanyBillDto implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "统计周期开始日期")
    private LocalDate statisticalStartTime;

    @ApiModelProperty(value = "统计周期结束日期")
    private LocalDate statisticalEndTime;

    @ApiModelProperty(value = "期初额")
    private BigDecimal initialAmount;

    @ApiModelProperty(value = "总收入")
    private BigDecimal incomeAmount;

    @ApiModelProperty(value = "总支出")
    private BigDecimal expensesAmount;

    @ApiModelProperty(value = "结余")
    private BigDecimal balanceAmount;

    @ApiModelProperty(value = "实付")
    private BigDecimal actuallyPayAmount;

    @ApiModelProperty(value = "待付")
    private BigDecimal waitPayAmount;

}

