package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;
import com.niimbot.asset.framework.annotation.IdToNameConvert;
import com.niimbot.asset.framework.constant.RefFieldType;

import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售订单
 *
 * <AUTHOR>
 * @date 2021/8/2 16:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "SaleOrderDetailDto", description = "销售订单详情")
public class SaleOrderDetailDto extends SaleOrderDto {
    private static final long serialVersionUID = 6768400582485584230L;

    @ApiModelProperty(value = "操作时间(支付/关闭)")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "订单来源: 1-web, 2-android, 3-iOS")
    private Integer source;

    @DictConvert(value = "order_source", refField = "source")
    private String sourceText;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @IdToNameConvert(refField = "createBy", type = RefFieldType.USER)
    private String createUserText;

    private String createByText;

    public String getCreateByText() {
        return getCreateUserText();
    }

    @ApiModelProperty(value = "用户手机")
    private String mobile;

    @ApiModelProperty(value = "发票信息")
    @DictConvert
    private AsInvoiceInfoDto invoiceInfo;

    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

    @ApiModelProperty(value = "用户备注")
    private String customerRemark;

    @ApiModelProperty(value = "商品信息")
    private List<SaleOrderItemDto> items;
}
