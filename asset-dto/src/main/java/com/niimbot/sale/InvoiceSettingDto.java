package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/21 17:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "InvoiceSettingDto对象", description = "开票设置")
public class InvoiceSettingDto implements  Serializable {
    @ApiModelProperty(value = "增值税发表最低金额")
    private BigDecimal vatInvoiceMinMoney;

    @ApiModelProperty(value = "开票项目")
    private List<InvoiceProjectDto> invoiceProjects;
}
