package com.niimbot.sale;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/12/8 下午4:07
 */
@Data
public class ResourceSaleOrderDto implements Serializable {

    private static final long serialVersionUID = 4878682100237481298L;

    @ApiModelProperty(value = "销售单ID")
    private Long id;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "单据编号：字母前缀ON+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "商品摘要")
    private String summary;

    @ApiModelProperty(value = "订单来源: 1-web, 2-android, 3-iOS")
    private Integer source;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "订单支付金额")
    private BigDecimal payMoney;

    @ApiModelProperty(value = "抵扣金额")
    private BigDecimal discountMoney;

    @ApiModelProperty(value = "支付方式: 1-支付宝, 2-微信, 3-对公转账")
    private Integer payType;

    @ApiModelProperty(value = "支付过期时间")
    private LocalDateTime expiredTime;

    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

    @ApiModelProperty(value = "收款账号")
    private String receivePaymentNo;

    @ApiModelProperty(value = "操作时间(支付/关闭)")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "支付状态: 1-待付款, 2-已完成, 3-已关闭")
    private Integer status;

    @ApiModelProperty(value = "开票状态:0-无需开票，1-待开票，2-开票中，3-已开票")
    private Integer invoiceStatus;

    @ApiModelProperty("中台订单同步状态 1-无需同步 2-同步中 3-同步成功 4-同步失败 5-待同步")
    private Integer middlendSyncStatus;

    @ApiModelProperty(value = "用户手机")
    private String mobile;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "用户备注")
    private String customerRemark;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "购买资源包数量")
    private Integer quantity;
}
