package com.niimbot.sale;

import com.niimbot.page.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/22 10:04
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "InvoiceRecordQueryDto", description = "开票记录分页查询对象")
public class InvoiceRecordQueryDto extends Pageable implements Serializable {
    @ApiModelProperty(value = "模糊搜索[发票号码, 物流单号]")
    private String kw;

    @ApiModelProperty(value = "开票状态：1-申请中，2-开票失败，3-已完成")
    private Integer status;

    @ApiModelProperty(value = "申请开票时间范围查询 [yyyy-MM-dd]")
    private List<String> createTime;

    @ApiModelProperty(value = "发票类型(虚拟列)：1-个人；2-普通；3：增值税")
    private Integer invoiceType;

    @ApiModelProperty("发票介质: 1-纸质；2-电子")
    private Integer invoiceMedia;

}
