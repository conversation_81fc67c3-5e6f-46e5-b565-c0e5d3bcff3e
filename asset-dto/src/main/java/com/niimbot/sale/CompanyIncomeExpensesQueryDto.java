package com.niimbot.sale;

import com.niimbot.page.Pageable;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 企业收支
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CompanyIncomeExpensesQueryDto对象", description = "企业收支明细查询")
public class CompanyIncomeExpensesQueryDto extends Pageable implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收支类型：1-收入，2-支出")
    private Integer type;

    @ApiModelProperty(value = "业务类型：1-购入，2-调账（转入），3-资产使用费，4-调账（转出）")
    private Integer bizType;

    @ApiModelProperty(value = "业务描述")
    private String bizDesc;

    @ApiModelProperty(value = "交易时间范围查询 [yyyy-MM-dd]")
    private List<String> tradeTime;
}
