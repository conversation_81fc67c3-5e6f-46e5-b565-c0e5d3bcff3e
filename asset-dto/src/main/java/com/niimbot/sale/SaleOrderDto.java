package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售订单
 *
 * <AUTHOR>
 * @date 2021/8/2 16:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@ApiModel(value = "SaleOrderDto对象", description = "销售订单信息")
public class SaleOrderDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售单ID")
    private Long id;

    @ApiModelProperty(value = "单据编号：字母前缀ON+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "商品摘要")
    private String summary;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "订单支付金额")
    private BigDecimal payMoney;

    @ApiModelProperty(value = "抵扣金额")
    private BigDecimal discountMoney;

    @ApiModelProperty(value = "支付状态: 1-待付款, 2-已完成, 3-已关闭")
    private Integer status;

    @DictConvert(value = "pay_status", refField = "status")
    private String statusText;

    @ApiModelProperty(value = "支付方式: 1-支付宝, 2-微信, 3-对公转账")
    private Integer payType;

    @DictConvert(value = "pay_type", refField = "payType")
    private String payTypeText;

    @ApiModelProperty(value = "开票状态:0-无需开票，1-待开票，2-开票中，3-已开票")
    private Integer invoiceStatus;

    @DictConvert(value = "sale_order_invoice_status", refField = "invoiceStatus")
    private String invoiceStatusText;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "订购时长（月）")
    private Integer buyTime;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expirationTime;

    @ApiModelProperty(value = "支付链接")
    private String payUrl;
}
