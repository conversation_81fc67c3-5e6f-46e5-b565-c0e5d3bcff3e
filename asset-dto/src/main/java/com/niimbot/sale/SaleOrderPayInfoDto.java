package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 销售订单支付信息
 *
 * <AUTHOR>
 * @date 2021/8/13 17:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleOrderPayInfoDto", description = "销售订单支付信息")
public class SaleOrderPayInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单id")
    private Long id;

    @ApiModelProperty(value = "单据编号：字母前缀ON+年月日时分秒+时间戳的微秒数小数点后半部分的前四位")
    private String orderNo;

    @ApiModelProperty(value = "支付超时时间: 单位: 分钟")
    private Integer timeoutExpress;

    @ApiModelProperty(value = "二维码地址")
    private String codeUrl;

    @ApiModelProperty(value = "开户行")
    private String bankName;

    @ApiModelProperty(value = "开户名")
    private String userName;

    @ApiModelProperty(value = "账号")
    private String bankNo;

    @ApiModelProperty(value = "客服电话")
    private String tel;
}
