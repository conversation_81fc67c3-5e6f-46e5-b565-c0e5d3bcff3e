package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/1 下午2:07
 */
@Data
@ApiModel(value = "DiscountConfigDto对象", description = "优惠策略配置")
public class DiscountConfigDto implements Serializable {

    private static final long serialVersionUID = 1044288022957812626L;

    @ApiModelProperty(value = "优惠策略配置主键")
    private Long id;

    @ApiModelProperty(value = "优惠策略编码")
    private String bizCode;

    @ApiModelProperty("优惠策略名称")
    private String discountName;

    @ApiModelProperty("优惠条件")
    private List<DiscountConditionItem> bonusCondition;

    @ApiModelProperty("适用资源包列表")
    private List<String> resourceCodeList;

    @ApiModelProperty("生效开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("生效结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("优惠对象类型")
    private Integer objectType;

    @ApiModelProperty("优惠对象")
    private DiscountObjectDto bonusObject;

    @ApiModelProperty("创建者")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
