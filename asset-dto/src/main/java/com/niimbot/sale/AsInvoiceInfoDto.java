package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;
import com.niimbot.validate.GroupValidate;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 发票信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AsInvoiceDto对象", description="发票信息")
public class AsInvoiceInfoDto implements GroupValidate, Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = {Update.class})
    private Long id;

    @ApiModelProperty(value = "发票类型：1-个人；2-普通；3：增值税")
    @NotNull(message = "发票类型不能为空", groups = {Save.class, Update.class})
    private Integer type;

    @DictConvert(value = "invoice_type", refField = "type")
    private String typeText;

    @ApiModelProperty(value = "发票抬头")
    @NotBlank(message = "发票抬头请输入1-100个字", groups = {Save.class, Update.class})
    @Size(min = 1, max = 100, message = "发票抬头请输入1-100个字", groups = {Save.class, Update.class})
    private String title;

    @ApiModelProperty(value = "税号")
    @NotBlank(message = "税号请输入1-100个字", groups = {Ordinary.class, Vat.class})
    @Size(min = 1, max = 100, message = "税号请输入1-100个字", groups = {Ordinary.class, Vat.class})
    private String taxNum;

    @ApiModelProperty(value = "地址")
    @NotBlank(message = "地址请输入1-200个字", groups = {Vat.class})
    @Size(min = 1, max = 200, message = "地址请输入1-200个字", groups = {Vat.class})
    private String address;

    @ApiModelProperty(value = "电话")
    @NotBlank(message = "电话请输入1-20个字", groups = {Vat.class})
    @Size(min = 1, max = 20, message = "电话请输入1-20个字", groups = {Vat.class})
    private String phone;

    @ApiModelProperty(value = "开户行")
    @NotBlank(message = "开户行请输入1-100个字", groups = {Vat.class})
    @Size(min = 1, max = 100, message = "开户行请输入1-100个字", groups = {Vat.class})
    private String bank;

    @ApiModelProperty(value = "银行账号")
    @NotBlank(message = "银行账号请输入1-100个字", groups = {Vat.class})
    @Size(min = 1, max = 100, message = "银行账号请输入1-100个字", groups = {Vat.class})
    private String bankAccount;

    /**
     * 普通
     */
    public interface Ordinary {
    }

    /**
     * 增值税
     */
    public interface Vat {
    }
}
