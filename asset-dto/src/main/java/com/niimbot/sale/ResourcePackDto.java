package com.niimbot.sale;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/12/5 14:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ResourcePackDto", description = "资源包")
public class ResourcePackDto {

    @ApiModelProperty(value = "资源包id")
    private Long id;

    @ApiModelProperty(value = "资源包编码")
    private String resourceCode;

    @ApiModelProperty(value = "资源包名称")
    private String resourceName;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "资源容量")
    private Integer capacity;

    @ApiModelProperty(value = "资源包单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "折算年价")
    private BigDecimal annualPrice;

    @ApiModelProperty(value = "是否可购买")
    private Boolean canBuy = true;

    @ApiModelProperty(value = "折扣信息")
    private List<DiscountConfigDto> discountInfo;

}
