package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/21 18:56
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InvoiceRecordApplyDto", description = "开票申请")
public class InvoiceRecordApplyDto implements Serializable {
    private static final long serialVersionUID = -1315822421482730714L;

    @ApiModelProperty(value = "发票抬头信息")
    @NotNull(message = "发票抬头不能为空")
    private Long invoiceInfo;

    @ApiModelProperty(value = "收件地址信息")
    @NotNull(message = "收件地址不能为空")
    private Long addressInfo;

    @ApiModelProperty(value = "销售单")
    @NotEmpty(message = "销售单列表不能为空")
    private List<Long> saleOrders;

    @ApiModelProperty(value = "发票项目")
    @NotBlank(message = "发票项目不能为空")
    private String invoiceProject;

    @ApiModelProperty(value = "发票介质: 1-纸质发票")
    @NotNull(message = "发票介质不能为空")
    private Integer invoiceMedia;

    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注最多输入500个字")
    private String remark;
}
