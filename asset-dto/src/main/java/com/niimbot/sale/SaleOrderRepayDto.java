package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 销售单重新支付
 *
 * <AUTHOR>
 * @date 2021/8/13 18:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SaleOrderRepayDto", description = "销售单重新支付")
public class SaleOrderRepayDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Long id;

    @ApiModelProperty(value = "支付方式: 1-支付宝, 2-微信, 3-对公转账")
    @NotNull(message = "支付方式")
    private Integer payType;
}
