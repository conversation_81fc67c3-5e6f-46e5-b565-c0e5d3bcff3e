package com.niimbot.sale;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品套餐详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PackageGoodsPageDto对象", description="商品套餐详情")
@TableName(autoResultMap = true)
public class PackageGoodsPageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    private Long id;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "套餐副标题")
    private String subtitle;

    @ApiModelProperty(value = "SKU信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> sku;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "适用企业描述")
    private String applyDescribe;

    @ApiModelProperty(value = "适用员工数")
    private String applyEmployee;

    @ApiModelProperty(value = "适用资产数")
    private String applyAsset;

    @ApiModelProperty(value = "营销文案")
    private String marketingPlan;

    @ApiModelProperty(value = "商品图片")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> goodsUrl;

    @ApiModelProperty(value = "商品主图")
    private String goodsMainUrl;

    @ApiModelProperty(value = "商品特点")
    private List<String> goodsTrait;

    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

    @ApiModelProperty(value = "套餐图地址")
    private String packageUrl;

}
