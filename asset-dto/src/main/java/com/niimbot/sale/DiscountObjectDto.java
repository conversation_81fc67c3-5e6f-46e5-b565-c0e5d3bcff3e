package com.niimbot.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6 上午10:03
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DiscountObjectDto", description = "优惠对象")
public class DiscountObjectDto implements Serializable {

    private static final long serialVersionUID = 8780017680459129906L;

    @ApiModelProperty(value = "付费状态")
    private List<Integer> paymentStatus;

    @ApiModelProperty(value = "企业规模")
    private List<DiscountCompanyItem> enterpriseSize;

    @ApiModelProperty(value = "运营标签")
    private List<String> companyTag;

    @ApiModelProperty(value = "指定客户")
    private List<String> specificCustomer;

    @ApiModelProperty(value = "指定客户名称")
    private List<String> specificCustomerName;
}
