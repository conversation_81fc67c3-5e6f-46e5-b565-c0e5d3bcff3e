package com.niimbot.sale;

import java.time.LocalDateTime;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/8 16:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CompanyResourceDto对象", description = "企业资源容量记录")
public class CompanyResourceDto {

    @ApiModelProperty(value = "企业资源ID")
    private Long id;

    @ApiModelProperty(value = "资源包名称")
    private String resourceName;

    @ApiModelProperty(value = "资源容量")
    private Integer capacity;

    @ApiModelProperty(value = "购买时长")
    private Integer buyTime;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expirationTime;

    @ApiModelProperty(value = "是否时间")
    private Boolean hasExpire;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveTime;

    public Boolean getHasExpire() {
        return !currentValid();
    }

    private Boolean currentValid() {
        return ObjectUtil.isNotNull(effectiveTime) && effectiveTime.compareTo(LocalDateTime.now()) <= 0
                && ObjectUtil.isNotNull(expirationTime) && expirationTime.compareTo(LocalDateTime.now()) >= 0;
    }

}
