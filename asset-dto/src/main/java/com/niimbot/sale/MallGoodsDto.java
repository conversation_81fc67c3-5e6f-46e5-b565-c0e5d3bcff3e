package com.niimbot.sale;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/10 下午3:47
 */
@Data
@ApiModel(value="MallGoodsDto对象", description="商品详情")
public class MallGoodsDto implements Serializable {

    private static final long serialVersionUID = -3361372019394890857L;

    @ApiModelProperty(value = "Id")
    private Long id;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品副标题")
    private String subtitle;

    @ApiModelProperty(value = "商品图地址")
    private List<JSONObject> goodsUrl;

    @ApiModelProperty(value = "SKU信息")
    private List<JSONObject> sku;

    @ApiModelProperty(value = "SKU编码")
    private String skuCode;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal goodsUnitPrice;

    @ApiModelProperty(value = "商品规格")
    private String goodsModel;

    @ApiModelProperty(value = "商品计价单位")
    private String goodsChargeUnit;

    @ApiModelProperty(value = "商品特点", required = true)
    private List<String> goodsTrait;

    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    @ApiModelProperty(value = "是否上架 0-上架  1-下架")
    private Integer status;

    @ApiModelProperty(value = "是否软删除 0-否  1-是")
    private Integer isDelete;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
