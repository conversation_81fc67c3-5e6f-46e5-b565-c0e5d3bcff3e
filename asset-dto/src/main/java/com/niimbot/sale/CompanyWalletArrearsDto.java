package com.niimbot.sale;

import com.niimbot.asset.framework.annotation.DictConvert;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/12/29 10:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CompanyWalletArrearsDto对象", description = "企业钱包欠费")
public class CompanyWalletArrearsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "欠费")
    private BigDecimal arrears = BigDecimal.ZERO;

    @ApiModelProperty(value = "收支明细记录")
    @DictConvert
    private List<CompanyIncomeExpensesDto> incomeExpenses = new ArrayList<>();

}
