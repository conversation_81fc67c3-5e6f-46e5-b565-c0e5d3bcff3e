package com.niimbot.sale;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2021/12/30 16:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CompanyBillDetailDto对象", description = "企业账单详情信息")
public class CompanyBillDetailDto implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "统计周期开始日期")
    private LocalDate statisticalStartTime;

    @ApiModelProperty(value = "统计周期结束日期")
    private LocalDate statisticalEndTime;

    @ApiModelProperty(value = "收入/支出")
    private BigDecimal incomeOrExpensesAmount;

    @ApiModelProperty(value = "调账")
    private BigDecimal reconciliationAmount;

    @ApiModelProperty(value = "总收入/总支出")
    private BigDecimal totalIncomeOrExpensesAmount;


}

