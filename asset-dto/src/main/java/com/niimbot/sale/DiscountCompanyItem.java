package com.niimbot.sale;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/6 上午10:01
 */
@Data
@Accessors(chain = true)
public class DiscountCompanyItem implements Serializable {

    private static final long serialVersionUID = -7970179774976367343L;

    /**
     * 企业规模最小值
     */
    private Integer minValue;

    /**
     * 企业规模最大值
     */
    private Integer maxValue;
}
