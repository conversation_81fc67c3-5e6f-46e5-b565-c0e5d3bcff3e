package com.niimbot.todo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 待办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "TodoDeleteMessageDto对象", description = "待办事项删除对象")
public class TodoDeleteMessageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "业务ID")
    private Long businessId;

    @ApiModelProperty(value = "子业务ID")
    private Long subBusinessId;

    @ApiModelProperty(value = "业务类型(对应单据类型)：1：领用，2：退还，3：借用，4：归还，5：调拨，6：报修，" +
            "7：维修，8：处置，9：变更, 10: 采购, 11: 保养, 33:耗材领用, 96: 盘点审核, 97: 保养计划, 98: 上报, 99: 盘点")
    private Short orderType;

    @ApiModelProperty(value = "处理人")
    private Long handleUser;
}
