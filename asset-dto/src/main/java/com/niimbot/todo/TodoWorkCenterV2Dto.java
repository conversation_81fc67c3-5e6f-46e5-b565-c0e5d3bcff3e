package com.niimbot.todo;

import com.niimbot.asset.framework.annotation.DictConvert;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/3/12 19:23
 */
@Data
@Accessors(chain = true)
public class TodoWorkCenterV2Dto {

    @ApiModelProperty(value = "待办数据")
    @DictConvert
    private List<TodoDto> todoList = new ArrayList<>();

    @ApiModelProperty(value = "待办数量")
    private Integer todoCount;

}
