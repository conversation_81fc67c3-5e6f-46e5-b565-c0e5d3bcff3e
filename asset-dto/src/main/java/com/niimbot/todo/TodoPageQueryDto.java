package com.niimbot.todo;

import com.niimbot.page.Pageable;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.Size;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 待办分页查询对象
 *
 * <AUTHOR>
 * @date 2021/7/9 17:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TodoPageQueryDto", description="待办分页查询对象")
public class TodoPageQueryDto extends Pageable implements Serializable {
    private static final long serialVersionUID = -2788953909343245226L;

    public static final Integer ALL_WAITE = -1;
    public static final Integer WILL_TIMEOUT = 1;
    public static final Integer TIMEOUT = 2;

    @ApiModelProperty(value = "关键字")
    private String kw;

    @ApiModelProperty(value = "单据类型")
    private Integer orderType;

    @ApiModelProperty(value = "发起人")
    private Long createBy;

    @ApiModelProperty(value = "发起时间")
    @Size(min = 2, max = 2, message = "创建时间必须包含开始和结束")
    private List<String> createTime;

    @ApiModelProperty(value = "超时状态: -1-全部，1-即将超时, 2-已超时")
    private Integer timeoutStatus = 0;

    @ApiModelProperty(value = "是否已处理")
    private Boolean isHandle = Boolean.FALSE;

    @ApiModelProperty(value = "排除单据")
    private List<Integer> excludeOrderType;

    public List<LocalDateTime> getCreateTime() {
        List<LocalDateTime> result = null;
        if (CollUtil.isNotEmpty(this.createTime)) {
            result = new ArrayList<>();
            result.add(LocalDateTime.parse(this.createTime.get(0) + " 00:00:00",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.add(LocalDateTime.parse(this.createTime.get(1) + " 23:59:59",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        return result;
    }
}
