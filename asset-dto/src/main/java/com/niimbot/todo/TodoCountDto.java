package com.niimbot.todo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 待办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TodoCountDto对象", description="待办数量")
public class TodoCountDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "待处理数量")
    private Integer waitHandleCount;

    @ApiModelProperty(value = "我处理的")
    private Integer handleCount;

    @ApiModelProperty(value = "即将超时数量")
    private Integer willTimeoutCount;

    @ApiModelProperty(value = "已超时数量")
    private Integer timeoutCount;

    @ApiModelProperty(value = "是都显示小红点")
    private Boolean isShowRedDot;
}
