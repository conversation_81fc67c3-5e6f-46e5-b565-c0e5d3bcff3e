package com.niimbot.todo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 待办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "TodoCreateMessageDto对象", description = "待办事项创建对象")
public class TodoCreateMessageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司ID（租户ID）")
    private Long companyId;

    @ApiModelProperty(value = "业务ID")
    private Long businessId;

    @ApiModelProperty(value = "子业务ID")
    private Long subBusinessId;

    @ApiModelProperty(value = "待办类型1:工作节点待办 2:我申请的 3:抄送我的")
    private Integer bizType;

    @ApiModelProperty(value = "业务类型(对应单据类型)：1：领用，2：退还，3：借用，4：归还，5：调拨，6：报修，" +
            "7：维修，8：处置，9：变更, 10: 采购, 11: 保养, 96: 盘点审核, 97: 保养计划, 98: 上报, 99: 盘点")
    private Short orderType;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "单据数据")
    private String orderData;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @ApiModelProperty(value = "处理人")
    private Long handleUser;
}
