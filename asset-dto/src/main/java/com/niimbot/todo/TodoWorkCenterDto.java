package com.niimbot.todo;

import com.niimbot.activiti.WorkflowApproveInfoDto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/9/7 上午10:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "TodoWorkCenterDto对象", description = "工作台待办中心")
public class TodoWorkCenterDto extends TodoDto implements Serializable {

    private static final long serialVersionUID = 1820026294953511596L;

    @ApiModelProperty(value = "待办数量")
    private Integer todoCount;

    @ApiModelProperty(value = "审批流程信息")
    private WorkflowApproveInfoDto approveInfo;
}
