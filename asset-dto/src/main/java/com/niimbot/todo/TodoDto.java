package com.niimbot.todo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.niimbot.asset.framework.annotation.DictConvert;
import com.niimbot.asset.framework.annotation.IdToNameConvert;
import com.niimbot.asset.framework.constant.RefFieldType;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 待办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "TodoDto对象", description = "待办事项")
public class TodoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Id")
    private Long id;

    @ApiModelProperty(value = "业务ID")
    private Long businessId;

    @ApiModelProperty(value = "子业务ID")
    private Long subBusinessId;

    @ApiModelProperty(value = "业务类型(对应单据类型)：1：领用，2：退还，3：借用，4：归还，5：调拨，6：报修，" +
            "7：维修，8：处置，9：变更, 10: 采购, 11: 保养, 33:耗材领用, 96: 盘点审核, 97: 保养计划, 98: 上报, 99: 盘点")
    private Short orderType;

    @ApiModelProperty(value = "业务类型名称")
    @DictConvert(value = "todo_type", refField = "orderType")
    private String orderTypeText;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "单据数据")
    private String orderData;

    @ApiModelProperty(value = "创建者")
    private Long createBy;

    @ApiModelProperty(value = "创建者名称")
    @IdToNameConvert(refField = "createBy", type = RefFieldType.USER)
    private String createUserText;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
