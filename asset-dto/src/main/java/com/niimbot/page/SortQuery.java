package com.niimbot.page;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020/12/31 10:25
 */
public class SortQuery extends Pageable {

    /**
     * 升序
     */
    private static final String ASC = "asc";

    @Getter
    @Setter
    @ApiModelProperty(value = "排序字段")
    public String sidx;

    @Getter
    @Setter
    @ApiModelProperty(value = "排序方式 [asc，desc]")
    public String order;

    @Override
    public <T> Page<T> buildIPage() {
        // 获取分页对象
        Page<T> page = super.buildIPage();
        //防止SQL注入（因为sidx、order是通过拼接SQL实现排序的，会有SQL注入风险）
        if (StrUtil.isNotEmpty(sidx)) {
            String[] fieldSplit = sidx.split(",");
            List<OrderItem> orderItems = new ArrayList<>();
            for (String sql : fieldSplit) {
                String orderField = this.sqlInject(sql);
                if (StrUtil.isNotEmpty(orderField)) {
                    // 设置排序字段
                    if (ASC.equalsIgnoreCase(order)) {
                        orderItems.add(OrderItem.asc(StrUtil.toUnderlineCase(orderField)));
                    } else {
                        orderItems.add(OrderItem.desc(StrUtil.toUnderlineCase(orderField)));
                    }
                }
            }
            page.addOrder(orderItems);
        }
        return page;
    }

    @Override
    public <T> Page<T> buildIPage(String tableAliasName) {
        // 获取分页对象
        Page<T> page = super.buildIPage();
        //防止SQL注入（因为sidx、order是通过拼接SQL实现排序的，会有SQL注入风险）
        if (StrUtil.isNotEmpty(sidx)) {
            String[] fieldSplit = sidx.split(",");
            List<OrderItem> orderItems = new ArrayList<>();
            for (String sql : fieldSplit) {
                String orderField = this.sqlInject(sql);
                if (StrUtil.isNotEmpty(orderField)) {
                    // 设置排序字段
                    if (ASC.equalsIgnoreCase(order)) {
                        orderItems.add(OrderItem.asc(tableAliasName + "." + StrUtil.toUnderlineCase(orderField)));
                    } else {
                        orderItems.add(OrderItem.desc(tableAliasName + "." + StrUtil.toUnderlineCase(orderField)));
                    }
                }
            }
            page.addOrder(orderItems);
        }
        return page;
    }

    /**
     * 不转换排序字段驼峰
     */
    public <T> Page<T> buildIPageOrigin() {
        // 获取分页对象
        Page<T> page = super.buildIPage();
        //防止SQL注入（因为sidx、order是通过拼接SQL实现排序的，会有SQL注入风险）
        if (StrUtil.isNotEmpty(sidx)) {
            String[] fieldSplit = sidx.split(",");
            List<OrderItem> orderItems = new ArrayList<>();
            for (String sql : fieldSplit) {
                String orderField = this.sqlInject(sql);
                if (StrUtil.isNotEmpty(orderField)) {
                    // 设置排序字段
                    if (ASC.equalsIgnoreCase(order)) {
                        orderItems.add(OrderItem.asc(orderField));
                    } else {
                        orderItems.add(OrderItem.desc(orderField));
                    }
                }
            }
            page.addOrder(orderItems);
        }
        return page;
    }

    private String sqlInject(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        //去掉'|"|;|\字符
        str = StrUtil.replace(str, "'", "");
        str = StrUtil.replace(str, "\"", "");
        str = StrUtil.replace(str, ";", "");
        str = StrUtil.replace(str, "\\", "");

        //转换成小写
        String lowerCase = str.toLowerCase();

        //非法字符
        String[] keywords = {"master ", "truncate ", "insert ", "select ", "delete ", "update ", "declare ", "alter ", "drop "};

        //放行字符
        List<String> exclude = ListUtil.of("update_time", "update_by");

        //判断是否包含非法字符
        for (String keyword : keywords) {
            if (lowerCase.contains(keyword) && !exclude.contains(lowerCase)) {
                return null;
            }
        }
        return str;
    }

}
