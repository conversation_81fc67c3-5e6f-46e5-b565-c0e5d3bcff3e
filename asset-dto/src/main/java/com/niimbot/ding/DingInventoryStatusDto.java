package com.niimbot.ding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/14 16:51
 */
@Data
public class DingInventoryStatusDto {

    @ApiModelProperty(value = "审核人")
    private Long approver;

    @ApiModelProperty(value = "状态（1-进行中, 2-待审核, 3-已完成, 4-已终止, 5-已驳回, 6-盘点已处理）")
    private Integer inventoryStatus;

    @ApiModelProperty(value = "是否已提醒")
    private Boolean hasReminder;

}
