package com.niimbot.ding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 当前区域资产盘点进度
 * <AUTHOR>
 * @date 2022/12/23 下午5:39
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryAssetAreaStatisticsDto", description = "区域盘点进度对象")
public class InventoryAssetAreaStatisticsDto implements Serializable {

    private static final long serialVersionUID = -8462533452731950404L;

    @ApiModelProperty(value = "区域id")
    private String areaId;

    @ApiModelProperty(value = "已盘数量")
    private Integer checkedNum = 0;

    @ApiModelProperty(value = "待盘数量")
    private Integer unCheckedNum = 0;

    @ApiModelProperty(value = "非当前区域资产")
    private Integer nonCurrentAreaNum = 0;
}
