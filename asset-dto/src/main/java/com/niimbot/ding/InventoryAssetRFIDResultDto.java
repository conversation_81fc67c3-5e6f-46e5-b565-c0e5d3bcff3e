package com.niimbot.ding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/26 上午10:21
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryAssetRFIDResultDto", description = "RFID盘点结果对象")
public class InventoryAssetRFIDResultDto implements Serializable {

    private static final long serialVersionUID = -1417805052037677183L;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "盘点单任务id")
    private Long inventoryTaskId;

    @ApiModelProperty(value = "已盘数量")
    private Integer checkedNum;

    @ApiModelProperty(value = "资产总数量")
    private Integer assetNum;

    @ApiModelProperty(value = "盘盈数量")
    private Integer nonCurrentTaskNum;
}
