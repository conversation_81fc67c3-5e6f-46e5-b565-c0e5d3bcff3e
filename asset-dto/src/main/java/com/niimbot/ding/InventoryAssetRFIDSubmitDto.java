package com.niimbot.ding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * RFID盘点请求对象
 * <AUTHOR>
 * @date 2022/12/26 上午10:06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryAssetRFIDSubmitDto", description = "RFID盘点请求对象")
public class InventoryAssetRFIDSubmitDto implements Serializable {

    private static final long serialVersionUID = 149952231501152084L;

    @ApiModelProperty(value = "盘点单id")
    @NotNull(message = "盘点单信息为空")
    private Long inventoryId;

    @ApiModelProperty(value = "盘点任务id")
    @NotNull(message = "盘点任务信息为空")
    private Long inventoryTaskId;

    @ApiModelProperty(value = "资产epcId列表")
    @NotEmpty(message = "资产列表为空")
    private List<String> epcIdList;

    @ApiModelProperty(value = "盘点终端：5")
    private Integer inventoryTerminal = 5;

    /**
     * 当前登录用户
     */
    private Long currentUserId;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 当前盘点任务的资产id列表
     */
    private List<Long> assetIdList;

    /**
     * 当前盘点单资产id列表
     */
    private List<Long> inventoryAssetIdList;

    private Integer inventoryMode = 1;
}
