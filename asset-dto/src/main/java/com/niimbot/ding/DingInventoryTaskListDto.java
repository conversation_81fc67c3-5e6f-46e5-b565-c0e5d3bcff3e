package com.niimbot.ding;

import com.niimbot.asset.framework.annotation.DictConvert;
import com.niimbot.asset.framework.annotation.IdToNameConvert;
import com.niimbot.asset.framework.constant.RefFieldType;
import com.niimbot.inventory.InventoryAssetCountDto;

import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/4/10 10:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DingInventoryTaskListDto extends InventoryAssetCountDto {

    @ApiModelProperty(value = "盘点任务id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "盘点单id")
    private Long inventoryId;

    @ApiModelProperty(value = "盘点人")
    private List<Long> inventoryUsers;

    @ApiModelProperty(value = "盘点人")
    @IdToNameConvert(refField = "inventoryUsers", type = RefFieldType.USER)
    private List<String> inventoryUsersText;

    @ApiModelProperty(value = "状态（1：进行中，2：待审核，3：已完成，4：已终止）")
    private Integer status;

    @ApiModelProperty(value = "盘点任务状态")
    @DictConvert(value = "inventory_status", refField = "status")
    private String statusText;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "是否需要拍照")
    private Boolean enableTakePicture;

    @ApiModelProperty(value = "是否允许手工盘点")
    private Boolean enableManual;

    @ApiModelProperty(value = "盘点是否可修改资产数据")
    private Boolean enableUpdateData;

    @ApiModelProperty(value = "允许没有资产查看权限人员参与盘点")
    private Boolean enableNoPermLook;

}
