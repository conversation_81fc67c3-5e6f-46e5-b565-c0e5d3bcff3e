package com.niimbot.ding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * RFID盘点-区域盘点进度查询对象
 * <AUTHOR>
 * @date 2022/12/23 下午4:46
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryAssetAreaQueryDto", description = "区域盘点进度查询对象")
public class InventoryAssetAreaQueryDto implements Serializable {

    private static final long serialVersionUID = 8483501160585346186L;

    @ApiModelProperty(value = "盘点单id")
    @NotNull(message = "盘点单id为空")
    private Long inventoryId;

    @ApiModelProperty(value = "盘点任务id")
    @NotNull(message = "盘点任务id为空")
    private Long inventoryTaskId;

    @ApiModelProperty(value = "盘点区域id")
    @NotNull(message = "区域id为空")
    private String areaId;

    /**
     * 存放区域，不在当前参数区域
     */
    private String notAreaId;

    /**
     * 是否盘点（0：未盘，1：已盘）
     */
    private Boolean checked;
}
