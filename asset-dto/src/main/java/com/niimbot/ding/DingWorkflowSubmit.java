package com.niimbot.ding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DingWorkflowSubmit {

    @ApiModelProperty("钉钉流程节点预测成功后返回的唯一标识")
    // @NotBlank(message = "预测节点唯一标识不能为空")
    private String uid;

    /**
     * 自选节点审批人设置
     */
    @Valid
    private List<WorkflowActor> actors;

    /**
     * 流程指定审批人设置
     */
    private List<WorkflowActor> approvers;

    /**
     * 抄送人列表
     */
    private List<Long> ccEmpIds;

}
