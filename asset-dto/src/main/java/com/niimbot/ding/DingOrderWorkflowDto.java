package com.niimbot.ding;

import com.alibaba.fastjson.JSONObject;
import com.niimbot.AbstractOrderDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class DingOrderWorkflowDto extends AbstractOrderDto {

    private List<JSONObject> assets;

    private List<JSONObject> materials;

    private List<JSONObject> products;

    private List<JSONObject> productDetail;
}
