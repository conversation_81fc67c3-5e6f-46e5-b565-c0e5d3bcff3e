package com.niimbot.ding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "DingWorkflowActor", description = "钉钉审批流节点操作人信息")
public class WorkflowActor implements Serializable {

    @ApiModelProperty("节点操作人key")
    // @NotBlank(message = "节点操作人Key不能为空")
    private String actorKey;

    @ApiModelProperty("审批人列表（云资产系统员工ID集合）")
    // @NotEmpty(message = "审批人列表不能为空")
    private List<Long> empIds;

    @ApiModelProperty("审批类型")
    private String actionType;

    @ApiModelProperty("节点操作人类型")
    private String actorType;

}
