package com.niimbot.ding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BatchChangeEmpAccountStatus", description = "批量启用或禁用员工账号")
public class BatchChangeEmpAccountStatus {

    @ApiModelProperty("员工ID集合")
    @NotEmpty(message = "员工集合ID不能为空")
    private List<Long> empIds;

    @ApiModelProperty("账号状态：1-未激活，3-已激活")
    @NotNull(message = "员工账号状态不能为空")
    private Integer accountStatus;

}
