package com.niimbot.ding;

import com.niimbot.page.Pageable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/4/10 11:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DingInventoryQueryDto extends Pageable {

    @ApiModelProperty(value = "名称")
    private String kw;

    @ApiModelProperty(value = "钉钉群聊Id")
    private String openConversationId;

    @ApiModelProperty(value = "状态（1：进行中，2：待审核，3：已完成，4：已终止）")
    private Integer status;

    @ApiModelProperty(value = "盘点人")
    private Long inventoryUser;

}

